<?php
define('BASEDIR', __DIR__);
//默认引入
function Intautoload($object){
    $file = BASEDIR."/Core/Tools/{$object}.php";
    if (file_exists($file))
    {
        require_once($file);
    }
}

spl_autoload_register('Intautoload');


use FG\ASN1\ASNObject;
use Rtgm\sm\RtSm2;
$sm2 = new RtSm2('base64');

//$sm2 = new Sm2('base64');


$private_key = 'D5F2AFA24E6BA9071B54A8C9AD735F9A1DE9C4657FA386C09B592694BC118B38';
$userId = '1234567812345678';


$data = json_decode('{"biz_content":"{\"termId\":\"term0525\",\"orderId\":\"2018060616435200002\",\"notifyUrl\":\"http://wlhost1.paas.cmbchina.cn/mch_notify/api/notify\",\"merId\":\"20180524200752LWW\",\"payValidTime\":\"100000\",\"currencyCode\":\"156\",\"userId\":\"N111555974\",\"txnAmt\":\"134\",\"body\":\"收款测试0606\"}","encoding":"UTF-8","signMethod":"01","version":"0.0.1"}');
$params =  urldecode(http_build_query($data));

$sign = $sm2->doSign($params, $private_key, $userId);
echo $sign;

echo "\n<br><br>";

echo "待验签字符串：\n<br><br>";

echo '{"biz_content":"{\"termId\":\"term0525\",\"orderId\":\"2018060616435200002\",\"notifyUrl\":\"http://wlhost1.paas.cmbchina.cn/mch_notify/api/notify\",\"merId\":\"20180524200752LWW\",\"payValidTime\":\"100000\",\"currencyCode\":\"156\",\"userId\":\"N111555974\",\"txnAmt\":\"134\",\"body\":\"收款测试0606\"}","encoding":"UTF-8","signMethod":"01","version":"0.0.1","sign":"' . $sign . '"}';
