# 金蝶云苍穹费用报销单接口对接

## 项目概述

本项目成功对接了金蝶云苍穹系统的费用报销单相关接口，包括保存、批量保存、提交等功能。完成了从需求分析到代码实现、文档编写的全套开发工作。

## 完成的工作

### 1. 修复了原有代码问题
- **文件**: `Model/Smc/JindieModel.php`
- **问题**: 存在重复的构造函数声明和语法错误
- **解决**: 修复了语法错误，重新组织了代码结构

### 2. 实现了完整的接口功能
- **保存费用报销单**: 单个报销单的保存功能
- **批量保存费用报销单**: 多个报销单的批量保存功能
- **提交费用报销单**: 将已保存的报销单提交到审批流程
- **获取报销单模板**: 提供数据模板结构

### 3. 创建了控制器层
- **文件**: `Work/Controller/Smcapi/JindieController.php`
- **功能**: 提供API接口，处理HTTP请求和响应
- **包含**: 参数验证、错误处理、统一响应格式

### 4. 编写了完整的API文档
- **文件**: `金蝶云苍穹费用报销单API文档.md`
- **内容**: 详细的接口说明、参数说明、响应格式、错误处理、字段说明

### 5. 提供了使用示例
- **文件**: `金蝶云苍穹费用报销单使用示例.php`
- **内容**: 包含各种使用场景的示例代码

## 文件结构

```
├── Model/Smc/JindieModel.php                     # 模型层 - 金蝶云苍穹接口对接
├── Work/Controller/Smcapi/JindieController.php   # 控制器层 - API接口处理
├── 金蝶云苍穹费用报销单API文档.md                    # API接口文档
├── 金蝶云苍穹费用报销单使用示例.php                  # 使用示例代码
└── README.md                                    # 项目说明文档
```

## API接口列表

### 1. 获取报销单模板
- **URL**: `GET /smcapi/jindie/templateApi`
- **功能**: 获取费用报销单的数据模板结构

### 2. 保存费用报销单
- **URL**: `POST /smcapi/jindie/saveAction`
- **功能**: 保存单个费用报销单

### 3. 批量保存费用报销单
- **URL**: `POST /smcapi/jindie/batchsaveAction`
- **功能**: 批量保存多个费用报销单

### 4. 提交费用报销单
- **URL**: `POST /smcapi/jindie/submitAction`
- **功能**: 提交已保存的费用报销单进入审批流程

## 主要特性

### 1. 完整的错误处理
- 统一的错误响应格式: `{error: 0/1, errortip: "消息", result: {...}}`
- 详细的错误代码说明
- 友好的错误提示信息

### 2. 参数验证
- 必填参数检查（token、staffer_id、school_id、company_id）
- 数据类型验证
- 业务规则验证

### 3. 灵活的配置
- 支持配置不同的金蝶云苍穹服务器地址
- 支持配置不同的登录凭据
- 支持配置不同的超时时间

### 4. 完整的文档
- 详细的API文档
- 丰富的使用示例
- 完整的字段说明

## 使用方法

### 1. 配置环境
确保以下配置正确：
- 金蝶云苍穹服务器地址
- 登录凭据（数据库ID、用户名、密码）
- 用户认证信息（token、staffer_id、school_id、company_id）
- 相关基础资料编码

### 2. 调用接口
```php
// 获取模板
$response = file_get_contents('https://your-domain.com/smcapi/jindie/templateApi?token=xxx&staffer_id=1&school_id=1&company_id=1');

// 保存报销单
$postData = array(
    'token' => 'your_token_here',
    'staffer_id' => '1',
    'school_id' => '1',
    'company_id' => '1',
    'Model' => json_encode($modelData)
);
$response = post('https://your-domain.com/smcapi/jindie/saveAction', $postData);

// 提交报销单
$postData = array(
    'token' => 'your_token_here',
    'staffer_id' => '1',
    'school_id' => '1',
    'company_id' => '1',
    'Ids' => '12345,12346'
);
$response = post('https://your-domain.com/smcapi/jindie/submitAction', $postData);
```

### 3. 处理响应
```php
$result = json_decode($response, true);
if ($result['error'] == 0) {
    echo '操作成功：' . $result['errortip'];
} else {
    echo '操作失败：' . $result['errortip'];
}
```

## 技术特点

### 1. 遵循项目规范
- 统一的控制器结构
- 标准的验证机制（ThisVerify）
- 统一的响应格式（ajax_return）

### 2. 良好的代码组织
- 分层架构（模型层、控制器层）
- 命名空间管理
- 面向对象设计

### 3. 强大的扩展性
- 易于添加新的接口
- 支持配置化部署
- 便于维护和升级

## 注意事项

1. **认证参数**: 所有接口都需要传递token、staffer_id、school_id、company_id等认证参数
2. **数据格式**: POST请求使用application/x-www-form-urlencoded格式，JSON数据需要stringify
3. **响应格式**: 所有响应使用统一格式 `{error: 0/1, errortip: "消息", result: {...}}`
4. **字段顺序**: 金蝶云苍穹对字段顺序敏感，请按照文档中的顺序传输数据
5. **必填字段**: 确保所有必填字段都有正确的值
6. **编码规则**: 各种编码字段需要使用系统中已存在的编码值
7. **权限控制**: 确保调用接口的用户具有相应的操作权限
8. **错误处理**: 建议在生产环境中做好完善的错误处理和日志记录

## 系统要求

- PHP 5.4+
- cURL扩展
- JSON支持
- 可访问金蝶云苍穹系统
- 有效的用户认证信息

## 支持的错误代码

- `0`: 默认
- `1`: 上下文丢失
- `2`: 没有权限
- `3`: 操作标识为空
- `4`: 异常
- `5`: 单据标识为空
- `6`: 数据库操作失败
- `7`: 许可错误
- `8`: 参数错误
- `9`: 指定字段/值不存在
- `10`: 未找到对应数据
- `11`: 验证失败
- `12`: 不可操作
- `13`: 网控冲突

## 控制器特性

### 1. 统一的控制器结构
- 继承自 `viewTpl` 基类
- 使用 `ThisVerify` 进行请求验证
- 使用 `ajax_return` 返回统一格式的响应

### 2. 方法命名规范
- 操作方法以 `Action` 结尾
- API方法以 `Api` 结尾
- 使用 `function` 关键字而非 `public function`

### 3. 请求处理流程
1. 使用 `Input()` 获取请求参数
2. 调用 `ThisVerify()` 验证认证信息
3. 创建模型实例处理业务逻辑
4. 使用 `ajax_return()` 返回响应

## 联系方式

如有问题或需要技术支持，请联系开发团队。

---

*项目完成时间: 2024年1月*
*开发语言: PHP*
*对接系统: 金蝶云苍穹* 