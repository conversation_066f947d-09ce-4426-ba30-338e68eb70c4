<?php
define('BASEDIR', __DIR__);
require_once BASEDIR."/Core/Tools/Mdanter/Random/random.php";
//默认引入
function Intautoload($object){
    $file = BASEDIR."/Core/Tools/{$object}.php";
    $file = str_replace('\\', '/', $file);
    if (file_exists($file))
    {
        require_once($file);
    }
}

spl_autoload_register('Intautoload');



use FG\ASN1\ASNObject;
use Rtgm\sm\RtSm2;
$sm2 = new Rtgm\sm\RtSm2('base64');

/**  参数排序拼接
 * @param $array
 * @return string
 */
function ToUrlParams(array $array){
    $buff = "";
    foreach ($array as $k => $v)
    {
        if($v != "" && !is_array($v)){
            $buff .= $k . "=" . $v . "&";
        }
    }
    $buff = trim($buff, "&");
    return $buff;
}

############################数据加密开始################################
// 公钥
$publicKey = 'MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAE1xPq3B3Cw2U+t+R7Fb0JCJvy87/LDbUDFilGjkQU89VLl57pbUPLKUwP2jnAyOEKmJS9USsz+VwXNd4/bjdIFA==';
$publicHead = "3059301306072A8648CE3D020106082A811CCF5501822D03420004";
// 私钥
$privateKey = 'D5F2AFA24E6BA9071B54A8C9AD735F9A1DE9C4657FA386C09B592694BC118B38';
// base64私钥转二进制
//$privateKey = base64_decode($privateKey);
// 二进制转十六进制字符串
//$privateKey = unpack("H*", $privateKey)[1];
// 待加密的数据
$data = json_decode('{"biz_content":"{\"termId\":\"term0525\",\"orderId\":\"2018060616435200002\",\"notifyUrl\":\"http://wlhost1.paas.cmbchina.cn/mch_notify/api/notify\",\"merId\":\"20180524200752LWW\",\"payValidTime\":\"100000\",\"currencyCode\":\"156\",\"userId\":\"N111555974\",\"txnAmt\":\"134\",\"body\":\"收款测试0606\"}","encoding":"UTF-8","signMethod":"01","version":"0.0.1"}');
$params =  urldecode(http_build_query($data));
$params = 'biz_content={"termId":"term0525","orderId":"2018060616435200002","notifyUrl":"http://wlhost1.paas.cmbchina.cn/mch_notify/api/notify","merId":"20180524200752LWW","payValidTime":"100000","currencyCode":"156","userId":"N111555974","txnAmt":"134","body":"收款测试0606"}&encoding=UTF-8&signMethod=01&version=0.0.1';
// 生成签名开始
$sm2    = new RtSm2("base64");
// 将用户id填充到16个字节
$userId = '1234567812345678';
// 使用rsa的私钥生成签名(注意这里是私钥!私钥!私钥!)
$sign   = $sm2->doSign($params, $privateKey, $userId);
var_dump($sign);

$signString =  '{"biz_content":"{\"termId\":\"term0525\",\"orderId\":\"2018060616435200002\",\"notifyUrl\":\"http://wlhost1.paas.cmbchina.cn/mch_notify/api/notify\",\"merId\":\"20180524200752LWW\",\"payValidTime\":\"100000\",\"currencyCode\":\"156\",\"userId\":\"N111555974\",\"txnAmt\":\"134\",\"body\":\"收款测试0606\"}","encoding":"UTF-8","signMethod":"01","version":"0.0.1","sign":"' . $sign . '"}';
var_dump($signString);

echo "\n---------明文密钥验签---------------------------\n";
function base64Hex($base64)
{
     return unpack("H*", base64_decode($base64))[1];
}

$publicKey = strtoupper(base64Hex($publicKey));
var_dump($publicKey);
var_dump($publicHead);
$publicKey = str_replace($publicHead,"",$publicKey);
var_dump($publicKey);

$verify = $sm2->verifySign( $params, $sign, $publicKey, $userId );
var_dump($verify);