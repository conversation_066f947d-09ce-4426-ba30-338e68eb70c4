---
description: 项目开发规则 - 遵循KISS原则
globs: ["**/*.php", "**/*.js", "**/*.md"]
alwaysApply: true
---

# 开发规则

## 核心原则：KISS (Keep It Simple, Stupid)

### 代码编写规则
1. **保持简单**: 每个方法只做一件事，避免复杂的逻辑
2. **方法长度**: 单个方法不超过50行
3. **参数限制**: 方法参数不超过5个
4. **嵌套控制**: 避免超过3层的嵌套
5. **删除冗余**: 及时清理不使用的代码和注释

### 命名规范
- 使用有意义的变量名和方法名
- 避免缩写，使用完整的单词
- 方法名以动词开头，类名使用名词

### 错误处理
- 使用try-catch包装可能失败的操作
- 统一的返回格式: `{error: 0/1, errortip: "消息", result: {...}}`
- 记录详细的错误日志

### 数据库操作
- 使用具体字段名而不是 `SELECT *`
- 使用参数化查询防止SQL注入
- 添加必要的LIMIT限制

### 注释要求
- 重要方法必须有PHPDoc注释
- 复杂逻辑要有说明注释
- API接口要有完整文档

### 测试规范
- 编写简单易懂的测试用例
- 及时清理测试代码和文件
- 核心业务逻辑必须有测试覆盖

**记住：简单的代码是最好的代码！**
