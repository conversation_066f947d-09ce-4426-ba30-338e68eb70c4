<FilesMatch "\.(bak|inc|lib|sh|tpl|lbi|dwt)$">
    order deny,allow
    deny from all
</FilesMatch>

Options -Indexes
RewriteEngine On
#RewriteBase /

<IfModule mod_headers.c>
    Header set Access-Control-Allow-Origin "*"
    #Header set Access-Control-Allow-Origin "http://sh.babyzb.com"
</IfModule>



RewriteRule ^([a-zA-Z]+)/([a-zA-Z]+)/([a-zA-Z]+)$  index\.php\?u=$1&t=$2&type=$3 [QSA,L]
RewriteRule ^([a-zA-Z0-9]+)/([a-zA-Z0-9]+)/id-([0-9]+)$  index\.php\?u=$1&t=$2&id=$3 [QSA,L]
RewriteRule ^([a-zA-Z0-9]+)/([a-zA-Z0-9]+)/([0-9]+)$  index\.php\?u=$1&t=$2&id=$3 [QSA,L]
RewriteRule ^([a-zA-Z0-9]+)/([a-zA-Z0-9]+)Action$  index\.php\?u=$1&c=$2 [QSA,L]
RewriteRule ^([a-zA-Z0-9]+)/([a-zA-Z0-9]+)Api$  index\.php\?u=$1&api=$2 [QSA,L]
RewriteRule ^([a-zA-Z0-9]+)/([a-zA-Z0-9_]+)$  index\.php\?u=$1&t=$2 [QSA,L]
RewriteRule ^([a-zA-Z0-9]+)/id-([0-9]+)$  index\.php\?u=$1&id=$2 [QSA,L]
RewriteRule ^([a-zA-Z0-9]+)/$  index\.php\?u=$1&t=Home [QSA,L]
RewriteRule ^([a-zA-Z0-9]+)$  index\.php\?u=$1 [QSA,L]


ErrorDocument 404 /404.php

order Deny,Allow
Allow from all
