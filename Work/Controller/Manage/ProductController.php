<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2019/2/16
 * Time: 11:56
 */

namespace Work\Controller\Manage;


class ProductController extends viewTpl{
    public $data;
    public $u;
    public $t;
    public $c;
    public $icampus;
    public $iuser;
    public $Viewhtm;
    public $Module;

    //预加载处理类
    function __construct() {
        parent::__construct ();
        if(!$this->check_login()){
            $this->LoginView();
        }
        if($this->check_login()){
            $this->smarty->assign("iuser", $this->UserLogin);
            $this->iuser = $this->UserLogin;
        }
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
        $this->Viewhtm = $this->router->getController()."/".$this->router->getUrl().".htm";
        $moduleOne = $this->Show_css->getOne("cms_module","module_mark='{$this->u}'");
        $this->smarty->assign("moduleOne",$moduleOne);
        $this->Module = $moduleOne;
    }

    //主页
    function HomeView()
    {
        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        if(!isset($request['p'])){
            $p = '1';
        }else{
            $p= $request['p'];
        }

        $datawhere = "1";
        $pageurl = "/{$this->u}/{$this->t}?site_id={$request['site_id']}";

        $datatype = array();
        if(isset($request['keyword']) && $request['keyword'] !==''){
            $datawhere .= " and (p.product_name like '%{$request['keyword']}%' or p.product_branch like '%{$request['keyword']}%')";
            $pageurl .="&keyword={$request['keyword']}";
            $datatype['keyword'] = $request['keyword'];
        }

        $sql = "select p.*
                from imc_product as p where {$datawhere}";


        $db_nums = $Show_css->selectOne("SELECT COUNT(p.product_id) as countnums FROM imc_product as p where {$datawhere}");//相关条件下的总记录数COUNT(*)
        $allnum = $db_nums['countnums'];

        $datalist = $Show_css->dbwherePage($sql,$allnum,'10',$pageurl.'&p=',$p,'10','2');

        $smarty->assign("pagelist",$datalist['pages']);//筛选信息
        $smarty->assign("dataList",$datalist['cont']);
        $smarty->assign("datatype",$datatype);
    }

    function AddView()
    {
        $smarty = $this->smarty;
        $smarty->assign("act", "Add");

        $this->Viewhtm = $this->router->getController() . "/" . "Edit.htm";
    }

    function AddAction()
    {
        $request = Input('post.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $bakurl ="/{$this->u}?site_id={$request['site_id']}";

        if(isset($request['product_name']) && $request['product_name'] =='' ){
            $res = array('error' => '1', 'errortip' => '请填写产品名称!',"bakfuntion"=>"errormotify");
            ajax_return($res);
        }

        if(isset($request['product_branch']) && $request['product_branch'] =='' ){
            $res = array('error' => '1', 'errortip' => '请填写产品编号!',"bakfuntion"=>"errormotify");
            ajax_return($res);
        }

        $editionOne=$this->Show_css->getFieldOne("imc_product","product_id","(product_name='{$request['product_name']}' or product_branch='{$request['product_branch']}')");

        if($editionOne){
            $res = array('error' => '1', 'errortip' => '产品已存在!',"bakfuntion"=>"errormotify");
            ajax_return($res);
        }

        $data = array();
        $data['product_name'] = $request['product_name'];
        $data['product_branch'] = $request['product_branch'];
        $data['product_openmodule'] = $request['product_openmodule'];

        if($Show_css->insertData("imc_product",$data)){
            $this->Recordweblog($request['site_id'],$this->Module['module_id'],$this->c,"新增产品");
            ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"successFromTip","bakurl"=>$bakurl));
        }else{
            ajax_return(array('error' => 1,'errortip' => "提交失败!","bakfuntion"=>"dangerFromTip"));
        }
    }

    function EditView(){
        $smarty = $this->smarty;

        $request = Input('get.','','trim,addslashes');
        $productOne =$this->Show_css->getOne("imc_product","product_id='{$request['product_id']}'");
        $smarty->assign("dataVar",$productOne);
        $smarty->assign("act","edit");
        $this->Viewhtm = $this->router->getController()."/"."Edit.htm";
    }

    function EditAction()
    {

        $request = Input('post.','','trim,addslashes');

        $bakurl =  "/{$this->u}?site_id={$request['site_id']}";
        if(isset($request['product_name']) && $request['product_name'] =='' ){
            $res = array('error' => '1', 'errortip' => '请填写产品名称!',"bakfuntion"=>"errormotify");
            ajax_return($res);
        }

        if(isset($request['product_branch']) && $request['product_branch'] =='' ){
            $res = array('error' => '1', 'errortip' => '请填写产品编号!',"bakfuntion"=>"errormotify");
            ajax_return($res);
        }

        $editionOne=$this->Show_css->getFieldOne("imc_product","product_id","(product_name='{$request['product_name']}' or product_branch='{$request['product_branch']}') and product_id<>{$request['product_id']}");

        if($editionOne){
            $res = array('error' => '1', 'errortip' => '产品已存在!',"bakfuntion"=>"errormotify");
            ajax_return($res);
        }

        $data = array();
        $data['product_name'] = $request['product_name'];
        $data['product_branch'] = $request['product_branch'];
        $data['product_openmodule'] = $request['product_openmodule'];
        if($this->Show_css->updateData('imc_product',"product_id='{$request['product_id']}'",$data)){
            $this->Recordweblog($request['site_id'],$this->Module['module_id'],$this->c,"编辑产品");
            ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"successFromTip","bakurl"=>$bakurl));
        }else{
            ajax_return(array('error' => 1,'errortip' => "提交失败!","bakfuntion"=>"dangerFromTip"));
        }

    }

    function ChangestatusAction(){
        $request = Input('get.','','trim,addslashes');
        $productOne = $this->Show_css->getOne("imc_product","product_id='{$request['id']}'");
        $data = array();
        if($productOne['product_openmodule'] == '1'){
            $data['product_openmodule'] = "0";
        }else{
            $data['product_openmodule'] = "1";
        }
        if($this->Show_css->updateData("imc_product","product_id = '{$productOne['product_id']}'",$data)){
            if($data['product_openmodule'] == '1'){
                $this->Recordweblog($request['site_id'],$this->Module['module_id'],$this->c,"修改产品启用状态，ID:{$request['id']}");
                ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"okmotify","state"=>"1"));
            }else{
                $this->Recordweblog($request['site_id'],$this->Module['module_id'],$this->c,"修改产品启用状态，ID:{$request['id']}");
                ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"okmotify","state"=>"0"));
            }
        }else{
            ajax_return(array('error' => 1,'errortip' => "修改失败!","bakfuntion"=>"errormotify"));
        }
    }

    function SetModuleView()
    {
        $request = Input('get.','','trim,addslashes');
        $datawhere = '1';

        $sql="SELECT m.*
              ,( SELECT COUNT( e.product_id ) FROM imc_editpromodule AS e WHERE e.product_id = '{$request['product_id']}' AND e.module_id = m.module_id ) AS status 
                FROM imc_module AS m 
                WHERE {$datawhere} and m.module_level=1
                ORDER BY status DESC,m.module_weight asc,m.module_id ASC";

        $dataList = $this->Show_css->select($sql);
        $this->smarty->assign('dataList',$dataList);
        $this->smarty->assign('product_id',$request['product_id']);
    }

    function SetModuleAction()
    {
        $request = Input('get.','','trim,addslashes');
        $data = array();
        $data['module_id'] = $request['module_id'];
        $data['product_id'] = $request['product_id'];
        $this->Show_css->insertData('imc_editpromodule',$data);

        $this->Recordweblog($request['site_id'],$this->Module['module_id'],$this->c,"产品{$request['product_id']}适配模块：{$request['module_id']}");
        ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"successFromTip"));

    }

    function UnSetModuleAction()
    {
        $request = Input('get.','','trim,addslashes');
        if($this->Show_css->delData('imc_editpromodule',"module_id={$request['module_id']} and product_id={$request['product_id']}")){
            $this->Recordweblog($request['site_id'],$this->Module['module_id'],$this->c,"产品{$request['product_id']}取消适配模块：{$request['module_id']}");
            ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"successFromTip"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "提交失败!","bakfuntion"=>"dangerFromTip"));
        }
    }

    function batchSetModuleAction()
    {
        $request = Input('post.','','trim,addslashes');
        if(isset($request['tab_list']) && count($request['tab_list'])>0){
            if($request['type']==1) {
                foreach($request['tab_list'] as $v) {
                    if(!$this->Show_css->getFieldOne('imc_editpromodule','module_id',"product_id={$request['product_id']} and module_id='{$v}'")) {
                        $data = array();
                        $data['product_id'] = $request['product_id'];
                        $data['module_id'] = $v;
                        $this->Show_css->insertData('imc_editpromodule',$data);
                    }
                    $this->Recordweblog($request['site_id'],$this->Module['module_id'],$this->c,"产品{$request['product_id']}适配模块：{$request['module_id']}");
                }
                ajax_return(array('error' => 0,'errortip' => "批量设置成功!","bakfuntion"=>"refreshpage"));
            }elseif($request['type']==2) {
                foreach($request['tab_list'] as $v) {
                    $this->Show_css->delData('imc_editpromodule',"product_id={$request['product_id']}  and module_id='{$v}'");
                    $this->Recordweblog($request['site_id'],$this->Module['module_id'],$this->c,"产品{$request['product_id']}取消适配模块：{$request['module_id']}");
                }
                ajax_return(array('error' => 0,'errortip' => "批量取消成功!","bakfuntion"=>"refreshpage"));
            }
        }
    }

    function DelAction()
    {
        $request = Input('get.','','trim,addslashes');
        $list_id = Input('get.id', 0);

        $applyOne=$this->Show_css->getFieldOne("imc_editproapply","edition_id","product_id='{$list_id}'");
        if($applyOne){
            ajax_return(array('error' => 1, 'errortip' => "已适配版本不可删除!", "bakfuntion" => "errormotify"));
        }

        if ($this->Show_css->delData('imc_product', "product_id='{$list_id}'")) {
            $this->Recordweblog($request['site_id'],$this->Module['module_id'],$this->c,"删除产品：{$list_id}");
            ajax_return(array('error' => 0, 'errortip' => "提交成功!", "bakfuntion" => "okmotify"));
        } else {
            ajax_return(array('error' => 1, 'errortip' => "新增失败!", "bakfuntion" => "errormotify"));
        }
    }

    function __destruct()
    {
        $site_id = Input('get.site_id',0);

        if ($this->c =='Wait') {
            $this->smarty->assign("u", $this->router->getController());
            $this->smarty->assign("t", $this->router->getUrl());
            $this->smarty->assign("Viewhtm", $this->Viewhtm);
            $websites = $this->Show_css->getOne("cms_websites","site_id='{$site_id}'");
            $this->smarty->assign("websites",$websites);
            if($site_id){
                $this->display("websiteindex.htm");
            }else{
                $this->display("index.htm");
            }
        }
        exit;
    }

}