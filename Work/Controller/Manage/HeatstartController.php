<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn 
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/2/13
 * Time: 17:07
 */

namespace Work\Controller\Manage;

class HeatstartController extends viewTpl{
    public $data;
    public $iuser;

    function __construct() {
        parent::__construct ();
    }

    //初始化登录函数
    function loginAction()
    {
        $username = Input('post.L_name', "", "trim,addslashes");
        $password = Input('post.L_pswd', "", "trim,addslashes");
        $rememberme = Input('post.rememberme', "", "trim,addslashes");
        $L_user = $this->Show_css->getOne("cms_users", "user_name='{$username}'");
        if ($L_user) {
            $password = md5($password);
            if ($password == $L_user['user_pass']) {
                $user = array();
                $user['user_id'] = $L_user['user_id'];
                $user['user_name'] = $L_user['user_name'];
                $this->intSession->setCookiearray("user", $user, '1');

                //登录日志记录表
                $this->addUsersLoginLog($L_user['user_id']);

                $this->Show_css->updateData("cms_users", "user_id = '{$L_user['user_id']}'", array("user_lasttime" => time(), "user_lastip" => real_ip()));

                if($L_user['user_type'] == '1' and $L_user['user_limitsinc'] == '1'){
                    ajax_return(array('error' => 0,'errortip' => "登录成功!","bakfuntion"=>"okmotify","bakurl"=>"/"));
                }else{
                    $websites = $this->Show_css->select("SELECT m.* FROM cms_websites_manage as m where m.user_id = '{$L_user['user_id']}' order by m.manage_id DESC");
                    if(count($websites) > 1){
                        ajax_return(array('error' => 0,'errortip' => "登录成功!","bakfuntion"=>"okmotify","bakurl"=>"/"));
                    }else{
                        $websitesOne =  $this->Show_css->getOne("cms_websites_manage", "user_id = '{$L_user['user_id']}'");
                        ajax_return(array('error' => 0,'errortip' => "登录成功!","bakfuntion"=>"okmotify","bakurl"=>"/Website/id-{$websitesOne['site_id']}"));
                    }
                }
            } else {
                ajax_return(array('error' => 1,'errortip' => "账户密码错误，请重新登录!","bakfuntion"=>"errormotify"));
            }

            jslocal_spl("/");
        } else {
            ajax_return(array('error' => 1,'errortip' => "账户不存在!","bakfuntion"=>"errormotify"));
        }
    }

    //登录日志记录表
    function addUsersLoginLog($user_id){
        $date = array();
        $date['user_id'] = $user_id;
        $date['loginlog_lastip'] = real_ip();
        $date['loginlog_time'] = time();
        $this->Show_css->insertData('cms_users_loginlog',$date);
        return true;
    }

    //初始化退出函数
    function outloginAction(){
        $this->intSession->setCookiearray("user",array(),'1');
        jslocal_spl("/");
    }

}