<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2016/12/13
 * Time: 21:21
 */

namespace Work\Controller\Manage;


class viewTpl {
    public $smarty;
    public $Static;
    public $intSession;
    public $Show_css;
    public $router;
    public $UserLogin;//管理用户
    public $CampusLogin;//机构用户

    public function __construct(){
        global $router;
        global $smarty;
        global $viewControl;
        //模板引擎开启
        $this->smarty = new \Smarty();
        //Session引擎开启
        $this->intSession = new \Incsession();
        //数据库操作
        $this->Show_css = new \Dbmysql();
        //操作类型
        $this->router = $router;

        $this->smarty->template_dir = BASEDIR.'/Work/View/Manage/';
        $this->smarty->compile_dir = BASEDIR.'/Temp/Compiled/Manage/';
        $this->smarty->config_dir = BASEDIR.'/Common/';
        $this->smarty->cache_dir = BASEDIR.'/Temp/Caches/';

        //指定定界符
        $this->smarty->left_delimiter="{";	//左定界符
        $this->smarty->right_delimiter="}";	//右定界符

        $this->smarty->compile_check = true;
        $this->smarty->debugging = false;

        $this->UserLogin = false;


        $viewControl = $this->Show_css;
        $smarty = $this->smarty;
        include(ROOT_PATH . "Core/Smarty/int.class.php");

        //静态资源加载
        $this->smarty->assign("CssUrl", URL_PATH."Work/Static/Manage/css/", true);
        $this->smarty->assign("JsUrl", URL_PATH."Work/Static/Manage/js/", true);
        $this->smarty->assign("ImgUrl", URL_PATH."Work/Static/Manage/images/", true);
        $this->smarty->assign("PluginsUrl", URL_PATH."Work/Static/Manage/plugins/", true);
        $this->smarty->assign("StaticUrl", IMG_PATH, true);
    }
    //检测用户是否登录 session 检测
    public function check_login(){
        if($this->intSession->getCookiearray('user') && count($this->intSession->getCookiearray('user')) > 0){
            $login_user = $this->intSession->getCookiearray('user');
            if(!empty($login_user) && $login_user){
                $iuser = $this->Show_css->getOne("cms_users","user_id='{$login_user['user_id']}'");
                if(!$iuser){
                    $this->intSession->setCookiearray("user",array(),'1');
                    return false;
                }else{
                    $this->UserLogin = $iuser;
                    return true;
                }
            }else{
                return false;
            }
        }else{
            return false;
        }
    }
    //检测用户是否有权限访问
    public function check_purview($purview){
        if($this->UserLogin['user_purview'] == 'all'){
            return false;
        }else{
            if(!strchr($this->UserLogin['user_purview'],$purview)){
                return true;
            }else{
                return false;
            }
        }
    }

    function addStudentIntegral($company_id, $school_id, $student_id, $course_id, $integral = 0, $integrallog_rule, $staffer_id = 0, $playname = '', $note = '', $time = '')
    {
        if (!$time || $time == '') {
            $time = time();
        }

        $stuIntOne = $this->Show_css->getFieldOne("smc_student_virtual_property", "property_integralbalance", "student_id='{$student_id}'");
        $stuIntOne['property_integralbalance'] = $stuIntOne['property_integralbalance'] ? $stuIntOne['property_integralbalance'] : 0;

        $integrallog_data = array();
        $integrallog_data['student_id'] = $student_id;
        $integrallog_data['company_id'] = $company_id;
        $integrallog_data['school_id'] = $school_id;
        $integrallog_data['staffer_id'] = $staffer_id;
        $integrallog_data['course_id'] = $course_id;

        $integrallog_data['integrallog_rule'] = $integrallog_rule;
        $integrallog_data['integrallog_playname'] = $playname ? $playname : $this->LgStringSwitch('积分增加');
        $integrallog_data['integrallog_playclass'] = '+';
        $integrallog_data['integrallog_fromamount'] = $stuIntOne['property_integralbalance'];
        $integrallog_data['integrallog_playamount'] = $integral;
        $integrallog_data['integrallog_finalamount'] = $stuIntOne['property_integralbalance'] + $integral;

        $integrallog_data['integrallog_remark'] = $note;
        $integrallog_data['integrallog_reason'] = $integrallog_rule;
        $integrallog_data['integrallog_time'] = $time;
        $this->Show_css->insertData("smc_student_integrallog", $integrallog_data);

        //积分余额
        if ($this->Show_css->getFieldOne("smc_student_virtual_property", "property_integralbalance", "student_id='{$student_id}'")) {
            $data = array();
            $data['property_integralbalance'] = $stuIntOne['property_integralbalance'] + $integral;
            $this->Show_css->updateData("smc_student_virtual_property", "student_id='{$student_id}'", $data);
        } else {
            $data = array();
            $data['student_id'] = $student_id;
            $data['property_integralbalance'] = $integral;
            $this->Show_css->insertData("smc_student_virtual_property", $data);
        }
        return true;
    }


    //管理操作日志
    public function Recordweblog($site_id,$module_id,$actiontype,$content){
        if($this->UserLogin){
            $date = array();
            $date['site_id'] = $site_id;
            $date['user_id'] = $this->UserLogin['user_id'];
            $date['module_id'] = $module_id;
            $date['actiontype'] = $actiontype;
            $date['weblog_content'] = $content;
            $date['weblog_ip'] = real_ip();
            $date['weblog_time'] = time();
            $this->Show_css->insertData('cms_weblog',$date);
            return true;
        }else{
            return false;
        }
    }
    public function display($tempview=""){
        return  $this->smarty->display($tempview);
    }
    public function __call($method, $args) {
        echo "unknown method " . $method;
        return false;

    }
    //后台登录文件
    public function LoginView() {
        $this->display("login.htm");
        exit;
    }
}