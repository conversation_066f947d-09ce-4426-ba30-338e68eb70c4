<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/1/15
 * Time: 10:02
 */

namespace Work\Controller\Manage;


class LinkUpTemplateController extends viewTpl{
    public $data;
    public $u;
    public $t;
    public $c;
    public $iuser;
    public $Viewhtm;

    //预加载处理类
    function __construct() {
        parent::__construct ();
        if(!$this->check_login()){
            $this->LoginView();
        }
        if($this->check_login()){
            $this->smarty->assign("iuser", $this->UserLogin);
            $this->iuser = $this->UserLogin;
        }
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
        $this->Viewhtm = $this->router->getController()."/".$this->router->getUrl().".htm";

        $moduleOne = $this->Show_css->getOne("cms_module","module_mark='{$this->u}'");
        $this->smarty->assign("moduleOne",$moduleOne);
    }

    //沟通模板管理
    function HomeView(){
        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        if (!isset($request['p'])) {
            $p = '1';
        } else {
            $p = $request['p'];
        }

        $datawhere = "1";
        $pageurl = "/{$this->u}/{$this->t}?";
        if ($request['site_id']) {
            $pageurl .= "&site_id={$request['site_id']}";
        }

        $sql = "SELECT t.*,
                (SELECT c.course_cnname FROM smc_course as c WHERE c.course_id=t.course_id) as course_cnname
                FROM eas_course_template as t WHERE {$datawhere} ORDER BY t.template_id DESC";

        $db_nums = $Show_css->select("SELECT COUNT(t.template_id) FROM eas_course_template as t WHERE {$datawhere} ");//相关条件下的总记录数COUNT(*)
        $allnum = $db_nums[0][0];

        $datalist = $Show_css->dbwherePage($sql, $allnum, '10', $pageurl . '&p=', $p, '10', '2');
        $smarty->assign("pagelist", $datalist['pages']);//筛选信息
        $smarty->assign("dataList", $datalist['cont']);
    }

    //添加沟通模板
    function AddView()
    {
        $this->smarty->assign("act", "Add");

        $course = $this->Show_css->selectClear("SELECT course_id,course_cnname,course_branch FROM smc_course WHERE course_status = '1'");
        $this->smarty->assign("course", $course);

        $this->Viewhtm = $this->router->getController() . "/" . "Manage.htm";
    }

    //提交处理机制
    function AddAction()
    {
        $request = Input('post.','','trim,addslashes');
        if ($request['template_name'] == '') {
            ajax_return(array('error' => 1, 'errortip' => "沟通模板名称不能为空!", "bakfuntion" => "warningFromTip"));
        }
        if ($request['course_id'] == '') {
            ajax_return(array('error' => 1, 'errortip' => "课程别必须选择!", "bakfuntion" => "warningFromTip"));
        }

        $data = array();
        $data['course_id'] = $request['course_id'];
        $data['template_name'] = $request['template_name'];
        if ($this->Show_css->insertData("eas_course_template", $data)) {
            ajax_return(array('error' => 0, 'errortip' => "新增成功!", "bakfuntion" => "successFromTip", "bakurl" => "/{$this->u}?site_id={$request['site_id']}"));
        } else {
            ajax_return(array('error' => 1, 'errortip' => "新增失败!", "bakfuntion" => "dangerFromTip"));
        }
    }

    //编辑沟通模板
    function EditView()
    {
        $request = Input('get.','','trim,addslashes');

        $this->smarty->assign("act", "Edit");

        $dataOne = $this->Show_css->getOne("eas_course_template","template_id='{$request['template_id']}'");
        $this->smarty->assign("dataVar", $dataOne);

        $course = $this->Show_css->selectClear("SELECT course_id,course_cnname,course_branch FROM smc_course WHERE course_status = '1'");
        $this->smarty->assign("course", $course);

        $this->Viewhtm = $this->router->getController() . "/" . "Manage.htm";
    }

    //提交处理机制
    function EditAction()
    {
        $request = Input('post.','','trim,addslashes');
        if ($request['template_name'] == '') {
            ajax_return(array('error' => 1, 'errortip' => "沟通模板名称不能为空!", "bakfuntion" => "warningFromTip"));
        }
        if ($request['course_id'] == '') {
            ajax_return(array('error' => 1, 'errortip' => "课程别必须选择!", "bakfuntion" => "warningFromTip"));
        }

        $data = array();
        $data['course_id'] = $request['course_id'];
        $data['template_name'] = $request['template_name'];
        if ($this->Show_css->updateData("eas_course_template", "template_id='{$request['template_id']}'", $data)) {
            ajax_return(array('error' => 0, 'errortip' => "编辑成功!", "bakfuntion" => "successFromTip", "bakurl" => "/{$this->u}?site_id={$request['site_id']}"));
        } else {
            ajax_return(array('error' => 1, 'errortip' => "编辑失败!", "bakfuntion" => "dangerFromTip"));
        }
    }

    //删除沟通模板
    function DelAction()
    {
        $request = Input('get.','','trim,addslashes');
        if ($this->Show_css->delData('eas_course_template', "template_id='{$request['id']}'")) {
            ajax_return(array('error' => 0, 'errortip' => "提交成功!", "bakfuntion" => "okmotify", "bakurl" => "/{$this->u}?site_id={$request['site_id']}"));
        } else {
            ajax_return(array('error' => 1, 'errortip' => "删除失败!", "bakfuntion" => "errormotify"));
        }
    }


    //沟通模板管理
    function TemplateContentView(){
        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        if (!isset($request['p'])) {
            $p = '1';
        } else {
            $p = $request['p'];
        }

        $datawhere = "tc.template_id='{$request['template_id']}'";
        $pageurl = "/{$this->u}/{$this->t}?";
        if ($request['site_id']) {
            $pageurl .= "&site_id={$request['site_id']}";
        }

        $sql = "SELECT tc.*,
                (SELECT t.template_name FROM eas_course_template as t WHERE t.template_id=tc.template_id) as template_name
                FROM eas_course_template_content as tc WHERE {$datawhere} ORDER BY tc.content_id DESC";

        $db_nums = $Show_css->select("SELECT COUNT(tc.content_id) FROM eas_course_template_content as tc WHERE {$datawhere} ");//相关条件下的总记录数COUNT(*)
        $allnum = $db_nums[0][0];

        $datalist = $Show_css->dbwherePage($sql, $allnum, '10', $pageurl . '&p=', $p, '10', '2');
        $smarty->assign("pagelist", $datalist['pages']);//筛选信息
        $smarty->assign("dataList", $datalist['cont']);
        $smarty->assign("template_id", $request['template_id']);
    }

    //添加沟通模板
    function TemplateAddView()
    {
        $request = Input('get.','','trim,addslashes');

        $this->smarty->assign("template_id", $request['template_id']);

        $this->smarty->assign("act", "TemplateAdd");

        $this->Viewhtm = $this->router->getController() . "/" . "TemplateManage.htm";
    }

    //提交处理机制
    function TemplateAddAction()
    {
        $request = Input('post.','','trim,addslashes');
        if ($request['content'] == '') {
            ajax_return(array('error' => 1, 'errortip' => "沟通模板名称不能为空!", "bakfuntion" => "warningFromTip"));
        }

        $data = array();
        $data['template_id'] = $request['template_id'];
        $data['content'] = $request['content'];
        $data['content_addtime'] = time();
        if ($this->Show_css->insertData("eas_course_template_content", $data)) {
            ajax_return(array('error' => 0, 'errortip' => "新增成功!", "bakfuntion" => "successFromTip", "bakurl" => "/{$this->u}/TemplateContent?site_id={$request['site_id']}&template_id={$request['template_id']}"));
        } else {
            ajax_return(array('error' => 1, 'errortip' => "新增失败!", "bakfuntion" => "dangerFromTip"));
        }
    }

    //编辑沟通模板
    function TemplateEditView()
    {
        $request = Input('get.','','trim,addslashes');

        $this->smarty->assign("act", "TemplateEdit");

        $this->smarty->assign("template_id", $request['template_id']);

        $dataOne = $this->Show_css->getOne("eas_course_template_content","content_id='{$request['content_id']}'");
        $this->smarty->assign("dataVar", $dataOne);

        $this->Viewhtm = $this->router->getController() . "/" . "TemplateManage.htm";
    }

    //提交处理机制
    function TemplateEditAction()
    {
        $request = Input('post.','','trim,addslashes');
        if ($request['content'] == '') {
            ajax_return(array('error' => 1, 'errortip' => "沟通模板名称不能为空!", "bakfuntion" => "warningFromTip"));
        }

        $data = array();
        $data['template_id'] = $request['template_id'];
        $data['content'] = $request['content'];
        if ($this->Show_css->updateData("eas_course_template_content", "content_id='{$request['content_id']}'", $data)) {
            ajax_return(array('error' => 0, 'errortip' => "编辑成功!", "bakfuntion" => "successFromTip", "bakurl" => "/{$this->u}/TemplateContent?site_id={$request['site_id']}&template_id={$request['template_id']}"));
        } else {
            ajax_return(array('error' => 1, 'errortip' => "编辑失败!", "bakfuntion" => "dangerFromTip"));
        }
    }

    //删除沟通模板
    function TemplateDelAction()
    {
        $request = Input('get.','','trim,addslashes');
        if ($this->Show_css->delData('eas_course_template_content', "content_id='{$request['id']}'")) {
            ajax_return(array('error' => 0, 'errortip' => "提交成功!", "bakfuntion" => "okmotify", "bakurl" => "/{$this->u}/TemplateContent?site_id={$request['site_id']}&template_id={$request['template_id']}"));
        } else {
            ajax_return(array('error' => 1, 'errortip' => "删除失败!", "bakfuntion" => "errormotify"));
        }
    }


    //魔术方法
    public function __call($name, $arguments) {
        $this->smarty->assign("errorTip", "Calling object method '$name' ". implode(', ', $arguments). "\n");
        $this->Viewhtm = "under.htm";
    }

    //魔术函数
    function __destruct()
    {
        $site_id = Input('get.site_id',0);

        if ($this->c =='Wait') {
            $this->smarty->assign("u", $this->router->getController());
            $this->smarty->assign("t", $this->router->getUrl());
            $this->smarty->assign("Viewhtm", $this->Viewhtm);
            $websites = $this->Show_css->getOne("cms_websites","site_id='{$site_id}'");
            $this->smarty->assign("websites",$websites);
            if($site_id){
                if($this->UserLogin['user_type'] == '1' and $this->UserLogin['user_limitsinc'] == '1'){
                    $moduleList = $this->Show_css->select("SELECT m.* FROM cms_module as m LEFT JOIN cms_moduleapply as a ON a.module_id = m.module_id WHERE a.site_id = '{$site_id}' ORDER BY m.module_weight ASC ");
                }else{
                    $moduleList = $this->Show_css->select("SELECT m.* FROM cms_module as m LEFT JOIN cms_moduleapply as a ON a.module_id = m.module_id LEFT JOIN cms_websites_func as c ON c.site_id = a.site_id and c.module_id = a.module_id WHERE a.site_id = '{$site_id}' and c.user_id = '{$this->UserLogin['user_id']}' ORDER BY m.module_weight ASC ");
                }
                // print_r($moduleList);die;
                $this->smarty->assign("moduleList", $moduleList);
                $this->display("websiteindex.htm");
            }else{
                $this->display("index.htm");
            }
        }
        exit;
    }
}