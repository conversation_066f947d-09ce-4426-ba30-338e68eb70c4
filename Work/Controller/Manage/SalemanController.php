<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2019/2/16
 * Time: 11:56
 */

namespace Work\Controller\Manage;


class SalemanController extends viewTpl
{
    public $data;
    public $u;
    public $t;
    public $c;
    public $icampus;
    public $iuser;
    public $Viewhtm;
    public $Module;

    //预加载处理类
    function __construct()
    {
        parent::__construct();
        if (!$this->check_login()) {
            $this->LoginView();
        }
        if ($this->check_login()) {
            $this->smarty->assign("iuser", $this->UserLogin);
            $this->iuser = $this->UserLogin;
        }
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
        $this->Viewhtm = $this->router->getController() . "/" . $this->router->getUrl() . ".htm";
        $moduleOne = $this->Show_css->getOne("cms_module", "module_mark='{$this->u}'");
        $this->smarty->assign("moduleOne", $moduleOne);
        $this->Module = $moduleOne;
    }

    //主页
    function HomeView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        if (!isset($request['p'])) {
            $p = '1';
        } else {
            $p = $request['p'];
        }

        $datawhere = "1";
        $pageurl = "/{$this->u}/{$this->t}?site_id={$request['site_id']}";

        $datatype = array();
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (c.saleman_cnname like '%{$request['keyword']}%' or c.saleman_enname like '%{$request['keyword']}%' or  c.saleman_jobnumber like '%{$request['keyword']}%')";
            $pageurl .= "&keyword={$request['keyword']}";
            $datatype['keyword'] = $request['keyword'];
        }
        $sql = "SELECT c.*
                FROM imc_saleman as c where {$datawhere} order by c.saleman_isleave ASC,c.saleman_id DESC";


        $db_nums = $Show_css->selectOne("SELECT COUNT(c.saleman_id) as countnums FROM imc_saleman as c where {$datawhere}");//相关条件下的总记录数COUNT(*)
        $allnum = $db_nums['countnums'];

        $datalist = $Show_css->dbwherePage($sql, $allnum, '10', $pageurl . '&p=', $p, '10', '2');

        $smarty->assign("pagelist", $datalist['pages']);//筛选信息

        $smarty->assign("dataList", $datalist['cont']);
        $smarty->assign("datatype", $datatype);
    }

    function AddView()
    {
        $smarty = $this->smarty;
        $datatype = array();
        $smarty->assign("datatype", $datatype);
        $smarty->assign("act", "Add");
        $this->Viewhtm = $this->router->getController() . "/" . "Edit.htm";


    }

    function AddAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $Show_css = $this->Show_css;
        $bakurl = "/{$this->u}?site_id={$request['site_id']}";

        if (!isset($request['saleman_pass']) || $request['saleman_pass'] == '') {
            ajax_return(array('error' => 1, 'errortip' => "密码必须填写!", "bakfuntion" => "dangerFromTip"));
        }

        $data = array();
        $like = date("Ymd", time());
        $jobnumbero = $this->Show_css->selectOne("select saleman_jobnumber from imc_saleman where saleman_jobnumber like '{$like}%' order by saleman_jobnumber DESC limit 0,1");
        if ($jobnumbero) {
            $data['saleman_jobnumber'] = $jobnumbero['saleman_jobnumber'] + 1;
        } else {
            $data['saleman_jobnumber'] = $like . '000001';
        }

        $data['saleman_cnname'] = $request['saleman_cnname'];
        $data['saleman_enname'] = $request['saleman_enname'];
        $data['saleman_tephone'] = $request['saleman_tephone'];
        $data['saleman_mobile'] = $request['saleman_mobile'];
        $data['saleman_pass'] = md5($request['saleman_pass']);
        $data['saleman_bakpass'] = $request['saleman_pass'];
        $data['saleman_isleave'] = $request['saleman_isleave'];
        $data['saleman_createtime'] = time();

        if ($Show_css->insertData("imc_saleman", $data)) {
            $this->Recordweblog($request['site_id'], $this->Module['module_id'], $this->c, "新增销售人员");
            ajax_return(array('error' => 0, 'errortip' => "提交成功!", "bakfuntion" => "successFromTip", "bakurl" => $bakurl));
        } else {
            ajax_return(array('error' => 1, 'errortip' => "提交失败!", "bakfuntion" => "dangerFromTip"));
        }
    }

    //编辑
    function EditView()
    {
        $smarty = $this->smarty;
        $datatype = array();
        $request = Input('get.', '', 'trim,addslashes');
        $SaleManOne = $this->Show_css->selectOne("select * from  imc_saleman where saleman_id='{$request['id']}'  limit 0,1");
        $smarty->assign("dataVar", $SaleManOne);
        $smarty->assign("datatype", $datatype);
        $smarty->assign("act", "edit");
        $this->Viewhtm = $this->router->getController() . "/" . "Edit.htm";
    }

    function EditAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $bakurl = "/{$this->u}?site_id={$request['site_id']}";

        if (isset($request['saleman_id']) && $request['saleman_id'] == '') {
            $res = array('error' => '1', 'errortip' => '请选择销售人员!', "bakfuntion" => "errormotify");
            ajax_return($res);
        }
        $Show_css = $this->Show_css;
        $data = array();
        $data['saleman_cnname'] = $request['saleman_cnname'];
        $data['saleman_enname'] = $request['saleman_enname'];
        $data['saleman_tephone'] = $request['saleman_tephone'];
        $data['saleman_mobile'] = $request['saleman_mobile'];
        $data['saleman_isleave'] = $request['saleman_isleave'];
        if (isset($request['saleman_pass']) && $request['saleman_pass'] != '') {
            $data['saleman_pass'] = md5($request['saleman_pass']);
            $data['saleman_bakpass'] = $request['saleman_pass'];
        }
        $data['saleman_createtime'] = time();;
        if ($Show_css->updateData('imc_saleman', "saleman_id='{$request['saleman_id']}'", $data)) {
            $this->Recordweblog($request['site_id'], $this->Module['module_id'], $this->c, "编辑销售人员信息");
            ajax_return(array('error' => 0, 'errortip' => "提交成功!", "bakfuntion" => "successFromTip", "bakurl" => $bakurl));
        } else {
            ajax_return(array('error' => 1, 'errortip' => "提交失败!", "bakfuntion" => "dangerFromTip"));
        }

    }

    //导入券码 -临时代码

    function ImportCouponsView()
    {

    }

    function ImportCouExcelView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        if ($_FILES['uploadFile']['name'] == '' || $_FILES['uploadFile']['error'] == '1') {
            $PlayInfoVar = array();
            $PlayInfoVar['course_branch'] = "导入出错";
            $PlayInfoVar['error'] = "1";
            $PlayInfoVar['errortip'] = "文件不存在";
            $PlayInfo[] = $PlayInfoVar;
            $this->smarty->assign("PlayInfo", $PlayInfo);
            exit;
        }

        $fileType = array('xls', 'csv', 'xlsx');
        $uploadfile = new \Webfile($_FILES['uploadFile'], $files_dir = '../static/file', $size = 2097152 * 10, $fileType);
        $up_file = $uploadfile->upload();

        $PlayInfo = array();
        if ($up_file && $uploadfile->updatastatus) {
            $ExeclName = array();
            $ExeclName['券名称'] = "coupons_name";
            $ExeclName['券编码'] = "coupons_pid";
            $ExeclName['生效时间'] = "coupons_starttime";
            $ExeclName['截止日期'] = "coupons_endtime";
            $ExeclName['截止日期'] = "coupons_endtime";

            $WorkerList = execl_to_array($up_file, $ExeclName);
            unset($WorkerList[0]);
            if ($WorkerList) {
                foreach ($WorkerList as $WorkerrVar) {
                    if ($WorkerrVar['coupons_pid'] !== '') {
                        $workersList[] = $WorkerrVar;
                    } else {
                        $PlayInfoVar = array();
                        $PlayInfoVar['coupons_name'] = $WorkerrVar['coupons_name'];
                        $PlayInfoVar['coupons_pid'] = $WorkerrVar['coupons_pid'];
                        $PlayInfoVar['coursecat_cnname'] = $WorkerrVar['coursecat_cnname'];
                        $PlayInfoVar['effective_date'] = $WorkerrVar['effective_date'];
                        $PlayInfoVar['error'] = "1";
                        $PlayInfoVar['errortip'] = "导入信息不完整";
                        $PlayInfo[] = $PlayInfoVar;
                    }
                }
            }
            if (count($workersList) > 1000) {
                ajax_return(array('error' => 1, 'errortip' => "导入数量不能大于1000!", "bakfuntion" => "errormotify"));
            }

            if ($workersList) {
                foreach ($workersList as $workersVar) {
                    $data = array();
                    $data['company_id'] = '8888';
                    $data['coupons_pid'] = $workersVar['coupons_pid'];
                    $data['coupons_name'] = $workersVar['coupons_name'];
                    if ($workersVar['coupons_name'] == "吉的堡50元代金券") {
                        $data['coupons_code'] = "LW50";
                        $data['coupons_price'] = "50";
                    } else {
                        continue;
                    }
                    $data['coupons_password'] = $workersVar['coupons_password'];
                    $data['coupons_brand_name'] = "捞王";
//                    $coupons_effective_date = explode(" - ",$workersVar['effective_date']);
                    $data['coupons_starttime'] =$workersVar['coupons_starttime'];
                    $data['coupons_endtime'] =$workersVar['coupons_endtime'];
                    $data['coupons_createtime'] = time();
                    $this->Show_css->insertData("smc_integral_cooperation_coupons", $data);

                    $PlayInfoVar = array();
                    $PlayInfoVar['coupons_name'] = $workersVar['coupons_pid'];
                    $PlayInfoVar['coupons_pid'] = $workersVar['coursetype_cnname'];
                    $PlayInfoVar['effective_date'] = $workersVar['effective_date'];
                    $PlayInfoVar['coupons_password'] = $workersVar['coupons_password'];
                    $PlayInfoVar['error'] = "0";
                    $PlayInfo[] = $PlayInfoVar;
                }
            } else {
                $PlayInfoVar = array();
                $PlayInfoVar['company_id'] = '文件为空';
                $PlayInfoVar['coursetype_cnname'] = '文件为空';
                $PlayInfoVar['coursecat_cnname'] = '文件为空';
                $PlayInfoVar['course_cnname'] = '文件为空';
                $PlayInfoVar['error'] = "1";
                $PlayInfoVar['errortip'] = "文件为空";
                $PlayInfo[] = $PlayInfoVar;
            }
        } else {
            $PlayInfoVar = array();
            $PlayInfoVar['company_id'] = '文件不存在';
            $PlayInfoVar['coursetype_cnname'] = '文件不存在';
            $PlayInfoVar['coursecat_cnname'] = '文件不存在';
            $PlayInfoVar['course_cnname'] = '文件不存在';
            $PlayInfoVar['error'] = "1";
            $PlayInfoVar['errortip'] = "文件不存在";
            $PlayInfo[] = $PlayInfoVar;
        }
        $this->smarty->assign("PlayInfo", $PlayInfo);


    }


    function __destruct()
    {
        $site_id = Input('get.site_id', 0);

        if ($this->c == 'Wait') {
            $this->smarty->assign("u", $this->router->getController());
            $this->smarty->assign("t", $this->router->getUrl());
            $this->smarty->assign("Viewhtm", $this->Viewhtm);
            $websites = $this->Show_css->getOne("cms_websites", "site_id='{$site_id}'");
            $this->smarty->assign("websites", $websites);
            if ($site_id) {
                $this->display("websiteindex.htm");
            } else {
                $this->display("index.htm");
            }
        }
        exit;
    }

}