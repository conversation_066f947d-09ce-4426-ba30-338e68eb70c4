<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn 
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/2/14
 * Time: 10:15
 */

namespace Work\Controller\Manage;


class FeedbackController extends viewTpl{
    public $data;
    public $u;
    public $t;
    public $c;
    public $icampus;
    public $iuser;
    public $Viewhtm;
    public $Module;

    //预加载处理类
    function __construct() {
        parent::__construct ();
        if(!$this->check_login()){
            $this->LoginView();
        }
        if($this->check_login()){
            $this->smarty->assign("iuser", $this->UserLogin);
            $this->iuser = $this->UserLogin;
        }
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
        $this->Viewhtm = $this->router->getController()."/".$this->router->getUrl().".htm";
        $moduleOne = $this->Show_css->getOne("cms_module","module_mark='{$this->u}'");
        $this->smarty->assign("moduleOne",$moduleOne);
        $this->Module = $moduleOne;
    }

    //主页
    function HomeView()
    {
        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        if(!isset($request['p'])){
            $p = '1';
        }else{
            $p= $request['p'];
        }

        $datawhere = "1";
        $pageurl = "/{$this->u}/{$this->t}?";
        if ($request['site_id']) {
            $pageurl .= "&site_id={$request['site_id']}";
        }

        $datatype = array();
        if(isset($request['keyword']) && $request['keyword'] !==''){
            $datawhere .= " and ( c.feedback_name like '%{$request['keyword']}%' or c.feedback_mobile like '%{$request['keyword']}%' ) ";
            $pageurl .="&keyword={$request['keyword']}";
            $datatype['keyword'] = $request['keyword'];
        }

        // if(isset($request['status'])){
        //     $datawhere .= " and c.tb_status = '{$request['status']}'";
        //     $pageurl .="&status={$request['status']}";
        //     $datatype['status'] = $request['status'];
        // }

        $sql = "SELECT c.* FROM cms_feedback as c where {$datawhere} order by c.feedback_id DESC";

        $db_nums = $Show_css->select("SELECT COUNT(c.feedback_id) FROM cms_feedback as c where {$datawhere}");//相关条件下的总记录数COUNT(*)
        $allnum = $db_nums[0][0];

        $datalist = $Show_css->dbwherePage($sql,$allnum,'10',$pageurl.'&p=',$p,'10','2');

        $smarty->assign("pagelist",$datalist['pages']);//筛选信息

        $smarty->assign("dataList",$datalist['cont']);
        $smarty->assign("datatype",$datatype);
    }

    //活动编辑
    function EditView()
    {
        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        $smarty->assign("act","Edit");

        $activity = $Show_css->getOne("cms_feedback","client_id='{$request['id']}'");
        $smarty->assign("dataVar",$activity);

        $employeeList = $Show_css->getList("hrm_employee","is_leave <> '1'");
        $smarty->assign("employeeList",$employeeList);

        $this->Viewhtm = $this->router->getController()."/"."Edit.htm";
    }
    //活动编辑
    function EditAction()
    {
        $request = Input('post.','','trim,addslashes');
        $Show_css = $this->Show_css;

        $data = array();
        $data['type'] = $request['type'];
        $data['employee_id'] = $request['employee_id'];
        $data['trace_type'] = $request['trace_type'];
        $data['trace_time'] = $request['trace_time'];
        $data['company_name'] = $request['company_name'];
        $data['company_sname'] = getfirstchar($request['company_name']);
        $data['phone'] = $request['phone'];
        $data['mobile'] = $request['mobile'];
        if(isset($request['password']) && $request['password'] !==''){
            $data['password'] = md5(trim($request['password']));
        }
        $data['fax'] = $request['fax'];
        $data['address'] = $request['address'];
        $data['remark'] = $request['remark'];
        $data['client_img'] = $request['client_img'];
        $data['amend_time'] = time();

        if($Show_css->updateData("cms_feedback","client_id = '{$request['client_id']}'",$data)){
            $this->Recordweblog($request['site_id'],$this->Module['module_id'],$this->c,"修改客户数据，客户ID:{$request['client_id']}");
            ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"successFromTip"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "提交失败!","bakfuntion"=>"dangerFromTip"));
        }
    }
    //添加客户
    function AddView()
    {
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;
        $smarty->assign("act","Add");

        $employeeList = $Show_css->getList("hrm_employee","is_leave <> '1'");
        $smarty->assign("employeeList",$employeeList);

        $this->Viewhtm = $this->router->getController()."/"."Edit.htm";
    }
    //新增客户
    function AddAction()
    {
        $request = Input('post.','','trim,addslashes');
        $Show_css = $this->Show_css;

        $data = array();
        $data['type'] = $request['type'];
        $data['employee_id'] = $request['employee_id'];
        $data['trace_type'] = $request['trace_type'];
        $data['trace_time'] = $request['trace_time'];
        $data['company_name'] = $request['company_name'];
        $data['company_sname'] = getfirstchar($request['company_name']);
        $data['phone'] = $request['phone'];
        $data['mobile'] = $request['mobile'];
        if(isset($request['password']) && $request['password'] !==''){
            $data['password'] = md5(trim($request['password']));
        }else{
            $data['password'] = md5("123456");
        }

        $data['fax'] = $request['fax'];
        $data['address'] = $request['address'];
        $data['remark'] = $request['remark'];
        $data['client_img'] = $request['client_img'];
        $data['entering_time'] = time();
        $data['amend_time'] = time();
        if($Show_css->insertData("cms_feedback",$data)){
            $this->Recordweblog($request['site_id'],$this->Module['module_id'],$this->c,"新增客户数据");
            ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"successFromTip","bakurl"=>"/{$this->u}?site_id={$request['site_id']}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "提交失败!","bakfuntion"=>"dangerFromTip"));
        }
    }
    //删除报名活动
    function DelAction()
    {
        $list_id = Input('get.id',0);

        if($this->Show_css->delData('cms_feedback',"feedback_id='{$list_id}'")){
            ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"okmotify"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "新增失败!","bakfuntion"=>"errormotify"));
        }
    }
    function __destruct()
    {
        $site_id = Input('get.site_id',0);

        if ($this->c =='Wait') {
            $this->smarty->assign("u", $this->router->getController());
            $this->smarty->assign("t", $this->router->getUrl());
            $this->smarty->assign("Viewhtm", $this->Viewhtm);
            $websites = $this->Show_css->getOne("cms_websites","site_id='{$site_id}'");
            $this->smarty->assign("websites",$websites);
            if($site_id){
                if($this->UserLogin['user_type'] == '1' and $this->UserLogin['user_limitsinc'] == '1'){
                    $moduleList = $this->Show_css->select("SELECT m.* FROM cms_module as m LEFT JOIN cms_moduleapply as a ON a.module_id = m.module_id WHERE a.site_id = '{$site_id}' ORDER BY m.module_weight ASC ");
                }else{
                    $moduleList = $this->Show_css->select("SELECT m.* FROM cms_module as m LEFT JOIN cms_moduleapply as a ON a.module_id = m.module_id LEFT JOIN cms_websites_func as c ON c.site_id = a.site_id and c.module_id = a.module_id WHERE a.site_id = '{$site_id}' and c.user_id = '{$this->UserLogin['user_id']}' ORDER BY m.module_weight ASC ");
                }
                $this->smarty->assign("moduleList", $moduleList);

                $this->display("websiteindex.htm");
            }else{
                $this->display("index.htm");
            }
        }
        exit;
    }
}