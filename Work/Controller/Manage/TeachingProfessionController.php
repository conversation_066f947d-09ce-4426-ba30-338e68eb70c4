<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2019/12/12
 * Time: 11:06
 */

namespace Work\Controller\Manage;


class TeachingProfessionController extends viewTpl{
    public $data;
    public $u;
    public $t;
    public $c;
    public $iuser;
    public $Viewhtm;

    //预加载处理类
    function __construct() {
        parent::__construct ();
        if(!$this->check_login()){
            $this->LoginView();
        }
        if($this->check_login()){
            $this->smarty->assign("iuser", $this->UserLogin);
            $this->iuser = $this->UserLogin;
        }
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
        $this->Viewhtm = $this->router->getController()."/".$this->router->getUrl().".htm";

        $moduleOne = $this->Show_css->getOne("cms_module","module_mark='{$this->u}'");
        $this->smarty->assign("moduleOne",$moduleOne);
    }

    //主页
    function HomeView(){
        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        if (!isset($request['p'])) {
            $p = '1';
        } else {
            $p = $request['p'];
        }

        $datawhere = "company_id = '8888'";
        $pageurl = "/{$this->u}/{$this->t}?";
        if ($request['site_id']) {
            $pageurl .= "&site_id={$request['site_id']}";
        }

        $datatype = array();
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (c.career_cnname like '%{$request['keyword']}%' or c.career_enname like '%{$request['keyword']}%' or c.career_branch like '%{$request['keyword']}%')";
            $pageurl .= "&keyword={$request['keyword']}";
            $datatype['keyword'] = $request['keyword'];
        }

        $sql = "SELECT c.*,
                (select ec.career_cnname from eas_career as ec where c.career_nextid = ec.career_id) as career_name,
                (select ct.careertype_cnname from eas_code_careertype as ct where c.careertype_id = ct.careertype_id) as careertype_cnname
                FROM eas_career AS c WHERE {$datawhere} ORDER BY career_id ASC";

        $db_nums = $Show_css->select("SELECT COUNT(c.career_id) FROM eas_career AS c WHERE {$datawhere}");//相关条件下的总记录数COUNT(*)
        $allnum = $db_nums[0][0];

        $datalist = $Show_css->dbwherePage($sql, $allnum, '10', $pageurl . '&p=', $p, '10', '2');

        $careertype = $this->Show_css->selectClear("SELECT careertype_id,careertype_cnname FROM eas_code_careertype WHERE company_id = '8888'");
        $smarty->assign("careertype", $careertype);

        $smarty->assign("pagelist", $datalist['pages']);//筛选信息
        $smarty->assign("dataList", $datalist['cont']);
        $smarty->assign("datatype", $datatype);
    }

    //添加职业明细
    function AddView(){
        $this->smarty->assign("act","Add");

        $careertype = $this->Show_css->selectClear("SELECT careertype_id,careertype_cnname FROM eas_code_careertype WHERE company_id = '8888'");
        $this->smarty->assign("careertype", $careertype);

        $career = $this->Show_css->selectClear("SELECT career_id,career_cnname FROM eas_career WHERE company_id = '8888'");
        $this->smarty->assign("career", $career);

        $this->Viewhtm = $this->router->getController()."/"."Manage.htm";
    }

    //提交处理机制
    function AddAction(){
        $request = Input('post.','','trim,addslashes');
        if($request['careertype_id'] == ''){
            ajax_return(array('error' => 1,'errortip' => "职业类型必须选择!","bakfuntion"=>"dangerFromTip"));
        }
        if($request['career_cnname'] == ''){
            ajax_return(array('error' => 1,'errortip' => "职业中文名不能为空!","bakfuntion"=>"dangerFromTip"));
        }
        if($request['career_enname'] == ''){
            ajax_return(array('error' => 1,'errortip' => "职业英文名不能为空!","bakfuntion"=>"dangerFromTip"));
        }
        if($request['career_branch'] == ''){
            ajax_return(array('error' => 1,'errortip' => "职业编号不能为空!","bakfuntion"=>"dangerFromTip"));
        }
        if($request['career_sort'] == ''){
            ajax_return(array('error' => 1,'errortip' => "职业排序不能为空!","bakfuntion"=>"dangerFromTip"));
        }
        if($this->Show_css->getOne("eas_career","career_branch='{$request['career_branch']}'")){
            ajax_return(array('error' => 1,'errortip' => "职业编号重复!","bakfuntion"=>"dangerFromTip"));
        }
        if($this->Show_css->getOne("eas_career","career_sort='{$request['career_sort']}'")){
            ajax_return(array('error' => 1,'errortip' => "职业排序重复!","bakfuntion"=>"dangerFromTip"));
        }
        $careerOne = $this->Show_css->selectOne("SELECT career_id FROM eas_career ORDER BY career_id DESC");
        if($request['career_sort'] < $careerOne['career_id']){
            if($request['career_nextid'] == ''){
                ajax_return(array('error' => 1,'errortip' => "下一级别职业必须选择!","bakfuntion"=>"dangerFromTip"));
            }
        }
        if($request['career_sort'] >= $request['career_nextid']){
            if($request['career_nextid'] !== ''){
                ajax_return(array('error' => 1,'errortip' => "职业级别已达最高级，无需选择下一级别职业!","bakfuntion"=>"dangerFromTip"));
            }
        }

        $data = array();
        $data['company_id'] = '8888';
        $data['careertype_id'] = $request['careertype_id'];
        $data['career_cnname'] = $request['career_cnname'];
        $data['career_enname'] = $request['career_enname'];
        $data['career_branch'] = $request['career_branch'];
        $data['career_nextid'] = $request['career_nextid'];
        $data['career_sort'] = $request['career_sort'];
        $data['career_createtime'] = time();
        $dataid = $this->Show_css->insertData("eas_career",$data);
        if($dataid){
            ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"successFromTip","bakurl"=>"/{$this->u}?site_id={$request['site_id']}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "新增失败!","bakfuntion"=>"errormotify"));
        }
    }

    //编辑职业明细
    function EditView(){
        $request = Input('get.','','trim,addslashes');

        $this->smarty->assign("act","Edit");

        $dataOne = $this->Show_css->getOne("eas_career","career_id='{$request['career_id']}'");
        $this->smarty->assign("dataVar", $dataOne);

        $careertype = $this->Show_css->selectClear("SELECT careertype_id,careertype_cnname FROM eas_code_careertype WHERE company_id = '8888'");
        $this->smarty->assign("careertype", $careertype);

        $career = $this->Show_css->selectClear("SELECT career_id,career_cnname FROM eas_career WHERE company_id = '8888'");
        $this->smarty->assign("career", $career);

        $this->Viewhtm = $this->router->getController()."/"."Manage.htm";
    }

    //提交处理机制
    function EditAction(){
        $request = Input('post.','','trim,addslashes');
        if($request['careertype_id'] == ''){
            ajax_return(array('error' => 1,'errortip' => "职业类型必须选择!","bakfuntion"=>"dangerFromTip"));
        }
        if($request['career_cnname'] == ''){
            ajax_return(array('error' => 1,'errortip' => "职业中文名不能为空!","bakfuntion"=>"dangerFromTip"));
        }
        if($request['career_enname'] == ''){
            ajax_return(array('error' => 1,'errortip' => "职业英文名不能为空!","bakfuntion"=>"dangerFromTip"));
        }
        if($request['career_branch'] == ''){
            ajax_return(array('error' => 1,'errortip' => "职业编号不能为空!","bakfuntion"=>"dangerFromTip"));
        }
        if($request['career_sort'] == ''){
            ajax_return(array('error' => 1,'errortip' => "职业排序不能为空!","bakfuntion"=>"dangerFromTip"));
        }
        if($this->Show_css->getOne("eas_career","career_id<>'{$request['career_id']}' and career_branch='{$request['career_branch']}'")){
            ajax_return(array('error' => 1,'errortip' => "职业编号重复!","bakfuntion"=>"dangerFromTip"));
        }
        if($this->Show_css->getOne("eas_career","career_id<>'{$request['career_id']}' and career_sort='{$request['career_sort']}'")){
            ajax_return(array('error' => 1,'errortip' => "职业排序重复!","bakfuntion"=>"dangerFromTip"));
        }
        $careerOne = $this->Show_css->selectOne("SELECT career_id FROM eas_career ORDER BY career_id DESC");
        if($request['career_sort'] < $careerOne['career_id']){
            if($request['career_nextid'] == ''){
                ajax_return(array('error' => 1,'errortip' => "下一级别职业必须选择!","bakfuntion"=>"dangerFromTip"));
            }
        }
        if($request['career_sort'] >= $request['career_nextid']){
            if($request['career_nextid'] !== ''){
                ajax_return(array('error' => 1,'errortip' => "职业级别已达最高级，无需选择下一级别职业!","bakfuntion"=>"dangerFromTip"));
            }
        }

        $data = array();
        $data['company_id'] = '8888';
        $data['careertype_id'] = $request['careertype_id'];
        $data['career_cnname'] = $request['career_cnname'];
        $data['career_enname'] = $request['career_enname'];
        $data['career_branch'] = $request['career_branch'];
        $data['career_nextid'] = $request['career_nextid'];
        $data['career_sort'] = $request['career_sort'];
        $data['career_updatatime'] = time();
        if($this->Show_css->updateData("eas_career","career_id='{$request['career_id']}'",$data)){
            ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"successFromTip","bakurl"=>"/{$this->u}?site_id={$request['site_id']}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "编辑失败!","bakfuntion"=>"errormotify"));
        }
    }

    //删除
    function DelAction(){
        $request = Input('get.','','trim,addslashes');

        if($this->Show_css->delData("eas_career","career_id='{$request['id']}'")){
            ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"successFromTip","bakurl"=>"/{$this->u}?site_id={$request['site_id']}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "删除失败!","bakfuntion"=>"errormotify"));
        }
    }

    //适配职务
    function SetPostView(){
        $request = Input('get.','','trim,addslashes');

        $Show_css = $this->Show_css;

        $datalist = $Show_css->selectClear("SELECT p.post_id,p.post_name,p.post_code,
                                            (SELECT COUNT(pc.post_id) FROM eas_post_career as pc WHERE pc.career_id = '{$request['career_id']}' AND pc.post_id = p.post_id) as thestatus
                                            FROM gmc_company_post as p WHERE p.company_id='{$request['company_id']}'");

        $this->smarty->assign("dataList", $datalist);
        $this->smarty->assign("career_id", $request['career_id']);
    }

    //适配职业
    function AtapplyAction()
    {
        $request = Input('get.','','trim,addslashes');

        $data = array();
        $data['post_id'] = $request['post_id'];
        $data['career_id'] = $request['career_id'];
        $this->Show_css->insertData("eas_post_career", $data);

        ajax_return(array('error' => 0, 'errortip' => "适配成功!", "bakfuntion" => "successFromTip"));
    }

    //取消适配
    function AtapplydelAction()
    {
        $request = Input('get.','','trim,addslashes');

        if ($this->Show_css->delData('eas_post_career', "post_id='{$request['post_id']}' and career_id='{$request['career_id']}'")) {
            ajax_return(array('error' => 0, 'errortip' => "取消适配成功!", "bakfuntion" => "successFromTip"));
        } else {
            ajax_return(array('error' => 1, 'errortip' => "取消适配失败!", "bakfuntion" => "dangerFromTip"));
        }
    }


    //批量适配/取消适配
    function batchSetQuestionAction()
    {
        $request = Input('post.','','trim,addslashes');
        if (isset($request['tab_list']) && count($request['tab_list']) > 0) {
            if ($request['type'] == 1) {
                foreach ($request['tab_list'] as $v) {
                    if (!$this->Show_css->getFieldOne('eas_post_career', 'post_id', "career_id='{$request['career_id']}' and career_id='{$v}'")) {
                        $data = array();
                        $data['career_id'] = $request['career_id'];
                        $data['post_id'] = $v;
                        $this->Show_css->insertData('eas_post_career', $data);
                    }
                }
                ajax_return(array('error' => 0, 'errortip' => "批量适配成功!", "bakfuntion" => "refreshpage"));
            } elseif ($request['type'] == 2) {
                foreach ($request['tab_list'] as $v) {
                    $this->Show_css->delData('eas_post_career', "career_id='{$request['career_id']}' and post_id='{$v}'");
                }
                ajax_return(array('error' => 0, 'errortip' => "批量取消适配成功!", "bakfuntion" => "refreshpage"));
            }
        }
    }


    //职业阶段管理
    function CareerStageView(){
        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        if (!isset($request['p'])) {
            $p = '1';
        } else {
            $p = $request['p'];
        }

        $datawhere = "s.career_id='{$request['career_id']}'";
        $pageurl = "/{$this->u}/{$this->t}?";
        if ($request['site_id']) {
            $pageurl .= "&site_id={$request['site_id']}";
        }

        $datatype = array();
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (c.career_cnname like '%{$request['keyword']}%' or c.career_enname like '%{$request['keyword']}%' or c.career_branch like '%{$request['keyword']}%')";
            $pageurl .= "&keyword={$request['keyword']}";
            $datatype['keyword'] = $request['keyword'];
        }

        $sql = "SELECT s.*,
                (select es.stage_cnname from eas_career_stage as es where s.stage_nextid = es.stage_id) as stage_name,
                (select c.career_cnname from eas_career as c where s.career_id = c.career_id) as career_cnname
                FROM eas_career_stage AS s WHERE {$datawhere} ORDER BY s.stage_id ASC";

        $db_nums = $Show_css->select("SELECT COUNT(s.stage_id) FROM eas_career_stage AS s WHERE {$datawhere}");//相关条件下的总记录数COUNT(*)
        $allnum = $db_nums[0][0];

        $datalist = $Show_css->dbwherePage($sql, $allnum, '10', $pageurl . '&p=', $p, '10', '2');

        $smarty->assign("pagelist", $datalist['pages']);//筛选信息
        $smarty->assign("dataList", $datalist['cont']);
        $smarty->assign("datatype", $datatype);
        $smarty->assign("career_id", $request['career_id']);
    }

    //添加职业阶段
    function AddCareerStageView(){
        $request = Input('get.','','trim,addslashes');

        $this->smarty->assign("act","AddCareerStage");

        $this->smarty->assign("career_id", $request['career_id']);

        $stage = $this->Show_css->selectClear("SELECT stage_id,stage_cnname FROM eas_career_stage WHERE career_id='{$request['career_id']}' AND company_id = '8888'");
        $this->smarty->assign("stage", $stage);

        $this->Viewhtm = $this->router->getController()."/"."CareerStageManage.htm";
    }

    //提交处理机制
    function AddCareerStageAction(){
        $request = Input('post.','','trim,addslashes');
        if($request['stage_cnname'] == ''){
            ajax_return(array('error' => 1,'errortip' => "职业阶段中文名不能为空!","bakfuntion"=>"dangerFromTip"));
        }
        if($request['stage_enname'] == ''){
            ajax_return(array('error' => 1,'errortip' => "职业阶段英文名不能为空!","bakfuntion"=>"dangerFromTip"));
        }
        if($request['stage_sort'] == ''){
            ajax_return(array('error' => 1,'errortip' => "职业排序不能为空!","bakfuntion"=>"dangerFromTip"));
        }

        $data = array();
        $data['company_id'] = '8888';
        $data['career_id'] = $request['career_id'];
        $data['stage_cnname'] = $request['stage_cnname'];
        $data['stage_enname'] = $request['stage_enname'];
        $data['stage_nextid'] = $request['stage_nextid'];
        $data['stage_sort'] = $request['stage_sort'];
        $data['stage_createtime'] = time();
        $dataid = $this->Show_css->insertData("eas_career_stage",$data);
        if($dataid){
            ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"successFromTip","bakurl"=>"/{$this->u}/CareerStage?site_id={$request['site_id']}&career_id={$request['career_id']}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "新增失败!","bakfuntion"=>"errormotify"));
        }
    }

    //编辑职业阶段
    function EditCareerStageView(){
        $request = Input('get.','','trim,addslashes');

        $this->smarty->assign("act","EditCareerStage");

        $this->smarty->assign("career_id", $request['career_id']);

        $dataOne = $this->Show_css->getOne("eas_career_stage","stage_id='{$request['stage_id']}'");
        $this->smarty->assign("dataVar", $dataOne);

        $stage = $this->Show_css->selectClear("SELECT stage_id,stage_cnname FROM eas_career_stage WHERE career_id='{$request['career_id']}' AND company_id = '8888'");
        $this->smarty->assign("stage", $stage);

        $this->Viewhtm = $this->router->getController()."/"."CareerStageManage.htm";
    }

    //提交处理机制
    function EditCareerStageAction(){
        $request = Input('post.','','trim,addslashes');
        if($request['stage_cnname'] == ''){
            ajax_return(array('error' => 1,'errortip' => "职业阶段中文名不能为空!","bakfuntion"=>"dangerFromTip"));
        }
        if($request['stage_enname'] == ''){
            ajax_return(array('error' => 1,'errortip' => "职业阶段英文名不能为空!","bakfuntion"=>"dangerFromTip"));
        }
        if($request['stage_sort'] == ''){
            ajax_return(array('error' => 1,'errortip' => "职业排序不能为空!","bakfuntion"=>"dangerFromTip"));
        }

        $stage_id = $this->Show_css->selectOne("SELECT stage_id FROM eas_career_stage WHERE career_id='{$request['career_id']}' AND company_id = '8888' ORDER BY stage_id DESC");
        if($stage_id['stage_id'] == $request['stage_id']){
            if($request['stage_nextid'] !== ''){
                ajax_return(array('error' => 1,'errortip' => "职业阶段已达最高级，无需选择下一阶段!","bakfuntion"=>"dangerFromTip"));
            }
        }else{
            if($request['stage_id'] == $request['stage_nextid'] || $request['stage_id'] > $request['stage_nextid']){
                ajax_return(array('error' => 1,'errortip' => "需选择高于当前阶段!","bakfuntion"=>"dangerFromTip"));
            }
            if($request['stage_nextid'] == ''){
                ajax_return(array('error' => 1,'errortip' => "下一阶段必须选择!","bakfuntion"=>"dangerFromTip"));
            }
        }

        $data = array();
        $data['company_id'] = '8888';
        $data['career_id'] = $request['career_id'];
        $data['stage_cnname'] = $request['stage_cnname'];
        $data['stage_enname'] = $request['stage_enname'];
        $data['stage_nextid'] = $request['stage_nextid'];
        $data['stage_sort'] = $request['stage_sort'];
        $data['stage_updatatime'] = time();
        if($this->Show_css->updateData("eas_career_stage","stage_id='{$request['stage_id']}'",$data)){
            ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"successFromTip","bakurl"=>"/{$this->u}/CareerStage?site_id={$request['site_id']}&career_id={$request['career_id']}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "编辑失败!","bakfuntion"=>"errormotify"));
        }
    }

    //删除职业阶段
    function DelCareerStageAction(){
        $request = Input('get.','','trim,addslashes');

        if($this->Show_css->delData("eas_career_stage","stage_id='{$request['id']}'")){
            ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"successFromTip","bakurl"=>"/{$this->u}/CareerStage?site_id={$request['site_id']}&career_id={$request['career_id']}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "删除失败!","bakfuntion"=>"errormotify"));
        }
    }


    //魔术方法
    public function __call($name, $arguments) {
        $this->smarty->assign("errorTip", "Calling object method '$name' ". implode(', ', $arguments). "\n");
        $this->Viewhtm = "under.htm";
    }

    //魔术函数
    function __destruct()
    {
        $site_id = Input('get.site_id',0);

        if ($this->c =='Wait') {
            $this->smarty->assign("u", $this->router->getController());
            $this->smarty->assign("t", $this->router->getUrl());
            $this->smarty->assign("Viewhtm", $this->Viewhtm);
            $websites = $this->Show_css->getOne("cms_websites","site_id='{$site_id}'");
            $this->smarty->assign("websites",$websites);
            if($site_id){
                if($this->UserLogin['user_type'] == '1' and $this->UserLogin['user_limitsinc'] == '1'){
                    $moduleList = $this->Show_css->select("SELECT m.* FROM cms_module as m LEFT JOIN cms_moduleapply as a ON a.module_id = m.module_id WHERE a.site_id = '{$site_id}' ORDER BY m.module_weight ASC ");
                }else{
                    $moduleList = $this->Show_css->select("SELECT m.* FROM cms_module as m LEFT JOIN cms_moduleapply as a ON a.module_id = m.module_id LEFT JOIN cms_websites_func as c ON c.site_id = a.site_id and c.module_id = a.module_id WHERE a.site_id = '{$site_id}' and c.user_id = '{$this->UserLogin['user_id']}' ORDER BY m.module_weight ASC ");
                }
                $this->smarty->assign("moduleList", $moduleList);
                $this->display("websiteindex.htm");
            }else{
                $this->display("index.htm");
            }
        }
        exit;
    }
}