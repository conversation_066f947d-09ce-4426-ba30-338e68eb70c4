<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2019/2/16
 * Time: 11:56
 */

namespace Work\Controller\Manage;


class ContractController extends viewTpl{
    public $data;
    public $u;
    public $t;
    public $c;
    public $icampus;
    public $iuser;
    public $Viewhtm;
    public $Module;

    //预加载处理类
    function __construct() {
        parent::__construct ();
        if(!$this->check_login()){
            $this->LoginView();
        }
        if($this->check_login()){
            $this->smarty->assign("iuser", $this->UserLogin);
            $this->iuser = $this->UserLogin;
        }
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
        $this->Viewhtm = $this->router->getController()."/".$this->router->getUrl().".htm";
        $moduleOne = $this->Show_css->getOne("cms_module","module_mark='{$this->u}'");
        $this->smarty->assign("moduleOne",$moduleOne);
        $this->Module = $moduleOne;
    }

    //主页
    function HomeView()
    {
        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        if(!isset($request['p'])){
            $p = '1';
        }else{
            $p= $request['p'];
        }

        $datawhere = "1";
        $pageurl = "/{$this->u}/{$this->t}?site_id={$request['site_id']}";

        $datatype = array();
        if(isset($request['keyword']) && $request['keyword'] !==''){
            $datawhere .= " and (c.contract_name like '%{$request['keyword']}%')";
            $pageurl .="&keyword={$request['keyword']}";
            $datatype['keyword'] = $request['keyword'];
        }
        if(isset($request['company_id']) && $request['company_id'] !==''){
            $datawhere .= " and c.company_id ='{$request['company_id']}'";
            $pageurl .="&company_id={$request['company_id']}";
            $datatype['company_id'] = $request['company_id'];
        }
        if(isset($request['edition_id']) && $request['edition_id'] !==''){
            $datawhere .= " and c.edition_id ='{$request['edition_id']}'";
            $pageurl .="&edition_id={$request['edition_id']}";
            $datatype['edition_id'] = $request['edition_id'];
        }

        $companywhere = '1';
        if(isset($request['from']) && $request['from'] =='company' ){
            $companywhere .= " and company_id= '{$request['company_id']}' ";
            $pageurl .="&from={$request['from']}";
            $datatype['from'] = $request['from'];
        }

        $sql = "SELECT c.*,ie.*,
              (select co.company_cnname from gmc_company as co where co.company_id =c.company_id) as company_cnname,
              (select s.saleman_cnname from imc_saleman as s where s.saleman_id =c.saleman_id) as saleman_cnname
              FROM imc_sales_contract as c 
              left join imc_edition as ie on ie.edition_id=c.edition_id
              where {$datawhere} 
              order by c.contract_id DESC";


        $db_nums = $Show_css->selectOne("SELECT COUNT(c.contract_id) as countnums FROM imc_sales_contract as c where {$datawhere}");//相关条件下的总记录数COUNT(*)
        $allnum = $db_nums['countnums'];

        $datalist = $Show_css->dbwherePage($sql,$allnum,'10',$pageurl.'&p=',$p,'10','2');

        $smarty->assign("pagelist",$datalist['pages']);//筛选信息
//        所属公司

        //所属版本
        $editionList = $Show_css->selectClear("select * from imc_edition ");
        $smarty->assign("editionList",$editionList);

        //从公司列表页进入
        $companyList = $Show_css->selectClear("select company_id,company_cnname,company_code from gmc_company where {$companywhere}");
        $smarty->assign("companyList",$companyList);

        $smarty->assign("dataList",$datalist['cont']);
        $smarty->assign("datatype",$datatype);
    }

    function AddView()
    {
        $smarty = $this->smarty;
        $Show_css = $this->Show_css;
        $companywhere = '1';
        $datatype = array();
        $request = Input('get.','','trim,addslashes');
        if(isset($request['from']) && $request['from'] =='company' ){
            $companywhere .= " and company_id= '{$request['company_id']}' ";
            $datatype['company_id'] = $request['company_id'];
            $datatype['from'] = $request['from'];
        }

        $companyList = $Show_css->selectClear("select company_id,company_cnname,company_code from gmc_company where {$companywhere}");

        $saleMans = $Show_css->selectClear("select s.saleman_id,s.saleman_cnname from  imc_saleman as s where s.saleman_isleave = 0  ");
        $editionList=$this->Show_css->getList("imc_edition");

        $smarty->assign("companyList",$companyList);
        $smarty->assign("editionList",$editionList);
        $smarty->assign("saleMans",$saleMans);
        $smarty->assign("datatype",$datatype);
        $smarty->assign("act","Add");
        $this->Viewhtm = $this->router->getController()."/"."Edit.htm";


    }
    //新增合同
    function AddAction()
    {
        $request = Input('post.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $bakurl ="/{$this->u}?site_id={$request['site_id']}";
        if(isset($request['company_id']) && $request['company_id'] =='' ){
            $res = array('error' => '1', 'errortip' => '请选择集团!',"bakfuntion"=>"errormotify");
            ajax_return($res);
        }
        if(isset($request['contract_name']) && $request['contract_name'] =='' ){
            $res = array('error' => '1', 'errortip' => '请填写合同名称!',"bakfuntion"=>"errormotify");
            ajax_return($res);
        }
        if(isset($request['saleman_id']) && $request['saleman_id'] =='' ){
            $res = array('error' => '1', 'errortip' => '请选择销售人员!',"bakfuntion"=>"errormotify");
            ajax_return($res);
        }

        if(isset($request['edition_id']) && $request['edition_id'] =='' ){
            $res = array('error' => '1', 'errortip' => '请选择版本!',"bakfuntion"=>"errormotify");
            ajax_return($res);
        }

        if(isset($request['from']) && $request['from'] =='company'){
            $bakurl .= "&company_id={$request['company_id']}&from=company";
        }

        $data = array();
        $data['company_id'] = $request['company_id'];
        $data['contract_name'] = $request['contract_name'];
        $data['edition_id'] = $request['edition_id'];
        $data['saleman_id']  =$request['saleman_id'];
        $data['contract_maxschoolnum'] = $request['contract_maxschoolnum'];
        $data['contract_maxstudentnum'] = $request['contract_maxstudentnum'];
        $data['contract_starttime'] = $request['contract_starttime'];
        $data['contract_endtime'] = $request['contract_endtime'];
        $data['contract_allprice'] = $request['contract_allprice'];
        $data['contract_couponmoney'] = $request['contract_couponmoney'];
        $data['contract_paymentprice'] = $request['contract_paymentprice'];
        $data['contract_createtime'] = time();

        if($Show_css->insertData("imc_sales_contract",$data)){

            $this->Recordweblog($request['site_id'],$this->Module['module_id'],$this->c,"新增集团合同");
            ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"successFromTip","bakurl"=>$bakurl));
        }else{
            ajax_return(array('error' => 1,'errortip' => "提交失败!","bakfuntion"=>"dangerFromTip"));
        }
    }

    //编辑
    function EditView(){
        $smarty = $this->smarty;
        $Show_css = $this->Show_css;
        $datatype = array();

        $request = Input('get.','','trim,addslashes');
        if(isset($request['from']) && $request['from'] =='company' ){
            $datatype['from'] = $request['from'];
        }

        $contractOne =$this->Show_css->selectOne("select * from  imc_sales_contract where contract_id='{$request['contract_id']}'  limit 0,1");
        $datatype['saleman_id'] = $contractOne['saleman_id'];
        $companyList = $Show_css->selectClear("select company_id,company_cnname,company_code from gmc_company where company_id ='{$contractOne['company_id']}'");
        $saleMans = $Show_css->selectClear(" select s.saleman_id,s.saleman_cnname,saleman_mobile from  imc_saleman as s where s.saleman_isleave =0 ");
        $editionList=$this->Show_css->getList("imc_edition");

        $datatype['company_id'] =$contractOne['company_id'];
        $smarty->assign("companyList",$companyList);
        $smarty->assign("editionList",$editionList);
        $smarty->assign("saleMans",$saleMans);
        $smarty->assign("dataVar",$contractOne);
        $smarty->assign("datatype",$datatype);
        $smarty->assign("act","edit");
        $this->Viewhtm = $this->router->getController()."/"."Edit.htm";
    }

    function EditAction()
    {
        
        $request = Input('post.','','trim,addslashes');

        $bakurl =  "/{$this->u}?site_id={$request['site_id']}";
        if(isset($request['company_id']) && $request['company_id'] =='' ){
            $res = array('error' => '1', 'errortip' => '请选择集团!',"bakfuntion"=>"errormotify");
            ajax_return($res);
        }
        if(isset($request['saleman_id']) && $request['saleman_id'] =='' ){
            $res = array('error' => '1', 'errortip' => '请选择销售人员!',"bakfuntion"=>"errormotify");
            ajax_return($res);
        }
        if(isset($request['saleman_id']) && $request['contract_name'] =='' ){
            $res = array('error' => '1', 'errortip' => '请填写合同名称!',"bakfuntion"=>"errormotify");
            ajax_return($res);
        }
        if(isset($request['edition_id']) && $request['edition_id'] =='' ){
            $res = array('error' => '1', 'errortip' => '请选择版本!',"bakfuntion"=>"errormotify");
            ajax_return($res);
        }

        if(isset($request['contract_id']) && $request['contract_id'] =='' ){
            $res = array('error' => '1', 'errortip' => 'ID不存在',"bakfuntion"=>"errormotify");
            ajax_return($res);
        }
         $Show_css = $this->Show_css;
         $contract=$Show_css->getOne('imc_sales_contract',"contract_id='{$request['contract_id']}'");
        if(!$contract){
            $res = array('error' => '1', 'errortip' => '合同不存在',"bakfuntion"=>"errormotify");
            ajax_return($res);
        }
        if(isset($request['from']) && $request['from'] =='company'){
            $bakurl .= "&from=company&company_id={$contract['company_id']}";
        }


        $data  =array();
        $data['contract_name'] =$request['contract_name'];
        $data['edition_id'] =$request['edition_id'];
        $data['contract_maxschoolnum']  =$request['contract_maxschoolnum'];
        $data['saleman_id']  =$request['saleman_id'];
        $data['contract_maxstudentnum']  =$request['contract_maxstudentnum'];
        $data['contract_starttime']  =$request['contract_starttime'];
        $data['contract_endtime']  =$request['contract_endtime'];
        $data['contract_allprice']  =$request['contract_allprice'];
        $data['contract_couponmoney']  =$request['contract_couponmoney'];
        $data['contract_paymentprice']  =$request['contract_paymentprice'];
        $data['contract_updatatime']  =time();
        if($Show_css->updateData('imc_sales_contract',"contract_id='{$request['contract_id']}'",$data)){
            $this->Recordweblog($request['site_id'],$this->Module['module_id'],$this->c,"编辑集团合同");
            ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"successFromTip","bakurl"=>$bakurl));
        }else{
            ajax_return(array('error' => 1,'errortip' => "提交失败!","bakfuntion"=>"dangerFromTip"));
        }

    }

    function ImportContractExcelView()
    {
        if ($_FILES['uploadFile']['name'] == '' || $_FILES['uploadFile']['error'] == '1') {
            $PlayInfoVar = array();
            $PlayInfoVar['contract_name'] = "导入出错";
            $PlayInfoVar['contract_name'] = "导入出错";
            $PlayInfoVar['error'] = "1";
            $PlayInfoVar['errortip'] = "文件不存在";
            $PlayInfo[] = $PlayInfoVar;
            $this->smarty->assign("PlayInfo", $PlayInfo);
            exit;
        }

        $fileType = array('xls', 'csv', 'xlsx');
        $uploadfile = new \Webfile($_FILES['uploadFile'], $files_dir = '../static/file', $size = 2097152 * 10, $fileType);
        $up_file = $uploadfile->upload();

        $PlayInfo = array();
        if ($up_file && $uploadfile->updatastatus) {
            $ExeclName = array();
            $ExeclName['所属集团'] = "company_id";
            $ExeclName['合同名称'] = "contract_name";
            $ExeclName['最大创建校园数'] = "contract_maxschoolnum";
            $ExeclName['最大创建学员数'] = "contract_maxstudentnum";
            $ExeclName['起效时间'] = "contract_starttime";
            $ExeclName['失效时间'] = "contract_endtime";
            $ExeclName['销售合同金额'] = "contract_allprice";
            $ExeclName['销售合同优惠价金额'] = "contract_couponmoney";
            $ExeclName['销售合同实际价钱'] = "contract_paymentprice";
            $ExeclName['所属销售'] = "saleman_id";

            $WorkerList = execl_to_array($up_file, $ExeclName);
            unset($WorkerList[0]);

            if ($WorkerList) {
                foreach ($WorkerList as $WorkerrVar) {
                    if ($WorkerrVar['合同名称'] !== '' && $WorkerrVar['起效时间'] !== '' && $WorkerrVar['失效时间'] !== '') {
                        $workersList[] = $WorkerrVar;
                    } else {
                        $PlayInfoVar = array();
                        $PlayInfoVar['contract_name'] = $WorkerrVar['contract_name'];
                        $PlayInfoVar['error'] = "1";
                        $PlayInfoVar['errortip'] = "导入账号信息不完整";
                        $PlayInfo[] = $PlayInfoVar;
                    }
                }
            }
            if ($workersList) {
                foreach ($workersList as $workersVar) {
                    $PlayInfoVar = array();
                    $PlayInfoVar['company_id'] = $workersVar['company_id'];
                    $PlayInfoVar['contract_name'] = $workersVar['contract_name'];
                    $PlayInfoVar['contract_maxschoolnum'] = $workersVar['contract_maxschoolnum'];
                    $PlayInfoVar['contract_maxstudentnum'] = $workersVar['contract_maxstudentnum'];
                    $PlayInfoVar['contract_starttime'] = gmdate('Y-m-d', \PHPExcel_Shared_Date::ExcelToPHP($WorkerrVar['contract_starttime']));
                    $PlayInfoVar['contract_endtime'] = gmdate('Y-m-d', \PHPExcel_Shared_Date::ExcelToPHP($WorkerrVar['contract_endtime']));
                    $PlayInfoVar['contract_allprice'] = $workersVar['contract_allprice'];
                    $PlayInfoVar['contract_couponmoney'] = $workersVar['contract_couponmoney'];
                    $PlayInfoVar['contract_paymentprice'] = $workersVar['contract_paymentprice'];
                    $PlayInfoVar['saleman_id'] = $workersVar['saleman_id'];

                    if ($this->Show_css->getFieldOne("imc_sales_contract", "contract_id", "company_id = '{$workersVar['company_id']}'")) {
                        $PlayInfoVar = array();
                        $PlayInfoVar['company_id'] = $workersVar['company_id'];
                        $PlayInfoVar['error'] = "1";
                        $PlayInfoVar['errortip'] = "集团{$workersVar['company_id']}已存在";
                        $PlayInfo[] = $PlayInfoVar;
                        continue;
                    }



                    $data = array();
                    $data['company_id'] = $workersVar['company_id'];
                    $data['contract_name'] = $workersVar['contract_name'];
                    $data['contract_maxschoolnum'] = $workersVar['contract_maxschoolnum'];
                    $data['contract_maxstudentnum'] = $workersVar['contract_maxstudentnum'];
                    $data['contract_starttime'] = gmdate('Y-m-d', \PHPExcel_Shared_Date::ExcelToPHP($WorkerrVar['contract_starttime']));
                    $data['contract_endtime'] = gmdate('Y-m-d', \PHPExcel_Shared_Date::ExcelToPHP($WorkerrVar['contract_endtime']));
                    $data['contract_allprice'] = $workersVar['contract_allprice'];
                    $data['contract_couponmoney'] = $workersVar['contract_couponmoney'];
                    $data['contract_paymentprice'] = $workersVar['contract_paymentprice'];
                    $data['saleman_id'] = $workersVar['saleman_id'];
                    $data['contract_createtime'] = time();

                    if ($this->Show_css->insertData("imc_sales_contract", $data)) {
                        $PlayInfoVar['error'] = "0";
                        $PlayInfoVar['errortip'] = "导入成功";
                    } else {
                        $PlayInfoVar['error'] = "1";
                        $PlayInfoVar['errortip'] = "导入失败";
                    }
                    $PlayInfo[] = $PlayInfoVar;
                }
            }

        } else {
            $PlayInfoVar = array();
            $PlayInfoVar['company_id'] = "导入出错";
            $PlayInfoVar['contract_name'] = "导入出错";
            $PlayInfoVar['error'] = "1";
            $PlayInfoVar['errortip'] = "文件不存在";
            $PlayInfo[] = $PlayInfoVar;
        }

        $this->smarty->assign("PlayInfo", $PlayInfo);
    }

    function ImportContractView()
    {

    }

    function ImportCompaniesExcelView()
    {
        if ($_FILES['uploadFile']['name'] == '' || $_FILES['uploadFile']['error'] == '1') {
            $PlayInfoVar = array();
            $PlayInfoVar['companies_cnname'] = "导入出错";
            $PlayInfoVar['companies_cnname'] = "导入出错";
            $PlayInfoVar['error'] = "1";
            $PlayInfoVar['errortip'] = "文件不存在";
            $PlayInfo[] = $PlayInfoVar;
            $this->smarty->assign("PlayInfo", $PlayInfo);
            exit;
        }

        $fileType = array('xls', 'csv', 'xlsx');
        $uploadfile = new \Webfile($_FILES['uploadFile'], $files_dir = '../static/file', $size = 2097152 * 10, $fileType);
        $up_file = $uploadfile->upload();

        $PlayInfo = array();
        if ($up_file && $uploadfile->updatastatus) {
            $ExeclName = array();
            $ExeclName['所属集团'] = "company_id";
            $ExeclName['企业主体名称'] = "companies_cnname";

            $WorkerList = execl_to_array($up_file, $ExeclName);
            unset($WorkerList[0]);

            if ($WorkerList) {
                foreach ($WorkerList as $WorkerrVar) {
                    if ($WorkerrVar['所属集团'] !== '' && $WorkerrVar['企业主体名称'] !== '') {
                        $workersList[] = $WorkerrVar;
                    } else {
                        $PlayInfoVar = array();
                        $PlayInfoVar['companies_cnname'] = $WorkerrVar['companies_cnname'];
                        $PlayInfoVar['error'] = "1";
                        $PlayInfoVar['errortip'] = "导入账号信息不完整";
                        $PlayInfo[] = $PlayInfoVar;
                    }
                }
            }
            if ($workersList) {
                foreach ($workersList as $workersVar) {
                    $PlayInfoVar = array();
                    $PlayInfoVar['company_id'] = $workersVar['company_id'];
                    $PlayInfoVar['companies_cnname'] = $workersVar['companies_cnname'];

                    if ($this->Show_css->getFieldOne("gmc_code_companies", "companies_id", "companies_cnname = '{$workersVar['companies_cnname']}'")) {
                        $PlayInfoVar = array();
                        $PlayInfoVar['companies_cnname'] = $workersVar['companies_cnname'];
                        $PlayInfoVar['error'] = "1";
                        $PlayInfoVar['errortip'] = "集团{$workersVar['companies_cnname']}已存在";
                        $PlayInfo[] = $PlayInfoVar;
                        continue;
                    }



                    $data = array();
                    $data['company_id'] = $workersVar['company_id'];
                    $data['companies_cnname'] = $workersVar['companies_cnname'];
                    $data['companies_createtime'] = time();

                    if ($this->Show_css->insertData("gmc_code_companies", $data)) {
                        $PlayInfoVar['error'] = "0";
                        $PlayInfoVar['errortip'] = "导入成功";
                    } else {
                        $PlayInfoVar['error'] = "1";
                        $PlayInfoVar['errortip'] = "导入失败";
                    }
                    $PlayInfo[] = $PlayInfoVar;
                }
            }

        } else {
            $PlayInfoVar = array();
            $PlayInfoVar['company_id'] = "导入出错";
            $PlayInfoVar['companies_cnname'] = "导入出错";
            $PlayInfoVar['error'] = "1";
            $PlayInfoVar['errortip'] = "文件不存在";
            $PlayInfo[] = $PlayInfoVar;
        }

        $this->smarty->assign("PlayInfo", $PlayInfo);
    }

    function ImportCompaniesView()
    {

    }


    function __destruct()
    {
        $site_id = Input('get.site_id',0);

        if ($this->c =='Wait') {
            $this->smarty->assign("u", $this->router->getController());
            $this->smarty->assign("t", $this->router->getUrl());
            $this->smarty->assign("Viewhtm", $this->Viewhtm);
            $websites = $this->Show_css->getOne("cms_websites","site_id='{$site_id}'");
            $this->smarty->assign("websites",$websites);
            if($site_id){
                $this->display("websiteindex.htm");
            }else{
                $this->display("index.htm");
            }
        }
        exit;
    }

}