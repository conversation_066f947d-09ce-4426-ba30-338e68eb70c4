<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn 
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2016/12/13
 * Time: 21:06
 */
namespace Work\Controller\Manage;

class IndexController extends viewTpl{
    public $data;
    public $iuser;

    function __construct() {
        parent::__construct ();
        if(!$this->check_login()){
            $this->LoginView();
        }
        $this->smarty->assign("iuser", $this->UserLogin);

        if($this->UserLogin['user_type'] !== '1' or $this->UserLogin['user_limitsinc'] !== '1') {
            $websites = $this->Show_css->select("SELECT m.* FROM cms_websites_manage as m where m.user_id = '{$this->UserLogin['user_id']}' order by m.manage_id DESC");
            if (count($websites) <= 1) {
                $websitesOne = $this->Show_css->getOne("cms_websites_manage", "user_id = '{$this->UserLogin['user_id']}'");
                jslocal_spl("/Website/id-{$websitesOne['site_id']}");
                exit;
            }
        }

    }

    //主页
    function HomeView()
    {
        if($this->UserLogin['user_type'] == '1' and $this->UserLogin['user_limitsinc'] == '1'){
            $websiteList = $this->Show_css->select("SELECT w.* FROM cms_websites as w ORDER BY site_weight asc limit 0,12");
        }else{
            $websiteList = $this->Show_css->select("SELECT w.* FROM cms_websites as w LEFT JOIN cms_websites_manage as m ON m.site_id = w.site_id WHERE m.user_id = '{$this->UserLogin['user_id']}' ORDER BY w.site_weight asc limit 0,12");
        }

        $this->smarty->assign("websiteList", $websiteList);

        $weblogList = $this->Show_css->select("SELECT w.*,u.user_cnname,u.user_enname,u.user_imghead,m.module_name,s.site_title FROM cms_weblog as w LEFT JOIN cms_users as u ON u.user_id = w.user_id LEFT JOIN cms_module as m ON m.module_id = w.module_id LEFT JOIN cms_websites as s ON s.site_id = w.site_id ORDER BY w.weblog_time DESC limit 0,4");
        $this->smarty->assign("weblogList", $weblogList);

//        $total = array();
//        $webtotal = $this->Show_css->selectOne("SELECT count(w.site_id) as total FROM cms_websites as w");
//        $total['website'] = $webtotal['total'];
//        $usertotal = $this->Show_css->selectOne("SELECT count(s.user_id) as total FROM cms_users as s");
//        $total['user'] = $usertotal['total'];
//        $ordertotal = $this->Show_css->selectOne("SELECT count(w.order_id) as total FROM crts_work_order as w WHERE w.status = '0'");
//        $total['feedback'] = $ordertotal['total'];
//
//        $this->smarty->assign("alltotal",$total);

        $this->smarty->assign("Viewhtm", $this->router->getController().$this->router->getUrl().".htm");
        $this->PlayView();
    }
    //主模板
    function PlayView()
    {
        $this->display("index.htm");
        exit;
    }
}