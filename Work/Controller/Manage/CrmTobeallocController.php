<?php


namespace Work\Controller\Manage;


class CrmTobeallocController extends viewTpl{
    public $data;
    public $u;
    public $t;
    public $c;
    public $iuser;
    public $Viewhtm;

    //预加载处理类
    function __construct() {
        parent::__construct ();
        if(!$this->check_login()){
            $this->LoginView();
        }
        if($this->check_login()){
            $this->smarty->assign("iuser", $this->UserLogin);
            $this->iuser = $this->UserLogin;
        }
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
        $this->Viewhtm = $this->router->getController()."/".$this->router->getUrl().".htm";


        $moduleOne = $this->Show_css->getOne("cms_module","module_mark='{$this->u}'");
        $this->smarty->assign("moduleOne",$moduleOne);
    }

    //主页
    function HomeView()
    {
        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        if(!isset($request['p'])){
            $p = '1';
        }else{
            $p= $request['p'];
        }

        $datawhere = "c.channel_id = l.channel_id AND c.company_id = '8888' AND c.client_tracestatus <> '-2' AND c.client_id NOT IN ( SELECT e.client_id FROM crm_client_schoolenter AS e WHERE e.company_id = '8888' )";
        $site_id = Input('get.site_id',0);
        $pageurl = "/{$this->u}/{$this->t}?site_id={$site_id}";

        if(isset($request['keyword']) && $request['keyword'] !==''){
            $datawhere .= " and (c.client_cnname like '%{$request['keyword']}%' or c.client_mobile like '%{$request['keyword']}%')";
            $pageurl .="&keyword={$request['keyword']}";
            $datatype['keyword'] = $request['keyword'];
        }


        if(isset($request['isseles']) && $request['isseles']=='1'){
            $datawhere .= " and outthree_userid=''";
            $pageurl .="&isseles={$request['isseles']}";
        }

//        if(isset($request['coupons_paytype']) && $request['coupons_paytype']!==''){
//            $datawhere .= " and coupons_paytype={$request['coupons_paytype']}";
//            $pageurl .="&coupons_paytype={$request['coupons_paytype']}";
//            $datatype['coupons_paytype'] = $request['coupons_paytype'];
//        }

        $sql = "SELECT c.*,l.channel_medianame,l.channel_name FROM crm_client AS c,crm_code_channel as l WHERE {$datawhere} ORDER BY c.client_id DESC";

        $db_nums = $Show_css->selectClear("SELECT COUNT(c.client_id) as countnum FROM crm_client as c ,crm_code_channel as l  where {$datawhere} ");
        $allnum = $db_nums[0]['countnum'];
        $datalist = $Show_css->dbwherePage($sql,$allnum,'10',$pageurl.'&p=',$p,'10','2');

        $smarty->assign("pagelist",$datalist['pages']);//筛选信息

        $smarty->assign("dataList",$datalist['cont']);
        $smarty->assign("datatype",$datatype);
    }

    function GmccrmclientView(){
        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        if(!isset($request['p'])){
            $p = '1';
        }else{
            $p= $request['p'];
        }

        $datawhere = "c.channel_id = l.channel_id AND c.company_id = '8888'";
        $site_id = Input('get.site_id',0);
        $pageurl = "/{$this->u}/{$this->t}?site_id={$site_id}";

        if(isset($request['keyword']) && $request['keyword'] !==''){
            $datawhere .= " and (c.client_cnname like '%{$request['keyword']}%' or c.client_mobile like '%{$request['keyword']}%')";
            $pageurl .="&keyword={$request['keyword']}";
            $datatype['keyword'] = $request['keyword'];
        }


        if(isset($request['isseles']) && $request['isseles']=='1'){
            $datawhere .= " and outthree_userid=''";
            $pageurl .="&isseles={$request['isseles']}";
        }

//        if(isset($request['coupons_paytype']) && $request['coupons_paytype']!==''){
//            $datawhere .= " and coupons_paytype={$request['coupons_paytype']}";
//            $pageurl .="&coupons_paytype={$request['coupons_paytype']}";
//            $datatype['coupons_paytype'] = $request['coupons_paytype'];
//        }

        $sql = "SELECT c.*,l.channel_medianame,l.channel_name,(SELECT o.school_cnname
FROM crm_client_schoolenter AS s, smc_school AS o
WHERE o.school_id = s.school_id
AND s.client_id = c.client_id ORDER BY s.schoolenter_id DESC LIMIT 0,1) as school_cnname FROM crm_client AS c,crm_code_channel as l WHERE {$datawhere} ORDER BY c.client_id DESC";

        $db_nums = $Show_css->selectClear("SELECT COUNT(c.client_id) as countnum FROM crm_client as c ,crm_code_channel as l  where {$datawhere} ");
        $allnum = $db_nums[0]['countnum'];
        $datalist = $Show_css->dbwherePage($sql,$allnum,'10',$pageurl.'&p=',$p,'10','2');

        $smarty->assign("pagelist",$datalist['pages']);//筛选信息

        $smarty->assign("dataList",$datalist['cont']);
        $smarty->assign("datatype",$datatype);
    }

    function ImexcelView(){
        $this->c="asdada";
        $datawhere = "c.channel_id = l.channel_id AND c.company_id = '8888' AND c.client_tracestatus <> '-2' and c.outthree_userid = '' AND c.client_isfromgmc = '1'";
        $sql = "SELECT c.client_id,c.client_patriarchname,c.client_cnname,c.client_enname,c.client_mobile,c.client_address,l.channel_medianame,l.channel_name 
        FROM crm_client AS c,crm_code_channel as l WHERE {$datawhere} ORDER BY c.client_id DESC limit 0,500";
        $clientList = $this->Show_css->selectClear($sql);
        if($clientList){
            foreach ($clientList as $clientOne){
                $salesforcelogOne = $this->Show_css->getFieldOne("crm_outthree","outthree_id","client_mobile='{$clientOne['client_mobile']}'");
                if($salesforcelogOne){
                    $datas = array();
                    $datas['outthree_userid'] = $salesforcelogOne['outthree_id'];
                    $this->Show_css->updateData("crm_client","client_id = '{$clientOne['client_id']}'",$datas);
                    echo "例子{$clientOne['client_mobile']}已完成例子更新<br />";
                }else{
                    $datas = array();
                    $datas['client_isfromgmc'] = '0';
                    $this->Show_css->updateData("crm_client","client_id = '{$clientOne['client_id']}'",$datas);
                    echo "完成例子{$clientOne['client_mobile']}已完成例子更新<br />";
                }
            }
        }else{
            echo "例子已结束";
        }
    }

    function ImexceldaiView(){
        exit;
    }

    function SaleforesAction(){
        exit;
    }

    //活动编辑
    function EditView()
    {
        $request = Input('get.','','trim,addslashes');
        $smarty = $this->smarty;

        $smarty->assign("act", "Edit");
        $sql = "SELECT c.client_id,c.client_patriarchname,c.client_cnname,c.client_enname,c.client_mobile,c.client_remark,client_createtime
,c.client_address,l.channel_medianame,l.channel_name
        FROM crm_client AS c,crm_code_channel as l WHERE c.channel_id = l.channel_id AND c.client_id='{$request['client_id']}' ORDER BY c.client_id DESC";
        $clientOne = $this->Show_css->selectOne($sql);
        $smarty->assign("dataVar", $clientOne);

        $trackList = $this->Show_css->selectClear("SELECT t.*
        FROM crm_client_track AS t WHERE t.client_id='{$request['client_id']}' ORDER BY t.track_id DESC");
        $smarty->assign("trackList", $trackList);

        $this->Viewhtm = $this->router->getController() . "/" . "Edit.htm";
    }

    //活动编辑
    function EditAction()
    {
        $request = Input('post.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $clientOne = $this->Show_css->getFieldOne("crm_client","client_id,company_id,client_createtime,channel_id","client_id='{$request['client_id']}'");

        $channelOne = $this->Show_css->getFieldOne("crm_code_channel","channel_id,channel_medianame"
            ,"channel_name='{$request['channel_editname']}' AND company_id = '{$clientOne['company_id']}'");
        if(!$channelOne){
            ajax_return(array('error' => 1, 'errortip' => "新渠道不存在!", "bakfuntion" => "errormotify"));
        }

        if($channelOne['channel_id'] == $clientOne['channel_id']){
            $data = array();
            $data['client_updatetime'] = time();
            $data['client_createtime'] = strtotime($request['client_createtime']);
            if ($Show_css->updateData("crm_client", "client_id = '{$request['client_id']}'", $data)) {
                ajax_return(array('error' => 0, 'errortip' => "提交成功!", "bakfuntion" => "successFromTip"));
            } else {
                ajax_return(array('error' => 1, 'errortip' => "提交失败!", "bakfuntion" => "dangerFromTip"));
            }
        }else{
            $data = array();
            $data['channel_id'] = $channelOne['channel_id'];
            $data['client_source'] = $channelOne['channel_medianame'];
            $data['client_updatetime'] = time();
            $data['client_createtime'] = strtotime($request['client_createtime']);
            if ($Show_css->updateData("crm_client", "client_id = '{$request['client_id']}'", $data)) {
                $channello = array();
                $channello['company_id'] = $clientOne['company_id'];
                $channello['client_id'] = $clientOne['client_id'];
                $channello['from_channel_id'] = $clientOne['channel_id'];
                $channello['to_channel_id'] = $channelOne['channel_id'];
                $channello['channellog_note'] = $request['channellog_note']."渠道变更时间：{$data['client_createtime']}";
                $channello['channellog_createtime'] = time();
                $Show_css->insertData("crm_client_channellog", $channello);
                ajax_return(array('error' => 0, 'errortip' => "提交成功!", "bakfuntion" => "successFromTip"));
            } else {
                ajax_return(array('error' => 1, 'errortip' => "提交失败!", "bakfuntion" => "dangerFromTip"));
            }
        }
    }

    //魔术方法
    public function __call($name, $arguments) {
        $this->smarty->assign("errorTip", "Calling object method '$name' ". implode(', ', $arguments). "\n");
        $this->Viewhtm = "under.htm";
    }
    //魔术函数
    function __destruct()
    {
        $site_id = Input('get.site_id',0);

        if ($this->c =='Wait') {
            $this->smarty->assign("u", $this->router->getController());
            $this->smarty->assign("t", $this->router->getUrl());
            $this->smarty->assign("Viewhtm", $this->Viewhtm);
            $websites = $this->Show_css->getOne("cms_websites","site_id='{$site_id}'");
            $this->smarty->assign("websites",$websites);
            if($site_id){
                if($this->UserLogin['user_type'] == '1' and $this->UserLogin['user_limitsinc'] == '1'){
                    $moduleList = $this->Show_css->select("SELECT m.* FROM cms_module as m LEFT JOIN cms_moduleapply as a ON a.module_id = m.module_id WHERE a.site_id = '{$site_id}' ORDER BY m.module_weight ASC ");
                }else{
                    $moduleList = $this->Show_css->select("SELECT m.* FROM cms_module as m LEFT JOIN cms_moduleapply as a ON a.module_id = m.module_id LEFT JOIN cms_websites_func as c ON c.site_id = a.site_id and c.module_id = a.module_id WHERE a.site_id = '{$site_id}' and c.user_id = '{$this->UserLogin['user_id']}' ORDER BY m.module_weight ASC ");
                }
                // print_r($moduleList);die;
                $this->smarty->assign("moduleList", $moduleList);
                $this->display("websiteindex.htm");
            }else{
                $this->display("index.htm");
            }
        }
        exit;
    }
}