<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn 
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2016/12/16
 * Time: 11:51
 */

namespace Work\Controller\Manage;


class MediaplaceController  extends viewTpl{
    public $data;
    public $u;
    public $t;
    public $c;
    public $iuser;
    public $Viewhtm;

    //预加载处理类
    function __construct() {
        parent::__construct ();
        if(!$this->check_login()){
            $this->LoginView();
        }
        if($this->check_login()){
            $this->smarty->assign("iuser", $this->UserLogin);
            $this->iuser = $this->UserLogin;
        }
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
        $this->Viewhtm = $this->router->getController()."/".$this->router->getUrl().".htm";


        $moduleOne = $this->Show_css->getOne("cms_module","module_mark='{$this->u}'");
        $this->smarty->assign("moduleOne",$moduleOne);

    }
    //主页
    function HomeView()
    {
        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        if(!isset($request['p'])){
            $p = '1';
        }else{
            $p= $request['p'];
        }

        $datawhere = "1";
        $pageurl = "/{$this->u}/{$this->t}?";
        if ($request['site_id']) {
            $pageurl .= "&site_id={$request['site_id']}";
        }

        $datatype = array();
        if(isset($request['keyword']) && $request['keyword'] !==''){
            $datawhere .= " and (m.place_name like '%{$request['keyword']}%')";
            $pageurl .="&keyword={$request['keyword']}";
            $datatype['keyword'] = $request['keyword'];
        }

        if(isset($request['site_id'])){
            $datawhere .= " and m.site_id = '{$request['site_id']}'";
            $pageurl .="&site_id={$request['site_id']}";
            $datatype['site_id'] = $request['site_id'];
        }

        $sql = "SELECT m.*,s.site_title FROM cms_mediaplace as m LEFT JOIN cms_websites as s ON s.site_id = m.site_id where {$datawhere} order by m.place_id DESC";

        $db_nums = $Show_css->select("SELECT COUNT(*) FROM cms_mediaplace as m LEFT JOIN cms_websites as s ON s.site_id = m.site_id where {$datawhere}");//相关条件下的总记录数COUNT(*)
        $allnum = $db_nums[0][0];

        $datalist = $Show_css->dbwherePage($sql,$allnum,'10',$pageurl.'&p=',$p,'10','2');

        $smarty->assign("pagelist",$datalist['pages']);//筛选信息

        $smarty->assign("dataList",$datalist['cont']);
        $smarty->assign("datatype",$datatype);
    }

    //增加系统功能
    function AddView()
    {
        $this->smarty->assign("act","Add");

        $websiteList = $this->Show_css->select("SELECT w.* FROM cms_websites as w WHERE w.site_type = '0' ORDER BY site_weight asc");
        $this->smarty->assign("siteList",$websiteList);

        $this->Viewhtm = $this->router->getController()."/"."Manage.htm";
    }
    //提交处理机制
    function AddAction()
    {
        $request = Input('post.','','trim,addslashes');
        $data = array();

        if($request['place_name'] == '' || $request['site_id'] == ''){
            ajax_return(array('error' => 1,'errortip' => "名称及站点归属必须设置!","bakfuntion"=>"warningFromTip"));
        }

        $data['place_name'] = $request['place_name'];
        $data['site_id'] = $request['site_id'];
        $data['place_type'] = $request['place_type'];
        $data['place_width'] = $request['place_width'];
        $data['place_height'] = $request['place_height'];
        if($this->Show_css->insertData("cms_mediaplace",$data)){
            ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"successFromTip","bakurl"=>"/Mediaplace"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "新增失败!","bakfuntion"=>"dangerFromTip"));
        }
    }

    //增加系统功能
    function EditView()
    {

        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        $websiteList = $this->Show_css->select("SELECT w.* FROM cms_websites as w WHERE w.site_type = '0' ORDER BY site_weight asc");
        $this->smarty->assign("siteList",$websiteList);
        $mediaplaceOne = $Show_css->getOne("cms_mediaplace", "place_id='{$request['id']}'");
        $smarty->assign("dataVar", $mediaplaceOne);

        $smarty->assign("act", "Edit");

        $this->Viewhtm = $this->router->getController()."/"."Manage.htm";
    }
    //提交处理机制
    function EditAction()
    {
        $request = Input('post.','','trim,addslashes');
        if($request['place_name'] == '' || $request['site_id'] == ''){
            ajax_return(array('error' => 1,'errortip' => "名称及站点归属必须设置!","bakfuntion"=>"warningFromTip"));
        }

        $data = array();
        $data['place_name'] = $request['place_name'];
        $data['site_id'] = $request['site_id'];
        $data['place_type'] = $request['place_type'];
        $data['place_width'] = $request['place_width'];
        $data['place_height'] = $request['place_height'];
        if($this->Show_css->updateData("cms_mediaplace","place_id = '{$request['place_id']}'",$data)){
            ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"okmotify","bakurl"=>"/{$this->u}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "新增失败!","bakfuntion"=>"errormotify"));
        }
    }
    //提交处理机制
    function DelAction()
    {
        $place_id = Input('get.id',0);

        if($this->Show_css->delData('cms_mediaplace',"place_id='{$place_id}'")){
            ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"okmotify","bakurl"=>"/{$this->u}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "新增失败!","bakfuntion"=>"errormotify"));
        }
    }


    //魔术方法
    public function __call($name, $arguments) {
        $this->smarty->assign("errorTip", "Calling object method '$name' ". implode(', ', $arguments). "\n");
        $this->Viewhtm = "under.htm";
    }
    //魔术函数
    function __destruct()
    {
        $site_id = Input('get.site_id',0);

        if ($this->c =='Wait') {
            $this->smarty->assign("u", $this->router->getController());
            $this->smarty->assign("t", $this->router->getUrl());
            $this->smarty->assign("Viewhtm", $this->Viewhtm);
            $websites = $this->Show_css->getOne("cms_websites","site_id='{$site_id}'");
            $this->smarty->assign("websites",$websites);
            if($site_id){
                if($this->UserLogin['user_type'] == '1' and $this->UserLogin['user_limitsinc'] == '1'){
                    $moduleList = $this->Show_css->select("SELECT m.* FROM cms_module as m LEFT JOIN cms_moduleapply as a ON a.module_id = m.module_id WHERE a.site_id = '{$site_id}' ORDER BY m.module_weight ASC ");
                }else{
                    $moduleList = $this->Show_css->select("SELECT m.* FROM cms_module as m LEFT JOIN cms_moduleapply as a ON a.module_id = m.module_id LEFT JOIN cms_websites_func as c ON c.site_id = a.site_id and c.module_id = a.module_id WHERE a.site_id = '{$site_id}' and c.user_id = '{$this->UserLogin['user_id']}' ORDER BY m.module_weight ASC ");
                }
                // print_r($moduleList);die;
                $this->smarty->assign("moduleList", $moduleList);
                $this->display("websiteindex.htm");
            }else{
                $this->display("index.htm");
            }
        }
        exit;
    }

}