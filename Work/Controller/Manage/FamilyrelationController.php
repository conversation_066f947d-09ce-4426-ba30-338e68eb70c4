<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2016/12/19
 * Time: 14:21
 */

namespace Work\Controller\Manage;


class FamilyrelationController extends viewTpl{
	public $data;
	public $iuser;
	public $u;
	public $t;
	public $c;
	public $Viewhtm;
	
	function __construct() {
		parent::__construct ();
		if(!$this->check_login()){
			$this->LoginView();
		}
		$this->smarty->assign("iuser", $this->UserLogin);
		$this->u = $this->router->getController();
		$this->t = $this->router->getUrl();
		$this->c = $this->router->getAction();
		$this->Viewhtm = $this->router->getController()."/".$this->router->getUrl().".htm";
		
		$moduleOne = $this->Show_css->getOne("cms_module","module_mark='{$this->u}'");
		$this->smarty->assign("moduleOne",$moduleOne);
		$this->Module = $moduleOne;
	}
	//主页
	function HomeView()
	{
		$request = Input('get.','','trim,addslashes');
		$smarty = $this->smarty;
		
		if ($request['site_id']) {
			
			$datatype['site_id'] = $request['site_id'];
		}
		
		$datatype = array();
		if(isset($request['keyword']) && $request['keyword'] !==''){
			$datatype['keyword'] = $request['keyword'];
		}
		
		$datalist = $this->Show_css->selectClear("select  familyrelation_id, familyrelation_code,familyrelation_name from  crm_code_familyrelation  limit 0,100");
		$smarty->assign("datalist",$datalist);
		$smarty->assign("datatype",$datatype);
	}

//	编辑操作
	function EditView()
	{
		
		
		$familyrelation_id = Input('get.id');
		$smarty = $this->smarty;
		$relationOne = $this->Show_css->getOne('crm_code_familyrelation',"familyrelation_id ={$familyrelation_id}");
		$smarty->assign("act","Edit");
		$smarty->assign("relationOne",$relationOne);
		
	}
	
	function  EditAction()
	{
		
		$request = Input('post.','','trim,addslashes');
		
		$data['familyrelation_code'] = $request['familyrelation_code'];
		$data['familyrelation_name'] = $request['familyrelation_name'];
		if ($this->Show_css->updateData('crm_code_familyrelation',"familyrelation_id ={$request['familyrelation_id']}",$data))
		{
			ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"successFromTip","bakurl"=>"/{$this->u}"));
		}else{
			ajax_return(array('error' => 0,'errortip' => "编辑失败!","bakfuntion"=>"dangerFromTip"));
		}
		
	}

//	增加操作
	function AddView()
	{
		
		$smarty = $this->smarty;
		$smarty->assign("act","Add");
		
		$this->Viewhtm = $this->router->getController()."/"."Edit.htm";
	}
	
	function  AddAction()
	{
		
		$request = Input('post.','','trim,addslashes');
		
		$data['familyrelation_code'] = $request['familyrelation_code'];
		$data['familyrelation_name'] = $request['familyrelation_name'];
		if ($this->Show_css->InsertData('crm_code_familyrelation',$data))
		{
			ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"successFromTip","bakurl"=>"/{$this->u}"));
		}else{
			ajax_return(array('error' => 0,'errortip' => "编辑失败!","bakfuntion"=>"dangerFromTip"));
		}
		
	}
	
	
	//提交处理机制
	function DelAction()
	{
		$familyrelation_id = Input('get.id','0');
		
		if($this->Show_css->delData('crm_code_familyrelation',"familyrelation_id='{$familyrelation_id}'")){
			ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"okmotify","bakurl"=>"/{$this->u}"));
		}else{
			ajax_return(array('error' => 1,'errortip' => "提交失败!","bakfuntion"=>"errormotify"));
		}
	}
	
	
	
	//下方批量操作
	public function setAction(){
		$request = Input('post.','','trim,addslashes');
		
		if($request['ActionType']==1){
			//转移
			if($request['cpat_class']=='' &&$request['cpat_class']==''){
				ajax_return(array('error' => 1,'errortip' => "请先选择分类!","bakfuntion"=>"errormotify"));
			}
			foreach($request['tab_list'] as $tabVar){
				$this->Show_css->updateData('cms_article',"cpat_id={$tabVar}",array('cpat_class'=>$request['cpat_class']));
			}
			ajax_return(array('error' => 0,'errortip' => "转移成功!","bakfuntion"=>"okmotify"));
		}elseif($request['ActionType']==2){
			//复制
			if($request['cpat_class']=='' &&$request['cpat_class']==''){
				ajax_return(array('error' => 1,'errortip' => "请先选择分类!","bakfuntion"=>"errormotify"));
			}
			foreach($request['tab_list'] as $tabVar){
				$articleOne = $this->Show_css->selectClear("select * from cms_article where cpat_id={$tabVar}");
				$articleOne = $articleOne[0];
				$articleOne['cpat_class'] = $request['cpat_class'];
				unset($articleOne['cpat_id']);
				$this->Show_css->insertData('cms_article',$articleOne);
			}
			ajax_return(array('error' => 0,'errortip' => "复制成功!","bakfuntion"=>"okmotify"));
		}elseif($request['ActionType']==3){
			//推荐
			foreach($request['tab_list'] as $tabVar){
				$this->Show_css->updateData('cms_article',"cpat_id={$tabVar}",array('cpat_recommendinc'=>1));
			}
			ajax_return(array('error' => 0,'errortip' => "推荐成功!","bakfuntion"=>"okmotify"));
		}elseif($request['ActionType']==4){
			//推荐
			foreach($request['tab_list'] as $tabVar){
				$this->Show_css->updateData('cms_article',"cpat_id={$tabVar}",array('cpat_topinc'=>1));
			}
			ajax_return(array('error' => 0,'errortip' => "置顶成功!","bakfuntion"=>"okmotify"));
		}elseif($request['ActionType']==5){
			//推荐
			foreach($request['tab_list'] as $tabVar){
				$this->Show_css->updateData('cms_article',"cpat_id={$tabVar}",array('cpat_newsinc'=>1));
			}
			ajax_return(array('error' => 0,'errortip' => "设为最新成功!","bakfuntion"=>"okmotify"));
		}elseif($request['ActionType']==6){
			//删除
			foreach($request['tab_list'] as $tabVar){
				$this->Show_css->delData('cms_article',"cpat_id='{$tabVar}'");
			}
			ajax_return(array('error' => 0,'errortip' => "批量删除成功!","bakfuntion"=>"okmotify"));
		}
	}
	//魔术方法
	public function __call($name, $arguments) {
		$this->smarty->assign("errorTip", "Calling object method '$name' ". implode(', ', $arguments). "\n");
		
		$this->Viewhtm = "under.htm";
	}
	//魔术方法
	function __destruct()
	{
		$site_id = Input('get.site_id',0);
		
		if ($this->c =='Wait') {
			$this->smarty->assign("u", $this->router->getController());
			$this->smarty->assign("t", $this->router->getUrl());
			$this->smarty->assign("Viewhtm", $this->Viewhtm);
			if($site_id){
				$websites = $this->Show_css->getOne("cms_websites","site_id='{$site_id}'");
				$this->smarty->assign("websites",$websites);
				
				if($this->UserLogin['user_type'] == '1' and $this->UserLogin['user_limitsinc'] == '1'){
					$moduleList = $this->Show_css->select("SELECT m.* FROM cms_module as m LEFT JOIN cms_moduleapply as a ON a.module_id = m.module_id WHERE a.site_id = '{$site_id}' ORDER BY m.module_weight ASC ");
				}else{
					$moduleList = $this->Show_css->select("SELECT m.* FROM cms_module as m LEFT JOIN cms_moduleapply as a ON a.module_id = m.module_id LEFT JOIN cms_websites_func as c ON c.site_id = a.site_id and c.module_id = a.module_id WHERE a.site_id = '{$site_id}' and c.user_id = '{$this->UserLogin['user_id']}' ORDER BY m.module_weight ASC ");
				}
				$this->smarty->assign("moduleList", $moduleList);
				$this->display("websiteindex.htm");
			}else{
				$this->display("index.htm");
			}
		}
		exit;
	}
	
}
