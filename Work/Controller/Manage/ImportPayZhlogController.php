<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2019/5/22 0022
 * Time: 下午 3:21
 */

namespace Work\Controller\Manage;


class ImportPayZhlogController extends viewTpl{
    public $data;
    public $u;
    public $t;
    public $c;
    public $iuser;
    public $Viewhtm;

    //预加载处理类
    function __construct() {
        parent::__construct ();
        if(!$this->check_login()){
            $this->LoginView();
        }
        if($this->check_login()){
            $this->smarty->assign("iuser", $this->UserLogin);
            $this->iuser = $this->UserLogin;
        }
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
        $this->Viewhtm = $this->router->getController()."/".$this->router->getUrl().".htm";

        $moduleOne = $this->Show_css->getOne("cms_module","module_mark='{$this->u}'");
        $this->smarty->assign("moduleOne",$moduleOne);
    }

    //主页
    function HomeView()
    {
        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        if(!isset($request['p'])){
            $p = '1';
        }else{
            $p= $request['p'];
        }

        $datawhere = " 1 and zhlog_cmbtype = '2' ";//
        $pageurl = "/{$this->u}/{$this->t}?";
        if ($request['site_id']) {
            $pageurl .= "&site_id={$request['site_id']}";
        }

        $datatype = array();
        if(isset($request['keyword']) && $request['keyword'] !==''){
            $datawhere .= " and (zhlog_cmbappid like '%{$request['keyword']}%' or zhlog_paypid like '%{$request['keyword']}%')";
            $pageurl .="&keyword={$request['keyword']}";
            $datatype['keyword'] = $request['keyword'];
        }

        $sql = "SELECT * FROM smc_payfee_order_pay_zhlog where {$datawhere} ORDER BY zhlog_id DESC";

        $db_nums = $Show_css->selectOne("SELECT COUNT(*) as num FROM smc_payfee_order_pay_zhlog where {$datawhere}");//相关条件下的总记录数COUNT(*)

        ;
        $allnum = $db_nums['num'];

        $datalist = $Show_css->dbwherePage($sql,$allnum,'10',$pageurl.'&p=',$p,'10','2');

        $smarty->assign("pagelist",$datalist['pages']);//筛选信息

        $smarty->assign("dataList",$datalist['cont']);
        $smarty->assign("datatype",$datatype);
    }
    //导入页面
    function ImportZhlogView(){

    }
    //导入操作
    function ImportZhlogExcelView()
    {
        if ($_FILES['uploadFile']['name'] == '' || $_FILES['uploadFile']['error'] == '1') {
            $PlayInfoVar = array();
            $PlayInfoVar['company_code'] = "导入出错";
            $PlayInfoVar['company_cnname'] = "导入出错";
            $PlayInfoVar['error'] = "1";
            $PlayInfoVar['errortip'] = "文件不存在";
            $PlayInfo[] = $PlayInfoVar;
            $this->smarty->assign("PlayInfo", $PlayInfo);
            exit;
        }

        $fileType = array('xls', 'csv', 'xlsx');
        $uploadfile = new \Webfile($_FILES['uploadFile'], $files_dir = '../static/file', $size = 2097152 * 10, $fileType);
        $up_file = $uploadfile->upload();

        $PlayInfo = array();
        if ($up_file && $uploadfile->updatastatus) {//商户号、商户订单号、支付方式、交易金额、手续费、费率、交易日期、 交易时间
            $ExeclName = array();
            $ExeclName['商户号'] = "zhlog_cmbappid";
            $ExeclName['商户订单号'] = "zhlog_paypid";
            $ExeclName['支付方式'] = "zhlog_pay_typename";
            $ExeclName['交易金额'] = "zhlog_pay_price";
            $ExeclName['手续费'] = "zhlog_ifee";
            $ExeclName['费率'] = "zhlog_rate";
            $ExeclName['交易日期'] = "zhlog_pay_date";
            $ExeclName['交易时间'] = "zhlog_pay_time";

            $WorkerList = execl_to_array($up_file, $ExeclName);
            unset($WorkerList[0]);

            if ($WorkerList) {
                foreach ($WorkerList as $WorkerrVar) {
                    if ($WorkerrVar['商户号'] !== '' && $WorkerrVar['商户订单号'] !== '' && $WorkerrVar['手续费'] !== '') {
                        $datasList[] = $WorkerrVar;
                    } else {
                        $PlayInfoVar = array();
                        $PlayInfoVar['company_cnname'] = $WorkerrVar['company_cnname'];
                        $PlayInfoVar['error'] = "1";
                        $PlayInfoVar['errortip'] = "导入账号信息不完整";
                        $PlayInfo[] = $PlayInfoVar;
                    }
                }
            }
            if ($datasList) {
                foreach ($datasList as $datasVar) {
                    $PlayInfoVar = array();
                    $PlayInfoVar['zhlog_cmbappid'] = $datasVar['zhlog_cmbappid'];
                    $PlayInfoVar['zhlog_paypid'] = $datasVar['zhlog_paypid'];
                    if ($this->Show_css->getFieldOne("smc_payfee_order_pay_zhlog", "zhlog_id", "zhlog_cmbappid = '{$datasVar['zhlog_cmbappid']}' and zhlog_paypid = '{$datasVar['zhlog_paypid']}' ")) {
                        $PlayInfoVar['error'] = "1";
                        $PlayInfoVar['errortip'] = "此订单【{$datasVar['zhlog_paypid']}】已存在";
                        $PlayInfo[] = $PlayInfoVar;
                        continue;
                    }

                    $data = array();
                    $data['zhlog_cmbappid'] = $datasVar['zhlog_cmbappid'];
                    $data['zhlog_cmbtype'] = 2;
                    $data['zhlog_paypid'] = $datasVar['zhlog_paypid'];
                    $data['zhlog_ifee'] = $datasVar['zhlog_ifee'];
                    $data['zhlog_rate'] = $datasVar['zhlog_rate'];
                    $data['zhlog_pay_typename'] = $datasVar['zhlog_pay_typename'];
                    $data['zhlog_pay_date'] = $datasVar['zhlog_pay_date'];
                    $data['zhlog_pay_time'] = $datasVar['zhlog_pay_time'];
                    $data['zhlog_pay_price'] = $datasVar['zhlog_pay_price'];
                    $data['zhlog_addtime'] = time();

                    if ($id = $this->Show_css->insertData("smc_payfee_order_pay_zhlog", $data)) {
                        $PlayInfoVar['error'] = "0";
                        $PlayInfoVar['errortip'] = "导入成功";
                    } else {
                        $PlayInfoVar['error'] = "1";
                        $PlayInfoVar['errortip'] = "导入失败";
                    }
                    $PlayInfo[] = $PlayInfoVar;
                }
            }
        } else {
            $PlayInfoVar = array();
            $PlayInfoVar['zhlog_cmbappid'] = "导入出错";
            $PlayInfoVar['zhlog_paypid'] = "导入出错";
            $PlayInfoVar['error'] = "1";
            $PlayInfoVar['errortip'] = "文件不存在";
            $PlayInfo[] = $PlayInfoVar;
        }

        $this->smarty->assign("PlayInfo", $PlayInfo);
    }


    //魔术方法
    public function __call($name, $arguments) {
        $this->smarty->assign("errorTip", "Calling object method '$name' ". implode(', ', $arguments). "\n");
        $this->Viewhtm = "under.htm";
    }
    //魔术函数
    function __destruct()
    {
        $site_id = Input('get.site_id',0);

        if ($this->c =='Wait') {
            $this->smarty->assign("u", $this->router->getController());
            $this->smarty->assign("t", $this->router->getUrl());
            $this->smarty->assign("Viewhtm", $this->Viewhtm);
            $websites = $this->Show_css->getOne("cms_websites","site_id='{$site_id}'");
            $this->smarty->assign("websites",$websites);
            if($site_id){
                if($this->UserLogin['user_type'] == '1' and $this->UserLogin['user_limitsinc'] == '1'){
                    $moduleList = $this->Show_css->select("SELECT m.* FROM cms_module as m LEFT JOIN cms_moduleapply as a ON a.module_id = m.module_id WHERE a.site_id = '{$site_id}' ORDER BY m.module_weight ASC ");
                }else{
                    $moduleList = $this->Show_css->select("SELECT m.* FROM cms_module as m LEFT JOIN cms_moduleapply as a ON a.module_id = m.module_id LEFT JOIN cms_websites_func as c ON c.site_id = a.site_id and c.module_id = a.module_id WHERE a.site_id = '{$site_id}' and c.user_id = '{$this->UserLogin['user_id']}' ORDER BY m.module_weight ASC ");
                }
                // print_r($moduleList);die;
                $this->smarty->assign("moduleList", $moduleList);
                $this->display("websiteindex.htm");
            }else{
                $this->display("index.htm");
            }
        }
        exit;
    }

}
