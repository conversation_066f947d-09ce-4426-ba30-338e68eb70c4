<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2021/3/5
 * Time: 18:21
 */

namespace Work\Controller\Manage;


class StafferPostbeController extends viewTpl{
    public $data;
    public $u;
    public $t;
    public $c;
    public $iuser;
    public $Viewhtm;

    //预加载处理类
    function __construct() {
        parent::__construct ();
        if(!$this->check_login()){
            $this->LoginView();
        }
        if($this->check_login()){
            $this->smarty->assign("iuser", $this->UserLogin);
            $this->iuser = $this->UserLogin;
        }
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
        $this->Viewhtm = $this->router->getController()."/".$this->router->getUrl().".htm";


        $moduleOne = $this->Show_css->getOne("cms_module","module_mark='{$this->u}'");
        $this->smarty->assign("moduleOne",$moduleOne);

    }

    //职工管理
    function HomeView(){
        $request = Input('get.','','trim,addslashes');

        $datatype = array();
        $datatype['company_id'] = $request['company_id'];
        $datatype['school_id'] = $request['school_id'];
        $datatype['from'] = $request['from'];
        $datawhere = "s.company_id = '8888' AND s.staffer_isparttime = '0' AND s.staffer_leave = '0' AND s.staffer_employeepid <> ''";
        $pageurl = "/{$this->u}/{$this->t}?site_id={$request['site_id']}";
        if(isset($request['keyword']) && $request['keyword'] !==''){
            $datawhere .= " and (s.staffer_cnname like '%{$request['keyword']}%' or s.staffer_employeepid like '%{$request['keyword']}%' or s.staffer_branch like '%{$request['keyword']}%' or s.staffer_mobile like '%{$request['keyword']}%' )";
            $pageurl .="&keyword={$request['keyword']}";
            $datatype['keyword'] = $request['keyword'];
        }

        if(!isset($request['p'])){
            $p = '1';
        }else{
            $p= $request['p'];
        }

        $Show_css = $this->Show_css;

        $sql =" SELECT s.staffer_id,
	s.staffer_cnname,
	s.staffer_branch,s.staffer_mobile,
	s.staffer_employeepid,
	(
		SELECT
			GROUP_CONCAT(s.school_cnname)
		FROM
			gmc_staffer_postbe AS p,
			smc_school AS s
		WHERE
			p.staffer_id = s.staffer_id
		AND p.school_id = s.school_id
		AND p.school_id <> '0'
		AND p.postbe_ismianjob = '1'
	) AS school_cnname,
	(
		SELECT
			COUNT(p.postbe_id)
		FROM
			gmc_staffer_postbe AS p
		WHERE
			p.staffer_id = s.staffer_id AND p.school_id <> '0'
	) pnums
FROM smc_staffer AS s WHERE {$datawhere}";


        $db_nums = $Show_css->selectOne("SELECT COUNT(s.staffer_id) as countnums FROM smc_staffer AS s WHERE {$datawhere}");
        $allnum = $db_nums['countnums'];

        $studentList = $Show_css->dbwherePage($sql,$allnum,'10',$pageurl.'&p=',$p,'10','2');
        $dataList = $studentList['cont'];
        $this->smarty->assign("dataList",$dataList);
        $this->smarty->assign("pagelist",$studentList['pages']);//筛选信息
        $this->smarty->assign("datatype",$datatype);//筛选信息
    }

    function BrowseView(){
        $request = Input('get.','','trim,addslashes');
        $stafferOne = $this->Show_css->getOne("smc_staffer", "staffer_id='{$request['staffer_id']}' and company_id = '8888' ");
        $this->smarty->assign("stafferOne",$stafferOne);//筛选信息

        $stafferpostbeList = $this->Show_css->selectClear("SELECT
    b.postbe_id,
	p.post_name,
	(
		SELECT
			a.postpart_name
		FROM
			smc_school_postpart AS a
		WHERE
			a.postpart_id = b.postpart_id
	) AS postpart_name,
	b.postbe_ismianjob,
	s.school_branch,
	s.school_cnname
FROM
	gmc_staffer_postbe AS b,
	gmc_company_post AS p,
	smc_school AS s
WHERE
	b.post_id = p.post_id
AND b.school_id = s.school_id
AND s.company_id = '8888'
AND b.staffer_id = '{$stafferOne['staffer_id']}'");
        $this->smarty->assign("stafferpostbeList",$stafferpostbeList);//筛选信息

        $paramto = array();
        $paramto['employeepid'] = $stafferOne['staffer_employeepid'];
        $codeApiSting = request_by_curl("https://webmanage.kidcastle.com.cn/api/getSchoolpost", dataEncode($paramto), "GET", array());
        $bingApiArray = json_decode($codeApiSting, "1");
        $this->smarty->assign("hrpostbeList",$bingApiArray['result']['postlist']);//筛选信息
    }


    //表单字段 -- 某些状态修改
    function UnderIsleaveAction(){
        $request = Input('get.','','strip_tags,addslashes');
        $dataOne = $this->Show_css->getOne("smc_staffer","staffer_id='{$request['staffer_id']}'");
        $data = array();
        if($dataOne['staffer_leave'] == '1'){
            $data['staffer_leave'] = "0";
        }else{
            $data['staffer_leave'] = "1";
        }
        $data['staffer_updatetime'] = time();
        if($this->Show_css->updateData("smc_staffer","staffer_id = '{$dataOne['staffer_id']}'",$data)){
            $this->Recordweblog($request['site_id'],$this->Module['module_id'],$this->c,"修改在职状态,ID:".$dataOne['staffer_id']);
            if($data['staffer_leave'] == '1'){
                ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"okmotify","state"=>"1"));
            }else{
                ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"okmotify","state"=>"0"));
            }
        }else{
            ajax_return(array('error' => 1,'errortip' => "新增失败!","bakfuntion"=>"errormotify"));
        }
    }

    //表单字段 -- 某些状态修改
    function UnderIsmianjobAction(){
        $request = Input('get.','','strip_tags,addslashes');
        $dataOne = $this->Show_css->getOne("gmc_staffer_postbe","postbe_id='{$request['postbe_id']}'");
        $data = array();
        if($dataOne['postbe_ismianjob'] == '1'){
            $data['postbe_ismianjob'] = "0";
        }else{
            $data['postbe_ismianjob'] = "1";
        }
        if($this->Show_css->updateData("gmc_staffer_postbe","postbe_id = '{$dataOne['postbe_id']}'",$data)){
            $this->Recordweblog($request['site_id'],$this->Module['module_id'],$this->c,"修改任职状态,ID:".$dataOne['postbe_id']);
            if($data['postbe_ismianjob'] == '1'){
                ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"okmotify","state"=>"1"));
            }else{
                ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"okmotify","state"=>"0"));
            }
        }else{
            ajax_return(array('error' => 1,'errortip' => "新增失败!","bakfuntion"=>"errormotify"));
        }
    }

    //魔术方法
    public function __call($name, $arguments) {
        $this->smarty->assign("errorTip", "Calling object method '$name' ". implode(', ', $arguments). "\n");
        $this->Viewhtm = "under.htm";
    }
    //魔术函数
    function __destruct()
    {
        $site_id = Input('get.site_id',0);

        if ($this->c =='Wait') {
            $this->smarty->assign("u", $this->router->getController());
            $this->smarty->assign("t", $this->router->getUrl());
            $this->smarty->assign("Viewhtm", $this->Viewhtm);
            $websites = $this->Show_css->getOne("cms_websites","site_id='{$site_id}'");
            $this->smarty->assign("websites",$websites);
            if($site_id){
                if($this->UserLogin['user_type'] == '1' and $this->UserLogin['user_limitsinc'] == '1'){
                    $moduleList = $this->Show_css->select("SELECT m.* FROM cms_module as m LEFT JOIN cms_moduleapply as a ON a.module_id = m.module_id WHERE a.site_id = '{$site_id}' ORDER BY m.module_weight ASC ");
                }else{
                    $moduleList = $this->Show_css->select("SELECT m.* FROM cms_module as m LEFT JOIN cms_moduleapply as a ON a.module_id = m.module_id LEFT JOIN cms_websites_func as c ON c.site_id = a.site_id and c.module_id = a.module_id WHERE a.site_id = '{$site_id}' and c.user_id = '{$this->UserLogin['user_id']}' ORDER BY m.module_weight ASC ");
                }
                // print_r($moduleList);die;
                $this->smarty->assign("moduleList", $moduleList);
                $this->display("websiteindex.htm");
            }else{
                $this->display("index.htm");
            }
        }
        exit;
    }

}