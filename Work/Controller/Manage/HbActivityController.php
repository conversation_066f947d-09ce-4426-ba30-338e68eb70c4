<?php

namespace Work\Controller\Manage;


class HbActivityController extends viewTpl
{
    public $data;
    public $u;
    public $t;
    public $c;
    public $iuser;
    public $Viewhtm;

    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
        $this->Viewhtm = $this->router->getController() . "/" . $this->router->getUrl() . ".htm";
    }

    function HomeView()
    {

    }

    /**
     * 获取 奖券列表
     * author: ling
     * 对应接口文档 0001
     * Date 2021/1/11 0011
     */
    function getHbListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        $datatype = array();
        if (!isset($request['p'])) {
            $p = '1';
        } else {
            $p = $request['p'];
        }

        $pageurl = "/{$this->u}/{$this->t}?";
        $datatype['p'] = $p;


        $datawhere = "1";
        if (isset($request['is_lottery']) && $request['is_lottery'] == 1) {
            $datawhere .= " and couponscode_pid<>'' ";
            $datatype['is_lottery'] = $request['is_lottery'];
        } elseif (isset($request['is_lottery']) && $request['is_lottery'] == 2) {
            $datawhere .= " and couponscode_pid ='' ";
            $datatype['is_lottery'] = $request['is_lottery'];
        }
        $pageurl .= "is_lottery={$request['is_lottery']}";
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (couponscode_mobile like '%{$request['keyword']}%' or couponscode_lotterypid like '%{$request['keyword']}%' or couponscode_pid like '%{$request['keyword']}%')  ";
            $datatype['keyword'] = $request['keyword'];
        }

        $sql = "select * from activity_couponscode where {$datawhere} ";
        $db_nums = $Show_css->selectOne("SELECT COUNT(*) as num FROM activity_couponscode where {$datawhere}");
        $allnum = $db_nums['num'];
        $datalist = $Show_css->dbwherePage($sql, $allnum, '40', $pageurl . '&p=', $p, '10', '2');

        $smarty->assign("pagelist", $datalist['pages']);//筛选信息

        $smarty->assign("dataList", $datalist['cont']);
        $smarty->assign("datatype", $datatype);

    }

    function importExcelView()
    {
        $reqeust = Input('get.', "", "trim,addslashes");
        $datawhere = '1';
        if (isset($reqeust['is_lottery']) && $reqeust['is_lottery'] == 1) {
            $datawhere .= " and couponscode_pid<>'' ";
        } elseif (isset($reqeust['is_lottery']) && $reqeust['is_lottery'] == 2) {
            $datawhere .= " and couponscode_pid ='' ";
        }

        $dataLsit = $this->Show_css->selectClear("select * from activity_couponscode where {$datawhere} ");
        if ($dataLsit) {
            $outexcel = array();
            foreach ($dataLsit as $dataVar) {
                $dataarray['couponscode_id'] = $dataVar['couponscode_id'];
                $dataarray['couponscode_lotterypid'] = $dataVar['couponscode_lotterypid'];
                $dataarray['couponscode_mobile'] = $dataVar['couponscode_mobile'];
                $dataarray['couponscode_pid'] = $dataVar['couponscode_pid'];
                $dataarray['couponscode_price'] = $dataVar['couponscode_price'];
                $dataarray['couponscode_lotterytime'] = $dataVar['couponscode_lotterytime'] ? date("Y-m-d H:i", $dataVar['couponscode_lotterytime']) : '';
                $dataarray['couponscode_status_name'] = $dataVar['couponscode_status'] == 1 ? '已使用' : '未使用';
                $outexcel[] = $dataarray;
            }
        }
        $excelheader = array("序号", "抽奖码", "手机号", "中奖码", "奖券价值", "抽奖时间", "状态");
        $excelfields = array("couponscode_id", "couponscode_lotterypid", "couponscode_mobile", "couponscode_pid", "couponscode_price", "couponscode_lotterytime", "couponscode_status_name");
        $excelname = "红包抽奖纪录.xlsx";
        if (!is_array($outexcel)) {
            jsbakerror_spl("没有数据！");
            exit;
        }
        query_to_excel($excelheader, $outexcel, $excelfields, $excelname);
        ajax_return(array("error" => 0, "errortip" => "下载完毕!", "bakfuntion" => "okmotify"));
    }


    public function __call($name, $arguments)
    {
        $this->smarty->assign("errorTip", "Calling object method '$name' " . implode(', ', $arguments) . "\n");
        $this->Viewhtm = "under.htm";
    }

    //魔术函数
    function __destruct()
    {
        $site_id = Input('get.site_id', 0);

        if ($this->c == 'Wait') {
            $this->smarty->assign("u", $this->router->getController());
            $this->smarty->assign("t", $this->router->getUrl());
            $this->smarty->assign("Viewhtm", $this->Viewhtm);
            $websites = $this->Show_css->getOne("cms_websites", "site_id='{$site_id}'");
            $this->smarty->assign("websites", $websites);
            if ($site_id) {
                if ($this->UserLogin['user_type'] == '1' and $this->UserLogin['user_limitsinc'] == '1') {
                    $moduleList = $this->Show_css->select("SELECT m.* FROM cms_module as m LEFT JOIN cms_moduleapply as a ON a.module_id = m.module_id WHERE a.site_id = '{$site_id}' ORDER BY m.module_weight ASC ");
                } else {
                    $moduleList = $this->Show_css->select("SELECT m.* FROM cms_module as m LEFT JOIN cms_moduleapply as a ON a.module_id = m.module_id LEFT JOIN cms_websites_func as c ON c.site_id = a.site_id and c.module_id = a.module_id WHERE a.site_id = '{$site_id}' and c.user_id = '{$this->UserLogin['user_id']}' ORDER BY m.module_weight ASC ");
                }
                // print_r($moduleList);die;
                $this->smarty->assign("moduleList", $moduleList);
                $this->display("websiteindex.htm");
            } else {
                $this->display("index.htm");
            }
        }
        exit;
    }

}