<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn 
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2016/12/16
 * Time: 14:45
 */

namespace Work\Controller\Manage;
use OSS\OssClient;
use OSS\Core\OssException;


class ImagesController  extends viewTpl{
    public $data;
    public $u;
    public $t;
    public $c;
    public $Viewhtm;
    //预加载处理类
    function __construct() {
        parent::__construct ();
        if(!$this->check_login()){
            ajax_return(array('error' => 1,'errortip' => "您未登录禁止上传图片!","bakfuntion"=>"dangerFromTip"));
        }
    }
    //主页
    function ListView()
    {
        ajax_return(array('error' => 1,'errortip' => "系统未收到您任何操作!","bakfuntion"=>"warningFromTip"));
    }
    //图片管理
    function ManageView(){
        $imgList = $this->Show_css->select("SELECT u.tb_imgurl FROM cms_uploadimg as u order by u.upload_id DESC limit 0 ,20");
        $imgarray = array();
        if($imgList){
            foreach($imgList as $imgVar){
                $imgarray[] = imggetpath($imgVar['tb_imgurl']);
            }
        }
        ajax_return($imgarray);}
    //主页
    function UploadingView()
    {
        $this->display($this->router->getController()."/".$this->router->getUrl().".htm");
        exit;
    }
    //上传图片路径
    function UpdataAction()
    {
        if (! empty ( $_FILES ["ossfile"] )) {
            $md5file = md5_file($_FILES['ossfile']['tmp_name']);
            $getTimg = $this->Show_css->getOne('cms_uploadimg',"upload_md5='{$md5file}'");
            $kuozhan = strrchr($_FILES['ossfile']['name'], '.');
            $imgname = basename($_FILES['ossfile']['name'], $kuozhan);
            if($getTimg){
                $res = array('error' => 0,'errortip' => "图片上传成功!",'originalimg'=>$getTimg['upload_imgurl'],'thumbnailimg'=>$getTimg['upload_thumburl'],'imgname'=>$imgname,"bakfuntion"=>"okmotify");
                ajax_return($res);
            }else{
                $md5file = md5_file($_FILES['ossfile']['tmp_name']);
                $imglink = UpOssFile($_FILES);
                if($imglink){
                    $date =array();
                    $date['upload_imgurl'] = $imglink;
                    $date['upload_thumburl'] = $imglink."?x-oss-process=style/wbl";
                    $date['upload_md5'] = $md5file;
                    $this->Show_css->insertData('cms_uploadimg',$date);

                    $res = array('error' => 0,'errortip' => "图片上传成功!",'originalimg'=>$date['upload_imgurl'],'thumbnailimg'=>$date['upload_thumburl'],'imgname'=>$imgname,"bakfuntion"=>"okmotify");
                    ajax_return($res);
                }else{
                    $res = array('error' => 1,'errortip' => "图片上传失败","bakfuntion"=>"errormotify");
                    ajax_return($res);
                }
            }
        }else{
            $res = array('error' => 1,'errortip' => "您未选择任何图片上传","bakfuntion"=>"errormotify");
            ajax_return($res);
        }
    }
    //上传图片路径
    function ImguploadView()
    {
        $this->c="Oss";
        $request = Input('get.','','trim,addslashes');
        $md5file = md5_file($_FILES['file']['tmp_name']);
        if($request['Type'] == 'maps'){
            $fileurl = UpOssFile($_FILES,"manage/maps/");
        }else{
            $fileurl = UpOssFile($_FILES);
        }

        $getTfile = $this->Show_css->getOne('cms_uploadfile',"upfile_md5='{$md5file}'");
        if($getTfile){
            $res = array('error' => 0,'errortip' => "文件上传成功!",'link'=>$getTfile['upfile_fileurl'],'filename'=>$getTfile['upfile_name'],'fileid'=>$getTfile['upfile_id'],"bakfuntion"=>"successFromTip");
            ajax_return($res);
        }else {
            $date = array();
            $date['upfile_name'] = $_FILES['file']['filename'];
            $date['upfile_fileurl'] = $fileurl;
            $date['upfile_md5'] = $md5file;
            $this->Show_css->insertData('cms_uploadfile', $date);
            $getTfile = $this->Show_css->getOne('cms_uploadfile',"upfile_md5='{$md5file}'");
            $res = array('error' => 0,'errortip' => "文件上传成功!",'link'=>$getTfile['upfile_fileurl'],'filename'=>$getTfile['upfile_name'],'fileid'=>$getTfile['upfile_id'],"bakfuntion"=>"successFromTip");
            ajax_return($res);
        }
    }
    //上传文件路径
    function FilesUploadView(){
        $request = Input('get.','','trim,addslashes');
        $fileType = array('pdf','jpg','gif','png','doc','xls','zip','docx','xlsx','ppt','pptx','mp3','mp4');
        $md5file = md5_file($_FILES['file']['tmp_name']);
        $uploadfile = new \Webfile($_FILES['file'],$files_dir='../static/file', $size = 2097152,$fileType);
        $fileurl = str_replace('../static/',"",$uploadfile->upload());

        $getTfile = $this->Show_css->getOne('cms_uploadfile',"tb_md5='{$md5file}'");
        if($getTfile){
            $res = array('error' => 0,'errortip' => "文件上传成功!",'link'=>$getTfile['tb_fileurl'],'filename'=>$getTfile['tb_name'],'fileid'=>$getTfile['upfile_id']);
            ajax_return($res);
        }else {
            $date = array();
            $date['tb_name'] = $request['filename'];
            $date['tb_fileurl'] = imggetpath($fileurl);
            $date['tb_md5'] = $md5file;
            $this->Show_css->insertData('cms_uploadfile', $date);
            $getTfile = $this->Show_css->getOne('cms_uploadfile',"tb_md5='{$md5file}'");
            $res = array('error' => 0,'errortip' => "文件上传成功!",'link'=>$getTfile['tb_fileurl'],'filename'=>$getTfile['tb_name'],'fileid'=>$getTfile['upfile_id']);
            ajax_return($res);
        }
    }
    //OSS上传文件路径
    function OssUploadView(){
        $this->c="Oss";
        $request = Input('get.','','trim,addslashes');
        if(empty($_FILES)){
            $res = array('error' => 1,'errortip' => "文件取消上传!");
            ajax_return($res);
        }
        $md5file = md5_file($_FILES['ossfile']['tmp_name']);
        if($request['Type'] == 'maps'){
            $fileurl = UpOssFile($_FILES,"manage/maps/");
        }else{
            $fileurl = UpOssFile($_FILES);
        }
        $filesize = $_FILES['ossfile']['size'];
        $getTfile = $this->Show_css->getOne('cms_uploadfile',"upfile_md5='{$md5file}'");
        if($getTfile){
            $res = array('error' => 0,'errortip' => "文件上传成功!",'link'=>$getTfile['upfile_fileurl'],'filename'=>$getTfile['upfile_name'],'filesize'=>$filesize,'fileid'=>$getTfile['upfile_id']);
            ajax_return($res);
        }else {
            $date = array();
            $date['upfile_name'] = $_FILES['ossfile']['name'];
            $date['upfile_fileurl'] = $fileurl;
            $date['upfile_md5'] = $md5file;
            $this->Show_css->insertData('cms_uploadfile', $date);
            $getTfile = $this->Show_css->getOne('cms_uploadfile',"upfile_md5='{$md5file}'");
            $res = array('error' => 0,'errortip' => "文件上传成功!",'link'=>$getTfile['upfile_fileurl'],'filename'=>$getTfile['upfile_name'],'filesize'=>$filesize,'fileid'=>$getTfile['upfile_id']);
            ajax_return($res);
        }
    }

    //OSS上传文件路径
    function OssUploadVideoView(){
        $this->c="Oss";
        $request = Input('get.','','trim,addslashes');
        if(empty($_FILES)){
            $res = array('error' => 1,'errortip' => "文件取消上传!");
            ajax_return($res);
        }
        $md5file = md5_file($_FILES['ossfile']['tmp_name']);
        if($request['Type'] == 'smc'){
            $fileurl = UpOssFile($_FILES,"manage/xiaowu/smc/");
        }elseif($request['Type'] == 'crm'){
            $fileurl = UpOssFile($_FILES,"manage/xiaowu/crm/");
        }elseif($request['Type'] == 'gmc'){
            $fileurl = UpOssFile($_FILES,"manage/xiaowu/gmc/");
        }elseif($request['Type'] == 'easx'){
            $fileurl = UpOssFile($_FILES,"manage/xiaowu/easx/");
        }else{
            $fileurl = UpOssFile($_FILES);
        }
//        print_r($request);die;
        $filesize = $_FILES['ossfile']['size'];
        $getTfile = $this->Show_css->getOne('cms_uploadfile',"upfile_md5='{$md5file}'");
        if($getTfile){
            $res = array('error' => 0,'errortip' => "文件上传成功!",'link'=>$getTfile['upfile_fileurl'],'filename'=>$getTfile['upfile_name'],'filesize'=>$filesize,'fileid'=>$getTfile['upfile_id']);
            ajax_return($res);
        }else {
            $date = array();
            $date['upfile_name'] = $_FILES['ossfile']['name'];
            $date['upfile_fileurl'] = $fileurl;
            $date['upfile_md5'] = $md5file;
            $this->Show_css->insertData('cms_uploadfile', $date);
            $getTfile = $this->Show_css->getOne('cms_uploadfile',"upfile_md5='{$md5file}'");
            $res = array('error' => 0,'errortip' => "文件上传成功!",'link'=>$getTfile['upfile_fileurl'],'filename'=>$getTfile['upfile_name'],'filesize'=>$filesize,'fileid'=>$getTfile['upfile_id']);
            ajax_return($res);
        }
    }
}
