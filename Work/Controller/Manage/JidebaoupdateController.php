<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2019/2/16
 * Time: 16:12
 */

namespace Work\Controller\Manage;


class JidebaoupdateController extends viewTpl{
    public $data;
    public $u;
    public $t;
    public $c;
    public $iuser;
    public $Viewhtm;
    public $Module;

    function __construct() {
        parent::__construct ();
        if(!$this->check_login()){
            $this->LoginView();
        }else{
            $this->smarty->assign("iuser", $this->UserLogin);
            $this->iuser = $this->UserLogin;
        }
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
        $this->Viewhtm = $this->router->getController()."/".$this->router->getUrl().".htm";
        $this->Module = $this->Show_css->getOne("cms_module","module_mark='{$this->u}'");
        $this->smarty->assign("moduleOne",$this->Module);
    }
    //主页
    function HomeView()
    {
        $request = Input('get.','','trim,addslashes');
        $smarty = $this->smarty;

        if(!isset($request['p'])){
            $p = '1';
        }else{
            $p= $request['p'];
        }

        $datawhere = "s.company_id = '1001'";
        $pageurl = "/{$this->u}/{$this->t}?";
        if ($request['site_id']) {
            $pageurl .= "&site_id={$request['site_id']}";
        }

        $datatype = array();
        if(isset($request['keyword']) && $request['keyword'] !==''){
            $datawhere .= " and (s.school_cnname like '%{$request['keyword']}%' or s.school_shortname like '%{$request['keyword']}%' or s.school_branch like '%{$request['keyword']}%')";
            $pageurl .="&keyword={$request['keyword']}";
            $datatype['keyword'] = $request['keyword'];
        }

        if(isset($request['school_nature']) && $request['school_nature'] !==''){
            $datawhere .= " and  s.school_nature = '{$request['school_nature']}'";
            $pageurl .="&school_nature={$request['school_nature']}";
            $datatype['school_nature'] = $request['school_nature'];
        }

        if(isset($request['school_class']) && $request['school_class'] !==''){
            $datawhere .= " and s.school_class = '{$request['school_class']}'";
            $pageurl .="&school_class={$request['school_class']}";
            $datatype['school_class'] = $request['school_class'];
        }

        $sql = "SELECT s.*
,(SELECT COUNT(c.class_id) FROM smc_class as c WHERE s.school_id = c.school_id) as classnums
,r1.region_name as province_name,r2.region_name as city_name,r3.region_name as area_name
              FROM smc_school as s
              left join smc_code_region as r1 on r1.region_id = s.school_province
              left join smc_code_region as r2 on r2.region_id = s.school_city
              left join smc_code_region as r3 on r3.region_id = s.school_area where {$datawhere} order by s.school_id DESC";
        $db_nums = $this->Show_css->selectOne("SELECT COUNT(s.school_id) as countnums FROM smc_school as s where {$datawhere}");
        $allnum = $db_nums['countnums'];

        $datalist = $this->Show_css->dbwherePage($sql,$allnum,'10',$pageurl.'&p=',$p,'10','2');

        $smarty->assign("pagelist",$datalist['pages']);//筛选信息

        $smarty->assign("dataList",$datalist['cont']);
        $smarty->assign("datatype",$datatype);

    }

    function ClassView(){
        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        if (!isset($request['p'])) {
            $p = '1';
        } else {
            $p = $request['p'];
        }

        $datawhere = "c.company_id = '1001'";
        $pageurl = "/{$this->u}/{$this->t}?";
        if ($request['site_id']) {
            $pageurl .= "&site_id={$request['site_id']}";
        }

        $datatype = array();
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (c.class_cnname like '%{$request['keyword']}%' or c.class_branch like '%{$request['keyword']}%' or c.class_id = '{$request['keyword']}')";
            $pageurl .= "&keyword={$request['keyword']}";
            $datatype['keyword'] = $request['keyword'];
        }

        if(isset($request['school_id']) && $request['school_id'] !== '0'){
            $datawhere .= " and c.school_id = '{$request['school_id']}' ";
            $pageurl .="&school_id={$request['school_id']}";
            $datatype['school_id'] = $request['school_id'];

            $schoolOne = $this->Show_css->getOne("smc_school","school_id='{$request['school_id']}'");
            $smarty->assign("schoolOne", $schoolOne);
        }

        $sql = "SELECT c.*,ca.course_cnname
	            ,(SELECT count(t.teach_id) FROM smc_class_teach as t WHERE t.class_id = c.class_id and t.teach_status = '0') as teanums
	            ,(SELECT count(s.study_id) FROM smc_student_study as s WHERE s.class_id = c.class_id) as stunums
            FROM smc_class AS c
            LEFT JOIN smc_course AS ca ON ca.course_id = c.course_id
             WHERE {$datawhere} ORDER BY c.class_branch DESC";

        $dbNums = $Show_css->selectOne("
            SELECT COUNT(c.class_id) as countnums FROM smc_class AS c
            LEFT JOIN smc_course AS ca ON ca.course_id = c.course_id
             WHERE {$datawhere} ORDER BY c.class_id DESC limit 0,1");//相关条件下的总记录数COUNT(*)
        $allnum = $dbNums['countnums'];

        $datalist = $Show_css->dbwherePage($sql, $allnum, '10', $pageurl . '&p=', $p, '10', '2');

        $smarty->assign("pagelist", $datalist['pages']);//筛选信息

        $smarty->assign("dataList", $datalist['cont']);
        $smarty->assign("datatype", $datatype);
    }

    function TeacherView(){
        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;
        $classOne = $Show_css->getOne("smc_class", "class_id='{$request['class_id']}'");
        $smarty->assign("classOne", $classOne);

        $dataList = $Show_css->selectClear("SELECT s.* FROM smc_staffer AS s
            LEFT JOIN smc_class_teach AS t ON s.staffer_id = t.staffer_id
            WHERE t.class_id='{$request['class_id']}' and t.teach_status = '0'
            ORDER BY t.staffer_id DESC");
        $smarty->assign("dataList", $dataList);
    }


    function ClasshourView(){
        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;
        $classOne = $Show_css->getOne("smc_class", "class_id='{$request['class_id']}'");
        $smarty->assign("classOne", $classOne);

        $hourList = $Show_css->selectClear("SELECT c.* FROM smc_class_hour as c WHERE c.class_id='{$request['class_id']}' ORDER BY c.hour_starttime ASC");
        $smarty->assign("hourList", $hourList);
    }


    function StudentView(){
        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;
        $classOne = $Show_css->getOne("smc_class", "class_id='{$request['class_id']}'");
        $smarty->assign("classOne", $classOne);

        $dataList = $Show_css->selectClear("SELECT t.* FROM smc_student AS t
            LEFT JOIN smc_student_study AS s ON t.student_id = s.student_id
            WHERE s.class_id='{$request['class_id']}'
            ORDER BY t.student_id DESC");
        $smarty->assign("dataList", $dataList);
    }
    //删除
    function DelAction()
    {
        $request = Input('get.','','trim,addslashes');
        if($this->Show_css->delData('smc_school',"school_id='{$request['school_id']}'")){
            $this->Recordweblog($request['site_id'],$this->Module['module_id'],$this->c,"删除校园数据，校园ID:{$request['school_id']}");
            ajax_return(array('error' => 0,'errortip' => "删除成功!","bakfuntion"=>"okmotify","bakurl"=>"/{$this->u}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "删除失败!","bakfuntion"=>"errormotify"));
        }
    }

    //魔术函数
    function __destruct()
    {
        $site_id = Input('get.site_id',0);

        if ($this->c =='Wait') {
            $this->smarty->assign("u", $this->router->getController());
            $this->smarty->assign("t", $this->router->getUrl());
            $this->smarty->assign("Viewhtm", $this->Viewhtm);
            $websites = $this->Show_css->getOne("cms_websites","site_id='{$site_id}'");
            $this->smarty->assign("websites",$websites);
            if($site_id){
                if($this->UserLogin['user_type'] == '1' and $this->UserLogin['user_limitsinc'] == '1'){
                    $moduleList = $this->Show_css->select("SELECT m.* FROM cms_module as m LEFT JOIN cms_moduleapply as a ON a.module_id = m.module_id WHERE a.site_id = '{$site_id}' ORDER BY m.module_weight ASC ");
                }else{
                    $moduleList = $this->Show_css->select("SELECT m.* FROM cms_module as m LEFT JOIN cms_moduleapply as a ON a.module_id = m.module_id LEFT JOIN cms_websites_func as c ON c.site_id = a.site_id and c.module_id = a.module_id WHERE a.site_id = '{$site_id}' and c.user_id = '{$this->UserLogin['user_id']}' ORDER BY m.module_weight ASC ");
                }
                $this->smarty->assign("moduleList", $moduleList);
                $this->display("websiteindex.htm");
            }else{
                $this->display("index.htm");
            }
        }
        exit;
    }

}