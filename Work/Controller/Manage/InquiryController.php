<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohisp.cn
 * 网站地址 : http://www.mohisp.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2016/12/19
 * Time: 14:21
 */

namespace Work\Controller\Manage;


class InquiryController extends viewTpl{
    public $data;
    public $iuser;
    public $u;
    public $t;
    public $c;
    public $Viewhtm;

    function __construct() {
        parent::__construct ();
        if(!$this->check_login()){
            $this->LoginView();
        }
        $this->smarty->assign("iuser", $this->UserLogin);
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
        $this->Viewhtm = $this->router->getController()."/".$this->router->getUrl().".htm";

        $moduleOne = $this->Show_css->getOne("cms_module","module_mark='{$this->u}'");
        $this->smarty->assign("moduleOne",$moduleOne);
        $this->Module = $moduleOne;
    }
    //主页
    function HomeView()
    {
        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        if(!isset($request['p'])){
            $p = '1';
        }else{
            $p= $request['p'];
        }

        $datawhere = "1";
        $pageurl = "/{$this->u}/{$this->t}?";
        if ($request['site_id']) {
            $pageurl .= "&site_id={$request['site_id']}";
        }

        $datatype = array();
        if(isset($request['keyword']) && $request['keyword'] !==''){
            $datawhere .= " and (p.pur_companyname like '%{$request['keyword']}%' or i.inquiry_pid like '%{$request['keyword']}%')";
            $pageurl .="&keyword={$request['keyword']}";
            $datatype['keyword'] = $request['keyword'];
        }

        if(isset($request['purchaser_id']) && $request['purchaser_id']>0){
            $datawhere .= " and i.purchaser_id = '{$request['purchaser_id']}'";
            $pageurl .="&purchaser_id={$request['purchaser_id']}";
            $datatype['purchaser_id'] = $request['purchaser_id'];
        }

        $sql = "SELECT i.*,p.pur_companyname FROM myw_inquiry as i LEFT JOIN myw_purchaser as p on i.purchaser_id=p.purchaser_id where {$datawhere} order by i.inquiry_id DESC ";
        $db_nums = $Show_css->select("SELECT COUNT(i.inquiry_id) FROM myw_inquiry as i LEFT JOIN myw_purchaser as p on i.purchaser_id=p.purchaser_id where {$datawhere}");//相关条件下的总记录数COUNT(*)
        $allnum = $db_nums[0][0];

        $datalist = $Show_css->dbwherePage($sql,$allnum,'10',$pageurl.'&p=',$p,'10','2');

        $smarty->assign("pagelist",$datalist['pages']);
        $smarty->assign("dataList",$datalist['cont']);
        $smarty->assign("datatype",$datatype);
    }

    //提交处理机制
    function DelAction()
    {
        $inquiry_id = Input('get.id',0);
        $request = Input('get.','','trim,addslashes');
        if($this->Show_css->delData('myw_inquiry',"inquiry_id='{$inquiry_id}'")){
            $this->Recordweblog($request['site_id'],$this->Module['module_id'],$this->c,"删除询价表，ID:{$inquiry_id}");
            ajax_return(array('error' => 0,'errortip' => "删除成功!","bakfuntion"=>"okmotify"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "新增失败!","bakfuntion"=>"errormotify"));
        }
    }

    //询价单明细表
    function DetailView(){
        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        $datawhere = "1";

        $datatype = array();
        if(isset($request['purchaser_id']) && $request['purchaser_id']>0){
            $datawhere .= " and i.purchaser_id = '{$request['purchaser_id']}'";
            $datatype['purchaser_id'] = $request['purchaser_id'];
        }

//        $datalist = $Show_css->selectClear("select d.*,(select g.goods_name from myw_goods as g where d.goods_id=g.goods_id )  as goods_name  from  myw_inquiry_goods as d   where '{$datawhere}' ");
          $datalist = $Show_css->selectClear("SELECT ig.*  FROM myw_inquiry_goods AS ig WHERE {$datawhere} AND ig.inquiry_pid = '{$request['inquiry_pid']}'");
        $smarty->assign("dataList",$datalist);
        $smarty->assign("datatype",$datatype);
    }

    //提交处理机制
    function DelDetailAction()
    {
        $detail_id = Input('get.id',0);
        $request = Input('get.','','trim,addslashes');
        if($this->Show_css->delData('myw_inquiry_detail',"detail_id='{$detail_id}'")){
            $this->Recordweblog($request['site_id'],$this->Module['module_id'],$this->c,"删除询价表，ID:{$detail_id}");
            ajax_return(array('error' => 0,'errortip' => "删除成功!","bakfuntion"=>"okmotify"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "新增失败!","bakfuntion"=>"errormotify"));
        }
    }


    //魔术方法
    public function __call($name, $arguments) {
        $this->smarty->assign("errorTip", "Calling object method '$name' ". implode(', ', $arguments). "\n");

        $this->Viewhtm = "under.htm";
    }
    //魔术方法
    function __destruct()
    {
        $site_id = Input('get.site_id',0);

        if ($this->c =='Wait') {
            $this->smarty->assign("u", $this->router->getController());
            $this->smarty->assign("t", $this->router->getUrl());
            $this->smarty->assign("Viewhtm", $this->Viewhtm);
            if($site_id){
                $websites = $this->Show_css->getOne("cms_websites","site_id='{$site_id}'");
                $this->smarty->assign("websites",$websites);

                if($this->UserLogin['user_type'] == '1' and $this->UserLogin['user_limitsinc'] == '1'){
                    $moduleList = $this->Show_css->select("SELECT m.* FROM cms_module as m LEFT JOIN cms_moduleapply as a ON a.module_id = m.module_id WHERE a.site_id = '{$site_id}' ORDER BY m.module_weight ASC ");
                }else{
                    $moduleList = $this->Show_css->select("SELECT m.* FROM cms_module as m LEFT JOIN cms_moduleapply as a ON a.module_id = m.module_id LEFT JOIN cms_websites_func as c ON c.site_id = a.site_id and c.module_id = a.module_id WHERE a.site_id = '{$site_id}' and c.user_id = '{$this->UserLogin['user_id']}' ORDER BY m.module_weight ASC ");
                }
                $this->smarty->assign("moduleList", $moduleList);
                $this->display("websiteindex.htm");
            }else{
                $this->display("index.htm");
            }
        }
        exit;
    }

}
