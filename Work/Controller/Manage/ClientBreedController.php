<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/6/29
 * Time: 23:55
 */

namespace Work\Controller\Manage;


class ClientBreedController extends viewTpl{
    public $data;
    public $u;
    public $t;
    public $c;
    public $iuser;
    public $Viewhtm;

    //预加载处理类
    function __construct() {
        parent::__construct ();
        if(!$this->check_login()){
            $this->LoginView();
        }
        if($this->check_login()){
            $this->smarty->assign("iuser", $this->UserLogin);
            $this->iuser = $this->UserLogin;
        }
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
        $this->Viewhtm = $this->router->getController()."/".$this->router->getUrl().".htm";


        $moduleOne = $this->Show_css->getOne("cms_module","module_mark='{$this->u}'");
        $this->smarty->assign("moduleOne",$moduleOne);
    }

    //主页
    function HomeView()
    {
        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        if(!isset($request['p'])){
            $p = '1';
        }else{
            $p= $request['p'];
        }

        $datawhere = "c.channel_id = l.channel_id AND c.company_id = '8888'";
        $site_id = Input('get.site_id',0);
        $pageurl = "/{$this->u}/{$this->t}?site_id={$site_id}";

        if(isset($request['keyword']) && $request['keyword'] !==''){
            $datawhere .= " and (c.client_cnname like '%{$request['keyword']}%' or c.client_mobile like '%{$request['keyword']}%')";
            $pageurl .="&keyword={$request['keyword']}";
            $datatype['keyword'] = $request['keyword'];
        }

        if(isset($request['breed_status']) && $request['breed_status']!==''){
            $datawhere .= " and c.breed_status='{$request['breed_status']}'";
            $pageurl .="&breed_status={$request['breed_status']}";
            $datatype['breed_status'] = $request['breed_status'];
        }

        //开始结束时间
        if(isset($request['starttime']) && $request['starttime']!=''){
            $starttime = strtotime($request['starttime']);
            $datawhere .= " and c.breed_readtimes>'{$starttime}'";
            $pageurl .="&starttime={$request['starttime']}";
            $datatype['starttime'] = $request['starttime'];
        }
        if(isset($request['endtime']) && $request['endtime']!=''){
            $endtime = strtotime($request['endtime'])+86400;
            $datawhere .= " and c.breed_readtimes<'{$endtime}'";
            $pageurl .="&endtime={$request['endtime']}";
            $datatype['endtime'] = $request['endtime'];
        }

        if(isset($request['readtimes']) && $request['readtimes'] !==''){
            if($request['readtimes'] == '1'){
                $datawhere .= " and c.breed_readtimes <> '0'";
            }else{
                $datawhere .= " and c.breed_readtimes = '0'";
            }
            $pageurl .="&readtimes={$request['readtimes']}";
            $datatype['readtimes'] = $request['readtimes'];
        }

        if(isset($request['buytimes']) && $request['buytimes'] !==''){
            if($request['buytimes'] == '1'){
                $datawhere .= " and c.breed_buytimes <> '0'";
            }else{
                $datawhere .= " and c.breed_buytimes = '0'";
            }
            $pageurl .="&buytimes={$request['buytimes']}";
            $datatype['buytimes'] = $request['buytimes'];
        }

        if(isset($request['leavetimes']) && $request['leavetimes'] !==''){
            if($request['leavetimes'] == '1'){
                $datawhere .= " and c.breed_leavetimes <> '0'";
            }else{
                $datawhere .= " and c.breed_leavetimes = '0'";
            }
            $pageurl .="&leavetimes={$request['leavetimes']}";
            $datatype['leavetimes'] = $request['leavetimes'];
        }

        $sql = "SELECT c.*,l.channel_medianame,l.channel_name FROM crm_client_breed AS c,crm_code_channel as l WHERE {$datawhere} ORDER BY c.breed_id DESC";

        $db_nums = $Show_css->selectClear("SELECT COUNT(c.breed_id) as countnum FROM crm_client_breed as c ,crm_code_channel as l  where {$datawhere} ");
        $allnum = $db_nums[0]['countnum'];
        $datalist = $Show_css->dbwherePage($sql,$allnum,'20',$pageurl.'&p=',$p,'10','2');

        $smarty->assign("pagelist",$datalist['pages']);//筛选信息

        $smarty->assign("dataList",$datalist['cont']);
        $smarty->assign("datatype",$datatype);
    }

    function BreedkxdAction()
    {
        ajax_return(array('error' => 0, 'errortip' => "随机100条成功!", "bakfuntion" => "okmotify"));
    }

    function BreedsushengAction()
    {
        $minTimes = time()-3600*24*90;
        $clientArray = $this->Show_css->selectClear("SELECT c.client_id,c.company_id,c.client_cnname, c.client_mobile, c.client_source, c.channel_id, c.client_createtime
FROM crm_client c WHERE
(c.company_id = '8888' AND c.client_tracestatus = '0' AND c.outthree_userid <> '' AND c.channel_id IN (255)
AND c.client_createtime <= '{$minTimes}'
AND c.client_id NOT IN ( SELECT s.client_id FROM crm_client_schoolenter s WHERE s.company_id = '8888' )
AND c.client_mobile NOT IN ( SELECT s.family_mobile FROM smc_student_family s, smc_student t WHERE s.student_id = t.student_id AND t.company_id = '8888')
AND c.client_mobile NOT IN ( SELECT b.client_mobile FROM crm_client_breed b WHERE b.company_id = '8888' )) ORDER BY RAND() LIMIT 0, 100");
        if($clientArray){
            $channelOne = $this->Show_css->getOne("crm_code_channel","channel_id='319'");
            foreach($clientArray as $clientOne){
                $breed = array();
                $breed['company_id'] = '8888';
                $breed['client_cnname'] = $clientOne['client_cnname'];
                $breed['client_mobile'] = $clientOne['client_mobile'];
                $breed['client_source'] = $clientOne['client_source'];
                $breed['breed_class'] = '1';
                $breed['channel_id'] = $clientOne['channel_id'];
                $breed['breed_updatetime'] = time();
                $breed['breed_createtime'] = $clientOne['client_createtime'];
                if($this->Show_css->insertData("crm_client_breed", $breed)){
                    //变更短信渠道
                    $data = array();
                    $data['channel_id'] = $channelOne['channel_id'];
                    $data['client_source'] = $channelOne['channel_medianame'];
                    $data['client_updatetime'] = time();
                    $data['client_createtime'] = time();
                    if ($this->Show_css->updateData("crm_client", "client_id = '{$clientOne['client_id']}'", $data)) {
                        $channello = array();
                        $channello['company_id'] = $clientOne['company_id'];
                        $channello['client_id'] = $clientOne['client_id'];
                        $channello['from_channel_id'] = $clientOne['channel_id'];
                        $channello['to_channel_id'] = $channelOne['channel_id'];
                        $channello['channellog_note'] = "名单通过TMK短信发送，进行重新激活操作！";
                        $channello['channellog_createtime'] = time();
                        $this->Show_css->insertData("crm_client_channellog", $channello);
                    }
                }
            }
        }
        ajax_return(array('error' => 0, 'errortip' => "随机100条成功!", "bakfuntion" => "okmotify"));
    }


    function BreedtmkAction()
    {
        $minTimes = time()-3600*24*90;
        $clientArray = $this->Show_css->selectClear("SELECT c.client_id,c.company_id,c.client_cnname, c.client_mobile, c.client_source, c.channel_id, c.client_createtime
FROM crm_client c WHERE
(c.company_id = '8888' AND c.client_tracestatus = '0' AND c.outthree_userid <> '' AND c.channel_id IN (253, 255, 270, 274, 275, 292)
AND c.client_createtime <= '{$minTimes}'
AND c.client_id NOT IN ( SELECT s.client_id FROM crm_client_schoolenter s WHERE s.company_id = '8888' )
AND c.client_mobile NOT IN ( SELECT s.family_mobile FROM smc_student_family s, smc_student t WHERE s.student_id = t.student_id AND t.company_id = '8888')
AND c.client_mobile NOT IN ( SELECT b.client_mobile FROM crm_client_breed b WHERE b.company_id = '8888' )) ORDER BY RAND() LIMIT 0, 100");
        if($clientArray){
            $channelOne = $this->Show_css->getOne("crm_code_channel","channel_id='319'");
            foreach($clientArray as $clientOne){
                $breed = array();
                $breed['company_id'] = '8888';
                $breed['client_cnname'] = $clientOne['client_cnname'];
                $breed['client_mobile'] = $clientOne['client_mobile'];
                $breed['client_source'] = $clientOne['client_source'];
                $breed['breed_class'] = '1';
                $breed['channel_id'] = $clientOne['channel_id'];
                $breed['breed_updatetime'] = time();
                $breed['breed_createtime'] = $clientOne['client_createtime'];
                if($this->Show_css->insertData("crm_client_breed", $breed)){
                    //变更短信渠道
                    $data = array();
                    $data['channel_id'] = $channelOne['channel_id'];
                    $data['client_source'] = $channelOne['channel_medianame'];
                    $data['client_updatetime'] = time();
                    $data['client_createtime'] = time();
                    if ($this->Show_css->updateData("crm_client", "client_id = '{$clientOne['client_id']}'", $data)) {
                        $channello = array();
                        $channello['company_id'] = $clientOne['company_id'];
                        $channello['client_id'] = $clientOne['client_id'];
                        $channello['from_channel_id'] = $clientOne['channel_id'];
                        $channello['to_channel_id'] = $channelOne['channel_id'];
                        $channello['channellog_note'] = "名单通过TMK短信发送，进行重新激活操作！";
                        $channello['channellog_createtime'] = time();
                        $this->Show_css->insertData("crm_client_channellog", $channello);
                    }
                }
            }
        }
        ajax_return(array('error' => 0, 'errortip' => "随机100条成功!", "bakfuntion" => "okmotify"));
    }

    /*function Sendmisgo($mobile,$mistxt,$tilte,$sendcode){
        if($this->xxSendmis($mobile,$mistxt)){
            $date['company_id'] = "8888";
            $date['mislog_tilte'] = $tilte;
            $date['mislog_mobile'] = $mobile;
            $date['mislog_sendcode'] = $sendcode;
            $date['mislog_mistxt'] = $mistxt;
            $date['mislog_sendinc'] = "1";
            $date['mislog_time'] = time();
            $this->Show_css->insertData('crm_mislog',$date);
            return true;
        }else{
            $date['company_id'] = "8888";
            $date['mislog_tilte'] = $tilte;
            $date['mislog_mobile'] = $mobile;
            $date['mislog_sendcode'] = $sendcode;
            $date['mislog_mistxt'] = $mistxt;
            $date['mislog_sendinc'] = "0";
            $date['mislog_time'] = time();
            $this->Show_css->insertData('crm_mislog',$date);
            return false;
        }
    }*/

    function Sendmisgo($mobile, $mistxt, $tilte, $sendcode,$array=array(),$tempId='0')
    {
        $Model = new \Model\Api\SmsqmModel();
        $Model->setAccount();
        $Model->setAppId();

        $result = $Model->sendTemplateSMS($mobile, $array, $tempId);
        if ($result == NULL) {
            $date = array();
            $date['company_id'] = "8888";
            $date['mislog_tilte'] = $tilte;
            $date['mislog_mobile'] = $mobile;
            $date['mislog_sendcode'] = $sendcode;
            $date['mislog_mistxt'] = $mistxt;
            $date['mislog_sendinc'] = "0";
            $date['mislog_errortip'] = "result error!";
            $date['mislog_time'] = time();
            $this->Show_css->insertData('crm_mislog', $date);
            return false;
        }
        if ($result->statusCode != 0) {
            $date = array();
            $date['company_id'] = "8888";
            $date['mislog_tilte'] = $tilte;
            $date['mislog_mobile'] = $mobile;
            $date['mislog_sendcode'] = $sendcode;
            $date['mislog_mistxt'] = $mistxt;
            $date['mislog_sendinc'] = "0";
            $date['mislog_errortip'] = "error code :" . $result->statusCode . "error msg :" . $result->statusMsg;
            $date['mislog_time'] = time();
            $this->Show_css->insertData('crm_mislog', $date);
            return false;
        } else {
            $date = array();
            $date['company_id'] = "8888";
            $date['mislog_tilte'] = $tilte;
            $date['mislog_mobile'] = $mobile;
            $date['mislog_sendcode'] = $sendcode;
            $date['mislog_mistxt'] = $mistxt;
            $date['mislog_sendinc'] = "1";
            $date['mislog_time'] = time();
            $this->Show_css->insertData('crm_mislog', $date);
            return true;
        }
    }

    /*function xxSendmis($mobile,$content){
        $frist = substr( $mobile, 0, 1 );
        if($frist == '1'){
            $sendbak = request_by_curl("http://api.feige.ee/smsservice.aspx","name=kidforce&pwd=37aca44219848c400c3ff9a6b&mobile={$mobile}&content={$content}&stime=&sign=吉的堡&type=pt&extno=","POST",array());
            $sendarray = explode(",",$sendbak);
            if($sendarray['0'] == '0'){
                return true;
            }else{
                return false;
            }
        }else{
            return false;
        }
    }*/

    function SendSmsAction()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $breedOne = $this->Show_css->getFieldOne("crm_client_breed","breed_id,client_mobile,breed_status"
            ,"breed_id='{$request['breed_id']}'");
        if(!$breedOne){
            ajax_return(array('error' => 1, 'errortip' => "名单不存在!", "bakfuntion" => "errormotify"));
        }
        if($breedOne['breed_status'] !== '0'){
            ajax_return(array('error' => 1, 'errortip' => "短信已发送!", "bakfuntion" => "errormotify"));
        }
        $bd = $this->from10to62($breedOne['breed_id']);
        $misNote = "亲爱的家长您好，吉的堡{成长中心}以全新的课程体系与大家见面啦！快带上小朋友来线下参观体验吧！https://pay.kidcastle.com.cn/Gener/pageThree?d=".$bd."}&回T退订";
        $array = array("{成长中心}","https://pay.kidcastle.com.cn/Gener/pageThree?d={$bd} ");
        if ($this->Sendmisgo($breedOne['client_mobile'], $misNote, "营销通知", $bd,$array,'1084664')) {
            $breeddata = array();
            $breeddata['breed_status'] = '1';
            $this->Show_css->updateData('crm_client_breed',"breed_id = '{$breedOne['breed_id']}'", $breeddata);
            ajax_return(array('error' => 0, 'errortip' => "发送成功!", "bakfuntion" => "successFromTip"));
        } else {
            $breeddata = array();
            $breeddata['breed_status'] = '-1';
            $this->Show_css->updateData('crm_client_breed', "breed_id = '{$breedOne['breed_id']}'", $breeddata);
            ajax_return(array('error' => 1, 'errortip' => "短信激活失败!", "bakfuntion" => "dangerFromTip"));
        }
    }

    function from62to10($str){
        $dict = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $len = strlen($str);
        $dec = 0;
        for($i = 0;$i<$len;$i++){
            //找到对应字典的下标
            $pos = strpos($dict, $str[$i]);
            $dec += $pos*pow(62,$len-$i-1);
        }
        return $dec;
    }
    function from10to62($dec) {
        $dict = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $result = '';
        do {
            $result = $dict[$dec % 62] . $result;
            $dec = intval($dec / 62);
        } while ($dec != 0);
        return $result;
    }

    //魔术方法
    public function __call($name, $arguments) {
        $this->smarty->assign("errorTip", "Calling object method '$name' ". implode(', ', $arguments). "\n");
        $this->Viewhtm = "under.htm";
    }
    //魔术函数
    function __destruct()
    {
        $site_id = Input('get.site_id',0);

        if ($this->c =='Wait') {
            $this->smarty->assign("u", $this->router->getController());
            $this->smarty->assign("t", $this->router->getUrl());
            $this->smarty->assign("Viewhtm", $this->Viewhtm);
            $websites = $this->Show_css->getOne("cms_websites","site_id='{$site_id}'");
            $this->smarty->assign("websites",$websites);
            if($site_id){
                if($this->UserLogin['user_type'] == '1' and $this->UserLogin['user_limitsinc'] == '1'){
                    $moduleList = $this->Show_css->select("SELECT m.* FROM cms_module as m LEFT JOIN cms_moduleapply as a ON a.module_id = m.module_id WHERE a.site_id = '{$site_id}' ORDER BY m.module_weight ASC ");
                }else{
                    $moduleList = $this->Show_css->select("SELECT m.* FROM cms_module as m LEFT JOIN cms_moduleapply as a ON a.module_id = m.module_id LEFT JOIN cms_websites_func as c ON c.site_id = a.site_id and c.module_id = a.module_id WHERE a.site_id = '{$site_id}' and c.user_id = '{$this->UserLogin['user_id']}' ORDER BY m.module_weight ASC ");
                }
                // print_r($moduleList);die;
                $this->smarty->assign("moduleList", $moduleList);
                $this->display("websiteindex.htm");
            }else{
                $this->display("index.htm");
            }
        }
        exit;
    }
}