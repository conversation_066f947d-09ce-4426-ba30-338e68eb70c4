<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2019/2/16
 * Time: 11:56
 */

namespace Work\Controller\Manage;


class CompanyController extends viewTpl
{
    public $data;
    public $u;
    public $t;
    public $c;
    public $icampus;
    public $iuser;
    public $Viewhtm;
    public $Module;
    public $pagestr = '';

    //预加载处理类
    function __construct()
    {
        parent::__construct();
        if (!$this->check_login()) {
            $this->LoginView();
        }
        if ($this->check_login()) {
            $this->smarty->assign("iuser", $this->UserLogin);
            $this->iuser = $this->UserLogin;
        }
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
        $this->Viewhtm = $this->router->getController() . "/" . $this->router->getUrl() . ".htm";
        $moduleOne = $this->Show_css->getOne("cms_module", "module_mark='{$this->u}'");
        $this->smarty->assign("moduleOne", $moduleOne);
        $this->Module = $moduleOne;
    }

    //主页
    function HomeView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $datatype = array();
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        if (!isset($request['p'])) {
            $p = '1';
        } else {
            $p = $request['p'];
        }

        $datawhere = "1";
        $pageurl = "/{$this->u}/{$this->t}?site_id={$request['site_id']}";


        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (c.company_cnname like '%{$request['keyword']}%' or c.company_code like '%{$request['keyword']}%' or c.company_mobile like '%{$request['keyword']}%' or c.company_id like '%{$request['keyword']}%' )";
            $pageurl .= "&keyword={$request['keyword']}";
            $datatype['keyword'] = $request['keyword'];
        }

        $sql = "SELECT c.*,(SELECT s.staffer_bakpass FROM smc_staffer as s WHERE c.company_id = s.company_id and s.account_class = '1' ORDER BY s.staffer_id ASC limit 0,1) as staffer_bakpass
FROM gmc_company as c where {$datawhere} order by c.company_id DESC";

        $db_nums = $Show_css->selectOne("SELECT COUNT(c.company_id) as countnums FROM gmc_company as c where {$datawhere}");//相关条件下的总记录数COUNT(*)
        $allnum = $db_nums['countnums'];

        $datalist = $Show_css->dbwherePage($sql, $allnum, '10', $pageurl . '&p=', $p, '10', '2');

        $smarty->assign("pagelist", $datalist['pages']);//筛选信息
        $smarty->assign("dataList", $datalist['cont']);
        $smarty->assign("datatype", $datatype);
    }

    function CompanyView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        if (!isset($request['p'])) {
            $p = '1';
        } else {
            $p = $request['p'];
        }

        $datawhere = "1";
        $pageurl = "/{$this->u}/{$this->t}?site_id={$request['site_id']}";

        $datatype = array();
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (c.company_cnname like '%{$request['keyword']}%' or c.company_code like '%{$request['keyword']}%' or c.company_mobile like '%{$request['keyword']}%' )";
            $pageurl .= "&keyword={$request['keyword']}";
            $datatype['keyword'] = $request['keyword'];
        }

        $sql = "SELECT c.*,
              (SELECT count(h.school_id) FROM smc_school as h WHERE h.company_id = c.company_id and h.school_isclose = '0' ) as schoolnum,
              (SELECT l.log_finalamount FROM tkl_balance_log AS l WHERE l.company_id = c.company_id ORDER BY log_id DESC limit 0,1) as log_finalamount 
              FROM gmc_company as c where {$datawhere} order by c.company_id DESC";

        $db_nums = $Show_css->selectOne("SELECT COUNT(c.company_id) as countnums FROM gmc_company as c where {$datawhere}");//相关条件下的总记录数COUNT(*)
        $allnum = $db_nums['countnums'];

        $datalist = $Show_css->dbwherePage($sql, $allnum, '10', $pageurl . '&p=', $p, '10', '2');

        $smarty->assign("pagelist", $datalist['pages']);//筛选信息

        $smarty->assign("dataList", $datalist['cont']);
        $smarty->assign("datatype", $datatype);
    }

    //合同管理
    function ContractView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        if (!isset($request['p'])) {
            $p = '1';
        } else {
            $p = $request['p'];
        }

        $datawhere = "1";
        $pageurl = "/{$this->u}/{$this->t}?site_id={$request['site_id']}";

        $datatype = array();
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (c.contract_name like '%{$request['keyword']}%')";
            $pageurl .= "&keyword={$request['keyword']}";
            $datatype['keyword'] = $request['keyword'];
        }
        if (isset($request['company_id']) && $request['company_id'] !== '') {
            $datawhere .= " and c.company_id ='{$request['company_id']}'";
            $pageurl .= "&company_id={$request['company_id']}";
            $datatype['company_id'] = $request['company_id'];
        }

        $sql = "SELECT c.*,ie.*
              (select co.company_cnname from gmc_company as co where co.company_id =c.company_id) as company_cnname
              FROM imc_sales_contract as c
              left join imc_edition as ie on ie.edition_id=c.edition_id 
              where {$datawhere} 
              order by c.contract_id DESC";


        $db_nums = $Show_css->selectOne("SELECT COUNT(c.contract_id) as countnums FROM imc_sales_contract as c where {$datawhere}");//相关条件下的总记录数COUNT(*)
        $allnum = $db_nums['countnums'];

        $datalist = $Show_css->dbwherePage($sql, $allnum, '10', $pageurl . '&p=', $p, '10', '2');
        debug($datalist);
        $smarty->assign("pagelist", $datalist['pages']);//筛选信息
        $smarty->assign("dataList", $datalist['cont']);
        $smarty->assign("datatype", $datatype);
    }

    //充值记录
    function LineRechargelogView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        if (!isset($request['p'])) {
            $p = '1';
        } else {
            $p = $request['p'];
        }

        $datawhere = "1 and c.company_id = '{$request['company_id']}' ";
        $pageurl = "/{$this->u}/{$this->t}?site_id={$request['site_id']}";

        $datatype = array();
//        if (isset($request['keyword']) && $request['keyword'] !== '') {
//            $datawhere .= " and (c.company_cnname like '%{$request['keyword']}%' or c.company_code like '%{$request['keyword']}%' or c.company_mobile like '%{$request['keyword']}%' )";
//            $pageurl .= "&keyword={$request['keyword']}";
//            $datatype['keyword'] = $request['keyword'];
//        }

        $sql = "SELECT c.*,
            (SELECT g.company_cnname FROM gmc_company as g WHERE g.company_id = c.company_id) as company_cnname,
            (SELECT s.school_cnname FROM smc_school as s WHERE s.school_branch = c.school_branch) as school_cnname 
            FROM tkl_balance_log as c 
            where {$datawhere} 
            order by c.log_id DESC";

        $db_nums = $Show_css->selectOne("SELECT COUNT(c.log_id) as countnums FROM tkl_balance_log as c where {$datawhere}");//相关条件下的总记录数COUNT(*)
        $allnum = $db_nums['countnums'];

        $datalist = $Show_css->dbwherePage($sql, $allnum, '10', $pageurl . '&p=', $p, '10', '2');

        $smarty->assign("pagelist", $datalist['pages']);//筛选信息

        $smarty->assign("dataList", $datalist['cont']);

        $datatype['company_id'] = $request['company_id'];
        $smarty->assign("datatype", $datatype);
    }

    function PermissionConfigureView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        if (!isset($request['p'])) {
            $p = '1';
        } else {
            $p = $request['p'];
        }

        $datawhere = "1";
        $pageurl = "/{$this->u}/{$this->t}?site_id={$request['site_id']}";

        $datatype = array();
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (p.product_name like '%{$request['keyword']}%' or p.product_branch like '%{$request['keyword']}%')";
            $pageurl .= "&keyword={$request['keyword']}";
            $datatype['keyword'] = $request['keyword'];
        }
        if (!isset($request['company_id']) || $request['company_id'] == '') {
            ajax_return(array('error' => 1, 'errortip' => "集团id必须传!", "bakfuntion" => "dangerFromTip"));
        } else {
            $pageurl .= "&company_id={$request['company_id']}";
            $datatype['company_id'] = $request['company_id'];
        }

        $sql = "select sc.edition_id
              from imc_sales_contract as sc,imc_edition as ie
              where sc.edition_id=ie.edition_id and sc.company_id='{$request['company_id']}' and sc.contract_starttime<=CURDATE() and sc.contract_endtime>=CURDATE() 
              order by sc.contract_createtime desc,sc.contract_id asc limit 0,1";

        $contractOne = $this->Show_css->selectOne($sql);

        if (!$contractOne) {
            ajax_return(array('error' => 1, 'errortip' => "无可用合同!", "bakfuntion" => "dangerFromTip"));
        }

        $sql = "select p.*,e.edition_id
              ,ifnull((select ge.editionpro_status from gmc_editionpro as ge where ge.company_id='{$request['company_id']}' and ge.product_id=e.product_id limit 0,1),1) as status
              from imc_editproapply as e 
              inner join imc_product as p on e.product_id=p.product_id
              where {$datawhere} and e.edition_id='{$contractOne['edition_id']}'
              order by e.product_id asc
              ";

        $db_nums = $Show_css->selectOne("SELECT COUNT(e.edition_id) as countnums
              from imc_editproapply as e 
              inner join imc_product as p on e.product_id=p.product_id
              where {$datawhere} and e.edition_id='{$contractOne['edition_id']}' ");//相关条件下的总记录数COUNT(*)
        $allnum = $db_nums['countnums'];

        $datalist = $Show_css->dbwherePage($sql, $allnum, '30', $pageurl . '&p=', $p, '30', '2');

        $smarty->assign("pagelist", $datalist['pages']);//筛选信息
        $smarty->assign("dataList", $datalist['cont']);
        $smarty->assign("datatype", $datatype);
    }

    function ChangestatusAction()
    {
        $request = Input('get.', '', 'trim,addslashes');

        if (isset($request['company_id']) && $request['company_id'] == '') {
            $res = array('error' => '1', 'errortip' => '集团ID必须传!', "bakfuntion" => "errormotify");
            ajax_return($res);
        }

        if (isset($request['id']) && $request['id'] == '') {
            $res = array('error' => '1', 'errortip' => '产品ID必须传!', "bakfuntion" => "errormotify");
            ajax_return($res);
        }

        $proOne = $this->Show_css->getOne("gmc_editionpro", "company_id='{$request['company_id']}' and product_id='{$request['id']}'");
        $data = array();
        if ($proOne) {
            if ($proOne['editionpro_status'] == '1') {
                $data['editionpro_status'] = "0";
            } else {
                $data['editionpro_status'] = "1";
            }

            if ($this->Show_css->updateData("gmc_editionpro", "product_id = '{$proOne['product_id']}' and company_id='{$request['company_id']}'", $data)) {
                if ($data['editionpro_status'] == '1') {
                    $this->Recordweblog($request['site_id'], $this->Module['module_id'], $this->c, "修改集团产品权限设置状态，ID:{$request['id']}");
                    ajax_return(array('error' => 0, 'errortip' => "提交成功!", "bakfuntion" => "okmotify", "state" => "1"));
                } else {
                    $this->Recordweblog($request['site_id'], $this->Module['module_id'], $this->c, "修改集团产品权限设置状态，ID:{$request['id']}");
                    ajax_return(array('error' => 0, 'errortip' => "提交成功!", "bakfuntion" => "okmotify", "state" => "0"));
                }
            } else {
                ajax_return(array('error' => 1, 'errortip' => "修改失败!", "bakfuntion" => "errormotify"));
            }
        } else {
            $data['company_id'] = $request['company_id'];
            $data['product_id'] = $request['id'];
            $data['editionpro_status'] = "1";
            if ($this->Show_css->insertData("gmc_editionpro", $data)) {
                $this->Recordweblog($request['site_id'], $this->Module['module_id'], $this->c, "修改集团产品权限设置状态，ID:{$request['id']}");
                ajax_return(array('error' => 0, 'errortip' => "提交成功!", "bakfuntion" => "okmotify", "state" => "1"));
            } else {
                ajax_return(array('error' => 1, 'errortip' => "修改失败!", "bakfuntion" => "errormotify"));
            }
        }
    }

    function EditionView()
    {
        $request = Input('get.', '', 'trim,addslashes');

        $promoduleList = $this->Show_css->getList("imc_editpromodule", "edition_id='{$request['edition_id']}' and product_id='{$request['product_id']}'");

        if (!$promoduleList) {
//            ajax_return(array('error' => 1, 'errortip' => "请先设置产品模块!", "bakfuntion" => "dangerFromTip"));
            $promoduleList = array();
        }

        $moduleArray = array_column($promoduleList, 'module_id');

        $sql = "select module_id,module_name,father_id,module_level
                ,ifnull((select em.module_status from gmc_editionpro_module as em where em.module_id=m.module_id and em.company_id='{$request['company_id']}'),1) as status
                from imc_module as m 
                where m.product_id='{$request['product_id']}'
                and exists(select 1 from imc_editpromodule as ie where ie.module_id=m.module_id and ie.product_id=m.product_id and ie.edition_id='{$request['edition_id']}')
                order by m.module_class asc,m.module_weight asc,m.module_id asc";
        $list = $this->Show_css->selectClear($sql);

        $tree = $this->getTree($list, 'module_id', 'father_id', 'children');

        foreach ($tree as $k => $v) {
            if ($v['father_id'] != 0) {
                unset($tree[$k]);
            }
        }

        if ($tree) {
            foreach ($tree as $key => $val) {
                if (in_array($val['module_id'], $moduleArray)) {

                    if ($val['status'] == 1) {
                        $this->pagestr .= '<p><input name="tab_list[]" value="' . $val['module_id'] . '" type="checkbox" checked="true"><span style="padding-left:' . (($val['module_level'] - 1) * 12) . 'px;"></span>' . $val['module_name'] . '</p>';
                    } else {
                        $this->pagestr .= '<p><input name="tab_list[]" value="' . $val['module_id'] . '" type="checkbox"><span style="padding-left:' . (($val['module_level'] - 1) * 12) . 'px;"></span>' . $val['module_name'] . '</p>';
                    }

                    foreach ($val['children'] as $k => $v) {
                        if (in_array($v['module_id'], $moduleArray)) {
                            if ($v['status'] == 1) {
                                $this->pagestr .= '<p><input name="tab_list[]" value="' . $v['module_id'] . '" type="checkbox" checked="true"><span style="padding-left:' . (($v['module_level'] - 1) * 12) . 'px;"></span>' . $v['module_name'] . '</p>';
                            } else {
                                $this->pagestr .= '<p><input name="tab_list[]" value="' . $v['module_id'] . '" type="checkbox"><span style="padding-left:' . (($v['module_level'] - 1) * 12) . 'px;"></span>' . $v['module_name'] . '</p>';
                            }

                            $this->get_str($v['children'], 'children');

                        }
                    }
                }
            }
        }

        $datatype = array();
        $datatype['company_id'] = $request['company_id'];
        $datatype['edition_id'] = $request['edition_id'];
        $datatype['product_id'] = $request['product_id'];
        $datatype['site_id'] = $request['site_id'];

        $this->smarty->assign("str", $this->pagestr);
        $this->smarty->assign("datatype", $datatype);
    }


    function ConfirmmoduleAction()
    {
        $request = Input('post.', '', 'trim,addslashes');

        if (isset($request['company_id']) && $request['company_id'] == '') {
            $res = array('error' => '1', 'errortip' => '集团ID必须传!', "bakfuntion" => "errormotify");
            ajax_return($res);
        }

        if (isset($request['tab_list']) && count($request['tab_list']) > 0) {

            $openArray = $request['tab_list'];
            if ($openArray) {
                foreach ($openArray as $val) {
                    $pro_moduleOne = $this->Show_css->getFieldOne("gmc_editionpro_module", "module_status", "company_id='{$request['company_id']}' and module_id='{$val}'");

                    if ($pro_moduleOne) {
                        if ($pro_moduleOne['module_status'] == 0) {
                            $data = array();
                            $data['module_status'] = 1;
                            $this->Show_css->updateData("gmc_editionpro_module", "company_id='{$request['company_id']}' and module_id='{$val}'", $data);

                        }
                    }
                }
            }

            $sql = "select module_id
                ,ifnull((select em.module_status from gmc_editionpro_module as em where em.module_id=m.module_id and em.company_id='{$request['company_id']}'),1) as status
                from imc_module as m 
                where m.product_id='{$request['product_id']}'
                having status=1
                ";
            $openList = $this->Show_css->selectClear($sql);

            if ($openList) {
                $openList = array_column($openList, 'module_id');
            } else {
                $openList = array();
            }

            $delArray = array_diff($openList, $request['tab_list']);
            if ($delArray) {
                foreach ($delArray as $val) {
                    $pro_moduleOne = $this->Show_css->getFieldOne("gmc_editionpro_module", "module_status", "company_id='{$request['company_id']}' and module_id='{$val}'");

                    if ($pro_moduleOne) {
                        if ($pro_moduleOne['module_status'] == 1) {
                            $data = array();
                            $data['module_status'] = 0;
                            $this->Show_css->updateData("gmc_editionpro_module", "company_id='{$request['company_id']}' and module_id='{$val}'", $data);

                        }
                    } else {
                        $data = array();
                        $data['company_id'] = $request['company_id'];
                        $data['module_id'] = $val;
                        $data['module_status'] = 0;
                        $this->Show_css->insertData("gmc_editionpro_module", $data);
                    }
                }
            }
        }

        $this->Recordweblog($request['site_id'], $this->Module['module_id'], $this->c, "设置权限模块");
        ajax_return(array('error' => 0, 'errortip' => "提交成功!", "type" => "replace", "bakfuntion" => "okmotify", "bakurl" => "/Company/edition?company_id={$request['company_id']}&product_id={$request['product_id']}&edition_id={$request['edition_id']}}&site_id={$request['site_id']}"));
    }

    function getTree($arr = array(), $pk = 'id', $upid = 'pid', $child = 'child')
    {
        $items = array();
        foreach ($arr as $val) {
            $items[$val[$pk]] = $val;
        }
        $tree = array();
        foreach ($items as $k => $val) {
            if (isset($items[$val[$upid]])) {
                $items[$val[$upid]][$child][] =& $items[$k];
            } else {
                $tree[] = &$items[$k];
            }
        }
        return $tree;
    }

    function get_str($arr, $field)
    {
        foreach ($arr as $v) {
            if (is_array($v[$field])) {

                if ($v['status'] == 1) {
                    $this->pagestr .= '<p><input name="tab_list[]" value="' . $v['module_id'] . '" type="checkbox" checked="true"><span style="padding-left:' . (($v['module_level'] - 1) * 12) . 'px;"></span>' . $v['module_name'] . '</p>';
                } else {
                    $this->pagestr .= '<p><input name="tab_list[]" value="' . $v['module_id'] . '" type="checkbox"><span style="padding-left:' . (($v['module_level'] - 1) * 12) . 'px;"></span>' . $v['module_name'] . '</p>';
                }

                $this->get_str($v[$field], $field);
            } else {

                if ($v['status'] == 1) {
                    $this->pagestr .= '<p><input name="tab_list[]" value="' . $v['module_id'] . '" type="checkbox" checked="true"><span style="padding-left:' . (($v['module_level'] - 1) * 12) . 'px;"></span>' . $v['module_name'] . '</p>';
                } else {
                    $this->pagestr .= '<p><input name="tab_list[]" value="' . $v['module_id'] . '" type="checkbox"><span style="padding-left:' . (($v['module_level'] - 1) * 12) . 'px;"></span>' . $v['module_name'] . '</p>';
                }
            }
        }
    }

    //活动编辑
    function LineRechargeView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        $smarty->assign("act", "LineRecharge");

        $companyOne = $Show_css->getOne("gmc_company", "company_id='{$request['company_id']}'");
        $smarty->assign("dataVar", $companyOne);

        $this->Viewhtm = $this->router->getController() . "/" . "LineRecharge.htm";
    }

    //活动编辑
    function LineRechargeAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $Show_css = $this->Show_css;
        $balanceOne = $this->Show_css->getOne('tkl_balance_log', "company_id = '{$request['company_id']}'", " ORDER BY log_id DESC");
        if (!$balanceOne) {
            $balanceOne['log_finalamount'] = 0;
        }

        $data = array();
        $data['company_id'] = $request['company_id'];
        $data['log_class'] = '0';
        $data['log_playname'] = $request['log_playname'];
        $data['log_playclass'] = '+';
        $data['log_fromamount'] = $balanceOne['log_finalamount'];
        $data['log_playamount'] = $request['log_playamount'];
        $data['log_finalamount'] = $balanceOne['log_finalamount'] + $request['log_playamount'];
        $data['log_reason'] = $request['log_reason'];
        $data['log_time'] = strtotime($request['log_time']);
        if ($Show_css->insertData("tkl_balance_log", $data)) {
            $this->Recordweblog($request['site_id'], $this->Module['module_id'], $this->c, "集团网课充值账户，ID:{$request['company_id']}");
            ajax_return(array('error' => 0, 'errortip' => "充值成功!", "bakfuntion" => "successFromTip"));
        } else {
            ajax_return(array('error' => 1, 'errortip' => "充值失败!", "bakfuntion" => "dangerFromTip"));
        }
    }

    //活动编辑
    function EditView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        $smarty->assign("act", "Edit");

        $companyOne = $Show_css->getOne("gmc_company", "company_id='{$request['company_id']}'");
        $smarty->assign("dataVar", $companyOne);

        $this->Viewhtm = $this->router->getController() . "/" . "Edit.htm";
    }

    //活动编辑
    function EditAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $Show_css = $this->Show_css;
        if ($this->Show_css->getOne('gmc_company', "company_code = '{$request['company_code']}' and company_id <> '{$request['company_id']}'")) {
            $res = array('error' => '1', 'errortip' => '企业编号已存在!', "bakfuntion" => "errormotify");
            ajax_return($res);
        }

        $data = array();
        $data['company_name'] = $request['company_name'];
        $data['company_notice'] = $request['company_notice'];
        $data['company_code'] = $request['company_code'];
        $data['company_shortname'] = $request['company_shortname'];
        $data['company_cnname'] = $request['company_cnname'];
        $data['company_enname'] = $request['company_enname'];
        $data['company_name'] = $request['company_name'];
        $data['company_position'] = $request['company_position'];
        $data['company_mobile'] = $request['company_mobile'];
        $data['company_address'] = $request['company_address'];
        $data['company_logo'] = $request['company_logo'];

        $data['company_language'] = $request['company_language'];
        $data['company_iseditimportcourse'] = $request['company_iseditimportcourse'];//导入课程是否允许修改
        $data['company_issupportpos'] = $request['company_issupportpos'];
        $data['company_issupportptc'] = $request['company_issupportptc'];
        $data['company_issupportqr'] = $request['company_issupportqr'];

        $data['company_email'] = $request['company_email'];
        $data['company_phone'] = $request['company_phone'];
        $data['company_fax'] = $request['company_fax'];
        $data['company_homeurl'] = $request['company_homeurl'];
        $data['company_brief'] = $request['company_brief'];
        $data['company_status'] = $request['company_status'];
        $data['company_ismajor'] = $request['company_ismajor'];
        $data['company_isinvoice'] = $request['company_isinvoice'];
        $data['company_isshowhour'] = $request['company_isshowhour'];
        $data['company_isshowpay'] = $request['company_isshowpay'];
        $data['company_issign'] = $request['company_issign'];
        $data['company_isstock'] = $request['company_isstock'];
        $data['company_logoutday'] = $request['company_logoutday'];
        $data['company_updatetime'] = time();

        if ($Show_css->updateData("gmc_company", "company_id = '{$request['company_id']}'", $data)) {
            if (!$this->Show_css->getFieldOne('smc_staffer', "staffer_id", "company_id = '{$request['company_id']}' and account_class = '1'")) {
                $user = array();
                $user['company_id'] = $request['company_id'];
                $user['account_class'] = "1";
                $user['staffer_branch'] = $request['company_code'];
                $user['staffer_cnname'] = "男";
                $user['staffer_cnname'] = "管理员";
                $user['staffer_enname'] = "Administrator";
                $user['staffer_mobile'] = "admin{$request['company_code']}";
                $user['staffer_pass'] = md5($request['company_code']);
                $user['staffer_bakpass'] = $request['company_code'];
                $user['staffer_createtime'] = time();
                $user['staffer_updatetime'] = time();
                $Show_css->insertData("smc_staffer", $user);
            }

            $this->Recordweblog($request['site_id'], $this->Module['module_id'], $this->c, "修改教育集团数据，ID:{$request['company_id']}");
            ajax_return(array('error' => 0, 'errortip' => "提交成功!", "bakfuntion" => "successFromTip"));
        } else {
            ajax_return(array('error' => 1, 'errortip' => "提交失败!", "bakfuntion" => "dangerFromTip"));
        }
    }

    //添加客户
    function AddView()
    {
        $smarty = $this->smarty;
        $smarty->assign("act", "Add");

        $this->Viewhtm = $this->router->getController() . "/" . "Edit.htm";
    }

    //新增客户
    function AddAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $Show_css = $this->Show_css;
        if ($this->Show_css->getOne('gmc_company', "company_code = '{$request['company_code']}'")) {
            $res = array('error' => '1', 'errortip' => '企业编号已存在!', "bakfuntion" => "errormotify");
            ajax_return($res);
        }

        $data = array();
        $data['company_name'] = $request['company_name'];
        $data['company_notice'] = $request['company_notice'];
        $data['company_code'] = $request['company_code'];
        $data['company_shortname'] = $request['company_shortname'];
        $data['company_cnname'] = $request['company_cnname'];
        $data['company_enname'] = $request['company_enname'];
        $data['company_name'] = $request['company_name'];
        $data['company_position'] = $request['company_position'];
        $data['company_mobile'] = $request['company_mobile'];
        $data['company_address'] = $request['company_address'];
        $data['company_logo'] = $request['company_logo'];

        $data['company_language'] = $request['company_language'];
        $data['company_isinvoice'] = $request['company_isinvoice'];
        $data['company_iseditimportcourse'] = $request['company_iseditimportcourse'];//导入课程是否允许修改
        $data['company_issupportpos'] = $request['company_issupportpos'];
        $data['company_issupportptc'] = $request['company_issupportptc'];
        $data['company_issupportqr'] = $request['company_issupportqr'];

        $data['company_email'] = $request['company_email'];
        $data['company_phone'] = $request['company_phone'];
        $data['company_fax'] = $request['company_fax'];
        $data['company_homeurl'] = $request['company_homeurl'];
        $data['company_brief'] = $request['company_brief'];
        $data['company_status'] = $request['company_status'];
        $data['company_ismajor'] = $request['company_ismajor'];
        $data['company_isinvoice'] = $request['company_isinvoice'];
        $data['company_isshowhour'] = $request['company_isshowhour'];
        $data['company_isshowpay'] = $request['company_isshowpay'];
        $data['company_issign'] = $request['company_issign'];
        $data['company_isstock'] = $request['company_isstock'];
        $data['company_logoutday'] = $request['company_logoutday'];
        $data['company_addtime'] = time();

        if ($cid = $Show_css->insertData("gmc_company", $data)) {
            $user = array();
            $user['company_id'] = $cid;
            $user['account_class'] = "1";
            $user['staffer_branch'] = $request['company_code'];
            $user['staffer_cnname'] = "男";
            $user['staffer_cnname'] = "管理员";
            $user['staffer_enname'] = "Administrator";
            $user['staffer_mobile'] = "admin{$request['company_code']}";
            $user['staffer_pass'] = md5($request['company_code']);
            $user['staffer_bakpass'] = $request['company_code'];
            $user['staffer_createtime'] = time();
            $user['staffer_updatetime'] = time();
            $Show_css->insertData("smc_staffer", $user);
            $companies = array();
            $companies['company_id'] = $cid;
            $companies['companies_cnname'] = $request['company_cnname'];
            $companies['companies_createtime'] = time();
            $Show_css->insertData("gmc_code_companies", $companies);

            $a = $Show_css->selectClear("select reason_code,reason_note,stuchange_code from smc_code_stuchange_reason where company_id = '99999999'");
            foreach ($a as &$val) {
                $data = array();
                $data['reason_code'] = $val['reason_code'];
                $data['reason_note'] = $val['reason_note'];
                $data['stuchange_code'] = $val['stuchange_code'];
                $data['company_id'] = $cid;
                $Show_css->insertData("smc_code_stuchange_reason", $data);
            }

            $b = $Show_css->selectClear("select coursetimes_cnname,coursetimes_nums from smc_code_coursetimes where company_id = '99999999'");
            foreach ($b as &$val) {
                $data = array();
                $data['coursetimes_cnname'] = $val['coursetimes_cnname'];
                $data['coursetimes_nums'] = $val['coursetimes_nums'];
                $data['company_id'] = $cid;
                $Show_css->insertData("smc_code_coursetimes", $data);
            }

            $this->Recordweblog($request['site_id'], $this->Module['module_id'], $this->c, "新增教育集团数据");
            ajax_return(array('error' => 0, 'errortip' => "提交成功!", "bakfuntion" => "successFromTip", "bakurl" => "/{$this->u}?site_id={$request['site_id']}"));
        } else {
            ajax_return(array('error' => 1, 'errortip' => "提交失败!", "bakfuntion" => "dangerFromTip"));
        }
    }

    function WorklogView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        if (!isset($request['p'])) {
            $p = '1';
        } else {
            $p = $request['p'];
        }

        $companyOne = $Show_css->getOne("gmc_company", "company_id='{$request['company_id']}'");
        $datawhere = "1";
        $pageurl = "/{$this->u}/{$this->t}?";

        if ($request['site_id']) {
            $pageurl .= "&site_id={$request['site_id']}";
        }

        $datatype = array();
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (u.staffer_branch like '%{$request['keyword']}%' or u.staffer_cnname like '%{$request['keyword']}%' or l.worklog_module like '%{$request['keyword']}%')";
            $pageurl .= "&keyword={$request['keyword']}";
            $datatype['keyword'] = $request['keyword'];
        }

        if (isset($companyOne['company_id']) && $companyOne['company_id'] !== '0') {
            $datawhere .= " and l.company_id = '{$companyOne['company_id']}'";
            $pageurl .= "&company_id={$companyOne['company_id']}";
            $datatype['company_id'] = $companyOne['company_id'];
        } else {
            $datatype['company_id'] = "0";
        }

        if (isset($request['staffer_id']) && $request['staffer_id'] !== '0') {
            $datawhere .= " and l.staffer_id = '{$request['staffer_id']}'";
            $pageurl .= "&staffer_id={$request['staffer_id']}";
            $datatype['staffer_id'] = $request['staffer_id'];
        } else {
            $datatype['staffer_id'] = "0";
        }

        $sql = "SELECT l.*,u.staffer_cnname,u.staffer_enname FROM smc_staffer_worklog as l LEFT JOIN smc_staffer as u ON u.staffer_id = l.staffer_id where {$datawhere} order by l.worklog_id DESC";

        $db_nums = $Show_css->select("SELECT COUNT(l.worklog_id) as countnums FROM smc_staffer_worklog as l LEFT JOIN smc_staffer as u ON u.staffer_id = l.staffer_id where {$datawhere}");//相关条件下的总记录数COUNT(*)
        $allnum = $db_nums[0]['countnums'];

        $datalist = $Show_css->dbwherePage($sql, $allnum, '10', $pageurl . '&p=', $p, '10', '2');

        $smarty->assign("pagelist", $datalist['pages']);//筛选信息

        $smarty->assign("dataList", $datalist['cont']);
        $smarty->assign("datatype", $datatype);
    }

    function StafferView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;
        $datatype = array();
        $datatype['company_id'] = $request['company_id'];
        $companyOne = $Show_css->getOne("gmc_company", "company_id='{$request['company_id']}'");
        $smarty->assign("companyOne", $companyOne);

        $request = Input('get.', '', 'trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        if (!isset($request['p'])) {
            $p = '1';
        } else {
            $p = $request['p'];
        }

        $datawhere = "s.company_id='{$request['company_id']}'";
        $pageurl = "/{$this->u}/{$this->t}?company_id={$request['company_id']}&site_id={$request['site_id']}";


        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (s.staffer_cnname like '%{$request['keyword']}%' or s.staffer_enname like '%{$request['keyword']}%' or s.staffer_mobile like '%{$request['keyword']}%' or s.staffer_branch like '%{$request['keyword']}%')";
            $pageurl .= "&keyword={$request['keyword']}";
            $datatype['keyword'] = $request['keyword'];
        }


        $sql = "SELECT s.* FROM smc_staffer as s LEFT JOIN gmc_company AS c ON c.company_id = s.company_id where {$datawhere} order by s.staffer_id DESC";

        $db_nums = $Show_css->selectOne("SELECT COUNT(s.staffer_id) as countnums FROM smc_staffer as s LEFT JOIN gmc_company AS c ON c.company_id = s.company_id where {$datawhere}");
        $allnum = $db_nums['countnums'];

        $datalist = $Show_css->dbwherePage($sql, $allnum, '30', $pageurl . '&p=', $p, '10', '2');
        $smarty->assign("pagelist", $datalist['pages']);//筛选信息
        $smarty->assign("dataList", $datalist['cont']);
        $smarty->assign("datatype", $datatype);

        $this->Viewhtm = $this->router->getController() . "/" . "Staffer.htm";
    }

    function WxSendView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;
        $datatype = array();
        $datatype['company_id'] = $request['company_id'];
        $companyOne = $Show_css->getOne("gmc_company", "company_id='{$request['company_id']}'");
        $smarty->assign("companyOne", $companyOne);

        $request = Input('get.', '', 'trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        if (!isset($request['p'])) {
            $p = '1';
        } else {
            $p = $request['p'];
        }

        $datawhere = "s.company_id='{$request['company_id']}'";
        $pageurl = "/{$this->u}/{$this->t}?company_id={$request['company_id']}&site_id={$request['site_id']}";


        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (s.student_cnname like '%{$request['keyword']}%' or s.student_branch like '%{$request['keyword']}%' or p.parenter_mobile like '%{$request['keyword']}%')";
            $pageurl .= "&keyword={$request['keyword']}";
            $datatype['keyword'] = $request['keyword'];
        }


        $sql = "
            SELECT
                s.student_cnname,
                s.student_branch,
                p.parenter_mobile,
                l.log_errmsg,
                l.log_type,
                l.log_day,
                l.log_status 
            FROM
                scptc_wxsend_log AS l
                LEFT JOIN smc_student AS s ON l.student_id = s.student_id
                LEFT JOIN smc_parenter AS p ON l.parenter_id = p.parenter_id
                where {$datawhere}
                order by l.log_id DESC";

        $db_nums = $Show_css->selectOne("SELECT COUNT(l.log_id) as countnums FROM scptc_wxsend_log as l LEFT JOIN smc_student AS s ON l.student_id = s.student_id LEFT JOIN smc_parenter AS p ON l.parenter_id = p.parenter_id where {$datawhere}");
        $allnum = $db_nums['countnums'];

        $datalist = $Show_css->dbwherePage($sql, $allnum, '30', $pageurl . '&p=', $p, '10', '2');
        $smarty->assign("pagelist", $datalist['pages']);//筛选信息
        $smarty->assign("dataList", $datalist['cont']);
        $smarty->assign("datatype", $datatype);

        $this->Viewhtm = $this->router->getController() . "/" . "WxSend.htm";
    }

    function DatameansView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->c = "ajax";
        $dataList = $this->Show_css->selectClear("SELECT c.coursetype_cnname,c.coursetype_branch,c.coursetype_isopenclass,c.coursetype_remk
FROM smc_code_coursetype AS c WHERE c.company_id = '79055'");
        if (count($dataList) > 0) {
            foreach ($dataList as $dataOne) {
                $itemOne = $this->Show_css->getFieldOne("smc_code_coursetype", "coursetype_id",
                    "coursetype_branch = '{$dataOne['coursetype_branch']}' and company_id='{$request['company_id']}'");//班级信息
                if (!$itemOne) {
                    $data = array();
                    $data['company_id'] = $request['company_id'];
                    $data['coursetype_branch'] = $dataOne['coursetype_branch'];
                    $data['coursetype_cnname'] = $dataOne['coursetype_cnname'];
                    $this->Show_css->insertData("smc_code_coursetype", $data);
                }
            }
        }
        $dataList = $this->Show_css->selectClear("SELECT c.coursetype_id,c.coursecat_cnname,c.coursecat_branch,c.coursecat_iscrmadded,t.coursetype_cnname
FROM smc_code_coursecat AS c,smc_code_coursetype as t WHERE t.coursetype_id = c.coursetype_id and c.company_id = '79055'");
        if (count($dataList) > 0) {
            foreach ($dataList as $dataOne) {
                $itemOne = $this->Show_css->getFieldOne("smc_code_coursecat", "coursecat_id", "coursecat_branch = '{$dataList['coursecat_branch']}' and company_id='{$request['company_id']}'");//班级信息
                if (!$itemOne) {
                    $coursetypeOne = $this->Show_css->getFieldOne("smc_code_coursetype", "coursetype_id", "coursetype_cnname = '{$dataOne['coursetype_cnname']}' and company_id='{$request['company_id']}'");
                    $data = array();
                    $data['company_id'] = $request['company_id'];
                    $data['coursetype_id'] = $coursetypeOne['coursetype_id'];
                    $data['coursecat_branch'] = $dataOne['coursecat_branch'];
                    $data['coursecat_cnname'] = $dataOne['coursecat_cnname'];
                    $data['coursecat_iscrmadded'] = '1';
                    $this->Show_css->insertData("smc_code_coursecat", $data);
                }
            }
        }

        $dataList = $this->Show_css->selectClear("SELECT c.course_cnname,c.course_branch,c.course_classnum,s.coursecat_branch FROM smc_course AS c,smc_code_coursecat as s
WHERE s.coursecat_id = c.coursecat_id and c.company_id = '79055' and c.course_status = '1'");
        if (count($dataList) > 0) {
            foreach ($dataList as $dataOne) {
                $itemOne = $this->Show_css->getFieldOne("smc_code_coursecat", "coursecat_id", "coursecat_branch = '{$dataList['coursecat_branch']}' and company_id='{$request['company_id']}'");//班级信息
                if (!$itemOne) {
                    $coursecatOne = $this->Show_css->getFieldOne("smc_code_coursecat", "coursecat_id,coursetype_id", "coursecat_branch = '{$dataOne['coursecat_branch']}' and company_id='{$request['company_id']}'");
                    $data = array();
                    $data['company_id'] = $request['company_id'];
                    $data['coursetype_id'] = $coursecatOne['coursetype_id'];
                    $data['coursecat_id'] = $coursecatOne['coursecat_id'];
                    $data['course_branch'] = $dataOne['course_branch'];
                    $data['course_cnname'] = $dataOne['course_cnname'];
                    $data['course_classnum'] = $dataOne['course_classnum'];
                    $data['course_createtime'] = time();
                    $data['course_updatatime'] = time();
                    $this->Show_css->insertData("smc_course", $data);
                }
            }
        }

        $dataList = $this->Show_css->selectClear("SELECT c.coursetimes_cnname,c.coursetimes_nums FROM smc_code_coursetimes AS c
WHERE c.company_id = '79055'");
        if (count($dataList) > 0) {
            foreach ($dataList as $dataOne) {
                $itemOne = $this->Show_css->getFieldOne("smc_code_coursetimes", "coursetimes_id", "coursetimes_cnname = '{$dataList['coursetimes_cnname']}' and company_id='{$request['company_id']}'");//班级信息
                if (!$itemOne) {
                    $data = array();
                    $data['company_id'] = $request['company_id'];
                    $data['coursetimes_cnname'] = $dataOne['coursetimes_cnname'];
                    $data['coursetimes_nums'] = $dataOne['coursetimes_nums'];
                    $this->Show_css->insertData("smc_code_coursetimes", $data);
                }
            }
        }


        $dataList = $this->Show_css->selectClear("SELECT c.teachtype_code,c.teachtype_name,c.teachtype_remk FROM smc_code_teachtype AS c
WHERE c.company_id = '79055'");
        if (count($dataList) > 0) {
            foreach ($dataList as $dataOne) {
                $itemOne = $this->Show_css->getFieldOne("smc_code_teachtype", "teachtype_id", "teachtype_code = '{$dataList['teachtype_code']}' and company_id='{$request['company_id']}'");//班级信息
                if (!$itemOne) {
                    $data = array();
                    $data['company_id'] = $request['company_id'];
                    $data['teachtype_code'] = $dataOne['teachtype_code'];
                    $data['teachtype_name'] = $dataOne['teachtype_name'];
                    $data['teachtype_remk'] = $dataOne['teachtype_remk'];
                    $this->Show_css->insertData("smc_code_teachtype", $data);
                }
            }
        }
        ajax_return(array('error' => 0, 'errortip' => "班种更新完毕!", "bakfuntion" => "okmotify"));
    }

    //重置集团密码默认为 123456
    function ResetPwdAction()
    {
        $list_id = Input('get.id', 0);
        $stfferOne = $this->Show_css->selectOne("select staffer_id from smc_staffer WHERE company_id = '{$list_id}' and account_class = '1' ORDER BY staffer_id ASC limit 0,1");
        if ($this->Show_css->updateData("smc_staffer", "company_id='{$list_id}' and staffer_id = '{$stfferOne['staffer_id']}' ", array('staffer_pass' => md5('123456'), 'staffer_bakpass' => '123456', 'staffer_updatetime' => time()))) {
            ajax_return(array('error' => 0, 'errortip' => "提交成功!", "bakfuntion" => "okmotify"));
        } else {
            ajax_return(array('error' => 1, 'errortip' => "新增失败!", "bakfuntion" => "errormotify"));
        }
    }

    //删除报名活动
    function DelAction()
    {
        $list_id = Input('get.id', 0);

        if ($this->Show_css->delData('gmc_company', "company_id='{$list_id}'")) {

            ajax_return(array('error' => 0, 'errortip' => "提交成功!", "bakfuntion" => "okmotify"));
        } else {
            ajax_return(array('error' => 1, 'errortip' => "新增失败!", "bakfuntion" => "errormotify"));
        }
    }

    //导入教材、班组、班种、班别
    function ImportTeachingView()
    {

    }

    function ImportTeachingExcelView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        if ($_FILES['uploadFile']['name'] == '' || $_FILES['uploadFile']['error'] == '1') {
            $PlayInfoVar = array();
            $PlayInfoVar['course_branch'] = "导入出错";
            $PlayInfoVar['error'] = "1";
            $PlayInfoVar['errortip'] = "文件不存在";
            $PlayInfo[] = $PlayInfoVar;
            $this->smarty->assign("PlayInfo", $PlayInfo);
            exit;
        }

        $fileType = array('xls', 'csv', 'xlsx');
        $uploadfile = new \Webfile($_FILES['uploadFile'], $files_dir = '../static/file', $size = 2097152 * 10, $fileType);
        $up_file = $uploadfile->upload();

        $PlayInfo = array();
        if ($up_file && $uploadfile->updatastatus) {
            $ExeclName = array();
            $ExeclName['集团'] = "company_id";
            $ExeclName['课程别名称'] = "course_cnname";
            $ExeclName['课程别编号'] = "course_branch";
            $ExeclName['课次数'] = "course_classnum";
            $ExeclName['所属班种'] = "coursecat_cnname";
            $ExeclName['班种编号'] = "coursecat_branch";
            $ExeclName['所属班组'] = "coursetype_cnname";
            $ExeclName['班组编号'] = "coursetype_branch";
            $ExeclName['课程状态'] = "course_status";
            $ExeclName['上课方式'] = "course_inclasstype";
            $WorkerList = execl_to_array($up_file, $ExeclName);
            unset($WorkerList[0]);

            if ($WorkerList) {
                foreach ($WorkerList as $WorkerrVar) {
                    if ($WorkerrVar['company_id'] !== '' && $WorkerrVar['coursetype_cnname'] !== '' && $WorkerrVar['coursetype_branch'] !== '') {
                        $workersList[] = $WorkerrVar;
                    } else {
                        $PlayInfoVar = array();
                        $PlayInfoVar['company_id'] = $WorkerrVar['company_id'];
                        $PlayInfoVar['coursetype_cnname'] = $WorkerrVar['coursetype_cnname'];
                        $PlayInfoVar['coursecat_cnname'] = $WorkerrVar['coursecat_cnname'];
                        $PlayInfoVar['course_cnname'] = $WorkerrVar['course_cnname'];
                        $PlayInfoVar['error'] = "1";
                        $PlayInfoVar['errortip'] = "导入信息不完整";
                        $PlayInfo[] = $PlayInfoVar;
                    }
                }
            }
            if (count($workersList) > 1000) {
                ajax_return(array('error' => 1, 'errortip' => "导入数量不能大于1000!", "bakfuntion" => "errormotify"));
            }
            if ($workersList) {
                foreach ($workersList as $workersVar) {
                    $companyOne = $this->Show_css->getFieldOne("gmc_company", "company_id", "company_id='{$workersVar['company_id']}'");
                    if (isset($companyOne['company_id']) && $companyOne['company_id'] != '') {
                        $PlayInfoVar = array();
                        $PlayInfoVar['company_id'] = $workersVar['company_id'];
                        $PlayInfoVar['coursetype_cnname'] = $workersVar['coursetype_cnname'];
                        $PlayInfoVar['coursecat_cnname'] = $workersVar['coursecat_cnname'];
                        $PlayInfoVar['course_cnname'] = $workersVar['course_cnname'];

                        //班组数据
                        $coursetypeOne = $this->Show_css->getFieldOne("smc_code_coursetype", "coursetype_id", "company_id='{$workersVar['company_id']}' and coursetype_branch = '{$workersVar['coursetype_branch']}'");
                        if (!$coursetypeOne) {
                            //班组数据
                            $coursetypedata = array();
                            $coursetypedata['company_id'] = $workersVar['company_id'];
                            $coursetypedata['coursetype_isimport'] = 1;
                            $coursetypedata['coursetype_cnname'] = $workersVar['coursetype_cnname'];
                            $coursetypedata['coursetype_branch'] = $workersVar['coursetype_branch'];
                            $coursetype_id = $this->Show_css->insertData('smc_code_coursetype', $coursetypedata);
                        } else {
                            $PlayInfoVar['coursetype_cnname'] = $workersVar['coursetype_cnname'] . "--(已存在)";
                            $coursetype_id = $coursetypeOne['coursetype_id'];
                        }

                        //班种数据
                        $coursecatOne = $this->Show_css->getFieldOne("smc_code_coursecat", "coursecat_id", "company_id='{$workersVar['company_id']}' and coursecat_branch = '{$workersVar['coursecat_branch']}'");
                        if (!$coursecatOne && $coursetype_id > 0 && $workersVar['coursetype_branch'] != '') {
                            //班种数据
                            $coursecatdata = array();
                            $coursecatdata['company_id'] = $workersVar['company_id'];
                            $coursecatdata['coursecat_isimport'] = 1;
                            $coursecatdata['coursetype_id'] = $coursetype_id;//班组
                            $coursecatdata['coursecat_cnname'] = $workersVar['coursecat_cnname'];
                            $coursecatdata['coursecat_branch'] = $workersVar['coursecat_branch'];
                            $coursecat_id = $this->Show_css->insertData('smc_code_coursecat', $coursecatdata);
                        } else {
                            if ($workersVar['coursecat_branch'] != '') {
                                $PlayInfoVar['coursecat_cnname'] = $workersVar['coursecat_cnname'] . "--(已存在)";
                            }
                            $coursecat_id = $coursecatOne['coursecat_id'];
                        }

                        //班别数据
                        $courseOne = $this->Show_css->getFieldOne("smc_course", "course_id", "company_id='{$workersVar['company_id']}' and course_branch = '{$workersVar['course_branch']}'");
                        if (!$courseOne && $workersVar['course_branch'] != '' && $coursetype_id > 0 && $coursecat_id > 0) {
                            //班别数据
                            $coursedata = array();
                            $coursedata['company_id'] = $workersVar['company_id'];
                            $coursedata['course_isimport'] = 1;
                            $coursedata['coursetype_id'] = $coursetype_id;//班组
                            $coursedata['coursecat_id'] = $coursecat_id;//班种
                            $coursedata['course_cnname'] = $workersVar['course_cnname'];
                            $coursedata['course_branch'] = $workersVar['course_branch'];
                            $coursedata['course_classnum'] = $workersVar['course_classnum'];//课次数
                            $coursedata['course_status'] = $workersVar['course_status'];//课程状态
                            $coursedata['course_inclasstype'] = $workersVar['course_inclasstype'];//上课方式
                            $course_id = $this->Show_css->insertData('smc_course', $coursedata);
                        }
                        if ($course_id > '0' || ($coursecat_id > 0 && $workersVar['course_branch'] == '') || ($coursetype_id > 0 && $workersVar['coursecat_branch'] == '' && $workersVar['course_branch'] == '')) {
                            $PlayInfoVar['error'] = "0";
                            $PlayInfoVar['errortip'] = "导入成功";
                        } else {
                            $PlayInfoVar['error'] = "1";
                            $PlayInfoVar['errortip'] = "导入失败2";
                        }
                    } else {
                        $PlayInfoVar = array();
                        $PlayInfoVar['company_id'] = $workersVar['company_id'];
                        $PlayInfoVar['coursetype_cnname'] = $workersVar['coursetype_cnname'];
                        $PlayInfoVar['coursecat_cnname'] = $workersVar['coursecat_cnname'];
                        $PlayInfoVar['course_cnname'] = $workersVar['course_cnname'];
                        $PlayInfoVar['error'] = "1";
                        $PlayInfoVar['errortip'] = "导入失败1";
                    }
                    $PlayInfo[] = $PlayInfoVar;
                }
            } else {
                $PlayInfoVar = array();
                $PlayInfoVar['company_id'] = '文件为空';
                $PlayInfoVar['coursetype_cnname'] = '文件为空';
                $PlayInfoVar['coursecat_cnname'] = '文件为空';
                $PlayInfoVar['course_cnname'] = '文件为空';
                $PlayInfoVar['error'] = "1";
                $PlayInfoVar['errortip'] = "文件为空";
                $PlayInfo[] = $PlayInfoVar;
            }
        } else {
            $PlayInfoVar = array();
            $PlayInfoVar['company_id'] = '文件不存在';
            $PlayInfoVar['coursetype_cnname'] = '文件不存在';
            $PlayInfoVar['coursecat_cnname'] = '文件不存在';
            $PlayInfoVar['course_cnname'] = '文件不存在';
            $PlayInfoVar['error'] = "1";
            $PlayInfoVar['errortip'] = "文件不存在";
            $PlayInfo[] = $PlayInfoVar;
        }
        $this->smarty->assign("PlayInfo", $PlayInfo);
    }

    //导入职工
    function importStafferView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $datatype = array();
        $datatype['company_id'] = $request['company_id'];
        $this->smarty->assign("datatype", $datatype);

    }

    function importStafferExcelView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        if ($_FILES['uploadFile']['name'] == '' || $_FILES['uploadFile']['error'] == '1') {
            $PlayInfoVar = array();
            $PlayInfoVar['course_branch'] = "导入出错";
            $PlayInfoVar['error'] = "1";
            $PlayInfoVar['errortip'] = "文件不存在";
            $PlayInfo[] = $PlayInfoVar;
            $this->smarty->assign("PlayInfo", $PlayInfo);
            exit;
        }

        $fileType = array('xls', 'csv', 'xlsx');
        $uploadfile = new \Webfile($_FILES['uploadFile'], $files_dir = '../static/file', $size = 2097152 * 10, $fileType);
        $up_file = $uploadfile->upload();

        $PlayInfo = array();
        if ($up_file && $uploadfile->updatastatus) {
            $ExeclName = array();
            $ExeclName['职工中文名'] = "staffer_cnname";
            $ExeclName['职工英文名'] = "staffer_enname";
            $ExeclName['员工编号'] = "staffer_employeepid";
            $ExeclName['性别'] = "staffer_sex";
            $ExeclName['手机号'] = "staffer_mobile";
            $ExeclName['职工邮箱'] = "staffer_email";
            $ExeclName['入职时间'] = "staffer_jointime";
            $WorkerList = execl_to_array($up_file, $ExeclName);
            unset($WorkerList[0]);

            if ($WorkerList) {
                foreach ($WorkerList as $WorkerrVar) {
                    if ($WorkerrVar['staffer_mobile'] !== '' && $WorkerrVar['staffer_mobile'] !== '') {
                        $workersList[] = $WorkerrVar;
                    } else {
                        $PlayInfoVar = array();
                        $PlayInfoVar['staffer_cnname'] = $WorkerrVar['staffer_cnname'];
                        $PlayInfoVar['error'] = "1";
                        $PlayInfoVar['errortip'] = "导入媒体信息不完整";
                        $PlayInfo[] = $PlayInfoVar;
                    }
                }
            }
            if (count($workersList) > 1000) {
                ajax_return(array('error' => 1, 'errortip' => "导入数量不能大于1000!", "bakfuntion" => "errormotify"));
            }
            if ($workersList) {
                foreach ($workersList as $workersVar) {
                    if (isset($workersVar['staffer_mobile']) && $workersVar['staffer_mobile'] != '') {
                        $data = array();
                        $stafferOne = $this->Show_css->getFieldOne("smc_staffer", "staffer_branch", "staffer_mobile='{$workersVar['staffer_mobile']}' and company_id='{$request['company_id']}'");


                        $like = date("Ymd", time());
                        $stuInfo = $this->Show_css->selectOne("select staffer_branch from smc_staffer where staffer_branch like '{$like}%' order by staffer_branch DESC limit 0,1");
                        if ($stuInfo) {
                            $data['staffer_branch'] = $stuInfo['staffer_branch'] + 1;
                        } else {
                            $data['staffer_branch'] = $like . '00001';
                        }
                        if (!$stafferOne) {
                            $PlayInfoVar['staffer_cnname'] = $workersVar['staffer_cnname'];
                            $PlayInfoVar['staffer_enname'] = $workersVar['staffer_enname'];
                            $PlayInfoVar['staffer_sex'] = $workersVar['staffer_sex'];
                            $PlayInfoVar['staffer_mobile'] = $workersVar['staffer_mobile'];
                            $PlayInfoVar['staffer_email'] = $workersVar['staffer_email'];
                            if ($WorkerrVar['staffer_jointime'] !== "") {
                                $PlayInfoVar['staffer_jointime'] = gmdate('Y-m-d', \PHPExcel_Shared_Date::ExcelToPHP($WorkerrVar['staffer_jointime']));
                            }
                            $PlayInfoVar['company_id'] = $request['company_id'];

                            $data['staffer_cnname'] = $workersVar['staffer_cnname'];
                            $data['staffer_enname'] = $workersVar['staffer_enname'];
                            $data['staffer_sex'] = $workersVar['staffer_sex'];
                            $data['staffer_mobile'] = $workersVar['staffer_mobile'];
                            $data['staffer_email'] = $workersVar['staffer_email'];
                            $data['staffer_jointime'] = $workersVar['staffer_jointime'];
                            $data['staffer_employeepid'] = $workersVar['staffer_employeepid'];
                            if ($WorkerrVar['staffer_jointime'] !== "") {
                                $data['staffer_jointime'] = gmdate('Y-m-d', \PHPExcel_Shared_Date::ExcelToPHP($WorkerrVar['staffer_jointime']));
                            }
                            $data['company_id'] = $request['company_id'];
                            $data['staffer_bakpass'] = substr($workersVar['staffer_mobile'], -6);
                            $data['staffer_pass'] = md5($data['staffer_bakpass']);

                            $data['staffer_createtime'] = time();
                            $employeepidOne = $this->Show_css->getOne("smc_staffer", "company_id='{$request['company_id']}'and staffer_employeepid='{$workersVar['staffer_employeepid']}' and  staffer_employeepid <>'' ");

//                            debug($data);die;
                            if ($employeepidOne) {
                                $PlayInfoVar['staffer_cnname'] = $workersVar['staffer_cnname'];
                                $PlayInfoVar['error'] = "1";
                                $PlayInfoVar['errortip'] = "原职工编号重复";
                            } else {
                                if ($student_id = $this->Show_css->insertData("smc_staffer", $data)) {
                                    $PlayInfoVar['error'] = "0";
                                    $PlayInfoVar['errortip'] = "导入成功";
                                } else {
                                    $PlayInfoVar['staffer_cnname'] = $workersVar['staffer_cnname'];
                                    $PlayInfoVar['error'] = "1";
                                    $PlayInfoVar['errortip'] = "导入失败1";
                                }
                            }
                        } else {
                            $PlayInfoVar['staffer_cnname'] = $workersVar['staffer_cnname'];
                            $PlayInfoVar['error'] = "1";
                            $PlayInfoVar['errortip'] = "职工手机号重复";


                        }
                    } else {
                        $PlayInfoVar['staffer_cnname'] = $workersVar['staffer_cnname'];
                        $PlayInfoVar['error'] = "1";
                        $PlayInfoVar['errortip'] = "导入失败2";
                    }

                    $PlayInfo[] = $PlayInfoVar;
                }
            } else {
                $PlayInfoVar = array();
                $PlayInfoVar['staffer_cnname'] = '文件为空';
                $PlayInfoVar['error'] = "1";
                $PlayInfoVar['errortip'] = "文件为空";
                $PlayInfo[] = $PlayInfoVar;
            }

        } else {
            $PlayInfoVar = array();
            $PlayInfoVar['staffer_cnname'] = "导入出错";
            $PlayInfoVar['error'] = "1";
            $PlayInfoVar['errortip'] = "文件不存在";
            $PlayInfo[] = $PlayInfoVar;
        }

        $this->smarty->assign("PlayInfo", $PlayInfo);

    }

    function changeToEasyAction()
    {
        $request = Input('get.', '', 'trim,addslashes');

        if ($request['company_id'] == '8888' || $request['company_id'] == '1001') {
            ajax_return(array('error' => '1', 'errortip' => '该集团不可操作!', "bakfuntion" => "errormotify"));
        }


        $companyOne = $this->Show_css->getOne("gmc_company", "company_id='{$request['company_id']}'");

        if (!$companyOne) {
            ajax_return(array('error' => '1', 'errortip' => '集团不存在!', "bakfuntion" => "errormotify"));
        }

        $sql = "select sc.contract_id,sc.edition_id,ie.edition_code
              from imc_sales_contract as sc,imc_edition as ie
              where sc.edition_id=ie.edition_id and sc.company_id='{$companyOne['company_id']}' and sc.contract_starttime<=CURDATE() and sc.contract_endtime>=CURDATE() 
              order by sc.contract_createtime desc,sc.contract_id asc limit 0,1";

        $contractOne = $this->Show_css->selectOne($sql);

        if (!$contractOne) {
            ajax_return(array('error' => '1', 'errortip' => '该集团无使用合同!', "bakfuntion" => "errormotify"));
        }

        if ($contractOne['edition_id'] != 2) {
            $data = array();
            $data['edition_id'] = 2;
            $data['contract_endtime'] = '2025-12-31';
            $this->Show_css->updateData("imc_sales_contract", "contract_id='{$contractOne['contract_id']}'", $data);

        }

        //清除集团职务
        $this->Show_css->delData("gmc_staffer_postbe", "company_id='{$request['company_id']}' and school_id=0");

        //清除离职的校园职务
        $this->Show_css->delData("gmc_staffer_postbe", "company_id='{$request['company_id']}' and school_id>0 and postbe_status=0");

        $sql = "select masterplate_id from gmc_company_masterplate where (company_id=0 or company_id='{$request['company_id']}')";

        $plateList = $this->Show_css->selectClear($sql);

        if ($plateList) {

            foreach ($plateList as $plateOne) {
                if (!$this->Show_css->getFieldOne("eas_masterplateapply", "masterplate_id", "company_id='{$request['company_id']}' and masterplate_id='{$plateOne['masterplate_id']}'")) {
                    $data = array();
                    $data['company_id'] = $request['company_id'];
                    $data['masterplate_id'] = $plateOne['masterplate_id'];
                    $this->Show_css->insertData("eas_masterplateapply", $data);
                }
            }
        }

        $stafferOne = $this->Show_css->getFieldOne("smc_staffer", "staffer_id", "company_id='{$companyOne['company_id']}' and account_class=1");
        if (!$stafferOne) {
            $data = array();
            $data['company_id'] = $request['company_id'];
            $data['account_class'] = 1;
            $data['staffer_pass'] = md5('mohism2021');
            $data['staffer_bakpass'] = 'mohism2021';
            $data['staffer_createtime'] = time();
            $this->Show_css->insertData("smc_staffer", $data);

        } else {
            $data = array();
            $data['staffer_pass'] = md5('mohism2021');
            $data['staffer_bakpass'] = 'mohism2021';
            $data['staffer_updatetime'] = time();
            $this->Show_css->updateData("smc_staffer", "staffer_id='{$stafferOne['staffer_id']}'", $data);
        }

        $sql = "select sc.course_cnname,sc.course_branch,sc.course_inclasstype,sc.course_checkingintype,sc.course_classnum,sc.course_freenums,cc.coursecat_cnname,cc.coursecat_branch,ct.coursetype_cnname,ct.coursetype_branch 
              from smc_course as sc,smc_code_coursecat as cc,smc_code_coursetype as ct
              where sc.coursecat_id=cc.coursecat_id and sc.coursetype_id=ct.coursetype_id
              and sc.company_id='79063' 
              ";

        $courseList = $this->Show_css->selectClear($sql);

        if (!$courseList) {
            ajax_return(array('error' => '1', 'errortip' => '该集团创建课程!', "bakfuntion" => "errormotify"));
        }

        foreach ($courseList as $courseOne) {
            $coursetypeOne = $this->Show_css->getFieldOne("smc_code_coursetype", "coursetype_id", "company_id='{$request['company_id']}' and coursetype_branch='{$courseOne['coursetype_branch']}'");

            if (!$coursetypeOne) {
                $data = array();
                $data['company_id'] = $request['company_id'];
                $data['coursetype_isimport'] = 1;
                $data['coursetype_cnname'] = $courseOne['coursetype_cnname'];
                $data['coursetype_branch'] = $courseOne['coursetype_branch'];
                if ($this->Show_css->insertData("smc_code_coursetype", $data)) {
                    $coursetypeOne = $this->Show_css->getFieldOne("smc_code_coursetype", "coursetype_id", "company_id='{$request['company_id']}' and coursetype_branch='{$courseOne['coursetype_branch']}'");
                } else {
                    ajax_return(array('error' => '1', 'errortip' => '班组创建失败!', "bakfuntion" => "errormotify"));
                }

            }

            $coursetype_id = $coursetypeOne['coursetype_id'];

            $coursecatOne = $this->Show_css->getFieldOne("smc_code_coursecat", "coursecat_id", "company_id='{$request['company_id']}' and coursecat_branch='{$courseOne['coursecat_branch']}'");

            if (!$coursecatOne) {
                $data = array();
                $data['company_id'] = $request['company_id'];
                $data['coursecat_isimport'] = 1;
                $data['coursetype_id'] = $coursetype_id;
                $data['coursecat_cnname'] = $courseOne['coursecat_cnname'];
                $data['coursecat_branch'] = $courseOne['coursecat_branch'];
                if ($this->Show_css->insertData("smc_code_coursecat", $data)) {
                    $coursecatOne = $this->Show_css->getFieldOne("smc_code_coursecat", "coursecat_id", "company_id='{$request['company_id']}' and coursecat_branch='{$courseOne['coursecat_branch']}'");
                } else {
                    ajax_return(array('error' => '1', 'errortip' => '班种创建失败!', "bakfuntion" => "errormotify"));
                }
            }

            $coursecat_id = $coursecatOne['coursecat_id'];


            if (!$this->Show_css->getFieldOne("smc_course", "course_id", "company_id='{$request['company_id']}' and course_branch='{$courseOne['course_branch']}'")) {
                $data = array();
                $data['company_id'] = $request['company_id'];
                $data['coursetype_id'] = $coursetype_id;
                $data['coursecat_id'] = $coursecat_id;
                $data['course_isimport'] = 1;
                $data['course_cnname'] = $courseOne['course_cnname'];
                $data['course_branch'] = $courseOne['course_branch'];
                $data['course_freenums'] = $courseOne['course_freenums'];
                $data['course_inclasstype'] = $courseOne['course_inclasstype'];
                $data['course_classnum'] = $courseOne['course_classnum'];
                $data['course_checkingintype'] = $courseOne['course_checkingintype'];

                $data['course_schedule'] = 0;//是否允许重新排课 0-允许 1-不允许

                if (!$this->Show_css->insertData("smc_course", $data)) {
                    ajax_return(array('error' => '1', 'errortip' => '课程创建失败!', "bakfuntion" => "errormotify"));
                }
            }
        }


        $postpartList = $this->Show_css->getList("smc_school_postpart", "company_id='79063'");
        if ($postpartList) {
            foreach ($postpartList as $postpartOne) {
                if (!$this->Show_css->getFieldOne("smc_school_postpart", "postpart_id", "company_id='{$companyOne['company_id']}' and postpart_name='{$postpartOne['postpart_name']}'")) {
                    $data = array();
                    $data['company_id'] = $companyOne['company_id'];
                    $data['postpart_name'] = $postpartOne['postpart_name'];
                    $data['postpart_isteregulator'] = $postpartOne['postpart_isteregulator'];
                    $this->Show_css->insertData("smc_school_postpart", $data);
                }
            }
        }

        $data = array();
        $data['coursecat_isfree'] = 1;
        $this->Show_css->updateData("smc_code_coursecat", "company_id='{$companyOne['company_id']}'", $data);

        $sql = "update smc_course a
                left join (
                select w.course_id,z.course_id as course_nextid
                from smc_course x ,smc_course y ,smc_course z ,smc_course w
                where x.company_id='79063' and y.company_id='79063'
                and z.company_id='{$companyOne['company_id']}' and w.company_id='{$companyOne['company_id']}'
                and x.course_nextid=y.course_id and y.course_branch=z.course_branch and w.course_branch=x.course_branch
                ) ta on a.course_id=ta.course_id
                set a.course_nextid=ta.course_nextid
                where a.company_id='{$companyOne['company_id']}'";

        $this->Show_css->selectClear($sql);

        $sql = "insert into gmc_company_post(post_type,company_id,postpart_id,post_code,post_name,post_remk,post_isrecrparttime,post_isteaching,post_istopjob,post_createtime,post_updatetime,postlevel_id,post_istel,post_teltype,post_stride)
select a.post_type,b.company_id,d.postpart_id,a.post_code,a.post_name,a.post_remk,a.post_isrecrparttime,a.post_isteaching,a.post_istopjob,a.post_createtime
,a.post_updatetime,(select postlevel_id from gmc_company_postlevel where company_id=b.company_id limit 1) as postlevel_id,a.post_istel,a.post_teltype,a.post_stride
            from gmc_company_post a
            left join gmc_company b on b.company_id ='{$companyOne['company_id']}'
            left join smc_school_postpart c on a.postpart_id=c.postpart_id and a.company_id=c.company_id
            left join smc_school_postpart d on c.postpart_name=d.postpart_name and b.company_id=d.company_id
            where a.company_id='79063' and not exists(select 1 from gmc_company_post as pt where pt.company_id='{$companyOne['company_id']}' and pt.post_code=a.post_code and pt.post_type=a.post_type) ";

        $this->Show_css->selectClear($sql);

        $sql = "insert into smc_staffer_usermodule(company_id,postpart_id,module_id)
              select b.company_id,d.postpart_id,a.module_id
              from smc_staffer_usermodule as a
              left join gmc_company b on b.company_id ='{$companyOne['company_id']}'
              left join smc_school_postpart c on a.postpart_id=c.postpart_id and a.company_id=c.company_id
              left join smc_school_postpart d on c.postpart_name=d.postpart_name and b.company_id=d.company_id
              where a.company_id='79063'
               ";

        $this->Show_css->selectClear($sql);

        $reasonList = $this->Show_css->getList("smc_code_stuchange_reason", "company_id='79063'");
        if ($reasonList) {
            foreach ($reasonList as $reasonOne) {
                if (!$this->Show_css->getFieldOne("smc_code_stuchange_reason", "reason_id", "company_id='{$companyOne['company_id']}' and reason_code='{$reasonOne['reason_code']}'")) {
                    $data = array();
                    $data['company_id'] = $companyOne['company_id'];
                    $data['reason_code'] = $reasonOne['reason_code'];
                    $data['reason_note'] = $reasonOne['reason_note'];
                    $data['stuchange_code'] = $reasonOne['stuchange_code'];
                    $this->Show_css->insertData("smc_code_stuchange_reason", $data);
                }
            }
        }

        $moduleList = $this->Show_css->getList("gmc_editionpro_module", "company_id='79063'");
        if ($moduleList) {
            foreach ($moduleList as $moduleOne) {
                if (!$this->Show_css->getFieldOne("gmc_editionpro_module", "module_id", "company_id='{$companyOne['company_id']}' and module_id='{$moduleOne['module_id']}'")) {
                    $data = array();
                    $data['company_id'] = $companyOne['company_id'];
                    $data['module_id'] = $moduleOne['module_id'];
                    $data['module_status'] = $moduleOne['module_status'];
                    $this->Show_css->insertData("gmc_editionpro_module", $data);
                } else {
                    $data = array();
                    $data['module_status'] = $moduleOne['module_status'];
                    $this->Show_css->updateData("gmc_editionpro_module", "company_id='{$companyOne['company_id']}' and module_id='{$moduleOne['module_id']}'", $data);
                }
            }
        }

        $time = time();

        $data = array();
        $data['order_status'] = '-1';
        $this->Show_css->updateData("smc_payfee_order", "company_id='{$request['company_id']}' and order_status>=0 and order_status<4", $data);

        $data = array();
        $data['trading_status'] = '-1';
        $this->Show_css->updateData("smc_student_trading", "company_id='{$request['company_id']}' and trading_status=0", $data);


        //清空账户余额
        $sql = "select student_id,student_balance,student_withholdbalance,school_id
              from smc_student_balance where company_id='{$request['company_id']}' and (student_balance>0 or student_withholdbalance>0)
              ";
        $studentBalanceList = $this->Show_css->selectClear($sql);
        if ($studentBalanceList) {
            $str = '';
            foreach ($studentBalanceList as $studentBalanceOne) {
                $schoolOne = $this->Show_css->getFieldOne("smc_school", "companies_id", "school_id='{$studentBalanceOne['school_id']}'");

                if ($studentBalanceOne['student_balance'] > 0) {
                    $str .= "insert into smc_student_balancelog (`company_id`,`companies_id`,`school_id`,`staffer_id`,`student_id`,`balancelog_class`,`balancelog_playname`,`balancelog_playclass`,`balancelog_fromamount`,`balancelog_playamount`,`balancelog_finalamount`,`balancelog_reason`,`balancelog_time`) values ('{$request['company_id']}','{$schoolOne['companies_id']}','{$studentBalanceOne['school_id']}','12357','{$studentBalanceOne['student_id']}','0','合同变更清空余额','-','{$studentBalanceOne['student_balance']}','{$studentBalanceOne['student_balance']}','0','合同变更清空余额','{$time}');";
                }

                if ($studentBalanceOne['student_withholdbalance'] > 0) {
                    $str .= "insert into smc_student_balancelog (`company_id`,`companies_id`,`school_id`,`staffer_id`,`student_id`,`balancelog_class`,`balancelog_playname`,`balancelog_playclass`,`balancelog_fromamount`,`balancelog_playamount`,`balancelog_finalamount`,`balancelog_reason`,`balancelog_time`) values ('{$request['company_id']}','{$schoolOne['companies_id']}','{$studentBalanceOne['school_id']}','12357','{$studentBalanceOne['student_id']}','2','合同变更清空余额','-','{$studentBalanceOne['student_withholdbalance']}','{$studentBalanceOne['student_withholdbalance']}','0','合同变更清空余额','{$time}');";
                }

            }

            $this->Show_css->selectClear($str);


            $data = array();
            $data['student_balance'] = 0;
            $data['student_withholdbalance'] = 0;
            $this->Show_css->updateData("smc_student_balance", "company_id='{$companyOne['company_id']}' and (student_balance>0 or student_withholdbalance>0)", $data);

        }

        //清空课程余额
        $sql = "select school_id,course_id,student_id,coursebalance_figure,coursebalance_time,companies_id from smc_student_coursebalance where company_id='{$request['company_id']}' and (coursebalance_figure>0 or coursebalance_time>0)";

        $courseBalanceList = $this->Show_css->selectClear($sql);

        if ($courseBalanceList) {
            $str = '';
            foreach ($courseBalanceList as $courseBalanceOne) {

                $str .= "insert into smc_student_coursebalance_log (`school_id`,`companies_id`,`course_id`,`student_id`,`log_class`,`log_playname`,`log_playclass`,`log_fromamount`,`log_playamount`,`log_finalamount`,`log_fromtimes`,`log_playtimes`,`log_finaltimes`,`log_reason`,`log_time`) values ('{$courseBalanceOne['school_id']}','{$courseBalanceOne['companies_id']}','{$courseBalanceOne['course_id']}','{$courseBalanceOne['student_id']}','0','合同变更清空课程余额','-','{$courseBalanceOne['coursebalance_figure']}','{$courseBalanceOne['coursebalance_figure']}','0','{$courseBalanceOne['coursebalance_time']}','{$courseBalanceOne['coursebalance_time']}','0','合同变更清空课程余额','{$time}');";

                if ($courseBalanceOne['coursebalance_time'] > 0) {
                    $str .= "insert into smc_student_coursebalance_timelog (`school_id`,`companies_id`,`course_id`,`student_id`,`timelog_playname`,`timelog_playclass`,`timelog_fromtimes`,`timelog_playtimes`,`timelog_finaltimes`,`timelog_reason`,`timelog_time`) values ('{$courseBalanceOne['school_id']}','{$courseBalanceOne['course_id']}','{$courseBalanceOne['companies_id']}','{$courseBalanceOne['student_id']}','合同变更清空课程课次','-','{$courseBalanceOne['coursebalance_time']}','{$courseBalanceOne['coursebalance_time']}','0','合同变更清空课程课次','{$time}');";
                }
            }

            $this->Show_css->selectClear($str);
//            $sql="update smc_student_coursebalance set coursebalance_figure='0',coursebalance_time='0',coursebalance_updatatime='{$time}' where company_id='{$request['company_id']}' and (coursebalance_figure>0 or coursebalance_time>0);";
//            $this->Show_css->selectClear($sql);

            $data = array();
            $data['coursebalance_figure'] = 0;
            $data['coursebalance_time'] = 0;
            $this->Show_css->updateData("smc_student_coursebalance", "company_id='{$companyOne['company_id']}' and (coursebalance_figure>0 or coursebalance_time>0)", $data);


        }

        ajax_return(array('error' => 0, 'errortip' => "转化成功!", "bakfuntion" => "okmotify", "state" => "1"));

    }


    function ImportCompanyExcel_bakView()
    {

        if ($_FILES['uploadFile']['name'] == '' || $_FILES['uploadFile']['error'] == '1') {
            $PlayInfoVar = array();
            $PlayInfoVar['company_code'] = "导入出错";
            $PlayInfoVar['company_cnname'] = "导入出错";
            $PlayInfoVar['error'] = "1";
            $PlayInfoVar['errortip'] = "文件不存在";
            $PlayInfo[] = $PlayInfoVar;
            $this->smarty->assign("PlayInfo", $PlayInfo);
            exit;
        }
        $fileType = array('xls', 'csv', 'xlsx');
        $uploadfile = new \Webfile($_FILES['uploadFile'], $files_dir = '../static/file', $size = 2097152 * 10, $fileType);
        $up_file = $uploadfile->upload();

        $PlayInfo = array();

        $like = date("Ymd", time());

        if ($up_file && $uploadfile->updatastatus) {
            $ExeclName = array();
            $ExeclName['校区名称'] = "school_cnname";
            $ExeclName['校区编号'] = "school_branch";
            $ExeclName['手机号'] = "staffer_mobile";
            $ExeclName['密码'] = "staffer_pass";
            $ExeclName['集团编号'] = "company_code";

            $WorkerList = execl_to_array($up_file, $ExeclName);
            unset($WorkerList[0]);

            if ($WorkerList) {
                foreach ($WorkerList as $WorkerrVar) {
                    if ($WorkerrVar['company_code'] !== '' && $WorkerrVar['staffer_pass'] !== '' && $WorkerrVar['staffer_mobile'] !== '') {
                        $workersList[] = $WorkerrVar;
                    } else {
                        $PlayInfoVar = array();
                        $PlayInfoVar['company_code'] = $WorkerrVar['company_code'];
                        $PlayInfoVar['error'] = "1";
                        $PlayInfoVar['errortip'] = "导入账号信息不完整";
                        $PlayInfo[] = $PlayInfoVar;
                    }
                }
            }
            if ($workersList) {
                foreach ($workersList as &$val) {
                    $val['school_cnname'] = trim($val['school_cnname']);
                    $val['school_branch'] = trim($val['school_branch']);
                    $val['staffer_mobile'] = trim($val['staffer_mobile']);
                    $val['staffer_pass'] = trim($val['staffer_pass']);
                    $val['company_code'] = trim($val['company_code']);
                }

                foreach ($workersList as $workersVar) {
                    $PlayInfoVar = array();
                    $PlayInfoVar['school_cnname'] = $workersVar['school_cnname'];
                    $PlayInfoVar['school_branch'] = $workersVar['school_branch'];
                    $PlayInfoVar['staffer_mobile'] = $workersVar['staffer_mobile'];
                    $PlayInfoVar['staffer_pass'] = $workersVar['staffer_pass'];
                    $PlayInfoVar['company_code'] = $workersVar['company_code'];

                    $companyOne = $this->Show_css->getFieldOne("gmc_company", "company_id", "company_code='{$workersVar['company_code']}'");

                    if (!$companyOne) {
                        $PlayInfoVar['error'] = "1";
                        $PlayInfoVar['errortip'] = "集团不存在";
                        $PlayInfo[] = $PlayInfoVar;
                        continue;
                    }

                    $sql = "select sc.contract_id,sc.edition_id,ie.edition_code
              from imc_sales_contract as sc,imc_edition as ie
              where sc.edition_id=ie.edition_id and sc.company_id='{$companyOne['company_id']}' and sc.contract_starttime<=CURDATE() and sc.contract_endtime>=CURDATE() 
              order by sc.contract_createtime desc,sc.contract_id asc limit 0,1";

                    $contractOne = $this->Show_css->selectOne($sql);

                    if (!$contractOne) {
                        $PlayInfoVar['error'] = "1";
                        $PlayInfoVar['errortip'] = "不存在有效合同";
                        $PlayInfo[] = $PlayInfoVar;
                        continue;
                    }

                    $stafferOne = $this->Show_css->getFieldOne("smc_staffer", "staffer_id", "company_id='{$companyOne['company_id']}' and account_class=1");
                    if (!$stafferOne) {
                        $PlayInfoVar['error'] = "1";
                        $PlayInfoVar['errortip'] = "未创建管理员";
                        $PlayInfo[] = $PlayInfoVar;
                        continue;
                    }


                    $postpartList = $this->Show_css->getList("smc_school_postpart", "company_id='79063'");
                    if ($postpartList) {
                        foreach ($postpartList as $postpartOne) {
                            if (!$this->Show_css->getFieldOne("smc_school_postpart", "postpart_id", "company_id='{$companyOne['company_id']}' and postpart_name='{$postpartOne['postpart_name']}'")) {
                                $data = array();
                                $data['company_id'] = $companyOne['company_id'];
                                $data['postpart_name'] = $postpartOne['postpart_name'];
                                $data['postpart_isteregulator'] = $postpartOne['postpart_isteregulator'];
                                $this->Show_css->insertData("smc_school_postpart", $data);
                            }
                        }
                    }


                    $data = array();
                    $data['staffer_pass'] = md5('mohism2021');
                    $data['staffer_bakpass'] = 'mohism2021';
                    $data['staffer_updatetime'] = time();
                    $this->Show_css->updateData("smc_staffer", "staffer_id='{$stafferOne['staffer_id']}'", $data);

                    if ($contractOne['edition_id'] != 2) {
                        $data = array();
                        $data['edition_id'] = 2;
                        $this->Show_css->updateData("imc_sales_contract", "contract_id='{$contractOne['contract_id']}'", $data);

                    }

                    if (!$this->Show_css->getFieldOne("smc_staffer", "staffer_id", "company_id='{$companyOne['company_id']}' and staffer_mobile='{$workersVar['staffer_mobile']}'")) {
                        $data = array();
                        $data['company_id'] = $companyOne['company_id'];

                        $stuInfo = $this->Show_css->selectOne("select staffer_branch from smc_staffer where staffer_branch like '{$like}%' AND LENGTH(staffer_branch) = '14' order by staffer_branch DESC limit 0,1");
                        if ($stuInfo) {
                            $data['staffer_branch'] = $stuInfo['staffer_branch'] + 1;
                        } else {
                            $data['staffer_branch'] = $like . '000001';
                        }

                        $data['staffer_cnname'] = '初始教師';
                        $data['staffer_mobile'] = $workersVar['staffer_mobile'];
                        $data['staffer_pass'] = md5($workersVar['staffer_pass']);
                        $data['staffer_bakpass'] = $workersVar['staffer_pass'];
                        $data['staffer_createtime'] = time();

                        $this->Show_css->insertData("smc_staffer", $data);
                    }

                    $data = array();
                    $data['coursecat_isfree'] = 1;
                    $this->Show_css->updateData("smc_code_coursecat", "company_id='{$companyOne['company_id']}'", $data);


                    $sql = "update smc_course a
                            left join (
                            select w.course_id,z.course_id as course_nextid
                            from smc_course x ,smc_course y ,smc_course z ,smc_course w
                            where x.company_id='79063' and y.company_id='79063'
                            and z.company_id='{$companyOne['company_id']}' and w.company_id='{$companyOne['company_id']}'
                            and x.course_nextid=y.course_id and y.course_branch=z.course_branch and w.course_branch=x.course_branch
                            ) ta on a.course_id=ta.course_id
                            set a.course_nextid=ta.course_nextid
                            where a.company_id='{$companyOne['company_id']}'";

                    $this->Show_css->selectClear($sql);

                    $sql = "insert into gmc_company_post(post_type,company_id,postpart_id,post_code,post_name,post_remk,post_isrecrparttime,post_isteaching,post_istopjob,post_createtime,post_updatetime,postlevel_id,post_istel,post_teltype,post_stride)
select  a.post_type,b.company_id,d.postpart_id,a.post_code,a.post_name,a.post_remk,a.post_isrecrparttime,a.post_isteaching,a.post_istopjob,a.post_createtime
,a.post_updatetime,(select postlevel_id from gmc_company_postlevel where company_id=b.company_id limit 1)as postlevel_id,a.post_istel,a.post_teltype,a.post_stride
from 
gmc_company_post a 
left join gmc_company b on b.company_id ='{$companyOne['company_id']}'
left join smc_school_postpart c on a.postpart_id=c.postpart_id and a.company_id=c.company_id
left join smc_school_postpart d on c.postpart_name=d.postpart_name and b.company_id=d.company_id
where a.company_id='79063'";

                    $this->Show_css->selectClear($sql);

                    $reasonList = $this->Show_css->getList("smc_code_stuchange_reason", "company_id='79063'");
                    if ($reasonList) {
                        foreach ($reasonList as $reasonOne) {
                            if (!$this->Show_css->getFieldOne("smc_code_stuchange_reason", "reason_id", "company_id='{$companyOne['company_id']}' and reason_code='{$reasonOne['reason_code']}'")) {
                                $data = array();
                                $data['company_id'] = $companyOne['company_id'];
                                $data['reason_code'] = $reasonOne['reason_code'];
                                $data['reason_note'] = $reasonOne['reason_note'];
                                $data['stuchange_code'] = $reasonOne['stuchange_code'];
                                $this->Show_css->insertData("smc_code_stuchange_reason", $data);
                            }
                        }
                    }

                    $moduleList = $this->Show_css->getList("gmc_editionpro_module", "company_id='79063'");
                    if ($moduleList) {
                        foreach ($moduleList as $moduleOne) {
                            if (!$this->Show_css->getFieldOne("gmc_editionpro_module", "module_id", "company_id='{$companyOne['company_id']}' and module_id='{$moduleOne['module_id']}'")) {
                                $data = array();
                                $data['company_id'] = $companyOne['company_id'];
                                $data['module_id'] = $moduleOne['module_id'];
                                $data['module_status'] = $moduleOne['module_status'];
                                $this->Show_css->insertData("gmc_editionpro_module", $data);
                            }
                        }
                    }

                    $PlayInfoVar['error'] = "0";
                    $PlayInfoVar['errortip'] = "导入成功";
                    $PlayInfo[] = $PlayInfoVar;
                }
            }

        } else {
            $PlayInfoVar = array();
            $PlayInfoVar['company_code'] = "导入出错";
            $PlayInfoVar['company_cnname'] = "导入出错";
            $PlayInfoVar['error'] = "1";
            $PlayInfoVar['errortip'] = "文件不存在";
            $PlayInfo[] = $PlayInfoVar;
        }

        if ($PlayInfo) {
            $data = array();
            $data['errorlog_json'] = json_encode($PlayInfo, JSON_UNESCAPED_UNICODE);
            $data['errorlog_createtime'] = time();
            $this->Show_css->insertData("smc_tolead_errorlog", $data);

        }

        $this->smarty->assign("PlayInfo", $PlayInfo);


    }


    function ImportCompanyExcelView()
    {
        if ($_FILES['uploadFile']['name'] == '' || $_FILES['uploadFile']['error'] == '1') {
            $PlayInfoVar = array();
            $PlayInfoVar['company_code'] = "导入出错";
            $PlayInfoVar['company_cnname'] = "导入出错";
            $PlayInfoVar['error'] = "1";
            $PlayInfoVar['errortip'] = "文件不存在";
            $PlayInfo[] = $PlayInfoVar;
            $this->smarty->assign("PlayInfo", $PlayInfo);
            exit;
        }

        $fileType = array('xls', 'csv', 'xlsx');
        $uploadfile = new \Webfile($_FILES['uploadFile'], $files_dir = '../static/file', $size = 2097152 * 10, $fileType);
        $up_file = $uploadfile->upload();

        $PlayInfo = array();
        if ($up_file && $uploadfile->updatastatus) {
            $ExeclName = array();
            $ExeclName['企业编号'] = "company_code";
            $ExeclName['企业简称'] = "company_shortname";
            $ExeclName['企业名称'] = "company_cnname";
            $ExeclName['联系人姓名'] = "company_name";
            $ExeclName['联系手机'] = "company_mobile";

            $WorkerList = execl_to_array($up_file, $ExeclName);
            unset($WorkerList[0]);

            if ($WorkerList) {
                foreach ($WorkerList as $WorkerrVar) {
                    if ($WorkerrVar['企业编号'] !== '' && $WorkerrVar['企业简称'] !== '' && $WorkerrVar['企业名称'] !== '') {
                        $workersList[] = $WorkerrVar;
                    } else {
                        $PlayInfoVar = array();
                        $PlayInfoVar['company_cnname'] = $WorkerrVar['company_cnname'];
                        $PlayInfoVar['error'] = "1";
                        $PlayInfoVar['errortip'] = "导入账号信息不完整";
                        $PlayInfo[] = $PlayInfoVar;
                    }
                }
            }
            if ($workersList) {
                foreach ($workersList as $workersVar) {
                    $PlayInfoVar = array();
                    $PlayInfoVar['company_code'] = $workersVar['company_code'];
                    $PlayInfoVar['company_shortname'] = $workersVar['company_shortname'];
                    $PlayInfoVar['company_cnname'] = $workersVar['company_cnname'];
                    $PlayInfoVar['company_name'] = $workersVar['company_name'];
                    $PlayInfoVar['company_mobile'] = $workersVar['company_mobile'];

                    if ($this->Show_css->getFieldOne("gmc_company", "company_id", "company_code = '{$workersVar['company_code']}'")) {
                        $PlayInfoVar = array();
                        $PlayInfoVar['company_cnname'] = $workersVar['company_cnname'];
                        $PlayInfoVar['error'] = "1";
                        $PlayInfoVar['errortip'] = "集团{$workersVar['company_cnname']}已存在";
                        $PlayInfo[] = $PlayInfoVar;
                        continue;
                    }

                    $data = array();
                    $data['company_code'] = $workersVar['company_code'];
                    $data['company_shortname'] = $workersVar['company_shortname'];
                    $data['company_cnname'] = $workersVar['company_cnname'];
                    $data['company_name'] = $workersVar['company_name'];
                    $data['company_mobile'] = $workersVar['company_mobile'];
                    $data['company_addtime'] = time();

                    if ($id = $this->Show_css->insertData("gmc_company", $data)) {
                        $datas = array();
                        $datas['company_id'] = $id;
                        $datas['account_class'] = '1';
                        $datas['staffer_branch'] = $data['company_code'];
                        $datas['staffer_cnname'] = '管理员';
                        $datas['staffer_sex'] = '男';
                        $datas['staffer_mobile'] = 'admin' . $data['company_code'];
                        $datas['staffer_pass'] = md5('123456');
                        $datas['staffer_bakpass'] = '123456';
                        $datas['staffer_createtime'] = time();

                        $this->Show_css->insertData("smc_staffer", $datas);

                        $PlayInfoVar['error'] = "0";
                        $PlayInfoVar['errortip'] = "导入成功";
                    } else {
                        $PlayInfoVar['error'] = "1";
                        $PlayInfoVar['errortip'] = "导入失败";
                    }
                    $PlayInfo[] = $PlayInfoVar;
                }
            }

        } else {
            $PlayInfoVar = array();
            $PlayInfoVar['company_code'] = "导入出错";
            $PlayInfoVar['company_cnname'] = "导入出错";
            $PlayInfoVar['error'] = "1";
            $PlayInfoVar['errortip'] = "文件不存在";
            $PlayInfo[] = $PlayInfoVar;
        }

        $this->smarty->assign("PlayInfo", $PlayInfo);
    }

    function ImportCompanyExcel_bak1View()
    {

        if ($_FILES['uploadFile']['name'] == '' || $_FILES['uploadFile']['error'] == '1') {
            $PlayInfoVar = array();
            $PlayInfoVar['company_code'] = "导入出错";
            $PlayInfoVar['company_cnname'] = "导入出错";
            $PlayInfoVar['error'] = "1";
            $PlayInfoVar['errortip'] = "文件不存在";
            $PlayInfo[] = $PlayInfoVar;
            $this->smarty->assign("PlayInfo", $PlayInfo);
            exit;
        }
        $fileType = array('xls', 'csv', 'xlsx');
        $uploadfile = new \Webfile($_FILES['uploadFile'], $files_dir = '../static/file', $size = 2097152 * 10, $fileType);
        $up_file = $uploadfile->upload();

        $PlayInfo = array();

        if ($up_file && $uploadfile->updatastatus) {
            $ExeclName = array();
            $ExeclName['课程编号'] = "course_branch";
            $ExeclName['可升级班级'] = "course_str";

            $WorkerList = execl_to_array($up_file, $ExeclName);
            unset($WorkerList[0]);

            if ($WorkerList) {
                foreach ($WorkerList as $WorkerrVar) {
                    if ($WorkerrVar['course_branch'] !== '' && $WorkerrVar['course_str'] !== '') {
                        $workersList[] = $WorkerrVar;
                    } else {
                        $PlayInfoVar = array();
                        $PlayInfoVar['company_code'] = $WorkerrVar['company_code'];
                        $PlayInfoVar['error'] = "1";
                        $PlayInfoVar['errortip'] = "无升级班级";
                        $PlayInfo[] = $PlayInfoVar;
                    }
                }
            }
            if ($workersList) {
                foreach ($workersList as &$val) {
                    $val['course_branch'] = trim($val['course_branch']);
                    $val['course_str'] = str_replace("，", ",", trim($val['course_str']));
                }

                foreach ($workersList as $workersVar) {
                    $PlayInfoVar = array();
                    $PlayInfoVar['course_branch'] = $workersVar['course_branch'];
                    $PlayInfoVar['course_str'] = $workersVar['course_str'];

                    $courseOne = $this->Show_css->getFieldOne("smc_course", "course_id", "company_id='8888' and course_branch='{$workersVar['course_branch']}'");

                    if (!$courseOne) {
                        $PlayInfoVar['error'] = "1";
                        $PlayInfoVar['errortip'] = "课程不存在";
                        $PlayInfo[] = $PlayInfoVar;
                        continue;
                    }

                    $courseArray = explode(',', $workersVar['course_str']);

                    if (!$courseArray) {
                        $PlayInfoVar['error'] = "1";
                        $PlayInfoVar['errortip'] = "升级内容不存在";
                        $PlayInfo[] = $PlayInfoVar;
                        continue;
                    }

                    $tem_data = array();
                    $status = 1;
                    foreach ($courseArray as $val) {
                        $one = $this->Show_css->getFieldOne("smc_course", "course_id", "company_id='8888' and  course_branch='{$val}'");
                        if ($one) {
                            $tem_data[] = $one['course_id'];
                        } else {
                            $status = 0;
                        }
                    }

                    if ($status == 0 || !$tem_data) {
                        $PlayInfoVar['error'] = "1";
                        $PlayInfoVar['errortip'] = "升级课程不存在";
                        $PlayInfo[] = $PlayInfoVar;
                        continue;
                    }

                    $tem_str = implode(',', $tem_data);

                    $data = array();

                    $data['course_nextid'] = $tem_str;

                    $this->Show_css->updateData("smc_course", "course_id='{$courseOne['course_id']}'", $data);


                    $PlayInfoVar['error'] = "0";
                    $PlayInfoVar['errortip'] = "导入成功";
                    $PlayInfo[] = $PlayInfoVar;
                }
            }

        } else {
            $PlayInfoVar = array();
            $PlayInfoVar['company_code'] = "导入出错";
            $PlayInfoVar['company_cnname'] = "导入出错";
            $PlayInfoVar['error'] = "1";
            $PlayInfoVar['errortip'] = "文件不存在";
            $PlayInfo[] = $PlayInfoVar;
        }

        $this->smarty->assign("PlayInfo", $PlayInfo);


    }


    function isAddStudentAction()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $companyOne = $this->Show_css->selectOne("select company_isaddstudent from gmc_company where company_id ='{$request['company_id']}' ");
        $isadd = 1 - $companyOne['company_isaddstudent'];
        $data['company_isaddstudent'] = $isadd;
        $this->Show_css->updateData('gmc_company', "company_id ='{$request['company_id']}'", $data);
        $this->Recordweblog($request['site_id'], $this->Module['module_id'], $this->c, "修改是否允许收到添加学员" . $isadd . "，校园ID:{$request['school_id']}");
        ajax_return(array('error' => 0, 'errortip' => "提交成功!", 'state' => $isadd));

    }

    function isAddVoucherAction()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $companyOne = $this->Show_css->selectOne("select company_isvoucher from gmc_company where company_id ='{$request['company_id']}' ");
        $isadd = 1 - $companyOne['company_isvoucher'];
        $data['company_isvoucher'] = $isadd;
        $this->Show_css->updateData('gmc_company', "company_id ='{$request['company_id']}'", $data);
        $this->Recordweblog($request['site_id'], $this->Module['module_id'], $this->c, "修改是否需要上传凭证" . $isadd . "，集团ID:{$request['company_id']}");
        ajax_return(array('error' => 0, 'errortip' => "提交成功!", 'state' => $isadd));

    }


    function givePriceAction()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $Show_css = $this->Show_css;
        $schoolArray = $this->Show_css->selectClear("select school_id,school_branch,school_cnname from smc_school
where company_id ='{$request['company_id']}' ");
        if ($schoolArray) {
            foreach ($schoolArray as $schoolOne) {
                $balanceOne = $this->Show_css->getFieldOne('tkl_balance_log', 'log_finalamount'
                    , "company_id = '{$request['company_id']}'", " ORDER BY log_id DESC");
                if (!$balanceOne) {
                    $balanceOne['log_finalamount'] = 0;
                }
                if (!$this->Show_css->getFieldOne("tkl_balance_log", "log_id", "school_branch = '{$schoolOne['school_branch']}'")) {
                    $data = array();
                    $data['company_id'] = $request['company_id'];
                    $data['log_class'] = '0';
                    $data['school_branch'] = $schoolOne['school_branch'];
                    $data['log_playname'] = '赠送网费';
                    $data['log_playclass'] = '+';
                    $data['log_fromamount'] = $balanceOne['log_finalamount'];
                    $data['log_playamount'] = '200';
                    $data['log_finalamount'] = $balanceOne['log_finalamount'] + 200;
                    $data['log_reason'] = "疫情期间吉的堡集团赠送伙伴网费，给与{$schoolOne['school_cnname']}赠送网费200元";
                    $data['log_time'] = time();
                    $Show_css->insertData("tkl_balance_log", $data);
                }
            }
        }

        $this->Recordweblog($request['site_id'], $this->Module['module_id'], $this->c, "赠送加盟商网费，ID:{$request['company_id']}");
        ajax_return(array('error' => 0, 'errortip' => "赠送加盟商网费成功!", "bakfuntion" => "successFromTip"));
    }

    function ImportCompanyView()
    {

    }

    //导入集团和对应的学校信息
    function ImportCompanySchoolView()
    {

    }

    //导入集团和对应的学校信息
    function ImportCompanySchoolExcelView()
    {
        if ($_FILES['uploadFile']['name'] == '' || $_FILES['uploadFile']['error'] == '1') {
            $PlayInfoVar = array();
            $PlayInfoVar['company_code'] = "导入出错";
            $PlayInfoVar['company_cnname'] = "导入出错";
            $PlayInfoVar['error'] = "1";
            $PlayInfoVar['errortip'] = "文件不存在";
            $PlayInfo[] = $PlayInfoVar;
            $this->smarty->assign("PlayInfo", $PlayInfo);
            exit;
        }

        $fileType = array('xls', 'csv', 'xlsx');
        $uploadfile = new \Webfile($_FILES['uploadFile'], $files_dir = '../static/file', $size = 2097152 * 10, $fileType);
        $up_file = $uploadfile->upload();

        $PlayInfo = array();
        if ($up_file && $uploadfile->updatastatus) {
            $ExeclName = array();
            $ExeclName['渠道编号'] = "company_code";
            $ExeclName['渠道名称'] = "company_cnname";//company_shortname
            $ExeclName['經營者中文名'] = "company_name";

            $ExeclName['渠道密码'] = "school_qiqupwd";
            $ExeclName['学校序号'] = "school_branch";
            $ExeclName['校区名称'] = "school_cnname";
            $ExeclName['省'] = "school_province";
            $ExeclName['市'] = "school_city";
            $ExeclName['区'] = "school_area";
            $ExeclName['地址'] = "school_address";
            $ExeclName['校园类型'] = "school_type";
            $ExeclName['联系电话'] = "school_phone";

            $WorkerList = execl_to_array($up_file, $ExeclName);
            unset($WorkerList[0]);

            if ($WorkerList) {
                foreach ($WorkerList as $WorkerrVar) {
                    if ($WorkerrVar['渠道编号'] !== '' && $WorkerrVar['渠道名称'] !== '') {
                        $workersList[] = $WorkerrVar;
                    } else {
                        $PlayInfoVar = array();
                        $PlayInfoVar['company_cnname'] = $WorkerrVar['company_cnname'];
                        $PlayInfoVar['error'] = "1";
                        $PlayInfoVar['errortip'] = "导入账号信息不完整";
                        $PlayInfo[] = $PlayInfoVar;
                    }
                }
            }
            if ($workersList) {
                foreach ($workersList as $workersVar) {
                    $PlayInfoVar = array();
                    $PlayInfoVar['company_code'] = $workersVar['company_code'];
                    $PlayInfoVar['company_shortname'] = $workersVar['company_cnname'];
                    $PlayInfoVar['company_cnname'] = $workersVar['company_cnname'];
                    $PlayInfoVar['company_name'] = $workersVar['company_name'];
                    $PlayInfoVar['school_phone'] = $workersVar['school_phone'];

                    if ($this->Show_css->getFieldOne("gmc_company", "company_id", "company_code = '{$workersVar['company_code']}'")) {
                        $PlayInfoVar = array();
                        $PlayInfoVar['company_cnname'] = $workersVar['company_cnname'];
                        $PlayInfoVar['error'] = "1";
                        $PlayInfoVar['errortip'] = "集团{$workersVar['company_cnname']}已存在";
                        $PlayInfo[] = $PlayInfoVar;
                        continue;
                    }

                    $data = array();
                    $data['company_ismajor'] = 1;
                    $data['company_language'] = 'tw';
                    $data['company_code'] = $workersVar['company_code'];
                    $data['company_shortname'] = $workersVar['company_cnname'];
                    $data['company_cnname'] = $workersVar['company_cnname'];
                    $data['company_name'] = $workersVar['company_name'];
                    $data['company_phone'] = $workersVar['school_phone'];
                    $data['company_brief'] = '台湾数据导入（加盟企业）';
                    $data['company_addtime'] = time();

                    $school_type = '0';
                    if ($workersVar['school_type'] == '直营校') {
                        $school_type = '1';
                    } elseif ($workersVar['school_type'] == '直营园') {
                        $school_type = '2';
                    } elseif ($workersVar['school_type'] == '加盟校') {
                        $school_type = '3';
                    } elseif ($workersVar['school_type'] == '加盟园') {
                        $school_type = '4';
                    }
                    $id = $this->Show_css->insertData("gmc_company", $data);

                    $school_province = $this->Show_css->selectOne(" select region_id from smc_code_region WHERE region_name = '{$workersVar['school_province']}' ");
                    $school_city = $this->Show_css->selectOne(" select region_id from smc_code_region WHERE region_name = '{$workersVar['school_city']}' ");
                    $school_area = $this->Show_css->selectOne(" select region_id from smc_code_region WHERE region_name = '{$workersVar['school_area']}' ");

                    if ($id) {
                        $datas = array();
                        $datas['company_id'] = $id;
                        $datas['account_class'] = '1';
                        $datas['staffer_branch'] = $data['company_code'];
                        $datas['staffer_cnname'] = '管理员';
                        $datas['staffer_sex'] = '男';
                        $datas['staffer_mobile'] = 'admin' . $data['company_code'];
                        $datas['staffer_pass'] = md5('123456');
                        $datas['staffer_bakpass'] = '123456';
                        $datas['staffer_createtime'] = time();

                        $this->Show_css->insertData("smc_staffer", $datas);

                        //添加对应的学校
                        $like = substr(date("Ymd", time()), 2, 6);
                        $ymd = date('ymd');

                        $dataOne = array();
                        $stuInfo = $this->Show_css->selectOne("select school_branch from smc_school where school_branch like '{$ymd}%' order by school_branch DESC limit 0,1");
                        if ($stuInfo) {
                            $dataOne['school_branch'] = $stuInfo['school_branch'] + 1;
                        } else {
                            $dataOne['school_branch'] = $like . '001';
                        }
                        $dataOne['company_id'] = $id;
                        $dataOne['school_qiqupwd'] = $workersVar['school_qiqupwd'];
                        $dataOne['school_qiqubranch'] = $workersVar['school_branch'];
                        $dataOne['school_cnname'] = $workersVar['school_cnname'];
                        $dataOne['school_province'] = $school_province['region_id'];
                        $dataOne['school_city'] = $school_city['region_id'];
                        $dataOne['school_area'] = $school_area['region_id'];
                        $dataOne['school_address'] = $workersVar['school_address'];
                        $dataOne['school_type'] = $school_type;
                        $dataOne['school_phone'] = $workersVar['school_phone'];
                        $dataOne['school_remark'] = '台湾数据导入（加盟）';
                        $dataOne['school_createtime'] = time();
                        $schoolOne = $this->Show_css->insertData("smc_school", $dataOne);

                        $PlayInfoVar['error'] = "0";
                        $PlayInfoVar['errortip'] = "导入成功";
                    } else {
                        //已经有的集团 添加学校
                        $companyOne = $this->Show_css->selectOne(" select * from gmc_company WHERE company_code = '{$workersVar['company_code']}' ");
                        if ($companyOne && $companyOne['company_id'] != $id) {

                            $like = substr(date("Ymd", time()), 2, 6);
                            $ymd = date('ymd');

                            $dataOne = array();
                            $stuInfo = $this->Show_css->selectOne("select school_branch from smc_school where school_branch like '{$ymd}%' order by school_branch DESC limit 0,1");
                            if ($stuInfo) {
                                $dataOne['school_branch'] = $stuInfo['school_branch'] + 1;
                            } else {
                                $dataOne['school_branch'] = $like . '001';
                            }
                            $dataOne['school_qiqupwd'] = $workersVar['school_qiqupwd'];
                            $dataOne['company_id'] = $companyOne['company_id'];
                            $dataOne['school_qiqubranch'] = $workersVar['school_branch'];
                            $dataOne['school_cnname'] = $workersVar['school_cnname'];
                            $dataOne['school_province'] = $school_province['region_id'];
                            $dataOne['school_city'] = $school_city['region_id'];
                            $dataOne['school_area'] = $school_area['region_id'];
                            $dataOne['school_address'] = $workersVar['school_address'];
                            $dataOne['school_type'] = $school_type;
                            $dataOne['school_phone'] = $workersVar['school_phone'];
                            $dataOne['school_remark'] = '台湾数据导入（加盟）';
                            $dataOne['school_createtime'] = time();
                            $schoolOne = $this->Show_css->insertData("smc_school", $dataOne);
                        }

                        $PlayInfoVar['error'] = "1";
                        $PlayInfoVar['errortip'] = "导入失败";
                    }

                    $PlayInfo[] = $PlayInfoVar;
                }
            }

        } else {
            $PlayInfoVar = array();
            $PlayInfoVar['company_code'] = "导入出错";
            $PlayInfoVar['company_cnname'] = "导入出错";
            $PlayInfoVar['error'] = "1";
            $PlayInfoVar['errortip'] = "文件不存在";
            $PlayInfo[] = $PlayInfoVar;
        }

        $this->smarty->assign("PlayInfo", $PlayInfo);
    }

    function __destruct()
    {
        $site_id = Input('get.site_id', 0);

        if ($this->c == 'Wait') {
            $this->smarty->assign("u", $this->router->getController());
            $this->smarty->assign("t", $this->router->getUrl());
            $this->smarty->assign("Viewhtm", $this->Viewhtm);
            $websites = $this->Show_css->getOne("cms_websites", "site_id='{$site_id}'");
            $this->smarty->assign("websites", $websites);
            if ($site_id) {
                $this->display("websiteindex.htm");
            } else {
                $this->display("index.htm");
            }
        }
        exit;
    }


}