<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2016/12/16
 * Time: 11:51
 */

namespace Work\Controller\Manage;


class CompanystuappController extends viewTpl
{
    public $data;
    public $u;
    public $t;
    public $c;
    public $iuser;
    public $Viewhtm;

    //预加载处理类
    function __construct()
    {
        parent::__construct();
        if (!$this->check_login()) {
            $this->LoginView();
        }
        if ($this->check_login()) {
            $this->smarty->assign("iuser", $this->UserLogin);
            $this->iuser = $this->UserLogin;
        }
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
        $this->Viewhtm = $this->router->getController() . "/" . $this->router->getUrl() . ".htm";


        $moduleOne = $this->Show_css->getOne("cms_module", "module_mark='{$this->u}'");
        $this->smarty->assign("moduleOne", $moduleOne);

    }

    //学员管理
    function HomeView()
    {
        $request = Input('get.','','trim,addslashes');

        $datawhere = "1";
        $datatype = array();
        $pageurl = "/{$this->u}/{$this->t}?site_id={$request['site_id']}&school_id={$request['school_id']}";
        if(isset($request['company_id']) && $request['company_id']!=''){
            $datawhere .=" and ssa.company_id='{$request['company_id']}'";
            $datatype['company_id'] = $request['company_id'];
        }

        if(isset($request['school_id']) && $request['school_id']!=''){
            $datawhere .=" and ssa.school_id='{$request['school_id']}'";
            $datatype['school_id'] = $request['school_id'];
        }

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (st.student_cnname like '%{$request['keyword']}%' or st.student_branch like '%{$request['keyword']}%' or st.student_enname like '%{$request['keyword']}%' or ssa.apppropermis_authcode like '%{$request['keyword']}%')";
            $pageurl .= "&keyword={$request['keyword']}";
            $datatype['keyword'] = $request['keyword'];
        }

        if (!isset($request['p'])) {
            $p = '1';
        } else {
            $p = $request['p'];
        }

        $Show_css = $this->Show_css;

        $sql = "select ssa.apppropermislog_id,ca.apppropermis_name,ssa.apppropermis_code,ssa.apppropermislog_createtime,ssa.apppropermislog_endday,ssa.apppropermislog_isenabled,st.student_cnname,st.student_branch,sc.school_cnname,sc.school_branch,ssa.apppropermis_authcode
              from smc_student_apppropermislog as ssa
              left join smc_code_apppropermis as ca on ca.apppropermis_code=ssa.apppropermis_code
              left join smc_student as st on st.student_id=ssa.student_id
              left join smc_school as sc on sc.school_id=ssa.school_id
              where {$datawhere}
              order by ssa.apppropermislog_id DESC ";


        $db_nums = $Show_css->selectOne("select count(ssa.apppropermislog_id) as countnums
              from smc_student_apppropermislog as ssa
              left join smc_code_apppropermis as ca on ca.apppropermis_code=ssa.apppropermis_code
              left join smc_student as st on st.student_id=ssa.student_id
              left join smc_school as sc on sc.school_id=ssa.school_id
              where {$datawhere}");
        $allnum = $db_nums['countnums'];

        $studentList = $Show_css->dbwherePage($sql, $allnum, '10', $pageurl . '&p=', $p, '10', '2');
//
        $studentList = $studentList == false ? array() : $studentList;

        $this->smarty->assign("dataList", $studentList['cont']);
        $this->smarty->assign("pagelist", $studentList['pages']);//筛选信息
        $this->smarty->assign("datatype", $datatype);//筛选信息
    }

    function RelieveAction()
    {
        $request = Input('get.','','trim,addslashes');
        $apppropermislogOne=$this->Show_css->getFieldOne("smc_student_apppropermislog","apppropermis_authcode,company_id","apppropermislog_id='{$request['id']}'");

        $companyOne=$this->Show_css->getFieldOne("gmc_company","company_language","company_id='{$apppropermislogOne['company_id']}'");
        $paramto = array();
        $paramto['branch'] = $apppropermislogOne['apppropermis_authcode'];
        if($companyOne['company_language'] =='tw'){
            $paramto['traditional'] = '1';
            $sendApiSting = request_by_curl("https://stuapi.kidcastleapp.tw/ThirdPartyApi/relieveCode", dataEncode($paramto), "GET", array());
        }else{
            $sendApiSting = request_by_curl("https://stuapi.kidcastle.cn/ThirdPartyApi/relieveCode", dataEncode($paramto), "GET", array());
        }

        $sendApiArray=json_decode($sendApiSting,1);
        if($sendApiArray['error']==0){
            if($this->Show_css->delData("smc_student_apppropermislog", "apppropermislog_id='{$request['id']}'")){
                ajax_return(array('error' => 0,'errortip' => "解除成功!","bakfuntion"=>"okmotify","bakurl"=>"/{$this->u}?site_id={$request['site_id']}"));
            }else{
                ajax_return(array('error' => 1,'errortip' => "解除失败!","bakfuntion"=>"errormotify"));
            }
        }else{
            ajax_return(array('error' => 1,'errortip' => "解除失败!","bakfuntion"=>"errormotify"));
        }
    }


    //魔术方法
    public function __call($name, $arguments)
    {
        $this->smarty->assign("errorTip", "Calling object method '$name' " . implode(', ', $arguments) . "\n");
        $this->Viewhtm = "under.htm";
    }

    //魔术函数
    function __destruct()
    {
        $site_id = Input('get.site_id', 0);

        if ($this->c == 'Wait') {
            $this->smarty->assign("u", $this->router->getController());
            $this->smarty->assign("t", $this->router->getUrl());
            $this->smarty->assign("Viewhtm", $this->Viewhtm);
            $websites = $this->Show_css->getOne("cms_websites", "site_id='{$site_id}'");
            $this->smarty->assign("websites", $websites);
            if ($site_id) {
                if ($this->UserLogin['user_type'] == '1' and $this->UserLogin['user_limitsinc'] == '1') {
                    $moduleList = $this->Show_css->select("SELECT m.* FROM cms_module as m LEFT JOIN cms_moduleapply as a ON a.module_id = m.module_id WHERE a.site_id = '{$site_id}' ORDER BY m.module_weight ASC ");
                } else {
                    $moduleList = $this->Show_css->select("SELECT m.* FROM cms_module as m LEFT JOIN cms_moduleapply as a ON a.module_id = m.module_id LEFT JOIN cms_websites_func as c ON c.site_id = a.site_id and c.module_id = a.module_id WHERE a.site_id = '{$site_id}' and c.user_id = '{$this->UserLogin['user_id']}' ORDER BY m.module_weight ASC ");
                }
                // print_r($moduleList);die;
                $this->smarty->assign("moduleList", $moduleList);
                $this->display("websiteindex.htm");
            } else {
                $this->display("index.htm");
            }
        }
        exit;
    }

}