<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2016/12/16
 * Time: 11:51
 */

namespace Work\Controller\Manage;


class BookintegralController  extends viewTpl{
    public $data;
    public $u;
    public $t;
    public $c;
    public $iuser;
    public $Viewhtm;

    //预加载处理类
    function __construct() {
        parent::__construct ();
        if(!$this->check_login()){
            $this->LoginView();
        }
        if($this->check_login()){
            $this->smarty->assign("iuser", $this->UserLogin);
            $this->iuser = $this->UserLogin;
        }
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
        $this->Viewhtm = $this->router->getController()."/".$this->router->getUrl().".htm";


        $moduleOne = $this->Show_css->getOne("cms_module","module_mark='{$this->u}'");
        $this->smarty->assign("moduleOne",$moduleOne);

    }

    //主页
    function HomeView()
    {
        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        if(!isset($request['p'])){
            $p = '1';
        }else{
            $p= $request['p'];
        }

        $datawhere = " 1 ";
        $pageurl = "/{$this->u}/{$this->t}?";
        if ($request['site_id']) {
            $pageurl .= "&site_id={$request['site_id']}";
        }

        $datatype = array();
        if(isset($request['keyword']) && $request['keyword'] !==''){
            $datawhere .= " and (l.student_branch like '%{$request['keyword']}%' or l.client_cnname like '%{$request['keyword']}%')";
            $pageurl .="&keyword={$request['keyword']}";
            $datatype['keyword'] = $request['keyword'];
        }
        if(isset($request['bookintegral_isaccount']) && $request['bookintegral_isaccount'] !==''){
            $datawhere .= " and l.bookintegral_isaccount = '{$request['bookintegral_isaccount']}'";
            $pageurl .="&bookintegral_isaccount={$request['bookintegral_isaccount']}";
            $datatype['bookintegral_isaccount'] = $request['bookintegral_isaccount'];
        }
        if(isset($request['coursetype_id']) && $request['coursetype_id'] !==''){
            $datawhere .= " and l.coursetype_id = '{$request['coursetype_id']}'";
            $pageurl .="&coursetype_id={$request['coursetype_id']}";
            $datatype['coursetype_id'] = $request['coursetype_id'];
        }


        $sql = "SELECT l.*
                FROM smc_student_bookintegral as l 
                where {$datawhere} order by l.bookintegral_id  DESC";

        $db_nums = $Show_css->selectOne("SELECT COUNT(l.bookintegral_id) as num FROM smc_student_bookintegral as l 
                                where {$datawhere}");//相关条件下的总记录数COUNT(*)

        $allnum = $db_nums['num'];

        $datalist = $Show_css->dbwherePage($sql,$allnum,'10',$pageurl.'&p=',$p,'10','2');

        $smarty->assign("pagelist",$datalist['pages']);//筛选信息

        $smarty->assign("dataList",$datalist['cont']);
        $smarty->assign("datatype",$datatype);
    }


    function accountAction(){
        $request = Input('get.','','trim,addslashes');

        $student_id = $this->Show_css->getFieldOne("smc_student","student_id","student_branch = '{$request['student_branch']}'");

        $data = $this->addStudentIntegral('8888', '0', $student_id['student_id'], '0', $request['integral'], '推荐报读课程奖励', '12357', '推荐报读课程奖励', '', time());

        $datas = array();
        $datas['bookintegral_isaccount'] = '1';
        $datas['bookintegral_accounttime'] = time();
        $this->Show_css->updateData("smc_student_bookintegral","bookintegral_id = '{$request['bookintegral_id']}'",$datas);

        if($data){
            ajax_return(array('error' => 0,'errortip' => '成功',"bakfuntion"=>"successFromTip"));
        }else{
            ajax_return(array('error' => 1,'errortip' => '失败',"bakfuntion"=>"dangerFromTip"));
        }

    }






    //魔术方法
    public function __call($name, $arguments) {
        $this->smarty->assign("errorTip", "Calling object method '$name' ". implode(', ', $arguments). "\n");
        $this->Viewhtm = "under.htm";
    }
    //魔术函数
    function __destruct()
    {
        $site_id = Input('get.site_id',0);

        if ($this->c =='Wait') {
            $this->smarty->assign("u", $this->router->getController());
            $this->smarty->assign("t", $this->router->getUrl());
            $this->smarty->assign("Viewhtm", $this->Viewhtm);
            $websites = $this->Show_css->getOne("cms_websites","site_id='{$site_id}'");
            $this->smarty->assign("websites",$websites);
            if($site_id){
                if($this->UserLogin['user_type'] == '1' and $this->UserLogin['user_limitsinc'] == '1'){
                    $moduleList = $this->Show_css->select("SELECT m.* FROM cms_module as m LEFT JOIN cms_moduleapply as a ON a.module_id = m.module_id WHERE a.site_id = '{$site_id}' ORDER BY m.module_weight ASC ");
                }else{
                    $moduleList = $this->Show_css->select("SELECT m.* FROM cms_module as m LEFT JOIN cms_moduleapply as a ON a.module_id = m.module_id LEFT JOIN cms_websites_func as c ON c.site_id = a.site_id and c.module_id = a.module_id WHERE a.site_id = '{$site_id}' and c.user_id = '{$this->UserLogin['user_id']}' ORDER BY m.module_weight ASC ");
                }
                // print_r($moduleList);die;
                $this->smarty->assign("moduleList", $moduleList);
                $this->display("websiteindex.htm");
            }else{
                $this->display("index.htm");
            }
        }
        exit;
    }

}