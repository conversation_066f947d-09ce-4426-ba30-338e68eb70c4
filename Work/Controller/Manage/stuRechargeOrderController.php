<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn 
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2016/12/16
 * Time: 11:51
 */

namespace Work\Controller\Manage;


class stuRechargeOrderController extends viewTpl{
    public $data;
    public $u;
    public $t;
    public $c;
    public $iuser;
    public $Viewhtm;

    //预加载处理类
    function __construct() {
        parent::__construct ();
        if(!$this->check_login()){
            $this->LoginView();
        }
        if($this->check_login()){
            $this->smarty->assign("iuser", $this->UserLogin);
            $this->iuser = $this->UserLogin;
        }
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
        $this->Viewhtm = $this->router->getController()."/".$this->router->getUrl().".htm";


        $moduleOne = $this->Show_css->getOne("cms_module","module_mark='{$this->u}'");
        $this->smarty->assign("moduleOne",$moduleOne);

    }

    //主页
    function HomeView()
    {
        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        if(!isset($request['p'])){
            $p = '1';
        }else{
            $p= $request['p'];
        }

        $datawhere = " a.company_id=8888 and a.order_type=2 and a.order_status=1";

        $pageurl = "/{$this->u}/{$this->t}?";
        if ($request['site_id']) {
            $pageurl .= "&site_id={$request['site_id']}";
        }

        $datatype = array();
        if(isset($request['keyword']) && $request['keyword'] !==''){
            $datawhere .= " and (a.order_pid LIKE '%{$request['keyword']}%' OR b.student_branch like '%{$request['keyword']}%' or b.student_cnname like '%{$request['keyword']}%')";
            $pageurl .="&keyword={$request['keyword']}";
            $datatype['keyword'] = $request['keyword'];
        }

        $sql = "SELECT a.order_id,a.order_pid,b.student_cnname,b.student_branch,c.school_cnname,a.order_paymentprice 
                from smc_payfee_order as a 
                inner join smc_student as b on b.student_id=a.student_id 
                inner join smc_school as c on c.school_id=a.school_id
                where {$datawhere}
                order by a.order_createtime desc
                ";

        $db_nums = $Show_css->selectOne("SELECT count(a.order_id) as num
                from smc_payfee_order as a 
                inner join smc_student as b on b.student_id=a.student_id 
                inner join smc_school as c on c.school_id=a.school_id
                where {$datawhere}");//相关条件下的总记录数COUNT(*)

        $allnum = $db_nums['num'];

        $datalist = $Show_css->dbwherePage($sql,$allnum,'10',$pageurl.'&p=',$p,'10','2');

        $smarty->assign("pagelist",$datalist['pages']);//筛选信息

        $smarty->assign("dataList",$datalist['cont']);
        $smarty->assign("datatype",$datatype);
    }

//编辑
    function EditView(){
        $smarty = $this->smarty;
        $Show_css = $this->Show_css;
        $datatype = array();

        $request = Input('get.','','trim,addslashes');
        if(isset($request['from']) && $request['from'] =='company' ){
            $datatype['from'] = $request['from'];
        }

        $orderOne=$this->Show_css->getFieldOne("smc_payfee_order","order_paymentprice,order_pid","order_pid='{$request['order_pid']}'");

        $smarty->assign("dataVar",$orderOne);
        $smarty->assign("act","edit");
        $this->Viewhtm = $this->router->getController()."/"."Edit.htm";
    }

    function EditAction()
    {

        $request = Input('post.','','trim,addslashes');

        $bakurl =  "/{$this->u}?site_id={$request['site_id']}";
        if(isset($request['order_pid']) && $request['order_pid'] =='' ){
            $res = array('error' => '1', 'errortip' => '请选择修改订单!',"bakfuntion"=>"errormotify");
            ajax_return($res);
        }

        if(isset($request['order_paymentprice']) && $request['order_paymentprice'] =='' ){
            $res = array('error' => '1', 'errortip' => '请输入充值金额!',"bakfuntion"=>"errormotify");
            ajax_return($res);
        }

        $data  =array();
        $data['order_allprice'] =$request['order_paymentprice'];
        $data['order_paymentprice'] =$request['order_paymentprice'];
        $data['order_arrearageprice']  =$request['order_arrearageprice'];
        if($this->Show_css->updateData('smc_payfee_order',"order_pid='{$request['order_pid']}'",$data)){
            $this->Recordweblog($request['site_id'],$this->Module['module_id'],$this->c,"编辑订单充值金额");
            ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"successFromTip","bakurl"=>$bakurl));
        }else{
            ajax_return(array('error' => 1,'errortip' => "提交失败!","bakfuntion"=>"dangerFromTip"));
        }

    }





    //魔术方法
    public function __call($name, $arguments) {
        $this->smarty->assign("errorTip", "Calling object method '$name' ". implode(', ', $arguments). "\n");
        $this->Viewhtm = "under.htm";
    }
    //魔术函数
    function __destruct()
    {
        $site_id = Input('get.site_id',0);

        if ($this->c =='Wait') {
            $this->smarty->assign("u", $this->router->getController());
            $this->smarty->assign("t", $this->router->getUrl());
            $this->smarty->assign("Viewhtm", $this->Viewhtm);
            $websites = $this->Show_css->getOne("cms_websites","site_id='{$site_id}'");
            $this->smarty->assign("websites",$websites);
            if($site_id){
                if($this->UserLogin['user_type'] == '1' and $this->UserLogin['user_limitsinc'] == '1'){
                    $moduleList = $this->Show_css->select("SELECT m.* FROM cms_module as m LEFT JOIN cms_moduleapply as a ON a.module_id = m.module_id WHERE a.site_id = '{$site_id}' ORDER BY m.module_weight ASC ");
                }else{
                    $moduleList = $this->Show_css->select("SELECT m.* FROM cms_module as m LEFT JOIN cms_moduleapply as a ON a.module_id = m.module_id LEFT JOIN cms_websites_func as c ON c.site_id = a.site_id and c.module_id = a.module_id WHERE a.site_id = '{$site_id}' and c.user_id = '{$this->UserLogin['user_id']}' ORDER BY m.module_weight ASC ");
                }
                $this->smarty->assign("moduleList", $moduleList);
                $this->display("websiteindex.htm");
            }else{
                $this->display("index.htm");
            }
        }
        exit;
    }

}