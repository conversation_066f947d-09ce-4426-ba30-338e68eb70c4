<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2019/12/12
 * Time: 16:34
 */

namespace Work\Controller\Manage;


class TrainingCourseController extends viewTpl{
    public $data;
    public $u;
    public $t;
    public $c;
    public $iuser;
    public $Viewhtm;

    //预加载处理类
    function __construct() {
        parent::__construct ();
        if(!$this->check_login()){
            $this->LoginView();
        }
        if($this->check_login()){
            $this->smarty->assign("iuser", $this->UserLogin);
            $this->iuser = $this->UserLogin;
        }
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
        $this->Viewhtm = $this->router->getController()."/".$this->router->getUrl().".htm";

        $moduleOne = $this->Show_css->getOne("cms_module","module_mark='{$this->u}'");
        $this->smarty->assign("moduleOne",$moduleOne);
    }

    //主页
    function HomeView()
    {
        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        if (!isset($request['p'])) {
            $p = '1';
        } else {
            $p = $request['p'];
        }

        $datawhere = "1";
        $pageurl = "/{$this->u}/{$this->t}?";
        if ($request['site_id']) {
            $pageurl .= "&site_id={$request['site_id']}";
        }

        $datatype = array();
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (c.course_name like '%{$request['keyword']}%')";
            $pageurl .= "&keyword={$request['keyword']}";
            $datatype['keyword'] = $request['keyword'];
        }
        if (isset($request['course_type']) && $request['course_type'] !== '') {
            $datawhere .= " and c.course_type='{$request['course_type']}'";
            $pageurl .= "&course_type={$request['course_type']}";
            $datatype['course_type'] = $request['course_type'];
        }

        $sql = "SELECT c.*,
                (SELECT cr.career_cnname FROM eas_career as cr WHERE cr.career_id=c.career_id) as career_cnname, 
                (SELECT s.stage_cnname FROM eas_career_stage as s WHERE s.stage_id=c.stage_id) as stage_cnname, 
                (SELECT o.openclasstype_cnname FROM eas_code_openclasstype as o WHERE o.openclasstype_id=c.openclasstype_id) as openclasstype_cnname
                FROM eas_course as c WHERE {$datawhere} ORDER BY c.course_id DESC";

        $db_nums = $Show_css->select("SELECT COUNT(c.course_id) FROM eas_course as c WHERE {$datawhere} ");//相关条件下的总记录数COUNT(*)
        $allnum = $db_nums[0][0];

        $datalist = $Show_css->dbwherePage($sql, $allnum, '10', $pageurl . '&p=', $p, '10', '2');
        $smarty->assign("pagelist", $datalist['pages']);//筛选信息
        $smarty->assign("dataList", $datalist['cont']);
        $smarty->assign("datatype", $datatype);
    }

    //添加课程
    function AddView()
    {
        $request = Input('get.','','trim,addslashes');

        $this->smarty->assign("company_id", $request['company_id']);

        $this->smarty->assign("act", "Add");
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        $career = $Show_css->selectClear("SELECT career_id,career_cnname FROM eas_career WHERE company_id = '8888'");
        $smarty->assign("career", $career);

        $stage = $Show_css->selectClear("SELECT stage_id,stage_cnname FROM eas_career_stage WHERE company_id = '8888'");
        $smarty->assign("stage", $stage);

        $openclasstype = $Show_css->selectClear("SELECT openclasstype_id,openclasstype_cnname FROM eas_code_openclasstype WHERE company_id = '8888'");
        $smarty->assign("openclasstype", $openclasstype);

        $this->Viewhtm = $this->router->getController() . "/" . "Manage.htm";
    }

    //提交处理机制
    function AddAction()
    {
        $request = Input('post.','','trim,addslashes');
        if ($request['course_name'] == '') {
            ajax_return(array('error' => 1, 'errortip' => "课程名称不能为空!", "bakfuntion" => "warningFromTip"));
        }
        if ($request['course_type'] == '') {
            ajax_return(array('error' => 1, 'errortip' => "课程类型必须选择!", "bakfuntion" => "warningFromTip"));
        }
        if ($request['course_intro'] == '') {
            ajax_return(array('error' => 1, 'errortip' => "课程介绍不能为空!", "bakfuntion" => "warningFromTip"));
        }
        if ($request['course_img'] == '') {
            ajax_return(array('error' => 1, 'errortip' => "课程图片必须上传!", "bakfuntion" => "warningFromTip"));
        }
        if($this->Show_css->getOne("eas_course","course_name='{$request['course_name']}'")){
            ajax_return(array('error' => 1,'errortip' => "课程名称重复!","bakfuntion"=>"dangerFromTip"));
        }

        $data = array();
        $data['company_id'] = '8888';
        $data['course_name'] = $request['course_name'];
        $data['course_intro'] = $request['course_intro'];
        $data['course_img'] = $request['course_img'];
        $data['course_type'] = $request['course_type'];
        $data['course_recommend'] = $request['course_recommend'];
        $data['course_popular'] = $request['course_popular'];
        if($request['course_type'] == '0'){
            $data['openclasstype_id'] = $request['openclasstype_id'];
            if($data['openclasstype_id'] == ''){
                ajax_return(array('error' => 1, 'errortip' => "所属类型必须选择!", "bakfuntion" => "dangerFromTip"));
            }
        }elseif($request['course_type'] == '1'){
            $data['career_id'] = $request['career_id'];
            $data['stage_id'] = $request['stage_id'];
            if($data['career_id'] == '' || $data['stage_id'] == ''){
                ajax_return(array('error' => 1, 'errortip' => "所属职业/所属阶段必须选择!", "bakfuntion" => "dangerFromTip"));
            }
        }
        $data['course_createtime'] = time();
        if ($this->Show_css->insertData("eas_course", $data)) {
            ajax_return(array('error' => 0, 'errortip' => "新增成功!", "bakfuntion" => "successFromTip", "bakurl" => "/{$this->u}?site_id={$request['site_id']}"));
        } else {
            ajax_return(array('error' => 1, 'errortip' => "新增失败!", "bakfuntion" => "dangerFromTip"));
        }
    }


    //编辑课程
    function EditView()
    {

        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        $smarty->assign("act", "Edit");
        $this->smarty->assign("company_id", $request['company_id']);

        $dataOne = $Show_css->getOne("eas_course", "course_id='{$request['course_id']}'");
        $smarty->assign("dataVar", $dataOne);

        $career = $Show_css->selectClear("SELECT career_id,career_cnname FROM eas_career WHERE company_id='{$request['company_id']}'");
        $smarty->assign("career", $career);

        $stage = $Show_css->selectClear("SELECT stage_id,stage_cnname FROM eas_career_stage WHERE company_id='{$request['company_id']}'");
        $smarty->assign("stage", $stage);

        $adaptive = $Show_css->selectClear("SELECT career_id FROM eas_course_adaptive WHERE course_id='{$request['course_id']}'");
        $smarty->assign("adaptive", $adaptive);

        $openclasstype = $Show_css->selectClear("SELECT openclasstype_id,openclasstype_cnname FROM eas_code_openclasstype WHERE company_id='{$request['company_id']}'");
        $smarty->assign("openclasstype", $openclasstype);

        $this->Viewhtm = $this->router->getController() . "/" . "Manage.htm";
    }


    //提交处理机制
    function EditAction()
    {
        $request = Input('post.','','trim,addslashes');
        if ($request['course_name'] == '') {
            ajax_return(array('error' => 1, 'errortip' => "课程名称不能为空!", "bakfuntion" => "warningFromTip"));
        }
        if ($request['course_type'] == '') {
            ajax_return(array('error' => 1, 'errortip' => "课程类型必须选择!", "bakfuntion" => "warningFromTip"));
        }
        if ($request['course_intro'] == '') {
            ajax_return(array('error' => 1, 'errortip' => "课程介绍不能为空!", "bakfuntion" => "warningFromTip"));
        }
        if ($request['course_img'] == '') {
            ajax_return(array('error' => 1, 'errortip' => "课程图片必须上传!", "bakfuntion" => "warningFromTip"));
        }
        if($this->Show_css->getOne("eas_course","course_id<>'{$request['course_id']}' and course_name='{$request['course_name']}'")){
            ajax_return(array('error' => 1,'errortip' => "课程名称重复!","bakfuntion"=>"dangerFromTip"));
        }

        $data = array();
        $data['company_id'] = '8888';
        $data['course_name'] = $request['course_name'];
        $data['course_intro'] = $request['course_intro'];
        $data['course_img'] = $request['course_img'];
        $data['course_type'] = $request['course_type'];
        $data['course_recommend'] = $request['course_recommend'];
        $data['course_popular'] = $request['course_popular'];
        if($request['course_type'] == '0'){
            $data['openclasstype_id'] = $request['openclasstype_id'];
            if($data['openclasstype_id'] == ''){
                ajax_return(array('error' => 1, 'errortip' => "所属类型必须选择!", "bakfuntion" => "dangerFromTip"));
            }
        }elseif($request['course_type'] == '1'){
            $data['career_id'] = $request['career_id'];
            $data['stage_id'] = $request['stage_id'];
            if($data['career_id'] == '' || $data['stage_id'] == ''){
                ajax_return(array('error' => 1, 'errortip' => "所属职业/所属阶段必须选择!", "bakfuntion" => "dangerFromTip"));
            }
        }
        $data['course_updatatime'] = time();
        if ($this->Show_css->updateData("eas_course", "course_id = '{$request['course_id']}'", $data)) {
            ajax_return(array('error' => 0, 'errortip' => "修改成功!", "bakfuntion" => "successFromTip", "bakurl" => "/{$this->u}?site_id={$request['site_id']}"));
        } else {
            ajax_return(array('error' => 1, 'errortip' => "修改失败!", "bakfuntion" => "errormotify"));
        }
    }

    //删除课程
    function DelAction()
    {
        $request = Input('get.','','trim,addslashes');
        if ($this->Show_css->delData('eas_course', "course_id='{$request['id']}'")) {
            ajax_return(array('error' => 0, 'errortip' => "提交成功!", "bakfuntion" => "okmotify", "bakurl" => "/{$this->u}?site_id={$request['site_id']}"));
        } else {
            ajax_return(array('error' => 1, 'errortip' => "新增失败!", "bakfuntion" => "errormotify"));
        }
    }

    //适配职务
    function SetCareerView(){
        $request = Input('get.','','trim,addslashes');

        $Show_css = $this->Show_css;

        $datalist = $Show_css->selectClear("SELECT c.career_id,c.career_cnname,c.career_branch,
                                            (SELECT COUNT(a.adaptive_id) FROM eas_course_adaptive as a WHERE a.course_id='{$request['course_id']}' AND a.career_id=c.career_id) as thestatus
                                            FROM eas_career as c WHERE c.company_id='{$request['company_id']}'");

        $this->smarty->assign("dataList", $datalist);
        $this->smarty->assign("course_id", $request['course_id']);
    }

    //适配职业
    function AtapplyAction()
    {
        $request = Input('get.','','trim,addslashes');

        $data = array();
        $data['course_id'] = $request['course_id'];
        $data['career_id'] = $request['career_id'];

        if ($this->Show_css->insertData("eas_course_adaptive", $data)) {
            ajax_return(array('error' => 0, 'errortip' => "适配成功!", "bakfuntion" => "successFromTip"));
        } else {
            ajax_return(array('error' => 1, 'errortip' => "适配失败!", "bakfuntion" => "dangerFromTip"));
        }
    }

    //取消适配
    function AtapplydelAction()
    {
        $request = Input('get.','','trim,addslashes');

        $data = array();
        $data['course_id'] = $request['course_id'];
        $data['career_id'] = $request['career_id'];

        if ($this->Show_css->delData('eas_course_adaptive', "course_id='{$request['course_id']}' and career_id='{$request['career_id']}'")) {
            ajax_return(array('error' => 0, 'errortip' => "取消适配成功!", "bakfuntion" => "successFromTip"));
        } else {
            ajax_return(array('error' => 1, 'errortip' => "取消适配失败!", "bakfuntion" => "dangerFromTip"));
        }
    }

    //批量适配/取消适配
    function batchSetQuestionAction()
    {
        $request = Input('post.','','trim,addslashes');
        if (isset($request['tab_list']) && count($request['tab_list']) > 0) {
            if ($request['type'] == 1) {
                foreach ($request['tab_list'] as $v) {
                    if (!$this->Show_css->getFieldOne('eas_course_adaptive', 'adaptive_id', "course_id={$request['course_id']} and career_id='{$v}'")) {
                        $data = array();
                        $data['course_id'] = $request['course_id'];
                        $data['career_id'] = $v;
                        $this->Show_css->insertData('eas_course_adaptive', $data);
                    }
                }
                ajax_return(array('error' => 0, 'errortip' => "批量适配成功!", "bakfuntion" => "refreshpage"));
            } elseif ($request['type'] == 2) {
                foreach ($request['tab_list'] as $v) {
                    $this->Show_css->delData('eas_course_adaptive', "course_id={$request['course_id']}  and career_id='{$v}'");
                }
                ajax_return(array('error' => 0, 'errortip' => "批量取消适配成功!", "bakfuntion" => "refreshpage"));
            }
        }
    }

    //课程章节明细
    function ChapterView(){
        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        if (!isset($request['p'])) {
            $p = '1';
        } else {
            $p = $request['p'];
        }

        $datawhere = "c.course_id='{$request['course_id']}'";
        $pageurl = "/{$this->u}/{$this->t}?course_id={$request['course_id']}";
        if ($request['site_id']) {
            $pageurl .= "&site_id={$request['site_id']}";
        }

        $datatype = array();
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (c.chapter_name like '%{$request['keyword']}%')";
            $pageurl .= "&keyword={$request['keyword']}";
            $datatype['keyword'] = $request['keyword'];
        }

        $sql = "SELECT c.*,
                (SELECT co.course_name FROM eas_course as co WHERE co.course_id=c.course_id) as course_name
                FROM eas_course_chapter as c WHERE {$datawhere} ORDER BY c.chapter_id DESC";

        $db_nums = $Show_css->select("SELECT COUNT(c.chapter_id) FROM eas_course_chapter as c WHERE {$datawhere} ");//相关条件下的总记录数COUNT(*)
        $allnum = $db_nums[0][0];

        $datalist = $Show_css->dbwherePage($sql, $allnum, '10', $pageurl . '&p=', $p, '10', '2');
        $smarty->assign("pagelist", $datalist['pages']);//筛选信息
        $smarty->assign("dataList", $datalist['cont']);
        $smarty->assign("datatype", $datatype);
        $smarty->assign("course_id", $request['course_id']);
    }

    //添加课程章节
    function AddChapterView()
    {
        $request = Input('get.','','trim,addslashes');

        $this->smarty->assign("act", "AddChapter");

        $course = $this->Show_css->selectClear("SELECT course_id,course_name FROM eas_course WHERE company_id = '8888'");
        $this->smarty->assign("course", $course);

        $this->smarty->assign("course_id", $request['course_id']);

        $this->Viewhtm = $this->router->getController() . "/" . "ChapterManage.htm";
    }

    //提交处理机制
    function AddChapterAction()
    {
        $request = Input('post.','','trim,addslashes');
        if ($request['chapter_name'] == '') {
            ajax_return(array('error' => 1, 'errortip' => "章节名称不能为空!", "bakfuntion" => "warningFromTip"));
        }
        if ($request['chapter_sort'] == '') {
            ajax_return(array('error' => 1, 'errortip' => "章节排序不能为空!", "bakfuntion" => "warningFromTip"));
        }
        if($this->Show_css->getOne("eas_course_chapter","chapter_name='{$request['chapter_name']}'")){
            ajax_return(array('error' => 1,'errortip' => "章节名称重复!","bakfuntion"=>"dangerFromTip"));
        }

        $data = array();
        $data['company_id'] = '8888';
        $data['course_id'] = $request['course_id'];
        $data['chapter_name'] = $request['chapter_name'];
        $data['chapter_sort'] = $request['chapter_sort'];
        $data['chapter_createtime'] = time();
        if ($this->Show_css->insertData("eas_course_chapter", $data)) {
            ajax_return(array('error' => 0, 'errortip' => "新增成功!", "bakfuntion" => "successFromTip", "bakurl" => "/{$this->u}/Chapter?site_id={$request['site_id']}&course_id={$request['course_id']}"));
        } else {
            ajax_return(array('error' => 1, 'errortip' => "新增失败!", "bakfuntion" => "dangerFromTip"));
        }
    }

    //编辑课程章节
    function EditChapterView()
    {
        $request = Input('get.','','trim,addslashes');

        $this->smarty->assign("act", "EditChapter");

        $dataOne = $this->Show_css->getOne("eas_course_chapter","chapter_id='{$request['chapter_id']}'");
        $this->smarty->assign("dataVar", $dataOne);

        $course = $this->Show_css->selectClear("SELECT course_id,course_name FROM eas_course WHERE company_id = '8888'");
        $this->smarty->assign("course", $course);

        $this->smarty->assign("course_id", $request['course_id']);

        $this->Viewhtm = $this->router->getController() . "/" . "ChapterManage.htm";
    }

    //提交处理机制
    function EditChapterAction()
    {
        $request = Input('post.','','trim,addslashes');
        if ($request['chapter_name'] == '') {
            ajax_return(array('error' => 1, 'errortip' => "章节名称不能为空!", "bakfuntion" => "warningFromTip"));
        }
        if ($request['chapter_sort'] == '') {
            ajax_return(array('error' => 1, 'errortip' => "章节排序不能为空!", "bakfuntion" => "warningFromTip"));
        }
        if($this->Show_css->getOne("eas_course_chapter","chapter_id<>'{$request['chapter_id']}' and chapter_name='{$request['chapter_name']}'")){
            ajax_return(array('error' => 1,'errortip' => "章节名称重复!","bakfuntion"=>"dangerFromTip"));
        }

        $data = array();
        $data['company_id'] = '8888';
        $data['course_id'] = $request['course_id'];
        $data['chapter_name'] = $request['chapter_name'];
        $data['chapter_sort'] = $request['chapter_sort'];
        $data['chapter_updatatime'] = time();
        if ($this->Show_css->updateData("eas_course_chapter", "chapter_id='{$request['chapter_id']}'", $data)) {
            ajax_return(array('error' => 0, 'errortip' => "编辑成功!", "bakfuntion" => "successFromTip", "bakurl" => "/{$this->u}/Chapter?site_id={$request['site_id']}&course_id={$request['course_id']}"));
        } else {
            ajax_return(array('error' => 1, 'errortip' => "编辑失败!", "bakfuntion" => "dangerFromTip"));
        }
    }

    //删除课程章节
    function DelChapterAction()
    {
        $request = Input('get.','','trim,addslashes');
        if ($this->Show_css->delData('eas_course_chapter', "chapter_id='{$request['id']}'")) {
            ajax_return(array('error' => 0, 'errortip' => "提交成功!", "bakfuntion" => "okmotify", "bakurl" => "/{$this->u}/Chapter?site_id={$request['site_id']}&course_id={$request['course_id']}"));
        } else {
            ajax_return(array('error' => 1, 'errortip' => "删除失败!", "bakfuntion" => "errormotify"));
        }
    }


    //培训视频明细
    function TrainingVideoView(){
        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        if (!isset($request['p'])) {
            $p = '1';
        } else {
            $p = $request['p'];
        }

        $datawhere = "t.chapter_id='{$request['chapter_id']}'";
        $pageurl = "/{$this->u}/{$this->t}?chapter_id={$request['chapter_id']}";
        if ($request['site_id']) {
            $pageurl .= "&site_id={$request['site_id']}";
        }

        $datatype = array();
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (t.trainhour_name like '%{$request['keyword']}%')";
            $pageurl .= "&keyword={$request['keyword']}";
            $datatype['keyword'] = $request['keyword'];
        }
        if (isset($request['trainhour_class']) && $request['trainhour_class'] !== '') {
            $datawhere .= " and t.trainhour_class='{$request['trainhour_class']}'";
            $pageurl .= "&trainhour_class={$request['trainhour_class']}";
            $datatype['trainhour_class'] = $request['trainhour_class'];
        }

        $sql = "SELECT t.*,
                (SELECT c.chapter_name FROM eas_course_chapter as c WHERE c.chapter_id=t.chapter_id) as chapter_name
                FROM eas_course_trainhour as t WHERE {$datawhere} ORDER BY t.trainhour_id DESC";

        $db_nums = $Show_css->select("SELECT COUNT(t.trainhour_id) FROM eas_course_trainhour as t WHERE {$datawhere} ");//相关条件下的总记录数COUNT(*)
        $allnum = $db_nums[0][0];

        $datalist = $Show_css->dbwherePage($sql, $allnum, '10', $pageurl . '&p=', $p, '10', '2');
        $smarty->assign("pagelist", $datalist['pages']);//筛选信息
        $smarty->assign("dataList", $datalist['cont']);
        $smarty->assign("datatype", $datatype);
        $smarty->assign("chapter_id", $request['chapter_id']);
    }

    //添加培训视频
    function AddTrainingVideoView()
    {
        $request = Input('get.','','trim,addslashes');

        $this->smarty->assign("act", "AddTrainingVideo");

        $dataList = $this->Show_css->selectClear("SELECT chapter_id,chapter_name FROM eas_course_chapter WHERE company_id = '8888'");
        $this->smarty->assign("dataList", $dataList);

        $this->smarty->assign("chapter_id", $request['chapter_id']);

        $this->Viewhtm = $this->router->getController() . "/" . "TrainingVideoManage.htm";
    }

    //提交处理机制
    function AddTrainingVideoAction()
    {
        $request = Input('post.','','trim,addslashes');
        if ($request['trainhour_name'] == '') {
            ajax_return(array('error' => 1, 'errortip' => "培训名称不能为空!", "bakfuntion" => "warningFromTip"));
        }
        if ($request['trainhour_class'] == '') {
            ajax_return(array('error' => 1, 'errortip' => "教案模式必须选择!", "bakfuntion" => "warningFromTip"));
        }
        if ($request['trainhour_coverimg'] == '') {
            ajax_return(array('error' => 1, 'errortip' => "教案封面必须上传!", "bakfuntion" => "warningFromTip"));
        }

        $data = array();
        $data['chapter_id'] = $request['chapter_id'];
        $data['trainhour_name'] = $request['trainhour_name'];
        $data['trainhour_coverimg'] = $request['trainhour_coverimg'];
        $data['trainhour_class'] = $request['trainhour_class'];
        if($request['trainhour_class'] == '0') {
            $data['trainhour_fileurl'] = $request['trainhourFileurl'];
            if ($data['trainhour_fileurl'] == '') {
                ajax_return(array('error' => 1, 'errortip' => "文件地址不能为空!", "bakfuntion" => "warningFromTip"));
            }
        }elseif($request['trainhour_class'] == '1'){
            $data['trainhour_fileurl'] = $request['trainhour_fileurl'];
            if ($data['trainhour_fileurl'] == '') {
                ajax_return(array('error' => 1, 'errortip' => "文件地址不能为空!", "bakfuntion" => "warningFromTip"));
            }
        }
        $data['trainhour_createtime'] = time();
        $dataid = $this->Show_css->insertData("eas_course_trainhour",$data);
        if(!$dataid){
            ajax_return(array('error' => 1,'errortip' => "新增失败!","bakfuntion"=>"dangerFromTip"));
        }
        if($request['trainhour_class'] == '2') {
            $list = array();
            if ($request['affix_img'] !== null) {
                foreach ($request['affix_img'] as $k => $v) {
                    $list[$k]['trainhour_id'] = $dataid;
                    $list[$k]['pptpage_imgurl'] = $v;
                    $list[$k]['pptpage_sort'] = $k+1;
                    foreach ($request['affix_thumburl'] as $key => $val) {
                        $list[$key]['pptpage_thumburl'] = $val;
                        $list[$key]['pptpage_time'] = time();
                    }
                    foreach ($request['affix_imgname'] as $key => $val) {
                        $list[$key]['pptpage_name'] = $val;
                    }
                }
                foreach ($list as $v) {
                    $listid = $this->Show_css->insertData("eas_course_trainhour_pptpage", $v);
                    if (!$listid) {
                        ajax_return(array('error' => 1, 'errortip' => "新增照片失败!", "bakfuntion" => "errormotify"));
                    }
                }
            }else{
                ajax_return(array('error' => 1, 'errortip' => "图片地址必须上传!", "bakfuntion" => "warningFromTip"));
            }
        }
        ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"successFromTip","bakurl"=>"/{$this->u}/TrainingVideo?site_id={$request['site_id']}&chapter_id={$request['chapter_id']}"));
    }

    //编辑培训视频
    function EditTrainingVideoView()
    {
        $request = Input('get.','','trim,addslashes');

        $this->smarty->assign("act", "EditTrainingVideo");

        $dataOne = $this->Show_css->getOne("eas_course_trainhour","trainhour_id='{$request['trainhour_id']}'");
        $this->smarty->assign("dataVar", $dataOne);

        $dataList = $this->Show_css->selectClear("SELECT chapter_id,chapter_name FROM eas_course_chapter WHERE company_id = '8888'");
        $this->smarty->assign("dataList", $dataList);

        $imglist = $this->Show_css->selectClear("SELECT * FROM eas_course_trainhour_pptpage WHERE trainhour_id='{$request['trainhour_id']}'");
        $this->smarty->assign("imglist", $imglist);

        $this->smarty->assign("chapter_id", $request['chapter_id']);

        $this->Viewhtm = $this->router->getController() . "/" . "TrainingVideoManage.htm";
    }

    //提交处理机制
    function EditTrainingVideoAction()
    {
        $request = Input('post.','','trim,addslashes');
        if ($request['trainhour_name'] == '') {
            ajax_return(array('error' => 1, 'errortip' => "培训名称不能为空!", "bakfuntion" => "warningFromTip"));
        }
        if ($request['trainhour_class'] == '') {
            ajax_return(array('error' => 1, 'errortip' => "教案模式必须选择!", "bakfuntion" => "warningFromTip"));
        }
        if ($request['trainhour_coverimg'] == '') {
            ajax_return(array('error' => 1, 'errortip' => "教案封面必须上传!", "bakfuntion" => "warningFromTip"));
        }

        $data = array();
        $data['chapter_id'] = $request['chapter_id'];
        $data['trainhour_name'] = $request['trainhour_name'];
        $data['trainhour_coverimg'] = $request['trainhour_coverimg'];
        $data['trainhour_class'] = $request['trainhour_class'];
        if($request['trainhour_class'] == '0') {
            $data['trainhour_fileurl'] = $request['trainhourFileurl'];
            if ($data['trainhour_fileurl'] == '') {
                ajax_return(array('error' => 1, 'errortip' => "文件地址不能为空!", "bakfuntion" => "warningFromTip"));
            }
        }elseif($request['trainhour_class'] == '1'){
            $data['trainhour_fileurl'] = $request['trainhour_fileurl'];
            if ($data['trainhour_fileurl'] == '') {
                ajax_return(array('error' => 1, 'errortip' => "文件地址不能为空!", "bakfuntion" => "warningFromTip"));
            }
        }
        $data['trainhour_updatatime'] = time();
        if(!$this->Show_css->updateData("eas_course_trainhour","trainhour_id='{$request['trainhour_id']}'",$data)){
            ajax_return(array('error' => 1,'errortip' => "编辑失败!","bakfuntion"=>"dangerFromTip"));
        }
        if($request['trainhour_class'] == '2') {
            $this->Show_css->delData("eas_course_trainhour_pptpage","trainhour_id='{$request['trainhour_id']}'");
            $list = array();
            if ($request['affix_img'] !== null) {
                foreach ($request['affix_img'] as $k => $v) {
                    $list[$k]['trainhour_id'] = $request['trainhour_id'];
                    $list[$k]['pptpage_imgurl'] = $v;
                    $list[$k]['pptpage_sort'] = $k+1;
                    foreach ($request['affix_thumburl'] as $key => $val) {
                        $list[$key]['pptpage_thumburl'] = $val;
                        $list[$key]['pptpage_time'] = time();
                    }
                    foreach ($request['affix_imgname'] as $key => $val) {
                        $list[$key]['pptpage_name'] = $val;
                    }
                }
                foreach ($list as $v) {
                    $listid = $this->Show_css->insertData("eas_course_trainhour_pptpage", $v);
                    if (!$listid) {
                        ajax_return(array('error' => 1, 'errortip' => "编辑照片失败!", "bakfuntion" => "errormotify"));
                    }
                }
            }else{
                ajax_return(array('error' => 1, 'errortip' => "图片地址必须上传!", "bakfuntion" => "warningFromTip"));
            }
        }
        ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"successFromTip","bakurl"=>"/{$this->u}/TrainingVideo?site_id={$request['site_id']}&chapter_id={$request['chapter_id']}"));
    }

    //删除培训视频
    function DelTrainingVideoAction()
    {
        $request = Input('get.','','trim,addslashes');

        if($this->Show_css->delData("eas_course_trainhour", "trainhour_id='{$request['id']}'")){
            ajax_return(array('error' => 0,'errortip' => "删除成功!","bakfuntion"=>"okmotify","bakurl"=>"/{$this->u}/TrainingVideo?site_id={$request['site_id']}&chapter_id={$request['chapter_id']}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "删除失败!","bakfuntion"=>"errormotify"));
        }
    }


    //魔术方法
    public function __call($name, $arguments) {
        $this->smarty->assign("errorTip", "Calling object method '$name' ". implode(', ', $arguments). "\n");
        $this->Viewhtm = "under.htm";
    }

    //魔术函数
    function __destruct()
    {
        $site_id = Input('get.site_id',0);

        if ($this->c =='Wait') {
            $this->smarty->assign("u", $this->router->getController());
            $this->smarty->assign("t", $this->router->getUrl());
            $this->smarty->assign("Viewhtm", $this->Viewhtm);
            $websites = $this->Show_css->getOne("cms_websites","site_id='{$site_id}'");
            $this->smarty->assign("websites",$websites);
            if($site_id){
                if($this->UserLogin['user_type'] == '1' and $this->UserLogin['user_limitsinc'] == '1'){
                    $moduleList = $this->Show_css->select("SELECT m.* FROM cms_module as m LEFT JOIN cms_moduleapply as a ON a.module_id = m.module_id WHERE a.site_id = '{$site_id}' ORDER BY m.module_weight ASC ");
                }else{
                    $moduleList = $this->Show_css->select("SELECT m.* FROM cms_module as m LEFT JOIN cms_moduleapply as a ON a.module_id = m.module_id LEFT JOIN cms_websites_func as c ON c.site_id = a.site_id and c.module_id = a.module_id WHERE a.site_id = '{$site_id}' and c.user_id = '{$this->UserLogin['user_id']}' ORDER BY m.module_weight ASC ");
                }
                // print_r($moduleList);die;
                $this->smarty->assign("moduleList", $moduleList);
                $this->display("websiteindex.htm");
            }else{
                $this->display("index.htm");
            }
        }
        exit;
    }
}