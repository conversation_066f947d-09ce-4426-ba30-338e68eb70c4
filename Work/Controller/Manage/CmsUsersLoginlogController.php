<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2019/2/16
 * Time: 11:56
 */

namespace Work\Controller\Manage;


class CmsUsersLoginlogController extends viewTpl
{
    public $data;
    public $u;
    public $t;
    public $c;
    public $icampus;
    public $iuser;
    public $Viewhtm;
    public $Module;
    public $pagestr = '';

    //预加载处理类
    function __construct()
    {
        parent::__construct();
        if (!$this->check_login()) {
            $this->LoginView();
        }
        if ($this->check_login()) {
            $this->smarty->assign("iuser", $this->UserLogin);
            $this->iuser = $this->UserLogin;
        }
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
        $this->Viewhtm = $this->router->getController() . "/" . $this->router->getUrl() . ".htm";
        $moduleOne = $this->Show_css->getOne("cms_module", "module_mark='{$this->u}'");
        $this->smarty->assign("moduleOne", $moduleOne);
        $this->Module = $moduleOne;
    }

    //主页
    function HomeView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $datatype = array();
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        if (!isset($request['p'])) {
            $p = '1';
        } else {
            $p = $request['p'];
        }

        $datawhere = "1 ";
        $pageurl = "/{$this->u}/{$this->t}?site_id={$request['site_id']}";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and ( l.loginlog_lastip like '%{$request['keyword']}%' or u.user_name like '%{$request['keyword']}%' )";
            $pageurl .= "&keyword={$request['keyword']}";
            $datatype['keyword'] = $request['keyword'];
        }

        $sql = "SELECT l.*,u.user_name  
        FROM cms_users_loginlog as l
        LEFT JOIN cms_users as u ON l.user_id = u.user_id 
        where {$datawhere} order by l.loginlog_id DESC";

        $db_nums = $Show_css->selectOne("SELECT COUNT(l.loginlog_id) as countnums FROM cms_users_loginlog as l
        LEFT JOIN cms_users as u ON l.user_id = u.user_id  where {$datawhere}");//相关条件下的总记录数COUNT(*)
        $allnum = $db_nums['countnums'];

        $datalist = $Show_css->dbwherePage($sql, $allnum, '10', $pageurl . '&p=', $p, '15', '2');

        $smarty->assign("pagelist", $datalist['pages']);//筛选信息
        $smarty->assign("dataList", $datalist['cont']);
        $smarty->assign("datatype", $datatype);
    }


    //魔术方法
    public function __call($name, $arguments) {
        $this->smarty->assign("errorTip", "Calling object method '$name' ". implode(', ', $arguments). "\n");

        $this->Viewhtm = "under.htm";
    }

    function __destruct()
    {
        $site_id = Input('get.site_id', 0);

        if ($this->c == 'Wait') {
            $this->smarty->assign("u", $this->router->getController());
            $this->smarty->assign("t", $this->router->getUrl());
            $this->smarty->assign("Viewhtm", $this->Viewhtm);
            $websites = $this->Show_css->getOne("cms_websites", "site_id='{$site_id}'");
            $this->smarty->assign("websites", $websites);
            if ($site_id) {
                $this->display("websiteindex.htm");
            } else {
                $this->display("index.htm");
            }
        }
        exit;
    }


}