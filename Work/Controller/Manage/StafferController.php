<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2016/12/16
 * Time: 11:51
 */

namespace Work\Controller\Manage;


class StafferController  extends viewTpl{
    public $data;
    public $u;
    public $t;
    public $c;
    public $iuser;
    public $Viewhtm;

    //预加载处理类
    function __construct() {
        parent::__construct ();
        if(!$this->check_login()){
            $this->LoginView();
        }
        if($this->check_login()){
            $this->smarty->assign("iuser", $this->UserLogin);
            $this->iuser = $this->UserLogin;
        }
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
        $this->Viewhtm = $this->router->getController()."/".$this->router->getUrl().".htm";


        $moduleOne = $this->Show_css->getOne("cms_module","module_mark='{$this->u}'");
        $this->smarty->assign("moduleOne",$moduleOne);

    }

    //职工管理
    function HomeView(){
        $request = Input('get.','','trim,addslashes');

        $datatype = array();
        $datatype['company_id'] = $request['company_id'];
        $datatype['school_id'] = $request['school_id'];
        $datatype['from'] = $request['from'];
        $datawhere = "1";
        $pageurl = "/{$this->u}/{$this->t}?site_id={$request['site_id']}";
        if(isset($request['keyword']) && $request['keyword'] !==''){
            $datawhere .= " and (s.student_cnname like '%{$request['keyword']}%' or s.student_branch like '%{$request['keyword']}%' or s.student_enname like '%{$request['keyword']}%' )";
            $pageurl .="&keyword={$request['keyword']}";
            $datatype['keyword'] = $request['keyword'];
        }

        if(!isset($request['p'])){
            $p = '1';
        }else{
            $p= $request['p'];
        }

        $Show_css = $this->Show_css;

        $sql =" SELECT  s.student_id,s.student_branch,s.student_cnname,s.student_enname,s.student_sex,s.student_birthday,sf.family_mobile,sf.family_cnname,s.student_idcard,student_createtime
                FROM smc_student_enrolled as se
                left join smc_student as s on se.student_id=s.student_id
                left join smc_student_family as sf on sf.student_id=s.student_id and sf.family_isdefault=1
                WHERE {$datawhere} and se.school_id='{$request['school_id']}' ";


        $db_nums = $Show_css->selectOne("SELECT COUNT(s.student_id) as countnums FROM smc_student_enrolled as se
                left join smc_student as s on se.student_id=s.student_id
                left join smc_student_family as sf on sf.student_id=s.student_id and sf.family_isdefault=1
                WHERE {$datawhere} and se.school_id='{$request['school_id']}'");
        $allnum = $db_nums['countnums'];

        $studentList = $Show_css->dbwherePage($sql,$allnum,'10',$pageurl.'&p=',$p,'10','2');
//
        $studentList = $studentList==false?array():$studentList;

        $this->smarty->assign("dataList",$studentList['cont']);
        $this->smarty->assign("pagelist",$studentList['pages']);//筛选信息
        $this->smarty->assign("datatype",$datatype);//筛选信息
    }

    //增加
    function AddView()
    {
        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $datatype = array();
        $this->smarty->assign("act","Add");
        if(isset($request['school_id']) && $request['school_id'] !==""){
            $datatype['school_id'] = $request['school_id'];

            $schoolOne =$Show_css->getFieldOne('smc_school','school_id,school_cnname,company_id',"school_id='{$request['school_id']}'");
            $datatype['company_id'] = $schoolOne['company_id'];
            $datatype['school_cnname'] = $schoolOne['school_cnname'];
        }

        $this->smarty->assign("datatype",$datatype);
        $this->Viewhtm = $this->router->getController()."/"."Edit.htm";
    }


    //提交处理
    function AddAction()
    {
        $request = Input('post.','','trim,addslashes');

        if(isset($request['student_cnname']) && $request['student_cnname'] =='' ){
            $res = array('error' => '1', 'errortip' => '请填写学员姓名!',"bakfuntion"=>"errormotify");
            ajax_return($res);
        }

        $like=date("Ymd",time());
        $stuInfo=$this->Show_css->selectOne("select student_branch from smc_student where student_branch like '{$like}%' AND LENGTH(student_branch) = '14' order by student_branch DESC limit 0,1");
        if($stuInfo){
            $student_data['student_branch']=$stuInfo['student_branch']+1;
        }else{
            $student_data['student_branch'] =$like.'000001';
        }
        if(isset($request['client_id']) && $request['client_id']!=''){
            $student_data['from_client_id']=$request['client_id'];
        }
        $student_data['student_cnname']=$request['student_cnname'];
        $student_data['student_enname']=$request['student_enname'];
        $student_data['student_sex']=$request['student_sex'];
        $student_data['student_idcard']=$request['student_idcard'];
        $student_data['company_id']=$request['company_id'];
        $student_data['student_birthday']=$request['student_birthday'];
        $student_data['student_createtime']=time();
        $student_data['student_updatatime'] = time();
        if($student_id  = $this->Show_css->insertData("smc_student",$student_data)){
                $en_data = array();
                $en_data['school_id'] = $request['school_id'];
                $en_data['student_id'] = $student_id;
                $en_data['enrolled_createtime'] = time();
                $en_data['enrolled_status'] = 0;
                if ($this->Show_css->insertData("smc_student_enrolled", $en_data)) {
                    /*if(!$this->Show_css->getFieldOne("smc_student_balance","student_id","student_id = '{$student_id}' and school_id = '{$request['school_id']}'")){
                        $schoolOne=$this->Show_css->getFieldOne("smc_school","companies_id","school_id='{$request['school_id']}'");
                        $balance = array();
                        $balance['company_id'] = $request['company_id'];
                        $balance['companies_id'] = $schoolOne['companies_id'];
                        $balance['student_id'] = $student_id;
                        $balance['school_id'] = $request['school_id'];
                        $this->Show_css->insertData("smc_student_balance", $balance);
                    }*/
                }

            ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"successFromTip","bakurl"=>"/{$this->u}?site_id={$request['site_id']}&school_id={$request['school_id']}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "新增失败!","bakfuntion"=>"dangerFromTip"));
        }
    }
    //修改
    function EditView()
    {
        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;
        $datatype = array();
        $sql = "select s.*,
        (select co.company_cnname  from gmc_company as co  where co.company_id = s.company_id) as company_cnname,
        (select r.region_name from smc_code_region as r where r.region_id =s.school_province) as province_name,
        (select r.region_name from smc_code_region as r where r.region_id =s.school_city) as city_name,
        (select r.region_name from smc_code_region as r where r.region_id =s.school_area) as area_name
        from smc_school as s where s.school_id='{$request['school_id']}'  limit 0,1";

        $schoolOne = $Show_css->selectOne($sql);
        if($schoolOne){
            $datatype['company_id'] = $schoolOne['company_id'];
            $datatype['company_cnname'] = $schoolOne['company_cnname'];
        }


        $smarty->assign("act", "Edit");
        $smarty->assign("datatype",$datatype);
        $smarty->assign("dataVar",$schoolOne);

        $this->Viewhtm = $this->router->getController()."/"."Edit.htm";
    }
    //修改处理
    function EditAction()
    {
        $request = Input('post.','','trim,addslashes');
        if($request['school_id'] == '' ){
            ajax_return(array('error' => 1,'errortip' => "请选择学校","bakfuntion"=>"warningFromTip"));
        }

        $data = array();

        $data['school_type'] = $request['school_type'];
//        $data['companies_id'] = $request['companies_id'];
//        $data['school_cmbshopcode'] = $request['school_cmbshopcode'];
//        $data['school_upcshopcode'] = $request['school_upcshopcode'];
        $data['school_cnname'] = $request['school_cnname'];
        $data['school_enname'] = $request['school_enname'];
//        $data['district_id'] = $request['district_id'];
        $data['company_id'] = $request['company_id'];
        $data['school_province'] = $request['school_province'];
        $data['school_city'] = $request['school_city'];
        $data['school_area'] = $request['school_area'];
//        $data['school_istest'] = $request['school_istest'];
        $data['school_address'] = $request['school_address'];
        $data['school_phone'] = $request['school_phone'];
        $data['school_createtime'] = time();

        if($this->Show_css->updateData("smc_school","school_id = '{$request['school_id']}'",$data)){
            ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"okmotify","bakurl"=>"/{$this->u}?site_id={$request['site_id']}&company_id={$request['company_id']}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "新增失败!","bakfuntion"=>"errormotify"));
        }
    }

    //魔术方法
    public function __call($name, $arguments) {
        $this->smarty->assign("errorTip", "Calling object method '$name' ". implode(', ', $arguments). "\n");
        $this->Viewhtm = "under.htm";
    }
    //魔术函数
    function __destruct()
    {
        $site_id = Input('get.site_id',0);

        if ($this->c =='Wait') {
            $this->smarty->assign("u", $this->router->getController());
            $this->smarty->assign("t", $this->router->getUrl());
            $this->smarty->assign("Viewhtm", $this->Viewhtm);
            $websites = $this->Show_css->getOne("cms_websites","site_id='{$site_id}'");
            $this->smarty->assign("websites",$websites);
            if($site_id){
                if($this->UserLogin['user_type'] == '1' and $this->UserLogin['user_limitsinc'] == '1'){
                    $moduleList = $this->Show_css->select("SELECT m.* FROM cms_module as m LEFT JOIN cms_moduleapply as a ON a.module_id = m.module_id WHERE a.site_id = '{$site_id}' ORDER BY m.module_weight ASC ");
                }else{
                    $moduleList = $this->Show_css->select("SELECT m.* FROM cms_module as m LEFT JOIN cms_moduleapply as a ON a.module_id = m.module_id LEFT JOIN cms_websites_func as c ON c.site_id = a.site_id and c.module_id = a.module_id WHERE a.site_id = '{$site_id}' and c.user_id = '{$this->UserLogin['user_id']}' ORDER BY m.module_weight ASC ");
                }
                // print_r($moduleList);die;
                $this->smarty->assign("moduleList", $moduleList);
                $this->display("websiteindex.htm");
            }else{
                $this->display("index.htm");
            }
        }
        exit;
    }

}