<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2019/2/16
 * Time: 11:56
 */

namespace Work\Controller\Manage;


class EditionController extends viewTpl{
    public $data;
    public $u;
    public $t;
    public $c;
    public $icampus;
    public $iuser;
    public $Viewhtm;
    public $Module;

    //预加载处理类
    function __construct() {
        parent::__construct ();
        if(!$this->check_login()){
            $this->LoginView();
        }
        if($this->check_login()){
            $this->smarty->assign("iuser", $this->UserLogin);
            $this->iuser = $this->UserLogin;
        }
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
        $this->Viewhtm = $this->router->getController()."/".$this->router->getUrl().".htm";
        $moduleOne = $this->Show_css->getOne("cms_module","module_mark='{$this->u}'");
        $this->smarty->assign("moduleOne",$moduleOne);
        $this->Module = $moduleOne;
    }

    //主页
    function HomeView()
    {
        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        if(!isset($request['p'])){
            $p = '1';
        }else{
            $p= $request['p'];
        }

        $datawhere = "1";
        $pageurl = "/{$this->u}/{$this->t}?site_id={$request['site_id']}";

        $datatype = array();
        if(isset($request['keyword']) && $request['keyword'] !==''){
            $datawhere .= " and (ie.edition_name like '%{$request['keyword']}%' or ie.edition_code like '%{$request['keyword']}%')";
            $pageurl .="&keyword={$request['keyword']}";
            $datatype['keyword'] = $request['keyword'];
        }

        $sql = "select ie.*
                ,ifnull((select count(distinct e.product_id) from imc_editproapply as e where e.edition_id=ie.edition_id),0) as num
                from imc_edition as ie where {$datawhere}";


        $db_nums = $Show_css->selectOne("SELECT COUNT(ie.edition_id) as countnums FROM imc_edition as ie where {$datawhere}");//相关条件下的总记录数COUNT(*)
        $allnum = $db_nums['countnums'];

        $datalist = $Show_css->dbwherePage($sql,$allnum,'10',$pageurl.'&p=',$p,'10','2');

        $smarty->assign("pagelist",$datalist['pages']);//筛选信息
        $smarty->assign("dataList",$datalist['cont']);
        $smarty->assign("datatype",$datatype);
    }

    function AddView()
    {
        $smarty = $this->smarty;
        $smarty->assign("act", "Add");

        $this->Viewhtm = $this->router->getController() . "/" . "Edit.htm";
    }

    function AddAction()
    {
        $request = Input('post.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $bakurl ="/{$this->u}?site_id={$request['site_id']}";

        if(isset($request['edition_name']) && $request['edition_name'] =='' ){
            $res = array('error' => '1', 'errortip' => '请填写版本名称!',"bakfuntion"=>"errormotify");
            ajax_return($res);
        }

        if(isset($request['edition_code']) && $request['edition_code'] =='' ){
            $res = array('error' => '1', 'errortip' => '请填写版本编号!',"bakfuntion"=>"errormotify");
            ajax_return($res);
        }

        $editionOne=$this->Show_css->getFieldOne("imc_edition","edition_id","(edition_name='{$request['edition_name']}' or edition_code='{$request['edition_code']}')");

        if($editionOne){
            $res = array('error' => '1', 'errortip' => '版本已存在!',"bakfuntion"=>"errormotify");
            ajax_return($res);
        }

        $data = array();
        $data['edition_name'] = $request['edition_name'];
        $data['edition_code'] = $request['edition_code'];

        if($Show_css->insertData("imc_edition",$data)){
            $this->Recordweblog($request['site_id'],$this->Module['module_id'],$this->c,"新增版本");
            ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"successFromTip","bakurl"=>$bakurl));
        }else{
            ajax_return(array('error' => 1,'errortip' => "提交失败!","bakfuntion"=>"dangerFromTip"));
        }
    }

    function EditView(){
        $smarty = $this->smarty;

        $request = Input('get.','','trim,addslashes');
        $editionOne =$this->Show_css->getOne("imc_edition","edition_id='{$request['edition_id']}'");
        $smarty->assign("dataVar",$editionOne);
        $smarty->assign("act","edit");
        $this->Viewhtm = $this->router->getController()."/"."Edit.htm";
    }

    function EditAction()
    {

        $request = Input('post.','','trim,addslashes');

        $bakurl =  "/{$this->u}?site_id={$request['site_id']}";
        if(isset($request['edition_name']) && $request['edition_name'] =='' ){
            $res = array('error' => '1', 'errortip' => '请填写版本名称!',"bakfuntion"=>"errormotify");
            ajax_return($res);
        }

        if(isset($request['edition_code']) && $request['edition_code'] =='' ){
            $res = array('error' => '1', 'errortip' => '请填写版本编号!',"bakfuntion"=>"errormotify");
            ajax_return($res);
        }

        $editionOne=$this->Show_css->getFieldOne("imc_edition","edition_id","(edition_name='{$request['edition_name']}' or edition_code='{$request['edition_code']}') and edition_id<>{$request['edition_id']}");

        if($editionOne){
            $res = array('error' => '1', 'errortip' => '版本已存在!',"bakfuntion"=>"errormotify");
            ajax_return($res);
        }

        $data = array();
        $data['edition_name'] = $request['edition_name'];
        $data['edition_code'] = $request['edition_code'];
        if($this->Show_css->updateData('imc_edition',"edition_id='{$request['edition_id']}'",$data)){
            $this->Recordweblog($request['site_id'],$this->Module['module_id'],$this->c,"编辑版本");
            ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"successFromTip","bakurl"=>$bakurl));
        }else{
            ajax_return(array('error' => 1,'errortip' => "提交失败!","bakfuntion"=>"dangerFromTip"));
        }

    }


    function SetProductView()
    {
        $request = Input('get.','','trim,addslashes');
        $datawhere = '1';

        $sql="SELECT p.*
              ,( SELECT COUNT( e.product_id ) FROM imc_editproapply AS e WHERE e.edition_id = '{$request['edition_id']}' AND p.product_id = e.product_id ) AS status 
              ,(select count(ed.module_id) from imc_editpromodule as ed where ed.edition_id='{$request['edition_id']}' and ed.product_id=p.product_id) as num
                FROM imc_product AS p 
                WHERE {$datawhere} ORDER BY status DESC,p.product_id ASC";

        $dataList = $this->Show_css->selectClear($sql);
        $this->smarty->assign('dataList',$dataList);
        $this->smarty->assign('edition_id',$request['edition_id']);
    }

    function SetProductAction()
    {
        $request = Input('get.','','trim,addslashes');
        $data = array();
        $data['edition_id'] = $request['edition_id'];
        $data['product_id'] = $request['product_id'];
        $this->Show_css->insertData('imc_editproapply',$data);

        $this->Recordweblog($request['site_id'],$this->Module['module_id'],$this->c,"版本{$request['edition_id']}适配产品：{$request['product_id']}");
        ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"successFromTip"));

    }

    function UnSetProductAction()
    {
        $request = Input('get.','','trim,addslashes');
        if($this->Show_css->delData('imc_editproapply',"edition_id={$request['edition_id']}  and product_id={$request['product_id']}")){
            $this->Recordweblog($request['site_id'],$this->Module['module_id'],$this->c,"版本{$request['edition_id']}取消适配产品：{$request['product_id']}");
            ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"successFromTip"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "提交失败!","bakfuntion"=>"dangerFromTip"));
        }
    }

    function batchSetProductAction()
    {
        $request = Input('post.','','trim,addslashes');
        if(isset($request['tab_list']) && count($request['tab_list'])>0){
            if($request['type']==1) {
                foreach($request['tab_list'] as $v) {
                    if(!$this->Show_css->getFieldOne('imc_editproapply','edition_id',"edition_id={$request['edition_id']} and product_id='{$v}'")) {
                        $data = array();
                        $data['edition_id'] = $request['edition_id'];
                        $data['product_id'] = $v;
                        $this->Show_css->insertData('imc_editproapply',$data);
                    }
                    $this->Recordweblog($request['site_id'],$this->Module['module_id'],$this->c,"版本{$request['edition_id']}适配产品：{$request['product_id']}");
                }
                ajax_return(array('error' => 0,'errortip' => "批量设置成功!","bakfuntion"=>"refreshpage"));
            }elseif($request['type']==2) {
                foreach($request['tab_list'] as $v) {
                    $this->Show_css->delData('imc_editproapply',"edition_id={$request['edition_id']}  and product_id='{$v}'");
                    $this->Recordweblog($request['site_id'],$this->Module['module_id'],$this->c,"版本{$request['edition_id']}取消适配产品：{$request['product_id']}");
                }
                ajax_return(array('error' => 0,'errortip' => "批量取消成功!","bakfuntion"=>"refreshpage"));
            }
        }
    }

    function SetModuleView()
    {
        $request = Input('get.','','trim,addslashes');
        $datawhere = '1';

        if(isset($request['father_id']) && $request['father_id']!=''){
            $datawhere.=" and m.father_id='{$request['father_id']}'";
        }else{
            $datawhere.=" and m.father_id='0'";
        }

        $sql="SELECT m.*
              ,( SELECT COUNT( e.product_id ) FROM imc_editpromodule AS e WHERE e.product_id = '{$request['product_id']}' AND e.module_id = m.module_id and e.edition_id='{$request['edition_id']}') AS status
              ,ifnull((select im.module_id from imc_module as im where im.father_id=m.module_id limit 0,1),0) as is_have
              ,(select count(ie.module_id) from imc_editpromodule as ie,imc_module as mo where ie.module_id=mo.module_id and ie.edition_id='{$request['edition_id']}' and ie.product_id='{$request['product_id']}' and mo.father_id=m.module_id) as num
                FROM imc_module AS m 
                WHERE {$datawhere} and m.product_id='{$request['product_id']}'
                ORDER BY m.module_class asc,status DESC,m.module_weight asc,m.module_id ASC";

        $dataList = $this->Show_css->selectClear($sql);
        $this->smarty->assign('dataList',$dataList);
        $this->smarty->assign('product_id',$request['product_id']);
        $this->smarty->assign('edition_id',$request['edition_id']);
    }

    function SetModuleAction()
    {
        $request = Input('get.','','trim,addslashes');

        $data = array();
        $data['module_id'] = $request['module_id'];
        $data['product_id'] = $request['product_id'];
        $data['edition_id'] = $request['edition_id'];
        $this->Show_css->insertData('imc_editpromodule',$data);

        $this->Recordweblog($request['site_id'],$this->Module['module_id'],$this->c,"产品{$request['product_id']}适配模块：{$request['module_id']}");
        ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"successFromTip"));

    }

    function UnSetModuleAction()
    {
        $request = Input('get.','','trim,addslashes');
        if($this->Show_css->delData('imc_editpromodule',"module_id={$request['module_id']} and product_id={$request['product_id']} and edition_id={$request['edition_id']}")){
            $this->Recordweblog($request['site_id'],$this->Module['module_id'],$this->c,"产品{$request['product_id']}取消适配模块：{$request['module_id']}");
            ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"successFromTip"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "提交失败!","bakfuntion"=>"dangerFromTip"));
        }
    }

    function batchSetModuleAction()
    {
        $request = Input('post.','','trim,addslashes');
        if(isset($request['tab_list']) && count($request['tab_list'])>0){
            if($request['type']==1) {
                foreach($request['tab_list'] as $v) {
                    if(!$this->Show_css->getFieldOne('imc_editpromodule','module_id',"product_id={$request['product_id']} and edition_id={$request['edition_id']} and module_id='{$v}'")) {
                        $data = array();
                        $data['product_id'] = $request['product_id'];
                        $data['edition_id'] = $request['edition_id'];
                        $data['module_id'] = $v;
                        $this->Show_css->insertData('imc_editpromodule',$data);
                    }
                    $this->Recordweblog($request['site_id'],$this->Module['module_id'],$this->c,"产品{$request['product_id']}适配模块：{$request['module_id']}");
                }
                ajax_return(array('error' => 0,'errortip' => "批量设置成功!","bakfuntion"=>"refreshpage"));
            }elseif($request['type']==2) {
                foreach($request['tab_list'] as $v) {
                    $this->Show_css->delData('imc_editpromodule',"product_id={$request['product_id']} and edition_id={$request['edition_id']} and module_id='{$v}'");
                    $this->Recordweblog($request['site_id'],$this->Module['module_id'],$this->c,"产品{$request['product_id']}取消适配模块：{$request['module_id']}");
                }
                ajax_return(array('error' => 0,'errortip' => "批量取消成功!","bakfuntion"=>"refreshpage"));
            }
        }
    }

    function SetTwoModuleView()
    {
        $request = Input('get.','','trim,addslashes');
        $datawhere = '1';

        $sql="SELECT m.*
              ,( SELECT COUNT( e.product_id ) FROM imc_editpromodule AS e WHERE e.product_id = '{$request['product_id']}' AND e.module_id = m.module_id and e.edition_id='{$request['edition_id']}') AS status 
                FROM imc_module AS m 
                WHERE {$datawhere} and m.product_id='{$request['product_id']}' and m.father_id='{$request['father_id']}'
                ORDER BY m.module_class asc,status DESC,m.module_weight asc,m.module_id ASC";

        $dataList = $this->Show_css->selectClear($sql);

        $this->smarty->assign('dataList',$dataList);
        $this->smarty->assign('product_id',$request['product_id']);
        $this->smarty->assign('edition_id',$request['edition_id']);
    }



    function DelAction()
    {
        $request = Input('get.','','trim,addslashes');
        $list_id = Input('get.id', 0);

        $applyOne=$this->Show_css->getFieldOne("imc_sales_contract","contract_id","edition_id='{$list_id}'");
        if($applyOne){
            ajax_return(array('error' => 1, 'errortip' => "合同已生成不可删除!", "bakfuntion" => "errormotify"));
        }

        if ($this->Show_css->delData('imc_edition', "edition_id='{$list_id}'")) {
            $this->Recordweblog($request['site_id'],$this->Module['module_id'],$this->c,"删除版本：{$list_id}");
            ajax_return(array('error' => 0, 'errortip' => "提交成功!", "bakfuntion" => "okmotify"));
        } else {
            ajax_return(array('error' => 1, 'errortip' => "新增失败!", "bakfuntion" => "errormotify"));
        }
    }



    function __destruct()
    {
        $site_id = Input('get.site_id',0);

        if ($this->c =='Wait') {
            $this->smarty->assign("u", $this->router->getController());
            $this->smarty->assign("t", $this->router->getUrl());
            $this->smarty->assign("Viewhtm", $this->Viewhtm);
            $websites = $this->Show_css->getOne("cms_websites","site_id='{$site_id}'");
            $this->smarty->assign("websites",$websites);
            if($site_id){
                $this->display("websiteindex.htm");
            }else{
                $this->display("index.htm");
            }
        }
        exit;
    }

}