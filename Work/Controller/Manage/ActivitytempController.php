<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2019/5/22 0022
 * Time: 下午 3:21
 */

namespace Work\Controller\Manage;


class ActivitytempController extends viewTpl{
    public $data;
    public $u;
    public $t;
    public $c;
    public $iuser;
    public $Viewhtm;

    //预加载处理类
    function __construct() {
        parent::__construct ();
        if(!$this->check_login()){
            $this->LoginView();
        }
        if($this->check_login()){
            $this->smarty->assign("iuser", $this->UserLogin);
            $this->iuser = $this->UserLogin;
        }
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
        $this->Viewhtm = $this->router->getController()."/".$this->router->getUrl().".htm";

        $moduleOne = $this->Show_css->getOne("cms_module","module_mark='{$this->u}'");
        $this->smarty->assign("moduleOne",$moduleOne);
    }

    //主页
    function HomeView()
    {
        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        if(!isset($request['p'])){
            $p = '1';
        }else{
            $p= $request['p'];
        }

        $datawhere = "1";
        $pageurl = "/{$this->u}/{$this->t}?";
        if ($request['site_id']) {
            $pageurl .= "&site_id={$request['site_id']}";
        }

        $datatype = array();
        if(isset($request['keyword']) && $request['keyword'] !==''){
            $datawhere .= " and (activitytemp_name like '%{$request['keyword']}%' or activitytemp_theme like '%{$request['keyword']}%' )";
            $pageurl .="&keyword={$request['keyword']}";
            $datatype['keyword'] = $request['keyword'];
        }
		if(isset($request['activitytemp_putaway']) && $request['activitytemp_putaway'] !==''){
			$datawhere .= " and activitytemp_putaway ='{$request['activitytemp_putaway']}' ";
			$pageurl .="&activitytemp_putaway={$request['activitytemp_putaway']}";
			$datatype['activitytemp_putaway'] = $request['activitytemp_putaway'];
		}

        if(isset($request['activitytemp_type']) && $request['activitytemp_type'] !==''){
            $datawhere .= " and activitytemp_type ='{$request['activitytemp_type']}' ";
            $pageurl .="&activitytemp_type={$request['activitytemp_type']}";
            $datatype['activitytemp_type'] = $request['activitytemp_type'];
        }

//        $sql = "SELECT m.*,(select d.module_name from imc_module as d WHERE m.father_id = d.module_id) as father_name  FROM imc_module as m  where {$datawhere} order by module_weight ASC, m.module_id DESC";


        $sql = "SELECT * FROM crm_code_activitytemp where {$datawhere} ORDER BY activitytemp_id DESC";

        $db_nums = $Show_css->selectOne("SELECT COUNT(*) as num FROM crm_code_activitytemp where {$datawhere}");//相关条件下的总记录数COUNT(*)

        ;
        $allnum = $db_nums['num'];

        $datalist = $Show_css->dbwherePage($sql,$allnum,'10',$pageurl.'&p=',$p,'10','2');

        $smarty->assign("pagelist",$datalist['pages']);//筛选信息

        $smarty->assign("dataList",$datalist['cont']);
        $smarty->assign("datatype",$datatype);
    }

    //添加
    function AddView()
    {
        $this->smarty->assign("act","Add");

        $this->Viewhtm = $this->router->getController()."/"."Manage.htm";
    }

    //提交处理机制
    function AddAction()
    {
        $request = Input('post.','','trim,addslashes');
        $data = array();

        if($request['activitytemp_name'] == '' ){
            ajax_return(array('error' => 1,'errortip' => "招生模板名称不能为空!","bakfuntion"=>"warningFromTip"));
        }

        $data['activitytemp_name'] = $request['activitytemp_name'];
        $data['activitytemp_theme'] = $request['activitytemp_theme'];
        $data['activitytemp_url'] = $request['activitytemp_url'];
        $data['activitytemp_styleimg'] = $request['activitytemp_styleimg'];
        $data['activitytemp_bannerimg'] = $request['activitytemp_bannerimg'];
        $dataid = $this->Show_css->insertData("crm_code_activitytemp", $data);
        if($dataid){
            ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"successFromTip","bakurl"=>"/Activitytemp?site_id={$request['site_id']}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "新增失败!","bakfuntion"=>"dangerFromTip"));
        }
    }

    //修改
    function EditView()
    {
        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        $activityOne = $Show_css->getOne("crm_code_activitytemp", "activitytemp_id='{$request['activitytemp_id']}'");
        $smarty->assign("dataVar", $activityOne);

        $this->smarty->assign("act","Edit");
        $this->Viewhtm = $this->router->getController()."/"."Manage.htm";
    }

    //提交处理机制
    function EditAction()
    {
        $request = Input('post.','','trim,addslashes');
        $data = array();

        if($request['activitytemp_name'] == '' ){
            ajax_return(array('error' => 1,'errortip' => "招生模板名称不能为空!","bakfuntion"=>"warningFromTip"));
        }

        $data['activitytemp_name'] = $request['activitytemp_name'];
        $data['activitytemp_theme'] = $request['activitytemp_theme'];
        $data['activitytemp_url'] = $request['activitytemp_url'];
        $data['activitytemp_styleimg'] = $request['activitytemp_styleimg'];
        $data['activitytemp_bannerimg'] = $request['activitytemp_bannerimg'];
        $data['activitytemp_putaway'] = $request['activitytemp_putaway'];
        if($this->Show_css->updateData("crm_code_activitytemp","activitytemp_id='{$request['activitytemp_id']}'", $data)){
            ajax_return(array('error' => 0,'errortip' => "修改成功!","bakfuntion"=>"successFromTip","bakurl"=>"/Activitytemp?site_id={$request['site_id']}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "修改失败!","bakfuntion"=>"dangerFromTip"));
        }
    }

    //删除
    function DelAction()
    {
        $request = Input('get.','','trim,addslashes');

        if($this->Show_css->delData("crm_code_activitytemp", "activitytemp_id='{$request['id']}'")){
            ajax_return(array('error' => 0,'errortip' => "删除成功!","bakfuntion"=>"okmotify","bakurl"=>"/{$this->u}?site_id={$request['site_id']}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "删除失败!","bakfuntion"=>"errormotify"));
        }
    }



    //魔术方法
    public function __call($name, $arguments) {
        $this->smarty->assign("errorTip", "Calling object method '$name' ". implode(', ', $arguments). "\n");
        $this->Viewhtm = "under.htm";
    }
    //魔术函数
    function __destruct()
    {
        $site_id = Input('get.site_id',0);

        if ($this->c =='Wait') {
            $this->smarty->assign("u", $this->router->getController());
            $this->smarty->assign("t", $this->router->getUrl());
            $this->smarty->assign("Viewhtm", $this->Viewhtm);
            $websites = $this->Show_css->getOne("cms_websites","site_id='{$site_id}'");
            $this->smarty->assign("websites",$websites);
            if($site_id){
                if($this->UserLogin['user_type'] == '1' and $this->UserLogin['user_limitsinc'] == '1'){
                    $moduleList = $this->Show_css->select("SELECT m.* FROM cms_module as m LEFT JOIN cms_moduleapply as a ON a.module_id = m.module_id WHERE a.site_id = '{$site_id}' ORDER BY m.module_weight ASC ");
                }else{
                    $moduleList = $this->Show_css->select("SELECT m.* FROM cms_module as m LEFT JOIN cms_moduleapply as a ON a.module_id = m.module_id LEFT JOIN cms_websites_func as c ON c.site_id = a.site_id and c.module_id = a.module_id WHERE a.site_id = '{$site_id}' and c.user_id = '{$this->UserLogin['user_id']}' ORDER BY m.module_weight ASC ");
                }
                // print_r($moduleList);die;
                $this->smarty->assign("moduleList", $moduleList);
                $this->display("websiteindex.htm");
            }else{
                $this->display("index.htm");
            }
        }
        exit;
    }

}
