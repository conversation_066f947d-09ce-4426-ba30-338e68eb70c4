<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/13
 * Time: 23:25
 */

namespace Work\Controller\Manage;


class ApiController {
    public $u;
    public $t;
    public $c;
    public $visitType="api";
    public $Show_css;
    public $result;

    //预加载处理类
    function __construct($visitType="api") {
        $this->visitType = $visitType;
        //数据库操作
        $this->Show_css = new \Dbsqlplay();
    }
    function getThreeLevelView($params=array())
    {
        if($this->visitType == 'api'){
            $request = Input('get.','','trim,addslashes');
        }else{
            $request = $params;
        }
        $column_id = $request['column_id'];
        if($column_id =='0') {
            $result = array();
            $result["error"] = "1";
            $result["errortip"] = '请传入栏目ID信息';
            $result["result"] = array();
            $this->result = $result;
            return $this;
        }

        $threeLevelList = $this->Show_css->select("SELECT c.* FROM cms_column as c where c.column_fatherid = '{$column_id}' and c.column_level='3' order by c.column_id ASC");;
        if($threeLevelList){
            $threeLevelArray = array();
            foreach($threeLevelList as $threeLevelVar){
                $threeLevelOne = array();
                $threeLevelOne['column_id']  = $threeLevelVar['column_id'] ;
                $threeLevelOne['column_name']  = $threeLevelVar['column_name'] ;
                $threeLevelArray[] = $threeLevelOne;
            }

            $result = array();
            $result["error"] = "0";
            $result["errortip"] = '三级栏目信息获取成功';
            $result["result"] = $threeLevelArray;
            $this->result = $result;
            return $this;
        }else{
            $result = array();
            $result["error"] = "1";
            $result["errortip"] = '不存在任何三级栏目信息!';
            $result["result"] = array();
            $this->result = $result;
            return $this;
        }
    }

    //获取台湾的市区 -- 后台暖心店再用
    function getAreaTwView($params=array())
    {
        if($this->visitType == 'api'){
            $request = Input('get.','','trim,addslashes');
        }else{
            $request = $params;
        }
        $city_id = $request['city_id'];
        if($city_id =='0') {
            $result = array();
            $result["error"] = "1";
            $result["errortip"] = '请传入城市ID信息!';
            $result["result"] = array();
            $this->result = $result;
            return $this;
        }

        $areaList = $this->Show_css->select("SELECT r.* FROM smc_code_region as r where r.parent_id = '{$city_id}' order by r.region_id ASC");;
        if($areaList){
            $cityArray = array();
            foreach($areaList as $areaVar){
                $cityOne = array();
                $cityOne['regional_id']  = $areaVar['regional_id'] ;
                $cityOne['regional_name']  = $areaVar['regional_name'] ;
                $cityArray[] = $cityOne;
            }

            $result = array();
            $result["error"] = "0";
            $result["errortip"] = '分类信息获取成功';
            $result["result"] = $cityArray;
            $this->result = $result;
            return $this;
        }else{
            $result = array();
            $result["error"] = "1";
            $result["errortip"] = '不存在任何分类信息';
            $result["result"] = array();
            $this->result = $result;
            return $this;
        }
    }

    function getCityView($params=array())
    {
        if($this->visitType == 'api'){
            $request = Input('get.','','trim,addslashes');
        }else{
            $request = $params;
        }
        $province_id = $request['province_id'];
        if($province_id =='0') {
            $result = array();
            $result["error"] = "1";
            $result["errortip"] = '请传入分类ID信息';
            $result["result"] = array();
            $this->result = $result;
            return $this;
        }

        $cityList = $this->Show_css->select("SELECT r.* FROM myw_regional as r where r.regional_parent = '{$province_id}' and r.regional_leveltype='2' order by r.regional_id ASC");;
        if($cityList){
            $cityArray = array();
            foreach($cityList as $cityVar){
                $cityOne = array();
                $cityOne['regional_id']  = $cityVar['regional_id'] ;
                $cityOne['regional_name']  = $cityVar['regional_name'] ;
                $cityArray[] = $cityOne;
            }

            $result = array();
            $result["error"] = "0";
            $result["errortip"] = '分类信息获取成功';
            $result["result"] = $cityArray;
            $this->result = $result;
            return $this;
        }else{
            $result = array();
            $result["error"] = "1";
            $result["errortip"] = '不存在任何分类信息';
            $result["result"] = array();
            $this->result = $result;
            return $this;
        }
    }

    function getAreaView($params=array())
    {
        if($this->visitType == 'api'){
            $request = Input('get.','','trim,addslashes');
        }else{
            $request = $params;
        }
        $city_id = $request['city_id'];
        if($city_id =='0') {
            $result = array();
            $result["error"] = "1";
            $result["errortip"] = '请传入分类ID信息';
            $result["result"] = array();
            $this->result = $result;
            return $this;
        }

        $areaList = $this->Show_css->select("SELECT r.* FROM myw_regional as r where r.regional_parent = '{$city_id}' and r.regional_leveltype='3' order by r.regional_id ASC");
        if($areaList){
            $areaArray = array();
            foreach($areaList as $areaVar){
                $areaOne = array();
                $areaOne['regional_id']  = $areaVar['regional_id'] ;
                $areaOne['regional_name']  = $areaVar['regional_name'] ;
                $areaArray[] = $areaOne;
            }

            $result = array();
            $result["error"] = "0";
            $result["errortip"] = '分类信息获取成功';
            $result["result"] = $areaArray;
            $this->result = $result;
            return $this;
        }else{
            $result = array();
            $result["error"] = "1";
            $result["errortip"] = '不存在任何分类信息';
            $result["result"] = array();
            $this->result = $result;
            return $this;
        }
    }


    /*产品分类*/
    function getCateView($params=array())
    {
        if($this->visitType == 'api'){
            $request = Input('get.','','trim,addslashes');
        }else{
            $request = $params;
        }
        $procat_id = $request['procat_id'];
        if($procat_id =='0') {
            $result = array();
            $result["error"] = "1";
            $result["errortip"] = '请传入分类ID信息';
            $result["result"] = array();
            $this->result = $result;
            return $this;
        }

        $CateList = $this->Show_css->select("SELECT p.* FROM myw_procat as p where p.father_id = '{$procat_id}' and p.procat_rank='1' order by p.procat_id ASC");
        if( $CateList){
            $areaArray = array();
            foreach( $CateList as $areaVar){
                $areaOne = array();
                $areaOne['procat_id']  = $areaVar['procat_id'] ;
                $areaOne['procat_name']  = $areaVar['procat_name'] ;
                $areaArray[] = $areaOne;
            }

            $result = array();
            $result["error"] = "0";
            $result["errortip"] = '分类信息获取成功';
            $result["result"] = $areaArray;
            $this->result = $result;
            return $this;
        }else{
            $result = array();
            $result["error"] = "1";
            $result["errortip"] = '不存在任何分类信息';
            $result["result"] = array();
            $this->result = $result;
            return $this;
        }
    }


    function getProvinceView(){
        $request = Input('get.','','trim,addslashes');

        $this->visitType = "getProvince";
        $Show_css = $this->Show_css;
        $datawhere = " 1 ";
        if(isset($request['keyword']) && $request['keyword'] !== ''){
            $datawhere .= " and (r.region_name like '%{$request['keyword']}%' or r.region_enname like '%{$request['keyword']}%') ";
        }
        if(isset($request['p']) && $request['p'] !== ''){
            $page = $request['p'];
        }else{
            $page = '1';
        }
        if(isset($request['num']) && $request['num'] !== ''){
            $num = $request['num'];
        }else{
            $num = '20';
        }
        $pagestart = ($page-1)*$num;
        $itemList = $Show_css->selectClear("SELECT r.region_id,r.region_name FROM smc_code_region AS r
 where {$datawhere} and r.parent_id=1 ORDER BY r.region_sort ASC limit {$pagestart},{$num} ");

        if($itemList){
            $result = array();
            $result["list"] = $itemList;


            $res = array('error' => '0', 'errortip' => '获取省信息', 'result' => $result);

            ajax_return($res);
        }else{
            $data=array();
            $result["list"] = $data;
            $res = array('error' => '1', 'errortip' => '暂无省信息', 'result' => $result);
            ajax_return($res);
        }
    }

    function getProvinceCityView(){
        $request = Input('get.','','trim,addslashes');
        $this->visitType = "getProvinceCity";
        $Show_css = $this->Show_css;
        $datawhere = " 1 ";
        if(isset($request['keyword']) && $request['keyword'] !== ''){
            $datawhere .= " and (r.region_name like '%{$request['keyword']}%' or r.region_enname like '%{$request['keyword']}%') ";
        }
        if(isset($request['region_id']) && $request['region_id'] !== '0' && $request['region_id'] !== ''){
            $datawhere .= " and r.parent_id = '{$request['region_id']}'";
        }
        if(isset($request['p']) && $request['p'] !== ''){
            $page = $request['p'];
        }else{
            $page = '1';
        }
        if(isset($request['num']) && $request['num'] !== ''){
            $num = $request['num'];
        }else{
            $num = '20';
        }
        $pagestart = ($page-1)*$num;
        $itemList = $Show_css->selectClear("SELECT r.region_id,r.region_name FROM smc_code_region AS r
 where {$datawhere} ORDER BY r.region_sort ASC limit {$pagestart},{$num} ");
        if($itemList){
            $result = array();
            $result["list"] = $itemList;
            $res = array('error' => '0', 'errortip' => '获取市信息', 'result' => $result);
            ajax_return($res);
        }else{
            $data=array();
            $result["list"] = $data;
            $res = array('error' => '1', 'errortip' => '暂无市信息', 'result' => $result);
            ajax_return($res);
        }
    }

    function getProvinceAreaView(){
        $request = Input('get.','','trim,addslashes');
        $this->visitType = "getProvinceArea";
        $Show_css = $this->Show_css;
        $datawhere = " 1 ";
        if(isset($request['keyword']) && $request['keyword'] !== ''){
            $datawhere .= " and (r.region_name like '%{$request['keyword']}%' or r.region_enname like '%{$request['keyword']}%') ";
        }
        if(isset($request['region_id']) && $request['region_id'] !== '0' && $request['region_id'] !== ''){
            $datawhere .= " and r.parent_id = '{$request['region_id']}'";
        }
        if(isset($request['p']) && $request['p'] !== ''){
            $page = $request['p'];
        }else{
            $page = '1';
        }
        if(isset($request['num']) && $request['num'] !== ''){
            $num = $request['num'];
        }else{
            $num = '20';
        }
        $pagestart = ($page-1)*$num;
        $itemList = $Show_css->selectClear("SELECT r.region_id,r.region_name FROM smc_code_region AS r
 where {$datawhere} ORDER BY r.region_sort ASC limit {$pagestart},{$num} ");


        if($itemList){
            $result = array();
            $result["list"] = $itemList;
            $res = array('error' => '0', 'errortip' => '获取区信息', 'result' => $result);
            ajax_return($res);
        }else{
            $data=array();
            $result["list"] = $data;
            $res = array('error' => '1', 'errortip' => '暂无区信息', 'result' => $result);
            ajax_return($res);
        }
    }
    function getClientView(){
        $itemList = $this->Show_css->selectClear("SELECT c.client_id,c.client_mobile,c.client_cnname,c.client_source,c.channel_id,COUNT(c.client_id) as cnums
FROM crm_client as c WHERE c.company_id = '8888' GROUP BY c.client_mobile HAVING cnums > 1");
        if($itemList){
            foreach($itemList as $itemOne){
                if(!$this->Show_css->selectOne("SELECT c.track_id FROM crm_client_track as c WHERE c.client_id = '{$itemOne['client_id']}' and c.marketer_id <> '0' and c.marketer_id <> '13'")
                    && $itemOne['channel_id'] !=='120' && $itemOne['channel_id'] !=='127'){
                    print_r($itemOne);
                    $this->Show_css->delData("crm_client","client_id = '{$itemOne['client_id']}'");
                    echo "删除ID:{$itemOne['client_id']}<br />";
                }
            }
        }
    }

    function getCompanyView(){
        $request  = Input("get.","","trim");
        $this->visitType = 'api';
        $datawhere ='1';
        if(isset($request['keyword']) && $request['keyword'] !== ''){
            $datawhere .=" and c.company_cnname like '%{$request['keyword']}%'";
        }
        $companyArray =  $this->Show_css->selectClear("SELECT c.company_id,c.company_cnname FROM gmc_company as c WHERE {$datawhere} order by company_id ASC limit 0,50   ");
        $result = array();
        $result['list'] =$companyArray;
        $res = array('error' => '0', 'errortip' => '获取集团信息', 'result' => $result);
       $this->result = $res;
    }

    function getCourseView(){
        $request  = Input("get.","","trim");
        $this->visitType = 'api';
        $datawhere ='1';
        if(isset($request['keyword']) && $request['keyword'] !== ''){
            $datawhere .=" and (c.course_cnname like '%{$request['keyword']}%' or  course_branch like '%{$request['keyword']}%')";
        }
        if(isset($request['company_id']) && $request['company_id'] !== ''){
            $datawhere .=" and c.company_id ='{$request['company_id']}'";
        }
        $companyArray =  $this->Show_css->selectClear("SELECT c.course_id,c.course_cnname FROM smc_course as c WHERE {$datawhere} order by course_id ASC limit 0,50   ");
        $result = array();
        $result['list'] =$companyArray;
        $res = array('error' => '0', 'errortip' => '获取smc课程信息', 'result' => $result);
        $this->result = $res;
    }


    function differenceView(){
        $schoolArray =  $this->Show_css->selectClear("SELECT c.school_id FROM smc_fee_warehouse_apply as c WHERE c.warehouse_id = '18'");
        print_r($schoolArray);
        $differenceArray = array();
        if($schoolArray){
            foreach($schoolArray as $key=>$schoolOne){
                $courseArray = array();
                $courseList = $this->Show_css->selectClear("SELECT
p.course_id,p.tuition_id
FROM
	smc_fee_pricing_apply AS a,
	smc_fee_pricing_tuition AS p,smc_fee_pricing as fp
WHERE
	a.pricing_id = p.pricing_id and a.pricing_id = fp.pricing_id
AND fp.agreement_id = '188' AND a.school_id = '{$schoolOne['school_id']}'");
                if($courseList){
                    foreach($courseList as $courseOne){
                        $courseArray[$courseOne['tuition_id']] = $courseOne['course_id'];
                    }
                }
                print_r($courseArray);
                if($key == 0){
                    $differenceArray = $courseArray;
                }else{
                    $differenceArray = array_intersect_assoc($differenceArray,$courseArray);
                }
            }
            print_r($differenceArray);
        }
    }

    //结尾函数
    public function exitfuc(){
        if($this->visitType == 'api'){
            $json_play = new \Webjson();

            exit($json_play->encode($this->result));
        }else{
            return $this->result;
        }
    }
    //结尾魔术函数
    function __destruct()
    {
        if($this->visitType == 'api'){
            $json_play = new \Webjson();
            exit($json_play->encode($this->result));

        }else{
            return $this->result;
        }
    }
}
