<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2019/11/27
 * Time: 16:40
 */

namespace Work\Controller\Manage;


class LessonPlanManageController extends viewTpl{
    public $data;
    public $u;
    public $t;
    public $c;
    public $iuser;
    public $Viewhtm;

    //预加载处理类
    function __construct() {
        parent::__construct ();
        if(!$this->check_login()){
            $this->LoginView();
        }
        if($this->check_login()){
            $this->smarty->assign("iuser", $this->UserLogin);
            $this->iuser = $this->UserLogin;
        }
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
        $this->Viewhtm = $this->router->getController()."/".$this->router->getUrl().".htm";

        $moduleOne = $this->Show_css->getOne("cms_module","module_mark='{$this->u}'");
        $this->smarty->assign("moduleOne",$moduleOne);
    }

    //主页
    function HomeView(){
        $request = Input('get.','','trim,addslashes');
        $smarty = $this->smarty;

        if(!isset($request['p'])){
            $p = '1';
        }else{
            $p= $request['p'];
        }

        $datawhere = "1";
        $pageurl = "/{$this->u}/{$this->t}?";
        if ($request['site_id']) {
            $pageurl .= "&site_id={$request['site_id']}";
        }

        $datatype = array();
        if(isset($request['keyword']) && $request['keyword'] !==''){
            $datawhere .= " and (classcode_name like '%{$request['keyword']}%' or classcode_branch like '%{$request['keyword']}%')";
            $pageurl .="&keyword={$request['keyword']}";
            $datatype['keyword'] = $request['keyword'];
        }

        $sql = "SELECT * FROM eas_classcode WHERE {$datawhere}";

        $db_nums = $this->Show_css->select("SELECT COUNT(*) FROM eas_classcode where {$datawhere}");//相关条件下的总记录数COUNT(*)
        $allnum = $db_nums[0][0];

        $datalist = $this->Show_css->dbwherePage($sql,$allnum,'10',$pageurl.'&p=',$p,'10','2');

        $smarty->assign("pagelist",$datalist['pages']);//筛选信息

        $smarty->assign("dataList",$datalist['cont']);
        $smarty->assign("datatype",$datatype);
    }

    //新增培训课程
    function AddView(){
        $this->smarty->assign("act","Add");

        $this->Viewhtm = $this->router->getController()."/"."Manage.htm";
    }

    //提交处理机制
    function AddAction(){
        $request = Input('post.','','trim,addslashes');
        if($request['catcode_branch'] == ''){
            ajax_return(array('error' => 1,'errortip' => "班种编号不能为空!","bakfuntion"=>"dangerFromTip"));
        }
        if($request['ClasscodeBranch'] == ''){
            ajax_return(array('error' => 1,'errortip' => "班别编号不能为空!","bakfuntion"=>"dangerFromTip"));
        }
        if($request['classcode_name'] == ''){
            ajax_return(array('error' => 1,'errortip' => "班别名称不能为空!","bakfuntion"=>"dangerFromTip"));
        }
        if($request['classcode_hour'] == ''){
            ajax_return(array('error' => 1,'errortip' => "班别课时数量不能为空!","bakfuntion"=>"dangerFromTip"));
        }
        if($this->Show_css->getOne("eas_classcode","classcode_branch='{$request['ClasscodeBranch']}'")){
            ajax_return(array('error' => 1,'errortip' => "班别编号重复，请重新填写!","bakfuntion"=>"dangerFromTip"));
        }
        $data = array();
        $data['company_id'] = '1001';
        $data['catcode_branch'] = $request['catcode_branch'];
        $data['classcode_branch'] = $request['ClasscodeBranch'];
        $data['classcode_name'] = $request['classcode_name'];
        $data['classcode_hour'] = $request['classcode_hour'];
        $data['classcode_isopen'] = $request['classcode_isopen'];
        if($this->Show_css->insertData("eas_classcode",$data)){
            ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"successFromTip","bakurl"=>"/{$this->u}?site_id={$request['site_id']}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "新增失败!","bakfuntion"=>"errormotify"));
        }
    }

    //编辑资料
    function EditView(){
        $request = Input('get.','','trim,addslashes');

        $this->smarty->assign("act","Edit");

        $dataOne = $this->Show_css->getOne("eas_classcode","classcode_id='{$request['classcode_id']}'");
        $this->smarty->assign("dataVar", $dataOne);

        $this->Viewhtm = $this->router->getController()."/"."Manage.htm";
    }

    //提交处理机制
    function EditAction(){
        $request = Input('post.','','trim,addslashes');
        if($request['catcode_branch'] == ''){
            ajax_return(array('error' => 1,'errortip' => "班种编号不能为空!","bakfuntion"=>"dangerFromTip"));
        }
        if($request['classcode_branch'] == ''){
            ajax_return(array('error' => 1,'errortip' => "班别编号不能为空!","bakfuntion"=>"dangerFromTip"));
        }
        if($request['classcode_name'] == ''){
            ajax_return(array('error' => 1,'errortip' => "班别名称不能为空!","bakfuntion"=>"dangerFromTip"));
        }
        if($request['classcode_hour'] == ''){
            ajax_return(array('error' => 1,'errortip' => "班别课时数量不能为空!","bakfuntion"=>"dangerFromTip"));
        }
        if($this->Show_css->selectOne("SELECT t.* FROM eas_classcode as c LEFT JOIN eas_teachhour as t ON t.company_id = c.company_id AND t.classcode_branch = c.classcode_branch WHERE c.classcode_id='{$request['classcode_id']}'")){
            $classcode = $this->Show_css->getOne("eas_classcode","classcode_id='{$request['classcode_id']}'");
            if($classcode['classcode_branch'] != $request['classcode_branch']){
                ajax_return(array('error' => 1,'errortip' => "已生成班别课时，不能修改班别编号!","bakfuntion"=>"errormotify"));
            }
        }
        if($this->Show_css->getOne("eas_classcode","classcode_id<>'{$request['classcode_id']}' and classcode_branch='{$request['classcode_branch']}'")){
            ajax_return(array('error' => 1,'errortip' => "班别编号重复，请重新填写!","bakfuntion"=>"dangerFromTip"));
        }

        $data = array();
        $data['company_id'] = '1001';
        $data['catcode_branch'] = $request['catcode_branch'];
        $data['classcode_branch'] = $request['classcode_branch'];
        $data['classcode_name'] = $request['classcode_name'];
        $data['classcode_hour'] = $request['classcode_hour'];
        $data['classcode_isopen'] = $request['classcode_isopen'];
        if($this->Show_css->updateData("eas_classcode","classcode_id='{$request['classcode_id']}'",$data)){
            ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"successFromTip","bakurl"=>"/{$this->u}?site_id={$request['site_id']}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "编辑失败!","bakfuntion"=>"errormotify"));
        }
    }

    //删除
    function DelAction()
    {
        $request = Input('get.','','trim,addslashes');

        if($this->Show_css->delData("eas_classcode", "classcode_id='{$request['classcode_id']}'")){
            ajax_return(array('error' => 0,'errortip' => "删除成功!","bakfuntion"=>"okmotify","bakurl"=>"/{$this->u}?site_id={$request['site_id']}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "删除失败!","bakfuntion"=>"errormotify"));
        }
    }

    //班别课时明细
    function ClassCodeHourView(){
        $request = Input('get.','','trim,addslashes');
        $smarty = $this->smarty;

        if(!isset($request['p'])){
            $p = '1';
        }else{
            $p= $request['p'];
        }

        $datawhere = "company_id = '{$request['company_id']}' and classcode_branch='{$request['classcode_branch']}'";
        $pageurl = "/{$this->u}/{$this->t}?classcode_branch={$request['classcode_branch']}";
        if ($request['site_id']) {
            $pageurl .= "&site_id={$request['site_id']}";
        }

        $datatype = array();
        if(isset($request['keyword']) && $request['keyword'] !==''){
            $datawhere .= " and (teachhour_name like '%{$request['keyword']}%' or teachhour_branch like '%{$request['keyword']}%')";
            $pageurl .="&keyword={$request['keyword']}";
            $datatype['keyword'] = $request['keyword'];
        }

        $sql = "SELECT * FROM eas_teachhour WHERE {$datawhere} ORDER BY teachhour_sort ASC ";

        $db_nums = $this->Show_css->select("SELECT COUNT(*) FROM eas_teachhour where {$datawhere}");//相关条件下的总记录数COUNT(*)
        $allnum = $db_nums[0][0];

        $datalist = $this->Show_css->dbwherePage($sql,$allnum,'10',$pageurl.'&p=',$p,'10','2');

        $smarty->assign("pagelist",$datalist['pages']);//筛选信息

        $smarty->assign("dataList",$datalist['cont']);
        $smarty->assign("datatype",$datatype);
        $smarty->assign("classcode_branch",$request['classcode_branch']);
    }

    //添加
    function HourAddView(){
        $request = Input('get.','','trim,addslashes');

        $this->smarty->assign("act","HourAdd");

        $this->smarty->assign("classcode_branch", $request['classcode_branch']);

        $this->Viewhtm = $this->router->getController()."/"."HourManage.htm";
    }

    //提交处理明细
    function HourAddAction(){
        $request = Input('post.','','trim,addslashes');
        if($request['teachhour_branch'] == ''){
            ajax_return(array('error' => 1,'errortip' => "课时编号不能为空!","bakfuntion"=>"dangerFromTip"));
        }
        if($request['teachhour_name'] == ''){
            ajax_return(array('error' => 1,'errortip' => "课时名称不能为空!","bakfuntion"=>"dangerFromTip"));
        }
        if($request['teachhour_sort'] == ''){
            ajax_return(array('error' => 1,'errortip' => "课时排序不能为空!","bakfuntion"=>"dangerFromTip"));
        }

        $data = array();
        $data['company_id'] = '1001';
        $data['classcode_branch'] = $request['classcode_branch'];
        $data['teachhour_branch'] = $request['teachhour_branch'];
        $data['teachhour_name'] = $request['teachhour_name'];
        $data['teachhour_sort'] = $request['teachhour_sort'];
        if($this->Show_css->insertData("eas_teachhour",$data)){
            ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"successFromTip","bakurl"=>"/{$this->u}/ClassCodeHour?site_id={$request['site_id']}&classcode_branch={$request['classcode_branch']}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "添加失败!","bakfuntion"=>"errormotify"));
        }
    }

    //编辑资料
    function HourEditView(){
        $request = Input('get.','','trim,addslashes');

        $this->smarty->assign("act","HourEdit");

        $dataOne = $this->Show_css->getOne("eas_teachhour","teachhour_id='{$request['teachhour_id']}'");
        $this->smarty->assign("dataVar", $dataOne);
        $this->smarty->assign("classcode_branch", $dataOne['classcode_branch']);

        $this->Viewhtm = $this->router->getController()."/"."HourManage.htm";
    }

    //编辑班别课时明细
    function HourEditAction(){
        $request = Input('post.','','trim,addslashes');
        if($request['teachhour_branch'] == ''){
            ajax_return(array('error' => 1,'errortip' => "课时编号不能为空!","bakfuntion"=>"dangerFromTip"));
        }
        if($request['teachhour_name'] == ''){
            ajax_return(array('error' => 1,'errortip' => "课时名称不能为空!","bakfuntion"=>"dangerFromTip"));
        }
        if($request['teachhour_sort'] == ''){
            ajax_return(array('error' => 1,'errortip' => "课时排序不能为空!","bakfuntion"=>"dangerFromTip"));
        }

        $data = array();
        $data['company_id'] = '1001';
        $data['classcode_branch'] = $request['classcode_branch'];
        $data['teachhour_branch'] = $request['teachhour_branch'];
        $data['teachhour_name'] = $request['teachhour_name'];
        $data['teachhour_sort'] = $request['teachhour_sort'];
        if($this->Show_css->updateData("eas_teachhour","teachhour_id='{$request['teachhour_id']}'",$data)){
            ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"successFromTip","bakurl"=>"/{$this->u}/ClassCodeHour?site_id={$request['site_id']}&classcode_branch={$request['classcode_branch']}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "编辑失败!","bakfuntion"=>"errormotify"));
        }
    }

    //更新课时数据
    function UpdateClassDataAction(){
        $request = Input('get.','','trim,addslashes');

        $ClasscodeOne = $this->Show_css->getOne("eas_classcode", "classcode_branch='{$request['classcode_branch']}'");
        if($ClasscodeOne['classcode_hour'] == '0'){
            ajax_return(array('error' => 1,'errortip' => "课时数为0不可以自动生成!","bakfuntion"=>"errormotify"));
        }else{
            for($hour=1;$hour<=$ClasscodeOne['classcode_hour'];$hour++){
                if(strstr($ClasscodeOne['catcode_branch'],"060")){
                    $hourName = "Week {$hour}";
                }else{
                    $hourName = "Lesson {$hour}";
                }

                if(!$this->Show_css->getFieldOne("eas_teachhour","teachhour_id", "classcode_branch='{$ClasscodeOne['classcode_branch']}' and teachhour_name = '{$hourName}'")){
                    $data = array();
                    $data['company_id'] = '1001';
                    $data['classcode_branch'] = $ClasscodeOne['classcode_branch'];
                    $data['teachhour_branch'] = $ClasscodeOne['classcode_branch'] .'-'. $hour;
                    $data['teachhour_name'] = $hourName;
                    $data['teachhour_sort'] = $hour;
                    $this->Show_css->insertData("eas_teachhour",$data);
                }
            }
            ajax_return(array('error' => 0,'errortip' => "自动生成成功!","bakfuntion"=>"okmotify"));
        }

    }

    //删除
    function HourDelAction()
    {
        $request = Input('get.','','trim,addslashes');

        if($this->Show_css->delData('eas_teachhour',"teachhour_id='{$request['id']}'")){
            ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"okmotify","bakurl"=>"/{$this->u}/ClassCodeHour?site_id={$request['site_id']}&classcode_branch={$request['classcode_branch']}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "提交失败!","bakfuntion"=>"errormotify"));
        }
    }

    //教案明细管理
    function TeachPlanView(){
        $request = Input('get.','','trim,addslashes');
        $smarty = $this->smarty;

        if(!isset($request['p'])){
            $p = '1';
        }else{
            $p= $request['p'];
        }

        $datawhere = "classcode_branch='{$request['classcode_branch']}' and teachhour_branch='{$request['teachhour_branch']}'";
        $pageurl = "/{$this->u}/{$this->t}?classcode_branch={$request['classcode_branch']}&teachhour_branch={$request['teachhour_branch']}";
        if ($request['site_id']) {
            $pageurl .= "&site_id={$request['site_id']}";
        }

        $datatype = array();
        if(isset($request['keyword']) && $request['keyword'] !==''){
            $datawhere .= " and (teachplan_name like '%{$request['keyword']}%')";
            $pageurl .="&keyword={$request['keyword']}";
            $datatype['keyword'] = $request['keyword'];
        }

        if(isset($request['teachplan_class']) && $request['teachplan_class'] !== ''){
            $datawhere .= " and teachplan_class='{$request['teachplan_class']}'";
            $pageurl .="&teachplan_class={$request['teachplan_class']}";
            $datatype['teachplan_class'] = $request['teachplan_class'];
        }

        $sql = "SELECT * FROM eas_teachhour_teachplan WHERE {$datawhere} ORDER BY teachplan_id DESC";

        $db_nums = $this->Show_css->select("SELECT COUNT(*) FROM eas_teachhour_teachplan where {$datawhere}");//相关条件下的总记录数COUNT(*)
        $allnum = $db_nums[0][0];

        $datalist = $this->Show_css->dbwherePage($sql,$allnum,'10',$pageurl.'&p=',$p,'10','2');

        $smarty->assign("pagelist",$datalist['pages']);//筛选信息

        $smarty->assign("dataList",$datalist['cont']);
        $smarty->assign("datatype",$datatype);
        $smarty->assign("classcode_branch",$request['classcode_branch']);
        $smarty->assign("teachhour_branch",$request['teachhour_branch']);
    }

    //新增教案明细
    function TeachPlanAddView(){
        $request = Input('get.','','trim,addslashes');

        $this->smarty->assign("act","TeachPlanAdd");

        $this->smarty->assign("classcode_branch",$request['classcode_branch']);
        $this->smarty->assign("teachhour_branch",$request['teachhour_branch']);

        $this->Viewhtm = $this->router->getController()."/"."TeachPlanManage.htm";
    }

    //提交处理机制
    function TeachPlanAddAction(){
        $request = Input('post.','','trim,addslashes');
        if($request['teachplan_name'] == ''){
            ajax_return(array('error' => 1,'errortip' => "教案名称不能为空!","bakfuntion"=>"dangerFromTip"));
        }

        $data = array();
        $data['company_id'] = '1001';
        $data['classcode_branch'] = $request['classcode_branch'];
        $data['teachhour_branch'] = $request['teachhour_branch'];
        $data['teachplan_name'] = $request['teachplan_name'];
        $data['teachplan_videourl'] = $request['teachplan_videourl'];
        $data['teachplan_class'] = $request['teachplan_class'];
        if($request['teachplan_fileurl']){
            $data['teachplan_fileurl'] = $request['teachplan_fileurl'];
            $url = "https://idocv.kidcastle.com.cn/view/url.json?url=" . $request['teachplan_fileurl'];
            $word = $this->getCurl($url);
            $word = json_decode($word, true);
            if($word['data']){
                $wordlist = array();
                foreach($word['data'] as $k => $v){
                    $wordlist[$k] = $v['content'];
                }
                $wordstring = implode(" ",$wordlist);
                $data['teachplan_wordcontent'] = addslashes($wordstring);
            }
        }
        $data['teachplan_postil'] = $request['teachplan_postil'];
        $data['teachplan_matters'] = $request['teachplan_matters'];
        $data['teachplan_coverimg'] = $request['teachplan_coverimg'];
        $data['teachplan_createtime'] = time();
        $dataid = $this->Show_css->insertData("eas_teachhour_teachplan",$data);
        if(!$dataid){
            ajax_return(array('error' => 1,'errortip' => "新增失败!","bakfuntion"=>"errormotify"));
        }

        if($request['teachplan_class'] == '1') {
            $list = array();
            if ($request['affix_img'] !== null) {
                foreach ($request['affix_img'] as $k => $v) {
                    $list[$k]['company_id'] = '1001';
                    $list[$k]['teachplan_id'] = $dataid;
                    $list[$k]['teachpics_url'] = $v;
                    $list[$k]['teachpics_sort'] = $k+1;
                    foreach($request['affix_imgname'] as $key => $val){
                        $list[$key]['teachpics_name'] = $val;
                        $list[$key]['teachpics_createtime'] = time();
                    }
                }
                $info = array();
                foreach ($list as $val) {
                    $listid = $this->Show_css->insertData("eas_teachhour_teachpics", $val);
                    if (!$listid) {
                        ajax_return(array('error' => 1, 'errortip' => "新增照片失败!", "bakfuntion" => "errormotify"));
                    }
                    $info['company_id'] = '1001';
                    $info['teachpics_id'] = $listid;
                    $this->Show_css->insertData("eas_prepare_teachpics",$info);
                }
            }else{
                ajax_return(array('error' => 1,'errortip' => "图片必须上传!","bakfuntion"=>"dangerFromTip"));
            }
        }
        ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"successFromTip","bakurl"=>"/{$this->u}/TeachPlan?site_id={$request['site_id']}&classcode_branch={$request['classcode_branch']}&teachhour_branch={$request['teachhour_branch']}"));
    }

    function getCurl($url){
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        $output = curl_exec($ch);
        curl_close($ch);
        return $output;
    }

    //编辑资料
    function TeachPlanEditView(){
        $request = Input('get.','','trim,addslashes');
        $this->smarty->assign("act","TeachPlanEdit");

        $dataOne = $this->Show_css->getOne("eas_teachhour_teachplan","teachplan_id='{$request['teachplan_id']}'");
        $this->smarty->assign("dataVar", $dataOne);

        $this->smarty->assign("classcode_branch",$dataOne['classcode_branch']);
        $this->smarty->assign("teachhour_branch",$dataOne['teachhour_branch']);

        $imglist = $this->Show_css->selectClear("SELECT * FROM eas_teachhour_teachpics WHERE teachplan_id='{$request['teachplan_id']}'");
        $this->smarty->assign("imglist", $imglist);

        $this->Viewhtm = $this->router->getController()."/"."TeachPlanManage.htm";
    }

    //提交处理机制
    function TeachPlanEditAction(){
        $request = Input('post.','','trim,addslashes');
        if($request['teachplan_name'] == ''){
            ajax_return(array('error' => 1,'errortip' => "教案名称不能为空!","bakfuntion"=>"dangerFromTip"));
        }

        $data = array();
        $data['classcode_branch'] = $request['classcode_branch'];
        $data['teachhour_branch'] = $request['teachhour_branch'];
        $data['teachplan_name'] = $request['teachplan_name'];
        $data['teachplan_class'] = $request['teachplan_class'];
        if($request['teachplan_fileurl']){
            $data['teachplan_fileurl'] = $request['teachplan_fileurl'];
            $url = "https://idocv.kidcastle.com.cn/view/url.json?url=" . $request['teachplan_fileurl'];
            $word = $this->getCurl($url);
            $word = json_decode($word, true);
            if($word['data']){
                $wordlist = array();
                foreach($word['data'] as $k => $v){
                    $wordlist[$k] = $v['content'];
                }
                $wordstring = implode(" ",$wordlist);
                $data['teachplan_wordcontent'] = addslashes($wordstring);
            }
        }
        $data['teachplan_videourl'] = $request['teachplan_videourl'];
        $data['teachplan_postil'] = $request['teachplan_postil'];
        $data['teachplan_matters'] = $request['teachplan_matters'];
        $data['teachplan_coverimg'] = $request['teachplan_coverimg'];
        if(!$this->Show_css->updateData("eas_teachhour_teachplan","teachplan_id='{$request['teachplan_id']}'",$data)){
            ajax_return(array('error' => 1,'errortip' => "编辑失败!","bakfuntion"=>"dangerFromTip"));
        }
        if($request['teachplan_class'] == '1') {
            $this->Show_css->delData("eas_teachhour_teachpics","teachplan_id='{$request['teachplan_id']}'");
            $list = array();
            if ($request['affix_img'] !== null) {
                foreach ($request['affix_img'] as $k => $v) {
                    $list[$k]['company_id'] = '1001';
                    $list[$k]['teachplan_id'] = $request['teachplan_id'];
                    $list[$k]['teachpics_url'] = $v;
                    $list[$k]['teachpics_sort'] = $k+1;
                    foreach($request['affix_imgname'] as $key => $val){
                        $list[$key]['teachpics_name'] = $val;
                        $list[$key]['teachpics_createtime'] = time();
                    }
                }
                $info = array();
                foreach ($list as $val) {
                    $listid = $this->Show_css->insertData("eas_teachhour_teachpics", $val);
                    if (!$listid) {
                        ajax_return(array('error' => 1, 'errortip' => "编辑照片失败!", "bakfuntion" => "errormotify"));
                    }
                    $info['company_id'] = '1001';
                    $info['teachpics_id'] = $listid;
                    $this->Show_css->insertData("eas_prepare_teachpics",$info);
                }
            }else{
                ajax_return(array('error' => 1,'errortip' => "图片必须上传!","bakfuntion"=>"dangerFromTip"));
            }
        }
        ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"successFromTip","bakurl"=>"/{$this->u}/TeachPlan?site_id={$request['site_id']}&classcode_branch={$request['classcode_branch']}&teachhour_branch={$request['teachhour_branch']}"));
    }

    function ImportView()
    {
        $request = Input("get.");

        $this->smarty->assign("classcode_branch",$request['classcode_branch']);
        $this->smarty->assign("teachhour_branch",$request['teachhour_branch']);
    }

    function ImportExcelView()
    {
        $request = Input("post.");
        if ($_FILES['uploadFile']['name'] == '' || $_FILES['uploadFile']['error'] == '1') {
            $PlayInfoVar = array();
            $PlayInfoVar['teachplan_name'] = "导入出错";
            $PlayInfoVar['teachplan_postil'] = "导入出错";
            $PlayInfoVar['teachplan_matters'] = "导入出错";
            $PlayInfoVar['teachplan_coverimg'] = "导入出错";
            $PlayInfoVar['teachplan_videourl'] = "导入出错";
            $PlayInfoVar['teachplan_img'] = "导入出错";
            $PlayInfoVar['error'] = "1";
            $PlayInfoVar['errortip'] = "文件不存在";
            $PlayInfo[] = $PlayInfoVar;
            $this->smarty->assign("PlayInfo", $PlayInfo);
            exit;
        }

        $fileType = array('xls', 'csv', 'xlsx');
        $uploadfile = new \Webfile($_FILES['uploadFile'], $files_dir = '../static/file', $size = 2097152 * 10, $fileType);
        $up_file = $uploadfile->upload();

        $PlayInfo = array();
        if ($up_file && $uploadfile->updatastatus) {
            $ExeclName = array();
            $ExeclName['教案名称'] = "teachplan_name";
            $ExeclName['中心批注'] = "teachplan_postil";
            $ExeclName['教案注意事项'] = "teachplan_matters";
            $ExeclName['教案封面'] = "teachplan_coverimg";
            $ExeclName['视频地址'] = "teachplan_videourl";
            $ExeclName['图片名称'] = "teachplan_imgname";
            $ExeclName['图片链接'] = "teachplan_img";

            $WorkerList = execl_to_array($up_file, $ExeclName);
            unset($WorkerList[0]);

            if ($WorkerList) {
                foreach ($WorkerList as $WorkerrVar) {
                    if ($WorkerrVar['教案名称'] !== '' && $WorkerrVar['图片链接'] !== '' ) {
                        $workersList[] = $WorkerrVar;
                    } else {
                        $PlayInfoVar = array();
                        $PlayInfoVar['teachplan_name'] = $WorkerrVar['teachplan_name'];
                        $PlayInfoVar['teachplan_postil'] = $WorkerrVar['teachplan_postil'];
                        $PlayInfoVar['teachplan_matters'] = $WorkerrVar['teachplan_matters'];
                        $PlayInfoVar['teachplan_coverimg'] = $WorkerrVar['teachplan_coverimg'];
                        $PlayInfoVar['teachplan_videourl'] = $WorkerrVar['teachplan_videourl'];
                        $PlayInfoVar['teachplan_imgname'] = $WorkerrVar['teachplan_imgname'];
                        $PlayInfoVar['teachplan_img'] = $WorkerrVar['teachplan_img'];
                        $PlayInfoVar['error'] = "1";
                        $PlayInfoVar['errortip'] = "导入账号信息不完整";
                        $PlayInfo[] = $PlayInfoVar;
                    }
                }
            }

            if (count($workersList) > 1000) {
                ajax_return(array('error' => 1, 'errortip' => "导入数量不能大于1000!", "bakfuntion" => "errormotify"));
            }
            if ($workersList) {
                foreach ($workersList as $key => $workersVar) {
                    $PlayInfoVar = array();
                    $PlayInfoVar['teachplan_name'] = $workersVar['teachplan_name'];
                    $PlayInfoVar['teachplan_postil'] = $workersVar['teachplan_postil'];
                    $PlayInfoVar['teachplan_matters'] = $workersVar['teachplan_matters'];
                    $PlayInfoVar['teachplan_coverimg'] = $workersVar['teachplan_coverimg'];
                    $PlayInfoVar['teachplan_videourl'] = $workersVar['teachplan_videourl'];
                    $PlayInfoVar['teachplan_imgname'] = $workersVar['teachplan_imgname'];
                    $PlayInfoVar['teachplan_img'] = $workersVar['teachplan_img'];

                    $data = array();
                    $data['company_id'] = '1001';
                    $data['classcode_branch'] = $request['classcode_branch'];
                    $data['teachhour_branch'] = $request['teachhour_branch'];
                    $data['teachplan_name'] = $workersVar['teachplan_name'];
                    $data['teachplan_class'] = '1';
                    $data['teachplan_postil'] = $workersVar['teachplan_postil'];
                    $data['teachplan_matters'] = $workersVar['teachplan_matters'];
                    $data['teachplan_coverimg'] = $workersVar['teachplan_coverimg'];
                    $data['teachplan_videourl'] = $workersVar['teachplan_videourl'];
                    $data['teachplan_createtime'] = time();
                    $teachplan = $this->Show_css->getFieldOne("eas_teachhour_teachplan","teachplan_id","teachplan_name='{$workersVar['teachplan_name']}'");
                    if($teachplan){
                        $dataid = $teachplan['teachplan_id'];
                    }else{
                        $dataid = $this->Show_css->insertData("eas_teachhour_teachplan", $data);
                    }
                    if ($dataid) {
                        $list['company_id'] = '1001';
                        $list['teachplan_id'] = $dataid;
                        $list['teachpics_name'] = $workersVar['teachplan_imgname'];
                        $list['teachpics_url'] = $workersVar['teachplan_img'];
                        $list['teachpics_sort'] = $key+1;
                        $list['teachpics_createtime'] = time();
                        $this->Show_css->insertData("eas_teachhour_teachpics",$list);
                        $PlayInfoVar['error'] = "0";
                        $PlayInfoVar['errortip'] = "导入成功";
                    } else {
                        $PlayInfoVar['error'] = "1";
                        $PlayInfoVar['errortip'] = "导入失败";
                    }
                    $PlayInfo[] = $PlayInfoVar;
                }
            }
        } else {
            $PlayInfoVar = array();
            $PlayInfoVar['teachplan_name'] = "导入出错";
            $PlayInfoVar['teachplan_postil'] = "导入出错";
            $PlayInfoVar['teachplan_matters'] = "导入出错";
            $PlayInfoVar['teachplan_coverimg'] = "导入出错";
            $PlayInfoVar['teachplan_videourl'] = "导入出错";
            $PlayInfoVar['teachplan_imgname'] = "导入出错";
            $PlayInfoVar['teachplan_img'] = "导入出错";
            $PlayInfoVar['error'] = "1";
            $PlayInfoVar['errortip'] = "文件不存在";
            $PlayInfo[] = $PlayInfoVar;
        }

        $this->smarty->assign("PlayInfo", $PlayInfo);
    }


    //删除
    function TeachPlanDelAction()
    {
        $request = Input('get.','','trim,addslashes');

        if($this->Show_css->delData("eas_teachhour_teachplan", "teachplan_id='{$request['id']}'")){
            ajax_return(array('error' => 0,'errortip' => "删除成功!","bakfuntion"=>"okmotify","bakurl"=>"/{$this->u}/TeachPlan?site_id={$request['site_id']}&classcode_branch={$request['classcode_branch']}&teachhour_branch={$request['teachhour_branch']}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "删除失败!","bakfuntion"=>"errormotify"));
        }
    }

    //优质视频管理
    function TempVideoView(){
        $request = Input('get.','','trim,addslashes');
        $smarty = $this->smarty;

        if(!isset($request['p'])){
            $p = '1';
        }else{
            $p= $request['p'];
        }

        $datawhere = "classcode_branch='{$request['classcode_branch']}' and teachhour_branch='{$request['teachhour_branch']}'";
        $pageurl = "/{$this->u}/{$this->t}?classcode_branch={$request['classcode_branch']}&teachhour_branch={$request['teachhour_branch']}";
        if ($request['site_id']) {
            $pageurl .= "&site_id={$request['site_id']}";
        }

        $datatype = array();
        if(isset($request['keyword']) && $request['keyword'] !==''){
            $datawhere .= " and (tempvideo_name like '%{$request['keyword']}%')";
            $pageurl .="&keyword={$request['keyword']}";
            $datatype['keyword'] = $request['keyword'];
        }

        if(isset($request['tempvideo_status']) && $request['tempvideo_status'] !== ''){
            $datawhere .= " and tempvideo_status='{$request['tempvideo_status']}'";
            $pageurl .="&tempvideo_status={$request['tempvideo_status']}";
            $datatype['tempvideo_status'] = $request['tempvideo_status'];
        }
        if(isset($request['tempvideo_isPerfect']) && $request['tempvideo_isPerfect'] !== ''){
            $datawhere .= " and tempvideo_isPerfect='{$request['tempvideo_isPerfect']}'";
            $pageurl .="&tempvideo_isPerfect={$request['tempvideo_isPerfect']}";
            $datatype['tempvideo_isPerfect'] = $request['tempvideo_isPerfect'];
        }

        $sql = "SELECT * FROM eas_teachhour_tempvideo WHERE {$datawhere} ORDER BY tempvideo_id DESC";

        $db_nums = $this->Show_css->select("SELECT COUNT(*) FROM eas_teachhour_tempvideo where {$datawhere}");//相关条件下的总记录数COUNT(*)
        $allnum = $db_nums[0][0];

        $datalist = $this->Show_css->dbwherePage($sql,$allnum,'10',$pageurl.'&p=',$p,'10','2');

        $smarty->assign("pagelist",$datalist['pages']);//筛选信息

        $smarty->assign("dataList",$datalist['cont']);
        $smarty->assign("datatype",$datatype);
        $smarty->assign("classcode_branch",$request['classcode_branch']);
        $smarty->assign("teachhour_branch",$request['teachhour_branch']);
    }

    //添加
    function TempVideoAddView(){
        $request = Input('get.','','trim,addslashes');

        $this->smarty->assign("act","TempVideoAdd");

        $this->smarty->assign("classcode_branch",$request['classcode_branch']);
        $this->smarty->assign("teachhour_branch",$request['teachhour_branch']);

        $this->Viewhtm = $this->router->getController()."/"."TempVideoManage.htm";
    }

    //提交处理明细
    function TempVideoAddAction(){
        $request = Input('post.','','trim,addslashes');
        if($request['tempvideo_name'] == ''){
            ajax_return(array('error' => 1,'errortip' => "视频名称不能为空!","bakfuntion"=>"dangerFromTip"));
        }
        if($request['tempvideo_author'] == ''){
            ajax_return(array('error' => 1,'errortip' => "视频作者不能为空!","bakfuntion"=>"dangerFromTip"));
        }
        if($request['tempvideo_coverimg'] == ''){
            ajax_return(array('error' => 1,'errortip' => "教案封面不能为空!","bakfuntion"=>"dangerFromTip"));
        }

        $data = array();
        $data['company_id'] = '1001';
        $data['classcode_branch'] = $request['classcode_branch'];
        $data['teachhour_branch'] = $request['teachhour_branch'];
        $data['tempvideo_name'] = $request['tempvideo_name'];
        $data['tempvideo_author'] = $request['tempvideo_author'];
        $data['tempvideo_status'] = '1';
        $data['tempvideo_coverimg'] = $request['tempvideo_coverimg'];
        $data['tempvideo_videourl'] = $request['tempvideo_videourl'];
        $data['tempvideo_details'] = $request['tempvideo_details'];
        $data['tempvideo_isPerfect'] = $request['tempvideo_isPerfect'];
        $data['tempvideo_createtime'] = time();
        if($this->Show_css->insertData("eas_teachhour_tempvideo", $data)){
            ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion" => "successFromTip","bakurl"=>"/{$this->u}/TempVideo?site_id={$request['site_id']}&classcode_branch={$request['classcode_branch']}&teachhour_branch={$request['teachhour_branch']}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "添加失败!","bakfuntion" => "errormotify"));
        }
    }

    //编辑资料
    function TempVideoEditView(){
        $request = Input('get.','','trim,addslashes');

        $this->smarty->assign("act","TempVideoEdit");

        $dataOne = $this->Show_css->getOne("eas_teachhour_tempvideo","tempvideo_id='{$request['tempvideo_id']}'");
        $this->smarty->assign("dataVar", $dataOne);

        $this->smarty->assign("classcode_branch",$dataOne['classcode_branch']);
        $this->smarty->assign("teachhour_branch",$dataOne['teachhour_branch']);

        $this->Viewhtm = $this->router->getController()."/"."TempVideoManage.htm";
    }

    //编辑班别课时明细
    function TempVideoEditAction(){
        $request = Input('post.','','trim,addslashes');
        if($request['tempvideo_name'] == ''){
            ajax_return(array('error' => 1,'errortip' => "视频名称不能为空!","bakfuntion"=>"dangerFromTip"));
        }
        if($request['tempvideo_author'] == ''){
            ajax_return(array('error' => 1,'errortip' => "视频作者不能为空!","bakfuntion"=>"dangerFromTip"));
        }
        if($request['tempvideo_coverimg'] == ''){
            ajax_return(array('error' => 1,'errortip' => "教案封面不能为空!","bakfuntion"=>"dangerFromTip"));
        }

        $data = array();
        $data['company_id'] = '1001';
        $data['classcode_branch'] = $request['classcode_branch'];
        $data['teachhour_branch'] = $request['teachhour_branch'];
        $data['tempvideo_name'] = $request['tempvideo_name'];
        $data['tempvideo_author'] = $request['tempvideo_author'];
        $data['tempvideo_status'] = '1';
        $data['tempvideo_coverimg'] = $request['tempvideo_coverimg'];
        $data['tempvideo_videourl'] = $request['tempvideo_videourl'];
        $data['tempvideo_details'] = $request['tempvideo_details'];
        $data['tempvideo_isPerfect'] = $request['tempvideo_isPerfect'];
        if($this->Show_css->updateData("eas_teachhour_tempvideo","tempvideo_id='{$request['tempvideo_id']}'",$data)){
            ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion" => "successFromTip","bakurl"=>"/{$this->u}/TempVideo?site_id={$request['site_id']}&classcode_branch={$request['classcode_branch']}&teachhour_branch={$request['teachhour_branch']}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "编辑失败!","bakfuntion" => "errormotify"));
        }
    }

    //审核通过/审核驳回
    function ExamineAction(){
        $request = Input('get.','','trim,addslashes');
        if($request['code'] == 1){
            $dataid = $this->Show_css->updateData("eas_teachhour_tempvideo","tempvideo_id='{$request['id']}'",array('tempvideo_status'=>'1'));
        }elseif($request['code'] == -1){
            $dataid = $this->Show_css->updateData("eas_teachhour_tempvideo","tempvideo_id='{$request['id']}'",array('tempvideo_status'=>'-1'));
        }
        if($dataid){
            ajax_return(array('error' => 0,'errortip' => "审核成功!","bakfuntion"=>"successFromTip","bakurl"=>"/{$this->u}/TempVideo?site_id={$request['site_id']}&classcode_branch={$request['classcode_branch']}&teachhour_branch={$request['teachhour_branch']}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "审核失败!","bakfuntion"=>"errormotify"));
        }
    }
    //删除
    function TempVideoDelAction()
    {
        $request = Input('get.','','trim,addslashes');

        if($this->Show_css->delData("eas_teachhour_tempvideo", "tempvideo_id='{$request['id']}'")){
            ajax_return(array('error' => 0,'errortip' => "删除成功!","bakfuntion"=>"okmotify","bakurl"=>"/{$this->u}/TempVideo?site_id={$request['site_id']}&classcode_branch={$request['classcode_branch']}&teachhour_branch={$request['teachhour_branch']}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "删除失败!","bakfuntion"=>"errormotify"));
        }
    }

    //优质批注管理
    function TempPostilView(){
        $request = Input('get.','','trim,addslashes');
        $smarty = $this->smarty;

        if(!isset($request['p'])){
            $p = '1';
        }else{
            $p= $request['p'];
        }

        $datawhere = "classcode_branch='{$request['classcode_branch']}' and teachhour_branch='{$request['teachhour_branch']}'";
        $pageurl = "/{$this->u}/{$this->t}?classcode_branch={$request['classcode_branch']}&teachhour_branch={$request['teachhour_branch']}";
        if ($request['site_id']) {
            $pageurl .= "&site_id={$request['site_id']}";
        }

        $datatype = array();
        if(isset($request['keyword']) && $request['keyword'] !==''){
            $datawhere .= " and (temppostil_tilte like '%{$request['keyword']}%')";
            $pageurl .="&keyword={$request['keyword']}";
            $datatype['keyword'] = $request['keyword'];
        }

        if(isset($request['temppostil_isPerfect']) && $request['temppostil_isPerfect'] !== ''){
            $datawhere .= " and temppostil_isPerfect='{$request['temppostil_isPerfect']}'";
            $pageurl .="&temppostil_isPerfect={$request['temppostil_isPerfect']}";
            $datatype['temppostil_isPerfect'] = $request['temppostil_isPerfect'];
        }
        if(isset($request['temppostil_isPopular']) && $request['temppostil_isPopular'] !== ''){
            $datawhere .= " and temppostil_isPopular='{$request['temppostil_isPopular']}'";
            $pageurl .="&temppostil_isPopular={$request['temppostil_isPopular']}";
            $datatype['temppostil_isPopular'] = $request['temppostil_isPopular'];
        }

        $sql = "SELECT * FROM eas_teachhour_temppostil WHERE {$datawhere} ORDER BY temppostil_id DESC";

        $db_nums = $this->Show_css->select("SELECT COUNT(*) FROM eas_teachhour_temppostil where {$datawhere}");//相关条件下的总记录数COUNT(*)
        $allnum = $db_nums[0][0];

        $datalist = $this->Show_css->dbwherePage($sql,$allnum,'10',$pageurl.'&p=',$p,'10','2');

        $smarty->assign("pagelist",$datalist['pages']);//筛选信息

        $smarty->assign("dataList",$datalist['cont']);
        $smarty->assign("datatype",$datatype);
        $smarty->assign("classcode_branch",$request['classcode_branch']);
        $smarty->assign("teachhour_branch",$request['teachhour_branch']);
    }

    //添加
    function TempPostilAddView(){
        $request = Input('get.','','trim,addslashes');

        $this->smarty->assign("act","TempPostilAdd");

        $this->smarty->assign("classcode_branch",$request['classcode_branch']);
        $this->smarty->assign("teachhour_branch",$request['teachhour_branch']);

        $this->Viewhtm = $this->router->getController()."/"."TempPostilManage.htm";
    }

    //提交处理明细
    function TempPostilAddAction(){
        $request = Input('post.','','trim,addslashes');
        if($request['temppostil_tilte'] == ''){
            ajax_return(array('error' => 1,'errortip' => "批注名称不能为空!","bakfuntion"=>"dangerFromTip"));
        }
        if($request['temppostil_author'] == ''){
            ajax_return(array('error' => 1,'errortip' => "视频作者不能为空!","bakfuntion"=>"dangerFromTip"));
        }
        if($request['temppostil_details'] == ''){
            ajax_return(array('error' => 1,'errortip' => "中心批注不能为空!","bakfuntion"=>"dangerFromTip"));
        }

        $data = array();
        $data['company_id'] = '1001';
        $data['classcode_branch'] = $request['classcode_branch'];
        $data['teachhour_branch'] = $request['teachhour_branch'];
        $data['temppostil_tilte'] = $request['temppostil_tilte'];
        $data['temppostil_author'] = $request['temppostil_author'];
        $data['temppostil_status'] = '1';
        $data['temppostil_details'] = $request['temppostil_details'];
        $data['temppostil_isPerfect'] = $request['temppostil_isPerfect'];
        $data['temppostil_isPopular'] = $request['temppostil_isPopular'];
        $data['temppostil_createtime'] = time();
        if($this->Show_css->insertData("eas_teachhour_temppostil", $data)){
            ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"successFromTip","bakurl"=>"/{$this->u}/TempPostil?site_id={$request['site_id']}&classcode_branch={$request['classcode_branch']}&teachhour_branch={$request['teachhour_branch']}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "添加失败!","bakfuntion"=>"errormotify"));
        }
    }

    //编辑资料
    function TempPostilEditView(){
        $request = Input('get.','','trim,addslashes');

        $this->smarty->assign("act","TempPostilEdit");

        $dataOne = $this->Show_css->getOne("eas_teachhour_temppostil","temppostil_id='{$request['temppostil_id']}'");
        $this->smarty->assign("dataVar", $dataOne);

        $this->smarty->assign("classcode_branch",$dataOne['classcode_branch']);
        $this->smarty->assign("teachhour_branch",$dataOne['teachhour_branch']);

        $this->Viewhtm = $this->router->getController()."/"."TempPostilManage.htm";
    }

    //编辑班别课时明细
    function TempPostilEditAction(){
        $request = Input('post.','','trim,addslashes');
        if($request['temppostil_tilte'] == ''){
            ajax_return(array('error' => 1,'errortip' => "批注名称不能为空!","bakfuntion"=>"dangerFromTip"));
        }
        if($request['temppostil_author'] == ''){
            ajax_return(array('error' => 1,'errortip' => "视频作者不能为空!","bakfuntion"=>"dangerFromTip"));
        }
        if($request['temppostil_details'] == ''){
            ajax_return(array('error' => 1,'errortip' => "中心批注不能为空!","bakfuntion"=>"dangerFromTip"));
        }

        $data = array();
        $data['company_id'] = '1001';
        $data['classcode_branch'] = $request['classcode_branch'];
        $data['teachhour_branch'] = $request['teachhour_branch'];
        $data['temppostil_tilte'] = $request['temppostil_tilte'];
        $data['temppostil_author'] = $request['temppostil_author'];
        $data['temppostil_status'] = '1';
        $data['temppostil_details'] = $request['temppostil_details'];
        $data['temppostil_isPerfect'] = $request['temppostil_isPerfect'];
        $data['temppostil_isPopular'] = $request['temppostil_isPopular'];
        if($this->Show_css->updateData("eas_teachhour_temppostil","temppostil_id='{$request['temppostil_id']}'",$data)){
            ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"successFromTip","bakurl"=>"/{$this->u}/TempPostil?site_id={$request['site_id']}&classcode_branch={$request['classcode_branch']}&teachhour_branch={$request['teachhour_branch']}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "编辑失败!","bakfuntion"=>"errormotify"));
        }
    }

    //删除
    function TempPostilDelAction()
    {
        $request = Input('get.','','trim,addslashes');

        if($this->Show_css->delData("eas_teachhour_temppostil", "temppostil_id='{$request['id']}'")){
            ajax_return(array('error' => 0,'errortip' => "删除成功!","bakfuntion"=>"okmotify","bakurl"=>"/{$this->u}/TempPostil?site_id={$request['site_id']}&classcode_branch={$request['classcode_branch']}&teachhour_branch={$request['teachhour_branch']}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "删除失败!","bakfuntion"=>"errormotify"));
        }
    }

    //课程资料管理
    function CourseMaterialsView(){
        $request = Input('get.','','trim,addslashes');
        $smarty = $this->smarty;

        if(!isset($request['p'])){
            $p = '1';
        }else{
            $p= $request['p'];
        }

        $datawhere = "company_id = '{$request['company_id']}' and classcode_branch='{$request['classcode_branch']}' and teachhour_branch='{$request['teachhour_branch']}'";
        $pageurl = "/{$this->u}/{$this->t}?classcode_branch={$request['classcode_branch']}&teachhour_branch={$request['teachhour_branch']}";
        if ($request['site_id']) {
            $pageurl .= "&site_id={$request['site_id']}";
        }

        $datatype = array();
        if(isset($request['keyword']) && $request['keyword'] !==''){
            $datawhere .= " and (tempfiles_name like '%{$request['keyword']}%')";
            $pageurl .="&keyword={$request['keyword']}";
            $datatype['keyword'] = $request['keyword'];
        }

        if(isset($request['tempfiles_class']) && $request['tempfiles_class'] !== ''){
            $datawhere .= " and tempfiles_class='{$request['tempfiles_class']}'";
            $pageurl .="&tempfiles_class={$request['tempfiles_class']}";
            $datatype['tempfiles_class'] = $request['tempfiles_class'];
        }

        $sql = "SELECT * FROM eas_teachhour_tempfiles WHERE {$datawhere} ORDER BY tempfiles_id DESC";

        $db_nums = $this->Show_css->select("SELECT COUNT(*) FROM eas_teachhour_tempfiles where {$datawhere}");//相关条件下的总记录数COUNT(*)
        $allnum = $db_nums[0][0];

        $datalist = $this->Show_css->dbwherePage($sql,$allnum,'10',$pageurl.'&p=',$p,'10','2');

        $smarty->assign("pagelist",$datalist['pages']);//筛选信息

        $smarty->assign("dataList",$datalist['cont']);
        $smarty->assign("datatype",$datatype);
        $smarty->assign("classcode_branch",$request['classcode_branch']);
        $smarty->assign("teachhour_branch",$request['teachhour_branch']);
    }

    //新增资料
    function CourseMaterialsAddView(){
        $request = Input('get.','','trim,addslashes');

        $this->smarty->assign("act","CourseMaterialsAdd");

        $this->smarty->assign("classcode_branch",$request['classcode_branch']);
        $this->smarty->assign("teachhour_branch",$request['teachhour_branch']);

        $this->Viewhtm = $this->router->getController()."/"."CourseMaterialsManage.htm";
    }

    //提交处理机制
    function CourseMaterialsAddAction(){
        $request = Input('post.','','trim,addslashes');
        if($request['tempfiles_name'] == ''){
            ajax_return(array('error' => 1,'errortip' => "文件名称不能为空!","bakfuntion"=>"dangerFromTip"));
        }
        if($request['tempfiles_class'] == ''){
            ajax_return(array('error' => 1,'errortip' => "教案模式不能为空!","bakfuntion"=>"dangerFromTip"));
        }

        $data = array();
        $data['company_id'] = '1001';
        $data['classcode_branch'] = $request['classcode_branch'];
        $data['teachhour_branch'] = $request['teachhour_branch'];
        $data['tempfiles_class'] = $request['tempfiles_class'];
        if($request['tempfiles_class'] == '0') {
            $data['tempfiles_url'] = $request['TempfilesUrl'];
            $data['tempfiles_size'] = $this->formatBytes($request['Tempfiles-Size']);
        }elseif($request['tempfiles_class'] == '1'){
            $data['tempfiles_url'] = $request['tempfiles_url'];
            $data['tempfiles_size'] = $this->formatBytes($request['tempfiles_size']);
        }elseif($request['tempfiles_class'] == '2') {
            $data['tempfiles_url'] = $request['Tempfiles-Url'];
            $data['tempfiles_size'] = $this->formatBytes($request['tempfilesSize']);
        }
        if($data['tempfiles_url'] == ''){
            ajax_return(array('error' => 1,'errortip' => "教案模式文件必须上传!","bakfuntion"=>"dangerFromTip"));
        }
        $data['tempfiles_name'] = $request['tempfiles_name'];
        $data['tempfiles_createtime'] = time();
        if($this->Show_css->insertData("eas_teachhour_tempfiles",$data)){
            ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"successFromTip","bakurl"=>"/{$this->u}/CourseMaterials?site_id={$request['site_id']}&classcode_branch={$request['classcode_branch']}&teachhour_branch={$request['teachhour_branch']}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "新增失败!","bakfuntion"=>"errormotify"));
        }
    }

    //编辑资料
    function CourseMaterialsEditView(){
        $request = Input('get.','','trim,addslashes');
        $this->smarty->assign("act","CourseMaterialsEdit");

        $dataOne = $this->Show_css->getOne("eas_teachhour_tempfiles","tempfiles_id='{$request['tempfiles_id']}'");
        $this->smarty->assign("dataVar", $dataOne);

        $this->smarty->assign("classcode_branch",$dataOne['classcode_branch']);
        $this->smarty->assign("teachhour_branch",$dataOne['teachhour_branch']);

        $this->Viewhtm = $this->router->getController()."/"."CourseMaterialsManage.htm";
    }

    //提交处理机制
    function CourseMaterialsEditAction(){
        $request = Input('post.','','trim,addslashes');
        if($request['tempfiles_name'] == ''){
            ajax_return(array('error' => 1,'errortip' => "文件名称不能为空!","bakfuntion"=>"dangerFromTip"));
        }
        if($request['tempfiles_class'] == ''){
            ajax_return(array('error' => 1,'errortip' => "教案模式不能为空!","bakfuntion"=>"dangerFromTip"));
        }

        $data = array();
        $data['company_id'] = '1001';
        $data['classcode_branch'] = $request['classcode_branch'];
        $data['teachhour_branch'] = $request['teachhour_branch'];
        $data['tempfiles_class'] = $request['tempfiles_class'];
        $data['tempfiles_name'] = $request['tempfiles_name'];
        if($request['tempfiles_class'] == '0') {
            $data['tempfiles_url'] = $request['TempfilesUrl'];
            $data['tempfiles_size'] = $this->formatBytes($request['Tempfiles-Size']);
        }elseif($request['tempfiles_class'] == '1'){
            $data['tempfiles_url'] = $request['tempfiles_url'];
            $data['tempfiles_size'] = $this->formatBytes($request['tempfiles_size']);
        }elseif($request['tempfiles_class'] == '2') {
            $data['tempfiles_url'] = $request['Tempfiles-Url'];
            $data['tempfiles_size'] = $this->formatBytes($request['tempfilesSize']);
        }
        if($data['tempfiles_url'] == ''){
            ajax_return(array('error' => 1,'errortip' => "教案模式文件必须上传!","bakfuntion"=>"dangerFromTip"));
        }
        if($this->Show_css->updateData("eas_teachhour_tempfiles","tempfiles_id='{$request['tempfiles_id']}'",$data)){
            ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"successFromTip","bakurl"=>"/{$this->u}/CourseMaterials?site_id={$request['site_id']}&classcode_branch={$request['classcode_branch']}&teachhour_branch={$request['teachhour_branch']}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "编辑失败!","bakfuntion"=>"errormotify"));
        }
    }

    //删除
    function CourseMaterialsDelAction()
    {
        $request = Input('get.','','trim,addslashes');

        if($this->Show_css->delData("eas_teachhour_tempfiles", "tempfiles_id='{$request['id']}'")){
            ajax_return(array('error' => 0,'errortip' => "删除成功!","bakfuntion"=>"okmotify","bakurl"=>"/{$this->u}/CourseMaterials?site_id={$request['site_id']}&classcode_branch={$request['classcode_branch']}&teachhour_branch={$request['teachhour_branch']}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "删除失败!","bakfuntion"=>"errormotify"));
        }
    }

    //文件大小单位转换
    function formatBytes($size){
        $units = array(' B', ' KB', ' MB', ' GB', ' TB');
        for ($i = 0; $size >= 1024 && $i < 4; $i++) $size /= 1024;
        return round($size, 2).$units[$i];
    }



    //魔术方法
    public function __call($name, $arguments) {
        $this->smarty->assign("errorTip", "Calling object method '$name' ". implode(', ', $arguments). "\n");
        $this->Viewhtm = "under.htm";
    }

    //魔术函数
    function __destruct()
    {
        $site_id = Input('get.site_id',0);

        if ($this->c =='Wait') {
            $this->smarty->assign("u", $this->router->getController());
            $this->smarty->assign("t", $this->router->getUrl());
            $this->smarty->assign("Viewhtm", $this->Viewhtm);
            $websites = $this->Show_css->getOne("cms_websites","site_id='{$site_id}'");
            $this->smarty->assign("websites",$websites);
            if($site_id){
                if($this->UserLogin['user_type'] == '1' and $this->UserLogin['user_limitsinc'] == '1'){
                    $moduleList = $this->Show_css->select("SELECT m.* FROM cms_module as m LEFT JOIN cms_moduleapply as a ON a.module_id = m.module_id WHERE a.site_id = '{$site_id}' ORDER BY m.module_weight ASC ");
                }else{
                    $moduleList = $this->Show_css->select("SELECT m.* FROM cms_module as m LEFT JOIN cms_moduleapply as a ON a.module_id = m.module_id LEFT JOIN cms_websites_func as c ON c.site_id = a.site_id and c.module_id = a.module_id WHERE a.site_id = '{$site_id}' and c.user_id = '{$this->UserLogin['user_id']}' ORDER BY m.module_weight ASC ");
                }
                // print_r($moduleList);die;
                $this->smarty->assign("moduleList", $moduleList);
                $this->display("websiteindex.htm");
            }else{
                $this->display("index.htm");
            }
        }
        exit;
    }
}