<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn 
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2016/12/15
 * Time: 15:56
 */

namespace Work\Controller\Manage;


class ModuleController extends viewTpl{
    public $data;
    public $iuser;
    public $u;
    public $t;
    public $c;
    public $Viewhtm;

    function __construct() {
        parent::__construct ();
        if(!$this->check_login()){
            $this->LoginView();
        }
        $this->smarty->assign("iuser", $this->UserLogin);
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
        $this->Viewhtm = $this->router->getController()."/".$this->router->getUrl().".htm";

    }

    //主页
    function HomeView()
    {

        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        if(!isset($request['p'])){
            $p = '1';
        }else{
            $p= $request['p'];
        }

        $datawhere = "1";
        $pageurl = "/{$this->u}/{$this->t}?";
        if ($request['site_id']) {
            $pageurl .= "&site_id={$request['site_id']}";
        }

        $datatype = array();
        if(isset($request['keyword']) && $request['keyword'] !==''){
            $datawhere .= " and m.module_name like '%{$request['keyword']}%' ";
            $pageurl .="&keyword={$request['keyword']}";
            $datatype['keyword'] = $request['keyword'];
        }

        if(isset($request['type']) && $request['type'] !==''){
            $datawhere .= " and m.module_type = '{$request['type']}'";
            $pageurl .="&type={$request['type']}";
            $datatype['type'] = $request['type'];
        }

        $sql = "SELECT m.* FROM cms_module as m where {$datawhere} order by m.module_weight ASC ";

        $db_nums = $Show_css->select("SELECT COUNT(*) FROM cms_module as m where {$datawhere}");//相关条件下的总记录数COUNT(*)
        $allnum = $db_nums[0][0];

        $datalist = $Show_css->dbwherePage($sql,$allnum,'10',$pageurl.'&p=',$p,'10','2');

        $smarty->assign("pagelist",$datalist['pages']);
        $smarty->assign("dataList",$datalist['cont']);
        $smarty->assign("datatype",$datatype);
    }
    //列表显示
    function ShowView()
    {
        $dataList = $this->Show_css->select("SELECT m.* FROM cms_module as m order by m.module_weight ASC ");
        $this->smarty->assign("dataList",$dataList);
    }

    //站点功能配置
    function WebsitesView()
    {

        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        if(!isset($request['p'])){
            $p = '1';
        }else{
            $p= $request['p'];
        }

        $datawhere = "w.site_type <> '1'";
        $pageurl = "/{$this->u}/{$this->t}?";

        $datatype = array();
        if(isset($request['keyword']) && $request['keyword'] !==''){
            $datawhere .= " and w.site_title like '%{$request['keyword']}%' ";
            $pageurl .="&keyword={$request['keyword']}";
            $datatype['keyword'] = $request['keyword'];
        }


        $sql = "SELECT w.*,(SELECT COUNT(a.apply_id) FROM cms_moduleapply AS a WHERE a.module_id = '{$request['id']}' and a.site_id = w.site_id) AS thestatus FROM cms_websites as w where {$datawhere} order by w.site_weight ASC ";

        $db_nums = $Show_css->select("SELECT COUNT(*) FROM cms_websites as w where {$datawhere}");//相关条件下的总记录数COUNT(*)
        $allnum = $db_nums[0][0];

        $datalist = $Show_css->dbwherePage($sql,$allnum,'10',$pageurl.'&p=',$p,'10','2');

        $smarty->assign("pagelist",$datalist['pages']);
        $smarty->assign("dataList",$datalist['cont']);
        $smarty->assign("module_id",$request['id']);
        $smarty->assign("datatype",$datatype);
    }

    //增加系统功能
    function AddView()
    {
        $this->smarty->assign("act","Add");
        $this->Viewhtm = $this->router->getController()."/"."Manage.htm";
    }
    //提交处理机制
    function AddAction()
    {
        $request = Input('post.','','trim,addslashes');
        $data = array();

        if($request['module_name'] == '' || $request['module_mark'] == '' || $request['module_link'] == ''){
            ajax_return(array('error' => 1,'errortip' => "表单相关内容未填写完整!","bakfuntion"=>"warningFromTip"));
        }

        $data['module_name'] = $request['module_name'];
        $data['module_type'] = $request['module_type'];
        $data['module_mark'] = $request['module_mark'];
        $data['module_weight'] = $request['module_weight'];
        $data['module_style'] = $request['module_style'];
        $data['module_color'] = $request['module_color'];
		if($request['module_styletype']){ $date['module_styletype'] = '1'; }else{  $date['module_styletype'] = '0'; }
        $data['module_link'] = $request['module_link'];
        $data['module_introduce'] = $request['module_introduce'];
        if($this->Show_css->insertData("cms_module",$data)){
            ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"successFromTip"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "新增失败!","bakfuntion"=>"dangerFromTip"));
        }
    }

    //增加系统功能
    function EditView()
    {

        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        $moduleOne = $Show_css->getOne("cms_module", "module_id='{$request['id']}'");
        $smarty->assign("dataVar", $moduleOne);

        $smarty->assign("act", "Edit");

        $this->Viewhtm = $this->router->getController()."/"."Manage.htm";
    }
    //提交处理机制
    function EditAction()
    {
        $request = Input('post.','','trim,addslashes');

        $data = array();
        $data['module_name'] = $request['module_name'];
        $data['module_type'] = $request['module_type'];
        $data['module_mark'] = $request['module_mark'];
        $data['module_weight'] = $request['module_weight'];
        if($request['module_styletype']){ $date['module_styletype'] = '1'; }else{  $date['module_styletype'] = '0'; }
        $data['module_style'] = $request['module_style'];
        $data['module_link'] = $request['module_link'];
        $data['module_color'] = $request['module_color'];
        $data['module_introduce'] = $request['module_introduce'];
        if($this->Show_css->updateData("cms_module","module_id = '{$request['module_id']}'",$data)){
            ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"okmotify","bakurl"=>"/{$this->u}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "新增失败!","bakfuntion"=>"errormotify"));
        }
    }
    //提交处理机制
    function DelAction()
    {
        $module_id = Input('get.id',0);

        if($this->Show_css->delData('cms_module',"module_id='{$module_id}'")){
            ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"okmotify","bakurl"=>"/{$this->u}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "新增失败!","bakfuntion"=>"errormotify"));
        }
    }
    //适用站点
    function ApplyAction()
    {
        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $data = array();
        $data['module_id']    = $request['module_id'];
        $data['site_id']      = $request['site_id'];

        if($Show_css->insertData("cms_moduleapply",$data)){
            $result = array();
            $res = array('error' => '0', 'errortip' => '开发成功', 'result' => $result);
            ajax_return($res);
        }else{
            $result = array();
            $res = array('error' => '0', 'errortip' => '提交失败', 'result' => $result);
            ajax_return($res);
        }
    }
    //适用站点 (删除)  设置
    function DelApplyAction()
    {
        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;

        $data = array();
        $data['module_id']    = $request['module_id'];
        $data['site_id']      = $request['site_id'];

        if($Show_css->delData('cms_moduleapply',"module_id='{$request['module_id']}' and site_id='{$request['site_id']}'")){
            $result = array();
            $res = array('error' => '0', 'errortip' => '取消成功', 'result' => $result);
            ajax_return($res);
        }else{
            $result = array();
            $res = array('error' => '1', 'errortip' => '取消失败', 'result' => $result);
            ajax_return($res);
        }
    }
    //魔术方法
    public function __call($name, $arguments) {
        $this->smarty->assign("errorTip", "Calling object method '$name' ". implode(', ', $arguments). "\n");

        $this->Viewhtm = "under.htm";
    }
    //魔术方法
    function __destruct()
    {
        $site_id = Input('get.site_id',0);

        if ($this->c =='Wait') {
            $this->smarty->assign("u", $this->router->getController());
            $this->smarty->assign("t", $this->router->getUrl());
            $this->smarty->assign("Viewhtm", $this->Viewhtm);
            if($site_id){
                $websites = $this->Show_css->getOne("cms_websites","site_id='{$site_id}'");
                $this->smarty->assign("websites",$websites);
                $this->display("websiteindex.htm");
            }else{
                $this->display("index.htm");
            }
        }
        exit;
    }

}