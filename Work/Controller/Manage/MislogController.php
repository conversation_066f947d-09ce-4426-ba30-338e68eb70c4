<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/2/14
 * Time: 10:15
 */

namespace Work\Controller\Manage;


class MislogController extends viewTpl{
    public $data;
    public $u;
    public $t;
    public $c;
    public $iuser;
    public $Viewhtm;
    public $Module;
    public $errortip;

    //预加载处理类
    function __construct() {
        parent::__construct ();
        if(!$this->check_login()){
            $this->LoginView();
        }
        if($this->check_login()){
            $this->smarty->assign("iuser", $this->UserLogin);
            $this->iuser = $this->UserLogin;
        }
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
        $this->Viewhtm = $this->router->getController()."/".$this->router->getUrl().".htm";
        $moduleOne = $this->Show_css->getOne("cms_module","module_mark='{$this->u}'");
        $this->smarty->assign("moduleOne",$moduleOne);
        $this->Module = $moduleOne;
    }

    //主页
    function HomeView()
    {
        $request = Input('get.');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        if(!isset($request['p'])){
            $p = '1';
        }else{
            $p= $request['p'];
        }

        $datawhere = "1";
        $pageurl = "/{$this->u}/{$this->t}?";

        $datatype = array();
        if(isset($request['keyword']) && $request['keyword'] !==''){
            $datawhere .= " and m.mislog_mobile like '%{$request['keyword']}%' ";
            $pageurl .="&keyword={$request['keyword']}";
            $datatype['keyword'] = $request['keyword'];
        }

        if(isset($request['mislog_sendinc']) && $request['mislog_sendinc'] !== ''){
            $datawhere .= " and m.mislog_sendinc = '{$request['mislog_sendinc']}'";
            $pageurl .="&mislog_sendinc={$request['mislog_sendinc']}";
            $datatype['mislog_sendinc'] = $request['mislog_sendinc'];
        }

        $sql = "SELECT m.* FROM crm_mislog as m  where {$datawhere} order by m.mislog_id DESC";

        $db_nums = $Show_css->select("SELECT COUNT(*) FROM crm_mislog as m where {$datawhere}");//相关条件下的总记录数COUNT(*)
        $allnum = $db_nums[0][0];

        $datalist = $Show_css->dbwherePage($sql,$allnum,'10',$pageurl.'&p=',$p,'10','2');

        $smarty->assign("pagelist",$datalist['pages']);//筛选信息

        $smarty->assign("dataList",$datalist['cont']);
        $smarty->assign("datatype",$datatype);
    }


    //重新补发
    function TodealAction(){
        $request = Input('get.');

        if($request['mislog_id'] == ''){
            ajax_return(array('error' => 1,'errortip' => "未传入日志ID！","bakfuntion"=>"errormotify"));
        }
        $mislogOne = $this->Show_css->getOne("crm_mislog","mislog_id='{$request['mislog_id']}'");

        $array=array('13节美语课只要1元',"还有0元带回家喔!https://pay.kidcastle.com.cn/Gener/pageTwo?d={$bd}");
        if(!$this->Sendmisgo($mislogOne['mislog_mobile'], $mislogOne['mislog_mistxt'], "营销通知", rand(111111, 999999),$array,'811107')){
            ajax_return(array('error' => 1,'errortip' => $this->errortip,"bakfuntion"=>"errormotify"));
        }else{
            /**补发订单**/
            $date = array();
            $date['mislog_class'] = $mislogOne['mislog_class'];
            $date['mislog_tilte'] = $mislogOne['mislog_tilte'];
            $date['mislog_mobile'] = $mislogOne['mislog_mobile'];
            $date['mislog_sendcode'] = $mislogOne['mislog_sendcode'];
            $date['mislog_mistxt'] = $mislogOne['mislog_mistxt'];
            $date['mislog_sendinc'] = "1";
            $date['mislog_time'] = time();
            $this->Show_css->insertData("crm_mislog",$date);

            $this->Show_css->updateData("crm_mislog", "mislog_id = '{$mislogOne['mislog_id']}'", array("mislog_retry" => "1"));
            ajax_return(array('error' => 0,'errortip' => "补发成功!","bakfuntion"=>"okmotify"));
        }
    }


    function Sendmisgo($mobile, $mistxt, $tilte, $sendcode,$array=array(),$tempId='0')
    {
        $Model = new \Model\Api\SmsqmModel();
        $Model->setAccount();
        $Model->setAppId();

        $result = $Model->sendTemplateSMS($mobile, $mistxt, $tempId);
        if ($result == NULL) {
            $date = array();
            $date['company_id'] = "8888";
            $date['mislog_tilte'] = $tilte;
            $date['mislog_mobile'] = $mobile;
            $date['mislog_sendcode'] = $sendcode;
            $date['mislog_mistxt'] = $mistxt;
            $date['mislog_sendinc'] = "0";
            $date['mislog_errortip'] = "result error!";
            $date['mislog_time'] = time();
            $this->Show_css->insertData('crm_mislog', $date);
            $this->errortip = "发送异常！";
            return false;
        }
        if ($result->statusCode != 0) {
            $date = array();
            $date['company_id'] = "8888";
            $date['mislog_tilte'] = $tilte;
            $date['mislog_mobile'] = $mobile;
            $date['mislog_sendcode'] = $sendcode;
            $date['mislog_mistxt'] = $mistxt;
            $date['mislog_sendinc'] = "0";
            $date['mislog_errortip'] = "error code :" . $result->statusCode . "error msg :" . $result->statusMsg;
            $date['mislog_time'] = time();
            $this->Show_css->insertData('crm_mislog', $date);
            $this->errortip = "error code :" . $result->statusCode . "error msg :" . $result->statusMsg;
            return false;
        } else {
            $date = array();
            $date['company_id'] = "8888";
            $date['mislog_tilte'] = $tilte;
            $date['mislog_mobile'] = $mobile;
            $date['mislog_sendcode'] = $sendcode;
            $date['mislog_mistxt'] = $mistxt;
            $date['mislog_sendinc'] = "1";
            $date['mislog_time'] = time();
            $this->Show_css->insertData('crm_mislog', $date);
            return true;
        }
    }

    //删除报名活动
    function DelAction()
    {
        $list_id = Input('get.id',0);

        if($this->Show_css->delData('crm_mislog',"mislog_id='{$list_id}'")){
            ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"okmotify"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "新增失败!","bakfuntion"=>"errormotify"));
        }
    }
    function __destruct()
    {
        $site_id = Input('get.site_id',0);

        if ($this->c =='Wait') {
            $this->smarty->assign("u", $this->router->getController());
            $this->smarty->assign("t", $this->router->getUrl());
            $this->smarty->assign("Viewhtm", $this->Viewhtm);
            $websites = $this->Show_css->getOne("cms_websites","site_id='{$site_id}'");
            $this->smarty->assign("websites",$websites);
            if($site_id){
                $this->display("websiteindex.htm");
            }else{
                $this->display("index.htm");
            }
        }
        exit;
    }
}