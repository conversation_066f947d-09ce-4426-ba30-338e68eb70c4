<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn 
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/2/14
 * Time: 10:15
 */

namespace Work\Controller\Manage;


class DaoruController extends viewTpl{
    public $data;
    public $u;
    public $t;
    public $c;
    public $icampus;
    public $iuser;
    public $Viewhtm;
    public $Module;

    //预加载处理类
    function __construct() {
        parent::__construct ();
        if(!$this->check_login()){
            $this->LoginView();
        }
        if($this->check_login()){
            $this->smarty->assign("iuser", $this->UserLogin);
            $this->iuser = $this->UserLogin;
        }
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
        $this->Viewhtm = $this->router->getController()."/".$this->router->getUrl().".htm";
        $moduleOne = $this->Show_css->getOne("cms_module","module_mark='{$this->u}'");
        $this->smarty->assign("moduleOne",$moduleOne);
        $this->Module = $moduleOne;
    }

    //主页
    function HomeView()
    {
        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        //所有的分院
        $organlist = $Show_css->selectClear("select * from ks_organ WHERE organ_status = '1' ");
        $smarty->assign("organlist",$organlist);

        if(!isset($request['p'])){
            $p = '1';
        }else{
            $p= $request['p'];
        }

        $datawhere = "1";
        $pageurl = "/{$this->u}/{$this->t}?";
        if ($request['site_id']) {
            $pageurl .= "&site_id={$request['site_id']}";
        }

        $datatype = array();
        if(isset($request['keyword']) && $request['keyword'] !=''){
            $datawhere .= " and (w.worker_name like '%{$request['keyword']}%' or w.worker_account like '%{$request['keyword']}%'  or w.worker_mobile like '%{$request['keyword']}%'  or o.organ_id like '%{$request['keyword']}%'  or o.province_id like '%{$request['keyword']}%'  or o.city_id like '%{$request['keyword']}%' )";
            $pageurl .="&keyword={$request['keyword']}";
            $datatype['keyword'] = $request['keyword'];
        }

         if(isset($request['organ_id']) && $request['organ_id'] !='0'){
             $datawhere .= " and w.organ_id = '{$request['organ_id']}'";
             $pageurl .="&organ_id={$request['organ_id']}";
             $datatype['organ_id'] = $request['organ_id'];
         }

        if(isset($request['worker_type']) && $request['worker_type'] !=''){
            $datawhere .= " and w.worker_type = '{$request['worker_type']}'";
            $pageurl .="&worker_type={$request['worker_type']}";
            $datatype['worker_type'] = $request['worker_type'];
        }
        if(isset($request['worker_issensitive']) && $request['worker_issensitive'] !=''){
            $datawhere .= " and w.worker_issensitive = '{$request['worker_issensitive']}'";
            $pageurl .="&worker_issensitive={$request['worker_issensitive']}";
            $datatype['worker_issensitive'] = $request['worker_issensitive'];
        }

        $sql = "SELECT w.*,(SELECT v.list_name from cms_variablelist as v where v.variable_id = '28' and v.list_parameter = w.branch_name) as branch_namestr,o.organ_id,o.organ_name,o.organ_level FROM ks_worker as w LEFT JOIN ks_organ as o ON w.organ_id=o.organ_id where {$datawhere} order by w.worker_id DESC";

        $db_nums = $Show_css->select("SELECT COUNT(*) FROM ks_worker as w LEFT JOIN ks_organ as o ON w.organ_id=o.organ_id where {$datawhere}");//相关条件下的总记录数COUNT(*)
        $allnum = $db_nums[0][0];

        $datalist = $Show_css->dbwherePage($sql,$allnum,'10',$pageurl.'&p=',$p,'10','2');

        $smarty->assign("pagelist",$datalist['pages']);//筛选信息

        $smarty->assign("dataList",$datalist['cont']);
        $smarty->assign("datatype",$datatype);
    }

    //数据
    function TwssdView(){
        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        $sql = "SELECT r.id,r.cityname 
                FROM smc_code_region_tw as r  
                where 1 
                GROUP BY r.cityname
                order by r.id DESC";
        $datalist = $Show_css->selectClear($sql);
        if (is_array($datalist)){
            foreach ($datalist as &$datavar){
                $data = $Show_css->selectClear("SELECT r.id,r.areaname 
                                                FROM smc_code_region_tw as r  
                                                where 1 and r.cityname = '{$datavar['cityname']}'
                                                order by r.id DESC");
                $datavar['data'] = $data;
            }
        }
        $datalist = json_encode($datalist,JSON_UNESCAPED_UNICODE);
    }

    //多机构导入
    function ImportWorkerAllView(){

    }
    //商品导入
    function ImportWorkerAllExcelView(){
        $request = Input('post.','','trim,addslashes');
        if($_FILES['uploadFile']['name'] == '' || $_FILES['uploadFile']['error'] == '1'){
            $PlayInfoVar = array();
            $PlayInfoVar['worker_name'] = "导入出错";
            $PlayInfoVar['worker_mobile'] = "导入出错";
            $PlayInfoVar['error'] = "1";
            $PlayInfoVar['errortip'] = "文件不存在";
            $PlayInfo[] = $PlayInfoVar;
            $this->smarty->assign("PlayInfo", $PlayInfo);
            exit;
        }

        $fileType = array('xls','csv','xlsx');
        $uploadfile = new \Webfile($_FILES['uploadFile'],$files_dir='../static/file', $size = 2097152*10,$fileType);
        $up_file = $uploadfile->upload();

        $PlayInfo = array();
        if($up_file && $uploadfile->updatastatus) {
            $ExeclName = array();
            $ExeclName['縣市'] = "region_name";
            $ExeclName['郵遞區號'] = "region_code";
            $ExeclName['鄉鎮市區'] = "region_enname";

            $WorkerList = execl_to_array($up_file, $ExeclName);
            unset($WorkerList[0]);

            if ($WorkerList) {
                foreach ($WorkerList as $WorkerrVar) {
                    if ($WorkerrVar['縣市'] !== '' && $WorkerrVar['郵遞區號'] !== '' && $WorkerrVar['鄉鎮市區'] !== '') {
                        $workersList[] = $WorkerrVar;
                    } else {
                        $PlayInfoVar = array();
                        $PlayInfoVar['region_name'] = $WorkerrVar['region_name'];
                        $PlayInfoVar['region_enname'] = $WorkerrVar['region_enname'];
                        $PlayInfoVar['error'] = "1";
                        $PlayInfoVar['errortip'] = "导入账号信息不完整";
                        $PlayInfo[] = $PlayInfoVar;
                    }
                }
            }

            if (count($workersList) > 1000) {
                ajax_return(array('error' => 1, 'errortip' => "导入数量不能大于1000!", "bakfuntion" => "errormotify"));
            }
            if($workersList) {
                foreach ($workersList as $workersVar) {
                    $PlayInfoVar = array();
                    $PlayInfoVar['region_name'] = $workersVar['region_name'];
                    $PlayInfoVar['region_code'] = $workersVar['region_code'];
                    $PlayInfoVar['region_enname'] = $workersVar['region_enname'];

                    $data = array();
                    $data['region_name'] = $workersVar['region_name'];
                    $data['region_code'] = $workersVar['region_code'];
                    $data['region_enname'] = $workersVar['region_enname'];

                    if ($this->Show_css->insertData("smc_code_region_tw", $data)) {
                        $PlayInfoVar['error'] = "0";
                        $PlayInfoVar['errortip'] = "导入成功";
                    }else{
                        $PlayInfoVar['error'] = "1";
                        $PlayInfoVar['errortip'] = "导入失败";
                    }
                    $PlayInfo[] = $PlayInfoVar;
                }
            }

        }else{
            $PlayInfoVar = array();
            $PlayInfoVar['region_name'] = "导入出错";
            $PlayInfoVar['region_enname'] = "导入出错";
            $PlayInfoVar['error'] = "1";
            $PlayInfoVar['errortip'] = "文件不存在";
            $PlayInfo[] = $PlayInfoVar;
        }

        $this->smarty->assign("PlayInfo", $PlayInfo);
    }


    function __destruct()
    {
        $site_id = Input('get.site_id',0);

        if ($this->c =='Wait') {
            $this->smarty->assign("u", $this->router->getController());
            $this->smarty->assign("t", $this->router->getUrl());
            $this->smarty->assign("Viewhtm", $this->Viewhtm);
            $websites = $this->Show_css->getOne("cms_websites","site_id='{$site_id}'");
            $this->smarty->assign("websites",$websites);
            if($site_id){
                if($this->UserLogin['user_type'] == '1' and $this->UserLogin['user_limitsinc'] == '1'){
                    $moduleList = $this->Show_css->select("SELECT m.* FROM cms_module as m LEFT JOIN cms_moduleapply as a ON a.module_id = m.module_id WHERE a.site_id = '{$site_id}' ORDER BY m.module_weight ASC ");
                }else{
                    $moduleList = $this->Show_css->select("SELECT m.* FROM cms_module as m LEFT JOIN cms_moduleapply as a ON a.module_id = m.module_id LEFT JOIN cms_websites_func as c ON c.site_id = a.site_id and c.module_id = a.module_id WHERE a.site_id = '{$site_id}' and c.user_id = '{$this->UserLogin['user_id']}' ORDER BY m.module_weight ASC ");
                }
                $this->smarty->assign("moduleList", $moduleList);

                $this->display("websiteindex.htm");
            }else{
                $this->display("index.htm");
            }
        }
        exit;
    }
}