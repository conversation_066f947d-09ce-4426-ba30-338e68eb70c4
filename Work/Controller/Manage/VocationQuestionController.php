<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2019/11/27
 * Time: 17:03
 */

namespace Work\Controller\Manage;


class VocationQuestionController extends viewTpl{
    public $data;
    public $u;
    public $t;
    public $c;
    public $iuser;
    public $Viewhtm;

    //预加载处理类
    function __construct() {
        parent::__construct ();
        if(!$this->check_login()){
            $this->LoginView();
        }
        if($this->check_login()){
            $this->smarty->assign("iuser", $this->UserLogin);
            $this->iuser = $this->UserLogin;
        }
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
        $this->Viewhtm = $this->router->getController()."/".$this->router->getUrl().".htm";

        $moduleOne = $this->Show_css->getOne("cms_module","module_mark='{$this->u}'");
        $this->smarty->assign("moduleOne",$moduleOne);
    }


    //主页
    function HomeView()
    {
        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        if (!isset($request['p'])) {
            $p = '1';
        } else {
            $p = $request['p'];
        }

        $datawhere = "1";
        $pageurl = "/{$this->u}/{$this->t}?";
        if ($request['site_id']) {
            $pageurl .= "&site_id={$request['site_id']}";
        }

        $datatype = array();
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (q.question_id like '%{$request['keyword']}%' or q.question_pid like '%{$request['keyword']}%' or q.question_title like '%{$request['keyword']}%')";
            $pageurl .= "&keyword={$request['keyword']}";
            $datatype['keyword'] = $request['keyword'];
        }

        if (isset($request['stage_id']) && $request['stage_id'] !== '') {
            $datawhere .= " and q.stage_id = '{$request['stage_id']}'";
            $pageurl .= "&stage_id={$request['stage_id']}";
            $datatype['stage_id'] = $request['stage_id'];
        }

        $sql = "
            SELECT
                q.question_id,
                q.question_pid,
                q.stage_id,
                q.question_title,
                q.question_content,
                q.question_time,
                q.question_correct,
                q.question_issoldout,
                (select count(an.answers_id) from eas_career_answers as an where q.question_id = an.question_id) as num
            FROM
                eas_career_question AS q
            WHERE {$datawhere}
            ORDER BY question_id ASC";

        $db_nums = $Show_css->select("
            SELECT 
                COUNT(q.question_id)
            FROM
                eas_career_question AS q
            WHERE {$datawhere}");//相关条件下的总记录数COUNT(*)
        $allnum = $db_nums[0][0];

        $datalist = $Show_css->dbwherePage($sql, $allnum, '10', $pageurl . '&p=', $p, '10', '2');

        $smarty->assign("pagelist", $datalist['pages']);//筛选信息

        $stageList = $this->Show_css->selectClear("SELECT stage_id,stage_cnname FROM eas_career_stage WHERE company_id = '8888'");
        $smarty->assign("stageList", $stageList);

        $smarty->assign("dataList", $datalist['cont']);
        $smarty->assign("datatype", $datatype);
    }


    //增加系统功能
    function AddView(){
        $stageList = $this->Show_css->selectClear("SELECT stage_id,stage_cnname FROM eas_career_stage WHERE company_id = '8888' ORDER BY stage_id ASC");
        $this->smarty->assign("stageList", $stageList);
    }


    //提交处理机制
    function AddQuestionAction()
    {
        $request = Input('post.','','trim,addslashes');

        $request['question_correct'] = trim($request['question_correct']);
        if(isset($request['question_correct']) && $request['question_correct'] == ''){
            ajax_return(array('error' => 1,'errortip' => "答案必须设置!","bakfuntion"=>"dangerFromTip"));
        }

        if(isset($request['question_pid']) && $request['question_pid'] != ''){
            if($this->Show_css->getFieldOne("eas_career_question","question_id","question_pid='{$request['question_pid']}'")){
                ajax_return(array('error' => 1,'errortip' => "题目编号不可重复","bakfuntion"=>"dangerFromTip"));
            }
        }else{
            ajax_return(array('error' => 1,'errortip' => "题目编号必须设置","bakfuntion"=>"dangerFromTip"));
        }

        $data['stage_id'] = $request['stage_id'];
        $data['question_pid'] = $request['question_pid'];
        $data['question_title'] = $request['question_title'];
        $data['question_content'] = $request['question_content'];
        $data['question_time'] = $request['question_time'];
        $data['question_correct'] = $request['question_correct'];
        $data['question_analysis'] = $request['question_analysis'];
        $data['question_issoldout'] = $request['question_issoldout'];
        $data['question_createtime'] = time();
        $question_id = $this->Show_css->insertData("eas_career_question",$data);
        if(!$question_id){
            ajax_return(array('error' => 1, 'errortip' => "新增失败!", "bakfuntion" => "dangerFromTip"));
        }

        if($request['answers_option']){
            foreach($request['answers_option'] as $key => $val){
                if($val !== ''){
                    $data = array();
                    $data['question_id'] = $question_id;
                    $data['answers_option'] = $val;
                    $data['answers_optionname'] = $request['answers_optionname'][$key];
                    $data['answers_sort'] = $request['answers_sort'][$key];
                    $this->Show_css->insertData("eas_career_answers",$data);
                }
            }
        }

        ajax_return(array('error' => 0, 'errortip' => "新增成功!", "bakfuntion" => "successFromTip", "bakurl" => "/{$this->u}?site_id={$request['site_id']}"));
    }

    //增加系统功能
    function EditQuestionView()
    {
        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        $menu = array(
            '0' => 'A',
            '1' => 'B',
            '2' => 'C',
            '3' => 'D',
            '4' => 'E',
            '5' => 'F',
        );
        $smarty->assign("menu", $menu);

        $dataOne = $Show_css->getOne("eas_career_question","question_id='{$request['question_id']}'");
        $smarty->assign("dataVar", $dataOne);

        $stageList = $Show_css->selectClear("SELECT stage_id,stage_cnname FROM eas_career_stage WHERE company_id = '8888'");
        $smarty->assign("stageList", $stageList);

        $answer = $Show_css->selectClear("select an.* from eas_career_answers as an where an.question_id='{$request['question_id']}' group by an.answers_id");
        if(is_array($answer)){
            $num = count($answer);
        }else{
            $answer=array();
            $num = 0;
        }
        $difference = 4 - $num;
        if ($difference > 0) {
            $need = range(0, $difference - 1);
            $answer = array_merge($answer, $need);
        }
        $smarty->assign("answer", $answer);
    }


    //提交处理机制
    function EditQuestionAction()
    {
        $request = Input('post.','','trim,addslashes');
        $request['question_correct'] = trim($request['question_correct']);
        if($request['question_correct'] == ''){
            ajax_return(array('error' => 1,'errortip' => "答案必须设置!","bakfuntion"=>"warningFromTip"));
        }

        if(isset($request['question_pid']) && $request['question_pid'] != ''){
            if($this->Show_css->getFieldOne("eas_career_question","question_id","question_id<>'{$request['question_id']}' and question_pid='{$request['question_pid']}'")){
                ajax_return(array('error' => 1,'errortip' => "题目编号不可重复","bakfuntion"=>"warningFromTip"));
            }
        }else{
            ajax_return(array('error' => 1,'errortip' => "题目编号必须设置","bakfuntion"=>"warningFromTip"));
        }

        $data['stage_id'] = $request['stage_id'];
        $data['question_pid'] = $request['question_pid'];
        $data['question_title'] = $request['question_title'];
        $data['question_content'] = $request['question_content'];
        $data['question_time'] = $request['question_time'];
        $data['question_correct'] = $request['question_correct'];
        $data['question_analysis'] = $request['question_analysis'];
        $data['question_issoldout'] = $request['question_issoldout'];
        $data['question_updatatime'] = time();
        if(!$this->Show_css->updateData("eas_career_question","question_id='{$request['question_id']}'",$data)){
            ajax_return(array('error' => 1, 'errortip' => "编辑失败!", "bakfuntion" => "dangerFromTip"));
        }
        $this->Show_css->delData("eas_career_answers","question_id='{$request['question_id']}'");
        if($request['answers_option']){
            foreach($request['answers_option'] as $key => $val){
                if($val != ''){
                    $data = array();
                    $data['question_id'] = $request['question_id'];
                    $data['answers_option'] = $val;
                    $data['answers_optionname'] = $request['answers_optionname'][$key];
                    $data['answers_sort'] = $request['answers_sort'][$key];
                    $this->Show_css->insertData("eas_career_answers", $data);
                }
            }
        }

        ajax_return(array('error' => 0, 'errortip' => "编辑成功!", "bakfuntion" => "successFromTip", "bakurl" => "/{$this->u}?site_id={$request['site_id']}"));
    }

    //提交处理机制
    function DelAction()
    {
        $list_id = Input('get.id', 0);

        if ($this->Show_css->delData('eas_career_question', "question_id='{$list_id}'")) {
            ajax_return(array('error' => 0, 'errortip' => "删除成功!", "bakfuntion" => "okmotify", "bakurl" => "/{$this->u}"));
        } else {
            ajax_return(array('error' => 1, 'errortip' => "新增失败!", "bakfuntion" => "errormotify"));
        }
    }


    //魔术方法
    public function __call($name, $arguments) {
        $this->smarty->assign("errorTip", "Calling object method '$name' ". implode(', ', $arguments). "\n");
        $this->Viewhtm = "under.htm";
    }
    //魔术函数
    function __destruct()
    {
        $site_id = Input('get.site_id',0);

        if ($this->c =='Wait') {
            $this->smarty->assign("u", $this->router->getController());
            $this->smarty->assign("t", $this->router->getUrl());
            $this->smarty->assign("Viewhtm", $this->Viewhtm);
            $websites = $this->Show_css->getOne("cms_websites","site_id='{$site_id}'");
            $this->smarty->assign("websites",$websites);
            if($site_id){
                if($this->UserLogin['user_type'] == '1' and $this->UserLogin['user_limitsinc'] == '1'){
                    $moduleList = $this->Show_css->select("SELECT m.* FROM cms_module as m LEFT JOIN cms_moduleapply as a ON a.module_id = m.module_id WHERE a.site_id = '{$site_id}' ORDER BY m.module_weight ASC ");
                }else{
                    $moduleList = $this->Show_css->select("SELECT m.* FROM cms_module as m LEFT JOIN cms_moduleapply as a ON a.module_id = m.module_id LEFT JOIN cms_websites_func as c ON c.site_id = a.site_id and c.module_id = a.module_id WHERE a.site_id = '{$site_id}' and c.user_id = '{$this->UserLogin['user_id']}' ORDER BY m.module_weight ASC ");
                }
                $this->smarty->assign("moduleList", $moduleList);
                $this->display("websiteindex.htm");
            }else{
                $this->display("index.htm");
            }
        }
        exit;
    }
}