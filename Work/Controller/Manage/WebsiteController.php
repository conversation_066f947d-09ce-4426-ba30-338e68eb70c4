<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn 
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2016/12/14
 * Time: 0:04
 */

namespace Work\Controller\Manage;


class WebsiteController extends viewTpl{
    public $data;
    public $iuser;
    public $u;
    public $t;
    public $c;
    public $Viewhtm;

    function __construct() {
        parent::__construct ();
        if(!$this->check_login()){
            $this->LoginView();
        }
        $this->smarty->assign("iuser", $this->UserLogin);
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
        $this->Viewhtm = $this->router->getController().$this->router->getUrl().".htm";
    }
    //主页
    function HomeView()
    {
        $site_id = Input('get.id',"0");
        if($this->UserLogin['user_type'] == '1' and $this->UserLogin['user_limitsinc'] == '1'){
            $moduleList = $this->Show_css->select("SELECT m.* FROM cms_module as m LEFT JOIN cms_moduleapply as a ON a.module_id = m.module_id WHERE a.site_id = '{$site_id}' ORDER BY m.module_weight ASC ");
        }else{
            $moduleList = $this->Show_css->select("SELECT m.* FROM cms_module as m LEFT JOIN cms_moduleapply as a ON a.module_id = m.module_id LEFT JOIN cms_websites_func as c ON c.site_id = a.site_id and c.module_id = a.module_id WHERE a.site_id = '{$site_id}' and c.user_id = '{$this->UserLogin['user_id']}' ORDER BY m.module_weight ASC ");
        }
        // print_r($moduleList);die;
        $this->smarty->assign("moduleList", $moduleList);

        $weblogList = $this->Show_css->select("SELECT w.*,u.user_cnname,u.user_enname,u.user_imghead,m.module_name FROM cms_weblog as w LEFT JOIN cms_users as u ON u.user_id = w.user_id LEFT JOIN cms_module as m ON m.module_id = w.module_id WHERE w.site_id = '{$site_id}' ORDER BY w.weblog_time DESC limit 0,4");
        $this->smarty->assign("weblogList", $weblogList);


        $total = array();

        $this->smarty->assign("alltotal",$total);


        $this->Viewhtm = $this->router->getController()."/".$this->router->getUrl().".htm";
    }
    //站点操作日志
    function WeblogView()
    {
        $request = Input('get.','','trim,addslashes');
        $site_id = $request['id'];

        if(!isset($request['p'])){
            $p = '1';
        }else{
            $p= $request['p'];
        }

        $datawhere = "w.site_id = '{$site_id}'";
        $pageurl = "/{$this->u}/{$this->t}/id-{$site_id}?";

        $datatype = array();
        if(isset($request['keyword']) && $request['keyword'] !==''){
            $datawhere .= " and (w.tb_content like '%{$request['keyword']}%' or m.module_name like '%{$request['keyword']}%')";
            $pageurl .="&keyword={$request['keyword']}";
            $datatype['keyword'] = $request['keyword'];
        }

        if(isset($request['status'])){
            $datawhere .= " and a.tb_status = '{$request['status']}'";
            $pageurl .="&status={$request['status']}";
            $datatype['status'] = $request['status'];
        }

        $sql = "SELECT w.*,u.user_cnname,u.user_enname,u.user_imghead,m.module_name FROM cms_weblog as w LEFT JOIN cms_users as u ON u.user_id = w.user_id LEFT JOIN cms_module as m ON m.module_id = w.module_id WHERE {$datawhere} order by w.tb_time DESC";

        $db_nums = $this->Show_css->select("SELECT COUNT(*) FROM cms_weblog as w LEFT JOIN cms_users as u ON u.user_id = w.user_id LEFT JOIN cms_module as m ON m.module_id = w.module_id WHERE {$datawhere}");//相关条件下的总记录数COUNT(*)
        $allnum = $db_nums[0][0];

        $datalist = $this->Show_css->dbwherePage($sql,$allnum,'10',$pageurl.'&p=',$p,'10','2');

        $this->smarty->assign("pagelist",$datalist['pages']);//筛选信息

        $this->smarty->assign("dataList",$datalist['cont']);
        $this->smarty->assign("datatype",$datatype);
        $this->Viewhtm = $this->router->getController()."/".$this->router->getUrl().".htm";
    }
    //站点操作日志
    function AccountsetView()
    {
        $this->smarty->assign("act", "Edit");
        $this->Viewhtm = $this->router->getController()."/".$this->router->getUrl().".htm";
    }
    //提交处理机制
    function EditAction()
    {
        $request = Input('post.','','trim,addslashes');

        $data = array();

        if($request['user_pass'] !==''){
            $data['user_pass'] = md5($request['user_pass']);
        }
        $data['user_imghead'] = $request['user_imghead'];
        $data['user_cnname'] = $request['user_cnname'];
        $data['user_enname'] = $request['user_enname'];
        $data['user_email'] = $request['user_email'];
        $data['user_mobile'] = $request['user_mobile'];
        if($this->Show_css->updateData("cms_users","user_id = '{$this->UserLogin['user_id']}'",$data)){
            ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"okmotify","bakurl"=>"/{$this->u}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "新增失败!","bakfuntion"=>"errormotify"));
        }
    }

    //主页
    function FeedbackView()
    {
        $request = Input('get.','','trim,addslashes');
        $site_id = $request['id'];

        if(!isset($request['p'])){
            $p = '1';
        }else{
            $p= $request['p'];
        }

        $datawhere = "f.site_id = '{$site_id}'";
        $pageurl = "/{$this->u}/{$this->t}/id-{$site_id}?";

        $datatype = array();
        if(isset($request['keyword']) && $request['keyword'] !==''){
            $datawhere .= " and (f.feedback_title like '%{$request['keyword']}%' or f.feedback_name like '%{$request['keyword']}%' or f.feedback_mobile like '%{$request['keyword']}%')";
            $pageurl .="&keyword={$request['keyword']}";
            $datatype['keyword'] = $request['keyword'];
        }

        if(isset($request['status'])){
            $datawhere .= " and a.tb_status = '{$request['status']}'";
            $pageurl .="&status={$request['status']}";
            $datatype['status'] = $request['status'];
        }

        $sql = "SELECT f.* FROM cms_feedback as f WHERE {$datawhere} order by f.feedback_time DESC";

        $db_nums = $this->Show_css->select("SELECT COUNT(*) FROM cms_feedback as f WHERE {$datawhere}");//相关条件下的总记录数COUNT(*)
        $allnum = $db_nums[0][0];

        $datalist = $this->Show_css->dbwherePage($sql,$allnum,'10',$pageurl.'&p=',$p,'10','2');

        $this->smarty->assign("pagelist",$datalist['pages']);//筛选信息

        $this->smarty->assign("dataList",$datalist['cont']);
        $this->smarty->assign("datatype",$datatype);
        $this->Viewhtm = $this->router->getController()."/".$this->router->getUrl().".htm";
    }
    //魔术方法
    public function __call($name, $arguments) {
        $this->Viewhtm = "under.htm";
        // Note: value of $name is case sensitive.
        //echo "Calling object method '$name' ". implode(', ', $arguments). "\n";
    }
    //魔术方法
    function __destruct()
    {
        $site_id = Input('get.id',"0");
        $websites = $this->Show_css->getOne("cms_websites","site_id='{$site_id}'");
        $this->smarty->assign("websites",$websites);

        if ($this->c =='Wait') {
            $this->smarty->assign("u", $this->router->getController());
            $this->smarty->assign("t", $this->router->getUrl());
            $this->smarty->assign("Viewhtm", $this->Viewhtm);
            $this->display("websiteindex.htm");
        }
        exit;
    }
}
