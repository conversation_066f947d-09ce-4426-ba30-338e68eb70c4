<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn 
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/3/22
 * Time: 17:56
 */

namespace Work\Controller\Manage;


class IuserController extends viewTpl{
    public $data;
    public $iuser;
    public $u;
    public $t;
    public $c;
    public $Viewhtm;

    function __construct() {
        parent::__construct ();
        if(!$this->check_login()){
            $this->LoginView();
        }
        $this->smarty->assign("iuser", $this->UserLogin);
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
        $this->iuser = $this->UserLogin;
        $this->Viewhtm = $this->router->getController()."/".$this->router->getUrl().".htm";
    }
    //主页
    function HomeView()
    {
        $usersList = $this->Show_css->select("SELECT m.user_cnname,m.user_imghead FROM cms_users as m order by m.user_id DESC");
        $this->smarty->assign("usersList",$usersList);
    }
    //主页
    function EditView()
    {
        $this->smarty->assign("act", "Edit");
    }
    //提交处理机制
    function EditAction()
    {
        $request = Input('post.','','trim,addslashes');

        $data = array();
        if($request['user_pass'] !==''){
            $data['user_pass'] = md5($request['user_pass']);
        }
        $data['user_imghead'] = $request['user_imghead'];
        $data['user_cnname'] = $request['user_cnname'];
        $data['user_enname'] = $request['user_enname'];
        $data['user_email'] = $request['user_email'];
        $data['user_mobile'] = $request['user_mobile'];
        if($this->Show_css->updateData("cms_users","user_id = '{$this->iuser['user_id']}'",$data)){
            ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"refreshpage"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "新增失败!","bakfuntion"=>"errormotify"));
        }
    }
    //魔术方法
    public function __call($name, $arguments) {
        $this->Viewhtm = "under.htm";
        // Note: value of $name is case sensitive.
        //echo "Calling object method '$name' ". implode(', ', $arguments). "\n";
    }
    //魔术方法
    function __destruct()
    {
        $site_id = Input('get.id',"0");
        $websites = $this->Show_css->getOne("cms_websites","site_id='{$site_id}'");
        $this->smarty->assign("websites",$websites);

        if ($this->c =='Wait') {
            $this->smarty->assign("u", $this->router->getController());
            $this->smarty->assign("t", $this->router->getUrl());
            $this->smarty->assign("Viewhtm", $this->Viewhtm);
            $this->display("websiteindex.htm");
        }
        exit;
    }

}