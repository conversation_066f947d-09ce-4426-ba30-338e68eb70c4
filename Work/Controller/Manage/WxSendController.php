<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn 
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2016/12/16
 * Time: 11:51
 */

namespace Work\Controller\Manage;


class WxSendController  extends viewTpl{
    public $data;
    public $u;
    public $t;
    public $c;
    public $iuser;
    public $Viewhtm;

    //预加载处理类
    function __construct() {
        parent::__construct ();
        if(!$this->check_login()){
            $this->LoginView();
        }
        if($this->check_login()){
            $this->smarty->assign("iuser", $this->UserLogin);
            $this->iuser = $this->UserLogin;
        }
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
        $this->Viewhtm = $this->router->getController()."/".$this->router->getUrl().".htm";


        $moduleOne = $this->Show_css->getOne("cms_module","module_mark='{$this->u}'");
        $this->smarty->assign("moduleOne",$moduleOne);

    }

    //筛选对应的学校 吉的堡学校信息 select2 的应用
    function getJDBSchoolAction(){
        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $datawhere = " 1 and s.company_id = '8888' and s.school_istest = '0' and s.school_isclose = '0'   ";
        if(isset($request['keyword']) && $request['keyword'] !== ''){
            $datawhere .= " and (s.school_branch like '%{$request['keyword']}%' or s.school_shortname like '%{$request['keyword']}%' or s.school_cnname like '%{$request['keyword']}%') ";
        }
//        if(isset($request['nature']) && $request['nature'] !== '0' && $request['nature'] !== ''){
//            $datawhere .= " and s.school_nature = '{$request['nature']}'";
//        }

        if(isset($request['p']) && $request['p'] !== ''){
            $page = $request['p'];
        }else{
            $page = '1';
        }
        if(isset($request['num']) && $request['num'] !== ''){
            $num = $request['num'];
        }else{
            $num = '10';
        }
        $pagestart = ($page-1)*$num;
        $itemList = $Show_css->selectClear("SELECT s.school_id,s.school_branch,s.school_cnname,s.school_shortname FROM smc_school AS s where {$datawhere} ORDER BY s.school_id DESC limit {$pagestart},{$num} ");
        if($itemList){
            $result = array();
            $result["list"] = $itemList;
            $res = array('error' => '0', 'errortip' => '获取校园信息', 'result' => $result);
            ajax_return($res);
        }else{
            $data=array();
            $result["list"] = $data;
            $res = array('error' => '1', 'errortip' => '暂无校园信息', 'result' => $result);
            ajax_return($res);
        }
    }

    //主页
    function HomeView()
    {
        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        //集团信息里边
        $companyList = $this->Show_css->selectClear("SELECT c.* from gmc_company as c ");//WHERE c.company_status = '1'
        $smarty->assign("companyList",$companyList);

        if(!isset($request['p'])){
            $p = '1';
        }else{
            $p= $request['p'];
        }

        $datawhere = "1 and s.school_isclose = '0' and s.school_istest = '0' ";
        $pageurl = "/{$this->u}/{$this->t}?";
        if ($request['site_id']) {
            $pageurl .= "&site_id={$request['site_id']}";
        }

        $datatype = array();
        if(isset($request['keyword']) && $request['keyword'] !==''){
            $datawhere .= " and (s.school_branch like '%{$request['keyword']}%' or s.school_cnname like '%{$request['keyword']}%')";
            $pageurl .="&keyword={$request['keyword']}";
            $datatype['keyword'] = $request['keyword'];
        }
        if(isset($request['company_id']) && $request['company_id'] !==''){
            $datawhere .= " and (s.company_id={$request['company_id']})";
            $pageurl .="&company_id={$request['company_id']}";
            $datatype['company_id'] = $request['company_id'];
        }
        if(isset($request['school_type']) && $request['school_type'] !==''){
            $datawhere .= " and (s.school_type={$request['school_type']})";
            $pageurl .="&school_type={$request['school_type']}";
            $datatype['school_type'] = $request['school_type'];
        }

        $sql = "SELECT s.*,c.company_cnname,c.company_code,(SELECT t.staffer_branch FROM smc_staffer as t WHERE c.company_id=t.company_id and t.account_class = '1' limit 0,1) as staffer_branch,(SELECT t.staffer_bakpass FROM smc_staffer as t WHERE c.company_id=t.company_id and t.account_class = '1' limit 0,1) as staffer_bakpass   
                FROM smc_school as s 
                LEFT JOIN gmc_company as c ON s.company_id = c.company_id  
                where {$datawhere} order by s.school_id  DESC";

        $db_nums = $Show_css->selectOne("SELECT COUNT(s.school_id) as num FROM smc_school as s 
                                LEFT JOIN gmc_company as c ON s.company_id = c.company_id 
                                where {$datawhere}");//相关条件下的总记录数COUNT(*)

        $allnum = $db_nums['num'];

        $datalist = $Show_css->dbwherePage($sql,$allnum,'10',$pageurl.'&p=',$p,'10','2');

        $smarty->assign("pagelist",$datalist['pages']);//筛选信息

        $smarty->assign("dataList",$datalist['cont']);
        $smarty->assign("datatype",$datatype);
    }


    //魔术方法
    public function __call($name, $arguments) {
        $this->smarty->assign("errorTip", "Calling object method '$name' ". implode(', ', $arguments). "\n");
        $this->Viewhtm = "under.htm";
    }
    //魔术函数
    function __destruct()
    {
        $site_id = Input('get.site_id',0);

        if ($this->c =='Wait') {
            $this->smarty->assign("u", $this->router->getController());
            $this->smarty->assign("t", $this->router->getUrl());
            $this->smarty->assign("Viewhtm", $this->Viewhtm);
            $websites = $this->Show_css->getOne("cms_websites","site_id='{$site_id}'");
            $this->smarty->assign("websites",$websites);
            if($site_id){
                if($this->UserLogin['user_type'] == '1' and $this->UserLogin['user_limitsinc'] == '1'){
                    $moduleList = $this->Show_css->select("SELECT m.* FROM cms_module as m LEFT JOIN cms_moduleapply as a ON a.module_id = m.module_id WHERE a.site_id = '{$site_id}' ORDER BY m.module_weight ASC ");
                }else{
                    $moduleList = $this->Show_css->select("SELECT m.* FROM cms_module as m LEFT JOIN cms_moduleapply as a ON a.module_id = m.module_id LEFT JOIN cms_websites_func as c ON c.site_id = a.site_id and c.module_id = a.module_id WHERE a.site_id = '{$site_id}' and c.user_id = '{$this->UserLogin['user_id']}' ORDER BY m.module_weight ASC ");
                }
                // print_r($moduleList);die;
                $this->smarty->assign("moduleList", $moduleList);
                $this->display("websiteindex.htm");
            }else{
                $this->display("index.htm");
            }
        }
        exit;
    }

}