<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2019/12/12
 * Time: 18:13
 */

namespace Work\Controller\Manage;


class OpenClassTypeController extends viewTpl{
    public $data;
    public $u;
    public $t;
    public $c;
    public $iuser;
    public $Viewhtm;

    //预加载处理类
    function __construct() {
        parent::__construct ();
        if(!$this->check_login()){
            $this->LoginView();
        }
        if($this->check_login()){
            $this->smarty->assign("iuser", $this->UserLogin);
            $this->iuser = $this->UserLogin;
        }
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
        $this->Viewhtm = $this->router->getController()."/".$this->router->getUrl().".htm";

        $moduleOne = $this->Show_css->getOne("cms_module","module_mark='{$this->u}'");
        $this->smarty->assign("moduleOne",$moduleOne);
    }

    //主页
    function HomeView(){
        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        if (!isset($request['p'])) {
            $p = '1';
        } else {
            $p = $request['p'];
        }

        $datawhere = "1";
        $pageurl = "/{$this->u}/{$this->t}?";
        if ($request['site_id']) {
            $pageurl .= "&site_id={$request['site_id']}";
        }

        $datatype = array();
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (openclasstype_cnname like '%{$request['keyword']}%' or openclasstype_enname like '%{$request['keyword']}%' or openclasstype_branch like '%{$request['keyword']}%')";
            $pageurl .= "&keyword={$request['keyword']}";
            $datatype['keyword'] = $request['keyword'];
        }

        $sql = "SELECT * FROM eas_code_openclasstype WHERE {$datawhere} ORDER BY openclasstype_id ASC";

        $db_nums = $Show_css->select("SELECT * FROM eas_code_openclasstype WHERE {$datawhere}");//相关条件下的总记录数COUNT(*)
        $allnum = $db_nums[0][0];

        $datalist = $Show_css->dbwherePage($sql, $allnum, '10', $pageurl . '&p=', $p, '10', '2');

        $smarty->assign("pagelist", $datalist['pages']);//筛选信息
        $smarty->assign("dataList", $datalist['cont']);
        $smarty->assign("datatype", $datatype);
    }

    //添加职业明细
    function AddView(){
        $this->smarty->assign("act","Add");

        $this->Viewhtm = $this->router->getController()."/"."Manage.htm";
    }

    //提交处理机制
    function AddAction(){
        $request = Input('post.','','trim,addslashes');
        if($request['openclasstype_cnname'] == ''){
            ajax_return(array('error' => 1,'errortip' => "职业类型中文名不能为空!","bakfuntion"=>"dangerFromTip"));
        }
        if($request['openclasstype_enname'] == ''){
            ajax_return(array('error' => 1,'errortip' => "职业类型英文名不能为空!","bakfuntion"=>"dangerFromTip"));
        }
        if($request['openclasstype_branch'] == ''){
            ajax_return(array('error' => 1,'errortip' => "职业类型编号不能为空!","bakfuntion"=>"dangerFromTip"));
        }
        if($this->Show_css->getOne("eas_code_openclasstype","openclasstype_branch='{$request['openclasstype_branch']}'")){
            ajax_return(array('error' => 1,'errortip' => "公开课类型编号重复!","bakfuntion"=>"dangerFromTip"));
        }

        $data = array();
        $data['company_id'] = '8888';
        $data['openclasstype_cnname'] = $request['openclasstype_cnname'];
        $data['openclasstype_enname'] = $request['openclasstype_enname'];
        $data['openclasstype_branch'] = $request['openclasstype_branch'];
        $dataid = $this->Show_css->insertData("eas_code_openclasstype",$data);
        if($dataid){
            ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"successFromTip","bakurl"=>"/{$this->u}?site_id={$request['site_id']}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "新增失败!","bakfuntion"=>"errormotify"));
        }
    }

    //编辑职业明细
    function EditView(){
        $request = Input('get.','','trim,addslashes');

        $this->smarty->assign("act","Edit");

        $dataOne = $this->Show_css->getOne("eas_code_openclasstype","openclasstype_id='{$request['openclasstype_id']}'");
        $this->smarty->assign("dataVar", $dataOne);

        $this->Viewhtm = $this->router->getController()."/"."Manage.htm";
    }

    //提交处理机制
    function EditAction(){
        $request = Input('post.','','trim,addslashes');
        if($request['openclasstype_cnname'] == ''){
            ajax_return(array('error' => 1,'errortip' => "职业类型中文名不能为空!","bakfuntion"=>"dangerFromTip"));
        }
        if($request['openclasstype_enname'] == ''){
            ajax_return(array('error' => 1,'errortip' => "职业类型英文名不能为空!","bakfuntion"=>"dangerFromTip"));
        }
        if($request['openclasstype_branch'] == ''){
            ajax_return(array('error' => 1,'errortip' => "职业类型编号不能为空!","bakfuntion"=>"dangerFromTip"));
        }
        if($this->Show_css->getOne("eas_code_openclasstype","openclasstype_id<>'{$request['openclasstype_id']}' and openclasstype_branch='{$request['openclasstype_branch']}'")){
            ajax_return(array('error' => 1,'errortip' => "公开课类型编号重复!","bakfuntion"=>"dangerFromTip"));
        }

        $data = array();
        $data['company_id'] = '8888';
        $data['openclasstype_cnname'] = $request['openclasstype_cnname'];
        $data['openclasstype_enname'] = $request['openclasstype_enname'];
        $data['openclasstype_branch'] = $request['openclasstype_branch'];
        if($this->Show_css->updateData("eas_code_openclasstype","openclasstype_id='{$request['openclasstype_id']}'",$data)){
            ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"successFromTip","bakurl"=>"/{$this->u}?site_id={$request['site_id']}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "编辑失败!","bakfuntion"=>"errormotify"));
        }
    }

    //删除
    function DelAction(){
        $request = Input('get.','','trim,addslashes');

        if($this->Show_css->delData("eas_code_openclasstype","openclasstype_id='{$request['id']}'")){
            ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"successFromTip","bakurl"=>"/{$this->u}?site_id={$request['site_id']}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "删除失败!","bakfuntion"=>"errormotify"));
        }
    }


    //魔术方法
    public function __call($name, $arguments) {
        $this->smarty->assign("errorTip", "Calling object method '$name' ". implode(', ', $arguments). "\n");
        $this->Viewhtm = "under.htm";
    }

    //魔术函数
    function __destruct()
    {
        $site_id = Input('get.site_id',0);

        if ($this->c =='Wait') {
            $this->smarty->assign("u", $this->router->getController());
            $this->smarty->assign("t", $this->router->getUrl());
            $this->smarty->assign("Viewhtm", $this->Viewhtm);
            $websites = $this->Show_css->getOne("cms_websites","site_id='{$site_id}'");
            $this->smarty->assign("websites",$websites);
            if($site_id){
                if($this->UserLogin['user_type'] == '1' and $this->UserLogin['user_limitsinc'] == '1'){
                    $moduleList = $this->Show_css->select("SELECT m.* FROM cms_module as m LEFT JOIN cms_moduleapply as a ON a.module_id = m.module_id WHERE a.site_id = '{$site_id}' ORDER BY m.module_weight ASC ");
                }else{
                    $moduleList = $this->Show_css->select("SELECT m.* FROM cms_module as m LEFT JOIN cms_moduleapply as a ON a.module_id = m.module_id LEFT JOIN cms_websites_func as c ON c.site_id = a.site_id and c.module_id = a.module_id WHERE a.site_id = '{$site_id}' and c.user_id = '{$this->UserLogin['user_id']}' ORDER BY m.module_weight ASC ");
                }
                // print_r($moduleList);die;
                $this->smarty->assign("moduleList", $moduleList);
                $this->display("websiteindex.htm");
            }else{
                $this->display("index.htm");
            }
        }
        exit;
    }
}