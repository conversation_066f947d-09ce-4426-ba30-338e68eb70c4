<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn 
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong <PERSON>
 * Date: 2017/2/17
 * Time: 11:42
 */

namespace Work\Controller\Manage;


class VariableController extends viewTpl{
    public $data;
    public $iuser;
    public $u;
    public $t;
    public $c;
    public $Viewhtm;

    function __construct() {
        parent::__construct ();
        if(!$this->check_login()){
            $this->LoginView();
        }
        $this->smarty->assign("iuser", $this->UserLogin);
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
        $this->Viewhtm = $this->router->getController()."/".$this->router->getUrl().".htm";
        $moduleOne = $this->Show_css->getOne("cms_module","module_mark='{$this->u}'");
        $this->smarty->assign("moduleOne",$moduleOne);
        $this->Module = $moduleOne;
    }

    //主页
    function HomeView()
    {

        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        if(!isset($request['p'])){
            $p = '1';
        }else{
            $p= $request['p'];
        }

        $datawhere = "1";
        $pageurl = "/{$this->u}/{$this->t}?";

        $datatype = array();
        if(isset($request['keyword']) && $request['keyword'] !==''){
            $datawhere .= " and (v.variable_name like '%{$request['keyword']}%' or v.variable_string like '%{$request['keyword']}%')";
            $pageurl .="&keyword={$request['keyword']}";
            $datatype['keyword'] = $request['keyword'];
        }

        if(isset($request['type'])){
            $datawhere .= " and v.variable_type = '{$request['type']}'";
            $pageurl .="&type={$request['type']}";
            $datatype['type'] = $request['type'];
        }

        $sql = "SELECT v.* FROM cms_variable as v where {$datawhere} order by v.variable_id DESC ";

        $db_nums = $Show_css->select("SELECT COUNT(*) FROM cms_variable as v where {$datawhere}");//相关条件下的总记录数COUNT(*)
        $allnum = $db_nums[0][0];

        $datalist = $Show_css->dbwherePage($sql,$allnum,'10',$pageurl.'&p=',$p,'10','2');

        $smarty->assign("pagelist",$datalist['pages']);
        $smarty->assign("dataList",$datalist['cont']);
        $smarty->assign("datatype",$datatype);
    }

    //增加系统功能
    function AddView()
    {
        $this->smarty->assign("act","Add");
        $this->Viewhtm = $this->router->getController()."/"."Manage.htm";
    }
    //提交处理机制
    function AddAction()
    {
        $request = Input('post.','','trim,addslashes');
        $data = array();

        $data['variable_name'] = $request['variable_name'];
        $data['variable_type'] = $request['variable_type'];
        $data['variable_string'] = $request['variable_string'];
        $data['addtime'] = time();
        if($this->Show_css->insertData("cms_variable",$data)){
            ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"successFromTip"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "新增失败!","bakfuntion"=>"dangerFromTip"));
        }
    }
    //增加系统功能
    function EditView()
    {

        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        $variableOne = $Show_css->getOne("cms_variable", "variable_id='{$request['id']}'");
        $smarty->assign("dataVar", $variableOne);

        $smarty->assign("act", "Edit");

        $this->Viewhtm = $this->router->getController()."/"."Manage.htm";
    }
    function TabeditView(){
        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        $smarty->assign("act", "Tabedit");

        $variableOne = $Show_css->getOne("cms_variable", "variable_id='{$request['id']}'");
        $smarty->assign("dataVar", $variableOne);


        $variablelist = $Show_css->getList("cms_variablelist", "variable_id='{$variableOne['variable_id']}'","Order by list_weight ASC");
        $smarty->assign("variablelist", $variablelist);

        $this->Viewhtm = $this->router->getController()."/"."Tabedit.htm";
    }
    //提交处理机制
    function TabeditAction()
    {
        $request = Input('post.','','trim,addslashes');
        //附加选项列表
        if(count($request['name']) > 0 && $request['name'][0] !==''){
            for($basei=0;$basei < count($request['name']);$basei++){
                $baselist = array();
                $baselist['list_name'] = $request['name'][$basei];
                $baselist['list_parameter'] = $request['parameter'][$basei];
                $baselist['list_number'] = $request['number'][$basei];
                $baselist['list_weight'] = $request['weight'][$basei];
                $baselist['variable_id'] = $request['variable_id'];
                $this->Show_css->insertData('cms_variablelist',$baselist);
            }
            ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"okmotify","bakurl"=>"/{$this->u}/{$this->c}?id=".$request['variable_id']));
        }else{
            ajax_return(array('error' => 1,'errortip' => "没有任何新数据更新!","bakfuntion"=>"errormotify"));
        }
    }
    function PerfectView()
    {
        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        $variableOne = $Show_css->getOne("cms_variable", "variable_id='{$request['id']}'");
        $smarty->assign("dataVar", $variableOne);

        $smarty->assign("act", "Perfect");

        $this->Viewhtm = $this->router->getController()."/"."Perfect.htm";
    }
    //提交处理机制
    function PerfectAction()
    {
        $request = Input('post.','','trim,addslashes');

        $data = array();
        $data['content'] = $request['content'];
        $data['addtime'] = time();
        if($this->Show_css->updateData("cms_variable","variable_id = '{$request['variable_id']}'",$data)){
            ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"okmotify","bakurl"=>"/{$this->u}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "新增失败!","bakfuntion"=>"errormotify"));
        }
    }
    function EditTabListAction(){
        $request = Input('get.','','trim,addslashes');
        $data = array();
        $data[$request['field']] = $request['value'];
        if($this->Show_css->updateData("cms_variablelist","list_id = '{$request['id']}'",$data)){
            ajax_return(array('error' => 0,'errortip' => "修改成功!"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "修改失败!"));
        }
    }
    //提交处理机制
    function EditAction()
    {
        $request = Input('post.','','trim,addslashes');

        $data = array();
        $data['variable_name'] = $request['variable_name'];
        $data['variable_type'] = $request['variable_type'];
        $data['variable_string'] = $request['variable_string'];
        $data['addtime'] = time();
        if($this->Show_css->updateData("cms_variable","variable_id = '{$request['variable_id']}'",$data)){
            ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"okmotify","bakurl"=>"/{$this->u}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "新增失败!","bakfuntion"=>"errormotify"));
        }
    }
    //提交处理机制
    function DelAction()
    {
        $variable_id = Input('get.id',0);

        if($this->Show_css->delData('cms_variable',"variable_id='{$variable_id}'")){
            ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"okmotify"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "新增失败!","bakfuntion"=>"errormotify"));
        }
    }
    //提交处理机制
    function DelTabAction()
    {
        $list_id = Input('get.id',0);

        if($this->Show_css->delData('cms_variablelist',"list_id='{$list_id}'")){
            ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"okmotify"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "新增失败!","bakfuntion"=>"errormotify"));
        }
    }
    //魔术方法
    public function __call($name, $arguments) {
        $this->smarty->assign("errorTip", "Calling object method '$name' ". implode(', ', $arguments). "\n");

        $this->Viewhtm = "under.htm";
    }
    //魔术方法
    function __destruct()
    {
        $site_id = Input('get.site_id',0);

        if ($this->c =='Wait') {
            $this->smarty->assign("u", $this->router->getController());
            $this->smarty->assign("t", $this->router->getUrl());
            $this->smarty->assign("Viewhtm", $this->Viewhtm);
            if($site_id){
                $websites = $this->Show_css->getOne("cms_websites","site_id='{$site_id}'");
                $this->smarty->assign("websites",$websites);
                $this->display("websiteindex.htm");
            }else{
                $this->display("index.htm");
            }
        }
        exit;
    }

}