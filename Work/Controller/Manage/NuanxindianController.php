<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn 
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2016/12/16
 * Time: 11:51
 */

namespace Work\Controller\Manage;


class NuanxindianController  extends viewTpl{
    public $data;
    public $u;
    public $t;
    public $c;
    public $iuser;
    public $Viewhtm;

    //预加载处理类
    function __construct() {
        parent::__construct ();
        if(!$this->check_login()){
            $this->LoginView();
        }
        if($this->check_login()){
            $this->smarty->assign("iuser", $this->UserLogin);
            $this->iuser = $this->UserLogin;
        }
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
        $this->Viewhtm = $this->router->getController()."/".$this->router->getUrl().".htm";


        $moduleOne = $this->Show_css->getOne("cms_module","module_mark='{$this->u}'");
        $this->smarty->assign("moduleOne",$moduleOne);

    }
    //主页
    function HomeView()
    {
        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        if(!isset($request['p'])){
            $p = '1';
        }else{
            $p= $request['p'];
        }

        $datawhere = "1 and c.activity_id = '89' ";
        $pageurl = "/{$this->u}/{$this->t}?";
        if ($request['site_id']) {
            $pageurl .= "&site_id={$request['site_id']}";
        }

        $datatype = array();
        if(isset($request['keyword']) && $request['keyword'] !==''){
            $datawhere .= " and (c.client_cnname like '%{$request['keyword']}%' or c.client_email like '%{$request['keyword']}%' or c.client_mobile like '%{$request['keyword']}%' or c.client_patriarchname like '%{$request['keyword']}%')";
            $pageurl .="&keyword={$request['keyword']}";
            $datatype['keyword'] = $request['keyword'];
        }
//        if(isset($request['module_class']) && $request['module_class'] !==''){
//            $datawhere .= " and (m.module_class={$request['module_class']})";
//            $pageurl .="&module_class={$request['module_class']}";
//            $datatype['module_class'] = $request['module_class'];
//        }

        $sql = "SELECT c.*,r.region_name as areaname,
                (SELECT g.region_name from smc_code_region as g WHERE g.region_id = c.city_id) as cityname
                FROM crm_client as c  
                LEFT JOIN  smc_code_region as r ON c.area_id = r.region_id
                where {$datawhere} order by c.client_id DESC";

        $db_nums = $Show_css->selectOne("SELECT COUNT(c.client_id) as num FROM crm_client as c 
                LEFT JOIN  smc_code_region as r ON c.area_id = r.region_id where {$datawhere}");//相关条件下的总记录数COUNT(*)
        $allnum = $db_nums['num'];

        $datalist = $Show_css->dbwherePage($sql,$allnum,'10',$pageurl.'&p=',$p,'10','2');

        $smarty->assign("pagelist",$datalist['pages']);//筛选信息

        $smarty->assign("dataList",$datalist['cont']);
        $smarty->assign("datatype",$datatype);
    }


    //导出
    function ExportView(){
        //台湾城市
        $cityList = $this->Show_css->selectClear("select region_id,region_name from smc_code_region WHERE parent_id='5005' ORDER BY region_sort ASC ");
        $this->smarty->assign("cityList",$cityList);
    }
    //历史案件下载管理
    function ExportAction(){
        $request=Input('post.','','trim,addslashes');
        $datawhere = " 1 and c.activity_id = '89' ";
        if(isset($request['province_id']) && $request['province_id'] !==''){
            $datawhere .= " and o.province_id = '{$request['province_id']}'";
            $datatype['province_id'] = $request['province_id'];
        }
        if(isset($request['city_id']) && $request['city_id'] !=='0'){
            $datawhere .= " and o.city_id = '{$request['city_id']}'";
            $datatype['city_id'] = $request['city_id'];
        }
        if(isset($request['starttime']) && $request['starttime'] != '' && isset($request['endtime']) && $request['endtime'] != ''){
            $datawhere .= " and FROM_UNIXTIME(client_createtime,'%Y-%m-%d') <= '{$request['endtime']}' and FROM_UNIXTIME(client_createtime,'%Y-%m-%d') >= '{$request['starttime']}'";
        }elseif(isset($request['starttime']) && $request['starttime'] != ''){
            $datawhere .= " and FROM_UNIXTIME(client_createtime,'%Y-%m-%d') >= '{$request['starttime']}'";
        }elseif(isset($request['endtime']) && $request['endtime'] != ''){
            $datawhere .= " and FROM_UNIXTIME(client_createtime,'%Y-%m-%d') <= '{$request['endtime']}'";
        }
        $sql = "SELECT c.*,r.region_name as areaname,
                (SELECT g.region_name from smc_code_region as g WHERE g.region_id = c.city_id) as cityname
                FROM crm_client as c  
                LEFT JOIN  smc_code_region as r ON c.area_id = r.region_id
                where {$datawhere} order by c.client_id DESC ";
        $dataList=$this->Show_css->select($sql);
        if($dataList){
            $outexcel=array();
            foreach($dataList as $dataVar){
                $dataarray=array();
                $dataarray['client_id']=$dataVar['client_id']; //ID
                $dataarray['client_cnname']=$dataVar['client_cnname']; //宝贝姓名
                $dataarray['client_age']=$dataVar['client_age']; //宝贝年龄
                $dataarray['client_primaryschool']=$dataVar['client_primaryschool']; //就读国小
                $dataarray['client_primaryschoolgrade']=$dataVar['client_primaryschoolgrade']; //就读年级
                $dataarray['client_patriarchname']=$dataVar['client_patriarchname']; //家长姓名
                $dataarray['client_mobile']=$dataVar['client_mobile']; //家长电话
                $dataarray['cityname']=$dataVar['cityname']; //县市
                $dataarray['areaname']=$dataVar['areaname']; //市区
                $dataarray['client_address']=$dataVar['client_address']; //地址
                $dataarray['client_createtime']=date("Y-m-d H:i:s",$dataVar['client_createtime']); //地址

                $outexcel[]=$dataarray;
            }
        }
        $excelheader=array("ID","宝贝姓名","宝贝年龄","就读国小","就读年级","家长姓名","家长电话","县市","市区","地址","报名时间");
        $excelfields=array("client_id","client_cnname","client_age","client_primaryschool","client_primaryschoolgrade","client_patriarchname","client_mobile","cityname","areaname","client_address","client_createtime");

        if($request['starttime'] !== ''  && $request['endtime'] !== ''){
            $excelname = $request['starttime'].'到'.$request['endtime']."暖心店报名信息.xlsx";
        }else{
            $excelname = date(Ymd)."暖心店报名信息.xlsx";
        }

        if(!is_array($outexcel)){
            jsbakerror_spl("没有数据！");
            exit;
        }
        query_to_excel($excelheader,$outexcel,$excelfields,$excelname);
        ajax_return(array("error"=>0,"errortip"=>"下载完毕!","bakfuntion"=>"okmotify"));
    }



    //魔术方法
    public function __call($name, $arguments) {
        $this->smarty->assign("errorTip", "Calling object method '$name' ". implode(', ', $arguments). "\n");
        $this->Viewhtm = "under.htm";
    }
    //魔术函数
    function __destruct()
    {
        $site_id = Input('get.site_id',0);

        if ($this->c =='Wait') {
            $this->smarty->assign("u", $this->router->getController());
            $this->smarty->assign("t", $this->router->getUrl());
            $this->smarty->assign("Viewhtm", $this->Viewhtm);
            $websites = $this->Show_css->getOne("cms_websites","site_id='{$site_id}'");
            $this->smarty->assign("websites",$websites);
            if($site_id){
                if($this->UserLogin['user_type'] == '1' and $this->UserLogin['user_limitsinc'] == '1'){
                    $moduleList = $this->Show_css->select("SELECT m.* FROM cms_module as m LEFT JOIN cms_moduleapply as a ON a.module_id = m.module_id WHERE a.site_id = '{$site_id}' ORDER BY m.module_weight ASC ");
                }else{
                    $moduleList = $this->Show_css->select("SELECT m.* FROM cms_module as m LEFT JOIN cms_moduleapply as a ON a.module_id = m.module_id LEFT JOIN cms_websites_func as c ON c.site_id = a.site_id and c.module_id = a.module_id WHERE a.site_id = '{$site_id}' and c.user_id = '{$this->UserLogin['user_id']}' ORDER BY m.module_weight ASC ");
                }
                // print_r($moduleList);die;
                $this->smarty->assign("moduleList", $moduleList);
                $this->display("websiteindex.htm");
            }else{
                $this->display("index.htm");
            }
        }
        exit;
    }

}