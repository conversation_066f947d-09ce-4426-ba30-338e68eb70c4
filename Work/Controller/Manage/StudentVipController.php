<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn 
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2016/12/16
 * Time: 11:51
 */

namespace Work\Controller\Manage;


class StudentVipController  extends viewTpl{
    public $data;
    public $u;
    public $t;
    public $c;
    public $iuser;
    public $Viewhtm;

    //预加载处理类
    function __construct() {
        parent::__construct ();
        if(!$this->check_login()){
            $this->LoginView();
        }
        if($this->check_login()){
            $this->smarty->assign("iuser", $this->UserLogin);
            $this->iuser = $this->UserLogin;
        }
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
        $this->Viewhtm = $this->router->getController()."/".$this->router->getUrl().".htm";


        $moduleOne = $this->Show_css->getOne("cms_module","module_mark='{$this->u}'");
        $this->smarty->assign("moduleOne",$moduleOne);

    }

    //主页
    function HomeView()
    {
        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        if(!isset($request['p'])){
            $p = '1';
        }else{
            $p= $request['p'];
        }

        $datawhere = " 1 ";
        $pageurl = "/{$this->u}/{$this->t}?";
        if ($request['site_id']) {
            $pageurl .= "&site_id={$request['site_id']}";
        }

        $datatype = array();
        if(isset($request['keyword']) && $request['keyword'] !==''){
            $datawhere .= " and (s.student_branch like '%{$request['keyword']}%' or s.student_cnname like '%{$request['keyword']}%')";
            $pageurl .="&keyword={$request['keyword']}";
            $datatype['keyword'] = $request['keyword'];
        }
        if(isset($request['student_isnew']) && $request['student_isnew'] !==''){
            $datawhere .= " and s.student_isnew = '{$request['student_isnew']}'";
            $pageurl .="&student_isnew={$request['student_isnew']}";
            $datatype['student_isnew'] = $request['student_isnew'];
        }


        if($request['islogin'] == '1'){
            $datawhere .= " and s.student_loginnum > '0'";
            $pageurl .="&islogin={$request['islogin']}";
            $datatype['islogin'] = $request['islogin'];
        }


        $sql = "
            SELECT
                s.student_branch,
                s.student_id,
                s.student_cnname,
                s.student_isnew,
                s.student_loginnum,
                FROM_UNIXTIME( s.student_logintime, '%Y-%m-%d %H:%i:%s' ) AS student_logintime,
                FROM_UNIXTIME( s.student_lastlogintime, '%Y-%m-%d %H:%i:%s' ) AS student_lastlogintime,
                ca.coursecat_cnname
            FROM
                smc_student AS s
                LEFT JOIN smc_student_registerinfo AS r ON s.student_id = r.student_id 
                AND r.info_isintegral = '1' 
                AND r.info_status = '1' 
                left join smc_code_coursecat as ca on ca.coursecat_id = r.coursecat_id
            WHERE
                {$datawhere} and s.company_id = '8888' 
                GROUP BY s.student_id
                
            ORDER BY
                s.student_logintime DESC";

        $db_nums = $Show_css->selectOne("SELECT COUNT(DISTINCT( s.student_id) ) AS num  FROM smc_student AS s
                LEFT JOIN smc_student_registerinfo AS r ON s.student_id = r.student_id 
                AND r.info_isintegral = '1' 
                AND r.info_status = '1' 
                left join smc_code_coursecat as ca on ca.coursecat_id = r.coursecat_id
            WHERE
                {$datawhere} and s.company_id = '8888'");//相关条件下的总记录数COUNT(*)

        $allnum = $db_nums['num'];

        $datalist = $Show_css->dbwherePage($sql,$allnum,'10',$pageurl.'&p=',$p,'10','2');

        $smarty->assign("pagelist",$datalist['pages']);//筛选信息

        $smarty->assign("dataList",$datalist['cont']);
        $smarty->assign("datatype",$datatype);
    }

    //導出Excel
    function ReportExcelAction(){
        $request = Input('get.');
        $datawhere = " 1 ";
        if(isset($request['keyword']) && $request['keyword'] !==''){
            $datawhere .= " and (s.student_branch like '%{$request['keyword']}%' or s.student_cnname like '%{$request['keyword']}%')";
        }
        if(isset($request['student_isnew']) && $request['student_isnew'] !==''){
            $datawhere .= " and s.student_isnew = '{$request['student_isnew']}'";
        }

        if($request['islogin'] == '1'){
            $datawhere .= " and s.student_loginnum > '0'";
        }

        $sql = "SELECT
                s.student_branch,
                s.student_id,
                s.student_cnname,
                s.student_isnew,
                s.student_loginnum,
                FROM_UNIXTIME( s.student_logintime, '%Y-%m-%d %H:%i:%s' ) AS student_logintime,
                FROM_UNIXTIME( s.student_lastlogintime, '%Y-%m-%d %H:%i:%s' ) AS student_lastlogintime,
                ca.coursecat_cnname
            FROM
                smc_student AS s
                LEFT JOIN smc_student_registerinfo AS r ON s.student_id = r.student_id 
                AND r.info_isintegral = '1' 
                AND r.info_status = '1' 
                left join smc_code_coursecat as ca on ca.coursecat_id = r.coursecat_id
            WHERE
                {$datawhere} and s.company_id = '8888' 
                GROUP BY s.student_id
            ORDER BY
                s.student_logintime DESC";
        $dataList=$this->Show_css->selectClear($sql);
        if($dataList){
            $outexcel=array();
            foreach($dataList as $dataVar){
                $dataarray=array();
                $dataarray['student_cnname']=$dataVar['student_cnname'];
                $dataarray['student_branch']=$dataVar['student_branch'];
                if($dataVar['student_isnew'] == '1'){
                    $dataarray['student_isnew'] = '是';
                }elseif($dataVar['student_isnew'] == '0'){
                    $dataarray['student_isnew'] = '否';
                }
                $dataarray['coursecat_cnname']=$dataVar['coursecat_cnname'];
                if($dataVar['student_logintime'] == '1970-01-01 08:00:00'){
                    $dataarray['student_logintime'] = '--';
                }else{
                    $dataarray['student_logintime'] = $dataVar['student_logintime'];
                }
                if($dataVar['student_lastlogintime'] == '1970-01-01 08:00:00'){
                    $dataarray['student_lastlogintime'] = '--';
                }else{
                    $dataarray['student_lastlogintime'] = $dataVar['student_lastlogintime'];
                }
                $dataarray['student_loginnum']=$dataVar['student_loginnum'];
                $outexcel[] = $dataarray;
            }
        }
        $excelheader = array("学生名称","学生编号","是否成为积分商城会员","拓课班种","成为会员后的首次登录时间","上次登录时间","登录总次数");
        $excelfields = array("student_cnname","student_branch","student_isnew","coursecat_cnname","student_logintime","student_lastlogintime","student_loginnum");

        query_to_excel($excelheader,$outexcel,$excelfields,"兑吧学生信息.xlsx");
        ajax_return(array("error"=>0,"errortip"=>"导出完毕!","bakfuntion"=>"okmotify"));
    }


    //魔术方法
    public function __call($name, $arguments) {
        $this->smarty->assign("errorTip", "Calling object method '$name' ". implode(', ', $arguments). "\n");
        $this->Viewhtm = "under.htm";
    }
    //魔术函数
    function __destruct()
    {
        $site_id = Input('get.site_id',0);

        if ($this->c =='Wait') {
            $this->smarty->assign("u", $this->router->getController());
            $this->smarty->assign("t", $this->router->getUrl());
            $this->smarty->assign("Viewhtm", $this->Viewhtm);
            $websites = $this->Show_css->getOne("cms_websites","site_id='{$site_id}'");
            $this->smarty->assign("websites",$websites);
            if($site_id){
                if($this->UserLogin['user_type'] == '1' and $this->UserLogin['user_limitsinc'] == '1'){
                    $moduleList = $this->Show_css->select("SELECT m.* FROM cms_module as m LEFT JOIN cms_moduleapply as a ON a.module_id = m.module_id WHERE a.site_id = '{$site_id}' ORDER BY m.module_weight ASC ");
                }else{
                    $moduleList = $this->Show_css->select("SELECT m.* FROM cms_module as m LEFT JOIN cms_moduleapply as a ON a.module_id = m.module_id LEFT JOIN cms_websites_func as c ON c.site_id = a.site_id and c.module_id = a.module_id WHERE a.site_id = '{$site_id}' and c.user_id = '{$this->UserLogin['user_id']}' ORDER BY m.module_weight ASC ");
                }
                // print_r($moduleList);die;
                $this->smarty->assign("moduleList", $moduleList);
                $this->display("websiteindex.htm");
            }else{
                $this->display("index.htm");
            }
        }
        exit;
    }

}