<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2016/12/16
 * Time: 11:51
 */

namespace Work\Controller\Manage;


class SchoolListController  extends viewTpl{
    public $data;
    public $u;
    public $t;
    public $c;
    public $iuser;
    public $Viewhtm;

    //预加载处理类
    function __construct() {
        parent::__construct ();
        if(!$this->check_login()){
            $this->LoginView();
        }
        if($this->check_login()){
            $this->smarty->assign("iuser", $this->UserLogin);
            $this->iuser = $this->UserLogin;
        }
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
        $this->Viewhtm = $this->router->getController()."/".$this->router->getUrl().".htm";


        $moduleOne = $this->Show_css->getOne("cms_module","module_mark='{$this->u}'");
        $this->smarty->assign("moduleOne",$moduleOne);

    }

    //分校管理
    function HomeView(){
        $request = Input('get.','','trim,addslashes');

        $datatype = array();
        $datatype['company_id'] = $request['company_id'];
        $datatype['from'] = $request['from'];

        $datawhere = "1";
        $pageurl = "/{$this->u}/{$this->t}?site_id={$request['site_id']}&company_id={$request['company_id']}&from={$request['from']}";
        if(isset($request['keyword']) && $request['keyword'] !==''){
            $datawhere .= " and (s.school_cnname like '%{$request['keyword']}%' or s.school_branch like '%{$request['keyword']}%' or s.school_phone like '%{$request['keyword']}%' )";
            $pageurl .="&keyword={$request['keyword']}";
            $datatype['keyword'] = $request['keyword'];
        }

        if(!isset($request['p'])){
            $p = '1';
        }else{
            $p= $request['p'];
        }

        $Show_css = $this->Show_css;
        $sql = "select s.*,
                (select co.company_cnname from  gmc_company as co where co.company_id = s.company_id ) as company_cnname,
                (select r.region_name from smc_code_region as r where r.region_id =s.school_province) as province_name,
                (select r.region_name from smc_code_region as r where r.region_id =s.school_city) as city_name,
                (select r.region_name from smc_code_region as r where r.region_id =s.school_area) as area_name
                from smc_school as s
                where s.company_id='{$request['company_id']}' and {$datawhere}";


        $db_nums = $Show_css->selectOne("SELECT COUNT(s.school_id) as countnums FROM smc_school as s where s.company_id='{$request['company_id']}' and {$datawhere}");//相关条件下的总记录数COUNT(*)
        $allnum = $db_nums['countnums'];

        $schoolList = $Show_css->dbwherePage($sql,$allnum,'10',$pageurl.'&p=',$p,'10','2');

        if($schoolList['cont']){
            foreach($schoolList['cont'] as $key => $value){
                if($value['school_type'] ==1){
                    $schoolList['cont'][$key]['school_typename'] ="直营校";
                }elseif($value['school_type'] ==2){
                    $schoolList['cont'][$key]['school_typename'] ="直营园";
                }elseif($value['school_type'] ==3){
                    $schoolList['cont'][$key]['school_typename'] ="加盟校";
                }elseif($value['school_type'] ==4){
                    $schoolList['cont'][$key]['school_typename'] ="加盟园";
                }else{
                    $schoolList['cont'][$key]['school_typename'] ="测试";
                }
                $schoolList['cont'][$key]['school_cityaddress'] =$value['province_name'].''.$value['city_name'].''.$value['area_name'];
            }
        }

        $schoolList = $schoolList==false?array():$schoolList;

        $this->smarty->assign("dataList",$schoolList['cont']);
        $this->smarty->assign("pagelist",$schoolList['pages']);//筛选信息
        $this->smarty->assign("datatype",$datatype);//筛选信息
    }

    //增加
    function AddView()
    {
        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $datatype = array();
        $district = array();
        $companies = array();
        $this->smarty->assign("act","Add");
        if(isset($request['company_id']) && $request['company_id'] !==""){
            $datatype['company_id'] = $request['company_id'];
            $companyOne =$Show_css->getFieldOne('gmc_company','company_cnname',"company_id='{$request['company_id']}'");
            $datatype['company_cnname'] = $companyOne['company_cnname'];

            $district = $this->Show_css->selectClear("select district_id,district_cnname from gmc_company_district where company_id = '{$request['company_id']}'");

            $companies  = $this->Show_css->selectClear("select companies_id,companies_cnname from gmc_code_companies  where  company_id ='{$request['company_id']}'");

        }


        $this->smarty->assign("datatype",$datatype);
        $this->smarty->assign("companies",$companies);
        $this->smarty->assign("district",$district);
        $this->Viewhtm = $this->router->getController()."/"."Edit.htm";
    }


    //提交处理
    function AddAction()
    {
        $request = Input('post.','','trim,addslashes');

        if(isset($request['company_id']) && $request['company_id'] =='' ){
            $res = array('error' => '1', 'errortip' => '请选择集团!',"bakfuntion"=>"errormotify");
            ajax_return($res);
        }
        if(isset($request['school_cnname']) && $request['school_cnname'] =='' ){
            $res = array('error' => '1', 'errortip' => '请填写校区名称!',"bakfuntion"=>"errormotify");
            ajax_return($res);
        }

        $like=substr(date("Ymd",time()) , 2 , 6);
        $ymd =date('ymd');
        $data = array();
        $stuInfo=$this->Show_css->selectOne("select school_branch from smc_school where school_branch like '{$ymd}%' order by school_branch DESC limit 0,1");
        if($stuInfo){
            $data['school_branch']=$stuInfo['school_branch']+1;
        }else{
            $data['school_branch'] =$like.'001';
        }

        $data['company_id'] =$request['company_id'];
        $data['school_type'] = $request['school_type'];
        $data['companies_id'] = $request['companies_id'];
        $data['school_cmbshopcode'] = $request['school_cmbshopcode'];
        $data['school_upcshopcode'] = $request['school_upcshopcode'];
        $data['school_erpshopcode'] = $request['school_erpshopcode'];
        $data['school_cnname'] = $request['school_cnname'];
        $data['school_enname'] = $request['school_enname'];
        $data['district_id'] = $request['district_id'];
        $data['company_id'] = $request['company_id'];
        $data['school_province'] = $request['school_province'];
        $data['school_city'] = $request['school_city'];
        $data['school_area'] = $request['school_area'];
        $data['school_istest'] = $request['school_istest'];
        $data['school_isclose'] = $request['school_isclose'];
        $data['school_address'] = $request['school_address'];
        $data['school_phone'] = $request['school_phone'];
        $data['school_synchro_day'] = $request['school_synchro_day'];
        $data['school_createtime'] = time();
        if($this->Show_css->insertData("smc_school",$data)){
            ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"successFromTip","bakurl"=>"/{$this->u}?site_id={$request['site_id']}&company_id={$request['company_id']}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "新增失败!","bakfuntion"=>"dangerFromTip"));
        }
    }
    //修改
    function EditView()
    {
        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;
        $datatype = array();
        $companies = array();
        $district = array();
        $sql = "select s.*,
        (select co.company_cnname  from gmc_company as co  where co.company_id = s.company_id) as company_cnname,
        (select r.region_name from smc_code_region as r where r.region_id =s.school_province) as province_name,
        (select r.region_name from smc_code_region as r where r.region_id =s.school_city) as city_name,
        (select r.region_name from smc_code_region as r where r.region_id =s.school_area) as area_name
        from smc_school as s where s.school_id='{$request['school_id']}'  limit 0,1";

        $schoolOne = $Show_css->selectOne($sql);


        if($schoolOne){
            $datatype['company_id'] = $schoolOne['company_id'];
            $datatype['company_cnname'] = $schoolOne['company_cnname'];
            $district = $this->Show_css->selectClear("select district_id,district_cnname from gmc_company_district where company_id = '{$schoolOne['company_id']}'");

            $companies  = $this->Show_css->selectClear("select companies_id,companies_cnname from gmc_code_companies  where  company_id ='{$schoolOne['company_id']}'");

        }


        $smarty->assign("act", "Edit");
        $smarty->assign("district", $district);
        $smarty->assign("companies", $companies);
        $smarty->assign("datatype",$datatype);
        $smarty->assign("dataVar",$schoolOne);
        $smarty->assign("schoolOne",$schoolOne);

        $this->Viewhtm = $this->router->getController()."/"."Edit.htm";
    }
    //修改处理
    function EditAction()
    {
        $request = Input('post.','','trim,addslashes');
        if($request['school_id'] == '' ){
            ajax_return(array('error' => 1,'errortip' => "请选择学校","bakfuntion"=>"warningFromTip"));
        }

        $data = array();

        $data['school_type'] = $request['school_type'];
        $data['companies_id'] = $request['companies_id'];
        $data['school_cmbshopcode'] = $request['school_cmbshopcode'];
        $data['school_upcshopcode'] = $request['school_upcshopcode'];
        $data['school_erpshopcode'] = $request['school_erpshopcode'];
        $data['school_shortname'] = $request['school_shortname'];
        $data['school_cnname_initial'] = $request['school_cnname_initial'];
        $data['school_cnname'] = $request['school_cnname'];
        $data['school_enname'] = $request['school_enname'];
        $data['district_id'] = $request['district_id'];
        $data['company_id'] = $request['company_id'];
        $data['school_province'] = $request['school_province'];
        $data['school_city'] = $request['school_city'];
        $data['school_area'] = $request['school_area'];
        $data['school_istest'] = $request['school_istest'];
        $data['school_isclose'] = $request['school_isclose'];
        $data['school_address'] = $request['school_address'];
        $data['school_phone'] = $request['school_phone'];
        $data['school_synchro_day'] = $request['school_synchro_day'];
        $data['school_createtime'] = time();


        if($this->Show_css->updateData("smc_school","school_id = '{$request['school_id']}'",$data)){
            ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"okmotify","bakurl"=>"/{$this->u}?site_id={$request['site_id']}&company_id={$request['company_id']}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "新增失败!","bakfuntion"=>"errormotify"));
        }
    }

    //魔术方法
    public function __call($name, $arguments) {
        $this->smarty->assign("errorTip", "Calling object method '$name' ". implode(', ', $arguments). "\n");
        $this->Viewhtm = "under.htm";
    }
    //魔术函数
    function __destruct()
    {
        $site_id = Input('get.site_id',0);

        if ($this->c =='Wait') {
            $this->smarty->assign("u", $this->router->getController());
            $this->smarty->assign("t", $this->router->getUrl());
            $this->smarty->assign("Viewhtm", $this->Viewhtm);
            $websites = $this->Show_css->getOne("cms_websites","site_id='{$site_id}'");
            $this->smarty->assign("websites",$websites);
            if($site_id){
                if($this->UserLogin['user_type'] == '1' and $this->UserLogin['user_limitsinc'] == '1'){
                    $moduleList = $this->Show_css->select("SELECT m.* FROM cms_module as m LEFT JOIN cms_moduleapply as a ON a.module_id = m.module_id WHERE a.site_id = '{$site_id}' ORDER BY m.module_weight ASC ");
                }else{
                    $moduleList = $this->Show_css->select("SELECT m.* FROM cms_module as m LEFT JOIN cms_moduleapply as a ON a.module_id = m.module_id LEFT JOIN cms_websites_func as c ON c.site_id = a.site_id and c.module_id = a.module_id WHERE a.site_id = '{$site_id}' and c.user_id = '{$this->UserLogin['user_id']}' ORDER BY m.module_weight ASC ");
                }
                // print_r($moduleList);die;
                $this->smarty->assign("moduleList", $moduleList);
                $this->display("websiteindex.htm");
            }else{
                $this->display("index.htm");
            }
        }
        exit;
    }

}