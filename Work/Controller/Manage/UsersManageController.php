<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn 
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2016/12/19
 * Time: 14:21
 */

namespace Work\Controller\Manage;


class UsersManageController extends viewTpl{
    public $data;
    public $iuser;
    public $u;
    public $t;
    public $c;
    public $Viewhtm;

    function __construct() {
        parent::__construct ();
        if(!$this->check_login()){
            $this->LoginView();
        }
        $this->smarty->assign("iuser", $this->UserLogin);
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
        $this->Viewhtm = $this->router->getController()."/".$this->router->getUrl().".htm";

    }
    //主页
    function HomeView()
    {

        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        if(!isset($request['p'])){
            $p = '1';
        }else{
            $p= $request['p'];
        }

        $datawhere = "1";
        $pageurl = "/{$this->u}/{$this->t}?";
        if ($request['site_id']) {
            $pageurl .= "&site_id={$request['site_id']}";
        }

        $datatype = array();
        if(isset($request['keyword']) && $request['keyword'] !==''){
            $datawhere .= " and u.user_name like '%{$request['keyword']}%' ";
            $pageurl .="&keyword={$request['keyword']}";
            $datatype['keyword'] = $request['keyword'];
        }

        if(isset($request['type']) && $request['type'] != ''){
            $datawhere .= " and u.user_type = '{$request['type']}'";
            $pageurl .="&type={$request['type']}";
            $datatype['type'] = $request['type'];
        }


        $sql = "SELECT u.* FROM cms_users as u where {$datawhere} order by u.user_id ASC ";

        $db_nums = $Show_css->select("SELECT COUNT(*) FROM cms_users as u where {$datawhere}");//相关条件下的总记录数COUNT(*)
        $allnum = $db_nums[0][0];

        $datalist = $Show_css->dbwherePage($sql,$allnum,'10',$pageurl.'&p=',$p,'10','2');

        $smarty->assign("pagelist",$datalist['pages']);
        $smarty->assign("dataList",$datalist['cont']);
        $smarty->assign("datatype",$datatype);
    }
    //权限控制
    function LimitsView()
    {

        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        if(!isset($request['p'])){
            $p = '1';
        }else{
            $p= $request['p'];
        }

        $datawhere = "w.site_type <> '1'";
        $pageurl = "/{$this->u}/{$this->t}?";

        $datatype = array();
        if(isset($request['keyword']) && $request['keyword'] !==''){
            $datawhere .= " and w.site_title like '%{$request['keyword']}%' ";
            $pageurl .="&keyword={$request['keyword']}";
            $datatype['keyword'] = $request['keyword'];
        }


        $sql = "SELECT w.*,(SELECT COUNT(m.manage_id) FROM cms_websites_manage AS m WHERE m.user_id = '{$request['user_id']}' and m.site_id = w.site_id) AS thestatus FROM cms_websites as w where {$datawhere} order by w.site_weight ASC ";

        $db_nums = $Show_css->select("SELECT COUNT(*) FROM cms_websites as w where {$datawhere}");//相关条件下的总记录数COUNT(*)
        $allnum = $db_nums[0][0];

        $datalist = $Show_css->dbwherePage($sql,$allnum,'10',$pageurl.'&p=',$p,'10','2');

        $smarty->assign("pagelist",$datalist['pages']);
        $smarty->assign("dataList",$datalist['cont']);
        $smarty->assign("user_id",$request['user_id']);
        $smarty->assign("datatype",$datatype);
    }
    //功能权限配置
    function ModuleView()
    {
        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        if(!isset($request['p'])){
            $p = '1';
        }else{
            $p= $request['p'];
        }

        $datawhere = "a.site_id = '{$request['site_id']}'";
        $pageurl = "/{$this->u}/{$this->t}?user_id={$request['user_id']}";

        $datatype = array();
        if(isset($request['keyword']) && $request['keyword'] !==''){
            $datawhere .= " and m.module_name like '%{$request['keyword']}%' ";
            $pageurl .="&keyword={$request['keyword']}";
            $datatype['keyword'] = $request['keyword'];
        }

        if(isset($request['site_id']) && $request['site_id'] !==''){
            $pageurl .="&site_id={$request['site_id']}";
            $datatype['site_id'] = $request['site_id'];
        }

        $sql = "SELECT m.module_id,m.module_name,a.apply_weight,(SELECT COUNT(c.func_id) FROM cms_websites_func AS c WHERE c.user_id = '{$request['user_id']}' and c.site_id = a.site_id and c.module_id = a.module_id) AS thestatus FROM cms_module as m LEFT JOIN cms_moduleapply as a ON a.module_id = m.module_id where {$datawhere} order by a.apply_weight ASC ";

        $db_nums = $Show_css->select("SELECT COUNT(*)  FROM cms_module as m LEFT JOIN cms_moduleapply as a ON a.module_id = m.module_id where {$datawhere}");//相关条件下的总记录数COUNT(*)
        $allnum = $db_nums[0][0];

        $datalist = $Show_css->dbwherePage($sql,$allnum,'10',$pageurl.'&p=',$p,'10','2');

        $smarty->assign("pagelist",$datalist['pages']);
        $smarty->assign("dataList",$datalist['cont']);
        $smarty->assign("user_id",$request['user_id']);
        $smarty->assign("site_id",$request['site_id']);
        $smarty->assign("datatype",$datatype);
    }

    //增加系统功能
    function AddView()
    {
        $this->smarty->assign("act","Add");
        $this->Viewhtm = $this->router->getController()."/"."Manage.htm";
    }
    //提交处理机制
    function AddAction()
    {
        $request = Input('post.','','trim,addslashes');
        $data = array();

        if($request['user_name'] == '' || $request['user_cnname'] == '' || $request['user_pass'] == ''){
            ajax_return(array('error' => 1,'errortip' => "表单相关内容未填写完整!","bakfuntion"=>"warningFromTip"));
        }

        $data['user_name'] = $request['user_name'];
        $data['user_type'] = $request['user_type'];
        $data['user_limitsinc'] = $request['user_limitsinc'];
        $data['user_pass'] = md5($request['user_pass']);
        $data['user_imghead'] = $request['user_imghead'];
        $data['user_cnname'] = $request['user_cnname'];
        $data['user_enname'] = $request['user_enname'];
        $data['user_email'] = $request['user_email'];
        $data['user_mobile'] = $request['user_mobile'];
        if($this->Show_css->insertData("cms_users",$data)){
            ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"successFromTip","bakurl"=>"/{$this->u}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "新增失败!","bakfuntion"=>"dangerFromTip"));
        }
    }
    //增加系统功能
    function EditView()
    {

        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        $usersOne = $Show_css->getOne("cms_users", "user_id='{$request['id']}'");
        $smarty->assign("dataVar", $usersOne);

        $smarty->assign("act", "Edit");

        $this->Viewhtm = $this->router->getController()."/"."Manage.htm";
    }
    //提交处理机制
    function EditAction()
    {
        $request = Input('post.','','trim,addslashes');

        $data = array();
        $data['user_name'] = $request['user_name'];
        $data['user_type'] = $request['user_type'];
        $data['user_limitsinc'] = $request['user_limitsinc'];
        if($request['user_pass'] !==''){
            $data['user_pass'] = md5($request['user_pass']);
        }
        $data['user_imghead'] = $request['user_imghead'];
        $data['user_cnname'] = $request['user_cnname'];
        $data['user_enname'] = $request['user_enname'];
        $data['user_email'] = $request['user_email'];
        $data['user_mobile'] = $request['user_mobile'];
        if($this->Show_css->updateData("cms_users","user_id = '{$request['user_id']}'",$data)){
            ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"okmotify","bakurl"=>"/{$this->u}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "新增失败!","bakfuntion"=>"errormotify"));
        }
    }
    //提交处理机制
    function DelAction()
    {
        $user_id = Input('get.id',0);

        if($this->Show_css->delData('cms_users',"user_id='{$user_id}'")){
            ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"okmotify","bakurl"=>"/{$this->u}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "新增失败!","bakfuntion"=>"errormotify"));
        }
    }
    //开发管理权限
    function ManageAction()
    {
        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $data = array();
        $data['user_id']    = $request['user_id'];
        $data['site_id']      = $request['site_id'];

        if($Show_css->insertData("cms_websites_manage",$data)){
            jsbakerror_spl("提交成功");
        }else{
            jsbakerror_spl("提交失败");
        }
    }
    //管理权限 (删除)  设置
    function DelManageAction()
    {
        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;

        $data = array();
        $data['user_id']    = $request['user_id'];
        $data['site_id']      = $request['site_id'];

        if($Show_css->delData('cms_websites_manage',"user_id='{$request['user_id']}' and site_id='{$request['site_id']}'")){
            jsbakerror_spl("取消成功");
        }else{
            jsbakerror_spl("取消失败");
        }
    }
    //开发管理权限
    function FuncAction()
    {
        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $data = array();
        $data['user_id']    = $request['user_id'];
        $data['site_id']      = $request['site_id'];
        $data['module_id']      = $request['module_id'];
        if($Show_css->insertData("cms_websites_func",$data)){
            jsbakerror_spl("提交成功");
        }else{
            jsbakerror_spl("提交失败");
        }
    }
    //管理权限 (删除)  设置
    function DelFuncAction()
    {
        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;

        $data = array();
        $data['user_id']    = $request['user_id'];
        $data['site_id']      = $request['site_id'];
        $data['module_id']      = $request['module_id'];
        if($Show_css->delData('cms_websites_func',"user_id='{$data['user_id']}' and site_id='{$data['site_id']}' and module_id='{$data['module_id']}'")){
            jsbakerror_spl("取消成功");
        }else{
            jsbakerror_spl("取消失败");
        }
    }
    //魔术方法
    public function __call($name, $arguments) {
        $this->smarty->assign("errorTip", "Calling object method '$name' ". implode(', ', $arguments). "\n");

        $this->Viewhtm = "under.htm";
    }
    //魔术方法
    function __destruct()
    {
        $site_id = Input('get.site_id',0);

        if ($this->c =='Wait') {
            $this->smarty->assign("u", $this->router->getController());
            $this->smarty->assign("t", $this->router->getUrl());
            $this->smarty->assign("Viewhtm", $this->Viewhtm);
            if($site_id){
                $websites = $this->Show_css->getOne("cms_websites","site_id='{$site_id}'");
                $this->smarty->assign("websites",$websites);
                $this->display("websiteindex.htm");
            }else{
                $this->display("index.htm");
            }
        }
        exit;
    }

}