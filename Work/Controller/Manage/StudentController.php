<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2016/12/16
 * Time: 11:51
 */

namespace Work\Controller\Manage;


class StudentController extends viewTpl
{
    public $data;
    public $u;
    public $t;
    public $c;
    public $iuser;
    public $Viewhtm;

    //预加载处理类
    function __construct()
    {
        parent::__construct();
        if (!$this->check_login()) {
            $this->LoginView();
        }
        if ($this->check_login()) {
            $this->smarty->assign("iuser", $this->UserLogin);
            $this->iuser = $this->UserLogin;
        }
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
        $this->Viewhtm = $this->router->getController() . "/" . $this->router->getUrl() . ".htm";


        $moduleOne = $this->Show_css->getOne("cms_module", "module_mark='{$this->u}'");
        $this->smarty->assign("moduleOne", $moduleOne);

    }

    //学员管理
    function HomeView()
    {
        $request = Input('get.','','trim,addslashes');

        $datatype = array();
        $datatype['company_id'] = $request['company_id'];
        $datatype['school_id'] = $request['school_id'];
        $datatype['from'] = $request['from'];
        $datawhere = "1";
        $pageurl = "/{$this->u}/{$this->t}?site_id={$request['site_id']}&school_id={$request['school_id']}";
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (s.student_cnname like '%{$request['keyword']}%' or s.student_branch like '%{$request['keyword']}%' or s.student_enname like '%{$request['keyword']}%' )";
            $pageurl .= "&keyword={$request['keyword']}";
            $datatype['keyword'] = $request['keyword'];
        }

        if (!isset($request['p'])) {
            $p = '1';
        } else {
            $p = $request['p'];
        }

        $Show_css = $this->Show_css;

        $sql = " SELECT  s.student_id,s.student_branch,s.student_cnname,s.student_enname,s.student_sex,s.student_birthday,sf.family_mobile,sf.family_cnname,s.student_idcard,student_createtime
                FROM smc_student_enrolled as se
                left join smc_student as s on se.student_id=s.student_id
                left join smc_student_family as sf on sf.student_id=s.student_id and sf.family_isdefault=1
                WHERE {$datawhere} and se.school_id='{$request['school_id']}' ";


        $db_nums = $Show_css->selectOne("SELECT COUNT(s.student_id) as countnums FROM smc_student_enrolled as se
                left join smc_student as s on se.student_id=s.student_id
                left join smc_student_family as sf on sf.student_id=s.student_id and sf.family_isdefault=1
                WHERE {$datawhere} and se.school_id='{$request['school_id']}'");
        $allnum = $db_nums['countnums'];

        $studentList = $Show_css->dbwherePage($sql, $allnum, '10', $pageurl . '&p=', $p, '10', '2');
//
        $studentList = $studentList == false ? array() : $studentList;

        $this->smarty->assign("dataList", $studentList['cont']);
        $this->smarty->assign("pagelist", $studentList['pages']);//筛选信息
        $this->smarty->assign("datatype", $datatype);//筛选信息
    }

    //增加
    function AddView()
    {
        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $datatype = array();
        $this->smarty->assign("act", "Add");
        if (isset($request['school_id']) && $request['school_id'] !== "") {
            $datatype['school_id'] = $request['school_id'];

            $schoolOne = $Show_css->getFieldOne('smc_school', 'school_id,school_cnname,company_id', "school_id='{$request['school_id']}'");
            $datatype['company_id'] = $schoolOne['company_id'];
            $datatype['school_cnname'] = $schoolOne['school_cnname'];
            $this->smarty->assign("schoolOne", $schoolOne);
        }


        $relation  = $this->Show_css->selectClear("select *  from smc_code_familyrelation  where 1 ");

        $this->smarty->assign("datatype", $datatype);
        $this->smarty->assign("relation", $relation);

        $this->Viewhtm = $this->router->getController() . "/" . "Edit.htm";
    }


    //提交处理
    function AddAction()
    {
        $request = Input('post.','','trim,addslashes');

        if (isset($request['student_cnname']) && $request['student_cnname'] == '') {
            $res = array('error' => '1', 'errortip' => '请填写学员姓名!', "bakfuntion" => "errormotify");
            ajax_return($res);
        }

        $like = date("Ymd", time());
        $stuInfo = $this->Show_css->selectOne("select student_branch from smc_student where student_branch like '{$like}%' AND LENGTH(student_branch) = '14' order by student_branch DESC limit 0,1");
        $schoolOne = $this->Show_css->getFieldOne('smc_school', "company_id", "school_id='{$request['school_id']}'");
        if ($stuInfo) {
            $student_data['student_branch'] = $stuInfo['student_branch'] + 1;
        } else {
            $student_data['student_branch'] = $like . '000001';
        }

        $student_data['student_cnname'] = addslashes(trim($request['student_cnname']));
        $student_data['student_enname'] = addslashes(trim($request['student_enname']));
        $student_data['student_sex'] = $request['student_sex'];
        $student_data['student_idcard'] = trim($request['student_idcard']);
        $student_data['company_id'] = $schoolOne['company_id'];
        $student_data['student_birthday'] = $request['student_birthday'];
        $student_data['student_createtime'] = time();
        $student_data['student_updatatime'] = time();
        if ($student_id = $this->Show_css->insertData("smc_student", $student_data)) {
            if(isset($request['family_mobile']) && $request['family_mobile'] !=="" ){

                $parentOne=$this->Show_css->getFieldOne("smc_parenter","parenter_id","parenter_mobile='{$request['family_mobile']}'");
                if(!$parentOne){
                    $data=array();
                    $data['parenter_mobile']=trim($request['family_mobile']);
                    $data['parenter_cnname']=addslashes(trim($request['family_cnname']));
                    $data['parenter_pass'] = md5(substr($request['family_mobile'],-6));
                    $data['parenter_bakpass'] = substr($request['family_mobile'],-6);
                    $data['parenter_addtime']=time();
                    $parentid = $this->Show_css->insertData("smc_parenter",$data);
                }

                $family_data = array();
                $family_data['family_mobile'] =trim($request['family_mobile']);
                $family_data['family_cnname'] =addslashes(trim($request['family_cnname']));
                $family_data['family_relation'] =addslashes(trim($request['family_relation']));
                $family_data['student_id'] =$student_id;
                $family_data['family_isdefault'] =1;
                if($parentid) {
                    $family_data['parenter_id'] = $parentid;
                }else{
                    $family_data['parenter_id'] = $parentOne['parenter_id'];
                }
                $this->Show_css->insertData("smc_student_family",$family_data);
            }

            $en_data = array();
            $en_data['school_id'] = $request['school_id'];
            $en_data['student_id'] = $student_id;
            $en_data['enrolled_createtime'] = time();
            $en_data['enrolled_status'] = 0;
            if ($this->Show_css->insertData("smc_student_enrolled", $en_data)) {
                /*if (!$this->Show_css->getFieldOne("smc_student_balance", "student_id", "student_id = '{$student_id}' and school_id = '{$request['school_id']}'")) {
                    $balance = array();
                    $balance['company_id'] = $request['company_id'];
                    $balance['student_id'] = $student_id;
                    $balance['school_id'] = $request['school_id'];
                    $this->Show_css->insertData("smc_student_balance", $balance);
                }*/
                $this->entrySchool($student_id, $request['school_id'], $request['company_id']);
                $this->Recordweblog($request['site_id'], $this->Module['module_id'], $this->c, "新增学员");
            }

            ajax_return(array('error' => 0, 'errortip' => "提交成功!", "bakfuntion" => "successFromTip", "bakurl" => "/{$this->u}?site_id={$request['site_id']}&school_id={$request['school_id']}"));
        } else {
            ajax_return(array('error' => 1, 'errortip' => "新增失败!", "bakfuntion" => "dangerFromTip"));
        }
    }

    //修改
    function EditView()
    {
        $request = Input('get.','','trim,addslashes');
        $smarty = $this->smarty;
        $datatype = array();
        $datatype['school_id'] = $request['school_id'];
        $studentOne = $this->Show_css->selectOne("select s.* ,f.family_relation,f.family_id,f.family_cnname,f.family_mobile from  smc_student  as s  
        left  join  smc_student_family as f ON f.student_id = s.student_id  and  f.family_isdefault = 1
        where s.student_id='{$request['student_id']}'  limit 0,1");


        $schoolOne = $this->Show_css->getFieldOne('smc_school', 'school_id,school_cnname,company_id', "school_id='{$request['school_id']}'");
        $datatype['comapany_id'] = $schoolOne['comapany_id'];


        $datatype['familyrelation_name'] = $studentOne['family_relation'];
        $relation  = $this->Show_css->selectClear("select *  from smc_code_familyrelation  where 1 ");


        $smarty->assign("act", "Edit");
        $smarty->assign("datatype", $datatype);
        $smarty->assign("dataVar", $studentOne);
        $smarty->assign("schoolOne", $schoolOne);

        $smarty->assign("relation", $relation);

        $this->Viewhtm = $this->router->getController() . "/" . "Edit.htm";
    }

    //修改处理
    function EditAction()
    {
        $request = Input('post.','','trim,addslashes');
        if ($request['student_id'] == '') {
            ajax_return(array('error' => 1, 'errortip' => "请选择学员", "bakfuntion" => "warningFromTip"));
        }

        $student_data = array();
        $student_data['student_cnname'] = $request['student_cnname'];
        $student_data['student_enname'] = $request['student_enname'];
        $student_data['student_sex'] = $request['student_sex'];
        $student_data['student_idcard'] = $request['student_idcard'];
        $student_data['student_birthday'] = $request['student_birthday'];
        $student_data['student_updatatime'] = time();

        if(isset($request['family_mobile']) && $request['family_mobile'] !="" ){
            if(isset($request['family_id'])  && $request['family_id'] !=""){
                $family = $this->Show_css->selectOne("select family_id from  smc_student_family where  student_id = '{$request['student_id']}' and family_mobile='{$request['family_mobile']}' and family_id<>{$request['family_id']}  limit 0,1  ");
                if($family){
                    ajax_return(array('error' => 1, 'errortip' => "手机重复", "bakfuntion" => "dangerFromTip"));
                }else{
                    $family_data = array();
                    $family_data['family_mobile'] = $request['family_mobile'];
                    $family_data['family_cnname'] = $request['family_cnname'];
                    $family_data['family_relation'] = $request['family_relation'];
                    $this->Show_css->updateData("smc_student_family","family_id='{$request['family_id']}'",$family_data);
                }
            }else{

                $family_data = array();
                $family_data['family_mobile'] = $request['family_mobile'];
                $family_data['family_cnname'] = $request['family_cnname'];
                $family_data['family_relation'] = $request['family_relation'];
                $family_data['student_id'] =$request['student_id'];
                $family_data['family_isdefault'] =1;
                $this->Show_css->insertData("smc_student_family",$family_data);
            }
        }

        if ($this->Show_css->updateData("smc_student", "student_id = '{$request['student_id']}'", $student_data)) {
            ajax_return(array('error' => 0, 'errortip' => "提交成功!", "bakfuntion" => "successFromTip", "bakurl" => "/{$this->u}?site_id={$request['site_id']}&school_id={$request['school_id']}"));
        } else {
            ajax_return(array('error' => 1, 'errortip' => "新增失败!", "bakfuntion" => "errormotify"));
        }
    }


    function ImportView()
    {
        $request = Input('get.','','trim,addslashes');
        $smarty = $this->smarty;
        $datatype = array();

        $datatype['school_id'] = $request['school_id'];

        $schoolOne = $this->Show_css->selectOne("select school_id,company_id,school_cnname from smc_school where school_id='{$request['school_id']}'");

        $smarty->assign("schoolOne", $schoolOne);
        $smarty->assign("datatype", $datatype);
    }


    function ImportStudentExcelView()
    {

        $request = Input('post.','','trim,addslashes');
        if ($_FILES['uploadFile']['name'] == '' || $_FILES['uploadFile']['error'] == '1') {
            $PlayInfoVar = array();
            $PlayInfoVar['course_branch'] = "导入出错";
            $PlayInfoVar['error'] = "1";
            $PlayInfoVar['errortip'] = "文件不存在";
            $PlayInfo[] = $PlayInfoVar;
            $this->smarty->assign("PlayInfo", $PlayInfo);
            exit;
        }

        $fileType = array('xls', 'csv', 'xlsx');
        $uploadfile = new \Webfile($_FILES['uploadFile'], $files_dir = '../static/file', $size = 2097152 * 10, $fileType);
        $up_file = $uploadfile->upload();
//
        $PlayInfo = array();
        if ($up_file && $uploadfile->updatastatus) {
            $ExeclName = array();
            $ExeclName['学生中文名'] = "student_cnname";
            $ExeclName['学生英文名'] = "student_enname";
            $ExeclName['身份证号码'] = "student_idcard";
            $ExeclName['性别'] = "student_sex";
            $ExeclName['生日'] = "student_birthday";
            $ExeclName['入校时间'] = "student_createtime";
            $ExeclName["联系电话"] = "family_mobile";
            $ExeclName["联系人姓名"] = "family_cnname";
            $ExeclName["亲属关系"] = "family_relation";
            $WorkerList = execl_to_array($up_file, $ExeclName);
            unset($WorkerList[0]);

            if ($WorkerList) {
                foreach ($WorkerList as $WorkerrVar) {
                    if ($WorkerrVar['student_cnname'] !== '' && $WorkerrVar['student_cnname'] !== '') {
                        $workersList[] = $WorkerrVar;
                    } else {
                        $PlayInfoVar = array();
                        $PlayInfoVar['student_cnname'] = $WorkerrVar['student_cnname'];
                        $PlayInfoVar['bangding'] = '绑定失败';
                        $PlayInfoVar['error'] = "1";
                        $PlayInfoVar['errortip'] = "导入媒体信息不完整";
                        $PlayInfo[] = $PlayInfoVar;
                    }
                }
            }
            if (count($workersList) > 1000) {
                ajax_return(array('error' => 1, 'errortip' => "导入数量不能大于1000!", "bakfuntion" => "errormotify"));
            }
            if ($workersList) {
                foreach ($workersList as $workersVar) {
                    if (isset($workersVar['family_mobile']) && $workersVar['family_mobile'] != '') {
                        $data = array();
                        $stuOne = $this->Show_css->getFieldOne("smc_student", "student_id", "student_idcard='{$workersVar['student_idcard']}' and company_id='{$request['company_id']}' and  student_idcard <>''");

                        $like = date("Ymd", time());
                        $stuInfo = $this->Show_css->selectOne("select student_branch from smc_student where student_branch like '{$like}%' AND LENGTH(student_branch) = '14' order by student_branch DESC limit 0,1");
                        if ($stuInfo) {
                            $data['student_branch'] = $stuInfo['student_branch'] + 1;
                        } else {
                            $data['student_branch'] = $like . '000001';
                        }
                        if (!$stuOne) {
                            $PlayInfoVar['student_cnname'] = addslashes(trim($workersVar['student_cnname']));
                            $PlayInfoVar['student_enname'] = addslashes(trim($workersVar['student_enname']));
                            $PlayInfoVar['student_idcard'] = $workersVar['student_idcard'];
                            $PlayInfoVar['student_sex'] = addslashes(trim($workersVar['student_sex']));
                            if( $workersVar['student_birthday'] !==""){
                                $PlayInfoVar['student_birthday'] = gmdate('Y-m-d', \PHPExcel_Shared_Date::ExcelToPHP($workersVar['student_birthday']));
                            }
                            $PlayInfoVar['company_id'] = $request['company_id'];

                            $data['student_cnname'] =  addslashes(trim($workersVar['student_cnname']));
                            $data['student_enname'] = addslashes(trim($workersVar['student_enname']));
                            $data['student_idcard'] = $workersVar['student_idcard'];
                            $data['student_sex'] = addslashes(trim($workersVar['student_sex']));
                            if( $workersVar['student_birthday'] !==""){
                                $data['student_birthday'] = gmdate('Y-m-d', \PHPExcel_Shared_Date::ExcelToPHP($workersVar['student_birthday']));

                            }
                            $data['company_id'] = $request['company_id'];
                            if( $workersVar['student_createtime'] !==""){
                                $data['student_createtime'] = strtotime(gmdate('Y-m-d', \PHPExcel_Shared_Date::ExcelToPHP($workersVar['student_createtime'])));
                            }else{
                                $data['student_createtime'] = time();
                            }
                            $data['student_updatatime'] = time();
                            if ($student_id = $this->Show_css->insertData('smc_student', $data)) {
                                $family_data =array();
                                if(isset($workersVar['family_mobile']) &&  $workersVar['family_mobile'] !=="" ){
                                    $parentOne=$this->Show_css->getFieldOne("smc_parenter","parenter_id","parenter_mobile='{$workersVar['family_mobile']}'");
                                    if(!$parentOne){
                                        $data=array();
                                        $data['parenter_mobile']=trim($workersVar['family_mobile']);
                                        $data['parenter_cnname']=($workersVar['family_cnname']==""?'':addslashes(trim($workersVar['family_cnname'])));
                                        $data['parenter_pass'] = md5(substr($workersVar['family_mobile'],-6));
                                        $data['parenter_bakpass'] = substr($workersVar['family_mobile'],-6);
                                        $data['parenter_addtime']=time();
                                        $parentid = $this->Show_css->insertData("smc_parenter",$data);
                                    }
                                    $family_data['family_relation']=$workersVar['family_relation']==''?'未知':addslashes(trim($workersVar['family_relation']));
                                    $family_data['family_mobile'] = $workersVar['family_mobile'];
                                    $family_data['family_cnname'] = $workersVar['family_cnname']==""?'':addslashes(trim($workersVar['family_cnname']));
                                    $family_data['student_id'] =$student_id;
                                    $family_data['family_isdefault'] = 1;
                                    if($parentid) {
                                        $family_data['parenter_id'] = $parentid;
                                    }else{
                                        $family_data['parenter_id'] = $parentOne['parenter_id'];
                                    }
                                    $this->Show_css->insertData("smc_student_family",$family_data);
                                }
                                $enrolldata = array();
                                $enrolldata['school_id'] = $request['school_id'];
                                $enrolldata['student_id'] = $student_id;
                                $enrolldata['enrolled_createtime'] = $data['student_createtime'] ;
                                $this->Show_css->insertData('smc_student_enrolled', $enrolldata);
                                /*if (!$this->Show_css->getFieldOne("smc_student_balance", "student_id", "student_id = '{$student_id}' and school_id = '{$request['school_id']}'")) {
                                    $balance = array();
                                    $balance['company_id'] = $request['company_id'];
                                    $balance['student_id'] = $student_id;
                                    $balance['school_id'] = $request['school_id'];
                                    $this->Show_css->insertData("smc_student_balance", $balance);
                                }*/
                                $this->entrySchool($student_id, $request['school_id'], $request['company_id']);

                                //数据同步到微商城
                                $data = array();
                                if ($stuInfo) {
                                    $data['student_branch'] = $stuInfo['student_branch'] + 1;
                                } else {
                                    $data['student_branch'] = $like . '000001';
                                }
                                $ptcData = array();
                                $ptcData['student_cnname'] = $workersVar['student_cnname'];
                                $ptcData['student_enname'] = $workersVar['student_enname'];
                                $ptcData['student_idcard'] = $workersVar['student_idcard'];
                                $ptcData['student_sex'] = $workersVar['student_sex'];
                                $ptcData['student_birthday'] = $workersVar['student_birthday'];
                                $ptcData['family_mobile'] = $workersVar['family_mobile'];
                                $ptcData['family_cnname'] = $workersVar['family_cnname'];
                                $ptcData['family_relation'] = $workersVar['family_relation'];
                                $ptcData['student_branch'] = $data['student_branch'];
                                $bangding = request_by_curl("https://ptcapi.kidcastle.com.cn/Api/BindingParStu", dataEncode($ptcData), "GET", array());
                                //微商城是否绑定成功
                                $PlayInfoVar['bangding'] = $bangding;

                                $PlayInfoVar['error'] = "0";
                                $PlayInfoVar['errortip'] = "导入成功";
                            } else {
                                $PlayInfoVar['student_cnname'] = $workersVar['student_cnname'];
                                $PlayInfoVar['bangding'] = '绑定失败';
                                $PlayInfoVar['error'] = "1";
                                $PlayInfoVar['errortip'] = "导入失败1";
                            }
                        } else {
                            $PlayInfoVar['student_cnname'] = $workersVar['student_cnname'];
                            $PlayInfoVar['bangding'] = '绑定失败';
                            $PlayInfoVar['error'] = "1";
                            $PlayInfoVar['errortip'] = "身份证号重复";
                        }
                    } else {
                        $PlayInfoVar['student_cnname'] = $workersVar['student_cnname'];
                        $PlayInfoVar['bangding'] = '绑定失败';
                        $PlayInfoVar['error'] = "1";
                        $PlayInfoVar['errortip'] = "联系号码为空，无法导入";
                    }

                    $PlayInfo[] = $PlayInfoVar;
                }
            }

        } else {
            $PlayInfoVar = array();
            $PlayInfoVar['student_cnname'] = "导入出错";
            $PlayInfoVar['bangding'] = '绑定失败';
            $PlayInfoVar['error'] = "1";
            $PlayInfoVar['errortip'] = "文件不存在";
            $PlayInfo[] = $PlayInfoVar;
        }

        $this->smarty->assign("PlayInfo", $PlayInfo);
    }

    function entrySchool($student_id, $school_id, $company_id)
    {
        $en_data = array();
        $en_data['school_id'] = $school_id;
        $en_data['student_id'] = $student_id;
        $en_data['enrolled_createtime'] = time();
        $en_data['enrolled_status'] = 0;
        if ($this->Show_css->insertData("smc_student_enrolled", $en_data)) {
            $like = date("Ymd", time());
            $changeInfo = $this->Show_css->selectOne("select change_pid from smc_student_change where change_pid like '{$like}%' and company_id='{$company_id}' order by change_pid DESC limit 0,1");
//
            $data = array();
            $data['company_id'] = $company_id;
            $data['student_id'] = $student_id;
            if ($changeInfo) {
                $data['change_pid'] = $changeInfo['change_pid'] + 1;
            } else {
                $data['change_pid'] = $like . '000001';
            }
            $data['to_stuchange_code'] = 'A01';
            $data['to_school_id'] = $school_id;
            $data['change_status'] = 1;
            $data['change_day'] = date("Y-m-d", time());
//            $data['reason_code']=$reason_code;
            $data['change_reason'] = '入校,后台添加';
            $data['change_workername'] = $this->UserLogin['user_name'];
            $data['change_createtime'] = time();
            $this->Show_css->insertData("smc_student_change", $data);

            $log_data = array();
            $log_data['change_pid'] = $data['change_pid'];
            $log_data['company_id'] = $company_id;
            $log_data['student_id'] = $student_id;
            $log_data['changelog_type'] = 1;
            $log_data['stuchange_code'] = 'A01';
            $log_data['school_id'] = $school_id;
            $log_data['changelog_note'] = '入校,后台添加';
            $log_data['changelog_day'] = date("Y-m-d", time());
//            $log_data['staffer_id'] = $this->stafferOne['staffer_id'];
            $log_data['changelog_createtime'] = time();
            $this->Show_css->insertData("smc_student_changelog", $log_data);

            $this->error = true;
            $this->oktip = "入校成功";
            return true;

        } else {
            $this->error = true;
            $this->errortip = "入校失败";
            return false;
        }
    }


    //魔术方法
    public function __call($name, $arguments)
    {
        $this->smarty->assign("errorTip", "Calling object method '$name' " . implode(', ', $arguments) . "\n");
        $this->Viewhtm = "under.htm";
    }

    //魔术函数
    function __destruct()
    {
        $site_id = Input('get.site_id', 0);

        if ($this->c == 'Wait') {
            $this->smarty->assign("u", $this->router->getController());
            $this->smarty->assign("t", $this->router->getUrl());
            $this->smarty->assign("Viewhtm", $this->Viewhtm);
            $websites = $this->Show_css->getOne("cms_websites", "site_id='{$site_id}'");
            $this->smarty->assign("websites", $websites);
            if ($site_id) {
                if ($this->UserLogin['user_type'] == '1' and $this->UserLogin['user_limitsinc'] == '1') {
                    $moduleList = $this->Show_css->select("SELECT m.* FROM cms_module as m LEFT JOIN cms_moduleapply as a ON a.module_id = m.module_id WHERE a.site_id = '{$site_id}' ORDER BY m.module_weight ASC ");
                } else {
                    $moduleList = $this->Show_css->select("SELECT m.* FROM cms_module as m LEFT JOIN cms_moduleapply as a ON a.module_id = m.module_id LEFT JOIN cms_websites_func as c ON c.site_id = a.site_id and c.module_id = a.module_id WHERE a.site_id = '{$site_id}' and c.user_id = '{$this->UserLogin['user_id']}' ORDER BY m.module_weight ASC ");
                }
                // print_r($moduleList);die;
                $this->smarty->assign("moduleList", $moduleList);
                $this->display("websiteindex.htm");
            } else {
                $this->display("index.htm");
            }
        }
        exit;
    }

}