<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/6/29
 * Time: 23:55
 */

namespace Work\Controller\Manage;


class TmkClientExportController extends viewTpl{
    public $data;
    public $u;
    public $t;
    public $c;
    public $iuser;
    public $Viewhtm;

    //预加载处理类
    function __construct() {
        parent::__construct ();
        if(!$this->check_login()){
            $this->LoginView();
        }
        if($this->check_login()){
            $this->smarty->assign("iuser", $this->UserLogin);
            $this->iuser = $this->UserLogin;
        }
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
        $this->Viewhtm = $this->router->getController()."/".$this->router->getUrl().".htm";

        $moduleOne = $this->Show_css->getOne("cms_module","module_mark='{$this->u}'");
        $this->smarty->assign("moduleOne",$moduleOne);
    }

    //主页
    function HomeView()
    {
        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        if(!isset($request['p'])){
            $p = '1';
        }else{
            $p= $request['p'];
        }

        $datawhere = " c.company_id = '8888' AND c.client_tracestatus = '0' AND e.client_id IS NULL AND c.client_gmcdistributionstatus = '0' and c.channel_id = '319' ";
        $site_id = Input('get.site_id',0);
        $pageurl = "/{$this->u}/{$this->t}?site_id={$site_id}";

        if(isset($request['keyword']) && $request['keyword'] !==''){
            $datawhere .= " and (c.client_cnname like '%{$request['keyword']}%' or c.client_enname like '%{$request['keyword']}%' or c.client_mobile like '%{$request['keyword']}%')";
            $pageurl .="&keyword={$request['keyword']}";
            $datatype['keyword'] = $request['keyword'];
        }

        //开始结束时间
        if(isset($request['starttime']) && $request['starttime']!=''){
            $starttime = strtotime($request['starttime']);
            $datawhere .= " and c.client_createtime>='{$starttime}'";
            $pageurl .="&starttime={$request['starttime']}";
            $datatype['starttime'] = $request['starttime'];
        }
        if(isset($request['endtime']) && $request['endtime']!=''){
            $endtime = strtotime($request['endtime'])+86400;
            $datawhere .= " and c.client_createtime<'{$endtime}'";
            $pageurl .="&endtime={$request['endtime']}";
            $datatype['endtime'] = $request['endtime'];
        }

        $sql = "SELECT c.client_id,c.client_cnname,c.client_enname,c.client_tag,c.client_patriarchname,c.client_mobile,c.client_createtime,c.client_remark,
                (select  GROUP_CONCAT(DISTINCT oo.coursecat_branch) FROM crm_client_intention as ii LEFT JOIN  smc_code_coursecat as oo ON oo.coursecat_id=ii.coursecat_id WHERE ii.client_id=c.client_id ) as course_cnname 
            FROM crm_client AS c
            LEFT JOIN crm_code_channel AS l ON c.channel_id = l.channel_id
            LEFT JOIN crm_client_schoolenter AS e ON c.client_id = e.client_id AND e.is_enterstatus = '1' 
            WHERE {$datawhere} ORDER BY c.client_updatetime DESC";

        $db_nums = $Show_css->selectClear("SELECT COUNT(c.client_id) as countnum FROM crm_client AS c
            LEFT JOIN crm_code_channel AS l ON c.channel_id = l.channel_id
            LEFT JOIN crm_client_schoolenter AS e ON c.client_id = e.client_id AND e.is_enterstatus = '1' 
            WHERE {$datawhere} ORDER BY c.client_updatetime DESC ");
        $allnum = $db_nums[0]['countnum'];
        $datalist = $Show_css->dbwherePage($sql,$allnum,'20',$pageurl.'&p=',$p,'10','2');

        $smarty->assign("pagelist",$datalist['pages']);//筛选信息

        $smarty->assign("dataList",$datalist['cont']);
        $smarty->assign("datatype",$datatype);
    }


    //導出Excel
    function ReportExcelAction(){
        $request = Input('get.');

        $datawhere = " c.company_id = '8888' AND c.client_tracestatus = '0' AND e.client_id IS NULL AND c.client_gmcdistributionstatus = '0' and c.channel_id = '319' ";
         if(isset($request['keyword']) && $request['keyword'] !==''){
            $datawhere .= " and (c.client_cnname like '%{$request['keyword']}%' or c.client_enname like '%{$request['keyword']}%' or c.client_mobile like '%{$request['keyword']}%')";
            $datatype['keyword'] = $request['keyword'];
        }

        //开始结束时间
        if(isset($request['starttime']) && $request['starttime']!=''){
            $starttime = strtotime($request['starttime']);
            $datawhere .= " and c.client_createtime>='{$starttime}'";
            $datatype['starttime'] = $request['starttime'];
        }
        if(isset($request['endtime']) && $request['endtime']!=''){
            $endtime = strtotime($request['endtime'])+86400;
            $datawhere .= " and c.client_createtime<'{$endtime}'";
            $datatype['endtime'] = $request['endtime'];
        }

        $sql = "SELECT c.client_id,c.client_cnname,c.client_enname,c.client_tag,c.client_patriarchname,c.client_mobile,c.client_createtime,c.client_remark,
                (select  GROUP_CONCAT(DISTINCT oo.coursecat_branch) FROM crm_client_intention as ii LEFT JOIN  smc_code_coursecat as oo ON oo.coursecat_id=ii.coursecat_id WHERE ii.client_id=c.client_id ) as course_cnname 
            FROM crm_client AS c
            LEFT JOIN crm_code_channel AS l ON c.channel_id = l.channel_id
            LEFT JOIN crm_client_schoolenter AS e ON c.client_id = e.client_id AND e.is_enterstatus = '1' 
            WHERE {$datawhere} ORDER BY c.client_updatetime DESC";

        $dataList=$this->Show_css->selectClear($sql);

        if($dataList){
            $outexcel=array();
            foreach($dataList as $dataVar){
                $dataarray=array();
                $dataarray['client_id']=$dataVar['client_id'];//ID
                $dataarray['client_cnname']=$dataVar['client_cnname'];//姓名
                $dataarray['client_tag']=$dataVar['client_tag'];//标签
                $dataarray['course_cnname']=$dataVar['course_cnname'];//意向课程
                $dataarray['client_patriarchname']=$dataVar['client_patriarchname'];//家长称呼
                $dataarray['client_mobile']=$dataVar['client_mobile'];//手机号
                $dataarray['client_createtime']=date('Y-m-d H:i:s',$dataVar['client_createtime']);//创建时间
                $dataarray['client_remark']=$dataVar['client_remark'];// 备注

                $outexcel[] = $dataarray;
            }
        }
        $excelheader = array("ID","姓名","标签","意向课程","家长称呼","手机号","创建时间","备注");
        $excelfields = array("client_id","client_cnname","client_tag","course_cnname","client_patriarchname","client_mobile","client_createtime","client_remark");

        query_to_excel($excelheader,$outexcel,$excelfields,date("Ymd")."TMK短信渠道名单.xlsx");
        ajax_return(array("error"=>0,"errortip"=>"导出完毕!","bakfuntion"=>"okmotify"));
    }


    //魔术方法
    public function __call($name, $arguments) {
        $this->smarty->assign("errorTip", "Calling object method '$name' ". implode(', ', $arguments). "\n");
        $this->Viewhtm = "under.htm";
    }
    //魔术函数
    function __destruct()
    {
        $site_id = Input('get.site_id',0);

        if ($this->c =='Wait') {
            $this->smarty->assign("u", $this->router->getController());
            $this->smarty->assign("t", $this->router->getUrl());
            $this->smarty->assign("Viewhtm", $this->Viewhtm);
            $websites = $this->Show_css->getOne("cms_websites","site_id='{$site_id}'");
            $this->smarty->assign("websites",$websites);
            if($site_id){
                if($this->UserLogin['user_type'] == '1' and $this->UserLogin['user_limitsinc'] == '1'){
                    $moduleList = $this->Show_css->select("SELECT m.* FROM cms_module as m LEFT JOIN cms_moduleapply as a ON a.module_id = m.module_id WHERE a.site_id = '{$site_id}' ORDER BY m.module_weight ASC ");
                }else{
                    $moduleList = $this->Show_css->select("SELECT m.* FROM cms_module as m LEFT JOIN cms_moduleapply as a ON a.module_id = m.module_id LEFT JOIN cms_websites_func as c ON c.site_id = a.site_id and c.module_id = a.module_id WHERE a.site_id = '{$site_id}' and c.user_id = '{$this->UserLogin['user_id']}' ORDER BY m.module_weight ASC ");
                }
                // print_r($moduleList);die;
                $this->smarty->assign("moduleList", $moduleList);
                $this->display("websiteindex.htm");
            }else{
                $this->display("index.htm");
            }
        }
        exit;
    }
}