<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2019/12/12
 * Time: 18:13
 */

namespace Work\Controller\Manage;


class CourseHourController extends viewTpl
{
    public $data;
    public $u;
    public $t;
    public $c;
    public $iuser;
    public $Viewhtm;

    //预加载处理类
    function __construct()
    {
        parent::__construct();
        if (!$this->check_login()) {
            $this->LoginView();
        }
        if ($this->check_login()) {
            $this->smarty->assign("iuser", $this->UserLogin);
            $this->iuser = $this->UserLogin;
        }
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
        $this->Viewhtm = $this->router->getController() . "/" . $this->router->getUrl() . ".htm";

        $moduleOne = $this->Show_css->getOne("cms_module", "module_mark='{$this->u}'");
        $this->smarty->assign("moduleOne", $moduleOne);
    }

    //  获取所有的班别
    function HomeView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;
        if (!isset($request['p'])) {
            $p = '1';
        } else {
            $p = $request['p'];
        }

        $datawhere = "1";
        $datatype = array();
        $pageurl = "/{$this->u}/{$this->t}?";
        if ($request['site_id']) {
            $pageurl .= "&site_id={$request['site_id']}";
        }

        if (isset($request['company_id']) && $request['company_id'] !== ''  && $request['company_id'] != 0) {
            $datawhere .= " and co.company_id ='{$request['company_id']}'";
        } else {
            $request['company_id'] = '8888';
            $datawhere .= " and co.company_id ='8888'";
        }
        $companyOne = $this->Show_css->getFieldOne("gmc_company","company_cnname","company_id='{$request['company_id']}'");
        $datatype['company_id'] = $request['company_id'];
        $datatype['company_cnname'] = $companyOne['company_cnname'];

        if (isset($request['coursecat_id']) && $request['coursecat_id'] !== '') {
            $datawhere .= " and co.coursecat_id ='{$request['coursecat_id']}'";
            $datatype['coursecat_id'] = $request['coursecat_id'];
        }

        if (isset($request['course_id']) && $request['course_id'] !== '' && $request['course_id'] != 0) {
            $datawhere .= " and co.course_id ='{$request['course_id']}'";
            $courseOne = $this->Show_css->getFieldOne("smc_course","course_cnname","course_id='{$request['course_id']}'");
            $datatype['course_id'] = $request['course_id'];
            $datatype['course_cnname'] = $courseOne['course_cnname'];
        }

        $sql = "select co.course_id,y.company_cnname,y.company_code,co.course_cnname,co.course_branch,ct.coursecat_cnname,ct.coursecat_branch,co.course_isuseeas
        from smc_course as co  
        left join smc_code_coursecat as ct On co.coursecat_id = ct.coursecat_id
        left join gmc_company as y ON y.company_id=co.company_id
        where  {$datawhere}  
        order by co.company_id,co.course_isuseeas DESC
        ";
        $db_nums = $Show_css->selectClear("select co.course_id from smc_course as co where {$datawhere} ");//相关条件下的总记录数COUNT(*)
        $allnum = count($db_nums);
        $datalist = $Show_css->dbwherePage($sql, $allnum, '20', $pageurl . '&p=', $p, '20', '2');

        $smarty->assign("pagelist", $datalist['pages']);//筛选信息
        $smarty->assign("dataList", $datalist['cont']);
        $smarty->assign("datatype", $datatype);
    }

    // 开启课程填写
    function changeCourseEasAction()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $courseOne = $this->Show_css->getFieldOne("smc_course", "course_isuseeas,course_id,company_id", "course_id='{$request['course_id']}'");
        $courseData = array();
        $courseData['course_isuseeas'] = 1 - $courseOne['course_isuseeas'];
        $this->Show_css->updateData("smc_course", "course_id='{$courseOne['course_id']}'", $courseData);
        if ($courseOne['course_isuseeas'] == 0) {
            $paramArray = array();
            $paramArray['company_id'] = $courseOne['company_id'];
            $paramArray['course_id'] = $courseOne['course_id'];
            $this->addCourseHourBranch($paramArray);
        }
        $res = array('error' => '0', 'errortip' => '修改成功', 'state' => $courseData['course_isuseeas']);
        ajax_return($res);
    }


    function addCourseHourBranch($reqeust)
    {
        $datawhere = "company_id='{$reqeust['company_id']}' and course_isuseeas =1 and course_id ='{$reqeust['course_id']}'";
        $courseOne = $this->Show_css->getFieldOne("smc_course", "course_classnum,course_id,course_branch", "{$datawhere}");
        if ($courseOne) {
            for ($i = 1; $i <= $courseOne['course_classnum']; $i++) {
                $data = array();
                $data['course_id'] = $courseOne['course_id'];
                $data['company_id'] = $reqeust['company_id'];
                $data['coursehour_isfilleas'] = '1';
                $data['coursehour_branch'] = $courseOne['course_branch'] . '_' . $i;
                $data['coursehour_sort'] = $i;

                if ($courseHourOne = $this->Show_css->getFieldOne("smc_coursehour", "coursehour_id", "course_id='{$courseOne['course_id']}' and coursehour_branch = concat('{$courseOne['course_branch']}','-',$i) ")) {
                    $this->Show_css->updateData("smc_coursehour", "coursehour_id='{$courseHourOne['coursehour_id']}'", $data);
                } else {

                    if (!$this->Show_css->insertData("smc_coursehour", $data)) {
                    }
                }
            }
            $this->error = 0;
            $this->errortip = "更新课程{$courseOne['course_branch']}成功";
            return true;
        } else {
            $this->error = 1;
            $this->errortip = "未查询到课次";
            return false;
        }
    }

    //主页
    function courseHourView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        if (!isset($request['p'])) {
            $p = '1';
        } else {
            $p = $request['p'];
        }

        $datawhere = "1";
        $pageurl = "/{$this->u}/{$this->t}?";
        if ($request['site_id']) {
            $pageurl .= "&site_id={$request['site_id']}";
        }

        $coursewhere = 'course_isuseeas =1';
        $datatype = array();
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (co.course_cnname like '%{$request['keyword']}%' or coursecat_branch like '%{$request['keyword']}%' or co.course_branch like '%{$request['keyword']}%')";
            $pageurl .= "&keyword={$request['keyword']}";
            $datatype['keyword'] = $request['keyword'];
        }
        if (isset($request['company_id']) && $request['company_id'] !== '') {
            $datawhere .= " and ch.company_id = '{$request['company_id']}'";
            $pageurl .= "&company_id={$request['company_id']}";
            $datatype['company_id'] = $request['company_id'];
            $coursewhere .= " and company_id='{$request['company_id']}'";
        }

        if (isset($request['course_id']) && $request['course_id'] !== '') {
            $datawhere .= " and ch.course_id = '{$request['course_id']}'";
            $pageurl .= "&course_id={$request['course_id']}";
            $datatype['course_id'] = $request['course_id'];
        }
        $courseList = $Show_css->selectClear("select course_id,concat(course_cnname,'_',course_branch) as course_cnname from smc_course where {$coursewhere} ");
        if (!$courseList) {
            $courseList = array();
        }
        $sql = "SELECT ch.coursehour_id,co.course_cnname,co.course_branch,ch.coursehour_branch,ch.coursehour_isfilleas,y.company_cnname,ct.coursecat_branch
        FROM smc_coursehour as ch
        LEFT JOIN smc_course as co ON ch.course_id = co.course_id and ch.company_id = co.company_id
        LEFT JOIN smc_code_coursecat as ct On co.coursecat_id = ct.coursecat_id 
        LEFT JOIN gmc_company as y ON y.company_id = ch.company_id 
        WHERE {$datawhere} ORDER BY ch.course_id ASC,ch.coursehour_sort ASC";


        $db_nums = $Show_css->selectClear("SELECT * FROM smc_coursehour as ch
        LEFT JOIN smc_course as co ON ch.course_id = co.course_id and ch.company_id = co.company_id
        LEFT JOIN smc_code_coursecat as ct On co.coursecat_id = ct.coursecat_id 
        LEFT JOIN gmc_company as y ON y.company_id = ch.company_id WHERE {$datawhere}");//相关条件下的总记录数COUNT(*)

        $allnum = count($db_nums);

        $datalist = $Show_css->dbwherePage($sql, $allnum, '20', $pageurl . '&p=', $p, '20', '2');

        $smarty->assign("pagelist", $datalist['pages']);//筛选信息
        $smarty->assign("dataList", $datalist['cont']);
        $smarty->assign("datatype", $datatype);
        $smarty->assign("courselist", $courseList);
    }

    // 开启课次填写
    function changeFillEasAction()
    {
        $request = Input("get.", '', 'trim');
        $Show_css = $this->Show_css;
        $hourOne = $Show_css->getFieldOne("smc_coursehour", "coursehour_isfilleas", "coursehour_id='{$request['coursehour_id']}'");
        if ($hourOne) {
            $data = array();
            $data['coursehour_isfilleas'] = 1 - $hourOne['coursehour_isfilleas'];
            $Show_css->updateData("smc_coursehour", "coursehour_id='{$request['coursehour_id']}'", $data);
            $res = array('error' => '0', 'errortip' => '修改成功', 'state' => $data['coursehour_isfilleas']);
            ajax_return($res);
        } else {
            $res = array('error' => '1', 'errortip' => '修改失败', 'result' => array());
            ajax_return($res);
        }
    }


    //魔术方法
    public function __call($name, $arguments)
    {
        $this->smarty->assign("errorTip", "Calling object method '$name' " . implode(', ', $arguments) . "\n");
        $this->Viewhtm = "under.htm";
    }

    //魔术函数
    function __destruct()
    {
        $site_id = Input('get.site_id', 0);

        if ($this->c == 'Wait') {
            $this->smarty->assign("u", $this->router->getController());
            $this->smarty->assign("t", $this->router->getUrl());
            $this->smarty->assign("Viewhtm", $this->Viewhtm);
            $websites = $this->Show_css->getOne("cms_websites", "site_id='{$site_id}'");
            $this->smarty->assign("websites", $websites);
            if ($site_id) {
                if ($this->UserLogin['user_type'] == '1' and $this->UserLogin['user_limitsinc'] == '1') {
                    $moduleList = $this->Show_css->select("SELECT m.* FROM cms_module as m LEFT JOIN cms_moduleapply as a ON a.module_id = m.module_id WHERE a.site_id = '{$site_id}' ORDER BY m.module_weight ASC ");
                } else {
                    $moduleList = $this->Show_css->select("SELECT m.* FROM cms_module as m LEFT JOIN cms_moduleapply as a ON a.module_id = m.module_id LEFT JOIN cms_websites_func as c ON c.site_id = a.site_id and c.module_id = a.module_id WHERE a.site_id = '{$site_id}' and c.user_id = '{$this->UserLogin['user_id']}' ORDER BY m.module_weight ASC ");
                }
                // print_r($moduleList);die;
                $this->smarty->assign("moduleList", $moduleList);
                $this->display("websiteindex.htm");
            } else {
                $this->display("index.htm");
            }
        }
        exit;
    }
}