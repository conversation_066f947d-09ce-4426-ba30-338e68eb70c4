<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn 
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2016/12/15
 * Time: 15:45
 */

namespace Work\Controller\Manage;


class MediaListController extends viewTpl{
    public $data;
    public $u;
    public $t;
    public $c;
    public $iuser;
    public $Viewhtm;

    //预加载处理类
    function __construct() {
        parent::__construct ();
        if(!$this->check_login()){
            $this->LoginView();
        }
        if($this->check_login()){
            $this->smarty->assign("iuser", $this->UserLogin);
            $this->iuser = $this->UserLogin;
        }
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
        $this->Viewhtm = $this->router->getController()."/".$this->router->getUrl().".htm";

        $moduleOne = $this->Show_css->getOne("cms_module","module_mark='{$this->u}'");
        $this->smarty->assign("moduleOne",$moduleOne);
    }
    //主页
    function HomeView()
    {
        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        if(!isset($request['p'])){
            $p = '1';
        }else{
            $p= $request['p'];
        }

        $datawhere = "1";
        $pageurl = "/{$this->u}/{$this->t}?";
        if ($request['site_id']) {
            $pageurl .= "&site_id={$request['site_id']}";
        }

        $datatype = array();
        if(isset($request['keyword']) && $request['keyword'] !==''){
            $datawhere .= " and (m.list_title like '%{$request['keyword']}%' or m.list_intro like '%{$request['keyword']}%' )";
            $pageurl .="&keyword={$request['keyword']}";
            $datatype['keyword'] = $request['keyword'];
        }

        if ($request['site_id']) {
            $datawhere .= " and p.site_id = '{$request['site_id']}'";

            $placeList = $this->Show_css->select("SELECT p.* FROM cms_mediaplace as p WHERE p.site_id = '{$request['site_id']}' ORDER BY place_id DESC");
            $this->smarty->assign("placeList", $placeList);
        }

        if(isset($request['place_id'])){
            $datawhere .= " and m.place_id = '{$request['place_id']}'";
            $pageurl .="&place_id={$request['place_id']}";
            $datatype['place_id'] = $request['place_id'];
        }

        $sql = "SELECT m.*,p.place_name,p.place_width,p.place_height FROM cms_medialist as m LEFT JOIN cms_mediaplace as p ON p.place_id = m.place_id where {$datawhere} order by m.list_time DESC";

        $db_nums = $Show_css->select("SELECT COUNT(*) FROM cms_medialist as m LEFT JOIN cms_mediaplace as p ON p.place_id = m.place_id where {$datawhere}");//相关条件下的总记录数COUNT(*)
        $allnum = $db_nums[0][0];

        $datalist = $Show_css->dbwherePage($sql,$allnum,'10',$pageurl.'&p=',$p,'10','2');

        $smarty->assign("pagelist",$datalist['pages']);//筛选信息

        $smarty->assign("dataList",$datalist['cont']);
        $smarty->assign("datatype",$datatype);
    }
    //增加系统功能
    function AddView()
    {
        $this->smarty->assign("act","Add");
        $request = Input('get.','','trim,addslashes');

        $datawhere = "1";
        if ($request['site_id']) {
            $datawhere .= " and p.site_id = '{$request['site_id']}'";
        }
        $placeList = $this->Show_css->select("SELECT p.*,(SELECT w.site_title FROM cms_websites as w WHERE w.site_id = p.site_id limit 0,1) as site_title FROM cms_mediaplace as p WHERE {$datawhere} ORDER BY place_id DESC");
        $this->smarty->assign("placeList", $placeList);

        $this->Viewhtm = $this->router->getController()."/"."Manage.htm";
    }
    //提交处理机制
    function AddAction()
    {
        $request = Input('post.','','trim,addslashes');
        if($request['list_title'] == '' || $request['place_id'] == ''){
            ajax_return(array('error' => 1,'errortip' => "名称及广告位必须设置!","bakfuntion"=>"warningFromTip"));
        }
        $data = array();
        $data['place_id'] = $request['place_id'];
        $data['list_title'] = $request['list_title'];
        $data['list_intro'] = $request['list_intro'];
        $data['list_weight'] = $request['list_weight'];
        $data['list_outlink'] = $request['list_outlink'];
        $data['list_mediaurl'] = $request['list_mediaurl'];
        $data['list_img'] = $request['list_img'];
        $data['list_imgthum'] = $request['list_imgthum'];
        $data['list_time'] = time();
        if($this->Show_css->insertData("cms_medialist",$data)){
            ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"successFromTip","bakurl"=>"/{$this->u}?site_id={$request['site_id']}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "新增失败!","bakfuntion"=>"dangerFromTip"));
        }
    }
    //增加系统功能
    function EditView()
    {

        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        $datawhere = "1";
        if ($request['site_id']) {
            $datawhere .= " and p.site_id = '{$request['site_id']}'";
        }
        $placeList = $this->Show_css->select("SELECT p.*,(SELECT w.site_title FROM cms_websites as w WHERE w.site_id = p.site_id limit 0,1) as site_title FROM cms_mediaplace as p WHERE {$datawhere} ORDER BY place_id DESC");
        $this->smarty->assign("placeList", $placeList);

        $medialistOne = $Show_css->getOne("cms_medialist", "list_id='{$request['id']}'");
        $smarty->assign("dataVar", $medialistOne);

        $smarty->assign("act", "Edit");

        $this->Viewhtm = $this->router->getController()."/"."Manage.htm";
    }
    //提交处理机制
    function EditAction()
    {
        $request = Input('post.','','trim,addslashes');
        if($request['list_title'] == '' || $request['place_id'] == ''){
            ajax_return(array('error' => 1,'errortip' => "名称及广告位必须设置!","bakfuntion"=>"warningFromTip"));
        }
        $data = array();
        $data['place_id'] = $request['place_id'];
        $data['list_title'] = $request['list_title'];
        $data['list_intro'] = $request['list_intro'];
        $data['list_weight'] = $request['list_weight'];
        $data['list_outlink'] = $request['list_outlink'];
        $data['list_mediaurl'] = $request['list_mediaurl'];
        $data['list_img'] = $request['list_img'];
        $data['list_imgthum'] = $request['list_imgthum'];
        $data['list_time'] = time();
        if($this->Show_css->updateData("cms_medialist","list_id = '{$request['list_id']}'",$data)){
            ajax_return(array('error' => 0,'errortip' => "修改成功!","bakfuntion"=>"successFromTip","bakurl"=>"/{$this->u}?site_id={$request['site_id']}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "新增失败!","bakfuntion"=>"errormotify"));
        }
    }
    //提交处理机制
    function DelAction()
    {
        $list_id = Input('get.id',0);
        if($this->Show_css->delData('cms_medialist',"list_id='{$list_id}'")){
            ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"okmotify","bakurl"=>"/{$this->u}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "新增失败!","bakfuntion"=>"errormotify"));
        }
    }
    //魔术方法
    public function __call($name, $arguments) {
        $this->smarty->assign("errorTip", "Calling object method '$name' ". implode(', ', $arguments). "\n");
        $this->Viewhtm = "under.htm";
    }
    //魔术函数
    function __destruct()
    {
        $site_id = Input('get.site_id',0);

        if ($this->c =='Wait') {
            $this->smarty->assign("u", $this->router->getController());
            $this->smarty->assign("t", $this->router->getUrl());
            $this->smarty->assign("Viewhtm", $this->Viewhtm);
            $websites = $this->Show_css->getOne("cms_websites","site_id='{$site_id}'");
            $this->smarty->assign("websites",$websites);
            if($site_id){
                if($this->UserLogin['user_type'] == '1' and $this->UserLogin['user_limitsinc'] == '1'){
                    $moduleList = $this->Show_css->select("SELECT m.* FROM cms_module as m LEFT JOIN cms_moduleapply as a ON a.module_id = m.module_id WHERE a.site_id = '{$site_id}' ORDER BY m.module_weight ASC ");
                }else{
                    $moduleList = $this->Show_css->select("SELECT m.* FROM cms_module as m LEFT JOIN cms_moduleapply as a ON a.module_id = m.module_id LEFT JOIN cms_websites_func as c ON c.site_id = a.site_id and c.module_id = a.module_id WHERE a.site_id = '{$site_id}' and c.user_id = '{$this->UserLogin['user_id']}' ORDER BY m.module_weight ASC ");
                }
                // print_r($moduleList);die;
                $this->smarty->assign("moduleList", $moduleList);
                $this->display("websiteindex.htm");
            }else{
                $this->display("index.htm");
            }
        }
        exit;
    }
}