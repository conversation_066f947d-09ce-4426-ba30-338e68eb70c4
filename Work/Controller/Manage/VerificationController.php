<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/2/14
 * Time: 10:15
 */

namespace Work\Controller\Manage;


class VerificationController extends viewTpl{
    public $data;
    public $u;
    public $t;
    public $c;
    public $iuser;
    public $Viewhtm;

    //预加载处理类
    function __construct()
    {
        parent::__construct();
        if (!$this->check_login()) {
            $this->LoginView();
        }
        if ($this->check_login()) {
            $this->smarty->assign("iuser", $this->UserLogin);
            $this->iuser = $this->UserLogin;
        }
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
        $this->Viewhtm = $this->router->getController() . "/" . $this->router->getUrl() . ".htm";

        $moduleOne = $this->Show_css->getOne("cms_module", "module_mark='{$this->u}'");
        $this->smarty->assign("moduleOne", $moduleOne);
    }

    //主页
    function HomeView()
    {
        $request = Input('get.','','trim,addslashes');
        $smarty = $this->smarty;

        if(!isset($request['p'])){
            $p = '1';
        }else{
            $p= $request['p'];
        }

        $datawhere = "1";
        $pageurl = "/{$this->u}/{$this->t}?";

        $datatype = array();
        if(isset($request['keyword']) && $request['keyword'] !==''){
            $datawhere .= " and (m.mislog_mobile like '%{$request['keyword']}%' or m.mislog_mistxt like '%{$request['keyword']}%') ";
            $pageurl .="&keyword={$request['keyword']}";
            $datatype['keyword'] = $request['keyword'];
        }

        if(isset($request['mislog_sendinc']) && $request['mislog_sendinc'] !== ''){
            $datawhere .= " and m.mislog_sendinc = '{$request['mislog_sendinc']}'";
            $pageurl .="&mislog_sendinc={$request['mislog_sendinc']}";
            $datatype['mislog_sendinc'] = $request['mislog_sendinc'];
        }

        $sql = "SELECT m.* FROM app_mislog as m  where {$datawhere} order by m.mislog_id DESC";

        $db_nums = $this->Show_css->select("SELECT COUNT(*) FROM app_mislog as m where {$datawhere}");//相关条件下的总记录数COUNT(*)
        $allnum = $db_nums[0][0];

        $datalist = $this->Show_css->dbwherePage($sql,$allnum,'10',$pageurl.'&p=',$p,'10','2');

        $smarty->assign("pagelist",$datalist['pages']);//筛选信息

        $smarty->assign("dataList",$datalist['cont']);
        $smarty->assign("datatype",$datatype);
    }


    //重新补发
    function TodealAction(){
        $request = Input('get.','','trim,addslashes');

        if($request['mislog_id'] == ''){
            ajax_return(array('error' => 1,'errortip' => "未传入日志ID！","bakfuntion"=>"errormotify"));
        }
        $mislogOne = $this->Show_css->getOne("app_mislog","mislog_id='{$request['mislog_id']}'");
        if(!Sendmis($mislogOne['mislog_mobile'],$mislogOne['mislog_mistxt'])){
            ajax_return(array('error' => 1,'errortip' => "短信重发失败！","bakfuntion"=>"errormotify"));
        }else{
            /**补发订单**/
            $date = array();
            $date['mislog_tilte'] = $mislogOne['mislog_tilte'];
            $date['mislog_mobile'] = $mislogOne['mislog_mobile'];
            $date['mislog_sendcode'] = $mislogOne['mislog_sendcode'];
            $date['mislog_mistxt'] = $mislogOne['mislog_mistxt'];
            $date['mislog_sendinc'] = "1";
            $date['mislog_time'] = time();
            $this->Show_css->insertData("app_mislog",$date);

            $this->Show_css->updateData("app_mislog", "mislog_id = '{$mislogOne['mislog_id']}'", array("mislog_retry" => "1"));
            ajax_return(array('error' => 0,'errortip' => "补发成功!","bakfuntion"=>"okmotify"));
        }
    }

    //删除报名活动
    function DelAction()
    {
        $list_id = Input('get.id',0);

        if($this->Show_css->delData('app_mislog',"mislog_id='{$list_id}'")){
            ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"okmotify"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "新增失败!","bakfuntion"=>"errormotify"));
        }
    }
    function __destruct()
    {
        $site_id = Input('get.site_id', 0);

        if ($this->c == 'Wait') {
            $this->smarty->assign("u", $this->router->getController());
            $this->smarty->assign("t", $this->router->getUrl());
            $this->smarty->assign("Viewhtm", $this->Viewhtm);
            $websites = $this->Show_css->getOne("cms_websites", "site_id='{$site_id}'");
            $this->smarty->assign("websites", $websites);
            if ($site_id) {
                if ($this->UserLogin['user_type'] == '1' and $this->UserLogin['user_limitsinc'] == '1') {
                    $moduleList = $this->Show_css->select("SELECT m.* FROM cms_module as m LEFT JOIN cms_moduleapply as a ON a.module_id = m.module_id WHERE a.site_id = '{$site_id}' ORDER BY m.module_weight ASC ");
                } else {
                    $moduleList = $this->Show_css->select("SELECT m.* FROM cms_module as m LEFT JOIN cms_moduleapply as a ON a.module_id = m.module_id LEFT JOIN cms_websites_func as c ON c.site_id = a.site_id and c.module_id = a.module_id WHERE a.site_id = '{$site_id}' and c.user_id = '{$this->UserLogin['user_id']}' ORDER BY m.module_weight ASC ");
                }
                // print_r($moduleList);die;
                $this->smarty->assign("moduleList", $moduleList);
                $this->display("websiteindex.htm");
            } else {
                $this->display("index.htm");
            }
        }
        exit;
    }
}