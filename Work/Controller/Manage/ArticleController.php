<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn 
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong <PERSON>
 * Date: 2016/12/19
 * Time: 14:21
 */

namespace Work\Controller\Manage;


class ArticleController extends viewTpl{
    public $data;
    public $iuser;
    public $u;
    public $t;
    public $c;
    public $Viewhtm;

    function __construct() {
        parent::__construct ();
        if(!$this->check_login()){
            $this->LoginView();
        }
        $this->smarty->assign("iuser", $this->UserLogin);
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
        $this->Viewhtm = $this->router->getController()."/".$this->router->getUrl().".htm";

        $moduleOne = $this->Show_css->getOne("cms_module","module_mark='{$this->u}'");
        $this->smarty->assign("moduleOne",$moduleOne);
        $this->Module = $moduleOne;
    }
    //主页
    function HomeView()
    {

        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        if(!isset($request['p'])){
            $p = '1';
        }else{
            $p= $request['p'];
        }

        $datawhere = "1";
        $pageurl = "/{$this->u}/{$this->t}?site_id={$request['site_id']}";
        if ($request['site_id']) {
            $pageurl .= "&site_id={$request['site_id']}";
        }

        $datatype = array();
        if(isset($request['keyword']) && $request['keyword'] !==''){
            $datawhere .= " and a.cpat_name like '%{$request['keyword']}%' ";
            $pageurl .="&keyword={$request['keyword']}";
            $datatype['keyword'] = $request['keyword'];
        }

        if(isset($request['class']) && $request['class'] !==''){
            $datawhere .= " and a.cpat_class = '{$request['class']}'";
            $pageurl .="&class={$request['class']}";
            $datatype['class'] = $request['class'];
        }


        $sql = "SELECT a.* FROM cms_article as a
                where {$datawhere} order by a.cpat_id ASC ";

        $db_nums = $Show_css->select("SELECT COUNT(*) FROM cms_article as a where {$datawhere}");//相关条件下的总记录数COUNT(*)
        $allnum = $db_nums[0][0];

        $datalist = $Show_css->dbwherePage($sql,$allnum,'10',$pageurl.'&p=',$p,'10','2');

        $smarty->assign("pagelist",$datalist['pages']);
        $smarty->assign("dataList",$datalist['cont']);
        $smarty->assign("datatype",$datatype);
    }

    //增加系统功能
    function AddView()
    {
        //获取标签
        $tags = $this->Show_css->getFieldquery('cms_tags','tags_id,tags_keyword','1');
        $this->smarty->assign("tagsArr",$tags);

        $this->smarty->assign("act","Add");
        $this->Viewhtm = $this->router->getController()."/"."Manage.htm";
    }
    //提交处理机制
    function AddAction()
    {
        $request = Input('post.','','trim,addslashes');
        $data = array();

        if($request['cpat_name'] == ''){
            ajax_return(array('error' => 1,'errortip' => "请填写文章名称!","bakfuntion"=>"warningFromTip"));
        }

        $data['cpat_releasetime'] = getVarToTime($request['cpat_releasetime']);
        $data['cpat_name'] = $request['cpat_name'];
        $data['cpat_from'] = $request['cpat_from'];
        $data['site_id'] = $request['site_id'];
        $data['cpat_author'] = $request['cpat_author'];
        $data['cpat_tags'] = $request['cpat_tags'];
        $data['cpat_urlarticle'] = $request['cpat_urlarticle'];
        $data['cpat_viewed'] = $request['cpat_viewed'];
        $data['cpat_weight'] = $request['cpat_weight'];
        $data['cpat_class'] = $request['cpat_class'];
        if($request['cpat_recommendinc']=='on'){
            $data['cpat_recommendinc']=1;
        }else{
            $data['cpat_recommendinc']=0;
        }
        if($request['cpat_newsinc']=='on'){
            $data['cpat_newsinc']=1;
        }else{
            $data['cpat_newsinc']=0;
        }
        if($request['cpat_topinc']=='on'){
            $data['cpat_topinc']=1;
        }else{
            $data['cpat_topinc']=0;
        }
        $data['cpat_img'] = $request['cpat_img'];
        $data['cpat_smallimg'] = $request['cpat_smallimg'];
        $data['cpat_content'] = $request['cpat_content'];
        $data['cpat_title'] = $request['cpat_title'];
        $data['cpat_key'] = $request['cpat_key'];
        $data['cpat_introduction'] = $request['cpat_introduction'];

        if(is_array($request['affix'])){
            $data['cpat_fileslist'] = implode(',',$request['affix']);
        }else{
            $data['cpat_fileslist'] = $request['affix'];
        }
        if(is_array($request['affix_img'])){
            $data['cpat_imgslist'] = implode(',',$request['affix_img']);
        }else{
            $data['cpat_imgslist'] = $request['affix_img'];
        }
        $data['cpat_addtime'] = time();
        if($this->Show_css->insertData("cms_article",$data)){
            $this->Recordweblog($request['site_id'],$this->Module['module_id'],$this->c,"新增文章数据");
            ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"successFromTip","bakurl"=>"/{$this->u}?site_id={$request['site_id']}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "新增失败!","bakfuntion"=>"dangerFromTip"));
        }
    }
    //增加系统功能
    function EditView()
    {

        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        $usersOne = $Show_css->getOne("cms_article", "cpat_id='{$request['id']}'");
        $smarty->assign("dataVar", $usersOne);

        $smarty->assign("act", "Edit");

        if(isset($usersOne['cpat_fileslist']) && $usersOne['cpat_fileslist'] !==''){
            $filelist = $Show_css->getList("cms_uploadfile", "upfile_id in ({$usersOne['cpat_fileslist']})");
            $smarty->assign("filelist", $filelist);
        }
        if(isset($usersOne['cpat_imgslist']) && $usersOne['cpat_imgslist'] !==''){
            $imglist = $Show_css->getList("cms_uploadfile", "upfile_id in ({$usersOne['cpat_imgslist']})");
            $smarty->assign("imglist", $imglist);
        }
        //获取标签
        $tags = $this->Show_css->getFieldquery('cms_tags','tags_id,tags_keyword','1');
        $this->smarty->assign("tagsArr",$tags);

        $this->Viewhtm = $this->router->getController()."/"."Manage.htm";
    }
    //提交处理机制
    function EditAction()
    {
        $request = Input('post.','','trim,addslashes');
        $data = array();
        if($request['cpat_name'] == ''){
            ajax_return(array('error' => 1,'errortip' => "请填写文章名称!","bakfuntion"=>"warningFromTip"));
        }
        $data['cpat_releasetime'] = getVarToTime($request['cpat_releasetime']);
        $data['cpat_name'] = $request['cpat_name'];
        $data['cpat_from'] = $request['cpat_from'];
        $data['site_id'] = $request['site_id'];
        $data['cpat_author'] = $request['cpat_author'];
        $data['cpat_tags'] = $request['cpat_tags'];
        $data['cpat_urlarticle'] = $request['cpat_urlarticle'];
        $data['cpat_viewed'] = $request['cpat_viewed'];
        $data['cpat_weight'] = $request['cpat_weight'];
        if($request['cpat_recommendinc']=='on'){
            $data['cpat_recommendinc']=1;
        }else{
            $data['cpat_recommendinc']=0;
        }
        if($request['cpat_newsinc']=='on'){
            $data['cpat_newsinc']=1;
        }else{
            $data['cpat_newsinc']=0;
        }
        if($request['cpat_topinc']=='on'){
            $data['cpat_topinc']=1;
        }else{
            $data['cpat_topinc']=0;
        }
        $data['cpat_img'] = $request['cpat_img'];
        $data['cpat_smallimg'] = $request['cpat_smallimg'];
        $data['cpat_content'] = $request['cpat_content'];
        $data['cpat_title'] = $request['cpat_title'];
        $data['cpat_key'] = $request['cpat_key'];
        $data['cpat_introduction'] = $request['cpat_introduction'];

        if(is_array($request['affix'])){
            $data['cpat_fileslist'] = implode(',',$request['affix']);
        }else{
            $data['cpat_fileslist'] = $request['affix'];
        }

        if(is_array($request['affix_img'])){
            $data['cpat_imgslist'] = implode(',',$request['affix_img']);
        }else{
            $data['cpat_imgslist'] = $request['affix_img'];
        }


        if($this->Show_css->updateData("cms_article","cpat_id = '{$request['cpat_id']}'",$data)){
            $this->Recordweblog($request['site_id'],$this->Module['module_id'],$this->c,"修改文章数据，文章ID:{$request['cpat_id']}");
            ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"okmotify","bakurl"=>"/{$this->u}?site_id={$request['site_id']}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "新增失败!","bakfuntion"=>"errormotify"));
        }
    }
    //提交处理机制
    function DelAction()
    {
        $cpat_id = Input('get.id',0);
        $request = Input('get.','','trim,addslashes');
        if($this->Show_css->delData('cms_article',"cpat_id='{$cpat_id}'")){
            $this->Recordweblog($request['site_id'],$this->Module['module_id'],$this->c,"删除文章数据，ID:{$cpat_id}");
            ajax_return(array('error' => 0,'errortip' => "删除成功!","bakfuntion"=>"okmotify"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "新增失败!","bakfuntion"=>"errormotify"));
        }
    }

    //评论
    function CommentView()
    {

        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        if(!isset($request['p'])){
            $p = '1';
        }else{
            $p= $request['p'];
        }

        $datawhere = "1";
        $pageurl = "/{$this->u}/{$this->t}?";

        $datatype = array();
//        if(isset($request['keyword']) && $request['keyword'] !==''){
//            $datawhere .= " and a.cpat_name like '%{$request['keyword']}%' ";
//            $pageurl .="&keyword={$request['keyword']}";
//            $datatype['keyword'] = $request['keyword'];
//        }

        $sql = "SELECT a.* FROM cms_article_comment as a where {$datawhere} order by a.comment_id ASC ";

        $db_nums = $Show_css->select("SELECT COUNT(*) FROM cms_article_comment as a where {$datawhere}");//相关条件下的总记录数COUNT(*)
        $allnum = $db_nums[0][0];

        $datalist = $Show_css->dbwherePage($sql,$allnum,'10',$pageurl.'&p=',$p,'10','2');

        $smarty->assign("pagelist",$datalist['pages']);
        $smarty->assign("dataList",$datalist['cont']);
        $smarty->assign("datatype",$datatype);
    }

    //提交处理机制
    function DelCommentAction()
    {
        $comment_id = Input('get.id',0);
        $request = Input('get.','','trim,addslashes');
        if($this->Show_css->delData('cms_article_comment',"comment_id='{$comment_id}'")){
            $this->Recordweblog($request['site_id'],$this->Module['module_id'],$this->c,"删除文章评论数据，ID:{$comment_id}");
            ajax_return(array('error' => 0,'errortip' => "删除成功!","bakfuntion"=>"okmotify"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "新增失败!","bakfuntion"=>"errormotify"));
        }
    }

    function MatchView(){
        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        if(!isset($request['p'])){
            $p = '1';
        }else{
            $p= $request['p'];
        }

        $datawhere = "1 and cpat_id<>{$request['cpat_id']}";
        $site_id = Input('get.site_id',0);
        $pageurl = "/{$this->u}/{$this->t}?site_id={$site_id}&cpat_id={$request['cpat_id']}";

        $datatype = array();
        if(isset($request['keyword']) && $request['keyword'] !==''){
            $datawhere .= " and (ue.cpat_name like '%{$request['keyword']}%' or ue.cpat_title like '%{$request['keyword']}%' )";
            $pageurl .="&keyword={$request['keyword']}";
        }
        if(isset($request['adaptive']) && $request['adaptive'] !==''){
            $res = $this->Show_css->selectOne("select group_concat(other_id) from cms_article_relevance where cpat_id='{$request['cpat_id']}'");
            $res = $res[0];
            if($request['adaptive']==1 && $res != ''){
                $datawhere .= " and (ue.cpat_id in ({$res}) )";
            }elseif($request['adaptive']==0 && $res != ''){
                $datawhere .= " and (ue.cpat_id not in ({$res}) )";
            }
        }



        $sql = "SELECT cpat_id,cpat_name FROM cms_article as ue
                where {$datawhere} order by ue.cpat_id ASC ";

        $db_nums = $Show_css->select("SELECT COUNT(*) FROM cms_article as ue
                                      where {$datawhere}");//相关条件下的总记录数COUNT(*)
        $allnum = $db_nums[0][0];

        $datalist = $Show_css->dbwherePage($sql,$allnum,'10',$pageurl.'&p=',$p,'10','2');

        $smarty->assign("pagelist",$datalist['pages']);//筛选信息
        $smarty->assign("dataList",$datalist['cont']);

        $article_idArr = $this->Show_css->selectClear("select other_id from cms_article_relevance where cpat_id ='{$request['cpat_id']}'");
        if($article_idArr){
            foreach($article_idArr as &$article_id){
                $article_id = $article_id['other_id'];
            }
        }else{
            $article_idArr = array(-100000);//防止数据为空时,smarty报错
        }
        $smarty->assign("articleIdArr",$article_idArr);
    }
    //关联文章--多个
    function MatchAction(){
        $request = Input('post.','','trim,addslashes');
        $cpat_id = $request['cpat_id'];
        $actionType = $request['ActionType'];
        $postUnivArr = $request['tab_list'];
        //该课程已经匹配的学校id
        $article_idArr = $this->Show_css->selectClear("select other_id from cms_article_relevance where cpat_id ='{$request['cpat_id']}'");
        if($article_idArr){
            foreach($article_idArr as &$article_id){
                $article_id = $article_id['other_id'];
            }
        }

        if($actionType==1){
            //添加关联记录
            if($postUnivArr){
                foreach($postUnivArr as $otherId){
                    if($article_idArr == ''){
                        $this->Show_css->insertData('cms_article_relevance',array('cpat_id'=>$cpat_id,'other_id'=>$otherId));
                    }else{
                        if(!in_array($otherId,$article_idArr)){
                            $this->Show_css->insertData('cms_article_relevance',array('cpat_id'=>$cpat_id,'other_id'=>$otherId));
                        }
                    }

                }
                $this->Recordweblog($request['site_id'],$this->Module['module_id'],$this->c,"添加文章关联数据，文章ID:{$request['cpat_id']}");
                ajax_return(array('error' => 0,'errortip' => "操作成功!","bakfuntion"=>"successFromTip","bakurl"=>"/{$this->u}/Match?site_id={$request['site_id']}&cpat_id=$cpat_id"));
            }
        }elseif($actionType==2){
            //取消适配
            if($postUnivArr){
                foreach($postUnivArr as $otherId){
                    if(in_array($otherId,$article_idArr)){
                        $this->Show_css->delData('cms_article_relevance',"cpat_id={$cpat_id} and other_id={$otherId} ");
                    }
                }
                $this->Recordweblog($request['site_id'],$this->Module['module_id'],$this->c,"取消文章关联数据，文章ID:{$request['cpat_id']}");
                ajax_return(array('error' => 0,'errortip' => "操作成功!","bakfuntion"=>"successFromTip","bakurl"=>"/{$this->u}/Match?site_id={$request['site_id']}&cpat_id=$cpat_id"));
            }
        }
    }
    //关联文章--单个
    function MatchSinAction(){
        $request = Input('get.','','trim,addslashes');
        $cpat_id = $request['cpat_id'];
        $other_id = $request['other_id'];
        //判断是否适配
        $exist = $this->Show_css->selectOne("select * from cms_article_relevance where cpat_id={$cpat_id} and other_id={$other_id}");
        if($exist){
            //删除记录
            if($this->Show_css->delData("cms_article_relevance","cpat_id={$cpat_id} and other_id={$other_id}")){
                ajax_return(array('error' => 0,'errortip' => "操作成功!","bakfuntion"=>"successFromTip","result"=>0));
            }else{
                ajax_return(array('error' => 1,'errortip' => "操作失败1!","bakfuntion"=>"successFromTip"));
            }
        }else{
            //添加记录
            if($this->Show_css->insertData("cms_article_relevance",array('cpat_id'=>$cpat_id,'other_id'=>$other_id))){
                ajax_return(array('error' => 0,'errortip' => "操作成功!","bakfuntion"=>"successFromTip","result"=>1));
            }else{
                ajax_return(array('error' => 1,'errortip' => "操作失败2!","bakfuntion"=>"successFromTip"));
            }
        }

        $this->Recordweblog($request['site_id'],$this->Module['module_id'],$this->c,"文章批量关联数据，文章ID:{$request['cpat_id']}");
        ajax_return($request);
    }

    function DoubleView(){

        $cpat_id = Input('get.cpat_id');
        $this->smarty->assign('cpat_id',$cpat_id);

        $this->smarty->assign("act", "Double");
        $this->Viewhtm = $this->router->getController()."/"."Double.htm";
    }
    function DoubleAction(){
        $request = Input('post.','','trim,addslashes');

        if($request['cpat_class']){
            $articleOne = $this->Show_css->selectClear("select * from cms_article where cpat_id={$request['cpat_id']}");
            $articleOne = $articleOne[0];
            unset($articleOne['cpat_id']);
            $articleOne['cpat_class'] = $request['cpat_class'];
            //print_r($articleOne);die;
            if($this->Show_css->insertData('cms_article',$articleOne)){
                $this->Recordweblog($request['site_id'],$this->Module['module_id'],$this->c,"复制文章数据，文章ID:{$request['cpat_id']}");
                ajax_return(array('error' => 0,'errortip' => "复制成功!","bakfuntion"=>"okmotify"));
            }else{
                ajax_return(array('error' => 1,'errortip' => "复制失败!","bakfuntion"=>"errormotify"));
            }
        }else{
            ajax_return(array('error' => 1,'errortip' => "请选择分类!","bakfuntion"=>"errormotify"));
        }
    }

    //ajax修改三个状态
    public function updatedataAction(){
        $request = Input('get.','','trim,addslashes');
        $articleOne = $this->Show_css->selectOne("select cpat_recommendinc,cpat_topinc,cpat_newsinc from cms_article where cpat_id={$request['article_id']}");
        if($articleOne["{$request['article_type']}"]=='1'){
            $res = $this->Show_css->updateData('cms_article',"cpat_id={$request['article_id']}",array("{$request['article_type']}"=>0));
            if($res){
                $this->Recordweblog($request['site_id'],$this->Module['module_id'],$this->c,"文章修改状态，文章ID:{$request['cpat_id']}");
                ajax_return(array('error' => 0,'errortip' => "操作成功!","bakfuntion"=>"okmotify",'type'=>'remove'));
            }else{
                ajax_return(array('error' => 1,'errortip' => "操作失败!","bakfuntion"=>"errormotify"));
            }
        }elseif($articleOne["{$request['article_type']}"]=='0'){
            $res = $this->Show_css->updateData('cms_article',"cpat_id={$request['article_id']}",array("{$request['article_type']}"=>1));
            if($res){
                $this->Recordweblog($request['site_id'],$this->Module['module_id'],$this->c,"文章修改状态，文章ID:{$request['cpat_id']}");
                ajax_return(array('error' => 0,'errortip' => "操作成功!","bakfuntion"=>"okmotify",'type'=>'add'));
            }else{
                ajax_return(array('error' => 1,'errortip' => "操作失败!","bakfuntion"=>"errormotify"));
            }
        }

    }


    //下方批量操作
    public function setAction(){
        $request = Input('post.','','trim,addslashes');

        if($request['ActionType']==1){
            //转移
            if($request['cpat_class']=='' &&$request['cpat_class']==''){
                ajax_return(array('error' => 1,'errortip' => "请先选择分类!","bakfuntion"=>"errormotify"));
            }
            foreach($request['tab_list'] as $tabVar){
                $this->Show_css->updateData('cms_article',"cpat_id={$tabVar}",array('cpat_class'=>$request['cpat_class']));
            }
            ajax_return(array('error' => 0,'errortip' => "转移成功!","bakfuntion"=>"okmotify"));
        }elseif($request['ActionType']==2){
            //复制
            if($request['cpat_class']=='' &&$request['cpat_class']==''){
                ajax_return(array('error' => 1,'errortip' => "请先选择分类!","bakfuntion"=>"errormotify"));
            }
            foreach($request['tab_list'] as $tabVar){
                $articleOne = $this->Show_css->selectClear("select * from cms_article where cpat_id={$tabVar}");
                $articleOne = $articleOne[0];
                $articleOne['cpat_class'] = $request['cpat_class'];
                unset($articleOne['cpat_id']);
                $this->Show_css->insertData('cms_article',$articleOne);
            }
            ajax_return(array('error' => 0,'errortip' => "复制成功!","bakfuntion"=>"okmotify"));
        }elseif($request['ActionType']==3){
            //推荐
            foreach($request['tab_list'] as $tabVar){
                $this->Show_css->updateData('cms_article',"cpat_id={$tabVar}",array('cpat_recommendinc'=>1));
            }
            ajax_return(array('error' => 0,'errortip' => "推荐成功!","bakfuntion"=>"okmotify"));
        }elseif($request['ActionType']==4){
            //推荐
            foreach($request['tab_list'] as $tabVar){
                $this->Show_css->updateData('cms_article',"cpat_id={$tabVar}",array('cpat_topinc'=>1));
            }
            ajax_return(array('error' => 0,'errortip' => "置顶成功!","bakfuntion"=>"okmotify"));
        }elseif($request['ActionType']==5){
            //推荐
            foreach($request['tab_list'] as $tabVar){
                $this->Show_css->updateData('cms_article',"cpat_id={$tabVar}",array('cpat_newsinc'=>1));
            }
            ajax_return(array('error' => 0,'errortip' => "设为最新成功!","bakfuntion"=>"okmotify"));
        }elseif($request['ActionType']==6){
            //删除
            foreach($request['tab_list'] as $tabVar){
                $this->Show_css->delData('cms_article',"cpat_id='{$tabVar}'");
            }
            ajax_return(array('error' => 0,'errortip' => "批量删除成功!","bakfuntion"=>"okmotify"));
        }
    }
    //魔术方法
    public function __call($name, $arguments) {
        $this->smarty->assign("errorTip", "Calling object method '$name' ". implode(', ', $arguments). "\n");

        $this->Viewhtm = "under.htm";
    }
    //魔术方法
    function __destruct()
    {
        $site_id = Input('get.site_id',0);

        if ($this->c =='Wait') {
            $this->smarty->assign("u", $this->router->getController());
            $this->smarty->assign("t", $this->router->getUrl());
            $this->smarty->assign("Viewhtm", $this->Viewhtm);
            if($site_id){
                $websites = $this->Show_css->getOne("cms_websites","site_id='{$site_id}'");
                $this->smarty->assign("websites",$websites);

                if($this->UserLogin['user_type'] == '1' and $this->UserLogin['user_limitsinc'] == '1'){
                    $moduleList = $this->Show_css->select("SELECT m.* FROM cms_module as m LEFT JOIN cms_moduleapply as a ON a.module_id = m.module_id WHERE a.site_id = '{$site_id}' ORDER BY m.module_weight ASC ");
                }else{
                    $moduleList = $this->Show_css->select("SELECT m.* FROM cms_module as m LEFT JOIN cms_moduleapply as a ON a.module_id = m.module_id LEFT JOIN cms_websites_func as c ON c.site_id = a.site_id and c.module_id = a.module_id WHERE a.site_id = '{$site_id}' and c.user_id = '{$this->UserLogin['user_id']}' ORDER BY m.module_weight ASC ");
                }
                $this->smarty->assign("moduleList", $moduleList);
                $this->display("websiteindex.htm");
            }else{
                $this->display("index.htm");
            }
        }
        exit;
    }

}
