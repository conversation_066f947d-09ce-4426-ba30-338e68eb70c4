<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn 
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2016/12/16
 * Time: 11:51
 */

namespace Work\Controller\Manage;


class SiteModuleController  extends viewTpl{
    public $data;
    public $u;
    public $t;
    public $c;
    public $iuser;
    public $Viewhtm;

    //预加载处理类
    function __construct() {
        parent::__construct ();
        if(!$this->check_login()){
            $this->LoginView();
        }
        if($this->check_login()){
            $this->smarty->assign("iuser", $this->UserLogin);
            $this->iuser = $this->UserLogin;
        }
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
        $this->Viewhtm = $this->router->getController()."/".$this->router->getUrl().".htm";


        $moduleOne = $this->Show_css->getOne("cms_module","module_mark='{$this->u}'");
        $this->smarty->assign("moduleOne",$moduleOne);

    }
    //主页
    function HomeView()
    {
        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        if(!isset($request['p'])){
            $p = '1';
        }else{
            $p= $request['p'];
        }

        $datawhere = "1 and m.father_id = '0' ";
        $pageurl = "/{$this->u}/{$this->t}?";
        if ($request['site_id']) {
            $pageurl .= "&site_id={$request['site_id']}";
        }

        $datatype = array();
        if(isset($request['keyword']) && $request['keyword'] !==''){
            $datawhere .= " and (m.module_name like '%{$request['keyword']}%')";
            $pageurl .="&keyword={$request['keyword']}";
            $datatype['keyword'] = $request['keyword'];
        }
        if(isset($request['module_class']) && $request['module_class'] !==''){
            $datawhere .= " and (m.module_class={$request['module_class']})";
            $pageurl .="&module_class={$request['module_class']}";
            $datatype['module_class'] = $request['module_class'];
        }
        if(isset($request['module_level']) && $request['module_level'] !==''){
            $datawhere .= " and (m.module_level={$request['module_level']})";
            $pageurl .="&module_level={$request['module_level']}";
            $datatype['module_level'] = $request['module_level'];
        }

        $sql = "SELECT m.*,(select d.module_name from imc_module as d WHERE m.father_id = d.module_id) as father_name  FROM imc_module as m  where {$datawhere} order by module_weight ASC, m.module_id DESC";

        $db_nums = $Show_css->selectOne("SELECT COUNT(*) as num FROM imc_module as m  where {$datawhere}");//相关条件下的总记录数COUNT(*)
		
		;
        $allnum = $db_nums['num'];

        $datalist = $Show_css->dbwherePage($sql,$allnum,'10',$pageurl.'&p=',$p,'10','2');

        $smarty->assign("pagelist",$datalist['pages']);//筛选信息

        $smarty->assign("dataList",$datalist['cont']);
        $smarty->assign("datatype",$datatype);
    }
    //手册管理
    function HandbookLookView(){
        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        $smarty->assign("module_id",$request['module_id']);

        if(!isset($request['p'])){
            $p = '1';
        }else{
            $p= $request['p'];
        }

        $datawhere = "1  and h.module_id = '{$request['module_id']}' ";

        $pageurl = "/{$this->u}/{$this->t}?";
        $pageurl .= "&module_id={$request['module_id']}";
        if ($request['site_id']) {
            $pageurl .= "&site_id={$request['site_id']}";
        }

        $datatype = array();
//        if(isset($request['keyword']) && $request['keyword'] !==''){
//            $datawhere .= " and (m.module_name like '%{$request['keyword']}%')";
//            $pageurl .="&keyword={$request['keyword']}";
//            $datatype['keyword'] = $request['keyword'];
//        }
//        if(isset($request['module_class']) && $request['module_class'] !==''){
//            $datawhere .= " and (m.module_class={$request['module_class']})";
//            $pageurl .="&module_class={$request['module_class']}";
//            $datatype['module_class'] = $request['module_class'];
//        }
//        if(isset($request['module_level']) && $request['module_level'] !==''){
//            $datawhere .= " and (m.module_level={$request['module_level']})";
//            $pageurl .="&module_level={$request['module_level']}";
//            $datatype['module_level'] = $request['module_level'];
//        }

        $sql = "SELECT h.*,(select d.module_name from imc_module as d WHERE h.module_id = d.module_id) as module_name  FROM imc_module_handbook as h  where {$datawhere} order by h.handbook_weight ASC, h.handbook_id DESC";

        $db_nums = $Show_css->selectOne("SELECT COUNT(h.handbook_id) as num FROM imc_module_handbook as h  where {$datawhere}");//相关条件下的总记录数COUNT(*)
        $allnum = $db_nums['num'];

        $datalist = $Show_css->dbwherePage($sql,$allnum,'10',$pageurl.'&p=',$p,'10','2');

        $smarty->assign("pagelist",$datalist['pages']);//筛选信息

        $smarty->assign("dataList",$datalist['cont']);
        $smarty->assign("datatype",$datatype);
    }
    //手册
    function HandbookAddView(){
        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;
        $this->smarty->assign("act","HandbookAdd");

        $mediaplaceOne = $Show_css->getOne("imc_module", "module_id='{$request['module_id']}'");
        if($mediaplaceOne['module_class'] == '1'){
            $mediaplaceOne['vtype'] = 'gmc';
        }elseif($mediaplaceOne['module_class'] == '2'){
            $mediaplaceOne['vtype'] = 'smc';
        }elseif($mediaplaceOne['module_class'] == '3'){
            $mediaplaceOne['vtype'] = 'crm';
        }elseif($mediaplaceOne['module_class'] == '4'){
            $mediaplaceOne['vtype'] = 'easx';
        }
        $smarty->assign("dataVar", $mediaplaceOne);

        $this->Viewhtm = $this->router->getController()."/"."HandbookManage.htm";
    }
    //提交处理
    function HandbookAddAction()
    {
        $request = Input('post.','','trim,addslashes');
        $data = array();

        if($request['handbook_name'] == '' ||  $request['handbook_note'] == ''){
            ajax_return(array('error' => 1,'errortip' => "手册名称或者手册内容必须设置!","bakfuntion"=>"warningFromTip"));
        }

        $data['module_id'] = $request['module_id'];
        $data['handbook_name'] = $request['handbook_name'];
        $data['handbook_note'] = $request['handbook_note'];
        $data['handbook_videourl'] = $request['handbook_videourl'];
        $data['handbook_weight'] = $request['handbook_weight'];
        $data['handbook_createtime'] = time();
        $data['handbook_updatatime'] = time();
        if($this->Show_css->insertData("imc_module_handbook",$data)){
            ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"successFromTip","bakurl"=>"/SiteModule/HandbookLook?site_id={$request['site_id']}&module_id={$request['module_id']}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "新增失败!","bakfuntion"=>"dangerFromTip"));
        }
    }
    //手册修改
    function HandbookEditView(){
        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;
        $this->smarty->assign("act","HandbookEdit");

        $mediaplaceOne = $Show_css->getOne("imc_module_handbook", "handbook_id='{$request['handbook_id']}'");
        $smarty->assign("dataVar", $mediaplaceOne);

        $this->Viewhtm = $this->router->getController()."/"."HandbookManage.htm";
    }
    //提交处理
    function HandbookEditAction()
    {
        $request = Input('post.','','trim,addslashes');
        $data = array();

        if($request['handbook_name'] == '' ||  $request['handbook_note'] == ''){
            ajax_return(array('error' => 1,'errortip' => "手册名称或者手册内容必须设置!","bakfuntion"=>"warningFromTip"));
        }
        $data['module_id'] = $request['module_id'];
        $data['handbook_name'] = $request['handbook_name'];
        $data['handbook_note'] = $request['handbook_note'];
        $data['handbook_videourl'] = $request['handbook_videourl'];
        $data['handbook_weight'] = $request['handbook_weight'];
        $data['handbook_updatatime'] = time();
        if($this->Show_css->updateData("imc_module_handbook","handbook_id = '{$request['handbook_id']}' and module_id = '{$request['module_id']}' ",$data)){
            ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"okmotify","bakurl"=>"/SiteModule/HandbookLook?site_id={$request['site_id']}&module_id={$request['module_id']}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "修改失败!","bakfuntion"=>"errormotify"));
        }
    }
    //删除单个
    function DelhandbookAction()
    {
        $request = Input('get.','','trim,addslashes');

        if($this->Show_css->delData('imc_module_handbook',"handbook_id = '{$request['handbook_id']}' and module_id = '{$request['module_id']}' ")){
            ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"okmotify","bakurl"=>"/{$this->u}/{$this->t}?site_id={$request['site_id']}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "新增失败!","bakfuntion"=>"errormotify"));
        }
    }

    //增加
    function AddView()
    {
        $this->smarty->assign("act","Add");
        $productList=$this->Show_css->getList("imc_product");
        $this->smarty->assign("productList", $productList);


        $this->Viewhtm = $this->router->getController()."/"."Manage.htm";
    }
    //提交处理
    function AddAction()
    {
        $request = Input('post.','','trim,addslashes');
        $data = array();

        if($request['module_name'] == '' ){
            ajax_return(array('error' => 1,'errortip' => "模块名称必须设置!","bakfuntion"=>"warningFromTip"));
        }

        if($request['product_id'] == '' ){
            ajax_return(array('error' => 1,'errortip' => "所属产品必须选择!","bakfuntion"=>"warningFromTip"));
        }

        $data['module_name'] = $request['module_name'];
        $data['product_id'] = $request['product_id'];
        $data['module_level'] = 1;
        $data['module_class'] = $request['module_class'];
        $data['module_markstring'] = $request['module_markstring'];
        $data['module_markurl'] = $request['module_markurl'];
        $data['father_id'] = $request['father_id'];
        $data['module_access'] = $request['module_access'];
        $data['module_weight'] = $request['module_weight'];
        $data['module_icon'] = $request['module_icon'];
        $data['module_isshow'] = $request['module_isshow'];
        $data['module_img'] = $request['module_img'];
        $data['module_ismajor'] = $request['module_ismajor'];
        $data['module_isset'] = $request['module_isset'];
        $data['module_createtime'] = time();
        $data['module_updatatime'] = time();
        if($this->Show_css->insertData("imc_module",$data)){
            ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"successFromTip","bakurl"=>"/SiteModule?site_id={$request['site_id']}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "新增失败!","bakfuntion"=>"dangerFromTip"));
        }
    }
    //修改
    function EditView()
    {
        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        $mediaplaceOne = $Show_css->getOne("imc_module", "module_id='{$request['module_id']}'");
        $smarty->assign("dataVar", $mediaplaceOne);

        $productList=$this->Show_css->getList("imc_product");
        $this->smarty->assign("productList", $productList);

        $smarty->assign("act", "Edit");

        $this->Viewhtm = $this->router->getController()."/"."Manage.htm";
    }
    //修改处理
    function EditAction()
    {
        $request = Input('post.','','trim,addslashes');
        if($request['module_name'] == '' ){
            ajax_return(array('error' => 1,'errortip' => "模块名称必须设置!","bakfuntion"=>"warningFromTip"));
        }

        if($request['product_id'] == '' ){
            ajax_return(array('error' => 1,'errortip' => "所属产品必须选择!","bakfuntion"=>"warningFromTip"));
        }


        $data = array();
        $data['module_name'] = $request['module_name'];
        $data['product_id'] = $request['product_id'];
        $data['module_class'] = $request['module_class'];
        $data['module_markstring'] = $request['module_markstring'];
        $data['module_markurl'] = $request['module_markurl'];
        $data['father_id'] = $request['father_id'];
        $data['module_access'] = $request['module_access'];
        $data['module_weight'] = $request['module_weight'];
        $data['module_icon'] = $request['module_icon'];
        $data['module_isshow'] = $request['module_isshow'];
        $data['module_img'] = $request['module_img'];
        $data['module_ismajor'] = $request['module_ismajor'];
        $data['module_isset'] = $request['module_isset'];
        $data['module_updatatime'] = time();
//print_r($data);die;
        if($this->Show_css->updateData("imc_module","module_id = '{$request['module_id']}'",$data)){
            ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"okmotify","bakurl"=>"/{$this->u}?site_id={$request['site_id']}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "新增失败!","bakfuntion"=>"errormotify"));
        }
    }
    //删除单个
    function DelAction()
    {
        $request = Input('get.','','trim,addslashes');
        $place_id = Input('get.id',0);

        $ModuleOne = $this->Show_css->getOne("imc_module", "father_id='{$place_id}'");
        if($ModuleOne){
            ajax_return(array('error' => 1,'errortip' => "请先删除子集!","bakfuntion"=>"warningFromTip"));
        }

        if($this->Show_css->delData('imc_module',"module_id='{$place_id}'")){
            ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"okmotify","bakurl"=>"/{$this->u}?site_id={$request['site_id']}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "新增失败!","bakfuntion"=>"errormotify"));
        }
    }

    //下级管理
    function SiteModuleChildView(){
        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        if(!isset($request['p'])){
            $p = '1';
        }else{
            $p= $request['p'];
        }

        $datawhere = "1  ";
        $pageurl = "/{$this->u}/{$this->t}?";
        if ($request['site_id']) {
            $pageurl .= "&site_id={$request['site_id']}";
        }

        $datatype = array();
        if(isset($request['keyword']) && $request['keyword'] !==''){
            $datawhere .= " and (m.module_name like '%{$request['keyword']}%')";
            $pageurl .="&keyword={$request['keyword']}";
            $datatype['keyword'] = $request['keyword'];
        }
        if(isset($request['module_class']) && $request['module_class'] !==''){
            $datawhere .= " and (m.module_class={$request['module_class']})";
            $pageurl .="&module_class={$request['module_class']}";
            $datatype['module_class'] = $request['module_class'];
        }
        if(isset($request['module_id']) && $request['module_id'] !==''){
            $datawhere .= " and m.father_id = '{$request['module_id']}' ";
            $pageurl .="&module_id={$request['module_id']}";
            $datatype['module_id'] = $request['module_id'];
        }

        $sql = "SELECT m.*,(select d.module_name from imc_module as d WHERE m.father_id = d.module_id) as father_name  FROM imc_module as m  where {$datawhere} order by module_weight ASC,m.module_id DESC";

        $db_nums = $Show_css->selectOne("SELECT COUNT(*) FROM imc_module as m  where {$datawhere}");//相关条件下的总记录数COUNT(*)
        $allnum = $db_nums[0];

        $datalist = $Show_css->dbwherePage($sql,$allnum,'20',$pageurl.'&p=',$p,'10','2');

        $smarty->assign("pagelist",$datalist['pages']);//筛选信息

        $smarty->assign("dataList",$datalist['cont']);
        $smarty->assign("datatype",$datatype);
    }
    //添加
    function AddChildView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->smarty->assign("act","AddChild");
        //父级
        $this->smarty->assign("father_id",$request['module_id']);

        $productList=$this->Show_css->getList("imc_product");
        $this->smarty->assign("productList", $productList);

        $this->Viewhtm = $this->router->getController()."/"."ManageChild.htm";
    }
    //提交处理
    function AddChildAction()
    {
        $request = Input('post.','','trim,addslashes');
        $data = array();

        if($request['module_name'] == '' ){
            ajax_return(array('error' => 1,'errortip' => "模块名称必须设置!","bakfuntion"=>"warningFromTip"));
        }
        if($request['father_id'] == '' ){
            ajax_return(array('error' => 1,'errortip' => " 非法操作!","bakfuntion"=>"warningFromTip"));
        }else{
            $mediaplaceOne = $this->Show_css->getOne("imc_module", "module_id='{$request['father_id']}'");
            $data['module_level'] = $mediaplaceOne['module_level']+1;
        }

        if($request['product_id'] == '' ){
            ajax_return(array('error' => 1,'errortip' => "所属产品必须选择!","bakfuntion"=>"warningFromTip"));
        }

        $data['module_name'] = $request['module_name'];
        $data['module_class'] = $request['module_class'];
        $data['product_id'] = $request['product_id'];
        $data['module_markstring'] = $request['module_markstring'];
        $data['module_markurl'] = $request['module_markurl'];
        $data['module_icon'] = $request['module_icon'];
        $data['father_id'] = $request['father_id'];
        $data['module_access'] = $request['module_access'];
        $data['module_weight'] = $request['module_weight'];
        $data['module_isshow'] = $request['module_isshow'];
        $data['module_img'] = $request['module_img'];
        $data['module_ismajor'] = $request['module_ismajor'];
        $data['module_isset'] = $request['module_isset'];
        $data['module_createtime'] = time();
        $data['module_updatatime'] = time();
        if($this->Show_css->insertData("imc_module",$data)){
            ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"successFromTip","bakurl"=>"/SiteModule/SiteModuleChild?site_id={$request['site_id']}&module_id={$request['father_id']}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "新增失败!","bakfuntion"=>"dangerFromTip"));
        }
    }
    //修改
    function EditChildView()
    {
        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        //父级
        $this->smarty->assign("father_id",$request['father_id']);

        $mediaplaceOne = $Show_css->getOne("imc_module", "module_id='{$request['module_id']}'");
        $smarty->assign("dataVar", $mediaplaceOne);

        $productList=$this->Show_css->getList("imc_product");
        $this->smarty->assign("productList", $productList);

        $smarty->assign("act", "EditChild");

        $this->Viewhtm = $this->router->getController()."/"."ManageChild.htm";
    }
    //修改处理
    function EditChildAction()
    {
        $request = Input('post.','','trim,addslashes');
        if($request['module_name'] == '' ){
            ajax_return(array('error' => 1,'errortip' => "模块名称必须设置!","bakfuntion"=>"warningFromTip"));
        }

        if($request['product_id'] == '' ){
            ajax_return(array('error' => 1,'errortip' => "所属产品必须选择!","bakfuntion"=>"warningFromTip"));
        }

        $data = array();
        $data['module_name'] = $request['module_name'];
        $data['module_class'] = $request['module_class'];
        $data['product_id'] = $request['product_id'];
        $data['module_markstring'] = $request['module_markstring'];
        $data['module_icon'] = $request['module_icon'];
        $data['module_markurl'] = $request['module_markurl'];
        $data['father_id'] = $request['father_id'];
        $data['module_access'] = $request['module_access'];
        $data['module_weight'] = $request['module_weight'];
        $data['module_isshow'] = $request['module_isshow'];
        $data['module_img'] = $request['module_img'];
        $data['module_ismajor'] = $request['module_ismajor'];
        $data['module_isset'] = $request['module_isset'];
        $data['module_updatatime'] = time();

        if($this->Show_css->updateData("imc_module","module_id = '{$request['module_id']}'",$data)){
            ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"okmotify","bakurl"=>"/{$this->u}/SiteModuleChild?site_id={$request['site_id']}&module_id={$request['father_id']}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "新增失败!","bakfuntion"=>"errormotify"));
        }
    }
    //删除单个
    function DelChildAction()
    {
        $request = Input('get.','','trim,addslashes');
        $place_id = Input('get.id',0);

        $ModuleOne = $this->Show_css->getOne("imc_module", "father_id='{$place_id}'");
        if($ModuleOne){
            ajax_return(array('error' => 1,'errortip' => "请先删除子集!","bakfuntion"=>"warningFromTip"));
        }

        if($this->Show_css->delData('imc_module',"module_id='{$place_id}'")){
            ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"okmotify","bakurl"=>"/{$this->u}/SiteModuleChild?site_id={$request['site_id']}&module_id={$request['father_id']}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "新增失败!","bakfuntion"=>"errormotify"));
        }
    }


    //功能模块管理
    function ModuleFunctionView(){
        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        if(!isset($request['p'])){
            $p = '1';
        }else{
            $p= $request['p'];
        }

        $datawhere = "1";
        $pageurl = "/{$this->u}/{$this->t}?";
        if ($request['site_id']) {
            $pageurl .= "&site_id={$request['site_id']}";
        }

        $datatype = array();
        if(isset($request['keyword']) && $request['keyword'] !==''){
            $datawhere .= " and (f.function_name like '%{$request['keyword']}%' or f.function_markstring like '%{$request['keyword']}%')";
            $pageurl .="&keyword={$request['keyword']}";
            $datatype['keyword'] = $request['keyword'];
        }
        if(isset($request['module_id']) && $request['module_id'] !==''){
            $datawhere .= " and f.module_id = '{$request['module_id']}' ";
            $pageurl .="&module_id={$request['module_id']}";
            $datatype['module_id'] = $request['module_id'];
        }

        $sql = "SELECT f.*  FROM imc_module_function as f  where {$datawhere} order by f.function_id DESC";

        $db_nums = $Show_css->selectOne("SELECT COUNT(*) FROM imc_module_function as f  where {$datawhere}");//相关条件下的总记录数COUNT(*)
        $allnum = $db_nums[0];

        $datalist = $Show_css->dbwherePage($sql,$allnum,'10',$pageurl.'&p=',$p,'10','2');

        $smarty->assign("pagelist",$datalist['pages']);//筛选信息

        $smarty->assign("dataList",$datalist['cont']);
        $smarty->assign("datatype",$datatype);
    }
    //功能模块添加
    function FunctionAddView(){
        $request = Input('get.','','trim,addslashes');
        $this->smarty->assign("act","FunctionAdd");
        //父级
        $this->smarty->assign("father_id",$request['module_id']);

        $this->Viewhtm = $this->router->getController()."/"."FunctionManage.htm";
    }
    //提交处理
    function FunctionAddAction()
    {
        $request = Input('post.','','trim,addslashes');
        $data = array();

        if($request['function_name'] == '' ){
            ajax_return(array('error' => 1,'errortip' => "功能名称必须设置!","bakfuntion"=>"warningFromTip"));
        }
        if($request['module_id'] == '' ){
            ajax_return(array('error' => 1,'errortip' => " 非法操作!","bakfuntion"=>"warningFromTip"));
        }

        $data['function_name'] = $request['function_name'];
        $data['function_markstring'] = $request['function_markstring'];
        $data['module_markstring'] = $request['module_markstring'];
        $data['module_id'] = $request['module_id'];
        $data['function_fieldjson'] = $request['function_fieldjson'];
        $data['function_access'] = $request['function_access'];
        $data['function_createtime'] = time();
        $data['function_updatatime'] = time();
        if($this->Show_css->insertData("imc_module_function",$data)){
            ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"successFromTip","bakurl"=>"/SiteModule/ModuleFunction?site_id={$request['site_id']}&module_id={$request['module_id']}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "新增失败!","bakfuntion"=>"dangerFromTip"));
        }
    }
    //修改
    function FunctionEditView()
    {
        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        //父级
        $this->smarty->assign("module_id",$request['module_id']);

        $mediaplaceOne = $Show_css->getOne("imc_module_function", "function_id='{$request['function_id']}'");
        $smarty->assign("dataVar", $mediaplaceOne);

        $smarty->assign("act", "FunctionEdit");

        $this->Viewhtm = $this->router->getController()."/"."FunctionManage.htm";
    }
    //修改处理
    function FunctionEditAction()
    {
        $request = Input('post.','','trim,addslashes');
        if($request['function_name'] == '' ){
            ajax_return(array('error' => 1,'errortip' => "模块功能名称必须设置!","bakfuntion"=>"warningFromTip"));
        }

        $data = array();
        $data['function_name'] = $request['function_name'];
        $data['function_markstring'] = $request['function_markstring'];
        $data['function_fieldjson'] = $request['function_fieldjson'];
        $data['function_access'] = $request['function_access'];
        $data['function_updatatime'] = time();

        if($this->Show_css->updateData("imc_module_function","function_id = '{$request['function_id']}'",$data)){
            ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"okmotify","bakurl"=>"/{$this->u}/ModuleFunction?site_id={$request['site_id']}&module_id={$request['module_id']}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "新增失败!","bakfuntion"=>"errormotify"));
        }
    }
    //删除单个
    function FunctionDelAction()
    {
        $request = Input('get.','','trim,addslashes');
        $place_id = Input('get.id',0);

        if($this->Show_css->delData('imc_module_function',"function_id='{$place_id}'")){
            ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"okmotify","bakurl"=>"/{$this->u}/ModuleFunction?site_id={$request['site_id']}&module_id={$request['module_id']}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "新增失败!","bakfuntion"=>"errormotify"));
        }
    }

    //常见问题
    function FaquestionView(){

        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        $smarty->assign("module_id",$request['module_id']);

        if(!isset($request['p'])){
            $p = '1';
        }else{
            $p= $request['p'];
        }

        $datawhere = "1  ";// and mf.module_id = '{$request['module_id']}'
        $pageurl = "/{$this->u}/{$this->t}?";
        if ($request['site_id']) {
            $pageurl .= "&site_id={$request['site_id']}";
        }

        if(isset($request['module_id']) && $request['module_id'] !==''){
            $datawhere .= " and mf.module_id='{$request['module_id']}' ";
            $pageurl .="&module_id={$request['module_id']}";
            $datatype['module_id'] = $request['module_id'];
        }

        $datatype = array();

         $sql = "select mf.*,
                (select m.module_name from imc_module as m where m.module_id=mf.module_id ) as module_name
                from imc_module_faq as mf  where {$datawhere} ";
        $db_nums = $Show_css->selectOne("SELECT COUNT(mf.faq_id) as num FROM imc_module_faq as mf  where {$datawhere}");//相关条件下的总记录数COUNT(*)
        $allnum = $db_nums['num'];
        $datalist = $Show_css->dbwherePage($sql,$allnum,'10',$pageurl.'&p=',$p,'10','2');

        $smarty->assign("pagelist",$datalist['pages']);//筛选信息

        $smarty->assign("dataList",$datalist['cont']);
        $smarty->assign("datatype",$datatype);

    }

    function FaquestionAddView(){
        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;
        $this->smarty->assign("act","FaquestionAdd");

        $mediaplaceOne = $Show_css->getOne("imc_module", "module_id='{$request['module_id']}'");

        $smarty->assign("dataVar", $mediaplaceOne);

        $this->Viewhtm = $this->router->getController()."/"."FaquestionManage.htm";

    }

    function FaquestionAddACtion(){
        $request = Input('post.','','trim,addslashes');
        $data = array();

        if($request['faq_name'] == '' ||  $request['faq_note'] == ''){
            ajax_return(array('error' => 1,'errortip' => "问题名称或者手册内容必须设置!","bakfuntion"=>"warningFromTip"));
        }
        if($request['module_id'] == '' ){
            ajax_return(array('error' => 1,'errortip' => " 非法操作!","bakfuntion"=>"warningFromTip"));
        }

        $data['faq_name'] = $request['faq_name'];
        $data['module_id'] = $request['module_id'];
        $data['faq_weight'] = $request['faq_weight'];
        $data['module_id'] = $request['module_id'];
        $data['faq_note'] = $request['faq_note'];
        $data['faq_createtime'] = $request['function_fieldjson'];
        $data['faq_createtime'] = time();

        if($this->Show_css->insertData("imc_module_faq",$data)){
            ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"successFromTip","bakurl"=>"/SiteModule/Faquestion?site_id={$request['site_id']}&module_id={$request['module_id']}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "新增失败!","bakfuntion"=>"dangerFromTip"));
        }

    }


    function FaquestionEditView(){
        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;
        $this->smarty->assign("act","FaquestionEdit");

        $mediaplaceOne = $Show_css->getOne("imc_module_faq", "faq_id='{$request['faq_id']}'");
        $smarty->assign("dataVar", $mediaplaceOne);

        $this->Viewhtm = $this->router->getController()."/"."FaquestionManage.htm";
    }

    function FaquestionEditAction()
    {
        $request = Input('post.','','trim,addslashes');
        $data = array();

        if($request['faq_name'] == '' ||  $request['faq_note'] == ''){
            ajax_return(array('error' => 1,'errortip' => "问题名称或者手册内容必须设置!","bakfuntion"=>"warningFromTip"));
        }
        $data['module_id'] = $request['module_id'];
        $data['faq_name'] = $request['faq_name'];
        $data['faq_note'] = $request['faq_note'];
        $data['faq_weight'] = $request['faq_weight'];
        $data['faq_updatatime'] = time();
        if($this->Show_css->updateData("imc_module_faq","faq_id = '{$request['faq_id']}' and module_id = '{$request['module_id']}' ",$data)){
            ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"okmotify","bakurl"=>"/SiteModule/Faquestion?site_id={$request['site_id']}&module_id={$request['module_id']}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "修改失败!","bakfuntion"=>"errormotify"));
        }
    }

    function DelFaquestionAction()
    {
        $request = Input('get.','','trim,addslashes');

        if($this->Show_css->delData('imc_module_faq',"faq_id = '{$request['faq_id']}' and module_id = '{$request['module_id']}' ")){
            ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"okmotify","bakurl"=>"/{$this->u}/{$this->t}?site_id={$request['site_id']}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "新增失败!","bakfuntion"=>"errormotify"));
        }
    }



    //魔术方法
    public function __call($name, $arguments) {
        $this->smarty->assign("errorTip", "Calling object method '$name' ". implode(', ', $arguments). "\n");
        $this->Viewhtm = "under.htm";
    }
    //魔术函数
    function __destruct()
    {
        $site_id = Input('get.site_id',0);

        if ($this->c =='Wait') {
            $this->smarty->assign("u", $this->router->getController());
            $this->smarty->assign("t", $this->router->getUrl());
            $this->smarty->assign("Viewhtm", $this->Viewhtm);
            $websites = $this->Show_css->getOne("cms_websites","site_id='{$site_id}'");
            $this->smarty->assign("websites",$websites);
            if($site_id){
                if($this->UserLogin['user_type'] == '1' and $this->UserLogin['user_limitsinc'] == '1'){
                    $moduleList = $this->Show_css->select("SELECT m.* FROM cms_module as m LEFT JOIN cms_moduleapply as a ON a.module_id = m.module_id WHERE a.site_id = '{$site_id}' ORDER BY m.module_weight ASC ");
                }else{
                    $moduleList = $this->Show_css->select("SELECT m.* FROM cms_module as m LEFT JOIN cms_moduleapply as a ON a.module_id = m.module_id LEFT JOIN cms_websites_func as c ON c.site_id = a.site_id and c.module_id = a.module_id WHERE a.site_id = '{$site_id}' and c.user_id = '{$this->UserLogin['user_id']}' ORDER BY m.module_weight ASC ");
                }
                // print_r($moduleList);die;
                $this->smarty->assign("moduleList", $moduleList);
                $this->display("websiteindex.htm");
            }else{
                $this->display("index.htm");
            }
        }
        exit;
    }

}