<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2019/2/16
 * Time: 11:56
 */

namespace Work\Controller\Manage;


class ComLineRechargeController extends viewTpl
{
    public $data;
    public $u;
    public $t;
    public $c;
    public $icampus;
    public $iuser;
    public $Viewhtm;
    public $Module;

    //预加载处理类
    function __construct()
    {
        parent::__construct();
        if (!$this->check_login()) {
            $this->LoginView();
        }
        if ($this->check_login()) {
            $this->smarty->assign("iuser", $this->UserLogin);
            $this->iuser = $this->UserLogin;
        }
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
        $this->Viewhtm = $this->router->getController() . "/" . $this->router->getUrl() . ".htm";
        $moduleOne = $this->Show_css->getOne("cms_module", "module_mark='{$this->u}'");
        $this->smarty->assign("moduleOne", $moduleOne);
        $this->Module = $moduleOne;
    }

    //主页
    function HomeView()
    {
        $request = Input('get.','','trim,addslashes');
        $datatype = array();
        $datatype['company_id'] = $request['company_id'];
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        if (!isset($request['p'])) {
            $p = '1';
        } else {
            $p = $request['p'];
        }

        $datawhere = " ";
        $pageurl = "/{$this->u}/{$this->t}?site_id={$request['site_id']}";


        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (c.company_cnname like '%{$request['keyword']}%' or c.company_code like '%{$request['keyword']}%' )";
            $pageurl .= "&keyword={$request['keyword']}";
            $datatype['keyword'] = $request['keyword'];
        }
//AS '集团ID',
//AS '集团名称',
//AS '集团编号',
//AS '管理员密码',
//AS '在线时长（分钟）',
//AS '上课时长（分钟）',
//AS '累计充值金额',
//AS '累计消费金额',
//AS '账户余额'
        $sql = "SELECT
                 s.company_id ,
                 s.company_cnname ,
                 s.company_code ,
                 s.staffer_bakpass ,
                 s.duration ,
                 paytime ,
                 IFNULL(playamount, 0)  as leijicz,
                 payprice as leijixf,
                 IFNULL(playamount, 0) - payprice  as zhanghuye
                FROM
                 (
                  SELECT
                   c.company_id,
                   c.company_cnname,
                   c.company_code,
                   (
                    SELECT
                     t.staffer_bakpass
                    FROM
                     smc_staffer AS t
                    WHERE
                     t.company_id = c.company_id
                    AND t.account_class = '1' 
                    AND (t.staffer_branch = c.company_code or t.staffer_mobile = c.company_code) 
                   ) AS staffer_bakpass,
                   (
                    sum(l.tkylog_duration) / 60
                   ) AS duration,
                   (sum(l.tkylog_paytime) / 60) AS paytime,
                   ROUND(
                    (
                     (sum(l.tkylog_paytime) / 60) * 0.0392
                    ),
                    2
                   ) AS payprice,
                   (
                    SELECT
                     SUM(b.log_playamount)
                    FROM
                     tkl_balance_log AS b
                    WHERE
                     b.company_id = c.company_id
                   ) AS playamount
                  FROM
                   tkl_linerooms_tkylog AS l,
                   gmc_company AS c
                  WHERE
                   l.company_id = c.company_id AND l.company_id <> '8888' {$datawhere}
                  GROUP BY
                   l.company_id
                 ) AS s
                
                ORDER BY (IFNULL(playamount, 0) - payprice) ASC";

        $db_nums = $Show_css->selectOne("SELECT COUNT(s.company_id) as countnums FROM
 (
  SELECT
   c.company_id,
   c.company_cnname,
   c.company_code,
   (
    SELECT
     t.staffer_bakpass
    FROM
     smc_staffer AS t
    WHERE
     t.company_id = c.company_id
    AND t.account_class = '1' 
    AND (t.staffer_branch = c.company_code or t.staffer_mobile = c.company_code) 
   ) AS staffer_bakpass,
   (
    sum(l.tkylog_duration) / 60
   ) AS duration,
   (sum(l.tkylog_paytime) / 60) AS paytime,
   ROUND(
    (
     (sum(l.tkylog_paytime) / 60) * 0.0392
    ),
    2
   ) AS payprice,
   (
    SELECT
     SUM(b.log_playamount)
    FROM
     tkl_balance_log AS b
    WHERE
     b.company_id = c.company_id
   ) AS playamount
  FROM
   tkl_linerooms_tkylog AS l,
   gmc_company AS c
  WHERE
   l.company_id = c.company_id AND l.company_id <> '8888'
  GROUP BY
   l.company_id
 ) AS s

ORDER BY (IFNULL(playamount, 0) - payprice) ASC");//相关条件下的总记录数COUNT(*)
        $allnum = $db_nums['countnums'];

        $datalist = $Show_css->dbwherePage($sql, $allnum, '10', $pageurl . '&p=', $p, '10', '2');

        $smarty->assign("pagelist", $datalist['pages']);//筛选信息
        $smarty->assign("dataList", $datalist['cont']);
        $smarty->assign("datatype", $datatype);
    }

    //下载统计
    function ExportAction(){
        $request=Input('post.','','trim,addslashes');

        $sql = "SELECT
                 s.company_id ,
                 s.company_cnname ,
                 s.company_code ,
                 s.staffer_bakpass ,
                 s.duration ,
                 paytime ,
                 IFNULL(playamount, 0)  as leijicz,
                 payprice as leijixf,
                 IFNULL(playamount, 0) - payprice  as zhanghuye
                FROM
                 (
                  SELECT
                   c.company_id,
                   c.company_cnname,
                   c.company_code,
                   (
                    SELECT
                     t.staffer_bakpass
                    FROM
                     smc_staffer AS t
                    WHERE
                     t.company_id = c.company_id
                    AND t.account_class = '1'
                    AND (t.staffer_branch = c.company_code or t.staffer_mobile = c.company_code) 
                   ) AS staffer_bakpass,
                   (
                    sum(l.tkylog_duration) / 60
                   ) AS duration,
                   (sum(l.tkylog_paytime) / 60) AS paytime,
                   ROUND(
                    (
                     (sum(l.tkylog_paytime) / 60) * 0.0392
                    ),
                    2
                   ) AS payprice,
                   (
                    SELECT
                     SUM(b.log_playamount)
                    FROM
                     tkl_balance_log AS b
                    WHERE
                     b.company_id = c.company_id
                   ) AS playamount
                  FROM
                   tkl_linerooms_tkylog AS l,
                   gmc_company AS c
                  WHERE
                   l.company_id = c.company_id AND l.company_id <> '8888' 
                  GROUP BY
                   l.company_id
                 ) AS s
                
                ORDER BY (IFNULL(playamount, 0) - payprice) ASC";

        $dataList=$this->Show_css->select($sql);
        if($dataList){
            $outexcel=array();
            foreach($dataList as $dataVar){
                    $dataarray = array();
                    $dataarray['company_id'] = $dataVar['company_id']; //集团ID
                    $dataarray['company_cnname'] = $dataVar['company_cnname']; //集团名称
                    $dataarray['company_code'] = $dataVar['company_code']; //集团编号
                    $dataarray['staffer_bakpass'] = $dataVar['staffer_bakpass']; //管理员密码
                    $dataarray['duration'] = $dataVar['duration']; //在线时长（分钟）
                    $dataarray['paytime'] = $dataVar['paytime']; //上课时长（分钟）
                    $dataarray['leijicz'] = $dataVar['leijicz']; //累计充值金额
                    $dataarray['leijixf'] = $dataVar['leijixf']; //累计消费金额
                    $dataarray['zhanghuye'] = $dataVar['zhanghuye']; //账户余额
                    $outexcel[] = $dataarray;
            }
        }
        $excelheader=array("集团ID","集团名称","集团编号","管理员密码","在线时长（分钟）","上课时长（分钟）","累计充值金额","累计消费金额","账户余额" );
        $excelfields=array("company_id","company_cnname","company_code","staffer_bakpass","duration","paytime","leijicz","leijixf","zhanghuye" );
        $excelname = date(Ymd)."集团充值统计数据导出.xlsx";
        if(!is_array($outexcel)){
            jsbakerror_spl("没有数据！");
            exit;
        }
        query_to_excel($excelheader,$outexcel,$excelfields,$excelname);
        ajax_return(array("error"=>0,"errortip"=>"下载完毕!","bakfuntion"=>"okmotify"));
    }


    function __destruct()
    {
        $site_id = Input('get.site_id', 0);

        if ($this->c == 'Wait') {
            $this->smarty->assign("u", $this->router->getController());
            $this->smarty->assign("t", $this->router->getUrl());
            $this->smarty->assign("Viewhtm", $this->Viewhtm);
            $websites = $this->Show_css->getOne("cms_websites", "site_id='{$site_id}'");
            $this->smarty->assign("websites", $websites);
            if ($site_id) {
                $this->display("websiteindex.htm");
            } else {
                $this->display("index.htm");
            }
        }
        exit;
    }


}