<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn 
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/2/14
 * Time: 10:15
 */

namespace Work\Controller\Manage;


class ParameterController extends viewTpl{
    public $data;
    public $u;
    public $t;
    public $c;
    public $icampus;
    public $iuser;
    public $Viewhtm;
    public $Module;

    //预加载处理类
    function __construct() {
        parent::__construct ();
        if(!$this->check_login()){
            $this->LoginView();
        }
        if($this->check_login()){
            $this->smarty->assign("iuser", $this->UserLogin);
            $this->iuser = $this->UserLogin;
        }
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
        $this->Viewhtm = $this->router->getController()."/".$this->router->getUrl().".htm";
        $moduleOne = $this->Show_css->getOne("cms_module","module_mark='{$this->u}'");
        $this->smarty->assign("moduleOne",$moduleOne);
        $this->Module = $moduleOne;
    }

    //主页
    function HomeView()
    {
        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        if(!isset($request['p'])){
            $p = '1';
        }else{
            $p= $request['p'];
        }

        $datawhere = "1";
        $pageurl = "/{$this->u}/{$this->t}?";
        if ($request['site_id']) {
            $pageurl .= "&site_id={$request['site_id']}";
        }

        $datatype = array();
        if(isset($request['keyword']) && $request['keyword'] !==''){
            $datawhere .= " and p.parameter_name like '%{$request['keyword']}%' ";
            $pageurl .="&keyword={$request['keyword']}";
            $datatype['keyword'] = $request['keyword'];
        }

         if(isset($request['type_name'])  && $request['type_name'] !==''){
             $datawhere .= " and t.type_name like '%{$request['type_name']}%'";
             $pageurl .="&type_name={$request['type_name']}";
             $datatype['type_name'] = $request['type_name'];
         }

        $sql = "SELECT p.*,t.type_name FROM myw_parameter as p LEFT JOIN myw_parameter_type as t on p.type_id=t.type_id where {$datawhere} order by p.parameter_id DESC";

        $db_nums = $Show_css->select("SELECT COUNT(p.parameter_id) FROM myw_parameter as p LEFT JOIN myw_parameter_type as t on p.type_id=t.type_id  where {$datawhere}");//相关条件下的总记录数COUNT(*)
        $allnum = $db_nums[0][0];

        $datalist = $Show_css->dbwherePage($sql,$allnum,'10',$pageurl.'&p=',$p,'10','2');

        $smarty->assign("pagelist",$datalist['pages']);//筛选信息

        $smarty->assign("dataList",$datalist['cont']);
        $smarty->assign("datatype",$datatype);
    }

    //活动编辑
    function EditView()
    {
        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        $smarty->assign("act","Edit");

        $typeList = $Show_css->getList("myw_parameter_type"," 1 ");
        $smarty->assign("typeList",$typeList);

        $activity = $Show_css->getOne("myw_parameter","parameter_id='{$request['id']}'");
        $smarty->assign("dataVar",$activity);

        $this->Viewhtm = $this->router->getController()."/"."Edit.htm";
    }
    //活动编辑
    function EditAction()
    {
        $request = Input('post.','','trim,addslashes');
        $Show_css = $this->Show_css;

        if($Show_css->getOne("myw_parameter","parameter_name = '{$request['parameter_name']}' and  parameter_id <> '{$request['parameter_id']}' ")){
            ajax_return(array('error' => 1,'errortip' => "名称重复!","bakfuntion"=>"dangerFromTip"));
        }

        $data = array();
        $data['type_id'] = $request['type_id'];
        $data['parameter_name'] = $request['parameter_name'];
        $data['parameter_sort'] = $request['parameter_sort'];

        if($Show_css->updateData("myw_parameter","parameter_id = '{$request['parameter_id']}'",$data)){
            $this->Recordweblog($request['site_id'],$this->Module['module_id'],$this->c,"修改参数信息数据，参数ID:{$request['parameter_id']}");
            ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"successFromTip"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "提交失败!","bakfuntion"=>"dangerFromTip"));
        }
    }
    //添加客户
    function AddView()
    {
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;
        $smarty->assign("act","Add");

        $typeList = $Show_css->getList("myw_parameter_type"," 1 ");
        $smarty->assign("typeList",$typeList);

        $this->Viewhtm = $this->router->getController()."/"."Edit.htm";
    }
    //新增客户
    function AddAction()
    {
        $request = Input('post.','','trim,addslashes');
        $Show_css = $this->Show_css;

        if($Show_css->getOne("myw_parameter","parameter_name = '{$request['parameter_name']}' and type_id = '{$request['type_id']}' ")){
            ajax_return(array('error' => 1,'errortip' => "名称重复!","bakfuntion"=>"dangerFromTip"));
        }

        $data = array();
        $data['type_id'] = $request['type_id'];
        $data['parameter_name'] = $request['parameter_name'];
        $data['parameter_sort'] = $request['parameter_sort'];

        if($Show_css->insertData("myw_parameter",$data)){
            $this->Recordweblog($request['site_id'],$this->Module['module_id'],$this->c,"新增参数信息数据");
            ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"successFromTip","bakurl"=>"/{$this->u}?site_id={$request['site_id']}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "提交失败!","bakfuntion"=>"dangerFromTip"));
        }
    }
    //删除报名活动
    function DelAction()
    {
        $list_id = Input('get.id',0);

        if($this->Show_css->delData('myw_parameter',"client_id='{$list_id}'")){
            // $this->Show_css->delData('cms_atapply',"client_id='{$list_id}'");
            // $this->Show_css->delData('cms_session',"client_id='{$list_id}'");
            ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"okmotify"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "新增失败!","bakfuntion"=>"errormotify"));
        }
    }
    function __destruct()
    {
        $site_id = Input('get.site_id',0);

        if ($this->c =='Wait') {
            $this->smarty->assign("u", $this->router->getController());
            $this->smarty->assign("t", $this->router->getUrl());
            $this->smarty->assign("Viewhtm", $this->Viewhtm);
            $websites = $this->Show_css->getOne("cms_websites","site_id='{$site_id}'");
            $this->smarty->assign("websites",$websites);
            if($site_id){
                if($this->UserLogin['user_type'] == '1' and $this->UserLogin['user_limitsinc'] == '1'){
                    $moduleList = $this->Show_css->select("SELECT m.* FROM cms_module as m LEFT JOIN cms_moduleapply as a ON a.module_id = m.module_id WHERE a.site_id = '{$site_id}' ORDER BY m.module_weight ASC ");
                }else{
                    $moduleList = $this->Show_css->select("SELECT m.* FROM cms_module as m LEFT JOIN cms_moduleapply as a ON a.module_id = m.module_id LEFT JOIN cms_websites_func as c ON c.site_id = a.site_id and c.module_id = a.module_id WHERE a.site_id = '{$site_id}' and c.user_id = '{$this->UserLogin['user_id']}' ORDER BY m.module_weight ASC ");
                }
                $this->smarty->assign("moduleList", $moduleList);

                $this->display("websiteindex.htm");
            }else{
                $this->display("index.htm");
            }
        }
        exit;
    }
}