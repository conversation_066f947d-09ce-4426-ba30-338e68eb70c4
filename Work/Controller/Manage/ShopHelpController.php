<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2016/12/14
 * Time: 22:02
 */

namespace Work\Controller\Manage;


class ShopHelpController extends viewTpl{
    public $data;
    public $u;
    public $t;
    public $c;
    public $iuser;
    public $Viewhtm;

    //预加载处理类
    function __construct() {
        parent::__construct ();
        if(!$this->check_login()){
            $this->LoginView();
        }
        if($this->check_login()){
            $this->smarty->assign("iuser", $this->UserLogin);
            $this->iuser = $this->UserLogin;
        }
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
        $this->Viewhtm = $this->router->getController()."/".$this->router->getUrl().".htm";


        $moduleOne = $this->Show_css->getOne("cms_module","module_mark='{$this->u}'");
        $this->smarty->assign("moduleOne",$moduleOne);
    }

    //主页
    function HomeView()
    {
        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        if(!isset($request['p'])){
            $p = '1';
        }else{
            $p= $request['p'];
        }

        $datawhere = "1";
        $pageurl = "/{$this->u}/{$this->t}?";
        if ($request['site_id']) {
            $pageurl .= "&site_id={$request['site_id']}";
        }

        $datatype = array();
        if(isset($request['keyword']) && $request['keyword'] !==''){
            $datawhere .= " and (h.help_menuname like '%{$request['keyword']}%' or h.help_title like '%{$request['keyword']}%' )";
            $pageurl .="&keyword={$request['keyword']}";
            $datatype['keyword'] = $request['keyword'];
        }

        if(isset($request['list_parameter']) && $request['list_parameter']!==''){
            $datawhere .= " and h.list_parameter = '{$request['list_parameter']}'";
            $pageurl .="&list_parameter={$request['list_parameter']}";
            $datatype['list_parameter'] = $request['list_parameter'];
        }

        $sql = "SELECT
                    h.*,v.list_name
                FROM
                    shop_help AS h
                LEFT JOIN
                    cms_variablelist as v ON v.list_parameter = h.list_parameter AND v.variable_id = '3'
                WHERE {$datawhere}
                ORDER BY h.list_parameter ASC,h.help_id DESC";
        $db_nums = $Show_css->selectOne("SELECT COUNT(help_id) as countnum FROM shop_help as h where {$datawhere}");//相关条件下的总记录数COUNT(*)
        $allnum = $db_nums['countnum'];

        $datalist = $Show_css->dbwherePage($sql,$allnum,'10',$pageurl.'&p=',$p,'10','2');

        $smarty->assign("pagelist",$datalist['pages']);//筛选信息

        $smarty->assign("dataList",$datalist['cont']);
        $smarty->assign("datatype",$datatype);

        $menulist = $this->Show_css->selectClear("SELECT v.list_name,v.list_parameter FROM cms_variablelist as v WHERE v.variable_id = '3'");
        $this->smarty->assign("menulist",$menulist);
    }

    function AddView(){
        $this->smarty->assign("act", "Add");

        $menulist = $this->Show_css->selectClear("SELECT v.list_name,v.list_parameter FROM cms_variablelist as v WHERE v.variable_id = '3'");
        $this->smarty->assign("menulist",$menulist);

        $this->Viewhtm = $this->router->getController()."/"."Manage.htm";
    }
    //提交处理机制
    function AddAction()
    {
        $request = Input('post.','','trim,addslashes');
        if($request['help_menuname'] == ''){
            ajax_return(array('error' => 1,'errortip' => "菜单名称不能为空!","bakfuntion"=>"warningFromTip"));
        }
        if($request['help_title'] == ''){
            ajax_return(array('error' => 1,'errortip' => "文章名称不能为空!","bakfuntion"=>"warningFromTip"));
        }
        if($request['help_content'] == ''){
            ajax_return(array('error' => 1,'errortip' => "菜单内容不能为空!","bakfuntion"=>"warningFromTip"));
        }

        $data = array();
        $data['list_parameter'] = $request['list_parameter'];
        $data['help_menuname'] = $request['help_menuname'];
        $data['help_title'] = $request['help_title'];
        $data['help_content'] = $request['help_content'];
        $data['help_addtime'] = time();

        if($this->Show_css->insertData("shop_help",$data)){
            ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"successFromTip","bakurl"=>"/{$this->u}?site_id={$request['site_id']}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "新增失败!","bakfuntion"=>"dangerFromTip"));
        }
    }
    //商品编辑
    function EditView(){
        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;
        $smarty->assign("act", "Edit");

        $helpOne = $Show_css->getOne("shop_help", "help_id='{$request['id']}'");
        $smarty->assign("dataVar", $helpOne);

        $menulist = $this->Show_css->selectClear("SELECT v.list_name,v.list_parameter FROM cms_variablelist as v WHERE v.variable_id = '3'");
        $this->smarty->assign("menulist",$menulist);

        $this->Viewhtm = $this->router->getController()."/"."Manage.htm";
    }
    //商品编辑
    function EditAction()
    {
        $request = Input('post.','','trim,addslashes');
        $Show_css = $this->Show_css;

        if($request['help_menuname'] == ''){
            ajax_return(array('error' => 1,'errortip' => "菜单名称不能为空!","bakfuntion"=>"warningFromTip"));
        }
        if($request['help_title'] == ''){
            ajax_return(array('error' => 1,'errortip' => "文章名称不能为空!","bakfuntion"=>"warningFromTip"));
        }
        if($request['help_content'] == ''){
            ajax_return(array('error' => 1,'errortip' => "菜单内容不能为空!","bakfuntion"=>"warningFromTip"));
        }

        $data = array();
        $data['list_parameter'] = $request['list_parameter'];
        $data['help_menuname'] = $request['help_menuname'];
        $data['help_title'] = $request['help_title'];
        $data['help_content'] = $request['help_content'];
        $data['help_updatetime'] = time();

        if($Show_css->updateData("shop_help","help_id = '{$request['help_id']}'",$data)){
            ajax_return(array('error' => 0,'errortip' => "修改成功!","bakfuntion"=>"successFromTip","bakurl"=>"/{$this->u}?site_id={$request['site_id']}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "修改失败!","bakfuntion"=>"dangerFromTip"));
        }
    }
    //删除
    function DelAction()
    {
        $request = Input('get.','','trim,addslashes');
        $goods_id = Input('get.id',0);

        if($this->Show_css->delData('shop_help',"help_id='{$goods_id}'")){
            $this->Recordweblog($request['site_id'], $this->Module['module_id'], $this->c, "微商城帮助中心，文章ID:{$goods_id}");
            ajax_return(array('error' => 0,'errortip' => "删除成功!","bakfuntion"=>"okmotify","bakurl"=>"/{$this->u}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "删除失败!","bakfuntion"=>"errormotify"));
        }
    }

    //魔术方法
    public function __call($name, $arguments) {
        $this->smarty->assign("errorTip", "Calling object method '$name' ". implode(', ', $arguments). "\n");
        $this->Viewhtm = "under.htm";
    }
    //魔术函数
    function __destruct()
    {
        $site_id = Input('get.site_id',0);

        if ($this->c =='Wait') {
            $this->smarty->assign("u", $this->router->getController());
            $this->smarty->assign("t", $this->router->getUrl());
            $this->smarty->assign("Viewhtm", $this->Viewhtm);
            $websites = $this->Show_css->getOne("cms_websites","site_id='{$site_id}'");
            $this->smarty->assign("websites",$websites);
            if($site_id){
                if($this->UserLogin['user_type'] == '1' and $this->UserLogin['user_limitsinc'] == '1'){
                    $moduleList = $this->Show_css->select("SELECT m.* FROM cms_module as m LEFT JOIN cms_moduleapply as a ON a.module_id = m.module_id WHERE a.site_id = '{$site_id}' ORDER BY m.module_weight ASC ");
                }else{
                    $moduleList = $this->Show_css->select("SELECT m.* FROM cms_module as m LEFT JOIN cms_moduleapply as a ON a.module_id = m.module_id LEFT JOIN cms_websites_func as c ON c.site_id = a.site_id and c.module_id = a.module_id WHERE a.site_id = '{$site_id}' and c.user_id = '{$this->UserLogin['user_id']}' ORDER BY m.module_weight ASC ");
                }
                // print_r($moduleList);die;
                $this->smarty->assign("moduleList", $moduleList);
                $this->display("websiteindex.htm");
            }else{
                $this->display("index.htm");
            }
        }
        exit;
    }

}
