<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohist.cn 
 * 网站地址 : http://www.mohist.cn
 * <AUTHOR> Zhu<PERSON>
 * Date: 2016/12/19
 * Time: 14:21
 */

namespace Work\Controller\Manage;


class TagsController extends viewTpl{
    public $data;
    public $iuser;
    public $u;
    public $t;
    public $c;
    public $Viewhtm;

    function __construct() {
        parent::__construct ();
        if(!$this->check_login()){
            $this->LoginView();
        }
        $this->smarty->assign("iuser", $this->UserLogin);
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
        $this->Viewhtm = $this->router->getController()."/".$this->router->getUrl().".htm";

        $moduleOne = $this->Show_css->getOne("cms_module","module_mark='{$this->u}'");
        $this->smarty->assign("moduleOne",$moduleOne);
        $this->Module = $moduleOne;
    }
    //主页
    function HomeView()
    {

        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        if(!isset($request['p'])){
            $p = '1';
        }else{
            $p= $request['p'];
        }

        $datawhere = "1";
        $pageurl = "/{$this->u}/{$this->t}?";
        if ($request['site_id']) {
            $pageurl .= "&site_id={$request['site_id']}";
        }

        $datatype = array();
        if(isset($request['keyword']) && $request['keyword'] !==''){
            $datawhere .= " and t.tags_keyword like '%{$request['keyword']}%'";
            $pageurl .="&keyword={$request['keyword']}";
            $datatype['keyword'] = $request['keyword'];
        }
        
        $sql = "SELECT t.* FROM cms_tags as t where {$datawhere} order by t.tags_id ASC ";

        $db_nums = $Show_css->select("SELECT COUNT(*) FROM cms_tags as t where {$datawhere}");//相关条件下的总记录数COUNT(*)
        $allnum = $db_nums[0][0];

        $datalist = $Show_css->dbwherePage($sql,$allnum,'10',$pageurl.'&p=',$p,'10','2');

        $smarty->assign("pagelist",$datalist['pages']);
        $smarty->assign("dataList",$datalist['cont']);
        $smarty->assign("datatype",$datatype);
    }
    
    //增加系统功能
    function AddView()
    {
        $this->smarty->assign("act","Add");
        $this->Viewhtm = $this->router->getController()."/"."Manage.htm";
    }
    //提交处理机制
    function AddAction()
    {
        $request = Input('post.','','trim,addslashes');
        $data = array();

        if($request['tags_keyword'] == ''){
            ajax_return(array('error' => 1,'errortip' => "请填写关键词!","bakfuntion"=>"warningFromTip"));
        }else{
            $res = $this->Show_css->selectOne("select * from cms_tags where tags_keyword='{$request['tags_keyword']}'");
            if($res){
                ajax_return(array('error' => 1,'errortip' => "该关键词已被添加!","bakfuntion"=>"warningFromTip"));
            }
        }

        $data['tags_keyword'] = $request['tags_keyword'];
        $data['tags_weight'] = $request['tags_weight'];
        $data['tags_replacenum'] = $request['tags_replacenum'];
        $data['tags_replaceurl'] = $request['tags_replaceurl'];
        $data['tags_time'] = time();
        if($this->Show_css->insertData("cms_tags",$data)){
            $this->Recordweblog($request['site_id'],$this->Module['module_id'],$this->c,"新增标签数据");
            ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"successFromTip","bakurl"=>"/{$this->u}?site_id={$request['site_id']}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "新增失败!","bakfuntion"=>"dangerFromTip"));
        }
    }
    //增加系统功能
    function EditView()
    {

        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        $usersOne = $Show_css->getOne("cms_tags", "tags_id='{$request['id']}'");
        $smarty->assign("dataVar", $usersOne);

        $smarty->assign("act", "Edit");

        $this->Viewhtm = $this->router->getController()."/"."Manage.htm";
    }
    //提交处理机制
    function EditAction()
    {
        $request = Input('post.','','trim,addslashes');
        $data = array();
        if($request['tags_keyword'] == ''){
            ajax_return(array('error' => 1,'errortip' => "请填写关键词!","bakfuntion"=>"warningFromTip"));
        }else{
            $res = $this->Show_css->selectOne("select * from cms_tags where tags_keyword='{$request['tags_keyword']}' and tags_id<>{$request['tags_id']}");
            if($res){
                ajax_return(array('error' => 1,'errortip' => "该关键词已被占用!","bakfuntion"=>"warningFromTip"));
            }
        }

        $data['tags_keyword'] = $request['tags_keyword'];
        $data['tags_weight'] = $request['tags_weight'];
        $data['tags_replacenum'] = $request['tags_replacenum'];
        $data['tags_replaceurl'] = $request['tags_replaceurl'];
        $data['tags_time'] = time();
        if($this->Show_css->updateData("cms_tags","tags_id = '{$request['tags_id']}'",$data)){
            $this->Recordweblog($request['site_id'],$this->Module['module_id'],$this->c,"修改标签数据，标签ID:{$request['tags_id']}");
            ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"okmotify","bakurl"=>"/{$this->u}?site_id={$request['site_id']}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "新增失败!","bakfuntion"=>"errormotify"));
        }
    }
    //提交处理机制
    function DelAction()
    {
        $tags_id = Input('get.id',0);
        $request = Input('get.','','trim,addslashes');
        if($this->Show_css->delData('cms_tags',"tags_id='{$tags_id}'")){
            $this->Recordweblog($request['site_id'],$this->Module['module_id'],$this->c,"删除标签数据，ID:{$tags_id}");
            ajax_return(array('error' => 0,'errortip' => "删除成功!","bakfuntion"=>"okmotify"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "新增失败!","bakfuntion"=>"errormotify"));
        }
    }

    //魔术方法
    public function __call($name, $arguments) {
        $this->smarty->assign("errorTip", "Calling object method '$name' ". implode(', ', $arguments). "\n");

        $this->Viewhtm = "under.htm";
    }
    //魔术方法
    function __destruct()
    {
        $site_id = Input('get.site_id',0);

        if ($this->c =='Wait') {
            $this->smarty->assign("u", $this->router->getController());
            $this->smarty->assign("t", $this->router->getUrl());
            $this->smarty->assign("Viewhtm", $this->Viewhtm);
            if($site_id){
                $websites = $this->Show_css->getOne("cms_websites","site_id='{$site_id}'");
                $this->smarty->assign("websites",$websites);

                if($this->UserLogin['user_type'] == '1' and $this->UserLogin['user_limitsinc'] == '1'){
                    $moduleList = $this->Show_css->select("SELECT m.* FROM cms_module as m LEFT JOIN cms_moduleapply as a ON a.module_id = m.module_id WHERE a.site_id = '{$site_id}' ORDER BY m.module_weight ASC ");
                }else{
                    $moduleList = $this->Show_css->select("SELECT m.* FROM cms_module as m LEFT JOIN cms_moduleapply as a ON a.module_id = m.module_id LEFT JOIN cms_websites_func as c ON c.site_id = a.site_id and c.module_id = a.module_id WHERE a.site_id = '{$site_id}' and c.user_id = '{$this->UserLogin['user_id']}' ORDER BY m.module_weight ASC ");
                }
                $this->smarty->assign("moduleList", $moduleList);
                $this->display("websiteindex.htm");
            }else{
                $this->display("index.htm");
            }
        }
        exit;
    }

}
