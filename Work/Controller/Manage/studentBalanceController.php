<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn 
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2016/12/16
 * Time: 11:51
 */

namespace Work\Controller\Manage;


class studentBalanceController extends viewTpl{
    public $data;
    public $u;
    public $t;
    public $c;
    public $iuser;
    public $Viewhtm;

    //预加载处理类
    function __construct() {
        parent::__construct ();
        if(!$this->check_login()){
            $this->LoginView();
        }
        if($this->check_login()){
            $this->smarty->assign("iuser", $this->UserLogin);
            $this->iuser = $this->UserLogin;
        }
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
        $this->Viewhtm = $this->router->getController()."/".$this->router->getUrl().".htm";


        $moduleOne = $this->Show_css->getOne("cms_module","module_mark='{$this->u}'");
        $this->smarty->assign("moduleOne",$moduleOne);

    }

    //主页
    function HomeView()
    {
        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        if(!isset($request['p'])){
            $p = '1';
        }else{
            $p= $request['p'];
        }

        $datawhere = "b.student_id = d.student_id AND b.school_id = s.school_id AND d.company_id = '8888' AND s.company_id = '8888' AND b.student_balance >0 AND b.companies_id = p.companies_id";
        $pageurl = "/{$this->u}/{$this->t}?";
        if ($request['site_id']) {
            $pageurl .= "&site_id={$request['site_id']}";
        }

        $datatype = array();
        if(isset($request['keyword']) && $request['keyword'] !==''){
            $datawhere .= " and (s.school_cnname LIKE '%{$request['keyword']}%' OR d.student_branch like '%{$request['keyword']}%' or d.student_cnname like '%{$request['keyword']}%')";
            $pageurl .="&keyword={$request['keyword']}";
            $datatype['keyword'] = $request['keyword'];
        }

        $sql = "SELECT p.companies_id,p.companies_cnname,s.school_id,s.school_branch,s.school_cnname,d.student_id,d.student_branch,d.student_cnname,b.student_balance
FROM smc_student_balance as b,smc_student as d,smc_school as s ,gmc_code_companies as p
WHERE {$datawhere} ORDER BY d.student_id DESC";

        $db_nums = $Show_css->selectOne("SELECT COUNT(DISTINCT b.student_id,b.school_id,b.companies_id) AS num
                FROM smc_student_balance as b,smc_student as d,smc_school as s ,gmc_code_companies as p
            WHERE {$datawhere}");//相关条件下的总记录数COUNT(*)

        $allnum = $db_nums['num'];

        $datalist = $Show_css->dbwherePage($sql,$allnum,'10',$pageurl.'&p=',$p,'10','2');

        $smarty->assign("pagelist",$datalist['pages']);//筛选信息

        $smarty->assign("dataList",$datalist['cont']);
        $smarty->assign("datatype",$datatype);
    }

    function peeOrderAddView(){
        $request = Input('get.','','trim,addslashes');
        $schoolOne = $this->Show_css->getFieldOne("smc_school","school_id,school_branch,school_cnname"
            ,"school_id='{$request['school_id']}'");
        $this->smarty->assign("schoolOne",$schoolOne);

        $companiesOne = $this->Show_css->getFieldOne("gmc_code_companies","companies_id,companies_cnname"
            ,"companies_id='{$request['companies_id']}'");
        $this->smarty->assign("companiesOne",$companiesOne);

        $studentOne = $this->Show_css->getFieldOne("smc_student","student_id,student_branch,student_cnname"
            ,"student_id='{$request['student_id']}'");
        $this->smarty->assign("studentOne",$studentOne);

        $balanceOne = $this->Show_css->getFieldOne("smc_student_balance","*"
            ,"student_id='{$request['student_id']}' AND school_id='{$request['school_id']}' AND companies_id='{$request['companies_id']}'");
        $this->smarty->assign("balanceOne",$balanceOne);
        $this->smarty->assign("act", "peeOrderEdit");

    }

    function peeOrderEditAction(){
        $request = Input('post.','','trim,addslashes');

        if(!isset($request['courseList']) || $request['courseList']==''){
            ajax_return(array('error' => 1, 'errortip' => "请输入课程信息", "bakfuntion" => "warningFromTip"));

        }


        $stafferOne=$this->Show_css->getFieldOne("smc_staffer","staffer_id","company_id='8888' and account_class=1");

        $public=array();
        $public['company_id']=8888;
        $public['school_id']=$request['school_id'];
        $public['staffer_id']=$stafferOne['staffer_id'];

        $ChangeModel = new \Model\Smc\ChangeModel($public);
        $RegistrationModel = new \Model\Smc\RegistrationModel($public);

        $courseArray=array();
        $all_price=0;
        $companies_id=0;

        foreach($request['courseList'] as $key=>$course_branch){



            $courseOne=$this->Show_css->getFieldOne("smc_course","course_id,coursecat_id","company_id='8888' and course_branch='{$course_branch}'");

            $sql = "select a.companies_id,b.companies_issupervise
			from smc_school_coursecat_subject a
 			left JOIN gmc_code_companies b ON a.companies_id = b.companies_id 
 			where a.school_id = '{$request['school_id']}' 
 			and a.coursecat_id='{$courseOne['coursecat_id']}' 
 			limit 0,1";
            $companiesOne = $this->Show_css->selectOne($sql);

            if($companies_id==0 ){
                $companies_id=$companiesOne['companies_id'];
            }elseif($companies_id!=$companiesOne['companies_id']){
                ajax_return(array('error' => 1, 'errortip' => "订单主体不一致", "bakfuntion" => "warningFromTip"));
            }

            $pricingOne = $ChangeModel->getCoursePricing($courseOne['course_id'], '8888', $request['school_id']);
            if (!$pricingOne) {
                ajax_return(array('error' => 1, 'errortip' => "选择课程{$course_branch}不在协议内", "bakfuntion" => "warningFromTip"));
            }

            $courseData = array();
            $courseData['agreement_id'] = $pricingOne['agreement_id'];
            $courseData['pricing_id'] = $pricingOne['pricing_id'];
            $courseData['course_id'] = $pricingOne['course_id'];
            $courseData['sellingprice'] = $request['priceList'][$key];
            $courseData['num'] = $request['numberList'][$key];
            $courseData['starttime'] = "";
            $courseData['discount_id'] = "";
            $courseData['market_price'] = "";
            $courseData['deductionmethod'] = 2;
            $courseData['from'] = 3;

            $all_price += $request['priceList'][$key];
            $courseArray[]=$courseData;
        }

        if($companies_id!=$request['companies_id']){

            $sql = "select a.* 
                    from smc_student_balance as a
                    WHERE a.school_id = '{$request['school_id']}' 
                    and a.student_id='{$request['student_id']}' 
                    and a.companies_id='{$request['companies_id']}' 
                    and a.company_id='8888' and a.student_balance>0";
            $stublcOne = $this->Show_css->selectOne($sql);
            if($stublcOne){
                $data=array();
                $data['student_id']=$request['student_id'];
                $data['company_id']='8888';
                $data['staffer_id']=$stafferOne['staffer_id'];
                $data['balance']=($stublcOne['student_balance']>$all_price)?$all_price:$stublcOne['student_balance'];
                $data['from_school_id']=$request['school_id'];
                $data['to_school_id']=$request['school_id'];
                $data['from_companies_id']=$request['companies_id'];
                $data['to_companies_id']=$companies_id;

                $bool=$ChangeModel->transferBalance($data);

                if(!$bool){
                    ajax_return(array('error' => 1, 'errortip' => $ChangeModel->errortip, "bakfuntion" => "warningFromTip"));
                }
            }
        }

        $data = array();
        $data['company_id'] = '8888';
        $data['school_id'] = $request['school_id'];
        $data['staffer_id'] = $stafferOne['staffer_id'];
        $data['student_id'] = $request['student_id'];
        $data['from'] = 'registrationPay';
        $data['is_forward'] = 0;
        $data['is_balance'] = 1;
        $data['price'] = $all_price;
        if ($all_price > 0) {
            $data['list'] = json_encode($courseArray, JSON_UNESCAPED_UNICODE);

            $bool = $RegistrationModel->createOrder($data);

            if(!$bool){
                ajax_return(array('error' => 1, 'errortip' => $RegistrationModel->errortip, "bakfuntion" => "warningFromTip"));
            }else{
                ajax_return(array('error' => 0, 'errortip' => "提交成功!", "bakfuntion" => "successFromTip", "bakurl" => "/{$this->u}?site_id={$request['site_id']}"));
            }
        }
    }


    //魔术方法
    public function __call($name, $arguments) {
        $this->smarty->assign("errorTip", "Calling object method '$name' ". implode(', ', $arguments). "\n");
        $this->Viewhtm = "under.htm";
    }
    //魔术函数
    function __destruct()
    {
        $site_id = Input('get.site_id',0);

        if ($this->c =='Wait') {
            $this->smarty->assign("u", $this->router->getController());
            $this->smarty->assign("t", $this->router->getUrl());
            $this->smarty->assign("Viewhtm", $this->Viewhtm);
            $websites = $this->Show_css->getOne("cms_websites","site_id='{$site_id}'");
            $this->smarty->assign("websites",$websites);
            if($site_id){
                if($this->UserLogin['user_type'] == '1' and $this->UserLogin['user_limitsinc'] == '1'){
                    $moduleList = $this->Show_css->select("SELECT m.* FROM cms_module as m LEFT JOIN cms_moduleapply as a ON a.module_id = m.module_id WHERE a.site_id = '{$site_id}' ORDER BY m.module_weight ASC ");
                }else{
                    $moduleList = $this->Show_css->select("SELECT m.* FROM cms_module as m LEFT JOIN cms_moduleapply as a ON a.module_id = m.module_id LEFT JOIN cms_websites_func as c ON c.site_id = a.site_id and c.module_id = a.module_id WHERE a.site_id = '{$site_id}' and c.user_id = '{$this->UserLogin['user_id']}' ORDER BY m.module_weight ASC ");
                }
                $this->smarty->assign("moduleList", $moduleList);
                $this->display("websiteindex.htm");
            }else{
                $this->display("index.htm");
            }
        }
        exit;
    }

}