<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn 
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2016/12/19
 * Time: 0:42
 */

namespace Work\Controller\Manage;


class SiteManageController extends viewTpl{
    public $data;
    public $iuser;
    public $u;
    public $t;
    public $c;
    public $Viewhtm;

    function __construct() {
        parent::__construct ();
        if(!$this->check_login()){
            $this->LoginView();
        }
        $this->smarty->assign("iuser", $this->UserLogin);
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
        $this->Viewhtm = $this->router->getController()."/".$this->router->getUrl().".htm";
    }

    //主页
    function HomeView()
    {

        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        if(!isset($request['p'])){
            $p = '1';
        }else{
            $p= $request['p'];
        }

        $datawhere = "1";
        $pageurl = "/{$this->u}/{$this->t}?";
        if ($request['site_id']) {
            $pageurl .= "&site_id={$request['site_id']}";
        }

        $datatype = array();
        if(isset($request['keyword']) && $request['keyword'] !==''){
            $datawhere .= " and (w.site_title like '%{$request['keyword']}%' or w.site_url like '%{$request['keyword']}%')";
            $pageurl .="&keyword={$request['keyword']}";
            $datatype['keyword'] = $request['keyword'];
        }

        if(isset($request['type'])){
            $datawhere .= " and w.site_type = '{$request['type']}'";
            $pageurl .="&type={$request['type']}";
            $datatype['type'] = $request['type'];
        }

        $sql = "SELECT w.* FROM cms_websites as w where {$datawhere} order by w.site_weight ASC ";

        $db_nums = $Show_css->select("SELECT COUNT(*) FROM cms_websites as w where {$datawhere}");//相关条件下的总记录数COUNT(*)
        $allnum = $db_nums[0][0];

        $datalist = $Show_css->dbwherePage($sql,$allnum,'10',$pageurl.'&p=',$p,'10','2');

        $smarty->assign("pagelist",$datalist['pages']);
        $smarty->assign("dataList",$datalist['cont']);
        $smarty->assign("datatype",$datatype);
    }

    //增加系统功能
    function AddView()
    {
        $this->smarty->assign("act","Add");
        $this->Viewhtm = $this->router->getController()."/"."Manage.htm";
    }
    //提交处理机制
    function AddAction()
    {
        $request = Input('post.','','trim,addslashes');
        $data = array();

        if($request['site_title'] == '' || $request['site_weight'] == ''){
            ajax_return(array('error' => 1,'errortip' => "表单相关内容未填写完整!","bakfuntion"=>"warningFromTip"));
        }

        $data['site_title'] = $request['site_title'];
        $data['site_url'] = $request['site_url'];
        $data['site_type'] = $request['site_type'];
        $data['site_img'] = $request['site_img'];
        $data['site_manageurl'] = $request['site_manageurl'];
        $data['site_name'] = $request['site_name'];
        $data['site_pswd'] = $request['site_pswd'];
        $data['site_dynamicpswd'] = $request['site_dynamicpswd'];
        $data['site_details'] = $request['site_details'];
        $data['site_weight'] = $request['site_weight'];
        if($this->Show_css->insertData("cms_websites",$data)){
            ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"successFromTip","bakurl"=>"/{$this->u}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "新增失败!","bakfuntion"=>"dangerFromTip"));
        }
    }
    //增加系统功能
    function EditView()
    {

        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        $websitesOne = $Show_css->getOne("cms_websites", "site_id='{$request['id']}'");
        $smarty->assign("dataVar", $websitesOne);

        $smarty->assign("act", "Edit");

        $this->Viewhtm = $this->router->getController()."/"."Manage.htm";
    }
    //提交处理机制
    function EditAction()
    {
        $request = Input('post.','','trim,addslashes');

        if($request['site_title'] == '' || $request['site_weight'] == ''){
            ajax_return(array('error' => 1,'errortip' => "表单相关内容未填写完整!","bakfuntion"=>"warningFromTip"));
        }
        $data = array();
        $data['site_title'] = $request['site_title'];
        $data['site_url'] = $request['site_url'];
        $data['site_type'] = $request['site_type'];
        $data['site_img'] = $request['site_img'];
        $data['site_manageurl'] = $request['site_manageurl'];
        $data['site_name'] = $request['site_name'];
        $data['site_pswd'] = $request['site_pswd'];
        $data['site_dynamicpswd'] = $request['site_dynamicpswd'];
        $data['site_details'] = $request['site_details'];
        $data['site_weight'] = $request['site_weight'];
        if($this->Show_css->updateData("cms_websites","site_id = '{$request['site_id']}'",$data)){
            ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"okmotify","bakurl"=>"/{$this->u}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "新增失败!","bakfuntion"=>"errormotify"));
        }
    }
    //提交处理机制
    function DelAction()
    {
        $site_id = Input('get.id',0);

        if($this->Show_css->delData('cms_websites',"site_id='{$site_id}'")){
            ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"okmotify","bakurl"=>"/{$this->u}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "新增失败!","bakfuntion"=>"errormotify"));
        }
    }
    //魔术方法
    public function __call($name, $arguments) {
        $this->smarty->assign("errorTip", "Calling object method '$name' ". implode(', ', $arguments). "\n");

        $this->Viewhtm = "under.htm";
    }
    //魔术方法
    function __destruct()
    {

        if ($this->c =='Wait') {
            $this->smarty->assign("u", $this->router->getController());
            $this->smarty->assign("t", $this->router->getUrl());
            $this->smarty->assign("Viewhtm", $this->Viewhtm);
            $this->display("index.htm");
        }
        exit;
    }

}