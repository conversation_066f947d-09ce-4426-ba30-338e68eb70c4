<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2018/12/29
 * Time: 10:09
 */

namespace Work\Controller\Posapi;


class LoginController extends viewTpl{
    public $u;
    public $t;
    public $c;

    function __construct() {
        parent::__construct ();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
    }

    //用户获取token
    function getToken($params=array()){
        $stafferOne = $this->DataControl->getFieldOne('smc_staffer',"staffer_id,staffer_tokencode,staffer_tokenencrypt","staffer_id='{$params['staffer_id']}'");
        if(!$stafferOne)
        {
            return false;
        }
        $md5tokenbar = base64_encode(md5($stafferOne["staffer_tokencode"].date("Y-m-d")));
        if($md5tokenbar == $stafferOne["staffer_tokenencrypt"]){
            $token = $stafferOne["staffer_tokenencrypt"];
        }else{
            //目前这里注释是为了测试方便
            $tokencode = rand(111111,999999);
            $md5tokenbar = base64_encode(md5($tokencode.date("Y-m-d")));
            $this->DataControl->query("UPDATE smc_staffer SET staffer_tokencode = '{$tokencode}',staffer_tokenencrypt = '{$md5tokenbar}' WHERE staffer_id ='{$stafferOne['staffer_id']}'");
            $token = $md5tokenbar;
//            $token = $stafferOne["staffer_tokenencrypt"];
        }
        return $token;
    }

    //校务系统登录操作
    function kddlogin($request){
        $companyOne = $this->DataControl->getFieldOne('gmc_company',"company_id","company_code = '{$request['L_code']}'");
        if($companyOne){
            $stafferOne = $this->DataControl->getFieldOne('smc_staffer',"staffer_id,staffer_leave,staffer_pass,account_class","(staffer_branch = '{$request['L_name']}' or staffer_mobile = '{$request['L_name']}') and company_id = '{$companyOne['company_id']}'");
            if($stafferOne){
                if($stafferOne['staffer_leave'] == '0'){
                    $password = md5($request['L_pswd']);
                    if ($password == $stafferOne['staffer_pass']) {
                        $istaffer = array();
                        $istaffer['staffer_id'] = $stafferOne['staffer_id'];
                        $istaffer['company_id'] = $companyOne['company_id'];
                        if($stafferOne['account_class'] == '1'){
                            $schoolOne = $this->DataControl->getFieldOne('smc_school',"school_id","company_id = '{$companyOne['company_id']}'","order by school_istemp DESC");
                            $istaffer['school_id'] = $schoolOne['school_id'];
                        }else{
                            $schoolOne = $this->DataControl->selectOne("select sp.school_id,sp.postpart_id,sp.post_id
                                FROM gmc_staffer_postbe as sp
                                where sp.staffer_id = '{$stafferOne['staffer_id']}' and sp.school_id <> '0' and sp.postbe_status = '1'
                                order by sp.postbe_ismianjob DESC,sp.postbe_ismianjob DESC limit 0,1 ");
                            if($schoolOne && $schoolOne['postpart_id'] == '0'){
                                $postOne = $this->DataControl->getFieldOne('gmc_company_post',"postpart_id","post_id = '{$schoolOne['post_id']}'","order by post_id DESC limit 0,1");
                                if($postOne['postpart_id'] !=='0'){
                                    $this->DataControl->updateData("gmc_staffer_postbe", "staffer_id = '{$stafferOne['staffer_id']}' and school_id = '{$schoolOne['school_id']}' and post_id = '{$schoolOne['post_id']}'", array("postpart_id" => $postOne['postpart_id']));
                                }
                            }

                            $istaffer['school_id'] = $schoolOne['school_id']?$schoolOne['school_id']:0;

                            if($schoolOne['school_id'] =='0'){
                                $schoolOne = $this->DataControl->selectOne("select sp.school_id
                                FROM gmc_staffer_postbe as sp
                                where sp.staffer_id = '{$stafferOne['staffer_id']}' and sp.school_id <> '0' and sp.postbe_status = '1'
                                order by sp.postbe_ismianjob DESC limit 0,1 ");
                                $istaffer['school_id'] = $schoolOne['school_id']?$schoolOne['school_id']:0;
                            }
                            if($schoolOne['school_id'] ==''){
                                $schoolOne = $this->DataControl->selectOne("select s.school_id FROM gmc_staffer_postbe as sp,gmc_company_organizeschool as s
where s.organize_id = sp.organize_id and sp.staffer_id = '{$stafferOne['staffer_id']}' and sp.school_id = '0' and sp.postbe_status = '1' order by sp.postbe_ismianjob DESC limit 0,1 ");
                                $istaffer['school_id'] = $schoolOne['school_id'];
                            }
                        }
                        $istaffer['token'] = $this->getToken($stafferOne);
                        $this->DataControl->updateData("smc_staffer", "staffer_id = '{$stafferOne['staffer_id']}'", array("staffer_lasttime" => time(), "staffer_lastip" => real_ip()));
                        ajax_return(array('error' => 0, 'errortip' => "登陆成功!", 'result' => $istaffer));
                    }else{
                        $result = array();
                        $result['staffer_id'] = '0';
                        $result['company_id'] = '0';
                        $result['school_id'] = '0';
                        $result['token'] = '0';
                        ajax_return(array('error' => 1,'errortip' => "密码错误!", 'result' => $result));
                    }
                }else{
                    $result = array();
                    $result['staffer_id'] = '0';
                    $result['company_id'] = '0';
                    $result['school_id'] = '0';
                    $result['token'] = '0';
                    ajax_return(array('error' => 1,'errortip' => "您已离职，账户权限已被关闭!", 'result' => $result));
                }
            }else{
                $result = array();
                $result['staffer_id'] = '0';
                $result['company_id'] = '0';
                $result['school_id'] = '0';
                $result['token'] = '0';
                ajax_return(array('error' => 1,'errortip' => "职工账户信息不存在!", 'result' => $result));
            }
        }else{
            $result = array();
            $result['staffer_id'] = '0';
            $result['company_id'] = '0';
            $result['school_id'] = '0';
            $result['token'] = '0';
            ajax_return(array('error' => 1,'errortip' => "企业账户不存在，请确认授权码是否正确!", 'result' => $result));
        }
    }


    function pclogin($request){
        $schoolOne = $this->PtCenterData->getFieldOne('ptc_school',"school_id,school_passwd","school_branch = '{$request['L_name']}'");
        if($schoolOne){
            if($schoolOne['school_passwd'] == $request['L_pswd']){
                $result = array();
                $result['staffer_id'] = $schoolOne['school_id'];
                $result['company_id'] = '666';
                $result['school_id'] = $schoolOne['school_id'];
                $result['token'] = md5($schoolOne['school_passwd']);
                ajax_return(array('error' => 0, 'errortip' => "登陆成功!", 'result' => $result));
            }else{
                $result = array();
                $result['staffer_id'] = '0';
                $result['company_id'] = '0';
                $result['school_id'] = '0';
                $result['token'] = '0';
                ajax_return(array('error' => 1,'errortip' => "管理密码错误，请检查密码正确性!", 'result' => $result));
            }
        }else{
            $result = array();
            $result['staffer_id'] = '0';
            $result['company_id'] = '0';
            $result['school_id'] = '0';
            $result['token'] = '0';
            ajax_return(array('error' => 1,'errortip' => "校区编号不正确，请检查校区编号正确性!", 'result' => $result));
        }
    }

    //账户密码登陆
    function pswdloginApi(){
        $request = Input('post.','','trim,addslashes');
        if($request['L_code'] == '666'){
            $this->pclogin($request);
        }else{
            $this->kddlogin($request);
        }
    }
}
