<?php


namespace Work\Controller\Posapi;


class AppRefundController extends viewTpl
{
	public $u;
	public $t;
	public $c;
	
	function __construct()
	{
		parent::__construct();
		$this->u = $this->router->getController();
		$this->t = $this->router->getUrl();
		$this->c = $this->router->getAction();
	}

    function KddAppRefund($request){
        if ($request['keyword'] == "") {
            $result = array();
            $result['field'] = array();
            $result['list'] = array();
            $res = array('error' => 1, 'errortip' => "请输入关键字", 'result' => $result);
            ajax_return($res);
        }

        $this->ThisVerify($request);
        $AppRefundModel = new \Model\Smc\AppRefundModel();
        $list = $AppRefundModel->getRefundList($request);

        $field = array();
        $field[0]["fieldstring"] = "order_id";
        $field[0]["fieldname"] = "ID";
        $field[0]["show"] = 0;
        $field[0]["custom"] = 0;

        $field[1]["fieldstring"] = "order_pid";
        $field[1]["fieldname"] = "订单编号";
        $field[1]["show"] = 1;
        $field[1]["custom"] = 1;

        $field[2]["fieldstring"] = "student_cnname";
        $field[2]["fieldname"] = "学员中文名";
        $field[2]["show"] = 1;
        $field[2]["custom"] = 1;

        $field[3]["fieldstring"] = "student_enname";
        $field[3]["fieldname"] = "学员英文名";
        $field[3]["show"] = 1;
        $field[3]["custom"] = 1;
        $result['field'] = $field;

        $result['list'] = $list;

        $res = array('error' => 0, 'errortip' => "获取成功", 'result' => $result);

        ajax_return($res);
    }

    function PcAppRefund($request){
        $this->PcVerify($request);
        if ($request['keyword'] == "") {
            $result = array();
            $result['field'] = array();
            $result['list'] = array();
            $res = array('error' => 1, 'errortip' => "请输入关键字", 'result' => $result);
            ajax_return($res);
        }

        $startDay = strtotime(date("Y-m-d"),time()) ;
        $datawhere = "o.order_pid like '%{$request['keyword']}%' and o.school_id ='{$request['school_id']}' and o.order_paytime >='{$startDay}' and (o.order_paychannel = '2' or o.order_paychannel = '4')";
        $shopOrderList = $this->PtCenterData->selectClear("SELECT o.order_id,o.order_pid,o.student_id,o.order_addtime,o.order_paymentprice,o.order_ispay FROM ptc_shop_order as o WHERE {$datawhere}");
        $shopOrderArray = array();
        if($shopOrderList){
            foreach($shopOrderList as $orderArray){
                $orderOne = array();
                $orderOne['order_id'] = $orderArray['order_id'];
                $orderOne['order_pid'] = $orderArray['order_pid'];
                $studentOne = $this->PtCenterData->getOne('ptc_student',"student_id = '{$orderArray['student_id']}'");
                $orderOne['student_cnname'] = $studentOne['cnname'];
                $orderOne['student_enname'] = $studentOne['enname'];
                $orderOne['student_branch'] = $studentOne['branch'];
                $orderOne['order_createtime'] = $orderArray['order_addtime'];
                $payList = array();
                $order = array();
                $surplusprice = $orderArray['order_paymentprice']-($orderArray['order_paymentprice']*$orderArray['order_ispay']);
                $order['order_surplusprice'] = sprintf("%01.2f",$surplusprice);
                $order['order_paymentprice'] = $orderArray['order_paymentprice'];
                $paidprice = $orderArray['order_paymentprice']*$orderArray['order_ispay'];
                $order['order_paidprice'] = sprintf("%01.2f",$paidprice);
                $payList['order'] = $order;
                $paylogOne = $this->PtCenterData->getOne('ptc_shop_paylog',"order_pid = '{$orderArray['order_pid']}'");
                $payOne = array();
                $payOne['pay_id'] = $paylogOne['paylog_id'];
                $payOne['pay_pid'] = $paylogOne['order_pid'];
                $payOne['pay_typename'] = $paylogOne['paylog_interfaceno'];
                $payOne['pay_successtime'] = $paylogOne['paylog_paytime'];
                $payOne['pay_issuccess'] = '已支付';
                $payOne['pay_price'] = $paylogOne['paylog_actualprice'];
                $payOne['order_pid'] = $paylogOne['order_pid'];
                $payOne['student_cnname'] = $studentOne['cnname'];
                $payOne['student_enname'] = $studentOne['enname'];
                $payOne['paylog_tradeno'] = $paylogOne['paylog_channeltradeno'];
                $payOne['pay_isrefund'] = '0';
                $payOne['pay_note'] = '';
                $payOne['pay_outnumber'] = $paylogOne['paylog_channeltradeno'];
                $payOne['pay_status'] = '1';
                $payList['list'][] = $payOne;
                $orderOne['payList'] = $payList;
                $shopOrderArray[] = $orderOne;
            }
        }



        $field = array();
        $field[0]["fieldstring"] = "order_id";
        $field[0]["fieldname"] = "ID";
        $field[0]["show"] = 0;
        $field[0]["custom"] = 0;

        $field[1]["fieldstring"] = "order_pid";
        $field[1]["fieldname"] = "订单编号";
        $field[1]["show"] = 1;
        $field[1]["custom"] = 1;

        $field[2]["fieldstring"] = "student_cnname";
        $field[2]["fieldname"] = "学员中文名";
        $field[2]["show"] = 1;
        $field[2]["custom"] = 1;

        $field[3]["fieldstring"] = "student_enname";
        $field[3]["fieldname"] = "学员英文名";
        $field[3]["show"] = 1;
        $field[3]["custom"] = 1;
        $result['field'] = $field;
        $result['list'] = $shopOrderArray;
        $res = array('error' => 0, 'errortip' => "获取成功", 'result' => $result);
        ajax_return($res);
    }
	
	function HomeView()
	{
		$request = Input('get.','','trim,addslashes');
        if($request['company_id'] == '666'){
            $this->PcAppRefund($request);
        }else{
            $this->KddAppRefund($request);
        }
	}


    function KddreFundSuccess($request){
        $this->ThisVerify($request);
        $AppRefundModel = new \Model\Smc\AppRefundModel();
        $bool = $AppRefundModel->reFundSuccess($request);
        if($bool){
            $res = array('error' => 0, 'errortip' => "调整成功", 'result' => array());
        }else{
            $res = array('error' => 1, 'errortip' => $AppRefundModel->errortip, 'result' => array());
        }

        $json_play = new \Webjson();
        $errorData['errorlog_apiurl'] = $this->u.'/'.$this->c;
        $errorData['errorlog_class'] = 0 ;
        $errorData['errorlog_parameter'] =$json_play->encode($request) ;
        $errorData['errorlog_bakjson'] =$json_play->encode($res) ;
        $errorData['errorlog_time'] =time() ;

        $this->DataControl->insertData("smc_api_errorlog",$errorData);
        ajax_return($res);
    }

    function PcreFundSuccess($request){
        $this->PcVerify($request);

        $parameter = array();
        $parameter['token'] = $request['token'];
        $parameter['company_id'] = $request['company_id'];
        $parameter['school_id'] = $request['school_id'];
        $parameter['trade_outnumber'] = $request['trade_outnumber'];
        $parameter['order_pid'] = $request['order_pid'];
        $sendApiSting = request_by_curl("https://ptcapi.kidcastle.com.cn/BoingPay/Ylrefundbak",dataEncode($parameter),"GET",array());
        $sendApiArray = json_decode($sendApiSting,"1");
        if($sendApiArray['error'] == '0'){
            $res = array('error' => 0, 'errortip' => $sendApiArray['errortip'], 'result' => array());
        } else {
            $res = array('error' => 1, 'errortip' => $sendApiArray['errortip'], 'result' => array());
        }

        $json_play = new \Webjson();
        $errorData['errorlog_apiurl'] = $this->u.'/'.$this->c;
        $errorData['errorlog_class'] = 0 ;
        $errorData['errorlog_parameter'] =$json_play->encode($request) ;
        $errorData['errorlog_bakjson'] = $json_play->encode($res) ;
        $errorData['errorlog_time'] =time() ;
        $this->DataControl->insertData("smc_api_errorlog",$errorData);
        ajax_return($res);

    }
	
	function reFundSuccessAction()
	{
		$request = Input('post.','','trim,addslashes');
        if($request['company_id'] == '666'){
            $this->PcreFundSuccess($request);
        }else{
            $this->KddreFundSuccess($request);
        }
	}
}