<?php


namespace Work\Controller\Posapi;


class OrderController extends viewTpl{
	public $u;
	public $t;
	public $c;
	
	function __construct() {
		parent::__construct ();
		$this->u = $this->router->getController();
		$this->t = $this->router->getUrl();
		$this->c = $this->router->getAction();
	}

    function KddorderList($request){
        $this->ThisVerify($request);
        $OrderModel = new \Model\Smc\OrderModel();
        $res = $OrderModel->orderList($request);

        if($res){
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        }else{

            $result = array();
            $result['list'] = array();
            $res = array('error' => 1, 'errortip' => $OrderModel->errortip, 'result' => $result);
        }
        ajax_return($res);
    }

    function PcorderList($request){
        $this->PcVerify($request);
        $datawhere = "o.student_id = s.student_id and o.school_id = '{$request['school_id']}' and o.order_status > '0'";

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        if(isset($request['keyword']) && $request['keyword'] !== ''){
            $datawhere .= " and (s.cnname like '%{$request['keyword']}%' or s.enname like '%{$request['keyword']}%' or s.branch like '%{$request['keyword']}%' or o.order_pid like '%{$request['keyword']}%')";
        }
        if(isset($request['order_status']) && $request['order_status'] == 1){
            $datawhere .= " and o.order_status = '3'";
        }else{
            $datawhere .= " and o.order_status <> '3'";
        }

        $ordersql = "SELECT o.order_pid AS order_pid,o.order_addtime as order_createtime, s.cnname AS student_cnname, s.enname AS student_enname
, s.branch AS student_branch, o.order_status AS order_status, o.order_paymentprice AS order_paymentprice
, o.order_ispay*o.order_paymentprice as order_paidprice, o.order_paymentprice-(o.order_ispay*o.order_paymentprice) AS arrears
FROM ptc_shop_order AS o, ptc_student AS s WHERE {$datawhere}
order by o.order_addtime desc limit {$pagestart},{$num}";
        $orderList = $this->PtCenterData->selectClear($ordersql);
        if($orderList){
            $order_status = array("0"=>"待确认", "1"=>"待支付", "2"=>"处理中", "3"=>"已完成", "-1"=>"已取消");
            foreach($orderList as &$orderOne){
                $orderOne['order_createtime']=date("Y-m-d H:i:s",$orderOne['order_createtime']);
                $orderOne['order_status_name']=$order_status[$orderOne['order_status']];
            }
            $result["list"] = $orderList;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        }else{
            $result = array();
            $result['list'] = array();
            $res = array('error' => 1, 'errortip' => '未检索到任何订单', 'result' => $result);
        }
        ajax_return($res);
    }

	
	function orderListApi(){
		$request = Input('get.','','trim,addslashes');
        $data=array();
        $data['errorlog_apiurl']="{$request['u']}/{$request['api']}{$request['t']}";
        $data['errorlog_parameter']= @mysql_escape_string(http_build_query($request));
        $this->DataControl->insertData("smc_api_errorlog",$data);
        if($request['company_id'] == '666'){
            $this->PcorderList($request);
        }else{
            $this->KddorderList($request);
        }
	}
	
}
