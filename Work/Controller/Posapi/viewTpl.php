<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong <PERSON>
 * Date: 2016/12/13
 * Time: 21:21
 */
namespace Work\Controller\Posapi;


class viewTpl {
    public $router;

    public function __construct(){
        global $router;
        // 指定允许其他域名访问
        header('Access-Control-Allow-Origin:*');
        // 响应类型
        header('Access-Control-Allow-Methods:*');
        // 响应头设置
        header('Access-Control-Allow-Headers:x-requested-with,content-type');

        //数据库操作
        $this->DataControl = new \Dbmysql();
        //数据库操作
        $this->PtCenterData = new \Dbsqlplay("rm-uf6p6o8a723201r5ro.mysql.rds.aliyuncs.com","mohism","JDBXL2016Mohism","jdbwebmange");
        //操作类型
        $this->router = $router;
    }
	
	function createOrderPid($initial){
		$Str = "ABCDEFGHIJKLMNOPQRSTUVWXYZ123456789";
		$rangtr = $Str[rand(0,34)].$Str[rand(0,34)].$Str[rand(0,34)].$Str[rand(0,34)];
		$rangtime = date("ymdHis",time());
		$rangnum = rand(10000,99999);
		$OrderPID = $initial.$rangtr.$rangtime.$rangnum;
		return $OrderPID;
	}
	
	function ThisVerify($request){
        $data=array();
        $data['errorlog_apiurl']="{$request['u']}/{$request['api']}{$request['t']}";
        $data['errorlog_parameter']= @mysql_escape_string(http_build_query($request));
        $this->DataControl->insertData("smc_api_errorlog",$data);


		$paramArray = array();
		$paramArray['staffer_id'] = $request['staffer_id'];
		$paramArray['school_id'] = $request['school_id'];
		$paramArray['company_id'] = $request['company_id'];
		$paramArray['token'] = $request['token'];
		if(!$this->UserLimit($paramArray)){
			$result = array();
			$result["list"] = array();
			$result["tokeninc"] = "0";
			$res = array('error' => 1, 'errortip' => "用户token失效", 'result' => $result);
			ajax_return($res);
		}
	}

    function PcVerify($request){
        $data=array();
        $data['errorlog_apiurl']="{$request['u']}/{$request['api']}{$request['t']}";
        $data['errorlog_parameter']= @mysql_escape_string(http_build_query($request));
        $this->DataControl->insertData("smc_api_errorlog",$data);
        $schoolView = $this->PtCenterData->getFieldOne('ptc_school',"school_id,school_passwd,school_cnname,school_branch","school_id = '{$request['school_id']}'");
        if(!$schoolView){
            $result = array();
            $result["list"] = array();
            $result["tokeninc"] = "0";
            $res = array('error' => 1, 'errortip' => "学校ID错误", 'result' => $result);
            ajax_return($res);
        }
        if(md5($schoolView['school_passwd']) !== $request['token']){
            print_r("11111");
            $result = array();
            $result["list"] = array();
            $result["tokeninc"] = "0";
            $res = array('error' => 1, 'errortip' => "用户token失效".$request['token'], 'result' => $result);
            ajax_return($res);
        }
    }
	
    
    //第三方接口权限验证
    function UserLimit($paramArray){
        $stafferOne = $this->DataControl->getFieldOne('smc_staffer',"staffer_id,staffer_leave,staffer_pass,account_class","company_id = '{$paramArray['company_id']}' and staffer_id='{$paramArray['staffer_id']}'");
        if($stafferOne['account_class'] == '1'){
            $sql="select s.staffer_id,s.staffer_cnname,s.staffer_branch,s.staffer_pass,s.staffer_mobile,s.staffer_tokencode
                                                      from smc_staffer as s
                                                      where s.company_id='{$paramArray['company_id']}' and s.staffer_id='{$paramArray['staffer_id']}' limit 0,1 ";
        }else{
            $sql="select s.staffer_id,s.staffer_cnname,s.staffer_branch,s.staffer_pass,sp.school_id,s.staffer_mobile,s.staffer_tokencode
                                                      from smc_staffer as s
                                                      left join gmc_staffer_postbe as sp on s.staffer_id=sp.staffer_id
                                                      where sp.company_id='{$paramArray['company_id']}'  and s.staffer_id='{$paramArray['staffer_id']}' limit 0,1 ";
        }

        $apiuser = $this->DataControl->selectOne($sql);
        if($apiuser){
            $md5tokenbar = base64_encode(md5($apiuser["staffer_tokencode"].date("Y-m-d")));
            if($md5tokenbar != $paramArray['token']){
                return false;
            }else{
                return true;
            }
        }else{
            return false;
        }
    }

    public function __call($method, $args) {
        echo "unknown method " . $method;
        return false;

    }
}
