<?php


namespace Work\Controller\Posapi;


class OrderPayController extends viewTpl
{
    public $u;
    public $t;
    public $c;

    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
    }

    function KddstuOrderOne($request)
    {
        $orderHandleModel = new  \Model\Smc\OrderPayModel($request, $request['order_pid']);
        $this->ThisVerify($request);

        $order = $orderHandleModel->stuOrderOne();
        $fieldstring = array('student_cnname', 'student_enname', 'student_branch', 'order_pid', 'order_createtime', 'order_taglist', 'order_allprice', 'order_balance_price', 'order_forward_price', 'order_coupon_price', 'order_surplusprice', 'order_status');
        $fieldname = array('学员中文名', '学员英文名', '学员编号', '订单编号', '下单时间', '订单标签', '订单总价', '余额抵扣', '结转金额抵扣', '优惠券抵扣', '剩余支付金额', '订单状态');
        $fieldcustom = array('1', "1", "1", "1", "1", "1", "1", "1", "1", "1", '1', '0');
        $fieldshow = array('1', "1", "1", "1", "1", "1", "1", "1", "1", "1", '1', '0');
        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["fieldname"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }
        $result = array();
        $result['fieldcustom'] = 1;
        $result['field'] = $field;

        $result['all_sign'] = $order['all_sign'];
        $result['list'] = ($order['list'] == "") ? array() : $order['list'];
        $result['course'] = ($order['order_goods']['course'] == "") ? array() : $order['order_goods']['course'];
        $result['items'] = ($order['order_goods']['items'] == "") ? array() : $order['order_goods']['items'];
        $result['common_items'] = ($order['order_goods']['common_items'] == "") ? array() : $order['order_goods']['common_items'];
        $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
        ajax_return($res);
    }

    function PcstuOrderOne($request)
    {
        $this->PcVerify($request);

        $fieldstring = array('student_cnname', 'student_enname', 'student_branch', 'order_pid', 'order_createtime', 'order_taglist', 'order_allprice', 'order_balance_price', 'order_forward_price', 'order_coupon_price', 'order_surplusprice', 'order_status');
        $fieldname = array('学员中文名', '学员英文名', '学员编号', '订单编号', '下单时间', '订单标签', '订单总价', '余额抵扣', '结转金额抵扣', '优惠券抵扣', '剩余支付金额', '订单状态');
        $fieldcustom = array('1', "1", "1", "1", "1", "1", "1", "1", "1", "1", '1', '0');
        $fieldshow = array('1', "1", "1", "1", "1", "1", "1", "1", "1", "1", '1', '0');
        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["fieldname"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }
        $result = array();
        $result['fieldcustom'] = 1;
        $result['field'] = $field;

        $orderOne = array();
        $orderArray = $this->PtCenterData->getOne('ptc_shop_order', "order_pid = '{$request['order_pid']}'");
        $orderOne['order_pid'] = $orderArray['order_pid'];
        $orderOne['order_createtime'] = date("Y-m-d H:i:s", $orderArray['order_addtime']);
        $orderOne['order_allprice'] = $orderArray['order_allprice'];
        $orderOne['order_market_price'] = $orderArray['order_allprice'];
        $orderOne['order_paymentprice'] = $orderArray['order_paymentprice'];
        $studentOne = $this->PtCenterData->getOne('ptc_student', "student_id = '{$orderArray['student_id']}'");
        $orderOne['student_cnname'] = $studentOne['cnname'];
        $orderOne['student_enname'] = $studentOne['enname'];
        $orderOne['student_branch'] = $studentOne['branch'];
        $orderOne['order_taglist'] = '校务,家长中心';
        $orderOne['order_paidprice'] = $orderArray['order_paymentprice'] * $orderArray['order_ispay'];
        $orderOne['student_balance'] = 0;
        $orderOne['student_forwardprice'] = 0;
        $orderOne['order_coupon_price'] = $orderArray['order_couponmoney'];
        $orderOne['order_type'] = 1;
        $orderOne['order_status'] = 0;
        $orderOne['order_balance_price'] = $orderArray['order_balanceprice'];
        $orderOne['order_forward_price'] = 0;
        $orderOne['order_surplusprice'] = $orderArray['order_paymentprice'] - ($orderArray['order_paymentprice'] * $orderArray['order_ispay']);
        $orderOne['order_activity_price'] = $orderArray['order_balanceprice'];
        $orderGoods = array();
        $goodsList = $this->PtCenterData->selectClear("SELECT
m.models_name as item_name,
m.models_number as course_branch,
o.models_buynums as item_buynums,
m.models_originalprice as item_reprice,
o.models_unitprice as items_sellingprice,
o.ordergoods_price as item_totalprice
  FROM ptc_shop_ordermodels as o,ptc_models as m WHERE m.models_id = o.models_id and o.order_pid = '{$orderArray['order_pid']}'");
        if ($goodsList) {
            foreach ($goodsList as $goodsOne) {
                $goodsOne['items_donatepiece'] = 0;
                $goodsOne['goods'] = array();
                $goodsOne['items'] = array();
                $orderGoods[] = $goodsOne;
            }
        }

        $result['list'] = !$orderOne ? array() : $orderOne;
        $result['course'] = !$orderGoods ? array() : $orderGoods;
        $result['items'] = array();
        $result['common_items'] = array();
        $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
        ajax_return($res);
    }

    //获取订单信息
    function stuOrderOneApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $data = array();
        $data['errorlog_apiurl'] = "{$request['u']}/{$request['api']}{$request['t']}";
        $data['errorlog_parameter'] = @mysql_escape_string(http_build_query($request));
        $this->DataControl->insertData("smc_api_errorlog", $data);
        if ($request['company_id'] == '666') {
            $this->PcstuOrderOne($request);
        } else {
            $this->KddstuOrderOne($request);
        }

    }


    function KddorderPayList($request)
    {
        $this->ThisVerify($request);

        $orderHandleModel = new  \Model\Smc\OrderPayModel($request, $request['order_pid']);

        $from = 0;
        $payList = $orderHandleModel->OrderPayList($request, $request['app_school_id'], $request['pay_status'], $from);

        $fieldstring = array('pay_pid', 'pay_typename', 'pay_price', 'pay_issuccess', 'pay_successtime', 'pay_outnumber', 'pay_note');
        $fieldname = array('支付编号', '支付方式', '支付金额', '支付状态', '支付时间', '外部交易编号', '备注');
        $fieldcustom = array('1', "1", "1", "1", "1", "1", "1");
        $fieldshow = array('1', "1", "1", "1", "1", "1", "1");
        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["fieldname"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }
        $result = array();
        $result['fieldcustom'] = 1;
        $result['field'] = $field;
        $result['list'] = ($payList['list'] == "") ? array() : $payList['list'];
        $result['order'] = ($payList['order'] == "") ? array() : $payList['order'];

        if ($result['list']) {
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
        } else {
            $res = array('error' => 1, 'errortip' => '暂无订单信息', 'result' => $result);
        }

        ajax_return($res);
    }

    function PcorderPayList($request)
    {
        $this->PcVerify($request);
        $datawhere = "o.student_id = s.student_id and o.school_id = '{$request['school_id']}' and o.order_status > '0'";

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (s.cnname like '%{$request['keyword']}%' or s.enname like '%{$request['keyword']}%' or s.branch like '%{$request['keyword']}%' or o.order_pid like '%{$request['keyword']}%')";
        }
        if (isset($request['pay_status']) && $request['pay_status'] == 1) {
            $datawhere .= " and o.order_status = '3'";
        } else {
            $datawhere .= " and o.order_status <> '3'";
        }

        if (isset($request['starttime']) and $request['starttime'] != "") {
            $starttime = strtotime($request['starttime']);
            $datawhere .= " and o.order_paytime >= '{$starttime}'";
        }

        if (isset($request['starttime']) and $request['endtime'] != "") {
            $endtime = strtotime($request['endtime']);
            $datawhere .= " and o.order_paytime <= '{$endtime}'";
        }

        if (isset($request['order_pid']) && $request['order_pid'] !== '') {
            $datawhere .= " and o.order_pid = '{$request['order_pid']}'";
        }

        $ordersql = "SELECT o.order_id as pay_id,
o.order_pid AS pay_pid,
o.order_paymentprice AS pay_price,
o.order_pid AS order_pid,
o.order_paytime as order_paytime,
s.cnname AS student_cnname,
s.enname AS student_enname
FROM ptc_shop_order AS o, ptc_student AS s WHERE {$datawhere}
order by o.order_addtime desc limit {$pagestart},{$num}";
        $payList = $this->PtCenterData->selectClear($ordersql);
        $payArray = false;
        if ($payList) {
            $payArray = array();
            foreach ($payList as $payVar) {
                $payOne = array();
                $payOne['pay_id'] = $payVar['pay_id'];
                $payOne['pay_pid'] = $payVar['pay_pid'];
                $payOne['pay_typename'] = '微信支付';
                if ($payVar['order_paytime'] !== '0') {
                    $payOne['pay_successtime'] = date("Y-m-d H:i:s", $payVar['order_paytime']);
                } else {
                    $payOne['pay_successtime'] = '----';
                }
                $payOne['pay_issuccess'] = '未支付';
                $payOne['pay_price'] = $payVar['pay_price'];
                $payOne['order_pid'] = $payVar['order_pid'];
                $payOne['student_cnname'] = $payVar['student_cnname'];
                $payOne['student_enname'] = $payVar['student_enname'];
                $payOne['paylog_tradeno'] = '站群系统订单';
                $payOne['pay_status'] = 0;
                $payArray[] = $payOne;
            }
        }

        $fieldstring = array('pay_pid', 'pay_typename', 'pay_price', 'pay_issuccess', 'pay_successtime', 'pay_outnumber', 'pay_note');
        $fieldname = array('支付编号', '支付方式', '支付金额', '支付状态', '支付时间', '外部交易编号', '备注');
        $fieldcustom = array('1', "1", "1", "1", "1", "1", "1");
        $fieldshow = array('1', "1", "1", "1", "1", "1", "1");
        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["fieldname"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 1;
        $result['field'] = $field;
        $result['list'] = !$payArray ? array() : $payArray;

        $order = array();
        $order['order_surplusprice'] = sprintf("%01.2f", 0);
        $order['order_paymentprice'] = sprintf("%01.2f", 0);
        $order['order_paidprice'] = sprintf("%01.2f", 0);
        $result['order'] = $order;
        if ($result['list']) {
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
        } else {
            $res = array('error' => 1, 'errortip' => '暂无订单信息', 'result' => $result);
        }
        ajax_return($res);
    }

    function orderPayListApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $data = array();
        $data['errorlog_apiurl'] = "{$request['u']}/{$request['api']}{$request['t']}";
        $data['errorlog_parameter'] = @mysql_escape_string(http_build_query($request));
        $this->DataControl->insertData("smc_api_errorlog", $data);
        if ($request['company_id'] == '666') {
            $this->PcorderPayList($request);
        } else {
            $this->KddorderPayList($request);
        }
    }

    //取消支付订单
    function cancelPayOrderAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        if ($request['company_id'] == '666') {
            $res = array('error' => 1, 'errortip' => '暂不支持订单取消', 'result' => array());
            ajax_return($res);
        }

        if (empty($request['order_pid'])) {
            $res = array('error' => 1, 'errortip' => '订单编号错误', 'result' => array());
            ajax_return($res);
        }
        if (empty($request['pay_pid'])) {
            $res = array('error' => 1, 'errortip' => '支付编号错误', 'result' => array());
            ajax_return($res);
        }
        $this->ThisVerify($request);
        $orderHandleModel = new  \Model\Smc\OrderHandleModel($request, $request['order_pid']);
        $orderHandleModel->cancelPayOrder($request['pay_pid']);

        $res = array('error' => $orderHandleModel->error, 'errortip' => $orderHandleModel->errortip, 'result' => array());

        $json_play = new \Webjson();
        $errorData['errorlog_apiurl'] = $this->u . '/' . $this->c;
        $errorData['errorlog_class'] = 0;
        $errorData['errorlog_parameter'] = $json_play->encode($request);
        $errorData['errorlog_bakjson'] = $json_play->encode($res);
        $errorData['errorlog_time'] = time();

        $this->DataControl->insertData("smc_api_errorlog", $errorData);

        ajax_return($res);
    }

    function KddsuccessPay($request)
    {
        if ($request['order_pid'] == "") {
            $res = array('error' => 1, 'errortip' => '订单编号错误', 'result' => array());
            ajax_return($res);
        }
        if (empty($request['pay_pid'])) {
            $res = array('error' => 1, 'errortip' => '支付编号错误', 'result' => array());
            ajax_return($res);
        }
        if ($request['paylog_tradeno'] == "") {
            $res = array('error' => 1, 'errortip' => '请输入外部交易编号', 'result' => array());
            ajax_return($res);
        }

        if (isset($request['create_time']) && $request['create_time'] !== "") {
            $createtime = $request['create_time'];
        } else {
            $createtime = date('Y-m-d H:i:s');
        }

        $orderPayModel = new \Model\Smc\OrderPayModel($request, $request['order_pid']);

        $payOne = $this->DataControl->getFieldOne("smc_payfee_order_pay", "paytype_code", "pay_pid='{$request['pay_pid']}'");
        if (!$payOne) {
            $res = array('error' => 1, 'errortip' => '支付编号不存在', 'result' => array());
            ajax_return($res, $request['language_type']);
        }

        $balance_array = array('balance', 'forward', 'norebalance', 'cattimes', 'catdeposit', 'catsales', 'catmanage', 'catbus', 'catfood');
        $cash_array = array('cash');
        $system_array = array('feewaiver');

        $paychannel_code = '';
        if (in_array($request['paytype_code'], $balance_array)) {
            $paychannel_code = 'balance';
        } elseif (in_array($request['paytype_code'], $cash_array)) {
            $paychannel_code = 'cash';
        } elseif (in_array($request['paytype_code'], $system_array)) {
            $paychannel_code = 'system';
        }

        $bools = $orderPayModel->orderPaylog($request['pay_pid'], $request['paylog_tradeno'], $request['ifee'], $request['pay_note'], $request['paytype_code'], $createtime, 0, '', '', $paychannel_code);
        if ($bools) {
            $res = array('error' => 0, 'errortip' => '操作订单成功', 'result' => array());
        } else {
            $res = array('error' => 1, 'errortip' => $orderPayModel->errortip, 'result' => array());
        }

        $errorData = array();
        $json_play = new \Webjson();
        $errorData['errorlog_apiurl'] = $this->u . '/' . $this->c;
        $errorData['errorlog_class'] = 0;
        $errorData['errorlog_parameter'] = $json_play->encode($request);
        $errorData['errorlog_bakjson'] = $json_play->encode($res);
        $errorData['errorlog_time'] = time();
        $this->DataControl->insertData("smc_api_errorlog", $errorData);
        ajax_return($res);
    }

    function PcsuccessPay($request)
    {
        $this->PcVerify($request);

        $parameter = array();
        $parameter['token'] = $request['token'];
        $parameter['company_id'] = $request['company_id'];
        $parameter['school_id'] = $request['school_id'];
        $parameter['order_pid'] = $request['order_pid'];
        $parameter['pay_pid'] = $request['pay_pid'];
        $parameter['paylog_tradeno'] = $request['paylog_tradeno'];
        $parameter['pay_note'] = $request['pay_note'];
        $parameter['paytype_code'] = $request['paytype_code'];
        if (isset($request['paylog_refertono'])) {
            $parameter['paylog_refertono'] = $request['paylog_refertono'];
        }
        if (isset($request['paylog_posterminal'])) {
            $parameter['paylog_posterminal'] = $request['paylog_posterminal'];
        }
        $sendApiSting = request_by_curl("https://ptcapi.kidcastle.com.cn/BoingPay/Ylpaybak", dataEncode($parameter), "GET", array());
        $sendApiArray = json_decode($sendApiSting, "1");
        if ($sendApiArray['error'] == '0') {
            $res = array('error' => 0, 'errortip' => $sendApiArray['errortip'], 'result' => array());
        } else {
            $res = array('error' => 1, 'errortip' => $sendApiArray['errortip'], 'result' => array());
        }

        $errorData = array();
        $json_play = new \Webjson();
        $errorData['errorlog_apiurl'] = $this->u . '/' . $this->c;
        $errorData['errorlog_class'] = 0;
        $errorData['errorlog_parameter'] = $json_play->encode($request);
        $errorData['errorlog_bakjson'] = $json_play->encode($res);
        $errorData['errorlog_time'] = time();
        $this->DataControl->insertData("smc_api_errorlog", $errorData);
        ajax_return($res);
    }

    function successPayAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        if ($request['company_id'] == '666') {
            $this->PcsuccessPay($request);
        } else {
            $this->KddsuccessPay($request);
        }
    }

    function orderPayAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        if ($request['company_id'] == '666') {
            $res = array('error' => 1, 'errortip' => '暂不支持生成支付订单', 'result' => array());
            ajax_return($res);
        }

        if (empty($request['order_pid'])) {
            $res = array('error' => 1, 'errortip' => '订单编号缺失', 'result' => array());
            ajax_return($res);
        }
        if ($request['paytype'] == "") {
            $res = array('error' => 1, 'errortip' => '请选择支付方式', 'result' => array());
            ajax_return($res);
        }
        if ($request['paytimes'] == "") {
            $res = array('error' => 1, 'errortip' => '请选择支付类型', 'result' => array());
            ajax_return($res);
        }


        $publicarray = array();
        $publicarray['company_id'] = $request['company_id'];
        $publicarray['school_id'] = $request['school_id'];
        $publicarray['staffer_id'] = $request['staffer_id'];


        $payArray = array();
        $payArray['order_pid'] = $request['order_pid'];
        $payArray['paytype'] = $request['paytype'];
        $payArray['paytimes'] = $request['paytimes'];
        $payArray['pay_price'] = $request['pay_price'];
        $payArray['create_time'] = $request['create_time'];
//		$payArray['pay_note'] = $request['pay_note'];

        if ($request['paytimes'] == '0' && !isset($payArray['pay_price'])) {
            $res = array('error' => 1, 'errortip' => '分批支付必须传入支付金额!', 'result' => array());
            ajax_return($res);
        }

        $Model = new \Model\Smc\OrderPayModel($publicarray, $request['order_pid']);
        $data = $Model->createOrderPay($payArray);

        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $data);
        $errorData = array();

        $json_play = new \Webjson();
        $errorData['errorlog_apiurl'] = $this->u . '/' . $this->c;
        $errorData['errorlog_class'] = 0;
        $errorData['errorlog_parameter'] = $json_play->encode($request);
        $errorData['errorlog_bakjson'] = $json_play->encode($res);
        $errorData['errorlog_time'] = time();

        $this->DataControl->insertData("smc_api_errorlog", $errorData);

        ajax_return($res);
    }


}