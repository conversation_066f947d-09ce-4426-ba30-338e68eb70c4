<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/13
 * Time: 23:25
 * crm-首页
 */

namespace Work\Controller\Posapi;

class CommonController extends viewTpl
{
	public $u;
	public $t;
	public $c;
	public $visitType = "api";
	
	
	//预加载处理类
	function __construct($visitType = "api")
	{
		parent::__construct();
		$this->u = $this->router->getController();
		$this->t = $this->router->getUrl();
		$this->c = $this->router->getAction();
	}

    function KddSchool($request){
        $this->ThisVerify($request);
        //验证账户

        $CommonModel = new \Model\Crm\CommonModel();
        $dataList = $CommonModel->getSchoolApi($request);

        $field = array();
        $field["school_id"] = "序号";
        $field["company_id"] = "所属公司";
        $field["district_id"] = "所属集团区域ID";
        $field["school_branch"] = "校区编号";
        $field["school_shortname"] = "校园简称";
        $field["school_cnname"] = "校园名称称";
        $field["school_enname"] = "检索代码称";
        $field["company_cnname"] = "机构名称";
        $field["school_address"] = "学校地址";
        $field["school_phone"] = "学校联系电话";

        $result = array();
        if($dataList){
            $result["field"] = $field;
            $result["data"] = $dataList['datalist'];
            $res = array('error' => '0', 'errortip' => '筛选学校成功', 'initial' =>  $dataList['initial'],'allid' =>  $dataList['allid'], 'result' => $result);
        }else{
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '筛选学校失败', 'result' => $result);
        }
        ajax_return($res);
    }

    function PcSchool($request){
        $this->PcVerify($request);
        $schoolView = $this->PtCenterData->getFieldOne('ptc_school',"school_id,school_passwd,school_cnname,school_branch","school_id = '{$request['school_id']}'");

        $schoolOne = array();
        $schoolOne['school_id'] = $schoolView['school_id'];
        $schoolOne['company_id'] = '666';
        $schoolOne['district_id'] = '120';
        $schoolOne['school_branch'] = $schoolView['school_branch'];
        $schoolOne['school_shortname'] = $schoolView['school_cnname'];
        $schoolOne['school_cnname'] = $schoolView['school_cnname'];
        $schoolOne['school_enname'] = 'Kidcastle';
        $schoolOne['company_cnname'] = '课叮铛教育集团';
        $schoolOne['school_address'] = '上海市嘉定区金沙江西路1069号11层';
        $schoolOne['school_phone'] = '021-67086888';

        $field = array();
        $field["school_id"] = "序号";
        $field["company_id"] = "所属公司";
        $field["district_id"] = "所属集团区域ID";
        $field["school_branch"] = "校区编号";
        $field["school_shortname"] = "校园简称";
        $field["school_cnname"] = "校园名称称";
        $field["school_enname"] = "检索代码称";
        $field["company_cnname"] = "机构名称";
        $field["school_address"] = "学校地址";
        $field["school_phone"] = "学校联系电话";

        $initial = array();
        $initial[] = '';

        $schoolArray  = array();
        $schoolArray[] = $schoolOne;

        $schoolList = array();
        $schoolList[] = $schoolArray;

        $result["field"] = $field;
        $result["data"] = $schoolList;
        $res = array(
            'error' => '0',
            'errortip' => '筛选学校成功',
            'initial' =>  $initial,
            'allid' =>  $schoolOne['school_id'],
            'result' => $result
        );
        ajax_return($res);

    }


	//首页 -- 筛选学校
	function getSchoolApi()
	{
		$request = Input('get.','','trim,addslashes');
		if($request['company_id'] == '666'){
            $this->PcSchool($request);
        }else{
            $this->KddSchool($request);
        }
	}
}