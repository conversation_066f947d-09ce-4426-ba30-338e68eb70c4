<?php
/**
 * 客诉-C端-工单
 */

namespace Work\Controller\Easxapi;


class UcsUserRepairorderController extends UcsUserController
{

    /**
     * 提交申诉 -zjc
     */
    function submitRepairorderAction()
    {
        $request = Input('POST.','','trim,addslashes');
        $request['repairorder_from'] = 1;
        $request['customer_id'] = $this->customer_id;
        $request['staffer_id'] = 0;
        $Model = new \Model\Easx\UcsRepairorderModel($request);
        $dataList = $Model->addRepairorder($request);
        $field = array();
        $field['repairorder_pid'] = "工单编号";
        $field['feedbacktype_catgory'] = "类型";
        $field['feedbacktype_id'] = "分类ID";
        $field['feedbacktheme_id'] = "主题ID";
        $field['repairorder_from'] = "来源";
        $field['company_id'] = "集团ID";
        $field['school_id'] = "学校ID";
        $field['repairorder_createtime'] = "创建时间";
        $field['repairorder_level'] = "风险星级";
        $field['repairorder_contactname'] = "联系人姓名";
        $field['repairorder_contactmobile'] = "联系人手机";
        $field['repairorder_content'] = "投诉内容";
        $field['first_staffer_id'] = "第一受理人";
        $field['staffer_id'] = "当前受理人";
        $field['create_staffer_id'] = "工单创建人";

        $result = array();

        $result["field"] = $field;
        $result["data"] = $Model->result;

        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $result));

    }


}