<?php
namespace Work\Controller\Easxapi;


class HomeworkController extends viewTpl
{
    public $u;
    public $t;
    public $c;

    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
    }

    function homeworkListApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Easx\HomeworkModel();
        $res = $Model->homeworkList($request);
        $result = array();
        if ($res) {
            $result["list"] = $res['list'];
            $result["allnum"] = $res['allnum'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res);
    }

    function homeworkItemApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Easx\HomeworkModel();
        $res = $Model->homeworkItem($request);
        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res);

    }

    function homeworkReceiveInfoApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Easx\HomeworkModel();
        $res = $Model->homeworkReceiveInfo($request);
        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res);

    }

    function homeworkStuListApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Easx\HomeworkModel();
        $res = $Model->homeworkStuList($request);
        $result = array();
        if ($res) {
            $result["list"] = $res['list'];
            $result["allnum"] = $res['allnum'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res);

    }

    function homeworkStuOneApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Easx\HomeworkModel();
        $res = $Model->homeworkStuOne($request);
        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res);

    }

    function getTempTypeApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Easx\HomeworkModel();
        $res = $Model->getTempType($request);
        $result = array();
        if ($res) {
            $result["list"] = $res['list'];
            $result["allnum"] = $res['allnum'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res);

    }

    function getHomeTempView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Easx\HomeworkModel();
        $datalist = $Model->getHomeTemp($request);
        $field = array();
        $result = array();
        $result["field"] = $field;
        if ($datalist['list']) {
            $result['list'] = $datalist['list'];
            $res = array('error' => 0, 'errortip' => '获取评星模板', 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => 1, 'errortip' => '暂无评星模板', 'result' => $result);
        }
        ajax_return($res);

    }

    function stuHomeworkListApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Easx\HomeworkModel();
        $res = $Model->stuHomeworkList($request);
        $result = array();
        if ($res) {
            $result["list"] = $res['list'];
            $result["allnum"] = $res['allnum'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res);

    }

    function submitCommentAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Easx\HomeworkModel();
        $res = $Model->submitComment($request);
        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '发布成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res);

    }

    function hourListApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Easx\HomeworkModel();
        $res = $Model->hourList($request);
        $result = array();
        if ($res) {
            $result["list"] = $res['list'];
            $result["allnum"] = $res['allnum'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res);

    }

    function classStuListApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Easx\HomeworkModel();
        $res = $Model->classStuList($request);
        $result = array();
        if ($res) {
            $result["list"] = $res['list'];
            $result["allnum"] = $res['allnum'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res);

    }

    function submitHomeworkAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Easx\HomeworkModel();
        $res = $Model->submitHomework($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '发布成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res);

    }


    function updateHomeworkAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Easx\HomeworkModel();
        $res = $Model->updateHomework($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '编辑成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res);

    }

    function delHomeworkAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Easx\HomeworkModel();
        $res = $Model->delHomework($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '删除成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res);

    }




    /**
     * 作业查询 管理列表 - 后台PC
     * 作者: 97
     * @param  对应接口文档 321
     * @return array
     */
    function homeworkListPCApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Easx\HomeworkModel();
        $res = $Model->homeworkListPC($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "homework_id";
        $field[$k]["fieldname"] = "作业ID";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;
        $field[$k]["fieldstring"] = "homework_title";
        $field[$k]["fieldname"] = "作业标题";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["ismethod"] = 1;
        $field[$k]["isTitle"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "homework_createtime";
        $field[$k]["fieldname"] = "创建时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "class_id";
        $field[$k]["fieldname"] = "班级ID";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "class_cnname";
        $field[$k]["fieldname"] = "班级中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "class_enname";
        $field[$k]["fieldname"] = "班级别名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "staffer_cnname";
        $field[$k]["fieldname"] = "作业布置教师";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "readNum";
        $field[$k]["fieldname"] = "阅读人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["ismethod"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "submitNum";
        $field[$k]["fieldname"] = "提交人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["ismethod"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "commentNum";
        $field[$k]["fieldname"] = "评论人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["ismethod"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "allNum";
        $field[$k]["fieldname"] = "全部人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["ismethod"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["field"] = $field;
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res);
    }
    /**
     * 作业查询 班级列表 - 后台PC
     * 作者: 97
     * @param  对应接口文档 322
     * @return array
     */
    function homeworkClassListPCApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Easx\HomeworkModel();
        $res = $Model->homeworkClassListPC($request);
        $result = array();
        if ($res) {
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res);
    }
    /**
     * 作业查询 作业教师学校列表 - 后台PC
     * 作者: 97
     * @param  对应接口文档 322
     * @return array
     */
    function homeworkSchListPCApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Easx\HomeworkModel();
        $res = $Model->homeworkSchListPC($request);
        $result = array();
        if ($res) {
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res);
    }

    //作业完成情况
    function homeworkDoneView(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Easx\HomeworkModel();
        $res = $Model->homeworkDone($request);
        $result = array();
        if ($res) {
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res);
    }

    //需要提交作业学生完成情况(待评，已评)
    function homeworkReferApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Easx\HomeworkModel();
        $res = $Model->homeworkReferApi($request);
        $result = array();
        if ($res) {
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res);
    }

    //需要提交作业学生完成情况(打回)
    function homeworkBackApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Easx\HomeworkModel();
        $res = $Model->homeworkBackApi($request);
        $result = array();
        if ($res) {
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res);
    }

    //需要提交作业学生完成情况(未交)
    function homeworkUnsendApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Easx\HomeworkModel();
        $res = $Model->homeworkUnsendApi($request);
        $result = array();
        if ($res) {
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res);
    }

    //查看作业
    function HomeworkDetailApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Easx\HomeworkModel();
        $res = $Model->HomeworkDetailApi($request);
        $result = array();
        if ($res) {
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res);
    }

    /**
     * 作业查询 作业教师列表 - 后台PC
     * 作者: 97
     * @param  对应接口文档 322
     * @return array
     */
    function homeworkTeaListPCApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Easx\HomeworkModel();
        $res = $Model->homeworkTeaListPC($request);
        $result = array();
        if ($res) {
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res);
    }

    /**
     * 作业查询 作业完成状态 - 后台PC
     * 作者: 97
     * @param  对应接口文档 324
     * @return array
     */
    function homeworkStuStatePCApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Easx\HomeworkModel();
        $res = $Model->homeworkStuStatePC($request);

        $field = array();
        $field["readlist"] = "已读";
        $field["noreadlist"] = "未读";
        $field["submitlist"] = "已提交";
        $field["commentlist"] = "已评价";
        $field["alllist"] = "全部";

        $result = array();
        if ($res) {
            $result["field"] = $field;
            $result["list"] = $res['list'];
            $result["num"] = $res['num'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["num"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res);
    }

    /**
     * 作业统计 按班级 - 后台PC
     * 作者: 97
     * @param  对应接口文档 321
     * @return array
     */
    function homeworkClassStatisApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Easx\HomeworkModel();
        $res = $Model->homeworkClassStatis($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "class_id";
        $field[$k]["fieldname"] = "班级ID";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;
        $field[$k]["fieldstring"] = "class_cnname";
        $field[$k]["fieldname"] = "班级中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;
        $field[$k]["fieldstring"] = "class_enname";
        $field[$k]["fieldname"] = "班级别名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;
        $field[$k]["fieldstring"] = "stunum";
        $field[$k]["fieldname"] = "发送人数";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;
        $field[$k]["fieldstring"] = "readnum";
        $field[$k]["fieldname"] = "阅读人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;
        $field[$k]["fieldstring"] = "readrate";
        $field[$k]["fieldname"] = "阅读率";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;
        $field[$k]["fieldstring"] = "submitNum";
        $field[$k]["fieldname"] = "提交人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;
        $field[$k]["fieldstring"] = "submitrate";
        $field[$k]["fieldname"] = "提交率";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;
        $field[$k]["fieldstring"] = "commentnum";
        $field[$k]["fieldname"] = "评价人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;
        $field[$k]["fieldstring"] = "commentrate";
        $field[$k]["fieldname"] = "评价率";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;
        $field[$k]["fieldstring"] = "homeworknum";
        $field[$k]["fieldname"] = "作业数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res);
    }


    /**
     * 作业统计 按班级 - 后台PC
     * 作者: 97
     * @param  对应接口文档 321
     * @return array
     */
    function homeworkTeacherStatisApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Easx\HomeworkModel();
        $res = $Model->homeworkTeacherStatis($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "staffer_id";
        $field[$k]["fieldname"] = "教师ID";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;
        $field[$k]["fieldstring"] = "staffer_cnname";
        $field[$k]["fieldname"] = "教师中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;
        $field[$k]["fieldstring"] = "staffer_enname";
        $field[$k]["fieldname"] = "教师英文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;
        $field[$k]["fieldstring"] = "homeworknum";
        $field[$k]["fieldname"] = "作业数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;
        $field[$k]["fieldstring"] = "stunum";
        $field[$k]["fieldname"] = "发送人数";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;
        $field[$k]["fieldstring"] = "readnum";
        $field[$k]["fieldname"] = "阅读人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;
        $field[$k]["fieldstring"] = "readrate";
        $field[$k]["fieldname"] = "阅读率";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;
        $field[$k]["fieldstring"] = "submitNum";
        $field[$k]["fieldname"] = "提交人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;
        $field[$k]["fieldstring"] = "submitrate";
        $field[$k]["fieldname"] = "提交率";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;
        $field[$k]["fieldstring"] = "commentnum";
        $field[$k]["fieldname"] = "评价人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;
        $field[$k]["fieldstring"] = "commentrate";
        $field[$k]["fieldname"] = "评价率";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res);
    }


    /**
     * 已发布的作业 列表 - 后台PC
     * 作者: 97
     * @param  对应接口文档 321
     * @return array
     */
    function homeworkAllListApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Easx\HomeworkModel();
        $res = $Model->homeworkAllList($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "hour_day";
        $field[$k]["fieldname"] = "课时日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;
        $field[$k]["fieldstring"] = "hour_starttime";
        $field[$k]["fieldname"] = "课时开始时间";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;
        $field[$k]["fieldstring"] = "hour_endtime";
        $field[$k]["fieldname"] = "课时结束时间";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;
        $field[$k]["fieldstring"] = "homework_title";
        $field[$k]["fieldname"] = "作业标题";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;
        $field[$k]["fieldstring"] = "homework_createtime";
        $field[$k]["fieldname"] = "发布时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;
        $field[$k]["fieldstring"] = "stunum";
        $field[$k]["fieldname"] = "总人数";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;
        $field[$k]["fieldstring"] = "readnum";
        $field[$k]["fieldname"] = "阅读人数";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;
        $field[$k]["fieldstring"] = "hourread";
        $field[$k]["fieldname"] = "阅读比";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;


        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res);
    }

    /**
     * 发布作业  删除一个作业接受人 - 后台PC
     * 作者: 97
     * @param  对应接口文档 331
     * @return array
     */
    function delHomeworkOneApi(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        //model
        $Model = new \Model\Easx\HomeworkModel();
        $bools = $Model->delHomeworkOne($request);
        if ($bools) {
            $res = array('error' => '0', 'errortip' => "删除成功", 'result' => array());
        } else {
            $res = array('error' => '1', 'errortip' => "删除失败", 'result' => array());
        }
        ajax_return($res);
    }

    /**
     * 发布作业  删除一个作业接受人 - 后台PC
     * 作者: 97
     * @param  对应接口文档 331
     * @return array
     */
    function delHomeworkReceiveOneApi(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        //model
        $Model = new \Model\Easx\HomeworkModel();
        $bools = $Model->delHomeworkReceiveOne($request);
        if ($bools) {
            $res = array('error' => '0', 'errortip' => "删除成功", 'result' => array());
        } else {
            $res = array('error' => '1', 'errortip' => "删除失败", 'result' => array());
        }
        ajax_return($res);
    }

    //打回订正
    function HomeBackApi(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        //model
        $Model = new \Model\Easx\HomeworkModel();
        $bools = $Model->HomeBackApi($request);
        if ($bools) {
            $res = array('error' => '0', 'errortip' => "打回成功", 'result' => array());
        } else {
            $res = array('error' => '1', 'errortip' => "打回失败", 'result' => array());
        }
        ajax_return($res);
    }






}