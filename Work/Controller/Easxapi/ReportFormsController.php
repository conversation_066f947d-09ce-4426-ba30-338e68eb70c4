<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2019/12/4
 * Time: 16:10
 */

namespace Work\Controller\Easxapi;

use Model\Easx\CoursewareModel;

class ReportFormsController extends viewTpl{
    public $u;
    public $t;
    public $c;

    //预加载处理类
    function __construct($visitType = "api")
    {
        parent::__construct ();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
    }

    //本地权限校验入口
    function ThisVerify($request)
    {
        $paramArray = array();
        $paramArray['staffer_id'] = $request['staffer_id'];
        $paramArray['school_id'] = $request['school_id'];
        $paramArray['company_id'] = $request['company_id'];
        $paramArray['token'] = $request['token'];
        if(!$this->UserLimit($paramArray)){
            $result = array();
            $result["list"] = array();
            $result["tokeninc"] = "0";
            $res = array('error' => '1', 'errortip' => "用户token失效", 'result' => $result);
            ajax_return($res);
        }
    }

    //班级统计报表
    function SchoolReportView(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new CoursewareModel($request);
        $datalist = $Model->SchoolReport($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldname"] = "班级ID";
        $field[$k]["fieldstring"] = "class_id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "班级名称";
        $field[$k]["fieldstring"] = "class_cnname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "班级别名";
        $field[$k]["fieldstring"] = "class_enname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "班级编号";
        $field[$k]["fieldstring"] = "class_branch";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "主教老师";
        $field[$k]["fieldstring"] = "staffer_cnname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "助教老师";
        $field[$k]["fieldstring"] = "fu_staffer_cnname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "已上课时数";
        $field[$k]["fieldstring"] = "hour_num";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "总备课时数";
        $field[$k]["fieldstring"] = "class_hournums";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "待备课时数";
        $field[$k]["fieldstring"] = "not_prepare_num";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "已备课时数";
        $field[$k]["fieldstring"] = "prepare_num";
        $field[$k]["show"] = 1;
        $field[$k]["isProgress"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "延迟备课时数";
        $field[$k]["fieldstring"] = "delay_num";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $datalist['allnums'];
        if($datalist['list']){
            $result['list'] = $datalist['list'];
            $res = array('error' => 0, 'errortip' => '获取班级统计报表成功', 'result' => $result);
        }else{
            $result['list'] = array();
            $res = array('error' => 1, 'errortip' => '暂无班级统计报表', 'result' => $result);
        }
        ajax_return($res);
    }

    //教师统计报表
    function StafferReportView(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new CoursewareModel($request);
        $datalist = $Model->StafferReport($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldname"] = "教师ID";
        $field[$k]["fieldstring"] = "staffer_id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "教师名称";
        $field[$k]["fieldstring"] = "staffer_cnname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "英文名称";
        $field[$k]["fieldstring"] = "staffer_enname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "教师编号";
        $field[$k]["fieldstring"] = "staffer_branch";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "需备课班级数";
        $field[$k]["fieldstring"] = "class_num";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "待备课时数";
        $field[$k]["fieldstring"] = "hour_num";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "已备课时数";
        $field[$k]["fieldstring"] = "prepare_num";
        $field[$k]["show"] = 1;
        $field[$k]["isProgress"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "延迟备课时数";
        $field[$k]["fieldstring"] = "delay_num";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $datalist['allnums'];
        if($datalist['list']){
            $result['list'] = $datalist['list'];
            $res = array('error' => 0, 'errortip' => '获取教师统计报表成功', 'result' => $result);
        }else{
            $result['list'] = array();
            $res = array('error' => 1, 'errortip' => '暂无教师统计报表', 'result' => $result);
        }
        ajax_return($res);
    }

    //班级备课详情
    function ClassHourTotalView(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new CoursewareModel($request);
        $datalist = $Model->ClassHourTotal($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldname"] = "班级ID";
        $field[$k]["fieldstring"] = "class_id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "班级名称";
        $field[$k]["fieldstring"] = "class_cnname";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "课次名称";
        $field[$k]["fieldstring"] = "hour_name";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "上课老师";
        $field[$k]["fieldstring"] = "staffer_cnname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "上课教室";
        $field[$k]["fieldstring"] = "classroom_cnname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "主教老师";
        $field[$k]["fieldstring"] = "staffer_cnname";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "助教老师";
        $field[$k]["fieldstring"] = "fu_staffer_cnname";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "班级编号";
        $field[$k]["fieldstring"] = "class_branch";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "课程别名称";
        $field[$k]["fieldstring"] = "course_cnname";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "课程别编号";
        $field[$k]["fieldstring"] = "course_branch";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "上课日期";
        $field[$k]["fieldstring"] = "hour_time";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "备课状态";
        $field[$k]["fieldstring"] = "prepare_status_name";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["isChangeStatus"] = 1;
        $k++;

        $field[$k]["fieldname"] = "上课状态";
        $field[$k]["fieldstring"] = "hour_ischecking_name";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["isChangeStatus"] = 1;
        $k++;

        $field[$k]["fieldname"] = "课时批注";
        $field[$k]["fieldstring"] = "prepare_postil";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $field[$k]["isChangeStatus"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $datalist['allnums'];
        $result['staffer_list'] = $datalist['staffer_list'];
        if($datalist['list']){
            $result['list'] = $datalist['list'];
            $res = array('error' => 0, 'errortip' => '获取班级备课详情成功', 'result' => $result);
        }else{
            $result['list'] = array();
            $res = array('error' => 1, 'errortip' => '暂无班级备课详情', 'result' => $result);
        }
        ajax_return($res);
    }

    //班级统计报表导出
    function SchoolExportView(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new CoursewareModel($request);
        $res = $Model->SchoolExport($request);

        ajax_return($res);
    }

    //教师统计报表导出
    function StafferExportView(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new CoursewareModel($request);
        $res = $Model->StafferExport($request);

        ajax_return($res);
    }

    //班级备课信息导出
    function ClassHourExportView(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new CoursewareModel($request);
        $res = $Model->ClassHour($request);

        ajax_return($res);
    }

}