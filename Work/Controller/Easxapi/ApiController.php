<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/13
 * Time: 23:25
 */

namespace Work\Controller\Easxapi;


class ApiController extends viewTpl
{
    public $u;
    public $t;
    public $c;
    public $visitType = "api";
    public $DataControl;
    public $backData;

    //预加载处理类
    function __construct($visitType = "api")
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();

        //数据库操作
        $this->DataControl = new \Dbmysql();
    }

    //用户学校切换记录
    function addStafferSchoolAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $company_id = $request['company_id'];

        if (!$company_id) {
            $res = array('error' => '1', 'errortip' => "公司id有误", 'result' => array());
            ajax_return($res, $request['language_type']);
        }

        $data = array();
        $data['company_id'] = $request['company_id'];
        $data['school_id'] = $request['newschool_id'];
        $data['staffer_id'] = $request['staffer_id'];
        $data['marketer_id'] = $request['marketer_id'];
        $data['schoollog_port'] = 3;
        $data['schoollog_createtime'] = time();
        $log_id = $this->DataControl->insertData('imc_staffer_schoollog', $data);

        if ($log_id) {
            $res = array('error' => '0', 'errortip' => "切换成功");
        } else {
            $res = array('error' => '1', 'errortip' => '切换失败');
        }
        ajax_return($res, $request['language_type']);
    }

    /**
     * 教务部分 同步学习平台的数据
     * author: ling
     * 对应接口文档 0001
     */
    function UpdateStuappAction()
    {
        $stuappHour = request_by_curl("https://stuapi.kidcastle.cn/Api/getClassHourPrograss", "", "get", array());
        $arr_stuappHour = json_decode($stuappHour, true);
        if ($arr_stuappHour) {
            $stuaReult = $arr_stuappHour['result'];

            if (is_array($stuaReult) && count($stuaReult) > 0) {
                $classOne = $this->DataControl->getFieldOne("smc_class", "class_id,class_branch", "class_branch='{$stuaReult[0]['class_branch']}'");
                if (!$classOne) {
                    $res = array('error' => 1, 'errortip' => "3.0不存在该班级" . $stuaReult[0]['class_branch'], 'result' => array());
                    ajax_return($res);
                }
                foreach ($stuaReult as $stuadata) {
                    $studentOne = $this->DataControl->getFieldOne("smc_student", "student_id", "student_branch='{$stuadata['student_branch']}'");
                    if (!$studentOne) {
                        continue;
                    }
                    if ($stuappOne = $this->DataControl->getOne("eas_stuapp_hour", "student_id='{$studentOne['student_id']}' and class_id='{$classOne['class_id']}'and hour_branch='{$stuadata['hour_branch']}'")) {
                        $stuapp_data = array();
                        $stuapp_data['classhour_comment'] = addslashes($stuadata['classhour_comment']);
                        $stuapp_data['classhour_note'] = addslashes($stuadata['classhour_note']);
                        $stuapp_data['task_allnums'] = $stuadata['taskitem_allnum'];
                        $stuapp_data['task_finhishnums'] = $stuadata['taskitem_finishnum'];
                        $stuapp_data['task_score'] = $stuadata['examine_score'];
                        $stuapp_data['radio_allnum'] = $stuadata['radio_allnum'];
                        $stuapp_data['radio_num'] = $stuadata['radio_num'];
                        $stuapp_data['video_num'] = $stuadata['video_num'];
                        $stuapp_data['all_video_num'] = $stuadata['all_video_num'];
                        $stuapp_data['classhour_memberstar'] = $stuadata['classhour_memberstar'];
                        $stuapp_data['radio_rate'] = $stuadata['radio_allnum'] ? ($stuadata['radio_num'] / $stuadata['radio_allnum']) : 0;
                        $stuapp_data['hour_updatatime'] = time();
                        $this->DataControl->updateData('eas_stuapp_hour', "student_id='{$studentOne['student_id']}' and class_id='{$classOne['class_id']}'and hour_branch='{$stuadata['hour_branch']}'", $stuapp_data);
                    } else {
                        $stuapp_data = array();
                        $stuapp_data['classhour_comment'] = addslashes($stuadata['classhour_comment']);
                        $stuapp_data['student_id'] = $studentOne['student_id'];
                        $stuapp_data['hour_branch'] = $stuadata['hour_branch'];
                        $stuapp_data['class_id'] = $classOne['class_id'];
                        $stuapp_data['classhour_note'] = addslashes($stuadata['classhour_note']);
                        $stuapp_data['task_allnums'] = $stuadata['taskitem_allnum'];
                        $stuapp_data['task_finhishnums'] = $stuadata['taskitem_finishnum'];
                        $stuapp_data['task_score'] = $stuadata['examine_score'];
                        $stuapp_data['radio_allnum'] = $stuadata['radio_allnum'];
                        $stuapp_data['radio_num'] = $stuadata['radio_num'];
                        $stuapp_data['video_num'] = $stuadata['video_num'];
                        $stuapp_data['all_video_num'] = $stuadata['all_video_num'];
                        $stuapp_data['classhour_memberstar'] = $stuadata['classhour_memberstar'];
                        $stuapp_data['radio_rate'] = $stuadata['radio_allnum'] ? ($stuadata['radio_num'] / $stuadata['radio_allnum']) : 0;
                        $stuapp_data['hour_updatatime'] = time();
                        $stuapp_data['hour_createtime'] = time();
                        $this->DataControl->insertData('eas_stuapp_hour', $stuapp_data);
                    }
                }
                $res = array('error' => 0, 'errortip' => "同步班级{$classOne['class_branch']}" . ' 课次编号' . $stuaReult[0]['hour_branch'] . "成功", 'result' => array());
                ajax_return($res);

            } else {
                $res = array('error' => 1, 'errortip' => "没有符合条件的数据", 'result' => array());
                ajax_return($res);
            }
        } else {
            $res = array('error' => 1, 'errortip' => "未查到数据", 'result' => array());
            ajax_return($res);
        }
    }

    /**
     * 教务部分 - 同步每个学校的数据每月的任务完成率
     * author: ling
     * 对应接口文档 0001
     */
    function SsynchroSchStuappAction($paramAarray = array())
    {
        $datawhere = "1";
        if (isset($paramAarray['school_branch']) && $paramAarray['school_branch']) {
            $datawhere .= " and l.school_branch='{$paramAarray['school_branch']}'";
        }
        if (isset($paramAarray['fixeddate']) && $paramAarray['fixeddate']) {
            $today = $paramAarray['fixeddate'];
        } else {
            $today = date("Y-m-d");
        }
        $month = date("Y-m", strtotime($today));
        $schoolOne = $this->DataControl->selectOne("select l.school_branch,l.school_id,l.school_cnname from smc_school as l where (l.company_id='8888' or company_id='1001') and l.school_isclose =0   and {$datawhere} and l.school_id not in (select t.school_id  from eas_school_stuapp_taskview as t where t.school_id=l.school_id and t.taskview_month='{$month}' and  t.taskview_updatetime > ( unix_timestamp(now()) -24*3600) )   limit 0,1  ");
        if ($schoolOne) {
            $taskData = array();
            $taskData['str_school_branch'] = $schoolOne['school_branch'];
            $taskData['starttime'] = date("Y-m-01", strtotime($today));
            $taskData['endtime'] = date("Y-m-t", strtotime($today));
            $schoolRate = request_by_curl("https://stuapi.kidcastle.cn/Api/getSchRateBySchBranch", dataEncode($taskData), "GET");
            if ($schoolRate) {
                $app_schoolRate = json_decode($schoolRate, true);
                if (!$app_schoolRate['result'] || !is_array($app_schoolRate['result'])) {
                    $app_schoolRateList = array();
                } else {
                    $app_schoolRateList = $app_schoolRate['result'][0];
                }
                $data = array();
                $data['school_id'] = $schoolOne['school_id'];
                $data['school_branch'] = $schoolOne['school_branch'];
                $data['taskview_month'] = $month;
                $data['taskview_up_rate'] = $app_schoolRateList['up_taskitem_rate'] + 0;
                $data['taskview_video_rate'] = $app_schoolRateList['taskview_video_rate'] + 0;  //录播
                $data['taskview_avg_testscore'] = $app_schoolRateList['taskview_avg_testscore'] + 0; //测试平均分
                $data['taskview_textbook_rate'] = '';
                $data['taskview_avg_audio_score'] = $app_schoolRateList['avg_audio_taskitem_score'] + 0; //配音平均分
                $data['taskview_classhour_memberstar'] = $app_schoolRateList['classhour_memberstar'] + 0; //家长评分
                $data['taskview_under_rate'] = $app_schoolRateList['taskview_under_rate'] + 0; //线下
                $data['taskview_updatetime'] = time();

                $taskviewOne = $this->DataControl->getFieldOne("eas_school_stuapp_taskview", "taskview_id", "school_id='{$schoolOne['school_id']}' and taskview_month='{$month}'");
                if ($taskviewOne) {
                    $this->DataControl->updateData("eas_school_stuapp_taskview", "school_id='{$schoolOne['school_id']}' and taskview_month='{$month}'", $data);
                } else {
                    $this->DataControl->insertData("eas_school_stuapp_taskview", $data);
                }
                if ($month == date("Y-m")) {
                    $array_data = array();
                    $array_data['school_branch'] = $schoolOne['school_branch'];
                    $array_data['fixeddate'] =  date("Y-m-d", strtotime(' -1 month', strtotime($today)));
                    $this->SsynchroSchStuappAction($array_data);
                } else {
                    ajax_return(array('error' => 0, 'errortip' => "同步{$schoolOne['school_cnname']} {$month} 数据成功!"));
                }
            }
            ajax_return(array('error' => 1, 'errortip' => "该校暂无 {$month}月 学习平台数据!"));
        } else {
            ajax_return(array('error' => 1, 'errortip' => "暂无需要同步的数据!"));
        }
    }


//     删除班别和对应的hour_branch不正确
    function delWrongHourBranchApi()
    {

        $sql = "select  co.course_branch, sh.*
    from  eas_stuapp_hour as sh 
    left join smc_class as  c ON c.class_id =sh.class_id
    left join smc_course as co ON co.course_id = c.course_id
    where   course_branch <> substring_index(sh.hour_branch,'_',1) limit 0,1000
    
        ";
        $dataList = $this->DataControl->selectClear($sql);
        $num = 0;
        if ($dataList) {
            foreach ($dataList as $value) {
//                $this->DataControl->delData('eas_stuapp_hour' ,"student_id='{$value['student_id']}' and class_id='{$value['class_id']}' and hour_branch ='{$value['hour_branch']}'");
                $num++;
            }
        }
        var_dump($num);
        die;

    }

    function bindView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $data = array();
        $data['staffer_wxtoken'] = $request['wxtoken'];
        $data['staffer_wximg'] = $request['imghead'];
        $data['staffer_updatetime'] = time();
        $this->DataControl->updateData("smc_staffer", "staffer_id = '{$request['staffer_id']}'", $data);
        ajax_return(array('error' => 0, 'errortip' => "绑定成功!"));

    }

    /**
     * 同步小书检核的代码
     * author: ling
     * 对应接口文档 0001
     */
    function addStuCheckAction()
    {
        $request = Input("get.", "", "addslashes");
        $courseOne = $this->DataControl->getFieldOne("smc_course", "course_id,course_branch", "course_branch='{$request['course_branch']}' and course_isuseeas =1 ");
        if ($courseOne) {
            $classOne = $this->DataControl->getFieldOne("smc_class", "class_id", "class_branch='{$request['class_branch']}'");
            $studentOne = $this->DataControl->getFieldOne("smc_student", "student_id", "student_branch='{$request['student_branch']}'");
            $data = array();
            $data['hour_bookcheck'] = '1';
            $hour_branch = $courseOne['course_branch'] . '_' . $request['hour_lessontimes'];
            $easStudentOne = $this->DataControl->selectOne("select student_id from eas_student_hour where class_id='{$classOne['class_id']}' and student_id='{$studentOne['student_id']}' and hour_branch='{$hour_branch}'  ");
            if ($easStudentOne) {
                $data['hour_updatatime'] = time();
                $this->DataControl->updateData("eas_student_hour", "class_id='{$classOne['class_id']}' and student_id='{$studentOne['student_id']}' and hour_branch='{$hour_branch}'", $data);
                return true;
            } else {
                $data = array();
                $data['student_id'] = $studentOne['student_id'];
                $data['class_id'] = $classOne['class_id'];
                $data['hour_branch'] = $courseOne['course_branch'] . '_' . $request['hour_lessontimes'];
                $data['hour_name'] = 'Lesson' . '' . $request['hour_lessontimes'];
                $data['hour_bookcheck'] = '1';
                $data['hour_createtime'] = time();
                $this->DataControl->insertData("eas_student_hour", $data);
                return true;
            }
        }
    }

    function stafferWxView()
    {
        $request = Input("get.", "", "addslashes");
        $staffer = $this->DataControl->getFieldOne("smc_staffer", "staffer_wxtoken,staffer_wximg", "staffer_id = '{$request['staffer_id']}'");
        if ($staffer['staffer_wxtoken']) {
            $res = array('error' => 0, 'errortip' => "获取成功", 'result' => $staffer);
        } else {
            $res = array('error' => 1, 'errortip' => "尚未绑定", 'result' => array());
        }
        ajax_return($res);
    }

    function unbindAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $data = array();
        $data['staffer_wxtoken'] = '';
        $data['staffer_wximg'] = '';
        $data['staffer_updatetime'] = time();
        $this->DataControl->updateData("smc_staffer", "staffer_id = '{$request['staffer_id']}'", $data);
        ajax_return(array('error' => 0, 'errortip' => "解绑成功!"));
    }

    function trackTypeApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $sql = "select tracktype_id,tracktype_name FROM smc_code_tracktype 
                where company_id = '{$request['company_id']}' 
                and tracktype_eas=1
                ORDER BY tracktype_id ASC";
        $list = $this->DataControl->selectClear($sql);
        if ($list) {
            $result["list"] = $list;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '获取信息', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }


}