<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/13
 * Time: 23:25
 */

namespace Work\Controller\Easxapi;

class EducationController extends viewTpl
{
    public $u;
    public $t;
    public $c;
    public $visitType = "api";
    public $backData;

    //预加载处理类
    function __construct($visitType = "api")
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
    }


    //本地权限校验入口
    function ThisVerify($request)
    {

        if (!intval($request['staffer_id'])) {
            $res = array('error' => '1', 'errortip' => "未检测到招生人员信息，请重新登录操作", 'result' => array());
            ajax_return($res);
        }
        if (empty($request['token'])) {
            $res = array('error' => '1', 'errortip' => "未传入用户Token信息", 'result' => array());
            ajax_return($res);
        }
        $paramArray = array();
        $paramArray['staffer_id'] = $request['staffer_id'];
        $paramArray['token'] = $request['token'];
        if (!$this->UserLimit($paramArray)) {
            $result = array();
            $result["list"] = array();
            $result["tokeninc"] = "0";
            $res = array('error' => '1', 'errortip' => "用户token失效", 'result' => $result);
            ajax_return($res);
        }
    }

    /**
     * 教务管理-小循环-我的班级
     * author: ling
     * 对应接口文档 0001
     */

    function getStaClassListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Easx\EducationModel($request);
        if ($Model->error) {
            $result['is_power'] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
            ajax_return($res);
        }

        $field = array();
        $k = 0;
        $field[$k]["fieldname"] = "校园";
        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "班级名称";
        $field[$k]["fieldstring"] = "class_cnname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "班级别名";
        $field[$k]["fieldstring"] = "class_enname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "班级编号";
        $field[$k]["fieldstring"] = "class_branch";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        if ($request['account_class'] == '1' || $Model->stafferOne['postpart_isteregulator'] == '1') {
            $field[$k]["fieldname"] = "主教中文名";
            $field[$k]["fieldstring"] = "staffer_cnname";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;
            $field[$k]["fieldname"] = "主教英文名";
            $field[$k]["fieldstring"] = "staffer_enname";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;
        }


        $field[$k]["fieldname"] = "课程别";
        $field[$k]["fieldstring"] = "course_cnname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "课程编号";
        $field[$k]["fieldstring"] = "course_branch";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "人数";
        $field[$k]["fieldstring"] = "student_nums";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["isProgress"] = 1;
        $k++;
        $field[$k]["fieldname"] = "教室";
        $field[$k]["fieldstring"] = "classroom_cnname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "状态";
        $field[$k]["fieldstring"] = "class_status_name";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;
        $field[$k]["fieldname"] = "满班人数";
        $field[$k]["fieldstring"] = "class_fullnums";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;
        $field[$k]["fieldname"] = "在班人数";
        $field[$k]["fieldstring"] = "study_num";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;

        $dataList = $Model->getStaClassList($request);
        $result["field"] = $field;
        $result["allnum"] = $dataList['allnum'];
        if ($dataList["list"]) {
            $result["field"] = $field;
            $result["list"] = $dataList['list'];
            $res = array('error' => '0', 'errortip' => '获取我的班级成功', 'allnum' => $dataList['allnum'], 'result' => $result);
        } else {
            $result["list"] = array();
            if ($request['account_class'] == '1' || $Model->stafferOne['postpart_isteregulator'] == '1') {
                $res = array('error' => '1', 'errortip' => '啊哦，暂无班级信息喔，请选择教师筛选~', 'allnum' => 0, 'result' => $result);
            } else {
                $res = array('error' => '1', 'errortip' => '啊哦，暂无班级信息喔~', 'allnum' => 0, 'result' => $result);
            }
        }
        ajax_return($res);
    }

    /**
     * 教务管理-登记分数
     * author: ling
     * 对应接口文档 0001
     */
    function fillStuWorkScoreAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Easx\EducationModel($request);
        if ($Model->error) {
            $result['is_power'] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
            ajax_return($res);
        }

        $Model->fillStuWorkScoreAction($request);
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $Model->backData);
        ajax_return($res);
    }


    /**
     * 教务管理_一键登记
     * author: ling
     * 对应接口文档 0001
     */
    function fillClassHourNetstatusAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Easx\EducationModel($request);
        if ($Model->error) {
            $result['is_power'] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
            ajax_return($res);
        }

        $Model->fillClassHourNetstatus($request);
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $Model->backData);
        ajax_return($res);
    }

    /**
     * 班级课时
     * author: ling
     * 对应接口文档 0001
     */
    function getClassHourListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Easx\EducationModel($request);
        if ($Model->error) {
            $result['is_power'] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
            ajax_return($res);
        }

        $field = array();
        $k = 0;
        $field[$k]["fieldname"] = "班级名称";
        $field[$k]["fieldstring"] = "class_cnname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;
        $field[$k]["fieldname"] = "课时名称";
        $field[$k]["fieldstring"] = 'hour_name';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;


        $dataList = $Model->getClassHourListApi($request);
        $result["field"] = $field;
        if ($dataList) {
            $result["list"] = $dataList['list'];
            if ($result["list"]) {
                $res = array('error' => '0', 'errortip' => '获取课时', 'allnum' => $dataList['allnums'], 'result' => $result);
            } else {
                $result["list"] = array();
                $res = array('error' => '1', 'errortip' => '暂无课时', 'allnum' => 0, 'result' => $result);
            }
        } else {
            $result["list"] = array();
            $res = array('error' => '1', 'errortip' => '暂无课时', 'allnum' => 0, 'result' => $result);
        }
        ajax_return($res);
    }


    /**
     * 获取班级在读人员
     * author: ling
     * 对应接口文档 0001
     */
    function getStudyStudentListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Easx\EducationModel($request);
        if ($Model->error) {
            $result['is_power'] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
            ajax_return($res);
        }

        $course_id = $this->DataControl->getFieldOne("smc_class_hour","course_id,hour_name","hour_id = '{$request['hour_id']}'");
        $course_branch = $this->DataControl->getFieldOne("smc_course","course_branch","course_id = '{$course_id['course_id']}'");
        $status = $this->DataControl->getFieldOne("eas_teachhour","teachhour_id,teachhour_isscore,teachhour_scoretype,teachhour_isoffline,teachhour_isonline,teachhour_isonscore,teachhour_onscoretype","classcode_branch = '{$course_branch['course_branch']}' and teachhour_name = '{$course_id['hour_name']}' and company_id = '{$request['company_id']}'");

        $field = array();
        $k = 0;
        $field[$k]["fieldname"] = "学员中文名";
        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "学员英文名";
        $field[$k]["fieldstring"] = "student_enname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "学员编号";
        $field[$k]["fieldstring"] = 'student_branch';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "性别";
        $field[$k]["fieldstring"] = 'student_sex';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "生日";
        $field[$k]["fieldstring"] = 'student_birthday';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "入班日期";
        $field[$k]["fieldstring"] = 'study_beginday';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
//        if (isset($request['is_catitrack']) && $request['is_catitrack'] == 1) {
//            $k++;
//            $field[$k]["fieldname"] = "电访次数";
//            $field[$k]["fieldstring"] = 'track_num';
//            $field[$k]["show"] = 1;
//            $field[$k]["custom"] = 1;
//            $k++;
//            $field[$k]["fieldname"] = "上次访谈日期";
//            $field[$k]["fieldstring"] = 'track_day';
//            $field[$k]["show"] = 1;
//            $field[$k]["custom"] = 1;
//        }

        if (isset($request['is_classtype']) && $request['is_classtype'] == 0) {

            if($status['teachhour_isscore'] == '1'){
                $k++;
                $field[$k]["fieldname"] = "考试成绩";
                $field[$k]["fieldstring"] = 'hour_testscore';
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $field[$k]["isInputText"] = true;
                $field[$k]["disabled"] = false;
                $k++;
                $field[$k]["fieldname"] = "补考成绩";
                $field[$k]["fieldstring"] = 'hour_resitscore';
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $field[$k]["isInputText"] = true;
                $field[$k]["disabled"] = false;
                if($status['teachhour_scoretype']) {
                    $k++;
                    $field[$k]["fieldname"] = "所属考试类型";
                    $field[$k]["fieldstring"] = 'teachhour_scoretype';
                    $field[$k]["show"] = 1;
                    $field[$k]["custom"] = 1;
                    $field[$k]["isInputText"] = false;
                    $field[$k]["disabled"] = false;
                }
            }
            if($status['teachhour_isoffline'] == '1'){
                $k++;
                $field[$k]["fieldname"] = "线下作业";
                $field[$k]["fieldstring"] = 'hour_homework_name';
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $field[$k]["isSelectHomework"] = true;
                $field[$k]["disabled"] = false;
                $k++;
                $field[$k]["fieldname"] = "作业分数";
                $field[$k]["fieldstring"] = 'hour_homeworkscores';
                $field[$k]["show"] = 0;
                $field[$k]["custom"] = 1;
                $field[$k]["isInputText"] = true;
                $field[$k]["disabled"] = false;
            }

            $k++;
            $field[$k]["fieldname"] = "小书检核";
            $field[$k]["fieldstring"] = 'hour_bookcheck_name';
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $field[$k]["isSelect"] = true;
        }

        if (isset($request['is_classtype']) && $request['is_classtype'] == 1) {
            if($status['teachhour_isonline'] == '1'){
                $k++;
                $field[$k]["fieldname"] = "网课状态";
                $field[$k]["fieldstring"] = 'hour_netstatus_name';
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $field[$k]["isSelect"] = true;
                $k++;
                $field[$k]["fieldname"] = "网课状态";
                $field[$k]["fieldstring"] = 'hour_netstatus';
                $field[$k]["show"] = 0;
                $field[$k]["custom"] = 0;
                $field[$k]["isSelect"] = true;
            }
            if($status['teachhour_isonscore'] == '1'){
                $k++;
                $field[$k]["fieldname"] = "网课分数";
                $field[$k]["fieldstring"] = 'hour_netscores';
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $field[$k]["isInputText"] = true;
                $field[$k]["disabled"] = false;
                if($status['teachhour_onscoretype']){
                    $k++;
                    $field[$k]["fieldname"] = "所属考试类型";
                    $field[$k]["fieldstring"] = 'teachhour_onscoretype';
                    $field[$k]["show"] = 1;
                    $field[$k]["custom"] = 1;
                    $field[$k]["isInputText"] = true;
                    $field[$k]["disabled"] = false;
                }
            }


        }
        if (isset($request['is_classtype']) && $request['is_classtype'] == 2) {
            $k++;
            $field[$k]["fieldname"] = "出勤状态";
            $field[$k]["fieldstring"] = 'hour_check_name';
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;
            $field[$k]["fieldname"] = "补课状态";
            $field[$k]["fieldstring"] = 'hour_refresher_name';
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $field[$k]["isSelect"] = true;
            $k++;

            $field[$k]["fieldname"] = "补课状态值";
            $field[$k]["fieldstring"] = 'hour_refresher';
            $field[$k]["show"] = 0;
            $field[$k]["custom"] = 0;
            $field[$k]["isSelect"] = true;
        }


        $dataList = $Model->getStudyStudentListApi($request);
        $result["field"] = $field;
        if ($dataList) {
            $result["list"] = $dataList['list'];
            $result["allnum"] = $dataList['allnum'];

            if ($result["list"]) {
                $res = array('error' => '0', 'errortip' => '获取课时', 'allnum' => $dataList['allnum'], 'result' => $result, 'status' => $status);
            } else {
                $result["list"] = array();
                $res = array('error' => '1', 'errortip' => '该课时暂无学员~', 'allnum' => 0, 'result' => $result);
            }
        } else {
            $result["list"] = array();
            $res = array('error' => '1', 'errortip' => '该课时暂无学员~', 'allnum' => 0, 'result' => $result);
        }
        ajax_return($res);

    }


    /**
     * 获取班级在读人员 -- 电访管理
     * author: ling
     * 对应接口文档 0001
     */
    function getStudentCatitrackListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Easx\EducationModel($request);
        if ($Model->error) {
            $result['is_power'] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
            ajax_return($res);
        }

        $field = array();
        $k = 0;
        $field[$k]["fieldname"] = "学员中文名";
        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "学员英文名";
        $field[$k]["fieldstring"] = "student_enname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "学员编号";
        $field[$k]["fieldstring"] = 'student_branch';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "联系电话";
        $field[$k]["fieldstring"] = 'family_mobile';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "性别";
        $field[$k]["fieldstring"] = 'student_sex';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "生日";
        $field[$k]["fieldstring"] = 'student_birthday';
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "入班日期";
        $field[$k]["fieldstring"] = 'study_beginday';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "电访状态";
        $field[$k]["fieldstring"] = 'track_class';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $dataList = $Model->getStudentCatitrackListApi($request);
        $result["field"] = $field;
        if ($dataList) {
            $result["list"] = $dataList['list'];
            $result["interval"] = $dataList['interval'];
            $result["class"] = $dataList['class'];

            if ($result["list"]) {
                $res = array('error' => '0', 'errortip' => '获取课时', 'allnum' => $dataList['allnum'], 'result' => $result);
            } else {
                $result["list"] = array();
                $res = array('error' => '1', 'errortip' => '暂无课时', 'allnum' => 0, 'result' => $result);
            }
        } else {
            $result["list"] = array();
            $res = array('error' => '1', 'errortip' => '暂无课时', 'allnum' => 0, 'result' => $result);
        }
        ajax_return($res);
    }


    /**
     * 获取访谈沟通对象
     * author: ling
     * 对应接口文档 0001
     */
    function getCatitrackCodeApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Easx\EducationModel($request);
        if ($Model->error) {
            $result['is_power'] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
            ajax_return($res);
        }

        $dataList = $Model->getCatitrackCodeApi();
        $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $dataList);
        ajax_return($res);

    }

    /**
     * 新增访谈记录
     * author: ling
     * 对应接口文档 0001
     */
    function addStuCatitrackAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Easx\EducationModel($request);
        if ($Model->error) {
            $result['is_power'] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
            ajax_return($res);
        }

        $Model->addStuCatitrackAction($request);
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res);
    }

    /**
     * 获取沟通类型
     * author: ling
     * 对应接口文档 0001
     */
    function getTrackTypeApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $company_id = $request['company_id'];

        if (!$company_id) {
            $res = array('error' => '1', 'errortip' => "公司id有误", 'result' => array());
            ajax_return($res, $request['language_type']);
        }

        $Model = new \Model\Crm\PublicModel();
        $dataList = $Model->getTrackTypeApi($company_id);

        $result = array();
        if ($dataList) {
            $result['list'] = $dataList;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        }

        ajax_return($res);
    }

    /**
     * 获取沟通方式
     * author: ling
     * 对应接口文档 0001
     */
    function getCommodeApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $company_id = $request['company_id'];

        if (!$company_id) {
            $res = array('error' => '1', 'errortip' => "公司id有误", 'result' => array());
            ajax_return($res, $request['language_type']);
        }

        $Model = new \Model\Crm\PublicModel();
        $dataList = $Model->getCommedeByCompanyId($company_id);

        $result = array();
        if ($dataList) {
            $result['list'] = $dataList;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        }

        ajax_return($res);
    }

    /**
     * 获取沟通结果
     * author: ling
     * 对应接口文档 0001
     */
    function getResultTypeApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $company_id = $request['company_id'];

        if (!$company_id) {
            $res = array('error' => '1', 'errortip' => "公司id有误", 'result' => array());
            ajax_return($res, $request['language_type']);
        }

        $Model = new \Model\Crm\PublicModel();
        $dataList = $Model->getResultTypeApi($company_id, $request['tracktype_id']);

        $result = array();
        if ($dataList) {
            $result['list'] = $dataList;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        }

        ajax_return($res);
    }

    /**
     * 获取访谈记录
     * author: ling
     * 对应接口文档 0001
     */
    function getCatitrackApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Easx\EducationModel($request);
        if ($Model->error) {
            $result['is_power'] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
            ajax_return($res);
        }

        $dataList = $Model->getCatitrackApi($request);

        $result = array();
        $result['catitrack'] = $dataList['catitrack'];
        if ($dataList) {
            $result["list"] = $dataList['list'];
            if ($result["list"]) {
                $res = array('error' => '0', 'errortip' => '获取课时', 'allnum' => $dataList['allnum'], 'result' => $result);
            } else {
                $result["list"] = array();
                $res = array('error' => '1', 'errortip' => '暂无课时', 'allnum' => 0, 'result' => $result);
            }
        } else {

            $result["list"] = array();
            $res = array('error' => '1', 'errortip' => '暂无课时', 'allnum' => 0, 'result' => $result);
        }
        ajax_return($res);
    }

    /**
     * 获取电访沟通模板
     * author: ling
     * 对应接口文档 0001
     */
    function getComtempApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Easx\EducationModel($request);
        if ($Model->error) {
            $result['is_power'] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
            ajax_return($res);
        }

        $dataList = $Model->getComtempApi($request);

        $result = array();
        if ($dataList) {
            $result["list"] = $dataList;
            $res = array('error' => '0', 'errortip' => '获取沟通模板', 'allnum' => 0, 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => '1', 'errortip' => '暂无沟通模板', 'allnum' => 0, 'result' => $result);
        }
        ajax_return($res);
    }


    /**
     * 获取班级在读人员 -- 小学成绩登记
     * author: wgh
     * 对应接口文档 0001
     */
    function getStudentScoreView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Easx\EducationModel($request);
        if ($Model->error) {
            $result['is_power'] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
            ajax_return($res);
        }

        $field = array();
        $k = 0;
        $field[$k]["fieldname"] = "学员中文名";
        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "学员英文名";
        $field[$k]["fieldstring"] = "student_enname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "学员编号";
        $field[$k]["fieldstring"] = 'student_branch';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "性别";
        $field[$k]["fieldstring"] = 'student_sex';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "生日";
        $field[$k]["fieldstring"] = 'student_birthday';
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "入班日期";
        $field[$k]["fieldstring"] = 'study_beginday';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "登记状态";
        $field[$k]["fieldstring"] = 'score_class';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $dataList = $Model->getStudentScoreApi($request);
        $result["field"] = $field;
        if ($dataList['list']) {
            $result["list"] = $dataList['list'];
            $res = array('error' => '0', 'errortip' => '获取课时', 'allnum' => $dataList['allnum'], 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => '1', 'errortip' => '暂无课时', 'allnum' => 0, 'result' => $result);
        }
        ajax_return($res);
    }

    /**
     * 获取历史成绩
     * author: wgh
     * 对应接口文档 0001
     */
    function getScoresApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Easx\EducationModel($request);
        if ($Model->error) {
            $result['is_power'] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
            ajax_return($res);
        }

        $dataList = $Model->getScoresApi($request);

        $result = array();
        $result['catitrack'] = $dataList['catitrack'];
        if ($dataList['list']) {
            $result["list"] = $dataList['list'];
            $res = array('error' => '0', 'errortip' => '获取课时', 'allnum' => $dataList['allnum'], 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => '1', 'errortip' => '暂无课时', 'allnum' => 0, 'result' => $result);
        }
        ajax_return($res);
    }

    /**
     * 添加成绩
     * author: wgh
     * 对应接口文档 0001
     */
    function addStuScoreAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Easx\EducationModel($request);
        if ($Model->error) {
            $result['is_power'] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
            ajax_return($res);
        }

        $Model->addStuScoreAction($request);
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res);
    }

    /**
     * 编辑成绩
     * author: wgh
     * 对应接口文档 0001
     */
    function editStuScoreAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Easx\EducationModel($request);
        if ($Model->error) {
            $result['is_power'] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
            ajax_return($res);
        }

        $Model->editStuScoreAction($request);
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res);
    }

    //班级统计报表
    function getStaClassReportView()
    {

        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Easx\EducationModel($request);
        if ($Model->error) {
            $result['is_power'] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
            ajax_return($res);
        }

        $field = array();
        $k = 0;
        $field[$k]["fieldname"] = "校园名称";
        $field[$k]["fieldstring"] = "school_shortname";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "校园编号";
        $field[$k]["fieldstring"] = 'school_branch';
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "班级名称";
        $field[$k]["fieldstring"] = 'class_cnname';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "班级别名";
        $field[$k]["fieldstring"] = 'class_enname';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "班级编号";
        $field[$k]["fieldstring"] = 'class_branch';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "课程名称";
        $field[$k]["fieldstring"] = 'course_cnname';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "课程编号";
        $field[$k]["fieldstring"] = 'course_branch';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "教师中文名";
        $field[$k]["fieldstring"] = 'staffer_cnname';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "教师英文名";
        $field[$k]["fieldstring"] = 'staffer_enname';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "教师编号";
        $field[$k]["fieldstring"] = 'staffer_branch';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "班级状态";
        $field[$k]["fieldstring"] = 'class_status_name';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "学员数";
        $field[$k]["fieldstring"] = 'study_num';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "课时进度";
        $field[$k]["fieldstring"] = 'hour_progress';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["isProgress"] = true;
        $dataList = $Model->getStaClassReport($request);
        $result["field"] = $field;
        $result["allnum"] = $dataList['allnum'];
        if ($dataList) {
            $result["list"] = $dataList['list'];
            if ($result["list"]) {
                $res = array('error' => '0', 'errortip' => '获取课时', 'allnum' => $dataList['allnum'], 'result' => $result);
            } else {
                $result["list"] = array();
                $result["field"] = $field;
                $res = array('error' => '1', 'errortip' => '暂无课时', 'allnum' => 0, 'result' => $result);
            }
        } else {

            $result["list"] = array();
            $result["field"] = $field;
            $res = array('error' => '1', 'errortip' => '暂无课时', 'allnum' => 0, 'result' => $result);
        }
        ajax_return($res);
    }

    //平常时期电访执行统计表
    function getCatitrackReportView()
    {

        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Easx\EducationModel($request);
        if ($Model->error) {
            $result['is_power'] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
            ajax_return($res);
        }

        $field = array();
        $k = 0;
        $field[$k]["fieldname"] = "校园名称";
        $field[$k]["fieldstring"] = "school_shortname";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "校园编号";
        $field[$k]["fieldstring"] = 'school_branch';
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "班级名称";
        $field[$k]["fieldstring"] = 'class_cnname';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "班级别名";
        $field[$k]["fieldstring"] = 'class_enname';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "班级编号";
        $field[$k]["fieldstring"] = 'class_branch';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "课程名称";
        $field[$k]["fieldstring"] = 'course_cnname';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "课程编号";
        $field[$k]["fieldstring"] = 'course_branch';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "主教中文名";
        $field[$k]["fieldstring"] = 'staffer_cnname';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "主教英文名";
        $field[$k]["fieldstring"] = 'staffer_enname';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "班级状态";
        $field[$k]["fieldstring"] = 'class_status_name';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "学员数";
        $field[$k]["fieldstring"] = 'study_num';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "已电访学员数";
        $field[$k]["fieldstring"] = 'track_num';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "电访率";
        $field[$k]["fieldstring"] = 'rate';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $dataList = $Model->getCatitrackReport($request);
        $result["field"] = $field;
        $result["allnum"] = $dataList['allnum'];
        if ($dataList) {
            $result["list"] = $dataList['list'];
            if ($result["list"]) {
                $res = array('error' => '0', 'errortip' => '获取课时', 'allnum' => $dataList['allnum'], 'result' => $result);
            } else {
                $result["list"] = array();
                $res = array('error' => '1', 'errortip' => '暂无课时', 'allnum' => 0, 'result' => $result);
            }
        } else {
            $result["list"] = array();
            $res = array('error' => '1', 'errortip' => '暂无课时', 'allnum' => 0, 'result' => $result);
        }
        ajax_return($res);
    }

    //校园平常时期电访执行统计表
    function getCatitrackScReportView()
    {

        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Easx\EducationModel($request);
        if ($Model->error) {
            $result['is_power'] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
            ajax_return($res);
        }

        $field = array();
        $k = 0;
        $field[$k]["fieldname"] = "所属区域";
        $field[$k]["fieldstring"] = "district_cnname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["fieldstring"] = 'school_branch';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "班级数量";
        $field[$k]["fieldstring"] = 'class_num';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "学员数";
        $field[$k]["fieldstring"] = 'stu_num';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "已电访学员数";
        $field[$k]["fieldstring"] = 'track_num';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "电访率";
        $field[$k]["fieldstring"] = 'rate';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $dataList = $Model->getCatitrackScReport($request);
        $result["field"] = $field;
        $result["allnum"] = $dataList['allnum'];
        if ($dataList) {
            $result["list"] = $dataList['list'];
            if ($result["list"]) {
                $res = array('error' => '0', 'errortip' => '获取课时', 'allnum' => $dataList['allnum'], 'result' => $result);
            } else {
                $result["list"] = array();
                $res = array('error' => '1', 'errortip' => '暂无课时', 'allnum' => 0, 'result' => $result);
            }
        } else {
            $result["list"] = array();
            $res = array('error' => '1', 'errortip' => '暂无课时', 'allnum' => 0, 'result' => $result);
        }
        ajax_return($res);
    }

    //小学成绩登记统计表
    function getStaScoreView()
    {

        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Easx\EducationModel($request);
        if ($Model->error) {
            $result['is_power'] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
            ajax_return($res);
        }

        $field = array();
        $k = 0;
        $field[$k]["fieldname"] = "校园名称";
        $field[$k]["fieldstring"] = "school_shortname";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "校园编号";
        $field[$k]["fieldstring"] = 'school_branch';
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "班级名称";
        $field[$k]["fieldstring"] = 'class_cnname';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "班级别名";
        $field[$k]["fieldstring"] = 'class_enname';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "班级编号";
        $field[$k]["fieldstring"] = 'class_branch';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "课程名称";
        $field[$k]["fieldstring"] = 'course_cnname';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "课程编号";
        $field[$k]["fieldstring"] = 'course_branch';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "主教中文名";
        $field[$k]["fieldstring"] = 'staffer_cnname';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "主教英文名";
        $field[$k]["fieldstring"] = 'staffer_enname';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "班级状态";
        $field[$k]["fieldstring"] = 'class_status_name';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "学员数";
        $field[$k]["fieldstring"] = 'study_num';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "已登记学员数";
        $field[$k]["fieldstring"] = 'study_registernum';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "登记率";
        $field[$k]["fieldstring"] = 'register_rate';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $dataList = $Model->getStaScore($request);
        $result["field"] = $field;
        if ($dataList['list']) {
            $result["list"] = $dataList['list'];
            $res = array('error' => '0', 'errortip' => '获取课时', 'allnum' => $dataList['allnum'], 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => '1', 'errortip' => '暂无课时', 'allnum' => 0, 'result' => $result);
        }
        ajax_return($res);
    }

    //校园小学成绩登记统计表
    function getScoreReportView()
    {

        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Easx\EducationModel($request);
        if ($Model->error) {
            $result['is_power'] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
            ajax_return($res);
        }

        $field = array();
        $k = 0;
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["fieldstring"] = 'school_branch';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "班级数量";
        $field[$k]["fieldstring"] = 'class_num';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "学员数";
        $field[$k]["fieldstring"] = 'stu_num';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "已登记学员数";
        $field[$k]["fieldstring"] = 'study_registernum';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "登记率";
        $field[$k]["fieldstring"] = 'register_rate';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $dataList = $Model->getScoreReport($request);
        $result["field"] = $field;
        if ($dataList['list']) {
            $result["list"] = $dataList['list'];
            $res = array('error' => '0', 'errortip' => '获取课时', 'allnum' => $dataList['allnum'], 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => '1', 'errortip' => '暂无课时', 'allnum' => 0, 'result' => $result);
        }
        ajax_return($res);
    }

    //校园小学成绩登记详情统计表
    function getScoreReportDetailView()
    {

        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Easx\EducationModel($request);
        if ($Model->error) {
            $result['is_power'] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
            ajax_return($res);
        }

        $field = array();
        $k = 0;
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["fieldstring"] = 'school_branch';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "班级名称";
        $field[$k]["fieldstring"] = 'class_cnname';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "班级别名";
        $field[$k]["fieldstring"] = 'class_enname';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "班级编号";
        $field[$k]["fieldstring"] = 'class_branch';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "课程名称";
        $field[$k]["fieldstring"] = 'course_cnname';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "学员中文名";
        $field[$k]["fieldstring"] = 'student_cnname';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "学员英文名";
        $field[$k]["fieldstring"] = 'student_enname';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "学员编号";
        $field[$k]["fieldstring"] = 'student_branch';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "性别";
        $field[$k]["fieldstring"] = 'student_sex';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "入班日期";
        $field[$k]["fieldstring"] = 'study_beginday';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "考试年份";
        $field[$k]["fieldstring"] = 'score_year';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "所属年级";
        $field[$k]["fieldstring"] = 'score_grade_name';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "所在考试学期";
        $field[$k]["fieldstring"] = 'score_term_name';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "考前辅导分数";
        $field[$k]["fieldstring"] = 'score_tutoring';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "小学成绩分数";
        $field[$k]["fieldstring"] = 'score_kidschool';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "登记时间";
        $field[$k]["fieldstring"] = 'score_createtime';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $dataList = $Model->getScoreReportDetail($request);
        $result["field"] = $field;
        if ($dataList['list']) {
            $result["list"] = $dataList['list'];
            $res = array('error' => '0', 'errortip' => '获取登记成绩数据', 'allnum' => $dataList['allnum'], 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => '1', 'errortip' => '暂无登记成绩数据哦~', 'allnum' => 0, 'result' => $result);
        }
        ajax_return($res);
    }

    //校园班平统计表
    function getScClassAverageView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Easx\EducationModel($request);
        if ($Model->error) {
            $result['is_power'] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
            ajax_return($res);
        }

        $field = array();
        $k = 0;
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["fieldstring"] = 'school_branch';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "所属区域";
        $field[$k]["fieldstring"] = 'district_cnname';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "在读总人数";
        $field[$k]["fieldstring"] = 'stu_num';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "班级数量";
        $field[$k]["fieldstring"] = 'class_num';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "班级平均人数";
        $field[$k]["fieldstring"] = 'class_averagenum';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "15天内结班情况";
        $field[$k]["fieldstring"] = 'endclass_info';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;


        $dataList = $Model->getScClassAverage($request);
        $result["field"] = $field;
        if ($dataList['list']) {
            $result["list"] = $dataList['list'];
            $res = array('error' => '0', 'errortip' => '获取课时', 'allnum' => $dataList['allnum'], 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => '1', 'errortip' => '暂无课时', 'allnum' => 0, 'result' => $result);
        }
        ajax_return($res);
    }

    //校园班平教师统计表
    function scClassTeaacherView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Easx\EducationModel($request);
        if ($Model->error) {
            $result['is_power'] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
            ajax_return($res);
        }

        $field = array();
        $k = 0;
        $field[$k]["fieldname"] = "校园名称";
        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "校园编号";
        $field[$k]["fieldstring"] = 'school_branch';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "教师中文名";
        $field[$k]["fieldstring"] = 'staffer_cnname';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "教师英文名";
        $field[$k]["fieldstring"] = 'staffer_enname';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "教师编号";
        $field[$k]["fieldstring"] = 'staffer_branch';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "带班总人数";
        $field[$k]["fieldstring"] = 'stu_num';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "带班级数";
        $field[$k]["fieldstring"] = 'teaching_num';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "班级平均人数";
        $field[$k]["fieldstring"] = 'class_averagenum';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $dataList = $Model->scClassTeaacher($request);
        $result["field"] = $field;
        if ($dataList['list']) {
            $result["list"] = $dataList['list'];
            $res = array('error' => '0', 'errortip' => '获取课时', 'allnum' => $dataList['allnum'], 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => '1', 'errortip' => '暂无课时', 'allnum' => 0, 'result' => $result);
        }
        ajax_return($res);
    }

    /**
     * 课时统计
     * author: ling
     * 对应接口文档 0001
     */
    function getClassHourReportView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Easx\EducationModel($request);
        if ($Model->error) {
            $result['is_power'] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
            ajax_return($res);
        }

        $field = array();
        if ($request['hour_id']) {
            $k = 0;
            $field[$k]["fieldname"] = "学员中文名";
            $field[$k]["fieldstring"] = "student_cnname";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;
            $field[$k]["fieldname"] = "学员英文名";
            $field[$k]["fieldstring"] = 'student_enname';
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;
            $field[$k]["fieldname"] = "学员编号";
            $field[$k]["fieldstring"] = 'student_branch';
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;
            $field[$k]["fieldname"] = "性别";
            $field[$k]["fieldstring"] = 'student_sex';
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;
            $field[$k]["fieldname"] = "线上任务";
            $field[$k]["fieldstring"] = 'up_taskitem_rate';
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;
            $field[$k]["fieldname"] = "录播任务";
            $field[$k]["fieldstring"] = 'taskitem_status_name';
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;
            $field[$k]["fieldname"] = "线上检测分数";
            $field[$k]["fieldstring"] = 'examine_score';
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;
            $field[$k]["fieldname"] = "mini book检核状况";
            $field[$k]["fieldstring"] = 'review_status_name';
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;
            $field[$k]["fieldname"] = "配音平均分数";
            $field[$k]["fieldstring"] = 'avg_audio_taskitem_score';
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;
            $field[$k]["fieldname"] = "网课参与状况";
            $field[$k]["fieldstring"] = 'hour_netstatus_name';
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;
            $field[$k]["fieldname"] = "网课检测分数";
            $field[$k]["fieldstring"] = 'hour_netscores';
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;
            $field[$k]["fieldname"] = "家长满意度";
            $field[$k]["fieldstring"] = 'classhour_memberstar';
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;
            $field[$k]["fieldname"] = "线下作业完成状况";
            $field[$k]["fieldstring"] = 'hour_homework_name';
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
        } else {
            $k = 0;
            $field[$k]["fieldname"] = "学员中文名";
            $field[$k]["fieldstring"] = "student_cnname";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;
            $field[$k]["fieldname"] = "学员英文名";
            $field[$k]["fieldstring"] = 'student_enname';
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;
            $field[$k]["fieldname"] = "学员编号";
            $field[$k]["fieldstring"] = 'student_branch';
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;
            $field[$k]["fieldname"] = "性别";
            $field[$k]["fieldstring"] = 'student_sex';
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;
            $field[$k]["fieldname"] = "线上任务平均完成率";
            $field[$k]["fieldstring"] = 'up_taskitem_rate';
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;
            $field[$k]["fieldname"] = "录播任务平均完成率";
            $field[$k]["fieldstring"] = 'video_taskitem_rate';
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;
            $field[$k]["fieldname"] = "线上检测平均分数";
            $field[$k]["fieldstring"] = 'avg_test_score';
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;
            $field[$k]["fieldname"] = "mini book平均完成率";
            $field[$k]["fieldstring"] = 'hour_bookcheck_rate';
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;
            $field[$k]["fieldname"] = "配音平均分数";
            $field[$k]["fieldstring"] = 'avg_audio_taskitem_score';
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;
            $field[$k]["fieldname"] = "网课平均参与率";
            $field[$k]["fieldstring"] = 'hour_netstatus_rate';
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;
            $field[$k]["fieldname"] = "网课检测平均分数";
            $field[$k]["fieldstring"] = 'hour_netstatus_scores';
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;
            $field[$k]["fieldname"] = "家长平均满意度";
            $field[$k]["fieldstring"] = 'classhour_memberstar';
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;
            $field[$k]["fieldname"] = "线下作业平均任务完成率";
            $field[$k]["fieldstring"] = 'under_taskitem_rate';
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;
            $field[$k]["fieldname"] = "线下成绩平均分数";
            $field[$k]["fieldstring"] = 'hour_test_scores';
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
        }

        $result["field"] = $field;

        $dataList = $Model->getClassHourReport($request);
        $result["info"] = $dataList['info'];
        if ($dataList) {

            $result["list"] = $dataList['list'];
            $result["allnum"] = $dataList['allnum'];

            if ($result["list"]) {
                $res = array('error' => '0', 'errortip' => '获取课时', 'allnum' => $dataList['allnum'], 'result' => $result);
            } else {
                $result["list"] = array();
                $res = array('error' => '1', 'errortip' => '暂无课时', 'allnum' => 0, 'result' => $result);
            }
        } else {

            $result["list"] = array();
            $res = array('error' => '1', 'errortip' => '暂无课时', 'allnum' => 0, 'result' => $result);
        }
        ajax_return($res);

    }


    /**
     * 电访记录统计
     * author: ling
     * 对应接口文档 0001
     */
    function getStuCatitrackView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Easx\EducationModel($request);
        if ($Model->error) {
            $result['is_power'] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
            ajax_return($res);
        }

        $field = array();
        $k = 0;
        $field[$k]["fieldname"] = "学员中文名";
        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "学员英文名";
        $field[$k]["fieldstring"] = 'student_enname';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "学员编号";
        $field[$k]["fieldstring"] = 'student_branch';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "性别";
        $field[$k]["fieldstring"] = 'student_sex';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "电访日期";
        $field[$k]["fieldstring"] = 'track_day';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "沟通对象";
        $field[$k]["fieldstring"] = 'object_name';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "沟通内容";
        $field[$k]["fieldstring"] = 'track_note';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "提交人";
        $field[$k]["fieldstring"] = 'staffer_cnname';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "提交时间";
        $field[$k]["fieldstring"] = 'track_createtime';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;


        $dataList = $Model->getStuCatitrack($request);
        $result["field"] = $field;
        $result["info"] = $dataList['info'];
        if ($dataList) {

            $result["list"] = $dataList['list'];
            $result["allnum"] = $dataList['allnum'];
            if ($result["list"]) {
                $res = array('error' => '0', 'errortip' => '获取课时', 'allnum' => $dataList['allnum'], 'result' => $result);
            } else {
                $result["list"] = array();
                $res = array('error' => '1', 'errortip' => '暂无课时', 'allnum' => 0, 'result' => $result);
            }
        } else {

            $result["list"] = array();
            $res = array('error' => '1', 'errortip' => '暂无课时', 'allnum' => 0, 'result' => $result);
        }
        ajax_return($res);
    }


    /**
     * 考试成绩统计
     * author: wgh
     * 对应接口文档 0001
     */
    function getExamScoreStatisView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Easx\EducationModel($request);
        if ($Model->error) {
            $result['is_power'] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
            ajax_return($res);
        }
        $dataList = $Model->getExamScoreStatis($request);

        $hourList = $this->DataControl->selectClear("
            SELECT h.hour_name,h.hour_lessontimes,th.teachhour_isscore,th.teachhour_scoretype,th.teachhour_isonscore,th.teachhour_onscoretype
            FROM smc_class_hour AS h
            LEFT JOIN smc_class AS c on c.class_id = h.class_id
            LEFT JOIN smc_course AS co ON  co.course_id = c.course_id 
            LEFT JOIN eas_classcode as cl ON cl.classcode_branch = co.course_branch and cl.company_id = co.company_id
            LEFT JOIN eas_teachhour as th ON th.company_id = cl.company_id AND th.classcode_branch = cl.classcode_branch AND th.teachhour_branch = CONCAT(co.course_branch,'-',h.hour_lessontimes)
            WHERE c.class_id = '{$request['class_id']}' and h.hour_iswarming = 0 and h.hour_ischecking <> -1 and cl.classcode_isregister = 1 and th.teachhour_isregister = 1");

        $field = array();
        $k = 0;
        $field[$k]["fieldname"] = "学员中文名";
        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "学员英文名";
        $field[$k]["fieldstring"] = 'student_enname';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "学员编号";
        $field[$k]["fieldstring"] = 'student_branch';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "性别";
        $field[$k]["fieldstring"] = 'student_sex';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "入班日期";
        $field[$k]["fieldstring"] = 'study_beginday';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        if($hourList){
            foreach ($hourList as $value) {
                if($value['teachhour_isscore'] == 1 && $value['teachhour_scoretype']){
                    $k++;
                    $field[$k]["fieldname"] = $value['hour_name'] .' '. $value['teachhour_scoretype'].'分数';
                    $field[$k]["fieldstring"] = 'hour_testscore'.$value['hour_lessontimes'];
                    $field[$k]["show"] = 1;
                    $field[$k]["custom"] = 1;
                }
                if($value['teachhour_isonscore'] == 1 && $value['teachhour_onscoretype']){
                    $k++;
                    $field[$k]["fieldname"] = $value['hour_name'] .' '. $value['teachhour_onscoretype'].'分数';
                    $field[$k]["fieldstring"] = 'hour_netscores'.$value['hour_lessontimes'];
                    $field[$k]["show"] = 1;
                    $field[$k]["custom"] = 1;
                }
            }
        }

        $result["field"] = $field;
        $result["info"] = $dataList['info'];
        if ($dataList['list']) {
            $result["list"] = $dataList['list'];
            $result["allnum"] = $dataList['allnum'];
            $res = array('error' => '0', 'errortip' => '获取课时', 'allnum' => $dataList['allnum'], 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => '1', 'errortip' => '暂无课时', 'allnum' => 0, 'result' => $result);
        }
        ajax_return($res);
    }

    function schoolTeaacherView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Easx\EducationModel($request);
        if ($Model->error) {
            $result['is_power'] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
            ajax_return($res);
        }

        $field = array();
        $k = 0;
        $field[$k]["fieldname"] = "校园名称";
        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "校园编号";
        $field[$k]["fieldstring"] = 'school_branch';
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "教师中文名";
        $field[$k]["fieldstring"] = 'staffer_cnname';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "教师英文名";
        $field[$k]["fieldstring"] = 'staffer_enname';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "教师编号";
        $field[$k]["fieldstring"] = 'staffer_branch';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "带课班级";
        $field[$k]["fieldstring"] = 'teaching_num';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result["field"] = $field;
        $dataList = $Model->schoolTeaacher($request);
        if ($dataList) {

            $result["list"] = $dataList['list'];
            $result["allnum"] = $dataList['allnum'];
            if ($result["list"]) {
                $res = array('error' => '0', 'errortip' => '获取课时', 'allnum' => $dataList['allnum'], 'result' => $result);
            } else {
                $result["list"] = array();
                $res = array('error' => '1', 'errortip' => '暂无课时', 'allnum' => 0, 'result' => $result);
            }
        } else {

            $result["list"] = array();
            $res = array('error' => '1', 'errortip' => '暂无课时', 'allnum' => 0, 'result' => $result);
        }
        ajax_return($res);
    }

    /**
     * 获取教务工作报表
     * author: ling
     * 对应接口文档 0001
     */
    function getEasWorkReportApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Easx\EducationModel($request);
        if ($Model->error) {
            $result['is_power'] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
            ajax_return($res);
        }

        $field = array();
        $k = 0;
        $field[$k]["fieldname"] = "班级名称";
        $field[$k]["fieldstring"] = "class_cnname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "班级别名";
        $field[$k]["fieldstring"] = 'class_enname';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "班级编号";
        $field[$k]["fieldstring"] = 'class_branch';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "主教教师中文名";
        $field[$k]["fieldstring"] = 'mian_staffer_cnname';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "主教教师英文名";
        $field[$k]["fieldstring"] = 'mian_staffer_enname';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "应上/总课次";
        $field[$k]["fieldstring"] = 'hour_num';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "实际登记课次";
        $field[$k]["fieldstring"] = 'hour_branch_num';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "未登记课次";
        $field[$k]["fieldstring"] = 'hour_diffnum';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "上次登记课次";
        $field[$k]["fieldstring"] = 'hour_name';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "上次登记时间";
        $field[$k]["fieldstring"] = 'hour_updatatime';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "最后上课日期";
        $field[$k]["fieldstring"] = 'hour_day';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result["field"] = $field;
        $dataList = $Model->getEasWorkReport($request);
        if ($dataList) {

            $result["list"] = $dataList['list'];
            $result["allnum"] = $dataList['allnum'];
            if ($result["list"]) {
                $res = array('error' => '0', 'errortip' => '教务工作报表', 'allnum' => $dataList['allnum'], 'result' => $result);
            } else {
                $result["list"] = array();
                $res = array('error' => '1', 'errortip' => '暂无教务工作记录', 'allnum' => 0, 'result' => $result);
            }
        } else {

            $result["list"] = array();
            $res = array('error' => '1', 'errortip' => '暂无课时', 'allnum' => 0, 'result' => $result);
        }
        ajax_return($res);
    }

    /**
     * 获取班级教务明细
     * author: ling
     * 对应接口文档 0001
     */
    function getEasWorkOneApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Easx\EducationModel($request);
        if ($Model->error) {
            $result['is_power'] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
            ajax_return($res);
        }

        $field = array();
        $k = 0;
        $field[$k]["fieldname"] = "课时名称";
        $field[$k]["fieldstring"] = "hour_name";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "上课时间";
        $field[$k]["fieldstring"] = 'hour_time';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "主教教师";
        $field[$k]["fieldstring"] = 'mian_staffer_cnname';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "助教教师";
        $field[$k]["fieldstring"] = 'fu_staffer_cnname';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "上课状态";
        $field[$k]["fieldstring"] = 'is_checking_name';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "登记状态";
        $field[$k]["fieldstring"] = 'is_register_name';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result["field"] = $field;
        $dataList = $Model->getEasWorkOne($request);

        $result["list"] = $dataList['list'];
        $result["info"] = $dataList['info'];
        $result["allnum"] = $dataList['allnums'];
        $res = array('error' => '0', 'errortip' => '教务工作报表', 'result' => $result);

        ajax_return($res);

    }

    //班级信息 -- 下拉 -- 班组
    function getCoursetypeView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Easx\EducationModel($request);
        if($Model->error){
            $result['is_power'] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
            ajax_return($res);
        }

        $datawhere = "c.company_id ='{$request['company_id']}'  ";

        $sql = "SELECT c.coursetype_id,c.coursetype_cnname,c.coursetype_branch 
                FROM smc_code_coursetype as c 
                WHERE {$datawhere}  ";

        $datalist = $this->DataControl->selectClear($sql);

        $result = array();
        if ($datalist) {
            $result['list'] = $datalist;
            $res = array('error' => 0, 'errortip' => '获取班组信息成功', 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => 1, 'errortip' => '暂无班组信息', 'result' => $result);
        }
        ajax_return($res);
    }

    //班级信息 -- 下拉 -- 班种
    function getCoursecatView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Easx\EducationModel($request);
        if($Model->error){
            $result['is_power'] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
            ajax_return($res);
        }

        $datawhere = "c.company_id ='{$request['company_id']}' and c.school_id = '{$request['school_id']}' and c.class_type = '0' and sc.course_inclasstype = '0' and cl.classcode_isbeike = '1'";//and course_isuseeas =1

        //补充了班组
        if (isset($paramArray['coursetype_id']) && $paramArray['coursetype_id'] !== '') {
            $datawhere .= " and cc.coursetype_id='{$paramArray['coursetype_id']}'";
        }

        if($request['account_class'] == 0){
            $postbe = $this->DataControl->selectOne(
                "select cp.postpart_isteregulator
                 from gmc_staffer_postbe as sp
                 left join smc_school_postpart as  cp ON  cp.postpart_id = sp.postpart_id
                 left join gmc_company_post as  cpt ON  sp.post_id = cpt.postpart_id
                 where sp.postbe_id = '{$request['re_postbe_id']}' and  (cp.postpart_isteregulator = 1 or cpt.post_istopjob =1) and cp.postpart_id >0
                 ");

            $compostbe = $this->DataControl->selectOne(
                "select cp.postpart_isteregulator
                 from gmc_staffer_postbe as sp
                 left join gmc_company_postrole as  cpl ON  sp.postrole_id = cpl.postrole_id
                 left join smc_school_postpart as  cp ON  cp.postpart_id = cpl.postpart_id
                 where sp.postbe_id = '{$request['re_postbe_id']}' and  cp.postpart_isteregulator = 1 and cp.postpart_id >0
                 ");

            if (!$postbe && !$compostbe) {
                $datawhere .= " and t.staffer_id = '{$request['staffer_id']}' ";
            }
        }

        $sql = "SELECT cc.coursecat_id,cc.coursecat_cnname,cc.coursecat_branch
                FROM smc_class_hour_teaching as t
                LEFT JOIN smc_class as c ON c.class_id = t.class_id
                LEFT JOIN smc_course as sc ON sc.course_id = c.course_id
                LEFT JOIN smc_code_coursecat as cc ON cc.coursecat_id = sc.coursecat_id
                LEFT JOIN eas_classcode as cl ON cl.classcode_branch = sc.course_branch and cl.company_id = sc.company_id
                WHERE {$datawhere} AND (c.class_status = '0' or c.class_status = '1') AND sc.course_status <> '-1' GROUP BY cc.coursecat_id";

        $datalist = $this->DataControl->selectClear($sql);

        $result = array();
        if ($datalist) {
            $result['list'] = $datalist;
            $res = array('error' => 0, 'errortip' => '获取班种信息成功', 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => 1, 'errortip' => '暂无班种信息', 'result' => $result);
        }
        ajax_return($res);
    }

    //班级信息 -- 下拉 -- 课程别
    function getCourseView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Easx\EducationModel($request);
        if ($Model->error) {
            $result['is_power'] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
            ajax_return($res);
        }


        $datawhere = " c.school_id = '{$request['school_id']}' and cl.classcode_isregister = '1'";// and sc.course_inclasstype = '0' and course_isuseeas =1
        if ($request['account_class'] != 1) {
            $postbe = $this->DataControl->selectOne(
                "select cp.postpart_isteregulator
                 from gmc_staffer_postbe as sp
                 left join smc_school_postpart as  cp ON  cp.postpart_id = sp.postpart_id
                 left join gmc_company_post as  cpt ON  sp.post_id = cpt.postpart_id
                 where sp.postbe_id = '{$request['re_postbe_id']}' and  (cp.postpart_isteregulator = 1 or cpt.post_istopjob =1) and cp.postpart_id >0
                 ");

            $compostbe = $this->DataControl->selectOne(
                "select cp.postpart_isteregulator
                 from gmc_staffer_postbe as sp
                 left join gmc_company_postrole as  cpl ON  sp.postrole_id = cpl.postrole_id
                 left join smc_school_postpart as  cp ON  cp.postpart_id = cpl.postpart_id
                 where sp.postbe_id = '{$request['re_postbe_id']}' and  cp.postpart_isteregulator = 1 and cp.postpart_id >0
                 ");

            if (!$postbe && !$compostbe) {
                $datawhere .= " and t.staffer_id = '{$request['staffer_id']}' ";
            }
        }


        $datalist = $this->DataControl->selectClear("SELECT sc.course_id,sc.course_cnname,sc.course_branch
                                                       FROM smc_class_hour_teaching as t
                                                       LEFT JOIN smc_class as c ON c.class_id = t.class_id
                                                       LEFT JOIN smc_course as sc ON sc.course_id = c.course_id
                                                       LEFT JOIN eas_classcode as cl ON cl.classcode_branch = sc.course_branch and cl.company_id = sc.company_id
                                                       WHERE {$datawhere} AND (c.class_status='0' or c.class_status='1') AND sc.course_status<>'-1' GROUP BY sc.course_id");

        $result = array();
        if ($datalist) {
            foreach ($datalist as &$vale) {
                $vale['course_cnname'] = $vale['course_cnname'] . '(' . $vale['course_branch'] . ')';
            }
            $result['list'] = $datalist;
            $res = array('error' => 0, 'errortip' => '获取课程别信息成功', 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => 1, 'errortip' => '暂无课程别信息', 'result' => $result);
        }
        ajax_return($res);
    }

    //班级信息 -- 下拉 -- 班级
    function getClassView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Easx\EducationModel($request);
        if ($Model->error) {
            $result['is_power'] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
            ajax_return($res);
        }


        $datawhere = " c.school_id = '{$request['school_id']}' and sc.course_inclasstype = '0' and cl.classcode_isregister = '1'";//and course_isuseeas =1
        if ($request['account_class'] != 1) {
            $postbe = $this->DataControl->selectOne(
                "select cp.postpart_isteregulator
                 from gmc_staffer_postbe as sp
                 left join smc_school_postpart as  cp ON  cp.postpart_id = sp.postpart_id
                 left join gmc_company_post as  cpt ON  sp.post_id = cpt.postpart_id
                 where sp.postbe_id = '{$request['re_postbe_id']}' and  (cp.postpart_isteregulator = 1 or cpt.post_istopjob =1) and cp.postpart_id >0
                 ");

            $compostbe = $this->DataControl->selectOne(
                "select cp.postpart_isteregulator
                 from gmc_staffer_postbe as sp
                 left join gmc_company_postrole as  cpl ON  sp.postrole_id = cpl.postrole_id
                 left join smc_school_postpart as  cp ON  cp.postpart_id = cpl.postpart_id
                 where sp.postbe_id = '{$request['re_postbe_id']}' and  cp.postpart_isteregulator = 1 and cp.postpart_id >0
                 ");

            if (!$postbe && !$compostbe) {
                $datawhere .= " and t.staffer_id = '{$request['staffer_id']}' ";
            }
        }

        $datalist = $this->DataControl->selectClear("SELECT c.class_id,c.class_cnname,c.class_enname,c.class_branch
                                                       FROM smc_class_hour_teaching as t
                                                       LEFT JOIN smc_class as c ON c.class_id = t.class_id
                                                       LEFT JOIN smc_course as sc ON sc.course_id = c.course_id
                                                       LEFT JOIN eas_classcode as cl ON cl.classcode_branch = sc.course_branch and cl.company_id = sc.company_id
                                                       WHERE {$datawhere} AND (c.class_status='0' or c.class_status='1') AND sc.course_status<>'-1' GROUP BY sc.course_id");

        $result = array();
        if ($datalist) {
            $result['list'] = $datalist;
            $res = array('error' => 0, 'errortip' => '获取班级信息成功', 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => 1, 'errortip' => '暂无班级信息', 'result' => $result);
        }
        ajax_return($res);
    }

    //班级信息 -- 下拉 -- 教师
    function getTeacherView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Easx\EducationModel($request);
        if ($Model->error) {
            $result['is_power'] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
            ajax_return($res);
        }

        $datawhere = "c.company_id = '{$request['company_id']}' and (c.class_status = '0' or c.class_status = '1') and cl.classcode_isregister = '1' and sf.staffer_leave = 0";

        if (isset($request['re_school_id']) && $request['re_school_id'] != '') {
            $datawhere .= " and c.school_id ='{$request['re_school_id']}'";
        }else{
            $datawhere .= " and c.school_id ='{$request['school_id']}'";
        }

        if ($request['account_class'] != 1) {
            $postbe = $this->DataControl->selectOne("select cp.postpart_isteregulator
             from gmc_staffer_postbe as sp
             left join smc_school_postpart as  cp ON  cp.postpart_id = sp.postpart_id
             left join gmc_company_post as  cpt ON  sp.post_id = cpt.postpart_id
             where sp.postbe_id = '{$request['re_postbe_id']}' and  (cp.postpart_isteregulator = 1 or cpt.post_istopjob =1) and cp.postpart_id >0
             ");

            $compostbe = $this->DataControl->selectOne("select cp.postpart_isteregulator
             from gmc_staffer_postbe as sp
             left join gmc_company_postrole as  cpl ON  sp.postrole_id = cpl.postrole_id
             left join smc_school_postpart as  cp ON  cp.postpart_id = cpl.postpart_id
             where sp.postbe_id = '{$request['re_postbe_id']}' and  cp.postpart_isteregulator = 1 and cp.postpart_id >0
             ");

            if (!$postbe && !$compostbe) {
                $datawhere .= " and t.class_id IN (SELECT tc.class_id FROM smc_class_teach AS tc WHERE tc.teach_status = '0' AND tc.staffer_id = '{$request['staffer_id']}')";
            }
        }

        $datalist = $this->DataControl->selectClear("SELECT sf.staffer_id,sf.staffer_cnname,sf.staffer_enname
                                                       FROM smc_class as c
                                                       LEFT JOIN smc_class_hour_teaching as t ON t.class_id = c.class_id
                                                       LEFT JOIN smc_staffer as sf ON sf.staffer_id = t.staffer_id
                                                       LEFT JOIN smc_course as sc ON sc.course_id = c.course_id
                                                       LEFT JOIN eas_classcode as cl ON cl.classcode_branch = sc.course_branch and cl.company_id = sc.company_id
                                                       WHERE {$datawhere} and sf.staffer_cnname is not NULL  GROUP BY sf.staffer_id");
        if ($datalist) {
            foreach ($datalist as &$v) {
                if ($v['staffer_enname'] && $v['staffer_enname'] != '') {
                    $v['staffer_cnname'] = $v['staffer_cnname'] . '-' . $v['staffer_enname'];
                }
            }
        }

        $result = array();
        if ($datalist) {
            $result['list'] = $datalist;
            $res = array('error' => 0, 'errortip' => '获取教师信息成功', 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => 1, 'errortip' => '暂无教师信息', 'result' => $result);
        }
        ajax_return($res);
    }


    //班级信息 -- 下拉 -- 教室
    function getClassroomView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Easx\EducationModel($request);
        if ($Model->error) {
            $result['is_power'] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
            ajax_return($res);
        }


        $datawhere = "c.company_id ='{$request['company_id']}' and c.school_id ='{$request['school_id']}' and (c.class_status = '0' or c.class_status = '1')";
        if ($request['account_class'] != '1') {
            $datawhere .= " and c.class_id IN (SELECT tc.class_id FROM smc_class_teach AS tc WHERE tc.teach_status = '0' AND tc.staffer_id = '{$request['staffer_id']}')";
        }

        $datalist = $this->DataControl->selectClear("SELECT sc.classroom_id,sc.classroom_cnname
                                                       FROM smc_class as c
                                                       LEFT JOIN smc_class_hour as h ON h.class_id = c.class_id
                                                       LEFT JOIN smc_classroom as sc ON sc.classroom_id = h.classroom_id
                                                       LEFT JOIN smc_course as co ON co.course_id = c.course_id
                                                       LEFT JOIN eas_classcode as cl ON cl.classcode_branch = co.course_branch and cl.company_id = co.company_id
                                                       WHERE {$datawhere} AND sc.classroom_status='1'
                                                       GROUP BY sc.classroom_id");

        $result = array();
        if ($datalist) {
            $result['list'] = $datalist;
            $res = array('error' => 0, 'errortip' => '获取教室信息成功', 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => 1, 'errortip' => '暂无教室信息', 'result' => $result);
        }
        ajax_return($res);
    }

    /**
     *  获取课时教务
     * author: ling
     * 对应接口文档 0001
     */
    function getHourcheckOneApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Easx\EducationModel($request);
        if ($Model->error) {
            $result['is_power'] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
            ajax_return($res);
        }

        $result = $Model->getHourcheckOne($request);
        $res = array('error' => '0', 'errortip' => '获取成功', 'result' => $result);
        ajax_return($res);
    }

    /**
     * 我的班级 学员管理
     * 作者: wgh
     * @param  对应接口文档 134
     * @return array
     */
    function getStudentListApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);

        $Model = new \Model\Easx\EducationModel($request);
        if ($Model->error) {
            $result['is_power'] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
            ajax_return($res);
        }

        $res = $Model->getStudentList($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldname"] = "学员中文名";
        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "学员英文名";
        $field[$k]["fieldstring"] = 'student_enname';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "学员编号";
        $field[$k]["fieldstring"] = 'student_branch';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "性别";
        $field[$k]["fieldstring"] = 'student_sex';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "联系电话";
        $field[$k]["fieldstring"] = 'family_mobile';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "所属子班级";
        $field[$k]["fieldstring"] = 'fu_class_cnname';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "学员状态";
        $field[$k]["fieldstring"] = 'student_status';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "入班日期";
        $field[$k]["fieldstring"] = 'study_beginday';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "剩余课次";
        $field[$k]["fieldstring"] = 'coursebalance_time';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        $result["classOne"] = $res['classOne'];
        if ($res) {
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["field"] = array();
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无学员信息', 'result' => $result);
        }
        ajax_return($res);
    }

    /**
     * 我的班级 学员管理 分班下拉列表
     * 作者: wgh
     * @param  对应接口文档 135
     * @return array
     */
    function getSeparateClassApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);

        $Model = new \Model\Easx\EducationModel($request);
        if ($Model->error) {
            $result['is_power'] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
            ajax_return($res);
        }


        $datawhere = "company_id = '{$request['company_id']}' and school_id = '{$request['school_id']}'";
        if (isset($request['class_id']) && $request['class_id'] !== '') {
            $datawhere .= " and father_id = '{$request['class_id']}'";
        }
        if (isset($request['to_class_id']) && $request['to_class_id'] !== '') {
            $datawhere .= " and class_id <> '{$request['to_class_id']}'";
        }

        $sql = "SELECT class_id,class_cnname,class_enname,class_branch FROM smc_class WHERE {$datawhere}";
        $ClassList = $this->DataControl->selectClear($sql);
        if (!$ClassList) {
            $ClassList = array();
        }

        $result["list"] = $ClassList;
        $res = array('error' => 0, 'errortip' => '获取分班下拉列表', 'result' => $result);

        ajax_return($res);
    }

    /**
     * 我的班级 学员管理 学员分班操作
     * 作者: wgh
     * @param  对应接口文档 136
     * @return array
     */
    function separateClassAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);

        $Model = new \Model\Easx\EducationModel($request);
        if ($Model->error) {
            $result['is_power'] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
            ajax_return($res);
        }

        $res = $Model->separateClassAction($request);

        if ($res) {
            $res = array('error' => 0, 'errortip' => '入班成功', 'result' => $res);
        } else {
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => array());
        }
        ajax_return($res);
    }

    /**
     * 我的班级 学员管理 学员转班操作
     * 作者: wgh
     * @param  对应接口文档 137
     * @return array
     */
    function toSeparateClassAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);

        $Model = new \Model\Easx\EducationModel($request);
        if ($Model->error) {
            $result['is_power'] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
            ajax_return($res);
        }

        $res = $Model->toSeparateClassAction($request);

        if ($res) {
            $res = array('error' => 0, 'errortip' => '转班成功', 'result' => $res);
        } else {
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => array());
        }
        ajax_return($res);
    }


    //结尾魔术函数
    function __destruct()
    {

    }
}
