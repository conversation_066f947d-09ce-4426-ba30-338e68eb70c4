<?php
/**
 * 客诉-C端-用户(登录态校验)
 */

namespace Work\Controller\Easxapi;


class UcsUserController extends viewTpl
{
    public $u;
    public $t;
    public $c;
    public $customer_id;

    //预加载处理类
    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
        $request = Input('POST.','','trim,addslashes');
        $info = $this->verifyLogin($request['token']);
        $this->customer_id = $info['customer_id'];
    }

    /**
     * 秘钥校验及解析
     * @param $token
     */
    function verifyLogin($token)
    {
        if(empty($token))
        {
            $this->error = 1;
            $this->errortip = "token必须传入";
            $this->result = array();
            ajax_return(array('error' => $this->error, 'errortip' => $this->errortip, 'result' => $this->result));
        }
        //校验解析token ->verifyLogin()
        $ucsTokenModel = new \Model\Easx\UcsTokenModel();
        $data = $ucsTokenModel ->verification(['token'=>$token]);
        return $data;

    }

}