<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2019/11/28
 * Time: 13:30
 */

namespace Work\Controller\Easxapi;

use Model\Easx\ClasshourModel;


class ClasshourController extends viewTpl
{
    public $u;
    public $t;
    public $c;

    //预加载处理类
    function __construct($visitType = "api")
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
    }

    //本地权限校验入口
    function ThisVerify($request)
    {
        if (!intval($request['staffer_id'])) {
            $res = array('error' => '1', 'errortip' => "未检测到招生人员信息，请重新登录操作", 'result' => array());
            ajax_return($res);
        }
        if (empty($request['token'])) {
            $res = array('error' => '1', 'errortip' => "未传入用户Token信息", 'result' => array());
            ajax_return($res);
        }
        $paramArray = array();
        $paramArray['staffer_id'] = $request['staffer_id'];
        $paramArray['token'] = $request['token'];
        if (!$this->UserLimit($paramArray)) {
            $result = array();
            $result["list"] = array();
            $result["tokeninc"] = "0";
            $res = array('error' => '1', 'errortip' => "用户token失效", 'result' => $result);
            ajax_return($res);
        }
    }


    /**
     * 获取对应课时需要点评的人
     *  author: ling
     * 对应接口文档 0001
     */

    function getHourStudentView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new ClasshourModel($request);
        $datalist = $Model->getHourStudent($request);
        $field = array();
        $k = 0;
        $field[$k]["fieldname"] = "学员中文名";
        $field[$k]["fieldstring"] = 'student_cnname';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "学员英文名";
        $field[$k]["fieldstring"] = 'student_enname';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "学员编号";
        $field[$k]["fieldstring"] = 'student_branch';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "性别";
        $field[$k]["fieldstring"] = 'student_sex';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "出生日期";
        $field[$k]["fieldstring"] = 'student_birthday';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "点评状态";
        $field[$k]["fieldstring"] = 'sturemark_status_name';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $result = array();
        $result["field"] = $field;
        if ($datalist['list']) {
            $result['list'] = $datalist['list'];
            $res = array('error' => 0, 'errortip' => '获取课次人员信息成功', 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => 1, 'errortip' => '暂无可点评的学员', 'result' => $result);
        }
        ajax_return($res);

    }

    /**
     * 查看点评详情
     *  author: ling
     * 对应接口文档 0001
     */
    function getStuHourRemarkOneView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new ClasshourModel($request);
        $result = $Model->getStuHourRemarkOne($request);

        if ($result) {
            $res = array('error' => 0, 'errortip' => '获取个人点评成功', 'result' => $result);
        } else {
            $res = array('error' => 1, 'errortip' => '暂无信息', 'result' => array());
        }
        ajax_return($res);

    }


    /**
     * 撤回点评
     * author: ling
     * 对应接口文档 0001
     */
    function withdrawStuHourRemarkAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new ClasshourModel($request);
        $Model->withdrawStuHourRemark($request);
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res);


    }

    /**
     * 获取集团课程的评星模板
     * author: ling
     * 对应接口文档 0001
     */
    function getRemarkTempStartView()
    {

        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new ClasshourModel($request);
        $datalist = $Model->getRemarkTempStart($request);
        $field = array();
        $result = array();
        $result["field"] = $field;
        if ($datalist['list']) {
            $result['list'] = $datalist['list'];
            $res = array('error' => 0, 'errortip' => '获取评星模板', 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => 1, 'errortip' => '暂无评星模板', 'result' => $result);
        }
        ajax_return($res);

    }

    /**
     * 获取学员信息
     */
    function getStudentApi()
    {

        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new ClasshourModel($request);
        $datalist = $Model->getStudentApi($request);
        $field = array();
        $result = array();
        $result["field"] = $field;
        if ($datalist['list']) {
            $result['list'] = $datalist['list'];
            $res = array('error' => 0, 'errortip' => '获取学员信息', 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => 1, 'errortip' => '暂无学员信息', 'result' => $result);
        }
        ajax_return($res);

    }

    /**
     * 获取的点评的评价模板
     * author: ling
     * 对应接口文档 0001
     */
    function getRemarkTempView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new ClasshourModel($request);
        $datalist = $Model->getRemarkTemp($request);
        $field = array();
        $result = array();
        $result["field"] = $field;
        if ($datalist['list']) {
            $result['list'] = $datalist['list'];
            $res = array('error' => 0, 'errortip' => '获取评星模板', 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => 1, 'errortip' => '暂无评星模板', 'result' => $result);
        }
        ajax_return($res);

    }


    /**
     * 提交上课点评
     * author: ling
     * 对应接口文档 0001
     */
    function submitHourRemarkAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Easx\ClasshourModel($request);
        $remark_id = $Model->submitHourRemark($request);
        $result = array();
        $result['sturemark_id'] = $remark_id;
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $result);
        ajax_return($res, $request['language_type']);
    }

    /**
     * 获取班级信息
     * author: ling
     * 对应接口文档 0001
     */
    function getClassOneApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Easx\ClasshourModel($request);
        $field = array();
        $field['class_cnname'] ="班级中文名";
        $field['class_enname'] ="班级别名";
        $field['class_branch'] ="班级编号";
        $field['staffer_cnname'] ="主教";
        $field['fu_staffer_cnname'] ="助教";
        $field['hour_allnum'] ="总计课时数";
        $field['hour_checkingnum'] ="已上课时数";
        $field['resign_hour_num'] ="已登记课时数";
        $field['no_resign_hour_num'] ="未登记课时数";
        $dataOne = $Model->getClassOneApi($request);
        $result = array();
        $result['list'] = $dataOne;
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $result);
        ajax_return($res, $request['language_type']);
    }


}