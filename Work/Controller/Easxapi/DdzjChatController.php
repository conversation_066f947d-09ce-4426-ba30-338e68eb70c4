<?php
/**
 * ============================================================================
 * 版权所有 : https://www.mohism.cn
 * 网站地址 : https://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/3/12
 * Time: 22:30
 */

namespace Work\Controller\Easxapi;

class DdzjChatController extends viewTpl
{
    public $data;
    public $appId = 'wx2a66618e4feffded';
    public $appSecret = '30983395129d5ee5ee76d27a79cea406';
    public $wxuser;

    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
    }

    //校务系统叮铛助教教师授权登录
    function scteloginView()
    {
        $host = "https://api.kedingdang.com/DdzjChat/wxChatUrl?url=sctelogin";
        $url = "https://open.weixin.qq.com/connect/oauth2/authorize?appid=" . $this->appId . "&redirect_uri=" . $host . "&response_type=code&scope=snsapi_userinfo&state=1#wechat_redirect";
        header("location:" . $url);
    }

    //校务系统叮铛助教教师授权登录
    function kidteloginView()
    {
        $host = "https://api.kedingdang.com/DdzjChat/wxChatUrl?url=kidtelogin";
        $url = "https://open.weixin.qq.com/connect/oauth2/authorize?appid=" . $this->appId . "&redirect_uri=" . $host . "&response_type=code&scope=snsapi_userinfo&state=1#wechat_redirect";
        header("location:" . $url);
    }

    //获取微信反馈数据
    function getWeixinInfo($CODE)
    {
        $paramarray = array(
            'appid' => $this->appId,
            'secret' => $this->appSecret,
            'code' => $CODE,
            'grant_type' => "authorization_code"
        );

        $getBakurl = request_by_curl("https://api.weixin.qq.com/sns/oauth2/access_token", dataEncode($paramarray), "GET");
        $json_play = new \Webjson();
        $cardarray = $json_play->decode($getBakurl, "1");
        return $cardarray;
    }

    function getWeixinToken()
    {
        $token = $this->DataControl->getFieldOne("eas_weixin_token", "token_failuretime,token_string", "token_type = '1' and token_site = '1' and wxchatnumber_id = '1'", "order by token_failuretime DESC limit 0,1");
        if ($token && $token['token_failuretime'] > time()) {
            $wxtoken = array();
            $wxtoken['access_token'] = $token['token_string'];
            $wxtoken['expires_in'] = 7200;
            return $wxtoken;
        } else {
            $paramarray = array(
                'appid' => $this->appId,
                'secret' => $this->appSecret,
                'grant_type' => "client_credential"
            );
            $getBakurl = request_by_curl("https://api.weixin.qq.com/cgi-bin/token", dataEncode($paramarray), "GET");
            $json_play = new \Webjson();
            $cardarray = $json_play->decode($getBakurl, "1");
            $data = array();
            $data['wxchatnumber_id'] = '1';
            $data['token_site'] = '1';
            $data['token_type'] = '1';
            $data['token_string'] = $cardarray['access_token'];
            $data['token_failuretime'] = time() + $cardarray['expires_in'];
            $this->DataControl->insertData("eas_weixin_token", $data);
            return $cardarray;
        }
    }

    //获取微信User
    function getWeixinUser($token, $openid)
    {
        $paramarray = array(
            'access_token' => $token,
            'openid' => $openid,
            'lang' => "zh_CN"
        );

        $getBakurl = trim(request_by_curl("https://api.weixin.qq.com/sns/userinfo", dataEncode($paramarray), "GET"));
        $json_play = new \Webjson();
        $cardarray = $json_play->decode($getBakurl, "1");
        return $cardarray;
    }

    //雇员登录
    function wxChatUrlView()
    {
        $request = Input('get.','','trim,addslashes');
        if (isset($_GET['code'])) {
            $WeixinInfo = $this->getWeixinInfo($_GET['code']);
            $Weixinuser = $this->getWeixinUser($WeixinInfo['access_token'], $WeixinInfo['openid']);
            if ($Weixinuser['openid'] == '') {
                $url = "https://api.kedingdang.com/DdzjChat/sctelogin";
                header("location:" . $url);
            }

            if ($request['url'] == 'sctelogin') {
                $openurl = "http://tesc.kcclassin.com/Login/wxBack?wxtoken={$Weixinuser['openid']}&imghead={$Weixinuser['headimgurl']}&nickname={$Weixinuser['nickname']}";
                header("location:{$openurl}");
                exit;
            }else {
                header("location:/");
                exit;
            }
        } else {
            header("location:/");
            exit;
        }
    }

    //表彰通知
    //$firstnote开始文字，$keyword1所在班级，$keyword2通知教师，$footernote结束文本
    function CitationMis($wxtoken, $firstnote, $keyword1, $keyword2, $footernote, $url, $student_id = '0', $member_id = '0')
    {
        $data = '{
			 "touser":"' . $wxtoken . '",
			 "template_id":"c5TkJyPDOBZ_bYiFLnEbeASSyFb-vT85uaTNVCNK-C4",
			 "url":"' . $url . '",
			 "topcolor":"#FF0000",
			 "data":{
					 "first": {
						 "value":"' . $firstnote . '",
						 "color":"#173177"
					 },
					 "keyword1":{
						 "value":"' . $keyword1 . '",
						 "color":"#173177"
					 },
					 "keyword2": {
						 "value":"' . $keyword2 . '",
						 "color":"#173177"
					 },
					 "remark":{
						 "value":"' . $footernote . '",
						 "color":"#173177"
					 }
			 }
		 }';
        return $this->SendWeixinMis($data, "课业完成通知", $student_id, $member_id);
    }


    //点评完成通知
    function testView()
    {
        // $request = Input('post.','','trim,addslashes');

        // if ($request['staffer_id'] !== '' && $request['firstnote'] !== '' && $request['keyword1'] !== '' && $request['keyword2'] !== '' && $request['keyword3'] !== '' && $request['footernote'] !== '') {

        //     $url = 'https://www.baidu.com';

        //     $this->ClassMis('ojk2Ws2VV3pru7eSisggCFwsV3mw', $request['firstnote'], $request['keyword1'], $request['keyword2'], $request['keyword3'], $request['footernote'], $url);

        //     ajax_return(array('error' => 0, 'errortip' => "微信通知发送成功!", "bakfuntion" => "okmotify"));

        // } else {
        //     ajax_return(array('error' => 1, 'errortip' => "参数不完整!", "bakfuntion" => "errormotify"));
        // }
    }

    //班级通知
    //$firstnote开始文字，$keyword1所在班级，$keyword2通知教师，$keyword3通知时间，$keyword4通知内容，$footernote结束文本
    function ClassMis($wxtoken, $firstnote, $keyword1, $keyword2, $keyword3, $footernote, $url)
    {
        $data = '{
			 "touser":"' . $wxtoken . '",
			 "template_id":"7EOOTpxwcmDJEwKFsTybJ8414Ni8VfdQxh8jCRFKEE4",
			 "url":"' . $url . '",
			 "topcolor":"#FF0000",
			 "data":{
					 "first": {
						 "value":"' . $firstnote . '",
						 "color":"#173177"
					 },
					 "keyword1":{
						 "value":"' . $keyword1 . '",
						 "color":"#173177"
					 },
					 "keyword2": {
						 "value":"' . $keyword2 . '",
						 "color":"#173177"
					 },
					 "keyword3": {
						 "value":"' . $keyword3 . '",
						 "color":"#173177"
					 },
					 "remark":{
						 "value":"' . $footernote . '",
						 "color":"#173177"
					 }
			 }
		 }';
        return $this->SendWeixinMis($data, "班级通知1111");
    }


    function SendWeixinMis($data, $log_type = '', $student_id = '0', $member_id = '0')
    {
        $tokenArray = $this->getWeixinToken();
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, "https://api.weixin.qq.com/cgi-bin/message/template/send?access_token={$tokenArray['access_token']}");
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE);
        curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (compatible; MSIE 5.01; Windows NT 5.0)');
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
        curl_setopt($ch, CURLOPT_AUTOREFERER, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        $tmpInfo = curl_exec($ch);
        if (curl_errno($ch)) {
            echo 'Errno' . curl_error($ch);
        }
        curl_close($ch);
        $json_play = new \Webjson();
        $retueninfo = $json_play->decode($tmpInfo, "1");
        if ($retueninfo['errmsg'] == 'ok') {
            $date = array();
            $date['student_id'] = 1;
            $date['parenter_id'] = 1;
            $date['staffer_id'] = 1;
            $date['log_type'] = $log_type;
            $date['log_status'] = 1;
            $date['log_content'] = $data;
            $date['log_addtime'] = time();
            $this->DataControl->insertData("ptc_wxsend_log", $date);
            return true;
        } else {
            $date = array();
            $date['student_id'] = 2;
            $date['parenter_id'] = 2;
            $date['staffer_id'] = 2;
            $date['log_type'] = $log_type;
            $date['log_status'] = 0;
            $date['log_errmsg'] = $tmpInfo;
            $date['log_content'] = $data;
            $date['log_addtime'] = time();
            $this->DataControl->insertData("ptc_wxsend_log", $date);
            return false;
        }
    }
}
