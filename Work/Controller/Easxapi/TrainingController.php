<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2019/12/13
 * Time: 10:28
 */

namespace Work\Controller\Easxapi;

use Model\Easx\TrainingModel;

class TrainingController extends viewTpl
{
    public $u;
    public $t;
    public $c;

    //预加载处理类
    function __construct($visitType = "api")
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
    }

    //本地权限校验入口
    function ThisVerify($request)
    {
        $paramArray = array();
        $paramArray['staffer_id'] = $request['staffer_id'];
        $paramArray['school_id'] = $request['school_id'];
        $paramArray['company_id'] = $request['company_id'];
        $paramArray['token'] = $request['token'];
        if (!$this->UserLimit($paramArray)) {
            $result = array();
            $result["list"] = array();
            $result["tokeninc"] = "0";
            $res = array('error' => '1', 'errortip' => "用户token失效", 'result' => $result);
            ajax_return($res);
        }
    }

    //教学成长路线
    function HomeView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new TrainingModel($request);
        if($Model->error){
            $result['is_power'] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
            ajax_return($res);
        }
        $datalist = $Model->GrowthPath();

        $result = array();
        if($datalist){
            $result["list"] = $datalist;
            $res = array('error' => '0', 'errortip' => '获取教学成长路线成功', 'result' => $result);
        }else{
            $result["list"] = array();
            $res = array('error' => '1', 'errortip' => '啊哦，暂无笔记哦~', 'result' => $result);
        }
        ajax_return($res);
    }

    //立即学习
    function AtOnceStudyView(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new TrainingModel($request);
        if($Model->error){
            $result['is_power'] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
            ajax_return($res);
        }
        $datalist = $Model->AtOnceStudy($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldname"] = "阶段名称";
        $field[$k]["fieldstring"] = "stage_cnname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "课程名称";
        $field[$k]["fieldstring"] = "course_name";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "学习情况";
        $field[$k]["fieldstring"] = "study_situation";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "考核情况";
        $field[$k]["fieldstring"] = "exam_situation";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        if($datalist){
            $result["list"] = $datalist;
            $res = array('error' => '0', 'errortip' => '获取学习阶段信息成功', 'result' => $result);
        }else{
            $result["list"] = array();
            $res = array('error' => '1', 'errortip' => '暂无学习阶段信息', 'result' => $result);
        }
        ajax_return($res);
    }

    //培训 -- 点击考核效果展示
    function ExamInfoView(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new TrainingModel($request);
        if($Model->error){
            $result['is_power'] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
            ajax_return($res);
        }
        $datalist = $Model->ExamInfo($request);

        $result = array();
        if($datalist){
            $result["list"] = $datalist;
            $res = array('error' => '0', 'errortip' => '获取试题信息成功', 'result' => $result);
        }else{
            $result["list"] = array();
            $res = array('error' => '1', 'errortip' => '暂无试题信息', 'result' => $result);
        }
        ajax_return($res);
    }

    //获取检测编号
    function getTestpaperQuestionView(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new TrainingModel($request);
        if($Model->error){
            $result['is_power'] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
            ajax_return($res);
        }
        $res = $Model->getTestpaperQuestion($request);

        ajax_return($res);
    }

    //通过检测编号获取题目信息
    function getSubjectsView(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $questData=array();
        if(isset($request['examine_branch']) && $request['examine_branch'] !== ''){
            $questData['examine_branch'] = $request['examine_branch'];
        }
        $Model = new TrainingModel($request);
        if($Model->error){
            $result['is_power'] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
            ajax_return($res);
        }
        $res = $Model->getSubjects($questData);
        ajax_return($res);
    }

    //根据题目ID获取问题信息
    function getQuestionView(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new TrainingModel($request);
        if($Model->error){
            $result['is_power'] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
            ajax_return($res);
        }
        $data = $Model->getQuestionJsonArray($request);
        $result = array();
        $result["list"] = $data;
        $res = array('error' => '0', 'errortip' => "根据问题ID获取问题信息", 'result' => $result);
        ajax_return($res);
    }

    //提交试卷获取分数
    function getScoreView(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);

        $questData = array();
        if(isset($request['examine_branch']) && $request['examine_branch'] !== ''){
            $questData['examine_branch'] = $request['examine_branch'];
        }
        if(isset($request['answer_list']) && $request['answer_list'] !== ''){
            $request['answer_list'] = stripslashes($request['answer_list']);
            $questData['answer_list'] = json_decode($request['answer_list'],true);
        }
        if(isset($request['examine_examtime']) && $request['examine_examtime'] !== ''){
            $questData['examine_examtime'] = $request['examine_examtime'];
        }

        $Model = new TrainingModel($request);
        if($Model->error){
            $result['is_power'] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
            ajax_return($res);
        }
        $res = $Model->getScore($questData);

        ajax_return($res);
    }

    //考核记录
    function ExamRecordView(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new TrainingModel($request);
        if($Model->error){
            $result['is_power'] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
            ajax_return($res);
        }
        $datalist = $Model->ExamRecord($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldname"] = "考核时间";
        $field[$k]["fieldstring"] = "examine_createtime";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "用时";
        $field[$k]["fieldstring"] = "examine_examtime";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "考核分数";
        $field[$k]["fieldstring"] = "examine_score";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "考核结果";
        $field[$k]["fieldstring"] = "exam_name";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        if($datalist){
            $result["list"] = $datalist['list'];
            $result['allnum'] = $datalist['allnums'];
            $res = array('error' => '0', 'errortip' => '获取试题信息成功', 'result' => $result);
        }else{
            $result["list"] = array();
            $res = array('error' => '1', 'errortip' => '暂无试题信息', 'result' => $result);
        }
        ajax_return($res);
    }

    //培训 -- 考核记录查看答案
    function LookAnswerView(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new TrainingModel($request);
        if($Model->error){
            $result['is_power'] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
            ajax_return($res);
        }
        $res = $Model->LookAnswer($request);

        ajax_return($res);
    }


    // 培训 -- 查看课程
    function SeeCourseView(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new TrainingModel($request);
        if($Model->error){
            $result['is_power'] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
            ajax_return($res);
        }
        $res = $Model->SeeCourse($request);

        ajax_return($res);
    }

    //课程收藏信息创建
    function CourseCollectAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new TrainingModel($request);
        if($Model->error){
            $result['is_power'] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
            ajax_return($res);
        }
        $result = $Model->CourseCollect($request);

        ajax_return($result);
    }

    //收藏/取消收藏
    function AddCourseCollectAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new TrainingModel($request);
        if($Model->error){
            $result['is_power'] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
            ajax_return($res);
        }
        $result = $Model->AddCourseCollect($request);

        ajax_return($result);
    }


    //查看课程 -- 观看课程
    function WatchVideoView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new TrainingModel($request);
        if($Model->error){
            $result['is_power'] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
            ajax_return($res);
        }
        $datalist = $Model->WatchVideo($request);

        $result = array();
        if($datalist){
            $result["list"]['list'] = $datalist['list'];
            $result["list"]['chapter'] = $datalist['chapter'];
            $res = array('error' => '0', 'errortip' => '获取课程信息成功', 'result' => $result);
        }else{
            $result["list"] = array();
            $res = array('error' => '1', 'errortip' => '暂无课程信息', 'result' => $result);
        }
        ajax_return($res);
    }

    //观看课程（全屏）
    function FullScreenView(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new TrainingModel($request);
        if($Model->error){
            $result['is_power'] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
            ajax_return($res);
        }
        $datalist = $Model->FullScreen($request);

        $result = array();
        if($datalist){
            $result["list"] = $datalist;
            $res = array('error' => '0', 'errortip' => '获取培训视频/音频信息成功', 'result' => $result);
        }else{
            $result["list"] = array();
            $res = array('error' => '1', 'errortip' => '暂无培训视频/音频', 'result' => $result);
        }
        ajax_return($res);
    }


    //培训 -- 添加个人笔记
    function PersonNoteAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new TrainingModel($request);
        if($Model->error){
            $result['is_power'] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
            ajax_return($res);
        }
        $result = $Model->PersonNoteApi($request);

        ajax_return($result);
    }

    //学习课程信息创建
    function AddWatchVideoInfoAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new TrainingModel($request);
        if($Model->error){
            $result['is_power'] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
            ajax_return($res);
        }
        $result = $Model->AddWatchVideoInfo($request);

        ajax_return($result);
    }

    //学习课程完成
    function WatchVideoCompleteAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new TrainingModel($request);
        if($Model->error){
            $result['is_power'] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
            ajax_return($res);
        }
        $result = $Model->WatchVideoComplete($request);

        ajax_return($result);
    }

    //获取通识课信息
    function OpenCourseView(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new TrainingModel($request);
        if($Model->error){
            $result['is_power'] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
            ajax_return($res);
        }
        $datalist = $Model->OpenCourse($request);

        $result = array();
        if($datalist){
            $result["list"] = $datalist['list'];
            $result["allnum"] = $datalist['allnums'];
            $res = array('error' => '0', 'errortip' => '获取通识课信息成功', 'result' => $result);
        }else{
            $result["list"] = array();
            $res = array('error' => '1', 'errortip' => '暂无通识课信息', 'result' => $result);
        }
        ajax_return($res);
    }

    //职务统计(培训/通识课)报表
    function PostTotalTrainReportView(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new TrainingModel($request);
        if($Model->error){
            $result['is_power'] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
            ajax_return($res);
        }
        $datalist = $Model->PostTotalTrainReport($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldname"] = "职务ID";
        $field[$k]["fieldstring"] = "career_id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "职务名称";
        $field[$k]["fieldstring"] = "career_cnname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "涉及在职人数";
        $field[$k]["fieldstring"] = "OnJob_num";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        if (isset($request['course_type']) && $request['course_type'] == '1') {
            $field[$k]["fieldname"] = "应学培训课";
            $field[$k]["fieldstring"] = "train_num";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;
        } else {
            $field[$k]["fieldname"] = "应学通识课";
            $field[$k]["fieldstring"] = "train_num";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;
        }

        $field[$k]["fieldname"] = "未学习";
        $field[$k]["fieldstring"] = "unlearned_num";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "正在学习";
        $field[$k]["fieldstring"] = "Inlearn_num";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "学习已完成";
        $field[$k]["fieldstring"] = "finish_num";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        if($datalist){
            $result['list'] = $datalist;
            $res = array('error' => 0, 'errortip' => '获取校园统计报表成功', 'result' => $result);
        }else{
            $result['list'] = array();
            $res = array('error' => 1, 'errortip' => '暂无校园统计报表', 'result' => $result);
        }
        ajax_return($res);
    }

    //培训 -- 下拉 -- 选择职务
    function getCareerView(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new TrainingModel($request);
        if($Model->error){
            $result['is_power'] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
            ajax_return($res);
        }

        $datalist = $this->DataControl->selectClear("SELECT c.career_id,c.career_cnname FROM eas_career as c WHERE c.company_id='{$request['company_id']}'");

        $result = array();
        if ($datalist) {
            $result['list'] = $datalist;
            $res = array('error' => 0, 'errortip' => '获取职务信息成功', 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => 1, 'errortip' => '暂无职务信息', 'result' => $result);
        }
        ajax_return($res);
    }

    //教师统计报表
    function TeacherReportView(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new TrainingModel($request);
        if($Model->error){
            $result['is_power'] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
            ajax_return($res);
        }
        $datalist = $Model->TeacherReport($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldname"] = "教师ID";
        $field[$k]["fieldstring"] = "staffer_id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "教师名称";
        $field[$k]["fieldstring"] = "staffer_cnname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "教师编号";
        $field[$k]["fieldstring"] = "staffer_branch";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "当前职务";
        $field[$k]["fieldstring"] = "career_cnname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "应学培训课程";
        $field[$k]["fieldstring"] = "shouldtrain_num";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "已学培训课程";
        $field[$k]["fieldstring"] = "finishtrain_num";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "应学通识课";
        $field[$k]["fieldstring"] = "shouldopenclass_num";
        $field[$k]["show"] = 1;
        $field[$k]["isProgress"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "已学通识课";
        $field[$k]["fieldstring"] = "finishopenclass_num";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $datalist['allnums'];
        if($datalist['list']){
            $result['list'] = $datalist['list'];
            $res = array('error' => 0, 'errortip' => '获取教师统计报表成功', 'result' => $result);
        }else{
            $result['list'] = array();
            $res = array('error' => 1, 'errortip' => '暂无教师统计报表', 'result' => $result);
        }
        ajax_return($res);
    }


    //通识课统计报表
    function OpenClassReportView(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new TrainingModel($request);
        if($Model->error){
            $result['is_power'] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
            ajax_return($res);
        }
        $datalist = $Model->OpenClassReport($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldname"] = "通识课名称";
        $field[$k]["fieldstring"] = "course_name";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "适配职务";
        $field[$k]["fieldstring"] = "fitjob_num";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "涉及在职人数";
        $field[$k]["fieldstring"] = "OnJob_num";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "未学习";
        $field[$k]["fieldstring"] = "unlearned_num";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "正在学习";
        $field[$k]["fieldstring"] = "Inlearn_num";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "学习已完成";
        $field[$k]["fieldstring"] = "finish_num";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $datalist['allnums'];
        if($datalist['list']){
            $result['list'] = $datalist['list'];
            $res = array('error' => 0, 'errortip' => '获取通识课统计报表成功', 'result' => $result);
        }else{
            $result['list'] = array();
            $res = array('error' => 1, 'errortip' => '暂无通识课统计报表', 'result' => $result);
        }
        ajax_return($res);
    }
}