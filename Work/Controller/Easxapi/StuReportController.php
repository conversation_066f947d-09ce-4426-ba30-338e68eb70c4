<?php


namespace Work\Controller\Easxapi;


use Model\Easx\StuReportModel;

class StuReportController extends viewTpl
{
    public $u;
    public $t;
    public $c;

    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
    }

    function ThisVerify($request)
    {
        if (!intval($request['staffer_id'])) {
            $res = array('error' => '1', 'errortip' => "未检测到招生人员信息，请重新登录操作", 'result' => array());
            ajax_return($res);
        }
        if (empty($request['token'])) {
            $res = array('error' => '1', 'errortip' => "未传入用户Token信息", 'result' => array());
            ajax_return($res);
        }
        $paramArray = array();
        $paramArray['staffer_id'] = $request['staffer_id'];
        $paramArray['token'] = $request['token'];
        if (!$this->UserLimit($paramArray)) {
            $result = array();
            $result["list"] = array();
            $result["tokeninc"] = "0";
            $res = array('error' => '1', 'errortip' => "用户token失效", 'result' => $result);
            ajax_return($res);
        }
    }


    function getComOrganizeClassView()
    {
        $reqeust = input('get.', '', 'trim');
        $this->ThisVerify($reqeust);
        $model = new StuReportModel($reqeust);
        $dataList = $model->getComOrganizeClass($reqeust);
        $res = array('error' => '0', 'errortip' => "获取集团组织架构模式", 'result' => $dataList);
        ajax_return($res);
    }

    /**
     * 获取组织架构
     * author: ling
     * 对应接口文档 0001
     */
    function getComOrganizeView()
    {
        $reqeust = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($reqeust);
        $model = new StuReportModel($reqeust);
        $dataList = $model->getComOrganize($reqeust);
        $res = array('error' => '0', 'errortip' => "获取集团组织架构", 'result' => $dataList);
        ajax_return($res);
    }

    /**
     * 获取组织架构下的学校
     * author: ling
     * 对应接口文档 0001
     */
    function getOrgnizeSchoolView()
    {
        $reqeust = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($reqeust);
        $model = new StuReportModel($reqeust);
        $dataList = $model->getOrgnizeSchool($reqeust);
        $res = array('error' => '0', 'errortip' => "获取组织学校", 'result' => $dataList);
        ajax_return($res);
    }

    /**
     * 获取学校的任务完成率
     * author: ling
     * 对应接口文档 0001
     */
    function getSchoolTaskRateView()
    {
        $reqeust = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($reqeust);
        $model = new StuReportModel($reqeust);
        $dataList = $model->getSchoolTaskRate($reqeust);
        $field = array();
        $k = 0;
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "线上任务平均完成率";
        $field[$k]["fieldstring"] = "up_taskitem_rate";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "录播任务平均完成率";
        $field[$k]["fieldstring"] = "video_taskitem_rate";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "线上检测平均分数";
        $field[$k]["fieldstring"] = "avg_test_score";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "mini book平均完成率";
        $field[$k]["fieldstring"] = "textbook_review_rate";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "配音平均分数";
        $field[$k]["fieldstring"] = "avg_audio_taskitem_score";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "网课平均参与率";
        $field[$k]["fieldstring"] = "net_school_rate";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "网课检测平均分数";
        $field[$k]["fieldstring"] = "avg_school_netscorses";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "家长平均满意度";
        $field[$k]["fieldstring"] = "classhour_memberstar";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "线下作业平均完成率";
        $field[$k]["fieldstring"] = "under_taskitem_rate";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "线下成绩平均分数";
        $field[$k]["fieldstring"] = "avg_school_testscore";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result['field'] = $field;
        $result['list'] = $dataList['list'];
        $result['allnum'] = $dataList['all_num'];
        if ($dataList['list']) {
            $res = array('error' => '0', 'errortip' => "获取学校的任务完成率", 'result' => $result);
        } else {
            $res = array('error' => '0', 'errortip' => "暂无学校的任务完成率", 'result' => $result);
        }
        ajax_return($res);
    }

    /**
     * 获取班级任务完成率
     * author: ling
     * 对应接口文档 0001
     */
    function getClassTaskRateView()
    {
        $reqeust = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($reqeust);
        $model = new StuReportModel($reqeust);
        $dataList = $model->getClassTaskRate($reqeust);
        $field = array();
        $k = 0;
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["fieldstring"] = "school_shortname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "班级名称";
        $field[$k]["fieldstring"] = "class_cnname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "班级别名";
        $field[$k]["fieldstring"] = "class_enname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "班级编号";
        $field[$k]["fieldstring"] = "class_branch";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "所属班种";
        $field[$k]["fieldstring"] = "coursecat_cnname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "所属课程别";
        $field[$k]["fieldstring"] = "course_cnname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "线上任务平均完成率";
        $field[$k]["fieldstring"] = "up_taskitem_rate";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "录播任务平均完成率";
        $field[$k]["fieldstring"] = "video_taskitem_rate";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "线上检测平均分数";
        $field[$k]["fieldstring"] = "avg_test_score";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "mini book平均完成率";
        $field[$k]["fieldstring"] = "textbook_review_rate";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "配音平均分数";
        $field[$k]["fieldstring"] = "avg_audio_taskitem_score";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "网课平均参与率";
        $field[$k]["fieldstring"] = "net_school_rate";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "网课检测平均分数";
        $field[$k]["fieldstring"] = "avg_school_netscorses";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "家长平均满意度";
        $field[$k]["fieldstring"] = "classhour_memberstar";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "线下作业平均完成率";
        $field[$k]["fieldstring"] = "under_taskitem_rate";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "线下成绩平均分数";
        $field[$k]["fieldstring"] = "avg_school_homeworkscores";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result['field'] = $field;
        $result['list'] = $dataList['list'];
        $result['allnum'] = $dataList['all_num'];
        if ($dataList['list']) {
            $res = array('error' => '0', 'errortip' => "获取班级的任务完成率", 'result' => $result);
        } else {
            $res = array('error' => '0', 'errortip' => "暂无班级的任务完成率", 'result' => $result);
        }

        ajax_return($res);
    }

    function getCoursecatView()
    {
        $reqeust = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($reqeust);
        $model = new StuReportModel($reqeust);
        $dataList = $model->getCoursecat($reqeust);
        $result['list'] = $dataList;
        $res = array('error' => '0', 'errortip' => "获取教务班种", 'result' => $result);
        ajax_return($res);
    }

    function getCourseView(){
        $reqeust = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($reqeust);
        $model = new StuReportModel($reqeust);
        $dataList = $model->getCourse($reqeust);
        $result['list'] = $dataList;
        $res = array('error' => '0', 'errortip' => "获取教务课程别", 'result' => $result);
        ajax_return($res);
    }

    /**
     * 获取所属区域的任务完成率
     */


    function getCompanyTaskRateView()
    {
        $reqeust = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($reqeust);
        $model = new StuReportModel($reqeust);
        $dataList = $model->getCompanyTaskRate($reqeust);
        $field = array();
        $k = 0;
        $field[$k]["fieldname"] = "所属区域";
        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "线上任务平均完成率";
        $field[$k]["fieldstring"] = "up_taskitem_rate";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "录播任务平均完成率";
        $field[$k]["fieldstring"] = "video_taskitem_rate";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "线上检测平均分数";
        $field[$k]["fieldstring"] = "avg_test_score";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "mini book平均完成率";
        $field[$k]["fieldstring"] = "textbook_review_rate";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "配音平均分数";
        $field[$k]["fieldstring"] = "avg_audio_taskitem_score";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "网课平均参与率";
        $field[$k]["fieldstring"] = "net_school_rate";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "网课检测平均分数";
        $field[$k]["fieldstring"] = "avg_school_netscorses";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "家长平均满意度";
        $field[$k]["fieldstring"] = "classhour_memberstar";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "线下作业平均完成率";
        $field[$k]["fieldstring"] = "under_taskitem_rate";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "线下成绩平均分数";
        $field[$k]["fieldstring"] = "avg_school_testscore";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result['field'] = $field;
        $result['list'] = $dataList;
        $result['allnum'] = 0;

        if ($dataList) {
            $res = array('error' => '0', 'errortip' => "获取学校的任务完成率", 'result' => $result);
        } else {
            $res = array('error' => '0', 'errortip' => "暂无学校的任务完成率", 'result' => $result);
        }

        ajax_return($res);

    }

}
