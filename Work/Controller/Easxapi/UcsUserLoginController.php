<?php
/**
 * 客诉-C端-登录
 */

namespace Work\Controller\Easxapi;


class UcsUserLoginController extends viewTpl
{
    /**
     * 小程序登录
     */
    function loginAction()
    {
        $request = Input('post.','','trim,addslashes');

        //微信登录
        $ucsWechatModel = new \Model\Easx\UcsWechatModel();
        $wxData = $ucsWechatModel->wxLogin($request);
//        $wxData['openid'] ='ocbJO5ZJQUhzfgRBBMCfGnKnYEbg1';

//        print_r($wxData);
        //获取用户信息
        $ucsUserModel = new \Model\Easx\UcsUserModel();
        $userData = $ucsUserModel->getUser($wxData);
//        print_r($userData);

        //根据用户信息创建token
        $ucsTokenModel = new \Model\Easx\UcsTokenModel();
        $ucsTokenModel->lssue($userData);

        $field = array();
        $field['token'] = '秘钥';

        $result = array();
        $result['field'] = $field;
        $result['data'] = $ucsTokenModel->result;

        ajax_return(array('error' => $ucsTokenModel->error, 'errortip' => $ucsTokenModel->errortip, 'result' => $result));
    }


}