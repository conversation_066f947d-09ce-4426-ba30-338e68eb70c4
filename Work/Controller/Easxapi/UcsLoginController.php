<?php
/**
 * 客诉-PC-登录
 */

namespace Work\Controller\Easxapi;


class UcsLoginController extends viewTpl
{
    public $u;
    public $t;
    public $c;
    public $visitType = "api";
    public $DataControl;
    public $Model;

    function __construct() {
        parent::__construct ();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();

        //数据库操作
        $this->DataControl = new \Dbmysql();
    }

    function ThisVerify($request){
        $paramArray = array();
        $paramArray['staffer_id'] = $request['staffer_id'];
        $paramArray['school_id'] = $request['school_id'];
        $paramArray['company_id'] = $request['company_id'];
        $paramArray['token'] = $request['token'];
        if(!$this->UserLimit($paramArray)){
            $result = array();
            $result["list"] = array();
            $result["tokeninc"] = "0";
            $res = array('error' => 1, 'errortip' => "用户token失效", 'result' => $result);
            ajax_return($res,$request['language_type']);
        }
    }

    //获取登陆个人信息
    function getStafferInfoApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Easx\UcsCommonModel($request);

        $Model->getStafferInfo($request);


        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $Model->result),$request['language_type'],1);
    }

    //更换地址
    function ChangeUrlView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model =  new \Model\Easx\UcsCommonModel($request);


        if($request['re_postbe_id'] == '0'){
            $result = $this->Model->getModuleList($request);
        }else{
            $result = $this->Model->getPowerList($request);
        }

        ajax_return($result,$request['language_type']);
    }

}