<?php
/**
 * 客诉-PC-话术
 */

namespace Work\Controller\Easxapi;


class UcsSpeechcraftController extends UcsCommonController
{
    /**
     * 话术列表
     */
    function speechcraftListView()
    {
        $request = Input('GET.','','trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Easx\UcsSpeechcraftModel($request);
        $Model->speechcraftList($request);

        $field = array();

        $k=0;
        $field[$k]["fieldstring"] = "feedbacktype_name";
        $field[$k]["fieldname"] = "投诉分类";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "feedbacktheme_name";
        $field[$k]["fieldname"] = "投诉主题";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "speechcraft_name";
        $field[$k]["fieldname"] = "话术名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "speechcraft_content";
        $field[$k]["fieldname"] = "话术内容";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "speechcraft_createtime";
        $field[$k]["fieldname"] = "发布时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();

        $result["field"] = $field;
        $result["data"] = $Model->result;

        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $result));

    }

    /**
     * 话术单条 -zjc
     */
    function speechcraftDetailView()
    {
        $request = Input('GET.','','trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Easx\UcsSpeechcraftModel($request);
        $Model->speechcraftDetail($request);
        $field = array();
        $field['speechcraft_id'] = "话术ID";
        $field['speechcraft_name'] = "话术名称";
        $field['speechcraft_content'] = "话术内容";

        $result = array();

        $result["field"] = $field;
        $result["data"] = $Model->result;

        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $result));
    }

    /**
     * 新增 提交 -zjc
     */
    function speechcraftAddAction()
    {
        $request = Input('POST.','','trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Easx\UcsSpeechcraftModel($request);
        $dataList = $Model->speechcraftadd($request);
        $field = array();
        $field['speechcraft_id'] = "话术ID";
        $field['speechcraft_name'] = "话术名称";
        $field['speechcraft_content'] = "话术内容";
        $field['feedbacktype_id'] = "类型ID";
        $field['feedbacktheme_id'] = "主题ID";
        $field['speechcraft_status'] = "话术状态";
        $field['speechcraft_createtime'] = "话术创建时间";

        $result = array();

        $result["field"] = $field;
        $result["data"] = $Model->result;

        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $result));
    }

    /**
     * 编辑提交 -zjc
     */
    function speechcraftSubmitAction()
    {
        $request = Input('POST.','','trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Easx\UcsSpeechcraftModel($request);
        $dataList = $Model->speechcraftSubmit($request);
        $field = array();
        $field['speechcraft_id'] = "话术ID";
        $field['speechcraft_name'] = "话术名称";
        $field['speechcraft_content'] = "话术内容";
        $field['feedbacktype_id'] = "类型ID";
        $field['feedbacktheme_id'] = "主题ID";
        $field['speechcraft_status'] = "话术状态";
        $field['speechcraft_createtime'] = "话术创建时间";

        $result = array();

        $result["field"] = $field;
        $result["data"] = $Model->result;

        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $result));
    }

    /**
     * 删除 -zjc
     */
    function speechcraftDeleteAction()
    {
        $request = Input('POST.','','trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Easx\UcsSpeechcraftModel($request);
        $Model->speechcraftDelete($request);
        $field = array();
        $field['speechcraft_id'] = "话术ID";

        $result = array();

        $result["field"] = $field;
        $result["data"] = $Model->result;

        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $result));
    }

}