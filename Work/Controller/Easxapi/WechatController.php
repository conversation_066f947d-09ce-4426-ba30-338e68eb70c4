<?php
/**
 * ============================================================================
 * 版权所有 : https://www.mohism.cn
 * 网站地址 : https://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/3/12
 * Time: 22:30
 */

namespace Work\Controller\Easxapi;

class WechatController extends viewTpl
{
    public $data;
    public $appId = 'wx2a66618e4feffded';
    public $appSecret = '30983395129d5ee5ee76d27a79cea406';
    public $wxuser;

    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
    }

    //获取微信反馈数据
    function getWeixinInfo($CODE)
    {
        $paramarray = array(
            'appid' => $this->appId,
            'secret' => $this->appSecret,
            'code' => $CODE,
            'grant_type' => "authorization_code"
        );

        $getBakurl = request_by_curl("https://api.weixin.qq.com/sns/oauth2/access_token", dataEncode($paramarray), "GET");
        $json_play = new \Webjson();
        $cardarray = $json_play->decode($getBakurl, "1");
        return $cardarray;
    }

    function getWeixinToken()
    {
        $token = $this->DataControl->getFieldOne("eas_weixin_token", "token_failuretime,token_string", "token_type = '1' and token_site = '1' and wxchatnumber_id = '1'", "order by token_failuretime DESC limit 0,1");
        if ($token && $token['token_failuretime'] > time()) {
            var_dump(1);
            $wxtoken = array();
            $wxtoken['access_token'] = $token['token_string'];
            $wxtoken['expires_in'] = 7200;
            return $wxtoken;
        } else {
            var_dump(2);

            $paramarray = array(
                'appid' => $this->appId,
                'secret' => $this->appSecret,
                'grant_type' => "client_credential"
            );
            $getBakurl = request_by_curl("https://api.weixin.qq.com/cgi-bin/token", dataEncode($paramarray), "GET");
            $json_play = new \Webjson();
            $cardarray = $json_play->decode($getBakurl, "1");
            var_dump($cardarray);


            $data = array();
            $data['wxchatnumber_id'] = '1';
            $data['token_site'] = '1';
            $data['token_type'] = '1';
            $data['token_string'] = $cardarray['access_token'];
            $data['token_failuretime'] = time() + $cardarray['expires_in'];
            $this->DataControl->insertData("eas_weixin_token", $data);
            return $cardarray;
        }
    }

    //获取微信User
    function getWeixinUser($token, $openid)
    {
        $paramarray = array(
            'access_token' => $token,
            'openid' => $openid,
            'lang' => "zh_CN"
        );

        $getBakurl = trim(request_by_curl("https://api.weixin.qq.com/sns/userinfo", dataEncode($paramarray), "GET"));
        $json_play = new \Webjson();
        $cardarray = $json_play->decode($getBakurl, "1");
        return $cardarray;
    }

    function mixerskipView()
    {
        $request = Input('get.','','trim,addslashes');
        if (isset($_GET['code'])) {
            $WeixinInfo = $this->getWeixinInfo($_GET['code']);
            $Weixinuser = $this->getWeixinUser($WeixinInfo['access_token'], $WeixinInfo['openid']);
            if ($Weixinuser['openid'] == '') {
                $url = "https://api.kidcastle.com.cn/Wechat/mixer";
                header("location:" . $url);
            }

            $openurl = "https://mixer.kidcastle.com.cn/?wxtoken={$Weixinuser['openid']}&imghead={$Weixinuser['headimgurl']}&nickname={$Weixinuser['nickname']}";
            header("location:{$openurl}");
            exit;
        } else {
            header("location:https://api.kidcastle.com.cn/Wechat/mixer");
            exit;
        }
    }

    //点评完成通知
    function testView()
    {
        $request = Input('post.','','trim,addslashes');

        if ($request['staffer_id'] !== '' && $request['firstnote'] !== '' && $request['keyword1'] !== '' && $request['keyword2'] !== '' && $request['keyword3'] !== '' && $request['footernote'] !== '') {

            $url = 'https://www.baidu.com';

            $this->ClassMis('oJhkowleW5siWIqGXhClaz_jBeRc', $request['firstnote'], $request['keyword1'], $request['keyword2'], $request['keyword3'], $request['footernote'], $url);

            ajax_return(array('error' => 0, 'errortip' => "微信通知发送成功!", "bakfuntion" => "okmotify"));

        } else {
            ajax_return(array('error' => 1, 'errortip' => "参数不完整!", "bakfuntion" => "errormotify"));
        }
    }

    //班级通知
    //$firstnote开始文字，$keyword1所在班级，$keyword2通知教师，$keyword3通知时间，$keyword4通知内容，$footernote结束文本
    function ClassMis($wxtoken, $firstnote, $keyword1, $keyword2, $keyword3, $footernote, $url)
    {
        $data = '{
			 "touser":"' . $wxtoken . '",
			 "template_id":"7EOOTpxwcmDJEwKFsTybJ8414Ni8VfdQxh8jCRFKEE4",
			 "url":"' . $url . '",
			 "topcolor":"#FF0000",
			 "data":{
					 "first": {
						 "value":"' . $firstnote . '",
						 "color":"#173177"
					 },
					 "keyword1":{
						 "value":"' . $keyword1 . '",
						 "color":"#173177"
					 },
					 "keyword2": {
						 "value":"' . $keyword2 . '",
						 "color":"#173177"
					 },
					 "keyword3": {
						 "value":"' . $keyword3 . '",
						 "color":"#173177"
					 },
					 "remark":{
						 "value":"' . $footernote . '",
						 "color":"#173177"
					 }
			 }
		 }';
        return $this->SendWeixinMis($data, "班级通知1111");
    }

    function DeleteHtml($str)
    {
        $str = trim($str); //清除字符串两边的空格
        $str = preg_replace("/\t/", "", $str); //使用正则表达式替换内容，如：空格，换行，并将替换为空。
        $str = preg_replace("/\r\n/", "", $str);
        $str = preg_replace("/\r/", "", $str);
        $str = preg_replace("/\n/", "", $str);
        return trim($str); //返回字符串
    }

    function WxsendView()
    {
        $request = Input('get.','','trim,addslashes');
        $logOne = $this->DataControl->getOne("ptc_wxsend_log", "log_id='{$request['log_id']}'");
        if ($logOne['log_content'] !== '' && $logOne['log_type'] !== '') {
            if ($this->SendWeixinMis($logOne['log_content'], $logOne['log_type'], $logOne['student_id'], $logOne['member_id'])) {
                ajax_return(array('error' => 0, 'errortip' => "发送成功!", "bakfuntion" => "errormotify"));
            } else {
                ajax_return(array('error' => 1, 'errortip' => "发送失败!", "bakfuntion" => "errormotify"));
            }
        } else {
            ajax_return(array('error' => 1, 'errortip' => "参数不完整!", "bakfuntion" => "errormotify"));
        }
    }

    function SendWeixinMis($data, $log_type = '')
    {
        $tokenArray = $this->getWeixinToken();
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, "https://api.weixin.qq.com/cgi-bin/message/template/send?access_token={$tokenArray['access_token']}");
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE);
        curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (compatible; MSIE 5.01; Windows NT 5.0)');
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
        curl_setopt($ch, CURLOPT_AUTOREFERER, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        $tmpInfo = curl_exec($ch);
        if (curl_errno($ch)) {
            echo 'Errno' . curl_error($ch);
        }
        curl_close($ch);
        $json_play = new \Webjson();
        $retueninfo = $json_play->decode($tmpInfo, "1");
        if ($retueninfo['errmsg'] == 'ok') {
            $date = array();
            $date['student_id'] = 1;
            $date['member_id'] = 2;
            $date['log_type'] = $log_type;
            $date['log_status'] = 1;
            $date['log_content'] = $data;
            $date['log_addtime'] = time();
            $this->DataControl->insertData("ptc_wxsend_log", $date);
            return true;
        } else {
            $date = array();
            $date['student_id'] = 1;
            $date['member_id'] = 2;
            $date['log_type'] = $log_type;
            $date['log_status'] = 0;
            $date['log_errmsg'] = $tmpInfo;
            $date['log_content'] = $data;
            $date['log_addtime'] = time();
            $this->DataControl->insertData("ptc_wxsend_log", $date);
            return false;
        }
    }

    function acceptView()
    {
        $wechatObj = new \Wechatcall("Jidebao2018", false);
        $wechatObj->getMsg();//获取操作类型
        if ($wechatObj->msgtype == "text") {
            if ($wechatObj->xmlmsg['Content'] == 'Hello2BizUser') {//微信用户第一次关注你的账号的时候，你的公众账号就会受到一条内容为'Hello2BizUser'的消息
                $reply = $wechatObj->makeText("吉的堡教育欢迎您的关注，请注册成为会员及时享受我们的会员服务！");
            } elseif ($wechatObj->xmlmsg['Content'] == "1") {
                $reply = $wechatObj->makeText("你需要做什么？");
            } elseif ($wechatObj->xmlmsg['Content'] == "2") {
                $newsData = array();
                $newsDatavar["title"] = "你需要在线咨询么！";
                $newsDatavar["description"] = "否需要在线咨询相关问题，如果需要请点击此处进行在线咨询。";
                $newsDatavar["picurl"] = "https://static.meinian365.com//pic/1502419629l726170827.jpg";
                $newsDatavar["url"] = "http://m.kidcastle.com.cn";
                $newsData[] = $newsDatavar;
                $reply = $wechatObj->makeNews($newsData);
            }
        } else {
            if ($wechatObj->msgtype == "event") {
                if ($wechatObj->xmlmsg['EventKey'] == 'LOCATION') {
                    $date = array();
                    $date['wxtoken'] = $wechatObj->xmlmsg['FromUserName'];
                    $date['Latitude'] = $wechatObj->xmlmsg['Latitude'];
                    $date['Longitude'] = $wechatObj->xmlmsg['Longitude'];
                    $date['Precision'] = $wechatObj->xmlmsg['Precision'];
                    $date['addtime'] = time();
                    //$this->DataControl->insertData('`wel_sharer_wxlocation` ',$date);
                }
            } else {
                $reply = $wechatObj->makeText("未知的操作，你可以输入相关网络关键词！" . $wechatObj->msgtype . $wechatObj->xmlmsg['EventKey']);
            }
        }

        $wechatObj->reply($reply);
        exit;
    }

    function setmenuView()
    {
        $data = '{
            "button": [
                {
                    "name": "官网活动",
                    "sub_button": [{
                            "type": "view",
                            "name": "微官网",
                            "url": "http://m.kidcastle.com.cn"
                        },
                        {
                            "type": "view",
                            "name": "小学英语阶段评估",
                            "url": "https://ptx.kidcastle.com.cn/beforeex/"
                        }
                    ]
                },
                {
                    "name": "学习中心",
                    "sub_button": [{
                            "type": "view",
                            "name": "奇趣Online",
                            "url": "https://ptc.kidcastle.com.cn/scenter"
                        },
                        {
                            "type": "view",
                            "name": "在线外教",
                            "url": "http://talk.kidcastle.cn/Weixin/Jdbwx/index?school_id=115"
                        }
                    ]
                },
                {
                    "name": "家长服务",
                    "type": "view",
                    "url": "https://api.kidcastle.com.cn/Wechat/memberlogin"
                }
            ]
        }';
        //https://api.kidcastle.com.cn/Wechat/thlogin
        $tokenArray = $this->getWeixinToken();
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, "https://api.weixin.qq.com/cgi-bin/menu/create?access_token={$tokenArray['access_token']}");
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE);
        curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (compatible; MSIE 5.01; Windows NT 5.0)');
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
        curl_setopt($ch, CURLOPT_AUTOREFERER, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        $tmpInfo = curl_exec($ch);
        if (curl_errno($ch)) {
            echo 'Errno' . curl_error($ch);
        }
        curl_close($ch);
        var_dump($tmpInfo);
    }
}
