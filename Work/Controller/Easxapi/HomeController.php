<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/13
 * Time: 23:25
 */

namespace Work\Controller\Easxapi;


use Model\Easx\HomeModel;

class HomeController extends viewTpl
{
    public $u;
    public $t;
    public $c;
    public $visitType = "api";
    public $DataControl;
    public $backData;

    //预加载处理类
    function __construct($visitType = "api")
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();

        //数据库操作
        $this->DataControl = new \Dbmysql();

    }


    //本地权限校验入口
    function ThisVerify($request)
    {

        if (!intval($request['staffer_id'])) {
            $res = array('error' => '1', 'errortip' => "未检测到招生人员信息，请重新登录操作", 'result' => array());
            ajax_return($res);
        }
        if (empty($request['token'])) {
            $res = array('error' => '1', 'errortip' => "未传入用户Token信息", 'result' => array());
            ajax_return($res);
        }
        $paramArray = array();
        $paramArray['staffer_id'] = $request['staffer_id'];
        $paramArray['token'] = $request['token'];
        if (!$this->UserLimit($paramArray)) {
            $result = array();
            $result["list"] = array();
            $result["tokeninc"] = "0";
            $res = array('error' => '1', 'errortip' => "用户token失效", 'result' => $result);
            ajax_return($res);
        }
    }

    //首页统计
    function TotalStatisticsView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Easx\HomeModel();

        $result = $Model->TotalStatistics($request);
        ajax_return($result);
    }

    //首页代办项列表
    function AgentListView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Easx\HomeModel();

        $datalist = $Model->AgentList($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldname"] = "班级id";
        $field[$k]["fieldstring"] = "class_id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "日期";
        $field[$k]["fieldstring"] = "hour_day";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "类型";
        $field[$k]["fieldstring"] = "type";
        $field[$k]["show"] = 1;
        $field[$k]["ismethod"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "代办名称";
        $field[$k]["fieldstring"] = "name";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "状态";
        $field[$k]["fieldstring"] = "status";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        $result['all_num'] = $datalist['all_num'];
        if ($datalist['list']) {
            $result['list'] = $datalist['list'];
            $res = array('error' => 0, 'errortip' => '获取代办项列表成功', 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => 1, 'errortip' => '暂无代办项列表失败', 'result' => $result);
        }
        ajax_return($res);
    }

    //班级信息 -- 下拉 -- 校园
    function getSchoolView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $datawhere = " c.company_id = '{$request['company_id']}'";
        if(isset($request['school_id']) && $request['school_id'] !== ''){
            $datawhere .= " and c.school_id = '{$request['school_id']}'";
        }

        if ($request['account_class'] != 1) {
            $postbe = $this->DataControl->selectOne(
                "
              select cp.postpart_isteregulator
             from gmc_staffer_postbe as sp
             left join smc_school_postpart as  cp ON  cp.postpart_id = sp.postpart_id
             left join gmc_company_post as  cpt ON  sp.post_id = cpt.postpart_id
             where sp.postbe_id = '{$request['re_postbe_id']}' and  (cp.postpart_isteregulator = 1 or cpt.post_istopjob =1) and cp.postpart_id >0
             ");

            $compostbe = $this->DataControl->selectOne(
                "
              select cp.postpart_isteregulator
             from gmc_staffer_postbe as sp
             left join gmc_company_postrole as  cpl ON  sp.postrole_id = cpl.postrole_id
             left join smc_school_postpart as  cp ON  cp.postpart_id = cpl.postpart_id
             where sp.postbe_id = '{$request['re_postbe_id']}' and  cp.postpart_isteregulator = 1 and cp.postpart_id >0
             ");

            if (!$postbe && !$compostbe) {
                $datawhere .= " and t.staffer_id = '{$request['staffer_id']}' ";
            }
        }
        $datalist = $this->DataControl->selectClear("SELECT s.school_id,s.school_shortname as school_cnname
                                                       FROM smc_class_hour_teaching as t
                                                       LEFT JOIN smc_class as c ON c.class_id = t.class_id
                                                       LEFT JOIN smc_school as s ON s.school_id = c.school_id
                                                       WHERE {$datawhere} AND s.school_isclose = 0 GROUP BY s.school_id");

        $result = array();
        if ($datalist) {
            $result['list'] = $datalist;
            $res = array('error' => 0, 'errortip' => '获取校园信息成功', 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => 1, 'errortip' => '暂无校园信息', 'result' => $result);
        }
        ajax_return($res);
    }

    //班级信息 -- 下拉 -- 课程别
    function getCourseView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $datawhere = " c.school_id = '{$request['school_id']}' AND (c.class_status='0' or c.class_status='1') AND sc.course_status<>'-1'";
        if ($request['account_class'] != 1) {
            $postbe = $this->DataControl->selectOne(
                "
              select cp.postpart_isteregulator
             from gmc_staffer_postbe as sp
             left join smc_school_postpart as  cp ON  cp.postpart_id = sp.postpart_id
             left join gmc_company_post as  cpt ON  sp.post_id = cpt.postpart_id
             where sp.postbe_id = '{$request['re_postbe_id']}' and  (cp.postpart_isteregulator = 1 or cpt.post_istopjob =1) and cp.postpart_id >0
             ");

            $compostbe = $this->DataControl->selectOne(
                "
              select cp.postpart_isteregulator
             from gmc_staffer_postbe as sp
             left join gmc_company_postrole as  cpl ON  sp.postrole_id = cpl.postrole_id
             left join smc_school_postpart as  cp ON  cp.postpart_id = cpl.postpart_id
             where sp.postbe_id = '{$request['re_postbe_id']}' and  cp.postpart_isteregulator = 1 and cp.postpart_id >0
             ");

            if (!$postbe && !$compostbe) {
                $datawhere .= " and t.staffer_id = '{$request['staffer_id']}' ";
            }
        }

        $datalist = $this->DataControl->selectClear("SELECT sc.course_id,sc.course_cnname,sc.course_branch
                                                       FROM smc_class_hour_teaching as t
                                                       LEFT JOIN smc_class as c ON c.class_id = t.class_id
                                                       LEFT JOIN smc_course as sc ON sc.course_id = c.course_id
                                                       WHERE {$datawhere} GROUP BY sc.course_id");

        $result = array();
        if ($datalist) {
            foreach($datalist as  &$vale){
                $vale['course_cnname'] =  $vale['course_cnname'].'('. $vale['course_branch'].')';
            }
            $result['list'] = $datalist;
            $res = array('error' => 0, 'errortip' => '获取课程别信息成功', 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => 1, 'errortip' => '暂无课程别信息', 'result' => $result);
        }
        ajax_return($res);
    }

    //班级信息 -- 下拉 -- 教师
    function getTeacherView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $datawhere = "c.company_id ='{$request['company_id']}' and c.school_id ='{$request['school_id']}' and (c.class_status = '0' or c.class_status = '1')";

        if ($request['account_class'] != 1) {
            $postbe = $this->DataControl->selectOne("select cp.postpart_isteregulator
             from gmc_staffer_postbe as sp
             left join smc_school_postpart as  cp ON  cp.postpart_id = sp.postpart_id
             left join gmc_company_post as  cpt ON  sp.post_id = cpt.postpart_id
             where sp.postbe_id = '{$request['re_postbe_id']}' and  (cp.postpart_isteregulator = 1 or cpt.post_istopjob =1) and cp.postpart_id >0
             ");

            $compostbe = $this->DataControl->selectOne("select cp.postpart_isteregulator
             from gmc_staffer_postbe as sp
             left join gmc_company_postrole as  cpl ON  sp.postrole_id = cpl.postrole_id
             left join smc_school_postpart as  cp ON  cp.postpart_id = cpl.postpart_id
             where sp.postbe_id = '{$request['re_postbe_id']}' and  cp.postpart_isteregulator = 1 and cp.postpart_id >0
             ");

            if (!$postbe && !$compostbe) {
                $datawhere .= " and t.class_id IN (SELECT tc.class_id FROM smc_class_teach AS tc WHERE tc.teach_status = '0' AND tc.staffer_id = '{$request['staffer_id']}')";
            }
        }

        $datalist = $this->DataControl->selectClear("SELECT sf.staffer_id,sf.staffer_cnname,sf.staffer_enname
                                                       FROM smc_class as c
                                                       LEFT JOIN smc_class_hour_teaching as t ON t.class_id = c.class_id
                                                       LEFT JOIN smc_staffer as sf ON sf.staffer_id = t.staffer_id
                                                       WHERE {$datawhere}  and sf.staffer_cnname is not NULL  GROUP BY sf.staffer_id");
        if($datalist){
            foreach($datalist as &$v){
                if($v['staffer_enname'] && $v['staffer_enname'] != ''){
                    $v['staffer_cnname'] = $v['staffer_cnname'] . '-' . $v['staffer_enname'];
                }
            }
        }

        $result = array();
        if ($datalist) {
            $result['list'] = $datalist;
            $res = array('error' => 0, 'errortip' => '获取教师信息成功', 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => 1, 'errortip' => '暂无教师信息', 'result' => $result);
        }
        ajax_return($res);
    }


    //班级信息 -- 下拉 -- 教室
    function getClassroomView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $datawhere = "c.company_id ='{$request['company_id']}' and c.school_id ='{$request['school_id']}' and (c.class_status = '0' or c.class_status = '1')";
        if ($request['account_class'] != '1') {
            $postbe = $this->DataControl->selectOne("select cp.postpart_isteregulator
             from gmc_staffer_postbe as sp
             left join smc_school_postpart as  cp ON  cp.postpart_id = sp.postpart_id
             left join gmc_company_post as  cpt ON  sp.post_id = cpt.postpart_id
             where sp.postbe_id = '{$request['re_postbe_id']}' and  (cp.postpart_isteregulator = 1 or cpt.post_istopjob =1) and cp.postpart_id >0
             ");

            $compostbe = $this->DataControl->selectOne("select cp.postpart_isteregulator
             from gmc_staffer_postbe as sp
             left join gmc_company_postrole as  cpl ON  sp.postrole_id = cpl.postrole_id
             left join smc_school_postpart as  cp ON  cp.postpart_id = cpl.postpart_id
             where sp.postbe_id = '{$request['re_postbe_id']}' and  cp.postpart_isteregulator = 1 and cp.postpart_id >0
             ");

            if (!$postbe && !$compostbe) {
                $datawhere .= " and c.class_id IN (SELECT tc.class_id FROM smc_class_teach AS tc WHERE tc.teach_status = '0' AND tc.staffer_id = '{$request['staffer_id']}')";
            }
        }

        $datalist = $this->DataControl->selectClear("SELECT sc.classroom_id,sc.classroom_cnname
                                                       FROM smc_class as c
                                                       LEFT JOIN smc_class_hour as h ON h.class_id = c.class_id
                                                       LEFT JOIN smc_classroom as sc ON sc.classroom_id = h.classroom_id
                                                       WHERE {$datawhere} AND sc.classroom_status='1'
                                                       GROUP BY sc.classroom_id");

        $result = array();
        if ($datalist) {
            $result['list'] = $datalist;
            $res = array('error' => 0, 'errortip' => '获取教室信息成功', 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => 1, 'errortip' => '暂无教室信息', 'result' => $result);
        }
        ajax_return($res);
    }

    //班级信息 -- 下拉 -- 班级
    function getClassView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $datawhere = "c.company_id ='{$request['company_id']}' and c.school_id = '{$request['school_id']}' and (c.class_status = '0' or c.class_status = '1')";
        if ($request['account_class'] != '1') {
            $postbe = $this->DataControl->selectOne("select cp.postpart_isteregulator
             from gmc_staffer_postbe as sp
             left join smc_school_postpart as  cp ON  cp.postpart_id = sp.postpart_id
             left join gmc_company_post as  cpt ON  sp.post_id = cpt.postpart_id
             where sp.postbe_id = '{$request['re_postbe_id']}' and  (cp.postpart_isteregulator = 1 or cpt.post_istopjob =1) and cp.postpart_id >0
             ");

            $compostbe = $this->DataControl->selectOne("select cp.postpart_isteregulator
             from gmc_staffer_postbe as sp
             left join gmc_company_postrole as  cpl ON  sp.postrole_id = cpl.postrole_id
             left join smc_school_postpart as  cp ON  cp.postpart_id = cpl.postpart_id
             where sp.postbe_id = '{$request['re_postbe_id']}' and  cp.postpart_isteregulator = 1 and cp.postpart_id >0
             ");

            if (!$postbe && !$compostbe) {
                $datawhere .= " and c.class_id IN (SELECT tc.class_id FROM smc_class_teach AS tc WHERE tc.teach_status = '0' AND tc.staffer_id = '{$request['staffer_id']}')";
            }
        }

        $sql = "SELECT c.class_id,c.class_cnname,c.class_enname FROM smc_class as c WHERE {$datawhere}";

        $datalist = $this->DataControl->selectClear($sql);

        $result = array();
        if ($datalist) {
            $result['list'] = $datalist;
            $res = array('error' => 0, 'errortip' => '获取班级信息成功', 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => 1, 'errortip' => '暂无班级信息', 'result' => $result);
        }
        ajax_return($res);
    }


    /**
     * 教务管理-小循环-我的班级
     * author: ling
     * 对应接口文档 0001
     */

    function getStaClassListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Easx\HomeModel($request);
        $field = array();
        $k = 0;
        $field[$k]["fieldname"] = "校园";
        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "班级中文名";
        $field[$k]["fieldstring"] = "class_cnname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "班级别名";
        $field[$k]["fieldstring"] = "class_enname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "班级编号";
        $field[$k]["fieldstring"] = "class_branch";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "班级类型";
        $field[$k]["fieldstring"] = "class_type";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "班级类型";
        $field[$k]["fieldstring"] = "class_typename";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;


        $field[$k]["fieldname"] = "课程别";
        $field[$k]["fieldstring"] = "course_cnname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "课程编号";
        $field[$k]["fieldstring"] = "course_branch";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "人数";
        $field[$k]["fieldstring"] = "student_nums";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["isProgress"] = 1;
        $k++;
        $field[$k]["fieldname"] = "教室";
        $field[$k]["fieldstring"] = "classroom_cnname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "状态";
        $field[$k]["fieldstring"] = "class_status_name";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;
        $field[$k]["fieldname"] = "满班人数";
        $field[$k]["fieldstring"] = "class_fullnums";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;
        $field[$k]["fieldname"] = "在班人数";
        $field[$k]["fieldstring"] = "study_num";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;


        $dataList = $Model->getStaClassList($request);
        $result["field"] = $field;
        $result["allnum"] = $dataList['allnum'];
        if ($dataList["list"]) {
            $result["list"] = $dataList['list'];
            $res = array('error' => '0', 'errortip' => '获取我的班级成功', 'allnum' => $dataList['allnum'], 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => '1', 'errortip' => '啊哦，暂无班级信息喔~', 'allnum' => 0, 'result' => $result);
        }
        ajax_return($res);
    }

    //结尾魔术函数
    function __destruct()
    {

    }
}
