<?php

namespace Work\Controller\Easxapi;

class IndexController extends viewTpl{
    public $u;
    public $t;
    public $c;
    public $visitType = "api";

    //预加载处理类
    function __construct($visitType = "api")
    {
        parent::__construct ();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
    }

    //本地权限校验入口
    function ThisVerify($request)
    {
        $paramArray = array();
        $paramArray['staffer_id'] = $request['staffer_id'];
        $paramArray['school_id'] = $request['school_id'];
        $paramArray['company_id'] = $request['company_id'];
        $paramArray['token'] = $request['token'];
        if(!$this->UserLimit($paramArray)){
            $result = array();
            $result["list"] = array();
            $result["tokeninc"] = "0";
            $res = array('error' => '1', 'errortip' => "用户token失效", 'result' => $result);
            ajax_return($res);
        }
    }

    //获取登陆个人信息
    function getStafferInfoView(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Easx\IndexModel($request);
        $result = $Model->getCompanyidApi($request);

        ajax_return($result);
    }

    //首页 -- 备课统计
    function StatisticView(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Easx\PrepareLessonsModel($request);
        if($Model->error){
            $result['is_power'] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
            ajax_return($res);
        }
        $datalist = $Model->Statistic($request);

        $result = array();
        if($datalist){
            $result['list'] = $datalist;
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
        }else{
            $result['list'] = array();
            $res = array('error' => 1, 'errortip' => '暂无数据', 'result' => $result);
        }
        ajax_return($res);
    }

    //首页 -- 备课提醒
    function LessonReminderView(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Easx\PrepareLessonsModel($request);
        if($Model->error){
            $result['is_power'] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
            ajax_return($res);
        }
        $datalist = $Model->LessonReminder($request);

        $result = array();
        if($datalist){
            $result['list'] = $datalist;
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
        }else{
            $result['list'] = array();
            if($request['account_class'] == '1' || $Model->stafferOne['postpart_isteregulator'] == '1'){
                $res = array('error' => 1, 'errortip' => '暂无备课提醒，请选择教师筛选~', 'result' => $result);
            }else{
                $res = array('error' => 1, 'errortip' => '暂无备课提醒', 'result' => $result);
            }
        }
        ajax_return($res);
    }

    //首页 -- 我的课程表
    function LessonTotalView(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Easx\PrepareLessonsModel($request);
        if($Model->error){
            $result['is_power'] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
            ajax_return($res);
        }
        $datalist = $Model->getTotal($request);

        $result = array();
        if($datalist){
            $result['list'] = $datalist;
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
        }else{
            $result['list'] = array();
            $res = array('error' => 1, 'errortip' => '暂无数据', 'result' => $result);
        }
        ajax_return($res);
    }


    //首页 -- 备课提醒
    function HomeView(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Easx\PrepareLessonsModel($request);
        if($Model->error){
            $result['is_power'] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
            ajax_return($res);
        }
        $datalist = $Model->getClassList($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldname"] = "班级ID";
        $field[$k]["fieldstring"] = "class_id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "班级名称";
        $field[$k]["fieldstring"] = "class_cnname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["judgeStause"] = 1;
        $k++;

        $field[$k]["fieldname"] = "班级编号";
        $field[$k]["fieldstring"] = "class_branch";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "教师名称";
        $field[$k]["fieldstring"] = "staffer_cnname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "课程别名称";
        $field[$k]["fieldstring"] = "course_cnname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "课程别编号";
        $field[$k]["fieldstring"] = "course_branch";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "上课时间";
        $field[$k]["fieldstring"] = "class_time";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "上课状态";
        $field[$k]["fieldstring"] = "attendclass_status_name";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $datalist['allnums'];
        if($datalist['list']){
            $result['list'] = $datalist['list'];
            $res = array('error' => 0, 'errortip' => '获取班级信息成功', 'result' => $result);
        }else{
            $result['list'] = array();
            if($request['account_class'] == '1' || $Model->stafferOne['postpart_isteregulator'] == '1'){
                $res = array('error' => '1', 'errortip' => '啊哦，暂无班级信息喔，请选择教师筛选~', 'result' => $result);
            }else{
                $res = array('error' => 1, 'errortip' => '啊哦，暂无班级信息喔~', 'result' => $result);
            }
        }
        ajax_return($res);
    }


    //获取图片
    function GetCompanyImgView(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $img = $this->DataControl->getFieldOne("gmc_company","company_logo","company_id = '{$request['company_id']}'");

        $result = array('error' => 0, 'errortip' => '获取成功', 'result' => $img['company_logo']);

        ajax_return($result);
    }

    //配置信息
    function getSelverView(){
        print_r($_SERVER['SERVER_ADDR']);
    }
}