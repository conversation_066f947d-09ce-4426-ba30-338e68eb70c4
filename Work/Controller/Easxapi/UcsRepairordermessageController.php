<?php
/**
 * 客诉-工单
 */

namespace Work\Controller\Easxapi;


class UcsRepairordermessageController extends UcsCommonController
{
    /**
     * 消息列表 -zjc
     */
    function messageListView()
    {
        $request = Input('GET.','','trim,addslashes');
//        $this->ThisVerify($request);
        $Model = new \Model\Easx\UcsRepairordermessageModel($request);
        $Model->messageList($request);
        $field = array();
        $field['message_id'] = "消息ID";
        $field['repairorder_pid'] = "工单号";
        $field['message_content'] = "消息内容";
        $field['message_status'] = "是否已读";
        $field['message_playname'] = "消息类型";
        $field['message_createdate'] = "消息日期";
        $field['repairorder_contactname'] = "工单联系人";
        $field['repairorder_contactmobile'] = "工单联系人手机号";
        $field['repairorder_updatetime'] = "工单最后更新时间";

        $result = array();

        $result["field"] = $field;
        $result["data"] = $Model->result;

        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $result));
    }

    function messageDetailView()
    {
        $request = Input('GET.','','trim,addslashes');
//        $this->ThisVerify($request);
        $Model = new \Model\Easx\UcsRepairordermessageModel($request);
        $Model->messageDetail($request);
        $field = array();
        $field['message_id'] = "消息ID";
        $field['repairorder_pid'] = "工单号";
        $field['message_content'] = "消息内容";
        $field['message_status'] = "是否已读";
        $field['message_playname'] = "消息类型";
        $field['message_createdate'] = "消息日期";

        $result = array();

        $result["field"] = $field;
        $result["data"] = $Model->result;

        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $result));
    }


}