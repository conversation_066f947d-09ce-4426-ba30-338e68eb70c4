<?php
/**
 * ============================================================================
 * 版权所有 : https://www.mohism.cn
 * 网站地址 : https://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/3/23
 * Time: 0:37
 */

namespace Work\Controller\Easxapi;

use OSS\Core\OssException;
use OSS\OssClient;

class WxevalController extends viewTpl
{
    public $u;
    public $t;
    public $c;

    const appId = 'wx2a66618e4feffded';
    const appSecret = '30983395129d5ee5ee76d27a79cea406';
    const nonceStr = 'kidcastle';
    const sha1Tool = 'Core/Tools/Eval/sig.py';
    const resultTool = 'Core/Tools/Eval/result.py';
    const evalAddress = 'https://cn-shanghai.aliyun.webginger.cloud.ssapi.cn/';
    //const evalAddress = 'https://api.cloud.ssapi.cn/';
    static $wxToken = 'https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=%s&secret=%s';
    static $wxTicket = 'https://api.weixin.qq.com/cgi-bin/ticket/getticket?access_token=%s&type=jsapi';
    static $wxSig = 'jsapi_ticket=%s&noncestr=%s&timestamp=%s&url=%s';
    static $wxAudio = 'https://api.weixin.qq.com/cgi-bin/media/get/jssdk?access_token=%s&media_id=%s';
    //static $wxAudio = 'http://file.api.weixin.qq.com/cgi-bin/media/get?access_token=%s&media_id=%s';

    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
    }

    //微信授权信息获取 -- 97  -- 微信授权登录信息
    function wxAuthorizationView(){
        $request = Input('post.','','strip_tags,trim,addslashes');
        $wxchatOne = $this->DataControl->getFieldOne("gmc_company_wxchatnumber","wxchatnumber_id,wxchatnumber_appid,wxchatnumber_appsecret"
            ,"company_id = '0' AND wxchatnumber_class = '1'");
        $paramarray = array(
            'appid' => $wxchatOne['wxchatnumber_appid'],
            'secret' => $wxchatOne['wxchatnumber_appsecret'],
            'js_code' => $request['code'],
            'grant_type' => "authorization_code"
        );

        $getBakurl = request_by_curl("https://api.weixin.qq.com/sns/jscode2session", dataEncode($paramarray),"GET");

        $json_play = new \Webjson();
        $dataArray = $json_play->decode($getBakurl,"1");

        $result["error"] = "0";
        $result["errortip"] = '用户信息存在!';
        $result["result"] = $dataArray;
        ajax_return($result);
    }

    //微信授权信息获取 -- 97
    function wxAuthorMobileView(){
        $request = Input('post.','','strip_tags,trim,addslashes');
        /**
         * sessionKey/encryptedData/iv参数均从url中获取，并赋给相应变量
         */
        $wxchatOne = $this->DataControl->getFieldOne("gmc_company_wxchatnumber","wxchatnumber_id,wxchatnumber_appid,wxchatnumber_appsecret"
            ,"company_id = '0' AND wxchatnumber_class = '1'");
        $sessionKey = $request['session_key'];
        $encryptedData = $request['encryptedData'];
        $iv = $request['iv'];

        $pc = new \WXBizDataCrypt($wxchatOne['wxchatnumber_appid'], $sessionKey);
        $errCode = $pc->decryptData($encryptedData, $iv, $data );

        if ($errCode == 0) {
            $res = array('error' => 0,'errortip' => "正确!","listjson"=>$data,"tokeninc"=>"1");
            ajax_return($res,$request['language_type']);

        } else {
            $res = array('error' => 1,'errortip' => "错误!","listjson"=>$errCode,"tokeninc"=>"1");
            ajax_return($res,$request['language_type']);
        }
    }

    //获取微信反馈数据
    public function getWeixinToken($company_id,$refresh=0){
        $wxchatnumberOne = $this->DataControl->getFieldOne("gmc_company_wxchatnumber","wxchatnumber_id,wxchatnumber_appid,wxchatnumber_appsecret","company_id = '{$company_id}' and wxchatnumber_class = '0'");
        if($wxchatnumberOne){
            $appId = $wxchatnumberOne['wxchatnumber_appid'];
            $appSecret = $wxchatnumberOne['wxchatnumber_appsecret'];
        }else{
            $wxchatnumberOne = $this->DataControl->getFieldOne("gmc_company_wxchatnumber","wxchatnumber_id,wxchatnumber_appid,wxchatnumber_appsecret","company_id = '0' and wxchatnumber_class = '0'");
            $appId = $wxchatnumberOne['wxchatnumber_appid'];
            $appSecret = $wxchatnumberOne['wxchatnumber_appsecret'];
        }

        if($refresh == 0){
            $token = $this->DataControl->getFieldOne("eas_weixin_token","token_failuretime,token_string"
                ,"wxchatnumber_id = '{$wxchatnumberOne['wxchatnumber_id']}' and token_type = '1' and token_site = '1'","order by token_failuretime DESC limit 0,1");
            if($token && $token['token_failuretime'] > time()){
                return $token['token_string'];
            }else{
                $paramarray = array(
                    'appid' => $appId,
                    'secret' => $appSecret,
                    'grant_type' => "client_credential"
                );
                $getBakurl = request_by_curl("https://api.weixin.qq.com/cgi-bin/token", dataEncode($paramarray),"GET");
                $json_play = new \Webjson();
                $cardarray = $json_play->decode($getBakurl,"1");
                $data = array();
                $data['token_site'] = '1';
                $data['token_type'] = '1';
                $data['wxchatnumber_id'] = $wxchatnumberOne['wxchatnumber_id'];
                $data['token_string'] = $cardarray['access_token'];
                $data['token_failuretime'] = time() + $cardarray['expires_in'];
                $this->DataControl->insertData("eas_weixin_token",$data);
                return $cardarray['access_token'];
            }
        }else{
            $paramarray = array(
                'appid' => $appId,
                'secret' => $appSecret,
                'grant_type' => "client_credential"
            );
            $getBakurl = request_by_curl("https://api.weixin.qq.com/cgi-bin/token", dataEncode($paramarray),"GET");
            $json_play = new \Webjson();
            $cardarray = $json_play->decode($getBakurl,"1");
            $data = array();
            $data['token_site'] = '1';
            $data['token_type'] = '1';
            $data['wxchatnumber_id'] = $wxchatnumberOne['wxchatnumber_id'];
            $data['token_string'] = $cardarray['access_token'];
            $data['token_failuretime'] = time() + $cardarray['expires_in'];
            $this->DataControl->insertData("eas_weixin_token",$data);
            return $cardarray['access_token'];
        }
    }
    /*
     * 获取微信ticket
     */
    public function getTicketView($company_id){
        $result = array();
        $at = $this->getWeixinToken($company_id);

        $wxchatnumberOne = $this->DataControl->getFieldOne("gmc_company_wxchatnumber","wxchatnumber_id,wxchatnumber_appid,wxchatnumber_appsecret","company_id = '{$company_id}' and wxchatnumber_class = '0'");
        if($wxchatnumberOne){
        }else{
            $wxchatnumberOne = $this->DataControl->getFieldOne("gmc_company_wxchatnumber","wxchatnumber_id,wxchatnumber_appid,wxchatnumber_appsecret","company_id = '0' and wxchatnumber_class = '0'");
        }
        $tokenOne = $this->DataControl->getFieldOne("eas_weixin_token","token_failuretime,token_string","wxchatnumber_id = '{$wxchatnumberOne['wxchatnumber_id']}' and token_type = '2' and token_site = '1'","order by token_failuretime DESC limit 0,1");

        if($tokenOne && $tokenOne['token_failuretime'] > time()){
            $result['token'] = $at;
            $result['ticket'] = $tokenOne['token_string'];
            return $result;
        }else{
            $tktUrl = sprintf(self::$wxTicket, $at);
            $ticketOne = json_decode(file_get_contents($tktUrl), true);
            if(isset($ticketOne['errcode']) && $ticketOne['errcode'] !== 0){
                $at = $this->getWeixinToken($company_id,1);
                $tktUrl = sprintf(self::$wxTicket, $at);
                $ticketOne = json_decode(file_get_contents($tktUrl), true);
                $data = array();
                $data['token_site'] = '1';
                $data['token_type'] = '2';
                $data['wxchatnumber_id'] = $wxchatnumberOne['wxchatnumber_id'];
                $data['token_string'] = $ticketOne['ticket'];
                $data['token_failuretime'] = time() + $ticketOne['expires_in'];
                $this->DataControl->insertData("eas_weixin_token",$data);
            }else{
                $data = array();
                $data['token_site'] = '1';
                $data['token_type'] = '2';
                $data['wxchatnumber_id'] = $wxchatnumberOne['wxchatnumber_id'];
                $data['token_string'] = $ticketOne['ticket'];
                $data['token_failuretime'] = time() + $ticketOne['expires_in'];
                $this->DataControl->insertData("eas_weixin_token",$data);
            }

            /**/
            $result['token'] = $at;
            $result['ticket'] = $ticketOne['ticket'];
            return $result;
        }
    }

    /*
     * 获取微信时间戳
     */
    public function getTimeStampView(){
        $t=time();
        return $t;
    }

    /*
     * 获取测评签名
     */
    public function getEvalSigView(){
        $sigFile = $_SERVER['DOCUMENT_ROOT'] . '/'.self::sha1Tool;
        $dir = $_SERVER['DOCUMENT_ROOT'];
        $_info = exec("python $sigFile $dir");

        $result = explode(',', $_info);
        return $result;
    }

    /*
     * 获取微信签名
     *
     */
    public function getSigView(){
        $result = array();

        $company_id = $_POST['company_id'];

        $app = $this->DataControl->getFieldOne("gmc_company_wxchatnumber","wxchatnumber_appid","company_id = '{$company_id}' and wxchatnumber_class = '0'");
        if($app){
            $appId = $app['wxchatnumber_appid'];
        }else{
            $kedingdang = $this->DataControl->getFieldOne("gmc_company_wxchatnumber","wxchatnumber_appid","company_id = '0' and wxchatnumber_class = '0'");
            $appId = $kedingdang['wxchatnumber_appid'];
        }

        $timestamp = $this->getTimeStampView();
        $ticket = $this->getTicketView($company_id);
        $url = $_POST['url'];


        $s = sha1(sprintf(self::$wxSig, $ticket['ticket'], self::nonceStr, $timestamp, $url));
        $result['signature'] = $s;
        $result['nonceStr'] = self::nonceStr;
        $result['timestamp'] = $timestamp;
        $result['appId'] = $appId;
        $result['token'] = $ticket['token'];

        echo json_encode($result);
    }

    /*
     * 获取测评结果
     */
    public function getResultView(){
        $pa = array('connect', 'start');
        $result = array();
        $token = $_POST['token'];
        $mediaId = $_POST['mediaId'];
        $jsonText = $_POST['jsonText'];

        $reValueJson = json_decode($jsonText);

        $evalSig = $this->getEvalSigView();

        foreach ($pa as $p){
            $reValueJson->$p->param->app->sig = $evalSig[0];
            $reValueJson->$p->param->app->timestamp = $evalSig[1];
            $reValueJson->$p->param->app->applicationId = $evalSig[2];
        }

        $audioUrl = sprintf(self::$wxAudio, $token, $mediaId);

        $result['audioUrl'] = $audioUrl;
        $result['textJson'] = $reValueJson;
        $result['evalUrl'] = self::evalAddress;

        echo json_encode($result);
    }

    public function savevideoView(){
        $data=array();
        $data['recordlog_apiurl'] = "Wxeval/savevideo";
        $data['recordlog_parameter'] = @mysql_escape_string(http_build_query($_POST));
        $data['recordlog_bakjson'] = @mysql_escape_string(http_build_query($_GET));
        $data['recordlog_time'] = time();
        $this->DataControl->insertData("easc_api_recordlog",$data);

        $media_id = $_POST["mediaId"];
        $company_id = $_POST["company_id"];
        $access_token = $this->getWeixinToken($company_id);

        $path = ROOT_PATH."../static/voice/";   //保存路径，相对当前文件的路径
        $outPath = "https://pic.kedingdang.com/recordvideo/";  //输出路径，给show.php 文件用，上一级

        if(!is_dir($path)){
            mkdir($path);
        }

        //微 信上传下载媒体文件
        $videoUrl = "https://api.weixin.qq.com/cgi-bin/media/get?access_token={$access_token}&media_id={$media_id}";
        $filename = "wxstu_".time().rand(1111,9999);
        $filenameurl = $filename.".amr";
        $this->downAndSaveFile($videoUrl,$path."/".$filenameurl."");
        $this->amrTransCodingMp3($filename);

        $amrFile = ROOT_PATH."../static/voice/".$filename.'.amr';
        $mp3File = ROOT_PATH."../static/voice/".$filename.'.mp3';
        $videoUrl = UpOssVideoFile($mp3File);


        /*unlink($amrFile);
        unlink($mp3File);*/

        $data = array();
        $data["access_token"] = $access_token;
        $data["path"] = $outPath.$filenameurl;
        $data["msg"] = "download record audio success!";
        $data["url"] = $videoUrl;
        $data["mediaId"] = $media_id;
        echo json_encode($data);
    }

    function http_post($url, $data_string) {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);

        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
                'X-AjaxPro-Method:ShowList',
                'Content-Type: application/json; charset=utf-8',
                'Content-Length: ' . strlen($data_string))
        );
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data_string);
        $data = curl_exec($ch);
        curl_close($ch);
        return $data;
    }



    //HTTP get 请求
    function httpGet($url) {
        $curl = curl_init();
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_TIMEOUT, 500);
        curl_setopt($curl, CURLOPT_URL, $url);

        $res = curl_exec($curl);
        curl_close($curl);

        return $res;
    }

    //根据URL地址，下载文件
    function downAndSaveFile($url,$savePath){
        ob_start();
        readfile($url);
        $img  = ob_get_contents();
        ob_end_clean();
        $size = strlen($img);
        $fp = fopen($savePath, 'a');
        fwrite($fp, $img);
        fclose($fp);
    }

    /**
     * 将amr格式转换成mp3格式
     *
     * @param $amr
     * @param $prefix_filename
     * @return mixed
     */
    public function amrTransCodingMp3($filename)
    {
        $amr = $filename.'.amr';
        $mp3 = $filename.'.mp3';
        $dir = ROOT_PATH."../static/voice/";   //保存路径，相对当前文件的路径

        exec("ffmpeg -y -i ".$dir.$amr." ".$dir.$mp3);
        return $mp3;
    }

    public function upload($newFilePath,$saveName){
        $bucket = $this -> bucket;
        $object = $newFilePath;
        $filePath = $saveName;
        try{
            $ossClient = new OssClient($this->accessKeyId,$this->AccessKeySecret,$this->endpoint);
            $res = $ossClient->uploadFile($bucket,$object,$filePath);
            return $res;
        }catch (OssException $e){
            printf(__FUNCTION__.":FAILED\n");
            printf($e->getMessage()."\n");
            return;
        }

    }



    function __destruct()
    {
        exit;
    }

}
