<?php
/**
 * 客诉-公共模块
 */
namespace Work\Controller\Easxapi;
class UcsPublicController extends viewTpl
{
    public $u;
    public $t;
    public $c;

    /**
     * 预加载处理类
     * UcsPublicController constructor.
     * @param string $visitType
     */
    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
    }

    /**
     * OSS上传文件 -zjc
     * @return 路径
     */
    function fileAction(){
        $request = Input('post.','','trim,addslashes');
        if (!$request['company_id']) {
            $res = array('error' => '1', 'errortip' => "请传入公司ID", 'result' => array());
            ajax_return($res);
        }

        if (!$request['files_type']) {
            $res = array('error' => '1', 'errortip' => "请传入文件类型", 'result' => array());
            ajax_return($res);
        }
        $this->c="Oss";
        $md5file = md5_file($_FILES['ossfile']['tmp_name']);
        $fileurl = UpOssFile($_FILES);

        $getTfile = $this->DataControl->getFieldOne('ucs_upload_files',"files_md5,files_url,files_id","files_md5='{$md5file}' and company_id = '{$request['company_id']}'");
        if($getTfile){
            $result = array();
            $result['files_url'] = $getTfile['files_url'];
            $result['files_id'] = (int)$getTfile['files_id'];
            $res = array('error' => 0,'errortip' => "文件上传成功!","result"=>$result);
            ajax_return($res);
        }else {
            $date = array();
            $date['company_id'] = $request['company_id'];
            $date['track_id'] = empty($request['track_id'])?0:$request['track_id'];
            $date['files_type'] = $request['files_type'];
            $date['files_name'] = $_FILES['ossfile']['filename'];
            $date['files_url'] = $fileurl;
            $date['files_md5'] = $md5file;
            $files_id = $this->DataControl->insertData('ucs_upload_files', $date);

            $result = array();
            $result['files_url'] = $fileurl;
            $result['files_id'] = $files_id;
            $res = array('error' => 0,'errortip' => "文件上传成功!","result"=>$result);
            ajax_return($res);
        }

    }

    /**
     * 工单来源 -zjc
     */
    function repairorderSourceView()
    {
        $request = Input('GET.','','trim,addslashes');
        $dataList = [
            'datalist' =>
                [
                    [
                        'repairorder_from'=>2,
                        'repairorder_from_name'=>"电话"
                    ],
                    [
                        'repairorder_from'=>3,
                        'repairorder_from_name'=>"公众号"
                    ],
                    [
                        'repairorder_from'=>4,
                        'repairorder_from_name'=>"其他"
                    ],

                ]
        ];
        $field = array();
        $field['repairorder_from'] ="来源0 2:电话 3:公众号 4:其他";

        $result = array();
        $result["field"] = $field;
        $result["data"] = $dataList;

        ajax_return(array('error' => 0, 'errortip' => '工单来源获取成功', 'result' => $result));

    }

    /**
     * 类型 -zjc
     */
    function feedbacktypeCatgoryView()
    {
        $request = Input('GET.','','trim,addslashes');
        $dataList = [
            'datalist' =>
                [
                    [
                        'feedbacktype_catgory'=>1,
                        'feedbacktype_catgory_name'=>'投诉'
                    ],
                    [
                        'feedbacktype_catgory'=>2,
                        'feedbacktype_catgory_name'=>'建议'
                    ],
                    [
                        'feedbacktype_catgory'=>3,
                        'feedbacktype_catgory_name'=>'表扬'
                    ],
                    [
                        'feedbacktype_catgory'=>4,
                        'feedbacktype_catgory_name'=>'其他'
                    ]

                ]
        ];
        $field = array();
        $field['feedbacktype_catgory'] ="类型 1:投诉 2:建议 3:表扬 4:其他";

        $result = array();
        $result["field"] = $field;
        $result["data"] = $dataList;

        ajax_return(array('error' => 0, 'errortip' => '投诉类型获取成功', 'result' => $result));

    }

    /**
     * 风险级别 -zjc
     */
    function repairorderLevelView()
    {
        $request = Input('GET.','','trim,addslashes');
        $dataList = [
            'datalist' =>
                [
                    [
                        'repairorder_level'=>1,
                        'repairorder_level_name'=>"1星"
                    ],
                    [
                        'repairorder_level'=>2,
                        'repairorder_level_name'=>"2星"
                    ],
                    [
                        'repairorder_level'=>3,
                        'repairorder_level_name'=>"3星"
                    ],
                    [
                        'repairorder_level'=>4,
                        'repairorder_level_name'=>"4星"
                    ],
                    [
                        'repairorder_level'=>5,
                        'repairorder_level_name'=>"5星"
                    ]

                ]
        ];
        $field = array();
        $field['repairorder_level'] ="风险级别-星 1-5";

        $result = array();
        $result["field"] = $field;
        $result["data"] = $dataList;

        ajax_return(array('error' => 0, 'errortip' => '风险级别获取成功', 'result' => $result));
    }

    /**
     * 处理状态 -zjc
     */
    function repairorderStatusView()
    {
        $request = Input('GET.','','trim,addslashes');
        $dataList = [
            'datalist' =>
                [
                    [
                        'repairorder_status'=>-2,
                        'repairorder_status_name'=>"已删除"
                    ],
                    [
                        'repairorder_status'=>0,
                        'repairorder_status_name'=>"待受理"
                    ],
                    [
                        'repairorder_status'=>1,
                        'repairorder_status_name'=>"处理中"
                    ],
                    [
                        'repairorder_status'=>2,
                        'repairorder_status_name'=>"已处理"
                    ],
                    [
                        'repairorder_status'=>3,
                        'repairorder_status_name'=>"已结案"
                    ]

                ]
        ];
        $field = array();
        $field['repairorder_status'] ="状态0 -2:已删除 0:待受理 1:处理中 2:已处理 3:已结案";

        $result = array();
        $result["field"] = $field;
        $result["data"] = $dataList;

        ajax_return(array('error' => 0, 'errortip' => '处理状态获取成功', 'result' => $result));
    }

    /**
     * 分类类型 -zjc
     */
    function feedbackTypeView()
    {
        $request = Input('GET.','','trim,addslashes');
//        $this->ThisVerify($request);
        $Model = new \Model\Easx\UcsFeedbacktypeModel($request);
        $Model->feedbacktypeList($request);
        $field = array();
        $field['feedbacktype_id'] = "客诉分类ID";
        $field['feedbacktype_name'] = "客诉分类名称";
        $field['feedbacktype_note'] = "备注";
        $result = array();

        $result["field"] = $field;
        $result["data"] = $Model->result;

        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $result));
    }

    /**
     * 主题类型 -zjc
     */
    function feedbackthemeListView()
    {
        $request = Input('GET.','','trim,addslashes');
//        $this->ThisVerify($request);
        $Model = new \Model\Easx\UcsFeedbackthemeModel($request);
        $dataList = $Model->feedbackthemeList($request);
        $field = array();
        $field['feedbacktheme_id'] = "主题ID";
        $field['feedbacktheme_name'] = "主题名称";
        $field['feedbacktheme_note'] = "备注";
        $result = array();

        $result["field"] = $field;
        $result["data"] = $Model->result;

        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $result));
    }

    /**
     * 省份列表 -zjc
     */
    function getProvinceListView()
    {
        $request = Input('GET.','','trim,addslashes');
//        $this->ThisVerify($request);
        $Model = new \Model\Easx\UcsPublicModel();
        $Model->getProvinceList($request);
        $field = array();
        $field['region_id'] = "地区ID";
        $field['region_initial'] = "地区首字母";
        $field['region_iscrown'] = "是否直辖 0:非 1:是";
        $field['region_code'] = "区域代码";
        $field['region_name'] = "地理名称";
        $field['region_enname'] = "英文名称";
        $field['region_shortenname'] = "英文简称";
        $result = array();

        $result["field"] = $field;
        $result["data"] = $Model->result;

        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $result));
    }

    /**
     * 市级列表 -zjc
     */
    function getCityListView()
    {
        $request = Input('GET.','','trim,addslashes');
//        $this->ThisVerify($request);
        $Model = new \Model\Easx\UcsPublicModel();
        $Model->getCityList($request);
        $field = array();
        $field['region_id'] = "地区ID";
        $field['region_initial'] = "地区首字母";
        $field['region_iscrown'] = "是否直辖 0:非 1:是";
        $field['region_code'] = "区域代码";
        $field['region_name'] = "地理名称";
        $field['region_enname'] = "英文名称";
        $field['region_shortenname'] = "英文简称";
        $result = array();

        $result["field"] = $field;
        $result["data"] = $Model->result;

        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $result));

    }

    /**
     * 学校列表 -zjc
     */
    function getSchoolListView()
    {
        $request = Input('GET.','','trim,addslashes');

        $CommonModel = new \Model\Easx\UcsPublicModel();
        $CommonModel->getSchoolList($request);
        $dataList = $CommonModel->result;

        $field = array();
        $field["school_id"] = "序号";
        $field["company_id"] = "所属公司";
        $field["district_id"] = "所属集团区域ID";
        $field["school_branch"] = "校区编号";
        $field["school_shortname"] = "校园简称";
        $field["school_cnname"] = "校园名称称";
        $field["school_enname"] = "检索代码称";
        $field["company_cnname"] = "机构名称";
        $field["school_address"] = "学校地址";
        $field["school_phone"] = "学校联系电话";

        $result = array();
        $result["field"] = $field;
        $result["data"] = is_array($dataList)?$dataList:array();

        if($dataList){
            $res = array('error' => '0', 'errortip' => '筛选学校成功', 'result' => $result);
        }else{
            $res = array('error' => '1', 'errortip' => '筛选学校失败', 'result' => $result);
        }

        ajax_return($res,$request['language_type'],1);

    }

    /**
     * 根据ID获取集团相关信息
     */
    function getCompanySchoolInfoView()
    {
        $request = Input('GET.','','trim,addslashes');

        $ucsPublicModel = new \Model\Easx\UcsPublicModel();
        $ucsPublicModel->getCompanySchoolInfo($request);
        $ucsPublicModel->result;

        $field = array();

        $field["company_id"] = "集团ID";
        $field["company_shortname"] = "企业简称";
        $field["company_cnname"] = "企业中文名称";
        $field["company_logo"] = "集团LOGO";
        $field["company_phone"] = "集团电话";
        $field["company_ucsservicecont"] = "集团介绍";
        $field["school_id"] = "学校ID";
        $field["school_cnname"] = "校区名称";

        $result = array();
        $result["field"] = $field;
        $result["data"] = $ucsPublicModel->result;



        ajax_return(array('error' => $ucsPublicModel->error, 'errortip' => $ucsPublicModel->errortip, 'result' => $result));

    }

    /**
     * 查询是否有未结案的工单
     */
    function getRepairorderRepeatedView()
    {

        $request = Input('GET.','','trim,addslashes');

        $ucsPublicModel = new \Model\Easx\UcsPublicModel();
        $ucsPublicModel->getRepairorderRepeated($request);
        $ucsPublicModel->result;

        $field = array();

        $field[""] = "error为1 有未结案工单 error为0 没有为结案的工单";

        $result = array();
        $result["field"] = $field;
        $result["data"] = $ucsPublicModel->result;



        ajax_return(array('error' => $ucsPublicModel->error, 'errortip' => $ucsPublicModel->errortip, 'result' => $result));

    }

    /**
     * 微信小程序二维码 文章详情
     */
    function getQRcodeApi()
    {
        $request = Input('get.','','trim,addslashes');

        $ucsPublicModel = new \Model\Easx\UcsWechatModel();
        $res = $ucsPublicModel->getQRcodeApi($request);

        $result = array();
        $result["data"]['datalist']['qdImg'] = $res;

        ajax_return(array('error' => 0, 'errortip' => '获取成功', 'result' => $result));
    }



}