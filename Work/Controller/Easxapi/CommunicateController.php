<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/2/13
 * Time: 16:04
 */

namespace Work\Controller\Easxapi;

use Model\Easx\CommunicateModel;

class CommunicateController extends viewTpl
{
    public $u;
    public $t;
    public $c;

    //预加载处理类
    function __construct($visitType = "api")
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
    }

    //本地权限校验入口
    function ThisVerify($request)
    {
        $paramArray = array();
        $paramArray['staffer_id'] = $request['staffer_id'];
        $paramArray['school_id'] = $request['school_id'];
        $paramArray['company_id'] = $request['company_id'];
        $paramArray['token'] = $request['token'];
        if (!$this->UserLimit($paramArray)) {
            $result = array();
            $result["list"] = array();
            $result["tokeninc"] = "0";
            $res = array('error' => '1', 'errortip' => "用户token失效", 'result' => $result);
            ajax_return($res);
        }
    }

    //学员沟通管理
    function HomeView(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new CommunicateModel($request);
        $datalist = $Model->getTotal($request);

        $result = array();
        $result['allnum'] = $datalist['allnums'];
        $result['await_num'] = $datalist['await_num'];

        $class_cnname = $this->DataControl->getFieldOne("smc_class","class_cnname","class_id = '{$request['class_id']}'");
        if ($datalist['list']) {
            $result['list'] = $datalist['list'];
            $res = array('error' => 0, 'errortip' => '获取学员沟通信息成功', 'result' => $result, 'class_cnname' =>$class_cnname['class_cnname']);
        } else {
            $result['list'] = array();
            $res = array('error' => 1, 'errortip' => '暂无学员沟通信息', 'result' => $result, 'class_cnname' =>$class_cnname['class_cnname']);
        }
        ajax_return($res);
    }


    //学员沟通记录
    function RecordView(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new CommunicateModel($request);
        $datalist = $Model->getRecordList($request);

        $result = array();
        $result['allnum'] = $datalist['allnums'];
        if ($datalist['list']) {
            $result['list'] = $datalist['list'];
            $res = array('error' => 0, 'errortip' => '获取学员沟通记录成功', 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => 1, 'errortip' => '暂无学员沟通记录', 'result' => $result);
        }
        ajax_return($res);
    }

    //添加沟通记录
    function AddRecordAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new CommunicateModel($request);
        $result = $Model->AddRecord($request);

        ajax_return($result);
    }

    //查看学员沟通记录
    function getRecordOneView(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new CommunicateModel($request);
        $datalist = $Model->getRecordOne($request);

        $result = array();
        if ($datalist) {
            $result['list'] = $datalist;
            $res = array('error' => 0, 'errortip' => '获取学员沟通记录成功', 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => 1, 'errortip' => '暂无学员沟通记录', 'result' => $result);
        }
        ajax_return($res);
    }

    //获取班级
    function getClassView(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new CommunicateModel($request);
        $datalist = $Model->getClass($request);

        $result = array();
        if($datalist){
            $result['list'] = $datalist;
            $res = array('error' => 0, 'errortip' => '获取班级成功', 'result' => $result);
        }else{
            $result['list'] = array();
            $res = array('error' => 1, 'errortip' => '暂无班级数据', 'result' => $result);
        }
        ajax_return($res);
    }

    //获取沟通模板
    function getTemplateApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new CommunicateModel($request);
        $datalist = $Model->getTemplateApi($request);

        $result = array();
        $result['allnum'] = $datalist['allnums'];
        if ($datalist['list']) {
            $result['list'] = $datalist['list'];
            $res = array('error' => 0, 'errortip' => '获取沟通模板成功', 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => 1, 'errortip' => '暂无沟通模板', 'result' => $result);
        }
        ajax_return($res);

    }
}