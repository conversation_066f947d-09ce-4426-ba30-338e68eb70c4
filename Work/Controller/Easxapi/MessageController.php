<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/13
 * Time: 23:25
 */

namespace Work\Controller\Easxapi;


use Model\Easx\MessageModel;

class MessageController extends viewTpl
{
    public $u;
    public $t;
    public $c;
    public $visitType = "api";
    public $DataControl;
    public $backData;

    //预加载处理类
    function __construct($visitType = "api")
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();

        //数据库操作
        $this->DataControl = new \Dbmysql();

    }


    //本地权限校验入口
    function ThisVerify($request)
    {

        if (!intval($request['staffer_id'])) {
            $res = array('error' => '1', 'errortip' => "未检测到招生人员信息，请重新登录操作", 'result' => array());
            ajax_return($res);
        }
        if (empty($request['token'])) {
            $res = array('error' => '1', 'errortip' => "未传入用户Token信息", 'result' => array());
            ajax_return($res);
        }
        $paramArray = array();
        $paramArray['staffer_id'] = $request['staffer_id'];
        $paramArray['token'] = $request['token'];
        if (!$this->UserLimit($paramArray)) {
            $result = array();
            $result["list"] = array();
            $result["tokeninc"] = "0";
            $res = array('error' => '1', 'errortip' => "用户token失效", 'result' => $result);
            ajax_return($res);
        }
    }

    //班级通知列表
    function ClassMessageView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Easx\MessageModel();

        $result = $Model->ClassMessage($request);
        ajax_return($result);
    }

    //查看阅读情况
    function ReadSituationView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Easx\MessageModel();

        $result = $Model->ReadSituation($request);
        ajax_return($result);
    }

    //选择接收人班级
    function ChoiceSelecterClassView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Easx\MessageModel();

        $result = $Model->ChoiceSelecterClass($request);
        ajax_return($result);
    }

    //选择接收人学员
    function ChoiceSelecterStudentView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Easx\MessageModel();

        $result = $Model->ChoiceSelecterStudent($request);
        ajax_return($result);
    }

    //发布通知
    function SendMessageAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Easx\MessageModel();

        $result = $Model->SendMessageAction($request);
        ajax_return($result);
    }

    //查看接收人
    function GetSelectorView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Easx\MessageModel();

        $result = $Model->GetSelector($request);
        ajax_return($result);
    }

    //修改密码
    function updatePassAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Easx\MessageModel();

        $result = $Model->updatePassAction($request);
        ajax_return($result);
    }

    //修改头像
    function updateImgAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Easx\MessageModel();

        $result = $Model->updateImgAction($request);
        ajax_return($result);
    }

    //班级通知详情
    function MessageDetailView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Easx\MessageModel();

        $result = $Model->MessageDetail($request);
        ajax_return($result);
    }

    //根据student_id查学员名字
    function StudentCnnameView(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Easx\MessageModel();

        $result = $Model->StudentCnname($request);
        ajax_return($result);
    }

    //个人通知列表
    function PersonnalMessageView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Easx\MessageModel();

        $result = $Model->PersonnalMessage($request);
        ajax_return($result);
    }

    //学校公告列表
    function SchoolMessageView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Easx\MessageModel();

        $result = $Model->SchoolMessage($request);
        ajax_return($result);
    }

    //获取班级信息
    function GetClassInfoView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Easx\MessageModel();

        $result = $Model->GetClassInfo($request);
        ajax_return($result);
    }

    //Pc班级通知列表
    function PcClassMessageView(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Easx\MessageModel();

        $datalist = $Model->PcClassMessage($request);

        $field = array();
        if($request['type'] == '1'){
            $k = 0;
            $field[$k]["fieldname"] = "通知ID";
            $field[$k]["fieldstring"] = "message_id";
            $field[$k]["show"] = 0;
            $field[$k]["custom"] = 0;
            $k++;

            $field[$k]["fieldname"] = "标题";
            $field[$k]["fieldstring"] = "message_title";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldname"] = "阅读状态";
            $field[$k]["fieldstring"] = "num";
            $field[$k]["ismethod"] = 1;
            $field[$k]["isTitle"] = 1;
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldname"] = "发布时间";
            $field[$k]["fieldstring"] = "message_createtime";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

        }else{
            $k = 0;
            $field[$k]["fieldname"] = "通知ID";
            $field[$k]["fieldstring"] = "message_id";
            $field[$k]["show"] = 0;
            $field[$k]["custom"] = 0;
            $k++;

            $field[$k]["fieldname"] = "班级中文名";
            $field[$k]["fieldstring"] = "class_cnname";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldname"] = "班级别名";
            $field[$k]["fieldstring"] = "class_enname";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldname"] = "时间";
            $field[$k]["fieldstring"] = "message_createtime";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldname"] = "教师";
            $field[$k]["fieldstring"] = "staffer_cnname";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldname"] = "通知标题";
            $field[$k]["fieldstring"] = "message_title";
            $field[$k]["show"] = 1;
            $field[$k]["ismethod"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldname"] = "已读人数";
            $field[$k]["fieldstring"] = "num";
            $field[$k]["ismethod"] = 1;
            $field[$k]["isTitle"] = 1;
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

        }

        $result = array();
        $result["field"] = $field;
        $result['all_num'] = $datalist['all_num'];
        if ($datalist['list']) {
            $result['list'] = $datalist['list'];
            $res = array('error' => 0, 'errortip' => '获取Pc班级通知列表成功', 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => 1, 'errortip' => '暂时还没有班级通知哦~', 'result' => $result);
        }
        ajax_return($res);
    }

    //Pc公告列表
    function PcNoticeMessageView(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Easx\MessageModel();

        $datalist = $Model->PcNoticeMessage($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldname"] = "通知ID";
        $field[$k]["fieldstring"] = "message_id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "时间";
        $field[$k]["fieldstring"] = "message_createtime";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "发布对象";
        $field[$k]["fieldstring"] = "message_object_name";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "通知标题";
        $field[$k]["fieldstring"] = "message_title";
        $field[$k]["show"] = 1;
        $field[$k]["ismethod"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "创建用户";
        $field[$k]["fieldstring"] = "staffer_cnname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        $result['all_num'] = $datalist['all_num'];
        if ($datalist['list']) {
            $result['list'] = $datalist['list'];
            $res = array('error' => 0, 'errortip' => '获取Pc公告列表成功', 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => 1, 'errortip' => '暂时没有公告哦~', 'result' => $result);
        }
        ajax_return($res);
    }

    //删除通知/公告
    function delMessageAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Easx\MessageModel();

        $result = $Model->delMessageAction($request);
        ajax_return($result);
    }

    //编辑公告
    function updateMessageAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Easx\MessageModel();

        $result = $Model->updateMessageAction($request);
        ajax_return($result);
    }

    //发布公告
    function createMessageAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Easx\MessageModel();

        $result = $Model->createMessageAction($request);
        ajax_return($result);
    }

    //教师下拉
    function MessageTeaListPCApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Easx\MessageModel();
        $res = $Model->MessageTeaListPCApi($request);
        $result = array();
        if ($res) {
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res);
    }

    //班级下拉
    function MessageClassListPCApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Easx\MessageModel();
        $res = $Model->MessageClassListPCApi($request);
        $result = array();
        if ($res) {
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res);
    }

    //
    function ClassApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Easx\MessageModel();
        $res = $Model->ClassApi($request);
        $result = array();
        if ($res) {
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res);
    }

    //学校下拉
    function getSchoolApi()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $sql = "select school_id,school_cnname,school_enname,school_branch from smc_school where company_id = '{$request['company_id']}' and school_isclose = '0'";
        $CompaniesList = $this->DataControl->selectClear($sql);
        if (!$CompaniesList) {
            $CompaniesList = array();
        }

        $result["list"] = $CompaniesList;
        $res = array('error' => 0, 'errortip' => '获取学校下拉', 'result' => $result);

        ajax_return($res);
    }







//结尾魔术函数
    function __destruct()
    {

    }
}
