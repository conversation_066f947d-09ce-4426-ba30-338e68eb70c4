<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/2/21
 * Time: 15:06
 */

namespace Work\Controller\Easxapi;

use Model\Easx\EvaluateModel;

class EvaluateController extends viewTpl
{
    public $u;
    public $t;
    public $c;

    //预加载处理类
    function __construct($visitType = "api")
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
    }

    //本地权限校验入口
    function ThisVerify($request)
    {
        $paramArray = array();
        $paramArray['staffer_id'] = $request['staffer_id'];
        $paramArray['school_id'] = $request['school_id'];
        $paramArray['company_id'] = $request['company_id'];
        $paramArray['token'] = $request['token'];
        if (!$this->UserLimit($paramArray)) {
            $result = array();
            $result["list"] = array();
            $result["tokeninc"] = "0";
            $res = array('error' => '1', 'errortip' => "用户token失效", 'result' => $result);
            ajax_return($res);
        }
    }

    //班级统计列表
    function HomeView(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new EvaluateModel($request);
        $datalist = $Model->EvaluateList($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldname"] = "班级ID";
        $field[$k]["fieldstring"] = "class_id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "班级中文名";
        $field[$k]["fieldstring"] = "class_cnname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "班级别名";
        $field[$k]["fieldstring"] = "class_enname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "班级编号";
        $field[$k]["fieldstring"] = "class_branch";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "教师名称";
        $field[$k]["fieldstring"] = "staffer_cnname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "上课次数";
        $field[$k]["fieldstring"] = "hour_num";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "出勤人数";
        $field[$k]["fieldstring"] = "hourstudy_num";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "评价人数";
        $field[$k]["fieldstring"] = "evaluate_num";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "评价率";
        $field[$k]["fieldstring"] = "evaluate_rate";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "回评人数";
        $field[$k]["fieldstring"] = "return_num";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "回评率";
        $field[$k]["fieldstring"] = "return_rate";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        $result['allnum'] = $datalist['allnums'];
        if ($datalist['list']) {
            $result['list'] = $datalist['list'];
            $res = array('error' => 0, 'errortip' => '获取班级统计信息成功', 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => 1, 'errortip' => '暂无班级统计信息', 'result' => $result);
        }
        ajax_return($res);
    }

    //教师统计列表
    function StafferListView(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new EvaluateModel($request);
        $datalist = $Model->StafferList($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldname"] = "教师ID";
        $field[$k]["fieldstring"] = "staffer_id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        if((isset($request['class_id']) && $request['class_id'] !=='') || (isset($request['rs_staffer_id']) && $request['rs_staffer_id'] !=='')){
            $field[$k]["fieldname"] = "上课时间";
            $field[$k]["fieldstring"] = "hour_time";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;
        }

        $field[$k]["fieldname"] = "教师中文名";
        $field[$k]["fieldstring"] = "staffer_cnname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "教师英文名";
        $field[$k]["fieldstring"] = "staffer_enname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "上课次数";
        $field[$k]["fieldstring"] = "hour_num";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "出勤人数";
        $field[$k]["fieldstring"] = "hourstudy_num";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "评价人数";
        $field[$k]["fieldstring"] = "evaluate_num";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "评价率";
        $field[$k]["fieldstring"] = "evaluate_rate";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "回评人数";
        $field[$k]["fieldstring"] = "return_num";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "回评率";
        $field[$k]["fieldstring"] = "return_rate";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        $result['allnum'] = $datalist['allnums'];
        if ($datalist['list']) {
            $result['list'] = $datalist['list'];
            $res = array('error' => 0, 'errortip' => '获取教师统计信息成功', 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => 1, 'errortip' => '暂无教师统计信息', 'result' => $result);
        }
        ajax_return($res);
    }

    //学员统计列表
    function StudentListView(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new EvaluateModel($request);
        $datalist = $Model->StudentList($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldname"] = "学员ID";
        $field[$k]["fieldstring"] = "student_id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "学员中文名";
        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "学员英文名";
        $field[$k]["fieldstring"] = "student_enname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "学员编号";
        $field[$k]["fieldstring"] = "student_branch";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "班级";
        $field[$k]["fieldstring"] = "class_cnname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "课程别";
        $field[$k]["fieldstring"] = "course_cnname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "上课教师";
        $field[$k]["fieldstring"] = "staffer_cnname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "上课时间";
        $field[$k]["fieldstring"] = "hour_time";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "上课内容";
        $field[$k]["fieldstring"] = "hour_content";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "教师评价";
        $field[$k]["fieldstring"] = "staffer_appraise";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["ismethod"] = 1;
        $k++;

        $field[$k]["fieldname"] = "学习意愿";
        $field[$k]["fieldstring"] = "score_num";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        $result['allnum'] = $datalist['allnums'];
        if ($datalist['list']) {
            $result['list'] = $datalist['list'];
            $res = array('error' => 0, 'errortip' => '获取学员统计信息成功', 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => 1, 'errortip' => '暂无学员统计信息', 'result' => $result);
        }
        ajax_return($res);
    }

    //班级 -- 查看评价状况
    function CevaluateStateView(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new EvaluateModel($request);
        $datalist = $Model->CevaluateState($request);

        $result = array();
        if ($datalist) {
            $result['list'] = $datalist['list'];
            $result['evaluate_num'] = $datalist['evaluate_num'];
            $result['return_num'] = $datalist['return_num'];
            $result['attendance_num'] = $datalist['attendance_num'];
            $res = array('error' => 0, 'errortip' => '获取班级评价状况信息成功', 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => 1, 'errortip' => '暂无班级评价状况信息', 'result' => $result);
        }
        ajax_return($res);
    }

    //教师 -- 查看评价状况
    function SevaluateStateView(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new EvaluateModel($request);
        $datalist = $Model->SevaluateState($request);

        $result = array();
        if ($datalist) {
            $result['list'] = $datalist['list'];
            $result['evaluate_num'] = $datalist['evaluate_num'];
            $result['return_num'] = $datalist['return_num'];
            $result['attendance_num'] = $datalist['attendance_num'];
            $res = array('error' => 0, 'errortip' => '获取教师评价状况信息成功', 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => 1, 'errortip' => '暂无教师评价状况信息', 'result' => $result);
        }
        ajax_return($res);
    }

    //查看评价明细
    function EvaluateDetailView(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new EvaluateModel($request);
        $datalist = $Model->EvaluateDetail($request);

        $result = array();
        if ($datalist) {
            $result['list'] = $datalist;
            $res = array('error' => 0, 'errortip' => '获取评价明细信息成功', 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => 1, 'errortip' => '暂无评价明细信息', 'result' => $result);
        }
        ajax_return($res);
    }


    //评价查询
    function EvaluateQueryView(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new EvaluateModel($request);
        $datalist = $Model->EvaluateQuery($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldname"] = "评价ID";
        $field[$k]["fieldstring"] = "hourcomment_id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "上课时间";
        $field[$k]["fieldstring"] = "hour_time";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "班级中文名";
        $field[$k]["fieldstring"] = "class_cnname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "班级别名";
        $field[$k]["fieldstring"] = "class_enname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "教师";
        $field[$k]["fieldstring"] = "staffer_cnname";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "满意度";
        $field[$k]["fieldstring"] = "hourcomment_level_name";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "评价标签";
        $field[$k]["fieldstring"] = "noun_word";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "评价内容";
        $field[$k]["fieldstring"] = "hourcomment_content";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "是否匿名";
        $field[$k]["fieldstring"] = "hourcomment_anonymous_name";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "学员中文名";
        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "学员英文名";
        $field[$k]["fieldstring"] = "student_enname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "学员编号";
        $field[$k]["fieldstring"] = "student_branch";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "主要联系手机";
        $field[$k]["fieldstring"] = "family_mobile";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "创建时间";
        $field[$k]["fieldstring"] = "hourcomment_createtime";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        $result['allnum'] = $datalist['allnums'];
        if ($datalist['list']) {
            $result['list'] = $datalist['list'];
            $res = array('error' => 0, 'errortip' => '获取评价查询信息成功', 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => 1, 'errortip' => '暂无评价查询信息', 'result' => $result);
        }
        ajax_return($res);
    }

    //评价统计
    function ScoreTotalView(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new EvaluateModel($request);
        $datalist = $Model->ScoreTotal($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldname"] = "上课时间";
        $field[$k]["fieldstring"] = "hour_time";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "班级中文名";
        $field[$k]["fieldstring"] = "class_cnname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "班级别名";
        $field[$k]["fieldstring"] = "class_enname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "主教老师";
        $field[$k]["fieldstring"] = "staffer_cnname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "助教老师";
        $field[$k]["fieldstring"] = "fu_staffer_cnname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "出勤人次";
        $field[$k]["fieldstring"] = "hourstudy_num";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "评价人数";
        $field[$k]["fieldstring"] = "hourcomment_num";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "非常满意";
        $field[$k]["fieldstring"] = "hourcomment_satisfied_num";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "一般";
        $field[$k]["fieldstring"] = "hourcomment_soso_num";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "不满意";
        $field[$k]["fieldstring"] = "hourcomment_dissatisfied_num";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        $result['allnum'] = $datalist['allnums'];
        if ($datalist['list']) {
            $result['list'] = $datalist['list'];
            $res = array('error' => 0, 'errortip' => '获取评价统计信息成功', 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => 1, 'errortip' => '暂无评价统计信息', 'result' => $result);
        }
        ajax_return($res);
    }

    //出勤学员评价详情
    function AttendanceView(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new EvaluateModel($request);
        $datalist = $Model->Attendance($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldname"] = "学员ID";
        $field[$k]["fieldstring"] = "student_id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "班级";
        $field[$k]["fieldstring"] = "class_cnname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "上课时间";
        $field[$k]["fieldstring"] = "hour_time";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "学员中文名";
        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "学员英文名";
        $field[$k]["fieldstring"] = "student_enname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "学员编号";
        $field[$k]["fieldstring"] = "student_branch";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "评分";
        $field[$k]["fieldstring"] = "hourcomment_score";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "评价内容";
        $field[$k]["fieldstring"] = "hourcomment_content";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        $result['allnum'] = $datalist['allnums'];
        if ($datalist['list']) {
            $result['list'] = $datalist['list'];
            $res = array('error' => 0, 'errortip' => '获取教师评价统计信息成功', 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => 1, 'errortip' => '暂无教师评价统计信息', 'result' => $result);
        }
        ajax_return($res);
    }

    //教师+班级评价统计
    function ScoreTotalTwoView(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new EvaluateModel($request);
        $datalist = $Model->ScoreTotalTwo($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldname"] = "教师ID";
        $field[$k]["fieldstring"] = "staffer_id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "教师中文名";
        $field[$k]["fieldstring"] = "staffer_cnname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "班级中文名";
        $field[$k]["fieldstring"] = "class_cnname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "班级别名";
        $field[$k]["fieldstring"] = "class_enname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "出勤人数";
        $field[$k]["fieldstring"] = "hourstudy_num";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "收到评价数";
        $field[$k]["fieldstring"] = "evaluate_num";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["ismethod"] = true;
        $k++;

        $field[$k]["fieldname"] = "评价率";
        $field[$k]["fieldstring"] = "evaluate_rate";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "平均分";
        $field[$k]["fieldstring"] = "average_score";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        $result['allnum'] = $datalist['allnums'];
        if ($datalist['list']) {
            $result['list'] = $datalist['list'];
            $res = array('error' => 0, 'errortip' => '获取教师评价统计信息成功', 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => 1, 'errortip' => '暂无教师评价统计信息', 'result' => $result);
        }
        ajax_return($res);
    }


    //沟通管理
    function getStudyStudentListView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new EvaluateModel($request);
        $dataList = $Model->getStudyStudentList($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldname"] = "姓名";
        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "学号";
        $field[$k]["fieldstring"] = 'student_branch';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "性别";
        $field[$k]["fieldstring"] = 'student_sex';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "出生日期";
        $field[$k]["fieldstring"] = 'student_birthday';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "沟通次数";
        $field[$k]["fieldstring"] = 'catitrack_num';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "上次沟通日期";
        $field[$k]["fieldstring"] = 'catitrack_day';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        if ($dataList["list"]) {
            $result["list"] = $dataList['list'];
            $result["allnum"] = $dataList['allnum'];
            $res = array('error' => '0', 'errortip' => '获取课时', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => '1', 'errortip' => '暂无课时', 'allnum' => 0, 'result' => $result);
        }

        ajax_return($res);
    }

    //获取沟通记录
    function getCatitrackApi()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);

        $Model = new EvaluateModel($request);
        $dataList = $Model->getCatitrackApi($request);

        $result = array();
        $result['catitrack'] = $dataList['catitrack'];
        $result['allnum'] = $dataList['allnum'];
        if ($dataList["list"]) {
            $result["list"] = $dataList['list'];
            $result["info"] = $dataList['info'];
            $res = array('error' => '0', 'errortip' => '获取沟通记录', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => '1', 'errortip' => '暂无沟通记录', 'result' => $result);
        }
        ajax_return($res);
    }

    //新增访谈记录
    function addStuCatitrackAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);

        $Model = new EvaluateModel($request);
        $Model->addStuCatitrackAction($request);

        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res);
    }

    //获取沟通模板
    function getTemplateApi()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);

        $Model = new EvaluateModel($request);
        $dataList = $Model->getTemplateApi($request);

        $result = array();
        if ($dataList) {
            $result["list"] = $dataList;
            $res = array('error' => '0', 'errortip' => '获取沟通模板', 'allnum' => 0, 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => '1', 'errortip' => '暂无沟通模板', 'allnum' => 0, 'result' => $result);
        }
        ajax_return($res);
    }

    /**
     * 家长评价教师 家长评价教师学校列表 - 后台PC
     * 作者: wgh
     * @param  对应接口文档 417->1
     * @return array
     */
    function EvaluateSchListPCApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);

        $Model = new EvaluateModel($request);
        $res = $Model->EvaluateSchListPC($request);
        $result = array();
        if ($res) {
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取学校信息成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res);
    }

    /**
     * 家长评价教师 家长评价教师班级列表 - 后台PC
     * 作者: wgh
     * @param  对应接口文档 417->2
     * @return array
     */
    function EvaluateClassListPCApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);

        $Model = new EvaluateModel($request);
        $res = $Model->EvaluateClassListPC($request);
        $result = array();
        if ($res) {
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取班级信息成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res);
    }

    /**
     * 家长评价教师 家长评价教师教师列表 - 后台PC
     * 作者: wgh
     * @param  对应接口文档 417->3
     * @return array
     */
    function EvaluateTeaListPCApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);
        $Model = new EvaluateModel($request);
        $res = $Model->EvaluateTeaListPC($request);
        $result = array();
        if ($res) {
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取教师信息成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res);
    }
}