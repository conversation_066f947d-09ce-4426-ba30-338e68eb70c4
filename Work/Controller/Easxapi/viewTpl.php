<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2016/12/13
 * Time: 21:21
 */

namespace Work\Controller\Easxapi;

class viewTpl
{
    public $DataControl;
    public $router;

    public function __construct()
    {
        global $router;
        // 指定允许其他域名访问
        header('Access-Control-Allow-Origin:*');
        // 响应类型
        header('Access-Control-Allow-Methods:*');
        // 响应头设置
        header('Access-Control-Allow-Headers:x-requested-with,content-type');

        //数据库操作
        $this->DataControl = new \Dbmysql();
        //操作类型
        $this->router = $router;

    }

    //第三方接口权限验证
    function UserLimit($paramArray)
    {
        $stafferOne = $this->DataControl->getFieldOne('smc_staffer', "staffer_tokencode", "staffer_id='{$paramArray['staffer_id']}'");
        if ($stafferOne) {
            $md5tokenbar = base64_encode(md5($stafferOne["staffer_tokencode"] . date("Y-m-d")));
            if ($md5tokenbar != $paramArray['token']) {
                return false;
            } else {
                return true;
            }
        } else {
            return false;
        }
    }

    function ThisVerify($request)
    {
        $paramArray = array();
        $paramArray['staffer_id'] = $request['staffer_id'];
        $paramArray['school_id'] = $request['school_id'];
        $paramArray['company_id'] = $request['company_id'];
        $paramArray['token'] = $request['token'];
        if (!$this->UserLimit($paramArray)) {
            $result = array();
            $result["list"] = array();
            $result["tokeninc"] = "0";
            $res = array('error' => '1', 'errortip' => "用户token失效", 'result' => $result);
            ajax_return($res);
        }
    }

    //发送短信
    public function Sendmisgocom($mobile, $mistxt, $tilte, $sendcode, $company_id = '0')
    {
        $publicarray = array();
        $publicarray['company_id'] = $company_id;
        $minsendModel = new \Model\Api\SmsModel($publicarray);
        return $minsendModel->gmcMisSend($mobile, $mistxt, $tilte, $sendcode);
    }

    function createOrderPid($initial)
    {
        $Str = "ABCDEFGHIJKLMNOPQRSTUVWXYZ123456789";
        $rangtr = $Str[rand(0, 34)] . $Str[rand(0, 34)] . $Str[rand(0, 34)] . $Str[rand(0, 34)];
        $rangtime = date("ymdHis", time());
        $rangnum = rand(10000, 99999);
        $OrderPID = $initial . $rangtr . $rangtime . $rangnum;
        return $OrderPID;
    }

    function createOutPid()
    {
        $Str = "ABCDEFGHIJKLMNOPQRSTUVWXYZ123456789";
        $rangtr = $Str[rand(0, 34)] . $Str[rand(0, 34)] . $Str[rand(0, 34)] . $Str[rand(0, 34)];
        $rangtime = date("ymdHis", time());
        $rangnum = rand(10000000, 99999999);
        $OutPID = $rangtr . $rangtime . $rangnum;
        return $OutPID;
    }


    public function addSmcWorkLog($company_id, $school_id, $staffer_id, $module, $type, $content, $module_id = 0)
    {
//    	$module = $this->DataControl->selectClear("select module_name from  imc_module where  module_id ='{$module_id}' ");
//    	$module = $this->DataControl->selectClear("select module_name from  imc_module where  module_id ='{$module_id}' ");

        $logData = array();
        $logData['company_id'] = $company_id;
        $logData['school_id'] = $school_id;
        $logData['staffer_id'] = $staffer_id;
        $logData['worklog_module'] = $module;
        $logData['worklog_type'] = $type;
        $logData['worklog_content'] = $content;
        $logData['worklog_ip'] = real_ip();
        $logData['worklog_time'] = time();
        $this->DataControl->insertData('smc_staffer_worklog', $logData);
    }


    //导入单个帐号
    public function insertAccountOne($identifier, $nickname, $headimg)
    {
        $random = rand(0, **********);
        $json = '{
   "Identifier":"' . $identifier . '",
   "Nick":"' . $nickname . '",
   "FaceUrl":"' . $headimg . '"
}';
        $sig = $this->getUserSig('kedingdang_2020');

        $url = "https://console.tim.qq.com/v4/im_open_login_svc/account_import?sdkappid=**********&identifier=kedingdang_2020&usersig={$sig}&random={$random}&contenttype=json";

        $getBakurl = trim(request_by_curl($url, $json, "POST"));
        $json_play = new \Webjson();
        $cardarray = $json_play->decode($getBakurl, "1");

        return $cardarray;

    }

    //创建群聊
    public function createGroup($groupname)
    {
        $random = rand(0, **********);
        $json = '{
  "Owner_Account": "",
  "Type": "Private",
  "Name": "' . $groupname . '"
}';

        $url = "https://console.tim.qq.com/v4/group_open_http_svc/create_group?sdkappid=**********&identifier=kedingdang_2020&usersig=eJyrVgrxCdYrSy1SslIy0jNQ0gHzM1NS80oy0zLBwtmpKZl56SmJeenxRgZGMCXFKdmJBQWZKUpWhiYGBsbGlsbG5hCZ1IqCzKJUoLipqamRgYEBRLQkMxcsZmlkamxgYmoINSUzHWhDcoy*U76Bs2*Us1OKZXqlv3lQYnl6aGVWiIV7jH6Ao3Gyq7*TtktySEmpi3mkrVItAKNyNHQ_&random={$random}&contenttype=json";


        $getBakurl = trim(request_by_curl($url, $json, "POST"));
        $json_play = new \Webjson();
        $cardarray = $json_play->decode($getBakurl, "1");

//        var_dump($cardarray);die();

        return $cardarray;

    }

    //创建群聊
    public function addFriend($fromid,$toid)
    {
        $random = rand(0, **********);
        $json = '{
 "From_Account":"'.$fromid.'",
 "AddFriendItem":
 [
     {
         "To_Account":"'.$toid.'",
         "AddSource":"AddSource_Type_XXXXXXXX"
     }
 ]
}';
        $sig = $this->getUserSig('kedingdang_2020');

        $url = "https://console.tim.qq.com/v4/sns/friend_add?sdkappid=**********&identifier=kedingdang_2020&usersig={$sig}&random={$random}&contenttype=json";


        $getBakurl = trim(request_by_curl($url, $json, "POST"));
        $json_play = new \Webjson();
        $cardarray = $json_play->decode($getBakurl, "1");

        return $cardarray;

    }

    //增加群成员
    public function addGroupMember()
    {
        $random = rand(0, **********);
        $json = '{
  "GroupId": "@TGS#1AHEKORGA",
  "MemberList": [ 
  {
      "Member_Account": "T201908020009"
  }]
}';

        $url = "https://console.tim.qq.com/v4/group_open_http_svc/add_group_member?sdkappid=**********&identifier=kedingdang_2020&usersig=eJyrVgrxCdYrSy1SslIy0jNQ0gHzM1NS80oy0zLBwtmpKZl56SmJeenxRgZGMCXFKdmJBQWZKUpWhiYGBsbGlsbG5hCZ1IqCzKJUoLipqamRgYEBRLQkMxcsZmlkamxgYmoINSUzHWhDcoy*U76Bs2*Us1OKZXqlv3lQYnl6aGVWiIV7jH6Ao3Gyq7*TtktySEmpi3mkrVItAKNyNHQ_&random={$random}&contenttype=json";

        $getBakurl = trim(request_by_curl($url, $json, "POST"));
        $json_play = new \Webjson();
        $cardarray = $json_play->decode($getBakurl, "1");

//        var_dump($cardarray);

        return $cardarray;

    }

    //生成usersig
    public function getUserSig($identifier)
    {
        $Model = new \Model\Easx\UserSigModel();
        $res = $Model->genSig($identifier);
        return $res;
    }

    //验证usersig
    public function verifySig()
    {
        $Model = new UserSigModel();
        $res = $Model->verifySigWithUserBuf('eJyrVgrxCdYrSy1SslIy0jNQ0gHzM1NS80oy0zLBwiWpxSWGhlCZ4pTsxIKCzBQlKwOIQGpFQWZRqpKVoampqZGBAVS0JDMXLGZpZGJiYWFqCtWcmQ40r7zSwjUkLa2qPCPDILciL9IjPb0sL8cvwz3C3KOgONnFOMM8pDjL0tzbM9RWqRYAB14wqA__', 'test11');
        var_dump($res);
    }

    //登录日志记录表
    function addStafferLoginLog($company_id, $staffer_id, $loginlog_type, $loginlog_source)
    {
        $date = array();
        $date['company_id'] = $company_id;//集团ID
        $date['staffer_id'] = $staffer_id;//员工ID
        $date['loginlog_type'] = $loginlog_type;//登录端口 0 总入口  1 集团 2 校务 3 CRM 4 教务 5 助教 6 助学
        $date['loginlog_source'] = $loginlog_source;//客户端来源 0 PC 1 手机
        $date['loginlog_lastip'] = real_ip();
        $date['loginlog_time'] = time();
        $this->DataControl->insertData('imc_staffer_loginlog', $date);
        return true;
    }


    public function __call($method, $args)
    {
        echo "unknown method " . $method;
        return false;

    }

    /**
     * 对象转数组
     * @param $object
     * @return mixed
     */
    function objectToArray(&$object)
    {
        $object = json_decode(json_encode($object), true);
        return $object;
    }

}
