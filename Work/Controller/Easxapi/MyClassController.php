<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/13
 * Time: 23:25
 */

namespace Work\Controller\Easxapi;


use Model\Easx\MyClassModel;

class MyClassController extends viewTpl
{
    public $u;
    public $t;
    public $c;
    public $visitType = "api";
    public $DataControl;
    public $backData;

    //预加载处理类
    function __construct($visitType = "api")
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();

        //数据库操作
        $this->DataControl = new \Dbmysql();

    }


    //本地权限校验入口
    function ThisVerify($request)
    {

        if (!intval($request['staffer_id'])) {
            $res = array('error' => '1', 'errortip' => "未检测到招生人员信息，请重新登录操作", 'result' => array());
            ajax_return($res);
        }
        if (empty($request['token'])) {
            $res = array('error' => '1', 'errortip' => "未传入用户Token信息", 'result' => array());
            ajax_return($res);
        }
        $paramArray = array();
        $paramArray['staffer_id'] = $request['staffer_id'];
        $paramArray['token'] = $request['token'];
        if (!$this->UserLimit($paramArray)) {
            $result = array();
            $result["list"] = array();
            $result["tokeninc"] = "0";
            $res = array('error' => '1', 'errortip' => "用户token失效", 'result' => $result);
            ajax_return($res);
        }
    }

    //班级列表
    function ClassListView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Easx\MyClassModel();

        $result = $Model->ClassList($request);
        ajax_return($result);
    }

    //班级列表
    function telClassListView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Easx\MyClassModel();

        $result = $Model->telClassList($request);
        ajax_return($result);
    }

    //通讯录
    function TimListView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Easx\MyClassModel();

        $result = $Model->TimList($request);
        ajax_return($result);
    }

    //班级详情
    function ClassDetailView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Easx\MyClassModel();

        $result = $Model->ClassDetail($request);
        ajax_return($result);
    }

    //班级人数
    function ClassStudentView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Easx\MyClassModel();

        $result = $Model->ClassStudent($request);
        ajax_return($result);
    }

    //课表
    function TeachingListView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Easx\MyClassModel();

        $result = $Model->TeachingList($request);
        ajax_return($result);
    }

    //班级教师
    function ClassTeacherView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Easx\MyClassModel();

        $result = $Model->ClassTeacher($request);
        ajax_return($result);
    }

    //班别下拉
    function CourseListApi()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $sql = "
            SELECT
                co.course_id,
                co.course_cnname 
            FROM
                smc_class_teach AS t
                LEFT JOIN smc_class AS c ON t.class_id = c.class_id
                LEFT JOIN smc_course AS co ON c.course_id = co.course_id 
            WHERE
                t.staffer_id = '{$request['staffer_id']}' and c.school_id = '{$request['school_id']}' and c.class_status > '-2'
                group by co.course_id";
        $list = $this->DataControl->selectClear($sql);
        if (!$list) {
            $list = array();
        }
        $result["list"] = $list;
        $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        ajax_return($res);
    }

//结尾魔术函数
    function __destruct()
    {

    }
}
