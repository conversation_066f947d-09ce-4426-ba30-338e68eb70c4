<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/13
 * Time: 23:25
 */

namespace Work\Controller\Easxapi;


use Model\Easx\UserSigModel;


class UserSigController extends viewTpl
{
    public $u;
    public $t;
    public $c;
    public $visitType = "api";
    public $DataControl;
    public $backData;

    //预加载处理类
    function __construct($visitType = "api")
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();

        //数据库操作
        $this->DataControl = new \Dbmysql();

    }

    //验证usersig
    function verifySigView(){
        $Model = new UserSigModel();
        $res = $Model->verifySigWithUserBuf('eJwtjMEKgkAURf-lrUPeDDPYE1oU0UKjKF3kMnCMRxmmNmrRv2fT3N09nHvfkG3TwJoGIpABwsx1Lsy945IdziRKRIWEvwivtMX1XNdcQIR-YIaaGwOR0FpP','T20200409000001');
        var_dump($res);
    }


    //结尾魔术函数
    function __destruct()
    {

    }
}
