<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/13
 * Time: 23:25
 */

namespace Work\Controller\Easxapi;


use Model\Easx\MineModel;

class MineController extends viewTpl
{
    public $u;
    public $t;
    public $c;
    public $visitType = "api";
    public $DataControl;
    public $backData;

    //预加载处理类
    function __construct($visitType = "api")
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();

        //数据库操作
        $this->DataControl = new \Dbmysql();

    }


    //本地权限校验入口
    function ThisVerify($request)
    {

        if (!intval($request['staffer_id'])) {
            $res = array('error' => '1', 'errortip' => "未检测到招生人员信息，请重新登录操作", 'result' => array());
            ajax_return($res);
        }
        if (empty($request['token'])) {
            $res = array('error' => '1', 'errortip' => "未传入用户Token信息", 'result' => array());
            ajax_return($res);
        }
        $paramArray = array();
        $paramArray['staffer_id'] = $request['staffer_id'];
        $paramArray['token'] = $request['token'];
        if (!$this->UserLimit($paramArray)) {
            $result = array();
            $result["list"] = array();
            $result["tokeninc"] = "0";
            $res = array('error' => '1', 'errortip' => "用户token失效", 'result' => $result);
            ajax_return($res);
        }
    }

    //个人资料
    function PersonnalInfoView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Easx\MineModel();

        $result = $Model->PersonnalInfo($request);
        ajax_return($result);
    }

    //修改出生日期
    function ChangeBirthdayAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Easx\MineModel();

        $result = $Model->ChangeBirthdayAction($request);
        ajax_return($result);
    }

    //查看家庭联系信息
    function FamilyInfoView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Easx\MineModel();

        $result = $Model->FamilyInfo($request);
        ajax_return($result);
    }

    //反馈学校列表
    function ComplainSchoolView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Easx\MineModel();

        $result = $Model->ComplainSchool($request);
        ajax_return($result);
    }

    //反馈班级列表
    function ComplainClassView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Easx\MineModel();

        $result = $Model->ComplainClass($request);
        ajax_return($result);
    }

    //反馈教师列表
    function ComplainTeacherView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Easx\MineModel();

        $result = $Model->ComplainTeacher($request);
        ajax_return($result);
    }

    //发布投诉
    function SendComplainAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Easx\MineModel();

        $result = $Model->SendComplainAction($request);
        ajax_return($result);
    }

    //切换孩子列表
    function ChangeStudentListView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Easx\MineModel();

        $result = $Model->ChangeStudentList($request);
        ajax_return($result);
    }

    //修改学员头像
    function ChangeImgAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Easx\MineModel();

        $result = $Model->ChangeImgAction($request);
        ajax_return($result);
    }

    //我的成长
    function MyGrowUpView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Easx\MineModel();

        $result = $Model->MyGrowUp($request);
        ajax_return($result);
    }

    //上课统计
    function ClassStatisticsView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Easx\MineModel();

        $result = $Model->ClassStatistics($request);
        ajax_return($result);
    }

    //课消详情
    function ClassPayDetailView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Easx\MineModel();

        $result = $Model->ClassPayDetail($request);
        ajax_return($result);
    }


    //投诉列表(学校)
    function ScComplainListView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Easx\MineModel();

        $datalist = $Model->ScComplainList($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldname"] = "投诉id";
        $field[$k]["fieldstring"] = "complain_id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "投诉与建议";
        $field[$k]["fieldstring"] = "complain_content";
        $field[$k]["show"] = 1;
        $field[$k]["ismethod"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        if ($request['account_class'] == '1') {
            $field[$k]["fieldname"] = "学员中文名";
            $field[$k]["fieldstring"] = "student_cnname";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldname"] = "学员英文名";
            $field[$k]["fieldstring"] = "student_enname";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldname"] = "学员编号";
            $field[$k]["fieldstring"] = "student_branch";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldname"] = "主要联系电话";
            $field[$k]["fieldstring"] = "family_mobile";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;
        }

        $field[$k]["fieldname"] = "创建时间";
        $field[$k]["fieldstring"] = "complain_createtime";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result['all_num'] = $datalist['all_num'];
        if ($datalist['list']) {
            $result['list'] = $datalist['list'];
            $res = array('error' => 0, 'errortip' => '获取投诉列表成功', 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => 1, 'errortip' => '暂无投诉列表信息', 'result' => $result);
        }
        ajax_return($res);
    }

    //投诉列表(教师)
    function TeaComplainListView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Easx\MineModel();

        $datalist = $Model->TeaComplainList($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldname"] = "投诉id";
        $field[$k]["fieldstring"] = "complain_id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "教师中文名";
        $field[$k]["fieldstring"] = "staffer_cnname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "教师英文名";
        $field[$k]["fieldstring"] = "staffer_enname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "教师编号";
        $field[$k]["fieldstring"] = "staffer_branch";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "投诉与建议";
        $field[$k]["fieldstring"] = "complain_content";
        $field[$k]["ismethod"] = 1;
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        if ($request['account_class'] == '1') {
            $field[$k]["fieldname"] = "学员中文名";
            $field[$k]["fieldstring"] = "student_cnname";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldname"] = "学员英文名";
            $field[$k]["fieldstring"] = "student_enname";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldname"] = "学员编号";
            $field[$k]["fieldstring"] = "student_branch";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldname"] = "主要联系电话";
            $field[$k]["fieldstring"] = "family_mobile";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;
        }

        $field[$k]["fieldname"] = "创建时间";
        $field[$k]["fieldstring"] = "complain_createtime";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result['all_num'] = $datalist['all_num'];
        if ($datalist['list']) {
            $result['list'] = $datalist['list'];
            $res = array('error' => 0, 'errortip' => '获取投诉列表成功', 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => 1, 'errortip' => '暂无投诉列表信息', 'result' => $result);
        }
        ajax_return($res);
    }

    //投诉详情
    function ComplainDetailView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Easx\MineModel();

        $result = $Model->ComplainDetail($request);
        ajax_return($result);
    }


//结尾魔术函数
    function __destruct()
    {

    }
}
