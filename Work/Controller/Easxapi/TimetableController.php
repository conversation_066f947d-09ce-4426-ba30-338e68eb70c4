<?php


namespace Work\Controller\Easxapi;


class TimetableController extends viewTpl
{
    public $u;
    public $t;
    public $c;

    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
    }

    function monthListApi()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Easx\TimetableModel();
        $res = $Model->monthList($request);
        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res);
    }

    function timetableListApi()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Easx\TimetableModel();
        $res = $Model->studentTimetable($request);
        $result = array();
        if ($res) {
            $result["list"] = $res['list'];
            $result["allnum"] = $res['allnum'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res);

    }

    function isHourApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Easx\TimetableModel();
        $res = $Model->isHourApi($request);
        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res);

    }

    //教师端 回放
    function LineThreeRecordPlaybackApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Easx\TimetableModel($request);
        $result = $Model->LineThreeRecordPlayback($request);
        if($result){
            $res = array('error' => 0, 'errortip' => '获取回放课程', 'result' => $result);
        }else{
            $res = array('error' => 1, 'errortip' => '视频正在录制中～', 'result' => array());
        }
        ajax_return($res);
    }

    //pc 针对 天
    function timetableDayListApi()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Easx\TimetableModel();
        $res = $Model->studentTimetableDay($request);
        $result = array();
        if ($res) {
            $result["field"] = $res['field'];
            $result["list"] = $res['list'];
            $result["allnum"] = $res['allnum'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["field"] = array();
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res);
    }

    function hourItemApi()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Easx\TimetableModel();
        $res = $Model->hourItem($request);
        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res);

    }

    function hourStuRemarkApi()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Easx\TimetableModel();
        $res = $Model->hourStuRemark($request);
        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res);

    }

    function hourContentApi()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Easx\TimetableModel();
        $res = $Model->hourContent($request);
        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res);

    }

    function updateHourContentAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Easx\TimetableModel();
        $res = $Model->updateHourContent($request);
        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '修改成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res);

    }

    function LineThreeTeUrlApi(){
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $teachOne = $this->DataControl->selectOne("select teaching_type from smc_class_hour_teaching where hour_id='{$request['hour_id']}' and staffer_id='{$request['staffer_id']}' ");
        if($teachOne['teaching_type'] ==1){
            $res = array('error' => 1, 'errortip' => "助教暂不支持手机端线上课", 'result' => array());
            ajax_return($res);
        }

        $Model = new \Model\Easx\TimetableModel($request);
        $resUrl = $Model->LineThreeTeUrl($request);
        if ($resUrl) {
            $res = array('error' => 0, 'errortip' => "成功获取地址", 'result' => $resUrl);
            ajax_return($res);
        } else {
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => array());
            ajax_return($res);
        }
    }


    /**
     * 发布作业 单个班级的信息 - 后台PC
     * 作者: 97
     * @param  对应接口文档 330
     * @return array
     */
    function timetableOneApi()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Easx\TimetableModel();
        $res = $Model->timetableOne($request);

        $field = array();
        $field["class_id"] = "班级id";
        $field["class_branch"] = "班级编号";
        $field["class_cnname"] = "班级中文名称";
        $field["class_enname"] = "班级别名";
        $field["class_fullnums"] = "满班人数";
        $field["course_id"] = "课程ID";
        $field["course_cnname"] = "课程名称";
        $field["course_branch"] = "课程编号";
        $field["school_branch"] = "校区编号";
        $field["school_shortname"] = "学校简称";
        $field["studentnum"] = "班级实际人数";
        $field["teachername"] = "教师";
        $field["classroom"] = "教室";
        $field["classhourlist"] = "课时列表";


        $result = array();
        if ($res) {
            $result["field"] = $field;
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["field"] = array();
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res);

    }


    /**
     *   报表-教师课表
     * author: ling
     * 对应接口文档 0001
     */
    function getTeacTimetableView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);

        $TimetableModel = new \Model\Easx\TimetableModel($request);
        $dataList = $TimetableModel->getTeacTimetable($request);


        $result = array();
        $field = array();

        $field[0]["fieldname"] = "教师名称";
        $field[0]["fieldstring"] = "staffer_cnname";
        $field[0]["show"] = 1;
        $field[0]["custom"] = 0;
        $field[0]["is_title"] = 1;


        if ($dataList) {

            foreach ($dataList['week_date'] as $key => $value) {
                $field[$key + 1]["fieldname"] = "{$value['weekday']}";
                $field[$key + 1]["fieldstring"] = "{$value['week_day']}";
                $field[$key + 1]["show"] = 1;
                $field[$key + 1]["custom"] = 0;
            }
            unset($dataList['week_date']);
        } else {
            $enweekarray = array("Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday",);
            $weekarray = array("一", "二", "三", "四", "五", "六", "日",);

            for ($i = 0; $i < 7; $i++) {
                $day = date('Y-m-d', strtotime("+$i day", strtotime($request['hour_startday'])));
                $field[$i + 1]["fieldname"] = $day . '周' . $weekarray[$i];
                $field[$i + 1]["fieldstring"] = $enweekarray[$i];
                $field[$i + 1]["show"] = 1;
                $field[$i + 1]["custom"] = 0;
            }

        }

        $result['field'] = $field;
        if ($dataList) {
            $result['list'] = $dataList;
        } else {
            $result['list'] = array();
        }

        if (!$dataList) {
            $res = array('error' => '1', 'errortip' => '暂无教师课表记录', 'result' => $result);
        } else {
            $res = array('error' => '0', 'errortip' => '获取信息', 'result' => $result);
        }
        ajax_return($res);
    }

    /**
     *  报表-教室课表
     * author: ling
     * 对应接口文档 0001
     */
    function roomTimeTableView()
    {
        $request = Input('get.','','trim,addslashes');
		$this->ThisVerify($request);
        $TimetableModel = new \Model\Easx\TimetableModel($request);
        $dataList = $TimetableModel->roomTimeTable($request);
        $result = array();
        $field = array();

        $field[0]["fieldname"] = "时间段";
        $field[0]["fieldstring"] = "time_quantum";
        $field[0]["show"] = 1;
        $field[0]["custom"] = 0;
        $field[0]["is_title"] = 1;
        if ($dataList) {

            foreach ($dataList['week_date'] as $key => $value) {
                $field[$key + 1]["fieldname"] = "{$value['weekday']}";
                $field[$key + 1]["fieldstring"] = "{$value['week_day']}";
                $field[$key + 1]["show"] = 1;
                $field[$key + 1]["custom"] = 0;
            }
            unset($dataList['week_date']);
        } else {
            $enweekarray = array("Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday");
            $weekarray = array("一", "二", "三", "四", "五", "六", "日",);

            for ($i = 0; $i < 7; $i++) {
                $day = date('Y-m-d', strtotime("+$i day", strtotime($request['hour_startday'])));
                $field[$i + 1]["fieldname"] = $day . '周' . $weekarray[$i];
                $field[$i + 1]["fieldstring"] = $enweekarray[$i];
                $field[$i + 1]["show"] = 1;
                $field[$i + 1]["custom"] = 0;
            }
        }

        $result['field'] = $field;
        if ($dataList) {
            $result['list'] = $dataList;
        } else {
            $result['list'] = array();
        }
        if ($dataList) {
            $res = array('error' => '0', 'errortip' => '获取信息', 'result' => $result);
        } else {
            $res = array('error' => '1', 'errortip' => '暂无教室课表记录', 'result' => $result);
        }

        ajax_return($res);
    }

    /**
     *  报表-教师详情课表
     * author: ling
     * 对应接口文档 0001
     */
    function stafferTimeTableView()
    {

        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);
        $TimetableModel = new \Model\Easx\TimetableModel($request);
        $dataList = $TimetableModel->stafferTimeTable($request);
        $result = array();
        $field = array();

        $field[0]["fieldname"] = "时间段";
        $field[0]["fieldstring"] = "time_quantum";
        $field[0]["show"] = 1;
        $field[0]["custom"] = 0;
        $field[0]["is_title"] = 1;
        if ($dataList) {

            foreach ($dataList['week_date'] as $key => $value) {
                $field[$key + 1]["fieldname"] = "{$value['weekday']}";
                $field[$key + 1]["fieldstring"] = "{$value['week_day']}";
                $field[$key + 1]["show"] = 1;
                $field[$key + 1]["custom"] = 0;
            }
            unset($dataList['week_date']);
        } else {
            $enweekarray = array("Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday");
            $weekarray = array("一", "二", "三", "四", "五", "六", "日",);

            for ($i = 0; $i < 7; $i++) {
                $day = date('Y-m-d', strtotime("+$i day", strtotime($request['hour_startday'])));
                $field[$i + 1]["fieldname"] = $day . '周' . $weekarray[$i];
                $field[$i + 1]["fieldstring"] = $enweekarray[$i];
                $field[$i + 1]["show"] = 1;
                $field[$i + 1]["custom"] = 0;
            }

        }

        $result['field'] = $field;
        if ($dataList) {
            $result['list'] = $dataList;
        } else {
            $result['list'] = array();
        }
        if (!$dataList) {
            $res = array('error' => '1', 'errortip' => '暂无教师课表记录', 'result' => $result);
        } else {
            $res = array('error' => '0', 'errortip' => '获取信息', 'result' => $result);
        }

        ajax_return($res);


    }

    /**
     * 报表下拉-获取教室
     * author: ling
     * 对应接口文档 0001
     */
    function getClassroomListView()
    {
        $request = Input('get.','','trim,addslashes');
		$this->ThisVerify($request);
        $TimetableModel = new \Model\Easx\TimetableModel($request);
        $dataList = $TimetableModel->getClassroomList($request);
        if ($dataList) {
            $res = array('error' => '0', 'errortip' => '获取教室成功', 'result' => $dataList);
        } else {
            $res = array('error' => '1', 'errortip' => '暂无教室记录', 'result' => array());
        }
        ajax_return($res);
    }

    /**
     *  获取课程详情
     * author: ling
     * 对应接口文档 0001
     */
    function classCourseOneApi()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);

        $CourseModel = new \Model\Smc\CourseModel($request);
        $hourOne = $CourseModel->getCourseOne($request['hour_id']);

        $result = array();
        $result["list"]['hour'] = $hourOne;
        $res = array('error' => '0', 'errortip' => '获取信息', 'result' => $result);

        ajax_return($res);
    }


    /**
     * 报表下拉-教师空闲时间表
     * author: ling
     * 对应接口文档 0001
     */
    function getStafferTeachingTimeView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);
        $TimetableModel = new \Model\Easx\TimetableModel($request);

        $dataList = $TimetableModel->getStafferTeachingTime($request);
        $result = array();
        $field = array();

        $k = 0 ;
        $field[$k]["fieldname"] = "教师中文名";
        $field[$k]["fieldstring"] = "staffer_cnname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++ ;
        $field[$k]["fieldname"] = "教师英文名";
        $field[$k]["fieldstring"] = "staffer_enname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++ ;
        $field[$k]["fieldname"] = "教师编号";
        $field[$k]["fieldstring"] = "staffer_branch";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++ ;
        $field[$k]["fieldname"] = "所属校区";
        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++ ;
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
         $k++ ;
        $field[$k]["fieldname"] = $dataList['month']."月份带班数";
        $field[$k]["fieldstring"] = "teaching_classnum";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++ ;
        $field[$k]["fieldname"] = $dataList['month']."月份课时数";
        $field[$k]["fieldstring"] = "teaching_hournum";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result['field'] = $field;
        if ($dataList) {
            $result['list'] = $dataList['list'];
            $result['all_num'] = $dataList['allnum'];
        } else {
            $result['list'] = array();
            $result['all_num'] = 0;
        }
        if (!$dataList) {
            $res = array('error' => '1', 'errortip' => '暂无教师课带课时间表', 'result' => $result);
        } else {
            $res = array('error' => '0', 'errortip' => '获取信息', 'result' => $result);
        }

        ajax_return($res);
    }


    /**
     * 获取集团的教学职务
     * author: ling
     * 对应接口文档 0001
     */
    function getCompanyTeaPostApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);
        $postList = $this->DataControl->selectClear("select DISTINCT p.post_name,p.post_id 
            from gmc_company_post  as  p 
            left join gmc_staffer_postbe as pt ON pt.post_id = p.post_id
            where  p.post_isteaching = 1 and  p.company_id='{$request['company_id']}' and pt.staffer_id>0 and pt.postpart_id>0
            ");

        if(!$postList){
            $postList = array();
        }
        $res = array('error' => '0', 'errortip' => '获取信息', 'result' => $postList);
        ajax_return($res);
    }



}