<?php
/**
 * 客诉-PC-工单
 */

namespace Work\Controller\Easxapi;


class UcsRepairorderController extends UcsCommonController
{

    /**
     * 案件库 -zjc
     */
    function repairorderListView()
    {
        $request = Input('GET.','','trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Easx\UcsRepairorderModel($request);
        $dataList = $Model->repairorderList($request);
        $field = array();

        $k=0;
        $field[$k]["fieldstring"] = "school_name";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "repairorder_from";
        $field[$k]["fieldname"] = "工单来源";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "repairorder_pid";
        $field[$k]["fieldname"] = "工单号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["ismethod"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "repairorder_issensitive_name";
        $field[$k]["fieldname"] = "工单类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["isRed"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "repairorder_createdate";
        $field[$k]["fieldname"] = "录入日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "repairorder_level";
        $field[$k]["fieldname"] = "风险级别";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["isLevel"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "feedbacktype_catgory_name";
        $field[$k]["fieldname"] = "类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "feedbacktype_name";
        $field[$k]["fieldname"] = "分类名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "feedbacktheme_name";
        $field[$k]["fieldname"] = "主题名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "repairorder_contactname";
        $field[$k]["fieldname"] = "投诉人称呼";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "repairorder_contactmobile";
        $field[$k]["fieldname"] = "投诉人联系方式";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "first_staffer_name";
        $field[$k]["fieldname"] = "受理人";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "staffer_name";
        $field[$k]["fieldname"] = "当前受理人";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "confirm_staffer_name";
        $field[$k]["fieldname"] = "结案人名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "repairorder_confirmdate";
        $field[$k]["fieldname"] = "结案人日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "repairorder_processtime";
        $field[$k]["fieldname"] = "处理时长";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "repairorder_updatetime";
        $field[$k]["fieldname"] = "最后跟踪时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "track_type_name";
        $field[$k]["fieldname"] = "流转状态 ";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "repairorder_status_name";
        $field[$k]["fieldname"] = "状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;


        $result = array();

        $result["field"] = $field;
        $result["data"] = $Model->result;

        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $result));
    }

    /**
     * 我的工单列表 -zjc
     */
    function myRepairorderListView()
    {
        $request = Input('GET.','','trim,addslashes');
        $this->ThisVerify($request);
        $ucsRepairorderModel = new \Model\Easx\UcsRepairorderModel($request);
        $ucsRepairorderModel->myRepairorderList($request);
        $field = array();
        $k=0;
        $field[$k]["fieldstring"] = "repairorder_from";
        $field[$k]["fieldname"] = "工单来源";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "repairorder_pid";
        $field[$k]["fieldname"] = "工单号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "repairorder_issensitive_name";
        $field[$k]["fieldname"] = "工单类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["isRed"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "repairorder_createdate";
        $field[$k]["fieldname"] = "录入时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "repairorder_level";
        $field[$k]["fieldname"] = "风险级别";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["isLevel"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "feedbacktype_catgory_name";
        $field[$k]["fieldname"] = "投诉类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "feedbacktype_name";
        $field[$k]["fieldname"] = "投诉分类";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "feedbacktheme_name";
        $field[$k]["fieldname"] = "投诉主题";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "repairorder_contactname";
        $field[$k]["fieldname"] = "投诉人称呼";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "repairorder_contactmobile";
        $field[$k]["fieldname"] = "联系方式";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "first_staffer_name";
        $field[$k]["fieldname"] = "受理人姓名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "staffer_name";
        $field[$k]["fieldname"] = "当前受理人姓名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "confirm_staffer_name";
        $field[$k]["fieldname"] = "结案人姓名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "repairorder_confirmdate";
        $field[$k]["fieldname"] = "结案时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "repairorder_processtime";
        $field[$k]["fieldname"] = "处理时长";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "repairorder_updatetime";
        $field[$k]["fieldname"] = "最后跟踪时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "track_type_name";
        $field[$k]["fieldname"] = "流转状态 ";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "repairorder_status";
        $field[$k]["fieldname"] = "状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "repairorder_isreturnvisit";
        $field[$k]["fieldname"] = "是否回访";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "company_name";
        $field[$k]["fieldname"] = "集团名称";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "school_name";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();

        $result["field"] = $field;
        $result["data"] = $ucsRepairorderModel->result;

        ajax_return(array('error' => $ucsRepairorderModel->error, 'errortip' => $ucsRepairorderModel->errortip, 'result' => $result));
    }

    /**
     * 敏感工单列表
     */
    function sensitiveRepairorderListView()
    {
        $request = Input('GET.','','trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Easx\UcsRepairorderModel($request);
        $Model->issensitiveRepairorderList($request);
        $field = array();

        $k=0;
        $field[$k]["fieldstring"] = "repairorder_pid";
        $field[$k]["fieldname"] = "工单号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "feedbacktype_catgory_name";
        $field[$k]["fieldname"] = "类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "feedbacktype_name";
        $field[$k]["fieldname"] = "分类名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "feedbacktheme_name";
        $field[$k]["fieldname"] = "投诉主题";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "repairorder_from";
        $field[$k]["fieldname"] = "来源";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "company_name";
        $field[$k]["fieldname"] = "集团名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "school_name";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "repairorder_issensitive_name";
        $field[$k]["fieldname"] = "是否敏感工单";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "repairorder_createdate";
        $field[$k]["fieldname"] = "工单录入日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "repairorder_level";
        $field[$k]["fieldname"] = "工单风险级别";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["isLevel"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "repairorder_contactname";
        $field[$k]["fieldname"] = "投诉人称呼";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "repairorder_contactmobile";
        $field[$k]["fieldname"] = "投诉人联系方式";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "first_staffer_name";
        $field[$k]["fieldname"] = "第一受理人";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "staffer_name";
        $field[$k]["fieldname"] = "当前受理人";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "confirm_staffer_name";
        $field[$k]["fieldname"] = "结案人名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "repairorder_confirmdate";
        $field[$k]["fieldname"] = "结案日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "repairorder_processtime";
        $field[$k]["fieldname"] = "处理时长";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "repairorder_updatetime";
        $field[$k]["fieldname"] = "最后跟踪时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "track_type_name";
        $field[$k]["fieldname"] = "流转状态 ";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "repairorder_status_name";
        $field[$k]["fieldname"] = "工单状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["data"] = $Model->result;

        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $result));
    }

    /**
     * 历史工单
     */
    function historyRepairorderListView()
    {
        $request = Input('GET.','','trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Easx\UcsRepairorderModel($request);
        $Model->historyRepairorderList($request);
        $field = array();

        $k=0;
        $field[$k]["fieldstring"] = "feedbacktype_catgory_name";
        $field[$k]["fieldname"] = "投诉类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "feedbacktype_name";
        $field[$k]["fieldname"] = "投诉分类";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "feedbacktheme_name";
        $field[$k]["fieldname"] = "投诉主题";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "repairorder_contactname";
        $field[$k]["fieldname"] = "称呼";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "repairorder_contactmobile";
        $field[$k]["fieldname"] = "联系方式";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "repairorder_pid";
        $field[$k]["fieldname"] = "工单号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "repairorder_level";
        $field[$k]["fieldname"] = "风险级别";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["isLevel"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "school_name";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "repairorder_status_name";
        $field[$k]["fieldname"] = "工单状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        //以下都不显示出来
        $field[$k]["fieldstring"] = "repairorder_from";
        $field[$k]["fieldname"] = "来源";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "company_name";
        $field[$k]["fieldname"] = "集团名称";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "repairorder_issensitive_name";
        $field[$k]["fieldname"] = "是否敏感工单";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "repairorder_createdate";
        $field[$k]["fieldname"] = "工单录入日期";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "first_staffer_name";
        $field[$k]["fieldname"] = "第一受理人";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "staffer_name";
        $field[$k]["fieldname"] = "当前受理人";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "confirm_staffer_name";
        $field[$k]["fieldname"] = "结案人名称";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "repairorder_confirmdate";
        $field[$k]["fieldname"] = "结案日期";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "repairorder_processtime";
        $field[$k]["fieldname"] = "处理时长";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "repairorder_updatetime";
        $field[$k]["fieldname"] = "最后跟踪时间";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "track_type_name";
        $field[$k]["fieldname"] = "流转状态 ";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;



        $result = array();

        $result["field"] = $field;
        $result["data"] = $Model->result;

        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $result));
    }

    /**
     * 创建工单 -zjc
     */
    function addRepairorderAction()
    {
        $request = Input('POST.','','trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Easx\UcsRepairorderModel($request);
        $Model->addRepairorder($request);
        $field = array();
        $field['repairorder_pid'] = "工单编号";
        $field['feedbacktype_catgory'] = "类型";
        $field['feedbacktype_id'] = "分类ID";
        $field['feedbacktheme_id'] = "主题ID";
        $field['repairorder_from'] = "来源";
        $field['company_id'] = "集团ID";
        $field['school_id'] = "学校ID";
        $field['repairorder_createtime'] = "创建时间";
        $field['repairorder_level'] = "风险星级";
        $field['repairorder_contactname'] = "联系人姓名";
        $field['repairorder_contactmobile'] = "联系人手机";
        $field['repairorder_content'] = "投诉内容";
        $field['first_staffer_id'] = "第一受理人";
        $field['staffer_id'] = "当前受理人";
        $field['create_staffer_id'] = "工单创建人";

        $result = array();

        $result["field"] = $field;
        $result["data"] = $Model->result;

        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $result));
    }

    /**
     * 工单详情 -zjc
     */
    function repairorderDetailsView()
    {
        $request = Input('GET.','','trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Easx\UcsRepairorderModel($request);
        $Model->repairorderDetails($request);
        $field = array();
        $field['repairorder_id'] = "工单ID";
        $field['repairorder_pid'] = "工单编号";
        $field['feedbacktype_catgory'] = "类型";
        $field['feedbacktype_id'] = "分类ID";
        $field['feedbacktheme_id'] = "主题ID";
        $field['repairorder_from'] = "来源";
        $field['repairorder_fromnote'] = "来源备注";
        $field['company_id'] = "集团ID";
        $field['school_id'] = "学校ID";
        $field['repairorder_createtime'] = "创建时间";
        $field['repairorder_level'] = "风险星级";
        $field['repairorder_contactname'] = "联系人姓名";
        $field['repairorder_contactmobile'] = "联系人手机";
        $field['repairorder_content'] = "投诉内容";
        $field['first_staffer_id'] = "第一受理人";
        $field['staffer_id'] = "当前受理人";
        $field['create_staffer_id'] = "工单创建人";
        $field['repairorder_status'] = "工单状态0 -2:已删除 0:待受理 1:处理中 2:已处理 3:已结案";
        $field['repairorder_issensitive'] = "是否上升敏感 0:非 1:敏感工单";
        $field['repairorder_updatetime'] = "最后更新时间";
        $field['repairorder_createtime'] = "创建时间";
        $field['repairorder_confirmtime'] = "结案时间";
        $field['trackList'] = "追踪记录";

        $result = array();

        $result["field"] = $field;
        $result["data"] = $Model->result;

        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $result));
    }

    /**
     * 编辑提交工单 -zjc
     */
    function editRepairorderAction()
    {
        $request = Input('POST.','','trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Easx\UcsRepairorderModel($request);
        $dataList = $Model->submitRepairorder($request);
        $field = array();
        $field['feedbacktype_catgory'] = "类型";
        $field['feedbacktype_id'] = "分类ID";
        $field['feedbacktheme_id'] = "主题ID";
        $field['repairorder_from'] = "来源";
        $field['company_id'] = "集团ID";
        $field['school_id'] = "学校ID";
        $field['repairorder_createtime'] = "创建时间";
        $field['repairorder_level'] = "风险星级";
        $field['repairorder_contactname'] = "联系人姓名";
        $field['repairorder_contactmobile'] = "联系人手机";
        $field['first_staffer_id'] = "第一受理人";
        $field['staffer_id'] = "当前受理人";

        $result = array();

        $result["field"] = $field;
        $result["data"] = $Model->result;

        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $result));
    }

    /**
     * 受理订单 -zjc
     */
    function processRepairorderAction()
    {
        $request = Input('POST.','','trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Easx\UcsRepairorderModel($request);
        $Model->processRepairorder($request);
        $field = array();
        $field[''] = "--";

        $result = array();

        $result["field"] = $field;
        $result["data"] = $Model->result;

        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $result));
    }

    /**
     * 敏感 -zjc
     */
    function setIssensitiveAction()
    {
        $request = Input('POST.','','trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Easx\UcsRepairorderModel($request);
        $Model->setIssensitive($request);
        $field = array();
        $field[''] = "--";

        $result = array();

        $result["field"] = $field;
        $result["data"] = $Model->result;

        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $result));
    }

    /**
     * 催单 -zjc
     */
    function setRushAction()
    {
        $request = Input('POST.','','trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Easx\UcsRepairorderModel($request);
        $Model->setRush($request);
        $field = array();
        $field[''] = "--";

        $result = array();

        $result["field"] = $field;
        $result["data"] = $Model->result;

        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $result));
    }

    /**
     * 结案 -zjc
     */
    function closeRepairorderAction()
    {
        $request = Input('POST.','','trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Easx\UcsRepairorderModel($request);
        $Model->closeRepairorder($request);
        $field = array();
        $field[''] = "--";

        $result = array();

        $result["field"] = $field;
        $result["data"] = $Model->result;

        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $result));
    }

    /**
     * 再跟进 -zjc
     */
    function reFollowUpRepairorderAction()
    {
        $request = Input('POST.','','trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Easx\UcsRepairorderModel($request);
        $Model->reFollowUpRepairorder($request);
        $field = array();
        $field[''] = "--";

        $result = array();

        $result["field"] = $field;
        $result["data"] = $Model->result;

        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $result));
    }

    /**
     * 跟进 -zjc
     * @param followup_type 跟进方式 1:投诉跟进 2:投诉已处理 3:内部流转 4:上升至集团 5:下发至校区 6:上升为敏感事件 7:敏感事件处理
     * @param followup_staffer_id 流转跟进人ID
     */
    function followUpRepairorderAction()
    {
        $request = Input('POST.','','trim,addslashes');
        $this->ThisVerify($request);
        switch ($request['followup_type'])
        {
            case 1://投诉跟进
                $ucsRepairorderModel = new \Model\Easx\UcsRepairorderModel($request);
                $ucsRepairorderModel->followUpRepairorder($request);

            break;
            case 2://投诉已处理
                $ucsRepairorderModel = new \Model\Easx\UcsRepairorderModel($request);
                $ucsRepairorderModel ->processCompletedRepairorder($request);

            break;
            case 3://内部流转
                $ucsRepairorderModel = new \Model\Easx\UcsRepairorderModel($request);
                $ucsRepairorderModel ->internalFlowRepairorder($request);

            break;
            case 4://上升至集团
                $ucsRepairorderModel = new \Model\Easx\UcsRepairorderModel($request);
                $ucsRepairorderModel ->upwardFlowRepairorder($request);

            break;
            case 5://下发至校区
                $ucsRepairorderModel = new \Model\Easx\UcsRepairorderModel($request);
                $ucsRepairorderModel ->downFlowRepairorder($request);

            break;
            case 6://上升为敏感事件
                $ucsRepairorderModel = new \Model\Easx\UcsRepairorderModel($request);
                $ucsRepairorderModel ->setIssensitive($request);

            break;
            case 7://敏感事件处理
                $ucsRepairorderModel = new \Model\Easx\UcsRepairorderModel($request);
                $ucsRepairorderModel ->processIssensitiveRepairorder($request);

            break;
            case 8://回访
                $ucsRepairorderModel = new \Model\Easx\UcsRepairorderModel($request);
                $ucsRepairorderModel ->visitRepairorder($request);

            break;

            default:
                $ucsRepairorderModel = new \Model\Easx\UcsCommonModel($request);
                $ucsRepairorderModel->throwException(1,"请选择正确的跟进方式",array());


        }

        $field = array();
        $field[''] = "--";

        $result = array();

        $result["field"] = $field;
        $result["data"] = $ucsRepairorderModel->result;

        ajax_return(array('error' => $ucsRepairorderModel->error, 'errortip' => $ucsRepairorderModel->errortip, 'result' => $result));
    }

    /**
     * 工单删除 -zjc
     */
    function deleteRepairorderAction()
    {
        $request = Input('POST.','','trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Easx\UcsRepairorderModel($request);
        $Model->deleteRepairorder($request);
        $field = array();
        $field[''] = "--";

        $result = array();

        $result["field"] = $field;
        $result["data"] = $Model->result;

        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $result));
    }




}