<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2018/12/29
 * Time: 10:09
 */

namespace Work\Controller\Easxapi;


class LoginAssistantController extends viewTpl{
    public $u;
    public $t;
    public $c;

    function __construct() {
        parent::__construct ();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
    }

    //用户获取token
    function getToken($params=array()){
        $stafferOne = $this->DataControl->getFieldOne('smc_staffer',"staffer_id,staffer_tokencode,staffer_tokenencrypt","staffer_id='{$params['staffer_id']}'");
        if(!$stafferOne)
        {
            return false;
        }
        $md5tokenbar = base64_encode(md5($stafferOne["staffer_tokencode"].date("Y-m-d")));
        if($md5tokenbar == $stafferOne["staffer_tokenencrypt"]){
            $token = $stafferOne["staffer_tokenencrypt"];
        }else{
            //目前这里注释是为了测试方便
            $tokencode = rand(111111,999999);
            $md5tokenbar = base64_encode(md5($tokencode.date("Y-m-d")));
            $this->DataControl->query("UPDATE smc_staffer SET staffer_tokencode = '{$tokencode}',staffer_tokenencrypt = '{$md5tokenbar}' WHERE staffer_id ='{$stafferOne['staffer_id']}'");
            $token = $md5tokenbar;
//            $token = $stafferOne["staffer_tokenencrypt"];
        }
        return $token;
    }
    //用户获取token
    function getCompanyOneApi(){
        $request = Input('get.','','trim,addslashes');
        $companyOne = $this->DataControl->getFieldOne('gmc_company',"company_id,company_code,company_shortname,company_cnname,company_logo","company_code = '{$request['fromcode']}' or company_id = '{$request['fromcode']}'");
        if($companyOne){
            $field = array();
            $field["company_id"] = "序号";
            $field["company_code"] = "企业授权编号";
            $field["company_shortname"] = "企业简称";
            $field["company_cnname"] = "企业中文名称";
            $field["company_logo"] = "企业logo";

            $result = array();
            $result["field"] = $field;
            $result["data"] = $companyOne;
            ajax_return(array('error' => 0, 'errortip' => "企业信息获取成功!", 'result' => $result));
        }else{
            ajax_return(array('error' => 1,'errortip' => "企业账户不存在，请确认授权码是否正确!"));
        }
    }

    //登录验证
    function ThisVerify($request)
    {
        $paramArray = array();
        $paramArray['staffer_id'] = $request['staffer_id'];
        $paramArray['school_id'] = $request['school_id'];
        $paramArray['company_id'] = $request['company_id'];
        $paramArray['token'] = $request['token'];
        if(!$this->UserLimit($paramArray)){
            $result = array();
            $result["list"] = array();
            $result["tokeninc"] = "0";
            $res = array('error' => '1', 'errortip' => "用户token失效", 'result' => $result);
            ajax_return($res);
        }
    }
    //用户的权限
    function stafferPostbeApi(){
        $request = Input('get.','','trim,addslashes');
        if($request['marketer_id']){

        }else{
            $this->ThisVerify($request);//验证账户
        }
        $istaffer = array();
        $stafferOne = $this->DataControl->getFieldOne('smc_staffer',"staffer_id,staffer_leave,staffer_pass,account_class"," staffer_id = '{$request['staffer_id']}' and company_id = '{$request['company_id']}'");
        if($stafferOne['account_class'] == '1'){
            $schoolOne = $this->DataControl->getFieldOne('smc_school',"school_id","company_id = '{$request['company_id']}'","order by school_istemp DESC");
            $istaffer['isCompany'] = '1';
            $istaffer['isSchool'] = '1';
            $istaffer['isCrm'] = '1';
        }else {
            $postroleOne = $this->DataControl->getFieldOne("gmc_staffer_postbe","postrole_id,organize_id,school_id,postbe_iscrmuser","postbe_id = '{$request['postbe_id']}'");

            if($postroleOne['school_id'] == '0'){
                $schoolOne = $this->DataControl->selectOne("SELECT p.school_id FROM gmc_company_organizeschool AS p WHERE p.organize_id = '{$postroleOne['organize_id']}' limit 0,1");
                //集团角色
                $status = $this->DataControl->getFieldOne("gmc_company_postrole","postpart_iscompanyuser,postpart_iscmsuser,postpart_iscrmuser","postrole_id = '{$postroleOne['postrole_id']}'");
                if ($status['postpart_iscompanyuser'] == '1') {
                    $istaffer['isCompany'] = '1';
                } else {
                    $istaffer['isCompany'] = '0';
                }

                //校园角色
                $status = $this->DataControl->getFieldOne("gmc_company_postrole","postpart_iscmsuser","postrole_id = '{$postroleOne['postrole_id']}'");
                if ($status['postpart_iscmsuser'] == '1') {
                    $istaffer['isSchool'] = '1';
                } else {
                    $istaffer['isSchool'] = '0';
                }
                //CRM权限
                $status = $this->DataControl->getFieldOne("gmc_company_postrole","postpart_iscrmuser","postrole_id = '{$postroleOne['postrole_id']}'");
                if ($status['postpart_iscrmuser'] == '1') {
                    $istaffer['isCrm'] = '1';
                } else {
                    $istaffer['isCrm'] = '0';
                }
            }else{
                //集团角色
                $istaffer['isCompany'] = '0';
                //校园角色
                $istaffer['isSchool'] = '1';
                //CRM权限
                if ($postroleOne['postbe_iscrmuser'] == '1') {
                    $istaffer['isCrm'] = '1';
                } else {
                    $istaffer['isCrm'] = '0';
                }
                $schoolOne['school_id'] = $postroleOne['school_id'];
            }

        }

        $school = $this->DataControl->getFieldOne("smc_school","school_id","company_id = '{$request['company_id']}'");
        if(!$school){
            $istaffer['isCrm'] = '0';
            $istaffer['isSchool'] = '0';
        }

        $result = array();
        if ($istaffer) {
            $result["list"] = $istaffer;
            $result["school_id"] = $schoolOne['school_id'];
            $res = array('error' => 0, 'errortip' => '权限获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '权限获取信息', 'result' => $result);
        }
        ajax_return($res);
    }


    //账户密码登录
    function pswdloginApi(){
        $request = Input('post.','','trim,addslashes');
        $companyOne = $this->DataControl->getFieldOne('gmc_company',"company_id,company_cnname","company_code = '{$request['L_code']}'");
        if($companyOne){
            /*$sql="select s.staffer_id,s.staffer_leave,s.staffer_pass,sp.school_id,s.staffer_mobile,s.staffer_tokencode
                                                      from smc_staffer as s
                                                      left join gmc_staffer_postbe as sp on s.staffer_id=sp.staffer_id
                                                      where sp.company_id='{$companyOne['company_id']}'
                                                      and (s.staffer_branch = '{$request['L_name']}' or s.staffer_mobile = '{$request['L_name']}')
                                                      and sp.school_id <>0 and sp.postbe_status=1 order by sp.postbe_createtime DESC limit 0,1 ";
            $stafferOne=$this->DataControl->selectOne($sql);*/
            $stafferOne = $this->DataControl->getFieldOne('smc_staffer',"staffer_id,staffer_leave,staffer_pass,account_class,staffer_branch,staffer_usersig,staffer_cnname,staffer_img,staffer_wxtoken,staffer_wximg","(staffer_branch = '{$request['L_name']}' or staffer_mobile = '{$request['L_name']}') and company_id = '{$companyOne['company_id']}'");
            $ComPost = $this->DataControl->getFieldOne("gmc_staffer_postbe","postbe_id","staffer_id = '{$stafferOne['staffer_id']}' and school_id = '0'");
            $ScPost = $this->DataControl->getFieldOne("gmc_staffer_postbe","postbe_id","staffer_id = '{$stafferOne['staffer_id']}' and school_id <> '0'");
            if($ComPost && $ScPost){
                $status = '0';
            }
            if(!$ComPost && $ScPost){
                $status = '2';
            }
            if($ComPost && !$ScPost){
                $status = '1';
            }

            $isAdmin = $stafferOne['account_class'];
            if($stafferOne){
                if($stafferOne['staffer_leave'] == '0'){
                    $password = md5($request['L_pswd']);
                    if ($password == $stafferOne['staffer_pass'] || $request['L_pswd'] == 'JSPTtoKDDmobile230809') {
                        $istaffer = array();
                        $istaffer['staffer_id'] = $stafferOne['staffer_id'];
                        $istaffer['company_id'] = $companyOne['company_id'];
                        $istaffer['company_cnname'] = $companyOne['company_cnname'];
                        if($stafferOne['account_class'] == '1'){
                            $schoolOne = $this->DataControl->getFieldOne('smc_school',"school_id","company_id = '{$companyOne['company_id']}'","order by school_istemp DESC");
                            $istaffer['school_id'] = $schoolOne['school_id'];
                        }else{
                            $schoolOne = $this->DataControl->selectOne("select sp.school_id,sp.postpart_id,sp.post_id
                                FROM gmc_staffer_postbe as sp 
                                where sp.staffer_id = '{$stafferOne['staffer_id']}' and sp.school_id <> '0' and sp.postbe_status = '1'
                                order by sp.postbe_ismianjob DESC,sp.postbe_ismianjob DESC limit 0,1 ");
                            if($schoolOne && $schoolOne['postpart_id'] == '0'){
                                $postOne = $this->DataControl->getFieldOne('gmc_company_post',"postpart_id","post_id = '{$schoolOne['post_id']}'","order by post_id DESC limit 0,1");
                                if($postOne['postpart_id'] !=='0'){
                                    $this->DataControl->updateData("gmc_staffer_postbe", "staffer_id = '{$stafferOne['staffer_id']}' and school_id = '{$schoolOne['school_id']}' and post_id = '{$schoolOne['post_id']}'", array("postpart_id" => $postOne['postpart_id']));
                                }
                            }

                            $istaffer['school_id'] = $schoolOne['school_id']?$schoolOne['school_id']:0;

                            if($schoolOne['school_id'] =='0'){
                                $schoolOne = $this->DataControl->selectOne("select sp.school_id
                                FROM gmc_staffer_postbe as sp 
                                where sp.staffer_id = '{$stafferOne['staffer_id']}' and sp.school_id <> '0' and sp.postbe_status = '1'
                                order by sp.postbe_ismianjob DESC limit 0,1 ");
                                $istaffer['school_id'] = $schoolOne['school_id']?$schoolOne['school_id']:0;
                            }
                            if($schoolOne['school_id'] ==''){
                                $schoolOne = $this->DataControl->selectOne("select s.school_id FROM gmc_staffer_postbe as sp,gmc_company_organizeschool as s
where s.organize_id = sp.organize_id and sp.staffer_id = '{$stafferOne['staffer_id']}' and sp.school_id = '0' and sp.postbe_status = '1' order by sp.postbe_ismianjob DESC limit 0,1 ");
                                $istaffer['school_id'] = $schoolOne['school_id'];
                            }
                        }
                        $istaffer['token'] = $this->getToken($stafferOne);
                        $istaffer['staffer_wxtoken'] = $stafferOne['staffer_wxtoken'];
                        $istaffer['staffer_wximg'] = $stafferOne['staffer_wximg'];

                        if($stafferOne['staffer_usersig']){
                            $userid = 'T'.$stafferOne['staffer_branch'];
                            $user_sign = $stafferOne['staffer_usersig'];
                            $this->DataControl->updateData("smc_staffer", "staffer_id = '{$stafferOne['staffer_id']}'", array("staffer_lasttime" => time(), "staffer_lastip" => real_ip()));
                        }else{
                            $userid = 'T'.$stafferOne['staffer_branch'];

                            $user_sign = $this->getUserSig($userid);
                            $this->insertAccountOne($userid,$stafferOne['staffer_cnname'],$stafferOne['staffer_img']);

                            $this->DataControl->updateData("smc_staffer", "staffer_id = '{$stafferOne['staffer_id']}'", array("staffer_lasttime" => time(), "staffer_lastip" => real_ip(), "staffer_usersig" => $user_sign));
                        }

                        ajax_return(array('error' => 0, 'errortip' => "登录成功!", 'result' => $istaffer, 'isAdmin' => $isAdmin ,'status' => $status,'usersign' => $user_sign,'userid' => $userid));
                    }else{
                        ajax_return(array('error' => 1,'errortip' => "密码错误!"));
                    }
                }else{
                    ajax_return(array('error' => 1,'errortip' => "您已离职，账户权限已被关闭!"));
                }
            }else{
                ajax_return(array('error' => 1,'errortip' => "职工账户信息不存在!"));
            }
        }else{
            ajax_return(array('error' => 1,'errortip' => "企业账户不存在，请确认集团编号是否正确!"));
        }
    }


    //微信token登录
    function wxloginApi(){
        $request = Input('post.','','trim,addslashes');

        $staffer = $this->DataControl->getFieldOne("smc_staffer","staffer_id,company_id","staffer_wxtoken = '{$request['wxtoken']}'");

        if($staffer){
            $companyOne = $this->DataControl->getFieldOne('gmc_company',"company_id,company_cnname","company_id = '{$staffer['company_id']}'");
            if($companyOne){
                $stafferOne = $this->DataControl->getFieldOne('smc_staffer',"staffer_id,staffer_leave,staffer_pass,account_class,staffer_branch,staffer_usersig,staffer_cnname,staffer_img,staffer_wxtoken,staffer_wximg","staffer_id = '{$staffer['staffer_id']}'");
                $ComPost = $this->DataControl->getFieldOne("gmc_staffer_postbe","postbe_id","staffer_id = '{$stafferOne['staffer_id']}' and school_id = '0'");
                $ScPost = $this->DataControl->getFieldOne("gmc_staffer_postbe","postbe_id","staffer_id = '{$stafferOne['staffer_id']}' and school_id <> '0'");
                if($ComPost && $ScPost){
                    $status = '0';
                }
                if(!$ComPost && $ScPost){
                    $status = '2';
                }
                if($ComPost && !$ScPost){
                    $status = '1';
                }

                $isAdmin = $stafferOne['account_class'];
                if($stafferOne){
                    if($stafferOne['staffer_leave'] == '0'){
                        $istaffer = array();
                        $istaffer['staffer_id'] = $stafferOne['staffer_id'];
                        $istaffer['company_id'] = $companyOne['company_id'];
                        $istaffer['company_cnname'] = $companyOne['company_cnname'];
                        if($stafferOne['account_class'] == '1'){
                            $schoolOne = $this->DataControl->getFieldOne('smc_school',"school_id","company_id = '{$companyOne['company_id']}'","order by school_istemp DESC");
                            $istaffer['school_id'] = $schoolOne['school_id'];
                        }else{
                            $schoolOne = $this->DataControl->selectOne("select sp.school_id,sp.postpart_id,sp.post_id
                                FROM gmc_staffer_postbe as sp 
                                where sp.staffer_id = '{$stafferOne['staffer_id']}' and sp.school_id <> '0' and sp.postbe_status = '1'
                                order by sp.postbe_ismianjob DESC,sp.postbe_ismianjob DESC limit 0,1 ");
                            if($schoolOne && $schoolOne['postpart_id'] == '0'){
                                $postOne = $this->DataControl->getFieldOne('gmc_company_post',"postpart_id","post_id = '{$schoolOne['post_id']}'","order by post_id DESC limit 0,1");
                                if($postOne['postpart_id'] !=='0'){
                                    $this->DataControl->updateData("gmc_staffer_postbe", "staffer_id = '{$stafferOne['staffer_id']}' and school_id = '{$schoolOne['school_id']}' and post_id = '{$schoolOne['post_id']}'", array("postpart_id" => $postOne['postpart_id']));
                                }
                            }

                            $istaffer['school_id'] = $schoolOne['school_id']?$schoolOne['school_id']:0;

                            if($schoolOne['school_id'] =='0'){
                                $schoolOne = $this->DataControl->selectOne("select sp.school_id
                                FROM gmc_staffer_postbe as sp 
                                where sp.staffer_id = '{$stafferOne['staffer_id']}' and sp.school_id <> '0' and sp.postbe_status = '1'
                                order by sp.postbe_ismianjob DESC limit 0,1 ");
                                $istaffer['school_id'] = $schoolOne['school_id']?$schoolOne['school_id']:0;
                            }
                            if($schoolOne['school_id'] ==''){
                                $schoolOne = $this->DataControl->selectOne("select s.school_id FROM gmc_staffer_postbe as sp,gmc_company_organizeschool as s
where s.organize_id = sp.organize_id and sp.staffer_id = '{$stafferOne['staffer_id']}' and sp.school_id = '0' and sp.postbe_status = '1' order by sp.postbe_ismianjob DESC limit 0,1 ");
                                $istaffer['school_id'] = $schoolOne['school_id'];
                            }
                        }
                        $istaffer['token'] = $this->getToken($stafferOne);
                        $istaffer['staffer_wxtoken'] = $stafferOne['staffer_wxtoken'];
                        $istaffer['staffer_wximg'] = $stafferOne['staffer_wximg'];

                        if($stafferOne['staffer_usersig']){
                            $userid = 'T'.$stafferOne['staffer_branch'];
                            $user_sign = $stafferOne['staffer_usersig'];
                            $this->DataControl->updateData("smc_staffer", "staffer_id = '{$stafferOne['staffer_id']}'", array("staffer_lasttime" => time(), "staffer_lastip" => real_ip()));
                        }else{
                            $userid = 'T'.$stafferOne['staffer_branch'];

                            $user_sign = $this->getUserSig($userid);
                            $this->insertAccountOne($userid,$stafferOne['staffer_cnname'],$stafferOne['staffer_img']);

                            $this->DataControl->updateData("smc_staffer", "staffer_id = '{$stafferOne['staffer_id']}'", array("staffer_lasttime" => time(), "staffer_lastip" => real_ip(), "staffer_usersig" => $user_sign));
                        }

                        ajax_return(array('error' => 0, 'errortip' => "登录成功!", 'result' => $istaffer, 'isAdmin' => $isAdmin ,'status' => $status,'usersign' => $user_sign,'userid' => $userid));
                    }else{
                        ajax_return(array('error' => 1,'errortip' => "您已离职，账户权限已被关闭!"));
                    }
                }else{
                    ajax_return(array('error' => 1,'errortip' => "职工账户信息不存在!"));
                }
            }else{
                ajax_return(array('error' => 1,'errortip' => "企业账户不存在，请确认集团编号是否正确!"));
            }
        }else{
            ajax_return(array('error' => 1,'errortip' => "没有此账号！"));
        }


    }

    //针对角色平台做的 -- 第三方直接登录
    function thirdPartyToDddApi(){
        $request = Input('post.','','trim,addslashes');

        $staffer = $this->DataControl->getFieldOne("smc_staffer","staffer_id,company_id","staffer_id = '{$request['staffer_id']}' and staffer_tokenencrypt = '{$request['stafftoken']}' and company_id = '{$request['company_id']}' ");

        if($staffer){
            $companyOne = $this->DataControl->getFieldOne('gmc_company',"company_id,company_cnname","company_id = '{$staffer['company_id']}'");
            if($companyOne){
                $stafferOne = $this->DataControl->getFieldOne('smc_staffer',"staffer_id,staffer_leave,staffer_pass,account_class,staffer_branch,staffer_usersig,staffer_cnname,staffer_img,staffer_wxtoken,staffer_wximg","staffer_id = '{$staffer['staffer_id']}'");
                $ComPost = $this->DataControl->getFieldOne("gmc_staffer_postbe","postbe_id","staffer_id = '{$stafferOne['staffer_id']}' and school_id = '0'");
                $ScPost = $this->DataControl->getFieldOne("gmc_staffer_postbe","postbe_id","staffer_id = '{$stafferOne['staffer_id']}' and school_id <> '0'");
                if($ComPost && $ScPost){
                    $status = '0';
                }
                if(!$ComPost && $ScPost){
                    $status = '2';
                }
                if($ComPost && !$ScPost){
                    $status = '1';
                }

                $isAdmin = $stafferOne['account_class'];
                if($stafferOne){
                    if($stafferOne['staffer_leave'] == '0'){
                        $istaffer = array();
                        $istaffer['staffer_id'] = $stafferOne['staffer_id'];
                        $istaffer['company_id'] = $companyOne['company_id'];
                        $istaffer['company_cnname'] = $companyOne['company_cnname'];
                        if($stafferOne['account_class'] == '1'){
                            $schoolOne = $this->DataControl->getFieldOne('smc_school',"school_id","company_id = '{$companyOne['company_id']}'","order by school_istemp DESC");
                            $istaffer['school_id'] = $schoolOne['school_id'];
                        }else{
                            $schoolOne = $this->DataControl->selectOne("select sp.school_id,sp.postpart_id,sp.post_id
                                FROM gmc_staffer_postbe as sp 
                                where sp.staffer_id = '{$stafferOne['staffer_id']}' and sp.school_id <> '0' and sp.postbe_status = '1'
                                order by sp.postbe_ismianjob DESC,sp.postbe_ismianjob DESC limit 0,1 ");
                            if($schoolOne && $schoolOne['postpart_id'] == '0'){
                                $postOne = $this->DataControl->getFieldOne('gmc_company_post',"postpart_id","post_id = '{$schoolOne['post_id']}'","order by post_id DESC limit 0,1");
                                if($postOne['postpart_id'] !=='0'){
                                    $this->DataControl->updateData("gmc_staffer_postbe", "staffer_id = '{$stafferOne['staffer_id']}' and school_id = '{$schoolOne['school_id']}' and post_id = '{$schoolOne['post_id']}'", array("postpart_id" => $postOne['postpart_id']));
                                }
                            }

                            $istaffer['school_id'] = $schoolOne['school_id']?$schoolOne['school_id']:0;

                            if($schoolOne['school_id'] =='0'){
                                $schoolOne = $this->DataControl->selectOne("select sp.school_id
                                FROM gmc_staffer_postbe as sp 
                                where sp.staffer_id = '{$stafferOne['staffer_id']}' and sp.school_id <> '0' and sp.postbe_status = '1'
                                order by sp.postbe_ismianjob DESC limit 0,1 ");
                                $istaffer['school_id'] = $schoolOne['school_id']?$schoolOne['school_id']:0;
                            }
                            if($schoolOne['school_id'] ==''){
                                $schoolOne = $this->DataControl->selectOne("select s.school_id FROM gmc_staffer_postbe as sp,gmc_company_organizeschool as s
where s.organize_id = sp.organize_id and sp.staffer_id = '{$stafferOne['staffer_id']}' and sp.school_id = '0' and sp.postbe_status = '1' order by sp.postbe_ismianjob DESC limit 0,1 ");
                                $istaffer['school_id'] = $schoolOne['school_id'];
                            }
                        }
                        $istaffer['token'] = $this->getToken($stafferOne);
                        $istaffer['staffer_wxtoken'] = $stafferOne['staffer_wxtoken'];
                        $istaffer['staffer_wximg'] = $stafferOne['staffer_wximg'];

                        if($stafferOne['staffer_usersig']){
                            $userid = 'T'.$stafferOne['staffer_branch'];
                            $user_sign = $stafferOne['staffer_usersig'];
                            $this->DataControl->updateData("smc_staffer", "staffer_id = '{$stafferOne['staffer_id']}'", array("staffer_lasttime" => time(), "staffer_lastip" => real_ip()));
                        }else{
                            $userid = 'T'.$stafferOne['staffer_branch'];

                            $user_sign = $this->getUserSig($userid);
                            $this->insertAccountOne($userid,$stafferOne['staffer_cnname'],$stafferOne['staffer_img']);

                            $this->DataControl->updateData("smc_staffer", "staffer_id = '{$stafferOne['staffer_id']}'", array("staffer_lasttime" => time(), "staffer_lastip" => real_ip(), "staffer_usersig" => $user_sign));
                        }

                        ajax_return(array('error' => 0, 'errortip' => "登录成功!", 'result' => $istaffer, 'isAdmin' => $isAdmin ,'status' => $status,'usersign' => $user_sign,'userid' => $userid));
                    }else{
                        ajax_return(array('error' => 1,'errortip' => "您已离职，账户权限已被关闭!"));
                    }
                }else{
                    ajax_return(array('error' => 1,'errortip' => "职工账户信息不存在!"));
                }
            }else{
                ajax_return(array('error' => 1,'errortip' => "企业账户不存在，请确认集团编号是否正确!"));
            }
        }else{
            ajax_return(array('error' => 1,'errortip' => "没有此账号！"));
        }


    }


    function addGroupMemberView(){
//        var_dump(1);die();
        $this->addGroupMember();
    }


    //手机快速登录
    function mobileloginApi(){
        $request = Input('post.','','trim,addslashes');
        $companyOne = $this->DataControl->getFieldOne('gmc_company',"company_id","company_code = '{$request['L_code']}'");
        if($companyOne){
            $stafferOne = $this->DataControl->getFieldOne('smc_staffer',"staffer_id,staffer_img,staffer_cnname,staffer_leave,staffer_pass,account_class,staffer_usersig,staffer_branch,staffer_wxtoken,staffer_wximg","(staffer_branch = '{$request['L_mobile']}' or staffer_mobile = '{$request['L_mobile']}') and company_id = '{$companyOne['company_id']}'");
            $ComPost = $this->DataControl->getFieldOne("gmc_staffer_postbe","postbe_id","staffer_id = '{$stafferOne['staffer_id']}' and school_id = '0'");
            $ScPost = $this->DataControl->getFieldOne("gmc_staffer_postbe","postbe_id","staffer_id = '{$stafferOne['staffer_id']}' and school_id <> '0'");
            if($ComPost && $ScPost){
                $status = '0';
            }
            if(!$ComPost && $ScPost){
                $status = '2';
            }
            if($ComPost && !$ScPost){
                $status = '1';
            }

            $isAdmin = $stafferOne['account_class'];
            if($stafferOne){
                if($stafferOne['staffer_leave'] == '0') {
                    $mobile = trim($request['L_mobile']);
                    $verifycode = trim($request['L_verifycode']);
                    $sendrz = $this->DataControl->getFieldOne('gmc_mislog', "mislog_sendcode", "mislog_mobile='{$mobile}' and mislog_tilte = '快速登录'", "order by mislog_time DESC");
                    if (!$sendrz || $sendrz['mislog_sendcode'] !== $verifycode) {
//                if(!isset($_COOKIE['mislog_sendcode']) || $_COOKIE['mislog_sendcode'] !== $verifycode){
                        $res = array('error' => '1', 'errortip' => '短信验证码错误!');
                        ajax_return($res);
                    } else {
                        $istaffer = array();
                        $istaffer['staffer_id'] = $stafferOne['staffer_id'];
                        $istaffer['company_id'] = $companyOne['company_id'];
                        $istaffer_info['isAdmin'] =$isAdmin;
                        $istaffer_info['status'] =$status;

                        if ($stafferOne['account_class'] == '1') {
                            $schoolOne = $this->DataControl->getFieldOne('smc_school', "school_id", "company_id = '{$companyOne['company_id']}'", "order by school_istemp DESC");
                            $istaffer['school_id'] = $schoolOne['school_id'];
                        } else {
                            $schoolOne = $this->DataControl->selectOne("select sp.school_id,sp.postpart_id,sp.post_id
                                FROM gmc_staffer_postbe as sp 
                                where sp.staffer_id = '{$stafferOne['staffer_id']}' and sp.school_id <> '0' and sp.postbe_status = '1'
                                order by sp.postbe_ismianjob DESC,sp.postbe_ismianjob DESC limit 0,1 ");
                            if ($schoolOne && $schoolOne['postpart_id'] == '0') {
                                $postOne = $this->DataControl->getFieldOne('gmc_company_post', "postpart_id", "post_id = '{$schoolOne['post_id']}'", "order by post_id DESC limit 0,1");
                                if ($postOne['postpart_id'] !== '0') {
                                    $this->DataControl->updateData("gmc_staffer_postbe", "staffer_id = '{$stafferOne['staffer_id']}' and school_id = '{$schoolOne['school_id']}' and post_id = '{$schoolOne['post_id']}'", array("postpart_id" => $postOne['postpart_id']));
                                }
                            }

                            $istaffer['school_id'] = $schoolOne['school_id'] ? $schoolOne['school_id'] : 0;

                            if ($schoolOne['school_id'] == '0') {
                                $schoolOne = $this->DataControl->selectOne("select sp.school_id
                                FROM gmc_staffer_postbe as sp 
                                where sp.staffer_id = '{$stafferOne['staffer_id']}' and sp.school_id <> '0' and sp.postbe_status = '1'
                                order by sp.postbe_ismianjob DESC limit 0,1 ");
                                $istaffer['school_id'] = $schoolOne['school_id'] ? $schoolOne['school_id'] : 0;
                            }
                            if ($schoolOne['school_id'] == '') {
                                $schoolOne = $this->DataControl->selectOne("select s.school_id FROM gmc_staffer_postbe as sp,gmc_company_organizeschool as s
where s.organize_id = sp.organize_id and sp.staffer_id = '{$stafferOne['staffer_id']}' and sp.school_id = '0' and sp.postbe_status = '1' order by sp.postbe_ismianjob DESC limit 0,1 ");
                                $istaffer['school_id'] = $schoolOne['school_id'];
                            }

                        }
                        $istaffer['token'] = $this->getToken($stafferOne);
                        $istaffer_info['isAdmin'] =$isAdmin;
                        $istaffer_info['status'] =$status;

                        $istaffer['staffer_wxtoken'] = $stafferOne['staffer_wxtoken'];
                        $istaffer['staffer_wximg'] = $stafferOne['staffer_wximg'];

                        if($stafferOne['staffer_usersig']){
                            $userid = 'T'.$stafferOne['staffer_branch'];
                            $user_sign = $stafferOne['staffer_usersig'];
                            $this->DataControl->updateData("smc_staffer", "staffer_id = '{$stafferOne['staffer_id']}'", array("staffer_lasttime" => time(), "staffer_lastip" => real_ip()));
                        }else{
                            $userid = 'T'.$stafferOne['staffer_branch'];
                            $user_sign = $this->getUserSig($userid);
                            $this->insertAccountOne($userid,$stafferOne['staffer_cnname'],$stafferOne['staffer_img']);
                            $this->DataControl->updateData("smc_staffer", "staffer_id = '{$stafferOne['staffer_id']}'", array("staffer_lasttime" => time(), "staffer_lastip" => real_ip(), "staffer_usersig" => $user_sign));
                        }
                        ajax_return(array('error' => 0, 'errortip' => "登录成功!", 'result' => $istaffer, 'isAdmin' => $isAdmin ,'status' => $status, 'usersign' => $user_sign, 'userid' => $userid));
                    }
                }else{
                    ajax_return(array('error' => 1,'errortip' => "您已离职，账户权限已被关闭!"));
                }
            }else{
                ajax_return(array('error' => 1,'errortip' => "职工账户信息不存在!"));
            }
        }else{
            ajax_return(array('error' => 1,'errortip' => "企业账户不存在，请确认集团编号是否正确!"));
        }
    }

    //获取手机验证码
    function getverifycodeApi()
    {
        $request = Input('get.','','trim,addslashes');
        $companyOne = $this->DataControl->getFieldOne('gmc_company', "company_id", "company_code = '{$request['L_code']}'");
        if ($companyOne) {
            $stafferOne = $this->DataControl->getFieldOne('smc_staffer',"staffer_id,staffer_leave,staffer_pass","(staffer_branch = '{$request['L_mobile']}' or staffer_mobile = '{$request['L_mobile']}') and company_id = '{$companyOne['company_id']}'");
            if(!$stafferOne){
                $res = array('error' => '1', 'errortip' => '职工账户信息不存在!');
                ajax_return($res);
            }else{
                $mobile = trim($request['L_mobile']);
                //一小时内发送次数
                $mintime = time()-3600;
                $mislognum = $this->DataControl->selectOne("select count(mislog_id) as mislognum from gmc_mislog where mislog_mobile='{$mobile}' and mislog_tilte = '快速登录' and mislog_time >= '{$mintime}' limit 0,1 ");
                if($mislognum['mislognum'] > 5){
                    $res = array('error' => '1', 'errortip' => '您的手机已超出发送验证码次数，请联系客服！');
                    ajax_return($res);
                }
                //最近一次发送时间
                $sendmisrz = $this->DataControl->getFieldOne('gmc_mislog',"mislog_time","mislog_mobile='{$mobile}' and mislog_tilte = '快速登录'","order by mislog_time DESC");
                if($sendmisrz && ( time() - $sendmisrz['mislog_time']) < 60){
                    $res = array('error' => '1', 'errortip' => '验证码已发送！');
                    ajax_return($res);
                }else{
                    $tilte = "快速登录";
                    $sendcode = rand(111111,999999);
                    setcookie('mislog_sendcode', $sendcode, time()+1800);
                    $contxt = "用户您好！您的手机验证码为{$sendcode}，请完成验证，如非本人操作，请忽略本短信。";
                    //短信发送
                    if($this->Sendmisgocom($mobile,$contxt,$tilte,$sendcode,$companyOne['company_id'])){
                        $res = array('error' => '0', 'errortip' => '发送成功',"bakfuntion"=>"okmotify");
                        ajax_return($res);
                    }else{
                        $res = array('error' => '1', 'errortip' => '发送失败!',"bakfuntion"=>"errormotify");
                        ajax_return($res);
                    }
                }
            }
        }else{
            ajax_return(array('error' => 1,'errortip' => "企业账户不存在，请确认集团编号是否正确!"));
        }
    }

    //忘记密码获取手机验证码
    function getForgetcodeApi()
    {
        $request = Input('get.','','trim,addslashes');
        $companyOne = $this->DataControl->getFieldOne('gmc_company', "company_id", "company_code = '{$request['L_code']}'");
        if ($companyOne) {
            $stafferOne = $this->DataControl->getFieldOne('smc_staffer',"staffer_id,staffer_leave,staffer_pass","(staffer_branch = '{$request['L_mobile']}' or staffer_mobile = '{$request['L_mobile']}') and company_id = '{$companyOne['company_id']}'");
            if(!$stafferOne){
                $res = array('error' => '1', 'errortip' => '职工账户信息不存在!');
                ajax_return($res);
            }else{
                $mobile = trim($request['L_mobile']);
                //一小时内发送次数
                $mintime = time()-3600;
                $mislognum = $this->DataControl->selectOne("select count(mislog_id) as mislognum from gmc_mislog where mislog_mobile='{$mobile}' and mislog_tilte = '叮铛助教忘记密码' and mislog_time >= '{$mintime}' limit 0,1 ");
                if($mislognum['mislognum'] > 5){
                    $res = array('error' => '1', 'errortip' => '您的手机已超出发送验证码次数，请联系客服！');
                    ajax_return($res);
                }
                //最近一次发送时间
                $sendmisrz = $this->DataControl->getFieldOne('gmc_mislog',"mislog_time","mislog_mobile='{$mobile}' and mislog_tilte = '叮铛助教忘记密码'","order by mislog_time DESC");
                if($sendmisrz && ( time() - $sendmisrz['mislog_time']) < 60){
                    $res = array('error' => '1', 'errortip' => '验证码已发送！');
                    ajax_return($res);
                }else{
                    $tilte = "叮铛助教忘记密码";
                    $sendcode = rand(111111,999999);
                    setcookie('mislog_sendcode', $sendcode, time()+1800);
                    $contxt = "用户您好！您的手机验证码为{$sendcode}，请完成验证，如非本人操作，请忽略本短信。";
                    //短信发送
                    if($this->Sendmisgocom($mobile,$contxt,$tilte,$sendcode,$companyOne['company_id'])){
                        $res = array('error' => '0', 'errortip' => '发送成功',"bakfuntion"=>"okmotify");
                        ajax_return($res);
                    }else{
                        $res = array('error' => '1', 'errortip' => '发送失败!',"bakfuntion"=>"errormotify");
                        ajax_return($res);
                    }
                }
            }
        }else{
            ajax_return(array('error' => 1,'errortip' => "企业账户不存在，请确认集团编号是否正确!"));
        }
    }

    //忘记密码
    function ForgetPassApi(){
        $request = Input('post.','','trim,addslashes');
        $companyOne = $this->DataControl->getFieldOne('gmc_company',"company_id","company_code = '{$request['L_code']}'");
        if($request['passone'] !== $request['passtwo']){
            $res = array('error' => '1', 'errortip' => '两次输入的密码不一致!');
            ajax_return($res);
        }
        if($companyOne){
            $stafferOne = $this->DataControl->getFieldOne('smc_staffer',"staffer_id,staffer_img,staffer_cnname,staffer_leave,staffer_pass,account_class,staffer_usersig,staffer_branch,staffer_wxtoken,staffer_wximg","(staffer_branch = '{$request['L_mobile']}' or staffer_mobile = '{$request['L_mobile']}') and company_id = '{$companyOne['company_id']}'");
            $ComPost = $this->DataControl->getFieldOne("gmc_staffer_postbe","postbe_id","staffer_id = '{$stafferOne['staffer_id']}' and school_id = '0'");
            $ScPost = $this->DataControl->getFieldOne("gmc_staffer_postbe","postbe_id","staffer_id = '{$stafferOne['staffer_id']}' and school_id <> '0'");
            if($ComPost && $ScPost){
                $status = '0';
            }
            if(!$ComPost && $ScPost){
                $status = '2';
            }
            if($ComPost && !$ScPost){
                $status = '1';
            }

            $isAdmin = $stafferOne['account_class'];
            if($stafferOne){
                if($stafferOne['staffer_leave'] == '0') {
                    $mobile = trim($request['L_mobile']);
                    $verifycode = trim($request['L_verifycode']);
                    $sendrz = $this->DataControl->getFieldOne('gmc_mislog', "mislog_sendcode", "mislog_mobile='{$mobile}' and mislog_tilte = '叮铛助教忘记密码'", "order by mislog_time DESC");
                    if (!$sendrz || $sendrz['mislog_sendcode'] !== $verifycode) {
//                if(!isset($_COOKIE['mislog_sendcode']) || $_COOKIE['mislog_sendcode'] !== $verifycode){
                        $res = array('error' => '1', 'errortip' => '短信验证码错误!');
                        ajax_return($res);
                    } else {
                        $istaffer = array();
                        $istaffer['staffer_id'] = $stafferOne['staffer_id'];
                        $istaffer['company_id'] = $companyOne['company_id'];
                        $istaffer_info['isAdmin'] =$isAdmin;
                        $istaffer_info['status'] =$status;

                        if ($stafferOne['account_class'] == '1') {
                            $schoolOne = $this->DataControl->getFieldOne('smc_school', "school_id", "company_id = '{$companyOne['company_id']}'", "order by school_istemp DESC");
                            $istaffer['school_id'] = $schoolOne['school_id'];
                        } else {
                            $schoolOne = $this->DataControl->selectOne("select sp.school_id,sp.postpart_id,sp.post_id
                                FROM gmc_staffer_postbe as sp 
                                where sp.staffer_id = '{$stafferOne['staffer_id']}' and sp.school_id <> '0' and sp.postbe_status = '1'
                                order by sp.postbe_ismianjob DESC,sp.postbe_ismianjob DESC limit 0,1 ");
                            if ($schoolOne && $schoolOne['postpart_id'] == '0') {
                                $postOne = $this->DataControl->getFieldOne('gmc_company_post', "postpart_id", "post_id = '{$schoolOne['post_id']}'", "order by post_id DESC limit 0,1");
                                if ($postOne['postpart_id'] !== '0') {
                                    $this->DataControl->updateData("gmc_staffer_postbe", "staffer_id = '{$stafferOne['staffer_id']}' and school_id = '{$schoolOne['school_id']}' and post_id = '{$schoolOne['post_id']}'", array("postpart_id" => $postOne['postpart_id']));
                                }
                            }

                            $istaffer['school_id'] = $schoolOne['school_id'] ? $schoolOne['school_id'] : 0;

                            if ($schoolOne['school_id'] == '0') {
                                $schoolOne = $this->DataControl->selectOne("select sp.school_id
                                FROM gmc_staffer_postbe as sp 
                                where sp.staffer_id = '{$stafferOne['staffer_id']}' and sp.school_id <> '0' and sp.postbe_status = '1'
                                order by sp.postbe_ismianjob DESC limit 0,1 ");
                                $istaffer['school_id'] = $schoolOne['school_id'] ? $schoolOne['school_id'] : 0;
                            }
                            if ($schoolOne['school_id'] == '') {
                                $schoolOne = $this->DataControl->selectOne("select s.school_id FROM gmc_staffer_postbe as sp,gmc_company_organizeschool as s
where s.organize_id = sp.organize_id and sp.staffer_id = '{$stafferOne['staffer_id']}' and sp.school_id = '0' and sp.postbe_status = '1' order by sp.postbe_ismianjob DESC limit 0,1 ");
                                $istaffer['school_id'] = $schoolOne['school_id'];
                            }

                        }
                        $istaffer['token'] = $this->getToken($stafferOne);
                        $istaffer_info['isAdmin'] =$isAdmin;
                        $istaffer_info['status'] =$status;

                        $istaffer['staffer_wxtoken'] = $stafferOne['staffer_wxtoken'];
                        $istaffer['staffer_wximg'] = $stafferOne['staffer_wximg'];

                        if($stafferOne['staffer_usersig']){
                            $userid = 'T'.$stafferOne['staffer_branch'];
                            $user_sign = $stafferOne['staffer_usersig'];
                            $data = array();
                            $data['staffer_lasttime'] = time();
                            $data['staffer_lastip'] = real_ip();
                            $data['staffer_pass'] = md5($request['passone']);
                            $data['staffer_bakpass'] = $request['passone'];
                            $data['staffer_usersig'] = $user_sign;
                            $data['staffer_updatetime'] = time();
                            $this->DataControl->updateData("smc_staffer", "staffer_id = '{$stafferOne['staffer_id']}'",$data);
                        }else{
                            $userid = 'T'.$stafferOne['staffer_branch'];
                            $user_sign = $this->getUserSig($userid);
                            $data = array();
                            $data['staffer_lasttime'] = time();
                            $data['staffer_lastip'] = real_ip();
                            $data['staffer_pass'] = md5($request['passone']);
                            $data['staffer_bakpass'] = $request['passone'];
                            $data['staffer_usersig'] = $user_sign;
                            $data['staffer_updatetime'] = time();
                            $this->insertAccountOne($userid,$stafferOne['staffer_cnname'],$stafferOne['staffer_img']);
                            $this->DataControl->updateData("smc_staffer", "staffer_id = '{$stafferOne['staffer_id']}'", $data);
                        }
                        ajax_return(array('error' => 0, 'errortip' => "登录成功!", 'result' => $istaffer, 'isAdmin' => $isAdmin ,'status' => $status, 'usersign' => $user_sign, 'userid' => $userid));
                    }
                }else{
                    ajax_return(array('error' => 1,'errortip' => "您已离职，账户权限已被关闭!"));
                }
            }else{
                ajax_return(array('error' => 1,'errortip' => "职工账户信息不存在!"));
            }
        }else{
            ajax_return(array('error' => 1,'errortip' => "企业账户不存在，请确认集团编号是否正确!"));
        }
    }

    function getComPostApi(){
        $request = Input('get.','','trim,addslashes');
        $ComPost = $this->DataControl->selectClear("SELECT
                p.postbe_id,
                o.organize_cnname as cnname,
                s.post_name,
                (select school_id from gmc_company_organizeschool as a WHERE a.organize_id = o.organize_id limit 0,1) as school_id
            FROM
                gmc_staffer_postbe AS p
                LEFT JOIN gmc_company_organize AS o ON p.organize_id = o.organize_id
                LEFT JOIN gmc_company_post AS s ON p.post_id = s.post_id 
            WHERE
                p.staffer_id = '{$request['staffer_id']}' and p.school_id = 0");
        if($ComPost){
            $info = $this->DataControl->getFieldOne("smc_staffer","staffer_sex,staffer_mobile,staffer_enname,staffer_branch,staffer_img,staffer_cnname","staffer_id = '{$request['staffer_id']}'");
            $result = array();
            $result["data"] = $ComPost;
            $result["info"] = $info;
            $res = array('error' => '0', 'errortip' => '获取集团职务成功','result' => $result);
            ajax_return($res);
        }else{
            $res = array('error' => '1', 'errortip' => '暂无集团职务!','result' => array());
            ajax_return($res);
        }
    }

    function getScPostApi(){
        $request = Input('get.','','trim,addslashes');
        $ComPost = $this->DataControl->selectClear("SELECT
                p.postbe_id,
                c.school_cnname as cnname,
                c.school_address,
                c.school_id,
                s.post_name 
            FROM
                gmc_staffer_postbe AS p
                left join smc_school as c on p.school_id = c.school_id
                LEFT JOIN gmc_company_post AS s ON p.post_id = s.post_id 
            WHERE
                p.staffer_id = '{$request['staffer_id']}' and p.school_id > 0");
        if($ComPost){
            $info = $this->DataControl->getFieldOne("smc_staffer","staffer_sex,staffer_mobile,staffer_enname,staffer_branch,staffer_img,staffer_cnname","staffer_id = '{$request['staffer_id']}'");

            $result = array();
            $result["data"] = $ComPost;
            $result["info"] = $info;
            $res = array('error' => '0', 'errortip' => '获取校园职务成功','result' => $result);
            ajax_return($res);
        }else{
            $res = array('error' => '1', 'errortip' => '暂无校园职务!','result' => array());
            ajax_return($res);
        }
    }

    //切换职务
    function ChangePostApi()
    {
        $request = Input('get.','','trim,addslashes');
        $stafferOne = $this->DataControl->getFieldOne('smc_staffer', "staffer_id,staffer_leave,staffer_pass,account_class", "staffer_id = '{$request['staffer_id']}'");

        $ComPost = $this->DataControl->getFieldOne("gmc_staffer_postbe", "postbe_id", "staffer_id = '{$stafferOne['staffer_id']}' and school_id = '0'");
        $ScPost = $this->DataControl->getFieldOne("gmc_staffer_postbe", "postbe_id", "staffer_id = '{$stafferOne['staffer_id']}' and school_id > '0'");

        if ($ComPost != '' && $ScPost != '') {
            $status = '0';
        }
        if ($ComPost == '' && $ScPost != '') {
            $status = '2';
        }
        if ($ComPost != '' && $ScPost == '') {
            $status = '1';
        }

        $isAdmin = $stafferOne['account_class'];
        ajax_return(array('error' => 0, 'errortip' => "切换成功!", 'isAdmin' => $isAdmin ,'status' => $status));


    }

    function ComTokenApi(){
        $request = Input('get.','','trim,addslashes');
        $staffer_id = $this->DataControl->getFieldOne("crm_marketer","staffer_id","marketer_id = '{$request['marketer_id']}'");
        $token = $this->DataControl->getFieldOne("smc_staffer","staffer_tokenencrypt","staffer_id = '{$staffer_id['staffer_id']}'");
        ajax_return(array('error' => 0, 'errortip' => "获取成功!",'token' => $token['staffer_tokenencrypt']));

    }

    //获取登录个人信息
    function getStafferInfoApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $stafferOne = $this->DataControl->selectOne("select s.staffer_id,s.account_class,s.staffer_branch,s.staffer_mobile,s.staffer_sex,s.staffer_img,s.staffer_cnname,s.staffer_enname,s.company_id,c.company_isaddstudent,c.company_logo
FROM smc_staffer as s,gmc_company as c where s.company_id = c.company_id and s.staffer_id = '{$request['staffer_id']}'");

        $schoolOne = $this->DataControl->selectOne("select s.school_id,s.school_cnname,s.school_groupid,s.school_shortname FROM smc_school as s
WHERE s.school_id = '{$request['school_id']}'  limit 0,1");
        if($schoolOne){
            $isphone = $this->DataControl->selectOne(" SELECT p.postpart_istel as post_istel,p.postpart_teltype as post_teltype FROM smc_school_postpart as p ,gmc_staffer_postbe AS b
WHERE p.postpart_id = b.postpart_id AND b.staffer_id = '{$request['staffer_id']}' and b.school_id = '{$request['school_id']}' ORDER BY p.postpart_istel DESC,p.postpart_teltype DESC limit 0,1");

            if($schoolOne['school_groupid'] == ''){
                $groupname = $schoolOne['school_cnname'].'群聊';
                $groupid = $this->createGroup($groupname);
                $data = array();
                $data['school_groupid'] = $groupid['GroupId'];
                $this->DataControl->updateData("smc_school","school_id = '{$schoolOne['school_id']}'",$data);
            }

            $result = array();
            $result['staffer_id'] = $stafferOne['staffer_id'];
            $result['company_id'] = $stafferOne['company_id'];
            $result['school_id'] = $schoolOne['school_id'];
            $result['school_cnname'] = $schoolOne['school_cnname'];
            $result['company_isaddstudent'] = $stafferOne['company_isaddstudent'];

            if($stafferOne['account_class'] == '1'){
                $icrmOne = $this->DataControl->selectOne("SELECT '管理员' AS post_name,'1' AS postbe_iscrmuser,'1' AS postbe_crmuserlevel, m.marketer_id
FROM crm_marketer AS m WHERE m.staffer_id = '{$request['staffer_id']}' LIMIT 0, 1");
            }else{
                $icrmOne = $this->DataControl->selectOne("SELECT
	p.*, m.marketer_id
FROM
	(
		(
			SELECT
				s.post_name,
				b.postbe_iscrmuser,
				b.postbe_crmuserlevel
			FROM
				gmc_staffer_postbe AS b,
				gmc_company_post AS s
			WHERE b.post_id = s.post_id AND b.staffer_id = '{$request['staffer_id']}' AND b.school_id = '{$request['school_id']}'
		)
		UNION ALL
			(
				SELECT
					s.post_name,
					r.postpart_iscrmuser AS postbe_iscrmuser,
					r.postpart_iscrmuser AS postbe_crmuserlevel
				FROM
					gmc_staffer_postbe AS b,
					gmc_company_post AS s,
					gmc_company_postrole AS r,
					gmc_company_organizeschool AS o
				WHERE b.post_id = s.post_id AND b.organize_id = o.organize_id AND b.postrole_id = r.postrole_id
				AND b.staffer_id = '{$request['staffer_id']}' AND b.school_id = '0' AND o.school_id = '{$request['school_id']}'
			)
	) AS p LEFT JOIN crm_marketer AS m ON m.staffer_id = '{$request['staffer_id']}'
ORDER BY p.postbe_iscrmuser DESC, p.postbe_crmuserlevel DESC LIMIT 0, 1");
            }



            $stafferDetail  = array();
            $stafferDetail['account_class'] = $stafferOne['account_class'];
            $stafferDetail['marketer_id'] = $icrmOne['marketer_id'];
            $stafferDetail['is_crm'] = $icrmOne['postbe_iscrmuser'];
            $stafferDetail['post_name'] = $icrmOne['post_name'];
            $stafferDetail['postbe_crmuserlevel'] = $icrmOne['postbe_crmuserlevel'];
            //$stafferDetail['staffer_birthday'] = $stafferOne['staffer_birthday'];
            $stafferDetail['staffer_branch'] = $stafferOne['staffer_branch'];
            $stafferDetail['staffer_cnname'] = $stafferOne['staffer_cnname'];
            $stafferDetail['staffer_enname'] = $stafferOne['staffer_enname'];
            $stafferDetail['staffer_img'] = $stafferOne['staffer_img'];
            $stafferDetail['staffer_mobile'] = $stafferOne['staffer_mobile'];
            $stafferDetail['staffer_sex'] = $stafferOne['staffer_sex'];
            $result['stafferInfo'][] = $stafferDetail;

            $result['crm'] = $icrmOne['postbe_iscrmuser'];
            $result['postbe_crmuserlevel'] = $icrmOne['postbe_crmuserlevel'];
            $result['marketer_id'] = $icrmOne['marketer_id'];

            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result, 'img' => $stafferOne['company_logo'], 'isphone' => $isphone);
        }else{
            $result = array();
            $res = array('error' => '1', 'errortip' => "学校信息未获取成功", 'result' => $result);
        }
        ajax_return($res);
    }

    function createUersigView(){
        $request = Input('get.','','trim,addslashes');
        $user_sign = $this->getUserSig($request['userid']);
        $res = array('error' => '0', 'errortip' => "获取成功", 'usersign' => $user_sign);
        ajax_return($res);

    }

    //首页 -- 筛选学校 , '', 'strip_tags,trim'
    function getSchoolApi()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);
        //验证账户

        $datawhere ="s.company_id = '{$request['company_id']}' AND s.school_city = r.region_id AND s.company_id = m.company_id AND s.school_isclose = '0' AND s.school_istest = '0'";
        //关键词
        if(isset($request['keyword']) && $request['keyword'] != ''){
            $datawhere .= " and (s.school_branch like '%{$request['keyword']}%' or s.school_shortname like '%{$request['keyword']}%' or s.school_cnname like '%{$request['keyword']}%' or s.school_enname like '%{$request['keyword']}%' or m.company_cnname  like '%{$request['keyword']}%')";
        }
        //区域
        if(isset($request['district_id']) && $request['district_id'] != ''){
            $datawhere .= " and s.district_id = '{$request['district_id']}'";
        }
        //省份
        if(isset($request['school_province']) && $request['school_province'] != ''){
            $datawhere .= " and s.school_province = '{$request['school_province']}'";
        }
        //城市
        if(isset($request['school_city']) && $request['school_city'] != ''){
            $datawhere .= " and s.school_city = '{$request['school_city']}'";
        }
        //城市
        if(isset($request['school_cnname_initial']) && $request['school_cnname_initial'] != ''){
            $datawhere .= " and s.school_cnname_initial = '{$request['school_cnname_initial']}'";
        }
        // p.staffer_id = '{$request['staffer_id']}'
        //组织
        if(isset($request['organize_id']) && $request['organize_id'] != ''){
            $datawhere .= " and s.school_id in ( SELECT o.school_id FROM gmc_company_organizeschool as o where o.organize_id = '{$request['organize_id']}')";
        }

        //城市名称 -- 定位
        if(isset($request['city_name']) && $request['city_name'] != ''){
            $datawhere .= " and r.region_name like '%{$request['city_name']}%'";
        }

        if(isset($request['p']) && $request['p'] !== ''){
            $page = $request['p'];
        }else{
            $page = '1';
        }
        if(isset($request['num']) && $request['num'] !== ''){
            $num = $request['num'];
        }else{
            $num = '10';
        }
        $pagestart = ($page-1)*$num;

        $stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id,account_class,company_id", "staffer_id = '{$request['staffer_id']}'");
        if($stafferOne['account_class'] == '0'){
            $datawhere .= " AND (s.school_id IN ( SELECT b.school_id FROM gmc_staffer_postbe AS b WHERE b.staffer_id = '{$request['staffer_id']}' AND b.school_id <> '0' )
	OR s.school_id IN ( SELECT o.school_id FROM gmc_staffer_postbe AS b, gmc_company_postrole AS r, gmc_company_organizeschool AS o
		WHERE b.organize_id = o.organize_id AND b.postrole_id = r.postrole_id AND b.staffer_id = '{$request['staffer_id']}' AND b.school_id = '0' AND (r.postpart_iscmsuser = '1' or r.postpart_iscrmuser = '1') ))";
        }
        $sqlfields = "s.school_id,s.company_id,s.district_id,s.school_branch,s.school_shortname,s.school_cnname,s.school_enname,s.school_address,s.school_phone,r.region_name,m.company_cnname";
        $dataList = $this->DataControl->selectClear("SELECT {$sqlfields} FROM smc_school AS s,smc_code_region AS r,gmc_company as m
        WHERE {$datawhere} LIMIT {$pagestart},{$num}");

        $dataarray = array();
        if($dataList) {
            $schoolArray = array();
            foreach ($dataList as $key => $info) {
                $schoolArray[] = $info['school_id'];
                $dataarray[$info['company_id']][] = $info;
            }
            $schoolstring = implode(",",$schoolArray);
        }

        $dataList['datalist'] = $dataarray;
        $dataList['allid'] = $schoolstring;

        $field = array();
        $field["school_id"] = "序号";
        $field["company_id"] = "所属公司";
        $field["district_id"] = "所属集团区域ID";
        $field["school_branch"] = "校区编号";
        $field["school_shortname"] = "校园简称";
        $field["school_cnname"] = "校园名称称";
        $field["school_enname"] = "检索代码称";
        $field["company_cnname"] = "机构名称";
        $field["school_address"] = "学校地址";
        $field["school_phone"] = "学校联系电话";

        $result = array();
        if($dataList){
            $result["field"] = $field;
            $result["data"] = $dataList['datalist'];
            $res = array('error' => '0', 'errortip' => '筛选学校成功', 'initial' =>  $dataList['initial'],'allid' =>  $dataList['allid'], 'result' => $result);
        }else{
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '筛选学校失败', 'result' => $result);
        }
        ajax_return($res);
    }



    //首页 -- 筛选学校 , '', 'strip_tags,trim'
    function getSchoolCityApi()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);
        //验证账户

        $datawhere ="s.company_id = '{$request['company_id']}' AND s.school_isclose = '0' AND s.school_istest = '0'
        AND s.school_city = r.region_id AND s.school_province = v.region_id AND r.region_id > 0 AND s.school_area = a.region_id ";

        $stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id,account_class,company_id", "staffer_id = '{$request['staffer_id']}'");
        if($stafferOne['account_class'] == '0'){
            $datawhere .= " AND (s.school_id IN ( SELECT b.school_id FROM gmc_staffer_postbe AS b WHERE b.staffer_id = '{$request['staffer_id']}' AND b.school_id <> '0' )
	OR s.school_id IN ( SELECT o.school_id FROM gmc_staffer_postbe AS b, gmc_company_postrole AS r, gmc_company_organizeschool AS o
		WHERE b.organize_id = o.organize_id AND b.postrole_id = r.postrole_id AND b.staffer_id = '{$request['staffer_id']}' AND b.school_id = '0' AND r.postpart_iscmsuser = '1' ))";
        }

        $sqlfields = " r.region_id,r.region_name,v.region_id as region_ids,v.region_name as region_names,a.region_id as region_idc,a.region_name as region_namec ";
        $dataList = $this->DataControl->selectClear("SELECT {$sqlfields} FROM smc_school AS s ,smc_code_region as r, smc_code_region as v, smc_code_region as a
WHERE {$datawhere} GROUP BY r.region_id ");

        $dataList['datalist'] = $dataList;

        $field = array();
        $field["region_id"] = "序号";
        $field["region_name"] = "城市名称";
        $field["region_ids"] = "序号省";
        $field["region_names"] = "城市名称省";
        $field["region_idc"] = "序号区";
        $field["region_namec"] = "城市名称区";

        $result = array();
        if($dataList){
            $result["field"] = $field;
            $result["data"] = $dataList['datalist'];
            $res = array('error' => '0', 'errortip' => '获取城市成功', 'result' => $result);
        }else{
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '获取城市失败', 'result' => $result);
        }
        ajax_return($res);
    }

    //老师获取即时通讯信息
    function getTimApi(){
        $request = Input('get.','','trim,addslashes');
        $stafferOne = $this->DataControl->getFieldOne("smc_staffer","staffer_branch,staffer_cnname,staffer_enname,staffer_img,staffer_sex","staffer_id = '{$request['staffer_id']}'");
        $userid = 'T'.$stafferOne['staffer_branch'];

        if(!$stafferOne['staffer_img']){
            if($stafferOne['staffer_sex'] == '女'){
                $stafferOne['staffer_img'] = 'https://pic.kedingdang.com/schoolmanage/202101191622x254703483.png';
            }else{
                $stafferOne['staffer_img'] = 'https://pic.kedingdang.com/schoolmanage/202101191622x427725268.png';
            }
        }

        $this->insertAccountOne($userid, $stafferOne['staffer_cnname'].$stafferOne['staffer_enname'], $stafferOne['staffer_img']);

        $user_sign = $this->getUserSig($userid);

        $result = array();
        if($user_sign){
            $result["user_sign"] = $user_sign;
            $result["user_id"] = $userid;
            $res = array('error' => '0', 'errortip' => '获取成功', 'result' => $result);
        }else{
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '获取失败', 'result' => $result);
        }
        ajax_return($res);
    }

    //获取学生即时通讯信息
    function getStuTimApi(){
        $request = Input('get.','','trim,addslashes');
        $studentOne = $this->DataControl->getFieldOne("smc_student","student_branch,student_cnname,student_enname,student_img,student_sex","student_id = '{$request['student_id']}'");
        $userid = 'S'.$studentOne['student_branch'];

        if(!$studentOne['student_img']){
            if($studentOne['student_sex'] == '女'){
                $studentOne['student_img'] = 'https://pic.kedingdang.com/schoolmanage/202101191704x550337680.png';
            }else{
                $studentOne['student_img'] = 'https://pic.kedingdang.com/schoolmanage/202101191704x965822906.png';
            }
        }

        $this->insertAccountOne($userid, $studentOne['student_cnname'].$studentOne['student_enname'], $studentOne['student_img']);

        $this->addFriend($request['uid'],$userid);

        $user_sign = $this->getUserSig($userid);

        $result = array();
        if($user_sign){
            $result["user_sign"] = $user_sign;
            $result["user_id"] = $userid;
            $res = array('error' => '0', 'errortip' => '获取成功', 'result' => $result);
        }else{
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '获取失败', 'result' => $result);
        }
        ajax_return($res);
    }

    function getsigView(){
        $user_sign = $this->getUserSig('kedingdang_2020');
        var_dump($user_sign);
    }

}
