<?php
/**
 * 客诉-PC-日历日程安排
 */

namespace Work\Controller\Easxapi;


class UcsScheduleController extends UcsCommonController
{
    /**
     * 日历
     */
    function scheduleListView()
    {
        $request = Input('GET.','','trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Easx\UcsScheduleModel($request);
        $Model->scheduleList($request);
        $field = array();
        $field['message_id'] = "消息ID";
        $field['repairorder_pid'] = "工单号";
        $field['message_content'] = "消息内容";
        $field['message_status'] = "是否已读";
        $field['message_playname'] = "消息类型";
        $field['message_createdate'] = "消息日期";

        $result = array();

        $result["field"] = $field;
        $result["data"] = $Model->result;

        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $result));
    }

    /**
     * 日历详情
     */
    function scheduleDetailView()
    {
        $request = Input('GET.','','trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Easx\UcsScheduleModel($request);
        $Model->scheduleDetail($request);
        $field = array();
        $field['message_id'] = "消息ID";
        $field['repairorder_pid'] = "工单号";
        $field['message_content'] = "消息内容";
        $field['message_status'] = "是否已读";
        $field['message_playname'] = "消息类型";
        $field['message_createdate'] = "消息日期";

        $result = array();

        $result["field"] = $field;
        $result["data"] = $Model->result;

        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $result),$request['language_type']);
    }

    /**
     * 提醒列表
     */
    function repairorderReminderRecordView()
    {
        $request = Input('GET.','','trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Easx\UcsScheduleModel($request);
        $Model->repairorderReminderRecord($request);
        $field = array();

        $k=0;
        $field[$k]["fieldstring"] = "repairorder_contactname";
        $field[$k]["fieldname"] = "投诉人称呼";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "repairorder_status_name";
        $field[$k]["fieldname"] = "工单状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "track_num";
        $field[$k]["fieldname"] = "跟进次数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "feedbacktype_catgory_name";
        $field[$k]["fieldname"] = "投诉类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "is_track";
        $field[$k]["fieldname"] = "是否跟进 1:跟了 0:没有";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();

        $result["field"] = $field;
        $result["data"] = $Model->result;

        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $result));

    }


}