<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/2/14
 * Time: 14:07
 */

namespace Work\Controller\Easxapi;

use Model\Easx\AppraiseModel;

class AppraiseController extends viewTpl
{
    public $u;
    public $t;
    public $c;

    //预加载处理类
    function __construct($visitType = "api")
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
    }

//    //本地权限校验入口
//    function ThisVerify($request)
//    {
//        $paramArray = array();
//        $paramArray['staffer_id'] = $request['staffer_id'];
//        $paramArray['school_id'] = $request['school_id'];
//        $paramArray['company_id'] = $request['company_id'];
//        $paramArray['token'] = $request['token'];
//        if (!$this->UserLimit($paramArray)) {
//            $result = array();
//            $result["list"] = array();
//            $result["tokeninc"] = "0";
//            $res = array('error' => '1', 'errortip' => "用户token失效", 'result' => $result);
//            ajax_return($res);
//        }
//    }

    //课堂评价
    function HomeView(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new AppraiseModel($request);
        $datalist = $Model->getAppraiseList($request);

        $result = array();
        if ($datalist) {
            $result['list'] = $datalist['list'];
            $result['allnum'] = $datalist['allnums'];
            $res = array('error' => 0, 'errortip' => '获取课堂评价信息成功', 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => 1, 'errortip' => '暂无课堂评价信息', 'result' => $result);
        }
        ajax_return($res);
    }

    //课堂评价详情
    function AppraiseDteailView(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new AppraiseModel($request);
        $datalist = $Model->AppraiseDteail($request);

        $result = array();
        if ($datalist) {
            $result['list'] = $datalist;
            $res = array('error' => 0, 'errortip' => '获取课堂评价信息成功', 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => 1, 'errortip' => '暂无课堂评价信息', 'result' => $result);
        }
        ajax_return($res);
    }


    //添加课堂评价
    function AddAppraiseAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new AppraiseModel($request);
        $result = $Model->AddAppraise($request);

        ajax_return($result);
    }
}