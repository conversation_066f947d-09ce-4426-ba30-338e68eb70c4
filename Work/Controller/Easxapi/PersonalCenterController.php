<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2019/12/11
 * Time: 10:21
 */

namespace Work\Controller\Easxapi;

use Model\Easx\CoursewareModel;

class PersonalCenterController extends viewTpl{
    public $u;
    public $t;
    public $c;
    public $visitType = "api";

    //预加载处理类
    function __construct($visitType = "api")
    {
        parent::__construct ();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
    }

    //本地权限校验入口
    function ThisVerify($request)
    {
        $paramArray = array();
        $paramArray['staffer_id'] = $request['staffer_id'];
        $paramArray['school_id'] = $request['school_id'];
        $paramArray['company_id'] = $request['company_id'];
        $paramArray['token'] = $request['token'];
        if(!$this->UserLimit($paramArray)){
            $result = array();
            $result["list"] = array();
            $result["tokeninc"] = "0";
            $res = array('error' => '1', 'errortip' => "用户token失效", 'result' => $result);
            ajax_return($res);
        }
    }

    //首页 -- 我的班级
    function HomeView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Easx\EducationModel($request);
        $dataList = $Model->getStaClassList($request);

        $result = array();
        $result["allnum"] = $dataList['allnum'];
        if ($dataList['list']) {
            $result["list"] = $dataList['list'];
            if ($result["list"]) {
                $res = array('error' => '0', 'errortip' => '获取我的班级成功', 'allnum' => $dataList['allnum'], 'result' => $result);
            } else {
                $result["list"] = array();
                $res = array('error' => '1', 'errortip' => '暂无我的班级', 'allnum' => 0, 'result' => $result);
            }
        } else {
            $result["list"] = array();
            $res = array('error' => '1', 'errortip' => '暂无我的班级', 'allnum' => 0, 'result' => $result);
        }
        ajax_return($res);
    }

    //在学课程
    function InlearnCourseView(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new CoursewareModel($request);
        $dataList = $Model->InlearnCourse($request);

        $result = array();
        $result["allnum"] = $dataList['allnums'];
        if($dataList['list']){
            $result["list"] = $dataList['list'];
            $res = array('error' => '0', 'errortip' => '获取在学课程成功', 'result' => $result);
        }else{
            $result["list"] = array();
            $res = array('error' => '1', 'errortip' => '啊哦，暂无在学课程哦~', 'result' => $result);
        }
        ajax_return($res);
    }

    //获取优秀批注
    function getExcellentPostilView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new CoursewareModel($request);
        $datalist = $Model->getExcellentPostil($request);

        $result = array();
        $result['allnum'] = $datalist['allnum'];
        if ($datalist['list']) {
            $result['list'] = $datalist['list'];
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => 1, 'errortip' => '啊哦，暂无批注哦~', 'result' => $result);
        }
        ajax_return($res);
    }

    //我的批注
    function getPostilView(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new CoursewareModel($request);
        $dataList = $Model->getPostil($request);

        $result = array();
        $result["allnum"] = $dataList['allnums'];
        if($dataList['list']){
            $result["list"] = $dataList['list'];
            $res = array('error' => '0', 'errortip' => '获取批注成功', 'result' => $result);
        }else{
            $result["list"] = array();
            $res = array('error' => '1', 'errortip' => '啊哦，暂无批注哦~', 'result' => $result);
        }
        ajax_return($res);
    }

    //查看批注
    function SeePostilView(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new CoursewareModel($request);
        $dataList = $Model->SeePostil($request);

        $result = array();
        if($dataList){
            $result["list"] = $dataList;
            $res = array('error' => '0', 'errortip' => '获取批注成功', 'result' => $result);
        }else{
            $result["list"] = array();
            $res = array('error' => '1', 'errortip' => '啊哦，暂无批注哦~', 'result' => $result);
        }
        ajax_return($res);
    }

    //查看单个批注
    function SeeOnePostilView(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new CoursewareModel($request);
        $dataList = $Model->SeeOnePostil($request);

        $result = array();
        if($dataList){
            $result["list"] = $dataList;
            $res = array('error' => '0', 'errortip' => '获取单个批注成功', 'result' => $result);
        }else{
            $result["list"] = array();
            $res = array('error' => '1', 'errortip' => '啊哦，暂无批注哦~', 'result' => $result);
        }
        ajax_return($res);
    }

    //查看笔记
    function LookNoteView(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new CoursewareModel($request);
        $dataList = $Model->LookNote($request);

        $result = array();
        $result["stage"] = $dataList['stage'];
        if($dataList["list"]){
            $result["list"] = $dataList['list'];
            $res = array('error' => '0', 'errortip' => '获取笔记成功', 'result' => $result);
        }else{
            $result["list"] = array();
            $res = array('error' => '1', 'errortip' => '啊哦，暂无笔记哦~', 'result' => $result);
        }
        ajax_return($res);
    }

    //查看单个笔记
    function LookOneNoteView(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new CoursewareModel($request);
        $dataList = $Model->LookOneNote($request);

        $result = array();
        if($dataList){
            $result["list"] = $dataList;
            $res = array('error' => '0', 'errortip' => '获取单个笔记成功', 'result' => $result);
        }else{
            $result["list"] = array();
            $res = array('error' => '1', 'errortip' => '啊哦，暂无笔记哦~', 'result' => $result);
        }
        ajax_return($res);
    }

    //我的教学作品
    function getVideoView(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new CoursewareModel($request);
        $dataList = $Model->getVideo($request);

        $result = array();
        $result["allnum"] = $dataList['allnums'];
        if($dataList['list']){
            $result["list"] = $dataList['list'];
            $res = array('error' => '0', 'errortip' => '获取视频成功', 'result' => $result);
        }else{
            $result["list"] = array();
            $res = array('error' => '1', 'errortip' => '暂无教学视频哦，快去上传吧~', 'result' => $result);
        }
        ajax_return($res);
    }

    //我的收藏
    function getCollectionView(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new CoursewareModel($request);
        $dataList = $Model->getCollection($request);

        $result = array();
        $result["allnum"] = $dataList['allnums'];
        if($dataList['list']){
            $result["list"] = $dataList['list'];
            $res = array('error' => '0', 'errortip' => '获取收藏成功', 'result' => $result);
        }else{
            $result["list"] = array();
            $res = array('error' => '1', 'errortip' => '啊哦，暂无收藏哦~', 'result' => $result);
        }
        ajax_return($res);
    }

    //获取个人资料
    function UserView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new CoursewareModel($request);
        $result = $Model->getUserlist($request);

        ajax_return($result);
    }

    //修改个人信息
    function updateStafferInfoAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new CoursewareModel($request);
        $result = $Model->updateStafferInfoAction($request);

        ajax_return($result);
    }

    //修改密码
    function updatePassAction(){
        $request = Input('post.','','trim,strip_tags');
        $this->ThisVerify($request);//验证账户

        $pattern = "/^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{6,}$/";
        if (!preg_match($pattern, $request['staffer_pass1'])){
            $res = array('error' => '1', 'errortip' => "密码必须包含数字和字母", 'result' => array());
            ajax_return($res);
        }

        $Model = new CoursewareModel($request);
        $result = $Model->updatePassAction($request);

        ajax_return($result);
    }

    //上传头像
    function PictureView()
    {
        $request = Input('post.','','trim,addslashes');
        if (!$request['company_id']) {
            $res = array('error' => '1', 'errortip' => "请传入公司ID", 'result' => array());
            ajax_return($res);
        }
        if (!empty ($_FILES ["ossfile"])) {
            $md5file = md5_file($_FILES['ossfile']['tmp_name']);
            $getTimg = $this->DataControl->getFieldOne('eas_upload_picture', "picture_id,picture_imgurl,picture_thumburl", "picture_md5='{$md5file}' and company_id = '{$request['company_id']}'");
            if ($getTimg) {
                $result = array();
                $result['picture_imgurl'] = $getTimg['picture_imgurl'];
                $result['picture_thumburl'] = $getTimg['picture_thumburl'];
                $res = array('error' => 0, 'errortip' => "图片上传成功!", "result" => $result);
                ajax_return($res);
            } else {
                $md5file = md5_file($_FILES['ossfile']['tmp_name']);
                $imglink = UpOssFile($_FILES);
                if ($imglink) {
                    $date = array();
                    $date['company_id'] = $request['company_id'];
                    $date['picture_name'] = $_FILES['ossfile']['tmp_name'];
                    $date['picture_imgurl'] = $imglink;
                    $date['picture_thumburl'] = $imglink;
                    $date['picture_md5'] = $md5file;
                    $this->DataControl->insertData('eas_upload_picture', $date);

                    $result = array();
                    $result['picture_imgurl'] = $date['picture_imgurl'];
                    $result['picture_thumburl'] = $date['picture_thumburl'];
                    $res = array('error' => 0, 'errortip' => "图片上传成功!", "result" => $result);
                    ajax_return($res);
                } else {
                    $res = array('error' => 1, 'errortip' => "图片上传失败!", "result" => array());
                    ajax_return($res);
                }
            }
        } else {
            $res = array('error' => 1, 'errortip' => "您未选择任何图片上传!", "result" => array());
            ajax_return($res);
        }
    }
}