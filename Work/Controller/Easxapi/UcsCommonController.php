<?php
/**
 * 客诉-PC-公共模块(登录态校验)
 */

namespace Work\Controller\Easxapi;


class UcsCommonController extends viewTpl
{
    public $u;
    public $t;
    public $c;

    //预加载处理类
    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
    }

    /**
     * 获取职务信息
     *
     */
    function getStafferInfoView()
    {
        $request = Input('GET.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Easx\UcsCommonModel($request);
        $Model->getStafferInfo($request);
        $field = array();
        $field['region_id'] = "地区ID";
        $result = array();

        $result["field"] = $field;
        $result["data"] = $Model->result;

        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $result));
    }

    /**
     * 处理方式列表 -zjc
     */
//    function processModeListView()
//    {
//        $request = Input('GET.','','trim,addslashes');
//        $this->ThisVerify($request);//验证账户
//        $Model = new \Model\Easx\UcsCommonModel($request);
//        $Model->processModeList();
//        $field = array();
//        $field['processmode_type'] = "处理类型";
//        $field['processmode_name'] = "处理名称";
//        $field['processmode_note'] = "处理方式说明";
//        $result = array();
//
//        $result["field"] = $field;
//        $result["data"] = $Model->result;
//
//        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $result));
//    }


    /**
     * 工单跟进种类
     */
    function followupListView()
    {
        $request = Input('GET.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Easx\UcsCommonModel($request);
        $Model->followupList($request);
        $field = array();
        $field[''] = "";
        $result = array();

        $result["field"] = $field;
        $result["data"] = $Model->result;

        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $result));
    }

    /**
     * 获取可以转发的组织
     */
    function getForwardOrganizationsView()
    {
        $request = Input('GET.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Easx\UcsCommonModel($request);
        $Model->getForwardOrganizations($request);
        $field = array();
        $field[''] = "";
        $result = array();

        $result["field"] = $field;
        $result["data"] = $Model->result;

        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $result));
    }

    /**
     * 有客诉权限的职员列表 -zjc
     */
    function getForwardStafferListView()
    {
        $request = Input('GET.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Easx\UcsCommonModel($request);
        $Model->getUscStafferList($request);
        $field = array();
        $field[''] = "";
        $result = array();

        $result["field"] = $field;
        $result["data"] = $Model->result;

        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $result));
    }

    function getIssensitiveRepairorderStafferListView()
    {
        $request = Input('GET.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Easx\UcsCommonModel($request);
        $Model->getIssensitiveRepairorderStafferList($request);
        $field = array();
        $field[''] = "";
        $result = array();

        $result["field"] = $field;
        $result["data"] = $Model->result;

        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $result));
    }


    /**
     * 首页统计数据 -zjc
     * @param [startdate] 开始日期
     * @param [enddate] 结束日期
     */
    function indexDataStatisticsView()
    {
        $request = Input('GET.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Easx\UcsCommonModel($request);
        $Model->indexDataStatistics($request);
        $field = array();
        $field['accepted_repairorder_num'] = "待处理";
        $field['processing_repairorder_num'] = "处理中";
        $field['completed_repairorder_num'] = "已结案";
        $result = array();

        $result["field"] = $field;
        $result["data"] = $Model->result;

        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $result));
    }

    /**
     * 结案模板
     */
    function settleRepairorderTemplateListView()
    {
        $request = Input('GET.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Easx\UcsCommonModel($request);
        $Model->settleRepairorderTemplateList($request);
        $field = array();
        $field['content'] = "内容";
        $result = array();

        $result["field"] = $field;
        $result["data"] = $Model->result;

        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $result));
    }


}