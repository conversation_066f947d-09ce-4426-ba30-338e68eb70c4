<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2019/11/28
 * Time: 13:30
 */

namespace Work\Controller\Easxapi;

use Model\Easx\CoursewareModel;

class CoursewareController extends viewTpl
{
    public $u;
    public $t;
    public $c;

    //预加载处理类
    function __construct($visitType = "api")
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
    }

    //本地权限校验入口
    function ThisVerify($request)
    {
        $paramArray = array();
        $paramArray['staffer_id'] = $request['staffer_id'];
        $paramArray['school_id'] = $request['school_id'];
        $paramArray['company_id'] = $request['company_id'];
        $paramArray['token'] = $request['token'];
        if (!$this->UserLimit($paramArray)) {
            $result = array();
            $result["list"] = array();
            $result["tokeninc"] = "0";
            $res = array('error' => '1', 'errortip' => "用户token失效", 'result' => $result);
            ajax_return($res);
        }
    }


    //班级信息列表
    function HomeView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new CoursewareModel($request);
        if($Model->error){
            $result['is_power'] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
            ajax_return($res);
        }
        $datalist = $Model->getClassList($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldname"] = "班级ID";
        $field[$k]["fieldstring"] = "class_id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "学校ID";
        $field[$k]["fieldstring"] = "school_id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "校园名称";
        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "班级名称";
        $field[$k]["fieldstring"] = "class_cnname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "班级别名";
        $field[$k]["fieldstring"] = "class_enname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "班级编号";
        $field[$k]["fieldstring"] = "class_branch";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        if($request['account_class'] == '1' || $Model->stafferOne['postpart_isteregulator'] == '1'){
            $field[$k]["fieldname"] = "教师";
            $field[$k]["fieldstring"] = "staffer_cnname";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;
        }

        $field[$k]["fieldname"] = "人数";
        $field[$k]["fieldstring"] = "class_fullnums";
        $field[$k]["show"] = 1;
        $field[$k]["isProgress"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "班级状态";
        $field[$k]["fieldstring"] = "class_status_name";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "课程别";
        $field[$k]["fieldstring"] = "course_cnname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "课程别编号";
        $field[$k]["fieldstring"] = "course_branch";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "教室";
        $field[$k]["fieldstring"] = "classroom_cnname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $datalist['allnums'];
        if ($datalist['list']) {
            $result['list'] = $datalist['list'];
            $res = array('error' => 0, 'errortip' => '获取班级信息成功', 'result' => $result);
        } else {
            $result['list'] = array();
            if($request['account_class'] == '1' || $Model->stafferOne['postpart_isteregulator'] == '1') {
                if (isset($paramArray['class_status']) && $paramArray['class_status'] == '1') {
                    $res = array('error' => 1, 'errortip' => '啊哦，暂无进行中的班级信息喔，请选择教师筛选~', 'result' => $result);
                } elseif (isset($paramArray['class_status']) && $paramArray['class_status'] == '0') {
                    $res = array('error' => 1, 'errortip' => '啊哦，暂无未开始的班级信息喔，请选择教师筛选~', 'result' => $result);
                } else {
                    $res = array('error' => 1, 'errortip' => '啊哦，暂无班级信息喔，请选择教师筛选~', 'result' => $result);
                }
            }else{
                if(isset($paramArray['class_status']) && $paramArray['class_status'] == '1'){
                    $res = array('error' => 1, 'errortip' => '啊哦，暂无进行中的班级信息喔~', 'result' => $result);
                }elseif(isset($paramArray['class_status']) && $paramArray['class_status'] == '0'){
                    $res = array('error' => 1, 'errortip' => '啊哦，暂无未开始的班级信息喔~', 'result' => $result);
                }else{
                    $res = array('error' => 1, 'errortip' => '啊哦，暂无班级信息喔~', 'result' => $result);
                }
            }
        }
        ajax_return($res);
    }

    //班级信息 -- 下拉 -- 校园
    function getSchoolView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new CoursewareModel($request);
        if($Model->error){
            $result['is_power'] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
            ajax_return($res);
        }
        $datalist = $Model->getSchoolList($request);

        $result = array();
        if ($datalist) {
            $result['list'] = $datalist;
            $res = array('error' => 0, 'errortip' => '获取校园信息成功', 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => 1, 'errortip' => '暂无校园信息', 'result' => $result);
        }
        ajax_return($res);
    }

    //班级信息 -- 下拉 -- 课程别
    function getCourseView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new CoursewareModel($request);
        if($Model->error){
            $result['is_power'] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
            ajax_return($res);
        }
        $datalist = $Model->getCourseList($request);

        $result = array();
        if ($datalist) {
            $result['list'] = $datalist;
            $res = array('error' => 0, 'errortip' => '获取课程别信息成功', 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => 1, 'errortip' => '暂无课程别信息', 'result' => $result);
        }
        ajax_return($res);
    }

    //班级信息 -- 下拉 -- 教师
    function getTeacherView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new CoursewareModel($request);
        if($Model->error){
            $result['is_power'] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
            ajax_return($res);
        }
        $datalist = $Model->getTeacherList($request);

        $result = array();
        if ($datalist) {
            $result['list'] = $datalist;
            $res = array('error' => 0, 'errortip' => '获取教师信息成功', 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => 1, 'errortip' => '暂无教师信息', 'result' => $result);
        }
        ajax_return($res);
    }

    //班级信息 -- 下拉 -- 教室
    function getClassroomView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new CoursewareModel($request);
        if($Model->error){
            $result['is_power'] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
            ajax_return($res);
        }
        $datalist = $Model->getClassroomList($request);

        $result = array();
        if ($datalist) {
            $result['list'] = $datalist;
            $res = array('error' => 0, 'errortip' => '获取教室信息成功', 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => 1, 'errortip' => '暂无教室信息', 'result' => $result);
        }
        ajax_return($res);
    }

    //班级信息 -- 下拉 -- 班级
    function getClassView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new CoursewareModel($request);
        if($Model->error){
            $result['is_power'] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
            ajax_return($res);
        }
        $datalist = $Model->getClass($request);

        $result = array();
        if ($datalist) {
            $result['list'] = $datalist;
            $res = array('error' => 0, 'errortip' => '获取班级信息成功', 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => 1, 'errortip' => '暂无班级信息', 'result' => $result);
        }
        ajax_return($res);
    }

    //班级信息 -- 下拉 -- 班组
    function getCoursetypeView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new CoursewareModel($request);
        if($Model->error){
            $result['is_power'] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
            ajax_return($res);
        }
        $datalist = $Model->getCoursetypeList($request);

        $result = array();
        if ($datalist) {
            $result['list'] = $datalist;
            $res = array('error' => 0, 'errortip' => '获取课程别信息成功', 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => 1, 'errortip' => '暂无课程别信息', 'result' => $result);
        }
        ajax_return($res);
    }

    //班级信息 -- 下拉 -- 班种
    function getCoursecatView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new CoursewareModel($request);
        if($Model->error){
            $result['is_power'] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
            ajax_return($res);
        }
        $datalist = $Model->getCoursecatList($request);

        $result = array();
        if ($datalist) {
            $result['list'] = $datalist;
            $res = array('error' => 0, 'errortip' => '获取班种信息成功', 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => 1, 'errortip' => '暂无班种信息', 'result' => $result);
        }
        ajax_return($res);
    }

    //备课 -- 选择课时
    function getClasshourView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new CoursewareModel($request);
        if($Model->error){
            $result['is_power'] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
            ajax_return($res);
        }
        $datalist = $Model->getClasshour($request);


        $field = array();
        $k = 0;
        $field[$k]["fieldname"] = "课时ID";
        $field[$k]["fieldstring"] = "hour_id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "班级ID";
        $field[$k]["fieldstring"] = "class_id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "课时名称";
        $field[$k]["fieldstring"] = "hour_name";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "上课状态";
        $field[$k]["fieldstring"] = "hour_ischecking_name";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["isChangeStatus"] = 1;
        $k++;

        $field[$k]["fieldname"] = "备课状态";
        $field[$k]["fieldstring"] = "prepare_status_name";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["isChangeStatus"] = 1;

        $result = array();
        $result["field"] = $field;
        $result['allnum'] = $datalist['allnums'];
        $result['classname'] = $datalist['classname'];
        if ($datalist['list']) {
            $result['list'] = $datalist['list'];
            $res = array('error' => 0, 'errortip' => '获取课时信息成功', 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => 1, 'errortip' => '暂无课时信息', 'result' => $result);
        }
        ajax_return($res);
    }

    //备课 -- 添加/修改批注
    function PersonPostilAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new CoursewareModel($request);
        if($Model->error){
            $result['is_power'] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
            ajax_return($res);
        }
        $result = $Model->PersonPostilApi($request);

        ajax_return($result);
    }

    //备课 -- 添加个人教学作品
    function AddTeachplanAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new CoursewareModel($request);
        if($Model->error){
            $result['is_power'] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
            ajax_return($res);
        }
        $result = $Model->AddTeachplanApi($request);

        ajax_return($result);
    }

    //获取教案信息
    function getLessonPlanView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new CoursewareModel($request);
        if($Model->error){
            $result['is_power'] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
            ajax_return($res);
        }
        $datalist = $Model->getLessonPlan($request);

        $result = array();
        if ($datalist) {
            $result['list'] = $datalist;
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => 1, 'errortip' => '暂无数据', 'result' => $result);
        }
        ajax_return($res);
    }

    //在线编辑教案信息
    function EditLessonPlanView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new CoursewareModel($request);
        if($Model->error){
            $result['is_power'] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
            ajax_return($res);
        }
        $datalist = $Model->EditLessonPlan($request);

        $result = array();
        if ($datalist) {
            $result['list'] = $datalist;
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => 1, 'errortip' => '暂无数据', 'result' => $result);
        }
        ajax_return($res);
    }

    //在线编辑教案信息操作
    function EditLessonPlanAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new CoursewareModel($request);
        if($Model->error){
            $result['is_power'] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
            ajax_return($res);
        }
        $result = $Model->EditLessonPlanApi($request);

        ajax_return($result);
    }

    //教案备课信息创建
    function AddLessonPlanInfoAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new CoursewareModel($request);
        if($Model->error){
            $result['is_power'] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
            ajax_return($res);
        }
        $result = $Model->AddLessonPlanInfo($request);

        ajax_return($result);
    }

    //备案进程
    function ProcessAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new CoursewareModel($request);
        if($Model->error){
            $result['is_power'] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
            ajax_return($res);
        }
        $datalist = $Model->Process($request);

        ajax_return($datalist);
    }


    //获取教学图片
    function getTeachpicsView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new CoursewareModel($request);
        if($Model->error){
            $result['is_power'] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
            ajax_return($res);
        }
        $datalist = $Model->getTeachpics($request);

        $result = array();
        if ($datalist) {
            $result['list'] = $datalist;
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => 1, 'errortip' => '暂无数据', 'result' => $result);
        }
        ajax_return($res);
    }

    //获取预览批注/预览教案
    function getCoursewareView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new CoursewareModel($request);
        if($Model->error){
            $result['is_power'] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
            ajax_return($res);
        }
        $datalist = $Model->getCourseware($request);

        $result = array();
        if ($datalist) {
            $result['list'] = $datalist;
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => 1, 'errortip' => '暂无数据', 'result' => $result);
        }
        ajax_return($res);
    }

    //添加收藏
    function AddCollectAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new CoursewareModel($request);
        if($Model->error){
            $result['is_power'] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
            ajax_return($res);
        }
        $result = $Model->AddCollect($request);

        ajax_return($result);
    }

    //收藏/取消收藏
    function AddCancelCollectAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new CoursewareModel($request);
        if($Model->error){
            $result['is_power'] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
            ajax_return($res);
        }
        $result = $Model->AddCancelCollect($request);

        ajax_return($result);
    }

    //点赞/取消点赞
    function AddCancelPraiseAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new CoursewareModel($request);
        if($Model->error){
            $result['is_power'] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
            ajax_return($res);
        }
        $result = $Model->AddCancelPraise($request);

        ajax_return($result);
    }


    //观看视频
    function AddCancelWatchAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new CoursewareModel($request);
        if($Model->error){
            $result['is_power'] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
            ajax_return($res);
        }
        $result = $Model->AddCancelWatch($request);

        ajax_return($result);
    }

    //获取其他优秀教学作品
    function getExcellentPostilVideoView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new CoursewareModel($request);
        if($Model->error){
            $result['is_power'] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
            ajax_return($res);
        }
        $datalist = $Model->getExcellentPostilVideo($request);

        $result = array();
        $result['allnum'] = $datalist['allnum'];
        if ($datalist) {
            $result['list'] = $datalist;
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => 1, 'errortip' => '暂无数据', 'result' => $result);
        }
        ajax_return($res);
    }


    //上传个人教学视频
    function UploadVideoAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new CoursewareModel($request);
        if($Model->error){
            $result['is_power'] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
            ajax_return($res);
        }
        $result = $Model->UploadVideoApi($request);

        ajax_return($result);
    }


    //获取优质教学作品
    function getExcellentPostilView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new CoursewareModel($request);
        if($Model->error){
            $result['is_power'] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
            ajax_return($res);
        }
        $datalist = $Model->getExcellentPostilApi($request);

        $result = array();
        if ($datalist) {
            $result['list'] = $datalist;
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => 1, 'errortip' => '暂无数据', 'result' => $result);
        }
        ajax_return($res);
    }

    //OSS上传文件路径
    function FileView()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        if (!$request['company_id']) {
            $res = array('error' => '1', 'errortip' => "请传入公司ID", 'result' => array());
            ajax_return($res);
        }
        $this->c = "Oss";
        $md5file = md5_file($_FILES['ossfile']['tmp_name']);
        $fileurl = UpOssFile($_FILES);

        $getTfile = $this->DataControl->getFieldOne('gmc_upload_file', "file_id,file_url", "file_md5='{$md5file}' and company_id = '{$request['company_id']}'");
        if ($getTfile) {
            $result = array();
            $result['file_url'] = $getTfile['file_url'];
            $res = array('error' => 0, 'errortip' => "文件上传成功!", "result" => $result);
            ajax_return($res);
        } else {
            $date = array();
            $date['company_id'] = $request['company_id'];
            $date['file_name'] = $_FILES['ossfile']['filename'];
            $date['file_url'] = $fileurl;
            $date['file_md5'] = $md5file;
            $this->DataControl->insertData('gmc_upload_file', $date);

            $result = array();
            $result['file_url'] = $fileurl;
            $res = array('error' => 0, 'errortip' => "文件上传成功!", "result" => $result);
            ajax_return($res);
        }
    }

    //个性化教案
    function PersonLessonPlanView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new CoursewareModel($request);
        if($Model->error){
            $result['is_power'] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
            ajax_return($res);
        }
        $datalist = $Model->PersonLessonPlan($request);

        $result = array();
        if ($datalist) {
            $result['list'] = $datalist;
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => 1, 'errortip' => '暂无数据', 'result' => $result);
        }
        ajax_return($res);
    }

    //备课标记
    function PrepareMarkAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new CoursewareModel($request);
        if($Model->error){
            $result['is_power'] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
            ajax_return($res);
        }
        $result = $Model->PrepareMarkApi($request);

        ajax_return($result);
    }

    //课程资料
    function CourseMaterialsView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new CoursewareModel($request);
        if($Model->error){
            $result['is_power'] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
            ajax_return($res);
        }
        $datalist = $Model->CourseMaterials($request);

        $result = array();
        if ($datalist) {
            $result['list'] = $datalist;
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => 1, 'errortip' => '暂无数据', 'result' => $result);
        }
        ajax_return($res);
    }

    //文件下载
    function getDownloadView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $full_path = $request['tempfiles_url'];
        $kuozhan = strrchr($full_path, '.');
        $full_name = $request['tempfiles_name'] . $kuozhan;

        header("Content-Type: video/{$kuozhan}");
        header("Content-Disposition: attachment;filename={$full_name}");
        readfile($full_path);
    }

}