<?php
/**
 * ============================================================================
 * 版权所有 : https://www.mohism.cn
 * 网站地址 : https://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/3/12
 * Time: 22:30
 */

namespace Work\Controller\Api;

class DdzjChatController extends viewTpl
{
    public $data;
    public $appId = 'wx2a66618e4feffded';
    public $appSecret = '30983395129d5ee5ee76d27a79cea406';
    public $wxuser;

    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
    }

    //校务系统叮铛助教教师授权登录
    function scteloginView()
    {
        $request = Input('get.','','trim,addslashes');
        $host = "https://api.kedingdang.com/DdzjChat/wxChatUrl/id-".$request['company_id'];

        $app = $this->DataControl->getFieldOne("gmc_company_wxchatnumber","wxchatnumber_appid,wxchatnumber_appsecret","company_id = '{$request['company_id']}' and wxchatnumber_class = '0' and wxchatnumber_impower = '0'");
        if($app){
            $appId = $app['wxchatnumber_appid'];
            $appSecret = $app['wxchatnumber_appsecret'];
        }else{
            $kedingdang = $this->DataControl->getFieldOne("gmc_company_wxchatnumber","wxchatnumber_appid,wxchatnumber_appsecret","company_id = '0' and wxchatnumber_class = '0'");
            $appId = $kedingdang['wxchatnumber_appid'];
            $appSecret = $kedingdang['wxchatnumber_appsecret'];
        }

        $url = "https://open.weixin.qq.com/connect/oauth2/authorize?appid=" . $appId . "&redirect_uri=" . $host . "&response_type=code&scope=snsapi_userinfo&state=1#wechat_redirect";
        header("location:" . $url);
    }

    //校务系统叮铛助教教师授权登录
    function scteintoView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        if(!isset($request['tourl'])){
            $request['tourl'] = 'Home';
        }
        if($request['tourl'] == 'Crm'){
            $host = "https://api.kedingdang.com/DdzjChat/wxChatUrl?url=Crm";
        }elseif($request['tourl'] == 'Timetable'){
            $host = "https://api.kedingdang.com/DdzjChat/wxChatUrl?url=Timetable";
        }elseif($request['tourl'] == 'ClassReview'){
            $host = "https://api.kedingdang.com/DdzjChat/wxChatUrl?url=ClassReview";
        }elseif($request['tourl'] == 'HomeWork'){
            $host = "https://api.kedingdang.com/DdzjChat/wxChatUrl?url=HomeWork";
        }elseif($request['tourl'] == 'Home'){
            $host = "https://api.kedingdang.com/DdzjChat/wxChatUrl?url=Home";
        }elseif($request['tourl'] == 'myCenter'){
            $host = "https://api.kedingdang.com/DdzjChat/wxChatUrl?url=myCenter";
        }elseif($request['tourl'] == 'Center'){
            $host = "https://api.kedingdang.com/DdzjChat/wxChatUrl?url=Center";
        }

        if(isset($request['cid']) && $request['cid'] !==''){
            $wxchatnumberOne = $this->DataControl->getFieldOne("gmc_company_wxchatnumber", "wxchatnumber_appid,wxchatnumber_appsecret", "company_id = '{$request['cid']}' and wxchatnumber_class = '0'", "order by wxchatnumber_id DESC limit 0,1");
            if($wxchatnumberOne){
                $this->appId = $wxchatnumberOne['wxchatnumber_appid'];
                $this->appSecret = $wxchatnumberOne['wxchatnumber_appsecret'];

                if($request['tourl'] == 'Crm'){
                    $host = "https://api.kedingdang.com/DdzjChat/wxChatUrl/{$request['cid']}?url=Crm";
                }elseif($request['tourl'] == 'Timetable'){
                    $host = "https://api.kedingdang.com/DdzjChat/wxChatUrl/{$request['cid']}?url=Timetable";
                }elseif($request['tourl'] == 'ClassReview'){
                    $host = "https://api.kedingdang.com/DdzjChat/wxChatUrl/{$request['cid']}?url=ClassReview";
                }elseif($request['tourl'] == 'HomeWork'){
                    $host = "https://api.kedingdang.com/DdzjChat/wxChatUrl/{$request['cid']}?url=HomeWork";
                }elseif($request['tourl'] == 'Home'){
                    $host = "https://api.kedingdang.com/DdzjChat/wxChatUrl/{$request['cid']}?url=Home";
                }elseif($request['tourl'] == 'myCenter'){
                    $host = "https://api.kedingdang.com/DdzjChat/wxChatUrl/{$request['cid']}?url=myCenter";
                }elseif($request['tourl'] == 'Center'){
                    $host = "https://api.kedingdang.com/DdzjChat/wxChatUrl/{$request['cid']}?url=Center";
                }
            }
        }


        $url = "https://open.weixin.qq.com/connect/oauth2/authorize?appid=" . $this->appId . "&redirect_uri=" . $host . "&response_type=code&scope=snsapi_userinfo&state=1#wechat_redirect";
        header("location:" . $url);
    }
    //教师登录
    function wxChatUrlView()
    {
        $request = Input('get.','','trim,addslashes');
        if (isset($_GET['code'])) {
            if(isset($request['id']) && $request['id'] !=='') {
                $wxchatnumberOne = $this->DataControl->getFieldOne("gmc_company_wxchatnumber", "company_id,wxchatnumber_appid,wxchatnumber_appsecret", "company_id = '{$request['id']}' and wxchatnumber_class = '0'", "order by wxchatnumber_id DESC limit 0,1");
                if ($wxchatnumberOne) {
                    $this->appId = $wxchatnumberOne['wxchatnumber_appid'];
                    $this->appSecret = $wxchatnumberOne['wxchatnumber_appsecret'];
                }
                $companyId = $wxchatnumberOne['company_id'];
            }else{
                $companyId = 0;
            }

            $WeixinInfo = $this->getWeixinInfo($_GET['code'],$request['id']);
            $Weixinuser = $this->getWeixinUser($WeixinInfo['access_token'], $WeixinInfo['openid']);
            if ($Weixinuser['openid'] == '') {
                $url = "https://api.kedingdang.com/DdzjChat/sctelogin";
                header("location:" . $url);
            }

            if ($request['url'] == 'sctelogin') {
                $openurl = "https://tesc.kedingdang.com/home?wxtoken={$Weixinuser['openid']}&imghead={$Weixinuser['headimgurl']}&nickname={$Weixinuser['nickname']}";
                header("location:{$openurl}");
                exit;
            }elseif ($request['url'] == 'Crm') {
                $openurl = "https://tesc.kedingdang.com/wxentry?wxtoken={$Weixinuser['openid']}&fromcode={$companyId}&intourl=/crmIndex";
                header("location:{$openurl}");
                exit;
            }elseif ($request['url'] == 'Timetable') {
                $openurl = "https://tesc.kedingdang.com/wxentry?wxtoken={$Weixinuser['openid']}&fromcode={$companyId}&intourl=/LookTimetable/lookTimetable";
                header("location:{$openurl}");
                exit;
            }elseif ($request['url'] == 'ClassReview') {
                $openurl = "https://tesc.kedingdang.com/wxentry?wxtoken={$Weixinuser['openid']}&fromcode={$companyId}&intourl=/ClassReview/home";
                header("location:{$openurl}");
                exit;
            }elseif ($request['url'] == 'HomeWork') {
                $openurl = "https://tesc.kedingdang.com/wxentry?wxtoken={$Weixinuser['openid']}&fromcode={$companyId}&intourl=/HomeWork/homework";
                header("location:{$openurl}");
                exit;
            }elseif ($request['url'] == 'Home') {
                $openurl = "https://tesc.kedingdang.com/wxentry?wxtoken={$Weixinuser['openid']}&fromcode={$companyId}&intourl=/";
                header("location:{$openurl}");
                exit;
            }elseif ($request['url'] == 'myCenter') {
                $openurl = "https://tesc.kedingdang.com/wxentry?wxtoken={$Weixinuser['openid']}&fromcode={$companyId}&intourl=/myCenter";
                header("location:{$openurl}");
                exit;
            }elseif ($request['url'] == 'Center') {
                $openurl = "https://tesc.kedingdang.com/myCenter?wxtoken={$Weixinuser['openid']}&fromcode={$companyId}&imghead={$Weixinuser['headimgurl']}&nickname={$Weixinuser['nickname']}";
                header("location:{$openurl}");
                exit;
            }else {
                header("location:/");
                exit;
            }
        } else {
            header("location:/");
            exit;
        }
    }

    //校务系统叮铛助教教师授权登录
    function kidteloginView()
    {
        $host = "https://api.kedingdang.com/DdzjChat/wxChatUrl?url=kidtelogin";
        $url = "https://open.weixin.qq.com/connect/oauth2/authorize?appid=" . $this->appId . "&redirect_uri=" . $host . "&response_type=code&scope=snsapi_userinfo&state=1#wechat_redirect";
        header("location:" . $url);
    }

    //获取微信反馈数据
    function getWeixinInfo($CODE,$company_id)
    {
        $app = $this->DataControl->getFieldOne("gmc_company_wxchatnumber","wxchatnumber_appid,wxchatnumber_appsecret","company_id = '{$company_id}' and wxchatnumber_class = '0' and wxchatnumber_impower = '0'");
        if($app){
            $appId = $app['wxchatnumber_appid'];
            $appSecret = $app['wxchatnumber_appsecret'];
        }else{
            $kedingdang = $this->DataControl->getFieldOne("gmc_company_wxchatnumber","wxchatnumber_appid,wxchatnumber_appsecret","company_id = '0' and wxchatnumber_class = '0'");
            $appId = $kedingdang['wxchatnumber_appid'];
            $appSecret = $kedingdang['wxchatnumber_appsecret'];
        }
        $paramarray = array(
            'appid' => $appId,
            'secret' => $appSecret,
            'code' => $CODE,
            'grant_type' => "authorization_code"
        );

        $getBakurl = request_by_curl("https://api.weixin.qq.com/sns/oauth2/access_token", dataEncode($paramarray), "GET");
        $json_play = new \Webjson();
        $cardarray = $json_play->decode($getBakurl, "1");
        return $cardarray;
    }

    function getWeixinToken()
    {
        $token = $this->DataControl->getFieldOne("eas_weixin_token", "token_failuretime,token_string", "token_type = '1' and token_site = '1' and wxchatnumber_id = '1'", "order by token_failuretime DESC limit 0,1");
        if ($token && $token['token_failuretime'] > time()) {
            $wxtoken = array();
            $wxtoken['access_token'] = $token['token_string'];
            $wxtoken['expires_in'] = 7200;
            return $wxtoken;
        } else {
            $paramarray = array(
                'appid' => $this->appId,
                'secret' => $this->appSecret,
                'grant_type' => "client_credential"
            );
            $getBakurl = request_by_curl("https://api.weixin.qq.com/cgi-bin/token", dataEncode($paramarray), "GET");
            $json_play = new \Webjson();
            $cardarray = $json_play->decode($getBakurl, "1");
            $data = array();
            $data['wxchatnumber_id'] = '1';
            $data['token_site'] = '1';
            $data['token_type'] = '1';
            $data['token_string'] = $cardarray['access_token'];
            $data['token_failuretime'] = time() + $cardarray['expires_in'];
            $this->DataControl->insertData("eas_weixin_token", $data);
            return $cardarray;
        }
    }

    //获取微信User
    function getWeixinUser($token, $openid)
    {
        $paramarray = array(
            'access_token' => $token,
            'openid' => $openid,
            'lang' => "zh_CN"
        );

        $getBakurl = trim(request_by_curl("https://api.weixin.qq.com/sns/userinfo", dataEncode($paramarray), "GET"));
        $json_play = new \Webjson();
        $cardarray = $json_play->decode($getBakurl, "1");
        return $cardarray;
    }


    function setmenuView()
    {
        $data = '{
            "button": [
                {
                    "name": "快捷功能",
                    "sub_button": [{
                            "type": "view",
                            "name": "我的课表",
                            "url": "https://api.kedingdang.com/DdzjChat/scteinto?tourl=Timetable"
                        },
                        {
                            "type": "view",
                            "name": "上课点评",
                            "url": "https://api.kedingdang.com/DdzjChat/scteinto?tourl=ClassReview"
                        },
                        {
                            "type": "view",
                            "name": "课后作业",
                            "url": "https://api.kedingdang.com/DdzjChat/scteinto?tourl=HomeWork"
                        }
                    ]
                },
                {
                    "name": "叮铛助教",
                    "type": "view",
                    "url": "https://api.kedingdang.com/DdzjChat/scteinto?tourl=Home"
                },
                {
                    "name": "常用服务",
                    "sub_button": [{
                            "type": "miniprogram",
                            "name": "免费试用",
                            "url": "https://www.kedingdang.com/",
                            "appid": "wx550be80d3f68edb1",
                            "pagepath": "pages/index/main?fromchannel=ddzj"
                        },
                        {
                            "type": "view",
                            "name": "招生CRM",
                            "url": "https://api.kedingdang.com/DdzjChat/scteinto?tourl=Crm"
                        },
                        {
                            "type": "view",
                            "name": "我的",
                            "url": "https://api.kedingdang.com/DdzjChat/scteinto?tourl=myCenter"
                        }
                    ]
                }
            ]
        }';
        $tokenArray = $this->getWeixinToken();
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, "https://api.weixin.qq.com/cgi-bin/menu/create?access_token={$tokenArray['access_token']}");
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE);
        curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (compatible; MSIE 5.01; Windows NT 5.0)');
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
        curl_setopt($ch, CURLOPT_AUTOREFERER, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        $tmpInfo = curl_exec($ch);
        if (curl_errno($ch)) {
            echo 'Errno' . curl_error($ch);
        }
        curl_close($ch);
        var_dump($tmpInfo);
    }
}
