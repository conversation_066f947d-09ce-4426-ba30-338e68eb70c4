<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2023/7/31
 * Time: 14:29
 */

namespace Work\Controller\Api;

class ThirdPartyController extends viewTpl
{
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $stafferOne = array();

    function __construct()
    {
        parent::__construct();
    }

    function stringReplace($string)
    {
        $datacode = trim(str_replace('"', "", $string));
        $datacode = urldecode(urldecode($datacode));
        $datacode = str_replace(' ', "+", $datacode);
        return $datacode;
    }

    //第三方授权访问权限校验
    function UserVerify($paramArray)
    {
        $apiuserOne = $this->DataControl->getFieldOne('imc_apiuser', "apiuser_id,apiuser_aeskey,apiuser_aesiv", "apiuser_code = '{$paramArray['apiuser_code']}'");
        if (!$apiuserOne) {
            $this->errortip = "未查询到您的授权信息";
            $this->error = true;
            return false;
        }

        if (isset($paramArray['apiuser_aeskey']) && $paramArray['apiuser_aeskey'] !== '' && isset($paramArray['apiuser_aesiv']) && $paramArray['apiuser_aesiv'] !== '') {
            if ($apiuserOne['apiuser_aeskey'] == $paramArray['apiuser_aeskey'] && $apiuserOne['apiuser_aesiv'] == $paramArray['apiuser_aesiv']) {
                $baseOne = array();
                $baseOne['apiuser_id'] = $apiuserOne['apiuser_id'];
                $companyOne = $this->DataControl->selectOne("select company_id,company_cnname from gmc_company WHERE company_id = '{$paramArray['company_id']}' limit 0,1");
                if ($companyOne) {
                    $baseOne['company_id'] = $companyOne['company_id'];
                    return $baseOne;
                } else {
                    $this->errortip = '你的授权集团编号错误，请确认编号正确';
                    $this->error = true;
                    return false;
                }
            } else {
                $this->errortip = "你的授权秘钥及偏移值不正确，{$paramArray['apiuser_aeskey']}-{$paramArray['apiuser_aesiv']}";
                $this->error = true;
                return false;
            }
        }

        if (!isset($paramArray['timesteps']) || $paramArray['timesteps'] == '') {
            $this->errortip = "请传入授权时间";
            $this->error = true;
            return false;
        }

        if ($paramArray['timesteps'] + 60 * 5 < time() || $paramArray['timesteps'] - 60 > time()) {
            $maxtimes = date("Y-m-d H:i:s", $paramArray['timesteps'] + 60 * 5);
            $this->errortip = "授权时间{$maxtimes}已过期5分钟，请确认连接及时性";//,{$timesteps}--{$jmsting}
            $this->error = true;
            return false;
        }

        $aes = new \Aesencdec($apiuserOne['apiuser_aeskey'], $apiuserOne['apiuser_aesiv']);
        $xssting = $aes->decrypt($this->stringReplace($paramArray['veytoken']));//解密
        if ($paramJson = json_decode($xssting, 1)) {//转化为数组
            if ((string)$paramJson['timesteps'] !== trim($paramArray['timesteps'])) {
                $this->errortip = '授权时间和连接时间不一致';
                $this->error = true;
                return false;
            }
            $baseOne = array();
            $baseOne['apiuser_id'] = $apiuserOne['apiuser_id'];
            $baseOne['tokenstring'] = $xssting;
            $companyOne = $this->DataControl->selectOne("select company_id,company_cnname from gmc_company WHERE company_id = '{$paramJson['company_id']}' limit 0,1");
            if ($companyOne) {
                $baseOne['company_id'] = $companyOne['company_id'];
                return $baseOne;
            } else {
                $this->errortip = '你的授权集团编号错误，请确认编号正确';
                $this->error = true;
                return false;
            }
        } else {
            $this->errortip = '数据机密信息传输有误，请检查！';
            $this->error = true;
            return false;
        }
    }

    function VeryModelNums($apiuser_id, $apimodule_code, $paramArray)
    {
        $request = Input('get.', '', 'trim,addslashes');
        $apimoduleOne = $this->DataControl->getFieldOne('imc_apiuser_apimodule'
            , "apimodule_id,apiuser_id,apimodule_name,apimodule_nums", "apiuser_id = '{$apiuser_id}' AND apimodule_code = '{$apimodule_code}'");
        $stattTimes = strtotime(date("Y-m-d"));
        $apilogOne = $this->DataControl->selectOne("SELECT COUNT(l.apilog_id) AS anums FROM imc_apiuser_apilog AS l
WHERE l.apimodule_id = '{$apimoduleOne['apimodule_id']}' AND l.apilog_createtime > '{$stattTimes}'");
        if ($apilogOne['anums'] > $apimoduleOne['apimodule_nums']) {
            $this->errortip = "您接口{$apimoduleOne['apimodule_name']}的本日最大授权次数{$apimoduleOne['apimodule_nums']}已消耗完毕！";
            $this->error = true;
            return false;
        } else {
            $data = array();
            $data['apiuser_id'] = $apimoduleOne['apiuser_id'];
            $data['apimodule_id'] = $apimoduleOne['apimodule_id'];
            $data['apilog_posturl'] = "https://api.kedingdang.com/{$request['u']}/{$request['t']}";
            $data['apilog_posttype'] = 'GET';
            $data['apilog_postorgjson'] = http_build_query($paramArray);
            $data['apilog_postjson'] = $paramArray['tokenstring'];
            $data['apilog_ip'] = real_ip();
            $data['apilog_createtime'] = time();
            $this->DataControl->insertData("imc_apiuser_apilog", $data);
            return true;
        }
    }

    //模拟加密参数
    function testParameterView()
    {
        $parameter = array();
        $parameter['timesteps'] = time();
        $parameter['apiuser_code'] = 'RoleToKdd';
        $parameter['company_id'] = '8888';

        $apiuserOne = $this->DataControl->getFieldOne('imc_apiuser', "apiuser_id,apiuser_aeskey,apiuser_aesiv", "apiuser_code = '{$parameter['apiuser_code']}'");
        if (!$apiuserOne) {
            $this->errortip = "未查询到您的授权信息";
            $this->error = true;
            return false;
        }

        $aes = new \Aesencdec($apiuserOne['apiuser_aeskey'], $apiuserOne['apiuser_aesiv']);
        $parameterJson = json_encode($parameter, '1');
        $jmsting = $aes->encrypt($parameterJson);//解密

        $result = array();
        $result['timesteps'] = $parameter['timesteps'];
        $result['apiuser_code'] = $parameter['apiuser_code'];
        $result['veytoken'] = $jmsting;

        ajax_return(array('error' => '0', 'errortip' => '模拟参数获取成功', 'result' => $result));
    }

    //第三方获取主要信息
    function getThirdPartyLoginApi()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'getThirdPartyLoginApi', $request)) {

                if($request['employeepid'] == '') {
                    ajax_return(array('error' => '1', 'errortip' => '职工编号不能为空', 'result' => array()));
                }

                //判断跳转的集团
                if($request['isLang'] == 'cn'){
                    $company_id = '8888';
                    $language = 'zh';
                }elseif($request['isLang'] == 'tw'){
                    $company_id = '79081';
                    $language = 'tw';
                }else{
                    $company_id = '8888';
                    $language = 'zh';
                }

                $stafferOne = $this->DataControl->selectOne(" select staffer_id,staffer_istest,account_class,staffer_branch,staffer_bakpass from smc_staffer where company_id = '{$company_id}' and staffer_employeepid = '{$request['employeepid']}' and staffer_leave = '0' limit 0,1  ");
                if(!$stafferOne){
                    ajax_return(array('error' => '1', 'errortip' => '未找到您的职工信息，请联系管理员！', 'result' => array()));
                }

                if($request['isToMobile'] == '1'){
                    $data = array();

                    //判断跳转的集团
                    if($request['isLang'] == 'tw'){
                        ajax_return(array('error' => '1', 'errortip' => "台湾的版本，暂未开放", 'result' => array()));
                    }else{
                        $data['L_code'] = 'jidebao';
                        $data['L_name'] = $stafferOne['staffer_branch'];
                        $data['L_pswd'] = 'JSPTtoKDDmobile230809';
                        $data['language_type'] = $language;
                    }
                    $dataListJson = request_by_curl("https://easxapi.kedingdang.com/LoginAssistant/pswdloginApi", dataEncode($data), "POST",array());
//                    $dataListJson = request_by_curl("http://easxapi.schoolapi102.com/LoginAssistant/pswdloginApi", dataEncode($data), "POST",array());
                    $jaonToData = json_decode($dataListJson, 1);

                    if($jaonToData['error'] == '1'){
                        ajax_return(array('error' => '1', 'errortip' => "信息获取失败", 'result' => array()));
                    }

                    $dataList = array();
                    $dataList['staffer_id'] = $jaonToData['result']['staffer_id'];
                    $dataList['token'] = $jaonToData['result']['token'];
                    $dataList['urlToKdd'] = "https://tesc.kedingdang.com/wxentry?stafftoken={$jaonToData['result']['token']}&staffer_id={$jaonToData['result']['staffer_id']}&company_id={$jaonToData['result']['company_id']}&intourl=/crmIndex";
                }else{
                    $data = array();
                    //判断跳转的集团
                    if($request['isLang'] == 'tw'){
                        $data['L_code'] = 'jdbtw';
                    }else{
                        $data['L_code'] = 'jidebao';
                    }
                    $data['L_name'] = $stafferOne['staffer_branch'];
                    $data['L_pswd'] = $stafferOne['staffer_bakpass'];
                    $data['language_type'] = $language;
                    $dataListJson = request_by_curl("https://scloginapi.kedingdang.com/Login/pswdloginApi", dataEncode($data), "POST",array());
                    $jaonToData = json_decode($dataListJson, 1);

                    if($jaonToData['error'] == '1'){
                        ajax_return(array('error' => '1', 'errortip' => "信息获取失败", 'result' => array()));
                    }

                    $dataList = array();
                    $dataList['staffer_id'] = $jaonToData['result']['staffer_id'];
                    $dataList['token'] = $jaonToData['result']['token'];
                    $dataList['urlToKdd'] = "https://sclogin.kedingdang.com/choose?token={$jaonToData['result']['token']}&staffer_id={$jaonToData['result']['staffer_id']}&company_id={$jaonToData['result']['company_id']}&status={$jaonToData['status']}&isAdmin={$jaonToData['isAdmin']}&language={$language}";
                }

                $field = [
                    "staffer_id"=>"职工ID",
                    "token"=>"职工token",
                    "urlToKdd"=>"登录跳转课叮铛的链接",
                ];

                $result = array();
                $result["field"] = $field;
                $result['list'] =$dataList?$dataList:array();
                if ($dataList) {
                    ajax_return(array('error' => '0', 'errortip' => "信息获取成功", 'result' => $result));
                } else {
                    ajax_return(array('error' => '1', 'errortip' => "信息获取失败", 'result' => array()));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //获取 校首页的 预警信息
    function getSmcWarningApi(){
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'getSmcWarningApi', $request)) {

                if($request['staffer_id'] == '') {
                    ajax_return(array('error' => '1', 'errortip' => '职工ID不能为空', 'result' => array()));
                }
                if($request['token'] == '') {
                    ajax_return(array('error' => '1', 'errortip' => '职工token不能为空', 'result' => array()));
                }
                if($request['schoolbranch'] == '') {
                    ajax_return(array('error' => '1', 'errortip' => '学校编号不能为空！', 'result' => array()));
                }
                $schooOne = $this->DataControl->selectOne(" select school_id from smc_school where company_id = '8888' and school_branch = '{$request['schoolbranch']}' and school_isclose = '0' ");
                if(!$schooOne){
                    ajax_return(array('error' => '1', 'errortip' => '未找到对应的未关闭学校！', 'result' => array()));
                }

                $data = array();
                $data['staffer_id'] = $request['staffer_id'];
                $data['token'] = $request['token'];
                $data['company_id'] = '8888';
                $data['language_type'] = 'zh';
                $data['school_id'] = $schooOne['school_id'];
                $data['re_postbe_id'] = 0;
                $dataListJson = request_by_curl("https://smcapi.kedingdang.com/Home/warningApi", dataEncode($data), "GET",array());
                $jaonToData = json_decode($dataListJson, 1);

                if(!$jaonToData['result']){
                    ajax_return(array('error' => '1', 'errortip' => "信息获取失败", 'result' => array()));
                }

                $dataList = $jaonToData['result'];

                foreach ($dataList as $key=>&$dataVar){
                    if($key == 0){
                        $dataVar['name'] = '学员待入班提醒';
                    }elseif($key == 1){
                        $dataVar['name'] = '学员账单欠费预警';
                    }elseif($key == 2){
                        $dataVar['name'] = '学员预升班预警';
                    }elseif($key == 3){
                        $dataVar['name'] = '待升班班级预警';
                    }elseif($key == 4){
                        $dataVar['name'] = '学员耗课预警';
                    }elseif($key == 5){
                        $dataVar['name'] = '学员自动流失预警';
                    }
                }

                $field = [
                    "id"=>"顺序ID",
                    "number"=>"预警数量",
                    "status"=>"状态（不用）",
                    "name"=>"名称",
                ];
                $result = array();
                $result["field"] = $field;
                $result['list'] =$dataList?$dataList:array();
                if ($dataList) {
                    ajax_return(array('error' => '0', 'errortip' => "信息获取成功", 'result' => $result));
                } else {
                    ajax_return(array('error' => '1', 'errortip' => "信息获取失败", 'result' => array()));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //获取 校首页的 待处理信息
    function getSmcToDoInfoApi(){
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'getSmcToDoInfoApi', $request)) {

                if($request['staffer_id'] == '') {
                    ajax_return(array('error' => '1', 'errortip' => '职工ID不能为空', 'result' => array()));
                }
                if($request['token'] == '') {
                    ajax_return(array('error' => '1', 'errortip' => '职工token不能为空', 'result' => array()));
                }
                if($request['schoolbranch'] == '') {
                    ajax_return(array('error' => '1', 'errortip' => '学校编号不能为空！', 'result' => array()));
                }
                $schooOne = $this->DataControl->selectOne(" select school_id from smc_school where company_id = '8888' and school_branch = '{$request['schoolbranch']}' and school_isclose = '0' ");
                if(!$schooOne){
                    ajax_return(array('error' => '1', 'errortip' => '未找到对应的未关闭学校！', 'result' => array()));
                }

                $data = array();
                $data['staffer_id'] = $request['staffer_id'];
                $data['token'] = $request['token'];
                $data['company_id'] = '8888';
                $data['language_type'] = 'zh';
                $data['school_id'] = $schooOne['school_id'];
                $data['re_postbe_id'] = 0;
                $dataListJson = request_by_curl("https://smcapi.kedingdang.com/Home/toDoInfoApi", dataEncode($data), "GET",array());
                $jaonToData = json_decode($dataListJson, 1);

                if(!$jaonToData['result']){
                    ajax_return(array('error' => '1', 'errortip' => "信息获取失败", 'result' => array()));
                }

                $dataList = $jaonToData['result'];

                foreach ($dataList as $key=>&$dataVar){
                    if($key == 0){
                        $dataVar['name'] = '优惠券审核';
                    }elseif($key == 1){
                        $dataVar['name'] = '申请拆班审核';
                    }elseif($key == 2){
                        $dataVar['name'] = '月度结算审核';
                    }elseif($key == 3){
                        $dataVar['name'] = '退费订单审核';
                    }elseif($key == 4){
                        $dataVar['name'] = '教材待领用';
                    }elseif($key == 5){
                        $dataVar['name'] = '离职老师课时未调整';
                    }
                }

                $field = [
                    "id"=>"顺序ID",
                    "number"=>"待处理数量",
                    "status"=>"状态（不用）",
                    "name"=>"名称",
                ];
                $result = array();
                $result["field"] = $field;
                $result['list'] =$dataList?$dataList:array();
                if ($dataList) {
                    ajax_return(array('error' => '0', 'errortip' => "信息获取成功", 'result' => $result));
                } else {
                    ajax_return(array('error' => '1', 'errortip' => "信息获取失败", 'result' => array()));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //第三方 -- 获取发短信
    function sendMisNoticeApi()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'sendMisNoticeApi', $request)) {

                if(!$request['mobile']){
                    $res = array('error' => '1', 'errortip' => '手机号不能为空!', "bakfuntion" => "errormotify");
                    ajax_return($res);
                }
                //一小时内发送次数
                $mintime = time() - 3600;
                $mislognum = $this->DataControl->selectOne("select count(mislog_id) as mislognum from gmc_mislog where mislog_mobile='{$request['mobile']}' and mislog_tilte = '行销营销' and mislog_time >= '{$mintime}' limit 0,1 ");
                if ($mislognum['mislognum'] > 5) {
                    $res = array('error' => '1', 'errortip' => '您的手机已超出发送验证码次数，请联系客服！');
                    ajax_return($res);
                }

                //最近一次发送时间
                $sendmisrz = $this->DataControl->getFieldOne('gmc_mislog', "mislog_time", "mislog_mobile='{$request['mobile']}' and mislog_tilte = '行销营销'", "order by mislog_time DESC");
                if ($sendmisrz && (time() - $sendmisrz['mislog_time']) < 60) {
                    $res = array('error' => '1', 'errortip' => '通知已发送！');
                    ajax_return($res);
                } else {
                    $tilte = "行销营销";
                    $contxt = $request['contxt'];
                    //短信发送
                    if ($this->Sendmisgo($request['mobile'], $contxt, $tilte, '','8888')) {
                        $res = array('error' => '0', 'errortip' => '发送成功', "bakfuntion" => "okmotify");
                        ajax_return($res);
                    } else {
                        $res = array('error' => '1', 'errortip' => '发送失败!', "bakfuntion" => "errormotify");
                        ajax_return($res);
                    }
                }

            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }



}