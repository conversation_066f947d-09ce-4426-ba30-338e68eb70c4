<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/5/27
 * Time: 11:56
 */

namespace Work\Controller\Api;


use Model\Smc\CheckingModel;
use Model\Smc\CourseModel;

class SmcController extends viewTpl
{
    public $u;
    public $t;
    public $c;
    public $visitType = "api";


    //预加载处理类
    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
        $this->api = $this->router->getApi();
    }

    //获取手机验证码
    function getLoginverifycodeApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $Model = new \Model\Api\SmcModel();
        $pucArray = $Model->ThreeVerify($request);

        if ($pucArray) {
            $companyOne = $this->DataControl->getFieldOne('gmc_company', "company_id,company_language", "company_id = '{$pucArray['company_id']}'");
            if ($companyOne) {

                if ($request['identity'] == 2) {
                    //家长身份
                    $parenterOne = $this->DataControl->getFieldOne("smc_parenter", " parenter_id ", " parenter_mobile = '{$request['L_name']}'");
                    if (!$parenterOne) {
                        ajax_return(array('error' => '1', 'errortip' => '家长账户信息不存在!'), $companyOne['company_language']);
                    }
                } elseif ($request['identity'] == 1) {
                    //教师身份
                    $stafferOne = $this->DataControl->getFieldOne('smc_staffer', "staffer_id", "staffer_mobile = '{$request['L_name']}' and company_id = '{$companyOne['company_id']}'");
                    if (!$stafferOne) {
                        ajax_return(array('error' => '1', 'errortip' => '职工账户信息不存在!'), $companyOne['company_language']);
                    }
                } else {
                    ajax_return(array('error' => 1, 'errortip' => "请选择正确的身份!"), $companyOne['company_language']);
                }

                $mobile = trim($request['L_name']);
                //一小时内发送次数
                $mintime = time() - 3600;
                $mislognum = $this->DataControl->selectOne("select count(mislog_id) as mislognum from gmc_mislog where mislog_mobile='{$mobile}' and mislog_tilte = '网课系统手机快速登录' and mislog_time >= '{$mintime}' limit 0,1 ");
                if ($mislognum['mislognum'] > 5) {
                    ajax_return(array('error' => '1', 'errortip' => '您的手机已超出发送验证码次数，请联系客服！'), $companyOne['company_language']);
                }
                //最近一次发送时间
                $sendmisrz = $this->DataControl->getFieldOne('gmc_mislog', "mislog_time", "mislog_mobile='{$mobile}' and mislog_tilte = '网课系统手机快速登录'", "order by mislog_time DESC");
                if ($sendmisrz && (time() - $sendmisrz['mislog_time']) < 60) {
                    ajax_return(array('error' => '1', 'errortip' => '验证码已发送！'), $companyOne['company_language']);
                } else {
                    $tilte = "网课系统手机快速登录";
                    $sendcode = rand(111111, 999999);
                    setcookie('mislog_sendcode', $sendcode, time() + 1800);
                    $contxt = "用户您好！您的手机验证码为{$sendcode}，请完成验证，如非本人操作，请忽略本短信。";
                    //短信发送
                    if ($this->Sendmisgo($mobile, $contxt, $tilte, $sendcode, $companyOne['company_id'])) {
                        ajax_return(array('error' => '0', 'errortip' => '发送成功', "bakfuntion" => "okmotify"), $companyOne['company_language']);
                    } else {
                        ajax_return(array('error' => '1', 'errortip' => '发送失败!', "bakfuntion" => "errormotify"), $companyOne['company_language']);
                    }
                }

            } else {
                ajax_return(array('error' => 1, 'errortip' => "企业账户不存在，请确认授权码是否正确!"), $companyOne['company_language']);
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $Model->errortip, 'result' => array()));
        }

    }

    function loginApi()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $Model = new \Model\Api\SmcModel();
        $pucArray = $Model->ThreeVerify($request);

        if ($pucArray) {
            $companyOne = $this->DataControl->getFieldOne('gmc_company', "company_id,company_language", "company_id = '{$pucArray['company_id']}'");

            if ($request['identity'] == 1) {
                //教师身份

                if ($companyOne) {

                    $stafferOne = $this->DataControl->getFieldOne('smc_staffer', "staffer_id,staffer_branch,staffer_leave,staffer_img,staffer_sex,staffer_pass,account_class", "(staffer_branch = '{$request['L_name']}' or staffer_mobile = '{$request['L_name']}') and company_id = '{$companyOne['company_id']}'");

                    if ($stafferOne) {
                        if ($stafferOne['staffer_leave'] == '0') {

                            if (isset($request['L_pswd']) && $request['L_pswd'] != '') {
                                $password = md5($request['L_pswd']);
                                if ($password == $stafferOne['staffer_pass']) {
                                    $istaffer = array();
                                    $istaffer['staffer_branch'] = $stafferOne['staffer_branch'];
                                    $istaffer['staffer_img'] = $stafferOne['staffer_img'];
                                    $istaffer['staffer_sex'] = $stafferOne['staffer_sex'];
                                    $istaffer['company_id'] = $companyOne['company_id'];
                                    $istaffer['company_language'] = $companyOne['company_language'];
                                    $istaffer['token'] = $this->getToken($stafferOne);
                                    $this->DataControl->updateData("smc_staffer", "staffer_id = '{$stafferOne['staffer_id']}'", array("staffer_lasttime" => time(), "staffer_lastip" => real_ip()));
                                    ajax_return(array('error' => 0, 'errortip' => "登录成功!", 'result' => $istaffer), $companyOne['company_language']);
                                } else {
                                    ajax_return(array('error' => 1, 'errortip' => "密码错误!"), $companyOne['company_language']);
                                }
                            } elseif (isset($request['L_verifycode']) && $request['L_verifycode'] != '') {

                                $mobile = trim($request['L_name']);
                                $verifycode = trim($request['L_verifycode']);
                                $sendrz = $this->DataControl->getFieldOne('gmc_mislog', "mislog_sendcode", "mislog_mobile='{$mobile}' and mislog_tilte = '网课系统手机快速登录'", "order by mislog_time DESC");
                                if (!$sendrz || $sendrz['mislog_sendcode'] !== $verifycode) {
                                    ajax_return(array('error' => '1', 'errortip' => '短信验证码错误!'), $companyOne['company_language']);
                                } else {
                                    $istaffer = array();
                                    $istaffer['staffer_branch'] = $stafferOne['staffer_branch'];
                                    $istaffer['staffer_img'] = $stafferOne['staffer_img'];
                                    $istaffer['staffer_sex'] = $stafferOne['staffer_sex'];
                                    $istaffer['company_id'] = $companyOne['company_id'];
                                    $istaffer['company_language'] = $companyOne['company_language'];
                                    $istaffer['token'] = $this->getToken($stafferOne);
                                    $this->DataControl->updateData("smc_staffer", "staffer_id = '{$stafferOne['staffer_id']}'", array("staffer_lasttime" => time(), "staffer_lastip" => real_ip()));
                                    ajax_return(array('error' => 0, 'errortip' => "登陆成功!", 'result' => $istaffer), $companyOne['company_language']);
                                }
                            } else {
                                ajax_return(array('error' => 1, 'errortip' => "请确认登录方式!"), $companyOne['company_language']);
                            }

                        } else {
                            ajax_return(array('error' => 1, 'errortip' => "您已离职，账户权限已被关闭!"), $companyOne['company_language']);
                        }
                    } else {
                        ajax_return(array('error' => 1, 'errortip' => "职工账户信息不存在!"), $companyOne['company_language']);
                    }
                } else {
                    ajax_return(array('error' => 1, 'errortip' => "企业账户不存在，请确认授权码是否正确!"), $companyOne['company_language']);
                }
            } elseif ($request['identity'] == 2) {
                //家长身份

                $parenterOne = $this->DataControl->getFieldOne("smc_parenter", " * ", " parenter_mobile = '{$request['L_name']}'");
                if ($parenterOne) {
                    if (isset($request['L_pswd']) && $request['L_pswd'] != '') {
                        $password = md5($request['L_pswd']);
                        if ($parenterOne['parenter_pass'] == '') {
                            $data = array();
                            $data['parenter_pass'] = md5(substr($parenterOne['parenter_mobile'], -6));
                            $data['parenter_bakpass'] = substr($parenterOne['parenter_mobile'], -6);

                            $this->DataControl->updateData("smc_parenter", "parenter_mobile = '{$request['L_name']}'", $data);

                            $parenterOne = $this->DataControl->getFieldOne("smc_parenter", " * ", " parenter_mobile = '{$request['L_name']}'");
                        }

                        if ($password == $parenterOne['parenter_pass']) {
                            $iparenter = array();
                            $iparenter['parenter_id'] = $parenterOne['parenter_id'];
                            $iparenter['parenter_nickname'] = $parenterOne['parenter_nickname'];
                            $iparenter['parenter_img'] = $parenterOne['parenter_img'];
                            $iparenter['parenter_mobile'] = $parenterOne['parenter_mobile'];
                            $iparenter['parenter_cnname'] = $parenterOne['parenter_cnname'];
                            $iparenter['parenter_enname'] = $parenterOne['parenter_enname'];
                            $iparenter['token'] = $this->getParentToken($parenterOne);
                            $this->DataControl->updateData("smc_parenter", "parenter_id = '{$parenterOne['parenter_id']}'", array("parenter_lasttime" => time(), "parenter_lastip" => real_ip()));
                            ajax_return(array('error' => 0, 'errortip' => "登陆成功!", 'result' => $iparenter), $companyOne['company_language']);
                        } else {
                            ajax_return(array('error' => 1, 'errortip' => "密码错误!"), $companyOne['company_language']);
                        }
                    } elseif (isset($request['L_verifycode']) && $request['L_verifycode'] != '') {

                        $mobile = trim($request['L_name']);
                        $verifycode = trim($request['L_verifycode']);
                        $sendrz = $this->DataControl->getFieldOne('gmc_mislog', "mislog_sendcode", "mislog_mobile='{$mobile}' and mislog_tilte = '网课系统手机快速登录'", "order by mislog_time DESC");
                        if (!$sendrz || $sendrz['mislog_sendcode'] !== $verifycode) {
                            ajax_return(array('error' => '1', 'errortip' => '短信验证码错误!'), $companyOne['company_language']);
                        } else {
                            $iparenter = array();
                            $iparenter['parenter_id'] = $parenterOne['parenter_id'];
                            $iparenter['parenter_nickname'] = $parenterOne['parenter_nickname'];
                            $iparenter['parenter_img'] = $parenterOne['parenter_img'];
                            $iparenter['parenter_mobile'] = $parenterOne['parenter_mobile'];
                            $iparenter['parenter_cnname'] = $parenterOne['parenter_cnname'];
                            $iparenter['parenter_enname'] = $parenterOne['parenter_enname'];
                            $iparenter['token'] = $this->getParentToken($parenterOne);
                            $this->DataControl->updateData("smc_parenter", "parenter_id = '{$parenterOne['parenter_id']}'", array("parenter_lasttime" => time(), "parenter_lastip" => real_ip()));
                            ajax_return(array('error' => 0, 'errortip' => "登陆成功!", 'result' => $iparenter), $companyOne['company_language']);
                        }
                    } else {
                        ajax_return(array('error' => 1, 'errortip' => "请确认登录方式!"), $companyOne['company_language']);
                    }
                } else {
                    ajax_return(array('error' => 1, 'errortip' => "家长账户信息不存在!"));
                }
            } else {
                ajax_return(array('error' => 1, 'errortip' => "请选择正确的身份!"));
            }

        } else {
            $res = array('error' => '1', 'errortip' => $Model->errortip, 'result' => array());
            ajax_return($res);
        }
    }

    /*
     * 名称：获取某集团下所有校区省份
     * 入参：集团编号ID
     * 返回：省份名称 省份ID
    */

    function getCompanyProvinceApi()
    {
        $request = Input('get.', '', 'trim,addslashes');

        $Model = new \Model\Api\SmcModel();
        $pucArray = $Model->ThreeVerify($request);

        if ($pucArray) {
            $request['company_id'] = $pucArray['company_id'];
            $dataList = $Model->getCompanyProvince($request);

            if ($dataList) {
                $res = array('error' => '0', 'errortip' => '获取集团下所有校区省份成功', 'result' => $dataList);
                ajax_return($res);
            } else {
                $res = array('error' => '1', 'errortip' => $Model->errortip, 'result' => array());
                ajax_return($res);
            }
        } else {
            $res = array('error' => '1', 'errortip' => $Model->errortip, 'result' => array());
            ajax_return($res);
        }
    }

    /*
     * 名称：获取某集团下所有校区城市
     * 入参：集团编号ID、省份ID
     * 返回：城市名称 城市ID
    */

    function getCompanyCityApi()
    {
        $request = Input('get.', '', 'trim,addslashes');

        $Model = new \Model\Api\SmcModel();
        $pucArray = $Model->ThreeVerify($request);

        if ($pucArray) {
            $request['company_id'] = $pucArray['company_id'];
            $dataList = $Model->getCompanyCity($request);

            if ($dataList) {
                $res = array('error' => '0', 'errortip' => '获取集团下所有校区城市成功', 'result' => $dataList);
                ajax_return($res);
            } else {
                $res = array('error' => '1', 'errortip' => $Model->errortip, 'result' => array());
                ajax_return($res);
            }
        } else {
            $res = array('error' => '1', 'errortip' => $Model->errortip, 'result' => array());
            ajax_return($res);
        }
    }


    /*
     * 名称：获取某集团下所有校区区域
     * 入参：集团编号ID、城市ID
     * 返回：区域名称 区域ID
    */

    function getCompanyAreaApi()
    {
        $request = Input('get.', '', 'trim,addslashes');

        $Model = new \Model\Api\SmcModel();
        $pucArray = $Model->ThreeVerify($request);

        if ($pucArray) {
            $request['company_id'] = $pucArray['company_id'];
            $dataList = $Model->getCompanyArea($request);

            if ($dataList) {
                $res = array('error' => '0', 'errortip' => '获取集团下所有校区城市成功', 'result' => $dataList);
                ajax_return($res);
            } else {
                $res = array('error' => '1', 'errortip' => $Model->errortip, 'result' => array());
                ajax_return($res);
            }
        } else {
            $res = array('error' => '1', 'errortip' => $Model->errortip, 'result' => array());
            ajax_return($res);
        }
    }

    /*
     * 名称：获取某集团下所有校区名称
     * 入参：集团编号ID、省份ID、城市ID、区域ID、是否测试校、是否关闭校、关键词检索
     * 返回：校园编号、校园全称、校园简称
    */

    function getCompanySchoolApi()
    {
        $request = Input('get.', '', 'trim,addslashes');

        $Model = new \Model\Api\SmcModel();
        $pucArray = $Model->ThreeVerify($request);

        if ($pucArray) {
            $request['company_id'] = $pucArray['company_id'];
            $dataList = $Model->getCompanySchool($request);

            if ($dataList) {
                $res = array('error' => '0', 'errortip' => '获取集团下所有校区成功', 'result' => $dataList);
                ajax_return($res);
            } else {
                $res = array('error' => '1', 'errortip' => $Model->errortip, 'result' => array());
                ajax_return($res);
            }
        } else {
            $res = array('error' => '1', 'errortip' => $Model->errortip, 'result' => array());
            ajax_return($res);
        }
    }

    /*
     * 名称：获取某集团下所有班组
     * 入参：集团编号ID、关键词检索
     * 返回：。。。
    */

    function getCompanyCoursetypeApi()
    {
        $request = Input('get.', '', 'trim,addslashes');

        $Model = new \Model\Api\SmcModel();
        $pucArray = $Model->ThreeVerify($request);

        if ($pucArray) {
            $request['company_id'] = $pucArray['company_id'];
            $dataList = $Model->getCompanyCoursetype($request);

            if ($dataList) {
                $res = array('error' => '0', 'errortip' => '获取集团下所有班组成功', 'result' => $dataList);
                ajax_return($res);
            } else {
                $res = array('error' => '1', 'errortip' => $Model->errortip, 'result' => array());
                ajax_return($res);
            }

        } else {
            $res = array('error' => '1', 'errortip' => $Model->errortip, 'result' => array());
            ajax_return($res);
        }
    }

    /*
     * 名称：获取某集团下所有班种
     * 入参：集团编号ID、班组编号、关键词检索
     * 返回：。。。
    */

    function getCompanyCoursecatApi()
    {
        $request = Input('get.', '', 'trim,addslashes');

        $Model = new \Model\Api\SmcModel();
        $pucArray = $Model->ThreeVerify($request);

        if ($pucArray) {
            $request['company_id'] = $pucArray['company_id'];
            $dataList = $Model->getCompanyCoursecat($request);

            if ($dataList) {
                $res = array('error' => '0', 'errortip' => '获取集团下所有班种成功', 'result' => $dataList);
                ajax_return($res);
            } else {
                $res = array('error' => '1', 'errortip' => $Model->errortip, 'result' => array());
                ajax_return($res);
            }

        } else {
            $res = array('error' => '1', 'errortip' => $Model->errortip, 'result' => array());
            ajax_return($res);
        }
    }


    /*
     * 名称：获取某集团下所有班别信息
     * 入参：集团编号ID、班班组编号、班种编号、关键词检索
     * 返回：。。。
    */

    function getCompanyCourseApi()
    {
        $request = Input('get.', '', 'trim,addslashes');

        $Model = new \Model\Api\SmcModel();
        $pucArray = $Model->ThreeVerify($request);

        if ($pucArray) {
            $request['company_id'] = $pucArray['company_id'];
            $dataList = $Model->getCompanyCourse($request);

            if ($dataList) {
                $res = array('error' => '0', 'errortip' => '获取集团下所有班别成功', 'result' => $dataList);
                ajax_return($res);
            } else {
                $res = array('error' => '1', 'errortip' => $Model->errortip, 'result' => array());
                ajax_return($res);
            }
        } else {
            $res = array('error' => '1', 'errortip' => $Model->errortip, 'result' => array());
            ajax_return($res);
        }
    }

    /*
     * 名称：获取某学校下的所有班级信息
     * 入参：集团编号ID、校园编号、开班开始时间、开班结束时间、班级类型：（课程、期度、预约），班种编号、班别编号、是否删除、关键词筛选
     * 返回：班级编号、班级中文名称、班级英文名称、班级班种编号、班级班别名称、班别编号、开班日期、结班日期、课时数
    */

    function getCompanyClassApi()
    {
        $request = Input('get.', '', 'trim,addslashes');

        $Model = new \Model\Api\SmcModel();
        $pucArray = $Model->ThreeVerify($request);

        if ($pucArray) {
            $request['company_id'] = $pucArray['company_id'];
            $dataList = $Model->getCompanyClass($request);

            if ($dataList) {
                $res = array('error' => '0', 'errortip' => '获取集团下所有班级成功', 'result' => $dataList);
                ajax_return($res);
            } else {
                $res = array('error' => '1', 'errortip' => $Model->errortip, 'result' => array());
                ajax_return($res);
            }
        } else {
            $res = array('error' => '1', 'errortip' => $Model->errortip, 'result' => array());
            ajax_return($res);
        }
    }


    /*
     * 名称：获取某班级所有学员信息（含离班学员）
     * 入参：集团编号ID、班级编号
     * 返回：学员中文名。。。。
    */

    function getCompanyClassStuApi()
    {
        $request = Input('get.', '', 'trim,addslashes');

        $Model = new \Model\Api\SmcModel();
        $pucArray = $Model->ThreeVerify($request);

        if ($pucArray) {
            $request['company_id'] = $pucArray['company_id'];
            $dataList = $Model->getCompanyClassStu($request);

            if ($dataList) {
                $res = array('error' => '0', 'errortip' => '获取班级所有学员信息成功', 'result' => $dataList);
                ajax_return($res);
            } else {
                $res = array('error' => '1', 'errortip' => $Model->errortip, 'result' => array());
                ajax_return($res);
            }
        } else {
            $res = array('error' => '1', 'errortip' => $Model->errortip, 'result' => array());
            ajax_return($res);
        }
    }

    /*
     * 名称：获取某班级所有授课教师信息
     * 入参：集团编号ID、班级编号
     * 返回：教师中文名。。。。
    */

    function getCompanyClassTeacherApi()
    {
        $request = Input('get.', '', 'trim,addslashes');

        $Model = new \Model\Api\SmcModel();
        $pucArray = $Model->ThreeVerify($request);

        if ($pucArray) {
            $request['company_id'] = $pucArray['company_id'];
            $dataList = $Model->getCompanyClassTeacher($request);

            if ($dataList) {
                $res = array('error' => '0', 'errortip' => '获取班级授课教师信息成功', 'result' => $dataList);
                ajax_return($res);
            } else {
                $res = array('error' => '1', 'errortip' => $Model->errortip, 'result' => array());
                ajax_return($res);
            }
        } else {
            $res = array('error' => '1', 'errortip' => $Model->errortip, 'result' => array());
            ajax_return($res);
        }
    }

    /*
    * 名称：获取某学校的授课教师信息
    * 入参：集团编号ID、校区编号
    * 返回：教师基本信息
   */

    function getCompanySchoolTeacherApi()
    {
        $request = Input('get.', '', 'trim,addslashes');

        $Model = new \Model\Api\SmcModel();
        $pucArray = $Model->ThreeVerify($request);

        if ($pucArray) {
            $request['company_id'] = $pucArray['company_id'];
            $dataList = $Model->getCompanySchoolTeacher($request);

            if ($dataList) {
                $res = array('error' => '0', 'errortip' => '获取学校教师信息成功', 'result' => $dataList);
                ajax_return($res);
            } else {
                $res = array('error' => '1', 'errortip' => $Model->errortip, 'result' => array());
                ajax_return($res);
            }
        } else {
            $res = array('error' => '1', 'errortip' => $Model->errortip, 'result' => array());
            ajax_return($res);
        }
    }

    /*
    * 名称：获取某教师的代班班级信息
    * 入参：集团编号ID、教师编号
    * 返回：班级编号,班级中文名,班级英文名
   */
    function getCompanyTeacherClassApi()
    {
        $request = Input('get.', '', 'trim,addslashes');

        $Model = new \Model\Api\SmcModel();
        $pucArray = $Model->ThreeVerify($request);

        if ($pucArray) {
            $request['company_id'] = $pucArray['company_id'];
            $dataList = $Model->getCompanyTeacherClass($request);

            if ($dataList) {
                $res = array('error' => '0', 'errortip' => '获取教师带班信息成功', 'result' => $dataList);
                ajax_return($res);
            } else {
                $res = array('error' => '1', 'errortip' => $Model->errortip, 'result' => array());
                ajax_return($res);
            }
        } else {
            $res = array('error' => '1', 'errortip' => $Model->errortip, 'result' => array());
            ajax_return($res);
        }
    }

    /*
     * 名称：获取某教师的个人信息
     * 入参：集团编号ID、教师编号
     * 返回：教师个人信息
    */

    function getStafferOneApi()
    {
        $request = Input('get.', '', 'trim,addslashes');

        $Model = new \Model\Api\SmcModel();
        $pucArray = $Model->ThreeVerify($request);

        if ($pucArray) {
            $request['company_id'] = $pucArray['company_id'];
            $dataList = $Model->getStafferOne($request);

            if ($dataList) {
                $res = array('error' => '0', 'errortip' => '获取身份信息成功', 'result' => $dataList);
                ajax_return($res);
            } else {
                $res = array('error' => '1', 'errortip' => $Model->errortip, 'result' => array());
                ajax_return($res);
            }
        } else {
            $res = array('error' => '1', 'errortip' => $Model->errortip, 'result' => array());
            ajax_return($res);
        }
    }

    /*
     * 名称：获取某教师的排课信息
     * 入参：集团编号ID、教师编号、班种编号、班别编号、班级编号、上课日期(时间段)
     * 返回：课次,日期,考勤信息
    */

    function getCompanyTeacherHourApi()
    {
        $request = Input('get.', '', 'trim,addslashes');

        $Model = new \Model\Api\SmcModel();
        $pucArray = $Model->ThreeVerify($request);

        if ($pucArray) {
            $request['company_id'] = $pucArray['company_id'];
            $dataList = $Model->getCompanyTeacherHour($request);

            if ($dataList) {
                $res = array('error' => '0', 'errortip' => '获取教师带班信息成功', 'result' => $dataList);
                ajax_return($res);
            } else {
                $res = array('error' => '1', 'errortip' => $Model->errortip, 'result' => array());
                ajax_return($res);
            }
        } else {
            $res = array('error' => '1', 'errortip' => $Model->errortip, 'result' => array());
            ajax_return($res);
        }
    }

    /*
    * 名称：获取某班级的排课信息
    * 入参：集团编号ID、班级编号
    * 返回：课次,日期,教室,云教室,考勤信息,代课教师
   */

    function getCompanyClassHourApi()
    {
        $request = Input('get.', '', 'trim,addslashes');

        $Model = new \Model\Api\SmcModel();
        $pucArray = $Model->ThreeVerify($request);

        if ($pucArray) {
            $request['company_id'] = $pucArray['company_id'];
            $dataList = $Model->getCompanyClassHour($request);

            if ($dataList) {
                $res = array('error' => '0', 'errortip' => '获取班级的排课信息成功', 'result' => $dataList);
                ajax_return($res);
            } else {
                $res = array('error' => '1', 'errortip' => $Model->errortip, 'result' => array());
                ajax_return($res);
            }
        } else {
            $res = array('error' => '1', 'errortip' => $Model->errortip, 'result' => array());
            ajax_return($res);
        }
    }

    /*
     * 名称：获取某学员的班级信息
     * 入参：集团编号ID、学员编号、校区编号
     * 返回：校区编号,班级编号,入班时间,离班时间,在班状态
    */

    function getCompanyStuClassApi()
    {
        $request = Input('get.', '', 'trim,addslashes');

        $Model = new \Model\Api\SmcModel();
        $pucArray = $Model->ThreeVerify($request);

        if ($pucArray) {
            $request['company_id'] = $pucArray['company_id'];
            $dataList = $Model->getCompanyStuClass($request);

            if ($dataList) {
                $res = array('error' => '0', 'errortip' => '获取学员的班级信息成功', 'result' => $dataList);
                ajax_return($res);
            } else {
                $res = array('error' => '1', 'errortip' => $Model->errortip, 'result' => array());
                ajax_return($res);
            }
        } else {
            $res = array('error' => '1', 'errortip' => $Model->errortip, 'result' => array());
            ajax_return($res);
        }
    }

    /*
     * 名称：获取学生入校关系信息
     * 入参：集团编号ID、学生编号
     * 返回：学员入校基本信息
    */

    function getStuEnrollApi()
    {
        $request = Input('get.', '', 'trim,addslashes');

        $Model = new \Model\Api\SmcModel();
        $pucArray = $Model->ThreeVerify($request);

        if ($pucArray) {
            $request['company_id'] = $pucArray['company_id'];
            $dataList = $Model->getStuEnroll($request);

            if ($dataList) {
                $res = array('error' => '0', 'errortip' => '获取学生入校关系信息成功', 'result' => $dataList);
                ajax_return($res);
            } else {
                $res = array('error' => '1', 'errortip' => $Model->errortip, 'result' => array());
                ajax_return($res);
            }
        } else {
            $res = array('error' => '1', 'errortip' => $Model->errortip, 'result' => array());
            ajax_return($res);
        }
    }

    /*
     * 名称：获取某学员的排课信息
     * 入参：集团编号ID、学员编号、班级编号、上课日期(时间段)
     * 返回：课次,日期,代课教师,教室,云教室,考勤信息
    */

    function getCourseListApi()
    {
        $request = Input('get.', '', 'trim,addslashes');

        $Model = new \Model\Api\SmcModel();
        $pucArray = $Model->ThreeVerify($request);

        if ($pucArray) {
            $request['company_id'] = $pucArray['company_id'];
            $dataList = $Model->getCourseList($request);

            if ($dataList) {
                $res = array('error' => '0', 'errortip' => '获取课程别信息成功', 'result' => $dataList);
                ajax_return($res);
            } else {
                $res = array('error' => '1', 'errortip' => $Model->errortip, 'result' => array());
                ajax_return($res);
            }
        } else {
            $res = array('error' => '1', 'errortip' => $Model->errortip, 'result' => array());
            ajax_return($res);
        }
    }

    /*
     * 名称：获取某学员的排课信息
     * 入参：集团编号ID、学员编号、班级编号、上课日期(时间段)
     * 返回：课次,日期,代课教师,教室,云教室,考勤信息
    */

    function getCompanyStuHourApi()
    {
        $request = Input('get.', '', 'trim,addslashes');

        $Model = new \Model\Api\SmcModel();
        $pucArray = $Model->ThreeVerify($request);

        if ($pucArray) {
            $request['company_id'] = $pucArray['company_id'];
            $dataList = $Model->getCompanyStuHour($request);

            if ($dataList) {
                $res = array('error' => '0', 'errortip' => '获取学员的排课信息成功', 'result' => $dataList);
                ajax_return($res);
            } else {
                $res = array('error' => '1', 'errortip' => $Model->errortip, 'result' => array());
                ajax_return($res);
            }
        } else {
            $res = array('error' => '1', 'errortip' => $Model->errortip, 'result' => array());
            ajax_return($res);
        }
    }

    /*
     * 名称：获取某学员的学生信息
     * 入参：集团编号ID、学员编号
     * 返回：家长基本信息
    */

    function getCompanyStuInfoApi()
    {
        $request = Input('get.', '', 'trim,addslashes');

        $Model = new \Model\Api\SmcModel();
        $pucArray = $Model->ThreeVerify($request);

        if ($pucArray) {
            $request['company_id'] = $pucArray['company_id'];
            $dataList = $Model->getCompanyStuInfo($request);

            if ($dataList) {
                $res = array('error' => '0', 'errortip' => '获取学员的信息成功', 'result' => $dataList);
                ajax_return($res);
            } else {
                $res = array('error' => '1', 'errortip' => $Model->errortip, 'result' => array());
                ajax_return($res);
            }
        } else {
            $res = array('error' => '1', 'errortip' => $Model->errortip, 'result' => array());
            ajax_return($res);
        }
    }

    /*
     * 名称：获取某学员的家长信息
     * 入参：集团编号ID、学员编号
     * 返回：家长基本信息
    */

    function getCompanyStuParentApi()
    {
        $request = Input('get.', '', 'trim,addslashes');

        $Model = new \Model\Api\SmcModel();
        $pucArray = $Model->ThreeVerify($request);

        if ($pucArray) {
            $request['company_id'] = $pucArray['company_id'];
            $dataList = $Model->getCompanyStuParent($request);

            if ($dataList) {
                $res = array('error' => '0', 'errortip' => '获取学员的家长信息成功', 'result' => $dataList);
                ajax_return($res);
            } else {
                $res = array('error' => '1', 'errortip' => $Model->errortip, 'result' => array());
                ajax_return($res);
            }
        } else {
            $res = array('error' => '1', 'errortip' => $Model->errortip, 'result' => array());
            ajax_return($res);
        }
    }

    /*
    * 名称：获取某家长的学员信息
    * 入参：集团编号ID、手机号
    * 返回：学员基本信息
   */

    function getCompanyParentStuApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $Model = new \Model\Api\SmcModel();
        $pucArray = $Model->ThreeVerify($request);
        if ($pucArray) {
            $request['company_id'] = $pucArray['company_id'];
            $dataList = $Model->getCompanyParentStu($request);

            if ($dataList) {
                $res = array('error' => '0', 'errortip' => '获取家长的学员信息成功', 'result' => $dataList);
                ajax_return($res);
            } else {
                $res = array('error' => '1', 'errortip' => $Model->errortip, 'result' => array());
                ajax_return($res);
            }
        } else {
            $res = array('error' => '1', 'errortip' => $Model->errortip, 'result' => array());
            ajax_return($res);
        }
    }

    /*
   * 名称：获取某集团简介信息
   * 入参：
   * 返回：集团简介信息
  */

    function getCompanyBriefApi()
    {
        $request = Input('get.', '', 'trim,addslashes');

        $Model = new \Model\Api\SmcModel();
        $pucArray = $Model->ThreeVerify($request);

        if ($pucArray) {
            $request['company_id'] = $pucArray['company_id'];
            $dataList = $Model->getCompanyBrief($request);

            if ($dataList) {
                $res = array('error' => '0', 'errortip' => '获取集团简介信息成功', 'result' => $dataList);
                ajax_return($res);
            } else {
                $res = array('error' => '1', 'errortip' => $Model->errortip, 'result' => array());
                ajax_return($res);
            }
        } else {
            $res = array('error' => '1', 'errortip' => $Model->errortip, 'result' => array());
            ajax_return($res);
        }
    }

    /*
   * 名称：获取某学校的教室信息
   * 入参：校区编号
   * 返回：教室基本信息
  */

    function getSchoolClassroomApi()
    {
        $request = Input('get.', '', 'trim,addslashes');

        $Model = new \Model\Api\SmcModel();
        $pucArray = $Model->ThreeVerify($request);

        if ($pucArray) {
            $request['company_id'] = $pucArray['company_id'];
            $dataList = $Model->getSchoolClassroom($request);

            if ($dataList) {
                $res = array('error' => '0', 'errortip' => '获取学校的教室信息成功', 'result' => $dataList);
                ajax_return($res);
            } else {
                $res = array('error' => '1', 'errortip' => $Model->errortip, 'result' => array());
                ajax_return($res);
            }
        } else {
            $res = array('error' => '1', 'errortip' => $Model->errortip, 'result' => array());
            ajax_return($res);
        }
    }

    /*
   * 名称：获取某学校的教师信息
   * 入参：校区编号
   * 返回：教师基本信息
  */

    function getSchoolTeacherApi()
    {
        $request = Input('get.', '', 'trim,addslashes');

        $Model = new \Model\Api\SmcModel();
        $pucArray = $Model->ThreeVerify($request);

        if ($pucArray) {
            $request['company_id'] = $pucArray['company_id'];
            $dataList = $Model->getSchoolTeacher($request);

            if ($dataList) {
                $res = array('error' => '0', 'errortip' => '获取学校的教师信息成功', 'result' => $dataList);
                ajax_return($res);
            } else {
                $res = array('error' => '1', 'errortip' => $Model->errortip, 'result' => array());
                ajax_return($res);
            }
        } else {
            $res = array('error' => '1', 'errortip' => $Model->errortip, 'result' => array());
            ajax_return($res);
        }
    }

    function getStuCalendarApi()
    {
        $request = Input('get.', '', 'trim,addslashes');

        $Model = new \Model\Api\SmcModel();
        $pucArray = $Model->ThreeVerify($request);

        if ($pucArray) {
            $request['company_id'] = $pucArray['company_id'];
            $dataList = $Model->getStuCalendar($request);

            if ($dataList) {
                $res = array('error' => '0', 'errortip' => '获取信息成功', 'result' => $dataList);
                ajax_return($res);
            } else {
                $res = array('error' => '1', 'errortip' => $Model->errortip, 'result' => array());
                ajax_return($res);
            }
        } else {
            $res = array('error' => '1', 'errortip' => $Model->errortip, 'result' => array());
            ajax_return($res);
        }
    }

    function getStafferCalendarApi()
    {
        $request = Input('get.', '', 'trim,addslashes');

        $Model = new \Model\Api\SmcModel();
        $pucArray = $Model->ThreeVerify($request);

        if ($pucArray) {
            $request['company_id'] = $pucArray['company_id'];
            $dataList = $Model->getStafferCalendar($request);

            if ($dataList) {
                $res = array('error' => '0', 'errortip' => '获取信息成功', 'result' => $dataList);
                ajax_return($res);
            } else {
                $res = array('error' => '1', 'errortip' => $Model->errortip, 'result' => array());
                ajax_return($res);
            }
        } else {
            $res = array('error' => '1', 'errortip' => $Model->errortip, 'result' => array());
            ajax_return($res);
        }
    }

    //获取修改密码手机验证码
    function getChangeverifycodeApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $Model = new \Model\Api\SmcModel();
        $pucArray = $Model->ThreeVerify($request);

        if ($pucArray) {
            $companyOne = $this->DataControl->getFieldOne('gmc_company', "company_id,company_language", "company_id = '{$pucArray['company_id']}'");
            if ($companyOne) {

                if ($request['identity'] == 2) {
                    //家长身份
                    $parenterOne = $this->DataControl->getFieldOne("smc_parenter", " parenter_id ", " parenter_mobile = '{$request['L_name']}'");
                    if (!$parenterOne) {
                        ajax_return(array('error' => '1', 'errortip' => '家长账户信息不存在!'), $companyOne['company_language']);
                    }
                } elseif ($request['identity'] == 1) {
                    //教师身份
                    $stafferOne = $this->DataControl->getFieldOne('smc_staffer', "staffer_id", "staffer_mobile = '{$request['L_name']}' and company_id = '{$companyOne['company_id']}'");
                    if (!$stafferOne) {
                        ajax_return(array('error' => '1', 'errortip' => '职工账户信息不存在!'), $companyOne['company_language']);
                    }
                } else {
                    ajax_return(array('error' => 1, 'errortip' => "请选择正确的身份!"), $companyOne['company_language']);
                }

                $mobile = trim($request['L_name']);
                //一小时内发送次数
//                $mintime = time() - 3600;
//                $mislognum = $this->DataControl->selectOne("select count(mislog_id) as mislognum from gmc_mislog where mislog_mobile='{$mobile}' and mislog_tilte = '网课系统修改密码' and mislog_time >= '{$mintime}' limit 0,1 ");
//                if ($mislognum['mislognum'] > 5) {
//                    ajax_return(array('error' => '1', 'errortip' => '您的手机已超出发送验证码次数，请联系客服！'),$companyOne['company_language']);
//                }
                //最近一次发送时间
                $sendmisrz = $this->DataControl->getFieldOne('gmc_mislog', "mislog_time", "mislog_mobile='{$mobile}' and mislog_tilte = '网课系统修改密码'", "order by mislog_time DESC");
                if ($sendmisrz && (time() - $sendmisrz['mislog_time']) < 60) {
                    ajax_return(array('error' => '1', 'errortip' => '验证码已发送！'), $companyOne['company_language']);
                } else {
                    $tilte = "网课系统修改密码";
                    $sendcode = rand(111111, 999999);
                    setcookie('mislog_sendcode', $sendcode, time() + 1800);
                    $contxt = "用户您好！您的手机验证码为{$sendcode}，请完成验证，如非本人操作，请忽略本短信。";
                    //短信发送
                    if ($this->Sendmisgo($mobile, $contxt, $tilte, $sendcode, $companyOne['company_id'])) {
                        ajax_return(array('error' => '0', 'errortip' => '发送成功', "bakfuntion" => "okmotify"), $companyOne['company_language']);
                    } else {
                        ajax_return(array('error' => '1', 'errortip' => '发送失败!', "bakfuntion" => "errormotify"), $companyOne['company_language']);
                    }
                }

            } else {
                ajax_return(array('error' => 1, 'errortip' => "企业账户不存在，请确认授权码是否正确!"), $companyOne['company_language']);
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $Model->errortip, 'result' => array()));
        }
    }

    /*
   * 名称：获取某学校的教师信息
   * 入参：校区编号
   * 返回：教师基本信息
  */

    function changePwdApi()
    {
        $request = Input('post.', '', 'trim,addslashes');

        $Model = new \Model\Api\SmcModel();
        $pucArray = $Model->ThreeVerify($request);

        if ($pucArray) {
            $request['company_id'] = $pucArray['company_id'];
            $dataList = $Model->changePwd($request);

            if ($dataList) {
                $res = array('error' => '0', 'errortip' => '修改密码成功', 'result' => $dataList);
                ajax_return($res);
            } else {
                $res = array('error' => '1', 'errortip' => $Model->errortip, 'result' => array());
                ajax_return($res);
            }
        } else {
            $res = array('error' => '1', 'errortip' => $Model->errortip, 'result' => array());
            ajax_return($res);
        }
    }


    /*
   * 名称：获取某学校的教师信息
   * 入参：校区编号
   * 返回：教师基本信息
  */

    function verifychangePwdApi()
    {
        $request = Input('post.', '', 'trim,addslashes');

        $Model = new \Model\Api\SmcModel();
        $pucArray = $Model->ThreeVerify($request);

        if ($pucArray) {
            $request['company_id'] = $pucArray['company_id'];
            $dataList = $Model->verifychangePwd($request);

            if ($dataList) {
                $res = array('error' => '0', 'errortip' => '修改密码成功', 'result' => $dataList);
                ajax_return($res);
            } else {
                $res = array('error' => '1', 'errortip' => $Model->errortip, 'result' => array());
                ajax_return($res);
            }
        } else {
            $res = array('error' => '1', 'errortip' => $Model->errortip, 'result' => array());
            ajax_return($res);
        }
    }

    function getCRMStuApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $Model = new \Model\Api\SmcModel();
        $pucArray = $Model->ThreeVerify($request);
        if ($pucArray) {
            $request['company_id'] = $pucArray['company_id'];
            $res = $Model->getCRMStu($request);
            $field = array();

            $k = 0;
            $field[$k]["fieldstring"] = "client_id";
            $field[$k]["fieldname"] = "客户学员ID";
            $field[$k]["show"] = 0;
            $field[$k]["custom"] = 0;
            $k++;

            $field[$k]["fieldstring"] = "school_cnname";
            $field[$k]["fieldname"] = "校区名称";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "client_cnname";
            $field[$k]["fieldname"] = "学员中文名";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "client_sex";
            $field[$k]["fieldname"] = "性别";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "parenter_cnname";
            $field[$k]["fieldname"] = "主要联系人";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "parenter_mobile_name";
            $field[$k]["fieldname"] = "手机号码";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "client_intention_level";
            $field[$k]["fieldname"] = "意向星级";
            $field[$k]["show"] = 0;
            $field[$k]["custom"] = 0;
            $field[$k]["isLevel"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "course_cnname";
            $field[$k]["fieldname"] = "意向课程";
            $field[$k]["show"] = 0;
            $field[$k]["custom"] = 0;
            $k++;

            $field[$k]["fieldstring"] = "client_icard";
            $field[$k]["fieldname"] = "身份证";
            $field[$k]["show"] = 0;
            $field[$k]["custom"] = 0;
            $k++;

            $field[$k]["fieldstring"] = "client_img";
            $field[$k]["fieldname"] = "学员头像";
            $field[$k]["show"] = 0;
            $field[$k]["custom"] = 0;
            $k++;

            $field[$k]["fieldstring"] = "family_relation";
            $field[$k]["fieldname"] = "关系";
            $field[$k]["show"] = 0;
            $field[$k]["custom"] = 0;
            $k++;

            $field[$k]["fieldstring"] = "client_enname";
            $field[$k]["fieldname"] = "学员英文名称";
            $field[$k]["show"] = 0;
            $field[$k]["custom"] = 0;
            $k++;

            $field[$k]["fieldstring"] = "parenter_mobile";
            $field[$k]["fieldname"] = "手机号码";
            $field[$k]["show"] = 0;
            $field[$k]["custom"] = 0;
            $k++;


            $result = array();
            $result["field"] = $field;
            $result["allnum"] = $res['allnum'];
            if ($res) {
                $result["fieldcustom"] = 1;
                $result["list"] = $res['list'];
                $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
            } else {
                $result["list"] = array();
                $result["allnum"] = 0;
                $res = array('error' => 1, 'errortip' => '暂无CRM学员信息', 'result' => $result);

            }
            ajax_return($res, $request['language_type']);
        } else {
            $res = array('error' => '1', 'errortip' => $Model->errortip, 'result' => array());
            ajax_return($res);
        }

    }

    function checkCanEntryClassApi()
    {
        $request = Input('get.', '', 'trim,addslashes');

        $Model = new \Model\Api\SmcModel();
        $pucArray = $Model->ThreeVerify($request);

        if ($pucArray) {
            $request['company_id'] = $pucArray['company_id'];
            $dataList = $Model->checkCanEntryClass($request);

            if ($dataList) {
                $res = array('error' => '0', 'errortip' => '获取信息', 'result' => $dataList);
                ajax_return($res);
            } else {
                $res = array('error' => '1', 'errortip' => $Model->errortip, 'result' => array());
                ajax_return($res);
            }
        } else {
            $res = array('error' => '1', 'errortip' => $Model->errortip, 'result' => array());
            ajax_return($res);
        }
    }

    function addCRMStuAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $Model = new \Model\Api\SmcModel();
        $pucArray = $Model->ThreeVerify($request);
        if ($pucArray) {
            $request['company_id'] = $pucArray['company_id'];
            $res = $Model->addNewStudent($request);

            $result = array();
            if ($res) {
                $result["list"] = $res;
                $res = array('error' => 0, 'errortip' => '新增成功', 'result' => $result);
            } else {
                $result["list"] = array();
                $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
            }
            ajax_return($res, $request['language_type']);
        } else {
            $res = array('error' => '1', 'errortip' => $Model->errortip, 'result' => array());
            ajax_return($res);
        }

    }

    //获取网课 教室号下的录制数据
    function getWkSerialVideoApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $Model = new \Model\Api\SmcModel();
        $pucArray = $Model->ThreeVerify($request);
        if ($pucArray) {
            $request['company_id'] = $pucArray['company_id'];

            $companyOne = $this->DataControl->getFieldOne("gmc_company", "company_id,company_tkyapikey,company_tkyapino", "company_id = '{$request['company_id']}'");
            if(!$companyOne['company_tkyapikey'] || !$companyOne['company_tkyapino']){
                $res = array('error' => '1', 'errortip' => '企业信息数据有误，请联系老师！', 'result' => array());
                ajax_return($res);
            }

            $data = array();
            $data['key'] = $companyOne['company_tkyapikey'];
            $data['serial'] = $request['serial'];
            $data['recordtype'] = 1;//返回录制件类型(0:常规，1:mp4 8:合并mp4)，默认只返回常规

            $getBackurl = request_by_curl("https://global.talk-cloud.net/WebAPI/getrecordlist", dataEncode($data), "POST");
            $bakData = json_decode($getBackurl, true);

            if ($bakData['result'] == '0') {
                $res = array('error' => '0', 'errortip' => '视频数据获取成功', 'result' => $bakData['recordlist']);
                ajax_return($res);
            } else {
                $res = array('error' => '1', 'errortip' => '视频数据获取失败', 'result' => array());
                ajax_return($res);
            }
        } else {
            $res = array('error' => '1', 'errortip' => $Model->errortip, 'result' => array());
            ajax_return($res);
        }
    }

    //==============================================================================================================================================================
    //小书检核接口

    //账户密码登陆
    function pswdloginApi()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $Model = new \Model\Api\SmcModel();
        $pucArray = $Model->ThreeVerify($request);

        if ($pucArray) {

            if (!$this->DataControl->getFieldOne("smc_school", "school_id", "school_branch='{$request['L_code']}'")) {
                ajax_return(array('error' => 1, 'errortip' => "您输入的校区编号有误，请核实校区编号!"), $request['language_type']);
            }


            $sql = "select st.staffer_id,st.staffer_leave,sc.company_id,sp.postbe_status,st.staffer_pass,co.company_language,sc.school_id,st.staffer_branch
              from smc_staffer as st
              inner join gmc_staffer_postbe as sp on sp.staffer_id=st.staffer_id
              inner join smc_school as sc on sc.school_id=sp.school_id
              inner join gmc_company as co on co.company_id=sc.company_id
              where sc.school_branch='{$request['L_code']}' and (st.staffer_mobile='{$request['L_name']}' or st.staffer_branch='{$request['L_name']}')
              ";
            $stafferOne = $this->DataControl->selectOne($sql);

            if ($stafferOne) {
                if ($stafferOne['staffer_leave'] == '0') {
                    $password = md5($request['L_pswd']);
                    if ($password == $stafferOne['staffer_pass']) {
                        $istaffer = array();
                        $istaffer['staffer_branch'] = $stafferOne['staffer_branch'];
                        $istaffer['company_id'] = $stafferOne['company_id'];
                        $istaffer['school_id'] = $stafferOne['school_id'];
                        $istaffer['school_branch'] = $request['L_code'];
                        $istaffer['company_language'] = $stafferOne['company_language'];
                        $istaffer['token'] = $this->getToken($stafferOne);
                        $this->DataControl->updateData("smc_staffer", "staffer_id = '{$stafferOne['staffer_id']}'", array("staffer_lasttime" => time(), "staffer_lastip" => real_ip()));

                        ajax_return(array('error' => 0, 'errortip' => "登陆成功!", 'result' => $istaffer), $request['language_type']);
                    } else {
                        ajax_return(array('error' => 1, 'errortip' => "密码错误!"), $request['language_type']);
                    }
                } else {
                    ajax_return(array('error' => 1, 'errortip' => "您已离职，账户权限已被关闭!"), $request['language_type']);
                }
            } else {
                ajax_return(array('error' => 1, 'errortip' => "未在校务系统查询到您的账号，请联系您的行政教师咨询哦~!"), $request['language_type']);
            }
        } else {
            $res = array('error' => '1', 'errortip' => $Model->errortip, 'result' => array());
            ajax_return($res, $request['language_type']);
        }
    }

    //手机快速登录
    function mobileloginApi()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $Model = new \Model\Api\SmcModel();
        $pucArray = $Model->ThreeVerify($request);

        if (!isset($request['L_code']) || $request['L_code'] == '') {
            $res = array('error' => '1', 'errortip' => '请输入校区编号!');
            ajax_return($res, $request['language_type']);
        }

        if (!isset($request['L_name']) || $request['L_name'] == '') {
            $res = array('error' => '1', 'errortip' => '请输入手机号!');
            ajax_return($res, $request['language_type']);
        }

        if ($pucArray) {

            if (!$this->DataControl->getFieldOne("smc_school", "school_id", "school_branch='{$request['L_code']}'")) {
                ajax_return(array('error' => 1, 'errortip' => "您输入的校区编号有误，请核实校区编号!"), $request['language_type']);
            }

            $sql = "select st.staffer_id,st.staffer_leave,sc.company_id,sp.postbe_status,st.staffer_pass,co.company_language,sc.school_id,st.staffer_branch
              from smc_staffer as st
              inner join gmc_staffer_postbe as sp on sp.staffer_id=st.staffer_id
              inner join smc_school as sc on sc.school_id=sp.school_id
              inner join gmc_company as co on co.company_id=sc.company_id
              where sc.school_branch='{$request['L_code']}' and (st.staffer_mobile='{$request['L_name']}' or st.staffer_branch='{$request['L_name']}')
              ";
            $stafferOne = $this->DataControl->selectOne($sql);

            if ($stafferOne) {
                if ($stafferOne['staffer_leave'] == '0') {
                    $mobile = trim($request['L_name']);
                    $verifycode = trim($request['L_verifycode']);
                    $sendrz = $this->DataControl->getFieldOne('gmc_mislog', "mislog_sendcode", "mislog_mobile='{$mobile}' and mislog_tilte = '快速登录'", "order by mislog_time DESC");
                    if (!$sendrz || $sendrz['mislog_sendcode'] !== $verifycode) {
                        $res = array('error' => '1', 'errortip' => '短信验证码错误!');
                        ajax_return($res);
                    } else {
                        $istaffer = array();
                        $istaffer['staffer_branch'] = $stafferOne['staffer_branch'];
                        $istaffer['company_id'] = $stafferOne['company_id'];
                        $istaffer['school_id'] = $stafferOne['school_id'];
                        $istaffer['school_branch'] = $request['L_code'];
                        $istaffer['company_language'] = $stafferOne['company_language'];
                        $istaffer['token'] = $this->getToken($stafferOne);
                        $this->DataControl->updateData("smc_staffer", "staffer_id = '{$stafferOne['staffer_id']}'", array("staffer_lasttime" => time(), "staffer_lastip" => real_ip()));
                        ajax_return(array('error' => 0, 'errortip' => "登陆成功!", 'result' => $istaffer), $request['language_type']);
                    }
                } else {
                    ajax_return(array('error' => 1, 'errortip' => "您已离职，账户权限已被关闭!"), $request['language_type']);
                }
            } else {
                ajax_return(array('error' => 1, 'errortip' => "未在校务系统查询到您的账号，请联系您的行政教师咨询哦~"), $request['language_type']);
            }
        } else {
            $res = array('error' => '1', 'errortip' => $Model->errortip, 'result' => array());
            ajax_return($res, $request['language_type']);
        }

    }

    //获取手机验证码
    function getverifycodeApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $Model = new \Model\Api\SmcModel();
        $pucArray = $Model->ThreeVerify($request);
        if ($pucArray) {
            if (!isset($request['L_code']) || $request['L_code'] == '') {
                $res = array('error' => '1', 'errortip' => '请填写校区编号!');
                ajax_return($res, $request['language_type']);
            }

            if (!isset($request['L_name']) || $request['L_name'] == '') {
                $res = array('error' => '1', 'errortip' => '请填写手机号!');
                ajax_return($res, $request['language_type']);
            }

            $sql = "select st.staffer_id,st.staffer_leave,sc.company_id,sp.postbe_status,st.staffer_pass,co.company_language
              from smc_staffer as st
              inner join gmc_staffer_postbe as sp on sp.staffer_id=st.staffer_id
              inner join smc_school as sc on sc.school_id=sp.school_id
              inner join gmc_company as co on co.company_id=sc.company_id
              where sc.school_branch='{$request['L_code']}' and st.staffer_mobile='{$request['L_name']}'
              ";
            $stafferOne = $this->DataControl->selectOne($sql);
            if (!$stafferOne) {
                $res = array('error' => '1', 'errortip' => '职工账户信息不存在!');
                ajax_return($res, $request['language_type']);
            } else {
                $mobile = trim($request['L_name']);
                //一小时内发送次数
                $mintime = time() - 3600;
                $mislognum = $this->DataControl->selectOne("select count(mislog_id) as mislognum from gmc_mislog where mislog_mobile='{$mobile}' and mislog_tilte = '快速登录' and mislog_time >= '{$mintime}' and company_id='{$stafferOne['company_id']}' limit 0,1 ");
                if ($mislognum['mislognum'] > 5) {
                    $res = array('error' => '1', 'errortip' => '您的手机已超出发送验证码次数，请联系客服！');
                    ajax_return($res, $request['language_type']);
                }
                //最近一次发送时间
                $sendmisrz = $this->DataControl->getFieldOne('gmc_mislog', "mislog_time", "mislog_mobile='{$mobile}' and mislog_tilte = '快速登录'", "order by mislog_time DESC");
                if ($sendmisrz && (time() - $sendmisrz['mislog_time']) < 60) {
                    $res = array('error' => '1', 'errortip' => '验证码已发送！');
                    ajax_return($res, $request['language_type']);
                } else {
                    $tilte = "快速登录";
                    $sendcode = rand(111111, 999999);
                    setcookie('mislog_sendcode', $sendcode, time() + 1800);
                    $contxt = "用户您好！您的手机验证码为{$sendcode}，请完成验证，如非本人操作，请忽略本短信。";
                    //短信发送
                    if ($this->Sendmisgo($mobile, $contxt, $tilte, $sendcode, $stafferOne['company_id'])) {
                        $res = array('error' => '0', 'errortip' => '发送成功', "bakfuntion" => "okmotify");
                        ajax_return($res, $request['language_type']);
                    } else {
                        $res = array('error' => '1', 'errortip' => '发送失败!', "bakfuntion" => "errormotify");
                        ajax_return($res, $request['language_type']);
                    }
                }
            }
        } else {
            $res = array('error' => '1', 'errortip' => $Model->errortip, 'result' => array());
            ajax_return($res, $request['language_type']);
        }

    }

    /*
     * 名称：获取某学校下所有时间段内有课的班级
     * 入参：校区编号
     * 返回：班级相关信息
    */

    function getSchoolClassHourApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $Model = new \Model\Api\SmcModel();
        $pucArray = $Model->ThreeVerify($request);
        if ($pucArray) {
            $dataList = $Model->getSchoolClassHour($request);

            if ($dataList) {
                $res = array('error' => '0', 'errortip' => '获取班级课时信息成功', 'result' => $dataList);
                ajax_return($res, $request['language_type']);
            } else {
                $res = array('error' => '1', 'errortip' => $Model->errortip, 'result' => array());
                ajax_return($res, $request['language_type']);
            }
        } else {
            $res = array('error' => '1', 'errortip' => $Model->errortip, 'result' => array());
            ajax_return($res, $request['language_type']);
        }
    }

    /*
     * 名称：获取某学校下某个时间点有课的学员
     * 入参：校区编号
     * 返回：学员相关信息
    */

    function getSchoolStuHourApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $Model = new \Model\Api\SmcModel();
        $pucArray = $Model->ThreeVerify($request);
        if ($pucArray) {
            $dataList = $Model->getSchoolStuHour($request);

            if ($dataList) {
                $res = array('error' => '0', 'errortip' => '获取班级课时信息成功', 'result' => $dataList);
                ajax_return($res, $request['language_type']);
            } else {
                $res = array('error' => '1', 'errortip' => $Model->errortip, 'result' => array());
                ajax_return($res, $request['language_type']);
            }
        } else {
            $res = array('error' => '1', 'errortip' => $Model->errortip, 'result' => array());
            ajax_return($res, $request['language_type']);
        }
    }

    /**
     * 自动考勤
     * author: ling
     * 对应接口文档 0001
     */
    function autoClassCheckingView()
    {

        $request = Input("post.", "", "");
        if (date("Ymd H:i:s") < date('Ymd') . '23:00:00') {
            $res = array('error' => 1, 'errortip' => "在本日".date('Ymd')."23:00:00 以后进行考勤", 'result' => array());
            ajax_return($res);
        }

        $datawhere = " c.company_id='8888' and sl.school_isclose=0 and c.class_status>=0";

        if ($request['class_id'] && $request['class_id'] !== '') {
            $datawhere .= " and c.class_id = '{$request['class_id']}'";
        }

        $sql = "select h.hour_id,c.class_id,c.school_id,sl.company_id
                from smc_class_hour as h 
                left join smc_class as c ON c.class_id = h.class_id 
                left join smc_course as co On co.course_id=c.course_id
                left join smc_school as sl ON sl.school_id = c.school_id
                where {$datawhere} and sl.school_autochecking =1 and co.course_inclasstype =0 and h.hour_ischecking = 0 
                and not exists(select 1 from smc_auto_checkinglog as g where g.hour_id = h.hour_id) 
                and h.hour_day = curdate() 
                order by class_id DESC,h.hour_id ASC 
                limit 0,40";
        $dataList = $this->DataControl->selectClear($sql);

        if ($dataList) {
            foreach ($dataList as $hourOne) {
                $logData = array();
                $logData['company_id'] = $hourOne['company_id'];
                $logData['school_id'] = $hourOne['school_id'];
                $logData['class_id'] = $hourOne['class_id'];
                $logData['hour_id'] = $hourOne['hour_id'];
                $logData['checkinglog_createtime'] = time();

                $data = array();
                $data['class_id'] = $hourOne['class_id'];
                $data['hour_id'] = $hourOne['hour_id'];
                $data['school_id'] = $hourOne['school_id'];
                $data['company_id'] = $hourOne['company_id'];
                $data['isLookAttendance'] = 1;
                $CourseModel = new CourseModel($data);
                $CheckingModel = new CheckingModel($data);
                $rollClassList = $CourseModel->rollCallClass($data);
                $studentList = $rollClassList['student'];
                $stuData = array();
                if ($studentList) {
                    foreach ($studentList as $key => $studentOne) {
                        $stuData['student_id'] = $studentOne['student_id'];
                        if ($studentOne['stuchecktype_code'] == '101') {
                            $stuData['checkin'] = 1;
                        } else {
                            $stuData['checkin'] = 0;
                        }
                        $stuData['hourstudy_id'] = 0;
                        $stuData['note'] = $studentOne['absence_reasonnote'];
                        $stuData['type'] = $studentOne['type'];
                        $student_data[] = $stuData;
                    }
                    $checkingData = array();
                    $checkingData['student_checkin_list'] = json_encode($student_data);
                    $checkingData['class_id'] = $hourOne['class_id'];
                    $checkingData['checktype_code'] = 'sys';
                    $checkingData['company_id'] = $hourOne['company_id'];
                    $checkingData['school_id'] = $hourOne['school_id'];
                    $checkingData['hour_id'] = $hourOne['hour_id'];

                    $bool = $CheckingModel->setClassChecken($checkingData);
                    if ($bool) {
                        $logData['checkinglog_status'] = 1;
                    } else {
                        $logData['checkinglog_status'] = '-1';
                    }
                    $logData['checkinglog_text'] = $CheckingModel->errortip;
                    $this->DataControl->insertData("smc_auto_checkinglog", $logData);
                    $res = array('error' => $CheckingModel->error, 'errortip' => $CheckingModel->errortip, 'result' => array());
                    ajax_return($res);
                } else {
                    $logData['checkinglog_text'] = '没有可以考勤的学生';
                    $logData['checkinglog_status'] = -1;
                    $this->DataControl->insertData("smc_auto_checkinglog", $logData);
                    $res = array('error' => 1, 'errortip' => '没有可以考勤的学生', 'result' => array());
                    ajax_return($res);
                }
            }
        } else {
            $res = array('error' => 1, 'errortip' => '暂无可以考勤的日期', 'result' => array());
            ajax_return($res);
        }
    }


    function autoClassCheckSchoolView(){

        $request = Input("post.", "", "");

//        if (date("Ymd H:i:s") < date('Ymd') . '23:00:00') {
//            $res = array('error' => 1, 'errortip' => "在本日".date('Ymd')."23:00:00 以后进行考勤", 'result' => array());
//            ajax_return($res);
//        }
        //暂时关闭
        exit;

        $datawhere = " a.company_id='8888' and a.school_isclose=0 and a.school_autochecking=1 ";

        if ($request['school_id'] && $request['school_id'] !== '') {
            $datawhere .= " and c.school_id = '{$request['school_id']}'";
        }

        $sql = "select a.school_id 
                from smc_school as a 
                where {$datawhere} 
                and exists(select 1 from smc_class_hour as x,smc_class as y,smc_course as z where x.class_id=y.class_id and y.course_id=z.course_id and y.school_id=a.school_id and z.course_inclasstype =0 and x.hour_ischecking = 0 and y.class_status>=0 and x.hour_day = curdate() and not exists(select 1 from smc_auto_checkinglog xx where xx.hour_id=x.hour_id) )
                ";
        $schoolList = $this->DataControl->selectClear($sql);

        if(!$schoolList){
            ajax_return(array('error' => 1, 'errortip' => '暂无可以考勤的学校', 'result' => array()));
        }

        $path = BASE_PATH."/ExecShell/autoClassCheck.sh";

        $myFile = fopen($path,"w");
        fwrite($myFile,'#!/bin/bash'."\n");

        foreach($schoolList as $schoolOne){

            $str ='/usr/bin/curl https://api.kedingdang.com/Smc/checkSchoolHour?school_id='.$schoolOne['school_id'].' &'."\n" ;
            fwrite($myFile,$str);
        }

        fclose($myFile);
        exec("chmod -R 755 {$path}");
        exec($path);

    }

    function checkSchoolHourView(){

        $request = Input("get.", "", "");

        $datawhere = " c.company_id='8888' and sl.school_isclose=0 and c.class_status>=0";

        if ($request['school_id'] && $request['school_id'] !== '') {
            $datawhere .= " and c.school_id = '{$request['school_id']}'";
        }else{
            ajax_return(array('error' => 1, 'errortip' => '请选择学校', 'result' => array()));
        }

        $sql = "select h.hour_id,c.class_id,c.school_id,sl.company_id
                from smc_class_hour as h 
                left join smc_class as c ON c.class_id = h.class_id 
                left join smc_course as co On co.course_id=c.course_id
                left join smc_school as sl ON sl.school_id = c.school_id
	            left join smc_auto_checkinglog as g on g.hour_id=h.hour_id
                where {$datawhere} and sl.school_autochecking =1 and co.course_inclasstype =0 and h.hour_ischecking = 0 
                and h.hour_day = curdate() and g.checkinglog_id is null
                order by class_id DESC,h.hour_id ASC 
                limit 0,20
                ";

        $dataList = $this->DataControl->selectClear($sql);

        if ($dataList) {

            $t_num=0;
            foreach ($dataList as $hourOne) {
                $logData = array();
                $logData['company_id'] = $hourOne['company_id'];
                $logData['school_id'] = $hourOne['school_id'];
                $logData['class_id'] = $hourOne['class_id'];
                $logData['hour_id'] = $hourOne['hour_id'];
                $logData['checkinglog_createtime'] = time();

                $data = array();
                $data['class_id'] = $hourOne['class_id'];
                $data['hour_id'] = $hourOne['hour_id'];
                $data['school_id'] = $hourOne['school_id'];
                $data['company_id'] = $hourOne['company_id'];
                $data['isLookAttendance'] = 1;
                $CourseModel = new CourseModel($data);
                $CheckingModel = new CheckingModel($data);
                $rollClassList = $CourseModel->rollCallClass($data);
                $studentList = $rollClassList['student'];
                if ($studentList) {

                    $stuIdsArray=array_column($studentList,'student_id');

                    $sql = "select a.student_id 
                            from smc_student_study as a 
                            where a.class_id='{$hourOne['class_id']}'";

                    $allstuList=$this->DataControl->selectClear($sql);

                    $allstuArray=$allstuList?array_column($allstuList,'student_id'):array();

                    if(array_diff($stuIdsArray,$allstuArray)){
                        $logData['checkinglog_text'] = '存在非班级考勤学员';
                        $logData['checkinglog_status'] = -1;
                        $this->DataControl->insertData("smc_auto_checkinglog", $logData);
                        continue;
                    }


                    foreach ($studentList as $key => $studentOne) {

                        $stuData = array();
                        $stuData['student_id'] = $studentOne['student_id'];
                        if ($studentOne['stuchecktype_code'] == '101') {
                            $stuData['checkin'] = 1;
                        } else {
                            $stuData['checkin'] = 0;
                        }
                        $stuData['hourstudy_id'] = 0;
                        $stuData['note'] = $studentOne['absence_reasonnote'];
                        $stuData['type'] = $studentOne['type'];
                        $student_data[] = $stuData;
                    }
                    $checkingData = array();
                    $checkingData['student_checkin_list'] = json_encode($student_data);
                    $checkingData['class_id'] = $hourOne['class_id'];
                    $checkingData['checktype_code'] = 'sys';
                    $checkingData['company_id'] = $hourOne['company_id'];
                    $checkingData['school_id'] = $hourOne['school_id'];
                    $checkingData['hour_id'] = $hourOne['hour_id'];

                    $bool = $CheckingModel->setClassChecken($checkingData);
                    if ($bool) {
                        $logData['checkinglog_status'] = 1;
                        $t_num++;
                    } else {
                        $logData['checkinglog_status'] = '-1';
                    }
                    $logData['checkinglog_text'] = $CheckingModel->errortip;
                    $this->DataControl->insertData("smc_auto_checkinglog", $logData);

                } else {
                    $logData['checkinglog_text'] = '没有可以考勤的学生';
                    $logData['checkinglog_status'] = -1;
                    $this->DataControl->insertData("smc_auto_checkinglog", $logData);

                }

            }

            $res = array('error' => 0, 'errortip' => '自动考勤结束', 'result' => array());
            ajax_return($res);


        } else {
            $res = array('error' => 1, 'errortip' => '暂无可以考勤的日期', 'result' => array());
            ajax_return($res);
        }
    }


    //自动考勤任务版本

    function createAutoAtteTaskView(){


        $request = Input('post.', '', 'trim,addslashes');
        
        $datawhere=" 1 ";

        if(isset($request['date']) && $request['date']!=''){
            $datawhere.=" and h.hour_day='{$request['date']}'";
        }else{
            $datawhere.=" and h.hour_day = curdate()";
        }

        $sql = "select h.hour_id,c.class_id,c.school_id,sl.company_id
                from smc_class_hour as h 
                inner join smc_class as c ON c.class_id = h.class_id 
                inner join smc_course as co On co.course_id=c.course_id
                inner join smc_school as sl ON sl.school_id = c.school_id
	            left join smc_auto_checkinglog as g on g.hour_id=h.hour_id
                where {$datawhere} and c.company_id='8888' and sl.school_isclose=0 and c.class_status>=0 and sl.school_autochecking =1 and co.course_inclasstype =0 and h.hour_ischecking = 0 and g.checkinglog_id is null
                order by h.hour_id ASC 
                limit 0,200
                ";

        $hourList=$this->DataControl->selectClear($sql);

        if($hourList){
            foreach ($hourList as $hourOne) {

                $data=array();
                $data['company_id'] = $hourOne['company_id'];
                $data['school_id'] = $hourOne['school_id'];
                $data['class_id'] = $hourOne['class_id'];
                $data['hour_id'] = $hourOne['hour_id'];
                $this->DataControl->insertData("smc_auto_checkinglog", $data);
            }
        }
    }

    function autoCompleteTaskView(){

        $sql = "select a.hour_id,a.class_id,a.school_id,a.company_id,a.checkinglog_id
                from smc_auto_checkinglog as a 
                where a.log_status=0
                order by a.hour_id asc
                limit 0,1
                ";

        $dataList = $this->DataControl->selectClear($sql);

        if ($dataList) {
            foreach ($dataList as $hourOne) {
                $data=array();
                $data['log_status']=1;
                $this->DataControl->updateData("smc_auto_checkinglog","checkinglog_id='{$hourOne['checkinglog_id']}'",$data);

                $logData = array();
                $logData['checkinglog_createtime'] = time();

                $data = array();
                $data['class_id'] = $hourOne['class_id'];
                $data['hour_id'] = $hourOne['hour_id'];
                $data['school_id'] = $hourOne['school_id'];
                $data['company_id'] = $hourOne['company_id'];
                $data['isLookAttendance'] = 1;

                $CourseModel = new CourseModel($data);
                $CheckingModel = new CheckingModel($data);
                $rollClassList = $CourseModel->rollCallClass($data);
                $studentList = $rollClassList['student'];

                $logData['log_stuList'] = json_encode($studentList,JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES);

                if ($studentList) {

                    foreach ($studentList as $studentOne) {
                        $stuData = array();
                        $stuData['student_id'] = $studentOne['student_id'];
                        if ($studentOne['stuchecktype_code'] == '101') {
                            $stuData['checkin'] = 1;
                        } else {
                            $stuData['checkin'] = 0;
                        }
                        $stuData['hourstudy_id'] = 0;
                        $stuData['note'] = $studentOne['absence_reasonnote'];
                        $stuData['type'] = $studentOne['type'];
                        $student_data[] = $stuData;
                    }
                    $checkingData = array();
                    $checkingData['student_checkin_list'] = json_encode($student_data);
                    $checkingData['class_id'] = $hourOne['class_id'];
                    $checkingData['checktype_code'] = 'sys';
                    $checkingData['company_id'] = $hourOne['company_id'];
                    $checkingData['school_id'] = $hourOne['school_id'];
                    $checkingData['hour_id'] = $hourOne['hour_id'];

                    $bool = $CheckingModel->setClassChecken($checkingData);
                    if ($bool) {
                        $logData['checkinglog_status'] = 1;
                    } else {
                        $logData['checkinglog_status'] = '-1';
                    }
                    $logData['checkinglog_text'] = $CheckingModel->errortip;
                    $this->DataControl->updateData("smc_auto_checkinglog","checkinglog_id='{$hourOne['checkinglog_id']}'",$logData);

                } else {
                    $logData['checkinglog_text'] = '没有可以考勤的学生';
                    $logData['checkinglog_status'] = -1;
                    $this->DataControl->updateData("smc_auto_checkinglog","checkinglog_id='{$hourOne['checkinglog_id']}'",$logData);
                }
            }
        }
    }


    //待支付批量检测
    function nopayStartView()
    {
        $sql = "SELECT
	s.school_cnname,
	o.trading_pid,
	o.order_status,
	o.school_id,
	o.order_pid,
	o.order_paymentprice,
	p.pay_pid,
	FROM_UNIXTIME(
		p.pay_createtime,
		'%Y-%m-%d'
	) AS createtime,
	p.pay_typename,
	p.paytype_code,
	p.pay_price
FROM
	smc_payfee_order AS o,
	smc_school AS s,
	smc_payfee_order_pay AS p
WHERE
	o.order_pid = p.order_pid
AND o.school_id = s.school_id
AND p.pay_issuccess = '0'
AND p.paytype_code <> 'canceldebts'
AND p.paytype_code <> 'pos'
AND p.paytype_code <> 'feewaiver'
AND p.paytype_code <> 'cash'
AND o.order_status <> '-1'
AND s.school_isclose <> '1'
AND s.school_istest <> '1'
AND o.company_id = '8888'
ORDER BY p.pay_createtime ASC limit 850,50";
        $dataList = $this->DataControl->selectClear($sql);
        if ($dataList) {
            foreach ($dataList as $hourOne) {
                //sleep(1);
                $parameter = array();
                $parameter['company_id'] = '8888';
                $parameter['staffer_id'] = '12357';
                $parameter['token'] = 'YTk1M2U4YTkwZmQ0MmMwOTc1OWFlOTEwYTRhM2E3OTc=';
                $parameter['paypid'] = $hourOne['pay_pid'];
                $dataArray = request_by_curl("https://smcapi.kedingdang.com/OrderPay/orderPayStatusApi", dataEncode($parameter), "GET", array());
                print_r($dataArray);
                echo "{$hourOne['pay_pid']} {$hourOne['createtime']}查询完毕<br />";
                //http://api.kcclassin.com/Smc/nopayStart
            }
        }
    }


    //美语学员自动流失程序
    function automaticDrainView()
    {
        //https://smcapi.kedingdang.com/Change/stuLossAction
        /*student_id: 63265
is_empty_balance: 0
is_empty_forward: 0
reason: 系统流失：系统自动检测超过21天未就读，且无任何课程余额，启动自动流失程序
reason_code: 042
category_note: 暂不知情
stuchange_code: C02
coursetype_id:
re_postbe_id: 0
token: OWE2ZGZhNWI5MTE2Njc1ODE5YTg3Y2NlZjdiMDJhNzE=
            company_id: 8888
staffer_id: 12357
school_id: 673
language_type: zh*/


    }

    //获取学生续费情况以便生成积分
    function getRenewAmountView()
    {
        $k = 0;
        $field = array();
        $field[$k]["fieldstring"] = "student_name";
        $field[$k]["fieldname"] = "学员姓名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_branch";
        $field[$k]["fieldname"] = "学员编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursetype_id";
        $field[$k]["fieldname"] = "班组id";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "type";
        $field[$k]["fieldname"] = "积分类别";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "left_times";
        $field[$k]["fieldname"] = "需费时剩余课次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "pay_time";
        $field[$k]["fieldname"] = "支付时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "pay_amount";
        $field[$k]["fieldname"] = "支付金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "buy_times";
        $field[$k]["fieldname"] = "购买课次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "trading_pid";
        $field[$k]["fieldname"] = "交易编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $request = Input('get.', '', 'trim,addslashes');
        $datawhere = " and 1 ";
        $havingwhere = " 1 ";
        if (isset($request['start_time']) && $request['start_time'] !== '') {
            $start_day = strtotime($request['start_time']);
//            $datawhere .= " and a.order_createtime>='{$start_day}'";
            $havingwhere .= " and pay_time>='{$start_day}'";
        }

        if (isset($request['end_time']) && $request['end_time'] !== '') {
            $end_day = strtotime($request['end_time']) + 86399;
//            $datawhere .= " and a.order_createtime<='{$end_day}'";
            $havingwhere .= " and pay_time<='{$end_day}'";
        }
        $havingwhere .= " and coursetype_id in(61,65,79653,79654,79655)";

        if (isset($request['trading_pid']) && $request['trading_pid'] !== '') {
            $datawhere .= " and a.trading_pid='{$request['trading_pid']}'";
        }
//        if (isset($request['p']) && $request['p'] !== '') {
//            $page = $request['p'];
//        } else {
//            $page = '1';
//        }
//        if (isset($request['num']) && $request['num'] !== '') {
//            $num = $request['num'];
//        } else {
//            $num = '10';
//        }
//        $pagestart = ($page - 1) * $num;

        $sql = "select a.company_id,a.student_id,a.school_id,b.student_branch
                ,(select y.coursetype_id from smc_payfee_order_course x,smc_course y where x.course_id=y.course_id and x.order_pid=a.order_pid limit 0,1) as coursetype_id
                ,(select y.coursecat_id from smc_payfee_order_course x,smc_course y where x.course_id=y.course_id and x.order_pid=a.order_pid limit 0,1) as coursecat_id
                ,a.order_createtime
                ,(select MAX(pay_successtime) from smc_payfee_order_pay x where x.order_pid=a.order_pid and pay_issuccess=1 and x.pay_type=0) as pay_time
                ,(select sum(pay_price) from smc_payfee_order_pay x where x.order_pid=a.order_pid and pay_issuccess=1 and x.pay_type=0) as pay_amount
                ,(select sum(ordercourse_buynums) from smc_payfee_order_course x where x.order_pid=a.order_pid ) as buy_times
                ,(select course_id from smc_payfee_order_course x where x.order_pid=a.order_pid limit 0,1) as course_id
                ,a.trading_pid
                from smc_payfee_order a
                left join smc_student b on a.student_id=b.student_id
                where a.company_id='8888'
                {$datawhere}
                and a.order_status=4
                and order_type=0
                and not exists(select 1 from smc_payfee_order_pay where order_pid=a.order_pid and pay_issuccess=1 and paytype_code='coursebalance')
                and not exists(select 1 from smc_student_registerinfo where info_status=1 and trading_pid=a.trading_pid)
                having {$havingwhere}";//concat(y.coursetype_id,'-',y.coursetype_cnname)
        $orderList = $this->DataControl->selectClear($sql);
        if (!$orderList) {
            $orderList = array();
        }

        foreach ($orderList as &$var) {
            $pay_day = date("Y-m-d", $var['pay_time']);
            $sqlOne = "select a.student_id,a.class_id,count(d.hour_id) as left_times
                        from smc_student_study a
                        left join smc_class b on a.class_id=b.class_id 
                        left join smc_course c on b.course_id=c.course_id
                        left join smc_class_hour d on a.class_id=d.class_id and d.hour_ischecking>=0 and d.hour_isfree='0' and d.hour_iswarming='0'
                        where a.company_id='{$var['company_id']}'
                        and c.coursetype_id='{$var['coursetype_id']}'
                        and (c.course_id<>'{$var['course_id']}' or c.course_sellclass=1)
                        and d.hour_day>='{$pay_day}'
                        and a.school_id='{$var['school_id']}'
                        and a.student_id='{$var['student_id']}'
                        and a.study_endday>='{$pay_day}'
                        and a.study_beginday<='{$pay_day}'
                        group by a.student_id,a.class_id
                        order by left_times desc
                        limit 0,1 ";
            $readinfo = $this->DataControl->selectOne($sqlOne);
            if ($readinfo && $readinfo['left_times'] > 0) {
                $var['left_times'] = $readinfo['left_times'];
            } else {
                $var['left_times'] = 0;
            }
        }

        $result["field"] = $field;
        $result["list"] = $orderList;
        $res = array('error' => 0, 'errortip' => '获取续费明细成功', 'result' => $result);
        ajax_return($res, $request['language_type']);
    }


    function ReimburseView(){
        $request = Input('post.', '', 'trim,addslashes');
        $reimburseOne = $this->DataControl->selectOne("SELECT r.* FROM tkl_reimburse AS r WHERE
 r.school_name = '{$request['school_name']}' AND r.company_id = '{$request['company_id']}'
 AND r.reimburse_cnname = '{$request['reimburse_cnname']}' AND r.reimburse_payeename = '{$request['reimburse_payeename']}'");
        if($reimburseOne){
            $res = array('error' => 0, 'errortip' => '查询成功！', 'result' => $reimburseOne);
            ajax_return($res);
        }else{
            $res = array('error' => 1, 'errortip' => '未查询到有效退费信息，请检查填写信息是否正确！', 'result' => array());
            ajax_return($res);
        }
    }

    function coursepListView(){
        $courseList = $this->DataControl->selectClear("SELECT
c.course_branch,
c.course_cnname
FROM
smc_course AS c
WHERE
c.company_id = '8888'
AND c.coursetype_id = '65'
AND c.course_branch NOT IN (
SELECT
t.classcode_branch
FROM
eas_teachhour AS t
WHERE t.company_id = '8888') Limit 0,20");
        if($courseList){
            foreach ($courseList as $hourOne) {
                $parameter = array();
                $parameter['company_id'] = '8888';
                $parameter['course_branch'] = $hourOne['course_branch'];
                $parameter['staffer_id'] = '12357';
                $parameter['token'] = 'ZmQ1NTQ3ZjgyZWI0ODdiOGQyNWYwMGU4ZmZjMGZhOTI=';
                $dataArray = request_by_curl("https://gmcapi.kedingdang.com/PrepareLessons/AddClassHourAction", dataEncode($parameter), "POST", array());
                $bingApiArray = json_decode($dataArray, "1");
                echo $hourOne['course_branch']."-".$bingApiArray['errortip']."<br />";
            }
        }else{
            $res = array('error' => 1, 'errortip' => '已处理完毕！', 'result' => array());
            ajax_return($res);
        }
    }


    function companiesRunView(){
        $studentList = $this->DataControl->selectClear("SELECT
	a.school_id,
	a.student_id,
	a.course_id,
	count(

		IF (
			a.log_playname <> '点名扣除课程余额',
			TRUE,
			NULL
		)
	) AS count_no,
	(
		SELECT
			COUNT(DISTINCT i.companies_id)
		FROM
			smc_school_income AS i
		WHERE
			i.school_id = b.school_id
		AND i.student_id = b.student_id
		AND i.course_id = b.course_id
		AND i.income_type = '0'
	) AS ccnums
FROM
	smc_student_coursebalance_log a
LEFT JOIN smc_student_coursebalance b ON a.school_id = b.school_id
AND a.student_id = b.student_id
AND a.course_id = b.course_id
WHERE
	b.company_id = '8888'
AND a.companies_id <> b.companies_id
GROUP BY
	a.school_id,
	a.student_id,
	a.course_id
HAVING
	count_no = 0
AND ccnums < 1
LIMIT 0,
 50");
        if($studentList){
            foreach ($studentList as $studentOne) {
                $coursebalanceOne = $this->DataControl->getFieldOne("smc_student_coursebalance","companies_id"
                    ,"school_id = '{$studentOne['school_id']}'
                    AND student_id = '{$studentOne['student_id']}' AND course_id = '{$studentOne['course_id']}'");
                if($coursebalanceOne && !$this->DataControl->getFieldOne("smc_student_coursebalance_log","log_id"
                    ,"companies_id <> '{$coursebalanceOne['companies_id']}' AND school_id = '{$studentOne['school_id']}'
                    AND student_id = '{$studentOne['student_id']}' AND course_id = '{$studentOne['course_id']}'
                    AND log_playname <> '点名扣除课程余额'")){
                    $this->DataControl->updateData("smc_student_coursebalance_log"
                        , "school_id = '{$studentOne['school_id']}' AND log_playname = '点名扣除课程余额'
                    AND student_id = '{$studentOne['student_id']}' AND course_id = '{$studentOne['course_id']}'"
                        , array("companies_id" => $coursebalanceOne['companies_id']));
                    echo "{$studentOne['student_id']}-{$studentOne['course_id']}-修改成功<br />";
                }else{
                    echo "{$studentOne['student_id']}-{$studentOne['course_id']}-无法修改-------------<br />";
                }
            }
        }else{
            $res = array('error' => 1, 'errortip' => '已处理完毕！', 'result' => array());
            ajax_return($res);
        }
    }

    //迁移班级电访日志
//    function migrateStucallView(){
//        $trackList = $this->DataControl->selectClear("SELECT
//	t.track_id,
//	t.class_id,
//	t.student_id,
//	t.track_note,
//	(
//		SELECT
//			n.class_id
//		FROM
//			smc_class AS n
//		WHERE
//			n.class_branch = l.to_class_branch
//	) AS nclass_id
//FROM
//	smc_student_track AS t,
//	smc_class AS c,
//	smc_excel_classtocall AS l
//WHERE
//	t.class_id = c.class_id
//AND c.class_branch = l.from_class_branch
//AND l.from_class_branch <> l.to_class_branch
//AND EXISTS (
//	SELECT
//		d.study_id
//	FROM
//		smc_student_study AS d,
//		smc_class AS n
//	WHERE
//		d.student_id = t.student_id
//	AND n.class_branch = l.to_class_branch
//)");
//        if($trackList){
//            foreach ($trackList as $trackOne) {
//                $this->DataControl->updateData("smc_student_track"
//                    , "track_id = '{$trackOne['track_id']}'"
//                    , array(
//                        "from_classid" => $trackOne['class_id'],
//                        "class_id" => $trackOne['nclass_id']
//                    )
//                );
//                echo "{$trackOne['track_id']}迁移完毕<br />";
//            }
//        }else{
//            $res = array('error' => 1, 'errortip' => '已处理完毕！', 'result' => array());
//            ajax_return($res);
//        }
//    }

//    //复制续费电访数据
//    function copyStucallView(){
//        set_time_limit(0);
//        $trackList = $this->DataControl->selectClear("SELECT
//	o.*,p.to_stdate
//FROM
//	smc_student_track AS o,
//	(SELECT
//	d.school_id,
//	d.student_id,
//	f.class_id,
//	f.class_stdate,
//	f.class_enddate,
//	u.coursetype_id,
//	t.class_stdate AS to_stdate,
//	t.class_enddate AS to_enddate
//FROM
//	smc_student_study AS d,
//	smc_class AS f,
//	smc_course AS u,
//	smc_excel_classtocall AS a,
//	smc_class AS t
//WHERE
//	d.class_id = f.class_id
//AND f.course_id = u.course_id
//AND f.class_branch = a.from_class_branch
//AND t.class_branch = a.to_class_branch
//AND a.call_setting = '0') AS p
//
//WHERE
//(
//	o.coursetype_id = 0
//	OR o.coursetype_id = p.coursetype_id
//)
//AND o.student_id = p.student_id
//AND o.school_id = p.school_id
//AND o.track_from = 0
//AND (o.track_classname = '续费电访' OR o.track_classname = '主管电访')
//AND o.track_day >= p.class_stdate
//AND o.track_day < p.to_stdate");
//        if($trackList){
//            foreach ($trackList as $trackOne) {
//                $track = array();
//                $track['track_from'] = $trackOne['track_from'];
//                $track['company_id'] = $trackOne['company_id'];
//                $track['school_id'] = $trackOne['school_id'];
//                $track['class_id'] = $trackOne['class_id'];
//                $track['interval_id'] = $trackOne['interval_id'];
//                $track['commode_id'] = $trackOne['commode_id'];
//                $track['student_id'] = $trackOne['student_id'];
//                $track['coursetype_id'] = $trackOne['coursetype_id'];
//                $track['coursecat_id'] = $trackOne['coursecat_id'];
//                $track['tracktype_id'] = $trackOne['tracktype_id'];
//                $track['staffer_id'] = $trackOne['staffer_id'];
//                $track['track_code'] = $trackOne['track_code'];
//                $track['track_classname'] = $trackOne['track_classname'];
//                $track['result_id'] = $trackOne['result_id'];
//                $track['track_linktype'] = $trackOne['track_linktype'];
//                $track['track_note'] = "转制迁移：".$trackOne['track_note'];
//                $track['track_picturejson'] = $trackOne['track_picturejson'];
//                $track['track_day'] = $trackOne['to_stdate'];
//                $track['staffer_type'] = $trackOne['staffer_type'];
//                $track['track_followutime'] = $trackOne['track_followutime'];
//                $track['track_createtime'] = $trackOne['track_createtime'];
//                $track['catitrack_id'] = $trackOne['track_id'];
//                $track['from_classid'] = '999999';
//                $this->DataControl->insertData('smc_student_track', $track);
//            }
//            $res = array('error' => 1, 'errortip' => '数据复制完毕！', 'result' => array());
//            ajax_return($res);
//        }else{
//            $res = array('error' => 1, 'errortip' => '已处理完毕！', 'result' => array());
//            ajax_return($res);
//        }
//    }




    function automaticAuditApi()
    {
        $request = Input('post.', '', 'trim,addslashes');

        $sql = "SELECT o.order_pid,o.company_id,o.school_id,o.staffer_id,o.student_id,o.order_alltimes as num,sc.coursetype_id
                FROM smc_freehour_order as o 
                LEFT JOIN smc_course as sc on sc.course_id = o.course_id 
                WHERE o.company_id = '8888' and o.school_id IN ('1175', '2323') and sc.coursetype_id IN ('79654', '79653', '79660') and o.order_status = 0";
        $orderList = $this->DataControl->selectClear($sql);

        if ($orderList) {
            foreach ($orderList as $value) {
                $ClassModel = new \Model\Smc\ClassModel($value);
                $ClassModel->automaticAudit($value);
            }
            $res = array('error' => 0, 'errortip' => '自动审核成功', 'result' => array());
        } else {
            $res = array('error' => 1, 'errortip' => '无需自动审核', 'result' => array());
        }

        ajax_return($res, $request['language_type']);
    }

    //给 科技中心-吴磊 提供的查校的再开班级接口
    function getSchoolClassView(){
        $request = Input('get.', '', 'trim,addslashes');

        $request['company_id'] = 8888;
        if(!isset($request['school_branch']) || $request['school_branch'] == '') {
            ajax_return(array('error' => '1', 'errortip' => '校园编号不能为空', 'result' => array()));
        }
        $nowtime = date("Y-m-d",time());
        $datawhere = " a.company_id = '{$request['company_id']}' and a.class_status <> '-2' and a.class_stdate <= '{$nowtime}' and a.class_enddate >= '{$nowtime}'
                and b.school_id > 1  ";
        if (isset($request['school_branch']) && $request['school_branch']) {
            $datawhere .= " and b.school_branch = '{$request['school_branch']}' ";
        }
        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '100';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select a.class_id,a.class_branch,a.class_cnname,a.class_enname 
                from smc_class as a 
                left join smc_school as b ON a.school_id = b.school_id 
                where {$datawhere}
                LIMIT {$pagestart},{$num}";
        $classList = $this->DataControl->selectClear($sql);

        $field = [
            "class_id"=>"班级自增ID",
            "class_branch"=>"班级编号",
            "class_cnname"=>"班级中文名",
            "class_enname"=>"班级英文名",
        ];

        $result = array();
        $result["field"] = $field;
        $result['list'] =$classList?$classList:array();
        if ($classList) {
            ajax_return(array('error' => '0', 'errortip' => "学校班级信息获取成功", 'result' => $result));
        } else {
            ajax_return(array('error' => '1', 'errortip' => "学校班级信息获取失败", 'result' => $result));
        }
    }













}