<?php


namespace Work\Controller\Api;


class attendStudentController extends viewTpl
{
    public $u;
    public $t;
    public $c;
    public $visitType = "api";


    //预加载处理类
    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
        $this->api = $this->router->getApi();
    }

    function DhattendanceView()
    {
        $paramArray = input("post.", "", "trim");
        $data = array();
        $data['apiuser_id'] = '7';
        $data['apimodule_id'] = '0';
        $data['apilog_posturl'] = "https://api.kedingdang.com/attendStudent/Dhattendance";
        $data['apilog_posttype'] = 'POST';
        $data['apilog_postorgjson'] = http_build_query($paramArray);
        $data['apilog_ip'] = real_ip();
        $data['apilog_createtime'] = time();
        $this->DataControl->insertData("imc_apiuser_apilog", $data);

        $paramArray = input("get.", "", "trim");
        $data = array();
        $data['apiuser_id'] = '7';
        $data['apimodule_id'] = '0';
        $data['apilog_posturl'] = "https://api.kedingdang.com/attendStudent/Dhattendance";
        $data['apilog_posttype'] = 'GET';
        $data['apilog_postorgjson'] = http_build_query($paramArray);
        $data['apilog_ip'] = real_ip();
        $data['apilog_createtime'] = time();
        $this->DataControl->insertData("imc_apiuser_apilog", $data);

        $paramArray = input("put.", "", "trim");
        $data = array();
        $data['apiuser_id'] = '7';
        $data['apimodule_id'] = '0';
        $data['apilog_posturl'] = "https://api.kedingdang.com/attendStudent/Dhattendance";
        $data['apilog_posttype'] = 'put';
        $data['apilog_postorgjson'] = file_get_contents('php://input');
        $data['apilog_ip'] = real_ip();
        $data['apilog_createtime'] = time();
        $this->DataControl->insertData("imc_apiuser_apilog", $data);

        $res = array('code' => '0', 'Msg' => '推送成功', 'result' => array());
        ajax_return($res);
    }


}