<?php


namespace Work\Controller\Api;


class CrmController extends viewTpl
{
    public $u;
    public $t;
    public $c;
    public $visitType = "api";


    //预加载处理类
    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
        $this->api = $this->router->getApi();
    }

    //渠道商名单 校验处理 --- c测试
    function testhandleChannelMobileView(){die;
        $data = array();
        $data['company_id'] = 8888;
        $data['client_mobile'] = '14782269749';
        //渠道商名单判断是否撞单问题
        $Model = new \Model\Crm\ClientModel($data);
        $Model->CrmRosterVerify($data);
        print_r($Model->errortip);die;
    }

    //渠道商名单 校验处理
    function handleExceloderView(){
        $sql = "select a.*,
                    if((select b.roster_id from crm_channel_exceloder_roster as b where a.company_id = b.company_id and a.channel_id = b.channel_id and a.exceloder_pid = b.exceloder_pid and b.roster_status = '0' ),1,0) as roster_id
                from crm_channel_exceloder as a 
                where a.exceloder_status in (0,1) 
                order by exceloder_id  
                limit 0,1";
        $exceloder = $this->DataControl->selectOne($sql);
        if($exceloder['roster_id'] == '0'){
            $redata = array();
            $redata['exceloder_status'] = 2;
            $redata['beinorder_completetime'] = time();
            $this->DataControl->updateData("crm_channel_exceloder", "exceloder_pid='{$exceloder['exceloder_pid']}' and company_id='8888' and channel_id='{$exceloder['channel_id']}'  ", $redata);
        }
    }
    //渠道商名单 校验处理
    function handleChannelMobileView(){
        $sql = "select * 
                from crm_channel_exceloder_roster as a 
                where a.roster_status = '0' 
                limit 0,100 ";
        $rosterData = $this->DataControl->selectClear($sql);
        if($rosterData){
            foreach ($rosterData as $rosterVar){
                $data = array();
                $data['company_id'] = 8888;
                $data['client_mobile'] = $rosterVar['roster_mobile'];
                $data['exceloder_pid'] = $rosterVar['exceloder_pid'];
                //渠道商名单判断是否撞单问题
                $Model = new \Model\Crm\ClientModel($data);
//                $result = $Model->CrmRosterVerify($data);
                //新的
                $result = $Model->CrmRosterVerifyNew($data);

                if($Model->errortip) {
                    $redata = array();
                    $redata['roster_status'] = 1;
                    $redata['roster_isexceltocrm'] = $Model->error;
                    $redata['roster_note'] = $Model->errortip;
                    $redata['roster_completetime'] = time();
                    $this->DataControl->updateData("crm_channel_exceloder_roster", " roster_mobile='{$rosterVar['roster_mobile']}' and exceloder_pid='{$rosterVar['exceloder_pid']}' and company_id='8888' and channel_id='{$rosterVar['channel_id']}'  ", $redata);
                }
            }
        }
    }

    //获取集团IMK电话厂商信息
    function getGmcTmkMerchantView(){
        $request = Input('get.', '', 'trim,addslashes');
        $dataList = [
            ["id"=>"1","name"=>"慧捷","isopen"=>"1"],
            ["id"=>"9","name"=>"容联七陌","isopen"=>"1"],
        ];
        //isopen  不生效，前端没有判断
//        ["id"=>"2","name"=>"合力","isopen"=>"0"],
//            ["id"=>"3","name"=>"仕决","isopen"=>"0"],
        if($dataList){
            $res = array('error' => '0', 'errortip' => '账密信息查询成功', 'result' => $dataList);
            ajax_return($res);
        } else {
            $res = array('error' => '1', 'errortip' => '或未查到账密信息', 'result' => array());
            ajax_return($res);
        }
    }
    //账密信息查询
    function getMarketerOutcallView(){
        $request = Input('get.', '', 'trim,addslashes');
        $marketerOne = $this->DataControl->selectOne("SELECT m.marketer_id,m.company_id,m.marketer_outtype,m.marketer_outdomainname,m.marketer_outsipname,m.marketer_outworknumber,m.marketer_outworkpasswd
FROM crm_marketer AS m WHERE m.marketer_id = '{$request['marketer_id']}' limit 0,1");
        if($marketerOne){
            $res = array('error' => '0', 'errortip' => '账密信息查询成功', 'result' => $marketerOne);
            ajax_return($res);
        } else {
            $res = array('error' => '1', 'errortip' => '或未查到账密信息', 'result' => array());
            ajax_return($res);
        }
    }

    function orderPayuserView(){
        $paylogList = $this->DataControl->selectClear("SELECT l.paylog_id, l.pay_pid, l.paylog_actualprice, l.paylog_bakjson
FROM smc_payfee_order_paylog l, smc_payfee_order_pay p, smc_payfee_order o
WHERE l.pay_pid = p.pay_pid AND p.order_pid = o.order_pid AND o.company_id = '8888' AND l.paylog_bakjson <> ''
AND l.paylog_bakjson LIKE '%buyer_user_name%'
AND l.paylog_id NOT IN ( SELECT u.paylog_id FROM smc_payfee_order_payuser u ) ORDER BY l.paylog_id ASC LIMIT 0, 10");
        if($paylogList){
            foreach($paylogList as $paylogOne){
                //print_r($paylogOne['paylog_bakjson']);
                $jsonPlay = new \Webjson();
                $payuserArray = $jsonPlay->decode($paylogOne['paylog_bakjson'],"1");
                //$payuserArray = json_decode($paylogOne['paylog_bakjson']);
                print_r($payuserArray);
            }
        }
    }

    //园务添加地推人员名单时 检查是否和校务重复，编号是否一致  --------  逻辑调整 应该是可以抛弃方法了 （20240314）
    function seekPromotionView(){
        $request = Input('get.', '', 'trim,addslashes');
        if(!isset($request['mobile']) || $request['mobile'] == ''){
            $res = array('error' => '1', 'errortip' => '手机号不能为空','result' => array());
            ajax_return($res);
        }
        if(!isset($request['jobnumber']) || $request['jobnumber'] == ''){
            $res = array('error' => '1', 'errortip' => '推广人员工号不能为空','result' => array());
            ajax_return($res);
        }
        if(!isset($request['company_id']) || $request['company_id'] == ''){
            $res = array('error' => '1', 'errortip' => '集团ID必须传','result' => array());
            ajax_return($res);
        }

        $sql = "select promotion_mobile,promotion_jobnumber from crm_ground_promotion where company_id = '{$request['company_id']}' and promotion_mobile = '{$request['mobile']}' and promotion_jobnumber = '{$request['jobnumber']}' limit 0,1 ";
        $One = $this->DataControl->selectOne($sql);
        if($One){
            $res = array('error' => '0', 'errortip' => '名单信息一致可以录入','result' => array());
            ajax_return($res);
        }

        $sql = "select promotion_mobile,promotion_jobnumber from crm_ground_promotion where company_id = '{$request['company_id']}' and promotion_mobile = '{$request['mobile']}' and promotion_jobnumber <> '{$request['jobnumber']}' limit 0,1 ";
        $twoOne = $this->DataControl->selectOne($sql);

        $sql = "select promotion_mobile,promotion_jobnumber from crm_ground_promotion where company_id = '{$request['company_id']}' and promotion_jobnumber = '{$request['jobnumber']}' and promotion_mobile <> '{$request['mobile']}'  limit 0,1 ";
        $threeoOne = $this->DataControl->selectOne($sql);

        if($twoOne || $threeoOne){
            $result = $twoOne?$twoOne:$threeoOne;
            $res = array('error' => '1', 'errortip' => '校务存在名单的部分信息，请按照返回的信息录入完全一致的信息','result' => $result);
            ajax_return($res);
        }

        $res = array('error' => '0', 'errortip' => '不存在重复信息可以录入','result' => array());
        ajax_return($res);
    }
    //园务添加地推人员名单时 获取校的地推人员 （20240314）
    function getSeekPromotionView(){
        $request = Input('get.', '', 'trim,addslashes');
        if(!isset($request['mobile']) || $request['mobile'] == ''){
            $res = array('error' => '1', 'errortip' => '手机号不能为空','result' => array());
            ajax_return($res);
        }
        if(!isset($request['company_id']) || $request['company_id'] == ''){
            $res = array('error' => '1', 'errortip' => '集团ID必须传','result' => array());
            ajax_return($res);
        }

        $sql = "select promotion_mobile,promotion_jobnumber,company_id from crm_ground_promotion where company_id = '{$request['company_id']}' and promotion_mobile = '{$request['mobile']}' limit 0,1 ";
        $One = $this->DataControl->selectOne($sql);
        if($One){
            $res = array('error' => '0', 'errortip' => '找到地推人员','result' => $One);
            ajax_return($res);
        }else{
            $res = array('error' => '1', 'errortip' => '未找到地推人员','result' => $One);
            ajax_return($res);
        }
    }

    /**
     * 关键词名单孕育系统
     */
    function keywordBreedView()
    {
        $schooltipsList = $this->DataControl->selectClear("SELECT t.school_id , t.schooltips_name FROM crm_code_schooltips AS t WHERE t.company_id = '8888'");
        if($schooltipsList){
            foreach($schooltipsList as $schooltipsOne){
                echo "<p>------{$schooltipsOne['schooltips_name']}附近名单已自动分配开始！-------</p>";

                $clientList = $this->DataControl->selectClear("SELECT c.client_id, c.company_id, c.client_cnname, c.client_mobile, c.client_source , c.client_remark, c.client_address,c.client_createtime
FROM crm_client c LEFT JOIN crm_client_schoolenter s ON c.client_id = s.client_id
WHERE s.client_id IS NULL AND c.company_id = '8888'
AND (c.client_address LIKE '%{$schooltipsOne['schooltips_name']}%' OR c.client_remark LIKE '%{$schooltipsOne['schooltips_name']}%')
ORDER BY c.client_updatetime DESC limit 0,50");

                if($clientList){
                    foreach($clientList as $clientOne){
                        if ($this->DataControl->getFieldOne("crm_client_schoolenter", "schoolenter_id", "client_id='{$clientOne['client_id']}'")) {
                            continue;
                        }
                        //分配至学校846丰庄674梅川
                        $data = array();
                        $data['company_id'] = $clientOne['company_id'];
                        $data['client_id'] = $clientOne['client_id'];
                        $data['school_id'] = $schooltipsOne['school_id'];
                        $data['is_schoolenter'] = 0;
                        $data['schoolenter_createtime'] = time();
                        $data['schoolenter_updatetime'] = time();
                        $this->DataControl->insertData('crm_client_schoolenter', $data);

                        $trackData = array();
                        $trackData['client_id'] = $clientOne['client_id'];
                        $trackData['marketer_id'] = 0;
                        $trackData['marketer_name'] = "系统";
                        $trackData['track_intention_level'] = "3";
                        $trackData['track_linktype'] = "系统分配";
                        $trackData['track_note'] = "由于名单未分配校区，由系统自动检索关键词“{$schooltipsOne['schooltips_name']}”匹配，分配至校园进行跟踪。";
                        $trackData['track_createtime'] = time();
                        $trackData['track_type'] = 1;
                        $trackData['track_initiative'] = 0;
                        $this->DataControl->insertData('crm_client_track', $trackData);

                        $clientData = array();
                        /*if($clientOne['client_createtime'] < time()-3600*24*30*6){
                            $clientData['client_createtime'] = time()-3600*24*3;
                        }*/
                        $clientData['client_updatetime'] = time();
                        $this->DataControl->updateData("crm_client", "client_id='{$clientOne['client_id']}'", $clientData);

                        echo "{$schooltipsOne['schooltips_name']}附近名单{$clientOne['client_cnname']},手机号码{$clientOne['client_mobile']}已分配到校区<br />";
                    }
                }

                echo "<p>------{$schooltipsOne['schooltips_name']}附近名单已自动分配完毕！-------</p>";
            }
        }


    }

    //获取CRM学校省份数据
    function getSchoolAreaApi()
    {
        $request = Input('get.', '', 'trim,addslashes');

        $ThreeApi = new \Model\Api\ThreeApiModel();
        $pucArray = $ThreeApi->ThreeVerify($request);
        if ($pucArray) {
            $Model = new \Model\Crm\CommonModel();
            $request['company_id'] = $pucArray['company_id'];
            $dataList = $Model->getGwSchoolAreaApi($request);

            $field = array();
            $field["school_city"] = "学校地址";
            $res = array('error' => '0', 'errortip' => '筛选学校机构地址成功', 'result' => $dataList);
            ajax_return($res);
        } else {
            $res = array('error' => '1', 'errortip' => $ThreeApi->errortip, 'result' => array());
            ajax_return($res);
        }
    }

    //获取CRM校区信息
    function getSchoolApi()
    {
        $request = Input('get.', '', 'trim,addslashes');

        $ThreeApi = new \Model\Api\ThreeApiModel();
        $pucArray = $ThreeApi->ThreeVerify($request);
        if ($pucArray) {
            $Model = new \Model\Crm\CommonModel();
            $request['company_id'] = $pucArray['company_id'];
            $dataList = $Model->getGwSchoolApi($request);

            $field = array();
            $field["school_id"] = "学校id";
            $field["school_branch"] = "校区编号";
            $field["school_shortname"] = "校园简称";
            $field["school_cnname"] = "校园名称称";
            $field["school_enname"] = "检索代码称";
            $field["school_phone"] = "学校联系电话";
            $field["school_address"] = "学校联系地址";

            if ($dataList) {
                $res = array('error' => '0', 'errortip' => '筛选学校机构成功', 'field' => $field, 'result' => $dataList);
            } else {
                $res = array('error' => '1', 'errortip' => '筛选学校机构失败', 'field' => array(), 'result' => array());
            }
            ajax_return($res, $request['language_type']);
        } else {
            $res = array('error' => '1', 'errortip' => $ThreeApi->errortip, 'field' => array(), 'result' => array());
            ajax_return($res);
        }
    }


    /**
     * 获取校区公开课信息
     * author: ling
     * 对应接口文档 0001
     */
    function getClassHourView()
    {

        $request = Input('get.', '', 'trim');
        $ThreeApi = new \Model\Api\ThreeApiModel();
        $pucArray = $ThreeApi->ThreeVerify($request);
        if ($pucArray) {
            $request['company_id'] = $pucArray['company_id'];
            $dataList = $ThreeApi->getSchoolClassHour($request);
            $field = array();
            $field["school_branch"] = "校区编号";
            $field["class_branch"] = "班级编号";
            $field["class_cnname"] = "班级名称";
            $field["hour_id"] = "课时编号";
            $field["hour_name"] = "课时名称";
            $field["hour_day"] = "上课日期";
            $field["hour_starttime"] = "上课时间";
            $field["hour_endtime"] = "下课时间";
            $field["hour_classtimes"] = "上课秒数";
            $field["hour_way"] = "上课方式";
            $field["staffer_cnname"] = "主教姓名";
            $field["is_ohhour"] = "是否公开课";
            $field["hour_ischeckingname"] = "课时状态";

            if ($dataList) {
                $res = array('error' => '0', 'errortip' => '获取校公开课成功', 'field' => $field, 'result' => $dataList);
            } else {
                $res = array('error' => '0', 'errortip' => '暂无公开课信息', 'field' => array(), 'result' => array());
            }
            ajax_return($res, $request['language_type']);
        } else {
            $res = array('error' => '1', 'errortip' => $ThreeApi->errortip, 'field' => array(), 'result' => array());
            ajax_return($res);
        }
    }

    /**
     * 预约学校公开课
     * author: ling
     * 对应接口文档 0001
     */
    function bookClassHourApi()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $ThreeApi = new \Model\Api\ThreeApiModel();
        $pucArray = $ThreeApi->ThreeVerify($request);
        if ($pucArray) {
            $request['company_id'] = $pucArray['company_id'];
            $dataList = $ThreeApi->bookClassHourApi($request);
            $field = array();
            $field["audition_id"] = "试听ID";
            $field["client_id"] = "客户唯一ID";
            $field["class_cnname"] = "试听班级名称";
            $field["hour_name"] = "课时名称";
            $field["hour_day"] = "上课日期";
            $field["hour_starttime"] = "上课时间";
            $field["hour_endtime"] = "下课时间";
            if ($dataList) {
                $res = array('error' => '0', 'errortip' => '预约公开课成功', 'field' => $field, 'result' => $dataList);
            } else {
                $res = array('error' => '1', 'errortip' => $ThreeApi->errortip, 'field' => array(), 'result' => array());
            }
            ajax_return($res, $request['language_type']);
        } else {
            $res = array('error' => '1', 'errortip' => $ThreeApi->errortip, 'field' => array(), 'result' => array());
            ajax_return($res);
        }
    }


    /**
     * 取消预约课
     * author: ling
     * 对应接口文档 0001
     */
    function cancelClassHourApi()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $ThreeApi = new \Model\Api\ThreeApiModel();
        $pucArray = $ThreeApi->ThreeVerify($request);
        if ($pucArray) {
            $request['company_id'] = $pucArray['company_id'];
            $ThreeApi->cancelClassHourApi($request);
            $res = array('error' => $ThreeApi->error, 'errortip' => $ThreeApi->errortip, 'field' => array(), 'result' => array());
            ajax_return($res, $request['language_type']);
        }else {
            $res = array('error' => '1', 'errortip' => $ThreeApi->errortip, 'field' => array(), 'result' => array());
            ajax_return($res);
        }
    }


    /**
     * 预约学校柜询
     * author: ling
     * 对应接口文档 0001
     */
    function bookSchoolvisitApi()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $ThreeApi = new \Model\Api\ThreeApiModel();
        $pucArray = $ThreeApi->ThreeVerify($request);
        if ($pucArray) {
            $request['company_id'] = $pucArray['company_id'];
            $dataList = $ThreeApi->bookSchoolvisitApi($request);
            $field = array();
            $field["invite_id"] = "柜询ID";
            $field["client_id"] = "客户唯一ID";
            $field["invite_visittime"] = "邀约到访时间";
            if ($dataList) {
                $res = array('error' => '0', 'errortip' => '预约邀约成功', 'field' => $field, 'result' => $dataList);
            } else {
                $res = array('error' => '1', 'errortip' => $ThreeApi->errortip, 'field' => array(), 'result' => array());
            }
            ajax_return($res, $request['language_type']);
        } else {
            $res = array('error' => '1', 'errortip' => $ThreeApi->errortip, 'field' => array(), 'result' => array());
            ajax_return($res);
        }
    }

    /**
     * 取消预约学校柜询
     * author: ling
     * 对应接口文档 0001
     */
    function cancelSchoolvisitApi()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $ThreeApi = new \Model\Api\ThreeApiModel();
        $pucArray = $ThreeApi->ThreeVerify($request);
        if ($pucArray) {
            $request['company_id'] = $pucArray['company_id'];
            $ThreeApi->cancelSchoolvisitApi($request);
            $res = array('error' => $ThreeApi->error, 'errortip' => $ThreeApi->errortip, 'field' => array(), 'result' => array());
            ajax_return($res, $request['language_type']);
        }else {
            $res = array('error' => '1', 'errortip' => $ThreeApi->errortip, 'field' => array(), 'result' => array());
            ajax_return($res);
        }
    }


    /**
     * 获取集团的招生课程
     * author: ling
     * 对应接口文档 0001
     */
    function getCourseApi(){
        $request = Input('get.', '', 'trim,addslashes');
        $ThreeApi = new \Model\Api\ThreeApiModel();
        $pucArray = $ThreeApi->ThreeVerify($request);
        if ($pucArray) {
            $request['company_id'] = $pucArray['company_id'];
            $dataList = $ThreeApi->getComCourseApi($request);
            $field = array();
            $field["course_branch"] = "课程编号";
            $field["course_cnname"] = "课程名称";
            $field["coursecat_branch"] = "班种编号";
            $field["course_classtimes"] = "课程时数";
            $field["course_img"] = "课程图片";
            if ($dataList) {
                $res = array('error' => '0', 'errortip' => '获取课程信息成功', 'field' => $field, 'result' => $dataList);
            } else {
                $res = array('error' => '1', 'errortip' => '暂无课程信息', 'field' => array(), 'result' => array());
            }
            ajax_return($res, $request['language_type']);
        } else {
            $res = array('error' => '1', 'errortip' => $ThreeApi->errortip, 'field' => array(), 'result' => array());
            ajax_return($res);
        }
    }


    /**
     * 用户跟踪信息更新
     * author: ling
     * 对应接口文档 0001
     */
    function  updataClientTrackApi(){
        $request = Input('post.', '', 'trim,addslashes');
        $ThreeApi = new \Model\Api\ThreeApiModel();
        $pucArray = $ThreeApi->ThreeVerify($request);
        if ($pucArray) {
            $request['company_id'] = $pucArray['company_id'];
            $ThreeApi->updataClientTrackApi($request);
            $res = array('error' => $ThreeApi->error, 'errortip' => $ThreeApi->errortip, 'field' => array(), 'result' => array());
            ajax_return($res, $request['language_type']);
        }else {
            $res = array('error' => '1', 'errortip' => $ThreeApi->errortip, 'field' => array(), 'result' => array());
            ajax_return($res);
        }

    }

//    function couponsLiView(){
//        $couponsList = $this->DataControl->selectClear("SELECT c.apply_id FROM smc_student_coupons AS c WHERE c.coupons_reason = 'BELLA-20201210导入' AND c.coupons_isuse = '0'");
//        if($couponsList){
//            foreach($couponsList as $couponsOne){
//                if(!$this->DataControl->getFieldOne("smc_student_coupons_xxx_applycourse", "applycourse_id" , "apply_id = '{$couponsOne['apply_id']}' AND course_id = '434'")){
//                    $datas = array();
//                    $datas['course_id'] = '434';
//                    $datas['apply_id'] = $couponsOne['apply_id'];
//                    $this->DataControl->insertData('smc_student_coupons_xxx_applycourse', $datas);
//                }
//                if(!$this->DataControl->getFieldOne("smc_student_coupons_xxx_applycourse", "applycourse_id" , "apply_id = '{$couponsOne['apply_id']}' AND course_id = '70984'")){
//                    $datas = array();
//                    $datas['course_id'] = '70984';
//                    $datas['apply_id'] = $couponsOne['apply_id'];
//                    $this->DataControl->insertData('smc_student_coupons_xxx_applycourse', $datas);
//                }
//                if(!$this->DataControl->getFieldOne("smc_student_coupons_xxx_applycourse", "applycourse_id" , "apply_id = '{$couponsOne['apply_id']}' AND course_id = '70986'")){
//                    $datas = array();
//                    $datas['course_id'] = '70986';
//                    $datas['apply_id'] = $couponsOne['apply_id'];
//                    $this->DataControl->insertData('smc_student_coupons_xxx_applycourse', $datas);
//                }
//            }
//        }
//    }


    //园展校导入名单
    function excelCrmKidfromView(){
        ini_set('max_execution_time', '0');
        $this->c = "111";
        $excelArray = execl_to_array(ROOT_PATH."dabancrm1.xlsx",
            array(
                '校园编号'=>'school_branch',
                '中文名'=>'client_cnname',
                '英文名'=>'client_enname',
                '性别'=>'client_sex',
                '出生日期'=>'client_birthday',
                '身份证号码'=>'client_card',
                '家长姓名'=>'client_patriarchname',
                '手机号码'=>'client_mobile',
                '园跟进日期'=>'yuan_day',
                '园跟进老师'=>'yuan_name',
                '园跟进记录'=>'yuan_note',
                '课程'=>'kecheng',
                '校跟进日期'=>'xiao_day',
                '校跟进老师'=>'xiao_name',
                '校跟进记录'=>'xiao_note',
                '标签'=>'client_tag'));
        array_shift($excelArray);
        if($excelArray){
            foreach($excelArray as $sqlarrayvar){
                $thirdpartycallOne = $this->DataControl->getFieldOne("crm_thirdpartycall", "thirdpartycall_id" , "client_mobile = '{$sqlarrayvar['client_mobile']}' and company_id = '8888'");
                if($thirdpartycallOne){
                    echo "{$sqlarrayvar['client_mobile']}名单已存在<br />";
                }else{
                    $datas = array();
                    $datas['company_id'] = '8888';
                    $datas['school_branch'] = $sqlarrayvar['school_branch'];
                    $datas['client_cnname'] = $sqlarrayvar['client_cnname'];
                    $datas['client_enname'] = $sqlarrayvar['client_enname'];
                    $datas['client_sex'] = $sqlarrayvar['client_sex'];
                    if(strpos($sqlarrayvar['client_birthday'],'-') !== false){
                        $sqlarrayvar['client_birthday'] = str_replace("-","",$sqlarrayvar['client_birthday']);
                        $sqlarrayvar['client_birthday'] = str_replace(".","",$sqlarrayvar['client_birthday']);
                        $sqlarrayvar['client_birthday'] = date("Y-m-d",strtotime($sqlarrayvar['client_birthday']));
                    }else{
                        if($sqlarrayvar['client_birthday'] > 55568){
                            $sqlarrayvar['client_birthday'] = date("Y-m-d",strtotime($sqlarrayvar['client_birthday']));
                        }else{
                            $sqlarrayvar['client_birthday'] = excelTime($sqlarrayvar['client_birthday']);
                        }
                    }
                    $sqlarrayvar['client_birthday'] == '1970-01-01'? $sqlarrayvar['client_birthday'] = '2015-05-05': $sqlarrayvar['client_birthday'] = $sqlarrayvar['client_birthday'];
                    $datas['client_birthday'] = $sqlarrayvar['client_birthday'];
                    $datas['client_age'] = birthdaytoage($datas['client_birthday']);
                    $datas['client_card'] = $sqlarrayvar['client_card'];
                    $datas['client_patriarchname'] = $sqlarrayvar['client_patriarchname'];
                    $datas['client_mobile'] = $sqlarrayvar['client_mobile'];
                    $datas['client_channelone'] = '园展校';
                    $datas['client_channeltwo'] = '园展校';
                    $datas['client_channelthree'] = '搜集导入';
                    $datas['client_tag'] = $sqlarrayvar['client_tag'];
                    $traceArray = array();
                    if($sqlarrayvar['yuan_name'] !==''){
                        $traceOne = array();
                        $traceOne['marketer_name'] = $sqlarrayvar['yuan_name'];
                        $traceOne['track_note'] = $sqlarrayvar['yuan_note'];
                        $traceOne['track_createtime'] = excelTime($sqlarrayvar['yuan_day']);
                        $traceArray[] = $traceOne;
                    }

                    if($sqlarrayvar['xiao_name'] !==''){
                        $traceOne = array();
                        $traceOne['marketer_name'] = $sqlarrayvar['xiao_name'];
                        $traceOne['track_note'] = $sqlarrayvar['xiao_note'];
                        $traceOne['track_createtime'] = excelTime($sqlarrayvar['xiao_day']);
                        $traceArray[] = $traceOne;
                    }

                    $datas['client_tracenote'] = json_encode($traceArray,JSON_UNESCAPED_UNICODE);
                    $datas['client_remark'] = "Bella搜集园展校名单批量导入";
                    $this->DataControl->insertData('crm_thirdpartycall', $datas);
                }
            }
        }
    }


    function excelCrmliView(){
        ini_set('max_execution_time', '0');
        $this->c = "111";
        $sqlarray = execl_to_array(ROOT_PATH."baoshan.xlsx",array('姓名'=>'client_cnname','性别'=>'client_sex','出生日期'=>'client_birthday','家长姓名'=>'client_patriarchname','手机号码'=>'client_mobile','居住地址'=>'client_address'));
        array_shift($sqlarray);
        if($sqlarray){
            foreach($sqlarray as $sqlarrayvar){
                $thirdpartycallOne = $this->DataControl->getFieldOne("crm_thirdpartycall", "thirdpartycall_id" , "client_mobile = '{$sqlarrayvar['client_mobile']}' and company_id = '8888'");
                if($thirdpartycallOne){
                    echo "{$sqlarrayvar['client_mobile']}名单已存在<br />";
                }else{
                    $datas = array();
                    $datas['company_id'] = '8888';
                    $datas['client_cnname'] = $sqlarrayvar['client_cnname'];
                    $datas['client_birthday'] = substr(excelTime($sqlarrayvar['client_birthday']),10);
                    $datas['client_age'] = birthdaytoage($datas['client_birthday']);
                    $datas['client_address'] = $sqlarrayvar['client_address'];
                    $datas['client_patriarchname'] = $sqlarrayvar['client_patriarchname'];
                    $datas['client_mobile'] = $sqlarrayvar['client_mobile'];
                    $datas['client_channelone'] = '异业合作';
                    $datas['client_channeltwo'] = '异业购买';
                    $datas['client_channelthree'] = '商务合作';
                    $datas['client_sex'] = $sqlarrayvar['client_sex'];
                    $datas['client_remark'] = "商务合作异业置换名单";
                    $this->DataControl->insertData('crm_thirdpartycall', $datas);
                }
            }
        }
    }

    /**渠道名单导入**/
    function excelCrmchannelView(){
        ini_set('max_execution_time', '0');
        $this->c = "111";
        $excelArray = execl_to_array(ROOT_PATH."qudaocrm.xlsx",
            array(
                '校区编号'=>'school_branch',
                '学员姓名'=>'client_cnname',
                '年龄'=>'client_age',
                '联系方式'=>'client_mobile',
                '客户备注'=>'client_remark',));
        array_shift($excelArray);
        if($excelArray){
            foreach($excelArray as $sqlarrayvar){
                $thirdpartycallOne = $this->DataControl->getFieldOne("crm_thirdpartycall", "thirdpartycall_id"
                    , "client_mobile = '{$sqlarrayvar['client_mobile']}' and company_id = '8888' AND client_channeltwo = '虎年线上活动'");
                if($thirdpartycallOne){
                    echo "{$sqlarrayvar['client_mobile']}名单已存在<br />";
                }else{
                    $datas = array();
                    $datas['company_id'] = '8888';
                    $datas['school_branch'] = $sqlarrayvar['school_branch'];
                    $datas['client_cnname'] = $sqlarrayvar['client_cnname'];
                    $datas['client_age'] = $sqlarrayvar['client_age'];
                    $datas['client_mobile'] = $sqlarrayvar['client_mobile'];
                    $datas['client_channelone'] = '线上推广';
                    $datas['client_channeltwo'] = '虎年线上活动';
                    $datas['client_channelthree'] = '戚总18活动导入';
                    $datas['client_remark'] = $sqlarrayvar['client_remark'];
                    $this->DataControl->insertData('crm_thirdpartycall', $datas);
                }
            }
        }
    }


    function excelCrmbirthdayView(){
        ini_set('max_execution_time', '0');
        $this->c = "111";
        $sqlarray = execl_to_array(ROOT_PATH."minhang.xlsx",array('姓名'=>'client_cnname','性别'=>'client_sex','出生日期'=>'client_birthday','家长姓名'=>'client_patriarchname','手机号码'=>'client_mobile','居住地址'=>'client_address'));
        array_shift($sqlarray);
        if($sqlarray){
            foreach($sqlarray as $sqlarrayvar){
                if (checkMobile($sqlarrayvar['client_mobile'])) {
                    $datas = array();
                    $datas['client_birthday'] = substr(excelTime($sqlarrayvar['client_birthday']),0,10);
                    $datas['client_age'] = birthdaytoage($datas['client_birthday']);
                    $datas['client_updatetime'] = time();
                    $this->DataControl->updateData("crm_client", "client_mobile = '{$sqlarrayvar['client_mobile']}' and company_id = '8888'", $datas);

                    echo "{$sqlarrayvar['client_mobile']}名单生日{$datas['client_birthday']}已修复<br />";
                }

                /*$thirdpartycallOne = $this->DataControl->getFieldOne("crm_thirdpartycall", "thirdpartycall_id" , "client_mobile = '{$sqlarrayvar['client_mobile']}' and company_id = '8888'");
                if($thirdpartycallOne){
                    echo "{$sqlarrayvar['client_mobile']}名单已存在<br />";
                }else{
                    $datas = array();
                    $datas['company_id'] = '8888';
                    $datas['client_cnname'] = $sqlarrayvar['client_cnname'];
                    $datas['client_birthday'] = substr(excelTime($sqlarrayvar['client_birthday']),10);
                    $datas['client_age'] = birthdaytoage($datas['client_birthday']);
                    $datas['client_address'] = $sqlarrayvar['client_address'];
                    $datas['client_patriarchname'] = $sqlarrayvar['client_patriarchname'];
                    $datas['client_mobile'] = $sqlarrayvar['client_mobile'];
                    $datas['client_channelone'] = '异业合作';
                    $datas['client_channeltwo'] = '异业购买';
                    $datas['client_channelthree'] = '商务合作';
                    $datas['client_sex'] = $sqlarrayvar['client_sex'];
                    $datas['client_remark'] = "商务合作异业置换名单";
                    $this->DataControl->insertData('crm_thirdpartycall', $datas);
                }*/
            }
        }
    }


    //第三方名单初始化导入
    function excelCrmStepOneView(){
        $callList = $this->DataControl->selectClear("select c.* from crm_thirdpartycall as c
where c.company_id = '8888' AND c.client_channelthree = '戚总18活动导入' AND c.client_tracestatus = '0' limit 0,100");
        if($callList) {
            foreach ($callList as $callOne) {
                $familyOne = $this->DataControl->selectOne("SELECT f.family_id,s.student_cnname,s.student_branch FROM smc_student_family AS f ,smc_student AS s
 WHERE f.student_id = s.student_id AND f.family_mobile = '{$callOne['client_mobile']}' and s.company_id = '8888' ORDER BY s.student_branch DESC limit 0,1");
                if ($familyOne) {
                    $data = array();
                    $data['client_tracestatus'] = '-2';
                    $this->DataControl->updateData("crm_thirdpartycall", "thirdpartycall_id = '{$callOne['thirdpartycall_id']}'", $data);
                    echo "{$callOne['client_mobile']}导入失败,家长已存在 <br/>";
                }else{
                    $clientOne = $this->DataControl->getFieldOne("crm_client", "client_id" , "client_mobile = '{$callOne['client_mobile']}' and company_id = '8888'");
                    if($clientOne){
                        $data = array();
                        $data['client_tracestatus'] = '-1';
                        $this->DataControl->updateData("crm_thirdpartycall", "thirdpartycall_id = '{$callOne['thirdpartycall_id']}'", $data);
                        echo "{$callOne['client_mobile']}导入失败，客户已存在 <br/>";
                    }else{
                        if (!checkMobile($callOne['client_mobile'])) {
                            $data = array();
                            $data['client_tracestatus'] = '-3';
                            $this->DataControl->updateData("crm_thirdpartycall", "thirdpartycall_id = '{$callOne['thirdpartycall_id']}'", $data);
                            echo "{$callOne['client_mobile']}导入失败，手机号码错误 <br/>";
                        }else{
                            $data = array();
                            $data['client_cnname'] = $callOne['client_cnname'];
                            $data['client_sex'] = $callOne['client_sex'];
                            $data['client_age'] = $callOne['client_age'];
                            $data['client_birthday'] = $callOne['client_birthday'];
                            $data['company_id'] = $callOne['company_id'];
                            $data['channel_id'] = '560';
                            $data['client_source'] = '线上推广';
                            $data['client_soursename'] = '2021年活动';
                            $data['client_address'] = $callOne['client_address'];
                            $data['client_remark'] = $callOne['client_remark'];
                            $data['client_mobile'] = $callOne['client_mobile'];
                            $data['client_updatetime'] = time();
                            $data['client_createtime'] = time();
                            $data['client_isfromgmc'] = '0';
                            $data['client_isnewtip'] = '1';
                            $id = $this->DataControl->insertData("crm_client", $data);
                            if ($id) {
                                $trackData = array();
                                $trackData['client_id'] = $id;
                                $trackData['marketer_id'] = '0';
                                $trackData['marketer_name'] = '系统管理员';
                                $trackData['track_validinc'] = 1;
                                $trackData['track_linktype'] = '名单导入';
                                $trackData['track_note'] = "由集团管理中心导入名单,新建客户信息";
                                $trackData['track_createtime'] = time();
                                $trackData['track_type'] = 1;
                                $trackData['track_initiative'] = 1;
                                $this->DataControl->insertData('crm_client_track', $trackData);
                                $data = array();
                                $data['client_tracestatus'] = '2';
                                $this->DataControl->updateData("crm_thirdpartycall", "thirdpartycall_id = '{$callOne['thirdpartycall_id']}'", $data);
                                echo "{$callOne['client_mobile']}导入成功 <br/>";
                            }else{
                                echo "{$callOne['client_mobile']}导入失败，Sql错误 <br/>";
                            }
                        }
                    }
                }
            }
        }
        echo '<script language="javascript" type="text/javascript">
var i = 1;
var intervalid;
intervalid = setInterval("fun()", 1000);
function fun() {
	if (i == 0) {
		window.location.href = "/Crm/excelCrmStepOne";
		clearInterval(intervalid);
	}
	document.getElementById("mes").innerHTML = i;
	i--;
}
</script>
<div id="error">
	<p>将在 <span id="mes">2</span> 秒钟后返回首页！</p>
</div> ';
    }

    //第三方名单二次处理
    function excelCrmStepTwoView(){
        $callList = $this->DataControl->selectClear("select c.* from crm_thirdpartycall as c where c.company_id = '8888'
AND c.client_channelthree = '戚总18活动导入' AND c.client_tracestatus = '-1' limit 0,500");
        if($callList) {
            foreach ($callList as $callOne) {
                if (!checkMobile($callOne['client_mobile'])) {
                    $data = array();
                    $data['client_tracestatus'] = '-3';
                    $this->DataControl->updateData("crm_thirdpartycall", "thirdpartycall_id = '{$callOne['thirdpartycall_id']}'", $data);
                }else{
                    $clientOne = $this->DataControl->getFieldOne("crm_client", "client_id,client_source,channel_id,client_tracestatus,client_remark"
                        , "client_mobile = '{$callOne['client_mobile']}' and company_id = '8888'");
                    if($clientOne['channel_id'] == '275' && $clientOne['client_tracestatus'] <= '0'){
                        $data = array();
                        $data['client_cnname'] = $callOne['client_cnname'];
                        $data['channel_id'] = '560';
                        $data['client_source'] = '线上推广';
                        $data['client_remark'] = $clientOne['client_remark']."，同步线上活动名单";
                        $data['client_updatetime'] = time();
                        $data['client_createtime'] = time();
                        $data['client_isfromgmc'] = '0';
                        $data['client_isnewtip'] = '1';
                        if ($this->DataControl->updateData("crm_client","client_id = '{$clientOne['client_id']}'", $data)) {
                            $trackData = array();
                            $trackData['client_id'] = $clientOne['client_id'];
                            $trackData['marketer_id'] = '0';
                            $trackData['marketer_name'] = '系统管理员';
                            $trackData['track_validinc'] = 1;
                            $trackData['track_linktype'] = '名单导入';
                            $trackData['track_note'] = "更新名单状态，重新激活需要CC邀约跟踪";
                            $trackData['track_createtime'] = time();
                            $trackData['track_type'] = 1;
                            $trackData['track_initiative'] = 1;
                            $this->DataControl->insertData('crm_client_track', $trackData);
                            $data = array();
                            $data['client_tracestatus'] = '2';
                            $this->DataControl->updateData("crm_thirdpartycall", "thirdpartycall_id = '{$callOne['thirdpartycall_id']}'", $data);
                            echo "{$callOne['client_mobile']}导入成功 <br/>";
                        }else{
                            echo "{$callOne['client_mobile']}导入失败 <br/>";
                        }
                    }else{
                        $data = array();
                        $data['client_tracestatus'] = '-5';
                        $this->DataControl->updateData("crm_thirdpartycall", "thirdpartycall_id = '{$callOne['thirdpartycall_id']}'", $data);
                        echo "{$callOne['client_mobile']}名单状态不对 <br/>";
                    }
                }
            }
        }
        echo '<script language="javascript" type="text/javascript">
var i = 1;
var intervalid;
intervalid = setInterval("fun()", 1000);
function fun() {
	if (i == 0) {
		window.location.href = "/Crm/excelCrmStepTwo";
		clearInterval(intervalid);
	}
	document.getElementById("mes").innerHTML = i;
	i--;
}
</script>
<div id="error">
	<p>将在 <span id="mes">2</span> 秒钟后返回首页！</p>
</div> ';
    }


    //导出百度竞价招生名单接口
    function ExportClientApi(){
        $request = Input('get.', '', 'trim,addslashes');

        $datawhere = "1";
        if(isset($request['starttime']) && $request['starttime'] !== ''){
            $starttime = strtotime($request['starttime']);
            $datawhere .= " and c.client_createtime >= '{$starttime}'";
        }
        if(isset($request['endtime']) && $request['endtime'] !== ''){
            $endtime = strtotime($request['endtime'].' 23:59:59');
            $datawhere .= " and c.client_createtime <= '{$endtime}'";
        }
        if(isset($request['city']) && $request['city'] !== ''){
            $city = $this->DataControl->getFieldOne("smc_code_region","region_id","region_level = '3' and (region_enname like '%{$request['city']}%')");
            $datawhere .= " and c.city_id = '{$city['region_id']}'";
        }

        $sql = "SELECT
                    c.client_id,c.client_cnname,c.client_mobile,c.client_age,c.client_frompage,c.client_remark,c.client_source_url,c.client_term,c.client_ip,c.client_keyword_id,c.client_tracestatus,c.client_createtime,
                    (SELECT l.positivelog_time FROM crm_client_positivelog as l WHERE l.client_id = c.client_id ORDER BY l.positivelog_id DESC LIMIT 1) AS positivelog_time,
                    (SELECT r.region_name FROM smc_code_region as r WHERE r.region_id = c.city_id) as region_name
                FROM
                    crm_client as c
                WHERE
                    c.company_id = '8888' AND c.channel_id = '260' AND {$datawhere}";

        $client = $this->DataControl->selectClear($sql);

        if (!$client) {
            $this->error = true;
            $this->errortip = "暂无数据";
            return false;
        }

        $outexceldate = array();
        if ($client) {
            $outexceldate = array();
            $status = array("0" => "待跟踪", "1" => "持续跟踪", "2" => "已柜询", "3" => "已试听", "4" => "已转正", "-1" => "无意向", "-2" => "无效名单");
            foreach ($client as $dateexcelvar) {
                $datearray = array();
//                $datearray['client_cnname'] = $dateexcelvar['client_cnname'];
                $datearray['client_createtime'] = date("Y-m-d H:i:s",$dateexcelvar['client_createtime']);
                $datearray['client_mobile'] = substr_replace($dateexcelvar['client_mobile'],'****',3,4);
                $datearray['client_remark'] = $dateexcelvar['client_remark'];
//                $datearray['client_age'] = $dateexcelvar['client_age'];
//                $datearray['client_id'] = $dateexcelvar['client_id'];
                $datearray['client_frompage'] = $dateexcelvar['client_frompage'];
                $datearray['client_source_url'] = $dateexcelvar['client_source_url'];
                $datearray['client_term'] = $dateexcelvar['client_term'];
                $datearray['client_ip'] = $dateexcelvar['client_ip'];
                $datearray['client_tracestatus'] = $status[$dateexcelvar['client_tracestatus']];
                $datearray['positivelog_time'] = $dateexcelvar['positivelog_time'];
//                if($dateexcelvar['region_name']){
//                    $datearray['region_name'] = $dateexcelvar['region_name'];
//                }else{
//                    $datearray['region_name'] = '全国';
//                }
                $outexceldate[] = $datearray;
            }
        }

        $excelheader = array('提交时间', '手机号码', '名单备注', '来源页面', '来源页面url链接', '端口（PC/移动）', 'IP地址', '名单状态', '报名时间');
        $excelfileds = array('client_createtime', 'client_mobile', 'client_remark', 'client_frompage', 'client_source_url', 'client_term', 'client_ip', 'client_tracestatus', 'positivelog_time');
        query_to_excel($excelheader, $outexceldate, $excelfileds, "导出名单列表.xlsx");

    }

    //重复名单删除
    function reuseCrmDelView(){
        $clientList = $this->DataControl->selectClear("SELECT
	c.client_id,
	e.school_id,
	e.is_enterstatus,
	c.client_mobile,
	c.client_source,
	c.client_tracestatus
FROM
	crm_client AS c,
	crm_client_schoolenter AS e
WHERE
	c.company_id = '8888'
AND c.client_id = e.client_id
AND e.is_enterstatus = '1'
AND c.client_tracestatus < 0
AND c.client_mobile IN (
	SELECT
		m.client_mobile
	FROM
		crm_client AS m
	WHERE
		m.company_id = '8888'
	GROUP BY
		m.client_mobile,
		m.client_cnname
	HAVING
		COUNT(m.client_id) > 1
) LIMIT 0,10");
        if($clientList) {
            foreach ($clientList as $clientOne) {
                $data = array();
                $data['staffer_id'] = '12357';
                $data['token'] = 'MzAxZDM2ZTA5MjVkODY0YjM4MWMyYmEyOTIwYzg3YmQ=';
                $data['company_id'] = '8888';
                $data['client_id'] = $clientOne['client_id'];
                $data['school_id'] = $clientOne['school_id'];
                $apiSting = request_by_curl("https://gmcapi.kedingdang.com/CrmClient/delRepeatAction", dataEncode($data), 'get');
                $ApiArray = json_decode($apiSting, "1");
                echo "{$clientOne['client_mobile']}|{$clientOne['client_tracestatus']}|{$ApiArray['errortip']}<br />";
            }
        }
        echo '<script language="javascript" type="text/javascript">
var i = 1;
var intervalid;
intervalid = setInterval("fun()", 1000);
function fun() {
	if (i == 0) {
		window.location.href = "/Crm/reuseCrmDel";
		clearInterval(intervalid);
	}
	document.getElementById("mes").innerHTML = i;
	i--;
}
</script>
<div id="error">
	<p>将在 <span id="mes">2</span> 秒钟后返回首页！</p>
</div> ';
    }

    function crmCoursetypeView(){
        $clientList = $this->DataControl->selectClear("SELECT
	c.client_id,
	t.coursetype_id,
	t.coursecat_id
FROM
	crm_client c
LEFT JOIN crm_client_track AS t ON c.client_id = t.client_id
AND t.track_isactive = '1'
AND t.coursetype_id <> '0'
AND t.coursecat_id <> '0'
LEFT JOIN crm_client_intention i ON c.client_id = i.client_id
WHERE
	c.company_id = '8888'
AND c.client_tracestatus >= 0
AND c.client_intention_level > 0
AND t.track_id IS NOT NULL
AND i.intention_id IS NULL
GROUP BY
	c.client_id,
	t.coursetype_id,
	t.coursecat_id
ORDER BY
	c.client_id DESC
limit 0,500");
        if($clientList) {
            $clientNum = 0;
            foreach ($clientList as $clientOne) {
                $datas = array();
                $datas['client_id'] = $clientOne['client_id'];
                $datas['coursetype_id'] = $clientOne['coursetype_id'];
                $datas['coursecat_id'] = $clientOne['coursecat_id'];
                $datas['intention_updatetime'] = time();
                $this->DataControl->insertData("crm_client_intention", $datas);
                $clientNum ++;
            }
        }
        echo "{$clientNum}更新完毕";
        echo '<script language="javascript" type="text/javascript">
var i = 1;
var intervalid;
intervalid = setInterval("fun()", 1000);
function fun() {
	if (i == 0) {
		window.location.href = "/Crm/crmCoursetype";
		clearInterval(intervalid);
	}
	document.getElementById("mes").innerHTML = i;
	i--;
}
</script>
<div id="error">
	<p>将在 <span id="mes">2</span> 秒钟后返回首页！</p>
</div> ';
    }

    function crmTmkUserbingView()
    {
        /*$clientList = $this->DataControl->selectClear("SELECT
	client_id,
	client_gmcdistributionstatus,client_createtime
FROM
	crm_client
WHERE
	(
		client_id IN (
			SELECT
				i.client_id
			FROM
				crm_client_invite AS i
			WHERE
				i.outthree_bookid <> ''
			GROUP BY
				i.client_id
		)
		OR client_id IN (
			SELECT
				a.client_id
			FROM
				crm_client_audition AS a
			WHERE
				a.outthree_bookid <> ''
			GROUP BY
				a.client_id
		)
	)
AND client_id NOT IN (
	SELECT
		p.client_id
	FROM
		crm_client_principal AS p
	WHERE
		p.school_id = '0'
) LIMIT 0,100");
        if ($clientList) {
            $clientNum = 0;
            foreach ($clientList as $clientOne) {
                $datas = array();
                $datas['client_id'] = $clientOne['client_id'];
                $datas['marketer_id'] = '201';
                $datas['principal_ismajor'] = '1';
                $datas['principal_leave'] = '0';
                $datas['principal_createtime'] = $clientOne['client_createtime'];
                $datas['principal_updatatime'] = time();
                $this->DataControl->insertData('crm_client_principal', $datas);
                $clientNum++;
            }
        }*/

        echo "{$clientNum}更新完毕";
        echo '<script language="javascript" type="text/javascript">
var i = 1;
var intervalid;
intervalid = setInterval("fun()", 1000);
function fun() {
	if (i == 0) {
		window.location.href = "/Crm/crmTmkUserbing";
		clearInterval(intervalid);
	}
	document.getElementById("mes").innerHTML = i;
	i--;
}
</script>
<div id="error">
	<p>将在 <span id="mes">2</span> 秒钟后返回首页！</p>
</div> ';
    }


    //获取课叮铛  园务系统 地推接口授权秘钥
    function getAuthpriv(){
        $parameter = array();
        $parameter['timesteps'] = time();
        $parameter['apiuser_code'] = 'dituibao1';
        $parameter['company_id'] = '8888';

        $aeskey = 'jidebaoDitui2022';
        $aesiv = 'jdbTidui7orRDzTa';

        $aes = new \Aesencdec($aeskey, $aesiv);
        $parameterJson = json_encode($parameter, '1');
        $jmsting = $aes->encrypt($parameterJson);//解密

        $result = array();
        $result['timesteps'] = $parameter['timesteps'];
        $result['apiuser_code'] = $parameter['apiuser_code'];
        $result['veytoken'] = $jmsting;
        return $result;
    }

    //账号密码登录判断  ---  地推宝
    function loginPromotionView(){
        $request = Input('post.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if($this->VeryModelNums($pucArray['apiuser_id'],'loginPromotion',$request)){

                if(!$request['mobile']){
                    ajax_return(array('error' => '1', 'errortip' => '用户手机号必须传入！', 'result' => array()));
                }
                if(!$request['pass']){
                    ajax_return(array('error' => '1', 'errortip' => '密码必须传入！', 'result' => array()));
                }

                $proOne = $this->DataControl->selectOne("select promotion_id,promotion_name,promotion_mobile,promotion_pass from crm_ground_promotion where promotion_mobile = '{$request['mobile']}' and company_id='8888' limit 0,1 ");
                if($proOne){
                    if(md5($request['pass']) == $proOne['promotion_pass']){
                        $ispro = array();
                        $ispro['isFrom'] = 'xw';
                        ajax_return(array('error' => '0', 'errortip' => '登录成功', 'result' => $ispro));
                    }else{
                        ajax_return(array('error' => '1', 'errortip' => '您的密码错误', 'result' => array()));
                    }
                }else{

                    //园务账号密码
                    $userAuthpOne = $this->getAuthpriv();

                    $data = array();
                    $data['company_id'] = '8888';
                    $data['timesteps'] = $userAuthpOne['timesteps'];
                    $data['apiuser_code'] = $userAuthpOne['apiuser_code'];
                    $data['veytoken'] = $userAuthpOne['veytoken'];

                    $data['mobile'] = $request['mobile'];
                    $data['pass'] = $request['pass'];

                    $getBackurl = request_by_curl("https://kidapi.kedingdang.com/Crm/loginPromotion", dataEncode($data), "POST");
//                    $getBackurl = request_by_curl("http://kidapi.kidmanageapi102.com/Crm/loginPromotion", dataEncode($data), "POST");
                    $bakData = json_decode($getBackurl, true);
                    if($bakData['error'] == '0'){
                        $ispro = array();
                        $ispro['isFrom'] = 'yw';
                        ajax_return(array('error' => '0', 'errortip' => '登录成功', 'result' => $ispro));
                    }else{
                        ajax_return(array('error' => '1', 'errortip' => $bakData['errortip'], 'result' => array()));
                    }

//                    ajax_return(array('error' => '1', 'errortip' => '您的信息不存在校务系统', 'result' => array()));
                }
            }else{
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        }else{
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }
    //账号验证码登录判断  ---  地推宝
    function loginCodePromotionView(){
        $request = Input('post.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if($this->VeryModelNums($pucArray['apiuser_id'],'loginCodePromotion',$request)){
                $mobile = trim($request['mobile']);
                $verifycode = trim($request['verifycode']);

                if(!$mobile){
                    ajax_return(array('error' => '1', 'errortip' => '用户手机号必须传入！', 'result' => array()));
                }
                if(!$verifycode){
                    ajax_return(array('error' => '1', 'errortip' => '短信验证码必须传入！', 'result' => array()));
                }

                $proOne = $this->DataControl->selectOne("select promotion_id,promotion_name,promotion_mobile,promotion_pass from crm_ground_promotion where promotion_mobile = '{$mobile}' and company_id='8888' limit 0,1 ");
                if($proOne){
                    $sendrz = $this->DataControl->getFieldOne('gmc_mislog', "mislog_sendcode", "mislog_mobile='{$mobile}' and mislog_tilte = '三方地推登录'", "order by mislog_time DESC");
                    if (!$sendrz || $sendrz['mislog_sendcode'] !== $verifycode) {
                        ajax_return(array('error' => '1', 'errortip' => '短信验证码错误!'));
                    } else {
                        $ispro = array();
                        $ispro['isFrom'] = 'xw';
                        ajax_return(array('error' => '0', 'errortip' => '登录成功', 'result' => $ispro));
                    }
                }else{
                    //园务账号密码
                    $userAuthpOne = $this->getAuthpriv();

                    $data = array();
                    $data['company_id'] = '8888';
                    $data['timesteps'] = $userAuthpOne['timesteps'];
                    $data['apiuser_code'] = $userAuthpOne['apiuser_code'];
                    $data['veytoken'] = $userAuthpOne['veytoken'];

                    $data['mobile'] = $mobile;
                    $data['verifycode'] = $verifycode;

                    $getBackurl = request_by_curl("https://kidapi.kedingdang.com/Crm/loginCodePromotion", dataEncode($data), "POST");
//                    $getBackurl = request_by_curl("http://kidapi.kidmanageapi102.com/Crm/loginCodePromotion", dataEncode($data), "POST");
                    $bakData = json_decode($getBackurl, true);
                    if($bakData['error'] == '0'){
                        $ispro = array();
                        $ispro['isFrom'] = 'yw';
                        ajax_return(array('error' => '0', 'errortip' => '登录成功', 'result' => $ispro));
                    }else{
                        ajax_return(array('error' => '1', 'errortip' => $bakData['errortip'], 'result' => array()));
                    }

//                    ajax_return(array('error' => '1', 'errortip' => '您的信息不存在', 'result' => array()));
                }
            }else{
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        }else{
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }
    //重置密码  ---  地推宝
    function resetPromotionPassView(){
        $request = Input('post.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if($this->VeryModelNums($pucArray['apiuser_id'],'resetPromotionPass',$request)){
                $mobile = trim($request['mobile']);
                $verifycode = trim($request['verifycode']);
                $newpass = trim($request['newpass']);

                if(!$mobile){
                    ajax_return(array('error' => '1', 'errortip' => '用户手机号必须传入！', 'result' => array()));
                }
                if(!$newpass){
                    ajax_return(array('error' => '1', 'errortip' => '新密码必须进行设置！', 'result' => array()));
                }
                if(!$verifycode){
                    ajax_return(array('error' => '1', 'errortip' => '短信验证码必须传入！', 'result' => array()));
                }

                $proOne = $this->DataControl->selectOne("select promotion_id,promotion_name,promotion_mobile,promotion_pass from crm_ground_promotion where promotion_mobile = '{$mobile}' and company_id='8888' limit 0,1 ");
                if($proOne){
                    $sendrz = $this->DataControl->getFieldOne('gmc_mislog', "mislog_sendcode", "mislog_mobile='{$mobile}' and mislog_tilte = '三方地推重置密码'", "order by mislog_time DESC");
                    if (!$sendrz || $sendrz['mislog_sendcode'] !== $verifycode) {
                        ajax_return(array('error' => '1', 'errortip' => '短信验证码错误!'));
                    } else {

                        $redata = array();
                        $redata['promotion_pass'] = MD5($newpass);
                        $redata['promotion_bakpass'] = $newpass;
                        $redata['promotion_updatetime'] = time();
                        $this->DataControl->updateData("crm_ground_promotion", "promotion_id='{$proOne['promotion_id']}' and company_id='8888' ", $redata);

                        //去园务找 是否有一样的地推人员也进行重置
                        //园务账号密码
                        $userAuthpOne = $this->getAuthpriv();
                        $data = array();
                        $data['company_id'] = '8888';
                        $data['timesteps'] = $userAuthpOne['timesteps'];
                        $data['apiuser_code'] = $userAuthpOne['apiuser_code'];
                        $data['veytoken'] = $userAuthpOne['veytoken'];

                        $data['mobile'] = $mobile;
                        $data['verifycode'] = 'neibudiaoyong';
                        $data['newpass'] = $newpass;
                        $getBackurl = request_by_curl("https://kidapi.kedingdang.com/Crm/resetPromotionPassTwo", dataEncode($data), "POST");
//                        $getBackurl = request_by_curl("http://kidapi.kidmanageapi102.com/Crm/resetPromotionPassTwo", dataEncode($data), "POST");

                        ajax_return(array('error' => '0', 'errortip' => '密码重置成功', 'result' => array()));
                    }
                }else{

                    //园务账号密码
                    $userAuthpOne = $this->getAuthpriv();

                    $data = array();
                    $data['company_id'] = '8888';
                    $data['timesteps'] = $userAuthpOne['timesteps'];
                    $data['apiuser_code'] = $userAuthpOne['apiuser_code'];
                    $data['veytoken'] = $userAuthpOne['veytoken'];

                    $data['mobile'] = $mobile;
                    $data['verifycode'] = $verifycode;
                    $data['newpass'] = $newpass;

                    $getBackurl = request_by_curl("https://kidapi.kedingdang.com/Crm/resetPromotionPass", dataEncode($data), "POST");
//                    $getBackurl = request_by_curl("http://kidapi.kidmanageapi102.com/Crm/resetPromotionPass", dataEncode($data), "POST");
                    $bakData = json_decode($getBackurl, true);
                    if($bakData['error'] == '0'){
                        ajax_return(array('error' => '0', 'errortip' => '密码重置成功', 'result' => array()));
                    }else{
                        ajax_return(array('error' => '1', 'errortip' => $bakData['errortip'], 'result' => array()));
                    }

//                    ajax_return(array('error' => '1', 'errortip' => '您的信息不存在', 'result' => array()));
                }
            }else{
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        }else{
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }
    //获取手机短信验证码 ---- 地推宝
    function getProMobileCodeView(){
        $request = Input('post.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if($this->VeryModelNums($pucArray['apiuser_id'],'getProMobileCode',$request)){

                $mobile = trim($request['mobile']);
                if(!$mobile){
                    ajax_return(array('error' => '1', 'errortip' => '用户手机号必须传入！', 'result' => array()));
                }

                $proOne = $this->DataControl->selectOne("select promotion_id,promotion_name,promotion_mobile,promotion_pass from crm_ground_promotion where promotion_mobile = '{$mobile}' and company_id='8888' limit 0,1 ");
                if($proOne){
                    //一小时内发送次数
                    $mintime = time() - 3600;
                    $mislognum = $this->DataControl->selectOne("select count(mislog_id) as mislognum from gmc_mislog where mislog_mobile='{$mobile}' and mislog_tilte = '三方地推登录' and mislog_time >= '{$mintime}' limit 0,1 ");
                    if ($mislognum['mislognum'] > 5) {
                        ajax_return(array('error' => '1', 'errortip' => '您的手机已超出发送验证码次数，请联系客服！'));
                    }
                    //最近一次发送时间
                    $sendmisrz = $this->DataControl->getFieldOne('gmc_mislog', "mislog_time", "mislog_mobile='{$mobile}' and mislog_tilte = '三方地推登录'", "order by mislog_time DESC");
                    if ($sendmisrz && (time() - $sendmisrz['mislog_time']) < 60) {
                        ajax_return(array('error' => '1', 'errortip' => '验证码已发送！'));
                    } else {
                        $tilte = "三方地推登录";
                        $sendcode = rand(111111, 999999);
                        setcookie('mislog_sendcode', $sendcode, time() + 1800);
                        $contxt = "用户您好！您的手机验证码为{$sendcode}，请完成验证，如非本人操作，请忽略本短信。";
                        //短信发送
                        if ($this->Sendmisgo($mobile, $contxt, $tilte, $sendcode, '8888')) {
                            ajax_return( array('error' => '0', 'errortip' => '发送成功', "bakfuntion" => "okmotify") );
                        } else {
                            ajax_return( array('error' => '1', 'errortip' => '发送失败!', "bakfuntion" => "errormotify") );
                        }
                    }

                }else{

                    //园务账号密码
                    $userAuthpOne = $this->getAuthpriv();

                    $data = array();
                    $data['company_id'] = '8888';
                    $data['timesteps'] = $userAuthpOne['timesteps'];
                    $data['apiuser_code'] = $userAuthpOne['apiuser_code'];
                    $data['veytoken'] = $userAuthpOne['veytoken'];

                    $data['mobile'] = $mobile;

                    $getBackurl = request_by_curl("https://kidapi.kedingdang.com/Crm/getProMobileCode", dataEncode($data), "POST");
//                    $getBackurl = request_by_curl("http://kidapi.kidmanageapi102.com/Crm/getProMobileCode", dataEncode($data), "POST");
                    $bakData = json_decode($getBackurl, true);
                    if($bakData['error'] == '0'){
                        ajax_return( array('error' => '0', 'errortip' => '发送成功', "bakfuntion" => "okmotify") );
                    }else{
                        ajax_return(array('error' => '1', 'errortip' => $bakData['errortip'], 'result' => array()));
                    }

//                    ajax_return(array('error' => '1', 'errortip' => '您的信息不存在', 'result' => array()));
                }
            }else{
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        }else{
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }
    //忘记密码获取手机短信验证码 ---- 地推宝
    function getProMobilePassCodeView(){
        $request = Input('post.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if($this->VeryModelNums($pucArray['apiuser_id'],'getProMobilePassCode',$request)){

                $mobile = trim($request['mobile']);
                if(!$mobile){
                    ajax_return(array('error' => '1', 'errortip' => '用户手机号必须传入！', 'result' => array()));
                }

                $proOne = $this->DataControl->selectOne("select promotion_id,promotion_name,promotion_mobile,promotion_pass from crm_ground_promotion where promotion_mobile = '{$mobile}' and company_id='8888' limit 0,1 ");
                if($proOne){
                    //一小时内发送次数
                    $mintime = time() - 3600;
                    $mislognum = $this->DataControl->selectOne("select count(mislog_id) as mislognum from gmc_mislog where mislog_mobile='{$mobile}' and mislog_tilte = '三方地推重置密码' and mislog_time >= '{$mintime}' limit 0,1 ");
                    if ($mislognum['mislognum'] > 5) {
                        ajax_return(array('error' => '1', 'errortip' => '您的手机已超出发送验证码次数，请联系客服！'));
                    }
                    //最近一次发送时间
                    $sendmisrz = $this->DataControl->getFieldOne('gmc_mislog', "mislog_time", "mislog_mobile='{$mobile}' and mislog_tilte = '三方地推重置密码'", "order by mislog_time DESC");
                    if ($sendmisrz && (time() - $sendmisrz['mislog_time']) < 60) {
                        ajax_return(array('error' => '1', 'errortip' => '验证码已发送！'));
                    } else {
                        $tilte = "三方地推重置密码";
                        $sendcode = rand(111111, 999999);
                        setcookie('mislog_sendcode', $sendcode, time() + 1800);
                        $contxt = "用户您好！您正在进行重置密码，手机验证码为{$sendcode}，请完成验证，如非本人操作，请忽略本短信。";
                        //短信发送
                        if ($this->Sendmisgo($mobile, $contxt, $tilte, $sendcode, '8888')) {
                            ajax_return( array('error' => '0', 'errortip' => '发送成功', "bakfuntion" => "okmotify") );
                        } else {
                            ajax_return( array('error' => '1', 'errortip' => '发送失败!', "bakfuntion" => "errormotify") );
                        }
                    }

                }else{

                    //园务账号密码
                    $userAuthpOne = $this->getAuthpriv();

                    $data = array();
                    $data['company_id'] = '8888';
                    $data['timesteps'] = $userAuthpOne['timesteps'];
                    $data['apiuser_code'] = $userAuthpOne['apiuser_code'];
                    $data['veytoken'] = $userAuthpOne['veytoken'];

                    $data['mobile'] = $mobile;

                    $getBackurl = request_by_curl("https://kidapi.kedingdang.com/Crm/getProMobilePassCode", dataEncode($data), "POST");
//                    $getBackurl = request_by_curl("http://kidapi.kidmanageapi102.com/Crm/getProMobilePassCode", dataEncode($data), "POST");
                    $bakData = json_decode($getBackurl, true);
                    if($bakData['error'] == '0'){
                        ajax_return( array('error' => '0', 'errortip' => '发送成功', "bakfuntion" => "okmotify") );
                    }else{
                        ajax_return(array('error' => '1', 'errortip' => $bakData['errortip'], 'result' => array()));
                    }

//                    ajax_return(array('error' => '1', 'errortip' => '您的信息不存在', 'result' => array()));
                }
            }else{
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        }else{
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }


    //第三方 名单手机号加密 -- 目前针对的地推宝 --- 临时使用
    function getLsEncryptedPhoneView(){
        $request = Input('get.', '', 'trim,addslashes');
        if(!checkMobile($request['client_mobile'])){
            $data = array();
            $data['apiuser_id'] = 0;
            $data['apimodule_id'] = 0;
            $data['errorlog_apiurl'] = "https://api.kedingdang.com/Crm/getEncryptedPhone";
            $data['errorlog_parameter'] = urldecode(http_build_query($request));
            $data['errorlog_errortips'] = '吴磊跑数据，您传入的手机号码格式不正确!';
            $data['errorlog_createtime'] = time();
            $this->DataControl->insertData("imc_apiuser_errorlog", $data);

            $res = array('error' => '1', 'errortip' => '吴磊跑数据，您传入的手机号码格式不正确!','result' => array());
            ajax_return($res,$request['language_type']);
        }

        $passdata = $this->DataControl->selectOne(" SELECT NEW_NCRYPTP('{$request['client_mobile']}') as passmobile limit 0,1 ");

        $result = array();
        if($passdata['passmobile']){
            $result['passmobile'] = $passdata['passmobile'];
            ajax_return(array('error' => 0, 'errortip' => '手机号加密成功', 'result' => $result));
        }else{
            $result['passmobile'] = '';
            ajax_return(array('error' => 1, 'errortip' => '手机号加密失败', 'result' => $result));
        }
    }
    //第三方 名单手机号加密 -- 目前针对的地推宝
    function getEncryptedPhoneView(){
        $request = Input('post.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if($this->VeryModelNums($pucArray['apiuser_id'],'getEncryptedPhone',$request)){

                if(!checkMobile($request['client_mobile'])){
                    $data = array();
                    $data['apiuser_id'] = $pucArray['apiuser_id'];
                    $data['apimodule_id'] = '7434';
                    $data['errorlog_apiurl'] = "https://api.kedingdang.com/Crm/getEncryptedPhone";
                    $data['errorlog_parameter'] = urldecode(http_build_query($request));
                    $data['errorlog_errortips'] = '您传入的手机号码格式不正确!';
                    $data['errorlog_createtime'] = time();
                    $this->DataControl->insertData("imc_apiuser_errorlog", $data);

                    $res = array('error' => '1', 'errortip' => '您传入的手机号码格式不正确!','result' => array());
                    ajax_return($res,$request['language_type']);
                }

                $passdata = $this->DataControl->selectOne(" SELECT NEW_NCRYPTP('{$request['client_mobile']}') as passmobile limit 0,1 ");

                $result = array();
                if($passdata['passmobile']){
                    $result['passmobile'] = $passdata['passmobile'];
                    ajax_return(array('error' => 0, 'errortip' => '手机号加密成功', 'result' => $result));
                }else{
                    $result['passmobile'] = '';
                    ajax_return(array('error' => 1, 'errortip' => '手机号加密失败', 'result' => $result));
                }
            }else{
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        }else{
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //第三方录入地推人员名单  ---  地推宝
    function addPromotionAction(){
        $request = Input('post.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if($this->VeryModelNums($pucArray['apiuser_id'],'addPromotion',$request)){
                $schoolOneId = '0';
                if(isset($request['school_branch']) && $request['school_branch'] != ''){
                    $brancharray = explode(',',$request['school_branch']);
                    $branchstr = '0';
                    if($brancharray){
                        $branchdata = array();
                        foreach ($brancharray as $branchvar){
                            $branchdata[] = "'".$branchvar."'";
                        }
                        $branchstr = implode(',',$branchdata);
                    }
                    $schoolOne = $this->DataControl->selectOne(" select group_concat(school_id) as school_ids  from smc_school where school_branch in ({$branchstr}) and company_id = '8888' limit 0,1 ");
                    $schoolOneId = $schoolOne['school_ids']?$schoolOne['school_ids']:'0';
                }

                //默认的  吉的堡集团和管理员
                $request['company_id'] = '8888';
                $request['school_id'] = 0;
                $request['api_school_id'] = $schoolOneId;
                $request['marketer_id'] = '15';
                $Model = new \Model\Crm\CommonModel($request);
                if($Model->addPromotionAction($request)){
                    ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $Model->result));
                }else{
                    $data = array();
                    $data['apiuser_id'] = $pucArray['apiuser_id'];
                    $data['apimodule_id'] = '7357';
                    $data['errorlog_apiurl'] = "https://api.kedingdang.com/Crm/addPromotionAction";
                    $data['errorlog_parameter'] = urldecode(http_build_query($request));
                    $data['errorlog_errortips'] = $Model->errortip;
                    $data['errorlog_createtime'] = time();
                    $this->DataControl->insertData("imc_apiuser_errorlog", $data);

                    ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $Model->result));
                }
            }else{
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        }else{
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //第三方地推人员 对应的名单录入  ---  地推宝
    function addPromotionClientAction(){
        $request = Input('post.','','trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if($this->VeryModelNums($pucArray['apiuser_id'],'addPromotionClient',$request)){
                $request['company_id'] = '8888';
                $request['school_id'] = '0';
                $request['marketer_id'] = '15';

                if(!isset($request['promotion_jobnumber']) || $request['promotion_jobnumber'] == ''){
                    $data = array();
                    $data['apiuser_id'] = $pucArray['apiuser_id'];
                    $data['apimodule_id'] = '7362';
                    $data['errorlog_apiurl'] = "https://api.kedingdang.com/Crm/addPromotionClientAction";
                    $data['errorlog_parameter'] = urldecode(http_build_query($request));
                    $data['errorlog_errortips'] = '地推人员编号不能为空！';
                    $data['errorlog_createtime'] = time();
                    $this->DataControl->insertData("imc_apiuser_errorlog", $data);

                    $res = array('error' => '1', 'errortip' => '地推人员编号不能为空！', 'result' => array());
                    ajax_return($res,$request['language_type']);
                }
                if(!isset($request['client_mobile']) && !isset($request['client_passmobile']) ){
                    $data = array();
                    $data['apiuser_id'] = $pucArray['apiuser_id'];
                    $data['apimodule_id'] = '7362';
                    $data['errorlog_apiurl'] = "https://api.kedingdang.com/Crm/addPromotionClientAction";
                    $data['errorlog_parameter'] = urldecode(http_build_query($request));
                    $data['errorlog_errortips'] = '联系手机未传！';
                    $data['errorlog_createtime'] = time();
                    $this->DataControl->insertData("imc_apiuser_errorlog", $data);

                    $res = array('error' => '1', 'errortip' => '联系手机未传！', 'result' => array());
                    ajax_return($res,$request['language_type']);
                }
                if(!isset($request['client_cnname']) || $request['client_cnname'] == '' || ($request['client_mobile'] == '' && $request['client_passmobile'] == '') ){
                    $data = array();
                    $data['apiuser_id'] = $pucArray['apiuser_id'];
                    $data['apimodule_id'] = '7362';
                    $data['errorlog_apiurl'] = "https://api.kedingdang.com/Crm/addPromotionClientAction";
                    $data['errorlog_parameter'] = urldecode(http_build_query($request));
                    $data['errorlog_errortips'] = '宝宝姓名和联系手机不能为空！';
                    $data['errorlog_createtime'] = time();
                    $this->DataControl->insertData("imc_apiuser_errorlog", $data);

                    $res = array('error' => '1', 'errortip' => '宝宝姓名和联系手机不能为空！', 'result' => array());
                    ajax_return($res,$request['language_type']);
                }

                $channelOne = $this->DataControl->getFieldOne("crm_code_channel", "channel_id,channel_isschoolchose,channel_medianame,channel_quality"
                    , "channel_name='{$request['channel_name']}' AND company_id = '{$request['company_id']}'");
                if(!$channelOne || ($channelOne['channel_id'] != '537' && $channelOne['channel_id'] != '91' && $channelOne['channel_id'] != '274' && $channelOne['channel_id'] != '126' && $channelOne['channel_id'] != '92' ) ){
                    $data = array();
                    $data['apiuser_id'] = $pucArray['apiuser_id'];
                    $data['apimodule_id'] = '7362';
                    $data['errorlog_apiurl'] = "https://api.kedingdang.com/Crm/addPromotionClientAction";
                    $data['errorlog_parameter'] = urldecode(http_build_query($request));
                    $data['errorlog_errortips'] = '渠道信息有误！';
                    $data['errorlog_createtime'] = time();
                    $this->DataControl->insertData("imc_apiuser_errorlog", $data);

                    $res = array('error' => '1', 'errortip' => '渠道信息有误！', 'result' => array());
                    ajax_return($res,$request['language_type']);
                }

                if($request['istogmc'] != '1' ) {//1 名单进入集团 ； 0 或者 空 名单那进入学校
                    $schoolOne = $this->DataControl->getFieldOne("smc_school", "school_id", "company_id = '{$request['company_id']}' and school_branch = '{$request['school_branch']}' and school_type = '1' ");
                    if (!$schoolOne) {
                        $data = array();
                        $data['apiuser_id'] = $pucArray['apiuser_id'];
                        $data['apimodule_id'] = '7362';
                        $data['errorlog_apiurl'] = "https://api.kedingdang.com/Crm/addPromotionClientAction";
                        $data['errorlog_parameter'] = urldecode(http_build_query($request));
                        $data['errorlog_errortips'] = '找不到对应的学校，请输入正确的学校编号!';
                        $data['errorlog_createtime'] = time();
                        $this->DataControl->insertData("imc_apiuser_errorlog", $data);

                        $res = array('error' => '1', 'errortip' => '找不到对应的学校，请输入正确的学校编号!', 'result' => array());
                        ajax_return($res, $request['language_type']);
                    }
                }else{
                    if($request['school_branch'] != ''){
                        $data = array();
                        $data['apiuser_id'] = $pucArray['apiuser_id'];
                        $data['apimodule_id'] = '7362';
                        $data['errorlog_apiurl'] = "https://api.kedingdang.com/Crm/addPromotionClientAction";
                        $data['errorlog_parameter'] = urldecode(http_build_query($request));
                        $data['errorlog_errortips'] = '名单进入集团，请不要传递学校编号!';
                        $data['errorlog_createtime'] = time();
                        $this->DataControl->insertData("imc_apiuser_errorlog", $data);

                        $res = array('error' => '1', 'errortip' => '名单进入集团，请不要传递学校编号!', 'result' => array());
                        ajax_return($res, $request['language_type']);
                    }
                }

                if(isset($request['client_mobile']) && $request['client_mobile'] != '' ){
                    if(!checkMobile($request['client_mobile'])){
                        $data = array();
                        $data['apiuser_id'] = $pucArray['apiuser_id'];
                        $data['apimodule_id'] = '7362';
                        $data['errorlog_apiurl'] = "https://api.kedingdang.com/Crm/addPromotionClientAction";
                        $data['errorlog_parameter'] = urldecode(http_build_query($request));
                        $data['errorlog_errortips'] = '您传入的手机号码格式不正确!';
                        $data['errorlog_createtime'] = time();
                        $this->DataControl->insertData("imc_apiuser_errorlog", $data);

                        $res = array('error' => '1', 'errortip' => '您传入的手机号码格式不正确!','result' => array());
                        ajax_return($res,$request['language_type']);
                    }
                }elseif(isset($request['client_passmobile']) && $request['client_passmobile'] != '' ){
                    $passmobile = $this->DataControl->selectOne(" select NEW_DECRYPTP('{$request['client_passmobile']}') as mobile limit 0,1 ");

                    $request['client_mobile'] = $passmobile['mobile'];
                    if(!checkMobile($request['client_mobile'])){
                        $data = array();
                        $data['apiuser_id'] = $pucArray['apiuser_id'];
                        $data['apimodule_id'] = '7362';
                        $data['errorlog_apiurl'] = "https://api.kedingdang.com/Crm/addPromotionClientAction";
                        $data['errorlog_parameter'] = urldecode(http_build_query($request));
                        $data['errorlog_errortips'] = '您传入的手机号码解密后格式不正确!';
                        $data['errorlog_createtime'] = time();
                        $this->DataControl->insertData("imc_apiuser_errorlog", $data);

                        $res = array('error' => '1', 'errortip' => '您传入的手机号码解密后格式不正确!','result' => array());
                        ajax_return($res,$request['language_type']);
                    }
                }

                //计算生日
                if(strlen($request['client_birthday']) > 7){
                    $nowyear = date("Y", time());
                    $getyear = date("Y", strtotime($request['client_birthday']));
                    $age = $nowyear - $getyear;
                    $request['client_age'] = $age;
                }elseif(preg_match("/^((\d){1,2})$/", $request['client_age']) == '1'){
                    $nowyear = date("Y", time());
                    $cyear = $nowyear - $request['client_age'];
                    $request['client_birthday'] = $cyear.'-06-01';
                }

//                if(!checkMobile($request['client_mobile'])){
//                    $data = array();
//                    $data['apiuser_id'] = $pucArray['apiuser_id'];
//                    $data['apimodule_id'] = '7362';
//                    $data['errorlog_apiurl'] = "https://api.kedingdang.com/Crm/addPromotionClientAction";
//                    $data['errorlog_parameter'] = urldecode(http_build_query($request));
//                    $data['errorlog_errortips'] = '您传入的手机号码格式不正确!';
//                    $data['errorlog_createtime'] = time();
//                    $this->DataControl->insertData("imc_apiuser_errorlog", $data);
//
//                    $res = array('error' => '1', 'errortip' => '您传入的手机号码格式不正确!','result' => array());
//                    ajax_return($res,$request['language_type']);
//                }
                $request['school_id'] = $schoolOne['school_id'];
//                print_r($request);die;
                $Model = new \Model\Crm\ClientModel($request);
                $dataList = $Model->addPhoneChannelAction($request);
                if($dataList){
                    $Model->result = $Model->result?$Model->result:array();
                    $res = array('error' => '0', 'errortip' => "你的信息提交成功，请耐心等待教师回电。",'result' => $Model->result);
                }else{
                    if($Model->errortip){
                        $Model->result = $Model->result?$Model->result:array();
                        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $Model->result);
                    }else {
                        $res = array('error' => '1', 'errortip' => '你的信息提交失败。' ,'result' => array());
                    }
                }
                ajax_return($res,$request['language_type']);
            }else{
                $data = array();
                $data['apiuser_id'] = $pucArray['apiuser_id'];
                $data['apimodule_id'] = '7362';
                $data['errorlog_apiurl'] = "https://api.kedingdang.com/Crm/addPromotionClientAction";
                $data['errorlog_parameter'] = urldecode(http_build_query($request));
                $data['errorlog_errortips'] = $this->errortip;
                $data['errorlog_createtime'] = time();
                $this->DataControl->insertData("imc_apiuser_errorlog", $data);

                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        }else{
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //获取吉的堡所有学校基础信息  ---  地推宝
    function getAllSchoolApi(){
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if($this->VeryModelNums($pucArray['apiuser_id'],'getAllSchool',$request)){
                //默认的  吉的堡集团和管理员
                $request['company_id'] = '8888';
                $request['school_id'] = '0';
                $request['marketer_id'] = '15';
                $Model = new \Model\Crm\CommonModel($request);
                $Model->getAllSchoolApi($request);
                ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $Model->result));
            }else{
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        }else{
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //获取 月到访 月签约  ---  地推宝
    function getSchVisitContractApi(){
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'getSchVisitContract', $request)) {

                $datawhere = " s.company_id = '{$pucArray['company_id']}' and s.school_isclose ='0' and s.school_istest = '0' and s.school_istemporaryclose = '0' and s.school_type = '0'";
                if (isset($request['school_branch']) && $request['school_branch'] !== '') {
                    $datawhere .= " and s.school_branch='{$request['school_branch']}' ";
                }

                $thisMonthStart = strtotime(date( 'Y-m-01',time()));//本月初 时间戳
                $today = time();//当前时间戳
                $invitedatawhere = " UNIX_TIMESTAMP(i.visittime) >= '{$thisMonthStart}' and UNIX_TIMESTAMP(i.visittime) <= '{$today}' and i.isvisit = '1' and i.school_id = s.school_id ";
                $thisMonth_regwhere = "r.pay_successtime >= '{$thisMonthStart}' and r.pay_successtime <='{$today}' and r.info_status =1";

                if (isset($request['p']) && $request['p'] !== '') {
                    $page = $request['p'];
                } else {
                    $page = '1';
                }
                if (isset($request['num']) && $request['num'] !== '') {
                    $num = $request['num'];
                } else {
                    $num = '10';
                }
                $pagestart = ($page - 1) * $num;

                $sql = "select s.school_branch,
                (select count(distinct(i.client_id)) from view_crm_invitelog as i where {$invitedatawhere}) as invitenum,
                (SELECT COUNT(r.info_id) from smc_student_registerinfo as r where r.school_id = s.school_id and {$thisMonth_regwhere}) as reginfonum 
                from smc_school as s 
                where {$datawhere} ";
                $sql .= ' limit ' . $pagestart . ',' . $num;
                $res = $this->DataControl->selectClear($sql);

                $count_sql = "select count(s.school_branch) as num from smc_school as s where {$datawhere}";
                $dbCount = $this->DataControl->selectOne($count_sql);
                $allnum = $dbCount['num'];

                $k = 0;
                $field = array();
                $field[$k]["fieldstring"] = "school_branch";
                $field[$k]["fieldname"] = "学校编号";
                $field[$k]["show"] = 0;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "invitenum";
                $field[$k]["fieldname"] = "月到访";
                $field[$k]["show"] = 0;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "reginfonum";
                $field[$k]["fieldname"] = "月签约";
                $field[$k]["show"] = 0;
                $field[$k]["custom"] = 1;
                $k++;

                $result = array();
                $result["field"] = $field;
                $result["allnum"] = $allnum;
                if ($res) {
                    $result['list'] = $res == false ? array() : $res;
                    $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
                    ajax_return($res);
                } else {
                    ajax_return(array('error' => '1', 'errortip' => '暂无数据', 'result' => array()));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //获取 地推人员名单那
    function getScPromotionApi(){
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if($this->VeryModelNums($pucArray['apiuser_id'],'getScPromotionApi',$request)){

                $datawhere = " a.company_id = '8888' ";
                if (isset($request['keyword']) && $request['keyword'] != '') {
                    $datawhere .= " and (a.promotion_name like '%{$request['keyword']}%' )";
                }
                if (isset($request['mobile']) && $request['mobile'] !== "") {
                    $datawhere .= " and a.promotion_mobile = '{$request['mobile']}' ";
                }
                if (isset($request['jobnumber']) && $request['jobnumber'] !== "") {
                    $datawhere .= " and a.promotion_jobnumber = '{$request['jobnumber']}' ";
                }
                if (isset($request['starttime']) && $request['starttime'] !== "" && isset($request['endtime']) && $request['endtime'] !== "") {
                    $starttime = strtotime($request['starttime']);
                    $endtime = strtotime($request['endtime'])+86399;
                    $datawhere .= " and ((a.promotion_createtime >= '{$starttime}' and a.promotion_createtime <= '{$endtime}') or (a.promotion_updatetime > 1 and a.promotion_updatetime >= '{$starttime}' and a.promotion_updatetime <= '{$endtime}' ))  ";
                }elseif (isset($request['starttime']) && $request['starttime'] !== ""){
                    $starttime = strtotime($request['starttime']);
                    $datawhere .= " and (a.promotion_createtime >= '{$starttime}' or a.promotion_updatetime >= '{$starttime}')   ";
                }elseif (isset($request['endtime']) && $request['endtime'] !== "") {
                    $endtime = strtotime($request['endtime'])+86399;
                    $datawhere .= " and (a.promotion_createtime <= '{$endtime}' or ( a.promotion_updatetime > 1  and a.promotion_updatetime <= '{$endtime}' ))  ";
                }

                if (isset($request['p']) && $request['p'] !== '') {
                    $page = $request['p'];
                } else {
                    $page = '1';
                }
                if (isset($request['num']) && $request['num'] !== '') {
                    $num = $request['num'];
                } else {
                    $num = '10';
                }
                $pagestart = ($page - 1) * $num;
                $sql="SELECT a.promotion_name,a.promotion_mobile,a.promotion_jobnumber,a.promotion_type,a.promotion_isprohibit,a.school_id,
                    (select group_concat(c.school_branch) from crm_ground_promotion_open as b,smc_school as c where a.promotion_id = b.promotion_id and b.open_status = '1' and b.school_id = c.school_id) as schoobranchstr
                    FROM crm_ground_promotion as a  
                    WHERE {$datawhere} limit {$pagestart},{$num} ";
                $dataList = $this->DataControl->selectClear($sql);

                if(!$dataList){
                    ajax_return(array('error' => '1', 'errortip' => '数据获取失败', 'result' => array()));
                }
                $dataresult = array();
                foreach ($dataList as $key=>$dataVar){
                    $dataresult[$key]['promotion_name'] = $dataVar['promotion_name'];
                    $dataresult[$key]['promotion_mobile'] = $dataVar['promotion_mobile'];
                    $dataresult[$key]['promotion_jobnumber'] = $dataVar['promotion_jobnumber'];
                    $dataresult[$key]['promotion_type'] = $dataVar['promotion_type'];
                    $dataresult[$key]['promotion_isprohibit'] = $dataVar['promotion_isprohibit'];
                    $dataresult[$key]['promotion_iscomcreate'] = ($dataVar['school_id']>1)?0:1;
                    if($dataVar['promotion_isprohibit'] == '1'){
                        $dataresult[$key]['schoobranchstr'] = '';
                    }else {
                        $dataresult[$key]['schoobranchstr'] = $dataVar['schoobranchstr']?$dataVar['schoobranchstr']:'';
                    }
                }

                //统计总数
                $allnum = $this->DataControl->selectOne("select count(a.promotion_id) as allnum FROM crm_ground_promotion as a WHERE {$datawhere}");

                $field = [
                    "promotion_name"=>"推广人姓名",
                    "promotion_mobile"=>"推广人电话",
                    "promotion_jobnumber"=>"推广人工号",
                    "promotion_type"=>"地推类型  0 市场(兼职)  1 销售  2市场专员",
                    "promotion_isprohibit"=>"是否禁止使用 0 否 1 是禁用 （新加字段目前都未禁用[离职]）",
                    "promotion_iscomcreate"=>"是否集团创建的名单 0 否 1 是  （目前这个字段数据不准确，需要整合地推宝数据后使用）",
                    "schoobranchstr"=>"推广人所属学校编号 例子：AAA,BBB  或者 CCC 或者  null （返回的有编号数据表示学校在使用中） ",
                ];

                $result = array();
                $result['allnum'] = $allnum['allnum'];
                $result["field"] = $field;
                $result['list'] = $dataresult;

                ajax_return(array('error' => '0', 'errortip' => "数据获取成功", 'result' => $result));
            }else{
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        }else{
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //获取错误日志
    function getErrorlogView(){

    }

    //第三方授权访问权限校验
    function UserVerify($paramArray)
    {
        $apiuserOne = $this->DataControl->getFieldOne('imc_apiuser', "apiuser_id,apiuser_aeskey,apiuser_aesiv", "apiuser_code = '{$paramArray['apiuser_code']}'");
        if (!$apiuserOne) {
            $this->errortip = "未查询到您的授权信息";
            $this->error = true;
            return false;
        }

        if (isset($paramArray['apiuser_aeskey']) && $paramArray['apiuser_aeskey'] !== '' && isset($paramArray['apiuser_aesiv']) && $paramArray['apiuser_aesiv'] !== '') {
            if ($apiuserOne['apiuser_aeskey'] == $paramArray['apiuser_aeskey'] && $apiuserOne['apiuser_aesiv'] == $paramArray['apiuser_aesiv']) {
                $baseOne = array();
                $baseOne['apiuser_id'] = $apiuserOne['apiuser_id'];
                $companyOne = $this->DataControl->selectOne("select company_id,company_cnname from gmc_company WHERE company_id = '{$paramArray['company_id']}' limit 0,1");
                if ($companyOne) {
                    $baseOne['company_id'] = $companyOne['company_id'];
                    return $baseOne;
                } else {
                    $this->errortip = '你的授权集团编号错误，请确认编号正确';
                    $this->error = true;
                    return false;
                }
            } else {
                $this->errortip = "你的授权秘钥及偏移值不正确，{$paramArray['apiuser_aeskey']}-{$paramArray['apiuser_aesiv']}";
                $this->error = true;
                return false;
            }
        }

        if (!isset($paramArray['timesteps']) || $paramArray['timesteps'] == '') {
            $this->errortip = "请传入授权时间";
            $this->error = true;
            return false;
        }

        if ($paramArray['timesteps'] + 60 * 5 < time() || $paramArray['timesteps'] - 60 > time()) {
            $maxtimes = date("Y-m-d H:i:s", $paramArray['timesteps'] + 60 * 5);
            $this->errortip = "授权时间{$maxtimes}已过期5分钟，请确认连接及时性";//,{$timesteps}--{$jmsting}
            $this->error = true;
            return false;
        }

        $aes = new \Aesencdec($apiuserOne['apiuser_aeskey'], $apiuserOne['apiuser_aesiv']);
        $xssting = $aes->decrypt($this->stringReplace($paramArray['veytoken']));//解密
        if ($paramJson = json_decode($xssting, 1)) {//转化为数组
            if ((string)$paramJson['timesteps'] !== trim($paramArray['timesteps'])) {
                $this->errortip = '授权时间和连接时间不一致';
                $this->error = true;
                return false;
            }
            $baseOne = array();
            $baseOne['apiuser_id'] = $apiuserOne['apiuser_id'];
            $baseOne['tokenstring'] = $xssting;
            $companyOne = $this->DataControl->selectOne("select company_id,company_cnname from gmc_company WHERE company_id = '{$paramJson['company_id']}' limit 0,1");
            if ($companyOne) {
                $baseOne['company_id'] = $companyOne['company_id'];
                return $baseOne;
            } else {
                $this->errortip = '你的授权集团编号错误，请确认编号正确';
                $this->error = true;
                return false;
            }
        } else {
            $this->errortip = '数据机密信息传输有误，请检查！';
            $this->error = true;
            return false;
        }
    }

    function VeryModelNums($apiuser_id, $apimodule_code, $paramArray)
    {
        $request = Input('get.', '', 'trim,addslashes');
        $apimoduleOne = $this->DataControl->getFieldOne('imc_apiuser_apimodule'
            , "apimodule_id,apiuser_id,apimodule_name,apimodule_nums", "apiuser_id = '{$apiuser_id}' AND apimodule_code = '{$apimodule_code}'");
        $stattTimes = strtotime(date("Y-m-d"));
        $apilogOne = $this->DataControl->selectOne("SELECT COUNT(l.apilog_id) AS anums FROM imc_apiuser_apilog AS l
WHERE l.apimodule_id = '{$apimoduleOne['apimodule_id']}' AND l.apilog_createtime > '{$stattTimes}'");
        if ($apilogOne['anums'] > $apimoduleOne['apimodule_nums']) {
            $this->errortip = "您接口{$apimoduleOne['apimodule_name']}的本日最大授权次数{$apimoduleOne['apimodule_nums']}已消耗完毕！";
            $this->error = true;

            $data = array();
            $data['apiuser_id'] = $apimoduleOne['apiuser_id'];
            $data['apimodule_id'] = $apimoduleOne['apimodule_id'];
            $data['errorlog_apiurl'] = "https://api.kedingdang.com/{$request['u']}/{$request['t']}{$request['c']}";
            $data['errorlog_parameter'] = urldecode(http_build_query($paramArray));
            $data['errorlog_errortips'] = $this->errortip;
            $data['errorlog_createtime'] = time();
            $this->DataControl->insertData("imc_apiuser_errorlog", $data);

            return false;
        } else {
            $data = array();
            $data['apiuser_id'] = $apimoduleOne['apiuser_id'];
            $data['apimodule_id'] = $apimoduleOne['apimodule_id'];
            $data['apilog_posturl'] = "https://api.kedingdang.com/{$request['u']}/{$request['t']}{$request['c']}";
            $data['apilog_posttype'] = 'GET';
            $data['apilog_postorgjson'] = urldecode(http_build_query($paramArray));
            $data['apilog_postjson'] = $paramArray['tokenstring'];
            $data['apilog_ip'] = real_ip();
            $data['apilog_createtime'] = time();
            $this->DataControl->insertData("imc_apiuser_apilog", $data);
            return true;
        }
    }
    function VeryLogs($apiuser_id,$apiurl,$paramArray)
    {
        $data = array();
        $data['apiuser_id'] = $apiuser_id;
        $data['apilog_posturl'] = "https://api.kedingdang.com/{$apiurl}";
        $data['apilog_postorgjson'] = urldecode(http_build_query($paramArray));
        $data['apilog_postjson'] = $this->errortip;
        $data['apilog_createtime'] = time();
        $this->DataControl->insertData("imc_apiuser_apilog", $data);
    }

    function stringReplace($string)
    {
        $datacode = trim(str_replace('"', "", $string));
        $datacode = urldecode(urldecode($datacode));
        $datacode = str_replace(' ', "+", $datacode);
        return $datacode;
    }

    function testParameterView()
    {
        $parameter = array();
        $parameter['timesteps'] = time();
        $parameter['apiuser_code'] = 'dituibao1';
        $parameter['company_id'] = '8888';

        $apiuserOne = $this->DataControl->getFieldOne('imc_apiuser', "apiuser_id,apiuser_aeskey,apiuser_aesiv", "apiuser_code = '{$parameter['apiuser_code']}'");
        if (!$apiuserOne) {
            $this->errortip = "未查询到您的授权信息";
            $this->error = true;
            return false;
        }

        $aes = new \Aesencdec($apiuserOne['apiuser_aeskey'], $apiuserOne['apiuser_aesiv']);
        $parameterJson = json_encode($parameter, '1');
        $jmsting = $aes->encrypt($parameterJson);//解密

        $result = array();
        $result['timesteps'] = $parameter['timesteps'];
        $result['apiuser_code'] = $parameter['apiuser_code'];
        $result['veytoken'] = $jmsting;

        ajax_return(array('error' => '0', 'errortip' => '模拟参数获取成功', 'result' => $result));
    }

    function LgStringSwitchOne($cnstring,$company_language='zh')
    {
        if ($company_language == 'tw') {
            $Model = new \Model\jianfanModel();
            $cnstring = $Model->gb2312_big5($cnstring);
            return $cnstring;
        } else {
            return $cnstring;
        }
    }
    //第三方地推人员 对应的名单录入  ---  台湾名单添加入课叮铛
    function addTwNameToClientAction(){
        $request = Input('post.','','trim,addslashes');

        $data = array();
        $data['apiuser_id'] = 2356;
        $data['apimodule_id'] = 7452;
        $data['apilog_posturl'] = "https://api.kedingdang.com/Crm/addTwNameToClientAction";
        $data['apilog_posttype'] = 'POST';
        $data['apilog_postorgjson'] = urldecode(http_build_query($request));
        $data['apilog_postjson'] = json_encode($request);
        $data['apilog_ip'] = real_ip();
        $data['apilog_createtime'] = time();
        $this->DataControl->insertData("imc_apiuser_apilog", $data);

        $request['company_id'] = '79081';
        $request['school_id'] = '0';
        $request['marketer_id'] = '5875';
        $request['language_type'] = 'tw';
//        $request['channel_name'];//渠道字段还没有确认

        if(!isset($request['parent_phone'])){
            $data = array();
            $data['apiuser_id'] = 2356;
            $data['apimodule_id'] = '7452';
            $data['errorlog_apiurl'] = "https://api.kedingdang.com/Crm/addTwNameToClientAction";
            $data['errorlog_parameter'] = urldecode(http_build_query($request));
            $data['errorlog_errortips'] = $this->LgStringSwitchOne('联系手机未传!',$request['language_type']);
            $data['errorlog_createtime'] = time();
            $this->DataControl->insertData("imc_apiuser_errorlog", $data);

            $res = array('error' => '1', 'errortip' => '联系手机未传！', 'result' => array());
            ajax_return($res,$request['language_type']);
        }
        if(preg_match("/^(1(\d){10})$|^(0(\d){7,11})$/", $request['parent_phone']) == '0'){
            $data = array();
            $data['apiuser_id'] = 2356;
            $data['apimodule_id'] = '7452';
            $data['errorlog_apiurl'] = "https://api.kedingdang.com/Crm/addTwNameToClientAction";
            $data['errorlog_parameter'] = urldecode(http_build_query($request));
            $data['errorlog_errortips'] = $this->LgStringSwitchOne('您传入的联系方式格式不正确!',$request['language_type']);
            $data['errorlog_createtime'] = time();
            $this->DataControl->insertData("imc_apiuser_errorlog", $data);

            $res = array('error' => '1', 'errortip' => '您传入的联系方式格式不正确!','result' => array());
            ajax_return($res,$request['language_type']);
        }
        if(!isset($request['student_name']) || $request['student_name'] == '' ){
            $data = array();
            $data['apiuser_id'] = 2356;
            $data['apimodule_id'] = '7452';
            $data['errorlog_apiurl'] = "https://api.kedingdang.com/Crm/addTwNameToClientAction";
            $data['errorlog_parameter'] = urldecode(http_build_query($request));
            $data['errorlog_errortips'] = $this->LgStringSwitchOne('学生姓名不能为空!',$request['language_type']);
            $data['errorlog_createtime'] = time();
            $this->DataControl->insertData("imc_apiuser_errorlog", $data);

            $res = array('error' => '1', 'errortip' => '学生姓名不能为空！', 'result' => array());
            ajax_return($res,$request['language_type']);
        }
        $channelOne = $this->DataControl->getFieldOne("crm_code_channel", "channel_id,channel_isschoolchose,channel_medianame,channel_quality"
            , "channel_name='{$request['channel_name']}' AND company_id = '{$request['company_id']}'");
        if(!$channelOne){
            $data = array();
            $data['apiuser_id'] = 2356;
            $data['apimodule_id'] = '7452';
            $data['errorlog_apiurl'] = "https://api.kedingdang.com/Crm/addTwNameToClientAction";
            $data['errorlog_parameter'] = urldecode(http_build_query($request));
            $data['errorlog_errortips'] = $this->LgStringSwitchOne('渠道信息有误!',$request['language_type']);
            $data['errorlog_createtime'] = time();
            $this->DataControl->insertData("imc_apiuser_errorlog", $data);

            $res = array('error' => '1', 'errortip' => '渠道信息有误！', 'result' => array());
            ajax_return($res,$request['language_type']);
        }
        $schoolOne = $this->DataControl->getFieldOne("smc_school", "school_id", "company_id = '{$request['company_id']}' and school_branch = '{$request['school_branch']}' and school_type = '1' ");
        if (!$schoolOne) {
            $data = array();
            $data['apiuser_id'] = 2356;
            $data['apimodule_id'] = '7452';
            $data['errorlog_apiurl'] = "https://api.kedingdang.com/Crm/addTwNameToClientAction";
            $data['errorlog_parameter'] = urldecode(http_build_query($request));
            $data['errorlog_errortips'] = $this->LgStringSwitchOne('找不到对应的学校，请输入正确的学校!',$request['language_type']);
            $data['errorlog_createtime'] = time();
            $this->DataControl->insertData("imc_apiuser_errorlog", $data);

            $res = array('error' => '1', 'errortip' => '找不到对应的学校，请输入正确的学校!', 'result' => array());
            ajax_return($res, $request['language_type']);
        }

        $request['school_id'] = $schoolOne['school_id'];
        $request['client_cnname'] = $request['student_name'];
        $request['client_age'] = $request['student_age'];
        $request['client_lineid'] = $request['student_lineid'];
        $request['client_facebookid'] = $request['student_facebookid'];

        //当年龄传入日期
        if ($request['client_age'] && preg_match("/^((\d){1,2})$/", $request['client_age'])) {
            $nowYear = date("Y",time());
            $birthadyYear = $nowYear - $request['client_age'] +1;
            //估算大致年月日
            $addOne['client_birthday'] = $birthadyYear."-06-01";
        }

        $request['client_patriarchname'] = $request['parent_name'];
        $request['client_mobile'] = $request['parent_phone'];
        $request['client_email'] = $request['parent_email'];
        $request['client_tag'] = $request['cram_course'];//班别
        if($request['source_name'] != '') {
            $request['client_frompage'] = $request['source_name'];//班别
            $request['client_remark'] = $this->LgStringSwitchOne("表单来源：",$request['language_type']) . $request['source_name'];//台湾表单来源的备注
        }

        $Model = new \Model\Crm\ClientModel($request);
        $dataList = $Model->addPhoneChannelAction($request);
        if($dataList){
            $Model->result = $Model->result?$Model->result:array();
            $res = array('error' => '0', 'errortip' => "你的信息提交成功，请耐心等待教师回电。",'result' => $Model->result);
        }else{
            if($Model->errortip){
                $Model->result = $Model->result?$Model->result:array();
                $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $Model->result);
            }else {
                $res = array('error' => '1', 'errortip' => '你的信息提交失败。' ,'result' => array());
            }
        }
        ajax_return($res,$request['language_type']);
    }

    //堡贝通APP 游客 对应的名单录入  ---  堡贝通APP
    function addBbtAppClientAction(){
        $request = Input('post.','','trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            //访问日志
            $this->VeryLogs($pucArray['apiuser_id'], "{$request['u']}/{$request['t']}{$request['api']}", $request);

            if($request['language_type'] == 'zh') {
                $request['company_id'] = '8888';
                $request['school_id'] = '0';
                $request['marketer_id'] = '15';
            }elseif($request['language_type'] == 'tw'){
                $request['company_id'] = '79081';
                $request['school_id'] = '0';
                $request['marketer_id'] = '5875';
            }else{
                $data = array();
                $data['apiuser_id'] = $pucArray['apiuser_id'];
                $data['apimodule_id'] = '0';
                $data['errorlog_apiurl'] = "https://api.kedingdang.com/Crm/addBbtAppClientAction";
                $data['errorlog_parameter'] = urldecode(http_build_query($request));
                $data['errorlog_errortips'] = $this->LgStringSwitchOne('参数不完整！',$request['language_type']);
                $data['errorlog_createtime'] = time();
                $this->DataControl->insertData("imc_apiuser_errorlog", $data);

                $res = array('error' => '1', 'errortip' => '参数不完整！', 'result' => array());
                ajax_return($res,$request['language_type']);
            }
            if($request['language_type'] == 'zh'){
                $request['channel_name'] = '堡贝通';
            }elseif($request['language_type'] == 'tw'){
                $request['channel_name'] = '堡貝通';
            }else{
                $request['channel_name'] = '堡贝通';
            }
//            $request['language_type'] = 'tw';
//            $request['family_relation'] = '爸爸';
//            $request['client_sex'] = $request['client_sex'];

            if(!isset($request['client_cnname']) || $request['client_cnname'] == '' || $request['client_mobile'] == '' ){
                $data = array();
                $data['apiuser_id'] = $pucArray['apiuser_id'];
                $data['apimodule_id'] = '0';
                $data['errorlog_apiurl'] = "https://api.kedingdang.com/Crm/addBbtAppClientAction";
                $data['errorlog_parameter'] = urldecode(http_build_query($request));
                $data['errorlog_errortips'] = $this->LgStringSwitchOne('宝宝姓名和联系手机不能为空！',$request['language_type']);
                $data['errorlog_createtime'] = time();
                $this->DataControl->insertData("imc_apiuser_errorlog", $data);

                $res = array('error' => '1', 'errortip' => '宝宝姓名和联系手机不能为空！', 'result' => array());
                ajax_return($res,$request['language_type']);
            }

            if(preg_match("/^(1(\d){10})$|^(0(\d){7,11})$/", $request['client_mobile']) == '0'){
                $data = array();
                $data['apiuser_id'] = $pucArray['apiuser_id'];
                $data['apimodule_id'] = '0';
                $data['errorlog_apiurl'] = "https://api.kedingdang.com/Crm/addBbtAppClientAction";
                $data['errorlog_parameter'] = urldecode(http_build_query($request));
                $data['errorlog_errortips'] = $this->LgStringSwitchOne('您传入的联系方式格式不正确!',$request['language_type']);
                $data['errorlog_createtime'] = time();
                $this->DataControl->insertData("imc_apiuser_errorlog", $data);

                $res = array('error' => '1', 'errortip' => '您传入的联系方式格式不正确!','result' => array());
                ajax_return($res,$request['language_type']);
            }

            //计算生日
            if(strlen($request['client_birthday']) > 7){
                $nowyear = date("Y", time());
                $getyear = date("Y", strtotime($request['client_birthday']));
                $age = $nowyear - $getyear;
                $request['client_age'] = $age;
            }elseif(preg_match("/^((\d){1,2})$/", $request['client_age']) == '1'){
                $nowyear = date("Y", time());
                $cyear = $nowyear - $request['client_age'];
                $request['client_birthday'] = $cyear.'-06-01';
            }

//                print_r($request);die;
            $Model = new \Model\Crm\ClientModel($request);
            $dataList = $Model->addPhoneChannelAction($request);
            if($dataList){
                $Model->result = $Model->result?$Model->result:array();
                $res = array('error' => '0', 'errortip' => "你的信息提交成功，请耐心等待教师回电。",'result' => $Model->result);
            }else{
                if($Model->errortip){
                    $Model->result = $Model->result?$Model->result:array();
                    $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $Model->result);
                }else {
                    $res = array('error' => '1', 'errortip' => '你的信息提交失败。' ,'result' => array());
                }
            }
            ajax_return($res,$request['language_type']);
        }else{
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }
    //堡贝通APP 游客 柜询试听记录  ---  堡贝通APP
    function getBbtAppClientVisitAction(){
        $request = Input('post.','','trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            //访问日志
            $this->VeryLogs($pucArray['apiuser_id'], "{$request['u']}/{$request['t']}{$request['api']}", $request);

            if($request['language_type'] == 'zh') {
                $request['company_id'] = '8888';
            }elseif($request['language_type'] == 'tw'){
                $request['company_id'] = '79081';
            }else{
                $data = array();
                $data['apiuser_id'] = $pucArray['apiuser_id'];
                $data['apimodule_id'] = '0';
                $data['errorlog_apiurl'] = "https://api.kedingdang.com/Crm/addBbtAppClientAction";
                $data['errorlog_parameter'] = urldecode(http_build_query($request));
                $data['errorlog_errortips'] = $this->LgStringSwitchOne('参数不完整！',$request['language_type']);
                $data['errorlog_createtime'] = time();
                $this->DataControl->insertData("imc_apiuser_errorlog", $data);

                $res = array('error' => '1', 'errortip' => '参数不完整！', 'result' => array());
                ajax_return($res,$request['language_type']);
            }

            $datawhere = " ";
            if(isset($request['access_type']) && $request['access_type'] != ''){
                $datawhere .= " and x.access_type = '{$request['access_type']}' ";
            }
            if(isset($request['access_isvisit']) && $request['access_isvisit'] != ''){
                if($request['access_isvisit'] == '-1'){
                    $datawhere .= " and  x.access_isvisit in ('-1','2') ";
                }else{
                    $datawhere .= " and x.access_isvisit = '{$request['access_isvisit']}' ";
                }
            }
            $sql = "
                SELECT x.* from
                (
                    select a.client_id,d.school_cnname,d.school_shortname,d.school_branch,d.school_address,'1' as access_type,c.invite_visittime as access_visitime,c.invite_isvisit as access_isvisit,'school' as school_big_type,d.school_type,d.school_phone,c.invite_id as access_id
                    from crm_client as a 
                    left join crm_client_schoolenter as b ON a.client_id = b.client_id and b.is_enterstatus = 1 
                    left join crm_client_invite as c ON a.client_id = c.client_id and b.school_id = c.school_id 
                    left join smc_school as d ON b.school_id = d.school_id and d.school_isclose = '0'
                    where a.company_id = '{$request['company_id']}' and a.client_mobile = '{$request['client_mobile']}'
                    UNION
                    select a.client_id,d.school_cnname,d.school_shortname,d.school_branch,d.school_address,'2' as access_type,c.audition_visittime as access_visitime,c.audition_isvisit as access_isvisit,'school' as school_big_type,d.school_type,d.school_phone,c.audition_id as access_id
                    from crm_client as a 
                    left join crm_client_schoolenter as b ON a.client_id = b.client_id and b.is_enterstatus = 1 
                    left join crm_client_audition as c ON a.client_id = c.client_id and b.school_id = c.school_id 
                    left join smc_school as d ON b.school_id = d.school_id and d.school_isclose = '0' 
                    where a.company_id = '{$request['company_id']}' and a.client_mobile = '{$request['client_mobile']}'
                ) as x 
                WHERE x.access_visitime is not null {$datawhere}
             ";
            $dataList = $this->DataControl->selectClear($sql);

            if($dataList){
                $res = array('error' => '0', 'errortip' => "信息获取成功。",'result' => $dataList);
            }else{
                $res = array('error' => '0', 'errortip' => "暂无记录。",'result' => array());
            }
            ajax_return($res,$request['language_type']);
        }else{
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }
    //堡贝通APP 游客 柜询试听记录  ----  取消操作  ---  堡贝通APP
    function upBbtAppClientVisitAction(){
        $request = Input('post.','','trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            //访问日志
            $this->VeryLogs($pucArray['apiuser_id'], "{$request['u']}/{$request['t']}{$request['api']}", $request);

            if($request['language_type'] == 'zh') {
                $request['company_id'] = '8888';
                $request['marketer_id'] = '15';
                $request['novisitreason'] = '堡贝通APP取消';
            }elseif($request['language_type'] == 'tw'){
                $request['company_id'] = '79081';
                $request['marketer_id'] = '5875';
                $request['novisitreason'] = '堡貝通APP取消';
            }else{
                $data = array();
                $data['apiuser_id'] = $pucArray['apiuser_id'];
                $data['apimodule_id'] = '0';
                $data['errorlog_apiurl'] = "https://api.kedingdang.com/Crm/upBbtAppClientVisitAction";
                $data['errorlog_parameter'] = urldecode(http_build_query($request));
                $data['errorlog_errortips'] = $this->LgStringSwitchOne('参数不完整！',$request['language_type']);
                $data['errorlog_createtime'] = time();
                $this->DataControl->insertData("imc_apiuser_errorlog", $data);

                $res = array('error' => '1', 'errortip' => '参数不完整！', 'result' => array());
                ajax_return($res,$request['language_type']);
            }
//            $request['access_type'] = $request['access_type'];//类型  类型   1 柜询  2 试听
//            $request['access_id'] = $request['access_id'];//类型
//            $request['school_id'] = $request['school_id'];//学校ID

            if($request['access_type'] == '1'){
                $redata = array();
                $redata['company_id'] = $request['company_id'];
                $redata['invite_id'] = $request['access_id'];//类型
                $redata['school_id'] = $request['school_id'];//学校ID
                $redata['invite_isvisit'] = '-1';//默认
                $redata['invite_type'] = 0;//默认
                $redata['marketer_id'] = $request['marketer_id'] ;//默认
                $redata['invite_novisitreason'] = $request['novisitreason'];//

                $Model = new \Model\Crm\ClientinviteModel($redata);
                $Model->setIsVisit($redata);
                $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $Model->result);
            }elseif($request['access_type'] == '2'){
                $redata = array();
                $redata['company_id'] = $request['company_id'];
                $redata['audition_id'] = $request['access_id'];//类型
                $redata['school_id'] = $request['school_id'];//学校ID
                $redata['audition_isvisit'] = '-1';//默认
                $redata['audition_type'] = 0;//默认
                $redata['marketer_id'] = $request['marketer_id'] ;//默认
                $redata['audition_novisitreason'] = $request['novisitreason'];//

                $Model = new \Model\Crm\ClientinviteModel($redata);
                $Model->setIsaudition($redata);
                $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $Model->result);
            }else{
                $res = array('error' => '0', 'errortip' => "参数有误。",'result' => array());
            }
            ajax_return($res,$request['language_type']);
        }else{
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //堡贝通小程序  在校生阅读打卡分享 广告页面留名单操作 ---  堡贝通小程序
    function addBbtReadClockinClientAction(){
        $request = Input('post.','','trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['u']=$request['u']?$request['u']:$this->u;
            $request['t']=$request['t']?$request['t']:$this->t;
            //访问日志
            $this->VeryLogs($pucArray['apiuser_id'], "{$request['u']}/{$request['t']}{$request['api']}", $request);

            $request['company_id'] = '8888';
            if ($request['client_mobile'] == '') {
                ajax_return(array('error' => '1', 'errortip' => '请输入联系方式!'));
            }
            if ($request['client_cnname'] == '') {
                ajax_return(array('error' => '1', 'errortip' => '请输入宝宝的姓名!'));
            }

            $Model = new \Model\Crm\ClientModel($request);
            $dataList = $Model->addPhoneChannelAction($request);
            if($dataList){
                $Model->result = $Model->result?$Model->result:array();
                $res = array('error' => '0', 'errortip' => "你的信息提交成功，请耐心等待教师回电。",'result' => $Model->result);
            }else{
                if($Model->errortip){
                    $Model->result = $Model->result?$Model->result:array();
                    $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $Model->result);
                }else {
                    $res = array('error' => '1', 'errortip' => '你的信息提交失败。' ,'result' => array());
                }
            }
            ajax_return($res,$request['language_type']);
        }else{
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    function clearCrmAction(){
        $request = Input('post.','','trim,addslashes');
        if($request['mobile'] == '15324578746' || $request['mobile'] == '15324578764' || $request['mobile'] == '15285235685' || $request['mobile'] == '18721737205' || $request['mobile'] == '17602186571'){
            $this->DataControl->delData("crm_client","client_mobile = '{$request['mobile']}'");
        }
    }
}