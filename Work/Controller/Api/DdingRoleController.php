<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2021/6/2
 * Time: 10:46
 */

namespace Work\Controller\Api;


class DdingRoleController extends viewTpl
{
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $stafferOne = array();

    function __construct()
    {
        parent::__construct();
    }

    function stringReplace($string)
    {
        $datacode = trim(str_replace('"', "", $string));
        $datacode = urldecode(urldecode($datacode));
        $datacode = str_replace(' ', "+", $datacode);
        return $datacode;
    }

    //第三方授权访问权限校验
    function UserVerify($paramArray)
    {
        $apiuserOne = $this->DataControl->getFieldOne('imc_apiuser', "apiuser_id,apiuser_aeskey,apiuser_aesiv", "apiuser_code = '{$paramArray['apiuser_code']}'");
        if (!$apiuserOne) {
            $this->errortip = "未查询到您的授权信息";
            $this->error = true;
            return false;
        }

        if (isset($paramArray['apiuser_aeskey']) && $paramArray['apiuser_aeskey'] !== '' && isset($paramArray['apiuser_aesiv']) && $paramArray['apiuser_aesiv'] !== '') {
            if ($apiuserOne['apiuser_aeskey'] == $paramArray['apiuser_aeskey'] && $apiuserOne['apiuser_aesiv'] == $paramArray['apiuser_aesiv']) {
                $baseOne = array();
                $baseOne['apiuser_id'] = $apiuserOne['apiuser_id'];
                $companyOne = $this->DataControl->selectOne("select company_id,company_cnname from gmc_company WHERE company_id = '{$paramArray['company_id']}' limit 0,1");
                if ($companyOne) {
                    $baseOne['company_id'] = $companyOne['company_id'];
                    return $baseOne;
                } else {
                    $this->errortip = '你的授权集团编号错误，请确认编号正确';
                    $this->error = true;
                    return false;
                }
            } else {
                $this->errortip = "你的授权秘钥及偏移值不正确，{$paramArray['apiuser_aeskey']}-{$paramArray['apiuser_aesiv']}";
                $this->error = true;
                return false;
            }
        }

        if (!isset($paramArray['timesteps']) || $paramArray['timesteps'] == '') {
            $this->errortip = "请传入授权时间";
            $this->error = true;
            return false;
        }

        if ($paramArray['timesteps'] + 60 * 5 < time() || $paramArray['timesteps'] - 60 > time()) {
            $maxtimes = date("Y-m-d H:i:s", $paramArray['timesteps'] + 60 * 5);
            $this->errortip = "授权时间{$maxtimes}已过期5分钟，请确认连接及时性";//,{$timesteps}--{$jmsting}
            $this->error = true;
            return false;
        }

        $aes = new \Aesencdec($apiuserOne['apiuser_aeskey'], $apiuserOne['apiuser_aesiv']);
        $xssting = $aes->decrypt($this->stringReplace($paramArray['veytoken']));//解密
        if ($paramJson = json_decode($xssting, 1)) {//转化为数组
            if ((string)$paramJson['timesteps'] !== trim($paramArray['timesteps'])) {
                $this->errortip = '授权时间和连接时间不一致';
                $this->error = true;
                return false;
            }
            $baseOne = array();
            $baseOne['apiuser_id'] = $apiuserOne['apiuser_id'];
            $baseOne['tokenstring'] = $xssting;
            $companyOne = $this->DataControl->selectOne("select company_id,company_cnname from gmc_company WHERE company_id = '{$paramJson['company_id']}' limit 0,1");
            if ($companyOne) {
                $baseOne['company_id'] = $companyOne['company_id'];
                return $baseOne;
            } else {
                $this->errortip = '你的授权集团编号错误，请确认编号正确';
                $this->error = true;
                return false;
            }
        } else {
            $this->errortip = '数据机密信息传输有误，请检查！';
            $this->error = true;
            return false;
        }
    }

    //判断访问的次数
    function VeryModelNums($apiuser_id, $apimodule_code, $paramArray)
    {
        $request = Input('get.', '', 'trim,addslashes');
        $apimoduleOne = $this->DataControl->getFieldOne('imc_apiuser_apimodule'
            , "apimodule_id,apiuser_id,apimodule_name,apimodule_nums", "apiuser_id = '{$apiuser_id}' AND apimodule_code = '{$apimodule_code}'");
        $stattTimes = strtotime(date("Y-m-d"));
        $apilogOne = $this->DataControl->selectOne("SELECT COUNT(l.apilog_id) AS anums FROM imc_apiuser_apilog AS l
WHERE l.apimodule_id = '{$apimoduleOne['apimodule_id']}' AND l.apilog_createtime > '{$stattTimes}'");
        if ($apilogOne['anums'] > $apimoduleOne['apimodule_nums']) {
            $this->errortip = "您接口{$apimoduleOne['apimodule_name']}的本日最大授权次数{$apimoduleOne['apimodule_nums']}已消耗完毕！";
            $this->error = true;
            return false;
        } else {
            $data = array();
            $data['apiuser_id'] = $apimoduleOne['apiuser_id'];
            $data['apimodule_id'] = $apimoduleOne['apimodule_id'];
            $data['apilog_posturl'] = "https://api.kedingdang.com/{$request['u']}/{$request['t']}";
            $data['apilog_posttype'] = 'GET';
            $data['apilog_postorgjson'] = http_build_query($paramArray);
            $data['apilog_postjson'] = $paramArray['tokenstring'];
            $data['apilog_ip'] = real_ip();
            $data['apilog_createtime'] = time();
            $this->DataControl->insertData("imc_apiuser_apilog", $data);
            return true;
        }
    }

    //钉钉 测试获取参数
    function testParameterView()
    {
        $parameter = array();
        $parameter['timesteps'] = time();
        $parameter['apiuser_code'] = 'DdRolePlatform';
        $parameter['company_id'] = '8888';

        $apiuserOne = $this->DataControl->getFieldOne('imc_apiuser', "apiuser_id,apiuser_aeskey,apiuser_aesiv", "apiuser_code = '{$parameter['apiuser_code']}'");
        if (!$apiuserOne) {
            $this->errortip = "未查询到您的授权信息";
            $this->error = true;
            return false;
        }

        $aes = new \Aesencdec($apiuserOne['apiuser_aeskey'], $apiuserOne['apiuser_aesiv']);
        $parameterJson = json_encode($parameter, '1');
        $jmsting = $aes->encrypt($parameterJson);//解密

        $result = array();
        $result['timesteps'] = $parameter['timesteps'];
        $result['apiuser_code'] = $parameter['apiuser_code'];
        $result['veytoken'] = $jmsting;

        ajax_return(array('error' => '0', 'errortip' => '模拟参数获取成功', 'result' => $result));
    }
    //获取校区明细  --  样例
    function getSmcschoolApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'Smcschool', $request)) {
                $datawhere = " d.district_id = s.district_id AND s.school_province = r.region_id AND s.company_id = '{$pucArray['company_id']}' AND s.school_type = '1'";
                $sql = "SELECT s.school_id,s.school_branch, s.school_cnname, s.school_shortname, concat(school_issubject,')',s.school_address) as school_address,s.school_phone, d.district_cnname
     ,s.school_isclose,r.region_name, s.school_istest
FROM smc_school AS s, gmc_company_district AS d,smc_code_region AS r WHERE {$datawhere}";
                $dataList = $this->DataControl->selectClear($sql);

                $field = array();
                $k = 0;
                $field[$k]["fieldstring"] = "school_id";
                $field[$k]["fieldname"] = "校区序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $result = array();
                $result['field'] = $field;
                if ($dataList) {
                    $result['list'] = $dataList == false ? array() : $dataList;
                    $res = array('error' => '0', 'errortip' => "校区信息获取成功", 'result' => $result);
                    ajax_return($res);
                } else {
                    $result['list'] = array();
                    ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => $result));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //钉钉-角色平台 获取校区域接口
    function getDdUserDistrictApi(){
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {

            $datawhere = " 1 AND a.company_id = '{$pucArray['company_id']}' ";
            if (isset($request['keyword']) && $request['keyword'] != '') {
                $datawhere .= " and  (a.district_branch like '%{$request['keyword']}%' or a.district_cnname like '%{$request['keyword']}%' ) ";
            }
            $sql = "SELECT a.district_id,a.district_branch,a.district_cnname 
                    FROM gmc_company_district AS a  
                    WHERE {$datawhere}";
            $dataList = $this->DataControl->selectClear($sql);

            if ($dataList) {
                $result['list'] = $dataList == false ? array() : $dataList;
                $res = array('error' => '0', 'errortip' => "区域信息获取成功", 'result' => $result);
                ajax_return($res);
            } else {
                $result['list'] = array();
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => $result));
            }

        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //钉钉-角色平台 获取校园接口
    function getDdUserSchoolApi(){
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {

            $datawhere = " 1 AND s.company_id = '{$pucArray['company_id']}' AND s.school_type = '1' and s.school_isclose = '0' and s.school_istest = '0' ";
            if (isset($request['keyword']) && $request['keyword'] != '') {
                $datawhere .= " and  (s.school_branch like '%{$request['keyword']}%' or s.school_cnname like '%{$request['keyword']}%' or s.school_shortname like '%{$request['keyword']}%'  ) ";
            }
            if (isset($request['district_id']) && $request['district_id'] != '') {
                $datawhere .= " and s.district_id = '{$request['district_id']}' ";
            }
            $sql = "SELECT s.school_id,s.school_branch,s.school_cnname,s.school_shortname,s.district_id 
                    FROM smc_school AS s 
                    WHERE {$datawhere}";
            $dataList = $this->DataControl->selectClear($sql);

            if ($dataList) {
                $result['list'] = $dataList == false ? array() : $dataList;
                $res = array('error' => '0', 'errortip' => "校区信息获取成功", 'result' => $result);
                ajax_return($res);
            } else {
                $result['list'] = array();
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => $result));
            }

        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }



}