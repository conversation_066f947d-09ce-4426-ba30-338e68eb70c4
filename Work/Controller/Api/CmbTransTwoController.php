<?php


namespace Work\Controller\Api;


class CmbTransTwoController extends viewTpl
{
    public $u;
    public $t;
    public $c;

    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
    }

    //同步招行班级余额
    //http://api.kcclassin.com/CmbTrans/autoRecordClassTransInfoApi
    function autoRecordClassTransInfoApi(){
        $request = Input('get.', '', 'trim,addslashes');
        $datawhere= "companies_issupervise=1 and companies_agencyid<>'' and companies_supervisetime>0 and company_id='8888'";

        if(isset($request['companies_agencyid']) && $request['companies_agencyid']!=''){
            $datawhere.=" and companies_agencyid='{$request['companies_agencyid']}'";
        }

        $sql = "select companies_agencyid,companies_signature,companies_secretkey 
                from gmc_code_companies 
                where {$datawhere}";

        $agencyList=$this->DataControl->selectClear($sql);

        if($agencyList){
            $num=1;
            foreach($agencyList as $agencyOne){
                $Model = new \Model\Api\CmbTransModel($agencyOne['companies_agencyid']);
                $n=$Model->autoRecordClassTransInfo();
                $num+=$n;
            }
            $res = array('error' => 0, 'errortip' => '更新成功'.$num.'条', 'result' => array());
        }else{
            $res = array('error' => 1, 'errortip' => '无主体', 'result' => array());
        }
        ajax_return($res);

    }

    //http://api.kcclassin.com/CmbTrans/updatePlanTransInfoApi
    //根据划拨计划更新耗课数据
    function updatePlanTransInfoApi(){

        $request = Input('get.', '', 'trim,addslashes');
        $datawhere= "companies_issupervise=1 and companies_agencyid<>'' and companies_supervisetime>0 and company_id='8888'";

        if(isset($request['companies_agencyid']) && $request['companies_agencyid']!=''){
            $datawhere.=" and companies_agencyid='{$request['companies_agencyid']}'";
        }

        $sql = "select companies_agencyid,companies_signature,companies_secretkey 
                from gmc_code_companies 
                where {$datawhere}";

        $agencyList=$this->DataControl->selectClear($sql);

        if($agencyList) {
            foreach ($agencyList as $agencyOne) {
                $Model = new \Model\Api\CmbTransModel($agencyOne['companies_agencyid']);
                $Model->updatePlanTransInfo();
            }
        }

        $res = array('error' => 0, 'errortip' => '更新成功', 'result' => array());
        ajax_return($res);

    }


    //http://api.kcclassin.com/CmbTrans/updateSynchroProposePushInfoStatusApi
    //根据划拨计划更新耗课数据
    function updateSynchroProposePushInfoStatusApi(){

        $request = Input('get.', '', 'trim,addslashes');
        $datawhere= "companies_issupervise=1 and companies_agencyid<>'' and companies_supervisetime>0 and company_id='8888'";

        if(isset($request['companies_agencyid']) && $request['companies_agencyid']!=''){
            $datawhere.=" and companies_agencyid='{$request['companies_agencyid']}'";
        }

        $sql = "select companies_agencyid,companies_signature,companies_secretkey 
                from gmc_code_companies 
                where {$datawhere}";

        $agencyList=$this->DataControl->selectClear($sql);

        if($agencyList) {
            foreach ($agencyList as $agencyOne) {
                $Model = new \Model\Api\CmbTransModel($agencyOne['companies_agencyid']);
                $Model->updateSynchroProposePushInfoStatus();
            }
        }

        $res = array('error' => 0, 'errortip' => '更新成功', 'result' => array());
        ajax_return($res);

    }

    function handelClassExceedInfoApi(){

        $request = Input('get.', '', 'trim,addslashes');
        $datawhere= "companies_issupervise=1 and companies_agencyid<>'' and companies_supervisetime>0 and company_id='8888'";

        if(isset($request['companies_agencyid']) && $request['companies_agencyid']!=''){
            $datawhere.=" and companies_agencyid='{$request['companies_agencyid']}'";
        }

        $sql = "select companies_agencyid,companies_signature,companies_secretkey 
                from gmc_code_companies 
                where {$datawhere}";

        $agencyList=$this->DataControl->selectClear($sql);

        if($agencyList) {
            foreach ($agencyList as $agencyOne) {
                $Model = new \Model\Api\CmbTransModel($agencyOne['companies_agencyid']);
                $Model->handelClassExceedInfo();
            }
        }

        $res = array('error' => 0, 'errortip' => '更新成功', 'result' => array());
        ajax_return($res);

    }


    function getSuccessTransInfoApi()
    {

        $sql = "select a.batch_pid,a.agency_id
                from cmb_trans_transfer_batch as a 
                where  a.batch_pid='F68256E1A9E14236762250866683E8A3'
                order by a.batch_id asc
                limit 0,20
                ";

        $batchList=$this->DataControl->selectClear($sql);


        if($batchList){
            $num=0;
            foreach($batchList as $batchOne){
                $Model = new \Model\Api\CmbTransModel($batchOne['agency_id']);
                $n=$Model->getSuccessTransInfo($batchOne);
                if($n){
                    $num+=$n;
                }
            }

            echo '<script language="javascript" type="text/javascript">
                    var i = 1;
                    var intervalid;
                    intervalid = setInterval("fun()", 1000);
                    function fun() {
                        if (i == 0) {
                            window.location.href = "/CmbTransTwo/getSuccessTransInfoApi";
                            clearInterval(intervalid);
                        }
                        document.getElementById("mes").innerHTML = i;
                        i--;
                    }
                    </script>
                    <div id="error">
                        <p>将在 <span id="mes">5</span> 秒钟后返回首页！</p>
                    </div> ';
            $res = array('error' => 0, 'errortip' => '更新成功'.$num.'条', 'result' => array());
        }else{
            $res = array('error' => 1, 'errortip' => '无主体', 'result' => array());
        }
        ajax_return($res);


    }



    //监管系统同步订单
    function getStuOrderListBakApi(){

        $request = Input('get.', '', 'trim,addslashes');

        if(!isset($request['fixedtime']) || $request['fixedtime']==''){
            ajax_return(array('error' => 1, 'errortip' => '请选择更新时间', 'result' => array()));
        }

        $starttime=strtotime($request['fixedtime']);
        $endtime=strtotime($request['fixedtime']." 23:59:59");

        $datawhere = "b.company_id='8888' and c.companies_issupervise=1 and b.order_status=4 and c.companies_supervisetime<=UNIX_TIMESTAMP() and b.order_createtime>=c.companies_supervisetime and c.companies_agencyid<>''";

        if(!isset($request['companies_id']) || $request['companies_id']==''){
            ajax_return(array('error' => 1, 'errortip' => '请选择更新主体', 'result' => array()));
        }else{
            $datawhere .= " and b.companies_id='{$request['companies_id']}'";
        }

//        if(isset($request['companies_id']) && $request['companies_id']!=''){
//            $datawhere .= " and b.companies_id='{$request['companies_id']}'";
//        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        if(isset($request['nextKeyValue']) && $request['nextKeyValue']!=''){
            $pagestart=$request['nextKeyValue'];
        }


        $datawhere .= " and not exists(select 1 from cmb_trans_transfer as x where x.order_pid=a.order_pid and x.is_confirm=1 and x.transfer_status<>0)";

        $datawhere .= " AND NOT EXISTS ( SELECT 1 FROM cmb_trans_order AS x WHERE x.order_pid = a.order_pid ) ";

//        $datawhere .= " and b.order_createtime between '{$starttime}' and '{$endtime}'";

        $datawhere .= " and FROM_UNIXTIME(( SELECT max( x.pay_successtime ) FROM smc_payfee_order_pay AS x WHERE x.order_pid = b.order_pid AND x.pay_issuccess = 1 ), '%Y-%m-%d' ) = '{$request['fixedtime']}' ";

        $datawhere .= " and exists(select 1 from smc_payfee_order_pay as x,smc_code_paytype as y where x.paytype_code=y.paytype_code and x.order_pid=b.order_pid and x.pay_issuccess=1 and y.paytype_ischarge=1)";

        $sql = "select b.student_id,d.student_cnname,d.student_branch,d.student_sex,b.order_pid,a.ordercourse_buynums,a.ordercourse_totalprice,b.order_createtime,b.companies_id,c.companies_agencyid,e.course_classnum,b.school_id,f.school_cnname,f.school_branch,a.course_id,e.course_cnname,e.course_branch,b.company_id
                ,FROM_UNIXTIME((select max(x.pay_successtime) from smc_payfee_order_pay as x where x.order_pid=b.order_pid and x.pay_issuccess=1),'%Y-%m-%d') as order_date
                ,ifnull((select x.family_mobile from smc_student_family as x where x.student_id=b.student_id and x.family_isdefault=1),'') as family_mobile
                from smc_payfee_order_course as a
                inner join smc_payfee_order as b on b.order_pid=a.order_pid
                inner join gmc_code_companies as c on c.companies_id=b.companies_id
                inner join smc_student as d on d.student_id=b.student_id
                inner join smc_course as e on e.course_id=a.course_id
                inner join smc_school as f on f.school_id=b.school_id
                where {$datawhere}
                order by b.order_id asc,a.ordercourse_id asc
                ";

        $sql .= " limit {$pagestart},{$num} ";

        $orderList=$this->DataControl->selectClear($sql);

        if(!$orderList){
            ajax_return(array('error' => 1, 'errortip' => '无订单', 'result' => array()));
        }

        $count_sql = "select a.ordercourse_id
                ,FROM_UNIXTIME((select max(x.pay_successtime) from smc_payfee_order_pay as x where x.order_pid=b.order_pid and x.pay_issuccess=1),'%Y-%m-%d') as order_date
                from smc_payfee_order_course as a
                inner join smc_payfee_order as b on b.order_pid=a.order_pid
                inner join gmc_code_companies as c on c.companies_id=b.companies_id
                where {$datawhere}
                ";

        $db_nums = $this->DataControl->selectClear($count_sql);

        if ($db_nums) {
            $allnum = count($db_nums);
        } else {
            $allnum = 0;
        }

        $data=array();
        $data['list']=$orderList;
        $data['allnum']=$allnum;
        $data['keyvalue']=($pagestart+count($orderList))==$allnum?'N':($pagestart+count($orderList));


        ajax_return(array('error' => 0, 'errortip' => '获取成功', 'result' => $data));

    }

    function getStuOrderListApi(){

        $request = Input('get.', '', 'trim,addslashes');



        $starttime=strtotime($request['fixedtime']);
        $endtime=strtotime($request['fixedtime']." 23:59:59");

        $datawhere = "b.company_id='8888' and c.companies_issupervise=1 and b.order_status=4 and c.companies_supervisetime<=UNIX_TIMESTAMP() and b.order_createtime>=c.companies_supervisetime and c.companies_agencyid<>''";

        if(!isset($request['companies_id']) || $request['companies_id']==''){
            ajax_return(array('error' => 1, 'errortip' => '请选择更新主体', 'result' => array()));
        }else{
            $datawhere .= " and b.companies_id='{$request['companies_id']}'";
        }

//        if(isset($request['companies_id']) && $request['companies_id']!=''){
//            $datawhere .= " and b.companies_id='{$request['companies_id']}'";
//        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        if(isset($request['nextKeyValue']) && $request['nextKeyValue']!=''){
            $pagestart=$request['nextKeyValue'];
        }

        if(isset($request['order_pid']) && $request['order_pid']!=''){
            $datawhere.= " and b.order_pid='{$request['order_pid']}'";
        }else{
            if(!isset($request['fixedtime']) || $request['fixedtime']==''){
                ajax_return(array('error' => 1, 'errortip' => '请选择更新时间', 'result' => array()));
            }

            $datawhere .= " and FROM_UNIXTIME(( SELECT max( x.pay_successtime ) FROM smc_payfee_order_pay AS x WHERE x.order_pid = b.order_pid AND x.pay_issuccess = 1 ), '%Y-%m-%d' ) = '{$request['fixedtime']}' ";
        }


        $datawhere .= " and not exists(select 1 from cmb_trans_transfer as x where x.order_pid=b.order_pid and x.is_confirm=1 and x.transfer_status<>0)";

        $datawhere .= " AND NOT EXISTS ( SELECT 1 FROM cmb_trans_order AS x WHERE x.order_pid = b.order_pid ) ";

//        $datawhere .= " and b.order_createtime between '{$starttime}' and '{$endtime}'";

        $datawhere .= " and exists(select 1 from smc_payfee_order_pay as x,smc_code_paytype as y where x.paytype_code=y.paytype_code and x.order_pid=b.order_pid and x.pay_issuccess=1 and y.paytype_ischarge=1)";

        $sql = "select b.student_id,d.student_cnname,d.student_branch,d.student_sex,b.order_pid,b.order_createtime,b.companies_id,c.companies_agencyid,b.school_id,f.school_cnname,f.school_branch,b.company_id
                ,FROM_UNIXTIME((select max(x.pay_successtime) from smc_payfee_order_pay as x where x.order_pid=b.order_pid and x.pay_issuccess=1),'%Y-%m-%d') as order_date
                ,ifnull((select x.family_mobile from smc_student_family as x where x.student_id=b.student_id and x.family_isdefault=1),'') as family_mobile
                ,ifnull((select sum(x.pay_price) from smc_payfee_order_pay as x,smc_code_paytype as y where x.paytype_code=y.paytype_code and x.order_pid=b.order_pid and x.pay_issuccess=1 and y.paytype_ischarge=1),0) as pay_price
                ,FROM_UNIXTIME((select max(x.pay_successtime) from smc_payfee_order_pay as x,smc_code_paytype as y where x.paytype_code=y.paytype_code and x.order_pid=b.order_pid and x.pay_issuccess=1 and y.paytype_ischarge=1),'%Y-%m-%d') as pay_date
                from smc_payfee_order as b
                inner join gmc_code_companies as c on c.companies_id=b.companies_id
                inner join smc_student as d on d.student_id=b.student_id
                inner join smc_school as f on f.school_id=b.school_id
                where {$datawhere}
                order by b.order_id asc
                ";

        $sql .= " limit {$pagestart},{$num} ";

        $orderList=$this->DataControl->selectClear($sql);

        if(!$orderList){
            ajax_return(array('error' => 1, 'errortip' => '无订单', 'result' => array()));
        }

        $orderArray=array();

        foreach($orderList as $orderOne){

            $allPrice=$orderOne['pay_price'];

            $sql = "select a.ordercourse_buynums,a.ordercourse_totalprice,a.course_id,b.course_cnname,b.course_branch,b.course_classnum 
                    from smc_payfee_order_course as a 
                    inner join smc_course as b on b.course_id=a.course_id
                    where a.order_pid='{$orderOne['order_pid']}'
                    order by a.ordercourse_id asc
                    ";

            $courseList=$this->DataControl->selectClear($sql);

            if($courseList){
                foreach($courseList as $courseOne){

                    if($courseOne['ordercourse_totalprice']>$allPrice){
                        $ordercourse_totalprice=$allPrice;
                    }else{
                        $ordercourse_totalprice=$courseOne['ordercourse_totalprice'];
                    }

                    $orderOne['ordercourse_buynums']=$courseOne['ordercourse_buynums'];
                    $orderOne['ordercourse_totalprice']=$ordercourse_totalprice;
                    $orderOne['course_id']=$courseOne['course_id'];
                    $orderOne['course_cnname']=$courseOne['course_cnname'];
                    $orderOne['course_branch']=$courseOne['course_branch'];
                    $orderOne['course_classnum']=$courseOne['course_classnum'];

                    $orderArray[]=$orderOne;

                    $allPrice-=$ordercourse_totalprice;

                    if($allPrice<=0){
                        break;
                    }
                }
            }
        }

        $count_sql = "select b.order_id
                ,FROM_UNIXTIME((select max(x.pay_successtime) from smc_payfee_order_pay as x where x.order_pid=b.order_pid and x.pay_issuccess=1),'%Y-%m-%d') as order_date
                from smc_payfee_order as b
                inner join gmc_code_companies as c on c.companies_id=b.companies_id
                where {$datawhere}
                ";

        $db_nums = $this->DataControl->selectClear($count_sql);

        if ($db_nums) {
            $allnum = count($db_nums);
        } else {
            $allnum = 0;
        }

        $data=array();
        $data['list']=$orderArray;
        $data['listNum']=$orderList?count($orderList):0;
        $data['allnum']=$allnum;
        $data['keyvalue']=($pagestart+count($orderList))==$allnum?'N':($pagestart+count($orderList));


        ajax_return(array('error' => 0, 'errortip' => '获取成功', 'result' => $data));

    }


    function getSuperviseListApi(){

        $sql = "select a.companies_id,a.company_id,a.companies_cnname,a.companies_branch,a.companies_agencyid,a.companies_issupervise,a.companies_supervisetime 
                from gmc_code_companies as a 
                where a.company_id='8888' and a.companies_issupervise=1";

        $companiesList=$this->DataControl->selectClear($sql);

        if(!$companiesList){
            ajax_return(array('error' => 1, 'errortip' => '无监管主体', 'result' => array()));
        }

        ajax_return(array('error' => 0, 'errortip' => '获取成功', 'result' => ['list'=>$companiesList]));
    }


    function getClassTransferApi(){

        $request = Input('get.', '', 'trim,addslashes');

        if(!isset($request['fixedtime']) || $request['fixedtime']==''){
            ajax_return(array('error' => 1, 'errortip' => '请选择更新时间', 'result' => array()));
        }

        $fixedtime=date("Ymd",strtotime($request['fixedtime']));

        //条件
        $datawhere = " a.date = '{$fixedtime}' AND b.class_enddate >= '2024-01-01' ";

        if(!isset($request['companies_id']) || $request['companies_id']==''){
            ajax_return(array('error' => 1, 'errortip' => '请选择更新主体', 'result' => array()));
        }else{
            $datawhere .= " and a.companies_id='{$request['companies_id']}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '100';
        }
        $pagestart = ($page - 1) * $num;

        if(isset($request['nextKeyValue']) && $request['nextKeyValue']!=''){
            $pagestart=$request['nextKeyValue'];
        }

        $sql = "SELECT
                    a.*
                    ,d.company_id,d.school_id,b.class_cnname,b.class_branch,e.course_classnum as class_lessontimes,b.class_stdate,b.class_enddate,b.class_createtime
                    ,d.student_id,d.course_id,d.parent_mobile,d.order_num,d.order_amt,d.order_fee,d.settle_date 
                    ,e.course_cnname,e.course_branch
                    ,f.student_cnname,f.student_branch,f.student_sex
                    ,ifnull((select sum(x.eliminateAmt) from cmb_trans_mapping_log as x where x.orderNo=a.orderNo and x.transferStatus='S'),0) as transPrice
                    ,h.hour_id,h.hour_lessontimes
                    ,date_sub(a.date, INTERVAL WEEKDAY(a.date) - 6 DAY ) as bank_date
                FROM
                    cmb_trans_mapping_log AS a
                    INNER JOIN smc_class AS b ON b.class_branch = a.classId
                    INNER JOIN cmb_trans_class_trans AS c ON c.classId = b.class_branch
                    INNER JOIN cmb_trans_order AS d ON d.order_pid = a.orderNo 
                    INNER JOIN smc_course as e on e.course_id=d.course_id
                    INNER JOIN smc_student as f on f.student_id=d.student_id
                    INNER JOIN smc_student_hourstudy as g on g.hourstudy_id=a.eliminateId
                    INNER JOIN smc_class_hour as h on h.hour_id=g.hour_id
                WHERE {$datawhere}
                    AND c.surplusAmt > 0 and a.eliminateAmt>0
                    ";

        $sql .= " limit {$pagestart},{$num} ";

        $transList=$this->DataControl->selectClear($sql);

        if(!$transList){
            ajax_return(array('error' => 1, 'errortip' => '无需要同步课销记录', 'result' => array()));
        }

        $count_sql = "SELECT a.log_id
                        FROM
                            cmb_trans_mapping_log AS a
                            INNER JOIN smc_class AS b ON b.class_branch = a.classId
                            INNER JOIN cmb_trans_class_trans AS c ON c.classId = b.class_branch
                            INNER JOIN cmb_trans_order AS d ON d.order_pid = a.orderNo 
                        WHERE {$datawhere}
                            AND c.surplusAmt > 0  and a.eliminateAmt>0
                        ";

        $db_nums = $this->DataControl->selectClear($count_sql);

        if ($db_nums) {
            $allnum = count($db_nums);
        } else {
            $allnum = 0;
        }

        $data=array();
        $data['list']=$transList;
        $data['allnum']=$allnum;
        $data['keyvalue']=($pagestart+count($transList))==$allnum?'N':($pagestart+count($transList));


        ajax_return(array('error' => 0, 'errortip' => '获取成功', 'result' => $data));




    }


    function getClassLessonListApi(){

        $request = Input('get.', '', 'trim,addslashes');

        if(!isset($request['class_branch']) || $request['class_branch']==''){
            ajax_return(array('error' => 1, 'errortip' => '请选择更新班级', 'result' => array()));
        }

        $classOne=$this->DataControl->getOne("cmb_trans_class","class_branch='{$request['class_branch']}'");

        $sql = "SELECT
                    a.class_id 
                FROM
                    cmb_trans_class AS a 
                WHERE
                    a.class_branch='{$request['class_branch']}' and a.class_enddate >= '2024-01-01' 
                    AND EXISTS (
                    SELECT
                        1 
                    FROM
                        cmb_trans_lesson AS x 
                WHERE
                    x.class_id = a.class_id)";

        if(!$this->DataControl->selectOne($sql)){
            $Model = new \Model\Api\CmbDataprocessModel($classOne['agency_id']);
            $bool=$Model->addCmbLessonInfo($request['class_branch']);

        }

        $sql = "select a.*,c.hour_ischecking
                from cmb_trans_lesson as a 
                inner join cmb_trans_class as b on b.class_id=a.class_id
                inner join smc_class_hour as c on c.hour_id=a.hour_id
                where b.class_branch='{$request['class_branch']}' and c.hour_ischecking>=0
                order by a.lesson_id asc
                ";

        $lessonList=$this->DataControl->selectClear($sql);

        if(!$lessonList){
            ajax_return(array('error' => 1, 'errortip' => '无排课信息', 'result' => array()));
        }

        ajax_return(array('error' => 0, 'errortip' => '获取成功', 'result' => ['list'=>$lessonList]));
    }

//api.kcclassin.com/CmbTransTwo/reduceClassBalanceApi
    function reduceClassBalanceApi(){
        $request = Input('get.', '', 'trim,addslashes');
        $dateWhere = "companies_issupervise=1 and companies_agencyid<>'' and companies_supervisetime>0 and company_id='8888' and companies_id<>'9'";
        if(isset($request['companies_agencyid'])) {
            $dateWhere .= " AND companies_agencyid = '{$request['companies_agencyid']}'";
        }

        $sql = "select companies_agencyid,companies_signature,companies_secretkey,companies_id  from gmc_code_companies  where {$dateWhere} ";
        $sql.=" and (companies_id in (8))";
        $agencyList=$this->DataControl->selectClear($sql);
        if($agencyList){
            $all_num=0;
            foreach($agencyList as $agencyOne){
                $Model = new \Model\Api\CmbTransModel($agencyOne['companies_agencyid']);
                $num = $Model->reduceClassBalance($agencyOne['companies_id']);
                $all_num+=$num;
            }

            if($all_num>0){
                echo '<script language="javascript" type="text/javascript">
                var i = 1;
                var intervalid;
                intervalid = setInterval("fun()", 1000);
                function fun() {
                    if (i == 0) {
                        window.location.href = "/CmbTransTwo/reduceClassBalanceApi";
                        clearInterval(intervalid);
                    }
                    document.getElementById("mes").innerHTML = i;
                    i--;
                }
                </script>
                <div id="error">
                    <p>将在 <span id="mes">5</span> 秒钟后返回首页！</p>
                </div> ';
            }

            $res = array('error' => 0, 'errortip' => "操作成功", 'result' => array());
        }else{
            $res = array('error' => 1, 'errortip' => '无主体', 'result' => array());
        }

        ajax_return($res);





    }



}