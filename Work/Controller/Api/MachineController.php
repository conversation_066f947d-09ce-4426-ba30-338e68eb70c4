<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2021/6/2
 * Time: 10:46
 */

namespace Work\Controller\Api;


class MachineController extends viewTpl
{
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $stafferOne = array();

    function __construct()
    {
        parent::__construct();
    }

    function stringReplace($string)
    {
        $datacode = trim(str_replace('"', "", $string));
        $datacode = urldecode(urldecode($datacode));
        $datacode = str_replace(' ', "+", $datacode);
        return $datacode;
    }

    //第三方授权访问权限校验
    function UserVerify($paramArray)
    {
        if($paramArray['isEncryption'] != '9'){
            return true;
        }else {
            $apiuserOne = $this->DataControl->getFieldOne('imc_apiuser', "apiuser_id,apiuser_aeskey,apiuser_aesiv", "apiuser_code = '{$paramArray['apiuser_code']}'");
            if (!$apiuserOne) {
                $this->errortip = "未查询到您的授权信息";
                $this->error = true;
                return false;
            }

            if (isset($paramArray['apiuser_aeskey']) && $paramArray['apiuser_aeskey'] !== '' && isset($paramArray['apiuser_aesiv']) && $paramArray['apiuser_aesiv'] !== '') {
                if ($apiuserOne['apiuser_aeskey'] == $paramArray['apiuser_aeskey'] && $apiuserOne['apiuser_aesiv'] == $paramArray['apiuser_aesiv']) {
                    $baseOne = array();
                    $baseOne['apiuser_id'] = $apiuserOne['apiuser_id'];
                    $companyOne = $this->DataControl->selectOne("select company_id,company_cnname from gmc_company WHERE company_id = '{$paramArray['company_id']}' limit 0,1");
                    if ($companyOne) {
                        $baseOne['company_id'] = $companyOne['company_id'];
                        return $baseOne;
                    } else {
                        $this->errortip = '你的授权集团编号错误，请确认编号正确';
                        $this->error = true;
                        return false;
                    }
                } else {
                    $this->errortip = "你的授权秘钥及偏移值不正确，{$paramArray['apiuser_aeskey']}-{$paramArray['apiuser_aesiv']}";
                    $this->error = true;
                    return false;
                }
            }

            if (!isset($paramArray['timesteps']) || $paramArray['timesteps'] == '') {
                $this->errortip = "请传入授权时间";
                $this->error = true;
                return false;
            }

            if($paramArray['isOneHour'] == '1'){
                if ($paramArray['timesteps'] + 60 * 30 < time() || $paramArray['timesteps'] - 60 * 30 > time()) {
                    $maxtimes = date("Y-m-d H:i:s", $paramArray['timesteps']);
                    $this->errortip = "授权时间{$maxtimes}已过期，请确认连接及时性";//,{$timesteps}--{$jmsting}
                    $this->error = true;
                    return false;
                }
            }else{
                if ($paramArray['timesteps'] + 60 * 5 < time() || $paramArray['timesteps'] - 60 > time()) {
                    $maxtimes = date("Y-m-d H:i:s", $paramArray['timesteps'] + 60 * 5);
                    $this->errortip = "授权时间{$maxtimes}已过期5分钟，请确认连接及时性";//,{$timesteps}--{$jmsting}
                    $this->error = true;
                    return false;
                }
            }

            $aes = new \Aesencdec($apiuserOne['apiuser_aeskey'], $apiuserOne['apiuser_aesiv']);
            $xssting = $aes->decrypt($this->stringReplace($paramArray['veytoken']));//解密
            if ($paramJson = json_decode($xssting, 1)) {//转化为数组
                if ((string)$paramJson['timesteps'] !== trim($paramArray['timesteps'])) {
                    $this->errortip = '授权时间和连接时间不一致';
                    $this->error = true;
                    return false;
                }
                $baseOne = array();
                $baseOne['apiuser_id'] = $apiuserOne['apiuser_id'];
                $baseOne['tokenstring'] = $xssting;
                $companyOne = $this->DataControl->selectOne("select company_id,company_cnname from gmc_company WHERE company_id = '{$paramJson['company_id']}' limit 0,1");
                if ($companyOne) {
                    $baseOne['company_id'] = $companyOne['company_id'];
                    return $baseOne;
                } else {
                    $this->errortip = '你的授权集团编号错误，请确认编号正确';
                    $this->error = true;
                    return false;
                }
            } else {
                $this->errortip = '数据机密信息传输有误，请检查！';
                $this->error = true;
                return false;
            }
        }
    }

    function VeryModelNums($apiuser_id, $apimodule_code, $paramArray)
    {
        if($paramArray['isEncryption'] != '9'){
            return true;
        }else {
            $request = Input('get.', '', 'trim,addslashes');
            $apimoduleOne = $this->DataControl->getFieldOne('imc_apiuser_apimodule'
                , "apimodule_id,apiuser_id,apimodule_name,apimodule_nums", "apiuser_id = '{$apiuser_id}' AND apimodule_code = '{$apimodule_code}'");
            $stattTimes = strtotime(date("Y-m-d"));
            $apilogOne = $this->DataControl->selectOne("SELECT COUNT(l.apilog_id) AS anums FROM imc_apiuser_apilog AS l
WHERE l.apimodule_id = '{$apimoduleOne['apimodule_id']}' AND l.apilog_createtime > '{$stattTimes}'");
            if ($apilogOne['anums'] > $apimoduleOne['apimodule_nums']) {
                $this->errortip = "您接口{$apimoduleOne['apimodule_name']}的本日最大授权次数{$apimoduleOne['apimodule_nums']}已消耗完毕！";
                $this->error = true;
                return false;
            } else {
                $data = array();
                $data['apiuser_id'] = $apimoduleOne['apiuser_id'];
                $data['apimodule_id'] = $apimoduleOne['apimodule_id'];
                $data['apilog_posturl'] = "https://api.kedingdang.com/{$request['u']}/{$request['t']}";
                $data['apilog_posttype'] = 'GET';
                $data['apilog_postorgjson'] = http_build_query($paramArray);
                $data['apilog_postjson'] = $paramArray['tokenstring'];
                $data['apilog_ip'] = real_ip();
                $data['apilog_createtime'] = time();
                $this->DataControl->insertData("imc_apiuser_apilog", $data);
                return true;
            }
        }
    }

    //模拟加密参数
    function testParameterView()
    {
        $parameter = array();
        $parameter['timesteps'] = 1679241600;
        $parameter['apiuser_code'] = 'addMachineStu';
        $parameter['company_id'] = '8888';

        $apiuserOne = $this->DataControl->getFieldOne('imc_apiuser', "apiuser_id,apiuser_aeskey,apiuser_aesiv", "apiuser_code = '{$parameter['apiuser_code']}'");
        if (!$apiuserOne) {
            $this->errortip = "未查询到您的授权信息";
            $this->error = true;
            return false;
        }

        $aes = new \Aesencdec($apiuserOne['apiuser_aeskey'], $apiuserOne['apiuser_aesiv']);
        $parameterJson = json_encode($parameter, '1');
        $jmsting = $aes->encrypt($parameterJson);//解密

        $result = array();
        $result['timesteps'] = $parameter['timesteps'];
        $result['apiuser_code'] = $parameter['apiuser_code'];
        $result['veytoken'] = $jmsting;

        ajax_return(array('error' => '0', 'errortip' => '模拟参数获取成功', 'result' => $result));
    }

    //版本号比对
    function str_compare($str1, $str2){
        $arr1 = explode('.',$str1);
        $arr2 = explode('.',$str2);
        for($i=0;$i < count($arr1);$i++){
            if($arr1[$i] > $arr2[$i]){
                return 1;
            }elseif($arr1[$i] < $arr2[$i]){
                return -1;
            }
        }
        return 0;
    }
    //版本控制
    function getNewMachineEditionApi(){
        $request = Input('get.', '', 'trim,addslashes');
        $request['isOneHour'] = '1';
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'getNewMachineEditionApi', $request)) {
                if(trim($request['edition_number']) == ''){
                    ajax_return(array('error' => '1', 'errortip' => '本地的版本号必须传值', 'result' => array()));
                }
                $machineversion = $this->DataControl->selectOne(" select content from cms_variable where variable_string = 'MachineNewVersion' limit 0,1 ");

                $editionOne = array();
                $editionOne['edition_number'] = trim($machineversion['content'])?trim($machineversion['content']):'99.0.0';
                $editionOne['edition_url'] = 'https://appdown.chevady.cn/XWmachine/KQmachine.apk';
                $editionOne['edition_ismust'] = '1';
                $editionOne['edition_explain'] = '新版本请更新下载';

                $result = array();
                $result['list'] = $editionOne;

                if ($editionOne) {
                    ajax_return(array('error' => '0', 'errortip' => "最新版本数据获取成功", 'result' => $result));
                } else {
                    ajax_return(array('error' => '1', 'errortip' => "您已经是最新版本了", 'result' => array()));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }
    //考勤机 心跳记录 （考勤机每间隔一段时间请求一次）
    function getMachineHeartbeatApi(){
        $request = Input('post.', '', 'trim,addslashes');
        $request['isOneHour'] = '1';
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'getMachineHeartbeatApi', $request)) {
                if(trim($request['machine_id']) == ''){
                    ajax_return(array('error' => '1', 'errortip' => '设备ID必须传值', 'result' => array()));
                }
                if(trim($request['school_id']) == ''){
                    ajax_return(array('error' => '1', 'errortip' => '学校ID必须传值', 'result' => array()));
                }
                if(trim($request['company_id']) == ''){
                    ajax_return(array('error' => '1', 'errortip' => '集团ID必须传值', 'result' => array()));
                }

                $heartbeat = $this->DataControl->selectOne(" select heartbeat_creattime from gmc_machine_heartbeat where company_id = '{$request['company_id']}' and school_id = '{$request['school_id']}' and  machine_id = '{$request['machine_id']}' order by heartbeat_id desc limit 0,1 ");

                $date =array();
                $date['company_id'] = $request['company_id'];
                $date['school_id'] = $request['school_id'];
                $date['machine_id'] = $request['machine_id'];
                $date['heartbeat_edition'] = $request['heartbeat_edition'];
                $date['heartbeat_intervaltime'] = ($heartbeat['heartbeat_creattime']>1)?(time() - $heartbeat['heartbeat_creattime']):0;
                $date['heartbeat_creattime'] = time();
                $heartbeatID = $this->DataControl->insertData('gmc_machine_heartbeat',$date);

                if ($heartbeatID) {
                    ajax_return(array('error' => '0', 'errortip' => "心跳记录成功", 'result' => array()));
                } else {
                    ajax_return(array('error' => '1', 'errortip' => "心跳记录失败", 'result' => array()));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }
    //通过机器码获取学校信息
    function getSchoolDetailApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $request['isOneHour'] = '1';
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'getSchoolDetailApi', $request)) {

                if($request['machine_code'] == '') {
                    ajax_return(array('error' => '1', 'errortip' => '考勤机序列号不能为空', 'result' => array()));
                }

                $schoolOne = $this->DataControl->selectOne("select m.machine_id,m.company_id,s.school_id,s.school_cnname,s.school_enname,s.school_shortname,s.school_branch,m.machine_brand   
                    from gmc_machine as m,smc_school as s 
                    where m.machine_code = '{$request['machine_code']}' and m.machine_state = '1' and m.school_id = s.school_id limit 0,1 ");

                if(!$schoolOne){
                    ajax_return(array('error' => '1', 'errortip' => "未绑定学校！", 'result' => array()));
                }

                $schoolOne['machine_brand'] = (int)$schoolOne['machine_brand'];

                $schoolOne['heartbeat_time'] = 300;//单位：秒  间隔5分钟
                $schoolOne['getSchoolStuList_time'] = 20;//单位：秒  间隔5分钟
                $schoolOne['getSchoolStuOne_time'] = 10;//单位：秒  间隔5分钟
                $schoolOne['addStuClockOne_time'] = 6;//单位：秒  间隔5分钟


                $field = [
                    "machine_id"=>"设备ID",
                    "company_id"=>"集团ID",
                    "school_id"=>"学校ID",
                    "school_shortname"=>"学校简称",
                    "school_branch"=>"学校编号",
                    "machine_brand"=>"品牌 1 中视 2 慧显",

                    "heartbeat_time"=>"设备心跳监测间隔时间 单位：秒",
                    "getSchoolStuList_time"=>"学生列表获取的间隔时间 单位：秒",
                    "getSchoolStuOne_time"=>"学生照片获取的间隔时间 单位：秒",
                    "addStuClockOne_time"=>"上传单条考勤记录的间隔时间 单位：秒",
                ];

                $result = array();
                $result["field"] = $field;
                $result['list'] =$schoolOne?$schoolOne:array();
                if ($schoolOne) {
                    ajax_return(array('error' => '0', 'errortip' => "学校信息获取成功", 'result' => $result));
                } else {
                    ajax_return(array('error' => '1', 'errortip' => "学校信息获取失败", 'result' => array()));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }
    //通过学校编号获取学生列表信息
    function getSchoolStuListApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $request['isOneHour'] = '1';
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'getSchoolStuListApi', $request)) {

                if($request['school_id'] == '') {
                    ajax_return(array('error' => '1', 'errortip' => '学校ID不能为空', 'result' => array()));
                }

//                if (isset($request['p']) && $request['p'] !== '') {
//                    $page = $request['p'];
//                } else {
//                    $page = '1';
//                }
//                if (isset($request['num']) && $request['num'] !== '') {
//                    $num = $request['num'];
//                } else {
//                    $num = '10';
//                }
//                $pagestart = ($page - 1) * $num;


                $datawhere = " e.school_id = '{$request['school_id']}' and e.school_id > 1  ";
                if(isset($request['enrolled_updatatime']) && $request['enrolled_updatatime'] != ''){
                    $datawhere .= " and  ( s.student_updatatime > '{$request['enrolled_updatatime']}' or s.student_createtime > '{$request['enrolled_updatatime']}' or (select g.changelog_createtime from smc_student_changelog as g where g.student_id = s.student_id and g.school_id = e.school_id order by g.changelog_createtime desc limit 0,1) > '{$request['enrolled_updatatime']}' )";
                }

                $sql = " select s.student_id,s.student_cnname,s.student_enname,s.student_branch,s.student_sex,s.student_img,e.enrolled_status,s.student_createtime,s.student_updatatime,
                -- if(e.enrolled_createtime>e.enrolled_updatatime,e.enrolled_createtime,e.enrolled_updatatime) as enrolled_updatatime,
                (select g.changelog_createtime from smc_student_changelog as g where g.student_id = s.student_id and g.school_id = e.school_id order by g.changelog_createtime desc limit 0,1) as enrolled_updatatime, 
                (select g.stuportrait_faceimg from gmc_machine_stuportrait as g where g.student_id = s.student_id  order by g.stuportrait_updatetime desc,g.stuportrait_id desc limit 0,1) as stuportrait_faceimg ,
                (select g.stuportrait_updatetime from gmc_machine_stuportrait as g where g.student_id = s.student_id  order by g.stuportrait_updatetime desc,g.stuportrait_id desc limit 0,1) as stuportrait_updatetime 
                from smc_student_enrolled as e 
                left join smc_student as s ON e.student_id = s.student_id  
                where {$datawhere}";
//                limit {$pagestart},{$num}
                $studentlist = $this->DataControl->selectClear($sql);

                if($studentlist){
                    foreach ($studentlist as &$studentvar){
                        $studentvar['stuportrait_faceimg'] = is_null($studentvar['stuportrait_faceimg'])?'':$studentvar['stuportrait_faceimg'];
                        $studentvar['ishaveportrait'] = $studentvar['stuportrait_faceimg']?1:0;
                        if($studentvar['student_sex'] != '男' && $studentvar['student_sex'] != '女'){
                            $studentvar['student_sex'] = '';
                        }
                    }
                }

                $field = [
                    "student_id"=>"学生ID",
                    "student_cnname"=>"学生中文名",
                    "student_enname"=>"学生英文名",
                    "student_branch"=>"学生编号",
                    "student_sex"=>"学生性别 男、女、空值",
                    "student_img"=>"学生头像",
                    "student_createtime"=>"学生创建时间",
                    "student_updatatime"=>"学生更新时间",
                    "enrolled_status"=>"学生入校状态 0待入班 1已入班 -1已离校 2已毕业 3保留学籍",
                    "enrolled_updatatime"=>"更新时间",
                    "ishaveportrait"=>"是否有人像 1有 0没有",
                    "stuportrait_faceimg"=>"最后一次采集的人像",
                    "stuportrait_updatetime"=>"照片更新时间",
                ];

                $result = array();
                $result["field"] = $field;
                $result['list'] =$studentlist?$studentlist:array();
                if ($studentlist) {
                    ajax_return(array('error' => '0', 'errortip' => "学生信息获取成功", 'result' => $result));
                } else {
                    ajax_return(array('error' => '1', 'errortip' => "学生信息获取失败", 'result' => array()));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }
    function getSchoolStuListBakApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'getSchoolStuListApi', $request)) {

                if($request['school_id'] == '') {
                    ajax_return(array('error' => '1', 'errortip' => '学校ID不能为空', 'result' => array()));
                }

//                if (isset($request['p']) && $request['p'] !== '') {
//                    $page = $request['p'];
//                } else {
//                    $page = '1';
//                }
//                if (isset($request['num']) && $request['num'] !== '') {
//                    $num = $request['num'];
//                } else {
//                    $num = '10';
//                }
//                $pagestart = ($page - 1) * $num;


                $datawhere = " e.school_id = '{$request['school_id']}' and e.school_id > 1  ";
                if(isset($request['enrolled_updatatime']) && $request['enrolled_updatatime'] != ''){
                    $datawhere .= " and  ( e.enrolled_updatatime > '{$request['enrolled_updatatime']}' or s.student_updatatime > '{$request['enrolled_updatatime']}' or s.student_createtime > '{$request['enrolled_updatatime']}')";
                }

                $sql = " select s.student_id,s.student_cnname,s.student_enname,s.student_branch,s.student_sex,s.student_img,e.enrolled_status,s.student_createtime,s.student_updatatime,
                if(e.enrolled_createtime>e.enrolled_updatatime,e.enrolled_createtime,e.enrolled_updatatime) as enrolled_updatatime,
                (select g.stuportrait_faceimg from gmc_machine_stuportrait as g where g.student_id = s.student_id  order by g.stuportrait_updatetime desc limit 0,1) as stuportrait_faceimg ,
                (select g.stuportrait_updatetime from gmc_machine_stuportrait as g where g.student_id = s.student_id  order by g.stuportrait_updatetime desc limit 0,1) as stuportrait_updatetime 
                from smc_student_enrolled as e 
                left join smc_student as s ON e.student_id = s.student_id  
                where {$datawhere}";
//                limit {$pagestart},{$num}
                $studentlist = $this->DataControl->selectClear($sql);

                if($studentlist){
                    foreach ($studentlist as &$studentvar){
                        $studentvar['stuportrait_faceimg'] = is_null($studentvar['stuportrait_faceimg'])?'':$studentvar['stuportrait_faceimg'];
                        $studentvar['ishaveportrait'] = $studentvar['stuportrait_faceimg']?1:0;
                        if($studentvar['student_sex'] != '男' && $studentvar['student_sex'] != '女'){
                            $studentvar['student_sex'] = '';
                        }
                    }
                }

                $field = [
                    "student_id"=>"学生ID",
                    "student_cnname"=>"学生中文名",
                    "student_enname"=>"学生英文名",
                    "student_branch"=>"学生编号",
                    "student_sex"=>"学生性别 男、女、空值",
                    "student_img"=>"学生头像",
                    "student_createtime"=>"学生创建时间",
                    "student_updatatime"=>"学生更新时间",
                    "enrolled_status"=>"学生入校状态 0待入班 1已入班 -1已离校 2已毕业 3保留学籍",
                    "enrolled_updatatime"=>"更新时间",
                    "ishaveportrait"=>"是否有人像 1有 0没有",
                    "stuportrait_faceimg"=>"最后一次采集的人像",
                    "stuportrait_updatetime"=>"照片更新时间",
                ];

                $result = array();
                $result["field"] = $field;
                $result['list'] =$studentlist?$studentlist:array();
                if ($studentlist) {
                    ajax_return(array('error' => '0', 'errortip' => "学生信息获取成功", 'result' => $result));
                } else {
                    ajax_return(array('error' => '1', 'errortip' => "学生信息获取失败", 'result' => array()));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //头像上传接口
    function PictureView()
    {
        $request = Input('post.','','trim,addslashes');
        if (!$request['company_id']) {
            $res = array('error' => '1', 'errortip' => "请传入公司ID", 'result' => array());
            ajax_return($res,$request['language_type']);
        }
        if (! empty ( $_FILES ["ossfile"] )) {
            $md5file = md5_file($_FILES['ossfile']['tmp_name']);
            $getTimg = $this->DataControl->getFieldOne('gmc_upload_picture',"picture_id,picture_imgurl,picture_thumburl","picture_md5='{$md5file}' and company_id = '{$request['company_id']}'");
            if($getTimg){
                $result = array();
                $result['picture_imgurl'] = $getTimg['picture_imgurl'];
                $result['picture_thumburl'] = $getTimg['picture_thumburl'];
                $res = array('error' => 0,'errortip' => "图片上传成功!","result"=>$result);
                ajax_return($res,$request['language_type']);
            }else{
                $md5file = md5_file($_FILES['ossfile']['tmp_name']);
                $imglink = UpOssFile($_FILES);
                if($imglink){
                    $date =array();
                    $date['company_id'] = $request['company_id'];
                    $date['picture_name'] = $_FILES['ossfile']['tmp_name'];
                    $date['picture_imgurl'] = $imglink;
                    $date['picture_thumburl'] = $imglink;
                    $date['picture_md5'] = $md5file;
                    $this->DataControl->insertData('gmc_upload_picture',$date);

                    $result = array();
                    $result['picture_imgurl'] = $date['picture_imgurl'];
                    $result['picture_thumburl'] = $date['picture_thumburl'];
                    $res = array('error' => 0,'errortip' => "图片上传成功!","result"=>$result);
                    ajax_return($res,$request['language_type']);
                }else{
                    $res = array('error' => 1,'errortip' => "图片上传失败!","result"=>array());
                    ajax_return($res,$request['language_type']);
                }
            }
        }else{
            $res = array('error' => 1,'errortip' => "您未选择任何图片上传!","result"=>array());
            ajax_return($res,$request['language_type']);
        }
    }
    //头像上传接口 -- 人脸上传图片不存储数据库校验
    function PictureNewView()
    {
        $request = Input('post.','','trim,addslashes');
        if (!$request['company_id']) {
            $res = array('error' => '1', 'errortip' => "请传入公司ID", 'result' => array());
            ajax_return($res,$request['language_type']);
        }
        if (! empty ( $_FILES ["ossfile"] )) {
            $imglink = UpOssFile($_FILES);
            if($imglink){
                $result = array();
                $result['picture_imgurl'] = $imglink;
                $result['picture_thumburl'] = $imglink;
                $res = array('error' => 0,'errortip' => "图片上传成功!","result"=>$result);
                ajax_return($res,$request['language_type']);
            }else{
                $res = array('error' => 1,'errortip' => "图片上传失败!","result"=>array());
                ajax_return($res,$request['language_type']);
            }
        }else{
            $res = array('error' => 1,'errortip' => "您未选择任何图片上传!","result"=>array());
            ajax_return($res,$request['language_type']);
        }
    }
    //获取错误日志
    function getMachineFilelogView(){
        $request = Input('get.','','trim,addslashes');

        $datawhere = ' 1 ';
        if (isset($request['starttime']) && $request['starttime']) {
            $stime = strtotime($request['starttime']);
            $datawhere .= " and filelog_creattime>='{$stime}' ";
        }else{
            $stime = strtotime(date("Y-m-d"));
            $datawhere .= " and filelog_creattime>='{$stime}' ";
        }

        if (isset($request['endtime']) && $request['endtime']) {
            $etime = strtotime($request['endtime'])+86400;
            $datawhere .= " and filelog_creattime<'{$etime}' ";
        }else{
            $etime = time();
            $datawhere .= " and filelog_creattime<'{$etime}' ";
        }

        $logdata = $this->DataControl->selectClear("select company_id,machine_code,filelog_fileurl,FROM_UNIXTIME(filelog_creattime) from gmc_machine_filelog where {$datawhere} ");
        $res = array('error' => 0,'errortip' => "文件上传成功!","result"=>$logdata);
        ajax_return($res,$request['language_type']);

    }
    //考勤机-错误日志记录
    function addMachineFilelog($company_id=0,$machine_code=0,$filelog_fileurl=''){
        $date =array();
        $date['company_id'] = $company_id;
        $date['machine_code'] = $machine_code;
        $date['filelog_fileurl'] = $filelog_fileurl;
        $date['filelog_creattime'] = time();
        $this->DataControl->insertData('gmc_machine_filelog',$date);
        return true;
    }
    //考勤机-错误日志记录
    function uploadFileView()
    {
        $request = Input('post.','','trim,addslashes');
        if (!$request['company_id'] || $request['company_id'] == '') {
            $res = array('error' => '1', 'errortip' => "请传入公司ID", 'result' => array());
            ajax_return($res,$request['language_type']);
        }
        if (!$request['machine_code'] || $request['machine_code'] == '') {
            $res = array('error' => '1', 'errortip' => "请传入设备Code", 'result' => array());
            ajax_return($res,$request['language_type']);
        }
        if (! empty ( $_FILES ["ossfile"] )) {
            $md5file = md5_file($_FILES['ossfile']['tmp_name']);
            $getTimg = $this->DataControl->getFieldOne('gmc_upload_file',"file_id,file_url","file_md5='{$md5file}' and company_id = '{$request['company_id']}'");
            if($getTimg){
                //考勤机 错误文件添加日志
                $this->addMachineFilelog($request['company_id'],$request['machine_code'],$getTimg['file_url']);

                $result = array();
                $result['file_url'] = $getTimg['file_url'];
                $res = array('error' => 0,'errortip' => "文件上传成功!","result"=>$result);
                ajax_return($res,$request['language_type']);
            }else{
                $fileurl = UpOssFile($_FILES);
                if($fileurl){
                    $date =array();
                    $date['company_id'] = $request['company_id'];
                    $date['file_name'] = $_FILES['ossfile']['name'];
                    $date['file_url'] = $fileurl;
                    $date['file_md5'] = $md5file;
                    $this->DataControl->insertData('gmc_upload_file',$date);

                    //考勤机 错误文件添加日志
                    $this->addMachineFilelog($request['company_id'],$request['machine_code'],$fileurl);

                    $result = array();
                    $result['file_url'] = $date['file_url'];
                    $res = array('error' => 0,'errortip' => "文件上传成功!","result"=>$result);
                    ajax_return($res,$request['language_type']);
                }else{
                    $res = array('error' => 1,'errortip' => "文件上传失败!","result"=>array());
                    ajax_return($res,$request['language_type']);
                }
            }
        }else{
            $res = array('error' => 1,'errortip' => "您未选择任何文件上传!","result"=>array());
            ajax_return($res,$request['language_type']);
        }
    }
    //单条 -- 出勤记录回传接口
    function addStuClockOneApi()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $request['isOneHour'] = '1';
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'addStuClockOneApi', $request)) {

                if($request['company_id'] == '' || $request['company_id'] == '0') {
                    ajax_return(array('error' => '1', 'errortip' => '集团ID不能为空', 'result' => array()));
                }
                if($request['school_id'] == '' || $request['school_id'] == '0') {
                    ajax_return(array('error' => '1', 'errortip' => '学校ID不能为空', 'result' => array()));
                }
                if($request['machine_id'] == '' || $request['machine_id'] == '0') {
                    ajax_return(array('error' => '1', 'errortip' => '设备ID不能为空', 'result' => array()));
                }
                if($request['student_id'] == '') {
                    ajax_return(array('error' => '1', 'errortip' => '学生ID不能为空', 'result' => array()));
                }
                if($request['cardlog_faceimg'] == '') {
                    ajax_return(array('error' => '1', 'errortip' => '学生人像图片不能为空', 'result' => array()));
                }

                $dataOne = $this->DataControl->selectOne(" select * from gmc_machine_stucardlog where student_id = '{$request['student_id']}' and cardlog_clocktime = '{$request['cardlog_clocktime']}'  ");
                if($dataOne){
                    ajax_return(array('error' => '1', 'errortip' => '本条记录已经同步', 'result' => array()));
                }

                $adddata = array();
                $adddata['company_id'] = $request['company_id'];
                $adddata['school_id'] = $request['school_id'];
                $adddata['machine_id'] = $request['machine_id'];
                $adddata['student_id'] = $request['student_id'];
                $adddata['cardlog_type'] = 1;
                $adddata['cardlog_temperature'] = $request['cardlog_temperature'];
                $adddata['cardlog_similarity'] = ($request['cardlog_similarity']==null)?'':$request['cardlog_similarity'];//相似度
                $adddata['cardlog_faceimg'] = $request['cardlog_faceimg'];
                $adddata['cardlog_state'] = 1;
                $adddata['cardlog_clocktime'] = $request['cardlog_clocktime'];
                $adddata['cardlog_creattime'] = time();
                $stucardlogid = $this->DataControl->insertData('gmc_machine_stucardlog', $adddata);
                if($stucardlogid){
                    ajax_return(array('error' => '0', 'errortip' => "出勤记录登记成功", 'result' => array()));
                } else {
                    ajax_return(array('error' => '1', 'errortip' => "出勤记录登记失败", 'result' => array()));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }
    //多条 -- 出勤记录回传接口
    function addStuClockSomeApi()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $request['isOneHour'] = '1';
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'addStuClockSomeApi', $request)) {

                $succ = '';
                $fail = '';
                if (isset($request['somestustr']) && $request['somestustr'] !== '' && $request['somestustr'] != '[]') {
                    $someArray = json_decode(stripslashes($request['somestustr']), 1);
                    if (is_array($someArray) && count($someArray) > 0) {
                        //新增判断
                        foreach ($someArray as $someVar) {
                            if($someVar['company_id'] == '' || $someVar['company_id'] == '0') {
                                $fail = $fail?$fail.','.$someVar['attendance_id']:$someVar['attendance_id'];
                                continue;
                            }
                            if($someVar['school_id'] == '' || $someVar['school_id'] == '0') {
                                $fail = $fail?$fail.','.$someVar['attendance_id']:$someVar['attendance_id'];
                                continue;
                            }
                            if($someVar['machine_id'] == '' || $someVar['machine_id'] == '0') {
                                $fail = $fail?$fail.','.$someVar['attendance_id']:$someVar['attendance_id'];
                                continue;
                            }
                            if($someVar['student_id'] == '') {
                                $fail = $fail?$fail.','.$someVar['attendance_id']:$someVar['attendance_id'];
                                continue;
                            }
                            if($someVar['cardlog_faceimg'] == '') {
                                $fail = $fail?$fail.','.$someVar['attendance_id']:$someVar['attendance_id'];
                                continue;
                            }

                            $dataOne = $this->DataControl->selectOne(" select * from gmc_machine_stucardlog where student_id = '{$someVar['student_id']}' and cardlog_clocktime = '{$someVar['cardlog_clocktime']}'  ");
                            if($dataOne){
                                $succ = $succ ? $succ.','.$someVar['attendance_id'] : $someVar['attendance_id'];
                                continue;
//                                $fail = $fail?$fail.','.$someVar['attendance_id']:$someVar['attendance_id'];
//                                continue;
                            }

                            $adddata = array();
                            $adddata['company_id'] = $someVar['company_id'];
                            $adddata['school_id'] = $someVar['school_id'];
                            $adddata['machine_id'] = $someVar['machine_id'];
                            $adddata['student_id'] = $someVar['student_id'];
                            $adddata['cardlog_type'] = 1;
                            $adddata['cardlog_temperature'] = $someVar['cardlog_temperature'];
                            $adddata['cardlog_similarity'] = ($someVar['cardlog_similarity']==null)?'':$someVar['cardlog_similarity'];//相似度
                            $adddata['cardlog_faceimg'] = $someVar['cardlog_faceimg'];
                            $adddata['cardlog_state'] = 1;
                            $adddata['cardlog_clocktime'] = $someVar['cardlog_clocktime'];
                            $adddata['cardlog_creattime'] = time();
                            $stucardlogid = $this->DataControl->insertData('gmc_machine_stucardlog', $adddata);
                            if($stucardlogid>1){
                                $succ = $succ?$succ.','.$someVar['attendance_id']:$someVar['attendance_id'];
                                continue;
                            }else{
                                $fail = $fail ? $fail . ',' . $someVar['attendance_id'] : $someVar['attendance_id'];
                                continue;
                            }
                        }
                    }
                }

                $result = array();
                $succArray = $succ?explode(',',$succ):array();
                $failArray = $fail?explode(',',$fail):array();

                $result['succArray'] = $succArray;
                $result['failArray'] = $failArray;
                if($succ){
                    ajax_return(array('error' => '0', 'errortip' => "出勤记录登记成功", 'result' => $result));
                } else {
                    ajax_return(array('error' => '1', 'errortip' => "出勤记录登记失败", 'result' => $result));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }
    //获取学生最新人像
    function getSchoolStuOneApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $request['isOneHour'] = '1';
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'getSchoolStuOneApi', $request)) {
                if($request['school_id'] == '') {
                    ajax_return(array('error' => '1', 'errortip' => '学校ID不能为空', 'result' => array()));
                }
                if($request['student_id'] == '') {
                    ajax_return(array('error' => '1', 'errortip' => '学生ID不能为空', 'result' => array()));
                }

                $schoooOne = $this->DataControl->selectOne("select school_id,company_id from smc_school where school_id = '{$request['school_id']}' limit 0,1");
                if(!$schoooOne) {
                    ajax_return(array('error' => '1', 'errortip' => '学校信息未找到！', 'result' => array()));
                }

                $studentOne = $this->DataControl->selectOne("select s.student_branch,ifnull(g.stuportrait_faceimg,'') as stuportrait_faceimg,g.stuportrait_creattime  
                from gmc_machine_stuportrait as g,smc_student as s 
                where g.company_id = '{$schoooOne['company_id']}' and g.student_id = '{$request['student_id']}' and g.student_id = s.student_id 
                order by g.stuportrait_creattime desc,g.stuportrait_id desc  
                limit 0,1");

                $field = [
                    "student_branch"=>"学生编号",
                    "stuportrait_faceimg"=>"人像链接",
                    "stuportrait_creattime"=>"人像录入时间"
                ];

                $result = array();
                $result["field"] = $field;
                $result['list'] =$studentOne?$studentOne:array();

                if ($studentOne) {
                    ajax_return(array('error' => '0', 'errortip' => "学生人像获取成功", 'result' => $result));
                } else {

                    ajax_return(array('error' => '1', 'errortip' => "学生人像获取失败", 'result' => (object)array()));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => (object)array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => (object)array()));
        }
    }
    //通过学校编号获取教师列表信息 -- app
    function getSchoolStaffListApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $request['isOneHour'] = '1';
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'getSchoolStaffListApi', $request)) {

                if($request['school_id'] == '') {
                    ajax_return(array('error' => '1', 'errortip' => '学校ID不能为空', 'result' => array()));
                }

                $SchoolOne = $this->DataControl->selectOne("select company_id,school_id,school_branch,district_id from smc_school where school_id = '{$request['school_id']}' limit 0,1 ");

                $schoolList = $this->DataControl->selectClear("select a.school_id from smc_school as a where a.district_id = '{$SchoolOne['district_id']}' and a.school_isclose = '0' and a.school_istest = '0' ");
                if($schoolList){
                    $schoolarray = array_column($schoolList,'school_id');
                    $schoolstr = implode(',',$schoolarray);
                }else{
                    $schoolstr = $request['school_id'];
                }

//                if (isset($request['p']) && $request['p'] !== '') {
//                    $page = $request['p'];
//                } else {
//                    $page = '1';
//                }
//                if (isset($request['num']) && $request['num'] !== '') {
//                    $num = $request['num'];
//                } else {
//                    $num = '10';
//                }
//                $pagestart = ($page - 1) * $num;


                $datawhere = " (
                (p.school_id in ($schoolstr) and p.school_id > 1 and s.staffer_leave ='0') 
                or (p.company_id = '{$SchoolOne['company_id']}' and p.school_id = '0' and s.staffer_leave ='0')
                )  ";
//                $datawhere = " (
//                (p.school_id = '{$request['school_id']}' and p.school_id > 1 and s.staffer_leave ='0')
//                or (p.company_id = '{$SchoolOne['company_id']}' and p.school_id = '0' and s.staffer_leave ='0')
//                )  ";
                if(isset($request['enrolled_updatatime']) && $request['enrolled_updatatime'] != ''){
                    $datawhere .= " and  ( s.staffer_updatetime > '{$request['enrolled_updatatime']}' or s.staffer_createtime > '{$request['enrolled_updatatime']}' )";
                }

                $sql = "SELECT s.staffer_id,s.staffer_cnname,s.staffer_enname,s.staffer_sex,s.staffer_branch,s.staffer_img,s.staffer_createtime,s.staffer_updatetime,s.staffer_leave,s.staffer_isparttime,
                        (select g.stuportrait_faceimg from gmc_machine_stuportrait as g where g.main_staffer_id = s.staffer_id  order by g.stuportrait_updatetime desc limit 0,1) as stuportrait_faceimg ,
                        (select g.stuportrait_updatetime from gmc_machine_stuportrait as g where g.main_staffer_id = s.staffer_id  order by g.stuportrait_updatetime desc limit 0,1) as stuportrait_updatetime 
                    FROM smc_staffer AS s
                    LEFT JOIN gmc_staffer_postbe AS p ON s.staffer_id = p.staffer_id 
                    left join smc_school as sc on sc.school_id = p.school_id
                    left join gmc_company as c on c.company_id = sc.company_id
                    WHERE {$datawhere}
                    GROUP BY s.staffer_id
                    ORDER BY s.staffer_id DESC ";
//                limit {$pagestart},{$num}
                $studentlist = $this->DataControl->selectClear($sql);

                if($studentlist){
                    foreach ($studentlist as &$studentvar){
                        $studentvar['stuportrait_faceimg'] = is_null($studentvar['stuportrait_faceimg'])?'':$studentvar['stuportrait_faceimg'];
                        $studentvar['ishaveportrait'] = $studentvar['stuportrait_faceimg']?1:0;
                        if($studentvar['staffer_sex'] != '男' && $studentvar['staffer_sex'] != '女'){
                            $studentvar['staffer_sex'] = '';
                        }
                    }
                }

                $field = [
                    "staffer_id"=>"职工ID",
                    "staffer_cnname"=>"职工中文名",
                    "staffer_enname"=>"职工英文名",
                    "staffer_branch"=>"职工编号",
                    "staffer_sex"=>"职工性别 男、女、空值",
                    "staffer_img"=>"职工头像",
                    "staffer_createtime"=>"职工创建时间",
                    "staffer_updatetime"=>"职工更新时间",
                    "staffer_leave"=>"职工入校状态 0待入班 1已入班 -1已离校 2已毕业 3保留学籍",
                    "staffer_isparttime"=>"是否兼职老师：0全职1兼职",
                    "ishaveportrait"=>"是否有人像 1有 0没有",
                    "stuportrait_faceimg"=>"最后一次采集的人像",
                    "stuportrait_updatetime"=>"照片更新时间",
                ];

                $result = array();
                $result["field"] = $field;
                $result['list'] =$studentlist?$studentlist:array();
                if ($studentlist) {
                    ajax_return(array('error' => '0', 'errortip' => "职工信息获取成功", 'result' => $result));
                } else {
                    ajax_return(array('error' => '1', 'errortip' => "职工信息获取失败", 'result' => array()));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }
    //单条 -- 教师的 -- 出勤记录回传接口
    function addStaffClockOneApi()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $request['isOneHour'] = '1';
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'addStaffClockOneApi', $request)) {

                if($request['company_id'] == '' || $request['company_id'] == '0') {
                    ajax_return(array('error' => '1', 'errortip' => '集团ID不能为空', 'result' => array()));
                }
                if($request['school_id'] == '' || $request['school_id'] == '0') {
                    ajax_return(array('error' => '1', 'errortip' => '学校ID不能为空', 'result' => array()));
                }
                if($request['machine_id'] == '' || $request['machine_id'] == '0') {
                    ajax_return(array('error' => '1', 'errortip' => '设备ID不能为空', 'result' => array()));
                }
                if($request['staffer_id'] == '') {
                    ajax_return(array('error' => '1', 'errortip' => '职工ID不能为空', 'result' => array()));
                }
                if($request['cardlog_faceimg'] == '') {
                    ajax_return(array('error' => '1', 'errortip' => '职工人像图片不能为空', 'result' => array()));
                }

                $dataOne = $this->DataControl->selectOne(" select * from gmc_machine_staffcardlog where staffer_id = '{$request['staffer_id']}' and cardlog_clocktime = '{$request['cardlog_clocktime']}'  ");
                if($dataOne){
                    ajax_return(array('error' => '1', 'errortip' => "本条记录已经同步", 'result' => array()));
                }

                $adddata = array();
                $adddata['company_id'] = $request['company_id'];
                $adddata['school_id'] = $request['school_id'];
                $adddata['machine_id'] = $request['machine_id'];
                $adddata['staffer_id'] = $request['staffer_id'];
                $adddata['cardlog_type'] = 1;
                $adddata['cardlog_temperature'] = $request['cardlog_temperature'];
                $adddata['cardlog_similarity'] = ($request['cardlog_similarity']==null)?'':$request['cardlog_similarity'];//相似度
                $adddata['cardlog_faceimg'] = $request['cardlog_faceimg'];
                $adddata['cardlog_state'] = 1;
                $adddata['cardlog_clocktime'] = $request['cardlog_clocktime'];
                $adddata['cardlog_creattime'] = time();
                $stucardlogid = $this->DataControl->insertData('gmc_machine_staffcardlog', $adddata);
                if($stucardlogid){
                    ajax_return(array('error' => '0', 'errortip' => "出勤记录登记成功", 'result' => array()));
                } else {
                    ajax_return(array('error' => '1', 'errortip' => "出勤记录登记失败", 'result' => array()));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }
    //多条 -- 教师的 -- 出勤记录回传接口
    function addStaffClockSomeApi()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $request['isOneHour'] = '1';
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'addStaffClockSomeApi', $request)) {

                $succ = '';
                $fail = '';
                if (isset($request['somestaffstr']) && $request['somestaffstr'] !== '' && $request['somestaffstr'] != '[]') {
                    $someArray = json_decode(stripslashes($request['somestaffstr']), 1);
                    if (is_array($someArray) && count($someArray) > 0) {
                        //新增判断
                        foreach ($someArray as $someVar) {
                            if($someVar['company_id'] == '' || $someVar['company_id'] == '0') {
                                $fail = $fail?$fail.','.$someVar['attendance_id']:$someVar['attendance_id'];
                                continue;
                            }
                            if($someVar['school_id'] == '' || $someVar['school_id'] == '0') {
                                $fail = $fail?$fail.','.$someVar['attendance_id']:$someVar['attendance_id'];
                                continue;
                            }
                            if($someVar['machine_id'] == '' || $someVar['machine_id'] == '0') {
                                $fail = $fail?$fail.','.$someVar['attendance_id']:$someVar['attendance_id'];
                                continue;
                            }
                            if($someVar['staffer_id'] == '') {
                                $fail = $fail?$fail.','.$someVar['attendance_id']:$someVar['attendance_id'];
                                continue;
                            }
                            if($someVar['cardlog_faceimg'] == '') {
                                $fail = $fail?$fail.','.$someVar['attendance_id']:$someVar['attendance_id'];
                                continue;
                            }

                            $dataOne = $this->DataControl->selectOne(" select * from gmc_machine_staffcardlog where staffer_id = '{$someVar['staffer_id']}' and cardlog_clocktime = '{$someVar['cardlog_clocktime']}'  ");
                            if($dataOne){
                                $succ = $succ ? $succ.','.$someVar['attendance_id'] : $someVar['attendance_id'];
                                continue;
//                                $fail = $fail?$fail.','.$someVar['attendance_id']:$someVar['attendance_id'];
//                                continue;
                            }

                            $adddata = array();
                            $adddata['company_id'] = $someVar['company_id'];
                            $adddata['school_id'] = $someVar['school_id'];
                            $adddata['machine_id'] = $someVar['machine_id'];
                            $adddata['staffer_id'] = $someVar['staffer_id'];
                            $adddata['cardlog_type'] = 1;
                            $adddata['cardlog_temperature'] = $someVar['cardlog_temperature'];
                            $adddata['cardlog_similarity'] = ($someVar['cardlog_similarity']==null)?'':$someVar['cardlog_similarity'];//相似度
                            $adddata['cardlog_faceimg'] = $someVar['cardlog_faceimg'];
                            $adddata['cardlog_state'] = 1;
                            $adddata['cardlog_clocktime'] = $someVar['cardlog_clocktime'];
                            $adddata['cardlog_creattime'] = time();
                            $stucardlogid = $this->DataControl->insertData('gmc_machine_staffcardlog', $adddata);
                            if($stucardlogid>1){
                                $succ = $succ?$succ.','.$someVar['attendance_id']:$someVar['attendance_id'];
                                continue;
                            }else{
                                $fail = $fail ? $fail . ',' . $someVar['attendance_id'] : $someVar['attendance_id'];
                                continue;
                            }
                        }
                    }
                }

                $result = array();
                $succArray = $succ?explode(',',$succ):array();
                $failArray = $fail?explode(',',$fail):array();

                $result['succArray'] = $succArray;
                $result['failArray'] = $failArray;
                if($succ){
                    ajax_return(array('error' => '0', 'errortip' => "出勤记录登记成功", 'result' => $result));
                } else {
                    ajax_return(array('error' => '1', 'errortip' => "出勤记录登记失败", 'result' => $result));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }
    //获取教师最新人像
    function getSchoolStaffOneApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $request['isOneHour'] = '1';
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'getSchoolStaffOneApi', $request)) {
                if($request['school_id'] == '') {
                    ajax_return(array('error' => '1', 'errortip' => '学校ID不能为空', 'result' => array()));
                }
                if($request['staffer_id'] == '') {
                    ajax_return(array('error' => '1', 'errortip' => '职工ID不能为空', 'result' => array()));
                }
                $schoooOne = $this->DataControl->selectOne("select school_id,company_id from smc_school where school_id = '{$request['school_id']}' limit 0,1");
                if(!$schoooOne) {
                    ajax_return(array('error' => '1', 'errortip' => '学校信息未找到！', 'result' => array()));
                }

                $studentOne = $this->DataControl->selectOne("select s.staffer_branch,ifnull(g.stuportrait_faceimg,'') as stuportrait_faceimg,g.stuportrait_creattime  
                from gmc_machine_stuportrait as g,smc_staffer as s 
                where  g.main_staffer_id = '{$request['staffer_id']}' and g.main_staffer_id = s.staffer_id and g.company_id = '{$schoooOne['company_id']}'
                order by g.stuportrait_creattime desc,g.stuportrait_id desc  
                limit 0,1");//g.school_id = '{$request['school_id']}' and

                $field = [
                    "student_branch"=>"职工编号",
                    "stuportrait_faceimg"=>"人像链接",
                    "stuportrait_creattime"=>"人像录入时间"
                ];

                $result = array();
                $result["field"] = $field;
                $result['list'] =$studentOne?$studentOne:array();

                if ($studentOne) {
                    ajax_return(array('error' => '0', 'errortip' => "职工人像获取成功", 'result' => $result));
                } else {

                    ajax_return(array('error' => '1', 'errortip' => "职工人像获取失败", 'result' => (object)array()));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => (object)array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => (object)array()));
        }
    }
    //获取集团职务职工信息 -- app  --- 废弃，菀直不用了
    function getGmcStaffListApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $request['isOneHour'] = '1';
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'getSchoolStaffListApi', $request)) {

                if($request['company_id'] == '') {
                    ajax_return(array('error' => '1', 'errortip' => '集团ID不能为空', 'result' => array()));
                }

                $datawhere = " p.company_id = '{$request['company_id']}' and p.school_id = '0' and s.staffer_leave ='0'  ";
                if(isset($request['enrolled_updatatime']) && $request['enrolled_updatatime'] != ''){
                    $datawhere .= " and  ( s.staffer_updatetime > '{$request['enrolled_updatatime']}' or s.staffer_createtime > '{$request['enrolled_updatatime']}' )";
                }

                $sql = "SELECT s.staffer_id,s.staffer_cnname,s.staffer_enname,s.staffer_sex,s.staffer_branch,s.staffer_img,s.staffer_createtime,s.staffer_updatetime,s.staffer_leave,
                        (select g.stuportrait_faceimg from gmc_machine_stuportrait as g where g.main_staffer_id = s.staffer_id  order by g.stuportrait_updatetime desc limit 0,1) as stuportrait_faceimg ,
                        (select g.stuportrait_updatetime from gmc_machine_stuportrait as g where g.main_staffer_id = s.staffer_id  order by g.stuportrait_updatetime desc limit 0,1) as stuportrait_updatetime 
                    FROM smc_staffer AS s
                    LEFT JOIN gmc_staffer_postbe AS p ON s.staffer_id = p.staffer_id 
                    left join smc_school as sc on sc.school_id = p.school_id
                    left join gmc_company as c on c.company_id = sc.company_id
                    WHERE {$datawhere}
                    GROUP BY s.staffer_id
                    ORDER BY s.staffer_id DESC ";
//                limit {$pagestart},{$num}
                $studentlist = $this->DataControl->selectClear($sql);

                if($studentlist){
                    foreach ($studentlist as &$studentvar){
                        $studentvar['stuportrait_faceimg'] = is_null($studentvar['stuportrait_faceimg'])?'':$studentvar['stuportrait_faceimg'];
                        $studentvar['ishaveportrait'] = $studentvar['stuportrait_faceimg']?1:0;
                        if($studentvar['staffer_sex'] != '男' && $studentvar['staffer_sex'] != '女'){
                            $studentvar['staffer_sex'] = '';
                        }
                    }
                }

                $field = [
                    "staffer_id"=>"职工ID",
                    "staffer_cnname"=>"职工中文名",
                    "staffer_enname"=>"职工英文名",
                    "staffer_branch"=>"职工编号",
                    "staffer_sex"=>"职工性别 男、女、空值",
                    "staffer_img"=>"职工头像",
                    "staffer_createtime"=>"职工创建时间",
                    "staffer_updatetime"=>"职工更新时间",
                    "staffer_leave"=>"职工入校状态 0待入班 1已入班 -1已离校 2已毕业 3保留学籍",
                    "ishaveportrait"=>"是否有人像 1有 0没有",
                    "stuportrait_faceimg"=>"最后一次采集的人像",
                    "stuportrait_updatetime"=>"照片更新时间",
                ];

                $result = array();
                $result["field"] = $field;
                $result['list'] =$studentlist?$studentlist:array();
                if ($studentlist) {
                    ajax_return(array('error' => '0', 'errortip' => "职工信息获取成功", 'result' => $result));
                } else {
                    ajax_return(array('error' => '1', 'errortip' => "职工信息获取失败", 'result' => array()));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }
    //判断人像图片质量 --- 测试专用
    public function judgeImgQualityTest($urlimg=''){
        if(!$urlimg){
            $this->errortip = '图片链接不能为空！';
            $this->error = 1;
            return false;
        }
        $api_key = "24888351c5b5";
        $api_Secret = "d272030551d11caa";

        $arrContextOptions=array(
            "ssl"=>array(
                "verify_peer"=>false,
                "verify_peer_name"=>false,
                "allow_self_signed"=>true,
            ),
        );
        $imageData = file_get_contents($urlimg, false, stream_context_create($arrContextOptions));
//        echo strlen($imageData);die;//14 669 898 //13.9M
        if(strlen($imageData)>'5242880'){
            $urlimg = $urlimg."?x-oss-process=image/resize,m_lfit,w_2000,limit_0/auto-orient,1/quality,q_99";
            $imageData = file_get_contents($urlimg, false, stream_context_create($arrContextOptions));
        }
//        $imageData = file_get_contents($urlimg); //正式报错 上边跳过验证
        $base64Data = base64_encode($imageData);

        $data = array();
        $data['api_key'] = $api_key;
        $data['timestamp'] = time();
        $data['sign'] = md5(time().'#'.$api_Secret);

        $data['image_type'] = 'BASE64';
        //$data['file'] = $urlimg;
        $data['image_base64'] = 'data:image/jpeg;base64,'.$base64Data;
//        echo '<img src="data:image/jpeg;base64,'.$base64Data.'">';
//        echo $data['image_base64'];die;
//        print_r($data);die;

        $getBackurl = request_by_curl("http://***********:8765/api/v1/faces/quality", dataEncode($data), "POST");
        print_r($getBackurl);die;
        $bakData = json_decode($getBackurl, true);

        if($bakData['code'] == '200'){
            if($bakData['data']['qa_code'] == '200'){
                $this->errortip = '图像合格';
                $this->error = 0;
                return true;
            }else{
                if( $bakData['data']['qa_message'] == 'Face Covered'){
                    $this->errortip = '面部遮盖';
                }else{
                    $this->errortip = $bakData['data']['qa_message'];
                }
                $this->error = 1;
                return false;
            }
        }else{
            $this->errortip = '图片未解析成功！';
            $this->error = 1;
            return false;
        }
    }
    //测试火枪手开发平台 图片质量是否合格
    function csImgView(){
        $request = Input('get.', '', 'trim,addslashes');
//        $urlimg = 'https://pic.kedingdang.com/schoolmanage/202306021837x206386608.jpg';
//        $urlimg = 'https://pic.kedingdang.com/schoolmanage/202406021928x305353225.jpg';//13.9M
//        $urlimg = 'https://pic.kedingdang.com/schoolmanage/202306061453x812651813.jpg';
//        $urlimg = 'https://pic.kedingdang.com/schoolmanage/202407010848x107294100.jpg';
        $urlimg = 'https://pic.kedingdang.com/schoolmanage/202411031347x988156406.jpg';
        $pucArray = $this->judgeImgQualityTest($urlimg);
        if(!$pucArray) {
            ajax_return(array('error' => $this->error, 'errortip' => $this->errortip, 'result' => array()));
        }else{
            ajax_return(array('error' => $this->error, 'errortip' => $this->errortip, 'result' => array()));
        }
    }
    //判断人像图片质量
    public function judgeImgQuality($urlimg=''){
        if(!$urlimg){
            $this->errortip = '图片链接不能为空！';
            $this->error = 1;
            return false;
        }
        $api_key = "24888351c5b5";
        $api_Secret = "d272030551d11caa";

        $arrContextOptions=array(
            "ssl"=>array(
                "verify_peer"=>false,
                "verify_peer_name"=>false,
                "allow_self_signed"=>true,
            ),
        );
        $imageData = file_get_contents($urlimg, false, stream_context_create($arrContextOptions));
        if(strlen($imageData)>'5242880'){
            $urlimg = $urlimg."?x-oss-process=image/resize,m_lfit,w_2000,limit_0/auto-orient,1/quality,q_99";
            $imageData = file_get_contents($urlimg, false, stream_context_create($arrContextOptions));
        }
//        $imageData = file_get_contents($urlimg); //正式报错 上边跳过验证
        $base64Data = base64_encode($imageData);

        $data = array();
        $data['api_key'] = $api_key;
        $data['timestamp'] = time();
        $data['sign'] = md5(time().'#'.$api_Secret);

        $data['image_type'] = 'BASE64';
        //$data['file'] = $urlimg;
        $data['image_base64'] = 'data:image/jpeg;base64,'.$base64Data;
//        echo '<img src="data:image/jpeg;base64,'.$base64Data.'">';
//        echo $data['image_base64'];die;
//        print_r($data);die;
        $getBackurl = request_by_curl("http://***********:8765/api/v1/faces/quality", dataEncode($data), "POST");
        $bakData = json_decode($getBackurl, true);

        if($bakData['code'] == '200'){
            if($bakData['data']['qa_code'] == '200'){
                $this->errortip = '图像合格';
                $this->error = 0;
                return true;
            }else{
                if( $bakData['data']['qa_message'] == 'Face Covered'){
                    $this->errortip = '面部遮盖';
                }else{
                    $this->errortip = $bakData['data']['qa_message'];
                }
                $this->error = 1;
                return false;
            }
        }else{
            $this->errortip = '图片未解析成功！';
            $this->error = 1;
            return false;
        }
    }
    //微信 H5 页面 获取学生 和 班级学生信息

    //获取校园 某 学生信息
    function getSchoolStuOneView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'getSchoolStuOne', $request)) {
                if($request['company_id'] == '') {
                    ajax_return(array('error' => '1', 'errortip' => '集团ID不能为空', 'result' => array()));
                }
                if($request['school_id'] == '') {
                    ajax_return(array('error' => '1', 'errortip' => '学校ID不能为空', 'result' => array()));
                }
                if($request['student_branch'] == '') {
                    ajax_return(array('error' => '1', 'errortip' => '学生编号不能为空', 'result' => array()));
                }
                if($request['staffer_id'] == '') {
                    ajax_return(array('error' => '1', 'errortip' => '职工ID不能为空', 'result' => array()));
                }

                $studentOne = $this->DataControl->selectOne("select s.student_cnname,s.student_enname,s.student_branch,s.student_img,
                (select g.stuportrait_faceimg from gmc_machine_stuportrait as g where g.student_id = s.student_id order by g.stuportrait_creattime desc limit 0,1) as stuportrait_faceimg 
                from smc_student as s 
                where s.student_branch = '{$request['student_branch']}' 
                and if((select 1 from smc_student_enrolled as e where e.student_id = s.student_id and e.school_id = '{$request['school_id']}' limit 0,1),1,0) = 1 
                limit 0,1");

                $studentOne['stuportrait_faceimg'] = is_null($studentOne['stuportrait_faceimg'])?'':$studentOne['stuportrait_faceimg'];
                $studentOne['ishaveportrait'] = $studentOne['stuportrait_faceimg']?1:0;

                $field = [
                    "student_cnname"=>"学生姓名",
                    "student_branch"=>"学生编号",
                    "student_img"=>"学生头像",
                    "stuportrait_faceimg"=>"采集的人像",
                    "ishaveportrait"=>"是否已录入过 1有 0没有",
                ];

                $result = array();
                $result["field"] = $field;
                $result['list'] =$studentOne?$studentOne:array();

                if ($studentOne) {
                    ajax_return(array('error' => '0', 'errortip' => "学生信息获取成功", 'result' => $result));
                } else {
                    ajax_return(array('error' => '1', 'errortip' => "学生信息获取失败", 'result' => array()));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }
    //获取校园班级学生信息
    function getClassStuListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'getClassStuList', $request)) {
                if($request['company_id'] == '') {
                    ajax_return(array('error' => '1', 'errortip' => '集团ID不能为空', 'result' => array()));
                }
                if($request['school_id'] == '') {
                    ajax_return(array('error' => '1', 'errortip' => '学校ID不能为空', 'result' => array()));
                }
                if($request['class_branch'] == '') {
                    ajax_return(array('error' => '1', 'errortip' => '班级编号不能为空', 'result' => array()));
                }
                if($request['staffer_id'] == '') {
                    ajax_return(array('error' => '1', 'errortip' => '职工ID不能为空', 'result' => array()));
                }

                //班级信息
                $classOne = $this->DataControl->selectOne("select c.class_id,c.class_cnname,c.class_branch from smc_class as c where c.class_branch='{$request['class_branch']}' and c.company_id = '{$request['company_id']}' and c.school_id = '{$request['school_id']}' limit 0,1");

//                if (isset($request['p']) && $request['p'] !== '') {
//                    $page = $request['p'];
//                } else {
//                    $page = '1';
//                }
//                if (isset($request['num']) && $request['num'] !== '') {
//                    $num = $request['num'];
//                } else {
//                    $num = '10';
//                }
//                $pagestart = ($page - 1) * $num;

                $datawhere = " s.school_id='{$request['school_id']}' and s.study_isreading=1 and s.class_id='{$classOne['class_id']}' ";
                if (isset($request['ishaveportrait']) && $request['ishaveportrait'] == '1') {
                    $having = ' having stuportrait_faceimg is not null ';
                }else{
                    $having = ' having stuportrait_faceimg is null ';
                }
                //过滤已经其他子班的学员
//                if (isset($request['is_in_childclass']) && $request['is_in_childclass'] == '1') {}
                $datawhere .= " and t.student_id not in ( select std.student_id from smc_student_study as std where std.student_id = s.student_id and std.class_id in (select scs.class_id from smc_class as scs where scs.father_id = s.class_id ))";

                $sql = " select t.student_cnname,t.student_enname,t.student_branch,t.student_img,t.student_sex,
                (select g.stuportrait_faceimg from gmc_machine_stuportrait as g where g.student_id = s.student_id order by g.stuportrait_creattime desc limit 0,1) as stuportrait_faceimg 
                from smc_student_study as s 
                left join smc_student as t ON s.student_id = t.student_id  
                where {$datawhere} 
                {$having}
                order by t.student_id ASC,s.study_beginday DESC
                ";
                $studentList = $this->DataControl->selectClear($sql);

                $field = [
                    "class_cnname"=>"班级名称",
                    "class_branch"=>"班级编号",

                    "student_cnname"=>"学生姓名",
                    "student_branch"=>"学生编号",
                    "student_img"=>"学生头像",
                    "student_sex"=>"学生性别  男、女、空值",
                    "stuportrait_faceimg"=>"采集的人像",
                    "ishaveportrait"=>"是否已录入过 1有 0没有",
                ];

                $result = array();
                $result["field"] = $field;
                $result["classone"] = $classOne;
                $result['list'] =$studentList?$studentList:array();

                if (!$studentList) {
                    ajax_return(array('error' => '1', 'errortip' => '学生信息获取失败', 'result' => $result));
                }
                foreach ($studentList as &$studentVar){
                    $studentVar['stuportrait_faceimg'] = is_null($studentVar['stuportrait_faceimg'])?'':$studentVar['stuportrait_faceimg'];
                    $studentVar['ishaveportrait'] = $studentVar['stuportrait_faceimg']?1:0;
                    $studentVar['student_cnname'] = $studentVar['student_enname']?$studentVar['student_cnname'].'/'.$studentVar['student_enname']:$studentVar['student_cnname'];
                    if($studentVar['student_sex'] != '男' && $studentVar['student_sex'] != '女'){
                        $studentVar['student_sex'] = '';
                    }
                }

                if ($studentList) {
                    ajax_return(array('error' => '0', 'errortip' => "学生信息获取成功", 'result' => $result));
                } else {
                    ajax_return(array('error' => '1', 'errortip' => "学生信息获取失败", 'result' => array()));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }
    //添加学生人脸采集头像
    function addStuPortraitView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'addStuPortrait', $request)) {
                if($request['company_id'] == '') {
                    ajax_return(array('error' => '1', 'errortip' => '集团ID不能为空', 'result' => array()));
                }
                if($request['school_id'] == '') {
                    ajax_return(array('error' => '1', 'errortip' => '学校ID不能为空', 'result' => array()));
                }
                if($request['student_branch'] == '') {
                    ajax_return(array('error' => '1', 'errortip' => '学生编号不能为空', 'result' => array()));
                }
                if($request['staffer_id'] == '') {
                    ajax_return(array('error' => '1', 'errortip' => '职工ID不能为空', 'result' => array()));
                }
                if($request['stuportrait_faceimg'] == '') {
                    ajax_return(array('error' => '1', 'errortip' => '人像图片不能为空', 'result' => array()));
                }
                //判断学生、教师 这里的数据是否完整
                $studentOne = $this->DataControl->selectOne("select s.student_id,s.student_cnname,s.student_enname,s.student_branch 
                from smc_student as s 
                where s.student_branch = '{$request['student_branch']}' 
                and if((select 1 from smc_student_enrolled as e where e.student_id = s.student_id and e.school_id = '{$request['school_id']}' limit 0,1),1,0) = 1 
                limit 0,1");

                if(!$studentOne) {
                    ajax_return(array('error' => '1', 'errortip' => '学生不在本校', 'result' => array()));
                }

                $urlimg = $request['stuportrait_faceimg'];
                $pucArray = $this->judgeImgQuality($urlimg);
                if(!$pucArray){
                    ajax_return(array('error' => $this->error, 'errortip' => $this->errortip, 'result' => array()));
                }

                $adddata = array();
                $adddata['company_id'] = $request['company_id'];
                $adddata['school_id'] = $request['school_id'];
                $adddata['staffer_id'] = $request['staffer_id'];
                $adddata['student_id'] = $studentOne['student_id'];
                $adddata['stuportrait_faceimg'] = $request['stuportrait_faceimg'];
                $adddata['stuportrait_isquality'] = 1;
                $adddata['stuportrait_isqualityname'] = '学校上传';
                $adddata['stuportrait_creattime'] = time();
                $adddata['stuportrait_updatetime'] = time();
                $stuportraitid = $this->DataControl->insertData('gmc_machine_stuportrait', $adddata);
                if($stuportraitid){
                    //更新学生的头像
                    $updatestu = array();
                    $updatestu['student_img'] = $adddata['stuportrait_faceimg'];
                    $updatestu['student_updatatime'] = time();
                    $this->DataControl->updateData("smc_student", "student_id='{$studentOne['student_id']}' and company_id = '{$request['company_id']}' ", $updatestu);

                    ajax_return(array('error' => '0', 'errortip' => "人像采集成功", 'result' => array()));
                } else {
                    ajax_return(array('error' => '1', 'errortip' => "人像采集失败", 'result' => array()));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }
    //添加教师人脸采集头像
    function addStafferPortraitView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'addStafferPortrait', $request)) {
                if($request['company_id'] == '') {
                    ajax_return(array('error' => '1', 'errortip' => '集团ID不能为空', 'result' => array()));
                }
                if($request['school_id'] == '') {
                    ajax_return(array('error' => '1', 'errortip' => '学校ID不能为空', 'result' => array()));
                }
                if($request['main_staffer_branch'] == '') {
                    ajax_return(array('error' => '1', 'errortip' => '职工编号不能为空', 'result' => array()));
                }
                if($request['staffer_id'] == '') {
                    ajax_return(array('error' => '1', 'errortip' => '操作职工ID不能为空', 'result' => array()));
                }
                if($request['stuportrait_faceimg'] == '') {
                    ajax_return(array('error' => '1', 'errortip' => '人像图片不能为空', 'result' => array()));
                }
                //判断学生、教师 这里的数据是否完整
                $stafferOne = $this->DataControl->selectOne("select s.staffer_id,s.staffer_cnname,s.staffer_enname,s.staffer_branch 
                from smc_staffer as s 
                where s.staffer_branch = '{$request['main_staffer_branch']}' and s.company_id = '{$request['company_id']}'
                limit 0,1");

                if(!$stafferOne) {
                    ajax_return(array('error' => '1', 'errortip' => '职工信息不存在！', 'result' => array()));
                }

                $adddata = array();
                $adddata['company_id'] = $request['company_id'];
                $adddata['school_id'] = $request['school_id'];
                $adddata['staffer_id'] = $request['staffer_id'];//操作人
                $adddata['main_staffer_id'] = $stafferOne['staffer_id'];// 录入的职工
                $adddata['stuportrait_faceimg'] = $request['stuportrait_faceimg'];
                $adddata['stuportrait_creattime'] = time();
                $adddata['stuportrait_updatetime'] = time();
                $stuportraitid = $this->DataControl->insertData('gmc_machine_stuportrait', $adddata);
                if($stuportraitid){
                    //更新学生的头像
                    $updatestu = array();
                    $updatestu['staffer_img'] = $adddata['stuportrait_faceimg'];
                    $updatestu['staffer_updatetime'] = time();
                    $this->DataControl->updateData("smc_staffer", "staffer_id='{$stafferOne['staffer_id']}' and company_id = '{$request['company_id']}' ", $updatestu);

                    ajax_return(array('error' => '0', 'errortip' => "人像采集成功", 'result' => array()));
                } else {
                    ajax_return(array('error' => '1', 'errortip' => "人像采集失败", 'result' => array()));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }
    //获取校园 某 教师信息
    function getSchoolStaffOneView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'getSchoolStaffOne', $request)) {
                if($request['company_id'] == '') {
                    ajax_return(array('error' => '1', 'errortip' => '集团ID不能为空', 'result' => array()));
                }
                if($request['school_id'] == '') {
                    ajax_return(array('error' => '1', 'errortip' => '学校ID不能为空', 'result' => array()));
                }
                if($request['main_staffer_branch'] == '') {
                    ajax_return(array('error' => '1', 'errortip' => '职工编号不能为空', 'result' => array()));
                }
                if($request['staffer_id'] == '') {
                    ajax_return(array('error' => '1', 'errortip' => '操作职工ID不能为空', 'result' => array()));
                }

                $stafferOne = $this->DataControl->selectOne("select s.staffer_cnname,s.staffer_enname,s.staffer_branch,s.staffer_img,
                (select g.stuportrait_faceimg from gmc_machine_stuportrait as g where g.main_staffer_id = s.staffer_id order by g.stuportrait_creattime desc limit 0,1) as stuportrait_faceimg 
                from smc_staffer as s 
                where s.staffer_branch = '{$request['main_staffer_branch']}' and s.company_id = '{$request['company_id']}' ");

                $stafferOne['stuportrait_faceimg'] = is_null($stafferOne['stuportrait_faceimg'])?'':$stafferOne['stuportrait_faceimg'];
                $stafferOne['ishaveportrait'] = $stafferOne['stuportrait_faceimg']?1:0;

                $field = [
                    "staffer_cnname"=>"教师姓名",
                    "staffer_branch"=>"教师编号",
                    "staffer_img"=>"教师头像",
                    "stuportrait_faceimg"=>"采集的人像",
                    "ishaveportrait"=>"是否已录入过 1有 0没有",
                ];

                $result = array();
                $result["field"] = $field;
                $result['list'] =$stafferOne?$stafferOne:array();

                if ($stafferOne) {
                    ajax_return(array('error' => '0', 'errortip' => "教师信息获取成功", 'result' => $result));
                } else {
                    ajax_return(array('error' => '1', 'errortip' => "教师信息获取失败", 'result' => array()));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }
    //获取 某 职工信息 -- 集团
    function getGmcStaffOneView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'getSchoolStaffOne', $request)) {
                if($request['company_id'] == '') {
                    ajax_return(array('error' => '1', 'errortip' => '集团ID不能为空', 'result' => array()));
                }
//                if($request['school_id'] == '') {
//                    ajax_return(array('error' => '1', 'errortip' => '学校ID不能为空', 'result' => array()));
//                }
                if($request['main_staffer_branch'] == '') {
                    ajax_return(array('error' => '1', 'errortip' => '职工编号不能为空', 'result' => array()));
                }
//                if($request['staffer_id'] == '') {
//                    ajax_return(array('error' => '1', 'errortip' => '操作职工ID不能为空', 'result' => array()));
//                }

                $stafferOne = $this->DataControl->selectOne("select s.staffer_cnname,s.staffer_enname,s.staffer_branch,s.staffer_img,
                (select g.stuportrait_faceimg from gmc_machine_stuportrait as g where g.main_staffer_id = s.staffer_id order by g.stuportrait_creattime desc limit 0,1) as stuportrait_faceimg 
                from smc_staffer as s 
                where s.staffer_branch = '{$request['main_staffer_branch']}' and s.company_id = '{$request['company_id']}' ");

                $stafferOne['stuportrait_faceimg'] = is_null($stafferOne['stuportrait_faceimg'])?'':$stafferOne['stuportrait_faceimg'];
                $stafferOne['ishaveportrait'] = $stafferOne['stuportrait_faceimg']?1:0;

                $field = [
                    "staffer_cnname"=>"教师姓名",
                    "staffer_branch"=>"教师编号",
                    "staffer_img"=>"教师头像",
                    "stuportrait_faceimg"=>"采集的人像",
                    "ishaveportrait"=>"是否已录入过 1有 0没有",
                ];

                $result = array();
                $result["field"] = $field;
                $result['list'] =$stafferOne?$stafferOne:array();

                if ($stafferOne) {
                    ajax_return(array('error' => '0', 'errortip' => "教师信息获取成功", 'result' => $result));
                } else {
                    ajax_return(array('error' => '1', 'errortip' => "教师信息获取失败", 'result' => array()));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }
    //添加职工人脸采集头像 -- 集团
    function addGmcStafferPortraitView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'addStafferPortrait', $request)) {
                if($request['company_id'] == '') {
                    ajax_return(array('error' => '1', 'errortip' => '集团ID不能为空', 'result' => array()));
                }
//                if($request['school_id'] == '') {
//                    ajax_return(array('error' => '1', 'errortip' => '学校ID不能为空', 'result' => array()));
//                }
                if($request['main_staffer_branch'] == '') {
                    ajax_return(array('error' => '1', 'errortip' => '职工编号不能为空', 'result' => array()));
                }
//                if($request['staffer_id'] == '') {
//                    ajax_return(array('error' => '1', 'errortip' => '操作职工ID不能为空', 'result' => array()));
//                }
                if($request['stuportrait_faceimg'] == '') {
                    ajax_return(array('error' => '1', 'errortip' => '人像图片不能为空', 'result' => array()));
                }
                //判断学生、教师 这里的数据是否完整
                $stafferOne = $this->DataControl->selectOne("select s.staffer_id,s.staffer_cnname,s.staffer_enname,s.staffer_branch,s.staffer_isparttime  
                from smc_staffer as s 
                where s.staffer_branch = '{$request['main_staffer_branch']}' and s.company_id = '{$request['company_id']}'
                limit 0,1");

                if(!$stafferOne) {
                    ajax_return(array('error' => '1', 'errortip' => '职工信息不存在！', 'result' => array()));
                }
                if($stafferOne['staffer_isparttime'] == '1') {
                    ajax_return(array('error' => '1', 'errortip' => '兼职职工不能录入人像！', 'result' => array()));
                }

                $adddata = array();
                $adddata['company_id'] = $request['company_id'];
                $adddata['school_id'] = 0;
                $adddata['staffer_id'] = 0;//操作人
                $adddata['main_staffer_id'] = $stafferOne['staffer_id'];// 录入的职工
                $adddata['stuportrait_faceimg'] = $request['stuportrait_faceimg'];
                $adddata['stuportrait_isgmc'] = 1;
                $adddata['stuportrait_creattime'] = time();
                $adddata['stuportrait_updatetime'] = time();
                $stuportraitid = $this->DataControl->insertData('gmc_machine_stuportrait', $adddata);
                if($stuportraitid){
                    //更新学生的头像
                    $updatestu = array();
                    $updatestu['staffer_img'] = $adddata['stuportrait_faceimg'];
                    $updatestu['staffer_updatetime'] = time();
                    $this->DataControl->updateData("smc_staffer", "staffer_id='{$stafferOne['staffer_id']}' and company_id = '{$request['company_id']}' ", $updatestu);

                    ajax_return(array('error' => '0', 'errortip' => "人像采集成功", 'result' => array()));
                } else {
                    ajax_return(array('error' => '1', 'errortip' => "人像采集失败", 'result' => array()));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //人脸APP -- 考勤机参数配置 -- 针对教务的小书检核 -- 20231010 (apifox教务pad端接口）
    function getXsjhSchoolConfigApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'getSchoolDetailApi', $request)) {

                $schoolOne['heartbeat_time'] = 300;//单位：秒  间隔5分钟
                $schoolOne['getSchoolStuList_time'] = 20;//单位：秒  间隔5分钟
                $schoolOne['getSchoolStuOne_time'] = 2;//单位：秒  间隔5分钟
                $schoolOne['addStuClockOne_time'] = 6;//单位：秒  间隔5分钟

                $field = [
                    "heartbeat_time"=>"设备心跳监测间隔时间 单位：秒",
                    "getSchoolStuList_time"=>"学生列表获取的间隔时间 单位：秒",
                    "getSchoolStuOne_time"=>"学生照片获取的间隔时间 单位：秒",
                    "addStuClockOne_time"=>"上传单条考勤记录的间隔时间 单位：秒",
                ];

                $result = array();
                $result["field"] = $field;
                $result['list'] =$schoolOne?$schoolOne:array();
                if ($schoolOne) {
                    ajax_return(array('error' => '0', 'errortip' => "小书检核考勤机参数获取成功", 'result' => $result));
                } else {
                    ajax_return(array('error' => '1', 'errortip' => "小书检核考勤机参数获取失败", 'result' => array()));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }
    //人脸APP -- 考勤机获取所有学生基本信息 -- 针对教务的小书检核 -- 20231010 (apifox教务pad端接口）
    function getXsjhSchoolStuApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'getXsjhSchoolStuApi', $request)) {

                if($request['staffer_branch'] == ''){
                    ajax_return(array('error' => '1', 'errortip' => '教师编号不能为空', 'result' => array()));
                }
                if(!$request['school_brancharr']){
                    ajax_return(array('error' => '1', 'errortip' => '学校编号不能为空', 'result' => array()));
                }
                $brancharr = json_decode(stripslashes($request['school_brancharr']), true);
                if ($brancharr) {
                    $branchstr = "'".implode("','", $brancharr)."'";
                }else{
                    ajax_return(array('error' => '1', 'errortip' => '学校编号不能为空', 'result' => array()));
                }
                $stafferOne = $this->DataControl->selectOne("select staffer_id,company_id,staffer_branch from smc_staffer where staffer_branch = '{$request['staffer_branch']}' limit 0,1 ");
                if(!$stafferOne){
                    ajax_return(array('error' => '1', 'errortip' => '教师信息不存在校务系统', 'result' => array()));
                }

                $datawhere = " h.school_branch in ({$branchstr}) and h.school_id > 1 and h.company_id = '{$stafferOne['company_id']}' and e.school_id > 1  ";
                if(isset($request['enrolled_updatatime']) && $request['enrolled_updatatime'] != ''){
                    $datawhere .= " and  ( e.enrolled_updatatime > '{$request['enrolled_updatatime']}' or s.student_updatatime > '{$request['enrolled_updatatime']}' or s.student_createtime > '{$request['enrolled_updatatime']}')";
                }

                $sql = " select h.school_branch,s.student_id,s.student_cnname,s.student_enname,s.student_branch,s.student_sex,s.student_img,e.enrolled_status,s.student_createtime,s.student_updatatime,
                if(e.enrolled_createtime>e.enrolled_updatatime,e.enrolled_createtime,e.enrolled_updatatime) as enrolled_updatatime,
                (select g.stuportrait_faceimg from gmc_machine_stuportrait as g where g.student_id = s.student_id  order by g.stuportrait_updatetime desc limit 0,1) as stuportrait_faceimg ,
                (select g.stuportrait_updatetime from gmc_machine_stuportrait as g where g.student_id = s.student_id  order by g.stuportrait_updatetime desc limit 0,1) as stuportrait_updatetime 
                from smc_school as h 
                left join smc_student_enrolled as e ON h.school_id = e.school_id  
                left join smc_student as s ON e.student_id = s.student_id  
                where {$datawhere}";
                $studentlist = $this->DataControl->selectClear($sql);

                if($studentlist){
                    foreach ($studentlist as &$studentvar){
                        $studentvar['stuportrait_faceimg'] = is_null($studentvar['stuportrait_faceimg'])?'':$studentvar['stuportrait_faceimg'];
                        $studentvar['ishaveportrait'] = $studentvar['stuportrait_faceimg']?1:0;
                        if($studentvar['student_sex'] != '男' && $studentvar['student_sex'] != '女'){
                            $studentvar['student_sex'] = '';
                        }
                    }
                }

                $field = [
                    "school_branch"=>"学校编号",
                    "student_id"=>"学生ID",
                    "student_cnname"=>"学生中文名",
                    "student_enname"=>"学生英文名",
                    "student_branch"=>"学生编号",
                    "student_sex"=>"学生性别 男、女、空值",
                    "student_img"=>"学生头像",
                    "student_createtime"=>"学生创建时间",
                    "student_updatatime"=>"学生更新时间",
                    "enrolled_status"=>"学生入校状态 0待入班 1已入班 -1已离校 2已毕业 3保留学籍",
                    "enrolled_updatatime"=>"更新时间",
                    "ishaveportrait"=>"是否有人像 1有 0没有",
                    "stuportrait_faceimg"=>"最后一次采集的人像",
                    "stuportrait_updatetime"=>"照片更新时间",
                ];

                $result = array();
                $result["field"] = $field;
                $result['list'] =$studentlist?$studentlist:array();
                if ($studentlist) {
                    ajax_return(array('error' => '0', 'errortip' => "学生信息获取成功", 'result' => $result));
                } else {
                    ajax_return(array('error' => '1', 'errortip' => "学生信息获取失败", 'result' => array()));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }
    //人脸APP -- 获取学生最新人像 -- 针对教务的小书检核 -- 20231010 (apifox教务pad端接口）
    function getXsjhSchoolStuOneApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'getXsjhSchoolStuOneApi', $request)) {

                if($request['staffer_branch'] == ''){
                    ajax_return(array('error' => '1', 'errortip' => '教师编号不能为空', 'result' => array()));
                }
                if($request['student_id'] == '') {
                    ajax_return(array('error' => '1', 'errortip' => '学生ID不能为空', 'result' => array()));
                }

                $stafferOne = $this->DataControl->selectOne("select staffer_id,company_id,staffer_branch from smc_staffer where staffer_branch = '{$request['staffer_branch']}' limit 0,1 ");
                if(!$stafferOne){
                    ajax_return(array('error' => '1', 'errortip' => '教师信息不存在校务系统', 'result' => array()));
                }

                $studentOne = $this->DataControl->selectOne("select s.student_branch,ifnull(g.stuportrait_faceimg,'') as stuportrait_faceimg,g.stuportrait_creattime  
                from gmc_machine_stuportrait as g,smc_student as s 
                where g.company_id = '{$stafferOne['company_id']}' and g.student_id = '{$request['student_id']}' and g.student_id = s.student_id 
                order by g.stuportrait_creattime desc  
                limit 0,1");

                $field = [
                    "student_branch"=>"学生编号",
                    "stuportrait_faceimg"=>"人像链接",
                    "stuportrait_creattime"=>"人像录入时间"
                ];

                $result = array();
                $result["field"] = $field;
                $result['list'] =$studentOne?$studentOne:array();

                if ($studentOne) {
                    ajax_return(array('error' => '0', 'errortip' => "学生人像获取成功", 'result' => $result));
                } else {
                    ajax_return(array('error' => '1', 'errortip' => "学生人像获取失败", 'result' => (object)array()));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => (object)array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => (object)array()));
        }
    }
    //人脸APP -- 针对教务的小书检核  -- 班级学生是否出勤 -- 20240927 (apifox教务pad端接口）
    function getClassHourStudyListaApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'getXsjhClassHourStudyListaApi', $request)) {

                if(!isset($request['class_branch']) || $request['class_branch']==''){
                    ajax_return(array('error' => '1', 'errortip' => '请选择班级', 'result' => array()));
                }

                if(!isset($request['fixedtime']) || $request['fixedtime']==''){
                    ajax_return(array('error' => '1', 'errortip' => '请选择日期', 'result' => array()));
                }

                $stime = strtotime($request['fixedtime']);
                $etime = strtotime($request['fixedtime'])+86400;

                $sql = "select c.student_branch
,if((select 1 from gmc_machine_stucardlog as g where g.student_id = a.student_id and g.cardlog_clocktime > '{$stime}' and g.cardlog_clocktime < '{$etime}' limit 0,1)='1','出勤','--') as stuchecktype_name 
                        from smc_student_study as a
                        left join smc_class as b on b.class_id=a.class_id
                        left join smc_student as c on c.student_id=a.student_id  
                        where b.class_branch='{$request['class_branch']}'
                        order by a.study_id asc
                ";
                $hourstudyList=$this->DataControl->selectClear($sql);
                if ($hourstudyList) {
                    ajax_return(array('error' => '0', 'errortip' => "学生信息获取成功", 'result' => $hourstudyList));
                } else {
                    ajax_return(array('error' => '1', 'errortip' => "学生信息获取失败", 'result' => array()));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //针对 旷世（AI） 的加密方式
    function KsAiThisVerify($kstoken)
    {
        if($kstoken == ''){
            $this->errortip = '用户token不能为空！';
            $this->error = 1;
            return false;
        }
        $md5tokenbar = base64_encode(md5('Ks1107Ai'.date("Y-m-d")));
        if($md5tokenbar == $kstoken){
            return true;
        }else{
            $this->errortip = '用户token失效！';
            $this->error = 1;
            return false;
        }
    }
    //针对 旷世（AI） 的返回数据目前提供两个学校的  江桥万达/延吉校 --- 获取学校数据 -- 美语在读
    function getKsSchReadStuFaceApi(){
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->KsAiThisVerify($request['kstoken']);
        if ($pucArray) {
            if($request['school_branch'] != '01403' && $request['school_branch'] != '01453'){//上海延吉校 01403  江桥万达校 01453
                ajax_return(array('error' => '1', 'errortip' => '学校编号不在规定范围内', 'result' => array()));
            }
            if($request['school_branch'] == '') {
                ajax_return(array('error' => '1', 'errortip' => '学校编号不能为空', 'result' => array()));
            }

            //获取学生数据，接口四 231107写的，这里获取学生获取
            $datawhere = " 1 AND sh.company_id='8888' AND sh.school_branch = '{$request['school_branch']}'  ";
            $datawhere .= " and e.coursetype_id='65' ";

            $sql = "  select c.student_cnname,c.student_enname,c.student_branch ,b.class_branch,b.class_cnname,b.class_enname,e.coursetype_branch,e.coursetype_cnname,h.coursecat_branch,h.coursecat_cnname , 
                (select g.stuportrait_faceimg from gmc_machine_stuportrait as g where g.student_id = a.student_id  order by g.stuportrait_updatetime desc limit 0,1) as stuportrait_faceimg ,
                (select g.stuportrait_updatetime from gmc_machine_stuportrait as g where g.student_id = a.student_id  order by g.stuportrait_updatetime desc limit 0,1) as stuportrait_updatetime  
                from smc_school sh 
                left join smc_student_study a on sh.school_id = a.school_id
                left join smc_class b on a.class_id=b.class_id
                left join smc_student c on a.student_id=c.student_id
                left join smc_course d on b.course_id=d.course_id
                left join smc_code_coursetype e on d.coursetype_id=e.coursetype_id
                left join smc_code_coursecat h on d.coursecat_id=h.coursecat_id  
                where {$datawhere} 
                and b.class_type=0
                and b.class_status>-2  
                and ifnull(b.class_enddate,'')<>''
                and ifnull(a.study_endday,'')<>''
                and (case when a.study_beginday < b.class_stdate then b.class_stdate else a.study_beginday end)<=CURDATE()
                and (case when a.study_endday > b.class_enddate then b.class_enddate else a.study_endday end)>=CURDATE()
                group by a.school_id,e.coursetype_id,a.student_id
                ORDER BY b.class_id DESC ";
            $studentlist = $this->DataControl->selectClear($sql);

            if($studentlist){
                foreach ($studentlist as &$studentvar){
                    $studentvar['stuportrait_faceimg'] = is_null($studentvar['stuportrait_faceimg'])?'':$studentvar['stuportrait_faceimg'];
                    $studentvar['ishaveportrait'] = $studentvar['stuportrait_faceimg']?1:0;
                }
            }
            $field = [
                "student_cnname"=>"学生中文名",
                "student_enname"=>"学生英文名",
                "student_branch"=>"学生编号",
                "class_branch"=>"班级编号 ",
                "class_cnname"=>"班级中文名 ",
                "class_enname"=>"班级英文名 ",
                "coursetype_branch"=>"班组编号 ",
                "coursetype_cnname"=>"班组名称 ",
                "coursecat_branch"=>"班种编号 ",
                "coursecat_cnname"=>"班种名称 ",
                "stuportrait_faceimg"=>"最后一次采集的人像",
                "stuportrait_updatetime"=>"照片更新时间",
                "ishaveportrait"=>"是否有人像 1有 0没有",
            ];

            $result = array();
            $result["field"] = $field;
            $result['list'] =$studentlist?$studentlist:array();
            if ($studentlist) {
                ajax_return(array('error' => '0', 'errortip' => "学生信息获取成功", 'result' => $result));
            } else {
                ajax_return(array('error' => '1', 'errortip' => "学生信息获取失败", 'result' => array()));
            }
        }else{
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }
    //针对 旷世（AI） 的返回数据目前提供两个学校的  江桥万达/延吉校 --- 获取学校数据
    function getKsSchoolStuFaceApi(){
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->KsAiThisVerify($request['kstoken']);
        if ($pucArray) {
            if($request['school_branch'] != '01403' && $request['school_branch'] != '01453'){//上海延吉校 01403  江桥万达校 01453
                ajax_return(array('error' => '1', 'errortip' => '学校编号不在规定范围内', 'result' => array()));
            }
            if($request['school_branch'] == '') {
                ajax_return(array('error' => '1', 'errortip' => '学校编号不能为空', 'result' => array()));
            }

            //获取学生数据，接口四 231107写的，这里获取学生获取
            $datawhere = " a.school_branch = '{$request['school_branch']}' and a.company_id = '8888' and e.school_id > 1  ";
            if($request['is_in_class'] == 1){//只获取在班的数据
                $datawhere .= " and e.enrolled_status = '1'  ";
            }else{
                $datawhere .= " and e.enrolled_createtime > '1698768000'  ";//获取 20231101 之后的数据
            }
            if(isset($request['enrolled_updatatime']) && $request['enrolled_updatatime'] != ''){
                $datawhere .= " and  ( e.enrolled_updatatime > '{$request['enrolled_updatatime']}' or s.student_updatatime > '{$request['enrolled_updatatime']}' or s.student_createtime > '{$request['enrolled_updatatime']}')";
            }

            $sql = " select s.student_cnname,s.student_enname,s.student_branch,s.student_sex,e.enrolled_status,s.student_createtime,s.student_updatatime,
                if(e.enrolled_createtime>e.enrolled_updatatime,e.enrolled_createtime,e.enrolled_updatatime) as enrolled_updatatime,
                (select g.stuportrait_faceimg from gmc_machine_stuportrait as g where g.student_id = s.student_id  order by g.stuportrait_updatetime desc limit 0,1) as stuportrait_faceimg ,
                (select g.stuportrait_updatetime from gmc_machine_stuportrait as g where g.student_id = s.student_id  order by g.stuportrait_updatetime desc limit 0,1) as stuportrait_updatetime 
                from smc_school as a 
                left join smc_student_enrolled as e ON a.school_id = e.school_id 
                left join smc_student as s ON e.student_id = s.student_id  
                where {$datawhere}";
            $studentlist = $this->DataControl->selectClear($sql);

            if($studentlist){
                foreach ($studentlist as &$studentvar){
                    $studentvar['stuportrait_faceimg'] = is_null($studentvar['stuportrait_faceimg'])?'':$studentvar['stuportrait_faceimg'];
                    $studentvar['ishaveportrait'] = $studentvar['stuportrait_faceimg']?1:0;
                    if($studentvar['student_sex'] != '男' && $studentvar['student_sex'] != '女'){
                        $studentvar['student_sex'] = '';
                    }
                }
            }

            $field = [
                "student_cnname"=>"学生中文名",
                "student_enname"=>"学生英文名",
                "student_branch"=>"学生编号",
                "student_sex"=>"学生性别 男、女、空值",
                "student_createtime"=>"学生创建时间",
                "student_updatatime"=>"学生更新时间",
                "enrolled_status"=>"学生入校状态 0待入班 1已入班 -1已离校 2已毕业 3保留学籍",
                "enrolled_updatatime"=>"更新时间",
                "ishaveportrait"=>"是否有人像 1有 0没有",
                "stuportrait_faceimg"=>"最后一次采集的人像",
                "stuportrait_updatetime"=>"照片更新时间",
            ];

            $result = array();
            $result["field"] = $field;
            $result['list'] =$studentlist?$studentlist:array();
            if ($studentlist) {
                ajax_return(array('error' => '0', 'errortip' => "学生信息获取成功", 'result' => $result));
            } else {
                ajax_return(array('error' => '1', 'errortip' => "学生信息获取失败", 'result' => array()));
            }
        }else{
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }
    //针对 旷世（AI） 的返回数据目前提供两个学校的  江桥万达/延吉校 --- 获取学校数据  -- 某个学生最新人像数据
    function getKsSchoolStuOneFaceApi(){
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->KsAiThisVerify($request['kstoken']);
        if ($pucArray) {
            if($request['school_branch'] != '01403' && $request['school_branch'] != '01453'){//上海延吉校 01403  江桥万达校 01453
                ajax_return(array('error' => '1', 'errortip' => '学校编号不在规定范围内', 'result' => array()));
            }
            if($request['school_branch'] == '') {
                ajax_return(array('error' => '1', 'errortip' => '学校编号不能为空', 'result' => array()));
            }
            if($request['student_branch'] == '') {
                ajax_return(array('error' => '1', 'errortip' => '学生编号不能为空', 'result' => array()));
            }

            $schoooOne = $this->DataControl->selectOne("select school_id,company_id from smc_school where school_branch = '{$request['school_branch']}' limit 0,1");
            if(!$schoooOne) {
                ajax_return(array('error' => '1', 'errortip' => '学校信息未找到！', 'result' => array()));
            }

            $studentOne = $this->DataControl->selectOne("select s.student_branch,ifnull(g.stuportrait_faceimg,'') as stuportrait_faceimg,g.stuportrait_creattime  
                from smc_student as s,gmc_machine_stuportrait as g 
                where s.student_branch = '{$request['student_branch']}' and s.company_id = '{$schoooOne['company_id']}' and s.student_id = g.student_id 
                order by g.stuportrait_creattime desc  
                limit 0,1");

            $field = [
                "student_branch"=>"学生编号",
                "stuportrait_faceimg"=>"人像链接",
                "stuportrait_creattime"=>"人像录入时间"
            ];

            $result = array();
            $result["field"] = $field;
            $result['list'] =$studentOne?$studentOne:array();

            if ($studentOne) {
                ajax_return(array('error' => '0', 'errortip' => "学生人像获取成功", 'result' => $result));
            } else {
                ajax_return(array('error' => '1', 'errortip' => "学生人像获取失败", 'result' => (object)array()));
            }
        }else{
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //针对 商汤 -- 是被 是否陌生人等操作 20240418
    //针对 商汤 的返回数据目前提供两个学校的  江桥万达 --- 获取学校数据
    function getStSchReadStuFaceApi(){
        $request = Input('get.', '', 'trim,addslashes');
        ajax_return(array('error' => '1', 'errortip' => '请使用正式接口', 'result' => array()));
//        $pucArray = $this->KsAiThisVerify($request['kstoken']);
        if (1) {
            if($request['school_branch'] != '01453' && $request['school_branch'] != '01428'){//江桥万达校 01453  我格 01428
                ajax_return(array('error' => '1', 'errortip' => '学校编号不在规定范围内', 'result' => array()));
            }
            if($request['school_branch'] == '') {
                ajax_return(array('error' => '1', 'errortip' => '学校编号不能为空', 'result' => array()));
            }
            $schoolOne = $this->DataControl->selectOne(" select school_id,school_branch,school_shortname from smc_school where company_id = '8888' and school_branch = '{$request['school_branch']}' limit 0,1 ");
            if(!$schoolOne) {
                ajax_return(array('error' => '1', 'errortip' => '学校不存在', 'result' => array()));
            }

            $datawhere = " e.school_id = '{$schoolOne['school_id']}' and e.school_id > 1  ";
            $datawhere .= " and exists (select 1 from gmc_machine_stuportrait as g where g.student_id = s.student_id and g.stuportrait_isquality = '1' order by g.stuportrait_updatetime desc limit 0,1)  ";

            $sql = " select s.student_branch,e.enrolled_status,
                (select g.stuportrait_faceimg from gmc_machine_stuportrait as g where g.student_id = s.student_id and g.stuportrait_isquality = '1' order by g.stuportrait_updatetime desc limit 0,1) as stuportrait_faceimg 
                from smc_student_enrolled as e 
                left join smc_student as s ON e.student_id = s.student_id  
                where {$datawhere}";

            $studentlist = $this->DataControl->selectClear($sql);

            if($studentlist){
                foreach ($studentlist as &$studentvar){
                    $studentvar['stuportrait_faceimg'] = is_null($studentvar['stuportrait_faceimg'])?'':$studentvar['stuportrait_faceimg'];

//                    if($studentvar['stuportrait_faceimg']){
//                        $pucArray = $this->judgeImgQuality($studentvar['stuportrait_faceimg']);
//
//                        if($pucArray){
//                            $studentvar['img_quality'] = 1;
//                            $studentvar['img_quality_name'] = $this->errortip;
//                        }else{
//                            $studentvar['img_quality'] = 0;
//                            $studentvar['img_quality_name'] = $this->errortip;
//                        }
//                    }else{
//                        $studentvar['img_quality'] = 0;
//                        $studentvar['img_quality_name'] = '暂无头像';
//                    }
                }
            }
            $field = [
                "student_branch"=>"学生编号",
                "enrolled_status"=>"学生入校状态 0待入班 1已入班 -1已离校 2已毕业 3保留学籍",
                "stuportrait_faceimg"=>"最后一次采集的人像",
            ];

            $result = array();
            $result["field"] = $field;
            $result['list'] =$studentlist?$studentlist:array();
            if ($studentlist) {
                ajax_return(array('error' => '0', 'errortip' => "学生信息获取成功", 'result' => $result));
            } else {
                ajax_return(array('error' => '1', 'errortip' => "学生信息获取失败", 'result' => array()));
            }
        }else{
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }


    //针对  临时 的加密方式
    function LishiThisVerify($kstoken)
    {
        if($kstoken == ''){
            $this->errortip = '用户token不能为空！';
            $this->error = 1;
            return false;
        }
        $md5tokenbar = base64_encode(md5('LiShi241219cs'.date("Y-m-d")));
        if($md5tokenbar == $kstoken){
            return true;
        }else{
            $this->errortip = '用户token失效！';
            $this->error = 1;
            return false;
        }
    }
    //针对   临时  的返回数据目前提供  江桥万达
    function getLishiSchoolStuFaceApi(){
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->LishiThisVerify($request['lstoken']);
        if ($pucArray) {
            if($request['school_branch'] != '01453'){//上海延吉校 01403  江桥万达校 01453  //$request['school_branch'] != '01403' &&
                ajax_return(array('error' => '1', 'errortip' => '学校编号不在规定范围内', 'result' => array()));
            }
            if($request['school_branch'] == '') {
                ajax_return(array('error' => '1', 'errortip' => '学校编号不能为空', 'result' => array()));
            }

            $schoolOne = $this->DataControl->selectOne(" select school_id,school_branch from smc_school where company_id = '8888' and school_branch = '{$request['school_branch']}' and school_isclose = '0' and school_istest = '0'  ");
            if(!$schoolOne){
                ajax_return(array('error' => '1', 'errortip' => '学校信息不存在', 'result' => (object)array()));
            }

            $datawhere = " e.school_id = '{$schoolOne['school_id']}' and e.school_id > 1  ";
            if(isset($request['enrolled_updatatime']) && $request['enrolled_updatatime'] != ''){
                $datawhere .= " (select g.changelog_createtime from smc_student_changelog as g where g.student_id = s.student_id and g.school_id = e.school_id order by g.changelog_createtime desc limit 0,1) > '{$request['enrolled_updatatime']}' ";
            }
            if(isset($request['stuportrait_updatetime']) && $request['stuportrait_updatetime'] != ''){
                $datawhere .= " and (select g.stuportrait_updatetime from gmc_machine_stuportrait as g where g.student_id = s.student_id  and g.stuportrait_isquality = '1' order by g.stuportrait_updatetime desc limit 0,1) > '{$request['stuportrait_updatetime']}' ";
            }

            if (isset($request['p']) && $request['p'] !== '') {
                $page = $request['p'];
            } else {
                $page = '1';
            }
            if (isset($request['num']) && $request['num'] !== '') {
                $num = $request['num']>1000?1000:$request['num'];
            } else {
                $num = '10';
            }
            $pagestart = ($page - 1) * $num;

            $sql = " select s.student_branch,e.enrolled_status,
                (select g.changelog_createtime from smc_student_changelog as g where g.student_id = s.student_id and g.school_id = e.school_id order by g.changelog_createtime desc limit 0,1) as enrolled_updatatime, 
                (select g.stuportrait_faceimg from gmc_machine_stuportrait as g where g.student_id = s.student_id and g.stuportrait_isquality = '1' order by g.stuportrait_updatetime desc limit 0,1) as stuportrait_faceimg ,
                (select g.stuportrait_updatetime from gmc_machine_stuportrait as g where g.student_id = s.student_id  and g.stuportrait_isquality = '1' order by g.stuportrait_updatetime desc limit 0,1) as stuportrait_updatetime 
                from smc_student_enrolled as e 
                left join smc_student as s ON e.student_id = s.student_id  
                where {$datawhere} 
                limit {$pagestart},{$num} ";

            $studentlist = $this->DataControl->selectClear($sql);

            if($studentlist){
                foreach ($studentlist as &$studentvar){
                    $studentvar['enrolled_updatatime'] = ($studentvar['enrolled_updatatime'] > time())?time():$studentvar['enrolled_updatatime'];//确保时间不会大于当前时间

                    $studentvar['stuportrait_faceimg'] = is_null($studentvar['stuportrait_faceimg'])?'':$studentvar['stuportrait_faceimg'].'?x-oss-process=image/resize,m_lfit,w_1500,limit_0/auto-orient,1/quality,q_99';
                    $studentvar['stuportrait_updatetime'] = is_null($studentvar['stuportrait_updatetime'])?'':$studentvar['stuportrait_updatetime'];
                    $studentvar['stuportrait_updatetime'] = ($studentvar['stuportrait_updatetime'] > time())?time():$studentvar['stuportrait_updatetime'];//确保时间不会大于当前时间
                    $studentvar['ishaveportrait'] = $studentvar['stuportrait_faceimg']?1:0;
                }
            }

            $field = [
                "student_branch"=>"学生编号",
                "enrolled_status"=>"学生状态 0待入班 1已入班 -1已离校",
                "enrolled_updatatime"=>"状态更新时间",
                "ishaveportrait"=>"是否有人像 1有 0没有",
                "stuportrait_faceimg"=>"最后一次采集的合格人像",
                "stuportrait_updatetime"=>"最后一次采集的合格人像照片更新时间",
            ];

            $result = array();
            $result["field"] = $field;
            $result['list'] =$studentlist?$studentlist:array();
            if ($studentlist) {
                ajax_return(array('error' => '0', 'errortip' => "学生信息获取成功", 'result' => $result));
            } else {
                ajax_return(array('error' => '1', 'errortip' => "学生信息获取失败", 'result' => (object)array()));
            }
        }else{
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }


    //针对 酷爱（陈总） 临时的加密方式
    function LishiKAThisVerify($kstoken)
    {
        if($kstoken == ''){
            $this->errortip = '用户token不能为空！';
            $this->error = 1;
            return false;
        }
        $md5tokenbar = base64_encode(md5('KuAi250109ak'.date("Y-m-d")));
        if($md5tokenbar == $kstoken){
            return true;
        }else{
            $this->errortip = '用户token失效！';
            $this->error = 1;
            return false;
        }
    }
    //针对 酷爱（陈总） 的返回数据 --- 抓拍到的数据
    function getKaLsSchoolSnapFaceApi(){
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->LishiKAThisVerify($request['katoken']);
        if ($pucArray) {
            if($request['school_branch'] != '01437'){//上海延吉校 01403  江桥万达校 01453  复地活力城 01437
                ajax_return(array('error' => '1', 'errortip' => '学校编号不在规定范围内', 'result' => array()));
            }
            if($request['school_branch'] == '') {
                ajax_return(array('error' => '1', 'errortip' => '学校编号不能为空', 'result' => array()));
            }
            $shijiancha = abs($request['endtime']) - abs($request['starttime']);
            if($shijiancha > '86400' || $shijiancha < '1') {
                ajax_return(array('error' => '1', 'errortip' => '时间差不能大于一天', 'result' => array()));
            }

            $schoolOne = $this->DataControl->selectOne(" select school_id,school_branch from smc_school where company_id = '8888' and school_branch = '{$request['school_branch']}' and school_isclose = '0' and school_istest = '0'  ");
            if(!$schoolOne){
                ajax_return(array('error' => '1', 'errortip' => '学校信息不存在', 'result' => (object)array()));
            }

            $datawhere = " a.school_id = '{$schoolOne['school_id']}' and a.school_id > 1  ";
            $datawheretwo = " a.school_id = '{$schoolOne['school_id']}' and a.school_id > 1  ";
            if(isset($request['starttime']) && $request['starttime'] != ''){
                $datawhere .= " and a.identifylog_clocktime > '{$request['starttime']}' ";
                $datawheretwo .= " and a.mokelog_clocktime > '{$request['starttime']}' ";
            }
            if(isset($request['endtime']) && $request['endtime'] != ''){
                $datawhere .= " and a.identifylog_clocktime < '{$request['endtime']}' ";
                $datawheretwo .= " and a.mokelog_clocktime < '{$request['endtime']}' ";
            }

//            if (isset($request['p']) && $request['p'] !== '') {
//                $page = $request['p'];
//            } else {
//                $page = '1';
//            }
//            if (isset($request['num']) && $request['num'] !== '') {
//                $num = $request['num']>1000?1000:$request['num'];
//            } else {
//                $num = '10';
//            }
//            $pagestart = ($page - 1) * $num;


            $sqlOne = "SELECT a.school_id,a.machine_code,a.identifylog_img as img,a.identifylog_clocktime as clocktime
                FROM gmc_machine_monitor_identifylog as a 
                WHERE {$datawhere}  ";
            $stuList = $this->DataControl->selectClear($sqlOne);

            $sqlTwo = "SELECT a.school_id,a.machine_code,a.mokelog_img as img,a.mokelog_clocktime as clocktime
                FROM gmc_machine_monitor_mokelog as a 
                WHERE {$datawheretwo}  ";
            $mkList = $this->DataControl->selectClear($sqlTwo);

            $dataList = array();
            $dataList = array_merge($stuList,$mkList);

            $field = [
                "school_id"=>"学校ID",
                "machine_code"=>"设备ID",
                "img"=>"抓拍的照片",
                "clocktime"=>"抓拍的照片时间",
            ];

            $result = array();
            $result["field"] = $field;
            $result['list'] =$dataList?$dataList:array();
            if ($dataList) {
                ajax_return(array('error' => '0', 'errortip' => "抓拍照片获取成功", 'result' => $result));
            } else {
                ajax_return(array('error' => '1', 'errortip' => "抓拍照片获取失败", 'result' => (object)array()));
            }
        }else{
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }
    //针对 酷爱（陈总） 的返回数据 --- 抓拍到的背景图
    function getKaLsSchoolBackimgApi(){
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->LishiKAThisVerify($request['katoken']);
        if ($pucArray) {
            if($request['school_branch'] != '01437'){//上海延吉校 01403  江桥万达校 01453  复地活力城 01437
                ajax_return(array('error' => '1', 'errortip' => '学校编号不在规定范围内', 'result' => array()));
            }
            if($request['school_branch'] == '') {
                ajax_return(array('error' => '1', 'errortip' => '学校编号不能为空', 'result' => array()));
            }
            $shijiancha = abs($request['endtime']) - abs($request['starttime']);
            if($shijiancha > '86400' || $shijiancha < '1') {
                ajax_return(array('error' => '1', 'errortip' => '时间差不能大于一天', 'result' => array()));
            }

            $schoolOne = $this->DataControl->selectOne(" select school_id,school_branch from smc_school where company_id = '8888' and school_branch = '{$request['school_branch']}' and school_isclose = '0' and school_istest = '0'  ");
            if(!$schoolOne){
                ajax_return(array('error' => '1', 'errortip' => '学校信息不存在', 'result' => (object)array()));
            }

            $datawhere = " a.school_id = '{$schoolOne['school_id']}' and a.school_id > 1  ";
            if(isset($request['starttime']) && $request['starttime'] != ''){
                $datawhere .= " and a.backimagelog_clocktime > '{$request['starttime']}' ";
            }
            if(isset($request['endtime']) && $request['endtime'] != ''){
                $datawhere .= " and a.backimagelog_clocktime < '{$request['endtime']}' ";
            }

            $sqlOne = "SELECT a.school_id,a.machine_code,a.backimagelog_img as img,a.backimagelog_clocktime as clocktime
                FROM gmc_machine_monitor_backimagelog as a 
                WHERE {$datawhere}  ";
            $dataList = $this->DataControl->selectClear($sqlOne);

            $field = [
                "school_id"=>"学校ID",
                "machine_code"=>"设备ID",
                "img"=>"抓拍的背景照片",
                "clocktime"=>"抓拍的时间",
            ];

            $result = array();
            $result["field"] = $field;
            $result['list'] =$dataList?$dataList:array();
            if ($dataList) {
                ajax_return(array('error' => '0', 'errortip' => "抓拍背景照片获取成功", 'result' => $result));
            } else {
                ajax_return(array('error' => '1', 'errortip' => "抓拍背景照片获取失败", 'result' => (object)array()));
            }
        }else{
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }
    //针对 酷爱（陈总） 的 某校注册照片
    function getKuAiSchoolStuFaceApi(){
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->LishiKAThisVerify($request['katoken']);
        if ($pucArray) {
            if($request['school_branch'] != '01437'){//上海延吉校 01403  江桥万达校 01453  复地活力城 01437
                ajax_return(array('error' => '1', 'errortip' => '学校编号不在规定范围内', 'result' => array()));
            }
            if($request['school_branch'] == '') {
                ajax_return(array('error' => '1', 'errortip' => '学校编号不能为空', 'result' => array()));
            }

            $schoolOne = $this->DataControl->selectOne(" select school_id,school_branch from smc_school where company_id = '8888' and school_branch = '{$request['school_branch']}' and school_isclose = '0' and school_istest = '0'  ");
            if(!$schoolOne){
                ajax_return(array('error' => '1', 'errortip' => '学校信息不存在', 'result' => (object)array()));
            }

            $datawhere = " e.school_id = '{$schoolOne['school_id']}' and e.school_id > 1  ";
            if(isset($request['enrolled_updatatime']) && $request['enrolled_updatatime'] != ''){
                $datawhere .= " (select g.changelog_createtime from smc_student_changelog as g where g.student_id = s.student_id and g.school_id = e.school_id order by g.changelog_createtime desc limit 0,1) > '{$request['enrolled_updatatime']}' ";
            }
            if(isset($request['stuportrait_updatetime']) && $request['stuportrait_updatetime'] != ''){
                $datawhere .= " and (select g.stuportrait_updatetime from gmc_machine_stuportrait as g where g.student_id = s.student_id  and g.stuportrait_isquality = '1' order by g.stuportrait_updatetime desc limit 0,1) > '{$request['stuportrait_updatetime']}' ";
            }

            if (isset($request['p']) && $request['p'] !== '') {
                $page = $request['p'];
            } else {
                $page = '1';
            }
            if (isset($request['num']) && $request['num'] !== '') {
                $num = $request['num']>1000?1000:$request['num'];
            } else {
                $num = '10';
            }
            $pagestart = ($page - 1) * $num;

            $sql = " select s.student_branch,e.enrolled_status,
                (select g.changelog_createtime from smc_student_changelog as g where g.student_id = s.student_id and g.school_id = e.school_id order by g.changelog_createtime desc limit 0,1) as enrolled_updatatime, 
                (select g.stuportrait_faceimg from gmc_machine_stuportrait as g where g.student_id = s.student_id and g.stuportrait_isquality = '1' order by g.stuportrait_updatetime desc limit 0,1) as stuportrait_faceimg ,
                (select g.stuportrait_updatetime from gmc_machine_stuportrait as g where g.student_id = s.student_id  and g.stuportrait_isquality = '1' order by g.stuportrait_updatetime desc limit 0,1) as stuportrait_updatetime 
                from smc_student_enrolled as e 
                left join smc_student as s ON e.student_id = s.student_id  
                where {$datawhere} 
                limit {$pagestart},{$num} ";

            $studentlist = $this->DataControl->selectClear($sql);

            $contOne = $this->DataControl->selectOne(" select count(1) as allnum 
                from smc_student_enrolled as e 
                left join smc_student as s ON e.student_id = s.student_id  
                where {$datawhere}  ");

            if($studentlist){
                foreach ($studentlist as &$studentvar){
                    $studentvar['enrolled_updatatime'] = ($studentvar['enrolled_updatatime'] > time())?time():$studentvar['enrolled_updatatime'];//确保时间不会大于当前时间

                    $studentvar['stuportrait_faceimg'] = is_null($studentvar['stuportrait_faceimg'])?'':$studentvar['stuportrait_faceimg'].'?x-oss-process=image/resize,m_lfit,w_1500,limit_0/auto-orient,1/quality,q_99';
                    $studentvar['stuportrait_updatetime'] = is_null($studentvar['stuportrait_updatetime'])?'':$studentvar['stuportrait_updatetime'];
                    $studentvar['stuportrait_updatetime'] = ($studentvar['stuportrait_updatetime'] > time())?time():$studentvar['stuportrait_updatetime'];//确保时间不会大于当前时间
                    $studentvar['ishaveportrait'] = $studentvar['stuportrait_faceimg']?1:0;
                }
            }

            $field = [
                "student_branch"=>"学生编号",
                "enrolled_status"=>"学生状态 0待入班 1已入班 -1已离校",
                "enrolled_updatatime"=>"状态更新时间",
                "ishaveportrait"=>"是否有人像 1有 0没有",
                "stuportrait_faceimg"=>"最后一次采集的合格人像",
                "stuportrait_updatetime"=>"最后一次采集的合格人像照片更新时间",
            ];

            $result = array();
            $result["field"] = $field;
            $result["allnum"] = $contOne['allnum'];
            $result['list'] =$studentlist?$studentlist:array();
            if ($studentlist) {
                ajax_return(array('error' => '0', 'errortip' => "学生信息获取成功", 'result' => $result));
            } else {
                ajax_return(array('error' => '1', 'errortip' => "学生信息获取失败", 'result' => (object)array()));
            }
        }else{
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }


    //临时自动跑数据任务 -- 人像数据 -- 活力城校
    function getHlcStSchReadStuFaceQualityApi(){
        $request = Input('get.', '', 'trim,addslashes');

        // 671 上海我格广场校      1199 上海江桥万达校  679 复地活力城学校
        $sql = "
            select a.stuportrait_id,a.stuportrait_faceimg
            from gmc_machine_stuportrait as a  
            where a.company_id = '8888' and a.stuportrait_isqualitytwo = 2 and a.stuportrait_isqualitytimetwo = '0'
            and a.school_id = '679' 
            limit 0,5
        ";
        //and a.school_id in ('671','1199')
        //and a.student_id > '0'
        $studentlist = $this->DataControl->selectClear($sql);

        if($studentlist){
            foreach ($studentlist as $studentvar){
                $pucArray = $this->judgeImgQuality($studentvar['stuportrait_faceimg']);

                $updata = array();
                if($pucArray){
                    $updata['stuportrait_isqualitytwo'] = 1;
                    $updata['stuportrait_isqualitynametwo'] = $this->errortip;
                    $updata['stuportrait_isqualitytimetwo'] = time();
                }else{
                    $updata['stuportrait_isqualitytwo'] = 2;
                    $updata['stuportrait_isqualitynametwo'] = $this->errortip;
                    $updata['stuportrait_isqualitytimetwo'] = time();
                }
                $this->DataControl->updateData('gmc_machine_stuportrait',"stuportrait_id='{$studentvar['stuportrait_id']}'",$updata);
            }
        }

        echo '1';
    }
    //临时自动跑数据任务 -- 人像数据
    function getStSchReadStuFaceQualityApi(){
        $request = Input('get.', '', 'trim,addslashes');

        // 671 上海我格广场校      1199 上海江桥万达校
        $sql = "
            select a.stuportrait_id,a.stuportrait_faceimg
            from gmc_machine_stuportrait as a  
            where a.company_id = '8888' and a.stuportrait_isqualitytwo = 0 
            limit 0,5
        ";
        //and  a.school_id in (563,578,584,609,611,669,671,674,679,681,689,846,1028,1115,1574,2112,2270,2286,2370,2372)
        //and a.school_id in ('671','1199')
        //and a.student_id > '0'
        $studentlist = $this->DataControl->selectClear($sql);

        if($studentlist){
            foreach ($studentlist as $studentvar){
                $pucArray = $this->judgeImgQuality($studentvar['stuportrait_faceimg']);

                $updata = array();
                if($pucArray){
                    $updata['stuportrait_isqualitytwo'] = 1;
                    $updata['stuportrait_isqualitynametwo'] = $this->errortip;
                    $updata['stuportrait_isqualitytimetwo'] = time();
                }else{
                    $updata['stuportrait_isqualitytwo'] = 2;
                    $updata['stuportrait_isqualitynametwo'] = $this->errortip;
                    $updata['stuportrait_isqualitytimetwo'] = time();
                }
                $this->DataControl->updateData('gmc_machine_stuportrait',"stuportrait_id='{$studentvar['stuportrait_id']}'",$updata);
            }
        }

        echo '1';
    }
    //临时自动跑数据任务 -- 人像数据
    function getStSchReadStuFaceQualityTwoApi(){
        $request = Input('get.', '', 'trim,addslashes');

        // 671 上海我格广场校      1199 上海江桥万达校
        $sql = "
            select a.stuportrait_id,a.stuportrait_faceimg
            from gmc_machine_stuportrait as a  
            where a.company_id = '8888' and a.stuportrait_isqualitytwo = 2 and a.stuportrait_isqualitynametwo = '图片未解析成功！'
            limit 0,5
        ";
        //and  a.school_id in (563,578,584,609,611,669,671,674,679,681,689,846,1028,1115,1574,2112,2270,2286,2370,2372)
        //and a.school_id in ('671','1199')
        //and a.student_id > '0'
        $studentlist = $this->DataControl->selectClear($sql);

        if($studentlist){
            foreach ($studentlist as $studentvar){
                $pucArray = $this->judgeImgQuality($studentvar['stuportrait_faceimg']);

                $updata = array();
                if($pucArray){
                    $updata['stuportrait_isqualitytwo'] = 1;
                    $updata['stuportrait_isqualitynametwo'] = $this->errortip;
                    $updata['stuportrait_isqualitytimetwo'] = time();
                }else{
                    $updata['stuportrait_isqualitytwo'] = 2;
                    $updata['stuportrait_isqualitynametwo'] = $this->errortip;
                    $updata['stuportrait_isqualitytimetwo'] = time();
                }
                $this->DataControl->updateData('gmc_machine_stuportrait',"stuportrait_id='{$studentvar['stuportrait_id']}'",$updata);
            }
        }

        echo '1';
    }

    //第一次跑数据
    function getStSchReadStuFaceQualityApiBak(){
        $request = Input('get.', '', 'trim,addslashes');

        // 671 上海我格广场校      1199 上海江桥万达校
        $sql = "
            select a.stuportrait_id,a.stuportrait_faceimg
            from gmc_machine_stuportrait as a  
            where a.company_id = '8888' and a.stuportrait_isquality = 0 
            limit 0,5
        ";
        //and a.school_id in ('671','1199')
        //and a.student_id > '0'
        $studentlist = $this->DataControl->selectClear($sql);

        if($studentlist){
            foreach ($studentlist as $studentvar){
                $pucArray = $this->judgeImgQuality($studentvar['stuportrait_faceimg']);

                $updata = array();
                if($pucArray){
                    $updata['stuportrait_isquality'] = 1;
                    $updata['stuportrait_isqualityname'] = $this->errortip;
                    $updata['stuportrait_isqualitytime'] = time();
                }else{
                    $updata['stuportrait_isquality'] = 2;
                    $updata['stuportrait_isqualityname'] = $this->errortip;
                    $updata['stuportrait_isqualitytime'] = time();
                }
                $this->DataControl->updateData('gmc_machine_stuportrait',"stuportrait_id='{$studentvar['stuportrait_id']}'",$updata);
            }
        }

        echo '1';
    }

    //针对 人脸识别项目各 --------- 源新坦达
    //模拟加密参数
    function testYxParameterApi()
    {
        $parameter = array();
        $parameter['timesteps'] = time();
        $parameter['apiuser_code'] = 'cFaceToKdd';
        $parameter['company_id'] = '8888';

        $apiuserOne = $this->DataControl->getFieldOne('imc_apiuser', "apiuser_id,apiuser_aeskey,apiuser_aesiv", "apiuser_code = '{$parameter['apiuser_code']}'");
        if (!$apiuserOne) {
            $this->errortip = "未查询到您的授权信息";
            $this->error = true;
            return false;
        }

        $aes = new \Aesencdec($apiuserOne['apiuser_aeskey'], $apiuserOne['apiuser_aesiv']);
        $parameterJson = json_encode($parameter, '1');
        $jmsting = $aes->encrypt($parameterJson);//解密

        $result = array();
        $result['timesteps'] = $parameter['timesteps'];
        $result['apiuser_code'] = $parameter['apiuser_code'];
        $result['veytoken'] = $jmsting;

        ajax_return(array('error' => '0', 'errortip' => '模拟参数获取成功', 'result' => $result));
    }
    //瞿 拆分数据
    function tree($items)
    {
        $son = array();
        if(is_array($items)){
            foreach($items as $k=>&$v) {
                if($v['father_id'] == 0) {
                    $son[$k]['title'] = $v['organize_cnname'];
                    $son[$k]['organize_class'] = $v['organize_class'];
                    $son[$k]['organize_id'] = $v['organize_id'];
                    foreach ($items as $key=>$value) {
                        if ($v['organize_id'] == $value['father_id']) {
                            $son[$k]['children'][$key]['title'] = $value['organize_cnname'];
                            $son[$k]['children'][$key]['organize_id'] = $value['organize_id'];
                            $son[$k]['children'][$key]['organize_class'] = $value['organize_class'];
                        }
                    }
                }
            }
        }
        return $son;
    }
    //获取课叮铛的组织架构信息 --------- 源新坦达
    function getYxComOrganizeApi()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $request['isEncryption'] = '9';
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'getYxComOrganizeApi', $request)) {
                //架构大类
                $data = $this-> DataControl->selectClear("select organize_id,organize_cnname,organize_class,father_id from gmc_company_organize where company_id = '8888' and organizeclass_id = '480' and organize_id <> '3387' and father_id <> '3387' ");
                if($data){
                    $organize = $this->tree($data);
                }else{
                    $organize = array();
                }
                $result = array();
                $result['list'] =$organize;
                if ($organize) {
                    ajax_return(array('error' => '0', 'errortip' => "组织架构获取成功", 'result' => $result));
                } else {
                    ajax_return(array('error' => '1', 'errortip' => "组织架构获取失败", 'result' => (object)array()));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => (object)array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => (object)array()));
        }
    }
    //获取学校信息数据 --------- 源新坦达
    function getYxSchoolListApi()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $request['isEncryption'] = '9';
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'getYxSchoolListApi', $request)) {

                $schoolList = $this->DataControl->selectClear("
                select a.school_cnname,a.school_shortname,a.school_branch,a.school_province,b.region_name as school_provincename
                    ,(SELECT p.organize_id from gmc_company_organizeschool as o,gmc_company_organize as p WHERE o.school_id = a.school_id and o.organize_id = p.organize_id and p.father_id = 0 and p.organizeclass_id = '480' limit 0,1) as organize_id_one
                    ,(SELECT p.organize_id from gmc_company_organizeschool as o,gmc_company_organize as p WHERE o.school_id = a.school_id and o.organize_id = p.organize_id and p.father_id > 0 and p.organizeclass_id = '480' limit 0,1) as organize_id_two
                from smc_school as a,smc_code_region as b
                where a.company_id = '8888' and a.school_isclose = '0' and a.school_istest = '0'  and a.school_province = b.region_id 
                order by a.district_id asc  
                ");

                $field = [
                    "school_cnname"=>"学校名称",
                    "school_shortname"=>"学校简称",
                    "school_branch"=>"学校编号",
                    "school_province"=>"省份ID",
                    "school_provincename"=>"省份名称",
                    "organize_id_one"=>"组织架构一级",
                    "organize_id_two"=>"组织架构二级",
                ];

                $result = array();
                $result["field"] = $field;
                $result['list'] =$schoolList?$schoolList:array();
                if ($schoolList) {
                    ajax_return(array('error' => '0', 'errortip' => "学校信息获取成功", 'result' => $result));
                } else {
                    ajax_return(array('error' => '1', 'errortip' => "学校信息获取失败", 'result' => (object)array()));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => (object)array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => (object)array()));
        }
    }
    //获取学校学员信息数据 --------- 源新坦达
    function getYxSchoolStuAllApi()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $request['isEncryption'] = '9';
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'getYxSchoolStuAllApi', $request)) {

                if($request['school_branch'] == '') {
                    ajax_return(array('error' => '1', 'errortip' => '学校编号不能为空', 'result' => (object)array()));
                }
                $schoolOne = $this->DataControl->selectOne(" select school_id,school_branch from smc_school where company_id = '8888' and school_branch = '{$request['school_branch']}' and school_isclose = '0' and school_istest = '0'  ");
                if(!$schoolOne){
                    ajax_return(array('error' => '1', 'errortip' => '学校信息不存在', 'result' => (object)array()));
                }

                $datawhere = " e.school_id = '{$schoolOne['school_id']}' and e.school_id > 1  ";
                if(isset($request['enrolled_updatatime']) && $request['enrolled_updatatime'] != ''){
                    $datawhere .= " (select g.changelog_createtime from smc_student_changelog as g where g.student_id = s.student_id and g.school_id = e.school_id order by g.changelog_createtime desc limit 0,1) > '{$request['enrolled_updatatime']}' ";
                }
                if(isset($request['stuportrait_updatetime']) && $request['stuportrait_updatetime'] != ''){
                    $datawhere .= " and (select g.stuportrait_updatetime from gmc_machine_stuportrait as g where g.student_id = s.student_id  and g.stuportrait_isquality = '1' order by g.stuportrait_updatetime desc limit 0,1) > '{$request['stuportrait_updatetime']}' ";
                }

                $sql = " select s.student_branch,e.enrolled_status,
                (select g.changelog_createtime from smc_student_changelog as g where g.student_id = s.student_id and g.school_id = e.school_id order by g.changelog_createtime desc limit 0,1) as enrolled_updatatime, 
                (select g.stuportrait_faceimg from gmc_machine_stuportrait as g where g.student_id = s.student_id and g.stuportrait_isquality = '1' order by g.stuportrait_updatetime desc,g.stuportrait_id desc limit 0,1) as stuportrait_faceimg ,
                (select g.stuportrait_updatetime from gmc_machine_stuportrait as g where g.student_id = s.student_id  and g.stuportrait_isquality = '1' order by g.stuportrait_updatetime desc,g.stuportrait_id desc limit 0,1) as stuportrait_updatetime 
                from smc_student_enrolled as e 
                left join smc_student as s ON e.student_id = s.student_id  
                where {$datawhere}";

                $studentlist = $this->DataControl->selectClear($sql);

                if($studentlist){
                    foreach ($studentlist as &$studentvar){
                        $studentvar['enrolled_updatatime'] = ($studentvar['enrolled_updatatime'] > time())?time():$studentvar['enrolled_updatatime'];//确保时间不会大于当前时间

                        $studentvar['stuportrait_faceimg'] = is_null($studentvar['stuportrait_faceimg'])?'':$studentvar['stuportrait_faceimg'].'?x-oss-process=image/resize,m_lfit,w_1500,limit_0/auto-orient,1/quality,q_99';
                        $studentvar['stuportrait_updatetime'] = is_null($studentvar['stuportrait_updatetime'])?'':$studentvar['stuportrait_updatetime'];
                        $studentvar['stuportrait_updatetime'] = ($studentvar['stuportrait_updatetime'] > time())?time():$studentvar['stuportrait_updatetime'];//确保时间不会大于当前时间
                        $studentvar['ishaveportrait'] = $studentvar['stuportrait_faceimg']?1:0;
                    }
                }

                $field = [
                    "student_branch"=>"学生编号",
                    "enrolled_status"=>"学生状态 0待入班 1已入班 -1已离校",
                    "enrolled_updatatime"=>"状态更新时间",
                    "ishaveportrait"=>"是否有人像 1有 0没有",
                    "stuportrait_faceimg"=>"最后一次采集的合格人像",
                    "stuportrait_updatetime"=>"最后一次采集的合格人像照片更新时间",
                ];

                $result = array();
                $result["field"] = $field;
                $result['list'] =$studentlist?$studentlist:array();
                if ($studentlist) {
                    ajax_return(array('error' => '0', 'errortip' => "学生信息获取成功", 'result' => $result));
                } else {
                    ajax_return(array('error' => '1', 'errortip' => "学生信息获取失败", 'result' => (object)array()));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => (object)array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => (object)array()));
        }
    }
    //获取学校教师信息数据 --------- 源新坦达
    function getYxSchoolStaffAllApi()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $request['isEncryption'] = '9';
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'getYxSchoolStaffAllApi', $request)) {

                if($request['school_branch'] == '') {
                    ajax_return(array('error' => '1', 'errortip' => '学校编号不能为空', 'result' => (object)array()));
                }
                $schoolOne = $this->DataControl->selectOne(" select school_id,school_branch,company_id from smc_school where company_id = '8888' and school_branch = '{$request['school_branch']}' and school_isclose = '0' and school_istest = '0'  ");
                if(!$schoolOne){
                    ajax_return(array('error' => '1', 'errortip' => '学校信息不存在', 'result' => (object)array()));
                }

                $datawhere = " ( (p.school_id = '{$schoolOne['school_id']}' and p.school_id > 1 and s.staffer_leave ='0') 
                or (p.company_id = '{$schoolOne['company_id']}' and p.school_id = '0' and s.staffer_leave ='0') )  ";

                if(isset($request['enrolled_updatatime']) && $request['enrolled_updatatime'] != ''){
                    $datawhere .= " and  ( s.staffer_updatetime > '{$request['enrolled_updatatime']}' or s.staffer_createtime > '{$request['enrolled_updatatime']}' )";
                }
                if(isset($request['stuportrait_updatetime']) && $request['stuportrait_updatetime'] != ''){
                    $datawhere .= " and (select g.stuportrait_updatetime from gmc_machine_stuportrait as g where g.main_staffer_id = s.staffer_id   and g.stuportrait_isquality = '1' order by g.stuportrait_updatetime desc limit 0,1) > '{$request['stuportrait_updatetime']}' ";
                }

                $sql = "SELECT s.staffer_branch,s.staffer_leave,s.staffer_isparttime,s.staffer_createtime,s.staffer_updatetime,
                        (select g.stuportrait_faceimg from gmc_machine_stuportrait as g where g.main_staffer_id = s.staffer_id and g.stuportrait_isquality = '1' order by g.stuportrait_updatetime desc,g.stuportrait_id desc limit 0,1) as stuportrait_faceimg ,
                        (select g.stuportrait_updatetime from gmc_machine_stuportrait as g where g.main_staffer_id = s.staffer_id and g.stuportrait_isquality = '1' order by g.stuportrait_updatetime desc,g.stuportrait_id desc limit 0,1) as stuportrait_updatetime 
                    FROM smc_staffer AS s
                    LEFT JOIN gmc_staffer_postbe AS p ON s.staffer_id = p.staffer_id 
                    left join smc_school as sc on sc.school_id = p.school_id
                    left join gmc_company as c on c.company_id = sc.company_id
                    WHERE {$datawhere}
                    GROUP BY s.staffer_id
                    ORDER BY s.staffer_id DESC ";
                $stafflist = $this->DataControl->selectClear($sql);

                if($stafflist){
                    foreach ($stafflist as &$staffvar){
                        $staffvar['staffer_updatetime'] = ($staffvar['staffer_updatetime'] > $staffvar['staffer_createtime'])?$staffvar['staffer_updatetime']:$staffvar['staffer_createtime'];
                        $staffvar['staffer_updatetime'] = ($staffvar['staffer_updatetime'] > time())?time():$staffvar['staffer_updatetime'];//确保时间不会大于当前时间

                        $staffvar['stuportrait_faceimg'] = is_null($staffvar['stuportrait_faceimg'])?'':$staffvar['stuportrait_faceimg'].'?x-oss-process=image/resize,m_lfit,w_1500,limit_0/auto-orient,1/quality,q_99';
                        $staffvar['stuportrait_updatetime'] = is_null($staffvar['stuportrait_updatetime'])?'':$staffvar['stuportrait_updatetime'];
                        $staffvar['stuportrait_updatetime'] = ($staffvar['stuportrait_updatetime'] > time())?time():$staffvar['stuportrait_updatetime'];//确保时间不会大于当前时间
                        $staffvar['ishaveportrait'] = $staffvar['stuportrait_faceimg']?1:0;

                        $staffvar['staffer_createtime'] = '';
                    }
                }

                $field = [
                    "staffer_branch"=>"职工编号",
                    "staffer_leave"=>"是否离职  0未离开 1已离开",
                    "staffer_isparttime"=>"是否兼职老师：0全职1兼职",
                    "staffer_createtime"=>"不使用",
                    "staffer_updatetime"=>"教师信息更新时间",
                    "ishaveportrait"=>"是否有人像 1有 0没有",
                    "stuportrait_faceimg"=>"最后一次采集的合格人像",
                    "stuportrait_updatetime"=>"最后一次采集的合格人像照片更新时间",
                ];

                $result = array();
                $result["field"] = $field;
                $result['list'] =$stafflist?$stafflist:array();
                if ($stafflist) {
                    ajax_return(array('error' => '0', 'errortip' => "职工信息获取成功", 'result' => $result));
                } else {
                    ajax_return(array('error' => '1', 'errortip' => "职工信息获取失败", 'result' => (object)array()));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => (object)array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => (object)array()));
        }
    }
    //获取惇信接口数据（固定开放 某几个手机号） --------- 源新坦达
    function sendYxSomeMisApi()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $request['isEncryption'] = '9';
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'sendYxSomeMisApi', $request)) {

                $mobile = trim($request['mobile']);//手机号
                $contxt = trim($request['contxt']);//短信内容
                if(!$contxt){
                    ajax_return(array('error' => '1', 'errortip' => "通知内容不能为空", 'result' => (object)array()));
                }

                if(preg_match("/^(1(\d){10})$|^(09(\d){8})$/", $mobile) == '0'){
                    ajax_return(array('error' => '1', 'errortip' => "手机号不符合要求", 'result' => (object)array()));
                }
                //授权手机号列表
                $mstr = ['13127587321','15221117893'];
                if(!in_array($mobile,$mstr)){
                    ajax_return(array('error' => '1', 'errortip' => "您的手机号未授权", 'result' => (object)array()));
                }

                //一小时内发送次数
                $mintime = time() - 3600;
                $mislognum = $this->DataControl->selectOne("select count(mislog_id) as mislognum from gmc_mislog where mislog_mobile='{$mobile}' and mislog_tilte = '人脸监控异常' and mislog_time >= '{$mintime}' limit 0,1 ");
                if ($mislognum['mislognum'] > 5) {
                    ajax_return(array('error' => '1', 'errortip' => "您的手机已超出发送验证码次数，请联系客服！", 'result' => (object)array()));
                }
                //最近一次发送时间
                $sendmisrz = $this->DataControl->getFieldOne('gmc_mislog', "mislog_time", "mislog_mobile='{$mobile}' and mislog_tilte = '人脸监控异常'", "order by mislog_time DESC");
                if ($sendmisrz && (time() - $sendmisrz['mislog_time']) < 60) {
                    ajax_return(array('error' => '1', 'errortip' => "验证码已发送！", 'result' => (object)array()));
                } else {
                    $tilte = "人脸监控异常";
                    //短信发送
                    if ($this->Sendmisgo($mobile, $contxt, $tilte, '', 8888)) {
                        ajax_return(array('error' => '0', 'errortip' => '发送成功', 'result' => (object)array()));
                    } else {
                        ajax_return(array('error' => '1', 'errortip' => '发送失败!', 'result' => (object)array()));
                    }
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => (object)array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => (object)array()));
        }
    }

    //考勤机-心跳记录 --------- 源新坦达
    function recordHeartbeatLogApi(){
        $request = Input('post.', '', 'trim,addslashes');
        $request['isEncryption'] = '9';
        $request['isOneHour'] = '1';
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'recordHeartbeatLogApi', $request)) {

                if(!isset($request['school_branch']) || $request['school_branch']==''){
                    ajax_return(array('error' => '1', 'errortip' => '校区代码必须传值', 'result' => array()));
                }else{
                    $schoolOne=$this->DataControl->getFieldOne("smc_school","company_id,school_id","school_branch='{$request['school_branch']}'");
                    if(!$schoolOne){
                        ajax_return(array('error' => '1', 'errortip' => '校区代码不存在', 'result' => array()));
                    }
                }

                if($request['machine_code'] == ''){
                    ajax_return(array('error' => '1', 'errortip' => '设备序列号必须传值', 'result' => array()));
                }

                $heartbeat = $this->DataControl->selectOne(" select heartbeat_creattime from gmc_machine_monitor_heartbeat where company_id = '{$schoolOne['company_id']}' and school_id = '{$schoolOne['school_id']}' and  machine_code = '{$request['machine_code']}' order by heartbeat_id desc limit 0,1 ");

                $date =array();
                $date['company_id'] = $schoolOne['company_id'];
                $date['school_id'] = $schoolOne['school_id'];
                $date['machine_code'] = $request['machine_code'];
                $date['heartbeat_type'] = $request['heartbeat_type'];
                $date['heartbeat_intervaltime'] = ($heartbeat['heartbeat_creattime']>1)?(time() - $heartbeat['heartbeat_creattime']):0;
                $date['heartbeat_creattime'] = time();
                $date['heartbeat_updatetime'] = time();

                if ($this->DataControl->insertData('gmc_machine_monitor_heartbeat',$date)) {
                    ajax_return(array('error' => '0', 'errortip' => "心跳记录成功", 'result' => array()));
                } else {
                    ajax_return(array('error' => '1', 'errortip' => "心跳记录失败", 'result' => array()));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }
    //考勤机-识别记录 -- 建波帮写 --------- 源新坦达
    function recordIdentifyLogApi(){
        $request = Input('post.', '', 'trim,addslashes');
        $request['isEncryption'] = '9';
        $request['isOneHour'] = '1';
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            if(!isset($request['school_branch']) || $request['school_branch']==''){
                ajax_return(array('error' => '1', 'errortip' => '校区代码必须传值', 'result' => array()));
            }else{
                $schoolOne=$this->DataControl->getFieldOne("smc_school","company_id,school_id","school_branch='{$request['school_branch']}'");
                if(!$schoolOne){
                    ajax_return(array('error' => '1', 'errortip' => '校区代码不存在', 'result' => array()));
                }
            }
            if($request['machine_code'] == ''){
                ajax_return(array('error' => '1', 'errortip' => '设备序列号必须传值', 'result' => array()));
            }

            if($request['identifylog_type'] == '1'){

                if($request['student_branch'] == ''){
                    ajax_return(array('error' => '1', 'errortip' => '学生代码必须传值', 'result' => array()));
                }else{
                    $stuOne=$this->DataControl->getFieldOne("smc_student","student_id","company_id='{$schoolOne['company_id']}' and student_branch='{$request['student_branch']}'");
                    if(!$stuOne){
                        ajax_return(array('error' => '1', 'errortip' => '学生不存在', 'result' => array()));
                    }
                }
                if(!$this->DataControl->getFieldOne("smc_student_enrolled","school_id","school_id='{$schoolOne['school_id']}' and student_id='{$stuOne['student_id']}' and enrolled_status in (0,1,3)")){
                    ajax_return(array('error' => '1', 'errortip' => '该学生未在学校在读', 'result' => array()));
                }

            }elseif($request['identifylog_type'] == '2'){

                if($request['staffer_branch'] == ''){
                    ajax_return(array('error' => '1', 'errortip' => '教师代码必须传值', 'result' => array()));
                }else{
                    $staffOne=$this->DataControl->getFieldOne("smc_staffer","staffer_id","company_id='{$schoolOne['company_id']}' and staffer_branch='{$request['staffer_branch']}'");
                    if(!$staffOne){
                        ajax_return(array('error' => '1', 'errortip' => '教师不存在', 'result' => array()));
                    }
                }

            }elseif($request['identifylog_type'] != '1' && $request['identifylog_type'] != '2'){
                ajax_return(array('error' => '1', 'errortip' => '识别类型参数不符合要求！', 'result' => array()));
            }

            $date =array();
            $date['company_id'] = $schoolOne['company_id'];
            $date['school_id'] = $schoolOne['school_id'];
            $date['machine_code'] = $request['machine_code'];
            $date['student_id'] = $stuOne['student_id']?$stuOne['student_id']:0;
            $date['staffer_id'] = $staffOne['staffer_id']?$staffOne['staffer_id']:0;
            $date['identifylog_type'] = $request['identifylog_type'];
            $date['identifylog_clocktime'] = $request['identifylog_clocktime'];
            $date['identifylog_img'] = $request['identifylog_img'];
            $date['identifylog_registerimg'] = $request['identifylog_registerimg'];
            $date['identifylog_rate'] = $request['identifylog_rate'];
            $date['identifylog_creattime'] = time();
            $date['identifylog_updatetime'] = time();

            if ($this->DataControl->insertData('gmc_machine_monitor_identifylog',$date)) {
                ajax_return(array('error' => '0', 'errortip' => "识别记录成功", 'result' => array()));
            } else {
                ajax_return(array('error' => '1', 'errortip' => "识别记录失败", 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }
    //考勤机-背景图回传记录 -- 建波帮写 --------- 源新坦达
    function recordBackimageLogApi(){
        $request = Input('post.', '', 'trim,addslashes');
        $request['isEncryption'] = '9';
        $request['isOneHour'] = '1';
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            if(!isset($request['school_branch']) || $request['school_branch']==''){
                ajax_return(array('error' => '1', 'errortip' => '校区代码必须传值', 'result' => array()));
            }else{
                $schoolOne=$this->DataControl->getFieldOne("smc_school","company_id,school_id","school_branch='{$request['school_branch']}'");
                if(!$schoolOne){
                    ajax_return(array('error' => '1', 'errortip' => '校区代码不存在', 'result' => array()));
                }
            }
            if($request['machine_code'] == ''){
                ajax_return(array('error' => '1', 'errortip' => '设备序列号必须传值', 'result' => array()));
            }

            $date =array();
            $date['company_id'] = $schoolOne['company_id'];
            $date['school_id'] = $schoolOne['school_id'];
            $date['machine_code'] = $request['machine_code'];
            $date['backimagelog_clocktime'] = $request['backimagelog_clocktime'];
            $date['backimagelog_img'] = $request['backimagelog_img'];
            $date['backimagelog_creattime'] = time();
            $date['backimagelog_updatetime'] = time();

            if ($this->DataControl->insertData('gmc_machine_monitor_backimagelog',$date)) {
                ajax_return(array('error' => '0', 'errortip' => "记录成功", 'result' => array()));
            } else {
                ajax_return(array('error' => '1', 'errortip' => "记录失败", 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }
    //考勤机-陌客记录回传记录 -- 建波帮写 --------- 源新坦达
    function recordMokeLogApi(){
        $request = Input('post.', '', 'trim,addslashes');
        $request['isEncryption'] = '9';
        $request['isOneHour'] = '1';
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            if(!isset($request['school_branch']) || $request['school_branch']==''){
                ajax_return(array('error' => '1', 'errortip' => '校区代码必须传值', 'result' => array()));
            }else{
                $schoolOne=$this->DataControl->getFieldOne("smc_school","company_id,school_id","school_branch='{$request['school_branch']}'");
                if(!$schoolOne){
                    ajax_return(array('error' => '1', 'errortip' => '校区代码不存在', 'result' => array()));
                }
            }
            if($request['machine_code'] == ''){
                ajax_return(array('error' => '1', 'errortip' => '设备序列号必须传值', 'result' => array()));
            }

            $date =array();
            $date['company_id'] = $schoolOne['company_id'];
            $date['school_id'] = $schoolOne['school_id'];
            $date['machine_code'] = $request['machine_code'];
            $date['mokelog_clocktime'] = $request['mokelog_clocktime'];
            $date['mokelog_branch'] = $request['mokelog_branch'];
            $date['mokelog_img'] = $request['mokelog_img'];
            $date['mokelog_rate'] = $request['mokelog_rate'];
            $date['mokelog_creattime'] = time();
            $date['mokelog_updatetime'] = time();

            if ($this->DataControl->insertData('gmc_machine_monitor_mokelog',$date)) {
                ajax_return(array('error' => '0', 'errortip' => "记录成功", 'result' => array()));
            } else {
                ajax_return(array('error' => '1', 'errortip' => "记录失败", 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }
    //考勤机-注册设备 -- 建波帮写 --------- 源新坦达
    function registerMachineApi(){
        $request = Input('post.', '', 'trim,addslashes');
        $request['isEncryption'] = '9';
        $request['isOneHour'] = '1';
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            if(!isset($request['school_branch']) || $request['school_branch']==''){
                ajax_return(array('error' => '1', 'errortip' => '校区代码必须传值', 'result' => array()));
            }else{
                $schoolOne=$this->DataControl->getFieldOne("smc_school","company_id,school_id","school_branch='{$request['school_branch']}'");
                if(!$schoolOne){
                    ajax_return(array('error' => '1', 'errortip' => '校区代码不存在', 'result' => array()));
                }
            }
            if(!isset($request['machine_name']) || $request['machine_name']==''){
                ajax_return(array('error' => '1', 'errortip' => '考勤机昵称必须传值', 'result' => array()));
            }
            if(!isset($request['machine_code']) || $request['machine_code']==''){
                ajax_return(array('error' => '1', 'errortip' => '机器码必须传值', 'result' => array()));
            }
            $machineOne=$this->DataControl->getFieldOne("gmc_machine_monitor","machine_id","machine_brand='嘀拍' and machine_code='{$request['machine_code']}'");
            if($machineOne){
                $date =array();
                $date['company_id'] = $schoolOne['company_id'];
                $date['school_id'] = $schoolOne['school_id'];
                if($request['machine_name']) {
                    $date['machine_name'] = $request['machine_name'];
                }
                $date['machine_brand'] = '嘀拍';
                $date['machine_code'] = $request['machine_code'];
                if($request['machine_lanip']) {
                    $date['machine_lanip'] = $request['machine_lanip'];
                }
                if($request['machine_mac']) {
                    $date['machine_mac'] = $request['machine_mac'];
                }
                if($request['machine_firmware']) {
                    $date['machine_firmware'] = $request['machine_firmware'];
                }
                $date['machine_state'] = 1;
                $date['machine_updatetime'] = time();
                if ($this->DataControl->updateData('gmc_machine_monitor', "machine_id='{$machineOne['machine_id']}'", $date) ) {
                    ajax_return(array('error' => '0', 'errortip' => "更新成功", 'result' => array('machine_id'=>$machineOne['machine_id'])));
                } else {
                    ajax_return(array('error' => '1', 'errortip' => "更新失败", 'result' => array()));
                }
            }else{
                $date =array();
                $date['company_id'] = $schoolOne['company_id'];
                $date['school_id'] = $schoolOne['school_id'];
                $date['machine_name'] = $request['machine_name'];
                $date['machine_brand'] = '嘀拍';
                $date['machine_code'] = $request['machine_code'];
                $date['machine_lanip'] = $request['machine_lanip'];
                $date['machine_mac'] = $request['machine_mac'];
                $date['machine_firmware'] = $request['machine_firmware'];
                $date['machine_state'] = 1;
                $date['machine_creattime'] = time();
                $date['machine_updatetime'] = time();
                if ($machine_id=$this->DataControl->insertData('gmc_machine_monitor',$date)) {
                    ajax_return(array('error' => '0', 'errortip' => "注册成功", 'result' => array('machine_id'=>$machine_id)));
                } else {
                    ajax_return(array('error' => '1', 'errortip' => "注册失败", 'result' => array()));
                }
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }
    //上传图片 -- 建波帮写 --------- 源新坦达
    function OssFileupNewView()
    {
//        $data = array();
//        $data['server'] = json_encode($_SERVER);
//        $data['data'] = json_encode($_REQUEST);
//        $data['addtime'] = time();
//        $this->DataControl->insertData('linshi_shangchuan',$data);

        $request = Input('request.', '', 'trim,addslashes');
        $request['isEncryption'] = '9';
        $request['isOneHour'] = '1';
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            if (! empty ( $_FILES ["ossfile"] )) {

                if(!isset($request['type']) || $request['type']==''){
                    ajax_return(array('error' => '1','errortip' => "请选择上传类型!","result"=>array()),$request['language_type']);
                }

                if($request['type']==0){
                    $dir=  'facetolist/backimg/';
                }else{
                    $dir=  'facetolist/faceimg/';
                }
                $dir.=date("Ymd").'/';

                $imglink = UpOssFile($_FILES,$dir);
                if($imglink){
                    $result = array();
                    $result['picture_imgurl'] = $imglink;
                    $result['picture_thumburl'] = $imglink;
                    $res = array('error' => '0','errortip' => "图片上传成功!","result"=>$result);
                    ajax_return($res,$request['language_type']);
                }else{
                    $res = array('error' => '1','errortip' => "图片上传失败!","result"=>array());
                    ajax_return($res,$request['language_type']);
                }
            }else{
                $res = array('error' => '1','errortip' => "您未选择任何图片上传!","result"=>array());
                ajax_return($res,$request['language_type']);
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //针对 嘀拍（高总） 的返回数据 --- 抓拍到的数据 -- 20250401
    function getGetSchoolSnapFaceApi(){
        $request = Input('get.', '', 'trim,addslashes');
        $request['isEncryption'] = '9';
        $request['isOneHour'] = '1';
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            if($request['school_branch'] != '01437'){//上海延吉校 01403  江桥万达校 01453  复地活力城 01437
                ajax_return(array('error' => '1', 'errortip' => '学校编号不在规定范围内', 'result' => array()));
            }
            if($request['school_branch'] == '') {
                ajax_return(array('error' => '1', 'errortip' => '学校编号不能为空', 'result' => array()));
            }
            $shijiancha = abs($request['endtime']) - abs($request['starttime']);
            if($shijiancha > '86400' || $shijiancha < '1') {
                ajax_return(array('error' => '1', 'errortip' => '时间差不能大于一天', 'result' => array()));
            }

            $schoolOne = $this->DataControl->selectOne(" select school_id,school_branch from smc_school where company_id = '8888' and school_branch = '{$request['school_branch']}' and school_isclose = '0' and school_istest = '0'  ");
            if(!$schoolOne){
                ajax_return(array('error' => '1', 'errortip' => '学校信息不存在', 'result' => (object)array()));
            }

            $datawhere = " a.school_id = '{$schoolOne['school_id']}' and a.school_id > 1  ";
            $datawheretwo = " a.school_id = '{$schoolOne['school_id']}' and a.school_id > 1  ";
            if(isset($request['starttime']) && $request['starttime'] != ''){
                $datawhere .= " and a.identifylog_clocktime > '{$request['starttime']}' ";
                $datawheretwo .= " and a.mokelog_clocktime > '{$request['starttime']}' ";
            }
            if(isset($request['endtime']) && $request['endtime'] != ''){
                $datawhere .= " and a.identifylog_clocktime < '{$request['endtime']}' ";
                $datawheretwo .= " and a.mokelog_clocktime < '{$request['endtime']}' ";
            }

            $sqlOne = "SELECT a.school_id,a.machine_code,a.identifylog_img as img,a.identifylog_clocktime as clocktime
                FROM gmc_machine_monitor_identifylog as a 
                WHERE {$datawhere}  ";
            $stuList = $this->DataControl->selectClear($sqlOne);

            $sqlTwo = "SELECT a.school_id,a.machine_code,a.mokelog_img as img,a.mokelog_clocktime as clocktime
                FROM gmc_machine_monitor_mokelog as a 
                WHERE {$datawheretwo}  ";
            $mkList = $this->DataControl->selectClear($sqlTwo);

            $dataList = array();
            $dataList = array_merge($stuList,$mkList);

            $field = [
                "school_id"=>"学校ID",
                "machine_code"=>"设备ID",
                "img"=>"抓拍的照片",
                "clocktime"=>"抓拍的照片时间",
            ];

            $result = array();
            $result["field"] = $field;
            $result['list'] =$dataList?$dataList:array();
            if ($dataList) {
                ajax_return(array('error' => '0', 'errortip' => "抓拍照片获取成功", 'result' => $result));
            } else {
                ajax_return(array('error' => '1', 'errortip' => "抓拍照片获取失败", 'result' => (object)array()));
            }
        }else{
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }
    //针对 嘀拍（高总） 的返回数据 --- 抓拍到的背景图 -- 20250401
    function getGetSchoolBackimgApi(){
        $request = Input('get.', '', 'trim,addslashes');
        $request['isEncryption'] = '9';
        $request['isOneHour'] = '1';
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            if($request['school_branch'] != '01437'){//上海延吉校 01403  江桥万达校 01453  复地活力城 01437
                ajax_return(array('error' => '1', 'errortip' => '学校编号不在规定范围内', 'result' => array()));
            }
            if($request['school_branch'] == '') {
                ajax_return(array('error' => '1', 'errortip' => '学校编号不能为空', 'result' => array()));
            }
            $shijiancha = abs($request['endtime']) - abs($request['starttime']);
            if($shijiancha > '86400' || $shijiancha < '1') {
                ajax_return(array('error' => '1', 'errortip' => '时间差不能大于一天', 'result' => array()));
            }

            $schoolOne = $this->DataControl->selectOne(" select school_id,school_branch from smc_school where company_id = '8888' and school_branch = '{$request['school_branch']}' and school_isclose = '0' and school_istest = '0'  ");
            if(!$schoolOne){
                ajax_return(array('error' => '1', 'errortip' => '学校信息不存在', 'result' => (object)array()));
            }

            $datawhere = " a.school_id = '{$schoolOne['school_id']}' and a.school_id > 1  ";
            if(isset($request['starttime']) && $request['starttime'] != ''){
                $datawhere .= " and a.backimagelog_clocktime > '{$request['starttime']}' ";
            }
            if(isset($request['endtime']) && $request['endtime'] != ''){
                $datawhere .= " and a.backimagelog_clocktime < '{$request['endtime']}' ";
            }

            $sqlOne = "SELECT a.school_id,a.machine_code,a.backimagelog_img as img,a.backimagelog_clocktime as clocktime
                FROM gmc_machine_monitor_backimagelog as a 
                WHERE {$datawhere}  ";
            $dataList = $this->DataControl->selectClear($sqlOne);

            $field = [
                "school_id"=>"学校ID",
                "machine_code"=>"设备ID",
                "img"=>"抓拍的背景照片",
                "clocktime"=>"抓拍的时间",
            ];

            $result = array();
            $result["field"] = $field;
            $result['list'] =$dataList?$dataList:array();
            if ($dataList) {
                ajax_return(array('error' => '0', 'errortip' => "抓拍背景照片获取成功", 'result' => $result));
            } else {
                ajax_return(array('error' => '1', 'errortip' => "抓拍背景照片获取失败", 'result' => (object)array()));
            }
        }else{
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //针对 嘀拍（高总） 的返回数据 --- 抓拍到的陌客 -- 20250401
    function getGetSchoolMokeimgApi(){
        $request = Input('get.', '', 'trim,addslashes');
        $request['isEncryption'] = '9';
        $request['isOneHour'] = '1';
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            if($request['school_branch'] != '01437'){//上海延吉校 01403  江桥万达校 01453  复地活力城 01437
                ajax_return(array('error' => '1', 'errortip' => '学校编号不在规定范围内', 'result' => array()));
            }
            if($request['school_branch'] == '') {
                ajax_return(array('error' => '1', 'errortip' => '学校编号不能为空', 'result' => array()));
            }
            $shijiancha = abs($request['endtime']) - abs($request['starttime']);
            if($shijiancha > '86400' || $shijiancha < '1') {
                ajax_return(array('error' => '1', 'errortip' => '时间差不能大于一天', 'result' => array()));
            }

            $schoolOne = $this->DataControl->selectOne(" select school_id,school_branch from smc_school where company_id = '8888' and school_branch = '{$request['school_branch']}' and school_isclose = '0' and school_istest = '0'  ");
            if(!$schoolOne){
                ajax_return(array('error' => '1', 'errortip' => '学校信息不存在', 'result' => (object)array()));
            }

            $datawhere = " a.school_id = '{$schoolOne['school_id']}' and a.school_id > 1  ";
            if(isset($request['starttime']) && $request['starttime'] != ''){
                $datawhere .= " and a.mokelog_clocktime > '{$request['starttime']}' ";
            }
            if(isset($request['endtime']) && $request['endtime'] != ''){
                $datawhere .= " and a.mokelog_clocktime < '{$request['endtime']}' ";
            }

            $sqlOne = "SELECT a.school_id,a.machine_code,a.mokelog_img as img,a.mokelog_clocktime as clocktime
                FROM gmc_machine_monitor_mokelog as a 
                WHERE {$datawhere}  ";
            $dataList = $this->DataControl->selectClear($sqlOne);

            $field = [
                "school_id"=>"学校ID",
                "machine_code"=>"设备ID",
                "img"=>"抓拍的陌客照片",
                "clocktime"=>"抓拍的时间",
            ];

            $result = array();
            $result["field"] = $field;
            $result['list'] =$dataList?$dataList:array();
            if ($dataList) {
                ajax_return(array('error' => '0', 'errortip' => "抓拍陌客照片获取成功", 'result' => $result));
            } else {
                ajax_return(array('error' => '1', 'errortip' => "抓拍陌客照片获取失败", 'result' => (object)array()));
            }
        }else{
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }
}