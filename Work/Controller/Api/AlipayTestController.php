<?php

namespace Work\Controller\Api;
require_once BASEDIR."/Core/Tools/aop/AopClient.php";
require_once BASEDIR."/Core/Tools/aop/request/AlipayUserInfoShareRequest.php";
require_once BASEDIR."/Core/Tools/aop/AopCertClient.php";
require_once BASEDIR."/Core/Tools/aop/AopCertification.php";
require_once BASEDIR."/Core/Tools/aop/AlipayConfig.php";
require_once BASEDIR."/Core/Tools/aop/request/AlipaySystemOauthTokenRequest.php";



class AlipayTestController extends viewTpl
{
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $stafferOne = array();

    // 支付宝配置常量
    private $alipayConfig = array(
        'app_id' => '2021005166665258',
        'gateway_url' => 'https://openapi.alipay.com/gateway.do',
        'private_key' => 'MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQChOly4k0HVryYRC8zKE0yB5iyFUWOrugPTK7KITzguhYLaST6FgeC5Rq2w5VKVxIo+AeHYK8w31achPamw/hFsF1qYJqUCUrvxr2VeNUdECq5Qd5YFVQl7vN3IcicTUobiuQh8fU9t92wV/zaVSGrcLXxQutCAs70sjL+vzWl3bfihmdWSLyCw0qiUQe18Swr89hMF71/DZvLND75UBMWWQFQSp2Vke/Tf39qPnVX9nTQ2rb+dO6uUaQqor6KPNDmQJv14oQob6eN+boFQfRYSAsIJp+Nszv2iftJ+MQO/0eH90xEJGgq/j9XTsfLaEQNn0KNkHZ5S8pFntXCyOJXJAgMBAAECggEBAJSwVkKCy/xdgyXFVCAonC5QME7jOKBu9IgTjJrZNF7zEjJtyhMmLDGyVQ0G8Jrenv+AjN/nxHbZu0CUtaJdLYQaJZqcSSFTW7EQqNOxHwBJOWlMYDaarhxHmeH6JL9VJMCDFKh6iH5J6IyAyb3zA1n7OOLa5t9K4/Pp+f4P2mwgq7RPyAL5dJJTQn1r+YXzGZNoLSwXRVkORornuDxMQDNt2ZIPK6giuBNJb25y4uTFTjK53Jvq8VrLMG5huoTaGCJaFAJtd1x0NV6lZgQCncG7QdU808gHWPugvpiM1wRcJIBBW9JXBBRYA4x28Xt5cN4aIls5qWSQXZkib/LJIYECgYEA7wGwaNYuJ70aC+caf5Un1t6qLLBPHIVqk4tzMbkdSD3zqtRyZ29msFzr+AB+5TeEq4IbVxf0bpeF+eiNczl0wrOIh2QbG04W0SAxRFnJn6zofpwLJnqJNz2IzPP+D0hhjpyl5hD1rTFBcpJX0sgfaugRXgj2wPve9t+4nJrlTy0CgYEArLD5mneeMzfL5+6GPlqy+p9BOTuMvSiOJXpGOzJZuvLsnDtKLgJOF3QAyJqP6klMD25uFifta8rai/L8TD6nHSZX0Xt+MxLmjD1koGA3O7AejTouROfbTfR9aoLdiHhxIqJynVCjIqZY28xwVBtinf2D1cAlQKFYPsPCpMQIIo0CgYEAzbMSdOgBCUqDJENeVve0NgWlSJjlPZLFyv51whGj1g0brabyX3tNUfRnUiZ7ECPcFw//H9IKGTRnplIzizuj9f42PvA1NCbdFFc0j7MHCFn0LyN1pURI3DYlit9jhRuyet7vGTb3enut7EFvPIXoJEZezCPC2wzrNGYPqRjWynECgYBxEkOsh0v3Xz5Ms1oEs+BixymurrYdGNPR6DjaA8LK7MpOFWghrfKx2ou0zbcTgUCjDfxY13XWUXE9lzDpP1Cufm+bww9Do8l+46fidn1TwKKQeBrZlTJ73WzM87jX5Xy2X8VKCgV255JhAIBAIdo1dkv/BhueMMaV+hmVRqHI9QKBgEjGFDvb6cE2V5jjvTi81wyEGnakKn+vcdik/iJK8c92CSbWHzfaBzwpsewaWdDXqnNGcdkZ/8gka+jKumnQQXxIFUD1ygiALP0+rD5IggM7VhBq18whMMp10/L7hZsork4TqO4iIzVbZju8kd8OJTZgKcq5g0MzT19ggTJqZ0Ip',
        'public_key' => 'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAt7KKXdlbAn3an/2/K1kKh548cnHBa2KslmHPz5LGEHx8kMLggi4JM6mUg193GSYmbl/QOSoQkJNhYeJbZcSgYESZwcMTe1/XY1NIcwKjpeWEl96IWHFzzT9hkRdSKmt4eJsbpe+GLkZLVxdqfgeYyNtxqSc45lQ8HL2BwqdGR0LW8yp3f1ISG7ZIAC69bJvqGm506nObJ0EeZuHPI9vf0hV2VS2enGWQNW+PF7X/C58Awdr1Z1iFkCY+WlILYrgBSSc0Oni21d+peLKLGmp4xXLSvyJLod7oftc7DT4rIgKzkJ6AEFFMdeIw0MbXMjEUYaURHu2Elry0KxFDX7PX8QIDAQAB',
        'api_version' => '1.0',
        'sign_type' => 'RSA2',
        'charset' => 'UTF-8',
        'format' => 'json'
    );

    function __construct()
    {
        parent::__construct();
    }

    /**
     * 创建支付宝客户端
     * @return \AopClient
     */
    private function createAlipayClient() {
        $alipayClient = new \AopClient();
        $alipayClient->gatewayUrl = $this->alipayConfig['gateway_url'];
        $alipayClient->appId = $this->alipayConfig['app_id'];
        $alipayClient->rsaPrivateKey = $this->alipayConfig['private_key'];
        $alipayClient->alipayrsaPublicKey = $this->alipayConfig['public_key'];
        $alipayClient->apiVersion = $this->alipayConfig['api_version'];
        $alipayClient->signType = $this->alipayConfig['sign_type'];
        $alipayClient->postCharset = $this->alipayConfig['charset'];
        $alipayClient->format = $this->alipayConfig['format'];
        
        return $alipayClient;
    }

    /**
     * 统一错误处理
     * @param object $response API响应对象
     * @return string 格式化的错误信息
     */
    private function formatApiError($response) {
        $error_msg = isset($response->msg) ? $response->msg : '未知错误';
        $error_code = isset($response->code) ? $response->code : '未知错误码';
        $sub_code = isset($response->sub_code) ? $response->sub_code : '';
        $sub_msg = isset($response->sub_msg) ? $response->sub_msg : '';
        
        $error_detail = "错误码: {$error_code}, 错误信息: {$error_msg}";
        if (!empty($sub_code)) {
            $error_detail .= ", 子错误码: {$sub_code}";
        }
        if (!empty($sub_msg)) {
            $error_detail .= ", 子错误信息: {$sub_msg}";
        }
        
        return $error_detail;
    }

    /**
     * 获取用户ID优先级检查
     * @param object $response 支付宝响应对象
     * @return string 用户ID
     */
    private function extractUserId($response) {
        // 支付宝可能返回不同的用户ID字段，按优先级检查
        if (!empty($response->user_id)) {
            return $response->user_id;
        } elseif (!empty($response->open_id)) {
            return $response->open_id;
        } elseif (!empty($response->alipay_user_id)) {
            return $response->alipay_user_id;
        }
        return '';
    }

    /**
     * 构建成功响应结果
     * @param array $userInfoResult 用户信息结果
     * @param object $tokenResponse 令牌响应
     * @param string $fallbackBuyerId 备用用户ID
     * @return array 响应数组
     */
    private function buildSuccessResponse($userInfoResult, $tokenResponse, $fallbackBuyerId = '') {
        if ($userInfoResult['success']) {
            return array(
                'error' => 0, 
                'errortip' => '获取用户ID成功', 
                'result' => array(
                    'buyer_id' => $userInfoResult['user_id'], // 从用户信息接口获取的user_id
                    'open_id' => $userInfoResult['open_id'],   // 从用户信息接口获取的open_id
                    'access_token' => $tokenResponse->access_token,
                    'expires_in' => isset($tokenResponse->expires_in) ? $tokenResponse->expires_in : '',
                    'refresh_token' => isset($tokenResponse->refresh_token) ? $tokenResponse->refresh_token : '',
                    'auth_start' => isset($tokenResponse->auth_start) ? $tokenResponse->auth_start : '',
                    'user_info' => $userInfoResult // 完整的用户信息
                )
            );
        } else {
            // 如果获取用户信息失败，使用原来的逻辑作为兜底
            return array(
                'error' => 0, 
                'errortip' => '获取基础用户ID成功（详细信息获取失败）', 
                'result' => array(
                    'buyer_id' => $fallbackBuyerId,
                    'open_id' => isset($tokenResponse->open_id) ? $tokenResponse->open_id : '',
                    'access_token' => $tokenResponse->access_token,
                    'expires_in' => isset($tokenResponse->expires_in) ? $tokenResponse->expires_in : '',
                    'refresh_token' => isset($tokenResponse->refresh_token) ? $tokenResponse->refresh_token : '',
                    'auth_start' => isset($tokenResponse->auth_start) ? $tokenResponse->auth_start : '',
                    'user_info_error' => $userInfoResult // 包含错误信息
                )
            );
        }
    }

    function getAlipayUseridView(){

        $requestData = Input('get.','','trim,addslashes');
        
        // 检查是否有auth_code参数（从回调中获取）
        if (!empty($requestData['auth_code'])) {
            // 使用auth_code获取用户ID
            return $this->exchangeAuthCodeForUserId($requestData['auth_code'], $requestData['language_type']);
        }
        
        // 如果没有auth_code，返回授权链接
        return $this->buildAuthorizationUrl($requestData['language_type']);
    }
    
    /**
     * 构建支付宝授权链接
     * 根据支付宝开放平台文档实现
     * @link https://opendocs.alipay.com/support/01raya
     */
    private function buildAuthorizationUrl($language_type = ''){
        
        // 使用统一配置
        $appId = $this->alipayConfig['app_id'];
        $scope = 'auth_base'; // 授权范围，固定为auth_base获取用户ID
        // 动态构建回调地址，指向当前接口
        $currentUrl = 'http://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'];
        $redirectUri = urlencode($currentUrl); // 回调到当前接口
        
        // 构建授权URL
        $authUrl = "https://openauth.alipay.com/oauth2/publicAppAuthorize.htm?app_id={$appId}&scope={$scope}&redirect_uri={$redirectUri}";
        
        $res = array(
            'error' => 0, 
            'errortip' => '请访问授权链接',
            'result' => array(
                'auth_url' => $authUrl,
                'tip' => '请用户访问此链接进行授权，授权后会跳转到回调页面并携带auth_code参数'
            )
        );
        
        ajax_return($res, $language_type);
    }
    
    /**
     * 使用auth_code交换用户ID
     * 根据支付宝开放平台文档的alipay.system.oauth.token接口
     */
    private function exchangeAuthCodeForUserId($auth_code, $language_type = ''){
        
        if(empty($auth_code)){
            $res = array('error' => 1, 'errortip' => '授权码不能为空', 'result' => array());
            ajax_return($res, $language_type);
            return;
        }
        
        // 初始化结果变量
        $res = array('error' => 1, 'errortip' => '未知错误', 'result' => array());
        
        // 通过支付宝官方SDK获取用户信息
        try {
            // 验证授权码格式
            if (strlen($auth_code) < 10) {
                $res = array('error' => 1, 'errortip' => '授权码格式不正确', 'result' => array());
                ajax_return($res, $language_type);
                return;
            }
            
            // 使用统一的客户端创建方法
            $alipayClient = $this->createAlipayClient();
            
            // 构造获取Token的请求
            $oauthRequest = new \AlipaySystemOauthTokenRequest();
            $oauthRequest->setCode($auth_code);
            $oauthRequest->setGrantType("authorization_code");

            // 执行API调用
            $responseResult = $alipayClient->execute($oauthRequest);
            $responseApiName = str_replace(".","_",$oauthRequest->getApiMethodName())."_response";
            $response = $responseResult->$responseApiName;
            
            // 检查API调用是否成功 - 根据实际响应判断
            if (!empty($response->access_token)) {
                // 成功获取到access_token，提取用户ID作为备用
                $buyer_id = $this->extractUserId($response);
                
                // 使用 access_token 调用 alipay.user.info.share 获取详细用户信息
                $userInfoResult = $this->callUserInfoShare($response->access_token);
                
                // 无论成功失败都使用统一的响应构建方法
                $res = $this->buildSuccessResponse($userInfoResult, $response, $buyer_id);
            } elseif (!empty($response->code)) {
                // 有错误码的情况 - API调用失败
                $error_detail = $this->formatApiError($response);
                $res = array('error' => 1, 'errortip' => '获取Token失败：' . $error_detail, 'result' => array());
            } else {
                // 既没有access_token也没有错误码，可能是响应格式异常
                $res = array(
                    'error' => 1, 
                    'errortip' => '支付宝API响应格式异常', 
                    'result' => array()
                );
            }

        } catch(\Exception $e){
            $res = array('error' => 1, 'errortip' => '接口调用异常：' . $e->getMessage(), 'result' => array());
        }
        
        ajax_return($res, $language_type);
    }

    /**
     * 调用 alipay.user.info.share 接口获取用户详细信息
     * @param string $access_token 访问令牌
     * @return array 用户信息数组
     */
    private function callUserInfoShare($access_token) {
        
        try {
            // 使用统一的客户端创建方法
            $alipayClient = $this->createAlipayClient();
            
            // 构造用户信息请求
            $userInfoRequest = new \AlipayUserInfoShareRequest();
            
            // 执行API调用
            $userInfoResult = $alipayClient->execute($userInfoRequest, $access_token);
            $responseApiName = str_replace(".", "_", $userInfoRequest->getApiMethodName()) . "_response";
            $userInfoResponse = $userInfoResult->$responseApiName;
            
            // 检查API调用是否成功
            if (!empty($userInfoResponse->code) && $userInfoResponse->code == '10000') {
                // 成功获取用户信息
                return array(
                    'success' => true,
                    'user_id' => isset($userInfoResponse->user_id) ? $userInfoResponse->user_id : '',
                    'open_id' => isset($userInfoResponse->open_id) ? $userInfoResponse->open_id : ''
                );
            } else {
                // API调用失败
                $error_detail = $this->formatApiError($userInfoResponse);
                
                return array(
                    'success' => false,
                    'error_detail' => $error_detail
                );
            }
            
        } catch(\Exception $e) {
            return array(
                'success' => false,
                'error_msg' => '获取用户信息异常：' . $e->getMessage(),
                'exception' => $e->getMessage()
            );
        }
    }

    /**
     * 测试 alipay.user.info.share 接口 - 用于调试用户信息获取
     */
    function testUserInfoView(){
        $requestData = Input('get.','','trim,addslashes');
        
        // 获取access_token参数
        $access_token = $requestData['access_token'];
        
        if(empty($access_token)){
            $res = array('error' => 1, 'errortip' => 'access_token不能为空', 'result' => array());
            ajax_return($res, $requestData['language_type']);
            return;
        }
        
        // 调用用户信息接口
        $userInfoResult = $this->callUserInfoShare($access_token);
        
        if ($userInfoResult['success']) {
            $res = array(
                'error' => 0,
                'errortip' => '获取用户信息成功',
                'result' => $userInfoResult
            );
        } else {
            $res = array(
                'error' => 1,
                'errortip' => '获取用户信息失败',
                'result' => $userInfoResult
            );
        }
        
        ajax_return($res, $requestData['language_type']);
    }
}