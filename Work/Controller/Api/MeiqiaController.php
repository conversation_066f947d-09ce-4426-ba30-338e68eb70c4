<?php

namespace Work\Controller\Api;

class MeiqiaController extends viewTpl
{
    public $u;
    public $t;
    public $c;
    public $visitType = "api";

    //美洽 ID
    const enterpriseId = '204107';
    //美洽 key:conversations
    const COappId = 'd6eb3c35162582ca212015f3cabb8c83';
    const COsign = '9c2fd32b019c283b79945f3fdabc3e83';
    //美洽 key:clients
    const CLappId = 'defaeadbde0d237e7c841d7c5d1cced6';
    const CLsign = '28f36f606b2eed7dd257b9a2ba57888d';

    //预加载处理类
    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
    }

    //待分配名单的微信提醒
    function getV1ConversationsListView()
    {
        $request = Input('get.','','trim,addslashes');
//        $starttm = "2020-09-29+16:44:00";
//        $endtm = "2020-09-29+16:46:00";
        $starttm = date("Y-m-d+H:i:s",time()-60);
        $endtm = date("Y-m-d+H:i:s",time());
        $url = "https://api.meiqia.com/v1/conversations?conv_start_from_tm={$starttm}&conv_start_to_tm={$endtm}&offset=0&limit=20&app_id=d6eb3c35162582ca212015f3cabb8c83&sign=9c2fd32b019c283b79945f3fdabc3e83&enterprise_id=204107";
        $options=array(
            "ssl"=>array(
                "verify_peer"=>false,
                "verify_peer_name"=>false,
            ),
        );
        $datajson = file_get_contents($url,false,stream_context_create($options));
        $dataArray = json_decode($datajson, true);
//        print_r($dataArray);die;
        if(is_array($dataArray['result'])){
            foreach ($dataArray['result'] as $dataVar){
                if($dataVar['conv_leads']){
                    $mobileslist = explode(',',$dataVar['conv_leads']);
//                    $page_from_url = $dataVar['page_from_url'];//来路页
//                    $page_land_url = $dataVar['page_land_url'];//着陆页
//                    $page_land_title = $dataVar['page_land_title'];//着陆页标题
                    $page_conv_url = $dataVar['page_conv_url'];//对话页
                    $page_conv_title = $dataVar['page_conv_title'];//对话页标题
                    $search_engine_name = $dataVar['search_engine_name'];//搜索引擎名称
                    $search_engine_kw = $dataVar['search_engine_kw'];//搜索关键字
                    $visitor_location = $dataVar['visitor_location'];//访客位置

                    $chantitle = explode('channelname=',$page_conv_url);
                    if(is_array($mobileslist)) {
                         foreach ($mobileslist as $mobilesvar){
                             if(preg_match("/^13(\d{9})$|^15(\d{9})$|^16(\d{9})$|^14(\d{9})$|^17(\d{9})$|^18(\d{9})$|^19(\d{9})$/",trim($mobilesvar))){
//                                 echo $mobilesvar;
                                 $parameter = array();
                                 $parameter['client_frompage'] = "百度推广美洽咨询API传值:".$page_conv_title;
                                 $parameter['client_tag'] = $page_conv_title;
                                 if($search_engine_name == ''){
                                    $parameter['client_source'] = "官网";
                                    $parameter['channel_name'] = "官网自然流量";
                                 }else{
                                    if($chantitle[1]){
                                        $parameter['client_source'] = "搜索引擎";
                                        $parameter['channel_name'] = $chantitle[1]?$chantitle[1]:'百度竞价';
                                    }else{
                                        if($search_engine_name == 'baidu_bcp' or $search_engine_name == 'baidu'){
                                            $parameter['client_source'] = "搜索引擎";
                                            $parameter['channel_name'] = '百度竞价';
                                        }elseif($search_engine_name == 'weixin'){
                                            $parameter['client_source'] = "微信";
                                            $parameter['channel_name'] = '各校公众号';
                                        }
                                    }
                                 }
//                                 $parameter['client_source'] = "搜索引擎";
//                                 $parameter['channel_name'] = $chantitle[1]?$chantitle[1]:'百度竞价';
                                 $parameter['client_remark'] = "百度推广美洽咨询API传值:".$search_engine_name."（".$search_engine_kw."）";
                                 $parameter['client_patriarchname'] = '匿名';
                                 $parameter['client_cnname'] = "美洽咨询";
                                 $parameter['client_mobile'] = $mobilesvar;
                                 $parameter['client_age'] = '';
                                 $parameter['company_id'] = '8888';
                                 $parameter['school_branch'] = '';
                                 $parameter['client_address'] = $visitor_location;
                                 request_by_curl("https://crmapi.kedingdang.com/PhoneActivity/addPhoneChannelAction",dataEncode($parameter),"POST",array());
                             }
                         }
                    }
                }
            }
        }
    }






}




