<?php


namespace Work\Controller\Api;


class CalloutController extends viewTpl
{
    public $u;
    public $t;
    public $c;
    public $visitType = "api";


    //预加载处理类
    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
        $this->api = $this->router->getApi();
    }

    //慧捷 电话外呼 通话结束回调接口
    function callBackHuijieAction(){
        header('content-Type:application/json; charset=utf-8');
        //获取 RAW 传递的参数
        $request = file_get_contents("php://input");
        if(!$request){
            $request = Input('get.', '', 'trim,addslashes');
        }
        $Model = new \Model\Crm\PhoneOutboundModel($request);
        $Model->callBackHuijieAction($request);
        if (isset($request['language_type'])){
            $language_type = $request['language_type'];
        }else{
            $language_type = 'zh';
        }
        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $Model->result), $language_type);
    }

    //给 教务系统 提供查询 400电话 回调处理
    function callBackHuijieFourAction() {
        header('content-Type:application/json; charset=utf-8');
        //获取 RAW 传递的参数
        $request = file_get_contents("php://input");

        $Model = new \Model\Crm\PhoneOutboundModel($request);
        $Model->callBackHuijieFourAction($request);
        ajax_return(array('error' => 0, 'errortip' => '获取成功', 'result' => $Model->result));
    }



    //合力 电话外呼 通话结束回调接口 http://api.kcclassin.com/Callout/callBackHeliAction  -- 测试通过
    function callBackHeliAction(){
        //获取 RAW 传递的参数
        $request = file_get_contents("php://input");
        if(!$request){
            $request = Input('get.', '', 'trim,addslashes');
        }
        $Model = new \Model\Crm\PhoneOutboundModel($request);
        $Model->callBackHeliAction($request);
        if($Model->error == '0'){
            echo 'success';
            die;
        }if($Model->error == '2'){
            echo 'success';
            die;
        }else {
            if (isset($request['language_type'])){
                $language_type = $request['language_type'];
            }else{
                $language_type = 'zh';
            }
            ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $Model->result), $language_type);
        }
    }
    //合力 电话外呼 通话接通回调接口 http://api.kcclassin.com/Callout/callBackHeliThroughAction -- 测试通过
    function callBackHeliThroughAction(){
        //获取 RAW 传递的参数
        $request = file_get_contents("php://input");
        if(!$request){
            $request = Input('get.', '', 'trim,addslashes');
        }
        $Model = new \Model\Crm\PhoneOutboundModel($request);
        $Model->callBackHeliThroughAction($request);
        if($Model->error == '0'){
            echo 'success';
            die;
        }else {
            ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $Model->result), $request['language_type']);
        }
    }
    //士决 电话外呼 通话结束回调接口
    function callBackShijueAction(){
        //获取 RAW 传递的参数
        $request = file_get_contents("php://input");
        if(!$request){
            $request = Input('get.', '', 'trim,addslashes');
        }
        $Model = new \Model\Crm\PhoneOutboundModel($request);
        $Model->callBackShijueAction($request);
        if($Model->error == '0'){
            echo 'success';
            die;
        }else {
            ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $Model->result), $request['language_type']);
        }
    }


    //极简互联 电话外呼 通话记录推送
    function callBackJijianHLAction(){
        header('content-Type:application/json; charset=utf-8');
        //获取 RAW 传递的参数
        $request = file_get_contents("php://input");
        $Model = new \Model\Crm\PhoneOutboundModel($request);
        $Model->callBackJijianHLAction($request);
        exit(json_encode(array('code' => "200")));
    }



    /**
     * 更新外播记录
     * author: ling
     * 对应接口文档 0001
     */
    function UpdateOutCallView()
    {
        $request = input("post.", "", "trim");
        $Model = new  \Model\Api\CalloutModel();
        $bool = $Model->UpdateOutCall($request);
        if ($bool) {
            $res = array('error' => '0', 'errortip' => '更新电话外播记录成功', 'result' => true);
        } else {
            $res = array('error' => '1', 'errortip' => '更新电话外播记录失败', 'result' => false);
        }
        ajax_return($res);
    }
    /**
     * 拨号
     * author: ling
     * 对应接口文档 0001
     */
    function sendOutCallView()
    {
        $request = input("get.", "", "trim");
        $Model = new  \Model\Api\CalloutModel();
        $bool = $Model->sendOutCall($request);
        if ($bool) {
            $res = array('error' => '0', 'errortip' => '拨号成功', 'result' => array());
        } else {
            $res = array('error' => '1', 'errortip' => '拨号失败', 'result' => array());
        }
        ajax_return($res);
    }

    function UpdateCallRecordView()
    {
        $dataList = $this->DataControl->selectClear("select outcall_json,outcall_id from gmc_outcall where 1 ");
        if ($dataList) {
            foreach ($dataList as &$value) {
                $outCall = json_decode($value['outcall_json'],1);
                if($outCall){
                    $data = array();
                    $data['outcall_caller'] = $outCall['caller'];
                    $data['outcall_called'] = $outCall['called'];
                    $data['outcall_begin_time'] = $outCall['begin_time'];
                    $data['outcall_end_time'] = $outCall['end_time'];
                    $data['outcall_duration'] = $outCall['duration'];
                    $data['outcall_call_type'] = $outCall['call_type'];
                    $data['outcall_playurl'] ="http://**************/atstar/index.php/cdr-cdrplay/mp3play?path=".$outCall['record_path'];
                    $data['outcall_trunk'] = $outCall['trunk'];
                    $data['outcall_uuid'] = $outCall['uuid'];
                    $data['outcall_sitecode'] = $outCall['site_code'];
                    $data['outcall_feedback_score'] = $outCall['feedback_score'];
                    $data['outcall_hangupcause'] = $outCall['hangupcause'];
                    $data['outcall_ref_id'] = $outCall['ref_id'];
                    $data['outcall_cdr_type'] = $outCall['cdr_type'];
                    $data['outcall_fax_result_code'] = $outCall['fax_result_code'];
                    $data['outcall_fax_file'] = $outCall['fax_file'];
                    $data['outcall_fax_success'] = $outCall['fax_success'];
                    $data['fax_image_pixel_size'] = $outCall['fax_image_pixel_size'];
                    $data['fax_transferred_pages'] = $outCall['fax_transferred_pages'];
                    $data['fax_transfer_rate'] = $outCall['fax_transfer_rate'];
                    $this->DataControl->updateData('gmc_outcall',"outcall_id='{$value['outcall_id']}'",$data);
                }
            }

        }


    }


}