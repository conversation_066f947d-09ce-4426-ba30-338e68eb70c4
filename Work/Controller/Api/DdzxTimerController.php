<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/4/7
 * Time: 18:16
 */

namespace Work\Controller\Api;


class DdzxTimerController extends viewTpl {


    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
        exit;
    }


    //测试集团模板推送
    function testMisSendView(){
        $request = Input('get.', '', 'trim,addslashes');
        if(!isset($request['company_id']) || $request['company_id'] ==''){
            ajax_return(array('error' => 1, 'errortip' => "请传入集团编号!"));
        }
        if(!isset($request['parenter_mobile']) || $request['parenter_mobile'] ==''){
            ajax_return(array('error' => 1, 'errortip' => "请传入手机号码!"));
        }

        $wxchatOne = $this->DataControl->getFieldOne("gmc_company_wxchatnumber"
            ,"wxchatnumber_id","company_id = '{$request['company_id']}' and wxchatnumber_class = '1'");
        if(!$wxchatOne){
            $wxchatOne = $this->DataControl->getFieldOne("gmc_company_wxchatnumber"
                ,"wxchatnumber_id","company_id = '0' and wxchatnumber_class = '1'");
        }

        $parenterOne = $this->DataControl->selectOne("SELECT p.parenter_id,w.parenter_wxtoken FROM smc_parenter_wxchattoken AS w, smc_parenter AS p
WHERE w.parenter_id = p.parenter_id AND w.wxchatnumber_id = '{$wxchatOne['wxchatnumber_id']}' AND p.parenter_mobile = '{$request['parenter_mobile']}'");
        if(!$parenterOne){
            ajax_return(array('error' => 1, 'errortip' => "家长微信未绑定!"));
        }
        $studentOne = $this->DataControl->selectOne("SELECT f.student_id FROM smc_student_family AS f,smc_student AS s
WHERE s.student_id = f.student_id AND s.company_id = '{$request['company_id']}' and f.parenter_id = '{$parenterOne['parenter_id']}' limit 0,1");
        if(!$studentOne){
            ajax_return(array('error' => 1, 'errortip' => "不存在绑定的学生信息!"));
        }

        $isset = $this->DataControl->getFieldOne("gmc_company_masterplate","masterplate_wxid","company_id = '{$request['company_id']}' and masterplate_name = '到校提醒'");
        if($isset){
            $wxid = $isset['masterplate_wxid'];
        }else{
            $masterplate = $this->DataControl->getFieldOne("gmc_company_masterplate","masterplate_wxid","company_id = '0' and masterplate_name = '到校提醒'");
            $wxid = $masterplate['masterplate_wxid'];
        }

        $wxteModel = new \Model\Api\ZxwxChatModel($parenterOne['parenter_id'],$studentOne['student_id']);
        $wxteModel->ToinformTip("您好，学员{$studentOne['student_cnname']}微信激活成功",date("Y-m-d H:i:s"),'测试校区','叮铛助学感谢您的使用','https://scptc.kedingdang.com/home',$wxid);
        ajax_return(array('error' => 0, 'errortip' => "微信通知发送成功!"));
    }

    //学员上课时间前一天16：30推送
    function HourPreTipView(){
        if(date("H") >= 16 && date("H") <= 20){
            $toDay = date("Y-m-d");
            $nextDay = date("Y-m-d",strtotime("+1 day"));
            $trackOne = $this->DataControl->selectOne("
            SELECT
                s.student_id,
                count(h.hour_id) as hournum,
                h.hour_id,
                h.hour_day,
                h.hour_starttime,
                h.hour_endtime,
                st.student_cnname,
                c.class_cnname,
                f.parenter_id,
                co.course_inclasstype
            FROM
                smc_student_study AS s,
                smc_class AS c,
                smc_class_hour AS h,
                smc_student AS st,
                smc_student_family as f,
                smc_parenter_wxchattoken as w ,
                smc_course as co
            WHERE
                s.class_id = c.class_id 
                AND c.class_id = h.class_id 
                AND st.student_id = s.student_id 
                AND f.student_id = s.student_id 
                AND co.course_id = c.course_id 
                AND h.hour_day = '{$nextDay}' 
                AND s.student_id NOT IN ( SELECT l.student_id FROM scptc_wxsend_log AS l WHERE l.log_day = '{$toDay}' AND l.log_type = 'hourpretip' ) 
                AND w.parenter_id = f.parenter_id and w.company_id = c.company_id and w.parenter_wxtoken is NOT NULL 
                AND co.course_inclasstype < '2'
            GROUP BY
                s.student_id,
                h.hour_id 
            ORDER BY
                rand( ) 
                LIMIT 0,1");

            $trackTwo = $this->DataControl->selectOne("
            SELECT
                s.student_id,
                count(h.hour_id) as hournum,
                h.hour_id,
                h.hour_day,
                h.hour_starttime,
                h.hour_endtime,
                st.student_cnname,
                c.class_cnname,
                f.parenter_id,
                co.course_inclasstype
            FROM
                smc_student_study AS s,
                smc_class AS c,
                smc_class_hour AS h,
                smc_student AS st,
                smc_student_family as f,
                smc_parenter_wxchattoken as w ,
                smc_course as co
            WHERE
                s.class_id = c.class_id 
                AND c.class_id = h.class_id 
                AND st.student_id = s.student_id 
                AND f.student_id = s.student_id 
                AND co.course_id = c.course_id 
                AND h.hour_day = '{$nextDay}' 
                AND s.student_id NOT IN ( SELECT l.student_id FROM scptc_wxsend_log AS l WHERE l.log_day = '{$toDay}' AND l.log_type = 'hourpretip' ) 
                AND s.student_id IN ( SELECT b.student_id FROM smc_class_booking AS b WHERE b.hour_id = h.hour_id and b.booking_status = '0') 
                AND w.parenter_id = f.parenter_id and w.company_id = c.company_id and w.parenter_wxtoken is NOT NULL 
                AND co.course_inclasstype = '2'
            GROUP BY
                s.student_id,
                h.hour_id 
            ORDER BY
                rand( ) 
                LIMIT 0,1");
            $numname = array("零","一","两","三","四");
            if($trackOne){
                $course = $this->DataControl->selectOne("select c.course_cnname,c.company_id from smc_course as c left join smc_class as cl on c.course_id = cl.course_id left join smc_class_hour as h on h.class_id = cl.class_id where h.hour_id = '{$trackOne['hour_id']}' limit 0,1");



                $isset = $this->DataControl->getFieldOne("gmc_company_masterplate","masterplate_wxid","company_id = '{$course['company_id']}' and masterplate_name = '上课提醒'");
                if($isset){
                    $wxid = $isset['masterplate_wxid'];
                }else{
                    $masterplate = $this->DataControl->getFieldOne("gmc_company_masterplate","masterplate_wxid","company_id = '0' and masterplate_name = '上课提醒'");
                    $wxid = $masterplate['masterplate_wxid'];
                }

                $time = time();
                $week = date("w", $time);
                $array = ["星期一", "星期二", "星期三", "星期四", "星期五", "星期六","星期天"];
                $a = $trackOne['student_cnname'].'学员您好，您明天有'.$numname[$trackOne['hournum']].'节课，请提前做好上课准备哦~';
                $b = $course['course_cnname'];
                if($trackOne['hournum']>1){
                    $c = "首节课：".date('m月d日', time() + 36400) . ' ' . $trackOne['hour_starttime'] . '-' . $trackOne['hour_endtime'] . '「' . $array[$week] . ' 」';
                }else {
                    $c = date('m月d日', time() + 36400) . ' ' . $trackOne['hour_starttime'] . '-' . $trackOne['hour_endtime'] . '「' . $array[$week] . ' 」';
                }
                $d = '点击这里查看课程详情';
                $e = "https://scptc.kedingdang.com/LookTimetable/timeoutDetail?hour_id={$trackOne['hour_id']}&cid={$course['company_id']}&s_id={$trackOne['student_id']}";
                $wxteModel = new \Model\Api\ZxwxChatModel($trackOne['parenter_id'],$trackOne['student_id']);
                $wxteModel->HourPreTip($a,$b,$c,$d,$e,$wxid);
                ajax_return(array('error' => 0, 'errortip' => "微信通知发送成功!"));
            }elseif($trackTwo){
                $course = $this->DataControl->selectOne("select c.course_cnname,c.company_id from smc_course as c left join smc_class as cl on c.course_id = cl.course_id left join smc_class_hour as h on h.class_id = cl.class_id where h.hour_id = '{$trackTwo['hour_id']}' limit 0,1");

                $isset = $this->DataControl->getFieldOne("gmc_company_masterplate","masterplate_wxid","company_id = '{$course['company_id']}' and masterplate_name = '上课提醒'");
                if($isset){
                    $wxid = $isset['masterplate_wxid'];
                }else{
                    $masterplate = $this->DataControl->getFieldOne("gmc_company_masterplate","masterplate_wxid","company_id = '0' and masterplate_name = '上课提醒'");
                    $wxid = $masterplate['masterplate_wxid'];
                }

                $time = time();
                $week = date("w", $time);
                $array = ["星期一", "星期二", "星期三", "星期四", "星期五", "星期六","星期天"];
                $a = $trackTwo['student_cnname'].'学员您好，您明天有'.$numname[$trackTwo['hournum']].'节课，请提前做好上课准备哦~';
                $b = $course['course_cnname'];
                if($trackTwo['hournum']>1){
                    $c = "首节课：".date('m月d日', time() + 36400) . ' ' . $trackTwo['hour_starttime'] . '-' . $trackTwo['hour_endtime'] . '「' . $array[$week] . ' 」';
                }else {
                    $c = date('m月d日', time() + 36400) . ' ' . $trackTwo['hour_starttime'] . '-' . $trackTwo['hour_endtime'] . '「' . $array[$week] . ' 」';
                }
                $d = '点击这里查看课程详情';
                $e = "https://scptc.kedingdang.com/LookTimetable/timeoutDetail?hour_id={$trackTwo['hour_id']}&cid={$course['company_id']}&s_id={$trackTwo['student_id']}";
                $wxteModel = new \Model\Api\ZxwxChatModel($trackTwo['parenter_id'],$trackTwo['student_id']);
                $wxteModel->HourPreTip($a,$b,$c,$d,$e,$wxid);
                ajax_return(array('error' => 0, 'errortip' => "微信通知发送成功!"));
            }else{
                exit(0);
            }
        }else{
            exit(0);
        }
    }

    //学员上课时间前半小时推送
    function HourDayTipView(){
//        $nextDay = date("Y-m-d",strtotime("+1 day"));
        $toDay = date("Y-m-d");
        $halfHour = date("H:i",strtotime("+30 minutes"));


        $trackOne = $this->DataControl->selectOne("
            SELECT
                s.student_id,
                h.hour_id,
                h.hour_day,
                h.hour_starttime,
                h.hour_endtime,
                st.student_cnname,
                c.class_cnname,
                f.parenter_id
            FROM
                smc_student_study AS s,
                smc_class AS c,
                smc_class_hour AS h,
                smc_student AS st,
                smc_student_family as f,
                smc_parenter_wxchattoken as w,
                smc_course as co
            WHERE
                s.class_id = c.class_id 
                AND c.class_id = h.class_id 
                AND st.student_id = s.student_id 
                AND f.student_id = s.student_id 
                AND co.course_id = c.course_id 
                AND h.hour_day = '{$toDay}' 
                AND h.hour_starttime = '{$halfHour}' 
                AND c.school_id = s.school_id 
                AND s.student_id NOT IN ( SELECT l.student_id FROM scptc_wxsend_log AS l WHERE l.log_day = '{$toDay}' AND l.log_type = 'HourDayTip' )  
                AND w.parenter_id = f.parenter_id and w.company_id = c.company_id and w.parenter_wxtoken is NOT NULL 
                AND co.course_inclasstype < '2'
            GROUP BY
                s.student_id,
                h.hour_id 
            ORDER BY
                rand( ) 
                LIMIT 0,1");



        $trackTwo = $this->DataControl->selectOne("
            SELECT
                s.student_id,
                h.hour_id,
                h.hour_day,
                h.hour_starttime,
                h.hour_endtime,
                st.student_cnname,
                c.class_cnname,
                f.parenter_id
            FROM
                smc_student_study AS s,
                smc_class AS c,
                smc_class_hour AS h,
                smc_student AS st,
                smc_student_family as f,
                smc_parenter_wxchattoken as w,
                smc_course as co
            WHERE
                s.class_id = c.class_id 
                AND c.class_id = h.class_id 
                AND st.student_id = s.student_id 
                AND f.student_id = s.student_id 
                AND co.course_id = c.course_id 
                AND h.hour_day = '{$toDay}' 
                AND h.hour_starttime = '{$halfHour}' 
                AND c.school_id = s.school_id
                AND s.student_id NOT IN ( SELECT l.student_id FROM scptc_wxsend_log AS l WHERE l.log_day = '{$toDay}' AND l.log_type = 'HourDayTip' )              
                AND s.student_id IN ( SELECT b.student_id FROM smc_class_booking AS b WHERE b.hour_id = h.hour_id and b.booking_status = '0') 
                AND w.parenter_id = f.parenter_id and w.company_id = c.company_id and w.parenter_wxtoken is NOT NULL 
                AND co.course_inclasstype = '2'
            GROUP BY
                s.student_id,
                h.hour_id 
            ORDER BY
                rand( ) 
                LIMIT 0,1");

        if($trackOne){
            $course = $this->DataControl->selectOne("select c.course_cnname,c.company_id from smc_course as c left join smc_class as cl on c.course_id = cl.course_id left join smc_class_hour as h on h.class_id = cl.class_id where h.hour_id = '{$trackOne['hour_id']}' limit 0,1");

            $isset = $this->DataControl->getFieldOne("gmc_company_masterplate","masterplate_wxid","company_id = '{$course['company_id']}' and masterplate_name = '上课提醒'");
            if($isset){
                $wxid = $isset['masterplate_wxid'];
            }else{
                $masterplate = $this->DataControl->getFieldOne("gmc_company_masterplate","masterplate_wxid","company_id = '0' and masterplate_name = '上课提醒'");
                $wxid = $masterplate['masterplate_wxid'];
            }

            $time = time();
            $week = date("w", $time);
            $array = ["星期天", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"];
            $a = $trackOne['student_cnname'].'学员您好，您半小时后有一节课即将开课，请做好上课准备哦~';
            $b = $course['course_cnname'];
            $c = date('m月d日', time()).' '.$trackOne['hour_starttime'].'-'.$trackOne['hour_endtime'] . '「' . $array[$week] . ' 」';
            $d = '点击这里查看课程详情';
            $e = "https://scptc.kedingdang.com/LookTimetable/timeoutDetail?hour_id={$trackOne['hour_id']}&cid={$course['company_id']}&s_id={$trackOne['student_id']}";
            $wxteModel = new \Model\Api\ZxwxChatModel($trackOne['parenter_id'],$trackOne['student_id']);
            $wxteModel->HourDayTip($a,$b,$c,$d,$e,$wxid);
            ajax_return(array('error' => 0, 'errortip' => "微信通知发送成功!"));

        }elseif($trackTwo){
            $course = $this->DataControl->selectOne("select c.course_cnname,c.company_id from smc_course as c left join smc_class as cl on c.course_id = cl.course_id left join smc_class_hour as h on h.class_id = cl.class_id where h.hour_id = '{$trackTwo['hour_id']}' limit 0,1");

            $isset = $this->DataControl->getFieldOne("gmc_company_masterplate","masterplate_wxid","company_id = '{$course['company_id']}' and masterplate_name = '上课提醒'");
            if($isset){
                $wxid = $isset['masterplate_wxid'];
            }else{
                $masterplate = $this->DataControl->getFieldOne("gmc_company_masterplate","masterplate_wxid","company_id = '0' and masterplate_name = '上课提醒'");
                $wxid = $masterplate['masterplate_wxid'];
            }

            $time = time();
            $week = date("w", $time);
            $array = ["星期天", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"];
            $a = $trackTwo['student_cnname'].'学员您好，您半小时后有一节课即将开课，请做好上课准备哦~';
            $b = $course['course_cnname'];
            $c = date('m月d日', time()).' '.$trackTwo['hour_starttime'].'-'.$trackTwo['hour_endtime'] . '「' . $array[$week] . ' 」';
            $d = '点击这里查看课程详情';
            $e = "https://scptc.kedingdang.com/LookTimetable/timeoutDetail?hour_id={$trackTwo['hour_id']}&cid={$course['company_id']}&s_id={$trackTwo['student_id']}";
            $wxteModel = new \Model\Api\ZxwxChatModel($trackTwo['parenter_id'],$trackTwo['student_id']);
            $wxteModel->HourDayTip($a,$b,$c,$d,$e,$wxid);
            ajax_return(array('error' => 0, 'errortip' => "微信通知发送成功!"));
        }else{
            exit(0);
        }

    }


}