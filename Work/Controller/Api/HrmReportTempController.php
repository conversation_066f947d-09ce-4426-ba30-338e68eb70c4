<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/5/27
 * Time: 11:56
 */

namespace Work\Controller\Api;


class HrmReportTempController extends viewTpl
{
    public $u;
    public $t;
    public $c;
    public $visitType = "api";


    //预加载处理类
    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
        $this->api = $this->router->getApi();
    }


    function numberReportApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $Model = new \Model\Api\HrmReportTempModel();
        $res = $Model->numberReport($request);
        $field = array();

        $k = 0;
        $field[$k]["fieldstring"] = "str_1";
        $field[$k]["fieldname"] = "在籍学生";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "str_2";
        $field[$k]["fieldname"] = "在读学生";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "str_3";
        $field[$k]["fieldname"] = "延班人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "str_4";
        $field[$k]["fieldname"] = "待入班人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

//        $field[$k]["fieldstring"] = "str_5";
//        $field[$k]["fieldname"] = "新生定金人数";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 0;
//        $k++;

        $field[$k]["fieldstring"] = "str_6";
        $field[$k]["fieldname"] = "非自然月流失人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "str_7";
        $field[$k]["fieldname"] = "班级数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "str_8";
        $field[$k]["fieldname"] = "班平数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "str_9";
        $field[$k]["fieldname"] = "报名/周 ";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "str_10";
        $field[$k]["fieldname"] = "报名/月";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "str_11";
        $field[$k]["fieldname"] = "新生入班/季";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $result = array();
        $result["field"] = $field;
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);

        }
        ajax_return($res, $request['language_type']);
    }












}