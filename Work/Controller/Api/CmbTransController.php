<?php


namespace Work\Controller\Api;


class CmbTransController extends viewTpl
{
    public $u;
    public $t;
    public $c;

    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
    }


    /**
     * 注册机构
     * 需要安排课叮铛做程序
     **/
    function addAgencyApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $companyOne = $this->DataControl->getFieldOne("gmc_company","company_cmbbranch","company_id='{$request['company_id']}'");
        $Model = new \Model\Api\CmbTransModel($companyOne['company_cmbbranch']);
        if($Model->error){
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }else{
            $res = $Model->addAgency($request);
            if ($res) {
                $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $res);
            } else {
                $result["list"] = array();
                $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
            }
        }
        ajax_return($res);
    }

//http://api.kcclassin.com/CmbTrans/recordCmbOrderChargeApi
    /**
     * 学生监管付费、退费总控表  监管金额及监管退费记录
     * 更新评率 ？？？？？
     **/
    function recordCmbOrderChargeApi()
    {
        $Model = new \Model\Api\CmbDataprocessModel();
        $res=$Model->recordCmbOrderCharge();
        if($res>0){

//                    echo '<script language="javascript" type="text/javascript">
//                    var i = 1;
//                    var intervalid;
//                    intervalid = setInterval("fun()", 1000);
//                    function fun() {
//                        if (i == 0) {
//                            window.location.href = "/CmbTrans/recordCmbOrderChargeApi";
//                            clearInterval(intervalid);
//                        }
//                        document.getElementById("mes").innerHTML = i;
//                        i--;
//                    }
//                    </script>
//                    <div id="error">
//                        <p>将在 <span id="mes">5</span> 秒钟后返回首页！</p>
//                    </div> ';


            $res = array('error' => 0, 'errortip' => "更新成功{$res}条", 'result' => array());
        }else{
            $res = array('error' => 1, 'errortip' => '无主体', 'result' => array());
        }
        ajax_return($res);
    }

    //http://api.kcclassin.com/CmbTrans/autoOpenApi
    //批量修改学生账户监管状态
    function autoOpenApi(){
        $request = Input('get.', '', 'trim,addslashes');
        $datawhere= "companies_issupervise=1 and companies_agencyid<>'' and companies_supervisetime>0 and company_id='8888'";
        if(isset($request['companies_agencyid']) && $request['companies_agencyid']!=''){
            $datawhere.=" and companies_agencyid='{$request['companies_agencyid']}'";
        }

        $sql = "select companies_agencyid,companies_signature,companies_secretkey 
                from gmc_code_companies 
                where {$datawhere}";

        $agencyList=$this->DataControl->selectClear($sql);

        if($agencyList){
            $num=1;
            foreach($agencyList as $agencyOne){
                $Model = new \Model\Api\CmbDataprocessModel($agencyOne['companies_agencyid']);
                $n=$Model->autoOpen();
                $num+=$n;
            }
            $res = array('error' => 0, 'errortip' => '更新成功'.$num.'条', 'result' => array());
        }else{
            $res = array('error' => 1, 'errortip' => '无主体', 'result' => array());
        }

        ajax_return($res);

    }


    //http://api.kcclassin.com/CmbTrans/autoConfigOrderApi  -暂不使用
//    function autoConfigOrderApi(){
//        $request = Input('get.', '', 'trim,addslashes');
//        $datawhere= "c.order_pid = o.order_pid AND o.order_status_all = '1' AND o.class_id <> c.class_id AND c.class_id <> 0 AND o.is_errorpaly = 0";
//
//        if(isset($request['student_id']) && $request['student_id']!=''){
//            $datawhere.=" and o.student_id='{$request['student_id']}'";
//        }
//
//        if(isset($request['order_pid']) && $request['order_pid']!=''){
//            $datawhere.=" and o.order_pid='{$request['order_pid']}'";
//        }
//
//
//        $sql = "SELECT o.*,c.class_id as yuan_class_id
//        FROM cmb_trans_order AS o, smc_payfee_order_course AS c WHERE {$datawhere} limit 0,1";
//        $orderList=$this->DataControl->selectClear($sql);
//        if($orderList){
//            $num=1;
//            foreach($orderList as $orderOne){
//                $Model = new \Model\Api\CmbDataprocessModel($orderOne['agency_id']);
//                $n=$Model->autoConfigOrder($orderOne);
//                print_r($orderOne);
//                echo $Model->errortip;
//                echo $Model->oktip;
//                $num+=$n;
//            }
//            $res = array('error' => 0, 'errortip' => '更新成功'.$num.'条', 'result' => array());
//        }else{
//            $res = array('error' => 1, 'errortip' => '无主体', 'result' => array());
//        }
//
//        ajax_return($res);
//
//    }

    //批量确认订单
    //http://api.kcclassin.com/CmbTrans/autoConfigOrderItemApi
    function autoConfigOrderItemApi(){
        $request = Input('get.', '', 'trim,addslashes');
        $datawhere= " a.is_confirm=0 and a.order_type='P'";

        if(isset($request['student_id']) && $request['student_id']!=''){
            $datawhere.=" and a.student_id='{$request['student_id']}'";
        }

        if(isset($request['order_pid']) && $request['order_pid']!=''){
            $datawhere.=" and a.order_pid='{$request['order_pid']}'";
        }


        $sql = "SELECT a.* FROM cmb_trans_order AS a WHERE {$datawhere} order by a.order_date asc limit 0,100";
        $orderList=$this->DataControl->selectClear($sql);

        if($orderList){
            $num=1;
            foreach($orderList as $orderOne){
                $Model = new \Model\Api\CmbDataprocessModel($orderOne['agency_id']);
                $n=$Model->autoConfigOrderItem($orderOne);
                echo $orderOne['order_pid']."处理完毕<br />";
                $num+=$n;
            }
            $res = array('error' => 0, 'errortip' => '更新成功'.$num.'条', 'result' => array());
        }else{
            $res = array('error' => 1, 'errortip' => '无主体', 'result' => array());
        }
        ajax_return($res);

    }

    //同步招行数据  开始

    //根据划拨记录同步招行划拨详情
    //http://api.kcclassin.com/CmbTrans/autoHandleErrorInfoApi
    function autoHandleErrorInfoApi(){
        $request = Input('get.', '', 'trim,addslashes');
        $datawhere= " a.log_url='/pfs/head/addBatchTransfer'";

        $timeOne=$this->DataControl->getFieldOne("cmb_trans_autotime","faillog_time"," 1 ");

        $sql = "SELECT a.log_requet_json,a.agency_id,a.batch_pid,a.log_time,a.log_id
                FROM cmb_trans_log AS a 
                WHERE {$datawhere} and a.log_transerStatus<>0 and (a.log_time>'{$timeOne['faillog_time']}' or (a.log_transerStatus=1 and a.log_updatetime=0))
                order by a.log_time asc
                limit 0,10";
        $logList=$this->DataControl->selectClear($sql);

        if($logList){
            $logtime=$timeOne['faillog_time'];
            foreach($logList as $logOne){
                $Model = new \Model\Api\CmbTransModel($logOne['agency_id']);
                $n=$Model->autoHandleErrorInfo($logOne);

                $data=array();
                $data['log_updatetime']=time();
                $this->DataControl->updateData("cmb_trans_log","log_id='{$logOne['log_id']}'",$data);


                echo $logOne['batch_pid']."处理完毕<br />";
                $logtime=$n;
            }
            if($logtime>$timeOne['faillog_time']){
                $data=array();
                $data['faillog_time']=$logtime;
                $this->DataControl->updateData("cmb_trans_autotime","autotime_id=1",$data);
            }
            echo '<script language="javascript" type="text/javascript">
                    var i = 1;
                    var intervalid;
                    intervalid = setInterval("fun()", 1000);
                    function fun() {
                        if (i == 0) {
                            window.location.href = "/CmbTrans/autoHandleErrorInfoApi";
                            clearInterval(intervalid);
                        }
                        document.getElementById("mes").innerHTML = i;
                        i--;
                    }
                    </script>
                    <div id="error">
                        <p>将在 <span id="mes">5</span> 秒钟后返回首页！</p>
                    </div> ';

            $res = array('error' => 0, 'errortip' => '更新成功', 'result' => array());
        }else{
            $res = array('error' => 1, 'errortip' => '无主体', 'result' => array());
        }
        ajax_return($res);

    }

    //自动修改处理与招行状态不同的划拨
    //http://api.kcclassin.com/CmbTrans/autoSynchroTransApi
    function autoSynchroTransApi(){
        $request = Input('get.', '', 'trim,addslashes');
        $datawhere= "companies_issupervise=1 and companies_agencyid<>'' and companies_supervisetime>0 and company_id='8888'";

        if(isset($request['companies_agencyid']) && $request['companies_agencyid']!=''){
            $datawhere.=" and companies_agencyid='{$request['companies_agencyid']}'";
        }

        $sql = "select companies_agencyid,companies_signature,companies_secretkey 
                from gmc_code_companies 
                where {$datawhere}";

        $agencyList=$this->DataControl->selectClear($sql);

        if($agencyList){
            $num=0;
            foreach($agencyList as $agencyOne){
                $Model = new \Model\Api\CmbTransModel($agencyOne['companies_agencyid']);
                $n=$Model->autoSynchroTrans();
                $num+=$n;
            }

            if($num>0){
                echo '<script language="javascript" type="text/javascript">
                    var i = 1;
                    var intervalid;
                    intervalid = setInterval("fun()", 1000);
                    function fun() {
                        if (i == 0) {
                            window.location.href = "/CmbTrans/autoSynchroTransApi";
                            clearInterval(intervalid);
                        }
                        document.getElementById("mes").innerHTML = i;
                        i--;
                    }
                    </script>
                    <div id="error">
                        <p>将在 <span id="mes">5</span> 秒钟后返回首页！</p>
                    </div> ';
            }

            $res = array('error' => 0, 'errortip' => '更新成功'.$num.'条', 'result' => array());
        }else{
            $res = array('error' => 1, 'errortip' => '无主体', 'result' => array());
        }
        ajax_return($res);

    }

    //根据招行划拨记录 修改本地耗课状态
    //http://api.kcclassin.com/CmbTrans/updateErrorTransApi
    function updateErrorTransApi(){

        $sql = "update
                cmb_trans_mapping_log A
                LEFT JOIN cmb_trans_transfer B ON A.eliminateId=B.hourstudy_id and a.orderNo=b.order_pid
                LEFT JOIN cmb_financial_batchlog C ON C.transferId=A.transferId and c.class_id=b.class_id
                set b.batch_pid=a.transferId,b.subTransferId=c.subTransferId,b.batch_date=c.applyDate,b.transfer_status=2,transfer_updatetime=UNIX_TIMESTAMP(now())
                WHERE A.transferStatus='S'
                and b.transfer_status<>2";

        if($this->DataControl->query($sql)){
            $res = array('error' => 0, 'errortip' => '更新成功', 'result' => array());
        }else{
            $res = array('error' => 1, 'errortip' => '无主体', 'result' => array());
        }
        ajax_return($res);

    }

    //http://api.kcclassin.com/CmbTrans/updatePlanTransInfoApi
    function updatePlanTransInfoApi(){

        $request = Input('get.', '', 'trim,addslashes');
        $datawhere= "companies_issupervise=1 and companies_agencyid<>'' and companies_supervisetime>0 and company_id='8888'";

        if(isset($request['companies_agencyid']) && $request['companies_agencyid']!=''){
            $datawhere.=" and companies_agencyid='{$request['companies_agencyid']}'";
        }

        $sql = "select companies_agencyid,companies_signature,companies_secretkey 
                from gmc_code_companies 
                where {$datawhere}";

        $agencyList=$this->DataControl->selectClear($sql);

        if($agencyList) {
            foreach ($agencyList as $agencyOne) {
                $Model = new \Model\Api\CmbTransModel($agencyOne['companies_agencyid']);
                $Model->updatePlanTransInfo();
            }
        }

        $res = array('error' => 0, 'errortip' => '更新成功', 'result' => array());
        ajax_return($res);

    }


    //根据订单同步记录插入本地订单数据
    //http://api.kcclassin.com/CmbTrans/autoHandleOrderErrorInfoApi
    function autoHandleOrderErrorInfoApi(){
        $request = Input('get.', '', 'trim,addslashes');

        $datawhere= " a.log_url='/pfs/head/addStudentOrder'";

        $timeOne=$this->DataControl->getFieldOne("cmb_trans_autotime","failorderlog_time"," 1 ");

        $sql = "SELECT a.log_requet_json,a.agency_id,a.batch_pid,a.log_time
                FROM cmb_trans_log AS a 
                WHERE {$datawhere} and a.log_time>'{$timeOne['failorderlog_time']}'
                order by a.log_time asc
                limit 0,10";
        $logList=$this->DataControl->selectClear($sql);

        if($logList){
            $logtime=$timeOne['faillog_time'];
            foreach($logList as $logOne){
                $Model = new \Model\Api\CmbTransModel($logOne['agency_id']);
                $n=$Model->autoHandleOrderErrorInfo($logOne);
                echo $logOne['batch_pid']."处理完毕<br />";
                $logtime=$n;
            }
            if($logtime>$timeOne['failorderlog_time']){
                $data=array();
                $data['failorderlog_time']=$logtime;
                $this->DataControl->updateData("cmb_trans_autotime","autotime_id=1",$data);
            }
            echo '<script language="javascript" type="text/javascript">
                    var i = 1;
                    var intervalid;
                    intervalid = setInterval("fun()", 1000);
                    function fun() {
                        if (i == 0) {
                            window.location.href = "/CmbTrans/autoHandleOrderErrorInfoApi";
                            clearInterval(intervalid);
                        }
                        document.getElementById("mes").innerHTML = i;
                        i--;
                    }
                    </script>
                    <div id="error">
                        <p>将在 <span id="mes">5</span> 秒钟后返回首页！</p>
                    </div> ';

            $res = array('error' => 0, 'errortip' => '更新成功', 'result' => array());
        }else{
            $res = array('error' => 1, 'errortip' => '无主体', 'result' => array());
        }
        ajax_return($res);

    }


    //自动更新招行已经接收到的订单数据
    //http://api.kcclassin.com/CmbTrans/autoRecordOrderApi
    function autoRecordOrderApi(){
        $request = Input('get.', '', 'trim,addslashes');
        $datawhere= "companies_issupervise=1 and companies_agencyid<>'' and companies_supervisetime>0 and company_id='8888'";

        if(isset($request['companies_agencyid']) && $request['companies_agencyid']!=''){
            $datawhere.=" and companies_agencyid='{$request['companies_agencyid']}'";
        }

        $sql = "select companies_agencyid,companies_signature,companies_secretkey 
                from gmc_code_companies 
                where {$datawhere}";

        $agencyList=$this->DataControl->selectClear($sql);

        if($agencyList){
            $num=1;
            foreach($agencyList as $agencyOne){
                $Model = new \Model\Api\CmbTransModel($agencyOne['companies_agencyid']);
                $n=$Model->autoRecordOrder();
                $num+=$n;
            }
//            echo '<script language="javascript" type="text/javascript">
//                    var i = 1;
//                    var intervalid;
//                    intervalid = setInterval("fun()", 1000);
//                    function fun() {
//                        if (i == 0) {
//                            window.location.href = "/CmbTrans/autoRecordOrderApi";
//                            clearInterval(intervalid);
//                        }
//                        document.getElementById("mes").innerHTML = i;
//                        i--;
//                    }
//                    </script>
//                    <div id="error">
//                        <p>将在 <span id="mes">5</span> 秒钟后返回首页！</p>
//                    </div> ';
            $res = array('error' => 0, 'errortip' => '更新成功'.$num.'条', 'result' => array());
        }else{
            $res = array('error' => 1, 'errortip' => '无主体', 'result' => array());
        }
        ajax_return($res);

    }


    //修改本地订单状态
    function autoHandleView(){
        $request = Input('get.', '', 'trim,addslashes');

        $sql = "UPDATE cmb_trans_order_mapping_log AS a
                LEFT JOIN cmb_trans_order AS b ON a.orderNo = b.order_pid 
                SET b.order_status = 1,
                b.order_status_all = 1,
                b.is_confirm = 1 
                WHERE b.order_status <>1 or b.order_status_all<>1";

        if($this->DataControl->query($sql)){
            $res = array('error' => 0, 'errortip' => '更新成功', 'result' => array());
        }else{
            $res = array('error' => 1, 'errortip' => '无主体', 'result' => array());
        }
        ajax_return($res);

    }

    //更新同步修改已提交招行订单笔数对不上的错误数据
    //http://api.kcclassin.com/CmbTrans/autoHandleGatherOrder
//    function autoHandleGatherOrderView(){
//        $request = Input('get.', '', 'trim,addslashes');
//        $timeOne=$this->DataControl->getFieldOne("cmb_trans_autotime","failgather_time"," 1 ");
//        $sql = "SELECT
//                    *
//                FROM
//                    cmb_trans_log AS a
//                WHERE
//                    a.log_url = '/pfs/head/addClassIncrement'
//                    AND a.log_back_json LIKE '%笔数对不上%' and a.log_time>'{$timeOne['failgather_time']}'
//                GROUP BY
//                    a.log_back_json
//                order by a.log_time asc";
//
//        $logList=$this->DataControl->selectClear($sql);
//
//        if($logList){
//            $time=0;
//            $classArray=array();
//            foreach($logList as $logOne){
//                $backArray=json_decode($logOne['log_back_json'],1);
//                if($backArray){
//                    foreach($backArray as $backOne){
//                        if(!in_array($backOne['classId'],$classArray)){
//                            $classArray[]=$backOne['classId'];
//                            $data=array();
//                            $data['classId']=$backOne['classId'];
//                            $this->DataControl->insertData("cmb_trans_class_gatherorder",$data);
//
//                        }
//                    }
//                }
//                $time=$logOne['log_time'];
//            }
//
//            $data=array();
//            $data['failgather_time']=$time;
//            $this->DataControl->updateData("cmb_trans_autotime","autotime_id=1",$data);
//
//        }
//
//        $sql = "select b.*,count(a.gatherorder_id) as num
//                from cmb_trans_class_gatherorder as a
//                inner join cmb_trans_class as b on a.classId=b.class_branch
//                where 1
//                and a.is_confirm=0
//                ";
//
////        $sql.=" and a.classId not in ('20230316000007','20220327000004')";
////        $sql.=" and a.classId in ('20220327000004')";
//
//        $sql.="group by a.classId
//                limit 0,1";
//        $classList=$this->DataControl->selectClear($sql);
//
//        if($classList){
//            $allNum=0;
//            foreach($classList as $classOne){
//                $Model = new \Model\Api\CmbTransModel($classOne['agency_id']);
//                $num=$Model->synchroGatherOrderItemInfo($classOne['class_branch']);
//                $allNum+=$num;
//            }
//
//            echo '<script language="javascript" type="text/javascript">
//                    var i = 1;
//                    var intervalid;
//                    intervalid = setInterval("fun()", 1000);
//                    function fun() {
//                        if (i == 0) {
//                            window.location.href = "/CmbTrans/autoHandleGatherOrder";
//                            clearInterval(intervalid);
//                        }
//                        document.getElementById("mes").innerHTML = i;
//                        i--;
//                    }
//                    </script>
//                    <div id="error">
//                        <p>将在 <span id="mes">5</span> 秒钟后返回首页！</p>
//                    </div> ';
//
//            $res = array('error' => 0, 'errortip' => '更新成功'.$allNum.'条', 'result' => array());
//        }else{
//            $res = array('error' => 1, 'errortip' => '无需更新', 'result' => array());
//        }
//
//        ajax_return($res);
//
//    }

    //根据订单同步记录插入本地订单数据
    //http://api.kcclassin.com/CmbTrans/autoRecordClassTransInfoApi
    function autoRecordClassTransInfoApi(){
        $request = Input('get.', '', 'trim,addslashes');
        $datawhere= "companies_issupervise=1 and companies_agencyid<>'' and companies_supervisetime>0 and company_id='8888'";

        if(isset($request['companies_agencyid']) && $request['companies_agencyid']!=''){
            $datawhere.=" and companies_agencyid='{$request['companies_agencyid']}'";
        }

        $sql = "select companies_agencyid,companies_signature,companies_secretkey 
                from gmc_code_companies 
                where {$datawhere}";

        $agencyList=$this->DataControl->selectClear($sql);

        if($agencyList){
            $num=1;
            foreach($agencyList as $agencyOne){
                $Model = new \Model\Api\CmbTransModel($agencyOne['companies_agencyid']);
                $n=$Model->autoRecordClassTransInfo();
                $num+=$n;
            }
            echo '<script language="javascript" type="text/javascript">
                    var i = 1;
                    var intervalid;
                    intervalid = setInterval("fun()", 1000);
                    function fun() {
                        if (i == 0) {
                            window.location.href = "/CmbTrans/autoRecordClassTransInfoApi";
                            clearInterval(intervalid);
                        }
                        document.getElementById("mes").innerHTML = i;
                        i--;
                    }
                    </script>
                    <div id="error">
                        <p>将在 <span id="mes">5</span> 秒钟后返回首页！</p>
                    </div> ';
            $res = array('error' => 0, 'errortip' => '更新成功'.$num.'条', 'result' => array());
        }else{
            $res = array('error' => 1, 'errortip' => '无主体', 'result' => array());
        }
        ajax_return($res);

    }

//http://api.kcclassin.com/CmbTrans/updateSynchroProposePushInfoStatusApi
    //根据划拨计划更新耗课数据
    function updateSynchroProposePushInfoStatusApi(){

        $request = Input('get.', '', 'trim,addslashes');
        $datawhere= "companies_issupervise=1 and companies_agencyid<>'' and companies_supervisetime>0 and company_id='8888'";

        if(isset($request['companies_agencyid']) && $request['companies_agencyid']!=''){
            $datawhere.=" and companies_agencyid='{$request['companies_agencyid']}'";
        }

        $sql = "select companies_agencyid,companies_signature,companies_secretkey 
                from gmc_code_companies 
                where {$datawhere}";

        $agencyList=$this->DataControl->selectClear($sql);

        if($agencyList) {
            foreach ($agencyList as $agencyOne) {
                $Model = new \Model\Api\CmbTransModel($agencyOne['companies_agencyid']);
                $Model->updateSynchroProposePushInfoStatus();
            }
        }

        $res = array('error' => 0, 'errortip' => '更新成功', 'result' => array());
        ajax_return($res);

    }

    function handelClassExceedInfoApi(){

        $request = Input('get.', '', 'trim,addslashes');
        $datawhere= "companies_issupervise=1 and companies_agencyid<>'' and companies_supervisetime>0 and company_id='8888'";

        if(isset($request['companies_agencyid']) && $request['companies_agencyid']!=''){
            $datawhere.=" and companies_agencyid='{$request['companies_agencyid']}'";
        }

        $sql = "select companies_agencyid,companies_signature,companies_secretkey 
                from gmc_code_companies 
                where {$datawhere}";

        $agencyList=$this->DataControl->selectClear($sql);

        if($agencyList) {
            foreach ($agencyList as $agencyOne) {
                $Model = new \Model\Api\CmbTransModel($agencyOne['companies_agencyid']);
                $Model->handelClassExceedInfo();
            }
        }

        $res = array('error' => 0, 'errortip' => '更新成功', 'result' => array());
        ajax_return($res);

    }



    //  账号数据同步结束


//--------------------------------------本地数据同步开始

    /**
     * addCmbClassInfo   addCmbLessonInfo  addCmbOrderChangeInfo  addCmbOrderInfo  addCmbTransferInfo
     * 处理数据开始入口
     * 生成班级缓存数据
     * 更新评率 ？？？？？
     * api.kcclassin.com/CmbTrans/synchroAllCmbClassInfoApi
     **/
    function synchroAllCmbClassInfoApi()
    {
        $sql = "select companies_agencyid,companies_signature,companies_secretkey 
                from gmc_code_companies 
                where companies_issupervise=1 and companies_agencyid<>'' and companies_supervisetime>0 and company_id='8888' and companies_id<>'9'";
        //$sql.=" and (companies_id in (8,10))";
        $agencyList=$this->DataControl->selectClear($sql);

        if($agencyList){
            $all_num=0;
            foreach($agencyList as $agencyOne){
                $Model = new \Model\Api\CmbDataprocessModel($agencyOne['companies_agencyid']);
                $num=$Model->addCmbClassInfo();
                $all_num+=$num;
            }
            $res = array('error' => 0, 'errortip' => "更新成功{$all_num}条", 'result' => array());
        }else{
            $res = array('error' => 1, 'errortip' => '无主体', 'result' => array());
        }

        ajax_return($res);
    }

    /**
     * 生成班级缓存数据
     * 更新评率 ？？？？？
     **/
    function synchroAllCmbLessonInfoApi()
    {
        $sql = "select companies_agencyid,companies_signature,companies_secretkey 
                from gmc_code_companies 
                where companies_issupervise=1 and companies_agencyid<>'' and companies_supervisetime>0 and company_id='8888' and companies_id<>'9'";
        //$sql.=" and (companies_id in (8,10))";
        $agencyList=$this->DataControl->selectClear($sql);

        if($agencyList){
            $all_num=0;
            foreach($agencyList as $agencyOne){
                $Model = new \Model\Api\CmbDataprocessModel($agencyOne['companies_agencyid']);
                $num=$Model->addCmbLessonInfo();
                $all_num+=$num;
            }

            echo '<script language="javascript" type="text/javascript">
                    var i = 1;
                    var intervalid;
                    intervalid = setInterval("fun()", 1000);
                    function fun() {
                        if (i == 0) {
                            window.location.href = "/CmbTrans/synchroAllCmbLessonInfoApi";
                            clearInterval(intervalid);
                        }
                        document.getElementById("mes").innerHTML = i;
                        i--;
                    }
                    </script>
                    <div id="error">
                        <p>将在 <span id="mes">5</span> 秒钟后返回首页！</p>
                    </div> ';


            $res = array('error' => 0, 'errortip' => "更新成功{$all_num}条", 'result' => array());
        }else{
            $res = array('error' => 1, 'errortip' => '无主体', 'result' => array());
        }

        ajax_return($res);
    }

    /**
     * 根据异动添加学生订单明细（缴费/结转）
     * 更新评率 ？？？？？
     **/
    function synchroAllCmbOrderChangeInfoApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $dateWhere = "companies_issupervise=1 and companies_agencyid<>'' and companies_supervisetime>0 and company_id='8888' and companies_id<>'9'";
        if(isset($request['companies_agencyid'])) {
            $dateWhere .= " AND companies_agencyid = '{$request['companies_agencyid']}'";
        }

        $sql = "select companies_agencyid,companies_signature,companies_secretkey  from gmc_code_companies  where {$dateWhere}";
        //$sql.=" and (companies_id in (8,10))";
        $agencyList=$this->DataControl->selectClear($sql);

        if($agencyList){
            $all_num=0;
            foreach($agencyList as $agencyOne){
                $Model = new \Model\Api\CmbDataprocessModel($agencyOne['companies_agencyid']);
                $num=$Model->addCmbOrderChangeInfo();
                $all_num+=$num;
            }

            $res = array('error' => 0, 'errortip' => "更新成功{$all_num}条", 'result' => array());
        }else{
            $res = array('error' => 1, 'errortip' => '无主体', 'result' => array());
        }

        ajax_return($res);
    }

    /**
     * 根据学生订单CHange生成带同步订单
     * 更新评率 ？？？？？
     * http://api.kcclassin.com/CmbTrans/synchroAllCmbOrderInfoApi?companies_agencyid=2022082082743975&student_id=177663
     **/
    function synchroAllCmbOrderInfoApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $dateWhere = "companies_issupervise=1 and companies_agencyid<>'' and companies_supervisetime>0 and company_id='8888' and companies_id<>'9'";
        if(isset($request['companies_agencyid'])) {
            $dateWhere .= " AND companies_agencyid = '{$request['companies_agencyid']}'";
        }
        $student_id = 0;
        if(isset($request['student_id'])) {
            $student_id = $request['student_id'];
        }

        $sql = "select companies_agencyid,companies_signature,companies_secretkey 
                from gmc_code_companies 
                where {$dateWhere} ";
        //$sql.=" and (companies_id in (78273))";
        $agencyList=$this->DataControl->selectClear($sql);
        if($agencyList){
            $all_num=0;
            foreach($agencyList as $agencyOne){
                $Model = new \Model\Api\CmbDataprocessModel($agencyOne['companies_agencyid']);
                $num=$Model->addCmbOrderInfo($student_id);
                $all_num+=$num;
            }
            $res = array('error' => 0, 'errortip' => "更新成功{$all_num}条", 'result' => array());
        }else{
            $res = array('error' => 1, 'errortip' => '无主体', 'result' => array());
        }

        ajax_return($res);
    }

    /**
     * 根据学生订单CHange生成带同步订单(Ajax版本 加速)
     * http://api.kcclassin.com/CmbTrans/synchroAllCmbOrderInfoAjaxApi
     **/
    function synchroAllCmbOrderInfoAjaxApi()
    {
        $sql = "select companies_agencyid,companies_signature,companies_secretkey 
                from gmc_code_companies 
                where companies_issupervise=1 and companies_agencyid<>'' and companies_supervisetime>0 and company_id='8888' and companies_id<>'9'";
        //$sql.=" and (companies_id in (78273))";
        $agencyList=$this->DataControl->selectClear($sql);
        if($agencyList){
            $all_num=0;
            foreach($agencyList as $agencyOne){
                $Model = new \Model\Api\CmbDataprocessModel($agencyOne['companies_agencyid']);
                $num=$Model->addCmbOrderInfo();
                $all_num+=$num;
            }

            echo '<script language="javascript" type="text/javascript">
                    var i = 1;
                    var intervalid;
                    intervalid = setInterval("fun()", 1000);
                    function fun() {
                        if (i == 0) {
                            window.location.href = "/CmbTrans/synchroAllCmbOrderInfoAjaxApi";
                            clearInterval(intervalid);
                        }
                        document.getElementById("mes").innerHTML = i;
                        i--;
                    }
                    </script>
                    <div id="error">
                        <p>将在 <span id="mes">5</span> 秒钟后返回首页！</p>
                    </div> ';
            $res = array('error' => 0, 'errortip' => "更新成功{$all_num}条", 'result' => array());
        }else{
            $res = array('error' => 1, 'errortip' => '无主体', 'result' => array());
        }

        ajax_return($res);
    }

    /**
     * 根据学生课耗生成带同步招行课耗信息
     * 更新评率 ？？？？？
     * http://api.kcclassin.com/CmbTrans/synchroAllCmbTransferInfoApi
     **/
    function synchroAllCmbTransferInfoApi()
    {
        $sql = "select companies_agencyid,companies_signature,companies_secretkey 
                from gmc_code_companies 
                where companies_issupervise=1 and companies_agencyid<>'' and companies_supervisetime>0 and company_id='8888' and companies_id<>'9'";
        //$sql.=" and (companies_id in (8,10))";
        $agencyList=$this->DataControl->selectClear($sql);

        if($agencyList){
            $all_num=0;
            foreach($agencyList as $agencyOne){
                $Model = new \Model\Api\CmbDataprocessModel($agencyOne['companies_agencyid']);
                $num=$Model->addCmbTransferInfo();
                $all_num+=$num;
            }
            $res = array('error' => 0, 'errortip' => "更新成功{$all_num}条", 'result' => array());
        }else{
            $res = array('error' => 1, 'errortip' => '无主体', 'result' => array());
        }

        ajax_return($res);
    }

    /**
     * 根据学生课耗生成带同步招行课耗信息(Ajax版本 加速)
     * http://api.kcclassin.com/CmbTrans/synchroAllCmbTransferInfoAjaxApi
     **/
    function synchroAllCmbTransferInfoAjaxApi()
    {
        $sql = "select companies_agencyid,companies_signature,companies_secretkey 
                from gmc_code_companies 
                where companies_issupervise=1 and companies_agencyid<>'' and companies_supervisetime>0 and company_id='8888' and companies_id<>'9'";
        $agencyList=$this->DataControl->selectClear($sql);
        if($agencyList){
            $all_num=0;
            foreach($agencyList as $agencyOne){
                $Model = new \Model\Api\CmbDataprocessModel($agencyOne['companies_agencyid']);
                $num=$Model->addCmbTransferInfo();
                $all_num+=$num;
            }

            echo '<script language="javascript" type="text/javascript">
                var i = 1;
                var intervalid;
                intervalid = setInterval("fun()", 1000);
                function fun() {
                    if (i == 0) {
                        window.location.href = "/CmbTrans/synchroAllCmbTransferInfoAjaxApi";
                        clearInterval(intervalid);
                    }
                    document.getElementById("mes").innerHTML = i;
                    i--;
                }
                </script>
                <div id="error">
                    <p>将在 <span id="mes">5</span> 秒钟后返回首页！</p>
                </div> ';

            $res = array('error' => 0, 'errortip' => "更新成功{$all_num}条", 'result' => array());
        }else{
            $res = array('error' => 1, 'errortip' => '无主体', 'result' => array());
        }
        ajax_return($res);
    }

//--------------------------------------本地数据同步结束

//--------------------------------------招行数据同步开始

    /**
     * synchroClassInfo   synchroClassHourInfo  synchroOrderItemInfo  synchroOrderInfo  synchroPushInfo  synchroProposePushInfo
     * 同步至招行数据开始入口
     * 同步招行班级修改及新增班级信息
     * 更新评率 ？？？？？
     * http://api.kcclassin.com/CmbTrans/synchroAllClassInfoApi

     **/

    function synchroAllClassInfoApi()
    {
        if(date("H") <= 12){
            ajax_return(array('error' => 1, 'errortip' => '更新未开始', 'result' => array()));
        }
        $sql = "select companies_agencyid,companies_signature,companies_secretkey 
                from gmc_code_companies where companies_issupervise=1 and companies_agencyid <> '' and companies_supervisetime >0 and company_id='8888' and companies_id<>'9'";
        //$sql.=" and (companies_id in (8,10))";
        $agencyList=$this->DataControl->selectClear($sql);
        if($agencyList){
            $all_num=0;
            foreach($agencyList as $agencyOne){
                $Model = new \Model\Api\CmbTransModel($agencyOne['companies_agencyid']);
                $num=$Model->synchroClassInfo();
                $all_num+=$num;
            }

            $res = array('error' => 0, 'errortip' => "更新成功{$all_num}条", 'result' => array());
        }else{
            $res = array('error' => 1, 'errortip' => '无主体', 'result' => array());
        }

        ajax_return($res);
    }

    /**
     * 同步招行班级课时数据
     * 更新评率 ？？？？？
     **/
    function synchroAllClassHourInfoApi()
    {
//        if(date("H")<=12){
//            ajax_return(array('error' => 1, 'errortip' => '更新未开始', 'result' => array()));
//        };
        $sql = "select companies_agencyid,companies_signature,companies_secretkey 
                from gmc_code_companies 
                where companies_issupervise=1 and companies_agencyid<>'' and companies_supervisetime>0 and company_id='8888' and companies_id<>'9'";
        //$sql.=" and (companies_id in (8,10))";
        $agencyList=$this->DataControl->selectClear($sql);
        if($agencyList){
            $all_num=0;
            foreach($agencyList as $agencyOne){
                $Model = new \Model\Api\CmbTransModel($agencyOne['companies_agencyid']);
                $num=$Model->synchroClassHourInfo();
                $all_num+=$num;
            }
            echo '<script language="javascript" type="text/javascript">
                var i = 1;
                var intervalid;
                intervalid = setInterval("fun()", 1000);
                function fun() {
                    if (i == 0) {
                        window.location.href = "/CmbTrans/synchroAllClassHourInfoApi";
                        clearInterval(intervalid);
                    }
                    document.getElementById("mes").innerHTML = i;
                    i--;
                }
                </script>
                <div id="error">
                    <p>将在 <span id="mes">5</span> 秒钟后返回首页！</p>
                </div> ';

            $res = array('error' => 0, 'errortip' => "更新成功{$all_num}条", 'result' => array());
        }else{
            $res = array('error' => 1, 'errortip' => '无主体', 'result' => array());
        }

        ajax_return($res);
    }


    /**
     * 2.3接口更新招行学员购课/退课订单汇总
     * 更新评率 ？？？？？
     * http://api.kcclassin.com/CmbTrans/synchroAllOrderItemInfoApi?companies_agencyid=2022012065699299&class_branch=20220228000127
     **/
    function synchroAllOrderItemInfoApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $dateWhere = "companies_issupervise=1 and companies_agencyid<>'' and companies_supervisetime>0 and company_id='8888' and companies_id<>'9'";
        if(isset($request['companies_agencyid'])){
            $dateWhere .= " AND companies_agencyid = '{$request['companies_agencyid']}'";
        }
        $class_branch = '';
        if(isset($request['class_branch'])){
            $class_branch = $request['class_branch'];
        }

        $sql = "select companies_agencyid,companies_signature,companies_secretkey 
                from gmc_code_companies 
                where  {$dateWhere} ";
//        $sql.=" and companies_agencyid='2022082082743975'";
        $sql.=" and (companies_id in (8))";
        $agencyList=$this->DataControl->selectClear($sql);

        if($agencyList){
            $all_num=0;
            foreach($agencyList as $agencyOne){
                $Model = new \Model\Api\CmbTransModel($agencyOne['companies_agencyid']);
                $num=$Model->synchroOrderItemInfo($class_branch);
                $all_num+=$num;
            }

                echo '<script language="javascript" type="text/javascript">
                var i = 1;
                var intervalid;
                intervalid = setInterval("fun()", 1000);
                function fun() {
                    if (i == 0) {
                        window.location.href = "/CmbTrans/synchroAllOrderItemInfoApi";
                        clearInterval(intervalid);
                    }
                    document.getElementById("mes").innerHTML = i;
                    i--;
                }
                </script>
                <div id="error">
                    <p>将在 <span id="mes">5</span> 秒钟后返回首页！</p>
                </div> ';
            $res = array('error' => 0, 'errortip' => "更新成功{$all_num}条", 'result' => array());
        }else{
            $res = array('error' => 1, 'errortip' => '无主体', 'result' => array());
        }

        ajax_return($res);
    }

    //http://api.kcclassin.com/CmbTrans/synchroAllOrderInfoApi
    function synchroAllOrderInfoApi()
    {
        $sql = "select companies_agencyid,companies_signature,companies_secretkey 
                from gmc_code_companies 
                where companies_issupervise=1 and companies_agencyid<>'' and companies_supervisetime>0 and company_id='8888' and companies_id<>'9'";
        $sql.=" and (companies_id in (8))";
        $agencyList=$this->DataControl->selectClear($sql);

        if($agencyList){
            $all_num=0;
            foreach($agencyList as $agencyOne){
                $Model = new \Model\Api\CmbTransModel($agencyOne['companies_agencyid']);
                $num=$Model->synchroOrderInfo();
                $all_num+=$num;
            }

                        echo '<script language="javascript" type="text/javascript">
                var i = 1;
                var intervalid;
                intervalid = setInterval("fun()", 1000);
                function fun() {
                    if (i == 0) {
                        window.location.href = "/CmbTrans/synchroAllOrderInfoApi";
                        clearInterval(intervalid);
                    }
                    document.getElementById("mes").innerHTML = i;
                    i--;
                }
                </script>
                <div id="error">
                    <p>将在 <span id="mes">5</span> 秒钟后返回首页！</p>
                </div> ';


            $res = array('error' => 0, 'errortip' => "更新成功{$all_num}条", 'result' => array());
        }else{
            $res = array('error' => 1, 'errortip' => '无主体', 'result' => array());
        }

        ajax_return($res);
    }

    //同步划拨详情数据
    //http://api.kcclassin.com/CmbTrans/synchroAllPushInfoApi
    function synchroAllPushInfoApi()
    {
        //        if(date("H")<=12){
        //            ajax_return(array('error' => 1, 'errortip' => '更新未开始', 'result' => array()));
        //        };
        $sql = "select companies_agencyid,companies_signature,companies_secretkey 
                from gmc_code_companies 
                where companies_issupervise=1 and companies_agencyid<>'' and companies_supervisetime>0 and company_id='8888' and companies_id<>'9'";
        //        $sql.=" and (companies_id in (8,10))";
        //        $sql.=" and (companies_id in (78603))";
        $agencyList=$this->DataControl->selectClear($sql);

        if($agencyList){
            $all_num=0;
            foreach($agencyList as $agencyOne){
                $Model = new \Model\Api\CmbTransModel($agencyOne['companies_agencyid']);
                $num=$Model->synchroPushInfo();
                $all_num+=$num;
            }

            echo '<script language="javascript" type="text/javascript">
                var i = 1;
                var intervalid;
                intervalid = setInterval("fun()", 1000);
                function fun() {
                    if (i == 0) {
                        window.location.href = "/CmbTrans/synchroAllPushInfoApi";
                        clearInterval(intervalid);
                    }
                    document.getElementById("mes").innerHTML = i;
                    i--;
                }
                </script>
                <div id="error">
                    <p>将在 <span id="mes">5</span> 秒钟后返回首页！</p>
                </div> ';

            $res = array('error' => 0, 'errortip' => "更新成功{$all_num}条", 'result' => array());
        }else{
            $res = array('error' => 1, 'errortip' => '无主体', 'result' => array());
        }

        ajax_return($res);
    }

//http://api.kcclassin.com/CmbTrans/synchroAllProposePushInfoApi    每小时 第16分钟
    function synchroAllProposePushInfoApi()
    {

//        if(date("w")!=5){
//          ajax_return(array('error' => 1, 'errortip' => '更新未开始', 'result' => array()));
//        };
        $sql = "select companies_agencyid,companies_signature,companies_secretkey 
                from gmc_code_companies 
                where companies_issupervise=1 and companies_agencyid<>'' and companies_supervisetime>0 and company_id='8888' and companies_id<>'9'
                ";

                $sql.=" and companies_agencyid not in (select x.agency_id from cmb_trans_falsecompanies as x)";
//                $sql.=" and (companies_id in (8))";
//                $sql.=" and companies_id in (78267,78273,8,10,78281)";
        $agencyList=$this->DataControl->selectClear($sql);

        if($agencyList){
            $all_num=0;
            foreach($agencyList as $agencyOne){
                $Model = new \Model\Api\CmbTransModel($agencyOne['companies_agencyid']);
                $num=$Model->synchroProposePushInfoTwo();
                $all_num+=$num;
            }

            $res = array('error' => 0, 'errortip' => "更新成功{$all_num}条", 'result' => array());
        }else{
            $res = array('error' => 1, 'errortip' => '无主体', 'result' => array());
        }

        ajax_return($res);
    }


//--------------------------------------招行数据同步结束

//--------------------------------------招行数据按月同步


    //同步划拨详情数据
    //http://api.kcclassin.com/CmbTrans/synchroMonthAllPushInfoApi
    function synchroMonthAllPushInfoApi()
    {
        //        if(date("H")<=12){
        //            ajax_return(array('error' => 1, 'errortip' => '更新未开始', 'result' => array()));
        //        };
        $sql = "select companies_agencyid,companies_signature,companies_secretkey 
                from gmc_code_companies 
                where companies_issupervise=1 and companies_agencyid<>'' and companies_supervisetime>0 and company_id='8888' and companies_id<>'9'";
//                $sql.=" and (companies_id in (8))";
        //        $sql.=" and (companies_id in (78603))";
        $agencyList=$this->DataControl->selectClear($sql);

        if($agencyList){
            $all_num=0;
            foreach($agencyList as $agencyOne){
                $Model = new \Model\Api\CmbTransModel($agencyOne['companies_agencyid']);
                $num=$Model->synchroMonthAllPushInfo();
                $all_num+=$num;
            }

            echo '<script language="javascript" type="text/javascript">
                var i = 1;
                var intervalid;
                intervalid = setInterval("fun()", 1000);
                function fun() {
                    if (i == 0) {
                        window.location.href = "/CmbTrans/synchroMonthAllPushInfoApi";
                        clearInterval(intervalid);
                    }
                    document.getElementById("mes").innerHTML = i;
                    i--;
                }
                </script>
                <div id="error">
                    <p>将在 <span id="mes">5</span> 秒钟后返回首页！</p>
                </div> ';

            $res = array('error' => 0, 'errortip' => "更新成功{$all_num}条", 'result' => array());
        }else{
            $res = array('error' => 1, 'errortip' => '无主体', 'result' => array());
        }

        ajax_return($res);
    }

//http://api.kcclassin.com/CmbTrans/synchroMonthAllProposePushInfoApi    每小时 第16分钟
    function synchroMonthAllProposePushInfoApi()
    {

        $sql = "select companies_agencyid,companies_signature,companies_secretkey 
                from gmc_code_companies 
                where companies_issupervise=1 and companies_agencyid<>'' and companies_supervisetime>0 and company_id='8888' and companies_id<>'9'
                ";

        $sql.=" and companies_agencyid not in (select x.agency_id from cmb_trans_falsecompanies as x)";
//                $sql.=" and (companies_id in (8,13,78582))";
//                $sql.=" and (companies_id not in (78273))";
//                $sql.=" and (companies_id in (78273))";
        $agencyList=$this->DataControl->selectClear($sql);

        if($agencyList){
            $all_num=0;
            foreach($agencyList as $agencyOne){
                $Model = new \Model\Api\CmbTransModel($agencyOne['companies_agencyid']);
                $num=$Model->synchroMonthAllProposePushInfo();
                $all_num+=$num;
            }

            $res = array('error' => 0, 'errortip' => "更新成功{$all_num}条", 'result' => array());
        }else{
            $res = array('error' => 1, 'errortip' => '无主体', 'result' => array());
        }

        ajax_return($res);
    }





//--------------------------------------招行数据同步结束

    function getClassInfoApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $Model = new \Model\Api\CmbTransModel($request['agencyid']);
        $Model->getClassInfo($request['class_branch']);
        debug($Model);
    }



    function getLastSubmitResultApi()
    {
        $sql = "select companies_agencyid,companies_signature,companies_secretkey 
                from gmc_code_companies 
                where companies_issupervise=1 and companies_agencyid<>'' and companies_supervisetime>0 and company_id='8888' and companies_id<>'9'";

        $agencyList=$this->DataControl->selectClear($sql);

        if($agencyList){
            $data=array();
            foreach($agencyList as $agencyOne){
                $Model = new \Model\Api\CmbTransModel($agencyOne['companies_agencyid']);
                $data[]=$Model->getLastSubmitResult();
            }

            $res = array('error' => 0, 'errortip' => "获取成功", 'result' => $data);
        }else{
            $res = array('error' => 1, 'errortip' => '无主体', 'result' => array());
        }

        ajax_return($res);
    }


    /**
     * 查询机构订单明细
     * 更新评率 ？？？？？
     **/
    function queryStudentOrderApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $dateWhere = "companies_issupervise=1 and companies_agencyid<>'' and companies_supervisetime>0 and company_id='8888' and companies_id<>'9'";
        if(isset($request['companies_agencyid'])) {
            $dateWhere .= " AND companies_agencyid = '{$request['companies_agencyid']}'";
        }
        $dayVar = $request['day'];
        $classId = $request['classId'];

        $sql = "select companies_agencyid,companies_signature,companies_secretkey  from gmc_code_companies  where {$dateWhere} ";
        //$sql.=" and (companies_id in (78273))";
        $agencyList=$this->DataControl->selectClear($sql);
        if($agencyList){
            $all_num=0;
            foreach($agencyList as $agencyOne){
                $Model = new \Model\Api\CmbTransModel($agencyOne['companies_agencyid']);
                $returnData = $Model->queryStudentOrder($dayVar,$classId);
                print_r($returnData);
            }
            $res = array('error' => 0, 'errortip' => "查询成功", 'result' => array());
        }else{
            $res = array('error' => 1, 'errortip' => '无主体', 'result' => array());
        }

        ajax_return($res);
    }


    /**
     * 查询机构订单明细
     * 更新评率 ？？？？？
     **/
    function queryClassEliminateApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $dateWhere = "companies_issupervise=1 and companies_agencyid<>'' and companies_supervisetime>0 and company_id='8888' and companies_id<>'9'";
        if(isset($request['companies_agencyid'])) {
            $dateWhere .= " AND companies_agencyid = '{$request['companies_agencyid']}'";
        }
        $dayVar = $request['day'];
        $classId = $request['classId'];

        $sql = "select companies_agencyid,companies_signature,companies_secretkey  from gmc_code_companies  where {$dateWhere} ";
        //$sql.=" and (companies_id in (78273))";
        $agencyList=$this->DataControl->selectClear($sql);
        if($agencyList){
            $all_num=0;
            foreach($agencyList as $agencyOne){
                $Model = new \Model\Api\CmbTransModel($agencyOne['companies_agencyid']);
                $returnData = $Model->queryClassEliminate($dayVar,$classId,$request['nextKeyValue']);
                print_r($returnData);exit;
            }
            $res = array('error' => 0, 'errortip' => "查询成功", 'result' => array());
        }else{
            $res = array('error' => 1, 'errortip' => '无主体', 'result' => array());
        }

        ajax_return($res);
    }



    //特殊转主体处理
    function specialTransSubjectApi()
    {
        $request = Input('get.', '', 'trim,addslashes');

        $datawhere=" 1 ";

        if(isset($request['student_branch']) && $request['student_branch']!=''){
            $datawhere.=" and a.student_branch='{$request['student_branch']}' ";
        }

        if(isset($request['companies_id']) && $request['companies_id']!=''){
            $datawhere.=" and a.from_companies_id='{$request['companies_id']}' ";
        }

        $sql = "SELECT a.*,c.student_id,b.school_id
                FROM
                    cmb_tem_stucoursebalance AS a
                    INNER JOIN smc_school AS b ON a.school_branch = b.school_branch
                    INNER JOIN smc_student AS c ON c.student_branch = a.student_branch 
                WHERE {$datawhere} 
                    and ( SELECT sum( x.log_playamount ) FROM smc_student_coursebalance_log AS x WHERE x.student_id = c.student_id AND x.companies_id = a.to_companies_id and x.hourstudy_id>0 )> 0 
                    and a.coursebalance>hastransprice
                    order by a.updatetime asc
                    limit 0,10
                    ";

        $studentList=$this->DataControl->selectClear($sql);

        if($studentList){
            foreach($studentList as $studentOne){
                $Model = new \Model\Api\CmbSpecialModel();
                $returnData = $Model->specialTransSubject($studentOne);

                if($returnData){
                    $data=array();
                    $data['hastransprice']=$returnData;
                    $data['updatetime']=time();
                    $this->DataControl->updateData("cmb_tem_stucoursebalance","stucoursebalance_id='{$studentOne['stucoursebalance_id']}'",$data);

                }
            }

            $res = array('error' => 0, 'errortip' => "数据更新成功", 'result' => $data);

        }else{
            $res = array('error' => 1, 'errortip' => '无学生', 'result' => array());

        }

        ajax_return($res);

    }


    //修改订单与划拨订单相同但班级不同的数据
    //http://api.kcclassin.com/CmbTrans/specialTransApi

    function specialTransApi(){

        $Model = new \Model\Api\CmbSpecialModel();
        $returnData = $Model->specialTrans();

    }

//http://api.kcclassin.com/CmbTrans/specialTransOrderApi
    function specialTransOrderApi(){

        $Model = new \Model\Api\CmbSpecialModel();
        $returnData = $Model->specialTransOrder();

    }







}