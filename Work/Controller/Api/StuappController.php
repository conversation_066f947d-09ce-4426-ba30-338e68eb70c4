<?php


namespace Work\Controller\Api;


class StuappController extends viewTpl
{
    public $u;
    public $t;
    public $c;
    public $visitType = "api";


    //预加载处理类
    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
        $this->api = $this->router->getApi();
    }

    function getAppCodeZhView(){
        $maxTime = time()-3600*24*7;
        $mislogList = $this->DataControl->selectClear("
            SELECT
             l.apppropermislog_id,
             l.apppropermis_authcode,
             l.apppropermislog_endday,
             c.company_language
            FROM
             smc_student_apppropermislog AS l,
             gmc_company AS c
            WHERE
             l.company_id = c.company_id
            AND l.apppropermis_code = 'QIQUONLINE'
            AND l.apppropermislog_endday >= date_format(now(), '%Y-%m-%d') AND c.company_language = 'zh'
            AND l.apppropermislog_updatetime <= '{$maxTime}'
            ORDER BY l.apppropermislog_updatetime ASC 
            limit 0,100");
        if($mislogList){
            $maArray = array();
            foreach($mislogList as $mislogOne){
                $maArray[] = $mislogOne['apppropermis_authcode'];
            }
            $maString = implode(",", $maArray);
            $Qqonline = new \Model\Api\QqonlineModel('zh');
            $cardList = $Qqonline->getCardDeadline($maString);

            if($cardList){
                foreach($cardList as $cardOne){
                    $data = array();
                    $data['apppropermislog_endday'] = $cardOne['endtime'];
                    $data['apppropermislog_updatetime'] = time();
                    $this->DataControl->updateData("smc_student_apppropermislog","apppropermis_authcode = '{$cardOne['carditem_branch']}'",$data);
                }
                ajax_return(array('error' => 0, 'errortip' => "卡号更新完毕!", 'result' => array()));
            }else{
                ajax_return(array('error' => 0, 'errortip' => "卡号更新失败!".$Qqonline->errortip, 'result' => array()));
            }
        }else{
            ajax_return(array('error' => 0, 'errortip' => "暂无最新更新记录!", 'result' => array()));
        }
    }

    function getAppCodeTwView(){
        $maxTime = time()-3600*24*7;
        $mislogList = $this->DataControl->selectClear("
            SELECT
             l.apppropermislog_id,
             l.apppropermis_authcode,
             l.apppropermislog_endday,
             c.company_language
            FROM
             smc_student_apppropermislog AS l,
             gmc_company AS c
            WHERE
             l.company_id = c.company_id
            AND l.apppropermis_code = 'QIQUONLINE'
            AND l.apppropermislog_endday >= date_format(now(), '%Y-%m-%d') AND c.company_language = 'tw'
            AND l.apppropermislog_updatetime <= '{$maxTime}'
            ORDER BY l.apppropermislog_updatetime ASC
            limit 0,100");
        if($mislogList){
            $maArray = array();
            foreach($mislogList as $mislogOne){
                $maArray[] = $mislogOne['apppropermis_authcode'];
            }
            $maString = implode(",", $maArray);
            $Qqonline = new \Model\Api\QqonlineModel('tw');
            $cardList = $Qqonline->getCardDeadline($maString);

            if($cardList){
                foreach($cardList as $cardOne){
                    $data = array();
                    $data['apppropermislog_endday'] = $cardOne['endtime'];
                    $data['apppropermislog_updatetime'] = time();
                    $this->DataControl->updateData("smc_student_apppropermislog","apppropermis_authcode = '{$cardOne['carditem_branch']}'",$data);
                }
                ajax_return(array('error' => 0, 'errortip' => "卡号更新完毕!", 'result' => array()));
            }else{
                ajax_return(array('error' => 0, 'errortip' => "卡号更新失败!".$Qqonline->errortip, 'result' => array()));
            }
        }else{
            ajax_return(array('error' => 0, 'errortip' => "暂无最新更新记录!", 'result' => array()));
        }
    }


    //学生产品权限 解绑
    function getStuQiquRelieveApi()
    {
        $request = Input('get.', '', 'trim,addslashes');

        if ($this->DataControl->delData("smc_student_apppropermislog", "apppropermis_authcode='{$request['apppropermis_authcode']}'")) {
            $res = array('error' => 0, 'errortip' => '删除成功', 'result' => array());
        } else {
            $res = array('error' => 1, 'errortip' => '删除失败', 'result' => array());
        }
        ajax_return($res, $request['language_type']);
    }

}