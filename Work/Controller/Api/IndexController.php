<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong <PERSON>
 * Date: 2017/3/23
 * Time: 0:37
 */
namespace Work\Controller\Api;

class IndexController extends viewTpl{
    public $u;
    public $t;
    public $c;

    function __construct() {
        parent::__construct ();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
    }

    function HomeView(){
        echo date("Y-m-d H:i:s",strtotime(date("Y-m-d", 1665561429)) + ((time() + 8 * 3600) % 86400));
    }

    //学校主体更新
    function schoolGetApi(){
        $schoolList = $this->DataControl->selectClear("SELECT s.school_id,s.company_id,s.companies_id,s.school_branch,s.school_shortname,s.school_cnname
FROM smc_school AS s WHERE s.companies_id = '0' GROUP BY s.company_id");
        if ($schoolList) {
            foreach($schoolList as $schoolOne){
                $companiesOne = $this->DataControl->getFieldOne("gmc_code_companies","companies_id,companies_cnname","company_id = '{$schoolOne['company_id']}'");
                if(!$companiesOne){
                    $companiesOne = array();
                    $companiesOne['company_id'] = $schoolOne['company_id'];
                    $companiesOne['companies_cnname'] = $schoolOne['school_cnname'];
                    $companiesOne['companies_branch'] = "CMTL".$schoolOne['company_id'];
                    $companiesOne['companies_createtime'] = time();
                    $companiesOne['companies_updatatime'] = time();
                    $this->DataControl->insertData("gmc_code_companies",$companiesOne);
                    $companiesOne = $this->DataControl->getFieldOne("gmc_code_companies","companies_id,companies_cnname","company_id = '{$schoolOne['company_id']}'");
                }
                $schoolData = array();
                $schoolData['companies_id'] = $companiesOne['companies_id'];
                $this->DataControl->updateData("smc_school","school_id = '{$schoolOne['school_id']}'",$schoolData);
            }
        }
    }

    function schoolGetpoApi(){
        $schoolList = $this->DataControl->selectClear("SELECT s.school_id,s.company_id,s.companies_id,s.school_branch,s.school_shortname,s.school_cnname
FROM smc_school AS s WHERE s.companies_id = '0' GROUP BY s.company_id");
        if ($schoolList) {
            foreach($schoolList as $schoolOne){
                $companiesOne = $this->DataControl->getFieldOne("gmc_code_companies","companies_id,companies_cnname","company_id = '{$schoolOne['company_id']}'");
                if(!$companiesOne){
                    $companiesOne = array();
                    $companiesOne['company_id'] = $schoolOne['company_id'];
                    $companiesOne['companies_cnname'] = $schoolOne['school_cnname'];
                    $companiesOne['companies_branch'] = "CMTL".$schoolOne['company_id'];
                    $companiesOne['companies_createtime'] = time();
                    $companiesOne['companies_updatatime'] = time();
                    $this->DataControl->insertData("gmc_code_companies",$companiesOne);
                    $companiesOne = $this->DataControl->getFieldOne("gmc_code_companies","companies_id,companies_cnname","company_id = '{$schoolOne['company_id']}'");
                }
                $schoolData = array();
                $schoolData['companies_id'] = $companiesOne['companies_id'];
                $this->DataControl->updateData("smc_school","school_id = '{$schoolOne['school_id']}'",$schoolData);
            }
        }
    }


    function pingziView(){
        $may = 1;
        $pingzi = 0;
        $shui = 0;
        while($may < 150){
            echo "买了第{$may}瓶水  ";
            $pingzi++;
            $shui += 1;
            echo "喝到第{$shui}瓶水  ";
            if($pingzi == 5){
                $pingzi=1;
                $shui += 1;
                echo "喝到兑换第{$shui}瓶水";
            }
            echo "<br />";
            $may++;
        }
    }

    function __destruct()
    {
        exit;
    }

}