<?php


namespace Work\Controller\Api;


use Model\Api\JiaowuModel;

class JiaowuController extends viewTpl
{
    public $u;
    public $t;
    public $c;
    public $visitType = "api";
    public $errortip = "";


    //预加载处理类
    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
        $this->api = $this->router->getApi();
    }

    //教师课表
    function StafferTimetableApi()
    {
        $request = Input('get.','','trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            //访问日志
            $this->VeryLogs($pucArray['apiuser_id'], "{$request['u']}/{$request['t']}{$request['api']}", $request);

            $schoolOne = $this->DataControl->getFieldOne("smc_school", "company_id,school_id", "school_branch = '{$request['school_branch']}'");
            if ($schoolOne) {
                $request['company_id'] = $schoolOne['company_id'];
                $request['school_id'] = $schoolOne['school_id'];
            }
            $stafferOne = $this->DataControl->getFieldOne("smc_staffer", "company_id,staffer_id", "staffer_branch = '{$request['teacher_branch']}'");
            if ($stafferOne) {
                $request['staffer_id'] = $stafferOne['staffer_id'];
            }
            $coursetypeOne = $this->DataControl->getFieldOne("smc_code_coursetype", "coursetype_id", "company_id = '{$schoolOne['company_id']}' and coursetype_branch = '{$request['coursetype_branch']}'");
            if ($coursetypeOne) {
                $request['coursetype_id'] = $coursetypeOne['coursetype_id'];
            }
            $coursecatOne = $this->DataControl->getFieldOne("smc_code_coursecat", "coursecat_id", "company_id = '{$schoolOne['company_id']}' and coursecat_branch = '{$request['coursecat_branch']}'");
            if ($coursecatOne) {
                $request['coursecat_id'] = $coursecatOne['coursecat_id'];
            }

            $Model = new JiaowuModel();
            $dataList = $Model->getTeacherWeekTimetable($request);

            if (!isset($request['is_export']) || $request['is_export'] != 1) {
                $result = array();
                $field = array();
                if ($dataList) {
                    foreach ($dataList['week_date'] as $key => $value) {
                        $field[$key + 1]["fieldcnname"] = "{$value['weekcnday']}";
                        $field[$key + 1]["fieldname"] = "{$value['weekday']}";
                        $field[$key + 1]["fieldstring"] = "{$value['week_day']}";
                        $field[$key + 1]["show"] = 1;
                        $field[$key + 1]["custom"] = 0;
                    }
                    unset($dataList['week_date']);
                } else {
                    $enweekarray = array("Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday",);
                    $weekarray = array("一", "二", "三", "四", "五", "六", "日",);

                    for ($i = 0; $i < 7; $i++) {
                        $day = date('Y-m-d', strtotime("+$i day", strtotime($request['hour_startday'])));
                        $field[$i + 1]["fieldcnname"] = '星期' . $weekarray[$i];
                        $field[$i + 1]["fieldname"] = $day;
                        $field[$i + 1]["fieldstring"] = $enweekarray[$i];
                        $field[$i + 1]["show"] = 1;
                        $field[$i + 1]["custom"] = 0;
                    }
                }

                $result['field'] = $field;
                if ($dataList) {
                    $result['list'] = $dataList;
                    $res = array('error' => 0, 'errortip' => '获取课表信息', 'result' => $result);
                } else {
                    $result['list'] = array();
                    $res = array('error' => 1, 'errortip' => '暂无课表信息', 'result' => $result);
                }
            } else {
                $result = array();
                $result['field'] = $dataList['field'];
                if ($dataList) {
                    $result['list'] = $dataList['list'];
                    $result['name'] = $dataList['name'];
                    $res = array('error' => 0, 'errortip' => '获取课表打印信息', 'result' => $result);
                } else {
                    $result['list'] = array();
                    $result['name'] = '';
                    $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
                }
            }
            ajax_return($res, $request['language_type']);
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //获取教师课表 -- 移动端
    function StafferTimetableOneApi()
    {
        $request = Input('get.','','trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            //访问日志
            $this->VeryLogs($pucArray['apiuser_id'], "{$request['u']}/{$request['t']}{$request['api']}", $request);

            $schoolOne = $this->DataControl->getFieldOne("smc_school", "company_id,school_id", "school_branch = '{$request['school_branch']}'");
            if ($schoolOne) {
                $request['company_id'] = $schoolOne['company_id'];
                $request['school_id'] = $schoolOne['school_id'];
            }
            $stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id", "staffer_branch = '{$request['teacher_branch']}'");
            if ($stafferOne) {
                $request['staffer_id'] = $stafferOne['staffer_id'];
            }
            $classOne = $this->DataControl->getFieldOne("smc_class", "class_id", "class_branch = '{$request['class_branch']}'");
            if ($classOne) {
                $request['class_id'] = $classOne['class_id'];
            }
            $coursetypeOne = $this->DataControl->getFieldOne("smc_code_coursetype", "coursetype_id", "company_id = '{$schoolOne['company_id']}' and coursetype_branch = '{$request['coursetype_branch']}'");
            if ($coursetypeOne) {
                $request['coursetype_id'] = $coursetypeOne['coursetype_id'];
            }
            $coursecatOne = $this->DataControl->getFieldOne("smc_code_coursecat", "coursecat_id", "company_id = '{$schoolOne['company_id']}' and coursecat_branch = '{$request['coursecat_branch']}'");
            if ($coursecatOne) {
                $request['coursecat_id'] = $coursecatOne['coursecat_id'];
            }

            $Model = new JiaowuModel();
            $dataList = $Model->StafferTimetableOne($request);

            if ($dataList) {
                $result['list'] = $dataList;
                $res = array('error' => 0, 'errortip' => '获取课表信息', 'result' => $result);
            } else {
                $result['list'] = array();
                $res = array('error' => 1, 'errortip' => '暂无课表信息', 'result' => $result);
            }
            ajax_return($res, $request['language_type']);
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //获取教师课程班组
    function getCourseTypeView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            //访问日志
            $this->VeryLogs($pucArray['apiuser_id'], "{$request['u']}/{$request['t']}{$request['api']}", $request);

            $schoolOne = $this->DataControl->getFieldOne("smc_school", "school_id", "school_branch = '{$request['school_branch']}'");

            $sql = "SELECT ct.coursetype_id,ct.coursetype_cnname,ct.coursetype_branch
                FROM smc_staffer as s
                LEFT JOIN smc_class_hour_teaching as t ON s.staffer_id = t.staffer_id
                LEFT JOIN smc_class_hour as ch ON ch.hour_id = t.hour_id
                LEFT JOIN smc_class as c ON c.class_id = ch.class_id
                LEFT JOIN smc_course as co ON co.course_id = c.course_id
                LEFT JOIN smc_code_coursetype as ct ON ct.coursetype_id = co.coursetype_id
                WHERE c.school_id = '{$schoolOne['school_id']}' and ch.hour_ischecking <> '-1' and c.class_status <> '-2'
                GROUP BY ct.coursetype_id";
            $dataList = $this->DataControl->selectClear($sql);

            $result = array();
            $result["list"] = $dataList;
            $res = array('error' => 0, 'errortip' => '获取班组信息', 'result' => $result);
            ajax_return($res, $request['language_type']);
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //获取教师课程班种
    function getCourseCatView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            //访问日志
            $this->VeryLogs($pucArray['apiuser_id'], "{$request['u']}/{$request['t']}{$request['api']}", $request);

            $schoolOne = $this->DataControl->getFieldOne("smc_school", "school_id", "school_branch = '{$request['school_branch']}'");

            $datawhere = "c.school_id = '{$schoolOne['school_id']}' and ch.hour_ischecking <> '-1' and c.class_status <> '-2'";
            if (isset($request['coursetype_id']) && $request['coursetype_id'] !== '') {
                $datawhere .= " and cc.coursetype_id = '{$request['coursetype_id']}'";
            }

            $sql = "SELECT cc.coursecat_id,cc.coursecat_cnname,cc.coursecat_branch
                FROM smc_staffer as s
                LEFT JOIN smc_class_hour_teaching as t ON s.staffer_id = t.staffer_id
                LEFT JOIN smc_class_hour as ch ON ch.hour_id = t.hour_id
                LEFT JOIN smc_class as c ON c.class_id = ch.class_id
                LEFT JOIN smc_course as co ON co.course_id = c.course_id
                LEFT JOIN smc_code_coursecat as cc ON cc.coursecat_id = co.coursecat_id
                WHERE {$datawhere}
                GROUP BY cc.coursecat_id";
            $dataList = $this->DataControl->selectClear($sql);

            $result = array();
            $result["list"] = $dataList;
            $res = array('error' => 0, 'errortip' => '获取班种信息', 'result' => $result);
            ajax_return($res, $request['language_type']);
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //获取课程详情
    function classCourseOneApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            //访问日志
            $this->VeryLogs($pucArray['apiuser_id'], "{$request['u']}/{$request['t']}{$request['api']}", $request);

            $stafferOne = $this->DataControl->getFieldOne("smc_staffer", "company_id,staffer_id", "staffer_branch = '{$request['teacher_branch']}'");
            if ($stafferOne) {
                $request['staffer_id'] = $stafferOne['staffer_id'];
            }

            $Model = new JiaowuModel();
            $res = $Model->getCourseOne($request);

            $result = array();
            if ($res) {
                $result["list"] = $res;
                $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
            } else {
                $result["list"] = array();
                $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
            }
            ajax_return($res, $request['language_type']);
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }


    //获取教师带班课时信息
    function getStafferClassHourView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            //访问日志
            $this->VeryLogs($pucArray['apiuser_id'], "{$request['u']}/{$request['t']}{$request['api']}", $request);

            $Model = new JiaowuModel();
            $res = $Model->getStafferClassHour($request);

            $result = array();
            if ($res['list'] || $res['outlist']) {
                $result["list"] = $res['list'];
                $result["outlist"] = $res['outlist'];
                $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
            } else {
                $result["list"] = array();
                $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
            }
            ajax_return($res, $request['language_type']);
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }


    //获取班外课时列表
    function getOutClassHourView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            //访问日志
            $this->VeryLogs($pucArray['apiuser_id'], "{$request['u']}/{$request['t']}{$request['api']}", $request);

            $schoolOne = $this->DataControl->getFieldOne("smc_school", "company_id,school_id", "school_branch = '{$request['school_branch']}'");
            if ($schoolOne) {
                $request['company_id'] = $schoolOne['company_id'];
                $request['school_id'] = $schoolOne['school_id'];
            }

            $Model = new JiaowuModel();
            $res = $Model->getOutClassHour($request);

            $field = array();
            $k = 0;
            $field[$k]["fieldstring"] = "staffer_cnname";
            $field[$k]["fieldname"] = "教师姓名";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "staffer_branch";
            $field[$k]["fieldname"] = "教师编号";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "staffer_postbe";
            $field[$k]["fieldname"] = "职务";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "staffer_leave";
            $field[$k]["fieldname"] = "在职状态";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "staffer_isparttime";
            $field[$k]["fieldname"] = "职务类型";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "postbe_ismianjob";
            $field[$k]["fieldname"] = "是否主职";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "staffer_native";
            $field[$k]["fieldname"] = "籍贯";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "outclasstype_name";
            $field[$k]["fieldname"] = "课时类型";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "outclasstype_code";
            $field[$k]["fieldname"] = "课时分类";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "hour_day";
            $field[$k]["fieldname"] = "课时日期";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "hour_time";
            $field[$k]["fieldname"] = "课时时间";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "hour_scname";
            $field[$k]["fieldname"] = "所在校点";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "hour_remk";
            $field[$k]["fieldname"] = "教师备注";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;

            if (!empty($request['check_status'])) {
                $k++;
                $field[$k]["fieldstring"] = "exam_staffer_cnname";
                $field[$k]["fieldname"] = "审核人";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;

                $k++;
                $field[$k]["fieldstring"] = "hourcheck_examtime";
                $field[$k]["fieldname"] = "审核时间";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;

                $k++;
                $field[$k]["fieldstring"] = "hourcheck_examremk";
                $field[$k]["fieldname"] = "备注";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
            }

            $result = array();
            $result["field"] = $field;
            $result["allnum"] = $res['allnum'];
            if ($res['list']) {
                $result["fieldcustom"] = 1;
                $result["list"] = $res['list'];
                $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
            } else {
                $result["list"] = array();
                $result["allnum"] = 0;
                $res = array('error' => 1, 'errortip' => "暂无班外课时", 'result' => $result);
            }
            ajax_return($res, $request['language_type']);
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //获取班外课时类型
    function getOutClassTypeView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            //访问日志
            $this->VeryLogs($pucArray['apiuser_id'], "{$request['u']}/{$request['t']}{$request['api']}", $request);

            $datawhere = "company_id = '{$request['company_id']}'";
            if (isset($request['outclasstype_id']) && $request['outclasstype_id'] !== '') {
                $datawhere .= " and outclasstype_id = '{$request['outclasstype_id']}'";
            }

            $sql = "SELECT 
                        outclasstype_id,outclasstype_name,(CASE outclasstype_code WHEN 0 THEN '教学时数' ELSE '其它时数' END) as outclasstype_code
                    FROM 
                        smc_code_outclasstype
                    WHERE 
                        {$datawhere}";
            $dataList = $this->DataControl->selectClear($sql);

            $result = array();
            $result["list"] = $dataList;
            $res = array('error' => 0, 'errortip' => '获取班外课时类型信息', 'result' => $result);
            ajax_return($res, $request['language_type']);
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //新增班外课时
    function addOutClassHourAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            //访问日志
            $this->VeryLogs($pucArray['apiuser_id'], "{$request['u']}/{$request['t']}{$request['api']}", $request);

            $schoolOne = $this->DataControl->getFieldOne("smc_school", "company_id,school_id", "school_branch = '{$request['school_branch']}'");
            if ($schoolOne) {
                $request['company_id'] = $schoolOne['company_id'];
                $request['school_id'] = $schoolOne['school_id'];
            }
            $stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id", "staffer_branch = '{$request['teacher_branch']}'");
            if ($stafferOne) {
                $request['worker_id'] = $stafferOne['staffer_id'];
                $request['staffer_id'] = $stafferOne['staffer_id'];
            }
            $request['json_week'] = stripslashes($request['json_week']);

            $AffairsModel =  new \Model\Smc\AffairsModel($request);
            $res = $AffairsModel->classScheduleAction($request);

            ajax_return($res, $request['language_type']);
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //取消班外课时
    function cancelOutClassHourAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            //访问日志
            $this->VeryLogs($pucArray['apiuser_id'], "{$request['u']}/{$request['t']}{$request['api']}", $request);

            $schoolOne = $this->DataControl->getFieldOne("smc_school", "company_id,school_id", "school_branch = '{$request['school_branch']}'");
            if ($schoolOne) {
                $request['company_id'] = $schoolOne['company_id'];
                $request['school_id'] = $schoolOne['school_id'];
            }
            $stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id", "staffer_branch = '{$request['teacher_branch']}'");
            if ($stafferOne) {
                $request['staffer_id'] = $stafferOne['staffer_id'];
            }

            $AffairsModel =  new \Model\Smc\AffairsModel($request);

            $HourArray = json_decode(stripslashes(stripslashes($request['hour_list'])), 1);
            if ($HourArray) {
                foreach ($HourArray as $value) {
                    $res = $AffairsModel->delClassScheduleAction($value);
                }
            }
            ajax_return($res, $request['language_type']);
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //执行班外课时
    function executeOutClassHourAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            //访问日志
            $this->VeryLogs($pucArray['apiuser_id'], "{$request['u']}/{$request['t']}{$request['api']}", $request);

            $schoolOne = $this->DataControl->getFieldOne("smc_school", "company_id,school_id", "school_branch = '{$request['school_branch']}'");
            if ($schoolOne) {
                $request['company_id'] = $schoolOne['company_id'];
                $request['school_id'] = $schoolOne['school_id'];
            }
            $stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id", "staffer_branch = '{$request['teacher_branch']}'");
            if ($stafferOne) {
                $request['staffer_id'] = $stafferOne['staffer_id'];
            }

            $Model =  new JiaowuModel();

            $HourArray = json_decode(stripslashes(stripslashes($request['hour_list'])), 1);
            if ($HourArray) {
                foreach ($HourArray as $value) {
                    $request['hour_id'] = $value['hour_id'];
                    $Model->executeOutClassHourAction($request);
                }
            }

            $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
            ajax_return($res, $request['language_type']);
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }


    //转介绍招生明细表
    function referralClientlistView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            //访问日志
            $this->VeryLogs($pucArray['apiuser_id'], "{$request['u']}/{$request['t']}{$request['api']}", $request);

            $schoolOne = $this->DataControl->getFieldOne("smc_school", "company_id,school_id", "school_branch = '{$request['school_branch']}'");
            if ($schoolOne) {
                $request['company_id'] = $schoolOne['company_id'];
                $request['school_id'] = $schoolOne['school_id'];
            }
            $classOne = $this->DataControl->getFieldOne("smc_class", "class_id", "class_branch = '{$request['class_branch']}'");
            if ($classOne) {
                $request['class_id'] = $classOne['class_id'];
            }
            $coursetypeOne = $this->DataControl->getFieldOne("smc_code_coursetype", "coursetype_id", "company_id = '{$schoolOne['company_id']}' and coursetype_branch = '{$request['coursetype_branch']}'");
            if ($coursetypeOne) {
                $request['coursetype_id'] = $coursetypeOne['coursetype_id'];
            }
            $coursecatOne = $this->DataControl->getFieldOne("smc_code_coursecat", "coursecat_id", "company_id = '{$schoolOne['company_id']}' and coursecat_branch = '{$request['coursecat_branch']}'");
            if ($coursecatOne) {
                $request['coursecat_id'] = $coursecatOne['coursecat_id'];
            }

            $ReportModel = new JiaowuModel();
            $res = $ReportModel->referralClientlist($request);

            $field = array();
            $k = 0;
            $field[$k]["fieldstring"] = "rom_cnname";
            $field[$k]["fieldname"] = "学员姓名";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "rom_branch";
            $field[$k]["fieldname"] = "学员编号";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "client_createtime";
            $field[$k]["fieldname"] = "推荐日期";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            if (!$classOne) {
                $field[$k]["fieldstring"] = "romclass_cnname";
                $field[$k]["fieldname"] = "班级名称";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "romclass_branch";
                $field[$k]["fieldname"] = "班级编号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;
            }

            $field[$k]["fieldstring"] = "client_cnname";
            $field[$k]["fieldname"] = "新学员姓名";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "student_branch";
            $field[$k]["fieldname"] = "新学员编号";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "school_cnname";
            $field[$k]["fieldname"] = "推荐校区名称";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "school_branch";
            $field[$k]["fieldname"] = "推荐校区编号";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "client_mobile";
            $field[$k]["fieldname"] = "新学员联系方式";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "client_tracestatus";
            $field[$k]["fieldname"] = "推荐状态";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "pay_successtime";
            $field[$k]["fieldname"] = "报名时间";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "coursetype_cnname";
            $field[$k]["fieldname"] = "报名班组";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "pay_price";
            $field[$k]["fieldname"] = "首付金额";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;

            $result = array();
            $result["field"] = $field;
            $result["allnum"] = $res['allnum'];
            if (isset($request['is_export']) && $request['is_export'] == 1) {
                $res = array('error' => $ReportModel->error, 'errortip' => $ReportModel->errortip, 'result' => $result);
                ajax_return($res, $request['language_type']);
            }

            if ($res['list']) {
                $result["fieldcustom"] = 1;
                $result["list"] = $res['list'];
                $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
            } else {
                $result["list"] = array();
                $result["allnum"] = 0;
                $res = array('error' => 1, 'errortip' => "暂无转介绍学员", 'result' => $result);
            }
            ajax_return($res, $request['language_type']);
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //获取学员课程余额与课次数
    function getCourseBalanceView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            //访问日志
            $this->VeryLogs($pucArray['apiuser_id'], "{$request['u']}/{$request['t']}{$request['api']}", $request);

            $classOne = $this->DataControl->getFieldOne("smc_class", "school_id,course_id,class_fullnums", "class_branch = '{$request['class_branch']}'");
            $studentOne = $this->DataControl->getFieldOne("smc_student", "student_id", "student_branch = '{$request['student_branch']}'");

            $courseblance = $this->DataControl->selectOne("SELECT coursebalance_figure,coursebalance_time FROM smc_student_coursebalance WHERE school_id = '{$classOne['school_id']}' and course_id = '{$classOne['course_id']}' and student_id = '{$studentOne['student_id']}'");
            $courseblance['class_fullnums'] = $classOne['class_fullnums'];

            $result = array();
            $result["list"] = $courseblance;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
            ajax_return($res, $request['language_type']);
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    function getClassLessonplanView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            //访问日志
            $this->VeryLogs($pucArray['apiuser_id'], "{$request['u']}/{$request['t']}{$request['api']}", $request);

            $classOne = $this->DataControl->getFieldOne("smc_class", "class_id", "class_branch = '{$request['class_branch']}'");

            $classlessonList = $this->DataControl->selectClear("SELECT l.lessonplan_week, l.lessonplan_starttime, l.lessonplan_endtime,s.staffer_cnname,s.staffer_enname
                                                            FROM smc_class_lessonplan AS l
                                                            LEFT JOIN smc_staffer AS s ON s.staffer_id = l.staffer_id
                                                            WHERE l.class_id = '{$classOne['class_id']}'");
            $cnteacher = array();
            $classtimestr = "";
            if ($classlessonList) {
                foreach ($classlessonList as $classlessonOne) {
                    $cnteacher[] = $classlessonOne['staffer_enname'] ? $classlessonOne['staffer_cnname'] . '-' . $classlessonOne['staffer_enname'] : $classlessonOne['staffer_cnname'];
                    $classtimestr .= $classlessonOne['lessonplan_week'] . " " . $classlessonOne['lessonplan_starttime'] . "~" . $classlessonOne['lessonplan_endtime'] . "<br>";
                }

                $cnteacher = implode("<br>", array_unique($cnteacher));
            }
            $result = array();
            $result["list"]['cnteacher'] = $cnteacher;
            $result["list"]['classtimestr'] = $classtimestr;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
            ajax_return($res, $request['language_type']);
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //获取节假日
    function getHolidaysView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            //访问日志
            $this->VeryLogs($pucArray['apiuser_id'], "{$request['u']}/{$request['t']}{$request['api']}", $request);

            $schoolOne = $this->DataControl->getFieldOne("smc_school", "company_id,school_id", "school_branch = '{$request['school_branch']}'");

            $sql = "SELECT
                    ch.holidays_day 
                FROM
                    smc_code_holidays AS ch 
                WHERE
                    ch.company_id = '{$schoolOne['company_id']}' 
                    AND ch.holidays_day >= '{$request['start']}' 
                    AND (
                        ( ch.school_id = '{$schoolOne['school_id']}' AND ch.holidays_status = '0' ) 
                        OR (
                            ch.school_id = '0' 
                            AND ch.holidays_status = '0' 
                            AND ch.company_id = '{$schoolOne['company_id']}' 
                            AND ch.holidays_day NOT IN (
                            SELECT
                                h.holidays_day 
                            FROM
                                smc_code_holidays AS h 
                            WHERE
                                h.company_id = '{$schoolOne['company_id']}' 
                                AND h.school_id = '{$schoolOne['school_id']}' 
                                AND h.holidays_status = '1' 
                            ) 
                        ) 
                    )";
            $holidaysList = $this->DataControl->selectClear($sql);

            $result = array();
            $result["list"] = $holidaysList;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
            ajax_return($res, $request['language_type']);
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }


    //获取学员电访记录
    function getStuCatitrackView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            //访问日志
            $this->VeryLogs($pucArray['apiuser_id'], "{$request['u']}/{$request['t']}{$request['api']}", $request);

            $schoolOne = $this->DataControl->getFieldOne("smc_school", "company_id,school_id", "school_branch = '{$request['school_branch']}'");
            if ($schoolOne) {
                $request['company_id'] = $schoolOne['company_id'];
                $request['school_id'] = $schoolOne['school_id'];
            }
            $coursetypeOne = $this->DataControl->getFieldOne("smc_code_coursetype", "coursetype_id", "company_id = '{$schoolOne['company_id']}' and coursetype_branch = '{$request['coursetype_branch']}'");
            if ($coursetypeOne) {
                $request['coursetype_id'] = $coursetypeOne['coursetype_id'];
            }
            $coursecatOne = $this->DataControl->getFieldOne("smc_code_coursecat", "coursecat_id", "company_id = '{$schoolOne['company_id']}' and coursecat_branch = '{$request['coursecat_branch']}'");
            if ($coursecatOne) {
                $request['coursecat_id'] = $coursecatOne['coursecat_id'];
            }
            $classOne = $this->DataControl->getFieldOne("smc_class", "class_id", "class_branch = '{$request['class_branch']}'");
            if ($classOne) {
                $request['class_id'] = $classOne['class_id'];
            }
            $studentOne = $this->DataControl->getFieldOne("smc_student", "student_id", "student_branch = '{$request['student_branch']}'");
            if ($studentOne) {
                $request['student_id'] = $studentOne['student_id'];
            }

            $Model = new JiaowuModel();
            $dataList = $Model->getStuCatitrack($request);

            $result = array();
            if ($dataList["list"]) {
                $result["list"] = $dataList['list'];
                $result["coursetypelist"] = $dataList['coursetypelist'];
                $res = array('error' => '0', 'errortip' => '获取电访记录', 'result' => $result);
            } else {
                $result["list"] = array();
                $result["coursetypelist"] = array();
                $res = array('error' => '1', 'errortip' => '暂无电访记录', 'result' => $result);
            }
            ajax_return($res, $request['language_type']);
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //新增电访记录
    function addStuCatitrackApi()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            //访问日志
            $this->VeryLogs($pucArray['apiuser_id'], "{$request['u']}/{$request['t']}{$request['api']}", $request);

            $schoolOne = $this->DataControl->getFieldOne("smc_school", "company_id,school_id", "school_branch = '{$request['school_branch']}'");
            if ($schoolOne) {
                $request['company_id'] = $schoolOne['company_id'];
                $request['school_id'] = $schoolOne['school_id'];
            }
            $coursetypeOne = $this->DataControl->getFieldOne("smc_code_coursetype", "coursetype_id", "company_id = '{$schoolOne['company_id']}' and coursetype_branch = '{$request['coursetype_branch']}'");
            if ($coursetypeOne) {
                $request['coursetype_id'] = $coursetypeOne['coursetype_id'];
            }
            $coursecatOne = $this->DataControl->getFieldOne("smc_code_coursecat", "coursecat_id", "company_id = '{$schoolOne['company_id']}' and coursecat_branch = '{$request['coursecat_branch']}'");
            if ($coursecatOne) {
                $request['coursecat_id'] = $coursecatOne['coursecat_id'];
            }
            $classOne = $this->DataControl->getFieldOne("smc_class", "class_id", "class_branch = '{$request['class_branch']}'");
            if ($classOne) {
                $request['class_id'] = $classOne['class_id'];
            }
            $stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id", "staffer_branch = '{$request['teacher_branch']}'");
            if ($stafferOne) {
                $request['staffer_id'] = $stafferOne['staffer_id'];
            }
            $studentOne = $this->DataControl->getFieldOne("smc_student", "student_id", "student_branch = '{$request['student_branch']}'");
            if ($studentOne) {
                $request['student_id'] = $studentOne['student_id'];
            }

            $Model = new JiaowuModel();
            $result = $Model->addStuCatitrackAction($request);

            $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $result);
            ajax_return($res, $request['language_type']);
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //学员续费电访
    function getStuRenewalTrackApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            //访问日志
            $this->VeryLogs($pucArray['apiuser_id'], "{$request['u']}/{$request['t']}{$request['api']}", $request);

            $schoolOne = $this->DataControl->getFieldOne("smc_school", "company_id,school_id", "school_branch = '{$request['school_branch']}'");
            $classOne = $this->DataControl->getFieldOne("smc_class", "class_id", "class_branch = '{$request['class_branch']}'");
            $studentOne = $this->DataControl->getFieldOne("smc_student", "student_id", "student_branch = '{$request['student_branch']}'");

            $sql = "SELECT 100 as coursetype_renewtimes,
    IFNULL((SELECT rs.trackresult_name FROM smc_student_track st,smc_code_trackresult rs WHERE rs.trackresult_id = st.result_id AND st.student_id = a.student_id AND st.school_id = a.school_id 
  AND (st.coursetype_id = 0 OR st.coursetype_id = e.coursetype_id) AND st.track_from = 0 AND st.track_classname = '续费电访' AND st.track_day >= c.class_stdate ORDER BY st.track_id DESC LIMIT 1), '') AS connect_times,
    IFNULL((SELECT sum(coursebalance_time) FROM smc_student_coursebalance X, smc_course Z WHERE X.company_id = a.company_id AND X.student_id = a.student_id AND X.course_id = Z.course_id AND Z.coursetype_id = e.coursetype_id AND X.course_id <> c.course_id), 0) AS renewal_times,
    IFNULL((SELECT count(x.income_id) FROM smc_school_income x, smc_course Y WHERE x.course_id = Y.course_id AND x.student_id = a.student_id AND x.company_id = a.company_id AND X.income_type = '0' AND x.income_confirmtime >= UNIX_TIMESTAMP(a.study_endday) AND y.coursetype_id = e.coursetype_id AND x.class_id <> a.class_id), 0) AS spend_times,
    IFNULL((SELECT sum(unpaid_times) FROM smc_student_unpaid WHERE school_id = a.school_id AND student_id = a.student_id AND coursetype_id = e.coursetype_id), 0) AS unpaid_times,
    IFNULL((SELECT sum(y.course_classtimerates) FROM smc_school_income x, smc_course Y WHERE x.course_id = Y.course_id AND x.student_id = a.student_id AND x.company_id = a.company_id AND X.income_type = '0' AND x.income_confirmtime >= UNIX_TIMESTAMP(a.study_endday) AND y.coursetype_id = e.coursetype_id AND x.class_id <> a.class_id), 0) AS spend_rates,
    IFNULL((SELECT sum(unpaid_timesrate) FROM smc_student_unpaid WHERE school_id = a.school_id AND student_id = a.student_id AND coursetype_id = e.coursetype_id), 0) AS unpaid_rates,
    IFNULL((SELECT sum( coursebalance_time * z.course_classtimerates ) FROM smc_student_coursebalance X, smc_course Z WHERE X.company_id = a.company_id AND X.student_id = a.student_id AND X.course_id = Z.course_id AND Z.coursetype_id = e.coursetype_id AND X.course_id <> c.course_id), 0) AS renewal_rates,
    IFNULL((SELECT sum(X.coursebalance_figure) FROM smc_student_coursebalance X, smc_course Z WHERE X.company_id = a.company_id AND X.student_id = a.student_id AND X.course_id = Z.course_id AND Z.coursetype_id = e.coursetype_id AND X.course_id <> c.course_id), 0) AS renewal_amount,
    IFNULL((SELECT sum(income_price) FROM smc_school_income x, smc_course Y WHERE x.course_id = Y.course_id AND x.company_id = a.company_id AND x.student_id = a.student_id AND X.income_type = '0' AND x.income_confirmtime >= UNIX_TIMESTAMP(a.study_endday) AND y.coursetype_id = e.coursetype_id AND x.class_id <> a.class_id), 0) AS spend_price,
    IFNULL((SELECT sum(all_price - pay_price) FROM smc_student_unpaid WHERE school_id = a.school_id AND student_id = a.student_id AND coursetype_id = e.coursetype_id), 0) AS unpaid_price
FROM 
    smc_student_study a 
    LEFT JOIN smc_class c ON c.class_id = a.class_id
    LEFT JOIN smc_school s ON s.school_id = a.school_id
    LEFT JOIN smc_course e ON e.course_id = c.course_id and e.company_id = a.company_id
WHERE
    a.company_id = '{$schoolOne['company_id']}' AND a.school_id = '{$schoolOne['school_id']}' AND c.class_id = '{$classOne['class_id']}' AND a.student_id = '{$studentOne['student_id']}'
  AND c.class_status <> '-2' AND c.class_type = '0' AND e.course_sellclass = 0 AND s.school_isclose<> '1' AND e.course_classnum > 10 AND a.study_endday >= c.class_enddate AND a.study_isreading=1";
            $studyOne = $this->DataControl->selectOne($sql);
            if ($studyOne) {
                $studyOne['estimate_price'] = $studyOne['renewal_amount'] - $studyOne['unpaid_price'] + $studyOne['spend_price'];
                $studyOne['renewal_times'] = $studyOne['renewal_times'] + $studyOne['spend_times'] - $studyOne['unpaid_times'];
                $studyOne['renewal_rates'] = $studyOne['renewal_rates'] + $studyOne['spend_rates'] - $studyOne['unpaid_rates'];
                $studyOne['is_renewal'] = ($studyOne['renewal_rates'] >= $studyOne['coursetype_renewtimes'] || $studyOne['connect_times'] == '续费') ? "是" : "否";
            }

            $result = array();
            if ($studyOne) {
                $result["list"] = $studyOne;
                $res = array('error' => '0', 'errortip' => '获取学员续费电访信息', 'result' => $result);
            } else {
                $result["list"] = array();
                $res = array('error' => '1', 'errortip' => '暂无学员续费电访信息', 'result' => $result);
            }
            ajax_return($res, $request['language_type']);
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }


    //班级学生的请假记录
    function getClassStudentAbsenceApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            //访问日志
            $this->VeryLogs($pucArray['apiuser_id'], "{$request['u']}/{$request['t']}{$request['api']}", $request);

            $classOne = $this->DataControl->getFieldOne("smc_class", "class_id,company_id,school_id", "class_branch = '{$request['class_branch']}'");
            if ($classOne) {
                $request['company_id'] = $classOne['company_id'];
                $request['school_id'] = $classOne['school_id'];
                $request['class_id'] = $classOne['class_id'];
            } else {
                $res = array('error' => 1, 'errortip' => '请传入正确的班级编号', 'result' => array());
                ajax_return($res);
            }

            $StudentModel = new \Model\Smc\StudentModel($request);
            $res = $StudentModel->getClassStudentAbsence($request);

            $field = array();
            $key = 0;
            $field[$key]["fieldstring"] = "student_name";
            $field[$key]["fieldname"] = "姓名";
            $field[$key]["show"] = 1;
            $field[$key]["custom"] = 1;
            $key++;

            $field[$key]["fieldstring"] = "student_branch";
            $field[$key]["fieldname"] = "学员编号";
            $field[$key]["show"] = 1;
            $field[$key]["custom"] = 1;
            $key++;

            $field[$key]["fieldstring"] = "apply_time";
            $field[$key]["fieldname"] = "申请时间";
            $field[$key]["show"] = 1;
            $field[$key]["custom"] = 1;
            $key++;

            $field[$key]["fieldstring"] = "absence_type_name";
            $field[$key]["fieldname"] = "请假类型";
            $field[$key]["show"] = 1;
            $field[$key]["custom"] = 1;
            $key++;

            $field[$key]["fieldstring"] = "absence_time";
            $field[$key]["fieldname"] = "开始-结束时间";
            $field[$key]["show"] = 1;
            $field[$key]["custom"] = 1;
            $key++;

            $field[$key]["fieldstring"] = "absence_day";
            $field[$key]["fieldname"] = "请假天数";
            $field[$key]["show"] = 1;
            $field[$key]["custom"] = 1;
            $key++;

            $field[$key]["fieldstring"] = "absence_reasonnote";
            $field[$key]["fieldname"] = "请假原因";
            $field[$key]["show"] = 1;
            $field[$key]["custom"] = 1;
            $key++;

            $field[$key]["fieldstring"] = "absence_status_name";
            $field[$key]["fieldname"] = "审批状态";
            $field[$key]["show"] = 1;
            $field[$key]["custom"] = 1;
            $key++;

            $field[$key]["fieldstring"] = "approval_time";
            $field[$key]["fieldname"] = "审批时间";
            $field[$key]["show"] = 1;
            $field[$key]["custom"] = 1;
            $key++;

            $field[$key]["fieldstring"] = "approval_applynote";
            $field[$key]["fieldname"] = "原因备注";
            $field[$key]["show"] = 1;
            $field[$key]["custom"] = 1;

            $result = array();
            $result["field"] = $field;
            $result["allnum"] = $res['allnum'];
            if ($res['list']) {
                $result["list"] = $res['list'];
                $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
            } else {
                $result["list"] = array();
                $result["allnum"] = 0;
                $res = array('error' => 1, 'errortip' => '暂无请假记录', 'result' => $result);
            }
            ajax_return($res, $request['language_type']);
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //审批通过/拒绝/撤销
    function approvedAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            //访问日志
            $this->VeryLogs($pucArray['apiuser_id'], "{$request['u']}/{$request['t']}{$request['api']}", $request);

            $request['absence_list'] = stripslashes($request['absence_list']);

            $StudentModel = new \Model\Smc\StudentModel($request);
            $res = $StudentModel->approved($request);
            $result = array();
            if ($res) {
                $result["list"] = $res;
                $res = array('error' => 0, 'errortip' => '审批通过', 'result' => $result);
            } else {
                $result["list"] = array();
                $res = array('error' => 1, 'errortip' => $StudentModel->errortip, 'result' => $result);
            }
            ajax_return($res, $request['language_type']);
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }


    //流失学员
    function getlostChangeListApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            //访问日志
            $this->VeryLogs($pucArray['apiuser_id'], "{$request['u']}/{$request['t']}{$request['api']}", $request);

            $schoolOne = $this->DataControl->getFieldOne("smc_school", "company_id,school_id", "school_branch = '{$request['school_branch']}'");
            if ($schoolOne) {
                $request['company_id'] = $schoolOne['company_id'];
                $request['school_id'] = $schoolOne['school_id'];
            }
            $stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id,account_class", "staffer_branch = '{$request['teacher_branch']}'");
            if ($stafferOne) {
                $request['staffer_id'] = $stafferOne['staffer_id'];
            }
            $coursetypeOne = $this->DataControl->getFieldOne("smc_code_coursetype", "coursetype_id", "company_id = '{$schoolOne['company_id']}' and coursetype_branch = '{$request['coursetype_branch']}'");
            if ($coursetypeOne) {
                $request['coursetype_id'] = $coursetypeOne['coursetype_id'];
            }

            $ChangeModel = new JiaowuModel();
            if (isset($request["change_type"]) && $request["change_type"] == '1') {
                $res = $ChangeModel->getCourseTypeLossStudentList($request);
            } else {
                $res = $ChangeModel->getLossStudentList($request);
            }

            $field = array();
            $k = 0;
            /*$field[$k]["fieldstring"] = "student_id";
            $field[$k]["fieldname"] = "学员ID";
            $field[$k]["show"] = 0;
            $field[$k]["custom"] = 0;
            $k++;*/

            $field[$k]["fieldstring"] = "student_cnname";
            $field[$k]["fieldname"] = "姓名";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $field[$k]["fieldwidth"] = 120;
            $k++;

            $field[$k]["fieldstring"] = "student_branch";
            $field[$k]["fieldname"] = "学员编号";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            /*$field[$k]["fieldstring"] = "student_birthday";
            $field[$k]["fieldname"] = "出生日期";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;*/

            /*$field[$k]["fieldstring"] = "parenter_cnname";
            $field[$k]["fieldname"] = "主要联系人";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;*/

            $field[$k]["fieldstring"] = "parenter_mobile";
            $field[$k]["fieldname"] = "主要联系电话";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "track_num";
            $field[$k]["fieldname"] = "跟踪次数";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = 'track_callid';
            $field[$k]["fieldname"] = "外呼录音";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $field[$k]["huijieAudioVisible"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "track_note";
            $field[$k]["fieldname"] = "最后流失电访内容";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "coursetype_cnname";
            $field[$k]["fieldname"] = empty($request["change_type"]) ? "跟进班组" : "流失班组";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            if (empty($request["change_type"])) {
                $field[$k]["fieldstring"] = "coursecat_cnname";
                $field[$k]["fieldname"] = "跟进班种";
            } else {
                $field[$k]["fieldstring"] = "changelog_category";
                $field[$k]["fieldname"] = "流失类型";
            }
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "staffer_name";
            $field[$k]["fieldname"] = "最后跟进人";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "changelog_day";
            $field[$k]["fieldname"] = "流失日期";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "last_atte_date";
            $field[$k]["fieldname"] = "最后考勤日期";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "class_cnname";
            $field[$k]["fieldname"] = "最后考勤班级";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            if (empty($request["change_type"])) {
                $field[$k]["fieldstring"] = "pay_firsttime";
                $field[$k]["fieldname"] = "首次缴费日期";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;
            }

            $field[$k]["fieldstring"] = "changelog_note";
            $field[$k]["fieldname"] = "流失原因";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            /*$field[$k]["fieldstring"] = "track_picturejson";
            $field[$k]["fieldname"] = "沟通图片";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $field[$k]["clickVisible"] = 1;*/

            $result = array();
            $result["field"] = $field;
            if ($res['list']) {
                $result["list"] = $res['list'];
                $result["allnum"] = $res['allnum'];
                $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
            } else {
                $result["list"] = array();
                $result["allnum"] = 0;
                $res = array('error' => 1, 'errortip' => '暂无流失学员明细', 'result' => $result);
            }
            ajax_return($res, $request['language_type']);
        } else{
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }


    //流失回读学员
    function getLossReadBackStuApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            //访问日志
            $this->VeryLogs($pucArray['apiuser_id'], "{$request['u']}/{$request['t']}{$request['api']}", $request);

            $schoolOne = $this->DataControl->getFieldOne("smc_school", "company_id,school_id", "school_branch = '{$request['school_branch']}'");
            if ($schoolOne) {
                $request['company_id'] = $schoolOne['company_id'];
                $request['school_id'] = $schoolOne['school_id'];
            }
            $coursecatOne = $this->DataControl->getFieldOne("smc_code_coursecat", "coursecat_id", "company_id = '{$schoolOne['company_id']}' and coursecat_branch = '{$request['coursecat_branch']}'");
            if ($coursecatOne) {
                $request['coursecat_id'] = $coursecatOne['coursecat_id'];
            }
            $stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id,account_class", "staffer_branch = '{$request['teacher_branch']}'");
            if ($stafferOne) {
                $request['staffer_id'] = $stafferOne['staffer_id'];
            }

            $ChangeModel = new JiaowuModel();
            $res = $ChangeModel->getLossReadBackStuApi($request);

            $field = array();
            $k = 0;
            $field[$k]["fieldstring"] = "student_cnname";
            $field[$k]["fieldname"] = "姓名";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $field[$k]["fieldwidth"] = 120;
            $k++;

            $field[$k]["fieldstring"] = "student_branch";
            $field[$k]["fieldname"] = "学员编号";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "loss_coursecat_cnname";
            $field[$k]["fieldname"] = "流失班种";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "loss_hour_day";
            $field[$k]["fieldname"] = "流失最后耗课日期";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "loss_class_branch";
            $field[$k]["fieldname"] = "流失班级编号";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "loss_class_enddate";
            $field[$k]["fieldname"] = "流失班级结班日期";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "loss_staffer_name";
            $field[$k]["fieldname"] = "流失班级中籍主教";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "is_refundloss";
            $field[$k]["fieldname"] = "是否退费流失";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "changelog_note";
            $field[$k]["fieldname"] = "流失原因";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "readback_coursecat_cnname";
            $field[$k]["fieldname"] = "回读班种";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "readback_study_beginday";
            $field[$k]["fieldname"] = "回读入班日期";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "readback_class_branch";
            $field[$k]["fieldname"] = "回读班级编号";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "readback_staffer_name";
            $field[$k]["fieldname"] = "回读中籍主教";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "readback_order_paymentprice";
            $field[$k]["fieldname"] = "回读缴费金额";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;

            $result = array();
            $result["field"] = $field;
            if ($res['list']) {
                $result["list"] = $res['list'];
                $result["allnum"] = $res['allnum'];
                $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
            } else {
                $result["list"] = array();
                $result["allnum"] = 0;
                $res = array('error' => 1, 'errortip' => '暂无流失回读学员明细', 'result' => $result);

            }
            ajax_return($res, $request['language_type']);
        } else{
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //获取学员所在班级列表
    function getStudentClassApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            //访问日志
            $this->VeryLogs($pucArray['apiuser_id'], "{$request['u']}/{$request['t']}{$request['api']}", $request);

            $schoolOne = $this->DataControl->getFieldOne("smc_school", "company_id,school_id", "school_branch = '{$request['school_branch']}'");
            if ($schoolOne) {
                $request['company_id'] = $schoolOne['company_id'];
                $request['school_id'] = $schoolOne['school_id'];
            }
            $coursetypeOne = $this->DataControl->getFieldOne("smc_code_coursetype", "coursetype_id", "company_id = '{$schoolOne['company_id']}' and coursetype_branch = '{$request['coursetype_branch']}'");
            if ($coursetypeOne) {
                $request['coursetype_id'] = $coursetypeOne['coursetype_id'];
            }
            $coursecatOne = $this->DataControl->getFieldOne("smc_code_coursecat", "coursecat_id", "company_id = '{$schoolOne['company_id']}' and coursecat_branch = '{$request['coursecat_branch']}'");
            if ($coursecatOne) {
                $request['coursecat_id'] = $coursecatOne['coursecat_id'];
            }
            $stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id", "staffer_branch = '{$request['teacher_branch']}'");
            if ($stafferOne) {
                $request['staffer_id'] = $stafferOne['staffer_id'];
            }
            $request['class_type'] = '["0"]';

            $StudentModel = new \Model\Crm\CrmStudentModel($request);
            $res = $StudentModel->getCrmStuClass($request);

            $field = array();
            $k = 0;
            $field[$k]["fieldstring"] = "class_cnname";
            $field[$k]["fieldname"] = "班级名称";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "class_enname";
            $field[$k]["fieldname"] = "班级别名";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 0;
            $k++;

            $field[$k]["fieldstring"] = "class_branch";
            $field[$k]["fieldname"] = "班级编号";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 0;
            $field[$k]["haveDetail"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "course_cnname";
            $field[$k]["fieldname"] = "课程别名称";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "course_branch";
            $field[$k]["fieldname"] = "课程别编号";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 0;
            $k++;

            $field[$k]["fieldstring"] = "coursetype_cnname";
            $field[$k]["fieldname"] = "所属班组";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 0;
            $k++;

            $field[$k]["fieldstring"] = "class_status_name";
            $field[$k]["fieldname"] = "班级状态";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 0;
            $k++;

            $field[$k]["fieldstring"] = "study_isreading_name";
            $field[$k]["fieldname"] = "在班状态";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 0;
            $k++;

            $field[$k]["fieldstring"] = "hour_checkingnum";
            $field[$k]["fieldname"] = "班级上课课时";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 0;
            $k++;

            $field[$k]["fieldstring"] = "hourstudy_num";
            $field[$k]["fieldname"] = "学员已耗课次";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 0;
            $k++;

            $field[$k]["fieldstring"] = "study_during";
            $field[$k]["fieldname"] = "在班日期";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 0;

            $result = array();
            $result["field"] = $field;
            if ($res['classList']) {
                $result["fieldcustom"] = 0;
                $result["list"] = $res['classList'];
                $result["allnum"] = count($res['classList']);
                $result["coursetypeList"] = $res['coursetypeList'];
                $result["coursecatList"] = $res['coursecatList'];
                $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
            } else {
                $result["list"] = array();
                $result["allnum"] = 0;
                $result["coursetypeList"] = array();
                $result["coursecatList"] = array();
                $res = array('error' => 1, 'errortip' => '学员暂无入班信息', 'result' => $result);
            }
            ajax_return($res, $request['language_type']);
        } else{
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //试听学员明细
    function getClientAuditionListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            //访问日志
            $this->VeryLogs($pucArray['apiuser_id'], "{$request['u']}/{$request['t']}{$request['api']}", $request);

            $classOne = $this->DataControl->getFieldOne("smc_class", "class_id,company_id,school_id", "class_branch = '{$request['class_branch']}'");
            if ($classOne) {
                $request['company_id'] = $classOne['company_id'];
                $request['school_id'] = $classOne['school_id'];
                $request['class_id'] = $classOne['class_id'];
            } else {
                $res = array('error' => 1, 'errortip' => '请传入正确的班级编号', 'result' => array());
                ajax_return($res);
            }

            $Model = new JiaowuModel();
            $res = $Model->getClientauditionList($request);

            $k = 0;
            $field = array();
            $field[$k]["fieldstring"] = "client_cnname";
            $field[$k]["fieldname"] = "学员姓名";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "client_branch";
            $field[$k]["fieldname"] = "学生编号";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "client_sex";
            $field[$k]["fieldname"] = "性别";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "client_birthday";
            $field[$k]["fieldname"] = "出生日期";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "client_mobile";
            $field[$k]["fieldname"] = "联系方式";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "audition_visittime";
            $field[$k]["fieldname"] = "试听时间";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "client_tracestatus";
            $field[$k]["fieldname"] = "客户状态";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "pay_successtime";
            $field[$k]["fieldname"] = "报名时间";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "coursetype_cnname";
            $field[$k]["fieldname"] = "报名班组";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "pay_price";
            $field[$k]["fieldname"] = "首付金额";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;


            $result = array();
            $result["field"] = $field;
            if ($res['list']) {
                $result["list"] = $res['list'];
                $result["allnum"] = $res['allnum'];
                $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
            } else {
                $result["list"] = array();
                $result["allnum"] = 0;
                $res = array('error' => 1, 'errortip' => "暂无试听学员明细", 'result' => $result);
            }
            ajax_return($res, $request['language_type']);
        } else{
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //学员实际留班 -- 刷新 --  教务系统
    function studEndcalcEstimateRefreshView(){
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            //访问日志
            $this->VeryLogs($pucArray['apiuser_id'], "{$request['u']}/{$request['t']}{$request['api']}", $request);


            $Model = new  \Model\Api\ClassendcalcModel();
            $request['company_id'] = '8888';

            if (!isset($request['class_branch']) || $request['class_branch'] == '') {
                ajax_return(array('error' => '1', 'errortip' => '传班级编号', 'result' => false));
            }
            $sql = "select a.endcalc_id,endcalc_issettle from smc_class_endcalc a,smc_class b 
        where a.class_id=b.class_id and b.class_branch='{$request['class_branch']}'";

            $calcOne = $this->DataControl->selectOne($sql);
            if (!$calcOne) {
                ajax_return(array('error' => '1', 'errortip' => '未查询到结算数据，确认班级编号和结班日期', 'result' => false));
            }

            if ($calcOne['endcalc_issettle'] >= 1) {
                ajax_return(array('error' => '1', 'errortip' => '班级留班率已固定，单独修改错误学生', 'result' => false));
            }
            $request['endcalc_id'] = $calcOne['endcalc_id'];
            $bool = $Model->classNewSettle($request);
            if ($bool) {
                ajax_return(array('error' => '0', 'errortip' => $Model->oktip, 'result' => $request),$request['language_type']);
            } else {
                ajax_return(array('error' => '1', 'errortip' => $Model->errortip, 'result' => false),$request['language_type']);
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //获取课叮铛校务系统 学校端 学员实际留班率 --- 教务系统
    function studEndcalcEstimateView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            //访问日志
            $this->VeryLogs($pucArray['apiuser_id'], "{$request['u']}/{$request['t']}{$request['api']}", $request);

            $field = array();
            $k = 0;
//            $field[$k]["fieldstring"] = "school_id";
//            $field[$k]["fieldname"] = "学校id";
//            $field[$k]["show"] = 0;
//            $field[$k]["custom"] = 0;
//            $k++;
//
//            $field[$k]["fieldstring"] = "student_id";
//            $field[$k]["fieldname"] = "学员id";
//            $field[$k]["show"] = 0;
//            $field[$k]["custom"] = 0;
//            $k++;

            $field[$k]["fieldstring"] = "student_branch";
            $field[$k]["fieldname"] = "学员编号";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "student_cnname";
            $field[$k]["fieldname"] = "学员中文名";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "student_enname";
            $field[$k]["fieldname"] = "学员英文名";
            $field[$k]["show"] = 0;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "channel_name";
            $field[$k]["fieldname"] = "专案名称";
            $field[$k]["show"] = (isset($request["company_id"]) && $request["company_id"] == '8888') ? 1 : 0;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "is_renewal";
            $field[$k]["fieldname"] = "是否续费";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "study_nexttimes";
            $field[$k]["fieldname"] = "续费课次";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "study_nextprice";
            $field[$k]["fieldname"] = "续费金额";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "study_upgraderate";
            $field[$k]["fieldname"] = "留班课时比";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

//            $field[$k]["fieldstring"] = "connect_times";studEndcalcEstimate
//            $field[$k]["fieldname"] = "电访结果";
//            $field[$k]["show"] = 0;
//            $field[$k]["custom"] = 1;
//            $k++;



            $ReportModel = new \Model\Report\Smc\FinanceReportModel($request);
            $dataList = $ReportModel->studEndcalcEstimateSummary($request);

            $result = array();
            $result["field"] = $field;
            $result["allnum"] = $dataList['allnum'];
            if ($dataList['list']) {
                $result["fieldcustom"] = 1;
                $result["list"] = $dataList['list'];
                $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
            } else {
                $result["list"] = array();
                $result["allnum"] = 0;
                $res = array('error' => 1, 'errortip' => "暂无学员续费预估信息", 'result' => $result);
            }
            ajax_return($res, $request['language_type']);
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //获取班级课表
    function classTimetableApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            //访问日志
            $this->VeryLogs($pucArray['apiuser_id'], "{$request['u']}/{$request['t']}{$request['api']}", $request);

            $field = array();
            $k = 0;
            $field[$k]["fieldstring"] = "hour_day";
            $field[$k]["fieldname"] = "上课日期";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 0;
            $k++;
            $field[$k]["custom"] = 0;
            $field[$k]["fieldstring"] = "week_day";
            $field[$k]["fieldname"] = "上课周次";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 0;
            $k++;
            $field[$k]["fieldstring"] = "hour_way_name";
            $field[$k]["fieldname"] = "上课方式";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 0;
            $k++;
            $field[$k]["fieldstring"] = "hour_timesection";
            $field[$k]["fieldname"] = "上课时间";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 0;
            $k++;
            $field[$k]["fieldstring"] = "classroom_cnname";
            $field[$k]["fieldname"] = "上课教室";
            $field[$k]["show"] = 1;
            $field[$k]["isShowWay"] = 1;
            $field[$k]["custom"] = 0;
            $k++;
            $field[$k]["fieldstring"] = "staffer_cnname";
            $field[$k]["fieldname"] = "主教教师";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 0;
            $k++;
            $field[$k]["fieldstring"] = "re_staffer_cnname";
            $field[$k]["fieldname"] = "助教教师";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 0;
            $k++;
            $field[$k]["fieldstring"] = "hour_lessontimes";
            $field[$k]["fieldname"] = "课次";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 0;
            $k++;
            $field[$k]["fieldstring"] = "hour_ischecking_name";
            $field[$k]["fieldname"] = "是否考勤";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 0;
            $k++;
            $field[$k]["fieldstring"] = "hour_isfree";
            $field[$k]["fieldname"] = "是否计费";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;
            $field[$k]["fieldstring"] = "hour_iswarming_name";
            $field[$k]["fieldname"] = "课次类型";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 0;

            $result = array();
            $result['field'] = $field;
            $classOne = $this->DataControl->getFieldOne("smc_class", "class_id,company_id,school_id", "class_branch = '{$request['class_branch']}'");
            if ($classOne) {
                $request['company_id'] = $classOne['company_id'];
                $request['school_id'] = $classOne['school_id'];
                $request['class_id'] = $classOne['class_id'];
            } else {
                $result["list"] = array();
                $result["allnum"] = 0;
                $res = array('error' => 1, 'errortip' => '班级暂无课表信息', 'result' => $result);
                ajax_return($res, $request['language_type']);
            }

            $ClassModel = new \Model\Smc\ClassModel($request);
            $dataList = $ClassModel->classhourtable($request);

            if ($dataList['list']) {
                $result["list"] = $dataList['list'];
                $result["allnum"] = $dataList['allnum'];
                $result["class_type"] = $dataList['class_type'];
                $res = array('error' => 0, 'errortip' => '获取班级课表信息', 'result' => $result);
            } else {
                $result["list"] = array();
                $result["allnum"] = 0;
                $res = array('error' => 1, 'errortip' => '班级暂无班级课表信息', 'result' => $result);
            }
            ajax_return($res, $request['language_type']);
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //获取班级复习课表
    function classReviewTimeApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            //访问日志
            $this->VeryLogs($pucArray['apiuser_id'], "{$request['u']}/{$request['t']}{$request['api']}", $request);

            $field = array();
            $k = 0;
            $field[$k]["fieldstring"] = "hour_lessontimes";
            $field[$k]["fieldname"] = "巩固课次";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 0;
            $k++;
            $field[$k]["fieldstring"] = "hour_ischecking_name";
            $field[$k]["fieldname"] = "是否完成巩固";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 0;
            $k++;
            $field[$k]["fieldstring"] = "hour_timesection";
            $field[$k]["fieldname"] = "巩固时间";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 0;
            $k++;
            $field[$k]["fieldstring"] = "classroom_cnname";
            $field[$k]["fieldname"] = "巩固场地";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 0;
            $k++;
            $field[$k]["fieldstring"] = "study_stunum";
            $field[$k]["fieldname"] = "应巩固人数";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 0;
            $k++;
            $field[$k]["fieldstring"] = "attendance_stunum";
            $field[$k]["fieldname"] = "已考勤人数";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 0;
            $k++;
            $field[$k]["fieldstring"] = "absence_stunum";
            $field[$k]["fieldname"] = "请假人数";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 0;

            $result = array();
            $result['field'] = $field;
            $classOne = $this->DataControl->getFieldOne("smc_class", "class_id,company_id,school_id", "class_branch = '{$request['class_branch']}'");
            if ($classOne) {
                $request['company_id'] = $classOne['company_id'];
                $request['school_id'] = $classOne['school_id'];
                $request['class_id'] = $classOne['class_id'];
            } else {
                $result["list"] = array();
                $result["allnum"] = 0;
                $res = array('error' => 1, 'errortip' => '班级暂无班级复习课次信息', 'result' => $result);
                ajax_return($res, $request['language_type']);
            }

            $Model = new JiaowuModel();
            $dataList = $Model->classReviewTime($request);

            if ($dataList['list']) {
                $result["list"] = $dataList['list'];
                $result["allnum"] = $dataList['allnum'];
                $res = array('error' => 0, 'errortip' => '获取班级复习课次信息', 'result' => $result);
            } else {
                $result["list"] = array();
                $result["allnum"] = 0;
                $res = array('error' => 1, 'errortip' => '班级暂无班级复习课次信息', 'result' => $result);
            }
            ajax_return($res, $request['language_type']);
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }


    //获取当日上课班级
    function classTodayApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            //访问日志
            $this->VeryLogs($pucArray['apiuser_id'], "{$request['u']}/{$request['t']}{$request['api']}", $request);

            $date = date('Y-m-d');

            $school_id = $this->DataControl->getFieldOne("smc_school","school_id","school_branch = '{$request['school_branch']}'");

            $classlist = $this->DataControl->selectClear("
                SELECT 
                    c.class_branch
                FROM
                    smc_class_hour AS h 
                    left join smc_class as c on h.class_id = c.class_id
                WHERE
                    h.hour_day = '{$date}' and c.school_id = '{$school_id['school_id']}'");

            if ($classlist) {
                $result["list"] = $classlist;
                $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
            } else {
                $result["list"] = array();
                $res = array('error' => 1, 'errortip' => '暂无班级', 'result' => $result);
            }
            ajax_return($res, $request['language_type']);
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }


    //点名上课列表
    function rollCallClassApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            //访问日志
            $this->VeryLogs($pucArray['apiuser_id'], "{$request['u']}/{$request['t']}{$request['api']}", $request);

            $classOne = $this->DataControl->getFieldOne("smc_class", "class_id,company_id,school_id", "class_branch = '{$request['class_branch']}'");
            if ($classOne) {
                $request['company_id'] = $classOne['company_id'];
                $request['school_id'] = $classOne['school_id'];
                $request['class_id'] = $classOne['class_id'];
            } else {
                $res = array('error' => 1, 'errortip' => '请传入正确的班级编号', 'result' => array());
                ajax_return($res);
            }

            $CourseModel = new \Model\Smc\CourseModel($request);
            $res = $CourseModel->rollCallClass($request);

            $fieldstring = array('key_num', 'hourstudy_id', 'hour_id', 'student_id', 'student_name', 'student_branch', 'family_mobile', 'fee', 'student_type', 'stuchecktype_name', 'hour_checkinnum', 'clockinginlog_note');
            $fieldname = array('序号', '记录id', '课时id', '学员id', '学员姓名', '学员编号', '联系电话', '是否计费', '学员类型', '考勤状态', '缺勤统计', '缺勤原因');
            $fieldcustom = array('1', '0', '0', "0", "1", "1", "1", "1", "1", '1', "1", '1');
            $fieldshow = array('1', '0', '0', "0", "1", "1", "1", "1", "1", '1', "1", '1');
            $fieldred = array('0', '0', '0', "0", "0", "0", "0", "1", "0", '0', "0", '1');

            $field = array();
            for ($i = 0; $i < count($fieldstring); $i++) {
                $field[$i]["fieldstring"] = trim($fieldstring[$i]);
                $field[$i]["fieldname"] = trim($fieldname[$i]);
                $field[$i]["custom"] = trim($fieldcustom[$i]);
                $field[$i]["show"] = trim($fieldshow[$i]);
                $field[$i]["is_red"] = trim($fieldred[$i]);
                if ($field[$i]["fieldstring"] == 'stuchecktype_code') {
                    $field[$i]["is_status"] = 1;
                }
                if ($field[$i]["fieldstring"] == 'clockinginlog_note') {
                    $field[$i]["is_reason"] = 0;
                }
                if ($field[$i]["fieldstring"] == 'fee') {
                    $field[$i]["is_red"] = 1;
                }
            }

            $result = array();
            $result["field"] = $field;
            if ($res['student']) {
                $result["list"] = $res['student'];
                $res = array('error' => '0', 'errortip' => '获取信息', 'result' => $result);
            } else {
                $result["list"] = array();
                $res = array('error' => '1', 'errortip' => '暂无需要点名上课的学员信息', 'result' => $result);
            }
            ajax_return($res, $request['language_type']);
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }

    }

    //获取缺勤课时
    function getAbsenceHourApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            //访问日志
            $this->VeryLogs($pucArray['apiuser_id'], "{$request['u']}/{$request['t']}{$request['api']}", $request);

            $day = date("Y-m-d", strtotime("- 30 days"));
            $datawhere = "hs.hour_id = ch.hour_id and hs.class_id = c.class_id and hs.student_id = s.student_id and c.course_id = co.course_id and ch.hour_iswarming = '0' and ch.hour_ischecking = '1' and hs.hourstudy_checkin = '0'";
            $schoolOne = $this->DataControl->getFieldOne("smc_school", "school_id,company_id", "school_branch='{$request['school_branch']}'");
            if ($schoolOne) {
                $datawhere .= " and c.school_id = '{$schoolOne['school_id']}' and co.coursetype_id in (select ct.coursetype_id from smc_code_coursetype ct where ct.company_id = '{$schoolOne['company_id']}' and ct.coursetype_branch IN ('E', 'L', 'A', 'OC'))";
            } else {
                ajax_return(array('error' => 1, 'errortip' => '暂无缺勤课时', 'result' => array()), $request['language_type']);
            }
            $classOne = $this->DataControl->getFieldOne("smc_class", "class_id", "class_branch = '{$request['class_branch']}'");
             if ($classOne) {
                 $datawhere .= " and hs.class_id = '{$classOne['class_id']}'";
             } else {
                 $datawhere .= " and c.class_enddate >= '{$day}'";
             }
             $studentOne = $this->DataControl->getFieldOne("smc_student", "student_id", "student_branch='{$request['student_branch']}'");
             if ($studentOne) {
                 $datawhere .= " and hs.student_id = '{$studentOne['student_id']}'";
             }
             $stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id", "staffer_branch = '{$request['teacher_branch']}'");
             if ($stafferOne) {
                 $datawhere .= " and c.class_id in (select ct.class_id from smc_class_teach ct where ct.staffer_id = '{$stafferOne['staffer_id']}')";
             }

             $dataList = $this->DataControl->selectClear("SELECT ch.hour_id,ch.hour_name,ch.hour_updatatime,c.class_branch,s.student_branch FROM smc_student_hourstudy as hs,smc_class_hour as ch,smc_class as c,smc_student as s,smc_course co WHERE {$datawhere} ORDER BY ch.hour_lessontimes ASC");
             if ($dataList) {
                 $result["list"] = $dataList;
                 $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
             } else {
                 $result["list"] = array();
                 $res = array('error' => 1, 'errortip' => '暂无缺勤课时', 'result' => $result);
             }
             ajax_return($res, $request['language_type']);
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }


    //获取班组流失学员
    function getCourseTypeStuLossApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            //访问日志
            $this->VeryLogs($pucArray['apiuser_id'], "{$request['u']}/{$request['t']}{$request['api']}", $request);

            $day = date("Y-m-d", strtotime("- 1 year"));
            $datawhere = "sc.coursetype_id = c.coursetype_id and sc.student_id = s.student_id and sc.stuchange_code = '{$request['code']}' and sc.changelog_day > '{$day}'";

            $schoolOne = $this->DataControl->getFieldOne("smc_school", "school_id,company_id", "school_branch='{$request['school_branch']}'");
            if ($schoolOne) {
                $datawhere .= " and sc.company_id = '{$schoolOne['company_id']}' and sc.school_id = '{$schoolOne['school_id']}' and c.coursetype_branch IN ('E', 'L', 'A', 'OC')";
            } else {
                ajax_return(array('error' => 1, 'errortip' => '暂无班组流失学员', 'result' => array()), $request['language_type']);
            }
            if ($request['coursetype_branch']) {
                $datawhere .= " and c.coursetype_branch = '{$request['coursetype_branch']}'";
            }

            $dataList = $this->DataControl->selectClear("SELECT sc.changelog_day,c.coursetype_branch,s.student_branch FROM smc_student_changelog as sc,smc_code_coursetype as c,smc_student as s WHERE {$datawhere}");
            if ($dataList) {
                $result["list"] = $dataList;
                $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
            } else {
                $result["list"] = array();
                $res = array('error' => 1, 'errortip' => '暂无班组流失学员', 'result' => $result);
            }
            ajax_return($res, $request['language_type']);
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }


    //获取班级课时
    function getClassHourApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            //访问日志
            $this->VeryLogs($pucArray['apiuser_id'], "{$request['u']}/{$request['t']}{$request['api']}", $request);

            $datawhere = "h.class_id = c.class_id";
            if ($request['class_branch']) {
                $datawhere .= " and c.class_branch = '{$request['class_branch']}'";
            } else {
                ajax_return(array('error' => 1, 'errortip' => '暂无班级课时', 'result' => array()), $request['language_type']);
            }
            if ($request['teacher_branch']) {
                $datawhere .= " and exists (select 1 from smc_class_hour_teaching as ht,smc_staffer as s where ht.staffer_id = s.staffer_id 
                                and ht.hour_id = h.hour_id and ht.class_id = c.class_id and s.staffer_branch = '{$request['teacher_branch']}')";
            }
            if ($request['hour_day']) {
                $datawhere .= " and h.hour_day = '{$request['hour_day']}'";
            }

            $dataList = $this->DataControl->selectClear("SELECT h.hour_id FROM smc_class_hour as h,smc_class as c WHERE {$datawhere}");
            if ($dataList) {
                $result["list"] = $dataList;
                $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
            } else {
                $result["list"] = array();
                $res = array('error' => 1, 'errortip' => '暂无班级课时', 'result' => $result);
            }
            ajax_return($res, $request['language_type']);
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }


    //获取园所学员意向学校
    function getSchoolApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            //访问日志
            $this->VeryLogs($pucArray['apiuser_id'], "{$request['u']}/{$request['t']}{$request['api']}", $request);

            if (!$request['school_branch']) {
                ajax_return(array('error' => 1, 'errortip' => '暂无信息', 'result' => array()), $request['language_type']);
            }

            $dataList = $this->DataControl->selectClear("SELECT school_cnname,school_branch FROM smc_school WHERE school_branch IN {$request['school_branch']}");

            if ($dataList) {
                $result["list"] = $dataList;
                $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
            } else {
                $result["list"] = array();
                $res = array('error' => 1, 'errortip' => '暂无信息', 'result' => $result);
            }
            ajax_return($res, $request['language_type']);
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }


    //获取学校/班级信息
    function getSchoolClassApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            //访问日志
            $this->VeryLogs($pucArray['apiuser_id'], "{$request['u']}/{$request['t']}{$request['api']}", $request);

            if ($request['type'] == 1) {
                if (!$request['class_branch']) {
                    ajax_return(array('error' => 1, 'errortip' => '暂无信息', 'result' => array()), $request['language_type']);
                }

                $dataOne = $this->DataControl->selectOne("SELECT s.school_cnname FROM smc_class as c,smc_school as s WHERE c.school_id = s.school_id and c.class_branch = '{$request['class_branch']}'");
            } elseif ($request['type'] == 2) {
                if (!$request['class_branch']) {
                    ajax_return(array('error' => 1, 'errortip' => '暂无信息', 'result' => array()), $request['language_type']);
                }

                $dataOne = $this->DataControl->getFieldOne("smc_class", "class_cnname", "class_branch = '{$request['class_branch']}'");
            } elseif ($request['type'] == 3) {
                //class_branch 实际是 school_branch
                $request['school_branch'] = $request['class_branch'];
                if (!$request['school_branch']) {
                    ajax_return(array('error' => 1, 'errortip' => '暂无信息', 'result' => array()), $request['language_type']);
                }

                $dataOne = $this->DataControl->getFieldOne("smc_school", "school_cnname", "school_branch = '{$request['school_branch']}'");
            }
            if ($dataOne) {
                $result["list"] = $dataOne;
                $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
            } else {
                $result["list"] = array();
                $res = array('error' => 1, 'errortip' => '暂无信息', 'result' => $result);
            }
            ajax_return($res, $request['language_type']);
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }


    //获取学员人像
    function getFaceImgApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            //访问日志
            $this->VeryLogs($pucArray['apiuser_id'], "{$request['u']}/{$request['t']}{$request['api']}", $request);

            if (!$request['student_branch']) {
                ajax_return(array('error' => 1, 'errortip' => '暂无人像信息', 'result' => array()), $request['language_type']);
            }

            $dataOne = $this->DataControl->selectOne("SELECT
                                                                (
                                                                    SELECT
                                                                        b.stuportrait_faceimg
                                                                    FROM
                                                                        gmc_machine_stuportrait AS b
                                                                    WHERE
                                                                        a.student_id = b.student_id
                                                                    ORDER BY
                                                                        b.stuportrait_id DESC
                                                                    LIMIT 1
                                                                ) AS faceimg
                                                            FROM
                                                                smc_student AS a
                                                            WHERE
                                                                a.student_branch = '{$request['student_branch']}'");
            if ($dataOne) {
                $result["list"] = $dataOne;
                $res = array('error' => 0, 'errortip' => '获取人像信息', 'result' => $result);
            } else {
                $result["list"] = array();
                $res = array('error' => 1, 'errortip' => '暂无人像信息', 'result' => $result);
            }
            ajax_return($res, $request['language_type']);
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }


    function addStudentIntegral($company_id, $school_id, $student_id, $course_id, $integral = 0, $integrallog_rule, $staffer_id = 0, $playname = '', $note = '', $time = '', $class_id)
    {
        if (!$time || $time == '') {
            $time = time();
        }

        $stuIntOne = $this->DataControl->getFieldOne("smc_student_virtual_property", "property_integralbalance", "student_id='{$student_id}'");
        $stuIntOne['property_integralbalance'] = $stuIntOne['property_integralbalance'] ? $stuIntOne['property_integralbalance'] : 0;

        $integral_new = 0;
        $courseOne = $this->DataControl->getFieldOne("smc_course", "course_isintegral,course_maxintegral", "course_id='{$course_id}'");
        $oldOne = $this->DataControl->getFieldOne("smc_student_integrallog", "sum(integrallog_playamount) as old_integral", "integrallog_playclass='+' and student_id='{$student_id}' and course_id='{$course_id}'");

        if ($courseOne['course_isintegral'] == '1') {
            if ($oldOne['old_integral'] >= $courseOne['course_maxintegral']) {
                return false;
            }
            if ($oldOne['old_integral'] + $integral >= $courseOne['course_maxintegral']) {
                $integral_new = $courseOne['course_maxintegral'] - $oldOne['old_integral'];
            } else {
                $integral_new = $integral;
            }
        } else {
            $integral_new = $integral;
        }

        if ($integral_new <= 0) {
            return false;
        }

        //积分余额
        if ($this->DataControl->getFieldOne("smc_student_virtual_property", "property_integralbalance", "student_id='{$student_id}'")) {
            $data = array();
            $data['property_integralbalance'] = $stuIntOne['property_integralbalance'] + $integral_new;
            $this->DataControl->updateData("smc_student_virtual_property", "student_id='{$student_id}'", $data);
        } else {
            $data = array();
            $data['student_id'] = $student_id;
            $data['property_integralbalance'] = $integral_new;
            $this->DataControl->insertData("smc_student_virtual_property", $data);
        }

        $integrallog_data = array();
        $integrallog_data['student_id'] = $student_id;
        $integrallog_data['company_id'] = $company_id;
        $integrallog_data['school_id'] = $school_id;
        $integrallog_data['staffer_id'] = $staffer_id;
        $integrallog_data['course_id'] = $course_id;
        $integrallog_data['class_id'] = $class_id;

        $integrallog_data['integrallog_rule'] = $integrallog_rule;
        $integrallog_data['integrallog_playname'] = $playname ? $playname : '积分增加';
        $integrallog_data['integrallog_playclass'] = '+';
        $integrallog_data['integrallog_fromamount'] = $stuIntOne['property_integralbalance'];
        $integrallog_data['integrallog_playamount'] = $integral_new;
        $integrallog_data['integrallog_finalamount'] = $stuIntOne['property_integralbalance'] + $integral_new;

        $integrallog_data['integrallog_remark'] = $note;
        $integrallog_data['integrallog_reason'] = $integrallog_rule;
        $integrallog_data['integrallog_time'] = $time;
        $this->DataControl->insertData("smc_student_integrallog", $integrallog_data);

        return true;
    }



    function integralTransformAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            //钻石金币转积分
            $sid = $this->DataControl->getFieldOne("smc_student","student_id","student_branch = '{$request['branch']}' and company_id = '8888'");
            if($sid){
                if ($request['coin'] > 0) {
                    $this->addStudentIntegral('8888', '0', $sid['student_id'], '0', $request['coin'] / 10, '奇趣金币转积分', '12357', '', '奇趣金币转积分', time(), '0');
                }
                if ($request['diamond'] > 0) {
                    $this->addStudentIntegral('8888', '0', $sid['student_id'], '0', $request['diamond'] * 10, '奇趣钻石转积分', '12357', '', '奇趣钻石转积分', time(), '0');
                }
            }

            ajax_return(array('error' => 0, 'errortip' => "结算成功!"));
        }else{
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));

        }




    }

    //获取教师类型
    function getTeachTypeApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            //访问日志
            $this->VeryLogs($pucArray['apiuser_id'], "{$request['u']}/{$request['t']}{$request['api']}", $request);

            $datawhere = " company_id = '{$request['company_id']}' ";
            if (isset($request['teachtype_class']) && $request['teachtype_class'] != '') {
                $datawhere .= " and (teachtype_class = '{$request['teachtype_class']}' or teachtype_class = '2')";
            }
            if (isset($request['teacher_branch']) && $request['teacher_branch'] != '') {
                $staffer = $this->DataControl->getFieldOne("smc_staffer", "staffer_native", "staffer_branch = '{$request['teacher_branch']}'");
                if ($staffer['staffer_native']) {
                    $datawhere .= " and FIND_IN_SET('{$staffer['staffer_native']}', teachtype_native)";
                }
            }
            if (isset($request['teachtype_code']) && $request['teachtype_code'] != '') {
                $datawhere .= " and teachtype_code = '{$request['teachtype_code']}'";
            }

            $dataList = $this->DataControl->selectClear("select teachtype_code,teachtype_name,teachtype_class,teachtype_native from smc_code_teachtype as ct where {$datawhere}");

            if ($dataList) {
                $result["list"] = $dataList;
                $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
            } else {
                $result["list"] = array();
                $res = array('error' => 1, 'errortip' => '暂无教师类型', 'result' => $result);
            }

            ajax_return($res, $request['language_type']);
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //获取职务
    function getPostApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            //访问日志
            $this->VeryLogs($pucArray['apiuser_id'], "{$request['u']}/{$request['t']}{$request['api']}", $request);

            $dataList = $this->DataControl->selectClear("select post_id,post_name from gmc_company_post where company_id = '{$request['company_id']}' and post_type = '1'");

            $result = array();
            $result["list"] = $dataList;
            ajax_return(array('error' => 0, 'errortip' => '获取成功', 'result' => $result), $request['language_type']);
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //获取上课时长
    function getCourseTimesApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            //访问日志
            $this->VeryLogs($pucArray['apiuser_id'], "{$request['u']}/{$request['t']}{$request['api']}", $request);

            $course = $this->DataControl->getFieldOne("smc_course", "course_islimittime,course_limittime", "company_id = '{$request['company_id']}' and course_branch = '{$request['course_branch']}'");
            if ($course && $course['course_islimittime'] == '1') {
                $course_limittime = explode(',', $course['course_limittime']);
                foreach ($course_limittime as $key => $value) {
                    $dataList[$key]['coursetimes_nums'] = $value;
                }
            } else {
                $dataList = $this->DataControl->selectClear("select coursetimes_nums from smc_code_coursetimes where company_id = '{$request['company_id']}'");
            }

            $result = array();
            $result["list"] = $dataList;
            ajax_return(array('error' => 0, 'errortip' => '获取成功', 'result' => $result), $request['language_type']);
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }


    //获取沟通方式
    function getCommodeApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            //访问日志
            $this->VeryLogs($pucArray['apiuser_id'], "{$request['u']}/{$request['t']}{$request['api']}", $request);

            $Model = new \Model\Crm\PublicModel();
            $dataList = $Model->getCommedeByCompanyId($request['company_id']);

            if ($dataList) {
                $result["list"] = $dataList;
                $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
            } else {
                $result["list"] = array();
                $res = array('error' => 1, 'errortip' => '暂无沟通方式', 'result' => $result);
            }
            ajax_return($res, $request['language_type']);
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //获取电访类型
    function trackTypeApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            //访问日志
            $this->VeryLogs($pucArray['apiuser_id'], "{$request['u']}/{$request['t']}{$request['api']}", $request);

            $dataList = $this->DataControl->selectClear("SELECT tracktype_id,tracktype_name FROM smc_code_tracktype WHERE company_id = '{$request['company_id']}' and tracktype_smc = 1 ORDER BY tracktype_id ASC");
            if ($dataList) {
                $result["list"] = $dataList;
                $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
            } else {
                $result["list"] = array();
                $res = array('error' => 1, 'errortip' => '暂无电访类型', 'result' => $result);
            }
            ajax_return($res, $request['language_type']);
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //获取沟通结果
    function trackResultTypeApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            //访问日志
            $this->VeryLogs($pucArray['apiuser_id'], "{$request['u']}/{$request['t']}{$request['api']}", $request);

            $dataList = $this->DataControl->selectClear("SELECT trackresult_id,trackresult_name FROM smc_code_trackresult WHERE company_id = '{$request['company_id']}' and tracktype_id = '{$request['tracktype_id']}' ORDER BY trackresult_id ASC");
            if ($dataList) {
                $result["list"] = $dataList;
                $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
            } else {
                $result["list"] = array();
                $res = array('error' => 1, 'errortip' => '暂无沟通结果', 'result' => $result);
            }
            ajax_return($res, $request['language_type']);
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //获取课叮铛学员校区ID信息
    function getStudentschoolApi(){
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            //访问日志
            $this->VeryLogs($pucArray['apiuser_id'],"{$request['u']}/{$request['t']}{$request['api']}",$request);

            $result = array();
            $schoolOne = $this->DataControl->getFieldOne("smc_school", "school_id,company_id", "school_branch='{$request['school_branch']}'");
            if ($schoolOne) {
                $result['school_id'] = $schoolOne['school_id'];
                $result['company_id'] = $schoolOne['company_id'];
            }
            $studentOne = $this->DataControl->getFieldOne("smc_student", "student_id", "student_branch='{$request['student_branch']}'");
            if ($studentOne) {
                $result['student_id'] = $studentOne['student_id'];
            }

//            //微商城教师可以跳过 页面去zzzzzzzzzzzzz申请优惠券 -- 这里是否教师信息
//            $stafferOne = $this->DataControl->getFieldOne('smc_staffer','staffer_id,staffer_cnname',"staffer_mobile='{$request['member_mobile']}' and company_id = '{$request['company_id']}' ");
//            if ($stafferOne) {
//                $result['isstaff'] = 1;
//            }else {
//                $bakinfo = request_by_curl("https://api.kidcastle.com.cn/Jdbpay/hrmworker", "mobile={$request['member_mobile']}", "GET", array());
//                $apiArray = json_decode($bakinfo, '1');
//                if($apiArray['error'] == '0') {
//                    $result['isstaff'] = 1;
//                }else{
//                    $result['isstaff'] = 0;
//                    $result['lgerrortip'] = '您好，您的信息未登记在系统内，无法使用微商城，请联系您的老师确认您的信息状态，谢谢。';
//                }
//            }

            ajax_return(array('error' => '0', 'errortip' => '信息获取成功！', 'result' => $result));
        }else{
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //通过手机号获取课叮铛用户授权信息
    function getParentTokenApi(){
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            //访问日志
            $this->VeryLogs($pucArray['apiuser_id'],"{$request['u']}/{$request['t']}{$request['api']}",$request);

            $parenterOne = $this->DataControl->getFieldOne("smc_parenter", "parenter_id,parenter_mobile,parenter_tokencode,parenter_tokenencrypt", "parenter_mobile='{$request['mobile']}'");
            if (!$parenterOne) {
                $data = array();
                $data['parenter_mobile'] = $request['mobile'];
                $data['parenter_addtime'] = time();
                $this->DataControl->insertData("smc_parenter",$data);
                $parenterOne = $this->DataControl->getFieldOne("smc_parenter", "parenter_id,parenter_mobile,parenter_tokencode,parenter_tokenencrypt", "parenter_mobile='{$request['mobile']}'");
            }
            //token 改为半年后失效  --- 20230818改（因为 8.26开收费）
            $montharr = ['08','09','10','11','12','01'];
            $thismonth = date('m',time());
            if(in_array($thismonth,$montharr)){
                $md5tokenbar = base64_encode(md5($parenterOne["parenter_tokencode"] . date("Y-07")));
            }else{
                $md5tokenbar = base64_encode(md5($parenterOne["parenter_tokencode"] . date("Y-01")));
            }
//            $md5tokenbar = base64_encode(md5($parenterOne["parenter_tokencode"] . date("Y-m")));
            if ($md5tokenbar == $parenterOne["parenter_tokenencrypt"]) {
                $token = $parenterOne["parenter_tokenencrypt"];
            } else {
                //目前这里注释是为了测试方便
                $tokencode = rand(111111, 999999);
                //token 改为半年后失效  --- 20230818改（因为 8.26开收费）
                if(in_array($thismonth,$montharr)){
                    $md5tokenbar = base64_encode(md5($tokencode . date("Y-07")));
                }else{
                    $md5tokenbar = base64_encode(md5($tokencode . date("Y-01")));
                }
//                $md5tokenbar = base64_encode(md5($tokencode . date("Y-m")));
                $this->DataControl->query("UPDATE smc_parenter SET parenter_tokencode = '{$tokencode}',parenter_tokenencrypt = '{$md5tokenbar}' WHERE parenter_id ='{$parenterOne['parenter_id']}'");
                $token = $md5tokenbar;
            }

            //是否是职工
            if($parenterOne) {
                $stafferOne = $this->DataControl->getFieldOne('smc_staffer','staffer_id,staffer_cnname',"staffer_mobile='{$parenterOne['parenter_mobile']}' and company_id = '8888' ");
                if ($stafferOne) {
                    $isstaff = 1;
                }else {
//                    $bakinfo = request_by_curl("https://api.kidcastle.com.cn/Jdbpay/hrmworker", "mobile={$parenterOne['parenter_mobile']}", "GET", array());
                    $bakinfo = request_by_curl("https://eduappv2.kidcastle.com.cn/api/beisen/zhuzhixinxi", "mobile={$parenterOne['parenter_mobile']}", "GET", array());//修改查询集团信息
                    $apiArray = json_decode($bakinfo, '1');
                    if($apiArray['error'] == '0') {
                        $isstaff = 1;
                    }else{
                        $isstaff = 0;
                        $lgerrortip = '您好，您的信息未登记在系统内，无法使用微商城，请联系您的老师确认您的信息状态，谢谢1。';
                    }
                }
            }else {
                $isstaff = 0;
                $lgerrortip = '您好，您的信息未登记在系统内，无法使用微商城，请联系您的老师确认您的信息状态，谢谢2。';
            }


            $result = array();
            $result['parenter_id'] = $parenterOne['parenter_id'];
            $result['parenter_tokenencrypt'] = $token;
            $result['isstaff'] = $isstaff;
            $result['lgerrortip'] = $lgerrortip;
            ajax_return(array('error' => '0', 'errortip' => '秘钥获取成功！', 'result' => $result));
        }else{
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    function testParameterView()
    {
        $parameter = array();
        $parameter['timesteps'] = time();
        $parameter['apiuser_code'] = 'Qiqueax';
        $parameter['company_id'] = '8888';

        $apiuserOne = $this->DataControl->getFieldOne('imc_apiuser', "apiuser_id,apiuser_aeskey,apiuser_aesiv", "apiuser_code = '{$parameter['apiuser_code']}'");
        if (!$apiuserOne) {
            $this->errortip = "未查询到您的授权信息";
            $this->error = true;
            return false;
        }

        $aes = new \Aesencdec($apiuserOne['apiuser_aeskey'], $apiuserOne['apiuser_aesiv']);
        $parameterJson = json_encode($parameter, '1');
        $jmsting = $aes->encrypt($parameterJson);//解密

        $result = array();
        $result['timesteps'] = $parameter['timesteps'];
        $result['apiuser_code'] = $parameter['apiuser_code'];
        $result['veytoken'] = $jmsting;

        ajax_return(array('error' => '0', 'errortip' => '模拟参数获取成功', 'result' => $result));
    }

    function stringReplace($string)
    {
        $datacode = trim(str_replace('"', "", $string));
        $datacode = urldecode(urldecode($datacode));
        $datacode = str_replace(' ', "+", $datacode);
        return $datacode;
    }
    //第三方授权访问权限校验
    function UserVerify($paramArray)
    {
        $apiuserOne = $this->DataControl->getFieldOne('imc_apiuser', "apiuser_id,apiuser_aeskey,apiuser_aesiv", "apiuser_code = '{$paramArray['apiuser_code']}'");
        if (!$apiuserOne) {
            $this->errortip = "未查询到您的授权信息";
            $this->error = true;
            return false;
        }

        if (isset($paramArray['apiuser_aeskey']) && $paramArray['apiuser_aeskey'] !== '' && isset($paramArray['apiuser_aesiv']) && $paramArray['apiuser_aesiv'] !== '') {
            if ($apiuserOne['apiuser_aeskey'] == $paramArray['apiuser_aeskey'] && $apiuserOne['apiuser_aesiv'] == $paramArray['apiuser_aesiv']) {
                $baseOne = array();
                $baseOne['apiuser_id'] = $apiuserOne['apiuser_id'];
                $companyOne = $this->DataControl->selectOne("select company_id,company_cnname from gmc_company WHERE company_id = '{$paramArray['company_id']}' limit 0,1");
                if ($companyOne) {
                    $baseOne['company_id'] = $companyOne['company_id'];
                    return $baseOne;
                } else {
                    $this->errortip = '你的授权集团编号错误，请确认编号正确';
                    $this->error = true;
                    return false;
                }
            } else {
                $this->errortip = "你的授权秘钥及偏移值不正确，{$paramArray['apiuser_aeskey']}-{$paramArray['apiuser_aesiv']}";
                $this->error = true;
                return false;
            }
        }

        if (!isset($paramArray['timesteps']) || $paramArray['timesteps'] == '') {
            $this->errortip = "请传入授权时间";
            $this->error = true;
            return false;
        }

        if ($paramArray['timesteps'] + 60 * 5 < time() || $paramArray['timesteps'] - 60 > time()) {
            $maxtimes = date("Y-m-d H:i:s", $paramArray['timesteps'] + 60 * 5);
            $this->errortip = "授权时间{$maxtimes}已过期5分钟，请确认连接及时性";//,{$timesteps}--{$jmsting}
            $this->error = true;
            return false;
        }

        $aes = new \Aesencdec($apiuserOne['apiuser_aeskey'], $apiuserOne['apiuser_aesiv']);
        $xssting = $aes->decrypt($this->stringReplace($paramArray['veytoken']));//解密
        if ($paramJson = json_decode($xssting, 1)) {//转化为数组
            if ((string)$paramJson['timesteps'] !== trim($paramArray['timesteps'])) {
                $this->errortip = '授权时间和连接时间不一致';
                $this->error = true;
                return false;
            }
            $baseOne = array();
            $baseOne['apiuser_id'] = $apiuserOne['apiuser_id'];
            $baseOne['tokenstring'] = $xssting;
            $companyOne = $this->DataControl->selectOne("select company_id,company_cnname from gmc_company WHERE company_id = '{$paramJson['company_id']}' limit 0,1");
            if ($companyOne) {
                $baseOne['company_id'] = $companyOne['company_id'];
                return $baseOne;
            } else {
                $this->errortip = '你的授权集团编号错误，请确认编号正确';
                $this->error = true;
                return false;
            }
        } else {
            $this->errortip = '数据机密信息传输有误，请检查！';
            $this->error = true;
            return false;
        }
    }

    function VeryLogs($apiuser_id,$apiurl,$paramArray)
    {
        $data = array();
        $data['apiuser_id'] = $apiuser_id;
        $data['apilog_posturl'] = "https://api.kedingdang.com/{$apiurl}";
        $data['apilog_postorgjson'] = urldecode(http_build_query($paramArray));
        $data['apilog_postjson'] = $this->errortip;
        $data['apilog_createtime'] = time();
        $this->DataControl->insertData("imc_apiuser_apilog", $data);
    }

    //推荐名单加积分 -- 推荐名单列表
    function getScTuijianApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            //访问日志
            $this->VeryLogs($pucArray['apiuser_id'], "{$request['u']}/{$request['t']}{$request['api']}", $request);

            $datawhere = " a.company_id = '{$request['company_id']}' and a.client_stubranch <> ''  and b.student_id > 1  and a.client_createtime > '1685548799'";//

            if (isset($request['start_time']) && $request['start_time'] !== '') {
                $stime = strtotime($request['start_time']);
            } else {
                $stime = strtotime(date("Y-m-d"));
            }
            $datawhere .= " and a.client_createtime >= '{$stime}' ";

            if (isset($request['end_time']) && $request['end_time'] !== '') {
                $etime = strtotime($request['end_time']);
            } else {
                $etime = strtotime(date("Y-m-d"))+86399;
            }
            $datawhere .= " and a.client_createtime <= '{$etime}' ";

            $sql = "SELECT a.client_stubranch,a.client_id,a.client_cnname,FROM_UNIXTIME(a.client_createtime,'%Y-%m-%d') as client_createtime  
                    FROM crm_client as a 
                    LEFT JOIN smc_student as b on a.client_stubranch = b.student_branch  
                    WHERE {$datawhere} 
                    order by a.client_createtime asc
                    ";
            $dataList = $this->DataControl->selectClear($sql);

            if ($dataList) {
                $result["list"] = $dataList;
                $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
            } else {
                $result["list"] = array();
                $res = array('error' => 1, 'errortip' => '暂无推荐数据', 'result' => $result);
            }

            ajax_return($res, $request['language_type']);
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //推荐名单加积分 -- 推荐名单列表 --- 试听到访
    function getScTuijianVisitApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            //访问日志
            $this->VeryLogs($pucArray['apiuser_id'], "{$request['u']}/{$request['t']}{$request['api']}", $request);

            $datawhere = " a.company_id = '{$request['company_id']}' and a.client_stubranch <> ''  and b.student_id > 1 and c.audition_isvisit = '1' and a.client_createtime > '1685548799' ";//

            if (isset($request['start_time']) && $request['start_time'] !== '') {
                $stime = strtotime($request['start_time']);
            } else {
                $stime = strtotime(date("Y-m-d"));
            }
            $datawhere .= " and UNIX_TIMESTAMP(c.audition_visittime) >= '{$stime}' ";

            if (isset($request['end_time']) && $request['end_time'] !== '') {
                $etime = strtotime($request['end_time']);
            } else {
                $etime = strtotime(date("Y-m-d"))+86399;
            }
            $datawhere .= " and UNIX_TIMESTAMP(c.audition_visittime) <= '{$etime}' ";

            $sql = "SELECT a.client_stubranch,a.client_id,a.client_cnname,FROM_UNIXTIME(a.client_createtime,'%Y-%m-%d') as client_createtime,UNIX_TIMESTAMP(c.audition_visittime) as audition_visittime   
                    FROM crm_client as a 
                    LEFT JOIN smc_student as b on a.client_stubranch = b.student_branch  
                    LEFT JOIN crm_client_audition as c ON a.client_id = c.client_id 
                    WHERE {$datawhere} 
                    group by a.client_id 
                    order by a.client_createtime asc
                    ";
            $dataList = $this->DataControl->selectClear($sql);

            if ($dataList) {
                $result["list"] = $dataList;
                $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
            } else {
                $result["list"] = array();
                $res = array('error' => 1, 'errortip' => '暂无推荐数据', 'result' => $result);
            }

            ajax_return($res, $request['language_type']);
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //教务那边获取有赞那边某课程被那些会员购买，在这边给名单打上标签
    function addYzClientLabelView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            //访问日志
            $this->VeryLogs($pucArray['apiuser_id'], "{$request['u']}/{$request['t']}{$request['api']}", $request);

            $goodstr = '';
            $goodArrData = [];
            if(isset($request['goods']) && $request['goods'] != ''){
                $goodArray = json_decode(stripslashes($request['goods']),true);
                if($goodArray){
                    $goods = array_column($goodArray,'yzgoods_outid');
                    $goodstr .= "'".implode("','",$goods)."'";
                    $goodArrData = array_column($goodArray,'yzgoods_name','yzgoods_outid');
                    $goodTidArrData = array_column($goodArray,'yztrade_tid','yzgoods_outid');
                }
            }

            $result = array();
            $result['open_id'] = $request['open_id'];
            $result['client_mobile'] = $request['client_mobile'];
            $result['receiver_tel'] = $request['receiver_tel'];

            if(checkMobile($request['client_mobile']) || checkMobile($request['receiver_tel'])){

                $mobileStr = "'99'";
                if(checkMobile($request['client_mobile'])){
                    $mobileStr .= ",'".$request['client_mobile']."'";
                }
                if(checkMobile($request['receiver_tel'])){
                    $mobileStr .= ",'".$request['receiver_tel']."'";
                }

                $client = $this->DataControl->selectClear(" select client_id,client_tag from crm_client where company_id = '{$request['company_id']}' and client_mobile in ($mobileStr) and client_tag NOT LIKE '%吉的堡多维探索课包%'  order by client_createtime desc limit 0,1 ");
                if($client){
                    foreach ($client as $clientVar){

                        $goodData = $this->DataControl->selectClear("select * from crm_sell_activity_package where package_threegoodid in ($goodstr) ");
                        if($goodData) {

                            if(strpos($clientVar['client_tag'], $request['client_tag']) === false) {//不存在传过来的标签，补充数据
                                $data = array();
                                $data['client_tag'] = ($clientVar['client_tag'] != '') ? ($clientVar['client_tag'] . "," . $request['client_tag']) : $request['client_tag'];
                                $data['client_updatetime'] = time();
                                $this->DataControl->updateData('crm_client', "client_id='{$clientVar['client_id']}'", $data);
                            }

                            foreach ($goodData as $goodVar) {
                                $goodname = '';
                                $goodname = $goodArrData[$goodVar['package_threegoodid']]?$goodArrData[$goodVar['package_threegoodid']]:$goodVar['package_name'];

                                //记录购买的课包
                                $pack = array();
                                $pack['client_id'] = $clientVar['client_id'];
                                $pack['package_branch'] = $goodVar['package_branch'];
                                $pack['package_threetid'] = $goodTidArrData[$goodVar['package_threegoodid']];//第三方订单ID
                                $pack['package_createtime'] = time();
                                $package_id = $this->DataControl->insertData('crm_client_package', $pack);

                                $data = array();
                                $data['client_id'] = $clientVar['client_id'];
                                $data['school_id'] = 0;
                                $data['track_linktype'] = '系统操作';
                                $data['track_validinc'] = 0;
                                $data['track_followmode'] = 0;
                                $data['track_type'] = 2;
//                                $data['track_note'] = "有赞购买’".$request['goodtitle']."‘,系统自动给名单打标签";
                                $data['track_note'] = "用户通过有赞购买".$goodname."商品，系统自动标记名单标签【引流课包】";
                                $data['track_createtime'] = time();
                                $track_id = $this->DataControl->insertData('crm_client_track', $data);
                            }
                        }
//                        else{
//                            $data = array();
//                            $data['client_id'] = $clientVar['client_id'];
//                            $data['school_id'] = 0;
//                            $data['track_linktype'] = '系统操作';
//                            $data['track_validinc'] = 0;
//                            $data['track_followmode'] = 0;
//                            $data['track_type'] = 2;
//                            $data['track_note'] = "有赞购买’".$request['goodtitle']."‘,系统自动给名单打标签";
//                            $data['track_createtime'] = time();
//                            $track_id = $this->DataControl->insertData('crm_client_track', $data);
//                        }
                    }
                    ajax_return(array('error' => '0', 'errortip' => '标签添加成功', 'result' => $result));
                }else{
                    ajax_return(array('error' => '1', 'errortip' => '找不到对应的名单', 'result' => $result));
                }
            }else{
                ajax_return(array('error' => '1', 'errortip' => '手机号格式不对', 'result' => $result));
            }
        } else{
            $result = array();
            $result['open_id'] = $request['open_id'];
            $result['client_mobile'] = $request['client_mobile'];
            $result['receiver_tel'] = $request['receiver_tel'];
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => $result));
        }
    }
    //教务那边获取有赞那边某课程被那些会员购买，查出的购买某课程的名单，同步到课叮铛里面
    function addYztradeToKddScView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            //访问日志
            $this->VeryLogs($pucArray['apiuser_id'], "{$request['u']}/{$request['t']}{$request['api']}", $request);

            $result = array();
            $result['open_id'] = $request['open_id'];
            $result['client_mobile'] = $request['client_mobile'];

            if(checkMobile($request['client_mobile'])){
                $yztradeOne = $this->DataControl->selectOne(" select open_id,yztrade_goodtitle from gmc_yztrade where open_id = '{$request['open_id']}' and yztrade_goodtitle = '{$request['goodtitle']}' and yztrade_mobile = '{$request['client_mobile']}' ");
                if(!$yztradeOne){
                    $data = array();
                    $data['open_id'] = $request['open_id'];
                    $data['yztrade_goodtitle'] = $request['goodtitle'];
                    $data['yztrade_crmlabel'] = $request['client_tag'];
                    $data['yztrade_mobile'] = $request['client_mobile'];
                    $data['yztrade_createtime'] = time();
                    $this->DataControl->insertData('gmc_yztrade', $data);
                    ajax_return(array('error' => '0', 'errortip' => '信息存储成功', 'result' => $result));
                }else{
                    ajax_return(array('error' => '1', 'errortip' => '信息已经存储', 'result' => $result));
                }
            }else{
                ajax_return(array('error' => '1', 'errortip' => '手机号格式不对', 'result' => $result));
            }
        } else{
            $result = array();
            $result['open_id'] = $request['open_id'];
            $result['client_mobile'] = $request['client_mobile'];
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => $result));
        }
    }

    //获取课叮铛 学校对应的省份数据
    function getKddProvinceView(){
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            //访问日志
            $this->VeryLogs($pucArray['apiuser_id'],"{$request['u']}/{$request['t']}{$request['api']}",$request);

            $dataList = $this->DataControl->selectClear("SELECT r.region_id,r.region_name
              FROM smc_code_region AS r,smc_school as s
              where s.school_province = r.region_id and r.parent_id= '1' AND s.school_isclose = 0 AND s.school_istest = 0 and s.company_id = '8888'
              GROUP BY r.region_id
              ORDER BY r.region_sort ASC");

            if($dataList){
                $res = array('error' => '0', 'errortip' => "省份信息获取成功",'result' => $dataList);
            }else{
                $res = array('error' => '1', 'errortip' => "暂无省份信息数据",'result' => array());
            }
            ajax_return($res,$request['language_type']);
        }else{
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }
    //获取课叮铛 学校对应的城市
    function getKddCityView(){
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            //访问日志
            $this->VeryLogs($pucArray['apiuser_id'],"{$request['u']}/{$request['t']}{$request['api']}",$request);

            $datawhere = "1";
            if(isset($request['region_id']) && $request['region_id'] != '0' && $request['region_id'] != '') {
                $datawhere .= " and r.parent_id = '{$request['region_id']}'";
            }

            $dataList = $this->DataControl->selectClear("SELECT r.region_id,r.region_name
              FROM smc_code_region AS r,smc_school as s
              where {$datawhere} and s.school_city = r.region_id AND s.school_isclose = 0 AND s.school_istest = 0 and s.company_id = '8888'
              GROUP BY r.region_id
              ORDER BY r.region_sort ASC");

            if($dataList){
                $res = array('error' => '0', 'errortip' => "城市信息获取成功",'result' => $dataList);
            }else{
                $res = array('error' => '1', 'errortip' => "暂无城市信息数据",'result' => array());
            }
            ajax_return($res,$request['language_type']);
        }else{
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }
    //获取课叮铛 学校列表
    function getKddSchoolListView(){
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            //访问日志
            $this->VeryLogs($pucArray['apiuser_id'],"{$request['u']}/{$request['t']}{$request['api']}",$request);

            $datawhere = "1";
            if(isset($request['keyword']) && $request['keyword'] !== ''){
                $datawhere .= " and (s.school_cnname like '%{$request['keyword']}%' or s.school_branch like '%{$request['keyword']}%' or s.school_address like '%{$request['keyword']}%') ";
            }
            if(isset($request['province_id']) && $request['province_id'] != '0' && $request['province_id'] != '') {
                $datawhere .= " and s.school_province = '{$request['province_id']}'";
            }
            if(isset($request['city_id']) && $request['city_id'] != '0' && $request['city_id'] != '') {
                $datawhere .= " and s.school_city = '{$request['city_id']}'";
            }

            $dataList = $this->DataControl->selectClear("SELECT s.school_id,s.school_branch,s.school_cnname
              FROM smc_school as s
              where {$datawhere} AND s.school_isclose = 0 and s.company_id = '8888' and s.school_istest = '0'
              ORDER BY s.school_id DESC");

            if($dataList){
                $res = array('error' => '0', 'errortip' => "学校信息获取成功",'result' => $dataList);
            }else{
                $res = array('error' => '1', 'errortip' => "暂无学校信息数据",'result' => array());
            }
            ajax_return($res,$request['language_type']);
        }else{
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }


    //获取课叮铛 学校对应考勤机出勤记录 -- 学生的 -- 97
    function getMachineStucardlogView(){
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            //访问日志
            $this->VeryLogs($pucArray['apiuser_id'],"{$request['u']}/{$request['t']}{$request['api']}",$request);

            if($request['school_branch'] == ''){
                ajax_return(array('error' => '1', 'errortip' => "请传入学校信息数据",'result' => array()),$request['language_type']);
            }

            $schooOne = $this->DataControl->selectOne("select company_id,school_id,school_branch from smc_school where school_branch = '{$request['school_branch']}' limit 0,1 ");
            $request['school_id'] = $schooOne['school_id'];
            if($schooOne['company_id']){
                $request['company_id'] = $schooOne['company_id'];
            }
            $Model = new \Model\Gmc\MachineModel($request);
            $Model->getMachineStucardlog($request,1);

            if($Model->result['list']){
                ajax_return(array('error' => '0', 'errortip' => "信息获取成功",'result' => $Model->result),$request['language_type']);
            }else{
                ajax_return(array('error' => '1', 'errortip' => "暂无考勤数据",'result' => array()),$request['language_type']);
            }
        }else{
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //获取课叮铛 职工人脸考勤机出勤记录 -- 教师的 -- 对接到站群转到中控中间表 -- 97
    function getMachineStaffCardlogView(){
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            //访问日志
            $this->VeryLogs($pucArray['apiuser_id'],"{$request['u']}/{$request['t']}{$request['api']}",$request);

            $request['company_id'] = '8888';

            $sql = " SELECT m.cardlog_id,h.machine_code,t.staffer_employeepid,m.cardlog_clocktime,m.cardlog_type 
                    FROM gmc_machine_staffcardlog as m 
                    left join gmc_machine as h on m.machine_id = h.machine_id 
                    left join smc_staffer as t ON t.staffer_id = m.staffer_id 
                    left join smc_school as s ON s.school_id = m.school_id 
                    WHERE 1 and m.company_id = '{$request['company_id']}' and m.cardlog_state = '1'  and m.staffer_id > 1 and m.cardlog_istozksql = '0'
                        and s.school_istest = '0' and s.school_isclose = '0' and t.staffer_employeepid <> '' 
                    ORDER BY m.cardlog_clocktime ASC 
                    limit 0,40";//and m.cardlog_type = '1'
            $MachineList = $this->DataControl->selectClear($sql);

            if($MachineList){
                ajax_return(array('error' => '0', 'errortip' => "信息获取成功",'result' => $MachineList),$request['language_type']);
            }else{
                ajax_return(array('error' => '1', 'errortip' => "暂无考勤数据",'result' => array()),$request['language_type']);
            }
        }else{
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //获取课叮铛  标记已同步的  职工人脸考勤机出勤记录 -- 教师的 -- 对接到站群转到中控中间表 -- 97
    function upMachineStaffCardlogOneView(){
        $request = Input('post.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            //访问日志
            $this->VeryLogs($pucArray['apiuser_id'],"{$request['u']}/{$request['t']}{$request['api']}",$request);

            if($request['cardlog_id'] == ''){
                ajax_return(array('error' => '1', 'errortip' => "考勤日志ID 不能为空"));
            }
            $request['company_id'] = '8888';

            $data = array();
            $data['cardlog_istozksql'] = 1;
            $data['cardlog_istozksqltime'] = time();
            $isup = $this->DataControl->updateData("gmc_machine_staffcardlog", "cardlog_id = '{$request['cardlog_id']}'", $data);
            if($isup){
                ajax_return(array('error' => '0', 'errortip' => "信息修改成功"),$request['language_type']);
            }else{
                ajax_return(array('error' => '1', 'errortip' => "信息修改失败"),$request['language_type']);
            }
        }else{
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

//    //获取课叮铛校务系统教师是否 校长权限 -- 即是否校园最高权限   ---- 需求改了不用了
//    function getTeacherPrincipalView(){
//        $request = Input('get.', '', 'trim,addslashes');
//        $pucArray = $this->UserVerify($request);
//        if ($pucArray) {
//            $request['tokenstring'] = $pucArray['tokenstring'];
//            //访问日志
//            $this->VeryLogs($pucArray['apiuser_id'],"{$request['u']}/{$request['t']}{$request['api']}",$request);
//
//            $staffOne = $this->DataControl->selectOne("
//                        select a.staffer_id
//                        from smc_staffer as a,gmc_staffer_postbe as b,smc_school as c,gmc_company_post as d
//                        WHERE a.staffer_branch = '{$request['teacher_branch']}'
//                        and a.staffer_id = b.staffer_id and b.school_id = c.school_id
//                        and c.school_branch = '{$request['school_branch']}'
//                        and b.post_id = d.post_id and d.post_istopjob = '1'
//                        limit 0,1
//             ");
//
//            if ($staffOne) {
//                ajax_return(array('error' => '0', 'errortip' => '存在最高权限！', 'result' => array()));
//            }else{
//                ajax_return(array('error' => '1', 'errortip' => '不存在最高权限！', 'result' => array()));
//            }
//        }else{
//            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
//        }
//    }

    //获取课叮铛校务系统 获取 校 校长权限 角色平台页面的数据总览 240423
    function getDataScreeningSchoolApi(){
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
//        if (1) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            //访问日志
            $this->VeryLogs($pucArray['apiuser_id'],"{$request['u']}/{$request['t']}{$request['api']}",$request);

            $schoolOne = $this->DataControl->selectOne(" select company_id,school_id,school_branch,school_cnname from smc_school where company_id = '{$request['company_id']}' and school_branch = '{$request['school_branch']}' ");

            //本周/本月 --- timetype  本周传0 本月传3  (延续用的 之前他人给前端的定义值）
            if($request['timetype'] == '3') {
                //本月
                $starttime = strtotime(date('Y-m-01', time()));
                $endtime = strtotime(date('Y-m-t', time())) + 86399;
            }else {
                //本周
                $starttime = strtotime("monday this week");
                $endtime = strtotime('sunday this week') + 86399;
                //$endWeek = date('Y-m-d', strtotime("+6 day", strtotime($startWeek)));
            }
            $strstarttime = date('Y-m-d',$starttime);
            $strendtime = date('Y-m-d',$endtime);
            $nowstrtime = date('Y-m-d',time());//当天


            //有效名单:本周或本月新增有效名单/新增毛名单--与时间筛选有关 --------------  开始
            $ValidClientList = $this->DataControl->selectOne(" SELECT count(a.client_id) as allnum,sum(if(a.client_intention_maxlevel>3,1,0)) as maxlevelnum 
                FROM crm_client as a,crm_client_schoolenter as b 
                WHERE a.company_id = '{$request['company_id']}' and a.client_createtime > '{$starttime}' and a.client_createtime <= '{$endtime}'
                    and a.client_id = b.client_id and b.school_id = '{$schoolOne['school_id']}' and b.is_enterstatus = '1' 
                ");
            //有效名单:本周或本月新增有效名单/新增毛名单--与时间筛选有关 --------------  结束

            //美语报名数量 --------------  开始 ---- 报名数量名字改为美语报名数量:本周或本月已报名缴费数/到访数
                //到访数量
            $visitClientList = $this->DataControl->selectOne(" SELECT count(a.client_id) as allnum
                FROM crm_client as a,crm_client_schoolenter as b
                WHERE a.company_id = '{$request['company_id']}' and a.client_createtime > '{$starttime}' and a.client_createtime <= '{$endtime}'
                    and a.client_id = b.client_id and b.school_id = '{$schoolOne['school_id']}' and b.is_enterstatus = '1'  
                    and 
                        (
                        exists (select 1 from crm_client_invite as x where a.client_id = x.client_id and x.invite_isvisit = '1' limit 0,1) 
                        or exists (select 1 from crm_client_audition as x where a.client_id = x.client_id and x.audition_isvisit = '1' limit 0,1) 
                        ) 
                ");
                //美语报名数 -- 子龙提供的sql
            $signupMYClient = $this->DataControl->selectOne(" select count(info_id) as count_regis
                from smc_student_registerinfo
                where school_id='{$schoolOne['school_id']}'
                and info_status=1
                and coursetype_id=65
                and pay_successtime >= '{$starttime}' -- 期初
                and pay_successtime <= '{$endtime}' -- 期末
                ");
            //美语报名数量 --------------  结束  ------ 报名数量名字改为美语报名数量:本周或本月已报名缴费数/到访数

            //美语班级数量-- 与时间筛选无关，显示当前数据-------------  开始 ------------- 显示美语班组当前进行中的班级数量 与时间筛选无关，显示当前数据
            //$clientdatawhere 共用的有效名单的
            $classMYlist = $this->DataControl->selectOne(" select count(a.class_id) as count_class
                    from smc_class a,smc_course b
                    where a.course_id=b.course_id
                    and a.school_id='{$schoolOne['school_id']}'
                    and a.class_status=1
                    and a.class_type=0
                    and a.class_isfictitious=0
                    and b.coursetype_id=65
                    and a.class_stdate <= '{$nowstrtime}' -- 期末
                    and a.class_enddate >= '{$nowstrtime}' -- 期末
                ");
            //美语班级数量--与时间筛选有关 --------------  结束

            //流失学员--与时间筛选有关 --------------  开始 ------------- 显示本周或本月美语班组流失的学员数量
            $lossMYStulist = $this->DataControl->selectOne(" select count(changelog_id) as count_lost
                    from smc_student_changelog
                    where school_id='{$schoolOne['school_id']}'
                    and stuchange_code='C04'
                    and coursetype_id=65
                    and changelog_day >= '{$strstarttime}' -- 期初
                    and changelog_day <= '{$strendtime}' -- 期末
                ");
            //流失学员--与时间筛选有关 --------------  结束

            //美语年度留班率--与时间筛选有关 --------------  开始 ------------- 本年度留班率显示为百分率(89%):本年度该校区留班率-固定时间显示
            $MYStuNumlist = $this->DataControl->selectOne(" 
                    select ifnull(concat(round(count(if(y.study_upgraderate>=100,true,null)) /count(y.study_id)*100,1),'%'),'--') as percentage
                    from smc_class_endcalc x,smc_class_endcalc_study y,smc_class z
                    where x.endcalc_id=y.endcalc_id and x.class_id=z.class_id
                    and x.school_id='{$schoolOne['school_id']}'
                    and x.coursetype_id=65
                    and y.study_iscalculate >= 0
                    and y.study_outtype in('0','1','3','4')
                    and z.class_isnotrenew=0
                    and z.class_status>-2
                    and z.class_type=0
                    and z.class_enddate >= '{$strstarttime}' -- 年初
                    and z.class_enddate <= '{$strendtime}' -- 今天
                    and not exists(select 1 from smc_class_breakoff where class_id=x.class_id and breakoff_status>=2 and breakoff_type=0)
                ");
            //美语年度留班率--与时间筛选有关 --------------  结束

            //续费预警--与时间筛选有关 --------------  开始 ------------- 显示在本周或本月结束或即将结束的班级学生
            $promptRenewStulist = $this->DataControl->selectOne(" 
                    select count(study_id) as allnum from (
                    select a.study_id
                    ,ifnull((SELECT rs.trackresult_name FROM smc_student_track AS st, smc_code_trackresult AS rs 
                    WHERE rs.trackresult_id = st.result_id AND st.student_id = a.student_id and st.school_id=b.school_id 
                    and (st.coursetype_id=0 or st.coursetype_id=e.coursetype_id) and st.track_from=0 AND st.track_classname = '续费电访'
                    AND track_day>=b.class_stdate ORDER BY st.track_id DESC LIMIT 0, 1 ),'') AS connect_times
                    ,100 as coursetype_renewtimes
                    ,ifnull((select sum(coursebalance_time*z.course_classtimerates) from smc_student_coursebalance X,smc_course Z
                    where X.company_id=a.company_id AND X.student_id=a.student_id AND X.course_id=Z.course_id
                    AND Z.coursetype_id=e.coursetype_id and X.course_id<>b.course_id),0) as renewal_rates
                    ,ifnull((select sum(y.course_classtimerates) from smc_school_income x,smc_course Y 
                    where x.course_id=Y.course_id and x.student_id=a.student_id and x.company_id=a.company_id
                    and X.income_type='0' AND income_confirmtime>=UNIX_TIMESTAMP(a.study_endday) 
                    and y.coursetype_id=e.coursetype_id and x.class_id<>a.class_id),0) as spend_rates
                    ,ifnull((select sum(unpaid_timesrate) from smc_student_unpaid where school_id=a.school_id 
                    and student_id=a.student_id and coursetype_id=e.coursetype_id),0) as unpaid_rates
                    from smc_student_study a
                    left join smc_class b on a.class_id=b.class_id
                    left join smc_course e on b.course_id=e.course_id 
                    where b.class_status > -2 
                    and b.class_type=0 
                    and e.course_sellclass=0
                    and e.course_classnum > 10 
                    and b.school_id='{$schoolOne['school_id']}' 
                    and b.class_enddate>= '{$strstarttime}'  -- 期初
                    and b.class_enddate<= '{$strendtime}'  -- 期末(周末或者月末)
                    and e.coursetype_id= 65
                    and a.study_endday>=b.class_enddate 
                    and e.course_isrenew= 1
                    and b.class_isnotrenew=0 
                    and not exists(select 1 from smc_class_breakoff where class_id=b.class_id and breakoff_status>=2 and breakoff_type=0)
                    having 1 and ((renewal_rates+spend_rates-unpaid_rates) < coursetype_renewtimes and ifnull(connect_times,'') <> '续费')
                    )ta
                ");
            //续费预警--与时间筛选有关 --------------  结束

            //已续费/应续费--与时间筛选有关 --------------  开始 ------------- 本周或本月已经结束的班级
            $RenewStulist = $this->DataControl->selectOne(" 
                select count(if( ((renewal_rates+spend_rates-unpaid_rates)>=coursetype_renewtimes or ifnull(connect_times,'')='续费'),true,null)) as count_yet
                ,count(study_id) as count_all 
                from (
                select a.study_id
                ,ifnull((SELECT rs.trackresult_name FROM smc_student_track AS st, smc_code_trackresult AS rs 
                WHERE rs.trackresult_id = st.result_id AND st.student_id = a.student_id and st.school_id=b.school_id 
                and (st.coursetype_id=0 or st.coursetype_id=e.coursetype_id) and st.track_from=0 AND st.track_classname = '续费电访'
                AND track_day>=b.class_stdate ORDER BY st.track_id DESC LIMIT 0, 1 ),'') AS connect_times
                ,100 as coursetype_renewtimes
                ,ifnull((select sum(coursebalance_time*z.course_classtimerates) from smc_student_coursebalance X,smc_course Z
                where X.company_id=a.company_id AND X.student_id=a.student_id AND X.course_id=Z.course_id
                AND Z.coursetype_id=e.coursetype_id and X.course_id<>b.course_id),0) as renewal_rates
                ,ifnull((select sum(y.course_classtimerates) from smc_school_income x,smc_course Y 
                where x.course_id=Y.course_id and x.student_id=a.student_id and x.company_id=a.company_id
                and X.income_type='0' AND income_confirmtime>=UNIX_TIMESTAMP(a.study_endday) 
                and y.coursetype_id=e.coursetype_id and x.class_id<>a.class_id),0) as spend_rates
                ,ifnull((select sum(unpaid_timesrate) from smc_student_unpaid where school_id=a.school_id 
                and student_id=a.student_id and coursetype_id=e.coursetype_id),0) as unpaid_rates
                from smc_student_study a
                left join smc_class b on a.class_id=b.class_id
                left join smc_course e on b.course_id=e.course_id 
                where b.class_status > -2 
                and b.class_type=0 
                and e.course_sellclass=0
                and e.course_classnum > 10 
                and b.school_id='{$schoolOne['school_id']}' 
                and b.class_enddate>= '{$strstarttime}'  -- 期初
                and b.class_enddate<= '{$strendtime}'  -- 期末（不超过当前日期）
                and e.coursetype_id= 65
                and a.study_endday>=b.class_enddate 
                and e.course_isrenew= 1
                and b.class_isnotrenew=0 
                and not exists(select 1 from smc_class_breakoff where class_id=b.class_id and breakoff_status>=2 and breakoff_type=0) 
                )ta
                ");
            //已续费/应续费--与时间筛选有关 --------------  结束

            //转介绍--与时间筛选有关 --------------  开始
            //$clientdatawhere 共用的有效名单的
            $recommendClientList = $this->DataControl->selectOne(" SELECT count(a.client_id) as allnum
                FROM crm_client as a,crm_client_schoolenter as b,crm_code_channel as c 
                WHERE a.company_id = '{$request['company_id']}' and a.client_createtime > '{$starttime}' and a.client_createtime <= '{$endtime}'
                    and a.client_id = b.client_id and b.school_id = '{$schoolOne['school_id']}' and b.is_enterstatus = '1' 
                    and a.channel_id = c.channel_id and c.channel_medianame = '转介绍'
                ");
            //转介绍--与时间筛选有关 --------------  结束

            $datalist = array();
            $validClientNum = $ValidClientList['maxlevelnum']?(int)$ValidClientList['maxlevelnum']:'--'; //新增有效名单数
            $allClientNum = $ValidClientList['allnum']?(int)$ValidClientList['allnum']:'--'; //新增毛名单数
            $datalist['ClientNum'] = $validClientNum ."/".$allClientNum;//有效名单

            $signupMYClientNum = $signupMYClient['count_regis']?(int)$signupMYClient['count_regis']:'--'; //美语报名名单数
            $visitClientNum = $visitClientList['allnum']?(int)$visitClientList['allnum']:'--'; //到访名单数
            $datalist['MYClientNum'] = $signupMYClientNum ."/".$visitClientNum;//报名数量

            $datalist['classMYNum'] = $classMYlist['count_class']?(int)$classMYlist['count_class']:'--'; //美语班级数量

            $datalist['lossMYStuNum'] = $lossMYStulist['count_lost']?(int)$lossMYStulist['count_lost']:'--'; //美语流失学员

            $datalist['MYStuNum'] = $MYStuNumlist['percentage']?$MYStuNumlist['percentage']:'--';//美语年度留班率

            $datalist['promptRenewStuNum'] = $promptRenewStulist['allnum']?(int)$promptRenewStulist['allnum']:'--'; //续费预警

            $alreadyRenewStuNum = $RenewStulist['count_yet']?(int)$RenewStulist['count_yet']:'--'; //已续费
            $shouldRenewStuNum = $RenewStulist['count_all']?(int)$RenewStulist['count_all']:'--'; //应续费
            $datalist['RenewStuNum'] = $alreadyRenewStuNum ."/".$shouldRenewStuNum;//已续费/应续费

            $datalist['recommendClientNum'] = $recommendClientList['allnum']?(int)$recommendClientList['allnum']:'--'; //转介绍名单

            ajax_return(array('error' => '0', 'errortip' => '数据获取成功！', 'result' => $datalist));
        }else{
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //教务系统 园转校 -- 园展校内招-- 名单转化为校的 有效名单那  ---  园展校内招
    function addKidstuToSchoolClientAction(){
        $request = Input('post.','','trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            //访问日志
            $this->VeryLogs($pucArray['apiuser_id'],"{$request['u']}/{$request['t']}{$request['api']}",$request);

            $schoolOne = $this->DataControl->getFieldOne("smc_school", "school_id,company_id", "school_branch = '{$request['school_branch']}' ");
            if(!$schoolOne){
                $res = array('error' => '1', 'errortip' => '您传入的校区编号不正确!','result' => array());
                ajax_return($res,$request['language_type']);
            }
            if(isset($request['client_mobile']) && $request['client_mobile'] != '' ){
                if(!checkMobile($request['client_mobile'])){
                    $res = array('error' => '1', 'errortip' => '您传入的手机号码格式不正确!','result' => array());
                    ajax_return($res,$request['language_type']);
                }
            }

            $request['company_id'] = $schoolOne['company_id'];
            if($schoolOne['company_id'] == '8888'){
                $request['marketer_id'] = '15';
            }else{
                $request['marketer_id'] = '0';
            }
            $request['channel_name'] = '园展校内招';
            $request['client_easxstubranch'] = $request['student_branch'];

            $request['t_linktype'] = '园展校内招跟进';
            $request['school_id'] = $schoolOne['school_id'];
            $request['isschool'] = 1;
            $Model = new \Model\Crm\ClientModel($request);
            $dataList = $Model->addPhoneChannelAction($request);
            if($dataList){
                $Model->result = $Model->result?$Model->result:array();
                $res = array('error' => '0', 'errortip' => "你的信息提交成功，请耐心等待教师回电。",'result' => $Model->result);
            }else{
                if($Model->errortip){
                    $Model->result = $Model->result?$Model->result:array();
                    $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $Model->result);
                }else {
                    $res = array('error' => '1', 'errortip' => '你的信息提交失败。' ,'result' => array());
                }
            }
            ajax_return($res,$request['language_type']);
        }else{
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //教务 - 校务 添加跟踪记录
    function addJwToClientTrackAction(){
        $request = Input('post.','','trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            //访问日志
            $this->VeryLogs($pucArray['apiuser_id'],"{$request['u']}/{$request['t']}{$request['api']}",$request);

            $request['client_easxstubranch'] = $request['student_branch'];
            $schoolOne = $this->DataControl->getFieldOne("smc_school", "school_id,company_id", "school_branch = '{$request['school_branch']}' ");
            if(!$schoolOne){
                $res = array('error' => '1', 'errortip' => '您传入的校区编号不正确!','result' => array());
                ajax_return($res,$request['language_type']);
            }
            $clientOne = $this->DataControl->getFieldOne("crm_client", "client_id", "company_id = '{$schoolOne['company_id']}' and client_easxstubranch = '{$request['client_easxstubranch']}' ");
            if(!$clientOne){
                $res = array('error' => '1', 'errortip' => '对应名单不存在!','result' => array());
                ajax_return($res,$request['language_type']);
            }

            $track_note = "【{$request['track_day']}】【{$request['trackresult_name']}】【{$request['track_note']}】";

            $trackData = array();
            $trackData['client_id'] = $clientOne['client_id'];
            if($schoolOne['school_id'] > 0){
                $trackData['school_id'] = $schoolOne['school_id'];
            }
            $trackData['marketer_id'] = '0';
            $trackData['marketer_name'] = '系统';
            $trackData['track_validinc'] = 1;
            $trackData['track_followmode'] = 0;
            $trackData['track_linktype'] = '园展校内招跟进';
            $trackData['track_note'] = $track_note;
            $trackData['track_createtime'] = time();
            $trackData['track_type'] = 0;
            $trackData['track_initiative'] = 0;
            $trackID = $this->DataControl->insertData('crm_client_track', $trackData);

            if($trackID){
                $res = array('error' => '0', 'errortip' => "你的信息提交成功。",'result' => array());
            }else{
                $res = array('error' => '1', 'errortip' => '你的信息提交失败。' ,'result' => array());
            }
            ajax_return($res,$request['language_type']);
        }else{
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    function getNeedConfirmOpenClassListApi(){

        $request = Input('get.','','trim,addslashes');

        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            //访问日志
            $this->VeryLogs($pucArray['apiuser_id'],"{$request['u']}/{$request['t']}{$request['api']}",$request);

            $schoolOne = $this->DataControl->getFieldOne("smc_school", "school_id,company_id", "school_branch = '{$request['school_branch']}' ");

            if(!$schoolOne){
                $res = array('error' => '1', 'errortip' => '您传入的校区编号不正确!','result' => array());
                ajax_return($res,$request['language_type']);
            }else{
                $request['school_id'] = $schoolOne['school_id'];
            }


            $ClassModel = new \Model\Smc\ClassModel($request);
            $res = $ClassModel->getNeedConfirmOpenClassList($request);
            $field = array();

            $k = 0;

            $field[$k]["fieldstring"] = "class_cnname";
            $field[$k]["fieldname"] = "班级名称";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $field[$k]["ismethod"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "class_enname";
            $field[$k]["fieldname"] = "班级别名";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "class_branch";
            $field[$k]["fieldname"] = "班级编号";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "stuNum";
            $field[$k]["fieldname"] = "人数";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "course_cnname";
            $field[$k]["fieldname"] = "课程别名称";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "course_branch";
            $field[$k]["fieldname"] = "课程别编号";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;
            $field[$k]["fieldstring"] = "sonTeacher";
            $field[$k]["fieldname"] = "教师";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "class_timestr";
            $field[$k]["fieldname"] = "上课时间";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "timerange";
            $field[$k]["fieldname"] = "开班结班日期";
            $field[$k]["show"] = 0;
            $field[$k]["custom"] = 0;
            $k++;


            $result = array();
            $result["field"] = $field;
            $result["allnum"] = $res['allnum'];
            if ($res) {
                $result["fieldcustom"] = 1;
                $result["list"] = $res['list'];
                $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
            } else {
                $result["list"] = array();
                $result["allnum"] = 0;
                $res = array('error' => 1, 'errortip' => $ClassModel->errortip, 'result' => $result);
            }
            ajax_return($res,$request['language_type']);
        }else{
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }


    function getSourceClassListApi(){

        $request = Input('get.','','trim,addslashes');

        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            //访问日志
            $this->VeryLogs($pucArray['apiuser_id'],"{$request['u']}/{$request['t']}{$request['api']}",$request);


            $schoolOne = $this->DataControl->getFieldOne("smc_school", "school_id,company_id", "school_branch = '{$request['school_branch']}' ");
            if(!$schoolOne){
                $res = array('error' => '1', 'errortip' => '您传入的校区编号不正确!','result' => array());
                ajax_return($res,$request['language_type']);
            }else{
                $request['school_id'] = $schoolOne['school_id'];
            }


            $classOne = $this->DataControl->getFieldOne("smc_class", "class_id", "class_branch = '{$request['class_branch']}'");

            if(!$classOne){
                $res = array('error' => '1', 'errortip' => '您传入的班级编号不正确!','result' => array());
                ajax_return($res,$request['language_type']);
            }else{
                $request['class_id'] = $classOne['class_id'];
            }

            $Model = new \Model\Smc\ClassModel($request);

            $res = $Model->getSourceClassList($request);

            $field = array();
            $k = 0;
    
            $field[$k]["fieldstring"] = "class_cnname";
            $field[$k]["fieldname"] = "班级名称";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;
    
            $field[$k]["fieldstring"] = "class_enname";
            $field[$k]["fieldname"] = "班级别名";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;
    
            $field[$k]["fieldstring"] = "class_branch";
            $field[$k]["fieldname"] = "班级编号";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;
    
            $field[$k]["fieldstring"] = "course_cnname";
            $field[$k]["fieldname"] = "课程别名称";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;
    
            $field[$k]["fieldstring"] = "course_branch";
            $field[$k]["fieldname"] = "课程别编号";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;
    
            $result = array();
            $result["field"] = $field;
    
            if ($res) {
                $result["list"] = $res;
                $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
            } else {
                $result["list"] = array();
                $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
            }
            ajax_return($res,$request['language_type']);
        }else{
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    function getLessonPlanListApi(){

        $request = Input('get.','','trim,addslashes');

        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            //访问日志
            $this->VeryLogs($pucArray['apiuser_id'],"{$request['u']}/{$request['t']}{$request['api']}",$request);


            $schoolOne = $this->DataControl->getFieldOne("smc_school", "school_id,company_id", "school_branch = '{$request['school_branch']}' ");
            if(!$schoolOne){
                $res = array('error' => '1', 'errortip' => '您传入的校区编号不正确!','result' => array());
                ajax_return($res,$request['language_type']);
            }else{
                $request['school_id'] = $schoolOne['school_id'];
            }
            
            $classOne = $this->DataControl->getFieldOne("smc_class", "class_id", "class_branch = '{$request['class_branch']}'");

            if(!$classOne){
                $res = array('error' => '1', 'errortip' => '您传入的班级编号不正确!','result' => array());
                ajax_return($res,$request['language_type']);
            }else{
                $request['class_id'] = $classOne['class_id'];
            }


            $classModel = new \Model\Smc\ClassModel($request);
            $list = $classModel->getLessonPlanList($request);
            $fieldstring = array( 'lessonplan_week', 'time', 'hour_way_name', 'staffer_cnname', 'assistant_staffer_cnname', 'classroom_cnname');
            $fieldname = array( '星期', '上课时间', '上课方式', '主教教师', '助教教师', '上课教室');
            $fieldcustom = array('1', '1', "1", "1", "1", "1");
            $fieldshow = array('1', '1', "1", "1", "1", "1");

            $field = array();
            for ($i = 0; $i < count($fieldstring); $i++) {
                $field[$i]["fieldstring"] = trim($fieldstring[$i]);
                $field[$i]["fieldname"] = trim($fieldname[$i]);
                $field[$i]["custom"] = trim($fieldcustom[$i]);
                $field[$i]["show"] = trim($fieldshow[$i]);
            }

            $result['field'] = $field;
            $result['list'] = $list['list'];
            $result['hour_last_day'] = $list['hour_last_day'];
            $result['child_hournum'] = $list['child_hournum'];
            $res = array('error' => '0', 'errortip' => '获取信息', 'result' => $result);
            ajax_return($res, $request['language_type']);
        }else{
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    function confirmOpenClassAction(){

        $request = Input('post.','','trim,addslashes');

        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            //访问日志
            $this->VeryLogs($pucArray['apiuser_id'],"{$request['u']}/{$request['t']}{$request['api']}",$request);


            $schoolOne = $this->DataControl->getFieldOne("smc_school", "school_id,company_id", "school_branch = '{$request['school_branch']}' ");
            if(!$schoolOne){
                $res = array('error' => '1', 'errortip' => '您传入的校区编号不正确!','result' => array());
                ajax_return($res,$request['language_type']);
            }else{
                $request['school_id'] = $schoolOne['school_id'];
            }
            
            $classOne = $this->DataControl->getFieldOne("smc_class", "class_id,class_isconfirmopen", "class_branch = '{$request['class_branch']}'");

            if(!$classOne){
                $res = array('error' => '1', 'errortip' => '您传入的班级编号不正确!','result' => array());
                ajax_return($res,$request['language_type']);
            }

            if($classOne['class_isconfirmopen']==1){
                $res = array('error' => '1', 'errortip' => '该班级已经开班了!','result' => array());
                ajax_return($res,$request['language_type']);

            }


            if( isset($request['from_class_branch']) && $request['from_class_branch'] != '' ){

                $fromClassOne = $this->DataControl->getFieldOne("smc_class", "class_id", "class_branch = '{$request['from_class_branch']}'");
                $request['class_id'] = $classOne['class_id'];
                $request['from_class_id'] = $fromClassOne['class_id'];

                $Model = new \Model\Smc\ClassModel($request);
                $res = $Model->addSourceClass($request);

                if(!$res){
                    $res = array('error' => '1', 'errortip' => $Model->errortip,'result' => array());
                    ajax_return($res,$request['language_type']);
                }
            }


            $data=array();
            $data['class_isconfirmopen']=1;
            $data['class_updatatime']=time();
            if($this->DataControl->updateData("smc_class","class_id='{$classOne['class_id']}'",$data)){
                $res = array('error' => '0', 'errortip' => '开班成功!','result' => array());
            }else{
                $res = array('error' => '1', 'errortip' => '操作失败!','result' => array());
              
            }
            ajax_return($res,$request['language_type']);

        }else{
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }




}