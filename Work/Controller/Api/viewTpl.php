<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2016/12/13
 * Time: 21:21
 */
namespace Work\Controller\Api;


class viewTpl {
    public $DataControl;
    public $router;

    public function __construct(){
        global $router;
        // 指定允许其他域名访问
        header('Access-Control-Allow-Origin:*');
        // 响应类型
        header('Access-Control-Allow-Methods:*');
        // 响应头设置
        header('Access-Control-Allow-Headers:x-requested-with,content-type');

        //数据库操作
        $this->DataControl = new \Dbmysql();
        //操作类型
        $this->router = $router;
    }

    function getToken($params=array()){
        $stafferOne = $this->DataControl->getFieldOne('smc_staffer',"staffer_id,staffer_tokencode,staffer_tokenencrypt","staffer_id='{$params['staffer_id']}'");
        if(!$stafferOne)
        {
            return false;
        }
        $md5tokenbar = base64_encode(md5($stafferOne["staffer_tokencode"].date("Y-m-d")));
        if($md5tokenbar == $stafferOne["staffer_tokenencrypt"]){
            $token = $stafferOne["staffer_tokenencrypt"];
        }else{
            //目前这里注释是为了测试方便
            $tokencode = rand(111111,999999);
            $md5tokenbar = base64_encode(md5($tokencode.date("Y-m-d")));
            $this->DataControl->query("UPDATE smc_staffer SET staffer_tokencode = '{$tokencode}',staffer_tokenencrypt = '{$md5tokenbar}' WHERE staffer_id ='{$stafferOne['staffer_id']}'");
            $token = $md5tokenbar;
//            $token = $stafferOne["staffer_tokenencrypt"];
        }
        return $token;
    }

    //用户获取token
    function getParentToken($params=array()){
        $parenterOne = $this->DataControl->getFieldOne("smc_parenter","parenter_id,parenter_tokencode,parenter_tokenencrypt","parenter_id='{$params['parenter_id']}'");
        if(!$parenterOne)
        {
            return false;
        }
        $md5tokenbar = base64_encode(md5($parenterOne["parenter_tokencode"].date("Y-m-d")));
        if($md5tokenbar == $parenterOne["parenter_tokenencrypt"]){
            $token = $parenterOne["parenter_tokenencrypt"];
        }else{
            $tokencode = rand(111111,999999);
            $md5tokenbar = base64_encode(md5($tokencode.date("Y-m-d")));
            $this->DataControl->query("UPDATE smc_parenter SET parenter_tokencode = '{$tokencode}',parenter_tokenencrypt = '{$md5tokenbar}' WHERE parenter_id ='{$parenterOne['parenter_id']}'");
            $token = $md5tokenbar;
        }
        return $token;
    }

    function ThisVerify($request){
        $paramArray = array();
        $paramArray['staffer_id'] = $request['staffer_id'];
        $paramArray['school_id'] = $request['school_id'];
        $paramArray['company_id'] = $request['company_id'];
        $paramArray['token'] = $request['token'];
        if(!$this->UserLimit($paramArray)){
            $result = array();
            $result["list"] = array();
            $result["tokeninc"] = "0";
            $res = array('error' => 1, 'errortip' => "用户token失效", 'result' => $result);
            ajax_return($res,$request['language_type']);
        }
    }

    //第三方接口权限验证
    function UserLimit($paramArray){
        $stafferOne = $this->DataControl->getFieldOne('smc_staffer',"staffer_tokencode","staffer_id='{$paramArray['staffer_id']}'");
        if($stafferOne){
            $md5tokenbar = base64_encode(md5($stafferOne["staffer_tokencode"].date("Y-m-d")));
            if($md5tokenbar != $paramArray['token']){
                return false;
            }else{
                return true;
            }
        }else{
            return false;
        }
    }

    //发送短信
    public function Sendmisgo($mobile,$mistxt,$tilte,$sendcode,$company_id='0'){
        $publicarray = array();
        $publicarray['company_id'] = $company_id;
        $minsendModel = new \Model\Api\SmsModel($publicarray);
        return $minsendModel->gmcMisSend($mobile,$mistxt,$tilte,$sendcode);
    }


    public function __call($method, $args) {
        echo "unknown method " . $method;
        return false;

    }
}