<?php


namespace Work\Controller\Api;

class HeadBopayController extends viewTpl
{
    public $u;
    public $t;
    public $c;
    public $visitType = "api";


    //预加载处理类
    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
        $this->api = $this->router->getApi();
    }

    function testPayApi(){
        $HeadPay = new \Model\Api\HeadboingPayModel();
        $HeadPay->testVery();
    }


    //获取交易二维码
    function qrcodeapplyApi(){
        $HeadPay = new \Model\Api\HeadboingPayModel();

        $orderInfo = [
            'orderId'    => "SC".date('Ymd').random_int(100, 999),
            'payValidTime'=>'1000',
            'txnAmt' => '1',//交易金额，单位为分（必传）
            "body"=> "吉的堡支付-生活服务"
        ];

        $qrcode = $HeadPay->qrcodeapply($orderInfo);
        if($qrcode){
            var_dump($qrcode);
        }else{
            echo $HeadPay->errortip;
        }
    }


    //支付结果查询
    function orderqueryApi(){
        $HeadPay = new \Model\Api\HeadboingPayModel();

        $orderInfo = [
            'orderId'    => 'ZFO7XJ23031610322959649',
            'cmbOrderId'    => '100423031610323060735210'
        ];

        $qrcode = $HeadPay->orderquery($orderInfo);
        if($qrcode){
            var_dump($qrcode);
        }else{
            echo $HeadPay->errortip;
        }
    }

    //退款申请
    function refundApi(){
        $HeadPay = new \Model\Api\HeadboingPayModel();

        $orderInfo = [
            'orderId'    => 'TKCQUW23031318352610400',//退款订单号
            'origCmbOrderId'    => '100423031611233550034990',//退款订单号
            'origOrderId'    => 'ZF28D523031611233586835',//原交易商户订单号
            'txnAmt'    => '1',//原交易金额
            'refundAmt'    => '1',//退款金额
            'refundReason'    => "吉的堡API退款，手工唤起API退款流程！"//退款原因
        ];

        $qrcode = $HeadPay->refund($orderInfo);
        if($qrcode){
            var_dump($qrcode);
        }else{
            echo $HeadPay->errortip;
        }
    }

    //退款结果查询
    function refundqueryApi(){
        $HeadPay = new \Model\Api\HeadboingPayModel();

        $orderInfo = [
            'orderId'    => ""//退款订单号
        ];

        $qrcode = $HeadPay->refundquery($orderInfo);
        if($qrcode){
            var_dump($qrcode);
        }else{
            echo $HeadPay->errortip;
        }
    }

    //关闭订单
    function closeOrderApi(){
        $HeadPay = new \Model\Api\HeadboingPayModel();

        $orderInfo = [
            'origOrderId'    => "ZFKUXS23032010375398494",//关闭订单的订单号
            'origCmbOrderId'    => "100423032010375481428672"
        ];

        $qrcode = $HeadPay->closeOrder($orderInfo);
        if($qrcode){
            var_dump($qrcode);
        }else{
            echo $HeadPay->errortip;
        }
    }

    //付款码收款
    function payApi(){
        $HeadPay = new \Model\Api\HeadboingPayModel();

        $orderInfo = [
            'orderId'    => "",//商户订单号
            'authCode'    => "",//授权码?
            'txnAmt'    => "",//交易金额
            'body'    => "",//商品描述
        ];

        $qrcode = $HeadPay->pay($orderInfo);
        if($qrcode){
            var_dump($qrcode);
        }else{
            echo $HeadPay->errortip;
        }
    }

    //付款码收款撤销
    function cancelPayApi(){
        $HeadPay = new \Model\Api\HeadboingPayModel();

        $orderInfo = [
            'origOrderId'    => "ZF1J6B23031410553344656",//原交易商户订单号
            'origCmbOrderId'    => "100423031410553331087168"
        ];

        $qrcode = $HeadPay->cancelPay($orderInfo);
        if($qrcode){
            var_dump($qrcode);
        }else{
            echo $HeadPay->errortip;
        }
    }

    //微信统一下单
    function onlinepayApi(){
        $HeadPay = new \Model\Api\HeadboingPayModel();

        $orderInfo = [
            'subAppId'    => "",//子公众账号ID
            'orderId'    => "",
            'tradeType'    => "",//交易类型 APP支付：APP  公众号支付：JSAPI  小程序支付：JSAPI
            'txnAmt'    => ""//交易金额
        ];

        $qrcode = $HeadPay->onlinepay($orderInfo);
        if($qrcode){
            var_dump($qrcode);
        }else{
            echo $HeadPay->errortip;
        }
    }

    //微信小程序下单
    function MiniAppOrderApplyApi(){
        $HeadPay = new \Model\Api\HeadboingPayModel();

        $orderInfo = [
            'orderId'    => "SC".date('Ymd').random_int(100, 999),
            'tradeType'    => "JSAPI",//交易类型 小程序支付：JSAPI
            'body'    => "测试",//商品描述
            'txnAmt'    => "1",//交易金额
            'spbillCreateIp'    => real_ip(),//交易金额
        ];

        $qrcode = $HeadPay->MiniAppOrderApply($orderInfo);
        if($qrcode){
            var_dump($qrcode);
        }else{
            echo $HeadPay->errortip;
        }
    }









}