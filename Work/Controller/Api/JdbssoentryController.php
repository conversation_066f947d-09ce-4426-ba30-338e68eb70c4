<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/5/27
 * Time: 11:56
 */

namespace Work\Controller\Api;


use Model\Smc\CheckingModel;
use Model\Smc\CourseModel;

class JdbssoentryController extends viewTpl
{
    public $u;
    public $t;
    public $c;
    public $visitType = "api";


    //预加载处理类
    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
        $this->api = $this->router->getApi();
    }

    //http://api.kcclassin.com/Jdbssoentry/guanbiUrl
    function guanbiUrlView(){
        $timestamp = time();
        $userInfo = "{\"domainId\":\"guanbi\",\"externalUserId\":\"yzzy\",\"timestamp\":{$timestamp}}";
        $RsaTo = new \Rsaencdec("Core/Tools/threeca/guanyuan/sso_public_key.pem", "Core/Tools/threeca/guanyuan/sso_private_key.pem");
        $sing_byte = bin2hex($RsaTo->encryptPri($userInfo,"base64"));

        $InOurtUrl = "https://jidebao.guandatacloud.com/home/<USER>/b227d8f23f344492ab91570d?provider=guanbi&ssoToken={$sing_byte}";
        header("Location:{$InOurtUrl}");
        exit;
        echo $sing_byte;
        echo "<br />";
        //$url_sing_byte = urlencode($sing_byte);
        $sing_byte = $RsaTo->decrypt($sing_byte,'base64');
        echo $sing_byte;

    }

    //http://api.kcclassin.com/Jdbssoentry/guanbiscUrl
    function guanbiscUrlView(){
        $timestamp = time();
        $userInfo = "{\"domainId\":\"guanbi\",\"externalUserId\":\"sczy\",\"timestamp\":{$timestamp}}";
        $RsaTo = new \Rsaencdec("Core/Tools/threeca/guanyuan/sso_public_key.pem", "Core/Tools/threeca/guanyuan/sso_private_key.pem");
        $sing_byte = bin2hex($RsaTo->encryptPri($userInfo,"base64"));

        $InOurtUrl = "https://jidebao.guandatacloud.com/m/portal?provider=guanbi&ssoToken={$sing_byte}";
        header("Location:{$InOurtUrl}");
        exit;
        echo $sing_byte;
        echo "<br />";
        //$url_sing_byte = urlencode($sing_byte);
        $sing_byte = $RsaTo->decrypt($sing_byte,'base64');
        echo $sing_byte;

    }

    function jdbssoApi(){
        $request = Input('get.','','trim,addslashes');
        $userAttribute = $request['userAttribute'];
        $userSign = $request['userSign'];
        $school_branch = $request['school_branch'];
        $module_url = $request['module_url'];
        $Workeross = new \Model\Api\JdbssoModel();
        $userArray = $Workeross->getDataToInfp($userAttribute);

        if(!$Workeross->getDataVerify($userAttribute,$userSign)){
            ajax_return(array('error' => 1,'errortip' => "授权信息不存在，禁止登录!"),'cn');
        }else{
            $companyOne = $this->DataControl->getFieldOne('gmc_company',"company_id,company_language","company_id = '8888'");
            if($companyOne){

                $stafferOne = $this->DataControl->getFieldOne('smc_staffer',"staffer_id,staffer_leave,staffer_pass,account_class,staffer_istest","staffer_employeepid = '{$userArray['jobnumber']}'");

//                $ComPost = $this->DataControl->getFieldOne("gmc_staffer_postbe","postbe_id","staffer_id = '{$stafferOne['staffer_id']}' and school_id = '0'");
//
//                $ScPost = $this->DataControl->getFieldOne("gmc_staffer_postbe","postbe_id","staffer_id = '{$stafferOne['staffer_id']}' and school_id <> '0'");
//
//                if($ComPost && $ScPost){
//                    $status = '0';
//                }
//                if(!$ComPost && $ScPost){
//                    $status = '2';
//                }
//                if($ComPost && !$ScPost){
//                    $status = '1';
//                }
//
//                $isAdmin = $stafferOne['account_class'];

                if($stafferOne){

                    $sql="select m.product_id 
                          from imc_module as m,imc_product as p 
                          where m.product_id=m.product_id and m.module_markurl='{$module_url}'";

                    $moduleOne=$this->DataControl->selectOne($sql);

                    if(!$moduleOne){
                        ajax_return(array('error' => 1,'errortip' => "模块设置错误，禁止登录!"),'cn');
                    }


                    if($stafferOne['staffer_leave'] == '0'){

                        $schoolOne=$this->DataControl->getFieldOne("smc_school","school_id","school_branch='{$school_branch}'");

                        $istaffer = array();
                        $istaffer['staffer_id'] = $stafferOne['staffer_id'];
                        $istaffer['school_id'] = $schoolOne['school_id'];
                        $istaffer['company_id'] = $companyOne['company_id'];
                        $istaffer['company_language'] = $companyOne['company_language'];
                        $istaffer['staffer_istest'] = $stafferOne['staffer_istest'];

                        $sql = "select sp.school_id,sp.postbe_id
                                FROM gmc_staffer_postbe as sp,smc_school as sc
                                where sp.school_id=sc.school_id and sp.staffer_id = '{$stafferOne['staffer_id']}' 
                                and sc.school_branch='{$school_branch}' and sp.postbe_status = '1'
                                order by sp.postbe_ismianjob DESC limit 0,1";
                        $smcPostOne = $this->DataControl->selectOne($sql);

                        $sql = "select sp.school_id,sp.postbe_id
                                FROM gmc_staffer_postbe as sp
                                where sp.staffer_id = '{$stafferOne['staffer_id']}' and sp.school_id = '0' and sp.postbe_status = '1'
                                order by sp.postbe_ismianjob DESC limit 0,1";
                        $gmcPostOne = $this->DataControl->selectOne($sql);



                        $istaffer['token'] = $this->getToken($stafferOne);

                        if($moduleOne['product_id']==1){

                            if($stafferOne['account_class']!=1){
                                if(!$gmcPostOne){
                                    ajax_return(array('error' => 1,'errortip' => "无权限，禁止登录!"),'cn');
                                }

                                $postbe_id=$gmcPostOne['postbe_id'];
                            }


                            $InOurtUrl="http://gmc.kcclassin.com/havePermission?staffer_id={$istaffer['staffer_id']}&token={$istaffer['token']}&company_id={$istaffer['company_id']}&postbe_id={$postbe_id}&language={$companyOne['company_language']}";

                        }elseif($moduleOne['product_id']==2){
                            if($stafferOne['account_class']!=1){
                                if(!$gmcPostOne && !$smcPostOne){
                                    ajax_return(array('error' => 1,'errortip' => "无权限，禁止登录!"),'cn');
                                }
                                $postbe_id=$smcPostOne?$smcPostOne['postbe_id']:$gmcPostOne['postbe_id'];
                            }

                            $InOurtUrl="http://smc.kcclassin.com/havePermission?staffer_id={$istaffer['staffer_id']}&token={$istaffer['token']}&company_id={$istaffer['company_id']}&postbe_id={$postbe_id}&school_id={$istaffer['school_id']}&language={$companyOne['company_language']}";
                        }elseif($moduleOne['product_id']==3){
                            if($stafferOne['account_class']!=1){
                                if(!$gmcPostOne && !$smcPostOne){
                                    ajax_return(array('error' => 1,'errortip' => "无权限，禁止登录!"),'cn');
                                }
                                $postbe_id=$smcPostOne?$smcPostOne['postbe_id']:$gmcPostOne['postbe_id'];
                            }

                            $InOurtUrl="http://crm.kcclassin.com/havePermission?staffer_id={$istaffer['staffer_id']}&token={$istaffer['token']}&company_id={$istaffer['company_id']}&postbe_id={$postbe_id}&school_id={$istaffer['school_id']}&language={$companyOne['company_language']}";
                        }else{
                            ajax_return(array('error' => 1,'errortip' => "无权限，禁止登录!"),'cn');


//                            $InOurtUrl = "https://sclogin.kedingdang.com/choose?token={$istaffer['token']}&staffer_id={$istaffer['staffer_id']}&company_id={$istaffer['company_id']}&status={$status}&isAdmin={$isAdmin}&language={$companyOne['company_language']}";
                        }

                        $this->DataControl->updateData("smc_staffer", "staffer_id = '{$stafferOne['staffer_id']}'", array("staffer_lasttime" => time(), "staffer_lastip" => real_ip()));

                        $InOurtUrl.='&tourl='.$module_url;
                        header("Location:{$InOurtUrl}");
                        exit;
                    }else{
                        ajax_return(array('error' => 1,'errortip' => "您已离职，账户权限已被关闭!"),$companyOne['company_language']);
                    }
                }else{
                    ajax_return(array('error' => 1,'errortip' => "职工账户信息不存在!"),$companyOne['company_language']);
                }
            }else{
                ajax_return(array('error' => 1,'errortip' => "企业账户不存在，请确认授权码是否正确!"),$companyOne['company_language']);
            }
        }
    }






}