<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2021/3/8
 * Time: 15:20
 */

namespace Work\Controller\Api;


class JdbssoentryContr extends viewTpl
{
    public $u;
    public $t;
    public $c;
    public $visitType = "api";


    //预加载处理类
    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
        $this->api = $this->router->getApi();
    }


    //缴费中心
    function smcPaycenterApi(){
        $request = Input('get.','','trim,addslashes');
        $userAttribute = $request['userAttribute'];
        $userSign = $request['userSign'];
        $Workeross = new \Model\Api\JdbssoModel();
        $userArray = $Workeross->getDataToInfp($userAttribute);
        //$InOurtUrl = "https://smc.kedingdang.com/choose?token={$istaffer['token']}&staffer_id={$istaffer['staffer_id']}&company_id={$istaffer['company_id']}&status={$status}&isAdmin={$isAdmin}&language=zh";
        //header("Location:{$InOurtUrl}");
        exit;
    }


    function jdbssoApi(){
        $request = Input('get.','','trim,addslashes');
        $userAttribute = $request['userAttribute'];
        $userSign = $request['userSign'];
        $Workeross = new \Model\Api\JdbssoModel();
        $userArray = $Workeross->getDataToInfp($userAttribute);
        if(!$Workeross->getDataVerify($userAttribute,$userSign)){
            ajax_return(array('error' => 1,'errortip' => "授权信息不存在，禁止登录!"),'cn');
        }else{
            $companyOne = $this->DataControl->getFieldOne('gmc_company',"company_id,company_language","company_id = '8888'");
            if($companyOne){
                $stafferOne = $this->DataControl->getFieldOne('smc_staffer',"staffer_id,staffer_leave,staffer_pass,account_class,staffer_istest","staffer_employeepid = '{$userArray['jobnumber']}'");
                $ComPost = $this->DataControl->getFieldOne("gmc_staffer_postbe","postbe_id","staffer_id = '{$stafferOne['staffer_id']}' and school_id = '0'");
                $ScPost = $this->DataControl->getFieldOne("gmc_staffer_postbe","postbe_id","staffer_id = '{$stafferOne['staffer_id']}' and school_id <> '0'");
                if($ComPost && $ScPost){
                    $status = '0';
                }
                if(!$ComPost && $ScPost){
                    $status = '2';
                }
                if($ComPost && !$ScPost){
                    $status = '1';
                }

                $isAdmin = $stafferOne['account_class'];
                if($stafferOne){
                    if($stafferOne['staffer_leave'] == '0'){
                        $istaffer = array();
                        $istaffer['staffer_id'] = $stafferOne['staffer_id'];
                        $istaffer['company_id'] = $companyOne['company_id'];
                        $istaffer['company_language'] = $companyOne['company_language'];
                        $istaffer['staffer_istest'] = $stafferOne['staffer_istest'];
                        if($stafferOne['account_class'] == '1'){
                            $schoolOne = $this->DataControl->getFieldOne('smc_school',"school_id","company_id = '{$companyOne['company_id']}' and  school_isclose = '0'","order by school_id ASC,school_istemp DESC");
                            $istaffer['school_id'] = $schoolOne['school_id'];
                        }else{
                            $schoolOne = $this->DataControl->selectOne("select sp.school_id,sp.postpart_id,sp.post_id
                                FROM gmc_staffer_postbe as sp
                                where sp.staffer_id = '{$stafferOne['staffer_id']}' and sp.school_id <> '0' and sp.postbe_status = '1'
                                order by sp.postbe_ismianjob DESC,sp.postbe_ismianjob DESC limit 0,1 ");
                            if($schoolOne && $schoolOne['postpart_id'] == '0'){
                                $postOne = $this->DataControl->getFieldOne('gmc_company_post',"postpart_id","post_id = '{$schoolOne['post_id']}'","order by post_id DESC limit 0,1");
                                if($postOne['postpart_id'] !=='0'){
                                    $this->DataControl->updateData("gmc_staffer_postbe", "staffer_id = '{$stafferOne['staffer_id']}' and school_id = '{$schoolOne['school_id']}' and post_id = '{$schoolOne['post_id']}'", array("postpart_id" => $postOne['postpart_id']));
                                }
                            }

                            $istaffer['school_id'] = $schoolOne['school_id']?$schoolOne['school_id']:0;

                            if($schoolOne['school_id'] =='0'){
                                $schoolOne = $this->DataControl->selectOne("select sp.school_id
                                FROM gmc_staffer_postbe as sp
                                where sp.staffer_id = '{$stafferOne['staffer_id']}' and sp.school_id <> '0' and sp.postbe_status = '1'
                                order by sp.postbe_ismianjob DESC limit 0,1 ");
                                $istaffer['school_id'] = $schoolOne['school_id']?$schoolOne['school_id']:0;
                            }
                            if($schoolOne['school_id'] ==''){
                                $schoolOne = $this->DataControl->selectOne("select s.school_id FROM gmc_staffer_postbe as sp,gmc_company_organizeschool as s
where s.organize_id = sp.organize_id and sp.staffer_id = '{$stafferOne['staffer_id']}' and sp.school_id = '0' and sp.postbe_status = '1' order by sp.postbe_ismianjob DESC limit 0,1 ");
                                $istaffer['school_id'] = $schoolOne['school_id'];
                            }
                        }
                        $istaffer['token'] = $this->getToken($stafferOne);
                        $this->DataControl->updateData("smc_staffer", "staffer_id = '{$stafferOne['staffer_id']}'", array("staffer_lasttime" => time(), "staffer_lastip" => real_ip()));
                        $istaffer['isTestTips'] = 0;
                        $InOurtUrl = "https://sclogin.kedingdang.com/choose?token={$istaffer['token']}&staffer_id={$istaffer['staffer_id']}&company_id={$istaffer['company_id']}&status={$status}&isAdmin={$isAdmin}&language=zh";
                        header("Location:{$InOurtUrl}");
                        exit;
                    }else{
                        ajax_return(array('error' => 1,'errortip' => "您已离职，账户权限已被关闭!"),$companyOne['company_language']);
                    }
                }else{
                    ajax_return(array('error' => 1,'errortip' => "职工账户信息不存在!"),$companyOne['company_language']);
                }
            }else{
                ajax_return(array('error' => 1,'errortip' => "企业账户不存在，请确认授权码是否正确!"),$companyOne['company_language']);
            }
        }
    }
}