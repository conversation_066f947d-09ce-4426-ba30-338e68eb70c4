<?php


namespace Work\Controller\Api;


class DataScreenController extends viewTpl
{
    public $u;
    public $t;
    public $c;
    public $visitType = "api";
    public $company_id = 8888;
    public $school_ids = array(647,660,661,662,670,672,673,686,692,693,694,1196,1244);
    public $coursetype_id = 65;
    public $organize_id = '2266';
    public $school_tagbak=array('<PERSON>','<PERSON>/<PERSON>');


    //预加载处理类
    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
        $this->api = $this->router->getApi();

//        $this->setPublic();
    }

    function setPublic()
    {
        $sql=" select school_id 
          from smc_school 
          where company_id='{$this->company_id}' 
          and school_istest=0 and school_isclose=0 
          and school_tagbak in('" . implode("','", $this->school_tagbak) . "')";
        $schoolArray = $this->DataControl->selectClear($sql);

        $this->school_ids=array_column($schoolArray,'school_id');//不知道为什么有问题
        var_dump($this->school_ids);die;
    }

//    function a122()
//    {
//        $request = Input('get.', '', 'trim,addslashes');
//        if (isset($request['organize_id']) && $request['organize_id'] != '') {
//            if ($request['organize_id'] == '3082') {
//                $request['company_id']='10001';
//                $request['coursetype_id']='142';
//            }else{
//                $request['coursetype_id'] = $this->coursetype_id;
//                $request['company_id'] = $this->company_id;
//            }
//        } else {
//            $request['organize_id'] = '2266';
//            $request['coursetype_id'] = $this->coursetype_id;
//            $request['company_id'] = $this->company_id;
//        }
//    }
    //区域数据概况
    function api1_1View()
    {
        $request = Input('get.', '', 'trim,addslashes');
        if (isset($request['organize_id']) && $request['organize_id'] != '') {
            if ($request['organize_id'] == '3082') {
                $request['company_id']='10001';
                $request['coursetype_id']='142';
            }else{
                $request['coursetype_id'] = $this->coursetype_id;
                $request['company_id'] = $this->company_id;
            }
        } else {
            $request['organize_id'] = '2266';
            $request['coursetype_id'] = $this->coursetype_id;
            $request['company_id'] = $this->company_id;
        }

        $Model = new \Model\Api\DataScreenModel($request);
        $dataList = $Model->api1_1($request);

        ajax_return($dataList);
    }

    //年龄分布
    function api1_2View()
    {
        $request = Input('get.', '', 'trim,addslashes');
        if (isset($request['organize_id']) && $request['organize_id'] != '') {
            if ($request['organize_id'] == '3082') {
                $request['company_id']='10001';
                $request['coursetype_id']='142';
            }else{
                $request['coursetype_id'] = $this->coursetype_id;
                $request['company_id'] = $this->company_id;
            }
        } else {
            $request['organize_id'] = '2266';
            $request['coursetype_id'] = $this->coursetype_id;
            $request['company_id'] = $this->company_id;
        }

        $Model = new \Model\Api\DataScreenModel($request);
        $dataList = $Model->api1_2($request);

        ajax_return($dataList);
    }

    //90天招生排行统计
    function api1_3View()
    {
        $request = Input('get.', '', 'trim,addslashes');
        if (isset($request['organize_id']) && $request['organize_id'] != '') {
            if ($request['organize_id'] == '3082') {
                $request['company_id']='10001';
                $request['coursetype_id']='142';
            }else{
                $request['coursetype_id'] = $this->coursetype_id;
                $request['company_id'] = $this->company_id;
            }
        } else {
            $request['organize_id'] = '2266';
            $request['coursetype_id'] = $this->coursetype_id;
            $request['company_id'] = $this->company_id;
        }

        $Model = new \Model\Api\DataScreenModel($request);
        $dataList = $Model->api1_3($request);

        ajax_return($dataList);
    }

    //招生增长率排行统计
    function api1_4View()
    {
        $request = Input('get.', '', 'trim,addslashes');
        if (isset($request['organize_id']) && $request['organize_id'] != '') {
            if ($request['organize_id'] == '3082') {
                $request['company_id']='10001';
                $request['coursetype_id']='142';
            }else{
                $request['coursetype_id'] = $this->coursetype_id;
                $request['company_id'] = $this->company_id;
            }
        } else {
            $request['organize_id'] = '2266';
            $request['coursetype_id'] = $this->coursetype_id;
            $request['company_id'] = $this->company_id;
        }

        $Model = new \Model\Api\DataScreenModel($request);
        $dataList = $Model->api1_4($request);

        ajax_return($dataList);
    }

    //招生增长率排行统计
    function api1_5View()
    {
        $request = Input('get.', '', 'trim,addslashes');
        if (isset($request['organize_id']) && $request['organize_id'] != '') {
            if ($request['organize_id'] == '3082') {
                $request['company_id']='10001';
                $request['coursetype_id']='142';
            }else{
                $request['coursetype_id'] = $this->coursetype_id;
                $request['company_id'] = $this->company_id;
            }
        } else {
            $request['organize_id'] = '2266';
            $request['coursetype_id'] = $this->coursetype_id;
            $request['company_id'] = $this->company_id;
        }

        $Model = new \Model\Api\DataScreenModel($request);
        $dataList = $Model->api1_5($request);

        ajax_return($dataList);
    }

    //招生增长率排行统计
    function api1_6View()
    {
        $request = Input('get.', '', 'trim,addslashes');
        if (isset($request['organize_id']) && $request['organize_id'] != '') {
            if ($request['organize_id'] == '3082') {
                $request['company_id']='10001';
                $request['coursetype_id']='142';
            }else{
                $request['coursetype_id'] = $this->coursetype_id;
                $request['company_id'] = $this->company_id;
            }
        } else {
            $request['organize_id'] = '2266';
            $request['coursetype_id'] = $this->coursetype_id;
            $request['company_id'] = $this->company_id;
        }

        $Model = new \Model\Api\DataScreenModel($request);
        $dataList = $Model->api1_6($request);

        ajax_return($dataList);
    }

    //课务实时数据统计
    function api2_1View()
    {
        $request = Input('get.', '', 'trim,addslashes');
        if (isset($request['organize_id']) && $request['organize_id'] != '') {
            if ($request['organize_id'] == '3082') {
                $request['company_id']='10001';
                $request['coursetype_id']='142';
            }else{
                $request['coursetype_id'] = $this->coursetype_id;
                $request['company_id'] = $this->company_id;
            }
        } else {
            $request['organize_id'] = '2266';
            $request['coursetype_id'] = $this->coursetype_id;
            $request['company_id'] = $this->company_id;
        }

        $Model = new \Model\Api\DataScreenModel($request);
        $dataList = $Model->api2_1($request);

        ajax_return($dataList);
    }

    //班种课时收入占比
    function api2_2View()
    {
        $request = Input('get.', '', 'trim,addslashes');
        if (isset($request['organize_id']) && $request['organize_id'] != '') {
            if ($request['organize_id'] == '3082') {
                $request['company_id']='10001';
                $request['coursetype_id']='142';
            }else{
                $request['coursetype_id'] = $this->coursetype_id;
                $request['company_id'] = $this->company_id;
            }
        } else {
            $request['organize_id'] = '2266';
            $request['coursetype_id'] = $this->coursetype_id;
            $request['company_id'] = $this->company_id;
        }

        $Model = new \Model\Api\DataScreenModel($request);
        $dataList = $Model->api2_2($request);

        ajax_return($dataList);
    }

    //下月预估续费率排行
    function api2_3View()
    {
        $request = Input('get.', '', 'trim,addslashes');
        if (isset($request['organize_id']) && $request['organize_id'] != '') {
            if ($request['organize_id'] == '3082') {
                $request['company_id']='10001';
                $request['coursetype_id']='142';
            }else{
                $request['coursetype_id'] = $this->coursetype_id;
                $request['company_id'] = $this->company_id;
            }
        } else {
            $request['organize_id'] = '2266';
            $request['coursetype_id'] = $this->coursetype_id;
            $request['company_id'] = $this->company_id;
        }

        $Model = new \Model\Api\DataScreenModel($request);
        $dataList = $Model->api2_3($request);

        ajax_return($dataList);
    }

    //本月外师课时排行
    function api2_4View()
    {
        $request = Input('get.', '', 'trim,addslashes');
        if (isset($request['organize_id']) && $request['organize_id'] != '') {
            if ($request['organize_id'] == '3082') {
                $request['company_id']='10001';
                $request['coursetype_id']='142';
            }else{
                $request['coursetype_id'] = $this->coursetype_id;
                $request['company_id'] = $this->company_id;
            }
        } else {
            $request['organize_id'] = '2266';
            $request['coursetype_id'] = $this->coursetype_id;
            $request['company_id'] = $this->company_id;
        }

        $Model = new \Model\Api\DataScreenModel($request);
        $dataList = $Model->api2_4($request);

        ajax_return($dataList);
    }

    //上月实际续费率排行
    function api2_5View()
    {
        $request = Input('get.', '', 'trim,addslashes');
        if (isset($request['organize_id']) && $request['organize_id'] != '') {
            if ($request['organize_id'] == '3082') {
                $request['company_id']='10001';
                $request['coursetype_id']='142';
            }else{
                $request['coursetype_id'] = $this->coursetype_id;
                $request['company_id'] = $this->company_id;
            }
        } else {
            $request['organize_id'] = '2266';
            $request['coursetype_id'] = $this->coursetype_id;
            $request['company_id'] = $this->company_id;
        }

        $Model = new \Model\Api\DataScreenModel($request);
        $dataList = $Model->api2_5($request);

        ajax_return($dataList);
    }

    //课程在读排行
    function api2_6View()
    {
        $request = Input('get.', '', 'trim,addslashes');
        if (isset($request['organize_id']) && $request['organize_id'] != '') {
            if ($request['organize_id'] == '3082') {
                $request['company_id']='10001';
                $request['coursetype_id']='142';
            }else{
                $request['coursetype_id'] = $this->coursetype_id;
                $request['company_id'] = $this->company_id;
            }
        } else {
            $request['organize_id'] = '2266';
            $request['coursetype_id'] = $this->coursetype_id;
            $request['company_id'] = $this->company_id;
        }

        $Model = new \Model\Api\DataScreenModel($request);
        $dataList = $Model->api2_6($request);

        ajax_return($dataList);
    }

    //本月财务数据概览
    function api3_1View()
    {
        $request = Input('get.', '', 'trim,addslashes');
        if (isset($request['organize_id']) && $request['organize_id'] != '') {
            if ($request['organize_id'] == '3082') {
                $request['company_id']='10001';
                $request['coursetype_id']='142';
            }else{
                $request['coursetype_id'] = $this->coursetype_id;
                $request['company_id'] = $this->company_id;
            }
        } else {
            $request['organize_id'] = '2266';
            $request['coursetype_id'] = $this->coursetype_id;
            $request['company_id'] = $this->company_id;
        }

        $Model = new \Model\Api\DataScreenModel($request);
        $dataList = $Model->api3_1($request);

        ajax_return($dataList);
    }

    //本月收入数据饼图
    function api3_2View()
    {
        $request = Input('get.', '', 'trim,addslashes');
        if (isset($request['organize_id']) && $request['organize_id'] != '') {
            if ($request['organize_id'] == '3082') {
                $request['company_id']='10001';
                $request['coursetype_id']='142';
            }else{
                $request['coursetype_id'] = $this->coursetype_id;
                $request['company_id'] = $this->company_id;
            }
        } else {
            $request['organize_id'] = '2266';
            $request['coursetype_id'] = $this->coursetype_id;
            $request['company_id'] = $this->company_id;
        }

        $Model = new \Model\Api\DataScreenModel($request);
        $dataList = $Model->api3_2($request);

        ajax_return($dataList);
    }

    //本月收入数据折线
    function api3_3View()
    {
        $request = Input('get.', '', 'trim,addslashes');
        if (isset($request['organize_id']) && $request['organize_id'] != '') {
            if ($request['organize_id'] == '3082') {
                $request['company_id']='10001';
                $request['coursetype_id']='142';
            }else{
                $request['coursetype_id'] = $this->coursetype_id;
                $request['company_id'] = $this->company_id;
            }
        } else {
            $request['organize_id'] = '2266';
            $request['coursetype_id'] = $this->coursetype_id;
            $request['company_id'] = $this->company_id;
        }

        $Model = new \Model\Api\DataScreenModel($request);
        $dataList = $Model->api3_3($request);

        ajax_return($dataList);
    }

    //校区90天收入排行
    function api3_4View()
    {
        $request = Input('get.', '', 'trim,addslashes');
        if (isset($request['organize_id']) && $request['organize_id'] != '') {
            if ($request['organize_id'] == '3082') {
                $request['company_id']='10001';
                $request['coursetype_id']='142';
            }else{
                $request['coursetype_id'] = $this->coursetype_id;
                $request['company_id'] = $this->company_id;
            }
        } else {
            $request['organize_id'] = '2266';
            $request['coursetype_id'] = $this->coursetype_id;
            $request['company_id'] = $this->company_id;
        }

        $Model = new \Model\Api\DataScreenModel($request);
        $dataList = $Model->api3_4($request);

        ajax_return($dataList);
    }

    //校区90天收费排行
    function api3_5View()
    {
        $request = Input('get.', '', 'trim,addslashes');
        if (isset($request['organize_id']) && $request['organize_id'] != '') {
            if ($request['organize_id'] == '3082') {
                $request['company_id']='10001';
                $request['coursetype_id']='142';
            }else{
                $request['coursetype_id'] = $this->coursetype_id;
                $request['company_id'] = $this->company_id;
            }
        } else {
            $request['organize_id'] = '2266';
            $request['coursetype_id'] = $this->coursetype_id;
            $request['company_id'] = $this->company_id;
        }

        $Model = new \Model\Api\DataScreenModel($request);
        $dataList = $Model->api3_5($request);

        ajax_return($dataList);
    }

    //校区90天退费排行
    function api3_6View()
    {
        $request = Input('get.', '', 'trim,addslashes');
        if (isset($request['organize_id']) && $request['organize_id'] != '') {
            if ($request['organize_id'] == '3082') {
                $request['company_id']='10001';
                $request['coursetype_id']='142';
            }else{
                $request['coursetype_id'] = $this->coursetype_id;
                $request['company_id'] = $this->company_id;
            }
        } else {
            $request['organize_id'] = '2266';
            $request['coursetype_id'] = $this->coursetype_id;
            $request['company_id'] = $this->company_id;
        }

        $Model = new \Model\Api\DataScreenModel($request);
        $dataList = $Model->api3_6($request);

        ajax_return($dataList);
    }

    //90天线上招生数据
    function api4_1View()
    {
        $request = Input('get.', '', 'trim,addslashes');
        if (isset($request['organize_id']) && $request['organize_id'] != '') {
            if ($request['organize_id'] == '3082') {
                $request['company_id']='10001';
                $request['coursetype_id']='142';
            }else{
                $request['coursetype_id'] = $this->coursetype_id;
                $request['company_id'] = $this->company_id;
            }
        } else {
            $request['organize_id'] = '2266';
            $request['coursetype_id'] = $this->coursetype_id;
            $request['company_id'] = $this->company_id;
        }

        $Model = new \Model\Api\DataScreenModel($request);
        $dataList = $Model->api4_1($request);

        ajax_return($dataList);
    }

    //90天线上渠道有效名单占比
    function api4_2View()
    {
        $request = Input('get.', '', 'trim,addslashes');
        if (isset($request['organize_id']) && $request['organize_id'] != '') {
            if ($request['organize_id'] == '3082') {
                $request['company_id']='10001';
                $request['coursetype_id']='142';
            }else{
                $request['coursetype_id'] = $this->coursetype_id;
                $request['company_id'] = $this->company_id;
            }
        } else {
            $request['organize_id'] = '2266';
            $request['coursetype_id'] = $this->coursetype_id;
            $request['company_id'] = $this->company_id;
        }

        $Model = new \Model\Api\DataScreenModel($request);
        $dataList = $Model->api4_2($request);

        ajax_return($dataList);
    }

    //90天线上渠道毛名单占比
    function api4_3View()
    {
        $request = Input('get.', '', 'trim,addslashes');
        if (isset($request['organize_id']) && $request['organize_id'] != '') {
            if ($request['organize_id'] == '3082') {
                $request['company_id']='10001';
                $request['coursetype_id']='142';
            }else{
                $request['coursetype_id'] = $this->coursetype_id;
                $request['company_id'] = $this->company_id;
            }
        } else {
            $request['organize_id'] = '2266';
            $request['coursetype_id'] = $this->coursetype_id;
            $request['company_id'] = $this->company_id;
        }

        $Model = new \Model\Api\DataScreenModel($request);
        $dataList = $Model->api4_3($request);

        ajax_return($dataList);
    }

    //90天线上渠道名单分配
    function api4_4View()
    {
        $request = Input('get.', '', 'trim,addslashes');
        if (isset($request['organize_id']) && $request['organize_id'] != '') {
            if ($request['organize_id'] == '3082') {
                $request['company_id']='10001';
                $request['coursetype_id']='142';
            }else{
                $request['coursetype_id'] = $this->coursetype_id;
                $request['company_id'] = $this->company_id;
            }
        } else {
            $request['organize_id'] = '2266';
            $request['coursetype_id'] = $this->coursetype_id;
            $request['company_id'] = $this->company_id;
        }

        $Model = new \Model\Api\DataScreenModel($request);
        $dataList = $Model->api4_4($request);

        ajax_return($dataList);
    }

    //90天线上渠道名单分配
    function api4_5View()
    {
        $request = Input('get.', '', 'trim,addslashes');
        if (isset($request['organize_id']) && $request['organize_id'] != '') {
            if ($request['organize_id'] == '3082') {
                $request['company_id']='10001';
                $request['coursetype_id']='142';
            }else{
                $request['coursetype_id'] = $this->coursetype_id;
                $request['company_id'] = $this->company_id;
            }
        } else {
            $request['organize_id'] = '2266';
            $request['coursetype_id'] = $this->coursetype_id;
            $request['company_id'] = $this->company_id;
        }

        $Model = new \Model\Api\DataScreenModel($request);
        $dataList = $Model->api4_5($request);

        ajax_return($dataList);
    }


    //90天线下渠道名单占比
    function api5_1View()
    {
        $request = Input('get.', '', 'trim,addslashes');
        if (isset($request['organize_id']) && $request['organize_id'] != '') {
            if ($request['organize_id'] == '3082') {
                $request['company_id']='10001';
                $request['coursetype_id']='142';
            }else{
                $request['coursetype_id'] = $this->coursetype_id;
                $request['company_id'] = $this->company_id;
            }
        } else {
            $request['organize_id'] = '2266';
            $request['coursetype_id'] = $this->coursetype_id;
            $request['company_id'] = $this->company_id;
        }

        $Model = new \Model\Api\DataScreenModel($request);
        $dataList = $Model->api5_1($request);

        ajax_return($dataList);
    }

    //90天线下渠道转化率
    function api5_2View()
    {
        $request = Input('get.', '', 'trim,addslashes');
        if (isset($request['organize_id']) && $request['organize_id'] != '') {
            if ($request['organize_id'] == '3082') {
                $request['company_id']='10001';
                $request['coursetype_id']='142';
            }else{
                $request['coursetype_id'] = $this->coursetype_id;
                $request['company_id'] = $this->company_id;
            }
        } else {
            $request['organize_id'] = '2266';
            $request['coursetype_id'] = $this->coursetype_id;
            $request['company_id'] = $this->company_id;
        }

        $Model = new \Model\Api\DataScreenModel($request);
        $dataList = $Model->api5_2($request);

        ajax_return($dataList);
    }

    //90天线下招生数据概览
    function api5_3View()
    {
        $request = Input('get.', '', 'trim,addslashes');
        if (isset($request['organize_id']) && $request['organize_id'] != '') {
            if ($request['organize_id'] == '3082') {
                $request['company_id']='10001';
                $request['coursetype_id']='142';
            }else{
                $request['coursetype_id'] = $this->coursetype_id;
                $request['company_id'] = $this->company_id;
            }
        } else {
            $request['organize_id'] = '2266';
            $request['coursetype_id'] = $this->coursetype_id;
            $request['company_id'] = $this->company_id;
        }

        $Model = new \Model\Api\DataScreenModel($request);
        $dataList = $Model->api5_3($request);

        ajax_return($dataList);
    }

    //90天转介绍渠道名单占比
    function api5_4View()
    {
        $request = Input('get.', '', 'trim,addslashes');
        if (isset($request['organize_id']) && $request['organize_id'] != '') {
            if ($request['organize_id'] == '3082') {
                $request['company_id']='10001';
                $request['coursetype_id']='142';
            }else{
                $request['coursetype_id'] = $this->coursetype_id;
                $request['company_id'] = $this->company_id;
            }
        } else {
            $request['organize_id'] = '2266';
            $request['coursetype_id'] = $this->coursetype_id;
            $request['company_id'] = $this->company_id;
        }

        $Model = new \Model\Api\DataScreenModel($request);
        $dataList = $Model->api5_4($request);

        ajax_return($dataList);
    }

    //90天校区转介绍报名排行（top5）
    function api5_5View()
    {
        $request = Input('get.', '', 'trim,addslashes');
        if (isset($request['organize_id']) && $request['organize_id'] != '') {
            if ($request['organize_id'] == '3082') {
                $request['company_id']='10001';
                $request['coursetype_id']='142';
            }else{
                $request['coursetype_id'] = $this->coursetype_id;
                $request['company_id'] = $this->company_id;
            }
        } else {
            $request['organize_id'] = '2266';
            $request['coursetype_id'] = $this->coursetype_id;
            $request['company_id'] = $this->company_id;
        }

        $Model = new \Model\Api\DataScreenModel($request);
        $dataList = $Model->api5_5($request);

        ajax_return($dataList);
    }

    //近一年线下渠道名单增长趋势
    function api5_6View()
    {
        $request = Input('get.', '', 'trim,addslashes');
        if (isset($request['organize_id']) && $request['organize_id'] != '') {
            if ($request['organize_id'] == '3082') {
                $request['company_id']='10001';
                $request['coursetype_id']='142';
            }else{
                $request['coursetype_id'] = $this->coursetype_id;
                $request['company_id'] = $this->company_id;
            }
        } else {
            $request['organize_id'] = '2266';
            $request['coursetype_id'] = $this->coursetype_id;
            $request['company_id'] = $this->company_id;
        }

        $Model = new \Model\Api\DataScreenModel($request);
        $dataList = $Model->api5_6($request);

        ajax_return($dataList);
    }

    //90天教师转介绍报名排行（top5）
    function api5_7View()
    {
        $request = Input('get.', '', 'trim,addslashes');
        if (isset($request['organize_id']) && $request['organize_id'] != '') {
            if ($request['organize_id'] == '3082') {
                $request['company_id']='10001';
                $request['coursetype_id']='142';
            }else{
                $request['coursetype_id'] = $this->coursetype_id;
                $request['company_id'] = $this->company_id;
            }
        } else {
            $request['organize_id'] = '2266';
            $request['coursetype_id'] = $this->coursetype_id;
            $request['company_id'] = $this->company_id;
        }

        $Model = new \Model\Api\DataScreenModel($request);
        $dataList = $Model->api5_7($request);

        ajax_return($dataList);
    }

    //90天教师招新业绩
    function api6_1View()
    {
        $request = Input('get.', '', 'trim,addslashes');
        if (isset($request['organize_id']) && $request['organize_id'] != '') {
            if ($request['organize_id'] == '3082') {
                $request['company_id']='10001';
                $request['coursetype_id']='142';
            }else{
                $request['coursetype_id'] = $this->coursetype_id;
                $request['company_id'] = $this->company_id;
            }
        } else {
            $request['organize_id'] = '2266';
            $request['coursetype_id'] = $this->coursetype_id;
            $request['company_id'] = $this->company_id;
        }

        $Model = new \Model\Api\DataScreenModel($request);
        $dataList = $Model->api6_1($request);

        ajax_return($dataList);
    }

    //90天校区邀约到访率（top5）
    function api6_2View()
    {
        $request = Input('get.', '', 'trim,addslashes');
        if (isset($request['organize_id']) && $request['organize_id'] != '') {
            if ($request['organize_id'] == '3082') {
                $request['company_id']='10001';
                $request['coursetype_id']='142';
            }else{
                $request['coursetype_id'] = $this->coursetype_id;
                $request['company_id'] = $this->company_id;
            }
        } else {
            $request['organize_id'] = '2266';
            $request['coursetype_id'] = $this->coursetype_id;
            $request['company_id'] = $this->company_id;
        }

        $Model = new \Model\Api\DataScreenModel($request);
        $dataList = $Model->api6_2($request);

        ajax_return($dataList);
    }

    //90天校区业绩排行统计（top5）
    function api6_3View()
    {
        $request = Input('get.', '', 'trim,addslashes');
        if (isset($request['organize_id']) && $request['organize_id'] != '') {
            if ($request['organize_id'] == '3082') {
                $request['company_id']='10001';
                $request['coursetype_id']='142';
            }else{
                $request['coursetype_id'] = $this->coursetype_id;
                $request['company_id'] = $this->company_id;
            }
        } else {
            $request['organize_id'] = '2266';
            $request['coursetype_id'] = $this->coursetype_id;
            $request['company_id'] = $this->company_id;
        }

        $Model = new \Model\Api\DataScreenModel($request);
        $dataList = $Model->api6_3($request);

        ajax_return($dataList);
    }

    //90天招生数据概览（top5）
    function api6_4View()
    {
        $request = Input('get.', '', 'trim,addslashes');
        if (isset($request['organize_id']) && $request['organize_id'] != '') {
            if ($request['organize_id'] == '3082') {
                $request['company_id']='10001';
                $request['coursetype_id']='142';
            }else{
                $request['coursetype_id'] = $this->coursetype_id;
                $request['company_id'] = $this->company_id;
            }
        } else {
            $request['organize_id'] = '2266';
            $request['coursetype_id'] = $this->coursetype_id;
            $request['company_id'] = $this->company_id;
        }

        $Model = new \Model\Api\DataScreenModel($request);
        $dataList = $Model->api6_4($request);

        ajax_return($dataList);
    }

    //近九十天内获得毛名单人数最多的前五个活动
    function api6_5View()
    {
        $request = Input('get.', '', 'trim,addslashes');
        if (isset($request['organize_id']) && $request['organize_id'] != '') {
            if ($request['organize_id'] == '3082') {
                $request['company_id']='10001';
                $request['coursetype_id']='142';
            }else{
                $request['coursetype_id'] = $this->coursetype_id;
                $request['company_id'] = $this->company_id;
            }
        } else {
            $request['organize_id'] = '2266';
            $request['coursetype_id'] = $this->coursetype_id;
            $request['company_id'] = $this->company_id;
        }

        $Model = new \Model\Api\DataScreenModel($request);
        $dataList = $Model->api6_5($request);

        ajax_return($dataList);
    }

    //近一年校区跟踪频次
    function api6_6View()
    {
        $request = Input('get.', '', 'trim,addslashes');
        if (isset($request['organize_id']) && $request['organize_id'] != '') {
            if ($request['organize_id'] == '3082') {
                $request['company_id']='10001';
                $request['coursetype_id']='142';
            }else{
                $request['coursetype_id'] = $this->coursetype_id;
                $request['company_id'] = $this->company_id;
            }
        } else {
            $request['organize_id'] = '2266';
            $request['coursetype_id'] = $this->coursetype_id;
            $request['company_id'] = $this->company_id;
        }

        $Model = new \Model\Api\DataScreenModel($request);
        $dataList = $Model->api6_6($request);

        ajax_return($dataList);
    }

}