<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/5/27
 * Time: 11:56
 */

namespace Work\Controller\Api;


class HrmWarnTempController extends viewTpl
{
    public $u;
    public $t;
    public $c;
    public $visitType = "api";


    //预加载处理类
    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
        $this->api = $this->router->getApi();
    }

    function getWaitAttendanceNumApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $Model = new \Model\Api\HrmWarnTempModel();
        $res = $Model->getWaitAttendanceNum($request);
        if ($res || $res==0) {
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $res);
        } else {
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => array());
        }
        ajax_return($res, $request['language_type']);
    }

    function getArrearsNumApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $Model = new \Model\Api\HrmWarnTempModel();
        $res = $Model->getArrearsNum($request);
        if ($res) {
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $res);
        } else {
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => array());
        }
        ajax_return($res, $request['language_type']);
    }

    function getWillArrearsNumApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $Model = new \Model\Api\HrmWarnTempModel();
        $res = $Model->getWillArrearsNum($request);
        if ($res) {
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $res);
        } else {
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => array());
        }
        ajax_return($res, $request['language_type']);
    }

    function getCouponWillApprovedNumApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $Model = new \Model\Api\HrmWarnTempModel();
        $res = $Model->getCouponWillApprovedNum($request);
        if ($res) {
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $res);
        } else {
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => array());
        }
        ajax_return($res, $request['language_type']);
    }

    function getRefundOrderWillApprovedNumApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $Model = new \Model\Api\HrmWarnTempModel();
        $res = $Model->getRefundOrderWillApprovedNum($request);
        if ($res) {
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $res);
        } else {
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => array());
        }
        ajax_return($res, $request['language_type']);
    }

    function getStayInClassNumApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $Model = new \Model\Api\HrmWarnTempModel();
        $res = $Model->getStayInClassNum($request);
        if ($res) {
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $res);
        } else {
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => array());
        }
        ajax_return($res, $request['language_type']);
    }

    function getBreakClassWillApprovedNumApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $Model = new \Model\Api\HrmWarnTempModel();
        $res = $Model->getBreakClassWillApprovedNum($request);
        if ($res) {
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $res);
        } else {
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => array());
        }
        ajax_return($res, $request['language_type']);
    }

    function getStudentCourseWarningNumApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $Model = new \Model\Api\HrmWarnTempModel();
        $res = $Model->getStudentCourseWarningNum($request);
        if ($res) {
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $res);
        } else {
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => array());
        }
        ajax_return($res, $request['language_type']);
    }

    function getClassUpWarningNumApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $Model = new \Model\Api\HrmWarnTempModel();
        $res = $Model->getClassUpWarningNum($request);
        if ($res) {
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $res);
        } else {
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => array());
        }
        ajax_return($res, $request['language_type']);
    }

    function getStuConsumeNumApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $Model = new \Model\Api\HrmWarnTempModel();
        $res = $Model->getStuConsumeNum($request);
        if ($res) {
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $res);
        } else {
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => array());
        }
        ajax_return($res, $request['language_type']);
    }

    function getLostWarningNumApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $Model = new \Model\Api\HrmWarnTempModel();
        $res = $Model->getLostWarningNum($request);
        if ($res) {
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $res);
        } else {
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => array());
        }
        ajax_return($res, $request['language_type']);
    }

    function getlostChangeNumApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $Model = new \Model\Api\HrmWarnTempModel();
        $res = $Model->getlostChangeNum($request);
        if ($res) {
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $res);
        } else {
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => array());
        }
        ajax_return($res, $request['language_type']);
    }




}