<?php

namespace Work\Controller\Api;


class ActivityYearController extends viewTpl
{
    public $u;
    public $t;
    public $c;
    public $visitType = "api";

    //预加载处理类
    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
        $this->api = $this->router->getApi();
    }

    static $couponsArray = array(
        '0' => array('title' => '助学红包166元', 'couponscode_price' => '166', 'sort' => 1, 'img' => 'https://kedingdang.oss-cn-shanghai.aliyuncs.com/KedingdangOssFiles/miniapp/luckdraw/166.png'),
        '1' => array('title' => '助学红包388元', 'couponscode_price' => '388', 'sort' => 3, 'img' => 'https://kedingdang.oss-cn-shanghai.aliyuncs.com/KedingdangOssFiles/miniapp/luckdraw/388.png'),
        '2' => array('title' => '助学红包666元', 'couponscode_price' => '666', 'sort' => 5, 'img' => 'https://kedingdang.oss-cn-shanghai.aliyuncs.com/KedingdangOssFiles/miniapp/luckdraw/666.png'),
        '3' => array('title' => '助学红包888元', 'couponscode_price' => '888', 'sort' => 7, 'img' => 'https://kedingdang.oss-cn-shanghai.aliyuncs.com/KedingdangOssFiles/miniapp/luckdraw/888.png'),
        '4' => array('title' => '谢谢参与', 'couponscode_price' => '0', 'sort' => 2, 'img' => 'https://kedingdang.oss-cn-shanghai.aliyuncs.com/KedingdangOssFiles/miniapp/luckdraw/xiexie-icon.png'),
        '5' => array('title' => '谢谢参与', 'couponscode_price' => '0', 'sort' => 4, 'img' => 'https://kedingdang.oss-cn-shanghai.aliyuncs.com/KedingdangOssFiles/miniapp/luckdraw/xiexie-icon.png'),
        '6' => array('title' => '谢谢参与', 'couponscode_price' => '0', 'sort' => 6, 'img' => 'https://kedingdang.oss-cn-shanghai.aliyuncs.com/KedingdangOssFiles/miniapp/luckdraw/xiexie-icon.png'),
        '7' => array('title' => '谢谢参与', 'couponscode_price' => '0', 'sort' => 8, 'img' => 'https://kedingdang.oss-cn-shanghai.aliyuncs.com/KedingdangOssFiles/miniapp/luckdraw/xiexie-icon.png'),
    );

    /**
     *  获取活动状态与详情
     * author: ling
     * 对应接口文档 0001
     * Date 2021/1/5 0005
     */
    function getActivityStatusView()
    {
        $array_sort = array_column(static::$couponsArray, "sort");
        array_multisort($array_sort, SORT_ASC, static::$couponsArray);
        $result = array();
        $result['list'] = static::$couponsArray;
        $res = array('error' => 0, 'errortip' => '获取活动详情', 'result' => $result);
        ajax_return($res);
    }


    /**
     * 红包抽奖活动 2021年1月份
     * 此活动必中
     * author: ling
     * 对应接口文档 0001
     * Date 2021/1/5 0005
     */

    function getActivityCouponsView()
    {
        $reqeust = Input('post.', "", "trim,addslashes");
        $pattern = "/^1[3456789]\d{9}$/";
        if (1) {
            $res = array('error' => 1, 'errortip' => '该活动已结束，谢谢您的关注！', 'result' => array());
            ajax_return($res);
        }
        die;
        if (!preg_match($pattern, $reqeust['couponscode_mobile'])) {
            $res = array('error' => 1, 'errortip' => '手机号不正确', 'result' => array());
            ajax_return($res);
        }
        if (isset($reqeust['couponscode_lotterypid']) && $reqeust['couponscode_lotterypid'] == '') {
            $res = array('error' => 1, 'errortip' => '请输入抽奖码', 'result' => array());
            ajax_return($res);
        }
        if (!$this->DataControl->getFieldOne("activity_couponscode", "couponscode_id", "couponscode_lotterypid='{$reqeust['couponscode_lotterypid']}'")) {
            $res = array('error' => 1, 'errortip' => '请输入正确的抽奖码', 'result' => array());
            ajax_return($res);
        }
        if ($this->DataControl->getFieldOne("activity_couponscode", "couponscode_id", "couponscode_lotterypid='{$reqeust['couponscode_lotterypid']}' and couponscode_mobile <> ''")) {
            $res = array('error' => -2, 'errortip' => '该抽奖码已使用', 'result' => array());
            ajax_return($res);
        }
//        if (!$this->DataControl->getFieldOne("activity_couponscode", "couponscode_id", "couponscode_lotterypid='{$reqeust['couponscode_lotterypid']}' and company_id  = '{$reqeust['company_id']}'")) {
//            $res = array('error' => 1, 'errortip' => '该抽奖码不属于该集团', 'result' => array());
//            ajax_return($res);
//        }
        // 0~3 必中 4~7必定不中
        $couponscode_price = static::$couponsArray[rand(0, 3)]['couponscode_price'];
        if ($couponscode_price > 0) {
            $data = array();
            $data['couponscode_mobile'] = $reqeust['couponscode_mobile'];
            $data['couponscode_price'] = $couponscode_price;
            $data['couponscode_lotterytime'] = time();
            do {
                $data['couponscode_pid'] = $this->createReceiptPid('XN');
            } while ($this->DataControl->getFieldOne("activity_couponscode", "couponscode_id", "couponscode_pid='{$data['couponscode_pid']}'"));

            if ($this->DataControl->updateData("activity_couponscode", "couponscode_lotterypid='{$reqeust['couponscode_lotterypid']}'", $data)) {
                $result = array();
                $result['data'] = $data;
                $res = array('error' => 0, 'errortip' => '恭喜您中奖啦', 'result' => $result);
                ajax_return($res);
            } else {
                $res = array('error' => -1, 'errortip' => '谢谢参与', 'result' => array());
                ajax_return($res);
            }
        } else {
            $res = array('error' => -1, 'errortip' => '谢谢参与', 'result' => array());
            ajax_return($res);
        }
    }


    /**
     * 生成抽奖码
     * author: ling
     * 对应接口文档 0001
     * Date 2021/1/5 0005
     */
    function addActivityLotteryPidView()
    {
        $reqeust = Input('get.', "", "trim,addslashes");
        if (isset($reqeust['company_id']) && $reqeust['company_id'] == '') {
            $res = array('error' => 1, 'errortip' => '请选择所属集团', 'result' => array());
            ajax_return($res);
        }
        if (isset($reqeust['pid_num']) && $reqeust['pid_num'] !== '') {
            $num = 0;
            for ($i = 0; $i < $reqeust['pid_num']; $i++) {
                $data = array();
                $data['company_id'] = $reqeust['company_id'];
                do {
                    $rand = rand(100000, 999999);
                } while ($this->DataControl->getFieldOne("activity_couponscode", "couponscode_id", "couponscode_lotterypid='{$rand}'"));
                $data['couponscode_lotterypid'] = $rand;
                $data['couponscode_createtime'] = time();
                $this->DataControl->insertData('activity_couponscode', $data);
                $num++;
            }
            $res = array('error' => 0, 'errortip' => '共生成' . $num . '张抽奖券券', 'result' => array());
            ajax_return($res);
        } else {
            $res = array('error' => 0, 'errortip' => '请选需要生成的张数', 'result' => array());
            ajax_return($res);
        }
    }

    /**\
     * 导出中奖纪录
     * author: ling
     * 对应接口文档 0001
     * Date 2021/1/9 0009
     */
    function importExcelView()
    {
        $reqeust = Input('get.', "", "trim,addslashes");
        $datawhere = '1';
        if (isset($reqeust['is_lottery']) && $reqeust['is_lottery'] == 1) {
            $datawhere .= " and couponscode_pid<>'' ";
        } elseif (isset($reqeust['is_lottery']) && $reqeust['is_lottery'] == 2) {
            $datawhere .= " and couponscode_pid ='' ";
        }

        $dataLsit = $this->DataControl->selectClear("select * from activity_couponscode where {$datawhere} ");
        if ($dataLsit) {
            $outexcel = array();
            foreach ($dataLsit as $dataVar) {
                $dataarray['couponscode_id'] = $dataVar['couponscode_id'];
                $dataarray['couponscode_lotterypid'] = $dataVar['couponscode_lotterypid'];
                $dataarray['couponscode_mobile'] = $dataVar['couponscode_mobile'];
                $dataarray['couponscode_pid'] = $dataVar['couponscode_pid'];
                $dataarray['couponscode_price'] = $dataVar['couponscode_price'];
                $dataarray['couponscode_lotterytime'] = $dataVar['couponscode_lotterytime'] ? date("Y-m-d H:i", $dataVar['couponscode_lotterytime']) : '';
                $outexcel[] = $dataarray;
            }
        }
        $excelheader = array("序号", "抽奖码", "手机号", "中奖码", "奖券价值", "抽奖时间");
        $excelfields = array("couponscode_id", "couponscode_lotterypid", "couponscode_mobile", "couponscode_pid", "couponscode_price", "couponscode_lotterytime");
        $excelname = "红包抽奖纪录.xlsx";
        if (!is_array($outexcel)) {
            jsbakerror_spl("没有数据！");
            exit;
        }
        query_to_excel($excelheader, $outexcel, $excelfields, $excelname);
        ajax_return(array("error" => 0, "errortip" => "下载完毕!", "bakfuntion" => "okmotify"));
    }

    /**
     *
     * author: ling
     * 对应接口文档 0001
     * Date 2021/1/5 0005
     * @param $initial
     * @return string
     */
    function createReceiptPid($initial)
    {
        $rand = $initial . chr(rand(65, 90)) . chr(rand(65, 90)) . rand(1000, 9999);
        return $rand;
    }

}