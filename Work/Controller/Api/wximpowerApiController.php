<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/7/4
 * Time: 13:07
 */

namespace Work\Controller\Api;


class wximpowerApiController extends viewTpl
{
    public $data;
    public $wxuser;
    public $appId = 'wx5b3041a74ac4e4cc';
    public $appSecret = 'e01b7d70f1dec6abb3766d6274b39d01';

    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
    }

    //校务系统叮铛助教教师授权登录
    function sendbakApi()
    {
        require(ROOT_PATH."Core/Tools/Wxcode/Wxcode.php");
        $date = array();
        $date['token_type'] = 5;
        $date['token_string'] = json_encode($GLOBALS['HTTP_RAW_POST_DATA']);
        $date['token_failuretime'] = time();
        $this->DataControl->insertData("imc_weixin_token", $date);
        exit('success');
    }
}