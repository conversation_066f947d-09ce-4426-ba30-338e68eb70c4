<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2021/3/30
 * Time: 23:56
 */

namespace Work\Controller\Api;


class ClassEndcalcController extends viewTpl
{
    public $u;
    public $t;
    public $c;
    public $visitType = "api";


    //预加载处理类
    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
        $this->api = $this->router->getApi();
    }

    //第一步处理结算班级
    function startSchoolAllView()
    {
        $sql = "select school_id from smc_school where school_isclose=0 and school_istest=0 and company_id='8888'";
        $schoolList = $this->DataControl->selectClear($sql);

        $Model = new \Model\Api\ClassendcalcModel();
        $paramArray = array();
        $paramArray['company_id'] = '8888';
        $Model->createSettleOrder($paramArray);

        if ($schoolList) {
            $res = array('error' => '0', 'errortip' => '校区待结算班级生成成功', 'result' => true);
        } else {
            $res = array('error' => '1', 'errortip' => '无校区待结算班级数据', 'result' => false);
        }
        ajax_return($res);
    }

    //第二步处理结算班级学生留班
    function classSettleAllView()
    {
        $Model = new  \Model\Api\ClassendcalcModel();
        $paramArray = array();
        $paramArray['company_id'] = '8888';
        $paramArray['coursetype_id'] = '65';
        $bool = $Model->classSettle($paramArray);
        if ($bool) {
            echo $Model->oktip . "<br />";
        } else {
            echo $Model->errortip . "<br />";
        }

        echo '<script language="javascript" type="text/javascript">
        var i = 1;
        var intervalid;
        intervalid = setInterval("fun()", 200);
        function fun() {
            if (i == 0) {
                window.location.href = "/ClassEndcalc/classSettleAll";
                clearInterval(intervalid);
            }
            document.getElementById("mes").innerHTML = i;
            i--;
        }
        </script>
        <div id="error">
            <p>将在 <span id="mes">2</span> 秒钟后返回首页！</p>
        </div> ';
    }

    //第二步处理结算班级学生留班
    function breakoffRefreshView()
    {
        $Model = new  \Model\Api\ClassendcalcModel();
        $paramArray = array();
        $paramArray['company_id'] = '8888';
        $paramArray['coursetype_id'] = '65';
        $bool1 = $Model->breakoffRefresh($paramArray);
        $bool2 = $Model->editOriClassId();

        if ($bool1 && $bool2) {
            echo $Model->oktip . "拆并班处理完成<br />";
        } else {
            echo $Model->errortip . "拆并班处理失败<br />";
        }
    }

    //第三步取已考勤班级且来源班级未结算处理
    function classNewSettleAllView()
    {
        $Model = new  \Model\Api\ClassendcalcModel();
        $paramArray = array();

        $paramArray = Input('get.', '', 'trim,addslashes');
        $paramArray['company_id'] = '8888';
        $bool = $Model->classNewSettle($paramArray);
        if ($bool) {
            echo $Model->oktip . "<br />";
        } else {
            echo $Model->errortip . "<br />";
        }

        echo '<script language="javascript" type="text/javascript">
        var i = 1;
        var intervalid;
        intervalid = setInterval("fun()", 200);
        function fun() {
            if (i == 0) {
                window.location.href = "/ClassEndcalc/classNewSettleAll";
                clearInterval(intervalid);
            }
            document.getElementById("mes").innerHTML = i;
            i--;
        }
        </script>
        <div id="error">
            <p>将在 <span id="mes">2</span> 秒钟后返回首页！</p>
        </div> ';
    }

    //三期联缴特殊处理
    function classTempSettleAllView()
    {
        $Model = new  \Model\Api\ClassendcalcModel();
        $paramArray = array();

        $paramArray = Input('get.', '', 'trim,addslashes');
        $paramArray['company_id'] = '8888';
        $bool = $Model->classTempSettle($paramArray);
        if ($bool) {
            echo $Model->oktip . "<br />";
        } else {
            echo $Model->errortip . "<br />";
        }

        echo '<script language="javascript" type="text/javascript">
        var i = 1;
        var intervalid;
        intervalid = setInterval("fun()", 200);
        function fun() {
            if (i == 0) {
                window.location.href = "/ClassEndcalc/classTempSettleAll";
                clearInterval(intervalid);
            }
            document.getElementById("mes").innerHTML = i;
            i--;
        }
        </script>
        <div id="error">
            <p>将在 <span id="mes">2</span> 秒钟后返回首页！</p>
        </div> ';
    }

    function calcSingleClassView()
    {
        $Model = new  \Model\Api\ClassendcalcModel();
        $paramArray = Input('get.', '', 'trim,addslashes');
        $paramArray['company_id'] = '8888';

        if (!isset($paramArray['class_branch']) || $paramArray['class_branch'] == '') {
            ajax_return(array('error' => '1', 'errortip' => '传班级编号', 'result' => false));
        }
        $sql = "select a.endcalc_id,endcalc_issettle from smc_class_endcalc a,smc_class b 
        where a.class_id=b.class_id and b.class_branch='{$paramArray['class_branch']}'";

        $calcOne = $this->DataControl->selectOne($sql);
        if (!$calcOne) {
            ajax_return(array('error' => '1', 'errortip' => '未查询到结算数据，确认班级编号和结班日期', 'result' => false));
        }

        if ($calcOne['endcalc_issettle'] >= 1) {
            ajax_return(array('error' => '1', 'errortip' => '班级留班率已固定，单独修改错误学生', 'result' => false));
        }
        $paramArray['endcalc_id'] = $calcOne['endcalc_id'];
        $bool = $Model->classNewSettle($paramArray);
        if ($bool) {
            ajax_return(array('error' => '0', 'errortip' => $Model->oktip, 'result' => $paramArray));
        } else {
            ajax_return(array('error' => '1', 'errortip' => $Model->errortip, 'result' => false));
        }
    }

    function changeSingleStudentView()
    {
        $paramArray = Input('get.', '', 'trim,addslashes');

        if (!isset($paramArray['class_branch']) || $paramArray['class_branch'] == '') {
            ajax_return(array('error' => '1', 'errortip' => '传班级编号', 'result' => false));
        }
        if (!isset($paramArray['student_branch']) || $paramArray['student_branch'] == '') {
            ajax_return(array('error' => '1', 'errortip' => '传学生编号', 'result' => false));
        }
        $sql = "select a.study_id,a.study_upgraderate,d.course_classtimerates
            from smc_class_endcalc_study a,smc_class b,smc_student c,smc_course d
            where a.class_id=b.class_id and a.student_id=c.student_id and b.course_id=d.course_id
            and b.class_branch='{$paramArray['class_branch']}'
            and c.student_branch='{$paramArray['student_branch']}'";

        $calcOne = $this->DataControl->selectOne($sql);
        if (!$calcOne) {
            ajax_return(array('error' => '1', 'errortip' => '未查询到学生结算数据，确认编号是否正确', 'result' => false));
        }

//        if ($calcOne['study_upgraderate'] > '100') {
//            ajax_return(array('error' => '1', 'errortip' => '该生已留班，无需修改', 'result' => false));
//        }

        $data = array();
        if ($paramArray['study_nexttimes'] && $paramArray['study_nexttimes'] > 0) {
            $data['study_nexttimes'] = $paramArray['study_nexttimes'];
            $data['study_upgraderate'] = $paramArray['study_nexttimes'] * $calcOne['course_classtimerates'];
        }
        if ($paramArray['study_nextprice'] && $paramArray['study_nextprice'] > 0) {
            $data['study_nextprice'] = $paramArray['study_nextprice'];
        }
        if ($paramArray['study_upgraderate'] && $paramArray['study_upgraderate'] > 0) {
            $data['study_upgraderate'] = $paramArray['study_upgraderate'];
        }
        if ($paramArray['study_upgradeprice'] && $paramArray['study_upgradeprice'] >= 0) {
            $data['study_upgradeprice'] = $paramArray['study_upgradeprice'];
        }
        if ($paramArray['study_outtype'] && $paramArray['study_outtype'] == '-1') {
            $data['study_outtype'] = -1;
        }
        $data['study_updatetime'] = time();

        $bool = $this->DataControl->updateData("smc_class_endcalc_study", "study_id='{$calcOne['study_id']}'", $data);
        if ($bool) {
            ajax_return(array('error' => '0', 'errortip' => "修改成功", 'result' => $paramArray));
        } else {
            ajax_return(array('error' => '1', 'errortip' => "修改失败", 'result' => false));
        }
    }

    function shieldSingleClassView()
    {
        $paramArray = Input('get.', '', 'trim,addslashes');

        if (!isset($paramArray['class_branch']) || $paramArray['class_branch'] == '') {
            ajax_return(array('error' => '1', 'errortip' => '传班级编号', 'result' => false));
        }
        $sql = "select class_id,class_isnotrenew
            from smc_class 
            where class_branch='{$paramArray['class_branch']}'";

        $classOne = $this->DataControl->selectOne($sql);
        if (!$classOne) {
            ajax_return(array('error' => '1', 'errortip' => '未查询到班级数据，确认编号是否正确', 'result' => false));
        }

        if ($classOne['class_isnotrenew'] == '1') {
            ajax_return(array('error' => '1', 'errortip' => '班级已屏蔽，不用重复提交', 'result' => false));
        }

        $data = array();
        $data['class_isnotrenew'] = 1;
        $data['class_updatatime'] = time();

        $bool = $this->DataControl->updateData("smc_class", "class_id='{$classOne['class_id']}'", $data);
        if ($bool) {
            ajax_return(array('error' => '0', 'errortip' => "修改成功", 'result' => $paramArray));
        } else {
            ajax_return(array('error' => '1', 'errortip' => "修改失败", 'result' => false));
        }
    }

    function lockSingleClassView()
    {
        $paramArray = Input('get.', '', 'trim,addslashes');

        if (!isset($paramArray['class_branch']) || $paramArray['class_branch'] == '') {
            ajax_return(array('error' => '1', 'errortip' => '传班级编号', 'result' => false));
        }
        $sql = "select b.endcalc_id,b.endcalc_issettle
            from smc_class a,smc_class_endcalc b
            where a.class_id=b.class_id
            and a.class_branch='{$paramArray['class_branch']}'";

        $classOne = $this->DataControl->selectOne($sql);
        if (!$classOne) {
            ajax_return(array('error' => '1', 'errortip' => '未查询到班级数据，确认编号是否正确', 'result' => false));
        }

        if ($classOne['endcalc_issettle'] >= 1) {
            ajax_return(array('error' => '1', 'errortip' => '班级留班已固定，不用重复固定', 'result' => false));
        }

        $data = array();
        $data['endcalc_issettle'] = 1;
        $data['endcalc_updatatime'] = time();

        $bool = $this->DataControl->updateData("smc_class_endcalc", "endcalc_id='{$classOne['endcalc_id']}'", $data);
        if ($bool) {
            ajax_return(array('error' => '0', 'errortip' => "固定留班率成功", 'result' => $paramArray));
        } else {
            ajax_return(array('error' => '1', 'errortip' => "固定留班率失败", 'result' => false));
        }
    }

    function changeClassTeacherView()
    {
        $paramArray = Input('get.', '', 'trim,addslashes');

        if (!isset($paramArray['class_branch']) || $paramArray['class_branch'] == '') {
            ajax_return(array('error' => '1', 'errortip' => '传班级编号', 'result' => false));
        }

        if (!isset($paramArray['staffer_branch'])) {
            ajax_return(array('error' => '1', 'errortip' => '传教师编号', 'result' => false));
        }

        $sql = "select a.class_id,b.endcalc_id,b.endcalc_issettle
            from smc_class a,smc_class_endcalc b
            where a.class_id=b.class_id
            and a.class_branch='{$paramArray['class_branch']}'";

        $classOne = $this->DataControl->selectOne($sql);
        if (!$classOne) {
            ajax_return(array('error' => '1', 'errortip' => '未查询到班级数据，确认编号是否正确', 'result' => false));
        }

        if ($classOne['endcalc_issettle'] < 1) {
            ajax_return(array('error' => '1', 'errortip' => '班级留班未固定，待固定后重新设置', 'result' => false));
        }

        $stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id", "staffer_branch = '{$paramArray['staffer_branch']}'");

        if ($paramArray['staffer_branch'] !== '') {
            $sql = "select ht.teaching_id
                          from smc_class_hour_teaching as ht,smc_code_teachtype as ct,smc_class_hour as ch
                          where ht.teachtype_code=ct.teachtype_code and ht.hour_id=ch.hour_id
                          and ct.teachtype_code='05CM'
                          and ht.class_id='{$classOne['class_id']}'
                          and ht.staffer_id='{$stafferOne['staffer_id']}'
                          and ht.teaching_type=0
                          and ht.teaching_isdel=0
                          and ch.hour_ischecking>=0";

            $hourList = $this->DataControl->selectClear($sql);
            $times = count($hourList);
            $staffer = $stafferOne['staffer_id'];
        } else {
            $times = 0;
            $staffer = null;
        }

        $data = array();
        $data['endcalc_staffer_id'] = $staffer;
        $data['endcalc_staffer_times'] = $times;
        $data['endcalc_updatatime'] = time();

        $bool = $this->DataControl->updateData("smc_class_endcalc", "endcalc_id='{$classOne['endcalc_id']}'", $data);
        if ($bool) {
            ajax_return(array('error' => '0', 'errortip' => "变更留班老师成功", 'result' => $paramArray));
        } else {
            ajax_return(array('error' => '1', 'errortip' => "变更留班老师失败", 'result' => false));
        }
    }

    function studentSettleView()
    {
        $sql = "select school_id from smc_school where school_isclose=0 and school_istest=0 and company_id='8888'";
        $schoolList = $this->DataControl->selectClear($sql);
        $Model = new  \Model\Api\ClassendcalcModel();
        $paramArray = array();
        $paramArray['company_id'] = '8888';
        foreach ($schoolList as $schoolOne) {
            $paramArray['school_id'] = $schoolOne['school_id'];
            $bool = $Model->studentSettle($paramArray);
        }
        if ($bool) {
            echo $Model->oktip . "<br />";
        } else {
            echo $Model->errortip . "<br />";
        }
    }

    function testView()
    {
        $a = array();
//        $a =array(49016,50732,52511,49583,52534,49710,51191,51385,51143);

        //        $b = date('Y-m-d', strtotime("+3 days $a"));
//        var_dump($a[5]);

        foreach ($a as $var) {
            $old_calc = $this->DataControl->getFieldOne("smc_class_endcalc", "endcalc_id", "class_id = '{$var}'");
            if ($old_calc) {
                $endcalc = array();
                $sql = "select ht.staffer_id
                          from smc_class_hour_teaching as ht,smc_code_teachtype as ct,smc_class_hour as ch
                          where ht.teachtype_code=ct.teachtype_code and ht.hour_id=ch.hour_id
                          and ct.teachtype_code='05CM'
                          and ct.company_id='8888'
                          and ht.class_id='{$var}'
                          and ht.teaching_type=0
                          and ht.teaching_isdel=0
                          and ch.hour_ischecking>=0
                          and exists(select 1 from smc_class_teach as ct where ct.class_id=ht.class_id and ct.staffer_id=ht.staffer_id)
                          order by ch.hour_day desc,ch.hour_starttime desc,ch.hour_id desc";

                $hourList = $this->DataControl->selectClear($sql);
                if ($hourList) {
                    $endcalc['endcalc_staffer_id'] = $hourList[0]['staffer_id'];

                    $times = 0;
                    foreach ($hourList as &$hourOne) {
                        if ($hourOne['staffer_id'] == $endcalc['endcalc_staffer_id']) {
                            $times++;
                        }
                    }
                    $endcalc['endcalc_staffer_times'] = $times;
                }

                $sql = "select ht.hour_id
                          from smc_class_hour_teaching as ht,smc_code_teachtype as ct,smc_class_hour as ch
                          where ht.teachtype_code=ct.teachtype_code and ht.hour_id=ch.hour_id and ct.teachtype_code='05CM' 
                          and ct.company_id='8888' and ht.class_id='{$var}' 
                          and ht.teaching_type=0 and ht.teaching_isdel=0 and ch.hour_ischecking>=0
                          group by ht.hour_id";
                $hourList = $this->DataControl->selectClear($sql);

                $endcalc['endcalc_chn_times'] = $hourList ? count($hourList) : 0;

                $endcalc['endcalc_updatatime'] = '1653975941';
                $this->DataControl->updateData('smc_class_endcalc', "endcalc_id = '{$old_calc['endcalc_id']}'", $endcalc);
//                break;
            }
        }
    }

    function createRegisterApi()
    {
        $where = '';
        if (1 == 1) {
//            $where = " and a.trading_pid in('JYS3WI22091812131299971') ";
//            $where = " and d.student_branch in('2017042700008','2017042700008')";
//            $where = " and a.student_id='78818'";
        }

        $sql = "SELECT
        a.company_id,
        a.school_id,
        a.student_id,
        d.from_client_id,
        a.trading_pid,
        a.order_status,
        a.order_createtime,
        e.coursetype_id,
        e.coursetype_cnname,
        e.coursetype_branch,
        b.coursecat_id,
        b.coursecat_cnname,
        b.coursecat_branch,
        (select course_id from smc_payfee_order_course where order_pid=a.order_pid order by course_id limit 0,1) as course_id,
        m.pay_id,
        -- m.pay_price,
        sum(f.ordercourse_totalprice)/count(distinct m.pay_id) as pay_price,
        m.pay_successtime,
        ifnull((select marketer_id from crm_client_principal where client_id=d.from_client_id and school_id=a.school_id and principal_leave=0 and principal_ismajor=1
                order by principal_id desc limit 0,1),0)as xz_marketer_id,
        ifnull((select marketer_id from crm_student_principal where student_id=d.student_id and school_id=a.school_id and principal_leave=0 and principal_ismajor=1
                order by principal_id desc limit 0,1),0) as kz_marketer_id,
        e.coursetype_newchecked,
        (select count(1) from smc_student_registerinfo where company_id=a.company_id -- and school_id=a.school_id
                and student_id=a.student_id and coursetype_id=a.coursetype_id and info_status=1) as regi_coursetype,
        0 as info_status,
        UNIX_TIMESTAMP(now()) as info_time
        FROM smc_payfee_order_pay m
        LEFT JOIN smc_payfee_order a ON a.order_pid=m.order_pid
        LEFT JOIN smc_student d ON d.student_id=a.student_id
        LEFT JOIN smc_payfee_order_course f ON f.order_pid=a.order_pid
        LEFT JOIN smc_course c ON c.course_id=f.course_id
        LEFT JOIN smc_code_coursecat b ON b.coursecat_id=c.coursecat_id
        LEFT JOIN smc_code_coursetype e ON e.coursetype_id=c.coursetype_id
        WHERE 1 {$where}
        AND m.pay_issuccess=1
        AND m.pay_successtime>0
        AND m.paytype_code NOT IN ('feewaiver', 'canceldebts', 'coursebalance')
        AND m.pay_type=0
        AND m.pay_price>=e.coursetype_newminprice
        AND e.coursetype_id IS NOT NULL
        AND e.coursetype_isregistercalc=1
        AND a.order_status=4 
        AND a.order_paidprice>0 
        AND M.pay_successtime>=unix_timestamp(DATE_SUB(CURDATE(),INTERVAL 25 DAY))
        AND M.pay_successtime<unix_timestamp(curdate())+86400
        AND not exists(select 1 from smc_student_registerinfo where company_id=a.company_id and school_id=a.school_id and student_id=a.student_id and coursetype_id=e.coursetype_id and trading_pid=a.trading_pid and info_status=1)
        AND not exists(select 1 from smc_student_registertemp where trading_pid=a.trading_pid and info_status>=0 and coursetype_id=e.coursetype_id)
        AND not exists(select 1 from smc_payfee_order_pay where order_pid=a.order_pid and pay_issuccess=1 and paytype_code='coursebalance')
        AND not exists(select 1 from smc_student_registerforb where coursetype_id=e.coursetype_id and student_branch=d.student_branch)
        group by a.trading_pid,e.coursetype_id
        order by m.pay_successtime asc";
        $newList = $this->DataControl->selectClear($sql);
        //先把待处理的数据放到temp
        if ($newList) {
            foreach ($newList as $newOne) {
                $tempOne = $this->DataControl->getFieldOne('smc_student_registertemp', "info_id,info_status", "trading_pid='{$newOne['trading_pid']}' and coursetype_id='{$newOne['coursetype_id']}'");
                if ($tempOne && $tempOne['info_status'] == 1) {
                    continue;//如果它线程已处理则跳过
                }

                $newdata = array();
                $newdata['company_id'] = $newOne['company_id'];
                $newdata['school_id'] = $newOne['school_id'];
                $newdata['student_id'] = $newOne['student_id'];
                $newdata['from_client_id'] = $newOne['from_client_id'];
                $newdata['trading_pid'] = $newOne['trading_pid'];
                $newdata['order_status'] = $newOne['order_status'];
                $newdata['order_createtime'] = $newOne['order_createtime'];
                $newdata['coursetype_id'] = $newOne['coursetype_id'];
                $newdata['coursetype_cnname'] = $newOne['coursetype_cnname'];
                $newdata['coursetype_branch'] = $newOne['coursetype_branch'];
                $newdata['coursecat_id'] = $newOne['coursecat_id'];
                $newdata['coursecat_cnname'] = $newOne['coursecat_cnname'];
                $newdata['coursecat_branch'] = $newOne['coursecat_branch'];
                $newdata['course_id'] = $newOne['course_id'];
                $newdata['pay_id'] = $newOne['pay_id'];
                $newdata['pay_price'] = $newOne['pay_price'];
                $newdata['pay_successtime'] = $newOne['pay_successtime'];
                $newdata['xz_marketer_id'] = $newOne['xz_marketer_id'];
                $newdata['kz_marketer_id'] = $newOne['kz_marketer_id'];
                $newdata['coursetype_newchecked'] = $newOne['coursetype_newchecked'];
                $newdata['regi_coursetype'] = $newOne['regi_coursetype'];
                $newdata['info_status'] = $newOne['info_status'];

                if (($newOne['coursetype_id'] == 65) && date("Y-m-d", $newOne['pay_successtime']) >= '2024-08-19') {
                    $sql = "select sum(a.pay_price) as pay_price
                        from smc_payfee_order_pay as a,smc_code_paytype as b,smc_payfee_order as c 
                        where a.paytype_code=b.paytype_code and c.order_pid=a.order_pid and c.student_id='{$newOne['student_id']}' and b.paytype_ischarge=1
                        AND c.order_status = 4 and FROM_UNIXTIME( a.pay_successtime, '%Y-%m-%d' )= FROM_UNIXTIME( {$newOne['pay_successtime']}, '%Y-%m-%d' ) and c.coursetype_id in (65,61)
                        and not exists(SELECT
                                            1 
                                        FROM
                                            smc_student_hourstudy AS x,
                                            smc_class AS y,
                                            smc_class_hour AS z,smc_course as p  
                                        WHERE
                                            x.class_id = y.class_id 
                                            AND z.hour_id = x.hour_id and p.course_id=y.course_id
                                            AND x.student_id = c.student_id 
                                            AND p.coursetype_id in (65,61) )
                        and not exists(select 1 from smc_payfee_order as x where x.student_id=c.student_id and x.info_iscontinuepay=1)
                        ";
                    $payOne = $this->DataControl->selectOne($sql);

                    if ($payOne && $payOne['pay_price'] >= 13000) {
                        $data = array();
                        $data['info_iscontinuepay'] = 1;
                        $data['order_updatatime'] = time();

                        $this->DataControl->updateData("smc_payfee_order", "trading_pid='{$newOne['trading_pid']}'", $data);

                    }
                }


                if ($tempOne) {
                    $newdata['info_updatetime'] = $newOne['info_time'];
                    $this->DataControl->updateData("smc_student_registertemp", "info_id = '{$tempOne['info_id']}'", $newdata);
                } else {
                    $newdata['info_createtime'] = $newOne['info_time'];
                    $this->DataControl->insertData("smc_student_registertemp", $newdata);
                }
            }
        }

        $sql = "select a.info_id,
            a.company_id,
            a.school_id,
            a.student_id,
            a.from_client_id,
            a.trading_pid,
            a.order_status,
            a.order_createtime,
            a.coursetype_id,
            a.coursetype_cnname,
            a.coursetype_branch,
            a.coursecat_id,
            a.coursecat_cnname,
            a.coursecat_branch,
            a.course_id,
            b.course_cnname,
            b.course_branch,
            a.pay_id,
            a.pay_price,
            a.pay_successtime,
            a.xz_marketer_id,
            a.kz_marketer_id,
            a.coursetype_newchecked,
            a.regi_coursetype,
            a.info_status,
            a.info_createtime,
            FROM_UNIXTIME(a.pay_successtime,'%Y-%m-%d') as new_date
            ,(select group_concat(course_id) from smc_payfee_order_course x,smc_payfee_order y where x.order_pid=y.order_pid and y.trading_pid=a.trading_pid) as course_ids
        from smc_student_registertemp a
        left join smc_course b on a.course_id=b.course_id
        where a.info_status=0 
        order by a.pay_successtime asc";
        $targetList = $this->DataControl->selectClear($sql);
        if (!$targetList) {
            return;
        }
        //处理temp
        foreach ($targetList as $targetOne) {
            //0.未完成订单不处理
            $ordercomplete = ($targetOne['order_status'] == '4') ? 1 : 0;
            if ($ordercomplete == 0) {
                continue;
            }

            //1.已有新生记录跳过
            $regiOne = $this->DataControl->getFieldOne('smc_student_registerinfo', "info_id,info_status", "trading_pid='{$targetOne['trading_pid']}' and info_status=1 and coursetype_id='{$targetOne['coursetype_id']}'");
            if ($regiOne) {
                $tempData = array();
                $tempData['info_status'] = '1';
                $tempData['info_remk'] = '0已有新生记录' . $regiOne['info_id'];
                $tempData['info_updatetime'] = time();
                $this->DataControl->updateData("smc_student_registertemp", "info_id = '{$targetOne['info_id']}'", $tempData);
                continue;
            }

            //有任意新生记录
            $regiOldAll = $this->DataControl->getFieldOne('smc_student_registerinfo', "info_id,course_id,pay_successtime"
                , "student_id='{$targetOne['student_id']}' and company_id='{$targetOne['company_id']}' and info_status='1' order by info_id desc ");

            $info_type = ($regiOldAll && $regiOldAll['pay_successtime'] <= $targetOne['pay_successtime']) ? 1 : 0;

            //有当日新生记录
            $regiSameDay = $this->DataControl->getFieldOne('smc_student_registerinfo', "info_id,xz_marketer_id,kz_marketer_id,pay_successtime"
                , "student_id='{$targetOne['student_id']}' and info_status='1' and info_type=0 
                and coursetype_id<>'{$targetOne['coursetype_id']}'
                and FROM_UNIXTIME(pay_successtime,'%Y-%m-%d')=FROM_UNIXTIME('{$targetOne['pay_successtime']}','%Y-%m-%d')");
            //and trading_pid<>'{$targetOne['trading_pid']}' and coursetype_id='{$targetOne['coursetype_id']}'

            //2.如果新生表无此订单数据
            if ($targetOne['coursetype_newchecked'] == '1') {
                //2.1班别新生
                //有同课程新生记录，课程新生使用
                $regiSameCourse = $this->DataControl->getFieldOne('smc_student_registerinfo', "info_id,course_id,pay_successtime"
                    , "student_id='{$targetOne['student_id']}' and course_id='{$targetOne['course_id']}' 
                    and trading_pid<>'{$targetOne['trading_pid']}' and info_status='1' order by info_id desc ");
                if ($regiSameCourse) {
                    $tempData = array();
                    $tempData['info_status'] = $ordercomplete;
                    $tempData['info_remk'] = '1有同课程新生记录' . $regiSameCourse['info_id'];
                    $tempData['info_updatetime'] = time();
                    $this->DataControl->updateData("smc_student_registertemp", "info_id = '{$targetOne['info_id']}'", $tempData);
                    continue;
                }

                $newData = array();
                $newData['company_id'] = $targetOne['company_id'];
                $newData['school_id'] = $targetOne['school_id'];
                $newData['student_id'] = $targetOne['student_id'];
                $newData['from_client_id'] = $targetOne['from_client_id'];
                $newData['info_status'] = 1;
                $newData['info_type'] = $info_type;
                $newData['coursetype_id'] = $targetOne['coursetype_id'];
                $newData['coursetype_cnname'] = $targetOne['coursetype_cnname'];
                $newData['coursetype_branch'] = $targetOne['coursetype_branch'];
                $newData['coursecat_id'] = $targetOne['coursecat_id'];
                $newData['coursecat_cnname'] = $targetOne['coursecat_cnname'];
                $newData['coursecat_branch'] = $targetOne['coursecat_branch'];
                $newData['course_id'] = $targetOne['course_id'];
                $newData['course_cnname'] = $targetOne['course_cnname'];
                $newData['course_branch'] = $targetOne['course_branch'];
                $newData['trading_pid'] = $targetOne['trading_pid'];
                $newData['order_createtime'] = $targetOne['order_createtime'];
                $newData['pay_price'] = $targetOne['pay_price'];
                $newData['pay_firsttime'] = $targetOne['pay_successtime'];
                $newData['pay_successtime'] = $targetOne['pay_successtime'];
                $newData['pay_id'] = $targetOne['pay_id'];
                $newData['xz_marketer_id'] = $targetOne['xz_marketer_id'];
                $newData['kz_marketer_id'] = $regiSameDay ? $regiSameDay['xz_marketer_id'] : $targetOne['kz_marketer_id'];
                $newData['confirm_reason'] = $regiSameDay ? '统一处理同日扩科负责人' : '';
                $newData['info_createtime'] = time();
                $this->DataControl->insertData("smc_student_registerinfo", $newData);

                $tempData = array();
                $tempData['info_status'] = $ordercomplete;
                $tempData['info_remk'] = '2课程新生';
                $tempData['info_updatetime'] = time();
                $this->DataControl->updateData("smc_student_registertemp", "info_id = '{$targetOne['info_id']}'", $tempData);
                continue;
            } else {
                if ($targetOne['coursecat_id'] == '11802') {
                    //有135就读记录则不算新生
                    $sql = "select count(a.hourstudy_id) as count_k
                            from smc_student_hourstudy a
                            left join smc_class b on a.class_id=b.class_id
                            left join smc_course c on b.course_id=c.course_id
                            where a.student_id='{$targetOne['student_id']}'
                            and c.coursecat_id='135'";
                    $kjCheckOne = $this->DataControl->selectOne($sql);
                    if ($kjCheckOne && $kjCheckOne['count_k'] > 0) {
                        $tempData = array();
                        $tempData['info_status'] = $ordercomplete;
                        $tempData['info_remk'] = '-3K升J，IRC不符合新生条件';
                        $tempData['info_updatetime'] = time();
                        $this->DataControl->updateData("smc_student_registertemp", "info_id = '{$targetOne['info_id']}'", $tempData);
                        continue;
                    }
                }
                //2.2班组新生
                $regiCoursetype = $this->DataControl->getFieldOne('smc_student_registerinfo', "info_id,pay_successtime"
                    , "student_id='{$targetOne['student_id']}' and coursetype_id='{$targetOne['coursetype_id']}' and info_status='1' order by info_id desc ");

                if ($targetOne['coursetype_id'] == '64' || $targetOne['coursetype_id'] == '79655') {
                    $regiCoursetype = $this->DataControl->getFieldOne('smc_student_registerinfo', "info_id,pay_successtime"
                        , "student_id='{$targetOne['student_id']}' and coursetype_id in ('64','{$targetOne['coursetype_id']}') and info_status='1' order by info_id desc ");
                }

                $sql = "select 1 
                from smc_student_coursebalance_log x,smc_course y 
                where x.course_id=y.course_id
                and x.log_playname LIKE '2.0%'
                and y.company_id='{$targetOne['company_id']}'
                and x.student_id='{$targetOne['student_id']}'
                and y.coursetype_id='{$targetOne['coursetype_id']}'";
                $coursebalanceList = $this->DataControl->selectClear($sql);

                $sql = "select 1 
                from smc_student_coursecatbalance x,smc_code_coursecat y 
                where x.coursecat_id=y.coursecat_id
                and x.feetype_code IN ('Manage', 'Times')
                and y.company_id='{$targetOne['company_id']}'
                and x.student_id='{$targetOne['student_id']}'
                and y.coursetype_id='{$targetOne['coursetype_id']}'
                and x.coursecatbalance_createtime<({$targetOne['pay_successtime']}-86400)";
                $coursecatbalanceList = $this->DataControl->selectClear($sql);

                $exists_oldtransfer = ($coursebalanceList || $coursecatbalanceList) ? 1 : 0;

                if (!$regiCoursetype && !$exists_oldtransfer) {
                    //无同班组新生记录和历史转档（屏蔽）记录
                    $newData = array();
                    $newData['company_id'] = $targetOne['company_id'];
                    $newData['school_id'] = $targetOne['school_id'];
                    $newData['student_id'] = $targetOne['student_id'];
                    $newData['from_client_id'] = $targetOne['from_client_id'];
                    $newData['info_status'] = 1;
                    $newData['info_type'] = $info_type;
                    $newData['coursetype_id'] = $targetOne['coursetype_id'];
                    $newData['coursetype_cnname'] = $targetOne['coursetype_cnname'];
                    $newData['coursetype_branch'] = $targetOne['coursetype_branch'];
                    $newData['coursecat_id'] = $targetOne['coursecat_id'];
                    $newData['coursecat_cnname'] = $targetOne['coursecat_cnname'];
                    $newData['coursecat_branch'] = $targetOne['coursecat_branch'];
                    $newData['course_id'] = $targetOne['course_id'];
                    $newData['course_cnname'] = $targetOne['course_cnname'];
                    $newData['course_branch'] = $targetOne['course_branch'];
                    $newData['trading_pid'] = $targetOne['trading_pid'];
                    $newData['order_createtime'] = $targetOne['order_createtime'];
                    $newData['pay_price'] = $targetOne['pay_price'];
                    $newData['pay_firsttime'] = $targetOne['pay_successtime'];
                    $newData['pay_successtime'] = $targetOne['pay_successtime'];
                    $newData['pay_id'] = $targetOne['pay_id'];
                    $newData['xz_marketer_id'] = $targetOne['xz_marketer_id'];
                    $newData['kz_marketer_id'] = $regiSameDay ? $regiSameDay['xz_marketer_id'] : $targetOne['kz_marketer_id'];
                    $newData['confirm_reason'] = $regiSameDay ? '统一处理同日扩科负责人' : '';
                    $newData['info_createtime'] = time();
                    $this->DataControl->insertData("smc_student_registerinfo", $newData);

                    $tempData = array();
                    $tempData['info_status'] = $ordercomplete;
                    $tempData['info_remk'] = '3班组新生';
                    $tempData['info_updatetime'] = time();
                    $this->DataControl->updateData("smc_student_registertemp", "info_id = '{$targetOne['info_id']}'", $tempData);
                    continue;
                } else {
                    //有同班组新生记录
                    //0)只有美语有流失回读
//                    if ($targetOne['coursetype_id'] !== '65') {
//                        $tempData = array();
//                        $tempData['info_status'] = $ordercomplete;
//                        $tempData['info_remk'] = '-2有班组新生记录' . $regiCoursetype['info_id'];
//                        $tempData['info_updatetime'] = time();
//                        $this->DataControl->updateData("smc_student_registertemp", "info_id = '{$targetOne['info_id']}'", $tempData);
//                        continue;
//                    }

                    //1)之前有流失和复读的且复读之后没有新生记录的，生成
                    $sql = "select stuchange_code as change_code
                        ,UNIX_TIMESTAMP(changelog_day) as change_time
                        ,a.changelog_day
                        from smc_student_changelog a
                        where a.student_id='{$targetOne['student_id']}'
                        and a.school_id='{$targetOne['school_id']}'
                        and (stuchange_code in('C02','D02') OR (stuchange_code in('C04','D04') AND coursetype_id='{$targetOne['coursetype_id']}'))
                        order by a.changelog_id desc";
                    $changeList = $this->DataControl->selectClear($sql);

//                    $sql = "select unix_timestamp(max(b.hour_day)) as old_check_date
//                        from smc_student_hourstudy a
//                        left join smc_class_hour b on a.hour_id=b.hour_id
//                        left join smc_class c on b.class_id=c.class_id
//                        left join smc_course d on c.course_id=d.course_id
//                        where a.student_id='{$targetOne['student_id']}'
//                        -- and c.school_id='{$targetOne['school_id']}'
//                        and d.coursetype_id='{$targetOne['coursetype_id']}'
//                        and b.hour_day<'{$changeList[0]['changelog_day']}'";
//                    $oldCheckList = $this->DataControl->selectOne($sql);

                    if ($targetOne['coursecat_id'] == '11802') {
                        //有135就读记录则不算新生
                        $sql = "select count(a.hourstudy_id) as count_k
                            from smc_student_hourstudy a
                            left join smc_class b on a.class_id=b.class_id
                            left join smc_course c on b.course_id=c.course_id
                            where a.student_id='{$targetOne['student_id']}'
                            and c.coursecat_id='135'";
                        $kjCheckOne = $this->DataControl->selectOne($sql);
                        if ($kjCheckOne && $kjCheckOne['count_k'] > 0) {
                            $tempData = array();
                            $tempData['info_status'] = $ordercomplete;
                            $tempData['info_remk'] = '-3K升J，IRC不符合新生条件';
                            $tempData['info_updatetime'] = time();
                            $this->DataControl->updateData("smc_student_registertemp", "info_id = '{$targetOne['info_id']}'", $tempData);
                            continue;
                        }
                    }
                    if ($changeList && count($changeList) >= 2
                        && ($changeList[0]['change_code'] == 'D02' || $changeList[0]['change_code'] == 'D04')
                        && ($targetOne['pay_successtime'] - $regiCoursetype['pay_successtime']) / 86400 >= 180
                        && ($targetOne['pay_successtime'] - $changeList[0]['change_time']) / 86400 < 15
                        && ($targetOne['pay_successtime'] - $changeList[1]['change_time']) / 86400 >= 180
//                            || ($targetOne['pay_successtime'] - $oldCheckList['old_check_date']) / 86400 >= 180)//20230801改为按流失时间开始计算
                    ) {
                        //班组新生，且上个同班组新生是1年前，生成新生，20230801改为180天
                        $newData = array();
                        $newData['company_id'] = $targetOne['company_id'];
                        $newData['school_id'] = $targetOne['school_id'];
                        $newData['student_id'] = $targetOne['student_id'];
                        $newData['from_client_id'] = $targetOne['from_client_id'];
                        $newData['info_status'] = 1;
                        $newData['info_type'] = $info_type;
                        $newData['coursetype_id'] = $targetOne['coursetype_id'];
                        $newData['coursetype_cnname'] = $targetOne['coursetype_cnname'];
                        $newData['coursetype_branch'] = $targetOne['coursetype_branch'];
                        $newData['coursecat_id'] = $targetOne['coursecat_id'];
                        $newData['coursecat_cnname'] = $targetOne['coursecat_cnname'];
                        $newData['coursecat_branch'] = $targetOne['coursecat_branch'];
                        $newData['course_id'] = $targetOne['course_id'];
                        $newData['course_cnname'] = $targetOne['course_cnname'];
                        $newData['course_branch'] = $targetOne['course_branch'];
                        $newData['trading_pid'] = $targetOne['trading_pid'];
                        $newData['order_createtime'] = $targetOne['order_createtime'];
                        $newData['pay_price'] = $targetOne['pay_price'];
                        $newData['pay_firsttime'] = $targetOne['pay_successtime'];
                        $newData['pay_successtime'] = $targetOne['pay_successtime'];
                        $newData['pay_id'] = $targetOne['pay_id'];
                        $newData['xz_marketer_id'] = '0';//班组回读取扩科负责人
                        $newData['kz_marketer_id'] = $targetOne['kz_marketer_id'];
                        $newData['confirm_reason'] = $regiSameDay ? '统一处理同日扩科负责人' : '';
                        $newData['info_createtime'] = time();
                        $this->DataControl->insertData("smc_student_registerinfo", $newData);

                        if ($regiSameDay) {
                            $sameData = array();
                            $sameData['xz_marketer_id'] = '0';
                            $sameData['kz_marketer_id'] = $targetOne['kz_marketer_id'];
                            $sameData['confirm_reason'] = '统一处理同日扩科负责人,4班组回读新生';
                            $sameData['info_updatetime'] = time();
                            $this->DataControl->updateData("smc_student_registerinfo", "info_id = '{$regiSameDay['info_id']}'", $sameData);
                        }

                        $tempData = array();
                        $tempData['info_status'] = $ordercomplete;
                        $tempData['info_remk'] = '4班组回读新生';
                        $tempData['info_updatetime'] = time();
                        $this->DataControl->updateData("smc_student_registertemp", "info_id = '{$targetOne['info_id']}'", $tempData);
                        continue;
                    } else {
                        $tempData = array();
                        $tempData['info_status'] = $ordercomplete;
                        $tempData['info_remk'] = '-1不符合新生条件';
                        $tempData['info_updatetime'] = time();
                        $this->DataControl->updateData("smc_student_registertemp", "info_id = '{$targetOne['info_id']}'", $tempData);
                        continue;
                    }
                }
            }
        }
        return 1;
    }

    function fillCourseTypeApi()
    {
        $request = Input('post.', '', 'trim,addslashes');
//        $this->ThisVerify($request);
        $Model = new \Model\Api\ClassendcalcModel($request);
        $res = $Model->fillCourseType($request);
        $result = array();
        if ($res) {
            $result["list"] = $res;
            $result = array('error' => 0, 'errortip' => '补全成功', 'result' => $result);
        } else {
            $result = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($result, $request['language_type']);
    }

    //提前续费券自动发放
    function autoGrantCouponsApi()
    {
        $request = Input('post.', '', 'trim,addslashes');
//        $this->ThisVerify($request);
        $Model = new \Model\Api\ClassendcalcModel($request);
        $res = $Model->autoGrantCoupons($request);
        $result = array();
        if ($res) {
            $result["list"] = $res;
            $result = array('error' => 0, 'errortip' => '提前续费券自动发放成功', 'result' => $result);
        } else {
            $result = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($result, $request['language_type']);
    }

    //提前续费券自动发放
    function autoClassGrantCouponsApi()
    {
        $request = Input('get.', '', 'trim,addslashes');

        if (!isset($request['class_branch']) || $request['class_branch'] == '') {
            ajax_return(array('error' => '1', 'errortip' => '传班级编号', 'result' => false));
        }
        $sql = "select a.endcalc_id,endcalc_issettle from smc_class_endcalc a,smc_class b 
        where a.class_id=b.class_id and b.class_branch='{$request['class_branch']}'";

        $calcOne = $this->DataControl->selectOne($sql);

        if ($calcOne['endcalc_issettle'] < 1) {
            ajax_return(array('error' => '1', 'errortip' => '班级留班率未固定', 'result' => false));
        }
        $Model = new \Model\Api\ClassendcalcModel($request);
        $res = $Model->autoClassGrantCoupons($request);
        $result = array();
        if ($res) {
            $result["list"] = $res;
            $result = array('error' => 0, 'errortip' => '补发续费券自动发放成功', 'result' => $result);
        } else {
            $result = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($result, $request['language_type']);
    }

    function singleStudentRechargeApi()
    {
        $paramArray = Input('get.', '', 'trim,addslashes');

        if (!isset($paramArray['school_branch']) || $paramArray['school_branch'] == '') {
            ajax_return(array('error' => '1', 'errortip' => '需要学校编号', 'result' => false));
        }
        if (!isset($paramArray['student_branch']) || $paramArray['student_branch'] == '') {
            ajax_return(array('error' => '1', 'errortip' => '需要学生编号', 'result' => false));
        }

        $sql = "select a.enrolled_status,a.school_id,a.student_id
            from smc_student_enrolled a,smc_student b,smc_school c
            where a.student_id=b.student_id and a.school_id=c.school_id
              and c.school_branch='{$paramArray['school_branch']}'
              and b.student_branch='{$paramArray['student_branch']}'
              and a.enrolled_status>=0";

        $schoolOne = $this->DataControl->selectOne($sql);
        if (!$schoolOne) {
            ajax_return(array('error' => '1', 'errortip' => '未查询到在校数据，确认学校/学生编号是否正确', 'result' => false));
        }

        $sql = "select a.companies_id 
            from smc_school_coursecat_subject a
            left join smc_school b on a.school_id=b.school_id
            where a.coursecat_id=11902
            and b.school_branch='{$paramArray['school_branch']}' ";
        $companies = $this->DataControl->selectOne($sql);

        if (!$companies) {
            ajax_return(array('error' => '1', 'errortip' => '未查询到主体数据', 'result' => false));
        }

        $request = array();
        $request['companies_id'] = $companies['companies_id'];
        $request['school_id'] = $schoolOne['school_id'];
        $request['student_id'] = $schoolOne['student_id'];
        $request['price'] = 1500;

        $Model = new \Model\Api\ClassendcalcModel();
        $res = $Model->singleStudentRecharge($request);
        if ($res) {
            $result = array('error' => 0, 'errortip' => '充值成功', 'result' => '');
        } else {
            $result = array('error' => 1, 'errortip' => $Model->errortip, 'result' => '');
        }
        ajax_return($result, $paramArray['language_type']);
    }
}