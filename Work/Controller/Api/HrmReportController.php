<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/5/27
 * Time: 11:56
 */

namespace Work\Controller\Api;

class HrmReportController extends viewTpl
{
    public $u;
    public $t;
    public $c;
    public $visitType = "api";

    //预加载处理类
    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
        $this->api = $this->router->getApi();
    }

    /**
     * 校务追踪大表
     * author: ling
     * 对应接口文档 0001
     * Date 2021/2/23 0023
     */
    function getSchClientTrackReportApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
//        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Api\HrmReportModel();
        $datalist = $Model->getSchClientTrackReport($request);

        $fieldname = array('校区名称', '校区编号', '报名/周', '报名/月', '新增线下名单/周', '新增线下名单/月', '新增线上名单/周', '新增线上名单/月', '新增名单/月', '新增名单/季', '推荐名单/月', '总有效名单', '待分配名单数', '追踪名单数/周', '追踪名单数/月', '呼出名单数', "电询/周", "电询/月", '柜询/周', '柜询/月', 'OH邀约/周', 'OH邀约/上周', 'OH邀约/月', "OH到访/周", "OH到访/月", "OH转正数/月", "OH转正率/月", "试听/周", "试听/月", '内部报名数', '外部报名数', "专案报名数", "累计无意向名单", "累计无效名单");
        $fieldstring = array('school_shortname', 'school_branch', 'positivelog_week_num', 'positivelog_month_num', 'client_under_weeknum', 'client_under_monthnum', 'client_up_weeknum', 'client_up_monthnum', 'client_monthnum', 'client_up_quarternum', 'recomend_clientnum', 'client_all_num', 'client_noallot_num', 'track_client_num', 'track_client_monthnum', "out_calll_num", 'track_week_tephonenum', 'track_month_tephonenum', 'invite_week_num', 'invite_month_num', 'ohaudition_curweek_num', 'ohaudition_lastweek_num', 'ohaudition_month_num', 'ohaudition_week_arrnum', 'ohaudition_month_arrnum', 'ohpostive_month_arrnum', 'ohpostive_month_rate', 'audition_week_num', 'audition_month_num', 'client_innernum', 'client_outnum', 'client_casenum', 'client_nointention_num', "client_noeffctive_num");

        $fieldcustom = array('1', '1', '1', "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1');
        $fieldshow = array('1', '1', '1', "1", '1', "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "0", "1", '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1');

        $field = array();
        $field_array = array('positivelog_week_num', 'positivelog_month_num', 'client_under_weeknum', 'client_under_monthnum', 'client_up_weeknum', 'client_up_monthnum', 'client_up_quarternum', 'ohaudition_week_num', 'ohaudition_month_num', 'ohaudition_week_arrnum', 'ohaudition_month_arrnum', 'audition_week_num', 'audition_month_num', 'ohpostive_month_arrnum');
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["fieldname"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
            if (in_array($field[$i]["fieldstring"], $field_array)) {
                $field[$i]["ismethod"] = 0;
                $field[$i]["table_name"] = $field[$i]['fieldname'] . '统计表';
            }
        }
        $result = array();
        $result['field'] = $field;
        $result['list'] = $datalist['list'] == false ? array() : $datalist['list'];
        $allnum = $datalist['allnum'] == false ? 0 : $datalist['allnum'];
        $result['allnum'] = $allnum;

        if ($result['list']) {
            $res = array('error' => '0', 'errortip' => "获取招生追踪大表成功", 'result' => $result, "allnum" => $allnum);
        } else {
            $res = array('error' => '1', 'errortip' => "暂无记录", 'result' => $result, "allnum" => $allnum);
        }
        ajax_return($res, $request['language_type']);
    }

    /**
     * 渠道分析
     * author: ling
     * 对应接口文档 0001
     * Date 2021/2/23 0023
     */
    function channelClientReportApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
//        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Api\HrmReportModel();
        $datalist = $Model->channelClientReport($request);

        $k = 0;
        $field = array();
        $field[$k]["fieldstring"] = "frommedia_name";
        $field[$k]["fieldname"] = "招生渠道类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "channel_num";
        $field[$k]["fieldname"] = "渠道明细数量";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "client_newaddnums";
        $field[$k]["fieldname"] = "新增毛名单数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "client_newvalidnums";
        $field[$k]["fieldname"] = "新增有效名单数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "princal_track_num";
        $field[$k]["fieldname"] = "正在跟进名单数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "positivenum";
        $field[$k]["fieldname"] = "已报名名单数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "positive_rate";
        $field[$k]["fieldname"] = "报名率";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "uneffective_num";
        $field[$k]["fieldname"] = "新增无效名单数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "no_intention";
        $field[$k]["fieldname"] = "新增无意向名单数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;


        $result = array();
        $result['field'] = $field;
        $result['list'] = is_array($datalist['list']) ? $datalist['list'] : [];
//        $result['list'] = $datalist['list'] == false ? array() : $datalist['list'];
        $allnum = $datalist['allnums'];
//        $positivewhere = $datalist['positivewhere'];
//        $clientwhere = $datalist['clientwhere'];
        $result['allnum'] = $allnum;
//        $result['positivewhere'] = $positivewhere;
//        $result['clientwhere'] = $clientwhere;


        if ($result['list']) {
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result, "allnum" => $allnum);
        } else {
            $res = array('error' => '1', 'errortip' => "暂无渠道业绩统计报表记录", 'result' => $result, "allnum" => $allnum);
        }
        ajax_return($res, $request['language_type']);

    }

    function customerTrackApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $Model = new \Model\Api\HrmReportModel();
        $res = $Model->customerTrack($request);
        $field = array();

        $k = 0;
        $field[$k]["fieldstring"] = "client_id";
        $field[$k]["fieldname"] = "客户ID";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "mainStaffer";
        $field[$k]["fieldname"] = "主要负责人";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "auxilStaffer";
        $field[$k]["fieldname"] = "协助负责人";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "client_cnname";
        $field[$k]["fieldname"] = "学员中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $field[$k]["ismethod"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "client_enname";
        $field[$k]["fieldname"] = "学员英文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "client_sex";
        $field[$k]["fieldname"] = "性别";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "client_age";
        $field[$k]["fieldname"] = "年龄";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "client_mobile";
        $field[$k]["fieldname"] = "联系电话";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "parenter_cnname";
        $field[$k]["fieldname"] = "家长姓名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "trackNum";
        $field[$k]["fieldname"] = "电访总次数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 0;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);

        }
        ajax_return($res, $request['language_type']);
    }

    function customerTrackItemApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $Model = new \Model\Api\HrmReportModel();
        $res = $Model->customerTrackItem($request);
        $field = array();

        $k = 0;
        $field[$k]["fieldstring"] = "client_id";
        $field[$k]["fieldname"] = "客户ID";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "mainStaffer";
        $field[$k]["fieldname"] = "主要负责人";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "auxilStaffer";
        $field[$k]["fieldname"] = "协助负责人";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "client_cnname";
        $field[$k]["fieldname"] = "学员中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "client_enname";
        $field[$k]["fieldname"] = "学员英文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "client_sex";
        $field[$k]["fieldname"] = "性别";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "client_age";
        $field[$k]["fieldname"] = "年龄";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "client_mobile";
        $field[$k]["fieldname"] = "联系电话";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "parenter_cnname";
        $field[$k]["fieldname"] = "家长姓名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "times";
        $field[$k]["fieldname"] = "电访次序";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "track_createtime";
        $field[$k]["fieldname"] = "电访日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "track_note";
        $field[$k]["fieldname"] = "电访内容";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 0;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);

        }
        ajax_return($res, $request['language_type']);
    }

    function counterInquiryListApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $Model = new \Model\Api\HrmReportModel();
        $res = $Model->counterInquiryList($request);
        $field = array();

        $k = 0;
        $field[$k]["fieldstring"] = "client_id";
        $field[$k]["fieldname"] = "客户ID";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "mainStaffer";
        $field[$k]["fieldname"] = "主要负责人";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "auxilStaffer";
        $field[$k]["fieldname"] = "协助负责人";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "client_cnname";
        $field[$k]["fieldname"] = "学员中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "client_enname";
        $field[$k]["fieldname"] = "学员英文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "client_sex";
        $field[$k]["fieldname"] = "性别";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "client_age";
        $field[$k]["fieldname"] = "年龄";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "client_mobile";
        $field[$k]["fieldname"] = "联系电话";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "parenter_cnname";
        $field[$k]["fieldname"] = "家长姓名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "course_cnname";
        $field[$k]["fieldname"] = "意向报名课程";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "track_createtime";
        $field[$k]["fieldname"] = "柜询时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "audition_visittime";
        $field[$k]["fieldname"] = "公开课试听时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "audition_visittime2";
        $field[$k]["fieldname"] = "插班试听时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "track_note";
        $field[$k]["fieldname"] = "电访内容";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 0;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);

        }
        ajax_return($res, $request['language_type']);
    }

    /**
     * 招生工作统计表
     * author: wgh
     * 对应接口文档 0001
     * Date 2021/2/23 0023
     */
    function enrollmentApi()
    {
        $request = Input('get.', '', 'trim,addslashes');

        $Model = new \Model\Api\HrmReportModel();
        $datalist = $Model->enrollmentApi($request);

        $k = 0;
        $field = array();
        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "staffer_cnname";
        $field[$k]["fieldname"] = "教师中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "staffer_enname";
        $field[$k]["fieldname"] = "教师英文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "staffer_branch";
        $field[$k]["fieldname"] = "教师编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "post_name";
        $field[$k]["fieldname"] = "职务";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "client_num";
        $field[$k]["fieldname"] = "主负责总名单数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["ismethod"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "inv_aud_num";
        $field[$k]["fieldname"] = "邀约总名单数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "visitnum";
        $field[$k]["fieldname"] = "邀约到访总名单数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "audition_num";
        $field[$k]["fieldname"] = "邀约试听名单数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "invite_num";
        $field[$k]["fieldname"] = "邀约柜询名单数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "OH_audition_num";
        $field[$k]["fieldname"] = "OH邀约名单数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "positivenum";
        $field[$k]["fieldname"] = "邀约报名名单数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "positive_rate";
        $field[$k]["fieldname"] = "邀约报名率";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result['field'] = $field;
        $result['list'] = $datalist['list'];
        $result['allnum'] = $datalist['allnums'];
        if ($result['list']) {
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $res = array('error' => '1', 'errortip' => "招生工作统计表", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    /**
     * 公开课运作表
     * author: qyh
     * 对应接口文档
     * Date 2021/2/26
     */
    function openClassOperateApi()
    {
        $request = Input('get.', '', 'trim,addslashes');

        $Model = new \Model\Api\HrmReportModel();
        $datalist = $Model->openClassOperateApi($request);

        $k = 0;
        $field = array();

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "time";
        $field[$k]["fieldname"] = "公开课时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "main_teacher";
        $field[$k]["fieldname"] = "主教老师";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "less_teacher";
        $field[$k]["fieldname"] = "助教老师";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "allnum";
        $field[$k]["fieldname"] = "邀约总人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "smcnum";
        $field[$k]["fieldname"] = "学校邀约人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "gmcnum";
        $field[$k]["fieldname"] = "集团邀约人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "arrivenum";
        $field[$k]["fieldname"] = "学校邀约到访人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "arriverate";
        $field[$k]["fieldname"] = "学校邀约到访率";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "gmcnum";
        $field[$k]["fieldname"] = "集团邀约到访人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "gmcnum";
        $field[$k]["fieldname"] = "集团邀约到访率";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "fullnum";
        $field[$k]["fieldname"] = "学校邀约报名数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "fullrate";
        $field[$k]["fieldname"] = "学校邀约报名率";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "gmcnum";
        $field[$k]["fieldname"] = "集团邀约报名数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "gmcnum";
        $field[$k]["fieldname"] = "集团邀约报名率";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "fullnum";
        $field[$k]["fieldname"] = "报名总人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "fullrate";
        $field[$k]["fieldname"] = "报名率";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result['field'] = $field;
        $result['list'] = $datalist['list'];
        $result['allnum'] = $datalist['allnums'];
        if ($result['list']) {
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $res = array('error' => '1', 'errortip' => "获取失败", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    /**
     * 公开课名单明细表
     * author: qyh
     * 对应接口文档
     * Date 2021/2/26
     */
    function openClassListApi()
    {
        $request = Input('get.', '', 'trim,addslashes');

        $Model = new \Model\Api\HrmReportModel();
        $datalist = $Model->openClassListApi($request);

        $k = 0;
        $field = array();

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "client_cnname";
        $field[$k]["fieldname"] = "学员中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "client_enname";
        $field[$k]["fieldname"] = "学员英文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "marketer_name";
        $field[$k]["fieldname"] = "主要负责人";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "fu_marketer_name";
        $field[$k]["fieldname"] = "协助负责人";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "client_sex";
        $field[$k]["fieldname"] = "性别";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "client_age";
        $field[$k]["fieldname"] = "年龄";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "client_mobile";
        $field[$k]["fieldname"] = "联系电话";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "family_cnname";
        $field[$k]["fieldname"] = "家长姓名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "time";
        $field[$k]["fieldname"] = "公开课时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "receiver_name";
        $field[$k]["fieldname"] = "接待人";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "invite_isvisit";
        $field[$k]["fieldname"] = "是否试听";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "client_remark";
        $field[$k]["fieldname"] = "备注";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result['field'] = $field;
        $result['list'] = $datalist['list'];
        $result['allnum'] = $datalist['allnums'];
        if ($result['list']) {
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $res = array('error' => '1', 'errortip' => "获取失败", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    /**
     * 渠道月度分析
     * author: ling
     * 对应接口文档 0001
     * Date 2021/2/23 0023
     */
    function channelMonthReportApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $Model = new \Model\Api\HrmReportModel();
        $datalist = $Model->channelMonthReport($request);
//        $field = array();
//
//        foreach ($datalist['field'] as $key => $value) {
//            $field[$key]["fieldname"] = $value['frommedia_name'];
//            $field[$key]["fieldstring"] = $value['frommedia_string'];
//            $field[$key]["show"] = 1;
//            $field[$key]["custom"] = 1;
//        }
        $result = array();
        $result['field'] = $datalist['field'];
        $result['list'] = $datalist['list'];
        $result['allnum'] = $datalist['allnum'];
        $errortip = $datalist['list'] == false ? "暂无渠道月度分析数据" : "获取成功";
        $res = array('error' => '0', 'errortip' => $errortip, 'result' => $result);
        ajax_return($res, $request['language_type']);
    }

    /**
     * 获取学校
     * author: ling
     * 对应接口文档 0001
     * Date 2021/2/27 0027
     */
    function getSchoolApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $Model = new \Model\Api\HrmReportModel();
        $datalist = $Model->getSchoolApi($request);
        $result = array();
        $result['list'] = $datalist;
        $result['allnum'] = '';
        $errortip = $datalist == false ? "暂无学校数据" : "获取成功";
        $res = array('error' => '0', 'errortip' => $errortip, 'result' => $result);
        ajax_return($res, $request['language_type']);

    }

    /**
     * 获取招生人员信息
     * author: ling
     * 对应接口文档 0001
     * Date 2021/2/27 0027
     */
    function getMarketerApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $Model = new \Model\Api\HrmReportModel();
        $datalist = $Model->getMarketerApi($request);
        $result = array();
        $result['list'] = $datalist;
        $result['allnum'] = '';
        $errortip = $datalist == false ? "暂无职工数据" : "获取成功";
        $res = array('error' => '0', 'errortip' => $errortip, 'result' => $result);
        ajax_return($res, $request['language_type']);

    }

    /**
     * 获取职工
     * author: 瞿
     */
    function getStafferApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $Model = new \Model\Api\HrmReportModel();
        $datalist = $Model->getStafferApi($request);
        $result = array();
        $result['list'] = $datalist;
        $result['allnum'] = '';
        $errortip = $datalist == false ? "暂无职工数据" : "获取成功";
        $res = array('error' => '0', 'errortip' => $errortip, 'result' => $result);
        ajax_return($res, $request['language_type']);

    }

    /**
     * 获取班组
     * author: ling
     * 对应接口文档 0001
     * Date 2021/2/27 0027
     */
    function getCourseTypeApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $Model = new \Model\Api\HrmReportModel();
        $datalist = $Model->getCourseTypeApi($request);
        $result = array();
        $result['list'] = $datalist;
        $result['allnum'] = '';
        $errortip = $datalist == false ? "暂无班组数据" : "获取成功";
        $res = array('error' => '0', 'errortip' => $errortip, 'result' => $result);
        ajax_return($res, $request['language_type']);
    }

    /**
     * 学生试听运作明细表
     * author: xzl
     * 对应接口文档
     * Date 2021/3/7
     */
    function studentAuditionOperateApi()
    {
        $request = Input('get.', '', 'trim,addslashes');

        $Model = new \Model\Api\HrmReportModel();
        $datalist = $Model->studentAuditionOperateReport($request);

        $k = 0;
        $field = array();
        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "client_cnname";
        $field[$k]["fieldname"] = "学员中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "client_enname";
        $field[$k]["fieldname"] = "学员英文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "client_sex";
        $field[$k]["fieldname"] = "性别";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "client_age";
        $field[$k]["fieldname"] = "年龄";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "client_mobile";
        $field[$k]["fieldname"] = "联系电话";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "parenter_cnname";
        $field[$k]["fieldname"] = "家长姓名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "main_principal";
        $field[$k]["fieldname"] = "主要负责人";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "sub_principal";
        $field[$k]["fieldname"] = "协助负责人";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "audition_genre";
        $field[$k]["fieldname"] = "试听类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "receiver_name";
        $field[$k]["fieldname"] = "接待人";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_cnname";
        $field[$k]["fieldname"] = "试听班级名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "course_cnname";
        $field[$k]["fieldname"] = "试听课程别名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "course_branch";
        $field[$k]["fieldname"] = "试听课程别编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "study_num";
        $field[$k]["fieldname"] = "班级人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "main_staffer";
        $field[$k]["fieldname"] = "主教老师";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "sub_staffer";
        $field[$k]["fieldname"] = "助教老师";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "audition_visittime";
        $field[$k]["fieldname"] = "试听课程时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "audition_isvisit";
        $field[$k]["fieldname"] = "是否试听";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "first_visittime";
        $field[$k]["fieldname"] = "首次试听时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "audition_times";
        $field[$k]["fieldname"] = "第几次试听";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "register_date";
        $field[$k]["fieldname"] = "报名日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "client_remark";
        $field[$k]["fieldname"] = "备注";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result['field'] = $field;
        $result['list'] = $datalist['list'];
        $result['allnum'] = $datalist['allnum'];
        if ($result['list']) {
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $res = array('error' => '1', 'errortip' => "获取失败", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function getSchoolAfterClassInfoApi()
    {
        $request = Input('get.', '', 'trim,addslashes');

        $Model = new \Model\Api\HrmReportModel();
        $datalist = $Model->afterClassInfoReport($request);

        $k = 0;
        $field = array();
        $field[$k]["fieldstring"] = "school_name";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "register_num";
        $field[$k]["fieldname"] = "在籍人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "reading_num";
        $field[$k]["fieldname"] = "在读人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "classroom_num";
        $field[$k]["fieldname"] = "教室使用数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_num";
        $field[$k]["fieldname"] = "在开班级数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_avg_num";
        $field[$k]["fieldname"] = "班平均";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "new_regi_num";
        $field[$k]["fieldname"] = "本学期新生累计人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result['field'] = $field;
        $result['list'] = $datalist['list'];
        $result['allnum'] = $datalist['allnum'];
        if ($result['list']) {
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $res = array('error' => '1', 'errortip' => "获取失败", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function getTeachAndStudyInfoApi()
    {
        $request = Input('get.', '', 'trim,addslashes');

        $Model = new \Model\Api\HrmReportModel();
        $datalist = $Model->teachAndStudyInfoReport($request);

        $k = 0;
        $field = array();
        $field[$k]["fieldstring"] = "school_name";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursetype_cnname";
        $field[$k]["fieldname"] = "班组名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursetype_branch";
        $field[$k]["fieldname"] = "班组编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "register_num";
        $field[$k]["fieldname"] = "在籍人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "reading_num";
        $field[$k]["fieldname"] = "在读人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "limit_losing_num";
        $field[$k]["fieldname"] = "年度可流失人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "minus_num";
        $field[$k]["fieldname"] = "差额";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "year_losing_num";
        $field[$k]["fieldname"] = "年流失人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "month_losing_num";
        $field[$k]["fieldname"] = "月流失人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "week_losing_num";
        $field[$k]["fieldname"] = "周流失人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "keep_num";
        $field[$k]["fieldname"] = "保留人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "will_study_num";
        $field[$k]["fieldname"] = "待开人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_num";
        $field[$k]["fieldname"] = "在读班级数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_avg_num";
        $field[$k]["fieldname"] = "在读班平均";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "limit_reading_num";
        $field[$k]["fieldname"] = "年度目标在读";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "year_reading_percatage";
        $field[$k]["fieldname"] = "在读达成率";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result['field'] = $field;
        $result['list'] = $datalist['list'];
        $result['allnum'] = $datalist['allnum'];
        if ($result['list']) {
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $res = array('error' => '1', 'errortip' => "获取失败", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function getSchoolStudLostInfoApi()
    {
        $request = Input('get.', '', 'trim,addslashes');

        $Model = new \Model\Api\HrmReportModel();
        $datalist = $Model->schoolStudLostInfoReport($request);

        $k = 0;
        $field = array();
        $field[$k]["fieldstring"] = "school_name";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursetype_cnname";
        $field[$k]["fieldname"] = "班组名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursetype_branch";
        $field[$k]["fieldname"] = "班组编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "course_branch";
        $field[$k]["fieldname"] = "流失课程别";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "lost_num";
        $field[$k]["fieldname"] = "流失人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "students";
        $field[$k]["fieldname"] = "流失学生";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result['field'] = $field;
        $result['list'] = $datalist['list'];
        $result['allnum'] = $datalist['allnum'];
        if ($result['list']) {
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $res = array('error' => '1', 'errortip' => "获取失败", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function getAchieveProcessApi()
    {
        $request = Input('get.', '', 'trim,addslashes');

        $Model = new \Model\Api\HrmReportModel();
        $datalist = $Model->achieveProcessReport($request);

        $k = 0;
        $field = array();
        $field[$k]["fieldstring"] = "school_name";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "year_target";
        $field[$k]["fieldname"] = "年度目标";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "regi_num_week";
        $field[$k]["fieldname"] = "周招生人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "regi_num_month";
        $field[$k]["fieldname"] = "月招生人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "regi_num_firsthalf";
        $field[$k]["fieldname"] = "上半年招生人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "regi_num_year";
        $field[$k]["fieldname"] = "自然年度招生人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "achieve_process";
        $field[$k]["fieldname"] = "招生达成率";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result['field'] = $field;
        $result['list'] = $datalist['list'];
        $result['allnum'] = $datalist['allnum'];
        if ($result['list']) {
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $res = array('error' => '1', 'errortip' => "获取失败", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function getPositiveProcessApi()
    {
        $request = Input('get.', '', 'trim,addslashes');

        $Model = new \Model\Api\HrmReportModel();
        $datalist = $Model->positiveProcessReport($request);

        $k = 0;
        $field = array();
        $field[$k]["fieldstring"] = "school_name";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "register_num";
        $field[$k]["fieldname"] = "在籍人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "reading_num";
        $field[$k]["fieldname"] = "在读人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "deposit_only_num";
        $field[$k]["fieldname"] = "当前定金数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "deposit_year_num";
        $field[$k]["fieldname"] = "年度缴定金总数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "register_year_num";
        $field[$k]["fieldname"] = "年度定转全总数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "deposit_week_num";
        $field[$k]["fieldname"] = "本周新增定金数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "register_week_num";
        $field[$k]["fieldname"] = "本周定转全总数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "deposit_month_num";
        $field[$k]["fieldname"] = "本月新增定金数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "register_month_num";
        $field[$k]["fieldname"] = "本月定转全总数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result['field'] = $field;
        $result['list'] = $datalist['list'];
        $result['allnum'] = $datalist['allnum'];
        if ($result['list']) {
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $res = array('error' => '1', 'errortip' => "获取失败", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function getRecruitInfoApi()
    {
        $request = Input('get.', '', 'trim,addslashes');

        $Model = new \Model\Api\HrmReportModel();
        $datalist = $Model->recruitInfoReport($request);

        $k = 0;
        $field = array();
        $field[$k]["fieldstring"] = "school_name";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "offline_week_num";
        $field[$k]["fieldname"] = "近7天线下新增有效名单";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "online_week_num";
        $field[$k]["fieldname"] = "近7天线上新增有效名单";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "invite_curweek_num";
        $field[$k]["fieldname"] = "当周OH邀约人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "invite_lastweek_num";
        $field[$k]["fieldname"] = "上周OH邀约人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "visit_lastweek_num";
        $field[$k]["fieldname"] = "上周OH到访人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "visit_lastweek_percent";
        $field[$k]["fieldname"] = "上周OH到访率";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "positive_lastweek_num";
        $field[$k]["fieldname"] = "上周OH报名人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "positive_lastweek_percent";
        $field[$k]["fieldname"] = "上周OH报名率";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result['field'] = $field;
        $result['list'] = $datalist['list'];
        $result['allnum'] = $datalist['allnum'];
        if ($result['list']) {
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $res = array('error' => '1', 'errortip' => "获取失败", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }
    

}