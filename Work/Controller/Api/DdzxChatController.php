<?php
/**
 * ============================================================================
 * 版权所有 : https://www.mohism.cn
 * 网站地址 : https://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/3/12
 * Time: 22:30
 */

namespace Work\Controller\Api;

class DdzxChatController extends viewTpl
{
    public $data;
    public $appId = 'wxff9e04b9a16746c4';
    public $appSecret = '26356916bcc36a6dbe37ed06bd96a628';
    public $wxuser;

    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
    }

    //校务系统叮铛助学家长授权登录
    function scptcloginView()
    {
        $request = Input('get.','','trim,addslashes');
        $host = "https://api.kedingdang.com/DdzxChat/ptcwxChatUrl/id-".$request['company_id'];
        $app = $this->DataControl->getFieldOne("gmc_company_wxchatnumber","wxchatnumber_appid,wxchatnumber_appsecret","company_id = '{$request['company_id']}' and wxchatnumber_class = '1' and wxchatnumber_impower = '0'");
        if($app){
            $this->appId = $app['wxchatnumber_appid'];
            $this->appSecret = $app['wxchatnumber_appsecret'];
        }else{
            $kedingdang = $this->DataControl->getFieldOne("gmc_company_wxchatnumber","wxchatnumber_appid,wxchatnumber_appsecret","company_id = '0' and wxchatnumber_class = '1'");
            $this->appId = $kedingdang['wxchatnumber_appid'];
            $this->appSecret = $kedingdang['wxchatnumber_appsecret'];
        }
        $url = "https://open.weixin.qq.com/connect/oauth2/authorize?appid=" . $this->appId . "&redirect_uri=" . $host . "&response_type=code&scope=snsapi_userinfo&state=1#wechat_redirect";
        header("location:" . $url);
    }

    //校务系统叮铛助学授权登录
    function scpcintoView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        if(!isset($request['tourl'])){
            $request['tourl'] = 'Home';
        }
        if($request['tourl'] == 'ClassNotice'){
            $host = "https://api.kedingdang.com/DdzxChat/wxChatUrl?url=ClassNotice";
        }elseif($request['tourl'] == 'SchoolNotice'){
            $host = "https://api.kedingdang.com/DdzxChat/wxChatUrl?url=SchoolNotice";
        }elseif($request['tourl'] == 'Home'){
            $host = "https://api.kedingdang.com/DdzxChat/wxChatUrl?url=Home";
        }elseif($request['tourl'] == 'myCenter'){
            $host = "https://api.kedingdang.com/DdzxChat/wxChatUrl?url=myCenter";
        }elseif($request['tourl'] == 'Center'){
            $host = "https://api.kedingdang.com/DdzxChat/wxChatUrl?url=Center";
        }elseif($request['tourl'] == 'myGrowUp'){
            $host = "https://api.kedingdang.com/DdzxChat/wxChatUrl?url=myGrowUp";
        }elseif($request['tourl'] == 'loginBind'){
            $host = "https://api.kedingdang.com/DdzxChat/wxChatUrl?url=loginBind";
        }elseif($request['tourl'] == 'loginBind'){
            $host = "https://api.kedingdang.com/DdzxChat/wxChatUrl?url=loginBind";
        }

        if(isset($request['cid']) && $request['cid'] !==''){
            $wxchatnumberOne = $this->DataControl->getFieldOne("gmc_company_wxchatnumber", "wxchatnumber_appid,wxchatnumber_appsecret", "company_id = '{$request['cid']}' and wxchatnumber_class = '1'", "order by wxchatnumber_id DESC limit 0,1");
            if($wxchatnumberOne){
                $this->appId = $wxchatnumberOne['wxchatnumber_appid'];
                $this->appSecret = $wxchatnumberOne['wxchatnumber_appsecret'];

                if($request['tourl'] == 'ClassNotice'){
                    $host = "https://api.kedingdang.com/DdzxChat/wxChatUrl/{$request['cid']}?url=ClassNotice";
                }elseif($request['tourl'] == 'SchoolNotice'){
                    $host = "https://api.kedingdang.com/DdzxChat/wxChatUrl/{$request['cid']}?url=SchoolNotice";
                }elseif($request['tourl'] == 'Home'){
                    $host = "https://api.kedingdang.com/DdzxChat/wxChatUrl/{$request['cid']}?url=Home";
                }elseif($request['tourl'] == 'myCenter'){
                    $host = "https://api.kedingdang.com/DdzxChat/wxChatUrl/{$request['cid']}?url=myCenter";
                }elseif($request['tourl'] == 'Center'){
                    $host = "https://api.kedingdang.com/DdzxChat/wxChatUrl/{$request['cid']}?url=Center";
                }elseif($request['tourl'] == 'myGrowUp'){
                    $host = "https://api.kedingdang.com/DdzxChat/wxChatUrl/{$request['cid']}?url=myGrowUp";
                }elseif($request['tourl'] == 'loginBind'){
                    $host = "https://api.kedingdang.com/DdzxChat/wxChatUrl/{$request['cid']}?url=loginBind";
                }
            }
        }

        $url = "https://open.weixin.qq.com/connect/oauth2/authorize?appid=" . $this->appId . "&redirect_uri=" . $host . "&response_type=code&scope=snsapi_userinfo&state=1#wechat_redirect";
        header("location:" . $url);
    }

    //家长登录入口
    function ptcwxChatUrlView()
    {

        $request = Input('get.','','trim,addslashes');

        if (isset($_GET['code'])) {
            $WeixinInfo = $this->getWeixinInfo($_GET['code'],$request['id']);
            $Weixinuser = $this->getWeixinUser($WeixinInfo['access_token'], $WeixinInfo['openid']);
            if ($Weixinuser['openid'] == '') {
                $url = "https://api.kedingdang.com/DdzxChat/scptclogin?company_id={$request['id']}";
                header("location:" . $url);
            }


            $openurl = "https://scptc.kedingdang.com/home?wxtoken={$Weixinuser['openid']}&imghead={$Weixinuser['headimgurl']}&nickname={$Weixinuser['nickname']}";
            header("location:{$openurl}");

            exit;
        } else {
            header("location:/");
            exit;
        }
    }

    //获取微信反馈数据
    function getWeixinInfo($CODE,$company_id)
    {
        $app = $this->DataControl->getFieldOne("gmc_company_wxchatnumber","wxchatnumber_appid,wxchatnumber_appsecret","company_id = '{$company_id}' and wxchatnumber_class = '1' and wxchatnumber_impower = '0'");
        if($app){
            $appId = $app['wxchatnumber_appid'];
            $appSecret = $app['wxchatnumber_appsecret'];
        }else{
            $kedingdang = $this->DataControl->getFieldOne("gmc_company_wxchatnumber","wxchatnumber_appid,wxchatnumber_appsecret","company_id = '0' and wxchatnumber_class = '1'");
            $appId = $kedingdang['wxchatnumber_appid'];
            $appSecret = $kedingdang['wxchatnumber_appsecret'];
        }
        $paramarray = array(
            'appid' => $appId,
            'secret' => $appSecret,
            'code' => $CODE,
            'grant_type' => "authorization_code"
        );

        $getBakurl = request_by_curl("https://api.weixin.qq.com/sns/oauth2/access_token", dataEncode($paramarray), "GET");
        $json_play = new \Webjson();
        $cardarray = $json_play->decode($getBakurl, "1");
        return $cardarray;
    }



    //获取微信User
    function getWeixinUser($token, $openid)
    {
        $paramarray = array(
            'access_token' => $token,
            'openid' => $openid,
            'lang' => "zh_CN"
        );

        $getBakurl = trim(request_by_curl("https://api.weixin.qq.com/sns/userinfo", dataEncode($paramarray), "GET"));
        $json_play = new \Webjson();
        $cardarray = $json_play->decode($getBakurl, "1");
        return $cardarray;
    }

    //雇员登录
    function wxChatUrlView()
    {
        $request = Input('get.','','trim,addslashes');
        if (isset($_GET['code'])) {
            if(isset($request['id']) && $request['id'] !=='') {
                $wxchatnumberOne = $this->DataControl->getFieldOne("gmc_company_wxchatnumber", "wxchatnumber_appid,wxchatnumber_appsecret", "company_id = '{$request['id']}' and wxchatnumber_class = '1'", "order by wxchatnumber_id DESC limit 0,1");
                if ($wxchatnumberOne) {
                    $this->appId = $wxchatnumberOne['wxchatnumber_appid'];
                    $this->appSecret = $wxchatnumberOne['wxchatnumber_appsecret'];
                }
            }
            $WeixinInfo = $this->getWeixinInfo($_GET['code'],$request['id']);
            $Weixinuser = $this->getWeixinUser($WeixinInfo['access_token'], $WeixinInfo['openid']);
            if ($Weixinuser['openid'] == '') {
                $url = "https://api.kedingdang.com/DdzxChat/scptclogin";
                header("location:" . $url);
            }

            if ($request['url'] == 'scptclogin') {
                $openurl = "https://scptc.kedingdang.com/home?wxtoken={$Weixinuser['openid']}&imghead={$Weixinuser['headimgurl']}&nickname={$Weixinuser['nickname']}";
                header("location:{$openurl}");
                exit;
            }elseif ($request['url'] == 'ClassNotice') {
                $openurl = "https://scptc.kedingdang.com/wxentry?wxtoken={$Weixinuser['openid']}&intourl=/ClassNotice/index";
                header("location:{$openurl}");
                exit;
            }elseif ($request['url'] == 'SchoolNotice') {
                $openurl = "https://scptc.kedingdang.com/wxentry?wxtoken={$Weixinuser['openid']}&intourl=/ClassNotice/school";
                header("location:{$openurl}");
                exit;
            }elseif ($request['url'] == 'Home') {
                if(isset($request['id']) && $request['id'] !==''){
//                    $openurl = "https://scptc.kedingdang.com/wxentry?wxtoken={$Weixinuser['openid']}&cid={$request['id']}&intourl=/home";
                    $openurl = "https://scptc.kedingdang.com/Home?wxtoken={$Weixinuser['openid']}&imghead={$Weixinuser['headimgurl']}&nickname={$Weixinuser['nickname']}&cid={$request['id']}";

                }else{
//                    $openurl = "https://scptc.kedingdang.com/wxentry?wxtoken={$Weixinuser['openid']}&intourl=/home";
                    $openurl = "https://scptc.kedingdang.com/Home?wxtoken={$Weixinuser['openid']}&imghead={$Weixinuser['headimgurl']}&nickname={$Weixinuser['nickname']}";

                }
                header("location:{$openurl}");
                exit;
            }elseif ($request['url'] == 'myCenter') {
                $openurl = "https://scptc.kedingdang.com/wxentry?wxtoken={$Weixinuser['openid']}&intourl=/myCenter";
                header("location:{$openurl}");
                exit;
            }elseif ($request['url'] == 'Center') {
                $openurl = "https://scptc.kedingdang.com/myCenter?wxtoken={$Weixinuser['openid']}&imghead={$Weixinuser['headimgurl']}&nickname={$Weixinuser['nickname']}";
                header("location:{$openurl}");
                exit;
            }elseif ($request['url'] == 'myGrowUp') {
                $openurl = "https://scptc.kedingdang.com/wxentry?wxtoken={$Weixinuser['openid']}&intourl=/MyCenter/myGrowUp";
                header("location:{$openurl}");
                exit;
            }elseif ($request['url'] == 'loginBind') {
                if(isset($request['id']) && $request['id'] !==''){
                    $openurl = "https://scptc.kedingdang.com/wxentry?wxtoken={$Weixinuser['openid']}&imghead={$Weixinuser['headimgurl']}&nickname={$Weixinuser['nickname']}&cid={$request['id']}&intourl=/loginBind";
                }else{
                    $openurl = "https://scptc.kedingdang.com/wxentry?wxtoken={$Weixinuser['openid']}&imghead={$Weixinuser['headimgurl']}&nickname={$Weixinuser['nickname']}&intourl=/loginBind";
                }
                header("location:{$openurl}");
                exit;
            }else  {
                header("location:/");
                exit;
            }
        } else {
            header("location:/");
            exit;
        }
    }


    function getWeixinToken()
    {
        $wxchatOne = $this->DataControl->getFieldOne("gmc_company_wxchatnumber","wxchatnumber_id,wxchatnumber_appid,wxchatnumber_appsecret"
            ,"company_id = '0' AND wxchatnumber_class = '1'");
        $tokenOne = $this->DataControl->getFieldOne("scptc_weixin_token", "token_failuretime,token_string"
            , "token_type = '1' and wxchatnumber_id = '{$wxchatOne['wxchatnumber_id']}'", "order by token_failuretime DESC limit 0,1");
        if ($tokenOne && $tokenOne['token_failuretime'] > time()) {
            $wxtoken = array();
            $wxtoken['access_token'] = $tokenOne['token_string'];
            $wxtoken['expires_in'] = 7200;
            return $wxtoken;
        } else {
            $paramarray = array(
                'appid' => $wxchatOne['wxchatnumber_appid'],
                'secret' => $wxchatOne['wxchatnumber_appsecret'],
                'grant_type' => "client_credential"
            );
            $getBakurl = request_by_curl("https://api.weixin.qq.com/cgi-bin/token", dataEncode($paramarray), "GET");
            $json_play = new \Webjson();
            $cardarray = $json_play->decode($getBakurl, "1");
            $data = array();
            $data['wxchatnumber_id'] = $wxchatOne['wxchatnumber_id'];
            $data['token_type'] = '1';
            $data['token_string'] = $cardarray['access_token'];
            $data['token_failuretime'] = time() + $cardarray['expires_in'];
            $this->DataControl->insertData("scptc_weixin_token", $data);
            return $cardarray;
        }
    }
    function setmenuView()
    {
        $data = '{
            "button": [
                {
                    "name": "校园通知",
                    "sub_button": [{
                            "type": "view",
                            "name": "教师通知",
                            "url": "https://api.kedingdang.com/DdzxChat/scpcinto?tourl=ClassNotice"
                        },
                        {
                            "type": "view",
                            "name": "学校公告",
                            "url": "https://api.kedingdang.com/DdzxChat/scpcinto?tourl=SchoolNotice"
                        }
                    ]
                },
                {
                    "name": "叮铛助学",
                    "type": "view",
                    "url": "https://api.kedingdang.com/DdzxChat/scpcinto?tourl=Home"
                },
                {
                    "name": "我的",
                    "sub_button": [
                        {
                            "type": "view",
                            "name": "学员中心",
                            "url": "https://api.kedingdang.com/DdzxChat/scpcinto?tourl=myCenter"
                        },
                        {
                            "type": "view",
                            "name": "我的成长",
                            "url": "https://api.kedingdang.com/DdzxChat/scpcinto?tourl=myGrowUp"
                        }
                    ]
                }
            ]
        }';
        $tokenArray = $this->getWeixinToken();
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, "https://api.weixin.qq.com/cgi-bin/menu/create?access_token={$tokenArray['access_token']}");
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE);
        curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (compatible; MSIE 5.01; Windows NT 5.0)');
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
        curl_setopt($ch, CURLOPT_AUTOREFERER, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        $tmpInfo = curl_exec($ch);
        if (curl_errno($ch)) {
            echo 'Errno' . curl_error($ch);
        }
        curl_close($ch);
        var_dump($tmpInfo);
    }
}
