<?php
/**
 * ============================================================================
 * 版权所有 : https://www.mohism.cn
 * 网站地址 : https://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/3/12
 * Time: 22:30
 */

namespace Work\Controller\Api;

class DuiBaController extends viewTpl
{
    public $data;
    public $AppKey = '3N4cR244g2h2B1Sk5ZjchWWJRWB';
    public $AppSecret = 'coNJuPFPT3nJpDcP9KEMq3iadc8';

    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
    }

    function loginView()
    {
        $request = Input('get.', '', 'trim,addslashes');

        $student_id = $this->DataControl->getFieldOne("smc_student", "student_id,student_loginnum,student_isnew,student_logintime", "student_branch = '{$request['branch']}'");
        //学生剩余积分
        $credits = $this->DataControl->getFieldOne("smc_student_virtual_property", "property_integralbalance", "student_id = '{$student_id['student_id']}'");
        if (!$credits) {
            $credits['property_integralbalance'] = '0';
        }

        $appKey = $this->AppKey;
        $appSecret = $this->AppSecret;
        $uid = $request['branch'];
        $credits = intval($credits['property_integralbalance']);
        $timestamp = time();
#$redirect="https://goods.m.duiba.com.cn/mobile/appItemDetail?appItemId=1422773";
#$transfer = urlencode("a=1&b=2");
#dcustom = urlencode("v=1&f=2");
#$params= array("uid"=>$uid,"credits"=>$credits,"redirect"=>$redirect,"transfer"=>$transfer,"dcustom"=>$dcustom);

        if($student_id['student_isnew'] == '1' && ($student_id['student_logintime'] == '0' || $timestamp - $student_id['student_logintime'] < 48*3600)){
            if($student_id['student_logintime'] == '0'){
                $data = array();
                $data['student_logintime'] = $timestamp;
                $this->DataControl->updateData("smc_student","student_id = '{$student_id['student_id']}'",$data);
            }
            $params = array("uid" => $uid, "credits" => $credits, "timestamp" => $timestamp, "vip" => 1);
            $url = "https://activity.m.duiba.com.cn/autoLogin/autologin?";
            $autourl = $this->buildUrlWithSign($url, $appKey, $appSecret, $params);
            $autourl = $autourl.'vip=1';
        }else{
            $params = array("uid" => $uid, "credits" => $credits, "timestamp" => $timestamp);
            $url = "https://activity.m.duiba.com.cn/autoLogin/autologin?";
            $autourl = $this->buildUrlWithSign($url, $appKey, $appSecret, $params);
        }

#echo $autourl;
        $this->DataControl->query("update smc_student set student_loginnum = student_loginnum+1,student_lastlogintime = '{$timestamp}' where student_id = '{$student_id['student_id']}'");
//        if($autourl && $student_id['student_loginnum'] == '0'){
//            $this->addStudentIntegral('8888', '0', $student_id['student_id'], '0', '1000', '首次登录加积分', '12357', '', '首次登录加积分', time(), '0');
//        }

        ajax_return(array('error' => 0, 'errortip' => "登录成功!", 'result' => $autourl));

    }

    function nologinView()
    {
        $request = Input('get.', '', 'trim,addslashes');

        $url = $request['dbredirect'];

        header("Location:$url");
    }

    /*
    *  签名验证,通过签名验证的才能认为是合法的请求
    */

    function buildUrlWithSign($url, $appKey, $appSecret, $params)
    {
        $timestamp = time() * 1000 . "";
        $params["appKey"] = $appKey;
        $params["timestamp"] = $timestamp;
        $params["appSecret"] = $appSecret;
        $sign = $this->sign($params);
        $params['sign'] = $sign;
        $url = $this->AssembleUrl($url, $params);
        return $url;
    }

    /*
    *构建URL
    */

    function sign($array)
    {
        ksort($array);
        $string = "";
        while (list($key, $val) = each($array)) {
            $string = $string . $val;
        }
        return md5($string);
    }

    /*
    *  构建积分商城请求链接的方式方法
    */

    function AssembleUrl($url, $array)
    {
        unset($array['appSecret']);
        foreach ($array as $key => $value) {
            $url = $url . $key . "=" . urlencode($value) . "&";
        }
        return $url;
    }

    /*
    *  积分消耗请求的解析方法
    *  当用户进行兑换时，兑吧会发起积分扣除请求，开发者收到请求后，可以通过此方法进行签名验证与解析，然后返回相应的格式
    *  返回格式为：
    *  {"status":"ok"，"credits":"10","bizId":"no123546","errorMessage":""}  或者
    *  {"status":"fail","credits":"10","errorMessage":"余额不足"}
    */

    function addStudentIntegral($company_id, $school_id, $student_id, $course_id, $integral = 0, $integrallog_rule, $staffer_id = 0, $playname = '', $note = '', $time = '', $class_id)
    {
        if (!$time || $time == '') {
            $time = time();
        }

        $stuIntOne = $this->DataControl->getFieldOne("smc_student_virtual_property", "property_integralbalance", "student_id='{$student_id}'");
        $stuIntOne['property_integralbalance'] = $stuIntOne['property_integralbalance'] ? $stuIntOne['property_integralbalance'] : 0;

        $integral_new = 0;
        $courseOne = $this->DataControl->getFieldOne("smc_course", "course_isintegral,course_maxintegral", "course_id='{$course_id}'");
        $oldOne = $this->DataControl->getFieldOne("smc_student_integrallog", "sum(integrallog_playamount) as old_integral", "integrallog_playclass='+' and student_id='{$student_id}' and course_id='{$course_id}'");

        if ($courseOne['course_isintegral'] == '1') {
            if ($oldOne['old_integral'] >= $courseOne['course_maxintegral']) {
                return false;
            }
            if ($oldOne['old_integral'] + $integral >= $courseOne['course_maxintegral']) {
                $integral_new = $courseOne['course_maxintegral'] - $oldOne['old_integral'];
            } else {
                $integral_new = $integral;
            }
        } else {
            $integral_new = $integral;
        }

        if ($integral_new <= 0) {
            return false;
        }

        //积分余额
        if ($this->DataControl->getFieldOne("smc_student_virtual_property", "property_integralbalance", "student_id='{$student_id}'")) {
            $data = array();
            $data['property_integralbalance'] = $stuIntOne['property_integralbalance'] + $integral_new;
            $this->DataControl->updateData("smc_student_virtual_property", "student_id='{$student_id}'", $data);
        } else {
            $data = array();
            $data['student_id'] = $student_id;
            $data['property_integralbalance'] = $integral_new;
            $this->DataControl->insertData("smc_student_virtual_property", $data);
        }

        $integrallog_data = array();
        $integrallog_data['student_id'] = $student_id;
        $integrallog_data['company_id'] = $company_id;
        $integrallog_data['school_id'] = $school_id;
        $integrallog_data['staffer_id'] = $staffer_id;
        $integrallog_data['course_id'] = $course_id;
        $integrallog_data['class_id'] = $class_id;

        $integrallog_data['integrallog_rule'] = $integrallog_rule;
        $integrallog_data['integrallog_playname'] = $playname ? $playname : '积分增加';
        $integrallog_data['integrallog_playclass'] = '+';
        $integrallog_data['integrallog_fromamount'] = $stuIntOne['property_integralbalance'];
        $integrallog_data['integrallog_playamount'] = $integral_new;
        $integrallog_data['integrallog_finalamount'] = $stuIntOne['property_integralbalance'] + $integral_new;

        $integrallog_data['integrallog_remark'] = $note;
        $integrallog_data['integrallog_reason'] = $integrallog_rule;
        $integrallog_data['integrallog_time'] = $time;
        $this->DataControl->insertData("smc_student_integrallog", $integrallog_data);

        return true;
    }

    /*
    *  兑换订单的结果通知请求的解析方法
    *  当兑换订单成功时，兑吧会发送请求通知开发者，兑换订单的结果为成功或者失败，如果为失败，开发者需要将积分返还给用户
    */

    function AddCreditsAction()
    {
        //构建积分商城加积分请求测试
        $requrl = $_SERVER['QUERY_STRING'];//解析请求url的参数封装成功数组对象
        $appKey = $this->AppKey;
        $appSecret = $this->AppSecret;
        $req_array = $this->buildRequestParams($requrl);
        $params = $this->parseAddCredits($appKey, $appSecret, $req_array);

        $sid = $this->DataControl->getFieldOne("smc_student", "student_id,company_id", "student_branch = '{$params['uid']}'");
        $integral = $this->DataControl->getFieldOne("smc_student_virtual_property", "property_integralbalance", "student_id = '{$sid['student_id']}'");

        if ($sid) {
            $data = array();
            $data['integralorder_status'] = '1';
            $data['integralorder_pid'] = $this->createOrderPid('DB');
            $data['integralorder_class'] = '0';
            $data['integralorder_credits'] = $params['credits'];
            $data['integralorder_createtime'] = $params['timestamp'];
            $data['integralorder_dbpid'] = $params['orderNum'];
            $data['student_branch'] = $params['uid'];
            $data['integralorder_remark'] = $params['description'] . '(' . $params['type'] . ')';
            $this->DataControl->insertData("smc_student_integralorder", $data);


            $property_data = array();
            $property_data['property_integralbalance'] = $integral['property_integralbalance'] + $params['credits'];
            $this->DataControl->updateData("smc_student_virtual_property", "student_id = '{$sid['student_id']}'", $property_data);

            $balancelog_data = array();
            $balancelog_data['student_id'] = $sid['student_id'];
            $balancelog_data['company_id'] = $sid['company_id'];
            $balancelog_data['integraltype_id'] = '1';
            $balancelog_data['integrallog_playname'] = $params['description'];
            $balancelog_data['integrallog_playclass'] = '+';
            $balancelog_data['integrallog_fromamount'] = $integral['property_integralbalance'];
            $balancelog_data['integrallog_playamount'] = $params['credits'];
            $balancelog_data['integrallog_finalamount'] = $integral['property_integralbalance'] + $params['credits'];
            $balancelog_data['integrallog_ip'] = real_ip();
            $balancelog_data['integrallog_reason'] = $params['description'] . '(' . $params['type'] . ')';
            $balancelog_data['integrallog_time'] = time();
            $this->DataControl->insertData("smc_student_integrallog", $balancelog_data);

            $status = 'ok';//状态根据自己逻辑返回
            $credits = $integral['property_integralbalance'] + $params['credits'];//此处填写用户剩余积分
            $errorMessage = '';
            $bizId = $data['integralorder_pid'];//此处开发者自己创建订单号，不要重复响应

        } else {
            $status = 'fail';//状态根据自己逻辑返回
            $errorMessage = '学生不存在';
            $credits = $integral['property_integralbalance'];//此处填写用户剩余积分
        }

        $result = array('credits' => $credits, 'status' => $status, 'bizId' => $bizId, 'errorMessage' => $errorMessage);

        print_r(json_encode($result));
    }

    /*
       *  虚拟商品充值请求的解析方法
       *  当用户兑换虚拟商品时，兑吧会发起虚拟商品充值请求，开发者收到请求后，可以通过此方法进行签名验证与解析，然后返回相应的格式
       *  返回格式为：
       *  成功：{"status":"success","credits":"10","supplierBizId":"no123546","errorMessage":""}
       *  处理中{"status":"process","credits":"10","supplierBizId":"no123546","errorMessage":""}
       * 失败：{"status":"fail","credits":"10","supplierBizId":"no123546","errorMessage":"余额不足"}
    */

    /**
     * @param $requrl
     * @return array
     * 通过解析请求的url，封装成功params对象
     */
    function buildRequestParams($requrl)
    {
        $arr = explode('&', $requrl);
        $params = array();
        foreach ($arr as $key => $val) {
            $arr = explode('=', $val);
            $params[$arr[0]] = urldecode($arr[1]);
        }
        return $params;
    }

    /*
       *  加积分请求的解析方法
        *  返回格式为：
        *  {"status":"ok"，"credits":"10","bizId":"no123546","errorMessage":""}  或者
        *  {"status":"fail","credits":"10","errorMessage":"余额不足"}
    */

    function parseAddCredits($appKey, $appSecret, $request_array)
    {
        if ($request_array["appKey"] != $appKey) {
            echo 'appKey not match';
        }
        if ($request_array["timestamp"] == null) {
            echo "timestamp can't be null";
        }
        $verify = $this->signVerify($appSecret, $request_array);
//        if(!$verify){
//            echo 'sign verify fail';
//        }
        $ret = $request_array;
        return $ret;
    }

    function signVerify($appSecret, $array)
    {
        $newarray = array();
        $newarray["appSecret"] = $appSecret;
        reset($array);
        while (list($key, $val) = each($array)) {
            if ($key != "sign") {
                $newarray[$key] = $val;
            }
        }
        $sign = $this->sign($newarray);
        if ($sign == $array["sign"]) {
            return true;
        }
        return false;
    }

    function createOrderPid($initial)
    {
        $Str = "ABCDEFGHIJKLMNOPQRSTUVWXYZ123456789";
        $rangtr = $Str[rand(0, 34)] . $Str[rand(0, 34)] . $Str[rand(0, 34)] . $Str[rand(0, 34)];
        $rangtime = date("ymdHis", time());
        $rangnum = rand(10000, 99999);
        $OrderPID = $initial . $rangtr . $rangtime . $rangnum;
        return $OrderPID;
    }

    //免登url

    function InvestAction()
    {
        //构建积分商城加积分请求测试
        $requrl = $_SERVER['QUERY_STRING'];//解析请求url的参数封装成功数组对象
        $appKey = $this->AppKey;
        $appSecret = $this->AppSecret;
        $req_array = $this->buildRequestParams($requrl);
        $params = $this->parseVitrual($appKey, $appSecret, $req_array);

        $sid = $this->DataControl->getFieldOne("smc_student", "student_id,company_id", "student_branch = '{$params['uid']}'");
        $integral = $this->DataControl->getFieldOne("smc_student_virtual_property", "property_integralbalance", "student_id = '{$sid['student_id']}'");

        if ($sid) {
            $data = array();
            $data['integralorder_status'] = '1';
            $data['integralorder_pid'] = $this->createOrderPid('DB');
            $data['integralorder_class'] = '0';
            $data['integralorder_credits'] = $params['params'];
            $data['integralorder_createtime'] = $params['timestamp'];
            $data['integralorder_dbpid'] = $params['orderNum'];
            $data['student_branch'] = $params['uid'];
            $data['integralorder_remark'] = $params['description'];
            $this->DataControl->insertData("smc_student_integralorder", $data);

            if ($integral) {
                $property_data = array();
                $property_data['property_integralbalance'] = $integral['property_integralbalance'] + $params['params'];
                $this->DataControl->updateData("smc_student_virtual_property", "student_id = '{$sid['student_id']}'", $property_data);
            } else {
                $integral['property_integralbalance'] = '0';
                $property_data = array();
                $property_data['property_integralbalance'] = $integral['property_integralbalance'] + $params['params'];
                $property_data['student_id'] = $sid['student_id'];
                $this->DataControl->insertData("smc_student_virtual_property", $property_data);
            }


            $balancelog_data = array();
            $balancelog_data['student_id'] = $sid['student_id'];
            $balancelog_data['company_id'] = $sid['company_id'];
            $balancelog_data['integraltype_id'] = '1';
            $balancelog_data['integrallog_playname'] = $params['description'];
            $balancelog_data['integrallog_playclass'] = '+';
            $balancelog_data['integrallog_fromamount'] = $integral['property_integralbalance'];
            $balancelog_data['integrallog_playamount'] = $params['params'];
            $balancelog_data['integrallog_finalamount'] = $integral['property_integralbalance'] + $params['params'];
            $balancelog_data['integrallog_ip'] = real_ip();
            $balancelog_data['integrallog_reason'] = $params['description'];
            $balancelog_data['integrallog_time'] = time();
            $this->DataControl->insertData("smc_student_integrallog", $balancelog_data);

            $status = 'ok';//状态根据自己逻辑返回
            $credits = $integral['property_integralbalance'] + $params['params'];//此处填写用户剩余积分
            $errorMessage = '';
            $bizId = $data['integralorder_pid'];//此处开发者自己创建订单号，不要重复响应

        } else {
            $status = 'fail';//状态根据自己逻辑返回
            $errorMessage = '学生不存在';
            $credits = $integral['property_integralbalance'];//此处填写用户剩余积分
        }

        $result = array('credits' => $credits, 'status' => $status, 'bizId' => $bizId, 'errorMessage' => $errorMessage);

        print_r(json_encode($result));
    }

    function parseVitrual($appKey, $appSecret, $request_array)
    {
        if ($request_array["appKey"] != $appKey) {
            echo 'appKey not match';
        }
        if ($request_array["timestamp"] == null) {
            echo "timestamp can't be null";
        }
        $verify = $this->signVerify($appSecret, $request_array);
//        if(!$verify){
//            echo 'sign verify fail';
//        }
        $ret = $request_array;
        return $ret;
    }

    function CreditsConsumeAction()
    {
        //构建积分商城扣积分请求测试
        $requrl = $_SERVER['QUERY_STRING'];
        $appKey = $this->AppKey;
        $appSecret = $this->AppSecret;
        $req_array = $this->buildRequestParams($requrl);
        $params = $this->parseCreditConsume($appKey, $appSecret, $req_array);

        if ($params) {

            $sid = $this->DataControl->getFieldOne("smc_student", "student_id,company_id", "student_branch = '{$params['uid']}'");
            $integral = $this->DataControl->getFieldOne("smc_student_virtual_property", "property_integralbalance", "student_id = '{$sid['student_id']}'");

            $data = array();
            $data['integralorder_pid'] = $this->createOrderPid('DB');
            $data['integralorder_class'] = '1';
            $data['integralorder_credits'] = $params['credits'];
            $data['integralorder_createtime'] = $params['timestamp'];
            $data['integralorder_dbpid'] = $params['orderNum'];
            $data['student_branch'] = $params['uid'];
            $data['integralorder_remark'] = $params['description'] . '(' . $params['type'] . ')' . $params['params'] . 'ip:' . $params['ip'];

            if ($integral['property_integralbalance'] > $params['credits']) {
                $status = 'ok';//状态根据自己逻辑返回
                $credits = $integral['property_integralbalance'] - $params['credits'];//此处填写用户剩余积分
                $errorMessage = '';
                $bizId = $data['integralorder_pid'];
                $data['integralorder_status'] = '1';
                $this->DataControl->insertData("smc_student_integralorder", $data);

                $property_data = array();
                $property_data['property_integralbalance'] = $credits;
                $this->DataControl->updateData("smc_student_virtual_property", "student_id = '{$sid['student_id']}'", $property_data);

                $balancelog_data = array();
                $balancelog_data['student_id'] = $sid['student_id'];
                $balancelog_data['company_id'] = $sid['company_id'];
                $balancelog_data['integraltype_id'] = '1';
                if ($params['type'] == 'hdtool') {
                    $balancelog_data['integrallog_playname'] = '幸运大抽奖';
                } else {
                    $balancelog_data['integrallog_playname'] = '兑吧兑换商品';
                }
                $balancelog_data['integrallog_playclass'] = '-';
                $balancelog_data['integrallog_fromamount'] = $integral['property_integralbalance'];
                $balancelog_data['integrallog_playamount'] = $params['credits'];
                $balancelog_data['integrallog_finalamount'] = $credits;
                $balancelog_data['integrallog_ip'] = real_ip();
                $balancelog_data['integrallog_reason'] = $params['description'] . '(' . $params['type'] . ')' . $params['params'] . 'ip:' . $params['ip'];
                $balancelog_data['integrallog_time'] = time();
                $this->DataControl->insertData("smc_student_integrallog", $balancelog_data);


            } else {
                $status = 'fail';//状态根据自己逻辑返回
                $credits = '0';
                $errorMessage = '积分不足！';
                $data['integralorder_note'] = '积分不足！';
                $data['integralorder_status'] = '0';

                $this->DataControl->insertData("smc_student_integralorder", $data);
            }
        }

        $result = array('credits' => $credits, 'status' => $status, 'bizId' => $bizId, 'errorMessage' => $errorMessage);

        print_r(json_encode($result));

    }

    function parseCreditConsume($appKey, $appSecret, $request_array)
    {
        if ($request_array["appKey"] != $appKey) {
            echo 'appKey not match';
        }
        if ($request_array["timestamp"] == null) {
            echo "timestamp can't be null";
        }
        $verify = $this->signVerify($appSecret, $request_array);
//        if(!$verify){
//            echo 'sign verify fail';
//        }
        $ret = $request_array;
        return $ret;
    }

    function ParseCreditNotifyView()
    {
        $requrl = $_SERVER['QUERY_STRING'];
        $appKey = $this->AppKey;
        $appSecret = $this->AppSecret;
        $req_array = $this->buildRequestParams($requrl);
        $params = $this->parseCreditNotify($appKey, $appSecret, $req_array);

        if ($params['success'] == 'true') {
            $data = array();
            $data['integralorder_fistatus'] = '1';
            $data['integralorder_note'] = $params['errorMessage'];
            $this->DataControl->updateData("smc_student_integralorder", "integralorder_pid = '{$params['bizId']}'", $data);
        } else {
            $data = array();
            $data['integralorder_fistatus'] = '-1';
            $data['integralorder_note'] = '取消兑换';
            $this->DataControl->updateData("smc_student_integralorder", "integralorder_pid = '{$params['bizId']}'", $data);

            $change = $this->DataControl->getFieldOne("smc_student_integralorder", "integralorder_credits,student_branch", "integralorder_pid = '{$params['bizId']}'");
            $sid = $this->DataControl->getFieldOne("smc_student", "student_id,company_id", "student_branch = '{$change['student_branch']}'");
            $integral = $this->DataControl->getFieldOne("smc_student_virtual_property", "property_integralbalance", "student_id = '{$sid['student_id']}'");

            $property_data = array();
            $property_data['property_integralbalance'] = $integral['property_integralbalance'] + $change['integralorder_credits'];
            $this->DataControl->updateData("smc_student_virtual_property", "student_id = '{$sid['student_id']}'", $property_data);

            $balancelog_data = array();
            $balancelog_data['student_id'] = $sid['student_id'];
            $balancelog_data['company_id'] = $sid['company_id'];
            $balancelog_data['integraltype_id'] = '3';
            $balancelog_data['integrallog_playname'] = '取消兑换';
            $balancelog_data['integrallog_playclass'] = '+';
            $balancelog_data['integrallog_fromamount'] = $integral['property_integralbalance'];
            $balancelog_data['integrallog_playamount'] = $change['integralorder_credits'];
            $balancelog_data['integrallog_finalamount'] = $integral['property_integralbalance'] + $change['integralorder_credits'];
            $balancelog_data['integrallog_reason'] = $params['errorMessage'];
            $balancelog_data['integrallog_ip'] = real_ip();
            $balancelog_data['integrallog_time'] = time();
            $this->DataControl->insertData("smc_student_integrallog", $balancelog_data);

        }

        print_r('ok');

    }



    //推荐有效名单

    function parseCreditNotify($appKey, $appSecret, $request_array)
    {
        if ($request_array["appKey"] != $appKey) {
            echo 'appKey not match';
        }
        if ($request_array["timestamp"] == null) {
            echo "timestamp can't be null";
        }
        $verify = $this->signVerify($appSecret, $request_array);
//        if(!$verify){
//            echo 'sign verify fail';
//        }
        $ret = $request_array;
        return $ret;
    }

    //推荐成为有效名单

    function listintegralAction()
    {
        $request = Input('post.', '', 'trim,addslashes');

        $param = array(
            'company_id' => $request['company_id'],
            'start_time' => $request['start_time'],
            'end_time' => $request['end_time']
        );
        $getBackurl = request_by_curl("http://crmapi.kcclassin.com/Api/getEffectiveClient", dataEncode($param), "GET");
        $List = json_decode($getBackurl, true);


//        var_dump($getBackurl);die;

        if ($List) {
            foreach ($List['result']['list'] as $val) {
                $data = array();
                $data['student_branch'] = $val['client_stubranch'];
                $data['client_id'] = $val['client_id'];
                $data['client_cnname'] = $val['client_cnname'];
                $data['student_branch'] = $val['client_stubranch'];
                if ($val['coursetype_id'] == '65') {
                    $data['listintegral_integral'] = '500';
                } elseif ($val['coursetype_id'] == '61') {
                    $data['listintegral_integral'] = '500';
                } elseif ($val['coursetype_id'] == '79655') {
                    $data['listintegral_integral'] = '600';
                } elseif ($val['coursetype_id'] == '79654') {
                    $data['listintegral_integral'] = '100';
                } elseif ($val['coursetype_id'] == '79660' || $val['coursetype_id'] == '79661') {
                    $data['listintegral_integral'] = '100';
                }
                $data['listintegral_day'] = $val['client_createtimedate'];
                $data['coursetype_id'] = $val['coursetype_id'];
                $data['listintegral_createtime'] = time();

                $this->DataControl->insertData("smc_student_listintegral", $data);
            }

            print_r('成功');

        } else {
            print_r('没有数据');

        }

    }

    //推荐名单到访

    function arriveintegralAction()
    {
        $request = Input('post.', '', 'trim,addslashes');

        $param = array(
            'company_id' => $request['company_id'],
            'start_time' => $request['start_time'],
            'end_time' => $request['end_time']
        );
        $getBackurl = request_by_curl("http://crmapi.kcclassin.com/Api/getEffectiveClientVisit", dataEncode($param), "GET");
        $List = json_decode($getBackurl, true);

        if ($List) {
            foreach ($List['result']['list'] as $val) {
                $data = array();
                $data['student_branch'] = $val['client_stubranch'];
                $data['client_id'] = $val['client_id'];
                $data['coursetype_id'] = $val['coursetype_id'];
                $data['client_cnname'] = $val['client_cnname'];
                $data['student_branch'] = $val['client_stubranch'];
                if ($val['coursetype_id'] == '65') {
                    $data['arriveintegral_integral'] = '1500';
                } elseif ($val['coursetype_id'] == '61') {
                    $data['arriveintegral_integral'] = '1500';
                } elseif ($val['coursetype_id'] == '79655') {
                    $data['arriveintegral_integral'] = '1600';
                } elseif ($val['coursetype_id'] == '79654') {
                    $data['arriveintegral_integral'] = '800';
                } elseif ($val['coursetype_id'] == '79660' || $val['coursetype_id'] == '79661') {
                    $data['arriveintegral_integral'] = '300';
                }
                $data['arriveintegral_day'] = $val['visittime'];
                $data['arriveintegral_createtime'] = time();

                $this->DataControl->insertData("smc_student_arriveintegral", $data);
            }

            print_r('成功');

        } else {
            print_r('没有数据');

        }

    }


    //推荐名单报名

    function bookintegralAction()
    {
        $request = Input('post.', '', 'trim,addslashes');

        $param = array(
            'company_id' => $request['company_id'],
            'start_time' => $request['start_time'],
            'end_time' => $request['end_time']
        );
        $getBackurl = request_by_curl("http://crmapi.kcclassin.com/Api/getEffectiveClientReg", dataEncode($param), "GET");
        $List = json_decode($getBackurl, true);

        if ($List) {
            foreach ($List['result']['list'] as $val) {
                $data = array();
                $data['student_branch'] = $val['client_stubranch'];
                $data['client_id'] = $val['client_id'];
                $data['coursetype_id'] = $val['coursetype_id'];
                $data['client_cnname'] = $val['client_cnname'];
                $data['student_branch'] = $val['client_stubranch'];
                if ($val['coursetype_id'] == '65') {
                    $data['bookintegral_integral'] = '10000';
                } elseif ($val['coursetype_id'] == '61') {
                    $data['bookintegral_integral'] = '10000';
                } elseif ($val['coursetype_id'] == '79655') {
                    $data['bookintegral_integral'] = '12000';
                } elseif ($val['coursetype_id'] == '79654') {
                    $data['bookintegral_integral'] = '8000';
                } elseif ($val['coursetype_id'] == '79660' || $val['coursetype_id'] == '79661') {
                    $data['bookintegral_integral'] = '5000';
                }
                $data['bookintegral_day'] = date('Y-m-d', $val['pay_firsttime']);
                $data['bookintegral_createtime'] = time();

                $this->DataControl->insertData("smc_student_bookintegral", $data);
            }

            print_r('成功');

        } else {
            print_r('没有数据');

        }

    }

    //学生续费积分奖励
    function renewintegralAction()
    {
        $request = Input('post.');

        $param = array(
            'start_time' => $request['start_time'],
            'end_time' => $request['end_time']
        );
        $getBackurl = request_by_curl("http://api.kcclassin.com/Smc/getRenewAmount", dataEncode($param), "GET");
        $List = json_decode($getBackurl, true);

//        var_dump($List);die();

        if ($List) {
            foreach ($List['result']['list'] as $val) {
                $data = array();
                $data['student_branch'] = $val['student_branch'];
                $data['school_id'] = $val['school_id'];
                $data['coursetype_id'] = $val['coursetype_id'];
                $data['coursecat_id'] = $val['coursecat_id'];
                $data['trading_pid'] = $val['trading_pid'];
                $data['renewintegral_price'] = $val['pay_amount'];
                $data['renewintegral_buytimes'] = $val['buy_times'];
                $data['renewintegral_lefttimes'] = $val['left_times'];
                if ($val['coursetype_id'] == '65') {
                    if ($val['left_times'] <= '4') {
                        $data['renewintegral_integral'] = '4000';
                    } elseif ($val['left_times'] > '4') {
                        $data['renewintegral_integral'] = '6000';
                    }
                } elseif ($val['coursetype_id'] == '79655') {
                    if ($val['left_times'] <= '10') {
                        $data['renewintegral_integral'] = '4000';
                    } elseif ($val['left_times'] > '10') {
                        $data['renewintegral_integral'] = '6000';
                    }
                } elseif ($val['coursetype_id'] == '61' || $val['coursetype_id'] == '79653  ' || $val['coursetype_id'] == '79654' || $val['coursetype_id'] == '79660' || $val['coursetype_id'] == '79661') {
                    $data['renewintegral_integral'] = $val['pay_amount'] * 2;
                }
                $data['renewintegral_day'] = date('Y-m-d', $val['order_createtime']);
                $data['renewintegral_createtime'] = time();

                $this->DataControl->insertData("smc_student_renewintegral", $data);
            }

            print_r('成功');

        } else {
            print_r('没有数据');

        }

    }

    //校务系统积分处理任务入口
    function smcIntegralIntoView(){

        $request = Input('post.');

        if ($request['type'] == '1') {
            //推荐新人奖励
            $list = $this->DataControl->selectClear("select l.*,s.student_id from smc_student_listintegral as l left join smc_student as s on l.student_branch = s.student_branch where l.listintegral_isaccount = '0' limit 0,1");
            if ($list) {
                foreach ($list as $val) {
                    $this->addStudentIntegral('8888', '0', $val['student_id'], '0', $val['listintegral_integral'], '推荐新ewqewq人奖励', '12357', '推荐新人奖励', '推荐的客户id' . $val['client_id'] . '为有效名单', time(), '0');
                    $data = array();
                    $data['listintegral_isaccount'] = '1';
                    $data['listintegral_accounttime'] = time();
                    $this->DataControl->updateData("smc_student_listintegral", "listintegral_id = '{$val['listintegral_id']}'", $data);
                    if($val['coursetype_id'] == '61' || $val['coursetype_id'] == '65'){
                        $this->sendCouponDynamic($val,$request['type']);
                    }
                }
            }
        } elseif ($request['type'] == '2') {
            //推荐到访体验奖励
            $list = $this->DataControl->selectClear("select l.*,s.student_id from smc_student_arriveintegral as l left join smc_student as s on l.student_branch = s.student_branch where l.arriveintegral_isaccount = '0' limit 0,1");
            if ($list) {
                foreach ($list as $val) {
                    $this->addStudentIntegral('8888', '0', $val['student_id'], '0', $val['arriveintegral_integral'], '推荐到访体验奖励', '12357', '推荐到访体验奖励', '推荐的客户id' . $val['client_id'] . '到访', time(), '0');
                    $data = array();
                    $data['arriveintegral_isaccount'] = '1';
                    $data['arriveintegral_accounttime'] = time();
                    $this->DataControl->updateData("smc_student_arriveintegral", "arriveintegral_id = '{$val['arriveintegral_id']}'", $data);
                    if($val['coursetype_id'] == '61' || $val['coursetype_id'] == '65'){
                        $this->sendCouponDynamic($val,$request['type']);
                    }
                }
            }
        } elseif ($request['type'] == '3') {
            //推荐报读课程奖励
            $list = $this->DataControl->selectClear("select l.*,s.student_id from smc_student_bookintegral as l left join smc_student as s on l.student_branch = s.student_branch where l.bookintegral_isaccount = '0' limit 0,1");
            if ($list) {
                foreach ($list as $val) {
                    $this->addStudentIntegral('8888', '0', $val['student_id'], '0', $val['bookintegral_integral'], '推荐报读课程奖励', '12357', '推荐报读课程奖励', '推荐的客户id' . $val['client_id'] . '报名', time(), '0');
                    $data = array();
                    $data['bookintegral_isaccount'] = '1';
                    $data['bookintegral_accounttime'] = time();
                    $this->DataControl->updateData("smc_student_bookintegral", "bookintegral_id = '{$val['bookintegral_id']}'", $data);
                    if($val['coursetype_id'] == '61' || $val['coursetype_id'] == '65'){
                        $this->sendCouponDynamic($val,$request['type']);
                    }
                }
            }
        } elseif ($request['type'] == '4') {
            //续费奖励
            $list = $this->DataControl->selectClear("select l.*,s.student_id from smc_student_renewintegral as l left join smc_student as s on l.student_branch = s.student_branch where l.renewintegral_isaccount = '0' limit 0,3000");
            if ($list) {
                foreach ($list as $val) {
                    $this->addStudentIntegral('8888', '0', $val['student_id'], '0', $val['renewintegral_integral'], '续费奖励', '12357', '续费奖励', '续费班组id' . $val['coursetype_id'], time(), '0');
                    $data = array();
                    $data['renewintegral_isaccount'] = '1';
                    $data['renewintegral_accounttime'] = time();
                    $this->DataControl->updateData("smc_student_renewintegral", "renewintegral_id = '{$val['renewintegral_id']}'", $data);
                }
            }
        }
    }


    function sendCouponDynamic($request,$dype){
        $sbranch = $this->DataControl->getFieldOne("smc_student","student_branch,student_cnname","student_id = '{$request['student_id']}'");

        if($dype == '1'){
            $title = "阅读有礼~".$sbranch['student_cnname']."堡贝推荐成功，恭喜你获得【iRC阅读打卡邀请奖励】<br/>"."<span style='color: red'>".$request['listintegral_integral']."</span>"."积分已存入您的账户<br/>点击海报前往奇趣商城兑换好礼～";
        }elseif($dype == '2'){
            $title = "阅读有礼~".$sbranch['student_cnname']."堡贝推荐成功，恭喜你获得【iRC阅读打卡邀请奖励】<br/>"."<span style='color: red'>".$request['arriveintegral_integral']."</span>"."积分已存入您的账户<br/>点击海报前往奇趣商城兑换好礼～";
        }elseif($dype == '3'){
            $title = "阅读有礼~".$sbranch['student_cnname']."堡贝推荐成功，恭喜你获得【iRC阅读打卡邀请奖励】<br/>"."<span style='color: red'>".$request['bookintegral_integral']."</span>"."积分已存入您的账户<br/>点击海报前往奇趣商城兑换好礼～";
        }


        $param = array(
            'title' => $title,
            'student_branch' => $sbranch['student_branch']
        );
        $getBackurl = request_by_curl("https://bptapi.chevady.cn/PersonalCenter/sendCouponDynamic", dataEncode($param), "POST");

        var_dump($getBackurl);
    }


    function stuIntegralQiquView(){
        $list = $this->DataControl->selectClear("select * from temp_integral where status = 0 limit 0,1000");
        if($list){
            foreach($list as &$value){
                $sid = $this->DataControl->getFieldOne("smc_student","student_id","student_branch = '{$value['student_branch']}'");
                $this->addStudentIntegral('8888', '0', $sid['student_id'], '0', $value['a'], '积分程序故障，统一补偿', '12357', '积分程序故障，统一补偿', '积分程序故障，统一补偿', time(), '0');
                $data = array();
                $data['status'] = '1';
                $this->DataControl->updateData("temp_integral","student_branch = '{$value['student_branch']}'",$data);
            }
        }


    }

    function integralTransformAction()
    {
        $request = Input('post.', '', 'trim,addslashes');

        //钻石金币转积分
        $sid = $this->DataControl->getFieldOne("smc_student","student_id","student_branch = '{$request['branch']}' and company_id = '8888'");
        if($sid){
            if ($request['coin'] > 0) {
                $this->addStudentIntegral('8888', '0', $sid['student_id'], '0', $request['coin'] / 10, '奇趣金币转积分', '12357', '', '奇趣金币转积分', time(), '0');
            }
            if ($request['diamond'] > 0) {
                $this->addStudentIntegral('8888', '0', $sid['student_id'], '0', $request['diamond'] * 10, '奇趣钻石转积分', '12357', '', '奇趣钻石转积分', time(), '0');
            }
        }

        ajax_return(array('error' => 0, 'errortip' => "结算成功!"));


    }

    function newbookAction (){
        $firstSeconds = strtotime(date("Y-m-d"));
        $lastSeconds = strtotime(date("Y-m-d 23:59:59"));

        $newbookArray = $this->DataControl->selectClear("select info_id,coursetype_id,student_id,pay_price,school_id from smc_student_registerinfo where info_status = '1' and coursetype_id in (61,65,79653,79654,79655) and pay_successtime >= {$firstSeconds} and pay_successtime <= {$lastSeconds} and company_id = '8888' and info_isintegral = '0' order by info_id DESC");

        if($newbookArray){
            foreach($newbookArray as $val){

                if ($val['coursetype_id'] == '79655') {
                    $this->addStudentIntegral('8888', $val['school_id'], $val['student_id'], '0', 5000, '首次报名安亲托管', '12357', '首次报名安亲托管', '首次报名安亲托管', time(), '0');
                }
                if ($val['coursetype_id'] == '65') {
                    $this->addStudentIntegral('8888', $val['school_id'], $val['student_id'], '0', 5000, '首次报名美语课程', '12357', '首次报名美语课程', '首次报名美语课程', time(), '0');
                }
                if ($val['coursetype_id'] == '61') {
                    $this->addStudentIntegral('8888', $val['school_id'], $val['student_id'], '0', $val['pay_price'], '首次报名阅读课程', '12357', '首次报名阅读课程', '首次报名阅读课程', time(), '0');
                }
                if ($val['coursetype_id'] == '79654') {
                    $this->addStudentIntegral('8888', $val['school_id'], $val['student_id'], '0', $val['pay_price'] * 2, '首次报名艺术美学', '12357', '首次报名艺术美学', '首次报名艺术美学', time(), '0');
                }
                if ($val['coursetype_id'] == '79653') {
                    $this->addStudentIntegral('8888', $val['school_id'], $val['student_id'], '0', $val['pay_price'] * 2, '首次报名小奇科学馆', '12357', '首次报名小奇科学馆', '首次报名小奇科学馆', time(), '0');
                }


                $data = array();
                $data['info_isintegral'] = '1';
                $this->DataControl->updateData("smc_student_registerinfo", "info_id = '{$val['info_id']}'", $data);
                $data = array();
                $data['student_isnew'] = '1';
                $data['student_logintime'] = '0';
                $this->DataControl->updateData("smc_student", "student_id = '{$val['student_id']}'", $data);
            }
        }

    }

    function addIntergralAction(){
        $request = Input('post.', '', 'trim,addslashes');
        $sid = $this->DataControl->getFieldOne("smc_student","student_id","student_branch = '{$request['student_branch']}'");
        $this->addStudentIntegral('8888', $request['school_id'], $sid['student_id'], '0', $request['intrgral'], '问卷调查', '12357', '问卷调查', '问卷调查', time(), '0');


    }

    function aView(){
        var_dump(real_ip());
    }


}
