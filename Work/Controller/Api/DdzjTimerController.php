<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/4/7
 * Time: 18:16
 */

namespace Work\Controller\Api;


class DdzjTimerController extends viewTpl {

    public $data;
    public $wxuser;

    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
        exit;
    }

    //学员电访设置待沟通提醒后，到设置的提醒当天早上8：00推送
    function TePaCommuView(){
        if(date("H") >= 8 && date("H") <= 20){
            $Day = date("Y-m-d");
            $trackOne = $this->DataControl->selectOne("
            SELECT
                s.staffer_id,
                s.company_id,
                count( track_id ) AS a
            FROM
                smc_student_track AS c
                LEFT JOIN smc_staffer AS s ON s.staffer_id = c.staffer_id
            WHERE
                c.track_followutime = '{$Day}'
                AND s.staffer_id NOT IN ( SELECT l.staffer_id FROM eas_wxsend_log AS l WHERE l.log_day = '{$Day}' AND l.log_type = 'TePaCommu' )
                AND s.staffer_wxtoken IS NOT NULL
            GROUP BY
                s.staffer_id
            ORDER BY
                rand( )
                limit 0,1
	");
            $staffer_cnname = $this->DataControl->getFieldOne("smc_staffer","staffer_cnname","staffer_id = '{$trackOne['staffer_id']}'");
            if($trackOne){
                $isset = $this->DataControl->getFieldOne("gmc_company_masterplate","masterplate_wxid","company_id = '{$trackOne['company_id']}' and masterplate_name = '家校沟通提醒' and masterplate_class = '0'");
                if($isset){
                    $wxid = $isset['masterplate_wxid'];
                }else{
                    $masterplate = $this->DataControl->getFieldOne("gmc_company_masterplate","masterplate_wxid","company_id = '0' and masterplate_name = '家校沟通提醒' and masterplate_class = '0'");
                    $wxid = $masterplate['masterplate_wxid'];
                }

                $a = $staffer_cnname['staffer_cnname'].'老师您好，您今天有'.$trackOne['a'].'个待沟通学员，请及时跟进哦~';
                $b = '学员待沟通提醒';
                $c = date('m月d日 H:i', time());
                $d = '点击这里查看待沟通详情';
                $e = "https://tesc.kedingdang.com/stuManage/stumanageClass";
                $wxteModel = new \Model\Api\ZjwxChatModel($trackOne['staffer_id']);
                $wxteModel->TePaCommu($a,$b,$c,$d,$e,$wxid);
                ajax_return(array('error' => 0, 'errortip' => "微信通知发送成功!"));

            }else{
                exit(0);
            }
        }else{
            exit(0);
        }
    }

    //老师发布作业后24小时内未读的学生即推送（晚上八点后不推送，早上八点再推送）
    function HomeworkRemindView(){
        if(date("H") >= 8 && date("H") <= 20){
            $Day = date("Y-m-d");
            $trackOne = $this->DataControl->selectOne("
            SELECT
                
            FROM
                eas_homework AS c
                LEFT JOIN smc_student AS s ON s.student_id = c.student_id
            WHERE
                c.catitrack_followutime = '{$Day}'
                AND s.staffer_id NOT IN ( SELECT l.staffer_id FROM eas_wxsend_log AS l WHERE l.log_day = '{$Day}' AND l.log_type = 'TePaCommu' )
            GROUP BY
                s.staffer_id
            ORDER BY
                rand( )
                limit 0,1
          ");
            $staffer_cnname = $this->DataControl->getFieldOne("smc_staffer","staffer_cnname","staffer_id = '{$trackOne['staffer_id']}'");
            if($trackOne){
                $parenter_id = $this->DataControl->selectOne("select p.parenter_cnname,p.parenter_id from smc_student_family as f left join smc_parenter as p on f.parenter_id = p.parenter_id WHERE f.student_id  = '{$val['student_id']}'");
                $staffer_cnnname = $this->DataControl->getFieldOne("smc_staffer", "staffer_cnname,company_id", "staffer_id = '{$request['staffer_id']}'");
                $status = $this->DataControl->getFieldOne("eas_homeworkstu", "homeworkstu_status,homeworkstu_sendtime", "homework_id = '{$homework_id}' and student_id");
                if ($status['homeworkstu_sendtime'] == '0') {
                    $isSubmit = '0';
                } else {
                    $isSubmit = '1';
                }

                $isset = $this->DataControl->getFieldOne("gmc_company_masterplate","masterplate_wxid","company_id = '{$staffer_cnnname['company_id']}' and masterplate_name = '作业提醒'");
                if($isset){
                    $wxid = $isset['masterplate_wxid'];
                }else{
                    $masterplate = $this->DataControl->getFieldOne("gmc_company_masterplate","masterplate_wxid","company_id = '0' and masterplate_name = '作业提醒'");
                    $wxid = $masterplate['masterplate_wxid'];
                }

                $a = $staffer_cnnname['staffer_cnname'] . '教师布置新的作业了，请及时完成哦~';
                $b = $val['class_cnname'];
                $c = $request['homework_title'];
                $d = $this->cutSubstr($request['homework_content'], 70);
                $e = '点击这里查看作业详情';
                $f = "https://scptc.kedingdang.com/HomeWork/JobDetails?homework_id={$homework_id}&homeworkstu_status={$status['homeworkstu_status']}&isSubmit={$isSubmit}";
                $wxteModel = new \Model\Api\ZxwxChatModel($parenter_id['parenter_id'], $val['student_id']);
                $wxteModel->SendHomework($a, $b, $c, $d, $e, $f, $wxid);
            }else{
                exit(0);
            }
        }else{
            exit(0);
        }
    }


}