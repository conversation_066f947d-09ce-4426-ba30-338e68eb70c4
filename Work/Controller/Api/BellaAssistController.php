<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/5/27
 * Time: 11:56
 */

namespace Work\Controller\Api;


use Work\Controller\Wwwapi\LoginController;

class BellaAssistController  extends viewTpl{
    public $u;
    public $t;
    public $c;
    public $visitType = "api";


    //预加载处理类1234的方法
    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
        $this->api = $this->router->getApi();
    }

    //处理 合并校  CRM意向客户名单 从 A 校 到 B 校  ( 不包括 tmk 负责中的 名单 )
    function handleSchoolYxClientView(){
        echo "学校端的  意向名单处理方式，已处理";
        //世茂都 到 鼎正
        //https://api.kedingdang.com/BellaAssist/handleSchoolYxClient?comId=8888&fromSid=1128&toSid=1169
        die;
        $request = Input('get.', '', 'trim,addslashes');
        $comId = $request['comId']?$request['comId']:1001;//8888
        $schoolId = $request['fromSid']?$request['fromSid']:919;//世茂都1128
        $fromSid = $request['fromSid']?$request['fromSid']:919;
        $toSid = $request['toSid']?$request['toSid']:926;
        $mkId = $comId=='8888'?15:13;
        $fromSchoolOne = $this->DataControl->selectOne("select school_shortname from smc_school where school_id = '{$fromSid}' and company_id = '{$comId}' ");
        $toSchoolOne = $this->DataControl->selectOne("select school_shortname from smc_school where school_id = '{$toSid}' and company_id = '{$comId}' ");
        $fromSname = $fromSchoolOne['school_shortname'];
        $toSname = $toSchoolOne['school_shortname'];

        $tmkClientIdStr = 0;

        //排除 TMK 的数据
        $tmkSql = "select c.client_id,c.client_cnname,c.client_mobile,c.channel_id,c.client_cnname,s.school_id,s.is_gmctocrmschool,s.is_gmcdirectschool  
                    ,(select 1 from crm_client_principal as x WHERE c.client_id = x.client_id and x.principal_ismajor = '1' and x.principal_leave = '0' and school_id = '0') as tmkone  
                from  crm_client as c
                Left JOIN  crm_client_principal as  p ON p.client_id = c.client_id
                Left JOIN  crm_marketer as mk ON mk.marketer_id = p.marketer_id
                Left JOIN  crm_client_schoolenter as s  ON s.client_id=c.client_id and p.school_id=s.school_id and s.is_enterstatus = '1'
                left join crm_code_channel as ch on ch.channel_id = c.channel_id 
                where c.company_id ='{$comId}' and c.client_isgross = '0' and c.client_tracestatus > '-1'  and  c.client_tracestatus <> 4 
                and p.school_id='{$schoolId}' and p.principal_leave = 0   
                -- and (s.is_gmctocrmschool = '1' or s.is_gmcdirectschool = '1') 
                group by c.client_id 
                Having tmkone = 1   
                ";
        $tmkClient = $this->DataControl->selectClear($tmkSql);
        if($tmkClient) {
            $tmkClientIdArr = array_column($tmkClient, 'client_id');
            $tmkClientIdStr = "'".implode("','",$tmkClientIdArr)."'";
        }
//print_r($tmkClientIdStr);

        //学校的意向客户
        if($tmkClientIdStr !== 0) {
            $yxWhere = " and c.client_id not in ({$tmkClientIdStr}) ";
        }
        $yxSql = " select c.client_id,c.channel_id,c.client_cnname,c.client_mobile,s.school_id,s.is_gmctocrmschool,s.is_gmcdirectschool 
                ,(SELECT 1 from crm_client_invite as x WHERE c.client_id = x.client_id and x.invite_isvisit = '0' limit 0,1) as inhave
                ,(SELECT 1 from crm_client_audition as x WHERE c.client_id = x.client_id and x.audition_isvisit = '0' limit 0,1) as auhave 
            from  crm_client as c
            Left JOIN  crm_client_principal as  p ON p.client_id = c.client_id
            Left JOIN  crm_marketer as mk ON mk.marketer_id = p.marketer_id
            Left JOIN  crm_client_schoolenter as s  ON s.client_id=c.client_id and p.school_id=s.school_id and s.is_enterstatus = '1'
            left join crm_code_channel as ch on ch.channel_id = c.channel_id 
            where c.company_id ='{$comId}' and c.client_isgross = '0' and c.client_tracestatus > '-1'  and  c.client_tracestatus <> 4 
            and p.school_id='{$schoolId}' and p.principal_leave = 0 
            {$yxWhere}
            -- and c.client_id = 500176
            group by c.client_id  
            ORDER BY c.client_tracestatus ASC,c.client_intention_level,c.client_updatetime DESC  
            limit 0,50
            ";
//        echo $yxSql;die;
        $yxClient = $this->DataControl->selectClear($yxSql);

//        print_r($yxClient);
//        die;

        if($yxClient){
            foreach ($yxClient as $yxClientVadr) {
                //存在 柜询未确认的 全部未到访
                if ($yxClientVadr['inhave'] == '1') {
                    $inSome = $this->DataControl->selectClear(" select invite_id,school_id,marketer_id,client_id from crm_client_invite where client_id = '{$yxClientVadr['client_id']}' and invite_isvisit = '0' ");
                    if ($inSome) {
                        foreach ($inSome as $inSomeVar) {
                            $somedata = array();
                            $somedata['invite_id'] = $inSomeVar['invite_id'];
                            $somedata['invite_isvisit'] = -1;
                            $somedata['invite_type'] = 0;
                            $somedata['invite_novisitreason'] = "并校（程序批量处理）";
                            $somedata['school_id'] = $schoolId;
                            $somedata['company_id'] = $comId;
                            $somedata['marketer_id'] = $mkId;
                            $Model = new \Model\Crm\ClientinviteModel($somedata);
                            $Model->setIsVisit($somedata);
                        }
                    }
                }
                //存在 试听未确认的 全部未试听
                if ($yxClientVadr['auhave'] == '1') {
                    $inSome = $this->DataControl->selectClear(" select audition_id,school_id,marketer_id,client_id from crm_client_audition where client_id = '{$yxClientVadr['client_id']}' and audition_isvisit = '0' ");
                    if ($inSome) {
                        foreach ($inSome as $inSomeVar) {
                            $somedata = array();
                            $somedata['audition_id'] = $inSomeVar['audition_id'];
                            $somedata['audition_isvisit'] = -1;
                            $somedata['audition_type'] = 0;
                            $somedata['audition_novisitreason'] = "并校（程序批量处理）";
                            $somedata['school_id'] = $schoolId;
                            $somedata['company_id'] = $comId;
                            $somedata['marketer_id'] = $mkId;
                            $Model = new \Model\Crm\ClientinviteModel($somedata);
                            $Model->setIsaudition($somedata);
                        }
                    }
                }
                //解除 负责人  --- 用的转为招生有效名单程序
                $somedata = array();
                $somedata['client_id'][0] = $yxClientVadr['client_id'];
                $somedata['type'] = 1;
                $somedata['school_id'] = $schoolId;
                $somedata['company_id'] = $comId;
                $somedata['marketer_id'] = $mkId;
                $Model = new \Model\Crm\IntentionClientModel($somedata);
                $bool = $Model->changeIntentionClientStatus($somedata);

                //重新分配 学校  1、解除已分配的学校  2、分配到对应的学校
                $this->addSchoolEnter($yxClientVadr['client_id'], $toSid,$comId,$fromSname,$toSname);

                echo "已处理【名单:{$yxClientVadr['client_id']},{$yxClientVadr['client_cnname']},{$yxClientVadr['client_mobile']};from:{$fromSid};to:{$toSid}】";
            }
        }

    }

    //处理 合并校  CRM意向客户名单 从 A 校 到 B 校  ( 专门处理包括 tmk 负责中的 名单 )
    function handleTmkSchoolYxClientView(){
        echo "学校端的  tmk 负责中的 意向名单处理方式，已处理";
        //世茂都 到 鼎正
        //https://api.kedingdang.com/BellaAssist/handleTmkSchoolYxClient?comId=8888&fromSid=1128&toSid=1169
        die;
        $request = Input('get.', '', 'trim,addslashes');
        $comId = $request['comId']?$request['comId']:1001;//8888
        $schoolId = $request['fromSid']?$request['fromSid']:919;//世茂都1128
        $fromSid = $request['fromSid']?$request['fromSid']:919;
        $toSid = $request['toSid']?$request['toSid']:926;
        $mkId = $comId=='8888'?15:13;
        $fromSchoolOne = $this->DataControl->selectOne("select school_shortname from smc_school where school_id = '{$fromSid}' and company_id = '{$comId}' ");
        $toSchoolOne = $this->DataControl->selectOne("select school_shortname from smc_school where school_id = '{$toSid}' and company_id = '{$comId}' ");
        $fromSname = $fromSchoolOne['school_shortname'];
        $toSname = $toSchoolOne['school_shortname'];

        $tmkClientIdStr = 0;

        //排除 TMK 的数据
        $tmkSql = "select c.client_id,c.client_cnname,c.client_mobile,c.channel_id,c.client_cnname,s.school_id,s.is_gmctocrmschool,s.is_gmcdirectschool  
                    ,(select 1 from crm_client_principal as x WHERE c.client_id = x.client_id and x.principal_ismajor = '1' and x.principal_leave = '0' and school_id = '0') as tmkone  
                from  crm_client as c
                Left JOIN  crm_client_principal as  p ON p.client_id = c.client_id
                Left JOIN  crm_marketer as mk ON mk.marketer_id = p.marketer_id
                Left JOIN  crm_client_schoolenter as s  ON s.client_id=c.client_id and p.school_id=s.school_id and s.is_enterstatus = '1'
                left join crm_code_channel as ch on ch.channel_id = c.channel_id 
                where c.company_id ='{$comId}' and c.client_isgross = '0' and c.client_tracestatus > '-1'  and  c.client_tracestatus <> 4 
                and p.school_id='{$schoolId}' and p.principal_leave = 0   
                -- and (s.is_gmctocrmschool = '1' or s.is_gmcdirectschool = '1') 
                group by c.client_id 
                Having tmkone = 1   
                ";
        $tmkClient = $this->DataControl->selectClear($tmkSql);
        if($tmkClient) {
            $tmkClientIdArr = array_column($tmkClient, 'client_id');
            $tmkClientIdStr = "'".implode("','",$tmkClientIdArr)."'";
        }
//print_r($tmkClientIdStr);

        //学校的意向客户
        if($tmkClientIdStr !== 0) {
            $yxWhere = " and c.client_id in ({$tmkClientIdStr}) ";
        }
        $yxSql = " select c.client_id,c.channel_id,c.client_cnname,c.client_mobile,s.school_id,s.is_gmctocrmschool,s.is_gmcdirectschool 
                ,(SELECT 1 from crm_client_invite as x WHERE c.client_id = x.client_id and x.invite_isvisit = '0' limit 0,1) as inhave
                ,(SELECT 1 from crm_client_audition as x WHERE c.client_id = x.client_id and x.audition_isvisit = '0' limit 0,1) as auhave 
            from  crm_client as c
            Left JOIN  crm_client_principal as  p ON p.client_id = c.client_id
            Left JOIN  crm_marketer as mk ON mk.marketer_id = p.marketer_id
            Left JOIN  crm_client_schoolenter as s  ON s.client_id=c.client_id and p.school_id=s.school_id and s.is_enterstatus = '1'
            left join crm_code_channel as ch on ch.channel_id = c.channel_id 
            where c.company_id ='{$comId}' and c.client_isgross = '0' and c.client_tracestatus > '-1'  and  c.client_tracestatus <> 4 
            and p.school_id='{$schoolId}' and p.principal_leave = 0 
            {$yxWhere}
            -- and c.client_id = 495366
            group by c.client_id  
            ORDER BY c.client_tracestatus ASC,c.client_intention_level,c.client_updatetime DESC  
            limit 0,50
            ";
//        echo $yxSql;die;
        $yxClient = $this->DataControl->selectClear($yxSql);

//        print_r($yxClient);
//        die;

        if($yxClient){
            foreach ($yxClient as $yxClientVadr) {
                //存在 柜询未确认的 全部未到访
                if ($yxClientVadr['inhave'] == '1') {
                    $inSome = $this->DataControl->selectClear(" select invite_id,school_id,marketer_id,client_id from crm_client_invite where client_id = '{$yxClientVadr['client_id']}' and invite_isvisit = '0' ");
                    if ($inSome) {
                        foreach ($inSome as $inSomeVar) {
                            $somedata = array();
                            $somedata['invite_id'] = $inSomeVar['invite_id'];
                            $somedata['invite_isvisit'] = -1;
                            $somedata['invite_type'] = 0;
                            $somedata['invite_novisitreason'] = "并校（程序批量处理）";
                            $somedata['school_id'] = $schoolId;
                            $somedata['company_id'] = $comId;
                            $somedata['marketer_id'] = $mkId;
                            $Model = new \Model\Crm\ClientinviteModel($somedata);
                            $Model->setIsVisit($somedata);
                        }
                    }
                }
                //存在 试听未确认的 全部未试听
                if ($yxClientVadr['auhave'] == '1') {
                    $inSome = $this->DataControl->selectClear(" select audition_id,school_id,marketer_id,client_id from crm_client_audition where client_id = '{$yxClientVadr['client_id']}' and audition_isvisit = '0' ");
                    if ($inSome) {
                        foreach ($inSome as $inSomeVar) {
                            $somedata = array();
                            $somedata['audition_id'] = $inSomeVar['audition_id'];
                            $somedata['audition_isvisit'] = -1;
                            $somedata['audition_type'] = 0;
                            $somedata['audition_novisitreason'] = "并校（程序批量处理）";
                            $somedata['school_id'] = $schoolId;
                            $somedata['company_id'] = $comId;
                            $somedata['marketer_id'] = $mkId;
                            $Model = new \Model\Crm\ClientinviteModel($somedata);
                            $Model->setIsaudition($somedata);
                        }
                    }
                }
//                echo '111';
//                die;
                //解除 负责人  --- 用的转为招生有效名单程序
                $somedata = array();
                $somedata['client_id'][0] = $yxClientVadr['client_id'];
                $somedata['type'] = 1;
                $somedata['school_id'] = $schoolId;
                $somedata['company_id'] = $comId;
                $somedata['marketer_id'] = $mkId;
                $Model = new \Model\Crm\IntentionClientModel($somedata);
                $bool = $Model->changeIntentionClientStatus($somedata);

                //重新分配 学校  1、解除已分配的学校  2、分配到对应的学校
                $this->addSchoolEnter($yxClientVadr['client_id'], $toSid,$comId,$fromSname,$toSname,'tmk');

                echo "已处理【名单:{$yxClientVadr['client_id']},{$yxClientVadr['client_cnname']},{$yxClientVadr['client_mobile']};from:{$fromSid};to:{$toSid}】";
            }
        }

    }



    //处理 CRM 名单错误数据
    function updateEscClientOneApi(){
        echo "已处理 这一种情况";

        exit("这个改的不全对，下边不能服用，需要 改  学校、负责人、名单状态");//没有负责人 名单状态应该为 0 未分配
        die;
        $sql = "SELECT a.client_id,a.client_tracestatus,group_concat( distinct( b.school_id ) ) as sids ,count(distinct( b.school_id )) as bb
        ,(select group_concat(x.school_id) from crm_client_principal as x WHERE a.client_id = x.client_id and x.principal_ismajor = 1 and x.school_id > 1 and x.principal_leave = 0) as msid
        ,(select x.school_id from crm_client_track as x WHERE a.client_id = x.client_id and x.school_id > 1 ORDER BY x.track_createtime desc limit 0,1) as trackSchId 
        ,(
            select se.school_id as school_id_in
            FROM smc_student_enrolled as se
            left join smc_student as s on se.student_id=s.student_id
            left join smc_student_family as sf on sf.student_id=s.student_id 
            left join smc_school as h on h.school_id = se.school_id
            where sf.family_isdefault=1 AND sf.family_mobile = a.client_mobile  and h.company_id = '8888' 
            order by field(se.enrolled_status,'1','0','2','3','-1',NULL),s.student_id ASC 
            limit 0,1
        ) as smcSchID
        , (
            select se.school_id as school_id_in
            FROM smc_student  as s
            left join smc_student_enrolled as se on se.student_id=s.student_id  
            left join smc_school as h on h.school_id = se.school_id
            where  h.company_id = '8888' and a.client_id = s.from_client_id  
            order by field(se.enrolled_status,'1','0','2','3','-1',NULL),s.student_id ASC 
            limit 0,1	
        ) as yyyy
        FROM crm_client as a,crm_client_schoolenter as b 
        where a.company_id = '8888' and a.client_id = b.client_id and b.is_enterstatus = '1' 
        and a.client_id in (54143,55278,55842,56409,56413,56416,56439,56454,56482,56890,57328,57463,58413,59170,59352,59391,59484,60222,60244,60673,60984,61343,61584,61719,62105,62555,63084,63424,64032,64482,65144,66176,66244,66451,66479,67737,67890,67993,68353,68393,68420,68473,68508,68509,68576,68631,68925,69398,69470,69517,69633,69718,70595,70762,70837,70876,70981,71087,72115,72472,72529,73053,73761,73928,73985,74055,74079,74088,74200,74293,74892,74893,75089,75469,75904,76233,76234,76236,76237,76238,76239,76240,76241,76242,76243,76244,76245,76247,76248,76249,76250,76251,76252,76253,76254,76255,76256,76257,76258,76259,76260,76261,76262,76263,76264,76265,76266,76267,76268,76269,76270,76271,76272,76273,76274,76275,76276,76277,76278,76279,76280,76281,76282,76283,76343,76344,76937,76983,76984,77055,77233,77305,77322,77333,77342,77345,77346,77348,77351,77353,77373,77387,77399,77401,77403,77404,77406,77407,77408,77410,77411,77414,78593,78690,78904,78911,78989,79223,79228,79230,79231,79238,79239,79241,79242,79244,79245,79246,79249,79283,79546,79877,80056,80721,80750,80778,80900,81058,81100,81148,81245,81353,81447,81626,81630,81636,81648,81728,81754,81776,81789,81793,81847,82012,82057,82171,82208,82299,82597,82618,82625,82792,83666,83750,84005,84054,84345,84415,84447,84452,84462,84476,84517,84541,84612,84613,84615,84629,84660,84665,84704,84777,84859,85077,85407,85467,85673,85772,85779,85842,85961,86104,86195,86446,86503,86546,86708,86790,86996,87017,87061,87094,87162,87309,87725,87789,87845,87953,87990,88060,88083,88631,88723,89086,89344,89443,89489,90555,93154,93312,98102,106802,106804,106808,106814,106839,106933,106948,108137,109922,110033,127637,140911,144468,177579,185964,198415,210895,223043,231047,258440,258464,258602,259723,260030,260447,266094,266133,266645,266822,266972,266983,268647,268648,268987,269132,269546,269549,269605,270249,270360,270752,270780,270781,270788,270923,272503,273693,274374,274391,274503,275025,275125,275519,275868,276721,277095,277783,278144,278146,278749,279520,279646,279978,280129,280549,281058,281245,281943,282881,283325,285785,286565,286635,286864,286914,286978,287788,288719,288942,289524,289584,290221,290222,290656,290974,291268,291701,291720,291845,291997,292206,292263,292418,292443,292572,292753,292813,293274,293381,293864,294256,294279,294605,296228,296588,296798,296857,296859,297130,297196,297225,297596,298212,298587,298971,298999,299014,299020,299045,299084,299439,299480,300765,301434,301634,302177,305022,305240,305371,306098,306660,306677,306780,306812,307437,307849,308326,309846,309901,309960,309969,310048,310123,310241,310709,311533,312613,312679,313166,313425,313618,313679,314156,314294,315620,315721,316339,316371,316424,317386,317408,317490,317565,317773,317781,317798,317990,318101,319613,320298,320489,320723,320863,321517,321807,322500,322875,324327,324346,324402,325279,326115,326150,326152,326582,326626,326972,329657,342422,343632,344400,344806,344818,344829,345160,345605,345978,346670,346685,347455,349049,351177,351844,389611,399417,399560,400343,402795,406617,413580,414283,416844,424393,425117,426318,430350,433842,442725,452865,458392,462644,470246,471538,471600,472138,473179,476427,476623,491190,492799,494715,496805,503928,504646,514106,519246,524672,526404,530557,533174,576402,578271,578651,578797,600180,602893,614650,625099,634692,642777,655829,680279,681533,698400,707044,716855,722245,737558,745384,758216,771254,772915,777970,785664,787933,788491,790716,791996,798815,800081,803547,834014,841289,848304,868473,877546,879246,885965,889221,936861,939027,940704,941871,960302,967770,991475,1002052,1004629,1013011,1021281,1031779,1031924,1038156,1047562,1073131,1099812,1133177,1146519,1167244,1170584,1189238,1192392,1225137,1226353,1266462,1276608,1284495,1285054,1285384,1287143,1319908,1334051,1358267,1373529,1389129,1410209,1446776,1459179,1476723,1488228,1508542,1509914,1525292,1529195,1539921)
        GROUP BY a.client_id 
        HAVING bb > 1";
        $clientList = $this->DataControl->selectClear($sql);
//        print_r($clientList);
//        die;
        $aa = 0;
        if($clientList){
            foreach ($clientList as $clientVar){

                // 无意向、无效 多个学校，无负责人学校，取 最后一次跟踪记录的 学校ID
                if($clientVar['client_tracestatus']<0 && $clientVar['msid'] == ''){
//                    $aa++;
//                    print_r($clientVar);
//                    die;
//                    if(strpos($clientVar['sids'],$clientVar['trackSchId']) !== false){
//                        $schoolenter = array();
//                        $schoolenter['is_enterstatus'] = '-1';
//                        $schoolenter['schoolenter_updatetime'] = time();
//                        $this->DataControl->updateData("crm_client_schoolenter", " client_id='{$clientVar['client_id']}' and school_id > 0 and school_id <> '{$clientVar['trackSchId']}' ", $schoolenter);
////                        die;
//                    }
                }

                // 无意向、无效 多个学校，无负责人学校，取 最后一次跟踪记录的 学校ID
                if($clientVar['client_tracestatus']>= '0' &&$clientVar['client_tracestatus'] < '4' && $clientVar['msid'] == ''){
//                    $aa++;
//                    print_r($clientVar);
//                    die;
//                    if(strpos($clientVar['sids'],$clientVar['trackSchId']) !== false){
//                        $schoolenter = array();
//                        $schoolenter['is_enterstatus'] = '-1';
//                        $schoolenter['schoolenter_updatetime'] = time();
//                        $this->DataControl->updateData("crm_client_schoolenter", " client_id='{$clientVar['client_id']}' and school_id > 0 and school_id <> '{$clientVar['trackSchId']}' ", $schoolenter);
////                        die;
//                    }
                }

                // 无意向、无效 多个学校，无负责人学校，取 最后一次跟踪记录的 学校ID
                if($clientVar['client_tracestatus']>= '0' &&$clientVar['client_tracestatus'] < '4' && $clientVar['msid'] != ''){
//                    $aa++;
//                    print_r($clientVar);
////                    die;
//                    if(strpos($clientVar['sids'],$clientVar['trackSchId']) !== false){
//                        $schoolenter = array();
//                        $schoolenter['is_enterstatus'] = '-1';
//                        $schoolenter['schoolenter_updatetime'] = time();
//                        $this->DataControl->updateData("crm_client_schoolenter", " client_id='{$clientVar['client_id']}' and school_id <> '{$clientVar['trackSchId']}' ", $schoolenter);
////                        die;
//                    }
                }

                // 已转正  负责人和转正学校一致
                if($clientVar['client_tracestatus']== '4' && $clientVar['msid'] == $clientVar['smcSchID']){
//                    $aa++;
////                    print_r($clientVar);
////                    die;
//                    if(strpos($clientVar['sids'],$clientVar['smcSchID']) !== false){
//                        $schoolenter = array();
//                        $schoolenter['is_enterstatus'] = '-1';
//                        $schoolenter['schoolenter_updatetime'] = time();
//                        $this->DataControl->updateData("crm_client_schoolenter", " client_id='{$clientVar['client_id']}' and school_id <> '{$clientVar['smcSchID']}' ", $schoolenter);
////                        die;
//                    }
////                    die;
                }

                // 已转正  负责人和转正学校不一致
                if($clientVar['client_tracestatus']== '4' && $clientVar['smcSchID'] == $clientVar['yyyy']  &&  $clientVar['msid'] <> $clientVar['smcSchID'] and $clientVar['smcSchID'] != ''){
                    $aa++;
//                    print_r($clientVar);
//                    die;
//                    if(strpos($clientVar['sids'],$clientVar['smcSchID']) !== false){
//                        $schoolenter = array();
//                        $schoolenter['is_enterstatus'] = '-1';
//                        $schoolenter['schoolenter_updatetime'] = time();
//                        $this->DataControl->updateData("crm_client_schoolenter", " client_id='{$clientVar['client_id']}' and school_id <> '{$clientVar['smcSchID']}' ", $schoolenter);
//                    }else{
//                        $datas = array();
//                        $datas['client_id'] = $clientVar['client_id'];
//                        $datas['school_id'] = $clientVar['smcSchID'];
//                        $datas['company_id'] = '8888';
//                        $datas['schoolenter_createtime'] = time();
//                        $datas['schoolenter_updatetime'] = time();
//                        $this->DataControl->insertData('crm_client_schoolenter', $datas);
//
//                        $schoolenter = array();
//                        $schoolenter['is_enterstatus'] = '-1';
//                        $schoolenter['schoolenter_updatetime'] = time();
//                        $this->DataControl->updateData("crm_client_schoolenter", " client_id='{$clientVar['client_id']}' and school_id <> '{$clientVar['smcSchID']}' ", $schoolenter);
//                    }
//                    die;
                }

                // 已转正  负责人和转正学校不一致
                if($clientVar['client_tracestatus']== '4' && $clientVar['yyyy'] == '' and $clientVar['smcSchID'] == '' && $clientVar['msid'] > 1 ){
//                    $aa++;
////                    print_r($clientVar);
////                    die;
//                    if(strpos($clientVar['sids'],$clientVar['msid']) !== false){
//                        $schoolenter = array();
//                        $schoolenter['is_enterstatus'] = '-1';
//                        $schoolenter['schoolenter_updatetime'] = time();
//                        $this->DataControl->updateData("crm_client_schoolenter", " client_id='{$clientVar['client_id']}' and school_id <> '{$clientVar['msid']}' ", $schoolenter);
//                    }else{
//                        $datas = array();
//                        $datas['client_id'] = $clientVar['client_id'];
//                        $datas['school_id'] = $clientVar['msid'];
//                        $datas['company_id'] = '8888';
//                        $datas['schoolenter_createtime'] = time();
//                        $datas['schoolenter_updatetime'] = time();
//                        $this->DataControl->insertData('crm_client_schoolenter', $datas);
//
//                        $schoolenter = array();
//                        $schoolenter['is_enterstatus'] = '-1';
//                        $schoolenter['schoolenter_updatetime'] = time();
//                        $this->DataControl->updateData("crm_client_schoolenter", " client_id='{$clientVar['client_id']}' and school_id <> '{$clientVar['msid']}' ", $schoolenter);
//                    }
//                    die;
                }


                // 已转正  负责人和转正学校不一致 又一种
                if($clientVar['client_tracestatus']== '4' && $clientVar['yyyy'] > '1' and $clientVar['smcSchID'] == '' && $clientVar['msid'] > 1 ){
                    $aa++;
//                    print_r($clientVar);
////                    die;
//                    if(strpos($clientVar['sids'],$clientVar['yyyy']) !== false){
//                        $schoolenter = array();
//                        $schoolenter['is_enterstatus'] = '-1';
//                        $schoolenter['schoolenter_updatetime'] = time();
//                        $this->DataControl->updateData("crm_client_schoolenter", " client_id='{$clientVar['client_id']}' and school_id <> '{$clientVar['yyyy']}' ", $schoolenter);
//                    }else{
//                        $datas = array();
//                        $datas['client_id'] = $clientVar['client_id'];
//                        $datas['school_id'] = $clientVar['yyyy'];
//                        $datas['company_id'] = '8888';
//                        $datas['schoolenter_createtime'] = time();
//                        $datas['schoolenter_updatetime'] = time();
//                        $this->DataControl->insertData('crm_client_schoolenter', $datas);
//
//                        $schoolenter = array();
//                        $schoolenter['is_enterstatus'] = '-1';
//                        $schoolenter['schoolenter_updatetime'] = time();
//                        $this->DataControl->updateData("crm_client_schoolenter", " client_id='{$clientVar['client_id']}' and school_id <> '{$clientVar['yyyy']}' ", $schoolenter);
//                    }
//
//                    //解除负责人
//                    if($clientVar['msid']  <> $clientVar['yyyy']) {
//                        //解除单校负责人
//                        $principal_data = array();
//                        $principal_data['principal_leave'] = 1;
//                        $principal_data['principal_updatatime'] = time();
//                        $this->DataControl->updateData("crm_client_principal", "client_id='{$clientVar['client_id']}' AND school_id  <> '0'", $principal_data);
//                    }
                }


                // 已转正   又一种
                if($clientVar['client_tracestatus']== '4' && $clientVar['yyyy'] > '1' && $clientVar['msid'] > 1 ){
                    $aa++;
//                    print_r($clientVar);
////                    die;
//                    if(strpos($clientVar['sids'],$clientVar['yyyy']) !== false){
//                        $schoolenter = array();
//                        $schoolenter['is_enterstatus'] = '-1';
//                        $schoolenter['schoolenter_updatetime'] = time();
//                        $this->DataControl->updateData("crm_client_schoolenter", " client_id='{$clientVar['client_id']}' and school_id <> '{$clientVar['yyyy']}' ", $schoolenter);
//                    }else{
//                        $datas = array();
//                        $datas['client_id'] = $clientVar['client_id'];
//                        $datas['school_id'] = $clientVar['yyyy'];
//                        $datas['company_id'] = '8888';
//                        $datas['schoolenter_createtime'] = time();
//                        $datas['schoolenter_updatetime'] = time();
//                        $this->DataControl->insertData('crm_client_schoolenter', $datas);
//
//                        $schoolenter = array();
//                        $schoolenter['is_enterstatus'] = '-1';
//                        $schoolenter['schoolenter_updatetime'] = time();
//                        $this->DataControl->updateData("crm_client_schoolenter", " client_id='{$clientVar['client_id']}' and school_id <> '{$clientVar['yyyy']}' ", $schoolenter);
//                    }
//
//                    //解除负责人
//                    if($clientVar['msid']  <> $clientVar['yyyy']) {
//                        //解除单校负责人
//                        $principal_data = array();
//                        $principal_data['principal_leave'] = 1;
//                        $principal_data['principal_updatatime'] = time();
//                        $this->DataControl->updateData("crm_client_principal", "client_id='{$clientVar['client_id']}' AND school_id  <> '0'", $principal_data);
//                    }
                }

            }
        }
        echo $aa;die;
    }

    //处理 CRM 名单错误数据
    function updateEscClientTwoApi(){
        echo "已处理 这一种情况";
        die;
        $sql = "SELECT a.client_id,a.client_tracestatus,group_concat( distinct( b.school_id ) ) as sids ,count(distinct( b.school_id )) as bb
            ,(select group_concat(x.school_id) from crm_client_schoolenter as x WHERE a.client_id = x.client_id and x.is_enterstatus = 1 and x.school_id > 1 ) as schsid 
            FROM crm_client as a,crm_client_principal as b 
            where a.company_id = '8888' and a.client_id = b.client_id and b.principal_ismajor = '1' and b.school_id > '1' and b.principal_leave = '0' 
            and a.client_id in (147985,402650,489212,800798,940277,1048616,1106426,1343844,1361865)
            GROUP BY a.client_id 
            HAVING bb > 1";
        $clientList = $this->DataControl->selectClear($sql);
        print_r($clientList);
        die;
        if($clientList){
            foreach ($clientList as $clientVar){

                if($clientVar['client_tracestatus']>=0 && $clientVar['schsid'] != ''){
//print_r($clientVar);
//die;
                    if(strpos($clientVar['schsid'],",") === false){
                        $schoolenter = array();
                        $schoolenter['principal_leave'] = '1';
                        $schoolenter['principal_updatatime'] = time();
                        $this->DataControl->updateData("crm_client_principal", " client_id='{$clientVar['client_id']}' and school_id > 0 and school_id <> '{$clientVar['schsid']}' ", $schoolenter);
                    }
//die;
                }
            }
        }
    }

    //处理 CRM 名单错误数据
    function updateEscClientThreeApi(){
        echo "已处理 这一种情况";
        die;
        $sql = "SELECT 
            a.client_id,a.client_tracestatus
            ,(select distinct x.school_id from crm_client_schoolenter as x WHERE a.client_id = x.client_id and x.is_enterstatus = 1) as s1
            ,(select distinct x.school_id as aa from crm_client_principal as x WHERE a.client_id = x.client_id and x.principal_ismajor = 1 and x.school_id > 1 and x.principal_leave = 0) as s2
            , (
                    select se.school_id as school_id_in
                    FROM smc_student  as s
                    left join smc_student_enrolled as se on se.student_id=s.student_id  
                    left join smc_school as h on h.school_id = se.school_id
                    where  h.company_id = '8888' and a.client_id = s.from_client_id  
                    order by field(se.enrolled_status,'1','0','2','3','-1',NULL),s.student_id ASC 
                    limit 0,1	
                ) as yyyy
            FROM crm_client as a 
            WHERE a.company_id = '8888'  
            and ( a.client_id in (54143,55278,55842,56409,56413,56416,56439,56454,56482,56890,57328,57463,58413,59170,59352,59391,59484,60222,60244,60673,60984,61343,61584,61719,62105,62555,63084,63424,64032,64482,65144,66176,66244,66451,66479,67737,67890,67993,68353,68393,68420,68473,68508,68509,68576,68631,68925,69398,69470,69517,69633,69718,70595,70762,70837,70876,70981,71087,72115,72472,72529,73053,73761,73928,73985,74055,74079,74088,74200,74293,74892,74893,75089,75469,75904,76233,76234,76236,76237,76238,76239,76240,76241,76242,76243,76244,76245,76247,76248,76249,76250,76251,76252,76253,76254,76255,76256,76257,76258,76259,76260,76261,76262,76263,76264,76265,76266,76267,76268,76269,76270,76271,76272,76273,76274,76275,76276,76277,76278,76279,76280,76281,76282,76283,76343,76344,76937,76983,76984,77055,77233,77305,77322,77333,77342,77345,77346,77348,77351,77353,77373,77387,77399,77401,77403,77404,77406,77407,77408,77410,77411,77414,78593,78690,78904,78911,78989,79223,79228,79230,79231,79238,79239,79241,79242,79244,79245,79246,79249,79283,79546,79877,80056,80721,80750,80778,80900,81058,81100,81148,81245,81353,81447,81626,81630,81636,81648,81728,81754,81776,81789,81793,81847,82012,82057,82171,82208,82299,82597,82618,82625,82792,83666,83750,84005,84054,84345,84415,84447,84452,84462,84476,84517,84541,84612,84613,84615,84629,84660,84665,84704,84777,84859,85077,85407,85467,85673,85772,85779,85842,85961,86104,86195,86446,86503,86546,86708,86790,86996,87017,87061,87094,87162,87309,87725,87789,87845,87953,87990,88060,88083,88631,88723,89086,89344,89443,89489,90555,93154,93312,98102,106802,106804,106808,106814,106839,106933,106948,108137,109922,110033,127637,140911,144468,177579,185964,198415,210895,223043,231047,258440,258464,258602,259723,260030,260447,266094,266133,266645,266822,266972,266983,268647,268648,268987,269132,269546,269549,269605,270249,270360,270752,270780,270781,270788,270923,272503,273693,274374,274391,274503,275025,275125,275519,275868,276721,277095,277783,278144,278146,278749,279520,279646,279978,280129,280549,281058,281245,281943,282881,283325,285785,286565,286635,286864,286914,286978,287788,288719,288942,289524,289584,290221,290222,290656,290974,291268,291701,291720,291845,291997,292206,292263,292418,292443,292572,292753,292813,293274,293381,293864,294256,294279,294605,296228,296588,296798,296857,296859,297130,297196,297225,297596,298212,298587,298971,298999,299014,299020,299045,299084,299439,299480,300765,301434,301634,302177,305022,305240,305371,306098,306660,306677,306780,306812,307437,307849,308326,309846,309901,309960,309969,310048,310123,310241,310709,311533,312613,312679,313166,313425,313618,313679,314156,314294,315620,315721,316339,316371,316424,317386,317408,317490,317565,317773,317781,317798,317990,318101,319613,320298,320489,320723,320863,321517,321807,322500,322875,324327,324346,324402,325279,326115,326150,326152,326582,326626,326972,329657,342422,343632,344400,344806,344818,344829,345160,345605,345978,346670,346685,347455,349049,351177,351844,389611,399417,399560,400343,402795,406617,413580,414283,416844,424393,425117,426318,430350,433842,442725,452865,458392,462644,470246,471538,471600,472138,473179,476427,476623,491190,492799,494715,496805,503928,504646,514106,519246,524672,526404,530557,533174,576402,578271,578651,578797,600180,602893,614650,625099,634692,642777,655829,680279,681533,698400,707044,716855,722245,737558,745384,758216,771254,772915,777970,785664,787933,788491,790716,791996,798815,800081,803547,834014,841289,848304,868473,877546,879246,885965,889221,936861,939027,940704,941871,960302,967770,991475,1002052,1004629,1013011,1021281,1031779,1031924,1038156,1047562,1073131,1099812,1133177,1146519,1167244,1170584,1189238,1192392,1225137,1226353,1266462,1276608,1284495,1285054,1285384,1287143,1319908,1334051,1358267,1373529,1389129,1410209,1446776,1459179,1476723,1488228,1508542,1509914,1525292,1529195,1539921)
            or a.client_id in (71057,73977,81873,106273,306302,390374,424234,455229,485970,572751,573153,582071,687599,741955))
            HAVING s1<>s2  and s1 = yyyy
            -- HAVING s2 > 1
            -- and client_id = '270604' 
            ";
        $clientList = $this->DataControl->selectClear($sql);
//        print_r($clientList);
//        die;
        $aa = 0;
        if($clientList){
            foreach ($clientList as $clientVar){
                // 已转正  负责人和转正学校不一致 又一种
                if($clientVar['client_tracestatus']== '4' && $clientVar['s1'] <> $clientVar['s2'] && $clientVar['s1'] == $clientVar['yyyy'] ){
//                    $aa++;
//                    print_r($clientVar);
////                    die;
////                    if(strpos($clientVar['sids'],$clientVar['yyyy']) !== false){
////                        $schoolenter = array();
////                        $schoolenter['is_enterstatus'] = '-1';
////                        $schoolenter['schoolenter_updatetime'] = time();
////                        $this->DataControl->updateData("crm_client_schoolenter", " client_id='{$clientVar['client_id']}' and school_id <> '{$clientVar['yyyy']}' ", $schoolenter);
////                    }else{
////                        $datas = array();
////                        $datas['client_id'] = $clientVar['client_id'];
////                        $datas['school_id'] = $clientVar['yyyy'];
////                        $datas['company_id'] = '8888';
////                        $datas['schoolenter_createtime'] = time();
////                        $datas['schoolenter_updatetime'] = time();
////                        $this->DataControl->insertData('crm_client_schoolenter', $datas);
////
////                        $schoolenter = array();
////                        $schoolenter['is_enterstatus'] = '-1';
////                        $schoolenter['schoolenter_updatetime'] = time();
////                        $this->DataControl->updateData("crm_client_schoolenter", " client_id='{$clientVar['client_id']}' and school_id <> '{$clientVar['yyyy']}' ", $schoolenter);
////                    }
////
//                    //解除负责人
//                    if($clientVar['s2']  <> $clientVar['yyyy']) {
//                        //解除单校负责人
//                        $principal_data = array();
//                        $principal_data['principal_leave'] = 1;
//                        $principal_data['principal_updatatime'] = time();
//                        $this->DataControl->updateData("crm_client_principal", "client_id='{$clientVar['client_id']}' AND school_id  <> '0' and school_id  <> '{$clientVar['yyyy']}'  ", $principal_data);
//
//                        //解除单校负责人
//                        $c_data = array();
//                        $c_data['client_distributionstatus'] = 0;
//                        $this->DataControl->updateData("crm_client", "client_id='{$clientVar['client_id']}' ", $c_data);
//                    }
                }

            }
        }
        echo $aa;die;
    }




    //极简互联 外呼 测试
    function testJjhlOneView(){
        echo '屏蔽';
        die;
        $request = Input('get.', '', 'trim,addslashes');
        $Model = new \Model\Crm\PhoneOutboundModel();
        $Model->getJjhlDualCal($request,'13764506328','13866877391');
        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $Model->result), $request['language_type']);
    }
    //极简互联 外呼 -- 查询外显号列表接口
    function testJjhlTwoView(){
        echo '屏蔽';
        die;
        $request = Input('get.', '', 'trim,addslashes');
        $Model = new \Model\Crm\PhoneOutboundModel();
        $Model->getJjhlNumbersUse($request);
        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $Model->result), $request['language_type']);
    }
    //极简互联 外呼 -- 查询外显号码组
    function testJjhlThreeView(){
        echo '屏蔽';
        die;
        $request = Input('get.', '', 'trim,addslashes');
        $Model = new \Model\Crm\PhoneOutboundModel();
        $Model->getJjhlNumbersGroup($request);
        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $Model->result), $request['language_type']);
    }
    //极简互联 外呼 测试 -- 坐席
    function testJjhlFourView(){
        echo '屏蔽';
        die;
        $request = Input('get.', '', 'trim,addslashes');
        $Model = new \Model\Crm\PhoneOutboundModel();
        $Model->getJjhlCallout($request,'17717880971','6003','18703629223');
        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $Model->result), $request['language_type']);
    }

    //重新适配学校
    function addSchoolEnter($client_id,$school_id,$company_id,$fromSname,$toSname,$isTmk)
    {
        if($school_id > 0){
            $exitNowschool = false;
            $schoolenterList = $this->DataControl->selectClear("select school_id from crm_client_schoolenter where client_id='{$client_id}'");//20250402 有做修改
            if($schoolenterList){
                $this->DataControl->updateData("crm_client_schoolenter", "client_id='{$client_id}' and school_id<>'{$school_id}'", array("is_enterstatus" => '-1',"schoolenter_updatetime" => time()));
                foreach ($schoolenterList as $schoolenterOne){
                    if(isset($schoolenterOne['school_id']) && $schoolenterOne['school_id'] == $school_id){
                        $schoolenter_array = array();
                        $schoolenter_array['is_enterstatus'] = '1';
                        if($isTmk == 'tmk'){
                            $schoolenter_array['is_gmctocrmschool'] = '1';
                        }
                        $schoolenter_array['schoolenter_updatetime'] = time();
                        $this->DataControl->updateData("crm_client_schoolenter", "client_id='{$client_id}' and school_id='{$school_id}'", $schoolenter_array);
                        $exitNowschool = true;
                    }
                }
            }

            if(!$exitNowschool){
                $schoolenter_array = array();
                $schoolenter_array['client_id'] = $client_id;
                $schoolenter_array['school_id'] = $school_id;
                $schoolenter_array['company_id'] = $company_id;
                $schoolenter_array['is_enterstatus'] = '1';
                if($isTmk == 'tmk'){
                    $schoolenter_array['is_gmctocrmschool'] = '1';
                }
                $schoolenter_array['schoolenter_updatetime'] = time();
                $schoolenter_array['schoolenter_createtime'] = time();
                $this->DataControl->insertData("crm_client_schoolenter", $schoolenter_array);
            }

            $trackData = array();
            $trackData['client_id'] = $client_id;
            $trackData['marketer_id'] = '0';
            $trackData['school_id'] = 0;
            $trackData['marketer_name'] = '并校操作';
            $trackData['track_validinc'] = 1;
            $trackData['track_linktype'] = '并校操作';
            $trackData['track_note'] = "并校操作：从{$fromSname}到{$toSname}";
            $trackData['track_createtime'] = time();
            $trackData['track_type'] = 1;
            $trackData['track_initiative'] = 1;
            $this->DataControl->insertData('crm_client_track', $trackData);
        }
    }

    //处理人像数据
    function chulirenxiangshujvView(){
        die;
        $some = $this->DataControl->selectClear(" 
            select g.stuportrait_id,g.stuportrait_faceimg,g.stuportrait_isquality,g.stuportrait_isqualitytwo  
            from gmc_machine_stuportrait as g 
            where g.stuportrait_isquality <> g.stuportrait_isqualitytwo 
            and g.stuportrait_isqualitytwo <> 0 
            and g.company_id = '8888'
            -- and g.stuportrait_isquality = '0' 
            and g.stuportrait_isgmc <> 1 
            and g.student_id > 0 
        ");
        if($some){

        }

    }

    //爱普云测试
    function bangApyUserView(){
        echo '屏蔽';
        die;
        $request = Input('get.', '', 'trim,addslashes');
        $Model = new \Model\Crm\PhoneOutboundModel();
        $Model->bangApyUser($request);


//        $Model = new \Model\Crm\PhoneOutboundModel();
//        $Model->getHuijieDualCall($paramArray,$this->marketerOne['marketer_mobile'], $clientOne['client_mobile'], $paramArray['numbertype'], $seatsOne['seats_account'],$schOne['school_city']);

        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $Model->result), $request['language_type']);
    }


    //给名单发短信
    function fdxQujiangFurongView(){
        echo "已经过时间了，不发送";
        die;
        $some = $this->DataControl->selectClear("select * from temp_fadunxin where temp_isto = '0' and temp_id > '28318' limit 0,5 ");
        if($some){
            foreach ($some as $somevar){
                $tilte = "吉的堡曲江龙湖校盛大开幕";
$contxt = "吉的堡曲江龙湖校盛大开幕
→沉浸式哪吒亲子闯关
→当日低至7折开幕价
→手机等福利抽奖
3月8日（明日）11:00开场 曲江龙湖天街LG西侧不见不散
拒收请回复R";
                // //短信接口不能用
                $Controller = new LoginController();
                $Controller->Sendmisgo($somevar['temp_mobile'], $contxt, $tilte, '吉的堡曲江龙湖校盛大开幕', 8888);

                $updata = array();
                $updata['temp_isto'] = 1;
                $updata['temp_time'] = time();
                $this->DataControl->updateData("temp_fadunxin"," temp_id = '{$somevar['temp_id']}' ",$updata);
            }
            echo '本次跑完';
        }else{
            echo '已发完';
        }


    }


    //20241111 处理大众点评渠道名单的创建时间是 0 的数据
    function upDpingCreattimeView(){
        echo '已处理';die;
        $somedata = $this->DataControl->selectClear(" SELECT a.client_id,a.client_cnname,a.client_mobile 
                FROM crm_client as a 
                WHERE a.company_id = '8888' and a.channel_id = '101' and a.client_createtime = '0'
                order by a.client_id ASC 
                limit 0,20 ");// and a.client_mobile = '19979179601'
//        print_r($somedata);die;
        if($somedata){
            foreach ($somedata as $somevar){
                $crTime = 0;
                $leadOne = $this->DataControl->selectOne("select dpingnewleads_json from gmc_company_dpingnewleads where dpingnewleads_client_status = '1' and dpingnewleads_json like '%{$somevar['client_mobile']}%' order by dpingnewleads_id ASC");
                if($leadOne){
                    $leadarr = json_decode($leadOne['dpingnewleads_json'],true);
                    if($leadarr['lastLeadsAddTime']){
                        $crTime = ceil($leadarr['lastLeadsAddTime']/1000);
                    }elseif($leadarr['leads_time']){
                        $crTime = strtotime($leadarr['leads_time']);
                    }
                }else{
                    $orderOne = $this->DataControl->selectOne("select dpingorder_buy_time from gmc_company_dpingorder where dpingorder_client_status = '1' and dpingorder_mobile = '{$somevar['client_mobile']}' order by dpingorder_id ASC");
                    if($orderOne){
                        $crTime = $orderOne['dpingorder_buy_time'];
                    }
                }

                if($crTime > 0 && $somevar['client_id'] > 0){
                    $this->DataControl->updateData('crm_client',"client_id = '{$somevar['client_id']}'",array("client_createtime" => $crTime));
                    echo "本次已处理【{$somevar['client_mobile']}】";
                }else{
                    echo "本次失败【{$somevar['client_mobile']}】";
                }
            }
        }else{
            echo '暂无要处理的名单数据';
        }
    }

    //处理台湾名单系统旧名单，导入到课叮铛系统
    function addTwRosterToKddClientView(){
        echo "现有的已处理";
        die;
        $rosterSome = $this->DataControl->selectClear("select * from linshi_twroster where issync = '0' order by cram_reservation_id ASC limit 0,20 ");

        print_r($rosterSome);die;
        $nowYear = date("Y",time());
        if($rosterSome){
            $succ = 0;
            $fail = 0;
            foreach ($rosterSome as $rosterVar){

                $rosterVar['company_id'] = 79081;

                //学校是否存在
                $schoolOne = $this->DataControl->selectOne("select * from smc_school where company_id = '{$rosterVar['company_id']}' and school_shortname = '{$rosterVar['school_name']}' ");
//                print_r($schoolOne);die;
                if(!$schoolOne){
                    //标记名单已处理
                    $data = array();
                    $data['issync'] = 1;
                    $data['errortip'] = "学校不存在";
                    $this->DataControl->updateData("linshi_twroster", "cram_reservation_id = '{$rosterVar['cram_reservation_id']}' and parent_phone = '{$rosterVar['parent_phone']}' ", $data);

                    $fail++;
                    continue;
                }
                //名单是否存在
                $rosterVar['student_name'] = $rosterVar['student_name']?$rosterVar['student_name']:'未填寫';
                $clientOne = $this->DataControl->selectOne("select 1 from crm_client where company_id = '{$rosterVar['company_id']}' and client_mobile = '{$rosterVar['parent_phone']}' and client_cnname = '{$rosterVar['student_name']}' ");
//                print_r($clientOne);die;
                if($clientOne){
                    //标记名单已处理
                    $data = array();
                    $data['issync'] = 1;
                    $data['errortip'] = "名单已存在";
                    $this->DataControl->updateData("linshi_twroster", "cram_reservation_id = '{$rosterVar['cram_reservation_id']}' and parent_phone = '{$rosterVar['parent_phone']}' ", $data);

                    $fail++;
                    continue;
                }
                //查询渠道
                $channelOne = $this->DataControl->selectOne(" 
                        SELECT c.channel_medianame,c.channel_id  
                        FROM linshi_twroster as a 
                        LEFT JOIN linshi_twchannel as b ON a.source_name = b.name  
                        left join crm_code_channel as c ON b.channel = c.channel_name 
                        WHERE a.cram_reservation_id = '{$rosterVar['cram_reservation_id']}' and a.source_name is not null
                ");


                //插入新名单
                $addOne = array();
                $addOne['company_id'] = $rosterVar['company_id'];
                $addOne['client_cnname'] = $rosterVar['student_name']?$rosterVar['student_name']:'未填寫';
                $addOne['client_mobile'] = $rosterVar['parent_phone'];
                $addOne['client_email'] = $rosterVar['parent_email'];

                if($channelOne){
                    $addOne['client_source'] = $channelOne['channel_medianame'];
                    $addOne['channel_id'] = $channelOne['channel_id'];
                }else{
                    $addOne['client_source'] = '網站';
                    $addOne['channel_id'] = '622';
                }

                $addOne['client_age'] = $rosterVar['student_year'];
                //当年龄传入日期
                if ($rosterVar['student_year']) {
                    $nowYear = date("Y",time());
                    $createdYear = date("Y",strtotime($rosterVar['created_at']));
                    $birthadyYear = $nowYear - ($nowYear-$createdYear) - $rosterVar['student_year'] +1;
                    //估算大致年月日
                    $addOne['client_birthday'] = $birthadyYear."-06-01";
                }
                //$addOne['aaa'] = $rosterVar['parent_name'];
                $addOne['client_tag'] = $rosterVar['course_name'];
                $addOne['client_frompage'] = $rosterVar['source_name'].'【臺灣直營校名單導入課叮鐺】';
                $addOne['client_fromtype'] = 0;//外部招生

                if($rosterVar['reservation_number']) {
                    $addOne['client_remark'] = $rosterVar['remark'] . "【来源表单：" . $rosterVar['reservation_number'] . "】";
                }else{
                    $addOne['client_remark'] = $rosterVar['remark'];
                }

                if($rosterVar['registration_name'] == '已拒絕'){
                    $addOne['client_tracestatus'] = '-1';
                }
                $addOne['client_createtime'] = strtotime($rosterVar['contact_at']);
//                print_r($addOne);die;
                $clientID = $this->DataControl->insertData('crm_client', $addOne);
                if($clientID){
                    //补充家长
                    $parenterOne = $this->DataControl->getFieldOne("smc_parenter", "parenter_id", "parenter_mobile='{$rosterVar['parent_phone']}' ");
                    $parenterid = $parenterOne['parenter_id'];
                    if(!$parenterid){
                        $dataThree = array();
                        $dataThree['parenter_cnname'] = $rosterVar['parent_name'];
                        $dataThree['parenter_mobile'] = $rosterVar['parent_phone'];
                        $dataThree['parenter_pass'] = md5(substr($rosterVar['parent_phone'], -6));
                        $dataThree['parenter_bakpass'] = substr($rosterVar['parent_phone'], -6);
                        $dataThree['parenter_addtime'] = time();
                        $parenterid = $this->DataControl->insertData("smc_parenter", $dataThree);
                    }
                    if($parenterid){
                        $dataThree = array();
                        $dataThree['client_id'] = $clientID;
                        $dataThree['company_id'] = $rosterVar['company_id'];
                        $dataThree['family_createtime'] = time();
                        $dataThree['parenter_id'] = $parenterid;
                        $dataThree['family_relation'] = '家長';
                        $dataThree['family_isdefault'] = 1;
                        $this->DataControl->insertData("crm_client_family", $dataThree);
                    }
                    //名单入校操作
                    if($schoolOne){
                        $stuOne = array();
                        $stuOne['client_id'] = $clientID;
                        $stuOne['company_id'] = $rosterVar['company_id'];
                        $stuOne['school_id'] = $schoolOne['school_id'];
                        $stuOne['is_schoolenter'] = '0';
                        $stuOne['schoolenter_createtime'] = time();
                        $stuOne['schoolenter_updatetime'] = time();
                        $this->DataControl->insertData('crm_client_schoolenter', $stuOne);
                    }
                    //名单添加跟踪记录
                    $track_note = "由名單管理系统導入：
                                    【表單來源:{$rosterVar['source_name']}】
                                    【聯繫狀況:{$rosterVar['contact_name']}】
                                    【處理狀況:{$rosterVar['status_name']}】
                                    【報名狀況:{$rosterVar['registration_name']}】
                                    ";
                    $dataFive = array();
                    if($schoolOne['school_id'] > 0){
                        $dataFive['school_id'] = $schoolOne['school_id'];
                    }
                    $dataFive['client_id'] = $clientID;
                    $dataFive['marketer_id'] = '0';
                    $dataFive['marketer_name'] = '系統';
                    $dataFive['track_linktype'] = '舊名單程式導入';
                    $dataFive['track_note'] = $track_note;
                    $dataFive['track_validinc'] = 1;
                    $dataFive['track_followmode'] = 0;
                    $dataFive['track_state'] = 0;
                    $dataFive['track_type'] = 2;
                    $dataFive['track_initiative'] = 1;
                    $dataFive['track_createtime'] = strtotime($rosterVar['contact_at']);
                    $this->DataControl->insertData("crm_client_track", $dataFive);


                    //标记名单已处理
                    $data = array();
                    $data['issync'] = 1;
                    $data['issucc'] = 1;
                    $this->DataControl->updateData("linshi_twroster", "cram_reservation_id = '{$rosterVar['cram_reservation_id']}'", $data);

                    $succ++;
                }else{
                    //名单插入失败
                    $data = array();
                    $data['issync'] = 1;
                    $data['errortip'] = "名单插入失败";
                    $this->DataControl->updateData("linshi_twroster", "cram_reservation_id = '{$rosterVar['cram_reservation_id']}' and parent_phone = '{$rosterVar['parent_phone']}' ", $data);

                    $fail++;
                    continue;
                }
            }

            echo "成功了：".$succ."个；"."失败了：".$fail."个";
        }else{
            echo "暂无名单处理！";
        }

    }

    //临时用 解密地推宝手机号
    function mobileJiemiApi(){
        $request = Input('get.', '', 'trim,addslashes');

        $mobilestr = $request['mobilestr'];
        $mobile = $this->DataControl->selectOne("select NEW_DECRYPTP('{$mobilestr}') as mobile limit 0,1");
        echo $mobile['mobile'];
    }

    //亚东要的江桥万达指定班级学生人像
    function downYdJqwdClassStuImgView(){

        echo '图片下载完毕';
        die;

        $sql = "SELECT (SELECT h.school_cnname FROM smc_school as h WHERE a.school_id = h.school_id limit 0,1 ) as school_cnname
                ,a.class_branch 
                ,c.student_branch 
                ,(SELECT h.stuportrait_faceimg FROM gmc_machine_stuportrait as h WHERE c.student_id = h.student_id order by h.stuportrait_creattime desc limit 0,1 ) as stuportrait_faceimg
                
            FROM smc_class as a ,smc_student_study as b ,smc_student as c 
            WHERE a.class_branch in ('20240225000037','20231229000022','20240223000012','20240201000119','20231104000047','20240225000036','20240218000048','20240219000027')
                and a.class_id = b.class_id 
                and b.student_id = c.student_id";
        $bookList = $this->DataControl->selectClear($sql);

        if($bookList){
            foreach ($bookList as $bookval) {
                $filenameurl = "StuFace/{$bookval['school_cnname']}/";
                if(!is_dir($filenameurl)){ //需要先判断文件夹是否存
                    mkdir(iconv('utf-8', 'gbk', $filenameurl) ,0777);
                }
                $filenameurl = "StuFace/{$bookval['school_cnname']}/{$bookval['class_branch']}/";
                if(!is_dir($filenameurl)){ //需要先判断文件夹是否存
                    mkdir(iconv('utf-8', 'gbk', $filenameurl) ,0777);
                }
                $url = $bookval['stuportrait_faceimg'];//图片
                if (@fopen($url, 'r')!=false) {
//                    $file_name = $filenameurl.basename($url);//文件名字
                    $file_name = $filenameurl."{$bookval['student_branch']}.". pathinfo($url, PATHINFO_EXTENSION);;//文件名字
                    if (file_put_contents(iconv('utf-8', 'gbk', $file_name), file_get_contents($url))) {
                        echo "文件下载成功--";
                    } else {
                        echo "文件下载失败--";
                    }
                }else{
                    echo '链接问题';
                }
            }
        }
    }

    //职工考勤人像图片下载 名称、图片下载 -- 下载职工人像数据
    function testdownMachineComstaffImgView(){
        echo '测试等比缩放的oss图片是否可以下载成功';
        die;
        $img = "https://oss.kidcastle.cn/Uploads/20180801/Images/163002263.jpg?x-oss-process=style/ekidbook";
        $imgone = "https://oss.kidcastle.cn/Uploads/20180801/Images/163002263.jpg";
        $filenameurl = "test/";//{$bookval['student_cnname']}_{$bookval['student_branch']}/
        if(!is_dir($filenameurl)){ //需要先判断文件夹是否存
            mkdir(iconv('utf-8', 'gbk', $filenameurl) ,0777);
        }
        $urlone = $imgone;//图片
        $url = $img;//图片
        if (@fopen($url, 'r')!=false) {
//                    $file_name = $filenameurl.basename($url);//文件名字
            $file_name = $filenameurl.'33.'. pathinfo($urlone, PATHINFO_EXTENSION);;//文件名字
            if (file_put_contents(iconv('utf-8', 'gbk', $file_name), file_get_contents($url))) {
                echo "文件下载成功--";
            } else {
                echo "文件下载失败--";
            }
        }else{
            echo '链接问题';
        }
        die;
    }

    //职工考勤人像图片下载 名称、图片下载 -- 下载职工人像数据
    function downMachineComstaffImgView(){

        echo '图片下载完毕';
        die;

        $sql = "SELECT x.* FROM ( 
                    SELECT a.staffer_id,a.staffer_cnname,a.staffer_branch,a.staffer_employeepid,d.stuportrait_faceimg 
                        ,if((SELECT f.postbe_id from gmc_staffer_postbe as f WHERE a.staffer_id = f.staffer_id and school_id = 0 limit 0,1)>1,1,0) as ff 
                    FROM smc_staffer as a,gmc_staffer_postbe as b,smc_school as c,gmc_machine_stuportrait as d 
                    WHERE a.company_id = '8888' and a.staffer_leave = 0 and a.staffer_isparttime = 0  
                    and a.staffer_id = b.staffer_id and b.school_id > 1 
                    and b.school_id = c.school_id and c.school_isclose = 0 and c.school_istest = 0 
                    and a.staffer_id = d.main_staffer_id
                    having ff = 0 
                    ORDER BY a.staffer_id asc,d.stuportrait_updatetime desc  
            ) as x  
            GROUP BY x.staffer_id
            limit 1200,200 ";
        $bookList = $this->DataControl->selectClear($sql);

        if($bookList){
            foreach ($bookList as $bookval) {
                $filenameurl = "MachineSchool/";//{$bookval['student_cnname']}_{$bookval['student_branch']}/
                if(!is_dir($filenameurl)){ //需要先判断文件夹是否存
                    mkdir(iconv('utf-8', 'gbk', $filenameurl) ,0777);
                }
                $url = $bookval['stuportrait_faceimg'];//图片
                if (@fopen($url, 'r')!=false) {
//                    $file_name = $filenameurl.basename($url);//文件名字
                    $file_name = $filenameurl."{$bookval['staffer_cnname']}_{$bookval['staffer_employeepid']}.". pathinfo($url, PATHINFO_EXTENSION);;//文件名字
                    if (file_put_contents(iconv('utf-8', 'gbk', $file_name), file_get_contents($url))) {
                        echo "文件下载成功--";
                    } else {
                        echo "文件下载失败--";
                    }
                }else{
                    echo '链接问题';
                }
            }
        }
    }

    //学生考勤人像图片下载 名称、图片下载 -- 下载某个学校学生人像数据
    function downMachineSchoolImgView(){

        echo '图片下载完毕';
        die;

        $sql = "SELECT x.* FROM ( 
            select a.student_id,b.student_cnname,b.student_branch,a.stuportrait_faceimg 
            from gmc_machine_stuportrait as a ,smc_student as b 
            WHERE a.school_id = '2322' and a.student_id = b.student_id 
            ORDER BY a.stuportrait_updatetime desc  
        ) as x  
        GROUP BY x.student_id";
        $bookList = $this->DataControl->selectClear($sql);

        if($bookList){
            foreach ($bookList as $bookval) {
                $filenameurl = "MachineSchool/";//{$bookval['student_cnname']}_{$bookval['student_branch']}/
                if(!is_dir($filenameurl)){ //需要先判断文件夹是否存
                    mkdir(iconv('utf-8', 'gbk', $filenameurl) ,0777);
                }
                $url = $bookval['stuportrait_faceimg'];//图片
                if (@fopen($url, 'r')!=false) {
//                    $file_name = $filenameurl.basename($url);//文件名字
                    $file_name = $filenameurl."{$bookval['student_cnname']}_{$bookval['student_branch']}.". pathinfo($url, PATHINFO_EXTENSION);;//文件名字
                    if (file_put_contents(iconv('utf-8', 'gbk', $file_name), file_get_contents($url))) {
                        echo "文件下载成功--";
                    } else {
                        echo "文件下载失败--";
                    }
                }else{
                    echo '链接问题';
                }
            }
        }
    }

    //临时名称处理
    function linshiKebaoClientView(){
        echo '已处理';
        die;
//        $someclient = $this->DataControl->selectClear(" select a.*,b.school_branch,c.promotion_jobnumber
//                 from linshi_0305kebao as a,smc_school as b,crm_ground_promotion as c
//                 where ishandle = '0' and ishaveschool = '1' and ispro = '1'
//                 and (a.`校区` = b.school_shortname or a.`校区` = b.school_cnname)
//                 and a.`分销员手机号` = c.promotion_mobile
//                 group by a.id
//                 order by a.id
//                 limit 0,10 ");
        $someclient = $this->DataControl->selectClear(" select a.*,b.school_branch 
                    from linshi_0305kebao as a,smc_school as b 
                    where ishandle = '0' and ishaveschool = '1'
                    and (a.`校区` = b.school_shortname or a.`校区` = b.school_cnname)   
                    group by a.id
                    order by a.id 
                    limit 0,10 ");
        if($someclient){
            foreach ($someclient as $someVar){
                $someVar['买家手机号'] = trim($someVar['买家手机号']);

                $clientOne = $this->DataControl->selectOne(" select * from crm_client where company_id = '8888' and client_mobile = '{$someVar['买家手机号']}' limit 0,1 ");
                if($clientOne){
                    $data = array();
                    $data['client_tag'] = $clientOne['client_tag']?$clientOne['client_tag'].',课包':'课包';
                    $this->DataControl->updateData("crm_client", "client_id = '{$clientOne['client_id']}'", $data);
                    $en1d = array();
                    $en1d['ishandle'] = '2';
                    $this->DataControl->updateData("linshi_0305kebao", "id = '{$someVar['id']}'", $en1d);
                    continue;
                }else{
                    $paramArray = array();
                    $paramArray['company_id'] = '8888';
                    $paramArray['client_cnname'] = $someVar['买家姓名'];
                    $paramArray['client_mobile'] = $someVar['买家手机号'];
                    $paramArray['client_frompage'] = '地推宝名单导入系统操作(课包)';
                    $paramArray['channel_name'] = '成长中心引流课包';
                    $paramArray['school_branch'] = $someVar['school_branch'];
                    $paramArray['client_remark'] = '地推宝名单导入系统操作(课包)';
//                $paramArray['client_address'] = $someVar['买家姓名'];
                    $paramArray['promotion_jobnumber'] = $someVar['promotion_jobnumber'];
                    $paramArray['client_tag'] = '课包';
                    $Model = new \Model\Crm\ClientModel($paramArray);
                    $dataList = $Model->linshiaddPhoneChannelAction($paramArray);
                    if($Model->error == '0'){
                        $end = array();
                        $end['ishandle'] = '1';
                        $this->DataControl->updateData("linshi_0305kebao", "id = '{$someVar['id']}'", $end);
                        continue;
                    }else{
                        $end = array();
                        $end['ishandle'] = '-1';
                        $this->DataControl->updateData("linshi_0305kebao", "id = '{$someVar['id']}'", $end);
                        continue;
                    }
                }
            }
        }

    }

    //测试导入 -- 招行特殊的 excel
    function ZhaoHangExcelApi(){
        $request = Input('post.', '', 'strip_tags');

        $polname = "POL30899918299084S20231118";
        $url = 'importexcel/compare/'.$polname.'.xlsx';
        $ys_array = array('日账单' => 'cmbheadappid');
        $sqlarray = execl_to_array_zhone($url, $ys_array);
        array_shift($sqlarray);
        $cmbappid11 = $sqlarray[0]['cmbheadappid'];
echo $cmbappid11.'----';

        $ys_arrayOne = array('日账单' => 'cmbheadappid');
        $sqlarrayOne = execl_to_array_zhone($url, $ys_arrayOne);
        array_shift($sqlarrayOne);
        $cmbappid112 = $sqlarray[0]['cmbheadappid'];

        print_r($cmbappid112);die;
        die;

    }

    //处理地推宝名单数据
    function handleTtbUserView(){die;
        //一些地推人员名单
        $someUser = $this->DataControl->selectClear(" select * from linshi_dituibao_user where 1 and school_id <> '' limit 0,2 ");
        if($someUser){
            foreach ($someUser as $someUserVar){
                //某个地推人员  所属学校名单
                $someSchool = $this->DataControl->selectClear(" select * from linshi_dituibao_school where id in ({$someUserVar['school_id']}) and school_type = '1' ");
                if($someSchool){
                    foreach ($someSchool as $someSchoolVar){
                        //$someUserVar['business_no']; //地推人员 工号
                        //$someUserVar['business_no']; //地推人员 手机号

                        //校的学校
                        $schoolOne = $this->DataControl->selectOne(" select school_id,school_branch from smc_school where school_branch = '{$someSchoolVar['school_no']}' and company_id = '8888' ");
                        if(!$schoolOne){
                            continue;
                        }

                        //查找 校务地推宝人员
                        $promotionOne = $this->DataControl->selectOne(" select promotion_id from crm_ground_promotion where promotion_mobile = '{$someUserVar['business_no']}' and promotion_jobnumber = '{$someUserVar['business_no']}' ");
                        if($promotionOne){
                            //添加地推人员 所属学校
                            $openOne = $this->DataControl->selectOne(" select * from crm_ground_promotion_open where promotion_id = '{$promotionOne['promotion_id']}' and school_id = '{$schoolOne['school_id']}' and company_id = '8888' ");
                            if($openOne){
                                //修改 所属学校是否开启
                                if($openOne['open_status'] == '0'){
                                    //修改所属学校是否开启
                                    $this->DataControl->updateData("crm_ground_promotion_open", " company_id = '8888' and school_id = '{$schoolOne['school_id']}' and promotion_id = '{$promotionOne['promotion_id']}' ", array("open_status" => '1' ));
                                    //修改主表的更新时间
                                    $this->DataControl->updateData("crm_ground_promotion", "company_id = '8888' and promotion_id = '{$promotionOne['promotion_id']}'", array("promotion_updatetime" => time() ));
                                }
                            }else{
                                //添加所属的学校
                                $opData = array();
                                $opData['company_id'] = 8888;
                                $opData['school_id'] = $schoolOne['school_id'];
                                $opData['promotion_id'] = $promotionOne['promotion_id'];
                                $opData['open_status'] = 1;
                                $this->DataControl->insertData('crm_ground_promotion_open', $opData);
                            }

                        }else{
                            //添加地推人员名单
                            $data = array();
                            $data['company_id'] = 8888;
                            $data['school_id'] = $schoolOne['school_id'];
                            $data['marketer_id'] = '';
                            $data['promotion_name'] = $someUserVar['username'];
//                            $data['promotion_mobile'] = $someUserVar['promotion_mobile'];
                            $data['promotion_jobnumber'] = $someUserVar['business_no'];
//                            $data['promotion_bankcard'] = $someUserVar['promotion_bankcard'];
//                            $data['promotion_bank'] = $someUserVar['promotion_bank'];
//                            $data['promotion_type'] = $someUserVar['promotion_type']; //0 市场  1 销售
                            $data['promotion_createtime'] = time();
                            $pOneId = $this->DataControl->insertData('crm_ground_promotion', $data);

                            //添加所属的学校
                            $opData = array();
                            $opData['company_id'] = 8888;
                            $opData['school_id'] = $schoolOne['school_id'];
                            $opData['promotion_id'] = $pOneId;
                            $opData['open_status'] = 1;
                            $this->DataControl->insertData('crm_ground_promotion_open', $opData);
                        }
                    }
                }
            }
        }else{
            echo '暂无地推宝名单';
        }
    }

    //调取 水印 方法
    function getUrlShuiyinApi(){
        $this->ceshiShuiyin();
    }

    //水印 URL图片上打水印
    function ceshiShuiyin(){
        // 创建画布
        $image = imagecreatetruecolor(1366, 768);
        // 载入原始图片
//        $originalImage = imagecreatefrompng('http://crmshare.kcclassin.com/temp/template-1/images/template-1-title-bg.png');//png 图片的
        $originalImage = imagecreatefromjpeg('https://pic.kedingdang.com/schoolmanage/201905161334x802048844.jpg');//jpg 图片的

//        $originalImage = imagecreatefromjpeg('ceshi/456.jpg');
        // 在画布上绘制原始图片
        // 首先是第一个参数，它是目标图像的文件资源。
        // 接下来是第二个参数，它也是一个文件资源
        // 第三个参数和第四个参数分别代表了我们需要复制到目标图像的位置（就是  复制到画布的开始未知）
        // 第五个参数和第六个参数是最重要的两个参数，因为它们定义了我们需要复制的数据的大小和位置（就是  要复制的图片的起始位置）
        // 第七个参数和第八个参数 这里的宽度是$sw=500，高度是$sh=300。这个函数将原图像的左上角（即（0,0）），在目标图像的（10,20）位置处，复制一个宽500高300的矩形。（就是  要复制的图片的大小）
        // 第九：最后一个参数是可选的，它表示了源图片的透明色
        imagecopy($image, $originalImage, 0, 0, 0, 0, 1366, 768);

        // 添加文字水印
        $font = 'ceshi/arial.ttf';
        $color = imagecolorallocate($image, 255, 255, 255); // 水印文字颜色
        $text = 'Your Watermark Text';
        //1 指定要处理的图像
        //2 它指定要使用的字体大小，以磅为单位
        //3 它以度为单位指定角度
        //4 指定 x 坐标。
        //5 它指定 y 坐标。
        //6 它指定文本所需颜色的索引
        //7 它指定要使用的字体。
        //8 它指定要写入的文本。
        imagettftext($image, 20, 0, 50, 50, $color, $font, $text);

        // 输出图片
        header('Content-Type: image/jpeg');
        //imagejpeg($image,'ceshi/img/fasdf.jpg');  //可以存储到本地
        imagejpeg($image);

        // 清理资源
        imagedestroy($image);
        imagedestroy($originalImage);
    }

    function moblielogApi(){
        echo '已经处理';die;

        $outcall = $this->DataControl->selectClear("select outcall_id,outcall_json from gmc_outcall where outcall_class = 1 and outcall_duration = 0 limit 0,50");

        if($outcall){
            foreach ($outcall as $outcallVar){
                $datajson = json_decode($outcallVar['outcall_json'],true);
                $stime = $datajson[0]['recordLength'];


                $updata = array();
                $updata['outcall_duration'] = $stime;
                $this->DataControl->updateData("gmc_outcall", "outcall_id = '{$outcallVar['outcall_id']}'", $updata);
            }
        }
    }

    function heliBranchLogApi(){
        echo '已经处理';die;

        $outcall = $this->DataControl->selectClear("select outcall_id,outcall_json from gmc_outcall where outcall_class = 2 ");

        if($outcall){
            foreach ($outcall as $outcallVar){
                $datajson = json_decode($outcallVar['outcall_json'],true);
                $hlbranch = $datajson['hlbranch'];


                $updata = array();
                $updata['outcall_uuid'] = $hlbranch;
                $this->DataControl->updateData("gmc_outcall", "outcall_id = '{$outcallVar['outcall_id']}'", $updata);
            }
        }
    }

    //截取指定两个字符之间的字符串
    function cut($begin,$end,$str){
        $b = mb_strpos($str,$begin) + mb_strlen($begin);
        $e = mb_strpos($str,$end) - $b;
        return mb_substr($str,$b,$e);
    }

    //处理地推宝数据
    function chulidituibaoView(){
        echo '已处理,曹艳_青浦吾悦校_异常例子明细数据_未脱敏';exit;
        $mobilelist = ['13482035287','17301799737','13062873639','18117162543','15026658085','13816529505','18621862807','18905731837','15921935788','13482221681','18717946559','15989348844','15001890861','18217508132','18016377519','15810616088','13651828081','13501823147','13701655429','18201984858','15221690337','18017069827','17640241141','13651746587','19526233605','17521727630','18994464988','15319860757','18217302636','18321291187','18964579892','17711766803','13203905529','15921893321','13585997517','18221157486','13625255252','15821561766','15921823682','18621655288','13818984317','15121177669','13671684079','18612136739','13331356013','18930898107','15821330665','13917298893','13517262032','13817358471','17639910296','13909649616','17301872400','15998344699','13761211921','15021279968','18117128771','15900491359','18918869668','15921187199','15821823973','13818012909','17302146351','13524173798','13564220899','13585602252','18726516668','13989476467','18321122473','13162702321','13661448685','13917278339','18918869368','13818303813','15800795917','13817054303','18930273620','15549995155','13451832879','13671983326','17740800882','19916646876','18953274331','13564754139','15955227001','13061898978','15202150320','18901827100','15821170763','18116307986','15221578367','17721196528','18601669317','18621186433','15721109023','13957388590','13917993543','15872062683','15721517524','15061527755','13162529878','13162061079','13818625730','15180159683','18121130678','17316561448','13661904751','18018518138','18255857848','13918008624','18516777182','15201768608','15900694397','13681766384','18516586219','13405039576','13764937982','18616962428','18217519046','13482236673','15501355864','18019135830','18016048615','18964322715','17317220324','18717890677','16712146505','13816300420','13585761239','13472510599','13795469581','13601234589','15000869316','17521181756','17721427278','13046421896','18016096628','17701652389','18201873576','13916168758','13585665606','18320493314','13482827181','18930967100'];

//        $mobilelist = ['13482035287'];

        foreach ($mobilelist as $mobilevar){
            $txt = '';
            $dataOne = $this->DataControl->selectOne("select apilog_postorgjson from imc_apiuser_apilog where apiuser_id = '2349' and apimodule_id = '7362' and  apilog_postorgjson like '%{$mobilevar}%' limit 0,1  ");

            $begin ='promotion_jobnumber=';
            $end ='&client_sex=';
            $txt = $this->cut($begin,$end,$dataOne['apilog_postorgjson']);

            $data = array();
            $data['linshi_mobile'] = $mobilevar;
            $data['linshi_branch'] = $txt;
            $this->DataControl->insertData('imc_apiuser_linshi', $data);
        }


    }

    //喆哥 西安曲江龙湖天街校 所有待分配有效名单转到  西安英杰校 （意向名单 在系统做了 待分配处理）
    function XaQujiangToYingjieView(){
//        echo '已处理';
//        echo "名单已处理";
        $sql = "SELECT c.client_id,c.client_cnname,c.client_enname  
                FROM crm_client as c
                Left JOIN crm_client_principal as  p ON p.client_id = c.client_id and principal_ismajor = 1 and p.school_id='647'
                Left JOIN crm_marketer as mk ON mk.marketer_id = p.marketer_id
                Left JOIN crm_client_schoolenter as s  ON s.client_id=c.client_id
                left JOIN crm_client_family as  f ON f.client_id= c.client_id
                LEFT JOIN smc_parenter as sp ON sp.parenter_id = f.parenter_id
                LEFT JOIN crm_code_nearschool as nl On nl.nearschool_id = c.nearschool_id
                WHERE s.is_enterstatus <> '-1' and c.client_distributionstatus = 0 and c.client_tracestatus in ('0','1','2','3') 
                            and c.company_id='8888' and s.school_id='2377'  and c.client_isgross = '0'  
                            GROUP BY c.client_id  limit 0,20";
        $dataList = $this->DataControl->selectClear($sql);
//        print_r($dataList);die;
        if($dataList){
            foreach ($dataList as $key=>$dataVar){
                $todata = array();
                $todata['company_id'] = '8888';
                $todata['school_id'] = '2377';
                $todata['client_id'] = $dataVar['client_id'];
                $todata['re_school_id'] = '2274';
                $todata['note'] = '喆哥：西安曲江龙湖天街校所有的待分配名单 转到西安英杰校';
                $todata['marketer_id'] = '15';

                $Model = new \Model\Crm\ClientModel($todata);
                $bool = $Model->transferSchool($todata);
                $this->addCrmWorkLog($todata['company_id'], $todata['school_id'], '15', "招生有效名单->招生有效名单", 'crm转校', dataEncode($todata));
                echo $dataVar['client_id'];
                echo '--';
            }
        }
    }

    //喆哥 桃浦校所有待分配有效名单转到  白丽校 （意向名单 在系统做了 待分配处理）
    function TaopuToBailiView(){
        echo '已处理';
        echo " '54167','54201','59764','59907','59922','59939','59986','62944','67853','69129','70098','70964','73490','74029','74913','75204','79340','79913','80988','81091','81559','81576','81856','81966','82078','82160','82336','82993','87390','89141','89191','90483','92856','94928','195550','202005','204281','206885','207136','227217','234690','260458','266057','266655','269488','269525','293380','298113','306402','323381','325339','328397','330746','353713','362405','363164','364311','373433','374195','387008','395428','413745','413782','413821','413837','413844','413858','413883','413930','413952','413962','414054','414076','418714','419181','435232','436789','437370','440357','444828','447945','448618','450027','453088','453101','461564','467295','473720','478253','479148','479866','479868','479873','479876','479880','479883','479887','483214','483242','484615','485990','489772','489790','489792','495013','495015','495018','496554','496564','496570','496581','496608','498124','499865','500496','501854','501877','501935','502067','502073','502082','502092','506039','506793','508422','510067','512049','513126','513676','514216','515640','515642','518510','525968','533375','540203','552594','560799','570763','591997','606660','610931','612372','614551','620217','622233','622590','623335','626229','626258','626841','635268','638837','653009','661121','666058','668558','675448','675649','675821','675833','677401','677610','677831','682243','695625','695737','697223','714966','732451','743406','756426','758080','762111','763105','763687','767033','768246','768883','770543','772744','773869','776448','776755','777465','777595','777663','778477','778656','779642','779648','781066','782794','790247','790630','790678','797463','797472','797473','797476','797482','797484','797489','797490','797491','797492','797494','797496','797506','797508','797512','797513','797514','797516','797521','797522','797524','797529','797531','797532','797533','797540','797545','797551','797555','797559','797561','797562','797566','797571','797572','797573','797574','797576','797579','797581','797582','797583','797584','797588','797592','797594','797596','797601','797603','797604','797605','797606','797607','797608','797609','797610','797611','797612','797613','797614','797615','797616','797617','797618','797619','797620','797621','797622','797623','797624','797625','797626','797627','797628','797629','797631','797633','797634','797635','797636','797637','797638','797639','797640','797641','797642','797643','797644','797645','797646','797647','797648','797649','797650','797652','797653','797654','797655','797656','797657','797658','797659','797660','797661','797662','797663','797664','797665','797666','797667','797668','797669','797670','797672','797673','797674','797675','797677','797678','797679','797680','797681','797682','797683','797684','797685','797687','797688','797689','797690','797691','797692','797693','797694','797695','797696','797698','797699','797700','797701','797703','797704','797705','797706','797707','797708','797709','797710','797711','797712','797713','797714','797715','797716','797717','797718','797719','797720','797721','797722','797723','797724','797725','797726','797727','797728','797729','797731','797732','797733','797734','797735','797736','797737','797738','797739','797740','797741','797742','797743','797744','797745','797746','797747','797748','797749','797750','797751','797752','797753','797754','797755','797756','797759','797760','797762','797763','797764','797765','797767','797768','797769','797770','797771','797772','797773','797774','797775','797776','797777','797778','797779','797781','797782','797783','797784','797785','797786','797787','797788','797789','797790','797791','797792','797794','797795','797797','797798','797799','797800','797801','797802','797803','797804','797805','797806','797808','797809','797811','797812','797813','797815','797816','797817','797818','797819','797820','797821','797822','797823','797824','797825','797826','797827','797828','797832','797833','797834','797835','797836','797838','797839','797840','797841','797842','797843','797844','797845','797846','797847','797848','797849','797850','797852','797853','797854','797855','797856','797857','797858','797859','797860','797861','797862','797863','797864','797865','797866','797867','797871','797872','797876','797877','797878','797879','797880','797881','797882','797883','797884','797885','797886','797887','797888','797889','797890','797891','797894','797895','797896','797898','797899','797900','797901','797902','797903','797905','797910','797911','797918','797925','799963','808497','812725' ";
        echo "名单已处理";
        $sql = "SELECT c.client_id,c.client_cnname,c.client_enname  
                FROM crm_client as c
                Left JOIN crm_client_principal as  p ON p.client_id = c.client_id and principal_ismajor = 1 and p.school_id='647'
                Left JOIN crm_marketer as mk ON mk.marketer_id = p.marketer_id
                Left JOIN crm_client_schoolenter as s  ON s.client_id=c.client_id
                left JOIN crm_client_family as  f ON f.client_id= c.client_id
                LEFT JOIN smc_parenter as sp ON sp.parenter_id = f.parenter_id
                LEFT JOIN crm_code_nearschool as nl On nl.nearschool_id = c.nearschool_id
                WHERE s.is_enterstatus <> '-1' and c.client_distributionstatus = 0 and c.client_tracestatus in ('0','1','2','3') 
                            and c.company_id='8888' and s.school_id='672'  and c.client_isgross = '0'  and c.client_id <> '54167'  
                            GROUP BY c.client_id  limit 0,20";
        $dataList = $this->DataControl->selectClear($sql);
//        print_r($dataList);die;
        if($dataList){
            foreach ($dataList as $key=>$dataVar){
                $todata = array();
                $todata['company_id'] = '8888';
                $todata['school_id'] = '672';
                $todata['client_id'] = $dataVar['client_id'];
                $todata['re_school_id'] = '689';
                $todata['note'] = '喆哥：桃浦校所有的待分配名单 转到白丽校';
                $todata['marketer_id'] = '15';

                $Model = new \Model\Crm\ClientModel($todata);
                $bool = $Model->transferSchool($todata);
                $this->addCrmWorkLog($todata['company_id'], $todata['school_id'], '15', "招生有效名单->招生有效名单", 'crm转校', dataEncode($todata));
                echo $dataVar['client_id'];
                echo '--';
            }
        }
    }

    //喆哥 武威东路所有待分配有效名单转到  白丽校 （意向名单 在系统做了 待分配处理）
    function WuweidongToBailiView(){
        echo '名单已处理';die;
        echo "  '54638','69440','76879','77882','79859','148203','180413','269860','271774','286100','286628','286938','292255','298580','298873','307431','317969','321369','329884','334174','434258','480121','484183','493965','518763','555306','568434','568541','572808','580401','589793','600443','604209','608193','645470','653491','662879','663835','714785','715431','736781','741130','741155','741196','741203','744618','744733','747681','748520','760544','763615','764479','768849','769304','778501','789551','809839','814615','818444','819441','823447','839643','881426','884531','919138' ";
        echo '名单已处理';die;
        $sql = "SELECT c.client_id,c.client_cnname,c.client_enname  
                FROM crm_client as c
                Left JOIN crm_client_principal as  p ON p.client_id = c.client_id and principal_ismajor = 1 and p.school_id='647'
                Left JOIN crm_marketer as mk ON mk.marketer_id = p.marketer_id
                Left JOIN crm_client_schoolenter as s  ON s.client_id=c.client_id
                left JOIN crm_client_family as  f ON f.client_id= c.client_id
                LEFT JOIN smc_parenter as sp ON sp.parenter_id = f.parenter_id
                LEFT JOIN crm_code_nearschool as nl On nl.nearschool_id = c.nearschool_id
                WHERE s.is_enterstatus <> '-1' and c.client_distributionstatus = 0 and c.client_tracestatus in ('0','1','2','3') 
                            and c.company_id='8888' and s.school_id='659'  and c.client_isgross = '0'  and c.client_id <> '269860'  
                            GROUP BY c.client_id  limit 0,20 ";
        $dataList = $this->DataControl->selectClear($sql);
//        print_r($dataList);die;
        if($dataList){
            foreach ($dataList as $key=>$dataVar){
                $todata = array();
                $todata['company_id'] = '8888';
                $todata['school_id'] = '659';
                $todata['client_id'] = $dataVar['client_id'];
                $todata['re_school_id'] = '689';
                $todata['note'] = '喆哥：武威东路校所有的待分配名单 转到白丽校';
                $todata['marketer_id'] = '15';

                $Model = new \Model\Crm\ClientModel($todata);
                $bool = $Model->transferSchool($todata);
                $this->addCrmWorkLog($todata['company_id'], $todata['school_id'], '15', "招生有效名单->招生有效名单", 'crm转校', dataEncode($todata));
                echo $dataVar['client_id'];
                echo '--';
            }
        }
    }

    //喆哥  月浦校所有的待分配名单 转到水产校
    function YuePuToShuiChanView(){
        echo  '已处理';die;
        die;
        "'53816','53817','53818','53819','53820','53821','53822','53823','53824','53826','65593','65594','65609','65610','65611','65613','65621','65623','65627','65628','65662','65737','65738','65739','65740','65741','65742','65743','65744','65745','65746','65747','65748','65749','65750','65752','65754','65755','65756','65758','65759','65761','65762','65764','65766','65768','65772','65779','65781','65782','65783','65785','65786','65787','65788','65789','65790','65791','65792','65793','65794','65795','65796','65797','65798','65800','65803','65807','65817','65819','65837','65841','65842','65843','65844','65845','65853','65868','65869','65870','65871','65876','65877','65885','65887','65888','65902','65903','65904','65905','65907','65910','65913','65914','65915','65916','65917','65920','65923','65926','65927','65928','65929','65930','65931','65932','65936','66073','66074','68341','69070','69503','69504','72519','73611','74238','75435','75436','75437','75438','75439','75440','78613','78614','78615','78616','78617','78618','79511','79512','79513','79514','79515','80320','82059','88435','89439','90065','90066','90067','90640','90642','90645','90888','91001','91002','91458','92822','93693','93744','94779','95100','95573','105661','110030','110192','114400','119766','140446','143016','147813','149305','184232','216817','221371','248679','258081','258165','258367','258539','258578','258584','258616','258624','258643','258855','258867','259157','259805','260150','265846','265848','265905','266045','266047','266048','266063','266101','266102','266672','266840','266856','266920','266930','266986','267557','268077','268151','268359','268688','268932','268959','268971','268982','269239','269480','269637','269639','269641','269643','269644','269645','269648','269649','269650','269654','269656','269657','269658','269660','269663','269665','269667','269669','269672','269676','269679','269682','269684','269687','269689','269693','269695','269697','269698','269700','269702','269704','269709','269712','269714','270139','270140','270143','270145','270146','270147','270148','270150','270151','270153','270154','270157','270158','270159','270160','270161','270162','270163','270165','270166','270179','270388','270389','270581','270791','270823','270921','271415','271522','271525','271528','271533','271537','271540','271544','271546','271547','271551','271558','271559','271564','271566','271567','271568','271569','271570','271574','271575','271576','271577','271579','271600','271601','271602','271603','271605','271607','271608','271609','271610','271691','271695','271849','271850','271852','271853','271855','271856','271857','271858','271860','271861','271862','271863','271864','271865','271867','271869','271870','271872','271874','271875','271876','271879','271880','271882','272003','272004','272155','272383','272656','272714','273694','273698','274406','274416','275086','275087','275089','275091','275101','275102','275105','275106','275110','275111','275112','275289','275292','275294','275317','275318','275319','275320','275321','275325','275326','275331','275332','275333','275334','275336','275337','275339','275340','275341','275342','275343','275344','275346','275347','275348','275349','275351','275353','275355','275357','275361','275364','275402','275886','275997','276135','276390','276401','276406','276409','276412','276413','276416','276629','276631','276634','276636','276637','276639','276640','276642','276644','276645','276646','276647','276648','276649','276650','276651','276652','276653','276654','276655','276657','276661','276664','276665','276667','276670','276671','276672','276674','276675','276677','276679','276680','276681','276682','276683','276686','276688','276689','276691','276692','276695','276697','276700','276803','276899','277259','277260','277262','277263','277264','277266','277270','277271','277272','277274','277275','277276','277277','277278','277279','277280','277281','277282','277283','277284','277285','277286','277287','277288','277290','277291','277292','277293','277294','277295','277297','277298','277300','277301','277630','277631','277634','277638','277639','277642','277644','277646','277648','277671','277673','277675','277677','277679','277680','277681','277696','277699','277704','277706','277727','277729','277731','277733','277788','277791','277795','277796','277797','277798','277799','277800','277804','277805','277809','277810','277817','278161','278433','278434','278437','278438','278439','278440','278441','278442','278443','278444','278447','278448','278450','278451','278452','278453','278456','278457','278459','278460','278461','278462','278463','278874','278875','279362','279363','279364','279365','279383','279384','279385','279386','279387','279388','279389','279390','279391','279392','279393','279394','279395','279396','279397','279398','279399','279623','279966','279971','279973','279975','279976','279982','279985','279986','279988','279989','280558','280559','280562','280564','280565','280567','280568','280570','280572','280574','280575','280579','280580','280582','280584','280587','280589','280590','280591','280592','280594','280595','280597','280598','280599','280601','280832','281145','281146','281150','281152','281154','281168','281170','281173','281174','281179','281181','281183','281186','281321','281323','281724','286034','286153','286155','286156','286167','286172','286201','286226','286228','286229','286232','286242','286283','286286','286290','286291','286296','286301','286302','286304','286305','286306','286307','286311','286569','287154','287157','287165','287166','287167','287169','287170','287174','287175','287178','287179','287181','287182','287184','287185','287188','287191','287193','287195','287197','287198','287199','287200','287202','287203','287211','287212','287215','287216','287220','287221','287222','287224','287722','287857','287870','289109','290218','290229','290254','290720','291387','291424','292395','292589','292591','292592','292593','292594','292596','292597','292598','292625','292626','292873','292874','292876','292877','292878','292880','292882','292889','293681','293682','293683','293685','293686','293687','293688','293689','293690','293879','294239','294242','294244','294245','294246','294247','294318','294321','294664','294749','294770','294782','295048','295160','295263','295701','296229','296253','296271','296399','296661','296664','296665','296667','296991','297645','298672','298673','298674','298675','298677','298762','300412','300413','300414','300415','300417','300418','300419','300420','300422','300536','300899','301007','301511','301512','301513','301515','301516','301530','301533','301534','301538','301542','301548','301550','301681','304831','304848','305146','306266','306517','306700','307434','309897','310526','311347','311348','311350','312588','312839','313028','313450','313455','314198','314405','314406','315693','315694','316057','316801','316803','317193','317411','317766','317788','317846','318129','318470','319171','319369','319515','319705','319709','319769','320015','320267','320716','321483','321531','321658','321844','321989','322095','322098','322125','322447','322452','322985','323067','324341','324435','324482','324705','325571','325593','325721','326559','326633','326694','341235','341386','341460','341857','341888','341905','342403','342990','343156','343556','344147','344483','344507','344510','344748','344752','344760','344824','345018','345141','345159','345564','345694','347348','348520','348524','349251','349550','349551','350035','350057','350516','350550','350570','351821','386489','389599','389624','390112','390196','390216','390224','390592','397013','397070','397314','398141','398150','398461','398466','398486','399159','400015','400564','400653','401463','401481','403168','403219','406170','407071','410868','410926','410946','410986','412553','412613','413679','413683','415127','415136','415260','415555','417987','418462','418492','418859','418868','420241','420248','422380','423045','423071','424281','425084','425676','425903','425907','425917','425953','427204','427479','428034','428047','428248','429151','429587','429937','429951','430030','430316','430902','431377','431986','432515','432909','432916','432977','433257','433317','433410','433422','433423','433427','433914','434113','443345','444085','446584','446814','448041','455292','457719','458707','458773','459100','461327','462194','462237','469169','469172','473071','474963','479282','481816','485437','486474','492635','497435','502675','502703','519997'";
        echo "这些名单迁移了";die;
        $sql = "SELECT c.client_id,c.client_cnname,c.client_enname  
                FROM crm_client as c
                Left JOIN crm_client_principal as  p ON p.client_id = c.client_id and principal_ismajor = 1 and p.school_id='647'
                Left JOIN crm_marketer as mk ON mk.marketer_id = p.marketer_id
                Left JOIN crm_client_schoolenter as s  ON s.client_id=c.client_id
                left JOIN crm_client_family as  f ON f.client_id= c.client_id
                LEFT JOIN smc_parenter as sp ON sp.parenter_id = f.parenter_id
                LEFT JOIN crm_code_nearschool as nl On nl.nearschool_id = c.nearschool_id
                WHERE s.is_enterstatus <> '-1' and c.client_distributionstatus = 0 and c.client_tracestatus in ('0','1','2','3') 
                            and c.company_id='8888' and s.school_id='647'  and c.client_isgross = '0'  and c.client_id <> '53818'  
                            GROUP BY c.client_id   ";
        $dataList = $this->DataControl->selectClear($sql);
        if($dataList){
            foreach ($dataList as $key=>$dataVar){
                $todata = array();
                $todata['company_id'] = '8888';
                $todata['school_id'] = '647';
                $todata['client_id'] = $dataVar['client_id'];
                $todata['re_school_id'] = '687';
                $todata['note'] = '喆哥：月浦校所有的待分配名单 转到水产校';
                $todata['marketer_id'] = '15';

                $Model = new \Model\Crm\ClientModel($todata);
                $bool = $Model->transferSchool($todata);
                $this->addCrmWorkLog($todata['company_id'], $todata['school_id'], '15', "招生有效名单->招生有效名单", 'crm转校', dataEncode($todata));
                echo $dataVar['client_id'];
                echo '--';
            }
        }
    }

    public function addCrmWorkLog($company_id,$school_id,$marketer_id,$module,$type,$content,$module_id=0){
        $stafferOne = $this->DataControl->getFieldOne("crm_marketer","staffer_id","marketer_id='{$marketer_id}'");
        if($stafferOne){
            $logData = array();
            $logData['company_id'] = $company_id;
            $logData['school_id'] =$school_id;
            $logData['staffer_id'] =$stafferOne['staffer_id'];
            $logData['worklog_module'] =$module;
            $logData['worklog_type'] =$type;
            $logData['worklog_content'] =$content;
            $logData['worklog_ip'] =real_ip();
            $logData['worklog_time'] =time();
            $this->DataControl->insertData('crm_staffer_worklog',$logData);
            return true;
        }else{
            return false;
        }
    }


    //喆哥 旭辉和龙湖早教数据处理相关事项：
    //1、两校所有年龄为0-2岁的名单（排除已转正），全部导入到早教的系统中，原成长中心数据改为无效客户。同步将所有历史跟踪记录合并保存为备注（或一条数据，需要技术评估一下）。
    //2、校区讲年龄为3岁的名单进行筛选，对需要转移的数据标记为早教标签（标记方法如图）。所有名单标记完毕后，按照第一条方式执行批量导入。
    //南昌龙湖天街校   ID:2322
    //南昌旭辉广场校   ID:2323
    //第一  查出两所学校的 0-2 岁的未转正的名单
    function getSchoolOneCrmClientView(){
        echo '当前数据已处理';
        die;
        $sql = "SELECT c.client_id,c.client_cnname,c.client_mobile,c.client_sex,c.client_source,c.channel_id,c.client_intention_level
                ,(select h.channel_name from crm_code_channel as h WHERE h.channel_id = c.channel_id) as channel_name
                ,(select GROUP_CONCAT(t.track_note) from crm_client_track as t where t.client_id = c.client_id) as track_notes 
                ,c.client_birthday,c.client_age,s.school_id   
                FROM crm_client as c ,crm_client_schoolenter as s 
                WHERE c.company_id = '8888' 
                and c.client_id = s.client_id 
                and (c.client_birthday > '2020-09-01'
                        or (c.client_age < 3 and c.client_age >= 0  and c.client_birthday	= '')
                        ) 
                and c.client_tracestatus < 4 
                and (s.school_id = '2323' or s.school_id = '2322') 
                and s.is_enterstatus = '1'  
                ORDER BY s.school_id   ";
        $dataList = $this->DataControl->selectClear($sql);
        ajax_return(array('error' => 0,'errortip' => "获取成功",'result' => $dataList));
    }
    //批量转为无效客户
    function getSchoolOneCrmClientToView(){
        echo '当前数据已处理';
        die;
 

//        $sql = "SELECT c.client_id,c.client_cnname,c.client_mobile,c.client_sex,c.client_source,c.channel_id,c.client_intention_level
//                ,(select h.channel_name from crm_code_channel as h WHERE h.channel_id = c.channel_id) as channel_name
//                ,(select GROUP_CONCAT(t.track_note) from crm_client_track as t where t.client_id = c.client_id) as track_notes
//                ,c.client_birthday,c.client_age,s.school_id
//                FROM crm_client as c ,crm_client_schoolenter as s
//                WHERE c.company_id = '8888'
//                and c.client_id = s.client_id
//                and (c.client_birthday > '2020-09-01'
//                        or (c.client_age < 3 and c.client_age >= 0  and c.client_birthday	= '')
//                        )
//                and c.client_tracestatus < 4
//                and (s.school_id = '2322')
//                and s.is_enterstatus = '1'
//                and c.client_tracestatus <> '-2'
//
//                ORDER BY s.school_id
//                ";
        $sql = "SELECT c.client_id,c.client_cnname,c.client_mobile,c.client_sex,c.client_source,c.channel_id,c.client_intention_level
                ,(select h.channel_name from crm_code_channel as h WHERE h.channel_id = c.channel_id) as channel_name
                ,(select GROUP_CONCAT(t.track_note) from crm_client_track as t where t.client_id = c.client_id) as track_notes 
                ,c.client_birthday,c.client_age,s.school_id   
                FROM crm_client as c ,crm_client_schoolenter as s 
                WHERE c.company_id = '8888' 
                and c.client_id = s.client_id 
                and (c.client_birthday > '2020-09-01'
                        or (c.client_age < 3 and c.client_age >= 0  and c.client_birthday	= '')
                        ) 
                and c.client_tracestatus < 4 
                and (s.school_id = '2323') 
                and s.is_enterstatus = '1'  
                and c.client_tracestatus <> '-2'
                
                ORDER BY s.school_id  
                ";
        $dataList = $this->DataControl->selectClear($sql);
//die;
        if($dataList){
            foreach ($dataList as $dataVar){
                echo $dataVar['client_id'].'--';
                echo $dataVar['client_cnname'].'--';
                echo $dataVar['client_mobile'].'--';
                $data = array();
                $data['client_id'] = $dataVar['client_id'];
                $data['track_linktype'] = '校转园：旭辉和龙湖早教';
                $data['client_answerphone'] = 1;
                $data['track_validinc'] = 2;
                $data['track_followmode'] = '-2';
                $data['track_note'] = "喆哥：旭辉和龙湖早教数据处理相关事项,校务转无效名单。";
                $data['object_code'] = 'O04';

                $data['marketer_id'] = 15;
                $data['school_id'] = $dataVar['school_id'];
                $data['company_id'] = 8888;
                $data['staffer_id'] = 12357;
                $data['postbe_crmuserlevel'] = 1;

                $Model = new \Model\Crm\ClientModel($data);
                $bool = $Model->insertTrackClientOne($data);
                var_dump($Model->errortip);
            }
        }
        echo "OK";
    }


    //戚总让吧 excel_client 表数据处理下
    //现在tmk有两个诉求：
    //1、把这个表的备注补充到招生表的备注里面
    //2、把名单自动分配到对应的集团招生人员手上

    //负责人 操作人 主-1 副-0
    function addAllotLog($client_id, $main_marketer_id, $marketer_id, $school_id, $ismajor)
    {
        $dataAllotlog = array();
        $dataAllotlog['client_id'] = $client_id;
        $dataAllotlog['marketer_id'] = $marketer_id;
        $dataAllotlog['allot_marketer_id'] = $main_marketer_id;
        $dataAllotlog['allotlog_status'] = 1;
        $dataAllotlog['allotlog_ismajor'] = $ismajor; //0辅助招1主招
        $dataAllotlog['allotlog_createtime'] = time();
        $dataAllotlog['school_id'] = $school_id;
        if (!$this->DataControl->insertData('crm_client_allotlog', $dataAllotlog)) {
            return false;
        } else {
            return true;
        }
    }

    function delExcelClientApi(){
        $excelClienList = $this->DataControl->selectClear("select * from excel_client where client_id not in (154275,154290,154591,154700,154912) order by client_id ASC   ");
        print_r($excelClienList);die;
        if($excelClienList){
            $failList = array();
            foreach ($excelClienList as $excelClienVar){
//                $trackOne = $this->DataControl->selectOne("select track_followmode from crm_client_track where client_id = '{$excelClienVar['client_id']}' and track_note <> 'TMK名单，按照Saleface导出名单进行自动集团分配' order by track_id desc limit 0,1  ");
//                if($trackOne['track_followmode'] != '0' && !is_null($trackOne['track_followmode'])) {
//                    $data = array();
//                    $data['client_gmcdistributionstatus'] = 0;
//                    if($trackOne['track_followmode'] == '-3'){
//                        $data['client_tracestatus'] = '-2';
//                    }elseif($trackOne['track_followmode'] == '1'){
//                        $data['client_tracestatus'] = '2';
//                    }elseif($trackOne['track_followmode'] == '2'){
//                        $data['client_tracestatus'] = '3';
//                    }else {
//                        $data['client_tracestatus'] = $trackOne['track_followmode'];
//                    }
//                    $data['client_ischaserlapsed'] = 0;
//                    $data['client_updatetime'] = time();
//                    $data['client_remark'] = "";
//                    $this->DataControl->updateData("crm_client", "client_id='{$excelClienVar['client_id']}' and client_updatetime > '1641807000' and client_updatetime < '1641808500' ", $data);
//                }else{
//                    $data = array();
//                    $data['client_gmcdistributionstatus'] = 0;
//                    $data['client_tracestatus'] = 0;
//                    $data['client_ischaserlapsed'] = 0;
//                    $data['client_updatetime'] = time();
//                    $data['client_remark'] = "";
//                    $this->DataControl->updateData("crm_client", "client_id='{$excelClienVar['client_id']}' and client_updatetime > '1641807000' and client_updatetime < '1641808500' ", $data);
//                }


//                //删除跟踪记录
//                $this->DataControl->delData('crm_client_track', " client_id = '{$excelClienVar['client_id']}' and track_type = 1 and track_note = 'TMK名单，按照Saleface导出名单进行自动集团分配' ");
//                //删除分配的负责人
//                $this->DataControl->delData('crm_client_principal', " client_id = '{$excelClienVar['client_id']}' and school_id = '0' and principal_ismajor = '1' and principal_leave = '0' and principal_createtime > '1641807000' and principal_createtime < '1641808500'  ");
//                //删除分配的负责人
//                $this->DataControl->delData('crm_client_allotlog', " client_id = '{$excelClienVar['client_id']}' and allot_marketer_id = '{$excelClienVar['marketer_id']}' and marketer_id = '15' and school_id = '0' and allotlog_ismajor = '1' and allotlog_createtime > '1641807000' and allotlog_createtime < '1641808500'  ");
            }
        }
    }


    //针对台湾复制一家学校数据  开始 ------------------11------------- 开始

    //人事   职等名称 -- 导入企业数据同步（台湾）1111
    function oneupdatePostlevelApi(){
        echo '特定数据，已处理,请勿使用';die;
        $companyList = $this->DataControl->selectClear("select company_id from gmc_company WHERE company_id='79034' and company_language = 'tw' ");
        if($companyList){
            foreach ($companyList as $companyVar){
                $postlevelList = $this->DataControl->selectClear("select * from gmc_company_postlevel WHERE company_id = '78970' and postlevel_id='818' ");
                if($postlevelList){
                    foreach ($postlevelList as $postlevelVar) {
                        $data = array();
                        $data['company_id'] = $companyVar['company_id'];
                        $data['postlevel_cnname'] = $postlevelVar['postlevel_cnname'];
                        $data['postlevel_enname'] = $postlevelVar['postlevel_enname'];
                        $data['postlevel_remk'] = $postlevelVar['postlevel_remk'];
                        $this->DataControl->insertData('gmc_company_postlevel', $data);
                    }
                }
            }
        }else{
            ajax_return(array('error' => 1,'errortip' => "企业不存在"));
        }
    }

    //人事   校务角色管理 -- 导入企业数据同步（台湾） 11111
    function oneupdatePostpartApi(){
        echo '特定数据，已处理,请勿使用';die;
        $companyList = $this->DataControl->selectClear("select company_id from gmc_company WHERE company_id='79034' and company_language = 'tw' ");
        if($companyList){
            foreach ($companyList as $companyVar){
                $postpartList = $this->DataControl->selectClear("select p.*,t.*
                                from smc_school_postpart as p 
                                LEFT JOIN gmc_company_post as t ON p.postpart_id=t.postpart_id
                                WHERE p.company_id = '78970' 
                                and (p.postpart_id='2158' or p.postpart_id='2159' or  p.postpart_id='2160' or p.postpart_id='2161')  
                                and t.company_id = '78970' 
                                ");
                if($postpartList){
                    foreach ($postpartList as $postpartVar) {
                        $postpartone = $this->DataControl->selectOne(" select postpart_id from smc_school_postpart WHERE company_id = '{$companyVar['company_id']}' and postpart_name = '{$postpartVar['postpart_name']}'");
                        $postpartid = $postpartone['postpart_id'];
                        if($postpartid < 10) {
                            $data = array();
                            $data['company_id'] = $companyVar['company_id'];
                            $data['school_id'] = $postpartVar['school_id'];
                            $data['postpart_name'] = $postpartVar['postpart_name'];
                            $data['postpart_remark'] = $postpartVar['postpart_remark'];
                            $data['postpart_isschooluser'] = $postpartVar['postpart_isschooluser'];
                            $data['postpart_isteregulator'] = $postpartVar['postpart_isteregulator'];
                            $postpartid = $this->DataControl->insertData('smc_school_postpart', $data);
                        }

                        $postlevelOne = $this->DataControl->selectOne("select postlevel_id from gmc_company_postlevel WHERE company_id = '{$companyVar['company_id']}'");

                        $dataone = array();
                        $dataone['company_id'] = $companyVar['company_id'];
                        $dataone['postpart_id'] = $postpartid;
                        $dataone['postlevel_id'] = $postlevelOne['postlevel_id'];

                        $dataone['postrole_id'] = $postpartVar['postrole_id'];
                        $dataone['post_type'] = $postpartVar['post_type'];
                        $dataone['post_code'] = $postpartVar['post_code'];
                        $dataone['post_name'] = $postpartVar['post_name'];
                        $dataone['post_remk'] = $postpartVar['post_remk'];
                        $dataone['post_isrecrparttime'] = $postpartVar['post_isrecrparttime'];
                        $dataone['post_isteaching'] = $postpartVar['post_isteaching'];
                        $dataone['post_istopjob'] = $postpartVar['post_istopjob'];
                        $dataone['post_createtime'] = time();
                        $dataone['post_updatetime'] = time();
                        $this->DataControl->insertData('gmc_company_post', $dataone);

                    }
                }
            }
        }else{
            ajax_return(array('error' => 1,'errortip' => "企业不存在"));
        }
    }

    //人事    园务角色 -- 权限 -- 导入企业数据同步（台湾） 111  -- 补充角色编辑权限
    function oneupdateStafferUsermoduleApi(){
        echo '特定数据，已处理,请勿使用';die;
        $companyList = $this->DataControl->selectClear("select company_id from gmc_company WHERE company_id='79034' and company_language = 'tw' ");
        if($companyList){
            foreach ($companyList as $companyVar){
                $coursetimesList = $this->DataControl->selectClear("select u.*,(SELECT p.postpart_name FROM smc_school_postpart as p WHERE p.company_id ='78970' AND postpart_id = '2161') as postpart_name from smc_staffer_usermodule as u  WHERE company_id = '78970' and postpart_id = '2161' ");

                if($coursetimesList){
                    foreach ($coursetimesList as $key=>$coursetimesVar) {
                        $postpartOne = $this->DataControl->selectOne("SELECT postpart_id FROM smc_school_postpart WHERE postpart_name='{$coursetimesVar['postpart_name']}' and company_id = '{$companyVar['company_id']}' limit 0,1 ");
//                        if($key == '0') {
//                            $this->DataControl->delData('smc_staffer_usermodule', "company_id='{$companyVar['company_id']}' and postpart_id='{$postpartOne['postpart_id']}'");
//                            // and module_id='{$coursetimesVar['module_id']}'
//                        }
                        $data = array();
                        $data['company_id'] = $companyVar['company_id'];
                        $data['postpart_id'] = $postpartOne['postpart_id'];

                        $data['module_id'] = $coursetimesVar['module_id'];
                        $this->DataControl->insertData('smc_staffer_usermodule', $data);

//                        // //$this->DataControl->delData('kmc_staffer_usermodule', "company_id='{$companyVar['company_id']}' and postpart_id='{$postpartOne['postpart_id']}' and module_id='{$coursetimesVar['module_id']}' ");
                    }
                }
            }
        }else{
            ajax_return(array('error' => 1,'errortip' => "企业不存在"));
        }
    }

    //人事   教师类别 -- 导入企业数据同步（台湾） 1111
    function oneupdateTeachtypeApi(){
        echo '特定数据，已处理,请勿使用';die;
        $companyList = $this->DataControl->selectClear("select company_id from gmc_company WHERE company_id='79034' and company_language = 'tw' ");
        if($companyList){
            foreach ($companyList as $companyVar){
                $teachtypeList = $this->DataControl->selectClear("select * from smc_code_teachtype WHERE company_id = '78970'  ");
                if($teachtypeList){
                    foreach ($teachtypeList as $teachtypeVar) {
                        $data = array();
                        $data['company_id'] = $companyVar['company_id'];
                        $data['teachtype_code'] = $teachtypeVar['teachtype_code'];
                        $data['teachtype_name'] = $teachtypeVar['teachtype_name'];
                        $data['teachtype_remk'] = $teachtypeVar['teachtype_remk'];
                        $this->DataControl->insertData('smc_code_teachtype', $data);
                    }
                }
            }
        }else{
            ajax_return(array('error' => 1,'errortip' => "企业不存在"));
        }
    }

    //班务相关    學生異動原因管理 -- 导入企业数据同步（台湾） 1111
    function oneupdateStuchangeReasonApi(){
        echo '特定数据，已处理,请勿使用';die;
        $companyList = $this->DataControl->selectClear("select company_id from gmc_company WHERE company_id='79034' and company_language = 'tw' ");
        if($companyList){
            foreach ($companyList as $companyVar){
                $teachtypeList = $this->DataControl->selectClear("select * from smc_code_stuchange_reason WHERE company_id = '78970'  ");
                if($teachtypeList){
                    foreach ($teachtypeList as $teachtypeVar) {
                        $data = array();
                        $data['company_id'] = $companyVar['company_id'];
                        $data['reason_code'] = $teachtypeVar['reason_code'];
                        $data['reason_note'] = $teachtypeVar['reason_note'];
                        $data['stuchange_code'] = $teachtypeVar['stuchange_code'];
                        $this->DataControl->insertData('smc_code_stuchange_reason', $data);
                    }
                }
            }
        }else{
            ajax_return(array('error' => 1,'errortip' => "企业不存在"));
        }
    }

    //课程相关   同步课程类型 -- 导入企业数据同步（台湾）  -- 一个    （课程 -- 课程分类 --  课程） 1111
//    function oneupdateCourseTypeApi(){
//        echo '特定数据，已处理,请勿使用';die;
//        $companyList = $this->DataControl->selectClear("select company_id from gmc_company WHERE company_id='79034' and company_language = 'tw'  ");
//        if($companyList){
//            foreach ($companyList as $companyVar){
//                $coursetypeList = $this->DataControl->selectClear("select * from smc_code_coursetype WHERE company_id = '78970' and coursetype_id = '79009' ");
//                if($coursetypeList){
//                    foreach ($coursetypeList as $coursetypeVar) {
//                        $data = array();
//                        $data['company_id'] = $companyVar['company_id'];
//                        $data['coursetype_cnname'] = $coursetypeVar['coursetype_cnname'];
//                        $data['coursetype_branch'] = $coursetypeVar['coursetype_branch'];
//                        $data['coursetype_isopenclass'] = $coursetypeVar['coursetype_isopenclass'];
//                        $data['coursetype_remk'] = $coursetypeVar['coursetype_remk'];
////                        $data['coursetype_protocol'] = $coursetypeVar['coursetype_protocol'];
////                        $data['coursetype_refundinfo'] = $coursetypeVar['coursetype_refundinfo'];
////                        $data['coursetype_buyinfo'] = $coursetypeVar['coursetype_buyinfo'];
//                        $coursetypeid = $this->DataControl->insertData('smc_code_coursetype', $data);
//
//                        //同步班种管理 -- 一个
//                        $coursecatList = $this->DataControl->selectClear("select * from smc_code_coursecat WHERE company_id = '78970' and  coursecat_id = '10743' ");
//                        if($coursecatList) {
//                            foreach ($coursecatList as $coursecatVar) {
//                                $dataone = array();
//                                $dataone['company_id'] = $companyVar['company_id'];
//                                $dataone['coursetype_id'] = $coursetypeid;
//                                $dataone['coursecat_cnname'] = $coursecatVar['coursecat_cnname'];
//                                $dataone['coursecat_branch'] = $coursecatVar['coursecat_branch'];
//                                $dataone['coursecat_iscrmadded'] = $coursecatVar['coursecat_iscrmadded'];
//                                $dataone['coursecat_ismonth'] = $coursecatVar['coursecat_ismonth'];
//                                $coursecatid = $this->DataControl->insertData('smc_code_coursecat', $dataone);
//
//                                //同步课程管理 -- 一类
//                                $courseList = $this->DataControl->selectClear("select * from smc_course WHERE company_id = '78970' and  coursecat_id = '10743' ");
//                                if($courseList) {
//                                    foreach ($courseList as $courseVar) {
//                                        $datatwo = array();
//                                        $datatwo['company_id'] = $companyVar['company_id'];
//                                        $datatwo['coursetype_id'] = $coursetypeid;
//                                        $datatwo['coursecat_id'] = $coursecatid;
//                                        $datatwo['course_cnname'] = $courseVar['course_cnname'];
//                                        $datatwo['course_branch'] = $courseVar['course_branch'];
//                                        $datatwo['courseterm_id'] = $courseVar['courseterm_id'];
//                                        $datatwo['course_intro'] = $courseVar['course_intro'];
//                                        $datatwo['course_img'] = $courseVar['course_img'];
//                                        $datatwo['course_imglist'] = $courseVar['course_imglist'];
//                                        $datatwo['course_inclasstype'] = $courseVar['course_inclasstype'];
//                                        $datatwo['course_classnum'] = $courseVar['course_classnum'];
//                                        $datatwo['course_classtimes'] = $courseVar['course_classtimes'];
//                                        $datatwo['course_presentednums'] = $courseVar['course_presentednums'];
//                                        $datatwo['course_warningnums'] = $courseVar['course_warningnums'];
//                                        $datatwo['course_selltype'] = $courseVar['course_selltype'];
//                                        $datatwo['course_schedule'] = $courseVar['course_schedule'];
//                                        $datatwo['course_checkingintype'] = $courseVar['course_checkingintype'];
//                                        $datatwo['course_checkingminday'] = $courseVar['course_checkingminday'];
//                                        $datatwo['course_minabsencenum'] = $courseVar['course_minabsencenum'];
//                                        $datatwo['course_status'] = $courseVar['course_status'];
//                                        $datatwo['course_isrenew'] = $courseVar['course_isrenew'];
//                                        $datatwo['course_isopensonclass'] = $courseVar['course_isopensonclass'];
//                                        $datatwo['course_upgradesection'] = $courseVar['course_upgradesection'];
//                                        $datatwo['course_protocolnums'] = $courseVar['course_protocolnums'];
//                                        $datatwo['course_freenums'] = $courseVar['course_freenums'];
//                                        $datatwo['course_refundprice'] = $courseVar['course_refundprice'];
//                                        $datatwo['course_createtime'] = time();
//                                        $datatwo['course_updatatime'] = time();
//                                        $coursecatid = $this->DataControl->insertData('smc_course', $datatwo);
//                                    }
//                                }
//                            }
//                        }
//                    }
//                }
//            }
//        }else{
//            ajax_return(array('error' => 1,'errortip' => "企业不存在"));
//        }
//    }

    //课程相关   课程时数类型规则 -- 导入企业数据同步（台湾）1111
    function oneupdateCourseTimesApi(){
        echo '特定数据，已处理,请勿使用';die;
        $companyList = $this->DataControl->selectClear("select company_id from gmc_company WHERE company_id='79034' and company_language = 'tw' ");
        if($companyList){
            foreach ($companyList as $companyVar){
                $coursetimesList = $this->DataControl->selectClear("select * from smc_code_coursetimes WHERE company_id = '78970' ");
                if($coursetimesList){
                    foreach ($coursetimesList as $coursetimesVar) {
                        $data = array();
                        $data['company_id'] = $companyVar['company_id'];
                        $data['coursetimes_cnname'] = $coursetimesVar['coursetimes_cnname'];
                        $data['coursetimes_nums'] = $coursetimesVar['coursetimes_nums'];
                        $this->DataControl->insertData('smc_code_coursetimes', $data);
                    }
                }
            }
        }else{
            ajax_return(array('error' => 1,'errortip' => "企业不存在"));
        }
    }

    //课程收费设定 -- 导入企业数据同步（台湾）1111
    function oneupdateFeeAgreementApi(){
        echo '特定数据，已处理,请勿使用';die;
        $companyList = $this->DataControl->selectClear("select company_id from gmc_company WHERE company_id='79034' and company_language = 'tw'  ");
        if($companyList){
            foreach ($companyList as $companyVar){
                $agreementList = $this->DataControl->selectClear("select * from smc_fee_agreement WHERE company_id = '78970'  ");
                if($agreementList){
                    foreach ($agreementList as $agreementVar) {

                        $data = array();
                        $data['company_id'] = $companyVar['company_id'];
                        $data['agreement_cnname'] = $agreementVar['agreement_cnname'];
                        $data['agreement_startday'] = $agreementVar['agreement_startday'];
                        $data['agreement_endday'] = $agreementVar['agreement_endday'];
                        $data['agreement_intro'] = $agreementVar['agreement_intro'];
                        $data['agreement_status'] = $agreementVar['agreement_status'];
                        $data['agreement_createtime'] = time();
                        $agreementid = $this->DataControl->insertData('smc_fee_agreement', $data);

                        //收费项目定价明细
                        $pricinglist = $this->DataControl->selectClear("select p.*,s.course_branch,s.course_cnname from smc_fee_pricing as p 
                                        LEFT JOIN smc_course as s ON p.course_id = s.course_id 
                                        WHERE p.agreement_id = '{$agreementVar['agreement_id']}' and s.company_id = '78970' and s.course_branch LIKE 'JW%'");
                        if($pricinglist) {
                            foreach ($pricinglist as $pricingvar) {

                                $courseOne = $this->DataControl->selectOne("select course_id from smc_course WHERE course_branch = '{$pricingvar['course_branch']}' and course_cnname = '{$pricingvar['course_cnname']}' and company_id = '{$companyVar['company_id']}'");

                                $dataone = array();
                                $dataone['agreement_id'] = $agreementid;

                                $dataone['pricing_name'] = $pricingvar['pricing_name'];
                                $dataone['course_id'] = $courseOne['course_id'];

                                $dataone['acct_code'] = $pricingvar['acct_code'];
                                $dataone['pricing_applytype'] = $pricingvar['pricing_applytype'];
                                $dataone['pricing_addtime'] = time();
                                $pricingid = $this->DataControl->insertData('smc_fee_pricing', $dataone);

                                //课程定价-学费收费设定
                                $tuitionlist = $this->DataControl->selectClear("select t.*,s.course_branch,s.course_cnname 
                                      from smc_fee_pricing_tuition as t 
                                      LEFT JOIN smc_course as s ON t.course_id = s.course_id 
                                      WHERE t.pricing_id = '{$pricingvar['pricing_id']}' and s.company_id = '78970' and s.course_branch LIKE 'JW%'");
                                if($tuitionlist){
                                    foreach ($tuitionlist as $tuitionvar) {
                                        $courseTwo = $this->DataControl->selectOne("select course_id from smc_course WHERE course_branch = '{$tuitionvar['course_branch']}' and course_cnname = '{$tuitionvar['course_cnname']}' and company_id = '{$companyVar['company_id']}'");

                                        $datatwo = array();
                                        $datatwo['pricing_id'] = $pricingid;
                                        $datatwo['course_id'] = $courseTwo['course_id'];

                                        $datatwo['tuition_originalprice'] = $tuitionvar['tuition_originalprice'];
                                        $datatwo['tuition_sellingprice'] = $tuitionvar['tuition_sellingprice'];
                                        $datatwo['tuition_buypiece'] = $tuitionvar['tuition_buypiece'];
                                        $datatwo['tuition_donatepiece'] = $tuitionvar['tuition_donatepiece'];
                                        $datatwo['tuition_refundprice'] = $tuitionvar['tuition_refundprice'];
                                        $datatwo['tuition_unitprice'] = $tuitionvar['tuition_unitprice'];

                                        $datatwo['tuition_addtime'] = time();
                                        $tuitionid = $this->DataControl->insertData('smc_fee_pricing_tuition', $datatwo);
                                    }
                                }



                            }
                        }

                    }
                }
            }
        }else{
            ajax_return(array('error' => 1,'errortip' => "企业不存在"));
        }
    }

    //20210407 针对台湾 课程复制到其他集团
//    function updateCourseType0407Api(){
//        echo '特定数据，已处理,请勿使用';die;
////        $companyList = $this->DataControl->selectClear("select company_id from gmc_company WHERE company_id='79034' and company_language = 'tw'  ");
//        $companyList = $this->DataControl->selectClear("select company_id from gmc_company WHERE company_language = 'tw' and company_id <> '79034' ");
//        if($companyList){
//            $courseList = $this->DataControl->selectClear("select * from smc_course WHERE company_id = '79034' and  coursecat_id = '11226' and course_id >= '86441' and course_id <= '86449'");
//            if($courseList) {
//                foreach ($companyList as $companyVar) {
//                    $courseOne = $this->DataControl->selectOne("select * from smc_course WHERE company_id = '{$companyVar['company_id']}' and course_cnname = '美語班一級' and course_branch = 'JW01' limit 0,1 ");
//                    if ($courseOne) {
//                        foreach ($courseList as $courseVar) {
//                            $datatwo = array();
//                            $datatwo['company_id'] = $companyVar['company_id'];
//
//                            $datatwo['coursetype_id'] = $courseOne['coursetype_id'];
//                            $datatwo['coursecat_id'] = $courseOne['coursecat_id'];
//
//                            $datatwo['course_cnname'] = $courseVar['course_cnname'];
//                            $datatwo['course_branch'] = $courseVar['course_branch'];
//                            $datatwo['courseterm_id'] = $courseVar['courseterm_id'];
//                            $datatwo['course_intro'] = $courseVar['course_intro'];
//                            $datatwo['course_img'] = $courseVar['course_img'];
//                            $datatwo['course_imglist'] = $courseVar['course_imglist'];
//                            $datatwo['course_inclasstype'] = $courseVar['course_inclasstype'];
//                            $datatwo['course_classnum'] = $courseVar['course_classnum'];
//                            $datatwo['course_classtimes'] = $courseVar['course_classtimes'];
//                            $datatwo['course_presentednums'] = $courseVar['course_presentednums'];
//                            $datatwo['course_warningnums'] = $courseVar['course_warningnums'];
//                            $datatwo['course_selltype'] = $courseVar['course_selltype'];
//                            $datatwo['course_schedule'] = $courseVar['course_schedule'];
//                            $datatwo['course_checkingintype'] = $courseVar['course_checkingintype'];
//                            $datatwo['course_checkingminday'] = $courseVar['course_checkingminday'];
//                            $datatwo['course_minabsencenum'] = $courseVar['course_minabsencenum'];
//                            $datatwo['course_status'] = $courseVar['course_status'];
//                            $datatwo['course_isrenew'] = $courseVar['course_isrenew'];
//                            $datatwo['course_isopensonclass'] = $courseVar['course_isopensonclass'];
//                            $datatwo['course_upgradesection'] = $courseVar['course_upgradesection'];
//                            $datatwo['course_protocolnums'] = $courseVar['course_protocolnums'];
//                            $datatwo['course_freenums'] = $courseVar['course_freenums'];
//                            $datatwo['course_refundprice'] = $courseVar['course_refundprice'];
//                            $datatwo['course_createtime'] = time();
//                            $datatwo['course_updatatime'] = time();
//                            $coursecatid = $this->DataControl->insertData('smc_course', $datatwo);
//                        }
//                    }
//                }
//            }
//        }
//    }

//    //20210510 课程相关   同步课程类型 -- 导入企业数据同步（台湾）  -- 一个    （课程 -- 课程分类 --  课程） 1111
//    function oneupdateCourseTypeTwoApi(){
//        echo '特定数据，已处理,请勿使用';die;
//        $companyList = $this->DataControl->selectClear("select company_id from gmc_company WHERE company_id='79067' and company_language = 'tw'  ");
//        if($companyList){
//            foreach ($companyList as $companyVar){
//                $coursetypeList = $this->DataControl->selectClear("select * from smc_code_coursetype WHERE company_id = '79034' and coursetype_id = '79161' ");
//                if($coursetypeList){
//                    foreach ($coursetypeList as $coursetypeVar) {
//                        $data = array();
//                        $data['company_id'] = $companyVar['company_id'];
//                        $data['coursetype_cnname'] = $coursetypeVar['coursetype_cnname'];
//                        $data['coursetype_branch'] = $coursetypeVar['coursetype_branch'];
//                        $data['coursetype_isopenclass'] = $coursetypeVar['coursetype_isopenclass'];
//                        $data['coursetype_remk'] = $coursetypeVar['coursetype_remk'];
////                        $data['coursetype_protocol'] = $coursetypeVar['coursetype_protocol'];
////                        $data['coursetype_refundinfo'] = $coursetypeVar['coursetype_refundinfo'];
////                        $data['coursetype_buyinfo'] = $coursetypeVar['coursetype_buyinfo'];
//                        $coursetypeid = $this->DataControl->insertData('smc_code_coursetype', $data);
//
//                        //同步班种管理 -- 一个
//                        $coursecatList = $this->DataControl->selectClear("select * from smc_code_coursecat WHERE company_id = '79034' and  coursecat_id = '11226' ");
//                        if($coursecatList) {
//                            foreach ($coursecatList as $coursecatVar) {
//                                $dataone = array();
//                                $dataone['company_id'] = $companyVar['company_id'];
//                                $dataone['coursetype_id'] = $coursetypeid;
//                                $dataone['coursecat_cnname'] = $coursecatVar['coursecat_cnname'];
//                                $dataone['coursecat_branch'] = $coursecatVar['coursecat_branch'];
//                                $dataone['coursecat_iscrmadded'] = $coursecatVar['coursecat_iscrmadded'];
//                                $dataone['coursecat_ismonth'] = $coursecatVar['coursecat_ismonth'];
//                                $coursecatid = $this->DataControl->insertData('smc_code_coursecat', $dataone);
//
//                                //同步课程管理 -- 一类
//                                $courseList = $this->DataControl->selectClear("select * from smc_course WHERE company_id = '79034' and  coursecat_id = '11226' ");
//                                if($courseList) {
//                                    foreach ($courseList as $courseVar) {
//                                        $datatwo = array();
//                                        $datatwo['company_id'] = $companyVar['company_id'];
//                                        $datatwo['coursetype_id'] = $coursetypeid;
//                                        $datatwo['coursecat_id'] = $coursecatid;
//                                        $datatwo['course_cnname'] = $courseVar['course_cnname'];
//                                        $datatwo['course_branch'] = $courseVar['course_branch'];
//                                        $datatwo['courseterm_id'] = $courseVar['courseterm_id'];
//                                        $datatwo['course_intro'] = $courseVar['course_intro'];
//                                        $datatwo['course_img'] = $courseVar['course_img'];
//                                        $datatwo['course_imglist'] = $courseVar['course_imglist'];
//                                        $datatwo['course_inclasstype'] = $courseVar['course_inclasstype'];
//                                        $datatwo['course_classnum'] = $courseVar['course_classnum'];
//                                        $datatwo['course_classtimes'] = $courseVar['course_classtimes'];
//                                        $datatwo['course_presentednums'] = $courseVar['course_presentednums'];
//                                        $datatwo['course_warningnums'] = $courseVar['course_warningnums'];
//                                        $datatwo['course_selltype'] = $courseVar['course_selltype'];
//                                        $datatwo['course_schedule'] = $courseVar['course_schedule'];
//                                        $datatwo['course_checkingintype'] = $courseVar['course_checkingintype'];
//                                        $datatwo['course_checkingminday'] = $courseVar['course_checkingminday'];
//                                        $datatwo['course_minabsencenum'] = $courseVar['course_minabsencenum'];
//                                        $datatwo['course_status'] = $courseVar['course_status'];
//                                        $datatwo['course_isrenew'] = $courseVar['course_isrenew'];
//                                        $datatwo['course_isopensonclass'] = $courseVar['course_isopensonclass'];
//                                        $datatwo['course_upgradesection'] = $courseVar['course_upgradesection'];
//                                        $datatwo['course_protocolnums'] = $courseVar['course_protocolnums'];
//                                        $datatwo['course_freenums'] = $courseVar['course_freenums'];
//                                        $datatwo['course_refundprice'] = $courseVar['course_refundprice'];
//                                        $datatwo['course_createtime'] = time();
//                                        $datatwo['course_updatatime'] = time();
//                                        $coursecatid = $this->DataControl->insertData('smc_course', $datatwo);
//                                    }
//                                }
//                            }
//                        }
//                    }
//                }
//            }
//        }else{
//            ajax_return(array('error' => 1,'errortip' => "企业不存在"));
//        }
//    }


    //20210407 针对台湾 课程复制到其他集团   (针对课程)
//    function updateCourseType0510Api(){
//        echo '特定数据，已处理,请勿使用';die;
////        $companyList = $this->DataControl->selectClear("select company_id from gmc_company WHERE company_id='79034' and company_language = 'tw'  ");
//        $companyList = $this->DataControl->selectClear("select company_id from gmc_company WHERE company_language = 'tw' and company_id = '79067' ");
//        if($companyList){
//            $courseList = $this->DataControl->selectClear("select * from smc_course WHERE company_id = '79034' and  coursecat_id = '11226' and course_id >= '86441' and course_id <= '86449'");
//            if($courseList) {
//                foreach ($companyList as $companyVar) {
//                    $courseOne = $this->DataControl->selectOne("select * from smc_course WHERE company_id = '{$companyVar['company_id']}' and course_cnname = '美語班一級' and course_branch = 'JW01' limit 0,1 ");
//                    if ($courseOne) {
//                        foreach ($courseList as $courseVar) {
//                            $datatwo = array();
//                            $datatwo['company_id'] = $companyVar['company_id'];
//
//                            $datatwo['coursetype_id'] = $courseOne['coursetype_id'];
//                            $datatwo['coursecat_id'] = $courseOne['coursecat_id'];
//
//                            $datatwo['course_cnname'] = $courseVar['course_cnname'];
//                            $datatwo['course_branch'] = $courseVar['course_branch'];
//                            $datatwo['courseterm_id'] = $courseVar['courseterm_id'];
//                            $datatwo['course_intro'] = $courseVar['course_intro'];
//                            $datatwo['course_img'] = $courseVar['course_img'];
//                            $datatwo['course_imglist'] = $courseVar['course_imglist'];
//                            $datatwo['course_inclasstype'] = $courseVar['course_inclasstype'];
//                            $datatwo['course_classnum'] = $courseVar['course_classnum'];
//                            $datatwo['course_classtimes'] = $courseVar['course_classtimes'];
//                            $datatwo['course_presentednums'] = $courseVar['course_presentednums'];
//                            $datatwo['course_warningnums'] = $courseVar['course_warningnums'];
//                            $datatwo['course_selltype'] = $courseVar['course_selltype'];
//                            $datatwo['course_schedule'] = $courseVar['course_schedule'];
//                            $datatwo['course_checkingintype'] = $courseVar['course_checkingintype'];
//                            $datatwo['course_checkingminday'] = $courseVar['course_checkingminday'];
//                            $datatwo['course_minabsencenum'] = $courseVar['course_minabsencenum'];
//                            $datatwo['course_status'] = $courseVar['course_status'];
//                            $datatwo['course_isrenew'] = $courseVar['course_isrenew'];
//                            $datatwo['course_isopensonclass'] = $courseVar['course_isopensonclass'];
//                            $datatwo['course_upgradesection'] = $courseVar['course_upgradesection'];
//                            $datatwo['course_protocolnums'] = $courseVar['course_protocolnums'];
//                            $datatwo['course_freenums'] = $courseVar['course_freenums'];
//                            $datatwo['course_refundprice'] = $courseVar['course_refundprice'];
//                            $datatwo['course_createtime'] = time();
//                            $datatwo['course_updatatime'] = time();
//                            $coursecatid = $this->DataControl->insertData('smc_course', $datatwo);
//                        }
//                    }
//                }
//            }
//        }
//    }


    //针对台湾复制一家学校数据  结束 ------------------------ 结束



//
//    //同步课程类型 -- 导入企业数据同步（台湾）  -- 一个
//    function updateCourseTypeApi(){
//        echo '特定数据，已处理,请勿使用';die;
//        $companyList = $this->DataControl->selectClear("select company_id from gmc_company WHERE company_id>'78556' and company_id<'79023' and company_language = 'tw' and company_id <> '79022' ");
//        if($companyList){
//            foreach ($companyList as $companyVar){
//                $coursetypeList = $this->DataControl->selectClear("select * from smc_code_coursetype WHERE company_id = '78531' and coursetype_id = '78813' ");
//                if($coursetypeList){
//                    foreach ($coursetypeList as $coursetypeVar) {
//                        $data = array();
//                        $data['company_id'] = $companyVar['company_id'];
//                        $data['coursetype_cnname'] = $coursetypeVar['coursetype_cnname'];
//                        $data['coursetype_branch'] = $coursetypeVar['coursetype_branch'];
//                        $data['coursetype_isopenclass'] = $coursetypeVar['coursetype_isopenclass'];
//                        $data['coursetype_remk'] = $coursetypeVar['coursetype_remk'];
//                        $data['coursetype_protocol'] = $coursetypeVar['coursetype_protocol'];
//                        $data['coursetype_refundinfo'] = $coursetypeVar['coursetype_refundinfo'];
//                        $data['coursetype_buyinfo'] = $coursetypeVar['coursetype_buyinfo'];
//                        $coursetypeid = $this->DataControl->insertData('smc_code_coursetype', $data);
//
//                        //同步班种管理 -- 一个
//                        $coursecatList = $this->DataControl->selectClear("select * from smc_code_coursecat WHERE company_id = '78531' and  coursecat_id = '10611' ");
//                        if($coursecatList) {
//                            foreach ($coursecatList as $coursecatVar) {
//                                $dataone = array();
//                                $dataone['company_id'] = $companyVar['company_id'];
//                                $dataone['coursetype_id'] = $coursetypeid;
//                                $dataone['coursecat_cnname'] = $coursecatVar['coursecat_cnname'];
//                                $dataone['coursecat_branch'] = $coursecatVar['coursecat_branch'];
//                                $dataone['coursecat_iscrmadded'] = $coursecatVar['coursecat_iscrmadded'];
//                                $dataone['coursecat_ismonth'] = $coursecatVar['coursecat_ismonth'];
//                                $coursecatid = $this->DataControl->insertData('smc_code_coursecat', $dataone);
//
//                                //同步课程管理 -- 一类
//                                $courseList = $this->DataControl->selectClear("select * from smc_course WHERE company_id = '78531' and  coursecat_id = '10611' ");
//                                if($courseList) {
//                                    foreach ($courseList as $courseVar) {
//                                        $datatwo = array();
//                                        $datatwo['company_id'] = $companyVar['company_id'];
//                                        $datatwo['coursetype_id'] = $coursetypeid;
//                                        $datatwo['coursecat_id'] = $coursecatid;
//                                        $datatwo['course_cnname'] = $courseVar['course_cnname'];
//                                        $datatwo['course_branch'] = $courseVar['course_branch'];
//                                        $datatwo['courseterm_id'] = $courseVar['courseterm_id'];
//                                        $datatwo['course_intro'] = $courseVar['course_intro'];
//                                        $datatwo['course_img'] = $courseVar['course_img'];
//                                        $datatwo['course_imglist'] = $courseVar['course_imglist'];
//                                        $datatwo['course_inclasstype'] = $courseVar['course_inclasstype'];
//                                        $datatwo['course_classnum'] = $courseVar['course_classnum'];
//                                        $datatwo['course_classtimes'] = $courseVar['course_classtimes'];
//                                        $datatwo['course_presentednums'] = $courseVar['course_presentednums'];
//                                        $datatwo['course_warningnums'] = $courseVar['course_warningnums'];
//                                        $datatwo['course_selltype'] = $courseVar['course_selltype'];
//                                        $datatwo['course_schedule'] = $courseVar['course_schedule'];
//                                        $datatwo['course_checkingintype'] = $courseVar['course_checkingintype'];
//                                        $datatwo['course_checkingminday'] = $courseVar['course_checkingminday'];
//                                        $datatwo['course_minabsencenum'] = $courseVar['course_minabsencenum'];
//                                        $datatwo['course_status'] = $courseVar['course_status'];
//                                        $datatwo['course_isrenew'] = $courseVar['course_isrenew'];
//                                        $datatwo['course_isopensonclass'] = $courseVar['course_isopensonclass'];
//                                        $datatwo['course_upgradesection'] = $courseVar['course_upgradesection'];
//                                        $datatwo['course_protocolnums'] = $courseVar['course_protocolnums'];
//                                        $datatwo['course_freenums'] = $courseVar['course_freenums'];
//                                        $datatwo['course_refundprice'] = $courseVar['course_refundprice'];
//                                        $datatwo['course_createtime'] = time();
//                                        $datatwo['course_updatatime'] = time();
//                                        $coursecatid = $this->DataControl->insertData('smc_course', $datatwo);
//                                    }
//                                }
//                            }
//                        }
//                    }
//                }
//            }
//        }else{
//            ajax_return(array('error' => 1,'errortip' => "企业不存在"));
//        }
//    }
    //课时时间设置规则 -- 导入企业数据同步（台湾）
    function updateCourseTimesApi(){
        echo '特定数据，已处理,请勿使用';die;
        $companyList = $this->DataControl->selectClear("select company_id from gmc_company WHERE company_id>'78556' and company_id<'79023' and company_language = 'tw' ");
        if($companyList){
            foreach ($companyList as $companyVar){
                $coursetimesList = $this->DataControl->selectClear("select * from smc_code_coursetimes WHERE company_id = '78531' ");
                if($coursetimesList){
                    foreach ($coursetimesList as $coursetimesVar) {
                        $data = array();
                        $data['company_id'] = $companyVar['company_id'];
                        $data['coursetimes_cnname'] = $coursetimesVar['coursetimes_cnname'];
                        $data['coursetimes_nums'] = $coursetimesVar['coursetimes_nums'];
                        $this->DataControl->insertData('smc_code_coursetimes', $data);
                    }
                }
            }
        }else{
            ajax_return(array('error' => 1,'errortip' => "企业不存在"));
        }
    }

    //职等名称 -- 导入企业数据同步（台湾）
    function updatePostlevelApi(){
        echo '特定数据，已处理,请勿使用';die;
        $companyList = $this->DataControl->selectClear("select company_id from gmc_company WHERE company_id>'78556' and company_id<'79023' and company_language = 'tw' ");
        if($companyList){
            foreach ($companyList as $companyVar){
                $postlevelList = $this->DataControl->selectClear("select * from gmc_company_postlevel WHERE company_id = '78531' and postlevel_id='714' ");
                if($postlevelList){
                    foreach ($postlevelList as $postlevelVar) {
                        $data = array();
                        $data['company_id'] = $companyVar['company_id'];
                        $data['postlevel_cnname'] = $postlevelVar['postlevel_cnname'];
                        $data['postlevel_enname'] = $postlevelVar['postlevel_enname'];
                        $data['postlevel_remk'] = $postlevelVar['postlevel_remk'];
                        $this->DataControl->insertData('gmc_company_postlevel', $data);
                    }
                }
            }
        }else{
            ajax_return(array('error' => 1,'errortip' => "企业不存在"));
        }
    }

    //校务角色管理 -- 导入企业数据同步（台湾）
    function updatePostpartApi(){
        echo '特定数据，已处理,请勿使用';die;
        $companyList = $this->DataControl->selectClear("select company_id from gmc_company WHERE company_id>'78556' and company_id<'79023' and company_language = 'tw' ");
        if($companyList){
            foreach ($companyList as $companyVar){
                $postpartList = $this->DataControl->selectClear("select p.*,t.*
                                from smc_school_postpart as p 
                                LEFT JOIN gmc_company_post as t ON p.postpart_id=t.postpart_id
                                WHERE p.company_id = '78531' 
                                and (p.postpart_id='1124' or p.postpart_id='1125' or  p.postpart_id='1126' or p.postpart_id='1127') 
                                and t.post_id > '1589' and t.company_id = '78531' 
                                ");
                if($postpartList){
                    foreach ($postpartList as $postpartVar) {
                        $postpartone = $this->DataControl->selectOne(" select postpart_id from smc_school_postpart WHERE company_id = '{$companyVar['company_id']}' and postpart_name = '{$postpartVar['postpart_name']}'");
                        $postpartid = $postpartone['postpart_id'];
                        if($postpartid < 10) {
                            $data = array();
                            $data['company_id'] = $companyVar['company_id'];
                            $data['school_id'] = $postpartVar['school_id'];
                            $data['postpart_name'] = $postpartVar['postpart_name'];
                            $data['postpart_remark'] = $postpartVar['postpart_remark'];
                            $data['postpart_isschooluser'] = $postpartVar['postpart_isschooluser'];
                            $data['postpart_isteregulator'] = $postpartVar['postpart_isteregulator'];
                            $postpartid = $this->DataControl->insertData('smc_school_postpart', $data);
                        }

                        $postlevelOne = $this->DataControl->selectOne("select postlevel_id from gmc_company_postlevel WHERE company_id = '{$companyVar['company_id']}'");

                        $dataone = array();
                        $dataone['company_id'] = $companyVar['company_id'];
                        $dataone['postpart_id'] = $postpartid;
                        $dataone['postlevel_id'] = $postlevelOne['postlevel_id'];

                        $dataone['postrole_id'] = $postpartVar['postrole_id'];
                        $dataone['post_type'] = $postpartVar['post_type'];
                        $dataone['post_code'] = $postpartVar['post_code'];
                        $dataone['post_name'] = $postpartVar['post_name'];
                        $dataone['post_remk'] = $postpartVar['post_remk'];
                        $dataone['post_isrecrparttime'] = $postpartVar['post_isrecrparttime'];
                        $dataone['post_isteaching'] = $postpartVar['post_isteaching'];
                        $dataone['post_istopjob'] = $postpartVar['post_istopjob'];
                        $dataone['post_createtime'] = time();
                        $dataone['post_updatetime'] = time();
                        $this->DataControl->insertData('gmc_company_post', $dataone);

                    }
                }
            }
        }else{
            ajax_return(array('error' => 1,'errortip' => "企业不存在"));
        }
    }

    //职等名称 -- 导入企业数据同步（台湾）
    function updateTeachtypeApi(){
        echo '特定数据，已处理,请勿使用';die;
        $companyList = $this->DataControl->selectClear("select company_id from gmc_company WHERE company_id>'78556' and company_id<'79023' and company_language = 'tw' ");
        if($companyList){
            foreach ($companyList as $companyVar){
                $teachtypeList = $this->DataControl->selectClear("select * from smc_code_teachtype WHERE company_id = '78531'  ");
                if($teachtypeList){
                    foreach ($teachtypeList as $teachtypeVar) {
                        $data = array();
                        $data['company_id'] = $companyVar['company_id'];
                        $data['teachtype_code'] = $teachtypeVar['teachtype_code'];
                        $data['teachtype_name'] = $teachtypeVar['teachtype_name'];
                        $data['teachtype_remk'] = $teachtypeVar['teachtype_remk'];
                        $this->DataControl->insertData('smc_code_teachtype', $data);
                    }
                }
            }
        }else{
            ajax_return(array('error' => 1,'errortip' => "企业不存在"));
        }
    }

    //园务角色 -- 权限 -- 导入企业数据同步（台湾）
    function updateStafferUsermoduleApi(){
        echo '11';
        echo '特定数据，已处理,请勿使用';die;
        $companyList = $this->DataControl->selectClear("select company_id from gmc_company WHERE company_id>'78556' and company_id<'79023' and company_language = 'tw' ");
        if($companyList){
            foreach ($companyList as $companyVar){
                $coursetimesList = $this->DataControl->selectClear("select u.*,(SELECT p.postpart_name FROM smc_school_postpart as p WHERE p.company_id ='78531' AND postpart_id = '1127') as postpart_name from smc_staffer_usermodule as u  WHERE company_id = '78531' and postpart_id = '1127' ");

                if($coursetimesList){
                    foreach ($coursetimesList as $key=>$coursetimesVar) {
                        $postpartOne = $this->DataControl->selectOne("SELECT postpart_id FROM smc_school_postpart WHERE postpart_name='{$coursetimesVar['postpart_name']}' and company_id = '{$companyVar['company_id']}' limit 0,1 ");
                        if($key == '0') {
                            $this->DataControl->delData('smc_staffer_usermodule', "company_id='{$companyVar['company_id']}' and postpart_id='{$postpartOne['postpart_id']}'");
                            // and module_id='{$coursetimesVar['module_id']}'
                        }
                        $data = array();
                        $data['company_id'] = $companyVar['company_id'];
                        $data['postpart_id'] = $postpartOne['postpart_id'];

                        $data['module_id'] = $coursetimesVar['module_id'];
                        $this->DataControl->insertData('smc_staffer_usermodule', $data);

//                        // //$this->DataControl->delData('kmc_staffer_usermodule', "company_id='{$companyVar['company_id']}' and postpart_id='{$postpartOne['postpart_id']}' and module_id='{$coursetimesVar['module_id']}' ");
                    }
                }
            }
        }else{
            ajax_return(array('error' => 1,'errortip' => "企业不存在"));
        }
    }

    //课程对应的  班种班种的修改之前倒入有问题现在修改 -- 导入企业数据同步（台湾）
    function updateCourseApi(){
        echo '特定数据，已处理,请勿使用';die;
        $companyList = $this->DataControl->selectClear("select company_id from gmc_company WHERE company_id>'78556' and company_id<'79023' and company_language = 'tw' and company_id <> '79022'");
        if($companyList){
            foreach ($companyList as $companyVar){
                $teachtypeList = $this->DataControl->selectClear("select * from smc_course WHERE company_id = '{$companyVar['company_id']}'  ");
                $coursecatOne = $this->DataControl->selectOne("select * from smc_code_coursecat WHERE coursecat_cnname = '美語班' and  company_id = '{$companyVar['company_id']}' limit 0,1");
                if($teachtypeList){
                    foreach ($teachtypeList as $teachtypeVar) {
                        $data = array();
                        $data['coursecat_id'] = $coursecatOne['coursecat_id'];
                        $data['coursetype_id'] = $coursecatOne['coursetype_id'];
                        $this->DataControl->updateData('smc_course'," course_id= '{$teachtypeVar['course_id']}' and company_id = '{$companyVar['company_id']}' ", $data);
                    }
                }
            }
        }else{
            ajax_return(array('error' => 1,'errortip' => "企业不存在"));
        }
    }

    //學生異動原因管理 -- 导入企业数据同步（台湾）
    function updateStuchangeReasonApi(){
        echo '特定数据，已处理,请勿使用';die;
        $companyList = $this->DataControl->selectClear("select company_id from gmc_company WHERE company_id>'78556' and company_id<'79023' and company_language = 'tw' ");
        if($companyList){
            foreach ($companyList as $companyVar){
                $teachtypeList = $this->DataControl->selectClear("select * from smc_code_stuchange_reason WHERE company_id = '78531'  ");
                if($teachtypeList){
                    foreach ($teachtypeList as $teachtypeVar) {
                        $data = array();
                        $data['company_id'] = $companyVar['company_id'];
                        $data['reason_code'] = $teachtypeVar['reason_code'];
                        $data['reason_note'] = $teachtypeVar['reason_note'];
                        $data['stuchange_code'] = $teachtypeVar['stuchange_code'];
                        $this->DataControl->insertData('smc_code_stuchange_reason', $data);
                    }
                }
            }
        }else{
            ajax_return(array('error' => 1,'errortip' => "企业不存在"));
        }
    }
    //教师设置 -- 导入企业数据同步（台湾）
    function updateSmcClassroomApi(){
        echo '特定数据，已处理,请勿使用';die;
        $companyList = $this->DataControl->selectClear("select company_id from gmc_company WHERE company_id>'78556' and company_id<'79023' and company_language = 'tw' and company_id<>'79022' ");
        if($companyList){
            foreach ($companyList as $companyVar){
                $classroomList = $this->DataControl->selectClear("select * from smc_classroom WHERE company_id = '78531' and school_id = '2090'  ");
                if($classroomList){
                    foreach ($classroomList as $classroomVar) {
                        $schoollist = $this->DataControl->selectClear("select school_id from smc_school WHERE company_id = '{$companyVar['company_id']}'");

                        if($schoollist) {
                            foreach ($schoollist as $schoolvar) {
                                $data = array();
                                $data['company_id'] = $companyVar['company_id'];

                                $data['school_id'] = $schoolvar['school_id'];

                                $data['classroom_branch'] = $classroomVar['classroom_branch'];
                                $data['classroom_cnname'] = $classroomVar['classroom_cnname'];
                                $data['classroom_enname'] = $classroomVar['classroom_enname'];
                                $data['classroom_place'] = $classroomVar['classroom_place'];
                                $data['classroom_coursetype'] = $classroomVar['classroom_coursetype'];
                                $data['classroom_maxnums'] = $classroomVar['classroom_maxnums'];
                                $data['classroom_minnums'] = $classroomVar['classroom_minnums'];
                                $data['classroom_createtime'] = time();
                                $data['classroom_status'] = $classroomVar['classroom_status'];
                                $this->DataControl->insertData('smc_classroom', $data);
                            }
                        }
                    }
                }
            }
        }else{
            ajax_return(array('error' => 1,'errortip' => "企业不存在"));
        }
    }
    //教师设置 -- 导入企业数据同步（台湾）
    function updateFeeAgreementApi(){
        echo '特定数据，已处理,请勿使用';die;
        $companyList = $this->DataControl->selectClear("select company_id from gmc_company WHERE company_id>'78556' and company_id<'79023' and company_language = 'tw' and company_id <> '79022'");
        // and company_id<>'79022'
        if($companyList){
            foreach ($companyList as $companyVar){
                $agreementList = $this->DataControl->selectClear("select * from smc_fee_agreement WHERE company_id = '78531'  ");
                if($agreementList){
                    foreach ($agreementList as $agreementVar) {

                        $data = array();
                        $data['company_id'] = $companyVar['company_id'];
                        $data['agreement_cnname'] = $agreementVar['agreement_cnname'];
                        $data['agreement_startday'] = $agreementVar['agreement_startday'];
                        $data['agreement_endday'] = $agreementVar['agreement_endday'];
                        $data['agreement_intro'] = $agreementVar['agreement_intro'];
                        $data['agreement_status'] = $agreementVar['agreement_status'];
                        $data['agreement_createtime'] = time();
                        $agreementid = $this->DataControl->insertData('smc_fee_agreement', $data);

                        //收费项目定价明细
                        $pricinglist = $this->DataControl->selectClear("select p.*,s.course_branch,s.course_cnname from smc_fee_pricing as p 
                                        LEFT JOIN smc_course as s ON p.course_id = s.course_id 
                                        WHERE p.agreement_id = '{$agreementVar['agreement_id']}' and s.company_id = '78531' and s.course_branch LIKE 'JW%'");
                        if($pricinglist) {
                            foreach ($pricinglist as $pricingvar) {

                                $courseOne = $this->DataControl->selectOne("select course_id from smc_course WHERE course_branch = '{$pricingvar['course_branch']}' and course_cnname = '{$pricingvar['course_cnname']}' and company_id = '{$companyVar['company_id']}'");

                                $dataone = array();
                                $dataone['agreement_id'] = $agreementid;

                                $dataone['pricing_name'] = $pricingvar['pricing_name'];
                                $dataone['course_id'] = $courseOne['course_id'];

                                $dataone['acct_code'] = $pricingvar['acct_code'];
                                $dataone['pricing_applytype'] = $pricingvar['pricing_applytype'];
                                $dataone['pricing_addtime'] = time();
                                $pricingid = $this->DataControl->insertData('smc_fee_pricing', $dataone);

                                //课程定价-学费收费设定
                                $tuitionlist = $this->DataControl->selectClear("select t.*,s.course_branch,s.course_cnname 
                                      from smc_fee_pricing_tuition as t 
                                      LEFT JOIN smc_course as s ON t.course_id = s.course_id 
                                      WHERE t.pricing_id = '{$pricingvar['pricing_id']}' and s.company_id = '78531' and s.course_branch LIKE 'JW%'");
                                if($tuitionlist){
                                    foreach ($tuitionlist as $tuitionvar) {
                                        $courseTwo = $this->DataControl->selectOne("select course_id from smc_course WHERE course_branch = '{$tuitionvar['course_branch']}' and course_cnname = '{$tuitionvar['course_cnname']}' and company_id = '{$companyVar['company_id']}'");

                                        $datatwo = array();
                                        $datatwo['pricing_id'] = $pricingid;
                                        $datatwo['course_id'] = $courseTwo['course_id'];

                                        $datatwo['tuition_originalprice'] = $tuitionvar['tuition_originalprice'];
                                        $datatwo['tuition_sellingprice'] = $tuitionvar['tuition_sellingprice'];
                                        $datatwo['tuition_buypiece'] = $tuitionvar['tuition_buypiece'];
                                        $datatwo['tuition_donatepiece'] = $tuitionvar['tuition_donatepiece'];
                                        $datatwo['tuition_refundprice'] = $tuitionvar['tuition_refundprice'];
                                        $datatwo['tuition_unitprice'] = $tuitionvar['tuition_unitprice'];

                                        $datatwo['tuition_addtime'] = time();
                                        $tuitionid = $this->DataControl->insertData('smc_fee_pricing_tuition', $datatwo);
                                    }
                                }



                            }
                        }

                    }
                }
            }
        }else{
            ajax_return(array('error' => 1,'errortip' => "企业不存在"));
        }
    }

    //bella 给的数据需要导入优惠券
    function addNewCouponsApi(){//http://api.schoolapi102.com/Smc/addNewCouponsApi

        echo '已操作请勿重复操作';die;

        ini_set('max_execution_time', '0');
        $this->c = "111";
        $sqlarray = execl_to_array(ROOT_PATH."songjiang.xlsx",array('学生编号'=>'branch','优惠金额'=>'coupons_price','使用班种'=>'coursecat_branch'));
        array_shift($sqlarray);

        print_r($sqlarray);die;

        foreach($sqlarray as $sqlarrayvar){
            $studentOne = $this->DataControl->getFieldOne("smc_student", "student_id,student_branch,student_cnname", "student_branch = '{$sqlarrayvar['branch']}'");
            $couponsOne = $this->DataControl->getFieldOne("smc_student_coupons","coupons_id,coupons_pid,student_id","student_id = '{$studentOne['student_id']}' and (coupons_name = '迁校优惠券' or coupons_name = '新校优惠券')");
            if(!$couponsOne){
                $data = array();
                $data['parenter_id'] = '';//
                $data['company_id'] = 8888;//
                $data['school_id'] = 676;//
                $data['student_id'] = $studentOne['student_id'];//
                $data['applytype_branch'] = '500CHAIBANXIAO';//
                $data['apply_playclass'] = 0;//
                $data['apply_discountstype'] = '0';//
                $data['apply_price'] = $sqlarrayvar['coupons_price'];//
                $data['apply_discount'] = '';//
                $data['apply_minprice'] = 5000;//
                $data['apply_reson'] = 'Bella给的松江校拆搬校抵用券申请表格，生成优惠券申请！';//
                $data['apply_status'] = '4';//
                $data['staffer_id'] = '12357';
                $data['apply_time'] = time();
                $data['apply_remark'] = 'Bella给的松江校拆搬校抵用券申请表格，生成优惠券申请！';//
                $data['apply_refusetime'] = time();
                $apply_id = $this->DataControl->insertData('smc_student_coupons_apply',$data);

                $courselist = $this->DataControl->selectClear("select c.course_id from smc_course as c 
                              LEFT JOIN smc_code_coursecat as t ON c.coursecat_id = t.coursecat_id 
                              WHERE t.coursecat_branch = '{$sqlarrayvar['coursecat_branch']}' and t.company_id = '8888' AND c.coursecat_id != '' ");
                if($courselist) {
                    foreach ($courselist as $coursevar) {
                        if(!$this->DataControl->selectOne("select * from xxx WHERE apply_id = '{$apply_id}' and course_id = '{$coursevar['course_id']}'  limit 0,1")) {
                            $dataone = array();
                            $dataone['apply_id'] = $apply_id;//
                            $dataone['course_id'] = $coursevar['course_id'];//
                            $applycourse_id = $this->DataControl->insertData('xxx', $dataone);
                        }
                    }
                }

                $datatwo = array();
                do {
                    $couponspid_get = str_pad(mt_rand(*********, *********), 9, "0", STR_PAD_BOTH);
                } while ($this->DataControl->getFieldOne("smc_student_coupons", "coupons_id", "coupons_pid='{$couponspid_get}'"));
                $datatwo['company_id'] = 8888;
                $datatwo['apply_id'] = $apply_id;
                $datatwo['coupons_pid'] = $couponspid_get;
                $datatwo['student_id'] = $studentOne['student_id'];
                $datatwo['coupons_range'] = 0;
                $datatwo['coupons_class'] = 2;
                $datatwo['coupons_name'] = '迁校优惠券';
                $datatwo['coupons_type'] = 0;
                $datatwo['coupons_playclass'] = 0;
                $datatwo['coupons_reason'] = "Bella给的松江校拆搬校抵用券申请表格，生成优惠券申请！";
//                $datatwo['couponsrules_id'] = $applyOne['couponsrules_id'];
//                $datatwo['coupons_discount'] = $playinc;
                $datatwo['coupons_price'] = $sqlarrayvar['coupons_price'];
                $datatwo['coupons_minprice'] = 10000;
                $datatwo['coupons_bindingtime'] = strtotime('2020-08-01 00:00:01');
                $datatwo['coupons_exittime'] = strtotime('2021-08-31 23:59:59');
                $datatwo['coupons_createtime'] = time();
                $last_id = $this->DataControl->insertData("smc_student_coupons", $datatwo);
            }else{
                echo $studentOne['cnname']."|".$couponsOne['coupons_pid']."拆校优惠券已发布<br />";
            }
        }
    }

    //bella  给的数据需要导入优惠券  --  子龙转发我的  20210904
    function addWujingbaolongCouponsApi(){

        echo '已操作请勿重复操作';die;

        ini_set('max_execution_time', '0');
        $this->c = "111";
        $sqlarray = execl_to_array(ROOT_PATH."/bellajiuqi/wujinglongfabuchong.xlsx",array('学生编号'=>'branch','优惠金额'=>'coupons_price','使用班种'=>'coursecat_branch'));
        array_shift($sqlarray);

        print_r($sqlarray);die;

        foreach($sqlarray as $sqlarrayvar){
            $sqlarrayvar['branch'] = trim($sqlarrayvar['branch']);
            $sqlarrayvar['coupons_price'] = trim($sqlarrayvar['coupons_price']);
            $sqlarrayvar['coursecat_branch'] = trim($sqlarrayvar['coursecat_branch']);
            $studentOne = $this->DataControl->getFieldOne("smc_student", "student_id,student_branch,student_cnname", "student_branch = '{$sqlarrayvar['branch']}'");
            $couponsOne = $this->DataControl->getFieldOne("smc_student_coupons","coupons_id,coupons_pid,student_id","student_id = '{$studentOne['student_id']}' and (coupons_name = '迁校优惠券' or coupons_name = '新校优惠券')");
            if($studentOne){
                $data = array();
                $data['parenter_id'] = '';//
                $data['company_id'] = 8888;//
                $data['school_id'] = 684;//
                $data['student_id'] = $studentOne['student_id'];//
                $data['applytype_branch'] = '250CHAIBANXIAO';//
                $data['apply_playclass'] = 0;//
                $data['apply_discountstype'] = '0';//
                $data['apply_price'] = $sqlarrayvar['coupons_price'];//
                $data['apply_discount'] = '';//
                $data['apply_minprice'] = 4000;//
                $data['apply_reson'] = 'Bella给的吴泾宝龙的拆搬校抵用券申请表格，生成优惠券申请！';//
                $data['apply_status'] = '4';//
                $data['staffer_id'] = '12357';
                $data['apply_time'] = time();
                $data['apply_remark'] = 'Bella给的吴泾宝龙的拆搬校抵用券申请表格，生成优惠券申请！';//
                $data['apply_refusetime'] = time();
                $apply_id = $this->DataControl->insertData('smc_student_coupons_apply',$data);

                $courselist = $this->DataControl->selectClear("select c.course_id from smc_course as c 
                              LEFT JOIN smc_code_coursecat as t ON c.coursecat_id = t.coursecat_id 
                              WHERE t.coursecat_branch = '{$sqlarrayvar['coursecat_branch']}' and t.company_id = '8888' AND c.coursecat_id != '' ");
                if($courselist) {
                    foreach ($courselist as $coursevar) {
                        if(!$this->DataControl->selectOne("select * from xxx WHERE apply_id = '{$apply_id}' and course_id = '{$coursevar['course_id']}'  limit 0,1")) {
                            $dataone = array();
                            $dataone['apply_id'] = $apply_id;//
                            $dataone['course_id'] = $coursevar['course_id'];//
                            $applycourse_id = $this->DataControl->insertData('xxx', $dataone);
                        }
                    }
                }

                $datatwo = array();
                do {
                    $couponspid_get = str_pad(mt_rand(*********, *********), 9, "0", STR_PAD_BOTH);
                } while ($this->DataControl->getFieldOne("smc_student_coupons", "coupons_id", "coupons_pid='{$couponspid_get}'"));
                $datatwo['company_id'] = 8888;
                $datatwo['apply_id'] = $apply_id;
                $datatwo['coupons_pid'] = $couponspid_get;
                $datatwo['student_id'] = $studentOne['student_id'];
                $datatwo['coupons_range'] = 0;
                $datatwo['coupons_class'] = 2;
                $datatwo['coupons_name'] = '迁校优惠券';
                $datatwo['coupons_type'] = 0;
                $datatwo['coupons_playclass'] = 0;
                $datatwo['coupons_reason'] = "Bella给的吴泾宝龙的拆搬校抵用券申请表格，生成优惠券申请！";
//                $datatwo['couponsrules_id'] = $applyOne['couponsrules_id'];
//                $datatwo['coupons_discount'] = $playinc;
                $datatwo['coupons_price'] = $sqlarrayvar['coupons_price'];
                $datatwo['coupons_minprice'] = 4000;
                $datatwo['coupons_bindingtime'] = strtotime('2021-09-03 00:00:01');
                $datatwo['coupons_exittime'] = strtotime('2022-09-03 23:59:59');
                $datatwo['coupons_createtime'] = time();
                $last_id = $this->DataControl->insertData("smc_student_coupons", $datatwo);
            }else{
                echo $studentOne['cnname']."|".$couponsOne['coupons_pid']."拆校优惠券已发布<br />";
            }
        }
    }

    //bella 给的数据需要导入优惠券
    function addBaolongCouponsApi(){

        echo '已操作请勿重复操作';die;

        ini_set('max_execution_time', '0');
        $this->c = "111";
        $sqlarray = execl_to_array(ROOT_PATH."/bellajiuqi/yuepu.xlsx",array('学生编号'=>'branch','优惠金额'=>'coupons_price','使用班种'=>'coursecat_branch'));
        array_shift($sqlarray);

        print_r($sqlarray);die;

        foreach($sqlarray as $sqlarrayvar){
            $studentOne = $this->DataControl->getFieldOne("smc_student", "student_id,student_branch,student_cnname", "student_branch = '{$sqlarrayvar['branch']}'");
            $couponsOne = $this->DataControl->getFieldOne("smc_student_coupons","coupons_id,coupons_pid,student_id","student_id = '{$studentOne['student_id']}' and (coupons_name = '迁校优惠券' or coupons_name = '新校优惠券')");
            if(!$couponsOne){
                $data = array();
                $data['parenter_id'] = '';//
                $data['company_id'] = 8888;//
                $data['school_id'] = 661;//
                $data['student_id'] = $studentOne['student_id'];//
                $data['applytype_branch'] = '2000CHAIBANXIAO';//
                $data['apply_playclass'] = 0;//
                $data['apply_discountstype'] = '0';//
                $data['apply_price'] = $sqlarrayvar['coupons_price'];//
                $data['apply_discount'] = '';//
                $data['apply_minprice'] = 10000;//
                $data['apply_reson'] = 'Bella给的月浦校拆搬校抵用券申请表格，生成优惠券申请！';//
                $data['apply_status'] = '4';//
                $data['staffer_id'] = '12357';
                $data['apply_time'] = time();
                $data['apply_remark'] = 'Bella给的月浦校拆搬校抵用券申请表格，生成优惠券申请！';//
                $data['apply_refusetime'] = time();
                $apply_id = $this->DataControl->insertData('smc_student_coupons_apply',$data);

                $courselist = $this->DataControl->selectClear("select c.course_id from smc_course as c 
                              LEFT JOIN smc_code_coursecat as t ON c.coursecat_id = t.coursecat_id 
                              WHERE t.coursecat_branch = '{$sqlarrayvar['coursecat_branch']}' and t.company_id = '8888' AND c.coursecat_id != '' ");
                if($courselist) {
                    foreach ($courselist as $coursevar) {
                        if(!$this->DataControl->selectOne("select * from xxx WHERE apply_id = '{$apply_id}' and course_id = '{$coursevar['course_id']}'  limit 0,1")) {
                            $dataone = array();
                            $dataone['apply_id'] = $apply_id;//
                            $dataone['course_id'] = $coursevar['course_id'];//
                            $applycourse_id = $this->DataControl->insertData('xxx', $dataone);
                        }
                    }
                }

                $datatwo = array();
                do {
                    $couponspid_get = str_pad(mt_rand(*********, *********), 9, "0", STR_PAD_BOTH);
                } while ($this->DataControl->getFieldOne("smc_student_coupons", "coupons_id", "coupons_pid='{$couponspid_get}'"));
                $datatwo['company_id'] = 8888;
                $datatwo['apply_id'] = $apply_id;
                $datatwo['coupons_pid'] = $couponspid_get;
                $datatwo['student_id'] = $studentOne['student_id'];
                $datatwo['coupons_range'] = 0;
                $datatwo['coupons_class'] = 2;
                $datatwo['coupons_name'] = '迁校优惠券';
                $datatwo['coupons_type'] = 0;
                $datatwo['coupons_playclass'] = 0;
                $datatwo['coupons_reason'] = "Bella给的月浦校拆搬校抵用券申请表格，生成优惠券申请！";
//                $datatwo['couponsrules_id'] = $applyOne['couponsrules_id'];
//                $datatwo['coupons_discount'] = $playinc;
                $datatwo['coupons_price'] = $sqlarrayvar['coupons_price'];
                $datatwo['coupons_minprice'] = 10000;
                $datatwo['coupons_bindingtime'] = strtotime('2021-08-14 00:00:01');
                $datatwo['coupons_exittime'] = strtotime('2022-08-13 23:59:59');
                $datatwo['coupons_createtime'] = time();
                $last_id = $this->DataControl->insertData("smc_student_coupons", $datatwo);
            }else{
                echo $studentOne['cnname']."|".$couponsOne['coupons_pid']."拆校优惠券已发布<br />";
            }
        }
    }

    //bella 给的数据需要导入优惠券 -- 江川校补充发放部分优惠券，没有申请记录的
    function addSomeNewCouponsApi()
    {

        echo '已操作请勿重复操作';
        die;

        ini_set('max_execution_time', '0');
        $this->c = "111";
        $sqlarray = execl_to_array(ROOT_PATH . "jiangchuan.xlsx", array('学生编号' => 'branch', '优惠金额' => 'coupons_price', '使用班种' => 'coursecat_branch'));
        array_shift($sqlarray);

        print_r($sqlarray);
        die;

        foreach ($sqlarray as $sqlarrayvar) {
            $studentOne = $this->DataControl->getFieldOne("smc_student", "student_id,student_branch,student_cnname", "student_branch = '{$sqlarrayvar['branch']}'");
            $couponsOne = $this->DataControl->getFieldOne("smc_student_coupons", "coupons_id,coupons_pid,student_id", "student_id = '{$studentOne['student_id']}' and (coupons_name = '迁校优惠券' or coupons_name = '新校优惠券')");
            if (!$couponsOne) {
                $datatwo = array();
                do {
                    $couponspid_get = str_pad(mt_rand(*********, *********), 9, "0", STR_PAD_BOTH);
                } while ($this->DataControl->getFieldOne("smc_student_coupons", "coupons_id", "coupons_pid='{$couponspid_get}'"));
                $datatwo['company_id'] = 8888;
                $datatwo['apply_id'] = 0;
                $datatwo['coupons_pid'] = $couponspid_get;
                $datatwo['student_id'] = $studentOne['student_id'];
                $datatwo['coupons_range'] = 0;
                $datatwo['coupons_class'] = 3;
                $datatwo['coupons_name'] = '迁校优惠券';
                $datatwo['coupons_type'] = 0;
                $datatwo['coupons_playclass'] = 1;
                $datatwo['coupons_reason'] = "Bella给的江川校拆搬校抵用券申请表格，生成优惠券！";
//                $datatwo['couponsrules_id'] = $applyOne['couponsrules_id'];
//                $datatwo['coupons_discount'] = $playinc;
                $datatwo['coupons_price'] = 1000;
                $datatwo['coupons_minprice'] = 10000;
                $datatwo['coupons_bindingtime'] = strtotime('2020-07-26 00:00:01');
                $datatwo['coupons_exittime'] = strtotime('2021-01-31 23:59:59');
                $datatwo['coupons_createtime'] = time();
                $last_id = $this->DataControl->insertData("smc_student_coupons", $datatwo);
            } else {
                echo $studentOne['student_cnname'] . "|" . $couponsOne['coupons_pid'] . "拆校优惠券已发布<br />";
            }
        }
    }

    //bella 给的数据需要导入优惠券 -- 桃浦校补充发放部分优惠券，没有申请记录的
    function addSomeNewCouponsTwoApi()
    {

        echo '已操作请勿重复操作';die;

//        ini_set('max_execution_time', '0');
//        $this->c = "111";
//        $sqlarray = execl_to_array(ROOT_PATH."taopu.xlsx",array('学生编号'=>'branch','优惠金额'=>'coupons_price','使用班种'=>'coursecat_branch'));
//        array_shift($sqlarray);

        $sqlarray = $this->DataControl->selectClear("SELECT s.student_branch FROM smc_student as s 
                    WHERE  s.student_branch in 
                    ('2018051200014','2018052000024','2018063000062','2019012700037','2019022700076','2019030300058','2019110600109','2019110900122','2019111500012','2020011500007','2017121700027','2018012000013','2018022400023','2018022400027','2018031100046','2018031100048','2018071400005','2017091700004','2018081900050','2016082700041','2017061000069','2017081900007','2018040800023','2018091300136','2018091600024','2019021700006','2019112000020','2017042800024','2017042900006','2017090300039','2017092000031','2017092400013','2017101200022','2018040100027','2016091000004','2016112000025','2016120300028','2017070800050','2018021000001','2019111600052','2016050400017','2016050700038','2016070900025','2016073100003','2016080700035','2016081400054','2017010600007','2017010600008','2017011100033','2018053100071','2018101400025','2019110300178','2015032401744','2017021100050','2017071200078','2017101400072','2017102100033','2018020300026','2018031600015','2018063000147','2019031400097','2019052500001','2019112300008','2019022300072','2019031600074','2019051900030','2019051900026','2018063000180','2019062200049','2018063000133','2019070600064','2019080300001','2019070400164','2018010600004','2019090100070','2018112500022','2019022800167','2019090800049','2020062700007','2019031000053','2019042400026','2019051900022','2019052200051','2019052600015','2019062200070','2019062200072','2019062200080','2019062900122','2019071300061','2019071400066','2019080400005','2019111400044','20200708000087','2019071300068','2019071400018','2019071400029','2019081100018','2019090100150','2019090700114','2019092200017','2019092200059','2019092600005','2019110200071','2019111000107','2019062200031','2019081800009','2018083100216','2019051900020','2019060200023','2019060100067','2019062200067','2019062200073','2019062300102','2019071300074','2019072700019','2019110700097','2019082500029','2019090800022','2019091900066','2019101000057','2019102000010','2019102700012','2019083100140','2019111700080','2019122000071','2019103000014','2018123000022','2018040100023','2019122800048','2020011500043','2018062400091','2018063000014','2018111800023','2019110900116','2017042700014','2017121600022','2018022800065','2018050300014','2018062300002','2019083000282','2019091100157','2018102800038','2019113000002','2018050500063','2018052600016','2018052600018','2018060200029','2018081900041','2018082500003','2018083000216','2018090800008','2018090900038','2019062300093','2019111000050','2019111300070','2016022800117','2016030200148','2016033000001','2016061100006','2016061800010','2019062300088','2019062900077','2019062900078','20200712000086','20200712000009','20200711000093','20200710000042','20200704000103','20200704000020','2020062700086','2020062000080','2020061300057','2020060600027','2020050800036','2020040900048','2020040900047','2020042200010','2019102000028','2015112000027','2020011100029','2020010200082','2015060400002','2019122200014','2020011200001','2015032401602','2020011100032','2020011800034','2019113000041','2019111300059','2019111500087','2019112400020','2019111000324','2019111500017','2019111000084','2019111500007','2019111700056','2019111300061','2019111700005','2019070700018','2019090700123','2019110300035','2019110200126','2019081800015','2019092700011','2019081800009','2019092500002','2020011400027','2019042700092','2019062900120','2019040400056','2019062200031','2019052600028','2018102000015','2018061600022','2018081200043','2018111800022','2018063000062','2018102800039','2017110400051','2018051300023','2017101800002','2017122900005','2018051200014','2017120800033','2017111700040','2018040100023','2018060300062','2017082300103','2017070800015','2017081700028','2017080600022','2017091300040','2017052100008','2016030300073','2016092100104','2016091600013','2016090400156','2015091600065','2015060200004','2015060200009','2015032401678','2015032401760','2015060200007','2015082100030','2015103100015','2015032401541','2015032401756','2015060200006','2015101700014','2015032401514','2015032401658','2015060200005','2015032401393','2015032401487','2015032401508','2015032401465','2015032401503','2015032401440','2015032401498') ");

        print_r($sqlarray);die;

        foreach($sqlarray as $sqlarrayvar){
            $studentOne = $this->DataControl->getFieldOne("smc_student", "student_id,student_branch,student_cnname", "student_branch = '{$sqlarrayvar['student_branch']}'");
            $couponsOne = $this->DataControl->getFieldOne("smc_student_coupons","coupons_id,coupons_pid,student_id","student_id = '{$studentOne['student_id']}' and (coupons_name = '迁校优惠券' or coupons_name = '新校优惠券')");
            if(!$couponsOne){
                $data = array();
                $data['parenter_id'] = '';//
                $data['company_id'] = 8888;//
                $data['school_id'] = 672;//
                $data['student_id'] = $studentOne['student_id'];//
                $data['applytype_branch'] = '1000TPCHAIQIANXIAO';//
                $data['apply_playclass'] = 0;//
                $data['apply_discountstype'] = '0';//
                $data['apply_price'] = $sqlarrayvar['coupons_price'];//
                $data['apply_discount'] = '';//
                $data['apply_minprice'] = 10000;//
                $data['apply_reson'] = 'Bella给的桃浦校拆搬校抵用券申请表格，生成优惠券申请！';//
                $data['apply_status'] = '4';//
                $data['staffer_id'] = '12357';
                $data['apply_time'] = time();
                $data['apply_remark'] = 'Bella给的桃浦校拆搬校抵用券申请表格，生成优惠券申请！';//
                $data['apply_refusetime'] = time();
                $apply_id = $this->DataControl->insertData('smc_student_coupons_apply',$data);

                $datatwo = array();
                do {
                    $couponspid_get = str_pad(mt_rand(*********, *********), 9, "0", STR_PAD_BOTH);
                } while ($this->DataControl->getFieldOne("smc_student_coupons", "coupons_id", "coupons_pid='{$couponspid_get}'"));
                $datatwo['company_id'] = 8888;
                $datatwo['apply_id'] = $apply_id;
                $datatwo['coupons_pid'] = $couponspid_get;
                $datatwo['student_id'] = $studentOne['student_id'];
                $datatwo['coupons_range'] = 0;
                $datatwo['coupons_class'] = 2;
                $datatwo['coupons_name'] = '迁校优惠券';
                $datatwo['coupons_type'] = 0;
                $datatwo['coupons_playclass'] = 0;
                $datatwo['coupons_reason'] = "Bella给的桃浦校拆搬校抵用券申请表格，生成优惠券申请！";
//                $datatwo['couponsrules_id'] = $applyOne['couponsrules_id'];
//                $datatwo['coupons_discount'] = $playinc;
                $datatwo['coupons_price'] = 1000;
                $datatwo['coupons_minprice'] = 10000;
                $datatwo['coupons_bindingtime'] = strtotime('2020-08-01 00:00:01');
                $datatwo['coupons_exittime'] = strtotime('2021-07-31 23:59:59');
                $datatwo['coupons_createtime'] = time();
                $last_id = $this->DataControl->insertData("smc_student_coupons", $datatwo);
            }else{
                echo $studentOne['student_id']."|".$studentOne['cnname']."|".$couponsOne['coupons_pid']."拆校优惠券已发布<br />";
            }
        }
    }

    //bella 给的数据需要导入优惠券 -- 桃浦校（第二次）补充发放部分优惠券，没有申请记录的
    function addSomeNewCouponsThreeApi()
    {

        echo '已操作请勿重复操作';die;

        ini_set('max_execution_time', '0');
        $this->c = "111";
        $sqlarray = execl_to_array(ROOT_PATH."/bellajiuqi/taoputwo.xlsx",array('学生编号'=>'branch','优惠金额'=>'coupons_price','班种说明'=>'coursecat_branch','开始日期'=>'coupons_bindingtime','截止日期'=>'coupons_exittime'));
        array_shift($sqlarray);

//        foreach($sqlarray as $sqlarrayvar){
//            echo strtotime($sqlarrayvar['coupons_exittime'])+86399;
//        }
        print_r($sqlarray);
        die;

        foreach($sqlarray as $sqlarrayvar){
            $sqlarrayvar['student_branch'] = trim($sqlarrayvar['branch']);
            $studentOne = $this->DataControl->getFieldOne("smc_student", "student_id,student_branch,student_cnname", "student_branch = '{$sqlarrayvar['student_branch']}'");
            $couponsOne = $this->DataControl->getFieldOne("smc_student_coupons","coupons_id,coupons_pid,student_id","student_id = '{$studentOne['student_id']}' and (coupons_name = '迁校优惠券' or coupons_name = '新校优惠券')");

            print_r($studentOne);

            if(!$couponsOne){
                $data = array();
                $data['parenter_id'] = '';//
                $data['company_id'] = 8888;//
                $data['school_id'] = 672;//
                $data['student_id'] = $studentOne['student_id'];//
                $data['applytype_branch'] = '1000TPCHAIQIANXIAO';//
                $data['apply_playclass'] = 0;//
                $data['apply_discountstype'] = '0';//
                $data['apply_price'] = $sqlarrayvar['coupons_price'];//
                $data['apply_discount'] = '';//
                $data['apply_minprice'] = 10000;//
                $data['apply_reson'] = 'Bella给的桃浦校拆搬校抵用券申请表格(二)，生成优惠券申请！';//
                $data['apply_status'] = '4';//
                $data['staffer_id'] = '12357';
                $data['apply_time'] = time();
                $data['apply_remark'] = 'Bella给的桃浦校拆搬校抵用券申请表格(二)，生成优惠券申请！';//
                $data['apply_refusetime'] = time();
                $apply_id = $this->DataControl->insertData('smc_student_coupons_apply',$data);

                //对应的班种
                $courselist = $this->DataControl->selectClear("select c.course_id from smc_course as c
                              LEFT JOIN smc_code_coursecat as t ON c.coursecat_id = t.coursecat_id
                              WHERE t.coursecat_branch = '{$sqlarrayvar['coursecat_branch']}' and t.company_id = '8888' AND c.coursecat_id != '' ");
                if($courselist) {
                    foreach ($courselist as $coursevar) {
                        if(!$this->DataControl->selectOne("select * from xxx WHERE apply_id = '{$apply_id}' and course_id = '{$coursevar['course_id']}'  limit 0,1")) {
                            $dataone = array();
                            $dataone['apply_id'] = $apply_id;//
                            $dataone['course_id'] = $coursevar['course_id'];//
                            $applycourse_id = $this->DataControl->insertData('xxx', $dataone);
                        }
                    }
                }

                $datatwo = array();
                do {
                    $couponspid_get = str_pad(mt_rand(*********, *********), 9, "0", STR_PAD_BOTH);
                } while ($this->DataControl->getFieldOne("smc_student_coupons", "coupons_id", "coupons_pid='{$couponspid_get}'"));
                $datatwo['company_id'] = 8888;
                $datatwo['apply_id'] = $apply_id;
                $datatwo['coupons_pid'] = $couponspid_get;
                $datatwo['student_id'] = $studentOne['student_id'];
                $datatwo['coupons_range'] = 0;
                $datatwo['coupons_class'] = 2;
                $datatwo['coupons_name'] = '迁校优惠券';
                $datatwo['coupons_type'] = 0;
                $datatwo['coupons_playclass'] = 0;
                $datatwo['coupons_reason'] = "Bella给的桃浦校拆搬校抵用券申请表格(二)，生成优惠券申请！";
//                $datatwo['couponsrules_id'] = $applyOne['couponsrules_id'];
//                $datatwo['coupons_discount'] = $playinc;
                $datatwo['coupons_price'] = 1000;
                $datatwo['coupons_minprice'] = 10000;
                $datatwo['coupons_bindingtime'] = strtotime($sqlarrayvar['coupons_bindingtime'])+86399;
                $datatwo['coupons_exittime'] = strtotime($sqlarrayvar['coupons_exittime'])+86399;
                $datatwo['coupons_createtime'] = time();
                $last_id = $this->DataControl->insertData("smc_student_coupons", $datatwo);
            }else{
                echo $studentOne['student_id']."|".$studentOne['cnname']."|".$couponsOne['coupons_pid']."拆校优惠券已发布<br />";
            }
        }
    }

    //bella 给的数据需要导入优惠券 -- 川沙校补充发放部分优惠券，没有申请记录的 -- 20200928
    function addSomeNewCouponsChuanshaApi()
    {
        echo '已操作请勿重复操作';die;

        ini_set('max_execution_time', '0');
        $this->c = "111";
        $sqlarray = execl_to_array(ROOT_PATH."/bellajiuqi/chuansha.xlsx",array('学生编号'=>'branch','优惠金额'=>'coupons_price','使用班种'=>'coursecat_branch','开始日期'=>'coupons_bindingtime','截止日期'=>'coupons_exittime'));
        array_shift($sqlarray);

        print_r($sqlarray);
        die;

        foreach($sqlarray as $sqlarrayvar){
            $sqlarrayvar['student_branch'] = trim($sqlarrayvar['branch']);
            $studentOne = $this->DataControl->getFieldOne("smc_student", "student_id,student_branch,student_cnname", "student_branch = '{$sqlarrayvar['student_branch']}'");
            $couponsOne = $this->DataControl->getFieldOne("smc_student_coupons","coupons_id,coupons_pid,student_id","student_id = '{$studentOne['student_id']}' and (coupons_name = '迁校优惠券' or coupons_name = '新校优惠券')");

//            print_r($studentOne);die;

            if(!$couponsOne){
                $data = array();
                $data['parenter_id'] = '';//
                $data['company_id'] = 8888;//
                $data['school_id'] = 652;//
                $data['student_id'] = $studentOne['student_id'];//
                $data['applytype_branch'] = '1000TPCHAIQIANXIAO';//
                $data['apply_playclass'] = 0;//
                $data['apply_discountstype'] = '0';//
                $data['apply_price'] = $sqlarrayvar['coupons_price'];//
                $data['apply_discount'] = '';//
                $data['apply_minprice'] = 10000;//
                $data['apply_reson'] = 'Bella给的川沙校拆搬校抵用券申请表格，生成优惠券申请！';//
                $data['apply_status'] = '4';//
                $data['staffer_id'] = '12357';
                $data['apply_time'] = time();
                $data['apply_remark'] = 'Bella给的川沙校拆搬校抵用券申请表格，生成优惠券申请！';//
                $data['apply_refusetime'] = time();
                $apply_id = $this->DataControl->insertData('smc_student_coupons_apply',$data);

                //对应的班种
                $courselist = $this->DataControl->selectClear("select c.course_id from smc_course as c
                              LEFT JOIN smc_code_coursecat as t ON c.coursecat_id = t.coursecat_id
                              WHERE t.coursecat_branch = '{$sqlarrayvar['coursecat_branch']}' and t.company_id = '8888' AND c.coursecat_id != '' ");
                if($courselist) {
                    foreach ($courselist as $coursevar) {
                        if(!$this->DataControl->selectOne("select * from xxx WHERE apply_id = '{$apply_id}' and course_id = '{$coursevar['course_id']}'  limit 0,1")) {
                            $dataone = array();
                            $dataone['apply_id'] = $apply_id;//
                            $dataone['course_id'] = $coursevar['course_id'];//
                            $applycourse_id = $this->DataControl->insertData('xxx', $dataone);
                        }
                    }
                }

                $datatwo = array();
                do {
                    $couponspid_get = str_pad(mt_rand(*********, *********), 9, "0", STR_PAD_BOTH);
                } while ($this->DataControl->getFieldOne("smc_student_coupons", "coupons_id", "coupons_pid='{$couponspid_get}'"));
                $datatwo['company_id'] = 8888;
                $datatwo['apply_id'] = $apply_id;
                $datatwo['coupons_pid'] = $couponspid_get;
                $datatwo['student_id'] = $studentOne['student_id'];
                $datatwo['coupons_range'] = 0;
                $datatwo['coupons_class'] = 2;
                $datatwo['coupons_name'] = '迁校优惠券';
                $datatwo['coupons_type'] = 0;
                $datatwo['coupons_playclass'] = 0;
                $datatwo['coupons_reason'] = "Bella给的川沙校拆搬校抵用券申请表格，生成优惠券申请！";
//                $datatwo['couponsrules_id'] = $applyOne['couponsrules_id'];
//                $datatwo['coupons_discount'] = $playinc;
                $datatwo['coupons_price'] = 1000;
                $datatwo['coupons_minprice'] = 10000;
                $datatwo['coupons_bindingtime'] = strtotime('2020-09-01');
                $datatwo['coupons_exittime'] = strtotime('2021-09-01')+86399;
                $datatwo['coupons_createtime'] = time();
                $last_id = $this->DataControl->insertData("smc_student_coupons", $datatwo);
            }else{
                echo $studentOne['student_id']."|".$studentOne['cnname']."|".$couponsOne['coupons_pid']."拆校优惠券已发布<br />";
            }
        }
    }
    //bella 给的数据需要导入优惠券 -- 川沙校补充发放部分优惠券，没有申请记录的 -- 20200928
    function addSomeNewCouponsChuanshatwoApi()
    {
        echo '已操作请勿重复操作';die;

        ini_set('max_execution_time', '0');
        $this->c = "111";
        $sqlarray = execl_to_array(ROOT_PATH."/bellajiuqi/chuanshatwo.xlsx",array('学生编号'=>'branch','优惠金额'=>'coupons_price','使用班种'=>'coursecat_branch','开始日期'=>'coupons_bindingtime','截止日期'=>'coupons_exittime'));
        array_shift($sqlarray);

        print_r($sqlarray);
        die;

        foreach($sqlarray as $sqlarrayvar){
            $sqlarrayvar['student_branch'] = trim($sqlarrayvar['branch']);
            $studentOne = $this->DataControl->getFieldOne("smc_student", "student_id,student_branch,student_cnname", "student_branch = '{$sqlarrayvar['student_branch']}'");
            $couponsOne = $this->DataControl->getFieldOne("smc_student_coupons","coupons_id,coupons_pid,student_id","student_id = '{$studentOne['student_id']}' and (coupons_name = '迁校优惠券' or coupons_name = '新校优惠券')");

//            print_r($studentOne);die;

            if(!$couponsOne){
                $data = array();
                $data['parenter_id'] = '';//
                $data['company_id'] = 8888;//
                $data['school_id'] = 652;//
                $data['student_id'] = $studentOne['student_id'];//
                $data['applytype_branch'] = '1000TPCHAIQIANXIAO';//
                $data['apply_playclass'] = 0;//
                $data['apply_discountstype'] = '0';//
                $data['apply_price'] = $sqlarrayvar['coupons_price'];//
                $data['apply_discount'] = '';//
                $data['apply_minprice'] = 10000;//
                $data['apply_reson'] = 'Bella给的川沙校拆搬校抵用券申请表格，生成优惠券申请！';//
                $data['apply_status'] = '4';//
                $data['staffer_id'] = '12357';
                $data['apply_time'] = time();
                $data['apply_remark'] = 'Bella给的川沙校拆搬校抵用券申请表格，生成优惠券申请！';//
                $data['apply_refusetime'] = time();
                $apply_id = $this->DataControl->insertData('smc_student_coupons_apply',$data);

                //对应的班种
                $courselist = $this->DataControl->selectClear("select c.course_id from smc_course as c
                              LEFT JOIN smc_code_coursecat as t ON c.coursecat_id = t.coursecat_id
                              WHERE t.coursecat_branch = '{$sqlarrayvar['coursecat_branch']}' and t.company_id = '8888' AND c.coursecat_id != '' ");
                if($courselist) {
                    foreach ($courselist as $coursevar) {
                        if(!$this->DataControl->selectOne("select * from xxx WHERE apply_id = '{$apply_id}' and course_id = '{$coursevar['course_id']}'  limit 0,1")) {
                            $dataone = array();
                            $dataone['apply_id'] = $apply_id;//
                            $dataone['course_id'] = $coursevar['course_id'];//
                            $applycourse_id = $this->DataControl->insertData('xxx', $dataone);
                        }
                    }
                }

                $datatwo = array();
                do {
                    $couponspid_get = str_pad(mt_rand(*********, *********), 9, "0", STR_PAD_BOTH);
                } while ($this->DataControl->getFieldOne("smc_student_coupons", "coupons_id", "coupons_pid='{$couponspid_get}'"));
                $datatwo['company_id'] = 8888;
                $datatwo['apply_id'] = $apply_id;
                $datatwo['coupons_pid'] = $couponspid_get;
                $datatwo['student_id'] = $studentOne['student_id'];
                $datatwo['coupons_range'] = 0;
                $datatwo['coupons_class'] = 2;
                $datatwo['coupons_name'] = '迁校优惠券';
                $datatwo['coupons_type'] = 0;
                $datatwo['coupons_playclass'] = 0;
                $datatwo['coupons_reason'] = "Bella给的川沙校拆搬校抵用券申请表格，生成优惠券申请！";
//                $datatwo['couponsrules_id'] = $applyOne['couponsrules_id'];
//                $datatwo['coupons_discount'] = $playinc;
                $datatwo['coupons_price'] = $sqlarrayvar['coupons_price'];
                $datatwo['coupons_minprice'] = 10000;
                $datatwo['coupons_bindingtime'] = strtotime('2020-09-01');
                $datatwo['coupons_exittime'] = strtotime('2021-09-01')+86399;
                $datatwo['coupons_createtime'] = time();
                $last_id = $this->DataControl->insertData("smc_student_coupons", $datatwo);
            }else{
                echo $studentOne['student_id']."|".$studentOne['cnname']."|".$couponsOne['coupons_pid']."拆校优惠券已发布<br />";
            }
        }
    }

    //bella 给的数据需要导入优惠券 -- 杜鹃校补充发放部分优惠券，没有申请记录的 -- 20201011
    function addSomeNewCouponsDujuanApi()
    {
        echo '已操作请勿重复操作';die;

        ini_set('max_execution_time', '0');
        $this->c = "111";
        $sqlarray = execl_to_array(ROOT_PATH."/bellajiuqi/dujuan.xlsx",array('学员编号'=>'branch','优惠金额'=>'coupons_price','使用班种'=>'coursecat_branch','开始日期'=>'coupons_bindingtime','截止日期'=>'coupons_exittime'));
        array_shift($sqlarray);
        $school_id = '677';

        print_r($sqlarray);
        die;

        foreach($sqlarray as $sqlarrayvar){
            $sqlarrayvar['student_branch'] = trim($sqlarrayvar['branch']);
            $studentOne = $this->DataControl->getFieldOne("smc_student", "student_id,student_branch,student_cnname", "student_branch = '{$sqlarrayvar['student_branch']}'");
            $couponsOne = $this->DataControl->getFieldOne("smc_student_coupons","coupons_id,coupons_pid,student_id","student_id = '{$studentOne['student_id']}' and (coupons_name = '迁校优惠券' or coupons_name = '新校优惠券')");

//            print_r($studentOne);die;

            if(!$couponsOne){
                $data = array();
                $data['parenter_id'] = '';//
                $data['company_id'] = 8888;//
                $data['school_id'] = $school_id;//
                $data['student_id'] = $studentOne['student_id'];//
                $data['applytype_branch'] = '1000TPCHAIQIANXIAO';//
                $data['apply_playclass'] = 0;//
                $data['apply_discountstype'] = '0';//
                $data['apply_price'] = $sqlarrayvar['coupons_price'];//
                $data['apply_discount'] = '';//
                $data['apply_minprice'] = 10000;//
                $data['apply_reson'] = 'Bella给的杜鹃校拆搬校抵用券申请表格，生成优惠券申请！';//
                $data['apply_status'] = '4';//
                $data['staffer_id'] = '12357';
                $data['apply_time'] = time();
                $data['apply_remark'] = 'Bella给的杜鹃校拆搬校抵用券申请表格，生成优惠券申请！';//
                $data['apply_refusetime'] = time();
                $apply_id = $this->DataControl->insertData('smc_student_coupons_apply',$data);

                //对应的班种
                $courselist = $this->DataControl->selectClear("select c.course_id from smc_course as c
                              LEFT JOIN smc_code_coursecat as t ON c.coursecat_id = t.coursecat_id
                              WHERE t.coursecat_branch = '{$sqlarrayvar['coursecat_branch']}' and t.company_id = '8888' AND c.coursecat_id != '' ");
                if($courselist) {
                    foreach ($courselist as $coursevar) {
                        if(!$this->DataControl->selectOne("select * from xxx WHERE apply_id = '{$apply_id}' and course_id = '{$coursevar['course_id']}'  limit 0,1")) {
                            $dataone = array();
                            $dataone['apply_id'] = $apply_id;//
                            $dataone['course_id'] = $coursevar['course_id'];//
                            $applycourse_id = $this->DataControl->insertData('xxx', $dataone);
                        }
                    }
                }

                $datatwo = array();
                do {
                    $couponspid_get = str_pad(mt_rand(*********, *********), 9, "0", STR_PAD_BOTH);
                } while ($this->DataControl->getFieldOne("smc_student_coupons", "coupons_id", "coupons_pid='{$couponspid_get}'"));
                $datatwo['company_id'] = 8888;
                $datatwo['apply_id'] = $apply_id;
                $datatwo['coupons_pid'] = $couponspid_get;
                $datatwo['student_id'] = $studentOne['student_id'];
                $datatwo['coupons_range'] = 0;
                $datatwo['coupons_class'] = 2;
                $datatwo['coupons_name'] = '迁校优惠券';
                $datatwo['coupons_type'] = 0;
                $datatwo['coupons_playclass'] = 0;
                $datatwo['coupons_reason'] = "Bella给的杜鹃校拆搬校抵用券申请表格，生成优惠券申请！";
//                $datatwo['couponsrules_id'] = $applyOne['couponsrules_id'];
//                $datatwo['coupons_discount'] = $playinc;
                $datatwo['coupons_price'] = $sqlarrayvar['coupons_price'];
                $datatwo['coupons_minprice'] = 10000;
                $datatwo['coupons_bindingtime'] = strtotime('2020-09-26');
                $datatwo['coupons_exittime'] = strtotime('2020-11-30')+86399;
                $datatwo['coupons_createtime'] = time();
                $last_id = $this->DataControl->insertData("smc_student_coupons", $datatwo);
            }else{
                echo $studentOne['student_id']."|".$studentOne['cnname']."|".$couponsOne['coupons_pid']."拆校优惠券已发布<br />";
            }
        }
    }


    //sandy 给的 kctw 的学校学生数据导入
    function addKctwSchoolStudentApi()
    {
        die;
        /*$request['company_id'] = '78531';
        echo '已操作请勿重复操作';die;
        ini_set('max_execution_time', '0');
        $this->c = "111";
        $sqlarray = execl_to_array(ROOT_PATH."/bellajiuqi/sandy/kctwtaiwan.xlsx",array('校园ID'=>'school_id',
            '学生中文名'=>'student_cnname',
            '学生英文名'=>'student_enname',
            '身份证号码'=>'student_idcard',
            '性别'=>'student_sex',
            '生日'=>'student_birthday',
            '入校时间'=>'student_createtime',
            '联系电话(父)'=>'family_mobile',
            '联系人姓名(父)'=>'family_cnname',
            '亲属关系(父)'=>'family_relation',
            '联系电话(母)'=>'family_mobiletwo',
            '联系人姓名(母)'=>'family_cnnametwo',
            '亲属关系(母)'=>'family_relationtwo'));
        array_shift($sqlarray);

        print_r($sqlarray);
        die;

        if ($sqlarray) {
            foreach ($sqlarray as $WorkerrVar) {
                if ($WorkerrVar['student_cnname'] !== '' && $WorkerrVar['student_cnname'] !== '') {
                    $sqlarray[] = $WorkerrVar;
                } else {
                    echo $WorkerrVar['student_cnname']."|".$WorkerrVar['student_enname']."|".$WorkerrVar['student_idcard']."没有姓名<br />";
                }
            }
        }
        if (count($sqlarray) > 1000) {
            ajax_return(array('error' => 1, 'errortip' => "导入数量不能大于1000!", "bakfuntion" => "errormotify"));
        }
        if ($sqlarray) {
            foreach ($sqlarray as $workersVar) {
                if ( (isset($workersVar['family_mobile']) && $workersVar['family_mobile'] != '') or (isset($workersVar['family_mobiletwo']) && $workersVar['family_mobiletwo'] != '') ) {
                    $data = array();
                    $stuOne = $this->DataControl->getFieldOne("smc_student", "student_id", "student_idcard='{$workersVar['student_idcard']}' and company_id='{$request['company_id']}' and  student_idcard <>''");

                    $like = date("Ymd", time());
                    $stuInfo = $this->DataControl->selectOne("select student_branch from smc_student where student_branch like '{$like}%' AND LENGTH(student_branch) = '14' order by student_branch DESC limit 0,1");
                    if ($stuInfo) {
                        $data['student_branch'] = $stuInfo['student_branch'] + 1;
                    } else {
                        $data['student_branch'] = $like . '000001';
                    }
                    if (!$stuOne) {
                        $PlayInfoVar['student_cnname'] = addslashes(trim($workersVar['student_cnname']));
                        $PlayInfoVar['student_enname'] = addslashes(trim($workersVar['student_enname']));
                        $PlayInfoVar['student_idcard'] = $workersVar['student_idcard'];
                        $PlayInfoVar['student_sex'] = addslashes(trim($workersVar['student_sex']));
                        if( $workersVar['student_birthday'] !==""){
                            $PlayInfoVar['student_birthday'] = gmdate('Y-m-d', \PHPExcel_Shared_Date::ExcelToPHP($workersVar['student_birthday']));
                        }
                        $PlayInfoVar['company_id'] = $request['company_id'];

                        $data['student_cnname'] =  addslashes(trim($workersVar['student_cnname']));
                        $data['student_enname'] = addslashes(trim($workersVar['student_enname']));
                        $data['student_idcard'] = $workersVar['student_idcard'];
                        $data['student_sex'] = addslashes(trim($workersVar['student_sex']));
                        if( $workersVar['student_birthday'] !==""){
                            $data['student_birthday'] = gmdate('Y-m-d', \PHPExcel_Shared_Date::ExcelToPHP($workersVar['student_birthday']));

                        }
                        $data['company_id'] = $request['company_id'];
                        if( $workersVar['student_createtime'] !==""){
                            $data['student_createtime'] = strtotime(gmdate('Y-m-d', \PHPExcel_Shared_Date::ExcelToPHP($workersVar['student_createtime'])));
                        }else{
                            $data['student_createtime'] = time();
                        }
//                        if ($student_id = $this->DataControl->insertData('smc_student', $data)) {
                        if ($student_id = $this->DataControl->insertData('smc_student', $data)) {
                            $family_data =array();
                            if(isset($workersVar['family_mobile']) &&  $workersVar['family_mobile'] !=="" ){
                                $parentOne=$this->DataControl->getFieldOne("smc_parenter","parenter_id","parenter_mobile='{$workersVar['family_mobile']}'");
                                if(!$parentOne){
                                    $data=array();
                                    $data['parenter_mobile']=trim($workersVar['family_mobile']);
                                    $data['parenter_cnname']=($workersVar['family_cnname']==""?'':addslashes(trim($workersVar['family_cnname'])));
                                    $data['parenter_pass'] = md5(substr($workersVar['family_mobile'],-6));
                                    $data['parenter_bakpass'] = substr($workersVar['family_mobile'],-6);
                                    $data['parenter_addtime']=time();
                                    $parentid = $this->DataControl->insertData("smc_parenter",$data);
                                }
                                $family_data['family_relation']=$workersVar['family_relation']==''?'未知':addslashes(trim($workersVar['family_relation']));
                                $family_data['family_mobile'] = $workersVar['family_mobile'];
                                $family_data['family_cnname'] = $workersVar['family_cnname']==""?'':addslashes(trim($workersVar['family_cnname']));
                                $family_data['student_id'] =$student_id;
                                $family_data['family_isdefault'] = 1;
                                if($parentid) {
                                    $family_data['parenter_id'] = $parentid;
                                }else{
                                    $family_data['parenter_id'] = $parentOne['parenter_id'];
                                }
                                $this->DataControl->insertData("smc_student_family",$family_data);
                            }

                            if(isset($workersVar['family_mobiletwo']) &&  $workersVar['family_mobiletwo'] !=="" ){
                                $parentOne=$this->DataControl->getFieldOne("smc_parenter","parenter_id","parenter_mobile='{$workersVar['family_mobiletwo']}'");
                                if(!$parentOne){
                                    $data=array();
                                    $data['parenter_mobile']=trim($workersVar['family_mobiletwo']);
                                    $data['parenter_cnname']=($workersVar['family_cnnametwo']==""?'':addslashes(trim($workersVar['family_cnnametwo'])));
                                    $data['parenter_pass'] = md5(substr($workersVar['family_mobiletwo'],-6));
                                    $data['parenter_bakpass'] = substr($workersVar['family_mobiletwo'],-6);
                                    $data['parenter_addtime']=time();
                                    $parentid = $this->DataControl->insertData("smc_parenter",$data);
                                }
                                $family_data['family_relation']=$workersVar['family_relationtwo']==''?'未知':addslashes(trim($workersVar['family_relationtwo']));
                                $family_data['family_mobile'] = $workersVar['family_mobiletwo'];
                                $family_data['family_cnname'] = $workersVar['family_cnnametwo']==""?'':addslashes(trim($workersVar['family_cnnametwo']));
                                $family_data['student_id'] =$student_id;
                                if (isset($workersVar['family_mobile']) && $workersVar['family_mobile'] != ''){
                                    $family_data['family_isdefault'] = 0;
                                }else{
                                    $family_data['family_isdefault'] = 1;
                                }
                                if($parentid) {
                                    $family_data['parenter_id'] = $parentid;
                                }else{
                                    $family_data['parenter_id'] = $parentOne['parenter_id'];
                                }
                                $this->DataControl->insertData("smc_student_family",$family_data);
                            }


                            $enrolldata = array();
                            $enrolldata['school_id'] = $workersVar['school_id'];
                            $enrolldata['student_id'] = $student_id;
                            $enrolldata['enrolled_createtime'] = $data['student_createtime'] ;
                            $this->DataControl->insertData('', $enrolldata);
                            if (!$this->DataControl->getFieldOne("smc_student_balance", "student_id", "student_id = '{$student_id}' and school_id = '{$workersVar['school_id']}'")) {
                                $schoolOne=$this->DataControl->getFieldOne("smc_school","companies_id","school_id='{$workersVar['school_id']}'");

                                $balance = array();
                                $balance['company_id'] = $request['company_id'];
                                $balance['companies_id'] = $schoolOne['companies_id'];
                                $balance['student_id'] = $student_id;
                                $balance['school_id'] = $workersVar['school_id'];
                                $this->DataControl->insertData("smc_student_balance", $balance);
                            }
                            $this->entrySchool($student_id, $workersVar['school_id'], $request['company_id']);

                            //数据同步到微商城
                            $data = array();
                            if ($stuInfo) {
                                $data['student_branch'] = $stuInfo['student_branch'] + 1;
                            } else {
                                $data['student_branch'] = $like . '000001';
                            }
                            $ptcData = array();
                            $ptcData['student_cnname'] = $workersVar['student_cnname'];
                            $ptcData['student_enname'] = $workersVar['student_enname'];
                            $ptcData['student_idcard'] = $workersVar['student_idcard'];
                            $ptcData['student_sex'] = $workersVar['student_sex'];
                            $ptcData['student_birthday'] = $workersVar['student_birthday'];
                            if (isset($workersVar['family_mobile']) && $workersVar['family_mobile'] != '') {
                                $ptcData['family_mobile'] = $workersVar['family_mobile'];
                            }else{
                                $ptcData['family_mobile'] = $workersVar['family_mobiletwo'];
                            }
                            $ptcData['family_cnname'] = $workersVar['family_cnname'];
                            $ptcData['family_relation'] = $workersVar['family_relation'];
                            $ptcData['student_branch'] = $data['student_branch'];
                            $bangding = request_by_curl("https://ptcapi.kidcastle.com.cn/Api/BindingParStu", dataEncode($ptcData), "GET", array());
                            //微商城是否绑定成功
                            $PlayInfoVar['bangding'] = $bangding;

                            $PlayInfoVar['error'] = "0";
                            $PlayInfoVar['errortip'] = "导入成功";
                        } else {
                            echo $workersVar['student_cnname']."|".$workersVar['student_enname']."|".$workersVar['student_idcard']."导入失败<br />";
                        }
                    } else {
                        //echo $workersVar['student_cnname']."|".$workersVar['student_enname']."|".$workersVar['student_idcard']."没有手机号<br />";
                    }
                } else {
                    echo $workersVar['student_cnname']."|".$workersVar['student_enname']."|".$workersVar['student_idcard']."没有手机号<br />";
                }
            }
                echo 'jieshu';
        }*/
    }

    function entrySchool($student_id, $school_id, $company_id)
    {
        $en_data = array();
        $en_data['school_id'] = $school_id;
        $en_data['student_id'] = $student_id;
        $en_data['enrolled_createtime'] = time();
        $en_data['enrolled_status'] = 0;
        if ($this->DataControl->insertData("smc_student_enrolled", $en_data)) {
            $like = date("Ymd", time());
            $changeInfo = $this->DataControl->selectOne("select change_pid from smc_student_change where change_pid like '{$like}%' and company_id='{$company_id}' order by change_pid DESC limit 0,1");
//
            $data = array();
            $data['company_id'] = $company_id;
            $data['student_id'] = $student_id;
            if ($changeInfo) {
                $data['change_pid'] = $changeInfo['change_pid'] + 1;
            } else {
                $data['change_pid'] = $like . '000001';
            }
            $data['to_stuchange_code'] = 'A01';
            $data['to_school_id'] = $school_id;
            $data['change_status'] = 1;
            $data['change_day'] = date("Y-m-d", time());
//            $data['reason_code']=$reason_code;
            $data['change_reason'] = '入校,后台添加';
            $data['change_workername'] = $this->UserLogin['user_name'];
            $data['change_createtime'] = time();
            $this->DataControl->insertData("smc_student_change", $data);

            $log_data = array();
            $log_data['change_pid'] = $data['change_pid'];
            $log_data['company_id'] = $company_id;
            $log_data['student_id'] = $student_id;
            $log_data['changelog_type'] = 1;
            $log_data['stuchange_code'] = 'A01';
            $log_data['school_id'] = $school_id;
            $log_data['changelog_note'] = '入校,后台添加';
            $log_data['changelog_day'] = date("Y-m-d", time());
//            $log_data['staffer_id'] = $this->stafferOne['staffer_id'];
            $log_data['changelog_createtime'] = time();
            $this->DataControl->insertData("smc_student_changelog", $log_data);

            $this->error = true;
            $this->oktip = "入校成功";
            return true;

        } else {
            $this->error = true;
            $this->errortip = "入校失败";
            return false;
        }
    }

}