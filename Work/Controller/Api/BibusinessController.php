<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2021/6/2
 * Time: 10:46
 */

namespace Work\Controller\Api;


class BibusinessController extends viewTpl
{
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $stafferOne = array();

    function __construct()
    {
        parent::__construct();
    }

    function stringReplace($string)
    {
        $datacode = trim(str_replace('"', "", $string));
        $datacode = urldecode(urldecode($datacode));
        $datacode = str_replace(' ', "+", $datacode);
        return $datacode;
    }

    //第三方授权访问权限校验
    function UserVerify($paramArray)
    {
        $apiuserOne = $this->DataControl->getFieldOne('imc_apiuser', "apiuser_id,apiuser_aeskey,apiuser_aesiv", "apiuser_code = '{$paramArray['apiuser_code']}'");
        if (!$apiuserOne) {
            $this->errortip = "未查询到您的授权信息";
            $this->error = true;
            return false;
        }

        if (isset($paramArray['apiuser_aeskey']) && $paramArray['apiuser_aeskey'] !== '' && isset($paramArray['apiuser_aesiv']) && $paramArray['apiuser_aesiv'] !== '') {
            if ($apiuserOne['apiuser_aeskey'] == $paramArray['apiuser_aeskey'] && $apiuserOne['apiuser_aesiv'] == $paramArray['apiuser_aesiv']) {
                $baseOne = array();
                $baseOne['apiuser_id'] = $apiuserOne['apiuser_id'];
                $companyOne = $this->DataControl->selectOne("select company_id,company_cnname from gmc_company WHERE company_id = '{$paramArray['company_id']}' limit 0,1");
                if ($companyOne) {
                    $baseOne['company_id'] = $companyOne['company_id'];
                    return $baseOne;
                } else {
                    $this->errortip = '你的授权集团编号错误，请确认编号正确';
                    $this->error = true;
                    return false;
                }
            } else {
                $this->errortip = "你的授权秘钥及偏移值不正确，{$paramArray['apiuser_aeskey']}-{$paramArray['apiuser_aesiv']}";
                $this->error = true;
                return false;
            }
        }

        if (!isset($paramArray['timesteps']) || $paramArray['timesteps'] == '') {
            $this->errortip = "请传入授权时间";
            $this->error = true;
            return false;
        }

        if ($paramArray['timesteps'] + 60 * 5 < time() || $paramArray['timesteps'] - 60 > time()) {
            $maxtimes = date("Y-m-d H:i:s", $paramArray['timesteps'] + 60 * 5);
            $this->errortip = "授权时间{$maxtimes}已过期5分钟，请确认连接及时性";//,{$timesteps}--{$jmsting}
            $this->error = true;
            return false;
        }

        $aes = new \Aesencdec($apiuserOne['apiuser_aeskey'], $apiuserOne['apiuser_aesiv']);
        $xssting = $aes->decrypt($this->stringReplace($paramArray['veytoken']));//解密
        if ($paramJson = json_decode($xssting, 1)) {//转化为数组
            if ((string)$paramJson['timesteps'] !== trim($paramArray['timesteps'])) {
                $this->errortip = '授权时间和连接时间不一致';
                $this->error = true;
                return false;
            }
            $baseOne = array();
            $baseOne['apiuser_id'] = $apiuserOne['apiuser_id'];
            $baseOne['tokenstring'] = $xssting;
            $companyOne = $this->DataControl->selectOne("select company_id,company_cnname from gmc_company WHERE company_id = '{$paramJson['company_id']}' limit 0,1");
            if ($companyOne) {
                $baseOne['company_id'] = $companyOne['company_id'];
                return $baseOne;
            } else {
                $this->errortip = '你的授权集团编号错误，请确认编号正确';
                $this->error = true;
                return false;
            }
        } else {
            $this->errortip = '数据机密信息传输有误，请检查！';
            $this->error = true;
            return false;
        }
    }

    function VeryModelNums($apiuser_id, $apimodule_code, $paramArray)
    {
        $request = Input('get.', '', 'trim,addslashes');
        $apimoduleOne = $this->DataControl->getFieldOne('imc_apiuser_apimodule'
            , "apimodule_id,apiuser_id,apimodule_name,apimodule_nums", "apiuser_id = '{$apiuser_id}' AND apimodule_code = '{$apimodule_code}'");
        $stattTimes = strtotime(date("Y-m-d"));
        $apilogOne = $this->DataControl->selectOne("SELECT COUNT(l.apilog_id) AS anums FROM imc_apiuser_apilog AS l
WHERE l.apimodule_id = '{$apimoduleOne['apimodule_id']}' AND l.apilog_createtime > '{$stattTimes}'");
        if ($apilogOne['anums'] > $apimoduleOne['apimodule_nums']) {
            $this->errortip = "您接口{$apimoduleOne['apimodule_name']}的本日最大授权次数{$apimoduleOne['apimodule_nums']}已消耗完毕！";
            $this->error = true;
            return false;
        } else {
            $data = array();
            $data['apiuser_id'] = $apimoduleOne['apiuser_id'];
            $data['apimodule_id'] = $apimoduleOne['apimodule_id'];
            $data['apilog_posturl'] = "https://api.kedingdang.com/{$request['u']}/{$request['t']}";
            $data['apilog_posttype'] = 'GET';
            $data['apilog_postorgjson'] = http_build_query($paramArray);
            $data['apilog_postjson'] = $paramArray['tokenstring'];
            $data['apilog_ip'] = real_ip();
            $data['apilog_createtime'] = time();
            $this->DataControl->insertData("imc_apiuser_apilog", $data);
            return true;
        }
    }

    //招生大表缓存
    function getCrmOnelogApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'CrmOnelog', $request)) {
                $datawhere = "l.bilog_type = '0'";
                if (isset($request['upcode']) && $request['upcode'] == 'news') {
                    $newsTimes = time() - 3600 * 24 * 1;
                    $datawhere .= " AND l.bilog_updatetime > '{$newsTimes}'";
                }

//                $upLimit = "";
//                if (isset($request['page']) && $request['page'] !== '' && $request['page'] !== '0') {
//                    $upLimit = "LIMIT 150,30000";
//                } else {
//                    $upLimit = " LIMIT 0,150";
//                }

                $sql = "SELECT l.school_id,l.coursetype_id,l.bilog_jsontxt,l.bilog_updatetime 
                FROM crm_client_bilog AS l 
                WHERE {$datawhere} 
                GROUP BY l.school_id,l.coursetype_id,FROM_UNIXTIME(l.bilog_updatetime, '%Y-%m-%d')
                order by l.school_id limit 0,10000";
                $logList = $this->DataControl->selectClear($sql);
                $dataList = array();
                if ($logList) {
                    foreach ($logList as $dataOne) {
                        $schoolOne = json_decode($dataOne['bilog_jsontxt'], 1);
                        $schoolOne['coursetype_id'] = $dataOne['coursetype_id'];
                        $schoolOne['logdate'] = date("Y-m-d", $dataOne['bilog_updatetime']);
                        $dataList[] = $schoolOne;
                    }
                }

                $fieldname = array("地区", "校区名称", "督导区", "在籍", "在读", "专案免费在读人数", "报名/去年", "专案报名/去年",
                    "报名/当年", "报名/周", "报名/月", "上月招生数", "上上月招生数", "新增总毛名单/自然周", "新增总毛名单/自然月",
                    "新增陆军有效名单/周", "新增陆军有效名单/月", "新增空军有效名单/周", "新增空军有效名单/月", "新增有效名单/月（空+陆）", "新增有效名单/季", "新增转介绍名单/月",
                    "园内招（名单/自然月）", "园内招（名单/自然季）", "园内招（名单/自然年）", "园内招（报名/自然月）", "园内招（报名/自然季）", "园内招（报名/自然年）",
                    "可追踪有效名单", "待分配名单数", "追踪(邀约到访）名单数/周", "追踪(邀约到访）人次/周", "追踪人数/月", "追踪人次/月", "邀约诺访/上周", "邀约到访/上周",
                    "邀约诺访/当周", "邀约到访/当周", "邀约诺访/月", "邀约到访/月", "主动到访/周",
                    "主动到访/月", "试读/周", "试读/月", "班组序号", "日志日期");
                $fieldstring = array("school_districtname", "school_shortname", "school_tagbak", "absenteenums", "readingnums", "feereadingnums", 'prelog_year_num', 'prelog_caseyear_num',
                    "nowlog_year_num", "positivelog_week_num", "positivelog_month_num", "positivelog_one_month_num", "positivelog_two_month_num", "mao_week_client_num", "mao_month_client_num",
                    "client_under_weeknum", "client_under_monthnum", "client_up_weeknum", "client_up_monthnum", "client_upunder_monthnum", "client_up_quarternum", "client_referral_monthnum",
                    "register_client_monthnum", "register_client_seasonnum", "register_client_yearnum", "info_month_num", "info_season_num", "info_year_num",
                    "client_all_num", "client_noallot_num", "invitelog_week_num", "invitelog_week_numonce", "track_client_num", "track_tracknum", "invitelog_lastweek_num", "invitelog_lastweek_isvisitnum",
                    "invitelog_thisweek_num", "invitelog_thisweek_isvisitnum", "invitelog_month_num", "invitelog_month_isvisitnum", "invite_week_num",
                    "invite_month_num", "audition_week_num", "audition_month_num", "coursetype_id", "logdate");

                $fieldcustom = array('1', '1', '1', '1', '1', "1", '1', "1",
                    '1', '1', '1', '1', "1", '1', "1",
                    '1', '1', '1', '1', "1", '1', "1",
                    '1', '1', '1', '1', "1", '1',
                    '1', '1', '1', '1', "1", '1', "1", '1',
                    '1', '1', '1', '1', "1",
                    '1', '1', '1', '1', '1');
                $fieldshow = array('1', '1', '1', '1', "1",
                    '1', '1', '1', '1', "1", '1', "1",
                    '1', '1', '1', '1', "1", '1', "1",
                    '1', '1', '1', '1', "1", '1',
                    '1', '1', '1', '1', "1", '1', "1", '1',
                    '1', '1', '1', '1', "1",
                    '1', '1', '1', '1', '1');

                $field = array();
                for ($i = 0; $i < count($fieldstring); $i++) {
                    $field[$i]["fieldstring"] = trim($fieldstring[$i]);
                    $field[$i]["fieldname"] = trim($fieldname[$i]);
                    $field[$i]["custom"] = trim($fieldcustom[$i]);
                    $field[$i]["show"] = trim($fieldshow[$i]);
                }

                $result = array();
                $result['field'] = $field;
                if ($dataList) {
                    $result['list'] = $dataList == false ? array() : $dataList;
                    $res = array('error' => '0', 'errortip' => "招生大表明细", 'result' => $result);
                    ajax_return($res);
                } else {
                    $result['list'] = array();
                    ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => $result));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //教师大表缓存
    function getCrmTwologApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'CrmTwolog', $request)) {
                $datawhere = "l.bilog_type = '1'";
                if (isset($request['upcode']) && $request['upcode'] == 'news') {
                    $newsTimes = time() - 3600 * 24 * 1;
                    $datawhere .= " AND l.bilog_updatetime > '{$newsTimes}'";
                }

                $sql = "SELECT l.school_id,l.coursetype_id,l.bilog_jsontxt,l.bilog_updatetime FROM crm_client_bilog AS l WHERE {$datawhere}";
                $logList = $this->DataControl->selectClear($sql);
                $dataList = array();
                if ($logList) {
                    foreach ($logList as $dataOne) {
                        if ($teachersArray = json_decode($dataOne['bilog_jsontxt'], 1)) {
                            foreach ($teachersArray as $teachersOne) {
                                $teachersOne['school_id'] = $dataOne['school_id'];
                                $teachersOne['coursetype_id'] = $dataOne['coursetype_id'];
                                $teachersOne['logdate'] = date("Y-m-d", $dataOne['bilog_updatetime']);
                                $dataList[] = $teachersOne;
                            }
                        }
                    }
                }


                $field = array();
                $k = 0;
                $field[$k]["fieldstring"] = "school_branch";
                $field[$k]["fieldname"] = "校区编号";
                $field[$k]["show"] = 0;
                $field[$k]["custom"] = 1;

                $k++;
                $field[$k]["fieldstring"] = "school_shortname";
                $field[$k]["fieldname"] = "校区名称";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;

                $k++;
                $field[$k]["fieldstring"] = "staffer_cnname";
                $field[$k]["fieldname"] = "教师中文名";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;

                $k++;
                $field[$k]["fieldstring"] = "staffer_enname";
                $field[$k]["fieldname"] = "教师英文名";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;

                $k++;
                $field[$k]["fieldstring"] = "post_name";
                $field[$k]["fieldname"] = "本校职务";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;

                $k++;
                $field[$k]["fieldstring"] = "clientnum";
                $field[$k]["fieldname"] = "系统名单总数";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;

                $k++;
                $field[$k]["fieldstring"] = "princ_clientnum";
                $field[$k]["fieldname"] = "报名/当年";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;

                $k++;
                $field[$k]["fieldstring"] = "princ_upmonth_num";
                $field[$k]["fieldname"] = "报名/上月";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;

                $k++;
                $field[$k]["fieldstring"] = "princ_nowmonth_num";
                $field[$k]["fieldname"] = "报名/当月";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;

                $k++;
                $field[$k]["fieldstring"] = "princ_month_num";
                $field[$k]["fieldname"] = "报名/月";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;

                $k++;
                $field[$k]["fieldstring"] = "princ_upweek_num";
                $field[$k]["fieldname"] = "报名/上周";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;

                $k++;
                $field[$k]["fieldstring"] = "princ_nowweek_num";
                $field[$k]["fieldname"] = "报名/当周";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;

                $k++;
                $field[$k]["fieldstring"] = "princ_nowweek_num";
                $field[$k]["fieldname"] = "报名/周";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;

                $k++;
                $field[$k]["fieldstring"] = "track_month_num";
                $field[$k]["fieldname"] = "追踪人数/月";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;

                $k++;
                $field[$k]["fieldstring"] = "track_month_time";
                $field[$k]["fieldname"] = "追踪人次/月";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;

                $k++;
                $field[$k]["fieldstring"] = "track_week_num";
                $field[$k]["fieldname"] = "追踪人数/周";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;

                $k++;
                $field[$k]["fieldstring"] = "track_week_time";
                $field[$k]["fieldname"] = "追踪人次/周";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;

                $k++;
                $field[$k]["fieldstring"] = "invite_upmonth_num";
                $field[$k]["fieldname"] = "邀约诺访/上月";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;

                $k++;
                $field[$k]["fieldstring"] = "ohaudition_upmonth_num";
                $field[$k]["fieldname"] = "OH邀约诺访/上月";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;

                $k++;
                $field[$k]["fieldstring"] = "audition_upmonth_num";
                $field[$k]["fieldname"] = "试听邀约诺访/上月";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;

                $k++;
                $field[$k]["fieldstring"] = "invitetwo_upmonth_num";
                $field[$k]["fieldname"] = "柜询插测邀约诺访/上月";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;

                $k++;
                $field[$k]["fieldstring"] = "invitesan_upmonth_num";
                $field[$k]["fieldname"] = "柜询邀约诺访/上月";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;

                $k++;
                $field[$k]["fieldstring"] = "arrive_upmonth_num";
                $field[$k]["fieldname"] = "邀约到访/上月";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;

                $k++;
                $field[$k]["fieldstring"] = "oharrive_upmonth_num";
                $field[$k]["fieldname"] = "OH邀约到访/上月";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;

                $k++;
                $field[$k]["fieldstring"] = "auditiontwo_upmonth_num";
                $field[$k]["fieldname"] = "试听邀约到访/上月";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;

                $k++;
                $field[$k]["fieldstring"] = "invitesix_upmonth_num";
                $field[$k]["fieldname"] = "柜询插测邀约到访/上月";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;

                $k++;
                $field[$k]["fieldstring"] = "inviteseven_upmonth_num";
                $field[$k]["fieldname"] = "柜询邀约到访/上月";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;

                $k++;
                $field[$k]["fieldstring"] = "arrive_upmonth_rate";
                $field[$k]["fieldname"] = "邀约到访率/上月";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;

                $k++;
                $field[$k]["fieldstring"] = "invite_nowmonth_num";
                $field[$k]["fieldname"] = "邀约诺访/当月";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;

                $k++;
                $field[$k]["fieldstring"] = "ohaudition_nowmonth_num";
                $field[$k]["fieldname"] = "OH邀约诺访/当月";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;

                $k++;
                $field[$k]["fieldstring"] = "audition_nowmonth_num";
                $field[$k]["fieldname"] = "试听邀约诺访/当月";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;

                $k++;
                $field[$k]["fieldstring"] = "invitetwo_nowmonth_num";
                $field[$k]["fieldname"] = "柜询插测邀约诺访/当月";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;

                $k++;
                $field[$k]["fieldstring"] = "invitesan_nowmonth_num";
                $field[$k]["fieldname"] = "柜询邀约诺访/当月";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;

                $k++;
                $field[$k]["fieldstring"] = "arrive_nowmonth_num";
                $field[$k]["fieldname"] = "邀约到访/当月";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;

                $k++;
                $field[$k]["fieldstring"] = "oharrive_nowmonth_num";
                $field[$k]["fieldname"] = "OH邀约到访/当月";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;

                $k++;
                $field[$k]["fieldstring"] = "auditiontwo_nowmonth_num";
                $field[$k]["fieldname"] = "试听邀约到访/当月";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;

                $k++;
                $field[$k]["fieldstring"] = "invitesix_nowmonth_num";
                $field[$k]["fieldname"] = "柜询插测邀约到访/当月";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;

                $k++;
                $field[$k]["fieldstring"] = "inviteseven_nowmonth_num";
                $field[$k]["fieldname"] = "柜询邀约到访/当月";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;

                $k++;
                $field[$k]["fieldstring"] = "arrive_nowmonth_rate";
                $field[$k]["fieldname"] = "邀约到访率/当月";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;

                $k++;
                $field[$k]["fieldstring"] = "invite_upweek_num";
                $field[$k]["fieldname"] = "邀约诺访/上周";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;

                $k++;
                $field[$k]["fieldstring"] = "ohaudition_upweek_num";
                $field[$k]["fieldname"] = "OH邀约诺访/上周";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;

                $k++;
                $field[$k]["fieldstring"] = "audition_upweek_num";
                $field[$k]["fieldname"] = "试听邀约诺访/上周";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;

                $k++;
                $field[$k]["fieldstring"] = "invitetwo_upweek_num";
                $field[$k]["fieldname"] = "柜询插测邀约诺访/上周";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;

                $k++;
                $field[$k]["fieldstring"] = "invitesan_upweek_num";
                $field[$k]["fieldname"] = "柜询邀约诺访/上周";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;

                $k++;
                $field[$k]["fieldstring"] = "arrive_upweek_num";
                $field[$k]["fieldname"] = "邀约到访/上周";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;

                $k++;
                $field[$k]["fieldstring"] = "oharrive_upweek_num";
                $field[$k]["fieldname"] = "OH邀约到访/上周";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;

                $k++;
                $field[$k]["fieldstring"] = "auditiontwo_upweek_num";
                $field[$k]["fieldname"] = "试听邀约到访/上周";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;

                $k++;
                $field[$k]["fieldstring"] = "invitesix_upweek_num";
                $field[$k]["fieldname"] = "柜询插测邀约到访/上周";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;

                $k++;
                $field[$k]["fieldstring"] = "inviteseven_upweek_num";
                $field[$k]["fieldname"] = "柜询邀约到访/上周";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;

                $k++;
                $field[$k]["fieldstring"] = "arrive_upweek_rate";
                $field[$k]["fieldname"] = "邀约到访率/上周";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;

                $k++;
                $field[$k]["fieldstring"] = "invite_nowweek_num";
                $field[$k]["fieldname"] = "邀约诺访/当周";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;

                $k++;
                $field[$k]["fieldstring"] = "ohaudition_nowweek_num";
                $field[$k]["fieldname"] = "OH邀约诺访/当周";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;

                $k++;
                $field[$k]["fieldstring"] = "audition_nowweek_num";
                $field[$k]["fieldname"] = "试听邀约诺访/当周";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;

                $k++;
                $field[$k]["fieldstring"] = "invitetwo_nowweek_num";
                $field[$k]["fieldname"] = "柜询插测邀约诺访/当周";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;

                $k++;
                $field[$k]["fieldstring"] = "invitesan_nowweek_num";
                $field[$k]["fieldname"] = "柜询邀约诺访/当周";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;

                $k++;
                $field[$k]["fieldstring"] = "arrive_nowweek_num";
                $field[$k]["fieldname"] = "邀约到访/当周";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;

                $k++;
                $field[$k]["fieldstring"] = "oharrive_nowweek_num";
                $field[$k]["fieldname"] = "OH邀约到访/当周";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;

                $k++;
                $field[$k]["fieldstring"] = "auditiontwo_nowweek_num";
                $field[$k]["fieldname"] = "试听邀约到访/当周";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;

                $k++;
                $field[$k]["fieldstring"] = "invitesix_nowweek_num";
                $field[$k]["fieldname"] = "柜询插测邀约到访/当周";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;

                $k++;
                $field[$k]["fieldstring"] = "inviteseven_nowweek_num";
                $field[$k]["fieldname"] = "柜询邀约到访/当周";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;

                $k++;
                $field[$k]["fieldstring"] = "arrive_nowweek_rate";
                $field[$k]["fieldname"] = "邀约到访率/当周";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;

                $k++;
                $field[$k]["fieldstring"] = "invite_month_num";
                $field[$k]["fieldname"] = "主动到访/月";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;

                $k++;
                $field[$k]["fieldstring"] = "enroll_month_num";
                $field[$k]["fieldname"] = "主动到访报名/月";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;

                $k++;
                $field[$k]["fieldstring"] = "enroll_month_rate";
                $field[$k]["fieldname"] = "主动到访报名率/月";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;

                $k++;
                $field[$k]["fieldstring"] = "invite_week_num";
                $field[$k]["fieldname"] = "主动到访/周";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;

                $k++;
                $field[$k]["fieldstring"] = "enroll_week_num";
                $field[$k]["fieldname"] = "主动到访报名/周";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;

                $k++;
                $field[$k]["fieldstring"] = "enroll_week_rate";
                $field[$k]["fieldname"] = "主动到访报名率/周";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;

                $k++;
                $field[$k]["fieldstring"] = "coursetype_id";
                $field[$k]["fieldname"] = "班组序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;

                $k++;
                $field[$k]["fieldstring"] = "logdate";
                $field[$k]["fieldname"] = "日志日期";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;

                $result = array();
                $result['field'] = $field;
                if ($dataList) {
                    $result['list'] = $dataList == false ? array() : $dataList;
                    $res = array('error' => '0', 'errortip' => "招生大表明细", 'result' => $result);
                    ajax_return($res);
                } else {
                    $result['list'] = array();
                    ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => $result));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //获取最新更新序号
    function getModelMinID($apiuser_id, $apimodule_code)
    {
        $apimoduleOne = $this->DataControl->getFieldOne('imc_apiuser_apimodule'
            , "apimodule_id,apiuser_id,apimodule_name,apimodule_nums,apimodule_miniD", "apiuser_id = '{$apiuser_id}' AND apimodule_code = '{$apimodule_code}'");
        return $apimoduleOne['apimodule_miniD'];
    }

    //更新模块最小序号
    function updataModelMinID($apiuser_id, $apimodule_code, $nimID)
    {
        $data = array();
        $data['apimodule_miniD'] = $nimID;
        $this->DataControl->updateData('imc_apiuser_apimodule', "apiuser_id = '{$apiuser_id}' AND apimodule_code = '{$apimodule_code}'", $data);
    }

    function getMaxId()
    {
        $data = array();
        $data['old_id'] = 10000;

        return $data;
    }

    //招生大表数据
    function getBiCrmgatherApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'BiCrmgather', $request)) {
                $paramArray = array();
                if (isset($request['p']) && $request['p'] > 0) {
                    $paramArray['p'] = $request['p'];
                } else {
                    $paramArray['p'] = '1';
                }
                if (isset($request['num']) && $request['num'] > 0) {
                    $paramArray['num'] = $request['num'];
                } else {
                    $paramArray['num'] = '10';
                }
                if (isset($request['fixedtime']) && $request['fixedtime'] !== '') {
                    $paramArray['fixedtime'] = $request['fixedtime'];
                } else {
                    $paramArray['fixedtime'] = date("Y-m-d");
                }
                if (isset($request['coursecode']) && $request['coursecode'] !== '') {
                    if ($request['coursecode'] == 'E') {
                        $paramArray['coursetype_id'] = '65';
                    } elseif ($request['coursecode'] == 'A') {
                        $paramArray['coursetype_id'] = '64';
                    } elseif ($request['coursecode'] == 'RC') {
                        $paramArray['coursetype_id'] = '61';
                    } else {
                        $paramArray['coursetype_id'] = '65';
                    }
                } else {
                    $paramArray['coursetype_id'] = '65';
                }

                $paramArray['school_type'] = '1';
                $paramArray['company_id'] = $pucArray['company_id'];
                $Model = new \Model\Report\Gmc\GmcCrmReportModel($paramArray);
                $datalist = $Model->getClientHighestReport($paramArray);

                $fieldname = array('校区名称', '校区编号', '在籍人数(当前)', '在读人数', '专案免费在读人数', '学校备注', '6月至今总招生', '报名/周', '报名/月', '内招/月', '外招/月', '新增毛名单/月', '新增线下名单/周', '新增线下名单/月', '新增线上名单/周', '新增线上名单/月', '新增名单/季', '推荐名单/月', '6个月内有效名单', '总有效名单', '待分配名单数', '追踪名单数/周', '追踪人次/周', "电询/周", "电询/月", '未确认邀约数/月', '柜询/周', '柜询/月', 'OH邀约/当周', '插班邀约/当周', 'OH邀约/上周', 'OH邀约到访/上周', 'OH邀约/自然周', 'OH邀约/自然月', "OH到访/自然周", "OH到访/自然月", "试听/周", "试听/月", "OH转正数/月", "OH转正率/月", '内招单数/月', '外招名单数/月', "专案名单数/月");
                $fieldstring = array('school_shortname', 'school_branch', 'absenteenums', 'readingnums', 'feereadingnums', 'school_tagbak', 'positivelog_history_num', 'positivelog_week_num', 'positivelog_month_num', 'inmonth_num', 'outmonth_num', 'mao_month_client_num', 'client_under_weeknum', 'client_under_monthnum', 'client_up_weeknum', 'client_up_monthnum', 'client_up_quarternum', 'recomend_clientnum', 'sixmoth_clientnum', 'client_all_num', 'client_noallot_num', 'track_client_num', 'track_tracknum', 'track_week_tephonenum', 'track_month_tephonenum', 'no_confirm', 'invite_week_num', 'invite_month_num', 'ohaudition_curweek_num', 'jmcaudition_curweek_num', 'ohaudition_lastweek_num', 'ohaudition_lastweek_postnum', 'ohaudition_week_num', 'ohaudition_month_num', 'ohaudition_week_arrnum', 'ohaudition_month_arrnum', 'audition_week_num', 'audition_month_num', 'ohpostive_month_arrnum', 'ohpostive_month_rate', 'client_innernum', 'client_outnum', 'client_casenum');

                $fieldcustom = array('1', '1', '1', '1', "1", "0", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1');
                $fieldshow = array('1', '1', '1', '1', "1", "0", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '0', '0', '0', '0', '0');

                $field = array();
                $field_array = array('positivelog_week_num', 'positivelog_month_num', 'client_under_weeknum', 'client_under_monthnum', 'client_up_weeknum', 'client_up_monthnum', 'client_up_quarternum', 'ohaudition_week_num', 'ohaudition_month_num', 'ohaudition_week_arrnum', 'ohaudition_month_arrnum', 'audition_week_num', 'audition_month_num', 'ohpostive_month_arrnum');
                for ($i = 0; $i < count($fieldstring); $i++) {
                    $field[$i]["fieldstring"] = trim($fieldstring[$i]);
                    $field[$i]["fieldname"] = trim($fieldname[$i]);
                    $field[$i]["custom"] = trim($fieldcustom[$i]);
                    $field[$i]["show"] = trim($fieldshow[$i]);
                    if (in_array($field[$i]["fieldstring"], $field_array)) {
                        $field[$i]["ismethod"] = 1;
                        $field[$i]["table_name"] = $field[$i]['fieldname'] . '统计表';
                    }
                }

                $result = array();
                $result['field'] = $field;
                $result['list'] = $datalist['list'] == false ? array() : $datalist['list'];
                $allnum = $datalist['allnum'] == false ? 0 : $datalist['allnum'];
                $result['allnum'] = $allnum;

                if ($result['list']) {
                    $res = array('error' => '0', 'errortip' => "获取招生追踪大表成功", 'result' => $result);
                } else {
                    $res = array('error' => '1', 'errortip' => "暂无记录", 'result' => $result);
                }
                ajax_return($res);
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //云之家教师业绩查询接口
    function getYzjTeperformanceApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'YzjTeperformance', $request)) {
                $paramArray = array();
                $paramArray['school_type'] = '1';
                $paramArray['company_id'] = $pucArray['company_id'];
                $paramArray['school_branch'] = $request['school_branch'];
                $paramArray['start_time'] = $request['start_time'];
                $paramArray['end_time'] = $request['end_time'];
                $Model = new \Model\Report\Gmc\GmcCrmReportModel($paramArray);
                $datalist = $Model->getClientMarketerReport($paramArray);

                $fieldname = array('教师ID', '教师名称', '主招新生数', '主招美语新生数', '主招课辅新生数', '辅招新生数', '辅招美语新生数', '辅招课辅新生数', '新增分配名单数', '新增OH邀约数', '新增OH到访数', '沟通人数', '沟通人次', '电话咨询数', '美语电话咨询数', '课辅电话咨询数', '柜询咨询量', '美语柜询咨询量', '课辅柜询咨询量');
                $fieldstring = array('marketer_id', 'marketer_name', 'main_studentnum', 'main_readingnum', 'main_coachnum', 'fu_studentnum', 'fu_readingnum', 'fu_coachnum', 'princ_clientnum', 'OH_audtion_num', 'OH_audtion_arrivenum', 'track_client_num', 'track_num', 'track_counsel_num', 'reading_client_num', 'coach_client_num', 'track_invite_num', 'reading_invite_num', 'coach_invite_num');

                $fieldcustom = array('1', '1', '1', '1', "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1");
                $fieldshow = array('1', '1', '1', '1', "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1");

                $field = array();
                for ($i = 0; $i < count($fieldstring); $i++) {
                    $field[$i]["fieldstring"] = trim($fieldstring[$i]);
                    $field[$i]["fieldname"] = trim($fieldname[$i]);
                    $field[$i]["custom"] = trim($fieldcustom[$i]);
                    $field[$i]["show"] = trim($fieldshow[$i]);
                }

                $result = array();
                $result['field'] = $field;

                if ($datalist) {
                    $result['list'] = $datalist['list'] == false ? array() : $datalist['list'];
                    if ($result['list']) {
                        $res = array('error' => '0', 'errortip' => "获取教师业绩报表成功", 'result' => $result);
                    } else {
                        $res = array('error' => '1', 'errortip' => "暂无记录", 'result' => $result);
                    }
                    ajax_return($res);
                } else {
                    ajax_return(array('error' => '1', 'errortip' => $Model->errortip, 'result' => array()));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //获取校区明细
    function getSmcschoolApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'Smcschool', $request)) {
                $datawhere = " d.district_id = s.district_id AND s.school_province = r.region_id AND s.company_id = '{$pucArray['company_id']}' AND s.school_type = '1'";
//                if (isset($request['upcode']) && $request['upcode'] == 'news') {
//                    $newsTimes = time() - 3600 * 24 * 3;
//                    $datawhere .= " AND s.school_updatatime > '{$newsTimes}'";
//                }
                $sql = "SELECT s.school_id,s.school_branch, s.school_cnname, s.school_shortname, concat(school_issubject,')',s.school_address) as school_address,s.school_phone, d.district_cnname
     ,s.school_isclose,r.region_name, s.school_istest
FROM smc_school AS s, gmc_company_district AS d,smc_code_region AS r WHERE {$datawhere}";
                $dataList = $this->DataControl->selectClear($sql);

                $field = array();
                $k = 0;
                $field[$k]["fieldstring"] = "school_id";
                $field[$k]["fieldname"] = "校区序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "region_name";
                $field[$k]["fieldname"] = "所在省份";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "school_branch";
                $field[$k]["fieldname"] = "校区编号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "school_cnname";
                $field[$k]["fieldname"] = "校区名称";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "school_shortname";
                $field[$k]["fieldname"] = "校区简称";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "school_isclose";
                $field[$k]["fieldname"] = "关闭状态";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "school_istest";
                $field[$k]["fieldname"] = "测试状态";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "school_phone";
                $field[$k]["fieldname"] = "联系电话";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "school_address";
                $field[$k]["fieldname"] = "校区地址";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "district_cnname";
                $field[$k]["fieldname"] = "所属区域";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $result = array();
                $result['field'] = $field;
                if ($dataList) {
                    $result['list'] = $dataList == false ? array() : $dataList;
                    $res = array('error' => '0', 'errortip' => "校区信息获取成功", 'result' => $result);
                    ajax_return($res);
                } else {
                    $result['list'] = array();
                    ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => $result));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //获取教师明细
    function getSmcstafferApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'Smcstaffer', $request)) {
                $datawhere = "s.company_id = '{$pucArray['company_id']}' AND s.account_class = '0'";
                if (isset($request['upcode']) && $request['upcode'] == 'news') {
                    $newsTimes = time() - 3600 * 24 * 5;
                    $datawhere .= " AND (s.staffer_createtime > '{$newsTimes}' OR s.staffer_updatetime > '{$newsTimes}' 
                        OR m.marketer_updatetime > '{$newsTimes}' OR m.marketer_createtime > '{$newsTimes}')";
                }

                $upLimit = "";
                //首次更新时
                /*$startnums = $request['page']*10000;
                $upLimit = "LIMIT {$startnums},10000";*/

                $sql = "SELECT s.staffer_id, staffer_cnname, staffer_enname, s.staffer_employeepid, s.staffer_sex
                , CASE s.staffer_isparttime WHEN 0 THEN '全职' WHEN 1 THEN '兼职' END AS staffer_parttype
                , CASE s.staffer_istest WHEN 0 THEN '不是' WHEN 1 THEN '是' END AS staffer_istestname
                , CASE s.staffer_leave WHEN 0 THEN '在职' WHEN 1 THEN '离职' END AS staffer_partstate
                ,s.staffer_leavetime
                , CASE s.staffer_native WHEN 0 THEN '陆籍' WHEN 1 THEN '外籍' WHEN 2 THEN '港澳籍' WHEN 3 THEN '台籍' END AS staffer_native
                , ifnull(m.marketer_id,0)AS marketer_id
                FROM smc_staffer s left join crm_marketer as m on s.staffer_id = m.staffer_id 
                WHERE {$datawhere} 
                order by s.staffer_id {$upLimit}";
                $dataList = $this->DataControl->selectClear($sql);

                $field = array();
                $k = 0;
                $field[$k]["fieldstring"] = "staffer_id";
                $field[$k]["fieldname"] = "职工序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "marketer_id";
                $field[$k]["fieldname"] = "招生人员序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "staffer_cnname";
                $field[$k]["fieldname"] = "中文名";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "staffer_enname";
                $field[$k]["fieldname"] = "英文名";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "staffer_employeepid";
                $field[$k]["fieldname"] = "职工编号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "staffer_sex";
                $field[$k]["fieldname"] = "性别";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "staffer_parttype";
                $field[$k]["fieldname"] = "职聘类型";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "staffer_istestname";
                $field[$k]["fieldname"] = "是否测试账号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "staffer_partstate";
                $field[$k]["fieldname"] = "在岗状态";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "staffer_native";
                $field[$k]["fieldname"] = "籍贯类型";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "staffer_leavetime";
                $field[$k]["fieldname"] = "离职日期";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $result = array();
                $result['field'] = $field;
                if ($dataList) {
                    $result['list'] = $dataList == false ? array() : $dataList;
                    $res = array('error' => '0', 'errortip' => "教师信息获取成功", 'result' => $result);
                    ajax_return($res);
                } else {
                    ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //教师带班明细表
    function getStafferClassApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'StafferClass', $request)) {
                $datawhere = "s.company_id = '{$pucArray['company_id']}'";
                if (isset($request['upcode']) && $request['upcode'] == 'news') {
                    $newsTimes = time() - 3600 * 24 * 5;
                    $datawhere .= " AND (t.teach_createtime > '{$newsTimes}' or t.teach_relievetime>'{$newsTimes}') ";
                }

                $upLimit = "";
                //首次更新时
                $startnums = $request['page'] * 30000;
                $upLimit = "LIMIT {$startnums},30000";

                $sql = "
                SELECT
                    t.teach_id,
                    t.class_id,
                    t.staffer_id,
                    CASE t.teach_type WHEN 0 THEN '主教' WHEN 1 THEN '助教' END AS teach_type,
                    CASE t.teach_status WHEN 0 THEN '教学中' WHEN 1 THEN '已解除' END AS teach_status,
                    t.teach_createtime,
	                t.teach_relievetime
                FROM
                    smc_class_teach AS t left join smc_staffer as s on t.staffer_id = s.staffer_id
                WHERE {$datawhere} order by t.teach_id {$upLimit}";
                $dataList = $this->DataControl->selectClear($sql);

                $field = array();
                $k = 0;
                $field[$k]["fieldstring"] = "teach_id";
                $field[$k]["fieldname"] = "序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "class_id";
                $field[$k]["fieldname"] = "班级序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "staffer_id";
                $field[$k]["fieldname"] = "教师序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "teach_status";
                $field[$k]["fieldname"] = "教学状态";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "teach_type";
                $field[$k]["fieldname"] = "教师类型";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "teach_createtime";
                $field[$k]["fieldname"] = "创建时间";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "teach_relievetime";
                $field[$k]["fieldname"] = "解除时间";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $result = array();
                $result['field'] = $field;
                $result['list'] = $dataList == false ? array() : $dataList;
                if ($dataList) {
                    $res = array('error' => '0', 'errortip' => "教师带班明细获取成功", 'result' => $result);
                    ajax_return($res);
                } else {
                    ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => $result));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //班级课时教师安排明细表
    function getHourTeachingApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'HourTeaching', $request)) {
                $datawhere = "c.company_id = '{$pucArray['company_id']}'";
                if (isset($request['upcode']) && $request['upcode'] == 'news') {
                    $newsTimes = time() - 3600 * 24 * 1.5;
                    $datawhere .= " AND (t.teaching_createtime > '{$newsTimes}' or t.teaching_updatatime>'{$newsTimes}') ";
                }

                $upLimit = "";
                //首次更新时
                $startnums = $request['page'] * 30000;
                $upLimit = "LIMIT {$startnums},30000";

                $sql = "
                   SELECT 
                        t.teaching_id,
                        t.class_id,
                        t.hour_id,
                        t.staffer_id,
                        CASE t.teaching_type WHEN 0 THEN '主教' WHEN 1 THEN '助教' END AS teaching_type,
                        ct.teachtype_name,
                        t.teaching_isdel
                    FROM
                        smc_class_hour_teaching as t
                        left join smc_class as c on t.class_id = c.class_id
                        left join smc_code_teachtype as ct on ct.teachtype_code = t.teachtype_code and ct.company_id = '{$pucArray['company_id']}'
                        where {$datawhere}
                     order by t.teaching_id 
                     {$upLimit}";
                $dataList = $this->DataControl->selectClear($sql);

                $field = array();
                $k = 0;
                $field[$k]["fieldstring"] = "teaching_id";
                $field[$k]["fieldname"] = "序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "class_id";
                $field[$k]["fieldname"] = "班级ID";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "hour_id";
                $field[$k]["fieldname"] = "课时ID";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "staffer_id";
                $field[$k]["fieldname"] = "所属教师ID";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "teaching_type";
                $field[$k]["fieldname"] = "教学类型";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "teachtype_name";
                $field[$k]["fieldname"] = "教师类型";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "teaching_isdel";
                $field[$k]["fieldname"] = "是否删除";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $result = array();
                $result['field'] = $field;
                if ($dataList) {
                    $result['list'] = $dataList == false ? array() : $dataList;
                    $res = array('error' => '0', 'errortip' => "班级课时教师安排明细获取成功", 'result' => $result);
                    ajax_return($res);
                } else {
                    ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //电访明细
    function getTelTrackApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'TelTrack', $request)) {
                $datawhere = "sc.company_id = '{$pucArray['company_id']}'";
                if (isset($request['upcode']) && $request['upcode'] == 'news') {
                    $newsTimes = time() - 3600 * 24 * 5;
                    $datawhere .= " AND (sc.track_createtime > '{$newsTimes}' or sc.track_updatetime> '{$newsTimes}' )";
                }

                $upLimit = "";
                //首次更新时
                $startnums = $request['page'] * 30000;
                $upLimit = "LIMIT {$startnums},30000";

                $sql = "
                SELECT 
                    sc.track_id,
                    CASE sc.track_from WHEN 0 THEN '校务' WHEN 1 THEN '教务' END AS track_from,
                    sc.school_id,
                    sc.class_id,
                    sc.student_id,
                    sc.coursetype_id,
                    sc.coursecat_id,
                    sc.track_classname,
                    sc.staffer_id,
                    co.object_name,
                    cr.trackresult_name,
                    sc.track_note,
                    sc.track_day,
                    CASE sc.staffer_type WHEN 0 THEN '教师' WHEN 1 THEN '主管' END AS staffer_type
                FROM
                    smc_student_track AS sc
                    left join crm_code_object as co on co.object_code = sc.track_code
                    left join smc_code_trackresult as cr on cr.trackresult_id = sc.result_id
                WHERE {$datawhere} order by sc.track_id {$upLimit}";
                $dataList = $this->DataControl->selectClear($sql);

                $field = array();
                $k = 0;
                $field[$k]["fieldstring"] = "track_id";
                $field[$k]["fieldname"] = "序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "track_from";
                $field[$k]["fieldname"] = "跟踪来源";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "school_id";
                $field[$k]["fieldname"] = "校区序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "class_id";
                $field[$k]["fieldname"] = "班级序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "student_id";
                $field[$k]["fieldname"] = "学生序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "coursetype_id";
                $field[$k]["fieldname"] = "班组序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "coursecat_id";
                $field[$k]["fieldname"] = "班种序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "track_classname";
                $field[$k]["fieldname"] = "沟通类型";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "staffer_id";
                $field[$k]["fieldname"] = "跟踪人序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "object_name";
                $field[$k]["fieldname"] = "沟通对象";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "trackresult_name";
                $field[$k]["fieldname"] = "沟通结果";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "track_note";
                $field[$k]["fieldname"] = "沟通内容";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "track_day";
                $field[$k]["fieldname"] = "沟通日期";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "staffer_type";
                $field[$k]["fieldname"] = "跟踪人类型";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $result = array();
                $result['field'] = $field;
                $result['list'] = $dataList == false ? array() : $dataList;
                if ($dataList) {
                    $res = array('error' => '0', 'errortip' => "电访明细获取成功", 'result' => $result);
                    ajax_return($res);
                } else {
                    ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => $result));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //获取人资教师在校任职明细
    function getSmcstafferPostbeApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'Smcstaffer', $request)) {
                $datawhere = "s.staffer_id = b.staffer_id 
                AND b.post_id = p.post_id 
                AND g.organize_id = b.organize_id 
                AND b.school_id <> '0' 
                AND ss.school_id = b.school_id 
                AND ss.school_istest = '0' 
                AND s.company_id = '{$pucArray['company_id']}'";
                // AND g.organizeclass_id = '459'

                $sql = "SELECT s.staffer_id, s.staffer_cnname, s.staffer_employeepid, b.school_id, p.post_name, b.postbe_ismianjob
FROM smc_staffer AS s, gmc_staffer_postbe AS b, gmc_company_organize AS g, gmc_company_post AS p , smc_school AS ss WHERE {$datawhere}";
                $dataList = $this->DataControl->selectClear($sql);

                $field = array();
                $k = 0;
                $field[$k]["fieldstring"] = "staffer_id";
                $field[$k]["fieldname"] = "职工序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "staffer_cnname";
                $field[$k]["fieldname"] = "中文名";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "staffer_employeepid";
                $field[$k]["fieldname"] = "职工编号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "school_id";
                $field[$k]["fieldname"] = "校区序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "post_name";
                $field[$k]["fieldname"] = "职务名称";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "postbe_ismianjob";
                $field[$k]["fieldname"] = "主职状态";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $result = array();
                $result['field'] = $field;
                if ($dataList) {
                    $result['list'] = $dataList == false ? array() : $dataList;
                    $res = array('error' => '0', 'errortip' => "教师任职明细获取成功", 'result' => $result);
                    ajax_return($res);
                } else {
                    $result['list'] = array();
                    ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => $result));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //班外课时明细表
    function getOutClassApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'OutClass', $request)) {
                $datawhere = "h.company_id = '{$pucArray['company_id']}'";
                if (isset($request['upcode']) && $request['upcode'] == 'news') {
                    $newsTimes = time() - 3600 * 24 * 5;
                    $datawhere .= " AND (h.hour_createtime > '{$newsTimes}' or h.hour_updatetime > '{$newsTimes}')";
                }
                $sql = "
                    SELECT
                        h.hour_id,
                        h.company_id,
                        h.school_id,
                        h.staffer_id,
                        h.hour_day,
                        h.hour_starttime,
                        h.hour_endtime,
                        h.hour_classtimes,
                        h.hour_scname,
                        h.hour_remk,
                        h.hour_iscancel,
                        h.hour_iscancel,
                        h.outclasstype_id,
                        CASE o.outclasstype_code WHEN 0 THEN '教学时数' WHEN 1 THEN '其他时数' END AS outclasstype_code,
                        o.outclasstype_name,
                        o.outclasstype_remk,
                        o.outclasstype_rate
                    FROM
                        smc_outclass_hour AS h
                        left join smc_code_outclasstype as o on h.outclasstype_id = o.outclasstype_id
                    WHERE {$datawhere}";
                $dataList = $this->DataControl->selectClear($sql);

                $field = array();
                $k = 0;
                $field[$k]["fieldstring"] = "hour_id";
                $field[$k]["fieldname"] = "课时序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "school_id";
                $field[$k]["fieldname"] = "所在学校id";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "staffer_id";
                $field[$k]["fieldname"] = "职工id";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "hour_day";
                $field[$k]["fieldname"] = "上课日期";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "hour_starttime";
                $field[$k]["fieldname"] = "开始时间";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "hour_endtime";
                $field[$k]["fieldname"] = "结束时间";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "hour_classtimes";
                $field[$k]["fieldname"] = "上课时数";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "hour_scname";
                $field[$k]["fieldname"] = "校点名称";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "hour_remk";
                $field[$k]["fieldname"] = "备注";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "hour_iscancel";
                $field[$k]["fieldname"] = "是否取消";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "outclasstype_id";
                $field[$k]["fieldname"] = "类型序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "outclasstype_code";
                $field[$k]["fieldname"] = "课时类别";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "outclasstype_name";
                $field[$k]["fieldname"] = "类型名称";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "outclasstype_remk";
                $field[$k]["fieldname"] = "类型备注";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "outclasstype_rate";
                $field[$k]["fieldname"] = "课时比例";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $result = array();
                $result['field'] = $field;
                if ($dataList) {
                    $result['list'] = $dataList == false ? array() : $dataList;
                    $res = array('error' => '0', 'errortip' => "教师任职明细获取成功", 'result' => $result);
                    ajax_return($res);
                } else {
                    $result['list'] = array();
                    ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => $result));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //客户跟踪记录明细
    function getClientTrackApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'ClientTrack', $request)) {
                $datawhere = " c.company_id = '{$pucArray['company_id']}'";
                if (isset($request['upcode']) && $request['upcode'] == 'news') {
                    $newsTimes = time() - 3600 * 24 * 1.5;
                    $datawhere .= " AND t.track_id > '6705027' ";
                    $datawhere .= " AND t.track_createtime > '{$newsTimes}'";
                }
                $upLimit = "";

                //首次更新时
                $startnums = $request['page'] * 30000;
                $upLimit = "LIMIT {$startnums},30000";


                $sql = "SELECT
                            t.track_id,
                            t.client_id,
                            t.school_id,
                            t.marketer_id,
                            t.track_intention_level,
                            t.coursetype_id,
                            t.coursecat_id,
                            t.track_linktype,
                            CASE t.track_followmode WHEN 0 THEN '普通回访' WHEN 1 THEN '邀约' WHEN 2 THEN '视听' WHEN 3 THEN '转正' WHEN 4 THEN '更新本校流失名单' WHEN 5 THEN '转校' WHEN 6 THEN '毛名单转有效' WHEN 7 THEN '有效转毛名单' WHEN '-1' THEN '跟进流失' WHEN '-2' THEN '主管确认流失' WHEN '-3' THEN '名单无效化' END AS track_followmode,
                            CASE t.track_state WHEN 0 THEN '普通跟踪' WHEN 1 THEN '转化跟踪' WHEN '-1' THEN '跟踪流失' WHEN '-2' THEN '无效化跟踪' END AS track_state,
                            CASE t.track_type WHEN 0 THEN '园所跟踪' WHEN 1 THEN '集团跟踪' WHEN 2 THEN '第三方导入' END AS track_type,
                            CASE t.track_isgmcactive WHEN 0 THEN '否' WHEN 1 THEN '是' END AS track_isgmcactive,
                            CASE t.track_isactive WHEN 0 THEN '否' WHEN 1 THEN '是' END AS track_isactive,
                            t.track_note,
                            t.track_createtime
                        FROM
                            crm_client_track AS t
                            LEFT JOIN crm_client AS c ON c.client_id = t.client_id 
                         WHERE {$datawhere}
                         order by t.track_id
                         {$upLimit}";
                $dataList = $this->DataControl->selectClear($sql);

                $field = array();
                $k = 0;
                $field[$k]["fieldstring"] = "track_id";
                $field[$k]["fieldname"] = "跟踪序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "client_id";
                $field[$k]["fieldname"] = "客户序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "school_id";
                $field[$k]["fieldname"] = "学校序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "marketer_id";
                $field[$k]["fieldname"] = "人员序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "track_intention_level";
                $field[$k]["fieldname"] = "意向星级";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "coursetype_id";
                $field[$k]["fieldname"] = "班组序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "coursecat_id";
                $field[$k]["fieldname"] = "班种序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "track_linktype";
                $field[$k]["fieldname"] = "沟通方式";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "track_followmode";
                $field[$k]["fieldname"] = "跟进模式";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "track_state";
                $field[$k]["fieldname"] = "跟踪状态";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "track_type";
                $field[$k]["fieldname"] = "跟踪类型";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "track_isgmcactive";
                $field[$k]["fieldname"] = "是否集团主动跟踪";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "track_isactive";
                $field[$k]["fieldname"] = "是否学校主动跟踪";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "track_note";
                $field[$k]["fieldname"] = "沟通内容";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "track_createtime";
                $field[$k]["fieldname"] = "创建时间";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $result = array();
                $result['field'] = $field;
                if ($dataList) {
                    $result['list'] = $dataList == false ? array() : $dataList;
                    $res = array('error' => '0', 'errortip' => "教师信息获取成功", 'result' => $result);
                    ajax_return($res);
                } else {
                    $result['list'] = array();
                    ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => $result));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //获取学生明细
    function getSmcstudentApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'Smcstudent', $request)) {
                $datawhere = "s.company_id = '8888' ";

                //开启更新时
                if (isset($request['upcode']) && $request['upcode'] == 'news') {
                    $newsTimes = time() - 3600 * 24 * 3;
                    $datawhere .= " AND s.student_updatatime > '{$newsTimes}'";
                }
                $upLimit = "";

                $upLimit = "";
                if (isset($request['page']) && $request['page'] !== '') {
                    $startnums = $request['page'] * 30000;
                    $upLimit = "LIMIT {$startnums},30000";
                } else {
                    $upLimit = " LIMIT 0,30000";
                }

                $sql = "SELECT s.student_id, s.student_branch, s.student_cnname, s.student_enname, s.student_sex , s.from_client_id, s.student_birthday
                    FROM smc_student s WHERE {$datawhere} ORDER BY s.student_id {$upLimit}";
                $dataList = $this->DataControl->selectClear($sql);

                $field = array();
                $k = 0;
                $field[$k]["fieldstring"] = "student_id";
                $field[$k]["fieldname"] = "学生ID";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "student_branch";
                $field[$k]["fieldname"] = "学生编号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "student_cnname";
                $field[$k]["fieldname"] = "中文名";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "student_enname";
                $field[$k]["fieldname"] = "英文名";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "student_sex";
                $field[$k]["fieldname"] = "性别";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "from_client_id";
                $field[$k]["fieldname"] = "客户ID";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "student_birthday";
                $field[$k]["fieldname"] = "出生日期";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;


                $result = array();
                $result['field'] = $field;
                if ($dataList) {
                    $result['list'] = $dataList == false ? array() : $dataList;
                    $res = array('error' => '0', 'errortip' => "学生信息获取成功", 'result' => $result);
                    ajax_return($res);
                } else {
                    ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //获取班级明细
    function getSmcclassApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'Smcclass', $request)) {
                $datawhere = "c.company_id = '{$pucArray['company_id']}' ";
                if (isset($request['upcode']) && $request['upcode'] == 'news') {
                    $newsTimes = time() - 3600 * 24 * 5;
                    $datawhere .= " AND (c.class_createtime > '{$newsTimes}'
                    or c.class_updatatime > '{$newsTimes}')";
                }

                if (isset($request['page']) && $request['upcode'] !== '0') {
                    $upLimit = $request['page'] * 30000;
                } else {
                    $upLimit = 0;
                }

                $sql = "SELECT c.class_id,c.school_id,c.father_id,c.course_id,c.from_class_id,c.class_type,c.class_cnname
,c.class_enname,c.class_branch,c.class_status,c.class_stdate,c.class_enddate,c.class_isnotrenew 
                        FROM smc_class AS c 
                        WHERE {$datawhere}
                        ORDER BY c.class_id
                        LIMIT {$upLimit},30000";
                $dataList = $this->DataControl->selectClear($sql);

                $field = array();
                $k = 0;
                $field[$k]["fieldstring"] = "school_id";
                $field[$k]["fieldname"] = "学校序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "class_id";
                $field[$k]["fieldname"] = "班级序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "course_id";
                $field[$k]["fieldname"] = "课程别序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "class_isnotrenew";
                $field[$k]["fieldname"] = "非留续班级";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "class_cnname";
                $field[$k]["fieldname"] = "班级名称";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "class_enname";
                $field[$k]["fieldname"] = "班级别名";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "class_branch";
                $field[$k]["fieldname"] = "班级编号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "class_status";
                $field[$k]["fieldname"] = "班级状态";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "class_stdate";
                $field[$k]["fieldname"] = "开始日期";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "class_enddate";
                $field[$k]["fieldname"] = "结束日期";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;

                $result = array();
                $result['field'] = $field;
                if ($dataList) {
                    $result['list'] = $dataList == false ? array() : $dataList;
                    $res = array('error' => '0', 'errortip' => "班级信息获取成功", 'result' => $result);
                    ajax_return($res);
                } else {
                    ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //收费明细同步回调函数
    function updatePayOrderSyncApi()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'updatePayOrderSync', $request)) {
                if ($request['order_pid'] == '' || $request['pay_pid'] == '') {
                    ajax_return(array('error' => '1', 'errortip' => "订单编号和支付编号必传", 'result' => array()));
                }
                if (!$this->DataControl->selectOne("select pay_id from smc_payfee_order_pay where order_pid = '{$request['order_pid']}' and pay_pid = '{$request['pay_pid']}' ")) {
                    ajax_return(array('error' => '1', 'errortip' => "支付订单不存在", 'result' => array()));
                }
                if (!$this->DataControl->selectOne("select jindie_id from smc_payfee_order_pay_jindie where order_pid = '{$request['order_pid']}' and pay_pid = '{$request['pay_pid']}' ")) {
                    $updata = array();
                    $updata['order_pid'] = $request['order_pid'];
                    $updata['pay_pid'] = $request['pay_pid'];
                    $updata['jindie_issync'] = 1;
                    $updata['jindie_createtime'] = time();
                    if ($this->DataControl->insertData("smc_payfee_order_pay_jindie", $updata)) {
                        ajax_return(array('error' => '0', 'errortip' => '订单状态标记成功', 'result' => array()));
                    } else {
                        ajax_return(array('error' => '1', 'errortip' => '订单状态标记失败', 'result' => array()));
                    }
                } else {
                    ajax_return(array('error' => '1', 'errortip' => '已存在相同订单', 'result' => array()));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //获取校区收费日记账
    function getstuPayReportApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'stuPayReport', $request)) {
                $paramArray = array();
                if (isset($request['p']) && $request['p'] > 0) {
                    $paramArray['p'] = $request['p'];
                } else {
                    $paramArray['p'] = '1';
                }
                if (isset($request['num']) && $request['num'] > 0) {
                    $paramArray['num'] = $request['num'];
                } else {
                    $paramArray['num'] = '10';
                }
                $isshagnhai = $request['isshagnhai'] ? $request['isshagnhai'] : 0;
                if (isset($request['pay_starttime']) && $request['pay_starttime'] !== '') {
                    $paramArray['pay_starttime'] = $request['pay_starttime'];
                }

                if (isset($request['pay_endtime']) && $request['pay_endtime'] !== '') {
                    $paramArray['pay_endtime'] = $request['pay_endtime'];
                }
                $paramArray['is_count'] = '1';
                $paramArray['company_id'] = $pucArray['company_id'];
                $paramArray['school_branch'] = $request['school_branch'];
                $paramArray['paytype_isbank_charge'] = $request['paytype_isbank_charge'];
                $paramArray['isSync'] = $request['isSync'];

                $ReportModel = new \Model\Report\Gmc\FinanceReportModel($paramArray);
                $res = $ReportModel->stuPayReport($paramArray, $isshagnhai);

                $k = 0;
                $field = array();
                $field[$k]["fieldstring"] = "school_branch";
                $field[$k]["fieldname"] = "校区编号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "school_cnname";
                $field[$k]["fieldname"] = "校区名称";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "order_pid";
                $field[$k]["fieldname"] = "订单编号";
                $field[$k]["show"] = 0;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "pay_pid";
                $field[$k]["fieldname"] = "支付编号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "companies_cnname";
                $field[$k]["fieldname"] = "支付企业主体";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "companies_kidbranch";
                $field[$k]["fieldname"] = "组织编号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "student_branch";
                $field[$k]["fieldname"] = "学员编号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "student_cnname";
                $field[$k]["fieldname"] = "学员中文名";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "pay_price";
                $field[$k]["fieldname"] = "支付金额";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;


                $field[$k]["fieldstring"] = "order_type";
                $field[$k]["fieldname"] = "订单类型";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "pay_type";
                $field[$k]["fieldname"] = "收费项目类别";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "paychannel_name";
                $field[$k]["fieldname"] = "支付渠道";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "pay_typename";
                $field[$k]["fieldname"] = "支付方式";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "pay_outnumber";
                $field[$k]["fieldname"] = "关联外部单号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "coursecat_branch";
                $field[$k]["fieldname"] = "收费班种";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "paytype_ischarge";
                $field[$k]["fieldname"] = "收费类型";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "paytype_isbank_charge";
                $field[$k]["fieldname"] = "是否银行收费 0 不是（现金） 1 是 -1 其他";
                $field[$k]["show"] = 0;
                $field[$k]["custom"] = 0;
                $k++;


                $field[$k]["fieldstring"] = "coupons_name";
                $field[$k]["fieldname"] = "优惠券名称";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "ordercoupons_price";
                $field[$k]["fieldname"] = "优惠券金额";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "order_createtime";
                $field[$k]["fieldname"] = "下单日期";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "pay_successtime";
                $field[$k]["fieldname"] = "支付成功日期";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "order_note";
                $field[$k]["fieldname"] = "订单备注";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "pay_note";
                $field[$k]["fieldname"] = "支付备注";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "isSync";
                $field[$k]["fieldname"] = "是否已同步到金蝶 0否 1是";
                $field[$k]["show"] = 0;
                $field[$k]["custom"] = 1;
                $k++;

                $result = array();
                $result["field"] = $field;
                $result["allnum"] = $res['allnum'];
                if ($res) {
                    $result["list"] = $res['list'];
                    $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
                    ajax_return($res);
                } else {
                    ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //获取校区收入分摊明细
    function getSchallocationApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'Schallocation', $request)) {
                $paramArray = array();
                if (isset($request['p']) && $request['p'] > 0) {
                    $paramArray['p'] = $request['p'];
                } else {
                    $paramArray['p'] = '1';
                }
                if (isset($request['num']) && $request['num'] > 0) {
                    $paramArray['num'] = $request['num'];
                } else {
                    $paramArray['num'] = '10';
                }
                $isshagnhai = $request['isshagnhai'] ? $request['isshagnhai'] : 0;
                if (isset($request['start_time']) && $request['start_time'] !== '') {
                    $paramArray['start_time'] = $request['start_time'];
                }

                if (isset($request['end_time']) && $request['end_time'] !== '') {
                    $paramArray['end_time'] = $request['end_time'];
                }
                $paramArray['is_count'] = '1';
                $paramArray['company_id'] = $pucArray['company_id'];
                $paramArray['school_branch'] = $request['school_branch'];

                $ReportModel = new \Model\Smc\ReportModel($paramArray);
                $res = $ReportModel->monthlyIncomeReport($paramArray, $isshagnhai);

                $k = 0;
                $field = array();
                $field[$k]["fieldstring"] = "income_id";
                $field[$k]["fieldname"] = "分摊编号";
                $field[$k]["show"] = 0;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "school_branch";
                $field[$k]["fieldname"] = "校区编号";
                $field[$k]["show"] = 0;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "school_cnname";
                $field[$k]["fieldname"] = "校区名称";
                $field[$k]["show"] = 0;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "student_branch";
                $field[$k]["fieldname"] = "学员编号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "student_cnname";
                $field[$k]["fieldname"] = "学员中文名";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "student_enname";
                $field[$k]["fieldname"] = "学员英文名";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;


                $field[$k]["fieldstring"] = "coursetype_cnname";
                $field[$k]["fieldname"] = "班组名称";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "coursetype_branch";
                $field[$k]["fieldname"] = "班组代码";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "coursecat_cnname";
                $field[$k]["fieldname"] = "班种名称";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "coursecat_branch";
                $field[$k]["fieldname"] = "班种代码";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "class_branch";
                $field[$k]["fieldname"] = "班级编号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "class_enname";
                $field[$k]["fieldname"] = "班级别名";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "income_type";
                $field[$k]["fieldname"] = "收入类型";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "companies_cnname";
                $field[$k]["fieldname"] = "收入主体";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "companies_kidbranch";
                $field[$k]["fieldname"] = "组织编号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "income_price";
                $field[$k]["fieldname"] = "收入金额";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "subtype_name";
                $field[$k]["fieldname"] = "认缴类型";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "income_note";
                $field[$k]["fieldname"] = "备注";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "income_confirmtime";
                $field[$k]["fieldname"] = "分摊时间";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $result = array();
                $result["field"] = $field;
                $result["allnum"] = $res['allnum'];
                if ($res) {
                    $result["list"] = $res['list'];
                    $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
                    ajax_return($res);
                } else {
                    ajax_return(array('error' => '1', 'errortip' => $this->errortip ? $this->errortip : $ReportModel->errortip, 'result' => array()));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //获取校区收入分摊明细 -- 分组聚合
    function getSchallocationTwoApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'Schallocation', $request)) {
                $paramArray = array();
                if (isset($request['p']) && $request['p'] > 0) {
                    $paramArray['p'] = $request['p'];
                } else {
                    $paramArray['p'] = '1';
                }
                if (isset($request['num']) && $request['num'] > 0) {
                    $paramArray['num'] = $request['num'];
                } else {
                    $paramArray['num'] = '10';
                }
                $isshagnhai = $request['isshagnhai'] ? $request['isshagnhai'] : 0;
                if (isset($request['start_time']) && $request['start_time'] !== '') {
                    $paramArray['start_time'] = $request['start_time'];
                }

                if (isset($request['end_time']) && $request['end_time'] !== '') {
                    $paramArray['end_time'] = $request['end_time'];
                }
                $paramArray['is_count'] = '1';
                $paramArray['company_id'] = $pucArray['company_id'];
                $paramArray['school_branch'] = $request['school_branch'];

                $ReportModel = new \Model\Smc\ReportModel($paramArray);
                $res = $ReportModel->monthlyIncomeReportToJindie($paramArray, $isshagnhai);

                $k = 0;
                $field = array();
                $field[$k]["fieldstring"] = "serialnoid";
                $field[$k]["fieldname"] = "唯一ID";
                $field[$k]["show"] = 0;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "school_branch";
                $field[$k]["fieldname"] = "校区编号";
                $field[$k]["show"] = 0;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "school_cnname";
                $field[$k]["fieldname"] = "校区名称";
                $field[$k]["show"] = 0;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "coursetype_cnname";
                $field[$k]["fieldname"] = "班组名称";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "coursetype_branch";
                $field[$k]["fieldname"] = "班组代码";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "income_type";
                $field[$k]["fieldname"] = "收入类型";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "companies_cnname";
                $field[$k]["fieldname"] = "收入主体";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "companies_kidbranch";
                $field[$k]["fieldname"] = "组织编号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "income_price";
                $field[$k]["fieldname"] = "收入金额";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "income_confirmtime";
                $field[$k]["fieldname"] = "分摊时间";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "confirmdate";
                $field[$k]["fieldname"] = "分摊日期";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $result = array();
                $result["field"] = $field;
                $result["allnum"] = $res['allnum'];
                if ($res) {
                    $result["list"] = $res['list'];
                    $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
                    ajax_return($res);
                } else {
                    ajax_return(array('error' => '1', 'errortip' => $this->errortip ? $this->errortip : $ReportModel->errortip, 'result' => array()));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //获取企业名称和结算银行账号
    function getCompanyCompaniesApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'CompanyCompanies', $request)) {
                $datawhere = " company_id = '{$pucArray['company_id']}' and companies_settleaccount <> '' ";
                $sql = "SELECT companies_cnname,companies_kidbranch,IF(companies_superviseaccount = '',companies_settleaccount,companies_superviseaccount) as endaccount  
                    FROM gmc_code_companies WHERE {$datawhere}";
                //companies_superviseaccount,companies_settleaccount,
                $dataList = $this->DataControl->selectClear($sql);

                $field = array();
                $k = 0;
                $field[$k]["fieldstring"] = "companies_cnname";
                $field[$k]["fieldname"] = "企业名称";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "companies_kidbranch";
                $field[$k]["fieldname"] = "金蝶组织编码";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

//                $field[$k]["fieldstring"] = "companies_superviseaccount";
//                $field[$k]["fieldname"] = "监管银行账号";
//                $field[$k]["show"] = 1;
//                $field[$k]["custom"] = 0;
//                $k++;
//
//                $field[$k]["fieldstring"] = "companies_settleaccount";
//                $field[$k]["fieldname"] = "结算银行账号";
//                $field[$k]["show"] = 1;
//                $field[$k]["custom"] = 0;
//                $k++;

                $field[$k]["fieldstring"] = "endaccount";
                $field[$k]["fieldname"] = "最终账号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $result = array();
                $result['field'] = $field;
                if ($dataList) {
                    $result['list'] = $dataList == false ? array() : $dataList;
                    $res = array('error' => '0', 'errortip' => "信息获取成功", 'result' => $result);
                    ajax_return($res);
                } else {
                    $result['list'] = array();
                    ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => $result));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //获取班级课时明细
    function getClassHourApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'getClassHour', $request)) {
                $datawhere = "h.class_id = c.class_id AND c.company_id = '{$pucArray['company_id']}'";
                if (isset($request['upcode']) && $request['upcode'] == 'news') {
                    $newsTimes = time() - 3600 * 24 * 1.5;
                    $datawhere .= " AND (h.hour_createtime>='{$newsTimes}' or h.hour_updatatime>'{$newsTimes}')";
                }

                $limit = '';
                //首次更新时
                if (isset($request['page']) && $request['page'] !== '') {
                    $startnums = $request['page'] * 30000;
                    $limit = " LIMIT {$startnums},30000";
                } else {
                    $limit = " desc LIMIT 0,30000";
                }

                $sql = "SELECT h.hour_id,h.class_id,h.hour_name,h.hour_isfree,h.hour_way,h.hour_day,h.hour_starttime,h.hour_endtime,h.hour_classtimes,h.hour_ischecking
                    FROM smc_class_hour AS h ,smc_class AS c 
                    WHERE  {$datawhere} 
                    Order BY h.hour_id 
                    {$limit}";
                $dataList = $this->DataControl->selectClear($sql);

                $field = array();
                $k = 0;
                $field[$k]["fieldstring"] = "hour_id";
                $field[$k]["fieldname"] = "课时序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "class_id";
                $field[$k]["fieldname"] = "班级序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "hour_name";
                $field[$k]["fieldname"] = "课时名称";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "hour_isfree";
                $field[$k]["fieldname"] = "课时免费状况";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "hour_way";
                $field[$k]["fieldname"] = "是否线上";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "hour_day";
                $field[$k]["fieldname"] = "上课日期";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "hour_starttime";
                $field[$k]["fieldname"] = "开始时间";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "hour_endtime";
                $field[$k]["fieldname"] = "结束时间";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "hour_classtimes";
                $field[$k]["fieldname"] = "上课时数";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "hour_ischecking";
                $field[$k]["fieldname"] = "考勤状态";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;


                $result = array();
                $result['field'] = $field;
                if ($dataList) {
                    $result['list'] = $dataList == false ? array() : $dataList;
                    $res = array('error' => '0', 'errortip' => "班级课时状态获取成功", 'result' => $result);
                    ajax_return($res);
                } else {
                    $result['list'] = array();
                    ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => $result));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //获取客户分配校区明细 -- wgh
    function getClientSchoolApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'getClientSchool', $request)) {
                $datawhere = "company_id = '{$pucArray['company_id']}'";
                if (isset($request['upcode']) && $request['upcode'] == 'news') {
                    $newsTimes = time() - 3600 * 24 * 2;
                    $datawhere .= " AND (schoolenter_updatetime > '{$newsTimes}' or schoolenter_createtime>'{$newsTimes}') ";
                }

                $limit = "";
                //首次更新时
                if (isset($request['page']) && $request['page'] !== '') {
                    $startnums = $request['page'] * 30000;
                    $limit = " LIMIT {$startnums},30000";
                } else {
                    $limit = " LIMIT 0,30000";
                }


                $sql = "SELECT school_id,client_id,is_enterstatus
                ,( CASE is_enterstatus WHEN '1' THEN '在校' WHEN '-1' THEN '不在校' END ) AS enter_status_name
                ,FROM_UNIXTIME(schoolenter_createtime) AS schoolenter_createtime
                ,( CASE is_gmctocrmschool WHEN '1' THEN '是' WHEN '0' THEN '不是' END ) AS gmctocrmschool_name
                ,( CASE is_gmcdirectschool WHEN '1' THEN '是' WHEN '0' THEN '不是' END ) AS gmcdirectschool_name
                FROM crm_client_schoolenter 
                WHERE {$datawhere} 
                ORDER BY schoolenter_id {$limit}";
                $dataList = $this->DataControl->selectClear($sql);

                $field = array();
                $k = 0;
                $field[$k]["fieldstring"] = "school_id";
                $field[$k]["fieldname"] = "学校序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "client_id";
                $field[$k]["fieldname"] = "客户序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "enter_status_name";
                $field[$k]["fieldname"] = "在校状态";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "schoolenter_createtime";
                $field[$k]["fieldname"] = "创建时间";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "gmctocrmschool_name";
                $field[$k]["fieldname"] = "是否集团分配给学校";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "gmcdirectschool_name";
                $field[$k]["fieldname"] = "是否集团直接分配给学校";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $result = array();
                $result['field'] = $field;
                if ($dataList) {
                    $result['list'] = $dataList == false ? array() : $dataList;
                    ajax_return(array('error' => '0', 'errortip' => "客户分配校区明细获取成功", 'result' => $result));
                } else {
                    $result['list'] = array();
                    ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => $result));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //获取客户分配负责人明细 -- wgh
    function getClientPrincipalApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'getClientPrincipal', $request)) {
                $datawhere = "s.company_id = '{$pucArray['company_id']}'";
                if (isset($request['upcode']) && $request['upcode'] == 'news') {
                    $newsTimes = time() - 3600 * 24 * 3;
                    $datawhere .= " AND (cp.principal_createtime > '{$newsTimes}'
                    or cp.principal_updatatime > '{$newsTimes}')";
                }
                $limit = "";

                //首次更新时
                if (isset($request['page']) && $request['page'] !== '') {
                    $startnums = $request['page'] * 30000;
                    $limit = " LIMIT {$startnums},30000";
                } else {
                    $limit = " LIMIT 0,30000";
                }

                $sql = "SELECT cp.principal_id,cp.school_id,cp.client_id,cp.marketer_id,cp.principal_ismajor,cp.principal_leave,
                ( CASE cp.principal_ismajor WHEN '0' THEN '辅招' WHEN '1' THEN '主招' END ) AS ismajor_name,
                ( CASE cp.principal_leave WHEN '0' THEN '负责中' WHEN '1' THEN '已解除' END ) AS leave_name,
                FROM_UNIXTIME( cp.principal_createtime ) AS principal_createtime,
                IF(cp.principal_updatatime>0,FROM_UNIXTIME( cp.principal_updatatime ),'--') AS principal_updatatime
                FROM crm_client_principal AS cp 
                LEFT JOIN smc_school AS s ON s.school_id = cp.school_id 
                WHERE {$datawhere} 
                ORDER BY cp.principal_id
                {$limit}";
                $dataList = $this->DataControl->selectClear($sql);

                $field = array();
                $k = 0;
                $field[$k]["fieldstring"] = "school_id";
                $field[$k]["fieldname"] = "学校序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "client_id";
                $field[$k]["fieldname"] = "客户序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "marketer_id";
                $field[$k]["fieldname"] = "人员序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "ismajor_name";
                $field[$k]["fieldname"] = "主招/辅招";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "leave_name";
                $field[$k]["fieldname"] = "负责状态";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "principal_createtime";
                $field[$k]["fieldname"] = "创建时间";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "principal_updatatime";
                $field[$k]["fieldname"] = "更新时间";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;

                $result = array();
                $result['field'] = $field;
                if ($dataList) {
                    $result['list'] = $dataList == false ? array() : $dataList;
                    ajax_return(array('error' => '0', 'errortip' => "客户分配负责人明细获取成功", 'result' => $result));
                } else {
                    $result['list'] = array();
                    ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => $result));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //获取客户分配负责人明细 -- 包含集团分配的 -- 97
    function getClientPrincipalAllApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'getClientPrincipal', $request)) {
                $datawhere = "m.company_id = '{$pucArray['company_id']}'";
                if (isset($request['upcode']) && $request['upcode'] == 'news') {
                    $newsTimes = time() - 3600 * 24 * 3;
                    $datawhere .= " AND (cp.principal_createtime > '{$newsTimes}'
                    or cp.principal_updatatime > '{$newsTimes}')";
                }
                $limit = "";

                //首次更新时
                if (isset($request['page']) && $request['page'] !== '') {
                    $startnums = $request['page'] * 30000;
                    $limit = " LIMIT {$startnums},30000";
                } else {
                    $limit = " LIMIT 0,30000";
                }

                $sql = "SELECT cp.principal_id,cp.school_id,cp.client_id,cp.marketer_id,cp.principal_ismajor,cp.principal_leave,
                ( CASE cp.principal_ismajor WHEN '0' THEN '辅招' WHEN '1' THEN '主招' END ) AS ismajor_name,
                ( CASE cp.principal_leave WHEN '0' THEN '负责中' WHEN '1' THEN '已解除' END ) AS leave_name,
                FROM_UNIXTIME( cp.principal_createtime ) AS principal_createtime,
                IF(cp.principal_updatatime>0,FROM_UNIXTIME( cp.principal_updatatime ),'--') AS principal_updatatime
                FROM crm_client_principal AS cp 
                LEFT JOIN crm_marketer AS m ON m.marketer_id = cp.marketer_id 
                WHERE {$datawhere} 
                ORDER BY cp.principal_id
                {$limit}";
                $dataList = $this->DataControl->selectClear($sql);

                $field = array();
                $k = 0;
                $field[$k]["fieldstring"] = "school_id";
                $field[$k]["fieldname"] = "学校序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "client_id";
                $field[$k]["fieldname"] = "客户序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "marketer_id";
                $field[$k]["fieldname"] = "人员序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "ismajor_name";
                $field[$k]["fieldname"] = "主招/辅招";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "leave_name";
                $field[$k]["fieldname"] = "负责状态";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "principal_createtime";
                $field[$k]["fieldname"] = "创建时间";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "principal_updatatime";
                $field[$k]["fieldname"] = "更新时间";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;

                $result = array();
                $result['field'] = $field;
                if ($dataList) {
                    $result['list'] = $dataList == false ? array() : $dataList;
                    ajax_return(array('error' => '0', 'errortip' => "客户分配负责人明细获取成功", 'result' => $result));
                } else {
                    $result['list'] = array();
                    ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => $result));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    function testParameterView()
    {
        $parameter = array();
        $parameter['timesteps'] = time();
        $parameter['apiuser_code'] = 'kdcloud1';
        $parameter['company_id'] = '8888';

        $apiuserOne = $this->DataControl->getFieldOne('imc_apiuser', "apiuser_id,apiuser_aeskey,apiuser_aesiv", "apiuser_code = '{$parameter['apiuser_code']}'");
        if (!$apiuserOne) {
            $this->errortip = "未查询到您的授权信息";
            $this->error = true;
            return false;
        }

        $aes = new \Aesencdec($apiuserOne['apiuser_aeskey'], $apiuserOne['apiuser_aesiv']);
        $parameterJson = json_encode($parameter, '1');
        $jmsting = $aes->encrypt($parameterJson);//解密

        $result = array();
        $result['timesteps'] = $parameter['timesteps'];
        $result['apiuser_code'] = $parameter['apiuser_code'];
        $result['veytoken'] = $jmsting;

        ajax_return(array('error' => '0', 'errortip' => '模拟参数获取成功', 'result' => $result));
    }

    //获取渠道列表
    function getChannelListApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'getChannelList', $request)) {
                $datawhere = "ch.company_id = '{$pucArray['company_id']}'";

                $sql = "SELECT ch.channel_id,ch.channel_medianame,ch.channel_name,ch.channel_push,ch.channel_way,ch.channel_board,ch.channel_isreferral,ch.channel_isbazaar,ch.channel_createtime,ch.channel_updatetime
                        FROM crm_code_channel AS ch 
                        WHERE  {$datawhere} 
                        Order BY ch.channel_id ASC";
                $dataList = $this->DataControl->selectClear($sql);

                $field = array();
                $k = 0;
                $field[$k]["fieldstring"] = "channel_id";
                $field[$k]["fieldname"] = "渠道序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "channel_medianame";
                $field[$k]["fieldname"] = "招生来源";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "channel_name";
                $field[$k]["fieldname"] = "渠道名称";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "channel_board";
                $field[$k]["fieldname"] = "渠道板块";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;


                $field[$k]["fieldstring"] = "channel_push";
                $field[$k]["fieldname"] = "地推专用";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "channel_way";
                $field[$k]["fieldname"] = "线上线下";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "channel_isreferral";
                $field[$k]["fieldname"] = "是否转介绍";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "channel_isbazaar";
                $field[$k]["fieldname"] = "市场专用";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "channel_createtime";
                $field[$k]["fieldname"] = "创建时间";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "channel_updatetime";
                $field[$k]["fieldname"] = "更新时间";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;


                $result = array();
                $result['field'] = $field;
                if ($dataList) {
                    $result['list'] = $dataList == false ? array() : $dataList;
                    $res = array('error' => '0', 'errortip' => "渠道信息获取成功", 'result' => $result);
                    ajax_return($res);
                } else {
                    ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //获取职工集团职务表 -- 97
    function getGmcStafferPostbeApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'Smcstudent', $request)) {
                $datawhere = " p.company_id = '{$pucArray['company_id']}' ";

                $sql = "select p.postbe_id,p.school_id,p.organize_id,p.staffer_id,p.postbe_ismianjob,p.postbe_status,p.postbe_iscrmuser,p.postbe_crmuserlevel,IFNULL(p.postbe_isgmccrm,0) as postbe_isgmccrm,p.postbe_gmccrmlevel 
                from gmc_staffer_postbe as p WHERE {$datawhere} ";
                $dataList = $this->DataControl->selectClear($sql);

                $field = array();
                $k = 0;
                $field[$k]["fieldstring"] = "postbe_id";
                $field[$k]["fieldname"] = "任职序号ID";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;
                $field[$k]["fieldstring"] = "school_id";
                $field[$k]["fieldname"] = "学校序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;
                $field[$k]["fieldstring"] = "organize_id";
                $field[$k]["fieldname"] = "所属组织序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;
                $field[$k]["fieldstring"] = "staffer_id";
                $field[$k]["fieldname"] = "职工序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;
                $field[$k]["fieldstring"] = "postbe_ismianjob";
                $field[$k]["fieldname"] = "是否主职";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;
                $field[$k]["fieldstring"] = "postbe_status";
                $field[$k]["fieldname"] = "任职状态";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;
                $field[$k]["fieldstring"] = "postbe_iscrmuser";
                $field[$k]["fieldname"] = "是否拥有CRM权限";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;
                $field[$k]["fieldstring"] = "postbe_crmuserlevel";
                $field[$k]["fieldname"] = "CRM权限级别";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;
                $field[$k]["fieldstring"] = "postbe_isgmccrm";
                $field[$k]["fieldname"] = "是否拥有集团招生权限";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;
                $field[$k]["fieldstring"] = "postbe_gmccrmlevel";
                $field[$k]["fieldname"] = "集团招生权限";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $result = array();
                $result['field'] = $field;
                if ($dataList) {
                    $result['list'] = $dataList == false ? array() : $dataList;
                    $res = array('error' => '0', 'errortip' => "职工职务获取成功", 'result' => $result);
                    ajax_return($res);
                } else {
                    $result['list'] = array();
                    ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => $result));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //获取客户明细表 -- 97
    function getCrmClientApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'Smcstudent', $request)) {
                $datawhere = "c.company_id = '{$pucArray['company_id']}' ";
                if (isset($request['upcode']) && $request['upcode'] == 'news') {
                    $newsTimes = time() - 3600 * 24 * 1.5;
                    $datawhere .= " AND (c.client_updatetime > '{$newsTimes}' or c.client_createtime > '{$newsTimes}')";
                }
                if (isset($request['channel_id']) && $request['channel_id'] !== '0') {
                    $datawhere .= " AND c.channel_id='{$request['channel_id']}'";
                }

                $upLimit = "";
                if (isset($request['page']) && $request['page'] !== '0') {
                    $limitwhere = $request['page'] * 30000;
                } else {
                    $limitwhere = 0;
                }
                $upLimit = "LIMIT {$limitwhere},30000";

                $sql = "select c.client_id,c.promotion_id,c.client_cnname,c.client_enname,c.client_fromtype,c.client_isgross,c.client_source,c.channel_id,
c.client_soursename,c.client_intention_level,c.client_intention_maxlevel,c.client_stubranch,c.client_answerphone,c.client_distributionstatus,c.client_tracestatus,
c.province_id,c.city_id,c.area_id,c.client_createtime,c.client_updatetime,c.client_frommobile
-- ,c.client_frompage,'' as spare_one
                from crm_client as c 
                WHERE {$datawhere}
                order by c.client_id
                {$upLimit}";
                $dataList = $this->DataControl->selectClear($sql);

                $field = array();
                $k = 0;
                $field[$k]["fieldstring"] = "client_id";
                $field[$k]["fieldname"] = "客户序号";
                $k++;

                $field[$k]["fieldstring"] = "promotion_id";
                $field[$k]["fieldname"] = "地推人员序号";
                $k++;

                $field[$k]["fieldstring"] = "client_cnname";
                $field[$k]["fieldname"] = "客户中文名";
                $k++;

                $field[$k]["fieldstring"] = "client_enname";
                $field[$k]["fieldname"] = "客户英文名";
                $k++;

                $field[$k]["fieldstring"] = "client_fromtype";
                $field[$k]["fieldname"] = "招生来源类型";
                $k++;

                $field[$k]["fieldstring"] = "client_isgross";
                $field[$k]["fieldname"] = "是否毛名单";
                $k++;

                $field[$k]["fieldstring"] = "client_source";
                $field[$k]["fieldname"] = "渠道类型";
                $k++;

                $field[$k]["fieldstring"] = "channel_id";
                $field[$k]["fieldname"] = "招生渠道序号";
                $k++;

                $field[$k]["fieldstring"] = "client_soursename";
                $field[$k]["fieldname"] = "渠道三";
                $k++;

                $field[$k]["fieldstring"] = "client_intention_level";
                $field[$k]["fieldname"] = "意向星级";
                $k++;

                $field[$k]["fieldstring"] = "client_intention_maxlevel";
                $field[$k]["fieldname"] = "最高意向星级";
                $k++;

                $field[$k]["fieldstring"] = "client_stubranch";
                $field[$k]["fieldname"] = "推荐学生编号";
                $k++;

                $field[$k]["fieldstring"] = "client_answerphone";
                $field[$k]["fieldname"] = "是否接通电话";
                $k++;

                $field[$k]["fieldstring"] = "client_distributionstatus";
                $field[$k]["fieldname"] = "分配状态";
                $k++;

                $field[$k]["fieldstring"] = "client_tracestatus";
                $field[$k]["fieldname"] = "跟踪状态";
                $k++;

                $field[$k]["fieldstring"] = "province_id";
                $field[$k]["fieldname"] = "省序号";
                $k++;

                $field[$k]["fieldstring"] = "city_id";
                $field[$k]["fieldname"] = "市序号";
                $k++;

                $field[$k]["fieldstring"] = "area_id";
                $field[$k]["fieldname"] = "区域序号";
                $k++;

                $field[$k]["fieldstring"] = "client_createtime";
                $field[$k]["fieldname"] = "创建时间";
                $k++;

                $field[$k]["fieldstring"] = "client_updatetime";
                $field[$k]["fieldname"] = "更新时间";
                $k++;

                $field[$k]["fieldstring"] = "client_frommobile";
                $field[$k]["fieldname"] = "推荐手机号";
                $k++;

//                $field[$k]["fieldstring"] = "client_frompage";
//                $field[$k]["fieldname"] = "接触点";
//                $k++;
//
//                $field[$k]["fieldstring"] = "spare_one";
//                $field[$k]["fieldname"] = "备用字段";
//                $k++;

                $result = array();
                $result['field'] = $field;
                if ($dataList) {
                    $result['list'] = $dataList == false ? array() : $dataList;
                    $res = array('error' => '0', 'errortip' => "客户信息获取成功", 'result' => $result);
                    ajax_return($res);
                } else {
                    $result['list'] = array();
                    ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => $result));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    function getCrmClientInfoApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'Smcstudent', $request)) {
                $datawhere = "c.company_id = '{$pucArray['company_id']}' ";
                if (isset($request['upcode']) && $request['upcode'] == 'news') {
                    $newsTimes = time() - 3600 * 24 * 1.5;
                    $datawhere .= " AND (c.client_updatetime > '{$newsTimes}' or c.client_createtime > '{$newsTimes}')";
                }

                $upLimit = "";
                if (isset($request['page']) && $request['page'] !== '0') {
                    $limitwhere = $request['page'] * 30000;
                } else {
                    $limitwhere = 0;
                }
                $upLimit = "LIMIT {$limitwhere},30000";

                $sql = "select c.client_id,c.client_frompage,'' as spare_one
                from crm_client as c 
                WHERE {$datawhere}
                and c.client_frompage<>''
                order by c.client_id
                {$upLimit}";
                $dataList = $this->DataControl->selectClear($sql);

                $field = array();
                $k = 0;
                $field[$k]["fieldstring"] = "client_id";
                $field[$k]["fieldname"] = "客户序号";
                $k++;

                $field[$k]["fieldstring"] = "client_frompage";
                $field[$k]["fieldname"] = "接触点";
                $k++;

                $field[$k]["fieldstring"] = "spare_one";
                $field[$k]["fieldname"] = "备用字段";
                $k++;

                $result = array();
                $result['field'] = $field;
                if ($dataList) {
                    $result['list'] = $dataList == false ? array() : $dataList;
                    $res = array('error' => '0', 'errortip' => "客户信息获取成功", 'result' => $result);
                    ajax_return($res);
                } else {
                    $result['list'] = array();
                    ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => $result));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //获取客户明细表 - 推荐老师明细 -- 97
    function getCrmClientTeacheridApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'Smcstudent', $request)) {
                $datawhere = "c.company_id = '{$pucArray['company_id']}' and c.client_teacherid > 0 ";
                if (isset($request['upcode']) && $request['upcode'] == 'news') {
                    $newsTimes = time() - 3600 * 24 * 1.5;
                    $datawhere .= " AND (c.client_updatetime > '{$newsTimes}' or c.client_createtime > '{$newsTimes}')";
                }

                $upLimit = "";
                if (isset($request['page']) && $request['page'] !== '0') {
                    $limitwhere = $request['page'] * 30000;
                } else {
                    $limitwhere = 0;
                }
                $upLimit = "LIMIT {$limitwhere},30000";

                $sql = "select c.client_id,c.client_teacherid 
                from crm_client as c WHERE {$datawhere} {$upLimit}";
                $dataList = $this->DataControl->selectClear($sql);

                $field = array();
                $k = 0;
                $field[$k]["fieldstring"] = "client_id";
                $field[$k]["fieldname"] = "客户序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "client_teacherid";
                $field[$k]["fieldname"] = "推荐教师序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $result = array();
                $result['field'] = $field;
                if ($dataList) {
                    $result['list'] = $dataList == false ? array() : $dataList;
                    $res = array('error' => '0', 'errortip' => "客户有推荐老师的信息获取成功", 'result' => $result);
                    ajax_return($res);
                } else {
                    $result['list'] = array();
                    ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => $result));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //获取客户意向课程明细 -- 97
    function getCrmClientIntentionApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'Smcstudent', $request)) {
                $datawhere = "c.company_id = '{$pucArray['company_id']}' and c.coursecat_id = i.coursecat_id  and i.coursecat_id>0 ";
                if (isset($request['upcode']) && $request['upcode'] == 'news') {
                    $newsTimes = time() - 3600 * 24 * 1.5;
                    $datawhere .= " AND (i.intention_updatetime > '{$newsTimes}')";
                }

                $upLimit = "";
                if (isset($request['page']) && $request['page'] !== '0') {
                    $limitwhere = $request['page'] * 30000;
                } else {
                    $limitwhere = 0;
                }
                $upLimit = "LIMIT {$limitwhere},30000";

                $sql = "SELECT min(i.intention_id) as intention_id,i.client_id,i.coursetype_id,i.coursecat_id,max(i.intention_is_delete) as intention_is_delete
                    FROM crm_client_intention as i,smc_code_coursecat c 
                    WHERE {$datawhere} 
                    group by i.client_id,i.coursetype_id,i.coursecat_id
                    order BY intention_id {$upLimit}";
                $dataList = $this->DataControl->selectClear($sql);

                $field = array();
                $k = 0;
                $field[$k]["fieldstring"] = "intention_id";
                $field[$k]["fieldname"] = "意向课程序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "client_id";
                $field[$k]["fieldname"] = "客户序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "coursetype_id";
                $field[$k]["fieldname"] = "意向班组序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "coursecat_id";
                $field[$k]["fieldname"] = "意向班种序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "intention_is_delete";
                $field[$k]["fieldname"] = "是否删除";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $result = array();
                $result['field'] = $field;
                if ($dataList) {
                    $result['list'] = $dataList == false ? array() : $dataList;
                    $res = array('error' => '0', 'errortip' => "客户意向课程信息获取成功", 'result' => $result);
                    ajax_return($res);
                } else {
                    $result['list'] = array();
                    ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => $result));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //获取客户柜询记录明细 -- 97
    function getCrmClientInviteApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'Smcstudent', $request)) {
                $datawhere = "i.company_id = '{$pucArray['company_id']}' ";
                if (isset($request['upcode']) && $request['upcode'] == 'news') {
                    $newsTimes = time() - 3600 * 24 * 1.5;
                    $datawhere .= " AND (i.invite_updatetime > '{$newsTimes}' or i.invite_createtime > '{$newsTimes}')";
                }

                $upLimit = "";
                if (isset($request['page']) && $request['page'] !== '0') {
                    $limitwhere = $request['page'] * 30000;
                } else {
                    $limitwhere = 0;
                }
                $upLimit = "LIMIT {$limitwhere},30000";

                $sql = "SELECT i.invite_id,i.school_id,i.client_id, i.marketer_id,i.invite_visittime,i.invite_isvisit,i.invite_genre,i.coursetype_id,i.coursecat_id,i.invite_createtime 
FROM crm_client_invite as i WHERE {$datawhere} GROUP BY i.invite_id {$upLimit}";
                $dataList = $this->DataControl->selectClear($sql);

                $field = array();
                $k = 0;
                $field[$k]["fieldstring"] = "invite_id";
                $field[$k]["fieldname"] = "邀约序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "school_id";
                $field[$k]["fieldname"] = "学校序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "client_id";
                $field[$k]["fieldname"] = "客户序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "marketer_id";
                $field[$k]["fieldname"] = "人员序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "invite_visittime";
                $field[$k]["fieldname"] = "邀约时间";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "invite_isvisit";
                $field[$k]["fieldname"] = "是否到访";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "invite_genre";
                $field[$k]["fieldname"] = "邀约类型";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "coursetype_id";
                $field[$k]["fieldname"] = "班组序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "coursecat_id";
                $field[$k]["fieldname"] = "班种序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "invite_createtime";
                $field[$k]["fieldname"] = "创建时间";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $result = array();
                $result['field'] = $field;
                if ($dataList) {
                    $result['list'] = $dataList == false ? array() : $dataList;
                    $res = array('error' => '0', 'errortip' => "客户邀约信息获取成功", 'result' => $result);
                    ajax_return($res);
                } else {
                    $result['list'] = array();
                    ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => $result));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    function getClassPlanApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'SmcClassPlan', $request)) {
                $datawhere = "b.company_id = '{$pucArray['company_id']}' ";
                if (isset($request['upcode']) && $request['upcode'] == 'news') {
                    $newsTimes = time() - 3600 * 24 * 1.5;
                    $datawhere .= " AND (a.lessonplan_createtime > '{$newsTimes}')";
                }

                if (isset($request['page']) && $request['page'] != '') {
                    $startnums = $request['page'] * 30000;
                    $upLimit = "LIMIT {$startnums},30000";
                } else {
                    $upLimit = "LIMIT 0,30000";
                }

                $sql = "select a.lessonplan_id,a.class_id,a.lessonplan_weekno,a.lessonplan_week,a.staffer_id,a.teachtype_code,a.poll_staffer_id,a.poll_teachtype_code,a.lessonplan_starttime,a.lessonplan_endtime 
                        from smc_class_lessonplan as a,smc_class as b 
                        where {$datawhere} and a.class_id=b.class_id {$upLimit}";

                $dataList = $this->DataControl->selectClear($sql);

                $field = array();
                $k = 0;
                $field[$k]["fieldstring"] = "lessonplan_id";
                $field[$k]["fieldname"] = "安排序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "class_id";
                $field[$k]["fieldname"] = "班级序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "lessonplan_weekno";
                $field[$k]["fieldname"] = "周次数";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "lessonplan_week";
                $field[$k]["fieldname"] = "周次";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "staffer_id";
                $field[$k]["fieldname"] = "主教教师序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "teachtype_code";
                $field[$k]["fieldname"] = "主教教师类型CODE";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "poll_staffer_id";
                $field[$k]["fieldname"] = "助教教师序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "poll_teachtype_code";
                $field[$k]["fieldname"] = "助教教师类型编号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "lessonplan_starttime";
                $field[$k]["fieldname"] = "上课起始时间";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "lessonplan_endtime";
                $field[$k]["fieldname"] = "上课结束时间";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $result = array();
                $result['field'] = $field;
                if ($dataList) {
                    $result['list'] = $dataList == false ? array() : $dataList;
                    $res = array('error' => '0', 'errortip' => "班级排课计划信息获取成功", 'result' => $result);
                    ajax_return($res);
                } else {
                    $result['list'] = array();
                    ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => $result));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //获取客户试听记录明细 -- 97
    function getCrmClientAuditionApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'Smcstudent', $request)) {
                $datawhere = "a.company_id = '{$pucArray['company_id']}' ";
                if (isset($request['upcode']) && $request['upcode'] == 'news') {
                    $newsTimes = time() - 3600 * 24 * 1.5;
                    $datawhere .= " AND (a.audition_updatetime > '{$newsTimes}' or a.audition_createtime > '{$newsTimes}')";
                }

                $upLimit = "";
                if (isset($request['page']) && $request['page'] !== '0') {
                    $limitwhere = $request['page'] * 30000;
                } else {
                    $limitwhere = 0;
                }
                $upLimit = "LIMIT {$limitwhere},30000";

                $sql = "SELECT a.audition_id,a.school_id,a.client_id,a.marketer_id,a.audition_visittime
                    ,a.audition_isvisit,a.audition_genre,a.coursetype_id,a.coursecat_id
                    ,a.course_id,a.class_id,a.hour_id,a.audition_createtime 
                    FROM crm_client_audition as a 
                    WHERE {$datawhere} 
                    GROUP BY a.audition_id {$upLimit}";
                $dataList = $this->DataControl->selectClear($sql);

                $field = array();
                $k = 0;
                $field[$k]["fieldstring"] = "audition_id";
                $field[$k]["fieldname"] = "试听序号";
                $k++;

                $field[$k]["fieldstring"] = "school_id";
                $field[$k]["fieldname"] = "学校序号";
                $k++;

                $field[$k]["fieldstring"] = "client_id";
                $field[$k]["fieldname"] = "客户序号";
                $k++;

                $field[$k]["fieldstring"] = "marketer_id";
                $field[$k]["fieldname"] = "人员序号";
                $k++;

                $field[$k]["fieldstring"] = "audition_visittime";
                $field[$k]["fieldname"] = "试听时间";
                $k++;

                $field[$k]["fieldstring"] = "audition_isvisit";
                $field[$k]["fieldname"] = "是否到访";
                $k++;

                $field[$k]["fieldstring"] = "audition_genre";
                $field[$k]["fieldname"] = "试听类型";
                $k++;

                $field[$k]["fieldstring"] = "coursetype_id";
                $field[$k]["fieldname"] = "班组序号";
                $k++;

                $field[$k]["fieldstring"] = "coursecat_id";
                $field[$k]["fieldname"] = "班种序号";
                $k++;

                $field[$k]["fieldstring"] = "course_id";
                $field[$k]["fieldname"] = "课程序号";
                $k++;

                $field[$k]["fieldstring"] = "class_id";
                $field[$k]["fieldname"] = "班级序号";
                $k++;

                $field[$k]["fieldstring"] = "hour_id";
                $field[$k]["fieldname"] = "课时序号";
                $k++;

                $field[$k]["fieldstring"] = "audition_createtime";
                $field[$k]["fieldname"] = "创建时间";
                $k++;

                $result = array();
                $result['field'] = $field;
                if ($dataList) {
                    $result['list'] = $dataList == false ? array() : $dataList;
                    $res = array('error' => '0', 'errortip' => "客户试听信息获取成功", 'result' => $result);
                    ajax_return($res);
                } else {
                    $result['list'] = array();
                    ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => $result));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //教务 获取拆并班明细表 -- 97
    function getSmcClassBreakoffApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'Smcstudent', $request)) {
                $datawhere = "b.company_id = '{$pucArray['company_id']}' ";
                if (isset($request['upcode']) && $request['upcode'] == 'news') {
                    $newsTimes = time() - 3600 * 24 * 1.5;
                    $datawhere .= " AND (b.breakoff_apply_time > '{$newsTimes}' or b.breakoff_audit_time > '{$newsTimes}' or b.breakoff_confirm_time > '{$newsTimes}')";
                }
                $upLimit = "";
                //首次更新时
//                $startnums = $request['page'] * 30000;
//                $upLimit = "LIMIT {$startnums},30000";

                $sql = "SELECT b.breakoff_id,b.school_id,b.class_id,b.breakoff_type,b.breakoff_status,breakoff_apply_staffer_id,breakoff_apply_time,breakoff_audit_staffer_id,breakoff_audit_time,breakoff_confirm_staffer_id,breakoff_confirm_time,
                (select t.tracks_note from smc_class_breakoff_track as t where t.breakoff_id=b.breakoff_id order by t.tracks_time asc,t.tracks_id asc limit 0,1) as reason 
                FROM smc_class_breakoff as b WHERE {$datawhere} ORDER BY b.breakoff_id {$upLimit}";
                $dataList = $this->DataControl->selectClear($sql);

                $field = array();
                $k = 0;
                $field[$k]["fieldstring"] = "breakoff_id";
                $field[$k]["fieldname"] = "中途结班申请序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "school_id";
                $field[$k]["fieldname"] = "学校序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "class_id";
                $field[$k]["fieldname"] = "班级序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "breakoff_type";
                $field[$k]["fieldname"] = "结班类别";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "breakoff_status";
                $field[$k]["fieldname"] = "申请状态";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "reason";
                $field[$k]["fieldname"] = "申请原因";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "breakoff_apply_staffer_id";
                $field[$k]["fieldname"] = "申请人ID";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "breakoff_apply_time";
                $field[$k]["fieldname"] = "申请时间";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "breakoff_audit_staffer_id";
                $field[$k]["fieldname"] = "分校审核人ID";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "breakoff_audit_time";
                $field[$k]["fieldname"] = "分校审核时间";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "breakoff_confirm_staffer_id";
                $field[$k]["fieldname"] = "集团审核人ID";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "breakoff_confirm_time";
                $field[$k]["fieldname"] = "集团审核时间";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $result = array();
                $result['field'] = $field;
                $result['list'] = $dataList == false ? array() : $dataList;
                if ($dataList) {
                    $res = array('error' => '0', 'errortip' => "拆并班明细信息获取成功", 'result' => $result);
                    ajax_return($res);
                } else {
                    ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => $result));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //教务 获取流失异动明细 -- 97
    function getSmcStudentChangelogApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'Smcstudent', $request)) {
                $datawhere = "c.company_id = '{$pucArray['company_id']}'  ";//and (c.stuchange_code = 'C02' or c.stuchange_code = 'C04')
                if (isset($request['upcode']) && $request['upcode'] == 'news') {
                    $newsTimes = time() - 3600 * 24 * 1.5;
                    $datawhere .= " AND c.changelog_createtime > '{$newsTimes}' ";
                }

                $upLimit = "";
                if (isset($request['page']) && $request['page'] !== '0') {
                    $limitwhere = $request['page'] * 30000;
                } else {
                    $limitwhere = 0;
                }
                $upLimit = "LIMIT {$limitwhere},30000";

                $sql = "SELECT c.changelog_id,c.school_id,c.coursetype_id,c.class_id,c.student_id,c.stuchange_code,c.changelog_note,c.changelog_day,c.changelog_category,
(SELECT MAX(y.clockinginlog_day) FROM smc_student_clockinginlog y WHERE y.school_id = c.school_id and y.student_id = c.student_id) as last_atte_date
FROM smc_student_changelog as c WHERE {$datawhere} ORDER BY c.changelog_id {$upLimit}";
                $dataList = $this->DataControl->selectClear($sql);

                $field = array();
                $k = 0;
                $field[$k]["fieldstring"] = "changelog_id";
                $field[$k]["fieldname"] = "异动序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "school_id";
                $field[$k]["fieldname"] = "学校序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "coursetype_id";
                $field[$k]["fieldname"] = "班组序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "class_id";
                $field[$k]["fieldname"] = "班级序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "student_id";
                $field[$k]["fieldname"] = "学生序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "stuchange_code";
                $field[$k]["fieldname"] = "异动类型";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "changelog_note";
                $field[$k]["fieldname"] = "异动备注";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "changelog_day";
                $field[$k]["fieldname"] = "异动日期";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "changelog_category";
                $field[$k]["fieldname"] = "流失类型";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "last_atte_date";
                $field[$k]["fieldname"] = "最后考勤日期";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;

                $result = array();
                $result['field'] = $field;
                if ($dataList) {
                    $result['list'] = $dataList == false ? array() : $dataList;
                    $res = array('error' => '0', 'errortip' => "流失异动明细信息获取成功", 'result' => $result);
                    ajax_return($res);
                } else {
                    $result['list'] = array();
                    ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => $result));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //校内招（二次招生） -- 获取客户明细 -- 97 （已同步到crm的学生）
    function getCrmStuApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'Smcstudent', $request)) {
                $datawhere = "s.student_id = t.student_id and t.company_id = '{$pucArray['company_id']}' ";
                if (isset($request['upcode']) && $request['upcode'] == 'news') {
                    $newsTimes = time() - 3600 * 24 * 1.5;
                    $datawhere .= " AND (s.student_createtime > '{$newsTimes}' or s.student_updatatime > '{$newsTimes}')";
                }
                $upLimit = "";
//                //首次更新时
//                $startnums = $request['page'] * 30000;
//                $upLimit = "LIMIT {$startnums},30000";

                $sql = "SELECT s.student_id,s.student_tracestatus,s.student_distributionstatus,s.student_intention_level,s.student_type,s.school_id,s.student_createtime,s.student_updatatime
FROM crm_student as s,smc_student as t 
WHERE {$datawhere} GROUP BY s.student_id {$upLimit}";
                $dataList = $this->DataControl->selectClear($sql);

                $field = array();
                $k = 0;
                $field[$k]["fieldstring"] = "student_id";
                $field[$k]["fieldname"] = "学生序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "student_tracestatus";
                $field[$k]["fieldname"] = "crm跟进状态";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "student_distributionstatus";
                $field[$k]["fieldname"] = "crm分配状态";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "student_intention_level";
                $field[$k]["fieldname"] = "crm意向星级";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "student_type";
                $field[$k]["fieldname"] = "crm学生类型";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "school_id";
                $field[$k]["fieldname"] = "二次跟进的学校ID";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "student_createtime";
                $field[$k]["fieldname"] = "创建时间";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "student_updatatime";
                $field[$k]["fieldname"] = "更新时间";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $result = array();
                $result['field'] = $field;
                if ($dataList) {
                    $result['list'] = $dataList == false ? array() : $dataList;
                    $res = array('error' => '0', 'errortip' => "客户试听信息获取成功", 'result' => $result);
                    ajax_return($res);
                } else {
                    $result['list'] = array();
                    ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => $result));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //校内招（二次招生） -- 获取客户柜询记录明细 -- 97  --  8888 集团未使用 柜询记录
    function getCrmStuInviteApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'Smcstudent', $request)) {
                $datawhere = "i.company_id = '{$pucArray['company_id']}' ";
                if (isset($request['upcode']) && $request['upcode'] == 'news') {
                    $newsTimes = time() - 3600 * 24 * 1.5;
                    $datawhere .= " AND (i.invite_updatetime > '{$newsTimes}' or i.invite_createtime > '{$newsTimes}')";
                }
                $upLimit = "";
                //首次更新时
//                $startnums = $request['page'] * 30000;
//                $upLimit = "LIMIT {$startnums},30000";

                $sql = "SELECT i.invite_id,i.school_id,i.student_id, i.marketer_id,i.invite_visittime,i.invite_isvisit,i.invite_genre,i.coursetype_id,i.coursecat_id
FROM crm_student_invite as i WHERE {$datawhere} GROUP BY i.invite_id {$upLimit}";
                $dataList = $this->DataControl->selectClear($sql);

                $field = array();
                $k = 0;
                $field[$k]["fieldstring"] = "invite_id";
                $field[$k]["fieldname"] = "邀约序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "school_id";
                $field[$k]["fieldname"] = "学校序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "student_id";
                $field[$k]["fieldname"] = "学生序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "marketer_id";
                $field[$k]["fieldname"] = "人员序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "invite_visittime";
                $field[$k]["fieldname"] = "邀约时间";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "invite_isvisit";
                $field[$k]["fieldname"] = "是否到访";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "invite_genre";
                $field[$k]["fieldname"] = "邀约类型";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "coursetype_id";
                $field[$k]["fieldname"] = "班组序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "coursecat_id";
                $field[$k]["fieldname"] = "班种序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $result = array();
                $result['field'] = $field;
                if ($dataList) {
                    $result['list'] = $dataList == false ? array() : $dataList;
                    $res = array('error' => '0', 'errortip' => "学生邀约信息获取成功", 'result' => $result);
                    ajax_return($res);
                } else {
                    $result['list'] = array();
                    ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => $result));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //校内招（二次招生） -- 获取客户试听记录明细 -- 97
    function getCrmStuAuditionApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'Smcstudent', $request)) {
                $datawhere = "a.company_id = '{$pucArray['company_id']}' ";
                if (isset($request['upcode']) && $request['upcode'] == 'news') {
                    $newsTimes = time() - 3600 * 24 * 1.5;
                    $datawhere .= " AND (a.audition_updatetime > '{$newsTimes}' or a.audition_createtime > '{$newsTimes}')";
                }
                $upLimit = "";
//                //首次更新时
//                $startnums = $request['page'] * 30000;
//                $upLimit = "LIMIT {$startnums},30000";

                $sql = "SELECT a.audition_id,a.school_id,a.student_id,a.marketer_id,a.audition_visittime,a.audition_isvisit,a.audition_genre,a.coursetype_id,a.coursecat_id
FROM crm_student_audition as a WHERE {$datawhere} GROUP BY a.audition_id {$upLimit}";
                $dataList = $this->DataControl->selectClear($sql);

                $field = array();
                $k = 0;
                $field[$k]["fieldstring"] = "audition_id";
                $field[$k]["fieldname"] = "试听序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "school_id";
                $field[$k]["fieldname"] = "学校序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "student_id";
                $field[$k]["fieldname"] = "学生序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "marketer_id";
                $field[$k]["fieldname"] = "人员序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "audition_visittime";
                $field[$k]["fieldname"] = "试听时间";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "audition_isvisit";
                $field[$k]["fieldname"] = "是否到访";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "audition_genre";
                $field[$k]["fieldname"] = "试听类型";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "coursetype_id";
                $field[$k]["fieldname"] = "班组序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "coursecat_id";
                $field[$k]["fieldname"] = "班种序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $result = array();
                $result['field'] = $field;
                if ($dataList) {
                    $result['list'] = $dataList == false ? array() : $dataList;
                    $res = array('error' => '0', 'errortip' => "客户试听信息获取成功", 'result' => $result);
                    ajax_return($res);
                } else {
                    $result['list'] = array();
                    ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => $result));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //校内招（二次招生） -- 获取客户分配负责人明细 -- 97
    function getCrmStuPrincipalApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'Smcstudent', $request)) {
                $datawhere = "s.company_id = '{$pucArray['company_id']}' ";
                if (isset($request['upcode']) && $request['upcode'] == 'news') {
                    $newsTimes = time() - 3600 * 24 * 1.5;
                    $datawhere .= " AND (p.principal_createtime > '{$newsTimes}' or p.principal_updatatime > '{$newsTimes}')";
                }
                $upLimit = "";
//                //首次更新时
//                $startnums = $request['page'] * 30000;
//                $upLimit = "LIMIT {$startnums},30000";

                $sql = "SELECT p.principal_id,p.school_id,p.student_id,p.marketer_id,p.principal_ismajor,p.principal_leave, 
                FROM_UNIXTIME(p.principal_createtime) AS principal_createtime,
                IF(p.principal_updatatime>0,FROM_UNIXTIME( p.principal_updatatime ),'--') AS principal_updatatime 
                FROM crm_student_principal as p 
                LEFT JOIN smc_school AS s ON s.school_id = p.school_id 
                WHERE {$datawhere} 
                ORDER BY p.principal_id {$upLimit}";
                $dataList = $this->DataControl->selectClear($sql);

                $field = array();
                $k = 0;
                $field[$k]["fieldstring"] = "school_id";
                $field[$k]["fieldname"] = "学校序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "student_id";
                $field[$k]["fieldname"] = "学生序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "marketer_id";
                $field[$k]["fieldname"] = "招生人员序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "principal_ismajor";
                $field[$k]["fieldname"] = "主招/辅招";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "principal_leave";
                $field[$k]["fieldname"] = "负责状态";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "principal_createtime";
                $field[$k]["fieldname"] = "创建时间";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "principal_updatatime";
                $field[$k]["fieldname"] = "更新时间";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $result = array();
                $result['field'] = $field;
                if ($dataList) {
                    $result['list'] = $dataList == false ? array() : $dataList;
                    $res = array('error' => '0', 'errortip' => "学生分配负责人明细获取成功", 'result' => $result);
                    ajax_return($res);
                } else {
                    $result['list'] = array();
                    ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => $result));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //校内招（二次招生） -- 获取客户意向课程明细 -- 97
    function getCrmStuIntentionApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'Smcstudent', $request)) {
                $datawhere = "c.company_id = '{$pucArray['company_id']}' and c.coursecat_id = i.coursecat_id  and i.coursecat_id>0 ";
                if (isset($request['upcode']) && $request['upcode'] == 'news') {
                    $newsTimes = time() - 3600 * 24 * 1.5;
                    $datawhere .= " AND (i.intention_updatetime > '{$newsTimes}')";
                }
                $upLimit = "";
                //首次更新时
                $startnums = $request['page'] * 30000;
                $upLimit = "LIMIT {$startnums},30000";

                $sql = "SELECT min(i.intention_id) as intention_id,i.student_id,i.coursetype_id,i.coursecat_id,max(i.intention_is_delete) as intention_is_delete
                    FROM crm_student_intention as i,smc_code_coursecat c 
                    WHERE {$datawhere} 
                    group by i.student_id,i.coursetype_id,i.coursecat_id
                    order BY intention_id {$upLimit}";
                $dataList = $this->DataControl->selectClear($sql);

                $field = array();
                $k = 0;
                $field[$k]["fieldstring"] = "intention_id";
                $field[$k]["fieldname"] = "意向课程序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "student_id";
                $field[$k]["fieldname"] = "学生序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "coursetype_id";
                $field[$k]["fieldname"] = "意向班组序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "coursecat_id";
                $field[$k]["fieldname"] = "意向班种序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "intention_is_delete";
                $field[$k]["fieldname"] = "是否删除";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $result = array();
                $result['field'] = $field;
                if ($dataList) {
                    $result['list'] = $dataList == false ? array() : $dataList;
                    $res = array('error' => '0', 'errortip' => "客户意向课程信息获取成功", 'result' => $result);
                    ajax_return($res);
                } else {
                    $result['list'] = array();
                    ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => $result));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //校内招（二次招生） -- 客户跟踪记录明细 -- 97
    function getCrmStuTrackApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'ClientTrack', $request)) {
                $datawhere = " s.company_id = '{$pucArray['company_id']}'";
                if (isset($request['upcode']) && $request['upcode'] == 'news') {
                    $newsTimes = time() - 3600 * 24 * 3;
                    $datawhere .= " AND t.track_createtime > '{$newsTimes}'";
                }
                $upLimit = "";

                //首次更新时
                $startnums = $request['page'] * 20000;
                $upLimit = "LIMIT {$startnums},20000";

                $sql = "SELECT t.track_id,t.student_id,t.school_id,t.marketer_id,t.track_intention_level,t.coursetype_id,t.coursecat_id,t.track_linktype,t.track_followmode,t.track_state,t.track_isactive,t.track_note,t.track_createtime
                        FROM crm_student_track AS t
                        LEFT JOIN smc_student AS s ON t.student_id = s.student_id 
                        WHERE {$datawhere}
                        order by t.track_id
                        {$upLimit}";
                $dataList = $this->DataControl->selectClear($sql);

                $field = array();
                $k = 0;
                $field[$k]["fieldstring"] = "track_id";
                $field[$k]["fieldname"] = "跟踪序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "student_id";
                $field[$k]["fieldname"] = "学生序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "school_id";
                $field[$k]["fieldname"] = "学校序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "marketer_id";
                $field[$k]["fieldname"] = "招生人员序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "track_intention_level";
                $field[$k]["fieldname"] = "意向星级";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "coursetype_id";
                $field[$k]["fieldname"] = "班组序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "coursecat_id";
                $field[$k]["fieldname"] = "班种序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "track_linktype";
                $field[$k]["fieldname"] = "沟通方式";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "track_followmode";
                $field[$k]["fieldname"] = "跟进模式";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "track_state";
                $field[$k]["fieldname"] = "跟踪状态";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "track_isactive";
                $field[$k]["fieldname"] = "是否学校主动跟踪";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "track_note";
                $field[$k]["fieldname"] = "沟通内容";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "track_createtime";
                $field[$k]["fieldname"] = "创建时间";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $result = array();
                $result['field'] = $field;
                if ($dataList) {
                    $result['list'] = $dataList == false ? array() : $dataList;
                    $res = array('error' => '0', 'errortip' => "校内招客户跟踪记录信息获取成功", 'result' => $result);
                    ajax_return($res);
                } else {
                    $result['list'] = array();
                    ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => $result));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //教务 满意度明细表 -- wgh
    function getSatisfactionDetailedApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'getClientSchool', $request)) {
                $datawhere = " s.school_class = 1 and s.school_istest = 0 and s.school_isdel = 0 and o.order_id > 0 and q.question_id > 0 ";
//                if(isset($request['upcode']) && $request['upcode'] == 'news'){
//                    $newsTimes = time()-3600*24*1.5;
//                    $datawhere .= " AND a.answer_addtime > '{$newsTimes}'";
//                }
//                $upLimit = "";
                //首次更新时
                $startnums = $request['page'] * 30000;
                $upLimit = "LIMIT {$startnums},30000";

                $sql = "SELECT a.answer_id,su.survey_name,o.student_branch,o.class_branch,cl.school_branch,q.question_name,qo.options_name
                        FROM app_survey_order_answer as a
                        LEFT JOIN app_survey_order as o ON o.order_id = a.order_id
                        LEFT JOIN app_survey as su ON su.survey_id = o.survey_id
                        LEFT JOIN app_classes as cl ON cl.class_branch = o.class_branch
                        LEFT JOIN app_school AS s ON s.school_branch = cl.school_branch
                        LEFT JOIN app_survey_question as q ON q.question_id = a.question_id
                        LEFT JOIN app_survey_question_options as qo ON qo.options_id = a.options_id
                        WHERE {$datawhere} ORDER BY a.answer_id {$upLimit}";

                $param = array();
                $param['sql'] = $sql;
                $dataApi = request_by_curl("https://ptcapi.kidcastle.cn/Api/runSql", dataEncode($param), "GET", array());
                $dataList = json_decode($dataApi, true);

                $field = array();
                $k = 0;
                $field[$k]["fieldstring"] = "answer_id";
                $field[$k]["fieldname"] = "调查问卷序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "school_branch";
                $field[$k]["fieldname"] = "学校序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "class_branch";
                $field[$k]["fieldname"] = "班级序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "student_branch";
                $field[$k]["fieldname"] = "学生序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "survey_name";
                $field[$k]["fieldname"] = "问卷名称";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "question_name";
                $field[$k]["fieldname"] = "问卷问题名称";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "options_name";
                $field[$k]["fieldname"] = "问卷答题选项";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;

                $result = array();
                $result['field'] = $field;
                if ($dataList['result']) {
                    $result['list'] = $dataList['result'];
                    ajax_return(array('error' => '0', 'errortip' => "满意度明细信息获取成功", 'result' => $result));
                } else {
                    $result['list'] = array();
                    ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => $result));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //站群 英检 -- xzl
    function getspokencheckApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'getClientSchool', $request)) {
                $datawhere = " d.goods_id in(9881,9882,9883,9884,9885,9886,9887) ";
                if(isset($request['upcode']) && $request['upcode'] == 'news'){
                    $newsTimes = time()-3600*24*3;
                    $datawhere .= " AND (a.order_addtime > {$newsTimes} or a.order_canceltime > {$newsTimes} or a.order_paytime > {$newsTimes})";
                }

                $upLimit = "";
                if (isset($request['page']) && $request['page'] !== '') {
                    $startnums = $request['page'] * 30000;
                    $upLimit = "LIMIT {$startnums},30000";
                } else {
                    $upLimit = " desc LIMIT 0,30000";
                }

                $sql = "SELECT a.order_id
                ,b.school_branch
                -- ,b.school_cnname
                ,a.student_branch
                -- ,a.student_cnname
                ,a.order_pid
                ,a.order_paymentprice
                ,a.order_status
                ,a.order_paytype
                -- ,d.goods_id
                ,d.goods_number
                ,d.goods_name
                ,a.order_addtime
                ,a.order_canceltime
                ,a.order_paytime
                ,a.order_isrefund
                FROM ptc_shop_order a
                left join ptc_school b on a.school_id=b.school_id
                left join ptc_shop_ordermodels c on c.order_pid=a.order_pid
                left join ptc_goods d on d.goods_id=c.goods_id
                WHERE {$datawhere} 
                ORDER BY a.order_id {$upLimit}";

                $param = array();
                $param['sql'] = $sql;
                $dataApi = request_by_curl("https://api.kidcastle.com.cn/Bibusiness/getDataFromSql", dataEncode($param), "GET", array());
                $dataList = json_decode($dataApi, true);
//                var_dump($dataApi);die;

                $field = array();
                $k = 0;
                $field[$k]["fieldstring"] = "order_id";
                $field[$k]["fieldname"] = "订单序号";
                $k++;

                $field[$k]["fieldstring"] = "school_branch";
                $field[$k]["fieldname"] = "学校序号";
                $k++;

                $field[$k]["fieldstring"] = "student_branch";
                $field[$k]["fieldname"] = "学生序号";
                $k++;

                $field[$k]["fieldstring"] = "order_pid";
                $field[$k]["fieldname"] = "订单编号";
                $k++;

                $field[$k]["fieldstring"] = "order_paymentprice";
                $field[$k]["fieldname"] = "订单金额";
                $k++;

                $field[$k]["fieldstring"] = "order_status";
                $field[$k]["fieldname"] = "订单状态";
                $k++;

                $field[$k]["fieldstring"] = "order_paytype";
                $field[$k]["fieldname"] = "支付方式";
                $k++;

                $field[$k]["fieldstring"] = "goods_number";
                $field[$k]["fieldname"] = "商品编码";
                $k++;

                $field[$k]["fieldstring"] = "goods_name";
                $field[$k]["fieldname"] = "商品名称";
                $k++;

                $field[$k]["fieldstring"] = "order_addtime";
                $field[$k]["fieldname"] = "下单时间";
                $k++;

                $field[$k]["fieldstring"] = "order_canceltime";
                $field[$k]["fieldname"] = "取消时间";
                $k++;

                $field[$k]["fieldstring"] = "order_paytime";
                $field[$k]["fieldname"] = "支付时间";
                $k++;

                $field[$k]["fieldstring"] = "order_isrefund";
                $field[$k]["fieldname"] = "是否退款";
                $k++;

                $result = array();
                $result['field'] = $field;
                if ($dataList['result']) {
                    $result['list'] = $dataList['result']['list'];
                    ajax_return(array('error' => '0', 'errortip' => "英检订单信息获取成功", 'result' => $result));
                } else {
                    $result['list'] = array();
                    ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => $result));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //获取地推人员明细 -- wgh
    function getPromotionApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'getClientSchool', $request)) {
                $datawhere = "company_id = '{$pucArray['company_id']}'";
                if (isset($request['upcode']) && $request['upcode'] == 'news') {
                    $newsTimes = time() - 3600 * 24 * 5;
                    $datawhere .= " AND (promotion_updatetime > '{$newsTimes}' OR promotion_createtime > '{$newsTimes}') ";
                }
                $limit = "";

                //首次更新时
                if (isset($request['page']) && $request['page'] !== '') {
                    $startnums = $request['page'] * 30000;
                    $limit = " LIMIT {$startnums},30000";
                } else {
                    $limit = " LIMIT 0,30000";
                }

                $sql = "SELECT promotion_id,promotion_name,promotion_jobnumber,IF(promotion_type=1, '销售', '市场') AS promotion_type
FROM crm_ground_promotion WHERE {$datawhere} ORDER BY promotion_id {$limit}";
                $dataList = $this->DataControl->selectClear($sql);

                $field = array();
                $k = 0;
                $field[$k]["fieldstring"] = "promotion_id";
                $field[$k]["fieldname"] = "地推人员序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "promotion_name";
                $field[$k]["fieldname"] = "地推人员姓名";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "promotion_jobnumber";
                $field[$k]["fieldname"] = "地推人员工号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "promotion_type";
                $field[$k]["fieldname"] = "地推类型";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;

                $result = array();
                $result['field'] = $field;
                if ($dataList) {
                    $result['list'] = $dataList == false ? array() : $dataList;
                    ajax_return(array('error' => '0', 'errortip' => "地推人员明细获取成功", 'result' => $result));
                } else {
                    $result['list'] = array();
                    ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => $result));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //获取学生在班明细--xzl
    function getSmcstudyApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'Smcstudy', $request)) {
                $datawhere = "d.company_id = '{$pucArray['company_id']}'";
                if (isset($request['upcode']) && $request['upcode'] == 'news') {
                    $nowTimes = date("Y-m-d", time() - 3600 * 24 * 5);
                    $preTimes = date("Y-m-d", time() - 3600 * 24 * 30);
                    $hocktime = time() - 3600 * 24 * 3;

                    $datawhere .= " AND (d.study_endday > '{$nowTimes}' OR d.study_beginday > '{$preTimes}')";
                    $datawhere .= " AND (exists(select 1 from smc_student_changelog where class_id=d.class_id and student_id=d.student_id and changelog_createtime>='{$hocktime}')
                        or exists(select 1 from smc_class where class_id=d.class_id and (class_createtime>='{$hocktime}' or class_updatatime>='{$hocktime}')))";
                }

                if (isset($request['class_id']) && $request['class_id'] !== '') {
                    $datawhere .= " and d.class_id='{$request['class_id']}'";
                }

                $upLimit = "";
                if (isset($request['page']) && $request['page'] !== '') {
                    $startnums = $request['page'] * 30000;
                    $upLimit = "LIMIT {$startnums},30000";
                } else {
                    $upLimit = " desc LIMIT 0,30000";
                }

                //首次更新时
//                $startnums = $request['page'] * 30000;
//                $upLimit = "LIMIT {$startnums},30000";

                $sql = "SELECT d.study_id,d.school_id,d.class_id,d.student_id,d.study_isreading,d.study_beginday,d.study_endday
                        FROM smc_student_study AS d 
                        WHERE {$datawhere}  
                        order by d.study_id {$upLimit}";
                $dataList = $this->DataControl->selectClear($sql);

                $field = array();
                $k = 0;
                $field[$k]["fieldstring"] = "study_id";
                $field[$k]["fieldname"] = "在班序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "school_id";
                $field[$k]["fieldname"] = "学校序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "class_id";
                $field[$k]["fieldname"] = "班级序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "student_id";
                $field[$k]["fieldname"] = "学生序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "study_isreading";
                $field[$k]["fieldname"] = "在班状态";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "study_beginday";
                $field[$k]["fieldname"] = "入班日期";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "study_endday";
                $field[$k]["fieldname"] = "出班日期";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;


                $result = array();
                $result['field'] = $field;
                $result['list'] = $dataList == false ? array() : $dataList;
                if ($dataList) {
                    $res = array('error' => '0', 'errortip' => "学生在读获取成功", 'result' => $result);
                    ajax_return($res);
                } else {
                    ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => $result));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //获取学生课程余额表明细--xzl
    function getStudentCoursebalanceApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'getStudentCoursebalance', $request)) {
                $datawhere = "b.company_id = '{$pucArray['company_id']}'";
                if (isset($request['upcode']) && $request['upcode'] == 'news') {
                    $newsTimes = time() - 3600 * 24 * 3;
                    $datawhere .= " AND (b.coursebalance_updatatime > '{$newsTimes}' OR b.coursebalance_createtime >= '{$newsTimes}')";
                }

                //首次更新时
                if (isset($request['page']) && $request['page'] !== '') {
                    $startnums = $request['page'] * 30000;
                    $limit = " LIMIT {$startnums},30000";
                } else {
                    $limit = " LIMIT 0,30000";
                }

                $sql = "SELECT b.companies_id,b.school_id,b.course_id,b.student_id,b.pricing_id,b.coursebalance_figure,b.coursebalance_time,b.coursebalance_issupervise
                        FROM smc_student_coursebalance AS b WHERE {$datawhere}  ORDER BY b.coursebalance_id ASC {$limit}";
                $dataList = $this->DataControl->selectClear($sql);

                $field = array();
                $k = 0;
                $field[$k]["fieldstring"] = "companies_id";
                $field[$k]["fieldname"] = "主体ID";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "school_id";
                $field[$k]["fieldname"] = "校区序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "course_id";
                $field[$k]["fieldname"] = "课程序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "student_id";
                $field[$k]["fieldname"] = "学员序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "pricing_id";
                $field[$k]["fieldname"] = "购买定价序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "coursebalance_figure";
                $field[$k]["fieldname"] = "剩余金额";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "coursebalance_time";
                $field[$k]["fieldname"] = "剩余课次";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "coursebalance_issupervise";
                $field[$k]["fieldname"] = "是否监管";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "coursebalance_createtime";
                $field[$k]["fieldname"] = "创建时间";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;


                $result = array();
                $result['field'] = $field;
                if ($dataList) {
                    $result['list'] = $dataList == false ? array() : $dataList;
                    $res = array('error' => '0', 'errortip' => "学生课程余额获取成功", 'result' => $result);
                    ajax_return($res);
                } else {
                    $result['list'] = array();
                    ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => $result));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //获取课程明细--xzl
    function getSmccourseApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'Smccourse', $request)) {
                $datawhere = "a.coursecat_id = b.coursecat_id AND b.coursetype_id = c.coursetype_id AND a.company_id = '{$pucArray['company_id']}'";
                $sql = "SELECT a.course_id,a.course_branch,a.course_cnname,b.coursecat_id,b.coursecat_branch,b.coursecat_cnname,
                c.coursetype_id,c.coursetype_branch,c.coursetype_cnname 
                ,a.course_inclasstype,a.course_openclasstype,a.course_sellclass,a.course_classnum,a.course_classtimerates
                ,a.course_classtimes
                ,concat(a.course_weekstandardnum,')',a.course_yearno) as course_yearno
                ,a.course_yearsort
                ,a.course_offline_main_percentage,a.course_offline_sub_percentage,a.course_online_main_percentage,a.course_online_sub_percentage
                ,a.course_isrenew
                ,a.course_issesson
                ,a.course_issupervise
                FROM smc_course AS a,smc_code_coursecat AS b,smc_code_coursetype AS c 
                WHERE {$datawhere}
                order by a.course_id";
                $dataList = $this->DataControl->selectClear($sql);

                $field = array();
                $k = 0;
                $field[$k]["fieldstring"] = "course_id";
                $field[$k]["fieldname"] = "课程序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "course_branch";
                $field[$k]["fieldname"] = "课程编号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "course_cnname";
                $field[$k]["fieldname"] = "课程名称";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "coursecat_id";
                $field[$k]["fieldname"] = "班种序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "coursecat_branch";
                $field[$k]["fieldname"] = "班种编号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "coursecat_cnname";
                $field[$k]["fieldname"] = "班种名称";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "coursetype_id";
                $field[$k]["fieldname"] = "班组序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "coursetype_branch";
                $field[$k]["fieldname"] = "班组编号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "coursetype_cnname";
                $field[$k]["fieldname"] = "班组名称";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "course_inclasstype";
                $field[$k]["fieldname"] = "上课方式";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "course_openclasstype";
                $field[$k]["fieldname"] = "公开课类型";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "course_sellclass";
                $field[$k]["fieldname"] = "销售方式";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "course_classnum";
                $field[$k]["fieldname"] = "课程课次";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "course_classtimerates";
                $field[$k]["fieldname"] = "课时比例";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "course_classtimes";
                $field[$k]["fieldname"] = "单次课时";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "course_yearno";
                $field[$k]["fieldname"] = "学年编号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "course_yearsort";
                $field[$k]["fieldname"] = "学年排序";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "course_isrenew";
                $field[$k]["fieldname"] = "留续课程";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "course_issesson";
                $field[$k]["fieldname"] = "季度课程";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "course_issupervise";
                $field[$k]["fieldname"] = "是否监管";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "course_offline_main_percentage";
                $field[$k]["fieldname"] = "线下主教";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "course_offline_sub_percentage";
                $field[$k]["fieldname"] = "线下助教";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "course_online_main_percentage";
                $field[$k]["fieldname"] = "线上主教";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "course_online_sub_percentage";
                $field[$k]["fieldname"] = "线上助教";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $result = array();
                $result['field'] = $field;
                $result['list'] = $dataList == false ? array() : $dataList;
                if ($dataList) {
                    $res = array('error' => '0', 'errortip' => "课程信息获取成功", 'result' => $result);
                    ajax_return($res);
                } else {
                    ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => $result));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //报名支付明细--xzl
    function getSmcregisterApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'Smcregister', $request)) {
                $datawhere = "a.company_id = '{$pucArray['company_id']}'";
                if (isset($request['upcode']) && $request['upcode'] == 'news') {
                    $newsTimes = time() - 3600 * 24 * 5;
                    $datawhere .= " AND (a.info_createtime > '{$newsTimes}'
                    or a.info_updatetime > '{$newsTimes}')";
                }

                if (isset($request['page']) && $request['upcode'] !== '0') {
                    $upLimit = $request['page'] * 30000;
                } else {
                    $upLimit = 0;
                }

                //首次更新时
//                $startnums = $request['page'] * 30000;
//                $upLimit = "LIMIT {$startnums},30000";


                $sql = "select a.info_id
                ,a.school_id
                ,(select companies_id from smc_payfee_order_pay where pay_id=a.pay_id) as companies_id
                ,a.info_status
                ,a.student_id
                ,a.coursetype_id
                ,a.coursecat_id
                ,a.info_type
                ,a.trading_pid
                ,a.pay_price
                ,a.pay_id
                ,a.pay_successtime
                ,a.xz_marketer_id
                ,a.kz_marketer_id
                from smc_student_registerinfo a
                where 1 
                and {$datawhere}
                order by a.info_id
                limit {$upLimit},30000";
                $dataList = $this->DataControl->selectClear($sql);

                $field = array();
                $k = 0;
                $field[$k]["fieldstring"] = "info_id";
                $field[$k]["fieldname"] = "数据序号";
                $field[$k]["show"] = 0;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "school_id";
                $field[$k]["fieldname"] = "学校序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "companies_id";
                $field[$k]["fieldname"] = "主体序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "info_status";
                $field[$k]["fieldname"] = "数据状态";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "student_id";
                $field[$k]["fieldname"] = "学生序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "coursetype_id";
                $field[$k]["fieldname"] = "班组序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "coursecat_id";
                $field[$k]["fieldname"] = "班种序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "info_type";
                $field[$k]["fieldname"] = "新招/扩科";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "trading_pid";
                $field[$k]["fieldname"] = "交易编号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "pay_price";
                $field[$k]["fieldname"] = "首缴金额";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "pay_id";
                $field[$k]["fieldname"] = "支付序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "pay_successtime";
                $field[$k]["fieldname"] = "支付时间";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "xz_marketer_id";
                $field[$k]["fieldname"] = "新招负责人";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "kz_marketer_id";
                $field[$k]["fieldname"] = "扩科负责人";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;


                $result = array();
                $result['field'] = $field;
                if ($dataList) {
                    foreach ($dataList as &$dataOne) {
                        $dataOne['pay_successtime'] = date("Y-m-d H:i:s", $dataOne['pay_successtime']);
                    }

                    $result['list'] = $dataList == false ? array() : $dataList;
                    $res = array('error' => '0', 'errortip' => "报名支付明细获取成功", 'result' => $result);
                    ajax_return($res);
                } else {
                    $result['list'] = array();
                    ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => $result));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //专案明细--xzl
    function getSmcguildpolicyApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'Smcguildpolicy', $request)) {
                $datawhere = " and a.company_id = '{$pucArray['company_id']}' ";
                if (isset($request['upcode']) && $request['upcode'] == 'news') {
                    $newsTimes = time() - 3600 * 24 * 5;
                    $datawhere .= " AND a.guildpolicy_createtime > '{$newsTimes}'";
                }

                $sql = "select a.guildpolicy_id
                ,a.student_id
                ,a.guildstutype_id
                ,a.channel_name
                ,a.guildpolicy_startdate
                ,a.guildpolicy_enddate
                ,a.guildpolicy_createtime
                from smc_student_guildpolicy a
                where a.guildpolicy_startdate<>''
                {$datawhere}
                order by a.guildpolicy_id";
                $dataList = $this->DataControl->selectClear($sql);

                $field = array();
                $k = 0;
                $field[$k]["fieldstring"] = "guildpolicy_id";
                $field[$k]["fieldname"] = "数据序号";
                $field[$k]["show"] = 0;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "student_id";
                $field[$k]["fieldname"] = "学生序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "guildstutype_id";
                $field[$k]["fieldname"] = "专案序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "channel_name";
                $field[$k]["fieldname"] = "专案名称";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "guildpolicy_startdate";
                $field[$k]["fieldname"] = "开始日期";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "guildpolicy_enddate";
                $field[$k]["fieldname"] = "结束日期";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "guildpolicy_createtime";
                $field[$k]["fieldname"] = "创建时间";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $result = array();
                $result['field'] = $field;
                if ($dataList) {
                    foreach ($dataList as &$dataOne) {
                        $dataOne['guildpolicy_createtime'] = $dataOne['guildpolicy_createtime'] > 0 ? date("Y-m-d H:i:s", $dataOne['guildpolicy_createtime']) : '';
                    }

                    $result['list'] = $dataList == false ? array() : $dataList;
                    $res = array('error' => '0', 'errortip' => "专案明细获取成功", 'result' => $result);
                    ajax_return($res);
                } else {
                    $result['list'] = array();
                    ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => $result));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //留班结算汇总--xzl
    function getEndcalcClassApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'EndcalcClass', $request)) {
                $datawhere = " and a.company_id = '{$pucArray['company_id']}' ";
                if (isset($request['upcode']) && $request['upcode'] == 'news') {
                    $newsTimes = time() - 3600 * 24 * 5;
                    $datawhere .= " AND (a.endcalc_createtime >= '{$newsTimes}' or a.endcalc_updatatime >= '{$newsTimes}')";
                }
                if (isset($request['page']) && $request['page'] !== '0') {
                    $limitwhere = $request['page'] * 30000;
                } else {
                    $limitwhere = 0;
                }

                $sql = " select endcalc_id
                ,school_id
                ,course_id
                ,class_id
                ,course_isrenew
                ,endcalc_issettle
                ,endcalc_startdate
                ,endcalc_enddate
                ,ifnull(endcalc_staffer_id,0) as endcalc_staffer_id
                ,ifnull(endcalc_chn_times,0) as endcalc_chn_times
                ,ifnull(endcalc_staffer_times,0) as endcalc_staffer_times
                from smc_class_endcalc a 
                where 1 {$datawhere}
                order by endcalc_id asc
                limit {$limitwhere},30000";
                $dataList = $this->DataControl->selectClear($sql);

                $field = array();
                $k = 0;
                $field[$k]["fieldstring"] = "endcalc_id";
                $field[$k]["fieldname"] = "结算序号";
                $field[$k]["show"] = 0;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "school_id";
                $field[$k]["fieldname"] = "学校序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "course_id";
                $field[$k]["fieldname"] = "课程序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "class_id";
                $field[$k]["fieldname"] = "班级序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "course_isrenew";
                $field[$k]["fieldname"] = "是否留续";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "endcalc_issettle";
                $field[$k]["fieldname"] = "结算状态";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "endcalc_startdate";
                $field[$k]["fieldname"] = "开班日期";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "endcalc_enddate";
                $field[$k]["fieldname"] = "结班日期";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "endcalc_staffer_id";
                $field[$k]["fieldname"] = "教师序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "endcalc_chn_times";
                $field[$k]["fieldname"] = "中师总课次";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "endcalc_staffer_times";
                $field[$k]["fieldname"] = "主教课次";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $result = array();
                $result['field'] = $field;
                $result['list'] = $dataList == false ? array() : $dataList;
                if ($dataList) {
                    $res = array('error' => '0', 'errortip' => "班级结算汇总获取成功", 'result' => $result);
                    ajax_return($res);
                } else {
                    ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => $result));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //留班结算明细--xzl
    function getEndcalcStudyApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'EndcalcStudy', $request)) {
                $datawhere = " and a.company_id = '{$pucArray['company_id']}' ";
                if (isset($request['upcode']) && $request['upcode'] == 'news') {
                    $newsTimes = strtotime(date("Y-m-d", time())) - 3600 * 24;
                    $datawhere .= " AND (a.study_createtime >= '{$newsTimes}' or a.study_updatetime >= '{$newsTimes}')";
                }
                if (isset($request['page']) && $request['page'] !== '0') {
                    $limitwhere = $request['page'] * 30000;
                } else {
                    $limitwhere = 0;
                }

                $sql = "select study_id
                ,endcalc_id
                ,student_id
                ,study_iscalculate
                ,study_outtype
                ,study_indate
                ,study_outdate
                ,study_upgradeprice
                ,study_nexttimes
                ,study_nextprice
                ,study_upgraderate
                from smc_class_endcalc_study a 
                where 1 {$datawhere}
                order by study_id asc
                limit {$limitwhere},30000";
                $dataList = $this->DataControl->selectClear($sql);

                $field = array();
                $k = 0;
                $field[$k]["fieldstring"] = "study_id";
                $field[$k]["fieldname"] = "明细序号";
                $field[$k]["show"] = 0;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "endcalc_id";
                $field[$k]["fieldname"] = "结算序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "student_id";
                $field[$k]["fieldname"] = "学生序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "study_iscalculate";
                $field[$k]["fieldname"] = "计算状态";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "study_outtype";
                $field[$k]["fieldname"] = "结算类型";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "study_indate";
                $field[$k]["fieldname"] = "入年级日期";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "study_outdate";
                $field[$k]["fieldname"] = "出年级日期";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "study_upgradeprice";
                $field[$k]["fieldname"] = "应留班金额";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "study_nexttimes";
                $field[$k]["fieldname"] = "后续留班课次";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "study_nextprice";
                $field[$k]["fieldname"] = "后续留班金额";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "study_upgraderate";
                $field[$k]["fieldname"] = "留班课时进度比";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $result = array();
                $result['field'] = $field;
                $result['list'] = $dataList == false ? array() : $dataList;
                if ($dataList) {
                    $res = array('error' => '0', 'errortip' => "班级结算明细获取成功", 'result' => $result);
                    ajax_return($res);
                } else {
                    ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => $result));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //续费预估明细--xzl
    function getEstimateApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'Estimate', $request)) {
                $datawhere = " and a.company_id = '{$pucArray['company_id']}' ";
                if (isset($request['upcode']) && $request['upcode'] == 'news') {
                    $newsTimes = date("Y-m-d");
                    $datawhere .= " AND a.calcdate >= '{$newsTimes}' ";
                }
                if (isset($request['page']) && $request['page'] !== '0') {
                    $limitwhere = $request['page'] * 30000;
                } else {
                    $limitwhere = 0;
                }

                $sql = "select a.school_id
                ,a.class_id
                ,a.student_id
                ,a.channel_name
                ,a.connect_times
                ,a.connect_main
                ,a.track_note
                ,a.course_id
                ,a.course_isrenew
                ,a.main_teacher
                ,a.coursetype_renewtimes
                ,a.renewal_times
                ,a.renewal_rates
                ,(a.renewal_amount-a.unpaid_price+a.spend_price) as renewal_price
                ,a.course_class_num
                ,a.class_num
                from smc_student_course_estimate a
                where 1 {$datawhere}
                order by a.class_id desc,a.student_id asc
                limit {$limitwhere},30000";
                $dataList = $this->DataControl->selectClear($sql);

                $field = array();
                $k = 0;
                $field[$k]["fieldstring"] = "school_id";
                $field[$k]["fieldname"] = "学校序号";
                $field[$k]["show"] = 0;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "class_id";
                $field[$k]["fieldname"] = "班级序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "student_id";
                $field[$k]["fieldname"] = "学生序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "channel_name";
                $field[$k]["fieldname"] = "专案名称";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "connect_times";
                $field[$k]["fieldname"] = "电访结果";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "connect_main";
                $field[$k]["fieldname"] = "主管电访结果";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "track_note";
                $field[$k]["fieldname"] = "电访内容";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "course_id";
                $field[$k]["fieldname"] = "课程序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "course_isrenew";
                $field[$k]["fieldname"] = "是否留续课程";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "main_teacher";
                $field[$k]["fieldname"] = "主教老师";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "coursetype_renewtimes";
                $field[$k]["fieldname"] = "班组留班课次";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "renewal_times";
                $field[$k]["fieldname"] = "预估续费课次";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "renewal_rates";
                $field[$k]["fieldname"] = "预估续费进度";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "renewal_price";
                $field[$k]["fieldname"] = "预估续费金额";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "course_class_num";
                $field[$k]["fieldname"] = "标准课次";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "class_num";
                $field[$k]["fieldname"] = "班级课次";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $result = array();
                $result['field'] = $field;
                $result['list'] = $dataList == false ? array() : $dataList;
                if ($dataList) {
                    $res = array('error' => '0', 'errortip' => "续费预估明细获取成功", 'result' => $result);
                    ajax_return($res);
                } else {
                    ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => $result));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //美语未入班明细--xzl
    function getUnStudyApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'getUnStudy', $request)) {
                $datawhere = " and a.company_id = '{$pucArray['company_id']}' ";
                if (isset($request['coursetype']) && $request['coursetype'] == 'all') {
                } else {
                    $datawhere .= " AND F.coursetype_id=65";
                }
                if (isset($request['page']) && $request['page'] !== '0') {
                    $limitwhere = $request['page'] * 30000;
                } else {
                    $limitwhere = 0;
                }

                $sql = "select TA.*,(select count(1) from smc_student_changelog x,smc_class y,smc_course z 
                    where x.class_id=y.class_id and y.course_id=z.course_id and x.stuchange_code='A07' and x.student_id=ta.student_id 
                    and x.school_id=ta.school_id and z.coursetype_id=65 and x.changelog_day>=ifnull(last_atte_date,'2029-01-01')) as delay_times 
                    from (
                    SELECT E.school_id 
                    ,C.student_id 
                    ,F.coursetype_id 
                    ,F.coursetype_branch 
                    ,F.coursetype_cnname 
                    ,GROUP_CONCAT(distinct D.coursecat_id) as coursecat_ids 
                    ,GROUP_CONCAT(distinct D.coursecat_branch) as coursecat_branches 
                    ,GROUP_CONCAT(distinct D.coursecat_cnname) as coursecat_cnnames 
                    ,GROUP_CONCAT(B.course_id) as course_ids 
                    ,GROUP_CONCAT(B.course_branch) AS course_branches 
                    ,GROUP_CONCAT(B.course_cnname) AS course_cnnames 
                    ,SUM(A.coursebalance_figure) AS total_price 
                    ,SUM(A.coursebalance_time) AS total_times 
                    ,(select sp.parenter_mobile from smc_student_family as sf inner join smc_parenter as sp on sf.parenter_id=sp.parenter_id 
                        where sf.student_id=A.student_id and sf.family_isdefault='1' limit 0,1) as parenter_mobile 
                    ,(select max(y.clockinginlog_day) from smc_student_clockinginlog y,smc_student_hourstudy z,smc_class x,smc_course w 
                        where y.hourstudy_id=z.hourstudy_id and z.class_id=x.class_id and x.course_id=w.course_id and y.clockinginlog_price>0 
                        and y.school_id=A.school_id and y.student_id=A.student_id and w.coursetype_id=D.coursetype_id) as last_atte_date 
                    FROM smc_student_coursebalance A  
                    LEFT JOIN smc_course B ON A.course_id=B.course_id AND A.company_id=B.company_id 
                    LEFT JOIN smc_student C ON A.student_id=C.student_id AND A.company_id=C.company_id 
                    LEFT JOIN smc_code_coursecat D ON B.coursecat_id=D.coursecat_id AND A.company_id=D.company_id 
                    INNER JOIN smc_school E ON A.school_id=E.school_id AND A.company_id=E.company_id
                    LEFT JOIN smc_code_coursetype F ON F.coursetype_id=D.coursetype_id AND F.company_id=D.company_id 
                    WHERE 1 {$datawhere}
                    AND B.course_inclasstype in (0,2) 
                    AND A.coursebalance_time>0 
                    AND NOT EXISTS(SELECT 1 FROM smc_student_study X,smc_class Y,smc_course Z 
                        WHERE X.student_id=A.student_id AND X.company_id=A.company_id 
                        AND X.class_id=Y.class_id AND Y.company_id=A.company_id  
                        AND Y.course_id=Z.course_id AND Z.company_id=A.company_id  
                        AND Z.coursecat_id=B.coursecat_id AND X.study_isreading=1  
                        AND X.study_endday>=CURDATE()) 
                    AND E.school_istest <> '1' AND E.school_isclose<> '1' 
                    GROUP BY E.school_id,C.student_id,F.coursetype_id 
                )ta
                where 1 
                limit {$limitwhere},30000";
                $dataList = $this->DataControl->selectClear($sql);
                if ($dataList) {
                    foreach ($dataList as &$dataOne) {
                        $dataOne['unstudy_type'] = '';
                        if ($dataOne['last_atte_date'] == '') {
                            $dataOne['unstudy_type'] = '新生未入班';
                        } else if ($dataOne['delay_times'] > 0) {
                            $dataOne['unstudy_type'] = '延班未入班';
                        } else {
                            $dataOne['unstudy_type'] = '老生未入班';
                        }
                    }
                }

                $field = array();
                $k = 0;
                $field[$k]["fieldstring"] = "school_id";
                $field[$k]["fieldname"] = "学校序号";
                $field[$k]["show"] = 0;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "student_id";
                $field[$k]["fieldname"] = "学生序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "unstudy_type";
                $field[$k]["fieldname"] = "未入班类型";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "coursetype_id";
                $field[$k]["fieldname"] = "班组序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "coursetype_branch";
                $field[$k]["fieldname"] = "班组编号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "coursetype_cnname";
                $field[$k]["fieldname"] = "班组名称";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "coursecat_ids";
                $field[$k]["fieldname"] = "班种序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "coursecat_branches";
                $field[$k]["fieldname"] = "班种编号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "coursecat_cnnames";
                $field[$k]["fieldname"] = "班种名称";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "course_ids";
                $field[$k]["fieldname"] = "课程序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "course_branches";
                $field[$k]["fieldname"] = "课程编号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "course_cnnames";
                $field[$k]["fieldname"] = "课程名称";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "total_price";
                $field[$k]["fieldname"] = "班组余额";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "total_times";
                $field[$k]["fieldname"] = "班组课次";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "parenter_mobile";
                $field[$k]["fieldname"] = "主要联系人";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "last_atte_date";
                $field[$k]["fieldname"] = "最近上课日期";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "delay_times";
                $field[$k]["fieldname"] = "延班次数";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "spare_one";
                $field[$k]["fieldname"] = "备用字段1";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "spare_two";
                $field[$k]["fieldname"] = "备用字段2";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $result = array();
                $result['field'] = $field;
                $result['list'] = $dataList == false ? array() : $dataList;
                if ($dataList) {
                    $res = array('error' => '0', 'errortip' => "未入班学生明细获取成功", 'result' => $result);
                    ajax_return($res);
                } else {
                    ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => $result));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //退费明细--xzl
    function getRefundApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'getRefund', $request)) {
                $datawhere = " and company_id = '{$pucArray['company_id']}' ";

                if (isset($request['upcode']) && $request['upcode'] == 'news') {
                    $newsTimes = strtotime(date("Y-m-d", time())) - 3600 * 24 * 5;
                    $datawhere .= " AND (refund_createtime>='{$newsTimes}' or refund_updatatime>='{$newsTimes}')";
                }

                if (isset($request['page']) && $request['page'] !== '0') {
                    $limitwhere = $request['page'] * 30000;
                } else {
                    $limitwhere = 0;
                }

                $sql = "select refund_id
                ,companies_id
                ,school_id
                ,student_id
                ,trading_pid
                ,refund_pid
                ,from_order_pid
                ,(case refund_tradeclass when 0 then '普通退费订单' when 1 then '教材退费订单' when 2 then '杂项退费订单' else '定金退费订单' end) as refund_tradeclass
                ,(case when refund_class='0' then '普通退款' else 'pos退款' end) as refund_class
                ,(case when refund_type='0' then '银行转账' else '原路返还' end) as refund_type
                ,(case when refund_from='0' then '家长自订' else '老师下单' end) as refund_from
                ,refund_name
                ,refund_mobile
                ,refund_bank
                ,refund_accountname
                ,refund_bankcard
                ,refund_reason
                ,refund_price
                ,(case when refund_isspecial='0' then '否' else '是' end) as refund_isspecial
                ,refund_specialreason
                ,refund_specialprice
                ,refund_payprice
                ,(case refund_status when 0 then '校区申请' when 1 then '审核通过' when 2 then '确认处理' when 3 then '确定金额' when 4 then '完成退款' when -1 then '拒绝退款' else '未知错误' end) as refund_status
                ,staffer_id
                ,refund_createtime
                ,refund_updatatime
                from smc_refund_order
                where 1 {$datawhere}
                order by refund_id
                limit {$limitwhere},30000";
                $dataList = $this->DataControl->selectClear($sql);

                $field = array();
                $k = 0;
                $field[$k]["fieldstring"] = "refund_id";
                $field[$k]["fieldname"] = "退费序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "companies_id";
                $field[$k]["fieldname"] = "主体序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "school_id";
                $field[$k]["fieldname"] = "学校序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "student_id";
                $field[$k]["fieldname"] = "学生序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "trading_pid";
                $field[$k]["fieldname"] = "交易编号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "refund_pid";
                $field[$k]["fieldname"] = "退费编号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "from_order_pid";
                $field[$k]["fieldname"] = "来源订单号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "refund_tradeclass";
                $field[$k]["fieldname"] = "退费订单类型";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "refund_class";
                $field[$k]["fieldname"] = "退费类型";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "refund_type";
                $field[$k]["fieldname"] = "退费方式";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "refund_from";
                $field[$k]["fieldname"] = "订单来源";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "refund_name";
                $field[$k]["fieldname"] = "联系人姓名";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "refund_mobile";
                $field[$k]["fieldname"] = "联系人电话";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "refund_bank";
                $field[$k]["fieldname"] = "开户行";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "refund_accountname";
                $field[$k]["fieldname"] = "开户名";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "refund_bankcard";
                $field[$k]["fieldname"] = "银行卡号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "refund_reason";
                $field[$k]["fieldname"] = "退费原因";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "refund_price";
                $field[$k]["fieldname"] = "退费金额";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "refund_isspecial";
                $field[$k]["fieldname"] = "是否含超退";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "refund_specialreason";
                $field[$k]["fieldname"] = "超退原因";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "refund_specialprice";
                $field[$k]["fieldname"] = "超退金额";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "refund_payprice";
                $field[$k]["fieldname"] = "实际退费金额";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "refund_status";
                $field[$k]["fieldname"] = "退费状态";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "staffer_id";
                $field[$k]["fieldname"] = "最近经办人";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "refund_createtime";
                $field[$k]["fieldname"] = "创建时间";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "refund_updatatime";
                $field[$k]["fieldname"] = "更新时间";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "spare_one";
                $field[$k]["fieldname"] = "备用字段1";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "spare_two";
                $field[$k]["fieldname"] = "备用字段2";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $result = array();
                $result['field'] = $field;
                $result['list'] = $dataList == false ? array() : $dataList;
                if ($dataList) {
                    $res = array('error' => '0', 'errortip' => "退费明细获取成功", 'result' => $result);
                    ajax_return($res);
                } else {
                    ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => $result));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //获取校区购买课程--xzl
    function getOrderCourseApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'getOrderCourse', $request)) {
                $datawhere = " and b.company_id = '{$pucArray['company_id']}' ";
                if (isset($request['upcode']) && $request['upcode'] == 'news') {
                    $newsTimes = strtotime(date("Y-m-d", time())) - 3600 * 24 * 2;
                    $datawhere .= " AND (b.order_createtime>='{$newsTimes}' or b.order_updatatime>='{$newsTimes}')";
                }
                if (isset($request['page']) && $request['page'] !== '0') {
                    $limitwhere = $request['page'] * 30000;
                } else {
                    $limitwhere = 0;
                }

                $sql = "select a.ordercourse_id
                ,a.course_id
                ,a.order_pid
                ,a.ordercourse_buynums
                ,a.ordercourse_unitprice
                ,a.ordercourse_totalprice
                ,b.trading_pid
                ,b.coursecat_id
                ,b.coursetype_id
                ,b.trading_pid
                ,b.order_paymentprice
                ,b.order_paidprice
                ,b.order_arrearageprice
                ,b.order_status
                ,b.companies_id
                ,b.school_id
                ,b.student_id
                ,FROM_UNIXTIME(b.order_createtime,'%Y-%m-%d') as order_date
                ,(select FROM_UNIXTIME(pay_successtime,'%Y-%m-%d') from smc_payfee_order_pay where order_pid=b.order_pid and pay_issuccess=1 and paytype_code not in ('feewaiver','canceldebts') order by pay_successtime desc limit 0,1) as pay_date
                ,(select paytype_code from smc_payfee_order_pay where order_pid=b.order_pid and pay_issuccess=1 and paytype_code not in ('feewaiver','canceldebts') order by pay_successtime desc limit 0,1) as paytype_code
                from smc_payfee_order_course a
                left join smc_payfee_order b on a.order_pid=b.order_pid
                where b.company_id='8888'
                AND B.order_type=0
                {$datawhere}
                order by a.ordercourse_id
                limit {$limitwhere},30000";
                $dataList = $this->DataControl->selectClear($sql);

                $k = 0;
                $field = array();
                $field[$k]["fieldstring"] = "ordercourse_id";
                $field[$k]["fieldname"] = "购课序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "school_id";
                $field[$k]["fieldname"] = "学校序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "student_id";
                $field[$k]["fieldname"] = "学员序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "course_id";
                $field[$k]["fieldname"] = "课程序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "trading_pid";
                $field[$k]["fieldname"] = "交易编号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "order_pid";
                $field[$k]["fieldname"] = "订单编号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "companies_id";
                $field[$k]["fieldname"] = "主体序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "coursetype_id";
                $field[$k]["fieldname"] = "班组序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "student_branch";
                $field[$k]["fieldname"] = "班种序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "course_id";
                $field[$k]["fieldname"] = "课程序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "order_date";
                $field[$k]["fieldname"] = "下单日期";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "pay_date";
                $field[$k]["fieldname"] = "最后支付日期";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "pay_date";
                $field[$k]["fieldname"] = "最后支付日期";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "paytype_code";
                $field[$k]["fieldname"] = "最后支付类型";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "order_paymentprice";
                $field[$k]["fieldname"] = "订单应付金额";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "order_paidprice";
                $field[$k]["fieldname"] = "订单已付金额";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "order_arrearageprice";
                $field[$k]["fieldname"] = "订单欠费金额";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "order_status";
                $field[$k]["fieldname"] = "订单状态";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "ordercourse_buynums";
                $field[$k]["fieldname"] = "课程课次";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "ordercourse_unitprice";
                $field[$k]["fieldname"] = "课程单价";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "ordercourse_totalprice";
                $field[$k]["fieldname"] = "课程总额";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $result = array();
                $result['field'] = $field;
                $result['list'] = $dataList == false ? array() : $dataList;
                if ($dataList) {
                    $res = array('error' => '0', 'errortip' => "购课信息获取成功", 'result' => $result);
                    ajax_return($res);
                } else {
                    ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => $result));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //获取校区学生账户余额--xzl
    function getStudentBalanceApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'getStudentBalance', $request)) {
                $datawhere = " and a.company_id = '{$pucArray['company_id']}' ";
                $havingwhere = " having 1 ";
                if (isset($request['upcode']) && $request['upcode'] == 'news') {
                    $newsTimes = strtotime(date("Y-m-d", time())) - 3600 * 24 * 3;
                    $havingwhere .= " AND balance_updatetime>='{$newsTimes}'";
                }
                if (isset($request['page']) && $request['page'] !== '0') {
                    $limitwhere = $request['page'] * 30000;
                } else {
                    $limitwhere = 0;
                }

                $sql = "select a.school_id,a.student_id,sum(student_balance+student_withholdbalance) as balance
                ,ifnull((select max(balancelog_time) from smc_student_balancelog where student_id=a.student_id and school_id=a.school_id),0) as balance_updatetime
                from smc_student_balance a
                where 1 {$datawhere}
                group by a.school_id,a.student_id
                {$havingwhere}
                order by balance_updatetime desc
                limit {$limitwhere},30000";
                $dataList = $this->DataControl->selectClear($sql);

                $k = 0;
                $field = array();
                $field[$k]["fieldstring"] = "school_id";
                $field[$k]["fieldname"] = "学校序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "student_id";
                $field[$k]["fieldname"] = "学员序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "balance";
                $field[$k]["fieldname"] = "账户余额";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "balance_updatetime";
                $field[$k]["fieldname"] = "更新时间";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $result = array();
                $result['field'] = $field;
                $result['list'] = $dataList == false ? array() : $dataList;
                if ($dataList) {
                    $res = array('error' => '0', 'errortip' => "账户余额信息获取成功", 'result' => $result);
                    ajax_return($res);
                } else {
                    ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => $result));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //获取校区收入--xzl
    function getSchoolIncomeApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'getSchoolIncome', $request)) {
                $datawhere = " and a.company_id = '{$pucArray['company_id']}' ";
                if ($request['income_type'] == '0') {
                    $datawhere = " and a.income_type=0 and a.income_confirmtime>=1640966400 and a.hourstudy_id>0";
                }
                if (isset($request['page']) && $request['page'] !== '0') {
                    $limitwhere = $request['page'] * 30000;
                    $order = ' asc';
                } else {
                    $limitwhere = 0;
                    $order = ' desc';
                }

                $sql = "select a.income_id,a.school_id,a.student_id,a.course_id,a.class_id,a.income_type,a.income_price,a.hourstudy_id
                ,FROM_UNIXTIME(a.income_confirmtime, '%Y-%m-%d') as income_date,a.companies_id
                from smc_school_income a
                where 1 {$datawhere}
                order by income_id {$order}
                limit {$limitwhere},30000";
                $dataList = $this->DataControl->selectClear($sql);

                $k = 0;
                $field = array();
                $field[$k]["fieldstring"] = "income_id";
                $field[$k]["fieldname"] = "收入序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "school_id";
                $field[$k]["fieldname"] = "学校序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "student_id";
                $field[$k]["fieldname"] = "学员序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "course_id";
                $field[$k]["fieldname"] = "课程序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "class_id";
                $field[$k]["fieldname"] = "班级序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = $request['income_type'] == '0' ? "hourstudy_id" : "income_type";
                $field[$k]["fieldname"] = $request['income_type'] == '0' ? "考勤序号" : "收入类型";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "income_price";
                $field[$k]["fieldname"] = "收入金额";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "income_date";
                $field[$k]["fieldname"] = "收入日期";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "companies_id";
                $field[$k]["fieldname"] = "主体序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $result = array();
                $result['field'] = $field;
                $result['list'] = $dataList == false ? array() : $dataList;
                if ($dataList) {
                    $res = array('error' => '0', 'errortip' => "收入信息获取成功", 'result' => $result);
                    ajax_return($res);
                } else {
                    ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => $result));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //主体明细--xzl
    function getCompaniesCodeApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'getCompanies', $request)) {

                $datawhere = " and a.company_id = '{$pucArray['company_id']}' ";

                $sql = "select companies_id
                ,companies_cnname
                ,companies_agencyid
                ,companies_issupervise
                ,companies_supervisetime
                from gmc_code_companies a
                where 1 {$datawhere}
                and a.companies_id<>9
                order by a.companies_id";
                $dataList = $this->DataControl->selectClear($sql);

                $field = array();
                $k = 0;
                $field[$k]["fieldstring"] = "companies_id";
                $field[$k]["fieldname"] = "主体序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "companies_cnname";
                $field[$k]["fieldname"] = "主体名称";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "companies_agencyid";
                $field[$k]["fieldname"] = "招行编码";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "companies_issupervise";
                $field[$k]["fieldname"] = "是否监管";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "companies_supervisetime";
                $field[$k]["fieldname"] = "监管时间戳";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "supervisetime";
                $field[$k]["fieldname"] = "监管时间";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
                $k++;

                $result = array();
                $result['field'] = $field;
                if ($dataList) {
                    foreach ($dataList as &$dataOne) {
                        $dataOne['supervisetime'] = $dataOne['companies_supervisetime'] > 0 ? date("Y-m-d H:i:s", $dataOne['companies_supervisetime']) : '';
                    }

                    $result['list'] = $dataList == false ? array() : $dataList;
                    $res = array('error' => '0', 'errortip' => "主体明细获取成功", 'result' => $result);
                    ajax_return($res);
                } else {
                    $result['list'] = array();
                    ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => $result));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //获取校区收入--xzl
    function getTransIncomeApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'getSchoolIncome', $request)) {
                $datawhere = " and a.company_id = '{$pucArray['company_id']}' ";

                if (isset($request['page'])) {
                    $limitwhere = $request['page'] * 30000;
                    $order = ' asc';
                } else {
                    $limitwhere = 0;
                    $order = ' desc';
                }

                $sql = "select a.income_id,a.companies_id,a.school_id,a.student_id,a.course_id,a.class_id,a.income_price,a.hourstudy_id
                ,FROM_UNIXTIME(a.income_confirmtime, '%Y-%m-%d') as income_date,c.hour_lessontimes
                from smc_school_income a
                left join smc_student_hourstudy b on a.hourstudy_id=b.hourstudy_id
                left join smc_class_hour c on b.hour_id=c.hour_id
                where 1 {$datawhere}
                and a.income_type=0 
                and a.income_confirmtime>=1640966400 
                and a.hourstudy_id>0
                order by income_id {$order}
                limit {$limitwhere},30000";
                $dataList = $this->DataControl->selectClear($sql);

                $k = 0;
                $field = array();
                $field[$k]["fieldstring"] = "income_id";
                $field[$k]["fieldname"] = "收入序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "companies_id";
                $field[$k]["fieldname"] = "主体序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "school_id";
                $field[$k]["fieldname"] = "学校序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "student_id";
                $field[$k]["fieldname"] = "学员序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "course_id";
                $field[$k]["fieldname"] = "课程序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "class_id";
                $field[$k]["fieldname"] = "班级序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "hourstudy_id";
                $field[$k]["fieldname"] = "考勤序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "income_price";
                $field[$k]["fieldname"] = "收入金额";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "income_date";
                $field[$k]["fieldname"] = "收入日期";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "hour_lessontimes";
                $field[$k]["fieldname"] = "课次序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $result = array();
                $result['field'] = $field;
                $result['list'] = $dataList == false ? array() : $dataList;
                if ($dataList) {
                    $res = array('error' => '0', 'errortip' => "收入信息获取成功", 'result' => $result);
                    ajax_return($res);
                } else {
                    ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => $result));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //获取学生考勤明细--xzl--cancel
    function getHourStudyApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'getHourStudy', $request)) {
                $datawhere = " and b.company_id = '{$pucArray['company_id']}' ";

                if (isset($request['page'])) {
                    $limitwhere = $request['page'] * 30000;
                    $order = ' asc';
                } else {
                    $limitwhere = 0;
                    $order = ' desc';
                }

                $sql = "select a.hourstudy_id,a.student_id,a.class_id,a.hour_id,a.hourstudy_checkin,a.hourstudy_makeup
                ,(select sum(income_price) from smc_school_income where student_id=a.student_id and class_id=a.class_id and hourstudy_id=a.hourstudy_id and income_type=0) as income_price
                from smc_student_hourstudy a
                left join smc_class b on a.class_id=b.class_id
                where 1 {$datawhere}
                and b.class_type=0
                and b.class_enddate>='2022-07-01'
                order by hourstudy_id {$order}
                limit {$limitwhere},30000";
                $dataList = $this->DataControl->selectClear($sql);

                $k = 0;
                $field = array();
                $field[$k]["fieldstring"] = "hourstudy_id";
                $field[$k]["fieldname"] = "考勤序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "student_id";
                $field[$k]["fieldname"] = "学员序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "class_id";
                $field[$k]["fieldname"] = "班级序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "hour_id";
                $field[$k]["fieldname"] = "课时序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "hourstudy_checkin";
                $field[$k]["fieldname"] = "是否考勤";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "hourstudy_makeup";
                $field[$k]["fieldname"] = "是否补课";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "income_price";
                $field[$k]["fieldname"] = "耗课金额";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $result = array();
                $result['field'] = $field;
                $result['list'] = $dataList == false ? array() : $dataList;
                if ($dataList) {
                    $res = array('error' => '0', 'errortip' => "考勤信息获取成功", 'result' => $result);
                    ajax_return($res);
                } else {
                    ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => $result));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //获取非监管扣除明细--xzl
    function getTransferReduceApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'getTransferReduce', $request)) {
                $datawhere = " 1 ";

                if (isset($request['upcode']) && $request['upcode'] == 'news') {
                    $newsTimes = strtotime(date("Y-m-d", time())) - 3600 * 24 * 3;
                    $datawhere .= " AND (a.reduce_createtime>='{$newsTimes}' or a.reduce_updatetime>='{$newsTimes}') ";
                }

                if (isset($request['page']) && $request['page'] !== '0') {
                    $limitwhere = $request['page'] * 30000;
                } else {
                    $limitwhere = 0;
                }

                $sql = "select a.reduce_id,a.course_id,a.class_id,a.hour_lessontimes,a.student_id,a.order_pid,a.reduce_price,'' as spare_1,'' as spare_2
                from cmb_trans_transfer_reduce a
                where {$datawhere}
                limit {$limitwhere},30000";
                $dataList = $this->DataControl->selectClear($sql);

                $k = 0;
                $field = array();
                $field[$k]["fieldstring"] = "reduce_id";
                $field[$k]["fieldname"] = "扣除序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "course_id";
                $field[$k]["fieldname"] = "课程序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "class_id";
                $field[$k]["fieldname"] = "班级序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "hour_lessontimes";
                $field[$k]["fieldname"] = "课次序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "student_id";
                $field[$k]["fieldname"] = "学生序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "order_pid";
                $field[$k]["fieldname"] = "订单编号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "reduce_price";
                $field[$k]["fieldname"] = "扣除金额";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "spare_1";
                $field[$k]["fieldname"] = "备用字段1";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "spare_2";
                $field[$k]["fieldname"] = "备用字段2";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $result = array();
                $result['field'] = $field;
                $result['list'] = $dataList == false ? array() : $dataList;
                if ($dataList) {
                    $res = array('error' => '0', 'errortip' => "扣除监管金额信息获取成功", 'result' => $result);
                    ajax_return($res);
                } else {
                    ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => $result));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //获取划拨申请明细--xzl
    function getTransferApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'getTransfer', $request)) {
                $datawhere = " 1 ";

                if (isset($request['upcode']) && $request['upcode'] == 'news') {
                    $newsTimes = strtotime(date("Y-m-d", time())) - 3600 * 24 * 3;
                    $datawhere .= " AND (transfer_createtime>='{$newsTimes}' or transfer_updatetime>='{$newsTimes}') ";
                }

                if (isset($request['page']) && $request['page'] !== '0') {
                    $limitwhere = $request['page'] * 20000;
                } else {
                    $limitwhere = 0;
                }

                $sql = "select transfer_id,companies_id,agency_id,school_id,coursetype_id,coursecat_id,course_id
                ,batch_pid,subTransferId,batch_date,order_pid,class_id,student_id,hourstudy_id,income_id
                ,income_price,income_times,income_date,confirm_type,income_isconfirm,confirm_ip,confirm_phone
                ,FROM_UNIXTIME(confirm_createtime, '%Y-%m-%d') as confirm_createtime
                ,(case transfer_status when 2 then '已划拨' when 1 then '划拨中' when 0 then '未申请' when '-2' then '同步失败' when '-3' then '错误数据' else '未知错误' end) as transfer_status
                ,is_confirm
                ,'' as spare_1,'' as spare_2
                from cmb_trans_transfer 
                where {$datawhere}
                order by transfer_id
                limit {$limitwhere},20000";
                $dataList = $this->DataControl->selectClear($sql);

                $k = 0;
                $field = array();
                $field[$k]["fieldstring"] = "transfer_id";
                $field[$k]["fieldname"] = "划拨明细序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "companies_id";
                $field[$k]["fieldname"] = "主体序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "agency_id";
                $field[$k]["fieldname"] = "招行编码";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "school_id";
                $field[$k]["fieldname"] = "学校序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "coursetype_id";
                $field[$k]["fieldname"] = "班组序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "coursecat_id";
                $field[$k]["fieldname"] = "班种序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "course_id";
                $field[$k]["fieldname"] = "课程序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "batch_pid";
                $field[$k]["fieldname"] = "批次号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "subTransferId";
                $field[$k]["fieldname"] = "子批次号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "batch_date";
                $field[$k]["fieldname"] = "批次日期";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "order_pid";
                $field[$k]["fieldname"] = "订单编号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "class_id";
                $field[$k]["fieldname"] = "班级序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "student_id";
                $field[$k]["fieldname"] = "学生序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "hourstudy_id";
                $field[$k]["fieldname"] = "考勤序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "income_id";
                $field[$k]["fieldname"] = "收入序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "income_price";
                $field[$k]["fieldname"] = "收入金额";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "income_times";
                $field[$k]["fieldname"] = "收入课次";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "income_date";
                $field[$k]["fieldname"] = "收入日期";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "confirm_type";
                $field[$k]["fieldname"] = "确认类型";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "confirm_createtime";
                $field[$k]["fieldname"] = "确认日期";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "income_isconfirm";
                $field[$k]["fieldname"] = "是否确认";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "confirm_ip";
                $field[$k]["fieldname"] = "确认ip";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "confirm_phone";
                $field[$k]["fieldname"] = "确认电话";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "transfer_status";
                $field[$k]["fieldname"] = "划拨状态";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "is_confirm";
                $field[$k]["fieldname"] = "确认可同步";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "spare_1";
                $field[$k]["fieldname"] = "备用字段1";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "spare_2";
                $field[$k]["fieldname"] = "备用字段2";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $result = array();
                $result['field'] = $field;
                $result['list'] = $dataList == false ? array() : $dataList;
                if ($dataList) {
                    $res = array('error' => '0', 'errortip' => "扣除监管金额信息获取成功", 'result' => $result);
                    ajax_return($res);
                } else {
                    ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => $result));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //获取账户划拨明细--xzl
    function getTransferBillApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'getTransferBill', $request)) {
                $datawhere = " 1 ";

                if (isset($request['upcode']) && $request['upcode'] == 'news') {
                    $newsTimes = strtotime(date("Y-m-d", time())) - 3600 * 24 * 3;
                    $datawhere .= " AND (transferlog_createtime>='{$newsTimes}' or transferlog_updatetime>='{$newsTimes}')";
                }

                if (isset($request['page']) && $request['page'] !== '0') {
                    $limitwhere = $request['page'] * 30000;
                } else {
                    $limitwhere = 0;
                }

                $sql = "select transferlog_id
                ,companies_id
                ,agencyId
                ,transferId
                ,transferClassNum
                ,sumTransferAmt
                ,transferDate
                ,tranSerial
                ,(case transferlog_status when -1 then '失败' when 0 then '待同步' when 1 then '同步中' when 2 then '已同步' else '未知错误' end) as transferlog_status
                ,FROM_UNIXTIME(transferlog_applytime, '%Y-%m-%d') as transferlog_applydate
                ,'' as spare_1,'' as spare_2
                from cmb_financial_transferlog
                where {$datawhere}
                order by transferlog_id
                limit {$limitwhere},30000";
                $dataList = $this->DataControl->selectClear($sql);

                $k = 0;
                $field = array();
                $field[$k]["fieldstring"] = "batchlog_id";
                $field[$k]["fieldname"] = "划拨明细序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "companies_id";
                $field[$k]["fieldname"] = "主体序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "agencyId";
                $field[$k]["fieldname"] = "招行编码";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "transferId";
                $field[$k]["fieldname"] = "批次号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "transferClassNum";
                $field[$k]["fieldname"] = "划拨课程总数";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "sumTransferAmt";
                $field[$k]["fieldname"] = "批次划拨总金额";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "transferDate";
                $field[$k]["fieldname"] = "划拨完成日期";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "tranSerial";
                $field[$k]["fieldname"] = "招行流水号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "transferlog_status";
                $field[$k]["fieldname"] = "划拨状态";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "transferlog_applydate";
                $field[$k]["fieldname"] = "申请划拨日期";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "spare_1";
                $field[$k]["fieldname"] = "备用字段1";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "spare_2";
                $field[$k]["fieldname"] = "备用字段2";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $result = array();
                $result['field'] = $field;
                $result['list'] = $dataList == false ? array() : $dataList;
                if ($dataList) {
                    $res = array('error' => '0', 'errortip' => "账户划拨明细信息获取成功", 'result' => $result);
                    ajax_return($res);
                } else {
                    ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => $result));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //获取批次划拨明细--xzl
    function getBatchlogApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'getBatchlog', $request)) {
                $datawhere = " 1 ";

                if (isset($request['upcode']) && $request['upcode'] == 'news') {
                    $newsTimes = strtotime(date("Y-m-d", time())) - 3600 * 24 * 3;
                    $datawhere .= " AND batchlog_createtime>='{$newsTimes}'";
                }

                if (isset($request['page']) && $request['page'] !== '0') {
                    $limitwhere = $request['page'] * 30000;
                } else {
                    $limitwhere = 0;
                }

                $sql = "select batchlog_id
                ,companies_id
                ,agencyId
                ,transferId
                ,transferNum
                ,sumTransferAmt
                ,applyDate
                ,classId
                ,class_id
                ,income_date
                ,subTransferId
                ,transferAmt
                ,eliminateClassHour
                ,(case batchlog_status when 0 then '失败' else '成功' end) as batchlog_status
                ,FROM_UNIXTIME(batchlog_applytime, '%Y-%m-%d') as batchlog_applydate
                ,'' as spare_1,'' as spare_2
                from cmb_financial_batchlog 
                where {$datawhere}
                order by batchlog_id
                limit {$limitwhere},30000";
                $dataList = $this->DataControl->selectClear($sql);

                $k = 0;
                $field = array();
                $field[$k]["fieldstring"] = "batchlog_id";
                $field[$k]["fieldname"] = "划拨明细序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "companies_id";
                $field[$k]["fieldname"] = "主体序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "agencyId";
                $field[$k]["fieldname"] = "招行编码";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "transferId";
                $field[$k]["fieldname"] = "批次号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "transferNum";
                $field[$k]["fieldname"] = "班级数量";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "sumTransferAmt";
                $field[$k]["fieldname"] = "批次总金额";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "applyDate";
                $field[$k]["fieldname"] = "申请日期";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "classId";
                $field[$k]["fieldname"] = "班级编号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "class_id";
                $field[$k]["fieldname"] = "班级序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "income_date";
                $field[$k]["fieldname"] = "收入日期";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "subTransferId";
                $field[$k]["fieldname"] = "子批次号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "transferAmt";
                $field[$k]["fieldname"] = "子批次金额";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "eliminateClassHour";
                $field[$k]["fieldname"] = "子批次课时数";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "batchlog_status";
                $field[$k]["fieldname"] = "完成状态";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "batchlog_applydate";
                $field[$k]["fieldname"] = "申请同步日期";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "spare_1";
                $field[$k]["fieldname"] = "备用字段1";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "spare_2";
                $field[$k]["fieldname"] = "备用字段2";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $result = array();
                $result['field'] = $field;
                $result['list'] = $dataList == false ? array() : $dataList;
                if ($dataList) {
                    $res = array('error' => '0', 'errortip' => "批次划拨明细信息获取成功", 'result' => $result);
                    ajax_return($res);
                } else {
                    ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => $result));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //获取账户流水明细--xzl
    function getAccountlogApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'getAccountlog', $request)) {
                $datawhere = " 1 ";

                if (isset($request['upcode']) && $request['upcode'] == 'news') {
                    $newsTimes = strtotime(date("Y-m-d", time())) - 3600 * 24 * 3;
                    $datawhere .= " AND (accountlog_createtime>='{$newsTimes}' or accountlog_updatetime>='{$newsTimes}')";
                }

                if (isset($request['page']) && $request['page'] !== '0') {
                    $limitwhere = $request['page'] * 30000;
                } else {
                    $limitwhere = 0;
                }

                $sql = "select accountlog
                ,companies_id
                ,agencyId
                ,regAccNo
                ,tranStatus
                ,transferId
                ,tranSerial
                ,transSequence
                ,tranAmt
                ,tag
                ,tranDir
                ,tranDate
                ,tranTime
                ,currency
                ,rcvPayAcc
                ,rcvPayName
                ,rcvPayEbk
                ,rcvPayEbb
                ,'' as spare_1
                ,'' as spare_2
                from cmb_financial_accountlog 
                where {$datawhere}
                order by accountlog
                limit {$limitwhere},30000";
                $dataList = $this->DataControl->selectClear($sql);

                $k = 0;
                $field = array();
                $field[$k]["fieldstring"] = "batchlog_id";
                $field[$k]["fieldname"] = "账户流水明细";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "companies_id";
                $field[$k]["fieldname"] = "主体序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "agencyId";
                $field[$k]["fieldname"] = "招行编码";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "regAccNo";
                $field[$k]["fieldname"] = "监管户户口号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "tranStatus";
                $field[$k]["fieldname"] = "交易状态";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "transferId";
                $field[$k]["fieldname"] = "批次号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "tranSerial";
                $field[$k]["fieldname"] = "交易流水";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "transSequence";
                $field[$k]["fieldname"] = "财务流水";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "tranAmt";
                $field[$k]["fieldname"] = "交易金额";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "tag";
                $field[$k]["fieldname"] = "清算摘要";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "tranDir";
                $field[$k]["fieldname"] = "借贷标志";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "tranDate";
                $field[$k]["fieldname"] = "清算日期";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "tranTime";
                $field[$k]["fieldname"] = "清算时间";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "currency";
                $field[$k]["fieldname"] = "币种";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "rcvPayAcc";
                $field[$k]["fieldname"] = "收付方帐号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "rcvPayName";
                $field[$k]["fieldname"] = "收付方名称";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "rcvPayEbk";
                $field[$k]["fieldname"] = "收付方开户行";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "rcvPayEbb";
                $field[$k]["fieldname"] = "收付方开户行联行号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "spare_1";
                $field[$k]["fieldname"] = "备用字段1";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "spare_2";
                $field[$k]["fieldname"] = "备用字段2";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $result = array();
                $result['field'] = $field;
                $result['list'] = $dataList == false ? array() : $dataList;
                if ($dataList) {
                    $res = array('error' => '0', 'errortip' => "账户流水明细信息获取成功", 'result' => $result);
                    ajax_return($res);
                } else {
                    ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => $result));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //获取实际欠费明细--xzl
    function getStudentUnReceApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'StudentUnRece', $request)) {

                $request['company_id'] = $pucArray['company_id'];
                $request['num'] = 30000;

                $ReportModel = new \Model\Report\Gmc\FinanceReportModel($request);
                $dataList = $ReportModel->studentUnReceReport($request);

                $k = 0;
                $field[$k]["fieldstring"] = "school_id";
                $field[$k]["fieldname"] = "学校id";
                $field[$k]["show"] = 0;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "province_name";
                $field[$k]["fieldname"] = "省份";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "school_branch";
                $field[$k]["fieldname"] = "校区编号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "school_cnname";
                $field[$k]["fieldname"] = "校区名称";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "student_id";
                $field[$k]["fieldname"] = "学员ID";
                $field[$k]["show"] = 0;
                $field[$k]["custom"] = 0;
                $k++;

                $field[$k]["fieldstring"] = "student_branch";
                $field[$k]["fieldname"] = "学员编号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "student_cnname";
                $field[$k]["fieldname"] = "学员中文名";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "student_enname";
                $field[$k]["fieldname"] = "学员英文名";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "channel_name";
                $field[$k]["fieldname"] = "专案名称";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "course_branch";
                $field[$k]["fieldname"] = "课程别";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "class_branch";
                $field[$k]["fieldname"] = "班级编号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "class_cnname";
                $field[$k]["fieldname"] = "班级名称";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "class_enname";
                $field[$k]["fieldname"] = "班级别名";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "ordercourse_totalprice";
                $field[$k]["fieldname"] = "购买金额";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "ordercourse_buynums";
                $field[$k]["fieldname"] = "购买课次";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "unpaid_money";
                $field[$k]["fieldname"] = "欠费金额";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "unpaid_times";
                $field[$k]["fieldname"] = "欠费课次";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "unpaid_day";
                $field[$k]["fieldname"] = "欠费日期";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "spare_1";
                $field[$k]["fieldname"] = "备用字段1";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "spare_2";
                $field[$k]["fieldname"] = "备用字段2";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $result = array();
                $result['field'] = $field;
                $result['list'] = $dataList == false ? array() : $dataList;
                if ($dataList) {
                    $res = array('error' => '0', 'errortip' => "账户流水明细信息获取成功", 'result' => $result);
                    ajax_return($res);
                } else {
                    ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => $result));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //获取班组支付明细--xzl
    function getCoursetypePayApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'getCoursetypePay', $request)) {
                $datawhere = " 1 and b.company_id = '{$pucArray['company_id']}' ";

                if (isset($request['upcode']) && $request['upcode'] == 'news') {
                    $newsTimes = strtotime(date("Y-m-d", time())) - 3600 * 24 * 3;
                    $datawhere .= " AND (a.pay_createtime>='{$newsTimes}' or a.pay_updatatime>='{$newsTimes}')";
                }

                if (isset($request['pay_id']) && $request['pay_id'] !== '0') {
                    $datawhere .= " AND a.pay_id='{$request['pay_id']}' ";
                } else {
                    $datawhere .= " and a.pay_id>392354 ";
                }

                if (isset($request['page']) && $request['page'] !== '0') {
                    $limitwhere = $request['page'] * 30000;
                } else {
                    $limitwhere = 0;
                }

                $sql = "select a.pay_id
                ,b.school_id
                ,b.student_id
                ,b.trading_pid
                ,b.order_pid
                ,b.order_status
                ,b.order_type
                ,b.coursetype_id
                ,a.pay_pid
                ,a.companies_id
                ,a.paytype_code
                ,a.pay_typename
                ,a.pay_type
                ,a.pay_price
                ,c.paytype_ischarge
                ,FROM_UNIXTIME(a.pay_successtime) as pay_time
                ,a.pay_createtime
                ,a.pay_updatatime
                from smc_payfee_order_pay a
                left join smc_payfee_order b on a.order_pid=b.order_pid
                left join smc_code_paytype c on a.paytype_code=c.paytype_code
                left join smc_school d on b.school_id=d.school_id
                where {$datawhere}
                and b.order_status>=0
                and d.school_istest=0
                and a.pay_issuccess=1
                and a.pay_type in (0,3)
                order by a.pay_id
                limit {$limitwhere},30000";
                $dataList = $this->DataControl->selectClear($sql);

                $k = 0;
                $field = array();
                $field[$k]["fieldstring"] = "pay_id";
                $field[$k]["fieldname"] = "支付序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "school_id";
                $field[$k]["fieldname"] = "学校序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "student_id";
                $field[$k]["fieldname"] = "学生序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "trading_pid";
                $field[$k]["fieldname"] = "交易编号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "order_pid";
                $field[$k]["fieldname"] = "订单编号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "order_status";
                $field[$k]["fieldname"] = "订单状态";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "order_type";
                $field[$k]["fieldname"] = "订单类型";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "pay_pid";
                $field[$k]["fieldname"] = "支付编号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "coursetype_id";
                $field[$k]["fieldname"] = "班组序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "companies_id";
                $field[$k]["fieldname"] = "主体序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "paytype_ischarge";
                $field[$k]["fieldname"] = "支付方式类型";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "paytype_code";
                $field[$k]["fieldname"] = "支付方式代码";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "pay_typename";
                $field[$k]["fieldname"] = "支付方式名称";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "pay_type";
                $field[$k]["fieldname"] = "收费类型";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "pay_price";
                $field[$k]["fieldname"] = "支付金额";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "pay_time";
                $field[$k]["fieldname"] = "支付时间";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "pay_createtime";
                $field[$k]["fieldname"] = "备用1";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "pay_updatatime";
                $field[$k]["fieldname"] = "备用2";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $result = array();
                $result['field'] = $field;
                $result['list'] = $dataList == false ? array() : $dataList;
                if ($dataList) {
                    $res = array('error' => '0', 'errortip' => "班组支付明细信息获取成功", 'result' => $result);
                    ajax_return($res);
                } else {
                    ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => $result));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //获取班组结转明细--xzl
    function getDealOrderApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'getDealOrder', $request)) {
                $datawhere = " 1 and a.company_id = '{$pucArray['company_id']}' ";

                if (isset($request['upcode']) && $request['upcode'] == 'news') {
                    $newsTimes = strtotime(date("Y-m-d", time())) - 3600 * 24 * 3;
                    $datawhere .= " AND (a.dealorder_createtime>='{$newsTimes}' or a.dealorder_updatatime>='{$newsTimes}')";
                }

                if (isset($request['dealorder_id']) && $request['dealorder_id'] !== '0') {
                    $datawhere .= " AND a.dealorder_id='{$request['dealorder_id']}' ";
                } else {
//                    $datawhere .= " and a.dealorder_id>118450 ";
                }

                if (isset($request['page']) && $request['page'] !== '0') {
                    $limitwhere = $request['page'] * 30000;
                } else {
                    $limitwhere = 0;
                }

                $sql = "select a.dealorder_id
                ,a.companies_id
                ,a.school_id
                ,a.student_id
                ,(select GROUP_CONCAT(distinct y.coursetype_id) from smc_forward_dealorder_course x,smc_course y where x.course_id=y.course_id and x.dealorder_pid=a.dealorder_pid) as coursetype_id
                ,a.dealorder_balanceprice
                ,a.dealorder_status
                ,FROM_UNIXTIME(a.dealorder_createtime) as dealorder_createtime
                ,a.dealorder_updatatime
                from smc_forward_dealorder a
                where {$datawhere}
                and a.dealorder_type=0
                order by a.dealorder_id
                limit {$limitwhere},30000";
                $dataList = $this->DataControl->selectClear($sql);

                $k = 0;
                $field = array();
                $field[$k]["fieldstring"] = "dealorder_id";
                $field[$k]["fieldname"] = "结转序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "companies_id";
                $field[$k]["fieldname"] = "主体序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "school_id";
                $field[$k]["fieldname"] = "学校序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "student_id";
                $field[$k]["fieldname"] = "学生序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "coursetype_id";
                $field[$k]["fieldname"] = "班组序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "dealorder_balanceprice";
                $field[$k]["fieldname"] = "结转金额";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "dealorder_status";
                $field[$k]["fieldname"] = "结转状态";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "dealorder_createtime";
                $field[$k]["fieldname"] = "结转日期";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "dealorder_updatatime";
                $field[$k]["fieldname"] = "备用1";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $result = array();
                $result['field'] = $field;
                $result['list'] = $dataList == false ? array() : $dataList;
                if ($dataList) {
                    $res = array('error' => '0', 'errortip' => "班组结转明细信息获取成功", 'result' => $result);
                    ajax_return($res);
                } else {
                    ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => $result));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //渠道变更明细--xzl
    function getChannellogApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'getChannellog', $request)) {
                $datawhere = " 1 and a.company_id = '{$pucArray['company_id']}' ";

                if (isset($request['upcode']) && $request['upcode'] == 'news') {
                    $newsTimes = strtotime(date("Y-m-d", time())) - 3600 * 24 * 3;
                    $datawhere .= " AND a.channellog_createtime>='{$newsTimes}' ";
                }

                if (isset($request['page']) && $request['page'] !== '0') {
                    $limitwhere = $request['page'] * 30000;
                } else {
                    $limitwhere = 0;
                }

                $sql = "select a.channellog_id
                ,a.client_id
                ,a.from_channel_id
                ,a.to_channel_id
                ,a.channellog_note
                ,a.channellog_status
                ,a.staffer_id
                ,a.astaffer_id
                ,a.channellog_createtime
                from crm_client_channellog a
                where {$datawhere}
                order by a.channellog_id
                limit {$limitwhere},30000";
                $dataList = $this->DataControl->selectClear($sql);

                $k = 0;
                $field = array();
                $field[$k]["fieldstring"] = "channellog_id";
                $field[$k]["fieldname"] = "变更序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "client_id";
                $field[$k]["fieldname"] = "客户序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "from_channel_id";
                $field[$k]["fieldname"] = "旧渠道序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "to_channel_id";
                $field[$k]["fieldname"] = "新渠道序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "channellog_note";
                $field[$k]["fieldname"] = "变更原因";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "channellog_status";
                $field[$k]["fieldname"] = "变更状态";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "staffer_id";
                $field[$k]["fieldname"] = "申请人";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "astaffer_id";
                $field[$k]["fieldname"] = "审核人";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "channellog_createtime";
                $field[$k]["fieldname"] = "操作时间";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $result = array();
                $result['field'] = $field;
                $result['list'] = $dataList == false ? array() : $dataList;
                if ($dataList) {
                    $res = array('error' => '0', 'errortip' => "渠道变更明细信息获取成功", 'result' => $result);
                    ajax_return($res);
                } else {
                    ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => $result));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //推荐券明细--xzl
    function getCouponApplyApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'getCouponApply', $request)) {
                $datawhere = " 1 and a.company_id = '{$pucArray['company_id']}' ";

                if (isset($request['upcode']) && $request['upcode'] == 'news') {
                    $newsTimes = strtotime(date("Y-m-d", time())) - 3600 * 24 * 3;
                    $datawhere .= " AND (a.apply_time>='{$newsTimes}' or a.apply_refusetime>='{$newsTimes}') ";
                }

                if (isset($request['page']) && $request['page'] !== '0') {
                    $limitwhere = $request['page'] * 30000;
                } else {
                    $limitwhere = 0;
                }

                $sql = "select a.apply_id
                ,c.student_id as rec_student_id 
                ,a.student_id
                ,a.apply_status
                ,a.staffer_id
                ,a.apply_time
                ,a.apply_refusetime
                ,b.coupons_id
                ,b.coupons_isuse
                ,a.apply_reson
                ,'' as spare_1
                ,'' as spare_2
                from smc_student_coupons_apply a
                left join smc_student_coupons b on a.apply_id=b.apply_id
                left join shop_student_coupons_apply_recommend c on a.apply_id=c.apply_id
                where {$datawhere}
                and a.applytype_branch='wsctuijian'
                limit {$limitwhere},30000";
                $dataList = $this->DataControl->selectClear($sql);

                $k = 0;
                $field = array();
                $field[$k]["fieldstring"] = "apply_id";
                $field[$k]["fieldname"] = "申请序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "rec_student_id";
                $field[$k]["fieldname"] = "被推荐学生序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "student_id";
                $field[$k]["fieldname"] = "学生序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "apply_status";
                $field[$k]["fieldname"] = "申请状态";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "staffer_id";
                $field[$k]["fieldname"] = "负责人";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "apply_time";
                $field[$k]["fieldname"] = "申请时间";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "apply_refusetime";
                $field[$k]["fieldname"] = "审核时间";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "coupons_id";
                $field[$k]["fieldname"] = "发券序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "coupons_isuse";
                $field[$k]["fieldname"] = "使用状态";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "apply_reson";
                $field[$k]["fieldname"] = "申请原因";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "spare_1";
                $field[$k]["fieldname"] = "备用字段1";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "spare_2";
                $field[$k]["fieldname"] = "备用字段2";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $result = array();
                $result['field'] = $field;
                $result['list'] = $dataList == false ? array() : $dataList;
                if ($dataList) {
                    $res = array('error' => '0', 'errortip' => "推荐券明细信息获取成功", 'result' => $result);
                    ajax_return($res);
                } else {
                    ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => $result));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //开班申请明细--xzl
    function getClassOpenApplyApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'getClassOpenApply', $request)) {
                $datawhere = " 1 and a.company_id = '{$pucArray['company_id']}' ";

                if (isset($request['upcode']) && $request['upcode'] == 'news') {
                    $newsTimes = strtotime(date("Y-m-d", time())) - 3600 * 24 * 3;
                    $datawhere .= " AND (a.openapply_createtime>='{$newsTimes}' or a.openapply_updatatime>='{$newsTimes}') ";
                }

                if (isset($request['page']) && $request['page'] !== '0') {
                    $limitwhere = $request['page'] * 30000;
                } else {
                    $limitwhere = 0;
                }

                $sql = "select a.openapply_id
                ,a.school_id
                ,a.class_id
                ,a.staffer_id
                ,a.openapply_studynum
                ,a.openapply_minclassnum
                ,a.openapply_status
                ,a.openapply_note
                ,a.openapply_createtime
                ,a.exam_staffer_id
                ,a.exam_openapply_note
                ,a.openapply_updatatime
                from smc_class_openapply a
                where {$datawhere}
                limit {$limitwhere},30000";
                $dataList = $this->DataControl->selectClear($sql);

                $k = 0;
                $field = array();
                $field[$k]["fieldstring"] = "openapply_id";
                $field[$k]["fieldname"] = "申请序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "school_id";
                $field[$k]["fieldname"] = "学校序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "class_id";
                $field[$k]["fieldname"] = "班级序号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "staffer_id";
                $field[$k]["fieldname"] = "申请人";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "openapply_studynum";
                $field[$k]["fieldname"] = "开班申请人数";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "openapply_minclassnum";
                $field[$k]["fieldname"] = "最小人数标准";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "openapply_status";
                $field[$k]["fieldname"] = "审核状态";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "openapply_note";
                $field[$k]["fieldname"] = "申请备注";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "openapply_createtime";
                $field[$k]["fieldname"] = "申请时间";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "exam_staffer_id";
                $field[$k]["fieldname"] = "审核人";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "exam_openapply_note";
                $field[$k]["fieldname"] = "审核备注";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $field[$k]["fieldstring"] = "openapply_updatatime";
                $field[$k]["fieldname"] = "更新时间";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;

                $result = array();
                $result['field'] = $field;
                $result['list'] = $dataList == false ? array() : $dataList;
                if ($dataList) {
                    $res = array('error' => '0', 'errortip' => "推荐券明细信息获取成功", 'result' => $result);
                    ajax_return($res);
                } else {
                    ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => $result));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //开班申请明细--xzl
    function getSchoolStudyApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'getSchoolStudy', $request)) {
                $datawhere = " 1 and a.company_id = '{$pucArray['company_id']}' ";

                if (isset($request['upcode']) && $request['upcode'] == 'news') {
//                    $newsTimes = strtotime(date("Y-m-d", time())) - 3600 * 24 * 3;
//                    $datawhere .= " AND (a.openapply_createtime>='{$newsTimes}' or a.openapply_updatatime>='{$newsTimes}') ";
                }

                if (isset($request['page']) && $request['page'] !== '0') {
                    $limitwhere = $request['page'] * 20000;
                } else {
                    $limitwhere = 0;
                }

                $sql = "select a.studyinfo_id
                ,a.studyinfo_date
                ,a.school_id
                ,a.student_num
                from temp_smc_school_studyinfo a
                where {$datawhere}
                and a.studyinfo_date>='2023-01-01'
                and a.studyinfo_type=2
                and a.coursetype_id=65
                order by a.studyinfo_id desc
                limit {$limitwhere},30000";
                $dataList = $this->DataControl->selectClear($sql);

                $k = 0;
                $field = array();
                $field[$k]["fieldstring"] = "studyinfo_id";
                $field[$k]["fieldname"] = "主键序号";
                $k++;

                $field[$k]["fieldstring"] = "studyinfo_date";
                $field[$k]["fieldname"] = "在读日期";
                $k++;

                $field[$k]["fieldstring"] = "school_id";
                $field[$k]["fieldname"] = "学校序号";
                $k++;

                $field[$k]["fieldstring"] = "student_num";
                $field[$k]["fieldname"] = "在读人数";
                $k++;

                $result = array();
                $result['field'] = $field;
                $result['list'] = $dataList == false ? array() : $dataList;
                if ($dataList) {
                    $res = array('error' => '0', 'errortip' => "美语在读人数明细信息获取成功", 'result' => $result);
                    ajax_return($res);
                } else {
                    ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => $result));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //学生打卡明细--xzl
    function getStudentCardlogApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'getStudentCardlog', $request)) {
                $datawhere = " 1 and a.company_id = '{$pucArray['company_id']}' ";

                if (isset($request['upcode']) && $request['upcode'] == 'news') {
                    $newsTimes = strtotime(date("Y-m-d", time())) - 3600 * 24 * 3;
                    $datawhere .= " AND (a.cardlog_creattime>='{$newsTimes}' or a.cardlog_updatetime>='{$newsTimes}') ";
                }

                if (isset($request['page']) && $request['page'] !== '0') {
                    $limitwhere = $request['page'] * 30000;
                } else {
                    $limitwhere = 0;
                }

                $sql = "select a.cardlog_id
                ,a.school_id
                ,a.student_id
                ,a.card_id
                ,a.cardlog_type
                ,a.cardlog_faceimg
                ,a.cardlog_state
                ,FROM_UNIXTIME(a.cardlog_clocktime) as clocktime
                ,a.cardlog_creattime
                from gmc_machine_stucardlog a
                where {$datawhere}
                order by a.cardlog_id asc
                limit {$limitwhere},30000";
                $dataList = $this->DataControl->selectClear($sql);

                $k = 0;
                $field = array();
                $field[$k]["fieldstring"] = "cardlog_id";
                $field[$k]["fieldname"] = "打卡序号";
                $k++;

                $field[$k]["fieldstring"] = "school_id";
                $field[$k]["fieldname"] = "学校序号";
                $k++;

                $field[$k]["fieldstring"] = "student_id";
                $field[$k]["fieldname"] = "学生序号";
                $k++;

                $field[$k]["fieldstring"] = "card_id";
                $field[$k]["fieldname"] = "卡片序号";
                $k++;

                $field[$k]["fieldstring"] = "cardlog_type";
                $field[$k]["fieldname"] = "打卡方式";
                $k++;

                $field[$k]["fieldstring"] = "cardlog_state";
                $field[$k]["fieldname"] = "打卡状态";
                $k++;

                $field[$k]["fieldstring"] = "clocktime";
                $field[$k]["fieldname"] = "打卡时间";
                $k++;

                $field[$k]["fieldstring"] = "cardlog_faceimg";
                $field[$k]["fieldname"] = "图片链接";
                $k++;

                $field[$k]["fieldstring"] = "cardlog_creattime";
                $field[$k]["fieldname"] = "创建时间";
                $k++;

                $result = array();
                $result['field'] = $field;
                $result['list'] = $dataList == false ? array() : $dataList;
                if ($dataList) {
                    $res = array('error' => '0', 'errortip' => "打卡考勤明细信息获取成功", 'result' => $result);
                    ajax_return($res);
                } else {
                    ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => $result));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //学生人脸明细--xzl
    function getStudentPortraitApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'getStudentCardlog', $request)) {
                $datawhere = " 1 and a.company_id = '{$pucArray['company_id']}' ";

                if (isset($request['upcode']) && $request['upcode'] == 'news') {
                    $newsTimes = strtotime(date("Y-m-d", time())) - 3600 * 24 * 3;
                    $datawhere .= " AND (a.stuportrait_creattime>='{$newsTimes}' or a.stuportrait_updatetime>='{$newsTimes}') ";
                }

                if (isset($request['page']) && $request['page'] !== '0') {
                    $limitwhere = $request['page'] * 30000;
                } else {
                    $limitwhere = 0;
                }

                $sql = "select a.stuportrait_id
                ,a.school_id
                ,a.student_id
                ,a.main_staffer_id
                ,a.stuportrait_faceimg
                ,a.stuportrait_creattime
                ,a.stuportrait_updatetime
                from gmc_machine_stuportrait a
                where {$datawhere}
                order by a.stuportrait_id asc
                limit {$limitwhere},30000";
                $dataList = $this->DataControl->selectClear($sql);

                $k = 0;
                $field = array();
                $field[$k]["fieldstring"] = "stuportrait_id";
                $field[$k]["fieldname"] = "人脸序号";
                $k++;

                $field[$k]["fieldstring"] = "school_id";
                $field[$k]["fieldname"] = "学校序号";
                $k++;

                $field[$k]["fieldstring"] = "student_id";
                $field[$k]["fieldname"] = "学生序号";
                $k++;

                $field[$k]["fieldstring"] = "main_staffer_id";
                $field[$k]["fieldname"] = "教师序号";
                $k++;

                $field[$k]["fieldstring"] = "stuportrait_faceimg";
                $field[$k]["fieldname"] = "图片链接";
                $k++;

                $field[$k]["fieldstring"] = "stuportrait_creattime";
                $field[$k]["fieldname"] = "创建时间";
                $k++;

                $field[$k]["fieldstring"] = "stuportrait_updatetime";
                $field[$k]["fieldname"] = "更新时间";
                $k++;

                $result = array();
                $result['field'] = $field;
                $result['list'] = $dataList == false ? array() : $dataList;
                if ($dataList) {
                    $res = array('error' => '0', 'errortip' => "人脸录入明细信息获取成功", 'result' => $result);
                    ajax_return($res);
                } else {
                    ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => $result));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //学生考勤明细--xzl
    function getStudentDeductApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'getStudentCardlog', $request)) {
                $datawhere = " 1 and d.company_id = '{$pucArray['company_id']}' ";

                if (isset($request['upcode']) && $request['upcode'] == 'news') {
                    $newsTimes = strtotime(date("Y-m-d", time())) - 3600 * 24 * 2;
                    $datawhere .= " AND e.clockinginlog_createtime>='{$newsTimes}' ";
                }

                if (isset($request['class_branch']) && $request['class_branch'] !== '') {
                    $datawhere .= " AND c.class_branch='{$request['class_branch']}' ";
                }

                $startId = 12000000;
                if (isset($request['page']) && $request['page'] !== '0') {
                    $limitwhere = $request['page'] * 30000;
//                    $startId += $request['page'] * 30000;
                } else {
                    $limitwhere = 0;
                }

                $sql = "select a.hourstudy_id
                ,d.school_id
                ,c.class_id
                ,b.hour_id
                ,a.student_id
                ,b.hour_day
                ,b.hour_lessontimes
                ,b.hour_name
                ,b.hour_isfree
                ,a.hourstudy_checkin
                ,e.stuchecktype_code
                ,e.clockinginlog_price
                ,FROM_UNIXTIME(e.clockinginlog_createtime) as deduct_time
                from smc_student_hourstudy a
                left join smc_class_hour b on a.hour_id=b.hour_id
                left join smc_class c on c.class_id=a.class_id
                left join smc_school d on d.school_id=c.school_id
                left join smc_student_clockinginlog e on a.student_id=e.student_id and a.hourstudy_id=e.hourstudy_id
                where {$datawhere}
                and a.hourstudy_id>{$startId}
                and b.hour_day>='2023-12-01'
                and d.school_istest=0
                order by a.hourstudy_id asc
                limit {$limitwhere},30000";
                $dataList = $this->DataControl->selectClear($sql);

                $k = 0;
                $field = array();
                $field[$k]["fieldstring"] = "hourstudy_id";
                $field[$k]["fieldname"] = "考勤序号";
                $k++;

                $field[$k]["fieldstring"] = "school_id";
                $field[$k]["fieldname"] = "学校序号";
                $k++;

                $field[$k]["fieldstring"] = "class_id";
                $field[$k]["fieldname"] = "班级序号";
                $k++;

                $field[$k]["fieldstring"] = "hour_id";
                $field[$k]["fieldname"] = "课时序号";
                $k++;

                $field[$k]["fieldstring"] = "student_id";
                $field[$k]["fieldname"] = "学生序号";
                $k++;

                $field[$k]["fieldstring"] = "hour_day";
                $field[$k]["fieldname"] = "排课日期";
                $k++;

                $field[$k]["fieldstring"] = "hour_lessontimes";
                $field[$k]["fieldname"] = "排课序号";
                $k++;

                $field[$k]["fieldstring"] = "hourstudy_name";
                $field[$k]["fieldname"] = "课时名称";
                $k++;

                $field[$k]["fieldstring"] = "hour_isfree";
                $field[$k]["fieldname"] = "是否免费";
                $k++;

                $field[$k]["fieldstring"] = "hourstudy_checkin";
                $field[$k]["fieldname"] = "是否出勤";
                $k++;

                $field[$k]["fieldstring"] = "stuchecktype_code";
                $field[$k]["fieldname"] = "考勤类别";
                $k++;

                $field[$k]["fieldstring"] = "clockinginlog_price";
                $field[$k]["fieldname"] = "应收费用";
                $k++;

                $field[$k]["fieldstring"] = "deduct_time";
                $field[$k]["fieldname"] = "操作日期";
                $k++;

                $result = array();
                $result['field'] = $field;
                $result['list'] = $dataList == false ? array() : $dataList;
                if ($dataList) {
                    $res = array('error' => '0', 'errortip' => "学生考勤明细信息获取成功", 'result' => $result);
                    ajax_return($res);
                } else {
                    ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => $result));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }


}