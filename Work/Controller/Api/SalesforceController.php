<?php


namespace Work\Controller\Api;


class SalesforceController extends viewTpl
{
    public $u;
    public $t;
    public $c;
    public $visitType = "api";


    //预加载处理类
    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
        $this->api = $this->router->getApi();

    }

    //名单孕育更新
    function breedActivateView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $breed_id = $this->from62to10($request['breed_id']);
        $breedOne = $this->DataControl->getFieldOne("crm_client_breed", "breed_id,company_id,channel_id,client_cnname,client_mobile,breed_level,breed_class"
            , "breed_id = '{$breed_id}'", " ORDER BY breed_id DESC limit 0,1");
        if ($breedOne) {
            $breeddata = array();
            if (isset($request['readtimes']) && $request['readtimes'] == '1') {
                if ($request['breed_level'] < '1') {
                    $breeddata['breed_level'] = 1;
                }
                $breeddata['breed_readtimes'] = time();
            }
            if (isset($request['buytimes']) && $request['buytimes'] == '1') {
                if ($request['breed_level'] < '3') {
                    $breeddata['breed_level'] = 3;
                }
                $breeddata['breed_buytimes'] = time();
            }
            if (isset($request['leavetimes']) && $request['leavetimes'] == '1') {
                if ($request['breed_level'] < '2') {
                    $breeddata['breed_level'] = 2;
                }
                $breeddata['breed_leavetimes'] = time();
            }
            $breeddata['breed_updatetime'] = time();
            if ($this->DataControl->updateData('crm_client_breed', "breed_id = '{$breed_id}'", $breeddata)) {
                if ($breedOne['breed_class'] == '0') {
                    $parameter = array();
                    $parameter['client_source'] = '其他';
                    $parameter['channel_name'] = 'TMK短信';
                    $parameter['client_remark'] = '原开心豆学员名单，经过TMK短信行销培育，已打开落地页，请勿告诉家长知道其开心豆学员信息，等待跟踪！';
                    $parameter['client_patriarchname'] = $breedOne['client_cnname'];
                    $parameter['client_cnname'] = '匿名';
                    $parameter['client_mobile'] = $breedOne['client_mobile'];
                    $parameter['client_age'] = '7';
                    $parameter['client_birthday'] = "2015-01-01";
                    $parameter['company_id'] = $breedOne['company_id'];
                    $parameter['client_address'] = "原开心豆校点：" . $breedOne['client_cnname'];
                    request_by_curl("https://crmapi.kedingdang.com/PhoneActivity/addPhoneChannelAction", dataEncode($parameter), "POST", array());
                    ajax_return(array('error' => 0, 'errortip' => "调用成功！", "bakfuntion" => "okmotify"));
                } else {
                    $clientOne = $this->DataControl->getOne("crm_client", "client_mobile='{$breedOne['client_mobile']}'");
                    if ($clientOne) {
                        $channelOne = $this->DataControl->getFieldOne("crm_code_channel", "channel_id,channel_medianame", "channel_name='TMK短信' AND company_id = '{$clientOne['company_id']}'");
                        $data = array();
                        $data['channel_id'] = $channelOne['channel_id'];
                        $data['client_source'] = $channelOne['channel_medianame'];
                        $data['client_remark'] = $clientOne['client_remark'] . '，原待分配名单，经过短信行销培育，已打开落地页，请TMK立即跟踪哦！';
                        $data['client_isfromgmc'] = '1';
                        $data['outthree_apiid'] = '1';
                        $data['client_updatetime'] = time();
                        $data['client_createtime'] = time();
                        if ($this->DataControl->updateData("crm_client", "client_id = '{$clientOne['client_id']}'", $data)) {
                            //渠道变更记录
                            $channello = array();
                            $channello['company_id'] = $clientOne['company_id'];
                            $channello['client_id'] = $clientOne['client_id'];
                            $channello['from_channel_id'] = $clientOne['channel_id'];
                            $channello['to_channel_id'] = $channelOne['channel_id'];
                            $channello['channellog_note'] = "TMK名单经过短信孕育，激活意向星级，更改短信意向！";
                            $channello['channellog_createtime'] = time();
                            $this->DataControl->insertData("crm_client_channellog", $channello);
                        }
                        ajax_return(array('error' => 0, 'errortip' => "调用成功！", "bakfuntion" => "okmotify"));
                    } else {
                        ajax_return(array('error' => 0, 'errortip' => "名单信息不存在！", "bakfuntion" => "okmotify"));
                    }
                }
            } else {
                ajax_return(array('error' => 1, 'errortip' => "调用失败！", "bakfuntion" => "errormotify"));
            }
        } else {
            ajax_return(array('error' => 1, 'errortip' => "名单查询不到！", "bakfuntion" => "errormotify"));
        }
    }

    function from62to10($str)
    {
        $dict = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $len = strlen($str);
        $dec = 0;
        for ($i = 0; $i < $len; $i++) {
            //找到对应字典的下标
            $pos = strpos($dict, $str[$i]);
            $dec += $pos * pow(62, $len - $i - 1);
        }
        return $dec;
    }

    function from10to62($dec)
    {
        $dict = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $result = '';
        do {
            $result = $dict[$dec % 62] . $result;
            $dec = intval($dec / 62);
        } while ($dec != 0);
        return $result;
    }

    function crmToSmsView()
    {
        if (date("H") >= 8 && date("H") <= 21) {
            $breedOne = $this->DataControl->selectOne("SELECT b.* FROM crm_client_breed AS b WHERE b.breed_status = '0' ORDER BY RAND() LIMIT 0,1");
            if (!$breedOne) {
                ajax_return(array('error' => 1, 'errortip' => "名单不存在!", "bakfuntion" => "errormotify"));
            }
            if ($breedOne['breed_status'] !== '0') {
                ajax_return(array('error' => 1, 'errortip' => "短信已发送!", "bakfuntion" => "errormotify"));
            }
            $bd = $this->from10to62($breedOne['breed_id']);
            $misNote = "亲爱的家长您好，吉的堡{成长中心}以全新的课程体系与大家见面啦！快带上小朋友来线下参观体验吧！{https://pay.kidcastle.com.cn/Gener/pageThree?d=".$bd."}&回T退订";
            $array = array("{成长中心}", "https://pay.kidcastle.com.cn/Gener/pageThree?d={$bd} ");
            if ($this->Sendmisgo($breedOne['client_mobile'], $misNote, "营销通知", $bd, $array, '1084664')) {
                $breeddata = array();
                $breeddata['breed_status'] = '1';
                $this->DataControl->updateData('crm_client_breed', "breed_id = '{$breedOne['breed_id']}'", $breeddata);
                ajax_return(array('error' => 0, 'errortip' => "{$breedOne['client_mobile']}短信发送成功!", "bakfuntion" => "okmotify"));
            } else {
                $breeddata = array();
                $breeddata['breed_status'] = '-1';
                $this->DataControl->updateData('crm_client_breed', "breed_id = '{$breedOne['breed_id']}'", $breeddata);
                ajax_return(array('error' => 1, 'errortip' => "短信激活失败!", "bakfuntion" => "errormotify"));
            }
        } else {
            ajax_return(array('error' => 1, 'errortip' => "时间不在8点到21点之间!", "bakfuntion" => "errormotify"));
        }
    }


    function crmToTestView()
    {
        $misNote = "亲爱的家长您好，吉的堡{成长中心}以全新的课程体系与大家见面啦！快带上小朋友来线下参观体验吧！{https://pay.kidcastle.com.cn/Gener/pageThree?d=lpt}&回T退订";
        $array = array("{成长中心}", "https://pay.kidcastle.com.cn/Gener/pageThree?d=lpt");
        if ($this->Sendmisgo('***********', $misNote, "营销通知", rand(111111, 999999), $array, '1084664')) {
            ajax_return(array('error' => 0, 'errortip' => "短信发送成功!", "bakfuntion" => "okmotify"));
        } else {
            ajax_return(array('error' => 1, 'errortip' => "发送失败!", "bakfuntion" => "errormotify"));
        }
    }

    function Sendmisgo($mobile, $mistxt, $tilte, $sendcode, $array = array(), $tempId = '0')
    {
        $Model = new \Model\Api\SmsqmModel();
        $Model->setAccount();
        $Model->setAppId();

        $result = $Model->sendTemplateSMS($mobile, $array, $tempId);
        if ($result == NULL) {
            $date = array();
            $date['company_id'] = "8888";
            $date['mislog_tilte'] = $tilte;
            $date['mislog_mobile'] = $mobile;
            $date['mislog_sendcode'] = $sendcode;
            $date['mislog_mistxt'] = $mistxt;
            $date['mislog_sendinc'] = "0";
            $date['mislog_errortip'] = "result error!";
            $date['mislog_time'] = time();
            $this->DataControl->insertData('crm_mislog', $date);
            return false;
        }
        if ($result->statusCode != 0) {
            $date = array();
            $date['company_id'] = "8888";
            $date['mislog_tilte'] = $tilte;
            $date['mislog_mobile'] = $mobile;
            $date['mislog_sendcode'] = $sendcode;
            $date['mislog_mistxt'] = $mistxt;
            $date['mislog_sendinc'] = "0";
            $date['mislog_errortip'] = "error code :" . $result->statusCode . "error msg :" . $result->statusMsg;
            $date['mislog_time'] = time();
            $this->DataControl->insertData('crm_mislog', $date);
            return false;
        } else {
            $date = array();
            $date['company_id'] = "8888";
            $date['mislog_tilte'] = $tilte;
            $date['mislog_mobile'] = $mobile;
            $date['mislog_sendcode'] = $sendcode;
            $date['mislog_mistxt'] = $mistxt;
            $date['mislog_sendinc'] = "1";
            $date['mislog_time'] = time();
            $this->DataControl->insertData('crm_mislog', $date);
            return true;
        }
    }

    /*function Sendmisgobak($mobile, $mistxt, $tilte, $sendcode)
    {
        if ($this->xxSendmis($mobile, $mistxt)) {
            $date['company_id'] = "8888";
            $date['mislog_tilte'] = $tilte;
            $date['mislog_mobile'] = $mobile;
            $date['mislog_sendcode'] = $sendcode;
            $date['mislog_mistxt'] = $mistxt;
            $date['mislog_sendinc'] = "1";
            $date['mislog_time'] = time();
            $this->DataControl->insertData('crm_mislog', $date);
            return true;
        } else {
            $date['company_id'] = "8888";
            $date['mislog_tilte'] = $tilte;
            $date['mislog_mobile'] = $mobile;
            $date['mislog_sendcode'] = $sendcode;
            $date['mislog_mistxt'] = $mistxt;
            $date['mislog_sendinc'] = "0";
            $date['mislog_time'] = time();
            $this->DataControl->insertData('crm_mislog', $date);
            return false;
        }
    }*/


    /*function xxSendmis($to, $datas, $tempId)
    {
        $Model = new \Model\Api\SmsqmModel();
        $Model->setAccount();
        $Model->setAppId();

        $result = $Model->sendTemplateSMS($to, $datas, $tempId);
        if ($result == NULL) {
            $this->error = true;
            $this->errortip = "result error!";
            return false;
        }
        if ($result->statusCode != 0) {
            $this->error = true;
            $this->errortip = "error code :" . $result->statusCode . "error msg :" . $result->statusMsg;
            return false;
        } else {
            return true;
        }
    }*/

    function xxSendmisbak($mobile, $content)
    {
        $frist = substr($mobile, 0, 1);
        if ($frist == '1') {
            $sendbak = request_by_curl("http://api.feige.ee/smsservice.aspx", "name=kidforce&pwd=37aca44219848c400c3ff9a6b&mobile={$mobile}&content={$content}&stime=&sign=吉的堡&type=pt&extno=", "POST", array());
            $sendarray = explode(",", $sendbak);
            if ($sendarray['0'] == '0') {
                return true;
            } else {
                return false;
            }
        } else {
            return false;
        }
    }

    //更新CRM新空军例子进入SalesFores
    function crmToSalesForesView()
    {
        exit;
    }

    //手动接口  -- 更新CRM新空军例子进入SalesFores -- 通过手机号码进行同步
    function crmToSalesForesMobileView()
    {
        exit;
    }

    //推送学校信息进入salesforce
    function pushSchoolInfoView()
    {
        exit;
    }

    //推送课程信息接口进入salesforce
    function pushCompanyCourseView()
    {
        exit;
    }

//    saleforce 那边每次只是接受一条数据  推送转正的客户
    function pushPositiveClientView()
    {
        exit;
    }

    /**
     * 推送作废学员
     * 操作类
     */
    function pushInvalidCLientView()
    {
        exit;
    }

    /**
     * 确认取消柜询的记录
     * author: ling
     * 对应接口文档 0001
     * 操作类
     */
    function cancelInviteIdView()
    {
        exit;
    }

    /**
     * 确认取消试听记录
     * author: ling
     * 对应接口文档 0001
     */
    function cancelAuditionIdView()
    {
        exit;
    }


    // 确认柜询的状态
    function changeInviteIdView()
    {
        exit;
    }

    /**
     * 确认试听邀约状态
     * author: ling
     * 对应接口文档 0001
     */
    function changeAudVisitView()
    {
        exit;
    }


    /**
     *  发送短信提醒
     * author: ling
     * 对应接口文档 0001
     */
    function sendClientAllotRemindView()
    {
        return true;
        $fixedTime = date("Y-m-d H:i:s");
        $today = date("Y-m-d");
        $todayStime = strtotime($today);
        $todayEtime = $todayStime+86399;
        if ($fixedTime < $today . ' 18:00:00') {
            ajax_return(array('error' => 1, 'errortip' => "请在18点以后发送提醒", "bakfuntion" => "okmotify"));
        }


        $dataList = $this->DataControl->selectClear("
            select f.staffer_id,l.school_id,l.school_cnname,l.school_shortname,f.staffer_mobile,
            (select count(t.client_id) from crm_client as t,crm_client_schoolenter as s where t.client_id=s.client_id and s.school_id =l.school_id and t.client_tracestatus = 0 and t.client_distributionstatus = 0 ) as client_num
            from gmc_staffer_postbe as p
            left join gmc_company_post as pt ON pt.post_id =p.post_id
            left join smc_staffer as f ON f.staffer_id = p.staffer_id
            left join smc_school as l ON l.school_id = p.school_id
            where p.postbe_status = 1 and pt.post_istopjob =1 and p.school_id > 0
            and l.school_istest = 0 and p.company_id = '8888'  and l.school_isclose =0 
            and p.postbe_iscrmuser = 1
            and pt.post_name like '%校长%'
            and f.staffer_mobile > 0 AND f.staffer_leave <> '1'
            and  f.staffer_mobile not in (select g.mislog_mobile from crm_mislog as g where g.mislog_tilte ='CRM分配提醒' and g.company_id = '8888' and g.mislog_time >= '{$todayStime}' and g.mislog_time <= '{$todayEtime}')
            group by p.staffer_id,p.school_id
            HAVING client_num > 0
            order by l.school_id DESC");

        $num = 0;
        if ($dataList) {
            foreach ($dataList as $val) {
                if ($this->DataControl->selectOne("select mislog_id from crm_mislog where mislog_mobile ='{$val['staffer_mobile']}' and mislog_tilte ='CRM分配提醒' and company_id = '8888' and mislog_time >= '{$todayStime}' and mislog_time <= '{$todayEtime}' ")) {
                    continue;
                } else {
                    $num++;
                    $mistext = "您好，{$val['school_cnname']}有{$val['client_num']}条招生有效名单待分配，请您及时分配到招生老师！";
                    //$this->Sendmisgo($val['staffer_mobile'], $mistext, "CRM分配提醒", "");

                    $this->Sendmisgo1($val['staffer_mobile'], $mistext, "CRM分配提醒", "", array($val['school_cnname'], $val['client_num']), '811109');
                }
            }
            ajax_return(array('error' => 0, 'errortip' => "发送成功,共发送{$num}条!", "bakfuntion" => "okmotify"));
        } else {
            ajax_return(array('error' => 1, 'errortip' => "暂时无需发送!", "bakfuntion" => "okmotify"));
        }
    }
}