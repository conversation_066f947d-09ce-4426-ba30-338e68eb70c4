<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2021/3/6
 * Time: 17:31
 */

namespace Work\Controller\Api;


class HrapiController extends viewTpl
{
    public $u;
    public $t;
    public $c;
    public $visitType = "api";

    //预加载处理类
    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
        $this->api = $this->router->getApi();
    }

    /**
     *https://api.kedingdang.com/Hrapi/guanbiUrl
     **/
    function guanbiUrlView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $timestamp = time();
        $userInfo = "{\"domainId\":\"guanbi\",\"externalUserId\":\"xzzy\",\"timestamp\":{$timestamp}}";
        $RsaTo = new \Rsaencdec("Core/Tools/threeca/guanyuan/sso_public_key.pem", "Core/Tools/threeca/guanyuan/sso_private_key.pem");
        $sing_byte = bin2hex($RsaTo->encryptPri($userInfo, "base64"));

        if (isset($request['vistType']) && $request['vistType'] == 'app') {
            $InOurtUrl = "https://jidebao.guandatacloud.com/m/app/hc7f101a1b086454393ef835?provider=guanbi&ssoToken={$sing_byte}";
        } else {
            $InOurtUrl = "https://jidebao.guandatacloud.com/home/<USER>/mdc4f42cb1886426cb3172e4?provider=guanbi&ssoToken={$sing_byte}";
        }

        header("Location:{$InOurtUrl}");
        exit;
        $stafferOne = $this->DataControl->selectOne("SELECT s.company_id,s.staffer_id FROM smc_staffer AS s 
        WHERE ((s.staffer_mobile = '{$request['mobile']}' AND s.staffer_mobile <> '' ) OR (s.staffer_employeepid = '{$request['employeepid']}' AND s.staffer_employeepid <> '')) 
        AND s.company_id = '8888' 
        LIMIT 0, 1");
        if ($stafferOne) {
            $data = array();
            $data['company_id'] = $stafferOne['company_id'];
            $data['staffer_id'] = $stafferOne['staffer_id'];
            $data['gyloginlog_createtime'] = time();
            $this->DataControl->insertData("gmc_staffer_gyloginlog", $data);


        } else {
            $res = array('error' => 1, 'errortip' => "未查询到您的人事信息！");
            ajax_return($res);
        }
    }

    /**
     *https://api.kedingdang.com/Hrapi/guanbiscUrl
     **/
    function guanbiscUrlView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $timestamp = time();
        $userInfo = "{\"domainId\":\"guanbi\",\"externalUserId\":\"sczy\",\"timestamp\":{$timestamp}}";
        $RsaTo = new \Rsaencdec("Core/Tools/threeca/guanyuan/sso_public_key.pem", "Core/Tools/threeca/guanyuan/sso_private_key.pem");
        $sing_byte = bin2hex($RsaTo->encryptPri($userInfo, "base64"));

        if (isset($request['vistType']) && $request['vistType'] == 'app') {
            $InOurtUrl = "https://jidebao.guandatacloud.com/m/portal?provider=guanbi&ssoToken={$sing_byte}";
        } else {
            $InOurtUrl = "https://jidebao.guandatacloud.com/home?provider=guanbi&ssoToken={$sing_byte}";
        }

        header("Location:{$InOurtUrl}");
        exit;
        $stafferOne = $this->DataControl->selectOne("SELECT s.company_id,s.staffer_id FROM kmc_staffer AS s 
        WHERE ((s.staffer_mobile = '{$request['mobile']}' AND s.staffer_mobile <> '' ) OR (s.staffer_employeepid = '{$request['employeepid']}' AND s.staffer_employeepid <> '')) 
        AND s.company_id = '8888' 
        LIMIT 0, 1");
        if ($stafferOne) {
            $data = array();
            $data['company_id'] = $stafferOne['company_id'];
            $data['staffer_id'] = $stafferOne['staffer_id'];
            $data['gyloginlog_createtime'] = time();
            $this->DataControl->insertData("gmc_staffer_gyloginlog", $data);
        } else {
            $res = array('error' => 1, 'errortip' => "未查询到您的人事信息！");
            ajax_return($res);
        }
    }

    //直营校人资信息获取
    function hrorganizeApi()
    {
        $Model = new \Model\Api\HrjdbApiModel();
        $Model->updateBsOrgaize();//北森组织归属
    }

    //直营校人资组织归属获取
    function hrorgSchvestApi()
    {
        $Model = new \Model\Api\HrjdbApiModel();
        $Model->updateBsOrgaizeSchool();//北森直营校组织归属
    }

    //获取所有员编
    function getHrworkerApi()
    {
        $Model = new \Model\Api\HrjdbApiModel();
        $Model->getBsWorkerBranch();//北森直营校所有员编
    }

    //获取北森职务编码
    function getBsPostApi()
    {
        $Model = new \Model\Api\HrjdbApiModel();
        $Model->getBsPostCode();//北森所有职务
    }

    //无用户信息更新
    function nostafferUpdataApi()
    {
        $stafferOne = $this->DataControl->selectOne("SELECT w.workerhr_branch 
            FROM smc_staffer_workerhr AS w 
            WHERE w.workerhr_branch NOT IN (SELECT s.staffer_employeepid FROM smc_staffer AS s WHERE s.staffer_employeepid <> '' AND s.company_id = '8888') 
            AND w.company_id = '8888' 
            AND w.workerhr_error = '0' 
            -- and w.workerhr_branch='0140014338'
            LIMIT 0, 1");
        if ($stafferOne) {
            $Model = new \Model\Api\HrjdbApiModel();
            $Model->getWorkerNew($stafferOne);
        } else {
            $res = array('error' => 1, 'errortip' => "暂无需要更新人事信息！");
            ajax_return($res);
        }
    }

    //人资信息更新程序
    function ThreeApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $datawhere = "";
        if (isset($request['employeepid']) && $request['employeepid'] !== '') {
            $datawhere .= " and h.workerhr_branch ='{$request['employeepid']}'";
            $limit = "LIMIT 0,1";
        } else {
            $limit = "LIMIT 0,10";
        }
        $sql = "SELECT h.workerhr_branch as staffer_employeepid 
            FROM smc_staffer_workerhr h 
            WHERE h.company_id = '8888' 
            AND h.workerhr_error = '0'
            {$datawhere} 
            ORDER BY workerhr_updatetime ASC 
            {$limit}";

        $stafferList = $this->DataControl->selectClear($sql);
        if ($stafferList) {
            $Model = new \Model\Api\HrjdbApiModel();
            foreach ($stafferList as $stafferOne) {
                $errortip = $Model->getWorkerInfoFromBs($stafferOne);
                echo "{$errortip}<br />";
            }
            $res = array('error' => 0, 'errortip' => $errortip);
            ajax_return($res);
        } else {
            $res = array('error' => 1, 'errortip' => "暂无需要更新人事信息！");
            ajax_return($res);
        }
    }

    //离职人员信息处理
    function ThreeleaveApi()
    {
        $stafferList = $this->DataControl->selectClear("SELECT
    s.company_id,
    s.staffer_id,
	s.staffer_branch,
	s.staffer_cnname,
	s.staffer_enname,
	s.staffer_mobile,
	s.staffer_employeepid,
	s.staffer_leave,
	w.worker_cnname,
	w.worker_enname,
	w.worker_mobile,
	w.worker_code
FROM
	smc_staffer AS s,
	smc_staffer_worker AS w
WHERE
	s.staffer_id = w.staffer_id
AND s.staffer_leave = '0'
AND w.worker_leave = '1'");
        if ($stafferList) {
            foreach ($stafferList as $stafferOne) {
                $data = array();
                $data['staffer_leave'] = '1';
                $data['staffer_updatetime'] = time();
                $data['staffer_leavetime'] = date("Y-m-d");
                $data['staffer_leavecause'] = "系统已检测HR人资信息离职，进行离职操作！";
                if ($this->DataControl->updateData("smc_staffer", "staffer_id = '{$stafferOne['staffer_id']}'", $data)) {
                    $data = array();
                    $data['company_id'] = $stafferOne['company_id'];
                    $data['staffer_id'] = $stafferOne['staffer_id'];
                    $data['workchange_code'] = 'Z02';
                    $data['postchangeslog_note'] = "集团给教师做离职操作，进行离职清算操作";
                    $data['postchangeslog_day'] = date("Y-m-d", time());
                    $data['postchangeslog_createtime'] = time();
                    $this->DataControl->insertData("gmc_staffer_postchangeslog", $data);
                    $this->addGmcWorkLog($stafferOne['company_id'], '12357', "集团架构->职工管理", '系统已检测HR人资信息离职，进行离职操作！', dataEncode($stafferOne));
                }
                echo "{$stafferOne['staffer_branch']}{$stafferOne['staffer_cnname']}检测HR人资信息离职<br />";
            }
        }
    }

    public function addGmcWorkLog($company_id, $staffer_id, $module, $type, $content)
    {
        $logData = array();
        $logData['company_id'] = $company_id;
        $logData['staffer_id'] = $staffer_id;
        $logData['worklog_module'] = $module;
        $logData['worklog_type'] = $type;
        $logData['worklog_content'] = $content;
        $logData['worklog_ip'] = real_ip();
        $logData['worklog_time'] = time();
        $this->DataControl->insertData('gmc_staffer_worklog', $logData);
    }

    function getInfoFromYZJView()
    {
        $request = Input('get.', '', 'trim,addslashes');

        $Model = new \Model\Api\HrjdbApiModel();
//        $result = $Model->getAccessTokenFromYZJ($request['scope']);
        $result = $Model->getAcquireContextFromYZJ();
//        $result = $Model->getPersonInfoFromYZJ();

        if ($result) {
            $res = array('error' => '0', 'errortip' => '获取成功', 'result' => $result);
        } else {
            $res = array('error' => '1', 'errortip' => '获取失败', 'result' => array());
        }
        ajax_return($res, $request['language_type']);
    }


}