<?php 

namespace Work\Controller\Api;

class PublicController extends viewTpl
{
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";

    function __construct()
    {
        parent::__construct();
    }

    function getOrderOneInfoView(){

        $request = Input('get.', '', 'trim,addslashes');

        if(empty($request['order_pid'])){
            $this->error = true;
            $this->errortip = '订单pid不能为空';
            ajax_return($this->error, $this->errortip);
            return;
        }

        $sql = "select a.mergeorder_pid as order_pid,b.student_cnname,b.student_branch,c.school_cnname,1 as type 
                from smc_payfee_mergeorder as a 
                inner join smc_student as b on b.student_id=a.student_id
                inner join smc_school as c on c.school_id=a.school_id
                where a.mergeorder_pid='{$request['order_pid']}' and a.mergeorder_status=0";
        $orderOne=$this->DataControl->selectOne($sql);
        if(empty($orderOne)){

            $sql = "select a.order_pid,b.student_cnname,b.student_branch,c.school_cnname,0 as type 
                from smc_payfee_order as a 
                inner join smc_student as b on b.student_id=a.student_id
                inner join smc_school as c on c.school_id=a.school_id
                where a.order_pid='{$request['order_pid']}' and a.mergeorder_pid='' and a.order_status>0 and a.order_status<>4";
            $orderOne=$this->DataControl->selectOne($sql);
        }
        if(empty($orderOne)){
            ajax_return(array('error' => 1, 'errortip' => '订单不存在'), $request['language_type']);
        }

        if (!empty($orderOne['student_cnname'])) {
            $name = $orderOne['student_cnname'];
            $len = mb_strlen($name, 'UTF-8');
            if ($len == 2) {
                // 两个字：只显示第一个字，第二个用*号
                $orderOne['student_cnname'] = mb_substr($name, 0, 1, 'UTF-8') . '*';
            } elseif ($len > 2) {
                // 超过两个字：显示第一个和最后一个字，中间用*号
                $orderOne['student_cnname'] = mb_substr($name, 0, 1, 'UTF-8') . str_repeat('*', $len - 2) . mb_substr($name, -1, 1, 'UTF-8');
            } else {
                // 一个字：后面加*
                $orderOne['student_cnname'] = $name . '*';
            }
        }

        ajax_return(array('error' => 0, 'errortip' => '获取成功', 'result' => $orderOne));

    }

    /**
     * 获取支付宝用户ID
     * 根据支付宝开放平台文档实现
     * @link https://opendocs.alipay.com/support/01raya
     */
    function getAlipayUserid(){
        
        $request = Input('get.', '', 'trim,addslashes');
        
        // 检查是否有auth_code参数（从回调中获取）
        if (!empty($request['auth_code'])) {
            // 步骤3: 使用auth_code获取用户ID
            return $this->exchangeAuthCodeForUserId($request['auth_code']);
        }
        
        // 步骤1: 构建授权链接
        return $this->buildAuthorizationUrl();
    }
    
    /**
     * 构建支付宝授权链接
     * 步骤1: 引导用户访问授权链接
     */
    private function buildAuthorizationUrl(){
        
        // 这些配置应该从配置文件或数据库中读取
        $appId = '你的APPID'; // 需要替换为实际的APPID
        $scope = 'auth_base'; // 授权范围，固定为auth_base
        $redirectUri = urlencode('你的回调地址'); // 需要替换为实际的回调地址
        
        // 构建授权URL
        $authUrl = "https://openauth.alipay.com/oauth2/publicAppAuthorize.htm?app_id={$appId}&scope={$scope}&redirect_uri={$redirectUri}";
        
        ajax_return(array(
            'error' => 0, 
            'errortip' => '请访问授权链接',
            'result' => array(
                'auth_url' => $authUrl,
                'tip' => '请用户访问此链接进行授权，授权后会跳转到回调页面'
            )
        ));
    }
    
    /**
     * 使用auth_code交换用户ID
     * 步骤3: 调用alipay.system.oauth.token API
     */
    private function exchangeAuthCodeForUserId($authCode){
        
        // 支付宝网关地址
        $gatewayUrl = 'https://openapi.alipay.com/gateway.do';
        
        // 公共参数（需要根据实际情况配置）
        $params = array(
            'app_id' => '你的APPID', // 需要替换
            'method' => 'alipay.system.oauth.token',
            'charset' => 'UTF-8',
            'sign_type' => 'RSA2',
            'timestamp' => date('Y-m-d H:i:s'),
            'version' => '1.0',
            'grant_type' => 'authorization_code',
            'code' => $authCode
        );
        
        // 生成签名（这里需要使用你的私钥）
        $params['sign'] = $this->generateAlipaySign($params);
        
        // 发送HTTP请求
        $response = $this->curlPost($gatewayUrl, $params);
        
        if ($response) {
            $result = json_decode($response, true);
            
            if (isset($result['alipay_system_oauth_token_response'])) {
                $tokenResponse = $result['alipay_system_oauth_token_response'];
                
                if ($tokenResponse['code'] == '10000') {
                    // 成功获取用户ID
                    ajax_return(array(
                        'error' => 0,
                        'errortip' => '获取用户ID成功',
                        'result' => array(
                            'user_id' => $tokenResponse['user_id'],
                            'access_token' => $tokenResponse['access_token'],
                            'expires_in' => $tokenResponse['expires_in']
                        )
                    ));
                } else {
                    // API调用失败
                    ajax_return(array(
                        'error' => 1,
                        'errortip' => '获取用户ID失败: ' . $tokenResponse['msg']
                    ));
                }
            }
        }
        
        ajax_return(array('error' => 1, 'errortip' => '网络请求失败'));
    }
    
    /**
     * 生成支付宝签名
     * 注意：这里需要使用你的RSA私钥
     */
    private function generateAlipaySign($params){
        // 排除sign字段
        unset($params['sign']);
        
        // 排序并拼接参数
        ksort($params);
        $stringToBeSigned = '';
        foreach ($params as $k => $v) {
            if ($v !== '' && $v !== null) {
                $stringToBeSigned .= $k . '=' . $v . '&';
            }
        }
        $stringToBeSigned = rtrim($stringToBeSigned, '&');
        
        // TODO: 这里需要使用你的RSA私钥进行签名
        // $privateKey = '你的RSA私钥';
        // 示例签名逻辑（需要根据实际私钥实现）
        // return $this->rsaSign($stringToBeSigned, $privateKey);
        
        // 临时返回空字符串，实际使用时需要实现真正的签名
        return '';
    }
    
    /**
     * 发送POST请求
     */
    private function curlPost($url, $data){
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($httpCode == 200) {
            return $response;
        }
        
        return false;
    }

}
