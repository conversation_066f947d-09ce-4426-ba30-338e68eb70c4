<?php

namespace Work\Controller\Api;

class WxmessageController extends viewTpl
{
    public $u;
    public $t;
    public $c;
    public $visitType = "api";


    //预加载处理类
    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
    }

    //待分配名单的微信提醒
    function wxClientAllotRemindView()
    {
        return;
        $fixedTime = date("Y-m-d H:i:s");
        $today = date("Y-m-d");

        if ($fixedTime < $today . ' 18:00:00') {
            ajax_return(array('error' => 1, 'errortip' => "请在18点以后发送提醒", "bakfuntion" => "okmotify"));
        }

        $dataList = $this->DataControl->selectClear("
            select f.staffer_id,l.school_id,l.school_cnname,l.school_shortname,f.staffer_mobile,f.company_id 
            from gmc_staffer_postbe as p
            left join gmc_company_post as pt ON pt.post_id =p.post_id
            left join smc_staffer as f ON f.staffer_id = p.staffer_id
            left join smc_school as l ON l.school_id = p.school_id
            left join (select y.company_id,x.school_id,count(y.client_id) as count_c
				from crm_client_schoolenter x left join crm_client y on x.client_id=y.client_id
				where x.is_enterstatus=1 and y.client_tracestatus=0 and y.client_distributionstatus=0
				group by y.company_id,x.school_id) s on s.company_id=l.company_id and s.school_id=l.school_id 
            where p.postbe_status = 1 and pt.post_istopjob =1 and p.school_id > 0 
            and l.school_istest = 0 and l.school_isclose =0 
            and p.postbe_iscrmuser = 1
            and pt.post_name like '%校长%'
            and s.count_c>0
            AND f.staffer_wxtoken <> '' 
            and not EXISTS (SELECT lg.staffer_id FROM crm_wxsend_log AS lg WHERE lg.staffer_id = f.staffer_id and lg.log_day = '{$today}' AND lg.log_type = 'ToDistribute' and lg.school_id=l.school_id  limit 0,1) 
            -- and EXISTS (select t.client_id from crm_client as t,crm_client_schoolenter as s where t.company_id = l.company_id and t.client_id=s.client_id and s.school_id =l.school_id and s.is_enterstatus = 1 and t.client_tracestatus = 0 and t.client_distributionstatus = 0  limit 0,1) 
            group by p.staffer_id,p.school_id 
            order by l.school_id DESC limit 0,1
         ");

//        (select count(t.client_id) from crm_client as t,crm_client_schoolenter as s where t.client_id=s.client_id and s.school_id =l.school_id and s.is_enterstatus = 1 and t.client_tracestatus = 0 and t.client_distributionstatus = 0 ) as client_num
        if ($dataList) {
            foreach ($dataList as $val) {
                $cnum = $this->DataControl->selectOne("select count(t.client_id) as cnum 
                        from crm_client as t,crm_client_schoolenter as s 
                        where  t.company_id = '{$val['company_id']}' and t.client_tracestatus = 0 and t.client_distributionstatus = 0 
                        and t.client_id = s.client_id  and s.school_id = '{$val['school_id']}' and s.is_enterstatus = 1  ");
                $val['client_num'] = $cnum['cnum']?$cnum['cnum']:0;

                $isset = $this->DataControl->getFieldOne("gmc_company_masterplate","masterplate_wxid","company_id = '{$val['company_id']}' and masterplate_name = '学员待分配提醒' and masterplate_class = '0'");
                if($isset){
                    $wxid = $isset['masterplate_wxid'];
                }else{
                    $masterplate = $this->DataControl->getFieldOne("gmc_company_masterplate","masterplate_wxid","company_id = '0' and masterplate_name = '学员待分配提醒' and masterplate_class = '0'");
                    $wxid = $masterplate['masterplate_wxid'];
                }

                $clientname = $this->DataControl->selectOne("select group_concat(q.client_cnname) as client_cnname  from (select t.client_cnname,s.school_id from crm_client as t,crm_client_schoolenter as s where t.client_id=s.client_id and  t.client_tracestatus = 0 and t.client_distributionstatus = 0 and s.school_id ='{$val['school_id']}' and s.is_enterstatus = 1 limit 0,3) as q limit 0,1  ");
                $firstnote = "亲爱的招生主管你好,【{$val['school_cnname']}】您有{$val['client_num']}条招生有效名单待分配，请及时分配喔~";
                if($val['client_num'] > 3 ){
                    $keyword1 = $clientname['client_cnname'] . "...";
                }else{
                    $keyword1 = $clientname['client_cnname'];
                }
                $keyword2 = date("Y年m月d日 H:i");
                $keyword3 = $val['school_cnname'];
                $footernote = "请及时处理，点击查看详情查看名单信息";
                $wxteModel = new \Model\Api\ZjwxChatModel($val['staffer_id'],$val['school_id']);
                $url = "https://tesc.kedingdang.com/crmIndex/validAllList";
                $wxteModel->ToDistribute($firstnote, $keyword1, $keyword2, $keyword3, $footernote, $url, $wxid);
            }
            ajax_return(array('error' => 0, 'errortip' => "微信通知发送成功!"));
        }else{
            ajax_return(array('error' => 1, 'errortip' => "暂无可推送的信息!"));
        }
    }
    //待分配名单的微信提醒
    function wxClientAllotRemindBakView()
    {
        return;
        $fixedTime = date("Y-m-d H:i:s");
        $today = date("Y-m-d");

        if ($fixedTime < $today . ' 18:00:00') {
            ajax_return(array('error' => 1, 'errortip' => "请在18点以后发送提醒", "bakfuntion" => "okmotify"));
        }

        $dataList = $this->DataControl->selectClear("
            select f.staffer_id,l.school_id,l.school_cnname,l.school_shortname,f.staffer_mobile,f.company_id,
            (select count(t.client_id) from crm_client as t,crm_client_schoolenter as s where t.client_id=s.client_id and s.school_id =l.school_id and s.is_enterstatus = 1 and t.client_tracestatus = 0 and t.client_distributionstatus = 0 ) as client_num
          
            from gmc_staffer_postbe as p
            left join gmc_company_post as pt ON pt.post_id =p.post_id
            left join smc_staffer as f ON f.staffer_id = p.staffer_id
            left join smc_school as l ON l.school_id = p.school_id
            where p.postbe_status = 1 and pt.post_istopjob =1 and p.school_id > 0 
            and l.school_istest = 0 and l.school_isclose =0 
            and p.postbe_iscrmuser = 1
            and pt.post_name like '%校长%'
            AND f.staffer_wxtoken <> ''
            AND f.staffer_id NOT IN ( SELECT lg.staffer_id FROM crm_wxsend_log AS lg WHERE lg.log_day = '{$today}' AND lg.log_type = 'ToDistribute' and lg.school_id=l.school_id )
            group by p.staffer_id,p.school_id
            HAVING client_num > 0
            order by l.school_id DESC limit 0,1
         ");


        if ($dataList) {
            foreach ($dataList as $val) {
                $isset = $this->DataControl->getFieldOne("gmc_company_masterplate","masterplate_wxid","company_id = '{$val['company_id']}' and masterplate_name = '学员待分配提醒' and masterplate_class = '0'");
                if($isset){
                    $wxid = $isset['masterplate_wxid'];
                }else{
                    $masterplate = $this->DataControl->getFieldOne("gmc_company_masterplate","masterplate_wxid","company_id = '0' and masterplate_name = '学员待分配提醒' and masterplate_class = '0'");
                    $wxid = $masterplate['masterplate_wxid'];
                }

                $clientname = $this->DataControl->selectOne("select group_concat(q.client_cnname) as client_cnname  from (select t.client_cnname,s.school_id from crm_client as t,crm_client_schoolenter as s where t.client_id=s.client_id and  t.client_tracestatus = 0 and t.client_distributionstatus = 0 and s.school_id ='{$val['school_id']}' and s.is_enterstatus = 1 limit 0,3) as q limit 0,1  ");
                $firstnote = "亲爱的招生主管你好,【{$val['school_cnname']}】您有{$val['client_num']}条招生有效名单待分配，请及时分配喔~";
                if($val['client_num'] > 3 ){
                    $keyword1 = $clientname['client_cnname'] . "...";
                }else{
                    $keyword1 = $clientname['client_cnname'];
                }
                $keyword2 = date("Y年m月d日 H:i");
                $keyword3 = $val['school_cnname'];
                $footernote = "请及时处理，点击查看详情查看名单信息";
                $wxteModel = new \Model\Api\ZjwxChatModel($val['staffer_id'],$val['school_id']);
                $url = "https://tesc.kedingdang.com/crmIndex/validAllList";
                $wxteModel->ToDistribute($firstnote, $keyword1, $keyword2, $keyword3, $footernote, $url, $wxid);
            }
            ajax_return(array('error' => 0, 'errortip' => "微信通知发送成功!"));
        }else{
            ajax_return(array('error' => 1, 'errortip' => "暂无可推送的信息!"));
        }
    }

//    招生跟踪提醒
    function wxClientTrackRemindView()
    {
        $today = date("Y-m-d");
        $fixedTime = date("Y-m-d H:i:s");
        if (($fixedTime < $today . ' 08:30:00')) {
            ajax_return(array('error' => 1, 'errortip' => "请在8点半以后发送提醒", "bakfuntion" => "okmotify"));
        }
        $sql = "select f.staffer_cnname,f.staffer_id,m.marketer_id,r.school_id,l.school_cnname,l.company_id,
            count(r.client_id) as num
            from crm_remind as r 
            left join smc_school as l  ON r.school_id = l.school_id 
            left join crm_marketer as m ON m.marketer_id  = r.marketer_id
            left join smc_staffer as f ON f.staffer_id  = m.staffer_id  
            where r.remind_time='{$today}' 
            AND f.staffer_wxtoken <> ''
           AND f.staffer_id NOT IN ( SELECT l.staffer_id FROM crm_wxsend_log AS l WHERE l.log_day = '{$today}' AND l.log_type = 'StuTrack' and r.school_id = l.school_id )
            group by f.staffer_id,r.school_id 
            limit 0,1
        ";
        $dataList = $this->DataControl->selectClear($sql);
        $weekarray = array("日", "一", "二", "三", "四", "五", "六");
        if ($dataList) {
            foreach ($dataList as $key => $val) {
                $clientname = $this->DataControl->selectOne("select group_concat(q.client_cnname) as client_cnname from (select  t.client_cnname from crm_client as t,crm_remind as r where r.client_id = t.client_id and r.marketer_id = '{$val['marketer_id']}' and r.school_id='{$val['school_id']}' and  r.remind_time='{$today}'  limit 0,3) as q limit 0,1 ");

                $isset = $this->DataControl->getFieldOne("gmc_company_masterplate","masterplate_wxid","company_id = '{$val['company_id']}' and masterplate_name = '客户跟进提醒' and masterplate_class = '0'");
                if($isset){
                    $wxid = $isset['masterplate_wxid'];
                }else{
                    $masterplate = $this->DataControl->getFieldOne("gmc_company_masterplate","masterplate_wxid","company_id = '0' and masterplate_name = '客户跟进提醒' and masterplate_class = '0'");
                    $wxid = $masterplate['masterplate_wxid'];
                }


                $firstnote = "亲爱的招生老师你好，【{$val['school_cnname']}】您有{$val['num']}名意向客户设置为今日跟踪，请做好跟进准备喔~";
                if($val['num'] >3){
                    $keyword1 = $clientname['client_cnname'] . "...";
                }else{
                    $keyword1 = $clientname['client_cnname'];
                }
                $keyword2 = date("Y.m.d") . '周' . $weekarray[date("w")];
                $footernote = "请尽快联系，点击查看详情查看客户信息";
                $url = "https://tesc.kedingdang.com/crmIndex/admissionsTracking";
                $wxteModel = new \Model\Api\ZjwxChatModel($val['staffer_id'],$val['school_id']);
                $wxteModel->StuTrack($firstnote, $keyword1, $keyword2, $footernote, $url, $wxid);
            }
            ajax_return(array('error' => 0, 'errortip' => "微信通知发送成功!"));
        }else{
            ajax_return(array('error' => 1, 'errortip' => "暂无可推送的消息!"));
        }
    }

    //s
    function wxClientInviteAudRemindView()
    {
        $today = date("Y-m-d");
        $sql = "
                select i.marketer_id,i.client_id,t.client_cnname,i.invite_visittime as visittime,'柜询' as reason_test,f.staffer_id,i.school_id,l.school_cnname from crm_client_invite as i,crm_client as t,crm_marketer as m,smc_staffer as f ,smc_school as l 
                where t.client_id =i.client_id  and m.marketer_id = i.marketer_id and m.staffer_id = f.staffer_id and l.school_id=i.school_id  
                and (unix_timestamp(i.invite_visittime) - unix_timestamp(now()) <=60*60*2) and (unix_timestamp(i.invite_visittime) - unix_timestamp(now()) > 0 ) 
                AND f.staffer_wxtoken <> ''
               AND f.staffer_id NOT IN ( SELECT l.staffer_id FROM crm_wxsend_log AS l WHERE l.log_day = '{$today}' AND l.log_type = 'Reception' AND l.log_data_id =i.client_id  ) 
            UNION  
                select a.marketer_id,a.client_id,t.client_cnname,a.audition_visittime as visittime,'试听' as reason_test,f.staffer_id,a.school_id,l.school_cnname,l.company_id  from crm_client_audition as a,crm_client as t,crm_marketer as m,smc_staffer as f,smc_school as l  where t.client_id =a.client_id and  m.marketer_id = a.marketer_id and m.staffer_id =f.staffer_id  and a.school_id=l.school_id and (unix_timestamp(a.audition_visittime) - unix_timestamp(now()) <=60*60*2) and (unix_timestamp(a.audition_visittime) - unix_timestamp(now()) > 0 ) 
                AND f.staffer_wxtoken <> ''  
               AND f.staffer_id NOT IN ( SELECT l.staffer_id FROM crm_wxsend_log AS l WHERE l.log_day = '{$today}' AND l.log_type = 'Reception' AND l.log_data_id =a.client_id ) 
                LIMIT 0,1
         ";

        $dataList = $this->DataControl->selectClear($sql);
        if ($dataList) {
            $weekarray = array("日", "一", "二", "三", "四", "五", "六");
            foreach ($dataList as $key => $val) {
                $isset = $this->DataControl->getFieldOne("gmc_company_masterplate","masterplate_wxid","company_id = '{$val['company_id']}' and masterplate_name = '接待提醒' and masterplate_class = '0'");
                if($isset){
                    $wxid = $isset['masterplate_wxid'];
                }else{
                    $masterplate = $this->DataControl->getFieldOne("gmc_company_masterplate","masterplate_wxid","company_id = '0' and masterplate_name = '接待提醒' and masterplate_class = '0'");
                    $wxid = $masterplate['masterplate_wxid'];
                }

                $firstnote = "亲爱的招生老师你好，【{$val['school_cnname']}】意向客户{$val['client_cnname']}预约今天{$val['visittime']}到校柜询，请做好接待准备喔~";
                $keyword1 = $val['visittime'] . ' 周' . $weekarray[date("w")];
                $keyword2 = $val['client_cnname'];
                $keyword3 = $val['reason_test'];
                $keyword4 = '1人';
                $footernote = "请做好接待工作喔~";
                $url = "";
                $wxteModel = new \Model\Api\ZjwxChatModel($val['staffer_id'],$val['school_id'],$val['client_id']);
                $wxteModel->Reception($firstnote, $keyword1, $keyword2, $keyword3, $keyword4, $footernote, $url, $wxid);
            }
            ajax_return(array('error' => 0, 'errortip' => "微信通知发送成功!"));
        }else{
            ajax_return(array('error' => 0, 'errortip' => "暂无可推送的数据!"));
        }
    }


    //招生日报
    function dailyClientReportView()
    {
        $today = date("Y-m-d");
        $starttime = strtotime($today);
        $endtime = $starttime + 3600 * 24 - 1;

        $fixedTime = date("Y-m-d H:i:s");
        if ($fixedTime < $today . ' 19:00:00') {
            ajax_return(array('error' => 1, 'errortip' => "请在19点以后发送提醒", "bakfuntion" => "okmotify"));
        }

        $sql = "SELECT o.company_id, s.staffer_id, s.staffer_cnname, o.school_id, o.school_cnname,o.school_shortname
FROM smc_staffer AS s, gmc_staffer_postbe AS p, gmc_company_post AS t, smc_school AS o
WHERE s.company_id = '8888' AND s.staffer_id = p.staffer_id AND p.school_id = o.school_id
AND p.post_id = t.post_id
AND s.staffer_wxtoken <> ''
AND s.staffer_leave <> '1'
AND p.school_id > 0
AND t.post_istopjob = '1'
AND p.postbe_iscrmuser = 1
AND o.school_istest = 0
AND o.school_isclose = 0
AND s.staffer_id NOT IN ( SELECT lg.staffer_id FROM crm_wxsend_log AS lg WHERE lg.log_day = '{$today}' AND lg.log_type = 'Daily' AND lg.school_id=o.school_id)
GROUP BY s.staffer_id,o.school_id LIMIT 0,1";
        $dataList = $this->DataControl->selectClear($sql);
        if ($dataList) {
            foreach ($dataList as $key => $val) {
                $toolsql = "SELECT (select count(t.client_id) from crm_client as t,crm_client_schoolenter as s where t.client_id=s.client_id and s.school_id =o.school_id and t.client_tracestatus = 0 and t.client_distributionstatus = 0 ) as client_noallotnum,
           (select count(t.client_id) from crm_client as t,crm_client_schoolenter as s where t.client_id=s.client_id and s.school_id =o.school_id and t.client_tracestatus <> 4 and t.client_tracestatus <> -1 and  t.client_tracestatus <> -2 and t.client_distributionstatus = 1 ) as client_intentionnum,
           (select count(t.client_id) from crm_client as t,crm_client_schoolenter as s where t.client_id=s.client_id and s.school_id =o.school_id and t.client_tracestatus =0 and t.client_distributionstatus = 1 ) as client_notracknum,
           (select count(t.client_id) from crm_client as t,crm_client_schoolenter as s where t.client_id=s.client_id and s.school_id =o.school_id and t.client_createtime >='{$starttime}' and  t.client_createtime <='{$endtime}' ) as client_insertNum,
           (select count(i.invite_id) from crm_client_invite as i where i.school_id = o.school_id and DATE_FORMAT(i.invite_visittime,'%Y-%m-%d')  ='{$today}' ) as invite_num,
           (select count(a.audition_id) from crm_client_audition as a where a.school_id = o.school_id and DATE_FORMAT(a.audition_visittime,'%Y-%m-%d')  ='{$today}' ) as audition_num,
            (select count(i.invite_id) from crm_client_invite as i where i.school_id = o.school_id and DATE_FORMAT(i.invite_visittime,'%Y-%m-%d')  ='{$today}' and i.invite_isvisit =1 ) as invite_arrivenum,
           (select count(a.audition_id) from crm_client_audition as a where a.school_id = o.school_id and DATE_FORMAT(a.audition_visittime,'%Y-%m-%d')  ='{$today}' and  a.audition_isvisit =1 ) as audition_arrivenum,
             (select count(t.client_id) from crm_client as t,crm_client_schoolenter as s where t.client_id=s.client_id and s.school_id =o.school_id and t.client_tracestatus = '-1' and client_ischaserlapsed = '0' ) as client_lossnu
             FROM smc_school AS o WHERE  o.school_id = '{$val['school_id']}' limit 0,1";
                $ToolOne = $this->DataControl->selectOne($toolsql);

                $isset = $this->DataControl->getFieldOne("gmc_company_masterplate","masterplate_wxid","company_id = '{$val['company_id']}' and masterplate_name = '报告生成通知' and masterplate_class = '0'");
                if($isset){
                    $wxid = $isset['masterplate_wxid'];
                }else{
                    $masterplate = $this->DataControl->getFieldOne("gmc_company_masterplate","masterplate_wxid","company_id = '0' and masterplate_name = '报告生成通知' and masterplate_class = '0'");
                    $wxid = $masterplate['masterplate_wxid'];
                }

                $invite_audNum = $ToolOne['invite_num'] + $ToolOne['audition_num'];
                $invite_arr_audNum = $ToolOne['invite_arrivenum'] + $ToolOne['audition_arrivenum'];
                $rate = $invite_arr_audNum > 0 ? round($invite_arr_audNum/$invite_audNum, 4) * 100 : 0;
                $firstnote = "报告生成通知";
                $keyword1 = '招生日报';
                $keyword2 = date("Y.m.d H:i");
                $footernote = "亲爱的校长你好，请查看{$val['school_cnname']}招生日报：当前待分配招生有效名单{$ToolOne['client_noallotnum']}条，当前意向客户数{$ToolOne['client_intentionnum']}条，待跟踪客户数{$ToolOne['client_notracknum']}条，无意向审核客户{$ToolOne['client_lossnum']}条,本日新增有效名单{$ToolOne['client_insertNum']}条，本日应接待柜询试听名单{$invite_audNum}条，邀约到访名单{$invite_arr_audNum}条，到访率{$rate}%，请知悉喔~";
                $url = "";
                $wxteModel = new \Model\Api\ZjwxChatModel($val['staffer_id'],$val['school_id']);
                $wxteModel->Daily($firstnote, $keyword1, $keyword2, $footernote, $url, $wxid);
            }
            ajax_return(array('error' => 0, 'errortip' => "微信通知发送成功!"));
        }
        ajax_return(array('error' => 1, 'errortip' => "暂无可以推送的数据!"));

    }
}




