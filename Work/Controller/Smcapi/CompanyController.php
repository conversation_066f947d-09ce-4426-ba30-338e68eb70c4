<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/13
 * Time: 23:25
 * crm-首页
 */

namespace Work\Controller\Smcapi;

class CompanyController extends viewTpl
{
	public $u;
	public $t;
	public $c;
	public $visitType = "api";
	public $Show_css;
	
	
	//预加载处理类
	function __construct($visitType = "api")
	{
        parent::__construct ();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();

		//数据库操作
		$this->Show_css = new \Dbsqlplay();
	}

    //获取个人基本信息名字
    function getOwnInfoApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Gmc\PostModel($request);

        $result = $Model->getOwnInfoApi($request);
        ajax_return($result,$request['language_type']);
    }

    //获取账号资料
    function UserView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Gmc\PostModel($request);

        $result = $Model->getUserlist($request);
        ajax_return($result,$request['language_type']);
    }

    //获取首页名字
    function getNameApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Gmc\PostModel($request);
        $res = $Model->getNameApi($request);
        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }

    //查看职工资料建档
    function StafferDataApi()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model =  new \Model\Gmc\StafferModel($request);

        $result = $Model->StafferDataApi($request);

        ajax_return($result,$request['language_type']);

    }

    //编辑职工资料建档
    function updateStafferDataAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model =  new \Model\Gmc\StafferModel($request);

        $result = $Model->updateStafferDataAction($request);

        $this->addSmcWorkLog($request['company_id'],$request['school_id'],$request['staffer_id'],"职工管理->资料建档",'编辑职工资料建档',dataEncode($request));
        ajax_return($result,$request['language_type']);
    }

    //查看职工校园职务
    function StafferSchoolPostApi()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model =  new \Model\Gmc\StafferModel($request);

        $result = $Model->StafferSchoolPostApi($request);

        ajax_return($result,$request['language_type']);

    }

    //查看职工集团职务
    function StafferCompanyPostApi()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model =  new \Model\Gmc\StafferModel($request);

        $result = $Model->StafferCompanyPostApi($request);

        ajax_return($result,$request['language_type']);

    }

    //修改密码
    function updatePassAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model =  new \Model\Smc\AffairsModel($request);

        $result = $Model->updatePassAction($request);

        $this->addSmcWorkLog($request['company_id'],$request['school_id'],$request['staffer_id'],"职工管理->资料建档",'修改密码职工密码',dataEncode($request));
        ajax_return($result,$request['language_type']);
    }


}