<?php


namespace Work\Controller\Smcapi;


class StuBalanceController extends viewTpl
{
    public $u;
    public $t;
    public $c;

    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
    }

    //账户充值
    function stuDepositAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\StuBalanceModel($request);

        $order_pid = $Model->stuDepositOne($request);

        if ($request['price'] <= 0) {
            $res = array('error' => 1, 'errortip' => '请输入正确的金额', 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        if ($order_pid) {
            $res = array('error' => 0, 'errortip' => '充值订单成功', 'result' => $order_pid);
        } else {
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => array());
        }
        ajax_return($res, $request['language_type']);

    }

//	  账户退款
    function sturRefundAction()
    {
        $request = Input('post.', '', 'trim,addslashes');

        $sql = "select po.order_id from smc_payfee_order as po where po.student_id='{$request['student_id']}' and po.company_id='{$request['company_id']}' and po.school_id='{$request['school_id']}' and po.order_status<'4' and po.order_status>='0'";

        if ($this->DataControl->selectOne($sql)) {
            $res = array('error' => 1, 'errortip' => "存在未完成订单,不可退费", 'result' => array());
            ajax_return($res, $request['language_type']);
        }

        $publicarray = array();
        $publicarray['company_id'] = $request['company_id'];
        $publicarray['school_id'] = $request['school_id'];
        $publicarray['staffer_id'] = $request['staffer_id'];
        $Model = new \Model\Smc\RefundModel($publicarray);
        $this->ThisVerify($request);
        if (isset($request['create_time']) && $request['create_time'] != "") {
            $time = strtotime($request['create_time']);

        } else {
            $time = time();
        }

        $refundList=json_decode(stripslashes($request['refundList']),true);
        if(!$refundList){
            $this->error = true;
            $this->errortip = "无退款信息";
            return false;
        }

        if($refundList){

            foreach($refundList as $refundOne){
                $stublcOne = $this->getStuBalance($request['student_id'],$request['company_id'],$request['school_id'],$refundOne['companies_id']);

                if($stublcOne['student_balance']<$refundOne['refund_price']){
                    $this->error = true;
                    $this->errortip = "退费金额不可超过已有金额";
                    return false;
                }
            }

            foreach($refundList as $refundOne){
                $Model->refund($request['student_id'], $request['bank'], $request['order_bankcard'], $request['accountname'], '申请退款', $information = "订单提交成功，等待校长审核", $request['remark'], $time, $request['is_clear'], $refundOne['refund_price'], $refundOne['specialprice'], $refundOne['refund_specialreason'],$refundOne['companies_id']);
            }
        }

        $res = array('error' => 0, 'errortip' => $Model->errortip, 'result' => '');
        
        ajax_return($res, $request['language_type']);
    }

    function getStuBalance($student_id, $company_id, $school_id, $companies_id)
    {
        $sql = "select s.student_forwardprice,b.* from smc_student as s,smc_student_balance as b
        WHERE b.student_id = s.student_id 
        and b.school_id = '{$school_id}' 
        and s.student_id='{$student_id}' 
        and b.companies_id='{$companies_id}' 
        and s.company_id='{$company_id}'";
        $stublcOne = $this->DataControl->selectOne($sql);

        if (!$stublcOne) {
            $data = array();
            $data['company_id'] = $company_id;
            $data['companies_id'] = $companies_id;
            $data['school_id'] = $school_id;
            $data['student_id'] = $student_id;
            $this->DataControl->insertData("smc_student_balance", $data);

            $stublcOne = $this->DataControl->selectOne($sql);
        }
        return $stublcOne;
    }

    //生成支付订单
    function stuOrderPayAction()
    {
        $request = Input('post.', '', 'trim,addslashes');

        if (isset($request['create_time'])) {
            $create_time = $request['create_time'];
        } else {
            $create_time = "";
        }
        $this->ThisVerify($request);
        $orderHandleModel = new  \Model\Smc\OrderHandleModel($request);
        $pid = $orderHandleModel->orderPay($request['pay_typename'], 0, $note = "账户充值", $request['paytype_code'], $request['pay_price'], $create_time, 3);
        $res = array('error' => 0, 'errortip' => '生成支付订单成功', 'result' => $pid);

        ajax_return($res, $request['language_type']);
    }

    //更新支付状态
    function updateOrderPayAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        if (isset($request['create_time'])) {
            $create_time = $request['create_time'];
        } else {
            $create_time = "";
        }

        $orderHandleModel = new  \Model\Smc\OrderHandleModel($request, $request['order_pid']);
        if ($orderHandleModel->updateOrderPay($request['pay_pid'], $request['pay_issuccess'], "", "", "", $create_time)) {
            $res = array('error' => 0, 'errortip' => '更新成功', 'result' => array());
        } else {
            $res = array('error' => 0, 'errortip' => '更新失败', 'result' => array());
        }

        ajax_return($res, $request['language_type']);

    }

    function getRefundBalanceApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\StuBalanceModel($request);
        $res= $Model->getRefundBalance($request);
        $field=array();
        $k=0;
        $field[$k]["fieldstring"] = "companies_cnname";
        $field[$k]["fieldname"] = "主体名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_balance";
        $field[$k]["fieldname"] = "可退金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result=array();
        $result["field"] = $field;
        if($res){
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
        }else{
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);

        }
        ajax_return($res,$request['language_type']);
    }


    //补费  --查询订单信息
    function getOrderListApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);

        $Model = new \Model\Smc\StuBalanceModel($request);
        $orderList = $Model->getOrderList($request);

        $fieldstring = array('order_pid', 'order_type', 'feeitem_cnname', 'staffer_cnname', 'order_time', 'school_cnname', 'order_allprice', 'order_coupon_price', 'order_paidprice');
        $fieldname = array('订单编号', '订单类型', '项目名称', '经办人', '经办时间', '经办学校', '订单总价', '优惠金额', '已支付金额');
        $fieldcustom = array('1', "1", "1", "1", "1", "1", "1", "1", "1");
        $fieldshow = array('1', "1", "1", "1", "1", "1", "1", "1", "1");
        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["fieldname"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }
        $result = array();
        $result['fieldcustom'] = 1;
        $result['field'] = $field;
        $result['list'] = $orderList;

        if ($orderList) {
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
        } else {
            $res = array('error' => 1, 'errortip' => '暂无订单信息', 'result' => $result);
        }

        ajax_return($res, $request['language_type']);
    }

    //根据学校获取适配的定金班组
    function getDepositCoursetypeBySchoolApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
//        $this->ThisVerify($request);
        $Model = new \Model\Smc\StuBalanceModel($request);
        $res = $Model->getDepositCoursetypeBySchool($request);
        $field = array();

        $k = 0;
        $field[$k]["fieldstring"] = "company_id";
        $field[$k]["fieldname"] = "集团ID";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "coursetype_id";
        $field[$k]["fieldname"] = "班组id";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursetype_cnname";
        $field[$k]["fieldname"] = "班组名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursetype_branch";
        $field[$k]["fieldname"] = "班组编码";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;
        if ($res) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $res['allnum'] = 0;
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);

        }
        ajax_return($res, $request['language_type']);
    }

    //根据学校获取适配的定金班组
    function getDepositCoursecatBySchoolApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
//        $this->ThisVerify($request);
        $Model = new \Model\Smc\StuBalanceModel($request);
        $res = $Model->getDepositCoursecatBySchool($request);
        $field = array();

        $k = 0;
        $field[$k]["fieldstring"] = "company_id";
        $field[$k]["fieldname"] = "集团ID";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "coursetype_id";
        $field[$k]["fieldname"] = "班组id";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursecat_id";
        $field[$k]["fieldname"] = "班种id";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursecat_cnname";
        $field[$k]["fieldname"] = "班种名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursecat_branch";
        $field[$k]["fieldname"] = "班种编码";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;
        if ($res) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $res['allnum'] = 0;
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //根据学校班组获取适配的定金金额
    function getDepositPriceBySchoolApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
//        $this->ThisVerify($request);
        $Model = new \Model\Smc\StuBalanceModel($request);
        $res = $Model->getDepositPriceBySchool($request);
        $field = array();

        $k = 0;
        $field[$k]["fieldstring"] = "company_id";
        $field[$k]["fieldname"] = "集团ID";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "coursetype_id";
        $field[$k]["fieldname"] = "班组id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "coursecat_id";
        $field[$k]["fieldname"] = "班种id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "deposit_id";
        $field[$k]["fieldname"] = "定金id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "deposit_price";
        $field[$k]["fieldname"] = "定金充值金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;
        if ($res) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $res['allnum'] = 0;
            $result["list"] = $res;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //定金充值
    function stuDepositChargeAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\StuBalanceModel($request);

        $order_pid = $Model->stuDepositChargeOne($request);

        if ($request['price'] <= 0) {
            $res = array('error' => 1, 'errortip' => '请选择正确的金额', 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        if ($request['coursetype_id'] <= 0) {
            $res = array('error' => 1, 'errortip' => '请选择正确的班组', 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        if ($order_pid) {
            $res = array('error' => 0, 'errortip' => '定金充值订单成功', 'result' => $order_pid);
        } else {
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => array());
        }
        ajax_return($res, $request['language_type']);

    }
}
