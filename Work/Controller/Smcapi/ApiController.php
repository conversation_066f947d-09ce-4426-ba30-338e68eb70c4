<?php

namespace Work\Controller\Smcapi;


class ApiController extends viewTpl
{
    public $u;
    public $t;
    public $c;

    function __construct()
    {
        // 指定允许其他域名访问
        header('Access-Control-Allow-Origin:*');
        // 响应类型
        header('Access-Control-Allow-Methods:*');
        // 响应头设置
        header('Access-Control-Allow-Headers:x-requested-with,content-type');
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
    }

    //获取报名缴费二维码
    function getSignUpPayCodeApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        if (!isset($request['school_id']) || $request['school_id'] == '') {
            $res = array('error' => 0, 'errortip' => '学校id丢失', 'result' => '');
            ajax_return($res, $request['language_type']);
        }

        if (!isset($request['companies_id']) || $request['companies_id'] == '') {
            $res = array('error' => 0, 'errortip' => '主体id丢失', 'result' => '');
            ajax_return($res, $request['language_type']);
        }

        $schoolOne = $this->DataControl->selectOne("select s.school_id,s.school_branch,s.school_shortname,s.school_cnname,c.company_id,c.company_code,c.company_cnname,c.company_shortname 
                    from smc_school as s 
                    LEFT JOIN gmc_company as c ON s.company_id=c.company_id
                    where s.school_id = '{$request['school_id']}' limit 0,1");

        $urlstring = "";
        $urlstring .= ($schoolOne['school_branch'] == '') ? '' : "school_branch={$schoolOne['school_branch']}";
        $urlstring .= ($request['price'] == '') ? '' : "&price={$request['price']}";
        $urlstring .= ($request['companies_id'] == '') ? '' : "&companies_id={$request['companies_id']}";

        if (isset($request['isoldchild']) && $request['isoldchild'] == 1) {
            $url = "https://{$schoolOne['company_id']}.scshop.kedingdang.com/paymentCenter/oldChild?{$urlstring}";
        } else {
            $url = "https://{$schoolOne['company_id']}.scshop.kedingdang.com/paymentCenter/newChild?{$urlstring}";
        }

        $signupcode = $activityOne['activity_appSchoolUrl'] = "https://smcapi.kedingdang.com/Api/geturlshowimg?imgurl=" . base64_encode($url);//二维码
        if ($url) {
            $result["list"] = $url;
            $res = array('error' => 0, 'errortip' => '二维码获取成功', 'result' => $signupcode);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '二维码获取失败', 'result' => '');
        }
        ajax_return($res, $request['language_type']);
    }

    function geturlshowimgView()
    {
        header('Content-Type:image/png');
        $request = Input('get.', '', 'trim,addslashes');
        $codeUrl = base64_decode($request['imgurl']);//."&school_id={$request['school_id']}&typ=0"
        require(ROOT_PATH . 'Core/Classlib/Webqrcode.php');//二维码生成
        $QRcode = new \QRcode();
        $errorCorrectionLevel = 'H';//容错级别
        $matrixPointSize = 15;//生成图片大小
        echo $QRcode->png($codeUrl, false, $errorCorrectionLevel, $matrixPointSize, 2);
    }


    function getFamilyRelationApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $RelationList = $this->DataControl->selectClear("select familyrelation_code,familyrelation_name from smc_code_familyrelation");
        if ($RelationList) {
            $result["list"] = $RelationList;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '获取信息', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function getTeacherApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $datawhere = "sp.company_id='{$request['company_id']}' and sp.school_id='{$request['school_id']}' and cp.post_isteaching=1 and s.staffer_leave = 0";
        if (isset($request['mainIds']) && $request['mainIds'] !== '') {
            $datawhere .= " and s.staffer_id not in ({$request['mainIds']})";
        }

        if (isset($request['auxiliaryIds']) && $request['auxiliaryIds'] !== '') {
            $datawhere .= " and s.staffer_id not in ({$request['auxiliaryIds']})";
        }

        $TeacherList = $this->DataControl->selectClear("select s.staffer_id,s.staffer_cnname,staffer_enname,s.staffer_branch,si.info_nation,cp.post_name
                                                      from smc_staffer as s
                                                      left join gmc_staffer_postbe as sp on s.staffer_id=sp.staffer_id
                                                      left join smc_staffer_info as si on si.staffer_id=s.staffer_id
                                                      left join gmc_company_post as cp on cp.post_id=sp.post_id
                                                      where {$datawhere}
                                                      group by s.staffer_id");
        if ($TeacherList) {
            foreach ($TeacherList as &$teacherOne) {
                $teacherOne['staffer_cnname'] = $teacherOne['staffer_enname'] ? $teacherOne['staffer_cnname'] . '-' . $teacherOne['staffer_enname'] : $teacherOne['staffer_cnname'];
            }


            $result["list"] = $TeacherList;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '获取信息', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function getCourseInfoApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);

        $datawhere = "";
        if ($request['staffer_id'] == '27550') {
            $datawhere .= " and c.coursecat_id in(133,141,11352) ";
        }

        $CourseList = $this->DataControl->selectClear("SELECT c.course_id,c.course_cnname,c.course_branch
            FROM smc_course AS c
			LEFT JOIN smc_fee_pricing AS fp ON c.course_id=fp.course_id
			LEFT JOIN smc_fee_agreement AS fa ON fa.agreement_id=fp.agreement_id AND fa.agreement_status = 1
            WHERE c.company_id = '{$request['company_id']}' 
            AND c.course_status = '1' 
            {$datawhere}
            GROUP BY c.course_id");

        if ($CourseList) {
            $result["list"] = $CourseList;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无课程信息', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function getStafferSchoolApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $sql = "select sc.school_id,(case when sc.school_shortname='' then sc.school_cnname else sc.school_shortname end) as school_cnname,sc.school_branch
              from smc_class_teach as ct
              inner join smc_class as cl on cl.class_id=ct.class_id
              inner join smc_school as sc on sc.school_id=cl.school_id
              where staffer_id='{$request['worker_id']}'
              group by sc.school_id
              ";

        $schoolList = $this->DataControl->selectClear($sql);

        if ($schoolList) {
            $result["list"] = $schoolList;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无学校信息', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function getClassroomApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $classroomList = $this->DataControl->selectClear("select c.classroom_id,c.classroom_branch,c.classroom_cnname from smc_classroom as c
where c.company_id='{$request['company_id']}' and c.school_id='{$request['school_id']}' and classroom_status = '1'");
        if ($classroomList) {
            $result["list"] = $classroomList;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '获取信息', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    function getCourseApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);

        $datawhere = " c.company_id='{$request['company_id']}' ";
        if (isset($request['coursetype_id']) and $request['coursetype_id'] != '') {
            $datawhere .= " and c.coursetype_id='{$request['coursetype_id']}'";
        }

        if (isset($request['coursecat_id']) and $request['coursecat_id'] != '') {
            $datawhere .= " and c.coursecat_id='{$request['coursecat_id']}'";
        }

        if (isset($request['course_inclasstype']) and $request['course_inclasstype'] != '') {
            $datawhere .= " and c.course_inclasstype='{$request['course_inclasstype']}'";
        }

        if ($request['from'] != '1') {
            $datawhere .= " and c.course_id = cs.course_id and cs.class_status <> '-2'";
            if (isset($request['school_id']) and $request['school_id'] != '') {
                $datawhere .= " and cs.school_id='{$request['school_id']}'";
            }

            $sql = "select c.course_id,c.course_cnname,c.course_branch,c.coursetype_id,c.coursecat_id
              from smc_course as c,smc_class as cs
              where {$datawhere}";
            $sql .= " and c.course_id in (select course_id from smc_class where company_id='{$request['company_id']}' and school_id='{$request['school_id']}' group by course_id)";
        } else {
            $sql = "select c.course_id,c.course_cnname,c.course_branch,c.coursetype_id,c.coursecat_id
              from smc_course as c
              where {$datawhere}";
        }

        $sql .= " GROUP BY c.course_id ORDER BY c.coursetype_id ASC,c.coursecat_id ASC";

        $courseList = $this->DataControl->selectClear($sql);
        if ($courseList) {
            $result["list"] = $courseList;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '获取信息', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    //创建班级时 获取课程
    function getCreateClassCourseApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $now = date("Y-m-d", time());
        $datawhere = " 1 ";
        if (isset($request['coursetype_id']) and $request['coursetype_id'] != '') {
            $datawhere .= " and c.coursetype_id='{$request['coursetype_id']}'";
        }

        if (isset($request['coursecat_id']) and $request['coursecat_id'] != '') {
            $datawhere .= " and c.coursecat_id='{$request['coursecat_id']}'";
        }

        if (isset($request['course_openclasstype']) and $request['course_openclasstype'] != '') {
            $datawhere .= " and c.course_openclasstype='{$request['course_openclasstype']}'";
        }
        if($request['course_isprepare'] == '1'){//获取班别开放了预备班的
            $datawhere .= " and c.course_isprepare='1'";
        }

        //默认查询允许建班的，超哥查了只有 建班的地方用到了这个接口
        $datawhere .= " and c.course_isbuildclass='1'";

        $contractOne = $this->getContract($request['company_id']);

        if ($contractOne && $contractOne['edition_id'] == '2') {
            $sql = "select c.course_id,c.course_cnname,c.course_branch,c.coursetype_id,c.coursecat_id
                  from smc_course as c,smc_code_coursecat as cc
                  where {$datawhere} and c.coursecat_id=cc.coursecat_id and c.company_id='{$request['company_id']}' 
                  and cc.coursecat_isfree=1  and c.course_inclasstype='{$request['course_inclasstype']}'
                  group by c.course_id
                  ";
            $courseList = $this->DataControl->selectClear($sql);
        } else {
            if (isset($request['course_inclasstype']) and $request['course_inclasstype'] != '') {
                $datawhere .= " and c.course_inclasstype='{$request['course_inclasstype']}'";
            }

            $sql = "SELECT
                        t.course_id,
                        c.course_cnname,
                        c.course_branch,
                        c.coursetype_id,
                        c.coursecat_id,
                        c.course_openclasstype
                    FROM
                        smc_fee_pricing_tuition AS t,
                        smc_fee_pricing AS p,
                        smc_fee_agreement AS a,
                        smc_course as c
                    WHERE
                        {$datawhere}
                    AND t.pricing_id = p.pricing_id
                    AND p.agreement_id = a.agreement_id
                    AND c.course_id = t.course_id
                    AND (
                        (
                            p.pricing_applytype = '1'
                            AND p.pricing_id IN (
                                SELECT
                                    pricing_id
                                FROM
                                    smc_fee_pricing_apply AS a
                                WHERE
                                    a.school_id = '{$request['school_id']}'
                            )
                        )
                        OR (
                            p.pricing_applytype = '-1'
                            AND p.pricing_id NOT IN (
                                SELECT
                                    pricing_id
                                FROM
                                    smc_fee_pricing_apply AS a
                                WHERE
                                    a.school_id = '{$request['school_id']}'
                            )
                        )
                        OR (p.pricing_applytype = '0')
                    )
                    AND a.agreement_startday <= '{$now}'
                    AND a.agreement_endday >= '{$now}'
                    AND a.agreement_status = '1'
                    AND a.company_id = '{$request['company_id']}'
                    AND c.course_status<>'-1'
                    GROUP BY
                        t.course_id
                    ";
            $temArray = $this->DataControl->selectClear($sql);

            $courseList = $temArray;
            if (!$courseList) {
                $courseList = array();
            }
            if (isset($request['course_inclasstype']) and $request['course_inclasstype'] == '3') {
                $sql = "select c.course_id,c.course_cnname,c.course_branch,c.coursetype_id,c.coursecat_id,c.course_openclasstype  
                      from smc_course as c
                      left join smc_code_coursetype as cc ON c.coursetype_id = cc.coursetype_id
                      where cc.coursetype_isopenclass =1 and {$datawhere} and cc.company_id= '{$request['company_id']}' 
                      group by c.course_id";

                $pucCourseList = $this->DataControl->selectClear($sql);


                if (!$pucCourseList) {
                    $pucCourseList = array();
                }
                $courseList = array_unique(array_merge($courseList, $pucCourseList), SORT_REGULAR);
            }
        }

        if ($courseList) {

            $result["list"] = $courseList;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
            ajax_return($res, $request['language_type']);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '获取信息', 'result' => $result);
            ajax_return($res, $request['language_type']);
        }

    }

    //二位数组去重
    function assoc_unique($arr, $key)
    {
        $tmp_arr = array();
        foreach ($arr as $k => $v) {
            if (in_array($v[$key], $tmp_arr)) {
                unset($arr[$k]);
            } else {
                $tmp_arr[] = $v[$key];
            }
        }
        sort($arr); //sort函数对数组进行排序
        return $arr;
    }


    function getClassApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $classList = $this->DataControl->selectClear("select c.class_id,c.class_cnname,c.class_enname,c.class_branch from smc_class as c
where c.company_id='{$request['company_id']}' and c.school_id='{$request['school_id']}' and c.class_status<>'-2' group by c.class_id");
        if ($classList) {
            $result["list"] = $classList;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '获取信息', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    //结转时获取班级下拉
    function getCarrayClassApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        if (!$request['class_id']) {
            $res = array('error' => 1, 'errortip' => "classId缺失", 'result' => array());
            ajax_return($res, $request['language_type']);
        }

        $stuCourse = $this->DataControl->getFieldOne('smc_class', 'class_id,course_id', "class_id='{$request['class_id']}'");
        $stuBalance = $this->DataControl->getFieldOne("smc_student_coursebalance", 'coursebalance_time', "student_id='{$request['student_id']}' and course_id='{$stuCourse['course_id']}' ");
        $sql = "select c.class_id,c.class_cnname,
                (select count(h.hour_id) from  smc_class_hour as h  where h.class_id=c.class_id and h.course_id = c.course_id  ) as hour_num
                from smc_class as c
                where  c.company_id='{$request['comapny_id']}' and c.school_id='{$request['school_id']}' and c.course_id='{$stuCourse['course_id']}' and c.class_id <>'{$stuCourse['class_id']}'
                HAVING hour_num < '{$stuBalance['coursebalance_time']}'";
        $classList = $this->DataControl->selectClear($sql);

        if (!$classList) {
            $classList = array();
        }
        $res = array('error' => 0, 'errortip' => "获取成功", 'result' => $classList);
        ajax_return($res, $request['language_type']);


    }


    function getChangeClassApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $classList = $this->DataControl->selectClear("select sc.class_id,c.class_cnname,c.class_branch,c.class_enname
                from smc_class as c
                left join smc_student_changelog as sc on c.class_id=sc.class_id
                WHERE sc.school_id='{$request['school_id']}' and sc.company_id='{$request['company_id']}' group by sc.class_id");
        if ($classList) {
            $result["list"] = $classList;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '获取信息', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    function courseTypeApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $datawhere = '1';
//        阅读类
        if (isset($request['from']) && $request['from'] == "courseterm") {
            $datawhere .= " and sc.course_inclasstype ='2'";
        }
        //期度
        if (isset($request['from']) && $request['from'] == "moth") {
            $datawhere .= " and sc.course_inclasstype ='1'";
        }

        if (isset($request['course_inclasstype']) && $request['course_inclasstype'] != "") {
            $datawhere .= " and sc.course_inclasstype ='{$request['course_inclasstype']}'";
        }

        if ($request['staffer_id'] == '27550') {
            $datawhere .= " and sc.coursecat_id in(133,141,11352) ";
        }

        $now = date("Y-m-d", time());

        $contractOne = $this->getContract($request['company_id']);

        if ($contractOne && $contractOne['edition_id'] == '2') {
            if($request['course_isprepare'] == '1'){//获取班别开放了预备班的 班组
                $sql = "select c.* 
                  from smc_code_coursetype as c,smc_code_coursecat as cc,smc_course as u 
                  where c.company_id='{$request['company_id']}'  AND c.coursetype_isopenclass  = 0  
                  and c.coursetype_id=cc.coursetype_id and cc.coursecat_isfree=1 
                  and c.coursetype_id = u.coursetype_id and u.course_isprepare = '1'
                  group by c.coursetype_id
                  ";
                $list = $this->DataControl->selectClear($sql);
            }else{
                $sql = "select c.* 
                  from smc_code_coursetype as c,smc_code_coursecat as cc
                  where c.coursetype_id=cc.coursetype_id and c.company_id='{$request['company_id']}' 
                  and cc.coursecat_isfree=1  AND c.coursetype_isopenclass  = 0
                  group by c.coursetype_id
                  ";
                $list = $this->DataControl->selectClear($sql);
            }
        } else {
            if($request['course_isprepare'] == '1') {//获取班别开放了预备班的 班组
                $datawhere .= " and sc.course_isprepare = '1' ";
            }

            $sql = " SELECT
                p.course_id,
                c.*
            FROM
                smc_fee_pricing AS p,
                smc_fee_agreement AS a,
                smc_course as sc,
                smc_code_coursetype as c
            WHERE
                {$datawhere}
            AND c.coursetype_isopenclass  = 0
            AND p.agreement_id = a.agreement_id
            AND sc.course_id = p.course_id
            AND c.coursetype_id = sc.coursetype_id
            AND (
                (
                    p.pricing_applytype = '1'
                    AND p.pricing_id IN (
                        SELECT
                            pricing_id
                        FROM
                            smc_fee_pricing_apply AS a
                        WHERE
                            a.school_id = '{$request['school_id']}'
                    )
                )
                OR (
                    p.pricing_applytype = '-1'
                    AND p.pricing_id NOT IN (
                        SELECT
                            pricing_id
                        FROM
                            smc_fee_pricing_apply AS a
                        WHERE
                            a.school_id = '{$request['school_id']}'
                    )
                )
                OR (p.pricing_applytype = '0')
            )
            AND a.agreement_startday <= '{$now}'
            AND a.agreement_endday >= '{$now}'
            AND a.agreement_status = '1'
            and sc.course_status <>'-1'
            AND a.company_id = '{$request['company_id']}'
          
            GROUP BY
                c.coursetype_id
                order by c.coursetype_id asc
                ";
            $list = $this->DataControl->selectClear($sql);
            if (!$list) {
                $list = array();
            }
            if (isset($request['course_inclasstype']) && $request['course_inclasstype'] != 3) {
            } else {
                $openList = $this->DataControl->selectClear("select cc.* from  smc_code_coursetype as cc where cc.coursetype_isopenclass =1 and cc.company_id = '{$request['company_id']}' ");

                if (!$openList) {
                    $openList = array();
                }
                $list = array_merge($list, $openList);
            }
        }
        if ($list) {
            $result["list"] = $list;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '无班组信息,获取失败', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    function courseCatApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $datawhere = " 1 ";
        if (isset($request['coursetype_id']) and $request['coursetype_id'] != '') {
            $datawhere .= " and c.coursetype_id='{$request['coursetype_id']}'";
        }
        if (isset($request['coursetype_id']) and $request['coursetype_id'] != '') {
            $datawhere .= " and c.coursetype_id='{$request['coursetype_id']}'";
        }
        if (isset($request['coursetype_array_id']) and $request['coursetype_array_id'] != '') {
            $coursetype_array = json_decode(stripslashes($request['coursetype_array_id']), 1);
            if (is_array($coursetype_array) && count($coursetype_array) > 0) {
                $string = implode($coursetype_array, ',');
                $datawhere .= " and c.coursetype_id in ({$string})";
            }
        }

        if ($request['staffer_id'] == '27550') {
            $datawhere .= " and sc.coursecat_id in(133,141,11352) ";
        }

        if (isset($request['keyword']) && $request['keyword'] != '') {
            $datawhere .= " and (c.coursecat_cnname like '%{$request['keyword']}%'  )";
        }
        // 公开课不需要协议
        if ($request['from'] == '1' || (isset($request['course_inclasstype']) && $request['course_inclasstype'] == '3')) {
            if (isset($request['course_inclasstype']) && $request['course_inclasstype'] != '') {
                $datawhere .= " and ty.coursetype_isopenclass ='1'";
            }
            $sql = "select c.* from smc_code_coursecat as c,smc_code_coursetype as ty where c.coursetype_id =ty.coursetype_id and  {$datawhere} and c.company_id='{$request['company_id']}'";
            $list = $this->DataControl->selectClear($sql);
        } else {
            $contractOne = $this->getContract($request['company_id']);

            if ($contractOne && $contractOne['edition_id'] == '2') {
                if($request['course_isprepare'] == '1'){//获取班别开放了预备班的 班组
                    $sql = "select cc.* 
                  from smc_code_coursecat as cc,smc_course as sc
                  where cc.coursecat_id=sc.coursecat_id and cc.company_id='{$request['company_id']}' 
                  and cc.coursecat_isfree=1 and sc.course_inclasstype=0 and sc.course_isprepare = '1'
                  group by cc.coursecat_id
                  ";
                }else {
                    $sql = "select cc.* 
                  from smc_code_coursecat as cc,smc_course as sc
                  where cc.coursecat_id=sc.coursecat_id and cc.company_id='{$request['company_id']}' 
                  and cc.coursecat_isfree=1 and sc.course_inclasstype=0
                  group by cc.coursecat_id
                  ";
                }
            } else {
                //阅读班
                if (isset($request['from']) && $request['from'] == "courseterm") {
                    $datawhere .= " and sc.course_inclasstype ='2'";
                }
                //期度
                if (isset($request['from']) && $request['from'] == "moth") {
                    $datawhere .= " and sc.course_inclasstype ='1'";
                }

                if (isset($request['course_inclasstype']) && $request['course_inclasstype'] != "") {
                    $datawhere .= " and sc.course_inclasstype ='{$request['course_inclasstype']}'";
                }
                if($request['course_isprepare'] == '1') {//获取班别开放了预备班的 班组
                    $datawhere .= " and sc.course_isprepare = '1' ";
                }
                $now = date("Y-m-d", time());
                $sql = " SELECT 
                p.course_id,
                c.*
            FROM
                smc_fee_pricing AS p,
                smc_fee_agreement AS a,
                smc_course as sc,
                smc_code_coursecat as c
            WHERE
                {$datawhere}
            AND p.agreement_id = a.agreement_id
            AND sc.course_id = p.course_id
            AND c.coursecat_id = sc.coursecat_id
            AND (
                (
                    p.pricing_applytype = '1'
                    AND p.pricing_id IN (
                        SELECT
                            pricing_id
                        FROM
                            smc_fee_pricing_apply AS a
                        WHERE
                            a.school_id = '{$request['school_id']}'
                    )
                )
                OR (
                    p.pricing_applytype = '-1'
                    AND p.pricing_id NOT IN (
                        SELECT
                            pricing_id
                        FROM
                            smc_fee_pricing_apply AS a
                        WHERE
                            a.school_id = '{$request['school_id']}'
                    )
                )
                OR (p.pricing_applytype = '0')
            )
            AND a.agreement_startday <= '{$now}'
            AND a.agreement_endday >= '{$now}'
            AND a.agreement_status = '1'
            and sc.course_status <>'-1'
            AND a.company_id = '{$request['company_id']}'
            GROUP BY
                c.coursecat_id
                order by c.coursecat_id asc
                ";
            }

            $str_coursecat_id = "0";
            $list = $this->DataControl->selectClear($sql);
//            if (!$list) {
//                $list = array();
//            } else {
//                $arr_coursecat_id = array_column($list, 'coursecat_id');
//                $str_coursecat_id = implode(',', $arr_coursecat_id);
//            }
//            $catwhere = "1";
//            if (isset($request['coursetype_id']) and $request['coursetype_id'] != '') {
//                $catwhere .= " and cc.coursetype_id = '{$request['coursetype_id']}' ";
//            }
//            if (isset($request['coursetype_array_id']) and $request['coursetype_array_id'] != '') {
//                $coursetype_array = json_decode(stripslashes($request['coursetype_array_id']), 1);
//                if (is_array($coursetype_array) && count($coursetype_array) > 0) {
//                    $string = implode($coursetype_array, ',');
//                    $catwhere .= " and cc.coursetype_id in ({$string})";
//                }
//            }
//            $openList = $this->DataControl->selectClear("select cc.* from  smc_code_coursecat as cc where  $catwhere and cc.company_id = '{$request['company_id']}' and  cc.coursecat_id not in ({$str_coursecat_id}) ");
//            if (!$openList) {
//                $openList = array();
//            }
//            $list = array_merge($list, $openList);
        }

        if ($list) {
            $result["list"] = $list;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '无班种信息,获取失败', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function changeTypeApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);

        $datawhere = ' where 1 ';
        //可能需要加个版本异动匹配表
        $contractOne = $this->getContract($request['company_id']);
        if ($contractOne && $contractOne['edition_code'] == 'freesimply') {
            $datawhere .= " and stuchange_code in('A01','A02','A03','B01','B02','C02','D02')";
        }

        $list = $this->DataControl->selectClear("select stuchange_code,stuchange_name from smc_code_stuchange {$datawhere}");
        if ($list) {
            $result["list"] = $list;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '获取信息', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function payTypeApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $list = $this->DataControl->selectClear("select v.list_name,v.list_parameter from cms_variablelist as v where v.variable_id=1");
        if ($list) {
            $result["list"] = $list;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '获取信息', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function getSchoolCompaniesListApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);

        $datawhere = "";
        if ($request['staffer_id'] == '27550') {
            $datawhere .= " and sc.companies_id in(8,78606) ";
        }

//        if ($request['staffer_id'] == '25721') {
//            $datawhere .= " and sc.companies_id in(0) ";
//        }

        if ($request['type'] == 'all') {
            $sql = "select sc.companies_id,cc.companies_cnname,cc.companies_issupervise
                from smc_student_trading sc
                left join gmc_code_companies cc on sc.companies_id=cc.companies_id
                WHERE 1 {$datawhere} 
                and sc.school_id = '{$request['school_id']}'
                group by sc.companies_id ";
        } else {
            $sql = "select sc.companies_id,cc.companies_cnname,cc.companies_issupervise
                FROM smc_school_companies as sc,gmc_code_companies as cc 
                where sc.companies_id=cc.companies_id 
                {$datawhere}
                and sc.school_id='{$request['s_school_id']}'
                ";
        }
        $list = $this->DataControl->selectClear($sql);

        if ($list) {
            $result["list"] = $list;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '获取信息', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function trackTypeApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $sql = "select tracktype_id,tracktype_name,if(tracktype_name='分校流失电访',1,0) as is_show FROM smc_code_tracktype 
                where company_id = '{$request['company_id']}' 
                and tracktype_smc=1
                ORDER BY tracktype_id ASC";
        $list = $this->DataControl->selectClear($sql);
        if ($list) {
            $result["list"] = $list;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '获取信息', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function trackLinkTypeApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $list = $this->DataControl->selectClear("select c.commode_id,c.commode_name from crm_code_commode as c where c.company_id='{$request['company_id']}' order by c.commode_id asc");
        if ($list) {
            $result["list"] = $list;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '获取信息', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function CatitrackCodeApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $list = $this->DataControl->selectClear("select c.object_code,c.object_name from crm_code_object as c where 1 = 1 ");
        if ($list) {
            $result["list"] = $list;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '获取信息', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function trackResultTypeApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $list = $this->DataControl->selectClear("select trackresult_id,trackresult_name 
                FROM smc_code_trackresult 
                where company_id = '{$request['company_id']}' 
                and tracktype_id = '{$request['tracktype_id']}' 
                ORDER BY trackresult_id ASC");
        if ($list) {
            $result["list"] = $list;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '获取信息', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function fileTypeApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $list = $this->DataControl->selectClear("select sf.file_format from smc_student_file as sf
where sf.company_id='{$request['company_id']}' and sf.student_id='{$request['student_id']}' group by sf.file_format");
        if ($list) {
            foreach ($list as $key => $val) {
                if ($val['file_format'] == '') {
                    unset($list[$key]);
                }
            }
            $result["list"] = $list;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '获取信息', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function getStuchangeReasonListApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $datawhere = "sr.company_id='{$request['company_id']}'";
        if (isset($request['stuchange_code']) && $request['stuchange_code'] != '') {
            $datawhere .= " and sr.stuchange_code='{$request['stuchange_code']}'";
        }

        $list = $this->DataControl->selectClear("select * from smc_code_stuchange_reason as sr where {$datawhere}");
        if ($list) {
            $result["list"] = $list;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '获取信息', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function getStuchangeCategoryListApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);

        $datawhere = " 1 ";//        $datawhere = "sr.company_id='{$request['company_id']}'";
        if (isset($request['stuchange_code']) && $request['stuchange_code'] != '') {
            $datawhere .= " and sr.stuchange_code='{$request['stuchange_code']}'";
        }

        $list = $this->DataControl->selectClear("select * from smc_code_stuchange_category as sr where {$datawhere}");
        if ($list) {
            $result["list"] = $list;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '获取信息', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function getFeetypeListApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $list = $this->DataControl->selectClear("select * from smc_code_feetype");
        if ($list) {
            $result["list"] = $list;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '获取信息', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }


    function getCoursetimesListApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);

        $list = array();

        $course = $this->DataControl->getFieldOne("smc_course", "course_islimittime,course_limittime,course_weeklimittime"
            , " course_id=(select course_id from smc_class where class_id='{$request['class_id']}' limit 0,1)");
        if ($course && $course['course_islimittime'] == '1') {
            $course_limittime = explode(',', $course['course_limittime']);
            foreach ($course_limittime as $key => $one) {
                $list[$key]['coursetimes_nums'] = $one;
            }
        } else {
            $list = $this->DataControl->selectClear("select sr.coursetimes_nums from smc_code_coursetimes as sr where sr.company_id='{$request['company_id']}'");
        }
        if ($list) {
            $result["list"] = $list;
            $result["week"] = $course['course_weeklimittime'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["week"] = null;
            $res = array('error' => 1, 'errortip' => '获取信息', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }


    function getCourseClassListApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $data = array();
        $data[0]['coursebalance_class'] = 0;
        $data[0]['name'] = "未入班课程";

        $data[1]['coursebalance_class'] = 1;
        $data[1]['name'] = "已入班课程";

        $data[2]['coursebalance_class'] = 2;
        $data[2]['name'] = "延班课程";

        $data[3]['coursebalance_class'] = 3;
        $data[3]['name'] = "已结转课程";

        if ($data) {
            $result["list"] = $data;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '获取信息', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }


    //班级列表
    function getClassListApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $datawhere = "1";

        if (isset($request['keyword']) && $request['keyword'] != '') {
            $datawhere .= " and (c.class_cnname like '%{$request['keyword']}%' or class_enname like '%{$request['keyword']}%' or class_branch like '%{$request['keyword']}%' )";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;
        $datawhere .= " and c.class_status<>'-2'";
        $classList = $this->DataControl->selectClear("select c.class_id,c.class_cnname,c.class_enname,c.class_branch from smc_class as c where c.company_id='{$request['company_id']}' and c.school_id='{$request['school_id']}' and {$datawhere} group by c.class_id limit {$pagestart},{$num}");

        $allnum = 0;
        if (isset($request['is_count']) && $request['is_count'] == 1) {
            $classNum = $this->DataControl->selectClear("select c.class_id from smc_class as c where c.company_id='{$request['company_id']}' and c.school_id='{$request['school_id']}' and {$datawhere} group by c.class_id");

            if ($classNum) {
                $allnum = count($classNum);
            } else {
                $allnum = '0';
            }
        }


        $fieldstring = array('class_id', 'class_cnname', 'class_enname', 'class_branch');
        $fieldname = array('班级id', '班级名称', '班级别名', '班级编号');
        $fieldcustom = array('0', '1', '1', '1');
        $fieldshow = array('0', '1', '1', '1');

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["fieldname"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }
        $result['field'] = $field;
        if ($classList) {
            $result["list"] = $classList;

            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result, 'allnum' => $allnum);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无班级信息', 'result' => $result, 'allnum' => '0');
        }
        ajax_return($res, $request['language_type']);
    }

    //pad  获取教师的对应的班级
    function getTeaClassListApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
//		$datawhere = "1";
        $classList = $this->DataControl->selectClear("select  DISTINCT  ct.class_id,ct.staffer_id,c.class_cnname,c.class_enname from smc_class_hour_teaching  as ct
 			left join  smc_class as c On c.class_id = ct.class_id
 			where ct.staffer_id='{$request['staffer_id']}' and ct.teaching_type =0 ");
        if (!$classList) {
            $classList = array();
        }

        $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $classList);
        ajax_return($res, $request['language_type']);
    }


    //预约班级
    function getCoursetermCourseApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $courseList = $this->DataControl->selectClear("select  c.course_id,c.course_branch,c.course_cnname  from smc_course as c
where  c.company_id='{$request['company_id']}' and  c.course_inclasstype = 2");

        if ($courseList) {
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $courseList);

        } else {
            $res = array('error' => 1, 'errortip' => '获取信息', 'result' => array());
        }
        ajax_return($res, $request['language_type']);
    }


    function getOutclasstypeApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);

        $sql = "select outclasstype_id,outclasstype_name,(case outclasstype_code when 0 then '教学时数' else '其它时数' end) as outclasstype_code
              from smc_code_outclasstype
              where company_id='{$request['company_id']}'
              ";
        $courseList = $this->DataControl->selectClear($sql);

        if ($courseList) {
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $courseList);

        } else {
            $res = array('error' => 1, 'errortip' => '获取信息', 'result' => array());
        }
        ajax_return($res, $request['language_type']);
    }

    function getSubtypeListApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);

        $sql = "select * from smc_code_subtype 
              where company_id='{$request['company_id']}'
              ";
        $list = $this->DataControl->selectClear($sql);

        if ($list) {
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $list);

        } else {
            $res = array('error' => 1, 'errortip' => '获取信息', 'result' => array());
        }
        ajax_return($res, $request['language_type']);
    }


    function getSchoolClassCourseApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $datawhere = " 1 ";

        if (isset($request['school_id']) && $request['school_id'] != '') {
            $datawhere .= " and cl.school_id='{$request['school_id']}'";
        }

        if (isset($request['course_id']) && $request['course_id'] != '') {
            $datawhere .= " and co.course_id='{$request['course_id']}'";
        }
        if (isset($request['coursetype_id']) && $request['coursetype_id'] != '') {
            $datawhere .= " and co.coursetype_id='{$request['coursetype_id']}'";
        }
        if (isset($request['coursecat_id']) && $request['coursecat_id'] != '') {
            $datawhere .= " and co.coursecat_id='{$request['coursecat_id']}'";
        }
        if (isset($request['course_inclasstype']) && $request['course_inclasstype'] != '') {
            $datawhere .= " and co.course_inclasstype='{$request['course_inclasstype']}'";
            if ($request['course_inclasstype'] == 3) {
                $datawhere .= " and cc.coursetype_isopenclass=1";
            }

        }

        $sql = "select co.course_id,co.course_cnname,co.course_branch,co.coursetype_id,co.coursecat_id
              from smc_class as cl,smc_course as co,smc_code_coursetype as cc 
              where {$datawhere} and cl.course_id=co.course_id and cc.coursetype_id=co.coursetype_id and cl.company_id='{$request['company_id']}'
              group by co.course_id
              order by co.course_id asc
              ";
        $courseList = $this->DataControl->selectClear($sql);

        if ($courseList) {
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $courseList);

        } else {
            $res = array('error' => 1, 'errortip' => '获取信息', 'result' => array());
        }
        ajax_return($res, $request['language_type']);
    }

    function getOrderTradingtypeCodeApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $codeList = $this->DataControl->selectClear("select tradingtype_code,tradingtype_name from smc_code_tradingtype where tradingtype_code<>'Subscribed' and tradingtype_code<>'MonthlyShare'");
        $result = array();
        if ($codeList) {
            $result["list"] = $codeList;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);

        } else {
            $res = array('error' => 1, 'errortip' => '获取信息', 'result' => $result);
        }
        ajax_return($res, $request['language_type'], 1);

    }


    //查询退款订单 //app 必须先搜所关键字
    function getRefundListView()
    {

        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $datawehre = "1";
        if (isset($request['keyword']) && $request['keyword'] != "") {
            $datawehre .= " and (po.order_pid  like  '%{$request['keyword']}%' or s.student_cnname like '%{$request['keyword']}%' or s.student_enname like  '%{$request['keyword']}%' or s.student_branch like  '%{$request['keyword']}%'   )   ";
        } else {
            $res = array('error' => 0, 'errortip' => '请输入关键字', 'result' => array());
            ajax_return($res, $request['language_type']);
        }

        $sql = "select po.order_pid,s.student_cnname,s.student_branch from smc_payfee_order as po
				left join smc_student as s ON s.student_id = po.student_id
			where {$datawehre}";
        $this->DataControl->selectClear($sql);

    }

    //下拉选择集团产品
    function appPropermisApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $datawehre = "company_id ='{$request['company_id']}'";

        $data = $this->DataControl->selectClear("select apppropermis_id,apppropermis_code,apppropermis_name
FROM smc_code_apppropermis  where  {$datawehre}");

        if ($data) {
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $data);

        } else {
            $res = array('error' => 1, 'errortip' => '暂无授权产品', 'result' => array());
        }

        ajax_return($res, $request['language_type']);


    }


    // 下拉 学员在读班级
    function stuReadClassApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $data = $this->DataControl->selectClear("
			select  c.class_id,c.class_cnname,c.class_branch from smc_student_study as ss
			left join smc_class as c  ON ss.class_id = c.class_id
			where  ss.student_id = '{$request['student_id']}' and ss.study_isreading =1");

        if ($data) {
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $data);
        } else {
            $res = array('error' => 1, 'errortip' => '暂无在读班级信息', 'result' => array());
        }

        ajax_return($res, $request['language_type']);


    }

    //	下拉获取教室
    function getSchoolClassroomApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $data = $this->DataControl->selectClear("select classroom_id,classroom_cnname,classroom_branch from smc_classroom
where  school_id='{$request['school_id']}' and classroom_status =1");
        if ($data) {
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $data);
        } else {
            $res = array('error' => 1, 'errortip' => '获取成功', 'result' => array());
        }

        ajax_return($res, $request['language_type']);
    }

    function getSchoolApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $datawhere = "company_id='{$request['company_id']}' and school_id<>'{$request['school_id']}' and school_isclose='0'";
        if (isset($request['student_id']) && $request['student_id'] > 0) {
            $datawhere .= " and school_id not in (select se.school_id from smc_student_enrolled as se where se.student_id='{$request['student_id']}' and (se.enrolled_status=0 or se.enrolled_status=1 or se.enrolled_status=3))";
        }

        $schoolList = $this->DataControl->selectClear("select school_id,school_shortname,school_cnname,school_enname,school_branch from smc_school where {$datawhere} ORDER BY school_sort DESC");
        if ($schoolList) {
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $schoolList);
        } else {
            $res = array('error' => 1, 'errortip' => '无其他学校', 'result' => array());
        }

        ajax_return($res, $request['language_type']);

    }

    function getSchoolAuthorityApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);

        $sql = "select school_iscreateroom 
              from smc_school where school_id='{$request['school_id']}'";

        $schoolOne = $this->DataControl->selectOne($sql);

        if ($schoolOne) {
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $schoolOne);
        } else {
            $res = array('error' => 1, 'errortip' => '无对应学校', 'result' => array());
        }

        ajax_return($res, $request['language_type']);

    }

    function getCompanyAuthorityApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);

        $sql = "select company_cantoother,company_canbatchespay,company_isachieve 
              from gmc_company where company_id='{$request['company_id']}'";

        $schoolOne = $this->DataControl->selectOne($sql);

        if ($schoolOne) {
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $schoolOne);
        } else {
            $res = array('error' => 1, 'errortip' => '无对应集团', 'result' => array());
        }

        ajax_return($res, $request['language_type']);

    }

    //获取集团对应的学校信息
    function getSchoolView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $DataControl = $this->DataControl;
        $datawhere = " 1 ";
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (s.school_branch like '%{$request['keyword']}%' or s.school_shortname like '%{$request['keyword']}%' or s.school_cnname like '%{$request['keyword']}%') ";
        }
        if (isset($request['school_type']) && $request['school_type'] !== '') {
            $datawhere .= " and s.school_type = '{$request['school_type']}'";
        }
        if (isset($request['company_id']) && $request['company_id'] !== '0' && $request['company_id'] !== '') {
            $datawhere .= " and s.company_id = '{$request['company_id']}'";
        } else {
            $res = array('error' => '1', 'errortip' => '请先选择集团');
            ajax_return($res);
        }

        $itemList = $DataControl->selectClear("SELECT s.school_id,s.school_branch,s.school_shortname,s.school_cnname,s.school_type,s.school_address FROM smc_school AS s where {$datawhere} ORDER BY s.school_id ASC");
        if ($itemList) {
            ajax_return($itemList);
        } else {
            $data = array();
            ajax_return($data);
        }
    }


    function getNextCourseApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        if (!isset($request['course_id']) || $request['course_id'] == '') {
            ajax_return(array('error' => 1, 'errortip' => '课程ID必须传', 'result' => array()));
        }

        $now = date("Y-m-d", time());

        $sql = "select course_nextid from smc_course where course_id='{$request['course_id']}' and company_id='{$request['company_id']}'";
        $nextCourse = $this->DataControl->selectOne($sql);

        if ($nextCourse['course_nextid'] !== '') {
            $tem_array = explode(",", $nextCourse['course_nextid']);

            $contractOne = $this->getContract($request['company_id']);

            $data = array();
            foreach ($tem_array as $one) {

                if ($contractOne && $contractOne['edition_id'] == '2') {
                    $sql = "select co.course_id,co.coursetype_id,co.coursecat_id,co.course_branch,co.course_cnname from smc_course as co where co.company_id='{$request['company_id']}' and co.course_id='{$one}'";
                } else {
                    $sql = "SELECT
                        p.course_id,
                        p.pricing_id,
                        c.course_cnname,
                        c.course_branch,
                        c.coursetype_id,
                        c.coursecat_id
                    FROM
                        smc_fee_pricing AS p,
                        smc_fee_agreement AS a,
                        smc_course as c
                    WHERE
                        p.agreement_id = a.agreement_id
                    AND c.course_id = p.course_id
                    AND (
                        (
                            p.pricing_applytype = '1'
                            AND p.pricing_id IN (
                                SELECT
                                    pricing_id
                                FROM
                                    smc_fee_pricing_apply AS a
                                WHERE
                                    a.school_id = '{$request['school_id']}'
                            )
                        )
                        OR (
                            p.pricing_applytype = '-1'
                            AND p.pricing_id NOT IN (
                                SELECT
                                    pricing_id
                                FROM
                                    smc_fee_pricing_apply AS a
                                WHERE
                                    a.school_id = '{$request['school_id']}'
                            )
                        )
                        OR (p.pricing_applytype = '0')
                    )
                    AND a.agreement_startday <= '{$now}'
                    AND a.agreement_endday >= '{$now}'
                    AND a.agreement_status = '1'
                    AND a.company_id = '{$request['company_id']}'
                    AND p.course_id='{$one}'
                    ";
                }


                $courseOne = $this->DataControl->selectOne($sql);
//                $courseOne=$this->DataControl->getFieldOne("smc_course","course_cnname","course_id='{$one}' and company_id='{$request['company_id']}'");
                if ($courseOne) {
                    $data[] = $courseOne;
                }

            }

            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $data);
        } else {
            $res = array('error' => 1, 'errortip' => '无下一级别课程', 'result' => array());
        }

        ajax_return($res, $request['language_type']);

    }

    function getTeachTypeApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);

        $datawhere = " 1 ";
        if (isset($request['teachtype_class']) && $request['teachtype_class'] != '') {
            $datawhere .= " and (teachtype_class='{$request['teachtype_class']}' or teachtype_class=2)";
        }
        if (isset($request['teacher_id']) && $request['teacher_id'] != '') {
            $staffer = $this->DataControl->getFieldOne("smc_staffer", "staffer_native", "staffer_id='{$request['teacher_id']}'");
            if (isset($staffer['staffer_native']) && $staffer['staffer_native'] != '') {
                $datawhere .= " and FIND_IN_SET('{$staffer['staffer_native']}', teachtype_native)";
            }
        }

        $dataList = $this->DataControl->selectClear("select teachtype_code,teachtype_name,teachtype_native from smc_code_teachtype as ct where {$datawhere} and company_id ='{$request['company_id']}'");

        if ($dataList) {
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $dataList);
        } else {
            $res = array('error' => 1, 'errortip' => '获取成功', 'result' => array());
        }

        ajax_return($res, $request['language_type']);
    }

    function getStafferApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);

        $class = $this->DataControl->getFieldOne("smc_class", "class_type", "class_id='{$request['class_id']}'");
        if ($class['class_type'] == 0) {

            $datawhere = "ct.class_id='{$request['class_id']}' and s.company_id='{$request['company_id']}' and ct.teach_status=0 and s.staffer_leave=0";
            if (isset($request['teachtype_code']) && $request['teachtype_code'] != '') {
                $teachtype = $this->DataControl->getFieldOne("smc_code_teachtype", "teachtype_native", "company_id='{$request['company_id']}' and teachtype_code='{$request['teachtype_code']}'");
                if (isset($teachtype['teachtype_native']) && $teachtype['teachtype_native'] !== '') {
                    $datawhere .= " and s.staffer_native in ({$teachtype['teachtype_native']})";
                }
            }
            if (isset($request['teach_type']) && $request['teach_type'] !== '') {
                $datawhere .= " and ct.teach_type = '{$request['teach_type']}'";
            }

            $dataList = $this->DataControl->selectClear("select ct.staffer_id,s.staffer_cnname,s.staffer_enname,s.staffer_native,ct.teach_type from smc_class_teach as ct left join smc_staffer as s on s.staffer_id=ct.staffer_id where {$datawhere}");
            if ($dataList) {
                foreach ($dataList as &$dataOne) {
                    $dataOne['staffer_cnname'] = $dataOne['staffer_enname'] ? $dataOne['staffer_cnname'] . '-' . $dataOne['staffer_enname'] : $dataOne['staffer_cnname'];
                }
            } else {
                $dataList = array();
            }
        } else {
            $datawhere = "sp.company_id='{$request['company_id']}' and sp.school_id='{$request['school_id']}' and cp.post_isteaching=1 and ct.teach_status=0 and s.staffer_leave=0";
            if (isset($request['teachtype_code']) && $request['teachtype_code'] != '') {
                $teachtype = $this->DataControl->getFieldOne("smc_code_teachtype", "teachtype_native", "company_id='{$request['company_id']}' and teachtype_code='{$request['teachtype_code']}'");
                if (isset($teachtype['teachtype_native']) && $teachtype['teachtype_native'] !== '') {
                    $datawhere .= " and s.staffer_native in ({$teachtype['teachtype_native']})";
                }
            }
            if (isset($request['teach_type']) && $request['teach_type'] !== '') {
                $datawhere .= " and ct.teach_type = '{$request['teach_type']}'";
            }

            $sql = "select s.staffer_id,s.staffer_cnname,s.staffer_enname,s.staffer_branch,s.staffer_native,ct.teach_type,cp.post_name
                from smc_class_teach as ct 
                left join smc_staffer as s on s.staffer_id=ct.staffer_id
                left join gmc_staffer_postbe as sp on s.staffer_id=sp.staffer_id
                left join gmc_company_post as cp on cp.post_id=sp.post_id
                where {$datawhere}
                group by s.staffer_id";
            $dataList = $this->DataControl->selectClear($sql);
            if (!$dataList) {
                $dataList = array();
            }
        }

        if ($dataList) {
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $dataList);
        } else {
            $res = array('error' => 1, 'errortip' => '获取成功', 'result' => array());
        }
        ajax_return($res, $request['language_type']);
    }


    private function getDateToken()
    {
        $token = base64_encode(md5(date("Y-m-d") . 'a'));
        return $token;
    }

    /**
     * 获取token
     * author: ling
     * 对应接口文档 0001
     * @return string
     */
    function getDateTokenApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $token = base64_encode(md5(date("Y-m-d") . 'a'));
        $res = array('error' => 0, 'errortip' => '获取token成功', 'result' => array(), 'token' => $token);
        ajax_return($res, $request['language_type']);
    }

    /**
     * 获取单校所有公开课的信息
     *  此接口用于对外使用
     * author: ling
     * 对应接口文档 0001
     */
    function getOpenClassByBranchApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $token = $request['token'];
        $branch = $request['branch'];
        $date = $request['date'];

        if ($token !== $this->getDateToken()) {
            $res = array('error' => 1, 'errortip' => 'token验证失败', 'result' => array());
            ajax_return($res, $request['language_type']);
        }

        if (!$branch) {
            $res = array('error' => 1, 'errortip' => '请选择学校', 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        if ($date === '') {
            $whereDate = date("Y-m-d");
        } else {
            $whereDate = date('Y-m-d', strtotime($date));
        }


        $where = '1';
        if (isset($request['classroom_id']) && $request['classroom_id'] === "") {

            $where .= "  and ch.classroom_id = '{$request['classroom_id']}'";
        }

        $where .= "  and sl.school_branch = '{$branch}' and ch.hour_day ='{$whereDate}' and ct.coursetype_isopenclass =1";


        $sql = "
                select ch.hour_id,cs.class_branch,cs.class_cnname,cs.class_enname,sc.classroom_cnname,sl.school_branch,ch.hour_day,sc.classroom_id
                from smc_class_hour as ch
                left join smc_class as cs On ch.class_id = cs.class_id
                left join smc_course as co ON co.course_id = cs.course_id 
                left join smc_classroom as sc ON sc.classroom_id = ch.classroom_id
                left join smc_code_coursetype as ct ON ct.coursetype_id = co.coursetype_id
                left join smc_school as sl ON sl.school_id = cs.school_id
                where {$where}  
                
            ";

        $dataList = $this->DataControl->selectClear($sql);
        if (!$dataList) {
            $dataList = array();
            $res = array('error' => 0, 'errortip' => '今日暂无公开课', 'result' => $dataList);
        } else {
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $dataList);
        }
        ajax_return($res, $request['language_type']);
    }


    /**
     * 获取单校所有教室的明细
     * author: ling
     * 对应接口文档 0001
     */
    function getClassRoomByBranchApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $token = $request['token'];
        $branch = $request['branch'];

        if ($token !== $this->getDateToken()) {
            $res = array('error' => 1, 'errortip' => 'token验证失败', 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        if (!$branch) {
            $res = array('error' => 1, 'errortip' => '请选择学校', 'result' => array());
            ajax_return($res, $request['language_type']);
        }

        $roomList = $this->DataControl->selectClear("
            select cm.classroom_id,cm.classroom_cnname,cm.classroom_branch
            from smc_classroom as cm
            left join smc_school as sl ON sl.school_id = cm.school_id
            where sl.school_branch = '{$branch}'
        ");

        if (!$roomList) {
            $roomList = array();
            $res = array('error' => 0, 'errortip' => '该校暂无可用的教室,请在校务系统进行维护', 'result' => $roomList);
        } else {
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $roomList);
        }
        ajax_return($res, $request['language_type']);
    }

    /**
     *  公开课视频回传
     * author: ling
     * 对应接口文档 0001
     */
    function updateHourVideoAction()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $token = $request['token'];
        $hour_id = $request['hour_id'];
        $video_url = $request['hour_videourl'];

        if ($token !== $this->getDateToken()) {
            $res = array('error' => 1, 'errortip' => 'token验证失败', 'result' => array());
            ajax_return($res, $request['language_type']);
        }

        if (!$hour_id) {
            $res = array('error' => 1, 'errortip' => '请选择课次', 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        $hourOne = $this->DataControl->getFieldOne("smc_class_hour", "hour_id", "hour_id ='{$hour_id}'");
        if (!$hourOne) {
            $res = array('error' => 1, 'errortip' => '未查询到对应的课次', 'result' => array());
            ajax_return($res, $request['language_type']);
        }

        if (!$video_url) {
            $res = array('error' => 1, 'errortip' => '请选择公开课视频', 'result' => array());
            ajax_return($res, $request['language_type']);
        }

        $data = array();
        $data['hour_videourl'] = $video_url;
        $data['hour_updatatime'] = time();
        if (!$this->DataControl->updateData("smc_class_hour", "hour_id='{$hour_id}'", $data)) {
            $res = array('error' => 1, 'errortip' => '操作失败', 'result' => array());
            ajax_return($res, $request['language_type']);
        } else {
            $res = array('error' => 0, 'errortip' => '操作成功', 'result' => array());
            ajax_return($res, $request['language_type']);
        }
    }


    function getSchoolListByCompanyApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        // $token = Input('get.token');
        $token = $request['token'];
//        $company_id = Input('get.','','trim,addslashes');
        $company_id = '8888';

        if ($token !== $this->getDateToken()) {
            $res = array('error' => 1, 'errortip' => 'token验证失败', 'result' => array());
            ajax_return($res, $request['language_type']);
        }

        $dataList = $this->DataControl->selectClear("select school_id,school_branch,school_cnname from  smc_school where company_id = '{$company_id}' ");
        if (!$dataList) {
            $dataList = array();
        }
        $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $dataList);
        ajax_return($res, $request['language_type']);


    }

    function getChangeDetlApi()
    {
        $k = 0;
        $field = array();
        $field[$k]["fieldstring"] = "student_branch";
        $field[$k]["fieldname"] = "学员编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["fieldname"] = "学员中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_enname";
        $field[$k]["fieldname"] = "学员英文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "stuchange_name";
        $field[$k]["fieldname"] = "异动类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "changelog_day";
        $field[$k]["fieldname"] = "异动日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "changelog_note";
        $field[$k]["fieldname"] = "备注";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $request = Input('get.', '', 'trim,addslashes');
        $datawhere = " 1 ";
        $havingwhere = " ";
        $is_studnum = false;

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (B.student_cnname like '%{$request['keyword']}%' 
            or B.student_enname like '%{$request['keyword']}%' 
            or B.student_branch like '%{$request['keyword']}%')";
        }

        if (isset($request['start_time']) && $request['start_time'] !== '') {
            $time = $request['start_time'];
        }

        $week_start_day = date("Y-m-d", strtotime($time));
        $week_end_day = date("Y-m-d", strtotime("$week_start_day  +  6 days"));

        if (isset($request['change_type'])) {
            switch ($request['change_type']) {
                case "转入":
                    $datawhere .= " AND C.class_branch='{$request['class_branch']}' and A.stuchange_code in('A03','A04','A05')";
                    break;
                case "转出":
                    $datawhere .= " AND C.class_branch='{$request['class_branch']}' and A.stuchange_code in('B02','B03','B04','B07')";
                    break;
                case "延班":
                    $datawhere .= " AND C.class_branch='{$request['class_branch']}' and A.stuchange_code in('A07')";
                    break;
                case "流失":
                    $datawhere .= " and (C.class_branch='{$request['class_branch']}' or A.stuchange_code ='C02' 
                    or (A.stuchange_code ='C04' and A.coursetype_id=(select Y.coursetype_id FROM SMC_CLASS X,SMC_COURSE Y WHERE X.COURSE_ID=Y.COURSE_ID AND X.CLASS_BRANCH='{$request['class_branch']}')))) and A.stuchange_code in('B01','C03','C04','C02')";
                    break;
                case "新生":
                    $datawhere .= " AND C.class_branch='{$request['class_branch']}' and A.stuchange_code in('A02')";
                    break;
                case "本周人数":
                    $datawhere .= " AND C.class_branch='{$request['class_branch']}' ";
//                    $havingwhere .= " having reading_status=1";
                    $is_studnum = true;
                    $field[3]["show"] = 0;
                    $field[4]["show"] = 0;
                    $field[5]["show"] = 0;
                    break;
                default:
                    $datawhere .= " and FALSE ";
            }
        } else {
            $datawhere .= " and FALSE ";
        }

        $sql = "SELECT A.changelog_id,A.student_id,B.student_branch,B.student_cnname,B.student_enname,D.stuchange_name,A.changelog_day,A.changelog_note
            FROM smc_student_changelog A 
            LEFT JOIN smc_student B ON A.student_id=B.student_id AND A.company_id=B.company_id
            LEFT JOIN smc_class C ON A.class_id=C.class_id AND A.company_id=C.company_id
            LEFT JOIN smc_code_stuchange D ON A.stuchange_code=D.stuchange_code
            WHERE {$datawhere} 
             and A.company_id='{$request['company_id']}'  
             -- AND A.school_id='{$request['school_id']}'  
             AND C.class_branch='{$request['class_branch']}'  
             and A.changelog_day <= '{$week_end_day}' 
             and A.changelog_day >='{$week_start_day}'  
             order by a.changelog_id
        ";

//        $sqlall = "SELECT B.student_branch,B.student_cnname,B.student_enname,
//	            (case when a.study_endday>='{$week_end_day}' then IFNULL((SELECT Y.stustatus_inclass FROM smc_student_changelog AS X, smc_code_stuchange AS Y WHERE X.stuchange_code = Y.stuchange_code
//						AND Y.stuchange_type = 0 AND X.company_id = A.company_id AND X.school_id = A.school_id AND X.student_id = A.student_id
//						AND X.class_id = A.class_id AND X.changelog_day<='{$week_end_day}' ORDER BY changelog_id DESC  LIMIT 0,1 ),1) else a.study_isreading end) as reading_status
//                FROM smc_student_study A
//                LEFT JOIN smc_student B ON A.student_id=B.student_id AND A.company_id=B.company_id
//                left join smc_class C on A.class_id=C.class_id AND A.company_id=C.company_id
//                LEFT JOIN SMC_COURSE D ON C.COURSE_ID=D.COURSE_ID
//                WHERE {$datawhere}
//                and A.company_id='{$request['company_id']}'
//                AND C.class_branch='{$request['class_branch']}'
//                and (A.study_endday>='{$week_start_day}' OR A.study_endday='')
//                and A.study_beginday<='{$week_end_day}'
//                and NOT exists(select 1 from smc_student_changelog X,smc_code_stuchange Y
//										WHERE X.stuchange_code=Y.stuchange_code
//                                    AND (class_id=A.class_id or x.stuchange_code ='C02'
//                                    or (x.stuchange_code ='C04' and x.coursetype_id=D.coursetype_id))
//										AND student_id=A.student_id
//										AND changelog_day>=A.study_endday
//										and Y.stustatus_inclass='0'
//										AND changelog_day<= '{$week_end_day}'
//										AND changelog_day>= '{$week_start_day}')
//        ";
        $sqlall = "SELECT A.class_id,C.class_branch,B.student_branch,B.student_cnname,B.student_enname 
            FROM smc_student_study A 
            LEFT JOIN smc_student B ON A.student_id=B.student_id AND A.company_id=B.company_id 
            LEFT JOIN smc_class C ON A.class_id=C.class_id AND A.company_id=C.company_id 
            LEFT JOIN smc_course D ON C.course_id=D.course_id 
            LEFT JOIN smc_code_coursetype E ON E.coursetype_id = D.coursetype_id 
            LEFT JOIN smc_school F ON A.school_id=F.school_id 
            WHERE {$datawhere}
            AND A.company_id='{$request['company_id']}' 
            AND C.class_branch='{$request['class_branch']}' 
            AND C.class_type =0  
            AND C.class_status>'-2' 
            AND C.class_enddate>='{$week_start_day}' 
            AND C.class_stdate<='{$week_end_day}' 
            AND A.study_endday>='{$week_start_day}' 
            AND A.study_beginday<='{$week_end_day}' 
            AND E.coursetype_isopenclass=0 
            and (d.course_inclasstype<>2 
                or exists(select 1 from smc_class_booking x left join smc_class_hour y on x.hour_id=y.hour_id 
                    where x.class_id=a.class_id and x.student_id=a.student_id and y.hour_day>='{$week_start_day}' and y.hour_day<='{$week_end_day}' and x.booking_status=0))
            and NOT exists(select 1 from smc_student_study x,smc_class y,smc_course z 
                where x.class_id=y.class_id and y.course_id=z.course_id 
                and x.study_endday>='{$week_start_day}' and x.study_beginday<='{$week_end_day}' 
                and y.class_enddate>='{$week_start_day}' and y.class_stdate<='{$week_end_day}' 
                and x.student_id=A.student_id and x.class_id<>A.class_id 
                and y.school_id=C.school_id and x.study_beginday>A.study_beginday 
                and z.coursetype_id=E.coursetype_id and y.class_type=0 and y.class_status>-2)
        ";
        if ($is_studnum) {
            $sql = $sqlall;
        }
        $changeList = $this->DataControl->selectClear($sql);
        if (!$changeList) {
            $changeList = array();
        }

        $result["field"] = $field;
        $result["list"] = $changeList;
        $res = array('error' => 0, 'errortip' => '获取异动明细成功', 'result' => $result);
        ajax_return($res, $request['language_type']);
    }

    function getUnconcatApi()
    {
        $k = 0;
        $field = array();
        $field[$k]["fieldstring"] = "key";
        $field[$k]["fieldname"] = "序号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "student_id";
        $field[$k]["fieldname"] = "学员id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["fieldname"] = "学员中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["ismethod"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_branch";
        $field[$k]["fieldname"] = "学员编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_enname";
        $field[$k]["fieldname"] = "学员英文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $request = Input('get.', '', 'trim,addslashes');
        $datawhere = " 1 ";

        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and a.school_id='{$request['school_id']}' ";
        }

        if (isset($request['coursetype_id']) && $request['coursetype_id'] !== '') {
            $datawhere .= " and a.coursetype_id='{$request['coursetype_id']}' ";
        }

        if (isset($request['end_time']) && $request['end_time'] !== '') {
            $datawhere .= " and a.class_enddate<='{$request['end_time']}'";
        } else {
            $request['end_time'] = date("Y-m-d");
            $datawhere .= " and a.class_enddate<='{$request['end_time']}'";
        }

        if (isset($request['start_time']) && $request['start_time'] !== '') {
            $datawhere .= " and a.class_enddate>='{$request['start_time']}'";
        } else {
            $request['start_time'] = date("Y-m-01");
            $datawhere .= " and a.class_enddate>='{$request['start_time']}'";
        }

        if (isset($request['is_containsbreakoff']) && $request['is_containsbreakoff'] == '0') {
            $datawhere .= " and not exists(select 1 from smc_class_breakoff where class_id=a.class_id and breakoff_status>=2 and breakoff_type=0) ";
        }

        $sql = "select student_id,student_branch,student_cnname,student_enname
            from smc_student_course_estimate a
            WHERE {$datawhere} 
            and course_isrenew=1 
            and connect_times=''
            and ((renewal_times<a.coursetype_renewtimes and a.class_enddate<'2022-01-01') or(renewal_rates<100 and a.class_enddate>='2022-01-01'))
            and channel_name=''
        ";

        $unconcatList = $this->DataControl->selectClear($sql);
        if (!$unconcatList) {
            $unconcatList = array();
        } else {
            $unconcatArray = array();
            foreach ($unconcatList as $key => $unconcatOne) {
                $unconcatVar = $unconcatOne;
                $unconcatVar['key'] = $key + 1;
                $unconcatArray[] = $unconcatVar;
            }
        }

        $result["field"] = $field;
        $result["list"] = $unconcatArray;
        $res = array('error' => 0, 'errortip' => '获取弹窗成功', 'result' => $result);
        ajax_return($res, $request['language_type']);

    }

    function getIncomeTypeListApi()
    {
        $request = Input('get.', '', 'trim,addslashes');

        $this->ThisVerify($request);

        $tem_array = array("0" => "耗课收益", "1" => "认缴收入", "2" => "教材收入", "3" => "杂费收入", "-1" => "收入红冲");

        $tem_data = array();
        foreach ($tem_array as $key => $val) {
            $data = array();
            $data['income_type'] = $key;
            $data['income_name'] = $val;
            $tem_data[] = $data;
        }

        $result = array();
        if ($tem_data) {
            $result["list"] = $tem_data;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '获取信息', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function getStuOneIntegralView()
    {
        $request = Input('get.', '', 'trim,addslashes');

        $sid = $this->DataControl->getFieldOne("smc_student", "student_id", "student_branch = '{$request['student_branch']}'");

        $integral = $this->DataControl->getFieldOne("smc_student_virtual_property", "property_integralbalance", "student_id = '{$sid['student_id']}'");

        if (!$integral) {
            $integral['property_integralbalance'] = '0';
        }

        $result = array('error' => 1, 'errortip' => '获取学员积分成功', 'result' => $integral['property_integralbalance']);
        ajax_return($result, $request['language_type']);
    }

    function isdefaultView()
    {
        $request = Input('get.', '', 'trim,addslashes');

        $sid = $this->DataControl->getFieldOne("smc_student", "student_id", "student_branch = '{$request['student_branch']}'");

        $isdefault = $this->DataControl->getFieldOne("smc_student_family", "family_isdefault", "student_id = '{$sid['student_id']}' and family_mobile = '{$request['mobile']}'");

        $result = array('error' => 1, 'errortip' => '获取是否默认家长成功', 'result' => $isdefault['family_isdefault']);
        ajax_return($result, $request['language_type']);
    }

    function stuReduceIntegralAction()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $sid = $this->DataControl->getFieldOne("smc_student", "student_id", "student_branch = '{$request['student_branch']}'");
        $pid = $this->DataControl->getFieldOne("smc_parenter", "parenter_id", "parenter_mobile = '{$request['mobile']}'");
        $BalanceModel = new \Model\Smc\BalanceModel();
        $bool = $BalanceModel->reduceStuIntegral($sid['student_id'], $request['integral'], 0, '积分提现', 0, $pid['parenter_id'], '积分提现', '积分提现', time(), 0, 0);
        if ($bool) {
            ajax_return(array('error' => '0', 'errortip' => '积分提现成功', 'result' => $bool));
        } else {
            ajax_return(array('error' => '1', 'errortip' => '积分提现失败'));
        }

    }


    function Pay()
    {
        $fee = 0.01;//举例充值0.01
        $appid = 'wx550be80d3f68edb1';//如果是公众号 就是公众号的appid
        $body = '充值';
        $mch_id = '1602629681';
        $nonce_str = $this->create_guid();//随机字符串
        $notify_url = '';//通知地址
        $openid = '';
        $out_trade_no = $this->order_number($openid);//商户订单号
        $spbill_create_ip = '';//服务器的ip
        $total_fee = $fee * 100;//因为充值金额最小是1 而且单位为分 如果是充值1元所以这里需要*100
        $trade_type = 'JSAPI';//交易类型 默认

        //这里是按照顺序的 因为下面的签名是按照顺序 排序错误 肯定出错
        $post['appid'] = $appid;
        $post['body'] = $body;
        $post['mch_id'] = $mch_id;
        $post['nonce_str'] = $nonce_str;//随机字符串
        $post['notify_url'] = $notify_url;
        $post['openid'] = $openid;
        $post['out_trade_no'] = $out_trade_no;
        $post['spbill_create_ip'] = $spbill_create_ip;//终端的ip
        $post['total_fee'] = $total_fee;//总金额 最低为一块钱 必须是整数
        $post['trade_type'] = $trade_type;
        $sign = $this->sign($post);//签名
        $post_xml = '<xml>
           <appid>' . $appid . '</appid>
           <body>' . $body . '</body>
           <mch_id>' . $mch_id . '</mch_id>
           <nonce_str>' . $nonce_str . '</nonce_str>
           <notify_url>' . $notify_url . '</notify_url>
           <openid>' . $openid . '</openid>
           <out_trade_no>' . $out_trade_no . '</out_trade_no>
           <spbill_create_ip>' . $spbill_create_ip . '</spbill_create_ip>
           <total_fee>' . $total_fee . '</total_fee>
           <trade_type>' . $trade_type . '</trade_type>
           <sign>' . $sign . '</sign>
        </xml> ';
        //统一接口prepay_id
        $url = 'https://api.mch.weixin.qq.com/pay/unifiedorder';
        $xml = $this->http_request($url, $post_xml);
        $array = $this->xml($xml);//全要大写
        if ($array['RETURN_CODE'] == 'SUCCESS' && $array['RESULT_CODE'] == 'SUCCESS') {
            $time = time();
            $tmp = '';//临时数组用于签名
            $tmp['appId'] = $appid;
            $tmp['nonceStr'] = $nonce_str;
            $tmp['package'] = 'prepay_id=' . $array['PREPAY_ID'];
            $tmp['signType'] = 'MD5';
            $tmp['timeStamp'] = "$time";

            $data['state'] = 1;
            $data['timeStamp'] = "$time";//时间戳
            $data['nonceStr'] = $nonce_str;//随机字符串
            $data['signType'] = 'MD5';//签名算法，暂支持 MD5
            $data['package'] = 'prepay_id=' . $array['PREPAY_ID'];//统一下单接口返回的 prepay_id 参数值，提交格式如：prepay_id=*
            $data['paySign'] = $this->sign($tmp);//签名,具体签名方案参见微信公众号支付帮助文档;
            $data['out_trade_no'] = $out_trade_no;

        } else {
            $data['state'] = 0;
            $data['text'] = "错误";
            $data['RETURN_CODE'] = $array['RETURN_CODE'];
            $data['RETURN_MSG'] = $array['RETURN_MSG'];
        }
        echo json_encode($data);
    }

    //学习平台 主管端 阅读班点评报表 （以上课次 、 剩余课次）
    function toJdbstuappRbClassApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
//        $this->ThisVerify($request);

//        $studentid = '50058';
//        $classid = '23561';
//        $stime = '2021-03-01';
//        $etime = '2021-03-23';
//        $classid = $request['classid'];
        $stubranch = $request['stubranch'];
        $classbranch = $request['classbranch'];
        $stime = $request['starttime'];
        $etime = $request['endtime'];
        $sql = "
            SELECT d.student_id,d.class_id,d.study_beginday,d.study_endday,
             (
              SELECT COUNT(l.log_id) FROM smc_student_coursebalance_log AS l
              WHERE l.student_id = d.student_id 
              AND l.class_id = d.class_id AND l.hourstudy_id <> '0'
              AND from_unixtime(l.log_time, '%Y-%m-%d') <= '{$etime}'
             ) AS overnum,
             (SELECT l.log_finaltimes FROM smc_student_coursebalance_log AS l
              WHERE l.student_id = d.student_id
              AND l.class_id = d.class_id
              AND from_unixtime(l.log_time, '%Y-%m-%d') <= '{$etime}'
              ORDER BY l.log_time DESC LIMIT 0,1
             ) AS surplusnum 
            FROM  smc_student_study AS d 
            LEFT JOIN smc_student as t ON d.student_id = t.student_id 
            LEFT JOIN smc_class as c ON d.class_id = c.class_id
            WHERE  c.class_branch = '{$classbranch}' AND d.study_endday >= '{$stime}' AND d.study_beginday <= '{$etime}' AND t.student_branch = '{$stubranch}' and d.student_id > 0 ";

        $studyList = $this->DataControl->selectClear($sql);

        if ($studyList) {
            $readOne = array();
            foreach ($studyList as $studyVar) {
                $readOne['overnum'] += $studyVar['overnum'];
                $readOne['surplusnum'] += $studyVar['surplusnum'];
            }
        }

        if ($studyList) {
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $readOne);
        } else {
            $res = array('error' => 1, 'errortip' => '无其他学校', 'result' => array());
        }

        ajax_return($res, $request['language_type']);
    }

//随机32位字符串
    function nonce_str()
    {
        $result = '';
        $str = 'QWERTYUIOPASDFGHJKLZXVBNMqwertyuioplkjhgfdsamnbvcxz';
        for ($i = 0; $i < 32; $i++) {
            $result .= $str[rand(0, 48)];
        }
        return $result;
    }

    function create_guid()
    {
        $charid = strtoupper(md5(uniqid(mt_rand(), true)));
        $uuid = substr($charid, 0, 8)
            . substr($charid, 8, 4)
            . substr($charid, 12, 4)
            . substr($charid, 16, 4)
            . substr($charid, 20, 12);
        return $uuid;
    }

//生成订单号
    function order_number($openid)
    {
        return md5($openid . time() . rand(10, 99));//32位
    }

//签名 $data要先排好顺序
    function sign($data)
    {
        $stringA = '';
        foreach ($data as $key => $value) {
            if (!$value) continue;
            if ($stringA) $stringA .= '&' . $key . "=" . $value;
            else $stringA = $key . "=" . $value;
        }

        return $stringA;
    }

    function http_request($url, $data = null, $headers = array())
    {
        $curl = curl_init();
        if (count($headers) >= 1) {
            curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);
        }
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, FALSE);
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, FALSE);

        if (!empty($data)) {
            curl_setopt($curl, CURLOPT_POST, 1);
            curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
        }
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);

        $output = curl_exec($curl);
        curl_close($curl);
        return $output;
    }

    function xml($xml)
    {
        $p = xml_parser_create();
        xml_parse_into_struct($p, $xml, $vals, $index);
        xml_parser_free($p);
        $data = "";
        foreach ($index as $key => $value) {
            if ($key == 'xml' || $key == 'XML') {
                continue;
            }
            $tag = $vals[$value[0]]['tag'];
            $value = $vals[$value[0]]['value'];
            $data[$tag] = $value;
        }
        return $data;
    }


}
