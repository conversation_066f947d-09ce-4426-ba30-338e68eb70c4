<?php


namespace Work\Controller\Smcapi;


class SettingController extends viewTpl
{
    public $u;
    public $t;
    public $c;
    public $visitType = "api";
    public $DataControl;
    public $Model;

    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();

        //数据库操作
        $this->DataControl = new \Dbmysql();
    }

    //获取校园机构信息
    function HomeView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\SettingModel($request);

        $result = $Model->getSchoolInfoList($request);
        ajax_return($result, $request['language_type']);
    }

    //编辑校园机构信息
    function updateSchoolAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\SettingModel($request);
        $this->addSmcWorkLog($request['company_id'], $request['school_id'], $request['staffer_id'], "校园设置->基本信息设置", '编辑机构信息', dataEncode($request));
        $result = $Model->updateSchoolAction($request);
        ajax_return($result, $request['language_type']);
    }

    function getSchAlerttimeApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\SettingModel($request);
        $res = $Model->getSchAlerttime($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function editSchAlerttimeAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\SettingModel($request);
        $res = $Model->editSchAlerttime($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '编辑成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //获取操作日志列表
    function getWorklogView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\SettingModel($request);

        $result = $Model->getWorklogList($request);
        ajax_return($result, $request['language_type']);
    }

    //获取疾病列表
    function getDiseaseListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\SettingModel($request);

        $result = $Model->getDiseaseList($request);
        ajax_return($result, $request['language_type']);
    }

    //获取教室列表
    function getClassroomView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\SettingModel($request);

        $result = $Model->getClassroomList($request);
        ajax_return($result, $request['language_type']);
    }

    //编辑教室
    function updateClassroomAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\SettingModel($request);
        $this->addSmcWorkLog($request['company_id'], $request['school_id'], $request['staffer_id'], "校园参数设置->教室设置", '编辑教室', dataEncode($request));
        $result = $Model->updateClassroomAction($request);
        ajax_return($result, $request['language_type']);
    }

    //添加教室
    function addClassroomAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\SettingModel($request);

        $result = $Model->addClassroomAction($request);
        $this->addSmcWorkLog($request['company_id'], $request['school_id'], $request['staffer_id'], "校园参数设置->教室设置", '新增教室', dataEncode($request));
        ajax_return($result, $request['language_type']);
    }

    //删除教室
    function delClassroomAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\SettingModel($request);

        $result = $Model->delClassroomAction($request);
        $this->addSmcWorkLog($request['company_id'], $request['school_id'], $request['staffer_id'], "校园参数设置->教室设置", '删除教室', dataEncode($request));
        ajax_return($result, $request['language_type']);
    }

    //获取角色设置列表
    function getPostpartView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\SettingModel($request);

        $result = $Model->getPostpartList($request);
        ajax_return($result, $request['language_type']);
    }

    //编辑角色
    function updatePostpartAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\SettingModel($request);

        $result = $Model->updatePostpartAction($request);
        $this->addSmcWorkLog($request['company_id'], $request['school_id'], $request['staffer_id'], "校园参数设置->内部参数设置->员工角色设置", '编辑角色', dataEncode($request));
        ajax_return($result, $request['language_type']);
    }

    //获取学校省
    function getProvinceApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\SettingModel($request);

        $result = $Model->getProvinceApi($request);
        ajax_return($result, $request['language_type']);
    }

    //获取学校市
    function getCityApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\SettingModel($request);

        $result = $Model->getCityApi($request);
        ajax_return($result, $request['language_type']);
    }

    //添加角色
    function addPostpartAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\SettingModel($request);

        $result = $Model->addPostpartAction($request);
        $this->addSmcWorkLog($request['company_id'], $request['school_id'], $request['staffer_id'], "校园参数设置->内部参数设置->员工角色设置", '添加角色', dataEncode($request));
        ajax_return($result, $request['language_type']);
    }

    //删除角色
    function delPostpartAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\SettingModel($request);

        $result = $Model->delPostpartAction($request);
        $this->addSmcWorkLog($request['company_id'], $request['school_id'], $request['staffer_id'], "校园参数设置->内部参数设置->员工角色设置", '删除角色 ', dataEncode($request));
        ajax_return($result, $request['language_type']);
    }

    //取消上课原因列表
    function getReasonView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\SettingModel($request);

        $result = $Model->getReasonList($request);


        ajax_return($result, $request['language_type']);
    }

    //添加取消上课原因
    function addReasonAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\SettingModel($request);

        $result = $Model->addReasonAction($request);
        $this->addSmcWorkLog($request['company_id'], $request['school_id'], $request['staffer_id'], "校园参数设置->基本参数设置->取消上课原因设置", '添加原因', dataEncode($request));
        ajax_return($result, $request['language_type']);
    }

    //编辑取消上课原因
    function updateReasonAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\SettingModel($request);

        $result = $Model->updateReasonAction($request);
        $this->addSmcWorkLog($request['company_id'], $request['school_id'], $request['staffer_id'], "校园参数设置->基本参数设置->取消上课原因设置", '修改原因', dataEncode($request));
        ajax_return($result, $request['language_type']);
    }

    //删除角色
    function delReasonAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\SettingModel($request);

        $result = $Model->delReasonAction($request);
        $this->addSmcWorkLog($request['company_id'], $request['school_id'], $request['staffer_id'], "校园参数设置->基本参数设置->取消上课原因设置", '删除原因', dataEncode($request));
        ajax_return($result, $request['language_type']);
    }

    //启用/不启用
    function RoomStatusAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\SettingModel($request);

        $result = $Model->RoomStatusAction($request);
        $this->addSmcWorkLog($request['company_id'], $request['school_id'], $request['staffer_id'], "校园参数设置->基本参数设置->教室设置", '修改状态', dataEncode($request));
        ajax_return($result, $request['language_type']);
    }

    //节假日管理列表
    function holidaysView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\SettingModel($request);

        $result = $Model->getHolidaysList($request);
        ajax_return($result, $request['language_type']);
    }

    //新增节假日
    function addHolidaysAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\SettingModel($request);

        $result = $Model->addHolidaysAction($request);
        $this->addSmcWorkLog($request['company_id'], $request['school_id'], $request['staffer_id'], "节假日设置->节假日设置", '新增节假日', dataEncode($request));
        ajax_return($result, $request['language_type']);
    }

    //编辑节假日
    function updateHolidaysAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\SettingModel($request);

        $result = $Model->updateHolidaysAction($request);
        $this->addSmcWorkLog($request['company_id'], $request['school_id'], $request['staffer_id'], "节假日设置->节假日设置", '修改节假日', dataEncode($request));
        ajax_return($result, $request['language_type']);
    }

    //删除节假日
    function delHolidaysAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\SettingModel($request);

        $result = $Model->delHolidaysAction($request);
        $this->addSmcWorkLog($request['company_id'], $request['school_id'], $request['staffer_id'], "节假日设置->节假日设置", '删除节假日', dataEncode($request));
        ajax_return($result, $request['language_type']);
    }


    //节假日日历列表
    function getMonthListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\SettingModel($request);

        $result = $Model->getMonthList($request);
        ajax_return($result, $request['language_type']);
    }

    //获取附近学校列表
    function getnearschoolListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\SettingModel($request);

        $result = $Model->getnearschoolList($request);
        ajax_return($result, $request['language_type']);
    }

    //添加附近学校
    function addnearschoolAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\SettingModel($request);

        $result = $Model->addnearschoolAction($request);
        $this->addSmcWorkLog($request['company_id'], $request['school_id'], $request['staffer_id'], "附近学校设置->附近学校管理", '添加附近学校', dataEncode($request));
        ajax_return($result, $request['language_type']);
    }

    //编辑附近学校
    function updatenearschoolAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\SettingModel($request);

        $result = $Model->updatenearschoolAction($request);
        $this->addSmcWorkLog($request['company_id'], $request['school_id'], $request['staffer_id'], "附近学校设置->附近学校管理", '编辑附近学校', dataEncode($request));
        ajax_return($result, $request['language_type']);
    }

    //删除附近学校
    function delnearschoolAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\SettingModel($request);

        $result = $Model->delnearschoolAction($request);
        $this->addSmcWorkLog($request['company_id'], $request['school_id'], $request['staffer_id'], "附近学校设置->附近学校管理", '删除附近学校', dataEncode($request));
        ajax_return($result, $request['language_type']);
    }

    //启用/不启用
    function nearschoolStatusAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\SettingModel($request);

        $result = $Model->nearschoolStatusAction($request);
        $this->addSmcWorkLog($request['company_id'], $request['school_id'], $request['staffer_id'], "附近学校设置->附近学校管理", '设置开启状态', dataEncode($request));
        ajax_return($result, $request['language_type']);
    }

    /**
     *  获取常用的上课时间
     */
    function getHourTimeListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\SettingModel($request);
        $dataList = $Model->getHourTimeList($request);
        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = 'hourtime_starttime';
        $field[$k]["fieldname"] = '上课开始时间';
        $field[$k]["custom"] = 1;
        $field[$k]["show"] = 1;
        $k++;
        $field[$k]["fieldstring"] = 'hourtime_endtime';
        $field[$k]["fieldname"] = '上课结束时间';
        $field[$k]["custom"] = 1;
        $field[$k]["show"] = 1;

        $result['field'] = $field;
        $result['list'] = $dataList['list'];
        $result['all_num'] = $dataList['num'];

        if ($result) {
            $res = array('error' => '0', 'errortip' => '获取常用上课时间', 'result' => $result);
        } else {
            $res = array('error' => '1', 'errortip' => '暂无常用上课时间', 'result' => array());
        }
        ajax_return($res, $request['language_type']);
    }

    function addHourTimeAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\SettingModel($request);
        if (!$request["hourtime_list"]) {
            $res = array('error' => '1', 'errortip' => '请选择时间', 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        $bool = $Model->addHourTime($request);
        if ($bool) {
            $res = array('error' => '0', 'errortip' => '新增成功', 'result' => array());
            $this->addSmcWorkLog($request['company_id'], $request['school_id'], $request['staffer_id'], "基本参数设置->常用上课时间", '添加上课时间', dataEncode($request));
        } else {
            $res = array('error' => '1', 'errortip' => '新增失败', 'result' => array());
        }
        ajax_return($res, $request['language_type']);
    }

    function editHourTimeAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\SettingModel($request);
        $bool = $Model->editHourTime($request);
        if ($bool) {
            $res = array('error' => '0', 'errortip' => '编辑成功', 'result' => array());
            $this->addSmcWorkLog($request['company_id'], $request['school_id'], $request['staffer_id'], "基本参数设置->常用上课时间", '编辑上课时间', dataEncode($request));
        } else {
            $res = array('error' => '1', 'errortip' => '编辑失败', 'result' => array());
        }
        ajax_return($res, $request['language_type']);
    }

    function delHourTimeAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\SettingModel($request);
        $bool = $Model->delHourTime($request);
        if ($bool) {
            $res = array('error' => '0', 'errortip' => '删除成功', 'result' => array());
            $this->addSmcWorkLog($request['company_id'], $request['school_id'], $request['staffer_id'], "基本参数设置->常用上课时间", '删除上课时间', dataEncode($request));
        } else {
            $res = array('error' => '1', 'errortip' => '删除失败', 'result' => array());
        }
        ajax_return($res, $request['language_type']);
    }


    //获取集团教室
    function getCompanyClassroomApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\SettingModel($request);

        $result = $Model->getCompanyClassroomApi($request);
        ajax_return($result, $request['language_type']);
    }

    //获取列表的个性化显示
    function getStafferFieldSetApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\SettingModel($request);

        $result = $Model->getStafferFieldSet($request);
        ajax_return($result, $request['language_type']);
    }

    //存储列表的个性化显示
    function saveStafferFieldSetAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\SettingModel($request);
        $bool = $Model->saveStafferFieldSet($request);
        if ($bool) {
            $res = array('error' => '0', 'errortip' => '保存成功', 'result' => array());
        } else {
            $res = array('error' => '1', 'errortip' => '保存失败', 'result' => array());
        }
        ajax_return($res, $request['language_type']);
    }

    //获取目标设定列表
    function getAchieveTargetView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Smc\SettingModel($request);
        $res = $ReportModel->getAchieveTarget($request);

        $field = array();

        $k = 0;

        $field[$k]["fieldstring"] = "school_id";
        $field[$k]["fieldname"] = "学校id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "school_name";
        $field[$k]["fieldname"] = "学校名称";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "学校编号";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "coursetype_id";
        $field[$k]["fieldname"] = "班组id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "target_year";
        $field[$k]["fieldname"] = "目标年度";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursetype_cnname";
        $field[$k]["fieldname"] = "目标所属班组";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_settle";
        $field[$k]["fieldname"] = "是否需要学校设定";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "school_settle_name";
        $field[$k]["fieldname"] = "目标状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "register_num";
        $field[$k]["fieldname"] = "年度招生目标";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "register_rate";
        $field[$k]["fieldname"] = "年度招生目标达成率";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "reading_num";
        $field[$k]["fieldname"] = "年度在读目标";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "reading_rate";
        $field[$k]["fieldname"] = "年度在读目标达成率";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "losing_num";
        $field[$k]["fieldname"] = "年度流失限额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "losing_rate";
        $field[$k]["fieldname"] = "年度流失限额率";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res['list']) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => "暂无学校目标设定信息", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //获取月度目标设定列表
    function getMonthAchieveTargetView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Smc\SettingModel($request);
        $res = $ReportModel->getMonthAchieveTarget($request);

        $field = array();
        $k = 0;

        $field[$k]["fieldstring"] = "target_month";
        $field[$k]["fieldname"] = "月份";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "month_sort";
        $field[$k]["fieldname"] = "月度";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "register_num";
        $field[$k]["fieldname"] = "招生目标";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

//        $field[$k]["fieldstring"] = "regi_num";
//        $field[$k]["fieldname"] = "实现目标";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//        $k++;

        $field[$k]["fieldstring"] = "register_rate";
        $field[$k]["fieldname"] = "招生目标达成率";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "reading_num";
        $field[$k]["fieldname"] = "在读目标";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "reading_rate";
        $field[$k]["fieldname"] = "在读目标达成率";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "losing_num";
        $field[$k]["fieldname"] = "流失限额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "losing_rate";
        $field[$k]["fieldname"] = "流失限额率";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res['list']) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => "暂无学校目标设定信息", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function getMonthTargetView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Smc\SettingModel($request);
        $res = $ReportModel->getMonthTarget($request);

        $result = array();
        $result["field"] = $res['field'];
        $result["allnum"] = $res['allnum'];
        $result["list"] = $res['list'];
        $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        ajax_return($res, $request['language_type']);
    }

    //编辑月度目标
    function editMonthAchieveTargetAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Smc\SettingModel($request);
        $boolres = $Model->editMonthTarget($request);

        $res = array('error' => $boolres ? '0' : '1', 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

}