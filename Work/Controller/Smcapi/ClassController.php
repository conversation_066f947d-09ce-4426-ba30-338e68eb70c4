<?php


namespace Work\Controller\Smcapi;


class ClassController extends viewTpl
{
    public $u;
    public $t;
    public $c;

    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
    }

    function HomeView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ClassModel = new \Model\Smc\ClassModel($request);
        $res = $ClassModel->classList($request);
        $contractOne = $this->getContract($request['company_id']);

        $field = array();

        $field[0]["fieldstring"] = "class_id";
        $field[0]["fieldname"] = "班级ID";
        $field[0]["show"] = 0;
        $field[0]["custom"] = 0;

        $field[1]["fieldstring"] = "is_schedule";
        $field[1]["fieldname"] = "是否排课";
        $field[1]["show"] = 0;
        $field[1]["custom"] = 0;

        $field[2]["fieldstring"] = "class_cnname";
        $field[2]["fieldname"] = "班级名称";
        $field[2]["show"] = 1;
        $field[2]["custom"] = 1;

        $field[3]["fieldstring"] = "class_enname";
        $field[3]["fieldname"] = "班级别名";
        $field[3]["show"] = 1;
        $field[3]["custom"] = 1;

        $field[4]["fieldstring"] = "class_branch";
        $field[4]["fieldname"] = "班级编号";
        $field[4]["show"] = 1;
        $field[4]["custom"] = 1;

        $field[5]["fieldstring"] = "number";
        $field[5]["fieldname"] = "人数";
        $field[5]["show"] = 1;
        $field[5]["custom"] = 1;
        $field[5]["isCircleProgress"] = false;
        $field[5]["isSquareProgress"] = true;

        $field[6]["fieldstring"] = "course_cnname";
        $field[6]["fieldname"] = "课程别";
        $field[6]["show"] = 1;
        $field[6]["custom"] = 1;

        $field[7]["fieldstring"] = "course_branch";
        $field[7]["fieldname"] = "课程别编号";
        $field[7]["show"] = 1;
        $field[7]["custom"] = 1;

        $field[8]["fieldstring"] = "cnteacher";
        $field[8]["fieldname"] = "教师";
        $field[8]["show"] = 1;
        $field[8]["custom"] = 1;

        if (!$contractOne || $contractOne['edition_id'] != '2') {
            $field[9]["fieldstring"] = "class_timestr";
            $field[9]["fieldname"] = "上课时间";
            $field[9]["show"] = 1;
            $field[9]["custom"] = 1;

            $field[10]["fieldstring"] = "classroom_cnname";
            $field[10]["fieldname"] = "上课教室";
            $field[10]["show"] = 1;
            $field[10]["custom"] = 1;
        }

        $field[11]["fieldstring"] = "class_stdate";
        $field[11]["fieldname"] = "开班日期";
        $field[11]["show"] = 1;
        $field[11]["custom"] = 1;

        if (!$contractOne || $contractOne['edition_id'] != '2') {

            $field[12]["fieldstring"] = "info";
            $field[12]["fieldname"] = "已上/计划";
            $field[12]["show"] = 1;
            $field[12]["custom"] = 1;
            $field[12]["isCircleProgress"] = true;
            $field[12]["isSquareProgress"] = false;
        }

        $field[13]["fieldstring"] = "class_status";
        $field[13]["fieldname"] = "班级状态";
        $field[13]["show"] = 0;
        $field[13]["custom"] = 0;

        $field[14]["fieldstring"] = "course_id";
        $field[14]["fieldname"] = "课程ID";
        $field[14]["show"] = 0;
        $field[14]["custom"] = 0;

        $field[15]["fieldstring"] = "can_promotion";
        $field[15]["fieldname"] = "是否可以升班";
        $field[15]["show"] = 0;
        $field[15]["custom"] = 0;

        $field[16]["fieldstring"] = "edit_schedule";
        $field[16]["fieldname"] = "是否可以修改排课";
        $field[16]["show"] = 0;
        $field[16]["custom"] = 0;

        $field[17]["fieldstring"] = "class_staus_namme";
        $field[17]["fieldname"] = "班级状态";
        $field[17]["show"] = 1;
        $field[17]["custom"] = 0;

        $field[18]["fieldstring"] = "class_enddate";
        $field[18]["fieldname"] = "结班时间";
        $field[18]["show"] = 1;
        $field[18]["custom"] = 0;

        if (!$contractOne || $contractOne['edition_id'] != '2') {
            $field[20]["fieldstring"] = "class_type_name";
            $field[20]["fieldname"] = "班级类型";
            $field[20]["show"] = 1;
            $field[20]["custom"] = 1;
        }

        $field[21]["fieldstring"] = "class_isprepare_name";
        $field[21]["fieldname"] = "是否为预备班";
        $field[21]["show"] = 1;
        $field[21]["custom"] = 0;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => '暂无课次类班级信息', 'result' => $result);

        }
        ajax_return($res, $request['language_type']);

    }


//     预约类班级
    function coursetermClassView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ClassModel = new \Model\Smc\ClassModel($request);
        $res = $ClassModel->coursetermClass($request);
        $field = array();

        $k = 0;
        $field[$k]["fieldstring"] = "class_id";
        $field[$k]["fieldname"] = "班级ID";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;
        $field[$k]["fieldstring"] = "course_id";
        $field[$k]["fieldname"] = "课程id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;
        $field[$k]["fieldstring"] = "class_cnname";
        $field[$k]["fieldname"] = "班级名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["ismethod"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "class_enname";
        $field[$k]["fieldname"] = "班级别名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "class_branch";
        $field[$k]["fieldname"] = "班级编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "class_status_name";
        $field[$k]["fieldname"] = "班级状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "number";
        $field[$k]["fieldname"] = "人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["isCircleProgress"] = false;
        $field[$k]["isSquareProgress"] = true;
        $k++;

        $field[$k]["fieldstring"] = "info";
        $field[$k]["fieldname"] = "已上/计划";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["isCircleProgress"] = true;
        $field[$k]["isSquareProgress"] = false;
        $k++;

        $field[$k]["fieldstring"] = "num";
        $field[$k]["fieldname"] = "预约人次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "course_cnname";
        $field[$k]["fieldname"] = "课程别";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "course_branch";
        $field[$k]["fieldname"] = "课程别编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "cnteacher";
        $field[$k]["fieldname"] = "教师";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

//		$field[9]["fieldstring"] = "class_timestr";
//		$field[9]["fieldname"] = "上课时间";
//		$field[9]["show"] = 1;
//		$field[9]["custom"] = 1;
//
//		$field[10]["fieldstring"] = "classroom_cnname";
//		$field[10]["fieldname"] = "上课教室";
//		$field[10]["show"] = 1;
//		$field[10]["custom"] = 1;

        $field[$k]["fieldstring"] = "class_stdate";
        $field[$k]["fieldname"] = "开班日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_status";
        $field[$k]["fieldname"] = "班级状态";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;

        $k++;
        $field[$k]["fieldstring"] = "is_schedule";
        $field[$k]["fieldname"] = "是否排课";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;
        $field[$k]["fieldstring"] = "can_promotion";
        $field[$k]["fieldname"] = "能否入班";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;
        $field[$k]["fieldstring"] = "edit_schedule";
        $field[$k]["fieldname"] = "是否可以修改排课";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;
        $field[$k]["fieldstring"] = "class_type_name";
        $field[$k]["fieldname"] = "班级类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;


        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
//			$res = array('error' => 1, 'errortip' => '暂无按月(期)计费班级信息', 'result' => $result);
            $res = array('error' => 1, 'errortip' => '暂无预约类计费班级信息', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    //期类班级
    function mothClassView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ClassModel = new \Model\Smc\ClassModel($request);
        $res = $ClassModel->mothClass($request);
        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "class_id";
        $field[$k]["fieldname"] = "班级ID";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "course_id";
        $field[$k]["fieldname"] = "课程id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "class_cnname";
        $field[$k]["fieldname"] = "班级名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["ismethod"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_enname";
        $field[$k]["fieldname"] = "班级别名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_branch";
        $field[$k]["fieldname"] = "班级编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "course_cnname";
        $field[$k]["fieldname"] = "课程别";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "course_branch";
        $field[$k]["fieldname"] = "课程别编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "number";
        $field[$k]["fieldname"] = "人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "cnteacher";
        $field[$k]["fieldname"] = "教师";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_stdate";
        $field[$k]["fieldname"] = "开班日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "info";
        $field[$k]["fieldname"] = "已上/计划";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["isCircleProgress"] = true;
        $field[$k]["isSquareProgress"] = false;
        $k++;

        $field[$k]["fieldstring"] = "class_status_name";
        $field[$k]["fieldname"] = "班级状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_type_name";
        $field[$k]["fieldname"] = "班级类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "is_schedule";
        $field[$k]["fieldname"] = "是否排课";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "can_promotion";
        $field[$k]["fieldname"] = "能否入班";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "edit_schedule";
        $field[$k]["fieldname"] = "是否可以修改排课";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;


        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => '暂无按月(期)计费班级信息', 'result' => $result);
//			$res = array('error' => 1, 'errortip' => '暂无预约类计费班级信息', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    //公开课班级
    function getOpenClassView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ClassModel = new \Model\Smc\ClassModel($request);
        $res = $ClassModel->getOpenClass($request);
        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "class_cnname";
        $field[$k]["fieldname"] = "班级名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["ismethod"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_enname";
        $field[$k]["fieldname"] = "班级别名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_branch";
        $field[$k]["fieldname"] = "班级编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "course_openclasstype_name";
        $field[$k]["fieldname"] = "公开课类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_appointnum";
        $field[$k]["fieldname"] = "可预约人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "course_cnname";
        $field[$k]["fieldname"] = "课程别名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "course_branch";
        $field[$k]["fieldname"] = "课程别编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "cnteacher";
        $field[$k]["fieldname"] = "教师";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_timestr";
        $field[$k]["fieldname"] = "上课时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "classroom_cnname";
        $field[$k]["fieldname"] = "上课教室";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_stdate";
        $field[$k]["fieldname"] = "开班日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "info";
        $field[$k]["fieldname"] = "已上/计划";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["isCircleProgress"] = true;
        $field[$k]["isSquareProgress"] = false;
        $k++;

        $field[$k]["fieldstring"] = "class_status_name";
        $field[$k]["fieldname"] = "班级状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => '暂无公开课班级信息', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    function getClassMenuApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ClassModel = new \Model\Smc\ClassModel($request);
        $res = $ClassModel->getClassMenu($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $ClassModel->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function classOneView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ClassModel = new \Model\Smc\ClassModel($request);
        $res = $ClassModel->classOne($request);

        $result = array();
        if ($res) {
            $result["list"] = $res['list'];
            $result["info"] = $res['info'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $ClassModel->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function classStuOptionalTimesListApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ClassModel = new \Model\Smc\ClassModel($request);
        $res = $ClassModel->classStuOptionalTimesList($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $ClassModel->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function giveAwayLessonsAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ClassModel = new \Model\Smc\ClassModel($request);
        $res = $ClassModel->giveAwayLessons($request);
        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '赠送课次申请成功', 'result' => $result);
            $this->addSmcWorkLog($request['company_id'], $request['school_id'], $request['staffer_id'], "班务管理->班级管理->申请赠送课次", '申请赠送课次', dataEncode($request));
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $ClassModel->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }


    function classStudentApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ClassModel = new \Model\Smc\ClassModel($request);
        $classOne = $this->DataControl->selectOne("
            select  ct.coursetype_isopenclass,co.course_inclasstype,cl.class_type,cl.class_status,co.course_issupervise,co.coursetype_id,co.coursecat_id
            from  smc_class as cl
            left join smc_course as co on cl.course_id= co.course_id
            left join smc_code_coursetype as ct on ct.coursetype_id= co.coursetype_id        
            where cl.class_id='{$request['class_id']}' 
        ");
        $result = array();
        if($classOne['class_status']=='-1' && ($classOne['course_inclasstype']!=3 && $classOne['course_openclasstype']!=1)){
            $res = $ClassModel->endClassStudent($request);

            $field = array();
            $k = 0;
            $field[$k]["fieldstring"] = "student_cnname";
            $field[$k]["fieldname"] = "学员中文名";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 0;
            $field[$k]["isportrait"] = 1;
            $k++;
            $field[$k]["fieldstring"] = "student_enname";
            $field[$k]["fieldname"] = "学员英文名";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 0;
            $k++;
            $field[$k]["custom"] = 0;
            $field[$k]["fieldstring"] = "student_branch";
            $field[$k]["fieldname"] = "学员编号";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 0;
            $k++;
            $field[$k]["fieldstring"] = "student_sex";
            $field[$k]["fieldname"] = "性别";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 0;
            $k++;
            $field[$k]["fieldstring"] = "parenter_mobile";
            $field[$k]["fieldname"] = "联系电话";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 0;
            $k++;
            $field[$k]["fieldstring"] = "study_beginday";
            $field[$k]["fieldname"] = "入班时间";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 0;
            $k++;
            $field[$k]["fieldstring"] = "study_endday";
            $field[$k]["fieldname"] = "出班时间";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 0;
            $k++;
        }else{
            if ($classOne['coursetype_isopenclass'] == 1) {
                $field = array();
                $field[0]["fieldstring"] = "client_id";
                $field[0]["fieldname"] = "学员ID";
                $field[0]["show"] = 0;
                $field[0]["custom"] = 0;

                $field[1]["fieldstring"] = "client_cnname";
                $field[1]["fieldname"] = "姓名";
                $field[1]["show"] = 1;
                $field[1]["custom"] = 0;

                $field[3]["fieldstring"] = "client_sex";
                $field[3]["fieldname"] = "性别";
                $field[3]["show"] = 1;
                $field[3]["custom"] = 0;

                $field[4]["fieldstring"] = "client_mobile";
                $field[4]["fieldname"] = "主要联系电话";
                $field[4]["show"] = 1;
                $field[4]["custom"] = 0;

                $field[5]["fieldstring"] = "hour_day";
                $field[5]["fieldname"] = "试听日期";
                $field[5]["show"] = 1;
                $field[5]["custom"] = 0;

                $field[6]["fieldstring"] = "audition_isvisitname";
                $field[6]["fieldname"] = "是否试听";
                $field[6]["show"] = 1;
                $field[6]["custom"] = 0;

                $field[7]["fieldstring"] = "audition_novisitreason";
                $field[7]["fieldname"] = "未试听原因";
                $field[7]["show"] = 1;
                $field[7]["custom"] = 0;

                $res = $ClassModel->openClassStudent($request);
                $result["coursetype_isopenclass"] = '1';
                $result["course_inclasstype"] = $classOne['course_inclasstype'];
            } else {
                $res = $ClassModel->classStudent($request);
                $contractOne = $this->getContract($request['company_id']);

                $field = array();
                $field[0]["fieldstring"] = "student_id";
                $field[0]["fieldname"] = "学员ID";
                $field[0]["show"] = 0;
                $field[0]["custom"] = 0;

                $field[1]["fieldstring"] = "student_cnname";
                $field[1]["fieldname"] = "学员中文名";
                $field[1]["show"] = 1;
                $field[1]["custom"] = 0;
                $field[1]["isportrait"] = 1;

                $field[2]["fieldstring"] = "student_enname";
                $field[2]["fieldname"] = "学员英文名";
                $field[2]["show"] = 1;
                $field[2]["custom"] = 0;

                $field[3]["fieldstring"] = "student_branch";
                $field[3]["fieldname"] = "学员编号";
                $field[3]["show"] = 1;
                $field[3]["custom"] = 0;

                $field[4]["fieldstring"] = "student_sex";
                $field[4]["fieldname"] = "性别";
                $field[4]["show"] = 1;
                $field[4]["custom"] = 0;

                $field[5]["fieldstring"] = "family_mobile";
                $field[5]["fieldname"] = "联系电话";
                $field[5]["show"] = 1;
                $field[5]["custom"] = 0;

                $field[6]["fieldstring"] = "study_beginday";
                $field[6]["fieldname"] = "入班时间";
                $field[6]["show"] = 1;
                $field[6]["custom"] = 0;

                $field[7]["fieldstring"] = "course_cnname";
                $field[7]["fieldname"] = "课程别名称";
                $field[7]["show"] = 0;
                $field[7]["custom"] = 0;

                $field[8]["fieldstring"] = "course_branch";
                $field[8]["fieldname"] = "课程别编号";
                $field[8]["show"] = 0;
                $field[8]["custom"] = 0;

                if (!$contractOne || $contractOne['edition_id'] != '2') {

                    $field[10]["fieldstring"] = "coursebalance_unitexpend";
                    $field[10]["fieldname"] = "消耗单价";
                    $field[10]["show"] = 1;
                    $field[10]["custom"] = 0;

                    $field[11]["fieldstring"] = "coursebalance_figure";
                    $field[11]["fieldname"] = "课程余额";
                    $field[11]["show"] = 1;
                    $field[11]["custom"] = 0;

                    $field[12]["fieldstring"] = "coursebalance_time";
                    $field[12]["fieldname"] = "剩余课次";
                    $field[12]["show"] = 1;
                    $field[12]["custom"] = 0;
                }

                $result["coursetype_isopenclass"] = '0';
                $result["course_inclasstype"] = $classOne['course_inclasstype'];
                $result["class_type"] = $classOne['class_type'];
            }
        }

        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];

        if(is_array($res['list'])){
            foreach($res['list'] as &$val){
                $val['coursetype_id'] = $classOne['coursetype_id'];
                $val['coursecat_id'] = $classOne['coursecat_id'];

                $a = $this->DataControl->getFieldOne("smc_student_coursebalance","companies_id,coursebalance_issupervise","student_id = '{$val['student_id']}' and school_id = '{$request['school_id']}' and course_id = '{$val['course_id']}'");

                $b = $this->DataControl->getFieldOne("gmc_code_companies","companies_issupervise","companies_id = '{$a['companies_id']}'");
                if($b['companies_issupervise'] == '0'){
                    $val['issupervise'] = '0';
                }else{
                    $val['issupervise'] = '1';
                }
            }
        }

        if ($res) {
            $result["fieldcustom"] = 0;
            if($res['list']){
                $result["list"] = $res['list'];
            }else{
                $result["list"] = array();
            }
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => '班级暂无学员信息', 'result' => $result);

        }
        ajax_return($res, $request['language_type']);
    }

    function classTimesApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\ClassModel($request);
        $dataList = $Model->classTimes($request);
        if ($dataList) {
            $result["list"] = $dataList;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);

        }
        ajax_return($res, $request['language_type']);
    }

    function classTimetableApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ClassModel = new \Model\Smc\ClassModel($request);
        $dataList = $ClassModel->classhourtable($request);

        $result = array();
        $field = array();
        if ($request['type'] == 1) {

            $sql="select co.course_inclasstype,co.course_openclasstype from smc_class as cl,smc_course as co where cl.course_id=co.course_id and cl.class_id='{$request['class_id']}' limit 0,1";
            $classOne=$this->DataControl->selectOne($sql);

            $k = 0;
            $field[$k]["fieldstring"] = "hour_id";
            $field[$k]["fieldname"] = "课时ID";
            $field[$k]["show"] = 0;
            $field[$k]["custom"] = 0;
            $k++;
            $field[$k]["fieldstring"] = "hour_day";
            $field[$k]["fieldname"] = "上课日期";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 0;
            $k++;
            $field[$k]["custom"] = 0;
            $field[$k]["fieldstring"] = "week_day";
            $field[$k]["fieldname"] = "上课周次";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 0;
            $k++;
            $field[$k]["fieldstring"] = "hour_way_name";
            $field[$k]["fieldname"] = "上课方式";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 0;
            $k++;

            $field[$k]["fieldstring"] = "hour_timesection";
            $field[$k]["fieldname"] = "上课时间";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 0;
            $k++;

            $field[$k]["fieldstring"] = "classroom_cnname";
            $field[$k]["fieldname"] = "上课教室";
            $field[$k]["show"] = 1;
            $field[$k]["isShowWay"] = 1;
            $field[$k]["custom"] = 0;
            $k++;
            $field[$k]["fieldstring"] = "staffer_cnname";
            $field[$k]["fieldname"] = "主教教师";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 0;
            $k++;
            $field[$k]["fieldstring"] = "re_staffer_cnname";
            $field[$k]["fieldname"] = "助教教师";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 0;
            $k++;
            $field[$k]["fieldstring"] = "hour_lessontimes";
            $field[$k]["fieldname"] = "课次";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 0;

            if($classOne['course_inclasstype']!='3' || $classOne['course_openclasstype']!='1'){
                $k++;
                $field[$k]["fieldstring"] = "hour_ischecking_name";
                $field[$k]["fieldname"] = "是否考勤";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
            }

            if ($dataList['class_type'] == 0) {
                $k++;
                $field[$k]["fieldstring"] = "hour_isfree";
                $field[$k]["fieldname"] = "是否计费";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
            }
            $k++;
            $field[$k]["fieldname"] = "教室id";
            $field[$k]["fieldstring"] = "classroom_id";
            $field[$k]["show"] = 0;
            $field[$k]["custom"] = 0;
            $k++;
            $field[$k]["fieldname"] = "教师id";
            $field[$k]["fieldstring"] = "staffer_id";
            $field[$k]["show"] = 0;
            $field[$k]["custom"] = 0;

            if ($dataList['class_type'] == 0) {
                $k++;
                $field[$k]["fieldname"] = "课次类型";
                $field[$k]["fieldstring"] = "hour_iswarming_name";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 0;
            }

        } else {
            $k = 0;
            $field[$k]["fieldname"] = "上午/下午";
            $field[$k]["fieldstring"] = "noon_name";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 0;
            $field[$k]["is_title"] = 1;
            $k++;
            $field[$k]["fieldname"] = "周一";
            $field[$k]["fieldstring"] = "Monday";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 0;
            $k++;
            $field[$k]["fieldname"] = "周二";
            $field[$k]["fieldstring"] = "Tuesday";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 0;
            $k++;
            $field[$k]["fieldname"] = "周三";
            $field[$k]["fieldstring"] = "Wednesday";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 0;
            $k++;
            $field[$k]["fieldname"] = "周四";
            $field[$k]["fieldstring"] = "Thursday";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 0;
            $k++;
            $field[$k]["fieldname"] = "周五";
            $field[$k]["fieldstring"] = "Friday";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 0;
            $k++;
            $field[$k]["fieldname"] = "周六";
            $field[$k]["fieldstring"] = "Saturday";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 0;
            $k++;
            $field[$k]["fieldname"] = "周日";
            $field[$k]["fieldstring"] = "Sunday";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 0;


        }

        $result['field'] = $field;
        if ($dataList) {
            $result["list"] = $dataList['list'];
            $result["info"] = $dataList['info'];
            $result["class_type"] = $dataList['class_type'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["info"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => '班级暂无课表信息', 'result' => $result);

        }
        ajax_return($res, $request['language_type']);

    }

    function classRollCallApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ClassModel = new \Model\Smc\ClassModel($request);
        $dataList = $ClassModel->classRollCall($request);
        if ($dataList) {
            $result["list"] = $dataList;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $ClassModel->errortip, 'result' => $result);

        }
        ajax_return($res, $request['language_type']);
    }

    function classAttendanceApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ClassModel = new \Model\Smc\ClassModel($request);
        $res = $ClassModel->classAttendance($request);
        $field = array();

        $k = 0;
        $field[$k]["fieldstring"] = "hour_id";
        $field[$k]["fieldname"] = "课时ID";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "hour_day";
        $field[$k]["fieldname"] = "上课时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "hour_way_name";
        $field[$k]["fieldname"] = "上课方式";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "mainteacher";
        $field[$k]["fieldname"] = "教师";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "auxiliaryteacher";
        $field[$k]["fieldname"] = "助教";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "statue";
        $field[$k]["fieldname"] = "实到/应到";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "ratio";
        $field[$k]["fieldname"] = "出勤率";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "number";
        $field[$k]["fieldname"] = "抵消次数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 0;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => '班级暂无上课记录', 'result' => $result);

        }
        ajax_return($res, $request['language_type']);
    }


    function classChangeApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ChangeModel = new \Model\Smc\ChangeModel();
        $res = $ChangeModel->classChange($request);
        $field = array();
        $field[0]["fieldstring"] = "changelog_id";
        $field[0]["fieldname"] = "异动ID";
        $field[0]["show"] = 0;
        $field[0]["custom"] = 0;

        $field[2]["fieldstring"] = "student_cnname";
        $field[2]["fieldname"] = "学员中文名";
        $field[2]["show"] = 1;
        $field[2]["custom"] = 1;
        $field[2]["ismethod"] = 1;

        $field[3]["fieldstring"] = "student_enname";
        $field[3]["fieldname"] = "学员英文名";
        $field[3]["show"] = 1;
        $field[3]["custom"] = 1;

        $field[4]["fieldstring"] = "student_branch";
        $field[4]["fieldname"] = "学员编号";
        $field[4]["show"] = 1;
        $field[4]["custom"] = 1;

        $field[5]["fieldstring"] = "stuchange_name";
        $field[5]["fieldname"] = "异动类型";
        $field[5]["show"] = 1;
        $field[5]["custom"] = 1;

        $field[6]["fieldstring"] = "school_cnname";
        $field[6]["fieldname"] = "异动学校";
        $field[6]["show"] = 1;
        $field[6]["custom"] = 1;

        $field[7]["fieldstring"] = "class_cnname";
        $field[7]["fieldname"] = "异动班级";
        $field[7]["show"] = 1;
        $field[7]["custom"] = 1;

        $field[8]["fieldstring"] = "class_branch";
        $field[8]["fieldname"] = "异动班级编号";
        $field[8]["show"] = 1;
        $field[8]["custom"] = 1;

        $field[9]["fieldstring"] = "changelog_note";
        $field[9]["fieldname"] = "异动描述";
        $field[9]["show"] = 1;
        $field[9]["custom"] = 1;

        $field[10]["fieldstring"] = "changelog_day";
        $field[10]["fieldname"] = "异动日期";
        $field[10]["show"] = 1;
        $field[10]["custom"] = 1;

        $field[11]["fieldstring"] = "stustatus_isdel";
        $field[11]["fieldname"] = "是否可以删除";
        $field[11]["show"] = 0;
        $field[11]["custom"] = 0;

        $field[12]["fieldstring"] = "change_pid";
        $field[12]["fieldname"] = "异动编号";
        $field[12]["show"] = 0;
        $field[12]["custom"] = 0;

        $field[13]["fieldstring"] = "stuchange_code";
        $field[13]["fieldname"] = "异动代码";
        $field[13]["show"] = 0;
        $field[13]["custom"] = 0;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 0;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => '班级暂无学员异动信息', 'result' => $result);

        }
        ajax_return($res, $request['language_type']);
    }

    function classTeacherInfoApi()
    {

        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ClassModel = new \Model\Smc\ClassModel($request);
        $res = $ClassModel->classTeacherInfo($request);
        $field = array();
        $field[0]["fieldstring"] = "staffer_id";
        $field[0]["fieldname"] = "职工ID";
        $field[0]["show"] = 0;
        $field[0]["custom"] = 0;

        $field[2]["fieldstring"] = "staffer_cnname";
        $field[2]["fieldname"] = "中文名";
        $field[2]["show"] = 1;
        $field[2]["custom"] = 1;
        $field[2]["ismethod"] = 1;

        $field[3]["fieldstring"] = "staffer_enname";
        $field[3]["fieldname"] = "英文名";
        $field[3]["show"] = 1;
        $field[3]["custom"] = 1;

        $field[4]["fieldstring"] = "staffer_branch";
        $field[4]["fieldname"] = "教师编号";
        $field[4]["show"] = 1;
        $field[4]["custom"] = 1;

        $field[5]["fieldstring"] = "staffer_sex";
        $field[5]["fieldname"] = "性别";
        $field[5]["show"] = 1;
        $field[5]["custom"] = 1;

        $field[6]["fieldstring"] = "staffer_mobile";
        $field[6]["fieldname"] = "联系电话";
        $field[6]["show"] = 1;
        $field[6]["custom"] = 1;

        $field[7]["fieldstring"] = "teachtype_name";
        $field[7]["fieldname"] = "教师类型";
        $field[7]["show"] = 1;
        $field[7]["custom"] = 1;

        $field[8]["fieldstring"] = "teach_status";
        $field[8]["fieldname"] = "带班状态";
        $field[8]["show"] = 1;
        $field[8]["custom"] = 1;

        $field[9]["fieldstring"] = "staffer_leave";
        $field[9]["fieldname"] = "在职状态";
        $field[9]["show"] = 1;
        $field[9]["custom"] = 1;

        $field[10]["fieldstring"] = "classNum";
        $field[10]["fieldname"] = "带课数量";
        $field[10]["show"] = 1;
        $field[10]["custom"] = 1;

        $field[11]["fieldstring"] = "post_name";
        $field[11]["fieldname"] = "职务";
        $field[11]["show"] = 1;
        $field[11]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 0;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => $ClassModel->errortip, 'result' => $result);

        }  
        ajax_return($res, $request['language_type']);

    }

    function classCourseInfoApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ClassModel = new \Model\Smc\ClassModel($request);
        $res = $ClassModel->classCourseInfo($request);
        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $ClassModel->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function classAddAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ClassModel = new \Model\Smc\ClassModel($request);
        $res = $ClassModel->classAdd($request);
        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '创建成功', 'result' => $result);
            $this->addSmcWorkLog($request['company_id'], $request['school_id'], $request['staffer_id'], "班务管理->班级管理->创建班级", '创建班级', dataEncode($request));
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $ClassModel->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    function classRefreshSortAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ClassModel = new \Model\Smc\ClassModel($request);
        $res = $ClassModel->classRefreshSort($request);
        $result = array();
        if ($res) {
            $res = array('error' => 0, 'errortip' => '刷新成功!', 'result' => $result);
            $this->addSmcWorkLog($request['company_id'], $request['school_id'], $request['staffer_id'], "班务管理->班级管理->刷新排课", '刷新排课', dataEncode($request));
        } else {
            $res = array('error' => 1, 'errortip' => $ClassModel->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    function classManageView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ClassModel = new \Model\Smc\ClassModel($request);
        $res = $ClassModel->classManage($request);
        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $ClassModel->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    function classEditAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ClassModel = new \Model\Smc\ClassModel($request);
        $res = $ClassModel->classEdit($request);
        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '编辑成功', 'result' => $result);
            $this->addSmcWorkLog($request['company_id'], $request['school_id'], $request['staffer_id'], "班务管理->班级管理", '编辑班级', dataEncode($request));
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $ClassModel->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function classDelAction()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ClassModel = new \Model\Smc\ClassModel($request);
        $res = $ClassModel->classDel($request);
        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '删除成功', 'result' => $result);
            $this->addSmcWorkLog($request['company_id'], $request['school_id'], $request['staffer_id'], "班务管理->班级管理", '删除班级', dataEncode($request));

        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $ClassModel->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function classScheduleAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
//        if($request['company_id'] !="1001"){
//			$res = array('error' => 1, 'errortip' =>"程序调试", 'result' => array());
//			ajax_return($res,$request['language_type']);
//		}

        $this->ThisVerify($request);
        $ClassModel = new \Model\Smc\ScheduleClassHourModel($request);
        $res = $ClassModel->classSchedule($request);
        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => $ClassModel->errortip, 'result' => $result);
            $this->addSmcWorkLog($request['company_id'], $request['school_id'], $request['staffer_id'], "班务管理->班级管理->创建班级->排课", '排课', dataEncode($request));
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $ClassModel->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function modifyLessonPlanAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $classModel = new \Model\Smc\ScheduleClassHourModel($request);
        $bool = $classModel->modifyLessonPlan($request);
        if ($bool) {
            $res = array('error' => 0, 'errortip' => '调整成功', 'result' => array());
            $this->addSmcWorkLog($request['company_id'], $request['school_id'], $request['staffer_id'], "班务管理->班级管理", '修改排课', dataEncode($request));
        } else {
            $res = array('error' => 1, 'errortip' => $classModel->errortip, 'result' => array());
        }
        ajax_return($res, $request['language_type']);
    }

    function againLessonPlanAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $classModel = new \Model\Smc\ScheduleClassHourModel($request);
        $bool = $classModel->againLessonPlan($request);
        if ($bool) {
            $res = array('error' => 0, 'errortip' => '调整成功', 'result' => array());
            $this->addSmcWorkLog($request['company_id'], $request['school_id'], $request['staffer_id'], "班务管理->班级管理", '修改排课(跳过节假日)', dataEncode($request));
        } else {
            $res = array('error' => 1, 'errortip' => $classModel->errortip, 'result' => array());
        }
        ajax_return($res, $request['language_type']);
    }

    function classChoiceTeacherApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ClassModel = new \Model\Smc\ClassModel($request);
        $res = $ClassModel->classChoiceTeacher($request);
        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $ClassModel->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    function classChoiceClassroomApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ClassModel = new \Model\Smc\ClassModel($request);
        $res = $ClassModel->classChoiceClassroom($request);
        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $ClassModel->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    function allClassTableView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ClassModel = new \Model\Smc\ClassModel($request);
        $dataList = $ClassModel->allClassTable($request);

        $result = array();
        $field = array();
        $field[0]["fieldname"] = "时间/教室";
        $field[0]["fieldstring"] = "noon_name";
        $field[0]["show"] = 1;
        $field[0]["custom"] = 0;
        $field[0]["is_title"] = 1;
        if ($dataList['roomList']) {
            foreach ($dataList['roomList'] as $key => $val) {
                $field[$key + 1]["fieldname"] = $val['classroom_cnname'];
                $field[$key + 1]["fieldstring"] = $val['classroom_cnname'];
                $field[$key + 1]["show"] = 1;
                $field[$key + 1]["custom"] = 0;
            }
        }


        $result['field'] = $field;
        if ($dataList) {
            $result["list"] = $dataList['data'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $ClassModel->errortip, 'result' => $result);

        }
        ajax_return($res, $request['language_type']);

    }


    function classCourseTableView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ClassModel = new \Model\Smc\ClassModel($request);
        $dataList = $ClassModel->classCourseTable($request);

        $result = array();
        $field = array();
        $field[0]["fieldname"] = "时间/教室";
        $field[0]["fieldstring"] = "noon_name";
        $field[0]["show"] = 1;
        $field[0]["custom"] = 0;
        $field[0]["is_title"] = 1;
        if ($dataList['roomList']) {
            foreach ($dataList['roomList'] as $key => $val) {
                $field[$key + 1]["fieldname"] = $val['classroom_cnname'];
                $field[$key + 1]["fieldstring"] = $val['classroom_cnname'];
                $field[$key + 1]["show"] = 1;
                $field[$key + 1]["custom"] = 0;
            }
        }


        $result['field'] = $field;
        if ($dataList) {
            $result["list"] = $dataList['data'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无课表需要点名上课', 'result' => $result);

        }
        ajax_return($res, $request['language_type']);

    }


    function classPercentageView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        if (!$request['class_id']) {
            $res = array('error' => 1, 'errortip' => '班级id的错误', 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        $this->ThisVerify($request);
        $ClassModel = new \Model\Smc\ClassModel($request);
        $result = $ClassModel->classPercentage($request);
        $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
        ajax_return($res, $request['language_type']);
    }


    function classStuBookingApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $classModel = new \Model\Smc\ClassModel($request);
        $list = $classModel->classStuBooking($request);

        $fieldstring = array('booking_id', 'student_cnname', 'student_enname', 'student_branch', 'hour_name', 'hour_during', 'staffer_cnname', 'booking_day', 'hour_ischecking_name', 'booking_status_name');
        $fieldname = array('ID', '学员中文名', '学员英文名', '学员编号', '预约课时', '预约时间', '教师', '预约日期', '是否考勤', '预约状态');
        $fieldcustom = array('0', '1', "1", "1", "1", "1", "1", "1", '1');
        $fieldshow = array('0', '1', "1", "1", "1", "1", "1", "1", '1');

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["fieldname"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result['fieldcustom'] = 1;
        $result['field'] = $field;

        if ($list['allnum']) {
            $result['allnum'] = $list['allnum'];
        } else {
            $result['allnum'] = 0;
        }

        if ($list['list']) {
            $result['list'] = $list['list'];
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => 1, 'errortip' => '班级暂无预约记录', 'result' => $result);
        }

        ajax_return($res, $request['language_type']);

    }

    //新增预约单人记录
    function addBookingAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $classModel = new \Model\Smc\ClassModel($request);
        $bool = $classModel->addBooking($request);
        if ($bool) {
            $res = array('error' => 0, 'errortip' => '新增成功', 'result' => array());
        } else {
            $res = array('error' => 1, 'errortip' => $classModel->errortip, 'result' => array());
        }
        ajax_return($res, $request['language_type']);
    }

    function addStuBookingAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        if (!$request['class_id'] || !$request['hour_id']) {
            $res = array('error' => 1, 'errortip' => "参数缺失", 'result' => array());
            ajax_return($res, $request['language_type']);
        }

        $classModel = new \Model\Smc\ClassModel($request);
        $bool = $classModel->addStuBooking($request);
        if ($bool) {
            $res = array('error' => 0, 'errortip' => '新增成功', 'result' => array());
            $this->addSmcWorkLog($request['company_id'], $request['school_id'], $request['staffer_id'], "班级管理->学员", '新增学员预约', dataEncode($request));
        } else {
            $res = array('error' => 1, 'errortip' => "新增失败", 'result' => array());
        }
        ajax_return($res, $request['language_type']);
    }

    //取消预约
    function cancelBookingAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        if (!$request['class_id'] || !$request['hour_id'] || !$request['student_id']) {
            $res = array('error' => 1, 'errortip' => "参数缺失", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        $classModel = new \Model\Smc\ClassModel($request);
        $bool = $classModel->cancelBooking($request);
        if ($bool) {
            $res = array('error' => 0, 'errortip' => '取消学员预约成功', 'result' => array());
            $this->addSmcWorkLog($request['company_id'], $request['school_id'], $request['staffer_id'], "班级管理->学员", '取消学员预约', dataEncode($request));
        } else {
            $res = array('error' => 1, 'errortip' => "取消学员预约失败", 'result' => array());
        }
        ajax_return($res, $request['language_type']);
    }

    //批量取消预约记录
    function batchCancelBookingAction()
    {

        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        if (!$request['booking_id']) {
            $res = array('error' => 1, 'errortip' => "参数缺失", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        $classModel = new \Model\Smc\ClassModel($request);
        $bool = $classModel->batchCancelBooking($request);
        if ($bool) {
            $res = array('error' => 0, 'errortip' => '取消学员预约成功', 'result' => array());
            $this->addSmcWorkLog($request['company_id'], $request['school_id'], $request['staffer_id'], "班级管理->学员", '取消学员预约', dataEncode($request));
        } else {
            $res = array('error' => 1, 'errortip' => "取消学员预约失败", 'result' => array());
        }
        ajax_return($res, $request['language_type']);
    }


    //预约课时的下拉
    function getClassHourNameApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $classModel = new \Model\Smc\ClassModel($request);
        $list = $classModel->getClassHourName($request);
        if ($list) {
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $list);
        } else {
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => array());
        }
        ajax_return($res, $request['language_type']);
    }

    function getPlanHourListApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $classModel = new \Model\Smc\ClassModel($request);
        $list = $classModel->getPlanHourList($request);
        $fieldstring = array('hour_id', 'hour_formerday', 'hour_formertimes', 'hour_formerweek', 'hour_day', 'hour_starttime', 'hour_endtime', 'staffer_cnname','hour_iswarming_name');
        $fieldname = array('课时序号', '原上课日期', '原上课时间', '上课星期', '上课日期', '开始时间', '结束时间', '教师','课次类型');
        $fieldcustom = array('1', '0', "0", "1", "1", "1", "1", "1","1");
        $fieldshow = array('1', '0', "0", "1", "1", "1", "1", "1","1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["fieldname"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result['field'] = $field;
        $result['list'] = $list['list'];
        $result['info'] = $list['info'];
        $res = array('error' => '0', 'errortip' => '获取信息', 'result' => $result);
        ajax_return($res, $request['language_type']);

    }

    function getLessonPlanListApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $classModel = new \Model\Smc\ClassModel($request);
        $list = $classModel->getLessonPlanList($request);
        $fieldstring = array('lessonplan_id', 'lessonplan_week', 'staffer_cnname', 'hour_way_name', 'classroom_cnname', 'time');
        $fieldname = array('序号', '星期', '原上课时间', '带课教师', '上课方式', '上课教室', '上课时间');
        $fieldcustom = array('0', '1', "1", "1", "1");
        $fieldshow = array('0', '1', "1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["fieldname"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result['field'] = $field;
        $result['list'] = $list['list'];
        $result['hour_last_day'] = $list['hour_last_day'];
        $result['child_hournum'] = $list['child_hournum'];
        $res = array('error' => '0', 'errortip' => '获取信息', 'result' => $result);
        ajax_return($res, $request['language_type']);
    }


    //无限排课
    function InfiniteClassScheduleView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $classModel = new \Model\Smc\ScheduleClassHourModel($request);
        $bool = $classModel->InfiniteClassSchedule($request);
        if ($bool) {
            $res = array('error' => 0, 'errortip' => '新增排课成功', 'result' => array());
            $this->addSmcWorkLog($request['company_id'], $request['school_id'], $request['staffer_id'], "班务管理->班级管理", '新增排课', dataEncode($request));
        } else {
            $res = array('error' => 1, 'errortip' => $classModel->errortip, 'result' => array());
        }
        ajax_return($res, $request['language_type']);
    }

    /**
     * 公开课新增排课
     * author: ling
     * 对应接口文档 0001
     * Date 2021/1/22 0022
     */
    function addOpenClassScheduleView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $classModel = new \Model\Smc\ScheduleClassHourModel($request);
        $bool = $classModel->addOpenClassSchedule($request);
        if ($bool) {
            $res = array('error' => 0, 'errortip' => '新增排课成功', 'result' => array());
            $this->addSmcWorkLog($request['company_id'], $request['school_id'], $request['staffer_id'], "班务管理->班级管理", '公开课新增排课', dataEncode($request));
        } else {
            $res = array('error' => 1, 'errortip' => $classModel->errortip, 'result' => array());
        }
        ajax_return($res, $request['language_type']);
    }

    function updateHourAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $classModel = new \Model\Smc\ClassModel($request);
        $bool = $classModel->updateHour($request);
        if ($bool) {
            $this->addSmcWorkLog($request['company_id'], $request['school_id'], $request['staffer_id'], "班务管理->班级管理->班级详情", '调整排课(教师)教室', dataEncode($request));
            $res = array('error' => 0, 'errortip' => '调整成功', 'result' => array());
        } else {
            $res = array('error' => 1, 'errortip' => "调整失败", 'result' => array());
        }
        ajax_return($res, $request['language_type']);
    }

    function updateClassHourAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $classModel = new \Model\Smc\ClassModel($request);
        $bool = $classModel->updateClassHourAction($request);
        if ($bool) {
            $this->addSmcWorkLog($request['company_id'], $request['school_id'], $request['staffer_id'], "班务管理->班级管理->班级详情", '调整排课(教师)教室', dataEncode($request));
            $res = array('error' => 0, 'errortip' => '调整成功', 'result' => array());
        } else {
            $res = array('error' => 1, 'errortip' => "调整失败", 'result' => array());
        }

        ajax_return($res, $request['language_type']);
    }

    function endClassAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $classModel = new \Model\Smc\ClassModel($request);
        $bool = $classModel->endClass($request);
        if ($bool) {
            $res = array('error' => 0, 'errortip' => '结班成功', 'result' => array());
            $this->addSmcWorkLog($request['company_id'], $request['school_id'], $request['staffer_id'], "班务管理->班级管理", '结班', dataEncode($request));
        } else {
            $res = array('error' => 1, 'errortip' => $classModel->errortip, 'result' => array());
        }
        ajax_return($res, $request['language_type']);
    }

    function halfEndClassAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $classModel = new \Model\Smc\ClassModel($request);
        $bool = $classModel->halfEndClass($request);
        if ($bool) {
            $res = array('error' => 0, 'errortip' => '申请成功', 'result' => array());
            $this->addSmcWorkLog($request['company_id'], $request['school_id'], $request['staffer_id'], "班务管理->班级管理", '申请中途拆班', dataEncode($request));
        } else {
            $res = array('error' => 1, 'errortip' => $classModel->errortip, 'result' => array());
        }
        ajax_return($res, $request['language_type']);
    }

    function classNewTimesApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ClassModel = new \Model\Smc\ClassModel($request);
        $res = $ClassModel->classNewTimes($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $ClassModel->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function getClassTimesApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ClassModel = new \Model\Smc\ClassModel($request);
        $res = $ClassModel->getClassTimes($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $ClassModel->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function advanceClassAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $classModel = new \Model\Smc\ClassModel($request);
        $bool = $classModel->advanceClass($request);
        if ($bool) {
            $res = array('error' => 0, 'errortip' => '提前入班成功', 'result' => array());
            $this->addSmcWorkLog($request['company_id'], $request['school_id'], $request['staffer_id'], "班务管理->班级管理", '提前入班', dataEncode($request));
        } else {
            $res = array('error' => 1, 'errortip' => $classModel->errortip, 'result' => array());
        }
        ajax_return($res, $request['language_type']);
    }

    /**
     * 获取需要补考勤的课次
     * author: ling
     * 对应接口文档 0001
     */
    function getStuClassCheckApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $classModel = new \Model\Smc\ClassModel($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = 'hour_day';
        $field[$k]["fieldname"] = '上课日期';
        $field[$k]["custom"] = 1;
        $field[$k]["show"] = 1;
        $k++;
        $field[$k]["fieldstring"] = 'hour_name';
        $field[$k]["fieldname"] = '课时名称';
        $field[$k]["custom"] = 1;
        $field[$k]["show"] = 1;
        $k++;
        $field[$k]["fieldstring"] = 'hour_time';
        $field[$k]["fieldname"] = '上课时间';
        $field[$k]["custom"] = 1;
        $field[$k]["show"] = 1;
        $k++;
        $field[$k]["fieldstring"] = 'hour_ischecking_name';
        $field[$k]["fieldname"] = '状态';
        $field[$k]["custom"] = 1;
        $field[$k]["show"] = 1;

        $k++;
        $field[$k]["fieldstring"] = 'hour_isfree_name';
        $field[$k]["fieldname"] = '是否计费';
        $field[$k]["custom"] = 1;
        $field[$k]["show"] = 1;
        $field[$k]["is_red"] = 1;


        $dataList = $classModel->getStuClassCheckApi($request);
        $result = array();
        $result['field'] = $field;

        $result['list'] = $dataList['list'];
        $result['num'] = $dataList['num'];

        $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);

        ajax_return($res, $request['language_type']);

    }


    /**
     * 期度类班级排课
     * author: ling
     * 对应接口文档 0001
     *
     */
    function scheduleDurationClassAction()
    {

        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $classModel = new \Model\Smc\ClassModel($request);
        $bool = $classModel->scheduleDurationClass($request);
        if ($bool) {
            $res = array('error' => 0, 'errortip' => '排课成功', 'result' => array());
            $this->addSmcWorkLog($request['company_id'], $request['school_id'], $request['staffer_id'], "班务管理->期度类班级", '排课', dataEncode($request));
        } else {
            $res = array('error' => 1, 'errortip' => $classModel->errortip, 'result' => array());
        }
        ajax_return($res, $request['language_type']);

    }


    function addDurationHourAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $classModel = new \Model\Smc\ClassModel($request);
        $bool = $classModel->addDurationHour($request);
        if ($bool) {
            $res = array('error' => 0, 'errortip' => '增加课次成功', 'result' => array());
            $this->addSmcWorkLog($request['company_id'], $request['school_id'], $request['staffer_id'], "班务管理->期度类班级", '增加课次', dataEncode($request));
        } else {
            $res = array('error' => 1, 'errortip' => $classModel->errortip, 'result' => array());
        }
        ajax_return($res, $request['language_type']);
    }


    /**
     * 获取课次的详情
     * author: ling
     * 对应接口文档 0001
     */
    function getHourOneApi()
    {

        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $classModel = new \Model\Smc\ClassModel($request);


        $dataOne = $classModel->getHourOneApi($request);

        $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $dataOne);

        ajax_return($res, $request['language_type']);

    }

    /**
     *  期度类班级 ,月结列表
     * author: ling
     * 对应接口文档 0001
     */
//    function durationSettleListView(){
//
//        $request = Input('get.','','trim,addslashes');
//        $this->ThisVerify($request);
//        $classModel = new \Model\Smc\ClassModel($request);
//
//        $field = array();
//        $k = 0;
//        $field[$k]["fieldstring"] = 'student_cnname';
//        $field[$k]["fieldname"] = '中文名';
//        $field[$k]["custom"] = 1;
//        $field[$k]["show"] = 1;
//        $field[$k]["ismethod"] = 1;
//        $k++;
//        $field[$k]["fieldstring"] = 'student_enname';
//        $field[$k]["fieldname"] = '英文名';
//        $field[$k]["custom"] = 1;
//        $field[$k]["show"] = 1;
//        $k++;
//        $field[$k]["fieldstring"] = 'student_branch';
//        $field[$k]["fieldname"] = '学员编号';
//        $field[$k]["custom"] = 1;
//        $field[$k]["show"] = 1;
//        $k++;
//        $field[$k]["fieldstring"] = 'student_sex';
//        $field[$k]["fieldname"] = '性别';
//        $field[$k]["custom"] = 1;
//        $field[$k]["show"] = 1;
//
//        $k++;
//        $field[$k]["fieldstring"] = '';
//        $field[$k]["fieldname"] = '应到';
//        $field[$k]["custom"] = 1;
//        $field[$k]["show"] = 1;
//        $k++;
//        $field[$k]["fieldstring"] = '';
//        $field[$k]["fieldname"] = '实到';
//        $field[$k]["custom"] = 1;
//        $field[$k]["show"] = 1;
//        $k++;
//        $field[$k]["fieldstring"] = 'hourstudy_nochecknum';
//        $field[$k]["fieldname"] = '缺勤';
//        $field[$k]["custom"] = 1;
//        $field[$k]["show"] = 1;
//
//
//        $dataList = $classModel->durationSettleList($request);
//        $result = array();
//        $result['field'] = $field;
//
//        $result['list'] = $dataList['list'];
//        $result['allnum'] = $dataList['allnum'];
//        if($dataList){
//            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
//        }else{
//            $res = array('error' => 1, 'errortip' => '暂无需要月结的记录', 'result' => $result);
//        }
//        ajax_return($res,$request['language_type']);
//    }

    /**
     * 期度类 获取学员考勤 一个月
     * author: ling
     * 对应接口文档 0001
     */
    function getDurationStuHourstudyApi()
    {

        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $classModel = new \Model\Smc\ClassModel($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = 'hour_day';
        $field[$k]["fieldname"] = '日期';
        $field[$k]["custom"] = 1;
        $field[$k]["show"] = 1;
        $k++;
        $field[$k]["fieldstring"] = 'hourstudy_checkin_name';
        $field[$k]["fieldname"] = '出勤状态';
        $field[$k]["custom"] = 1;
        $field[$k]["show"] = 1;
        $k++;
        $field[$k]["fieldstring"] = 'clockinginlog_note';
        $field[$k]["fieldname"] = '缺勤原因';
        $field[$k]["custom"] = 1;
        $field[$k]["show"] = 1;


        $dataList = $classModel->getDurationStuHourstudy($request);
        $result = array();
        $result['field'] = $field;
        $result['list'] = $dataList['list'];
        $result['info'] = $dataList['info'];

        if ($dataList) {
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
        } else {
            $res = array('error' => 1, 'errortip' => '暂无学员的考勤记录', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    function durationSettleListView()
    {

        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\ClassModel($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = 'student_id';
        $field[$k]["fieldname"] = '学员ID';
        $field[$k]["custom"] = 0;
        $field[$k]["show"] = 0;
        $k++;
        $field[$k]["fieldstring"] = 'student_cnname';
        $field[$k]["fieldname"] = '学员中文名';
        $field[$k]["custom"] = 1;
        $field[$k]["show"] = 1;
        $field[$k]["ismethod"] = 1;
        $k++;
        $field[$k]["fieldstring"] = 'student_enname';
        $field[$k]["fieldname"] = '学员英文名';
        $field[$k]["custom"] = 1;
        $field[$k]["show"] = 1;
        $k++;
        $field[$k]["fieldstring"] = 'student_branch';
        $field[$k]["fieldname"] = '学员编号';
        $field[$k]["custom"] = 1;
        $field[$k]["show"] = 1;
        $k++;
        $field[$k]["fieldstring"] = 'student_sex';
        $field[$k]["fieldname"] = '性别';
        $field[$k]["custom"] = 1;
        $field[$k]["show"] = 1;

        $k++;
        $field[$k]["fieldstring"] = 'hourNum';
        $field[$k]["fieldname"] = '应到';
        $field[$k]["custom"] = 1;
        $field[$k]["show"] = 1;
        $k++;
        $field[$k]["fieldstring"] = 'attendanceNum';
        $field[$k]["fieldname"] = '实到';
        $field[$k]["custom"] = 1;
        $field[$k]["show"] = 1;
        $k++;
        $field[$k]["fieldstring"] = 'hourstudy_nochecknum';
        $field[$k]["fieldname"] = '缺勤';
        $field[$k]["custom"] = 1;
        $field[$k]["show"] = 1;


        $dataList = $Model->classSettlementList($request);
        $result = array();
        $result['field'] = $field;

        if (!$dataList['allnum']) {
            $result['allnum'] = '0';
        } else {
            $result['allnum'] = $dataList['allnum'];
        }

        if (!$dataList['list']) {
            $result['list'] = array();
        } else {
            $result['list'] = $dataList['list'];
        }

        if (!$dataList['can_settle']) {
            $result['can_settle'] = '-1';
        } else {
            $result['can_settle'] = $dataList['can_settle'];
        }
        if ($dataList) {
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
        } else {
            $res = array('error' => 1, 'errortip' => '暂无需要月结的记录', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function settlementAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ClassModel = new \Model\Smc\ClassModel($request);
        $res = $ClassModel->settlement($request);
        $result = array();
        if ($res) {
            $res = array('error' => 0, 'errortip' => '结算成功!', 'result' => $result);
            $this->addSmcWorkLog($request['company_id'], $request['school_id'], $request['staffer_id'], "班务管理->班级管理->月度结算", '月度结算', dataEncode($request));
        } else {
            $res = array('error' => 1, 'errortip' => $ClassModel->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function checkSettlementApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ClassModel = new \Model\Smc\ClassModel($request);
        $res = $ClassModel->checkSettlement($request);
        if ($res) {
            $res = array('error' => 0, 'errortip' => '获取成功!', 'result' => $res);
        } else {
            $res = array('error' => 1, 'errortip' => $ClassModel->errortip, 'result' => array());
        }
        ajax_return($res, $request['language_type']);
    }

    function getSettlementMonthListApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $OrderModel = new \Model\Smc\ClassModel($request);
        $res = $OrderModel->getSettlementMonthList($request);
        $field = array();
        $k = 0;


        if (isset($request['from']) && $request['from'] == 1) {
            $field[$k]["fieldname"] = "class_cnname";
            $field[$k]["fieldstring"] = "班级名称";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldname"] = "class_enname";
            $field[$k]["fieldstring"] = "班级别名";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldname"] = "class_branch";
            $field[$k]["fieldstring"] = "班级编号";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;
        }


        $field[$k]["fieldname"] = "mon";
        $field[$k]["fieldstring"] = "结算月份";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "hourNum";
        $field[$k]["fieldstring"] = "已排课时";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "share_settle_price";
        $field[$k]["fieldstring"] = "原结算金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "share_confirm_price";
        $field[$k]["fieldstring"] = "实际结算金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

//        $field[$k]["fieldname"] = "surplus_price";
//        $field[$k]["fieldstring"] = "结算剩余金额";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//        $k++;

        $field[$k]["fieldname"] = "status_name";
        $field[$k]["fieldstring"] = "结算状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "notsettleNum";
        $field[$k]["fieldstring"] = "未结算人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "settleNum";
        $field[$k]["fieldstring"] = "已结算人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();

        $result["field"] = $field;

        if ($res) {
            $result["list"] = $res['list'];
            $result['allnum'] = $res['allnum'];

            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result['allnum'] = 0;
            $res = array('error' => 1, 'errortip' => $OrderModel->errortip, 'result' => $result);

        }
        ajax_return($res, $request['language_type']);
    }

    function getSettlementInfoApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ClassModel = new \Model\Smc\ClassModel($request);
        $res = $ClassModel->getSettlementInfo($request);
        $field = array();
        $k = 0;

        $field[$k]["fieldname"] = "student_id";
        $field[$k]["fieldstring"] = "学员ID";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "student_cnname";
        $field[$k]["fieldstring"] = "学员中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "student_enname";
        $field[$k]["fieldstring"] = "学员英文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "student_branch";
        $field[$k]["fieldstring"] = "学员编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "student_sex";
        $field[$k]["fieldstring"] = "性别";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "hourNum";
        $field[$k]["fieldstring"] = "应到";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "attendanceNum";
        $field[$k]["fieldstring"] = "实到";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "absenceNum";
        $field[$k]["fieldstring"] = "缺勤";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "courseshare_price";
        $field[$k]["fieldstring"] = "原分摊金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "price";
        $field[$k]["fieldstring"] = "实际分摊金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

//        $field[$k]["fieldname"] = "surplus_price";
//        $field[$k]["fieldstring"] = "剩余金额";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//        $k++;

        $field[$k]["fieldname"] = "status_name";
        $field[$k]["fieldstring"] = "处理状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "tip";
        $field[$k]["fieldstring"] = "备注";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;


        $result = array();

        $result["field"] = $field;
        if ($res) {
            $result["list"] = $res['list'];
            $result["month_status"] = $res['status'];
            $result["classOne"] = $res['classOne'];
            $res = array('error' => 0, 'errortip' => '获取成功!', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $ClassModel->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function submitSettlementAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ClassModel = new \Model\Smc\ClassModel($request);
        $res = $ClassModel->submitSettlement($request);
        if ($res) {
            $res = array('error' => 0, 'errortip' => '结算成功!', 'result' => $res);
        } else {
            $res = array('error' => 1, 'errortip' => $ClassModel->errortip, 'result' => array());
        }
        ajax_return($res, $request['language_type']);
    }

    /**
     * 更新课时计划
     * author: ling
     * 对应接口文档 0001
     */
    function updateHourPlanAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ClassModel = new \Model\Smc\ClassModel($request);
        $ClassModel->updateHourPlan($request);
        $res = array('error' => $ClassModel->error, 'errortip' => $ClassModel->errortip, 'result' => array());
        $this->addSmcWorkLog($request['company_id'], $request['school_id'], $request['staffer_id'], "班务管理->班级详情->班级课表", '更新课时计划', dataEncode($request));
        ajax_return($res, $request['language_type']);
    }

    /**
     * 创建子班
     * author: ling
     * 对应接口文档 0001
     */
    function createChildClassAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ClassModel = new \Model\Smc\ClassModel($request);
        $id = $ClassModel->createChildClass($request);
        if ($id) {
            $result = array();
            $result['class_id'] = $id;
            $this->addSmcWorkLog($request['company_id'], $request['school_id'], $request['staffer_id'], "班务管理->班级详情->创建子班", '创建子班', dataEncode($request));
            $res = array('error' => $ClassModel->error, 'errortip' => $ClassModel->errortip, 'result' => $result);
        } else {
            $res = array('error' => $ClassModel->error, 'errortip' => $ClassModel->errortip, 'result' => array());
        }
        ajax_return($res, $request['language_type']);
    }

    /**
     * 编辑子班级
     * author: ling
     * 对应接口文档 0001
     */
    function editChildClassAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ClassModel = new \Model\Smc\ClassModel($request);
        $ClassModel->editChildClass($request);
        $this->addSmcWorkLog($request['company_id'], $request['school_id'], $request['staffer_id'], "班务管理->班级详情->编辑子班", '编辑子班', dataEncode($request));
        $res = array('error' => $ClassModel->error, 'errortip' => $ClassModel->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    /**
     * 子班排课时获取教师
     * author: ling
     * 对应接口文档 0001
     */
    function getChildClassTeacherApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ClassModel = new \Model\Smc\ClassModel($request);
        $dataList = $ClassModel->getChildClassTeacher($request);
        if ($dataList) {
            $result = array();
            $result['list'] = $dataList;
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
        } else {
            $res = array('error' => 1, 'errortip' => '暂无数据', 'result' => array());
        }
        ajax_return($res, $request['language_type']);

    }

    /**
     * 子班排课时,获取子班的排课方式
     * author: ling
     * 对应接口文档 0001
     * Date 2021/2/25 0025
     */
    function  getChildClassWayApi(){
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ClassModel = new \Model\Smc\ClassModel($request);
        $dataList = $ClassModel->getChildClassWayApi($request);
        if ($dataList) {
            $result = array();
            $result['data'] = $dataList['data'];
            $result['list'] = $dataList['list'];
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
        } else {
            $res = array('error' => 1, 'errortip' => '暂无数据', 'result' => array());
        }
        ajax_return($res, $request['language_type']);
    }

    /**
     *  获取父班的子班列表
     * author: ling
     * 对应接口文档 0001
     */
    function getClassChildCLassView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ClassModel = new \Model\Smc\ClassModel($request);
        $dataList = $ClassModel->getClassChildCLass($request);
        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "class_id";
        $field[$k]["fieldname"] = "班级id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;
        $field[$k]["fieldstring"] = "class_cnname";
        $field[$k]["fieldname"] = "子班名称";
        $field[$k]["ismethod"] = 1;
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;
        $field[$k]["fieldstring"] = "class_enname";
        $field[$k]["fieldname"] = "子班别名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;
        $field[$k]["fieldstring"] = "class_branch";
        $field[$k]["fieldname"] = "子班编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;
        $field[$k]["fieldstring"] = "student_num";
        $field[$k]["fieldname"] = "人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;
        $field[$k]["fieldstring"] = "class_timestr";
        $field[$k]["fieldname"] = "上课时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;
        $field[$k]["fieldstring"] = "cnteacher";
        $field[$k]["fieldname"] = "主教教师";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;
        $field[$k]["fieldstring"] = "info";
        $field[$k]["fieldname"] = "已上/计划";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["isCircleProgress"] = true;
        $field[$k]["isSquareProgress"] = false;
        $k++;
        $field[$k]["fieldstring"] = "cnteacher";
        $field[$k]["fieldname"] = "主教教师";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;

        $result = array();
        $result['field'] = $field;

        if ($dataList) {
            $result['list'] = $dataList['list'];
            $result['allnum'] = $dataList['allnum'];
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
        } else {
            $res = array('error' => 1, 'errortip' => '暂无数据', 'result' => array());
        }
        ajax_return($res, $request['language_type']);
    }

    function getAllClassApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $datawhere = " 1 ";
        if (isset($request['class_status']) && $request['class_status'] != '') {
            $datawhere .= " class_status='{$request['class_status']}'";
        }
        $sql = "select class_branch
              from smc_class
              where {$datawhere} and company_id='8888' and substring(class_branch,1,7)>'20200101'";

        $classList = $this->DataControl->selectClear($sql);

        $result = array();
        if ($classList) {
            $classList = array_column($classList, 'class_branch');
            $result['list'] = $classList;
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
        } else {
            $res = array('error' => 1, 'errortip' => '暂无数据', 'result' => array());
        }
        ajax_return($res, $request['language_type']);
    }

    function setFictitiousAction()
    {

        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\ClassModel($request);
        $res = $Model->setFictitious($request);
        $result = array();
        if ($res) {
            $res = array('error' => 0, 'errortip' => '设置成功!', 'result' => $result);
        } else {
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    /**
     * 获取可以转入的子班级
     * author: ling
     * 对应接口文档 0001
     */
    function getClassChildCLassApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ClassModel = new \Model\Smc\ClassModel($request);
        $dataList = $ClassModel->getClassChildCLassApi($request);
        $result = array();
        if ($dataList) {
            $result['list'] = $dataList;

            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
        } else {
            $res = array('error' => 1, 'errortip' => '暂无数据', 'result' => array());
        }
        ajax_return($res, $request['language_type']);
    }

    /**
     * 清除排课
     * author: ling
     * 对应接口文档 0001
     */
    function delClassHourAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ClassModel = new \Model\Smc\ClassModel($request);
        $ClassModel->delClassHour($request);
        $this->addSmcWorkLog($request['company_id'], $request['school_id'], $request['staffer_id'], "班务管理->班级详情->编辑子班", '编辑子班', dataEncode($request));
        $res = array('error' => $ClassModel->error, 'errortip' => $ClassModel->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    /**
     * 更新课时计划
     * author: ling
     * 对应接口文档 0001
     */
    function toHourWayAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ClassModel = new \Model\Smc\ClassModel($request);
        $ClassModel->toHourWay($request);
        $this->addSmcWorkLog($request['company_id'], $request['school_id'], $request['staffer_id'], "班务管理->班级详情->转为线上课", '转为线上课', dataEncode($request));
        $res = array('error' => $ClassModel->error, 'errortip' => $ClassModel->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);

    }

    /**
     * 更新课时计划
     * author: ling
     * 对应接口文档 0001
     */
    function toClassHourWayAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ClassModel = new \Model\Smc\ClassModel($request);
        $result = $ClassModel->toClassHourWayAction($request);
        $this->addSmcWorkLog($request['company_id'], $request['school_id'], $request['staffer_id'], "班务管理->班级详情->转为线上课", '转为线上课', dataEncode($request));
        ajax_return($result, $request['language_type']);
    }

    /**
     * 一键延后
     * author: ling
     * 对应接口文档 0001
     */
    function DelayClassHourAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ClassModel = new \Model\Smc\ScheduleClassHourModel($request);
        $ClassModel->DelayClassHour($request);
        $this->addSmcWorkLog($request['company_id'], $request['school_id'], $request['staffer_id'], "班务管理->班级详情->一键延后", '一键延后', dataEncode($request));
        $res = array('error' => $ClassModel->error, 'errortip' => $ClassModel->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    function addChildClassHourAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ClassModel = new \Model\Smc\ScheduleClassHourModel($request);
        $ClassModel->addChildClassHour($request);
        $this->addSmcWorkLog($request['company_id'], $request['school_id'], $request['staffer_id'], "班务管理->班级详情->子班增加排课", '子班增加排课', dataEncode($request));
        $res = array('error' => $ClassModel->error, 'errortip' => $ClassModel->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //学校班级名称预设 -- 列表
    function getCoursePresupListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);

        $ClassModel = new \Model\Smc\ClassModel($request);
        $dataList = $ClassModel->getCoursePresupList($request);
        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "presup_id";
        $field[$k]["fieldname"] = "班级id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;
        $field[$k]["fieldstring"] = "presup_type";
        $field[$k]["fieldname"] = "班级类型ID";
        $field[$k]["ismethod"] = 1;
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;
        $field[$k]["fieldstring"] = "presup_name";
        $field[$k]["fieldname"] = "名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;
        $field[$k]["fieldstring"] = "presup_type_namme";
        $field[$k]["fieldname"] = "名称类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $result = array();
        $result['field'] = $field;

        $result = array();
        $result['field'] = $field;
        $result['list'] = $ClassModel->result['list'];
        $result['allnum'] = $ClassModel->result['allnum'];

        ajax_return(array('error' => $ClassModel->error, 'errortip' => $ClassModel->errortip, 'result' => $result));

    }

    //学校班级名称预设 -- 添加
    function addCoursePresupAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);

        $ClassModel = new \Model\Smc\ClassModel($request);
        $dataList = $ClassModel->addCoursePresupAction($request);

        $field = array();

        $result = array();
        $result['field'] = $field;
        $result['list'] = $ClassModel->result;

        ajax_return(array('error' => $ClassModel->error, 'errortip' => $ClassModel->errortip, 'result' => $result));

    }

    //学校班级名称预设 -- 查看
    function getCoursePresupOneApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);

        $ClassModel = new \Model\Smc\ClassModel($request);
        $dataList = $ClassModel->getCoursePresupOneApi($request);

        $field = array();
        $field['presup_type'] = '分类 0未知 1 班级名 2 班级别名';
        $field['presup_typename'] = '分类名称 0未知 1 班级名 2 班级别名';
        $field['presup_name'] = '课程名称';

        $result = array();
        $result['field'] = $field;
        $result['list'] = $ClassModel->result;

        ajax_return(array('error' => $ClassModel->error, 'errortip' => $ClassModel->errortip, 'result' => $result));

    }

    //学校班级名称预设 -- 编辑
    function editCoursePresupAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);

        $ClassModel = new \Model\Smc\ClassModel($request);
        $dataList = $ClassModel->editCoursePresupAction($request);

        $field = array();
        $field['presup_type'] = '分类 0未知 1 班级名 2 班级别名';
        $field['presup_name'] = '课程名称';
        $field['presup_id'] = '预设课程名称ID';

        $result = array();
        $result['field'] = $field;
        $result['list'] = $ClassModel->result;

        ajax_return(array('error' => $ClassModel->error, 'errortip' => $ClassModel->errortip, 'result' => $result));

    }

    //学校班级名称预设 -- 删除
    function delCoursePresupAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);

        $ClassModel = new \Model\Smc\ClassModel($request);
        $dataList = $ClassModel->delCoursePresupAction($request);

        $field = array();

        $result = array();
        $result['field'] = $field;
        $result['list'] = $ClassModel->result;

        ajax_return(array('error' => $ClassModel->error, 'errortip' => $ClassModel->errortip, 'result' => $result));

    }

    function previewStudentView()
    {
        $request = Input('post.', '', 'strip_tags');
        $this->ThisVerify($request);//验证账户
        $url = $request['url'];

        $ys_array = array('学员中文名' => 'student_cnname', '学员英文名' => 'student_enname', '性别' => 'student_sex', '出生日期' => 'student_birthday', '联系人' => 'parenter_cnname', '联系电话' => 'parenter_mobile', '家长与幼儿关系' => 'family_relation', '证件号码' => 'student_idcard', '班级编号' => 'class_branch', '入班日期' => 'study_beginday');
        $options=array(
            "ssl"=>array(
                "verify_peer"=>false,
                "verify_peer_name"=>false,
            ),
        );
        file_put_contents('analysis.xls', file_get_contents($url,false,stream_context_create($options)));
        $sqlarray = execl_to_array("analysis.xls", $ys_array);
        array_shift($sqlarray);

        if ($sqlarray) {
            foreach ($sqlarray as &$one) {
                if ($one['student_birthday']) {
                    $one['student_birthday'] = addslashes(trim(date("Y-m-d", \PHPExcel_Shared_Date::ExcelToPHP($one['student_birthday']))));
                }
                if ($one['study_beginday']) {
                    $one['study_beginday'] = addslashes(trim(date("Y-m-d", \PHPExcel_Shared_Date::ExcelToPHP($one['study_beginday']))));
                }
            }
        }

        $field = array();
        $k = 0;

        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["fieldname"] = "学员中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_enname";
        $field[$k]["fieldname"] = "学员英文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_sex";
        $field[$k]["fieldname"] = "性别";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_birthday";
        $field[$k]["fieldname"] = "出生日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "parenter_cnname";
        $field[$k]["fieldname"] = "联系人";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "parenter_mobile";
        $field[$k]["fieldname"] = "联系电话";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "family_relation";
        $field[$k]["fieldname"] = "家长与幼儿关系";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_idcard";
        $field[$k]["fieldname"] = "证件号码";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_branch";
        $field[$k]["fieldname"] = "班级编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "study_beginday";
        $field[$k]["fieldname"] = "入班日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;
        if ($sqlarray) {
            $result["list"] = $sqlarray;
            $res = array('error' => 0, 'errortip' => '预览成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '文件不存在', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function ImportStudentView()
    {
        $request = Input('post.', '', 'strip_tags');
        $this->ThisVerify($request);//验证账户
        $url = $request['url'];

        $ys_array = array('学员中文名' => 'student_cnname', '学员英文名' => 'student_enname', '性别' => 'student_sex', '出生日期' => 'student_birthday', '联系人' => 'parenter_cnname', '联系电话' => 'parenter_mobile', '家长与幼儿关系' => 'family_relation', '证件号码' => 'student_idcard', '班级编号' => 'class_branch', '入班日期' => 'study_beginday');

        $options=array(
            "ssl"=>array(
                "verify_peer"=>false,
                "verify_peer_name"=>false,
            ),
        );
        file_put_contents('analysis.xls', file_get_contents($url,false,stream_context_create($options)));
        $sqlarray = execl_to_array("analysis.xls", $ys_array);
        array_shift($sqlarray);

        $Model = new \Model\Smc\ClassModel($request);
        $res = $Model->ImportStudent($request, $sqlarray);

        $field = array();
        $k = 0;

        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["fieldname"] = "学员中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_enname";
        $field[$k]["fieldname"] = "学员英文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_sex";
        $field[$k]["fieldname"] = "性别";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_birthday";
        $field[$k]["fieldname"] = "出生日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "parenter_cnname";
        $field[$k]["fieldname"] = "联系人";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "parenter_mobile";
        $field[$k]["fieldname"] = "联系电话";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "family_relation";
        $field[$k]["fieldname"] = "家长与幼儿关系";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_idcard";
        $field[$k]["fieldname"] = "证件号码";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_branch";
        $field[$k]["fieldname"] = "班级编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "study_beginday";
        $field[$k]["fieldname"] = "入班日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "reason";
        $field[$k]["fieldname"] = "原因";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["is_red"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;
        if ($res) {
            $result["t_num"] = $res['t_num'];
            $result["f_num"] = $res['f_num'];
            $result["errorlog_id"] = $res['errorlog_id'];
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '导入成功', 'result' => $result);
        } else {
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function exportErrorStudentView()
    {
        $request = Input('get.', '', 'strip_tags,trim');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\ClassModel($request);
        $res = $Model->exportErrorStudent($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    function getImportStudentApi()
    {
        $request = Input('get.', '', 'strip_tags');
        $this->ThisVerify($request);//验证账户
        $result = 'https://smcapi.kedingdang.com/importexcel/smc/学员导入模板.xlsx';
        $res = array('error' => '0', 'errortip' => '下载导入模版成功', 'result' => $result);
        ajax_return($res, $request['language_type']);
    }

    function getSourceClassListApi()
    {
        $request = Input('get.', '', 'strip_tags,trim');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\ClassModel($request);
        $res = $Model->getSourceClassList($request);
        $field = array();
        $k = 0;

        $field[$k]["fieldstring"] = "class_id";
        $field[$k]["fieldname"] = "班级ID";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_cnname";
        $field[$k]["fieldname"] = "班级名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_enname";
        $field[$k]["fieldname"] = "班级别名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_branch";
        $field[$k]["fieldname"] = "班级编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "course_cnname";
        $field[$k]["fieldname"] = "课程别名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "course_branch";
        $field[$k]["fieldname"] = "课程别编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;

        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    function addSourceClassAction()
    {
        $request = Input('post.', '', 'strip_tags,trim');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\ClassModel($request);
        $res = $Model->addSourceClass($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '添加成功', 'result' => $result);
            $this->addSmcWorkLog($request['company_id'], $request['school_id'], $request['staffer_id'], "班务管理->班级管理->班级轨迹", '来源班级', dataEncode($request));
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    function getClassPromotionListApi()
    {
        $request = Input('get.', '', 'strip_tags,trim');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\ClassModel($request);
        $res = $Model->getClassPromotionList($request);
        $field = array();
        $k = 0;

        $field[$k]["fieldstring"] = "class_id";
        $field[$k]["fieldname"] = "班级ID";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_cnname";
        $field[$k]["fieldname"] = "班级名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_enname";
        $field[$k]["fieldname"] = "班级别名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_branch";
        $field[$k]["fieldname"] = "班级编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "course_cnname";
        $field[$k]["fieldname"] = "课程别名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "course_branch";
        $field[$k]["fieldname"] = "课程别编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "lastNum";
        $field[$k]["fieldname"] = "班级人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "mainTeacher";
        $field[$k]["fieldname"] = "主教老师";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "auxiTeacher";
        $field[$k]["fieldname"] = "助教老师";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;


        $result = array();
        $result["field"] = $field;
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    function submitOpenApplyAction()
    {
        $request = Input('post.', '', 'strip_tags,trim');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\ClassModel($request);
        $res = $Model->submitOpenApply($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '申请成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    function applyFreeHourAction()
    {
        $request = Input('post.', '', 'strip_tags,trim');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\ClassModel($request);
        $res = $Model->applyFreeHour($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '申请成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }


    function getNeedConfirmOpenClassListApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $ClassModel = new \Model\Smc\ClassModel($request);
        $res = $ClassModel->getNeedConfirmOpenClassList($request);
        $field = array();

        $k = 0;

        $field[$k]["fieldstring"] = "class_cnname";
        $field[$k]["fieldname"] = "班级名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["ismethod"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_enname";
        $field[$k]["fieldname"] = "班级别名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_branch";
        $field[$k]["fieldname"] = "班级编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "stuNum";
        $field[$k]["fieldname"] = "人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "course_cnname";
        $field[$k]["fieldname"] = "课程别名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "course_branch";
        $field[$k]["fieldname"] = "课程别编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "sonTeacher";
        $field[$k]["fieldname"] = "教师";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_timestr";
        $field[$k]["fieldname"] = "上课时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "timerange";
        $field[$k]["fieldname"] = "开班结班日期";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;


        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => $ClassModel->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    function batchConfirmOpenClassAction()
    {
        $request = Input('post.', '', 'strip_tags,trim');
        $Model = new \Model\Smc\ClassModel($request);
        $res = $Model->batchConfirmOpenClass($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '开班成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }




}
