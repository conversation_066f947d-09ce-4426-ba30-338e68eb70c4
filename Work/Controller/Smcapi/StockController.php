<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/13
 * Time: 23:25
 */

namespace Work\Controller\Smcapi;


class StockController extends viewTpl
{
    public $u;
    public $t;
    public $c;
    public $visitType = "api";
    public $DataControl;
    public $Model;

    //预加载处理类
    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();

        //数据库操作
        $this->DataControl = new \Dbmysql();

    }

    //获取货品列表
    function getGoodsListView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\StockModel($request);

        $result = $Model->getGoodsList($request);
        ajax_return($result,$request['language_type']);
    }


    //查看货品详情
    function getGoodsDetailView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\StockModel($request);

        $result = $Model->getGoodsDetail($request);
        ajax_return($result,$request['language_type']);
    }

    //获取货品价格变更列表
    function getGoodsPriceListView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\StockModel($request);

        $result = $Model->getGoodsPriceList($request);
        ajax_return($result,$request['language_type']);
    }

    //获取新增采购商品列表
    function getBuyGoodsListView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\StockModel($request);

        $result = $Model->getBuyGoodsList($request);
        ajax_return($result,$request['language_type']);
    }

    //创建订单
    function CreateProorderAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\StockModel($request);

        $result = $Model->CreateProorderAction($request);
        ajax_return($result,$request['language_type']);
    }

    //借调审核
    function transferbuyAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\StockModel($request);

        $result = $Model->transferbuyAction($request);
        ajax_return($result,$request['language_type']);
    }

    //采购管理列表
    function getProGoodsListView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\StockModel($request);

        $result = $Model->getProGoodsList($request);
        ajax_return($result,$request['language_type']);
    }

    //审核学校采购订单
    function ExamineScAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\StockModel($request);

        $result = $Model->ExamineScAction($request);
        ajax_return($result,$request['language_type']);
    }

    //采购货品明细
    function getGoodsDetailListView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\StockModel($request);

        $result = $Model->getGoodsDetailList($request);
        ajax_return($result,$request['language_type']);
    }

    //采购单详情
    function getProorderDetailView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\StockModel($request);

        $result = $Model->getProorderDetail($request);
        ajax_return($result,$request['language_type']);
    }

    //采购单跟踪记录
    function getGoodsTracksListView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\StockModel($request);

        $result = $Model->getGoodsTracksList($request);
        ajax_return($result,$request['language_type']);
    }

    //采购货品明细
    function getGoodsDetailOneView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\StockModel($request);

        $result = $Model->getGoodsDetailOne($request);
        ajax_return($result,$request['language_type']);
    }

    //删除采购货品
    function DelGoodsAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\StockModel($request);

        $result = $Model->DelGoodsAction($request);
        ajax_return($result,$request['language_type']);
    }


    //新增采购货品
    function addGoodsAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\StockModel($request);

        $result = $Model->addGoodsAction($request);
        ajax_return($result,$request['language_type']);
    }

    //入库单
    function getBeinorderListView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\StockModel($request);

        $result = $Model->getBeinorderList($request);
        ajax_return($result,$request['language_type']);
    }

    //入库单商品明细
    function getbeinorderGoodsListView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\StockModel($request);

        $result = $Model->getbeinorderGoodsList($request);
        ajax_return($result,$request['language_type']);
    }

    //入库单资料
    function getBeinorderInfoView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\StockModel($request);

        $result = $Model->getBeinorderInfo($request);
        ajax_return($result,$request['language_type']);
    }

    //出库单资料
    function getBeoutorderInfoView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\StockModel($request);

        $result = $Model->getBeoutorderInfo($request);
        ajax_return($result,$request['language_type']);
    }


    //入库货品明细
    function getInGoodsListView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\StockModel($request);

        $result = $Model->getInGoodsList($request);
        ajax_return($result,$request['language_type']);
    }

    //出库单
    function getBeoutorderListView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\StockModel($request);

        $result = $Model->getBeoutorderList($request);
        ajax_return($result,$request['language_type']);
    }

    //出库货品明细
    function getOutGoodsListView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\StockModel($request);

        $result = $Model->getOutGoodsList($request);
        ajax_return($result,$request['language_type']);
    }

    //调拨分校列表
    function getApplytypeApi()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\StockModel($request);

        $result = $Model->getApplytypeApi($request);
        ajax_return($result,$request['language_type']);
    }

    //获取库存变动日志列表
    function getChangelogListView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\StockModel($request);

        $result = $Model->getChangelogList($request);
        ajax_return($result,$request['language_type']);
    }

    //审核入库单
    function ExamineInorderAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\StockModel($request);

        $result = $Model->ExamineInorderAction($request);
        ajax_return($result,$request['language_type']);
    }

    //添加校园库存
    function addScRepertoryAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\StockModel($request);

        $result = $Model->addScRepertoryAction($request);
        ajax_return($result,$request['language_type']);
    }

    //审核出库单
    function ExamineOutorderAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\StockModel($request);

        $result = $Model->ExamineOutorderAction($request);
        ajax_return($result,$request['language_type']);
    }

    //减少校园库存
    function DelScRepertoryAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\StockModel($request);

        $result = $Model->DelScRepertoryAction($request);
        ajax_return($result,$request['language_type']);
    }

    //编辑采购货品明细
    function updateGoodsDetailAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\StockModel($request);

        $result = $Model->updateGoodsDetailAction($request);
        ajax_return($result,$request['language_type']);
    }

    //生成借调订单
    function CreateLendorderAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\StockModel($request);

        $result = $Model->CreateLendorderAction($request);
        $this->addSmcWorkLog($request['company_id'],$request['school_id'],$request['staffer_id'],"库存管理->采购管理",'生成借调订单',dataEncode($request));
        ajax_return($result,$request['language_type']);
    }

    //销货管理
    function getSalesListView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\StockModel($request);

        $result = $Model->getSalesList($request);
        ajax_return($result,$request['language_type']);
    }

    function getChangeGoodsListView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\StockModel($request);

        $result = $Model->getChangeGoodsList($request);
        ajax_return($result,$request['language_type']);
    }

    //创建领用订单
    function CreateGetOrderAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\StockModel($request);

        $result = $Model->CreateGetOrderAction($request);
        $this->addSmcWorkLog($request['company_id'],$request['school_id'],$request['staffer_id'],"库存管理->采购管理",'创建领用/报损订单',dataEncode($request));
        ajax_return($result,$request['language_type']);
    }

    //创建报损订单
    function CreateBadOrderAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\StockModel($request);

        $result = $Model->CreateBadOrderAction($request);
        $this->addSmcWorkLog($request['company_id'],$request['school_id'],$request['staffer_id'],"库存管理->采购管理",'创建报损订单',dataEncode($request));
        ajax_return($result,$request['language_type']);
    }

    //班组选项
    function getCourseTypeApi()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Smc\StockModel($request);

        $result = $this->Model->getCourseTypeApi($request);
        ajax_return($result,$request['language_type']);
    }

    //班种选项
    function getCourseCatApi()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Smc\StockModel($request);

        $result = $this->Model->getCourseCatApi($request);
        ajax_return($result,$request['language_type']);
    }

    //班别安全数量列表
    function getCourseSafeListView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Smc\StockModel($request);

        $result = $this->Model->getCourseSafeList($request);
        ajax_return($result,$request['language_type']);
    }

    //增加/改变安全库存
    function UpdateSafeAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\StockModel($request);

        $result = $Model->UpdateSafeAction($request);
        $this->addSmcWorkLog($request['company_id'],$request['school_id'],$request['staffer_id'],"采购管理->安全库存设定",'增加/改变安全库存',dataEncode($request));
        ajax_return($result,$request['language_type']);
    }

    //预估采购班级列表
    function getSafeBuyClassListView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Smc\StockModel($request);

        $result = $this->Model->getSafeBuyClassList($request);
        ajax_return($result,$request['language_type']);
    }

    //下一级别班别列表
    function getNextCourseListView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Smc\StockModel($request);

        $result = $this->Model->getNextCourseList($request);
        ajax_return($result,$request['language_type']);
    }

    //班级查看列表
    function getClassDetailListView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Smc\StockModel($request);

        $result = $this->Model->getClassDetailList($request);
        ajax_return($result,$request['language_type']);
    }

    //预约采购下单申请单
    function ApplyOrderBuyAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\StockModel($request);

        $result = $Model->ApplyOrderBuyAction($request);
        $this->addSmcWorkLog($request['company_id'],$request['school_id'],$request['staffer_id'],"采购管理->预约采购",'预约采购下单申请单',dataEncode($request));
        ajax_return($result,$request['language_type']);
    }

    //申请预约采购提交查看
    function getOrderBuyOneView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Smc\StockModel($request);

        $result = $this->Model->getOrderBuyOne($request);
        ajax_return($result,$request['language_type']);
    }

    //采购活动选项列表
    function getActivityBuyApi()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Smc\StockModel($request);

        $result = $this->Model->getActivityBuyApi($request);
        ajax_return($result,$request['language_type']);
    }

    //采购活动商品列表
    function getActivityBuyGoodsListView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Smc\StockModel($request);

        $result = $this->Model->getActivityBuyGoodsList($request);
        ajax_return($result,$request['language_type']);
    }

    //活动采购下单申请单
    function ApplyActivityBuyAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\StockModel($request);

        $result = $Model->ApplyActivityBuyAction($request);
        $this->addSmcWorkLog($request['company_id'],$request['school_id'],$request['staffer_id'],"采购管理->活动采购",'活动采购下单申请单',dataEncode($request));
        ajax_return($result,$request['language_type']);
    }

    //预估采购确认列表
    function getOrderBuyConfirmListView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Smc\StockModel($request);

        $result = $this->Model->getOrderBuyConfirmList($request);
        ajax_return($result,$request['language_type']);
    }

    //活动采购确认列表
    function getActivityBuyConfirmListView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Smc\StockModel($request);

        $result = $this->Model->getActivityBuyConfirmList($request);
        ajax_return($result,$request['language_type']);
    }

    //自主采购确认列表
    function getOwnBuyConfirmListView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Smc\StockModel($request);

        $result = $this->Model->getOwnBuyConfirmList($request);
        ajax_return($result,$request['language_type']);
    }

    //借调确认列表
    function getTransferbuyConfirmListView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Smc\StockModel($request);

        $result = $this->Model->getTransferbuyConfirmList($request);
        ajax_return($result,$request['language_type']);
    }

    //预估采购单信息
    function getOrderBuyInfoApi()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Smc\StockModel($request);

        $result = $this->Model->getOrderBuyInfoApi($request);
        ajax_return($result,$request['language_type']);
    }

    //预估采购项目列表
    function getOwnBuySetsListView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Smc\StockModel($request);

        $result = $this->Model->getOwnBuySetsList($request);
        ajax_return($result,$request['language_type']);
    }

    //改变预估采购项目列表
    function UpdateOwnBuySetsAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Smc\StockModel($request);

        $result = $this->Model->UpdateOwnBuySetsAction($request);
        ajax_return($result,$request['language_type']);
    }

    //预估采购下一步
    function addSetsAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Smc\StockModel($request);

        $result = $this->Model->addSetsAction($request);
        ajax_return($result,$request['language_type']);
    }

    //新增课程别选项列表
    function getCourseApi()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Smc\StockModel($request);

        $result = $this->Model->getCourseApi($request);
        ajax_return($result,$request['language_type']);
    }

    //获取安全库存数
    function getSafeApi()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Smc\StockModel($request);

        $result = $this->Model->getSafeApi($request);
        ajax_return($result,$request['language_type']);
    }

    //新增库存预购
    function CreateSetAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Smc\StockModel($request);

        $result = $this->Model->CreateSetAction($request);
        $this->addSmcWorkLog($request['company_id'],$request['school_id'],$request['staffer_id'],"库存管理->采购管理",'新增库存预购',dataEncode($request));
        ajax_return($result,$request['language_type']);
    }

    //预估采购商品列表
    function getOrderGoodsListView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Smc\StockModel($request);

        $result = $this->Model->getOrderGoodsList($request);
        ajax_return($result,$request['language_type']);
    }

    //改变申请状态
    function UpdateApplyAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Smc\StockModel($request);

        $result = $this->Model->UpdateApplyAction($request);
        ajax_return($result,$request['language_type']);
    }

    //取消采购
    function CancelProorderAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Smc\StockModel($request);

        $result = $this->Model->CancelProorderAction($request);
        $this->addSmcWorkLog($request['company_id'],$request['school_id'],$request['staffer_id'],"库存管理->采购管理",'取消采购',dataEncode($request));
        ajax_return($result,$request['language_type']);
    }

    //借调申请
    function ApplytransferbuyAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Smc\StockModel($request);

        $result = $this->Model->ApplytransferbuyAction($request);
        ajax_return($result,$request['language_type']);
    }

    //删除预估采购项目
    function DelOrderSetAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Smc\StockModel($request);

        $result = $this->Model->DelOrderSetAction($request);
        ajax_return($result,$request['language_type']);
    }

    //自主采购单信息
    function getOwnBuyInfoApi()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Smc\StockModel($request);

        $result = $this->Model->getOwnBuyInfoApi($request);
        ajax_return($result,$request['language_type']);
    }

    //自主采购单商品列表
    function getOwnBuyGoodsListView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Smc\StockModel($request);

        $result = $this->Model->getOwnBuyGoodsList($request);
        ajax_return($result,$request['language_type']);
    }

    //借调商品列表
    function getTransferBuyGoodsListView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Smc\StockModel($request);

        $result = $this->Model->getTransferBuyGoodsList($request);
        ajax_return($result,$request['language_type']);
    }

    //活动采购信息
    function getActivityBuyInfoApi()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Smc\StockModel($request);

        $result = $this->Model->getActivityBuyInfoApi($request);
        ajax_return($result,$request['language_type']);
    }

    //活动采购单商品列表
    function getActivityGoodsListView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Smc\StockModel($request);

        $result = $this->Model->getActivityGoodsList($request);
        ajax_return($result,$request['language_type']);
    }

    //删除自主采购项目
    function DelOwnBuyAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Smc\StockModel($request);

        $result = $this->Model->DelOwnBuyAction($request);
        ajax_return($result,$request['language_type']);
    }

    //删除活动采购项目
    function DelActivityBuyAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Smc\StockModel($request);

        $result = $this->Model->DelActivityBuyAction($request);
        ajax_return($result,$request['language_type']);
    }

    //库存盘点列表
    function getCheckListView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Smc\StockModel($request);

        $result = $this->Model->getCheckList($request);
        ajax_return($result,$request['language_type']);
    }

    //盘点明细
    function getCheckInfoApi()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Smc\StockModel($request);

        $result = $this->Model->getCheckInfoApi($request);
        ajax_return($result,$request['language_type']);
    }

    //盘点明细列表
    function getCheckInfoListView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Smc\StockModel($request);

        $result = $this->Model->getCheckInfoList($request);
        ajax_return($result,$request['language_type']);
    }

    //新建盘点
    function addCheckAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Smc\StockModel($request);

        $result = $this->Model->addCheckAction($request);
        ajax_return($result,$request['language_type']);
    }

    //拒绝审核
    function refuseAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Smc\StockModel($request);

        $result = $this->Model->refuseAction($request);
        ajax_return($result,$request['language_type']);
    }

    //下载导入模版
    function getImportApi()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Smc\StockModel($request);

        $result = $this->Model->getImportApi($request);
        ajax_return($result,$request['language_type']);
    }

    function ImportExcelView()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $data = array();
        $data['check_date'] = $request['check_date'];
        $data['check_class'] = $request['check_class'];
        $data['school_id'] = $request['school_id'];
        $data['company_id'] = $request['company_id'];
        $data['staffer_id'] = $request['staffer_id'];
        $data['check_createtime'] = time();

        $field = array();
        $field['check_date'] = "职务类型";
        $field['check_class'] = "所属集团";
//
//        $post_code = $this->DataControl->getFieldOne('erp_goods_check', 'check_id', "check_date = '{$request['check_date']}' and school_id = '{$request['school_id']}'");
//        if ($post_code) {
//            ajax_return(array('error' => 1, 'errortip' => "该日期已进行过盘点!"));
//        }
        $id = $this->DataControl->insertData('erp_goods_check', $data);

        $ys_array = array('K3货号' => 'a', '盘点数' => 'b');
        $options=array(
            "ssl"=>array(
                "verify_peer"=>false,
                "verify_peer_name"=>false,
            ),
        );
        file_put_contents('analysis.xlsx', file_get_contents($request['file_url'],false,stream_context_create($options)));
        $sqlarray = execl_to_array("analysis.xlsx", $ys_array);
        array_shift($sqlarray);

        foreach ($sqlarray as $item) {
            $data = array();
            $data['goods_pid'] = $item['a'];
            $data['checkdetail_checknum'] = $item['b'];
            $data['check_id'] = $id;
            $data['check_createtime'] = time();

            $this->DataControl->insertData('erp_goods_check_detail', $data);
        }

        $res = array('error' => '0', 'errortip' => '导入盘点成功', 'result' => $id);
        ajax_return($res,$request['language_type']);

    }

    //POS机盘点
    function posCheckAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Smc\StockModel($request);

        $result = $this->Model->posCheckAction($request);
        ajax_return($result,$request['language_type']);
    }

    function changeCheckAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Smc\StockModel($request);

        $result = $this->Model->changeCheckAction($request);
        ajax_return($result,$request['language_type']);
    }

    //改变学校库存
    function changeRepertoryAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Smc\StockModel($request);

        $result = $this->Model->changeRepertoryAction($request);
        $this->addSmcWorkLog($request['company_id'],$request['school_id'],$request['staffer_id'],"库存管理->采购管理",'改变学校库存',dataEncode($request));
        ajax_return($result,$request['language_type']);
    }

    //改变盘点库存
    function UpdateCheckAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Smc\StockModel($request);

        $result = $this->Model->UpdateCheckAction($request);
        $this->addSmcWorkLog($request['company_id'],$request['school_id'],$request['staffer_id'],"库存管理->库存盘点",'改变盘点库存',dataEncode($request));
        ajax_return($result,$request['language_type']);
    }

    //盘点明细列表
    function getCheckGoodsListView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Smc\StockModel($request);

        $result = $this->Model->getCheckGoodsList($request);
        ajax_return($result,$request['language_type']);
    }

    //自主采购下单申请
    function ApplyOwnBuyAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Smc\StockModel($request);

        $result = $this->Model->ApplyOwnBuyAction($request);
        $this->addSmcWorkLog($request['company_id'],$request['school_id'],$request['staffer_id'],"库存管理->采购管理",'自主采购',dataEncode($request));
        ajax_return($result,$request['language_type']);
    }

    //查看采购商品列表
    function getOwnBuyGoodsView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Smc\StockModel($request);

        $result = $this->Model->getOwnBuyGoods($request);
        ajax_return($result,$request['language_type']);
    }

    //更新入库单
    function beOutOrderinfoView()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $sendinfo ="<StrXMLPara><FBrNo>01102</FBrNo>"
            ."<BRANCH>D.0007001</BRANCH>"
            ."<BEGINDATE>{$request['startTime']}</BEGINDATE>"
            ."<ENDDATE>{$request['endTime']}</ENDDATE>";

        $sendinfo .="</StrXMLPara>";

        $sendbak = request_by_curl("http://47.96.36.32:55520/jdbbillwebservicetest/Webservice1.asmx/MakeOutStock", "strXMLPara={$sendinfo}", "POST", array());

        $xml = simplexml_load_string($sendbak);
        $data = json_decode(json_encode($xml),TRUE);

        $StingConter = new \Websting();
        $Success = $StingConter->getContent($sendbak,'&lt;Success&gt;','&lt;/Success&gt;') ;
        $item = $StingConter->getContentarray($data[0],'<BILL_LIST>','</BILL_LIST>') ;

        if($Success){
            foreach ($item as $vals){
                $data = array();
                $data['company_id'] = $request['company_id'];
                $data['school_id'] = $request['school_id'];
                $data['outorder_pid'] = $StingConter->getContentarray($vals,'<SHIP_KEY_NO>','</SHIP_KEY_NO>')[0];
                $a = $StingConter->getContentarray($vals,'<SHIP_DATE>','</SHIP_DATE>')[0];
                $b = substr($a,0,-7);
                $c = strtotime($b);
                $data['outorder_time'] = date('Y-m-d', $c);
                $pid = $StingConter->getContentarray($vals,'<SALE_NO>','</SALE_NO>')[0];


                if($this->DataControl->getFieldOne("smc_erp_outorder","outorder_id","outorder_pid = '{$data['outorder_pid']}'")){
                    $data['outorder_updatetime'] = time();
                    $this->DataControl->updateData("smc_erp_outorder","outorder_pid = '{$data['outorder_pid']}'",$data);
                }else{
                    $data['outorder_createtime'] = time();
                    $this->DataControl->insertData("smc_erp_outorder",$data);
                    $items = $StingConter->getContentarray($vals,'<PROD_LIST>','</PROD_LIST>');

                    foreach ($items as $val){
                        $goodspid = $StingConter->getContentarray($val,'<PROD_ID>','</PROD_ID>')[0];
                        $goodsid = $this->DataControl->getFieldOne("erp_goods","goods_id","goods_outpid = '{$goodspid}' and company_id = '1001'");
                        $datas = array();
                        $datas['outorder_pid'] = $data['outorder_pid'];
                        $datas['proorder_pid'] = $pid;
                        $datas['goods_id'] = $goodsid['goods_id'];
                        $datas['outordergoods_buynums'] = $StingConter->getContentarray($val,'<QTY>','</QTY>')[0];
                        $this->DataControl->insertData("smc_erp_outorder_goods",$datas);

                    }
                }
            }
            ajax_return(array('error' => 0,'errortip' => "更新成功！"));
        }else{
            ajax_return(array('error' => 0,'errortip' => "更新失败！"));

        }

    }

    //确认收货
    function ConfirmReceiptAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\StockModel($request);

        $result = $Model->ConfirmReceiptAction($request);
        ajax_return($result,$request['language_type']);
    }

    //载入入库单列表
    function getErpinorderListView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Smc\StockModel($request);

        $result = $this->Model->getErpinorderList($request);
        ajax_return($result,$request['language_type']);
    }

    //载入入库单列表
    function getRrpOutorderInfoView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Smc\StockModel($request);

        $result = $this->Model->getRrpOutorderInfo($request);
        ajax_return($result,$request['language_type']);
    }

    //查看ERP出库单商品明细
    function getErpOutGoodsView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Smc\StockModel($request);

        $result = $this->Model->getErpOutGoods($request);
        ajax_return($result,$request['language_type']);
    }

    //添加至入库单
    function addBeinorderAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\StockModel($request);

        $result = $Model->addBeinorderAction($request);
        ajax_return($result,$request['language_type']);
    }

    function addaaaAction(){

        $schoolList = $this->DataControl->selectClear("select company_id from gmc_company where company_id not in (1001,6666,7777,8888,9999,10001,77777)");
        foreach ($schoolList as $item) {
            $a = $this->DataControl->getFieldOne("smc_code_coursecat","coursecat_id,coursetype_id","coursecat_branch = 'S' and company_id = '{$item['company_id']}'");
            $data = array();
            $data['company_id'] = $item['company_id'];
            $data['coursetype_id'] = $a['coursetype_id'];
            $data['coursecat_id'] = $a['coursecat_id'];
            $data['course_cnname'] = '大儿童美语一级';
            $data['course_branch'] = 'S01';
            $data['course_classnum'] = '32';
            $data['course_createtime'] = time();
            $this->DataControl->insertData('smc_course',$data);
            $data = array();
            $data['company_id'] = $item['company_id'];
            $data['coursetype_id'] = $a['coursetype_id'];
            $data['coursecat_id'] = $a['coursecat_id'];
            $data['course_cnname'] = '大儿童美语二级';
            $data['course_branch'] = 'S02';
            $data['course_classnum'] = '32';
            $data['course_createtime'] = time();
            $this->DataControl->insertData('smc_course',$data);
            $data = array();
            $data['company_id'] = $item['company_id'];
            $data['coursetype_id'] = $a['coursetype_id'];
            $data['coursecat_id'] = $a['coursecat_id'];
            $data['course_cnname'] = '大儿童美语三级';
            $data['course_branch'] = 'S03';
            $data['course_classnum'] = '32';
            $data['course_createtime'] = time();
            $this->DataControl->insertData('smc_course',$data);
        }
        ajax_return(array('error' => 0,'errortip' => "成功"));

    }

    //入库
    function InorderAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\StockModel($request);

        $result = $Model->InorderAction($request);
        ajax_return($result,$request['language_type']);
    }

    //货品类别下拉列表
    function getProdtypeApi()
    {
        $request = Input('get.','','trim,addslashes');

        $sql = "select prodtype_id,prodtype_code,prodtype_name from smc_code_prodtype where company_id = '{$request['company_id']}'";
        $CompaniesList = $this->DataControl->selectClear($sql);
        if (!$CompaniesList) {
            $CompaniesList = array();
        }

        $result["list"] = $CompaniesList;
        $res = array('error' => 0, 'errortip' => '获取货品类别列表', 'result' => $result);

        ajax_return($res,$request['language_type']);
    }

    //采购活动下拉列表
    function getBuyActivityApi()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $sql = "select activitybuy_id,activitybuy_name from gmc_company_activitybuy where company_id = '{$request['company_id']}'";
        $CompaniesList = $this->DataControl->selectClear($sql);
        if (!$CompaniesList) {
            $CompaniesList = array();
        }

        $result["list"] = $CompaniesList;
        $res = array('error' => 0, 'errortip' => '获取采购活动下拉列表', 'result' => $result);

        ajax_return($res,$request['language_type']);
    }

    /**
     *库存管理->借调可选商品列表
     */
    function getChangeGoodsListsView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Smc\StockModel($request);
        $res = $Model->getChangeGoodsLists($request);

        ajax_return($res,$request['language_type']);
    }

    /**
     *库存管理->自主采购可选商品列表
     */
    function getOwnBuyoodsListView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Smc\StockModel($request);
        $res = $Model->getOwnBuyoodsList($request);

        ajax_return($res,$request['language_type']);
    }

    function getSalesorderListView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);

        $Model = new \Model\Smc\StockModel($request);
        $res = $Model->getSalesorderList($request);

        $field = array();
        $k = 0;

        if($request['salesorder_from'] == '3' || $request['salesorder_from'] == '2'){

            $field[$k]["fieldstring"] = "salesorder_id";
            $field[$k]["fieldname"] = "销货单id";
            $field[$k]["show"] = 0;
            $field[$k]["custom"] = 0;
            $k++;

            $field[$k]["fieldstring"] = "salesorder_pid";
            $field[$k]["fieldname"] = "销货单号";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "salesorder_from";
            $field[$k]["fieldname"] = "销货类型";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            if($request['salesorder_from'] !== '2' && $request['salesorder_from'] !== '3'){
                $field[$k]["fieldstring"] = "proorder_pid";
                $field[$k]["fieldname"] = "采购单号";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;
            }


            $field[$k]["fieldstring"] = "salenum";
            $field[$k]["fieldname"] = "销货货品数量";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            if($request['salesorder_from'] !== '2' && $request['salesorder_from'] !== '3'){
                $field[$k]["fieldstring"] = "proorder_allprice";
                $field[$k]["fieldname"] = "销货金额";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;
            }


            $field[$k]["fieldstring"] = "salesorder_createtime";
            $field[$k]["fieldname"] = "申请日期";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "salesorder_status_name";
            $field[$k]["fieldname"] = "状态";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

        }else{

            $field[$k]["fieldstring"] = "salesorder_id";
            $field[$k]["fieldname"] = "销货单id";
            $field[$k]["show"] = 0;
            $field[$k]["custom"] = 0;
            $k++;

            $field[$k]["fieldstring"] = "salesorder_pid";
            $field[$k]["fieldname"] = "销货单号";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "salesorder_from";
            $field[$k]["fieldname"] = "销货类型";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "to_school_cnname";
            if($request['salesorder_from'] == '0'){
                $field[$k]["fieldname"] = "调入校区";
            }else{
                $field[$k]["fieldname"] = "借入校区";
            }
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "from_school_cnname";
            if($request['salesorder_from'] == '0'){
                $field[$k]["fieldname"] = "调出校区";
            }else{
                $field[$k]["fieldname"] = "借出校区";
            }
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "proorder_pid";
            $field[$k]["fieldname"] = "采购单号";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "salenum";
            $field[$k]["fieldname"] = "销货货品数量";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "proorder_allprice";
            $field[$k]["fieldname"] = "销货金额";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "salesorder_createtime";
            $field[$k]["fieldname"] = "申请日期";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "salesorder_status_name";
            $field[$k]["fieldname"] = "状态";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

        }

        $result = array();
        $result["field"] = $field;

        $result["fieldcustom"] = 1;
        $result["all_num"] = $res['all_num'];

        if ($res['list']) {
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["all_num"] = 0;
            $res = array('error' => 1, 'errortip' => '无销货单数据', 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }

    function getSalesorderOneApi()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Smc\StockModel($request);
        $result = $Model->getSalesorderOneApi($request);

        ajax_return($result,$request['language_type']);
    }

    function getOutSalesorderOneApi()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Smc\StockModel($request);
        $result = $Model->getOutSalesorderOneApi($request);

        ajax_return($result,$request['language_type']);
    }


    /**
     *库存管理->销货货品明细
     *by:qyh
     *接口391
     */
    function getSalesorderGoodsListView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);

        $Model = new \Model\Smc\StockModel($request);
        $res = $Model->getSalesorderGoodsList($request);

        $field = array();
        $k = 0;

        $field[$k]["fieldstring"] = "goods_id";
        $field[$k]["fieldname"] = "商品id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "salesordergoods_id";
        $field[$k]["fieldname"] = "id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "goods_pid";
        $field[$k]["fieldname"] = "货品编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "goods_cnname";
        $field[$k]["fieldname"] = "货品名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "prodtype_name";
        $field[$k]["fieldname"] = "货品类别";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "goods_unit";
        $field[$k]["fieldname"] = "单位";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "goods_vipprice";
        $field[$k]["fieldname"] = "协议价";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "salesordergoods_buynums";
        if($request['salesorder_from'] !== '2' && $request['salesorder_from'] !== '3'){
            $field[$k]["fieldname"] = "采购数量";
        }elseif($request['salesorder_from'] == '2'){
            $field[$k]["fieldname"] = "领用数量";
        }elseif($request['salesorder_from'] == '3'){
            $field[$k]["fieldname"] = "报损数量";
        }
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;



        if($request['salesorder_from'] !== '2' && $request['salesorder_from'] !== '3'){
            $field[$k]["fieldstring"] = "price";
            $field[$k]["fieldname"] = "金额小计";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "salesordergoods_sendnums";
            $field[$k]["fieldname"] = "已发货数量";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;
        }

        if($request['type'] == '1'){
            $field[$k]["fieldstring"] = "goodsNum";
            $field[$k]["fieldname"] = "本次发货数量";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $field[$k]["isInputText"] = true;
            $k++;
        }




        $result = array();
        $result["field"] = $field;
        $result["fieldcustom"] = 1;
        if ($res['list']) {
            $result["list"] = $res['list'];
            $result["all_num"] = $res['all_num'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["all_num"] = 0;
            $res = array('error' => 1, 'errortip' => '无货品数据', 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }

    /**
     *库存管理->销货单跟踪记录
     *by:qyh
     *接口392
     */
    function getSalesorderTracksView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);

        $Model = new \Model\Smc\StockModel($request);
        $res = $Model->getSalesorderTracks($request);

        $field = array();
        $k = 0;

        $field[$k]["fieldstring"] = "tracks_title";
        $field[$k]["fieldname"] = "操作内容";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "tracks_playname";
        $field[$k]["fieldname"] = "操作人";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "tracks_information";
        $field[$k]["fieldname"] = "备注";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "tracks_createtime";
        $field[$k]["fieldname"] = "操作时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;

        $result["fieldcustom"] = 1;
        if ($res['list']) {
            $result["list"] = $res['list'];
            $result["all_num"] = $res['all_num'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["all_num"] = 0;
            $res = array('error' => 1, 'errortip' => '无跟踪数据', 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }

    /**
     *库存管理->审核销货单
     */
    function salesorderStatusAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\StockModel($request);

        $result = $Model->salesorderStatusAction($request);

        ajax_return($result,$request['language_type']);
    }

    /**
     *库存管理->发货
     */
    function sendGoodsAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\StockModel($request);

        $result = $Model->sendGoodsAction($request);

        ajax_return($result,$request['language_type']);
    }

    /**
     *库存管理->待审核数量
     */
    function toVerifyCountApi()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $orderbuy = $this->DataControl->selectOne("select count(orderbuy_id) as num from smc_erp_orderbuy where company_id = '{$request['company_id']}' and school_id = '{$request['school_id']}' and orderbuy_status = '0'");

        $transferbuy = $this->DataControl->selectOne("select count(transferbuy_id) as num from smc_erp_transferbuy where company_id = '{$request['company_id']}' and school_id = '{$request['school_id']}' and transferbuy_status = '0'");

        $activitybuy = $this->DataControl->selectOne("select count(activity_id) as num from smc_erp_activity where company_id = '{$request['company_id']}' and school_id = '{$request['school_id']}' and activity_status = '0'");

        $ownbuy = $this->DataControl->selectOne("select count(ownbuy_id) as num from smc_erp_ownbuy where company_id = '{$request['company_id']}' and school_id = '{$request['school_id']}' and ownbuy_status = '0'");

        $result = array();
        $result["orderbuy"] = $orderbuy['num'];
        $result["transferbuy"] = $transferbuy['num'];
        $result["activitybuy"] = $activitybuy['num'];
        $result["ownbuy"] = $ownbuy['num'];
        $res = array('error' => 0, 'errortip' => '获取待审核数量成功', 'result' => $result);

        ajax_return($res,$request['language_type']);
    }

    //积分兑换明细列表
    function getIntegralgoodsListView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\StockModel($request);

        $result = $Model->getIntegralgoodsList($request);
        ajax_return($result,$request['language_type']);
    }

    //根据学生id获取学生积分信息
    function getStudentIntegralView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\StockModel($request);

        $result = $Model->getStudentIntegral($request);
        ajax_return($result,$request['language_type']);
    }

    //根据学生id获取学生积信息
    function getStudentInfoView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\StockModel($request);

        $result = $Model->getStudentInfo($request);
        ajax_return($result,$request['language_type']);
    }

    //积分兑换商品明细
    function getIntegralgoodsDetailView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\StockModel($request);

        $result = $Model->getIntegralgoodsDetail($request);
        ajax_return($result,$request['language_type']);
    }

    //积分跟踪记录
    function integralTrackApi()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $sql = "select FROM_UNIXTIME( tracks_time, '%Y-%m-%d %H:%i:%s' ) as tracks_time,tracks_title,tracks_information from smc_integral_tracks where integralgoods_id = '{$request['integralgoods_id']}'";
        $TrackList = $this->DataControl->selectClear($sql);
        if (!$TrackList) {
            $TrackList = array();
        }

        $result["list"] = $TrackList;
        $res = array('error' => 0, 'errortip' => '获取积分跟踪记录', 'result' => $result);

        ajax_return($res,$request['language_type']);
    }

    //改变积分兑换明细状态
    function UpdateIntegralStatusAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Smc\StockModel($request);

        $result = $this->Model->UpdateIntegralStatusAction($request);
        ajax_return($result,$request['language_type']);
    }

    //积分交易明细列表
    function getIntegrallogListView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\StockModel($request);

        $result = $Model->getIntegrallogList($request);
        ajax_return($result,$request['language_type']);
    }

    //积分类型下拉
    function getIntegraltypeApi()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\StockModel($request);

        $result = $Model->getIntegraltypeApi($request);
        ajax_return($result,$request['language_type']);
    }

    //货物订单管理列表
    function getGeneralgoodsListView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\StockModel($request);

        $result = $Model->getGeneralgoodsList($request);
        ajax_return($result,$request['language_type']);
    }

    //货物订单商品明细
    function getGeneralgoodsDetailView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\StockModel($request);

        $result = $Model->getGeneralgoodsDetail($request);
        ajax_return($result,$request['language_type']);
    }

    //货物订单跟踪记录
    function generalTrackApi()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $sql = "select FROM_UNIXTIME( tracks_time, '%Y-%m-%d %H:%i:%s' ) as tracks_time,tracks_title,tracks_information from smc_payfee_order_tracks where order_pid = '{$request['order_pid']}'";
        $TrackList = $this->DataControl->selectClear($sql);
        if (!$TrackList) {
            $TrackList = array();
        }

        $result["list"] = $TrackList;
        $res = array('error' => 0, 'errortip' => '获取货物订单跟踪记录', 'result' => $result);

        ajax_return($res,$request['language_type']);
    }

    //改变货物订单状态
    function UpdateGeneralStatusAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Smc\StockModel($request);

        $result = $this->Model->UpdateGeneralStatusAction($request);
        ajax_return($result,$request['language_type']);
    }


    //结尾魔术函数
    function __destruct()
    {

    }
}
