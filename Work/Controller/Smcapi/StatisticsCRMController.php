<?php


namespace Work\Controller\Smcapi;


class StatisticsCRMController extends viewTpl

{
    public $u;
    public $t;
    public $c;

    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
    }


    //校务CRM统计 - 招生情况概览
    function CountOverCRMView()
    {
        $request = input("get.", '', "trim,addslashes");
        $this->ThisVerify($request);
        $Model = new \Model\Smc\CommonModel($request);
        $dataList = $Model->getEnrollStuCount($request);
        $field = array();
        $field["client_num"] = "当前有效名单数";
        $field["intention_num"] = "当前意向客户数";
        $field["invite_num"] = " 待柜询客户数";
        $field["audition_num"] = "待试听人数";
        $field["loss_num"] = " 历史累计无意向人数";
        $field["conversion_num"] = "累计转正人数";
        $field["marketer_num"] = "招生负责人数";
        $field["activity_num"] = "正在进行活动数";
        $field["notrack_num"] = "待跟踪客户";
        $field["seven_notrack_num"] = "7天未跟踪的意向客户";
        $field["no_auditionclientNum"] = "未试听意向客户";
        $field["no_inviteclientNum"] = "未邀约意向客户";
        $result["field"] = $field;
        $result["data"] = $dataList;
        $res = array('error' => '0', 'errortip' => '获取成功', 'result' => $result);
        ajax_return($res, $request['language_type']);
    }

    // 校务CRM统计 -  招生统计 - 根据时间筛选
    function CountTimeOverCRMView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        if (!isset($request['school_id']) || $request['school_id'] == '') {
            $res = array('error' => '1', 'errortip' => '必须选择对应学校', 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        if (!isset($request['client_starttime']) || $request['client_starttime'] == '' || $request['client_endtime'] == '') {
            $res = array('error' => '1', 'errortip' => '必须选择查询时间段', 'result' => array());
            ajax_return($res, $request['language_type']);
        }

        $Model = new \Model\Smc\StatisticsCRMModel($request);
        $dataList = $Model->CountTimeOverCRM($request);

        $field = array();
        $field["addClient"] = "新增";
        $field["intentionClient"] = "意向";
        $field["loseClient"] = "无意向";
        $field["inviteClient"] = "柜询";
        $field["auditionClient"] = "试听";
        $field["officialClient"] = "正式";
        //转化率
        $field["intentionClientRate"] = "意向转化率";
        $field["inviteClientRate"] = "柜询转化率";
        $field["auditionClientRate"] = "试听转化率";
        $field["officialClientRate"] = "正式转化率";
        $field["loseClientRate"] = "无意向转化率";

        $array_name = array('招生有效名单', '意向客户', '柜询客户', '试听客户', '转正客户');
        $array_data = array(
            array('name' => '招生有效名单', 'value' => $dataList['funnel_addClient']),
            array('name' => '意向客户', 'value' => $dataList['funnel_intentionClient']),
            array('name' => '柜询客户', 'value' => $dataList['funnel_inviteClient']),
            array('name' => '试听客户', 'value' => $dataList['funnel_auditionClient']),
            array('name' => '转正客户', 'value' => $dataList['funnel_officialClient']),
        );

        $count_array = array();
        $count_array['x_data'] = $array_name;
        $count_array['y_data'] = $array_data;
        $result = array();
        if ($dataList) {
            $result["field"] = $field;
            $result["data"] = $dataList;
            $result["count_data"] = $count_array;
            $res = array('error' => '0', 'errortip' => '首页招生简章数据统计', 'result' => $result);
        } else {
            $result["field"] = $field;
            $result["data"] = array();
            $result["count_data"] = $count_array;
            $res = array('error' => '1', 'errortip' => '首页招生简章数据统计', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    // 校务CRM统计 -  招生统计 - 获取最近的10天的招生 -折线图
    function CountTenDaysCRMView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $model = new \Model\Smc\StatisticsCRMModel($request);
        $dataList = $model->CountTenDaysCRM($request);
        $res = array('error' => '1', 'errortip' => '最近10天招生折线图', 'result' => $dataList);
        ajax_return($res, $request['language_type']);
    }

    // 校务CRM统计 - 活动转化排行
    function CountActivityView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $model = new \Model\Smc\StatisticsCRMModel($request);
        $dataList = $model->CountActivity($request);
        $field = array();
        $i = 0;
        $field[$i]["fieldname"] = "序号";
        $field[$i]["fieldstring"] = 'number';
        $field[$i]["custom"] = 1;
        $field[$i]["show"] = 1;
        $i++;
        $field[$i]["fieldname"] = "活动名称";
        $field[$i]["fieldstring"] = 'activity_name';
        $field[$i]["custom"] = 1;
        $field[$i]["show"] = 1;
        $i++;
        $field[$i]["fieldname"] = "有效名单数";
        $field[$i]["fieldstring"] = 'client_all_num';
        $field[$i]["custom"] = 1;
        $field[$i]["show"] = 1;
        $i++;
        $field[$i]["fieldname"] = "转正名单数";
        $field[$i]["fieldstring"] = 'client_postive_num';
        $field[$i]["custom"] = 1;
        $field[$i]["show"] = 1;
        $i++;
        $field[$i]["fieldname"] = "转化率";
        $field[$i]["fieldstring"] = 'rate';
        $field[$i]["custom"] = 1;
        $field[$i]["show"] = 1;
        $result = array();
        $result['field'] = $field;
        $result['list'] = $dataList;
        if($dataList){
            $res = array('error' => '0', 'errortip' => '活动转化排行', 'result' => $result);
        }else{
            $res = array('error' => '1', 'errortip' => '活动转化排行', 'result' => $result);
        }


        ajax_return($res, $request['language_type']);
    }

    // 校务CRM统计 - 业绩排行
    function CountStafferPostiveView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $model = new \Model\Smc\StatisticsCRMModel($request);
        $dataList = $model->CountStafferPostive($request);
        $field = array();
        $i = 0;
        $field[$i]["fieldname"] = "序号";
        $field[$i]["fieldstring"] = 'number';
        $field[$i]["custom"] = 1;
        $field[$i]["show"] = 1;
        $i++;
        $field[$i]["fieldname"] = "教师名称";
        $field[$i]["fieldstring"] = 'marketer_name';
        $field[$i]["custom"] = 1;
        $field[$i]["show"] = 1;
        $i++;
        $field[$i]["fieldname"] = "意向客户";
        $field[$i]["fieldstring"] = 'client_all_num';
        $field[$i]["custom"] = 1;
        $field[$i]["show"] = 1;
        $i++;
        $field[$i]["fieldname"] = "转正名单数";
        $field[$i]["fieldstring"] = 'client_postive_num';
        $field[$i]["custom"] = 1;
        $field[$i]["show"] = 1;
        $i++;
        $field[$i]["fieldname"] = "转化率";
        $field[$i]["fieldstring"] = 'rate';
        $field[$i]["custom"] = 1;
        $field[$i]["show"] = 1;
        $result = array();
        $result['field'] = $field;
        $result['list'] = $dataList;
        if($dataList){
            $res = array('error' => '0', 'errortip' => '业绩转化排行', 'result' => $result);
        }else{
            $res = array('error' => '1', 'errortip' => '业绩转化排行', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    // 校务CRM统计 - 转正来源渠道分析
    function CountChannelView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $model = new \Model\Smc\StatisticsCRMModel($request);
        $dataList = $model->CountChannel($request);
        $res = array('error' => '1', 'errortip' => '转正来源渠道分析', 'result' => $dataList);
        ajax_return($res, $request['language_type']);
    }

    // 校务CRM统计 - 年龄转化排行
    function CountAgePostiveView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $model = new \Model\Smc\StatisticsCRMModel($request);
        $dataList = $model->CountAgePostive($request);
        $res = array('error' => '1', 'errortip' => '年龄转化率排行', 'result' => $dataList);
        ajax_return($res, $request['language_type']);
    }


}