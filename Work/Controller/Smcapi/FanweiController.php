<?php
namespace Work\Controller\Smcapi;

class FanweiController extends viewTpl
{

    public function fanWeiAutoUpdateApi()
    {

        $request = Input('get.','','strip_tags,trim,addslashes');
        $Model = new \Model\Smc\FanweiModel();
        if(isset($request['branch']) && $request['branch']!=''){
            $res = $Model->fanWeiAutoUpdate($request['branch']);
        }else{
            $res = $Model->fanWeiAutoUpdate();
        }

        if($res){
//            echo '<script language="javascript" type="text/javascript">
//                    var i = 1;
//                    var intervalid;
//                    intervalid = setInterval("fun()", 1000);
//                    function fun() {
//                        if (i == 0) {
//                            window.location.href = "/Fanwei/fanWeiAutoUpdateApi";
//                            clearInterval(intervalid);
//                        }
//                        document.getElementById("mes").innerHTML = i;
//                        i--;
//                    }
//                    </script>
//                    <div id="error">
//                        <p>将在 <span id="mes">5</span> 秒钟后返回首页！</p>
//                    </div> ';

            $res = array('error' => 0, 'errortip' => "同步成功", 'result' => array());
        }else {
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => array());
        }
        ajax_return($res);

    }
    public function autoCreateAdjustApplyApi()
    {

        $Model = new \Model\Smc\FanweiModel();
        $res = $Model->autoCreateAdjustApply();

        if($res){

            $res = array('error' => 0, 'errortip' => "同步成功", 'result' => array());
        }else {
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => array());
        }
        ajax_return($res);

    }


    public function testApplyApi()
    {

        $Model = new \Model\Smc\FanweiModel();
        $res = $Model->createRefundApply('47740MMLSMK250721MLQPTW');

        if($res){

            $res = array('error' => 0, 'errortip' => "同步成功", 'result' => array());
        }else {
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => array());
        }
        ajax_return($res);

    }






}