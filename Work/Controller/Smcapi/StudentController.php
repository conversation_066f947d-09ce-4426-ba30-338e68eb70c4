<?php


namespace Work\Controller\Smcapi;


class StudentController extends viewTpl
{
    public $u;
    public $t;
    public $c;

    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
    }


    function homeView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $StudentModel = new \Model\Smc\StudentModel($request);
        $res = $StudentModel->studentList($request);
        $contractOne = $this->getContract($request['company_id']);

        $companyOne=$this->DataControl->getFieldOne("gmc_company","company_isopenspecialnumber","company_id='{$request['company_id']}'");

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "student_id";
        $field[$k]["fieldname"] = "学员ID";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["fieldname"] = "学员中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["isportrait"] = 1;
        $field[$k]["fieldwidth"] = 80;
        $k++;

        $field[$k]["fieldstring"] = "ishaveportrait";
        $field[$k]["fieldname"] = "是否有人像 1有 0没有";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_enname";
        $field[$k]["fieldname"] = "学员英文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        if($companyOne['company_isopenspecialnumber']==1){
            $field[$k]["fieldstring"] = "student_thirdbranch";
            $field[$k]["fieldname"] = "学号";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;
        }

        $field[$k]["fieldstring"] = "student_branch";
        $field[$k]["fieldname"] = "学员编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        if (!$contractOne || $contractOne['edition_id'] != '2') {
            $field[$k]["fieldstring"] = "student_tags";
            $field[$k]["fieldname"] = "学员标签";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "channel_name";
            $field[$k]["fieldname"] = "专案名称";
            $field[$k]["show"] = (isset($request["company_id"]) && $request["company_id"] == '8888') ? 1 : 0;
            $field[$k]["custom"] = 1;
            $k++;
        }

        $field[$k]["fieldstring"] = "enrolled_status";
        $field[$k]["fieldname"] = "状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_sex";
        $field[$k]["fieldname"] = "性别";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_birthday";
        $field[$k]["fieldname"] = "出生日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "family_cnname";
        $field[$k]["fieldname"] = "主要联系人";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "family_mobile";
        $field[$k]["fieldname"] = "主要联系电话";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_idcard";
        $field[$k]["fieldname"] = "证件号码";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        if (!$contractOne || $contractOne['edition_id'] != '2') {
            $field[$k]["fieldstring"] = "student_balance";
            $field[$k]["fieldname"] = "账户余额";
            $field[$k]["show"] = 0;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "student_forwardprice";
            $field[$k]["fieldname"] = "账户结转余额";
            $field[$k]["show"] = 0;
            $field[$k]["custom"] = 1;
            $k++;

//            $field[$k]["fieldstring"] = "coursecatbalance_left";
//            $field[$k]["fieldname"] = "预收金额";
//            $field[$k]["show"] = 0;
//            $field[$k]["custom"] = 1;
//            $k++;

            $field[$k]["fieldstring"] = "arrears";
            $field[$k]["fieldname"] = "欠费金额";
            $field[$k]["show"] = 0;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "property_integralbalance";
            $field[$k]["fieldname"] = "账户积分";
            $field[$k]["show"] = 0;
            $field[$k]["custom"] = 1;
            $k++;
        }

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => '暂无学员信息', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //分班列表
    function studentSeparateClassApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $StudentModel = new \Model\Smc\StudentModel($request);
        $res = $StudentModel->studentSeparateClass($request);

        $field = array();
        $field[0]["fieldstring"] = "student_id";
        $field[0]["fieldname"] = "学员ID";
        $field[0]["show"] = 0;
        $field[0]["custom"] = 0;

        $field[1]["fieldstring"] = "student_cnname";
        $field[1]["fieldname"] = "学员中文名";
        $field[1]["show"] = 1;
        $field[1]["custom"] = 1;

        $field[2]["fieldstring"] = "student_enname";
        $field[2]["fieldname"] = "学员英文名";
        $field[2]["show"] = 1;
        $field[2]["custom"] = 1;

        $field[3]["fieldstring"] = "student_branch";
        $field[3]["fieldname"] = "学员编号";
        $field[3]["show"] = 1;
        $field[3]["custom"] = 1;

        $field[4]["fieldstring"] = "enrolled_status";
        $field[4]["fieldname"] = "状态";
        $field[4]["show"] = 1;
        $field[4]["custom"] = 1;

        $field[5]["fieldstring"] = "student_sex";
        $field[5]["fieldname"] = "性别";
        $field[5]["show"] = 1;
        $field[5]["custom"] = 1;

        $field[6]["fieldstring"] = "student_birthday";
        $field[6]["fieldname"] = "出生日期";
        $field[6]["show"] = 1;
        $field[6]["custom"] = 1;

        $field[7]["fieldstring"] = "family_mobile";
        $field[7]["fieldname"] = "主要联系电话";
        $field[7]["show"] = 1;
        $field[7]["custom"] = 1;

        $field[8]["fieldstring"] = "class_cnname";
        $field[8]["fieldname"] = "班级名称";
        $field[8]["show"] = 1;
        $field[8]["custom"] = 1;

        $field[9]["fieldstring"] = "class_branch";
        $field[9]["fieldname"] = "班级编号";
        $field[9]["show"] = 1;
        $field[9]["custom"] = 1;

        $field[10]["fieldstring"] = "course_cnname";
        $field[10]["fieldname"] = "课程别名称";
        $field[10]["show"] = 1;
        $field[10]["custom"] = 1;

        $field[11]["fieldstring"] = "course_branch";
        $field[11]["fieldname"] = "课程别编号";
        $field[11]["show"] = 1;
        $field[11]["custom"] = 1;

        $field[12]["fieldstring"] = "study_beginday";
        $field[12]["fieldname"] = "入班时间";
        $field[12]["show"] = 1;
        $field[12]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => '暂无学员分班信息', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    function stuOrderArrearsApi()
    {
        $request = Input('get.', '', 'trim,addslashes');

        $this->ThisVerify($request);
        $StudentModel = new \Model\Smc\StudentModel($request);
        $res = $StudentModel->stuOrderArrears($request);
        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "student_id";
        $field[$k]["fieldname"] = "学员ID";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "trading_pid";
        $field[$k]["fieldname"] = "交易编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "order_status_name";
        $field[$k]["fieldname"] = "订单状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "order_allprice";
        $field[$k]["fieldname"] = "订单总额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "order_paymentprice";
        $field[$k]["fieldname"] = "实付金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "order_arrearageprice";
        $field[$k]["fieldname"] = "欠费金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "order_paidprice";
        $field[$k]["fieldname"] = "已付金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["fieldname"] = "学员中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_enname";
        $field[$k]["fieldname"] = "学员英文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_branch";
        $field[$k]["fieldname"] = "学员编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "course_cnname";
        $field[$k]["fieldname"] = "课程别名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "course_branch";
        $field[$k]["fieldname"] = "课程别编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_cnname";
        $field[$k]["fieldname"] = "当前班级名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_enname";
        $field[$k]["fieldname"] = "当前班级别名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_branch";
        $field[$k]["fieldname"] = "当前班级编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursebalance_time";
        $field[$k]["fieldname"] = "课程剩余课次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "hasaragetime";
        $field[$k]["fieldname"] = "已欠费课次数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "last_hour_time";
        $field[$k]["fieldname"] = "预计欠费课次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "last_hour_day";
        $field[$k]["fieldname"] = "预计欠费日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_enddate";
        $field[$k]["fieldname"] = "班级结束日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "typecoursebalance_time";
        $field[$k]["fieldname"] = "班组剩余课次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "typecoursebalance_figure";
        $field[$k]["fieldname"] = "班组剩余余额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "all_balance";
        $field[$k]["fieldname"] = "账户余额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];

        if ($res) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result, 'allnum' => $res['allnum']);
        } else {
            $result["list"] = array();
            $res['allnum'] = 0;
            $res = array('error' => 1, 'errortip' => '暂无学员欠费信息', 'result' => $result, 'allnum' => $res['allnum']);
        }
        ajax_return($res, $request['language_type']);
    }

    //欠费学员列表
    function studentArrearsClassApi()
    {
        $request = Input('get.', '', 'trim,addslashes');

        $this->ThisVerify($request);
        $StudentModel = new \Model\Smc\StudentModel($request);
        $res = $StudentModel->studentArrearsClass($request);
//		s.studnt_id,s.student_cnname,s.student_enname,s.student_branch,s.student_sex,po.order_pid,po.order_paymentprice,po.order_paidprice
        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "student_id";
        $field[$k]["fieldname"] = "学员ID";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["fieldname"] = "学员中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_enname";
        $field[$k]["fieldname"] = "学员英文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_branch";
        $field[$k]["fieldname"] = "学员编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_sex";
        $field[$k]["fieldname"] = "性别";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursecat_cnname";
        $field[$k]["fieldname"] = "班种";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "course_cnname";
        $field[$k]["fieldname"] = "课程别名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "course_branch";
        $field[$k]["fieldname"] = "课程别编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "order_pid";
        $field[$k]["fieldname"] = "订单号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "order_paymentprice";
        $field[$k]["fieldname"] = "订单总价";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "order_paidprice";
        $field[$k]["fieldname"] = "已付金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "order_arrearsdprice";
        $field[$k]["fieldname"] = "欠费金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursebalance_figure";
        $field[$k]["fieldname"] = "课程余额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];

        if ($res) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result, 'allnum' => $res['allnum']);
        } else {
            $result["list"] = array();
            $res['allnum'] = 0;
            $res = array('error' => 1, 'errortip' => '暂无学员欠费信息', 'result' => $result, 'allnum' => $res['allnum']);
        }
        ajax_return($res, $request['language_type']);

    }

    function exportAction()
    {

        $request = Input('get.', '', 'trim,addslashes');

        $this->ThisVerify($request);
        $datawhere = " 1 ";
        $having = " 1 ";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (s.student_cnname like '%{$request['keyword']}%' or s.student_enname like '%{$request['keyword']}%' or s.student_branch like '%{$request['keyword']}%' or po.order_pid like '{$request['keyword']}' )";
        }

        if (isset($request['arrears_order_type']) && $request['arrears_order_type'] !== '') {
            //实际欠费
            if ($request['arrears_order_type'] == 1) {
                $having .= " and coursebalance_figure<=(po.order_paymentprice-po.order_paidprice)";
            }
            //预欠费
            if ($request['arrears_order_type'] == 0) {
                $having .= " and (coursebalance_figure>(po.order_paymentprice-po.order_paidprice) or coursebalance_figure=null)";
            }
        }
        $sql = "select s.student_id,s.student_cnname,s.student_enname,s.student_branch,s.student_sex,po.order_pid,po.order_paymentprice,po.order_paidprice,co.course_cnname,co.course_branch,cc.coursecat_cnname,cc.coursecat_branch
                  ,(select sum(ssc.coursebalance_figure)
                  from smc_payfee_order_course as spoc
                  left join smc_student_coursebalance as ssc on ssc.course_id=spoc.course_id
                  where spoc.order_pid=po.order_pid and ssc.student_id=po.student_id and ssc.coursebalance_status<>3 and ssc.school_id=po.school_id
                  ) as coursebalance_figure
                  from smc_payfee_order as po
                  left join smc_payfee_order_course as poc on poc.order_pid=po.order_pid
                  left join smc_student as s on s.student_id=po.student_id
                  left join smc_course as co on co.course_Id=poc.course_id
                  left join smc_code_coursecat as cc on cc.coursecat_id=co.coursecat_id
                  where {$datawhere} and po.company_id='{$request['company_id']}' and po.school_id='{$request['school_id']}' and (po.order_status between 1 and 3)
                  GROUP BY po.order_pid
                  HAVING {$having}
                  order by po.order_createtime desc";

        $dateexcelarray = $this->DataControl->selectClear($sql);
        if (!$dateexcelarray) {
            return false;
        }
        foreach ($dateexcelarray as &$value) {
            $value['order_arrearsdprice'] = $value['order_paymentprice'] - $value['order_paidprice'];
        }
        $outexceldate = array();
        if ($dateexcelarray) {
            $outexceldate = array();
            foreach ($dateexcelarray as $dateexcelvar) {
                $datearray = array();
                $datearray['student_id'] = $dateexcelvar['student_id'];
                $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                $datearray['student_enname'] = $dateexcelvar['student_enname'];
                $datearray['student_branch'] = $dateexcelvar['student_branch'];
                $datearray['student_sex'] = $dateexcelvar['student_sex'];
                $datearray['coursecat_cnname'] = $dateexcelvar['coursecat_cnname'];
                $datearray['course_cnname'] = $dateexcelvar['course_cnname'];
                $datearray['course_branch'] = $dateexcelvar['course_branch'];
                $datearray['order_pid'] = $dateexcelvar['order_pid'];
                $datearray['order_paymentprice'] = $dateexcelvar['order_paymentprice'];
                $datearray['order_paidprice'] = $dateexcelvar['order_paidprice'];
                $datearray['order_arrearsdprice'] = $dateexcelvar['order_arrearsdprice'];
                $datearray['coursebalance_figure'] = $dateexcelvar['coursebalance_figure'];
                $outexceldate[] = $datearray;
            }
        }
        $excelheader = array("学员ID", "学员中文名", "学员英文名", "学员编号", "性别", "班种", "课程别名称", "课程别编号", "订单号", "订单总价", "已付金额", "欠费金额", "课程余额");
        $excelfileds = array('student_id', 'student_cnname', 'student_enname', 'student_branch', 'student_sex', 'coursecat_cnname', 'course_cnname', 'course_branch', 'order_pid', 'order_paymentprice', 'order_paidprice', 'order_arrearsdprice', 'coursebalance_figure');
        query_to_excel($excelheader, $outexceldate, $excelfileds, "学员账单欠费预警明细.xlsx");

//		$str="https://smcapi.kedingdang.com/Student/exportAction?staffer_id=".$request['staffer_id']."&token=".$request['token']."&school_id=".$request['school_id']."&company_id=".$request['company_id']."&arrears_order_type=&keyword=";
        $res = array('error' => 0, 'errortip' => '导出成功', 'result' => array());
        ajax_return($res, $request['language_type']);

    }


    //学员课程预警
    function studentCourseWarningApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $StudentModel = new \Model\Smc\StudentModel($request);
        $datalist = $StudentModel->studentCourseWarning($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "coursebalance_id";
        $field[$k]["fieldname"] = "余额id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "student_id";
        $field[$k]["fieldname"] = "学员id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["fieldname"] = "学员中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_enname";
        $field[$k]["fieldname"] = "学员英文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_branch";
        $field[$k]["fieldname"] = "学员编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_cnname";
        $field[$k]["fieldname"] = "班级名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_branch";
        $field[$k]["fieldname"] = "班级编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "hour_day";
        $field[$k]["fieldname"] = "班级结束日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "course_cnname";
        $field[$k]["fieldname"] = "课程别名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "course_branch";
        $field[$k]["fieldname"] = "课程别编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursebalance_time";
        $field[$k]["fieldname"] = "课程剩余课次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursetype_time";
        $field[$k]["fieldname"] = "班组剩余课次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursetype_figure";
        $field[$k]["fieldname"] = "班组剩余金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;


        $result = array();
        $result['fieldcustom'] = 0;   //支持自定义
        $result['field'] = $field;
        if ($datalist['list']) {
            $result['list'] = $datalist['list'];
        } else {
            $result['list'] = array();
        }
        if (isset($datalist['allnums']) && $datalist['allnums'] != "") {
            $allnum = intval($datalist['allnums']);
        } else {
            $allnum = 0;
        }

        if ($datalist['list']) {
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result, "allnum" => $allnum);
        } else {
            $res = array('error' => '1', 'errortip' => "暂无学员课程预警记录", 'result' => $result, "allnum" => $allnum);
        }

        ajax_return($res, $request['language_type']);

    }

    //班级升班预警
    function classUpWarningApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $StudentModel = new \Model\Smc\StudentModel($request);
//		c.class_id,c.class_cnname,c.class_enname,c.class_branch,ce.course_cnname,ce.course_branch,
        $datalist = $StudentModel->classUpWarning($request);
        $fieldstring = array('class_id', 'class_cnname', 'class_enname', 'class_branch', 'course_cnname', 'course_branch', 'hour_num', 'stuNum', 'hour_day');
        $fieldname = array('班级id', '班级名称', '班级别名', '班级编号', '课程别名称', '课程别编号', '班级剩余课次', '预计升班学员数量', '预计升班时间');
        $fieldcustom = array('0', "1", "1", "1", "1", "1", "1", "1", "1");
        $fieldshow = array('0', "1", "1", "1", "1", "1", "1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["fieldname"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 1;   //不支持自定义
        $result['field'] = $field;
        if ($datalist['list']) {
            $result['list'] = $datalist['list'];
        } else {
            $result['list'] = array();
        }
        if (isset($datalist['allnums']) && $datalist['allnums'] != "") {
            $allnum = intval($datalist['allnums']);
        } else {
            $allnum = 0;
        }
        if ($datalist['list']) {
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result, "allnum" => $allnum);
        } else {
            $res = array('error' => '1', 'errortip' => "暂无班级需要升班", 'result' => $result, "allnum" => $allnum);
        }

        ajax_return($res, $request['language_type']);
    }

    //学员流失预警
    function stuLostWarningApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $StudentModel = new \Model\Smc\StudentModel($request);
        $datalist = $StudentModel->stuLostWarning($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "school_id";
        $field[$k]["fieldname"] = "学校id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "student_id";
        $field[$k]["fieldname"] = "学员id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["fieldname"] = "学员中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_enname";
        $field[$k]["fieldname"] = "学员英文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_branch";
        $field[$k]["fieldname"] = "学员编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursetype_cnname";
        $field[$k]["fieldname"] = "班组名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursetype_branch";
        $field[$k]["fieldname"] = "班组编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "times_spend";
        $field[$k]["fieldname"] = "累计耗课次数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "last_atte_date";
        $field[$k]["fieldname"] = "最后出勤日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_cnname";
        $field[$k]["fieldname"] = "最后就读班级名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_enname";
        $field[$k]["fieldname"] = "最后就读班级别名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_branch";
        $field[$k]["fieldname"] = "最后就读班级编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "left_amount";
        $field[$k]["fieldname"] = "班组剩余金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "left_times";
        $field[$k]["fieldname"] = "班组剩余课次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "left_coursebalance_others";
        $field[$k]["fieldname"] = "其他班组余额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "left_balance";
        $field[$k]["fieldname"] = "账户余额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "left_prerece";
        $field[$k]["fieldname"] = "班组预收余额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "warning_days";
        $field[$k]["fieldname"] = "流失预警天数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result['fieldcustom'] = 0;   //支持自定义
        $result['field'] = $field;
        if ($datalist['list']) {
            $result['list'] = $datalist['list'];
        } else {
            $result['list'] = array();
        }
        if (isset($datalist['allnums']) && $datalist['allnums'] != "") {
            $allnum = intval($datalist['allnums']);
        } else {
            $allnum = 0;
        }

        if ($datalist['list']) {
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result, "allnum" => $allnum);
        } else {
            $res = array('error' => '1', 'errortip' => "暂无学员流失预警信息", 'result' => $result, "allnum" => $allnum);
        }

        ajax_return($res, $request['language_type']);

    }

    function signStuLossAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ChangeModel = new \Model\Smc\StudentModel($request);
        $res = $ChangeModel->signStuLoss($request);
        $result = array();
        if ($res) {
            $result["list"] = $res;
            $result = array('error' => 0, 'errortip' => '申请保留成功', 'result' => $result);
        } else {
            $result = array('error' => 1, 'errortip' => $ChangeModel->errortip, 'result' => $result);
        }
        ajax_return($result, $request['language_type']);
    }


    function studentOneView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $StudentModel = new \Model\Smc\StudentModel($request);
        $res = $StudentModel->studentOne($request);
        $result = array();
        if ($res) {
            $result["list"] = $res['list'];
            $result["tags_list"] = $res['tags_list'];
            $result["family_list"] = $res['family_list'];
            $result["issupervise"] = $res['issupervise'];
            $result["staffismanage"] = $res['staffismanage'];//是否技术账号  1 是  0不是
            $result["coursebalance_issupervise"] = $res['coursebalance_issupervise'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["tags_list"] = array();
            $result["family_list"] = array();
            $result["staffismanage"] = 0;//是否技术账号  1 是  0不是
            $res = array('error' => 1, 'errortip' => '获取信息', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function studentOneBalanceApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $StudentModel = new \Model\Smc\StudentModel($request);
        $res = $StudentModel->studentOneBalance($request);

        $fieldstring = array('companies_id', 'companies_cnname', 'supervise_left', 'student_balance', 'student_withholdbalance', 'student_balance_all', 'companies_issupervise');
        $fieldname = array('主体id', '主体名称', '是否有监管余额', '可退金额', '不可退金额', '账户总余额', '是否监管');
        $fieldcustom = array('0', "1", "1", "1", "1", "0", "0");
        $fieldshow = array('0', "1", "1", "1", "1", "0", "0");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["fieldname"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 1;   //不支持自定义
        $result['field'] = $field;
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => $StudentModel->errortip, 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $StudentModel->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function studentOneItemApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $StudentModel = new \Model\Smc\StudentModel($request);
        $res = $StudentModel->studentOneItem($request);
        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取详细信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '获取详细信息', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function studentOneEditItemAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);

        $StudentModel = new \Model\Smc\StudentModel($request);
        $res = $StudentModel->studentOneEditItem($request);
        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '编辑详细信息成功', 'result' => $result);
            $this->addSmcWorkLog($request['company_id'], $request['school_id'], $request['staffer_id'], "班务管理->学员管理->学员详情", '编辑学员资料', dataEncode($request));
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $StudentModel->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function studentSubscriptionAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);

        $StudentModel = new \Model\Smc\StudentModel($request);
        $res = $StudentModel->studentSubscription($request);
        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '认缴成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $StudentModel->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function studentEmptyForwardAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);

        $StudentModel = new \Model\Smc\StudentModel($request);
        $res = $StudentModel->studentEmptyForward($request);
        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '清空成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $StudentModel->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function studentCourseApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $StudentModel = new \Model\Smc\StudentModel($request);
        $res = $StudentModel->studentCourse($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "course_id";
        $field[$k]["fieldname"] = "课程ID";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "student_id";
        $field[$k]["fieldname"] = "学员ID";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "agreement_id";
        $field[$k]["fieldname"] = "协议ID";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "course_cnname";
        $field[$k]["fieldname"] = "课程别名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "course_branch";
        $field[$k]["fieldname"] = "课程别编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "coursetype_cnname";
        $field[$k]["fieldname"] = "班组名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "coursetype_branch";
        $field[$k]["fieldname"] = "班组编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "tuition_originalprice";
        $field[$k]["fieldname"] = "销售价";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "tuition_unitprice";
        $field[$k]["fieldname"] = "销售单价";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "tuition_buypiece";
        $field[$k]["fieldname"] = "标准课次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "buy_price";
        $field[$k]["fieldname"] = "购买金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "buy_times";
        $field[$k]["fieldname"] = "购买课次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "unit_price";
        $field[$k]["fieldname"] = "课消单价";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "spend_times";
        $field[$k]["fieldname"] = "已消耗课次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "income_total";
        $field[$k]["fieldname"] = "课消总收入";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 0;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => '学员暂无购买课程信息', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //代领 优惠券列表
    function getStuReceiveCouponsApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);

        $StudentModel = new \Model\Smc\StudentModel($request);
        $res = $StudentModel->getStuReceiveCoupons($request);
        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $StudentModel->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //优惠券领取 发放程序
    function receiveCouponsAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);

        $StudentModel = new \Model\Smc\StudentModel($request);
        $res = $StudentModel->receiveCoupons($request);
        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '领取成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $StudentModel->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function studentCourseConsumeApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $StudentModel = new \Model\Smc\StudentModel($request);
        $res = $StudentModel->studentCourseConsume($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "log_day";
        $field[$k]["fieldname"] = "考勤日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "class_cnname";
        $field[$k]["fieldname"] = "班级名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "class_branch";
        $field[$k]["fieldname"] = "班级编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "lesson_position";
        $field[$k]["fieldname"] = "课次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "log_playamount";
        $field[$k]["fieldname"] = "金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "log_frequency";
        $field[$k]["fieldname"] = "上课堂数";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "log_reason";
        $field[$k]["fieldname"] = "备注";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "log_time";
        $field[$k]["fieldname"] = "创建时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 0;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => '暂无交易记录', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function studentCourseTransactionApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $StudentModel = new \Model\Smc\StudentModel($request);
        $res = $StudentModel->studentCourseTransaction($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "log_playname";
        $field[$k]["fieldname"] = "交易名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "log_playamount";
        $field[$k]["fieldname"] = "交易金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

//        $field[$k]["fieldstring"] = "log_playclass";
//        $field[$k]["fieldname"] = "交易类型";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 0;
//        $k++;
//
//        $field[$k]["fieldstring"] = "log_day";
//        $field[$k]["fieldname"] = "交易日期";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 0;
//        $k++;

        $field[$k]["fieldstring"] = "log_reason";
        $field[$k]["fieldname"] = "备注";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "log_time";
        $field[$k]["fieldname"] = "创建时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;


        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res['list']) {
            $result["fieldcustom"] = 0;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => '暂无交易记录', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //人脸采集记录
    function getStuPortraitApi(){
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\StudentModel($request);
        $Model->getStuPortraitApi($request);
        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $Model->result), $request['language_type']);
    }

    function studentClassApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $StudentModel = new \Model\Smc\StudentModel($request);
        $res = $StudentModel->studentClass($request);
        $field = array();
        $contractOne = $this->getContract($request['company_id']);

        $k = 0;
        $field[$k]["fieldstring"] = "class_id";
        $field[$k]["fieldname"] = "班级id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "class_cnname";
        $field[$k]["fieldname"] = "班级名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_enname";
        $field[$k]["fieldname"] = "班级别名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "class_branch";
        $field[$k]["fieldname"] = "班级编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $field[$k]["haveDetail"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "course_cnname";
        $field[$k]["fieldname"] = "课程别名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "course_branch";
        $field[$k]["fieldname"] = "课程别编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "coursetype_name";
        $field[$k]["fieldname"] = "所属班组";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "class_status";
        $field[$k]["fieldname"] = "班级状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "class_type";
        $field[$k]["fieldname"] = "班级类型";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "study_isreading_name";
        $field[$k]["fieldname"] = "在班状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "class_teacher";
        $field[$k]["fieldname"] = "主教教师";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "study_during";
        $field[$k]["fieldname"] = "上课时间";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_room";
        $field[$k]["fieldname"] = "教室";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        if (!$contractOne || $contractOne['edition_id'] != '2') {
            $field[$k]["fieldstring"] = "spend_times";
            $field[$k]["fieldname"] = "学员已耗课次";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 0;
            $k++;

            $field[$k]["fieldstring"] = "coursebalance_time";
            $field[$k]["fieldname"] = "学员剩余课次";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 0;
            $k++;

            $field[$k]["fieldstring"] = "num_fullnum";
            $field[$k]["fieldname"] = "上课进度";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 0;
            $k++;
        }

        $field[$k]["fieldstring"] = "study_during";
        $field[$k]["fieldname"] = "在班日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

//        $field[$k]["fieldstring"] = "study_endday";
//        $field[$k]["fieldname"] = "出班日期";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 0;
//        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 0;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => '学员暂无入班信息', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //转班时 班级列表
    function studentClassListApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $StudentModel = new \Model\Smc\StudentModel($request);
        $res = $StudentModel->studentClassList($request);
        $fieldstring = array('class_id', 'class_branch', 'course_cnname', 'coursebalance_time', 'coursebalance_figure', ' ');
        $fieldname = array('ID', '中文名', '班级编号', '课程别', '剩余数量', '课程余额', '可结转金额');
        $fieldcustom = array('0', "1", "1", "1", "1", "1", "1");
        $fieldshow = array('0', "1", "1", "1", "1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["fieldname"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 0;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => $StudentModel->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }


    //单个学员的课表
    function studentTimetableApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $StudentModel = new \Model\Smc\StudentModel($request);
        $res = $StudentModel->studentTimetable($request);
        $result = array();
        $field = array();
        $field[0]["fieldname"] = "上午/下午";
        $field[0]["fieldstring"] = "noon_name";
        $field[0]["show"] = 1;
        $field[0]["custom"] = 0;
        $field[0]["is_title"] = 1;

        $field[1]["fieldname"] = "周一";
        $field[1]["fieldstring"] = "Monday";
        $field[1]["show"] = 1;
        $field[1]["custom"] = 0;

        $field[2]["fieldname"] = "周二";
        $field[2]["fieldstring"] = "Tuesday";
        $field[2]["show"] = 1;
        $field[2]["custom"] = 0;

        $field[3]["fieldname"] = "周三";
        $field[3]["fieldstring"] = "Wednesday";
        $field[3]["show"] = 1;
        $field[3]["custom"] = 0;

        $field[4]["fieldname"] = "周四";
        $field[4]["fieldstring"] = "Thursday";
        $field[4]["show"] = 1;
        $field[4]["custom"] = 0;

        $field[5]["fieldname"] = "周五";
        $field[5]["fieldstring"] = "Friday";
        $field[5]["show"] = 1;
        $field[5]["custom"] = 0;

        $field[6]["fieldname"] = "周六";
        $field[6]["fieldstring"] = "Saturday";
        $field[6]["show"] = 1;
        $field[6]["custom"] = 0;

        $field[7]["fieldname"] = "周日";
        $field[7]["fieldstring"] = "Sunday";
        $field[7]["show"] = 1;
        $field[7]["custom"] = 0;

        $result['field'] = $field;
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '获取信息', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //单个学员的考勤
    function studentAttendanceApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
//        $this->ThisVerify($request);
        $StudentModel = new \Model\Smc\StudentModel($request);
//        $res = $StudentModel->studentAttendance($request);
        $res = $StudentModel->studentHourstudy($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "school_id";
        $field[$k]["fieldname"] = "学校id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "学校名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "学校编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "class_cnname";
        $field[$k]["fieldname"] = "班级名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "class_enname";
        $field[$k]["fieldname"] = "班级别名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "class_branch";
        $field[$k]["fieldname"] = "班级编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "staffer_name";
        $field[$k]["fieldname"] = "主教教师";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "course_cnname";
        $field[$k]["fieldname"] = "课程别名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "course_branch";
        $field[$k]["fieldname"] = "课程别编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "hour_during";
        $field[$k]["fieldname"] = "上课时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "hour_way";
        $field[$k]["fieldname"] = "上课方式";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "hour_isfree";
        $field[$k]["fieldname"] = "是否免费上课";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "share_price";
        $field[$k]["fieldname"] = "分摊金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "hour_ischecking";
        $field[$k]["fieldname"] = "上课状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "hourstudy_checkin";
        $field[$k]["fieldname"] = "出勤状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "absence_reason";
        $field[$k]["fieldname"] = "缺勤原因";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $sql = "select 
            ifnull((select sum(income_price) from smc_school_income where school_id=a.school_id and student_id=a.student_id and income_type=0),0) as income_past
            ,ifnull((select sum(coursebalance_figure) from smc_student_coursebalance where school_id=a.school_id and student_id=a.student_id),0) as income_will
            from smc_student_enrolled a
            where student_id='{$request['student_id']}'
            and school_id='{$request['school_id']}'";
        $incomearray = $this->DataControl->selectOne($sql);

        $result = array();
        $result["field"] = $field;
        $result['income_past'] = $incomearray['income_past'];
        $result['income_will'] = $incomearray['income_will'];
        $result['income_all'] = $incomearray['income_past'] + $incomearray['income_will'];

        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 0;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => '学员暂无考勤记录', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //单个学员的免费课时列表
    function studentFreeTimesApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $StudentModel = new \Model\Smc\StudentModel($request);
        $res = $StudentModel->studentFreeTimes($request);
        $field = array();
        $k = 0;

        $field[$k]["fieldstring"] = "class_cnname";
        $field[$k]["fieldname"] = "班级名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "class_enname";
        $field[$k]["fieldname"] = "班级别名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "class_branch";
        $field[$k]["fieldname"] = "班级编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "course_cnname";
        $field[$k]["fieldname"] = "课程别名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "course_branch";
        $field[$k]["fieldname"] = "课程别编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "hour_lessontimes";
        $field[$k]["fieldname"] = "课次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "hour_day";
        $field[$k]["fieldname"] = "上课时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "is_use_name";
        $field[$k]["fieldname"] = "课时状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "order_pid";
        $field[$k]["fieldname"] = "订单编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "coursetimes_createtime";
        $field[$k]["fieldname"] = "创建时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 0;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => $StudentModel->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function cancelFreeTimesAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $StudentModel = new \Model\Smc\StudentModel($request);
        $res = $StudentModel->cancelFreeTimes($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '取消成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $StudentModel->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }


    //单个学员的订单
    function studentOrderApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $StudentModel = new \Model\Smc\StudentModel($request);
        $res = $StudentModel->studentOrder($request);

        $field = array();
        $field[0]["fieldstring"] = "order_pid";
        $field[0]["fieldname"] = "订单号";
        $field[0]["show"] = 1;
        $field[0]["custom"] = 0;

        $field[1]["fieldstring"] = "order_type";
        $field[1]["fieldname"] = "订单类型";
        $field[1]["show"] = 1;
        $field[1]["custom"] = 0;

        $field[2]["fieldstring"] = "order_from";
        $field[2]["fieldname"] = "订单来源";
        $field[2]["show"] = 1;
        $field[2]["custom"] = 0;

        $field[3]["fieldstring"] = "order_allprice";
        $field[3]["fieldname"] = "总价";
        $field[3]["show"] = 1;
        $field[3]["custom"] = 0;

        $field[4]["fieldstring"] = "order_coupon_price";
        $field[4]["fieldname"] = "优惠金额";
        $field[4]["show"] = 1;
        $field[4]["custom"] = 0;

        $field[5]["fieldstring"] = "balance_payprice";
        $field[5]["fieldname"] = "余额支付";
        $field[5]["show"] = 1;
        $field[5]["custom"] = 0;

        $field[6]["fieldstring"] = "order_createtime";
        $field[6]["fieldname"] = "下单时间";
        $field[6]["show"] = 1;
        $field[6]["custom"] = 0;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 0;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => $StudentModel->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //订单详情
    function studentOrderItemApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $StudentModel = new \Model\Smc\StudentModel($request);
        $res = $StudentModel->studentOrderItem($request);
        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '获取信息', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function stuChangeSchoolApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $StudentModel = new \Model\Smc\StudentModel($request);
        $res = $StudentModel->stuChangeSchool($request);
        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $StudentModel->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //单个学员异动列表
    function studentChangeApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ChangeModel = new \Model\Smc\ChangeModel($request);
        $res = $ChangeModel->classChange($request);
        $k = 0;
        $field = array();
        $field[$k]["fieldstring"] = "changelog_id";
        $field[$k]["fieldname"] = "异动ID";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "stuchange_name";
        $field[$k]["fieldname"] = "异动类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursetype_cnname";
        $field[$k]["fieldname"] = "异动班组名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursetype_branch";
        $field[$k]["fieldname"] = "异动班组编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "异动学校";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_cnname";
        $field[$k]["fieldname"] = "异动班级";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_branch";
        $field[$k]["fieldname"] = "异动班级编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "changelog_note";
        $field[$k]["fieldname"] = "异动描述";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "changelog_day";
        $field[$k]["fieldname"] = "异动日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "staffer_cnname";
        $field[$k]["fieldname"] = "经办人";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "stustatus_isdel";
        $field[$k]["fieldname"] = "是否可以删除";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "change_pid";
        $field[$k]["fieldname"] = "异动编号";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "stuchange_code";
        $field[$k]["fieldname"] = "异动代码";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $list = $this->DataControl->selectClear("select distinct 
	    x.stuchange_code,stuchange_name 
        from smc_code_stuchange X,smc_student_changelog Y 
        where X.stuchange_code=Y.stuchange_code  
        and Y.student_id='{$request['student_id']}' 
        and Y.school_id='{$request['school_id']}'");

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        $result["changecodelist"] = $list;
        if ($res) {
            $result["fieldcustom"] = 0;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => '学员暂无异动记录', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //单个学员的课程别余额
    function studentCoursebalanceApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $StudentModel = new \Model\Smc\StudentModel($request);
        $res = $StudentModel->studentCoursebalance($request);
        $field = array();

        $k = 0;
        $field[$k]["fieldstring"] = "course_cnname";
        $field[$k]["fieldname"] = "课程别名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "course_branch";
        $field[$k]["fieldname"] = "课程别编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "study_isreading";
        $field[$k]["fieldname"] = "在班状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "course_classnum";
        $field[$k]["fieldname"] = "标准课次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "ordercourse_buynums";
        $field[$k]["fieldname"] = "购买课次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "coursebalance_unitrefund";
        $field[$k]["fieldname"] = "耗课单价(原价)";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "coursebalance_unitearning";
        $field[$k]["fieldname"] = "课消单价(实收)";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;
//
//        $field[$k]["fieldstring"] = "tuition_sellingprice";
//        $field[$k]["fieldname"] = "销售价";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 0;
//        $k++;
//
//        $field[$k]["fieldstring"] = "order_paidprice";
//        $field[$k]["fieldname"] = "实收金额";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 0;
//        $k++;
//
//        $field[$k]["fieldstring"] = "order_coupon_price";
//        $field[$k]["fieldname"] = "优惠券金额";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 0;
//        $k++;

        $field[$k]["fieldstring"] = "coursebalance_time";
        $field[$k]["fieldname"] = "剩余课次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "coursebalance_figure";
        $field[$k]["fieldname"] = "课程余额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

//        $field[$k]["fieldstring"] = "course_id";
//        $field[$k]["fieldname"] = "课程别id";
//        $field[$k]["show"] = 0;
//        $field[$k]["custom"] = 0;
//        $k++;

        $field[$k]["fieldstring"] = "courseforward_price";
        $field[$k]["fieldname"] = "优惠余额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;


        $field[$k]["fieldstring"] = "companies_cnname";
        $field[$k]["fieldname"] = "所属主体";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "coursebalance_issupervise_name";
        $field[$k]["fieldname"] = "是否监管";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;


        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 0;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => '学员暂无课程信息', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function getStuCourseShareListApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $StudentModel = new \Model\Smc\StudentModel($request);
        $res = $StudentModel->getStuCourseShareList($request);
        $field = array();

        $k = 0;
        $field[$k]["fieldstring"] = "courseshare_month";
        $field[$k]["fieldname"] = "分摊月份";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "courseshare_sellingprice";
        $field[$k]["fieldname"] = "分摊金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;


        $result = array();
        $result["field"] = $field;
        if ($res) {
            $result["fieldcustom"] = 0;
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $StudentModel->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    function editCourseShareAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $StudentModel = new \Model\Smc\StudentModel($request);
        $res = $StudentModel->editCourseShare($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '编辑成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $StudentModel->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }


    function studentCoursebalanceItemApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $StudentModel = new \Model\Smc\StudentModel($request);
        $res = $StudentModel->studentCoursebalanceItem($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '获取信息', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function getStuBalanceListApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $StudentModel = new \Model\Smc\StudentModel($request);
        $res = $StudentModel->getStuBalanceList($request);

        $field = array();

        $k = 0;
        $field[$k]["fieldstring"] = "companies_cnname";
        $field[$k]["fieldname"] = "主体名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_balance";
        $field[$k]["fieldname"] = "可退金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_withholdbalance";
        $field[$k]["fieldname"] = "不可退金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;


        $result = array();
        $result["field"] = $field;
        if ($res) {
            $result["fieldcustom"] = 0;
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $StudentModel->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function studentForwardbalanceApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $StudentModel = new \Model\Smc\StudentModel($request);
        $res = $StudentModel->studentForwardbalance($request);
        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "balancelog_id";
        $field[$k]["fieldname"] = "结转余额日志ID";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "balancelog_playname";
        $field[$k]["fieldname"] = "交易类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

//		$field[2]["fieldstring"] = "balancelog_reason";
//		$field[2]["fieldname"] = "操作明细";
//		$field[2]["show"] = 1;
//		$field[2]["custom"] = 0;

        $field[$k]["fieldstring"] = "balancelog_fromamount";
        $field[$k]["fieldname"] = "原始结转余额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $field[$k]["is_money"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "balance";
        $field[$k]["fieldname"] = "交易金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $field[$k]["is_money"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "balancelog_finalamount";
        $field[$k]["fieldname"] = "完成结转余额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $field[$k]["is_money"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "staffer_cnname";
        $field[$k]["fieldname"] = "经办人";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "balancelog_time";
        $field[$k]["fieldname"] = "操作时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 0;
            $result["list"] = $res['list'];
            $result["type_list"] = $res['type_list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["type_list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => '学员暂无结转余额日志信息', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //学员杂费余额
    function studentCourseGoodsApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
//        $this->ThisVerify($request);
        $StudentModel = new \Model\Smc\StudentModel($request);
        $res = $StudentModel->studentCourseGoods($request);
        $field = array();

        $field[0]["fieldstring"] = "itemtimes_id";
        $field[0]["fieldname"] = "收费项目ID";
        $field[0]["show"] = 0;
        $field[0]["custom"] = 0;

        $field[1]["fieldstring"] = "feeitem_cnname";
        $field[1]["fieldname"] = "项目名称";
        $field[1]["show"] = 1;
        $field[1]["custom"] = 0;

        $field[2]["fieldstring"] = "feeitem_branch";
        $field[2]["fieldname"] = "项目编号";
        $field[2]["show"] = 1;
        $field[2]["custom"] = 0;

        $field[3]["fieldstring"] = "course_cnname";
        $field[3]["fieldname"] = "课程别名称";
        $field[3]["show"] = 1;
        $field[3]["custom"] = 0;

        $field[4]["fieldstring"] = "course_branch";
        $field[4]["fieldname"] = "课程别编号";
        $field[4]["show"] = 1;
        $field[4]["custom"] = 0;

        $field[5]["fieldstring"] = "itemtimes_figure";
        $field[5]["fieldname"] = "剩余金额";
        $field[5]["show"] = 1;
        $field[5]["custom"] = 0;
        $field[5]["is_money"] = 1;

        $field[6]["fieldstring"] = "itemtimes_number";
        $field[6]["fieldname"] = "剩余次数";
        $field[6]["show"] = 1;
        $field[6]["custom"] = 0;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        $result["list"] = $res['list'];

        $sql = "select feeitem_id,feeitem_branch,feeitem_cnname
              from smc_code_feeitem 
              where company_id='{$request['company_id']}' 
              order by feeitem_branch
              ";
        $item_list = $this->DataControl->selectClear($sql);

        if ($res) {
            $result["fieldcustom"] = 0;
            $result["list"] = $res['list'];
            $result["item_list"] = $item_list;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["allnum"] = 0;
            $result["list"] = array();
            $result["item_list"] = $item_list;
            $res = array('error' => 1, 'errortip' => '学员暂无杂项余额信息', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function studentAdvanceApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $StudentModel = new \Model\Smc\StudentModel($request);
        $res = $StudentModel->studentAdvance($request);
        $field = array();

        $k = 0;
        $field[$k]["fieldstring"] = "coursecatbalance_id";
        $field[$k]["fieldname"] = "预收ID";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "feetype_name";
        $field[$k]["fieldname"] = "预收类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "coursetype_cnname";
        $field[$k]["fieldname"] = "班组名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "coursetype_branch";
        $field[$k]["fieldname"] = "班组编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "coursecat_cnname";
        $field[$k]["fieldname"] = "班种名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "coursecat_branch";
        $field[$k]["fieldname"] = "班种编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "coursecatbalance_figure";
        $field[$k]["fieldname"] = "剩余金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "coursecatbalance_time";
        $field[$k]["fieldname"] = "剩余次数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;

        $field[$k]["fieldstring"] = "coursecatbalance_unitexpend";
        $field[$k]["fieldname"] = "单价";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "coursecatbalance_unitrefund";
        $field[$k]["fieldname"] = "退费单价";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "can_useName";
        $field[$k]["fieldname"] = "是否可用";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "can_refund";
        $field[$k]["fieldname"] = "是否可退费";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 0;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => '学员暂无预收余额信息', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function stuOtherSchoolBalanceApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\StudentModel($request);
        $res = $Model->stuOtherSchoolBalance($request);
        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "school_id";
        $field[$k]["fieldname"] = "学校ID";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "school_shortname";
        $field[$k]["fieldname"] = "学校名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "学校编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "companies_cnname";
        $field[$k]["fieldname"] = "主体名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "student_balance";
        $field[$k]["fieldname"] = "可退余额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "student_withholdbalance";
        $field[$k]["fieldname"] = "不可退余额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "companies_issupervise_name";
        $field[$k]["fieldname"] = "是否监管";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 0;
            $result["list"] = $res['list'];
            $result["issupervise"] = $res['issupervise'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function stuOtherSchoolCourseApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\StudentModel($request);
        $res = $Model->stuOtherSchoolCourse($request);
        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "coursebalance_id";
        $field[$k]["fieldname"] = "课程余额ID";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "school_shortname";
        $field[$k]["fieldname"] = "学校名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "学校编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "course_cnname";
        $field[$k]["fieldname"] = "课程别名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "course_branch";
        $field[$k]["fieldname"] = "课程别编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "coursebalance_status_name";
        $field[$k]["fieldname"] = "在班状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "coursebalance_unitrefund";
        $field[$k]["fieldname"] = "耗课单价";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "coursebalance_unitexpend";
        $field[$k]["fieldname"] = "课消单价";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "coursebalance_figure";
        $field[$k]["fieldname"] = "课程余额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "coursebalance_time";
        $field[$k]["fieldname"] = "剩余课次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "courseforward_price";
        $field[$k]["fieldname"] = "优惠余额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 0;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function studPaymentApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\StudentModel($request);
        $res = $Model->stuPayment($request);
        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "school_id";
        $field[$k]["fieldname"] = "学校ID";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "student_id";
        $field[$k]["fieldname"] = "学生ID";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "rece_name";
        $field[$k]["fieldname"] = "缴费名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "pay_no";
        $field[$k]["fieldname"] = "支付编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "play_amount";
        $field[$k]["fieldname"] = "缴费金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "play_name";
        $field[$k]["fieldname"] = "支付方式";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "play_day";
        $field[$k]["fieldname"] = "缴费日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "remk";
        $field[$k]["fieldname"] = "交易编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "order_note";
        $field[$k]["fieldname"] = "交易备注";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "staffer_name";
        $field[$k]["fieldname"] = "经办人";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "createtime";
        $field[$k]["fieldname"] = "创建日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 0;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    function stuSpendingApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\StudentModel($request);
        $res = $Model->stuSpending($request);
        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "income_type";
        $field[$k]["fieldname"] = "消费类别";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "course_branch";
        $field[$k]["fieldname"] = "消费课程别";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "class_branch";
        $field[$k]["fieldname"] = "班级编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "class_enname";
        $field[$k]["fieldname"] = "班级别名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "play_amount";
        $field[$k]["fieldname"] = "消费金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "play_times";
        $field[$k]["fieldname"] = "消费次数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "st_date";
        $field[$k]["fieldname"] = "开始日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "end_date";
        $field[$k]["fieldname"] = "结束日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "remk";
        $field[$k]["fieldname"] = "备注";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 0;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function stuRegisterApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\StudentModel($request);
        $res = $Model->stuRegisterInfo($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "info_id";
        $field[$k]["fieldname"] = "数据id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "coursetype_cnname";
        $field[$k]["fieldname"] = "班组名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "coursetype_branch";
        $field[$k]["fieldname"] = "班组编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "pay_price";
        $field[$k]["fieldname"] = "首缴金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "pay_day";
        $field[$k]["fieldname"] = "原首缴日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

//        $field[$k]["fieldstring"] = "calc_day";
//        $field[$k]["fieldname"] = "业绩日期";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 0;
//        $k++;

        $field[$k]["fieldstring"] = "change_day";
        $field[$k]["fieldname"] = "申请变更首缴日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "apply_staffer_cnname";
        $field[$k]["fieldname"] = "申请人";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "apply_time";
        $field[$k]["fieldname"] = "申请时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "apply_reason";
        $field[$k]["fieldname"] = "申请原因";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "confirm_status";
        $field[$k]["fieldname"] = "审核状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "confirm_staffer_cnname";
        $field[$k]["fieldname"] = "审核人";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "confirm_time";
        $field[$k]["fieldname"] = "审核时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "confirm_reason";
        $field[$k]["fieldname"] = "备注";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "is_change";
        $field[$k]["fieldname"] = "是否可申请变更";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 0;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //编辑在籍时间
    function updateRegisterTimeApplyAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\StudentModel($request);
        $result = $Model->updateRegisterTimeApply($request);

        if ($result) {
            $res = array('error' => $result['error'], 'errortip' => $result['errortip'], 'result' => array());
        } else {
            $res = array('error' => '1', 'errortip' => '申请失败', 'result' => array());
        }
        ajax_return($res, $request['language_type']);
    }

    //单个学员的账户余额日志
    function studentBalanceApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $StudentModel = new \Model\Smc\StudentModel($request);
        $res = $StudentModel->studentBalance($request);

        $stafferOne=$this->DataControl->getFieldOne("smc_staffer","staffer_ismanage","staffer_id='{$request['staffer_id']}'");

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "balancelog_id";
        $field[$k]["fieldname"] = "余额日志ID";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "companies_id";
        $field[$k]["fieldname"] = "交易主体id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "companies_cnname";
        $field[$k]["fieldname"] = "交易主体";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "balancelog_playname";
        $field[$k]["fieldname"] = "交易类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

//        $field[2]["fieldstring"] = "school_cnname";
//        $field[2]["fieldname"] = "经办校区";
//        $field[2]["show"] = 1;
//        $field[2]["custom"] = 0;
//        $k++;

        $field[$k]["fieldstring"] = "fromamount";
        $field[$k]["fieldname"] = "原始金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $field[$k]["is_money"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "balance";
        $field[$k]["fieldname"] = "交易金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $field[$k]["is_money"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "finalamount";
        $field[$k]["fieldname"] = "完成金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $field[$k]["is_money"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "staffer_cnname";
        $field[$k]["fieldname"] = "经办人";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        if($stafferOne && $stafferOne['staffer_ismanage']==1){
            $field[$k]["fieldstring"] = "balancelog_filejson";
            $field[$k]["fieldname"] = "上传附件";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 0;
            $field[$k]["isImgFiles"] = 1;
            $k++;
        }

        $field[$k]["fieldstring"] = "balancelog_reason";
        $field[$k]["fieldname"] = "操作备注";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "balancelog_time";
        $field[$k]["fieldname"] = "操作时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 0;
            $result["list"] = $res['list'];
            $result["type_list"] = $res['type_list'];
            $result["companies_list"] = $res['companies_list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $result["type_list"] = array();
            $result["companies_list"] = array();
            $res = array('error' => 1, 'errortip' => '学员暂无账户余额日志信息', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function getStuTradeOneApi()
    {

        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\StudentModel($request);
        $res = $Model->getStuTradeOne($request);
        $result = array();
        if ($res) {
            $result["list"] = $res;
            $result = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
        } else {
            $result = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($result, $request['language_type']);
    }

    function studentGoodsApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $StudentModel = new \Model\Smc\StudentModel($request);
        $res = $StudentModel->studentGoods($request);
        $field = array();

        $k = 0;
        $field[$k]["fieldstring"] = "erpgoods_id";
        $field[$k]["fieldname"] = "资产ID";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["fieldname"] = "学员中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_enname";
        $field[$k]["fieldname"] = "学员英文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_branch";
        $field[$k]["fieldname"] = "学员编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "goods_cnname";
        $field[$k]["fieldname"] = "商品名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "ordergoods_buynums";
        $field[$k]["fieldname"] = "商品数量";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "ordergoods_totalprice";
        $field[$k]["fieldname"] = "商品总价";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "order_pid";
        $field[$k]["fieldname"] = "交易编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "ordergoods_buynums";
        $field[$k]["fieldname"] = "数量";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

//        $field[$k]["fieldstring"] = "isreceive";
//        $field[$k]["fieldname"] = "是否领用";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//        $k++;

        $field[$k]["fieldstring"] = "erpgoods_receivetime";
        $field[$k]["fieldname"] = "领用时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "beoutorder_pid";
        $field[$k]["fieldname"] = "出库编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "erpgoods_createtime";
        $field[$k]["fieldname"] = "创建时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "erpgoods_status";
        $field[$k]["fieldname"] = "商品状态";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "erpgoods_status_name";
        $field[$k]["fieldname"] = "商品状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
//
//        $field[$k]["fieldstring"] = "isreceive";
//        $field[$k]["fieldname"] = "是否已领用";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//        $k++;
//
//        $field[$k]["fieldstring"] = "isrestore";
//        $field[$k]["fieldname"] = "是否归还";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//        $k++;

        $field[$k]["fieldstring"] = "isfree";
        $field[$k]["fieldname"] = "是否免费";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "isrefund";
        $field[$k]["fieldname"] = "是否退费";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "ispaid";
        $field[$k]["fieldname"] = "是否支付";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 0;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => '学员暂无领用资产信息', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function goodsConsumeLogApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\StudentModel($request);
        $res = $Model->goodsConsumeLog($request);
        $field = array();

        $k = 0;
        $field[$k]["fieldstring"] = "feeitem_class_name";
        $field[$k]["fieldname"] = "项目类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "log_reason";
        $field[$k]["fieldname"] = "项目明细";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

//        $field[$k]["fieldstring"] = "log_playclass";
//        $field[$k]["fieldname"] = "操作类型";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 0;
//        $k++;

        $field[$k]["fieldstring"] = "log_playamount";
        $field[$k]["fieldname"] = "交易金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $field[$k]["isRed"] = 1;
        $k++;

//        $field[$k]["fieldstring"] = "school_cnname";
//        $field[$k]["fieldname"] = "经办人";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 0;
//        $k++;

        $field[$k]["fieldstring"] = "log_time";
        $field[$k]["fieldname"] = "操作时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 0;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function receiveGoodsAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $StudentModel = new \Model\Smc\StudentModel($request);
        $res = $StudentModel->receiveGoods($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '领用成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $StudentModel->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function receiveGoodsForQiquAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $StudentModel = new \Model\Smc\StudentModel($request);
        $res = $StudentModel->receiveGoods($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '领用成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $StudentModel->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function studentCouponsApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $StudentModel = new \Model\Smc\StudentModel($request);
        $res = $StudentModel->studentCoupons($request);
        $field = array();

        $field[0]["fieldstring"] = "coupons_id";
        $field[0]["fieldname"] = "优惠券ID";
        $field[0]["show"] = 0;
        $field[0]["custom"] = 0;

        $field[1]["fieldstring"] = "ticket_cnname";
        $field[1]["fieldname"] = "优惠券名称";
        $field[1]["show"] = 1;
        $field[1]["custom"] = 0;

        $field[2]["fieldstring"] = "coupons_class";
        $field[2]["fieldname"] = "来源类型";
        $field[2]["show"] = 1;
        $field[2]["custom"] = 0;

        $field[3]["fieldstring"] = "course";
        $field[3]["fieldname"] = "适用课程";
        $field[3]["show"] = 0;
        $field[3]["custom"] = 0;

        $field[4]["fieldstring"] = "ticket_way";
        $field[4]["fieldname"] = "优惠模式";
        $field[4]["show"] = 1;
        $field[4]["custom"] = 0;

        $field[5]["fieldstring"] = "policy_derateprice";
        $field[5]["fieldname"] = "优惠券金额";
        $field[5]["show"] = 1;
        $field[5]["custom"] = 0;

        $field[6]["fieldstring"] = "policy_deraterate";
        $field[6]["fieldname"] = "优惠折扣";
        $field[6]["show"] = 1;
        $field[6]["custom"] = 0;

        $field[7]["fieldstring"] = "policy_minprice";
        $field[7]["fieldname"] = "最低课程金额";
        $field[7]["show"] = 1;
        $field[7]["custom"] = 0;

        $field[8]["fieldstring"] = "ticket_status";
        $field[8]["fieldname"] = "优惠券状态";
        $field[8]["show"] = 0;
        $field[8]["custom"] = 0;

        $field[8]["fieldstring"] = "coupons_reason";
        $field[8]["fieldname"] = "申请原因";
        $field[8]["show"] = 1;
        $field[8]["custom"] = 0;

        $field[9]["fieldstring"] = "coupons_isuse_name";
        $field[9]["fieldname"] = "使用状态";
        $field[9]["show"] = 1;
        $field[9]["custom"] = 0;

        $field[10]["fieldstring"] = "coupons_usetime";
        $field[10]["fieldname"] = "使用时间";
        $field[10]["show"] = 1;
        $field[10]["custom"] = 0;

        $field[11]["fieldstring"] = "coupons_exittime";
        $field[11]["fieldname"] = "有效期";
        $field[11]["show"] = 1;
        $field[11]["custom"] = 0;

        $field[12]["fieldstring"] = "coupons_createtime";
        $field[12]["fieldname"] = "创建时间";
        $field[12]["show"] = 0;
        $field[12]["custom"] = 0;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 0;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => '学员暂无优惠券信息', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //激活（复制）一张 7 天有效期的优惠券
    function copyStudentCouponOneAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\StudentModel($request);
        $res = $Model->copyStudentCouponOne($request);

        if ($res) {
            $res = array('error' => 0, 'errortip' => $Model->errortip, 'result' => array());
        } else {
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => array());
        }
        ajax_return($res, $request['language_type']);
    }

    function invalidCouponsAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $StudentModel = new \Model\Smc\StudentModel($request);
        $res = $StudentModel->invalidCoupons($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '失效成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $StudentModel->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }


    function apppropermisApi()
    {

        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $StudentModel = new \Model\Smc\StudentModel($request);
        $res = $StudentModel->apppropermis($request);
        $field = array();

        $k = 0;
        $field[$k]["fieldstring"] = "apppropermis_name";
        $field[$k]["fieldname"] = "产品名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "apppropermislog_createtime";
        $field[$k]["fieldname"] = "授权日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "apppropermis_authcode";
        $field[$k]["fieldname"] = "产品授权码";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "course_cnname";
        $field[$k]["fieldname"] = "课程别名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "course_branch";
        $field[$k]["fieldname"] = "课程别编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "staffer_cnname";
        $field[$k]["fieldname"] = "开通人";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "apppropermislog_endday";
        $field[$k]["fieldname"] = "截止日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "apppropermislog_isenabled";
        $field[$k]["fieldname"] = "是否禁用";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;


        $result = array();
        $result["field"] = $field;
        if ($res['allnum']) {
            $result["allnum"] = $res['allnum'];
        } else {
            $result["allnum"] = 0;
        }

        if ($res) {
            $result["fieldcustom"] = 0;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => '学员暂无授权产品信息', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    //单个学员的文件列表
    function studentFileApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $StudentModel = new \Model\Smc\StudentModel($request);
        $res = $StudentModel->studentFile($request);
        $field = array();

        $field[0]["fieldstring"] = "file_id";
        $field[0]["fieldname"] = "文件ID";
        $field[0]["show"] = 0;
        $field[0]["custom"] = 0;

        $field[1]["fieldstring"] = "file_name";
        $field[1]["fieldname"] = "文件名";
        $field[1]["show"] = 1;
        $field[1]["custom"] = 0;

        $field[2]["fieldstring"] = "file_format";
        $field[2]["fieldname"] = "格式";
        $field[2]["show"] = 1;
        $field[2]["custom"] = 0;

        $field[3]["fieldstring"] = "file_size";
        $field[3]["fieldname"] = "大小";
        $field[3]["show"] = 1;
        $field[3]["custom"] = 0;

        $field[4]["fieldstring"] = "staffer_cnname";
        $field[4]["fieldname"] = "上传人";
        $field[4]["show"] = 1;
        $field[4]["custom"] = 0;

        $field[5]["fieldstring"] = "file_createtime";
        $field[5]["fieldname"] = "上传日期";
        $field[5]["show"] = 1;
        $field[5]["custom"] = 0;

        $field[6]["fieldstring"] = "file_fileurl";
        $field[6]["fieldname"] = "fileurl";
        $field[6]["show"] = 0;
        $field[6]["custom"] = 0;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 0;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => '学员暂无文件信息', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //新增学员的文件
    function studentAddFileAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $StudentModel = new \Model\Smc\StudentModel($request);
        $res = $StudentModel->studentAddFile($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '新建文档成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '新建文档失败', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //删除学员文件
    function studentDelFileAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $StudentModel = new \Model\Smc\StudentModel($request);
        $res = $StudentModel->studentDelFile($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $this->addSmcWorkLog($request['company_id'], $request['school_id'], $request['staffer_id'], "班务管理->学员管理->学员详情", '删除文档', dataEncode($request));
            $res = array('error' => 0, 'errortip' => '删除文档成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '删除文档失败', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function stuExchangeLogApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $StudentModel = new \Model\Smc\StudentModel($request);
        $res = $StudentModel->stuExchangeLog($request);
        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "integrallog_id";
        $field[$k]["fieldname"] = "记录ID";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "integraltype_class_name";
        $field[$k]["fieldname"] = "所属类别";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "integrallog_rule";
        $field[$k]["fieldname"] = "积分类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "integrallog_playname";
        $field[$k]["fieldname"] = "交易名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

//        $field[$k]["fieldstring"] = "class_cnname";
//        $field[$k]["fieldname"] = "班级名称";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 0;
//        $k++;

        $field[$k]["fieldstring"] = "integrallog_fromamount";
        $field[$k]["fieldname"] = "原始积分";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "integrallog_playamount";
        $field[$k]["fieldname"] = "交易积分";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "integrallog_finalamount";
        $field[$k]["fieldname"] = "完成积分";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "integrallog_reason";
        $field[$k]["fieldname"] = "交易原因";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "staffer_cnname";
        $field[$k]["fieldname"] = "经办人";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "integrallog_time";
        $field[$k]["fieldname"] = "操作时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 0;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => $StudentModel->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function getIntegraltypeApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $StudentModel = new \Model\Smc\StudentModel($request);
        $res = $StudentModel->getIntegraltype($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $StudentModel->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function giveIntegralAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $StudentModel = new \Model\Smc\StudentModel($request);
        $res = $StudentModel->giveIntegral($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '赠送成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $StudentModel->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function deductionIntegralAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $StudentModel = new \Model\Smc\StudentModel($request);
        $res = $StudentModel->deductionIntegral($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '扣除成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $StudentModel->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //学员兑换列表
    function stuExchangeListApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $StudentModel = new \Model\Smc\StudentModel($request);
        $res = $StudentModel->stuExchangeList($request);
        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "integralgoods_id";
        $field[$k]["fieldname"] = "兑换ID";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "goods_cnname";
        $field[$k]["fieldname"] = "兑换商品名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "integralgoods_score";
        $field[$k]["fieldname"] = "兑换积分";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "integralgoods_number";
        $field[$k]["fieldname"] = "兑换数量";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "integralgoods_all";
        $field[$k]["fieldname"] = "消耗积分";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "integralgoods_createtime";
        $field[$k]["fieldname"] = "兑换时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

//		$field[$k]["fieldstring"] = "parenter_cnname";
//		$field[$k]["fieldname"] = "兑换人";
//		$field[$k]["show"] = 1;
//		$field[$k]["custom"] = 0;
//		$k++;

        $field[$k]["fieldstring"] = "integralgoods_isreceive_name";
        $field[$k]["fieldname"] = "是否领取";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "integralgoods_receivetime";
        $field[$k]["fieldname"] = "领取时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 0;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => $StudentModel->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function cancelExchangeAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $StudentModel = new \Model\Smc\StudentModel($request);
        $res = $StudentModel->cancelExchange($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '取消成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $StudentModel->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //单个学员的沟通列表
    function studentTrackApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $StudentModel = new \Model\Smc\StudentModel($request);
        $res = $StudentModel->studentTrack($request);
        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "track_id";
        $field[$k]["fieldname"] = "沟通ID";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "coursetype_id";
        $field[$k]["fieldname"] = "班组id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "coursecat_id";
        $field[$k]["fieldname"] = "班种id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "coursetype_name";
        $field[$k]["fieldname"] = "沟通班组";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "coursecat_name";
        $field[$k]["fieldname"] = "沟通班种";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "object_name";
        $field[$k]["fieldname"] = "沟通对象";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "track_from";
        $field[$k]["fieldname"] = "是否可编辑0是1否";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "track_day";
        $field[$k]["fieldname"] = "沟通日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "tracktype_id";
        $field[$k]["fieldname"] = "沟通类型id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "track_classname";
        $field[$k]["fieldname"] = "沟通类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "track_note";
        $field[$k]["fieldname"] = "沟通内容";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = 'track_callid';
        $field[$k]["fieldname"] = "外呼录音";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $field[$k]["huijieAudioVisible"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "track_linktype";
        $field[$k]["fieldname"] = "沟通方式id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "commode_name";
        $field[$k]["fieldname"] = "沟通方式";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "trackresult_id";
        $field[$k]["fieldname"] = "沟通结果id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "trackresult_name";
        $field[$k]["fieldname"] = "沟通结果";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "staffer_cnname";
        $field[$k]["fieldname"] = "教师姓名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "staffer_postbe";
        $field[$k]["fieldname"] = "教师职务";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "track_createtime";
        $field[$k]["fieldname"] = "创建时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 0;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => '学员暂无沟通记录', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function studentAddTrackAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $StudentModel = new \Model\Smc\StudentModel($request);
        $res = $StudentModel->studentAddTrack($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '新增沟通成功' . $StudentModel->errortip, 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '新增沟通失败', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function studentEditTrackAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $StudentModel = new \Model\Smc\StudentModel($request);
        $res = $StudentModel->studentEditTrack($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $this->addSmcWorkLog($request['company_id'], $request['school_id'], $request['staffer_id'], "班务管理->学员管理->学员详情", '修改沟通内容', dataEncode($request));
            $res = array('error' => 0, 'errortip' => '编辑沟通成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '编辑沟通失败', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function studentDelTrackAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $StudentModel = new \Model\Smc\StudentModel($request);
        $res = $StudentModel->studentDelTrack($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $this->addSmcWorkLog($request['company_id'], $request['school_id'], $request['staffer_id'], "班务管理->学员管理->学员详情", '删除沟通内容', dataEncode($request));
            $res = array('error' => 0, 'errortip' => '删除沟通成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '删除沟通成功', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function stuConsumeListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $StudentModel = new \Model\Smc\StudentModel($request);
        $res = $StudentModel->stuConsumeList($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "student_id";
        $field[$k]["fieldname"] = "ID";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["fieldname"] = "学员中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_enname";
        $field[$k]["fieldname"] = "学员英文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_branch";
        $field[$k]["fieldname"] = "学员编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_cnname";
        $field[$k]["fieldname"] = "班级中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_enname";
        $field[$k]["fieldname"] = "班级别名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_branch";
        $field[$k]["fieldname"] = "班级编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "course_cnname";
        $field[$k]["fieldname"] = "课程别名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "course_branch";
        $field[$k]["fieldname"] = "课程别编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursebalance_time";
        $field[$k]["fieldname"] = "剩余课次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursebalance_figure";
        $field[$k]["fieldname"] = "剩余余额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursebalance_unitexpend";
        $field[$k]["fieldname"] = "耗课单价";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "hour_nochecknum";
        $field[$k]["fieldname"] = "班级剩余课次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "arrearagetimes";
        $field[$k]["fieldname"] = "预计欠费课次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result['field'] = $field;
        $result['list'] = $res['list'];

        if (!$res['allnum']) {
            $res['allnum'] = 0;
        }

        $warningtips = '预警值：';
        $schoolOne = $this->DataControl->getFieldOne("smc_school", "school_coursewarning,school_classtimeswarning", "school_id='{$request['school_id']}'");
        if ($schoolOne && $schoolOne['school_coursewarning'] == 1) {
            $warningtips .= '1.按集团设置的课程剩余课次进行预警 ';
        }

        $arrearsalertOne = $this->DataControl->getFieldOne("smc_school_arrears_alert", "alert_time,alert_status", "school_id='{$request['school_id']}'");
        if ($arrearsalertOne && $arrearsalertOne['alert_status'] == 1) {
            $warningtips .= '. 2.学员剩余课次≤' . $arrearsalertOne['alert_time'];
        }

        if ($schoolOne && $schoolOne['school_classtimeswarning'] == 1) {
            $warningtips .= '. 3.学员剩余课次小于班级剩余课次 ';
        }

        $warningtips .= '. 可前往系统设置-首页设置修改预警维度 ';
        $result['tips'] = $this->LgStringSwitch($warningtips);

        if ($res['list']) {
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $result, 'allnum' => $res['allnum']);
        } else {
            $res = array('error' => 1, 'errortip' => '暂无学员耗课预警', 'result' => $result, 'allnum' => $res['allnum']);
        }
        ajax_return($res, $request['language_type']);
    }

    //个人 集团产品权限
    function stuApppropermisApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $StudentModel = new \Model\Smc\StudentModel($request);
        $res = $StudentModel->stuApppropermis($request);

        $fieldstring = array('apppropermislog_id', 'apppropermis_name', 'apppropermis_code', 'apppropermislog_createtime', 'staffer_cnname', 'apppropermislog_endday');
        $fieldname = array('ID', '产品名称', '产品权限代码', '授权时间', '开通人', '截止日期');
        $fieldcustom = array('0', "1", "0", "1", "1", "1");
        $fieldshow = array('0', "1", "0", "1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["fieldname"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result['field'] = $field;
        $result['list'] = $res['list'];

        ajax_return($result, $request['language_type']);

//		 smc_code_apppropermis

    }

    function ApppropermisAction()
    {
        $request = Input('post.', '', 'trim,addslashes');

        if (!$request['class_id']) {
            $res = array('error' => 1, 'errortip' => '请选择班级', 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        if (!$request['apppropermis_code']) {
            $res = array('error' => 1, 'errortip' => '请选择授权产品', 'result' => array());
            ajax_return($res, $request['language_type']);
        }


        $this->ThisVerify($request);
        $StudentModel = new \Model\Smc\StudentModel($request);

        $bool = $StudentModel->addPppropermis($request);
        if ($bool) {
            $res = array('error' => 0, 'errortip' => '新增成功', 'result' => array());

        } else {
            $res = array('error' => 1, 'errortip' => $StudentModel->errortip, 'result' => array());
        }

        ajax_return($res, $request['language_type']);

    }


    function stuReduceCourseAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $StudentModel = new \Model\Smc\StudentModel($request);
        $res = $StudentModel->stuReduceCourse($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '已生成减少课程订单', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $StudentModel->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function stuReduceCourseBalanceAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $StudentModel = new \Model\Smc\StudentModel($request);
        $res = $StudentModel->stuReduceCourseBalance($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => $StudentModel->errortip, 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $StudentModel->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function giveStuCourseAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\StudentModel($request);
        $res = $Model->giveStuCourse($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '课程赠送申请成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function refundStuGoodsAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\StudentModel($request);
        $res = $Model->refundStuGoods($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '退费成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function getStuTimesPriceApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\StudentModel($request);
        $res = $Model->getStuTimesPrice($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function refundStuTimesAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\StudentModel($request);
        $res = $Model->refundStuTimes($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '退费成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function subscribeStuTimesAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\StudentModel($request);
        $res = $Model->subscribeStuTimes($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '认缴成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function updateOldStuAction()
    {
        $request = Input('get.', '', 'strip_tags,trim,addslashes');
        $Model = new \Model\SchoolOldModel('8888');
        $res = $Model->updateOldStu($request['school_branch'], $request['student_branch']);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '同步成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function ImportStudentsAction()
    {
        $request = Input('get.', '', 'strip_tags,trim,addslashes');
        $Model = new \Model\SchoolOldModel('8888');

        $res = $Model->ImportStudents();

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '同步成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    function updateLossLogAction()
    {
        $request = Input('get.', '', 'strip_tags,trim');
        $Model = new \Model\SchoolOldModel('8888');

        $res = $Model->updateLossLog($request['school_branch'], $request['student_branch']);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '同步成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    //同业学员申请列表 --  学校端 -- 97
    function getGuildstuApplyView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $StudentModel = new \Model\Smc\StudentModel($request);
        $dataList = $StudentModel->getGuildstuApply($request);

        $field = array();
        $key = 0;
        $field[$key]["fieldstring"] = "student_cnname";
        $field[$key]["fieldname"] = "学员中文名";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldstring"] = "student_enname";
        $field[$key]["fieldname"] = "学员英文名";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldstring"] = "student_branch";
        $field[$key]["fieldname"] = "学员编号";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldstring"] = "guildstutype_name";
        $field[$key]["fieldname"] = "同业学员类型";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldstring"] = "apply_contract";
        $field[$key]["fieldname"] = "培训协议编号";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldstring"] = "apply_payprice";
        $field[$key]["fieldname"] = "缴费金额";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldstring"] = "apply_paytime";
        $field[$key]["fieldname"] = "合同缴费时间";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldstring"] = "apply_status_name";
        $field[$key]["fieldname"] = "审核状态";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldstring"] = "apply_statusreason";
        $field[$key]["fieldname"] = "审核备注";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldstring"] = "apply_addtime";
        $field[$key]["fieldname"] = "申请时间";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        if ($dataList) {
            $result["field"] = $field;
            $result["list"] = $dataList['datalist'];
            if ($result["list"]) {
                $res = array('error' => '0', 'errortip' => '获取信息成功', 'allnum' => $dataList['count'], 'result' => $result);
            } else {
                $result["list"] = array();
                $res = array('error' => '1', 'errortip' => '暂无信息', 'allnum' => 0, 'result' => $result);
            }
        } else {
            $result["list"] = array();
            $res = array('error' => '1', 'errortip' => '暂无信息', 'allnum' => 0, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    //获取同业学员类型
    function getGuildstutypeApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        //model
        $Model = new \Model\Smc\StudentModel($request);
        $dataList = $Model->getGuildstutypeApi($request);

        $result = array();
        if ($dataList) {
            $result["list"] = $dataList;
            $res = array('error' => '0', 'errortip' => '同业类型获取成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => '1', 'errortip' => '同业类型获取失败', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }


    //获取同业学员信息
    function getGuildstuListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $StudentModel = new \Model\Smc\StudentModel($request);
        $dataList = $StudentModel->getGuildstuList($request);

        $field = array();
        $key = 0;
        $field[$key]["fieldstring"] = "student_cnname";
        $field[$key]["fieldname"] = "学员中文名";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldstring"] = "student_enname";
        $field[$key]["fieldname"] = "学员英文名";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldstring"] = "student_branch";
        $field[$key]["fieldname"] = "学员编号";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldstring"] = "student_sex";
        $field[$key]["fieldname"] = "性别";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldstring"] = "family_cnname";
        $field[$key]["fieldname"] = "主要联系人";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldstring"] = "family_mobile";
        $field[$key]["fieldname"] = "手机号";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        if ($dataList) {
            $result["field"] = $field;
            $result["list"] = $dataList['list'];
            if ($result["list"]) {
                $res = array('error' => '0', 'errortip' => '获取招生目标信息成功', 'allnum' => $dataList['count'], 'result' => $result);
            } else {
                $result["list"] = array();
                $res = array('error' => '1', 'errortip' => '暂无招生目标信息', 'allnum' => 0, 'result' => $result);
            }
        } else {
            $result["list"] = array();
            $res = array('error' => '1', 'errortip' => '暂无招生目标信息', 'allnum' => 0, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //提交同业学员资料
    function addGuildstuInfoAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        if (!isset($request['apply_imgslist']) || $request['apply_imgslist'] == '') {
            $res = array('error' => '1', 'errortip' => '附件不能为空！', 'result' => array());
            ajax_return($res, $request['language_type']);
        }

        //model
        $StudentModel = new \Model\Smc\StudentModel($request);
        $dataList = $StudentModel->addGuildstuInfo($request);

        if ($dataList) {
            $this->addSmcWorkLog($request['company_id'], $request['school_id'], $request['staffer_id'], "同业接纳学员", '新增同业接纳学员信息', dataEncode($request));
            $res = array('error' => '0', 'errortip' => $StudentModel->errortip);
        } else {
            $res = array('error' => '1', 'errortip' => $StudentModel->errortip);
        }
        ajax_return($res, $request['language_type']);

    }


    //提交同业学员资料
    function updateGuildstuImgsAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        if (!isset($request['apply_imgslist']) || $request['apply_imgslist'] == '') {
            $res = array('error' => '1', 'errortip' => '附件不能为空！', 'result' => array());
            ajax_return($res, $request['language_type']);
        }

        //model
        $StudentModel = new \Model\Smc\StudentModel($request);
        $dataList = $StudentModel->updateGuildstuImgs($request);

        if ($dataList) {
            $this->addSmcWorkLog($request['company_id'], $request['school_id'], $request['staffer_id'], "同业接纳学员", '修改同业接纳学员附件信息', dataEncode($request));
            $res = array('error' => '0', 'errortip' => $StudentModel->errortip);
        } else {
            $res = array('error' => '1', 'errortip' => $StudentModel->errortip);
        }
        ajax_return($res, $request['language_type']);

    }

    //获取一条学员申请基本信息
    function getGuildstuinfoApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        //model
        $Model = new \Model\Smc\StudentModel();
        $dataList = $Model->getGuildstuinfoApi($request);

        $result = array();
        if ($dataList) {
            $result["list"] = $dataList;
            $res = array('error' => '0', 'errortip' => '信息获取成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => '1', 'errortip' => '信息获取失败', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //获取一条学员申请信息
    function getGuildstuOneApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        //model
        $Model = new \Model\Smc\StudentModel($request);
        $dataList = $Model->getGuildstuOneApi($request);


        $field = array();
        $key = 0;
        $field[$key]["fieldstring"] = "apply_contract";
        $field[$key]["fieldname"] = "协议编号";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldstring"] = "apply_payprice";
        $field[$key]["fieldname"] = "同业学员缴费金额";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldstring"] = "apply_paytime";
        $field[$key]["fieldname"] = "同业学员合同缴费时间(起)";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldstring"] = "apply_payendtime";
        $field[$key]["fieldname"] = "同业学员合同缴费时间(止)";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldstring"] = "apply_classtimes";
        $field[$key]["fieldname"] = "同业学员剩余课时";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldstring"] = "apply_toclasstimes";
        $field[$key]["fieldname"] = "转吉的堡课次";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldstring"] = "guildstutype_name";
        $field[$key]["fieldname"] = "同业学员类型";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldstring"] = "apply_status_name";
        $field[$key]["fieldname"] = "审核状态";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldstring"] = "apply_remarks";
        $field[$key]["fieldname"] = "备注";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "statisticshour";
        $field[$key]["fieldstring"] = "当前剩余课时";
        $field[$key]["show"] = 0;
        $field[$key]["custom"] = 0;
        $key++;

        $result = array();
        if ($dataList) {
            $result["field"] = $field;
            $result["list"] = $dataList;
            $res = array('error' => '0', 'errortip' => '信息获取成功', 'result' => $result);
        } else {
            $result["field"] = $field;
            $result["list"] = array();
            $res = array('error' => '1', 'errortip' => '信息获取失败', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //获取单个就读课程数据
    function getStuWastingClassApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        //model
        $Model = new \Model\Smc\StudentModel();
        $dataList = $Model->getStuWastingClassApi($request);


        $field = array();
        $key = 0;
        $field[$key]["fieldstring"] = "student_cnname";
        $field[$key]["fieldname"] = "学员中文名";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldstring"] = "student_enname";
        $field[$key]["fieldname"] = "学员英文名";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldstring"] = "student_branch";
        $field[$key]["fieldname"] = "学员编号";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldstring"] = "study_beginday";
        $field[$key]["fieldname"] = "学习开始时间";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldstring"] = "study_endday";
        $field[$key]["fieldname"] = "学习结束时间";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldstring"] = "class_cnname";
        $field[$key]["fieldname"] = "班级名称";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldstring"] = "class_enname";
        $field[$key]["fieldname"] = "班级别名";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldstring"] = "class_branch";
        $field[$key]["fieldname"] = "班级编号";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldstring"] = "course_cnname";
        $field[$key]["fieldname"] = "课程名称";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldstring"] = "course_branch";
        $field[$key]["fieldname"] = "课程编号";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldstring"] = "hour_name";
        $field[$key]["fieldname"] = "第一次入课次";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldstring"] = "successhour";
        $field[$key]["fieldname"] = "班级以上课次";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldstring"] = "allhour";
        $field[$key]["fieldname"] = "班级全部课次";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldstring"] = "studyhour";
        $field[$key]["fieldname"] = "已经消耗课次";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $result = array();
        if ($dataList) {
            $result["field"] = $field;
            $result["list"] = $dataList;
            $res = array('error' => '0', 'errortip' => '信息获取成功', 'result' => $result);
        } else {
            $result["field"] = $field;
            $result["list"] = array();
            $res = array('error' => '1', 'errortip' => '信息获取失败', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    function activateCouponsAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);

        $StudentModel = new \Model\Smc\StudentModel($request);
        $res = $StudentModel->activateCoupons($request);
        $result = array();
        if ($res) {
            $result["list"] = $res;
            $this->addSmcWorkLog($request['company_id'], $request['school_id'], $request['staffer_id'], "班务管理->学员管理->学员详情", '激活活动优惠券', dataEncode($request));
            $res = array('error' => 0, 'errortip' => '激活成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $StudentModel->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function forwardStuAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);

        $StudentModel = new \Model\Smc\StudentModel($request);
        $res = $StudentModel->forwardStu($request);
        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '结转成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $StudentModel->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function getStuSchoolApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);

        $StudentModel = new \Model\Smc\StudentModel($request);
        $res = $StudentModel->getStuSchool($request);
        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '无学校数据', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //学校/单个学生的请假记录
    function studentAbsenceApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);

        $StudentModel = new \Model\Smc\StudentModel($request);
        $res = $StudentModel->studentAbsence($request);

        $field = array();
        $key = 0;
        if (!isset($request['student_id']) || $request['student_id'] == '') {
            $field[$key]["fieldstring"] = "student_cnname";
            $field[$key]["fieldname"] = "学员中文名";
            $field[$key]["show"] = 1;
            $field[$key]["custom"] = 1;
            $key++;

            $field[$key]["fieldstring"] = "student_enname";
            $field[$key]["fieldname"] = "学员英文名";
            $field[$key]["show"] = 1;
            $field[$key]["custom"] = 1;
            $key++;

            $field[$key]["fieldstring"] = "student_branch";
            $field[$key]["fieldname"] = "学生编号";
            $field[$key]["show"] = 1;
            $field[$key]["custom"] = 1;
            $key++;
        }
        $field[$key]["fieldstring"] = "absence_id";
        $field[$key]["fieldname"] = "请假记录ID";
        $field[$key]["show"] = 0;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldstring"] = "absence_time";
        $field[$key]["fieldname"] = "请假日期";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 1;
        $key++;

        $field[$key]["fieldstring"] = "hour_allnum";
        $field[$key]["fieldname"] = "请假课次";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 1;
        $field[$key]["ismethod"] = 1;
        $key++;

        $field[$key]["fieldstring"] = "absence_type_name";
        $field[$key]["fieldname"] = "请假类型";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 1;
        $key++;

        $field[$key]["fieldstring"] = "absence_reasonnote";
        $field[$key]["fieldname"] = "备注";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 1;
        $key++;

        $field[$key]["fieldstring"] = "absence_status_name";
        $field[$key]["fieldname"] = "请假状态";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 1;
        $key++;

        $field[$key]["fieldstring"] = "applicant";
        $field[$key]["fieldname"] = "申请人";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => '学员暂无请假记录', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //班级学生的请假记录
    function getClassStudentAbsenceApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);

        $StudentModel = new \Model\Smc\StudentModel($request);
        $res = $StudentModel->getClassStudentAbsence($request);

        $field = array();
        $key = 0;
        $field[$key]["fieldstring"] = "student_cnname";
        $field[$key]["fieldname"] = "学员中文名";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 1;
        $key++;

        $field[$key]["fieldstring"] = "student_enname";
        $field[$key]["fieldname"] = "学员英文名";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 1;
        $key++;

        $field[$key]["fieldstring"] = "student_branch";
        $field[$key]["fieldname"] = "学生编号";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 1;
        $key++;

        $field[$key]["fieldstring"] = "hour_time";
        $field[$key]["fieldname"] = "上课时间";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 1;
        $key++;

        $field[$key]["fieldstring"] = "hour_way_name";
        $field[$key]["fieldname"] = "上课方式";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 1;
        $key++;

        $field[$key]["fieldstring"] = "staffer_cnname";
        $field[$key]["fieldname"] = "主教老师";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 1;
        $key++;

        $field[$key]["fieldstring"] = "fu_staffer_cnname";
        $field[$key]["fieldname"] = "助教老师";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 1;
        $key++;

        $field[$key]["fieldstring"] = "hour_isfree_name";
        $field[$key]["fieldname"] = "是否计费";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 1;
        $key++;

        $field[$key]["fieldstring"] = "absence_type_name";
        $field[$key]["fieldname"] = "请假类型";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 1;
        $key++;

        $field[$key]["fieldstring"] = "hour_status_name";
        $field[$key]["fieldname"] = "是否有效";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 1;
        $key++;

        $field[$key]["fieldstring"] = "absence_status_name";
        $field[$key]["fieldname"] = "请假状态";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 1;
        $key++;

        $field[$key]["fieldstring"] = "absence_reasonnote";
        $field[$key]["fieldname"] = "备注";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => '学员暂无请假记录', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //请假课次
    function getAbsenceHourApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);

        $StudentModel = new \Model\Smc\StudentModel($request);
        $res = $StudentModel->getAbsenceHour($request);

        $field = array();
        $key = 0;
        $field[$key]["fieldstring"] = "class_cnname";
        $field[$key]["fieldname"] = "班级名称";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 1;
        $key++;

        $field[$key]["fieldstring"] = "class_enname";
        $field[$key]["fieldname"] = "班级别名";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 1;
        $key++;

        $field[$key]["fieldstring"] = "class_branch";
        $field[$key]["fieldname"] = "班级编号";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 1;
        $key++;

        $field[$key]["fieldstring"] = "course_cnname";
        $field[$key]["fieldname"] = "课程别名称";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 1;
        $key++;

        $field[$key]["fieldstring"] = "course_branch";
        $field[$key]["fieldname"] = "课程别编号";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 1;
        $key++;

        $field[$key]["fieldstring"] = "hour_time";
        $field[$key]["fieldname"] = "上课时间";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 1;
        $key++;

        $field[$key]["fieldstring"] = "hour_way_name";
        $field[$key]["fieldname"] = "上课方式";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 1;
        $key++;

        $field[$key]["fieldstring"] = "staffer_cnname";
        $field[$key]["fieldname"] = "主教老师";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 1;
        $key++;

        $field[$key]["fieldstring"] = "hour_isfree_name";
        $field[$key]["fieldname"] = "是否计费";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 1;
        $key++;

        $field[$key]["fieldstring"] = "hour_status_name";
        $field[$key]["fieldname"] = "是否有效";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        $result["absence"] = $res['absence_hournum'];
        if ($res) {
            $result["list"] = $res['list'];
            $result["class"] = $res['class_list'];
            $result["course"] = $res['course_list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => '暂无请假课次', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //获取课次信息
    function getClassHourApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);

        $StudentModel = new \Model\Smc\StudentModel($request);
        $res = $StudentModel->getClassHour($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "class_cnname";
        $field[$k]["fieldname"] = "班级名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_enname";
        $field[$k]["fieldname"] = "班级别名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_branch";
        $field[$k]["fieldname"] = "班级编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "course_cnname";
        $field[$k]["fieldname"] = "课程别名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "course_branch";
        $field[$k]["fieldname"] = "课程别编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "hour_time";
        $field[$k]["fieldname"] = "上课时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["is_status"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "hour_way_name";
        $field[$k]["fieldname"] = "上课方式";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "main_staffer_cnname";
        $field[$k]["fieldname"] = "主教老师";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "hour_isfree_name";
        $field[$k]["fieldname"] = "是否计费";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        if ($res) {
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '该请假时间段内暂无已排课时信息', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //添加请假记录
    function addAbsenceAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);

        $StudentModel = new \Model\Smc\StudentModel($request);
        $res = $StudentModel->addAbsence($request);
        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '添加成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $StudentModel->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //审批通过/拒绝/撤销
    function approvedAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);

        $StudentModel = new \Model\Smc\StudentModel($request);
        $res = $StudentModel->approved($request);
        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '审批通过', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $StudentModel->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }


    //查看请假记录
    function getApprovedDetailApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);

        $StudentModel = new \Model\Smc\StudentModel($request);
        $res = $StudentModel->getApprovedDetail($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "class_cnname";
        $field[$k]["fieldname"] = "班级名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_enname";
        $field[$k]["fieldname"] = "班级别名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_branch";
        $field[$k]["fieldname"] = "班级编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "course_cnname";
        $field[$k]["fieldname"] = "课程别名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "course_branch";
        $field[$k]["fieldname"] = "课程别编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "hour_time";
        $field[$k]["fieldname"] = "上课时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "hour_way_name";
        $field[$k]["fieldname"] = "上课方式";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "staffer_cnname";
        $field[$k]["fieldname"] = "主教老师";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "hour_isfree_name";
        $field[$k]["fieldname"] = "是否计费";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "hour_status_name";
        $field[$k]["fieldname"] = "是否有效";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '该请假时间段内暂无已排课时信息', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //获取整个学校的学员
    function getSchoolStudentListApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);

        $datawhere = "ss.company_id = '{$request['company_id']}' and ss.school_id = '{$request['school_id']}' and ss.study_isreading = '1' and s.student_isdel = '0'";
        if (isset($request['class_id']) && $request['class_id'] !== '') {
            $datawhere .= " and ss.class_id = '{$request['class_id']}'";
        }

        $sql = "SELECT
					s.student_id,s.student_cnname,s.student_enname,s.student_sex
				FROM
					smc_student_enrolled as se
				LEFT JOIN
					smc_student as s ON s.student_id = se.student_id
				LEFT JOIN
					smc_student_study as ss ON ss.student_id = s.student_id
				WHERE
					{$datawhere}
				GROUP BY
					s.student_id";

        $studentList = $this->DataControl->selectClear($sql);
        if (!$studentList) {
            $studentList = array();
        }

        if ($studentList) {
            $result['list'] = $studentList;
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
        } else {
            $res = array('error' => 1, 'errortip' => '暂无数据', 'result' => array());
        }
        ajax_return($res, $request['language_type']);
    }

    //无效化 课次的请假记录
    function invalidAbsenceHourAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $StudentModel = new \Model\Smc\StudentModel($request);
        $StudentModel->invalidAbsenceHourAction($request);
        $res = array('error' => $StudentModel->error, 'errortip' => $StudentModel->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    function getAppApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Smc\StudentModel($request);

        $result = $this->Model->getAppApi($request);
        ajax_return($result, $request['language_type']);
    }

    function getClassApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Smc\StudentModel($request);

        $result = $this->Model->getClassApi($request);
        ajax_return($result, $request['language_type']);
    }

    function addAppPowerAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Smc\StudentModel($request);

        $result = $this->Model->addAppPowerAction($request);
        ajax_return($result, $request['language_type']);
    }

    //下载导入模版
    function getImportIntegralApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Smc\StudentModel($request);

        $result = $this->Model->getImportIntegralApi($request);
        ajax_return($result, $request['language_type']);
    }

    //上传
    function ImportImportIntegralView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $url = $request['url'];

        $ys_array = array('学员中文名' => 'student_cnname', '学员编号' => 'student_branch', '班级编号' => 'class_branch', '赠送规则' => 'rule', '交易名称' => 'playname', '赠送积分' => 'score', '赠送原因' => 'reason', '赠送日期' => 'date');
        $options = array(
            "ssl" => array(
                "verify_peer" => false,
                "verify_peer_name" => false,
            ),
        );
        file_put_contents('analysis.xls', file_get_contents($url, false, stream_context_create($options)));
        $sqlarray = execl_to_array("analysis.xls", $ys_array);
        array_shift($sqlarray);

        $fieldstring = array('student_cnname', 'student_branch', 'class_branch', 'rule', 'playname', 'score', 'reason', 'date');
        $fieldname = array('学员中文名', '学员编号', '班级编号', '赠送规则', '交易名称', '赠送积分', '赠送原因', '赠送日期');
        $fieldcustom = array("1", "1", "1", "1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1", "1", "1", "1");


        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldname[$i]);
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['list'] = $sqlarray;

        $res = array('error' => '0', 'errortip' => '导入成功', 'result' => $result);
        ajax_return($res, $request['language_type']);

    }


    //解析
    function NextStepIntegralView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $List = json_decode(stripslashes($request['list']), true);
        $suc = 0;
        $fal = 0;
        $falarray = array();

        do {
            $couponspid_get = $this->createOrderPid('CW');
        } while ($this->DataControl->getFieldOne("smc_integral_fal", "fal_id", "fal_pid='{$couponspid_get}'"));


        foreach ($List as $item) {
            if ($item['date'] == '') {
                $item['date'] = date('Y-m-d');
            }

            $student_id = $this->DataControl->getFieldOne("smc_student", "student_id", "student_branch = '{$item['student_branch']}' and student_cnname = '{$item['student_cnname']}' and company_id = '{$request['company_id']}'");
            $isset = $this->DataControl->getFieldOne("smc_student_enrolled", "school_id", "student_id = '{$student_id['student_id']}' and school_id = '{$request['school_id']}'");
            $rule = $this->DataControl->getFieldOne("smc_code_integralrule", "integralrule_id", "integralrule_name = '{$item['rule']}' and company_id = '{$request['company_id']}' and integralrule_status=1 ");
            $class_id = $this->DataControl->getFieldOne("smc_class", "class_id,course_id", "class_branch = '{$item['class_branch']}' and school_id = '{$request['school_id']}'");
            $isreading = $this->DataControl->getFieldOne("smc_student_study", "study_id", "student_id = '{$student_id['student_id']}' and class_id = '{$class_id['class_id']}' and study_isreading = '1' and school_id = '{$request['school_id']}'");

            $datas = array();
            $datas['school_id'] = $request['school_id'];
            $datas['student_id'] = $student_id['student_id'];
            $datas['playname'] = $item['playname'];
            $datas['rule'] = $item['rule'];
            $datas['date'] = $item['date'];
            $datas['fal_reason'] = $item['fal_reason'];
            $datas['reason'] = $item['reason'];
            $datas['class_branch'] = $item['class_branch'];
            $datas['score'] = $item['score'];
            $datas['fal_pid'] = $couponspid_get;
            $datas['student_cnname'] = $item['student_cnname'];
            $datas['student_branch'] = $item['student_branch'];
            $datas['fal_createtime'] = time();

            if (!is_numeric($item['score'])) {
                $item['fal_reason'] = '赠送积分必须为整数';
                $falarray[] = $item;
                $fal++;
                $this->DataControl->insertData('smc_integral_fal', $datas);
                continue;
            }

            if ($item['student_cnname'] == '' || $item['student_branch'] == '' || $item['rule'] == '' || $item['playname'] == '' || $item['score'] == '' || $item['reason'] == '' || $item['date'] == '') {
                $item['fal_reason'] = '所有字段均不能为空';
                $falarray[] = $item;
                $fal++;
                $this->DataControl->insertData('smc_integral_fal', $datas);
                continue;
            }

            if (!$rule) {
                $item['fal_reason'] = '赠送规则与该集团设置的已启用规则不匹配';
                $falarray[] = $item;
                $fal++;
                $this->DataControl->insertData('smc_integral_fal', $datas);
                continue;
            }

            if (!$student_id) {
                $item['fal_reason'] = '学员中文名与学员编号填写不匹配或错误';
                $falarray[] = $item;
                $fal++;
                $this->DataControl->insertData('smc_integral_fal', $datas);
                continue;
            }

            if (!$class_id) {
                $item['fal_reason'] = '班级编号错误';
                $falarray[] = $item;
                $fal++;
                $this->DataControl->insertData('smc_integral_fal', $datas);
                continue;
            }

            if (!$isset) {
                $item['fal_reason'] = '学生不在该学校就读';
                $falarray[] = $item;
                $fal++;
                $this->DataControl->insertData('smc_integral_fal', $datas);
                continue;
            }

            if (!$isreading) {
                $item['fal_reason'] = '学生不在该课程就读';
                $falarray[] = $item;
                $fal++;
                $this->DataControl->insertData('smc_integral_fal', $datas);
                continue;
            }

            $a = $this->DataControl->getFieldOne("smc_student_integrallog", "integrallog_id", "student_id = '{$student_id['student_id']}' 
            and school_id = '{$request['school_id']}' and integrallog_reason = '{$item['reason']}' and FROM_UNIXTIME(integrallog_time,'%Y-%m-%d')='{$item['date']}' ");

            if ($a && $a['integrallog_id'] > 0) {
                $item['fal_reason'] = '同一天同一个学员赠送积分原因相同时导入失败';
                $falarray[] = $item;
                $fal++;
                $this->DataControl->insertData('smc_integral_fal', $datas);
                continue;
            }

            $integral_new = 0;
            $courseOne = $this->DataControl->getFieldOne("smc_course", "course_isintegral,course_maxintegral", "course_id='{$class_id['course_id']}'");
            $oldOne = $this->DataControl->getFieldOne("smc_student_integrallog", "sum(integrallog_playamount) as old_integral", "integrallog_playclass='+' and student_id='{$student_id['student_id']}' and course_id='{$class_id['course_id']}'");

            if ($courseOne['course_isintegral'] == '1') {
                if ($oldOne['old_integral'] >= $courseOne['course_maxintegral']) {
                    $item['fal_reason'] = '该课程总积分已达上限';
                    $falarray[] = $item;
                    $fal++;
                    $this->DataControl->insertData('smc_integral_fal', $datas);
                    continue;
                }
                if ($oldOne['old_integral'] + $item['score'] >= $courseOne['course_maxintegral']) {
                    $integral_new = $courseOne['course_maxintegral'] - $oldOne['old_integral'];
                } else {
                    $integral_new = $item['score'];
                }
            } else {
                $integral_new = $item['score'];
            }

            $BalanceModel = new \Model\Smc\BalanceModel($request);
            $BalanceModel->addStuIntegral($student_id['student_id'], $integral_new, 0, $item['rule'], $request['staffer_id'], 0, $item['playname'], $item['reason'], $item['date'], $class_id['class_id'], $class_id['course_id']);
            $suc++;
        }

        $fieldstring = array('student_cnname', 'student_branch', 'class_branch', 'rule', 'playname', 'score', 'reason', 'date', 'fal_reason');
        $fieldname = array('学员中文名', '学员编号', '班级编号', '赠送规则', '交易名称', '赠送积分', '赠送原因', '赠送日期', '错误原因');
        $fieldcustom = array("1", "1", "1", "1", "1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1", "1", "1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldname[$i]);
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fal'] = $fal;
        $result['suc'] = $suc;
        $result['field'] = $field;
        $result['falarray'] = $falarray;
        $result['time'] = $couponspid_get;

        ajax_return(array('error' => '0', 'errortip' => "导入积分成功", 'result' => $result));

    }


    //导出
    function ExportIntegralView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $sql = "select * from smc_integral_fal as f where f.fal_pid = '{$request['pid']}'";

        $falarray = $this->DataControl->selectClear($sql);

        $dateexcelarray = $falarray;

        if (!$dateexcelarray) {
            $this->error = true;
            $this->errortip = "无数据";
            return false;
        }

        $outexceldate = array();

        if ($dateexcelarray) {
            $outexceldate = array();
            foreach ($dateexcelarray as $dateexcelvar) {
                $datearray = array();

                $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                $datearray['student_branch'] = $dateexcelvar['student_branch'];
                $datearray['integraltype_name'] = $dateexcelvar['integraltype_name'];
                $datearray['playname'] = $dateexcelvar['playname'];
                $datearray['score'] = $dateexcelvar['score'];
                $datearray['reason'] = $dateexcelvar['reason'];
                $datearray['date'] = $dateexcelvar['date'];
                $datearray['fal_reason'] = $dateexcelvar['fal_reason'];
                $outexceldate[] = $datearray;

            }
        }

        $excelheader = $this->LgArraySwitch(array('学员中文名', '学员编号', '赠送积分类型', '交易名称', '赠送积分', '赠送原因', '赠送日期', '错误原因'));
        $excelfileds = array('student_cnname', 'student_branch', 'integraltype_name', 'playname', 'score', 'reason', 'date', 'fal_reason');


        query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch("积分导入错误数据.xlsx"));
        exit;

    }

    function reStuClassTypeListApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\StudentModel($request);
        $res = $Model->reStuClassTypeList($request);
        $result = array();
        if ($res) {
            $result["list"] = $res;
            $result = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
        } else {
            $result = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($result, $request['language_type']);
    }

    function reStuClassTypeAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\StudentModel($request);
        $res = $Model->reStuClassType($request);
        $result = array();
        if ($res) {
            $result["list"] = $res;
            $result = array('error' => 0, 'errortip' => '回读成功', 'result' => $result);
        } else {
            $result = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($result, $request['language_type']);
    }

    function getStuDepositPriceApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
//        $this->ThisVerify($request);
        $Model = new \Model\Smc\StudentModel($request);
        $res = $Model->getStuDepositPrice($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function refundStuDepositAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\StudentModel($request);
        $res = $Model->refundStuDeposit($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '退费成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function studentClassView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);

        $sql = "SELECT c.class_id,c.class_branch,c.class_cnname,c.course_id
                from smc_class as c
                left join smc_student_study as ss on ss.class_id=c.class_id
                WHERE ss.school_id='{$request['school_id']}' and ss.company_id='{$request['company_id']}' and ss.student_id='{$request['student_id']}' and ss.study_isreading = '1'
                group by c.class_id
                order by c.class_stdate DESC";

        $res = $this->DataControl->selectClear($sql);

        if ($res) {
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $res);
        } else {
            $res = array('error' => 1, 'errortip' => '暂无在读班级', 'result' => array());
        }
        ajax_return($res, $request['language_type']);
    }

    //学员积分明细
    function stuIntegralDetailView()
    {
        $request = Input('get.', '', 'trim,addslashes,strip_tags');
//        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\StudentModel($request);
        $dataList = $Model->StuIntegralDetail($request);

        $result = array();
        if ($dataList) {
            $result["list"] = $dataList['list'];
            $result["allnum"] = $dataList['allnum'];
            $result["integral"] = $dataList['integral'];
            $result["integralall"] = $dataList['integralall'];
            $res = array('error' => 0, 'errortip' => "获取学员积分明细成功", 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function getTagsListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\StudentModel($request);
        $res = $Model->getTagsList($request);

        $result = array();
        if (count($res['list']) !== 0) {
            $result = $res;
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
        } else {
            $res = array('error' => 1, 'errortip' => '暂无可添加的标签，请先到集团设置', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function addStudentTagsAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\StudentModel($request);
        $res = $Model->addStudentTags($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '编辑成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

}
