<?php

namespace Work\Controller\Smcapi;


class AutoHandleController extends viewTpl
{

    public function autoHandleForwardApi()
    {
        $request = Input('get.','','strip_tags,trim,addslashes');
        $Model = new \Model\Smc\AutoHandleModel();
        $res = $Model->autoHandleForward($request);

        if($res){
            $res = array('error' => 0, 'errortip' => "同步成功", 'result' => array());
        }else {
            $res = array('error' => 1, 'errortip' => "同步失败", 'result' => array());
        }
        ajax_return($res);





    }







}