<?php


namespace Work\Controller\Smcapi;


class AdjustmentController extends viewTpl
{
    public $u;
    public $t;
    public $c;

    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
    }

    function getDateHourListApi(){
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\AdjustmentModel($request);
        $res = $Model->getDateHourList($request);
        $k = 0;
        $field = array();
        $field[$k]["fieldstring"] = "time";
        $field[$k]["fieldname"] = "上课时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_cnname";
        $field[$k]["fieldname"] = "班级名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        if(isset($request['classroom_id']) && $request['classroom_id']!=''){
            $field[$k]["fieldstring"] = "staffer_cnname";
            $field[$k]["fieldname"] = "教师";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;
        }else{
            $field[$k]["fieldstring"] = "classroom_cnname";
            $field[$k]["fieldname"] = "教室";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;
        }


        $result = array();

        $result["field"] = $field;
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);

        }
        ajax_return($res, $request['language_type']);

    }


    function getHourStafferListApi(){
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\AdjustmentModel($request);
        $res = $Model->getHourStafferList($request);
        $k = 0;
        $field = array();
        $field[$k]["fieldstring"] = "staffer_id";
        $field[$k]["fieldname"] = "教师id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "staffer_cnname";
        $field[$k]["fieldname"] = "教师姓名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "post_name";
        $field[$k]["fieldname"] = "职务";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "hour_num";
        $field[$k]["fieldname"] = "本周课时数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "info_isforeign";
        $field[$k]["fieldname"] = "教师类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;


        $result = array();

        $result["field"] = $field;
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);

        }
        ajax_return($res, $request['language_type']);

    }

    function getHourRoomListApi(){
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\AdjustmentModel($request);
        $res = $Model->getHourRoomList($request);
        $k = 0;
        $field = array();
        $field[$k]["fieldstring"] = "classroom_id";
        $field[$k]["fieldname"] = "教室id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "classroom_cnname";
        $field[$k]["fieldname"] = "教室";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "hour_num";
        $field[$k]["fieldname"] = "本周课时数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "classroom_maxnums";
        $field[$k]["fieldname"] = "容量";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
//
//        $field[$k]["fieldstring"] = "classroom_status";
//        $field[$k]["fieldname"] = "状态";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//        $k++;


        $result = array();

        $result["field"] = $field;
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);

        }
        ajax_return($res, $request['language_type']);

    }

    function getConflictListApi(){

        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\AdjustmentModel($request);
        $res = $Model->getConflictList($request);
        $k = 0;
        $field = array();
        $field[$k]["fieldstring"] = "class_cnname";
        $field[$k]["fieldname"] = "班级名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_enname";
        $field[$k]["fieldname"] = "班级别名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "course_cnname";
        $field[$k]["fieldname"] = "课程别名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
//
//        $field[$k]["fieldstring"] = "z_staffer_cnname";
//        $field[$k]["fieldname"] = "主教教师";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//        $k++;
//
//        $field[$k]["fieldstring"] = "f_staffer_cnname";
//        $field[$k]["fieldname"] = "助教教师";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//        $k++;
//
//        $field[$k]["fieldstring"] = "classroom_cnname";
//        $field[$k]["fieldname"] = "教室";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//        $k++;

        $field[$k]["fieldstring"] = "time";
        $field[$k]["fieldname"] = "上课时段";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;


        $result = array();

        $result["field"] = $field;
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);

        }
        ajax_return($res, $request['language_type']);

    }

    function adjustCourseAction(){
        $request = Input('post.','','strip_tags,trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\AdjustmentModel($request);
        $res= $Model->adjustCourse($request);
        
        if($res){
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => $Model->oktip, 'result' => $result);
        }else{
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);

        }
        ajax_return($res,$request['language_type']);
    }

    function adjustCourseTestAction(){
        $fanweiModel = new \Model\Smc\FanweiModel();
        $fanweiModel->createNewAdjustApply(0);
//        $fanweiModel->createNewAdjustApply(643);
//        $fanweiModel->createNewAdjustApply(1301);
        debug($fanweiModel->errortip);
        exit;
    }

    function postponeCourseAction(){
        $request = Input('post.','','strip_tags,trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\AdjustmentModel($request);
        $res= $Model->postponeCourse($request);
        if($res){
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '顺延成功', 'result' => $result);
        }else{
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);

        }
        ajax_return($res,$request['language_type']);
    }

    function getConflictCourseListApi(){

        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\AdjustmentModel($request);
        $res = $Model->getConflictCourseList($request);
        $k = 0;
        $field = array();
        $field[$k]["fieldstring"] = "class_cnname";
        $field[$k]["fieldname"] = "班级名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_enname";
        $field[$k]["fieldname"] = "班级别名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "course_cnname";
        $field[$k]["fieldname"] = "课程别名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "time";
        $field[$k]["fieldname"] = "上课时段";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;


        $result = array();

        $result["field"] = $field;
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);

        }
        ajax_return($res, $request['language_type']);

    }




}