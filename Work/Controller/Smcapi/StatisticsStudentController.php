<?php


namespace Work\Controller\Smcapi;


class StatisticsStudentController extends viewTpl

{
    public $u;
    public $t;
    public $c;

    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
    }

    //校务学员统计 - 学员概览
    function StatisticsStudentOverView()
    {
        $request = input("get.", '', "trim,addslashes");
        $this->ThisVerify($request);
        $Model = new \Model\Smc\StatisticsStudentModel($request);
        $dataList = $Model->StatisticsStudentOver($request);
        $res = array('error' => '0', 'errortip' => '学员概览', 'result' => $dataList);
        ajax_return($res, $request['language_type']);
    }

    //校务学员统计 -  统计最近10天学员
    function StatisticsStudentTenDaysView()
    {
        $request = input("get.", '', "trim,addslashes");
        $this->ThisVerify($request);
        $Model = new \Model\Smc\StatisticsStudentModel($request);
        $dataList = $Model->StatisticsStudentTenDays($request);
        $res = array('error' => '0', 'errortip' => '近10天学员统计', 'result' => $dataList);
        ajax_return($res, $request['language_type']);
    }

    //校务学员统计 -  学员用户画像 年龄分布
    function StatisticsStudentAgesView()
    {
        $request = input("get.", '', "trim,addslashes");
        $this->ThisVerify($request);
        $Model = new \Model\Smc\StatisticsStudentModel($request);

        $dataList = $Model->StatisticsStudentAges($request);
        $res = array('error' => '0', 'errortip' => '学员年龄分布', 'result' => $dataList);
        ajax_return($res, $request['language_type']);
    }

    //校务学员统计 -  学员用户画像 男女分布
    function StatisticsStudentSexView()
    {
        $request = input("get.", '', "trim,addslashes");
        $this->ThisVerify($request);
        $Model = new \Model\Smc\StatisticsStudentModel($request);
        $dataList = $Model->StatisticsStudentSex($request);
        $res = array('error' => '0', 'errortip' => '学员性别分布', 'result' => $dataList);
        ajax_return($res, $request['language_type']);
    }


    //统计中心-考勤概况
    function CheckingApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);

        $ReportModel = new \Model\Smc\StatisticsStudentModel($request);
        $res = $ReportModel->CheckingApi($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "class_id";
        $field[$k]["fieldname"] = "班级id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "class_cnname";
        $field[$k]["fieldname"] = "班级中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_enname";
        $field[$k]["fieldname"] = "班级别名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_branch";
        $field[$k]["fieldname"] = "班级编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $field[$k]["fieldstring"] = "attendance_all_num";
        $field[$k]["fieldname"] = "应出勤人次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "attendance_num";
        $field[$k]["fieldname"] = "出勤人次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["sortable"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "attendance_not_num";
        $field[$k]["fieldname"] = "缺勤人次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["sortable"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "attendance_rate";
        $field[$k]["fieldname"] = "出勤率";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["sortable"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "attendance_not_rate";
        $field[$k]["fieldname"] = "缺勤率";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["sortable"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取考勤信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => "暂无学员考勤信息", 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }

}