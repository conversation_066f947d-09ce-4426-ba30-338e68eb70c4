<?php


namespace Work\Controller\Smcapi;


class OrderTagController extends viewTpl
{
	public $u;
	public $t;
	public $c;
	
	function __construct()
	{
		parent::__construct();
		$this->u = $this->router->getController();
		$this->t = $this->router->getUrl();
		$this->c = $this->router->getAction();
	}
	
	//标签下拉
	function orderTagApi()
	{
	    $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Smc\OrderTagModel($request);
		 $list= $Model->getChooseTagList($request);
		 $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $list);
		 ajax_return($res,$request['language_type']);
		
	}
 
	//新增标签时的标签列表
    function orderTagListApi(){
		$request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Smc\OrderTagModel($request);
		
		$list= $Model->getOrderTagList($request);
		
		$fieldstring = array('ordertag_id', 'ordertag_name', 'ordertag_isopen');
		$fieldname = array('序号', '标签名称', '开启状态');
		$fieldcustom = array('1', "1", "1" );
		$fieldshow = array('1', "1", "1");
		$field = array();
		for ($i = 0; $i < count($fieldstring); $i++) {
			$field['hour'][$i]["fieldstring"] = trim($fieldstring[$i]);
			$field['hour'][$i]["fieldname"] = trim($fieldname[$i]);
			$field['hour'][$i]["custom"] = trim($fieldcustom[$i]);
			$field['hour'][$i]["show"] = trim($fieldshow[$i]);
		}
	
		$reult['field'] = $field;
		 $reult['list'] = $list;
		 $reult['isopen_num'] = $list[0]['isopen_num'];
		 $reult['is_num'] = $list[0]['is_num'];
		 
		$res = array('error' => 0, 'errortip' => '获取成功', 'result' => $reult);
		ajax_return($res,$request['language_type']);
	}
	
	 //
	function  insertTagAction()
	{
		$request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
		$Model = new \Model\Smc\OrderTagModel($request);
		$Model->insertTag($request['ordertag_name']);
		$res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
		ajax_return($res,$request['language_type']);
		 
		 
	}
	
	function  updateTagStatusAction()
	{
		$request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
		$Model = new \Model\Smc\OrderTagModel($request);
		$Model-> updateTagStatus($request['orertag_id']);
		$res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
		ajax_return($res,$request['language_type']);
		
	}
	
//	function savaTagAction()
//	{
//		$request = Input('post.','','trim,addslashes');
        // $this->ThisVerify($request);//验证账户
//		$Model = new \Model\Smc\OrderHandleModel();
//		$bools = $Model-> updateTag($request);
//		$res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
//		ajax_return($res,$request['language_type']);
//	}
	 
	
	
}