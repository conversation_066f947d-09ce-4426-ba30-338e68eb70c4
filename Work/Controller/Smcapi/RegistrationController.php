<?php


namespace Work\Controller\Smcapi;


class RegistrationController extends viewTpl
{
    public $u;
    public $t;
    public $c;

    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
    }

    function addStudentAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\RegistrationModel($request);
        $res = $Model->addNewStudent($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '新增成功', 'result' => $result);
            $this->addSmcWorkLog($request['company_id'], $request['school_id'], $request['staffer_id'], "缴费管理->缴费中心->报名缴费", '新增学员', dataEncode($request));
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function getStudentInfoApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\RegistrationModel($request);
        $res = $Model->getStudentInfo($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }


    function getAlreadyStuApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\RegistrationModel($request);
        $res = $Model->getAlreadyStu($request);

        $field = array();

        $key = 0;
        $field[$key]["fieldstring"] = "student_id";
        $field[$key]["fieldname"] = "学员ID";
        $field[$key]["show"] = 0;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldstring"] = "student_cnname";
        $field[$key]["fieldname"] = "学员中文名";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 1;
        $key++;

        $field[$key]["fieldstring"] = "student_enname";
        $field[$key]["fieldname"] = "学员英文名";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 1;
        $key++;

        $field[$key]["fieldstring"] = "student_branch";
        $field[$key]["fieldname"] = "学员编号";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 1;
        $key++;

        $field[$key]["fieldstring"] = "student_sex";
        $field[$key]["fieldname"] = "性别";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 1;
        $key++;

        $field[$key]["fieldstring"] = "family_cnname";
        $field[$key]["fieldname"] = "主要联系人";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 1;
        $key++;

        $field[$key]["fieldstring"] = "family_mobile";
        $field[$key]["fieldname"] = "手机号";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 1;
        $key++;

        $field[$key]["fieldstring"] = "classNum";
        $field[$key]["fieldname"] = "在读课程";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 1;
        $key++;

        $field[$key]["fieldstring"] = "show_balance";
        $field[$key]["fieldname"] = "账户余额";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 1;
        $key++;

        $field[$key]["fieldstring"] = "student_forwardprice";
        $field[$key]["fieldname"] = "账户结转余额";
        $field[$key]["show"] = 0;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldstring"] = "student_img";
        $field[$key]["fieldname"] = "学员头像";
        $field[$key]["show"] = 0;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldstring"] = "family_relation";
        $field[$key]["fieldname"] = "关系";
        $field[$key]["show"] = 0;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldstring"] = "arrears";
        $field[$key]["fieldname"] = "欠费金额";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldstring"] = "student_enname";
        $field[$key]["fieldname"] = "英文名";
        $field[$key]["show"] = 0;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldstring"] = "student_idcard";
        $field[$key]["fieldname"] = "身份证";
        $field[$key]["show"] = 0;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldstring"] = "student_balance";
        $field[$key]["fieldname"] = "可退余额";
        $field[$key]["show"] = 0;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldstring"] = "student_withholdbalance";
        $field[$key]["fieldname"] = "预提余额";
        $field[$key]["show"] = 0;
        $field[$key]["custom"] = 0;
        $key++;


        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => '暂无正式学员信息', 'result' => $result);

        }
        ajax_return($res, $request['language_type']);
    }


    function getCRMStuApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\RegistrationModel($request);
        $res = $Model->getCRMStu($request);

        $field = array();

        $key = 0;
        $field[$key]["fieldstring"] = "client_id";
        $field[$key]["fieldname"] = "客户学员ID";
        $field[$key]["show"] = 0;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldstring"] = "school_shortname";
        $field[$key]["fieldname"] = "校区名称";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 1;
        $key++;

        $field[$key]["fieldstring"] = "client_cnname";
        $field[$key]["fieldname"] = "学员中文名";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 1;
        $key++;

        $field[$key]["fieldstring"] = "client_enname";
        $field[$key]["fieldname"] = "学员英文名";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 1;
        $key++;

        $field[$key]["fieldstring"] = "client_sex";
        $field[$key]["fieldname"] = "性别";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 1;
        $key++;

        $field[$key]["fieldstring"] = "parenter_cnname";
        $field[$key]["fieldname"] = "主要联系人";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 1;
        $key++;

        $field[$key]["fieldstring"] = "parenter_mobile_name";
        $field[$key]["fieldname"] = "手机号码";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 1;
        $key++;

        $field[$key]["fieldstring"] = "client_intention_level";
        $field[$key]["fieldname"] = "意向星级";
        $field[$key]["show"] = 0;
        $field[$key]["custom"] = 0;
        $field[$key]["isLevel"] = 1;
        $key++;

        $field[$key]["fieldstring"] = "course_cnname";
        $field[$key]["fieldname"] = "意向课程";
        $field[$key]["show"] = 0;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldstring"] = "client_icard";
        $field[$key]["fieldname"] = "身份证";
        $field[$key]["show"] = 0;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldstring"] = "client_img";
        $field[$key]["fieldname"] = "学员头像";
        $field[$key]["show"] = 0;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldstring"] = "family_relation";
        $field[$key]["fieldname"] = "关系";
        $field[$key]["show"] = 0;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldstring"] = "client_enname";
        $field[$key]["fieldname"] = "学员英文名称";
        $field[$key]["show"] = 0;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldstring"] = "parenter_mobile";
        $field[$key]["fieldname"] = "手机号码";
        $field[$key]["show"] = 0;
        $field[$key]["custom"] = 0;
        $key++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => '暂无CRM学员信息', 'result' => $result);

        }
        ajax_return($res, $request['language_type']);
    }

    function getOldStuApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\RegistrationModel($request);
        $res = $Model->getOldStu($request);

        $field = array();

        $field[0]["fieldstring"] = "school_id";
        $field[0]["fieldname"] = "学校ID";
        $field[0]["show"] = 0;
        $field[0]["custom"] = 0;

        $field[1]["fieldstring"] = "school_shortname";
        $field[1]["fieldname"] = "校区名称";
        $field[1]["show"] = 1;
        $field[1]["custom"] = 1;

        $field[2]["fieldstring"] = "student_id";
        $field[2]["fieldname"] = "学员ID";
        $field[2]["show"] = 0;
        $field[2]["custom"] = 0;

        $field[3]["fieldstring"] = "student_cnname";
        $field[3]["fieldname"] = "学员中文名";
        $field[3]["show"] = 1;
        $field[3]["custom"] = 1;

        $field[4]["fieldstring"] = "student_enname";
        $field[4]["fieldname"] = "学员英文名";
        $field[4]["show"] = 1;
        $field[4]["custom"] = 1;

        $field[5]["fieldstring"] = "student_branch";
        $field[5]["fieldname"] = "学员编号";
        $field[5]["show"] = 1;
        $field[5]["custom"] = 1;

        $field[6]["fieldstring"] = "family_cnname";
        $field[6]["fieldname"] = "主要联系人";
        $field[6]["show"] = 1;
        $field[6]["custom"] = 1;

        $field[7]["fieldstring"] = "family_mobile";
        $field[7]["fieldname"] = "主要联系方式";
        $field[7]["show"] = 1;
        $field[7]["custom"] = 1;

        $field[8]["fieldstring"] = "enrolled_leavetime";
        $field[8]["fieldname"] = "离校时间";
        $field[8]["show"] = 1;
        $field[8]["custom"] = 1;


        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => '暂无正式学员信息', 'result' => $result);

        }
        ajax_return($res, $request['language_type']);
    }

    function getEffectiveCourseApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\RegistrationModel($request);
        $res = $Model->getEffectiveCourse($request, $request['from']);
        $field = array();

        $field[0]["fieldstring"] = "course_id";
        $field[0]["fieldname"] = "课程ID";
        $field[0]["show"] = 0;
        $field[0]["custom"] = 0;

        $field[1]["fieldstring"] = "course_cnname";
        $field[1]["fieldname"] = "课程别名称";
        $field[1]["show"] = 1;
        $field[1]["custom"] = 1;

        $field[2]["fieldstring"] = "course_branch";
        $field[2]["fieldname"] = "课程别编号";
        $field[2]["show"] = 1;
        $field[2]["custom"] = 1;

        $field[3]["fieldstring"] = "coursecat_cnname";
        $field[3]["fieldname"] = "班种名称";
        $field[3]["show"] = 1;
        $field[3]["custom"] = 1;

        $field[4]["fieldstring"] = "coursecat_branch";
        $field[4]["fieldname"] = "班种编号";
        $field[4]["show"] = 1;
        $field[4]["custom"] = 1;

        $field[5]["fieldstring"] = "tuition_originalprice";
        $field[5]["fieldname"] = "原始价格";
        $field[5]["show"] = 1;
        $field[5]["custom"] = 1;

        $field[6]["fieldstring"] = "tuition_sellingprice";
        $field[6]["fieldname"] = "销售价格";
        $field[6]["show"] = 1;
        $field[6]["custom"] = 1;

        $field[7]["fieldstring"] = "tuition_unitprice";
        $field[7]["fieldname"] = "标准单价";
        $field[7]["show"] = 1;
        $field[7]["custom"] = 1;

        $field[8]["fieldstring"] = "course_classnum";
        $field[8]["fieldname"] = "课次";
        $field[8]["show"] = 1;
        $field[8]["custom"] = 1;

        $field[9]["fieldstring"] = "classNum";
        $field[9]["fieldname"] = "已开班数";
        $field[9]["show"] = 1;
        $field[9]["custom"] = 1;

        $field[10]["fieldstring"] = "pricing_id";
        $field[10]["fieldname"] = "定价ID";
        $field[10]["show"] = 0;
        $field[10]["custom"] = 0;

        $field[11]["fieldstring"] = "agreement_id";
        $field[11]["fieldname"] = "协议ID";
        $field[11]["show"] = 0;
        $field[11]["custom"] = 0;

        $field[12]["fieldstring"] = "course_presentednums";
        $field[12]["fieldname"] = "赠送课时数";
        $field[12]["show"] = 0;
        $field[12]["custom"] = 0;

        $field[13]["fieldstring"] = "course_inclasstype";
        $field[13]["fieldname"] = "上课方式";
        $field[13]["show"] = 0;
        $field[13]["custom"] = 0;

        $result = array();
        $result["field"] = $field;
        if ($res) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无课程信息', 'result' => $result);

        }
        ajax_return($res, $request['language_type']);

    }

    function getWarehouseListApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\RegistrationModel($request);
        $res = $Model->getWarehouseList($request);
        $field = array();

        $field[0]["fieldstring"] = "coursepacks_id";
        $field[0]["fieldname"] = "组合商品ID";
        $field[0]["show"] = 0;
        $field[0]["custom"] = 0;

        $field[1]["fieldstring"] = "coursepacks_name";
        $field[1]["fieldname"] = "组合课程名称";
        $field[1]["show"] = 1;
        $field[1]["custom"] = 1;

        $field[2]["fieldstring"] = "time";
        $field[2]["fieldname"] = "有效期";
        $field[2]["show"] = 1;
        $field[2]["custom"] = 1;

        $field[3]["fieldstring"] = "course_num";
        $field[3]["fieldname"] = "课程数量";
        $field[3]["show"] = 1;
        $field[3]["custom"] = 1;

        $field[4]["fieldstring"] = "num";
        $field[4]["fieldname"] = "涉及主体";
        $field[4]["show"] = 1;
        $field[4]["custom"] = 1;

        $field[5]["fieldstring"] = "agreement_cnname";
        $field[5]["fieldname"] = "协议名称";
        $field[5]["show"] = 1;
        $field[5]["custom"] = 1;

        $field[6]["fieldstring"] = "tuition_sellingprice";
        $field[6]["fieldname"] = "组合销售优惠价";
        $field[6]["show"] = 1;
        $field[6]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        if ($res) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无课程信息', 'result' => $result);

        }
        ajax_return($res, $request['language_type']);

    }

    function getOneWarehouseInfoApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\RegistrationModel($request);
        $res = $Model->getOneWarehouseInfo($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function getEffectiveClassApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\RegistrationModel($request);
        $res = $Model->getEffectiveClass($request);

        $field = array();

        $field[0]["fieldstring"] = "class_id";
        $field[0]["fieldname"] = "班级ID";
        $field[0]["show"] = 0;
        $field[0]["custom"] = 0;

        $field[2]["fieldstring"] = "class_cnname";
        $field[2]["fieldname"] = "班级名称";
        $field[2]["show"] = 1;
        $field[2]["custom"] = 1;

        $field[3]["fieldstring"] = "class_enname";
        $field[3]["fieldname"] = "班级别名";
        $field[3]["show"] = 1;
        $field[3]["custom"] = 1;

        $field[4]["fieldstring"] = "class_branch";
        $field[4]["fieldname"] = "班级编号";
        $field[4]["show"] = 1;
        $field[4]["custom"] = 1;

        $field[5]["fieldstring"] = "number";
        $field[5]["fieldname"] = "人数";
        $field[5]["show"] = 1;
        $field[5]["custom"] = 1;
        $field[5]["isCircleProgress"] = false;
        $field[5]["isSquareProgress"] = true;

        $field[6]["fieldstring"] = "course_cnname";
        $field[6]["fieldname"] = "课程别";
        $field[6]["show"] = 1;
        $field[6]["custom"] = 1;

        $field[7]["fieldstring"] = "cnteacher";
        $field[7]["fieldname"] = "教师";
        $field[7]["show"] = 1;
        $field[7]["custom"] = 1;

        $field[9]["fieldstring"] = "class_stdate";
        $field[9]["fieldname"] = "开班日期";
        $field[9]["show"] = 1;
        $field[9]["custom"] = 1;

        $field[10]["fieldstring"] = "info";
        $field[10]["fieldname"] = "已上/计划";
        $field[10]["show"] = 1;
        $field[10]["custom"] = 1;
        $field[10]["isCircleProgress"] = true;
        $field[10]["isSquareProgress"] = false;

        $field[11]["fieldstring"] = "course_presentednums";
        $field[11]["fieldname"] = "赠送课时数";
        $field[11]["show"] = 0;
        $field[11]["custom"] = 0;

        $field[12]["fieldstring"] = "course_id";
        $field[12]["fieldname"] = "课程ID";
        $field[12]["show"] = 0;
        $field[12]["custom"] = 0;

        $field[13]["fieldstring"] = "course_inclasstype";
        $field[13]["fieldname"] = "上课方式";
        $field[13]["show"] = 0;
        $field[13]["custom"] = 0;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 0;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => '暂无班级信息', 'result' => $result);

        }
        ajax_return($res, $request['language_type']);
    }


    //获取课程绑定的教材信息
    function getCourseGoodsApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\RegistrationModel($request);
        $res = $Model->getCourseGoods($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function getachieveListApi(){
        $request = Input('post.', '', 'trim,addslashes');

        $this->ThisVerify($request);
        $Model = new \Model\Smc\RegistrationModel($request);
        $res = $Model->getachieveList($request);
        $field = array();
        $k=0;

        $field[$k]["fieldstring"] = "staffer_cnname";
        $field[$k]["fieldname"] = "教师姓名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "staffer_branch";
        $field[$k]["fieldname"] = "教师编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "performance_name";
        $field[$k]["fieldname"] = "业绩明细";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "num";
        $field[$k]["fieldname"] = "计业绩人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "price";
        $field[$k]["fieldname"] = "业绩金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "performance_iscalculated_name";
        $field[$k]["fieldname"] = "是否计业绩";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $result = array();
        $result["field"] = $field;
        if ($res) {
            $result["list"] = $res['list'];
            $result["class"] = $res['class'];
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    function getclientTrackApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\RegistrationModel($request);
        $res = $Model->getclientTrack($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function getPerformanceListApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\RegistrationModel($request);
        $res = $Model->getPerformanceList($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //获取课程需要支付的金额
    function getOrderPriceApi()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\RegistrationModel($request);

        if(isset($request['coursepacks_id']) && $request['coursepacks_id']!=''){
            $res = $Model->getMergeOrderPrice($request);
        }else{

            $courseList = json_decode(stripslashes($request['list']), true);

            if(!$courseList){
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }

            $comArray=array();

            foreach($courseList as $itemOne){
                $courseCompaniesOne=$this->getSchoolCourseCompanies($request['school_id'],'',$itemOne['course_id']);
                $comArray[$courseCompaniesOne['companies_id']][]=$itemOne;
            }

            if(count($comArray)>1){
                $res = $Model->getMergeOrderPrice($request);
            }else{
                $res = $Model->getOrderPrice($request);
            }

        }


        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    //生成缴费报名订单
    function createOrderAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\RegistrationModel($request);

        $is_merge=0;
        if(isset($request['coursepacks_id']) && $request['coursepacks_id']!=''){
            $res = $Model->createMergeOrder($request, 0);
            $is_merge=1;
        }else{

            $courseList = json_decode(stripslashes($request['list']), true);

            if(!$courseList){
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }

            $comArray=array();

            foreach($courseList as $itemOne){
                $courseCompaniesOne=$this->getSchoolCourseCompanies($request['school_id'],'',$itemOne['course_id']);
                $comArray[$courseCompaniesOne['companies_id']][]=$itemOne;
            }
            if(count($comArray)>1){
                $res = $Model->createMergeOrder($request, 0);
                $is_merge=1;
            }else{
                $res = $Model->createOrder($request, 0, 1);
            }

        }

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '生成成功', 'result' => $result, 'is_merge' => $is_merge);

            $this->addSmcWorkLog($request['company_id'], $request['school_id'], $request['staffer_id'], "缴费管理->报名缴费", '生成缴费订单', dataEncode($request));
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }


    function getOrderachieveListApi(){
        $request = Input('get.', '', 'trim,addslashes');

        $this->ThisVerify($request);
        $Model = new \Model\Smc\RegistrationModel($request);
        $res = $Model->getOrderachieveList($request);
        $field = array();
        $k=0;

        $field[$k]["fieldstring"] = "staffer_cnname";
        $field[$k]["fieldname"] = "教师姓名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "staffer_branch";
        $field[$k]["fieldname"] = "教师编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "performance_name";
        $field[$k]["fieldname"] = "业绩明细";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "achieve_number";
        $field[$k]["fieldname"] = "计业绩人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "achieve_price";
        $field[$k]["fieldname"] = "业绩金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "achieve_iscalculated_name";
        $field[$k]["fieldname"] = "是否计业绩";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "achieve_note";
        $field[$k]["fieldname"] = "备注";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "achieve_status_name";
        $field[$k]["fieldname"] = "审核状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $result = array();
        $result["field"] = $field;
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    function addAchieveAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\RegistrationModel($request);
        $res = $Model->addAchieve($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '添加成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function getSchoolachieveListApi(){
        $request = Input('get.', '', 'trim,addslashes');

        $this->ThisVerify($request);
        $Model = new \Model\Smc\RegistrationModel($request);
        $res = $Model->getSchoolachieveList($request);
        $field = array();
        $k=0;

        $field[$k]["fieldstring"] = "staffer_cnname";
        $field[$k]["fieldname"] = "业绩教师";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "performance_name";
        $field[$k]["fieldname"] = "业绩明细";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "coursetype_cnname";
        $field[$k]["fieldname"] = "所属班组";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "trading_pid";
        $field[$k]["fieldname"] = "交易编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "achieve_price";
        $field[$k]["fieldname"] = "业绩金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["fieldname"] = "学员姓名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "student_branch";
        $field[$k]["fieldname"] = "学员编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "achieve_note";
        $field[$k]["fieldname"] = "备注";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "order_status_name";
        $field[$k]["fieldname"] = "订单状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "achieve_status_name";
        $field[$k]["fieldname"] = "审核状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "achieve_iscalculated_name";
        $field[$k]["fieldname"] = "是否计业绩";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $result = array();
        $result["field"] = $field;
        if ($res) {
            $result["list"] = $res;
            $result["allnum"] = $res['allnum'];
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    function getachieveOneApi(){
        $request = Input('get.', '', 'trim,addslashes');

        $this->ThisVerify($request);
        $Model = new \Model\Smc\RegistrationModel($request);
        $res = $Model->getachieveOne($request);
        $field = array();
        $k=0;

        $field[$k]["fieldstring"] = "staffer_cnname";
        $field[$k]["fieldname"] = "教师姓名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "staffer_branch";
        $field[$k]["fieldname"] = "教师编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["fieldname"] = "学员姓名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "student_branch";
        $field[$k]["fieldname"] = "学员编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "performance_name";
        $field[$k]["fieldname"] = "业绩明细";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "achieve_number";
        $field[$k]["fieldname"] = "计业绩人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $result = array();
        $result["field"] = $field;
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function getAllTeacherApi(){
        $request = Input('get.', '', 'trim,addslashes');

        $this->ThisVerify($request);
        $Model = new \Model\Smc\RegistrationModel($request);
        $res = $Model->getAllTeacher($request);
        $field = array();
        $k=0;

        $field[$k]["fieldstring"] = "staffer_id";
        $field[$k]["fieldname"] = "教师id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "staffer_cnname";
        $field[$k]["fieldname"] = "教师姓名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "staffer_branch";
        $field[$k]["fieldname"] = "教师编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "ismianjob_name";
        $field[$k]["fieldname"] = "是否主职";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "post_name";
        $field[$k]["fieldname"] = "职务";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $result = array();
        $result["field"] = $field;
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function getAllPostApi(){
        $request = Input('get.', '', 'trim,addslashes');

        $this->ThisVerify($request);
        $Model = new \Model\Smc\RegistrationModel($request);
        $res = $Model->getAllPost($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function editachieveAction(){

        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\RegistrationModel($request);
        $res = $Model->editachieve($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '编辑成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function schoolHandleachieveAction(){

        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\RegistrationModel($request);
        $res = $Model->schoolHandleachieve($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => $Model->oktip, 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function autoHandleAchieveAction()
    {
        $request = Input('get.', '', 'trim,addslashes');
//        $this->ThisVerify($request);
        $Model = new \Model\Smc\RegistrationModel($request);
        $res = $Model->autoHandleAchieve($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function complementFeesAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\RegistrationModel($request);
        $res = $Model->complementFees($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function getFitPriceListApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\RegistrationModel($request);
        $res = $Model->getFitPriceList($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function makeUpInfoApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\RegistrationModel($request);
        $res = $Model->makeUpInfo($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function getMakeUpPriceApi()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\RegistrationModel($request);
        $res = $Model->getMakeUpPrice($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function getAdmissionInfoApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\RegistrationModel($request);
        $res = $Model->getAdmissionInfo($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);

        }
        ajax_return($res, $request['language_type']);
    }

    function forceClassAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);

        $a = $this->DataControl->selectOne("
            SELECT
                p.post_istopjob 
            FROM
                gmc_staffer_postbe AS sp
            LEFT JOIN gmc_company_post AS p ON p.post_id = sp.post_id
            WHERE sp.postbe_id = '{$request['re_postbe_id']}'");

        if ($a['post_istopjob'] != '1' && $request['re_postbe_id'] != '0') {
            ajax_return(array('error' => 1, 'errortip' => "学员出班期间，班级已有新的上课记录，您没有权限操作回原班，请联系校长操作回原班"));
        }

        $Model = new \Model\Smc\RegistrationModel($request);
        $res = $Model->forceClass($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '入班成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //课程充值
    function makeUpFeesAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\RegistrationModel($request);
        $res = $Model->makeUpFees($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '补费成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //判断学员是否在就读过班级
    function checkStuClassApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\RegistrationModel($request);
        $res = $Model->checkStuClass($request);

        if ($res) {
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $res);
        } else {
            $res = array('error' => 1, 'errortip' => '暂无信息', 'result' => array());

        }
        ajax_return($res, $request['language_type']);
    }


    //生成支付订单
    function createPayOrderAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\RegistrationModel($request);
        $res = $Model->createPayOrder($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '生成成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //查看学员冲突列表
    function getConflictStuApi()
    {

        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\RegistrationModel($request);
        $res = $Model->getConflictStu($request);

        $field = array();

        $field[0]["fieldstring"] = "student_id";
        $field[0]["fieldname"] = "学员ID";
        $field[0]["show"] = 0;
        $field[0]["custom"] = 0;

        $field[1]["fieldstring"] = "school_cnname";
        $field[1]["fieldname"] = "校区名称";
        $field[1]["show"] = 1;
        $field[1]["custom"] = 1;

        $field[2]["fieldstring"] = "student_cnname";
        $field[2]["fieldname"] = "学员姓名";
        $field[2]["show"] = 1;
        $field[2]["custom"] = 1;

        $field[3]["fieldstring"] = "from";
        $field[3]["fieldname"] = "所在位置";
        $field[3]["show"] = 1;
        $field[3]["custom"] = 1;

        $field[4]["fieldstring"] = "student_branch";
        $field[4]["fieldname"] = "学员编号";
        $field[4]["show"] = 1;
        $field[4]["custom"] = 1;

        $field[5]["fieldstring"] = "student_sex";
        $field[5]["fieldname"] = "性别";
        $field[5]["show"] = 1;
        $field[5]["custom"] = 1;

        $field[6]["fieldstring"] = "family_cnname";
        $field[6]["fieldname"] = "主要联系人";
        $field[6]["show"] = 1;
        $field[6]["custom"] = 1;

        $field[7]["fieldstring"] = "family_mobile";
        $field[7]["fieldname"] = "手机号";
        $field[7]["show"] = 1;
        $field[7]["custom"] = 1;

        $field[8]["fieldstring"] = "classNum";
        $field[8]["fieldname"] = "在读课程";
        $field[8]["show"] = 1;
        $field[8]["custom"] = 1;

        $field[9]["fieldstring"] = "student_balance";
        $field[9]["fieldname"] = "学员账户余额";
        $field[9]["show"] = 0;
        $field[9]["custom"] = 0;

        $field[10]["fieldstring"] = "student_forwardprice";
        $field[10]["fieldname"] = "账户结转余额";
        $field[10]["show"] = 0;
        $field[10]["custom"] = 0;

        $field[11]["fieldstring"] = "student_img";
        $field[11]["fieldname"] = "学员头像";
        $field[11]["show"] = 0;
        $field[11]["custom"] = 0;

        $field[12]["fieldstring"] = "student_enname";
        $field[12]["fieldname"] = "学员英文名";
        $field[12]["show"] = 0;
        $field[12]["custom"] = 0;

        $field[13]["fieldstring"] = "student_idcard";
        $field[13]["fieldname"] = "身份证";
        $field[13]["show"] = 0;
        $field[13]["custom"] = 0;

        $field[14]["fieldstring"] = "family_relation";
        $field[14]["fieldname"] = "关系";
        $field[14]["show"] = 0;
        $field[14]["custom"] = 0;

        $field[15]["fieldstring"] = "arrears";
        $field[15]["fieldname"] = "欠费金额";
        $field[15]["show"] = 0;
        $field[15]["custom"] = 0;

        $field[16]["fieldstring"] = "enrolled_status_name";
        $field[16]["fieldname"] = "状态";
        $field[16]["show"] = 1;
        $field[16]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        if ($res) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);

        }
        ajax_return($res, $request['language_type']);
    }

    //获取可用优惠活动
    function getAvailableActivitiesListApi()
    {

        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\RegistrationModel($request);

        if(isset($request['coursepacks_id']) && $request['coursepacks_id']!=''){
            $res = $Model->getMergeAvailableActivitiesList($request);
        }else{

            $courseList = json_decode(stripslashes($request['list']), true);

            if(!$courseList){
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }

            $comArray=array();

            foreach($courseList as $itemOne){
                $courseCompaniesOne=$this->getSchoolCourseCompanies($request['school_id'],'',$itemOne['course_id']);
                $comArray[$courseCompaniesOne['companies_id']][]=$itemOne;
            }

            if(count($comArray)>1){
                $res = $Model->getMergeAvailableActivitiesList($request);
            }else{
                $res = $Model->getAvailableActivitiesList($request);
            }

        }

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //获取可用订单优惠券
    function getAvailableOrderTicketListApi()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\RegistrationModel($request);

        if(isset($request['warehouse_id']) && $request['warehouse_id']!=''){
            $res = $Model->getMergeAvailableOrderTicketList($request);
        }else{

            $courseList = json_decode(stripslashes($request['list']), true);

            if(!$courseList){
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }

            $comArray=array();

            foreach($courseList as $itemOne){
                $courseCompaniesOne=$this->getSchoolCourseCompanies($request['school_id'],'',$itemOne['course_id']);
                $comArray[$courseCompaniesOne['companies_id']][]=$itemOne;
            }

            if(count($comArray)>1){
                $res = $Model->getMergeAvailableOrderTicketList($request);
            }else{
                $res = $Model->getAvailableOrderTicketList($request);
            }

        }

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }


    //获取可用优惠券
    function getAvailableTicketListApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\RegistrationModel($request);
        $res = $Model->getAvailableTicketList($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //优惠券申请列表
    function getCouponsListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);

        $Model = new \Model\Smc\RegistrationModel($request);

        $result = $Model->getCouponsList($request);
        ajax_return($result, $request['language_type']);
    }

    //批量申请学员列表
    function batchStudentInfoListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);

        $Model = new \Model\Smc\RegistrationModel($request);

        $result = $Model->batchStudentInfoList($request);
        ajax_return($result, $request['language_type']);
    }

    //学员优惠券列表
    function getStudentCouponsListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);

        $Model = new \Model\Smc\RegistrationModel($request);

        $result = $Model->getStudentCouponsList($request);
        ajax_return($result, $request['language_type']);
    }

    //申请优惠券
    function applyCouponsAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\RegistrationModel($request);

        $result = $Model->applyCouponsAction($request);
        $this->addSmcWorkLog($request['company_id'], $request['school_id'], $request['staffer_id'], "缴费管理->优惠券申请", '优惠券申请', dataEncode($request));

        ajax_return($result, $request['language_type']);
    }

    //批量申请优惠券
    function batchApplyCouponsAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\RegistrationModel($request);

        $List = json_decode(stripslashes($request['list']), true);
        foreach ($List as $k => &$v) {
            $v['course'] = json_encode(($v['course']), true);
            $v['url'] = json_encode(($v['url']), true);
            $result = $Model->applyCouponsAction($v);
        }
        ajax_return($result, $request['language_type']);
    }


    //禁用优惠券
    function forbiddenAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\RegistrationModel($request);

        $result = $Model->forbiddenAction($request);
        $this->addSmcWorkLog($request['company_id'], $request['school_id'], $request['staffer_id'], "缴费管理->优惠券申请", '禁用优惠券', dataEncode($request));

        ajax_return($result, $request['language_type']);
    }

    //添加适配课程
    function addCourseAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\RegistrationModel($request);

        $result = $Model->addCourseAction($request);
        ajax_return($result, $request['language_type']);
    }

    //优惠券类型列表
    function getApplytypeApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\RegistrationModel($request);

        $result = $Model->getApplytypeApi($request);
        ajax_return($result, $request['language_type']);
    }

    //可选课程列表
    function getCourseApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\RegistrationModel($request);

        $result = $Model->getCourseApi($request);
        ajax_return($result, $request['language_type']);
    }

    //同意拒绝
    function updateStatusAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Smc\RegistrationModel($request);

        $result = $this->Model->updateStatusAction($request);
        ajax_return($result, $request['language_type']);
    }

    function getCouponsFileView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Smc\RegistrationModel($request);

        $result = $this->Model->getCouponsFile($request);
        ajax_return($result, $request['language_type']);
    }

    function getSettleInfoListApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\RegistrationModel($request);
        $res = $Model->getSettleInfoList($request);
        $field = array();

        $k = 0;
        $field[$k]["fieldstring"] = "share_id";
        $field[$k]["fieldname"] = "分摊ID";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["fieldname"] = "学员中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_enname";
        $field[$k]["fieldname"] = "学员英文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_branch";
        $field[$k]["fieldname"] = "学员编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_cnname";
        $field[$k]["fieldname"] = "班级名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_branch";
        $field[$k]["fieldname"] = "班级编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "share_month";
        $field[$k]["fieldname"] = "结算月份";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "should_times";
        $field[$k]["fieldname"] = "应到";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "share_attend_times";
        $field[$k]["fieldname"] = "实到";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "share_absent_times";
        $field[$k]["fieldname"] = "缺勤";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "share_settle_price";
        $field[$k]["fieldname"] = "原分摊金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "price";
        $field[$k]["fieldname"] = "实际分摊金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

//        $field[$k]["fieldstring"] = "surplus_price";
//        $field[$k]["fieldname"] = "剩余金额";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//        $k++;

        $field[$k]["fieldstring"] = "share_status_name";
        $field[$k]["fieldname"] = "处理状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;
        if ($res) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无课程信息', 'result' => $result);

        }
        ajax_return($res, $request['language_type']);
    }

    function confirmSettleAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\RegistrationModel($request);
        $res = $Model->confirmSettle($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '处理完成', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function settleClassListApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\RegistrationModel($request);
        $res = $Model->settleClassList($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function getSettleOneApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\RegistrationModel($request);
        $res = $Model->getSettleOne($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function writeOffDebtAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\RegistrationModel($request);
        $res = $Model->writeOffDebt($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '坏账处理申请成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function getschoolBreakoffApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\RegistrationModel($request);
        $res = $Model->getschoolBreakoff($request);
        $field = array();

        $k = 0;
        $field[$k]["fieldstring"] = "breakoff_id";
        $field[$k]["fieldname"] = "拆班ID";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "class_cnname";
        $field[$k]["fieldname"] = "班级名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "course_cnname";
        $field[$k]["fieldname"] = "课程别名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "course_branch";
        $field[$k]["fieldname"] = "课程别编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_enname";
        $field[$k]["fieldname"] = "班级别名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_branch";
        $field[$k]["fieldname"] = "班级编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "number";
        $field[$k]["fieldname"] = "班级人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_status_name";
        $field[$k]["fieldname"] = "班级状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "progress";
        $field[$k]["fieldname"] = "上课进度";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_enddate";
        $field[$k]["fieldname"] = "结班日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "teacher_name";
        $field[$k]["fieldname"] = "主教老师";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "breakoff_type_name";
        $field[$k]["fieldname"] = "拆班类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "reason";
        $field[$k]["fieldname"] = "申请拆班原因";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "staffer_cnname";
        $field[$k]["fieldname"] = "申请人";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "breakoff_status_name";
        $field[$k]["fieldname"] = "审核状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;
        if ($res) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无课程信息', 'result' => $result);

        }
        ajax_return($res, $request['language_type']);
    }

    function examineBreakoffAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\RegistrationModel($request);
        $res = $Model->examineBreakoff($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '审核成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function getbreakoffOneApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\RegistrationModel($request);
        $res = $Model->getbreakoffOne($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function stuBalanceApplyAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\RegistrationModel($request);
        $res = $Model->stuBalanceApply($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '申请成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function getCompanyBalanceApplyListApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\RegistrationModel($request);
        $res= $Model->getCompanyBalanceApplyList($request);
        $field=array();

        $k=0;
        $field[$k]["fieldname"] = "balanceapply_id";
        $field[$k]["fieldstring"] = "申请ID";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "student_cnname";
        $field[$k]["fieldstring"] = "学员中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "student_branch";
        $field[$k]["fieldstring"] = "学员编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "coursetype_cnname";
        $field[$k]["fieldstring"] = "班组";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "picture";
        $field[$k]["fieldstring"] = "申请凭证";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["ismethod"] = 1;
        $k++;

        $field[$k]["fieldname"] = "balanceapply_remarks";
        $field[$k]["fieldstring"] = "申请原因";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "staffer_cnname";
        $field[$k]["fieldstring"] = "申请人";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "balanceapply_status_name";
        $field[$k]["fieldstring"] = "审核状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "balanceapply_usestatus_name";
        $field[$k]["fieldstring"] = "使用状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "balanceapply_statusreason";
        $field[$k]["fieldstring"] = "审核备注";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "staff_cnname";
        $field[$k]["fieldstring"] = "审核人";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "balanceapply_addtime";
        $field[$k]["fieldstring"] = "申请时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "balanceapply_examinetime";
        $field[$k]["fieldstring"] = "审核时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        if($res){
            $result["fieldcustom"] = 1;
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        }else{
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);

        }
        ajax_return($res,$request['language_type']);
    }


}
