<?php


namespace Work\Controller\Smcapi;


class AffairsController extends viewTpl{
    public $u;
    public $t;
    public $c;
    public $visitType = "api";
    public $DataControl;
    public $Model;

    function __construct() {
        parent::__construct ();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();

        //数据库操作
        $this->DataControl = new \Dbmysql();
    }

    function ThisVerify($request){
        $paramArray = array();
        $paramArray['staffer_id'] = $request['staffer_id'];
        $paramArray['school_id'] = $request['school_id'];
        $paramArray['company_id'] = $request['company_id'];
        $paramArray['token'] = $request['token'];
        if(!$this->UserLimit($paramArray)){
            $result = array();
            $result["list"] = array();
            $result["tokeninc"] = "0";
            $res = array('error' => 1, 'errortip' => "用户token失效", 'result' => $result);
            ajax_return($res,$request['language_type']);
        }
    }

    //编辑职工校园职务
    function updateStafferSchoolPostAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $this->Model =  new \Model\Smc\AffairsModel($request);

        $result = $this->Model->updateStafferSchoolPostAction($request);
        ajax_return($result,$request['language_type']);
    }

    //职工管理列表
    function HomeView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\AffairsModel($request);

        $result = $Model->getStafferList($request);
        ajax_return($result,$request['language_type']);
    }

    //职工管理列表
    function outClassListView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\AffairsModel($request);

        $result = $Model->outClassList($request);
        ajax_return($result,$request['language_type']);
    }

    //带班记录列表
    function ClassListView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\AffairsModel($request);

        $result = $Model->ClassList($request);
        ajax_return($result,$request['language_type']);
    }

    //上课记录列表
    function ClassTeachingView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\AffairsModel($request);

        $result = $Model->ClassTeaching($request);
        ajax_return($result,$request['language_type']);
    }

    //教师课表列表
    function TeaTableListView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\AffairsModel($request);

        $result = $Model->TeaTableList($request);
        ajax_return($result,$request['language_type']);
    }

    //获取学校
    function getSchoolApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $this->Model = new \Model\Smc\AffairsModel($request);

        $result = $this->Model->getSchoolApi($request);
        ajax_return($result,$request['language_type']);
    }

    //获取组织
    function getOrganizeApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $this->Model = new \Model\Smc\AffairsModel($request);

        $result = $this->Model->getOrganizeApi($request);
        ajax_return($result,$request['language_type']);
    }

    //获取校园职务
    function getPostApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $this->Model = new \Model\Smc\AffairsModel($request);

        $result = $this->Model->getPostApi($request);
        ajax_return($result,$request['language_type']);
    }

    //获取校园角色
    function getPostpartApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $this->Model = new \Model\Smc\AffairsModel($request);

        $result = $this->Model->getPostpartApi($request);
        ajax_return($result,$request['language_type']);
    }


    //删除职务
    function delPostAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $this->Model =  new \Model\Smc\AffairsModel($request);

        $result = $this->Model->delPostAction($request);

        ajax_return($result,$request['language_type']);
    }


    //教师课表列表
    function getStridePostListView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\AffairsModel($request);

        $result = $Model->getStridePostList($request);
        ajax_return($result,$request['language_type']);
    }

    //获取单个职工
    function getStafferOneListView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\AffairsModel($request);

        $result = $Model->getStafferOneList($request);
        ajax_return($result,$request['language_type']);
    }

    //人脸采集记录
    function getStafferPortraitApi(){
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\AffairsModel($request);
        $Model->getStafferPortraitApi($request);
        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $Model->result), $request['language_type']);
    }

    //添加职工
    function addStafferAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\AffairsModel($request);

        $result = $Model->addStafferAction($request);
        $this->addSmcWorkLog($request['company_id'],$request['school_id'],$request['staffer_id'],"人事管理->职工管理",'添加职工',dataEncode($request));
        ajax_return($result,$request['language_type']);
    }

    //添加跨校职务
    function addStafferSchoolPostAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\AffairsModel($request);

        $result = $Model->addStafferSchoolPostAction($request);
        $this->addSmcWorkLog($request['company_id'],$request['school_id'],$request['staffer_id'],"人事管理->职工管理",'添加跨校职务',dataEncode($request));
        ajax_return($result,$request['language_type']);
    }

    //编辑职工
    function updateStafferAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\AffairsModel($request);

        $result = $Model->updateStafferAction($request);
        $this->addSmcWorkLog($request['company_id'],$request['school_id'],$request['staffer_id'],"人事管理->职工管理",'编辑职工',dataEncode($request));
        ajax_return($result,$request['language_type']);
    }

    //删除职工
    function delStafferAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model =  new \Model\Smc\AffairsModel($request);

        $result = $Model->delStafferAction($request);
        $this->addSmcWorkLog($request['company_id'],$request['school_id'],$request['staffer_id'],"人事管理->职工管理",'删除职工',dataEncode($request));
        ajax_return($result,$request['language_type']);
    }

    //获取登陆个人信息
    function getStafferInfoApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Smc\AffairsModel($request);

        $result = $Model->getStafferInfoApi($request);


        ajax_return($result,$request['language_type'],1);
    }

    //编辑职工资料建档
    function updateStafferDataAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model =  new \Model\Smc\AffairsModel($request);

        $result = $Model->updateStafferDataAction($request);
        $this->addSmcWorkLog($request['company_id'],$request['school_id'],$request['staffer_id'],"人事管理->职工管理",'编辑职工资料建档',dataEncode($request));
        ajax_return($result,$request['language_type']);
    }

    //联系人
    function ContectView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\AffairsModel($request);

        $result = $Model->getContect($request);
        ajax_return($result,$request['language_type']);
    }

    //联系人
    function getStafferDataAction()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\AffairsModel($request);

        $result = $Model->getStafferDataAction($request);
        ajax_return($result,$request['language_type']);
    }

    //校园角色管理列表
    function getSchoolRoleListView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\AffairsModel($request);

        $result = $Model->getSchoolRoleList($request);
        ajax_return($result,$request['language_type']);
    }

    //删除校园角色
    function delSchoolRoleAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model =  new \Model\Smc\AffairsModel($request);

        $result = $Model->delSchoolRoleAction($request);
        $this->addSmcWorkLog($request['company_id'],$request['school_id'],$request['staffer_id'],"人事管理->职工管理",'删除校园角色',dataEncode($request));
        ajax_return($result,$request['language_type']);
    }


    //获取校园通知
    function NoticeView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Smc\AffairsModel($request);

        $result = $Model->getNotice($request);
        ajax_return($result,$request['language_type']);
    }

    //获取校园通知
    function NoticeDetailView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Smc\AffairsModel($request);

        $result = $Model->getNoticeDetail($request);
        ajax_return($result,$request['language_type']);
    }

    //添加校园角色
    function addSchoolRoleAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\AffairsModel($request);

        $result = $Model->addSchoolRoleAction($request);
        $this->addSmcWorkLog($request['company_id'],$request['school_id'],$request['staffer_id'],"人事管理->职工管理",'添加校园角色',dataEncode($request));
        ajax_return($result,$request['language_type']);
    }

    //编辑校园角色
    function updateSchoolRoleAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\AffairsModel($request);

        $result = $Model->updateSchoolRoleAction($request);
        $this->addSmcWorkLog($request['company_id'],$request['school_id'],$request['staffer_id'],"人事管理->职工管理",'编辑校园角色',dataEncode($request));
        ajax_return($result,$request['language_type']);
    }

    //校园职工列表
    function getpostListAction()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\AffairsModel($request);

        $result = $Model->getpostList($request);
        ajax_return($result,$request['language_type']);
    }

    //获取校园职工角色
    function addRoleStafferAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model =  new \Model\Smc\AffairsModel($request);

        $result = $Model->addRoleStafferAction($request);
        $this->addSmcWorkLog($request['company_id'],$request['school_id'],$request['staffer_id'],"校园角色管理->列表成员设置",'添加成员',dataEncode($request));
        ajax_return($result,$request['language_type']);
    }

    //校园角色成员列表
    function getRoleListAction()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\AffairsModel($request);

        $result = $Model->getRoleList($request);
        ajax_return($result,$request['language_type']);
    }

    //删除职工校园角色
    function delStafferRoleAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model =  new \Model\Smc\AffairsModel($request);

        $result = $Model->delStafferRoleAction($request);
        $this->addSmcWorkLog($request['company_id'],$request['school_id'],$request['staffer_id'],"人事管理->职工管理",'删除职工校园角色',dataEncode($request));
        ajax_return($result,$request['language_type']);
    }

    //编辑学校职工角色
    function updatePostPartAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\AffairsModel($request);

        $result = $Model->updatePostPartAction($request);
        $this->addSmcWorkLog($request['company_id'],$request['school_id'],$request['staffer_id'],"人事管理->职工管理",'编辑学校职工角色',dataEncode($request));
        ajax_return($result,$request['language_type']);
    }

    //更换地址
    function ChangeUrlView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\AffairsModel($request);

        $result = $Model->ChangeUrl($request);
        ajax_return($result,$request['language_type'],1);
    }

    //职工离职
    function updateLeaveAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $this->Model =  new \Model\Smc\AffairsModel($request);

        $result = $this->Model->updateLeaveAction($request);
        $this->addSmcWorkLog($request['company_id'],$request['school_id'],$request['staffer_id'],"人事管理->职工管理",'职工离职',dataEncode($request));
        ajax_return($result,$request['language_type']);
    }

    //解除职务
    function relieveLeaveAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $this->Model =  new \Model\Smc\AffairsModel($request);

        $result = $this->Model->relieveLeaveAction($request);
        $this->addSmcWorkLog($request['company_id'],$request['school_id'],$request['staffer_id'],"人事管理->职工管理",'解除职务',dataEncode($request));
        ajax_return($result,$request['language_type']);
    }

    //职工复职
    function updateJoinAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $this->Model =  new \Model\Smc\AffairsModel($request);

        $result = $this->Model->updateJoinAction($request);
        $this->addSmcWorkLog($request['company_id'],$request['school_id'],$request['staffer_id'],"人事管理->职工管理",'职工复职',dataEncode($request));
        ajax_return($result,$request['language_type']);
    }

    //首页统计的数量
    function getIndexCountAction()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\AffairsModel($request);

        $result = $Model->getIndexCountAction($request);
        ajax_return($result,$request['language_type']);
    }

    //新增兼职职工
    function addPartStafferAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $this->Model =  new \Model\Smc\AffairsModel($request);

        $result = $this->Model->addPartStafferAction($request);
        $this->addSmcWorkLog($request['company_id'],$request['school_id'],$request['staffer_id'],"人事管理->职工管理",'新增兼职职工',dataEncode($request));
        ajax_return($result,$request['language_type']);
    }

    //兼职职务下拉列表
    function getCompaniesListApi()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $sql = "select post_id,post_name from gmc_company_post where post_isrecrparttime = '1' and company_id = '{$request['company_id']}'";
        $CompaniesList = $this->DataControl->selectClear($sql);
        if (!$CompaniesList) {
            $CompaniesList = array();
        }

        $result["list"] = $CompaniesList;
        $res = array('error' => 0, 'errortip' => '获取兼职职务列表', 'result' => $result);

        ajax_return($res,$request['language_type']);
    }

    //班级下拉列表
    function getClassApi()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $sql = "select class_id,class_cnname,class_enname from smc_class where school_id = '{$request['school_id']}'";
        $CompaniesList = $this->DataControl->selectClear($sql);
        if (!$CompaniesList) {
            $CompaniesList = array();
        }

        $result["list"] = $CompaniesList;
        $res = array('error' => 0, 'errortip' => '获取兼职职务列表', 'result' => $result);

        ajax_return($res,$request['language_type']);
    }

    //编辑兼职职工
    function updatePartStafferAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $this->Model =  new \Model\Smc\AffairsModel($request);

        $result = $this->Model->updatePartStafferAction($request);
        $this->addSmcWorkLog($request['company_id'],$request['school_id'],$request['staffer_id'],"人事管理->职工管理",'编辑兼职职工',dataEncode($request));
        ajax_return($result,$request['language_type']);
    }

    //判断是否为校长
    function isHeadmasterApi()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $a = $this->DataControl->selectOne("
            SELECT
                p.post_istopjob 
            FROM
                gmc_staffer_postbe AS sp
            LEFT JOIN gmc_company_post AS p ON p.post_id = sp.post_id
            WHERE sp.postbe_id = '{$request['re_postbe_id']}'");

        if($a['post_istopjob'] == '1' || $request['re_postbe_id'] == '0'){
            $status = '1';
            $res = array('error' => 0, 'errortip' => '获取是否为校长', 'result' => $status);
        }else{
            $status = '0';
            $res = array('error' => 1, 'errortip' => '您没有权限！', 'result' => $status);
        }


        ajax_return($res,$request['language_type']);
    }

    function testView(){
       $a = $this->DataControl->selectClear("select s.staffer_leavetime,s.staffer_id from smc_staffer as s where s.staffer_leavetime > 0 ORDER BY s.staffer_leavetime limit 0, 279");
       foreach($a as &$val){
           $data = array();
           $data['staffer_leavetime'] = date('Y-m-d', $val['staffer_leavetime']);
           $data['staffer_updatetime'] = time();
           $this->DataControl->updateData("smc_staffer","staffer_id = '{$val['staffer_id']}'",$data);
       }


    }

    //添加班外课时
    function classScheduleAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model =  new \Model\Smc\AffairsModel($request);

        $result = $Model->classScheduleAction($request);

        ajax_return($result, $request['language_type']);
    }

    //班外课时列表
    function getClassScheduleListView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\AffairsModel($request);

        $result = $Model->getClassScheduleList($request);
        ajax_return($result,$request['language_type']);
    }

    //取消班外课时
    function delClassScheduleAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model =  new \Model\Smc\AffairsModel($request);

        $result = $Model->delClassScheduleAction($request);
        ajax_return($result,$request['language_type']);
    }

    //批量取消班外课时
    function batchDelClassScheduleAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model =  new \Model\Smc\AffairsModel($request);

        $hour_list = json_decode(stripslashes($request['hour_list']), 1);
        if ($hour_list) {
            foreach ($hour_list as $value) {
                $result = $Model->delClassScheduleAction($value);
            }
        } else {
            $result = array('error' => '1', 'errortip' => '数据不全', 'result' => array());
        }
        ajax_return($result,$request['language_type']);
    }

    //注销学生
    function logoutStuAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\AffairsModel($request);
        $res = $Model->logoutStu($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '注销成功', 'result' => $result);
            $this->addSmcWorkLog($request['company_id'], $request['school_id'], $request['staffer_id'], "班务管理->学员异动管理->学员流失管理", '注销学籍', dataEncode($request));
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

}