<?php


namespace Work\Controller\Smcapi;


class OrderController extends viewTpl
{
    public $u;
    public $t;
    public $c;

    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
    }

    function homeView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $OrderModel = new \Model\Smc\OrderModel($request);
        $res = $OrderModel->stuTradeList($request);
        $field = array();
        $k = 0;

        $field[$k]["fieldstring"] = "trading_pid";
        $field[$k]["fieldname"] = "交易编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        if (!isset($request['student_id']) || $request['student_id'] == '') {
            $field[$k]["fieldstring"] = "student_cnname";
            $field[$k]["fieldname"] = "学员中文名";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "student_enname";
            $field[$k]["fieldname"] = "学员英文名";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "student_branch";
            $field[$k]["fieldname"] = "学员编号";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;
        } else {
            $field[$k]["fieldstring"] = "school_cnname";
            $field[$k]["fieldname"] = "学校名称";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "school_branch";
            $field[$k]["fieldname"] = "学校编号";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;
        }

        $field[$k]["fieldstring"] = "item_name";
        $field[$k]["fieldname"] = "项目名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "item_branch";
        $field[$k]["fieldname"] = "项目编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "order_type";
        $field[$k]["fieldname"] = "订单类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "companies_cnname";
        $field[$k]["fieldname"] = "订单主体";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "family_mobile";
        $field[$k]["fieldname"] = "下单手机号";
        $field[$k]["show"] = (!isset($request['student_id']) || $request['student_id'] == '') ? 1 : 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "order_status";
        $field[$k]["fieldname"] = "订单状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "order_from";
        $field[$k]["fieldname"] = "订单来源";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

//        $field[$k]["fieldstring"] = "order_type";
//        $field[$k]["fieldname"] = "收费类型";
//        $field[$k]["show"] = 0;
//        $field[$k]["custom"] = 1;
//        $k++;

        $field[$k]["fieldstring"] = "order_allprice";
        $field[$k]["fieldname"] = "订单总额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coupon_price";
        $field[$k]["fieldname"] = "优惠金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "order_paymentprice";
        $field[$k]["fieldname"] = "应付金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "order_paidprice";
        $field[$k]["fieldname"] = "已付金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "order_arrears";
        $field[$k]["fieldname"] = "欠费金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "order_isinvoice";
        $field[$k]["fieldname"] = "开票状态";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "dealorder_balanceprice";
        $field[$k]["fieldname"] = "账户余额变动";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "dealorder_forwardprice";
        $field[$k]["fieldname"] = "结转金额";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "tradingtype_name";
        $field[$k]["fieldname"] = "交易类型";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "order_refusereason";
        $field[$k]["fieldname"] = "拒绝原因";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "is_should_pay";
        $field[$k]["fieldname"] = "是否需要支付";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "can_cancel";
        $field[$k]["fieldname"] = "是否可以取消";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "is_should_check";
        $field[$k]["fieldname"] = "是否可以审核";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "student_id";
        $field[$k]["fieldname"] = "学员ID";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "staffer_name";
        $field[$k]["fieldname"] = "经办人";
        $field[$k]["show"] = (!isset($request['student_id']) || $request['student_id'] == '') ? 0 : 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "trading_createtime";
        $field[$k]["fieldname"] = "创建时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "family_cnname";
        $field[$k]["fieldname"] = "家长姓名";
        $field[$k]["show"] = (!isset($request['student_id']) || $request['student_id'] == '') ? 0 : 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "balance";
        $field[$k]["fieldname"] = "使用账户余额";
        $field[$k]["show"] = (!isset($request['student_id']) || $request['student_id'] == '') ? 0 : 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "forward";
        $field[$k]["fieldname"] = "使用结转金额";
        $field[$k]["show"] = (!isset($request['student_id']) || $request['student_id'] == '') ? 0 : 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "catdeposit";
        $field[$k]["fieldname"] = "使用定金金额";
        $field[$k]["show"] = (!isset($request['student_id']) || $request['student_id'] == '') ? 0 : 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();

        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
//        $result["companieslist"] = $res['companieslist'];
        if ($res) {
            $result["list"] = $res['list'];
            $result["fieldcustom"] = 0;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => '暂无订单信息', 'result' => $result);

        }
        ajax_return($res, $request['language_type']);

    }

    function getMergeOrderListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $OrderModel = new \Model\Smc\OrderModel($request);
        $res = $OrderModel->getMergeOrderList($request);
        $field = array();
        $k = 0;

        $field[$k]["fieldstring"] = "mergeorder_pid";
        $field[$k]["fieldname"] = "组合交易编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["fieldname"] = "学员中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_enname";
        $field[$k]["fieldname"] = "学员英文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_branch";
        $field[$k]["fieldname"] = "学员编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "courseNum";
        $field[$k]["fieldname"] = "涉及订单数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["ismethod"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "courseNum";
        $field[$k]["fieldname"] = "涉及主体";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "family_mobile";
        $field[$k]["fieldname"] = "下单手机号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "mergeorder_status_name";
        $field[$k]["fieldname"] = "订单状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;


        $field[$k]["fieldstring"] = "eorders_payprice";
        $field[$k]["fieldname"] = "订单金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "mergeorder_createtime";
        $field[$k]["fieldname"] = "创建时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;


        $result = array();

        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];

        if ($res) {
            $result["list"] = $res['list'];
            $result["fieldcustom"] = 0;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => '暂无订单信息', 'result' => $result);

        }
        ajax_return($res, $request['language_type']);

    }

    function orderStatusListApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $OrderModel = new \Model\Smc\OrderModel($request);
        $res = $OrderModel->orderStatusList($request);

        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $res = array('error' => 1, 'errortip' => $OrderModel->errortip, 'result' => array());
        }
        ajax_return($res, $request['language_type']);
    }

    function orderListApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $OrderModel = new \Model\Smc\OrderModel($request);
        $res = $OrderModel->orderList($request);

        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $res = array('error' => 1, 'errortip' => $OrderModel->errortip, 'result' => array());
        }
        ajax_return($res, $request['language_type']);


    }

    function refundInfoApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $OrderModel = new \Model\Smc\OrderModel($request);
        $res = $OrderModel->refundInfo($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $OrderModel->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    function orderInfoApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $OrderModel = new \Model\Smc\OrderModel($request);
        $res = $OrderModel->orderInfo($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $OrderModel->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    function orderContractListApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $OrderModel = new \Model\Smc\OrderModel($request);
        $res = $OrderModel->orderContractList($request);
        $field = array();
        $field[0]["fieldstring"] = "order_pid";
        $field[0]["fieldname"] = "订单编号";
        $field[0]["show"] = 1;
        $field[0]["custom"] = 1;

        $field[1]["fieldstring"] = "course_id";
        $field[1]["fieldname"] = "课程ID";
        $field[1]["show"] = 0;
        $field[1]["custom"] = 0;

        $field[2]["fieldstring"] = "agreement_id";
        $field[2]["fieldname"] = "协议ID";
        $field[2]["show"] = 0;
        $field[2]["custom"] = 0;

        $field[3]["fieldstring"] = "pricing_id";
        $field[3]["fieldname"] = "定价ID";
        $field[3]["show"] = 0;
        $field[3]["custom"] = 0;

        $result = array();
        $result["field"] = $field;
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无订单信息', 'result' => $result);

        }
        ajax_return($res, $request['language_type']);
    }

    function contractItemApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $OrderModel = new \Model\Smc\OrderModel($request);
        $res = $OrderModel->contractItem($request);

        if ($res) {
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $res);
        } else {
            $res = array('error' => 1, 'errortip' => $OrderModel->errortip, 'result' => $res);
        }
        ajax_return($res, $request['language_type']);
    }

    function orderItemApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $OrderModel = new \Model\Smc\OrderModel($request);
        $res = $OrderModel->orderItem($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $OrderModel->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function cancelOrderAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $OrderModel = new \Model\Smc\OrderModel($request, $request['order_pid']);
        $res = $OrderModel->cancelOrder($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $data = array();
            $data['protocol_isdel'] = '1';
            $this->DataControl->updateData("smc_student_protocol","order_pid = '{$request['order_pid']}'",$data);
            $res = array('error' => 0, 'errortip' => '取消成功', 'result' => $result);
            $this->addSmcWorkLog($request['company_id'], $request['school_id'], $request['staffer_id'], "缴费管理->缴费中心", '取消订单', dataEncode($request));
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $OrderModel->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function cancelMergeOrderAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $OrderModel = new \Model\Smc\OrderModel($request);
        $res = $OrderModel->cancelMergeOrder($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $data = array();
            $data['protocol_isdel'] = '1';
            $this->DataControl->updateData("smc_student_protocol","order_pid = '{$request['order_pid']}'",$data);
            $res = array('error' => 0, 'errortip' => '取消成功', 'result' => $result);
            $this->addSmcWorkLog($request['company_id'], $request['school_id'], $request['staffer_id'], "缴费管理->缴费中心", '取消订单', dataEncode($request));
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $OrderModel->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function schoolVerifyAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);

        $post = $this->DataControl->selectOne("select p.post_istopjob from gmc_company_post as p left join gmc_staffer_postbe as pb on pb.post_id = p.post_id where pb.postbe_id = '{$request['re_postbe_id']}'");

        if ($post['post_istopjob'] == '0') {
            ajax_return(array('error' => 1, 'errortip' => '请联系校长审核'));
        }

        $OrderModel = new \Model\Smc\OrderModel($request);
        $res = $OrderModel->schoolVerify($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '审核成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $OrderModel->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function refuseSchoolVerifyAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);

        $post = $this->DataControl->selectOne("select p.post_istopjob from gmc_company_post as p left join gmc_staffer_postbe as pb on pb.post_id = p.post_id where pb.postbe_id = '{$request['re_postbe_id']}'");

        if ($post['post_istopjob'] == '0') {
            ajax_return(array('error' => 1, 'errortip' => '请联系校长审核'));
        }

        $OrderModel = new \Model\Smc\OrderModel($request);
        $res = $OrderModel->refuseSchoolVerify($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '拒绝成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $OrderModel->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //订单详情
    function getOrderOneByTradingView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $publicArray = array();
        $order_pid = $request['order_pid'];
        $publicArray['company_id'] = $request['company_id'];
        $publicArray['school_id'] = $request['school_id'];
        $publicArray['staffer_id'] = $request['staffer_id'];
        $publicArray['token'] = $request['token'];
        $OrderModel = new \Model\Smc\OrderModel($publicArray, $order_pid);
        $dataList = $OrderModel->getOrderOneByTrading($request['trading_pid']);

        $v = $this->DataControl->getFieldOne("gmc_company", "company_isvoucher", "company_id = '{$request['company_id']}'");

        if ($dataList && is_array($dataList['list']['paylist'])) {
            foreach ($dataList['list']['paylist'] as &$val) {
                $val['voucher'] = $v['company_isvoucher'];
            }
        }

        $order_status = $this->DataControl->getFieldOne("smc_payfee_order", "order_status", "trading_pid = '{$request['trading_pid']}'");

        $a = $this->DataControl->selectOne("SELECT p.post_istopjob FROM gmc_staffer_postbe AS sp LEFT JOIN gmc_company_post AS p ON p.post_id = sp.post_id  WHERE sp.postbe_id = '{$request['re_postbe_id']}'");

        $status['create'] = '0';
        $status['update'] = '0';
        $status['verify'] = '0';

        if ($order_status['order_status'] == '1') {
            if ($a['post_istopjob'] != '1' && $request['re_postbe_id'] != '0') {
                $status['create'] = '0';
                $status['update'] = '1';
                $status['verify'] = '0';
            } else {
                $status['create'] = '1';
                $status['update'] = '1';
                $status['verify'] = '1';
            }
        } elseif ($order_status['order_status'] == '2' || $order_status['order_status'] == '4') {
            if ($a['post_istopjob'] != '1' && $request['re_postbe_id'] != '0') {
                $status['create'] = '1';
                $status['update'] = '1';
                $status['verify'] = '0';
            } else {
                $status['create'] = '1';
                $status['update'] = '1';
                $status['verify'] = '1';
            }
        }

        $dataList['status'] = $status;

        $res = array('error' => 0, 'errortip' => '获取订单详情成功', 'result' => $dataList);
        ajax_return($res, $request['language_type']);
    }

    //下载收据 --- 针对支付订单
    function downReceiptApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);

        if ($_SERVER['SERVER_NAME'] == 'gmcapi.kedingdang.com') {
            $res = array('error' => 0, 'errortip' => '该功能暂未开放', 'result' => array());
            ajax_return($res, $request['language_type']);
        }

        $publicArray = array();
        $order_pid = $request['order_pid'];
        $publicArray['company_id'] = $request['company_id'];
        $publicArray['school_id'] = $request['school_id'];
        $publicArray['staffer_id'] = $request['staffer_id'];
        $publicArray['token'] = $request['token'];
        $OrderModel = new \Model\Smc\OrderModel($publicArray, $order_pid);
        $res = $OrderModel->downReceiptApi($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '下载成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $OrderModel->errortip, 'result' => array());
        }
        ajax_return($res, $request['language_type']);
    }

    //下载收据 --- 针对合同
    function downContractReceiptApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);

        if ($_SERVER['SERVER_NAME'] == 'gmcapi.kedingdang.com') {
            $res = array('error' => 0, 'errortip' => '该功能暂未开放', 'result' => array());
            ajax_return($res, $request['language_type']);
        }

        $publicArray = array();
        $order_pid = $request['order_pid'];
        $publicArray['company_id'] = $request['company_id'];
        $publicArray['school_id'] = $request['school_id'];
        $publicArray['staffer_id'] = $request['staffer_id'];
        $publicArray['token'] = $request['token'];
        $OrderModel = new \Model\Smc\OrderModel($publicArray, $order_pid);
        $res = $OrderModel->downContractReceiptApi($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '下载成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $OrderModel->errortip, 'result' => array());
        }
        ajax_return($res, $request['language_type']);
    }

    //单独添加订单-收据下载的跟踪记录
    function addOrderProtocolLogAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);

        $OrderModel = new \Model\Smc\OrderModel($request, $request['order_pid']);
        $res = $OrderModel->orderTracks($this->LgStringSwitch('下载收据'), $this->LgStringSwitch('下载收据，合同编号' . $request['protocol_pid']));

        $result = array('error' => 0, 'errortip' => '添加成功');
        ajax_return($result, $request['language_type']);
    }

    function aView(){
        $a = $this->convert_2_cn(1800);
        var_dump($a);
    }

    function convert_2_cn($num) {
        $convert_cn = array("零","壹","贰","叁","肆","伍","陆","柒","捌","玖");
        $repair_number = array('零仟零佰零拾零','万万','零仟','零佰','零拾');
        $unit_cn = array("拾","佰","仟","万","亿");
        $exp_cn = array("","万","亿");
        $max_len = 12;
        $len = strlen($num);
        if($len > $max_len) {
            return 'outnumber';
        }
        $num = str_pad($num,12,'-',STR_PAD_LEFT);
        $exp_num = array();
        $k = 0;
        for($i=12;$i>0;$i--){
            if($i%4 == 0) {
                $k++;
            }
            $exp_num[$k][] = substr($num,$i-1,1);
        }
        $str = '';
        foreach($exp_num as $key=>$nums) {
            if(array_sum($nums)){
                $str = array_shift($exp_cn) . $str;
            }
            foreach($nums as $nk=>$nv) {
                if($nv == '-'){continue;}
                if($nk == 0) {
                    $str = $convert_cn[$nv] . $str;
                } else {
                    $str = $convert_cn[$nv].$unit_cn[$nk-1] . $str;
                }
            }
        }
        $str = str_replace($repair_number,array('万','亿','-'),$str);
        $str = preg_replace("/-{2,}/","",$str);
        $str = str_replace(array('零','-'),array('','零'),$str);
        return $str;
    }

    function convertAmountToCn($amount, $type = 1) {

        if ($amount == 0) {

            return "零元整";

        }

        if (strlen($amount) > 12) {

            return "不支持万亿及更高金额";

        }



        // 预定义中文转换的数组

        $digital = array('零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖');

        // 预定义单位转换的数组

        $position = array('仟', '佰', '拾', '亿', '仟', '佰', '拾', '万', '仟', '佰', '拾', '元');



        // 将金额的数值字符串拆分成数组

        $amountArr = explode('.', $amount);



        // 将整数位的数值字符串拆分成数组

        $integerArr = str_split($amountArr[0], 1);

        // 将整数部分替换成大写汉字

        $result = '';

        $integerArrLength = count($integerArr);

        $positionLength = count($position);

        for($i=0; $i<$integerArrLength; $i++){

            $result = $result . $digital[$integerArr[$i]]. $position[$positionLength - $integerArrLength + $i];

        }


        $result = $result . '整';
        return $result;

    }


    //订单合同列表
    function getProtocolListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\OrderModel($request);

        $result = $Model->getProtocolList($request);

        ajax_return($result, $request['language_type']);
    }


    //创建合同
    function createProtocolAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);

        $OrderModel = new \Model\Smc\OrderModel($request, $request['order_pid']);
        $res = $OrderModel->createProtocolAction($request);

        $this->addSmcWorkLog($request['company_id'], $request['school_id'], $request['staffer_id'], "缴费中心->订单详情", '生成合同', dataEncode($request));
        ajax_return($res, $request['language_type']);
    }


    //创建合同
    function createAnboAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
//        $this->ThisVerify($request);
        $request['school_id'] = '656';
        $list = $this->DataControl->selectClear("select p.order_pid from smc_student_protocol as p where p.school_id = '656' and protocol_isaudit = '0' and protocol_createtime > '1704097445' and (protocol_id = '562769')");
        if($list){
            foreach($list as &$value){
                $OrderModel = new \Model\Smc\OrderModel($request, $value['order_pid']);
                $OrderModel->createProtocolAction($request);
            }
        }

        echo '成功';
    }

    function downloadInvoiceView(){
        $request = Input('get.','','trim,addslashes');

        $invoiceOne = $this->DataControl->getOne("shop_invoice","invoice_id='{$request['invoice_id']}'");


        if($invoiceOne['invoice_pdfurl']){
            $result = $invoiceOne['invoice_pdfurl'];
            $res = array('error' => 0, 'errortip' => '下载成功', 'result' => $result);
        }else{
            $param = '{
    "invoiceSn": "' . $invoiceOne['invoice_serialnumber'] . '",
    "invoiceCode": "' . $invoiceOne['invoice_code'] . '",
    "invoiceNo": "' . $invoiceOne['invoice_number'] . '"
}';

            $getBackurl = request_by_curl("https://wxp.easyfapiao.com/v2/haizhouJiaotong/getInvoiceUrl", $param, "POST");
            $List = json_decode($getBackurl, true);

            $result = $List['body']['downloadUrl'];

            $res = array('error' => 0, 'errortip' => '下载成功', 'result' => $result);
        }

        ajax_return($res,$request['language_type']);


    }


    function getHiddenMobile($mobile, $head = 3, $tail = 4, $hide_str = '****')
    {
        $hide = mb_strlen($hide_str, 'utf-8');
        return preg_replace("/(\d{{$head}})\d{{$hide}}(\d{{$tail}})/", "$1{$hide_str}$2", $mobile);
    }

    //协议详情
    function protocolDetailView()
    {
        $request = Input('get.', '', 'trim,addslashes,strip_tags');
        $this->ThisVerify($request);//验证账户


//        if($request['school_id'] == '1175'){
//            $PartyA = array();
//            $PartyA['school_signet'] = 'https://pic.kedingdang.com/schoolmanage/202104131840x270972325.png';
//            $a = $this->DataControl->selectOne("select alltreaty,sign from temp_result_copy1 where result_id = '{$request['protocol_id']}'");
//            $guarder = array();
//            $guarder['parenter_sign'] = $a['sign'];
//
//            $result = array();
//            $result['istable'] = '0';
//            $result['PartyA'] = $PartyA;
////            $result['student_id'] = $protocol['student_id'];
////            $result['PartyB'] = $PartyB;
//            $result['guarder'] = $guarder;
////            $result['courseInfo'] = $courseInfo;
////            $result['priceInfo'] = $priceInfo;
////            $result['order'] = $order;
////            $result['date'] = date('Y-m-d', $protocol['protocol_createtime']);
//            $result['text'] = $a['alltreaty'];
////            $result['tip'] = $protocolOne['treaty_tabletip'];
////            $result['signtime'] = substr($protocol['protocol_signtime'], 0, 10);
//
//            ajax_return(array('error' => 0, 'errortip' => "获取成功", 'result' => $result));
//        }


        //甲方
        $PartyA = array();
        $protocol = $this->DataControl->getOne("smc_student_protocol", "protocol_id = '{$request['protocol_id']}'");
        $order = $this->DataControl->selectOne("select o.order_pid,o.trading_pid,o.companies_id,p.pay_pid,p.pay_typename,p.pay_price,o.order_coupon_price,o.order_paidprice,o.order_arrearageprice,o.order_paymentprice,c.ordercourse_buynums,c.ordercourse_totalprice,c.ordercourse_unitprice 
        from smc_payfee_order as o 
        left join smc_payfee_order_pay as p on o.order_pid = p.order_pid 
        left join smc_payfee_order_course as c on c.order_pid = o.order_pid where o.order_pid = '{$protocol['order_pid']}' and c.course_id='{$protocol['course_id']}'");

        $company = $this->DataControl->getFieldOne("gmc_company", "company_cnname,company_logo", "company_id = '{$request['company_id']}'");
        $school = $this->DataControl->getFieldOne("smc_school", "school_cnname,school_signet,school_address,school_phone,companies_id,school_liaison,school_examine,school_register,school_permitbranch,school_permitstday,school_permitendday,school_icp,school_licensestday,school_licenseendday,school_society,school_licensestatus", "school_id = '{$protocol['school_id']}'");
        $course = $this->DataControl->getFieldOne("smc_course", "course_cnname,course_branch,course_classnum,course_classtimes,coursetype_id,course_inclasstype,course_perhour,coursecat_id", "course_id = '{$protocol['course_id']}'");

        $companies_id = $this->DataControl->getFieldOne("smc_school_coursecat_subject","companies_id","school_id = '{$request['school_id']}' and coursecat_id = '{$course['coursecat_id']}'");

//        if ($request['staffer_id'] == '25721' && $course['coursecat_id']==133) {
//            $companies_id['companies_id']='78397';
//        }

        $companies = $this->DataControl->getFieldOne("gmc_code_companies", "companies_cnname,companies_signet,companies_liaison,companies_examine,companies_register,companies_permitbranch,companies_permitstday,companies_permitendday,companies_icp,companies_licensestday,companies_licenseendday,companies_society,companies_licensestatus,companies_supervisebank,companies_superviseaccount,companies_settlebank,companies_settleaccount,companies_permitstday,companies_licensestday", "companies_id = '{$companies_id['companies_id']}'");

        $PartyA['companies_cnname'] = $companies['companies_cnname'];
        $PartyA['companies_permitstday'] = substr($companies['companies_permitstday'],strripos($companies['companies_permitstday'],"至")+3);
        $PartyA['companies_licensestday'] =  substr($companies['companies_licensestday'],strripos($companies['companies_licensestday'],"至")+3);
        $PartyA['companies_supervisebank'] = $companies['companies_supervisebank'];
        $PartyA['companies_superviseaccount'] = $companies['companies_superviseaccount'];
        $PartyA['companies_settlebank'] = $companies['companies_settlebank'];
        $PartyA['companies_settleaccount'] = $companies['companies_settleaccount'];
        $PartyA['school_cnname'] = $school['school_cnname'];
        $PartyA['company_cnname'] = $company['company_cnname'];
        $PartyA['school_address'] = $school['school_address'];
        $PartyA['school_phone'] = $school['school_phone'];
        if($school['school_phone']){
            $PartyA['school_phone'] = $school['school_phone'];
        }else{
            $PartyA['school_phone'] = '--';
        }
        if($companies['companies_liaison']){
            $PartyA['school_liaison'] = $companies['companies_liaison'];
        }else{
            $PartyA['school_liaison'] = '--';
        }
        if($companies['companies_examine']){
            $PartyA['school_examine'] = $companies['companies_examine'];
        }else{
            $PartyA['school_examine'] = '--';
        }
        if($companies['companies_register']){
            $PartyA['school_register'] = $companies['companies_register'];
        }else{
            $PartyA['school_register'] = '--';
        }
        if($companies['companies_permitbranch']){
            $PartyA['school_permitbranch'] = $companies['companies_permitbranch'];
        }else{
            $PartyA['school_permitbranch'] = '--';
        }
        if($companies['companies_permitstday']){
            $PartyA['school_permitstday'] = $companies['companies_permitstday'];
        }else{
            $PartyA['school_permitstday'] = '--';
        }
        if($companies['companies_permitstday']){
            $PartyA['school_permitstday'] = $companies['companies_permitstday'];
        }else{
            $PartyA['school_permitstday'] = '--';
        }
        if($companies['companies_licensestday']){
            $PartyA['school_licensestday'] = $companies['companies_licensestday'];
        }else{
            $PartyA['school_licensestday'] = '--';
        }
        if($companies['companies_society']){
            $PartyA['school_society'] = $companies['companies_society'];
        }else{
            $PartyA['school_society'] = '--';
        }
        $PartyA['school_signet'] = $companies['companies_signet'];

//        var_dump($PartyA['school_signet']);
        $PartyA['school_icp'] = $companies['companies_icp'];
        $PartyA['company_logo'] = $company['company_logo'];
        $PartyA['company_shortname'] = $school['company_shortname'];

        $PartyB = array();
        $student = $this->DataControl->getFieldOne("smc_student", "student_cnname,student_enname,student_birthday,student_sex,student_branch,student_idcard", "student_id = '{$protocol['student_id']}'");
        $PartyB['student_branch'] = $student['student_branch'];
        $PartyB['student_cnname'] = $student['student_cnname'];
        $PartyB['student_sex'] = $student['student_sex'];
        $PartyB['student_birthday'] = $student['student_birthday'];
        $famliy = $this->DataControl->getFieldOne("smc_student_family", "parenter_id", "student_id = '{$protocol['student_id']}' and family_isdefault = '1'");
        $parenter = $this->DataControl->selectOne("
            SELECT
                p.parenter_id,
                p.parenter_mobile,
                p.parenter_cnname,
                p.parenter_sign,
                f.family_relation,
                f.family_cnname,
                f.family_address 
            FROM
                smc_parenter AS p
                LEFT JOIN smc_student_family AS f ON p.parenter_id = f.parenter_id
                where p.parenter_id = '{$famliy['parenter_id']}' and f.student_id = '{$protocol['student_id']}' order by f.family_isdefault desc limit 1");
//        if($request['staffer_id'] == '25721' && $request['school_id']==2270){
//            $PartyB['phone'] = $this->getHiddenMobile($parenter['parenter_mobile']);
//
//        }else{
            $PartyB['phone'] = $parenter['parenter_mobile'];

//        }


        $PartyB['schoolname'] = '--';
        $PartyB['address'] = '--';
        $PartyB['student_idcard'] = $student['student_idcard'];

        $guarder = array();
        $guarder['guardername'] = $parenter['family_cnname'];

        $guarder['guarderrelation'] = $parenter['family_relation'];
        $guarder['parenter_sign'] = $protocol['protocol_sign'];

//        if($request['staffer_id'] == '25721' && $request['school_id']==2270){
//            $guarder['guarderphone'] = $this->getHiddenMobile($parenter['parenter_mobile']);
//        }else{
            $guarder['guarderphone'] = $parenter['parenter_mobile'];
//        }

        $famliys = $this->DataControl->getFieldOne("smc_student_family", "parenter_id", "student_id = '{$protocol['student_id']}' and family_isdefault = '0'");

        if ($famliys) {
            $parenters = $this->DataControl->selectOne("
            SELECT
                p.parenter_id,
                p.parenter_mobile,
                p.parenter_cnname,
                p.parenter_sign,
                f.family_relation,
                f.family_cnname,
                f.family_address 
            FROM
                smc_parenter AS p
                LEFT JOIN smc_student_family AS f ON p.parenter_id = f.parenter_id
                where p.parenter_id = '{$famliys['parenter_id']}' and f.student_id = '{$protocol['student_id']}'");
            $guarder['urgentname'] = $parenters['family_cnname'];
            $guarder['urgentphone'] = $parenters['parenter_mobile'];
            $guarder['parenter_id'] = $parenter['parenter_id'];
            $guarder['urgentrelation'] = $parenters['family_relation'];
        } else {
            $guarder['urgentname'] = $parenter['family_cnname'];
            $guarder['urgentphone'] = $parenter['parenter_mobile'];
            $guarder['parenter_id'] = $parenter['parenter_id'];
            $guarder['urgentrelation'] = $parenter['family_relation'];
        }

//        if($request['staffer_id'] == '25721' && $request['school_id']==2270){
//            $guarder['urgentphone'] = $this->getHiddenMobile($parenter['parenter_mobile']);
//        }else{
//            $guarder['urgentphone'] = $parenter['parenter_mobile'];
//        }

//        $courseInfo = array();
        $pricing_id = $this->DataControl->selectOne("select pricing_id,class_id from smc_payfee_order_course as c where c.order_pid = '{$protocol['order_pid']}'");
        $agreement = $this->DataControl->getFieldOne("smc_fee_pricing", "agreement_id", "pricing_id = '{$pricing_id['pricing_id']}'");

//        if ($protocol['protocol_isaudit'] == '1') {
//            $protocolOne = $this->DataControl->getFieldOne("smc_student_protocol", "treaty_tabletip,treaty_protocol", "protocol_id = '{$protocol['protocol_id']}'");
//        } else {
//            if($course['coursecat_id'] == '135'){
//                $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '731' and coursetype_id = '79654' and t.treaty_applytype = '0'");
//            }else{
//                $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and t.treaty_applytype = '1' and a.school_id = '{$request['school_id']}'");
//                if(!$protocolOne){
//                    $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and t.treaty_applytype = '0'");
//                }
//            }
//        }

        if ($protocol['protocol_isaudit'] == '1') {
            $protocolOne = $this->DataControl->getFieldOne("smc_student_protocol", "treaty_tabletip,treaty_protocol,treaty_id", "protocol_id = '{$protocol['protocol_id']}'");

            $istable = $this->DataControl->getFieldOne("smc_fee_treaty","treaty_tabletip","treaty_id = '{$protocolOne['treaty_id']}'");

            if($istable['treaty_tabletip'] == null){
                $protocolStr = strip_tags($protocolOne['treaty_protocol']);
                $start = stripos($protocolStr,"甲方（提供培训方）：");
                $end = stripos($protocolStr,"机构名称");
                $dif=strlen("甲方（提供培训方）：");

            }else{
                $protocolStr = strip_tags($protocolOne['treaty_tabletip']);
                $start = stripos($protocolStr,"机构名称");
                $end = stripos($protocolStr,"学校地址");
                $dif=strlen("机构名称");
            }
            $sbstr = trim(substr($protocolStr,($start+$dif),($end - $start-$dif)));

            if($sbstr == '上海吉的堡权胜英语培训学校有限公司'){
                $sbstr = '上海吉的堡权胜培训学校有限公司';
            }


            if($sbstr){
                $sign = $this->DataControl->getFieldOne("gmc_code_companies","companies_signet","companies_cnname = '{$sbstr}'");
                if($sign){
                    $PartyA['school_signet'] = $sign['companies_signet'];
                }
            }

            

            if(!$protocolOne['treaty_protocol']){
                $isprocat = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and coursecat_id = '{$course['coursecat_id']}'");
                if($isprocat){
                    $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and coursecat_id = '{$course['coursecat_id']}' and t.treaty_applytype = '1' and a.school_id = '{$request['school_id']}'");
                    if(!$protocolOne){
                        $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and coursecat_id = '{$course['coursecat_id']}' and t.treaty_applytype = '0'");
                        if(!$protocolOne){
                            $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and coursecat_id = '0' and t.treaty_applytype = '0'");
                            if(!$protocolOne){
                                $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and coursecat_id = '0' and t.treaty_applytype = '1'  and a.school_id = '{$request['school_id']}'");
                            }
                        }
                    }
                }else{
                    $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and t.treaty_applytype = '1' and a.school_id = '{$request['school_id']}' and t.coursecat_id = '0'");
                    if(!$protocolOne){
                        $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and t.treaty_applytype = '0' and t.coursecat_id = '0'");
                    }
                }
            }


        } else {
            $isprocat = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and coursecat_id = '{$course['coursecat_id']}'");
            if($isprocat){
                $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and coursecat_id = '{$course['coursecat_id']}' and t.treaty_applytype = '1' and a.school_id = '{$request['school_id']}'");
                if(!$protocolOne){
                    $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and coursecat_id = '{$course['coursecat_id']}' and t.treaty_applytype = '0'");
                    if(!$protocolOne){
                        $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and coursecat_id = '0' and t.treaty_applytype = '0'");
                        if(!$protocolOne){
                            $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and coursecat_id = '0' and t.treaty_applytype = '1'  and a.school_id = '{$request['school_id']}'");
                        }
                    }
                }
            }else{
                $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and t.treaty_applytype = '1' and a.school_id = '{$request['school_id']}' and t.coursecat_id = '0'");
                if(!$protocolOne){
                    $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and t.treaty_applytype = '0' and t.coursecat_id = '0'");
                }
            }
        }

        $courseInfo['course_branch'] = $course['course_branch'];
        $courseInfo['course_cnname'] = $course['course_cnname'];
        $courseInfo['ordercourse_unitprice'] = $order['ordercourse_unitprice'];
        if ($order['ordercourse_buynums'] == $course['course_classnum']) {
            $courseInfo['type'] = '新班';
        } else {
            $courseInfo['type'] = '插班';
        }
        if ($course['course_inclasstype'] == '2' || $course['course_inclasstype'] == '0') {
            $courseInfo['course_classnum'] = $protocol['protocol_nums'];
        } elseif ($course['course_inclasstype'] == '1') {
            $courseInfo['course_classnum'] = '';
        } else {
            $courseInfo['course_classnum'] = $course['course_classnum'];
        }


        if ($course['course_inclasstype'] == '1') {
            $courseInfo['protocol_nums'] = '';
        } else {
            $courseInfo['protocol_nums'] = $protocol['protocol_nums'];
        }

        $courseInfo['course_classtimes'] = $course['course_perhour'] . 'h';

        $priceInfo = array();
        $priceInfo['protocol_pid'] = $protocol['protocol_pid'];
        $priceInfo['courseprice'] = '¥' . $order['ordercourse_totalprice'];
        $priceInfo['sendprice'] = '¥' . $protocol['protocol_price'];

//        if ($request['staffer_id'] == '25721'  && $course['coursecat_id']==133) {
//            if($protocol['protocol_nums']==26){
//                $priceInfo['sendprice'] = '¥' . $protocol['protocol_price']*0.64;
//                $courseInfo['protocol_nums']=$courseInfo['protocol_nums']*2;
//            }else{
//                $priceInfo['sendprice'] = '¥' . $protocol['protocol_price']*0.5;
//            }
//        }



        $track = $this->DataControl->getFieldOne("smc_payfee_order_tracks", "staffer_id", "order_pid = '{$order['order_pid']}' and tracks_title = '创建订单'");
        $agent = $this->DataControl->getFieldOne("smc_staffer", "staffer_cnname", "staffer_id = '{$track['staffer_id']}'");
        $priceInfo['adviser'] = '--';
        $priceInfo['agent'] = $agent['staffer_cnname'];
        $priceInfo['principal'] = '--';

        $priceInfo['bigprice'] = $this->convert_2_cn(intval($order['ordercourse_totalprice']));

        $startday = $this->DataControl->selectOne("SELECT
	h.changelog_day 
FROM
	smc_student_changelog AS h
WHERE
	h.student_id = '{$protocol['student_id']}' 
	AND h.class_id = '{$pricing_id['class_id']}'
	AND h.stuchange_code = 'A02'");
        $date = $startday['changelog_day'];

        $endday = date('Y-m-d',strtotime("$date +90 day"));



        if($pricing_id['class_id'] == '0'){
            $startday['changelog_day'] = '';
            $endday = '';
        }


        if($protocolOne){
            $treatyArray = array();
            $treatyArray['companies_cnname'] = $PartyA['companies_cnname'];
            $treatyArray['companies_supervisebank'] = $PartyA['companies_supervisebank'];
            $treatyArray['companies_superviseaccount'] = $PartyA['companies_superviseaccount'];
            $treatyArray['companies_settlebank'] = $PartyA['companies_settlebank'];
            $treatyArray['companies_settleaccount'] = $PartyA['companies_settleaccount'];
            $treatyArray['companies_licensestday'] = $PartyA['companies_licensestday'];
            $treatyArray['courseprice'] = $priceInfo['courseprice'];
            $treatyArray['bigprice'] = $priceInfo['bigprice'];
            $treatyArray['school_address'] = $PartyA['school_address'];
            $treatyArray['school_examine'] = $PartyA['school_examine'];
            $treatyArray['school_register'] = $PartyA['school_register'];
            $treatyArray['school_permitbranch'] = $PartyA['school_permitbranch'];
            $treatyArray['school_permitstday'] = $PartyA['school_permitstday'];
            $treatyArray['school_icp'] = $PartyA['school_icp'];
            $treatyArray['school_licensestday'] = $PartyA['school_licensestday'];
            $treatyArray['school_society'] = $PartyA['school_society'];
            $treatyArray['school_liaison'] = $PartyA['school_liaison'];
            $treatyArray['school_phone'] = $PartyA['school_phone'];
            $treatyArray['student_branch'] = $PartyB['student_branch'];
            $treatyArray['student_cnname'] = $PartyB['student_cnname'];
            $treatyArray['student_sex'] = $PartyB['student_sex'];
            $treatyArray['student_birthday'] = $PartyB['student_birthday'];
            $treatyArray['phone'] = $PartyB['phone'];
            $treatyArray['schoolname'] = $PartyB['schoolname'];
            $treatyArray['address'] = $PartyB['address'];
            $treatyArray['student_idcard'] = $PartyB['student_idcard'];
            $treatyArray['guardername'] = $guarder['guardername'];
            $treatyArray['guarderphone'] = $guarder['guarderphone'];
            $treatyArray['guarderrelation'] = $guarder['guarderrelation'];
            $treatyArray['urgentname'] = $guarder['urgentname'];
            $treatyArray['urgentphone'] = $guarder['urgentphone'];
            $treatyArray['urgentrelation'] = $guarder['urgentrelation'];
            $treatyArray['course_branch'] = $courseInfo['course_branch'];
            $treatyArray['course_cnname'] = $courseInfo['course_cnname'];
            $treatyArray['type'] = $courseInfo['type'];
            $treatyArray['protocol_nums'] = $courseInfo['protocol_nums'];
            $treatyArray['protocol_pid'] = $priceInfo['protocol_pid'];
            $treatyArray['sendprice'] = $priceInfo['sendprice'];
            $treatyArray['agent'] = $priceInfo['agent'];
            $protocolOne['treaty_tabletip'] = $this->contractTable($protocolOne['treaty_tabletip'],$treatyArray);
        }

        if($protocolOne){
            $treatyArray = array();
            $treatyArray['companies_cnname'] = $PartyA['companies_cnname'];
            $treatyArray['startday'] = $startday['changelog_day'];
            $treatyArray['endday'] = $endday;
            $treatyArray['companies_permitstday'] = $PartyA['companies_permitstday'];
            $treatyArray['companies_licensestday'] = $PartyA['companies_licensestday'];
            $treatyArray['course_classnum'] = $courseInfo['course_classnum'];
            $treatyArray['course_classtimes'] = $courseInfo['course_classtimes'];
            $treatyArray['ordercourse_unitprice'] = $courseInfo['ordercourse_unitprice'];
            $treatyArray['companies_supervisebank'] = $PartyA['companies_supervisebank'];
            $treatyArray['companies_superviseaccount'] = $PartyA['companies_superviseaccount'];
            $treatyArray['companies_settlebank'] = $PartyA['companies_settlebank'];
            $treatyArray['companies_settleaccount'] = $PartyA['companies_settleaccount'];
            $treatyArray['courseprice'] = $priceInfo['courseprice'];
            $treatyArray['bigprice'] = $priceInfo['bigprice'];
            $treatyArray['school_address'] = $PartyA['school_address'];
            $treatyArray['school_examine'] = $PartyA['school_examine'];
            $treatyArray['school_register'] = $PartyA['school_register'];
            $treatyArray['school_permitbranch'] = $PartyA['school_permitbranch'];
            $treatyArray['school_permitstday'] = $PartyA['school_permitstday'];
            $treatyArray['school_icp'] = $PartyA['school_icp'];
            $treatyArray['school_licensestday'] = $PartyA['school_licensestday'];
            $treatyArray['school_society'] = $PartyA['school_society'];
            $treatyArray['school_liaison'] = $PartyA['school_liaison'];
            $treatyArray['school_phone'] = $PartyA['school_phone'];
            $treatyArray['student_branch'] = $PartyB['student_branch'];
            $treatyArray['student_cnname'] = $PartyB['student_cnname'];
            $treatyArray['student_sex'] = $PartyB['student_sex'];
            $treatyArray['student_birthday'] = $PartyB['student_birthday'];
            $treatyArray['phone'] = $PartyB['phone'];
            $treatyArray['schoolname'] = $PartyB['schoolname'];
            $treatyArray['address'] = $PartyB['address'];
            $treatyArray['student_idcard'] = $PartyB['student_idcard'];
            $treatyArray['guardername'] = $guarder['guardername'];
            $treatyArray['guarderphone'] = $guarder['guarderphone'];
            $treatyArray['guarderrelation'] = $guarder['guarderrelation'];
            $treatyArray['urgentname'] = $guarder['urgentname'];
            $treatyArray['urgentphone'] = $guarder['urgentphone'];
            $treatyArray['urgentrelation'] = $guarder['urgentrelation'];
            $treatyArray['course_branch'] = $courseInfo['course_branch'];
            $treatyArray['course_cnname'] = $courseInfo['course_cnname'];
            $treatyArray['type'] = $courseInfo['type'];
            $treatyArray['protocol_nums'] = $courseInfo['protocol_nums'];
            $treatyArray['protocol_pid'] = $priceInfo['protocol_pid'];
            $treatyArray['sendprice'] = $priceInfo['sendprice'];
            $treatyArray['agent'] = $priceInfo['agent'];
            $protocolOne['treaty_protocol'] = $this->contractTable($protocolOne['treaty_protocol'],$treatyArray);
        }

        $result = array();
        $result['istable'] = $protocol['protocol_istable'];
        $result['PartyA'] = $PartyA;
        $result['student_id'] = $protocol['student_id'];
        $result['PartyB'] = $PartyB;
        $result['guarder'] = $guarder;
        $result['courseInfo'] = $courseInfo;
        $result['priceInfo'] = $priceInfo;
        $result['order'] = $order;
        $result['date'] = date('Y-m-d', $protocol['protocol_createtime']);
        $result['text'] = $protocolOne['treaty_protocol'];
        $result['tip'] = $protocolOne['treaty_tabletip'];
        $result['signtime'] = substr($protocol['protocol_signtime'], 0, 10);

        ajax_return(array('error' => 0, 'errortip' => "获取成功", 'result' => $result));

    }


    function contractTable($tabletip,$treatyArray){
        $tableNote = $tabletip;
        foreach($treatyArray as $key=>$treatyOne){

            $tableNote = str_replace("#".$key."#",$treatyOne,$tableNote);
        }
        return $tableNote;
    }

    //编辑合同
    function updateProtocolAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);

        $OrderModel = new \Model\Smc\OrderModel($request, $request['order_pid']);
        $res = $OrderModel->updateProtocolAction($request);
        $this->addSmcWorkLog($request['company_id'], $request['school_id'], $request['staffer_id'], "缴费管理->订单详情", '编辑合同', dataEncode($request));
        ajax_return($res, $request['language_type']);
    }

    //审核合同
    function isProtocolAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);

        $OrderModel = new \Model\Smc\OrderModel($request, $request['order_pid']);
        $res = $OrderModel->isProtocolAction($request);

        $this->addSmcWorkLog($request['company_id'], $request['school_id'], $request['staffer_id'], "缴费管理->订单详情", '审核合同', dataEncode($request));
        ajax_return($res, $request['language_type']);
    }

    //审核合同
    function istestAction()
    {
//        var_dump(1);
        $request = Input('get.', '', 'trim,addslashes');
//        $this->ThisVerify($request);
        $request['school_id'] = '656';
        $list = $this->DataControl->selectClear("select p.order_pid,p.protocol_id from smc_student_protocol as p where p.school_id = '694' and protocol_isaudit = '0' and protocol_createtime > '1704097445' limit 0,1000");

        if($list){
            foreach($list as &$value){
                $request['protocol_id'] = $value['protocol_id'];
                $OrderModel = new \Model\Smc\OrderModel($request, $value['order_pid']);
                $OrderModel->isProtocolAction($request);
            }
        }
        echo '成功';
    }

    //审核合同
    function testView()
    {
        $request = Input('post.', '', 'trim,addslashes');
//        $this->ThisVerify($request);

        $OrderModel = new \Model\Smc\OrderModel($request, $request['order_pid']);
        $res = $OrderModel->test($request);

        ajax_return($res, $request['language_type']);
    }

    function getPayInfoApi()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $OrderModel = new \Model\Smc\OrderModel($request);
        $res = $OrderModel->refuseSchoolVerify($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '拒绝成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $OrderModel->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function getTradeListApi()
    {

        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $OrderModel = new \Model\Smc\OrderModel($request);
        $res = $OrderModel->getTradeList($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $OrderModel->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function bView(){
        $a = '32132132';
        var_dump($b);
    }


    function canceldebtsPayApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $OrderModel = new \Model\Smc\OrderModel($request);
        $res = $OrderModel->canceldebtsPay($request);
        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["fieldname"] = "学员中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_enname";
        $field[$k]["fieldname"] = "学员英文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_branch";
        $field[$k]["fieldname"] = "学员编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "trading_pid";
        $field[$k]["fieldname"] = "交易编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "order_allprice";
        $field[$k]["fieldname"] = "订单总额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "order_paidprice";
        $field[$k]["fieldname"] = "已付金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "order_arrearageprice";
        $field[$k]["fieldname"] = "欠费金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "order_paymentprice";
        $field[$k]["fieldname"] = "实付金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "pay_price";
        $field[$k]["fieldname"] = "坏账处理金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "pay_note";
        $field[$k]["fieldname"] = "坏账处理原因";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "pay_issuccess_name";
        $field[$k]["fieldname"] = "订单状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "pay_createtime";
        $field[$k]["fieldname"] = "创建时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();

        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["list"] = $res['list'];
            $result["fieldcustom"] = 0;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => $OrderModel->errortip, 'result' => $result);

        }
        ajax_return($res, $request['language_type']);
    }

    //申请发票(新)
    function ApplyInvoiceView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $orderOne = $this->DataControl->getFieldOne("smc_payfee_order", "school_id,companies_id,order_paymentprice", "order_pid='{$request['order_pid']}'");
        //$companies = $this->DataControl->getFieldOne("smc_school", "companies_id", "school_id = '{$orderOne['school_id']}'");
        $invoiceOne = $this->DataControl->getFieldOne("shop_invoice", 'invoice_id,invoice_status,student_id'
            , "invoice_status > '-1' and invoice_cancel = '0' and protocol_id = '{$request['protocol_id']}'");
        if ($invoiceOne) {
            ajax_return(array('error' => 1, 'errortip' => "您已申请电子发票成功，请勿重复申请", "tokeninc" => 1));
        } else {
            $data = array();
            $data['staffer_id'] = $request['staffer_id'];
            $data['protocol_id'] = $request['protocol_id'];
            $data['student_id'] = $request['student_id'];
            $data['order_pid'] = $request['order_pid'];
            $data['school_id'] = $orderOne['school_id'];
            $data['invoice_allprice'] = $request['protocol_price'];
            $data['companies_id'] = $orderOne['companies_id'];
            $data['company_id'] = $request['company_id'];
            $data['invoice_type'] = '2';
            $data['invoice_status'] = '0';
            $data['invoice_title'] = $request['title'];
            if (isset($request['taxpayernum']) && $request['taxpayernum'] !== '') {
                $data['invoice_taxpayernum'] = $request['taxpayernum'];
            }
            $data['invoice_email'] = trim($request['email']);
            $data['invoice_createtime'] = time();
            if ($invoice_id = $this->DataControl->insertData("shop_invoice", $data)) {
                $datas = array();
                $datas['protocol_isinvoice'] = 1;
                $this->DataControl->updateData("smc_student_protocol", "protocol_id = '{$request['protocol_id']}'", $datas);

                $name = $this->DataControl->getFieldOne("smc_staffer", "staffer_cnname", "staffer_id = '{$request['staffer_id']}'");
                $datatrack = array();
                $datatrack['invoice_id'] = $invoice_id;
                $datatrack['tracks_title'] = '提交申请';
                $datatrack['tracks_information'] = '由校务老师' . $name['staffer_cnname'] . '代申请发票';
                $datatrack['staffer_id'] = $request['staffer_id'];
                $name = $this->DataControl->getFieldOne("smc_staffer", "staffer_cnname", "staffer_id = '{$request['staffer_id']}'");
                $datatrack['tracks_playname'] = $name['staffer_cnname'];
                $datatrack['tracks_time'] = time();
                $this->DataControl->insertData("shop_invoice_tracks", $datatrack);

                $Model = new \Model\Smc\OrderModel($request, $request['order_pid']);

                $protocolOne = $this->DataControl->getFieldOne("smc_student_protocol", "protocol_id,protocol_pid", "protocol_id = '{$request['protocol_id']}'");

                $Model->orderTracks('申请代开发票', '申请代开发票，合同编号' . $protocolOne['protocol_pid']);

                $this->addSmcWorkLog($request['company_id'], $request['school_id'], $request['staffer_id'], "缴费管理->发票明细", '申请发票', dataEncode($request));
                ajax_return(array('error' => 0, 'errortip' => "申请发票成功"), $request['language_type']);
            } else {
                ajax_return(array('error' => 1, 'errortip' => "申请电子发票失败,请稍后再试!"), $request['language_type']);
            }
        }
    }




    function goalActivityshowimgView()
    {
        header('Content-Type:image/png');
        $request = Input('get.', '', 'trim,addslashes');
        $codeUrl = base64_decode($request['imgurl']);//."&school_id={$request['school_id']}&typ=0"
        require(ROOT_PATH . 'Core/Classlib/Webqrcode.php');//二维码生成
        $QRcode = new \QRcode();
        $errorCorrectionLevel = 'H';//容错级别
        $matrixPointSize = 15;//生成图片大小
        echo $QRcode->png($codeUrl, false, $errorCorrectionLevel, $matrixPointSize, 2);
    }

    //合同列表
    function protocolListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $OrderModel = new \Model\Smc\OrderModel($request);
        $result = $OrderModel->protocolList($request);

        ajax_return($result, $request['language_type']);
    }



    //合同列表
    function protocolPrintView()
    {
        header('Content-Type:image/png');
        $request = Input('get.', '', 'trim,addslashes');
        $OrderModel = new \Model\Smc\OrderModel();
        $result = $OrderModel->goalActivityitemimg($request['protocol_id']);
        echo $result;
    }

    //发票列表
    function invoiceListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $OrderModel = new \Model\Smc\OrderModel($request);
        $result = $OrderModel->invoiceList($request);

        ajax_return($result, $request['language_type']);
    }

    //撤销申请
    function cancelApplyAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $OrderModel = new \Model\Smc\OrderModel($request);
        $result = $OrderModel->cancelApplyAction($request);
        $this->addSmcWorkLog($request['company_id'], $request['school_id'], $request['staffer_id'], "缴费管理->发票明细", '撤销申请', dataEncode($request));
        ajax_return($result, $request['language_type']);
    }

    //发票跟踪记录
    function invoiceTrackApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $sql = "select FROM_UNIXTIME( tracks_time, '%Y-%m-%d %H:%i:%s' ) as tracks_time,tracks_title,tracks_information from shop_invoice_tracks where invoice_id = '{$request['invoice_id']}'";
        $CompaniesList = $this->DataControl->selectClear($sql);
        if (!$CompaniesList) {
            $CompaniesList = array();
        }

        $result["list"] = $CompaniesList;
        $res = array('error' => 0, 'errortip' => '获取发票跟踪记录', 'result' => $result);

        ajax_return($res, $request['language_type']);
    }

    //支付记录
    function payListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $OrderModel = new \Model\Smc\OrderModel($request);
        $result = $OrderModel->payList($request);

        ajax_return($result, $request['language_type']);
    }

    //发票合同列表
    function getProtocolsListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $OrderModel = new \Model\Smc\OrderModel($request);
        $result = $OrderModel->getProtocolsList($request);

        ajax_return($result, $request['language_type']);
    }


    //作废合同
    function DelProtocolAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Smc\OrderModel($request, $request['order_pid']);

        $result = $Model->DelProtocolAction($request);
        ajax_return($result, $request['language_type']);
    }

    //家长签名
    function parentSignView()
    {
        $request = Input('post.', '', 'trim,addslashes,strip_tags');

        $this->ThisVerify($request);//验证账户

        $order_pid = $this->DataControl->getFieldOne("smc_student_protocol", "order_pid", "protocol_id = '{$request['protocol_id']}'");

        $data = array();
        $data['protocol_sign'] = $request['protocol_sign'];
        $data['protocol_issign'] = '1';
        $data['protocol_signip'] = real_ip();
        $data['protocol_signtime'] = date("Y-m-d H:i", time());

        if ($request['type'] == '1') {
            if ($this->DataControl->updateData("smc_student_protocol", "order_pid = '{$order_pid['order_pid']}' and protocol_issign = '0'", $data)) {
                ajax_return(array('error' => 0, 'errortip' => "家长签名成功"));
            } else {
                ajax_return(array('error' => 1, 'errortip' => "家长签名失败"));
            }
        } else {
            if ($this->DataControl->updateData("smc_student_protocol", "protocol_id = '{$request['protocol_id']}'", $data)) {
                ajax_return(array('error' => 0, 'errortip' => "家长签名成功"));
            } else {
                ajax_return(array('error' => 1, 'errortip' => "家长签名失败"));
            }
        }


    }

    function autoprotocolView(){
        $list = $this->DataControl->selectClear("SELECT protocol_id,company_id,school_id FROM `schoolmanage`.`smc_student_protocol` WHERE `company_id` = '8888' AND `protocol_isaudit` = '1' and protocol_isauto = 0 ORDER BY `protocol_id` DESC LIMIT 0,100");

        foreach ($list as $val){
            $PartyA = array();
            $protocol = $this->DataControl->getOne("smc_student_protocol", "protocol_id = '{$val['protocol_id']}'");
            $order = $this->DataControl->selectOne("select o.order_pid,o.trading_pid,o.companies_id,p.pay_pid,p.pay_typename,p.pay_price,o.order_coupon_price,o.order_paidprice,o.order_arrearageprice,o.order_paymentprice,c.ordercourse_buynums,c.ordercourse_totalprice from smc_payfee_order as o left join smc_payfee_order_pay as p on o.order_pid = p.order_pid left join smc_payfee_order_course as c on c.order_pid = o.order_pid where o.order_pid = '{$protocol['order_pid']}'");

            $company = $this->DataControl->getFieldOne("gmc_company", "company_cnname,company_logo", "company_id = '{$val['company_id']}'");
            $school = $this->DataControl->getFieldOne("smc_school", "school_cnname,school_signet,school_address,school_phone,companies_id,school_liaison,school_examine,school_register,school_permitbranch,school_permitstday,school_permitendday,school_icp,school_licensestday,school_licenseendday,school_society,school_licensestatus", "school_id = '{$protocol['school_id']}'");
            $course = $this->DataControl->getFieldOne("smc_course", "course_cnname,course_branch,course_classnum,course_classtimes,coursetype_id,course_inclasstype,course_perhour,coursecat_id", "course_id = '{$protocol['course_id']}'");
            $companies_id = $this->DataControl->getFieldOne("smc_school_coursecat_subject","companies_id","school_id = '{$val['school_id']}' and coursecat_id = '{$course['coursecat_id']}'");
            $companies = $this->DataControl->getFieldOne("gmc_code_companies", "companies_cnname,companies_signet,companies_liaison,companies_examine,companies_register,companies_permitbranch,companies_permitstday,companies_permitendday,companies_icp,companies_licensestday,companies_licenseendday,companies_society,companies_licensestatus", "companies_id = '{$companies_id['companies_id']}'");

            $PartyA['companies_cnname'] = $companies['companies_cnname'];
            $PartyA['school_cnname'] = $school['school_cnname'];
            $PartyA['company_cnname'] = $company['company_cnname'];
            $PartyA['school_address'] = $school['school_address'];
            $PartyA['school_phone'] = $school['school_phone'];
            $PartyA['school_liaison'] = $companies['companies_liaison'];
            $PartyA['school_examine'] = $companies['companies_examine'];
            $PartyA['school_register'] = $companies['companies_register'];
            $PartyA['school_permitbranch'] = $companies['companies_permitbranch'];
            $PartyA['school_permitstday'] = $companies['companies_permitstday'];
            $PartyA['school_licensestday'] = $companies['companies_licensestday'];
            $PartyA['school_society'] = $companies['companies_society'];
            $PartyA['school_signet'] = $companies['companies_signet'];
            $PartyA['school_icp'] = $companies['companies_icp'];
            $PartyA['company_logo'] = $company['company_logo'];
            $PartyA['company_shortname'] = $school['company_shortname'];

            $PartyB = array();
            $student = $this->DataControl->getFieldOne("smc_student", "student_cnname,student_enname,student_birthday,student_sex,student_branch,student_idcard", "student_id = '{$protocol['student_id']}'");
            $PartyB['student_branch'] = $student['student_branch'];
            $PartyB['student_cnname'] = $student['student_cnname'];
            $PartyB['student_sex'] = $student['student_sex'];
            $PartyB['student_birthday'] = $student['student_birthday'];
            $famliy = $this->DataControl->getFieldOne("smc_student_family", "parenter_id", "student_id = '{$protocol['student_id']}' and family_isdefault = '1'");
            $parenter = $this->DataControl->selectOne("
            SELECT
                p.parenter_mobile,
                p.parenter_cnname,
                p.parenter_sign,
                f.family_relation,
                f.family_cnname,
                f.family_address 
            FROM
                smc_parenter AS p
                LEFT JOIN smc_student_family AS f ON p.parenter_id = f.parenter_id
                where p.parenter_id = '{$famliy['parenter_id']}' and f.student_id = '{$protocol['student_id']}'");
            $PartyB['phone'] = $parenter['parenter_mobile'];
            $PartyB['schoolname'] = '--';
            $PartyB['address'] = '--';
            $PartyB['student_idcard'] = $student['student_idcard'];

            $guarder = array();
            $guarder['guardername'] = $parenter['family_cnname'];
            $guarder['guarderphone'] = $parenter['parenter_mobile'];
            $guarder['guarderrelation'] = $parenter['family_relation'];
            $guarder['parenter_sign'] = $protocol['protocol_sign'];

            $famliys = $this->DataControl->getFieldOne("smc_student_family", "parenter_id", "student_id = '{$protocol['student_id']}' and family_isdefault = '0'");

            if ($famliys) {
                $parenters = $this->DataControl->selectOne("
            SELECT
                p.parenter_mobile,
                p.parenter_cnname,
                p.parenter_sign,
                f.family_relation,
                f.family_cnname,
                f.family_address 
            FROM
                smc_parenter AS p
                LEFT JOIN smc_student_family AS f ON p.parenter_id = f.parenter_id
                where p.parenter_id = '{$famliys['parenter_id']}' and f.student_id = '{$protocol['student_id']}'");
                $guarder['urgentname'] = $parenters['family_cnname'];
                $guarder['urgentphone'] = $parenters['parenter_mobile'];
                $guarder['urgentrelation'] = $parenters['family_relation'];
            } else {
                $guarder['urgentname'] = $parenter['family_cnname'];
                $guarder['urgentphone'] = $parenter['parenter_mobile'];
                $guarder['urgentrelation'] = $parenter['family_relation'];
            }

            $courseInfo = array();
            $pricing_id = $this->DataControl->selectOne("select pricing_id from smc_payfee_order_course as c where c.order_pid = '{$protocol['order_pid']}'");
            $agreement = $this->DataControl->getFieldOne("smc_fee_pricing", "agreement_id", "pricing_id = '{$pricing_id['pricing_id']}'");

            if ($protocol['protocol_isaudit'] == '1') {
                $protocolOne = $this->DataControl->getFieldOne("smc_student_protocol", "treaty_tabletip,treaty_protocol", "protocol_id = '{$protocol['protocol_id']}'");
            } else {
                $isprocat = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and coursecat_id = '{$course['coursecat_id']}'");
                if($isprocat){
                    $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and coursecat_id = '{$course['coursecat_id']}' and t.treaty_applytype = '1' and a.school_id = '{$val['school_id']}'");
                    if(!$protocolOne){
                        $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and coursecat_id = '{$course['coursecat_id']}' and t.treaty_applytype = '0'");
                    }
                }else{
                    $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and t.treaty_applytype = '1' and a.school_id = '{$val['school_id']}'");
                    if(!$protocolOne){
                        $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and t.treaty_applytype = '0'");
                    }
                }
            }

            $courseInfo['course_branch'] = $course['course_branch'];
            $courseInfo['course_cnname'] = $course['course_cnname'];
            if ($order['ordercourse_buynums'] == $course['course_classnum']) {
                $courseInfo['type'] = '新班';
            } else {
                $courseInfo['type'] = '插班';
            }
            if ($course['course_inclasstype'] == '2') {
                $courseInfo['course_classnum'] = $protocol['protocol_nums'];
            } elseif ($course['course_inclasstype'] == '1') {
                $courseInfo['course_classnum'] = '';
            } else {
                $courseInfo['course_classnum'] = $course['course_classnum'];

            }

            if ($course['course_inclasstype'] == '1') {
                $courseInfo['protocol_nums'] = '';
            } else {
                $courseInfo['protocol_nums'] = $protocol['protocol_nums'];
            }

            $courseInfo['course_classtimes'] = $course['course_perhour'] . 'h';

            $priceInfo = array();
            $priceInfo['protocol_pid'] = $protocol['protocol_pid'];
            $priceInfo['courseprice'] = '¥' . $order['ordercourse_totalprice'];
            $priceInfo['sendprice'] = '¥' . $protocol['protocol_price'];

            $track = $this->DataControl->getFieldOne("smc_payfee_order_tracks", "staffer_id", "order_pid = '{$order['order_pid']}' and tracks_title = '创建订单'");
            $agent = $this->DataControl->getFieldOne("smc_staffer", "staffer_cnname", "staffer_id = '{$track['staffer_id']}'");
            $priceInfo['adviser'] = '--';
            $priceInfo['agent'] = $agent['staffer_cnname'];
            $priceInfo['principal'] = '--';

            if($protocolOne){
                $treatyArray = array();
                $treatyArray['companies_cnname'] = $PartyA['companies_cnname'];
                $treatyArray['school_address'] = $PartyA['school_address'];
                $treatyArray['school_examine'] = $PartyA['school_examine'];
                $treatyArray['school_register'] = $PartyA['school_register'];
                $treatyArray['school_permitbranch'] = $PartyA['school_permitbranch'];
                $treatyArray['school_permitstday'] = $PartyA['school_permitstday'];
                $treatyArray['school_icp'] = $PartyA['school_icp'];
                $treatyArray['school_licensestday'] = $PartyA['school_licensestday'];
                $treatyArray['school_society'] = $PartyA['school_society'];
                $treatyArray['school_liaison'] = $PartyA['school_liaison'];
                $treatyArray['school_phone'] = $PartyA['school_phone'];
                $treatyArray['student_branch'] = $PartyB['student_branch'];
                $treatyArray['student_cnname'] = $PartyB['student_cnname'];
                $treatyArray['student_sex'] = $PartyB['student_sex'];
                $treatyArray['student_birthday'] = $PartyB['student_birthday'];
                $treatyArray['phone'] = $PartyB['phone'];
                $treatyArray['schoolname'] = $PartyB['schoolname'];
                $treatyArray['address'] = $PartyB['address'];
                $treatyArray['student_idcard'] = $PartyB['student_idcard'];
                $treatyArray['guardername'] = $guarder['guardername'];
                $treatyArray['guarderphone'] = $guarder['guarderphone'];
                $treatyArray['guarderrelation'] = $guarder['guarderrelation'];
                $treatyArray['urgentname'] = $guarder['urgentname'];
                $treatyArray['urgentphone'] = $guarder['urgentphone'];
                $treatyArray['urgentrelation'] = $guarder['urgentrelation'];
                $treatyArray['course_branch'] = $courseInfo['course_branch'];
                $treatyArray['course_cnname'] = $courseInfo['course_cnname'];
                $treatyArray['type'] = $courseInfo['type'];
                $treatyArray['protocol_nums'] = $courseInfo['protocol_nums'];
                $treatyArray['protocol_pid'] = $priceInfo['protocol_pid'];
                $treatyArray['sendprice'] = $priceInfo['sendprice'];
                $treatyArray['agent'] = $priceInfo['agent'];
                $protocolOne['treaty_tabletip'] = $this->contractTable($protocolOne['treaty_tabletip'],$treatyArray);
            }

            $data = array();
            $data['treaty_tabletip'] = $protocolOne['treaty_tabletip'];
            $data['protocol_isauto'] = '1';
            $this->DataControl->updateData("smc_student_protocol","protocol_id = '{$val['protocol_id']}'",$data);
        }

        echo '<script language="javascript" type="text/javascript">
var i = 1;
var intervalid;
intervalid = setInterval("fun()", 100);
function fun() {
	if (i == 0) {
		window.location.href = "/Order/autoprotocol";
		clearInterval(intervalid);
	}
	document.getElementById("mes").innerHTML = i;
	i--;
}
</script>
<div id="error">
	<p>将在 <span id="mes">2</span> 秒钟后返回首页！</p>
</div> ';



    }


    //家长签名
    function isSignView()
    {
        $request = Input('get.', '', 'trim,addslashes,strip_tags');

        $this->ThisVerify($request);//验证账户

        $order_pid = $this->DataControl->getFieldOne("smc_student_protocol", "protocol_issign", "protocol_id = '{$request['protocol_id']}'");
        ajax_return(array('error' => '0', 'errortip' => "获取成功", 'result' => $order_pid['protocol_issign']));

    }


    //获取上次签名
    function getParentSignView()
    {
        $request = Input('get.', '', 'trim,addslashes,strip_tags');

        $this->ThisVerify($request);//验证账户

        $sign = $this->DataControl->getFieldOne("smc_student_protocol", "protocol_sign", "student_id = '{$request['student_id']}' and protocol_issign = '1' order by protocol_signtime DESC");

        if ($sign['protocol_sign']) {
            ajax_return(array('error' => 0, 'errortip' => "获取成功", 'sign' => $sign['protocol_sign']));
        } else {
            ajax_return(array('error' => 1, 'errortip' => "获取失败"));

        }
    }

    function getSanOrderListApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $OrderModel = new \Model\Smc\OrderModel($request);
        $res = $OrderModel->getSanOrderList($request);

        $result = array();

        $result["field"] = $res['field'];
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["list"] = $res['list'];
            $result["fieldcustom"] = 0;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => '暂无订单信息', 'result' => $result);

        }
        ajax_return($res, $request['language_type']);
    }

}

