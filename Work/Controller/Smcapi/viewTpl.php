<?php

namespace Work\Controller\Smcapi;


class viewTpl {
    public $DataControl;
    public $router;

    public function __construct(){
        global $router;
        // 指定允许其他域名访问
        header('Access-Control-Allow-Origin:*');
        // 响应类型
        header('Access-Control-Allow-Methods:*');
        // 响应头设置
        header('Access-Control-Allow-Headers:x-requested-with,content-type');

        //数据库操作
        $this->DataControl = new \Dbmysql();
        //操作类型
        $this->router = $router;
    }

    function ThisVerify($request){
        $paramArray = array();
        if(isset($request['staffer_id'])){
            $paramArray['staffer_id'] = $request['staffer_id'];
        }

        if(isset($request['school_id'])){
            $paramArray['school_id'] = $request['school_id'];
        }

        if(isset($request['company_id'])){
            $paramArray['company_id'] = $request['company_id'];
        }


        if(isset($request['class_id']) && $request['class_id']!=''){
            $paramArray['class_id'] = $request['class_id'];
        }


        if(isset($request['school_id']) && $request['school_id']!=''){
            $paramArray['school_id'] = $request['school_id'];
        }

        if(isset($request['token'])){
            $paramArray['token'] = $request['token'];
        }

        if(!$this->UserLimit($paramArray)){
            $result = array();
            $result["list"] = array();
            $result["tokeninc"] = "0";
            $res = array('error' => 1, 'errortip' => "用户token失效", 'result' => $result);
            ajax_return($res,$request['language_type']);
        }
    }

    //第三方接口权限验证
    function UserLimit($paramArray){
        $stafferOne = $this->DataControl->getFieldOne('smc_staffer',"staffer_tokencode,staffer_ismanage","staffer_id='{$paramArray['staffer_id']}'");
        if($stafferOne){

//            if($stafferOne['staffer_ismanage']!=1){
//                return false;
//            }

            if(isset($paramArray['school_id']) && $paramArray['school_id']!=''){

//                if(isset($paramArray['class_id']) && $paramArray['class_id']!=''){
//                    if(!$this->DataControl->getFieldOne("smc_class","class_id","school_id='{$paramArray['school_id']}' and class_id='{$paramArray['class_id']}'")){
//                        return false;
//                    }
//                }

                if(isset($paramArray['student_id']) && $paramArray['student_id']!=''){
                    if(!$this->DataControl->getFieldOne("smc_student_enrolled","student_id","school_id='{$paramArray['school_id']}' and student_id='{$paramArray['student_id']}'")){
                        return false;
                    }
                }
            }


            $md5tokenbar = base64_encode(md5($stafferOne["staffer_tokencode"].date("Y-m-d")));
            if($md5tokenbar != $paramArray['token']){
                return false;
            }else{
                return true;
            }
        }else{
            return false;
        }
    }

    function createOrderPid($initial){
        $Str = "ABCDEFGHIJKLMNOPQRSTUVWXYZ123456789";
        $rangtr = $Str[rand(0,34)].$Str[rand(0,34)].$Str[rand(0,34)].$Str[rand(0,34)];
        $rangtime = date("ymdHis",time());
        $rangnum = rand(10000,99999);
        $OrderPID = $initial.$rangtr.$rangtime.$rangnum;
        return $OrderPID;
    }

    function createOutPid(){
        $Str = "ABCDEFGHIJKLMNOPQRSTUVWXYZ123456789";
        $rangtr = $Str[rand(0,34)].$Str[rand(0,34)].$Str[rand(0,34)].$Str[rand(0,34)];
        $rangtime = date("ymdHis",time());
        $rangnum = rand(10000000,99999999);
        $OutPID = $rangtr.$rangtime.$rangnum;
        return $OutPID;
    }

    function getParentToken($params=array()){
        $parenterOne = $this->DataControl->getFieldOne("smc_parenter","parenter_id,parenter_tokencode,parenter_tokenencrypt","parenter_id='{$params['parenter_id']}'");
        if(!$parenterOne)
        {
            return false;
        }
        $md5tokenbar = base64_encode(md5($parenterOne["parenter_tokencode"].date("Y-m-d")));
        if($md5tokenbar == $parenterOne["parenter_tokenencrypt"]){
            $token = $parenterOne["parenter_tokenencrypt"];
        }else{
            $tokencode = rand(111111,999999);
            $md5tokenbar = base64_encode(md5($tokencode.date("Y-m-d")));
            $this->DataControl->query("UPDATE smc_parenter SET parenter_tokencode = '{$tokencode}',parenter_tokenencrypt = '{$md5tokenbar}' WHERE parenter_id ='{$parenterOne['parenter_id']}'");
            $token = $md5tokenbar;
        }
        return $token;
    }


    public function addSmcWorkLog($company_id,$school_id,$staffer_id,$module,$type,$content,$module_id=0)
	{
//    	$module = $this->DataControl->selectClear("select module_name from  imc_module where  module_id ='{$module_id}' ");
//    	$module = $this->DataControl->selectClear("select module_name from  imc_module where  module_id ='{$module_id}' ");
		
		$logData = array();
		$logData['company_id'] = $company_id;
		$logData['school_id'] = $school_id;
		$logData['staffer_id'] = $staffer_id;
		$logData['worklog_module'] = $module;
		$logData['worklog_type'] = $type;
		$logData['worklog_content'] = $content;
		$logData['worklog_ip'] = real_ip();
		$logData['worklog_time'] = time();
		$this->DataControl->insertData('smc_staffer_worklog', $logData);
	}

    //登录日志记录表
    function addStafferLoginLog($company_id,$staffer_id,$loginlog_type,$loginlog_source){
        $date = array();
        $date['company_id'] = $company_id;//集团ID
        $date['staffer_id'] = $staffer_id;//员工ID
        $date['loginlog_type'] = $loginlog_type;//登录端口 0 总入口  1 集团 2 校务 3 CRM 4 教务 5 助教 6 助学
        $date['loginlog_source'] = $loginlog_source;//客户端来源 0 PC 1 手机
        $date['loginlog_lastip'] = real_ip();
        $date['loginlog_time'] = time();
        $this->DataControl->insertData('imc_staffer_loginlog',$date);
        return true;
    }

    function getContract($company_id){
        $sql = "select sc.edition_id,ie.edition_code
              from imc_sales_contract as sc,imc_edition as ie
              where sc.edition_id=ie.edition_id and sc.company_id='{$company_id}' and sc.contract_starttime<=CURDATE() and sc.contract_endtime>=CURDATE() 
              order by sc.contract_createtime desc,sc.contract_id asc limit 0,1";

        $contractOne = $this->DataControl->selectOne($sql);

        return $contractOne;
    }

    public function LgArraySwitch($cnarray){
        if($this->companyOne['company_language'] == 'tw'){
            $Model = new \Model\jianfanModel();
            $dataString = $Model->gb2312_big5(json_encode($cnarray, JSON_UNESCAPED_UNICODE));
            $cnarray = json_decode($dataString, true);
            return $cnarray;
        }else{
            return $cnarray;
        }
    }
    public function LgStringSwitch($cnstring){
        if($this->companyOne['company_language'] == 'tw'){
            $Model = new \Model\jianfanModel();
            $cnstring = $Model->gb2312_big5($cnstring);
            return $cnstring;
        }else{
            return $cnstring;
        }
    }

    function getSchoolCourseCompanies($school_id, $coursecat_id = 0, $course_id = 0,$coursepacks_id=0)
    {
        if ($coursecat_id == 0 && $course_id == 0) {
            return false;
        }
        $datawhere = "c.coursecat_id = s.coursecat_id AND s.school_id = '{$school_id}'";
        if ($course_id != 0) {
            $datawhere .= " AND c.course_id = '{$course_id}'";
        }
        if ($coursecat_id != 0) {
            $datawhere .= " AND s.coursecat_id = '{$coursecat_id}'";
        }

        $companiesOne = $this->DataControl->selectOne("SELECT if('{$coursepacks_id}'>0 and s.merge_companies_id>0,s.merge_companies_id,s.companies_id) as companies_id FROM smc_school_coursecat_subject AS s, smc_course AS c WHERE {$datawhere} limit 0,1");


        if ($companiesOne) {
            return $companiesOne;
        } else {
            $companiesOne = $this->DataControl->selectOne("SELECT s.companies_id FROM smc_school AS s WHERE s.school_id = '{$school_id}' limit 0,1");
            return $companiesOne;
        }
    }



    public function __call($method, $args) {
        echo "unknown method " . $method;
        return false;

    }
}
