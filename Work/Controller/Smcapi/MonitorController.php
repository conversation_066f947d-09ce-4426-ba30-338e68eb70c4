<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/13
 * Time: 23:25
 */

namespace Work\Controller\Smcapi;


class MonitorController extends viewTpl
{
    public $u;
    public $t;
    public $c;
    public $visitType = "api";
    public $DataControl;
    public $Model;

    //预加载处理类
    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();

        //数据库操作
        $this->DataControl = new \Dbmysql();

    }

    //999024->监控->监控位置管理列表
    function getMonitorPositionListView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        //model
        $Model = new \Model\Smc\MonitorModel($request);
        $dataList = $Model->getMonitorPositionList($request);

        $key = 0;
        $field = array();
        $field[$key]["fieldname"] = "position_id";
        $field[$key]["fieldstring"] = "位置ID";
        $field[$key]["show"] = 0;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "company_id";
        $field[$key]["fieldstring"] = "集团ID";
        $field[$key]["show"] = 0;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "position_name";
        $field[$key]["fieldstring"] = "位置名称";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "position_desc";
        $field[$key]["fieldstring"] = "位置描述";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $result = array();
        $result['field'] = $field;
        $result['data'] = $Model->result;

        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $result));
    }

    //999024->监控->学校位置 设备实时预览 -- 97
    function getTimelyPositionVideoView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        //model
        $Model = new \Model\Smc\MonitorModel($request);
        $dataList = $Model->getTimelyPositionVideo($request);

        $key = 0;
        $field = array();
        $field[$key]["fieldname"] = "device_deviceserial";
        $field[$key]["fieldstring"] = "设备序列号（学校唯一的 监控交换机 码）";
        $field[$key]["show"] = 0;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "device_channelid";
        $field[$key]["fieldstring"] = "设备通道id";
        $field[$key]["show"] = 0;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "device_channelname";
        $field[$key]["fieldstring"] = "设备名称(通道名称)";
        $field[$key]["show"] = 0;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "device_channelno";
        $field[$key]["fieldstring"] = "通道号";
        $field[$key]["show"] = 0;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "device_remarks";
        $field[$key]["fieldstring"] = "备注";
        $field[$key]["show"] = 0;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "hls";
        $field[$key]["fieldstring"] = "HLS流畅标准流预览地址";
        $field[$key]["show"] = 0;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "hlsHd";
        $field[$key]["fieldstring"] = "HLS高清标准流预览地址";
        $field[$key]["show"] = 0;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "rtmp";
        $field[$key]["fieldstring"] = "RTMP流畅标准流预览地址";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "rtmpHd";
        $field[$key]["fieldstring"] = "RTMP高清标准流预览地址";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "definition";
        $field[$key]["fieldstring"] = "清晰度";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "playbacktype";
        $field[$key]["fieldstring"] = "播放类型";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $result = array();
        $result['field'] = $field;
        $result['data'] = $Model->result;

        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $result));
    }

    //999024->监控->获取视频取流时需要的认证信息
    function getVideoAuthenInfoApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        //model
        $Model = new \Model\Smc\MonitorModel($request);
        $dataList = $Model->getVideoAuthenInfoApi($request);

        $field = array();
        $field['appKey'] = '取流认证信息';
        $field['token'] = '取流认证信息，token有效期为7天，默认1天刷新1次，无需每次请求都获取，定时刷新即可';

        $result = array();
        $result['field'] = $field;
        $result['data'] = $Model->result;

        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $result));
    }


    //结尾魔术函数
    function __destruct()
    {

    }
}
