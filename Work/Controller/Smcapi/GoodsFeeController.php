<?php


namespace Work\Controller\Smcapi;


class GoodsFeeController extends viewTpl
{
    public $u;
    public $t;
    public $c;

    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
    }

    function GoodsListApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\GoodsFeeModel($request);
        $list = $Model->goodsList($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "goods_id";
        $field[$k]["fieldname"] = "ID";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "goods_cnname";
        $field[$k]["fieldname"] = "名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "goods_pid";
        $field[$k]["fieldname"] = "编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "purchase_quantity";
        $field[$k]["fieldname"] = "购买数量";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "goods_vipprice";
        $field[$k]["fieldname"] = "单价";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "goods_unit";
        $field[$k]["fieldname"] = "单位";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "goods_repertory";
        $field[$k]["fieldname"] = "库存";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "prodtype_name";
        $field[$k]["fieldname"] = "类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "companies_id";
        $field[$k]["fieldname"] = "主体id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "companies_cnname";
        $field[$k]["fieldname"] = "主体名称";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $list['allnum'];
        if ($list) {
            $result["fieldcustom"] = 1;
            $result["list"] = $list['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => '您还没有添加收费信息', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function getItemListApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\GoodsFeeModel($request);
        $res = $Model->getItemList($request);

        $field = array();
        $field[0]["fieldstring"] = "feeitem_id";
        $field[0]["fieldname"] = "收费项目ID";
        $field[0]["show"] = 1;
        $field[0]["custom"] = 0;

        $field[1]["fieldstring"] = "feeitem_branch";
        $field[1]["fieldname"] = "收费项目编号";
        $field[1]["show"] = 1;
        $field[1]["custom"] = 0;

        $field[2]["fieldstring"] = "feeitem_cnname";
        $field[2]["fieldname"] = "收费项目名称";
        $field[2]["show"] = 1;
        $field[2]["custom"] = 0;

        $field[3]["fieldstring"] = "feeitem_class";
        $field[3]["fieldname"] = "杂费类型";
        $field[3]["show"] = 1;
        $field[3]["custom"] = 0;

        $field[4]["fieldstring"] = "feeitem_unit";
        $field[4]["fieldname"] = "销售单位";
        $field[4]["show"] = 1;
        $field[4]["custom"] = 0;

        $field[5]["fieldstring"] = "feeitem_remk";
        $field[5]["fieldname"] = "收费项目备注";
        $field[5]["show"] = 1;
        $field[5]["custom"] = 0;

        $field[6]["fieldstring"] = "companies_id";
        $field[6]["fieldname"] = "主体id";
        $field[6]["show"] = 0;
        $field[6]["custom"] = 0;

        $field[7]["fieldstring"] = "companies_cnname";
        $field[7]["fieldname"] = "主体名称";
        $field[7]["show"] = 0;
        $field[7]["custom"] = 0;

        $result = array();
        $result["field"] = $field;
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function getCourseItemListApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\GoodsFeeModel($request);
        $result = $Model->getCourseItemList($request);

        if ($result) {
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function buyGoodsAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\GoodsFeeModel($request);
        $res = $Model->buyGoods($request);
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '生成成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);

        }
        ajax_return($res, $request['language_type']);
    }

}
