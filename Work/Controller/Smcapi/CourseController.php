<?php


namespace Work\Controller\Smcapi;


use function PHPSTORM_META\elementType;

class CourseController extends viewTpl
{
    public $u;
    public $t;
    public $c;

    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
    }

    function ThisVerify($request)
    {
        $paramArray = array();
        $paramArray['staffer_id'] = $request['staffer_id'];
        $paramArray['school_id'] = $request['school_id'];
        $paramArray['company_id'] = $request['company_id'];
        $paramArray['token'] = $request['token'];
        if (!$this->UserLimit($paramArray)) {
            $result = array();
            $result["list"] = array();
            $result["tokeninc"] = "0";
            $res = array('error' => '1', 'errortip' => "用户token失效", 'result' => $result);
            ajax_return($res, $request['language_type']);
        }
    }


    //课程明细列表页
    function homeView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        if (!$request['school_id']) {
            $res = array('error' => '1', 'errortip' => '学校id错误', 'result' => array());
            ajax_return($res, $request['language_type']);
        }

        $ClassModel = new \Model\Smc\CourseModel($request);
        $res = $ClassModel->courseList($request);

        $fieldstring = array('hour_id', 'class_id', 'class_cnname', 'class_enname', 'class_branch', 'hour_way', 'course_cnname', 'course_branch', 'staffer', 'time', 'classroom_cnname', 'status', 'hour_ischecking_name');
        $fieldname = array('课时ID', '班级id', '班级名称', '班级别名', '班级编号', '上课方式', '课程别名称', '课程别编号', '教师', '上课时间', '上课教室', '已上/计划课次', '是否考勤');
        $fieldcustom = array('0', '0', "1", "1", "1", '1', "1", "1", "1", "1", "1", "1", '1');
        $fieldshow = array('0', '0', "1", "1", "1", '1', "1", "1", "1", "1", "1", "1", '1');
        $ismethod = array('0', '0', "1", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["fieldname"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
            $field[$i]["ismethod"] = trim($ismethod[$i]);
        }

        $result = array();
        if ($res['rel_courseList']) {


            $result["field"] = $field;
            $result["list"] = $res['rel_courseList'];
            $result["allnum"] = $res['allnum'];

            $res = array('error' => '0', 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["field"] = $field;
            $result["list"] = array();
            $result["allnum"] = 0;

            $res = array('error' => '1', 'errortip' => '暂无课程明细', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    //上课记录-列表页
    function courseClassRecordApi()
    {

        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        if (!$request['school_id']) {
            $res = array('error' => '1', 'errortip' => '获取信息', 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        $ClassModel = new \Model\Smc\CourseModel($request);
        $res = $ClassModel->courseClassRecord($request);

        $fieldstring = array('hour_id', 'class_id', 'hour_day', 'class_cnname', 'class_enname', 'class_branch', 'hour_way_name', 'course_cnname', 'course_branch', 'staffer', 'status', 'check_status', 'course_inclasstype');
        $fieldname = array('课时ID', '班级id', '上课时间', '班级名称', '班级别名', '班级编号', "上课方式", '课程别名称', '课程别编号', '教师', "实到/应到", "缺勤", "班级类型");
        $fieldcustom = array('0', '0', "1", "1", '1', '1', "1", "1", "1", "1", "1", "1", "0");
        $fieldshow = array('0', '0', "1", "1", '1', '1', "1", "1", "1", "1", "1", "1", "0");
        $ismethod = array('0', '0', "0", "1", "0", "0", "0", "0", "0", "0", "0", "0", "0");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["fieldname"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);

        }

        $result = array();
        $result["field"] = $field;
        if ($res['rel_recordList']) {

            $result["list"] = $res['rel_recordList'];
            $result["allnum"] = $res['allnum'];

            $res = array('error' => '0', 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => '1', 'errortip' => '暂无上课记录', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

//	学员的上课记录
    function padstudRecordApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        if (!$request['school_id']) {
            $res = array('error' => '1', 'errortip' => '获取信息', 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        $ClassModel = new \Model\Smc\CourseModel($request);
        $res = $ClassModel->padstudRecord($request);

        $fieldstring = array('hour_id', 'class_id', 'hour_day', 'student_cnname', 'student_enname', 'student_branch', 'family_mobile', 'class_cnname', 'class_enname', 'course_cnname', 'course_branch', 'hour_isfree', 'staffer_cnname', 'hourstudy_checkinname', 'clockinginlog_note', 'hourstudy_checkin', 'type');
        $fieldname = array('课时ID', '班级id', '上课时间', '学员中文名', '学员英文名', '学员编号', '联系手机', '班级名称', '班级别名', '课程别名称', '课程别编号', '是否计费', "教师", '考勤', "缺勤原因", '缺勤状态');
        $fieldcustom = array('0', '0', '1', '1', '1', "1", "1", '1', "1", "1", "1", "1", "1", "1", "0", '0', '0');
        $fieldshow = array('0', '0', '1', '1', '1', "1", "1", '1', "1", "1", "1", "1", "1", "1", "0", '0', '0');

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["fieldname"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result["field"] = $field;
        if ($res['list']) {

            $result["list"] = $res['list'];
            $result["allnum"] = $res['allnum'];

            $res = array('error' => '0', 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;

            $res = array('error' => '1', 'errortip' => '暂无学员上课记录', 'result' => $result);

            $res = array('error' => '0', 'errortip' => '获取信息', 'result' => $result);

        }
        ajax_return($res, $request['language_type']);

    }

    //上课_试听记录
    function courseAuditionRecordApi()
    {

        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ClassModel = new \Model\Smc\CourseModel($request);
        $res = $ClassModel->courseAuditionRecord($request);


        $field = array();
        $field[0]["fieldname"] = "试听ID";
        $field[0]["fieldstring"] = "audition_id";
        $field[0]["show"] = 0;
        $field[0]["custom"] = 0;

        $field[1]["fieldname"] = "试听时间";
        $field[1]["fieldstring"] = "hour_day";
        $field[1]["show"] = 1;
        $field[1]["custom"] = 0;

        $field[2]["fieldname"] = "中文名";
        $field[2]["fieldstring"] = "audition_cnname";
        $field[2]["show"] = 1;
        $field[2]["custom"] = 0;

        $field[3]["fieldname"] = "英文名";
        $field[3]["fieldstring"] = "audition_enname";
        $field[3]["show"] = 1;
        $field[3]["custom"] = 0;

        $field[4]["fieldname"] = "班级名称";
        $field[4]["fieldstring"] = "class_cnname";
        $field[4]["show"] = 1;
        $field[4]["custom"] = 0;

        $field[5]["fieldname"] = "班级编号";
        $field[5]["fieldstring"] = "class_branch";
        $field[5]["show"] = 1;
        $field[5]["custom"] = 0;

        $field[6]["fieldname"] = "课程别名称";
        $field[6]["fieldstring"] = "course_cnname";
        $field[6]["show"] = 1;
        $field[6]["custom"] = 0;

        $field[7]["fieldname"] = "课程别编号";
        $field[7]["fieldstring"] = "course_branch";
        $field[7]["show"] = 1;
        $field[7]["custom"] = 0;

        $field[8]["fieldname"] = "上课教师";
        $field[8]["fieldstring"] = "staffer_cnname";
        $field[8]["show"] = 1;
        $field[8]["custom"] = 0;

        $field[9]["fieldname"] = "是否转正";
        $field[9]["fieldstring"] = "is_formal";
        $field[9]["show"] = 1;
        $field[9]["custom"] = 0;

        $field[10]["fieldname"] = "是否试听";
        $field[10]["fieldstring"] = "audition_isvisit";
        $field[10]["show"] = 1;
        $field[10]["custom"] = 0;

        $field[11]["fieldname"] = "上课方式";
        $field[11]["fieldstring"] = "hour_way_name";
        $field[11]["show"] = 1;
        $field[11]["custom"] = 0;

        $result = array();
        $result["field"] = $field;
        if ($res['rel_auditionList']) {

            $result["list"] = $res['rel_auditionList'];
            $result["allnum"] = $res['num'];
            $res = array('error' => '0', 'errortip' => '获取成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => '1', 'errortip' => '暂无学员试听记录', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    //获取教室
    function classroomApi()
    {
        $request = Input('get.', '', 'trim,addslashes');

        if (!$request['school_id']) {
            $res = array('error' => '1', 'errortip' => 'school_id缺失', 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        $this->ThisVerify($request);

        $CourseModel = new \Model\Smc\CourseModel($request);
        $dataList = $CourseModel->getClassroom($request);
        $field = array();
        $field[0]["fieldname"] = "教室id";
        $field[0]["fieldstring"] = "classroom_id";
        $field[1]["fieldname"] = "教室名字";
        $field[1]["fieldstring"] = "classroom_cnname";

        $result['field'] = $field;
        $result['list'] = $dataList;

        if (!$dataList) {
            $res = array('error' => '1', 'errortip' => '暂无教室信息', 'result' => $result);
        } else {
            $res = array('error' => '0', 'errortip' => '获取信息', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //点名上课列表
    function rollCallClassApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $CourseModel = new \Model\Smc\CourseModel($request);
        $res = $CourseModel->rollCallClass($request);

        $fieldstring = array('key_num', 'hourstudy_id', 'hour_id', 'student_id', 'student_branch', 'student_cnname', 'student_enname', 'family_mobile', 'fee', 'isbooking_name', 'student_type', 'stuchecktype_code', 'hour_checkinnum', 'clockinginlog_note', 'type');
        $fieldname = array('序号', '记录id', '课时id', '学员id', '学员编号', '学员中文名', '学员英文名', '联系电话', '是否计费', '是否预约', '学员类型', '出勤状态', '缺勤统计', '缺勤原因', '记录类型');
        $fieldcustom = array('1', '0', '0', "0", "1", "1", "1", "1", "1", "1", "1", '1', "1", '1', '0');
        $fieldshow = array('1', '0', '0', "0", "1", "1", "1", "1", "1", "1", "1", '1', "1", '1', '0');
        $fieldred = array('0', '0', '0', "0", "0", "0", "0", "0", "1", "0", "0", '0', "0", '1', '0');

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["fieldname"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
            $field[$i]["is_red"] = trim($fieldred[$i]);
            if ($field[$i]["fieldstring"] == 'stuchecktype_code') {
                $field[$i]["is_status"] = 1;
            }
            if ($field[$i]["fieldstring"] == 'clockinginlog_note') {
                $field[$i]["is_reason"] = 0;
            }
            if ($field[$i]["fieldstring"] == 'fee') {
                $field[$i]["is_red"] = 1;
            }
        }

        $result = array();
        $result["field"] = $field;
        if ($res['student']) {
            $result["list"] = $res['student'];
            $result["class"] = $res['class'];
            $result["is_checkin"] = $res['is_checkin'];
            $res = array('error' => '0', 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["class"] = $res['class'];
            $result["is_checkin"] = $res['is_checkin'];
            $res = array('error' => $CourseModel->error, 'errortip' => $CourseModel->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    //预约类添加 预约学员列表
    function inClassStudentApi()
    {

        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $CourseModel = new \Model\Smc\CourseModel($request);
        $list = $CourseModel->inClassStudentList($request);
        $fieldstring = array('student_cnname', 'student_enname', 'student_branch', 'student_sex', 'booking_name', 'coursebalance_time');
        $fieldname = array('学员中文名', '学员英文名', '编号', '性别', '是否预约', '剩余课次');
        $fieldcustom = array("1", "1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["fieldname"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result["field"] = $field;
        $result["list"] = $list;
        if ($list) {
            $error = '0';
            $errortip = '获取成功';
        } else {
            $error = '1';
            $errortip = '暂无需要点名上课的学员信息';
        }

        $res = array('error' => $error, 'errortip' => $errortip, 'result' => $result);
        ajax_return($res, $request['language_type']);

    }


    // 点名上课 出勤 缺勤 //考勤
    function setCheckenApi()
    {

        $request = Input('post.', '', 'trim,addslashes');
//		if($request['company_id'] <> 1001){
//			$res = array('error' =>  1, 'errortip' => "程序正在调试", 'result' => array());
//			ajax_return($res,$request['language_type']);
//		}

        $this->ThisVerify($request);

        if (!$request['class_id']) {
            $res = array('error' => 1, 'errortip' => "班级id缺失", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        if (!$request['hour_id']) {
            $res = array('error' => 1, 'errortip' => "课时id缺失", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        $CourseModel = new \Model\Smc\CheckingModel($request);
        $bool = $CourseModel->setClassChecken($request);

        $res = array('error' => $CourseModel->error, 'errortip' => $CourseModel->errortip, 'result' => array());
        $this->addSmcWorkLog($request['company_id'], $request['school_id'], $request['staffer_id'], "课务管理->课程明细->点名", '点名', dataEncode($request));

        ajax_return($res, $request['language_type']);
    }

    //获取课程详情-class班级列表
    function classCourseOneApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);

        $CourseModel = new \Model\Smc\CourseModel($request);
        $hourOne = $CourseModel->getCourseOne($request['hour_id']);

        if ($hourOne) {
            $classConflict = $CourseModel->classCourseConflict($hourOne['hour_day'], $request['school_id'], $request['hour_id'], $hourOne['staffer_id'], $hourOne['classroom_id']);

            if (count($classConflict) < 2) {
                $classConflict = array();
            } else {
                $hourOne['hour_ischecking_status'] = -2;
            }

        } else {
            $classConflict = array();
        }
        $result = array();

        $fieldstring = array('hour_id', 'course_cnname', 'course_branch', 'classroom_cnname', 'coursecat_cnname', 'staffer_cnname', 'class_cnname', 'hour_day', 'hour_time', 'hour_endtime', 'hour_ischecking', 'class_id');
        $fieldname = array('课时id', '课程名', '课程别编号', '教室', '课程别名称', '教师', '班级名称', '上课时间', '上课开始时间', '上课结束时间', '状态', '班级id');
        $fieldcustom = array('0', "1", "1", "1", "1", "1", "1", "1", "1", "1", '1', '1', '0');
        $fieldshow = array('0', "1", "1", "1", "1", "1", "1", "1", "1", "1", '1', '1', '0');
        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field['hour'][$i]["fieldstring"] = trim($fieldstring[$i]);
            $field['hour'][$i]["fieldname"] = trim($fieldname[$i]);
            $field['hour'][$i]["custom"] = trim($fieldcustom[$i]);
            $field['hour'][$i]["show"] = trim($fieldshow[$i]);

        }
        $fieldstring = array('hour_id', 'course_branch', 'class_cnname', 'classroom_cnname', 'course_cnname', 'staffer_cnname', 'hour_time');
        $fieldname = array('课时id', '课程别编号', '班级名称', '教室', '课程别名称', '教师', '时间');
        $fieldcustom = array('0', "0", "1", "1", "1", "1", "1", "1", "1");
        $fieldshow = array('0', "0", "1", "1", "1", "1", "1", "1", "1");

        for ($i = 0; $i < count($fieldstring); $i++) {
            $field['conflict'][$i]["fieldstring"] = trim($fieldstring[$i]);
            $field['conflict'][$i]["fieldname"] = trim($fieldname[$i]);
            $field['conflict'][$i]["custom"] = trim($fieldcustom[$i]);
            $field['conflict'][$i]["show"] = trim($fieldshow[$i]);

        }

        $result["field"] = $field;
        $result["list"]['hour'] = $hourOne;
        $result["list"]['conflict'] = $classConflict;

        $res = array('error' => '0', 'errortip' => '获取信息', 'result' => $result);
        if (!$classConflict) {
            $res = array('error' => '1', 'errortip' => '暂无冲突记录', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //获取课程详情 -teacher 教师课程表
    function teacherCourseOneApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);

        $CourseModel = new \Model\Smc\CourseModel($request);
        $hourOne = $CourseModel->getCourseOne($request['hour_id']);
        if ($hourOne) {
            $classConflict = $CourseModel->classCourseConflict($hourOne['hour_day'], $request['school_id'], $request['hour_id'], $hourOne['staffer_id'], $hourOne['classroom_id']);

            if (!$classConflict) {
                $classConflict = array();
            }
            $hourOne['hour_ischecking'] = -2;
        } else {
            $classConflict = array();
        }
        $result = array();

        $fieldstring = array('hour_id', 'course_cnname', 'course_branch', 'classroom_cnname', 'staffer_cnname', 'class_cnname', 'hour_day', 'hour_starttime', 'hour_endtime', 'hour_ischecking', 'class_id');
        $fieldname = array('课时id', '课程名', '课程别', '教室', '教师', '班级名称', '上课时间', '上课开始时间', '上课结束时间', '班级id');
        $fieldcustom = array('0', "1", "1", "1", "1", "1", "1", "1", "1", '1', '0');
        $fieldshow = array('0', "1", "1", "1", "1", "1", "1", "1", "1", '1', '0');
        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field['hour'][$i]["fieldstring"] = trim($fieldstring[$i]);
            $field['hour'][$i]["fieldname"] = trim($fieldname[$i]);
            $field['hour'][$i]["custom"] = trim($fieldcustom[$i]);
            $field['hour'][$i]["show"] = trim($fieldshow[$i]);

        }
        $fieldstring = array('hour_id', 'course_branch', 'class_cnname', 'classroom_cnname', 'course_cnname', 'staffer_cnname', 'hour_time');
        $fieldname = array('课时id', '课程别', '班级名称', '教室', '课程名', '教师', '时间');
        $fieldcustom = array('0', "0", "1", "1", "1", "1", "1", "1", "1");
        $fieldshow = array('0', "0", "1", "1", "1", "1", "1", "1", "1");


        for ($i = 0; $i < count($fieldstring); $i++) {
            $field['conflict'][$i]["fieldstring"] = trim($fieldstring[$i]);
            $field['conflict'][$i]["fieldname"] = trim($fieldname[$i]);
            $field['conflict'][$i]["custom"] = trim($fieldcustom[$i]);
            $field['conflict'][$i]["show"] = trim($fieldshow[$i]);

        }

        $result["field"] = $field;
        $result["list"]['hour'] = $hourOne;
        $result["list"]['conflict'] = $classConflict;

        $res = array('error' => '0', 'errortip' => '获取信息', 'result' => $result);
        ajax_return($res, $request['language_type']);
    }

    //获取教教室课程详情  -classroom
    function classroomCourseOneApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);

        $CourseModel = new \Model\Smc\CourseModel($request);
        $hourOne = $CourseModel->getCourseOne($request['hour_id']);
        if ($hourOne) {
            $classConflict = $CourseModel->classCourseConflict($hourOne['hour_day'], $request['school_id'], $request['hour_id'], $hourOne['staffer_id'], $hourOne['classroom_id']);

            if (!$classConflict) {
                $classConflict = array();
            }
            $hourOne['hour_ischecking'] = -2;

        } else {
            $classConflict = array();
        }
        $result = array();

        $fieldstring = array('hour_id', 'course_cnname', 'course_branch', 'classroom_cnname', 'staffer_cnname', 'class_cnname', 'hour_day', 'hour_starttime', 'hour_endtime', 'hour_ischecking', 'class_id');
        $fieldname = array('课时id', '课程名', '课程别', '教室', '教师', '班级名称', '上课时间', '上课开始时间', '上课结束时间', '班级id');
        $fieldcustom = array('0', "1", "1", "1", "1", "1", "1", "1", "1", '1', '0');
        $fieldshow = array('0', "1", "1", "1", "1", "1", "1", "1", "1", '1', '0');
        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field['hour'][$i]["fieldstring"] = trim($fieldstring[$i]);
            $field['hour'][$i]["fieldname"] = trim($fieldname[$i]);
            $field['hour'][$i]["custom"] = trim($fieldcustom[$i]);
            $field['hour'][$i]["show"] = trim($fieldshow[$i]);

        }
        $fieldstring = array('hour_id', 'course_branch', 'class_cnname', 'classroom_cnname', 'course_cnname', 'staffer_cnname', 'hour_time');
        $fieldname = array('课时id', '课程别', '班级名称', '教室', '课程名', '教师', '时间');
        $fieldcustom = array('0', "0", "1", "1", "1", "1", "1", "1", "1");
        $fieldshow = array('0', "0", "1", "1", "1", "1", "1", "1", "1");


        for ($i = 0; $i < count($fieldstring); $i++) {
            $field['conflict'][$i]["fieldstring"] = trim($fieldstring[$i]);
            $field['conflict'][$i]["fieldname"] = trim($fieldname[$i]);
            $field['conflict'][$i]["custom"] = trim($fieldcustom[$i]);
            $field['conflict'][$i]["show"] = trim($fieldshow[$i]);

        }

        $result["field"] = $field;
        $result["list"]['hour'] = $hourOne;
        $result["list"]['conflict'] = $classConflict;

        $res = array('error' => '0', 'errortip' => '获取信息', 'result' => $result);
        ajax_return($res, $request['language_type']);
    }

    //取消上课页面
    function viewCancelClassApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);

        $CourseModel = new \Model\Smc\CourseModel($request);
        $data = $CourseModel->viewCancel($request);
        $fieldstring = array('class_cnname', 'hour_day', 'hour_starttime', 'hour_endtime', 'week_day');
        $fieldname = array('班级名称', '上课日期', '上课开始时间', '上课结束时间', '星期',);


        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["fieldname"] = trim($fieldname[$i]);

        }

        $result['field'] = $field;
        if ($data) {
            $result['data'] = $data;
        } else {
            $result['data'] = array();
        }

        $res = array('error' => '1', 'errortip' => '获取成功', 'result' => $result);
        ajax_return($res, $request['language_type']);
    }

    //获取取消上课原因
    function getCancelReasonApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);


        $CourseModel = new \Model\Smc\CourseModel($request);
        $datalsit = $CourseModel->getCancelReason($request);

        $field = array();
        $field[0]["fieldstring"] = "reason_id";
        $field[0]["fieldname"] = "id";
        $field[0]["custom"] = 0;
        $field[0]["show"] = 0;

        $field[1]["fieldstring"] = "reason_content";
        $field[1]["fieldname"] = "取消原因";
        $field[1]["custom"] = 0;
        $field[1]["show"] = 0;


        $result['field'] = $field;

        $result['list'] = $datalsit;

        $res = array('error' => '0', 'errortip' => '获取成功', 'result' => $result);

        ajax_return($res, $request['language_type']);

    }

    function getCancelHourListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);

        $CourseModel = new \Model\Smc\CourseModel($request);
        $hourList = $CourseModel->getCancelHourList($request);

        $field[0]["fieldstring"] = "hour_id";
        $field[0]["fieldname"] = "课时id";
        $field[0]["custom"] = 0;
        $field[0]["show"] = 0;

        $field[1]["fieldstring"] = "hour_day";
        $field[1]["fieldname"] = "上课日期";
        $field[1]["custom"] = 0;
        $field[1]["show"] = 0;

        $field[2]["fieldstring"] = "hour_starttime";
        $field[2]["fieldname"] = "开始时间";
        $field[2]["custom"] = 0;
        $field[2]["show"] = 0;

        $field[3]["fieldstring"] = "hour_endtime";
        $field[3]["fieldname"] = "结束时间";
        $field[3]["custom"] = 0;
        $field[3]["show"] = 0;

        $result["field"] = $field;
        $result["list"] = $hourList;
        $result["num"] = count($hourList);

        $res = array('error' => '0', 'errortip' => '获取信息成功', 'result' => $result);
        ajax_return($res, $request['language_type']);
    }

    //取消上课
    function cancelClassApi()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);


        if (empty($request['hour_cancelnote'])) {
            $res = array('error' => '1', 'errortip' => '请填入取消原因');
            ajax_return($res, $request['language_type']);
        }

        $CourseModel = new \Model\Smc\CourseModel($request);
        $bool = $CourseModel->cancelClass($request);

        if ($bool) {
            $res = array('error' => '0', 'errortip' => $CourseModel->errortip, 'result' => array());
        } else {
            $res = array('error' => '1', 'errortip' => $CourseModel->errortip, 'result' => array());
        }

        ajax_return($res, $request['language_type']);
    }

    // 调整课程获取课程详情
    function courseOneApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);

        $CourseModel = new \Model\Smc\CourseModel($request);
        $hourOne = $CourseModel->getCourseOne($request['hour_id']);

        $fieldstring = array('hour_id', 'course_cnname', 'course_branch', 'classroom_cnname', 'staffer_cnname', 'class_cnname', 'hour_day', 'hour_starttime', 'hour_endtime', 'hour_ischecking', 'class_id', 'course_id', 'teaching_id');
        $fieldname = array('课时id', '课程名', '课程别', '教室', '教师', '班级名称', '上课时间', '上课开始时间', '上课结束时间', '班级id', '课程id', '班级课时id');
        $fieldcustom = array('0', "1", "1", "1", "1", "1", "1", "1", "1", '1', '0', '0', '0');
        $fieldshow = array('0', "1", "1", "1", "1", "1", "1", "1", "1", '1', '0', '0', '0');
        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field['hour'][$i]["fieldstring"] = trim($fieldstring[$i]);
            $field['hour'][$i]["fieldname"] = trim($fieldname[$i]);
            $field['hour'][$i]["custom"] = trim($fieldcustom[$i]);
            $field['hour'][$i]["show"] = trim($fieldshow[$i]);

        }

        $result["field"] = $field;
        $result["list"] = $hourOne;

        $res = array('error' => '0', 'errortip' => '获取信息成功', 'result' => $result);
        ajax_return($res, $request['language_type']);
    }

    //调整上课日期
    function adjustCourseDayApi()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $classOne = $this->DataControl->selectOne("select  s.course_inclasstype from  smc_class_hour as c,smc_course as s where hour_id ='{$request['hour_id']}' and s.course_id =c.course_id ");
//        if ($classOne['course_inclasstype'] == 2) {
//            $res = array('error' => '1', 'errortip' => "预约类班级无法调课", 'conflict' => array(), 'result' => array());
//            ajax_return($res,$request['language_type']);
//        }
        $CourseModel = new \Model\Smc\CourseModel($request);
        $bool = $CourseModel->adjustCourseDay($request);
        if ($bool == false) {
            $res = array('error' => $CourseModel->error, 'errortip' => $CourseModel->errortip, 'conflict' => $CourseModel->conflict, 'result' => array());
            ajax_return($res, $request['language_type']);
        } else {
            $this->addSmcWorkLog($request['company_id'], $request['school_id'], $request['staffer_id'], "课务管理->课程表->调整排课", '调整上课日期', dataEncode($request));

            $res = array('error' => '0', 'errortip' => '调整成功', 'result' => array());
            ajax_return($res, $request['language_type']);
        }
    }


    /**
     * 批量调整上课日期
     * author: ling
     * 对应接口文档 0001
     */
    function adjustChooseCourseDayAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
//        $res = array('error' => '1', 'errortip' => '该功能正在维护', 'result' => array());
//        ajax_return($res,$request['language_type']);

        $CourseModel = new \Model\Smc\CourseModel($request);

        $bool = $CourseModel->adjustChooseCourseDay($request);

        if ($bool == false) {
            $fieldname = array('班级名称', '班级别名', '上课日期', '上课时间', '教师', '教室');
            $fieldstring = array('class_cnname', 'class_enname', 'hour_day', 'time', 'staffer_cnname', 'classroom_cnname');

            $field = array();
            for ($i = 0; $i < count($fieldstring); $i++) {
                $field[$i]["fieldstring"] = trim($fieldstring[$i]);
                $field[$i]["fieldname"] = trim($fieldname[$i]);
                $field[$i]["custom"] = 1;
                $field[$i]["show"] = 1;
            }

            $result["field"] = $field;
            $result["list"] = $CourseModel->conflictData;
            $res = array('error' => $CourseModel->error, 'errortip' => $CourseModel->errortip, 'conflict' => $CourseModel->conflict, 'adjust_error' => $CourseModel->adjust_error, 'result' => $result);

            ajax_return($res, $request['language_type']);
        } else {

            $this->addSmcWorkLog($request['company_id'], $request['school_id'], $request['staffer_id'], "课务管理->课程表->调整排课", '调整上课日期', dataEncode($request));

            $res = array('error' => '0', 'errortip' => '调整成功', 'result' => array());
            ajax_return($res, $request['language_type']);
        }

    }

    //获取教师今日排课
    function teacherClassApi()
    {

        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);

        $CourseModel = new \Model\Smc\CourseModel($request);

        $teacherList = $CourseModel->getTeacherClass($request);

        $fieldstring = array('hour_time', 'class_cnname', 'classroom_cnname');
        $fieldname = array('时间', '班级', '教室');
        $fieldcustom = array('1', "1", "1");
        $fieldshow = array('1', "1", "1");
        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["fieldname"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }


        $result['field'] = $field;
        $result['list'] = $teacherList;
        if ($teacherList) {
            $res = array('error' => '0', 'errortip' => '获取成功', 'result' => $result);
        } else {
            $res = array('error' => '1', 'errortip' => '暂无教师课程安排信息', 'result' => $result);
        }

        ajax_return($res, $request['language_type']);


    }

    //获取教室今日排课
    function getClassroomClassApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);

        $CourseModel = new \Model\Smc\CourseModel($request);

        $classroomList = $CourseModel->getClassroomClass($request);
        $fieldstring = array('hour_time', 'class_cnname', 'staffer_cnname');
        $fieldname = array('时间', '班级', '教师');
        $fieldcustom = array('1', "1", "1");
        $fieldshow = array('1', "1", "1");
        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["fieldname"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result['field'] = $field;
        $result['list'] = $classroomList;

        if (!$classroomList) {
            $res = array('error' => '1', 'errortip' => '暂无班级上课信息', 'result' => $result);
        } else {
            $res = array('error' => '0', 'errortip' => '获取成功', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    //按照教师来调课
    function adjustCourseTimeByStafferApi()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);

        $CourseModel = new \Model\Smc\CourseModel($request);

        $request['hour_starttime'] = sprintf("%05s", $request['hour_starttime']);
        $request['hour_endtime'] = sprintf("%05s", $request['hour_endtime']);
        $bool = $CourseModel->adjustCourseTimeByStaffer($request);

        if ($bool == false) {
            $res = array('error' => $CourseModel->error, 'errortip' => $CourseModel->errortip, 'conflict' => $CourseModel->conflict, 'result' => array());
            ajax_return($res, $request['language_type']);
        } else {
            $this->addSmcWorkLog($request['company_id'], $request['school_id'], $request['staffer_id'], "课务管理->课程表->调整排课", '调整上课时间', dataEncode($request));
            $res = array('error' => $CourseModel->error, 'errortip' => $CourseModel->errortip, 'conflict' => $CourseModel->conflict, 'result' => array());
            ajax_return($res, $request['language_type']);
        }
    }

    //按照教室来调课
    function adjustCourseTimeByRoomApi()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $classOne = $this->DataControl->selectOne("select  s.course_inclasstype from  smc_class_hour as c,smc_course as s where hour_id ='{$request['hour_id']}' and s.course_id =c.course_id  ");
//        if ($classOne['course_inclasstype'] == 2) {
//            $res = array('error' => '1', 'errortip' => "预约类班级无法调课", 'conflict' => array(), 'result' => array());
//            ajax_return($res,$request['language_type']);
//        }
        $CourseModel = new \Model\Smc\CourseModel($request);
        $request['hour_starttime'] = sprintf("%05s", $request['hour_starttime']);
        $request['hour_endtime'] = sprintf("%05s", $request['hour_endtime']);
        $bool = $CourseModel->adjustCourseTimeByRoom($request);
        if (!$bool) {
            $res = array('error' => $CourseModel->error, 'errortip' => $CourseModel->errortip, 'conflict' => $CourseModel->conflict, 'result' => array());
            ajax_return($res, $request['language_type']);
        } else {
            $this->addSmcWorkLog($request['company_id'], $request['school_id'], $request['staffer_id'], "课务管理->课程表->调整排课", '调整上课时间', dataEncode($request));
            $res = array('error' => $CourseModel->error, 'errortip' => $CourseModel->errortip, 'conflict' => $CourseModel->conflict, 'result' => array());
            ajax_return($res, $request['language_type']);
        }
    }

    //获取主教课程安排状态
    function getMainStafferApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);

        if (empty($request['hour_starttime'])) {

            $res = array('error' => '1', 'errortip' => '课程开始时间缺失', 'result' => "");
            ajax_return($res, $request['language_type']);
        }
        if (empty($request['hour_endtime'])) {
            $res = array('error' => '1', 'errortip' => '课程结束时间缺失', 'result' => "");
            ajax_return($res, $request['language_type']);
        }


        $CourseModel = new \Model\Smc\CourseModel($request);
//
        $datalist = $CourseModel->getMainStafferList($request);
        if (!$datalist) {
            $datalist = array();
        }

        $fieldstring = array('staffer_id', 'staffer_cnname', 'post_name', 'hour_num', 'info_isforeign', 'staffer_status');
        $fieldname = array('教师id', '教师姓名', '职务', '本周课时数', '教师类型', '状态');
        $fieldcustom = array('0', '1', "1", "1", '1', '1');
        $fieldshow = array('0', '1', "1", "1", '1', '1');
        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["fieldname"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }
        $field[5]["is_string"] = 1;
        $result['field'] = $field;
        $result['list'] = $datalist;
        if (!$datalist) {
            $res = array('error' => '1', 'errortip' => '暂无上课教师信息', 'result' => $result);
        } else {
            $res = array('error' => '0', 'errortip' => '获取成功', 'result' => $result);
        }

        ajax_return($res, $request['language_type']);
    }

    //获取教室课时安排
    function getClassroomApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        if (empty($request['hour_starttime'])) {

            $res = array('error' => '1', 'errortip' => '课程开始时间缺失', 'result' => "");
            ajax_return($res, $request['language_type']);
        }
        if (empty($request['hour_endtime'])) {
            $res = array('error' => '1', 'errortip' => '课程结束时间缺失', 'result' => "");
            ajax_return($res, $request['language_type']);
        }
        if (empty($request['hour_day'])) {
            $res = array('error' => '1', 'errortip' => '课程日期缺失', 'result' => "");
            ajax_return($res, $request['language_type']);
        }

        $CourseModel = new \Model\Smc\CourseModel($request);

        $datalist = $CourseModel->getClassroomList($request);
        if (!$datalist) {
            $datalist = array();
        }
        $fieldstring = array('classroom_id', 'classroom_cnname', 'hour_num', 'classroom_maxnums', 'classroom_status');
        $fieldname = array('教室id', '教室', '今日课时数', '容量', '状态');
        $fieldcustom = array('0', '1', "1", "1", '1');
        $fieldshow = array('0', '1', "1", "1", '1');
        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["fieldname"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }
        $field[4]["is_string"] = 1;

        $result['field'] = $field;
        $result['list'] = $datalist;
        if ($datalist) {
            $res = array('error' => '0', 'errortip' => '获取成功', 'result' => $result);
        } else {
            $res = array('error' => '1', 'errortip' => '暂无上课教室信息', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //调整上课教师
    function adjustClassTeacherApi()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);

        if (!$request['hour_id']) {
            $res = array('error' => '1', 'errortip' => 'hour_id为空', 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        if (!$request['teaching_id']) {
            $res = array('error' => '1', 'errortip' => '记录id为空', 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        $CourseModel = new \Model\Smc\CourseModel($request);

        $bool = $CourseModel->adjustClassTeacher($request);

        if ($bool) {
            $this->addSmcWorkLog($request['company_id'], $request['school_id'], $request['staffer_id'], "课务管理->课程表->调整排课", '调整上课教师', dataEncode($request));
            $res = array('error' => $CourseModel->error, 'errortip' => $CourseModel->errortip, 'conflict' => $CourseModel->conflict, 'result' => array());

        } else {
            $res = array('error' => $CourseModel->error, 'errortip' => $CourseModel->errortip, 'conflict' => $CourseModel->conflict, 'result' => array());

        }
        ajax_return($res, $request['language_type']);

    }

    //调整上课教室

    function adjustClassRoomApi()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $CourseModel = new \Model\Smc\CourseModel($request);
        if (!$request['hour_id']) {
            $res = array('error' => '1', 'errortip' => 'hour_id为空', 'result' => array());
        }
        if (!$request['classroom_id']) {
            $res = array('error' => '1', 'errortip' => '请选择教室', 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        $bool = $CourseModel->adjustClassRoomApi($request);

        if ($bool) {
            $res = array('error' => $CourseModel->error, 'errortip' => $CourseModel->errortip, 'conflict' => $CourseModel->conflict, 'result' => array());
            $this->addSmcWorkLog($request['company_id'], $request['school_id'], $request['staffer_id'], "课务管理->课程表->调整排课", '调整上课教室', dataEncode($request));

        } else {
            $res = array('error' => $CourseModel->error, 'errortip' => $CourseModel->errortip, 'conflict' => $CourseModel->conflict, 'result' => array());

        }
        ajax_return($res, $request['language_type']);
    }

    //更新校务试听记录表

//    function insertAuditionRecordApi()
//    {
//        $request = Input('get.', '', 'trim,addslashes');
//        //由CRM调用接口 ,不要加验证
//        //$this->ThisVerify($request);
//
//        $data = array();
//        $data['company_id'] = $request['company_id'];
//        $data['school_id'] = $request['school_id'];
//        $data['hour_id'] = $request['hour_id'];
//        $data['class_id'] = $request['class_id'];
//        $data['client_id'] = $request['client_id'];
//        $data['audition_cnname'] = $request['audition_cnname'];
//        $data['audition_enname'] = $request['audition_enname'];
//        $data['audition_createtime'] = time();
//        if ($this->DataControl->insertData('smc_class_hour_audition', $data)) {
//            $res = array('error' => '0', 'errortip' => '新增成功', 'result' => array());
//            ajax_return($res, $request['language_type']);
//
//        } else {
//            $res = array('error' => '1', 'errortip' => '新增失败', 'result' => array());
//            ajax_return($res, $request['language_type']);
//        }
//
//    }


    //获取学员缺勤原因
    function getStudentReasonApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);

        $CourseModel = new \Model\Smc\CourseModel($request);

        $field[0]["fieldstring"] = "reason_id";
        $field[0]["fieldname"] = "原因id";
        $field[0]["custom"] = "0";
        $field[0]["show"] = "0";
        $field[0]["fieldstring"] = "reason_note";
        $field[0]["fieldname"] = "原因";
        $field[0]["custom"] = "1";
        $field[0]["show"] = "1";

        $datalist = $CourseModel->getStudentReason($request);

        $result['field'] = $field;
        $result['list'] = $datalist;


        $res = array('error' => '0', 'errortip' => '获取成功', 'result' => $result);
        ajax_return($res, $request['language_type']);


    }

    //试听记录列表设置已到访-未到访-课务
    function isauditionApi()
    {
        $request = Input('post.', '', 'trim,addslashes');
        if (!$request['audition_id']) {
            $res = array('error' => '1', 'errortip' => "id错误", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        if ($request['audition_isvisit'] == "") {
            $res = array('error' => '1', 'errortip' => "请试听状态", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        $CourseModel = new \Model\Smc\CourseModel($request);

        $bools = $CourseModel->setIsaudition($request);
        if ($bools) {
            $res = array('error' => '0', 'errortip' => "设置成功", 'result' => array());
        } else {
            $res = array('error' => '0', 'errortip' => "设置失败", 'result' => array());
        }
        ajax_return($res, $request['language_type']);
    }

    //获取上课课时列表 -crm试听跟进调用
//    function getAuditionHourApi()
//    {
//
//        $request = Input('get.', '', 'trim,addslashes');
////不要加校验
//
//        $CourseModel = new \Model\Smc\CourseModel($request);
//
//        if ($request['audition_genre'] == 0) {
//            $dataList = $CourseModel->getPucList($request);
//        } else {
//            $dataList = $CourseModel->getAuditionHour($request);
//        }
//
//
//        $fieldstring = array('hour_id', 'class_id', 'course_id', 'class_cnname', 'class_branch', 'course_cnname', 'course_branch', 'hour_day', 'hour_time', 'classroom_cnname', 'staffer_cnname', 'class_num', 'hour_num');
//        $fieldname = array('课时id', '班级id', '课程id', '班级名称', '班级编号', '课程名', '课程别编号', '上课日期', '上课时间', '教室', '教师', '在班人数', '计划/已上课时');
//        $fieldcustom = array('0', '0', '0', '1', '1', "1", "1", "1", '1', '1', '1', '1');
//        $fieldshow = array('0', '0', '0', '1', '1', "1", "1", "1", '1', '1', '1', '1');
//        $field = array();
//        for ($i = 0; $i < count($fieldstring); $i++) {
//            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
//            $field[$i]["fieldname"] = trim($fieldname[$i]);
//            $field[$i]["custom"] = trim($fieldcustom[$i]);
//            $field[$i]["show"] = trim($fieldshow[$i]);
//        }
//
////		$field[8]["is_percentage"] = 1;
//        $result['field'] = $field;
//        $result['list'] = $dataList;
//
//        ajax_return($result, $request['language_type']);
////		$res = array('error' => '0', 'errortip' => '获取成功', 'result' => $result);
////		ajax_return($res,$request['language_type']);
//    }

    //获取上课教师 -crm试听跟进调用
    function getAuditionTeacherApi()
    {

        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);

        $CourseModel = new \Model\Smc\CourseModel($request);
        $dataList = $CourseModel->getAuditionTeacher($request);
        $field[0]["fieldstring"] = 'staffer_id';
        $field[0]["fieldname"] = '教师id';
        $field[0]["custom"] = '0';
        $field[0]["show"] = '1';
        $field[1]["fieldstring"] = 'staffer_cnname';
        $field[1]["fieldname"] = '教师';
        $field[1]["custom"] = '0';
        $field[1]["show"] = '1';

        $result['field'] = $field;
        $result['list'] = $dataList;

        ajax_return($result, $request['language_type']);

    }

    //获取课程 crm
    function getCousreApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        //千万不要加token 验证!!!!!


        if (!$request['company_id']) {
            $res = array('error' => '1', 'errortip' => "公司id有误", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        $Model = new \Model\Smc\CourseModel($request);
        $dataList = $Model->getIntentCourse($request['company_id']);

        $field['course_id'] = "课程id";
        $field['course_cnname'] = "课程名";
        $field['course_branch'] = "课程别编号";
        $result['field'] = $field;
        if ($dataList) {
            $result['list'] = $dataList;
        } else {
            $result['list'] = array();
        }


        ajax_return($result, $request['language_type']);

    }

    //修改单个学员的考勤
    function updateOneCheckenAction()
    {

        $request = Input("post.", "", "trim,addslashes");
        $Model = new \Model\Smc\CheckingModel($request);
        $this->ThisVerify($request);

        $hourstudy_id = $this->DataControl->selectOne("select hourstudy_id from smc_student_hourstudy where  hour_id='{$request['hour_id']}' and student_id ='{$request['student_id']}'  limit 0,1");


        if (!$hourstudy_id) {
            $res = array('error' => '1', 'errortip' => "无考勤记录", 'result' => array());
            ajax_return($res, $request['language_type']);
        }

//		 type  = student  audition
        if ($request['checkin'] == 0) {
            $stuchecktype_code = '107';
        } else {
            $stuchecktype_code = '101';
        }

        $bool = $Model->updateOneChecken($request['hour_id'], $request['student_id'], 'student', $request['checkin'], $hourstudy_id['hourstudy_id'], $request['note'], $request['checktype_code'], $stuchecktype_code);
        if ($bool) {
            $this->addSmcWorkLog($request['company_id'], $request['school_id'], $request['staffer_id'], "班务管理->班级管理", '修改学员考勤', dataEncode($request));
            $res = array('error' => '0', 'errortip' => "修改成功", 'result' => array());
        } else {
            $res = array('error' => '1', 'errortip' => "修改失败", 'result' => array());
        }

        ajax_return($res, $request['language_type']);

    }

    /**
     * 加盟校一键考勤
     * author: ling
     * 对应接口文档 0001
     */
    function autoCheckenAction()
    {
        $request = Input("post.", "", 'trim,addslashes');
        $this->ThisVerify($request);
        $comapnyOne = $this->DataControl->getFieldOne('gmc_company', 'comapny_isclocking', "company_id='{$request['company_id']}'");
        if ($comapnyOne['comapny_isclocking'] != 1) {
            $res = array('error' => 1, 'errortip' => "该功能正在维护中", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        $Model = new \Model\Smc\CheckingModel($request);
        $Model->autoCheckenAction($request);
        $this->addSmcWorkLog($request['company_id'], $request['school_id'], $request['staffer_id'], "班务管理->班级管理", '一键考勤', dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);

    }

    /**
     * 新增暖身课
     * author: ling
     * 对应接口文档 0001
     */
    function addWarmHourOneAction()
    {
        $request = Input("post.", "", "trim,addslashes");
        $Model = new \Model\Smc\CourseModel($request);
        $this->ThisVerify($request);

        $Model->addWarmHourOne($request);
        
        $this->addSmcWorkLog($request['company_id'], $request['school_id'], $request['staffer_id'], "班务管理->班级管理", '新增暖身课', dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);

    }

    /**
     * 补考勤
     * author: ling
     * 对应接口文档 0001
     */
    function stuReplenishCheckinAction()
    {
        $request = Input('post.', '', 'trim,addslashes');

        $this->ThisVerify($request);
        $classModel = new \Model\Smc\CheckingModel($request);
        $bool = $classModel->stuReplenishCheckinAction($request);
        if ($bool) {
            $res = array('error' => 0, 'errortip' => '补考勤成功', 'result' => array());
            $this->addSmcWorkLog($request['company_id'], $request['school_id'], $request['staffer_id'], "班务管理->班级管理", '提前入班', dataEncode($request));
        } else {
            $res = array('error' => 1, 'errortip' => $classModel->errortip, 'result' => array());
        }
        ajax_return($res, $request['language_type']);
    }

    /**
     * 获取可选的出勤状态
     * author: ling
     * 对应接口文档 0001
     */
    function getStucheckCodeView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);

        $hourOne = $this->DataControl->getFieldOne("smc_class_hour", "hour_day", "hour_id='{$request['hour_id']}'");
        if ($hourOne && ($hourOne['hour_day'] < date("Y-m-d"))) {
            $isbefore = 1;
        } else {
            $isbefore = 0;
        }
        $data = array();

        $k = 0;
        $data[$k]['stuchecktype_code'] = '101';
        $data[$k]['stuchecktype_name'] = '出勤';
        $data[$k]['is_choose'] = '1';
        $k++;
        $data[$k]['stuchecktype_code'] = '107';
        $data[$k]['stuchecktype_name'] = '缺勤';
        $data[$k]['is_choose'] = '1';

        if ($request['course_inclasstype'] <> 2) {

            $k++;
            $data[$k]['stuchecktype_code'] = '102';
            $data[$k]['stuchecktype_name'] = '请假';
            if ($isbefore == 1) {
                $data[$k]['is_choose'] = '0';
            } else {
                $data[$k]['is_choose'] = '1';
            }
            $k++;
            $data[$k]['stuchecktype_code'] = '301';
            $data[$k]['stuchecktype_name'] = '请假待审批';
            $data[$k]['is_choose'] = '0';
        }

        $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $data);
        ajax_return($res, $request['language_type']);
    }


    //教师带课明细
    function courseStafferRecordApi()
    {

        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ClassModel = new \Model\Smc\CourseModel($request);
        $res = $ClassModel->courseStafferRecord($request);


        $field = array();
        $k = 0;
        $field[$k]["fieldname"] = "教师ID";
        $field[$k]["fieldstring"] = "staffer_id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "班级ID";
        $field[$k]["fieldstring"] = "class_id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "教师姓名";
        $field[$k]["fieldstring"] = "staffer_cnname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "教师编号";
        $field[$k]["fieldstring"] = "staffer_branch";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "职务";
        $field[$k]["fieldstring"] = "post_name";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "班级名称";
        $field[$k]["fieldstring"] = "class_cnname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "班级别名";
        $field[$k]["fieldstring"] = "class_enname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "班级编号";
        $field[$k]["fieldstring"] = "class_branch";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "课程别名称";
        $field[$k]["fieldstring"] = "course_cnname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "课程别编号";
        $field[$k]["fieldstring"] = "course_branch";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "上课方式";
        $field[$k]["fieldstring"] = "hour_way_name";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "上课日期";
        $field[$k]["fieldstring"] = "hour_day";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "上课时间";
        $field[$k]["fieldstring"] = 'hour_time';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "上课教室";
        $field[$k]["fieldstring"] = "classroom_cnname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "是否考勤";
        $field[$k]["fieldstring"] = "hour_ischecking_name";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;

        $result = array();
        $result["field"] = $field;
        if ($res['list']) {
            $result["list"] = $res['list'];
            $result["post"] = $res['post'];
            $result["allnum"] = $res['num'];
            $res = array('error' => '0', 'errortip' => '获取成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["post"] = array();
            $result["allnum"] = 0;
            $res = array('error' => '1', 'errortip' => '暂无教师带课明细', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }


    //调整上课教师
    function adjustClassStafferApi()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);

        if (!$request['re_staffer_id']) {
            $res = array('error' => '1', 'errortip' => '教师id为空', 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        $CourseModel = new \Model\Smc\CourseModel($request);

        $bool = $CourseModel->adjustClassStaffer($request);

        if ($bool) {
            $this->addSmcWorkLog($request['company_id'], $request['school_id'], $request['staffer_id'], "课务管理->课程管理->教师带课明细", '调整上课教师', dataEncode($request));
            $res = array('error' => $CourseModel->error, 'errortip' => $CourseModel->errortip, 'conflict' => $CourseModel->conflict, 'result' => array());
        } else {
            $res = array('error' => $CourseModel->error, 'errortip' => $CourseModel->errortip, 'conflict' => $CourseModel->conflict, 'result' => array());
        }
        ajax_return($res, $request['language_type']);
    }
}
