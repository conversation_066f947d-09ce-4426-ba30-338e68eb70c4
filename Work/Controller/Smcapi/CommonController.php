<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/13
 * Time: 23:25
 * crm-首页
 */

namespace Work\Controller\Smcapi;

class CommonController extends viewTpl
{
	public $u;
	public $t;
	public $c;
	public $visitType = "api";
	
	
	//预加载处理类
	function __construct($visitType = "api")
	{
        parent::__construct ();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
	}

	//凡是未支付的订单，里面含有已经过期48小时的优惠券，则取消订单（陈喆发出的需求 2024-01-03）
    //改一下  +的48小时不要了  直接到期就取消（陈喆发出的需求 2024-12-31）
	function cancelCouponsOrderView(){//smc_payfee_order_autocancel
//	    echo '111';die;
	    $company_id = '8888';
//	    $nowTime = time()-3600*48;
	    $nowTime = time();
	    $orderOne = $this->DataControl->selectOne(" select a.*
                        ,if((select f.autocancel_id from smc_payfee_order_autocancel as f where f.order_pid = a.order_pid limit 0,1)>1,1,0) as autocancel  
                    from smc_payfee_order as a,smc_payfee_order_coupons as b,smc_student_coupons as c 
                    where a.company_id = '{$company_id}' and a.order_status = '1' and a.school_id <> '1175'
                    and a.order_pid = b.order_pid 
                    and b.coupons_pid = c.coupons_pid and c.coupons_exittime < '{$nowTime}' and a.student_id = c.student_id 
                    having autocancel = 0 
                    limit 0,1
                     ");
        if ($orderOne) {
            $canceldata = array();
            $canceldata['staffer_id'] = ($company_id=='8888')?'12357':'12356';//超级管理员
            $canceldata['company_id'] = $company_id;
            $canceldata['school_id'] = $orderOne['school_id'];
            $canceldata['order_pid'] = $orderOne['order_pid'];
            $canceldata['mergeorder_pid'] = $orderOne['mergeorder_pid'];
            $canceldata['create_time'] = date('Y-m-d');

            if($orderOne['mergeorder_pid']!=''){
                $OrderModel = new \Model\Smc\OrderModel($canceldata);
                $res = $OrderModel->cancelMergeOrder($canceldata);
            }else{
                $OrderModel = new \Model\Smc\OrderModel($canceldata, $canceldata['order_pid']);
                $res = $OrderModel->cancelOrder($canceldata);
            }

            if ($res) {
                $data = array();
                $data['protocol_isdel'] = '1';
                $this->DataControl->updateData("smc_student_protocol","order_pid = '{$canceldata['order_pid']}'",$data);
                $this->addSmcWorkLog($canceldata['company_id'], $canceldata['school_id'], $canceldata['staffer_id'], "缴费管理->缴费中心", '取消订单', dataEncode($canceldata));

                $autocancel = array();
                $autocancel['company_id'] = $company_id;
                $autocancel['trading_pid'] = $orderOne['trading_pid'];
                $autocancel['order_pid'] = $orderOne['order_pid'];
                $autocancel['autocancel_type'] = 1;
                $autocancel['autocancel_note'] = '同步成功';
                $this->DataControl->insertData('smc_payfee_order_autocancel', $autocancel);
//                echo $orderOne['trading_pid'].'--'.$orderOne['order_pid'].'--'.'同步成功';
            }else{
                $theerrortip = $OrderModel->errortip;
                $autocancel = array();
                $autocancel['company_id'] = $company_id;
                $autocancel['trading_pid'] = $orderOne['trading_pid'];
                $autocancel['order_pid'] = $orderOne['order_pid'];
                $autocancel['autocancel_type'] = 2;
                $autocancel['autocancel_note'] = $theerrortip;
                $this->DataControl->insertData('smc_payfee_order_autocancel', $autocancel);
//                echo $orderOne['trading_pid'].'--'.$orderOne['order_pid'].'--'.$theerrortip;
            }
        }else{
            echo '暂无数据';
        }


    }

    function autoCancelOrderView(){

        $sql = "SELECT
                    a.order_pid, 
                    a.mergeorder_pid,a.company_id,a.school_id 
                FROM
                    smc_payfee_order a
                    LEFT JOIN smc_student c ON a.student_id = c.student_id
                    LEFT JOIN smc_school d ON d.school_id = a.school_id 
                WHERE
                    d.company_id = 8888 
                    AND d.school_istest = 0 
                    AND d.school_isclose = 0 
                    AND a.order_createtime < 1735747200 AND a.order_type = 0 AND a.order_status >= 0 
                    AND a.order_status < 4 
                    AND NOT EXISTS (
                    SELECT
                        1 
                    FROM
                        smc_payfee_order_pay x,
                        smc_code_paytype y 
                    WHERE
                        x.paytype_code = y.paytype_code 
                        AND x.order_pid = a.order_pid 
                        AND x.paytype_code NOT IN ( 'feewaiver', 'canceldebts' ) 
                        AND x.pay_issuccess = 1 
                        AND y.paytype_ischarge = 1 
                    ) 
                    and not exists(select 1 from smc_cancelorder_log as x where x.order_pid=a.order_pid and x.mergeorder_pid=a.mergeorder_pid )
            ORDER BY
            a.order_createtime
            limit 0,15
            ";

        $orderList=$this->DataControl->selectClear($sql);

        if($orderList){
            foreach($orderList as $orderOne){
               $canceldata = array();
               $canceldata['staffer_id'] = ($orderOne['company_id']=='8888')?'12357':'12356';//超级管理员
               $canceldata['company_id'] = $orderOne['company_id'];
               $canceldata['school_id'] = $orderOne['school_id'];
               $canceldata['order_pid'] = $orderOne['order_pid'];
               $canceldata['mergeorder_pid'] = $orderOne['mergeorder_pid'];

               if($orderOne['mergeorder_pid']!=''){
                   $OrderModel = new \Model\Smc\OrderModel($canceldata);
                   $res = $OrderModel->cancelMergeOrder($canceldata);
               }else{
                   $OrderModel = new \Model\Smc\OrderModel($canceldata, $canceldata['order_pid']);
                   $res = $OrderModel->cancelOrder($canceldata);
               }

               if(!$res){
                   $data=array();
                   $data['mergeorder_pid']=$orderOne['mergeorder_pid'];
                   $data['order_pid']=$orderOne['order_pid'];
                   $data['log_failMessage']=$OrderModel->errortip;
                   $data['log_status']=0;
                   $data['log_time']=time();
                   $this->DataControl->insertData("smc_cancelorder_log",$data);
               }else{
                   $data=array();
                   $data['mergeorder_pid']=$orderOne['mergeorder_pid'];
                   $data['order_pid']=$orderOne['order_pid'];
                   $data['log_status']=1;
                   $data['log_time']=time();
                   $this->DataControl->insertData("smc_cancelorder_log",$data);
               }

           }

            echo '<script language="javascript" type="text/javascript">
                    var i = 1;
                    var intervalid;
                    intervalid = setInterval("fun()", 1000);
                    function fun() {
                        if (i == 0) {
                            window.location.href = "/Common/autoCancelOrder";
                            clearInterval(intervalid);
                        }
                        document.getElementById("mes").innerHTML = i;
                        i--;
                    }
                    </script>
                    <div id="error">
                        <p>将在 <span id="mes">5</span> 秒钟后返回首页！</p>
                    </div> ';

            $res = array('error' => '0', 'errortip' => "取消成功");
        }else{
            $res = array('error' => '1', 'errortip' => '取消失败');
        }
        ajax_return($res);
    }

    

    function batchCancelOrderView(){

        $sql = "SELECT d.* from smc_student_coupons a 
                left join smc_student_coupons_apply b on a.apply_id=b.apply_id
                left join smc_payfee_order_coupons c on a.coupons_pid=c.coupons_pid
                left join smc_payfee_order d on d.order_pid=c.order_pid
                where b.applytype_branch in('IRCFirstpurchase1','IRCFirstpurchase2')
                and d.order_status>=0
                and d.order_status<4
                and a.coupons_price=400
                and d.order_createtime>=1743436800
                limit 0,15
            ";

        $orderList=$this->DataControl->selectClear($sql);

        if($orderList){
            foreach($orderList as $orderOne){
               $canceldata = array();
               $canceldata['staffer_id'] = ($orderOne['company_id']=='8888')?'12357':'12356';//超级管理员
               $canceldata['company_id'] = $orderOne['company_id'];
               $canceldata['school_id'] = $orderOne['school_id'];
               $canceldata['order_pid'] = $orderOne['order_pid'];
               $canceldata['mergeorder_pid'] = $orderOne['mergeorder_pid'];

               if($orderOne['mergeorder_pid']!=''){
                   $OrderModel = new \Model\Smc\OrderModel($canceldata);
                   $res = $OrderModel->cancelMergeOrder($canceldata);
               }else{
                   $OrderModel = new \Model\Smc\OrderModel($canceldata, $canceldata['order_pid']);
                   $res = $OrderModel->cancelOrder($canceldata);
               }

               if(!$res){
                   $data=array();
                   $data['mergeorder_pid']=$orderOne['mergeorder_pid'];
                   $data['order_pid']=$orderOne['order_pid'];
                   $data['log_failMessage']=$OrderModel->errortip;
                   $data['log_status']=0;
                   $data['log_time']=time();
                   $this->DataControl->insertData("smc_cancelorder_log",$data);
               }else{
                   $data=array();
                   $data['mergeorder_pid']=$orderOne['mergeorder_pid'];
                   $data['order_pid']=$orderOne['order_pid'];
                   $data['log_status']=1;
                   $data['log_time']=time();
                   $this->DataControl->insertData("smc_cancelorder_log",$data);
               }

           }

            echo '<script language="javascript" type="text/javascript">
                    var i = 1;
                    var intervalid;
                    intervalid = setInterval("fun()", 1000);
                    function fun() {
                        if (i == 0) {
                            window.location.href = "/Common/batchCancelOrder";
                            clearInterval(intervalid);
                        }
                        document.getElementById("mes").innerHTML = i;
                        i--;
                    }
                    </script>
                    <div id="error">
                        <p>将在 <span id="mes">5</span> 秒钟后返回首页！</p>
                    </div> ';

            $res = array('error' => '0', 'errortip' => "取消成功");
        }else{
            $res = array('error' => '1', 'errortip' => '取消失败');
        }
        ajax_return($res);
    }

    function autoCancelOrderOneView(){

        $sql = "SELECT
                    * 
                FROM
                    smc_payfee_order AS a 
                WHERE
                    a.mergeorder_pid <> '' 
                    AND EXISTS ( SELECT 1 FROM smc_payfee_mergeorder_mergepay AS x WHERE x.mergeorder_pid = a.mergeorder_pid AND x.mergepay_issuccess >= 0 ) 
                    AND EXISTS ( SELECT 1 FROM smc_payfee_order AS x WHERE x.mergeorder_pid = a.mergeorder_pid AND x.order_status >= 0 ) 
                    AND EXISTS ( SELECT 1 FROM smc_payfee_order AS x WHERE x.mergeorder_pid = a.mergeorder_pid AND x.order_status = - 1 ) 
                    and not exists(select 1 from smc_cancelorder_log as x where x.order_pid=a.order_pid and x.mergeorder_pid=a.mergeorder_pid )
            ORDER BY
            a.order_createtime
            limit 0,15
            ";

        $orderList=$this->DataControl->selectClear($sql);

        if($orderList){
            foreach($orderList as $orderOne){
               $canceldata = array();
               $canceldata['staffer_id'] = ($orderOne['company_id']=='8888')?'12357':'12356';//超级管理员
               $canceldata['company_id'] = $orderOne['company_id'];
               $canceldata['school_id'] = $orderOne['school_id'];
               $canceldata['order_pid'] = $orderOne['order_pid'];
               $canceldata['mergeorder_pid'] = $orderOne['mergeorder_pid'];

               $OrderModel = new \Model\Smc\OrderModel($canceldata, $canceldata['order_pid']);
               $res = $OrderModel->cancelOrder($canceldata);

//               if($orderOne['mergeorder_pid']!=''){
//                   $OrderModel = new \Model\Smc\OrderModel($canceldata);
//                   $res = $OrderModel->cancelMergeOrder($canceldata);
//               }else{
//                   $OrderModel = new \Model\Smc\OrderModel($canceldata, $canceldata['order_pid']);
//                   $res = $OrderModel->cancelOrder($canceldata);
//               }

               if(!$res){
                   $data=array();
                   $data['mergeorder_pid']=$orderOne['mergeorder_pid'];
                   $data['order_pid']=$orderOne['order_pid'];
                   $data['log_failMessage']=$OrderModel->errortip;
                   $data['log_status']=0;
                   $data['log_time']=time();
                   $this->DataControl->insertData("smc_cancelorder_log",$data);
               }else{
                   $data=array();
                   $data['mergeorder_pid']=$orderOne['mergeorder_pid'];
                   $data['order_pid']=$orderOne['order_pid'];
                   $data['log_status']=1;
                   $data['log_time']=time();
                   $this->DataControl->insertData("smc_cancelorder_log",$data);
               }

               $sql = "select * 
                        from smc_payfee_order as a 
                        where a.mergeorder_pid='{$orderOne['mergeorder_pid']}'
                        and a.order_status>=0
                        limit 0,1
                        ";

               if(!$this->DataControl->selectOne($sql)){
                    $data=array();
                    $data['mergeorder_status']=-1;
                    $data['mergeorder_createtime']=time();
                    $this->DataControl->updateData("smc_payfee_mergeorder","mergeorder_pid='{$orderOne['mergeorder_pid']}'",$data);
               }

           }

            echo '<script language="javascript" type="text/javascript">
                    var i = 1;
                    var intervalid;
                    intervalid = setInterval("fun()", 1000);
                    function fun() {
                        if (i == 0) {
                            window.location.href = "/Common/autoCancelOrderOne";
                            clearInterval(intervalid);
                        }
                        document.getElementById("mes").innerHTML = i;
                        i--;
                    }
                    </script>
                    <div id="error">
                        <p>将在 <span id="mes">5</span> 秒钟后返回首页！</p>
                    </div> ';

            $res = array('error' => '0', 'errortip' => "取消成功");
        }else{
            $res = array('error' => '1', 'errortip' => '取消失败');
        }
        ajax_return($res);
    }


    //用户学校切换记录
    function addStafferSchoolAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $company_id = Input('post.company_id');

        if (!$company_id) {
            $res = array('error' => '1', 'errortip' => "公司id有误", 'result' => array());
            ajax_return($res,$request['language_type']);
        }

        $Model= new \Model\Smc\CommonModel($request);
        $dataList =$Model->addStafferSchoolAction($request);

        if($dataList){
            $this->addSmcWorkLog($request['company_id'],$request['newschool_id'],$request['staffer_id'],"用户切换学校",'用户切换学校',dataEncode($request));
            $res = array('error' => '0', 'errortip' => "切换成功");
        }else{
            $res = array('error' => '1', 'errortip' => '切换失败');
        }
        ajax_return($res,$request['language_type']);
    }

    //首页 -- 筛选区域
    function getDistrictApi()
    {
        $request = Input('get.','','trim,addslashes');

        //model
        $CommonModel = new \Model\Smc\CommonModel($request);
        $dataList = $CommonModel->getDistrictApi($request);

        $field = array();
        $field["district_id"] = "序号";
        $field["company_id"] = "所属公司";
        $field["district_branch"] = "区域代码";
        $field["district_cnname"] = "区域名称";
        $field["district_sort"] = "区域排序";
        $field["district_content"] = "区域描述";

        if($dataList){
            $result["field"] = $field;
            $result["data"] = $dataList;
            $res = array('error' => '0', 'errortip' => '筛选区域成功', 'result' => $result);
        }else{
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '筛选区域失败', 'result' => $result);
        }
        ajax_return($res,$request['language_type'],1);
    }
    //首页 -- 筛选省份
    function getProvinceApi()
    {
        $request = Input('get.','','trim,addslashes');

        //model
        $CommonModel = new \Model\Smc\CommonModel($request);
        $dataList = $CommonModel->getProvinceApi($request);

        $field = array();
        $field["region_id"] = "序号";
        $field["region_initial"] = "首字母";
        $field["region_iscrown"] = "直辖";
        $field["region_code"] = "区域代码";
        $field["region_name"] = "地理名称";
        $field["region_enname"] = "英文名称";
        $field["region_shortenname"] = "英文简称";

        if($dataList){
            $result["field"] = $field;
            $result["data"] = $dataList;
            $res = array('error' => '0', 'errortip' => '筛选省份成功', 'result' => $result);
        }else{
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '筛选省份失败', 'result' => $result);
        }
        ajax_return($res,$request['language_type'],1);
    }
    //首页 -- 筛选城市
    function getCityApi()
    {
        $request = Input('get.','','trim,addslashes');

        $region_id = $request['region_id'];
        if(!isset($region_id) || $region_id == '' || $region_id == '0'){
            $res = array('error' => '1', 'errortip' => '必须选择省份', 'result' => array());
            ajax_return($res,$request['language_type']);
        }

        //model
        $CommonModel = new \Model\Smc\CommonModel($request);
        $dataList = $CommonModel->getCityApi($request);

        $field = array();
        $field["region_id"] = "序号";
        $field["region_initial"] = "首字母";
        $field["region_iscrown"] = "直辖";
        $field["region_code"] = "区域代码";
        $field["region_name"] = "地理名称";
        $field["region_enname"] = "英文名称";
        $field["region_shortenname"] = "英文简称";

        if($dataList){
            $result["field"] = $field;
            $result["data"] = $dataList;
            $res = array('error' => '0', 'errortip' => '筛选城市成功', 'result' => $result);
        }else{
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '筛选城市失败', 'result' => $result);
        }
        ajax_return($res,$request['language_type'],1);
    }
    //首页 -- 筛选区域
    function getAreaApi()
    {
        $request = Input('get.','','trim,addslashes');

        $region_id = $request['region_id'];
        if(!isset($region_id) || $region_id == '' || $region_id == '0'){
            $res = array('error' => '1', 'errortip' => '必须选择城市', 'result' => array());
            ajax_return($res,$request['language_type']);
        }

        //model
        $CommonModel = new \Model\Smc\CommonModel($request);
        $dataList = $CommonModel->getAreaApi($request);

        $field = array();
        $field["region_id"] = "序号";
        $field["region_initial"] = "首字母";
        $field["region_iscrown"] = "直辖";
        $field["region_code"] = "区域代码";
        $field["region_name"] = "地理名称";
        $field["region_enname"] = "英文名称";
        $field["region_shortenname"] = "英文简称";

        if($dataList){
            $result["field"] = $field;
            $result["data"] = $dataList;
            $res = array('error' => '0', 'errortip' => '筛选区域成功', 'result' => $result);
        }else{
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '筛选区域失败', 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }

    //首页 -- 筛选城市 --  台湾市县
    function getTwCityApi()
    {
        $request = Input('get.','','trim,addslashes');
        //model
        $CommonModel = new \Model\Smc\CommonModel($request);
        $dataList = $CommonModel->getTwCityApi($request);

        $field = array();
        $field["region_id"] = "序号";
        $field["region_initial"] = "首字母";
        $field["region_iscrown"] = "直辖";
        $field["region_code"] = "区域代码";
        $field["region_name"] = "地理名称";
        $field["region_enname"] = "英文名称";
        $field["region_shortenname"] = "英文简称";

        if($dataList){
            $result["field"] = $field;
            $result["data"] = $dataList;
            $res = array('error' => '0', 'errortip' => '筛选城市成功', 'result' => $result);
        }else{
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '筛选城市失败', 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }

    //首页 -- 月份列表日程展示
    function monthEventApi()
    {
        $request = Input('get.','','trim,addslashes');
        //model
        $CommonModel = new \Model\Smc\CommonModel($request);
        $dataList = $CommonModel->monthEventApi($request);

        $field = array();
        $field["event_id"] = "事件序号";
        $field["year"] = "年";
        $field["month"] = "月";
        $field["day"] = "日";
        $field["is_have"] = "是否有事件：-1 没有";

        $result = array();
        if($dataList){
            $result["field"] = $field;
            $result["theWeek"] = $dataList['week'];
            $result["data"] = $dataList['mothList'];
            $res = array('error' => '0', 'errortip' => '某日的日程安排', 'result' => $result);
        }else{
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '某日的日程安排', 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }


    //首页 -- 筛选学校
    function getSchoolApi()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);
        //验证账户

        $CommonModel = new \Model\Crm\CommonModel($request);
        $dataList = $CommonModel->getSchoolApi($request);

        $sql="select school_id,postrole_id,postbe_id,postbe_iscrmuser from gmc_staffer_postbe where company_id='{$request['company_id']}'";

        $postbeList=$this->DataControl->selectClear($sql);
        if($postbeList){
            $postbeArray=array_column($postbeList,null,'postbe_id');
        }

        $sql="select postrole_id,postpart_iscrmuser from gmc_company_postrole where company_id='{$request['company_id']}'";

        $postroleList=$this->DataControl->selectClear($sql);
        if($postroleList){
            $postroleArray=array_column($postroleList,null,'postrole_id');
        }


        if ($dataList['datalist'][$request['company_id']]) {
            foreach ($dataList['datalist'][$request['company_id']] as &$val) {
                if($request['re_postbe_id'] == '0'){
                    $status = '1';
                }else{
                    if($postbeArray['postbe_id']['school_id'] == '0'){
                        if($postroleArray[$postbeArray['postbe_id']['postrole_id']]['postpart_iscrmuser'] == '1'){
                            $status = '1';
                        }else{
                            $status = '0';
                        }
                    }else{
                        if($postbeArray['postbe_id']['postbe_iscrmuser'] == '1'){
                            $status = '1';
                        }else{
                            $status = '0';
                        }
                    }
                }
                $val['status'] = $status;
            }
        }


        $field = array();
        $field["school_id"] = "序号";
        $field["company_id"] = "所属公司";
        $field["district_id"] = "所属集团区域ID";
        $field["school_branch"] = "校区编号";
        $field["school_shortname"] = "校园简称";
        $field["school_cnname"] = "校园名称称";
        $field["school_enname"] = "检索代码称";
        $field["company_cnname"] = "机构名称";
        $field["school_address"] = "学校地址";
        $field["school_phone"] = "学校联系电话";

        $result = array();
        if($dataList){
            $result["field"] = $field;
            $result["data"] = $dataList['datalist'];
            $res = array('error' => '0', 'errortip' => '筛选学校成功', 'initial' =>  $dataList['initial'],'allid' =>  $dataList['allid'], 'result' => $result);
        }else{
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '筛选学校失败', 'result' => $result);
        }
        ajax_return($res,$request['language_type'],1);
    }

    //首页 -- 筛选学校机构
    function getSchooOrganizelApi()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        //model
        $CommonModel = new \Model\Crm\CommonModel($request);
        $dataList = $CommonModel->getSchooOrganizelApi($request);

        $field = array();
        $field["organize_id"] = "序号";
        $field["organize_cnname"] = "机构中文名称";
        $field["organize_enname"] = "机构英文名称";

        $result = array();
        if($dataList){
            $result["field"] = $field;
            $result["data"] = $dataList;
            $res = array('error' => '0', 'errortip' => '筛选学校机构成功', 'result' => $result);
        }else{
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '筛选学校机构失败', 'result' => $result);
        }
        ajax_return($res,$request['language_type'],1);
    }

    //首页 -- 筛选学校机构
    function getModuleHandbookApi()
    {
        $request = Input('get.','','trim,addslashes');
        //model
        $CommonModel = new \Model\Crm\CommonModel($request);
        $dataList = $CommonModel->getModuleHandbookApi($request);

        $field = array();
        $field["handbook_id"] = "ID";
        $field["module_id"] = "所属模块ID";
        $field["handbook_name"] = "手册名称";
        $field["handbook_note"] = "手册内容";
        $field["handbook_videourl"] = "培训视频路径";

        $result = array();
        if($dataList){
            $result["field"] = $field;
            $result["data"] = $dataList;
            $res = array('error' => '0', 'errortip' => '模块培训教程获取成功', 'result' => $result);
        }else{
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '模块培训教程获取失败', 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }

    //首页 -- 筛选学校机构
    function getModuleHandbookOneApi()
    {
        $request = Input('get.','','trim,addslashes');
        //model
        $CommonModel = new \Model\Crm\CommonModel($request);
        $dataList = $CommonModel->getModuleHandbookOneApi($request);

        $field = array();
        $field["handbook_id"] = "ID";
        $field["module_id"] = "模块ID";
        $field["handbook_name"] = "手册名称";
        $field["handbook_note"] = "手册内容";

        $result = array();
        if($dataList){
            $result["field"] = $field;
            $result["data"] = $dataList;
            $res = array('error' => '0', 'errortip' => '培训教程获取成功', 'result' => $result);
        }else{
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '培训教程获取失败', 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }

}