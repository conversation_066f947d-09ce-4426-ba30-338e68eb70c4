<?php


namespace Work\Controller\Smcapi;


class SuperviseController extends viewTpl
{
    public $u;
    public $t;
    public $c;

    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
    }

    function getTransferInfoApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Smc\SuperviseModel($request);
        $res = $Model->getTransferInfo($request);

        $k=0;
        $field = array();


        $field[$k]["fieldstring"] = "class_cnname";
        $field[$k]["fieldname"] = "班级名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "class_branch";
        $field[$k]["fieldname"] = "班级编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["fieldname"] = "学员名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "student_branch";
        $field[$k]["fieldname"] = "学员编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "order_pid";
        $field[$k]["fieldname"] = "订单编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "confirm_phone";
        $field[$k]["fieldname"] = "手机号码";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "batch_pid";
        $field[$k]["fieldname"] = "用户销课流水号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "income_price";
        $field[$k]["fieldname"] = "销课金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "hour_name";
        $field[$k]["fieldname"] = "销课课时";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "income_times";
        $field[$k]["fieldname"] = "销课课时数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "income_isconfirm";
        $field[$k]["fieldname"] = "销课状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }

    function getStuCourseBalanceApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Smc\SuperviseModel($request);
        $res = $Model->getStuCourseBalance($request);

        $k=0;
        $field = array();

        $field[$k]["fieldstring"] = "companies_cnname";
        $field[$k]["fieldname"] = "主体名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["fieldname"] = "学员名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "student_branch";
        $field[$k]["fieldname"] = "学员编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "course_cnname";
        $field[$k]["fieldname"] = "课程名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "course_branch";
        $field[$k]["fieldname"] = "课程编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "class_cnname";
        $field[$k]["fieldname"] = "班级名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "class_branch";
        $field[$k]["fieldname"] = "班级编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "coursebalance_figure";
        $field[$k]["fieldname"] = "课程余额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "coursebalance_time";
        $field[$k]["fieldname"] = "剩余课次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "coursebalance_issupervise_name";
        $field[$k]["fieldname"] = "监管状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "booking_status_name";
        $field[$k]["fieldname"] = "是否绑定小程序";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }

    function changeSuperviseStatusAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $StudentModel = new \Model\Smc\SuperviseModel($request);
        $res = $StudentModel->changeSuperviseStatus($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '监管成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $StudentModel->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

}