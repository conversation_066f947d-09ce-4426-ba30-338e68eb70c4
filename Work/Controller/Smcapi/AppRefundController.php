<?php


namespace Work\Controller\Smcapi;


class AppRefundController extends viewTpl
{
	public $u;
	public $t;
	public $c;
	
	function __construct()
	{
		parent::__construct();
		$this->u = $this->router->getController();
		$this->t = $this->router->getUrl();
		$this->c = $this->router->getAction();
	}
	
	function homeView()
	{
		$request = Input('get.','','trim,addslashes');
		
		if ($request['keyword'] == "") {
			$res = array('error' => 1, 'errortip' => "请输入关键字", 'result' => array());

			ajax_return($res,$request['language_type']);
		}

		$this->ThisVerify($request);
		$AppRefundModel = new \Model\Smc\AppRefundModel($request);
		$list = $AppRefundModel->getRefundList($request);
		
		$field = array();
		$field[0]["fieldstring"] = "order_id";
		$field[0]["fieldname"] = "ID";
		$field[0]["show"] = 0;
		$field[0]["custom"] = 0;
		
		$field[1]["fieldstring"] = "order_pid";
		$field[1]["fieldname"] = "订单编号";
		$field[1]["show"] = 1;
		$field[1]["custom"] = 1;
		
		$field[2]["fieldstring"] = "student_cnname";
		$field[2]["fieldname"] = "学员中文名";
		$field[2]["show"] = 1;
		$field[2]["custom"] = 1;
		
		$field[3]["fieldstring"] = "student_enname";
		$field[3]["fieldname"] = "学员英文名";
		$field[3]["show"] = 1;
		$field[3]["custom"] = 1;
		$result['field'] = $field;

		$result['list'] = $list;
		
		$res = array('error' => 0, 'errortip' => "获取成功", 'result' => $result);

		ajax_return($res,$request['language_type']);


	}
	
	
	function reFundSuccessAction()
	{
		$request = Input('post.','','trim,addslashes');
		$this->ThisVerify($request);
		$AppRefundModel = new \Model\Smc\AppRefundModel($request);
		$bool = $AppRefundModel->reFundSuccess($request);
		if($bool){
			$res = array('error' => 0, 'errortip' => "调整成功", 'result' => array());
		}else{
			$res = array('error' => 1, 'errortip' => $AppRefundModel->errortip, 'result' => array());
		}
		
		$json_play = new \Webjson();
		$errorData['errorlog_apiurl'] = $this->u.'/'.$this->c;
		$errorData['errorlog_class'] = 0 ;
		$errorData['errorlog_parameter'] =$json_play->encode($request) ;
		$errorData['errorlog_bakjson'] =$json_play->encode($res) ;
		$errorData['errorlog_time'] =time() ;
		
		$this->DataControl->insertData("smc_api_errorlog",$errorData);
		ajax_return($res,$request['language_type']);
	
	
	}
	
	
	
}