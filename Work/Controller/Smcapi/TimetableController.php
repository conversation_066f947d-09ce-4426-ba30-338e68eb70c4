<?php

namespace Work\Controller\Smcapi;


class TimetableController extends viewTpl
{
    public $u;
    public $t;
    public $c;

    function __construct()
    {
        // 指定允许其他域名访问
        header('Access-Control-Allow-Origin:*');
        // 响应类型
        header('Access-Control-Allow-Methods:*');
        // 响应头设置
        header('Access-Control-Allow-Headers:x-requested-with,content-type');
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();

        error_reporting(E_ALL);
        ini_set('display_errors', '1');
    }

    function ThisVerify($request)
    {

        $paramArray = array();
        if (!isset($request['staffer_id']) || $request['staffer_id'] < 0) {
            $res = array('error' => '1', 'errortip' => "参数错误", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        $paramArray['staffer_id'] = $request['staffer_id'];
        $paramArray['school_id'] = $request['school_id'];
        $paramArray['company_id'] = $request['company_id'];
        $paramArray['token'] = $request['token'];
        if (!$this->UserLimit($paramArray)) {
            $result = array();
            $result["list"] = array();
            $result["tokeninc"] = "0";
            $res = array('error' => '1', 'errortip' => "用户token失效", 'result' => $result);
            if (!isset($request['language_type'])) {
                $request['language_type'] = 'zh';
            }
            ajax_return($res, $request['language_type']);
        }
    }


    //调整上课教师,查看操作    获取教师课表

    function teacherOneTimeTableApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        if (!$request['te_staffer_id']) {
            $res = array('error' => '1', 'errortip' => '教师id错误', 'result' => array());
            ajax_return($res, $request['language_type']);
        }

        $field[0]["fieldname"] = "时间";
        $field[0]["fieldstring"] = "time";
        $field[0]["show"] = 1;
        $field[0]["custom"] = 0;
        $field[0]["is_title"] = 1;

        $field[1]["fieldname"] = "课程";
        $field[1]["fieldstring"] = "course";
        $field[1]["show"] = 1;
        $field[1]["custom"] = 0;


        $TimetableModel = new \Model\Smc\TimetableModel($request);
        $dataList = $TimetableModel->getTeacherOneTimeTable($request);

        $result['field'] = $field;
        if ($dataList) {
            $result['list'] = $dataList;
        } else {
            $result['list'] = array();
        }
        if (!$dataList) {
            $res = array('error' => '1', 'errortip' => '暂无教师上课记录', 'result' => $result);
        } else {
            $res = array('error' => '0', 'errortip' => '获取信息', 'result' => $result);
        }

        ajax_return($res);

    }

    //调整上课教室,查看操作  获取教室的课表

    function classOneTimeTableApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        if (!$request['classroom_id']) {
            $res = array('error' => '1', 'errortip' => '教室id错误', 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        $field[0]["fieldname"] = "时间";
        $field[0]["fieldstring"] = "time";
        $field[0]["show"] = 1;
        $field[0]["custom"] = 0;
        $field[0]["is_title"] = 1;

        $field[1]["fieldname"] = "课程";
        $field[1]["fieldstring"] = "course";
        $field[1]["show"] = 1;
        $field[1]["custom"] = 0;

        $TimetableModel = new \Model\Smc\TimetableModel($request);
        $dataList = $TimetableModel->getClassOneTimeTable($request);

        $result['field'] = $field;
        if ($dataList) {
            $result['list'] = $dataList;
        } else {
            $result['list'] = array();
        }

        if (!$dataList) {
            $res = array('error' => '1', 'errortip' => '暂无教室上课记录', 'result' => $result);
        } else {
            $res = array('error' => '0', 'errortip' => '获取信息', 'result' => $result);
        }

        ajax_return($res);
    }

    //3个课表-------------------------------------------------------------
    // 获取 所有班级一周课表
    function classTimetableView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);

        $TimetableModel = new \Model\Smc\TimetableModel($request);
        $dataList = $TimetableModel->getClassWeekTimetable($request);

        $result = array();
        $field = array();

        $field[0]["fieldname"] = "班级名称";
        $field[0]["fieldstring"] = "class_cnname";
        $field[0]["show"] = 1;
        $field[0]["custom"] = 0;
        $field[0]["is_title"] = 1;
        if ($dataList) {

            foreach ($dataList['week_date'] as $key => $value) {
                $field[$key + 1]["fieldname"] = "{$value['weekday']}";
                $field[$key + 1]["fieldstring"] = "{$value['week_day']}";
                $field[$key + 1]["show"] = 1;
                $field[$key + 1]["custom"] = 0;
            }
            unset($dataList['week_date']);
        } else {
            $enweekarray = array("Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday",);
            $weekarray = array("一", "二", "三", "四", "五", "六", "日",);

            for ($i = 0; $i < 7; $i++) {
                $day = date('Y-m-d', strtotime("+$i day", strtotime($request['hour_startday'])));
                $field[$i + 1]["fieldname"] = $day . '周' . $weekarray[$i];
                $field[$i + 1]["fieldstring"] = $enweekarray[$i];
                $field[$i + 1]["show"] = 1;
                $field[$i + 1]["custom"] = 0;
            }

        }

        $result['field'] = $field;
        if ($dataList) {
            $result['list'] = $dataList;
        } else {
            $result['list'] = array();
        }

        if (!$dataList) {
            $res = array('error' => '1', 'errortip' => '暂无班级课表记录', 'result' => $result);
        } else {
            $res = array('error' => '0', 'errortip' => '获取信息', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    // 获取所有教师一周的课表
    function teacherTimetableView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $TimetableModel = new \Model\Smc\TimetableModel($request);
        $dataList = $TimetableModel->getTeacherWeekTimetable($request);

        if (!isset($request['is_export']) || $request['is_export'] != 1) {
            $result = array();
            $field = array();
            $field[0]["fieldname"] = "教师名称";
            $field[0]["fieldstring"] = "staffer_cnname";
            $field[0]["show"] = 1;
            $field[0]["custom"] = 0;
            $field[0]["is_title"] = 1;
            if ($dataList) {
                foreach ($dataList['week_date'] as $key => $value) {
                    $field[$key + 1]["fieldname"] = "{$value['weekday']}";
                    $field[$key + 1]["fieldstring"] = "{$value['week_day']}";
                    $field[$key + 1]["show"] = 1;
                    $field[$key + 1]["custom"] = 0;
                }
                unset($dataList['week_date']);
            } else {
                $enweekarray = array("Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday",);
                $weekarray = array("一", "二", "三", "四", "五", "六", "日",);

                for ($i = 0; $i < 7; $i++) {
                    $day = date('Y-m-d', strtotime("+$i day", strtotime($request['hour_startday'])));
                    $field[$i + 1]["fieldname"] = $day . '周' . $weekarray[$i];
                    $field[$i + 1]["fieldstring"] = $enweekarray[$i];
                    $field[$i + 1]["show"] = 1;
                    $field[$i + 1]["custom"] = 0;
                }
            }

            foreach ($dataList as $key => &$dataOne) {
                if ($dataOne) {
                    foreach ($dataOne as &$One) {
                        if (!isset($One['staffer_cnname'])) {
                            if ($One) {
                                $TEM = array_column($One, 'hour_starttime');
                                array_multisort($TEM, SORT_ASC, $One);
                            }
                        }
                    }
                }
            }


            $result['field'] = $field;
            if ($dataList) {
                $result['list'] = $dataList;
            } else {
                $result['list'] = array();
            }
            if (!$dataList) {
                $res = array('error' => '1', 'errortip' => '暂无教师课表记录', 'result' => $result);
            } else {
                $res = array('error' => '0', 'errortip' => '获取信息', 'result' => $result);
            }

        } else {
            $result = array();
            $result['field'] = $dataList['field'];
            if ($dataList) {
                $result['list'] = $dataList['list'];
                $result['name'] = $dataList['name'];
                $res = array('error' => '0', 'errortip' => '获取信息', 'result' => $result);
            } else {
                $result['list'] = array();
                $result['name'] = '';
                $res = array('error' => '1', 'errortip' => $TimetableModel->errortip, 'result' => $result);
            }
        }
        if (!isset($request['language_type'])) {
            $request['language_type'] = 'zh';
        }
        ajax_return($res, $request['language_type']);
    }


    //获取所有教室一周的课表
    function classroomTimetableView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);

        $TimetableModel = new \Model\Smc\TimetableModel($request);
        $dataList = $TimetableModel->getRoomWeekTimetable($request);


        $result = array();
        $field = array();

        $field[0]["fieldname"] = "教室名称";
        $field[0]["fieldstring"] = "classroom_cnname";
        $field[0]["show"] = 1;
        $field[0]["custom"] = 0;
        $field[0]["is_title"] = 1;
        if ($dataList) {

            foreach ($dataList['week_date'] as $key => $value) {
                $field[$key + 1]["fieldname"] = "{$value['weekday']}";
                $field[$key + 1]["fieldstring"] = "{$value['week_day']}";
                $field[$key + 1]["show"] = 1;
                $field[$key + 1]["custom"] = 0;
            }
            unset($dataList['week_date']);
        } else {
            $enweekarray = array("Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday",);
            $weekarray = array("一", "二", "三", "四", "五", "六", "日",);

            for ($i = 0; $i < 7; $i++) {
                $day = date('Y-m-d', strtotime("+$i day", strtotime($request['hour_startday'])));
                $field[$i + 1]["fieldname"] = $day . '周' . $weekarray[$i];
                $field[$i + 1]["fieldstring"] = $enweekarray[$i];
                $field[$i + 1]["show"] = 1;
                $field[$i + 1]["custom"] = 0;
            }

        }

        $result['field'] = $field;
        if ($dataList) {
            $result['list'] = $dataList;
        } else {
            $result['list'] = array();
        }
        if (!$dataList) {
            $res = array('error' => '1', 'errortip' => '暂无教室课表记录', 'result' => $result);
        } else {
            $res = array('error' => '0', 'errortip' => '获取信息', 'result' => $result);
        }

        ajax_return($res, $request['language_type']);

    }

    function inviteTimetableApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);

        $TimetableModel = new \Model\Smc\TimetableModel($request);
        $dataList = $TimetableModel->inviteTimetable($request);

        $result = array();
        $field = array();

        $k=0;
        $field[$k]["fieldname"] = "周一";
        $field[$k]["fieldstring"] = "Monday";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "周二";
        $field[$k]["fieldstring"] = "Tuesday";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "周三";
        $field[$k]["fieldstring"] = "Wednesday";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "周四";
        $field[$k]["fieldstring"] = "Thursday";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "周五";
        $field[$k]["fieldstring"] = "Friday";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "周六";
        $field[$k]["fieldstring"] = "Saturday";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "周日";
        $field[$k]["fieldstring"] = "Sunday";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;


        $result['field'] = $field;
        if ($dataList) {
            $result['list'] = $dataList;
        } else {
            $result['list'] = array();
        }
        if (!$dataList) {
            $res = array('error' => '1', 'errortip' => $TimetableModel->errortip, 'result' => $result);
        } else {
            $res = array('error' => '0', 'errortip' => '获取信息', 'result' => $result);
        }

        ajax_return($res, $request['language_type']);

    }





    //------------------------------------------------------------------

    //获取教室课表的一周的教师

    function roomTableStafferApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);

        $TimetableModel = new \Model\Smc\TimetableModel($request);
        $dataList = $TimetableModel->getRoomTableStaffer($request);
        $res = array('error' => '0', 'errortip' => '获取信息', 'result' => $dataList);
        ajax_return($res, $request['language_type']);

    }

    function teaOneTimeTableView()
    {

        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $TableModel = new \Model\Smc\TimetableModel($request);
        $dataList = $TableModel->teaTodayTimeTable($request);
        $result = array();
        $field = array();
        $field[0]["fieldname"] = "时间/教师";
        $field[0]["fieldstring"] = "noon_name";
        $field[0]["show"] = 1;
        $field[0]["custom"] = 0;
        $field[0]["is_title"] = 1;

        if ($dataList['roomList']) {
            foreach ($dataList['roomList'] as $key => $val) {
                $field[$key + 1]["fieldname"] = $val['staffer_cnname'];
                $field[$key + 1]["fieldstring"] = $val['staffer_cnname'];
                $field[$key + 1]["show"] = 1;
                $field[$key + 1]["custom"] = 0;
            }
        }


        $result['field'] = $field;
        if ($dataList) {
            $result["list"] = $dataList['data'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => "暂无教师课表记录", 'result' => $result);

        }
        if (!isset($request['language_type'])) {
            $request['language_type'] = 'zh';
        }
        ajax_return($res, $request['language_type']);

    }

    //新加3个课表
    //教室详情课表
    function roomTimeTableListView()
    {


        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $timeModel = new \Model\Smc\TimetableModel($request);
        $dataList = $timeModel->roomTimeTableList($request);
        $result = array();
        $field = array();

        $field[0]["fieldname"] = "时间段";
        $field[0]["fieldstring"] = "time_quantum";
        $field[0]["show"] = 1;
        $field[0]["custom"] = 0;
        $field[0]["is_title"] = 1;
        if ($dataList) {

            foreach ($dataList['week_date'] as $key => $value) {
                $field[$key + 1]["fieldname"] = "{$value['weekday']}";
                $field[$key + 1]["fieldstring"] = "{$value['week_day']}";
                $field[$key + 1]["show"] = 1;
                $field[$key + 1]["custom"] = 0;
            }
            unset($dataList['week_date']);
        } else {
            $enweekarray = array("Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday");
            $weekarray = array("一", "二", "三", "四", "五", "六", "日",);

            for ($i = 0; $i < 7; $i++) {
                $day = date('Y-m-d', strtotime("+$i day", strtotime($request['hour_startday'])));
                $field[$i + 1]["fieldname"] = $day . '周' . $weekarray[$i];
                $field[$i + 1]["fieldstring"] = $enweekarray[$i];
                $field[$i + 1]["show"] = 1;
                $field[$i + 1]["custom"] = 0;
            }

        }

        $result['field'] = $field;
        if ($dataList) {
            $result['list'] = $dataList;
        } else {
            $result['list'] = array();
        }
        if ($dataList) {
            $res = array('error' => '0', 'errortip' => '获取信息', 'result' => $result);
        } else {
            $res = array('error' => '1', 'errortip' => '暂无教室课表记录', 'result' => $result);
        }

        ajax_return($res, $request['language_type']);


    }


    //教师详情课表
    function stafferTimeTableListView()
    {

        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $timeModel = new \Model\Smc\TimetableModel($request);
        $dataList = $timeModel->stafferTimeTableList($request);

        $result = array();
        $field = array();

        $field[0]["fieldname"] = "时间段";
        $field[0]["fieldstring"] = "time_quantum";
        $field[0]["show"] = 1;
        $field[0]["custom"] = 0;
        $field[0]["is_title"] = 1;
        if ($dataList) {

            foreach ($dataList['week_date'] as $key => $value) {
                $field[$key + 1]["fieldname"] = "{$value['weekday']}";
                $field[$key + 1]["fieldstring"] = "{$value['week_day']}";
                $field[$key + 1]["show"] = 1;
                $field[$key + 1]["custom"] = 0;
            }
            unset($dataList['week_date']);
        } else {
            $enweekarray = array("Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday");
            $weekarray = array("一", "二", "三", "四", "五", "六", "日",);

            for ($i = 0; $i < 7; $i++) {
                $day = date('Y-m-d', strtotime("+$i day", strtotime($request['hour_startday'])));
                $field[$i + 1]["fieldname"] = $day . '周' . $weekarray[$i];
                $field[$i + 1]["fieldstring"] = $enweekarray[$i];
                $field[$i + 1]["show"] = 1;
                $field[$i + 1]["custom"] = 0;
            }

        }
        $dataList = array_values($dataList);

        $result['field'] = $field;
        if ($dataList) {
            $result['list'] = $dataList;
        } else {
            $result['list'] = array();
        }
        if (!$dataList) {
            $res = array('error' => '1', 'errortip' => '暂无教师课表记录', 'result' => $result);
        } else {
            $res = array('error' => '0', 'errortip' => '获取信息', 'result' => $result);
        }

        ajax_return($res, $request['language_type']);


    }

    function weekTimeListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $timeModel = new \Model\Smc\TimetableModel($request);
        $dataList = $timeModel->weekTimeList($request);
        $fieldstring = array('lessonplan_week', 'class_cnname', 'class_enname', 'class_branch', 'time', 'hour_way_name', 'classroom_cnname', 'staffer_cnname');
        $fieldname = array('时间', '班级名称', '班级别名', '班级编号', '上课时间', '上课方式', '教室', '教师');
        $fieldcustom = array('1', '1', "1", "1", '1', '1', '1', '1');
        $fieldshow = array('1', '1', "1", "1", '1', '1', '1', '1');
        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["fieldname"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }
        $result['field'] = $field;
        $result['list'] = $dataList;

        if ($dataList) {
            $res = array('error' => '0', 'errortip' => '获取信息', 'result' => $result);
        } else {
            $res = array('error' => '1', 'errortip' => '暂无周次课程记录', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //预约类班级课表
    function appointTimeTableView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);

        $TimetableModel = new \Model\Smc\TimetableModel($request);
        $dataList = $TimetableModel->appointTimeTable($request);

        $result = array();
        $field = array();

        $field[0]["fieldname"] = "班级名称";
        $field[0]["fieldstring"] = "class_cnname";
        $field[0]["show"] = 1;
        $field[0]["custom"] = 0;
        $field[0]["is_title"] = 1;
        if ($dataList) {

            foreach ($dataList['week_date'] as $key => $value) {
                $field[$key + 1]["fieldname"] = "{$value['weekday']}";
                $field[$key + 1]["fieldstring"] = "{$value['week_day']}";
                $field[$key + 1]["show"] = 1;
                $field[$key + 1]["custom"] = 0;
            }
            unset($dataList['week_date']);
        } else {
            $enweekarray = array("Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday");
            $weekarray = array("一", "二", "三", "四", "五", "六", "日");

            for ($i = 0; $i < 7; $i++) {
                $day = date('Y-m-d', strtotime("+$i day", strtotime($request['hour_startday'])));
                $field[$i + 1]["fieldname"] = $day . '周' . $weekarray[$i];
                $field[$i + 1]["fieldstring"] = $enweekarray[$i];
                $field[$i + 1]["show"] = 1;
                $field[$i + 1]["custom"] = 0;
            }
        }
        $result['field'] = $field;
        if ($dataList) {
            $result['list'] = $dataList;
        } else {
            $result['list'] = array();
        }

        if (!$dataList) {
            $res = array('error' => '1', 'errortip' => '暂无班级课表记录', 'result' => $result);
        } else {
            $res = array('error' => '0', 'errortip' => '获取信息', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //期度类班级课表
    function periodTimeTableView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);

        $TimetableModel = new \Model\Smc\TimetableModel($request);
        $dataList = $TimetableModel->periodTimeTable($request);

        $result = array();
        $field = array();

        $field[0]["fieldname"] = "班级名称";
        $field[0]["fieldstring"] = "class_cnname";
        $field[0]["show"] = 1;
        $field[0]["custom"] = 0;
        $field[0]["is_title"] = 1;
        if ($dataList) {

            foreach ($dataList['week_date'] as $key => $value) {
                $field[$key + 1]["fieldname"] = "{$value['weekday']}";
                $field[$key + 1]["fieldstring"] = "{$value['week_day']}";
                $field[$key + 1]["show"] = 1;
                $field[$key + 1]["custom"] = 0;
            }
            unset($dataList['week_date']);
        } else {
            $enweekarray = array("Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday");
            $weekarray = array("一", "二", "三", "四", "五", "六", "日");

            for ($i = 0; $i < 7; $i++) {
                $day = date('Y-m-d', strtotime("+$i day", strtotime($request['fixedtime'])));
                $field[$i + 1]["fieldname"] = $day . '周' . $weekarray[$i];
                $field[$i + 1]["fieldstring"] = $enweekarray[$i];
                $field[$i + 1]["show"] = 1;
                $field[$i + 1]["custom"] = 0;
            }

        }

        $result['field'] = $field;
        if ($dataList) {
            $result['list'] = $dataList;
        } else {
            $result['list'] = array();
        }

        if (!$dataList) {
            $res = array('error' => '1', 'errortip' => '暂无班级课表记录', 'result' => $result);
        } else {
            $res = array('error' => '0', 'errortip' => '获取信息', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //公开课班级
    function openClassTimeTableView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);

        $TimetableModel = new \Model\Smc\TimetableModel($request);
        $dataList = $TimetableModel->openClassTimeTable($request);

        $result = array();
        $field = array();

        $field[0]["fieldname"] = "班级名称";
        $field[0]["fieldstring"] = "class_cnname";
        $field[0]["show"] = 1;
        $field[0]["custom"] = 0;
        $field[0]["is_title"] = 1;
        if ($dataList) {

            foreach ($dataList['week_date'] as $key => $value) {
                $field[$key + 1]["fieldname"] = "{$value['weekday']}";
                $field[$key + 1]["fieldstring"] = "{$value['week_day']}";
                $field[$key + 1]["show"] = 1;
                $field[$key + 1]["custom"] = 0;
            }
            unset($dataList['week_date']);
        } else {
            $enweekarray = array("Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday");
            $weekarray = array("一", "二", "三", "四", "五", "六", "日");

            for ($i = 0; $i < 7; $i++) {
                $day = date('Y-m-d', strtotime("+$i day", strtotime($request['hour_startday'])));
                $field[$i + 1]["fieldname"] = $day . '周' . $weekarray[$i];
                $field[$i + 1]["fieldstring"] = $enweekarray[$i];
                $field[$i + 1]["show"] = 1;
                $field[$i + 1]["custom"] = 0;
            }

        }

        $result['field'] = $field;
        if ($dataList) {
            $result['list'] = $dataList;
        } else {
            $result['list'] = array();
        }

        if (!$dataList) {
            $res = array('error' => '1', 'errortip' => '暂无班级课表记录', 'result' => $result);
        } else {
            $res = array('error' => '0', 'errortip' => '获取信息', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    /**
     * 报表下拉-教师空闲时间表
     * author: ling
     * 对应接口文档 0001
     */
    function getStafferTeachingTimeView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        if ($request['re_postbe_id'] == 0) {
            $request['account_class'] = 1;
        } else {
            $request['account_class'] = 0;
        }
        $TimetableModel = new \Model\Easx\TimetableModel($request);

        $dataList = $TimetableModel->getStafferTeachingTime($request);
        $result = array();
        $field = array();

        $k = 0;
        $field[$k]["fieldname"] = "教师中文名";
        $field[$k]["fieldstring"] = "staffer_cnname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "教师英文名";
        $field[$k]["fieldstring"] = "staffer_enname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "教师编号";
        $field[$k]["fieldstring"] = "staffer_branch";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = $dataList['month'] . "月份带班数";
        $field[$k]["fieldstring"] = "teaching_classnum";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = $dataList['month'] . "月份课时数";
        $field[$k]["fieldstring"] = "teaching_hournum";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "所属校区";
        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result['field'] = $field;
        if ($dataList) {
            $result['list'] = $dataList['list'];
            $result['all_num'] = $dataList['allnum'];
        } else {
            $result['list'] = array();
            $result['all_num'] = 0;
        }
        if (!$dataList) {
            $res = array('error' => '1', 'errortip' => '暂无教师课带课时间表', 'result' => $result);
        } else {
            $res = array('error' => '0', 'errortip' => '获取信息', 'result' => $result);
        }

        ajax_return($res, $request['language_type']);
    }

    /**
     * 获取集团的教学职务
     * author: ling
     * 对应接口文档 0001
     */
    function getCompanyTeaPostApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $postList = $this->DataControl->selectClear("select DISTINCT p.post_name,p.post_id 
            from gmc_company_post  as  p 
            left join gmc_staffer_postbe as pt ON pt.post_id = p.post_id
            where  p.post_isteaching = 1 and  p.company_id='{$request['company_id']}' and pt.staffer_id>0 and pt.postpart_id>0
            ");

        if (!$postList) {
            $postList = array();
        }
        $res = array('error' => '0', 'errortip' => '获取信息', 'result' => $postList);
        ajax_return($res, $request['language_type']);
    }


}
