<?php


namespace Work\Controller\Smcapi;


class AdvanceController extends viewTpl{
    public $u;
    public $t;
    public $c;

    function __construct() {
        parent::__construct ();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
    }

    function timesAdvancePriceApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\AdvanceModel($request);
        $res= $Model->timesAdvancePrice($request);
        if($res){
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $res);
        }else{
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }

    function itemsAdvancePriceApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\AdvanceModel($request);
        $res= $Model->itemsAdvancePrice($request);
        if($res){
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $res);
        }else{
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }

    function manageAdvancePriceApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\AdvanceModel($request);
        $res= $Model->manageAdvancePrice($request);
        if($res){
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $res);
        }else{
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }

    function courseListApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\AdvanceModel($request);
        $res= $Model->courseList($request);
        if($res['list']){
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $res);
        }else{
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $res);
        }
        ajax_return($res,$request['language_type']);
    }

    function advanceHistoryApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\AdvanceModel($request);
        $res = $Model->advanceHistory($request);
        $field = array();

        $field[0]["fieldstring"] = "log_playname";
        $field[0]["fieldname"] = "交易名称";
        $field[0]["show"] = 1;
        $field[0]["custom"] = 0;

        $field[1]["fieldstring"] = "log_playclass";
        $field[1]["fieldname"] = "交易类型";
        $field[1]["show"] = 1;
        $field[1]["custom"] = 0;

        $field[2]["fieldstring"] = "log_playamount";
        $field[2]["fieldname"] = "交易金额";
        $field[2]["show"] = 1;
        $field[2]["custom"] = 0;

        $field[3]["fieldstring"] = "log_playme";
        $field[3]["fieldname"] = "交易课次";
        $field[3]["show"] = 1;
        $field[3]["custom"] = 0;

        $field[4]["fieldstring"] = "log_reason";
        $field[4]["fieldname"] = "备注";
        $field[4]["show"] = 1;
        $field[4]["custom"] = 0;

        $field[5]["fieldstring"] = "staffer_cnname";
        $field[5]["fieldname"] = "操作人";
        $field[5]["show"] = 1;
        $field[5]["custom"] = 0;

        $field[6]["fieldstring"] = "log_time";
        $field[6]["fieldname"] = "交易时间";
        $field[6]["show"] = 1;
        $field[6]["custom"] = 0;

        $result = array();
        $result["field"] = $field;
        if ($res) {
            $result["fieldcustom"] = 0;
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '学员暂无预收余额信息', 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }

    function catSubscriptionAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\AdvanceModel($request);
        $res= $Model->catSubscription($request);
        if($res){
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '认缴成功', 'result' => $result);
        }else{
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);

        }
        ajax_return($res,$request['language_type']);
    }

    function turnOutAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\AdvanceModel($request);
        $res= $Model->turnOut($request);
        if($res){
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '转换成功', 'result' => $result);
        }else{
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);

        }
        ajax_return($res,$request['language_type']);

    }

    function turnOutTimesAction(){
        $request = Input('post.','','strip_tags,trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\AdvanceModel($request);
        $res= $Model->turnOutTimes($request);
        if($res){
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '转换成功', 'result' => $result);
        }else{
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);

        }
        ajax_return($res,$request['language_type']);

    }

    function writeOffTimesAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\AdvanceModel($request);
        $res= $Model->writeOffTimes($request);
        if($res){
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '课次冲销成功', 'result' => $result);
        }else{
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }

    function writeOffItemsAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\AdvanceModel($request);
        $res= $Model->writeOffItems($request);
        if($res){
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '杂费冲销成功', 'result' => $result);
        }else{
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }

    function writeOffManageAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\AdvanceModel($request);
        $res= $Model->writeOffManage($request);
        if($res){
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '管理费冲销成功', 'result' => $result);
        }else{
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }

}
