<?php


namespace Work\Controller\Smcapi;


class ChangeController extends viewTpl
{
    public $u;
    public $t;
    public $c;

    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
    }

    function homeView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ChangeModel = new \Model\Smc\ChangeModel($request);
        $res = $ChangeModel->classChange($request);
        $k = 0;
        $field = array();
        $field[$k]["fieldstring"] = "changelog_id";
        $field[$k]["fieldname"] = "异动ID";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["fieldname"] = "学员中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_enname";
        $field[$k]["fieldname"] = "学员英文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_branch";
        $field[$k]["fieldname"] = "学员编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "stuchange_name";
        $field[$k]["fieldname"] = "异动类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursetype_cnname";
        $field[$k]["fieldname"] = "异动班组名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursetype_branch";
        $field[$k]["fieldname"] = "异动班组编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "异动学校";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_cnname";
        $field[$k]["fieldname"] = "异动班级";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_enname";
        $field[$k]["fieldname"] = "异动班级别名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_branch";
        $field[$k]["fieldname"] = "异动班级编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "changelog_note";
        $field[$k]["fieldname"] = "异动描述";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "changelog_day";
        $field[$k]["fieldname"] = "异动日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "stustatus_isdel";
        $field[$k]["fieldname"] = "是否可以删除";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "change_pid";
        $field[$k]["fieldname"] = "异动编号";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "stuchange_code";
        $field[$k]["fieldname"] = "异动代码";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "staffer_cnname";
        $field[$k]["fieldname"] = "执行人";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_id";
        $field[$k]["fieldname"] = "班级id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;


        $result = array();

        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["list"] = $res['list'];
            $result["fieldcustom"] = 0;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["allnum"] = 0;
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无学员异动明细', 'result' => $result);

        }
        ajax_return($res, $request['language_type']);

    }

    function changeItemApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ChangeModel = new \Model\Smc\ChangeModel($request);
        $res = $ChangeModel->changeItem($request);
        $field = array();
        $field[0]["fieldstring"] = "changelog_id";
        $field[0]["fieldname"] = "异动ID";
        $field[0]["show"] = 0;
        $field[0]["custom"] = 0;

        $field[1]["fieldstring"] = "student_cnname";
        $field[1]["fieldname"] = "学员中文名";
        $field[1]["show"] = 1;
        $field[1]["custom"] = 0;

        $field[2]["fieldstring"] = "student_enname";
        $field[2]["fieldname"] = "学员英文名";
        $field[2]["show"] = 1;
        $field[2]["custom"] = 0;

        $field[3]["fieldstring"] = "student_branch";
        $field[3]["fieldname"] = "学生编号";
        $field[3]["show"] = 1;
        $field[3]["custom"] = 0;

        $field[4]["fieldstring"] = "stuchange_name";
        $field[4]["fieldname"] = "异动类型";
        $field[4]["show"] = 1;
        $field[4]["custom"] = 0;

        $field[5]["fieldstring"] = "school_cnname";
        $field[5]["fieldname"] = "异动学校";
        $field[5]["show"] = 1;
        $field[5]["custom"] = 0;

        $field[6]["fieldstring"] = "class_branch";
        $field[6]["fieldname"] = "异动班级";
        $field[6]["show"] = 1;
        $field[6]["custom"] = 0;

        $field[7]["fieldstring"] = "class_cnname";
        $field[7]["fieldname"] = "异动编号";
        $field[7]["show"] = 1;
        $field[7]["custom"] = 0;

        $field[8]["fieldstring"] = "changelog_note";
        $field[8]["fieldname"] = "异动描述";
        $field[8]["show"] = 1;
        $field[8]["custom"] = 0;

        $result = array();
        if ($res) {
            $result["fieldcustom"] = 0;
            $result["field"] = $field;
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["field"] = $field;
            $res = array('error' => 1, 'errortip' => $ChangeModel->errortip, 'result' => $result);

        }
        ajax_return($res, $request['language_type']);

    }

    function changeExamineListApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ChangeModel = new \Model\Smc\ChangeModel($request);
        $res = $ChangeModel->changeExamineList($request);
        $field = array();
        $field[0]["fieldstring"] = "change_id";
        $field[0]["fieldname"] = "异动ID";
        $field[0]["show"] = 0;
        $field[0]["custom"] = 0;

        $field[1]["fieldstring"] = "change_pid";
        $field[1]["fieldname"] = "异动编号";
        $field[1]["show"] = 0;
        $field[1]["custom"] = 0;

        $field[2]["fieldstring"] = "student_cnname";
        $field[2]["fieldname"] = "学员中文名";
        $field[2]["show"] = 1;
        $field[2]["custom"] = 1;

        $field[3]["fieldstring"] = "student_enname";
        $field[3]["fieldname"] = "学员英文名";
        $field[3]["show"] = 1;
        $field[3]["custom"] = 1;

        $field[4]["fieldstring"] = "student_branch";
        $field[4]["fieldname"] = "学号编号";
        $field[4]["show"] = 1;
        $field[4]["custom"] = 1;

        $field[5]["fieldstring"] = "from_stuchange_name";
        $field[5]["fieldname"] = "来源异动类型";
        $field[5]["show"] = 1;
        $field[5]["custom"] = 1;

        $field[6]["fieldstring"] = "from_school_cnname";
        $field[6]["fieldname"] = "来源异动学校";
        $field[6]["show"] = 1;
        $field[6]["custom"] = 1;

        $field[7]["fieldstring"] = "from_class_cnname";
        $field[7]["fieldname"] = "来源异动班级";
        $field[7]["show"] = 1;
        $field[7]["custom"] = 1;

        $field[8]["fieldstring"] = "to_stuchange_name";
        $field[8]["fieldname"] = "去向异动类型";
        $field[8]["show"] = 1;
        $field[8]["custom"] = 1;

        $field[9]["fieldstring"] = "to_school_cnname";
        $field[9]["fieldname"] = "去向异动学校";
        $field[9]["show"] = 1;
        $field[9]["custom"] = 1;

        $field[10]["fieldstring"] = "to_class_cnname";
        $field[10]["fieldname"] = "去向异动班级";
        $field[10]["show"] = 1;
        $field[10]["custom"] = 1;

        $field[11]["fieldstring"] = "change_reason";
        $field[11]["fieldname"] = "异动描述";
        $field[11]["show"] = 1;
        $field[11]["custom"] = 1;

        $field[12]["fieldstring"] = "change_day";
        $field[12]["fieldname"] = "异动日期";
        $field[12]["show"] = 1;
        $field[12]["custom"] = 1;

        $field[13]["fieldstring"] = "change_status";
        $field[13]["fieldname"] = "异动状态";
        $field[13]["show"] = 1;
        $field[13]["custom"] = 1;

        $field[14]["fieldstring"] = "change_workername";
        $field[14]["fieldname"] = "操作人";
        $field[14]["show"] = 1;
        $field[14]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => '暂无需要审核的学员异动信息', 'result' => $result);

        }
        ajax_return($res, $request['language_type']);

    }

    function classStudentApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ChangeModel = new \Model\Smc\ChangeModel($request);
        $res = $ChangeModel->classStudent($request);

        $field = array();
        $k = 0;
//        $field[$k]["fieldstring"] = "student_id";
//        $field[$k]["fieldname"] = "学员ID";
//        $field[$k]["show"] = 0;
//        $field[$k]["custom"] = 0;
//        $k++;

        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["fieldname"] = "学生中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "student_enname";
        $field[$k]["fieldname"] = "学生英文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "student_branch";
        $field[$k]["fieldname"] = "学生编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "student_sex";
        $field[$k]["fieldname"] = "性别";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "study_beginday";
        $field[$k]["fieldname"] = "入班日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $result = array();
        $result["field"] = $field;
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $ChangeModel->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function classCourseStuListApi()
    {

        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ChangeModel = new \Model\Smc\ChangeModel($request);
        $res = $ChangeModel->classCourseStuList($request);

        $contractOne = $this->getContract($request['company_id']);

        $field = array();
        $field[0]["fieldstring"] = "student_id";
        $field[0]["fieldname"] = "学员ID";
        $field[0]["show"] = 0;
        $field[0]["custom"] = 0;

        $field[1]["fieldstring"] = "student_branch";
        $field[1]["fieldname"] = "学员编号";
        $field[1]["show"] = 1;
        $field[1]["custom"] = 0;

        $field[2]["fieldstring"] = "student_cnname";
        $field[2]["fieldname"] = "学员中文名";
        $field[2]["show"] = 1;
        $field[2]["custom"] = 0;

        $field[3]["fieldstring"] = "student_enname";
        $field[3]["fieldname"] = "学员英文名";
        $field[3]["show"] = 1;
        $field[3]["custom"] = 0;

        $field[4]["fieldstring"] = "student_sex";
        $field[4]["fieldname"] = "性别";
        $field[4]["show"] = 1;
        $field[4]["custom"] = 0;

        if (!$contractOne || $contractOne['edition_id'] != '2') {
            $field[5]["fieldstring"] = "coursebalance_time";
            $field[5]["fieldname"] = "剩余课次";
            $field[5]["show"] = 1;
            $field[5]["custom"] = 0;
        }

        $result = array();
        $result["field"] = $field;
        if ($res) {
            $result["list"] = $res['list'];
            $result["fieldcustom"] = 0;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无需要入班的学员信息', 'result' => $result);

        }
        ajax_return($res, $request['language_type']);

    }

    function turnClassAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ChangeModel = new \Model\Smc\ChangeModel($request);
        $res = $ChangeModel->turnClass($request);
        if ($res) {
            $res = array('error' => 0, 'errortip' => '转班成功', 'result' => true);
        } else {
            $res = array('error' => 1, 'errortip' => $ChangeModel->errortip, 'result' => array());
        }
        ajax_return($res, $request['language_type']);
    }

    function outClassAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ChangeModel = new \Model\Smc\ChangeModel($request);
        $res = $ChangeModel->outClass($request);
        if ($res) {
            $res = array('error' => 0, 'errortip' => '出班成功', 'result' => true);
        } else {
            $res = array('error' => 1, 'errortip' => $ChangeModel->errortip, 'result' => array());
        }
        ajax_return($res, $request['language_type']);
    }

    function classEntryClassAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ChangeModel = new \Model\Smc\ChangeModel($request);
        $res = $ChangeModel->classEntryClass($request);
        if ($res) {
            $res = array('error' => 0, 'errortip' => '入班成功', 'result' => $res);
        } else {
            $res = array('error' => 1, 'errortip' => $ChangeModel->errortip, 'result' => array());
        }
        ajax_return($res, $request['language_type']);
    }


    //跨校
    function stuCrossSchoolAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ChangeModel = new \Model\Smc\ChangeModel($request);
        $res = $ChangeModel->stuCrossSchool($request);
        if ($res) {
            $res = array('error' => 0, 'errortip' => '跨校成功', 'result' => true);

            $this->addSmcWorkLog($request['company_id'], $request['school_id'], $request['staffer_id'], "缴费中心->旧生转入", '跨校', dataEncode($request));
        } else {
            $res = array('error' => 1, 'errortip' => $ChangeModel->errortip, 'result' => array());
        }
        ajax_return($res, $request['language_type']);
    }

    //获取学员所有课程的结转之后的信息
    function getForwardInfoApi()
    {

        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ChangeModel = new \Model\Smc\ChangeModel($request);
        $res = $ChangeModel->getForwardInfo($request);
        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $ChangeModel->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);


    }

    //转校
    function stuTransferSchoolAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ChangeModel = new \Model\Smc\ChangeModel($request);
        $res = $ChangeModel->stuTransferSchool($request);
        if ($res) {
            $res = array('error' => 0, 'errortip' => '转校成功', 'result' => true);
            $this->addSmcWorkLog($request['company_id'], $request['school_id'], $request['staffer_id'], "学员管理->学生列表", '转校', dataEncode($request));
        } else {
            $res = array('error' => 1, 'errortip' => $ChangeModel->errortip, 'result' => array());
        }
        ajax_return($res, $request['language_type']);
    }

    //转校 和 跨校 中待审核名单列表
    function getSchChangeApplyApi(){
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ChangeModel = new \Model\Smc\ChangeModel($request);
        $res = $ChangeModel->getSchChangeApplyApi($request);

        $k = 0;
        $field = array();
        $field[$k]["fieldstring"] = "school_shortname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_name";
        $field[$k]["fieldname"] = "学员姓名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_branch";
        $field[$k]["fieldname"] = "学员编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "allprice";
        $field[$k]["fieldname"] = "账户余额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "stuchange_code";
        $field[$k]["fieldname"] = "异动类型编号";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "stuchange_name";
        $field[$k]["fieldname"] = "异动类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "apply_reason";
        $field[$k]["fieldname"] = "申请原因";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "apply_remark";
        $field[$k]["fieldname"] = "申请备注";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "apply_time";
        $field[$k]["fieldname"] = "异动日期";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "apply_createtime";
        $field[$k]["fieldname"] = "申请时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "schoolnum";
        $field[$k]["fieldname"] = "来自几家学校";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "studentnum";
        $field[$k]["fieldname"] = "有多少个学生";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "staffer_ismanage";
        $field[$k]["fieldname"] = "是否技术账号 1 是 0 否";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;
        if ($res['list']) {
            $result["list"] = $res['list'];
            $result["schoolnum"] = $res['schoolnum'];
            $result["studentnum"] = $res['studentnum'];
            $result["staffer_ismanage"] = $res['staffer_ismanage'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无异动申请记录', 'result' => $result);

        }
        ajax_return($res, $request['language_type']);
    }

    //转校 和 跨校 待审核 同意操作
    function updateAgreeChangeApplyAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\ChangeModel($request);
        $Model->updateAgreeChangeApplyAction($request);
        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $Model->result), $request['language_type']);
    }

    //转校 和 跨校 待审核 拒绝操作
    function updateRefuseChangeApplyAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\ChangeModel($request);
        $Model->updateRefuseChangeApplyAction($request);
        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $Model->result), $request['language_type']);
    }

    //异动申请记录
    function getChangeApplyApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ChangeModel = new \Model\Smc\ChangeModel($request);
        $res = $ChangeModel->getChangeApplyApi($request);
        $k = 0;
        $field = array();
        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["fieldname"] = "学员中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_enname";
        $field[$k]["fieldname"] = "学员英文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_branch";
        $field[$k]["fieldname"] = "学员编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "stuchange_name";
        $field[$k]["fieldname"] = "异动类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "apply_status_name";
        $field[$k]["fieldname"] = "异动状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "apply_reason";
        $field[$k]["fieldname"] = "异动描述";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "apply_time";
        $field[$k]["fieldname"] = "异动日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "staffer_name";
        $field[$k]["fieldname"] = "执行人";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "to_staffer_name";
        $field[$k]["fieldname"] = "审批人";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "apply_refusereson";
        $field[$k]["fieldname"] = "拒绝原因";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();

        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["list"] = $res['list'];
            $result["fieldcustom"] = 0;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["allnum"] = 0;
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无异动申请记录', 'result' => $result);

        }
        ajax_return($res, $request['language_type']);
    }



    function newClassOneApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ChangeModel = new \Model\Smc\ChangeModel($request);
        $res = $ChangeModel->getNewclassOne($request);
        if ($res) {
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $res);
        } else {
            $res = array('error' => 0, 'errortip' => '暂无数据', 'result' => array());
        }
        ajax_return($res, $request['language_type']);

    }

    function getStuClassListApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ChangeModel = new \Model\Smc\ChangeModel($request);
        $res = $ChangeModel->getStuClassList($request);
        $field = array();
        $field[0]["fieldstring"] = "course_id";
        $field[0]["fieldname"] = "异动ID";
        $field[0]["show"] = 0;
        $field[0]["custom"] = 0;

        $field[1]["fieldstring"] = "pricing_id";
        $field[1]["fieldname"] = "姓名";
        $field[1]["show"] = 0;
        $field[1]["custom"] = 0;

        $field[2]["fieldstring"] = "class_cnname";
        $field[2]["fieldname"] = "班级名称";
        $field[2]["show"] = 1;
        $field[2]["custom"] = 0;

        $field[3]["fieldstring"] = "class_branch";
        $field[3]["fieldname"] = "班级编号";
        $field[3]["show"] = 1;
        $field[3]["custom"] = 0;

        $field[4]["fieldstring"] = "course_branch";
        $field[4]["fieldname"] = "课程别";
        $field[4]["show"] = 1;
        $field[4]["custom"] = 0;

        $field[5]["fieldstring"] = "coursebalance_time";
        $field[5]["fieldname"] = "剩余课次";
        $field[5]["show"] = 1;
        $field[5]["custom"] = 0;

        $field[6]["fieldstring"] = "coursebalance_figure";
        $field[6]["fieldname"] = "课程余额";
        $field[6]["show"] = 1;
        $field[6]["custom"] = 0;

        $field[7]["fieldstring"] = "courseforward_price";
        $field[7]["fieldname"] = "结转金额";
        $field[7]["show"] = 1;
        $field[7]["custom"] = 0;

        $field[8]["fieldstring"] = "class_id";
        $field[8]["fieldname"] = "班级ID";
        $field[8]["show"] = 0;
        $field[8]["custom"] = 0;

        $result = array();
        $result["field"] = $field;
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '学员没有入班信息', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function getNewClassListApi()
    {
        $request = Input('get.', '', 'strip_tags,trim');
        $this->ThisVerify($request);
        $ChangeModel = new \Model\Smc\ChangeModel($request);
        $res = $ChangeModel->getNewClassList($request);
        $field = array();
        $field[0]["fieldstring"] = "pricing_id";
        $field[0]["fieldname"] = "定价ID";
        $field[0]["show"] = 0;
        $field[0]["custom"] = 0;

        $field[1]["fieldstring"] = "course_id";
        $field[1]["fieldname"] = "课程ID";
        $field[1]["show"] = 0;
        $field[1]["custom"] = 0;

        $field[2]["fieldstring"] = "class_id";
        $field[2]["fieldname"] = "班级ID";
        $field[2]["show"] = 0;
        $field[2]["custom"] = 0;

        $field[3]["fieldstring"] = "class_cnname";
        $field[3]["fieldname"] = "班级名称";
        $field[3]["show"] = 1;
        $field[3]["custom"] = 0;

        $field[4]["fieldstring"] = "course_branch";
        $field[4]["fieldname"] = "课程别";
        $field[4]["show"] = 1;
        $field[4]["custom"] = 0;

        $field[5]["fieldstring"] = "classInfo";
        $field[5]["fieldname"] = "已上/计划";
        $field[5]["show"] = 1;
        $field[5]["custom"] = 0;

        $field[6]["fieldstring"] = "nonum";
        $field[6]["fieldname"] = "未上课次";
        $field[6]["show"] = 1;
        $field[6]["custom"] = 0;

        $field[7]["fieldstring"] = "tuition_originalprice";
        $field[7]["fieldname"] = "原价";
        $field[7]["show"] = 1;
        $field[7]["custom"] = 0;

        $field[8]["fieldstring"] = "tuition_sellingprice";
        $field[8]["fieldname"] = "销售价";
        $field[8]["show"] = 1;
        $field[8]["custom"] = 0;

        $field[9]["fieldstring"] = "unitsellprice";
        $field[9]["fieldname"] = "销售单价";
        $field[9]["show"] = 1;
        $field[9]["custom"] = 0;

        $field[9]["fieldstring"] = "course_nextid";
        $field[9]["fieldname"] = "下一级别课程ID";
        $field[9]["show"] = 0;
        $field[9]["custom"] = 0;

        $result = array();
        $result["field"] = $field;
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无数据', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }


    function transferClassAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ChangeModel = new \Model\Smc\ChangeModel($request);
        $res = $ChangeModel->transferClass($request);
        $result = array();
        if ($res) {
            $result["list"] = $res;
            $result = array('error' => 0, 'errortip' => '转班成功', 'result' => $result);
        } else {
            $result = array('error' => 1, 'errortip' => $ChangeModel->errortip, 'result' => $result);
        }
        ajax_return($result, $request['language_type']);

    }

    function transferNewClassListApi()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ChangeModel = new \Model\Smc\ChangeModel($request);
        $res = $ChangeModel->transferNewClassList($request);
        $result = array();
        if ($res) {
            $result["list"] = $res;
            $result = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
        } else {
            $result = array('error' => 1, 'errortip' => $ChangeModel->errortip, 'result' => $result);
        }
        ajax_return($result, $request['language_type']);
    }

    function transferClassPriceApi()
    {

        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ChangeModel = new \Model\Smc\ChangeModel($request);
        $res = $ChangeModel->transferClassPrice($request);
        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
        } else {
            $res = array('error' => 1, 'errortip' => $ChangeModel->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    function classStudentListApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ChangeModel = new \Model\Smc\ChangeModel($request);
        $res = $ChangeModel->classStudentList($request);
        $key = 0;
        $field = array();
        $field[$key]["fieldstring"] = "student_id";
        $field[$key]["fieldname"] = "学员ID";
        $field[$key]["show"] = 0;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldstring"] = "student_cnname";
        $field[$key]["fieldname"] = "学员中文名";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldstring"] = "student_enname";
        $field[$key]["fieldname"] = "学员英文名";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldstring"] = "student_branch";
        $field[$key]["fieldname"] = "学员编号";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldstring"] = "coursebalance_time";
        $field[$key]["fieldname"] = "剩余课次";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldstring"] = "coursebalance_figure";
        $field[$key]["fieldname"] = "课程余额";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldstring"] = "pricing_id";
        $field[$key]["fieldname"] = "定价ID";
        $field[$key]["show"] = 0;
        $field[$key]["custom"] = 0;
        $key++;

        $result = array();
        $result["field"] = $field;
        if ($res) {
            $result["list"] = $res;
            $result = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $result = array('error' => 1, 'errortip' => '班级暂无学员信息', 'result' => $result);
        }
        ajax_return($result, $request['language_type']);
    }

    function classForwardListApi()
    {

        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ChangeModel = new \Model\Smc\ChangeModel($request);
        $res = $ChangeModel->classForwardList($request);
        $field = array();
        $field[0]["fieldstring"] = "student_id";
        $field[0]["fieldname"] = "学员ID";
        $field[0]["show"] = 0;
        $field[0]["custom"] = 0;

        $field[1]["fieldstring"] = "student_cnname";
        $field[1]["fieldname"] = "学员中文名";
        $field[1]["show"] = 1;
        $field[1]["custom"] = 0;

        $field[2]["fieldstring"] = "coursebalance_time";
        $field[2]["fieldname"] = "剩余课次";
        $field[2]["show"] = 1;
        $field[2]["custom"] = 0;

        $field[3]["fieldstring"] = "coursebalance_unitexpend";
        $field[3]["fieldname"] = "购买单价";
        $field[3]["show"] = 1;
        $field[3]["custom"] = 0;

        $field[4]["fieldstring"] = "balance";
        $field[4]["fieldname"] = "可结转课程余额";
        $field[4]["show"] = 1;
        $field[4]["custom"] = 0;

        $field[5]["fieldstring"] = "courseforward_price";
        $field[5]["fieldname"] = "结转余额";
        $field[5]["show"] = 1;
        $field[5]["custom"] = 0;

        $result = array();
        $result["field"] = $field;
        if ($res) {
            $result["list"] = $res;
            $result = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $result = array('error' => 1, 'errortip' => $ChangeModel->errortip, 'result' => $result);
        }
        ajax_return($result, $request['language_type']);


    }


    function transferableToClassApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ChangeModel = new \Model\Smc\ChangeModel($request);
        $res = $ChangeModel->transferableToClass($request);
        $field = array();
        $field[0]["fieldstring"] = "pricing_id";
        $field[0]["fieldname"] = "定价ID";
        $field[0]["show"] = 0;
        $field[0]["custom"] = 0;

        $field[1]["fieldstring"] = "class_branch";
        $field[1]["fieldname"] = "班级编号";
        $field[1]["show"] = 1;
        $field[1]["custom"] = 0;

        $field[2]["fieldstring"] = "class_id";
        $field[2]["fieldname"] = "班级ID";
        $field[2]["show"] = 0;
        $field[2]["custom"] = 0;

        $field[3]["fieldstring"] = "class_cnname";
        $field[3]["fieldname"] = "班级名称";
        $field[3]["show"] = 1;
        $field[3]["custom"] = 0;

        $field[4]["fieldstring"] = "class_enname";
        $field[4]["fieldname"] = "班级别名";
        $field[4]["show"] = 1;
        $field[4]["custom"] = 0;

        $field[5]["fieldstring"] = "course_cnname";
        $field[5]["fieldname"] = "课程别";
        $field[5]["show"] = 1;
        $field[5]["custom"] = 0;

        $field[6]["fieldstring"] = "course_branch";
        $field[6]["fieldname"] = "课程别编号";
        $field[6]["show"] = 1;
        $field[6]["custom"] = 0;

        $field[7]["fieldstring"] = "classInfo";
        $field[7]["fieldname"] = "人数";
        $field[7]["show"] = 1;
        $field[7]["custom"] = 0;
        $field[7]["isSquareProgress"] = true;

        $field[8]["fieldstring"] = "nonum";
        $field[8]["fieldname"] = "未上课次";
        $field[8]["show"] = 1;
        $field[8]["custom"] = 0;

        $field[9]["fieldstring"] = "tuition_originalprice";
        $field[9]["fieldname"] = "原价";
        $field[9]["show"] = 1;
        $field[9]["custom"] = 0;

        $field[10]["fieldstring"] = "tuition_sellingprice";
        $field[10]["fieldname"] = "销售价";
        $field[10]["show"] = 1;
        $field[10]["custom"] = 0;

        $field[11]["fieldstring"] = "unitsellprice";
        $field[11]["fieldname"] = "销售单价";
        $field[11]["show"] = 1;
        $field[11]["custom"] = 0;

        $result = array();
        $result["field"] = $field;
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无班级信息', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function canTransferClassListApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ChangeModel = new \Model\Smc\ChangeModel($request);
        $res = $ChangeModel->canTransferClassList($request);
        $field = array();
        $field[0]["fieldstring"] = "pricing_id";
        $field[0]["fieldname"] = "定价ID";
        $field[0]["show"] = 0;
        $field[0]["custom"] = 0;

        $field[1]["fieldstring"] = "class_branch";
        $field[1]["fieldname"] = "班级编号";
        $field[1]["show"] = 1;
        $field[1]["custom"] = 0;

        $field[2]["fieldstring"] = "class_id";
        $field[2]["fieldname"] = "班级ID";
        $field[2]["show"] = 0;
        $field[2]["custom"] = 0;

        $field[3]["fieldstring"] = "class_cnname";
        $field[3]["fieldname"] = "班级名称";
        $field[3]["show"] = 1;
        $field[3]["custom"] = 0;

        $field[4]["fieldstring"] = "class_enname";
        $field[4]["fieldname"] = "班级别名";
        $field[4]["show"] = 1;
        $field[4]["custom"] = 0;

        $field[5]["fieldstring"] = "course_cnname";
        $field[5]["fieldname"] = "课程别";
        $field[5]["show"] = 1;
        $field[5]["custom"] = 0;

        $field[6]["fieldstring"] = "course_branch";
        $field[6]["fieldname"] = "课程别编号";
        $field[6]["show"] = 1;
        $field[6]["custom"] = 0;

        $field[7]["fieldstring"] = "classInfo";
        $field[7]["fieldname"] = "人数";
        $field[7]["show"] = 1;
        $field[7]["custom"] = 0;
        $field[7]["isSquareProgress"] = true;

        $field[8]["fieldstring"] = "nonum";
        $field[8]["fieldname"] = "未上课次";
        $field[8]["show"] = 1;
        $field[8]["custom"] = 0;

        $field[9]["fieldstring"] = "tuition_originalprice";
        $field[9]["fieldname"] = "原价";
        $field[9]["show"] = 1;
        $field[9]["custom"] = 0;

        $field[10]["fieldstring"] = "tuition_sellingprice";
        $field[10]["fieldname"] = "销售价";
        $field[10]["show"] = 1;
        $field[10]["custom"] = 0;

        $field[11]["fieldstring"] = "unitsellprice";
        $field[11]["fieldname"] = "销售单价";
        $field[11]["show"] = 1;
        $field[11]["custom"] = 0;

        $result = array();
        $result["field"] = $field;
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无班级信息', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function transferInfoApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ChangeModel = new \Model\Smc\ChangeModel($request);
        $res = $ChangeModel->transferInfo($request);
        $result = array();
        if ($res) {
            $result["list"] = $res;
            $result = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
        } else {
            $result = array('error' => 1, 'errortip' => $ChangeModel->errortip, 'result' => $result);
        }
        ajax_return($result, $request['language_type']);
    }

    function promotionAction()
    {

        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ChangeModel = new \Model\Smc\ChangeModel($request);
        $res = $ChangeModel->promotion($request);

        $field = array();
        $field[0]["fieldstring"] = "studnet_id";
        $field[0]["fieldname"] = "学员ID";
        $field[0]["show"] = 0;
        $field[0]["custom"] = 0;

        $field[1]["fieldstring"] = "student_cnname";
        $field[1]["fieldname"] = "学员中文名";
        $field[1]["show"] = 1;
        $field[1]["custom"] = 0;

        $field[2]["fieldstring"] = "student_branch";
        $field[2]["fieldname"] = "学员编号";
        $field[2]["show"] = 1;
        $field[2]["custom"] = 0;

        $field[3]["fieldstring"] = "order_pid";
        $field[3]["fieldname"] = "订单编号";
        $field[3]["show"] = 1;
        $field[3]["custom"] = 0;

        $field[4]["fieldstring"] = "order_paymentprice";
        $field[4]["fieldname"] = "订单总价";
        $field[4]["show"] = 1;
        $field[4]["custom"] = 0;

        $field[5]["fieldstring"] = "discount_price";
        $field[5]["fieldname"] = "优惠金额";
        $field[5]["show"] = 1;
        $field[5]["custom"] = 0;

        $field[6]["fieldstring"] = "order_paidprice";
        $field[6]["fieldname"] = "已支付金额";
        $field[6]["show"] = 1;
        $field[6]["custom"] = 0;

        $field[7]["fieldstring"] = "order_arrearageprice";
        $field[7]["fieldname"] = "欠费金额";
        $field[7]["show"] = 1;
        $field[7]["custom"] = 0;


        $result = array();
        $result["field"] = $field;
        if ($res) {
            $result["list"] = $res;
            $result = array('error' => 0, 'errortip' => '升班成功', 'result' => $result);
        } else {
            $result = array('error' => 1, 'errortip' => $ChangeModel->errortip, 'result' => $result);
        }
        ajax_return($result, $request['language_type']);

    }

    function dismantleClassAction()
    {

        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ChangeModel = new \Model\Smc\ChangeModel($request);
        $res = $ChangeModel->dismantleClass($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $result = array('error' => 0, 'errortip' => '拆班成功', 'result' => $result);
        } else {
            $result = array('error' => 1, 'errortip' => $ChangeModel->errortip, 'result' => $result);
        }
        ajax_return($result, $request['language_type']);

    }

    function promotionNewClassOneApi()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ChangeModel = new \Model\Smc\ChangeModel($request);
        $res = $ChangeModel->promotionNewClassOne($request);
        $result = array();
        if ($res) {
            $result["list"] = $res;
            $result = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
        } else {
            $result = array('error' => 1, 'errortip' => $ChangeModel->errortip, 'result' => $result);
        }
        ajax_return($result, $request['language_type']);
    }

    function promotionNewClassListApi()
    {

        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ChangeModel = new \Model\Smc\ChangeModel($request);
        $res = $ChangeModel->promotionNewClassList($request);
        $result = array();
        if ($res) {
            $result["list"] = $res;
            $result = array('error' => 0, 'errortip' => '升班成功', 'result' => $result);
        } else {
            $result = array('error' => 1, 'errortip' => $ChangeModel->errortip, 'result' => $result);
        }
        ajax_return($result, $request['language_type']);

    }

    function upgradeOrderStuListApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ChangeModel = new \Model\Smc\ChangeModel($request);
        $res = $ChangeModel->upgradeOrderStuList($request);
        $field = array();
        $key = 0;
        $field[$key]["fieldstring"] = "student_id";
        $field[$key]["fieldname"] = "学员ID";
        $field[$key]["show"] = 0;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldstring"] = "student_cnname";
        $field[$key]["fieldname"] = "学员中文名";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldstring"] = "student_enname";
        $field[$key]["fieldname"] = "学员英文名";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldstring"] = "student_branch";
        $field[$key]["fieldname"] = "学员编号";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldstring"] = "coursebalance_time";
        $field[$key]["fieldname"] = "剩余课次";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldstring"] = "coursebalance_figure";
        $field[$key]["fieldname"] = "课程余额";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldstring"] = "pricing_id";
        $field[$key]["fieldname"] = "定价ID";
        $field[$key]["show"] = 0;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldstring"] = "is_have";
        $field[$key]["fieldname"] = "是否可以升班";
        $field[$key]["show"] = 0;
        $field[$key]["custom"] = 0;
        $key++;

        $result = array();
        $result["field"] = $field;
        if ($res) {
            $result["list"] = $res;
            $result = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $result = array('error' => 1, 'errortip' => '班级暂无学员信息', 'result' => $result);
        }
        ajax_return($result, $request['language_type']);
    }

    function upgradeOrderNewClassListApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ChangeModel = new \Model\Smc\ChangeModel($request);
        $res = $ChangeModel->upgradeOrderNewClassList($request);
        $result = array();
        if ($res) {
            $result["list"] = $res;
            $result = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
        } else {
            $result = array('error' => 1, 'errortip' => $ChangeModel->errortip, 'result' => $result);
        }
        ajax_return($result, $request['language_type']);
    }

    function upgradeStuListApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ChangeModel = new \Model\Smc\ChangeModel($request);
        $res = $ChangeModel->upgradeStuList($request);

        $contractOne = $this->getContract($request['company_id']);

        $field = array();
        $field[0]["fieldstring"] = "student_id";
        $field[0]["fieldname"] = "学员ID";
        $field[0]["show"] = 0;
        $field[0]["custom"] = 0;

        $field[1]["fieldstring"] = "student_cnname";
        $field[1]["fieldname"] = "学员中文名";
        $field[1]["show"] = 1;
        $field[1]["custom"] = 0;

        $field[2]["fieldstring"] = "student_branch";
        $field[2]["fieldname"] = "学员编号";
        $field[2]["show"] = 1;
        $field[2]["custom"] = 0;

        if (!$contractOne || $contractOne['edition_id'] != '2') {
            $field[3]["fieldstring"] = "coursebalance_time";
            $field[3]["fieldname"] = "剩余课次";
            $field[3]["show"] = 1;
            $field[3]["custom"] = 0;

            $field[4]["fieldstring"] = "coursebalance_figure";
            $field[4]["fieldname"] = "课程余额";
            $field[4]["show"] = 1;
            $field[4]["custom"] = 0;

            $field[5]["fieldstring"] = "pricing_id";
            $field[5]["fieldname"] = "定价ID";
            $field[5]["show"] = 0;
            $field[5]["custom"] = 0;

            $field[6]["fieldstring"] = "is_have_name";
            $field[6]["fieldname"] = "拥有情况";
            $field[6]["show"] = 1;
            $field[6]["custom"] = 0;
        }

        $result = array();
        $result["field"] = $field;
        if ($res) {
            $result["list"] = $res;
            $result = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $result = array('error' => 1, 'errortip' => $ChangeModel->errortip, 'result' => $result);
        }
        ajax_return($result, $request['language_type']);
    }

    function transferNewClassOneApi()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ChangeModel = new \Model\Smc\ChangeModel($request);
        $res = $ChangeModel->transferNewClassOne($request);
        $result = array();
        if ($res) {
            $result["list"] = $res;
            $result = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
        } else {
            $result = array('error' => 1, 'errortip' => $ChangeModel->errortip, 'result' => $result);
        }
        ajax_return($result, $request['language_type']);
    }

    function upgradeOrderAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ChangeModel = new \Model\Smc\ChangeModel($request);
        $res = $ChangeModel->upgradeOrder($request);
        $result = array();
        if ($res) {
            $result["list"] = $res;
            $result = array('error' => 0, 'errortip' => $ChangeModel->errortip, 'result' => $result);
        } else {
            $result = array('error' => 1, 'errortip' => $ChangeModel->errortip, 'result' => $result);
        }
        ajax_return($result, $request['language_type']);
    }

    function upgradeAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ChangeModel = new \Model\Smc\ChangeModel($request);
        $res = $ChangeModel->upgrade($request);
        $result = array();
        if ($res) {
            $result["list"] = $res;
            $result = array('error' => 0, 'errortip' => '升班成功', 'result' => $result);
        } else {
            $result = array('error' => 1, 'errortip' => $ChangeModel->errortip, 'result' => $result);
        }
        ajax_return($result, $request['language_type']);

    }

    function stuBackSchoolAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ChangeModel = new \Model\Smc\ChangeModel($request);
        $res = $ChangeModel->goBackToSchool($request);
        $result = array();
        if ($res) {
            $result["list"] = $res;
            $result = array('error' => 0, 'errortip' => '流失复读成功', 'result' => $result);
        } else {
            $result = array('error' => 1, 'errortip' => $ChangeModel->errortip, 'result' => $result);
        }
        ajax_return($result, $request['language_type']);
    }

    function stuClassLossAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ChangeModel = new \Model\Smc\ChangeModel($request);
        $res = $ChangeModel->classLoss($request);
        $result = array();
        if ($res) {
            $result["list"] = $res;
            $result = array('error' => 0, 'errortip' => '班级流失成功', 'result' => $result);
        } else {
            $result = array('error' => 1, 'errortip' => $ChangeModel->errortip, 'result' => $result);
        }
        ajax_return($result, $request['language_type']);
    }

    function classGraduationAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ChangeModel = new \Model\Smc\ChangeModel($request);
        $res = $ChangeModel->graduation($request);
        $result = array();
        if ($res) {
            $result["list"] = $res;
            $result = array('error' => 0, 'errortip' => '班级毕业成功', 'result' => $result);
        } else {
            $result = array('error' => 1, 'errortip' => $ChangeModel->errortip, 'result' => $result);
        }
        ajax_return($result, $request['language_type']);
    }

    function stuLossAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ChangeModel = new \Model\Smc\ChangeModel($request);
        $res = $ChangeModel->stuLoss($request);
        $result = array();
        if ($res) {
            $result["list"] = $res;
            $result = array('error' => 0, 'errortip' => '流失成功', 'result' => $result);
        } else {
            $result = array('error' => 1, 'errortip' => $ChangeModel->errortip, 'result' => $result);
        }
        ajax_return($result, $request['language_type']);
    }

    function getlostChangeListApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ChangeModel = new \Model\Smc\ChangeModel($request);
        $res = $ChangeModel->lostChange($request);
        $k = 0;
        $field = array();
        $field[$k]["fieldstring"] = "changelog_id";
        $field[$k]["fieldname"] = "异动ID";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "student_name";
        $field[$k]["fieldname"] = "学员姓名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["ismethod"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_branch";
        $field[$k]["fieldname"] = "学员编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "stuchange_name";
        $field[$k]["fieldname"] = "异动类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "changelog_day";
        $field[$k]["fieldname"] = "流失日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursetype_cnname";
        $field[$k]["fieldname"] = "流失班组";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "changelog_category";
        $field[$k]["fieldname"] = "流失类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "reason_code";
        $field[$k]["fieldname"] = "原因代码";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "changelog_note";
        $field[$k]["fieldname"] = "流失原因";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "connect_info";
        $field[$k]["fieldname"] = "流失电访结果";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["ismethod"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "staffer_name";
        $field[$k]["fieldname"] = "操作人";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "create_time";
        $field[$k]["fieldname"] = "操作时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "enrolled_status";
        $field[$k]["fieldname"] = "在校状态";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "outlog_id";
        $field[$k]["fieldname"] = "是否已注销";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $result = array();

        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["list"] = $res['list'];
            $result["fieldcustom"] = 0;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["allnum"] = 0;
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无学员异动明细', 'result' => $result);

        }
        ajax_return($res, $request['language_type']);

    }

    function updChangeAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ChangeModel = new \Model\Smc\ChangeModel($request);
        $res = $ChangeModel->updChange($request);
        $result = array();
        if ($res) {
            $result = array('error' => 0, 'errortip' => '编辑成功', 'result' => $result);
        } else {
            $result = array('error' => 1, 'errortip' => $ChangeModel->errortip, 'result' => $result);
        }
        ajax_return($result, $request['language_type']);
    }

    function cancelChangeAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);

//        $a = $this->DataControl->selectOne("
//            SELECT
//                p.post_istopjob
//            FROM
//                gmc_staffer_postbe AS sp
//            LEFT JOIN gmc_company_post AS p ON p.post_id = sp.post_id
//            WHERE sp.postbe_id = '{$request['re_postbe_id']}'");
//
//        if($a['post_istopjob'] != '1'){
//            ajax_return(array('error' => 1,'errortip' => "您没有权限删除，请联系校长删除异动！"));
//        }

        if ($request['re_postbe_id'] != '0') {
            ajax_return(array('error' => 1, 'errortip' => "暂时只有管理员可拥有删除权限"));
        }

        $ChangeModel = new \Model\Smc\ChangeModel($request);
        $res = $ChangeModel->cancelChange($request);
        $result = array();
        if ($res) {
            $result["list"] = $res;
            $this->addSmcWorkLog($request['company_id'], $request['school_id'], $request['staffer_id'], "班务管理->学员异动管理", '删除异动', dataEncode($request));
            $result = array('error' => 0, 'errortip' => '异动记录删除成功', 'result' => $result);
        } else {
            $result = array('error' => 1, 'errortip' => $ChangeModel->errortip, 'result' => $result);
        }
        ajax_return($result, $request['language_type']);
    }

    function stuClassLossListApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ChangeModel = new \Model\Smc\ChangeModel($request);
        $res = $ChangeModel->stuClassLossList($request);

        $field = array();
        $field[0]["fieldstring"] = "class_id";
        $field[0]["fieldname"] = "班级ID";
        $field[0]["show"] = 0;
        $field[0]["custom"] = 0;

        $field[1]["fieldstring"] = "class_cnname";
        $field[1]["fieldname"] = "班级名称";
        $field[1]["show"] = 1;
        $field[1]["custom"] = 0;

        $field[2]["fieldstring"] = "class_branch";
        $field[2]["fieldname"] = "班级编号";
        $field[2]["show"] = 1;
        $field[2]["custom"] = 0;

        $field[3]["fieldstring"] = "course_cnname";
        $field[3]["fieldname"] = "课程别名称";
        $field[3]["show"] = 1;
        $field[3]["custom"] = 0;

        $field[4]["fieldstring"] = "course_branch";
        $field[4]["fieldname"] = "课程别编号";
        $field[4]["show"] = 1;
        $field[4]["custom"] = 0;

        $result = array();
        $result["field"] = $field;
        if ($res) {
            $result["list"] = $res;
            $result = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
        } else {
            $result = array('error' => 1, 'errortip' => $ChangeModel->errortip, 'result' => $result);
        }
        ajax_return($result, $request['language_type']);
    }

    function getGoodsListApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ChangeModel = new \Model\Smc\ChangeModel($request);
        $res = $ChangeModel->getGoodsList($request);

        $field = array();
        $field[0]["fieldstring"] = "class_id";
        $field[0]["fieldname"] = "班级ID";
        $field[0]["show"] = 0;
        $field[0]["custom"] = 0;

        $field[1]["fieldstring"] = "class_cnname";
        $field[1]["fieldname"] = "班级名称";
        $field[1]["show"] = 1;
        $field[1]["custom"] = 0;

        $field[2]["fieldstring"] = "class_branch";
        $field[2]["fieldname"] = "班级编号";
        $field[2]["show"] = 1;
        $field[2]["custom"] = 0;

        $field[3]["fieldstring"] = "course_cnname";
        $field[3]["fieldname"] = "课程别名称";
        $field[3]["show"] = 1;
        $field[3]["custom"] = 0;

        $field[4]["fieldstring"] = "course_branch";
        $field[4]["fieldname"] = "课程别编号";
        $field[4]["show"] = 1;
        $field[4]["custom"] = 0;

        $result = array();
        $result["field"] = $field;
        if ($res) {
            $result["list"] = $res;
            $result = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
        } else {
            $result = array('error' => 1, 'errortip' => $ChangeModel->errortip, 'result' => $result);
        }
        ajax_return($result, $request['language_type']);
    }

    function getClassStudentApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ChangeModel = new \Model\Smc\ChangeModel($request);
        $res = $ChangeModel->getClassStudent($request);

        $k = 0;
        $field = array();
        $field[$k]["fieldstring"] = "student_id";
        $field[$k]["fieldname"] = "学员ID";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["fieldname"] = "学员姓名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "student_enname";
        $field[$k]["fieldname"] = "学员英文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "student_branch";
        $field[$k]["fieldname"] = "学员编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "balance";
        $field[$k]["fieldname"] = "账户余额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "student_forwardprice";
        $field[$k]["fieldname"] = "结转金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "family_mobile";
        $field[$k]["fieldname"] = "联系电话";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "goodStr";
        $field[$k]["fieldname"] = "拥有情况";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $result = array();
        $result["field"] = $field;
        if ($res) {
            $result["list"] = $res;
            $result = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
        } else {
            $result = array('error' => 1, 'errortip' => $ChangeModel->errortip, 'result' => $result);
        }
        ajax_return($result, $request['language_type']);
    }

    function getStuCrossSchoolApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\ChangeModel($request);
        $res = $Model->getStuCrossSchool($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $result = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
        } else {
            $result = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($result, $request['language_type']);
    }

    function transferBalanceAction()
    {

        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);

//        $a = $this->DataControl->selectOne("
//            SELECT
//                p.post_istopjob
//            FROM
//                gmc_staffer_postbe AS sp
//            LEFT JOIN gmc_company_post AS p ON p.post_id = sp.post_id
//            WHERE sp.postbe_id = '{$request['re_postbe_id']}'");
//
//        if($a['post_istopjob'] != '1'){
//            ajax_return(array('error' => 1,'errortip' => "您没有权限转移金额，请联系校长操作！"));
//        }

        $ChangeModel = new \Model\Smc\ChangeModel($request);
        $res = $ChangeModel->transferBalance($request);
        $result = array();
        if ($res) {
            $result["list"] = $res;
            $this->addSmcWorkLog($request['company_id'], $request['school_id'], $request['staffer_id'], "班务管理->学员管理", '账户余额转移', dataEncode($request));
            $result = array('error' => 0, 'errortip' => '账户余额转移成功', 'result' => $result);
        } else {
            $result = array('error' => 1, 'errortip' => $ChangeModel->errortip, 'result' => $result);
        }
        ajax_return($result, $request['language_type']);
    }

    function getStuClassTypeListApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ChangeModel = new \Model\Smc\ChangeModel($request);
        $res = $ChangeModel->getStuClassTypeList($request);
        $result = array();
        if ($res) {
            $result["list"] = $res;
            $result = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
        } else {
            $result = array('error' => 1, 'errortip' => $ChangeModel->errortip, 'result' => $result);
        }
        ajax_return($result, $request['language_type']);
    }

    function getFreeapplyListApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ChangeModel = new \Model\Smc\ChangeModel($request);
        $res = $ChangeModel->getFreeapplyList($request);
        $k = 0;
        $field = array();
        $field[$k]["fieldstring"] = "class_cnname";
        $field[$k]["fieldname"] = "班级名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_branch";
        $field[$k]["fieldname"] = "班级编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "freeapply_type_name";
        $field[$k]["fieldname"] = "申请类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "freeapply_day";
        $field[$k]["fieldname"] = "申请上课日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "timerange";
        $field[$k]["fieldname"] = "上课时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "main_staffer_cnname";
        $field[$k]["fieldname"] = "主教教师";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "fu_staffer_cnname";
        $field[$k]["fieldname"] = "助教教师";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "classroom_cnname";
        $field[$k]["fieldname"] = "上课教室";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "freeapply_reason";
        $field[$k]["fieldname"] = "申请原因";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "staffer_cnname";
        $field[$k]["fieldname"] = "申请人";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "freeapply_createtime";
        $field[$k]["fieldname"] = "申请时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "freeapply_status_name";
        $field[$k]["fieldname"] = "审核状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "freeapply_note";
        $field[$k]["fieldname"] = "审核备注";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();

        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["list"] = $res['list'];
            $result["fieldcustom"] = 0;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["allnum"] = 0;
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $ChangeModel->errortip, 'result' => $result);

        }
        ajax_return($res, $request['language_type']);
    }

    function revokeFreeapplyAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ChangeModel = new \Model\Smc\ChangeModel($request);
        $res = $ChangeModel->revokeFreeapply($request);
        $result = array();
        if ($res) {
            $result["list"] = $res;
            $result = array('error' => 0, 'errortip' => '撤销成功', 'result' => $result);
        } else {
            $result = array('error' => 1, 'errortip' => $ChangeModel->errortip, 'result' => $result);
        }
        ajax_return($result, $request['language_type']);
    }

    function getAdjustapplyListApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ChangeModel = new \Model\Smc\ChangeModel($request);
        $res = $ChangeModel->getAdjustapplyList($request);
        $k = 0;
        $field = array();
        $field[$k]["fieldstring"] = "adjustapply_type_name";
        $field[$k]["fieldname"] = "申请类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursetype_cnname";
        $field[$k]["fieldname"] = "所属班组";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_cnname";
        $field[$k]["fieldname"] = "班级名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_branch";
        $field[$k]["fieldname"] = "班级编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "hour_day";
        $field[$k]["fieldname"] = "原上课日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "adjustapply_day";
        $field[$k]["fieldname"] = "申请调课日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "days_difference";
        $field[$k]["fieldname"] = "间隔天数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "adjustapply_class_name";
        $field[$k]["fieldname"] = "调课原因";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "adjustapply_reason";
        $field[$k]["fieldname"] = "调课备注";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "adjustapply_fileurl_name";
        $field[$k]["fieldname"] = "附件";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["ismethod"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "staffer_cnname";
        $field[$k]["fieldname"] = "申请人";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "adjustapply_createtime";
        $field[$k]["fieldname"] = "申请时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "adjustapply_status_name";
        $field[$k]["fieldname"] = "审核状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "adjustapply_note";
        $field[$k]["fieldname"] = "审核备注";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();

        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["list"] = $res['list'];
            $result["fieldcustom"] = 0;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["allnum"] = 0;
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $ChangeModel->errortip, 'result' => $result);

        }
        ajax_return($res, $request['language_type']);
    }

    function revokeAdjustapplyAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ChangeModel = new \Model\Smc\ChangeModel($request);
        $res = $ChangeModel->revokeAdjustapply($request);
        $result = array();
        if ($res) {
            $result["list"] = $res;
            $result = array('error' => 0, 'errortip' => '撤销成功', 'result' => $result);
        } else {
            $result = array('error' => 1, 'errortip' => $ChangeModel->errortip, 'result' => $result);
        }
        ajax_return($result, $request['language_type']);
    }

    function batchRevokeAdjustapplyAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ChangeModel = new \Model\Smc\ChangeModel($request);
        $res = $ChangeModel->batchRevokeAdjustapply($request);
        $result = array();
        if ($res) {
            $result["list"] = $res;
            $result = array('error' => 0, 'errortip' => '撤销成功', 'result' => $result);
        } else {
            $result = array('error' => 1, 'errortip' => $ChangeModel->errortip, 'result' => $result);
        }
        ajax_return($result, $request['language_type']);
    }


    function applySchAdjustapplyAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ChangeModel = new \Model\Smc\ChangeModel($request);
        $res = $ChangeModel->applySchAdjustapply($request);
        $result = array();
        if ($res) {
            $result["list"] = $res;
            $result = array('error' => 0, 'errortip' => '申请成功', 'result' => $result);
        } else {
            $result = array('error' => 1, 'errortip' => $ChangeModel->errortip, 'result' => $result);
        }
        ajax_return($result, $request['language_type']);
    }

    function getJindieErrorListApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ChangeModel = new \Model\Smc\ChangeModel($request);
        $res = $ChangeModel->getJindieErrorList($request);
        $k = 0;
        $field = array();
        $field[$k]["fieldstring"] = "trading_pid";
        $field[$k]["fieldname"] = "退费交易编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["ismethod"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["fieldname"] = "学员姓名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["ismethod"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_branch";
        $field[$k]["fieldname"] = "学员编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "order_status_name";
        $field[$k]["fieldname"] = "订单状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "refund_payprice";
        $field[$k]["fieldname"] = "本次退费金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "error_message";
        $field[$k]["fieldname"] = "异常提示";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "create_time";
        $field[$k]["fieldname"] = "触发时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "is_handle_name";
        $field[$k]["fieldname"] = "处理状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "handle_staffer_cnname";
        $field[$k]["fieldname"] = "处理人";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "handle_time";
        $field[$k]["fieldname"] = "处理时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;


        $result = array();

        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["list"] = $res['list'];
            $result["fieldcustom"] = 0;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["allnum"] = 0;
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $ChangeModel->errortip, 'result' => $result);

        }
        ajax_return($res, $request['language_type']);
    }


    function submitErrorHandleAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ChangeModel = new \Model\Smc\ChangeModel($request);
        $res = $ChangeModel->submitErrorHandle($request);
        $result = array();
        if ($res) {
            $result["list"] = $res;
            $result = array('error' => 0, 'errortip' => '提交成功', 'result' => $result);
        } else {
            $result = array('error' => 1, 'errortip' => $ChangeModel->errortip, 'result' => $result);
        }
        ajax_return($result, $request['language_type']);
    }

}
