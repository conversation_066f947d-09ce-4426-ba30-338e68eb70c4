<?php


namespace Work\Controller\Smcapi;


class ClassReportController extends viewTpl
{
    public $u;
    public $t;
    public $c;

    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
    }


//    function weeklyNumberView()
//    {
//        $request = Input('get.','','trim,addslashes');
//        $this->ThisVerify($request);
//        $ReportModel = new \Model\Report\Smc\ClassReportModel($request);
//        $res = $ReportModel->weeklyNumber($request);
//
//        $field = array();
//        $k=0;
//
//        $field[$k]["fieldstring"] = "coursecat_cnname";
//        $field[$k]["fieldname"] = "班种";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//        $k++;
//
//        $field[$k]["fieldstring"] = "coursecat_branch";
//        $field[$k]["fieldname"] = "班种编号";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//        $k++;
//
//        $field[$k]["fieldstring"] = "class_cnname";
//        $field[$k]["fieldname"] = "班级名称";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//        $k++;
//
//        $field[$k]["fieldstring"] = "class_enname";
//        $field[$k]["fieldname"] = "英文名称";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//        $k++;
//
//        $field[$k]["fieldstring"] = "class_branch";
//        $field[$k]["fieldname"] = "班级编号";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//        $k++;
//
//        $field[$k]["fieldstring"] = "classTime";
//        $field[$k]["fieldname"] = "起止日期";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//        $k++;
//
//        $field[$k]["fieldstring"] = "class_timestr";
//        $field[$k]["fieldname"] = "上课时段";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//        $k++;
//
//        $field[$k]["fieldstring"] = "classInfo";
//        $field[$k]["fieldname"] = "已上课次数";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//        $k++;
//
//        $field[$k]["fieldstring"] = "cnteacher";
//        $field[$k]["fieldname"] = "教师姓名";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//        $k++;
//
//        $field[$k]["fieldstring"] = "lastweeknum";
//        $field[$k]["fieldname"] = "上周人数";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//        $k++;
//
//        $field[$k]["fieldstring"] = "weeknum";
//        $field[$k]["fieldname"] = "本周人数";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//        $k++;
//
//        $field[$k]["fieldstring"] = "newnum";
//        $field[$k]["fieldname"] = "新增人数";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//        $k++;
//
//        $field[$k]["fieldstring"] = "lossnum";
//        $field[$k]["fieldname"] = "流失人数";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//        $k++;
//
//        $field[$k]["fieldstring"] = "extendednum";
//        $field[$k]["fieldname"] = "延班人数";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//        $k++;
//
//        $field[$k]["fieldstring"] = "classinnum";
//        $field[$k]["fieldname"] = "班级转入人数";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//        $k++;
//
//        $field[$k]["fieldstring"] = "classtonum";
//        $field[$k]["fieldname"] = "班级转出人数";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//        $k++;
//
//        $result = array();
//        $result["field"] = $field;
//        $result["allnum"] = $res['allnum'];
//        if ($res) {
//            $result["fieldcustom"] = 1;
//            $result["list"] = $res['list'];
//            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
//        } else {
//            $result["list"] = array();
//            $result["allnum"] = 0;
//            $res = array('error' => 1, 'errortip' => $ReportModel->errortip, 'result' => $result);
//        }
//        ajax_return($res,$request['language_type']);
//    }

//    function classInfoView()
//    {
//        $request = Input('get.','','trim,addslashes');
//        $this->ThisVerify($request);
//        $ReportModel = new \Model\Report\Smc\ClassReportModel($request);
//        $res = $ReportModel->classInfo($request);
//
//        $field = array();
//        $k=0;
//
//        $field[$k]["fieldstring"] = "class_cnname";
//        $field[$k]["fieldname"] = "班级名称";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//        $k++;
//
//        $field[$k]["fieldstring"] = "class_enname";
//        $field[$k]["fieldname"] = "班级别名";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//        $k++;
//
//        $field[$k]["fieldstring"] = "class_branch";
//        $field[$k]["fieldname"] = "班级编号";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//        $k++;
//
//        $field[$k]["fieldstring"] = "course_cnname";
//        $field[$k]["fieldname"] = "课程别名称";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//        $k++;
//
//        $field[$k]["fieldstring"] = "course_branch";
//        $field[$k]["fieldname"] = "课程别编号";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//        $k++;
//
//        $field[$k]["fieldstring"] = "all_teacher_num";
//        $field[$k]["fieldname"] = "教师累计总数";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//        $k++;
//
//        $field[$k]["fieldstring"] = "serving_teacher_num";
//        $field[$k]["fieldname"] = "教师人数";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//        $k++;
//
//        $field[$k]["fieldstring"] = "class_fullnums";
//        $field[$k]["fieldname"] = "满班人数";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//        $k++;
//
//        $field[$k]["fieldstring"] = "reading_num";
//        $field[$k]["fieldname"] = "在读人数";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//        $k++;
//
//        $field[$k]["fieldstring"] = "full_class_rate";
//        $field[$k]["fieldname"] = "满班率";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//        $k++;
//
//        $field[$k]["fieldstring"] = "classinnum";
//        $field[$k]["fieldname"] = "转入人数";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//        $k++;
//
//        $field[$k]["fieldstring"] = "classtonum";
//        $field[$k]["fieldname"] = "转出人数";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//        $k++;
//
//        $field[$k]["fieldstring"] = "lossnum";
//        $field[$k]["fieldname"] = "流失人数";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//        $k++;
//
//        $field[$k]["fieldstring"] = "extendednum";
//        $field[$k]["fieldname"] = "延班人数";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//        $k++;
//
//        $field[$k]["fieldstring"] = "arrive_num";
//        $field[$k]["fieldname"] = "应到人次";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//        $k++;
//
//        $field[$k]["fieldstring"] = "attendance_num";
//        $field[$k]["fieldname"] = "实到人次";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//        $k++;
//
//        $field[$k]["fieldstring"] = "not_arrived_num";
//        $field[$k]["fieldname"] = "未到人次";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//        $k++;
//
//        $field[$k]["fieldstring"] = "attendance_rate";
//        $field[$k]["fieldname"] = "出勤率";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//        $k++;
//
//
//        $result = array();
//        $result["field"] = $field;
//        $result["allnum"] = $res['allnum'];
//        if ($res) {
//            $result["fieldcustom"] = 1;
//            $result["list"] = $res['list'];
//            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
//        } else {
//            $result["list"] = array();
//            $result["allnum"] = 0;
//            $res = array('error' => 1, 'errortip' => $ReportModel->errortip, 'result' => $result);
//        }
//        ajax_return($res,$request['language_type']);
//    }
//

    function classLevelUpView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Report\Smc\ClassReportModel($request);
        $res = $ReportModel->promotion($request);

        $field = array();
        $k=0;

        $field[$k]["fieldstring"] = "from_class_cnname";
        $field[$k]["fieldname"] = "班级名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "from_class_enname";
        $field[$k]["fieldname"] = "班级别名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "from_class_branch";
        $field[$k]["fieldname"] = "班级编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "from_course_cnname";
        $field[$k]["fieldname"] = "课程别名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "from_course_branch";
        $field[$k]["fieldname"] = "课程别编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "from_staffer_cnname";
        $field[$k]["fieldname"] = "班级教师";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "from_num";
        $field[$k]["fieldname"] = "班级人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "upgradeorder_createtime";
        $field[$k]["fieldname"] = "升班时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "to_class_cnname";
        $field[$k]["fieldname"] = "升班班级名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "to_class_enname";
        $field[$k]["fieldname"] = "升班班级别名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "to_class_branch";
        $field[$k]["fieldname"] = "升班班级编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "to_staffer_cnname";
        $field[$k]["fieldname"] = "升班班级教师";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "to_num";
        $field[$k]["fieldname"] = "升班人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "promotion_rate";
        $field[$k]["fieldname"] = "升班率";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => $ReportModel->errortip, 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }


}