<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/13
 * Time: 23:25
 */

namespace Work\Controller\Smcapi;


class ModuleController extends viewTpl
{
    public $u;
    public $t;
    public $c;
    public $visitType = "api";
    public $DataControl;
    public $Model;

    //预加载处理类
    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();

        //数据库操作
        $this->DataControl = new \Dbmysql();

    }

    function ThisVerify($request)
    {
        $paramArray = array();
        $paramArray['staffer_id'] = $request['staffer_id'];
        $paramArray['company_id'] = $request['company_id'];
        $paramArray['token'] = $request['token'];
        if(!$this->UserLimit($paramArray)){
            $result = array();
            $result["list"] = array();
            $result["tokeninc"] = "0";
            $res = array('error' => '1', 'errortip' => "用户token失效", 'result' => $result);
            ajax_return($res,$request['language_type']);
        }
    }

    //权限列表
    function HomeView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model =  new \Model\Smc\ModuleModel($request);
        $result = $Model->getModuleList($request);

        $res = array('error' => 0, 'errortip' => '菜单获取成功', 'result' => $result);
        ajax_return($res,$request['language_type']);
    }

    //根据职务权限列表
    function getPowerListView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\ModuleModel($request);
        $result = $Model->getPowerList($request);

        $res = array('error' => 0, 'errortip' => '菜单获取成功', 'result' => $result);
        ajax_return($res,$request['language_type'],1);
    }

    //查看某集团角色权限
    function getPostrolePowerApi()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\ModuleModel($request);
        $result = $Model->getPostrolePowerApi($request);

        $res = array('error' => 0, 'errortip' => '权限查看成功', 'result' => $result);
        ajax_return($res,$request['language_type']);
    }

    //查看校园角色权限
    function getPostpartPowerApi()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\ModuleModel($request);
        $result = $Model->getPostpartPowerApi($request);

        $res = array('error' => 0, 'errortip' => '权限查看成功', 'result' => $result);
        ajax_return($res,$request['language_type']);
    }

    //更换地址
    function ChangeUrlView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model =  new \Model\Smc\ModuleModel($request);
        $module = $this->DataControl->getFieldOne("imc_module","module_id,father_id","module_markurl = '{$request['url']}'");
        $result = $Model->getModuleListUrl($request);

        $res = array('error' => 0, 'errortip' => '获取成功', 'module_id' => $module['module_id'], 'father_id' => $module['father_id'], 'result' => $result);
        ajax_return($res,$request['language_type']);
    }


    //结尾魔术函数
    function __destruct()
    {

    }
}
