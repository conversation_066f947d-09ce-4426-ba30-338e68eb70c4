<?php


namespace Work\Controller\Smcapi;


class OrderPayController extends viewTpl
{
    public $u;
    public $t;
    public $c;

    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();

    }

//	 订单下单页的操作

    function getQRCodeApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);

        $pay_pid = $request['paypid'];

        $CompanyOne = $this->DataControl->selectOne("select * from gmc_company where company_id = '{$request['company_id']}' limit 0,1");
        $orderOne = $this->DataControl->selectOne("select p.*,o.student_id,o.school_id,o.companies_id
                    from smc_payfee_order_pay as p 
                    LEFT JOIN smc_payfee_order as o ON o.order_pid = p.order_pid 
                    where p.pay_pid = '{$pay_pid}' limit 0,1");

        if ($request['paytype'] == 'qrcode') {

            $paycompaniesOne = $this->DataControl->getFieldOne("gmc_code_companies","companies_chargchannel", "companies_id='{$orderOne['companies_id']}'");


            if($paycompaniesOne['companies_chargchannel'] == 'cmbbank'){
                $payqrcode = "https://scshopapi.kedingdang.com/BoingPay/OrderPay?paypid={$pay_pid}&paymenttype=ewmpay";
            }elseif($paycompaniesOne['companies_chargchannel'] == 'cmbheadbank'){
                $payqrcode = "https://scshopapi.kedingdang.com/HeadBoingPay/OrderPay?paypid={$pay_pid}&paymenttype=ewmpay";
            }elseif($paycompaniesOne['companies_chargchannel'] == 'cmbmergebank'){
                $payqrcode = "https://scshopapi.kedingdang.com/MergeBoingPay/OrderPay?paypid={$pay_pid}&paymenttype=ewmpay";
            }else{
                $this->error = 1;
                $this->errortip = "未查询收款方式";
                return false;
            }

//            $payqrcode = "https://scshopapi.kedingdang.com/BoingPay/OrderPay?paypid={$pay_pid}&paymenttype=ewmpay";

        } elseif ($request['paytype'] == 'parent') {
            $payurl = "https://{$CompanyOne['company_id']}.scshop.kedingdang.com/Shop/payMethod?paypid={$pay_pid}";
            $payqrcode = "https://smcapi.kedingdang.com/OrderPay/urlshowimg?imgurl=" . base64_encode($payurl);

//            $payurl = "https://{$request['company_id']}.scshop.kedingdang.com/Shop/payMethod?paypid={$pay_pid}";
//            $data['payqrcode'] = "https://smcapi.kedingdang.com/OrderPay/urlshowimg?imgurl=".base64_encode($payurl);

            //微信通知家长
            $schoolONE = $this->DataControl->selectOne("select school_shortname from smc_school WHERE school_id = '{$request['school_id']}'");
            $stuONE = $this->DataControl->selectOne("select student_branch from smc_student WHERE student_id = '{$orderOne['student_id']}'");

            $firstnote = "家长您好，您在我校交费的订单已产生，请及时通过微信支付";
            $keyword1 = $pay_pid;
            $keyword2 = $orderOne['pay_price'];
            $keyword3 = "您在我校购买了课程或商品";
            $keyword4 = $schoolONE['school_shortname'];
            $keyword5 = date("Y-m-d", $orderOne['pay_createtime']);
            $footernote = "家长您好，您在我校交费的订单已产生，请及时通过微信支付，感谢您的使用！";
            $str = "branch={$stuONE['student_branch']}&firstnote={$firstnote}&keyword1={$keyword1}&keyword2={$keyword2}&keyword3={$keyword3}&keyword4={$keyword4}&keyword5={$keyword5}&footernote={$footernote}&companyid={$CompanyOne['company_id']}";
            request_by_curl("https://api.kidcastle.com.cn/Wechat/payOrderGoToParent", $str, "GET", array());


        } elseif ($request['paytype'] == 'pos') {
            $studentOne = $this->DataControl->getFieldOne("smc_student", "student_cnname,student_enname,student_branch", "student_id='{$orderOne['student_id']}'");
            $schoolOne = $this->DataControl->getOne("smc_school", "school_id='{$orderOne['school_id']}'");
            $ewmCodeArray = array();
            $ewmCodeArray['orderNo'] = $pay_pid;
            $ewmCodeArray['paymentprice'] = $orderOne['pay_price'];
            $ewmCodeArray['school_id'] = $request['school_id'];
            $ewmCodeArray['school_cnname'] = $schoolOne['school_cnname'];
            $ewmCodeArray['tokencode'] = $schoolOne['school_id'] . $schoolOne['school_branch'];
            $ewmCodeArray['cnname'] = $studentOne['student_cnname'];
            $ewmCodeArray['enname'] = $studentOne['student_enname'];
            $ewmCodeArray['branch'] = $studentOne['student_branch'];
            $ewmCodeString = json_encode($ewmCodeArray, JSON_UNESCAPED_UNICODE);

            $payqrcode = "https://smcapi.kedingdang.com/OrderPay/urlshowimgTwo?imgurl=" . urlencode(base64_encode($ewmCodeString));
        }elseif($request['paytype'] == 'H5pay'){

            

        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无课程信息', 'result' => $result);
            ajax_return($res, $request['language_type']);
        }
        if ($payqrcode) {
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $payqrcode);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无课程信息', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //获取订单信息
    function stuOrderOneApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $orderHandleModel = new  \Model\Smc\OrderPayModel($request, $request['order_pid']);
//		$this->ThisVerify($request);
//		if(isset($request['from']) &&  $request['from'] =='app' )
//		{
//			$from = 'app';
//		}else{
//			$from = '0';
//		}

        $order = $orderHandleModel->stuOrderOne();//c.companies_cnname,'招商银行' as supervisebankname,c.companies_agencyid,c.companies_superviseaccount,
        $fieldstring = array('student_cnname', 'student_enname', 'student_branch', 'order_pid', 'order_createtime', 'order_taglist', 'order_allprice', 'order_balance_price', 'order_forward_price', 'order_coupon_price', 'order_surplusprice', 'order_status', 'companies_cnname', 'companies_supervisebank', 'companies_agencyid', 'companies_superviseaccount','companies_issupervise');
        $fieldname = array('学员中文名', '学员英文名', '学员编号', '订单编号', '下单时间', '订单标签', '订单总价', '余额抵扣', '结转金额抵扣', '优惠券抵扣', '剩余支付金额', '订单状态', '校区主体', '课消监管银行', '监管机构编号', '课消监管账户', '是否监管');
        $fieldcustom = array('1', "1", "1", "1", "1", "1", "1", "1", "1", "1", '1', '0', "1", "1", "1", '1', '1');
        $fieldshow = array('1', "1", "1", "1", "1", "1", "1", "1", "1", "1", '1', '0', "1", "1", "1", '1' ,'0');
        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["fieldname"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }
        $result = array();
        $result['fieldcustom'] = 1;
        $result['field'] = $field;

        $result['all_sign'] = $order['all_sign'];
        $result['list'] = ($order['list'] == "") ? array() : $order['list'];
        $result['deposit_price'] = $order['deposit_price'] ? $order['deposit_price'] : 0;
        $result['companyinfo'] = ($order['companyinfo'] == "") ? array() : $order['companyinfo'];
        $result['course'] = ($order['order_goods']['course'] == "") ? array() : $order['order_goods']['course'];
        $result['items'] = ($order['order_goods']['items'] == "") ? array() : $order['order_goods']['items'];
        $result['common_items'] = ($order['order_goods']['common_items'] == "") ? array() : $order['order_goods']['common_items'];
        $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
        ajax_return($res, $request['language_type']);
    }

    //获取这个订单的支付记录
    function orderPayListApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);

        $orderHandleModel = new  \Model\Smc\OrderPayModel($request, $request['order_pid']);

//		if(isset($request['from']) &&  $request['from'] =="app")
//		{
//			$from = "app";
//		}else{
//			$from =  '0';
//		}
        $from = 0;
        $payList = $orderHandleModel->OrderPayList($request, $request['app_school_id'], $request['pay_status'], $from);
        $v = $this->DataControl->getFieldOne("gmc_company", "company_isvoucher", "company_id = '{$request['company_id']}'");

        if ($payList['list']) {
            foreach ($payList['list'] as &$val) {
                $val['voucher'] = $v['company_isvoucher'];
            }
        }


        $fieldstring = array('pay_pid', 'pay_typename', 'pay_price', 'pay_issuccess_name', 'pay_successtime', 'pay_outnumber', 'pay_note');
        $fieldname = array('支付编号', '支付方式', '支付金额', '支付状态', '支付时间', '外部交易编号', '备注');
        $fieldcustom = array('1', "1", "1", "1", "1", "1", "1");
        $fieldshow = array('1', "1", "1", "1", "1", "1", "1");
        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["fieldname"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }


        $result = array();
        $result['fieldcustom'] = 1;
        $result['field'] = $field;
        $result['list'] = ($payList['list'] == "") ? array() : $payList['list'];
        $result['order'] = ($payList['order'] == "") ? array() : $payList['order'];

        if ($result['list']) {
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
        } else {
            $res = array('error' => 1, 'errortip' => '暂无订单信息', 'result' => $result);
        }

        ajax_return($res, $request['language_type']);
    }

    //取消支付订单
    function cancelPayOrderAction()
    {
        $request = Input('post.', '', 'trim,addslashes');

        if (empty($request['order_pid'])) {
            $res = array('error' => 1, 'errortip' => '订单编号错误', 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        if (empty($request['pay_pid'])) {
            $res = array('error' => 1, 'errortip' => '支付编号错误', 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        $this->ThisVerify($request);
        $orderHandleModel = new  \Model\Smc\OrderHandleModel($request, $request['order_pid']);
        $orderHandleModel->cancelPayOrder($request['pay_pid']);

        $res = array('error' => $orderHandleModel->error, 'errortip' => $orderHandleModel->errortip, 'result' => array());

        $json_play = new \Webjson();
        $errorData['errorlog_apiurl'] = $this->u . '/' . $this->c;
        $errorData['errorlog_class'] = 0;
        $errorData['errorlog_parameter'] = $json_play->encode($request);
        $errorData['errorlog_bakjson'] = $json_play->encode($res);
        $errorData['errorlog_time'] = time();

        $this->DataControl->insertData("smc_api_errorlog", $errorData);

        ajax_return($res, $request['language_type']);
    }

//    //取消支付订单  --- ceshi
//    function cancelPayOrderTestAction()
//    {
//        $request = Input('post.', '', 'trim,addslashes');
//        $orderHandleModel = new  \Model\Smc\OrderHandleModel($request, $request['order_pid']);
//        $orderHandleModel->cancelPayOrdertwo($request['pay_pid']);
//    }

    //	支付成功后
    function successPayAction()
    {
        $request = Input('post.', '', 'trim,addslashes');

        if ($request['order_pid'] == "") {
            $res = array('error' => 1, 'errortip' => '订单编号错误', 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        if (empty($request['pay_pid'])) {
            $res = array('error' => 1, 'errortip' => '支付编号错误', 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        if ($request['paylog_tradeno'] == "" && $request['paytype_code'] <> "balance" && $request['paytype_code'] <> "deposit") {
            $res = array('error' => 1, 'errortip' => '请输入外部交易编号', 'result' => array());
            ajax_return($res, $request['language_type']);
        }

//		if(isset($request['from']) && $request['from'] =="app"  )
//		{
//			$request['from']  = 'app';
//		}else{
//			$request['from']  = 0;
//		}

        if (isset($request['create_time']) && $request['create_time'] !== "") {
            $createtime = $request['create_time'];
        } else {
            $createtime = '';
        }

        $orderPayModel = new  \Model\Smc\OrderPayModel($request, $request['order_pid']);

        $balance_array = array('balance', 'forward', 'norebalance', 'cattimes', 'catdeposit', 'catsales', 'catmanage', 'catbus', 'catfood');
        $cash_array = array('cash');
        $system_array = array('feewaiver');

        $payOne = $this->DataControl->getFieldOne("smc_payfee_order_pay", "paytype_code,pay_successtime,pay_price", "pay_pid='{$request['pay_pid']}'");
        if (!$payOne) {
            $res = array('error' => 1, 'errortip' => '支付编号不存在', 'result' => array());
            ajax_return($res, $request['language_type']);
        }

        $paychannel_code = '';
        if (in_array($payOne['paytype_code'], $balance_array)) {
            $paychannel_code = 'balance';
        } elseif (in_array($payOne['paytype_code'], $cash_array)) {
            $paychannel_code = 'cash';
        } elseif (in_array($payOne['paytype_code'], $system_array)) {
            $paychannel_code = 'system';
        }
//		$request['paytype_code']=$payOne['paytype_code']?$payOne['paytype_code']:$request['paytype_code'];

        $bools = $orderPayModel->orderPaylog($request['pay_pid'], $request['paylog_tradeno'], $request['ifee'], $request['pay_note'], $request['paytype_code'], $createtime, 0, '', $request['img'], $paychannel_code);
        if ($bools) {
            $res = array('error' => 0, 'errortip' => '操作订单成功', 'result' => array());
        } else {
            $res = array('error' => 1, 'errortip' => $orderPayModel->errortip, 'result' => array());
        }

        $payOne = $this->DataControl->getFieldOne("smc_payfee_order_pay", "paytype_code,pay_successtime,pay_price", "pay_pid='{$request['pay_pid']}'");

        $order = $this->DataControl->getFieldOne("smc_payfee_order", "order_arrearageprice,order_paymentprice,order_type,student_id,order_paidprice", "order_pid = '{$request['order_pid']}'");

        if ($order['order_type'] == '2') {
            $parenter = $this->DataControl->selectClear("select p.parenter_cnname,p.parenter_id from smc_student_family as f left join smc_parenter as p on f.parenter_id = p.parenter_id WHERE f.student_id  = '{$order['student_id']}'");
            $trading = $this->DataControl->selectClear("select a.trading_pid,a.tradingtype_code from smc_student_trading a,smc_payfee_order b 
              where a.trading_pid=b.trading_pid and a.student_id=b.student_id and b.order_pid = '{$request['order_pid']}' limit 0,1");

            foreach ($parenter as &$value) {
                $student_cnname = $this->DataControl->getFieldOne("smc_student", "student_cnname", "student_id = '{$order['student_id']}'");

                $isset = $this->DataControl->getFieldOne("gmc_company_masterplate", "masterplate_wxid", "company_id = '{$request['company_id']}' and masterplate_name = '充值成功通知'");
                if ($isset) {
                    $wxid = $isset['masterplate_wxid'];
                } else {
                    $masterplate = $this->DataControl->getFieldOne("gmc_company_masterplate", "masterplate_wxid", "company_id = '0' and masterplate_name = '充值成功通知'");
                    $wxid = $masterplate['masterplate_wxid'];
                }

                $token = $this->getParentToken($value);
                $a = $student_cnname['student_cnname'] . '学员您好，您已充值成功';
                if ($trading && $trading['tradingtype_code'] == 'DepositCharge') {
                    $b = '定金充值';
                } else {
                    $b = '账户充值';
                }
                $c = $request['order_pid'];
                $d = $order['order_paidprice'];
                $e = date('m-d H:i', $payOne['pay_successtime']);
                $f = '充值金额已到账，感谢您的使用';
                $g = "https://{$request['company_id']}.scshop.kedingdang.com/Order/orderDetail?order_pid={$request['order_pid']}&order_type=0&company_id={$request['company_id']}&student_id={$order['student_id']}&parenter_id={$value['parenter_id']}&token={$token}&cid={$request['company_id']}&s_id={$order['student_id']}";
                $wxteModel = new \Model\Api\ZxwxChatModel($value['parenter_id'], $order['student_id']);
                $wxteModel->StuInvest($a, $b, $c, $d, $e, $f, $g, $wxid);
            }

        } elseif ($order['order_type'] == '0') {
            $parenter = $this->DataControl->selectClear("select p.parenter_cnname,p.parenter_id from smc_student_family as f left join smc_parenter as p on f.parenter_id = p.parenter_id WHERE f.student_id  = '{$order['student_id']}'");

            foreach ($parenter as &$value) {
                $student_cnname = $this->DataControl->getFieldOne("smc_student", "student_cnname", "student_id = '{$order['student_id']}'");
                $course_cnname = $this->DataControl->selectOne("select c.course_cnname,c.course_id from smc_course as c left join smc_payfee_order_course as o on c.course_id = o.course_id where o.order_pid = '{$request['order_pid']}'");
                $status = $this->DataControl->getFieldOne("gmc_company", "company_isshowhour", "company_id = '{$request['company_id']}'");

                $isset = $this->DataControl->getFieldOne("gmc_company_masterplate", "masterplate_wxid", "company_id = '{$request['company_id']}' and masterplate_name = '学生缴费确认通知'");
                if ($isset) {
                    $wxid = $isset['masterplate_wxid'];
                } else {
                    $masterplate = $this->DataControl->getFieldOne("gmc_company_masterplate", "masterplate_wxid", "company_id = '0' and masterplate_name = '学生缴费确认通知'");
                    $wxid = $masterplate['masterplate_wxid'];
                }

                $a = $student_cnname['student_cnname'] . '学员您好，您已缴费成功';
                $b = $course_cnname['course_cnname'];
                $c = $order['order_paymentprice'];
                $d = $order['order_paidprice'];
                $e = $payOne['pay_price'];
                $f = $order['order_arrearageprice'];
                if ($status['company_isshowhour'] == '1') {
                    $g = '点击这里即可查看购课详情哦～';
                    $h = "https://scptc.kedingdang.com/MyCenter/courseDetail?course_id={$course_cnname['course_id']}&cid={$request['company_id']}&s_id={$order['student_id']}";
                } else {
                    $g = '本次交易已完成，您已成功缴费';
                    $h = "";
                }
                $wxteModel = new \Model\Api\ZxwxChatModel($value['parenter_id'], $order['student_id']);
                $wxteModel->StuPay($a, $b, $c, $d, $e, $f, $g, $h, $wxid);
            }

        }

        $isset = $this->DataControl->getFieldOne("smc_student_protocol","protocol_id","order_pid = '{$request['order_pid']}' and protocol_issign = '0'");
        $iscom = $this->DataControl->getFieldOne("smc_payfee_order","order_status","order_pid = '{$request['order_pid']}'");
        $protocol = $this->DataControl->getFieldOne("smc_student_protocol","protocol_id","order_pid = '{$request['order_pid']}'");

        if(!$isset && $iscom['order_status'] == '4' && $protocol){
            $data = array();
            $data['protocol_isaudit'] = '1';
            $this->DataControl->updateData("smc_student_protocol","order_pid = '{$request['order_pid']}'",$data);
        }



        $errorData = array();

        $json_play = new \Webjson();
        $errorData['errorlog_apiurl'] = $this->u . '/' . $this->c;
        $errorData['errorlog_class'] = 0;
        $errorData['errorlog_parameter'] = $json_play->encode($request);
        $errorData['errorlog_bakjson'] = $json_play->encode($res);
        $errorData['errorlog_time'] = time();

        $this->DataControl->insertData("smc_api_errorlog", $errorData);

        ajax_return($res, $request['language_type']);
    }

    // 获取二维码
    function getCodePayApi()
    {
//		$res = array('error' => 1, 'errortip' => "获取成功", 'code' => '2222222');
//		ajax_return($res,$request['language_type']);

    }

    //B扫C 扫码枪支付
    function bsaocPayApi()
    {
        $request = Input('get.', '', 'strip_tags,trim,addslashes');
        $this->ThisVerify($request);
        $PayModel = new \Model\Scshop\BoingPayModel($request['company_id']);
        $result = $PayModel->OrderPay($request['paypid'], 'bsaocpay', $request['openid']);

        $res = array('error' => $result['error'], 'errortip' => $result['errortip'], 'result' => $result['result']);
        ajax_return($res, $request['language_type']);
    }

    function orderPayStatusApi()
    {
        $request = Input('get.', '', 'strip_tags,trim,addslashes');
        $this->ThisVerify($request);
        $PayModel = new \Model\Scshop\BoingPayModel($request['company_id']);
        $result = $PayModel->orderPayStatus($request['paypid']);
        if ($result && $result['result']['pay_result'] == 'succeed') {
            $res = array('error' => 0, 'errortip' => '该订单已成功支付', 'result' => $result['result']['pay_result'], 'info' => $result);
        } else {
            $res = array('error' => 1, 'errortip' => '该订单未完成支付', 'result' => $result['result']['pay_result'], 'info' => $result);
        }
        ajax_return($res, $request['language_type']);
    }
    function getorderPayOneApi()
    {
        $request = Input('get.', '', 'strip_tags,trim,addslashes');
        $this->ThisVerify($request);
        $PayModel = new \Model\Scshop\BoingPayModel($request['company_id']);
        $result = $PayModel->orderPayStatusOne($request['paypid']);
        print_r($result);

        $Refund = new \Model\Scshop\BoingPayModel($request['company_id']);
        $Refundref = $Refund->RefundQueryView($request['paypid'], '1182740025519935488');
        print_r($Refundref);
    }

    function orderPayAction()
    {
        $request = Input('post.', '', 'strip_tags,trim');
        $this->ThisVerify($request);
        if (empty($request['order_pid'])) {
            $res = array('error' => 1, 'errortip' => '订单编号缺失', 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        if ($request['paytype'] == "") {
            $res = array('error' => 1, 'errortip' => '请选择支付方式', 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        if ($request['paytimes'] == "") {
            $res = array('error' => 1, 'errortip' => '请选择支付类型', 'result' => array());
            ajax_return($res, $request['language_type']);
        }


        $publicarray = array();
        $publicarray['company_id'] = $request['company_id'];
        $publicarray['school_id'] = $request['school_id'];
        $publicarray['staffer_id'] = $request['staffer_id'];


        $payArray = array();
        $payArray['order_pid'] = $request['order_pid'];
        $payArray['paytype'] = $request['paytype'];
        $payArray['paytimes'] = $request['paytimes'];
        $payArray['pay_price'] = $request['pay_price'];
        $payArray['create_time'] = $request['create_time'];
        $payArray['pay_note'] = $request['pay_note'];
        $payArray['pay_img'] = $request['pay_img'];

        if ($request['paytimes'] == '0' && !isset($payArray['pay_price'])) {
            $res = array('error' => 1, 'errortip' => '分批支付必须传入支付金额!', 'result' => array());
            ajax_return($res, $request['language_type']);
        }

        $Model = new \Model\Smc\OrderPayModel($publicarray, $request['order_pid']);
        $data = $Model->createOrderPay($payArray);

        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $data);
        $errorData = array();

        $json_play = new \Webjson();
        $errorData['errorlog_apiurl'] = $this->u . '/' . $this->c;
        $errorData['errorlog_class'] = 0;
        $errorData['errorlog_parameter'] = $json_play->encode($request);
        $errorData['errorlog_bakjson'] = $json_play->encode($res);
        $errorData['errorlog_time'] = time();

        $this->DataControl->insertData("smc_api_errorlog", $errorData);

        ajax_return($res, $request['language_type']);
    }

    //生成二维码
    function urlshowimgView()
    {
        header('Content-Type:image/png');
        $request = Input('get.', '', 'trim,addslashes');
        $codeUrl = base64_decode($request['imgurl']);//."&school_id={$request['school_id']}&typ=0"
        require(ROOT_PATH . 'Core/Classlib/Webqrcode.php');//二维码生成
        $QRcode = new \QRcode();
        $errorCorrectionLevel = 'H';//容错级别
        $matrixPointSize = 15;//生成图片大小
        echo $QRcode->png($codeUrl, false, $errorCorrectionLevel, $matrixPointSize, 2);
    }

    
    //生成二维码
    function urlcodeimgView()
    {
        
        $request = Input('get.', '', 'trim,addslashes');
        $OrderModel = new \Model\Smc\MergeOrderPayModel();
        // 生成二维码图片（返回base64编码）
        echo $OrderModel->generateQRCode($request['imgurl'], 'base64', 8, 'H');
    }

    //生成二维码 --- 对url的get传值进行反转义，后生成二维码
    function urlshowimgTwoView()
    {
        header('Content-Type:image/png');
        $request = Input('get.', '', 'trim,addslashes');
        $codeUrl = urldecode(base64_decode($request['imgurl']));//."&school_id={$request['school_id']}&typ=0"
        require(ROOT_PATH . 'Core/Classlib/Webqrcode.php');//二维码生成
        $QRcode = new \QRcode();
        $errorCorrectionLevel = 'H';//容错级别
        $matrixPointSize = 15;//生成图片大小
        echo $QRcode->png($codeUrl, false, $errorCorrectionLevel, $matrixPointSize, 2);
    }


    function mergeOrderPayAction(){
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $OrderModel = new \Model\Smc\MergeOrderPayModel($request, $request['mergeorder_pid']);
        $res = $OrderModel->mergeOrderPay($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $data = array();
            $data['protocol_isdel'] = '1';
            $this->DataControl->updateData("smc_student_protocol","order_pid = '{$request['order_pid']}'",$data);
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
            $this->addSmcWorkLog($request['company_id'], $request['school_id'], $request['staffer_id'], "缴费管理->缴费中心", '获取招行组合支付二维码', dataEncode($request));
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $OrderModel->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function mergeOrderPayStatusApi()
    {
        $request = Input('get.', '', 'strip_tags,trim,addslashes');
        $this->ThisVerify($request);
        $OrderModel = new \Model\Smc\MergeOrderPayModel($request, $request['mergeorder_pid']);
        $result = $OrderModel->mergeOrderPayStatus($request);
        if ($result) {
            $res = array('error' => 0, 'errortip' => '该订单已成功支付', 'result' => $result);
        } else {
            $res = array('error' => 1, 'errortip' => $OrderModel->errortip, 'result' => array());
        }
        ajax_return($res, $request['language_type']);
    }


}
