<?php


namespace Work\Controller\Smcapi;


class ReportStorageControlle extends viewTpl
{
    public $u;
    public $t;
    public $c;

    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
    }

    //借调相关报表
    function transferReportView(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Smc\ReportModel($request);
        $res = $ReportModel->transferReport($request);

        $field = array();
        $field[0]["fieldstring"] = "to_school_cnname";
        $field[0]["fieldname"] = "借入分校";
        $field[0]["show"] = 1;
        $field[0]["custom"] = 1;

        $field[1]["fieldstring"] = "from_school_cnname";
        $field[1]["fieldname"] = "借出分校";
        $field[1]["show"] = 1;
        $field[1]["custom"] = 1;

        $field[2]["fieldstring"] = "proorder_pid";
        $field[2]["fieldname"] = "采购单号";
        $field[2]["show"] = 1;
        $field[2]["custom"] = 1;

        $field[3]["fieldstring"] = "goods_cnname";
        $field[3]["fieldname"] = "货品名称";
        $field[3]["show"] = 1;
        $field[3]["custom"] = 1;

        $field[4]["fieldstring"] = "goods_pid";
        $field[4]["fieldname"] = "货品编号";
        $field[4]["show"] = 1;
        $field[4]["custom"] = 1;

        $field[5]["fieldstring"] = "prodtype_name";
        $field[5]["fieldname"] = "货品类型";
        $field[5]["show"] = 1;
        $field[5]["custom"] = 1;

        $field[6]["fieldstring"] = "goods_unit";
        $field[6]["fieldname"] = "单位";
        $field[6]["show"] = 1;
        $field[6]["custom"] = 1;

        $field[7]["fieldstring"] = "proogoods_buynums";
        $field[7]["fieldname"] = "数量";
        $field[7]["show"] = 1;
        $field[7]["custom"] = 1;

        $field[8]["fieldstring"] = "proorder_createtime";
        $field[8]["fieldname"] = "申请日期";
        $field[8]["show"] = 1;
        $field[8]["custom"] = 1;

        $field[9]["fieldstring"] = "proorder_updatetime";
        $field[9]["fieldname"] = "完成日期";
        $field[9]["show"] = 1;
        $field[9]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => "暂无借调相关记录", 'result' => $result);
        }
        ajax_return($res,$request['language_type']);

    }

    //学员教材购买报表
    function StuMaterialReportView(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Smc\ReportModel($request);
        $res = $ReportModel->StuMaterialReport($request);

        $field = array();
        $field[0]["fieldstring"] = "student_cnname";
        $field[0]["fieldname"] = "学员中文名";
        $field[0]["show"] = 1;
        $field[0]["custom"] = 1;

        $field[1]["fieldstring"] = "student_enname";
        $field[1]["fieldname"] = "学员英文名";
        $field[1]["show"] = 1;
        $field[1]["custom"] = 1;

        $field[2]["fieldstring"] = "student_branch";
        $field[2]["fieldname"] = "学员编号";
        $field[2]["show"] = 1;
        $field[2]["custom"] = 1;

        $field[3]["fieldstring"] = "beoutorder_pid";
        $field[3]["fieldname"] = "出库编号";
        $field[3]["show"] = 1;
        $field[3]["custom"] = 1;

        $field[4]["fieldstring"] = "goods_cnname";
        $field[4]["fieldname"] = "货品名称";
        $field[4]["show"] = 1;
        $field[4]["custom"] = 1;

        $field[5]["fieldstring"] = "goods_pid";
        $field[5]["fieldname"] = "货品编号";
        $field[5]["show"] = 1;
        $field[5]["custom"] = 1;

        $field[6]["fieldstring"] = "prodtype_name";
        $field[6]["fieldname"] = "货品类别";
        $field[6]["show"] = 1;
        $field[6]["custom"] = 1;

        $field[7]["fieldstring"] = "goods_unit";
        $field[7]["fieldname"] = "单位";
        $field[7]["show"] = 1;
        $field[7]["custom"] = 1;

        $field[8]["fieldstring"] = "goods_vipprice";
        $field[8]["fieldname"] = "销售价格";
        $field[8]["show"] = 1;
        $field[8]["custom"] = 1;

        $field[9]["fieldstring"] = "ordergoods_buynums";
        $field[9]["fieldname"] = "购买数量";
        $field[9]["show"] = 1;
        $field[9]["custom"] = 1;

        $field[9]["fieldstring"] = "allprice";
        $field[9]["fieldname"] = "销售金额";
        $field[9]["show"] = 1;
        $field[9]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => "暂无学员教材购买记录", 'result' => $result);
        }
        ajax_return($res,$request['language_type']);

    }

    //活动采购报表
    function ActivityBuyReportView(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Smc\ReportModel($request);
        $res = $ReportModel->ActivityBuyReport($request);

        $field = array();
        $field[0]["fieldstring"] = "goods_cnname";
        $field[0]["fieldname"] = "活动名称";
        $field[0]["show"] = 1;
        $field[0]["custom"] = 1;

        $field[1]["fieldstring"] = "goods_pid";
        $field[1]["fieldname"] = "货品名称";
        $field[1]["show"] = 1;
        $field[1]["custom"] = 1;

        $field[2]["fieldstring"] = "prodtype_name";
        $field[2]["fieldname"] = "货品编号";
        $field[2]["show"] = 1;
        $field[2]["custom"] = 1;

        $field[3]["fieldstring"] = "goods_unit";
        $field[3]["fieldname"] = "单位";
        $field[3]["show"] = 1;
        $field[3]["custom"] = 1;

        $field[4]["fieldstring"] = "goods_repertory";
        $field[4]["fieldname"] = "库存数量";
        $field[4]["show"] = 1;
        $field[4]["custom"] = 1;

        $field[5]["fieldstring"] = "goods_vipprice";
        $field[5]["fieldname"] = "货品价格";
        $field[5]["show"] = 1;
        $field[5]["custom"] = 1;

        $field[5]["fieldstring"] = "allprice";
        $field[5]["fieldname"] = "总金额";
        $field[5]["show"] = 1;
        $field[5]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => "暂无活动采购记录", 'result' => $result);
        }
        ajax_return($res,$request['language_type']);

    }


}
