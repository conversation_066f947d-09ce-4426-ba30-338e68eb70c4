<?php


namespace Work\Controller\Smcapi;


class ReportFinanceController extends viewTpl
{
    public $u;
    public $t;
    public $c;

    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
    }

    //学员充值明细表
    function studentInvestView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Smc\ReportModel($request);
        $res = $ReportModel->studentInvest($request);

        $field = array();

        $k = 0;

        $field[$k]["fieldstring"] = "order_pid";
        $field[$k]["fieldname"] = "交易订单编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["fieldname"] = "学员中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_enname";
        $field[$k]["fieldname"] = "学员英文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_branch";
        $field[$k]["fieldname"] = "学员编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "order_allprice";
        $field[$k]["fieldname"] = "订单总额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "order_status";
        $field[$k]["fieldname"] = "订单状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "order_createtime";
        $field[$k]["fieldname"] = "下单时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res['list']) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => "暂无学员充值明细报表", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    //学员账单欠费明细表--X
//    function stuUnpaidReportView()
//    {
//        $request = Input('get.', '', 'trim,addslashes');
//        $this->ThisVerify($request);
//        $ReportModel = new \Model\Smc\ReportModel($request);
//        $res = $ReportModel->orderUnpaidReport($request);
//
//
//        $k = 0;
//        $field = array();
//
//        $field[$k]["fieldstring"] = "school_id";
//        $field[$k]["fieldname"] = "学校ID";
//        $field[$k]["show"] = 0;
//        $field[$k]["custom"] = 1;
//        $k++;
//
//        $field[$k]["fieldstring"] = "school_branch";
//        $field[$k]["fieldname"] = "校区编号";
//        $field[$k]["show"] = 0;
//        $field[$k]["custom"] = 1;
//        $k++;
//
//        $field[$k]["fieldstring"] = "school_cnname";
//        $field[$k]["fieldname"] = "校区名称";
//        $field[$k]["show"] = 0;
//        $field[$k]["custom"] = 1;
//        $k++;
//
//        $field[$k]["fieldstring"] = "school_enname";
//        $field[$k]["fieldname"] = "检索代码";
//        $field[$k]["show"] = 0;
//        $field[$k]["custom"] = 1;
//        $k++;
//
//        $field[$k]["fieldstring"] = "student_branch";
//        $field[$k]["fieldname"] = "学员编号";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//        $k++;
//
//        $field[$k]["fieldstring"] = "student_cnname";
//        $field[$k]["fieldname"] = "学员中文名";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//        $k++;
//
//        $field[$k]["fieldstring"] = "student_enname";
//        $field[$k]["fieldname"] = "学员英文名";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//        $k++;
//
//        $field[$k]["fieldstring"] = "order_pid";
//        $field[$k]["fieldname"] = "订单编号";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//        $k++;
//
//        $field[$k]["fieldstring"] = "order_createtime";
//        $field[$k]["fieldname"] = "下单日期";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//        $k++;
//
//        $field[$k]["fieldstring"] = "order_note";
//        $field[$k]["fieldname"] = "订单备注";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//        $k++;
//
//        $field[$k]["fieldstring"] = "order_from";
//        $field[$k]["fieldname"] = "订单来源";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//        $k++;
//
//        $field[$k]["fieldstring"] = "order_type";
//        $field[$k]["fieldname"] = "订单类型";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//        $k++;
//
//        $field[$k]["fieldstring"] = "order_status";
//        $field[$k]["fieldname"] = "订单状态";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//        $k++;
//
//        $field[$k]["fieldstring"] = "order_allprice";
//        $field[$k]["fieldname"] = "订单总金额";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//        $k++;
//
//        $field[$k]["fieldstring"] = "order_coupon_price";
//        $field[$k]["fieldname"] = "优惠券抵扣金额";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//        $k++;
//
//        $field[$k]["fieldstring"] = "order_market_price";
//        $field[$k]["fieldname"] = "营销活动抵扣金额";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//        $k++;
//
//        $field[$k]["fieldstring"] = "order_paymentprice";
//        $field[$k]["fieldname"] = "订单应付总金额";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//        $k++;
//
//        $field[$k]["fieldstring"] = "order_paidprice";
//        $field[$k]["fieldname"] = "订单已付总金额";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//        $k++;
//
//        $field[$k]["fieldstring"] = "order_arrearageprice";
//        $field[$k]["fieldname"] = "订单欠费总金额";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//        $k++;
//
//        if (isset($request['pay_type']) && $request['pay_type'] !== '') {
//            switch ($request['pay_type']) {
//                case "0":
//                    $field[$k]["fieldstring"] = "ordercourse_totalprice";
//                    $field[$k]["fieldname"] = "课程应收金额";
//                    $field[$k]["show"] = 1;
//                    $field[$k]["custom"] = 1;
//                    $k++;
//
//                    $field[$k]["fieldstring"] = "ordercourse_paidprice";
//                    $field[$k]["fieldname"] = "课程已付金额";
//                    $field[$k]["show"] = 1;
//                    $field[$k]["custom"] = 1;
//                    $k++;
//
//                    $field[$k]["fieldstring"] = "ordercourse_ownprice";
//                    $field[$k]["fieldname"] = "课程欠费金额";
//                    $field[$k]["show"] = 1;
//                    $field[$k]["custom"] = 1;
//                    $k++;
//                    break;
//                case "1":
//                    $field[$k]["fieldstring"] = "ordergoods_totalprice";
//                    $field[$k]["fieldname"] = "教材应收金额";
//                    $field[$k]["show"] = 1;
//                    $field[$k]["custom"] = 1;
//                    $k++;
//
//                    $field[$k]["fieldstring"] = "ordergoods_paidprice";
//                    $field[$k]["fieldname"] = "教材已付金额";
//                    $field[$k]["show"] = 1;
//                    $field[$k]["custom"] = 1;
//                    $k++;
//
//                    $field[$k]["fieldstring"] = "ordergoods_ownprice";
//                    $field[$k]["fieldname"] = "教材欠费金额";
//                    $field[$k]["show"] = 1;
//                    $field[$k]["custom"] = 1;
//                    $k++;
//                    break;
//                case "2":
//                    $field[$k]["fieldstring"] = "orderitem_totalprice";
//                    $field[$k]["fieldname"] = "杂费应收金额";
//                    $field[$k]["show"] = 1;
//                    $field[$k]["custom"] = 1;
//                    $k++;
//
//                    $field[$k]["fieldstring"] = "orderitem_paidprice";
//                    $field[$k]["fieldname"] = "杂费已付金额";
//                    $field[$k]["show"] = 1;
//                    $field[$k]["custom"] = 1;
//                    $k++;
//
//                    $field[$k]["fieldstring"] = "orderitem_ownprice";
//                    $field[$k]["fieldname"] = "杂费欠费金额";
//                    $field[$k]["show"] = 1;
//                    $field[$k]["custom"] = 1;
//                    $k++;
//                    break;
//                default:
//                    break;
//            }
//        }
//
//        $result = array();
//        $result["field"] = $field;
//        $result["allnum"] = $res['allnum'];
//        if ($res) {
//            $result["fieldcustom"] = 1;
//            $result["list"] = $res['list'];
//            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
//        } else {
//            $result["list"] = array();
//            $result["allnum"] = 0;
//            $res = array('error' => 1, 'errortip' => "暂无学员未缴费记录", 'result' => $result);
//        }
//        ajax_return($res, $request['language_type']);
//
//    }

    //学员实际欠费明细表--X
    function studentUnReceView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Report\Smc\FinanceReportModel($request);
        $res = $ReportModel->studentUnReceReport($request);

        $field = array();

        $k = 0;

        $field[$k]["fieldstring"] = "student_id";
        $field[$k]["fieldname"] = "学员ID";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "student_branch";
        $field[$k]["fieldname"] = "学员编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["fieldname"] = "学员中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_enname";
        $field[$k]["fieldname"] = "学员英文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "channel_name";
        $field[$k]["fieldname"] = "专案名称";
        $field[$k]["show"] = (isset($request["company_id"]) && $request["company_id"] == '8888') ? 1 : 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "course_branch";
        $field[$k]["fieldname"] = "课程别";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_branch";
        $field[$k]["fieldname"] = "班级编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_cnname";
        $field[$k]["fieldname"] = "班级名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_enname";
        $field[$k]["fieldname"] = "班级别名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "ordercourse_totalprice";
        $field[$k]["fieldname"] = "购买金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "ordercourse_buynums";
        $field[$k]["fieldname"] = "购买课次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "unpaid_money";
        $field[$k]["fieldname"] = "欠费金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "unpaid_times";
        $field[$k]["fieldname"] = "欠费课次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "unpaid_day";
        $field[$k]["fieldname"] = "欠费日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res['list']) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => $ReportModel->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    //班级结算学员收入明细报表
    function classEndStudentIncomeView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Smc\ReportModel($request);
        $res = $ReportModel->classEndStudentIncome($request);
        $field = array();


        $k = 0;

        $field[$k]["fieldstring"] = "school_id";
        $field[$k]["fieldname"] = "学校id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_id";
        $field[$k]["fieldname"] = "班级id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "class_branch";
        $field[$k]["fieldname"] = "班级编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_cnname";
        $field[$k]["fieldname"] = "班级名称";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_enname";
        $field[$k]["fieldname"] = "班级别名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_period";
        $field[$k]["fieldname"] = "开班时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_id";
        $field[$k]["fieldname"] = "学员id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "student_branch";
        $field[$k]["fieldname"] = "学员编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["fieldname"] = "学员中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_enname";
        $field[$k]["fieldname"] = "学员英文名";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_period";
        $field[$k]["fieldname"] = "就读时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

//        $field[$k]["fieldstring"] = "leave_times";
//        $field[$k]["fieldname"] = "请假课次";
//        $field[$k]["show"] = 0;
//        $field[$k]["custom"] = 1;
//        $k++;

        $field[$k]["fieldstring"] = "rece_times";
        $field[$k]["fieldname"] = "冲销课次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "rece_amount";
        $field[$k]["fieldname"] = "冲销金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "back_times";
        $field[$k]["fieldname"] = "结算课次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "back_amount";
        $field[$k]["fieldname"] = "结算金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "used_times";
        $field[$k]["fieldname"] = "已认列课次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "used_amount";
        $field[$k]["fieldname"] = "已认列金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "left_times";
        $field[$k]["fieldname"] = "未认列课次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "left_amount";
        $field[$k]["fieldname"] = "未认列金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res['list']) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => "暂无班级结算学员收入记录", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //学员续费预估统计表
    function studCourseEstimateView()
    {
        ini_set("memory_limit", '64M');
        set_time_limit(600);
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Report\Smc\FinanceReportModel($request);
        $res = $ReportModel->studCourseEstimateSummary($request);
        $field = array();

        $k = 0;

        $field[$k]["fieldstring"] = "school_id";
        $field[$k]["fieldname"] = "学校id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_id";
        $field[$k]["fieldname"] = "学员id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "student_branch";
        $field[$k]["fieldname"] = "学员编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["fieldname"] = "学员中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_enname";
        $field[$k]["fieldname"] = "学员英文名";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "is_renewal";
        $field[$k]["fieldname"] = "是否续费";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "renewal_times";
        $field[$k]["fieldname"] = "续费评估课次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "renewal_amount";
        $field[$k]["fieldname"] = "班组剩余金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "unpaid_price";
        $field[$k]["fieldname"] = "班组欠费金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "spend_price";
        $field[$k]["fieldname"] = "后续耗课金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "estimate_price";
        $field[$k]["fieldname"] = "续费评估金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "connect_times";
        $field[$k]["fieldname"] = "电访结果";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["ismethod"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "connect_main";
        $field[$k]["fieldname"] = "主管电访结果";
        $field[$k]["show"] = (isset($request["company_id"]) && $request["company_id"] == '8888') ? 1 : 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "track_note";
        $field[$k]["fieldname"] = "沟通内容";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursebalance_time";
        $field[$k]["fieldname"] = "剩余课次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursebalance_figure";
        $field[$k]["fieldname"] = "剩余课时金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "left_free_times";
        $field[$k]["fieldname"] = "剩余免费课次";
        $field[$k]["show"] = (isset($request["company_id"]) && $request["company_id"] == '8888') ? 1 : 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "channel_name";
        $field[$k]["fieldname"] = "专案名称";
        $field[$k]["show"] = (isset($request["company_id"]) && $request["company_id"] == '8888') ? 1 : 0;
        $field[$k]["custom"] = 1;
        $k++;

//        $field[$k]["fieldstring"] = "free_times_left_sent";
//        $field[$k]["fieldname"] = "已赠送剩余免费课";
//        $field[$k]["show"] = (isset($request["company_id"]) && $request["company_id"] == '8888') ? 1 : 0;
//        $field[$k]["custom"] = 1;
//        $k++;
//
//        $field[$k]["fieldstring"] = "free_times_left_unsent";
//        $field[$k]["fieldname"] = "未赠送免费课次";
//        $field[$k]["show"] = (isset($request["company_id"]) && $request["company_id"] == '8888') ? 1 : 0;
//        $field[$k]["custom"] = 1;
//        $k++;
//
//        $field[$k]["fieldstring"] = "free_times_left_all";
//        $field[$k]["fieldname"] = "剩余总免费课次";
//        $field[$k]["show"] = (isset($request["company_id"]) && $request["company_id"] == '8888') ? 1 : 0;
//        $field[$k]["custom"] = 1;
//        $k++;

        $field[$k]["fieldstring"] = "class_id";
        $field[$k]["fieldname"] = "班级id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "class_branch";
        $field[$k]["fieldname"] = "班级编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_cnname";
        $field[$k]["fieldname"] = "班级名称";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_enname";
        $field[$k]["fieldname"] = "班级别名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_enddate";
        $field[$k]["fieldname"] = "班级结束日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "main_teacher";
        $field[$k]["fieldname"] = "主教教师";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "course_branch";
        $field[$k]["fieldname"] = "课程别编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "course_cnname";
        $field[$k]["fieldname"] = "课程别名称";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursecat_branch";
        $field[$k]["fieldname"] = "班种编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursecat_cnname";
        $field[$k]["fieldname"] = "班种名称";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursetype_branch";
        $field[$k]["fieldname"] = "班组编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursetype_cnname";
        $field[$k]["fieldname"] = "班组名称";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_num";
        $field[$k]["fieldname"] = "班级课次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "course_class_num";
        $field[$k]["fieldname"] = "课程别设定课次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res['list']) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => "暂无学员续费预估信息", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //学员实际留班统计表
    function studEndcalcEstimateView()
    {
        error_reporting(E_ALL);
        ini_set('display_errors', '1');
        ini_set("memory_limit", '-1');
        set_time_limit(600);
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Report\Smc\FinanceReportModel($request);
        $res = $ReportModel->studEndcalcEstimateSummary($request);
        $field = array();

        $k = 0;

        $field[$k]["fieldstring"] = "school_id";
        $field[$k]["fieldname"] = "学校id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "student_id";
        $field[$k]["fieldname"] = "学员id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "student_branch";
        $field[$k]["fieldname"] = "学员编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["fieldname"] = "学员中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_enname";
        $field[$k]["fieldname"] = "学员英文名";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "channel_name";
        $field[$k]["fieldname"] = "专案名称";
        $field[$k]["show"] = (isset($request["company_id"]) && $request["company_id"] == '8888') ? 1 : 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "is_renewal";
        $field[$k]["fieldname"] = "是否续费";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "study_nexttimes";
        $field[$k]["fieldname"] = "续费课次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "study_nextprice";
        $field[$k]["fieldname"] = "续费金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "study_upgraderate";
        $field[$k]["fieldname"] = "留班课时比";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "connect_times";
        $field[$k]["fieldname"] = "电访结果";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "connect_main";
        $field[$k]["fieldname"] = "主管电访结果";
        $field[$k]["show"] = (isset($request["company_id"]) && $request["company_id"] == '8888') ? 1 : 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "track_note";
        $field[$k]["fieldname"] = "沟通内容";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_id";
        $field[$k]["fieldname"] = "班级id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "class_branch";
        $field[$k]["fieldname"] = "班级编号";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_cnname";
        $field[$k]["fieldname"] = "班级名称";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_enname";
        $field[$k]["fieldname"] = "班级别名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_enddate";
        $field[$k]["fieldname"] = "班级结束日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "study_endday";
        $field[$k]["fieldname"] = "学员出班日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "main_teacher";
        $field[$k]["fieldname"] = "主教教师";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "course_branch";
        $field[$k]["fieldname"] = "课程别编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "course_cnname";
        $field[$k]["fieldname"] = "课程别名称";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursecat_branch";
        $field[$k]["fieldname"] = "班种编号";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursecat_cnname";
        $field[$k]["fieldname"] = "班种名称";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursetype_branch";
        $field[$k]["fieldname"] = "班组编号";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursetype_cnname";
        $field[$k]["fieldname"] = "班组名称";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_num";
        $field[$k]["fieldname"] = "班级课次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "course_class_num";
        $field[$k]["fieldname"] = "课程别设定课次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res['list']) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => "暂无学员续费预估信息", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    //学员续课包预估统计表
    function studPackageEstimateView()
    {
        ini_set("memory_limit", '64M');
        set_time_limit(600);
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Report\Smc\FinanceReportModel($request);
        $res = $ReportModel->studPackageEstimateDetail($request);
        $field = array();

        $k = 0;
        $field[$k]["fieldstring"] = "school_id";
        $field[$k]["fieldname"] = "学校id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "student_id";
        $field[$k]["fieldname"] = "学员id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "student_branch";
        $field[$k]["fieldname"] = "学员编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["fieldname"] = "学员中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_enname";
        $field[$k]["fieldname"] = "学员英文名";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "hour_isbuy";
        $field[$k]["fieldname"] = "是否续费";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_id";
        $field[$k]["fieldname"] = "班级id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "class_branch";
        $field[$k]["fieldname"] = "班级编号";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_cnname";
        $field[$k]["fieldname"] = "班级名称";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_enname";
        $field[$k]["fieldname"] = "班级别名";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "course_branch";
        $field[$k]["fieldname"] = "课程别编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "course_cnname";
        $field[$k]["fieldname"] = "课程别名称";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursecat_branch";
        $field[$k]["fieldname"] = "班种编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursecat_cnname";
        $field[$k]["fieldname"] = "班种名称";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursetype_branch";
        $field[$k]["fieldname"] = "班组编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursetype_cnname";
        $field[$k]["fieldname"] = "班组名称";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "track_result";
        $field[$k]["fieldname"] = "电访结果";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["ismethod"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "track_note";
        $field[$k]["fieldname"] = "沟通内容";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursebalance_time";
        $field[$k]["fieldname"] = "课程剩余课次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursebalance_figure";
        $field[$k]["fieldname"] = "课程剩余金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "hour_day";
        $field[$k]["fieldname"] = "应续包日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res['list']) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => "暂无学员课包续费预估信息", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //续课包电访执行统计表
    function studPackageTrackSummaryView()
    {
        ini_set("memory_limit", '64M');
        set_time_limit(600);
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Report\Smc\FinanceReportModel($request);
        $res = $ReportModel->studPackageTrackSummary($request);
        $field = array();

        $k = 0;
        $field[$k]["fieldstring"] = "school_id";
        $field[$k]["fieldname"] = "学校id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "district_cnname";
        $field[$k]["fieldname"] = "所属区域";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "total_num";
        $field[$k]["fieldname"] = "应续课包人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "renew_num";
        $field[$k]["fieldname"] = "已续课包人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "unrenew_num";
        $field[$k]["fieldname"] = "未缴费电访人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "track_renewal_num";
        $field[$k]["fieldname"] = "电访表示续课包人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "track_consider_num";
        $field[$k]["fieldname"] = "电访表示考虑人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "track_dicide_num";
        $field[$k]["fieldname"] = "电访确认流失";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "track_notyet_num";
        $field[$k]["fieldname"] = "未进行电访人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["ismethod"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "track_percentage";
        $field[$k]["fieldname"] = "电访执行率";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "track_main_percentage";
        $field[$k]["fieldname"] = "主管电访执行率";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "renewal_percentage";
        $field[$k]["fieldname"] = "目前续课包率";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res['list']) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => "暂无学员续课包执行统计信息", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function getCourseInfoApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $datawhere = " a.company_id='{$request['company_id']}' and a.school_id='{$request['school_id']}' ";

        if (isset($request['end_time']) && $request['end_time'] !== '') {
            $lastday = date("Y-m-d", strtotime($request['end_time']));
            $datawhere .= " and a.hour_day<= '{$lastday}'";
        }

        if (isset($request['start_time']) && $request['start_time'] !== '') {
            $firstday = date("Y-m-d", strtotime($request['start_time']));
            $datawhere .= " and a.hour_day>= '{$firstday}'";
        }

        $now = date("Y-m-d", time());
        $sql = "select a.coursetype_id
         ,b.coursetype_branch
         ,b.coursetype_cnname
         ,a.coursecat_id
         ,c.coursecat_branch
         ,c.coursecat_cnname
         ,a.course_id
         ,d.course_branch         
         ,d.course_cnname
         from temp_smc_hourplan a
         left join smc_code_coursetype b on a.coursetype_id=b.coursetype_id
         left join smc_code_coursecat c on a.coursecat_id=c.coursecat_id
         left join smc_course d on a.course_id=d.course_id         
         where {$datawhere}
         group by a.course_id";


        $list = $this->DataControl->selectClear($sql);
        if (!$list) {
            $list = array();
        }

        if ($list) {
            $result["list"] = $list;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '无班组等信息,获取失败', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function getUnTrackApi()
    {
        $k = 0;
        $field = array();
        $field[$k]["fieldstring"] = "student_branch";
        $field[$k]["fieldname"] = "学员编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["fieldname"] = "学员中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_enname";
        $field[$k]["fieldname"] = "学员英文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $request = Input('get.', '', 'trim,addslashes');
        $datawhere = " a.company_id='{$request['company_id']}' and a.school_id='{$request['school_id']}' ";

        if (isset($request['end_time']) && $request['end_time'] !== '') {
            $lastday = date("Y-m-d", strtotime($request['end_time']));
            $datawhere .= " and a.hour_day<= '{$lastday}'";
        }

        if (isset($request['start_time']) && $request['start_time'] !== '') {
            $firstday = date("Y-m-d", strtotime($request['start_time']));
            $datawhere .= " and a.hour_day>= '{$firstday}'";
        }

        if (isset($request['coursetype_id']) && $request['coursetype_id'] !== '') {
            $datawhere .= " and d.coursetype_id= '{$request['coursetype_id']}'";
        }

        $sql = "select ta.*,tb.student_branch,tb.student_cnname,tb.student_enname,
                ifnull((SELECT rs.trackresult_name FROM smc_student_track AS st, smc_code_trackresult AS rs WHERE rs.trackresult_id = st.result_id 
                    AND st.student_id = ta.student_id AND st.track_classname = '续费电访' AND track_day>=ta.min_date and track_day<=ta.max_date
                    and st.track_from=0 and st.school_id=ta.school_id and st.coursetype_id=ta.coursetype_id ORDER BY st.track_id DESC LIMIT 0,1),'') AS track_result
            from (
            select a.hourplan_id
            ,a.student_id
            ,a.coursetype_id
            ,a.school_id
            ,ifnull((select max(hour_isbuy) from temp_smc_hourplan where student_id=a.student_id and school_id=a.school_id and hourplan_rank>a.hourplan_rank),0) as hour_isbuy
            ,ifnull((select min(hour_day) from temp_smc_hourplan where student_id=a.student_id and school_id=a.school_id and hourplan_rank>a.hourplan_rank),a.hour_day) as max_date
            ,ifnull((select hour_day from temp_smc_hourplan where student_id=a.student_id and school_id=a.school_id and hourplan_rank < a.hourplan_rank order by hourplan_rank desc limit 3,1),a.hour_day) as min_date
            from temp_smc_hourplan a
            left join smc_class_hour b on a.hour_id=b.hour_id
            left join smc_class c on a.class_id=c.class_id
            left join smc_course d on c.course_id=d.course_id
            where {$datawhere}
            and a.hour_isnode=1
            and b.hour_ischecking>-1
            and c.class_status>-2
            and d.course_sellclass=1
            ) ta
            left join smc_student tb on ta.student_id=tb.student_id
            where 1
            having track_result='' and hour_isbuy=0
        ";

        $unconcatList = $this->DataControl->selectClear($sql);
        if (!$unconcatList) {
            $unconcatList = array();
        }

        $result["field"] = $field;
        $result["list"] = $unconcatList;
        $res = array('error' => 0, 'errortip' => '获取弹窗成功', 'result' => $result);
        ajax_return($res, $request['language_type']);
    }

}
