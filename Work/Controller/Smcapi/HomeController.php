<?php


namespace Work\Controller\Smcapi;


class HomeController extends viewTpl
{
    public $u;
    public $t;
    public $c;

    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
    }

    function checkPsdApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
//        $this->ThisVerify($request);
        $str = md5(123456);
        $one = $this->DataControl->getFieldOne("smc_staffer", "staffer_pass", "staffer_id='{$request['staffer_id']}' and company_id='{$request['company_id']}'");
        if (!$one) {
            $res = array('error' => 1, 'errortip' => '无该职工', 'result' => array());
            ajax_return($res, $request['language_type']);
        }

        if ($str == $one['staffer_pass']) {
            $res = array('error' => 1, 'errortip' => '密码是初始密码', 'result' => array());
        } else {
            $res = array('error' => 0, 'errortip' => '成功', 'result' => array());
        }
        ajax_return($res, $request['language_type']);
    }

    function warningApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\HomeModel($request);
        $res = $Model->warning($request);

        if ($res) {
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $res);
        } else {
            $res = array('error' => 1, 'errortip' => '获取失败', 'result' => $res);
        }
        ajax_return($res, $request['language_type']);
    }

    function statisticsInfoApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\HomeModel($request);
        $res = $Model->statisticsInfo($request);

        if ($res) {
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $res);
        } else {
            $res = array('error' => 1, 'errortip' => '获取失败', 'result' => $res);
        }
        ajax_return($res, $request['language_type']);
    }

    //待处理事项
    function toDoInfoApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\HomeModel($request);
        $res = $Model->toDoInfo($request);

        if ($res) {
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $res);
        } else {
            $res = array('error' => 1, 'errortip' => '获取失败', 'result' => $res);
        }
        ajax_return($res, $request['language_type']);

    }

    function gardenIncomeApi()
    {
        $request = Input('get.', '', 'strip_tags,trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\HomeModel($request);
        $res = $Model->gardenIncome($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    function chargeInfoApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\HomeModel($request);
        $res = $Model->chargeInfo($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    function enrolmentNumberApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\HomeModel($request);
        $res = $Model->enrolmentNumber($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function hourIncomeApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\HomeModel($request);
        $res = $Model->hourIncome($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function attendanceApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\HomeModel($request);
        $res = $Model->attendance($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function reminderNoticeApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\HomeModel($request);
        $res = $Model->reminderNotice($request);

        if ($res) {
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $res);
        } else {
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $res);
        }
        ajax_return($res, $request['language_type']);
    }

    //校务统计中心 数据部分查询 --20200403 zjb
    function schoolInfoApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Smc\HomeModel($request);
        $res = $Model->schoolInfo($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    //首页 -- 月份列表日程展示  --- 20200402  97
    function monthEventApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        //model
        $Model = new \Model\Smc\HomeModel($request);
        $dataList = $Model->monthEventApi($request);

        $field = array();
        $field["event_id"] = "事件序号";
        $field["year"] = "年";
        $field["month"] = "月";
        $field["day"] = "日";
        $field["is_have"] = "是否有事件：-1 没有";

        $result = array();
        if ($dataList) {
            $result["field"] = $field;
            $result["theWeek"] = $dataList['week'];
            $result["data"] = $dataList['mothList'];
            $res = array('error' => '0', 'errortip' => '某日的日程安排', 'result' => $result);
        } else {
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '某日的日程安排', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //首页 -- 某日的日程安排  --- 20200402  97
    function eventOneApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        if (!isset($request['event_time']) || $request['event_time'] == '') {
            $res = array('error' => '1', 'errortip' => '必须输入查询时间', 'result' => array());
        }
        //model
        $Model = new \Model\Smc\HomeModel($request);
        $dataList = $Model->eventOneApi($request);

        $field = array();
        $field["event_tag"] = "事件标签：字符串";
        $field["event_remark"] = "跟进提醒备注";

        $result = array();
        if ($dataList) {
            $result["field"] = $field;
            $result["data"] = $dataList;
            $res = array('error' => '0', 'errortip' => '某日的日程安排', 'result' => $result);
        } else {
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '某日的日程安排', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //首页 -- 新增日程安排  --- 20200402  97
    function addEventAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        //model
        $Model = new \Model\Smc\HomeModel($request);
        $dataList = $Model->addEventAction($request);

        if ($dataList) {
            $res = array('error' => '0', 'errortip' => "日程安排新增成功");
        } else {
            $res = array('error' => '1', 'errortip' => '日程安排新增失败');
        }
        ajax_return($res, $request['language_type']);
    }

    function sendMsgAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\HomeModel($request);
        $res = $Model->test('13127587321', array(123456), '417315');

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '发送成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

}
