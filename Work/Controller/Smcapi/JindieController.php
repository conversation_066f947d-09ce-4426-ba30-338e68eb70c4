<?php

namespace Work\Controller\Smcapi;

/**
 * 金蝶云苍穹费用报销单接口控制器
 *
 * 提供费用报销单相关的API接口，包括：
 * 1. 保存费用报销单 - saveAction()
 * 2. 提交费用报销单 - submitAction()
 * 3. 获取报销单模板 - templateApi()
 *
 * 所有接口无需验证参数，可直接调用
 *
 * 返回格式统一为：
 * {
 *   "error": 0/1,           // 0-成功，1-失败
 *   "errortip": "消息内容", // 提示信息
 *   "result": {             // 结果数据
 *     "data": {...}         // 具体数据
 *   }
 * }
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2023-12-01
 */
class JindieController extends viewTpl
{
    public $u; // 控制器名称
    public $t; // URL路径
    public $c; // 动作名称

    /**
     * 构造函数
     * 初始化路由相关属性
     */
    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
    }

    /**
     * 保存费用报销单接口
     *
     * 接口地址：POST /smcapi/jindie/saveAction
     *
     * 业务参数：
     * @param array Model 报销单数据模型，包含以下主要字段：
     *   单据头字段：
     *   - FDate: 申请日期（必填）格式：YYYY-MM-DD
     *   - FCausa: 事由（必填）
     *   - FProposerID: 申请人（必填）格式：{"FSTAFFNUMBER": "员工编码"}
     *   - FRequestDeptID: 申请部门（必填）格式：{"FNUMBER": "部门编码"}
     *   - FExpenseDeptID: 费用承担部门（必填）格式：{"FNUMBER": "部门编码"}
     *   - FCurrencyID: 币别（必填）格式：{"FNUMBER": "币别编码"}
     *   - FOrgID: 申请组织（必填）格式：{"FNumber": "组织编码"}
     *   - FBillTypeID: 单据类型（必填）格式：{"FNUMBER": "单据类型编码"}
     *   - FExpenseOrgId: 费用承担组织（必填）格式：{"FNumber": "组织编码"}
     *   - FCONTACTUNITTYPE: 往来单位类型（必填）
     *   - FCONTACTUNIT: 往来单位（必填）格式：{"FNumber": "往来单位编码"}
     *   - FExchangeTypeID: 汇率类型（必填）格式：{"FNUMBER": "汇率类型编码"}
     *   - FLocCurrencyID: 本位币（必填）格式：{"FNUMBER": "币别编码"}
     *
     *   报销明细字段（FEntity数组）：
     *   - FExpID: 费用项目（必填）格式：{"FNUMBER": "费用项目编码"}
     *   - FInvoiceType: 发票类型（必填）
     *   - FExpenseAmount: 申请报销金额
     *   - FRequestAmount: 申请退/付款金额
     *   - FRemark: 备注
     *   - FExpenseDeptEntryID: 费用承担部门（必填）格式：{"FNUMBER": "部门编码"}
     *   - F_JDB_FDFYXM: 法定费用项目（必填）格式：{"FNUMBER": "费用项目编码"}
     *
     * 可选参数：
     * @param array NeedUpDateFields 需要更新的字段数组
     * @param array NeedReturnFields 需返回结果的字段数组
     * @param bool IsDeleteEntry 是否删除已存在的分录，默认true
     * @param bool ValidateFlag 是否验证标志，默认true
     * @param bool NumberSearch 是否用编码搜索基础资料，默认true
     *
     * 返回结果：
     * @return array 统一格式的响应数据
     *   - error: 0-成功，1-失败
     *   - errortip: 提示信息
     *   - result.data: 保存成功后的单据信息，包含单据ID、编号等
     *
     * 错误代码说明：
     * - 0: 默认
     * - 1: 上下文丢失
     * - 2: 没有权限
     * - 4: 异常
     * - 8: 参数错误
     * - 11: 验证失败
     *
     * 使用示例：
     * POST /smcapi/jindie/saveAction
     * Content-Type: application/x-www-form-urlencoded
     *
     * token=your_token&staffer_id=1&school_id=1&company_id=1&Model={"FDate":"2023-12-01","FCausa":"差旅费报销",...}
     */
    function saveAction()
    {
        // 获取POST请求参数并进行安全过滤
        $request = Input('post.', '', 'strip_tags,trim,addslashes');

        // 创建金蝶云苍穹模型实例
        $Model = new \Model\Smc\JindieModel();

        // 调用保存费用报销单方法
        $res = $Model->saveExpenseReimbursement($request);

        // 处理返回结果
        if ($res && $res['success']) {
            // 保存成功
            $result["data"] = $res['data'];
            $result = array('error' => 0, 'errortip' => $Model->errortip, 'result' => $result);
        } else {
            // 保存失败
            $result["data"] = array();
            $result = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }

        // 返回JSON格式响应
        ajax_return($result, $request['language_type']);
    }



    /**
     * 提交费用报销单接口（将已保存的报销单提交到工作流）
     *
     * 接口地址：POST /smcapi/jindie/submitAction
     *
     * 业务参数（二选一）：
     * @param array Numbers 单据编码集合，格式：["FYBS001", "FYBS002", ...]
     *   使用单据编码提交时必填，支持批量提交多个报销单
     * @param string Ids 单据内码集合，格式："123,456,789"
     *   使用单据内码提交时必填，多个内码用逗号分隔
     *
     * 可选参数：
     * @param int CreateOrgId 创建者组织内码
     * @param int SelectedPostId 工作流发起员工岗位内码
     *   注：员工身兼多岗时不传参默认取第一个岗位
     * @param bool NetworkCtrl 是否启用网控，默认false
     * @param bool IgnoreInterationFlag 是否允许忽略交互，默认true
     *
     * 返回结果：
     * @return array 统一格式的响应数据
     *   - error: 0-成功，1-失败
     *   - errortip: 提示信息
     *   - result.data: 提交结果，包含成功提交的单据信息
     *
     * 业务流程：
     * 1. 报销单必须先通过保存接口保存成功
     * 2. 保存成功后获得单据编号或内码
     * 3. 使用本接口将报销单提交到审批流程
     * 4. 提交成功后，报销单进入相应的审批环节
     *
     * 注意事项：
     * 1. 只有保存状态的报销单才能提交
     * 2. 已提交或已审批的报销单不能重复提交
     * 3. 提交后的报销单不能再修改，只能撤回后重新提交
     * 4. Numbers和Ids参数至少提供一个
     *
     * 使用示例：
     * POST /smcapi/jindie/submitAction
     * Content-Type: application/x-www-form-urlencoded
     *
     * // 使用单据编码提交
     * token=your_token&staffer_id=1&school_id=1&company_id=1&Numbers=["FYBS001","FYBS002"]
     *
     * // 使用单据内码提交
     * token=your_token&staffer_id=1&school_id=1&company_id=1&Ids="123,456"
     */
    function submitAction()
    {
        // 获取POST请求参数并进行安全过滤
        $request = Input('post.', '', 'strip_tags,trim,addslashes');

        // 创建金蝶云苍穹模型实例
        $Model = new \Model\Smc\JindieModel();

        // 调用提交费用报销单方法
        $res = $Model->submitExpenseReimbursement($request);

        // 处理返回结果
        if ($res && $res['success']) {
            // 提交成功
            $result["data"] = $res['data'];
            $result = array('error' => 0, 'errortip' => $res['message'], 'result' => $result);
        } else {
            // 提交失败
            $result["data"] = array();
            $result = array('error' => 1, 'errortip' => $res['message'], 'result' => $result);
        }

        // 返回JSON格式响应
        ajax_return($result, $request['language_type']);
    }

    function queryApi()
    {
        // 获取POST请求参数并进行安全过滤
        $request = Input('post.', '', 'strip_tags,trim,addslashes');

        // 创建金蝶云苍穹模型实例
        $Model = new \Model\Smc\JindieModel();

        // 调用保存费用报销单方法
        $res = $Model->queryExpenseReimbursement($request);

        // 处理返回结果
        if ($res && $res['success']) {
            // 保存成功
            $result["data"] = $res['data'];
            $result = array('error' => 0, 'errortip' => $res['message'], 'result' => $result);
        } else {
            // 保存失败
            $result["data"] = array();
            $result = array('error' => 1, 'errortip' => $res['message'], 'result' => $result);
        }

        // 返回JSON格式响应
        ajax_return($result, $request['language_type']);
    }

    /**
     * 获取费用报销单模板接口
     *
     * 接口地址：GET /smcapi/jindie/templateApi
     *
     * 返回结果：
     * @return array 统一格式的响应数据
     *   - error: 0-成功，1-失败
     *   - errortip: 提示信息
     *   - result.data: 报销单数据模板，包含所有必填和可选字段的结构
     *
     * 模板包含字段：
     * 单据头字段：
     * - FID: 实体主键（系统自动生成）
     * - FBillNo: 单据编号（系统自动生成）
     * - FDate: 申请日期（必填）
     * - FCurrencyID: 币别（必填）
     * - FOrgID: 申请组织（必填）
     * - FCausa: 事由（必填）
     * - FProposerID: 申请人（必填）
     * - FRequestDeptID: 申请部门（必填）
     * - FExpenseDeptID: 费用承担部门（必填）
     * - FBillTypeID: 单据类型（必填）
     * - FExpenseOrgId: 费用承担组织（必填）
     * - FCONTACTUNITTYPE: 往来单位类型（必填）
     * - FCONTACTUNIT: 往来单位（必填）
     * - FExchangeTypeID: 汇率类型（必填）
     * - FExchangeRate: 汇率
     *
     * 报销明细字段（FEntity数组）：
     * - FEntryID: 实体主键（系统自动生成）
     * - FExpID: 费用项目（必填）
     * - FInvoiceType: 发票类型（必填）
     * - FExpenseAmount: 申请报销金额
     * - FRequestAmount: 申请退/付款金额
     * - FRemark: 备注
     * - FExpenseDeptEntryID: 费用承担部门（必填）
     * - F_JDB_FDFYXM: 法定费用项目（必填）
     * - F_JDB_GLSQRQ1: 关联申请日期
     * - F_JDB_XXHD: 行销活动
     * - F_PAEZ_Integer: 有源单为1，无源单为0
     * - F_PAEZ_Assistant: 陆军行销
     *
     * 使用场景：
     * - 前端页面初始化报销单表单
     * - 了解报销单数据结构
     * - 开发时参考字段格式
     * - 数据导入时的模板参考
     *
     * 使用示例：
     * GET /smcapi/jindie/templateApi?token=your_token&staffer_id=1&school_id=1&company_id=1
     */
    function templateApi()
    {
        // 获取GET请求参数并进行安全过滤
        $request = Input('get.', '', 'trim,addslashes');

        // 创建金蝶云苍穹模型实例
        $Model = new \Model\Smc\JindieModel();

        // 获取报销单模板数据
        $res = $Model->getExpenseReimbursementTemplate();

        // 处理返回结果
        if ($res) {
            // 获取模板成功
            $result["data"] = $res;
            $result = array('error' => 0, 'errortip' => '获取模板成功', 'result' => $result);
        } else {
            // 获取模板失败
            $result["data"] = array();
            $result = array('error' => 1, 'errortip' => '获取模板失败', 'result' => $result);
        }

        // 返回JSON格式响应
        ajax_return($result, $request['language_type']);
    }

}