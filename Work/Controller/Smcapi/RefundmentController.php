<?php


namespace Work\Controller\Smcapi;


class RefundmentController extends viewTpl{
    public $u;
    public $t;
    public $c;

    function __construct() {
        parent::__construct ();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
    }


    function getRefundListApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\RefundmentModel($request);
        $res= $Model->getRefundList($request);

        $field=array();

        $field[0]["fieldstring"] = "class_id";
        $field[0]["fieldname"] = "班级ID";
        $field[0]["show"] = 0;
        $field[0]["custom"] = 0;

        $field[1]["fieldstring"] = "course_cnname";
        $field[1]["fieldname"] = "课程名称";
        $field[1]["show"] = 1;
        $field[1]["custom"] = 1;

        $field[2]["fieldstring"] = "course_branch";
        $field[2]["fieldname"] = "课程编号";
        $field[2]["show"] = 1;
        $field[2]["custom"] = 1;

        $field[3]["fieldstring"] = "class_cnname";
        $field[3]["fieldname"] = "班级名称";
        $field[3]["show"] = 1;
        $field[3]["custom"] = 1;

        $field[4]["fieldstring"] = "class_branch";
        $field[4]["fieldname"] = "班级编号";
        $field[4]["show"] = 1;
        $field[4]["custom"] = 1;

        $field[5]["fieldstring"] = "coursebalance_figure";
        $field[5]["fieldname"] = "课程剩余金额";
        $field[5]["show"] = 1;
        $field[5]["custom"] = 1;

        $field[6]["fieldstring"] = "courseforward_price";
        $field[6]["fieldname"] = "课程结转余额";
        $field[6]["show"] = 1;
        $field[6]["custom"] = 1;

        $field[7]["fieldstring"] = "coursebalance_time";
        $field[7]["fieldname"] = "课程剩余课次";
        $field[7]["show"] = 1;
        $field[7]["custom"] = 1;

        $field[8]["fieldstring"] = "classNum";
        $field[8]["fieldname"] = "班级考勤次数";
        $field[8]["show"] = 1;
        $field[8]["custom"] = 1;

        $field[9]["fieldstring"] = "stuNum";
        $field[9]["fieldname"] = "学员班内考勤次数";
        $field[9]["show"] = 1;
        $field[9]["custom"] = 1;

        $field[10]["fieldstring"] = "course_id";
        $field[10]["fieldname"] = "课程ID";
        $field[10]["show"] = 0;
        $field[10]["custom"] = 0;

        $result = array();
        $result["field"] = $field;
        if($res){
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        }else{
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '学员没有剩余课次信息', 'result' => $result);

        }
        ajax_return($res,$request['language_type']);
    }

    function refundAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\RefundmentModel($request);
        $res= $Model->refund($request);
        if($res){
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '退费申请成功', 'result' => $result);
        }else{
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);

        }
        ajax_return($res,$request['language_type']);

    }

    function stuCourseTypeApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\RefundmentModel($request);
        $res= $Model->stuCourseType($request);
        if($res){
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
        }else{
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);

        }
        ajax_return($res,$request['language_type']);
    }

}
