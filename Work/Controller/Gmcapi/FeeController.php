<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/13
 * Time: 23:25
 */

namespace Work\Controller\Gmcapi;


class FeeController extends viewTpl
{
    public $u;
    public $t;
    public $c;
    public $visitType = "api";
    public $Model;

    //预加载处理类
    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
    }

    function ThisVerify($request)
    {
        $paramArray = array();
        $paramArray['staffer_id'] = $request['staffer_id'];
        $paramArray['company_id'] = $request['company_id'];
        $paramArray['token'] = $request['token'];
        if (!$this->UserLimit($paramArray)) {
            $result = array();
            $result["list"] = array();
            $result["tokeninc"] = "0";
            $res = array('error' => '1', 'errortip' => "用户token失效", 'result' => $result);
            ajax_return($res, $request['language_type']);
        }
    }


    //收费协议列表
    function HomeView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->getAgreementList($request);
        ajax_return($result, $request['language_type']);
    }

    //添加收费协议
    function addAgreementAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->addAgreementAction($request);
        ajax_return($result, $request['language_type']);
    }

    //查看收费协议
    function getAgreementDetailView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->getAgreementDetail($request);
        ajax_return($result, $request['language_type']);
    }

    //编辑缴费协议
    function updateAgreementDetailAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->updateAgreementDetailAction($request);
        ajax_return($result, $request['language_type']);
    }

    //查看收费协议
    function getCourseView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->getCourse($request);
        ajax_return($result, $request['language_type']);
    }

    //缴费设定列表
    function getPriceListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->getPriceList($request);
        ajax_return($result, $request['language_type']);
    }

    //适用分校
    function getSetSchoolView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->getSetSchool($request);
        ajax_return($result, $request['language_type']);
    }

    //删除适用学校
    function delSetSchoolAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->delSetSchoolAction($request);
        ajax_return($result, $request['language_type']);
    }

    //删除适用学校
    function delSetTreatySchoolAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->delSetTreatySchoolAction($request);
        ajax_return($result, $request['language_type']);
    }

    //适用教材
    function getSetProductView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->getSetProduct($request);
        ajax_return($result, $request['language_type']);
    }

    //适用教材
    function getSetProductsView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->getSetProducts($request);
        ajax_return($result, $request['language_type']);
    }

    //删除适用教材
    function delSetProductAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->delSetProductAction($request);
        ajax_return($result, $request['language_type']);
    }

    //新增收费价格
    function addPriceAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->addPriceAction($request);
        ajax_return($result, $request['language_type']);
    }

    //分校列表
    function getSchoolView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->getSchool($request);
        ajax_return($result, $request['language_type']);
    }

    //缴费设定添加学校
    function addSchoolAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->addSchoolAction($request);
        ajax_return($result, $request['language_type']);
    }

    //协议设定添加学校
    function addTreatySchoolAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->addTreatySchoolAction($request);
        ajax_return($result, $request['language_type']);
    }

    //教材列表
    function getGoodsListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->getGoodsList($request);
        ajax_return($result, $request['language_type']);
    }

    //选择绑定教材
    function addProductsAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->addProductsAction($request);
        ajax_return($result, $request['language_type']);
    }

    //编辑收费项目
    function updatePriceAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->updatePriceAction($request);
        ajax_return($result, $request['language_type']);
    }

    //删除缴费设定
    function delPriceAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->delPriceAction($request);
        ajax_return($result, $request['language_type']);
    }

    //删除协议
    function delTreatyAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->delTreatyAction($request);
        ajax_return($result, $request['language_type']);
    }

    //缴费设定添加学校
    function copyAgreementAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->copyAgreementAction($request);
        ajax_return($result, $request['language_type']);
    }

    //缴费设定添加学校
    function copyAgreementtestAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->copyAgreementtestAction($request);
        ajax_return($result, $request['language_type']);
    }

    //删除缴费协议
    function delAgreementAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->delAgreementAction($request);
        ajax_return($result, $request['language_type']);
    }

    //重新设置适配课程
    function resetCourseAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->resetCourseAction($request);
        ajax_return($result, $request['language_type']);
    }

    //是否免费
    function updateIsFreeAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->updateIsFreeAction($request);
        ajax_return($result, $request['language_type']);
    }

    //是否必买
    function updateMustBuyAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->updateMustBuyAction($request);
        ajax_return($result, $request['language_type']);
    }

    //营销活动列表
    function getMarketActiveListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->getMarketActiveList($request);
        ajax_return($result, $request['language_type']);
    }

    //获取活动基本信息
    function getMarketActivityAction()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->getMarketActivityAction($request);
        ajax_return($result, $request['language_type']);
    }

    //获取活动设置
    function getActivitySetAction()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->getActivitySetAction($request);
        ajax_return($result, $request['language_type']);
    }

    //营销活动适用学校列表
    function getActiveSchoolListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->getActiveSchoolList($request);
        ajax_return($result, $request['language_type']);
    }

    //营销活动适用课程列表
    function getActiveCourseListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->getActiveCourseList($request);
        ajax_return($result, $request['language_type']);
    }

    //新增营销活动
    function addMarketActivityAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->addMarketActivityAction($request);
        ajax_return($result, $request['language_type']);
    }

    //新增营销设置
    function addActivitySetAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->addActivitySetAction($request);
        ajax_return($result, $request['language_type']);
    }

    //活动添加学校
    function addActivitySchoolAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->addActivitySchoolAction($request);
        ajax_return($result, $request['language_type']);
    }

    //活动添加课程
    function addActivityCourseAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->addActivityCourseAction($request);
        ajax_return($result, $request['language_type']);
    }

    //课程列表
    function getCourseListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->getCourseList($request);
        ajax_return($result, $request['language_type']);
    }

    //删除活动适配课程
    function DelActivityCourseAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->DelActivityCourseAction($request);
        ajax_return($result, $request['language_type']);
    }

    //删除活动适配课程
    function DelActivityAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->DelActivityAction($request);
        ajax_return($result, $request['language_type']);
    }

    //删除活动适配学校
    function DelActivitySchoolAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->DelActivitySchoolAction($request);
        ajax_return($result, $request['language_type']);
    }

    //编辑营销活动
    function updateActivityAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->updateActivityAction($request);
        ajax_return($result, $request['language_type']);
    }

    //编辑优惠方式
    function updateActivitySetAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->updateActivitySetAction($request);
        ajax_return($result, $request['language_type']);
    }

    //删除优惠方式
    function DelActivitySetAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->DelActivitySetAction($request);
        ajax_return($result, $request['language_type']);
    }

    //启用/不启用
    function OpenStatusAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->OpenStatusAction($request);
        ajax_return($result, $request['language_type']);
    }

    //优惠券列表
    function getTicketListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->getTicketList($request);
        ajax_return($result, $request['language_type']);
    }

    //新增营销活动
    function addTicketAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->addTicketAction($request);
        ajax_return($result, $request['language_type']);
    }

    //优惠券来源课程
    function getTicketFromListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->getTicketFromList($request);
        ajax_return($result, $request['language_type']);
    }

    //删除优惠券来源课程
    function DelTicketFromAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->DelTicketFromAction($request);
        ajax_return($result, $request['language_type']);
    }

    //添加优惠券来源课程
    function addTicketFromCourseAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->addTicketFromCourseAction($request);
        ajax_return($result, $request['language_type']);
    }

    //编辑优惠券优惠方式
    function addTicketWayAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->addTicketWayAction($request);
        ajax_return($result, $request['language_type']);
    }

    //添加优惠券适用学校
    function addTicketSchoolAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->addTicketSchoolAction($request);
        ajax_return($result, $request['language_type']);
    }

    //删除优惠券适用学校
    function DelTicketSchoolAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->DelTicketSchoolAction($request);
        ajax_return($result, $request['language_type']);
    }

    //优惠券适配学校列表
    function getTicketSchoolListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->getTicketSchoolList($request);
        ajax_return($result, $request['language_type']);
    }

    //优惠课程限制列表
    function getUseCourseListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->getUseCourseList($request);
        ajax_return($result, $request['language_type']);
    }

    //优惠物品限制列表
    function getUseGoodsListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->getUseGoodsList($request);
        ajax_return($result, $request['language_type']);
    }

    //删除优惠券
    function DelTicketAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->DelTicketAction($request);
        ajax_return($result, $request['language_type']);
    }

    //课程限制
    function CourseLimitAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->CourseLimitAction($request);
        ajax_return($result, $request['language_type']);
    }

    //物品限制
    function GoodsLimitAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->GoodsLimitAction($request);
        ajax_return($result, $request['language_type']);
    }

    //删除优惠适用课程
    function DelLimitCourseAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->DelLimitCourseAction($request);
        ajax_return($result, $request['language_type']);
    }

    //删除优惠适用物品
    function DelLimitGoodsAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->DelLimitGoodsAction($request);
        ajax_return($result, $request['language_type']);
    }

    //启用/不启用
    function TicketStatusAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->TicketStatusAction($request);
        ajax_return($result, $request['language_type']);
    }

    //查看优惠券详情
    function getTicketDetailView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->getTicketDetail($request);
        ajax_return($result, $request['language_type']);
    }

    //查看优惠方式
    function getTicketPolicyView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->getTicketPolicy($request);
        ajax_return($result, $request['language_type']);
    }

    //编辑营销活动
    function updateTicketAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->updateTicketAction($request);
        ajax_return($result, $request['language_type']);
    }

    //编辑杂项设置
    function addItemAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->addItemAction($request);
        ajax_return($result, $request['language_type']);
    }

    //获取收费项目列表
    function getItemPriceView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->getItemPrice($request);
        ajax_return($result, $request['language_type']);
    }

    //杂项列表
    function getSetItemView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->getSetItem($request);
        ajax_return($result, $request['language_type']);
    }

    //查看杂项
    function getItemDetailView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->getItemDetail($request);
        ajax_return($result, $request['language_type']);
    }

    //杂物是否必买
    function updateItemMustBuyAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->updateItemMustBuyAction($request);
        ajax_return($result, $request['language_type']);
    }

    //同意拒绝
    function updateStatusAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->updateStatusAction($request);
        ajax_return($result, $request['language_type']);
    }

    //是否免费
    function updateItemIsFreeAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->updateItemIsFreeAction($request);
        ajax_return($result, $request['language_type']);
    }

    //是否免费
    function AgreementStatusAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->AgreementStatusAction($request);
        ajax_return($result, $request['language_type']);
    }

    //计算单价
    function UnitPriceAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->UnitPriceAction($request);
        ajax_return($result, $request['language_type']);
    }

    //优惠券审核列表
    function getCouponsListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->getCouponsList($request);
        ajax_return($result, $request['language_type']);
    }

    //优惠券审核列表
    function getCouponsRemarkListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->getCouponsRemarkList($request);
        ajax_return($result, $request['language_type']);
    }

    //审核适配课程
    function getSetCourseView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);
        $result = $this->Model->getSetCourse($request);
        ajax_return($result, $request['language_type']);
    }

    //根据班种获取班组
    function getCoursetypeView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->getCoursetype($request);
        ajax_return($result, $request['language_type']);
    }

    //优惠政策列表
    function getPolicyListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->getPolicyList($request);
        ajax_return($result, $request['language_type']);
    }

    function getPolicyLabelListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->getPolicyLabelList($request);
        ajax_return($result, $request['language_type']);
    }

    //同班组课程下拉
    function SameCoursetypeListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->SameCoursetypeList($request);
        ajax_return($result, $request['language_type']);
    }

    //改变优惠政策状态
    function ChangePolicyStatusAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->ChangePolicyStatusAction($request);
        ajax_return($result, $request['language_type']);
    }

    //撤销申请
    function DelCouponsApplyAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->DelCouponsApplyAction($request);
        ajax_return($result, $request['language_type']);
    }

    //改变优惠政策是否免审
    function ChangePolicyIstrialAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->ChangePolicyIstrialAction($request);
        ajax_return($result, $request['language_type']);
    }

    //创建优惠政策
    function CreatePolicyAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->CreatePolicyAction($request);
        ajax_return($result, $request['language_type']);
    }

    function CreateLabelPolicyAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->CreateLabelPolicy($request);
        ajax_return($result, $request['language_type']);
    }

    //获取班种列表
    function getCourseCatView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->getCourseCat($request);
        ajax_return($result, $request['language_type']);
    }

    //获取班别列表
    function getCourseByCatView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->getCourseByCat($request);
        ajax_return($result, $request['language_type']);
    }

    //添加政策售价
    function addPolicyCourseAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->addPolicyCourseAction($request);
        ajax_return($result, $request['language_type']);
    }

    //获取享受政策学员列表
    function getEnjoyStudentListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->getEnjoyStudentList($request);
        ajax_return($result, $request['language_type']);
    }

    //删除享受政策学员
    function DelEnjoyStudentAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->DelEnjoyStudentAction($request);
        ajax_return($result, $request['language_type']);
    }

    //删除优惠政策
    function DelPolicyAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->DelPolicyAction($request);
        ajax_return($result, $request['language_type']);
    }

    //新增学员
    function AddEnjoyStudentAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->AddEnjoyStudentAction($request);
        ajax_return($result, $request['language_type']);
    }

    //编辑优惠政策信息
    function UpdateLabelPolicyAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->UpdateLabelPolicy($request);
        ajax_return($result, $request['language_type']);
    }

    //编辑优惠政策信息UpdateLabelPolicy
    function UpdatePolicyAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->UpdatePolicyAction($request);
        ajax_return($result, $request['language_type']);
    }

    //获取活动设置
    function getPolicySetAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->getPolicySetAction($request);
        ajax_return($result, $request['language_type']);
    }

    //班别选项
    function getCourseApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->getCourseApi($request);
        ajax_return($result, $request['language_type']);
    }

    //下载导入模版
    function getImportApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->getImportApi($request);
        ajax_return($result, $request['language_type']);
    }

    function ImportExcelView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $url = $request['url'];

        $ys_array = array('学员编号' => 'student_branch');
        $a = pathinfo($url);
        $options=array(
            "ssl"=>array(
                "verify_peer"=>false,
                "verify_peer_name"=>false,
            ),
        );
        file_put_contents('analysis.xls', file_get_contents($url,false,stream_context_create($options)));
        if ($a['extension'] == 'xlsx' || $a['extension'] == 'xls') {

            $sqlarray = execl_to_array("analysis.xls", $ys_array);
            array_shift($sqlarray);

            $policyOne=$this->DataControl->getFieldOne("smc_fee_policy","policy_id,policy_trial_num,applytype_branch","policy_id='{$request['policy_id']}'");

            if(!$policyOne){
                ajax_return(array('error' => 1, 'errortip' => "无对应政策"));
            }

            $couponsapplytypeOne=$this->DataControl->getFieldOne("smc_code_couponsapplytype","applytype_id,applytype_applyschool","applytype_branch='{$policyOne['applytype_branch']}' and company_id='{$request['company_id']}'");

            foreach ($sqlarray as $item) {
                $item['student_branch']=trim($item['student_branch']);
                if($item['student_branch']){
                    if($couponsapplytypeOne['applytype_applyschool']==1){
                        $sql="select a.schoolapply_id from smc_couponsapplytype_schoolapply as a,smc_student_enrolled as b,smc_student as c where a.school_id=b.school_id and b.student_id=c.student_id and a.applytype_id='{$couponsapplytypeOne['applytype_id']}' and c.student_branch='{$item['student_branch']}' and b.enrolled_status>=0 limit 0,1";

                        if(!$this->DataControl->selectOne($sql)){
                            ajax_return(array('error' => 1, 'errortip' => "存在他校学员".$item['student_branch']));
                        }
                    }elseif($couponsapplytypeOne['applytype_applyschool']==-1){
                        $sql="select a.schoolapply_id from smc_couponsapplytype_schoolapply as a,smc_student_enrolled as b,smc_student as c where a.school_id=b.school_id and b.student_id=c.student_id and a.applytype_id='{$couponsapplytypeOne['applytype_id']}' and c.student_branch='{$item['student_branch']}' and b.enrolled_status>=0 limit 0,1";

                        if($this->DataControl->selectOne($sql)){
                            ajax_return(array('error' => 1, 'errortip' => "存在他校学员".$item['student_branch']));
                        }
                    }
                }

            }

            foreach ($sqlarray as $item) {

                $item['student_branch']=trim($item['student_branch']);
                if($item['student_branch']){
                    $studentOne = $this->DataControl->getFieldOne("smc_student", "student_id", "student_branch = '{$item['student_branch']}'");

                    if($studentOne){

                        if(!$this->DataControl->getFieldOne("smc_fee_policy_student","policy_id","policy_id='{$request['policy_id']}' and student_id='{$studentOne['student_id']}'")){
                            $data = array();
                            $data['policy_id'] = $request['policy_id'];
                            $data['student_id'] = $studentOne['student_id'];
                            $data['trial_free_num'] = $policyOne['trial_free_num'];
                            $this->DataControl->insertData('smc_fee_policy_student', $data);
                        }
                    }
                }

            }

            $res = array('error' => '0', 'errortip' => '导入学员成功');
            ajax_return($res, $request['language_type']);
        } else {
            ajax_return(array('error' => 1, 'errortip' => "文件格式有误"));

        }


    }

    //获取权益学校
    function getDataequitySchoolApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->getDataequitySchoolApi($request);
        ajax_return($result, $request['language_type']);
    }

    //获取组合商品库列表
    function getWarehouseListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->getWarehouseList($request);
        ajax_return($result, $request['language_type']);
    }

    //获取组合商品库适用学校列表
    function getWarehouseSchoolListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->getWarehouseSchoolList($request);
        ajax_return($result, $request['language_type']);
    }

    //获取商品库课程组合列表
    function getCoursepacksListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->getCoursepacksList($request);
        ajax_return($result, $request['language_type']);
    }

    //新增组合课程库
    function addWarehouseFirstAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->addWarehouseFirstAction($request);
        ajax_return($result, $request['language_type']);
    }

    //添加组合课程适用学校
    function addWarehouseSchoolAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->addWarehouseSchoolAction($request);
        ajax_return($result, $request['language_type']);
    }

    //添加组合课程
    function addCoursepacksAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->addCoursepacksAction($request);
        ajax_return($result, $request['language_type']);
    }

    //选择课程列表
    function getCourseChoiceListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->getCourseChoiceList($request);
        ajax_return($result, $request['language_type']);
    }

    //删除组合商品库适配学校
    function delWarehouseSchoolAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->delWarehouseSchoolAction($request);
        ajax_return($result, $request['language_type']);
    }

    //删除组合课程
    function delCoursepacksAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->delCoursepacksAction($request);
        ajax_return($result, $request['language_type']);
    }

    //获取单个组合库
    function getWarehouseOneApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->getWarehouseOneApi($request);
        ajax_return($result, $request['language_type']);
    }

    //删除组合库
    function delWarehouseAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $FeeModel = new \Model\Gmc\FeeModel($request);

        $res = $FeeModel->delWarehouseAction($request);
        if ($res) {
            $res = array('error' => 0, 'errortip' => '删除成功', 'result' => true);
        } else {
            $res = array('error' => 1, 'errortip' => $FeeModel->errortip, 'result' => array());
        }
        ajax_return($res, $request['language_type']);
    }

    //获取单个组合课程信息
    function getCoursepacksOneApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->getCoursepacksOneApi($request);
        ajax_return($result, $request['language_type']);
    }

    //根据收费项目编号获取消耗类型
    function getExpendtypeApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->getExpendtypeApi($request);
        ajax_return($result, $request['language_type']);
    }

    //根据收费项目编号获取消耗类型
    function getCouponsFileView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->getCouponsFile($request);
        ajax_return($result, $request['language_type']);
    }

    //禁用优惠券
    function forbiddenAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Gmc\FeeModel($request);

        $result = $Model->forbiddenAction($request);

        ajax_return($result, $request['language_type']);
    }

    //编辑优惠券
    function updateCouponsAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Gmc\FeeModel($request);

        $result = $Model->updateCouponsAction($request);

        ajax_return($result, $request['language_type']);
    }

    //获取优惠券附件
    function getCouponsManageListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);

        $Model = new \Model\Gmc\FeeModel($request);

        $result = $Model->getCouponsManageList($request);
        ajax_return($result, $request['language_type']);
    }


    //优惠券申请列表
    function getApplytypeListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);

        $applyList=$this->DataControl->selectClear("select applytype_id,applytype_branch,applytype_cnname,applytype_playclass from smc_code_couponsapplytype
where company_id = '{$request['company_id']}'");

        $result['applytype'] =$applyList;

        ajax_return(array('error' => '0', 'errortip' => "获取成功", 'result' => $result));
    }

    //复制优惠券
    function copyCouponsAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Gmc\FeeModel($request);

        $result = $Model->copyCouponsAction($request);

        ajax_return($result, $request['language_type']);
    }

    //协议内容
    function getTreatyListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Gmc\FeeModel($request);

        $result = $Model->getTreatyList($request);
        ajax_return($result, $request['language_type']);
    }


    //添加协议内容
    function addTreatyAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->addTreatyAction($request);
        ajax_return($result, $request['language_type']);
    }


    //编辑协议内容
    function updateTreatyAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Gmc\FeeModel($request);

        $result = $Model->updateTreatyAction($request);

        ajax_return($result, $request['language_type']);
    }

    //编辑协议内容
    function updateTreatyTipAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Gmc\FeeModel($request);

        $result = $Model->updateTreatyTipAction($request);

        ajax_return($result, $request['language_type']);
    }

    //预览合同
    function previewContractView()
    {
        $request = Input('get.', '', 'trim,addslashes,strip_tags');
        $this->ThisVerify($request);//验证账户

        $PartyA = array();
        $treaty = $this->DataControl->selectOne("SELECT treaty_protocol,FROM_UNIXTIME(treaty_createtime,'%Y-%m-%d') as date FROM smc_fee_treaty WHERE treaty_id = '{$request['treaty_id']}'");
        $school = $this->DataControl->getFieldOne("smc_school","school_cnname,school_signet,school_address,school_phone,school_liaison,school_examine,school_register,school_permitbranch,school_permitstday,school_permitendday,school_icp,school_licensestday,school_licenseendday,school_society,school_licensestatus","school_id = '{$request['school_id']}'");
        $companies = $this->DataControl->getFieldOne("gmc_code_companies","companies_cnname,companies_signet,companies_liaison,companies_examine,companies_register,companies_permitbranch,companies_permitstday,companies_permitendday,companies_icp,companies_licensestday,companies_licenseendday,companies_society,companies_licensestatus","companies_id = '{$request['companies_id']}'");
        $PartyA['companies_cnname'] = $companies['companies_cnname'];
        $PartyA['school_cnname'] = $school['school_cnname'];
        $PartyA['school_address'] = $school['school_address'];
        $PartyA['school_phone'] = $school['school_phone'];
        if($school['school_liaison']){
            $PartyA['school_liaison'] = $school['school_liaison'];
        }else{
            $PartyA['school_liaison'] = $companies['companies_liaison'];
        }
        if($school['school_examine']){
            $PartyA['school_examine'] = $school['school_examine'];
        }else{
            $PartyA['school_examine'] = $companies['companies_examine'];
        }
        if($school['school_register']){
            $PartyA['school_register'] = $school['school_register'];
        }else{
            $PartyA['school_register'] = $companies['companies_register'];
        }
        if($school['school_permitbranch']){
            $PartyA['school_permitbranch'] = $school['school_permitbranch'];
        }else{
            $PartyA['school_permitbranch'] = $companies['companies_permitbranch'];
        }
        if($school['school_permitstday']){
            $PartyA['school_permitstday'] = $school['school_permitstday'];
        }else{
            $PartyA['school_permitstday'] = $companies['companies_permitstday'];
        }
        if($school['school_icp']){
            $PartyA['school_icp'] = $school['school_icp'];
        }else{
            $PartyA['school_icp'] = $companies['companies_icp'];
        }
        if($school['school_licensestday']){
            $PartyA['school_licensestday'] = $school['school_licensestday'];
        }else{
            $PartyA['school_licensestday'] = $companies['companies_licensestday'];
        }
        if($school['school_society']){
            $PartyA['school_society'] = $school['school_society'];
        }else{
            $PartyA['school_society'] = $companies['companies_society'];
        }
        if($school['school_signet']){
            $PartyA['school_signet'] = $school['school_signet'];
        }else{
            $PartyA['school_signet'] = $companies['companies_signet'];
        }

        $result = array();
        $result['PartyA'] = $PartyA;
        $result['date'] = $treaty['date'];
        $result['text'] = $treaty['treaty_protocol'];

        ajax_return(array('error' => 0,'errortip' => "获取成功",'result' => $result));
    }

    //适用主体
    function getSetCompaniesView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $list = $this->DataControl->selectClear("select c.companies_id,c.companies_cnname from smc_school_companies as sc left join gmc_code_companies as c on c.companies_id = sc.companies_id where sc.school_id = '{$request['school_id']}'");
        if ($list) {
            $result["list"] = $list;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '获取信息', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }


    //分校列表
    function getTreatySchoolView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->getTreatySchool($request);
        ajax_return($result, $request['language_type']);
    }

    //适用分校
    function getSetTreatySchoolView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FeeModel($request);

        $result = $this->Model->getSetTreatySchool($request);
        ajax_return($result, $request['language_type']);
    }



    //获取推荐信息
    function getShopRecommendView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Gmc\FeeModel($request);

        $result = $Model->getShopRecommend($request);

        ajax_return($result, $request['language_type']);
    }

    //获取推荐信息
    function updateOffsetDiscountAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Gmc\FeeModel($request);

        $result = $Model->updateOffsetDiscountAction($request);

        ajax_return($result, $request['language_type']);
    }

    //生成推荐申请
    function addCouponsApplyAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Gmc\FeeModel($request);

        $result = $Model->addCouponsApplyAction($request);

        ajax_return($result, $request['language_type']);
    }

    //批量生成推荐申请
    function batchaddCouponsApplyAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Gmc\FeeModel($request);

        $result = $Model->batchaddCouponsApplyAction($request);

        ajax_return($result, $request['language_type']);
    }

    //获取亲戚子女信息
    function getRelativeChildView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Gmc\FeeModel($request);

        $result = $Model->getRelativeChild($request);

        ajax_return($result, $request['language_type']);
    }

    //亲戚子女信息  -- 解绑亲戚子女
    function unboundRelativeChildView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Gmc\FeeModel($request);
        $res = $Model->unboundRelativeChild($request);

        $result = array();
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $result);
        ajax_return($res,$request['language_type']);
    }

    //一级组织下拉
    function organizeOneApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $list = $this->DataControl->selectClear("select organize_id,organize_cnname from gmc_company_organize where organizeclass_id = '{$request['organizeclass_id']}' and father_id = '0'");
        if ($list) {
            $result["list"] = $list;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '获取信息', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //二级组织下拉
    function organizeTwoApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $list = $this->DataControl->selectClear("select organize_id,organize_cnname from gmc_company_organize where father_id = '{$request['organize_id']}'");
        if ($list) {
            $result["list"] = $list;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '获取信息', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }



    //结尾魔术函数
    function __destruct()
    {

    }
}
