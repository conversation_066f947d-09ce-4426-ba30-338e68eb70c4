<?php


namespace Work\Controller\Gmcapi;


class RegistrationController extends viewTpl
{
    public $u;
    public $t;
    public $c;

    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
    }


    function getSettleInfoListApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Gmc\RegistrationModel($request);
        $res= $Model->getSettleInfoList($request);
        $field=array();

        $k=0;
        $field[$k]["fieldstring"] = "share_id";
        $field[$k]["fieldname"] = "分摊ID";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["fieldname"] = "学员中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_enname";
        $field[$k]["fieldname"] = "学员英文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_branch";
        $field[$k]["fieldname"] = "学员编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_cnname";
        $field[$k]["fieldname"] = "班级名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_branch";
        $field[$k]["fieldname"] = "班级编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "share_month";
        $field[$k]["fieldname"] = "结算月份";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "should_times";
        $field[$k]["fieldname"] = "应到";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "share_attend_times";
        $field[$k]["fieldname"] = "实到";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "share_absent_times";
        $field[$k]["fieldname"] = "缺勤";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "share_settle_price";
        $field[$k]["fieldname"] = "原分摊金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "price";
        $field[$k]["fieldname"] = "实际分摊金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

//        $field[$k]["fieldstring"] = "surplus_price";
//        $field[$k]["fieldname"] = "剩余金额";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//        $k++;

        $field[$k]["fieldstring"] = "share_status_name";
        $field[$k]["fieldname"] = "处理状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "share_apply_time";
        $field[$k]["fieldname"] = "申请时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "share_audit_time";
        $field[$k]["fieldname"] = "审核时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        if($res){
            $result["fieldcustom"] = 1;
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        }else{
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无课程信息', 'result' => $result);

        }
        ajax_return($res,$request['language_type']);
    }

    function confirmSettleAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Gmc\RegistrationModel($request);
        $res= $Model->confirmSettle($request);

        $result = array();
        if($res){
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '处理完成', 'result' => $result);
        }else{
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }

    function settleClassListApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Gmc\RegistrationModel($request);
        $res= $Model->settleClassList($request);

        $result = array();
        if($res){
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        }else{
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }

    function getSettleOneApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Gmc\RegistrationModel($request);
        $res= $Model->getSettleOne($request);

        $result = array();
        if($res){
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        }else{
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }

    function getschoolBreakoffApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Gmc\RegistrationModel($request);
        $res= $Model->getschoolBreakoff($request);
        $field=array();

        $k=0;
        $field[$k]["fieldstring"] = "breakoff_id";
        $field[$k]["fieldname"] = "拆班ID";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_cnname";
        $field[$k]["fieldname"] = "班级名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "course_cnname";
        $field[$k]["fieldname"] = "课程别名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "course_branch";
        $field[$k]["fieldname"] = "课程别编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_enname";
        $field[$k]["fieldname"] = "班级别名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_branch";
        $field[$k]["fieldname"] = "班级编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "number";
        $field[$k]["fieldname"] = "班级人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_status_name";
        $field[$k]["fieldname"] = "班级状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "progress";
        $field[$k]["fieldname"] = "上课进度";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_enddate";
        $field[$k]["fieldname"] = "结班日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "teacher_name";
        $field[$k]["fieldname"] = "主教老师";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "breakoff_type_name";
        $field[$k]["fieldname"] = "拆班类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "reason";
        $field[$k]["fieldname"] = "申请拆班原因";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "staffer_cnname";
        $field[$k]["fieldname"] = "申请人";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "breakoff_status_name";
        $field[$k]["fieldname"] = "审核状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "breakoff_apply_time";
        $field[$k]["fieldname"] = "申请时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "breakoff_audit_time";
        $field[$k]["fieldname"] = "审核时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        if($res){
            $result["fieldcustom"] = 1;
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        }else{
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无课程信息', 'result' => $result);

        }
        ajax_return($res,$request['language_type']);
    }

    function examineBreakoffAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Gmc\RegistrationModel($request);
        $res= $Model->examineBreakoff($request);

        $result = array();
        if($res){
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '审核成功', 'result' => $result);
        }else{
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }

    function getbreakoffOneApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Gmc\RegistrationModel($request);
        $res= $Model->getbreakoffOne($request);

        $result = array();
        if($res){
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        }else{
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }

    function getRegisterChangeApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Gmc\RegistrationModel($request);
        $res = $Model->getRegisterChange($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "info_id";
        $field[$k]["fieldname"] = "数据id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_branch";
        $field[$k]["fieldname"] = "学员编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["fieldname"] = "学员中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_enname";
        $field[$k]["fieldname"] = "学员英文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_sex";
        $field[$k]["fieldname"] = "学员性别";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "student_img";
        $field[$k]["fieldname"] = "学员头像";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "coursetype_cnname";
        $field[$k]["fieldname"] = "班组名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursetype_branch";
        $field[$k]["fieldname"] = "班组编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "pay_price";
        $field[$k]["fieldname"] = "首缴金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "pay_day";
        $field[$k]["fieldname"] = "原首缴日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

//        $field[$k]["fieldstring"] = "calc_day";
//        $field[$k]["fieldname"] = "业绩日期";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//        $k++;

        $field[$k]["fieldstring"] = "change_day";
        $field[$k]["fieldname"] = "申请变更首缴日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "apply_staffer_cnname";
        $field[$k]["fieldname"] = "申请人";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "apply_time";
        $field[$k]["fieldname"] = "申请时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "apply_reason";
        $field[$k]["fieldname"] = "申请原因";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "confirm_status";
        $field[$k]["fieldname"] = "审核状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "confirm_staffer_cnname";
        $field[$k]["fieldname"] = "审核人";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "confirm_time";
        $field[$k]["fieldname"] = "审核时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "confirm_reason";
        $field[$k]["fieldname"] = "备注";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "is_change";
        $field[$k]["fieldname"] = "是否可申请变更";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 0;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //编辑在籍时间
    function updateRegisterTimeChangeAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request); 
        $Model = new \Model\Gmc\RegistrationModel($request);
        $result = $Model->updateRegisterTimeChange($request);

        if ($result) {
            $res = array('error' => $result['error'], 'errortip' => $result['errortip'], 'result' => array());
        } else {
            $res = array('error' => '1', 'errortip' => '审核失败', 'result' => array());
        }
        ajax_return($res, $request['language_type']);
    }

    function getCompanyBalanceApplyListApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Gmc\RegistrationModel($request);
        $res= $Model->getCompanyBalanceApplyList($request);
        $field=array();

        $k=0;
        $field[$k]["fieldname"] = "balanceapply_id";
        $field[$k]["fieldstring"] = "申请ID";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "school_cnname";
        $field[$k]["fieldstring"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "school_branch";
        $field[$k]["fieldstring"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "student_cnname";
        $field[$k]["fieldstring"] = "学员中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "student_branch";
        $field[$k]["fieldstring"] = "学员编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "coursetype_cnname";
        $field[$k]["fieldstring"] = "班组";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "picture";
        $field[$k]["fieldstring"] = "申请凭证";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["ismethod"] = 1;
        $k++;

        $field[$k]["fieldname"] = "balanceapply_remarks";
        $field[$k]["fieldstring"] = "申请原因";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "staffer_cnname";
        $field[$k]["fieldstring"] = "申请人";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "balanceapply_status_name";
        $field[$k]["fieldstring"] = "审核状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "balanceapply_usestatus_name";
        $field[$k]["fieldstring"] = "使用状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "balanceapply_statusreason";
        $field[$k]["fieldstring"] = "审核备注";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "staff_cnname";
        $field[$k]["fieldstring"] = "审核人";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "balanceapply_addtime";
        $field[$k]["fieldstring"] = "申请时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "balanceapply_examinetime";
        $field[$k]["fieldstring"] = "审核时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        if($res){
            $result["fieldcustom"] = 1;
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        }else{
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);

        }
        ajax_return($res,$request['language_type']);
    }

    function examineBalanceApplyAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Gmc\RegistrationModel($request);
        $res= $Model->examineBalanceApply($request);

        $result = array();
        if($res){
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '审核成功', 'result' => $result);
        }else{
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }

    function getClassUncheckListApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Gmc\RegistrationModel($request);
        $res= $Model->getClassUncheckList($request);
        $field=array();

        $k=0;

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_cnname";
        $field[$k]["fieldname"] = "班级名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_enname";
        $field[$k]["fieldname"] = "班级别名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_branch";
        $field[$k]["fieldname"] = "班级编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "course_cnname";
        $field[$k]["fieldname"] = "课程别名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "hour_day";
        $field[$k]["fieldname"] = "上课日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "timeslot";
        $field[$k]["fieldname"] = "上课时间段";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "main_staffer_cnname";
        $field[$k]["fieldname"] = "主教";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "hour_name";
        $field[$k]["fieldname"] = "课次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "hour_iswarming_name";
        $field[$k]["fieldname"] = "课次类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "checkinglog_text";
        $field[$k]["fieldname"] = "考勤失败原因";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "checkinglog_createtime";
        $field[$k]["fieldname"] = "记录时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 0;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }


}