<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/13
 * Time: 23:25
 */

namespace Work\Controller\Gmcapi;


class MonitorController extends viewTpl
{
    public $u;
    public $t;
    public $c;
    public $visitType = "api";
    public $Model;

    //预加载处理类
    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
    }

    function ThisVerify($request)
    {
        $paramArray = array();
        $paramArray['staffer_id'] = $request['staffer_id'];
        $paramArray['company_id'] = $request['company_id'];
        $paramArray['token'] = $request['token'];
        if (!$this->UserLimit($paramArray)) {
            $result = array();
            $result["list"] = array();
            $result["tokeninc"] = "0";
            $res = array('error' => '1', 'errortip' => "用户token失效", 'result' => $result);
            ajax_return($res, $request['language_type']);
        }
    }

    //999013 ->监控->学校列表 -- 97
    function getDeviceSchoolListView(){
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        //model
        $Model = new \Model\Gmc\MonitorModel($request);
        $dataList = $Model->getDeviceSchoolList($request);

        $key = 0;
        $field = array();
        $field[$key]["fieldname"] = "school_shortname";
        $field[$key]["fieldstring"] = "校区名称";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "school_branch";
        $field[$key]["fieldstring"] = "校区编号";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "device_channelname";
        $field[$key]["fieldstring"] = "设备名称";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "device_deviceserial";
        $field[$key]["fieldstring"] = "设备序列号";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;


        $result = array();
        $result['field'] = $field;
        $result['data'] = $Model->result;
        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $result));

        $result = array();
        if ($dataList) {
            $result["field"] = $field;
            $result["list"] = $dataList['datalist'];
            if ($result["list"]) {
                $res = array('error' => '0', 'errortip' => '监控设备列表', 'allnum' => $dataList['count'], 'result' => $result);
            } else {
                $result["list"] = array();
                $res = array('error' => '1', 'errortip' => '暂无监控设备列表', 'allnum' => 0, 'result' => $result);
            }
        } else {
            $result["list"] = array();
            $res = array('error' => '1', 'errortip' => '暂无监控设备列表', 'allnum' => 0, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //999013 ->监控->监控设备列表 -- 97
    function getDeviceListView(){
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        //model
        $Model = new \Model\Gmc\MonitorModel($request);
        $dataList = $Model->getDeviceList($request);

        $key = 0;
        $field = array();
        $field[$key]["fieldname"] = "school_shortname";
        $field[$key]["fieldstring"] = "校区名称";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "school_branch";
        $field[$key]["fieldstring"] = "校区编号";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "device_channelname";
        $field[$key]["fieldstring"] = "设备名称";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "device_deviceserial";
        $field[$key]["fieldstring"] = "设备序列号";
        $field[$key]["show"] = 0;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "device_channelid";
        $field[$key]["fieldstring"] = "设备ID";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "device_channelno";
        $field[$key]["fieldstring"] = "设备通道号";
        $field[$key]["show"] = 0;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "position_name";
        $field[$key]["fieldstring"] = "位置名称";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "device_statusname";
        $field[$key]["fieldstring"] = "状态";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "device_channeltype";
        $field[$key]["fieldstring"] = "通道类型";
        $field[$key]["show"] = 0;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "device_remarks";
        $field[$key]["fieldstring"] = "备注";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;




        $field[$key]["fieldname"] = "device_id";
        $field[$key]["fieldstring"] = "设备ID";
        $field[$key]["show"] = 0;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "school_id";
        $field[$key]["fieldstring"] = "学校ID";
        $field[$key]["show"] = 0;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "position_id";
        $field[$key]["fieldstring"] = "位置ID";
        $field[$key]["show"] = 0;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "device_channelstatus";
        $field[$key]["fieldstring"] = "状态类型";
        $field[$key]["show"] = 0;
        $field[$key]["custom"] = 0;
        $key++;


        $result = array();
        $result['field'] = $field;
        $result['data'] = $Model->result;
        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $result));

        $result = array();
        if ($dataList) {
            $result["field"] = $field;
            $result["list"] = $dataList['datalist'];
            if ($result["list"]) {
                $res = array('error' => '0', 'errortip' => '监控设备列表', 'allnum' => $dataList['count'], 'result' => $result);
            } else {
                $result["list"] = array();
                $res = array('error' => '1', 'errortip' => '暂无监控设备列表', 'allnum' => 0, 'result' => $result);
            }
        } else {
            $result["list"] = array();
            $res = array('error' => '1', 'errortip' => '暂无监控设备列表', 'allnum' => 0, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //999014 ->监控->监控单个设备详情
    function getDeviceOneApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        //model
        $Model = new \Model\Gmc\MonitorModel($request);
        $dataList = $Model->getDeviceOneApi($request);

        $field = array();
        $field['device_id'] = '设备 id';
        $field['company_id'] = '集团ID';
        $field['school_id'] = '学校ID';
        $field['device_deviceserial'] = '设备序列号';
        $field['device_channelid'] = '设备通道id';
        $field['device_channelname'] = '设备名称(通道名称)';
        $field['device_channelno'] = '通道号';
        $field['device_channelstatus'] = '设备状态 0：离线 1：在线 -1：未知状态';
        $field['device_channelstatusname'] = '设备状态名称';
        $field['device_channeltype'] = '通道类型，10300:监控点，10302：报警输入';
        $field['device_remarks'] = '备注';
        $field['school_shortname'] = '校区名称';
        $field['school_branch'] = '校区编号';
        $field['position_id'] = '位置ID';

        $result = array();
        $result['field'] = $field;
        $result['data'] = $Model->result;

        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $result));
    }

    //999014 ->监控->监控单个设备编辑 -- 97
    function editDeviceOneApi()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        //model
        $Model = new \Model\Gmc\MonitorModel($request);
        $dataList = $Model->editDeviceOneApi($request);

        $field = array();
        $field['position_id'] = '位置ID';
        $field['device_remarks'] = '备注';

        $result = array();
        $result['field'] = $field;
        $result['data'] = $Model->result;

        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $result));
    }

    //999014 ->监控->监控多个设备编辑 -- 97
    function editSomeDeviceOneApi()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        //model
        $Model = new \Model\Gmc\MonitorModel($request);
        $dataList = $Model->editSomeDeviceOneApi($request);

        $field = array();
        $field['position_id'] = '位置ID';
        $field['device_remarks'] = '备注';

        $result = array();
        $result['field'] = $field;
        $result['data'] = $Model->result;

        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $result));
    }

    //999015 ->监控->（海康云眸）某学校的监控设备
    function getHkSchoolDeviceView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        //model
        $Model = new \Model\Gmc\MonitorModel($request);
        $dataList = $Model->getHkSchoolDevice($request);

        $key = 0;
        $field = array();

        $field[$key]["fieldname"] = "school_shortname";
        $field[$key]["fieldstring"] = "校区名称";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "school_branch";
        $field[$key]["fieldstring"] = "校区编号";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "device_deviceserial";
        $field[$key]["fieldstring"] = "设备序列号";
        $field[$key]["show"] = 0;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "device_channelname";
        $field[$key]["fieldstring"] = "设备名称";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "device_channelid";
        $field[$key]["fieldstring"] = "设备通道id";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "device_channelno";
        $field[$key]["fieldstring"] = "设备通道号";
        $field[$key]["show"] = 0;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "device_channelstatusname";
        $field[$key]["fieldstring"] = "设备状态";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "device_channelstatus";
        $field[$key]["fieldstring"] = "设备状态码";
        $field[$key]["show"] = 0;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "device_isusename";
        $field[$key]["fieldstring"] = "是否启用";
        $field[$key]["show"] = 0;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "device_isuse";
        $field[$key]["fieldstring"] = "是否启用码";
        $field[$key]["show"] = 0;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "device_channeltype";
        $field[$key]["fieldstring"] = "通道类型";
        $field[$key]["show"] = 0;
        $field[$key]["custom"] = 0;
        $key++;

        $result = array();
        $result['field'] = $field;
        $result['data'] = $Model->result;

        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $result));
    }

    //999015 ->监控->同步某个学校的监控设备 -- 97
    function pullHkSchoolDeviceView()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        //model
        $Model = new \Model\Gmc\MonitorModel($request);
        $dataList = $Model->pullHkSchoolDevice($request);

        $field = array();

        $result = array();
        $result['field'] = $field;
        $result['data'] = $Model->result;

        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $result));
    }

    //999016->监控->（集团）监控位置管理列表
    function getHaveMonitorPositionListView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        //model
        $Model = new \Model\Gmc\MonitorModel($request);
        $dataList = $Model->getHaveMonitorPositionList($request);

        $key = 0;
        $field = array();
        $field[$key]["fieldname"] = "position_id";
        $field[$key]["fieldstring"] = "位置ID";
        $field[$key]["show"] = 0;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "company_id";
        $field[$key]["fieldstring"] = "集团ID";
        $field[$key]["show"] = 0;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "position_name";
        $field[$key]["fieldstring"] = "位置名称";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "position_desc";
        $field[$key]["fieldstring"] = "位置描述";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $result = array();
        $result['field'] = $field;
        $result['data'] = $Model->result;

        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $result));
    }

    //999016->监控->（集团）监控位置管理列表
    function getMonitorPositionListView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        //model
        $Model = new \Model\Gmc\MonitorModel($request);
        $dataList = $Model->getMonitorPositionList($request);

        $key = 0;
        $field = array();
        $field[$key]["fieldname"] = "position_id";
        $field[$key]["fieldstring"] = "位置ID";
        $field[$key]["show"] = 0;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "company_id";
        $field[$key]["fieldstring"] = "集团ID";
        $field[$key]["show"] = 0;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "position_name";
        $field[$key]["fieldstring"] = "位置名称";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "position_desc";
        $field[$key]["fieldstring"] = "位置描述";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "postrolenames";
        $field[$key]["fieldstring"] = "角色名称";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $result = array();
        $result['field'] = $field;
        $result['data'] = $Model->result;

        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $result));
    }

    //999017->监控->（集团）监控位置添加 --97
    function addMonitorPositionOneApi()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        //model
        $Model = new \Model\Gmc\MonitorModel($request);
        $dataList = $Model->addMonitorPositionOneApi($request);

        $field = array();
        $field['position_name'] = '位置名称';
        $field['position_desc'] = '描述';
        $field['position_id'] = '新增的位置ID';

        $result = array();
        $result['field'] = $field;
        $result['data'] = $Model->result;

        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $result));
    }

    //999018->监控->（集团）监控位置详情 --97
    function getMonitorPositionOneApi()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        //model
        $Model = new \Model\Gmc\MonitorModel($request);
        $dataList = $Model->getMonitorPositionOneApi($request);

        $field = array();
        $field['position_id'] = '监控位置ID';
        $field['company_id'] = '集团ID';
        $field['position_name'] = '位置名称';
        $field['position_desc'] = '描述';

        $result = array();
        $result['field'] = $field;
        $result['data'] = $Model->result;

        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $result));
    }

    //999018->监控->（集团）监控位置编辑
    function editMonitorPositionOneApi()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        //model
        $Model = new \Model\Gmc\MonitorModel($request);
        $dataList = $Model->editMonitorPositionOneApi($request);

        $field = array();
        $field['position_name'] = '位置名称';
        $field['position_desc'] = '描述';

        $result = array();
        $result['field'] = $field;
        $result['data'] = $Model->result;

        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $result));
    }

    //999018->监控->（集团）监控位置删除 -- 97
    function delMonitorPositionOneAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        //model
        $Model = new \Model\Gmc\MonitorModel($request);
        $dataList = $Model->delMonitorPositionOneAction($request);

        $field = array();

        $result = array();
        $result['field'] = $field;
        $result['data'] = $Model->result;

        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $result));
    }

    //999019->监控->（集团）监控位置角色权限设置 -- 97
    function editPositionUserfuncAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        //model
        $Model = new \Model\Gmc\MonitorModel($request);
        $dataList = $Model->editPositionUserfuncAction($request);

        $field = array();

        $result = array();
        $result['field'] = $field;
        $result['data'] = $Model->result;

        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $result));
    }

    //999019->监控->校园角色（以及位置对应的权限） -- 97
    function getPostpartUserfuncApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        //model
        $Model = new \Model\Gmc\MonitorModel($request);
        $dataList = $Model->getPostpartUserfuncApi($request);

        $key = 0;
        $field = array();
        $field[$key]["fieldname"] = "postpart_id";
        $field[$key]["fieldstring"] = "校园角色ID";
        $field[$key]["show"] = 0;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "postpart_name";
        $field[$key]["fieldstring"] = "校园角色名称";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "userfunc_videotype";
        $field[$key]["fieldstring"] = "观看权限";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "ischoice";
        $field[$key]["fieldstring"] = "是否已设置  0 未  1 已";
        $field[$key]["show"] = 0;
        $field[$key]["custom"] = 0;
        $key++;


        $result = array();
        $result['field'] = $field;
        $result['data'] = $Model->result;

        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $result));
    }

    //999019->监控->位置校园角色权限 -- 97
    function getPositionPostpartUserfuncApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        //model
        $Model = new \Model\Gmc\MonitorModel($request);
        $dataList = $Model->getPositionPostpartUserfuncApi($request);

        $key = 0;
        $field = array();
        $field[$key]["fieldname"] = "postpart_id";
        $field[$key]["fieldstring"] = "校园角色ID";
        $field[$key]["show"] = 0;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "postpart_name";
        $field[$key]["fieldstring"] = "校园角色名称";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "position_desc";
        $field[$key]["fieldstring"] = "位置的描述";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "userfunc_videotype";
        $field[$key]["fieldstring"] = "观看权限";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;


        $result = array();
        $result['field'] = $field;
        $result['data'] = $Model->result;

        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $result));
    }

    //999020>监控->（集团）学校列表
    function getMonitorSchoolListView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        //model
        $Model = new \Model\Gmc\MonitorModel($request);
        $dataList = $Model->getMonitorSchoolList($request);

        $key = 0;
        $field = array();
        $field[$key]["fieldname"] = "school_id";
        $field[$key]["fieldstring"] = "学校ID";
        $field[$key]["show"] = 0;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "school_shortname";
        $field[$key]["fieldstring"] = "校区名称";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "school_branch";
        $field[$key]["fieldstring"] = "校区编号";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "school_monitorcode";
        $field[$key]["fieldstring"] = "监控设备码";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $result = array();
        $result['field'] = $field;
        $result['data'] = $Model->result;

        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $result));
    }

    //999021->监控->（集团）添加设备码 --97
    function addMonitorCodeAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        //model
        $Model = new \Model\Gmc\MonitorModel($request);
        $dataList = $Model->addMonitorCodeAction($request);

        $field = array();
        $field['school_shortname'] = '校区名称';
        $field['school_monitorcode'] = '设备码';

        $result = array();
        $result['field'] = $field;
        $result['data'] = $Model->result;

        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $result));
    }

    //999022->监控->（集团）学校设备码详情 --97
    function getMonitorCodeOneApi()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        //model
        $Model = new \Model\Gmc\MonitorModel($request);
        $dataList = $Model->getMonitorCodeOneApi($request);

        $field = array();
        $field['school_id'] = '学校ID';
        $field['school_shortname'] = '校区名称';
        $field['school_monitorcode'] = '设备码';
        $field['monitorcodelist'] = '设备码数组';
        $field['issynchro'] = '是否已经同步 0未 1已经';

        $result = array();
        $result['field'] = $field;
        $result['data'] = $Model->result;

        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $result));
    }

    //999023->监控->（集团）学校设备码编辑 -- 97
    function editMonitorCodeOneAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        //model
        $Model = new \Model\Gmc\MonitorModel($request);
        $dataList = $Model->editMonitorCodeOneAction($request);

        $field = array();
        $field['school_id'] = '学校ID';
        $field['school_shortname'] = '校区名称';
        $field['school_monitorcode'] = '设备码';

        $result = array();
        $result['field'] = $field;
        $result['data'] = $Model->result;

        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $result));
    }

    //999024->监控->（集团）学校设备码删除 -- 97
    function delMonitorCodeOneAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        //model
        $Model = new \Model\Gmc\MonitorModel($request);
        $dataList = $Model->delMonitorCodeOneAction($request);

        $field = array();

        $result = array();
        $result['field'] = $field;
        $result['data'] = $Model->result;

        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $result));
    }


    //结尾魔术函数
    function __destruct()
    {

    }
}
