<?php


namespace Work\Controller\Gmcapi;


class PackageController extends viewTpl
{
    public $u;
    public $t;
    public $c;

    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
    }

    // 获取课件包
    function getPackageListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $PackageModel = new \Model\Gmc\PackageModel($request);
        $res = $PackageModel->getPackageList($request);
        $field = array();
        $k = 0;
        $field[$k]["fieldname"] = "package_name";
        $field[$k]["fieldstring"] = "课件包名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "class_type_name";
        $field[$k]["fieldstring"] = "班级类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "lesson_num";
        $field[$k]["fieldstring"] = "课次数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "course_num";
        $field[$k]["fieldstring"] = "适配课程别数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "forward_lesson_num";
        $field[$k]["fieldstring"] = "允许往前观看课次数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "backward_lesson_num";
        $field[$k]["fieldstring"] = "允许往后观看课次数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["list"] = $res['list'];
            $result["fieldcustom"] = 0;
            $res = array('error' => 0, 'errortip' => '获取课件包列表', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => '暂无课件包列表', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    // 新增课件包
    function addPackageAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $PackageModel = new \Model\Gmc\PackageModel($request);
        $result = $PackageModel->addPackageAction($request);
        $res = array('error' => $PackageModel->error, 'errortip' => $PackageModel->errortip, 'result' => $result);
        ajax_return($res, $request['language_type']);
    }

    // 编辑课件包
    function editPackageAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $PackageModel = new \Model\Gmc\PackageModel($request);
        $PackageModel->editPackageAction($request);
        $res = array('error' => $PackageModel->error, 'errortip' => $PackageModel->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //  课件包适配班别
    function batchCourseAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $PackageModel = new \Model\Gmc\PackageModel($request);
        $PackageModel->batchCourseAction($request);
        $res = array('error' => $PackageModel->error, 'errortip' => $PackageModel->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    /**
     * 获取课件包的适配的课程
     * author: ling
     * 对应接口文档 0001
     */
    function getApplyPackageCourseListApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $PackageModel = new \Model\Gmc\PackageModel($request);
        $res = $PackageModel->getApplyPackageCourseList($request);
        $field = array();
        $k = 0;
        $field[$k]["fieldname"] = "course_cnname";
        $field[$k]["fieldstring"] = "课程别名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "course_branch";
        $field[$k]["fieldstring"] = "课程别编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["list"] = $res['list'];
            $result["fieldcustom"] = 0;
            $res = array('error' => 0, 'errortip' => '获取课件包适配的课程', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => '暂无课件包适配的课程', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    // 获取与课件包课次的对应的课程别
    function getPackageCourseListApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $PackageModel = new \Model\Gmc\PackageModel($request);
        $res = $PackageModel->getPackageCourseList($request);
        $field = array();
        $k = 0;
        $field[$k]["fieldname"] = "course_cnname";
        $field[$k]["fieldstring"] = "课程别名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "course_branch";
        $field[$k]["fieldstring"] = "课程别编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["list"] = $res['list'];
            $result["fieldcustom"] = 0;
            $res = array('error' => 0, 'errortip' => '获取课件包可适配的课程', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => '暂无课件包可适配的课程', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    //移除课程别
    function delPackageCourseAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $PackageModel = new \Model\Gmc\PackageModel($request);
        $PackageModel->delPackageCourseAction($request);
        $res = array('error' => $PackageModel->error, 'errortip' => $PackageModel->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    // 课件包课次管理
    function getPackageHourListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $PackageModel = new \Model\Gmc\PackageModel($request);
        $res = $PackageModel->getPackageHourList($request);
        $field = array();
        $k = 0;
        $field[$k]["fieldname"] = "lesson_name";
        $field[$k]["fieldstring"] = "课次名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "word_num";
        $field[$k]["fieldstring"] = "课件数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "lesson_sort";
        $field[$k]["fieldstring"] = "课次排序";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "is_synchro_name";
        $field[$k]["fieldstring"] = "是否同步网课";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        $result["data"] = $res['data'];
        if ($res) {
            $result["list"] = $res['list'];
            $result["fieldcustom"] = 0;
            $res = array('error' => 0, 'errortip' => '获取课件包课次列表', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => '暂无课件包课次', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    /**
     * 新增课件包的单个课次
     * author: ling
     * 对应接口文档 0001
     */
    function addPackageLessonAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $PackageModel = new \Model\Gmc\PackageModel($request);
        $PackageModel->addPackageLessonAction($request);
        $res = array('error' => $PackageModel->error, 'errortip' => $PackageModel->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    /**
     * 删除课次
     * author: ling
     * 对应接口文档 0001
     */
    function delLessonTimeAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $PackageModel = new \Model\Gmc\PackageModel($request);
        $PackageModel->delLessonTimeAction($request);
        $res = array('error' => $PackageModel->error, 'errortip' => $PackageModel->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    /**
     * 编辑
     * author: ling
     * 对应接口文档 0001
     */
    function editLessontimeAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $PackageModel = new \Model\Gmc\PackageModel($request);
        $PackageModel->editLessontimeAction($request);
        $res = array('error' => $PackageModel->error, 'errortip' => $PackageModel->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //获取课件包课次的课件
    function getLesstimesWordsView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $PackageModel = new \Model\Gmc\PackageModel($request);
        $res = $PackageModel->getLesstimesWords($request);
        $field = array();
        $k = 0;
        $field[$k]["fieldname"] = "word_name";
        $field[$k]["fieldstring"] = "文件名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldname"] = "word_size";
        $field[$k]["fieldstring"] = "文件大小";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldname"] = "word_status";
        $field[$k]["fieldstring"] = "文件状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldname"] = "word_createtime";
        $field[$k]["fieldstring"] = "上传时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["list"] = $res['list'];
            $result["fieldcustom"] = 0;
            $res = array('error' => 0, 'errortip' => '获取课件包课次课件列表', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => '暂无课件包课次课件', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    /**
     * 删除课件
     * author: ling
     * 对应接口文档 0001
     */
    function delWordAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $PackageModel = new \Model\Gmc\PackageModel($request);
        $PackageModel->delWordAction($request);
        $res = array('error' => $PackageModel->error, 'errortip' => $PackageModel->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    /**
     * 上传课件
     * author: ling
     * 对应接口文档 0001
     */
    function loadWordAction()
    {

        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $PackageModel = new \Model\Gmc\PackageModel($request);
        $id = $PackageModel->loadWordAction($request);
        $result = array();
        $result['word_id'] = $id;
        $res = array('error' => $PackageModel->error, 'errortip' => $PackageModel->errortip, 'result' => $result);
        ajax_return($res, $request['language_type']);
    }

    /**
     * 上传到拓课云
     * author: ling
     * 对应接口文档 0001
     */
    function uploadFieldAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        // 这个不需要加验证
        $TalkcloudModel = new \Model\Tlk\TalkcloudModel($request['company_id']);
        $putArray = array();
        $putArray['fromclass'] = 0;
        $putArray['filedata'] = $_FILES['ossfile'];
        $putArray['dynamicppt'] = 1;
        $modelArray = $TalkcloudModel->exuploadFile($putArray);

        if ($modelArray['result'] == '0') {
            $worddata = array();
            $worddata['word_createtime'] = time();
            $worddata['line_fileid'] = $modelArray['fileid'];
            $this->DataControl->updateData("eas_coursepackage_lessonword", "word_id='{$request['word_id']}'", $worddata);
            $result = array();
            $result['word_id'] = $request['word_id'];
            $result['fileid'] = $modelArray['fileid'];
            $res = array('error' => 0, 'errortip' => '上传至拓课云成功', 'result' => $result);
            ajax_return($res, $request['language_type']);
        } else {
            $res = array('error' => 1, 'errortip' => '上传至拓课云失败', 'result' => array());
            ajax_return($res, $request['language_type']);
        }
    }

    /**
     * 删除课件包
     * author: ling
     * 对应接口文档 0001
     */
    function delPackageOneAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $PackageModel = new \Model\Gmc\PackageModel($request);
        $PackageModel->delPackageOneAction($request);
        $res = array('error' => $PackageModel->error, 'errortip' => $PackageModel->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    /**
     * 下载课件
     * author: ling
     * 对应接口文档 0001
     * Date 2020/12/23 0023
     */
    function DownloadView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $dataOne = $this->DataControl->getFieldOne("eas_coursepackage_lessonword", "word_url,word_name", "word_id='{$request['word_id']}'");
        $full_path = $dataOne['word_url'];
        $kuozhan = strrchr($full_path, '.');
        $full_name = $dataOne['word_name'];
        header("Content-Type: video/{$kuozhan}");
        header("Content-Disposition: attachment;filename={$full_name}");
        readfile($full_path);
    }


    function getListApi()
    {
        $request['company_id'] = '8888';
        $PackageModel = new \Model\Gmc\PackageModel($request);
        $data = $PackageModel->getTkl_lineroomsList();
    }

    function getLineListView()
    {
        $request['company_id'] = '8888';
        $PackageModel = new \Model\Gmc\PackageModel($request);
        $data = $PackageModel->getLineList();
    }


}