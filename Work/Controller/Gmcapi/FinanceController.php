<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/13
 * Time: 23:25
 */

namespace Work\Controller\Gmcapi;


class FinanceController extends viewTpl
{
    public $u;
    public $t;
    public $c;
    public $visitType = "api";
    public $Model;

    //预加载处理类
    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
    }

    function ThisVerify($request)
    {
        $paramArray = array();
        $paramArray['staffer_id'] = $request['staffer_id'];
        $paramArray['company_id'] = $request['company_id'];
        $paramArray['token'] = $request['token'];
        if (!$this->UserLimit($paramArray)) {
            $result = array();
            $result["list"] = array();
            $result["tokeninc"] = "0";
            $res = array('error' => '1', 'errortip' => "用户token失效", 'result' => $result);
            ajax_return($res, $request['language_type']);
        }
    }


    //会计科目列表
    function HomeView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FinanceModel($request);

        $result = $this->Model->getAcctList($request);
        ajax_return($result, $request['language_type']);
    }

    //添加会计科目
    function addAcctAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FinanceModel($request);

        $result = $this->Model->addAcctAction($request);
        ajax_return($result, $request['language_type']);
    }

    //编辑会计科目
    function updateAcctAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FinanceModel($request);

        $result = $this->Model->updateAcctAction($request);
        ajax_return($result, $request['language_type']);
    }

    //删除会计科目
    function delAcctAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FinanceModel($request);

        $result = $this->Model->delAcctAction($request);
        ajax_return($result, $request['language_type']);
    }

    //会计科目分组列表
    function acctgroupView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FinanceModel($request);

        $result = $this->Model->getAcctgroupList($request);
        ajax_return($result, $request['language_type']);
    }

    //添加会计科目分组
    function addAcctgroupAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FinanceModel($request);

        $result = $this->Model->addAcctgroupAction($request);
        ajax_return($result, $request['language_type']);
    }

    //编辑会计科目分组
    function updateAcctgroupAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FinanceModel($request);

        $result = $this->Model->updateAcctgroupAction($request);
        ajax_return($result, $request['language_type']);
    }

    //删除计科目分组
    function delAcctgroupAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FinanceModel($request);

        $result = $this->Model->delAcctgroupAction($request);
        ajax_return($result, $request['language_type']);
    }

    //会计科目类型列表
    function accttypeView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FinanceModel($request);

        $result = $this->Model->getAccttypeList($request);
        ajax_return($result, $request['language_type']);
    }

    //添加会计科目类型
    function addActtypeAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FinanceModel($request);

        $result = $this->Model->addActtypeAction($request);
        ajax_return($result, $request['language_type']);
    }

    //编辑会计科目类型
    function updateActtypeAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FinanceModel($request);

        $result = $this->Model->updateActtypeAction($request);
        ajax_return($result, $request['language_type']);
    }

    //删除计科目类型
    function delActtypeAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FinanceModel($request);

        $result = $this->Model->delActtypeAction($request);
        ajax_return($result, $request['language_type']);
    }

    //银行列表
    function bankView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FinanceModel($request);

        $result = $this->Model->getBankList($request);
        ajax_return($result, $request['language_type']);
    }

    //添加银行
    function addBankAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FinanceModel($request);

        $result = $this->Model->addBankAction($request);
        ajax_return($result, $request['language_type']);
    }

    //编辑银行
    function updateBankAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FinanceModel($request);

        $result = $this->Model->updateBankAction($request);
        ajax_return($result, $request['language_type']);
    }

    //删除银行
    function delBankAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FinanceModel($request);

        $result = $this->Model->delBankAction($request);
        ajax_return($result, $request['language_type']);
    }

    //收费类型列表
    function feeView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FinanceModel($request);

        $result = $this->Model->getFeeList($request);
        ajax_return($result, $request['language_type']);
    }

    //添加收费类型
    function addFeeAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FinanceModel($request);

        $result = $this->Model->addFeeAction($request);
        ajax_return($result, $request['language_type']);
    }

    //编辑收费类型
    function updateFeeAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FinanceModel($request);

        $result = $this->Model->updateFeeAction($request);
        ajax_return($result, $request['language_type']);
    }

    //删除收费类型
    function delFeeAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FinanceModel($request);

        $result = $this->Model->delFeeAction($request);
        ajax_return($result, $request['language_type']);
    }

    //收费支付方式列表
    function paytypeView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FinanceModel($request);

        $result = $this->Model->getPaytypeList($request);
        ajax_return($result, $request['language_type']);
    }

    //添加支付方式类型
    function addPaytypeAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FinanceModel($request);

        $result = $this->Model->addPaytypeAction($request);
        ajax_return($result, $request['language_type']);
    }

    //编辑支付方式类型
    function updatePaytypeAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FinanceModel($request);

        $result = $this->Model->updatePaytypeAction($request);
        ajax_return($result, $request['language_type']);
    }

    //删除收费类型
    function delPaytypeAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FinanceModel($request);

        $result = $this->Model->delPaytypeAction($request);
        ajax_return($result, $request['language_type']);
    }

    //零用金列表
    function pettycashView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FinanceModel($request);

        $result = $this->Model->getPettycashList($request);
        ajax_return($result, $request['language_type']);
    }

    //新增零用金
    function addPettycashAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FinanceModel($request);

        $result = $this->Model->addPettycashAction($request);
        ajax_return($result, $request['language_type']);
    }

    //编辑零用金
    function updatePettycashAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FinanceModel($request);

        $result = $this->Model->updatePettycashAction($request);
        ajax_return($result, $request['language_type']);
    }

    //删除零用金
    function delPettycashAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FinanceModel($request);

        $result = $this->Model->delPettycashAction($request);
        ajax_return($result, $request['language_type']);
    }

    //支出类型列表
    function spendingView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FinanceModel($request);

        $result = $this->Model->getSpendingList($request);
        ajax_return($result, $request['language_type']);
    }

    //新增支出类型列表
    function addSpendingAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FinanceModel($request);

        $result = $this->Model->addSpendingAction($request);
        ajax_return($result, $request['language_type']);
    }

    //编辑支出类型
    function updateSpendingAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FinanceModel($request);

        $result = $this->Model->updateSpendingAction($request);
        ajax_return($result, $request['language_type']);
    }

    //删除支出类型
    function delSpendingAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FinanceModel($request);

        $result = $this->Model->delSpendingAction($request);
        ajax_return($result, $request['language_type']);
    }

    //优惠券申请类型列表
    function couponsapplytypeView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FinanceModel($request);

        $result = $this->Model->couponsapplytype($request);
        ajax_return($result, $request['language_type']);
    }

    //新增优惠券申请类型
    function addcouponsapplytypeAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FinanceModel($request);

        $result = $this->Model->addcouponsapplytypeAction($request);
        ajax_return($result, $request['language_type']);
    }

    //是否启用优惠券类型
    function updatecouponsapplytypeAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FinanceModel($request);

        $result = $this->Model->updatecouponsapplytypeAction($request);
        ajax_return($result, $request['language_type']);
    }

    //编辑优惠券申请类型
    function updateStatusAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FinanceModel($request);

        $result = $this->Model->updateStatusAction($request);
        ajax_return($result, $request['language_type']);
    }

    //删除优惠券申请类型
    function delcouponsapplytypeAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FinanceModel($request);

        $result = $this->Model->delcouponsapplytypeAction($request);
        ajax_return($result, $request['language_type']);
    }

    function couponsapplytypeListApi()
    {

        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Gmc\FinanceModel($request);
        $res = $Model->couponsapplytypeList($request);
        $field = array();

        $k = 0;
        $field[$k]["fieldstring"] = "applytype_id";
        $field[$k]["fieldname"] = "领取优惠券ID";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "applytype_cnname";
        $field[$k]["fieldname"] = "优惠券类型名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "applytype_branch";
        $field[$k]["fieldname"] = "优惠券类型编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "applytype_playclass_name";
        $field[$k]["fieldname"] = "优惠券使用类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "effecttime";
        $field[$k]["fieldname"] = "优惠券生效期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;


        $field[$k]["fieldstring"] = "applytype_coexist_name";
        $field[$k]["fieldname"] = "是否和其他优惠券共用";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "applytype_type_name";
        $field[$k]["fieldname"] = "使用方式";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "applytype_price";
        $field[$k]["fieldname"] = "优惠金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "applytype_discount";
        $field[$k]["fieldname"] = "优惠折扣";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "applytype_getscope_name";
        $field[$k]["fieldname"] = "适用学员";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "applytype_applyschool_name";
        $field[$k]["fieldname"] = "适用学校";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "applytype_applycourse_name";
        $field[$k]["fieldname"] = "适用班种";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        
        $field[$k]["fieldstring"] = "applytype_course_name";
        $field[$k]["fieldname"] = "适用课程";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursepacksNum_name";
        $field[$k]["fieldname"] = "适用组合课程";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "applytype_isgetdouble_name";
        $field[$k]["fieldname"] = "是否允许重复使用";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "is_manycourse";
        $field[$k]["fieldname"] = "组合课程是否适用";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "is_limitPrice";
        $field[$k]["fieldname"] = "是否限制最低消费金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "applytype_minprice";
        $field[$k]["fieldname"] = "最低消费金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "applytype_caneditcourse_name";
        $field[$k]["fieldname"] = "是否允许校区修改适用课程";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "applytype_eftime";
        $field[$k]["fieldname"] = "领取后有效期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "applytype_play";
        $field[$k]["fieldname"] = "使用规则";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "applytype_get";
        $field[$k]["fieldname"] = "领取规则";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "applytype_remark";
        $field[$k]["fieldname"] = "备注";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "applytype_buildtype_name";
        $field[$k]["fieldname"] = "创建类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;


        $result = array();
        $result["field"] = $field;
        if ($res) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);

        }
        ajax_return($res, $request['language_type']);
    }

    function addApplySchoolAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Gmc\FinanceModel($request);
        $res = $Model->addApplySchool($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => $Model->oktip, 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function delApplySchoolAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Gmc\FinanceModel($request);
        $res = $Model->delApplySchool($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => $Model->oktip, 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function allSchoolListApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Gmc\FinanceModel($request);
        $res = $Model->allSchoolList($request);
        $field = array();

        $k = 0;
        $field[$k]["fieldstring"] = "school_id";
        $field[$k]["fieldname"] = "学校ID";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校园名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_enname";
        $field[$k]["fieldname"] = "检索代码";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "district_cnname";
        $field[$k]["fieldname"] = "区域";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;
        if ($res) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);

        }
        ajax_return($res, $request['language_type']);
    }

    function applytypeSchoolListApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Gmc\FinanceModel($request);
        $res = $Model->applytypeSchoolList($request);
        $field = array();

        $k = 0;
        $field[$k]["fieldstring"] = "school_id";
        $field[$k]["fieldname"] = "学校ID";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校园名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_enname";
        $field[$k]["fieldname"] = "检索代码";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "district_cnname";
        $field[$k]["fieldname"] = "区域";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;
        if ($res) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $res['allnum'] = 0;
            $result["list"] = $res;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);

        }
        ajax_return($res, $request['language_type']);
    }

    function applytypeCoursepacksListApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Gmc\FinanceModel($request);
        $res = $Model->applytypeCoursepacksList($request);
        $field = array();

        $k = 0;
        $field[$k]["fieldstring"] = "coursepacks_id";
        $field[$k]["fieldname"] = "组合课程ID";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "coursepacks_name";
        $field[$k]["fieldname"] = "组合课程名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "effectetime";
        $field[$k]["fieldname"] = "有效期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "courseNum";
        $field[$k]["fieldname"] = "课程数量";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "sellingprice";
        $field[$k]["fieldname"] = "组合价格";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;
        if ($res) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $res['allnum'] = 0;
            $result["list"] = $res;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);

        }
        ajax_return($res, $request['language_type']);
    }


    function addApplyCoursepacksAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Gmc\FinanceModel($request);
        $res = $Model->addApplyCoursepacks($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => $Model->oktip, 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function delApplyCoursepacksAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Gmc\FinanceModel($request);
        $res = $Model->delApplyCoursepacks($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => $Model->oktip, 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function applytypeCourseListApi()
    {

        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Gmc\FinanceModel($request);
        $res = $Model->applytypeCourseList($request);
        $field = array();

        $k = 0;
        $field[$k]["fieldstring"] = "coursecat_id";
        $field[$k]["fieldname"] = "班种ID";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "coursecat_cnname";
        $field[$k]["fieldname"] = "班种名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursecat_branch";
        $field[$k]["fieldname"] = "班种编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursetype_cnname";
        $field[$k]["fieldname"] = "所属班组";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursecat_cnname";
        $field[$k]["fieldname"] = "所属班种";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;
        if ($res) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);

        }
        ajax_return($res, $request['language_type']);
    }

    function getCoursepacksListApi()
    {

        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Gmc\FinanceModel($request);
        $res = $Model->getCoursepacksList($request);
        $field = array();

        $k = 0;
        $field[$k]["fieldstring"] = "coursepacks_id";
        $field[$k]["fieldname"] = "组合课程ID";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "coursepacks_name";
        $field[$k]["fieldname"] = "组合课程名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursepacks_startday";
        $field[$k]["fieldname"] = "起效时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursepacks_endday";
        $field[$k]["fieldname"] = "结束时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;
        if ($res) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);

        }
        ajax_return($res, $request['language_type']);
    }

    //添加适配渠道
    function addApplyChannelAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Gmc\FinanceModel($request);
        $res = $Model->addApplyChannel($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => $Model->oktip, 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }
    //删除渠道
    function delApplychannelAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Gmc\FinanceModel($request);
        $res = $Model->delApplychannel($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => $Model->oktip, 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }
    //获取全部渠道
    function allchannelListApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
//        $this->ThisVerify($request);
        $Model = new \Model\Gmc\FinanceModel($request);
        $res = $Model->allchannelList($request);
        $field = array();

        $k = 0;
        $field[$k]["fieldstring"] = "channel_id";
        $field[$k]["fieldname"] = "渠道ID";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "channel_medianame";
        $field[$k]["fieldname"] = "渠道类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "channel_name";
        $field[$k]["fieldname"] = "渠道明细";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;
        if ($res) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);

        }
        ajax_return($res, $request['language_type']);
    }
    //获取适配的渠道数据
    function applytypeChannelListApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Gmc\FinanceModel($request);
        $res = $Model->applytypeChannelList($request);
        $field = array();

        $k = 0;
        $field[$k]["fieldstring"] = "channel_id";
        $field[$k]["fieldname"] = "渠道ID";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "channel_medianame";
        $field[$k]["fieldname"] = "渠道类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "channel_name";
        $field[$k]["fieldname"] = "渠道明细";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;
        if ($res) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $res['allnum'] = 0;
            $result["list"] = $res;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);

        }
        ajax_return($res, $request['language_type']);
    }

    //新增及编辑领取优惠券
    function editapplytypeAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Gmc\FinanceModel($request);
        $res = $Model->editapplytype($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => $Model->oktip, 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //领取优惠券规则 -- 复制功能
    function copyApplytypeAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Gmc\FinanceModel($request);
        $res = $Model->copyApplytype($request);

        if ($res) {
            $res = array('error' => 0, 'errortip' => $Model->errortip, 'result' => array());
        } else {
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => array());
        }
        ajax_return($res, $request['language_type']);
    }

    function delapplytypeAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Gmc\FinanceModel($request);
        $res = $Model->delapplytype($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => $Model->oktip, 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //商品优惠券申请规则列表
    function couponsrulesView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FinanceModel($request);

        $result = $this->Model->couponsrules($request);
        ajax_return($result, $request['language_type']);
    }

    //新增商品优惠券申请规则
    function addouponsrulesAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FinanceModel($request);

        $result = $this->Model->addouponsrulesAction($request);
        ajax_return($result, $request['language_type']);
    }

    //编辑商品优惠券申请规则
    function updatecouponsrulesAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FinanceModel($request);

        $result = $this->Model->updatecouponsrulesAction($request);
        ajax_return($result, $request['language_type']);
    }

    //删除商品优惠券申请规则
    function delcouponsrulesAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FinanceModel($request);

        $result = $this->Model->delcouponsrulesAction($request);
        ajax_return($result, $request['language_type']);
    }

    //规则下拉
    function getCouponsRuleView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);

        $result['rule'] = $this->DataControl->selectClear("select couponsrules_id,couponsrules_name from shop_code_couponsrules where company_id = '{$request['company_id']}' and couponsrules_isshop = '0'");

        ajax_return(array('error' => '0', 'errortip' => "获取成功", 'result' => $result));
    }

    //定金类型列表
    function getDepositListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
//        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FinanceModel($request);

        $result = $this->Model->getDepositList($request);
        ajax_return($result, $request['language_type']);
    }

    //新增定金类型
    function addDepositAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FinanceModel($request);

        $result = $this->Model->addDepositAction($request);
        ajax_return($result, $request['language_type']);
    }

    //编辑定金类型信息
    function updDepositAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FinanceModel($request);

        $result = $this->Model->updateDepositAction($request);
        ajax_return($result, $request['language_type']);
    }

    //是否启用定金类型
    function updDepositStatusAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FinanceModel($request);

        $result = $this->Model->updateDepositStatusAction($request);
        ajax_return($result, $request['language_type']);
    }

    //删除定金类型
    function delDepositAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FinanceModel($request);

        $result = $this->Model->deleteDepositAction($request);
        ajax_return($result, $request['language_type']);
    }

    //获取定金匹配学校
    function getDepositSchoolListApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Gmc\FinanceModel($request);
        $res = $Model->getDepositSchoolList($request);
        $field = array();

        $k = 0;
        $field[$k]["fieldstring"] = "school_id";
        $field[$k]["fieldname"] = "学校ID";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校园名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_enname";
        $field[$k]["fieldname"] = "检索代码";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "district_cnname";
        $field[$k]["fieldname"] = "区域";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;
        if ($res) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $res['allnum'] = 0;
            $result["list"] = $res;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);

        }
        ajax_return($res, $request['language_type']);
    }

    //添加定金匹配学校
    function addDepositSchoolAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Gmc\FinanceModel($request);
        $res = $Model->addDepositSchool($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => $Model->oktip, 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //删除定金匹配学校
    function delDepositSchoolAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Gmc\FinanceModel($request);
        $res = $Model->deleteDepositSchool($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => $Model->oktip, 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //获取班组
    function getActiveCoursetypeListApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Gmc\FinanceModel($request);
        $res = $Model->getActiveCoursetypeList($request);
        $field = array();

        $k = 0;
        $field[$k]["fieldstring"] = "company_id";
        $field[$k]["fieldname"] = "集团ID";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "coursetype_id";
        $field[$k]["fieldname"] = "班组id";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursetype_branch";
        $field[$k]["fieldname"] = "班组编码";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursetype_cnname";
        $field[$k]["fieldname"] = "班组名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;
        if ($res) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $res['allnum'] = 0;
            $result["list"] = $res;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);

        }
        ajax_return($res, $request['language_type']);
    }

    //获取可以匹配的学校
    function getSchoolListApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Gmc\FinanceModel($request);
        $res = $Model->getSchoolList($request);

        $k=0;
        $field = array();
        $field[$k]["fieldstring"] = "school_id";
        $field[$k]["fieldname"] = "学校ID";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校园名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "school_enname";
        $field[$k]["fieldname"] = "检索代码";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "district_cnname";
        $field[$k]["fieldname"] = "所属区域";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $result = array();
        $result["field"] = $field;
        if ($res) {
            $result["list"] = $res['schoolList'];
            $result["district"] = $res['district'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["district"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }


    function applyCourseListApi()
    {

        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Gmc\FinanceModel($request);
        $res = $Model->applyCourseList($request);
        $field = array();

        $k = 0;
        $field[$k]["fieldstring"] = "course_id";
        $field[$k]["fieldname"] = "课程别ID";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] ="course_cnname";
        $field[$k]["fieldname"] = "课程名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "course_branch";
        $field[$k]["fieldname"] = "课程编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursecat_cnname";
        $field[$k]["fieldname"] = "班种";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursetype_cnname";
        $field[$k]["fieldname"] = "班组";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;
        if ($res['allnum']) {
            $result['allnum'] = $res['allnum'];
        } else {
            $result['allnum'] = 0;
        }
        if ($res) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);

        }
        ajax_return($res, $request['language_type']);
    }

    function applyAllCourseListApi()
    {

        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Gmc\FinanceModel($request);
        $res = $Model->applyAllCourseList($request);
        $field = array();

        $k = 0;
        $field[$k]["fieldstring"] = "course_id";
        $field[$k]["fieldname"] = "课程别ID";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] ="course_cnname";
        $field[$k]["fieldname"] = "课程名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "course_branch";
        $field[$k]["fieldname"] = "课程编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursetype_cnname";
        $field[$k]["fieldname"] = "所属班组";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursecat_cnname";
        $field[$k]["fieldname"] = "所属班种";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;
        if ($res['allnum']) {
            $result['allnum'] = $res['allnum'];
        } else {
            $result['allnum'] = 0;
        }
        if ($res) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);

        }
        ajax_return($res, $request['language_type']);
    }

    function addApplyCourseAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Gmc\FinanceModel($request);
        $res = $Model->addApplyCourse($request);
        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '新增成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function removeApplyCourseAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Gmc\FinanceModel($request);
        $res = $Model->removeApplyCourse($request);
        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '移除成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }



    //结尾魔术函数
    function __destruct()
    {

    }
}
