<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/13
 * Time: 23:25
 */

namespace Work\Controller\Gmcapi;


class TradeController extends viewTpl
{
    public $u;
    public $t;
    public $c;
    public $visitType = "api";
    public $Model;

    //预加载处理类
    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
    }

    function ThisVerify($request)
    {
        $paramArray = array();
        $paramArray['staffer_id'] = $request['staffer_id'];
        $paramArray['company_id'] = $request['company_id'];
        $paramArray['token'] = $request['token'];
        if(!$this->UserLimit($paramArray)){
            $result = array();
            $result["list"] = array();
            $result["tokeninc"] = "0";
            $res = array('error' => '1', 'errortip' => "用户token失效", 'result' => $result);
            ajax_return($res,$request['language_type']);
        }
    }


    //校园资金交易记录
    function HomeView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\TradeModel($request);

        $result = $this->Model->getSchoolTradeList($request);
        ajax_return($result,$request['language_type']);
    }

    function getCompaniesListApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Gmc\TradeModel($request);
        $res = $Model->getCompaniesList($request);
        if ($res) {
            $res = array('error' => 0, 'errortip' => '获取成功!', 'result' => $res);
        } else {
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => array());
        }
        ajax_return($res, $request['language_type']);

    }

    //主体交易明细管理
    function getMainTradeListView()
    {
        error_reporting(E_ALL);
        ini_set('display_errors', '1');
        ini_set("memory_limit", '-1');
        set_time_limit(600);
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\TradeModel($request);

        $result = $this->Model->getMainTradeList($request);
        ajax_return($result,$request['language_type']);
    }

    function getSchoolTradeOneApi(){

        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Gmc\TradeModel($request);
        $res = $Model->getSchoolTradeOne($request);
        $result = array();
        if($res){
            $result["list"] = $res;
            $result = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
        } else {
            $result = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($result,$request['language_type']);
    }

    function examineSchoolTradeAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Gmc\TradeModel($request);
        $res = $Model->examineSchoolTrade($request);
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '审核成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);

        }
        ajax_return($res,$request['language_type']);
    }


    //校园收入明细
    function getSchoolIncomeView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\TradeModel($request);

        $result = $this->Model->getSchoolIncome($request);
        ajax_return($result,$request['language_type']);
    }


    function getIncomeTypeListApi()
    {
        $request = Input('get.', '', 'trim,addslashes');

        $this->ThisVerify($request);

        $Model = new \Model\Gmc\TradeModel($request);
        $res = $Model->getIncomeTypeList($request);
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '获取信息', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function writeOffIncomeAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);

        $Model = new \Model\Gmc\TradeModel($request);
        $res = $Model->writeOffIncome($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '操作成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }

    //校园支出明细
    function getSchoolExpendView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\TradeModel($request);

        $result = $this->Model->getSchoolExpend($request);
        ajax_return($result,$request['language_type']);
    }

    //校园退费审核列表
    function getRefundOrderListView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\TradeModel($request);

        $result = $this->Model->getRefundOrderList($request);
        ajax_return($result,$request['language_type']);
    }

    //合同列表
    function protocolListView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\TradeModel($request);

        $result = $this->Model->protocolList($request);
        ajax_return($result,$request['language_type']);
    }

    //批量监管
    function BatchSuperviseAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
       $this->ThisVerify($request);
        $TradeModel = new \Model\Gmc\TradeModel($request);

        $pidList = json_decode(stripslashes($request['list']), true);
        if ($pidList) {
            foreach ($pidList as $value) {
                $data = array();
                $data['protocol_issupervise'] = '1';
                $this->DataControl->updateData("smc_student_protocol","protocol_id = '{$value['protocol_id']}'",$data);
                $protocol = $this->DataControl->getFieldOne("smc_student_protocol","school_id,order_pid","protocol_id = '{$value['protocol_id']}'");
                $request['school_id'] = $protocol['school_id'];
                $request['order_pid'] = $protocol['order_pid'];
                $request['protocol_id'] = $value['protocol_id'];
                $info = $TradeModel->createProtocolAction($request);
                $superdata = array();
                $superdata['protocol_id'] = $value['protocol_id'];
                $superdata['supervise_protocol'] = $info;
                $this->DataControl->insertData("smc_student_protocol_supervise",$superdata);
            }
            $res = array('error' => 0, 'errortip' => '成功', 'result' => array());
        } else {
            $res = array('error' => 1, 'errortip' => '请选择合同', 'result' => array());
        }

        ajax_return($res, $request['language_type']);
    }


    //查看
    function protocolDetailView(){
        $request = Input('get.', '', 'trim,addslashes,strip_tags');
        $this->ThisVerify($request);//验证账户
        $img = $this->DataControl->getFieldOne("gmc_company","company_logo","company_id = '{$request['company_id']}'");
        $protocol = $this->DataControl->getOne("smc_student_protocol","protocol_id = '{$request['protocol_id']}'");
        $student = $this->DataControl->getFieldOne("smc_student","student_cnname,student_enname,student_birthday,student_sex,student_branch","student_id = '{$protocol['student_id']}'");
        $restmoney = $this->DataControl->getFieldOne("smc_student_balance","student_balance,student_withholdbalance","student_id = '{$protocol['student_id']}'");
        if($restmoney){
            $a = $restmoney['student_balance'] + $restmoney['student_withholdbalance'];
        }else{
            $a = 0;
        }
        $school = $this->DataControl->getFieldOne("smc_school","school_cnname","school_id = '{$protocol['school_id']}'");
        $famliy = $this->DataControl->getFieldOne("smc_student_family","parenter_id","student_id = '{$protocol['student_id']}' and family_isdefault = '1'");
        $parenter = $this->DataControl->selectOne("
            SELECT
                p.parenter_mobile,
                p.parenter_cnname,
                f.family_relation,
                f.family_address 
            FROM
                smc_parenter AS p
                LEFT JOIN smc_student_family AS f ON p.parenter_id = f.parenter_id
                where p.parenter_id = '{$famliy['parenter_id']}' and f.student_id = '{$protocol['student_id']}'");
        $order = $this->DataControl->selectOne("select o.order_pid,p.pay_pid,p.pay_typename,p.pay_price,o.order_coupon_price,o.order_paidprice,o.order_arrearageprice from smc_payfee_order as o left join smc_payfee_order_pay as p on o.order_pid = p.order_pid where o.order_pid = '{$protocol['order_pid']}'");
        $pricing_id = $this->DataControl->selectOne("select pricing_id from smc_payfee_order_course as c where c.order_pid = '{$protocol['order_pid']}'");
        $standardprice = $this->DataControl->getFieldOne("smc_fee_pricing_tuition","tuition_unitprice","pricing_id = '{$pricing_id['pricing_id']}'");
        $course = $this->DataControl->getFieldOne("smc_course","course_cnname,course_branch,course_classnum,course_classtimes,coursetype_id","course_id = '{$protocol['course_id']}'");
        $agreement = $this->DataControl->getFieldOne("smc_fee_pricing","agreement_id","pricing_id = '{$pricing_id['pricing_id']}'");
        $text = $this->DataControl->getFieldOne("smc_fee_treaty","treaty_protocol","agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}'");

        $result = array();
        $result['protocol'] = $protocol;
        $result['img'] = $img['company_logo'];
        $result['standardprice'] = $standardprice['tuition_unitprice'];
        $result['student'] = $student;
        $result['school'] = $school;
        $result['parenter'] = $parenter;
        $result['order'] = $order;
        $result['course'] = $course;
        $result['times'] = $protocol['protocol_nums']*$course['course_classtimes'];
        $result['time'] = $course['course_classnum']*$course['course_classtimes'];
        $result['text'] = $text['treaty_protocol'];
        $result['restmoney'] = $a;
        $result['date'] =date('Y-m-d',$protocol['protocol_createtime']);
        $result['perprice'] = intval($protocol['protocol_price'] / $protocol['protocol_nums']);
        ajax_return(array('error' => 0,'errortip' => "获取成功",'result' => $result));

    }


    //审核
    function examineAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\TradeModel($request);

        $result = $this->Model->examineAction($request);
        ajax_return($result,$request['language_type']);
    }

    //编辑监管合同
    function updateSuperviseAction()
    {
        $request = Input('post.','','trim,addslashes');
       $this->ThisVerify($request);//验证账户
        $TradeModel = new \Model\Gmc\TradeModel($request);

        $pidList = json_decode(stripslashes($request['list']), true);
        if ($pidList) {
            foreach ($pidList as $value) {
                $request['protocol_id'] = $value['protocol_id'];
                $sid = $this->DataControl->getFieldOne("smc_student_protocol","school_id","protocol_id = '{$value['protocol_id']}'");
                $request['school_id'] = $sid['school_id'];
                $info = $TradeModel->updateSupervise($request);
                $superdata = array();
                $superdata['supervise_protocol'] = $info;
                $this->DataControl->updateData("smc_student_protocol_supervise","protocol_id = '{$value['protocol_id']}'",$superdata);
            }
            $res = array('error' => 0, 'errortip' => '成功', 'result' => array());
        } else {
            $res = array('error' => 1, 'errortip' => '请选择合同', 'result' => array());
        }

        ajax_return($res,$request['language_type']);
    }

    //处理退费
    function refundAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\TradeModel($request);

        $result = $this->Model->refundAction($request);
        ajax_return($result,$request['language_type']);
    }

    //校财务审核信息输入
    function serialAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\TradeModel($request);

        $result = $this->Model->serialAction($request);
        ajax_return($result,$request['language_type']);
    }

    //确认金额
    function SureRefundAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\TradeModel($request);

        $result = $this->Model->SureRefundAction($request);
        ajax_return($result,$request['language_type']);
    }

    //发票管理
    function invoiceListView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\TradeModel($request);

        $result = $this->Model->invoiceList($request);
        ajax_return($result,$request['language_type']);
    }

    //开票公司下拉列表
    function getConpaniesApi()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $sql = "select companies_id,companies_cnname from gmc_code_companies where company_id = '{$request['company_id']}' and (companies_isInvoice = '1' or companies_id = '78372') ";
        $CompaniesList = $this->DataControl->selectClear($sql);
        if (!$CompaniesList) {
            $CompaniesList = array();
        }

        $result["list"] = $CompaniesList;
        $res = array('error' => 0, 'errortip' => '获取开票公司下拉列表', 'result' => $result);

        ajax_return($res,$request['language_type']);
    }

    //发票跟踪记录
    function invoiceTrackApi()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $sql = "select FROM_UNIXTIME( tracks_time, '%Y-%m-%d %H:%i:%s' ) as tracks_time,tracks_title,tracks_information from shop_invoice_tracks where invoice_id = '{$request['invoice_id']}'";
        $CompaniesList = $this->DataControl->selectClear($sql);
        if (!$CompaniesList) {
            $CompaniesList = array();
        }

        $result["list"] = $CompaniesList;
        $res = array('error' => 0, 'errortip' => '获取发票跟踪记录', 'result' => $result);

        ajax_return($res,$request['language_type']);
    }

    //支付记录
    function payListView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\TradeModel($request);

        $result = $this->Model->payList($request);
        ajax_return($result,$request['language_type']);
    }

    //发票合同列表
    function getProtocolListView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model =  new \Model\Gmc\TradeModel($request);

        $result = $Model->getProtocolList($request);

        ajax_return($result,$request['language_type']);
    }

    //下载收据
    function downReceiptApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);
//
//        if($_SERVER['SERVER_NAME'] == 'gmcapi.kedingdang.com') {
//            $res = array('error' => 0, 'errortip' => '该功能暂未开放', 'result' => array());
//            ajax_return($res, $request['language_type']);
//        }

        $publicArray = array();
        $order_pid = $request['order_pid'];
        $publicArray['company_id'] = $request['company_id'];
        $publicArray['school_id'] =  $request['school_id'];
        $publicArray['staffer_id'] =  $request['staffer_id'];
        $publicArray['token'] =  $request['token'];
        $OrderModel  = new \Model\Gmc\TradeModel($publicArray,$order_pid);
        $res = $OrderModel ->downReceiptApi($request);

        $result = array();
        if($res){
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '下载成功', 'result' => $result);
        }else{
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '下载失败', 'result' => array());
        }
        ajax_return($res,$request['language_type']);
    }













    //回传校务系统发票
    function PostInvoiceAdd($data,$invocieOne)
    {
        $sendData = array();
        $sendData[0]['INVOCIE_CODE'] = $data['invoice_code']; //发票代码
        $sendData[0]['INVOCIE_NO'] = $data['invoice_number'];   //发票编号
        $sendData[0]['INVOCIE_DATE'] = date('Ymd'); //发票日期
        $sendData[0]['INVOCIE_AMT'] = $invocieOne['invoice_allprice']; //发票金额

        $timestamp = date('YmdHis');
        $uid = 'test';
        $sign = md5('timestamp='.$timestamp.'&uid='.$uid.'9juWCHNvBcDRTLmF');
        $params = array();
        $params['KEY_NO'] = $invocieOne['order_pid']; //订单pid
        $studentOne = $this->DataControl->getFieldOne('smc_student','student_branch',"student_id={$invocieOne['student_id']}");
        $params['STUD_NO'] = $studentOne['student_branch']; //学员编号
        $params['INVOICES'] = $sendData; //发票列表
        $params = json_encode($params);
//        $ch = curl_init("http://180.166.253.196:85/Sync/api/KidsSyncRece_API/PostInvoiceAdd?sign={$sign}&uid=test&timestamp={$timestamp}");
        $ch = curl_init("http://120.55.44.95/Sync/api/KidsSyncRece_API/PostInvoiceAdd?sign={$sign}&uid=test&timestamp={$timestamp}");
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");
        curl_setopt($ch, CURLOPT_POSTFIELDS,$params);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER,true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            'Content-Type: application/json',
            'Content-Length: ' . strlen($params)
        ));

        $result = curl_exec($ch);
//        if (curl_errno($ch)) {
//            print curl_error($ch);
//        }
        curl_close($ch);
//        echo $result;die;
    }

    function downloadInvoiceView(){
        $request = Input('get.','','trim,addslashes');

        $invoiceOne = $this->DataControl->getOne("shop_invoice","invoice_id='{$request['invoice_id']}'");


        if($invoiceOne['invoice_pdfurl']){
            $result = $invoiceOne['invoice_pdfurl'];
            $res = array('error' => 0, 'errortip' => '下载成功', 'result' => $result);
        }else{
            $param = '{
    "invoiceSn": "' . $invoiceOne['invoice_serialnumber'] . '",
    "invoiceCode": "' . $invoiceOne['invoice_code'] . '",
    "invoiceNo": "' . $invoiceOne['invoice_number'] . '"
}';

            $getBackurl = request_by_curl("https://wxp.easyfapiao.com/v2/haizhouJiaotong/getInvoiceUrl", $param, "POST");
            $List = json_decode($getBackurl, true);

            $result = $List['body']['downloadUrl'];

            $res = array('error' => 0, 'errortip' => '下载成功', 'result' => $result);
        }

        ajax_return($res,$request['language_type']);


    }


    //确认开票(电子)
    function confirmInvoiceAction()
    {
        $request = Input('post.','','trim,addslashes');
       $this->ThisVerify($request);//验证账户

        $res = $this->DataControl->getOne("shop_invoice","invoice_id={$request['invoice_id']}");
        //发票详细数据
        $data = array();
        $data['invoice_mobile'] = $res['invoice_mobile'];
        $data['protocol_id'] = $res['protocol_id'];
        $data['order_pid'] = $res['order_pid'];
        $data['invoice_id'] = $request['invoice_id'];
        $data['parenter_id'] = $res['parenter_id'];
        $data['invoice_type'] = 2;

        $price = $this->DataControl->getFieldOne("smc_student_protocol",'protocol_price,protocol_pid',"protocol_id='{$res['protocol_id']}'");
        $data['invoice_allprice'] = $price['protocol_price'];
        $data['invoice_createtime'] = time();
        $Model = new \Model\InvoiceModel();
        $insertData = $Model->Invoice($data);


        exit;
        if ($insertData) {
            $insertData['invoice_status'] = 1;
            if ($this->DataControl->updateData("shop_invoice", "invoice_id = '{$request['invoice_id']}'", $insertData)) {
                $this->PostInvoiceAdd($insertData,$res);

                $name = $this->DataControl->getFieldOne("smc_staffer","staffer_cnname","staffer_id = '{$request['staffer_id']}'");
                $datatrack = array();
                $datatrack['invoice_id'] = $request['invoice_id'];
                $datatrack['tracks_title'] = $this->LgStringSwitch('已开票');
                $datatrack['tracks_information'] = $this->LgStringSwitch($name['staffer_cnname'].'老师审核通过开票成功');
                $datatrack['staffer_id'] = $request['staffer_id'];
                $datatrack['tracks_playname'] = $name['staffer_cnname'];
                $datatrack['tracks_time'] = time();
                $this->DataControl->insertData("shop_invoice_tracks",$datatrack);

                $this->orderTracks($this->LgStringSwitch('开具发票成功，合同编号'.$price['protocol_pid']), '');

                ajax_return(array('error' => 0,'errortip' => "开票成功!"),$request['language_type']);
            }else{

                $this->orderTracks($this->LgStringSwitch('开具发票失败，请查看原因，合同编号'.$price['protocol_pid']), '');

                ajax_return(array('error' => 1,'errortip' => "开票失败,请稍后再试!"),$request['language_type']);
            }
        }

    }

    function orderTracks($title, $information, $time = '')
    {
//        if ($time == '') {
//            $time = time();
//        }
        $orderTracksData = array();
        $orderTracksData['order_pid'] = $this->payfeeorderOne['order_pid'];
        $orderTracksData['tracks_title'] = $this->LgStringSwitch($title);
        $orderTracksData['tracks_information'] = $this->LgStringSwitch($information);
        $orderTracksData['staffer_id'] = $this->stafferOne['staffer_id'];
        $orderTracksData['tracks_playname'] = $this->LgStringSwitch($this->stafferOne['staffer_cnname']);
//        $orderTracksData['tracks_time'] = $time;//戚总确认日志时间用 系统时间
        $orderTracksData['tracks_time'] = time();
        $this->DataControl->insertData("smc_payfee_order_tracks", $orderTracksData);
    }

    //确认开票(纸质)
    function confirmInvoicePaperAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $data = array();
        $data['invoice_voucher'] = $request['invoice_voucher'];
        $data['invoice_status'] = 1;
        $data['invoice_type'] = 1;

        if($this->DataControl->updateData("shop_invoice","invoice_id = '{$request['invoice_id']}'",$data)) {
            $name = $this->DataControl->getFieldOne("smc_staffer","staffer_cnname","staffer_id = '{$request['staffer_id']}'");
            $datatrack = array();
            $datatrack['invoice_id'] = $request['invoice_id'];
            $datatrack['tracks_title'] = $this->LgStringSwitch('已开票');
            $datatrack['tracks_information'] = $this->LgStringSwitch($name['staffer_cnname'].'老师审核通过开票成功');
            $datatrack['staffer_id'] = $request['staffer_id'];
            $datatrack['tracks_playname'] = $name['staffer_cnname'];
            $datatrack['tracks_time'] = time();
            $this->DataControl->insertData("shop_invoice_tracks",$datatrack);

            $invoiceOne=$this->DataControl->getFieldOne("shop_invoice","order_pid,protocol_id","invoice_id = '{$request['invoice_id']}'");
            $protocolOne = $this->DataControl->getFieldOne("smc_student_protocol","protocol_id,protocol_pid", "protocol_id = '{$invoiceOne['protocol_id']}'");
            $data = array();
            $data['order_pid'] = $invoiceOne['order_pid'];
            $data['tracks_title'] = $this->LgStringSwitch('开票成功');
            $data['tracks_information'] = $this->LgStringSwitch('合同编号为'.$protocolOne['protocol_pid'].'的发票开具成功');
            $data['staffer_id'] = $request['staffer_id'];
            $data['tracks_playname'] = $name['staffer_cnname'];
            $data['tracks_time'] = time();
            $this->DataControl->insertData("smc_payfee_order_tracks", $data);


            ajax_return(array('error' => 0,'errortip' => "开票成功!"),$request['language_type']);
        }else{
            ajax_return(array('error' => 1,'errortip' => "开票失败,请稍后再试!"),$request['language_type']);
        }

    }

    function testView(){
        header("content-type:application/json;charset:utf-8");

        $param = '{
    "storeNo": "jdb19",
    "invoiceSn": "jdb1911234567"
}
';

        $getBackurl = request_by_curl("https://prep.easyfapiao.com/v2/haizhouJiaotong/cancelInvoice", $param, "POST");

        $List = json_decode($getBackurl, true);

        var_dump($List);
    }

    //红冲发票
    function redInkAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $companies = $this->DataControl->getFieldOne("shop_invoice","companies_id,protocol_id","invoice_id = '{$request['invoice_id']}'");
        $status = $this->DataControl->getFieldOne("gmc_code_companies","companies_isInvoice","companies_id = '{$companies['companies_id']}'");

        if($status['companies_isInvoice'] == '1'){
            $data = array();
            $data['invoice_id'] = $request['invoice_id'];
            $Model = new \Model\InvoiceModel();
            $insertData = $Model->Red($data);
            if ($insertData == 1) {
                $datas = array();
                $datas['invoice_cancel'] = $insertData;
                if ($this->DataControl->updateData("shop_invoice", "invoice_id = '{$request['invoice_id']}'", $datas)) {
                    $data = array();
                    $data['protocol_isinvoice'] = 0;
                    $this->DataControl->updateData("smc_student_protocol", "protocol_id = '{$companies['protocol_id']}'", $data);

                    $name = $this->DataControl->getFieldOne("smc_staffer","staffer_cnname","staffer_id = '{$request['staffer_id']}'");
                    $datatrack = array();
                    $datatrack['invoice_id'] = $request['invoice_id'];
                    $datatrack['tracks_title'] = $this->LgStringSwitch('作废发票');
                    $datatrack['tracks_information'] = $this->LgStringSwitch($name['staffer_cnname'].'作废该发票');
                    $datatrack['staffer_id'] = $request['staffer_id'];
                    $datatrack['tracks_playname'] = $name['staffer_cnname'];
                    $datatrack['tracks_time'] = time();
                    $this->DataControl->insertData("shop_invoice_tracks",$datatrack);

                    ajax_return(array('error' => 0,'errortip' => "作废成功!"),$request['language_type']);
                }else{
                    ajax_return(array('error' => 1,'errortip' => "作废失败,请稍后再试!"),$request['language_type']);
                }
            }
        }else{
            $datas = array();
            $datas['invoice_cancel'] = 1;
            if ($this->DataControl->updateData("shop_invoice", "invoice_id = '{$request['invoice_id']}'", $datas)) {
                $data = array();
                $data['protocol_isinvoice'] = 0;
                $this->DataControl->updateData("smc_student_protocol", "protocol_id = '{$companies['protocol_id']}'", $data);

                $name = $this->DataControl->getFieldOne("smc_staffer","staffer_cnname","staffer_id = '{$request['staffer_id']}'");
                $datatrack = array();
                $datatrack['invoice_id'] = $request['invoice_id'];
                $datatrack['tracks_title'] = $this->LgStringSwitch('作废发票');
                $datatrack['tracks_information'] = $this->LgStringSwitch($name['staffer_cnname'].'作废该发票');
                $datatrack['staffer_id'] = $request['staffer_id'];
                $datatrack['tracks_playname'] = $name['staffer_cnname'];
                $datatrack['tracks_time'] = time();
                $this->DataControl->insertData("shop_invoice_tracks",$datatrack);

                ajax_return(array('error' => 0,'errortip' => "作废成功!"),$request['language_type']);
            }else{
                ajax_return(array('error' => 1,'errortip' => "作废失败,请稍后再试!"),$request['language_type']);
            }
        }



    }

    //批量开票
    function batchConfirmInvoiceAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        if(!$request['list']) {
            ajax_return(array('error' => 0,'errortip' => "请选择需处理的发票!","bakfuntion"=>"errormotify"));
        }

        $invoiceList = json_decode(stripslashes($request['list']),true);
        if($invoiceList) {
            foreach ($invoiceList as $val) {
                $res = $this->DataControl->getOne("shop_invoice","invoice_id={$val['invoice_id']}");
                if ($res['invoice_status'] == 1)continue;
                //发票详细数据
                $data = array();
                $data['invoice_mobile'] = $res['invoice_mobile'];
                $data['protocol_id'] = $res['protocol_id'];
                $data['order_pid'] = $res['order_pid'];
                $data['invoice_id'] = $val['invoice_id'];
                $data['parenter_id'] = $res['parenter_id'];
                $data['invoice_type'] = 2;

                $Model = new \Model\InvoiceModel();
                $insertData = $Model->InvoiceMore($data);
                if ($insertData['invoice_pdfurl']) {
                    $insertData['invoice_status'] = 1;
                    $this->DataControl->updateData("shop_invoice","parenter_id={$res['parenter_id']} and protocol_id='{$res['protocol_id']}'",$insertData);
                    $name = $this->DataControl->getFieldOne("smc_staffer","staffer_cnname","staffer_id = '{$request['staffer_id']}'");
                    $datatrack = array();
                    $datatrack['invoice_id'] = $val['invoice_id'];
                    $datatrack['tracks_title'] = $this->LgStringSwitch('已开票');
                    $datatrack['tracks_information'] = $this->LgStringSwitch($name['staffer_cnname'].'老师审核通过开票成功');
                    $datatrack['staffer_id'] = $request['staffer_id'];
                    $datatrack['tracks_playname'] = $name['staffer_cnname'];
                    $datatrack['tracks_time'] = time();
                    $this->DataControl->insertData("shop_invoice_tracks",$datatrack);

                }else{
                    ajax_return(array('error' => 1, 'errortip' => "{$insertData['note']}"),$request['language_type']);

                }

            }
            ajax_return(array('error' => 0, 'errortip' => "批量开票成功!"),$request['language_type']);
        }else{
            ajax_return(array('error' => 1, 'errortip' => "请选择需要开票的发票"),$request['language_type']);
        }
    }

    //拒绝开票
    function refuseInvoiceAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $adfOne = $this->DataControl->selectOne("SELECT l.* from shop_invoice as l where l.invoice_id ='{$request['invoice_id']}'");
        $data = array();
        $data['invoice_status'] = '-1';
        $data['invoice_reason'] = $request['reason'];
        if ($this->DataControl->updateData("shop_invoice", "invoice_id = '{$adfOne['invoice_id']}'", $data)) {
            $datas = array();
            $datas['protocol_isinvoice'] = '0';
            $this->DataControl->updateData("smc_student_protocol", "protocol_id = '{$adfOne['protocol_id']}'", $datas);

            $protocol = $this->DataControl->getFieldOne("smc_student_protocol","protocol_pid","protocol_id = '{$adfOne['protocol_id']}'");

            $name = $this->DataControl->getFieldOne("smc_staffer","staffer_cnname","staffer_id = '{$request['staffer_id']}'");
            $datatrack = array();
            $datatrack['invoice_id'] = $request['invoice_id'];
            $datatrack['tracks_title'] = $this->LgStringSwitch('已拒绝');
            $datatrack['tracks_information'] = $this->LgStringSwitch($name['staffer_cnname'].'老师审核拒绝，'.$request['reason']);
            $datatrack['staffer_id'] = $request['staffer_id'];
            $datatrack['tracks_playname'] = $name['staffer_cnname'];
            $datatrack['tracks_time'] = time();
            $this->DataControl->insertData("shop_invoice_tracks",$datatrack);

            $orderTracksData = array();
            $orderTracksData['order_pid'] = $adfOne['order_pid'];
            $orderTracksData['tracks_title'] = $this->LgStringSwitch('开票失败');
            $orderTracksData['tracks_information'] = $this->LgStringSwitch('合同编号为'.$protocol['protocol_pid'].'的发票开具失败');
            $orderTracksData['tracks_note'] = $this->LgStringSwitch($request['reason']);
            $orderTracksData['staffer_id'] = $request['staffer_id'];
            $orderTracksData['tracks_playname'] = $this->LgStringSwitch($name['staffer_cnname']);
            $orderTracksData['tracks_time'] = time();
            $this->DataControl->insertData("smc_payfee_order_tracks", $orderTracksData);

            ajax_return(array('error' => 0, 'errortip' => "拒绝开票成功!"),$request['language_type']);

        } else {
            ajax_return(array('error' => 1, 'errortip' => "拒绝开票失败!"),$request['language_type']);
        }
    }



    //结尾魔术函数
    function __destruct()
    {

    }
}
