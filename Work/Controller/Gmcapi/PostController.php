<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/13
 * Time: 23:25
 */

namespace Work\Controller\Gmcapi;


class PostController extends viewTpl
{
    public $u;
    public $t;
    public $c;
    public $visitType = "api";
    public $Model;

    //预加载处理类
    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
    }

    function ThisVerify($request){
        $paramArray = array();
        $paramArray['staffer_id'] = $request['staffer_id'];
        $paramArray['company_id'] = $request['company_id'];
        $paramArray['token'] = $request['token'];
        if(!$this->UserLimit($paramArray)){
            $result = array();
            $result["list"] = array();
            $result["tokeninc"] = "0";
            $res = array('error' => '1', 'errortip' => "用户token失效", 'result' => $result);
            ajax_return($res,$request['language_type']);
        }
    }

    //职务列表
    function HomeView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\PostModel($request);

        $result = $this->Model->getPostList($request);
        ajax_return($result,$request['language_type']);
    }

    //添加职务
    function addPostAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\PostModel($request);

        $result = $this->Model->addPostAction($request);
        ajax_return($result,$request['language_type']);
    }

    //编辑职务
    function updatePostAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\PostModel($request);

        $result = $this->Model->updatePostAction($request);
        ajax_return($result,$request['language_type']);
    }

    //职务列表
    function getSchoolPostListView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\PostModel($request);

        $result = $this->Model->getSchoolPostList($request);
        ajax_return($result,$request['language_type']);
    }

    //添加职务
    function addSchoolPostAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\PostModel($request);

        $result = $this->Model->addSchoolPostAction($request);
        ajax_return($result,$request['language_type']);
    }

    //编辑职务
    function updateSchoolPostAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\PostModel($request);

        $result = $this->Model->updateSchoolPostAction($request);
        ajax_return($result,$request['language_type']);
    }

    //删除职务
    function delPostAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\PostModel($request);

        $result = $this->Model->delPostAction($request);
        ajax_return($result,$request['language_type']);
    }

    //离职原因列表
    function reasonsleavingApi()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\PostModel($request);

        $result = $this->Model->reasonsleavingApi($request);
        ajax_return($result,$request['language_type']);
    }

    //添加离职原因
    function addReasonsleavingAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\PostModel($request);

        $result = $this->Model->addReasonsleavingAction($request);
        ajax_return($result,$request['language_type']);
    }

    //编辑离职原因
    function updateReasonsleavingAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\PostModel($request);

        $result = $this->Model->updateReasonsleavingAction($request);
        ajax_return($result,$request['language_type']);
    }

    //删除离职原因
    function delReasonsleavingAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\PostModel($request);

        $result = $this->Model->delReasonsleavingAction($request);
        ajax_return($result,$request['language_type']);
    }

    //职等列表
    function postlevelView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\PostModel($request);

        $result = $this->Model->getPostlevelList($request);
        ajax_return($result,$request['language_type']);
    }

    //编辑职务
    function updatePostlevelAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\PostModel($request);

        $result = $this->Model->updatePostlevelAction($request);
        ajax_return($result,$request['language_type']);
    }

    //添加职等
    function addPostlevelAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\PostModel($request);

        $result = $this->Model->addPostlevelAction($request);
        ajax_return($result,$request['language_type']);
    }

    //删除职等
    function delPostlevelAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\PostModel($request);

        $result = $this->Model->delPostlevelAction($request);
        ajax_return($result,$request['language_type']);
    }

    //职工异动类型列表
    function workchangeView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\PostModel($request);

        $result = $this->Model->getWorkchangeList($request);
        ajax_return($result,$request['language_type']);
    }

    //添加职工异动类型
    function addWorkchangeAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\PostModel($request);

        $result = $this->Model->addWorkchangeAction($request);
        ajax_return($result,$request['language_type']);
    }

    //编辑职工异动类型
    function updateWorkchangeAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\PostModel($request);

        $result = $this->Model->updateWorkchangeAction($request);
        ajax_return($result,$request['language_type']);
    }

    //删除职工异动类型
    function delWorkchangeAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\PostModel($request);

        $result = $this->Model->delWorkchangeAction($request);
        ajax_return($result,$request['language_type']);
    }

    //集团角色列表
    function postroleView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\PostModel($request);

        $result = $this->Model->getPostroleList($request);
        ajax_return($result,$request['language_type']);
    }

    //添加集团角色
    function addPostroleAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\PostModel($request);

        $result = $this->Model->addPostroleAction($request);
        ajax_return($result,$request['language_type']);
    }

    //编辑集团角色
    function updatePostroleAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\PostModel($request);

        $result = $this->Model->updatePostroleAction($request);
        ajax_return($result,$request['language_type']);
    }

    //删除集团角色
    function delPostroleAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\PostModel($request);

        $result = $this->Model->delPostroleAction($request);
        ajax_return($result,$request['language_type']);
    }

    //校园角色列表
    function postpartView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\PostModel($request);

        $result = $this->Model->getPostpartList($request);
        ajax_return($result,$request['language_type']);
    }

    //添加校园角色
    function addPostPartAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\PostModel($request);

        $result = $this->Model->addPostPartAction($request);
        ajax_return($result,$request['language_type']);
    }

    //编辑校园角色
    function updatePostpartAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\PostModel($request);

        $result = $this->Model->updatePostpartAction($request);
        ajax_return($result,$request['language_type']);
    }

    //删除集团角色
    function delPostpartAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\PostModel($request);

        $result = $this->Model->delPostpartAction($request);
        ajax_return($result,$request['language_type']);
    }

    //职工异动原因列表
    function workchangereasonView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\PostModel($request);

        $result = $this->Model->getWorkchangereasonList($request);
        ajax_return($result,$request['language_type']);
    }

    //添加职工异动原因
    function addWorkchangereasonAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\PostModel($request);

        $result = $this->Model->addWorkchangereasonAction($request);
        ajax_return($result,$request['language_type']);
    }

    //编辑职工异动原因
    function updateWorkchangereasonAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\PostModel($request);

        $result = $this->Model->updateWorkchangereasonAction($request);
        ajax_return($result,$request['language_type']);
    }

    //删除职工异动原因
    function delWorkchangereasonAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\PostModel($request);

        $result = $this->Model->delWorkchangereasonAction($request);
        ajax_return($result,$request['language_type']);
    }

    //教师类型列表
    function teachtypeView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\PostModel($request);

        $result = $this->Model->getTeachtypeList($request);
        ajax_return($result,$request['language_type']);
    }

    //添加教师类型
    function addTeachtypeAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\PostModel($request);

        $result = $this->Model->addTeachtypeAction($request);
        ajax_return($result,$request['language_type']);
    }

    //编辑教师类型
    function updateTeachtypeAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\PostModel($request);

        $result = $this->Model->updateTeachtypeAction($request);
        ajax_return($result,$request['language_type']);
    }

    //删除教师类型
    function delTeachtypeAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\PostModel($request);

        $result = $this->Model->delTeachtypeAction($request);
        ajax_return($result,$request['language_type']);
    }

    //员工出勤类型列表
    function stachecktypeView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\PostModel($request);

        $result = $this->Model->getStachecktypeList($request);
        ajax_return($result,$request['language_type']);
    }

    //添加员工出勤类型
    function addStachecktypeAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\PostModel($request);

        $result = $this->Model->addStachecktypeAction($request);
        ajax_return($result,$request['language_type']);
    }

    //编辑员工出勤类型
    function updateStachecktypeAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\PostModel($request);

        $result = $this->Model->updateStachecktypeAction($request);
        ajax_return($result,$request['language_type']);
    }

    //删除员工出勤类型
    function delStachecktypeAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\PostModel($request);

        $result = $this->Model->delStachecktypeAction($request);
        ajax_return($result,$request['language_type']);
    }

    //消息类型列表
    function messagetypeView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\PostModel($request);

        $result = $this->Model->getMessagetypeList($request);
        ajax_return($result,$request['language_type']);
    }

    //消息类型列表
    function getPostroleApi()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\PostModel($request);

        $result = $this->Model->getPostroleApi($request);
        ajax_return($result,$request['language_type']);
    }

    //消息类型列表
    function getPostpartApi()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\PostModel($request);

        $result = $this->Model->getPostpartApi($request);
        ajax_return($result,$request['language_type']);
    }
    //消息类型列表
    function getCoRoleApi()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\PostModel($request);

        $result = $this->Model->getCoRoleApi($request);
        ajax_return($result,$request['language_type']);
    }
    //消息类型列表
    function getScPartApi()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\PostModel($request);

        $result = $this->Model->getScPartApi($request);
        ajax_return($result,$request['language_type']);
    }

    //添加消息类型
    function addMessagetypeAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\PostModel($request);

        $result = $this->Model->addMessagetypeAction($request);
        ajax_return($result,$request['language_type']);
    }

    //编辑消息类型
    function updateMessagetypeAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\PostModel($request);

        $result = $this->Model->updateMessagetypeAction($request);
        ajax_return($result,$request['language_type']);
    }

    //删除消息类型
    function delMessagetypeAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\PostModel($request);

        $result = $this->Model->delMessagetypeAction($request);
        ajax_return($result,$request['language_type']);
    }

    //薪资模块管理
    function getSalarymoduleListView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\PostModel($request);

        $result = $this->Model->getSalarymoduleList($request);
        ajax_return($result,$request['language_type']);
    }

    //添加模块管理
    function addSalarymoduleAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\PostModel($request);

        $result = $this->Model->addSalarymoduleAction($request);
        ajax_return($result,$request['language_type']);
    }

    //编辑模块管理
    function updateSalarymoduleAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\PostModel($request);

        $result = $this->Model->updateSalarymoduleAction($request);
        ajax_return($result,$request['language_type']);
    }

    //删除模块管理
    function delSalarymoduleAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\PostModel($request);

        $result = $this->Model->delSalarymoduleAction($request);
        ajax_return($result,$request['language_type']);
    }

    //校园角色下拉框列表
    function getScPostpartApi()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\PostModel($request);

        $result = $this->Model->getScPostpartApi($request);
        ajax_return($result,$request['language_type']);
    }

    function getPerformanceListApi(){
        $request = Input('get.', '', 'trim,addslashes');

        $this->ThisVerify($request);
        $Model = new \Model\Gmc\PostModel($request);
        $res = $Model->getPerformanceList($request);
        $field = array();
        $k=0;

        $field[$k]["fieldstring"] = "performance_id";
        $field[$k]["fieldname"] = "id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "performance_name";
        $field[$k]["fieldname"] = "业绩明细";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "performance_iscalculated";
        $field[$k]["fieldname"] = "是否计业绩";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $result = array();
        $result["field"] = $field;
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function addPerformanceAction(){

        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Gmc\PostModel($request);
        $res = $Model->addPerformance($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => $Model->oktip, 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function editPerformanceAction(){

        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Gmc\PostModel($request);
        $res = $Model->editPerformance($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => $Model->oktip, 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function delPerformanceAction(){

        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Gmc\PostModel($request);
        $res = $Model->delPerformance($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => $Model->oktip, 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }



    //结尾魔术函数
    function __destruct()
    {

    }
}
