<?php


namespace Work\Controller\Gmcapi;


class HomeController extends viewTpl
{
    public $u;
    public $t;
    public $c;

    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
    }


    function enrolmentNumberApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Gmc\HomeModel($request);
        $res = $Model->enrolmentNumber($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }

    //集团统计
    function companyCensusView(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Gmc\HomeModel($request);

        $result = $Model->companyCensus($request);
        ajax_return($result,$request['language_type']);
    }

    //集团总揽
    function companyAssumeView(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Gmc\HomeModel($request);

        $result = $Model->companyAssume($request);
        ajax_return($result,$request['language_type']);
    }

    //待办事项
    function toDoView(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Gmc\HomeModel($request);

        $result = $Model->toDo($request);
        ajax_return($result,$request['language_type'],1);
    }

    //初始化设置
    function initializationSetView(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Gmc\HomeModel($request);

        $result = $Model->initializationSet($request);
        ajax_return($result,$request['language_type']);
    }

    //本月报名人数趋势
    function monthStuBookView(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Gmc\HomeModel($request);

        $result = $Model->monthStuBook($request);
        ajax_return($result,$request['language_type']);
    }


}