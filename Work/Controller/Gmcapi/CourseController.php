<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong <PERSON>
 * Date: 2017/4/13
 * Time: 23:25
 */

namespace Work\Controller\Gmcapi;


class CourseController extends viewTpl
{
    public $u;
    public $t;
    public $c;
    public $visitType = "api";
    public $Model;

    //预加载处理类
    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
    }

    //课程类型列表
    function HomeView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\CourseModel($request);

        $result = $this->Model->getCoursetypeList($request);
        ajax_return($result, $request['language_type']);
    }

    //添加课程类型
    function addCoursetypeAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\CourseModel($request);

        $result = $this->Model->addCoursetypeAction($request);
        ajax_return($result, $request['language_type']);
    }

    //编辑课程类型
    function updateCoursetypeAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\CourseModel($request);

        $result = $this->Model->updateCoursetypeAction($request);
        ajax_return($result, $request['language_type']);
    }

    //删除课程类型
    function delCoursetypeAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\CourseModel($request);

        $result = $this->Model->delCoursetypeAction($request);
        ajax_return($result, $request['language_type']);
    }

    //班种管理列表
    function coursecatView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\CourseModel($request);

        $result = $this->Model->coursecatView($request);
        ajax_return($result, $request['language_type']);
    }

    //班种特殊月份设置列表
    function coursecatMonthView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\CourseModel($request);

        $result = $this->Model->coursecatMonth($request);
        ajax_return($result, $request['language_type']);
    }

    //添加班种
    function addCoursecatAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\CourseModel($request);

        $result = $this->Model->addCoursecatAction($request);
        ajax_return($result, $request['language_type']);
    }

    //添加特殊月份设置
    function addCoursecatMonthAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\CourseModel($request);

        $result = $this->Model->addCoursecatMonthAction($request);
        ajax_return($result, $request['language_type']);
    }

    //编辑特殊月份设置
    function updateCoursecatMonthAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\CourseModel($request);

        $result = $this->Model->updateCoursecatMonthAction($request);
        ajax_return($result, $request['language_type']);
    }

    //删除特殊月份设置
    function delCoursecatMonthAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\CourseModel($request);

        $result = $this->Model->delCoursecatMonthAction($request);
        ajax_return($result, $request['language_type']);
    }

    //编辑班种
    function updateCoursecatAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\CourseModel($request);

        $result = $this->Model->updateCoursecatAction($request);
        ajax_return($result, $request['language_type']);
    }

    //删除班种
    function delCoursecatAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\CourseModel($request);

        $result = $this->Model->delCoursecatAction($request);
        ajax_return($result, $request['language_type']);
    }

    //课程期收类型列表
    function coursetermView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\CourseModel($request);

        $result = $this->Model->coursetermView($request);
        ajax_return($result, $request['language_type']);
    }

    //添加课程期收类型
    function addCoursetermAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\CourseModel($request);

        $result = $this->Model->addCoursetermAction($request);
        ajax_return($result, $request['language_type']);
    }

    //编辑课程期收类型
    function updateCoursetermAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\CourseModel($request);

        $result = $this->Model->updateCoursetermAction($request);
        ajax_return($result, $request['language_type']);
    }

    //删除课程期收类型
    function delCoursetermAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\CourseModel($request);

        $result = $this->Model->delCoursetermAction($request);
        ajax_return($result, $request['language_type']);
    }

    //课程管理列表
    function courseView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\CourseModel($request);

        $result = $this->Model->course($request);
        ajax_return($result, $request['language_type']);
    }

    //改变监管
    function updateSuperviseAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\CourseModel($request);

        $result = $this->Model->updateSuperviseAction($request);
        ajax_return($result,$request['language_type']);
    }

    function changeIsNeedConfirmAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Gmc\CourseModel($request);

        $res = $Model->changeIsNeedConfirm($request);
        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => $Model->oktip, 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function getCourseOneApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Gmc\CourseModel($request);

        $res = $Model->getCourseOne($request);
        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => $Model->oktip, 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }


    //编辑课程
    function updateCourseAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\CourseModel($request);

        $result = $this->Model->updateCourseAction($request);
        ajax_return($result, $request['language_type']);
    }

    //添加课程
    function addCourseAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\CourseModel($request);

        $result = $this->Model->addCourseAction($request);
        ajax_return($result, $request['language_type']);
    }

    //删除课程
    function delCourseAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\CourseModel($request);

        $result = $this->Model->delCourseAction($request);
        ajax_return($result, $request['language_type']);
    }

    //根据班种获取班组
    function getCoursetypeApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\CourseModel($request);

        $result = $this->Model->getCoursetypeApi($request);
        ajax_return($result, $request['language_type']);
    }

    //根据班种获取班级
    function getCoursecatApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\CourseModel($request);

        $result = $this->Model->getCoursecatApi($request);
        ajax_return($result, $request['language_type']);
    }

    //课时时间设置列表
    function coursetimesListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\CourseModel($request);

        $result = $this->Model->coursetimesList($request);
        ajax_return($result, $request['language_type']);
    }

    // 获取排课的时间限制
    function getCourseLimitTimeApi()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\CourseModel($request);
        $data = $this->Model->getCourseLimitTimeApi($request);

        $result['list'] = $data;
        $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        ajax_return($res, $request['language_type']);
    }

    //添加课时时间设置
    function addCoursetimesAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\CourseModel($request);

        $result = $this->Model->addCoursetimesAction($request);
        ajax_return($result, $request['language_type']);
    }

    //编辑课时时间设置
    function updateCoursetimesAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\CourseModel($request);

        $result = $this->Model->updateCoursetimesAction($request);
        ajax_return($result, $request['language_type']);
    }

    //删除课时时间设置
    function delCoursetimesAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\CourseModel($request);

        $result = $this->Model->delCoursetimesAction($request);
        ajax_return($result, $request['language_type']);
    }

    //删除课时时间设置
    function aAction()
    {
        $pizza = "1,2,3";
        $pieces = explode(",", $pizza);
        var_dump($pieces);
    }

    //启用/不启用
    function OpenOnClassAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\CourseModel($request);

        $result = $this->Model->OpenOnClassAction($request);
        ajax_return($result, $request['language_type']);
    }

    //获取电访课次区间列表
    function getIntervalListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\CourseModel($request);

        $result = $this->Model->getIntervalList($request);
        ajax_return($result, $request['language_type']);
    }

    //添加电访课次区间
    function AddIntervalAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\CourseModel($request);

        $result = $this->Model->AddIntervalAction($request);
        ajax_return($result, $request['language_type']);
    }

    //编辑电访课次区间
    function EditIntervalAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\CourseModel($request);

        $result = $this->Model->EditIntervalAction($request);
        ajax_return($result, $request['language_type']);
    }

    //删除电访课次区间
    function DelIntervalAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\CourseModel($request);

        $result = $this->Model->DelIntervalAction($request);
        ajax_return($result, $request['language_type']);
    }

    function getMonthdisListApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Gmc\CourseModel($request);
        $res = $Model->getMonthdisList($request);
        $field = array();
        $k = 0;

        $field[$k]["fieldname"] = "monthdis_month";
        $field[$k]["fieldstring"] = "优惠月份";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "monthdis_ratio_name";
        $field[$k]["fieldstring"] = "优惠折扣";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "schoolName";
        $field[$k]["fieldstring"] = "适用学校";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["ismethod"] = 1;
        $k++;

        $field[$k]["fieldname"] = "coursecatName";
        $field[$k]["fieldstring"] = "适用班种";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["ismethod"] = 1;
        $k++;

        $field[$k]["fieldname"] = "monthdis_remark";
        $field[$k]["fieldstring"] = "备注";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();

        $result["field"] = $field;

        if ($res) {
            $result["list"] = $res['list'];
            $result['allnum'] = $res['allnum'];

            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result['allnum'] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);

        }
        ajax_return($res, $request['language_type']);
    }

    function addMonthdisAction(){
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Gmc\CourseModel($request);
        $res = $Model->addMonthdis($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '新增成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function editMonthdisAction(){
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Gmc\CourseModel($request);
        $res = $Model->editMonthdis($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '编辑成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function delMonthdisAction(){
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Gmc\CourseModel($request);
        $res = $Model->delMonthdis($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '删除成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function getSchoolListApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Gmc\CourseModel($request);
        $res = $Model->getSchoolList($request);

        $k=0;
        $field = array();
        $field[$k]["fieldstring"] = "school_id";
        $field[$k]["fieldname"] = "学校ID";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校园名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "school_enname";
        $field[$k]["fieldname"] = "检索代码";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "district_cnname";
        $field[$k]["fieldname"] = "所属区域";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $result = array();
        $result["field"] = $field;
        if ($res) {
            $result["list"] = $res['schoolList'];
            $result["district"] = $res['district'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["district"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }

    function getApplySchoolListApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Gmc\CourseModel($request);
        $res = $Model->getApplySchoolList($request);

        $k=0;
        $field = array();
        $field[$k]["fieldstring"] = "school_id";
        $field[$k]["fieldname"] = "学校ID";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校园名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "school_enname";
        $field[$k]["fieldname"] = "检索代码";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "district_cnname";
        $field[$k]["fieldname"] = "所属区域";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $result = array();
        $result["field"] = $field;
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }

    function applySchoolAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);

        $Model = new \Model\Gmc\CourseModel($request);
        $res = $Model->applySchool($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '适配成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }

    function removeSchoolAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);

        $Model = new \Model\Gmc\CourseModel($request);
        $res = $Model->removeSchool($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '移除成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }

    function getAllCourseCatApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);

        $Model = new \Model\Gmc\CourseModel($request);
        $res = $Model->getAllCourseCat($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }

    function getCoursecatListApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Gmc\CourseModel($request);
        $res = $Model->getCoursecatList($request);

        $k=0;
        $field = array();
        $field[$k]["fieldstring"] = "coursecat_id";
        $field[$k]["fieldname"] = "班种ID";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "coursecat_cnname";
        $field[$k]["fieldname"] = "班种名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "coursecat_branch";
        $field[$k]["fieldname"] = "班种编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;


        $result = array();
        $result["field"] = $field;
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }

    function getApplyCoursecatListApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Gmc\CourseModel($request);
        $res = $Model->getApplyCoursecatList($request);

        $k=0;
        $field = array();
        $field[$k]["fieldstring"] = "coursecat_id";
        $field[$k]["fieldname"] = "课程ID";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "coursecat_cnname";
        $field[$k]["fieldname"] = "班种名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "coursecat_branch";
        $field[$k]["fieldname"] = "班种编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $result = array();
        $result["field"] = $field;
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }

    function applyCoursecatAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);

        $Model = new \Model\Gmc\CourseModel($request);
        $res = $Model->applyCoursecat($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '适配成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }

    function removeCoursecatAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);

        $Model = new \Model\Gmc\CourseModel($request);
        $res = $Model->removeCoursecat($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '移除成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }

    //班外课时类型列表
    function outclasstypeView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\CourseModel($request);
        $result = $this->Model->outclasstype($request);
        ajax_return($result, $request['language_type']);
    }

    //编辑班外课时类型
    function updateOutclasstypeAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\CourseModel($request);

        $result = $this->Model->updateOutclasstypeAction($request);
        ajax_return($result, $request['language_type']);
    }

    //添加班外课时类型
    function addOutclasstypeAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\CourseModel($request);

        $result = $this->Model->addOutclasstypeAction($request);
        ajax_return($result, $request['language_type']);
    }

    //删除班外课时类型
    function delOutclasstypeAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\CourseModel($request);

        $result = $this->Model->delOutclasstypeAction($request);
        ajax_return($result, $request['language_type']);
    }

    //添加班外课时
    function classScheduleAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
//        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\CourseModel($request);

        $result = $this->Model->classScheduleAction($request);
        ajax_return($result, $request['language_type']);
    }



    //结尾魔术函数
    function __destruct()
    {

    }
}
