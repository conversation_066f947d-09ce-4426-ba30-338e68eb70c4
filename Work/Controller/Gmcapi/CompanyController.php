<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/13
 * Time: 23:25
 */

namespace Work\Controller\Gmcapi;


class CompanyController extends viewTpl
{
    public $u;
    public $t;
    public $c;
    public $visitType = "api";
    public $Model;

    //预加载处理类
    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
    }

    function ThisVerify($request){
        $paramArray = array();
        $paramArray['staffer_id'] = $request['staffer_id'];
        $paramArray['company_id'] = $request['company_id'];
        $paramArray['token'] = $request['token'];
        if(!$this->UserLimit($paramArray)){
            $result = array();
            $result["list"] = array();
            $result["tokeninc"] = "0";
            $res = array('error' => '1', 'errortip' => "用户token失效", 'result' => $result);
            ajax_return($res,$request['language_type']);
        }
    }


    //获取集团资料
    function HomeView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $this->Model = new \Model\Gmc\PostModel($request);

        $result = $this->Model->getCompanyApi($request);
        ajax_return($result,$request['language_type']);
    }

    //编辑集团资料
    function updateCompanyView()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $this->Model = new \Model\Gmc\PostModel($request);

        $result = $this->Model->updateCompanyView($request);
        ajax_return($result,$request['language_type']);
    }

    function editCompanySetAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Gmc\PostModel($request);
        $res= $Model->editCompanySet($request);
        if($res){
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '编辑成功', 'result' => $result);
        }else{
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }

    function getCompanyAuthorityApi(){

        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Gmc\PostModel($request);
        $res = $Model->getCompanyAuthority($request);
        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
        } else {
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }

    //获取company_id
    function getCompanyidApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $this->Model = new \Model\Gmc\PostModel($request);

        $result = $this->Model->getCompanyidApi($request);
        ajax_return($result,$request['language_type']);
    }

    //获取首页名字
    function getNameApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Gmc\PostModel($request);
        $result = $Model->getNameApi($request);
        if ($result) {
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
        } else {
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }

        ajax_return($res,$request['language_type']);
    }

    //获取个人基本信息名字
    function getOwnInfoApi(){
        $request = Input('get.','','trim,addslashes');
//        $this->ThisVerify($request);//验证账户

        $this->Model = new \Model\Gmc\PostModel($request);

        $result = $this->Model->getOwnInfoApi($request);
        ajax_return($result,$request['language_type'],1);
    }

    //获取个人职务
    function getOwnPostApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $this->Model = new \Model\Gmc\PostModel($request);

        $result = $this->Model->getOwnPostApi($request);
        ajax_return($result,$request['language_type']);
    }

    //获取阅读人物信息
    function getReadStafferApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $this->Model = new \Model\Gmc\PostModel($request);

        $result = $this->Model->getReadStafferApi($request);
        ajax_return($result,$request['language_type']);
    }

    //获取账号资料
    function UserView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $this->Model = new \Model\Gmc\PostModel($request);

        $result = $this->Model->getUserlist($request);
        ajax_return($result,$request['language_type']);
    }

    //获取账号资料
    function ContectView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $this->Model = new \Model\Gmc\PostModel($request);

        $result = $this->Model->getContect($request);
        ajax_return($result,$request['language_type']);
    }


    //获取消息详情
    function getMessageDetailView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $this->Model = new \Model\Gmc\PostModel($request);

        $result = $this->Model->getMessageDetail($request);
        ajax_return($result,$request['language_type']);
    }

    //获取集团通知
    function NoticeView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $this->Model = new \Model\Gmc\PostModel($request);

        $result = $this->Model->getNotice($request);
        ajax_return($result,$request['language_type']);
    }

    //获取集团通知
    function NoticeDetailView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $this->Model = new \Model\Gmc\PostModel($request);

        $result = $this->Model->getNoticeDetail($request);
        ajax_return($result,$request['language_type']);
    }

    //获取消息管理集团通知
    function CompanyNoticeView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $this->Model = new \Model\Gmc\PostModel($request);

        $result = $this->Model->getCompanyNotice($request);
        ajax_return($result,$request['language_type']);
    }

    //获取消息管理校园通知
    function SchoolNoticeView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $this->Model = new \Model\Gmc\PostModel($request);

        $result = $this->Model->getSchoolNotice($request);
        ajax_return($result,$request['language_type']);
    }

    //删除消息
    function delNoticeAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\PostModel($request);

        $result = $this->Model->delNoticeAction($request);
        ajax_return($result,$request['language_type']);
    }

    //置顶消息
    function topNoticeAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\PostModel($request);

        $result = $this->Model->topNoticeAction($request);
        ajax_return($result,$request['language_type']);
    }

    //取消置顶消息
    function downNoticeAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\PostModel($request);

        $result = $this->Model->downNoticeAction($request);
        ajax_return($result,$request['language_type']);
    }

    //发布集团通知
    function addCompanyNoticeAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\PostModel($request);

        $result = $this->Model->addCompanyNoticeAction($request);
        ajax_return($result,$request['language_type']);
    }

    //校园通知适配校园
    function getSchoolRangeView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $this->Model = new \Model\Gmc\PostModel($request);

        $result = $this->Model->getSchoolRange($request);
        ajax_return($result,$request['language_type']);
    }

    //集团通知适配职位
    function getCompanyPostRangeView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $this->Model = new \Model\Gmc\PostModel($request);

        $result = $this->Model->getCompanyPostRange($request);
        ajax_return($result,$request['language_type']);
    }

    //校园通知适配职位
    function getSchoolPostRangeView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $this->Model = new \Model\Gmc\PostModel($request);

        $result = $this->Model->getSchoolPostRange($request);
        ajax_return($result,$request['language_type']);
    }

    //获取消息类别
    function getNoticeTypeView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $this->Model = new \Model\Gmc\PostModel($request);

        $result = $this->Model->getNoticeType($request);
        ajax_return($result,$request['language_type']);
    }


    //获取校园消息类别
    function getScNoticeTypeView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $this->Model = new \Model\Gmc\PostModel($request);

        $result = $this->Model->getScNoticeType($request);
        ajax_return($result,$request['language_type']);
    }

    //发布校园通知
    function addSchoolNoticeAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\PostModel($request);

        $result = $this->Model->addSchoolNoticeAction($request);
        ajax_return($result,$request['language_type']);
    }

    //适用职务列表
    function getSetPostView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $this->Model = new \Model\Gmc\PostModel($request);

        $result = $this->Model->getSetPost($request);
        ajax_return($result,$request['language_type']);
    }

    //适用学校列表
    function getSetSchoolView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $this->Model = new \Model\Gmc\PostModel($request);

        $result = $this->Model->getSetSchool($request);
        ajax_return($result,$request['language_type']);
    }

    //删除适用学校
    function delSetSchoolAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\PostModel($request);

        $result = $this->Model->delSetSchoolAction($request);
        ajax_return($result,$request['language_type']);
    }

    //删除适用职务
    function delSetPostAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\PostModel($request);

        $result = $this->Model->delSetPostAction($request);
        ajax_return($result,$request['language_type']);
    }

    //改变监管
    function updateSuperviseAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\PostModel($request);

        $result = $this->Model->updateSuperviseAction($request);
        ajax_return($result,$request['language_type']);
    }

    //改变推送启用状态
    function updateWxSendStatusAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\PostModel($request);

        $result = $this->Model->updateWxSendStatusAction($request);
        ajax_return($result,$request['language_type']);
    }

    //删除消息
    function delMessageAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\PostModel($request);

        $result = $this->Model->delMessageAction($request);
        ajax_return($result,$request['language_type']);
    }

    //编辑集团消息
    function updateCompanyNoticeAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\PostModel($request);

        $result = $this->Model->updateCompanyNoticeAction($request);
        ajax_return($result,$request['language_type']);
    }

    //修改密码
    function updatePassAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $this->Model =  new \Model\Smc\AffairsModel($request);

        $result = $this->Model->updatePassAction($request);
        ajax_return($result,$request['language_type']);
    }

    //集团操作日志
    function getComWorkLogView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $this->Model = new \Model\Gmc\PostModel($request);

        $result = $this->Model->getComWorkLog($request);
        ajax_return($result,$request['language_type']);
    }

    //CRM操作日志
    function getCrmWorkLogView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $this->Model = new \Model\Gmc\PostModel($request);

        $result = $this->Model->getCrmWorkLog($request);
        ajax_return($result,$request['language_type']);
    }

    //校务操作日志
    function getScWorkLogView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $this->Model = new \Model\Gmc\PostModel($request);

        $result = $this->Model->getScWorkLog($request);
        ajax_return($result,$request['language_type']);
    }

    //推送消息配置列表
    function getWxSendListView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $this->Model = new \Model\Gmc\PostModel($request);

        $result = $this->Model->getWxSendList($request);
        ajax_return($result,$request['language_type']);
    }

    //集团操作日志JSON
    function getComWorkLogJSONView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $this->Model = new \Model\Gmc\PostModel($request);

        $result = $this->Model->getComWorkLogJSON($request);
        ajax_return($result,$request['language_type']);
    }

    //CRM操作日志JSON
    function getCrmWorkLogJSONView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $this->Model = new \Model\Gmc\PostModel($request);

        $result = $this->Model->getCrmWorkLogJSON($request);
        ajax_return($result,$request['language_type']);
    }

    //校务操作日志JSON
    function getScWorkLogJSONView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $this->Model = new \Model\Gmc\PostModel($request);

        $result = $this->Model->getScWorkLogJSON($request);
        ajax_return($result,$request['language_type']);
    }

    //首页 -- 某日的日程安排
    function eventOneApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        if(!isset($request['event_time']) || $request['event_time'] == ''){
            $res = array('error' => '1', 'errortip' => '必须输入查询时间', 'result' => array());
        }
        //model
        $Model = new \Model\Gmc\StafferModel($request);
        $dataList = $Model->eventOneApi($request);

        $field = array();
        $field["event_tag"] = "事件标签：字符串";
        $field["event_remark"] = "跟进提醒备注";

        $result = array();
        if($dataList){
            $result["field"] = $field;
            $result["data"] = $dataList;
            $res = array('error' => '0', 'errortip' => '某日的日程安排', 'result' => $result);
        }else{
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '某日的日程安排', 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }

    //首页 -- 月份列表日程展示
    function monthEventApi()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        //model
        $Model = new \Model\Gmc\StafferModel($request);
        $dataList = $Model->monthEventApi($request);

        $field = array();
        $field["event_id"] = "事件序号";
        $field["year"] = "年";
        $field["month"] = "月";
        $field["day"] = "日";
        $field["is_have"] = "是否有事件：-1 没有";

        $result = array();
        if($dataList){
            $result["field"] = $field;
            $result["theWeek"] = $dataList['week'];
            $result["data"] = $dataList['mothList'];
            $res = array('error' => '0', 'errortip' => '某日的日程安排', 'result' => $result);
        }else{
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '某日的日程安排', 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }

    //首页 -- 新增日程安排
    function addEventAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        //model
        $Model = new \Model\Gmc\StafferModel($request);
        $dataList = $Model->addEventAction($request);

        if($dataList){
            $res = array('error' => '0', 'errortip' => "日程安排新增成功");
        }else{
            $res = array('error' => '1', 'errortip' => '日程安排新增失败');
        }
        ajax_return($res,$request['language_type']);
    }


    function getSetModuleListView(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model =  new \Model\Gmc\ModuleModel($request);

        $result = $this->Model->getSetModuleList($request);

        ajax_return($result,$request['language_type']);
    }

    function getSetPercentView(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model =  new \Model\Gmc\ModuleModel($request);

        $result = $this->Model->getSetPercent($request);

        ajax_return($result,$request['language_type']);
    }

    //更换地址
    function ChangeUrlView()
    {
        $request = Input('get.','','trim,addslashes');
//        $this->ThisVerify($request);//验证账户
        $this->Model =  new \Model\Gmc\ModuleModel($request);



//        if($request['re_postbe_id'] == '0' || $request['re_postbe_id'] == 'null'){
//            $result = $this->Model->getModuleList($request);
//        }else{
//            $result = $this->Model->getPowerList($request);
//        }

        $result = $this->Model->getPowerList($request);


        ajax_return($result,$request['language_type']);

    }

    //集团 短信 日志
    function getGmcMislogView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $this->Model = new \Model\Gmc\PostModel($request);

        $result = $this->Model->getGmcMislog($request);
        ajax_return($result,$request['language_type']);
    }


    //结尾魔术函数
    function __destruct()
    {

    }
}
