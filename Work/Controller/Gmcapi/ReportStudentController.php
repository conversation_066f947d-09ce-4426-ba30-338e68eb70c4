<?php


namespace Work\Controller\Gmcapi;


class ReportStudentController extends viewTpl
{
    public $u;
    public $t;
    public $c;

    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
    }

    //1866学员个人资产表--X
    function studentPropertyView()
    {
        ini_set('display_errors', '1');
        ini_set("memory_limit", '-1');
        set_time_limit(600);
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Report\Gmc\StudentReportModel($request);
        $res = $ReportModel->studentProperty($request);

        $k = 0;
        $field = array();

        $field[$k]["fieldstring"] = "province_name";
        $field[$k]["fieldname"] = "省份";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "school_shortname";
        $field[$k]["fieldname"] = "学校简称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "student_id";
        $field[$k]["fieldname"] = "学员ID";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["fieldname"] = "学员中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_enname";
        $field[$k]["fieldname"] = "学员英文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_branch";
        $field[$k]["fieldname"] = "学员编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "itembalance_left";
        $field[$k]["fieldname"] = "杂费余额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "balance_left";
        $field[$k]["fieldname"] = "账户余额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

//        $field[$k]["fieldstring"] = "catbalance_left";
//        $field[$k]["fieldname"] = "预收余额";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//        $k++;

        $field[$k]["fieldstring"] = "coursebalance_left";
        $field[$k]["fieldname"] = "课程余额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "unpaid_left";
        $field[$k]["fieldname"] = "订单欠费";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "goods_left";
        $field[$k]["fieldname"] = "待领用资产";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "trading_price";
        $field[$k]["fieldname"] = "在途资产";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

//        $field[$k]["fieldstring"] = "integral_left";
//        $field[$k]["fieldname"] = "剩余积分";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//        $k++;

        $field[$k]["fieldstring"] = "property_left";
        $field[$k]["fieldname"] = "资产总额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => "暂无学员资产信息", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //学员账户余额表--X
    function studentTimebalanceView()
    {
        ini_set('display_errors', '1');
        ini_set("memory_limit", '-1');
        set_time_limit(600);
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Report\Gmc\StudentReportModel($request);
        $res = $ReportModel->studentTimebalance($request);

        $k = 0;
        $field = array();
        $field[$k]["fieldstring"] = "school_id";
        $field[$k]["fieldname"] = "分校id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "province_name";
        $field[$k]["fieldname"] = "省份";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "分校编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "分校名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "companies_cnname";
        $field[$k]["fieldname"] = "主体名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["fieldname"] = "学员中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_enname";
        $field[$k]["fieldname"] = "学员英文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_branch";
        $field[$k]["fieldname"] = "学员编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "balance";
        $field[$k]["fieldname"] = "账户可退余额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "withholdbalance";
        $field[$k]["fieldname"] = "账户不可退余额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "allprice";
        $field[$k]["fieldname"] = "账户总余额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "forwardprice";
        $field[$k]["fieldname"] = "账户结转金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            if ($request['school_id']) {
                $res = array('error' => 1, 'errortip' => "暂无学员账户余额信息", 'result' => $result);
            } else {
                $res = array('error' => 1, 'errortip' => "请先选择学校", 'result' => $result);
            }
        }
        ajax_return($res, $request['language_type']);
    }

    //学员课程余额明细--X
    function studentCourseBalancesLeftView()
    {
        //error_reporting(E_ALL);
        ini_set('display_errors', '1');
        ini_set("memory_limit", '-1');
        set_time_limit(600);
        $request = Input('get.', '', 'strip_tags,trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Report\Gmc\StudentReportModel($request);
        $res = $ReportModel->studentCourseBalancesLeftReport($request);

        $k = 0;
        $field = array();
        $field[$k]["fieldstring"] = "student_id";
        $field[$k]["fieldname"] = "学员ID";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "province_name";
        $field[$k]["fieldname"] = "省份";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["fieldname"] = "学员中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_enname";
        $field[$k]["fieldname"] = "学员英文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_branch";
        $field[$k]["fieldname"] = "学员编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_balance";
        $field[$k]["fieldname"] = "学员账户余额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursetype_cnname";
        $field[$k]["fieldname"] = "班组";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "companies_cnname";
        $field[$k]["fieldname"] = "主体名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursetype_branch";
        $field[$k]["fieldname"] = "班组编号";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursecat_cnname";
        $field[$k]["fieldname"] = "班种";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursecat_branch";
        $field[$k]["fieldname"] = "班种编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "course_cnname";
        $field[$k]["fieldname"] = "课程别";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "course_branch";
        $field[$k]["fieldname"] = "课程别编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursebalance_figure";
        $field[$k]["fieldname"] = "课程余额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursebalance_time";
        $field[$k]["fieldname"] = "剩余课次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursebalance_unitexpend";
        $field[$k]["fieldname"] = "耗课单价";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_cnname";
        $field[$k]["fieldname"] = "班级名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_enname";
        $field[$k]["fieldname"] = "班级别名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_branch";
        $field[$k]["fieldname"] = "班级编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "clockNum";
        $field[$k]["fieldname"] = "班内耗课次数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_status";
        $field[$k]["fieldname"] = "在班状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "unpaid_price";
        $field[$k]["fieldname"] = "订单欠费金额";
        $field[$k]["show"] = ($request['end_time'] == date("Y-m-d")) ? 1 : 0;
        $field[$k]["custom"] = 0;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => "暂无学员课程余额明细", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //学员预收余额明细表--X
    function stuAdvanceReportView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Smc\ReportModel($request);
        $res = $ReportModel->stuAdvanceReport($request);

        $k = 0;
        $field = array();

        $field[$k]["fieldstring"] = "school_id";
        $field[$k]["fieldname"] = "分校id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "province_name";
        $field[$k]["fieldname"] = "省份";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "分校编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "分校名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_branch";
        $field[$k]["fieldname"] = "学员编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["fieldname"] = "学员中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_enname";
        $field[$k]["fieldname"] = "学员英文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "enrolled_status";
        $field[$k]["fieldname"] = "在校状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "feetype_name";
        $field[$k]["fieldname"] = "收费类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursetype_cnname";
        $field[$k]["fieldname"] = "班组名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursetype_branch";
        $field[$k]["fieldname"] = "班组编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursecat_branch";
        $field[$k]["fieldname"] = "班种";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursecatbalance_month";
        $field[$k]["fieldname"] = "管理费月份";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursecatbalance_figure";
        $field[$k]["fieldname"] = "课程剩余余额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursecatbalance_time";
        $field[$k]["fieldname"] = "课程剩余次数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursecatbalance_unitexpend";
        $field[$k]["fieldname"] = "消耗单价";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursecatbalance_unitrefund";
        $field[$k]["fieldname"] = "退费单价";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => "暂无学员预收余额信息", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    //学员杂费余额报表--X
    function stuItemLeftReportView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Report\Gmc\StudentReportModel($request);
        $res = $ReportModel->stuItemLeftReport($request);

        $k = 0;
        $field = array();
        $field[$k]["fieldstring"] = "province_name";
        $field[$k]["fieldname"] = "省份";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["fieldname"] = "学员中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_enname";
        $field[$k]["fieldname"] = "学员英文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_branch";
        $field[$k]["fieldname"] = "学员编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursecat_cnname";
        $field[$k]["fieldname"] = "班种名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursecat_branch";
        $field[$k]["fieldname"] = "班种编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "course_cnname";
        $field[$k]["fieldname"] = "课程别名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "course_branch";
        $field[$k]["fieldname"] = "课程别编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "feeitem_class";
        $field[$k]["fieldname"] = "杂费类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "feeitem_cnname";
        $field[$k]["fieldname"] = "杂费名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "left_figure";
        $field[$k]["fieldname"] = "剩余金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "left_number";
        $field[$k]["fieldname"] = "剩余次数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => "暂无学员杂费余额记录", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //班级学员信息列表--wgh
    function classInfoListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);

        $ReportModel = new \Model\Report\Gmc\ClassReportModel($request);
        $res = $ReportModel->classInfoList($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "province_name";
        $field[$k]["fieldname"] = "省份";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["fieldname"] = "学员中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_enname";
        $field[$k]["fieldname"] = "学员英文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_branch";
        $field[$k]["fieldname"] = "学员编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "family_mobile";
        $field[$k]["fieldname"] = "联系电话";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_cnname";
        $field[$k]["fieldname"] = "班级名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_enname";
        $field[$k]["fieldname"] = "班级别名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_branch";
        $field[$k]["fieldname"] = "班级编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursecat_cnname";
        $field[$k]["fieldname"] = "班种名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursecat_branch";
        $field[$k]["fieldname"] = "班种编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "course_cnname";
        $field[$k]["fieldname"] = "课程别名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "course_branch";
        $field[$k]["fieldname"] = "课程别编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "study_beginday";
        $field[$k]["fieldname"] = "入班日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "study_endday";
        $field[$k]["fieldname"] = "出班日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "study_isreading_name";
        $field[$k]["fieldname"] = "当前在班状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => "暂无班级学员信息", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    //学员待入班报表 -- qu
    function stuEnrolledClassView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Report\Gmc\StudentReportModel($request);
        $res = $ReportModel->studentUnStudy($request);
        $field = array();
        $k = 0;

        $field[$k]["fieldstring"] = "school_id";
        $field[$k]["fieldname"] = "分校id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "province_name";
        $field[$k]["fieldname"] = "省份";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "分校编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "分校名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursetype_id";
        $field[$k]["fieldname"] = "班组id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "coursetype_branch";
        $field[$k]["fieldname"] = "班组编号";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursetype_cnname";
        $field[$k]["fieldname"] = "班组名称";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursecat_id";
        $field[$k]["fieldname"] = "班种id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursecat_branch";
        $field[$k]["fieldname"] = "班种编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursecat_cnname";
        $field[$k]["fieldname"] = "班种名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_branch";
        $field[$k]["fieldname"] = "学员编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["fieldname"] = "学员中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_enname";
        $field[$k]["fieldname"] = "学员英文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "parenter_mobile";
        $field[$k]["fieldname"] = "联系电话";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "course_branches";
        $field[$k]["fieldname"] = "课程别编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "course_cnnames";
        $field[$k]["fieldname"] = "课程别名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "total_money";
        $field[$k]["fieldname"] = "课程别余额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "total_times";
        $field[$k]["fieldname"] = "剩余课次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursecat_last_atte_date";
        $field[$k]["fieldname"] = "班种最后考勤日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res['list']) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => "暂无学员待入班信息", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);


    }

    //学员延班报表 -- ling qu
    function stuWaitClassView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Report\Gmc\StudentReportModel($request);
        $res = $ReportModel->stuWaitClass($request);

        $field = array();
        $k = 0;

        $field[$k]["fieldstring"] = "school_id";
        $field[$k]["fieldname"] = "分校id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "province_name";
        $field[$k]["fieldname"] = "省份";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "分校编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "分校名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["fieldname"] = "学员中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_enname";
        $field[$k]["fieldname"] = "学员英文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_branch";
        $field[$k]["fieldname"] = "学员编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_cnname";
        $field[$k]["fieldname"] = "班级名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_enname";
        $field[$k]["fieldname"] = "班级别名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_branch";
        $field[$k]["fieldname"] = "班级编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "course_cnname";
        $field[$k]["fieldname"] = "课程别名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "course_branch";
        $field[$k]["fieldname"] = "课程别编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursebalance_time";
        $field[$k]["fieldname"] = "剩余课次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursebalance_figure";
        $field[$k]["fieldname"] = "课程别余额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "changelog_day";
        $field[$k]["fieldname"] = "异动日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "reason_note";
        $field[$k]["fieldname"] = "异动原因";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "staffer_cnname";
        $field[$k]["fieldname"] = "操作人";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "changelog_createtime";
        $field[$k]["fieldname"] = "操作时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;


        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res['list']) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => "暂无学员延班记录", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    //学员课次购买报表 -- ling
    function stuCoursePurchaseView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Report\Gmc\StudentReportModel($request);
        $res = $ReportModel->stuCoursePurchase($request);

        $field = array();
        $k = 0;

        $field[$k]["fieldstring"] = "school_id";
        $field[$k]["fieldname"] = "分校id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "province_name";
        $field[$k]["fieldname"] = "省份";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "分校编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "分校名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["fieldname"] = "学员中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_enname";
        $field[$k]["fieldname"] = "学员英文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_branch";
        $field[$k]["fieldname"] = "学员编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursetype_cnname";
        $field[$k]["fieldname"] = "班组名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursetype_branch";
        $field[$k]["fieldname"] = "班组编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursecat_cnname";
        $field[$k]["fieldname"] = "班种名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursecat_branch";
        $field[$k]["fieldname"] = "班种编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "order_pid";
        $field[$k]["fieldname"] = "订单编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "course_cnname";
        $field[$k]["fieldname"] = "课程别名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "course_branch";
        $field[$k]["fieldname"] = "课程别编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "pricinglog_buytimes";
        $field[$k]["fieldname"] = "购买课次数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "pricinglog_buyprice";
        $field[$k]["fieldname"] = "购买金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "pricinglog_createtime";
        $field[$k]["fieldname"] = "购买时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "status";
        $field[$k]["fieldname"] = "在班状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "finaltimes";
        $field[$k]["fieldname"] = "剩余课次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;


        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => $ReportModel->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //学员班级流失报表 -- zhou
    function studentClassOffView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Report\Gmc\StudentReportModel($request);
        $res = $ReportModel->studentClassOff($request);
        $field = array();
        $k = 0;

        $field[$k]["fieldstring"] = "province_name";
        $field[$k]["fieldname"] = "省份";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "district_cnname";
        $field[$k]["fieldname"] = "所属区域";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;


        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["fieldname"] = "学员中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_enname";
        $field[$k]["fieldname"] = "学员英文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_branch";
        $field[$k]["fieldname"] = "学员编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "channel_name";
        $field[$k]["fieldname"] = "专案名称";
        $field[$k]["show"] = (isset($request["company_id"]) && $request["company_id"] == '8888') ? 1 : 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_cnname";
        $field[$k]["fieldname"] = "班级名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_enname";
        $field[$k]["fieldname"] = "班级别名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_branch";
        $field[$k]["fieldname"] = "班级编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursetype_branch";
        $field[$k]["fieldname"] = "班组编号";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "oursetype_cnname";
        $field[$k]["fieldname"] = "班组名称";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursecat_branch";
        $field[$k]["fieldname"] = "班种编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursecat_cnname";
        $field[$k]["fieldname"] = "班种名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "course_branch";
        $field[$k]["fieldname"] = "课程别编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "course_cnname";
        $field[$k]["fieldname"] = "课程别名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "changelog_day";
        $field[$k]["fieldname"] = "异动日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "last_atte_date";
        $field[$k]["fieldname"] = "最后考勤日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "change_reason";
        $field[$k]["fieldname"] = "异动原因";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "staffer_cnname";
        $field[$k]["fieldname"] = "操作人";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "changelog_createtime";
        $field[$k]["fieldname"] = "操作时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => $ReportModel->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    //分校流失报表 -- zhou
    function studentSchoolOffView()
    {
        ini_set('display_errors', '1');
        ini_set("memory_limit", '-1');
        set_time_limit(600);
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Report\Gmc\StudentReportModel($request);
        $res = $ReportModel->studentSchoolOff($request);
        $field = array();
        $k = 0;

        $field[$k]["fieldstring"] = "province_name";
        $field[$k]["fieldname"] = "省份";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "district_cnname";
        $field[$k]["fieldname"] = "所属区域";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["fieldname"] = "学员中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_enname";
        $field[$k]["fieldname"] = "学员英文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_branch";
        $field[$k]["fieldname"] = "学员编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "channel_name";
        $field[$k]["fieldname"] = "专案名称";
        $field[$k]["show"] = (isset($request["company_id"]) && $request["company_id"] == '8888') ? 1 : 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "stuchange_name";
        $field[$k]["fieldname"] = "异动类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "changelog_day";
        $field[$k]["fieldname"] = "流失日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursetype_cnname";
        $field[$k]["fieldname"] = "流失班组";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursetype_branch";
        $field[$k]["fieldname"] = "流失班组编号";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "changelog_category";
        $field[$k]["fieldname"] = "流失类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "changelog_note";
        $field[$k]["fieldname"] = "流失原因";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "connect_times";
        $field[$k]["fieldname"] = "电访结果";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "last_atte_date";
        $field[$k]["fieldname"] = "最后考勤日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "staffer_cnname";
        $field[$k]["fieldname"] = "操作人";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "changelog_createtime";
        $field[$k]["fieldname"] = "操作时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;


        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => $ReportModel->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    //分校流失统计报表
    function statisticsSchoolOffView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Gmc\ReportModel($request);
        $res = $ReportModel->statisticsSchoolOff($request);
        $field = array();
        $k = 0;

        $field[$k]["fieldstring"] = "school_tagbak";
        $field[$k]["fieldname"] = "校区备注";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_id";
        $field[$k]["fieldname"] = "学校ID";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "offnums";
        $field[$k]["fieldname"] = "分校流失总数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "trackingnums";
        $field[$k]["fieldname"] = "流失电访数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => $ReportModel->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    //电访明细表
    function catitrackView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);

        $ReportModel = new \Model\Smc\ReportModel($request);
        $res = $ReportModel->catitrack($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["fieldname"] = "学生名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_branch";
        $field[$k]["fieldname"] = "学生编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "track_classname";
        $field[$k]["fieldname"] = "电访类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "track_day";
        $field[$k]["fieldname"] = "电访日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "commode_name";
        $field[$k]["fieldname"] = "电访方式";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "result_name";
        $field[$k]["fieldname"] = "电访结果";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["ismethod"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "track_note";
        $field[$k]["fieldname"] = "电访内容";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "staffer_branch";
        $field[$k]["fieldname"] = "电访老师编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "staffer_cnname";
        $field[$k]["fieldname"] = "电访老师姓名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "post_name";
        $field[$k]["fieldname"] = "电访人职务";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res['list']) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => "暂无电访明细表", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //学生积分交易明细
    function studentIntegrallogView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Report\Gmc\StudentReportModel($request);
        $res = $ReportModel->studentIntegrallog($request);
        $field = array();
        $k = 0;

        $field[$k]["fieldstring"] = "province_name";
        $field[$k]["fieldname"] = "省份";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["fieldname"] = "学员中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_enname";
        $field[$k]["fieldname"] = "学员英文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_branch";
        $field[$k]["fieldname"] = "学员编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "integrallog_playclass_name";
        $field[$k]["fieldname"] = "所属类别";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "integraltype_name";
        $field[$k]["fieldname"] = "积分类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "integrallog_playamount";
        $field[$k]["fieldname"] = "交易积分";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "integrallog_reason";
        $field[$k]["fieldname"] = "交易原因";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "integrallog_time";
        $field[$k]["fieldname"] = "完成时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "status";
        $field[$k]["fieldname"] = "状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;


        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => $ReportModel->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    //公益学生免费课次明细
    function studentChannelInfoView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Report\Gmc\StudentReportModel($request);
        $res = $ReportModel->studentChannelInfo($request);
        $field = array();
        $k = 0;

        $field[$k]["fieldstring"] = "province_name";
        $field[$k]["fieldname"] = "省份";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["fieldname"] = "学员中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_enname";
        $field[$k]["fieldname"] = "学员英文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_branch";
        $field[$k]["fieldname"] = "学员编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "enrolled_status";
        $field[$k]["fieldname"] = "学员状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "guildstutype_name";
        $field[$k]["fieldname"] = "公益来源";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "guildpolicy_startdate";
        $field[$k]["fieldname"] = "公益开始日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "guildpolicy_enddate";
        $field[$k]["fieldname"] = "公益结束日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "apply_toclasstimes";
        $field[$k]["fieldname"] = "转吉的堡课次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "used_times";
        $field[$k]["fieldname"] = "已消耗课次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "free_times_new";
        $field[$k]["fieldname"] = "已赠送未消耗课次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "used_times_month";
        $field[$k]["fieldname"] = "区间消耗免费课次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "free_times_left";
        $field[$k]["fieldname"] = "剩余未赠送课次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "last_atte_date";
        $field[$k]["fieldname"] = "最近消耗免费课次日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_info_now";
        $field[$k]["fieldname"] = "学员当前所在校";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => $ReportModel->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    function stuConsumeListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Report\Gmc\StudentReportModel($request);
        $res = $ReportModel->stuConsumeList($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "student_id";
        $field[$k]["fieldname"] = "ID";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "province_name";
        $field[$k]["fieldname"] = "省份";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["fieldname"] = "学员中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_enname";
        $field[$k]["fieldname"] = "学员英文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_branch";
        $field[$k]["fieldname"] = "学员编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_cnname";
        $field[$k]["fieldname"] = "班级中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_enname";
        $field[$k]["fieldname"] = "班级别名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_branch";
        $field[$k]["fieldname"] = "班级编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "course_cnname";
        $field[$k]["fieldname"] = "课程别名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "course_branch";
        $field[$k]["fieldname"] = "课程别编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursebalance_time";
        $field[$k]["fieldname"] = "剩余课次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursebalance_figure";
        $field[$k]["fieldname"] = "剩余余额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursebalance_unitexpend";
        $field[$k]["fieldname"] = "耗课单价";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "hour_nochecknum";
        $field[$k]["fieldname"] = "班级剩余课次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "arrearagetimes";
        $field[$k]["fieldname"] = "预计欠费课次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result['field'] = $field;
        $result['list'] = $res['list'];
        $result['allnum'] = $res['allnum'];

        $result['tips'] = $this->LgStringSwitch('预警值：按集团设置的课程剩余课次进行预警');
        if ($res['list']) {
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $result, 'allnum' => $res['allnum']);
        } else {
            $res = array('error' => 1, 'errortip' => '暂无学员耗课预警', 'result' => $result, 'allnum' => $res['allnum']);
        }
        ajax_return($res, $request['language_type']);
    }

}
