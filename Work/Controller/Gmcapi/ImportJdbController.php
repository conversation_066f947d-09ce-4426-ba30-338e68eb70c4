<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2018/9/1
 * Time: 11:24
 */

namespace Work\Controller\Gmcapi;


class ImportJdbController extends viewTpl{
    public $u;
    public $t;
    public $c;

    function __construct() {
        parent::__construct ();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
    }

    function ImportStubalanceView(){
        $balanceArray = execl_to_array("studentbalance.xlsx",array('BRANCH'=>'school_branch','STUD_NO'=>'student_branch','LEFT_AMT'=>'student_balance'));
        array_shift($balanceArray);

        $Model = new \Model\SchoolOldModel('8888');
        $stunums = 0;
        if($balanceArray){
            foreach($balanceArray as $balanceOne){
                if($balanceOne['school_branch'] !=='' && $balanceOne['student_branch']!=='' && $balanceOne['student_balance'] !==''){
                    $Model->UpdataStudentBalance($balanceOne['school_branch'],$balanceOne['student_branch'],$balanceOne['student_balance'],strtotime('2019-04-30 12:00:00'));
                    $stunums++;
                    echo $Model->oktip."|{$stunums}"."<br />";
                }
            }
        }
    }

    function ImportClassView(){
        $dataArray = execl_to_array("iptclass.xlsx",array('BRANCH'=>'school_branch','CLASS_NO'=>'class_branch','ST_DATE'=>'class_stdate','END_DATE'=>'class_enddate'));
        array_shift($dataArray);

        $Model = new \Model\SchoolOldModel('8888');
        $classnums = 1;
        if($dataArray){
            foreach($dataArray as $dataOne){
                echo $classnums.":".$dataOne['class_branch']."<br />";
                $modelCont = $Model->UpdataSchoolClass($dataOne['school_branch'],$dataOne['class_branch']);
                if($modelCont){
                    echo $Model->oktip."<br />";
                }else{
                    echo $Model->errortip.$dataOne['class_branch']."<br />";
                }
                $classnums++;
            }
        }
    }

    function ImportStudyView(){
        $dataArray = execl_to_array("studentstudy.xlsx",array('BRANCH'=>'school_branch','CLASS_NO'=>'class_branch','STUD_NO'=>'student_branch','入班日期'=>'study_beginday'));
        array_shift($dataArray);

        $Model = new \Model\SchoolOldModel('8888');
        $datanums = 1;
        if($dataArray){
            foreach($dataArray as $dataOne){
                echo $datanums.":".$dataOne['student_branch']."<br />";
                $modelCont = $Model->UpdataStudentClassStudy($dataOne['student_branch'],$dataOne['class_branch'],$dataOne['study_beginday']);
                if($modelCont){
                    echo $Model->oktip."<br />";
                }else{
                    echo $Model->errortip.$dataOne['student_branch']."<br />";
                }
                $datanums++;
            }
        }
    }

    function ImportStuCourseBalanceView(){
        $dataArray = execl_to_array("stucoursebalance.xlsx",array('BRANCH'=>'school_branch','CLASS_NO'=>'class_branch','STUD_NO'=>'student_branch','课程别'=>'course_branch','剩余课次'=>'coursebalance_time','剩余金额'=>'coursebalance_figure','退费单价'=>'coursebalance_unitrefund','耗课单价'=>'coursebalance_unitexpend'));
        array_shift($dataArray);

        $Model = new \Model\SchoolOldModel('8888');
        $datanums = 1;
        if($dataArray){
            foreach($dataArray as $dataOne){
                echo $datanums.":".$dataOne['student_branch']."<br />";
                $modelCont = $Model->UpdataStuCourseBalance($dataOne['school_branch'],$dataOne['class_branch'],$dataOne['student_branch'],$dataOne['course_branch'],$dataOne['coursebalance_time'],$dataOne['coursebalance_figure'],$dataOne['coursebalance_unitrefund'],$dataOne['coursebalance_unitexpend']);
                if($modelCont){
                    echo $Model->oktip."<br />";
                }else{
                    echo $Model->errortip.$dataOne['student_branch']."<br />";
                }
                $datanums++;
            }
        }
    }

    function ImportStuArrearsView(){
        $dataArray = execl_to_array("stuarrears.xlsx",array('BRANCH'=>'school_branch','CLASS_NO'=>'class_branch','STUD_NO'=>'student_branch','课程别'=>'course_branch','欠费类别'=>'order_type','欠费金额'=>'order_paymentprice','欠费课次'=>'num'));
        array_shift($dataArray);

        $Model = new \Model\SchoolOldModel('8888');
        $datanums = 1;
        if($dataArray){
            foreach($dataArray as $dataOne){
                echo $datanums.":".$dataOne['student_branch']."<br />";
                $modelCont = $Model->UpdataStuArrears($dataOne['school_branch'],$dataOne['class_branch'],$dataOne['student_branch'],$dataOne['course_branch'],$dataOne['order_type'],$dataOne['order_paymentprice'],$dataOne['num']);
                if($modelCont){
                    echo $Model->oktip."<br />";
                }else{
                    echo $Model->errortip.$dataOne['student_branch']."<br />";
                }
                $datanums++;
            }
        }
    }


    function ImportSchoolView(){
        $dataArray = execl_to_array("iptclass.xlsx",array('BRANCH'=>'school_branch','CLASS_NO'=>'class_branch','ST_DATE'=>'class_stdate','END_DATE'=>'class_enddate'));
        array_shift($dataArray);

        $Model = new \Model\SchoolOldModel('8888');
        $school_branch = array();
        if($dataArray){
            foreach($dataArray as $dataOne){
                $school_branch[] = $dataOne['school_branch'];
            }
        }

        $school_branch = array_unique($school_branch);
        if($school_branch){
            foreach($school_branch as $branchone){
                $modelCont = $Model->UpdataStudentEnrolled($branchone);
                if($modelCont){
                    echo $Model->oktip."<br />";
                }else{
                    echo $Model->errortip.$branchone['student_branch']."<br />";
                }
            }
        }
    }
    //课程管理费导入
    function ImportMangepriceView(){
        $dataArray = execl_to_array("stumangeprice.xlsx",array('ORDERPID'=>'order_pid','BRANCH'=>'school_branch','CLASS_NO'=>'class_branch','STUD_NO'=>'student_branch','CODE_CLASS'=>'course_branch','MONTH'=>'MONTH','PRICE'=>'orderPrice','CLASSTYPE'=>'orderType'));
        array_shift($dataArray);

        $Model = new \Model\SchoolOldModel('8888');
        if($dataArray){
            foreach($dataArray as $dataOne){
                $modelBak = $Model->UpdataStudentMange($dataOne['school_branch'],$dataOne['class_branch'],$dataOne['student_branch'],$dataOne['order_pid'],$dataOne['orderPrice'],$dataOne['orderType'],$dataOne['MONTH']);
                if($modelBak){
                    echo $Model->oktip."<br />";
                }else{
                    echo $Model->errortip.$dataOne['order_pid']."<br />";
                }
            }
        }
    }









    function ImportExcelStubalanceView(){
        $balanceArray = execl_to_array("studentbalance.xlsx",array('CODE'=>'excel_code','BRANCH'=>'school_branch','STUD_NO'=>'student_branch','LEFT_AMT'=>'student_balance'));
        array_shift($balanceArray);

        $Model = new \Model\SchoolOldModel('8888');
        $stunums = 0;
        if($balanceArray){
            foreach($balanceArray as $balanceOne){
                if($balanceOne['school_branch'] !=='' && $balanceOne['student_branch']!=='' && $balanceOne['student_balance'] !==''){
                    $Model->InsertExcelStubalance($balanceOne['excel_code'],$balanceOne['school_branch'],$balanceOne['student_branch'],$balanceOne['student_balance']);
                    $stunums++;
                    echo $Model->oktip."|{$stunums}"."<br />";
                }
            }
        }
    }


    function ImportExcelClassView(){
        $dataArray = execl_to_array("iptclass.xlsx",array('CODE'=>'excel_code','BRANCH'=>'school_branch','CLASS_NO'=>'class_branch','课程别名称'=>'course_branch','ST_DATE'=>'class_stdate','END_DATE'=>'class_enddate'));
        array_shift($dataArray);

        $Model = new \Model\SchoolOldModel('8888');
        $classnums = 1;
        if($dataArray){
            foreach($dataArray as $dataOne){
                echo $classnums.":".$dataOne['class_branch']."<br />";
                $modelCont = $Model->InsertExcelClass($dataOne['excel_code'],$dataOne['school_branch'],$dataOne['class_branch'],$dataOne['course_branch'],$dataOne['class_stdate'],$dataOne['class_enddate']);
                if($modelCont){
                    echo $Model->oktip."<br />";
                }else{
                    echo $Model->errortip.$dataOne['class_branch']."<br />";
                }
                $classnums++;
            }
        }
    }


    function ImportExcelStudyView(){
        $dataArray = execl_to_array("studentstudy.xlsx",array('CODE'=>'excel_code','BRANCH'=>'school_branch','CLASS_NO'=>'class_branch','STUD_NO'=>'student_branch','入班日期'=>'study_beginday'));
        array_shift($dataArray);

        $Model = new \Model\SchoolOldModel('8888');
        $datanums = 1;
        if($dataArray){
            foreach($dataArray as $dataOne){
                echo $datanums.":".$dataOne['student_branch']."<br />";
                $modelCont = $Model->InsertExcelStudentClassStudy($dataOne['excel_code'],$dataOne['school_branch'],$dataOne['class_branch'],$dataOne['student_branch'],$dataOne['study_beginday']);
                if($modelCont){
                    echo $Model->oktip."<br />";
                }else{
                    echo $Model->errortip.$dataOne['student_branch']."<br />";
                }
                $datanums++;
            }
        }
    }

    function ImportExcelStuCourseBalanceView(){
        $dataArray = execl_to_array("stucoursebalance.xlsx",array('CODE'=>'excel_code','BRANCH'=>'school_branch','CLASS_NO'=>'class_branch','STUD_NO'=>'student_branch','课程别'=>'course_branch','剩余课次'=>'coursebalance_time','剩余金额'=>'coursebalance_figure','退费单价'=>'coursebalance_unitrefund','耗课单价'=>'coursebalance_unitexpend'));
        array_shift($dataArray);

        $Model = new \Model\SchoolOldModel('8888');
        $datanums = 1;
        if($dataArray){
            foreach($dataArray as $dataOne){
                echo $datanums.":".$dataOne['student_branch']."<br />";
                $modelCont = $Model->InsertExcelStuCourseBalance($dataOne['excel_code'],$dataOne['school_branch'],$dataOne['class_branch'],$dataOne['student_branch'],$dataOne['course_branch'],$dataOne['coursebalance_time'],$dataOne['coursebalance_figure'],$dataOne['coursebalance_unitrefund'],$dataOne['coursebalance_unitexpend']);
                if($modelCont){
                    echo $Model->oktip."<br />";
                }else{
                    echo $Model->errortip.$dataOne['student_branch']."<br />";
                }
                $datanums++;
            }
        }
    }

    function ImportExcelStuArrearsView(){
        $dataArray = execl_to_array("stuarrears.xlsx",array('CODE'=>'excel_code','BRANCH'=>'school_branch','CLASS_NO'=>'class_branch','STUD_NO'=>'student_branch','课程别'=>'course_branch','欠费类别'=>'order_type','欠费金额'=>'order_paymentprice','欠费课次'=>'num'));
        array_shift($dataArray);

        $Model = new \Model\SchoolOldModel('8888');
        $datanums = 1;
        if($dataArray){
            foreach($dataArray as $dataOne){
                echo $datanums.":".$dataOne['student_branch']."<br />";
                $modelCont = $Model->InsertExcelStuArrears($dataOne['excel_code'],$dataOne['school_branch'],$dataOne['class_branch'],$dataOne['student_branch'],$dataOne['course_branch'],$dataOne['order_type'],$dataOne['order_paymentprice'],$dataOne['num']);
                if($modelCont){
                    echo $Model->oktip."<br />";
                }else{
                    echo $Model->errortip.$dataOne['student_branch']."<br />";
                }
                $datanums++;
            }
        }
    }

    function ImportExcelMangepriceView(){
        $dataArray = execl_to_array("stumangeprice.xlsx",array('CODE'=>'excel_code','BRANCH'=>'school_branch','CLASS_NO'=>'class_branch','STUD_NO'=>'student_branch','CODE_CLASS'=>'course_branch','MONTH'=>'MONTH','PRICE'=>'orderPrice','CLASSTYPE'=>'orderType'));
        array_shift($dataArray);

        $Model = new \Model\SchoolOldModel('8888');
        if($dataArray){
            foreach($dataArray as $dataOne){
                $modelBak = $Model->InsertExcelStudentMange($dataOne['excel_code'],$dataOne['school_branch'],$dataOne['class_branch'],$dataOne['student_branch'],$dataOne['course_branch'],$dataOne['orderType'],$dataOne['MONTH'],$dataOne['orderPrice']);
                if($modelBak){
                    echo $Model->oktip."<br />";
                }else{
                    echo $Model->errortip.$dataOne['order_pid']."<br />";
                }
            }
        }
    }

    function ImportExcelstuadvanceView(){
        $dataArray = execl_to_array("stucatbalance.xlsx",array('CODE'=>'excel_code','BRANCH'=>'school_branch','STUD_NO'=>'student_branch','code_Charge'=>'feetype_code','code_Cat'=>'coursecat_branch','剩余金额'=>'coursecatbalance_figure','剩余课次'=>'coursecat_time'));
        array_shift($dataArray);

        $Model = new \Model\SchoolOldModel('8888');
        if($dataArray){
            foreach($dataArray as $dataOne){
                $modelBak = $Model->InsertExcelstuadvance($dataOne['excel_code'],$dataOne['school_branch'],$dataOne['student_branch'],$dataOne['feetype_code'],$dataOne['coursecat_branch'],$dataOne['coursecatbalance_figure'],$dataOne['coursecat_time']);
                if($modelBak){
                    echo $Model->oktip."<br />";
                }else{
                    echo $Model->errortip.$dataOne['school_branch']."<br />";
                }
            }
        }
    }

    function ImportExcelstuadvancebalanceView(){
        $dataArray = execl_to_array("stuadvancebalance.xlsx",array('预收编号'=>'excel_code','学员编号'=>'student_branch','课程别'=>'coursecat_branch','预收分类'=>'feetype_code','预收日期'=>'advance_day','预收金额'=>'advance_price','剩余金额'=>'coursebalance_figure','预收课次'=>'advance_time','剩余课次'=>'coursebalance_time','备注'=>'advance_note','对应级别'=>'advance_coursebranch','预收科目'=>'advance_item'));
        array_shift($dataArray);
        $Model = new \Model\SchoolOldModel('8888');
        if($dataArray){
            foreach($dataArray as $dataOne){
                $modelBak = $Model->InsertExcelstuadvancebalance($dataOne);
                if($modelBak){
                    echo $Model->oktip."<br />";
                }else{
                    echo $Model->errortip.$dataOne['student_branch']."<br />";
                }
            }
        }
    }

    //处理用户余额
    function UpdateStubalanceView(){
        $Model = new \Model\SchoolOldModel('8888');
        $balanceArray=$this->DataControl->selectClear("select * from smc_excel_studentbalance where excel_updatatype='0' and school_branch='01425'");
        $stunums = 0;
        if($balanceArray){
            foreach($balanceArray as $balanceOne){
                if($balanceOne['school_branch'] !=='' && $balanceOne['student_branch']!=='' && $balanceOne['student_balance'] !==''){
                    $modelCont=$Model->UpdataStudentBalance($balanceOne['school_branch'],$balanceOne['student_branch'],$balanceOne['student_balance'],strtotime('2019-04-30 23:59:59'));
                    if($modelCont){
                        $excel_data=array();
                        $excel_data['excel_updatatype']='1';
                        $this->DataControl->updateData("smc_excel_studentbalance","excel_code='{$balanceOne['excel_code']}'",$excel_data);
                        echo $Model->oktip."|{$stunums}"."<br />";
                    }else{
                        $excel_data=array();
                        $excel_data['excel_updatatype']='-1';
                        $this->DataControl->updateData("smc_excel_studentbalance","excel_code='{$balanceOne['excel_code']}'",$excel_data);
                        echo $Model->oktip."|{$stunums}"."<br />";
                    }
                    $stunums++;
                }
            }
        }
    }

    //处理在读班级
    function UpdateClassView(){
        $Model = new \Model\SchoolOldModel('8888');
        $dataArray=$this->DataControl->selectClear("select * from smc_excel_class where excel_updatatype='0' and school_branch='01425'");
        $classnums = 1;
        if($dataArray){
            foreach($dataArray as $dataOne){
                echo $classnums.":".$dataOne['class_branch']."<br />";
                $modelCont = $Model->UpdataSchoolClass($dataOne['school_branch'],$dataOne['class_branch']);
                if($modelCont){
                    $excel_data=array();
                    $excel_data['excel_updatatype']='1';
                    $this->DataControl->updateData("smc_excel_class","excel_code='{$dataOne['excel_code']}'",$excel_data);
                    echo $Model->oktip."<br />";
                }else{
                    $excel_data=array();
                    $excel_data['excel_updatatype']='-1';
                    $this->DataControl->updateData("smc_excel_class","excel_code='{$dataOne['excel_code']}'",$excel_data);
                    echo $Model->errortip.$dataOne['class_branch']."<br />";
                }
                $classnums++;
            }
        }
    }

    function UpdateStudyView(){
        $Model = new \Model\SchoolOldModel('8888');
        $dataArray=$this->DataControl->selectClear("select * from smc_excel_classstudent where excel_updatatype='0' and school_branch='01425'");
        $datanums = 1;
        if($dataArray){
            foreach($dataArray as $dataOne){
                echo $datanums.":".$dataOne['student_branch']."<br />";
                $modelCont = $Model->UpdataStudentClassStudy($dataOne['excel_code'],$dataOne['school_branch'],$dataOne['student_branch'],$dataOne['class_branch'],$dataOne['study_beginday']);
                if($modelCont){
                    $excel_data=array();
                    $excel_data['excel_updatatype']='1';
                    $this->DataControl->updateData("smc_excel_classstudent","excel_code='{$dataOne['excel_code']}'",$excel_data);
                    echo $Model->oktip."<br />";
                }else{
                    $excel_data=array();
                    $excel_data['excel_updatatype']='-1';
                    $this->DataControl->updateData("smc_excel_classstudent","excel_code='{$dataOne['excel_code']}'",$excel_data);
                    echo $Model->errortip.$dataOne['student_branch']."<br />";
                }
                $datanums++;
            }
        }
    }

    function UpdateStuCourseBalanceView(){
        $Model = new \Model\SchoolOldModel('8888');
        $dataArray=$this->DataControl->selectClear("select * from smc_excel_studentcoursebalance where excel_updatatype='0' and school_branch='01425'");
        $datanums = 1;
        if($dataArray){
            foreach($dataArray as $dataOne){
                echo $datanums.":".$dataOne['student_branch']."<br />";
                $modelCont = $Model->UpdataStuCourseBalance($dataOne['school_branch'],$dataOne['class_branch'],$dataOne['student_branch'],$dataOne['course_branch'],$dataOne['coursebalance_time'],$dataOne['coursebalance_figure'],$dataOne['coursebalance_unitrefund'],$dataOne['coursebalance_unitexpend']);
                if($modelCont){
                    $excel_data=array();
                    $excel_data['excel_updatatype']='1';
                    $this->DataControl->updateData("smc_excel_studentcoursebalance","excel_code='{$dataOne['excel_code']}'",$excel_data);
                    echo $Model->oktip."<br />";
                }else{
                    $excel_data=array();
                    $excel_data['excel_updatatype']='-1';
                    $this->DataControl->updateData("smc_excel_studentcoursebalance","excel_code='{$dataOne['excel_code']}'",$excel_data);
                    echo $Model->errortip.$dataOne['student_branch']."<br />";
                }
                $datanums++;
            }
        }
    }

    function UpdateStuArrearsView(){
        $Model = new \Model\SchoolOldModel('8888');
        $dataArray=$this->DataControl->selectClear("select * from smc_excel_arrearage where excel_updatatype='0' and school_branch='01425'");
        $datanums = 1;
        if($dataArray){
            foreach($dataArray as $dataOne){
                echo $datanums.":".$dataOne['student_branch']."<br />";
                $modelCont = $Model->UpdataStuArrears($dataOne['school_branch'],$dataOne['class_branch'],$dataOne['student_branch'],$dataOne['course_branch'],$dataOne['arrearage_type'],$dataOne['arrearage_figure'],$dataOne['arrearage_time']);
                if($modelCont){
                    $excel_data=array();
                    $excel_data['excel_updatatype']='1';
                    $this->DataControl->updateData("smc_excel_arrearage","excel_code='{$dataOne['excel_code']}'",$excel_data);
                    echo $Model->oktip."<br />";
                }else{
                    $excel_data=array();
                    $excel_data['excel_updatatype']='-1';
                    $this->DataControl->updateData("smc_excel_arrearage","excel_code='{$dataOne['excel_code']}'",$excel_data);
                    echo $Model->errortip.$dataOne['student_branch']."<br />";
                }
                $datanums++;
            }
        }
    }

    //管理费
    function UpdateMangepriceView(){
        $Model = new \Model\SchoolOldModel('8888');
        $dataArray=$this->DataControl->selectClear("select * from smc_excel_studentmanagebalance where excel_updatatype='0' and school_branch='01425'");
        if($dataArray){
            foreach($dataArray as $dataOne){
                $modelBak = $Model->UpdataStudentMange($dataOne['school_branch'],$dataOne['class_branch'],$dataOne['student_branch'],$dataOne['excel_code'],$dataOne['studentmanagebalance_figure'],$dataOne['manage_type'],$dataOne['manage_month']);
                if($modelBak){
                    $excel_data=array();
                    $excel_data['excel_updatatype']='1';
                    $this->DataControl->updateData("smc_excel_studentmanagebalance","excel_code='{$dataOne['excel_code']}'",$excel_data);
                    echo $Model->oktip."<br />";
                }else{
                    $excel_data=array();
                    $excel_data['excel_updatatype']='-1';
                    $this->DataControl->updateData("smc_excel_studentmanagebalance","excel_code='{$dataOne['excel_code']}'",$excel_data);
                    echo $Model->errortip.$dataOne['order_pid']."<br />";
                }
            }
        }
    }

    //预收余额表
    function UpdateCatbalanceView(){
        $Model = new \Model\SchoolOldModel('8888');
        $dataArray=$this->DataControl->selectClear("select * from smc_excel_studentcatbalance where excel_updatatype='0' and school_branch='01425'");
        if($dataArray){
            foreach($dataArray as $dataOne){
                $modelBak = $Model->UpdateAdvance($dataOne['school_branch'],$dataOne['student_branch'],$dataOne['feetype_code'],$dataOne['coursecat_branch'],$dataOne['coursecatbalance_figure'],$dataOne['coursecat_time']);
                if($modelBak){
                    $excel_data=array();
                    $excel_data['excel_updatatype']='1';
                    $this->DataControl->updateData("smc_excel_studentcatbalance","excel_code='{$dataOne['excel_code']}'",$excel_data);
                    echo $Model->oktip."<br />";
                }else{
                    $excel_data=array();
                    $excel_data['excel_updatatype']='-1';
                    $this->DataControl->updateData("smc_excel_studentcatbalance","excel_code='{$dataOne['excel_code']}'",$excel_data);
                    echo $Model->errortip.$dataOne['order_pid']."<br />";
                }
            }
        }
    }

    //续费政策归属表
    function UpdatePolicyView(){
//        AND s.student_branch < '2018070300001'

//        AND
//        s.student_branch >= '2018070300001'
//        AND s.student_branch < '2019060300001'
//        ('847','665','662','670','650','669','663','676','1126')
        $studentArray = $this->DataControl->selectClear("SELECT
                s.student_id,s.student_branch
            FROM
                smc_student AS s
            WHERE
                s.student_id IN (
                    SELECT
                        e.student_id
                    FROM
                        smc_student_enrolled AS e
                    WHERE
                        e.school_id ='847'
                )
            AND s.student_branch >= '2018070300001'
            AND s.student_branch < '2019060300001'
            AND (
                s.student_id IN (
                    SELECT
                        e.student_id
                    FROM
                        smc_student_coursebalance AS e
                    WHERE
                        e.school_id ='847'
                )
                OR s.student_id IN (
                    SELECT
                        e.student_id
                    FROM
                        smc_student_coursecatbalance AS e
                    WHERE
                        e.school_id ='847' AND e.feetype_code = 'Times' AND e.coursecat_branch IN ('AE','J','S','K')
                )
            )");

        if($studentArray){
            foreach($studentArray as $studentOne){
                if(!$this->DataControl->getFieldOne("smc_fee_policy_student","policy_id","policy_id='57' and student_id='{$studentOne['student_id']}'")){
                    $data = array();
                    $data['policy_id'] = '57';
                    $data['student_id'] = $studentOne['student_id'];
                    $this->DataControl->insertData("smc_fee_policy_student", $data);
                }
            }
        }
    }

    function ImportExceldemoView()
    {

        $excelArray = format_exceltoarray("exceldemo.xlsx");

        $lineArray = $excelArray[0];
        array_shift($excelArray);
        $columnArray = $excelArray;
        $demoArray = array();

//        var_dump($columnArray);exit;
        foreach ($columnArray as $columnOne) {
            $demoOne = array();
            $demoOne['from_course_branch'] = $columnOne['A'];
            foreach ($columnOne as $ckey => $clineOne) {
                if ($ckey !== 'A') {
                    $demoOne['to_course_branch'] = $lineArray[$ckey];
                    $demoOne['value'] = $clineOne;
                    $demoArray[] = $demoOne;
                }
            }
        }
        foreach ($demoArray as $demoOne) {
            if($demoOne['value']!=''){
                if(trim($demoOne['from_course_branch'])=='PJ'){
                    $demoOne['from_course_branch']='KJ01';
                }
                if(trim($demoOne['to_course_branch'])=='PJ'){
                    $demoOne['to_course_branch']='KJ01';
                }

                $demoOne['from_course_branch']=trim($demoOne['from_course_branch']);
                $demoOne['to_course_branch']=trim($demoOne['to_course_branch']);
                $demoOne['value']=trim($demoOne['value']);


                $from = $this->DataControl->getFieldOne("smc_course", "course_id", "course_branch='{$demoOne['from_course_branch']}' and company_id='8888'");
                $to = $this->DataControl->getFieldOne("smc_course", "course_id", "course_branch='{$demoOne['to_course_branch']}' and company_id='8888'");
                if ($from && $to) {
                    $data = array();
                    $data['company_id'] = '8888';
                    $data['policy_id'] = '57';
                    $data['from_course_id'] = $from['course_id'];
                    $data['from_course_branch'] = $demoOne['from_course_branch'];
                    $data['to_course_id'] = $to['course_id'];
                    $data['to_course_branch'] = $demoOne['to_course_branch'];
                    $data['sell_unitprice'] = $demoOne['value'];

                    if ($this->DataControl->getFieldOne("smc_fee_policy_course", "company_id", "policy_id='57' and from_course_id = '{$from['course_id']}' and to_course_id = '{$to['course_id']}' and company_id='8888'")) {

                        $this->DataControl->updateData("smc_fee_policy_course", "policy_id='57' and from_course_id = '{$from['course_id']}' and to_course_id = '{$to['course_id']}' and company_id='8888'", $data);
                        echo "跟新成功";
                    } else {
                        $this->DataControl->insertData("smc_fee_policy_course", $data);
                        echo "导入成功";
                    }
                } else {
                    if(!$from){
                        echo "导入失败,无对应课程别" . $demoOne['from_course_branch']."<br />";
                    }else{
                        echo "导入失败,无对应课程别" . $demoOne['to_course_branch']."<br />";
                    }
                }
            }
        }
    }

    function updatePayTimeView(){

        $Model = new \Model\SchoolOldModel('8888');

        $modelBak = $Model->UpdatePayTime();
    }

    function createOrderPid($initial){
        $Str = "0123456789";
        $rangtr = $Str[rand(0,9)].$Str[rand(0,9)];
        $rangtime = date("ymd",time());
        $rangnum = rand(100,999);
        $OrderPID = $initial.$rangtr.$rangtime.$rangnum;
        return $OrderPID;
    }

    //生成体验券
    function CreateFreevoucherView()
    {
        for ($i = 1; $i <= 310; $i++) {
            do {
                $freevoucher_pid = $this->createOrderPid('TY');
            } while ($this->DataControl->selectOne("select freevoucher_id from crm_sell_activity_freevoucher where freevoucher_pid='{$freevoucher_pid}' and activity_id = '353' limit 0,1"));
            $data = array();
            $data['company_id'] = '8888';
            $data['activity_id'] = '353';
            $data['freevoucher_pid'] = $freevoucher_pid;
            $data['freevoucher_name'] = "招行优惠公开课体验券";
            $data['freevoucher_exittime'] = "2019-08-31";
            $data['freevoucher_content'] = "";
            $data['freevoucher_createtime'] = time();
            if ($this->DataControl->insertData('crm_sell_activity_freevoucher', $data)) {
                echo $freevoucher_pid;
            } else {
                return false;
            }
        }
    }

}