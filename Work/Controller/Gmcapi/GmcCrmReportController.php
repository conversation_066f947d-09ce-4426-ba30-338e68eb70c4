<?php

namespace Work\Controller\Gmcapi;


class GmcCrmReportController extends viewTpl
{
    public $u;
    public $t;
    public $c;
    public $visitType = "api";
    public $error;
    public $errortip;
    public $company_isassist = 0;

    //预加载处理类
    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
    }


    //本地权限校验入口
    function ThisVerify($request)
    {

        if (!intval($request['staffer_id'])) {
            $res = array('error' => '1', 'errortip' => "未检测到招生人员信息，请重新登录操作", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        if (empty($request['token'])) {
            $res = array('error' => '1', 'errortip' => "未传入用户Token信息", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        if (isset($request['company_id']) && $request['company_id'] !== '') {
            $companyOne = $this->DataControl->getFieldOne("gmc_company", "company_isassist", "company_id='{$request['company_id']}'");
            $this->company_isassist = $companyOne['company_isassist'];
        }
        $paramArray = array();
        $paramArray['staffer_id'] = $request['staffer_id'];
        $paramArray['company_id'] = $request['company_id'];
        $paramArray['token'] = $request['token'];
        if (!$this->UserLimit($paramArray)) {
            $result = array();
            $result["list"] = array();
            $result["tokeninc"] = "0";
            $res = array('error' => '1', 'errortip' => "用户token失效", 'result' => $result);
            ajax_return($res, $request['language_type']);
        }
    }

    //渠道版块招生统计表
    function channelSectionView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Report\Gmc\GmcCrmReportModel($request);
        $datalist = $Model->channelSectionReport($request);

        $k = 0;
        $field = array();
        $field[$k]["fieldstring"] = "section_name";
        $field[$k]["fieldname"] = "渠道版块名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "channel_num";
        $field[$k]["fieldname"] = "渠道明细数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "client_newaddnums";
        $field[$k]["fieldname"] = "新增毛名单";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "client_newvalidnums";
        $field[$k]["fieldname"] = "新增有效名单";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "client_validrate";
        $field[$k]["fieldname"] = "新增有效率";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "client_unvalidnums";
        $field[$k]["fieldname"] = "新增无意向名单数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "inv_aud_num";
        $field[$k]["fieldname"] = "邀约名单数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "inv_aud_arrivenum";
        $field[$k]["fieldname"] = "到访名单数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "inv_aud_rate";
        $field[$k]["fieldname"] = "到访率";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "invite_num";
        $field[$k]["fieldname"] = "柜询邀约数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "invite_arrivenum";
        $field[$k]["fieldname"] = "柜询邀约到访数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "OH_audition_num";
        $field[$k]["fieldname"] = "OH邀约名单数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "OH_audition_arrivenum";
        $field[$k]["fieldname"] = "OH邀约到访数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "Class_audition_num";
        $field[$k]["fieldname"] = "插班试听邀约数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "Class_audition_arrivenum";
        $field[$k]["fieldname"] = "插班试听邀约到访数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "positivenum";
        $field[$k]["fieldname"] = "报名名单数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "registernum";
        $field[$k]["fieldname"] = "实缴名单";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;


        $result = array();
        $result['field'] = $field;
        $result['list'] = $datalist['list'] == false ? array() : $datalist['list'];
        $result['list'] = $datalist['list'] == false ? array() : $datalist['list'];
        $allnum = $datalist['allnums'] == false ? 0 : $datalist['allnums'];
        $result['allnum'] = $allnum;


        if ($result['list']) {
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result, "allnum" => $allnum);
        } else {
            $res = array('error' => '1', 'errortip' => "暂无渠道业绩统计报表记录", 'result' => $result, "allnum" => $allnum);
        }
        ajax_return($res, $request['language_type']);

    }

    //渠道招生统计
    function channelClientReportView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Report\Gmc\GmcCrmReportModel($request);
        $datalist = $Model->channelClientReport($request);

        $k = 0;
        $field = array();
        $field[$k]["fieldstring"] = "frommedia_name";
        $field[$k]["fieldname"] = "渠道类型名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "channel_num";
        $field[$k]["fieldname"] = "渠道明细数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "client_newaddnums";
        $field[$k]["fieldname"] = "新增毛名单";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "client_newvalidnums";
        $field[$k]["fieldname"] = "新增有效名单";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "client_validrate";
        $field[$k]["fieldname"] = "新增有效率";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "client_unvalidnums";
        $field[$k]["fieldname"] = "新增无意向名单数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "inv_aud_num";
        $field[$k]["fieldname"] = "邀约名单数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "inv_aud_arrivenum";
        $field[$k]["fieldname"] = "到访名单数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "inv_aud_rate";
        $field[$k]["fieldname"] = "到访率";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "invite_num";
        $field[$k]["fieldname"] = "柜询邀约名单数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "invite_arrivenum";
        $field[$k]["fieldname"] = "柜询邀约到访名单数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "OH_audition_num";
        $field[$k]["fieldname"] = "OH邀约名单数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "OH_audition_arrivenum";
        $field[$k]["fieldname"] = "OH邀约到访名单数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "Class_audition_num";
        $field[$k]["fieldname"] = "插班试听邀约名单数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "Class_audition_arrivenum";
        $field[$k]["fieldname"] = "插班试听邀约到访名单数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "positivenum";
        $field[$k]["fieldname"] = "报名名单数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "registernum";
        $field[$k]["fieldname"] = "实缴名单";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;


        $result = array();
        $result['field'] = $field;
        $result['list'] = $datalist['list'] == false ? array() : $datalist['list'];
        $result['list'] = $datalist['list'] == false ? array() : $datalist['list'];
        $allnum = $datalist['allnums'] == false ? 0 : $datalist['allnums'];
        $result['allnum'] = $allnum;


        if ($result['list']) {
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result, "allnum" => $allnum);
        } else {
            $res = array('error' => '1', 'errortip' => "暂无渠道业绩统计报表记录", 'result' => $result, "allnum" => $allnum);
        }
        ajax_return($res, $request['language_type']);

    }

    //校招生统计报表
    //第N版 +1
    function schoolErollClientView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户marketer_num
        $Model = new \Model\Report\Gmc\GmcCrmReportModel($request);
        $datalist = $Model->schoolErollClient($request);

        $k = 0;
        $field = array();
        $field[$k]["fieldname"] = "省份";
        $field[$k]["fieldstring"] = "province_name";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "所属区域";
        $field[$k]["fieldstring"] = "district_cnname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "新增毛名单数";
        $field[$k]["fieldstring"] = "client_maonum";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "新增有效名单数";
        $field[$k]["fieldstring"] = "client_validnum";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "新增跟踪人数";
        $field[$k]["fieldstring"] = "client_tracknum";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "新增跟踪人次";
        $field[$k]["fieldstring"] = "school_track_num";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "新增电话沟通人次";
        $field[$k]["fieldstring"] = "track_moblie_num";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "新增邀约名单数";
        $field[$k]["fieldstring"] = "inv_aud_num";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "新增到访名单数";
        $field[$k]["fieldstring"] = "inv_aud_arrivenum";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "新增柜询邀约名单数";
        $field[$k]["fieldstring"] = "invite_num";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "新增柜询邀约到访名单数";
        $field[$k]["fieldstring"] = "invite_arrivenum";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "新增OH邀约名单数";
        $field[$k]["fieldstring"] = "OH_client_num";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "新增OH邀约到访名单数";
        $field[$k]["fieldstring"] = "OH_client_arrive_num";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "新增插班邀约名单数";
        $field[$k]["fieldstring"] = "Class_client_num";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "新增插班邀约到访名单数";
        $field[$k]["fieldstring"] = "Class_client_arrivenum";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "新增累计报名人数";
        $field[$k]["fieldstring"] = "positive_num";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "新增外部招生人数";
        $field[$k]["fieldstring"] = "client_outnum";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "新增内部招生人数";
        $field[$k]["fieldstring"] = "client_innernum";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "新增专案招生人数";
        $field[$k]["fieldstring"] = "client_casenum";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "当前待分配名单数";
        $field[$k]["fieldstring"] = "client_noprincipal_num";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "当前待跟踪名单数";
        $field[$k]["fieldstring"] = "client_notrack_num";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "0跟踪记录名单数";
        $field[$k]["fieldstring"] = "client_zerotrack_num";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "当前招生老师数";
        $field[$k]["fieldstring"] = "marketer_num";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result['field'] = $field;
        $result['list'] = $datalist['list'] == false ? array() : $datalist['list'];
        $result['list'] = $datalist['list'] == false ? array() : $datalist['list'];
        $allnum = $datalist['allnums'] == false ? 0 : $datalist['allnums'];
        $result['allnum'] = $allnum;

        if ($result['list']) {
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result, "allnum" => $allnum);
        } else {
            $res = array('error' => '1', 'errortip' => "暂无校区招生统计表记录", 'result' => $result, "allnum" => $allnum);
        }
        ajax_return($res, $request['language_type']);

    }


    //招生业绩统计报表
    function marketerClientReportView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Report\Gmc\GmcCrmReportModel($request);
        $datalist = $Model->marketerClientReport($request);
        $fieldname = array('省份', '校区名称', '校区编号', '教师中文名', '教师英文名', '职务名称', '教师编号', '新增分配名单', '有效跟踪人数', ' 有效跟踪人次', '邀约名单数', '到访名单数', '柜询邀约名单数', '柜询邀约到访名单数', 'OH邀约名单数', "OH邀约到访名单数", "插班试听邀约数", "插班试听邀约到访数", "报名人数");

        $fieldstring = array('province_name', 'school_cnname', 'school_branch', 'marketer_name', 'staffer_enname', 'post_name', 'staffer_branch', 'princ_clientnum', 'track_client_num', 'client_track_num', 'inv_aud_num', 'inv_aud_arrivenum', 'invite_num', 'invite_arrivenum', 'OH_audtion_num', 'OH_audtion_arrivenum', 'Class_audtion_num', 'Class_audtion_arrivenum', 'register_num');
        $fieldcustom = array('1', '1', "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1');
        $fieldshow = array('1', '1', "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1');

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["fieldname"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['field'] = $field;
        $result['list'] = $datalist['list'] == false ? array() : $datalist['list'];
        $allnum = $datalist['allnums'] == false ? 0 : $datalist['allnums'];
        $result['allnum'] = $allnum;
        if ($result['list']) {
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result, "allnum" => $allnum);
        } else {
            $res = array('error' => '1', 'errortip' => "暂无校招生咨询报表信息", 'result' => $result, "allnum" => $allnum);
        }
        ajax_return($res, $request['language_type']);

    }


    //招生业绩明细报表
    function enrollPerDetailsView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Report\Gmc\GmcCrmReportModel($request);
        $datalist = $Model->enrollPerDetails($request);
        $k = 0;
        $field = array();
        $field[$k]["fieldstring"] = "province_name";
        $field[$k]["fieldname"] = "省份";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "district_cnname";
        $field[$k]["fieldname"] = "所属区域";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_branch";
        $field[$k]["fieldname"] = "学员编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["fieldname"] = "学员姓名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "coursetype_cnname";
        $field[$k]["fieldname"] = "班组名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "coursecat_cnname";
        $field[$k]["fieldname"] = "班种名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "client_mobile";
        $field[$k]["fieldname"] = "联系手机";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "zhu_marketer_name";
        $field[$k]["fieldname"] = "主负责人";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "school_post_name";
        $field[$k]["fieldname"] = "主负责人职务";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "pay_price";
        $field[$k]["fieldname"] = "首缴金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "pay_successtime";
        $field[$k]["fieldname"] = "报名日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "section_name";
        $field[$k]["fieldname"] = "渠道版块";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "channel_sourcename";
        $field[$k]["fieldname"] = "渠道类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "channel_name";
        $field[$k]["fieldname"] = "渠道明细";
        $field[$k]["show"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "promotion_jobnumber";
        $field[$k]["fieldname"] = "地推工号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "post_name";
        $field[$k]["fieldname"] = "职务";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "client_frompage";
        $field[$k]["fieldname"] = "接触点";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "client_createtime";
        $field[$k]["fieldname"] = "名单创建时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;


        $result = array();
        $result['field'] = $field;
        $result['list'] = $datalist['list'] == false ? array() : $datalist['list'];
        $allnum = $datalist['allnums'] == false ? 0 : $datalist['allnums'];
        $result['allnum'] = $allnum;

        if ($result['list']) {
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result, "allnum" => $allnum);
        } else {
            $res = array('error' => '1', 'errortip' => "暂无校招生咨询报表信息", 'result' => $result, "allnum" => $allnum);
        }
        ajax_return($res, $request['language_type']);
    }


    //招生业绩咨询报表
    function companyClientRerportView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Report\Gmc\GmcCrmReportModel($request);
        $datalist = $Model->companyClientRerport($request);

        $fieldname = array('province_name', 'school_cnname', 'school_branch', 'client_id', 'client_cnname', 'client_enname', 'client_sex', 'client_createtime', 'client_tracestatus', 'client_source', 'client_intention_level', 'marketer_name', 'track_note', 'track_createtime', 'track_num');
        $fieldstring = array('省份', '校区名称', '校区编号', '咨询编号', '中文名', '英文名', '性别', '创建日期', '跟踪状态', '招生渠道类型', '意向星级', '最后跟踪教师', '最后跟踪内容', '最后跟踪时间', '跟踪人次');
        $fieldcustom = array('1', '1', "1", "0", "1", "1", "1", "1", "1", "1", "1", '1', '1', '1', '1', '1', '1', '1', '1');
        $fieldshow = array('1', '1', "1", "0", "1", "1", "1", "1", "1", "1", "1", '1', '1', '1', '1', '1', '1', '1', '1');

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        if ($datalist === false) {
            $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array(), "allnum" => 0);
            ajax_return($res, $request['language_type']);
        }

        $result = array();
        $result['field'] = $field;
        $result['list'] = $datalist['list'] == false ? array() : $datalist['list'];
        $allnum = $datalist['allnums'] == false ? 0 : $datalist['allnums'];
        $result['allnum'] = $allnum;

        if ($result['list']) {
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result, "allnum" => $allnum);
        } else {
            $res = array('error' => '1', 'errortip' => "暂无校招生咨询报表信息", 'result' => $result, "allnum" => $allnum);
        }
        ajax_return($res, $request['language_type']);
    }


    //集团活动统计报表
    function companyActivityView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Report\Gmc\GmcCrmReportModel($request);
        $datalist = $Model->companyActivity($request);

        $fieldstring = array('活动名称', "招生渠道类型", '招生渠道明细', '适配学校数', '新增毛名单', '新增邀约数', '新增邀约到访数', '新增OH邀约名单数', '新增OH邀约到访名单数', '新增邀约报名数', '累计毛名单', '累计有效名单数', '累计报名数', '累计转化率');
        $fieldname = array('activity_name', 'frommedia_name', 'channel_name', 'applysch_num', 'client_maoallnum', 'inv_aud_num', 'inv_aud_arrivenum', 'OH_client_num', 'OH_client_arrivenum', 'inv_aud_positivenum', 'client_leijimaoallnum', 'client_allnum', 'conversion_num', 'conversion_rate');
        $fieldcustom = array('1', "1", '1', "1", "1", "1", "1", "1", "1", "1", "1", '1', '1', '1', '1', '1', '1', '1', '1', '1');
        $fieldshow = array('1', "1", '1', "1", "1", "1", "1", "1", "1", "1", "1", '1', '1', '1', '1', '1', '1', '1', '1', '1');

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['field'] = $field;
        $result['list'] = $datalist['list'] == false ? array() : $datalist['list'];
        $allnum = $datalist['allnums'] == false ? 0 : $datalist['allnums'];
        $result['allnum'] = $allnum;

        if ($result['list']) {
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result, "allnum" => $allnum);
        } else {
            $res = array('error' => '1', 'errortip' => "暂无校招生咨询报表信息", 'result' => $result, "allnum" => $allnum);
        }
        ajax_return($res, $request['language_type']);
    }

    //活动业绩统计报表
    function ActivityEnrollView()
    {

        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Report\Gmc\GmcCrmReportModel($request);
        $datalist = $Model->ActivityEnroll($request);
        $fieldstring = array('省份', '校区名称', '校区编号', '活动名称', '新增毛名单', '新增有效名单', '新增无意向名单', '新增到访名单数', '累计学员报名数', '累计转化率');
        $fieldname = array('province_name', 'school_cnname', 'school_branch', 'activity_name', 'client_maonum', 'client_validnum', 'client_nointentnum', 'client_invitenum', 'client_bookingnum', 'conv_rate');

        $fieldcustom = array('1', '1', "1", "1", "1", "1", "1", "1", "1", "1");
        $fieldshow = array('1', '1', "1", "1", "1", "1", "1", "1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['field'] = $field;
        $result['list'] = $datalist['list'] == false ? array() : $datalist['list'];
        $allnum = $datalist['allnums'] == false ? 0 : $datalist['allnums'];
        $result['allnum'] = $allnum;

        if ($result['list']) {
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result, "allnum" => $allnum);
        } else {
            $res = array('error' => '1', 'errortip' => "暂无活动业绩统计报表", 'result' => $result, "allnum" => $allnum);
        }
        ajax_return($res, $request['language_type']);
    }


    function channelInfoReportView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Report\Gmc\GmcCrmReportModel($request);
        $datalist = $Model->channelInfoReport($request);

        $fieldname = array('招生渠道明细', '所属渠道类型', '渠道版块', '新增毛名单', "新增有效名单", "名单有效率", "新增无意向名单", "邀约名单数", "到访名单数", "到访率", "柜询邀约名单数", "柜询邀约到访名单数", "OH邀约名单数", "OH邀约到访名单数", "插班试听邀约名单数", "插班试听邀约到访名单数", "报名名单数");

        $fieldstring = array('channel_name', 'channel_medianame', 'section_name', 'client_allnum', 'client_allvalidnum', 'client_allvalid_rate', 'client_novalidnum', 'inv_aud_num', 'inv_aud_arrivenum', 'inv_aud_arriverate', 'inv_num', 'inv_arrivenum', 'OH_audition_num', 'OH_audition_arrivenum', 'Class_audition_num', 'Class_audition_arrivenum', 'qujian_conversion_num');

        $fieldcustom = array('1', "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1');
        $fieldshow = array('1', "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1');

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["fieldname"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['field'] = $field;
        $result['list'] = $datalist['list'] == false ? array() : $datalist['list'];
        $result['list'] = $datalist['list'] == false ? array() : $datalist['list'];
        $allnum = $datalist['allnum'] == false ? 0 : $datalist['allnum'];
        $result['allnum'] = $allnum;

        if ($result['list']) {
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result, "allnum" => $allnum);
        } else {
            $res = array('error' => '1', 'errortip' => "暂无渠道明细分析报表", 'result' => $result, "allnum" => $allnum);
        }
        ajax_return($res, $request['language_type']);

    }


    function schoolChannelReportView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Report\Gmc\GmcCrmReportModel($request);
        $datalist = $Model->schoolChannelReport($request);

        $fieldname = array('省份', '校区名称', '校区编号', '所属区域', '渠道明细', '渠道类型', '渠道版块', '新增毛名单', '新增有效名单', '新增有效率', '新增无意向名单', "邀约名单数", "到访名单数", '到访率', '报名名单数', '累计招新人数');
        $fieldstring = array('province_name', 'school_cnname', 'school_branch', 'district_cnname', 'channel_name', 'channel_medianame', 'section_name', 'client_num', 'client_validnum', 'client_validrate', 'client_novalidnum', 'inv_aud_clientnum', 'inv_aud_arriveclientnum', 'inv_aud_arriverate', 'register_num', 'crm_newnum');

        $fieldcustom = array('1', '1', '1', '1', "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", '1', '1', '1', '1', '1', '1', '1');
        $fieldshow = array('1', '1', '1', '1', "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", '1', '1', '1', '1', '1', '1', '1');

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldname[$i]);
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['field'] = $field;
        $result['list'] = $datalist['list'] == false ? array() : $datalist['list'];
        $result['list'] = $datalist['list'] == false ? array() : $datalist['list'];
        $allnum = $datalist['allnum'] == false ? 0 : $datalist['allnum'];
        $result['allnum'] = $allnum;

        if ($result['list']) {
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result, "allnum" => $allnum);
        } else {
            $res = array('error' => '1', 'errortip' => $Model->errortip == "请先选择学校" ? "请先选择学校" : "暂无渠道明细分析报表", 'result' => $result, "allnum" => $allnum);
        }
        ajax_return($res, $request['language_type']);
    }


    /**
     * 董 需要的招生报表
     * author: ling
     * 对应接口文档 0001
     */
    function getClientHighestReportView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Report\Gmc\GmcCrmReportModel($request);
        $datalist = $Model->getClientHighestReport($request);

        $fieldname = array('省份', '校区名称', '校区编号', '所属区域', '在籍人数(当前)', '在读人数', '专案免费在读人数', "报名/去年", "专案报名/去年", "报名/当年"
            //, '20-06至今总招生','20-06至今内招','21秋季内招新生','21秋季外招新生'
            //,'自然周报名（定金）'
        , '报名/周', '报名/月', '内招/月', '外招/月', '新增毛名单/月', '新增毛名单/周', '新增线下名单/周', '新增线下名单/月', '新增线上名单/周', '新增线上名单/月', '新增名单/季',
            '推荐名单/月', '6个月内有效名单', '总有效名单', '待分配名单数', '追踪名单数/周', '追踪人次/周', "电询/周", "电询/月", '未确认邀约数/月', '柜询/周',
            '柜询/月', '邀约试读数/当月', 'OH邀约/当周', '插班邀约/当周', 'OH邀约/上周', 'OH邀约到访/上周', 'OH邀约/自然周', 'OH邀约/自然月', "OH到访/自然周", "OH到访/自然月", "试听/周",
            "试听/月", "OH转正数/月", "OH转正率/月", '内招单数/月', '外招名单数/月');
        $fieldstring = array('province_name', 'school_shortname', 'school_branch', 'school_tagbak', 'absenteenums', 'readingnums', 'feereadingnums', 'prelog_year_num', 'prelog_caseyear_num', 'nowlog_year_num'
            //, 'positivelog_history_num','indoor_history_num','autumn_positivelog_history_num','autumn_indoor_history_num'
            //,'trading_week_num'
        , 'positivelog_week_num', 'positivelog_month_num', 'inmonth_num', 'outmonth_num', 'mao_month_client_num', 'mao_week_client_num', 'client_under_weeknum', 'client_under_monthnum', 'client_up_weeknum', 'client_up_monthnum', 'client_up_quarternum',
            'recomend_clientnum', 'sixmoth_clientnum', 'client_all_num', 'client_noallot_num', 'track_client_num', 'track_tracknum', 'track_week_tephonenum', 'track_month_tephonenum', 'no_confirm', 'invite_week_num',
            'invite_month_num', 'audition_thismonth_num', 'ohaudition_curweek_num', 'jmcaudition_curweek_num', 'ohaudition_lastweek_num', 'ohaudition_lastweek_postnum', 'ohaudition_week_num', 'ohaudition_month_num', 'ohaudition_week_arrnum', 'ohaudition_month_arrnum', 'audition_week_num',
            'audition_month_num', 'ohpostive_month_arrnum', 'ohpostive_month_rate', 'client_innernum', 'client_outnum');

        $fieldcustom = array('1', '1', '1', '1', '1', "1", "1", "1", "1", "1", "1", "1",
            "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1",
            "1", "1", "1", "1", '1', '1', '1', '1', '1', '1',
            '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1',
            '1', '1', '1', '1', '1', '1');
        $fieldshow = array('1', '1', '1', '1', '1', "1", "1", "1", "1", "1", "1", "1",
            "1", "1", "1", "1", "1", "0", "1", "1", "1", "1", "1",
            "1", "1", "1", "1", '1', '1', '1', '1', '1', '1',
            '1', '0', '1', '1', '1', '1', '1', '1', '1', '1', '1',
            '1', '1', '1', '0', '0', '0');

        $field = array();
        $field_array = array('positivelog_week_num', 'positivelog_month_num', 'client_under_weeknum', 'client_under_monthnum', 'client_up_weeknum', 'client_up_monthnum', 'client_up_quarternum', 'ohaudition_week_num', 'ohaudition_month_num', 'ohaudition_week_arrnum', 'ohaudition_month_arrnum', 'audition_week_num', 'audition_month_num', 'ohpostive_month_arrnum');
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["fieldname"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
            if (in_array($field[$i]["fieldstring"], $field_array)) {
                $field[$i]["ismethod"] = 1;
                $field[$i]["table_name"] = $field[$i]['fieldname'] . '统计表';
            }
        }

        $result = array();
        $result['field'] = $field;
        $result['list'] = $datalist['list'] == false ? array() : $datalist['list'];
        $allnum = $datalist['allnum'] == false ? 0 : $datalist['allnum'];
        $result['allnum'] = $allnum;

        if ($result['list']) {
            $res = array('error' => '0', 'errortip' => "获取招生追踪大表成功", 'result' => $result, "allnum" => $allnum);
        } else {
            $res = array('error' => '1', 'errortip' => "暂无记录", 'result' => $result, "allnum" => $allnum);
        }
        ajax_return($res, $request['language_type']);
    }

    //董 需要的招生报表  -----  学校这边提的修改
    function getClientRecruitReportView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Report\Gmc\GmcCrmReportModel($request);
        $datalist = $Model->getClientRecruitReport($request);


        $fieldname = array("地区", "校区名称", "督导区",
            "报名/去年",
            "报名/当年", "报名/周", "报名/月",
            "新增总毛名单/月",
            "新增陆军有效名单/月", "新增空军有效名单/月",
            "新增转介绍名单/月", "转介绍名单报名/月",
            "待分配名单数" );
        $fieldstring = array("school_districtname", "school_shortname", "school_tagbak",
            'prelog_year_num',
            "nowlog_year_num", "positivelog_week_num", "positivelog_month_num",
            "mao_month_client_num",
            "client_under_monthnum","client_up_monthnum",
            "client_referral_monthnum", "client_referral_regmonthnum"
        , "client_noallot_num" );

        $fieldcustom = array('1', '1', '1',
            "1",
            '1', '1', '1',
            '1',
            "1", "1",
            '1', '1',
            '1' );
        $fieldshow = array('1', '1', '1',
            "1",
            '1', '1', '1',
            '1',
            "1", "1",
            '1', '1',
            '1'  );
//        $fieldname = array("地区", "校区名称", "督导区", "在籍", "在读",
//            "报名/去年",
//            "报名/当年", "报名/周", "报名/月",
//            "新增总毛名单/月",
//            "新增陆军有效名单/月", "新增空军有效名单/月", "新增有效名单/月（空+陆）", "新增有效名单/季",
//            "新增转介绍名单/月", "转介绍名单报名/月",
//
//            "园内招（名单/月）", "园内招（报名/月）",
//            "可追踪有效名单", "待分配名单数",
//            "追踪人数/月",
//            "邀约诺访/月", "邀约到访/月");
//        $fieldstring = array("school_districtname", "school_shortname", "school_tagbak", "absenteenums", "readingnums",
//            'prelog_year_num',
//            "nowlog_year_num", "positivelog_week_num", "positivelog_month_num",
//             "mao_month_client_num",
//            "client_under_monthnum","client_up_monthnum", "client_upunder_monthnum", "client_up_quarternum",
//            "client_referral_monthnum", "client_referral_regmonthnum",
//
//            "register_client_monthnum", "info_month_num",
//            "client_all_num", "client_noallot_num",
//            "track_client_num",
//            "invitelog_month_num", "invitelog_month_isvisitnum");
//
//        $fieldcustom = array('1', '1', '1', '1', '1',
//            "1",
//            '1', '1', '1',
//            '1',
//            "1", "1", "1", "1",
//            '1', '1',
//            '1', '1',
//            "1", '1',
//            '1',
//            '1', '1');
//        $fieldshow = array('1', '1', '1', '1', '1',
//            "1",
//            '1', '1', '1',
//            '1',
//            "1", "1", "1", "1",
//            '1', '1',
//            '1', '1',
//            "1", '1',
//            '1',
//            '1', '1');

        $field = array();
//        $field_array = array('positivelog_week_num', 'positivelog_month_num', 'client_under_weeknum', 'client_under_monthnum', 'client_up_weeknum', 'client_up_monthnum', 'client_up_quarternum', 'ohaudition_week_num', 'ohaudition_month_num', 'ohaudition_week_arrnum', 'ohaudition_month_arrnum', 'audition_week_num', 'audition_month_num', 'ohpostive_month_arrnum');
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["fieldname"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
//            if (in_array($field[$i]["fieldstring"], $field_array)) {
//                $field[$i]["ismethod"] = 1;
//                $field[$i]["table_name"] = $field[$i]['fieldname'] . '统计表';
//            }
        }

        $result = array();
        $result['field'] = $field;
        $result['list'] = $datalist['list'] == false ? array() : $datalist['list'];
        $allnum = $datalist['allnum'] == false ? 0 : $datalist['allnum'];
        $result['allnum'] = $allnum;

        if ($result['list']) {
            $res = array('error' => '0', 'errortip' => "获取校区招生追踪大表成功", 'result' => $result, "allnum" => $allnum);
        } else {
            $res = array('error' => '1', 'errortip' => "暂无记录", 'result' => $result, "allnum" => $allnum);
        }
        ajax_return($res, $request['language_type']);
    }

    /**
     * 需要传入对应的字段,根据字段进行对应的查询数据详情
     * author: ling
     * 对应接口文档 0001
     */
    function getReportClientListApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Report\Gmc\GmcCrmReportModel($request);
        $result = $Model->getReportClientList($request);
        $allnum = 0;
        $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result, "allnum" => $allnum);
        ajax_return($res, $request['language_type']);
    }

    //渠道招生明细
    function detailsByChannelView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Report\Gmc\GmcCrmReportModel($request);
        $res = $ReportModel->detailsByChannel($request);

        $k = 0;
        $field = array();
        $field[$k]["fieldstring"] = "province_name";
        $field[$k]["fieldname"] = "省份";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "district_cnname";
        $field[$k]["fieldname"] = "所属区域";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "client_cnname";
        $field[$k]["fieldname"] = "中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "client_enname";
        $field[$k]["fieldname"] = "英文名";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "client_sex";
        $field[$k]["fieldname"] = "性别";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "client_age";
        $field[$k]["fieldname"] = "年龄";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "parenter_cnname";
        $field[$k]["fieldname"] = "主要联系人";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "client_mobile";
        $field[$k]["fieldname"] = "联系手机";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "client_tracestatus";
        $field[$k]["fieldname"] = "客户状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "client_answerphone";
        $field[$k]["fieldname"] = "接通状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "track_createtime";
        $field[$k]["fieldname"] = "最后跟踪时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "track_note";
        $field[$k]["fieldname"] = "跟踪明细";
        $field[$k]["show"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "client_intention_level";
        $field[$k]["fieldname"] = "意向星级";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["islevel"] = true;

        $k++;
        $field[$k]["fieldstring"] = "track_num";
        $field[$k]["fieldname"] = "跟踪次数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "client_intention_maxlevel";
        $field[$k]["fieldname"] = "最高意向星级";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["islevel"] = true;
        $k++;
        $field[$k]["fieldstring"] = "client_source";
        $field[$k]["fieldname"] = "招生渠道类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "channel_name";
        $field[$k]["fieldname"] = "招生渠道明细";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "section_name";
        $field[$k]["fieldname"] = "渠道板块";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "activity_name";
        $field[$k]["fieldname"] = "招生活动";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "promotion_jobnumber";
        $field[$k]["fieldname"] = "地推工号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "post_name";
        $field[$k]["fieldname"] = "职务";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "client_frompage";
        $field[$k]["fieldname"] = "接触点";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "client_createtime";
        $field[$k]["fieldname"] = "创建时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "client_updatetime";
        $field[$k]["fieldname"] = "更新时间";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        if ($request['client_tracestatus'] == '-2') {
            $field[$k]["fieldstring"] = "last_zhu_marketer_name";
            $field[$k]["fieldname"] = "最后负责人";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;
        } else {
            $field[$k]["fieldstring"] = "zhu_marketer_name";
            $field[$k]["fieldname"] = "主要负责人";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;
        }
        if ($this->company_isassist == 1) {
            $field[$k]["fieldstring"] = "fu_marketer_name";
            $field[$k]["fieldname"] = "协助负责人";
            $field[$k]["show"] = 0;
            $field[$k]["custom"] = 1;
            $k++;
        }

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $res = array('error' => $ReportModel->error, 'errortip' => $ReportModel->errortip, 'result' => $result);
            ajax_return($res, $request['language_type']);
        }

        if ($res) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => "暂无渠道招生明细", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //转介绍招生明细表
    function referralClientlistView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Report\Gmc\GmcCrmReportModel($request);
        $res = $ReportModel->referralClientlist($request);

        $k = 0;

        $field = array();
        $field[$k]["fieldstring"] = "client_createtime";
        $field[$k]["fieldname"] = "名单创建时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "province_name";
        $field[$k]["fieldname"] = "省份";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "district_cnname";
        $field[$k]["fieldname"] = "所属区域";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;


        $field[$k]["fieldstring"] = "client_cnname";
        $field[$k]["fieldname"] = "被推荐学员姓名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_branch";
        $field[$k]["fieldname"] = "被推荐学员编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;


//        $field[$k]["fieldstring"] = "client_enname";
//        $field[$k]["fieldname"] = "英文名";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//        $k++;
        $field[$k]["fieldstring"] = "client_sex";
        $field[$k]["fieldname"] = "性别";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "client_age";
        $field[$k]["fieldname"] = "年龄";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "client_mobile";
        $field[$k]["fieldname"] = "联系方式";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "client_tracestatus";
        $field[$k]["fieldname"] = "客户状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "pay_successtime";
        $field[$k]["fieldname"] = "报名时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursetype_cnname";
        $field[$k]["fieldname"] = "报名班组";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;


        $field[$k]["fieldstring"] = "pay_price";
        $field[$k]["fieldname"] = "首付金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "zhu_marketer_name";
        $field[$k]["fieldname"] = "主要负责人";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "channel_name";
        $field[$k]["fieldname"] = "招生渠道明细";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "rom_branch";
        $field[$k]["fieldname"] = "推荐学员编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "rom_cnname";
        $field[$k]["fieldname"] = "推荐学员姓名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "romclass_branch";
        $field[$k]["fieldname"] = "推荐班级编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "romclass_enname";
        $field[$k]["fieldname"] = "推荐班级别名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "client_teachername";
        $field[$k]["fieldname"] = "推荐教师姓名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $res = array('error' => $ReportModel->error, 'errortip' => $ReportModel->errortip, 'result' => $result);
            ajax_return($res, $request['language_type']);
        }

        if ($res) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => "暂无渠道招生明细", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //转介绍报名明细表
    function referralBooklistView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Report\Gmc\GmcCrmReportModel($request);
        $res = $ReportModel->referralBooklist($request);

        $k = 0;
        $field = array();
        $field[$k]["fieldstring"] = "province_name";
        $field[$k]["fieldname"] = "省份";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "district_cnname";
        $field[$k]["fieldname"] = "所属区域";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "client_cnname";
        $field[$k]["fieldname"] = "CRM中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "client_sex";
        $field[$k]["fieldname"] = "性别";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "client_mobile";
        $field[$k]["fieldname"] = "联系手机";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "channel_name";
        $field[$k]["fieldname"] = "招生渠道明细";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "client_createtime";
        $field[$k]["fieldname"] = "创建时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_branch";
        $field[$k]["fieldname"] = "学生编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["fieldname"] = "学员中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_enname";
        $field[$k]["fieldname"] = "学员英文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursetype_cnname";
        $field[$k]["fieldname"] = "报名班组";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "pay_price";
        $field[$k]["fieldname"] = "首缴金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "pay_firsttime";
        $field[$k]["fieldname"] = "首缴日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "pay_successtime";
        $field[$k]["fieldname"] = "报名缴费日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;


        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $res = array('error' => $ReportModel->error, 'errortip' => $ReportModel->errortip, 'result' => $result);
            ajax_return($res, $request['language_type']);
        }

        if ($res) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => "暂无渠道招生明细", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //渠道业绩报表
    function gainsByChannelView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Report\Gmc\GmcCrmReportModel($request);
        $res = $ReportModel->gainsByChannel($request);

        $k = 0;
        $field = array();
        $field[$k]["fieldstring"] = "channel_name";
        $field[$k]["fieldname"] = "渠道明细";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "channel_medianame";
        $field[$k]["fieldname"] = "渠道类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "section_name";
        $field[$k]["fieldname"] = "渠道板块";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "channel_allnum";
        $field[$k]["fieldname"] = "渠道总名单";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "channel_effectivenum";
        $field[$k]["fieldname"] = "渠道有效名单";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "channel_tmknum";
        $field[$k]["fieldname"] = "电销有效名单";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "channel_tmkcallnum";
        $field[$k]["fieldname"] = "电销诺访";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "channel_tmkcallrate";
        $field[$k]["fieldname"] = "电销诺访率";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_allnum";
        $field[$k]["fieldname"] = "校区有效名单";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_visitnum";
        $field[$k]["fieldname"] = "实际到访";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_visitrate";
        $field[$k]["fieldname"] = "校区电转率";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_booknum";
        $field[$k]["fieldname"] = "校区签约数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_bookrate";
        $field[$k]["fieldname"] = "面转率";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_bookallnum";
        $field[$k]["fieldname"] = "区间报名总数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_totalnum";
        $field[$k]["fieldname"] = "历史累计报名数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => "暂无渠道统计分析", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //TMK业绩咨询表
    function getTmkConsulView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Report\Gmc\GmcCrmReportModel($request);
        $res = $ReportModel->getTmkConsul($request);

        $k = 0;
        $field = array();
        $field[$k]["fieldstring"] = "province_name";
        $field[$k]["fieldname"] = "省份";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "district_cnname";
        $field[$k]["fieldname"] = "所属区域";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "client_cnname";
        $field[$k]["fieldname"] = "学员名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_branch";
        $field[$k]["fieldname"] = "学生编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "client_mobile";
        $field[$k]["fieldname"] = "手机号码";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "client_source";
        $field[$k]["fieldname"] = "渠道类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "channel_name";
        $field[$k]["fieldname"] = "渠道明细";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "positivelog_time";
        $field[$k]["fieldname"] = "转正有效期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "client_gmcmarket";
        $field[$k]["fieldname"] = "导入人";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "client_soursename";
        $field[$k]["fieldname"] = "渠道备注";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "client_remark";
        $field[$k]["fieldname"] = "客户备注";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "audition_visittime";
        $field[$k]["fieldname"] = "试听日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "invite_visittime";
        $field[$k]["fieldname"] = "柜询日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "client_updatetime";
        $field[$k]["fieldname"] = "名单更新时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "client_createtime";
        $field[$k]["fieldname"] = "名单创建时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => "暂无渠道统计分析", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //沟通类型明细表
    function trackDetailByLinkTypeView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Report\Gmc\GmcCrmReportModel($request);
        $res = $ReportModel->trackDetailByLinkType($request);

        $k = 0;
        $field = array();
        $field[$k]["fieldstring"] = "province_name";
        $field[$k]["fieldname"] = "省份";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "district_cnname";
        $field[$k]["fieldname"] = "所属区域";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "client_cnname";
        $field[$k]["fieldname"] = "中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "client_enname";
        $field[$k]["fieldname"] = "英文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "client_mobile";
        $field[$k]["fieldname"] = "联系手机";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "channel_name";
        $field[$k]["fieldname"] = "招生渠道明细";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "client_createtime";
        $field[$k]["fieldname"] = "创建时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "main_marketer_name";
        $field[$k]["fieldname"] = "主要负责人";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        if ($this->company_isassist == 1) {
            $k++;
            $field[$k]["fieldstring"] = "sub_marketer_name";
            $field[$k]["fieldname"] = "协助负责人";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
        }
        $k++;
        $field[$k]["fieldstring"] = "client_tracestatus";
        $field[$k]["fieldname"] = "客户状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "track_linktype";
        $field[$k]["fieldname"] = "沟通类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "track_createtime";
        $field[$k]["fieldname"] = "跟踪时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "track_note";
        $field[$k]["fieldname"] = "跟踪明细";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "client_remark";
        $field[$k]["fieldname"] = "学员备注";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => "暂无沟通类型明细", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //学员转介绍报名明细
    function getStuSponsorPositiveView()
    {
        $request = Input('get.', '', 'trim,addslashes');
//        $this->ThisVerify($request);
        $ReportModel = new \Model\Report\Gmc\GmcCrmReportModel($request);
        $res = $ReportModel->getStuSponsorPositive($request);

        $k = 0;
        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["fieldname"] = "中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "student_enname";
        $field[$k]["fieldname"] = "英文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "student_branch";
        $field[$k]["fieldname"] = "学生编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "报名校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "报名校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "client_mobile";
        $field[$k]["fieldname"] = "联系手机";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "channel_medianame";
        $field[$k]["fieldname"] = "渠道类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "channel_name";
        $field[$k]["fieldname"] = "招生渠道明细";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "pay_successtime";
        $field[$k]["fieldname"] = "报名时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "info_createtime";
        $field[$k]["fieldname"] = "添加时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;


        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => "暂无沟通类型明细", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);


    }

    //校园转介绍统计分析表
    function referralAnalyseView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Report\Gmc\GmcCrmReportModel($request);
        $dataList = $Model->referralAnalyseReport($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "school_id";
        $field[$k]["fieldname"] = "ID";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "province_name";
        $field[$k]["fieldname"] = "省份";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "district_cnname";
        $field[$k]["fieldname"] = "所属区域";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "readingnums";
        $field[$k]["fieldname"] = "在读人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "channel_name";
        $field[$k]["fieldname"] = "渠道名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "maonums";
        $field[$k]["fieldname"] = "毛名单数量";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "validnums";
        $field[$k]["fieldname"] = "新增有效名单数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "validrate";
        $field[$k]["fieldname"] = "新增有效率";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "invitenums";
        $field[$k]["fieldname"] = "新增邀约名单数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "visitnums";
        $field[$k]["fieldname"] = "新增到访名单数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "invvisitrate";
        $field[$k]["fieldname"] = "新增邀约到访率";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "inquirenums";
        $field[$k]["fieldname"] = "新增柜询邀约数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "inquirevisitnums";
        $field[$k]["fieldname"] = "新增柜询到访数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "inquireviterate";
        $field[$k]["fieldname"] = "新增柜询到访率";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "ohauditionnums";
        $field[$k]["fieldname"] = "新增OH邀约数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "ohauditionvisitnums";
        $field[$k]["fieldname"] = "新增OH到访数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "ohauditionvisitrate";
        $field[$k]["fieldname"] = "新增OH到访率";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "auditionnums";
        $field[$k]["fieldname"] = "新增试听邀约数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "auditioninvitenums";
        $field[$k]["fieldname"] = "新增试听到访数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "auditioninviterate";
        $field[$k]["fieldname"] = "新增试听到访率";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "booknums";
        $field[$k]["fieldname"] = "新增报名数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $result['field'] = $field;
        if ($dataList['list']) {
            $result['list'] = $dataList['list'];
        } else {
            $result['list'] = array();
        }
        if (isset($dataList['allnum']) && $dataList['allnum'] != "") {
            $allnum = intval($dataList['allnum']);
        } else {
            $allnum = 0;
        }
        $result['allnum'] = $allnum;

        if ($result['list']) {
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result, "allnum" => $allnum);
        } else {
            $res = array('error' => '1', 'errortip' => "暂无更多列表信息", 'result' => $result, "allnum" => $allnum);
        }
        ajax_return($res, $request['language_type']);
    }


    //TMK坐席业绩统计表
    function getTmkSeatsAchievementView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Report\Gmc\GmcCrmReportModel($request);
        $res = $ReportModel->getTmkSeatsAchievement($request);

        $k = 0;
        $field = array();
        $field[$k]["fieldstring"] = "staffer_id";
        $field[$k]["fieldname"] = "职工ID";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "staffer_cnname";
        $field[$k]["fieldname"] = "职工姓名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "staffer_branch";
        $field[$k]["fieldname"] = "职工编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "addnum";
        $field[$k]["fieldname"] = "当前名单数量";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "addnewnum";
        $field[$k]["fieldname"] = "新增分配名单";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "toschnum";
        $field[$k]["fieldname"] = "邀约至校区名单数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "istoschnum";
        $field[$k]["fieldname"] = "邀约到访名单数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "nointennum";
        $field[$k]["fieldname"] = "新增无意向名单";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "invalidnum";
        $field[$k]["fieldname"] = "新增无效名单";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "signupnum";
        $field[$k]["fieldname"] = "报名名单";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => "暂无TMK坐席业绩统计数据", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //TMK渠道业绩转化表
    function getTmkChannelAchievementView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Report\Gmc\GmcCrmReportModel($request);
        $res = $ReportModel->getTmkChannelAchievement($request);

        $k = 0;
        $field = array();
        $field[$k]["fieldstring"] = "channel_name";
        $field[$k]["fieldname"] = "渠道明细";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "channel_medianame";
        $field[$k]["fieldname"] = "所属渠道类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "allnum";
        $field[$k]["fieldname"] = "新增毛名单数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "allvalidnum";
        $field[$k]["fieldname"] = "新增有效名单";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "addallnum";
        $field[$k]["fieldname"] = "新增分配TMK派发量";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "addvalidnum";
        $field[$k]["fieldname"] = "新增分配TMK有效量";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "toschnum";
        $field[$k]["fieldname"] = "TMK邀约名单数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "istoschnum";
        $field[$k]["fieldname"] = "TMK到访名单数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "signupnum";
        $field[$k]["fieldname"] = "TMK报名名单数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => "暂无TMK渠道业绩转化数据", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //TMK邀约跟踪状况表
    function getTmkInviteTrackView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Report\Gmc\GmcCrmReportModel($request);
        $res = $ReportModel->getTmkInviteTrack($request);

        $k = 0;
        $field = array();
        $field[$k]["fieldstring"] = "client_id";
        $field[$k]["fieldname"] = "名单ID";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;
        $field[$k]["fieldstring"] = "client_cnname";
        $field[$k]["fieldname"] = "姓名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "client_mobile";
        $field[$k]["fieldname"] = "手机号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "client_tracestatusname";
        $field[$k]["fieldname"] = "客户状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "channel_medianame";
        $field[$k]["fieldname"] = "渠道类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "channel_name";
        $field[$k]["fieldname"] = "渠道明细";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "client_createtime";
        $field[$k]["fieldname"] = "创建日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "staffer_cnname";
        $field[$k]["fieldname"] = "TMK负责人";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "first_track_createtime";
        $field[$k]["fieldname"] = "TMK首次邀约日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "TMK邀约校区";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "visittime";
        $field[$k]["fieldname"] = "到访时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "last_track_createtime";
        $field[$k]["fieldname"] = "最后跟踪时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "last_track_note";
        $field[$k]["fieldname"] = "最后跟踪内容";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "last_staffer_cnname";
        $field[$k]["fieldname"] = "最后跟踪人";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "isinvite";
        $field[$k]["fieldname"] = "是否已柜询";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "isaudition";
        $field[$k]["fieldname"] = "是否已试听";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "isconversionlog";
        $field[$k]["fieldname"] = "是否转正";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "ispay";
        $field[$k]["fieldname"] = "是否缴费";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
//            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => "暂无TMK邀约跟踪状况数据", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //校内扩科招生大表
    function getStuClientEnrollView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Report\Gmc\GmcCrmReportModel($request);
        $res = $ReportModel->getStuClientEnroll($request);

        $k = 0;
        $field = array();
        $field[$k]["fieldstring"] = "province_name";
        $field[$k]["fieldname"] = "省份";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "学校名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "学校编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "district_cnname";
        $field[$k]["fieldname"] = "所属区域";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "coursetype_cnname";
        $field[$k]["fieldname"] = "班组名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "coursecat_cnname";
        $field[$k]["fieldname"] = "班种名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "absenteenums";
        $field[$k]["fieldname"] = "在籍人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "readingnums";
        $field[$k]["fieldname"] = "在读人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "register_week";
        $field[$k]["fieldname"] = "报名/周";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "register_month";
        $field[$k]["fieldname"] = "报名/月";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "principalnum";
        $field[$k]["fieldname"] = "新增意向学员/月";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "tracknum";
        $field[$k]["fieldname"] = "追踪名单/月";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "trackallnum";
        $field[$k]["fieldname"] = "追踪名单次/月";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "auditionnum";
        $field[$k]["fieldname"] = "试听邀约人数/月";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "auditionvisitnum";
        $field[$k]["fieldname"] = "试听邀约到访/月";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => "暂无TMK邀约跟踪状况数据", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //校区招生总览报表
    function getCoursecatRecruitView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Report\Gmc\GmcCrmReportModel($request);
        $res = $ReportModel->getCoursecatRecruit($request);

        $k = 0;
        $field = array();
        $field[$k]["fieldstring"] = "province_name";
        $field[$k]["fieldname"] = "省份";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "school_shortname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "district_cnname";
        $field[$k]["fieldname"] = "所属区域";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "coursetype_cnname";
        $field[$k]["fieldname"] = "班组名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "coursecat_cnname";
        $field[$k]["fieldname"] = "班种名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "coursecat_branch";
        $field[$k]["fieldname"] = "班种编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "absenteenums";
        $field[$k]["fieldname"] = "当前在籍人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "readingnums";
        $field[$k]["fieldname"] = "当前在读人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "allclientstudent";
        $field[$k]["fieldname"] = "总报名人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "clienttostudent";
        $field[$k]["fieldname"] = "新学员报名人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "extendcoursereg";
        $field[$k]["fieldname"] = "扩科报名人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => "暂无班种招生分析表数据", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //校区招生分析报表
    function getCenterEnrollAnalysisView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Report\Gmc\GmcCrmReportModel($request);
        $res = $ReportModel->getCenterEnrollAnalysis($request);

        $k = 0;
        $field = array();
        $field[$k]["fieldstring"] = "province_name";
        $field[$k]["fieldname"] = "省份";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "school_shortname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "district_cnname";
        $field[$k]["fieldname"] = "所属区域";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "absenteenums";
        $field[$k]["fieldname"] = "当前在籍人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "readingnums";
        $field[$k]["fieldname"] = "当前在读人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "allregstu";
        $field[$k]["fieldname"] = "总报名人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "clienttostudent";
        $field[$k]["fieldname"] = "新学员报名人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "extendcoursereg";
        $field[$k]["fieldname"] = "扩科报名人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "recruitclientreg";
        $field[$k]["fieldname"] = "园内内招报名人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "newaddgrossclient";
        $field[$k]["fieldname"] = "新增毛名单";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "newaddpushchannelclient";
        $field[$k]["fieldname"] = "新增地推有效名单";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "newaddwaychannelclient";
        $field[$k]["fieldname"] = "新增线上有效名单";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "addstubranchclient";
        $field[$k]["fieldname"] = "推荐名单";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "addstubranchclientreg";
        $field[$k]["fieldname"] = "推荐报名人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "nothaveprincipal";
        $field[$k]["fieldname"] = "当前待分配名单";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "haveprincipaltrackclient";
        $field[$k]["fieldname"] = "追踪名单数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "haveprincipaltrackclientall";
        $field[$k]["fieldname"] = "追踪人次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "inviteclient";
        $field[$k]["fieldname"] = "邀约人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "inviteclientreach";
        $field[$k]["fieldname"] = "邀约到访人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "conversion";
        $field[$k]["fieldname"] = "电转率";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "pushinviteclientreach";
        $field[$k]["fieldname"] = "推带到访人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "activeinviteclientreach";
        $field[$k]["fieldname"] = "主动到访人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "allinviteclientreach";
        $field[$k]["fieldname"] = "总到访人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "allinviteclientreachbook";
        $field[$k]["fieldname"] = "总到访报名人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "signrate";
        $field[$k]["fieldname"] = "到访签约率";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "ohauditionclient";
        $field[$k]["fieldname"] = "OH试听人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "ohauditionclientreach";
        $field[$k]["fieldname"] = "OH转正人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "ohformalrate";
        $field[$k]["fieldname"] = "OH转正率";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => "暂无班种招生分析表数据", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }


    //结尾魔术函数
    function __destruct()
    {

    }
}
