<?php


namespace Work\Controller\Gmcapi;


use Work\Controller\Gmcapi\viewTpl;

class ChangeController extends viewTpl
{
    public $u;
    public $t;
    public $c;

    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
    }

    function getFreeapplyListApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ChangeModel = new \Model\Gmc\ChangeModel($request);
        $res = $ChangeModel->getFreeapplyList($request);
        $k = 0;
        $field = array();

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "district_cnname";
        $field[$k]["fieldname"] = "督导区域";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_cnname";
        $field[$k]["fieldname"] = "班级名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_branch";
        $field[$k]["fieldname"] = "班级编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "freeapply_type_name";
        $field[$k]["fieldname"] = "申请类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "freeapply_day";
        $field[$k]["fieldname"] = "申请上课日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "timerange";
        $field[$k]["fieldname"] = "上课时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "main_staffer_cnname";
        $field[$k]["fieldname"] = "主教教师";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "fu_staffer_cnname";
        $field[$k]["fieldname"] = "助教教师";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "classroom_cnname";
        $field[$k]["fieldname"] = "上课教室";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "freeapply_reason";
        $field[$k]["fieldname"] = "申请原因";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "staffer_cnname";
        $field[$k]["fieldname"] = "申请人";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "freeapply_createtime";
        $field[$k]["fieldname"] = "申请时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "freeapply_status_name";
        $field[$k]["fieldname"] = "审核状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "freeapply_staffer_cnname";
        $field[$k]["fieldname"] = "审核人";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "freeapply_updatatime";
        $field[$k]["fieldname"] = "审核时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "freeapply_note";
        $field[$k]["fieldname"] = "审核备注";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();

        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["list"] = $res['list'];
            $result["fieldcustom"] = 0;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["allnum"] = 0;
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $ChangeModel->errortip, 'result' => $result);

        }
        ajax_return($res, $request['language_type']);
    }

    function batchExamineFreeapplyAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ChangeModel = new \Model\Gmc\ChangeModel($request);
        $res = $ChangeModel->batchExamineFreeapply($request);
        $result = array();
        if ($res) {
            $result["list"] = $res;
            $result = array('error' => 0, 'errortip' => '审核成功', 'result' => $result);
        } else {
            $result = array('error' => 1, 'errortip' => $ChangeModel->errortip, 'result' => $result);
        }
        ajax_return($result, $request['language_type']);
    }

    function examineFreeapplyAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ChangeModel = new \Model\Gmc\ChangeModel($request);
        $res = $ChangeModel->examineFreeapply($request);
        $result = array();
        if ($res) {
            $result["list"] = $res;
            $result = array('error' => 0, 'errortip' => '审核成功', 'result' => $result);
        } else {
            $result = array('error' => 1, 'errortip' => $ChangeModel->errortip, 'result' => $result);
        }
        ajax_return($result, $request['language_type']);
    }

    function getAdjustapplyListApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ChangeModel = new \Model\Gmc\ChangeModel($request);
        $res = $ChangeModel->getAdjustapplyList($request);
        $k = 0;
        $field = array();
        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "district_cnname";
        $field[$k]["fieldname"] = "督导区域";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "adjustapply_type_name";
        $field[$k]["fieldname"] = "申请类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursetype_cnname";
        $field[$k]["fieldname"] = "所属班组";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_cnname";
        $field[$k]["fieldname"] = "班级名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_branch";
        $field[$k]["fieldname"] = "班级编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "hour_day";
        $field[$k]["fieldname"] = "原上课日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "adjustapply_day";
        $field[$k]["fieldname"] = "申请调课日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "days_difference";
        $field[$k]["fieldname"] = "间隔天数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "adjustapply_class_name";
        $field[$k]["fieldname"] = "调课原因";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "adjustapply_reason";
        $field[$k]["fieldname"] = "调课备注";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "adjustapply_fileurl_name";
        $field[$k]["fieldname"] = "附件";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["ismethod"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "staffer_cnname";
        $field[$k]["fieldname"] = "申请人";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "adjustapply_createtime";
        $field[$k]["fieldname"] = "申请时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "adjustapply_status_name";
        $field[$k]["fieldname"] = "审核状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "adjustapply_staffer_cnname";
        $field[$k]["fieldname"] = "审核人";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "adjustapply_updatatime";
        $field[$k]["fieldname"] = "审核时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "adjustapply_note";
        $field[$k]["fieldname"] = "审核备注";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();

        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["list"] = $res['list'];
            $result["fieldcustom"] = 0;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["allnum"] = 0;
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $ChangeModel->errortip, 'result' => $result);

        }
        ajax_return($res, $request['language_type']);
    }

    function batchExamineAdjustapplyAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ChangeModel = new \Model\Gmc\ChangeModel($request);
        $res = $ChangeModel->batchExamineAdjustapply($request);
        $result = array();
        if ($res) {
            $result["list"] = $res;
            $result = array('error' => 0, 'errortip' => '审核成功', 'result' => $result);
        } else {
            $result = array('error' => 1, 'errortip' => $ChangeModel->errortip, 'result' => $result);
        }
        ajax_return($result, $request['language_type']);
    }

    function examineAdjustapplyAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ChangeModel = new \Model\Gmc\ChangeModel($request);
        $res = $ChangeModel->examineAdjustapply($request);
        $result = array();
        if ($res) {
            $result["list"] = $res;
            $result = array('error' => 0, 'errortip' => '审核成功', 'result' => $result);
        } else {
            $result = array('error' => 1, 'errortip' => $ChangeModel->errortip, 'result' => $result);
        }
        ajax_return($result, $request['language_type']);
    }

    
    function getApplicationListApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Gmc\ChangeModel($request);
        $res= $Model->getApplicationList($request);

        $field=array();
        $k=0;

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["fieldname"] = "学员名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_branch";
        $field[$k]["fieldname"] = "学员编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "course_cnname";
        $field[$k]["fieldname"] = "课程名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "course_branch";
        $field[$k]["fieldname"] = "课程编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_cnname";
        $field[$k]["fieldname"] = "所在班级";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "sub_course_name";
        $field[$k]["fieldname"] = "随堂课程";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "application_type_name";
        $field[$k]["fieldname"] = "结转类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "out_class_date";
        $field[$k]["fieldname"] = "申请出班日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "back_class_date";
        $field[$k]["fieldname"] = "申请回班耗课日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "forward_reason";
        $field[$k]["fieldname"] = "结转原因";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "attachment_url_name";
        $field[$k]["fieldname"] = "附件";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["ismethod"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "remaining_amount";
        $field[$k]["fieldname"] = "预估结转金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "application_status_name";
        $field[$k]["fieldname"] = "审核状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "apply_time";
        $field[$k]["fieldname"] = "申请日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "staffer_cnname";
        $field[$k]["fieldname"] = "申请人";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "approval_note";
        $field[$k]["fieldname"] = "备注";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;
        if($res){
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        }else{
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }

    
    function getJindieErrorListApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ChangeModel = new \Model\Smc\ChangeModel($request);
        $res = $ChangeModel->getJindieErrorList($request);
        $k = 0;
        $field = array();

        
        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "学校名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "trading_pid";
        $field[$k]["fieldname"] = "退费交易编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["ismethod"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["fieldname"] = "学员姓名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["ismethod"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_branch";
        $field[$k]["fieldname"] = "学员编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "order_status_name";
        $field[$k]["fieldname"] = "订单状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "refund_payprice";
        $field[$k]["fieldname"] = "本次退费金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "error_message";
        $field[$k]["fieldname"] = "异常提示";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "create_time";
        $field[$k]["fieldname"] = "触发时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "is_handle_name";
        $field[$k]["fieldname"] = "处理状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "handle_staffer_cnname";
        $field[$k]["fieldname"] = "处理人";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "handle_time";
        $field[$k]["fieldname"] = "处理时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;


        $result = array();

        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["list"] = $res['list'];
            $result["fieldcustom"] = 0;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["allnum"] = 0;
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $ChangeModel->errortip, 'result' => $result);

        }
        ajax_return($res, $request['language_type']);
    }


    function submitErrorHandleAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ChangeModel = new \Model\Smc\ChangeModel($request);
        $res = $ChangeModel->submitErrorHandle($request);
        $result = array();
        if ($res) {
            $result["list"] = $res;
            $result = array('error' => 0, 'errortip' => '提交成功', 'result' => $result);
        } else {
            $result = array('error' => 1, 'errortip' => $ChangeModel->errortip, 'result' => $result);
        }
        ajax_return($result, $request['language_type']);
    }


}
