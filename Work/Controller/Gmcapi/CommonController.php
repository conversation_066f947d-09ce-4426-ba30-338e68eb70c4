<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/13
 * Time: 23:25
 * crm-首页
 */

namespace Work\Controller\Gmcapi;

class CommonController extends viewTpl
{
	public $u;
	public $t;
	public $c;
	public $visitType = "api";
	
	
	//预加载处理类
	function __construct($visitType = "api")
	{
        parent::__construct ();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
	}

    //首页 -- 筛选学校机构
    function getModuleHandbookApi()
    {
        $request = Input('get.','','trim,addslashes');
        //model
        $CommonModel = new \Model\Gmc\ModuleModel($request);
        $dataList = $CommonModel->getModuleHandbookApi($request);

        $field = array();
        $field["handbook_id"] = "ID";
        $field["module_id"] = "所属模块ID";
        $field["handbook_name"] = "手册名称";
        $field["handbook_note"] = "手册内容";
        $field["handbook_videourl"] = "培训视频路径";

        $result = array();
        if($dataList){
            $result["field"] = $field;
            $result["data"] = $dataList;
            $res = array('error' => '0', 'errortip' => '模块培训教程获取成功', 'result' => $result);
        }else{
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '模块培训教程获取失败', 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }

    //首页 -- 筛选学校机构
    function getModuleHandbookOneApi()
    {
        $request = Input('get.','','trim,addslashes');
        //model
        $CommonModel = new \Model\Crm\CommonModel($request);
        $dataList = $CommonModel->getModuleHandbookOneApi($request);

        $field = array();
        $field["handbook_id"] = "ID";
        $field["module_id"] = "模块ID";
        $field["handbook_name"] = "手册名称";
        $field["handbook_note"] = "手册内容";

        $result = array();
        if($dataList){
            $result["field"] = $field;
            $result["data"] = $dataList;
            $res = array('error' => '0', 'errortip' => '培训教程获取成功', 'result' => $result);
        }else{
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '培训教程获取失败', 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }

    //获取疾病列表
    function getDiseaseListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\SettingModel($request);

        $result = $Model->getDiseaseList($request);
        ajax_return($result, $request['language_type']);
    }

    //获取证件类型
    function getDocumentTypeListApi()
    {
        $request = Input('get.','','trim,addslashes');
        $sql = "select * from gmc_code_documenttype";
        $list = $this->DataControl->selectClear($sql);
        if (!$list) {
            $list = array();
        }
        $result["list"] = $list;
        $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        ajax_return($res,$request['language_type']);
    }

}