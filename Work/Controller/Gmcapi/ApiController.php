<?php

namespace Work\Controller\Gmcapi;


class ApiController extends viewTpl
{
    public $u;
    public $t;
    public $c;
    public $visitType = "api";
    public $Model;

    //预加载处理类
    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
    }

    //发送短信的测试
    function testFaDuanXinApi()
    {
        die;
        $request = Input('get.', '', 'trim,addslashes');
        $minsendModel = new \Model\Api\SmsModel($request);
        $minsendModel->crmMisSend('13127587321', "ceshi", '试听邀约', date("m-d"));
    }

    //应急 BI 报表数据 --  招生报表 新
    function getClientRecruitReportView()
    {
        $hour = date("G");
        if ($hour > 8 && $hour < 24) {
            $nowday = date("Y-m-d");
            $sql = "SELECT f.*
FROM(SELECT s.school_id, s.school_cnname, c.coursetype_id, c.coursetype_cnname
FROM smc_school AS s, smc_code_coursetype AS c
WHERE s.company_id = c.company_id AND c.company_id = '8888' AND s.school_isclose = '0' AND c.coursetype_id = '65'
AND s.school_istest = '0' AND c.coursetype_isrecruit = '1' AND c.coursetype_isopenclass = '0') AS f 
WHERE NOT EXISTS (SELECT 1 FROM crm_client_bilog as c WHERE c.bilog_type = '0' AND c.coursetype_id = f.coursetype_id 
		AND c.school_id = f.school_id AND c.bilog_day >= '{$nowday}') limit 0,10";
            $schoolList = $this->DataControl->selectClear($sql);

            $fieldname = array("地区", "校区名称", "督导区",
                "报名/去年",
                "报名/当年", "报名/周", "报名/月",
                "新增总毛名单/月",
                "新增陆军有效名单/月", "新增空军有效名单/月",
                "新增转介绍名单/月", "转介绍名单报名/月",
                "待分配名单数" );
            $fieldstring = array("school_districtname", "school_shortname", "school_tagbak",
                'prelog_year_num',
                "nowlog_year_num", "positivelog_week_num", "positivelog_month_num",
                "mao_month_client_num",
                "client_under_monthnum","client_up_monthnum",
                "client_referral_monthnum", "client_referral_regmonthnum"
            , "client_noallot_num" );

            $fieldcustom = array('1', '1', '1',
                "1",
                '1', '1', '1',
                '1',
                "1", "1",
                '1', '1',
                '1' );
            $fieldshow = array('1', '1', '1',
                "1",
                '1', '1', '1',
                '1',
                "1", "1",
                '1', '1',
                '1'  );

//            $fieldname = array("地区", "校区名称", "督导区", "在籍", "在读", "专案免费在读人数", "报名/去年", "专案报名/去年",
//                "报名/当年", "报名/周", "报名/月", "上月招生数", "上上月招生数", "新增总毛名单/自然周", "新增总毛名单/自然月",
//                "新增陆军有效名单/周", "新增陆军有效名单/月", "新增空军有效名单/周", "新增空军有效名单/月", "新增有效名单/月（空+陆）", "新增有效名单/季", "新增转介绍名单/月",
//                "园内招（名单/自然月）", "园内招（名单/自然季）", "园内招（名单/自然年）", "园内招（报名/自然月）", "园内招（报名/自然季）", "园内招（报名/自然年）",
//                "可追踪有效名单", "待分配名单数", "追踪(邀约到访）名单数/周", "追踪(邀约到访）人次/周", "追踪人数/月", "追踪人次/月", "邀约诺访/上周", "邀约到访/上周",
//                "邀约诺访/当周", "邀约到访/当周", "邀约诺访/月", "邀约到访/月", "主动到访/周",
//                "主动到访/月", "试读/周", "试读/月");
//            $fieldstring = array("school_districtname", "school_shortname", "school_tagbak", "absenteenums", "readingnums", "feereadingnums", 'prelog_year_num', 'prelog_caseyear_num',
//                "nowlog_year_num", "positivelog_week_num", "positivelog_month_num", "positivelog_one_month_num", "positivelog_two_month_num", "mao_week_client_num", "mao_month_client_num",
//                "client_under_weeknum", "client_under_monthnum", "client_up_weeknum", "client_up_monthnum", "client_upunder_monthnum", "client_up_quarternum", "client_referral_monthnum",
//                "register_client_monthnum", "register_client_seasonnum", "register_client_yearnum", "info_month_num", "info_season_num", "info_year_num",
//                "client_all_num", "client_noallot_num", "invitelog_week_num", "invitelog_week_numonce", "track_client_num", "track_tracknum", "invitelog_lastweek_num", "invitelog_lastweek_isvisitnum",
//                "invitelog_thisweek_num", "invitelog_thisweek_isvisitnum", "invitelog_month_num", "invitelog_month_isvisitnum", "invite_week_num",
//                "invite_month_num", "audition_week_num", "audition_month_num");
//
//            $fieldcustom = array('1', '1', '1', '1', '1', "1", '1', "1",
//                '1', '1', '1', '1', "1", '1', "1",
//                '1', '1', '1', '1', "1", '1', "1",
//                '1', '1', '1', '1', "1", '1',
//                '1', '1', '1', '1', "1", '1', "1", '1',
//                '1', '1', '1', '1', "1",
//                '1', '1', '1');
//            $fieldshow = array('1', '1', '1', '1', "1",
//                '1', '1', '1', '1', "1", '1', "1",
//                '1', '1', '1', '1', "1", '1', "1",
//                '1', '1', '1', '1', "1", '1',
//                '1', '1', '1', '1', "1", '1', "1", '1',
//                '1', '1', '1', '1', "1",
//                '1', '1', '1');

            $field = array();
            for ($i = 0; $i < count($fieldstring); $i++) {
                $field[$i]["fieldstring"] = trim($fieldstring[$i]);
                $field[$i]["fieldname"] = trim($fieldname[$i]);
                $field[$i]["custom"] = trim($fieldcustom[$i]);
                $field[$i]["show"] = trim($fieldshow[$i]);
            }

            $paramArray = array();
            $paramArray['company_id'] = 8888;
            $Model = new \Model\Report\Gmc\GmcCrmReportModel($paramArray);
            if ($schoolList) {
                foreach ($schoolList as $schoolOne) {
                    $paramArray = array();
                    $paramArray['company_id'] = '8888';
                    $paramArray['school_id'] = $schoolOne['school_id'];
                    $paramArray['coursetype_id'] = $schoolOne['coursetype_id'];
                    $datalist = $Model->getClientRecruitReport($paramArray);
                    $datajson = json_encode($datalist['list'][0], JSON_UNESCAPED_UNICODE);

                    $data = array();
                    $data['school_id'] = $schoolOne['school_id'];
                    $data['coursetype_id'] = $schoolOne['coursetype_id'];
                    $data['bilog_jsontxt'] = $datajson;
                    $data['bilog_type'] = 0;
                    $data['bilog_updatetime'] = time();
                    $data['bilog_day'] = date("Y-m-d");
                    $this->DataControl->insertData('crm_client_bilog', $data);
                }
            }

            $res = array('error' => '1', 'errortip' => "更新成功");
            ajax_return($res);
        } else {
            $res = array('error' => '1', 'errortip' => "非更新时段！");
            ajax_return($res);
        }
    }


    //优惠券适配课程 -- 20220224 加  -- 97 (调整老生优惠券的时候改的  废弃了）
    function couponsToCourseApi()
    {

        die;
        $this->DataControl->selectClear("SELECT * 
                    FROM smc_student_coupons as c 
                    LEFT JOIN smc_student_coupons_apply as a ON c.apply_id = a.apply_id
                    WHERE a.applytype_branch = 'oldstupre' 
                    and c.coupons_isuse = '0' 
                    and c.coupons_exittime > '1645632000' 
                    ORDER BY c.coupons_id DESC
                    ");
    }

    function getTmkBatchIdApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        if ($request['company_id'] == '') {
            $res = array('error' => 1, 'errortip' => '集团ID不能为空', 'result' => '');
            ajax_return($res, $request['language_type']);
        }
        do {
            $Batchid = getTmkBatchId();
        } while ($this->DataControl->selectOne("select tmkbatch_id from crm_client_tmkbatch where tmkbatch_number='{$Batchid}' and company_id='{$request['company_id']}' limit 0,1"));

        if ($Batchid) {
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $Batchid);
        } else {
            $res = array('error' => 1, 'errortip' => '获取失败', 'result' => '');
        }
        ajax_return($res, $request['language_type']);
    }

    function courseCatApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $datawhere = "c.company_id='{$request['company_id']}'";
        if (isset($request['coursetype_id']) and $request['coursetype_id'] != '') {
            $datawhere .= " and c.coursetype_id='{$request['coursetype_id']}'";
        }
        if (isset($request['coursetype_array_id']) and $request['coursetype_array_id'] != '') {
            $coursetype_array = json_decode(stripslashes($request['coursetype_array_id']), 1);
            if (is_array($coursetype_array) && count($coursetype_array) > 0) {
                $string = implode($coursetype_array, ',');
                $datawhere .= " and c.coursetype_id in ({$string})";
            }
        }

        if ($request['type'] == '1') {
            $sql = "select c.* from smc_code_coursecat as c where {$datawhere} and c.coursecat_istreaty = '1' ORDER BY coursecat_iscrmadded DESC,coursecat_branch ASC";
        } else {
            $sql = "select c.* from smc_code_coursecat as c where {$datawhere} ORDER BY coursecat_iscrmadded DESC,coursecat_branch ASC";
        }

        $list = $this->DataControl->selectClear($sql);

        if ($list) {
            $result["list"] = $list;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '无班种信息,获取失败', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function getFeetypeListApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $list = $this->DataControl->selectClear("select * from smc_code_feetype");

        $companyOne = $this->DataControl->getFieldOne("gmc_company", "company_language", "company_id='{$request['company_id']}'");

        $Model = new \Model\jianfanModel();
        foreach ($list as &$val) {
            if ($companyOne['company_language'] == 'tw') {
                $val['feetype_name'] = $Model->gb2312_big5($val['feetype_name']);
            }
        }

        if ($list) {
            $result["list"] = $list;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '获取信息', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //获取所有班别
    function getCourseApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $datawhere = "c.company_id='{$request['company_id']}'";
        if (isset($request['coursetype_id']) and $request['coursetype_id'] != '') {
            $datawhere .= " and c.coursetype_id='{$request['coursetype_id']}'";
        }

        if (isset($request['coursecat_id']) and $request['coursecat_id'] != '') {
            $datawhere .= " and c.coursecat_id='{$request['coursecat_id']}'";
        }

        if (isset($request['course_inclasstype']) and $request['course_inclasstype'] != '') {
            $datawhere .= " and c.course_inclasstype='{$request['course_inclasstype']}'";
        }

        $classwhere = "a.company_id = '8888' AND a.class_status <> '-2'";
        if (isset($request['school_id']) and $request['school_id'] != '') {
            $classwhere .= " and a.school_id='{$request['school_id']}'";
        }

        $datawhere .= " AND ( c.course_status <> '-1' OR c.course_id IN ( SELECT a.course_id FROM smc_class AS a WHERE {$classwhere}) )";

        $sql = "SELECT c.course_id, c.course_cnname, c.course_branch, c.coursetype_id, c.coursecat_id
FROM smc_course AS c WHERE {$datawhere} ORDER BY c.coursetype_id ASC,c.coursecat_id ASC,c.course_branch ASC";

        $courseList = $this->DataControl->selectClear($sql);
        if ($courseList) {
            $result["list"] = $courseList;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '获取信息', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    function getTeacherApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $datawhere = " 1 ";

        if (isset($request['mainIds']) && $request['mainIds'] !== '') {
            $datawhere .= " and s.staffer_id not in ({$request['mainIds']})";
        }

        if (isset($request['auxiliaryIds']) && $request['auxiliaryIds'] !== '') {
            $datawhere .= " and s.staffer_id not in ({$request['auxiliaryIds']})";
        }

        if (isset($request['school_id']) and $request['school_id'] != '') {
            $datawhere .= " and sp.school_id='{$request['school_id']}'";
        }

        $TeacherList = $this->DataControl->selectClear("select s.staffer_id,s.staffer_cnname,s.staffer_enname,s.staffer_branch,si.info_nation,cp.post_name
                                                      from smc_staffer as s
                                                      left join gmc_staffer_postbe as sp on s.staffer_id=sp.staffer_id
                                                      left join smc_staffer_info as si on si.staffer_id=s.staffer_id
                                                      left join gmc_company_post as cp on cp.post_id=sp.post_id
                                                      where {$datawhere} and sp.company_id='{$request['company_id']}' and cp.post_isteaching=1 and s.staffer_leave = 0
                                                      group by s.staffer_id");
        if ($TeacherList) {
            $result["list"] = $TeacherList;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '获取信息', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }


    function courseTypeApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $datawhere = '1';
        //阅读类
        if (isset($request['from']) && $request['from'] == "courseterm") {
            $datawhere .= " and sc.course_inclasstype ='2'";
        }
        //期度
        if (isset($request['from']) && $request['from'] == "moth") {
            $datawhere .= " and sc.course_inclasstype ='1'";
        }
        if (isset($request['coursetype_isopenclass']) && $request['coursetype_isopenclass'] !== "") {
            $datawhere .= " and c.coursetype_isopenclass = '{$request['coursetype_isopenclass']}'";
        }
        if (isset($request['coursetype_isrecruit']) && $request['coursetype_isrecruit'] !== "") {
            $datawhere .= " and c.coursetype_isrecruit = '{$request['coursetype_isrecruit']}'";
        }
        //协议
        if (isset($request['school_id']) && $request['school_id'] !== "") {
            $datawhere .= " AND (
                (
                    p.pricing_applytype = '1'
                    AND p.pricing_id IN (
                        SELECT
                            pa.pricing_id
                        FROM
                            smc_fee_pricing_apply AS pa
                        WHERE
                           pa.school_id = '{$request['school_id']}'
                    )
                )
                OR (
                    p.pricing_applytype = '-1'
                    AND p.pricing_id NOT IN (
                        SELECT
                            pa.pricing_id
                        FROM
                            smc_fee_pricing_apply AS pa
                        WHERE pa.school_id = '{$request['school_id']}'
                    )
                )
                OR (p.pricing_applytype = '0')
            )";
        }

        $now = date("Y-m-d", time());
        $sql = " SELECT
                c.*
            FROM
                smc_fee_pricing AS p,
                smc_fee_agreement AS a,
                smc_course as sc,
                smc_code_coursetype as c
            WHERE
                {$datawhere}
            AND p.agreement_id = a.agreement_id
            AND sc.course_id = p.course_id
            AND c.coursetype_id = sc.coursetype_id
            AND a.agreement_startday <= '{$now}'
            AND a.agreement_endday >= '{$now}'
            AND a.agreement_status = '1'
            and sc.course_status <> '-1'
            AND a.company_id = '{$request['company_id']}'
            GROUP BY c.coursetype_id order by c.coursetype_id ASC ";

        $openList = $this->DataControl->selectClear("select cc.* from  smc_code_coursetype as cc
where cc.coursetype_isopenclass = '1' and cc.company_id = '{$request['company_id']}' ");

        if (!$openList) {
            $openList = array();
        }

        $list = $this->DataControl->selectClear($sql);
        if (!$list) {
            $list = array();
        }
        $list = array_unique(array_merge($list, $openList), SORT_REGULAR);

        if ($list) {
            $result["list"] = $list;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '无班组信息,获取失败', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }


    //集团招生课程字段
    function courseTypeRecruitApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        // isFromTrack = 1  跟进这边调取班组接口时添加一个这样的参数
        $datawhere = '';
        if (isset($request['coursetype_isrecruit']) && $request['coursetype_isrecruit'] != '') {
            $datawhere .= " and u.coursetype_isrecruit = '{$request['coursetype_isrecruit']}'";
        }

        $sql = "SELECT u.coursetype_id,u.coursetype_cnname,u.coursetype_branch from smc_code_coursetype as u WHERE u.company_id = '{$request['company_id']}' and u.coursetype_isopenclass = '0' {$datawhere} order by coursetype_id asc";
        $typeList = $this->DataControl->selectClear($sql);

        if ($typeList) {
            $result["list"] = $typeList;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '无班组信息,获取失败', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    //获取全部 班组
    function getAllCourseTypeApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        // isFromTrack = 1  跟进这边调取班组接口时添加一个这样的参数
        $now = date("Y-m-d", time());
        $typeList = $this->DataControl->selectClear("SELECT c.coursetype_id, c.coursetype_cnname, c.coursetype_branch
FROM smc_code_coursetype c, smc_course u, smc_fee_pricing p, smc_fee_agreement a
WHERE c.company_id = '{$request['company_id']}' AND c.coursetype_id = u.coursetype_id AND u.course_status <> '-1'
AND p.agreement_id = a.agreement_id AND u.course_id = p.course_id AND a.agreement_startday <= '{$now}'
AND a.agreement_endday >= '{$now}' AND a.agreement_status = '1' GROUP BY c.coursetype_id");
        if ($typeList) {
            $result["list"] = $typeList;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '无班组信息,获取失败', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    //获取 班种接口
    function getTypeCourseCatApi()
    {
        $request = Input('get.', '', 'trim,addslashes');

        $datawhere = " c.company_id = '{$request['company_id']}' and c.coursecat_iscrmadded = '1' and c.coursetype_id = t.coursetype_id and t.coursetype_isopenclass = '0' ";
        if (isset($request['coursetype_id']) && $request['coursetype_id'] != '') {
            $datawhere .= " and t.coursetype_id = '{$request['coursetype_id']}'";
        }

        $sql = " select c.coursecat_id,c.coursetype_id,c.coursecat_cnname,c.coursecat_branch,t.coursetype_cnname,t.coursetype_branch from smc_code_coursecat as c,smc_code_coursetype as t where {$datawhere}";
        $typeList = $this->DataControl->selectClear($sql);

        if ($typeList) {
            $result["list"] = $typeList;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '无班种信息,获取失败', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //获取 组合课程列表
    function getJointcourseApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        if(!$request['company_id']){
            $res = array('error' => 1, 'errortip' => '集团参数不存在', 'result' => array());
            ajax_return($res, $request['language_type']);
        }

        $sql = " select * from smc_code_jointcourse where company_id = '{$request['company_id']}' ";
        $typeList = $this->DataControl->selectClear($sql);

        if ($typeList) {
            $res = array('error' => 0, 'errortip' => '获取组合课程信息', 'result' => $typeList);
        } else {
            $res = array('error' => 1, 'errortip' => '暂无组合课程信息', 'result' => array());
        }
        ajax_return($res, $request['language_type']);
    }

    /**
     * 获取支付方式
     * author: ling
     * 对应接口文档 0001
     */
    function getPayCodeApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $datawhere = " 1 ";
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (paytype_name like %{$request['keyword']}% or paytype_code like %{$request['keyword']}%)";
        }

        $companyOne = $this->DataControl->getFieldOne("gmc_company", "company_language", "company_id='{$request['company_id']}'");

        $payList = $this->DataControl->selectClear("select * from smc_code_paytype where {$datawhere} ");
        if (!$payList) {
            $payList = array();
        } else {
            $Model = new \Model\jianfanModel();
            foreach ($payList as &$payOne) {
                if ($companyOne['company_language'] == 'tw') {
                    $payOne['paytype_name'] = $Model->gb2312_big5($payOne['paytype_name']);
                }
            }
        }
        $res = array('error' => 0, 'errortip' => '获取支付代码成功', 'result' => $payList);
        ajax_return($res, $request['language_type']);
    }

//    获取基础代码接口
    function getCodeApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $datawhere = " 1 ";

        if (isset($request['company_id']) && $request['company_id'] != '' && $request['company_type'] != '0') {
            $datawhere .= " and company_id='{$request['company_id']}'";
        }

        $companyOne = $this->DataControl->getFieldOne("gmc_company", "company_language", "company_id='{$request['company_id']}'");

        if (!isset($request['tablename']) | !isset($request['codecolumn']) | !isset($request['namecolumn'])) {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '获取代码信息失败', 'result' => $result);
        } else {
            $tablename = $request['tablename'];
            $codecolumn = $request['codecolumn'];
            $namecolumn = $request['namecolumn'];

            if (isset($request['keyword']) && $request['keyword'] !== '') {
                $datawhere .= " and ({$codecolumn} like '%{$request['keyword']}%' or {$namecolumn} like '%{$request['keyword']}%')";
            }

            $sql = " select {$codecolumn} as code_id,{$namecolumn} as code_name 
              from {$tablename} 
              where {$datawhere} 
              order by {$codecolumn}";

            $codeList = $this->DataControl->selectClear($sql);
            if (!$codeList) {
                $result["list"] = array();
                $res = array('error' => 1, 'errortip' => '获取代码信息失败', 'result' => $result);
            } else {
                $Model = new \Model\jianfanModel();
                foreach ($codeList as &$codeOne) {
                    if ($companyOne['company_language'] == 'tw') {
                        $codeOne['code_name'] = $Model->gb2312_big5($codeOne['code_name']);
                    }
                }
                $res = array('error' => 0, 'errortip' => '获取代码信息成功', 'result' => $codeList);
            }
        }
        ajax_return($res, $request['language_type']);
    }

    function getLostDetlApi()
    {
        $k = 0;
        $field = array();
        $field[$k]["fieldstring"] = "student_name";
        $field[$k]["fieldname"] = "学员姓名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_branch";
        $field[$k]["fieldname"] = "学员编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "stuchange_name";
        $field[$k]["fieldname"] = "异动类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "changelog_day";
        $field[$k]["fieldname"] = "流失日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursetype_cnname";
        $field[$k]["fieldname"] = "流失班组";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "changelog_category";
        $field[$k]["fieldname"] = "流失类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "changelog_note";
        $field[$k]["fieldname"] = "流失原因";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "staffer_name";
        $field[$k]["fieldname"] = "操作人";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "create_time";
        $field[$k]["fieldname"] = "操作时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $request = Input('get.', '', 'trim,addslashes');
        $datawhere = " 1 ";

//        if (isset($request['keyword']) && $request['keyword'] !== '') {
//            $datawhere .= " and (B.student_cnname like '%{$request['keyword']}%'
//            or B.student_enname like '%{$request['keyword']}%'
//            or B.student_branch like '%{$request['keyword']}%')";
//        }

        if (isset($request['start_time']) && $request['start_time'] !== '') {
            $start_day = date("Y-m-d", strtotime($request['start_time']));
            $datawhere .= " and A.changelog_day >='{$start_day}'";
        }

        if (isset($request['end_time']) && $request['end_time'] !== '') {
            $end_day = date("Y-m-d", strtotime($request['end_time']));
            $datawhere .= " and A.changelog_day <= '{$end_day}'";
        }

        if (isset($request['coursetype_id']) && $request['coursetype_id'] !== '') {
            $datawhere .= " and a.coursetype_id='{$request['coursetype_id']}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select 
                    A.changelog_id,A.student_id,A.coursetype_id,C.stuchange_code,
                    CONCAT(B.student_cnname,(CASE B.student_enname WHEN '' THEN '' ELSE CONCAT('/',B.student_enname) END)) AS student_name,
                    B.student_branch,
                    C.stuchange_name,	
                    A.changelog_day,
                    ifnull(D.coursetype_cnname,'--') as coursetype_cnname,
                    A.changelog_category,
                    (case A.changelog_note when '' then '--' end) as changelog_note,
                    (select CONCAT(staffer_cnname,(CASE ifnull(staffer_enname,'') WHEN '' THEN '' ELSE CONCAT('/',staffer_enname) end)) from smc_staffer where staffer_id =A.staffer_id ) as staffer_name,
                    FROM_UNIXTIME(A.changelog_createtime) AS create_time
                    from smc_student_changelog A 
                    left join smc_student B on B.student_id=A.student_id
                    left join smc_code_stuchange C on C.stuchange_code=A.stuchange_code
                    left join smc_code_coursetype D on D.coursetype_id=A.coursetype_id
                    WHERE {$datawhere}
                    and a.company_id='{$request['company_id']}' 
                    and A.school_id='{$request['school_id']}'  
                    and A.stuchange_code IN('C02','C04') 
                    AND B.student_isdel <> '1' 
                    and a.changelog_note=''
                    AND NOT(A.stuchange_code IN('C02','C04') AND exists(select 1 from smc_student_changelog where change_pid>a.change_pid and student_id=a.student_id and school_id=a.school_id and stuchange_code in('A01','A06','D02','F01')))
                    and not(A.stuchange_code='C04' AND exists(select 1 from smc_student_changelog where change_pid>a.change_pid and student_id=a.student_id and school_id=a.school_id and coursetype_id=A.coursetype_id and stuchange_code ='D04'))
                    order by A.changelog_createtime desc 
                    limit {$pagestart},{$num}";
        $changeList = $this->DataControl->selectClear($sql);
        if (!$changeList) {
            $changeList = array();
        }

        $all_num = $this->DataControl->selectOne("select count(A.changelog_id) as lost_num from smc_student_changelog A,smc_student B 
                where {$datawhere}
                AND A.student_id=B.student_id                
                and A.company_id='{$request['company_id']}' 
                and A.school_id='{$request['school_id']}'  
                and A.stuchange_code IN('C02','C04')  
                AND B.student_isdel <> '1' 
                and A.changelog_note=''
                AND NOT(A.stuchange_code IN('C02','C04') AND exists(select 1 from smc_student_changelog where change_pid>A.change_pid and student_id=A.student_id and school_id=A.school_id and stuchange_code in('A01','A06','D02','F01')))
                and not(A.stuchange_code='C04' AND exists(select 1 from smc_student_changelog where change_pid>A.change_pid and student_id=A.student_id and school_id=A.school_id and coursetype_id=A.coursetype_id and stuchange_code ='D04'))
              ");
        if ($all_num) {
            $result['all_num'] = $all_num['lost_num'];
        } else {
            $result['all_num'] = 0;
        }

        $result["field"] = $field;
        $result["list"] = $changeList;
        $res = array('error' => 0, 'errortip' => '获取异动明细成功', 'result' => $result);
        ajax_return($res, $request['language_type']);
    }

    function getChangeDetlApi()
    {
        $k = 0;
        $field = array();
        $field[$k]["fieldstring"] = "student_branch";
        $field[$k]["fieldname"] = "学员编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["fieldname"] = "学员中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_enname";
        $field[$k]["fieldname"] = "学员英文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "stuchange_name";
        $field[$k]["fieldname"] = "异动类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "changelog_day";
        $field[$k]["fieldname"] = "异动日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "changelog_note";
        $field[$k]["fieldname"] = "备注";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $request = Input('get.', '', 'trim,addslashes');
        $datawhere = " 1 ";
        $havingwhere = " ";
        $is_studnum = false;

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (B.student_cnname like '%{$request['keyword']}%' 
            or B.student_enname like '%{$request['keyword']}%' 
            or B.student_branch like '%{$request['keyword']}%')";
        }

        if (isset($request['start_time']) && $request['start_time'] !== '') {
            $time = $request['start_time'];
        }

        $week_start_day = date("Y-m-d", strtotime($time));
        $week_end_day = date("Y-m-d", strtotime("$week_start_day  +  6 days"));

        if (isset($request['change_type'])) {
            switch ($request['change_type']) {
                case "转入":
                    $datawhere .= " and A.stuchange_code in('A03','A04','A05')";
                    break;
                case "转出":
                    $datawhere .= " and A.stuchange_code in('B02','B03','B04','B07')";
                    break;
                case "延班":
                    $datawhere .= " and A.stuchange_code in('A07')";
                    break;
                case "流失":
                    $datawhere .= " and A.stuchange_code in('B01','C03')";
                    break;
                case "新生":
                    $datawhere .= " and A.stuchange_code in('A02')";
                    break;
                case "本周人数":
//                    $havingwhere .= " having reading_status=1";
                    $is_studnum = true;
                    $field[5]["show"] = 0;
                    $field[3]["show"] = 0;
                    $field[4]["show"] = 0;
                    break;
                default:
                    $datawhere .= " and FALSE ";
            }
        } else {
            $datawhere .= " and FALSE ";
        }

        $sql = "SELECT A.changelog_id,A.student_id,B.student_branch,B.student_cnname,B.student_enname,D.stuchange_name,A.changelog_day,A.changelog_note
            FROM smc_student_changelog A 
            LEFT JOIN smc_student B ON A.student_id=B.student_id AND A.company_id=B.company_id
            LEFT JOIN smc_class C ON A.class_id=C.class_id AND A.company_id=C.company_id
            LEFT JOIN smc_code_stuchange D ON A.stuchange_code=D.stuchange_code
            WHERE {$datawhere} 
             and A.company_id='{$request['company_id']}'  
             -- AND A.school_id='{$request['school_id']}'  
             AND C.class_branch='{$request['class_branch']}'  
             and A.changelog_day <= '{$week_end_day}' 
             and A.changelog_day >='{$week_start_day}'  
             order by a.changelog_id
        ";

//        $sqlall = "SELECT B.student_branch,B.student_cnname,B.student_enname,
//	            (case when a.study_endday>='{$week_end_day}' then IFNULL((SELECT Y.stustatus_inclass FROM smc_student_changelog AS X, smc_code_stuchange AS Y
//	            WHERE X.stuchange_code = Y.stuchange_code
//						AND Y.stuchange_type = 0 AND X.company_id = A.company_id AND X.school_id = A.school_id AND X.student_id = A.student_id
//						AND X.class_id = A.class_id AND X.changelog_day<='{$week_end_day}' ORDER BY changelog_id DESC  LIMIT 0,1 ),1) else a.study_isreading end) as reading_status
//                FROM smc_student_study A
//                LEFT JOIN smc_student B ON A.student_id=B.student_id AND A.company_id=B.company_id
//                left join smc_class C on A.class_id=C.class_id AND A.company_id=C.company_id
//                LEFT JOIN SMC_COURSE D ON C.COURSE_ID=D.COURSE_ID
//                WHERE {$datawhere}
//                and A.company_id='{$request['company_id']}'
//                AND C.class_branch='{$request['class_branch']}'
//
//                and (A.study_endday>='{$week_start_day}' OR A.study_endday='')
//                and A.study_beginday<='{$week_end_day}'
//                and NOT exists(select 1 from smc_student_changelog X,smc_code_stuchange Y
//										WHERE X.stuchange_code=Y.stuchange_code
//                                    AND (class_id=A.class_id or x.stuchange_code ='C02'
//                                    or (x.stuchange_code ='C04' and x.coursetype_id=D.coursetype_id))
//										AND student_id=A.student_id
//										AND changelog_day>=A.study_endday
//										and Y.stustatus_inclass='0'
//										AND changelog_day<= '{$week_end_day}'
//										AND changelog_day>= '{$week_start_day}')";
        $sqlall = "SELECT A.class_id,C.class_branch,B.student_branch,B.student_cnname,B.student_enname 
            FROM smc_student_study A 
            LEFT JOIN smc_student B ON A.student_id=B.student_id AND A.company_id=B.company_id 
            LEFT JOIN smc_class C ON A.class_id=C.class_id AND A.company_id=C.company_id 
            LEFT JOIN smc_course D ON C.course_id=D.course_id 
            LEFT JOIN smc_code_coursetype E ON E.coursetype_id = D.coursetype_id 
            LEFT JOIN smc_school F ON A.school_id=F.school_id 
            WHERE {$datawhere}
            AND A.company_id='{$request['company_id']}' 
            AND C.class_branch='{$request['class_branch']}' 
            AND C.class_type =0  
            AND C.class_status>'-2' 
            AND C.class_enddate>='{$week_start_day}' 
            AND C.class_stdate<='{$week_end_day}' 
            AND A.study_endday>='{$week_start_day}' 
            AND A.study_beginday<='{$week_end_day}' 
            AND E.coursetype_isopenclass=0 
            and (d.course_inclasstype<>2 
                or exists(select 1 from smc_class_booking x left join smc_class_hour y on x.hour_id=y.hour_id 
                    where x.class_id=a.class_id and x.student_id=a.student_id and y.hour_day>='{$week_start_day}' and y.hour_day<='{$week_end_day}' and x.booking_status=0))
            and NOT exists(select 1 from smc_student_study x,smc_class y,smc_course z 
                where x.class_id=y.class_id and y.course_id=z.course_id 
                and x.study_endday>='{$week_start_day}' and x.study_beginday<='{$week_end_day}' 
                and y.class_enddate>='{$week_start_day}' and y.class_stdate<='{$week_end_day}' 
                and x.student_id=A.student_id and x.class_id<>A.class_id 
                and y.school_id=C.school_id and x.study_beginday>A.study_beginday 
                and z.coursetype_id=E.coursetype_id and y.class_type=0 and y.class_status>-2)
        ";
        if ($is_studnum) {
            $sql = $sqlall;
        }
        $changeList = $this->DataControl->selectClear($sql);
        if (!$changeList) {
            $changeList = array();
        }

        $result["field"] = $field;
        $result["list"] = $changeList;
        $res = array('error' => 0, 'errortip' => '获取异动明细成功', 'result' => $result);
        ajax_return($res, $request['language_type']);
    }

    function getSchoolRenewalCalcApi()
    {
        $request = Input('get.', '', 'trim,addslashes');

        $datawhere = " 1  ";
        if (isset($request['company_id']) && $request['company_id'] !== '') {
            $datawhere .= " and a.company_id='{$request['company_id']}' ";
        }
        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and a.school_id='{$request['school_id']}' ";
        }
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (a.school_cnname like '%{$request['keyword']}%' 
            or a.school_branch like '%{$request['keyword']}%' )";
        }

        $sql = "select a.coursecat_id,a.coursecat_branch,a.coursecat_cnname
        from smc_student_course_estimate a 
        WHERE {$datawhere} 
        AND course_isrenew=1 
        AND a.class_enddate >= DATE_ADD(curdate(),interval -day(curdate())+1 day) 
        AND a.class_enddate <= last_day( date_add( curdate( ), INTERVAL 2 MONTH ) ) 
        AND channel_name='' 
        group by a.coursecat_id
        order by a.coursecat_id
        ";
        $coursecatArray = $this->DataControl->selectClear($sql);

        $coursecat = array();
        if ($coursecatArray) {
            $coursecat = array_column($coursecatArray, "coursecat_branch");
        }

        $sql = "select DATE_FORMAT(class_enddate,'%Y%m') as calc_month,DATE_FORMAT(class_enddate,'%m') as month_name
        from smc_student_course_estimate a 
        WHERE 1 
        AND course_isrenew=1 
        AND a.class_enddate >= DATE_ADD(curdate(),interval -day(curdate())+1 day) 
        AND a.class_enddate <= last_day( date_add( curdate( ), INTERVAL 2 MONTH ) ) 
        AND channel_name='' 
        group by calc_month
        order by calc_month
        ";
        $monthArray = $this->DataControl->selectClear($sql);

        $month = array();
        if ($monthArray) {
            $month = array_column($monthArray, "month_name");
        }

        $coursecatMonthArray = array();

        $k = 0;//如果要添加列，后面数字也要加上对应个数
        $field = array();
        $field[$k]["fieldstring"] = "school_id";
        $field[$k]["fieldname"] = "分校id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_shortname";
        $field[$k]["fieldname"] = "分校名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        if ($coursecatArray) {
            foreach ($coursecatArray as $coursecatRow) {
                foreach ($monthArray as $monthRow) {
                    $field[$k]["fieldstring"] = "percentage_" . ($k);
                    $field[$k]["fieldname"] = $monthRow['month_name'] . "月." . $coursecatRow['coursecat_branch'];
                    $field[$k]["show"] = 1;
                    $field[$k]["custom"] = 1;
                    $field[$k]["is_method"] = 1;
                    $field[$k]["coursecat_id"] = $coursecatRow['coursecat_id'];
                    $field[$k]["calc_month"] = $monthRow['calc_month'];
                    $k++;
                }
            }
        }


        if ($monthArray) {
            foreach ($monthArray as $key => $monthRow) {
                $field[$k]["fieldstring"] = "percentage_" . ($k);
                $field[$k]["fieldname"] = $monthRow['month_name'] . "月.实际";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $field[$k]["is_method"] = 1;
                $field[$k]["calc_month"] = $monthRow['calc_month'];
                $k++;

                $field[$k]["fieldstring"] = "percentage_" . ($k);
                $field[$k]["fieldname"] = $monthRow['month_name'] . "月.预估";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $field[$k]["is_method"] = 1;
                $field[$k]["calc_month"] = $monthRow['calc_month'];
                $k++;
            }
        }

        $field[$k]["fieldstring"] = "percentage_" . ($k);
        $field[$k]["fieldname"] = "总计.实际";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["is_method"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "percentage_" . ($k);
        $field[$k]["fieldname"] = "总计.预估";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["is_method"] = 1;
        $k++;

        $finalDataArray = array();
        $sql = "select a.school_id,a.school_branch,s.school_shortname,DATE_FORMAT(class_enddate,'%Y%m') as calc_month,
        a.coursecat_id,a.coursecat_branch,a.coursecat_cnname, 
        count(a.student_id) as total_count, 
        sum(if(((renewal_times>=a.coursetype_renewtimes and a.class_enddate<'2022-01-01') or(renewal_rates>=100 and a.class_enddate>='2022-01-01')),1,0)) as renewal_real_count, 
        sum(if(((renewal_times>=a.coursetype_renewtimes and a.class_enddate<'2022-01-01') or(renewal_rates>=100 and a.class_enddate>='2022-01-01')) or connect_times='续费',1,0)) as renewal_estimate_count 
        from smc_student_course_estimate a,smc_school AS s
        WHERE  {$datawhere} AND a.school_id = s.school_id
        AND a.course_isrenew=1
        AND a.class_enddate >= DATE_ADD(curdate(),interval -day(curdate())+1 day) 
        AND a.class_enddate <= last_day( date_add( curdate( ), INTERVAL 2 MONTH ) ) 
        AND channel_name=''
        AND a.class_num=a.course_class_num
        group by a.school_id,a.coursecat_id,calc_month 
        order by s.school_sort,a.school_id,a.coursecat_id,calc_month
        ";
        $mainDataArray = $this->DataControl->selectClear($sql);

        if (!$mainDataArray) {
            $finalDataArray = array();
        } else {

            $finalDataRow = array();
            $school_id = 0;
            $monthdata = array();

            $totalcalc_count = 0;
            $totalcalc_count_renewal = 0;
            $totalcalc_count_real_renewal = 0;


//            临时hardcode
            $pecentages = array();
            $pecentage = array();


            foreach ($mainDataArray as $mainDataRow) {
                if ($mainDataRow['school_id'] !== $school_id && $school_id !== 0) {
                    //分月%
                    if ($monthdata) {
                        foreach ($monthdata as $monthlydata) {
                            if ($monthlydata['total_count'] > 0) {
                                $finalDataRow['percentage_' . (count($coursecatArray) * count($monthArray) + $monthlydata['month_key'] * 2 + 3)] = round($monthlydata['renewal_real_count'] / $monthlydata['total_count'], 2) * 100;///
                                $finalDataRow['percentage_' . (count($coursecatArray) * count($monthArray) + $monthlydata['month_key'] * 2 + 4)] = round($monthlydata['renewal_count'] / $monthlydata['total_count'], 2) * 100;///
                                //合计平均
                                $pecentage[$monthlydata['month_key']]['real_numerator'] += $monthlydata['renewal_real_count'];
                                $pecentage[$monthlydata['month_key']]['estimate_numerator'] += $monthlydata['renewal_count'];
                                $pecentage[$monthlydata['month_key']]['denominator'] += $monthlydata['total_count'];
                            }
                        }
                    }
                    //总计%
                    $finalDataRow['percentage_' . ((count($coursecatArray) + 2) * count($monthArray) + 3)] = round($totalcalc_count_real_renewal / $totalcalc_count, 2) * 100;///
                    $finalDataRow['percentage_' . ((count($coursecatArray) + 2) * count($monthArray) + 4)] = round($totalcalc_count_renewal / $totalcalc_count, 2) * 100;///
                    $finalDataArray[] = $finalDataRow;

                    //合计平均
                    $pecentage[count($monthArray)]['real_numerator'] += $totalcalc_count_real_renewal;
                    $pecentage[count($monthArray)]['estimate_numerator'] += $totalcalc_count_renewal;
                    $pecentage[count($monthArray)]['denominator'] += $totalcalc_count;

                    $finalDataRow = array();
                    $totalcalc_count = 0;
                    $totalcalc_count_renewal = 0;
                    $totalcalc_count_real_renewal = 0;
                    $monthdata = array();
                }

                //新的学校--显示学校信息
                if ($school_id == 0 || $mainDataRow['school_id'] !== $school_id) {
                    $school_id = $mainDataRow['school_id'];

                    $finalDataRow['school_id'] = $mainDataRow['school_id'];
                    $finalDataRow['school_branch'] = $mainDataRow['school_branch'];
                    $finalDataRow['school_shortname'] = $mainDataRow['school_shortname'];

                    $monthdata = array();
                }

                if ($mainDataRow['school_id'] == $school_id) {
                    $month_key = array_search($mainDataRow['calc_month'], array_column($monthArray, 'calc_month'));
                    $coursecat_key = array_search($mainDataRow['coursecat_id'], array_column($coursecatArray, 'coursecat_id'));
                    if ($mainDataRow['total_count'] > 0) {
                        $finalDataRow['percentage_' . ($coursecat_key * count($monthArray) + $month_key + 3)] = round($mainDataRow['renewal_estimate_count'] / $mainDataRow['total_count'], 2) * 100 . '%';///
                    }

                    $totalcalc_count += $mainDataRow['total_count'];
                    $totalcalc_count_renewal += $mainDataRow['renewal_estimate_count'];
                    $totalcalc_count_real_renewal += $mainDataRow['renewal_real_count'];

                    if ($monthdata) {
                        $monthexists = 0;
                        foreach ($monthdata as $key => $monthlydata) {
                            if ($monthlydata['month_key'] == $month_key) {
                                $monthexists = 1;
                                $monthlydata['month_key'] = $month_key;
                                $monthlydata['total_count'] += $mainDataRow['total_count'];
                                $monthlydata['renewal_count'] += $mainDataRow['renewal_estimate_count'];
                                $monthlydata['renewal_real_count'] += $mainDataRow['renewal_real_count'];
                                $monthdata[$key] = $monthlydata;
                            }
                        }
                        if ($monthexists == 0 && $mainDataRow['total_count'] > 0) {
                            $monthlydata = array();
                            $monthlydata['month_key'] = $month_key;
                            $monthlydata['total_count'] += $mainDataRow['total_count'];
                            $monthlydata['renewal_count'] += $mainDataRow['renewal_estimate_count'];
                            $monthlydata['renewal_real_count'] += $mainDataRow['renewal_real_count'];
                            $monthdata[] = $monthlydata;
                        }
                    } else {
                        if ($mainDataRow['total_count'] > 0) {
                            $monthlydata = array();
                            $monthlydata['month_key'] = $month_key;
                            $monthlydata['total_count'] += $mainDataRow['total_count'];
                            $monthlydata['renewal_count'] += $mainDataRow['renewal_estimate_count'];
                            $monthlydata['renewal_real_count'] += $mainDataRow['renewal_real_count'];
                            $monthdata[] = $monthlydata;
                        }
                    }
                }
            }

            if ($monthdata) {
                foreach ($monthdata as $monthlydata) {
                    if ($monthlydata['total_count'] > 0) {
                        $finalDataRow['percentage_' . (count($coursecatArray) * count($monthArray) + $monthlydata['month_key'] * 2 + 3)] = round($monthlydata['renewal_real_count'] / $monthlydata['total_count'], 2) * 100;///
                        $finalDataRow['percentage_' . (count($coursecatArray) * count($monthArray) + $monthlydata['month_key'] * 2 + 4)] = round($monthlydata['renewal_count'] / $monthlydata['total_count'], 2) * 100;///

                        //合计平均
                        $pecentage[$monthlydata['month_key']]['real_numerator'] += $monthlydata['renewal_real_count'];
                        $pecentage[$monthlydata['month_key']]['estimate_numerator'] += $monthlydata['renewal_count'];
                        $pecentage[$monthlydata['month_key']]['denominator'] += $monthlydata['total_count'];
                    }
                }
            }
            $finalDataRow['percentage_' . ((count($coursecatArray) + 2) * count($monthArray) + 3)] = round($totalcalc_count_real_renewal / $totalcalc_count, 2) * 100;///
            $finalDataRow['percentage_' . ((count($coursecatArray) + 2) * count($monthArray) + 4)] = round($totalcalc_count_renewal / $totalcalc_count, 2) * 100;///

            $finalDataArray[] = $finalDataRow;

            //合计平均
            $pecentage[count($monthArray)]['real_numerator'] += $totalcalc_count_real_renewal;
            $pecentage[count($monthArray)]['estimate_numerator'] += $totalcalc_count_renewal;
            $pecentage[count($monthArray)]['denominator'] += $totalcalc_count;

            ksort($pecentage);
            foreach ($pecentage as $pecentageCol) {
                if ($pecentageCol['denominator'] > 0) {
                    $this_percentage = round($pecentageCol['real_numerator'] / $pecentageCol['denominator'], 2) * 100 . '%';
                    array_push($pecentages, $this_percentage);
                    $this_percentage = round($pecentageCol['estimate_numerator'] / $pecentageCol['denominator'], 2) * 100 . '%';
                    array_push($pecentages, $this_percentage);
                }
            }
        }

        $result["percentages"] = $pecentages;
        $result["coursecat"] = $coursecat;
        $result["month"] = $month;
        $result["field"] = $field;
        $result["list"] = $finalDataArray;
        $res = array('error' => 0, 'errortip' => '获取分校续费统计成功', 'result' => $result);
        ajax_return($res, $request['language_type']);
    }

    function getClassRenewalCalcApi()
    {
        $request = Input('get.', '', 'trim,addslashes');

        $datawhere = " 1  ";
        if (isset($request['company_id']) && $request['company_id'] !== '') {
            $datawhere .= " and a.company_id='{$request['company_id']}' ";
        }
        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and a.school_id='{$request['school_id']}' ";
        }
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (a.class_cnname like '%{$request['keyword']}%' 
            or a.class_enname like '%{$request['keyword']}%'
            or a.class_branch like '%{$request['keyword']}%' )";
        }

        $k = 0;//如果要添加列，后面数字也要加上对应个数
        $field = array();
        $field[$k]["fieldstring"] = "school_id";
        $field[$k]["fieldname"] = "分校id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "分校名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_id";
        $field[$k]["fieldname"] = "班级id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_branch";
        $field[$k]["fieldname"] = "班级编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursecat_branch";
        $field[$k]["fieldname"] = "班种编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "course_branch";
        $field[$k]["fieldname"] = "课程别编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_cnname";
        $field[$k]["fieldname"] = "班级名称";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_enname";
        $field[$k]["fieldname"] = "班级别称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_enddate";
        $field[$k]["fieldname"] = "班级结束日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "calc_month";
        $field[$k]["fieldname"] = "结班月份";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "main_teacher";
        $field[$k]["fieldname"] = "主教教师";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "total_count";
        $field[$k]["fieldname"] = "总人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "total_channel_count";
        $field[$k]["fieldname"] = "公益人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "renewal_estimate_count";
        $field[$k]["fieldname"] = "总续费人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "renewal_estimate_channel_count";
        $field[$k]["fieldname"] = "公益续费人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "total_common_count";
        $field[$k]["fieldname"] = "人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "renewal_real_common_count";
        $field[$k]["fieldname"] = "实际续费";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "renewal_estimate_common_count";
        $field[$k]["fieldname"] = "预估续费";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "renewal_real_common_count_percentage";
        $field[$k]["fieldname"] = "实际续费率";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "renewal_estimate_common_count_percentage";
        $field[$k]["fieldname"] = "预估续费率";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $finalDataArray = array();
        $sql = "select a.school_id,a.school_branch,a.school_cnname,s.school_tagbak,
        a.class_id,a.class_branch,a.class_cnname,a.class_enname,
        a.coursecat_id,a.coursecat_branch,a.coursecat_cnname,
        a.course_id,a.course_branch,a.course_cnname,
        a.class_enddate,DATE_FORMAT(class_enddate,'%Y%m') as calc_month,
        a.main_teacher,
        count(a.student_id) as total_count,
        sum(if(channel_name<>'',1,0)) as total_channel_count,
        sum(if(((renewal_times>=a.coursetype_renewtimes and a.class_enddate<'2022-01-01') or(renewal_rates>=100 and a.class_enddate>='2022-01-01')) or connect_times='续费',1,0)) as renewal_estimate_count,
        sum(if((((renewal_times>=a.coursetype_renewtimes and a.class_enddate<'2022-01-01') or(renewal_rates>=100 and a.class_enddate>='2022-01-01')) or connect_times='续费') and channel_name<>'',1,0)) as renewal_estimate_channel_count,
        sum(if(((renewal_times>=a.coursetype_renewtimes and a.class_enddate<'2022-01-01') or(renewal_rates>=100 and a.class_enddate>='2022-01-01')) and channel_name='',1,0)) as renewal_real_common_count
        from smc_student_course_estimate AS a,smc_school AS s
        WHERE {$datawhere} 
        and a.school_id = s.school_id AND a.course_isrenew=1
        AND a.class_enddate >= DATE_ADD(curdate(),interval -day(curdate())+1 day)
        AND a.class_enddate <= LAST_DAY( date_add( curdate( ), INTERVAL 2 MONTH ) )
        group by a.school_id,a.class_id
        order by a.school_branch,calc_month,a.coursecat_id,a.course_id,a.class_id
        ";
        $mainDataArray = $this->DataControl->selectClear($sql);

        foreach ($mainDataArray as &$var) {
            $var['total_common_count'] = $var['total_count'] - $var['total_channel_count'];
            $var['renewal_estimate_common_count'] = $var['renewal_estimate_count'] - $var['renewal_estimate_channel_count'];
            $var['renewal_real_common_count_percentage'] = $var['total_common_count'] > 0 ? round($var['renewal_real_common_count'] / $var['total_common_count'], 2) * 100 . '%' : "";///
            $var['renewal_estimate_common_count_percentage'] = $var['total_common_count'] > 0 ? round($var['renewal_estimate_common_count'] / $var['total_common_count'], 2) * 100 . '%' : "";///
        }

        $result["field"] = $field;
        $result["list"] = $mainDataArray;
        $res = array('error' => 0, 'errortip' => '获取班级续费统计成功', 'result' => $result);
        ajax_return($res, $request['language_type']);
    }

    function getStudentRenewalCalcApi()
    {
        $request = Input('get.', '', 'trim,addslashes');

        $datawhere = " 1  ";
        if (isset($request['company_id']) && $request['company_id'] !== '') {
            $datawhere .= " and a.company_id='{$request['company_id']}' ";
        }
        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and a.school_id='{$request['school_id']}' ";
        }
        if (isset($request['class_id']) && $request['class_id'] !== '') {
            $datawhere .= " and a.class_id='{$request['class_id']}' ";
        }
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (a.class_cnname like '%{$request['keyword']}%' 
            or a.class_enname like '%{$request['keyword']}%'
            or a.class_branch like '%{$request['keyword']}%' 
            or a.student_cnname like '%{$request['keyword']}%' 
            or a.student_enname like '%{$request['keyword']}%'
            or a.student_branch like '%{$request['keyword']}%' )";
        }

        $field = array();

        $k = 0;

        $field[$k]["fieldstring"] = "school_id";
        $field[$k]["fieldname"] = "学校id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_id";
        $field[$k]["fieldname"] = "学员id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "student_branch";
        $field[$k]["fieldname"] = "学员编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["fieldname"] = "学员中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_enname";
        $field[$k]["fieldname"] = "学员英文名";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "is_renewal";
        $field[$k]["fieldname"] = "是否续费";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "renewal_times";
        $field[$k]["fieldname"] = "班组剩余课次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "renewal_amount";
        $field[$k]["fieldname"] = "班组剩余金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "unpaid_price";
        $field[$k]["fieldname"] = "班组欠费金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "spend_price";
        $field[$k]["fieldname"] = "后续耗课金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "estimate_price";
        $field[$k]["fieldname"] = "续费评估金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "connect_times";
        $field[$k]["fieldname"] = "电访结果";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["ismethod"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "track_note";
        $field[$k]["fieldname"] = "沟通内容";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "channel_name";
        $field[$k]["fieldname"] = "专案名称";
        $field[$k]["show"] = (isset($request["company_id"]) && $request["company_id"] == '8888') ? 1 : 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_id";
        $field[$k]["fieldname"] = "班级id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "class_branch";
        $field[$k]["fieldname"] = "班级编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_cnname";
        $field[$k]["fieldname"] = "班级中文名";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_enname";
        $field[$k]["fieldname"] = "班级别名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_enddate";
        $field[$k]["fieldname"] = "班级结束日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "main_teacher";
        $field[$k]["fieldname"] = "主教教师";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        /*$field[$k]["fieldstring"] = "sub_teacher";
        $field[$k]["fieldname"] = "助教教师";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;*/

        $field[$k]["fieldstring"] = "course_branch";
        $field[$k]["fieldname"] = "课程别编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "course_cnname";
        $field[$k]["fieldname"] = "课程别名称";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursecat_branch";
        $field[$k]["fieldname"] = "班种编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursecat_cnname";
        $field[$k]["fieldname"] = "班种名称";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursetype_branch";
        $field[$k]["fieldname"] = "班组编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursetype_cnname";
        $field[$k]["fieldname"] = "班组名称";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $sql = "select a.*
        from smc_student_course_estimate a 
        WHERE {$datawhere} 
        and course_isrenew=1 

        order by a.school_branch,a.coursecat_id,a.course_id,a.class_id
        ";
//        AND a.class_enddate >= DATE_ADD(curdate(),interval -day(curdate())+1 day)
//        AND a.class_enddate <= LAST_DAY( date_add( curdate( ), INTERVAL 2 MONTH ) )
        $mainDataArray = $this->DataControl->selectClear($sql);

        $result["field"] = $field;
        $result["list"] = $mainDataArray;
        $res = array('error' => 0, 'errortip' => '获取学生续费明细成功', 'result' => $result);
        ajax_return($res, $request['language_type']);
    }

    function getSchoolRenewalCalcOldApi()
    {
        $request = Input('get.', '', 'trim,addslashes');

        $datawhere = " 1  ";
        if (isset($request['company_id']) && $request['company_id'] !== '') {
            $datawhere .= " and a.company_id='{$request['company_id']}' ";
        }
        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and a.school_id='{$request['school_id']}' ";
        }
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (a.school_cnname like '%{$request['keyword']}%' 
            or a.school_branch like '%{$request['keyword']}%' )";
        }

        $sql = "select a.coursecat_id,a.coursecat_branch,a.coursecat_cnname
        from smc_student_course_estimate a 
        WHERE {$datawhere} 
        AND course_isrenew=1 
        AND a.class_enddate >= date_add(curdate()-day(curdate())+1,interval -3 month)
        AND a.class_enddate <= last_day(curdate()) 
        AND channel_name='' 
        group by a.coursecat_id
        order by a.coursecat_id
        ";
        $coursecatArray = $this->DataControl->selectClear($sql);

        $coursecat = array();
        if ($coursecatArray) {
            $coursecat = array_column($coursecatArray, "coursecat_branch");
        }

        $sql = "select DATE_FORMAT(class_enddate,'%Y%m') as calc_month,DATE_FORMAT(class_enddate,'%m') as month_name
        from smc_student_course_estimate a 
        WHERE 1
        AND course_isrenew=1 
        AND a.class_enddate >= date_add(curdate()-day(curdate())+1,interval -3 month)
        AND a.class_enddate <= last_day(curdate()) 
        AND channel_name='' 
        group by calc_month
        order by calc_month
        ";
        $monthArray = $this->DataControl->selectClear($sql);

        $month = array();
        if ($monthArray) {
            $month = array_column($monthArray, "month_name");
        }

        $sql = "select a.school_id,DATE_FORMAT(b.class_enddate,'%Y%m') as calc_month,count(1) as breakoff_count
        from smc_class_breakoff a
        LEFT JOIN smc_class b ON A.class_id=B.class_id AND A.school_id=B.school_id AND A.company_id=B.company_id 
        where a.company_id='{$request['company_id']}'
        and a.breakoff_status>=2
        AND b.class_enddate >= date_add(curdate()-day(curdate())+1,interval -3 month)
        AND b.class_enddate <= last_day(curdate()) 
        group by a.school_id,calc_month
        ";
        $breakoffArray = $this->DataControl->selectClear($sql);

        $coursecatMonthArray = array();

        $k = 0;//如果要添加列，后面数字也要加上对应个数
        $field = array();
        $field[$k]["fieldstring"] = "school_id";
        $field[$k]["fieldname"] = "分校id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_shortname";
        $field[$k]["fieldname"] = "分校名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        if ($coursecatArray) {
            foreach ($monthArray as $monthRow) {
                foreach ($coursecatArray as $coursecatRow) {
                    $field[$k]["fieldstring"] = "percentage_" . ($k);
                    $field[$k]["fieldname"] = $monthRow['month_name'] . "月." . $coursecatRow['coursecat_branch'];
                    $field[$k]["show"] = 1;
                    $field[$k]["custom"] = 1;
                    $field[$k]["is_method"] = 1;
                    $field[$k]["coursecat_id"] = $coursecatRow['coursecat_id'];
                    $field[$k]["calc_month"] = $monthRow['calc_month'];
                    $k++;
                }
                $field[$k]["fieldstring"] = "percentage_" . ($k);
                $field[$k]["fieldname"] = $monthRow['month_name'] . "月.实际";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $field[$k]["is_method"] = 1;
                $field[$k]["calc_month"] = $monthRow['calc_month'];
                $k++;

                $field[$k]["fieldstring"] = "breakoff_count_" . ($k);
                $field[$k]["fieldname"] = $monthRow['month_name'] . "月.拆班数";
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $field[$k]["is_method"] = 1;
                $field[$k]["calc_month"] = $monthRow['calc_month'];
                $k++;
            }
        }

        $field[$k]["fieldstring"] = "percentage_" . ($k);
        $field[$k]["fieldname"] = "总计.实际";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["is_method"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "breakoff_count_total";
        $field[$k]["fieldname"] = "总计.拆班数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["is_method"] = 1;
        $k++;

        $finalDataArray = array();
        $sql = "select a.school_id,a.school_branch,s.school_shortname,DATE_FORMAT(class_enddate,'%Y%m') as calc_month,
        a.coursecat_id,a.coursecat_branch,a.coursecat_cnname, 
        count(a.student_id) as total_count, 
        sum(if(((renewal_times>=a.coursetype_renewtimes and a.class_enddate<'2022-01-01') or(renewal_rates>=100 and a.class_enddate>='2022-01-01')),1,0)) as renewal_real_count 
        from smc_student_course_estimate a,smc_school AS s
        WHERE  {$datawhere} 
        AND a.school_id = s.school_id
        AND a.course_isrenew=1
        AND a.class_enddate >= date_add(curdate()-day(curdate())+1,interval -3 month)
        AND a.class_enddate <= last_day(curdate()) 
        AND channel_name=''
        AND a.class_num=a.course_class_num
        group by a.school_id,a.coursecat_id,calc_month 
        order by s.school_sort,a.school_id,a.coursecat_id,calc_month
        ";
        $mainDataArray = $this->DataControl->selectClear($sql);

        if (!$mainDataArray) {
            $finalDataArray = array();
        } else {
            $finalDataRow = array();
            $school_id = 0;
            $monthdata = array();

            $totalcalc_count = 0;
            $totalcalc_count_real_renewal = 0;

            $breakoff_count_monthly = 0;
            $breakoff_count = 0;


//            临时hardcode
            $pecentages = array();
            $pecentage = array();


            foreach ($mainDataArray as $mainDataRow) {
                if ($mainDataRow['school_id'] !== $school_id && $school_id !== 0) {
                    //分月%
                    if ($monthdata) {
                        foreach ($monthdata as $monthlydata) {
                            if ($monthlydata['total_count'] > 0) {
                                $finalDataRow['percentage_' . ((count($coursecatArray) + 2) * ($monthlydata['month_key'] + 1) + 1)] = round($monthlydata['renewal_real_count'] / $monthlydata['total_count'], 2) * 100;///
                                $finalDataRow['breakoff_count_' . ((count($coursecatArray) + 2) * ($monthlydata['month_key'] + 1) + 2)] = $monthlydata['breakoff_count'] == 0 ? '' : $monthlydata['breakoff_count'];

//                                合计平均
                                $pecentage[$monthlydata['month_key']]['real_numerator'] += $monthlydata['renewal_real_count'];
                                $pecentage[$monthlydata['month_key']]['denominator'] += $monthlydata['total_count'];
                            }
                        }
                    }
                    //总计%
                    $finalDataRow['percentage_' . ((count($coursecatArray) + 2) * count($monthArray) + 3)] = round($totalcalc_count_real_renewal / $totalcalc_count, 2) * 100;///
                    $finalDataRow['breakoff_count_total'] = $breakoff_count;
                    $finalDataArray[] = $finalDataRow;


                    //合计平均
                    $pecentage[count($monthArray)]['real_numerator'] += $totalcalc_count_real_renewal;
                    $pecentage[count($monthArray)]['denominator'] += $totalcalc_count;


                    $finalDataRow = array();
                    $totalcalc_count = 0;
                    $totalcalc_count_real_renewal = 0;

                    $breakoff_count_monthly = 0;
                    $breakoff_count = 0;
                    $monthdata = array();
                }

                //新的学校--显示学校信息
                if ($school_id == 0 || $mainDataRow['school_id'] !== $school_id) {
                    $school_id = $mainDataRow['school_id'];

                    $finalDataRow['school_id'] = $mainDataRow['school_id'];
                    $finalDataRow['school_branch'] = $mainDataRow['school_branch'];
                    $finalDataRow['school_shortname'] = $mainDataRow['school_shortname'];

                    $monthdata = array();
                }

                if ($mainDataRow['school_id'] == $school_id) {
                    $month_key = array_search($mainDataRow['calc_month'], array_column($monthArray, 'calc_month'));
                    $coursecat_key = array_search($mainDataRow['coursecat_id'], array_column($coursecatArray, 'coursecat_id'));


                    if ($mainDataRow['total_count'] > 0) {
                        $finalDataRow['percentage_' . ((count($coursecatArray) + 2) * $month_key + $coursecat_key + 3)] = round($mainDataRow['renewal_real_count'] / $mainDataRow['total_count'], 2) * 100 . '%';///
                    }

                    foreach ($breakoffArray as $breakoffRow) {
                        if ($breakoffRow['school_id'] == $school_id && $breakoffRow['calc_month'] == $mainDataRow['calc_month']) {
                            $breakoff_count_monthly = $breakoffRow['breakoff_count'];
                        }
                    }

                    $totalcalc_count += $mainDataRow['total_count'];
                    $totalcalc_count_real_renewal += $mainDataRow['renewal_real_count'];
                    $breakoff_count += $breakoff_count_monthly;

                    if ($monthdata) {
                        $monthexists = 0;
                        foreach ($monthdata as $key => $monthlydata) {
                            if ($monthlydata['month_key'] == $month_key) {
                                $monthexists = 1;
                                $monthlydata['month_key'] = $month_key;
                                $monthlydata['coursecat_key'] = $coursecat_key;
                                $monthlydata['total_count'] += $mainDataRow['total_count'];
                                $monthlydata['renewal_real_count'] += $mainDataRow['renewal_real_count'];
                                $monthlydata['breakoff_count'] += $breakoff_count_monthly;
                                $monthdata[$key] = $monthlydata;
                            }
                        }
                        if ($monthexists == 0 && $mainDataRow['total_count'] > 0) {
                            $monthlydata = array();
                            $monthlydata['month_key'] = $month_key;
                            $monthlydata['coursecat_key'] = $coursecat_key;
                            $monthlydata['total_count'] += $mainDataRow['total_count'];
                            $monthlydata['renewal_real_count'] += $mainDataRow['renewal_real_count'];
                            $monthlydata['breakoff_count'] += $breakoff_count_monthly;
                            $monthdata[] = $monthlydata;
                        }
                    } else {
                        if ($mainDataRow['total_count'] > 0) {
                            $monthlydata = array();
                            $monthlydata['month_key'] = $month_key;
                            $monthlydata['coursecat_key'] = $coursecat_key;
                            $monthlydata['total_count'] += $mainDataRow['total_count'];
                            $monthlydata['renewal_real_count'] += $mainDataRow['renewal_real_count'];
                            $monthlydata['breakoff_count'] += $breakoff_count_monthly;
                            $monthdata[] = $monthlydata;
                        }
                    }
                }
            }

            if ($monthdata) {
                foreach ($monthdata as $monthlydata) {
                    if ($monthlydata['total_count'] > 0) {
                        $finalDataRow['percentage_' . ((count($coursecatArray) + 2) * ($monthlydata['month_key'] + 1) + 1)] = round($monthlydata['renewal_real_count'] / $monthlydata['total_count'], 2) * 100;///
                        $finalDataRow['breakoff_count_' . ((count($coursecatArray) + 2) * ($monthlydata['month_key'] + 1) + 2)] = $monthlydata['breakoff_count'] = 0 ? '' : $monthlydata['breakoff_count'];

//                                合计平均
                        $pecentage[$monthlydata['month_key']]['real_numerator'] += $monthlydata['renewal_real_count'];
                        $pecentage[$monthlydata['month_key']]['denominator'] += $monthlydata['total_count'];
                    }
                }
            }

            $finalDataRow['percentage_' . ((count($coursecatArray) + 2) * count($monthArray) + 3)] = round($totalcalc_count_real_renewal / $totalcalc_count, 2) * 100;///
            $finalDataRow['breakoff_count_total'] = $breakoff_count == 0 ? '' : $breakoff_count;

            $finalDataArray[] = $finalDataRow;

            //合计平均
            $pecentage[count($monthArray)]['real_numerator'] += $totalcalc_count_real_renewal;
            $pecentage[count($monthArray)]['denominator'] += $totalcalc_count;


            ksort($pecentage);
            foreach ($pecentage as $pecentageCol) {
                if ($pecentageCol['denominator'] > 0) {
                    $this_percentage = round($pecentageCol['real_numerator'] / $pecentageCol['denominator'], 2) * 100 . '%';
                    array_push($pecentages, $this_percentage);
                }
            }
        }

        $result["pecentages"] = $pecentages;
        $result["coursecat"] = $coursecat;
        $result["month"] = $month;
        $result["field"] = $field;
        $result["list"] = $finalDataArray;
        $res = array('error' => 0, 'errortip' => '获取分校续费统计成功', 'result' => $result);
        ajax_return($res, $request['language_type']);
    }

    function getClassRenewalCalcOldApi()
    {
        $request = Input('get.', '', 'trim,addslashes');

        $datawhere = " 1  ";
        if (isset($request['company_id']) && $request['company_id'] !== '') {
            $datawhere .= " and a.company_id='{$request['company_id']}' ";
        }
        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and a.school_id='{$request['school_id']}' ";
        }
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (a.class_cnname like '%{$request['keyword']}%' 
            or a.class_enname like '%{$request['keyword']}%'
            or a.class_branch like '%{$request['keyword']}%' )";
        }

        $k = 0;//如果要添加列，后面数字也要加上对应个数
        $field = array();
        $field[$k]["fieldstring"] = "school_id";
        $field[$k]["fieldname"] = "分校id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "分校名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_id";
        $field[$k]["fieldname"] = "班级id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_branch";
        $field[$k]["fieldname"] = "班级编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursecat_branch";
        $field[$k]["fieldname"] = "班种编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "course_branch";
        $field[$k]["fieldname"] = "课程别编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_cnname";
        $field[$k]["fieldname"] = "班级名称";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_enname";
        $field[$k]["fieldname"] = "班级别称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_enddate";
        $field[$k]["fieldname"] = "班级结束日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "calc_month";
        $field[$k]["fieldname"] = "结班月份";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "main_teacher";
        $field[$k]["fieldname"] = "主教教师";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "total_count";
        $field[$k]["fieldname"] = "总人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "total_channel_count";
        $field[$k]["fieldname"] = "公益人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "renewal_estimate_count";
        $field[$k]["fieldname"] = "总续费人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "renewal_estimate_channel_count";
        $field[$k]["fieldname"] = "公益续费人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "total_common_count";
        $field[$k]["fieldname"] = "人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "renewal_real_common_count";
        $field[$k]["fieldname"] = "实际续费";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "renewal_estimate_common_count";
        $field[$k]["fieldname"] = "预估续费";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "renewal_real_common_count_percentage";
        $field[$k]["fieldname"] = "实际续费率";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "renewal_estimate_common_count_percentage";
        $field[$k]["fieldname"] = "预估续费率";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $finalDataArray = array();
        $sql = "select a.school_id,a.school_branch,a.school_cnname,s.school_tagbak,
        a.class_id,a.class_branch,a.class_cnname,a.class_enname,
        a.coursecat_id,a.coursecat_branch,a.coursecat_cnname,
        a.course_id,a.course_branch,a.course_cnname,
        a.class_enddate,DATE_FORMAT(class_enddate,'%Y%m') as calc_month,
        a.main_teacher,
        count(a.student_id) as total_count,
        sum(if(channel_name<>'',1,0)) as total_channel_count,
        sum(if(((renewal_times>=a.coursetype_renewtimes and a.class_enddate<'2022-01-01') or(renewal_rates>=100 and a.class_enddate>='2022-01-01')) or connect_times='续费',1,0)) as renewal_estimate_count,
        sum(if((((renewal_times>=a.coursetype_renewtimes and a.class_enddate<'2022-01-01') or(renewal_rates>=100 and a.class_enddate>='2022-01-01')) or connect_times='续费') and channel_name<>'',1,0)) as renewal_estimate_channel_count,
        sum(if(((renewal_times>=a.coursetype_renewtimes and a.class_enddate<'2022-01-01') or(renewal_rates>=100 and a.class_enddate>='2022-01-01')) and channel_name='',1,0)) as renewal_real_common_count
        from smc_student_course_estimate AS a,smc_school AS s
        WHERE {$datawhere} 
        and a.school_id = s.school_id AND a.course_isrenew=1
        AND a.class_enddate >= date_add(curdate()-day(curdate())+1,interval -3 month)
        AND a.class_enddate <= last_day(curdate()) 
        group by a.school_id,a.class_id
        order by a.school_branch,calc_month,a.coursecat_id,a.course_id,a.class_id
        ";
        $mainDataArray = $this->DataControl->selectClear($sql);

        foreach ($mainDataArray as &$var) {
            $var['total_common_count'] = $var['total_count'] - $var['total_channel_count'];
            $var['renewal_estimate_common_count'] = $var['renewal_estimate_count'] - $var['renewal_estimate_channel_count'];
            $var['renewal_real_common_count_percentage'] = $var['total_common_count'] > 0 ? round($var['renewal_real_common_count'] / $var['total_common_count'], 2) * 100 . '%' : "";///
            $var['renewal_estimate_common_count_percentage'] = $var['total_common_count'] > 0 ? round($var['renewal_estimate_common_count'] / $var['total_common_count'], 2) * 100 . '%' : "";///
        }

        $result["field"] = $field;
        $result["list"] = $mainDataArray;
        $res = array('error' => 0, 'errortip' => '获取班级续费统计成功', 'result' => $result);
        ajax_return($res, $request['language_type']);
    }

    function getSchoolConnectSummaryApi()
    {
        $request = Input('get.', '', 'trim,addslashes');

        $datawhere = "a.school_branch = s.school_branch";
        if (isset($request['company_id']) && $request['company_id'] !== '') {
            $datawhere .= " and a.company_id='{$request['company_id']}' ";
        }
        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and a.school_id='{$request['school_id']}' ";
        }

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (a.school_branch like '%{$request['keyword']}%' 
            or a.school_cnname like '%{$request['keyword']}%' )";
        }

        if (isset($request['end_time']) && $request['end_time'] !== '') {
            $lastday = date("Y-m-t", strtotime($request['end_time']));
            $datawhere .= " and a.class_enddate<= '{$lastday}'";
        }

        if (isset($request['start_time']) && $request['start_time'] !== '') {
            $firstday = date("Y-m-01", strtotime($request['start_time']));
            $datawhere .= " and a.class_enddate>= '{$firstday}'";
        }

        $field = array();

        $k = 0;

        $field[$k]["fieldstring"] = "school_id";
        $field[$k]["fieldname"] = "学校id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "calc_month";
        $field[$k]["fieldname"] = "月份";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "channel_count";
        $field[$k]["fieldname"] = "公益人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "channel_renewal_count";
        $field[$k]["fieldname"] = "公益学生续费人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "channel_renewal_percentage";
        $field[$k]["fieldname"] = "公益学生续费率";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "total_count";
        $field[$k]["fieldname"] = "升级留班班级人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "renewal_count";
        $field[$k]["fieldname"] = "已续费人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "unpaid_count";
        $field[$k]["fieldname"] = "未缴费电访人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

//        $field[$k]["fieldstring"] = "connect_count";
//        $field[$k]["fieldname"] = "应电访人数";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//        $k++;

        $field[$k]["fieldstring"] = "connect_renewal_count";
        $field[$k]["fieldname"] = "电访表示续费人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "connect_consider_count";
        $field[$k]["fieldname"] = "电访表示考虑人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "connect_refuse_count";
        $field[$k]["fieldname"] = "电访确认流失";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "connect_notyet_count";
        $field[$k]["fieldname"] = "未进行电访人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "connect_percentage";
        $field[$k]["fieldname"] = "电访执行率";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "estimate_percentage";
        $field[$k]["fieldname"] = "预估本月留班率";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "renewal_percentage";
        $field[$k]["fieldname"] = "目前续费率";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;


        $sql = "select a.school_id,a.school_branch,s.school_shortname AS school_cnname,
        DATE_FORMAT(class_enddate,'%Y-%m') as calc_month,
            sum(if(a.channel_name<>'',1,0)) as channel_count,
            sum(if(((renewal_times>=a.coursetype_renewtimes and a.class_enddate<'2022-01-01') or(renewal_rates>=100 and a.class_enddate>='2022-01-01')) and a.channel_name<>'',1,0)) as channel_renewal_count,
            sum(if(a.channel_name='',1,0)) as total_count,
            sum(if(((renewal_times>=a.coursetype_renewtimes and a.class_enddate<'2022-01-01') or(renewal_rates>=100 and a.class_enddate>='2022-01-01')) and channel_name='',1,0)) as renewal_count,
            sum(if((connect_times<>'' or ((renewal_times<a.coursetype_renewtimes and a.class_enddate<'2022-01-01') or(renewal_rates<100 and a.class_enddate>='2022-01-01'))) and channel_name='',1,0)) as connect_count,
            sum(if(connect_times='续费' and ((renewal_times<a.coursetype_renewtimes and a.class_enddate<'2022-01-01') or(renewal_rates<100 and a.class_enddate>='2022-01-01')) and channel_name='',1,0)) as connect_renewal_count,
            sum(if(connect_times like '%考虑%' and ((renewal_times<a.coursetype_renewtimes and a.class_enddate<'2022-01-01') or(renewal_rates<100 and a.class_enddate>='2022-01-01')) and channel_name='',1,0)) as connect_consider_count,
            sum(if(connect_times like '%流失%' and ((renewal_times<a.coursetype_renewtimes and a.class_enddate<'2022-01-01') or(renewal_rates<100 and a.class_enddate>='2022-01-01')) and channel_name='',1,0)) as connect_refuse_count,
            sum(if(connect_times='' and ((renewal_times<a.coursetype_renewtimes and a.class_enddate<'2022-01-01') or(renewal_rates<100 and a.class_enddate>='2022-01-01')) and channel_name='',1,0)) as connect_notyet_count
        from smc_student_course_estimate as a,smc_school AS s
        WHERE {$datawhere} 
        and course_isrenew=1 
        group by a.school_id,calc_month
        order by a.school_branch,calc_month
        ";
        $mainDataArray = $this->DataControl->selectClear($sql);

        foreach ($mainDataArray as &$var) {
            $var['channel_renewal_percentage'] = $var['channel_count'] > 0 ? (round($var['channel_renewal_count'] / $var['channel_count'], 2) * 100 . "%") : '---';
            $var['unpaid_count'] = $var['total_count'] - $var['renewal_count'];
            $var['connect_percentage'] = $var['total_count'] > 0 ? (round(($var['total_count'] - $var['connect_notyet_count']) / $var['total_count'], 2) * 100 . "%") : '---';
            $var['estimate_percentage'] = $var['total_count'] > 0 ? (round(($var['renewal_count'] + $var['connect_renewal_count']) / $var['total_count'], 2) * 100 . "%") : '---';
            $var['renewal_percentage'] = $var['total_count'] > 0 ? (round($var['renewal_count'] / $var['total_count'], 2) * 100 . "%") : '---';
        }

        $result["field"] = $field;
        $result["list"] = $mainDataArray;
        $res = array('error' => 0, 'errortip' => '获取续费统计成功', 'result' => $result);
        ajax_return($res, $request['language_type']);
    }

    function getSchoolMonthlySummaryApi()
    {
        $request = Input('get.', '', 'trim,addslashes');

        $datawhere = " 1 ";
        if (isset($request['company_id']) && $request['company_id'] !== '') {
            $datawhere .= " and a.company_id='{$request['company_id']}' ";
        }
        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and a.school_id='{$request['school_id']}' ";
        }

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (a.school_branch like '%{$request['keyword']}%' 
            or a.school_cnname like '%{$request['keyword']}%' )";
        }

        $firstSeconds = strtotime(date("Y-m-01 00:00:00"));
        $lastSeconds = strtotime(date("Y-m-d 23:59:59"));

        $field = array();

        $k = 0;

        $field[$k]["fieldstring"] = "school_id";
        $field[$k]["fieldname"] = "学校id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "school_tagbak";
        $field[$k]["fieldname"] = "督导区域";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_shortname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "register_count_this";
        $field[$k]["fieldname"] = "当月新生";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "lost_count_this";
        $field[$k]["fieldname"] = "当月流失";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "rece_total";
        $field[$k]["fieldname"] = "当月收费金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "expend_toatal";
        $field[$k]["fieldname"] = "当月退款金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "net_rece_total";
        $field[$k]["fieldname"] = "净收费金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "income_toatal";
        $field[$k]["fieldname"] = "本月实际收入";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "estimate_percentage_this";
        $field[$k]["fieldname"] = "本月预估续费率";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "real_percentage_this";
        $field[$k]["fieldname"] = "本月实际续费率";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "estimate_percentage_next";
        $field[$k]["fieldname"] = "下月预估续费率";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "real_percentage_next";
        $field[$k]["fieldname"] = "下月实际续费率";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "reading_count_now";
        $field[$k]["fieldname"] = "在读学生数量";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "register_count_now";
        $field[$k]["fieldname"] = "在籍学生数量";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "reading_percentage";
        $field[$k]["fieldname"] = "实际在读学员比例";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;


        $sql = "select a.school_id,a.school_tagbak,a.school_branch,a.school_cnname,a.school_shortname,
            (select count(1) from smc_student_registerinfo x 
            where info_status=1 and x.school_id=a.school_id and x.pay_successtime <='{$lastSeconds}' and x.pay_successtime>='{$firstSeconds}') as register_count_this,
            (select count(distinct student_id) from smc_student_changelog x 
            where x.school_id=a.school_id and x.stuchange_code in ('B05','C02') and x.changelog_day<=curdate() and x.changelog_day>=DATE_ADD(curdate(),interval -day(curdate())+1 day)) as lost_count_this,
            (select sum(x.pay_price) from smc_payfee_order_pay x,smc_code_paytype y,smc_payfee_order z 
            where z.school_id=a.school_id and x.paytype_code=y.paytype_code and x.order_pid=z.order_pid and y.paytype_ischarge=1 and x.pay_issuccess=1 and x.pay_successtime<='{$lastSeconds}' and x.pay_successtime>='{$firstSeconds}')as rece_total,
            (select sum(x.refund_payprice) from smc_refund_order x 
            where x.refund_status>=0 and x.school_id=a.school_id and x.refund_createtime<='{$lastSeconds}' and x.refund_createtime>='{$firstSeconds}' ) as refund_total,
            (select sum(expend_price) from smc_school_expend x 
            where x.school_id=a.school_id and expend_type='2' and x.expend_confirmtime<='{$lastSeconds}' and x.expend_confirmtime>='{$firstSeconds}') as expend_toatal,
            (select sum(income_price) from smc_school_income x 
            where x.school_id=a.school_id and x.income_confirmtime<='{$lastSeconds}' and x.income_confirmtime>='{$firstSeconds}') as income_toatal,
            (select sum(single) from (SELECT x.school_id,count(distinct x.student_id) as single FROM smc_student_coursebalance x,smc_course y 
            WHERE x.course_id = y.course_id and x.coursebalance_figure > 0 GROUP BY x.school_id,y.coursetype_id) as ta where ta.school_id=a.school_id) as register_count_now
            from smc_school a 
            where {$datawhere}
            AND a.company_id='8888' 
            and a.school_istest<>'1' 
            and a.school_isclose<>'1' 
            and a.school_type='1' 
            order by (case when a.school_istest=0 and a.school_isclose=0 then 1 when a.school_isclose=0 then 2 when a.school_istest=0 then 3 else 4 end)
            ,a.school_istest asc,field(a.school_sort,0),a.school_sort asc,a.school_createtime asc  
        ";

        $mainDataArray = $this->DataControl->selectClear($sql);

        $sql = "SELECT
             TA.school_id,COUNT(1) AS reading_count_now
            FROM
            (
              SELECT
               X.student_id,
               X.school_id,
               Z.coursetype_id,
               MIN(X.study_beginday) AS beginday,
               MAX(X.study_endday) AS endday
              FROM
               smc_student_study AS X,
               smc_class AS Y,
               smc_course AS Z,
               smc_code_coursetype W 
              WHERE
               X.class_id = Y.class_id
               AND Y.course_id = Z.course_id
               AND Z.coursetype_id=W.coursetype_id
               AND X.study_endday > DATE_SUB(curdate(), INTERVAL W.coursetype_intervaltime DAY) 
               AND X.company_id = '8888'
               AND Y.class_type='0' AND Y.class_status>'-2'
              GROUP BY
               X.school_id,
               X.student_id,
               Z.coursetype_id
             ) AS TA
            WHERE
             TA.beginday <=curdate()
             AND TA.endday >=curdate()
            GROUP BY TA.school_id";
        $readingDataArray = $this->DataControl->selectClear($sql);

        $sql = "select a.school_id,
            (case when DATE_FORMAT(class_enddate,'%Y-%m')=DATE_FORMAT(CURDATE(),'%Y-%m') then 0 else 1 end)as month_tag,
            count(a.student_id) as total_count, 
            count(if(((renewal_times>=a.coursetype_renewtimes and a.class_enddate<'2022-01-01') or(renewal_rates>=100 and a.class_enddate>='2022-01-01')),TRUE,NULL)) as renewal_real_count, 
            count(if(((renewal_times>=a.coursetype_renewtimes and a.class_enddate<'2022-01-01') or(renewal_rates>=100 and a.class_enddate>='2022-01-01')) or connect_times='续费',TRUE,NULL)) as renewal_estimate_count 
            from smc_student_course_estimate a
            WHERE a.company_id='8888' 
            AND a.course_isrenew=1
            AND a.class_enddate >= DATE_ADD(curdate(),interval -day(curdate())+1 day) 
            AND a.class_enddate <= last_day( date_add( curdate( ), INTERVAL 1 MONTH ) ) 
            AND channel_name=''
            AND a.class_num=a.course_class_num
            group by a.school_id,DATE_FORMAT(class_enddate,'%Y-%m')  
        ";
        $subDataArray = $this->DataControl->selectClear($sql);

        $estimate_count_this = 0;
        $real_count_this = 0;
        $total_count_this = 0;
        $estimate_count_next = 0;
        $real_count_next = 0;
        $total_count_next = 0;
        foreach ($mainDataArray as &$var) {
            $var['expend_toatal'] = ceil($var['expend_toatal']);
            $var['refund_total'] = ceil($var['refund_total']);
            $var['rece_total'] = ceil($var['rece_total']);
            $var['income_toatal'] = ceil($var['income_toatal']);
            $var['expend_toatal'] = $var['expend_toatal'] + $var['refund_total'];
            $var['net_rece_total'] = $var['rece_total'] - $var['expend_toatal'];
            if ($subDataArray) {
                foreach ($subDataArray as $subvar) {
                    if ($subvar['school_id'] == $var['school_id']) {
                        if ($subvar['month_tag'] == '0') {
                            $var['estimate_percentage_this'] = round($subvar['renewal_estimate_count'] / $subvar['total_count'], 2) * 100;
                            $var['real_percentage_this'] = round($subvar['renewal_real_count'] / $subvar['total_count'], 2) * 100;
                            $estimate_count_this += $subvar['renewal_estimate_count'];
                            $real_count_this += $subvar['renewal_real_count'];
                            $total_count_this += $subvar['total_count'];
                        }
                        if ($subvar['month_tag'] == '1') {
                            $var['estimate_percentage_next'] = round($subvar['renewal_estimate_count'] / $subvar['total_count'], 2) * 100;
                            $var['real_percentage_next'] = round($subvar['renewal_real_count'] / $subvar['total_count'], 2) * 100;
                            $estimate_count_next += $subvar['renewal_estimate_count'];
                            $real_count_next += $subvar['renewal_real_count'];
                            $total_count_next += $subvar['total_count'];
                        }
                    }
                }
                foreach ($readingDataArray as $readingvar) {
                    if ($readingvar['school_id'] == $var['school_id']) {
                        $var['reading_count_now'] = $readingvar['reading_count_now'];
                    }
                }
            }
            if ($var['reading_count_now'] > 0 && $var['register_count_now'] > 0) {
                $var['reading_percentage'] = round($var['reading_count_now'] / $var['register_count_now'], 2) * 100;
            } else {
                $var['reading_percentage'] = "0";
            }
        }

        $reneal = array();
        $j = 0;
        $reneal[$j] = round($estimate_count_this / $total_count_this, 2) * 100 . "%";
        $j++;
        $reneal[$j] = round($real_count_this / $total_count_this, 2) * 100 . "%";
        $j++;
        $reneal[$j] = round($estimate_count_next / $total_count_next, 2) * 100 . "%";
        $j++;
        $reneal[$j] = round($real_count_next / $total_count_next, 2) * 100 . "%";
        $j++;


        if ($request['is_export'] == '1') {

            $outexceldate = array();
            if ($mainDataArray) {
                $outexceldate = array();
                foreach ($mainDataArray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['school_id'] = $dateexcelvar['school_id'];//学校ID
                    $datearray['school_shortname'] = $dateexcelvar['school_shortname'];//校区名称
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];//校区编号
                    $datearray['register_count_this'] = $dateexcelvar['register_count_this'];//当月新生
                    $datearray['lost_count_this'] = $dateexcelvar['lost_count_this'];//当月流失
                    $datearray['rece_total'] = $dateexcelvar['rece_total'];//当月收费金额
                    $datearray['expend_toatal'] = $dateexcelvar['expend_toatal'];//当月退款金额
                    $datearray['net_rece_total'] = $dateexcelvar['net_rece_total'];//净收费金额
                    $datearray['income_toatal'] = $dateexcelvar['income_toatal'];//本月实际收入
                    $datearray['estimate_percentage_this'] = $dateexcelvar['estimate_percentage_this'];//本月预估续费率
                    $datearray['real_percentage_this'] = $dateexcelvar['real_percentage_this'];//本月实际续费率
                    $datearray['estimate_percentage_next'] = $dateexcelvar['estimate_percentage_next'];//下月预估续费率
                    $datearray['real_percentage_next'] = $dateexcelvar['real_percentage_next'];//下月实际续费率
                    $datearray['reading_count_now'] = $dateexcelvar['reading_count_now'];//在读学生数量
                    $datearray['register_count_now'] = $dateexcelvar['register_count_now'];//在籍学生数量
                    $datearray['reading_percentage'] = $dateexcelvar['reading_percentage'];//实际在读学员比例
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array('学校ID', '校区名称', '校区编号', '当月新生', '当月流失', '当月收费金额', '当月退款金额', '净收费金额', '本月实际收入', '本月预估续费率', '本月实际续费率', '下月预估续费率', '下月实际续费率', '在读学生数量', '在籍学生数量', '实际在读学员比例'));
            $excelfileds = array('school_id', 'school_shortname', 'school_branch', 'register_count_this', 'lost_count_this', 'rece_total', 'expend_toatal', 'net_rece_total', 'income_toatal', 'estimate_percentage_this', 'real_percentage_this', 'estimate_percentage_next', 'real_percentage_next', 'reading_count_now', 'register_count_now', 'reading_percentage');

            $fielname = $this->LgStringSwitch("月度kpi统计");
            query_to_excel($excelheader, $outexceldate, $excelfileds, "{$fielname}.xlsx");
            exit;
        }

        $result["reneal_summary"] = $reneal;
        $result["field"] = $field;
        $result["list"] = $mainDataArray;
        $res = array('error' => 0, 'errortip' => '获取月度kpi统计成功', 'result' => $result);
        ajax_return($res, $request['language_type']);
    }

    function getSchoolMonthlyStudentSummaryApi()
    {
        $request = Input('get.', '', 'trim,addslashes');

        $datawhere = " 1 ";
        if (isset($request['company_id']) && $request['company_id'] !== '') {
            $datawhere .= " and a.company_id='{$request['company_id']}' ";
        }
        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and a.school_id='{$request['school_id']}' ";
        }

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (school_branch like '%{$request['keyword']}%' 
            or school_cnname like '%{$request['keyword']}%' )";
        }

        $field = array();

        $k = 0;

        $field[$k]["fieldstring"] = "school_id";
        $field[$k]["fieldname"] = "学校id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "school_tagbak";
        $field[$k]["fieldname"] = "督导区域";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_shortname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "reading_count_now";
        $field[$k]["fieldname"] = "在读学员数量";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "wait_count_now";
        $field[$k]["fieldname"] = "等班学员数量";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "new_count_now";
        $field[$k]["fieldname"] = "新生待入班学员";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "register_count_now";
        $field[$k]["fieldname"] = "在籍学生数量";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "reading_percentage";
        $field[$k]["fieldname"] = "在读比率";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $sql = "select a.school_id,a.school_tagbak,a.school_branch,a.school_cnname,a.school_shortname,
            (select sum(single) from (SELECT x.school_id,count(distinct x.student_id) as single FROM smc_student_coursebalance x,smc_course y 
            WHERE x.course_id = y.course_id and x.coursebalance_figure > 0 GROUP BY x.school_id,y.coursetype_id) as ta where ta.school_id=a.school_id) as register_count_now
            from smc_school a 
            where {$datawhere}
            and a.company_id='8888' 
            and a.school_istest<>'1' 
            and a.school_isclose<>'1' 
            and a.school_type='1' 
            order by (case when a.school_istest=0 and a.school_isclose=0 then 1 when a.school_isclose=0 then 2 when a.school_istest=0 then 3 else 4 end)
            ,a.school_istest asc,field(a.school_sort,0),a.school_sort asc,a.school_createtime asc  
        ";
//        ,(select count(student_id) from (select x.school_id,x.student_id from smc_student_study x
//            where x.study_beginday<=curdate() and x.study_endday>=curdate() group by x.student_id) as ta where ta.school_id=a.school_id) as reading_count_now
        $mainDataArray = $this->DataControl->selectClear($sql);

        $sql = "SELECT
             TA.school_id,COUNT(1) AS reading_count_now
            FROM
            (
              SELECT
               X.student_id,
               X.school_id,
               Z.coursetype_id,
               MIN(X.study_beginday) AS beginday,
               MAX(X.study_endday) AS endday
              FROM
               smc_student_study AS X,
               smc_class AS Y,
               smc_course AS Z,
               smc_code_coursetype W
              WHERE
               X.class_id = Y.class_id
               AND Y.course_id = Z.course_id
               AND Z.coursetype_id=W.coursetype_id
               AND X.study_endday > DATE_SUB(curdate(), INTERVAL W.coursetype_intervaltime DAY) 
               AND X.company_id = '8888'
               AND Y.class_type='0' AND Y.class_status>'-2'
              GROUP BY
               X.school_id,
               X.student_id,
               Z.coursetype_id
             ) AS TA
            WHERE
             TA.beginday <=curdate()
             AND TA.endday >=curdate()
            GROUP BY TA.school_id";
        $readingDataArray = $this->DataControl->selectClear($sql);

        $sql = "select TA.school_id,
            COUNT(IF(atte_count>0,TRUE,NULL)) AS wait_count,
            COUNT(IF(atte_count=0,TRUE,NULL)) AS new_count
            FROM(
                SELECT A.school_id 
                ,B.coursecat_id 
                ,A.student_id 
                ,(select count(X.hourstudy_id) from smc_student_hourstudy X,smc_class Y,smc_course Z 
                WHERE X.student_id=A.student_id AND X.class_id=Y.class_id AND Y.course_id=Z.course_id AND Z.coursecat_id=B.coursecat_id AND Y.school_id=A.school_id) as atte_count
                FROM smc_student_coursebalance A  
                LEFT JOIN smc_course B ON A.course_id=B.course_id 
                LEFT JOIN smc_school C ON A.school_id=C.school_id
                WHERE  A.company_id='8888' 
                AND C.school_istest<>'1' 
                AND C.school_isclose<>'1' 
                and C.school_type='1' 
                AND B.course_inclasstype in (0,1) 
                AND A.coursebalance_time>0 
                AND NOT EXISTS(SELECT 1 FROM smc_student_study X,smc_class Y,smc_course Z 
                WHERE X.student_id=A.student_id  AND X.class_id=Y.class_id 
                AND Y.course_id=Z.course_id  AND Z.coursecat_id=B.coursecat_id 
                AND X.study_isreading=1  AND X.study_endday>=CURDATE()) 						
                GROUP BY A.school_id,A.student_id,B.coursecat_id 
            ) AS TA 
            GROUP BY TA.school_id
        ";
        $subDataArray = $this->DataControl->selectClear($sql);

        foreach ($mainDataArray as &$var) {
            if ($subDataArray) {
                foreach ($subDataArray as $subvar) {
                    if ($subvar['school_id'] == $var['school_id']) {
                        $var['wait_count_now'] = $subvar['wait_count'];
                        $var['new_count_now'] = $subvar['new_count'];
                    }
                }

                foreach ($readingDataArray as $readingvar) {
                    if ($readingvar['school_id'] == $var['school_id']) {
                        $var['reading_count_now'] = $readingvar['reading_count_now'];
                    }
                }
            }
            if ($var['reading_count_now'] > 0 && $var['register_count_now'] > 0) {
                $var['reading_percentage'] = round($var['reading_count_now'] / $var['register_count_now'], 2) * 100;
            } else {
                $var['reading_percentage'] = "0";
            }
        }


        if ($request['is_export'] == '1') {

            $outexceldate = array();
            if ($mainDataArray) {
                $outexceldate = array();
                foreach ($mainDataArray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['school_id'] = $dateexcelvar['school_id'];//学校ID
                    $datearray['school_shortname'] = $dateexcelvar['school_shortname'];//校区名称
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];//校区编号
                    $datearray['reading_count_now'] = $dateexcelvar['reading_count_now'];//在读学员数量
                    $datearray['wait_count_now'] = $dateexcelvar['wait_count_now'];//等班学员数量
                    $datearray['new_count_now'] = $dateexcelvar['new_count_now'];//新生待入班学员
                    $datearray['register_count_now'] = $dateexcelvar['register_count_now'];//在籍学生数量
                    $datearray['reading_percentage'] = $dateexcelvar['reading_percentage'];//在读比率
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array('学校ID', '校区名称', '校区编号', '在读学员数量', '等班学员数量', '新生待入班学员', '在籍学生数量', '在读比率'));
            $excelfileds = array('school_id', 'school_shortname', 'school_branch', 'reading_count_now', 'wait_count_now', 'new_count_now', 'register_count_now', 'reading_percentage');

            $fielname = $this->LgStringSwitch("月度学生统计");
            query_to_excel($excelheader, $outexceldate, $excelfileds, "{$fielname}.xlsx");
            exit;
        }

        $result["field"] = $field;
        $result["list"] = $mainDataArray;
        $res = array('error' => 0, 'errortip' => '获取月度学生统计成功', 'result' => $result);
        ajax_return($res, $request['language_type']);
    }

    function getSchoolMonthlyAverageSummaryApi()
    {
        $request = Input('get.', '', 'trim,addslashes');

        $datawhere = " 1 ";
        if (isset($request['company_id']) && $request['company_id'] !== '') {
            $datawhere .= " and a.company_id='{$request['company_id']}' ";
        }
        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and a.school_id='{$request['school_id']}' ";
        }

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (school_branch like '%{$request['keyword']}%' 
            or school_cnname like '%{$request['keyword']}%' )";
        }

        $yearFirstSeconds = strtotime(date("Y-01-01 00:00:00"));
        $firstSeconds = strtotime(date("Y-m-01 00:00:00"));
        $lastSeconds = strtotime(date("Y-m-d 23:59:59"));

        $field = array();

        $k = 0;

        $field[$k]["fieldstring"] = "school_id";
        $field[$k]["fieldname"] = "学校id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "school_tagbak";
        $field[$k]["fieldname"] = "督导区域";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_shortname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coupons_count_month";
        $field[$k]["fieldname"] = "优惠券数量.当月";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coupons_price_month";
        $field[$k]["fieldname"] = "优惠金额.当月";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coupons_avg_month";
        $field[$k]["fieldname"] = "均值.当月";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coupons_count_year";
        $field[$k]["fieldname"] = "优惠券数量.本年";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coupons_price_year";
        $field[$k]["fieldname"] = "优惠金额.本年";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coupons_avg_year";
        $field[$k]["fieldname"] = "均值.本年";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "order_count_month";
        $field[$k]["fieldname"] = "超退订单数量.当月";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "order_price_month";
        $field[$k]["fieldname"] = "超退金额.当月";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "order_avg_month";
        $field[$k]["fieldname"] = "均值.当月";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "order_count_year";
        $field[$k]["fieldname"] = "超退订单数量.本年";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "order_price_year";
        $field[$k]["fieldname"] = "超退金额.本年";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "order_avg_year";
        $field[$k]["fieldname"] = "均值.本年";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "free_count_month";
        $field[$k]["fieldname"] = "学员数量.当月";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "free_times_month";
        $field[$k]["fieldname"] = "总课时.当月";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "free_avg_month";
        $field[$k]["fieldname"] = "均值.当月";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "free_count_year";
        $field[$k]["fieldname"] = "学员数量.本年";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "free_times_year";
        $field[$k]["fieldname"] = "总课时.本年";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "free_avg_year";
        $field[$k]["fieldname"] = "均值.本年";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $sql = "SELECT a.school_id,a.school_tagbak,a.school_branch,a.school_cnname,a.school_shortname ,
            SUM(IF(TA.order_createtime>='{$firstSeconds}',TA.order_coupon_price,0)) AS coupons_price_month,
            SUM(IF(TA.order_createtime>='{$firstSeconds}',TA.coupons_count,0)) AS coupons_count_month,
            SUM(TA.order_coupon_price) AS coupons_price_year,
            SUM(TA.coupons_count) AS coupons_count_year
            from smc_school a 
            LEFT JOIN (
            SELECT x.school_id,x.order_id,x.order_createtime,x.order_coupon_price,count(y.coupons_pid) as coupons_count
            FROM smc_payfee_order x, smc_student_coupons y 
            WHERE x.company_id = '8888' 
            AND x.order_coupon_price > 0 
            AND x.order_status >= 0 
            AND x.order_createtime >= '{$yearFirstSeconds}' 
            AND x.order_createtime <= '{$lastSeconds}'
            AND x.order_pid = y.order_pid
            GROUP BY x.order_id) AS TA ON a.school_id=TA.school_id
            where a.company_id='8888' 
            and a.school_istest<>'1' 
            and a.school_isclose<>'1' 
            and a.school_type='1' 
            GROUP BY a.school_id 
            order by (case when a.school_istest=0 and a.school_isclose=0 then 1 when a.school_isclose=0 then 2 when a.school_istest=0 then 3 else 4 end)
            ,a.school_istest asc,field(a.school_sort,0),a.school_sort asc,a.school_createtime asc  
        ";
        $mainDataArray = $this->DataControl->selectClear($sql);

        $sql = "SELECT a.school_id,
            COUNT(IF(TB.refund_createtime>='{$firstSeconds}',TRUE,NULL)) AS order_count_month,
            SUM(IF(TB.refund_createtime>='{$firstSeconds}',TB.refund_specialprice,0)) AS order_price_month,
            COUNT(TB.refund_id) AS order_count_year,
            SUM(TB.refund_specialprice) AS order_price_year
            from smc_school a 
            LEFT JOIN (
            SELECT W.school_id,W.refund_id,W.refund_createtime,W.refund_specialprice
            FROM smc_refund_order W 
            WHERE W.company_id='8888'
            AND W.refund_status>=0
            AND W.refund_isspecial=1
            AND W.refund_createtime >= '{$yearFirstSeconds}' 
            AND W.refund_createtime <= '{$lastSeconds}'
            ) AS TB ON a.school_id=TB.school_id 
            where a.company_id='8888' 
            and a.school_istest<>'1' 
            and a.school_isclose<>'1'
            and a.school_type='1'  
            GROUP BY a.school_id 
            order by a.school_id 
        ";
        $subDataArray = $this->DataControl->selectClear($sql);

        $sql = "SELECT W.school_id,
            COUNT(DISTINCT W.student_id) AS free_count_month,
            SUM(W.order_alltimes) AS free_times_month
            FROM smc_freehour_order W 
            WHERE W.company_id='8888'
            AND W.order_status>=0 
            AND W.order_createtime >= '{$firstSeconds}' 
            AND W.order_createtime <= '{$lastSeconds}'
            GROUP BY W.school_id 
        ";
        $sub1DataArray = $this->DataControl->selectClear($sql);

        $sql = "SELECT W.school_id,
            COUNT(DISTINCT W.student_id) AS free_count_year,
            SUM(W.order_alltimes) AS free_times_year
            FROM smc_freehour_order W 
            WHERE W.company_id='8888'
            AND W.order_status>=0 
            AND W.order_createtime >= '{$yearFirstSeconds}' 
            AND W.order_createtime <= '{$lastSeconds}'
            GROUP BY W.school_id 
        ";
        $sub2DataArray = $this->DataControl->selectClear($sql);

        foreach ($mainDataArray as &$var) {
            $var['coupons_price_month'] = $var['coupons_price_month'] == 0 ? '--' : ceil($var['coupons_price_month']);
            $var['coupons_count_month'] = $var['coupons_count_month'] == 0 ? '--' : $var['coupons_count_month'];
            $var['coupons_avg_month'] = $var['coupons_count_month'] > 0 ? ceil($var['coupons_price_month'] / $var['coupons_count_month']) : '--';
//            $var['coupons_avg_month'] = $var['coupons_count_month'] > 0 ? (sprintf("%.2f", $var['coupons_price_month'] / $var['coupons_count_month'])) : '--';
            $var['coupons_price_year'] = $var['coupons_price_year'] == 0 ? '--' : ceil($var['coupons_price_year']);
            $var['coupons_count_year'] = $var['coupons_count_year'] == 0 ? '--' : $var['coupons_count_year'];
            $var['coupons_avg_year'] = $var['coupons_count_year'] > 0 ? ceil($var['coupons_price_year'] / $var['coupons_count_year']) : '--';
//            $var['coupons_avg_year'] = $var['coupons_count_year'] > 0 ? (sprintf("%.2f", $var['coupons_price_year'] / $var['coupons_count_year'])) : '--';

            if ($subDataArray) {
                foreach ($subDataArray as $subvar) {
                    if ($subvar['school_id'] == $var['school_id']) {
                        $var['order_count_month'] = $subvar['order_count_month'] > 0 ? $subvar['order_count_month'] : '--';
                        $var['order_price_month'] = $subvar['order_count_month'] > 0 ? ceil($subvar['order_price_month']) : '--';
                        $var['order_avg_month'] = $subvar['order_count_month'] > 0 ? ceil($subvar['order_price_month'] / $subvar['order_count_month']) : '--';
//                        $var['order_avg_month'] = $subvar['order_count_month'] > 0 ? (sprintf("%.2f", $subvar['order_price_month'] / $subvar['order_count_month'])) : '--';
                        $var['order_count_year'] = $subvar['order_count_year'] > 0 ? $subvar['order_count_year'] : '--';
                        $var['order_price_year'] = $subvar['order_count_year'] > 0 ? ceil($subvar['order_price_year']) : '--';
                        $var['order_avg_year'] = $subvar['order_count_year'] > 0 ? ceil($subvar['order_price_year'] / $subvar['order_count_year']) : '--';
//                        $var['order_avg_year'] = $subvar['order_count_year'] > 0 ? (sprintf("%.2f", $subvar['order_price_year'] / $subvar['order_count_year'])) : '--';
                    }
                }
            }
            if ($sub1DataArray) {
                foreach ($sub1DataArray as $sub1var) {
                    if ($sub1var['school_id'] == $var['school_id']) {
                        $var['free_count_month'] = $sub1var['free_count_month'] > 0 ? $sub1var['free_count_month'] : '--';
                        $var['free_times_month'] = $sub1var['free_count_month'] > 0 ? $sub1var['free_times_month'] : '--';
                        $var['free_avg_month'] = $sub1var['free_count_month'] > 0 ? ceil($sub1var['free_times_month'] / $sub1var['free_count_month']) : '--';
//                        $var['free_avg_month'] = $sub1var['free_count_month'] > 0 ? (sprintf("%.2f", $sub1var['free_times_month'] / $sub1var['free_count_month'])) : '--';
                    }
                }
            }
            if ($sub2DataArray) {
                foreach ($sub2DataArray as $sub2var) {
                    if ($sub2var['school_id'] == $var['school_id']) {
                        $var['free_count_year'] = $sub2var['free_count_year'] > 0 ? $sub2var['free_count_year'] : '--';
                        $var['free_times_year'] = $sub2var['free_count_year'] > 0 ? $sub2var['free_times_year'] : '--';
                        $var['free_avg_year'] = $sub2var['free_count_year'] > 0 ? ceil($sub2var['free_times_year'] / $sub2var['free_count_year']) : '--';
//                        $var['free_avg_year'] = $sub2var['free_count_year'] > 0 ? (sprintf("%.2f", $sub2var['free_times_year'] / $sub2var['free_count_year'])) : '--';
                    }
                }
            }
        }

        if ($request['is_export'] == '1') {

            $outexceldate = array();
            if ($mainDataArray) {
                $outexceldate = array();
                foreach ($mainDataArray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['school_id'] = $dateexcelvar['school_id'];//学校ID
                    $datearray['school_shortname'] = $dateexcelvar['school_shortname'];//校区名称
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];//校区编号
                    $datearray['coupons_count_month'] = $dateexcelvar['coupons_count_month'];//优惠券数量.当月
                    $datearray['coupons_price_month'] = $dateexcelvar['coupons_price_month'];//优惠金额.当月
                    $datearray['coupons_avg_month'] = $dateexcelvar['coupons_avg_month'];//均值.当月
                    $datearray['coupons_count_year'] = $dateexcelvar['coupons_count_year'];//优惠券数量.本年
                    $datearray['coupons_price_year'] = $dateexcelvar['coupons_price_year'];//优惠金额.本年
                    $datearray['coupons_avg_year'] = $dateexcelvar['coupons_avg_year'];//均值.本年
                    $datearray['order_count_month'] = $dateexcelvar['order_count_month'];//超退订单数量.当月
                    $datearray['order_price_month'] = $dateexcelvar['order_price_month'];//超退金额.当月
                    $datearray['order_avg_month'] = $dateexcelvar['order_avg_month'];//均值.当月
                    $datearray['order_count_year'] = $dateexcelvar['order_count_year'];//超退订单数量.本年
                    $datearray['order_price_year'] = $dateexcelvar['order_price_year'];//超退金额.本年
                    $datearray['order_avg_year'] = $dateexcelvar['order_avg_year'];//均值.本年
                    $datearray['free_count_month'] = $dateexcelvar['free_count_month'];//学员数量.当月
                    $datearray['free_times_month'] = $dateexcelvar['free_times_month'];//总课时.当月
                    $datearray['free_avg_month'] = $dateexcelvar['free_avg_month'];//均值.当月
                    $datearray['free_count_year'] = $dateexcelvar['free_count_year'];//学员数量.本年
                    $datearray['free_times_year'] = $dateexcelvar['free_times_year'];//总课时.本年
                    $datearray['free_avg_year'] = $dateexcelvar['free_avg_year'];//均值.本年 
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array('学校ID', '校区名称', '校区编号', '优惠券数量.当月', '优惠金额.当月', '均值.当月', '优惠券数量.本年', '优惠金额.本年', '均值.本年', '超退订单数量.当月', '超退金额.当月', '均值.当月', '超退订单数量.本年', '超退金额.本年', '均值.本年', '学员数量.当月', '总课时.当月', '均值.当月', '学员数量.本年', '总课时.本年', '均值.本年'));
            $excelfileds = array('school_id', 'school_shortname', 'school_branch', 'coupons_count_month', 'coupons_price_month', 'coupons_avg_month', 'coupons_count_year', 'coupons_price_year', 'coupons_avg_year', 'order_count_month', 'order_price_month', 'order_avg_month', 'order_count_year', 'order_price_year', 'order_avg_year', 'free_count_month', 'free_times_month', 'free_avg_month', 'free_count_year', 'free_times_year', 'free_avg_year');

            $fielname = $this->LgStringSwitch("合规项报告");
            query_to_excel($excelheader, $outexceldate, $excelfileds, "{$fielname}.xlsx");
            exit;
        }

        $result["field"] = $field;
        $result["list"] = $mainDataArray;
        $res = array('error' => 0, 'errortip' => '获取合规项报告成功', 'result' => $result);
        ajax_return($res, $request['language_type']);
    }

    // 日报-课程试听销售情况
    function getCrmAuditionByCourseView()
    {
        $k = 0;
        $field[$k]["fieldstring"] = "aud_course_name";
        $field[$k]["fieldname"] = "产品类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "today_audition_num";
        $field[$k]["fieldname"] = "当日试听诺访人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "today_audition_arr_nopositive_num";
        $field[$k]["fieldname"] = "当日到访未报名人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "today_audition_arr_positive_num";
        $field[$k]["fieldname"] = "当日到访报名人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "week_audition_num";
        $field[$k]["fieldname"] = "当周试听诺访人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "week_audition_arr_nopositive_num";
        $field[$k]["fieldname"] = "当周到访未报名人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "week_audition_arr_positive_num";
        $field[$k]["fieldname"] = "当周到访报名人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "month_audition_num";
        $field[$k]["fieldname"] = "当月试听诺访人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "month_audition_arr_nopositive_num";
        $field[$k]["fieldname"] = "当月到访未报名人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "month_audition_arr_positive_num";
        $field[$k]["fieldname"] = "当月到访报名人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $j = 0;
        $data = array();
        $today = date("Y-m-d");
        $today_aud_data = $this->getAudtionCourseNum('OHK', $today, $today, '0');
        $data[$j]['aud_course_name'] = "w公开课";
        $data[$j]['today_audition_num'] = $today_aud_data['audition_num'];
        $data[$j]['today_audition_arr_nopositive_num'] = $today_aud_data['audition_arr_nopositive_num'];
        $data[$j]['today_audition_arr_positive_num'] = $today_aud_data['audition_arr_positive_num'];
        $start_week = GetWeekAll($today)['lastweek_start'];
        $end_week = GetWeekAll($today)['lastweek_end'];
        $today_aud_data = $this->getAudtionCourseNum('OHK', $start_week, $end_week, '0');
        $data[$j]['week_audition_num'] = $today_aud_data['audition_num'];
        $data[$j]['week_audition_arr_nopositive_num'] = $today_aud_data['audition_arr_nopositive_num'];
        $data[$j]['week_audition_arr_positive_num'] = $today_aud_data['audition_arr_positive_num'];
        $start_month = GetMonth($today) . '-01';
        $end_month = date("Y-m-t", strtotime($start_month));
        $today_aud_data = $this->getAudtionCourseNum('OHK', $start_month, $end_month, '0');
        $data[$j]['month_audition_num'] = $today_aud_data['audition_num'];
        $data[$j]['month_audition_arr_nopositive_num'] = $today_aud_data['audition_arr_nopositive_num'];
        $data[$j]['month_audition_arr_positive_num'] = $today_aud_data['audition_arr_positive_num'];
        $j++;
        $today_aud_data = $this->getAudtionCourseNum('OHK', $today, $today, '1');
        $data[$j]['aud_course_name'] = "W插班课";
        $data[$j]['today_audition_num'] = $today_aud_data['audition_num'];
        $data[$j]['today_audition_arr_nopositive_num'] = $today_aud_data['audition_arr_nopositive_num'];
        $data[$j]['today_audition_arr_positive_num'] = $today_aud_data['audition_arr_positive_num'];
        $start_week = GetWeekAll($today)['lastweek_start'];
        $end_week = GetWeekAll($today)['lastweek_end'];
        $today_aud_data = $this->getAudtionCourseNum('OHK', $start_week, $end_week, '1');
        $data[$j]['week_audition_num'] = $today_aud_data['audition_num'];
        $data[$j]['week_audition_arr_nopositive_num'] = $today_aud_data['audition_arr_nopositive_num'];
        $data[$j]['week_audition_arr_positive_num'] = $today_aud_data['audition_arr_positive_num'];
        $start_month = GetMonth($today) . '-01';
        $end_month = date("Y-m-t", strtotime($start_month));
        $today_aud_data = $this->getAudtionCourseNum('OHK', $start_month, $end_month, '1');
        $data[$j]['month_audition_num'] = $today_aud_data['audition_num'];
        $data[$j]['month_audition_arr_nopositive_num'] = $today_aud_data['audition_arr_nopositive_num'];
        $data[$j]['month_audition_arr_positive_num'] = $today_aud_data['audition_arr_positive_num'];

        $j++;
        $today_aud_data = $this->getAudtionCourseNum('J', $today, $today, '1');
        $data[$j]['aud_course_name'] = "J插班课";
        $data[$j]['today_audition_num'] = $today_aud_data['audition_num'];
        $data[$j]['today_audition_arr_nopositive_num'] = $today_aud_data['audition_arr_nopositive_num'];
        $data[$j]['today_audition_arr_positive_num'] = $today_aud_data['audition_arr_positive_num'];
        $start_week = GetWeekAll($today)['lastweek_start'];
        $end_week = GetWeekAll($today)['lastweek_end'];
        $today_aud_data = $this->getAudtionCourseNum('J', $start_week, $end_week, '1');
        $data[$j]['week_audition_num'] = $today_aud_data['audition_num'];
        $data[$j]['week_audition_arr_nopositive_num'] = $today_aud_data['audition_arr_nopositive_num'];
        $data[$j]['week_audition_arr_positive_num'] = $today_aud_data['audition_arr_positive_num'];
        $start_month = GetMonth($today) . '-01';
        $end_month = date("Y-m-t", strtotime($start_month));
        $today_aud_data = $this->getAudtionCourseNum('J', $start_month, $end_month, '1');
        $data[$j]['month_audition_num'] = $today_aud_data['audition_num'];
        $data[$j]['month_audition_arr_nopositive_num'] = $today_aud_data['audition_arr_nopositive_num'];
        $data[$j]['month_audition_arr_positive_num'] = $today_aud_data['audition_arr_positive_num'];
        $j++;
        $today_aud_data = $this->getAudtionCourseNum('S', $today, $today, '1');
        $data[$j]['aud_course_name'] = "S插班课";
        $data[$j]['today_audition_num'] = $today_aud_data['audition_num'];
        $data[$j]['today_audition_arr_nopositive_num'] = $today_aud_data['audition_arr_nopositive_num'];
        $data[$j]['today_audition_arr_positive_num'] = $today_aud_data['audition_arr_positive_num'];
        $start_week = GetWeekAll($today)['lastweek_start'];
        $end_week = GetWeekAll($today)['lastweek_end'];
        $today_aud_data = $this->getAudtionCourseNum('S', $start_week, $end_week, '1');
        $data[$j]['week_audition_num'] = $today_aud_data['audition_num'];
        $data[$j]['week_audition_arr_nopositive_num'] = $today_aud_data['audition_arr_nopositive_num'];
        $data[$j]['week_audition_arr_positive_num'] = $today_aud_data['audition_arr_positive_num'];
        $start_month = GetMonth($today) . '-01';
        $end_month = date("Y-m-t", strtotime($start_month));
        $today_aud_data = $this->getAudtionCourseNum('S', $start_month, $end_month, '1');
        $data[$j]['month_audition_num'] = $today_aud_data['audition_num'];
        $data[$j]['month_audition_arr_nopositive_num'] = $today_aud_data['audition_arr_nopositive_num'];
        $data[$j]['month_audition_arr_positive_num'] = $today_aud_data['audition_arr_positive_num'];

        $result = array();
        $result['field'] = $field;
        $result['list'] = $data;
        $res = array('error' => 0, 'errortip' => '获取课程试听销售情况', 'result' => $result);
        ajax_return($res);
    }

    /**
     * 获取班种的试听状况
     * author: ling
     * 对应接口文档 0001
     * @param $coursecat_branch
     * @param $start_day
     * @param $end_day
     * @return array|bool
     */
    private function getAudtionCourseNum($coursecat_branch, $start_day, $end_day, $audition_genre = 0)
    {
        $datawhere = " ct.coursecat_branch='{$coursecat_branch}' and a.audition_genre='{$audition_genre}'";
        if (isset($start_day) && $start_day != '') {
            $datawhere .= " and  DATE_FORMAT(a.audition_visittime, '%Y-%m-%d') >= '{$start_day}'";
        }
        if (isset($end_day) && $end_day != '') {
            $datawhere .= " and  DATE_FORMAT(a.audition_visittime, '%Y-%m-%d') <= '{$end_day}'";
        }

        $sql = "select 
                 count(1) as audition_num,
                 sum(CASE WHEN audition_isvisit =1 AND g.positivelog_id is null THEN 1 ELSE 0 END) as audition_arr_nopositive_num,
                 sum(CASE WHEN audition_isvisit =1 AND g.positivelog_id > 0 THEN 1 ELSE 0 END) as audition_arr_positive_num
                from crm_client_audition as a
                left join smc_class as s ON a.class_id = s.class_id
                left join smc_school as l ON a.school_id = l.school_id
                left join gmc_company as gc ON gc.company_id =l.company_id
                left join smc_course as co ON co.course_id = s.course_id
                left join smc_code_coursecat as ct On co.coursecat_id=ct.coursecat_id
                left join crm_client_positivelog as g ON a.client_id=g.client_id and DATE_FORMAT(a.audition_visittime, '%Y-%m-%d') = g.positivelog_time
                where gc.company_id='8888' and {$datawhere} limit 0,1";

        $dataOne = $this->DataControl->selectOne($sql);
        $dataOne['audition_num'] = intval($dataOne['audition_num']);
        $dataOne['audition_arr_nopositive_num'] = intval($dataOne['audition_arr_nopositive_num']);
        $dataOne['audition_arr_positive_num'] = intval($dataOne['audition_arr_positive_num']);
        if (!$dataOne) {
            $dataOne = array();
        }
        return $dataOne;
    }

    /**
     * 日报-学校销售过程情况
     * author: ling
     * 对应接口文档 0001
     */
    function getCrmAuditionBySchoolView()
    {
        $k = 0;
        $field[$k]["fieldstring"] = "school_tagbak";
        $field[$k]["fieldname"] = "督导区域";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "today_audition_num";
        $field[$k]["fieldname"] = "当日试听诺访人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "today_audition_arr_nopositive_num";
        $field[$k]["fieldname"] = "当日到访未报名人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "today_audition_arr_positive_num";
        $field[$k]["fieldname"] = "当日到访报名人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "week_audition_num";
        $field[$k]["fieldname"] = "当周试听诺访人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "week_audition_arr_nopositive_num";
        $field[$k]["fieldname"] = "当周到访未报名人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "week_audition_arr_positive_num";
        $field[$k]["fieldname"] = "当周到访报名人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "month_audition_num";
        $field[$k]["fieldname"] = "当月试听诺访人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "month_audition_arr_nopositive_num";
        $field[$k]["fieldname"] = "当月到访未报名人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "month_audition_arr_positive_num";
        $field[$k]["fieldname"] = "当月到访报名人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $today = date("Y-m-d");
        $today_data = $this->getAudtionSchoolNum($today, $today, 'today');
        $start_week = GetWeekAll($today)['lastweek_start'];
        $end_week = GetWeekAll($today)['lastweek_end'];
        $week_data = $this->getAudtionSchoolNum($start_week, $end_week, 'week');
        $start_month = GetMonth($today) . '-01';
        $end_month = date("Y-m-t", strtotime($start_month));
        $month_data = $this->getAudtionSchoolNum($start_month, $end_month, 'month');
        foreach ($today_data as $key => $dataOne) {
            $data[] = array_merge($dataOne, $week_data[$key]);
        }
        foreach ($data as $k => $data_one) {
            $dataList[] = array_merge($data_one, $month_data[$k]);
        }
        $result = array();
        $result['field'] = $field;
        $result['list'] = $dataList;
        $res = array('error' => 0, 'errortip' => '学校销售过程情况', 'result' => $result);
        ajax_return($res);
    }

    private function getAudtionSchoolNum($start_day, $end_day, $type = 'today')
    {
        $datawhere = 'l.school_istest=0 and school_isclose = 0  and school_type =1 ';
        if (isset($start_day) && $start_day != '') {
            $datawhere .= " and  DATE_FORMAT(a.audition_visittime, '%Y-%m-%d') >= '{$start_day}'";
        }
        if (isset($end_day) && $end_day != '') {
            $datawhere .= " and  DATE_FORMAT(a.audition_visittime, '%Y-%m-%d') <= '{$end_day}'";
        }
        $sql = "select l.school_id,l.school_tagbak,l.school_shortname as school_cnname,l.school_branch,
                 count(1) as {$type}_audition_num,
                 sum(CASE WHEN audition_isvisit =1 AND g.positivelog_id is null THEN 1 ELSE 0 END) as {$type}_audition_arr_nopositive_num,
                 sum(CASE WHEN audition_isvisit =1 AND g.positivelog_id > 0 THEN 1 ELSE 0 END) as {$type}_audition_arr_positive_num
                from  smc_school as l
                left join smc_class as s ON l.school_id = s.school_id
                left join crm_client_audition as a ON a.class_id=s.class_id 
                left join gmc_company as gc ON gc.company_id =l.company_id
                left join smc_course as co ON co.course_id = s.course_id
                left join smc_code_coursecat as ct On co.coursecat_id=ct.coursecat_id
                left join crm_client_positivelog as g ON a.client_id=g.client_id and DATE_FORMAT(a.audition_visittime, '%Y-%m-%d') = g.positivelog_time
                where gc.company_id='8888' and {$datawhere}  
                group by l.school_id  
                order by (case when l.school_istest=0 and l.school_isclose=0 then 1 when l.school_isclose=0 then 2 when l.school_istest=0 then 3 else 4 end)
                ,l.school_istest asc,field(l.school_sort,0),l.school_sort asc,l.school_createtime asc 
                ";
        $dataList = $this->DataControl->selectClear($sql);

        $schoolList = $this->DataControl->selectClear("select l.school_id,l.school_shortname as school_cnname,l.school_branch,l.school_tagbak from smc_school as l 
                where l.company_id='8888' and l.school_istest=0 and school_isclose = 0  and school_type = 1
                order by (case when l.school_istest=0 and l.school_isclose=0 then 1 when l.school_isclose=0 then 2 when l.school_istest=0 then 3 else 4 end)
                ,l.school_istest asc,field(l.school_sort,0),l.school_sort asc,l.school_createtime asc ");
        $data = array();
        $dataArray = array();
        if ($schoolList) {
            foreach ($schoolList as $key => $schoolOne) {
                if ($dataList) {
                    $dataList = array_combine(array_column($dataList, 'school_id'), $dataList);
                    $dataArray['school_tagbak'] = $schoolOne['school_tagbak'];
                    $dataArray['school_branch'] = $schoolOne['school_branch'];
                    $dataArray['school_cnname'] = $schoolOne['school_cnname'];
                    $dataArray['school_id'] = $schoolOne['school_id'];
                    $dataArray["{$type}_audition_num"] = intval($dataList[$schoolOne['school_id']]["{$type}_audition_num"]);
                    $dataArray["{$type}_audition_arr_nopositive_num"] = intval($dataList[$schoolOne['school_id']]["{$type}_audition_arr_nopositive_num"]);
                    $dataArray["{$type}_audition_arr_positive_num"] = intval($dataList[$schoolOne['school_id']]["{$type}_audition_arr_positive_num"]);;
                } else {
                    $dataArray['school_tagbak'] = $schoolOne['school_tagbak'];
                    $dataArray['school_branch'] = $schoolOne['school_branch'];
                    $dataArray['school_cnname'] = $schoolOne['school_cnname'];
                    $dataArray['school_id'] = $schoolOne['school_id'];
                    $dataArray["{$type}_audition_num"] = 0;
                    $dataArray["{$type}_audition_arr_nopositive_num"] = 0;
                    $dataArray["{$type}_audition_arr_positive_num"] = 0;
                }
                $data[$key] = $dataArray;
            }
        }
        $data = array_values($data);
        return $data;
    }

    /**
     * 日报-销售渠道分析
     * author: ling
     * 对应接口文档 0001
     */
    function getCrmAuditionByChannelView()
    {
        $request = Input('get.', '', 'trim,addslashes');

        $k = 0;
        $field[$k]["fieldstring"] = "frommedia_name";
        $field[$k]["fieldname"] = "渠道名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "aud_num";
        $field[$k]["fieldname"] = "邀约人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "aud_arrive_num";
        $field[$k]["fieldname"] = "邀约到访人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "aud_arrive_nopositive_num";
        $field[$k]["fieldname"] = "邀约到访未签约人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "aud_arrive_rate";
        $field[$k]["fieldname"] = "邀约到访率";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "aud_arrive_nopositive_rate";
        $field[$k]["fieldname"] = "邀约到访未签率";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $today = date("Y-m-d");
        $endday = $today;
        $startday = $today;

        if (isset($request['end_time']) && $request['end_time'] !== '') {
            $endday = date("Y-m-d", strtotime($request['end_time']));
        }

        if (isset($request['start_time']) && $request['start_time'] !== '') {
            $startday = date("Y-m-d", strtotime($request['start_time']));
        }

        $audwhere = "DATE_FORMAT(ca.audition_visittime,'%Y-%m-%d') >= '{$startday}'";
        $audwhere .= " and DATE_FORMAT(ca.audition_visittime,'%Y-%m-%d') <= '{$endday}'";

        $sql = "
            select cl.frommedia_name,
            (select count(DISTINCT t.client_id) from crm_client_audition as ca,crm_client as t where ca.client_id =t.client_id and t.company_id=cl.company_id and t.client_source=cl.frommedia_name and {$audwhere} ) as aud_num,
            (select count(DISTINCT t.client_id) from crm_client_audition as ca,crm_client as t where ca.client_id =t.client_id and t.company_id=cl.company_id and t.client_source=cl.frommedia_name and ca.audition_isvisit =1 and {$audwhere} ) as aud_arrive_num,
            (select count(DISTINCT t.client_id) from crm_client_audition as ca,crm_client as t,crm_client_positivelog as g where ca.client_id =t.client_id and t.company_id=cl.company_id and t.client_source=cl.frommedia_name and ca.audition_isvisit =1 and g.client_id =t.client_id and DATE_FORMAT(ca.audition_visittime,'%Y-%m-%d') = g.positivelog_time  and {$audwhere} ) as aud_arrive_positive_num,
            (select count(DISTINCT t.client_id) 
            from crm_client_audition as ca
            left join crm_client as t ON  ca.client_id =t.client_id
            where ca.audition_isvisit =1 and t.client_source=cl.frommedia_name
            and {$audwhere} and  not exists (select 1 from crm_client_positivelog as g where g.client_id=ca.client_id and DATE_FORMAT(ca.audition_visittime,'%Y-%m-%d') = g.positivelog_time)
             ) as aud_arrive_nopositive_num
            from crm_code_frommedia as cl 
            where cl.company_id='8888' 
            order by aud_num DESC
        ";
        $dataList = $this->DataControl->selectClear($sql);
        if (!$dataList) {
            $dataList = array();
        } else {
            foreach ($dataList as &$value) {
                $value['aud_arrive_rate'] = $value['aud_num'] > 0 ? round($value['aud_arrive_num'] / $value['aud_num'], 4) * 100 . '%' : '0%';
                $value['aud_arrive_nopositive_rate'] = $value['aud_arrive_num'] > 0 ? round($value['aud_arrive_nopositive_num'] / $value['aud_arrive_num'], 4) * 100 . '%' : '0%';
            }
        }
        $result = array();
        $result['field'] = $field;
        $result['list'] = $dataList;
        $res = array('error' => 0, 'errortip' => '获取课程试听销售情况', 'result' => $result);
        ajax_return($res);
    }

    function getSchoolLostSummaryApi()
    {
        $request = Input('get.', '', 'trim,addslashes');

        $datawhere = " 1 ";

        if (isset($request['company_id']) && $request['company_id'] !== '') {
            $datawhere .= " and a.company_id='{$request['company_id']}' ";
        }
        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and a.school_id='{$request['school_id']}' ";
        }

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (a.school_branch like '%{$request['keyword']}%' 
            or a.school_cnname like '%{$request['keyword']}%' )";
        }

        $field = array();

        $k = 0;

        $field[$k]["fieldstring"] = "school_id";
        $field[$k]["fieldname"] = "学校id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "school_tagbak";
        $field[$k]["fieldname"] = "督导区域";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "lost_count_day";
        $field[$k]["fieldname"] = "当日流失人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "lost_count_week";
        $field[$k]["fieldname"] = "当周流失人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "lost_count_month";
        $field[$k]["fieldname"] = "当月流失人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "newlost_count_day";
        $field[$k]["fieldname"] = "当日新签流失人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "newlost_count_week";
        $field[$k]["fieldname"] = "当周新签流失人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "newlost_count_month";
        $field[$k]["fieldname"] = "当月新签流失人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "oldlost_count_day";
        $field[$k]["fieldname"] = "当日老生流失人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "oldlost_count_week";
        $field[$k]["fieldname"] = "当周老生流失人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "oldlost_count_month";
        $field[$k]["fieldname"] = "当月老生流失人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;


        $sql = "select a.school_id,a.school_tagbak,a.school_shortname as school_cnname,a.school_branch,
            count(distinct b.student_id,if(b.changelog_day>=curdate(),true,null)) as lost_count_day,
            count(distinct b.student_id,if(b.changelog_day>=DATE_SUB(curdate(),INTERVAL WEEKDAY(curdate()) DAY),true,null)) as lost_count_week,
            count(distinct b.student_id,if(b.changelog_day>=DATE_ADD(curdate(),INTERVAL -DAY(curdate())+1 DAY),true,null)) as lost_count_month,
            count(distinct b.student_id,if(b.changelog_day>=curdate() and (select count(1) from view_smc_student_study_spend where school_id=b.school_id and student_id=b.student_id)=0,true,null)) as newlost_count_day,
            count(distinct b.student_id,if(b.changelog_day>=DATE_SUB(curdate(),INTERVAL WEEKDAY(curdate()) DAY) and (select count(1) from view_smc_student_study_spend where school_id=b.school_id and student_id=b.student_id)=0,true,null)) as newlost_count_week,
            count(distinct b.student_id,if(b.changelog_day>=DATE_ADD(curdate(),INTERVAL -DAY(curdate())+1 DAY) and (select count(1) from view_smc_student_study_spend where school_id=b.school_id and student_id=b.student_id)=0,true,null)) as newlost_count_month
            from smc_school a 
            left join smc_student_changelog b 
            on a.school_id=b.school_id and b.changelog_day<=curdate() and b.stuchange_code in ('C02')
            and b.changelog_day>=date_add(curdate()-day(curdate())+1,interval -1 month)
            where {$datawhere}
            and a.school_istest=0
            and a.school_isclose=0
            and a.school_type='1' 
            group by a.school_id
            order by (case when a.school_istest=0 and a.school_isclose=0 then 1 when a.school_isclose=0 then 2 when a.school_istest=0 then 3 else 4 end)
            ,a.school_istest asc,field(a.school_sort,0),a.school_sort asc,a.school_createtime asc  
        ";
        $mainDataArray = $this->DataControl->selectClear($sql);

        foreach ($mainDataArray as &$var) {
            $var['oldlost_count_day'] = $var['lost_count_day'] - $var['newlost_count_day'];
            $var['oldlost_count_week'] = $var['lost_count_week'] - $var['newlost_count_week'];
            $var['oldlost_count_month'] = $var['lost_count_month'] - $var['newlost_count_month'];
        }

        $result["field"] = $field;
        $result["list"] = $mainDataArray;
        $res = array('error' => 0, 'errortip' => '获取流失情况成功', 'result' => $result);
        ajax_return($res, $request['language_type']);
    }

    function getSchoolReceSummaryApi()
    {
        $request = Input('get.', '', 'trim,addslashes');

        $datawhere = " 1 and a.company_id='8888'";

        if (isset($request['company_id']) && $request['company_id'] !== '') {
            $datawhere .= " and a.company_id='{$request['company_id']}' ";
        }
        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and a.school_id='{$request['school_id']}' ";
        }

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (a.school_branch like '%{$request['keyword']}%' 
            or a.school_cnname like '%{$request['keyword']}%' )";
        }

        $field = array();

        $k = 0;

        $field[$k]["fieldstring"] = "school_id";
        $field[$k]["fieldname"] = "学校id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "school_tagbak";
        $field[$k]["fieldname"] = "督导区域";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "register_count_day";
        $field[$k]["fieldname"] = "当日新签人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "register_count_week";
        $field[$k]["fieldname"] = "当周新签人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "register_count_month";
        $field[$k]["fieldname"] = "当月新签人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "estimate_count_day";
        $field[$k]["fieldname"] = "当日缴费人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "estimate_count_week";
        $field[$k]["fieldname"] = "当周缴费人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "estimate_count_month";
        $field[$k]["fieldname"] = "当月缴费人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $sql = "select a.school_id,a.school_tagbak,a.school_branch,a.school_shortname as school_cnname,
            (select count(1) from smc_student_registerinfo x where info_status=1 and x.school_id=a.school_id and FROM_UNIXTIME(x.pay_successtime,'%Y-%m-%d')=curdate()) as register_count_day,
            (select count(1) from smc_student_registerinfo x where info_status=1 and x.school_id=a.school_id and FROM_UNIXTIME(x.pay_successtime,'%Y-%m-%d')<=curdate()and FROM_UNIXTIME(x.pay_successtime,'%Y-%m-%d')>=DATE_SUB(curdate(),INTERVAL WEEKDAY(curdate()) DAY)) as register_count_week,
            (select count(1) from smc_student_registerinfo x where info_status=1 and x.school_id=a.school_id and FROM_UNIXTIME(x.pay_successtime,'%Y-%m-%d')<=curdate()and FROM_UNIXTIME(x.pay_successtime,'%Y-%m-%d')>=DATE_ADD(curdate(),INTERVAL -DAY(curdate())+1 DAY)) as register_count_month
            from smc_school a 
            where {$datawhere}
            and a.school_istest<>'1' 
            and a.school_isclose<>'1' 
            and a.school_type='1' 
            order by (case when a.school_istest=0 and a.school_isclose=0 then 1 when a.school_isclose=0 then 2 when a.school_istest=0 then 3 else 4 end)
            ,a.school_istest asc,field(a.school_sort,0),a.school_sort asc,a.school_createtime asc  
        ";
        $mainDataArray = $this->DataControl->selectClear($sql);

        $subSql = "select B.school_id,
            count(distinct student_id,if(FROM_UNIXTIME(A.pay_successtime,'%Y-%m-%d')=curdate(),true,null)) as rece_count_day,
            count(distinct student_id,if(FROM_UNIXTIME(A.pay_successtime,'%Y-%m-%d')<=curdate() and FROM_UNIXTIME(A.pay_successtime,'%Y-%m-%d')>=DATE_SUB(curdate(),INTERVAL WEEKDAY(curdate()) DAY),true,null)) as rece_count_week,
            count(distinct student_id,if(FROM_UNIXTIME(A.pay_successtime,'%Y-%m-%d')<=curdate() and FROM_UNIXTIME(A.pay_successtime,'%Y-%m-%d')>=DATE_ADD(curdate(),INTERVAL -DAY(curdate())+1 DAY),true,null)) as rece_count_month
            from smc_payfee_order_pay A
            LEFT JOIN smc_payfee_order B ON A.order_pid=B.order_pid
            LEFT JOIN smc_code_paytype C ON A.paytype_code=C.paytype_code
            WHERE A.pay_type=0
            AND A.pay_issuccess=1
            AND A.pay_successtime>0
            AND B.order_status>0
            AND B.order_paidprice>0
            AND C.paytype_ischarge=1
            AND B.company_id='8888'
            AND FROM_UNIXTIME(A.pay_successtime,'%Y-%m-%d')>=date_add(curdate()-day(curdate())+1,interval -1 month)
            GROUP BY B.school_id
        ";

        $subDataArray = $this->DataControl->selectClear($subSql);

        foreach ($mainDataArray as &$var) {
            if ($subDataArray) {
                foreach ($subDataArray as $subvar) {
                    if ($subvar['school_id'] == $var['school_id']) {
                        $var['estimate_count_day'] = $subvar['rece_count_day'];
                        $var['estimate_count_week'] = $subvar['rece_count_week'];
                        $var['estimate_count_month'] = $subvar['rece_count_month'];
                    }
                }
            }
        }

        $result["field"] = $field;
        $result["list"] = $mainDataArray;
        $res = array('error' => 0, 'errortip' => '获取学校销售结果情况成功', 'result' => $result);
        ajax_return($res, $request['language_type']);
    }

    function getUnconcatApi()
    {
        $k = 0;
        $field = array();
        $field[$k]["fieldstring"] = "student_branch";
        $field[$k]["fieldname"] = "学员编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["fieldname"] = "学员中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_enname";
        $field[$k]["fieldname"] = "学员英文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $request = Input('get.', '', 'trim,addslashes');
        $datawhere = " 1 ";

        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and a.school_id='{$request['school_id']}' ";
        }

        if (isset($request['coursetype_id']) && $request['coursetype_id'] !== '') {
            $datawhere .= " and a.coursetype_id='{$request['coursetype_id']}' ";
        }

        if (isset($request['end_time']) && $request['end_time'] !== '') {
            $datawhere .= " and a.class_enddate<='{$request['end_time']}'";
        } else {
            $request['end_time'] = date("Y-m-d");
            $datawhere .= " and a.class_enddate<='{$request['end_time']}'";
        }

        if (isset($request['start_time']) && $request['start_time'] !== '') {
            $datawhere .= " and a.class_enddate>='{$request['start_time']}'";
        } else {
            $request['start_time'] = date("Y-m-01");
            $datawhere .= " and a.class_enddate>='{$request['start_time']}'";
        }

        if (isset($request['is_containsbreakoff']) && $request['is_containsbreakoff'] == '0') {
            $datawhere .= " and not exists(select 1 from smc_class_breakoff where class_id=a.class_id and breakoff_status>=2 and breakoff_type=0) ";
        }

        $sql = "select student_branch,student_cnname,student_enname
            from smc_student_course_estimate a
            WHERE {$datawhere} 
            and course_isrenew=1 
            and connect_times=''
            and ((renewal_times<a.coursetype_renewtimes and a.class_enddate<'2022-01-01') or(renewal_rates<100 and a.class_enddate>='2022-01-01'))
            and channel_name=''
        ";

        $unconcatList = $this->DataControl->selectClear($sql);
        if (!$unconcatList) {
            $unconcatList = array();
        }

        $result["field"] = $field;
        $result["list"] = $unconcatList;
        $res = array('error' => 0, 'errortip' => '获取弹窗成功', 'result' => $result);
        ajax_return($res, $request['language_type']);
    }

    //判断输入的编号是不是校的编号
    function checkScSchoolBranchView(){
        $request = Input('get.', '', 'trim,addslashes');

        $schoolOne = $this->DataControl->selectOne("select 1 from smc_school where school_branch = '{$request['school_branch']}' limit 0,1 ");
        if($schoolOne){
            ajax_return(array('error' => '0', 'errortip' => "系统存在该编号"));
        }else{
            ajax_return(array('error' => '1', 'errortip' => "系统不存在该编号"));
        }
    }

    function getMainCourseListView(){
        $request = Input('get.', '', 'trim,addslashes');

        $datawhere = " a.company_id='{$request['company_id']}' and a.course_status = '1' and a.course_isfollow=0 ";

        if(isset($request['is_limit_main']) && $request['is_limit_main']!='' && $request['is_limit_main']==1){

            if(isset($request['course_id']) && $request['course_id']!=''){
                $courseOne=$this->DataControl->getFieldOne("smc_course","main_course_id","course_id='{$request['course_id']}'");
                $datawhere .= " and (not exists(select 1 from smc_course as x where a.course_id=x.main_course_id) or a.course_id='{$courseOne['main_course_id']}') ";

            }else{
                $datawhere .= " and not exists(select 1 from smc_course as x where a.course_id=x.main_course_id) ";

            }

        }

        if(isset($request['coursetype_id']) && $request['coursetype_id']!='' ){
            $datawhere .= " and a.coursetype_id='{$request['coursetype_id']}' ";
        }

        $sql = "select a.course_id,a.course_cnname,a.course_branch 
                from smc_course as a
                left join smc_code_coursetype as b on b.coursetype_id=a.coursetype_id
                where {$datawhere}
                ";

        $courseList=$this->DataControl->selectClear($sql);

        if($courseList){

            foreach($courseList as &$courseOne){
                $courseOne['course_cnname']=$courseOne['course_cnname']."(".$courseOne['course_branch'].")";
            }


            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $courseList);
        }else{
            $res = array('error' => 1, 'errortip' => '无数据', 'result' => array());

        }
        ajax_return($res, $request['language_type']);
    }

    function getCoursepacksListView(){
        $request = Input('get.', '', 'trim,addslashes');

        $datawhere = " c.company_id='{$request['company_id']}'";

        $sql = "select a.coursepacks_id,a.coursepacks_name
                from smc_fee_warehouse_coursepacks as a
                left join smc_fee_warehouse as b on b.warehouse_id=a.warehouse_id
                left join smc_fee_agreement as c on c.agreement_id=b.agreement_id
                where {$datawhere} and (b.warehouse_startday<=curdate() or b.warehouse_startday='' ) and (b.warehouse_endday>=curdate() or b.warehouse_endday='' ) and (a.coursepacks_startday<=curdate() or a.coursepacks_startday='' ) and (a.coursepacks_endday>=curdate() or a.coursepacks_endday='' )
                and c.agreement_status=1
                ";

        $dateList=$this->DataControl->selectClear($sql);

        if($dateList){

            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $dateList);
        }else{
            $res = array('error' => 1, 'errortip' => '无数据', 'result' => array());

        }
        ajax_return($res, $request['language_type']);
    }

}