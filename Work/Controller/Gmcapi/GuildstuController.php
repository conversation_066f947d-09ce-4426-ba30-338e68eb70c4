<?php


namespace Work\Controller\Gmcapi;


class GuildstuController extends viewTpl
{
    public $u;
    public $t;
    public $c;

    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
    }

    //同业学员接纳 申请列表
    function getGuildstuApplyView(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $StudentModel = new \Model\Gmc\GuildstuModel($request);
        $dataList = $StudentModel->getGuildstuApply($request);

        $field = array();
        $key = 0;

        $field[$key]["fieldname"] = "school_cnname";
        $field[$key]["fieldstring"] = "校区名称";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "student_cnname";
        $field[$key]["fieldstring"] = "学员中文名";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "student_enname";
        $field[$key]["fieldstring"] = "学员英文名";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "student_branch";
        $field[$key]["fieldstring"] = "学员编号";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "guildstutype_name";
        $field[$key]["fieldstring"] = "同业学员类型";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "apply_contract";
        $field[$key]["fieldstring"] = "培训协议编号";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "apply_payprice";
        $field[$key]["fieldstring"] = "缴费金额";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "apply_paytime";
        $field[$key]["fieldstring"] = "合同缴费时间";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "apply_status_name";
        $field[$key]["fieldstring"] = "审核状态";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "staffer_cnname";
        $field[$key]["fieldstring"] = "审核人";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "apply_addtime";
        $field[$key]["fieldstring"] = "申请时间";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "enrolled_status";
        $field[$key]["fieldstring"] = "学生状态";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 1;
        $key++;

        $field[$key]["fieldname"] = "enrolled_leavetime";
        $field[$key]["fieldstring"] = "流失日期";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 1;
        $key++;

        $field[$key]["fieldname"] = "last_atte_date";
        $field[$key]["fieldstring"] = "最后考勤日期";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 1;
        $key++;

        $field[$key]["fieldname"] = "apply_statustime";
        $field[$key]["fieldstring"] = "审核时间";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "apply_statusreason";
        $field[$key]["fieldstring"] = "审核备注";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        if($dataList){
            $result["field"] = $field;
            $result["list"] = $dataList['datalist'];
            if($result["list"]){
                $res = array('error' => '0', 'errortip' => '获取招生目标信息成功', 'allnum' => $dataList['count'], 'result' => $result);
            }else{
                $result["list"] = array();
                $res = array('error' => '1', 'errortip' => '暂无招生目标信息', 'allnum' => 0, 'result' => $result);
            }
        }else{
            $result["list"] = array();
            $res = array('error' => '1', 'errortip' => '暂无招生目标信息', 'allnum' => 0, 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }

    //同业学员接纳 -- 审核通过
    function adoptGuildstuApi()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $StudentModel = new \Model\Gmc\GuildstuModel($request);
        $dataList = $StudentModel->adoptGuildstuApi($request);

        if ($dataList) {
            $this->addGmcWorkLog($request['company_id'],$request['staffer_id'],"同业接纳学员",'审核通过同业接纳学员信息',dataEncode($request));
            $res = array('error' => 0, 'errortip' => '审核通过成功', 'result' => array());
        } else {
            $res = array('error' => 1, 'errortip' => '审核通过失败', 'result' => array());

        }
        ajax_return($res,$request['language_type']);

    }

    //同业学员接纳 -- 审核拒绝
    function refuseGuildstuApi()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $StudentModel = new \Model\Gmc\GuildstuModel($request);
        $dataList = $StudentModel->refuseGuildstuApi($request);

        if ($dataList) {
            $this->addGmcWorkLog($request['company_id'],$request['staffer_id'],"同业接纳学员",'审核拒绝同业接纳学员信息',dataEncode($request));
            $res = array('error' => 0, 'errortip' => '审核拒绝成功', 'result' => array());
        } else {
            $res = array('error' => 1, 'errortip' => '审核拒绝失败', 'result' => array());

        }
        ajax_return($res,$request['language_type']);

    }

    //同业学员接纳 -- 审核取消
    function cancelGuildstuApi()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $StudentModel = new \Model\Gmc\GuildstuModel($request);
        $dataList = $StudentModel->cancelGuildstuApi($request);

        if ($dataList) {
            $this->addGmcWorkLog($request['company_id'],$request['staffer_id'],"同业接纳学员",'审核取消接纳学员信息',dataEncode($request));
            $res = array('error' => 0, 'errortip' => '审核取消成功', 'result' => array());
        } else {
            $res = array('error' => 1, 'errortip' => '审核取消失败', 'result' => array());

        }
        ajax_return($res,$request['language_type']);

    }

    //获取一条学员申请信息
    function getGuildstuOneApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        //model
        $StudentModel = new \Model\Gmc\GuildstuModel($request);
        $dataList = $StudentModel->getGuildstuOneApi($request);

        $field = array();
        $key = 0;
        $field[$key]["fieldname"] = "apply_contract";
        $field[$key]["fieldstring"] = "协议编号";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "apply_payprice";
        $field[$key]["fieldstring"] = "同业学员缴费金额";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "apply_paytime";
        $field[$key]["fieldstring"] = "同业学员合同缴费时间(起)";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "apply_payendtime";
        $field[$key]["fieldstring"] = "同业学员合同缴费时间(止)";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "apply_classtimes";
        $field[$key]["fieldstring"] = "同业学员剩余课时";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "apply_toclasstimes";
        $field[$key]["fieldstring"] = "转吉的堡课时";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "guildstutype_name";
        $field[$key]["fieldstring"] = "同业学员类型";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "apply_status_name";
        $field[$key]["fieldstring"] = "审核状态";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "apply_remarks";
        $field[$key]["fieldstring"] = "备注";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "statisticshour";
        $field[$key]["fieldstring"] = "当前剩余课时";
        $field[$key]["show"] = 0;
        $field[$key]["custom"] = 0;
        $key++;

        $result = array();
        if($dataList){
            $result["field"] = $field;
            $result["list"] = $dataList;
            $res = array('error' => '0', 'errortip' => '信息获取成功', 'result' => $result);
        }else{
            $result["field"] = $field;
            $result["list"] = array();
            $res = array('error' => '1', 'errortip' => '信息获取失败', 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }

    //获取同业学员类型
    function getGuildstutypeApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        //model
        $StudentModel = new \Model\Gmc\GuildstuModel($request);
        $dataList = $StudentModel->getGuildstutypeApi($request);

        $result = array();
        if($dataList){
            $result["list"] = $dataList;
            $res = array('error' => '0', 'errortip' => '同业类型获取成功', 'result' => $result);
        }else{
            $result["list"] = array();
            $res = array('error' => '1', 'errortip' => '同业类型获取失败', 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }

    //获取单个学员的基本信息
    function getGuildstuinfoApi(){
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        //model
        $StudentModel = new \Model\Gmc\GuildstuModel($request);
        $dataList = $StudentModel->getGuildstuinfoApi($request);

        $result = array();
        if($dataList){
            $result["list"] = $dataList;
            $res = array('error' => '0', 'errortip' => '信息获取成功', 'result' => $result);
        }else{
            $result["list"] = array();
            $res = array('error' => '1', 'errortip' => '信息获取失败', 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }

    //获取单个就读课程数据
    function getStuWastingClassApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        //model
        $StudentModel = new \Model\Gmc\GuildstuModel($request);
        $dataList = $StudentModel->getStuWastingClassApi($request);

        $field = array();
        $key = 0;
        $field[$key]["fieldname"] = "student_cnname";
        $field[$key]["fieldstring"] = "学员中文名";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "student_enname";
        $field[$key]["fieldstring"] = "学员英文名";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "student_branch";
        $field[$key]["fieldstring"] = "学员编号";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "study_beginday";
        $field[$key]["fieldstring"] = "学习开始时间";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "study_endday";
        $field[$key]["fieldstring"] = "学习结束时间";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "class_cnname";
        $field[$key]["fieldstring"] = "班级名称";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "class_enname";
        $field[$key]["fieldstring"] = "班级别名";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "class_branch";
        $field[$key]["fieldstring"] = "班级编号";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "course_cnname";
        $field[$key]["fieldstring"] = "课程名称";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "course_branch";
        $field[$key]["fieldstring"] = "课程编号";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "hour_name";
        $field[$key]["fieldstring"] = "第一次入课次";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "successhour";
        $field[$key]["fieldstring"] = "班级以上课次";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "allhour";
        $field[$key]["fieldstring"] = "班级全部课次";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "studyhour";
        $field[$key]["fieldstring"] = "已经消耗课次";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $result = array();
        if($dataList){
            $result["field"] = $field;
            $result["list"] = $dataList;
            $res = array('error' => '0', 'errortip' => '信息获取成功', 'result' => $result);
        }else{
            $result["field"] = $field;
            $result["list"] = array();
            $res = array('error' => '1', 'errortip' => '信息获取失败', 'result' => $result);
        }
        ajax_return($res,$request['language_type']);

    }

}
