<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/13
 * Time: 23:25
 */

namespace Work\Controller\Gmcapi;


class IntegralController extends viewTpl
{
    public $u;
    public $t;
    public $c;
    public $visitType = "api";
    public $Model;

    //预加载处理类
    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
    }

    function ThisVerify($request){
        $paramArray = array();
        $paramArray['staffer_id'] = $request['staffer_id'];
        $paramArray['company_id'] = $request['company_id'];
        $paramArray['token'] = $request['token'];
        if(!$this->UserLimit($paramArray)){
            $result = array();
            $result["list"] = array();
            $result["tokeninc"] = "0";
            $res = array('error' => '1', 'errortip' => "用户token失效", 'result' => $result);
            ajax_return($res,$request['language_type']);
        }
    }

    //积分类型列表
    function HomeView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model =  new \Model\Gmc\IntegralModel($request);

        $result = $this->Model->getIntegraltypeList($request);

        ajax_return($result,$request['language_type']);
    }

    //添加积分类型
    function addIntegraltypeAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model =  new \Model\Gmc\IntegralModel($request);

        $result = $this->Model->addIntegraltypeAction($request);
        ajax_return($result,$request['language_type']);
    }

    //编辑积分类型
    function updateIntegraltypeAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model =  new \Model\Gmc\IntegralModel($request);

        $result = $this->Model->updateIntegraltypeAction($request);

        ajax_return($result,$request['language_type']);
    }

    //删除积分类型
    function delIntegraltypeAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model =  new \Model\Gmc\IntegralModel($request);

        $result = $this->Model->delIntegraltypeAction($request);

        ajax_return($result,$request['language_type']);
    }

    //积分规则列表
    function IntegralruleListView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model =  new \Model\Gmc\IntegralModel($request);

        $result = $this->Model->IntegralruleList($request);

        ajax_return($result,$request['language_type']);
    }

    //添加积分规则
    function addIntegralruleAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model =  new \Model\Gmc\IntegralModel($request);

        $result = $this->Model->addIntegralruleAction($request);
        ajax_return($result,$request['language_type']);
    }

    //编辑积分规则
    function updateIntegralruleAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model =  new \Model\Gmc\IntegralModel($request);

        $result = $this->Model->updateIntegralruleAction($request);

        ajax_return($result,$request['language_type']);
    }

    //设置积分规则
    function updateIntegralAutoruleAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model =  new \Model\Gmc\IntegralModel($request);

        $result = $this->Model->updateIntegralAutoruleAction($request);

        ajax_return($result,$request['language_type']);
    }

    //删除积分规则
    function delIntegralruleAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model =  new \Model\Gmc\IntegralModel($request);

        $result = $this->Model->delIntegralruleAction($request);

        ajax_return($result,$request['language_type']);
    }


    //是否启用积分规则
    function isOpenRuleAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model =  new \Model\Gmc\IntegralModel($request);

        $result = $this->Model->isOpenRuleAction($request);

        ajax_return($result,$request['language_type']);
    }


    //积分区间列表
    function getIntegralsectionListView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model =  new \Model\Gmc\IntegralModel($request);

        $result = $this->Model->getIntegralsectionList($request);

        ajax_return($result,$request['language_type']);
    }

    //添加积分区间
    function addIntegralsectionAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model =  new \Model\Gmc\IntegralModel($request);

        $result = $this->Model->addIntegralsectionAction($request);
        ajax_return($result,$request['language_type']);
    }

    //编辑积分区间
    function updateIntegralsectionAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model =  new \Model\Gmc\IntegralModel($request);

        $result = $this->Model->updateIntegralsectionAction($request);

        ajax_return($result,$request['language_type']);
    }

    //删除积分区间
    function delIntegralsectionAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model =  new \Model\Gmc\IntegralModel($request);

        $result = $this->Model->delIntegralsectionAction($request);

        ajax_return($result,$request['language_type']);
    }

    //商品分类设置
    function getGoodsSortListView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model =  new \Model\Gmc\IntegralModel($request);

        $result = $this->Model->getGoodsSortList($request);

        ajax_return($result,$request['language_type']);
    }

    //编辑商品分类
    function updateGoodsSortAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model =  new \Model\Gmc\IntegralModel($request);

        $result = $this->Model->updateGoodsSortAction($request);

        ajax_return($result,$request['language_type']);
    }

    //是否启用
    function isOpenAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model =  new \Model\Gmc\IntegralModel($request);

        $result = $this->Model->isOpenAction($request);

        ajax_return($result,$request['language_type']);
    }

    //商品下级分类设置
    function getCategoryListView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model =  new \Model\Gmc\IntegralModel($request);

        $result = $this->Model->getCategoryList($request);

        ajax_return($result,$request['language_type']);
    }

    //添加商品下级分类
    function addCategoryAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model =  new \Model\Gmc\IntegralModel($request);

        $result = $this->Model->addCategoryAction($request);
        ajax_return($result,$request['language_type']);
    }

    //编辑商品下级分类
    function updateCategoryAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model =  new \Model\Gmc\IntegralModel($request);

        $result = $this->Model->updateCategoryAction($request);

        ajax_return($result,$request['language_type']);
    }

    //删除商品下级分类
    function delCategoryAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model =  new \Model\Gmc\IntegralModel($request);

        $result = $this->Model->delCategoryAction($request);

        ajax_return($result,$request['language_type']);
    }

    //结尾魔术函数
    function __destruct()
    {

    }
}
