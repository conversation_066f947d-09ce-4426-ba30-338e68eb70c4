<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2016/12/13
 * Time: 21:21
 */
namespace Work\Controller\Gmcapi;


class viewTpl {
    public $DataControl;
    public $router;

    public function __construct(){
        global $router;
        // 指定允许其他域名访问
        header('Access-Control-Allow-Origin:*');
        // 响应类型
        header('Access-Control-Allow-Methods:*');
        // 响应头设置
        header('Access-Control-Allow-Headers:x-requested-with,content-type');
        //数据库操作
        $this->DataControl = new \Dbmysql();
        //操作类型
        $this->router = $router;
    }

    //第三方接口权限验证
    function UserLimit($paramArray){
        $apiuser = $this->DataControl->getFieldOne("smc_staffer","staffer_tokencode"
            ,"company_id = '{$paramArray['company_id']}' AND staffer_id = '{$paramArray['staffer_id']}'");
        if($apiuser){
            $md5tokenbar = base64_encode(md5($apiuser["staffer_tokencode"].date("Y-m-d")));
            if($md5tokenbar !== $paramArray['token'] && $paramArray['token'] !== 'vcentmohism'){
                return false;
            }else{
                return true;
            }
        }else{
            return false;
        }
    }

    function ThisVerify($request){
        $paramArray = array();
        $paramArray['staffer_id'] = $request['staffer_id'];
        $paramArray['company_id'] = $request['company_id'];
        $paramArray['token'] = $request['token'];
        if(!$this->UserLimit($paramArray)){
            $result = array();
            $result["list"] = array();
            $result["tokeninc"] = "0";
            $res = array('error' => '1', 'errortip' => "用户token失效", 'result' => $result);
            ajax_return($res,$request['language_type']);
        }
    }

    //发送短信
    public function Sendmisgo($mobile,$mistxt,$tilte,$sendcode,$company_id='0'){
        $publicarray = array();
        $publicarray['company_id'] = $company_id;
        $minsendModel = new \Model\Api\SmsModel($publicarray);
        return $minsendModel->gmcMisSend($mobile,$mistxt,$tilte,$sendcode);
    }

    public function addGmcWorkLog($company_id,$staffer_id,$module,$type,$content)
    {
        $logData = array();
        $logData['company_id'] = $company_id;
        $logData['staffer_id'] = $staffer_id;
        $logData['worklog_module'] = $module;
        $logData['worklog_type'] = $type;
        $logData['worklog_content'] = $content;
        $logData['worklog_ip'] = real_ip();
        $logData['worklog_time'] = time();
        $this->DataControl->insertData('gmc_staffer_worklog', $logData);
    }

    public function addCrmWorkLog($company_id,$school_id,$marketer_id,$module,$type,$content){
        $stafferOne = $this->DataControl->getFieldOne("crm_marketer","staffer_id","marketer_id='{$marketer_id}'");
        $logData = array();
        $logData['company_id'] = $company_id;
        $logData['school_id'] = $school_id;
        $logData['staffer_id'] = $stafferOne['staffer_id'];
        $logData['worklog_module'] = $module;
        $logData['worklog_type'] = $type;
        $logData['worklog_content'] = $content;
        $logData['worklog_ip'] = real_ip();
        $logData['worklog_time'] = time();
        $this->DataControl->insertData('crm_staffer_worklog',$logData);
    }


    function createOrderPid($initial){
        $Str = "ABCDEFGHIJKLMNOPQRSTUVWXYZ123456789";
        $rangtr = $Str[rand(0,34)].$Str[rand(0,34)].$Str[rand(0,34)].$Str[rand(0,34)];
        $rangtime = date("ymdHis",time());
        $rangnum = rand(10000,99999);
        $OrderPID = $initial.$rangtr.$rangtime.$rangnum;
        return $OrderPID;
    }

    public function LgStringSwitch($cnstring){
        if($this->companyOne['company_language'] == 'tw'){
            $Model = new \Model\jianfanModel();
            $cnstring = $Model->gb2312_big5($cnstring);
            return $cnstring;
        }else{
            return $cnstring;
        }
    }

    public function LgArraySwitch($cnarray){
        if($this->companyOne['company_language'] == 'tw'){
            $Model = new \Model\jianfanModel();
            $dataString = $Model->gb2312_big5(json_encode($cnarray, JSON_UNESCAPED_UNICODE));
            $cnarray = json_decode($dataString, true);
            return $cnarray;
        }else{
            return $cnarray;
        }
    }

    //登录日志记录表
    function addStafferLoginLog($company_id,$staffer_id,$loginlog_type,$loginlog_source){
        $date = array();
        $date['company_id'] = $company_id;//集团ID
        $date['staffer_id'] = $staffer_id;//员工ID
        $date['loginlog_type'] = $loginlog_type;//登录端口 0 总入口  1 集团 2 校务 3 CRM 4 教务 5 助教 6 助学
        $date['loginlog_source'] = $loginlog_source;//客户端来源 0 PC 1 手机
        $date['loginlog_lastip'] = real_ip();
        $date['loginlog_time'] = time();
        $this->DataControl->insertData('imc_staffer_loginlog',$date);
        return true;
    }

    public function __call($method, $args) {
        echo "unknown method " . $method;
        return false;

    }
}
