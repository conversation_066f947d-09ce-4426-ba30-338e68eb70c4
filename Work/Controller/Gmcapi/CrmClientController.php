<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/13
 * Time: 23:25
 */

namespace Work\Controller\Gmcapi;


class CrmClientController extends viewTpl
{
    public $u;
    public $t;
    public $c;
    public $visitType = "api";
    public $Model;

    //预加载处理类
    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();

    }

    function ThisVerify($request)
    {
        $paramArray = array();
        $paramArray['staffer_id'] = $request['staffer_id'];
        $paramArray['company_id'] = $request['company_id'];
        $paramArray['token'] = $request['token'];
        if (!$this->UserLimit($paramArray)) {
            $result = array();
            $result["list"] = array();
            $result["tokeninc"] = "0";
            $res = array('error' => '1', 'errortip' => "用户token失效", 'result' => $result);
            ajax_return($res,$request['language_type']);
        }
    }

    /**
     * 已分配学校的名单
     */
    function getClientListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        if (!isset($request['company_id']) && $request['company_id'] !== '') {
            $res = array('error' => '1', 'errortip' => "请传入集团ID", 'result' => array());
            ajax_return($res,$request['language_type']);
        }
        $publicarray = array();
        $publicarray['company_id'] = $request['company_id'];
        $publicarray['staffer_id'] = $request['staffer_id'];
        $Model = new \Model\Gmc\CrmClientModel($publicarray);
        $datalist = $Model->getClientList($request);
        $fieldname = array(
            'client_cnname',
            'client_sex',
            'client_age',
            'client_tag',
            'course_cnname',
            'client_patriarchname',
            'client_mobile',
            'school_cnname',
            'school_branch',
            'client_status',
            'client_answerphone',
            'track_count',
            'track_lasttime',
            'track_lasttrack_note',
            'client_intention_level',
            'activity_name',
            'client_source',
            'channel_name',
            'client_address',
            'client_soursename',
            'client_frompage',
            'positivelog_time',
            'student_thirdbranch',
            'client_createtime',
            'shengshiqu');
        $fieldstring = array(
            '姓名',
            '性别',
            '年龄',
            '标签',
            '意向课程',
            '主要联系人姓名',
            '主要联系手机',
            '所属校区',
            '校区编号',
            '客户状态',
            '是否接通',
            '跟进次数',
            '最后跟进日期',
            '最后跟进内容',
            '意向星级',
            '来源活动',
            '渠道类型',
            '渠道明细',
            '联系地址',
            '渠道备注',
            '来源页面',
            '报名时间',
            '学号',
            '创建时间',
            "省市区");
        if($Model->companyOne['company_isopenspecialnumber'] != '1') {
            $fieldname = array_filter($fieldname,function ($value){
                return $value != 'student_thirdbranch';
            });
            $fieldname = array_merge($fieldname);
            $fieldstring = array_filter($fieldstring,function ($value){
                return $value != '学号';
            });
            $fieldstring = array_merge($fieldstring);
        }
        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["fieldname"] = trim($fieldname[$i]);
            $field[$i]["custom"] = 1;
            $field[$i]["show"] = 1;
            if ($fieldname[$i] == 'school_branch') {
                $field[$i]["custom"] = 1;
                $field[$i]["show"] = 0;
            }
            if ($fieldname[$i] == 'client_soursename') {
                $field[$i]["custom"] = 1;
                $field[$i]["show"] = 0;
            }
            if ($fieldname[$i] == 'shengshiqu') {
                $field[$i]["custom"] = 1;
                $field[$i]["show"] = 0;
            }
            if ($fieldname[$i] == 'client_intention_level') {
                $field[$i]["custom"] = 0;
                $field[$i]["show"] = 0;
                $field[$i]["islevel"] = "true";
            }
            if ($field[$i]["fieldname"] == "client_cnname") {
                $field[$i]["ismethod"] = 1;
            }
            if($field[$i]["fieldname"] == 'client_tag' || $field[$i]["fieldname"] == 'course_cnname'){
                $field[$i]["istag"] = 1;
            }
        }
        $result['field'] = $field;
        if ($datalist['list']) {
            $result['list'] = $datalist['list'];
        } else {
            $result['list'] = array();
        }
        if (isset($datalist['allnums']) && $datalist['allnums'] != "") {
            $allnum = intval($datalist['allnums']);
        } else {
            $allnum = 0;
        }
        $result['allnum'] = $allnum;
        if ($result['list']) {
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result, "allnum" => $allnum);
        } else {
            $res = array('error' => '1', 'errortip' => "暂无招生名单", 'result' => $result, "allnum" => $allnum);
        }
        ajax_return($res,$request['language_type']);
    }

    /**
     * 待分配名单/无意向名单/
     */
    function getUnAssignmentListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Gmc\CrmClientModel();
        $res = $Model->getUnAssignmentList($request);

        $fieldstring = array('client_cnname','client_tag','course_cnname','client_patriarchname','client_sex','client_age','client_birthday','client_mobile','activity_name','client_frompage','channel_medianame','channel_name','client_address','client_gmcmarket','outthree_userid','client_createtime','client_updatetime','client_tmkbatch','client_remark','shengshiqu');
        $fieldname = $this->LgArraySwitch(array('姓名', '标签','意向课程', '家长称呼', '性别', '年龄', '出生日期', '手机号', '来源活动', '接触点', '渠道类型', '渠道明细', '联系地址', '集团负责人', 'UserThreeId', '创建时间', '更新时间','批次编号', '备注', '省市区'));
        $fieldcustom = array("1", "1","1", "1", "1", "1", "1", "1", "1","1", "1", "1",  "1", "1", "1", "1", "1", "1","1","1");
        $fieldshow = array("1", "1","1", "1", "1", "1", "0", "1", "1", "1", "1",  "1", "1","0",  "0", "1", "1", "1","1","1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
            if ($field[$i]["fieldname"] == "client_cnname") {
                $field[$i]["ismethod"] = 1;
            }
            if ($field[$i]["fieldname"] == "client_tag" || $field[$i]["fieldname"] == "course_cnname") {
                $field[$i]["istag"] = 1;
            }
        }

        $resultarray = array();
        $resultarray['field'] = $field;
        $resultarray['fieldcustom'] = $res['fieldcustom']?$res['fieldcustom']:'';
        $resultarray['all_num'] = $res['all_num']>0?$res['all_num']:'0';
        $resultarray['list'] = is_array($res['list'])?$res['list']:array();

        $result = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $resultarray);
        ajax_return($result,$request['language_type']);
    }

    /**
     * 集团招生名单管理-招生有效名单-基本信息
     */
    function clientOneApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Gmc\CrmClientModel($request);
        $dataOne = $Model->getClientOne($request['client_id'],$request['company_id']);
        $field['client_id'] = "客户id";
        $field['client_img'] = "头像";
        $field['client_sex'] = "性别";
        $field['client_cnname'] = "中文名";
        $field['client_enname'] = "英文名";
        $field['client_mobile'] = "主要联系手机";
        $field['main_family_cnname'] = "主要联系人";
        $field['client_source'] = "招生渠道类型";
        $field['channel_name'] = "招生渠道类型";
        $field['activity_name'] = "活动名称";
        $field['client_stubranchnname'] = "推荐人";
        $field['client_fromtype_name'] = "招生来源类型";
        $field['client_intention_level'] = "意向等级";
        $field['client_birthday'] = "出生日期";
        $field['client_icard'] = "身份证号";
        $field['nearschool_name'] = "附近就读学校";
        $field['client_oh_month'] = "OH月份";
        $field['client_push_month'] = "PUSH月份";
        $field['client_sponsor'] = "介绍人";
        $field['client_stubranchname'] = "推荐人";
        $field['client_remark'] = "备注";
        $field['lasttracttime'] = "最后跟踪时间";
        $field['client_tags'] = "标签";
        $field['client_family_list']['family_cnname'] = "中文名";
        $field['client_family_list']['family_mobile'] = "联系电话";
        $field['client_tracestatus'] = "客户状态";
        $field['main_marketer_name'] = "主负责人";
        $field['client_principal_name'] = "副负责人";
        $field['client_family_list']['family_id'] = "亲属关系id";
        $field['client_family_list']['family_relation'] = "亲属关系";
        $field['client_family_list']['family_isdefault'] = "是否为主要联系人";
        $field['client_course_list']['intention_id'] = "意向课程id";
        $field['client_course_list']['course_cnname'] = "意向课程";

        $field['client_count_list']['track_num'] = "跟进总次数";
        $field['client_count_list']['visit_num'] = "普通回访次数";
        $field['client_count_list']['invite_num'] = "柜询次数";
        $field['client_count_list']['audition_num'] = "视同次数哦";
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['list'] = $dataOne;

        $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        ajax_return($res,$request['language_type']);
    }

    /**
     * 跟踪记录
     */
    function clientOneTrackApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $client_id = $request['client_id'];
        if (!$client_id) {
            $res = array('error' => '1', 'errortip' => "客户id有误", 'result' => array());
            ajax_return($res,$request['language_type']);
        }
        $Model = new \Model\Crm\ClientModel();
        $dataOne = $Model->getclientTrackList($request);
        if ($dataOne) {
            foreach ($dataOne as $key => $value) {
                $value['track_createtime'] = date('Y-m-d H:i:s');
            }
        }
        $field['marketer_name'] = "负责人";
        $field['marketer_img'] = "负责人头像";
        $field['track_linktype'] = "沟通方式";
        $field['client_img'] = "客户头像";
        $field['track_followmode'] = "本次跟进模式0普通回访1柜询2视听3转正-1无意向跟进";
        $field['track_followuptype'] = "下次跟进类型";  //0普通跟进1提醒跟进
        $field['track_followuptime'] = "下次跟进时间";
        $field['commode_name'] = "沟通类型";
        $field['track_visitingtime'] = "柜询/视听时间";
        $field['main_marketer_name'] = "主负责人";
        $field['track_note'] = "沟通内容";
        $field['track_createtime'] = "创建时间";
        $field['class_cnname'] = "试听班级";
        $field['object_name'] = "沟通对象";
        $field['track_invalidreason'] = '无效时选择的原因';
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($dataOne) {
            $result['list'] = $dataOne;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无跟踪记录", 'result' => $result);
        }

        ajax_return($res,$request['language_type']);

    }

    function clientOneInviteApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $client_id = $request['client_id'];
        if (!$client_id) {
            $res = array('error' => '1', 'errortip' => "客户id有误", 'result' => array());
            ajax_return($res,$request['language_type']);
        }
        $Model = new \Model\Crm\ClientModel();
        $datalist = $Model->getclientInviteList($client_id);
        $fieldstring = array('school_cnname','invite_genre', 'invite_level', 'invite_visittime', 'invite_isvisit', 'invite_novisitreason');
        $fieldname = array('柜询校区','柜询类型', '客户意向', '柜询日期', '是否到访', '原因');
        $fieldcustom = array("1", "1", "1", "1", "1", '0');
        $fieldshow = array("1", "1", "1", "1", '1', '0');
        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
            if ($field[$i]["fieldname"] == "invite_level") {
                $field[$i]["isLevel"] = true;
            }
            if ($field[$i]["fieldname"] == 'invite_isvisit') {
                $field[$i]["isTip"] = "1";
            }
        }
        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        if ($datalist) {
            $result['list'] = $datalist;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无柜询记录", 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }

    /**
     * 客户试听记录
     */
    function clientOneAuditionApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $client_id = $request['client_id'];
        if (!$client_id) {
            $res = array('error' => '1', 'errortip' => "客户id有误", 'result' => array());
            ajax_return($res,$request['language_type']);
        }
        $Model = new \Model\Crm\ClientModel();
        $datalist = $Model->getclinetAuditionList($client_id);

        $fieldstring = array('school_cnname','class_cnname','track_intention_level', 'audition_visittime', 'course_cnname', 'audition_isvisit', 'invite_novisitreason');
        $fieldname = array('试听校区','试听班级','客户意向', "试听日期", '试听课程', '是否到访', '原因');
        $fieldcustom = array("1","1", "1", "1", "1", "1", '0');
        $fieldshow = array("1","1", "1", "1", "1", "1", '0');
        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
            if ($field[$i]["fieldname"] == 'audition_isvisit') {
                $field[$i]["isTip"] = "1";
            }
            if ($field[$i]["fieldname"] == 'track_intention_level') {
                $field[$i]["isLevel"] = true;
            }
        }
        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['list'] = $datalist;
        if ($datalist) {
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无试听记录", 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }

    // 获取渠道类型
    function getSourceApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $company_id = $request['company_id'];
        if (!$company_id) {
            $res = array('error' => '1', 'errortip' => "公司id有误", 'result' => array());
            ajax_return($res,$request['language_type']);
        }
        $Model = new \Model\Crm\PublicModel();
        $dataList = $Model->getSourceFromMedia($company_id, '0');
        $field['frommedia_id'] = "序号id";
        $field['commode_name'] = "来源名称";
        $result['field'] = $field;
        if ($dataList) {
            $result['list'] = $dataList;
        } else {
            $result['list'] = array();
        }
        $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        ajax_return($res,$request['language_type']);
    }

    // 获取渠道类型 -- 吉的堡去除转介绍
    function getSourceTwoApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $company_id = $request['company_id'];
        if (!$company_id) {
            $res = array('error' => '1', 'errortip' => "公司id有误", 'result' => array());
            ajax_return($res,$request['language_type']);
        }
        $Model = new \Model\Crm\PublicModel();
        $dataList = $Model->getSourceFromMediaTwo($company_id, '0');
        $field['frommedia_id'] = "序号id";
        $field['commode_name'] = "来源名称";
        $result['field'] = $field;
        if ($dataList) {
            $result['list'] = $dataList;
        } else {
            $result['list'] = array();
        }
        $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        ajax_return($res,$request['language_type']);
    }

    // 获取来源活动
    function getActivityApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $company_id = $request['company_id'];
        if (!$company_id) {
            $res = array('error' => '1', 'errortip' => "公司id有误", 'result' => array());
            ajax_return($res,$request['language_type']);
        }
        $Model = new \Model\Crm\PublicModel();
        $dataList = $Model->getActivityApi($request);
        $field['activity_id'] = "序号id";
        $field['activity_name'] = "来源名称";
        $result['field'] = $field;
        if ($dataList) {
            $result['list'] = $dataList;
        } else {
            $result['list'] = array();
        }
        $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        ajax_return($res,$request['language_type']);
    }

    //获取渠道明细
    function getChannelApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $company_id = $request['company_id'];
        if (!$company_id) {
            $res = array('error' => '1', 'errortip' => "公司id有误", 'result' => array());
            ajax_return($res,$request['language_type']);
        }
        $Model = new \Model\Crm\PublicModel();
        $dataList = $Model->getClientChannel($request, '0');
        $field['channel_id'] = "渠道id";
        $field['channel_name'] = "渠道名称";
        if ($dataList) {
            $result['list'] = $dataList;
        } else {
            $result['list'] = array();
        }
        $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        ajax_return($res,$request['language_type']);
    }

    //获取渠道板块
    function getChannelBoardApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
//        $this->ThisVerify($request);//验证账户
        $company_id = $request['company_id'];
        if (!$company_id) {
            $res = array('error' => '1', 'errortip' => "公司id有误", 'result' => array());
            ajax_return($res,$request['language_type']);
        }
        $Model = new \Model\Crm\PublicModel();
        $dataList = $Model->getChannelBoardApi($request);
        $field['channel_name'] = "渠道板块名称";
        if ($dataList) {
            $result['list'] = $dataList;
        } else {
            $result['list'] = array();
        }
        $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        ajax_return($res,$request['language_type']);
    }

    //获取渠道明细 -- 吉的堡去除转介绍
    function getChannelTwoApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $company_id = $request['company_id'];
        if (!$company_id) {
            $res = array('error' => '1', 'errortip' => "公司id有误", 'result' => array());
            ajax_return($res,$request['language_type']);
        }
        $Model = new \Model\Crm\PublicModel();
        $dataList = $Model->getClientChannelTwo($request, '0');
        $field['channel_id'] = "渠道id";
        $field['channel_name'] = "渠道名称";
        if ($dataList) {
            $result['list'] = $dataList;
        } else {
            $result['list'] = array();
        }
        $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        ajax_return($res,$request['language_type']);
    }

    //获取渠道明细
    function getReportClientChannelView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $company_id = $request['company_id'];
        if (!$company_id) {
            $res = array('error' => '1', 'errortip' => "公司id有误", 'result' => array());
            ajax_return($res,$request['language_type']);
        }
        $Model = new \Model\Crm\PublicModel();
        $dataList = $Model->getReportClientChannel($request, '0');
        $field['channel_id'] = "渠道id";
        $field['channel_name'] = "渠道名称";
        if ($dataList) {
            $result['list'] = $dataList;
        } else {
            $result['list'] = array();
        }
        $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        ajax_return($res,$request['language_type']);
    }

    //检测重复手机号 -- TMK要可以录入名单
    function checkClientMobileApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        if (empty($request['client_cnname'])) {
            $res = array('error' => '1', 'errortip' => "用户名为空", 'result' => array());
            ajax_return($res, $request['language_type']);

        }
        if (empty($request['client_mobile'])) {
            $res = array('error' => '1', 'errortip' => "手机号为空", 'result' => array());
            ajax_return($res, $request['language_type']);
        }

        $from = '';
        $Model = new \Model\Gmc\CrmClientModel($request);
        $clientList = $Model->checkClientMobile($request, $from);

        if ($clientList) {
            $fieldname = array('client_id ', 'client_img', 'client_enname', 'client_cnname', 'client_sex',
                'client_age', 'school_cnname','client_source','channel_name','marketer_name',
                'client_tracestatus_name', 'client_mobile',  'client_status');
            $fieldstring = array('客户id', '头像', '英文名', '姓名', '性别',
                '年龄', '所属学校', '渠道类型', '渠道明细','主要负责人',
                '客户状态', '主要联系手机', '客户状态ID');
            $fieldcustom = array('0', "1", '0', "1", "1",     "1", "1", "1", "1", "1",     "1", "1", "0");
            $fieldshow = array('0', "0", '0', "1", "1",      "1", "1", "1", "1", "1",     "1", "0", "0");

            $field = array();
            for ($i = 0; $i < count($fieldstring); $i++) {
                $field[$i]["fieldname"] = trim($fieldstring[$i]);
                $field[$i]["fieldstring"] = trim($fieldname[$i]);
                $field[$i]["custom"] = trim($fieldcustom[$i]);
                $field[$i]["show"] = trim($fieldshow[$i]);
            }
            $result['fieldcustom'] = 0;
            $result['field'] = $field;
            $result['list'] = $clientList;
            $res = array('error' => '0', 'errortip' => "拥有重复的数据,请确认", 'result' => $result);
        } else {
            $res = array('error' => '1', 'errortip' => "唯一的记录", 'result' => array());
        }
        ajax_return($res, $request['language_type']);
    }

    //录入名单 -- TMK要可以录入名单
    function addClientAction(){
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        if (empty($request['client_cnname'])) {
            $res = array('error' => '1', 'errortip' => '用户名为空', 'result' => array());
            ajax_return($res, $request['language_type']);
        }

        $Model = new \Model\Gmc\CrmClientModel($request);
        $bools = $Model->insertClientOne($request);
        if($bools){
            $this->addGmcWorkLog($request['company_id'],$request['staffer_id'],"集团名单管理->新增名单",'添加有效名单',dataEncode($request));
        }

        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $Model->result?$Model->result:array());
        ajax_return($res, $request['language_type']);
    }

    //录入名单 -- TMK我要跟踪
    function trackRepeatClientAction(){
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Gmc\CrmClientModel($request);
        $Model->trackRepeatClient($request);
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }




    /**
     * 跟进-提交
     */
    function insertTrackClientOneAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        if (!$request['client_id']) {
            $res = array('error' => '1', 'errortip' => "请选择学员", 'result' => array());
            ajax_return($res,$request['language_type']);
        }
//        if (!isset($request['client_answerphone'])) {
//            $res = array('error' => '1', 'errortip' => "请选择是否接通", 'result' => array());
//            ajax_return($res, $request['language_type']);
//        }
        $Model = new \Model\Gmc\CrmClientModel($request);
        $bools = $Model->insertTrackClientOne($request);
        if ($bools) {
            $res = array('error' => '0', 'errortip' => "集团跟进成功", 'result' => array());
        } else {
            $res = array('error' => '1', 'errortip' => $Model->errortip, 'result' => array());
        }
        ajax_return($res,$request['language_type']);
    }
    function followUpClientsAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        if (!$request['clients_json']) {
            $res = array('error' => '1', 'errortip' => "请选择学员", 'result' => array());
            ajax_return($res,$request['language_type']);
        }
        $Model = new \Model\Gmc\CrmClientModel();
        $bools = $Model->followUpClients($request);
        if ($bools) {
            $res = array('error' => '0', 'errortip' => "跟进成功", 'result' => array());
        } else {
            $res = array('error' => '0', 'errortip' => "跟进失败:(", 'result' => array());
        }
        ajax_return($res,$request['language_type']);
    }

    //获取沟通方式
    function getCommodeApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $company_id = $request['company_id'];

        if (!$company_id) {
            $res = array('error' => '1', 'errortip' => "公司id有误", 'result' => array());
            ajax_return($res,$request['language_type']);
        }

        $Model = new \Model\Crm\PublicModel();
        $dataList = $Model->getCommedeByCompanyId($company_id);

        $field['commode_id'] = "序号id";
        $field['commode_name'] = "方式名称";
        $result['field'] = $field;
        $result['fieldcustom'] = 1;
        $result['field'] = $field;
        if ($dataList) {
            $result['list'] = $dataList;
        } else {
            $result['list'] = array();
        }

        $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        ajax_return($res,$request['language_type']);
    }

    //获取沟通模板
    function getTraceNoteApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
         $this->ThisVerify($request);//验证账户
        $company_id = $request['company_id'];
        if (!$company_id) {
            $res = array('error' => '1', 'errortip' => "公司id有误", 'result' => array());
            ajax_return($res,$request['language_type']);
        }
        $Model = new \Model\Crm\PublicModel();
        $dataList = $Model->getTraceNote($company_id);
        $field['tracenote_id'] = "序号id";
        $field['tracenote_code'] = "模板编号";
        $field['tracenote_remk'] = "模板内容";
        $result['field'] = $field;
        if ($dataList) {
            $result['list'] = $dataList;
        } else {
            $result['list'] = array();
        }
        $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        ajax_return($res,$request['language_type']);
    }

    //获取沟通对象
    function getObjectApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $dataList = $this->DataControl->selectClear("select co.object_code,co.object_name from crm_code_object  as co where 1 ");
        $result['list'] = $dataList;
        if ($dataList) {
            $res = array('error' => '0', 'errortip' => '获取沟通对象成功', 'result' => $result);
        } else {
            $res = array('error' => '0', 'errortip' => '获取沟通对象成功', 'result' => array());
        }
        ajax_return($res,$request['language_type']);
    }

    //获对应的组织
    function getOrganizeByCompanyApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $datawhere = '1';

        if ($request['dataequity'] != '1') {
            $organzie_id = $this->DataControl->getFieldOne("gmc_staffer_postbe", "organize_id", "postbe_id = '{$request['re_postbe_id']}'");
            $datawhere .= " and (organize_id ='{$organzie_id['organize_id']}' or father_id='{$organzie_id['organize_id']}') ";

            $data = $this->DataControl->selectClear("
            select organize_id as id,father_id,organize_cnname as label from gmc_company_organize  where company_id='{$request['company_id']}'  and {$datawhere}
            ");
        } else {
            $data = $this->DataControl->selectClear("
            select organize_id as id,father_id,organize_cnname as label from gmc_company_organize  where company_id='{$request['company_id']}'  and {$datawhere}
            ");
        }


        $arr_id = array_column($data, 'id');

        $orgList = array();

        if ($data) {
            foreach ($data as $value) {
                if ($value['father_id'] == 0) {
                    $orgList[$value['id']] = $value;

                } else {
                    if (in_array($value['father_id'], $arr_id)) {
                        $orgList[$value['father_id']]['children'][] = $value;
                    } else {
                        $orgList[] = $value;
                    }
                }
            }
        }
        $res = array('error' => '1', 'errortip' => '获取组织成功', 'result' => $orgList);
        ajax_return($res,$request['language_type']);
    }


    function getComorganzizeApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $orgList = $this->DataControl->selectClear("
        select co.* from gmc_code_organizeclass as  co where co.company_id= '{$request['company_id']}'
        
        ");
        if (!$orgList) {
            $orgList = array();
        }
        $res = array('error' => '0', 'errortip' => '获取组织成功', 'result' => $orgList);
        ajax_return($res,$request['language_type']);
    }

    //下载导入模版
    function getImportApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\CrmClientModel();

        $result = $this->Model->getImportApi($request);
        ajax_return($result,$request['language_type']);
    }

    //检查接口
    function ImportExcelView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $url = $request['url'];

        $ys_array = array('中文名' => 'a', '英文名' => 'b', '性别' => 'c', '年龄' => 'd', '出生日期' => 'e', '所在地址' => 'f', '身份证号码' => 'g', '校区编号' => 'h', '介绍人' => 'i', '联系人姓名' => 'j', '联系人手机' => 'k', '关系' => 'l', '备注' => 'm');

        $options=array(
            "ssl"=>array(
                "verify_peer"=>false,
                "verify_peer_name"=>false,
            ),
        );

        file_put_contents('analysis.xls', file_get_contents($url,false,stream_context_create($options)));
        $sqlarray = execl_to_array("analysis.xlsx", $ys_array);
        array_shift($sqlarray);

        foreach ($sqlarray as $item) {
            $data = array();
            $data['client_cnname'] = $item['a'];
            $data['client_enname'] = $item['b'];
            $data['client_sex'] = $item['c'];
            $data['client_age'] = $item['d'];
            $data['company_id'] = $request['company_id'];
            $data['client_source'] = $request['frommedia_name'];
            $data['channel_id'] = $request['channel_id'];
            if ($request['activity_id']) {
                $data['activity_id'] = $request['activity_id'];
                $data['client_updatetime'] = time();

            }
            $data['client_birthday'] = $item['e'];
            $data['client_address'] = $item['f'];
            $data['client_icard'] = $item['g'];
            $data['client_sponsor'] = $item['i'];
            $data['client_remark'] = $item['m'];
            $data['client_mobile'] = $item['k'];
            $data['client_createtime'] = time();
            if ($item['e']) {
                $id = $this->DataControl->insertData("crm_client", $data);
                if ($id) {
                    $trackData = array();
                    $trackData['client_id'] = $id;
                    $trackData['marketer_id'] = '0';
                    $trackData['marketer_name'] = '系统管理员';
                    $trackData['track_validinc'] = 1;
                    $trackData['track_linktype'] = '名单导入';
                    $trackData['track_note'] = "由集团管理中心导入名单,新建客户信息";
                    $trackData['track_createtime'] = time();
                    $trackData['track_type'] = 1;
                    $trackData['track_initiative'] = 1;
                    $this->DataControl->insertData('crm_client_track', $trackData);
                }
            }
            $school_id = $this->DataControl->getFieldOne("smc_school", "school_id", "school_branch = '{$item['h']}'");
            $datas = array();
            $datas['client_id'] = $id;
            $datas['school_id'] = $school_id['school_id'];
            $datas['company_id'] = $request['company_id'];
            if ($item['e']) {
                $datas['schoolenter_createtime'] = time();
                $datas['schoolenter_updatetime'] = time();
                $this->DataControl->insertData('crm_client_schoolenter', $datas);
            }
            $partent = $this->DataControl->selectOne("select * from smc_parenter as p Where p.parenter_mobile ='{$item['k'] }'");

            $relation = array();
            if ($partent) {
                $relation['parenter_id'] = $partent['parenter_id'];
            } else {
                $pardata = array();
                $pardata['parenter_nickname'] = $item['j'];
                $pardata['parenter_cnname'] = $item['j'];
                $pardata['parenter_mobile'] = $item['k'];
                $pardata['parenter_pass'] = md5(substr($item['k'],-6));
                $pardata['parenter_bakpass'] = substr($item['k'],-6);
                $id = $this->DataControl->insertData("smc_parenter", $pardata);
                $relation['parenter_id'] = $partent['$id'];
            }
            $relation['family_relation'] = $item['l'];
            $relation['client_id'] = $id;
            $relation['company_id'] = $request['company_id'];
            $relation['family_createtime'] = time();

            if ($item['e']) {
                $this->DataControl->insertData("crm_client_family", $relation);

            }
        }

        $res = array('error' => '0', 'errortip' => '导入学员成功');
        ajax_return($res,$request['language_type']);

    }

    //招生券管理列表
    function enrollTicketListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Crm\ClientModel();

        $result = $this->Model->enrollTicketList($request);
        ajax_return($result,$request['language_type']);
    }

    //招生名单列表
    function freevoucherListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Crm\ClientModel();

        $result = $this->Model->freevoucherList($request);
        ajax_return($result,$request['language_type']);
    }

    //生成招生券
    function createEnrollTicketAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Crm\ClientModel();

        $result = $this->Model->createEnrollTicketAction($request);
        ajax_return($result,$request['language_type']);
    }

    //申请变更渠道
    function applyChannelAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\CrmClientModel();

        $result = $this->Model->applyChannelAction($request);
        ajax_return($result,$request['language_type']);
    }

    //下载导入模版
    function getImportActivityOneApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\CrmClientModel();

        $result = $this->Model->getImportActivityOneApi($request);
        ajax_return($result,$request['language_type']);
    }

    //下载导入模 版
    function getImportActivityChannelApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\CrmClientModel();

        $result = $this->Model->getImportActivityChannelApi($request);
        ajax_return($result,$request['language_type']);
    }

    //下载导入模版
    function getImportActivitysApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\CrmClientModel();

        $result = $this->Model->getImportActivitysApi($request);
        ajax_return($result,$request['language_type']);
    }

    function ImportActivityOneView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $url = $request['url'];

        if ($request['upload_type'] == '0') {
            $ys_array = array('校区编号' => 'school_branch', '校区名称' => 'school_cnname', '日期' => 'updatetime', '学员姓名' => 'student_cnname', '性别' => 'sex', '联系方式' => 'mobile', '年龄' => 'age', '意向班种代码' => 'coursecatstr', '电销姓名' => 'client_gmcmarket', '客户备注' => 'remark');
        } else {
            $ys_array = array('校区编号' => 'school_branch', '校区名称' => 'school_cnname', '日期' => 'updatetime', '学员姓名' => 'student_cnname', '性别' => 'sex', '联系方式' => 'mobile', '年龄' => 'age', '意向班种代码' => 'coursecatstr', '活动名称' => 'activity_name', '电销姓名' => 'client_gmcmarket', '客户备注' => 'remark');
        }
        $options=array(
            "ssl"=>array(
                "verify_peer"=>false,
                "verify_peer_name"=>false,
            ),
        );
        file_put_contents('analysis.xlsx', file_get_contents($url,false,stream_context_create($options)));
        $sqlarray = execl_to_array("analysis.xlsx", $ys_array);
        array_shift($sqlarray);

//        foreach($sqlarray as $k=>$v){
//            $sqlarray[$k] = array_filter($v);
//        }
//
        foreach ($sqlarray as &$item) {
            $d = 25569;
            $t = 24 * 60 * 60;
            if($item['updatetime'] != ''){
                $item['updatetime'] = gmdate('Y-m-d', ($item['updatetime'] - $d) * $t);
            }else {
                $item['updatetime'] = date('Y-m-d',time());
            }


            if ($item['school_branch']) {
                $school_id = $this->DataControl->getFieldOne("smc_school", "school_id", "school_branch = '{$item['school_branch']}'");
                $item['school_id'] = $school_id['school_id'];
            }
            if (!$item['school_id'] && $item['school_cnname']) {
                $school_id = $this->DataControl->getFieldOne("smc_school", "school_id", "school_cnname = '{$item['school_cnname']}'");
                $item['school_id'] = $school_id['school_id'];
            }
            if (!$school_id) {
                $item['school_id'] = 0;
            }
            if ($request['upload_type'] == '1') {

                if ($item['activity_name']) {
                    $activity_id = $this->DataControl->getFieldOne("crm_sell_activity", "activity_id", "activity_name = '{$item['activity_name']}' and company_id = '{$request['company_id']}'");
                    $item['activity_id'] = $activity_id['activity_id'];
                }
            }
        }

        if ($request['upload_type'] == '0') {
            $fieldstring = array('school_cnname', 'school_branch', 'updatetime', 'student_cnname', 'mobile', 'age', 'coursecatstr', 'client_gmcmarket', 'remark');
            $fieldname = array('校区名称', '校区编号', '日期', '学员姓名', '联系方式', '年龄', '意向班种代码', '电销姓名', '客户备注');
            $fieldcustom = array("1", "1", "1", "1", "1", "1", "1", "1", "1");
            $fieldshow = array("1", "1", "1", "1", "1", "1", "1", "1", "1");
        } else {
            $fieldstring = array('school_cnname', 'school_branch', 'updatetime', 'student_cnname', 'mobile', 'age','coursecatstr', 'activity_name', 'client_gmcmarket', 'remark');
            $fieldname = array('校区名称', '校区编号', '日期', '学员姓名', '联系方式', '年龄','意向班种代码', '活动名称', '电销姓名', '客户备注');
            $fieldcustom = array("1", "1", "1", "1", "1", "1", "1", "1", "1", "1");
            $fieldshow = array("1", "1", "1", "1", "1", "1", "1", "1", "1", "1");
        }


        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['list'] = $sqlarray;

        $res = array('error' => '0', 'errortip' => '导入学员成功', 'result' => $result);
        ajax_return($res,$request['language_type']);

    }

    function ImportActivityChannelView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $excelUrl = $request['url'];
        $excelArray = array('校区编号' => 'school_branch', '校区名称' => 'school_cnname', '日期' => 'updatetime', '学员姓名' => 'student_cnname','家长姓名' => 'client_patriarchname','Email' => 'client_email','地址' => 'client_address', '性别' => 'sex', '联系方式' => 'mobile', '年龄' => 'age', '电销姓名' => 'client_gmcmarket', '渠道名称' => 'channel_name', '接触点' => 'client_frompage','意向班种代码' => 'coursercatstr', '客户备注' => 'remark','第三方'=>'outthree_userid');
        //,'渠道重新激活是否进校区'=>'client_istoschool'  -- 20250324去除换到渠道上


//        $dir = '../static/excelfile/';
//        $excelname = date("YmdHis").rand(111111,999999).".xlsx";
//        $excelname = $dir . $excelname; // 将图片定位到正确地址
//
//        file_put_contents($excelname, file_get_contents($url));
//        $sqlarray = execl_to_array($excelname, $ys_array);
//        array_shift($sqlarray);
////
//        foreach($sqlarray as $k=>$v){
//            $sqlarray[$k] = array_filter($v);
//        }

        $options=array(
            "ssl"=>array(
                "verify_peer"=>false,
                "verify_peer_name"=>false,
            ),
        );

        file_put_contents("analysis.xls", file_get_contents($excelUrl,false,stream_context_create($options)));
        $excelList = execl_to_array("analysis.xls", $excelArray);
        array_shift($excelList);

        $gmcChannels = $this->DataControl->selectClear("select channel_name from crm_code_channel where company_id = '{$request['company_id']}' and channel_istoschool = '0'  ");
        $namesGmcChannel = array_column($gmcChannels, 'channel_name');//进集团

        foreach ($excelList as &$item) {
//            //之前的代码  我看不懂
//            $d = 25569;
//            $t = 24 * 60 * 60;
//            $item['updatetime'] = gmdate('Y-m-d', ($item['updatetime'] - $d) * $t);

            $item['channel_name'] = trim($item['channel_name']);
            if(in_array($item['channel_name'], $namesGmcChannel)) {//渠道重新激活是否进校区
                $item['istoschool'] = '否';//进集团
            }else{
                $item['istoschool'] = '是';
            }

            if($item['updatetime'] == ''){
                $item['updatetime'] = date('Y-m-d', time());
            }else{
                $item['updatetime'] = trim($item['updatetime']);
            }
            if ($item['school_branch']) {
                $school_id = $this->DataControl->getFieldOne("smc_school", "school_id", "school_branch = '{$item['school_branch']}'");
                $item['school_id'] = $school_id['school_id'];
            }
            if (!$item['school_id'] && $item['school_cnname']) {
                $school_id = $this->DataControl->getFieldOne("smc_school", "school_id", "school_cnname = '{$item['school_cnname']}'");
                $item['school_id'] = $school_id['school_id'];
            }
            if (!$school_id) {
                $item['school_id'] = 0;
            }

//            if ($item['client_istoschool'] == '否') {//渠道重新激活是否进校区  1 进校区  -1 不进校区
//                $item['istoschool'] = '否';
//            }else{
//                $item['istoschool'] = '是';
//            }
        }

        $fieldstring = array('school_cnname', 'school_branch', 'student_cnname','client_patriarchname','client_email','client_address','age','sex','updatetime','mobile','coursercatstr','channel_name','client_frompage','client_gmcmarket','remark','istoschool');
        $fieldname = array('校区名称', '校区编号','学员姓名','家长姓名', 'Email', '地址', '年龄', '性别', '日期', '联系方式','意向班种代码','渠道名称','接触点','电销姓名','客户备注','渠道重新激活是否进校区');
        $fieldcustom = array("1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1");


        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['list'] = $excelList;

        $res = array('error' => '0', 'errortip' => '导入学员成功', 'result' => $result);
        ajax_return($res,$request['language_type']);

    }

    //解析
    function NextStepChannelView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $marOne = $this->DataControl->selectOne("select marketer_id,staffer_id,marketer_name from  crm_marketer where staffer_id='{$request['staffer_id']}' and company_id='{$request['company_id']}' ");
        $staffer = $this->DataControl->selectOne("select * from smc_staffer WHERE staffer_id = '{$request['staffer_id']}'");
        if (!$marOne) {
            $datas = array();
            $datas['company_id'] = $staffer['company_id'];
            $datas['staffer_id'] = $staffer['staffer_id'];
            $datas['postrole_id'] = $staffer['postrole_id'];
            $datas['marketer_istest'] = $staffer['staffer_istest'];
            $datas['marketer_name'] = $staffer['staffer_cnname'];
            $datas['marketer_img'] = $staffer['staffer_img'];
            $datas['marketer_mobile'] = $staffer['staffer_mobile'];
            $datas['marketer_tokencode'] = $staffer['staffer_tokencode'];
            $datas['marketer_tokenencrypt'] = $staffer['staffer_tokenencrypt'];
            $datas['marketer_lasttime'] = $staffer['staffer_lasttime'];
            $datas['marketer_lastip'] = $staffer['staffer_lastip'];
            $datas['marketer_createtime'] = $staffer['staffer_createtime'];
            $datas['marketer_status'] = '1';
            $mark_id = $this->DataControl->insertData('crm_marketer', $datas);
            $marOne['marketer_id'] = $mark_id;
        }


        $List = json_decode(stripslashes($request['list']), true);
        $suc = 0;
        $fal = 0;
        $falarray = array();
        $falarrayisstu = array();

        do {
            $couponspid_get = $this->createOrderPid('CW');
        } while ($this->DataControl->getFieldOne("crm_client_fal", "fal_id", "fal_pid='{$couponspid_get}'"));

        $TmkBatchId = '';
        if(count($List) >= 1){
            do {
                $TmkBatchId = getTmkBatchId();
            } while ($this->DataControl->selectOne("select tmkbatch_id from crm_client_tmkbatch where tmkbatch_number='{$TmkBatchId}' and company_id='{$request['company_id']}' limit 0,1"));
        }
        foreach ($List as $item) {
            $item['mobile'] = trim($item['mobile']);
            $channelOne = $this->DataControl->getFieldOne("crm_code_channel", "channel_id,channel_medianame,channel_quality,channel_intention_level"
                , "channel_name = '{$item['channel_name']}' and company_id = '{$request['company_id']}'");
            if (!$channelOne) {
                $item['reason'] = '系统未检测名单归属渠道，无法导入名单，请检查渠道明细是否存在！';
                $falarray[] = $item;
                $datas = array();
                $datas['school_cnname'] = $item['school_cnname'];
                $datas['school_branch'] = $item['school_branch'];
                $datas['updatetime'] = $item['updatetime'];
                $datas['student_cnname'] = $item['student_cnname'];
                $datas['mobile'] = $item['mobile'];
                $datas['age'] = $item['age'];
                $datas['channel_cnname'] = $item['channel_name'];
                $datas['client_gmcmarket'] = $item['client_gmcmarket'];
                $datas['remark'] = $item['remark'];
                $datas['reason'] = $item['reason'];
                $datas['fal_pid'] = $couponspid_get;
                $datas['fal_createtime'] = time();
                $fal++;
                $this->DataControl->insertData('crm_client_fal', $datas);
                continue;
            }

            if (!checkMobile($item['mobile'])) {
                $item['reason'] = '手机号码校验错误，无法导入名单，请检查手机号码是否正确！';
                $falarray[] = $item;
                $datas = array();
                $datas['school_cnname'] = $item['school_cnname'];
                $datas['school_branch'] = $item['school_branch'];
                $datas['updatetime'] = $item['updatetime'];
                $datas['student_cnname'] = $item['student_cnname'];
                $datas['mobile'] = $item['mobile'];
                $datas['age'] = $item['age'];
                $datas['channel_cnname'] = $item['channel_name'];
                $datas['client_gmcmarket'] = $item['client_gmcmarket'];
                $datas['remark'] = $item['remark'];
                $datas['reason'] = $item['reason'];
                $datas['fal_pid'] = $couponspid_get;
                $datas['fal_createtime'] = time();
                $fal++;
                $this->DataControl->insertData('crm_client_fal', $datas);
                continue;
            }
            if ($item['school_branch']) {
                $school = $this->DataControl->getFieldOne("smc_school", "school_id", "school_branch = '{$item['school_branch']}' and company_id = '{$request['company_id']}'");
                if (!$school) {
                    $item['reason'] = '学校不存在！';
                    $falarray[] = $item;
                    $datas = array();
                    $datas['school_cnname'] = $item['school_cnname'];
                    $datas['school_branch'] = $item['school_branch'];
                    $datas['updatetime'] = $item['updatetime'];
                    $datas['student_cnname'] = $item['student_cnname'];
                    $datas['mobile'] = $item['mobile'];
                    $datas['age'] = $item['age'];
                    $datas['client_gmcmarket'] = $item['client_gmcmarket'];
                    $datas['channel_cnname'] = $item['channel_name'];
                    $datas['remark'] = $item['remark'];
                    $datas['reason'] = $item['reason'];
                    $datas['fal_pid'] = $couponspid_get;
                    $datas['fal_createtime'] = time();
                    $fal++;
                    $this->DataControl->insertData('crm_client_fal', $datas);
                    continue;
                }
            }else{
                $school = array();
            }

            $client_cnname = $item['student_cnname']?$item['student_cnname']:$item['client_cnname'];
            if ($client_cnname == '' || $item['mobile'] == '') {
                $item['reason'] = '学员姓名/手机不能为空';
                $falarray[] = $item;
                $datas = array();
                $datas['school_cnname'] = $item['school_cnname'];
                $datas['school_branch'] = $item['school_branch'];
                $datas['updatetime'] = $item['updatetime'];
                $datas['student_cnname'] = $item['student_cnname'];
                $datas['mobile'] = $item['mobile'];
                $datas['age'] = $item['age'];
                $datas['client_gmcmarket'] = $item['client_gmcmarket'];
                $datas['channel_cnname'] = $item['channel_name'];
                $datas['remark'] = $item['remark'];
                $datas['reason'] = $item['reason'];
                $datas['fal_pid'] = $couponspid_get;
                $datas['fal_createtime'] = time();
                $fal++;
                $this->DataControl->insertData('crm_client_fal', $datas);
                continue;
            }

            $paramOne = array();
            $paramOne['client_cnname'] = $item['student_cnname']?$item['student_cnname']:$item['client_cnname'];
            $paramOne['company_id'] = $request['company_id'];
            $paramOne['channel_medianame'] = $channelOne['channel_medianame'];
            $paramOne['client_source'] = $channelOne['channel_medianame'];
            $paramOne['channel_id'] = $channelOne['channel_id'];
            $paramOne['client_mobile'] = $item['mobile'];
            $paramOne['school_id'] = $school['school_id'];
            $paramOne['marketer_id'] = $marOne['marketer_id'];
            $paramOne['client_age'] = $item['age'];

            $paramOne['client_patriarchname'] = $item['client_patriarchname'];
            $paramOne['client_email'] = $item['client_email'];
            $paramOne['client_address'] = $item['client_address'];
            $paramOne['client_frompage'] = $item['client_frompage'];

            if($item['istoschool'] == '否'){
                $item['istoschool'] = '-1';
            }elseif($item['istoschool'] == '是'){
                $item['istoschool'] = '1';
            }
            $paramOne['istoschool'] = $item['istoschool'];//渠道重新激活是否进校区  1 进校区  -1 不进校区

            $track_note = "名单通过集团招生导入名单页面导入，";

            $impotParam = array();
            $impotParam['company_id'] = $request['company_id'];
            if(isset($marOne['marketer_id'])){
                $impotParam['marketer_id'] = $marOne['marketer_id'];
            }
            if(isset($school['school_id'])){
                $impotParam['school_id'] = $school['school_id'];
            }

            $Model = new \Model\Crm\ClientCreatedModel($impotParam);
            if (!$Model->CrmClientVerify($paramOne, $track_note,1)) {
                if($Model->result['isstudent'] == '1') {
                    $item['fal_type'] = 1;
                    $item['student_id'] = $Model->result['student_id'];
                }

                $item['reason'] = $Model->errortip;

                //集团存在这个手机号 并且状态 >= 0
                $isSchoolExistence = $this->DataControl->getFieldOne("crm_client", "client_id,client_source,client_updatetime", "client_mobile = '{$item['mobile']}' and company_id = '{$request['company_id']}' and client_tracestatus >= '0'");
                //名单存在 CRM学校 过
                $schoolOne = $this->DataControl->selectOne("select s.school_cnname from crm_client_schoolenter as sc left join smc_school as s on s.school_id = sc.school_id WHERE client_id = '{$isSchoolExistence['client_id']}' order by is_enterstatus desc,schoolenter_updatetime desc limit 0,1 ");

                $datas = array();
                $datas['school_cnname'] = $item['school_cnname'];
                $datas['school_branch'] = $item['school_branch'];
                $datas['updatetime'] = $item['updatetime'];
                $datas['student_cnname'] = $item['student_cnname'];
                $datas['mobile'] = $item['mobile'];
                $datas['age'] = $item['age'];
                $datas['client_gmcmarket'] = $item['client_gmcmarket'];
                $datas['channel_cnname'] = $item['channel_name'];
                $datas['school_Rename'] = $schoolOne['school_cnname'];
                $datas['remark'] = $item['remark'];
                $datas['reason'] = $item['reason'];
                $datas['fal_pid'] = $couponspid_get;
                $datas['fal_createtime'] = time();
                $fal++;
                $isstufalid = $this->DataControl->insertData('crm_client_fal', $datas);
                if($Model->result['isstudent'] == '1'){
                    $item['fal_id'] = $isstufalid;
                    $falarray[] = $item;
                    $falarrayisstu[] = $item;

                    //导入名单 已在学校，更新意向课程，添加记录
                    $track_note = "系统检测到在校生家长信息；";
                    $Model = new \Model\Crm\ClientModel($request);
                    $Model->falClientAddIntention($marOne['marketer_id'],$Model->result['student_id'],$request['company_id'],$Model->result['school_id'],$item['coursercatstr'],1,$track_note,2);
                }else{
                    $falarray[] = $item;

                    //导入名单 已在学校，更新意向课程，添加记录
                    $item['remark'] = ($item['remark'] == '' ? '--' : $item['remark']);
                    $track_note = "集团招生管理中心导入名单，系统检测到重复记录，记录本次导入信息，导入备注：{$item['remark']};";
                    $Model = new \Model\Crm\ClientModel($request);
                    $Model->falClientAddIntention($marOne['marketer_id'], $Model->result['client_id'], $request['company_id'], $Model->result['school_id'], $item['coursercatstr'], 0, $track_note, 2);
                }
                continue;;
            }
            // 引用的撞单逻辑不部分   结束  ----------------------------------

            $data = array();
            $data['client_cnname'] = $item['student_cnname']?$item['student_cnname']:$item['client_cnname'];
            $data['marketer_id'] = $marOne['marketer_id'];
            $data['client_sex'] = $item['sex'];
            $data['client_updatetime'] = strtotime($item['updatetime']);
            $data['client_age'] = $item['age'];
            $data['company_id'] = $request['company_id'];
            $data['outthree_userid'] = $item['outthree_userid'];

            if($item['istoschool'] == '否'){
                $item['istoschool'] = '-1';
            }elseif($item['istoschool'] == '是'){
                $item['istoschool'] = '1';
            }

            $school = $this->DataControl->getFieldOne("smc_school", "school_id", "school_branch = '{$item['school_branch']}' and company_id = '{$request['company_id']}'");
            if($school){
                $familyOne = $this->DataControl->selectOne("SELECT f.family_id,s.student_id,s.student_cnname,s.student_branch 
                            FROM smc_student_family AS f ,smc_student AS s,smc_student_enrolled as e 
                            WHERE f.student_id = s.student_id AND f.family_mobile = '{$item['mobile']}' and s.company_id = '{$request['company_id']}' 
                                and s.student_id=e.student_id and e.school_id = '{$school['school_id']}' and e.enrolled_status > '-1' 
                            ORDER BY s.student_branch DESC limit 0,1");
            }else{
                $familyOne = $this->DataControl->selectOne("SELECT f.family_id,s.student_id,s.student_cnname,s.student_branch FROM smc_student_family AS f ,smc_student AS s
 WHERE f.student_id = s.student_id AND f.family_mobile = '{$item['mobile']}' and s.company_id = '{$request['company_id']}' ORDER BY s.student_branch DESC limit 0,1");
            }
            if ($familyOne) {
                continue;
            }


            $data['client_patriarchname'] = $item['client_patriarchname'];
            $data['client_email'] = $item['client_email'];
            $data['client_address'] = $item['client_address'];
            $data['client_frompage'] = $item['client_frompage'];

            $data['channel_id'] = $channelOne['channel_id'];
            $data['client_source'] = $channelOne['channel_medianame'];
            //加渠道有效判断
            if($channelOne['channel_quality'] == '1'){
                $data['client_isgross'] = 0;//不是毛名单 -- 即 有效
            }elseif($channelOne['channel_quality'] == '0' && $channelOne['channel_quality'] != ''){
                $data['client_isgross'] = 1;//是毛名单
            }else{
                $data['client_isgross'] = 0;//不是毛名单 -- 即 有效
            }

            $data['client_remark'] = $item['remark'];
            $data['client_gmcmarket'] = $item['client_gmcmarket'];
            $data['client_mobile'] = $item['mobile'];
            $data['client_updatetime'] = time();
            $data['client_createtime'] = time();

            if($channelOne['channel_intention_level'] > 0){
                $data['client_intention_level'] = $channelOne['channel_intention_level'];
                $data['client_intention_maxlevel'] = $channelOne['channel_intention_level'];
            }

            $data['client_isfromgmc'] = 1;
            $data['client_isnewtip'] = '1';
            $data['marketer_id'] = $marOne['marketer_id'];
            $data['client_tmkbatch'] = $TmkBatchId;//增加批次编号
            $id = $this->DataControl->insertData("crm_client", $data);
            //新加
            $addcoursecat = '';
            if ($item['coursercatstr'] != ''){
                $coursercatarr = explode(",",$item['coursercatstr']);
                if($coursercatarr){
                    foreach ($coursercatarr as $coursercatarrvar){
                        //意向课程
                        $coursercatOne = $this->DataControl->selectOne("select * from smc_code_coursecat where coursecat_branch = '{$coursercatarrvar}' and company_id = '{$request['company_id']}' limit 0,1");
                        if(!$this->DataControl->getFieldOne("crm_client_intention","intention_id","client_id = '{$id}' AND coursecat_id = '{$coursercatOne['coursecat_id']}'")){
                            $intention = array();
                            $intention['client_id'] = $id;
                            $intention['coursetype_id'] = $coursercatOne['coursetype_id'];
                            $intention['coursecat_id'] = $coursercatOne['coursecat_id'];
                            $intention['intention_updatetime'] = time();
                            $this->DataControl->insertData("crm_client_intention", $intention);
                            $addcoursecat .= $coursercatarrvar.",";
                        }
                    }
                }
            }

            if ($id) {
                if($item['istoschool'] != '-1') {//渠道重新激活是否进校区  1 进校区  -1 不进校区
                    if ($item['school_id']) {
                        $datas = array();
                        $datas['client_id'] = $id;
                        $datas['school_id'] = $item['school_id'];
                        $datas['company_id'] = $request['company_id'];
                        $datas['schoolenter_createtime'] = time();
                        $datas['schoolenter_updatetime'] = time();
                        $this->DataControl->insertData('crm_client_schoolenter', $datas);

                        //添加名单状态记录
                        $Model = new  \Model\Api\CalloutModel($paramOne);
                        $Model->addClientTimerecord($request['company_id'],$item['school_id'],$id,1,$marOne['marketer_id'],"集团TMK导入名单");
                    }
                }else{
                    //添加名单状态记录
                    $Model = new  \Model\Api\CalloutModel($paramOne);
                    $Model->addClientTimerecord($request['company_id'],0,$id,1,$marOne['marketer_id'],"集团TMK导入名单");
                }

                $trackData = array();
                $trackData['client_id'] = $id;
                $trackData['marketer_id'] = $marOne['marketer_id'];
                $trackData['marketer_name'] = $staffer['staffer_cnname'];
                $trackData['track_validinc'] = 1;
                $trackData['track_linktype'] = '名单导入';
                $trackData['track_note'] = "由集团管理中心导入名单,新建客户信息";
                if($addcoursecat != ''){
                    $addcoursecat = substr($addcoursecat, 0, -1);
                    $trackData['track_note'] .= ",同时新增({$addcoursecat})意向班种";
                }
                $trackData['track_createtime'] = time();
                $trackData['track_type'] = 1;
                $trackData['track_initiative'] = 1;
                $this->DataControl->insertData('crm_client_track', $trackData);

                $suc++;
            } else {
                $item['reason'] = '数据格式有误';
                $falarray[] = $item;
                $datas = array();
                $datas['school_cnname'] = $item['school_cnname'];
                $datas['school_branch'] = $item['school_branch'];
                $datas['updatetime'] = $item['updatetime'];
                $datas['student_cnname'] = $item['student_cnname'];
                $datas['mobile'] = $item['mobile'];
                $datas['age'] = $item['age'];
                $datas['client_gmcmarket'] = $item['client_gmcmarket'];
                $datas['channel_cnname'] = $item['channel_name'];
                $datas['remark'] = $item['remark'];
                $datas['reason'] = $item['reason'];
                $datas['fal_pid'] = $couponspid_get;
                $datas['fal_createtime'] = time();
                $fal++;
                $this->DataControl->insertData('crm_client_fal', $datas);
            }

        }

        $fieldstring = array('school_cnname', 'school_branch','student_cnname','age','sex','reason','updatetime','mobile','channel_name','client_gmcmarket', 'remark');
        $fieldname = array('校区名称', '校区编号','学员姓名','年龄','性别','失败原因', '日期','联系方式','渠道名称', '电销姓名', '客户备注');
        $fieldcustom = array("1", "1", "1", "1", "1", "1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1", "1", "1", "1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        if($suc>0){
            $this->DataControl->insertData('crm_client_tmkbatch',  ["company_id"=>"{$request['company_id']}","tmkbatch_number"=>"{$TmkBatchId}"]);
        }
        $result = array();
        $result['fal'] = $fal;
        $result['suc'] = $suc;
        $result['field'] = $field;
        $result['falarray'] = $falarray;
        $result['falarrayisstu'] = $falarrayisstu;
        $result['time'] = $couponspid_get;

        $this->addGmcWorkLog($request['company_id'], $request['staffer_id'], "招生管理", '导入渠道招生名单', dataEncode($request));

        ajax_return(array('error' => '0', 'errortip' => "导入名单成功", 'result' => $result));

    }
    //解析 -- 之前的代码 -- 改撞单逻辑前 20230721
    function NextStepChannelOldView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $marOne = $this->DataControl->selectOne("select marketer_id,staffer_id,marketer_name from  crm_marketer where staffer_id='{$request['staffer_id']}' and company_id='{$request['company_id']}' ");
        $staffer = $this->DataControl->selectOne("select * from smc_staffer WHERE staffer_id = '{$request['staffer_id']}'");
        if (!$marOne) {
            $datas = array();
            $datas['company_id'] = $staffer['company_id'];
            $datas['staffer_id'] = $staffer['staffer_id'];
            $datas['postrole_id'] = $staffer['postrole_id'];
            $datas['marketer_istest'] = $staffer['staffer_istest'];
            $datas['marketer_name'] = $staffer['staffer_cnname'];
            $datas['marketer_img'] = $staffer['staffer_img'];
            $datas['marketer_mobile'] = $staffer['staffer_mobile'];
            $datas['marketer_tokencode'] = $staffer['staffer_tokencode'];
            $datas['marketer_tokenencrypt'] = $staffer['staffer_tokenencrypt'];
            $datas['marketer_lasttime'] = $staffer['staffer_lasttime'];
            $datas['marketer_lastip'] = $staffer['staffer_lastip'];
            $datas['marketer_createtime'] = $staffer['staffer_createtime'];
            $datas['marketer_status'] = '1';
            $mark_id = $this->DataControl->insertData('crm_marketer', $datas);
            $marOne['marketer_id'] = $mark_id;
        }


        $List = json_decode(stripslashes($request['list']), true);
        $suc = 0;
        $fal = 0;
        $falarray = array();
        $falarrayisstu = array();

        do {
            $couponspid_get = $this->createOrderPid('CW');
        } while ($this->DataControl->getFieldOne("crm_client_fal", "fal_id", "fal_pid='{$couponspid_get}'"));

        $TmkBatchId = '';
        if(count($List) >= 1){
            do {
                $TmkBatchId = getTmkBatchId();
            } while ($this->DataControl->selectOne("select tmkbatch_id from crm_client_tmkbatch where tmkbatch_number='{$TmkBatchId}' and company_id='{$request['company_id']}' limit 0,1"));
        }
        foreach ($List as $item) {

            // 引用的撞单逻辑不部分   开始  ----------------------------------
//            $channelOne = $this->DataControl->getFieldOne("crm_code_channel", "channel_id,channel_medianame,channel_quality"
//                , "channel_name = '{$item['channel_name']}' and company_id = '{$request['company_id']}'");
//
//            $school = $this->DataControl->getFieldOne("smc_school", "school_id", "school_branch = '{$item['school_branch']}' and company_id = '{$request['company_id']}'");
//
//            $paramOne = array();
//            $paramOne['client_cnname'] = $item['client_cnname'];
//            $paramOne['company_id'] = $request['company_id'];
//            $paramOne['channel_medianame'] = $channelOne['channel_medianame'];
//            $paramOne['channel_id'] = $channelOne['channel_id'];
//            $paramOne['client_mobile'] = $item['mobile'];
//            $paramOne['school_id'] = $school['school_id'];
//            $paramOne['marketer_id'] = $marOne['marketer_id'];
//
//            $track_note = "名单通过集团招生导入名单页面导入，";
//            $Model = new \Model\Crm\ClientModel($request);
//            if (!$Model->CrmClientVerify($paramOne,array(),$track_note)) {
//                $item['reason'] = $Model->errortip;
//                $falarray[] = $item;
//                $datas = array();
//                $datas['school_cnname'] = $item['school_cnname'];
//                $datas['school_branch'] = $item['school_branch'];
//                $datas['updatetime'] = $item['updatetime'];
//                $datas['student_cnname'] = $item['student_cnname'];
//                $datas['mobile'] = $item['mobile'];
//                $datas['age'] = $item['age'];
//                $datas['client_gmcmarket'] = $item['client_gmcmarket'];
//                $datas['channel_cnname'] = $item['channel_name'];
//                $datas['remark'] = $item['remark'];
//                $datas['reason'] = $item['reason'];
//                $datas['fal_pid'] = $couponspid_get;
//                $datas['fal_createtime'] = time();
//                $fal++;
//                $this->DataControl->insertData('crm_client_fal', $datas);
//                return false;
//            }
            // 引用的撞单逻辑不部分   结束  ----------------------------------

            $data = array();
            $data['client_cnname'] = $item['student_cnname'];
            $data['marketer_id'] = $marOne['marketer_id'];
            $data['client_sex'] = $item['sex'];
            $data['client_updatetime'] = strtotime($item['updatetime']);
            $data['client_age'] = $item['age'];
            $data['company_id'] = $request['company_id'];
            $data['outthree_userid'] = $item['outthree_userid'];

            if($item['istoschool'] == '否'){
                $item['istoschool'] = '-1';
            }elseif($item['istoschool'] == '是'){
                $item['istoschool'] = '1';
            }

            $school = $this->DataControl->getFieldOne("smc_school", "school_id", "school_branch = '{$item['school_branch']}' and company_id = '{$request['company_id']}'");
            if($school){
                $familyOne = $this->DataControl->selectOne("SELECT f.family_id,s.student_id,s.student_cnname,s.student_branch 
                            FROM smc_student_family AS f ,smc_student AS s,smc_student_enrolled as e 
                            WHERE f.student_id = s.student_id AND f.family_mobile = '{$item['mobile']}' and s.company_id = '{$request['company_id']}' 
                                and s.student_id=e.student_id and e.school_id = '{$school['school_id']}' and e.enrolled_status > '-1' 
                            ORDER BY s.student_branch DESC limit 0,1");
            }else{
                $familyOne = $this->DataControl->selectOne("SELECT f.family_id,s.student_id,s.student_cnname,s.student_branch FROM smc_student_family AS f ,smc_student AS s
 WHERE f.student_id = s.student_id AND f.family_mobile = '{$item['mobile']}' and s.company_id = '{$request['company_id']}' ORDER BY s.student_branch DESC limit 0,1");
            }
            if ($familyOne) {
                $item['fal_type'] = 1;
                $item['student_id'] = $familyOne['student_id'];
                $item['reason'] = "系统检测到在校生家长信息,学员姓名：{$familyOne['student_cnname']}，编号：{$familyOne['student_branch']}，无法导入名单，请确认此信息的准确性！";
                $datas = array();
                $datas['school_cnname'] = $item['school_cnname'];
                $datas['school_branch'] = $item['school_branch'];
                $datas['updatetime'] = $item['updatetime'];
                $datas['student_cnname'] = $item['student_cnname'];
                $datas['mobile'] = $item['mobile'];
                $datas['age'] = $item['age'];
                $datas['channel_cnname'] = $item['channel_name'];
                $datas['client_gmcmarket'] = $item['client_gmcmarket'];
                $datas['remark'] = $item['remark'];
                $datas['reason'] = $item['reason'];
                $datas['fal_pid'] = $couponspid_get;
                $datas['fal_type'] = 1;
                $datas['fal_createtime'] = time();
                $fal++;
                $isstufalid = $this->DataControl->insertData('crm_client_fal', $datas);
                $item['fal_id'] = $isstufalid;
                $falarray[] = $item;
                $falarrayisstu[] = $item;

                //导入名单 已在学校，更新意向课程，添加记录
                $track_note = "系统检测到在校生家长信息；";
                $Model = new \Model\Crm\ClientModel($request);
                $Model->falClientAddIntention($marOne['marketer_id'],$familyOne['student_id'],$request['company_id'],0,$item['coursercatstr'],1,$track_note,2);

                continue;
            }

            $channelOne = $this->DataControl->getFieldOne("crm_code_channel", "channel_id,channel_medianame,channel_quality"
                , "channel_name = '{$item['channel_name']}' and company_id = '{$request['company_id']}'");
            if (!$channelOne) {
                $item['reason'] = '系统未检测名单归属渠道，无法导入名单，请检查渠道明细是否存在！';
                $falarray[] = $item;
                $datas = array();
                $datas['school_cnname'] = $item['school_cnname'];
                $datas['school_branch'] = $item['school_branch'];
                $datas['updatetime'] = $item['updatetime'];
                $datas['student_cnname'] = $item['student_cnname'];
                $datas['mobile'] = $item['mobile'];
                $datas['age'] = $item['age'];
                $datas['channel_cnname'] = $item['channel_name'];
                $datas['client_gmcmarket'] = $item['client_gmcmarket'];
                $datas['remark'] = $item['remark'];
                $datas['reason'] = $item['reason'];
                $datas['fal_pid'] = $couponspid_get;
                $datas['fal_createtime'] = time();
                $fal++;
                $this->DataControl->insertData('crm_client_fal', $datas);
                continue;
            }

            if (!checkMobile($item['mobile'])) {
                $item['reason'] = '手机号码校验错误，无法导入名单，请检查手机号码是否正确！';
                $falarray[] = $item;
                $datas = array();
                $datas['school_cnname'] = $item['school_cnname'];
                $datas['school_branch'] = $item['school_branch'];
                $datas['updatetime'] = $item['updatetime'];
                $datas['student_cnname'] = $item['student_cnname'];
                $datas['mobile'] = $item['mobile'];
                $datas['age'] = $item['age'];
                $datas['channel_cnname'] = $item['channel_name'];
                $datas['client_gmcmarket'] = $item['client_gmcmarket'];
                $datas['remark'] = $item['remark'];
                $datas['reason'] = $item['reason'];
                $datas['fal_pid'] = $couponspid_get;
                $datas['fal_createtime'] = time();
                $fal++;
                $this->DataControl->insertData('crm_client_fal', $datas);
                continue;
            }

            if ($item['school_branch']) {
                $school = $this->DataControl->getFieldOne("smc_school", "school_id", "school_branch = '{$item['school_branch']}' and company_id = '{$request['company_id']}'");
                if (!$school) {
                    $item['reason'] = '学校不存在！';
                    $falarray[] = $item;
                    $datas = array();
                    $datas['school_cnname'] = $item['school_cnname'];
                    $datas['school_branch'] = $item['school_branch'];
                    $datas['updatetime'] = $item['updatetime'];
                    $datas['student_cnname'] = $item['student_cnname'];
                    $datas['mobile'] = $item['mobile'];
                    $datas['age'] = $item['age'];
                    $datas['client_gmcmarket'] = $item['client_gmcmarket'];
                    $datas['channel_cnname'] = $item['channel_name'];
                    $datas['remark'] = $item['remark'];
                    $datas['reason'] = $item['reason'];
                    $datas['fal_pid'] = $couponspid_get;
                    $datas['fal_createtime'] = time();
                    $fal++;
                    $this->DataControl->insertData('crm_client_fal', $datas);
                    continue;

                }

            }

            $data['channel_id'] = $channelOne['channel_id'];
            $data['client_source'] = $channelOne['channel_medianame'];
            //加渠道有效判断
            if($channelOne['channel_quality'] == '1'){
                $data['client_isgross'] = 0;//不是毛名单 -- 即 有效
            }elseif($channelOne['channel_quality'] == '0' && $channelOne['channel_quality'] != ''){
                $data['client_isgross'] = 1;//是毛名单
            }else{
                $data['client_isgross'] = 0;//不是毛名单 -- 即 有效
            }

            $data['client_remark'] = $item['remark'];
            $data['client_gmcmarket'] = $item['client_gmcmarket'];
            $data['client_mobile'] = $item['mobile'];
            $data['client_updatetime'] = time();
            $data['client_createtime'] = time();

            //集团存在这个手机号 并且状态 >= 0
            $isSchoolExistence = $this->DataControl->getFieldOne("crm_client", "client_id,client_source,client_updatetime", "client_mobile = '{$item['mobile']}' and company_id = '{$request['company_id']}' and client_tracestatus >= '0'");
            //名单存在 CRM学校 过
            $schoolOne = $this->DataControl->selectOne("select s.school_cnname from crm_client_schoolenter as sc left join smc_school as s on s.school_id = sc.school_id WHERE client_id = '{$isSchoolExistence['client_id']}'");
            //集团 当前学校存在这个手机号  并且状态 < 0
            $isSchollnews = $this->DataControl->selectOne("select c.client_id from crm_client as c left join crm_client_schoolenter as s on c.client_id = s.client_id 
where c.client_mobile = '{$item['mobile']}' and c.company_id = '{$request['company_id']}' and c.client_tracestatus < '0' and s.school_id = '{$item['school_id']}'");
            //集团 不是当前学校存在这个手机号  并且状态 < 0
            $isSchollupdata = $this->DataControl->selectOne("select c.client_id,s.school_id from crm_client as c left join crm_client_schoolenter as s on c.client_id = s.client_id 
where c.client_mobile = '{$item['mobile']}' and c.company_id = '{$request['company_id']}' and c.client_tracestatus < '0' and s.school_id <> '{$item['school_id']}'");

            //发现一个漏洞  20230308   集团存在这个手机号 并且状态 < 0
            $isNotScholl = $this->DataControl->selectOne("select c.client_id from crm_client as c where c.client_mobile = '{$item['mobile']}' and c.company_id = '{$request['company_id']}' and c.client_tracestatus < '0' ");

            if ($data['client_cnname'] == '' || $data['client_mobile'] == '') {
                $item['reason'] = '学员姓名/手机不能为空';
                $falarray[] = $item;
                $datas = array();
                $datas['school_cnname'] = $item['school_cnname'];
                $datas['school_branch'] = $item['school_branch'];
                $datas['updatetime'] = $item['updatetime'];
                $datas['student_cnname'] = $item['student_cnname'];
                $datas['mobile'] = $item['mobile'];
                $datas['age'] = $item['age'];
                $datas['client_gmcmarket'] = $item['client_gmcmarket'];
                $datas['channel_cnname'] = $item['channel_name'];
                $datas['remark'] = $item['remark'];
                $datas['reason'] = $item['reason'];
                $datas['fal_pid'] = $couponspid_get;
                $datas['fal_createtime'] = time();
                $fal++;
                $this->DataControl->insertData('crm_client_fal', $datas);
            } elseif ($isSchoolExistence) {
                $updatetime = date("Y-m-d", $isSchoolExistence['client_updatetime']);
                $item['reason'] = "系统已存在此意向客户，客户渠道来源{$isSchoolExistence['client_source']},更新时间为：{$updatetime}！";
                $falarray[] = $item;
                $datas = array();
                $datas['school_cnname'] = $item['school_cnname'];
                $datas['school_branch'] = $item['school_branch'];
                $datas['updatetime'] = $item['updatetime'];
                $datas['student_cnname'] = $item['student_cnname'];
                $datas['mobile'] = $item['mobile'];
                $datas['age'] = $item['age'];
                $datas['client_gmcmarket'] = $item['client_gmcmarket'];
                $datas['remark'] = $item['remark'];
                $datas['reason'] = $item['reason'];
                $datas['school_Rename'] = $schoolOne['school_cnname'];
                $datas['channel_cnname'] = $item['channel_name'];
                $datas['fal_pid'] = $couponspid_get;
                $datas['fal_createtime'] = time();
                $fal++;
                $this->DataControl->insertData('crm_client_fal', $datas);

                if($item['school_branch'] && !$this->DataControl->selectOne("select c.client_id from crm_client_schoolenter as c WHERE c.client_id = '{$isSchoolExistence['client_id']}' and c.company_id = '{$request['company_id']}'")){
                    $datas = array();
                    $datas['client_id'] = $isSchoolExistence['client_id'];
                    $datas['school_id'] = $item['school_id'];
                    $datas['company_id'] = $request['company_id'];
                    $datas['schoolenter_createtime'] = time();
                    $datas['schoolenter_updatetime'] = time();
                    $this->DataControl->insertData('crm_client_schoolenter', $datas);
                }

                if ($item['coursercatstr'] == '') {
                    $trackData = array();
                    $trackData['client_id'] = $isSchoolExistence['client_id'];
                    $trackData['marketer_id'] = $marOne['marketer_id'];
                    $trackData['marketer_name'] = $staffer['staffer_cnname'];
                    $trackData['track_validinc'] = 1;
                    $trackData['track_followmode'] = 4;
                    $trackData['track_linktype'] = '名单导入';
                    $trackData['track_note'] = "集团招生管理中心导入名单，系统检测到重复记录，记录本次导入信息，导入备注：{$item['remark']}";
                    $trackData['track_createtime'] = time();
                    $trackData['track_type'] = 1;
                    $trackData['track_initiative'] = 1;
                    $this->DataControl->insertData('crm_client_track', $trackData);
                }else {
                    //导入名单 已在学校，更新意向课程，添加记录
                    $item['remark'] = ($item['remark'] == '' ? '--' : $item['remark']);
                    $track_note = "集团招生管理中心导入名单，系统检测到重复记录，记录本次导入信息，导入备注：{$item['remark']};";
                    $Model = new \Model\Crm\ClientModel($request);
                    $Model->falClientAddIntention($marOne['marketer_id'], $isSchoolExistence['client_id'], $request['company_id'], 0, $item['coursercatstr'], 0, $track_note, 2);
                }
            } elseif ($isSchollnews) {
                $datass = array();
                $datass['client_tracestatus'] = '0';
                $datass['channel_id'] = $channelOne['channel_id'];
                $datass['client_source'] = $channelOne['channel_medianame'];
                //导入时渠渠道变更   名单状态小于 0   最高意向星级改为 0
                $datass['client_intention_maxlevel'] = 0;
                $datass['client_intention_level'] = 0;

                $datass['client_isnewtip'] = '1';
                $datass['client_isfromgmc'] = '1';
                $datass['client_tmkbatch'] = $TmkBatchId;//增加批次编号

                if($item['istoschool'] == '-1'){//渠道重新激活是否进校区  1 进校区  -1 不进校区
                    $datass['client_gmcdistributionstatus'] = 0;
                    $datass['client_schtmkdistributionstatus'] = 0;
                    $datass['client_ischaserlapsed'] = 0;
                }
                $this->DataControl->updateData("crm_client", "client_id = '{$isSchollnews['client_id']}'", $datass);

                if($item['istoschool'] == '-1'){//渠道重新激活是否进校区  1 进校区  -1 不进校区
                    $schoolenter = array();
                    $schoolenter['is_enterstatus'] = '-1';
                    $schoolenter['schoolenter_updatetime'] = time();
                    $this->DataControl->updateData("crm_client_schoolenter", "client_id='{$isSchollnews['client_id']}' ", $schoolenter);

                    $principal = array();
                    $principal['principal_leave'] = '1';
                    $principal['principal_updatatime'] = time();
                    $this->DataControl->updateData("crm_client_principal", "client_id='{$isSchollnews['client_id']}' ", $principal);
                }

                //新加
                $addcoursecat = '';
                if ($item['coursercatstr'] != ''){
                    $coursercatarr = explode(",",$item['coursercatstr']);
                    if($coursercatarr){
                        foreach ($coursercatarr as $coursercatarrvar){
                            //意向课程
                            $coursercatOne = $this->DataControl->selectOne("select * from smc_code_coursecat where coursecat_branch = '{$coursercatarrvar}' and company_id = '{$request['company_id']}' limit 0,1");
                            if(!$this->DataControl->getFieldOne("crm_client_intention","intention_id","client_id = '{$isSchollnews['client_id']}' AND coursecat_id = '{$coursercatOne['coursecat_id']}'")){
                                $intention = array();
                                $intention['client_id'] = $isSchollnews['client_id'];
                                $intention['coursetype_id'] = $coursercatOne['coursetype_id'];
                                $intention['coursecat_id'] = $coursercatOne['coursecat_id'];
                                $intention['intention_updatetime'] = time();
                                $this->DataControl->insertData("crm_client_intention", $intention);
                                $addcoursecat .= $coursercatarrvar.",";
                            }
                        }
                    }
                }

                $trackData = array();
                $trackData['client_id'] = $isSchollnews['client_id'];
                $trackData['marketer_id'] = $marOne['marketer_id'];
                $trackData['marketer_name'] = $staffer['staffer_cnname'];
                $trackData['track_validinc'] = 1;
                $trackData['track_followmode'] = 4;
                $trackData['track_linktype'] = '名单导入';
                if($item['istoschool'] == '-1') {//渠道重新激活是否进校区  1 进校区  -1 不进校区
                    $trackData['track_note'] = '由集团管理中心导入名单，无意向客户转为招生有效名单（渠道重新激活不进入校区，划到集团（同校））';
                }else {
                    $trackData['track_note'] = '由集团管理中心导入名单，无意向客户转为招生有效名单（同校）';
                }
                if($addcoursecat != ''){
                    $addcoursecat = substr($addcoursecat, 0, -1);
                    $trackData['track_note'] .= ",同时新增({$addcoursecat})意向班种";
                }
                $trackData['track_createtime'] = time();
                $trackData['track_type'] = 1;
                $trackData['track_initiative'] = 1;
                $this->DataControl->insertData('crm_client_track', $trackData);

                //已存在名单进行更新 显示为导入失败
                $updatetime = date("Y-m-d",time());
                $item['reason'] = "系统已存在此意向客户，之前名单被标记为无效或无意向，现在重置为待跟踪状态，部分信息进行更新，更新时间为：{$updatetime}！";
                $falarray[] = $item;
                $datas = array();
                $datas['school_cnname'] = $item['school_cnname'];
                $datas['school_branch'] = $item['school_branch'];
                $datas['updatetime'] = $item['updatetime'];
                $datas['student_cnname'] = $item['student_cnname'];
                $datas['mobile'] = $item['mobile'];
                $datas['age'] = $item['age'];
                $datas['client_gmcmarket'] = $item['client_gmcmarket'];
                $datas['remark'] = $item['remark'];
                $datas['reason'] = $item['reason'];
                $datas['school_Rename'] = $schoolOne['school_cnname'];
                $datas['channel_cnname'] = $item['channel_name'];
                $datas['fal_pid'] = $couponspid_get;
                $datas['fal_createtime'] = time();
                $fal++;
                $this->DataControl->insertData('crm_client_fal', $datas);
//                $suc++;
            } elseif ($isSchollupdata) {
                $datass = array();
                $datass['client_tracestatus'] = '0';
                $datass['channel_id'] = $channelOne['channel_id'];
                $datass['client_source'] = $channelOne['channel_medianame'];
                //导入时渠渠道变更   名单状态小于 0   最高意向星级改为 0
                $datass['client_intention_maxlevel'] = 0;
                $datass['client_intention_level'] = 0;

                $datass['client_isnewtip'] = '1';
                $datass['client_isfromgmc'] = '1';
                $datass['client_tmkbatch'] = $TmkBatchId;//增加批次编号

                if($item['istoschool'] == '-1'){//渠道重新激活是否进校区  1 进校区  -1 不进校区
                    $datass['client_gmcdistributionstatus'] = 0;
                    $datass['client_schtmkdistributionstatus'] = 0;
                    $datass['client_ischaserlapsed'] = 0;
                }
                $this->DataControl->updateData("crm_client", "client_id = '{$isSchollupdata['client_id']}'", $datass);


                if($item['istoschool'] == '-1'){//渠道重新激活是否进校区  1 进校区  -1 不进校区
                    $schoolenter = array();
                    $schoolenter['is_enterstatus'] = '-1';
                    $schoolenter['schoolenter_updatetime'] = time();
                    $this->DataControl->updateData("crm_client_schoolenter", "client_id='{$isSchollupdata['client_id']}' ", $schoolenter);

                    $principal = array();
                    $principal['principal_leave'] = '1';
                    $principal['principal_updatatime'] = time();
                    $this->DataControl->updateData("crm_client_principal", "client_id='{$isSchollupdata['client_id']}' ", $principal);
                }else {
                    if ($item['school_id']) {
                        $schoolenter_id = $this->DataControl->getFieldOne('crm_client_schoolenter', 'schoolenter_id', "client_id='{{$isSchollupdata['client_id']}' and school_id='{$item['school_id']}'");
                        if ($schoolenter_id) {
                            $schoolenter = array();
                            $schoolenter['is_enterstatus'] = '1';
                            $schoolenter['schoolenter_updatetime'] = time();
                            $this->DataControl->updateData("crm_client_schoolenter", "client_id='{$isSchollupdata['client_id']}' and school_id='{$item['school_id']}'", $schoolenter);
                        } else {
                            $data = array();
                            $data['client_id'] = $isSchollupdata['client_id'];
                            $data['school_id'] = $item['school_id'];
                            $data['company_id'] = $request['company_id'];
                            $data['schoolenter_createtime'] = time();
                            $data['schoolenter_updatetime'] = time();
                            $this->DataControl->insertData('crm_client_schoolenter', $data);
                        }
                        $schoolenter = array();
                        $schoolenter['is_enterstatus'] = '-1';
                        $schoolenter['schoolenter_updatetime'] = time();
                        $this->DataControl->updateData("crm_client_schoolenter", "client_id='{$isSchollupdata['client_id']}' and school_id <> '{$item['school_id']}'", $schoolenter);
                    }
                }
                /*else {
                    $this->DataControl->delData("crm_client_schoolenter", "client_id = '{$isSchollupdata['client_id']}'");
                }*/


                //新加
                $addcoursecat = '';
                if ($item['coursercatstr'] != ''){
                    $coursercatarr = explode(",",$item['coursercatstr']);
                    if($coursercatarr){
                        foreach ($coursercatarr as $coursercatarrvar){
                            //意向课程
                            $coursercatOne = $this->DataControl->selectOne("select * from smc_code_coursecat where coursecat_branch = '{$coursercatarrvar}' and company_id = '{$request['company_id']}' limit 0,1");
                            if(!$this->DataControl->getFieldOne("crm_client_intention","intention_id","client_id = '{$isSchollupdata['client_id']}' AND coursecat_id = '{$coursercatOne['coursecat_id']}'")){
                                $intention = array();
                                $intention['client_id'] = $isSchollupdata['client_id'];
                                $intention['coursetype_id'] = $coursercatOne['coursetype_id'];
                                $intention['coursecat_id'] = $coursercatOne['coursecat_id'];
                                $intention['intention_updatetime'] = time();
                                $this->DataControl->insertData("crm_client_intention", $intention);
                                $addcoursecat .= $coursercatarrvar.",";
                            }
                        }
                    }
                }

                $trackData = array();
                $trackData['client_id'] = $isSchollupdata['client_id'];
                $trackData['marketer_id'] = $marOne['marketer_id'];
                $trackData['marketer_name'] = $staffer['staffer_cnname'];
                $trackData['track_validinc'] = 1;
                $trackData['track_followmode'] = 5;
                $trackData['track_linktype'] = '名单导入';
                if($item['istoschool'] == '-1') {//渠道重新激活是否进校区  1 进校区  -1 不进校区
                    $trackData['track_note'] = '由集团管理中心导入名单，无意向客户转为招生有效名单（渠道重新激活不进入校区，划到集团（转校））';
                }else{
                    $trackData['track_note'] = '由集团管理中心导入名单，无意向客户转为招生有效名单（转校）';
                }
                if($addcoursecat != ''){
                    $addcoursecat = substr($addcoursecat, 0, -1);
                    $trackData['track_note'] .= ",同时新增({$addcoursecat})意向班种";
                }
                $trackData['track_createtime'] = time();
                $trackData['track_type'] = 1;
                $trackData['track_initiative'] = 1;
                $this->DataControl->insertData('crm_client_track', $trackData);

                //已存在名单进行更新 显示为导入失败
                $updatetime = date("Y-m-d",time());
                $item['reason'] = "系统已存在此意向客户，之前名单被标记为无效或无意向，现在重置为待跟踪状态，部分信息进行更新，更新时间为：{$updatetime}！";
                $falarray[] = $item;
                $datas = array();
                $datas['school_cnname'] = $item['school_cnname'];
                $datas['school_branch'] = $item['school_branch'];
                $datas['updatetime'] = $item['updatetime'];
                $datas['student_cnname'] = $item['student_cnname'];
                $datas['mobile'] = $item['mobile'];
                $datas['age'] = $item['age'];
                $datas['client_gmcmarket'] = $item['client_gmcmarket'];
                $datas['remark'] = $item['remark'];
                $datas['reason'] = $item['reason'];
                $datas['school_Rename'] = $schoolOne['school_cnname'];
                $datas['channel_cnname'] = $item['channel_name'];
                $datas['fal_pid'] = $couponspid_get;
                $datas['fal_createtime'] = time();
                $fal++;
                $this->DataControl->insertData('crm_client_fal', $datas);

//                $suc++;
            }elseif($isNotScholl){

                $datass = array();
                $datass['client_tracestatus'] = '0';
                $datass['channel_id'] = $channelOne['channel_id'];
                $datass['client_source'] = $channelOne['channel_medianame'];
                //导入时渠渠道变更   名单状态小于 0   最高意向星级改为 0
                $datass['client_intention_maxlevel'] = 0;
                $datass['client_intention_level'] = 0;

                $datass['client_isnewtip'] = '1';
                $datass['client_isfromgmc'] = '1';
                $datass['client_tmkbatch'] = $TmkBatchId;//增加批次编号

                if($item['istoschool'] == '-1'){//渠道重新激活是否进校区  1 进校区  -1 不进校区
                    $datass['client_gmcdistributionstatus'] = 0;
                    $datass['client_schtmkdistributionstatus'] = 0;
                    $datass['client_ischaserlapsed'] = 0;
                }
                $this->DataControl->updateData("crm_client", "client_id = '{$isNotScholl['client_id']}'", $datass);


                if($item['istoschool'] == '-1'){//渠道重新激活是否进校区  1 进校区  -1 不进校区
                    $schoolenter = array();
                    $schoolenter['is_enterstatus'] = '-1';
                    $schoolenter['schoolenter_updatetime'] = time();
                    $this->DataControl->updateData("crm_client_schoolenter", "client_id='{$isNotScholl['client_id']}' ", $schoolenter);

                    $principal = array();
                    $principal['principal_leave'] = '1';
                    $principal['principal_updatatime'] = time();
                    $this->DataControl->updateData("crm_client_principal", "client_id='{$isNotScholl['client_id']}' ", $principal);
                }else {
                    if ($item['school_id']) {
                        $schoolenter_id = $this->DataControl->getFieldOne('crm_client_schoolenter', 'schoolenter_id', "client_id='{{$isNotScholl['client_id']}' and school_id='{$item['school_id']}'");
                        if ($schoolenter_id) {
                            $schoolenter = array();
                            $schoolenter['is_enterstatus'] = '1';
                            $schoolenter['schoolenter_updatetime'] = time();
                            $this->DataControl->updateData("crm_client_schoolenter", "client_id='{$isNotScholl['client_id']}' and school_id='{$item['school_id']}'", $schoolenter);
                        } else {
                            $data = array();
                            $data['client_id'] = $isNotScholl['client_id'];
                            $data['school_id'] = $item['school_id'];
                            $data['company_id'] = $request['company_id'];
                            $data['schoolenter_createtime'] = time();
                            $data['schoolenter_updatetime'] = time();
                            $this->DataControl->insertData('crm_client_schoolenter', $data);
                        }
                        $schoolenter = array();
                        $schoolenter['is_enterstatus'] = '-1';
                        $schoolenter['schoolenter_updatetime'] = time();
                        $this->DataControl->updateData("crm_client_schoolenter", "client_id='{$isNotScholl['client_id']}' and school_id <> '{$item['school_id']}'", $schoolenter);
                    }
                }

                //新加
                $addcoursecat = '';
                if ($item['coursercatstr'] != ''){
                    $coursercatarr = explode(",",$item['coursercatstr']);
                    if($coursercatarr){
                        foreach ($coursercatarr as $coursercatarrvar){
                            //意向课程
                            $coursercatOne = $this->DataControl->selectOne("select * from smc_code_coursecat where coursecat_branch = '{$coursercatarrvar}' and company_id = '{$request['company_id']}' limit 0,1");
                            if(!$this->DataControl->getFieldOne("crm_client_intention","intention_id","client_id = '{$isNotScholl['client_id']}' AND coursecat_id = '{$coursercatOne['coursecat_id']}'")){
                                $intention = array();
                                $intention['client_id'] = $isNotScholl['client_id'];
                                $intention['coursetype_id'] = $coursercatOne['coursetype_id'];
                                $intention['coursecat_id'] = $coursercatOne['coursecat_id'];
                                $intention['intention_updatetime'] = time();
                                $this->DataControl->insertData("crm_client_intention", $intention);
                                $addcoursecat .= $coursercatarrvar.",";
                            }
                        }
                    }
                }

                $trackData = array();
                $trackData['client_id'] = $isNotScholl['client_id'];
                $trackData['marketer_id'] = $marOne['marketer_id'];
                $trackData['marketer_name'] = $staffer['staffer_cnname'];
                $trackData['track_validinc'] = 1;
                $trackData['track_followmode'] = 4;
                $trackData['track_linktype'] = '名单导入';
                if($item['istoschool'] == '-1') {//渠道重新激活是否进校区  1 进校区  -1 不进校区
                    $trackData['track_note'] = '由集团管理中心导入名单，无意向客户转为招生有效名单（渠道重新激活不进入校区，划到集团（集团本身名单））';
                }else {
                    $trackData['track_note'] = '由集团管理中心导入名单，无意向客户转为招生有效名单（集团本身名单）';
                }
                if($addcoursecat != ''){
                    $addcoursecat = substr($addcoursecat, 0, -1);
                    $trackData['track_note'] .= ",同时新增({$addcoursecat})意向班种";
                }
                $trackData['track_createtime'] = time();
                $trackData['track_type'] = 1;
                $trackData['track_initiative'] = 1;
                $this->DataControl->insertData('crm_client_track', $trackData);

                //已存在名单进行更新 显示为导入失败
                $updatetime = date("Y-m-d",time());
                $item['reason'] = "系统已存在此意向客户，之前名单被标记为无效或无意向，现在重置为待跟踪状态，部分信息进行更新，更新时间为：{$updatetime}！";
                $falarray[] = $item;
                $datas = array();
                $datas['school_cnname'] = $item['school_cnname'];
                $datas['school_branch'] = $item['school_branch'];
                $datas['updatetime'] = $item['updatetime'];
                $datas['student_cnname'] = $item['student_cnname'];
                $datas['mobile'] = $item['mobile'];
                $datas['age'] = $item['age'];
                $datas['client_gmcmarket'] = $item['client_gmcmarket'];
                $datas['remark'] = $item['remark'];
                $datas['reason'] = $item['reason'];
                $datas['school_Rename'] = $schoolOne['school_cnname'];
                $datas['channel_cnname'] = $item['channel_name'];
                $datas['fal_pid'] = $couponspid_get;
                $datas['fal_createtime'] = time();
                $fal++;
                $this->DataControl->insertData('crm_client_fal', $datas);
            }  else {
                //导入时 新增名单 最高意向星级改为 0
                $data['client_intention_maxlevel'] = 0;

                $data['client_isfromgmc'] = 1;
                $data['client_isnewtip'] = '1';
                $data['marketer_id'] = $marOne['marketer_id'];
                $data['client_tmkbatch'] = $TmkBatchId;//增加批次编号
                $id = $this->DataControl->insertData("crm_client", $data);

                //新加
                $addcoursecat = '';
                if ($item['coursercatstr'] != ''){
                    $coursercatarr = explode(",",$item['coursercatstr']);
                    if($coursercatarr){
                        foreach ($coursercatarr as $coursercatarrvar){
                            //意向课程
                            $coursercatOne = $this->DataControl->selectOne("select * from smc_code_coursecat where coursecat_branch = '{$coursercatarrvar}' and company_id = '{$request['company_id']}' limit 0,1");
                            if(!$this->DataControl->getFieldOne("crm_client_intention","intention_id","client_id = '{$id}' AND coursecat_id = '{$coursercatOne['coursecat_id']}'")){
                                $intention = array();
                                $intention['client_id'] = $id;
                                $intention['coursetype_id'] = $coursercatOne['coursetype_id'];
                                $intention['coursecat_id'] = $coursercatOne['coursecat_id'];
                                $intention['intention_updatetime'] = time();
                                $this->DataControl->insertData("crm_client_intention", $intention);
                                $addcoursecat .= $coursercatarrvar.",";
                            }
                        }
                    }
                }

                if ($id) {
                    if($item['istoschool'] != '-1') {//渠道重新激活是否进校区  1 进校区  -1 不进校区
                        if ($item['school_id']) {
                            $datas = array();
                            $datas['client_id'] = $id;
                            $datas['school_id'] = $item['school_id'];
                            $datas['company_id'] = $request['company_id'];
                            $datas['schoolenter_createtime'] = time();
                            $datas['schoolenter_updatetime'] = time();
                            $this->DataControl->insertData('crm_client_schoolenter', $datas);
                        }
                    }
                    $trackData = array();
                    $trackData['client_id'] = $id;
                    $trackData['marketer_id'] = $marOne['marketer_id'];
                    $trackData['marketer_name'] = $staffer['staffer_cnname'];
                    $trackData['track_validinc'] = 1;
                    $trackData['track_linktype'] = '名单导入';
                    $trackData['track_note'] = "由集团管理中心导入名单,新建客户信息";
                    if($addcoursecat != ''){
                        $addcoursecat = substr($addcoursecat, 0, -1);
                        $trackData['track_note'] .= ",同时新增({$addcoursecat})意向班种";
                    }
                    $trackData['track_createtime'] = time();
                    $trackData['track_type'] = 1;
                    $trackData['track_initiative'] = 1;
                    $this->DataControl->insertData('crm_client_track', $trackData);

                    $suc++;
                } else {
                    $item['reason'] = '数据格式有误';
                    $falarray[] = $item;
                    $datas = array();
                    $datas['school_cnname'] = $item['school_cnname'];
                    $datas['school_branch'] = $item['school_branch'];
                    $datas['updatetime'] = $item['updatetime'];
                    $datas['student_cnname'] = $item['student_cnname'];
                    $datas['mobile'] = $item['mobile'];
                    $datas['age'] = $item['age'];
                    $datas['client_gmcmarket'] = $item['client_gmcmarket'];
                    $datas['channel_cnname'] = $item['channel_name'];
                    $datas['remark'] = $item['remark'];
                    $datas['reason'] = $item['reason'];
                    $datas['fal_pid'] = $couponspid_get;
                    $datas['fal_createtime'] = time();
                    $fal++;
                    $this->DataControl->insertData('crm_client_fal', $datas);

                }
            }
        }

        $fieldstring = array('school_cnname', 'school_branch','student_cnname','age','sex','reason','updatetime','mobile','channel_name','client_gmcmarket', 'remark');
        $fieldname = array('校区名称', '校区编号','学员姓名','年龄','性别','失败原因', '日期','联系方式','渠道名称', '电销姓名', '客户备注');
        $fieldcustom = array("1", "1", "1", "1", "1", "1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1", "1", "1", "1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        if($suc>0){
            $this->DataControl->insertData('crm_client_tmkbatch',  ["company_id"=>"{$request['company_id']}","tmkbatch_number"=>"{$TmkBatchId}"]);
        }
        $result = array();
        $result['fal'] = $fal;
        $result['suc'] = $suc;
        $result['field'] = $field;
        $result['falarray'] = $falarray;
        $result['falarrayisstu'] = $falarrayisstu;
        $result['time'] = $couponspid_get;

        $this->addGmcWorkLog($request['company_id'], $request['staffer_id'], "招生管理", '导入渠道招生名单', dataEncode($request));

        ajax_return(array('error' => '0', 'errortip' => "导入名单成功", 'result' => $result));

    }

    //渠道名单导入中 在校生 失败名单扩科导入操作   ----  在上一步 失败名单中继续导入扩科名单那，名单是累计使用，导入成功数、失败数也是累计的
    function NextStepChannelIsstuView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $marOne = $this->DataControl->selectOne("select marketer_id,staffer_id,marketer_name from  crm_marketer where staffer_id='{$request['staffer_id']}' and company_id='{$request['company_id']}' ");
        $staffer = $this->DataControl->selectOne("select * from smc_staffer WHERE staffer_id = '{$request['staffer_id']}'");
        if (!$marOne) {
            $datas = array();
            $datas['company_id'] = $staffer['company_id'];
            $datas['staffer_id'] = $staffer['staffer_id'];
            $datas['postrole_id'] = $staffer['postrole_id'];
            $datas['marketer_istest'] = $staffer['staffer_istest'];
            $datas['marketer_name'] = $staffer['staffer_cnname'];
            $datas['marketer_img'] = $staffer['staffer_img'];
            $datas['marketer_mobile'] = $staffer['staffer_mobile'];
            $datas['marketer_tokencode'] = $staffer['staffer_tokencode'];
            $datas['marketer_tokenencrypt'] = $staffer['staffer_tokenencrypt'];
            $datas['marketer_lasttime'] = $staffer['staffer_lasttime'];
            $datas['marketer_lastip'] = $staffer['staffer_lastip'];
            $datas['marketer_createtime'] = $staffer['staffer_createtime'];
            $datas['marketer_status'] = '1';
            $mark_id = $this->DataControl->insertData('crm_marketer', $datas);
            $marOne['marketer_id'] = $mark_id;
            $marOne['marketer_name'] = $staffer['staffer_cnname'];
        }

        $List = json_decode(stripslashes($request['list']), true);
        $suc = 0;
        $fal = 0;
        $sucIds = '0';
        $falarray = array();

        do {
            $couponspid_get = $this->createOrderPid('CWSTU');
        } while ($this->DataControl->getFieldOne("crm_client_fal", "fal_id", "fal_pid='{$couponspid_get}'"));

//        $TmkBatchId = '';
//        if(count($List) >= 1){
//            do {
//                $TmkBatchId = getTmkBatchId();
//            } while ($this->DataControl->selectOne("select tmkbatch_id from crm_client_tmkbatch where tmkbatch_number='{$TmkBatchId}' and company_id='{$request['company_id']}' limit 0,1"));
//        }
        foreach ($List as $item) {
            $data = array();
            $data['student_id'] = $item['student_id'];
            $data['school_id'] = $item['school_id'];
            $data['tips_mobile'] = $item['mobile'];
            $data['tips_isdistribute'] = 0;
            $data['tips_createtime'] = strtotime($item['updatetime']);

            $falOne = $this->DataControl->getFieldOne("crm_client_fal", "fal_id" ,"fal_id = '{$item['fal_id']}' ");
            if(!$falOne || $item['fal_id']==''){
                $item['reason'] = '在校生：扩科导入名单数据有问题！';
                $falarray[] = $item;
                $datas = array();
                $datas['school_cnname'] = $item['school_cnname'];
                $datas['school_branch'] = $item['school_branch'];
                $datas['updatetime'] = $item['updatetime'];
                $datas['student_cnname'] = $item['student_cnname'];
                $datas['mobile'] = $item['mobile'];
                $datas['age'] = $item['age'];
                $datas['channel_cnname'] = $item['channel_name'];
                $datas['client_gmcmarket'] = $item['client_gmcmarket'];
                $datas['remark'] = $item['remark'];
                $datas['reason'] = $item['reason'];
                $datas['fal_pid'] = $couponspid_get;
                $datas['fal_createtime'] = time();
                $fal++;
                $this->DataControl->insertData('crm_student_fal', $datas);
                continue;
            }

            if ($item['school_branch']) {
                $school = $this->DataControl->getFieldOne("smc_school", "school_id", "school_branch = '{$item['school_branch']}' and company_id = '{$request['company_id']}'");
                if (!$school) {
                    $item['reason'] = '在校生：（excel）学校不存在！';
                    $falarray[] = $item;
                    $datas = array();
                    $datas['school_cnname'] = $item['school_cnname'];
                    $datas['school_branch'] = $item['school_branch'];
                    $datas['updatetime'] = $item['updatetime'];
                    $datas['student_cnname'] = $item['student_cnname'];
                    $datas['mobile'] = $item['mobile'];
                    $datas['age'] = $item['age'];
                    $datas['client_gmcmarket'] = $item['client_gmcmarket'];
                    $datas['channel_cnname'] = $item['channel_name'];
                    $datas['remark'] = $item['remark'];
                    $datas['reason'] = $item['reason'];
                    $datas['fal_pid'] = $couponspid_get;
                    $datas['fal_createtime'] = time();
                    $fal++;
                    $this->DataControl->insertData('crm_student_fal', $datas);

                    $clientFal = array();
                    $clientFal['fal_isstu_issuc'] = 0;
                    $clientFal['fal_isstu_falreason'] = $item['reason'];
                    $this->DataControl->updateData("crm_client_fal","fal_id = '{$item['fal_id']}'",$clientFal);
                    continue;
                }
            }else{
                $item['reason'] = '在校生：（excel）学校编号不存在！';
                $falarray[] = $item;
                $datas = array();
                $datas['school_cnname'] = $item['school_cnname'];
                $datas['school_branch'] = $item['school_branch'];
                $datas['updatetime'] = $item['updatetime'];
                $datas['student_cnname'] = $item['student_cnname'];
                $datas['mobile'] = $item['mobile'];
                $datas['age'] = $item['age'];
                $datas['client_gmcmarket'] = $item['client_gmcmarket'];
                $datas['channel_cnname'] = $item['channel_name'];
                $datas['remark'] = $item['remark'];
                $datas['reason'] = $item['reason'];
                $datas['fal_pid'] = $couponspid_get;
                $datas['fal_createtime'] = time();
                $fal++;
                $this->DataControl->insertData('crm_student_fal', $datas);

                $clientFal = array();
                $clientFal['fal_isstu_issuc'] = 0;
                $clientFal['fal_isstu_falreason'] = $item['reason'];
                $this->DataControl->updateData("crm_client_fal","fal_id = '{$item['fal_id']}'",$clientFal);
                continue;
            }

            $channelOne = $this->DataControl->getFieldOne("crm_code_channel", "channel_id,channel_medianame,channel_quality"
                , "channel_name = '{$item['channel_name']}' and company_id = '{$request['company_id']}'");
            if (!$channelOne) {
                $item['reason'] = '在校生：（excel）系统未检测名单归属渠道，无法导入名单，请检查渠道明细是否存在！';
                $falarray[] = $item;
                $datas = array();
                $datas['school_cnname'] = $item['school_cnname'];
                $datas['school_branch'] = $item['school_branch'];
                $datas['updatetime'] = $item['updatetime'];
                $datas['student_cnname'] = $item['student_cnname'];
                $datas['mobile'] = $item['mobile'];
                $datas['age'] = $item['age'];
                $datas['channel_cnname'] = $item['channel_name'];
                $datas['client_gmcmarket'] = $item['client_gmcmarket'];
                $datas['remark'] = $item['remark'];
                $datas['reason'] = $item['reason'];
                $datas['fal_pid'] = $couponspid_get;
                $datas['fal_createtime'] = time();
                $fal++;
                $this->DataControl->insertData('crm_student_fal', $datas);

                $clientFal = array();
                $clientFal['fal_isstu_issuc'] = 0;
                $clientFal['fal_isstu_falreason'] = $item['reason'];
                $this->DataControl->updateData("crm_client_fal","fal_id = '{$item['fal_id']}'",$clientFal);
                continue;
            }

            //找到对应的渠道
            $data['client_source'] = $channelOne['channel_medianame'];
            $data['channel_id'] = $channelOne['channel_id'];

            if (!checkMobile($item['mobile'])) {
                $item['reason'] = '在校生：（excel）手机号码校验错误，无法导入名单，请检查手机号码是否正确！';
                $falarray[] = $item;
                $datas = array();
                $datas['school_cnname'] = $item['school_cnname'];
                $datas['school_branch'] = $item['school_branch'];
                $datas['updatetime'] = $item['updatetime'];
                $datas['student_cnname'] = $item['student_cnname'];
                $datas['mobile'] = $item['mobile'];
                $datas['age'] = $item['age'];
                $datas['channel_cnname'] = $item['channel_name'];
                $datas['client_gmcmarket'] = $item['client_gmcmarket'];
                $datas['remark'] = $item['remark'];
                $datas['reason'] = $item['reason'];
                $datas['fal_pid'] = $couponspid_get;
                $datas['fal_createtime'] = time();
                $fal++;
                $this->DataControl->insertData('crm_student_fal', $datas);

                $clientFal = array();
                $clientFal['fal_isstu_issuc'] = 0;
                $clientFal['fal_isstu_falreason'] = $item['reason'];
                $this->DataControl->updateData("crm_client_fal","fal_id = '{$item['fal_id']}'",$clientFal);
                continue;
            }

            //补充crm学生数据
            if($this->DataControl->selectOne(" select student_id from crm_student where student_id = '{$item['student_id']}'  limit 0,1")){
                $studentData = array();
                $studentData['student_type'] = 0;
                $this->DataControl->updateData("crm_student", "student_id='{$item['student_id']}'", $studentData);
            }else {
                $crmStudentData = array();
                $crmStudentData['student_id'] = $item['student_id'];
                $crmStudentData['student_distributionstatus'] = '0';
                $crmStudentData['student_tracestatus'] = '0';
                $crmStudentData['student_intention_level'] = '0';
                $crmStudentData['student_type'] = 0;
                $crmStudentData['school_id'] = $item['school_id'];
                $crmStudentData['student_createtime'] = time();
                $crmStudentData['student_updatatime'] = time();
                $this->DataControl->insertData("crm_student", $crmStudentData);
            }

            //新加 -- 意向课程
            $addcoursecat = '';
            $nothavereginfo = 0;
            if ($item['coursercatstr'] != ''){
                $coursercatarr = explode(",",$item['coursercatstr']);
                $coursesucc = 0;
                if($coursercatarr){
                    foreach ($coursercatarr as $coursercatarrvar){
                        //意向课程
                        $coursercatOne = $this->DataControl->selectOne("select * from smc_code_coursecat where coursecat_branch = '{$coursercatarrvar}' and company_id = '{$request['company_id']}' limit 0,1");
                        //是否存这个班种
                        $intentionOne = $this->DataControl->getFieldOne("crm_student_intention","intention_id","student_id = '{$item['student_id']}' AND coursetype_id = '{$coursercatOne['coursetype_id']}'");
                        //是否有这个学生这个班种的报名
                        $infoOne = $this->DataControl->selectOne("SELECT info_id FROM smc_student_registerinfo WHERE student_id = '{$item['student_id']}' and info_status = 1 and school_id = '{$item['school_id']}' and coursetype_id = '{$coursercatOne['coursetype_id']}' limit 0,1");
                        $infotwo = $this->DataControl->selectOne("SELECT info_id FROM smc_student_registerinfo WHERE student_id = '{$item['student_id']}' and info_status = 1 and school_id = '{$item['school_id']}'  limit 0,1");
                        if($infotwo){
                            if($intentionOne['intention_id'] > 1  && !$infoOne){
                                $coursesucc++;
                            }elseif(!$intentionOne && !$infoOne ){
                                $intention = array();
                                $intention['student_id'] = $item['student_id'];
                                $intention['coursetype_id'] = $coursercatOne['coursetype_id'];
                                $intention['coursecat_id'] = $coursercatOne['coursecat_id'];
                                $intention['intention_updatetime'] = time();
                                $this->DataControl->insertData("crm_student_intention", $intention);
                                $coursesucc++;
                                $addcoursecat .= $coursercatarrvar.",";
                            }
                        }else{
                            $nothavereginfo = '1';
                        }
                    }
                    if($nothavereginfo == 1){
                        $item['reason'] = '在校生：（excel）该生暂未满足扩科要求';
                        $falarray[] = $item;
                        $datas = array();
                        $datas['school_cnname'] = $item['school_cnname'];
                        $datas['school_branch'] = $item['school_branch'];
                        $datas['updatetime'] = $item['updatetime'];
                        $datas['student_cnname'] = $item['student_cnname'];
                        $datas['mobile'] = $item['mobile'];
                        $datas['age'] = $item['age'];
                        $datas['client_gmcmarket'] = $item['client_gmcmarket'];
                        $datas['channel_cnname'] = $item['channel_name'];
                        $datas['remark'] = $item['remark'];
                        $datas['reason'] = $item['reason'];
                        $datas['fal_pid'] = $couponspid_get;
                        $datas['fal_createtime'] = time();
                        $fal++;
                        $this->DataControl->insertData('crm_student_fal', $datas);

                        $clientFal = array();
                        $clientFal['fal_isstu_issuc'] = 0;
                        $clientFal['fal_isstu_falreason'] = $item['reason'];
                        $this->DataControl->updateData("crm_client_fal","fal_id = '{$item['fal_id']}'",$clientFal);
                        continue;
                    }

                    if($coursesucc) {
                        //名单要是已经是扩科名单了（不论在哪家校），提示扩科名单重复
                        if(!$this->DataControl->selectOne("select tips_id from crm_student_expand_tips where student_id = '{$item['student_id']}'  limit 0,1")) {//and school_id = '{$item['school_id']}'
                            //添加集团渠道导入的扩科名单记录
                            $tipsdata = array();
                            $tipsdata['student_id'] = $item['student_id'];
                            $tipsdata['school_id'] = $item['school_id'];
                            $tipsdata['client_source'] = $data['client_source'];
                            $tipsdata['channel_id'] = $data['channel_id'];
                            $tipsdata['tips_mobile'] = $item['mobile'];
                            $tipsdata['tips_isexpand'] = 1;
                            $tipsdata['tips_isdistribute'] = 0;
                            $tipsdata['tips_createtime'] = time();
                            $tipsid = $this->DataControl->insertData("crm_student_expand_tips", $tipsdata);

                            $sucIds .= ",".$item['fal_id'];

                            if ($tipsid) {
                                $trackData = array();
                                $dataTrack['school_id'] = $item['school_id'];
                                $dataTrack['student_id'] = $item['student_id'];
                                $trackData['marketer_id'] = $marOne['marketer_id'];
                                $trackData['marketer_name'] = $marOne['marketer_name'];
                                $trackData['track_validinc'] = 1;
                                $trackData['track_linktype'] = '名单导入';
                                $trackData['track_note'] = "由集团管理中心导入名单,在校生：（excel）渠道导入";
                                if($addcoursecat != ''){
                                    $addcoursecat = substr($addcoursecat, 0, -1);
                                    $trackData['track_note'] .= ",同时新增({$addcoursecat})意向班种";
                                }
                                $trackData['track_createtime'] = time();
                                $this->DataControl->insertData('crm_student_track', $trackData);

                                //导入成功
                                $clientFal = array();
                                $clientFal['fal_isstu_issuc'] = 1;
                                $this->DataControl->updateData("crm_client_fal","fal_id = '{$item['fal_id']}'",$clientFal);

                                $suc++;
                            } else {
                                $item['reason'] = '在校生：（excel）数据存储失败';
                                $falarray[] = $item;
                                $datas = array();
                                $datas['school_cnname'] = $item['school_cnname'];
                                $datas['school_branch'] = $item['school_branch'];
                                $datas['updatetime'] = $item['updatetime'];
                                $datas['student_cnname'] = $item['student_cnname'];
                                $datas['mobile'] = $item['mobile'];
                                $datas['age'] = $item['age'];
                                $datas['client_gmcmarket'] = $item['client_gmcmarket'];
                                $datas['channel_cnname'] = $item['channel_name'];
                                $datas['remark'] = $item['remark'];
                                $datas['reason'] = $item['reason'];
                                $datas['fal_pid'] = $couponspid_get;
                                $datas['fal_createtime'] = time();
                                $fal++;
                                $this->DataControl->insertData('crm_student_fal', $datas);

                                $clientFal = array();
                                $clientFal['fal_isstu_issuc'] = 0;
                                $clientFal['fal_isstu_falreason'] = $item['reason'];
                                $this->DataControl->updateData("crm_client_fal","fal_id = '{$item['fal_id']}'",$clientFal);
                                continue;
                            }
                        }else {
                            $item['reason'] = '在校生：（excel）已存在这个扩科名单的数据';
                            $falarray[] = $item;
                            $datas = array();
                            $datas['school_cnname'] = $item['school_cnname'];
                            $datas['school_branch'] = $item['school_branch'];
                            $datas['updatetime'] = $item['updatetime'];
                            $datas['student_cnname'] = $item['student_cnname'];
                            $datas['mobile'] = $item['mobile'];
                            $datas['age'] = $item['age'];
                            $datas['client_gmcmarket'] = $item['client_gmcmarket'];
                            $datas['channel_cnname'] = $item['channel_name'];
                            $datas['remark'] = $item['remark'];
                            $datas['reason'] = $item['reason'];
                            $datas['fal_pid'] = $couponspid_get;
                            $datas['fal_createtime'] = time();
                            $fal++;
                            $this->DataControl->insertData('crm_student_fal', $datas);

                            $clientFal = array();
                            $clientFal['fal_isstu_issuc'] = 0;
                            $clientFal['fal_isstu_falreason'] = $item['reason'];
                            $this->DataControl->updateData("crm_client_fal","fal_id = '{$item['fal_id']}'",$clientFal);
                            continue;
                        }

                    }else{
                        $item['reason'] = '在校生：（excel）意向课程与在读课程同班组，导入失败！';
                        $falarray[] = $item;
                        $datas = array();
                        $datas['school_cnname'] = $item['school_cnname'];
                        $datas['school_branch'] = $item['school_branch'];
                        $datas['updatetime'] = $item['updatetime'];
                        $datas['student_cnname'] = $item['student_cnname'];
                        $datas['mobile'] = $item['mobile'];
                        $datas['age'] = $item['age'];
                        $datas['channel_cnname'] = $item['channel_name'];
                        $datas['client_gmcmarket'] = $item['client_gmcmarket'];
                        $datas['remark'] = $item['remark'];
                        $datas['reason'] = $item['reason'];
                        $datas['fal_pid'] = $couponspid_get;
                        $datas['fal_createtime'] = time();
                        $fal++;
                        $this->DataControl->insertData('crm_student_fal', $datas);

                        $clientFal = array();
                        $clientFal['fal_isstu_issuc'] = 0;
                        $clientFal['fal_isstu_falreason'] = $item['reason'];
                        $this->DataControl->updateData("crm_client_fal","fal_id = '{$item['fal_id']}'",$clientFal);
                        continue;
                    }
                }
            }else {
                $item['reason'] = '在校生：（excel）意向班种数据为空';
                $falarray[] = $item;
                $datas = array();
                $datas['school_cnname'] = $item['school_cnname'];
                $datas['school_branch'] = $item['school_branch'];
                $datas['updatetime'] = $item['updatetime'];
                $datas['student_cnname'] = $item['student_cnname'];
                $datas['mobile'] = $item['mobile'];
                $datas['age'] = $item['age'];
                $datas['channel_cnname'] = $item['channel_name'];
                $datas['client_gmcmarket'] = $item['client_gmcmarket'];
                $datas['remark'] = $item['remark'];
                $datas['reason'] = $item['reason'];
                $datas['fal_pid'] = $couponspid_get;
                $datas['fal_createtime'] = time();
                $fal++;
                $this->DataControl->insertData('crm_student_fal', $datas);

                $clientFal = array();
                $clientFal['fal_isstu_issuc'] = 0;
                $clientFal['fal_isstu_falreason'] = $item['reason'];
                $this->DataControl->updateData("crm_client_fal","fal_id = '{$item['fal_id']}'",$clientFal);
                continue;
            }
        }
        $fieldstring = array('school_cnname', 'student_cnname', 'age', 'school_branch', 'updatetime', 'mobile', 'channel_name', 'client_gmcmarket', 'remark', 'reason');
        $fieldname = array('校区名称','学员姓名',  '年龄','校区编号', '日期',  '联系方式', '渠道名称', '电销姓名', '客户备注', '失败原因');
        $fieldcustom = array("1", "1", "1", "1", "1", "1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1", "1", "1", "1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

//        if($suc>0){
//            $this->DataControl->insertData('crm_client_tmkbatch',  ["company_id"=>"{$request['company_id']}","tmkbatch_number"=>"{$TmkBatchId}"]);
//        }

        $falarrayOld = $this->DataControl->selectClear("select *,IF(fal_isstu_falreason <> '',fal_isstu_falreason,reason) as reason from crm_client_fal where fal_pid = '{$request['fal_pid']}' and fal_id not in ({$sucIds})");

        $result = array();
//        $result['fal'] = $fal;
//        $result['suc'] = $suc;
        $result['fal'] = $request['fal']-$suc;
        $result['suc'] = $request['suc']+$suc;
        $result['field'] = $field;
        $result['falarray'] = $falarrayOld;
        $result['falarraynew'] = $falarray;
//        $result['time'] = $couponspid_get;//本次导入失败的编号
//        $result['fal_pid'] = $request['fal_pid'];//上一步导入失败的编号
        //方便前端接口
        $result['fal_pid'] = $couponspid_get;//本次导入失败的编号
        $result['time'] = $request['fal_pid'];//上一步导入失败的编号
        $this->addGmcWorkLog($request['company_id'], $request['staffer_id'], "招生管理", '导入渠道招生名单-在校生扩科名单', dataEncode($request));

        ajax_return(array('error' => '0', 'errortip' => "导入在校生名单成功", 'result' => $result));
    }

    //补充意向课程
    function addClientAddIntention($company_id,$client_id,$coursercatstr){
        //新加
        $addcoursecat = '';
        if ($coursercatstr != ''){
            $coursercatarr = explode(",",$coursercatstr);
            if($coursercatarr){
                foreach ($coursercatarr as $coursercatarrvar){
                    //意向课程
                    $coursercatOne = $this->DataControl->selectOne("select * from smc_code_coursecat where coursecat_branch = '{$coursercatarrvar}' and company_id = '{$company_id}' limit 0,1");
                    if(!$this->DataControl->getFieldOne("crm_client_intention","intention_id","client_id = '{$client_id}' AND coursecat_id = '{$coursercatOne['coursecat_id']}'")){
                        $intention = array();
                        $intention['client_id'] = $client_id;
                        $intention['coursetype_id'] = $coursercatOne['coursetype_id'];
                        $intention['coursecat_id'] = $coursercatOne['coursecat_id'];
                        $intention['intention_updatetime'] = time();
                        $this->DataControl->insertData("crm_client_intention", $intention);
                        $addcoursecat .= $coursercatarrvar.",";
                    }
                }
            }
        }
        return $addcoursecat;
    }

    //解析
    function NextStepActivityView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        return false;

        $marOne = $this->DataControl->selectOne("select marketer_id,staffer_id,marketer_name from  crm_marketer where staffer_id='{$request['staffer_id']}' and company_id='{$request['company_id']}' ");
        $staffer = $this->DataControl->selectOne("select * from smc_staffer WHERE staffer_id = '{$request['staffer_id']}'");
        if (!$marOne) {

            $datas = array();
            $datas['company_id'] = $staffer['company_id'];
            $datas['staffer_id'] = $staffer['staffer_id'];
            $datas['postrole_id'] = $staffer['postrole_id'];
            $datas['marketer_istest'] = $staffer['staffer_istest'];
            $datas['marketer_name'] = $staffer['staffer_cnname'];
            $datas['marketer_img'] = $staffer['staffer_img'];
            $datas['marketer_mobile'] = $staffer['staffer_mobile'];
            $datas['marketer_tokencode'] = $staffer['staffer_tokencode'];
            $datas['marketer_tokenencrypt'] = $staffer['staffer_tokenencrypt'];
            $datas['marketer_lasttime'] = $staffer['staffer_lasttime'];
            $datas['marketer_lastip'] = $staffer['staffer_lastip'];
            $datas['marketer_createtime'] = $staffer['staffer_createtime'];
            $datas['marketer_status'] = '1';
            $mark_id = $this->DataControl->insertData('crm_marketer', $datas);
            $marOne['marketer_id'] = $mark_id;
        }

        $List = json_decode(stripslashes($request['list']), true);
        $suc = 0;
        $fal = 0;
        $falarray = array();

        do {
            $couponspid_get = $this->createOrderPid('CW');
        } while ($this->DataControl->getFieldOne("crm_client_fal", "fal_id", "fal_pid='{$couponspid_get}'"));

        $TmkBatchId = '';
        if(count($List) >= 1){
            do {
                $TmkBatchId = getTmkBatchId();
            } while ($this->DataControl->selectOne("select tmkbatch_id from crm_client_tmkbatch where tmkbatch_number='{$TmkBatchId}' and company_id='{$request['company_id']}' limit 0,1"));
        }
        foreach ($List as $item) {
            $data = array();
            $data['client_cnname'] = $item['student_cnname'];
            $data['marketer_id'] = $marOne['marketer_id'];
            $data['client_sex'] = $item['sex'];
            $data['client_updatetime'] = strtotime($item['updatetime']);
            $data['client_age'] = $item['age'];
            $data['company_id'] = $request['company_id'];
            $data['activity_id'] = $item['activity_id'];
            $activity_name = $this->DataControl->getFieldOne("crm_sell_activity", "activity_name,channel_id,frommedia_name", "activity_id = '{$item['activity_id']}' and company_id = '{$request['company_id']}'");
            $data['channel_id'] = $activity_name['channel_id'];
            $data['client_source'] = $activity_name['frommedia_name'];
            if (!$item['activity_name']) {
                $item['activity_name'] = $activity_name['activity_name'];
            }
            //加渠道判断
            $channelOne = $this->DataControl->getFieldOne("crm_code_channel", "channel_id,channel_isschoolchose,channel_quality"
                , "channel_id='{$activity_name['channel_id']}' ");
            if($channelOne['channel_quality'] == '1'){
                $data['client_isgross'] = 0;//不是毛名单 -- 即 有效
            }elseif($channelOne['channel_quality'] == '0' && $channelOne['channel_quality'] != ''){
                $data['client_isgross'] = 1;//是毛名单
            }else{
                $data['client_isgross'] = 0;//不是毛名单 -- 即 有效
            }

            $data['client_remark'] = $item['remark'];
            $data['client_gmcmarket'] = $item['client_gmcmarket'];
            $data['client_mobile'] = $item['mobile'];
            $data['client_createtime'] = time();

            $isset = $this->DataControl->getFieldOne("crm_sell_activity", "activity_id", "activity_name = '{$activity_name['activity_name']}' and company_id = '{$request['company_id']}'");
            $isname = $this->DataControl->getFieldOne("crm_client", "client_id", "client_mobile = '{$item['mobile']}' and company_id = '{$request['company_id']}' and client_tracestatus >= '0'");
            $school = $this->DataControl->selectOne("select s.school_cnname from crm_client_schoolenter as sc left join smc_school as s on s.school_id = sc.school_id WHERE client_id = '{$isname['client_id']}'");
            $clientType = $this->DataControl->getFieldOne("crm_client", "client_source", "client_id = '{$isname['client_id']}'");
            $isname2 = $this->DataControl->selectOne("select c.client_id from crm_client as c left join crm_client_schoolenter as s on c.client_id = s.client_id where c.client_mobile = '{$item['mobile']}' and c.company_id = '{$request['company_id']}' and c.client_tracestatus < '0' and s.school_id = '{$item['school_id']}'");
            $isname3 = $this->DataControl->selectOne("select c.client_id,s.school_id from crm_client as c left join crm_client_schoolenter as s on c.client_id = s.client_id where c.client_mobile = '{$item['mobile']}' and c.company_id = '{$request['company_id']}' and c.client_tracestatus < '0' and s.school_id <> '{$item['school_id']}'");

            if ($data['client_cnname'] == '' || $data['client_mobile'] == '') {
                $item['reason'] = '学员姓名/手机不能为空';
                $falarray[] = $item;
                $datas = array();
                $datas['school_cnname'] = $item['school_cnname'];
                $datas['school_branch'] = $item['school_branch'];
                $datas['updatetime'] = $item['updatetime'];
                $datas['student_cnname'] = $item['student_cnname'];
                $datas['mobile'] = $item['mobile'];
                $datas['age'] = $item['age'];
                $datas['activity_name'] = $item['activity_name'];
                $datas['client_gmcmarket'] = $item['client_gmcmarket'];
                $datas['remark'] = $item['remark'];
                $datas['reason'] = $item['reason'];
                $datas['fal_pid'] = $couponspid_get;
                $datas['fal_createtime'] = time();

                $fal++;
                $this->DataControl->insertData('crm_client_fal', $datas);

            } elseif (!$isset) {
                $item['reason'] = '活动不存在';
                $falarray[] = $item;
                $datas = array();
                $datas['school_cnname'] = $item['school_cnname'];
                $datas['school_branch'] = $item['school_branch'];
                $datas['updatetime'] = $item['updatetime'];
                $datas['student_cnname'] = $item['student_cnname'];
                $datas['mobile'] = $item['mobile'];
                $datas['age'] = $item['age'];
                $datas['activity_name'] = $item['activity_name'];
                $datas['client_gmcmarket'] = $item['client_gmcmarket'];
                $datas['remark'] = $item['remark'];
                $datas['reason'] = $item['reason'];
                $datas['fal_pid'] = $couponspid_get;
                $datas['fal_createtime'] = time();

                $fal++;
                $this->DataControl->insertData('crm_client_fal', $datas);


            } elseif ($isname) {
                $item['reason'] = '学校已存在此名单';
                $falarray[] = $item;
                $datas = array();
                $datas['school_cnname'] = $item['school_cnname'];
                $datas['school_branch'] = $item['school_branch'];
                $datas['updatetime'] = $item['updatetime'];
                $datas['student_cnname'] = $item['student_cnname'];
                $datas['mobile'] = $item['mobile'];
                $datas['age'] = $item['age'];
                $datas['activity_name'] = $item['activity_name'];
                $datas['client_gmcmarket'] = $item['client_gmcmarket'];
                $datas['remark'] = $item['remark'];
                $datas['reason'] = $item['reason'];
                $datas['school_Rename'] = $school['school_cnname'];
                $datas['channel_cnname'] = $clientType['client_source'];
                $datas['fal_pid'] = $couponspid_get;
                $datas['fal_createtime'] = time();

                $fal++;
                $this->DataControl->insertData('crm_client_fal', $datas);

                //补充意向班种
                $addcoursecatstr = $this->addClientAddIntention($request['company_id'],$isname['client_id'],$item['coursecatstr']);

                $trackData = array();
                $trackData['client_id'] = $isname['client_id'];
                $trackData['marketer_id'] = $marOne['marketer_id'];
                $trackData['marketer_name'] = $staffer['staffer_cnname'];
                $trackData['track_validinc'] = 1;
                $trackData['track_followmode'] = 4;
                $trackData['track_linktype'] = '名单导入';
                $trackData['track_note'] = "集团招生管理中心导入名单，系统检测到重复记录，记录本次导入信息，导入备注：{$item['remark']}";
                if($addcoursecatstr != ''){
                    $addcoursecat = substr($addcoursecatstr, 0, -1);
                    $trackData['track_note'] .= ",同时新增({$addcoursecat})意向班种";
                }
                $trackData['track_createtime'] = time();
                $trackData['track_type'] = 1;
                $trackData['track_initiative'] = 1;
                $this->DataControl->insertData('crm_client_track', $trackData);

            } elseif ($isname2) {
                $datass = array();
                $datass['client_tracestatus'] = '0';
                $datass['activity_id'] = $item['activity_id'];
                $activity_name = $this->DataControl->getFieldOne("crm_sell_activity", "activity_name,channel_id,frommedia_name", "activity_id = '{$item['activity_id']}' and company_id = '{$request['company_id']}'");
                $datass['channel_id'] = $activity_name['channel_id'];
                $datass['client_source'] = $activity_name['frommedia_name'];
                $datass['client_isnewtip'] = '1';
                $datass['client_tmkbatch'] = $TmkBatchId;//添加批次号

                //导入时渠渠道变更   名单状态小于 0   最高意向星级改为 0
                $datass['client_intention_maxlevel'] = 0;
                $datass['client_intention_level'] = 0;
                $this->DataControl->updateData("crm_client", "client_id = '{$isname2['client_id']}'", $datass);

                //补充意向班种
                $addcoursecatstr = $this->addClientAddIntention($request['company_id'],$isname2['client_id'],$item['coursecatstr']);

                $trackData = array();
                $trackData['client_id'] = $isname2['client_id'];
                $trackData['marketer_id'] = $marOne['marketer_id'];
                $trackData['marketer_name'] = $staffer['staffer_cnname'];
                $trackData['track_validinc'] = 1;
                $trackData['track_followmode'] = 4;
                $trackData['track_linktype'] = '名单导入';
                $trackData['track_note'] = '由集团名单导入，无意向客户转为招生有效名单（同校）';
                if($addcoursecatstr != ''){
                    $addcoursecat = substr($addcoursecatstr, 0, -1);
                    $trackData['track_note'] .= ",同时新增({$addcoursecat})意向班种";
                }
                $trackData['track_createtime'] = time();
                $trackData['track_type'] = 1;
                $trackData['track_initiative'] = 1;
                $this->DataControl->insertData('crm_client_track', $trackData);

                //已存在名单进行更新 显示为导入失败
                $updatetime = date("Y-m-d",time());
                $item['reason'] = "系统已存在此意向客户，之前名单被标记为无效或无意向，现在重置为待跟踪状态，部分信息进行更新，更新时间为：{$updatetime}！";
                $falarray[] = $item;
                $datas = array();
                $datas['school_cnname'] = $item['school_cnname'];
                $datas['school_branch'] = $item['school_branch'];
                $datas['updatetime'] = $item['updatetime'];
                $datas['student_cnname'] = $item['student_cnname'];
                $datas['mobile'] = $item['mobile'];
                $datas['age'] = $item['age'];
                $datas['activity_name'] = $item['activity_name'];
                $datas['client_gmcmarket'] = $item['client_gmcmarket'];
                $datas['remark'] = $item['remark'];
                $datas['reason'] = $item['reason'];
                $datas['school_Rename'] = $school['school_cnname'];
                $datas['channel_cnname'] = $clientType['client_source'];
                $datas['fal_pid'] = $couponspid_get;
                $datas['fal_createtime'] = time();

                $fal++;
                $this->DataControl->insertData('crm_client_fal', $datas);
//                $suc++;

            } elseif ($isname3) {

                $datass = array();
                $datass['client_tracestatus'] = '0';
                $datass['activity_id'] = $item['activity_id'];
                $activity_name = $this->DataControl->getFieldOne("crm_sell_activity", "activity_name,channel_id,frommedia_name", "activity_id = '{$item['activity_id']}' and company_id = '{$request['company_id']}'");
                $datass['channel_id'] = $activity_name['channel_id'];
                $datass['client_source'] = $activity_name['frommedia_name'];
                $datass['client_isnewtip'] = '1';
                $datass['client_tmkbatch'] = $TmkBatchId;//添加批次号
                //导入时渠渠道变更   名单状态小于 0   最高意向星级改为 0
                $datass['client_intention_maxlevel'] = 0;
                $datass['client_intention_level'] = 0;

                $this->DataControl->updateData("crm_client", "client_id = '{$isname3['client_id']}'", $datass);

                if ($item['school_id']) {
                    $schoolenter_id = $this->DataControl->getFieldOne('crm_client_schoolenter', 'schoolenter_id', "client_id='{{$isname3['client_id']}' and school_id='{$item['school_id']}'");
                    if ($schoolenter_id) {
                        $schoolenter = array();
                        $schoolenter['is_enterstatus'] = '1';
                        $schoolenter['schoolenter_updatetime'] = time();
                        $this->DataControl->updateData("crm_client_schoolenter", "client_id='{$isname3['client_id']}' and school_id='{$item['school_id']}'", $schoolenter);
                    }else{
                        $data = array();
                        $data['client_id'] = $isname3['client_id'];
                        $data['school_id'] = $item['school_id'];
                        $data['company_id'] = $request['company_id'];
                        $data['schoolenter_createtime'] = time();
                        $data['schoolenter_updatetime'] = time();
                        $this->DataControl->insertData('crm_client_schoolenter', $data);
                    }
                    $schoolenter = array();
                    $schoolenter['is_enterstatus'] = '-1';
                    $schoolenter['schoolenter_updatetime'] = time();
                    $this->DataControl->updateData("crm_client_schoolenter", "client_id='{$isname3['client_id']}' and school_id <> '{$item['school_id']}'", $schoolenter);
                }
                /*else {
                    $this->DataControl->delData("crm_client_schoolenter", "client_id = '{$isname3['client_id']}'");
                }*/

                //补充意向班种
                $addcoursecatstr = $this->addClientAddIntention($request['company_id'],$isname3['client_id'],$item['coursecatstr']);

                $trackData = array();
                $trackData['client_id'] = $isname3['client_id'];
                $trackData['marketer_id'] = $marOne['marketer_id'];
                $trackData['marketer_name'] = $staffer['staffer_cnname'];
                $trackData['track_validinc'] = 1;
                $trackData['track_followmode'] = 5;
                $trackData['track_linktype'] = '名单导入';
                $trackData['track_note'] = '由集团名单导入，无意向客户转为招生有效名单（异校）';
                if($addcoursecatstr != ''){
                    $addcoursecat = substr($addcoursecatstr, 0, -1);
                    $trackData['track_note'] .= ",同时新增({$addcoursecat})意向班种";
                }
                $trackData['track_createtime'] = time();
                $trackData['track_type'] = 1;
                $trackData['track_initiative'] = 1;
                $this->DataControl->insertData('crm_client_track', $trackData);

                //已存在名单进行更新 显示为导入失败
                $updatetime = date("Y-m-d",time());
                $item['reason'] = "系统已存在此意向客户，之前名单被标记为无效或无意向，现在重置为待跟踪状态，部分信息进行更新，更新时间为：{$updatetime}！";
                $falarray[] = $item;
                $datas = array();
                $datas['school_cnname'] = $item['school_cnname'];
                $datas['school_branch'] = $item['school_branch'];
                $datas['updatetime'] = $item['updatetime'];
                $datas['student_cnname'] = $item['student_cnname'];
                $datas['mobile'] = $item['mobile'];
                $datas['age'] = $item['age'];
                $datas['activity_name'] = $item['activity_name'];
                $datas['client_gmcmarket'] = $item['client_gmcmarket'];
                $datas['remark'] = $item['remark'];
                $datas['reason'] = $item['reason'];
                $datas['school_Rename'] = $school['school_cnname'];
                $datas['channel_cnname'] = $clientType['client_source'];
                $datas['fal_pid'] = $couponspid_get;
                $datas['fal_createtime'] = time();

                $fal++;
                $this->DataControl->insertData('crm_client_fal', $datas);
//                $suc++;

            } else {
                //导入时 新增名单 最高意向星级改为 0
                $data['client_intention_maxlevel'] = 0;

                $data['client_isnewtip'] = '1';
                $data['marketer_id'] =  $marOne['marketer_id'];
                $data['client_tmkbatch'] = $TmkBatchId;//添加批次号
                $id = $this->DataControl->insertData("crm_client", $data);

                //补充意向班种
                $addcoursecatstr = $this->addClientAddIntention($request['company_id'],$id,$item['coursecatstr']);

                if ($id) {
                    if ($item['school_id']) {
                        $datas = array();
                        $datas['client_id'] = $id;
                        $datas['school_id'] = $item['school_id'];
                        $datas['company_id'] = $request['company_id'];
                        $datas['schoolenter_createtime'] = time();
                        $datas['schoolenter_updatetime'] = time();
                        $this->DataControl->insertData('crm_client_schoolenter', $datas);
                    }
                    $trackData = array();
                    $trackData['client_id'] = $id;
                    $trackData['marketer_id'] = $marOne['marketer_id'];
                    $trackData['marketer_name'] = $staffer['staffer_cnname'];
                    $trackData['track_validinc'] = 1;
                    $trackData['track_linktype'] = '名单导入';
                    $trackData['track_note'] = '由集团活动'.$activity_name['activity_name'].'名单导入,新建客户信息';
                    if($addcoursecatstr != ''){
                        $addcoursecat = substr($addcoursecatstr, 0, -1);
                        $trackData['track_note'] .= ",同时新增({$addcoursecat})意向班种";
                    }
                    $trackData['track_createtime'] = time();
                    $trackData['track_type'] = 1;
                    $trackData['track_initiative'] = 1;
                    $this->DataControl->insertData('crm_client_track', $trackData);

                    $suc++;
                } else {
                    $item['reason'] = '数据格式有误';
                    $falarray[] = $item;
                    $datas = array();
                    $datas['school_cnname'] = $item['school_cnname'];
                    $datas['school_branch'] = $item['school_branch'];
                    $datas['updatetime'] = $item['updatetime'];
                    $datas['student_cnname'] = $item['student_cnname'];
                    $datas['mobile'] = $item['mobile'];
                    $datas['age'] = $item['age'];
                    $datas['activity_name'] = $item['activity_name'];
                    $datas['client_gmcmarket'] = $item['client_gmcmarket'];
                    $datas['remark'] = $item['remark'];
                    $datas['reason'] = $item['reason'];
                    $datas['fal_pid'] = $couponspid_get;
                    $datas['fal_createtime'] = time();

                    $fal++;
                    $this->DataControl->insertData('crm_client_fal', $datas);

                }
            }
        }


        $fieldstring = array('school_cnname', 'school_branch', 'updatetime', 'student_cnname', 'mobile', 'age', 'activity_name', 'client_gmcmarket', 'remark', 'reason');
        $fieldname = array('校区名称', '校区编号', '日期', '学员姓名', '联系方式', '年龄', '活动名称', '电销姓名', '客户备注', '失败原因');
        $fieldcustom = array("1", "1", "1", "1", "1", "1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1", "1", "1", "1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        if($suc>0){
            $this->DataControl->insertData('crm_client_tmkbatch',  ["company_id"=>"{$request['company_id']}","tmkbatch_number"=>"{$TmkBatchId}"]);
        }
        $result = array();
        $result['fal'] = $fal;
        $result['suc'] = $suc;
        $result['field'] = $field;
        $result['falarray'] = $falarray;
        $result['time'] = $couponspid_get;

        $this->addGmcWorkLog($request['company_id'], $request['staffer_id'], "招生管理", '导入活动招生名单', dataEncode($request));

        ajax_return(array('error' => '0', 'errortip' => "导入名单成功", 'result' => $result));
    }

    function importFalView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $schoolList = $this->DataControl->selectClear("select * from crm_client_fal WHERE fal_pid = '{$request['time']}'");

        if (!$schoolList) {
            $this->error = true;
            $this->errortip = "无错误数据";
            return false;
        }

        $outexceldate = array();
        if ($schoolList) {
            $outexceldate = array();
            foreach ($schoolList as $dateexcelvar) {
                $datearray = array();
                $datearray['school_cnname'] = $dateexcelvar['school_cnname'];
                $datearray['school_branch'] = $dateexcelvar['school_branch'];
                $datearray['updatetime'] = $dateexcelvar['updatetime'];
                $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                $datearray['mobile'] = $dateexcelvar['mobile'];
                $datearray['age'] = $dateexcelvar['age'];
                $datearray['activity_name'] = $dateexcelvar['activity_name'];
                $datearray['client_gmcmarket'] = $dateexcelvar['client_gmcmarket'];
                $datearray['remark'] = $dateexcelvar['remark'];
                $datearray['reason'] = $dateexcelvar['reason'];
                $datearray['school_Rename'] = $dateexcelvar['school_Rename'];
                $datearray['channel_cnname'] = $dateexcelvar['channel_cnname'];
                $outexceldate[] = $datearray;
            }
        }

        $excelheader = array('校区名称', '校区编号', '日期', '学员姓名', '联系方式', '年龄', '活动名称', '电销姓名', '客户备注', '失败原因', '学员已存在学校', '渠道类型');
        $excelfileds = array('school_cnname', 'school_branch', 'updatetime', 'student_cnname', 'mobile', 'age', 'activity_name', 'client_gmcmarket', 'remark', 'reason', 'school_Rename', 'channel_cnname');
        query_to_excel($excelheader, $outexceldate, $excelfileds, "导出失败列表.xlsx");


    }

    function importChannelFalView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

//        $schoolList = json_decode($request['list'], true);
        $datawhere = " 1 ";
        //去除导入成功的扩科名单
        if(isset($request['isStuExpand']) && $request['isStuExpand'] == '1' ){
            $datawhere .= " and fal_isstu_issuc <> '1'  ";
        }
        $schoolList = $this->DataControl->selectClear("select * from crm_client_fal WHERE {$datawhere} and fal_pid = '{$request['time']}'");

        if (!$schoolList) {
            $this->error = true;
            $this->errortip = "无错误数据";
            return false;
        }

        $outexceldate = array();
        if ($schoolList) {
            $outexceldate = array();
            foreach ($schoolList as $dateexcelvar) {
                $datearray = array();
                $datearray['school_cnname'] = $dateexcelvar['school_cnname'];
                $datearray['school_branch'] = $dateexcelvar['school_branch'];
                $datearray['updatetime'] = $dateexcelvar['updatetime'];
                $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                $datearray['mobile'] = $dateexcelvar['mobile'];
                $datearray['age'] = $dateexcelvar['age'];
                $datearray['client_gmcmarket'] = $dateexcelvar['client_gmcmarket'];
                $datearray['remark'] = $dateexcelvar['remark'];
                $datearray['reason'] = $dateexcelvar['reason'];
                $datearray['school_Rename'] = $dateexcelvar['school_Rename'];
                $datearray['channel_cnname'] = $dateexcelvar['channel_cnname'];
                $outexceldate[] = $datearray;
            }
        }

        $excelheader = array('校区名称', '校区编号', '日期', '学员姓名', '联系方式', '年龄', '电销姓名', '客户备注', '失败原因', '学员已存在学校', '渠道名称');
        $excelfileds = array('school_cnname', 'school_branch', 'updatetime', 'student_cnname', 'mobile', 'age', 'activity_name', 'client_gmcmarket', 'remark', 'reason', 'school_Rename', 'channel_cnname');
        query_to_excel($excelheader, $outexceldate, $excelfileds, "导出失败列表.xlsx");


    }

    function ImportTrackView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $url = $request['url'];

        $ys_array = array('姓名' => 'client_cnname', '手机号' => 'client_mobile', '沟通类型' => 'track_linktype', '沟通对象' => 'object_code', '意向星级' => 'track_intention_level', '邀约备注' => 'track_note');

        $options=array(
            "ssl"=>array(
                "verify_peer"=>false,
                "verify_peer_name"=>false,
            ),
        );

        file_put_contents('analysis.xls', file_get_contents($url,false,stream_context_create($options)));
        $sqlarray = execl_to_array("analysis.xlsx", $ys_array);
        array_shift($sqlarray);

        $Model = new \Model\Gmc\CrmClientModel();
        $res = $Model->ImportTrack($request, $sqlarray);

        $field = array();
        $k = 0;

        $field[$k]["fieldname"] = "client_cnname";
        $field[$k]["fieldstring"] = "姓名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "client_mobile";
        $field[$k]["fieldstring"] = "手机号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "track_linktype";
        $field[$k]["fieldstring"] = "沟通类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "object_code";
        $field[$k]["fieldstring"] = "沟通对象";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "track_intention_level";
        $field[$k]["fieldstring"] = "意向星级";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "track_note";
        $field[$k]["fieldstring"] = "邀约备注";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        if ($request['step'] == '1') {
            $field[$k]["fieldname"] = "reason";
            $field[$k]["fieldstring"] = "错误原因";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;
        }

        $result = array();
        $result["field"] = $field;
        if ($res) {
            if ($request['step'] == '0') {
                $result["list"] = $res;
            } else {
                $result["suc"] = $res['suc'];
                $result["fal"] = $res['fal'];
                $result["falarray"] = $res['falarray'];
            }

            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res,$request['language_type']);

    }

    function getImportTrackApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\CrmClientModel();
        $result = $this->Model->getImportTrack();
        ajax_return($result,$request['language_type']);
    }

    function delRepeatAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Gmc\CrmClientModel();
        $Model->delRepeat($request);

        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res,$request['language_type']);
    }

    function transferSchoolAction()
    {
        $request = Input('post.', '', 'trim,addslashes');

        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Gmc\CrmClientModel();
        $bool = $Model->transferSchool($request);
        if ($bool) {
            $this->addGmcWorkLog($request['company_id'], $request['staffer_id'], "招生有效名单->招生有效名单", '转校', dataEncode($request));
            $res = array('error' => '0', 'errortip' => $Model->errortip, 'result' => array());
        } else {
            $res = array('error' => '1', 'errortip' => $Model->errortip, 'result' => array());
        }
        ajax_return($res,$request['language_type']);

    }

    function getTransferCnameApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $tranferList = $this->DataControl->selectClear("select transfer_name,transfer_code from crm_code_transfer where company_id='{$request['company_id']}'");
        if (!$tranferList) {
            $data['list'] = array();
        } else {
            $data['list'] = $tranferList;
        }
        $res = array('error' => '0', 'errortip' => '获取成功', 'result' => $data);

        ajax_return($res,$request['language_type']);
    }

    function getAllSchoolApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $schoolList = $this->DataControl->selectClear("select school_id,school_branch,school_cnname from smc_school  where   school_id <>'{$request['school_id']}' and company_id ='{$request['company_id']}'");
        if (!$schoolList) {
            $data['list'] = array();
        } else {
            $data['list'] = $schoolList;
        }
        $res = array('error' => '0', 'errortip' => '获取成功', 'result' => $data);

        ajax_return($res,$request['language_type']);

    }

    function searchComClientApi()
    {

        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $fieldstring = array('client_id', 'client_cnname', 'client_enname', 'client_source', 'channel_name', 'client_sex', 'client_mobile', 'parenter_cnname', 'client_statusname', 'marketer_name', 'school_cnname');
        $fieldname = array('ID', '中文名', '英文名', '渠道类型', '渠道名称', '性别', '主要联系手机', '主要联系人', '名单状态', '主负责人', '已分配学校');
        $fieldcustom = array('0', "1", "1", "1", "1", "1", "1", "1", "1", "1", '1');
        $fieldshow = array('0', "1", "1", "1", "1", "1", "1", "1", "1", '1', '1');

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }
        $result['list'] = array();
        $result['field'] = $field;

        $Model = new \Model\Gmc\CrmClientModel();
        $clientOne = $Model->searchComClient($request);

        $result['list'] = $clientOne['list'];
        $result['field'] = $field;
        $allnum = $clientOne['allnum'];

        if ($result['list']) {
            $res = array('error' => '0', 'errortip' => "名单获取成功", 'result' => $result, "allnum" => $allnum);
        } else {
            $res = array('error' => '1', 'errortip' => "未检索到名单信息，请确认手机号码是否正确", 'result' => $result, "allnum" => '0');
        }

        ajax_return($res,$request['language_type']);
    }

    //沟通类型统计  //--给学校的临时下载接口
    function getComModeView(){
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        if (!$request['company_id']) {
            $res = array('error' => '1', 'errortip' => "公司id错误", 'result' => array());
            ajax_return($res, $request['language_type']);
        }

        $Model = new \Model\Gmc\CrmClientModel();
        $datalist = $Model->getComMode($request);

        if ($datalist) {
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => array() );
        } else {
            $res = array('error' => '1', 'errortip' => "暂无有效名单", 'result' => array() );
        }
        ajax_return($res, $request['language_type']);
    }

    /**
     * 批量分配给校区
     */
    function batchDistributeClientToSchoolAction()
    {
        $request = Input('POST.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        if (!$request['school_id'] || $request['school_id'] == '' || $request['school_id'] == '0') {
            $res = array('error' => '1', 'errortip' => "学校必选", 'result' => array());
            ajax_return($res, $request['language_type']);
        }

        $Model = new \Model\Gmc\CrmClientModel($request);
        $Model->batchDistributeClientToSchool($request);

        $res = array();
        $res['field'] = array();
        $res['data'] = $Model->result;
        $result = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $res);

        ajax_return($result,$request['language_type']);
    }

    /**
     * 批量分配给负责人
     */
    function batchDistributeClientToMarketAction()
    {
        $request = Input('POST.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        if (!$request['tostaffer_id'] || $request['tostaffer_id'] == '') {
            $res = array('error' => '1', 'errortip' => "请选择主负责人", 'result' => array());
            ajax_return($res, $request['language_type']);
        }

        $Model = new \Model\Gmc\CrmClientModel($request);
        $Model->batchDistributeClientToMarket($request);

        $res = array();
        $res['field'] = array();
        $res['data'] = $Model->result;
        $result = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $res);
        ajax_return($result,$request['language_type']);
    }
    /**
     * 批量 转换 负责人
     */
    function batchForwardClientToMarketAction()
    {
        $request = Input('POST.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        if (!$request['tostaffer_id'] || $request['tostaffer_id'] == '') {
            $res = array('error' => '1', 'errortip' => "请选择主负责人", 'result' => array());
            ajax_return($res, $request['language_type']);
        }

        $Model = new \Model\Gmc\CrmClientModel($request);
        $Model->batchForwardClientToMarketAction($request);

        $res = array();
        $res['field'] = array();
        $res['data'] = $Model->result;
        $result = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $res);
        ajax_return($result,$request['language_type']);
    }

    //集团名单跟进
    function getClientFollowUpListView()
    {
        $request = Input('GET.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Gmc\CrmClientModel($request);
        $res = $Model->getClientFollowUpList($request);

        $fieldstring = array('client_cnname','client_tag','course_cnname','client_sex','client_age','client_birthday','client_patriarchname','client_mobile','client_tracestatusname','client_answerphone','track_count','track_alltracknote','principal_name','principal_createtime','channel_medianame','channel_name','activity_name','client_frompage','shengshiqu','client_createtime','track_lasttime');
        $fieldname = $this->LgArraySwitch(array('姓名', '标签','意向课程', '性别', '年龄','出生日期', '主要联系人', '主要联系人手机', '客户状态', '是否接通', '跟进次数', '跟进内容', '负责人','分配时间', '渠道类型', '渠道明细', '来源活动', '来源页面','省市区', '创建时间', '最后跟踪时间'));
        $fieldcustom = array("1", "1","1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1");
        $fieldshow = array("1", "1","1", "0", "1", "0", "1", "1", "1", "1", "1", "1", "1", "1", "1","1","1",  "0", "1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
            if($field[$i]["fieldname"] == 'client_tag' || $field[$i]["fieldname"] == 'course_cnname'){
                $field[$i]["istag"] = "1";
            }
            if($field[$i]["fieldname"] == 'track_alltracknote' ){
                $field[$i]["istrack"] = "1";
            }
        }

        $resultarray = array();
        $resultarray['field'] = $field;
        $resultarray['fieldcustom'] = $res['fieldcustom']?$res['fieldcustom']:'';
        $resultarray['all_num'] = $res['all_num']>0?$res['all_num']:'0';
        $resultarray['remind_num'] = $res['remind_num']>0?$res['remind_num']:'0';
        $resultarray['list'] = is_array($res['list'])?$res['list']:array();

        $result = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $resultarray);
        ajax_return($result,$request['language_type']);
    }

    //获取名单跟进 页切的统计数字
    function getClientFollowUpNumView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        //model
        $Model = new \Model\Gmc\CrmClientModel($request);
        $Model->getClientFollowUpNum($request);
        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $Model->result), $request['language_type']);
    }

    function inviteTimetableApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);

        $TimetableModel = new \Model\Gmc\CrmClientModel($request);
        $dataList = $TimetableModel->inviteTimetable($request);

        $result = array();
        $field = array();

        $k=0;
        $field[$k]["fieldname"] = "周一";
        $field[$k]["fieldstring"] = "Monday";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "周二";
        $field[$k]["fieldstring"] = "Tuesday";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "周三";
        $field[$k]["fieldstring"] = "Wednesday";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "周四";
        $field[$k]["fieldstring"] = "Thursday";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "周五";
        $field[$k]["fieldstring"] = "Friday";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "周六";
        $field[$k]["fieldstring"] = "Saturday";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "周日";
        $field[$k]["fieldstring"] = "Sunday";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;


        $result['field'] = $field;
        if ($dataList) {
            $result['list'] = $dataList;
        } else {
            $result['list'] = array();
        }
        if (!$dataList) {
            $res = array('error' => '1', 'errortip' => $TimetableModel->errortip, 'result' => $result);
        } else {
            $res = array('error' => '0', 'errortip' => '获取信息', 'result' => $result);
        }

        ajax_return($res, $request['language_type']);

    }

    /**
     * 批量设置已审核
     */
    public function batchSetApprovedAction()
    {
        $request = Input('POST.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Gmc\CrmClientModel($request);
        $Model->batchSetApproved($request);

        $res = array();
        $res['field'] = array();
        $res['data'] = $Model->result;
        $result = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $res);
        ajax_return($result,$request['language_type']);
    }

    //可选的分配负责人列表
    function getGmccrmPersonListView(){
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Gmc\CrmClientModel($request);
        $result = $Model->getGmccrmPersonList($request);
        ajax_return($result,$request['language_type']);
    }

    //批量转为待分配名单/无意向/无效
    function changePrincipalClientApi(){
        $request = Input('POST.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        if (!isset($request['type']) || $request['type'] == '') {
            $res = array('error' => '1', 'errortip' => "流转类型为空", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        $Model = new \Model\Gmc\CrmClientModel($request);
        $Model->changePrincipalClientApi($request);

        $res = array();
        $res['field'] = array();
        $res['data'] = $Model->result;
        $result = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $res);
        ajax_return($result,$request['language_type']);
    }

    //获取无意向名单列表
    function getLossclientListView(){
        $request = Input('GET.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Gmc\CrmClientModel($request);
        $res = $Model->getLossclientList($request);

        $fieldstring = array('client_cnname','client_tag','client_sex','client_age','gmcschoolenter','school_shortname','client_birthday','client_tracestatusname', 'client_mobile',
            'channel_medianame', 'channel_name',  'client_createtime' );
        $fieldname = $this->LgArraySwitch(array('姓名','标签','性别','年龄','名单来源','所属学校','出生日期','客户状态','主要联系人',
           '渠道类型', '渠道明细','创建时间' ));
        if($request['client_ischaserlapsed'] == '1') {
            $fieldcustom = array("1", "1","1", "1", "1","1","1", "1", "1",
                "1", "1",  "1");
            $fieldshow = array("1", "1","1", "1", "1", "1", "1", "1", "1",
                 "1", "1", "1");
        }else{
            $fieldcustom = array("1", "1","1", "1", "1", "1", "1", "1", "1",
                 "1", "1",  "1");
            $fieldshow = array("1", "1","1", "1", "1", "1", "1", "1", "1",
                 "1", "1",  "1");
        }

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
            if ($field[$i]["fieldname"] == "client_cnname") {
                $field[$i]["ismethod"] = 1;
            }
            if ($field[$i]["fieldname"] == "client_tag" || $field[$i]["fieldname"] == "course_cnname") {
                $field[$i]["istag"] = 1;
            }
        }

        $resultarray = array();
        $resultarray['field'] = $field;
        $resultarray['fieldcustom'] = $res['fieldcustom']?$res['fieldcustom']:'';
        $resultarray['all_num'] = $res['all_num']>0?$res['all_num']:'0';
        $resultarray['list'] = is_array($res['list'])?$res['list']:array();

        $result = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $resultarray);
        ajax_return($result,$request['language_type']);
    }

    //获取 无效
    function getInvalidLossclientListView(){
        $request = Input('GET.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Gmc\CrmClientModel($request);
        $res = $Model->getInvalidLossclientList($request);

        $fieldstring = array('client_allname','client_tag','course_cnname','client_sex','client_age',
            'gmcschoolenter','school_shortname','client_birthday','client_tracestatusname', 'family_cnname',
            'client_mobile','invalidnote_reason','track_lasttrack_note','principal_name','activity_name', 'channel_medianame',
            'channel_name', 'shengshiqu' , 'examine_cnname' , 'examine_createtime' , 'client_createtime' );
        $fieldname = $this->LgArraySwitch(array('姓名','标签','意向课程','性别','年龄','名单来源','所属学校','出生日期','客户状态','主要联系人',
            '主要联系手机','无效原因','最后跟踪内容', '负责人', '来源活动', '渠道类型', '渠道明细','省市区','审核人','审核时间','创建时间' ));
        if($request['client_ischaserlapsed'] == '1') {
            $fieldcustom = array("1", "1","1", "1", "1","1","1", "1", "1", "1",
                "1","1", "1", "1", "1", "1", "1", "1", "1", "1", "1");
            $fieldshow = array("1", "1","1", "1", "1", "1", "1", "0", "0", "1",
                "1","1", "1", "0", "1", "1", "1", "0", "1", "1", "1");
        }else{
            $fieldcustom = array("1", "1","1", "1", "1", "1", "1", "1", "1", "1",
                "1", "1", "1", "1", "1", "1", "1", "1", "0", "0", "1");
            $fieldshow = array("1", "1","1", "1", "1", "0", "0", "0", "0", "1",
                "1","1", "1", "1", "1", "1", "1", "0", "0", "0", "1");
        }

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
            if ($field[$i]["fieldname"] == "client_cnname") {
                $field[$i]["ismethod"] = 1;
            }
            if ($field[$i]["fieldname"] == "client_tag" || $field[$i]["fieldname"] == "course_cnname") {
                $field[$i]["istag"] = 1;
            }
        }

        $resultarray = array();
        $resultarray['field'] = $field;
        $resultarray['fieldcustom'] = $res['fieldcustom']?$res['fieldcustom']:'';
        $resultarray['all_num'] = $res['all_num']>0?$res['all_num']:'0';
        $resultarray['list'] = is_array($res['list'])?$res['list']:array();

        $result = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $resultarray);
        ajax_return($result,$request['language_type']);
    }

//    //获取无效名单列表
//    function getVerifyInvalidClientListView(){
//        $request = Input('GET.', '', 'trim,addslashes');
//        $this->ThisVerify($request);//验证账户
//
//        $Model = new \Model\Gmc\CrmClientModel($request);
//        $res = $Model->getVerifyInvalidClientList($request);
//
//        $fieldstring = array('client_cnname','client_tag','course_cnname','client_sex','client_age','gmcschoolenter','school_shortname','client_birthday','client_tracestatusname', 'family_cnname','client_mobile','track_lasttrack_note','principal_name','activity_name', 'channel_medianame', 'channel_name', 'shengshiqu' , 'examine_cnname' , 'examine_createtime' , 'client_createtime' );
//        $fieldname = $this->LgArraySwitch(array('姓名','标签','意向课程','性别','年龄','名单来源','所属学校','出生日期','客户状态','主要联系人','主要联系手机','最后跟踪内容', '负责人', '来源活动', '渠道类型', '渠道明细','省市区','审核人','审核时间','创建时间' ));
//        if($request['client_ischaserlapsed'] == '1') {
//            $fieldcustom = array("1", "1","1", "1", "1","1","1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1");
//            $fieldshow = array("1", "1","1", "1", "1", "1", "1", "0", "0", "1", "1", "1", "0", "1", "1", "1", "0", "1", "1", "1");
//        }else{
//            $fieldcustom = array("1", "1","1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "0", "0", "1");
//            $fieldshow = array("1", "1","1", "1", "1", "0", "0", "0", "0", "1", "1", "1", "1", "1", "1", "1", "0", "0", "0", "1");
//        }
//
//        $field = array();
//        for ($i = 0; $i < count($fieldstring); $i++) {
//            $field[$i]["fieldname"] = trim($fieldstring[$i]);
//            $field[$i]["fieldstring"] = trim($fieldname[$i]);
//            $field[$i]["custom"] = trim($fieldcustom[$i]);
//            $field[$i]["show"] = trim($fieldshow[$i]);
//            if ($field[$i]["fieldname"] == "client_cnname") {
//                $field[$i]["ismethod"] = 1;
//            }
//            if ($field[$i]["fieldname"] == "client_tag" || $field[$i]["fieldname"] == "course_cnname") {
//                $field[$i]["istag"] = 1;
//            }
//        }
//
//        $resultarray = array();
//        $resultarray['field'] = $field;
//        $resultarray['fieldcustom'] = $res['fieldcustom']?$res['fieldcustom']:'';
//        $resultarray['all_num'] = $res['all_num']>0?$res['all_num']:'0';
//        $resultarray['list'] = is_array($res['list'])?$res['list']:array();
//
//        $result = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $resultarray);
//        ajax_return($result,$request['language_type']);
//    }

    //获取无效名单列表
    function getInvalidClientListView(){
        $request = Input('GET.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Gmc\CrmClientModel($request);
        $result = $Model->getInvalidClientList($request);
        ajax_return($result,$request['language_type']);
    }

    //招生名单线索 -- 修改招生名单线索
    function editClientAction(){
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Gmc\CrmClientModel($request);
        $Model->editClientOne($request);

        $res = array();
        $res['field'] = array();
        $res['data'] = $Model->result;
        $result = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $res);
        ajax_return($result,$request['language_type']);
    }

    //集团招生 -- 高管审核 无意向名单
    function lossExamineAction(){
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Gmc\CrmClientModel($request);
        $Model->lossExamine($request);
        $result = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($result,$request['language_type']);
    }
    //集团招生 -- 高管审核 无效向名单
    function lossInvalidExamineAction(){
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Gmc\CrmClientModel($request);
        $Model->lossInvalidExamineAction($request);
        $result = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($result,$request['language_type']);
    }

    //集团招生 -- 高管审核 无意向名单
    function dellossClientAction(){
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Gmc\CrmClientModel($request);
        $Model->dellossClient($request);

        $res = array();
        $res['field'] = array();
        $res['data'] = $Model->result;
        $result = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $res);
        ajax_return($result,$request['language_type']);
    }

    //集团招生 -- 批量添加至待拨打
    function addClientDialRecordAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $ClientModel = new \Model\Gmc\CrmClientModel($request);
        $ClientModel->addClientDialRecordAction($request);

        $res = array('error' => $ClientModel->error, 'errortip' => $ClientModel->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }
    //获取客户标签
    function getLabelApi()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        if (!$request['company_id']) {
            $res = array('error' => '1', 'errortip' => "公司id有误", 'result' => array());
            ajax_return($res,$request['language_type']);
        }
        $ClientModel = new \Model\Gmc\CrmClientModel($request);
        $dataList = $ClientModel->getLabelApi($request['company_id'], $request['client_id'],$request);

        $field = array();
        $field['label'] = '名单标签';
        $field['list'] = '集团标签';
        if ($dataList['label']) {
            $result['field'] = $field;
            $result['list'] = $dataList;
            ajax_return(array('error' => '0', 'errortip' => "获取成功", 'result' => $result),$request['language_type']);
        } else {
            $result['list'] = $dataList;
            $result['list'] = $dataList;
            ajax_return(array('error' => '1', 'errortip' => "暂无常用标签哦~", 'result' => $result),$request['language_type']);
        }
    }
    //获取意向课程
    function getIntentCourseApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $company_id = $request['company_id'];
        if (!$company_id) {
            $res = array('error' => '1', 'errortip' => "公司id有误", 'result' => array());
            ajax_return($res,$request['language_type']);
        }

        $ClientModel = new \Model\Gmc\CrmClientModel($request);
        $dataList = $ClientModel->getIntentCourse($request['company_id']);

        $field['course_id'] = "课程id";
        $field['course_cnname'] = "课程名";
        $field['course_branch'] = "课程别编号";
        $result['field'] = $field;
        if ($dataList) {
            $result['list'] = $dataList;
        } else {
            $result['list'] = array();
        }

        $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        ajax_return($res,$request['language_type']) ;
    }
    //直接修改名单标签
    function updateClientTagsAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        if (!$request['client_id']) {
            $res = array('error' => '1', 'errortip' => "名单id有误", 'result' => array());
            ajax_return($res,$request['language_type']);
        }
        if($request['label_list']){
            $tagarray = json_decode(stripslashes($request['label_list']),true);
            if(is_array($tagarray)) {
                $tagstr = implode(',', $tagarray);
            }else{
                $tagstr = $request['label_list'];
            }
            $data = array();
            $data['client_tag'] = $tagstr;
            $this->DataControl->updateData("crm_client", "client_id='{$request['client_id']}'", $data);

            $res = array('error' => 0, 'errortip' => '添加标签成功', 'result' => array());
            ajax_return($res, $request['language_type']);
        }else{
            $res = array('error' => '1', 'errortip' => "标签数据有误", 'result' => array());
            ajax_return($res,$request['language_type']);
        }

    }
    //获取集团活动
    function getGmcActivityApi()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        if (!$request['company_id']) {
            $res = array('error' => '1', 'errortip' => "公司id有误", 'result' => array());
            ajax_return($res,$request['language_type']);
        }
        $ClientModel = new \Model\Gmc\CrmClientModel($request);
        $dataList = $ClientModel->getGmcActivityApi($request);

        $field = array();
        $field['activity_id'] = '活动ID';
        $field['activity_name'] = '活动名称';
        if ($dataList) {
            $result['field'] = $field;
            $result['list'] = $dataList;
            ajax_return(array('error' => '0', 'errortip' => "获取成功", 'result' => $result),$request['language_type']);
        } else {
            $result['field'] = $field;
            $result['list'] = $dataList;
            ajax_return(array('error' => '1', 'errortip' => "暂无常用集团活动哦~", 'result' => $result),$request['language_type']);
        }
    }

    //意向星级列表
    public function getIntentionLevelListView()
    {
        $request = Input('GET.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $crmModel = new \Model\Crm\PublicModel();
        $crmModel->getIntentionLevelList($request['company_id']);

        $field = array();
        $field["intentlevel_starnum"] = "意向星级";
        $field["intentlevel_remark"] = "意向星级注释";

        $result = array();
        $result["field"] = $field;
        $result["list"] = $crmModel->result;

        ajax_return(array('error' => $crmModel->error, 'errortip' => $crmModel->errortip, 'result' => $result));
    }
    //获取负责人
    function getMarketerApi()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $company_id = Input("get.company_id");
        $marketer_id = Input('get.main_marketer_id');
        if (!$company_id) {
            $res = array('error' => '1', 'errortip' => "公司id有误", 'result' => array());
            ajax_return($res,$request['language_type']);
        }

        $Model= new \Model\Crm\PublicModel($request);

        $dataList =$Model->getMarketerList($company_id, $marketer_id,$request['school_id']);

        if ($dataList) {
            $result['list'] = $dataList;
        } else {
            $result['list'] = array();
        }

        $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        ajax_return($res,$request['language_type']);
    }
    //试听类型
    function getAuditionTypesApi(){
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ClassModel = new \Model\Crm\IntentionClientModel($request);
        $res = $ClassModel->getAuditionTypes($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $ClassModel->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }
    //接待人
    function getReceiverListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $where = " s.staffer_leave =0 and m.company_id='{$request['company_id']}' and  p.school_id ='{$request['school_id']}' and p.postbe_status =1 and postbe_iscrmuser = 1 ";
        $dataList = $this->DataControl->selectClear("SELECT m.marketer_id,m.marketer_name,s.staffer_enname,s.staffer_branch,t.post_name,p.postbe_isdefaultuser 
			FROM crm_marketer AS m
			LEFT JOIN smc_staffer AS s  ON m.staffer_id = s.staffer_id
			LEFT JOIN gmc_staffer_postbe AS p ON p.staffer_id = m.staffer_id 
			left join gmc_company_post as t ON p.post_id = t.post_id 
			WHERE {$where} and  p.postbe_isreceptionuser = 1  group by  m.marketer_id ");

        if ($dataList) {
            foreach ($dataList as &$value){
                $staffer_enname = $value['staffer_enname']?'('.$value['staffer_enname'].')':'';
                $value['marketer_name'] = $value['marketer_name'].$staffer_enname;
                $value['postbe_isdefaultusername'] = ($value['postbe_isdefaultuser']=='1')?"是":'否';
            }
        }

        $fieldname = array(
            'marketer_id',
            'marketer_name',
            'staffer_enname',
            'staffer_branch',
            'post_name',
            'postbe_isdefaultuser',
            'postbe_isdefaultusername' );
        $fieldstring = array('ID',
            '教师姓名',
            '教师英文名',
            '教师编号',
            '职务',
            '是否默认接待人  0 不是  1 是',
            '是否默认接待人'  );
        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["fieldname"] = trim($fieldname[$i]);
            $field[$i]["custom"] = 1;
            $field[$i]["show"] = 1;
            if ($fieldname[$i] == 'marketer_id') {
                $field[$i]["custom"] = 0;
                $field[$i]["show"] = 0;
            }
            if ($fieldname[$i] == 'staffer_enname') {
                $field[$i]["custom"] = 0;
                $field[$i]["show"] = 0;
            }
            if ($fieldname[$i] == 'postbe_isdefaultuser') {
                $field[$i]["custom"] = 0;
                $field[$i]["show"] = 0;
            }
        }

        if ($dataList) {
            $result['field'] = $field;
            $result['list'] = $dataList;
            ajax_return(array('error' => '0', 'errortip' => "获取成功", 'result' => $result),$request['language_type']);
        } else {
            $result['field'] = $field;
            $result['list'] = array();
            ajax_return(array('error' => '1', 'errortip' => "暂无职工", 'result' => $result),$request['language_type']);
        }

    }
    //删除客户的意向课程记录
    function delIntentionSourceApi()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $intention_id = Input('post.intention_id');
        if (!$intention_id) {
            $res = array('error' => '1', 'errortip' => "课程意向记录id有误", 'result' => array());
            ajax_return($res,$request['language_type']);
        }
        $Model= new \Model\crm\PublicModel($request);
        $bools =$Model->deleteIntentionSource($intention_id);
        if ($bools) {
            $res = array('error' => '0', 'errortip' => "删除成功", 'result' => array());
        } else {
            $res = array('error' => '1', 'errortip' => "删除失败", 'result' => array());
        }

        ajax_return($res,$request['language_type']);
    }
    //获取试听班级
    function getAuditionApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        if (!$request['school_id']) {
            $res = array('error' => '1', 'errortip' => "学校id错误", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        if (isset($request['audition_genre']) && $request['audition_genre'] !== '') {
            $smcData['audition_genre'] = $request['audition_genre'];
        } else {
            $res = array('error' => '1', 'errortip' => "请选择试听类型", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        $CourseModel = new \Model\Smc\CourseModel($request);
        if ($request['audition_genre'] == 0 || $request['audition_genre'] == 2) {
            $dataList = $CourseModel->getPucList($request,'gmc');
        } else {
            $dataList = $CourseModel->getAuditionHour($request,'gmc');
        }

        $fieldname = array('class_cnname', 'class_branch', 'class_appointnum', 'course_cnname', 'course_branch', 'hour_day', 'hour_time', 'classroom_cnname', 'staffer_cnname', 'class_num', 'hour_num');
        $fieldstring = array('班级名称', '班级编号', '可预约人数', '课程名', '课程别编号', '上课日期', '上课时间', '教室', '教师', '在班人数', '计划/已上课时');
        $fieldcustom = array('1', '1', "1", "1", "1", '1', '1', '1', '1', '1');
        $fieldshow = array('1', '1', "1", "1", "1", '1', '1', '1', '1', '1');
        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["fieldname"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }
        $result['field'] = $field;
        $result['list'] = $dataList['data'];
        $result['courselist'] = $dataList['courselist'];
        $res = array('error' => '0', 'errortip' => "获取试听课", 'result' => $result);
        ajax_return($res, $request['language_type']);
    }
    //获取学校的教师
    function getAuditionStafferApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        if (!$request['school_id']) {
            $res = array('error' => '1', 'errortip' => "学校id错误", 'result' => array());
            ajax_return($res, $request['language_type']);
        }

        $CourseModel = new \Model\Smc\CourseModel($request);
        $dataList = $CourseModel->getAuditionTeacher($request);
        $field[0]["fieldstring"] = 'staffer_id';
        $field[0]["fieldname"] = '教师id';
        $field[0]["custom"] = '0';
        $field[0]["show"] = '1';
        $field[1]["fieldstring"] = 'staffer_cnname';
        $field[1]["fieldname"] = '教师';
        $field[1]["custom"] = '0';
        $field[1]["show"] = '1';

        $result['field'] = $field;
        $result['list'] = $dataList;
        $res = array('error' => '0', 'errortip' => "获取学校的教师", 'result' => $result);
        ajax_return($res, $request['language_type']);

    }
    //获取 全部学校
    function getComAllSchoolApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Gmc\CrmClientModel($request);
        $result = $Model->getComAllSchoolApi($request);
        ajax_return($result, $request['language_type']);
    }
    //获取 全部渠道明细
    function getComAllChannelApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $company_id = $request['company_id'];
        if (!$company_id) {
            $res = array('error' => '1', 'errortip' => "公司id有误", 'result' => array());
            ajax_return($res,$request['language_type']);
        }
        $Model = new \Model\Gmc\CrmClientModel($request);
        $dataList = $Model->getComAllChannelApi($request);
        $field['channel_id'] = "渠道id";
        $field['channel_name'] = "渠道名称";
        if ($dataList) {
            $result['list'] = $dataList;
        } else {
            $result['list'] = array();
        }
        $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        ajax_return($res,$request['language_type']);
    }
    // 跟进  个人信息 渠道变更记录
    function getChannelTrackView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Crm\ClientModel($request);

        $result = $Model->getChannelTrack($request);
        ajax_return($result, $request['language_type']);
    }

    //集团柜询记录管理
    function getClientInviteListView()
    {
        $request = Input('GET.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Gmc\CrmClientModel($request);
        $res = $Model->getClientInviteList($request);

        $fieldstring = array('client_cnname','client_tag','course_cnname','client_sex','client_age','client_intention_level','invite_isvisitname','school_shortname','invite_genrename','invite_visittime','invite_novisitreason','invite_novisitreason_two','issignup','principal_name','receiver_name','client_patriarchname','client_mobile','coursecat_cnname','channel_medianame','channel_name','client_createtime');
        $fieldname = $this->LgArraySwitch(array('姓名', '标签','意向课程', '性别', '年龄','意向星级', '柜询状态', '柜询校区', '柜询类型', '柜询日期', '未到访原因', '改约原因', '报名状态', '负责人', '接待人', '主要联系人', '主要联系手机', '意向课程','渠道类型', '渠道明细', '名单创建时间'));
        $fieldcustom = array("1", "1","1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1");
        $fieldshow = array("1", "1","1", "0", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1","1","1",  "1", "1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
            if($field[$i]["fieldname"] == 'client_tag' || $field[$i]["fieldname"] == 'course_cnname'){
                $field[$i]["istag"] = "1";
            }
            if($field[$i]["fieldname"] == 'client_intention_level'){
                $field[$i]["islevel"] = "1";
            }
        }

        $resultarray = array();
        $resultarray['field'] = $field;
        $resultarray['fieldcustom'] = $res['fieldcustom']?$res['fieldcustom']:'';
        $resultarray['all_num'] = $res['all_num']>0?$res['all_num']:'0';
        $resultarray['list'] = is_array($res['list'])?$res['list']:array();
        $resultarray['TheSchool'] = is_array($res['TheSchool'])?$res['TheSchool']:array();

        $result = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $resultarray);
        ajax_return($result,$request['language_type']);
    }
    //集团试听记录管理
    function getClientAuditionListView()
    {
        $request = Input('GET.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Gmc\CrmClientModel($request);
        $res = $Model->getClientAuditionList($request);

        $fieldstring = array('client_cnname','client_tag','course_cnname','client_sex','client_age','client_intention_level','audition_isvisitname','school_shortname','audition_genrename','audition_visittime','class_cnname','audition_novisitreason','audition_novisitreason_two','issignup','principal_name','receiver_name','client_patriarchname','client_mobile','coursecat_cnname','channel_medianame','channel_name','client_createtime');
        $fieldname = $this->LgArraySwitch(array('姓名', '标签','意向课程', '性别', '年龄','意向星级', '试听状态', '试听校区', '试听类型', '试听日期', '试听班级', '未到访原因', '改约原因', '报名状态', '负责人', '接待人', '主要联系人', '主要联系手机', '意向课程','渠道类型', '渠道明细', '创建时间'));
        $fieldcustom = array("1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "0", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1","1","1",  "1", "1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
            if($field[$i]["fieldname"] == 'client_tag' || $field[$i]["fieldname"] == 'course_cnname'){
                $field[$i]["istag"] = "1";
            }
            if($field[$i]["fieldname"] == 'client_intention_level'){
                $field[$i]["islevel"] = "1";
            }
        }

        $resultarray = array();
        $resultarray['field'] = $field;
        $resultarray['fieldcustom'] = $res['fieldcustom']?$res['fieldcustom']:'';
        $resultarray['all_num'] = $res['all_num']>0?$res['all_num']:'0';
        $resultarray['list'] = is_array($res['list'])?$res['list']:array();
        $resultarray['TheSchool'] = is_array($res['TheSchool'])?$res['TheSchool']:array();
        $resultarray['TheClass'] = is_array($res['TheClass'])?$res['TheClass']:array();

        $result = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $resultarray);
        ajax_return($result,$request['language_type']);
    }
    //集团已登记学籍名单
    function getClientConversionListView()
    {
        $request = Input('GET.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Gmc\CrmClientModel($request);
        $res = $Model->getClientConversionList($request);

        $fieldstring = array('client_cnname','student_branch','client_sex','client_age','conversionlog_time','issignup','classpaytime','client_patriarchname','client_mobile','principal_name','channel_medianame','channel_name','activity_name','client_createtime');
        $fieldname = $this->LgArraySwitch(array('姓名', '学员编号', '性别', '年龄','登记日期', '报名状态', '报名日期', '主要联系人', '主要联系手机', '负责人', '渠道类型', '渠道明细', '来源活动', '创建时间'));
        $fieldcustom = array("1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "0", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1","1","1",  "1", "1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $resultarray = array();
        $resultarray['field'] = $field;
        $resultarray['fieldcustom'] = $res['fieldcustom']?$res['fieldcustom']:'';
        $resultarray['all_num'] = $res['all_num']>0?$res['all_num']:'0';
        $resultarray['list'] = is_array($res['list'])?$res['list']:array();

        $result = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $resultarray);
        ajax_return($result,$request['language_type']);
    }

    //获取柜询的类型
    function getInviteGenreApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $company_id = Input("get.company_id");
        if (!$company_id) {
            $res = array('error' => '1', 'errortip' => "公司id有误", 'result' => array());
            ajax_return($res, $request['language_type']);
        }

        $Model = new \Model\Crm\PublicModel($request);
        $dataList = $Model->getInviteGenreApi($company_id,0);
        if ($dataList) {
            $result['list'] = $dataList;
        } else {
            $result['list'] = array();
        }

        $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        ajax_return($res, $request['language_type']);
    }

    //获取柜询的类型
    function getIsvisterReasonApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $company_id = Input("get.company_id");
        if (!$company_id) {
            $res = array('error' => '1', 'errortip' => "公司id有误", 'result' => array());
            ajax_return($res, $request['language_type']);
        }

        $Model = new \Model\Crm\PublicModel($request);
        $dataList = $Model->getIsvisterReasonList($company_id);
        if ($dataList) {
            $result['list'] = $dataList;
        } else {
            $result['list'] = array();
        }

        $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        ajax_return($res, $request['language_type']);
    }

    //柜询到访或未到访设置
    function isvisitApi()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        if (!$request['invite_id']) {
            $res = array('error' => '1', 'errortip' => "id错误", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        if ($request['invite_isvisit'] == "") {
            $res = array('error' => '1', 'errortip' => "请填入到访状态", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        $Model = new \Model\Crm\ClientinviteModel($request);
        $Model->setIsVisit($request,'gmc');
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $Model->result);
        if ($request['invite_isvisit'] == '1') {
            $word = '柜询确认到访';
        } elseif ($request['invite_isvisit'] == '-1') {
            $word = '柜询确认未到访';
        }
        $this->addCrmWorkLog($request['company_id'], $request['school_id'], $request['marketer_id'], "柜询试听管理->柜询记录管理", $word, dataEncode($request));

        ajax_return($res, $request['language_type']);
    }

    //试听成功 或 取消试听
    function isauditionApi()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        if (!$request['audition_id']) {
            $res = array('error' => '1', 'errortip' => "id错误", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        if ($request['audition_isvisit'] == "") {
            $res = array('error' => '1', 'errortip' => "请填入试听状态", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        $Model = new \Model\Crm\ClientinviteModel($request);
        $Model->setIsaudition($request,'gmc');

        if ($request['audition_isvisit'] == '1') {
            $word = '试听确认到访';
        } elseif ($request['audition_isvisit'] == '-1') {
            $word = '试听确认取消';
        }
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $Model->result);
        $this->addCrmWorkLog($request['company_id'], $request['school_id'], $request['marketer_id'], "柜询试听管理->柜询记录管理", $word, dataEncode($request));

        ajax_return($res, $request['language_type']);
    }

    //首页 -- 跟踪提醒
    function remindApi()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
//        if(!isset($request['remind_starttime']) || $request['remind_starttime'] == '' || $request['remind_endtime'] == ''){
//            $res = array('error' => '1', 'errortip' => '必须选择查询时间段', 'result' => array());
//            ajax_return($res,$request['language_type']);
//        }
        //model
        $Model = new \Model\Gmc\CrmClientModel($request);
        $dataList = $Model->remindApi($request);

        $field = array();
        $key = 0;
        $field[$key]["fieldname"] = "client_cnname";
        $field[$key]["fieldstring"] = "姓名";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $field[$key]["ismethod"] = 1;
        $key++;

        $field[$key]["fieldname"] = "client_sex";
        $field[$key]["fieldstring"] = "性别";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "trackNum";
        $field[$key]["fieldstring"] = "跟踪次数";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "client_mobile";
        $field[$key]["fieldstring"] = "联系手机";
        $field[$key]["show"] = 0;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "client_tracestatus";
        $field[$key]["fieldstring"] = "客户状态";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "lastTrackTime";
        $field[$key]["fieldstring"] = "上次跟踪时间";
        $field[$key]["show"] = 0;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "remind_id";
        $field[$key]["fieldstring"] = "序号";
        $field[$key]["show"] = 0;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "remind_time";
        $field[$key]["fieldstring"] = "提醒时间";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "remind_remark";
        $field[$key]["fieldstring"] = "提醒备注";
        $field[$key]["show"] = 0;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "client_enname";
        $field[$key]["fieldstring"] = "客户英文名";
        $field[$key]["show"] = 0;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "todayTrackNum";
        $field[$key]["fieldstring"] = "是否已经跟踪";
        $field[$key]["show"] = 0;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "isTrack";
        $field[$key]["fieldstring"] = "是否跟踪";
        $field[$key]["show"] = 0;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "isTracknum";
        $field[$key]["fieldstring"] = "标签：跟踪名称";
        $field[$key]["show"] = 0;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "client_age";
        $field[$key]["fieldstring"] = "年龄";
        $field[$key]["show"] = 0;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "client_id";
        $field[$key]["fieldstring"] = "客户id";
        $field[$key]["show"] = 0;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "stuisloss";
        $field[$key]["fieldstring"] = "是否流失  0 没有  1 已流失  空是crm";
        $field[$key]["show"] = 0;
        $field[$key]["custom"] = 0;
        $key++;

        $result = array();
        if($dataList){
            $result["fieldcustom"] = 1;
            $result["field"] = $field;
            if(is_array($dataList["datalist"])) {
                $result["data"] = $dataList["datalist"];
            }else{
                $result["data"] = array();
            }
            if($result["data"]){
                $res = array('error' => '0', 'errortip' => '跟踪提醒','allnum' => $dataList['count'], 'result' => $result);
            }else{
                $res = array('error' => '1', 'errortip' => '本周暂无招生跟踪提醒','allnum' => 0, 'result' => $result);
            }
        }else{
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '本周暂无招生跟踪提醒', 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }

//    //拨打电话 -- 提醒拨打
//    function getDialRemindApi()
//    {
//        $request = Input('get.','','trim,addslashes');
//        $this->ThisVerify($request);//验证账户
//        //model
//        $Model = new \Model\Gmc\CrmClientModel($request);
//        $dataList = $Model->getDialRemindApi($request);
//
//        $field = array();
//        $key = 0;
//        $field[$key]["fieldname"] = "client_cnname";
//        $field[$key]["fieldstring"] = "姓名";
//        $field[$key]["show"] = 1;
//        $field[$key]["custom"] = 0;
//        $key++;
//
//        $field[$key]["fieldname"] = "client_sex";
//        $field[$key]["fieldstring"] = "性别";
//        $field[$key]["show"] = 1;
//        $field[$key]["custom"] = 0;
//        $key++;
//
//        $field[$key]["fieldname"] = "client_mobile";
//        $field[$key]["fieldstring"] = "联系手机";
//        $field[$key]["show"] = 0;
//        $field[$key]["custom"] = 0;
//        $key++;
//
//        $field[$key]["fieldname"] = "lastTrackTime";
//        $field[$key]["fieldstring"] = "上次跟踪时间";
//        $field[$key]["show"] = 0;
//        $field[$key]["custom"] = 0;
//        $key++;
//
//        $field[$key]["fieldname"] = "remind_id";
//        $field[$key]["fieldstring"] = "序号";
//        $field[$key]["show"] = 0;
//        $field[$key]["custom"] = 0;
//        $key++;
//
//        $field[$key]["fieldname"] = "client_img";
//        $field[$key]["fieldstring"] = "用户头像";
//        $field[$key]["show"] = 0;
//        $field[$key]["custom"] = 0;
//        $key++;
//
//        $result = array();
//        if($dataList){
//            $result["fieldcustom"] = 1;
//            $result["field"] = $field;
//            if(is_array($dataList["datalist"])) {
//                $result["data"] = $dataList["datalist"];
//            }else{
//                $result["data"] = array();
//            }
//            if($result["data"]){
//                $res = array('error' => '0', 'errortip' => '提醒拨打','allnum' => $dataList['count'], 'result' => $result);
//            }else{
//                $res = array('error' => '1', 'errortip' => '当天暂无招生提醒拨打','allnum' => 0, 'result' => $result);
//            }
//        }else{
//            $result["data"] = array();
//            $res = array('error' => '1', 'errortip' => '当天暂无招生提醒拨打', 'result' => $result);
//        }
//        ajax_return($res,$request['language_type']);
//    }

    //获取第三方外播记录
    function getThreeTrackApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Crm\ClientModel($request);
        $Model->getThreeTrackApi($request);
        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $Model->result), $request['language_type']);


//        $request = Input('get.', '', 'trim,addslashes');
//        $this->ThisVerify($request);//验证账户
//
//        $Model = new \Model\Gmc\CrmClientModel($request);
//        $dataList = $Model->getThreeTrackApi($request);
//
//        $result = array();
//        $result['list'] = $dataList;
//        if($dataList){
//            $res = array('error' => 0, 'errortip' => '获取外播记录成功', 'result' => $result);
//        }else{
//            $res = array('error' => 0, 'errortip' => '暂无外拨记录~', 'result' => array());
//        }
//        ajax_return($res, $request['language_type']);
    }

    //获取待拨打/已通话列表
    function getDialRecordApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Gmc\CrmClientModel($request);
        $dataList = $Model->getDialRecord($request);

        $result = array();
        $result['allnum'] = $dataList['allnum'];
        $result['allnums'] = $dataList['allnums'];
        $result['remindallnum'] = $dataList['remindallnum'];
        if ($dataList['list'] || $dataList['remindlist']) {
            $result['list'] = $dataList['list'];
            $result['remindlist'] = $dataList['remindlist'];
            $res = array('error' => 0, 'errortip' => '获取拨打列表成功', 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => 0, 'errortip' => '暂无拨打信息~', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //更新拨打状态
    function DialRecordApi()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Gmc\CrmClientModel($request);
        $Model->DialRecord($request);

        $res = array('error' => 0, 'errortip' => '更新拨打状态成功');
        ajax_return($res, $request['language_type']);
    }

    //更新电话接通状态
    function updateClientAnswerPhoneAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $ClientModel = new \Model\Crm\ClientModel($request);
        $ClientModel->updateClientAnswerPhone($request);

        $res = array('error' => $ClientModel->error, 'errortip' => $ClientModel->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    /**
     * 容联七陌-外呼接口模式
     */
    public function dialoutAction()
    {
        $request = Input('POST.', '', 'trim,addslashes');
//        $this->ThisVerify($request);//验证账户

        $fromExten = $request['fromExten'];//坐席工号
        $exten = $request['exten'];//被叫号码

        $Model = new \Model\Crm\SevenMoorModel();
        $result = $Model->dialout($fromExten,$exten);

        ajax_return(['error' => '0', 'errortip' => '已获取到返回值', 'result' =>$result]);
    }

    /**
     * 容联七陌-隐私模式(小号绑定接口)
     */
    public function midNumBindAction()
    {
        $request = Input('POST.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $midNum = $request['midNum'];
        $called = $request['called'];

        $Model = new \Model\Crm\SevenMoorModel();
        $result = $Model->midNumBind($midNum,$called);

        ajax_return(['error' => '0', 'errortip' => '已获取到返回值', 'result' =>$result]);
    }

    /**
     * 容联七陌-隐私模式(小号解绑接口)
     */
    public function midNumUnBindingAction()
    {
        $request = Input('POST.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $midNum = $request['midNum'];
        $mappingId = $request['mappingId'];

        $Model = new \Model\Crm\SevenMoorModel();
        $result = $Model->midNumUnBinding($midNum,$mappingId);

        ajax_return(['error' => '0', 'errortip' => '已获取到返回值', 'result' =>$result]);
    }


    //柜询改约的信息
    function getInviteOneApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $result = $this->DataControl->selectOne("
            SELECT
                ( CASE i.invite_genre WHEN '0' THEN '普通柜询' WHEN '1' THEN '能力测试' WHEN '2' THEN '推带到访' WHEN '3' THEN '主动到访' END ) AS invite_genre,
                t.coursetype_cnname,
                c.coursecat_cnname,
                k.track_id,
                k.marketer_name,
                i.invite_id,
                i.school_id,
                i.invite_visittime,
                i.invite_novisitreason,
                s.school_cnname
            FROM
                crm_client_invite AS i 
                left join smc_code_coursetype as t on t.coursetype_id = i.coursetype_id
                left join smc_code_coursecat as c on c.coursecat_id = i.coursecat_id
                left join crm_client_track as k on i.track_id = k.track_id
                left join smc_school as s on s.school_id = i.school_id
            WHERE
                i.invite_id = '{$request['invite_id']}'");

        $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        ajax_return($res, $request['language_type']);
    }

    //试听改约的信息
    function getAuditionOneApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $result = $this->DataControl->selectOne("
            SELECT
                ( CASE i.audition_genre WHEN '0' THEN '普通公开课试听' WHEN '1' THEN '插班试听' WHEN '2' THEN '试读公开课' END ) AS audition_genre,
                t.coursetype_cnname,
                c.coursecat_cnname,
                k.track_id,
                k.marketer_name,
                i.audition_visittime,
                i.course_id,
                i.class_id,
                i.hour_id,
                i.school_id,
                i.class_cnname,
                i.audition_genre as audition_genre_num,
                i.audition_id,
                i.audition_novisitreason,
                t.coursetype_id,
                c.coursecat_id,
                s.school_cnname
            FROM
                crm_client_audition AS i 
                left join smc_code_coursetype as t on t.coursetype_id = i.coursetype_id
                left join smc_code_coursecat as c on c.coursecat_id = i.coursecat_id
                left join crm_client_track as k on i.track_id = k.track_id
                left join smc_school as s on s.school_id = i.school_id
            WHERE
                i.audition_id = '{$request['audition_id']}'");

        $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        ajax_return($res, $request['language_type']);
    }


    // 改约柜询
    function changeInviteAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $marketerOne = $this->DataControl->selectOne("select marketer_id,staffer_id,marketer_name from  crm_marketer
        where staffer_id='{$request['staffer_id']}' and company_id='{$request['company_id']}' ");
        if (!$marketerOne) {
            $stamodel = new \Model\Gmc\modelTpl();
            $addmarkertOne = $stamodel->addStaffMarketerOne($request['staffer_id']);
            $marketerOne['staffer_id'] = $addmarkertOne['staffer_id'];
            $marketerOne['marketer_id'] = $addmarkertOne['marketer_id'];
            $marketerOne['marketer_name'] = $addmarkertOne['marketer_name'];
        }

        $oldt = $this->DataControl->getFieldOne("crm_client_track"," * ", "track_id = '{$request['track_id']}'");
        $data = array();
        $data['client_id'] = $oldt['client_id'];
        $data['school_id'] = $request['school_id'];

        $data['marketer_id'] = $marketerOne['marketer_id'];
        $data['marketer_name'] = $marketerOne['marketer_name'];

        $data['track_validinc'] = $oldt['track_validinc'];
        $data['object_code'] = $oldt['object_code'];
        $data['track_intention_level'] = $oldt['track_intention_level'];
        $data['coursetype_id'] = $oldt['coursetype_id'];
        $data['coursecat_id'] = $oldt['coursecat_id'];
        $data['track_linktype'] = $oldt['track_linktype'];
        $data['track_followuptype'] = $oldt['track_followuptype'];
        $data['track_followmode'] = $oldt['track_followmode'];
        $data['track_followuptime'] = $oldt['track_followuptime'];

        $data['track_visitingtime'] = $request['time'];

        $data['track_note'] = $oldt['track_note']."-改约";
        $data['track_state'] = $oldt['track_state'];
        $data['track_type'] = 1;
//        $data['track_isschoolread'] = $oldt['track_isschoolread'];
//        $data['track_initiative'] = $oldt['track_initiative'];
//        $data['track_isschooltmk'] = $oldt['track_isschooltmk'];
        $data['track_isactive'] = 0;
        $data['track_isgmcactive'] = 1;
        $data['update_note'] = $oldt['update_note'];
        $data['track_createtime'] = time();
        $tid = $this->DataControl->insertData("crm_client_track",$data);

        if($request['invite_id']){
            $old = $this->DataControl->getFieldOne("crm_client_invite"," * ","invite_id = '{$request['invite_id']}'");
            $data = array();
            $data['company_id'] = $request['company_id'];
            $data['school_id'] = $request['school_id'];

            $data['marketer_id'] = $marketerOne['marketer_id'];

            $data['receiver_name'] = $old['receiver_name'];
            $data['client_id'] = $old['client_id'];
            $data['track_id'] = $tid;
            $data['coursetype_id'] = $old['coursetype_id'];
            $data['coursecat_id'] = $old['coursecat_id'];
            $data['invite_level'] = $old['invite_level'];
            $data['invite_genre'] = $old['invite_genre'];
//            $data['invite_isschooltmk'] = $old['invite_isschooltmk'];
            $data['invite_isvisit'] = '0';
            $data['invite_visittime'] = $request['time'];
            $data['invite_daytime'] = $old['invite_daytime'];
            $data['invite_createtime'] = time();
            $this->DataControl->insertData("crm_client_invite",$data);

            $oldInvite=array();
            $oldInvite['invite_isvisit']=2;
            $oldInvite['invite_updatetime'] = time();
            $oldInvite['invite_novisitreason'] = $request['invite_novisitreason'];
            $this->DataControl->updateData("crm_client_invite","invite_id = '{$request['invite_id']}'",$oldInvite);

            $Model = new \Model\Crm\ClientModel();
            $Model->addSchoolEnter($old['client_id'], $request['school_id'], $request['company_id']);
        }else{
            $old = $this->DataControl->getFieldOne("crm_client_audition"," * ","audition_id = '{$request['audition_id']}'");

            $data = array();
            $data['company_id'] = $request['company_id'];
            $data['school_id'] = $request['school_id'];
            $data['marketer_id'] = $marketerOne['marketer_id'];

            $data['receiver_name'] = $old['receiver_name'];
            $data['client_id'] = $old['client_id'];
            $data['track_id'] = $tid;
            $data['object_code'] = $old['object_code'];
            $data['coursetype_id'] = $old['coursetype_id'];
            $data['coursecat_id'] = $old['coursecat_id'];

            $data['class_id'] = $request['class_id'];
            $data['class_cnname'] = $request['class_cnname'];

            $data['course_id'] = $request['course_id'];
            $data['hour_id'] = $request['hour_id'];

            $data['audition_genre'] = $old['audition_genre'];
            $data['audition_isschooltmk'] = $old['audition_isschooltmk'];
            $data['audition_isvisit'] = 0;
            $data['audition_visittime'] = $request['time'];
            $data['outthree_bookid'] = $old['outthree_bookid'];
            $data['audition_createtime'] = time();
            $this->DataControl->insertData("crm_client_audition",$data);

            $oldAudition=array();
            $oldAudition['audition_isvisit']=2;
            $oldAudition['audition_updatetime'] = time();
            $oldAudition['audition_novisitreason'] = $request['audition_novisitreason'];
            $this->DataControl->updateData("crm_client_audition","audition_id = '{$request['audition_id']}'",$oldAudition);

            $Model = new \Model\Crm\ClientModel();
            $Model->addSchoolEnter($old['client_id'], $request['school_id'], $request['company_id']);
        }


        $res = array('error' => '0', 'errortip' => '改约成功', 'result' => array());
        ajax_return($res, $request['language_type']);
    }


    //结尾魔术函数
    function __destruct()
    {

    }

}
