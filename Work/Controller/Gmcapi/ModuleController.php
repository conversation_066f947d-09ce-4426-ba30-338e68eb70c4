<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/13
 * Time: 23:25
 */

namespace Work\Controller\Gmcapi;


class ModuleController extends viewTpl
{
    public $u;
    public $t;
    public $c;
    public $visitType = "api";
    public $Model;

    //预加载处理类
    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
    }

    function ThisVerify($request)
    {
        $paramArray = array();
        $paramArray['staffer_id'] = $request['staffer_id'];
        $paramArray['company_id'] = $request['company_id'];
        $paramArray['token'] = $request['token'];
        if(!$this->UserLimit($paramArray)){
            $result = array();
            $result["list"] = array();
            $result["tokeninc"] = "0";
            $res = array('error' => '1', 'errortip' => "用户token失效", 'result' => $result);
            ajax_return($res,$request['language_type']);
        }
    }

    //权限列表
    function HomeView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model =  new \Model\Gmc\ModuleModel($request);

        $result = $this->Model->getModuleList($request);


    }

    //根据职务权限列表
    function getPowerListView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\ModuleModel($request);

        $result = $this->Model->getPowerList($request);

    }

    //查看某集团角色权限
    function getPostrolePowerApi()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\ModuleModel($request);

        $result = $this->Model->getPostrolePowerApi($request);

        ajax_return($result,$request['language_type']);
    }

    //查看校园角色权限
    function getPostpartPowerApi()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\ModuleModel($request);

        $result = $this->Model->getPostpartPowerApi($request);

        ajax_return($result,$request['language_type']);
    }

    //添加集团角色权限
    function addPowerAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model =  new \Model\Gmc\ModuleModel($request);

        $result = $this->Model->addPowerAction($request);

        ajax_return($result,$request['language_type']);

    }

    //添加校园角色权限
    function addScPowerAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model =  new \Model\Gmc\ModuleModel($request);

        $result = $this->Model->addScPowerAction($request);

        ajax_return($result,$request['language_type']);

    }

    //添加员工常用功能
    function addCommonmoduleAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model =  new \Model\Gmc\ModuleModel($request);

        $result = $this->Model->addCommonmoduleAction($request);

        ajax_return($result,$request['language_type']);

    }

    //查看员工常用功能
    function getCommonmoduleView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\ModuleModel($request);

        $result = $this->Model->getCommonmodule($request);

        ajax_return($result,$request['language_type'],1);
    }

    //结尾魔术函数
    function __destruct()
    {

    }
}
