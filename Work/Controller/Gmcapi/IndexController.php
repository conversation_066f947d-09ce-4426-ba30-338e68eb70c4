<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2018/9/1
 * Time: 11:24
 */

namespace Work\Controller\Gmcapi;


class IndexController extends viewTpl{
    public $u;
    public $t;
    public $c;

    function __construct() {
        parent::__construct ();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
    }

    //一级页面演示
    function HomeView(){
        exit("此为一级页面演示效果");
    }

    //一级页面api演示
    function testApi(){
        exit("此为一级页面api演示效果");
    }

    //二级页面演示
    function pageView(){
        exit("此为二级页面演示效果");
    }

    //二级页面api演示
    function pageTestApi(){
        exit("此为二级页面api演示效果");
    }

    function pageThreeView(){
        exit("此为三级页面演示效果");
    }

    function pageThreeTestApi(){
        exit("此为三级页面api演示效果");
    }

    function commonView(){
        exit('{"error":"1","errortip":"正确或错误的提示","result":{"fieldcustom":"0","field":{"name":"姓名","url":"地址"},"list":[{"name":"Google","url":"http://www.google.com"},{"name":"Baidu","url":"http://www.baidu.com"},{"name":"SoSo","url":"http://www.SoSo.com"}]}}');
    }

    function fieldcustomView(){
        exit('{"error":"1","errortip":"正确或错误的提示","result":{"fieldcustom":"1","field":[{"fieldname":"姓名","fieldstring":"name","show":"1","custom":"0"},{"fieldname":"地址","fieldstring":"url","show":"0","custom":"1"}],"list":[{"name":"Google","url":"http://www.google.com"},{"name":"Baidu","url":"http://www.baidu.com"},{"name":"SoSo","url":"http://www.SoSo.com"}]}}');
    }

    function generalApi(){
        exit('{
    "error": "1",
    "errortip": "正确或错误的提示",
    "result": {
        "bakurl": "http://www.bejson.com/",
        "tipstyle": "errorTipstyle"
    }
}');
    }

    function advancedApi(){
        exit('{
    "error": "1",
    "errortip": "正确或错误的提示",
    "result": {
        "bakfunction": "playList",
        "parameter": {
            "demo1": "1",
            "demo2": "2"
        }
    }
}');
    }
}