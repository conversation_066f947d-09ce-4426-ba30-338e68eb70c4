<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/13
 * Time: 23:25
 */

namespace Work\Controller\Gmcapi;


class StockController extends viewTpl
{
    public $u;
    public $t;
    public $c;
    public $visitType = "api";
    public $Model;

    //预加载处理类
    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
    }

    function ThisVerify($request)
    {
        $paramArray = array();
        $paramArray['staffer_id'] = $request['staffer_id'];
        $paramArray['company_id'] = $request['company_id'];
        $paramArray['token'] = $request['token'];
        if(!$this->UserLimit($paramArray)){
            $result = array();
            $result["list"] = array();
            $result["tokeninc"] = "0";
            $res = array('error' => '1', 'errortip' => "用户token失效", 'result' => $result);
            ajax_return($res,$request['language_type']);
        }
    }


    //仓库管理列表
    function HomeView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\StockModel($request);

        $result = $this->Model->getWarehouseList($request);
        ajax_return($result,$request['language_type']);
    }

    //添加仓库
    function addWarehouseAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\StockModel($request);

        $result = $this->Model->addWarehouseAction($request);
        ajax_return($result,$request['language_type']);
    }

    //编辑仓库
    function updateWarehouseAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\StockModel($request);

        $result = $this->Model->updateWarehouseAction($request);
        ajax_return($result,$request['language_type']);
    }

    //删除仓库
    function delWarehouseAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\StockModel($request);

        $result = $this->Model->delWarehouseAction($request);
        ajax_return($result,$request['language_type']);
    }

    //货品类别列表
    function prodtypeView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\StockModel($request);

        $result = $this->Model->getProdtypeList($request);
        ajax_return($result,$request['language_type']);
    }

    //编辑货品类别
    function updateProdtypeAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\StockModel($request);

        $result = $this->Model->updateProdtypeAction($request);
        ajax_return($result,$request['language_type']);
    }

    //添加货品类别
    function addProdtypeAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\StockModel($request);

        $result = $this->Model->addProdtypeAction($request);
        ajax_return($result,$request['language_type']);
    }

    //删除货品类别
    function delProdtypeAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\StockModel($request);

        $result = $this->Model->delProdtypeAction($request);
        ajax_return($result,$request['language_type']);
    }

    //出入库类型列表
    function warehousetypeView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\StockModel($request);

        $result = $this->Model->getWarehousetypeList($request);
        ajax_return($result,$request['language_type']);
    }

    //添加出入库类型
    function addWarehousetypeAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\StockModel($request);

        $result = $this->Model->addWarehousetypeAction($request);
        ajax_return($result,$request['language_type']);
    }

    //编辑出入库类型
    function updateWarehousetypeAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\StockModel($request);

        $result = $this->Model->updateWarehousetypeAction($request);
        ajax_return($result,$request['language_type']);
    }

    //删除出入库类型
    function delWarehousetypeAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\StockModel($request);

        $result = $this->Model->delWarehousetypeAction($request);
        ajax_return($result,$request['language_type']);
    }

    //CRM用户角色列表
    function postroleView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\CrmModel($request);

        $result = $this->Model->getPostroleList($request);
        ajax_return($result,$request['language_type']);
    }

    //添加CRM用户角色
    function addPostroleAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\CrmModel($request);

        $result = $this->Model->addPostroleAction($request);
        ajax_return($result,$request['language_type']);
    }

    //编辑CRM用户角色
    function updatePostroleAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\CrmModel($request);

        $result = $this->Model->updatePostroleAction($request);
        ajax_return($result,$request['language_type']);
    }

    //删除CRM用户角色
    function delPostroleAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\CrmModel($request);

        $result = $this->Model->delPostroleAction($request);
        ajax_return($result,$request['language_type']);
    }

    //用户跟踪内容模板列表
    function tracenoteView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\CrmModel($request);

        $result = $this->Model->getTracenoteList($request);
        ajax_return($result,$request['language_type']);
    }

    //添加用户跟踪内容模板
    function addTracenoteAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\CrmModel($request);

        $result = $this->Model->addTracenoteAction($request);
        ajax_return($result,$request['language_type']);
    }

    //编辑用户跟踪内容模板
    function updateTracenoteAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\CrmModel($request);

        $result = $this->Model->updateTracenoteAction($request);
        ajax_return($result,$request['language_type']);
    }

    //删除用户跟踪内容模板
    function delTracenoteAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\CrmModel($request);

        $result = $this->Model->delTracenoteAction($request);
        ajax_return($result,$request['language_type']);
    }

    //获取货品类型列表
    function getGoodsTypeListView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $this->Model = new \Model\Gmc\StockModel($request);

        $result = $this->Model->getGoodsTypeList($request);
        ajax_return($result,$request['language_type']);
    }

    //获取货品列表
    function getGoodsListView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\StockModel($request);

        $result = $this->Model->getGoodsList($request);
        ajax_return($result,$request['language_type']);
    }

    //获取货品列表
    function getGoodsListsView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\StockModel($request);

        $result = $this->Model->getGoodsLists($request);
        ajax_return($result,$request['language_type']);
    }

    //添加货品
    function addGoodsAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\StockModel($request);

        $result = $this->Model->addGoodsAction($request);
        ajax_return($result,$request['language_type']);
    }

    //编辑货品
    function updateGoodsAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\StockModel($request);

        $result = $this->Model->updateGoodsAction($request);
        ajax_return($result,$request['language_type']);
    }

    //删除货品
    function delGoodsAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\StockModel($request);

        $result = $this->Model->delGoodsAction($request);
        ajax_return($result,$request['language_type']);
    }

    //上/下架商品
    function topGoodsAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\StockModel($request);

        $result = $this->Model->topGoodsAction($request);
        ajax_return($result,$request['language_type']);
    }

    function updateShopSaleAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Gmc\StockModel($request);
        $res=$Model->updateShopSale($request);
        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => $Model->oktip, 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }

    function updateIntegralSaleAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Gmc\StockModel($request);
        $res=$Model->updateIntegralSale($request);
        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => $Model->oktip, 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }

    //货品类型列表
    function prodclassListView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\StockModel($request);

        $result = $this->Model->prodclassList($request);
        ajax_return($result,$request['language_type']);
    }

    //编辑货品类型
    function updateProdclassAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\StockModel($request);

        $result = $this->Model->updateProdclassAction($request);
        ajax_return($result,$request['language_type']);
    }

    //添加货品类型
    function addProdclassAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\StockModel($request);

        $result = $this->Model->addProdclassAction($request);
        ajax_return($result,$request['language_type']);
    }

    //删除货品类型
    function delProdclassAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\StockModel($request);

        $result = $this->Model->delProdclassAction($request);
        ajax_return($result,$request['language_type']);
    }

    //货品类型列表
    function getProdClassView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\StockModel($request);

        $result = $this->Model->getProdClass($request);
        ajax_return($result,$request['language_type']);
    }

    function DockingAction(){
        $request = Input('get.','','trim,addslashes');
//        $this->ThisVerify($request);//验证账户

        $sendinfo ="<StrXMLPara><FBrNo>06101</FBrNo>"
            ."<PROD_ID></PROD_ID>"
            ."<FBeginDate>2023-01-01</FBeginDate>"
            ."<FEndDate>2025-01-01</FEndDate>";

        $sendinfo .="</StrXMLPara>";

        $sendbak = request_by_curl("http://***********:55520/JDBBillWebservice/Webservice1.asmx/MakeAllMaterial","strXMLPara={$sendinfo}","POST",array());

        $xml = simplexml_load_string($sendbak);
        $data = json_decode(json_encode($xml),TRUE);

        $StingConter = new \Websting();
        $Success = $StingConter->getContent($sendbak,'&lt;Success&gt;','&lt;/Success&gt;') ;
        $item = $StingConter->getContentarray($data[0],'<Item>','</Item>') ;


//        var_dump($Success);die();
        if($Success){
            foreach ($item as $vals){
                $data = array();
                $data['goods_outpid'] = $StingConter->getContentarray($vals,'<PROD_ID>','</PROD_ID>')[0] ;
                $data['goods_pid'] = $StingConter->getContentarray($vals,'<PROD_ID>','</PROD_ID>')[0] ;
                $name = str_replace(']]>', '', str_replace('<![CDATA[', '', $StingConter->getContentarray($vals,'<PROD_NAME>','</PROD_NAME>')[0]));
                $data['prodtype_code'] = $StingConter->getContentarray($vals,'<GATEGORY_CODE>','</GATEGORY_CODE>')[0] ;
                $data['goods_barcode'] = $StingConter->getContentarray($vals,'<FBarCode>','</FBarCode>')[0] ;
                $data['goods_pathways'] = $StingConter->getContentarray($vals,'<TongLu>','</TongLu>')[0] ;
                $data['goods_issale'] = '1';
                $data['company_id'] = $request['company_id'];
                if($this->DataControl->getFieldOne("erp_goods","goods_id","goods_outpid = '{$data['goods_outpid']}' and company_id = '{$request['company_id']}'")){
                    $data['goods_updatetime'] = time();
                    $data['goods_backname'] = addslashes($name);
                    $this->DataControl->updateData("erp_goods","goods_outpid = '{$data['goods_outpid']}' and company_id = '{$request['company_id']}'",$data);
                }else{
                    $data['goods_createtime'] = time();
                    $data['goods_cnname'] = addslashes($name);
                    $data['goods_backname'] = addslashes($name);
                    $this->DataControl->insertData("erp_goods",$data);
                }
            }
            ajax_return(array('error' => 0,'errortip' => "更新成功！"));
        }else{
            ajax_return(array('error' => 0,'errortip' => "更新失败！"));

        }

    }

    function PriceAction(){
        $request = Input('get.','','trim,addslashes');
//        $this->ThisVerify($request);//验证账户

//        $a = real_ip();
//        var_dump($a);die;

        $sendinfo ="<StrXMLPara><FBrNo>06101</FBrNo>"
            ."<PROD_ID></PROD_ID>";

        $sendinfo .="</StrXMLPara>";

        $sendbak = request_by_curl("http://***********:55520/JDBBillWebservice/Webservice1.asmx/MakeAllPrice","strXMLPara={$sendinfo}","POST",array());

        $xml = simplexml_load_string($sendbak);
        $data = json_decode(json_encode($xml),TRUE);

//        print_r($data);die();
//
//
//        $p = xml_parser_create();
//        xml_parse_into_struct($p, $data[0], $vals, $index);
//        xml_parser_free($p);
//        print_r($vals);die();


        $StingConter = new \Websting();
        $Success = $StingConter->getContent($sendbak,'&lt;Success&gt;','&lt;/Success&gt;') ;
        $item = $StingConter->getContentarray($data[0],'<Item>','</Item>') ;


//        var_dump($item);die();

        if($Success){
            foreach ($item as $vals){
                $data = array();
                $data['goods_originalprice'] = round($StingConter->getContentarray($vals,'<PRICE>','</PRICE>')[0],2);


                $data['goods_outpid'] = $StingConter->getContentarray($vals,'<PROD_ID>','</PROD_ID>')[0] ;
                $a = $this->DataControl->getFieldOne("erp_goods","goods_cloudprice,goods_vipprice","goods_outpid = '{$data['goods_outpid']}' and company_id = '8888'");
                if($a['goods_cloudprice'] == $a['goods_vipprice']){
                    $data['goods_originalprice'] = round($StingConter->getContentarray($vals,'<PRICE>','</PRICE>')[0],2);
                    $data['goods_vipprice'] = round($StingConter->getContentarray($vals,'<PRICE>','</PRICE>')[0],2);
                    $data['goods_cloudprice'] = round($StingConter->getContentarray($vals,'<PRICE>','</PRICE>')[0],2);
                }else{
                    $data['goods_originalprice'] = round($StingConter->getContentarray($vals,'<PRICE>','</PRICE>')[0],2);
                    $data['goods_vipprice'] = round($StingConter->getContentarray($vals,'<PRICE>','</PRICE>')[0],2);
                }

//                print_r($data);die();

                $this->DataControl->updateData("erp_goods","goods_outpid = '{$data['goods_outpid']}' and company_id = '{$request['company_id']}'",$data);

            }
            ajax_return(array('error' => 0,'errortip' => "更新成功！"));
        }else{
            ajax_return(array('error' => 0,'errortip' => "更新失败！"));

        }

    }

    //获取集团商务产品列表
    function getApppropermisListView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\StockModel($request);

        $result = $this->Model->getApppropermisList($request);
        ajax_return($result,$request['language_type']);
    }

    //添加集团商务产品
    function addApppropermisAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\StockModel($request);

        $result = $this->Model->addApppropermisAction($request);
        ajax_return($result,$request['language_type']);
    }

    //编辑集团商务产品
    function updateApppropermisAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\StockModel($request);

        $result = $this->Model->updateApppropermisAction($request);
        ajax_return($result,$request['language_type']);
    }

    //删除集团商务产品
    function delApppropermisAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\StockModel($request);

        $result = $this->Model->delApppropermisAction($request);
        ajax_return($result,$request['language_type']);
    }

    //获取企业信息列表
    function getCompaniesListView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\StockModel($request);

        $result = $this->Model->getCompaniesList($request);
        ajax_return($result,$request['language_type']);
    }

    //添加企业信息
    function addCompaniesAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\StockModel($request);

        $result = $this->Model->addCompaniesAction($request);
        ajax_return($result,$request['language_type']);
    }

    //编辑企业信息
    function updateCompaniesAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\StockModel($request);

        $result = $this->Model->updateCompaniesAction($request);
        ajax_return($result,$request['language_type']);
    }

    //删除企业信息
    function delCompaniesAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\StockModel($request);

        $result = $this->Model->delCompaniesAction($request);
        ajax_return($result,$request['language_type']);
    }

    //采购管理列表（校业助）
    function getScAssistantListView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\StockModel($request);

        $result = $this->Model->getScAssistantList($request);
        ajax_return($result,$request['language_type']);
    }

    //采购货品明细
    function getGoodsDetailListView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\StockModel($request);

        $result = $this->Model->getGoodsDetailList($request);
        ajax_return($result,$request['language_type']);
    }

    //处理详情
    function getGoodsTracksListView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\StockModel($request);

        $result = $this->Model->getGoodsTracksList($request);
        ajax_return($result,$request['language_type']);
    }

    //调拨分校列表
    function getApplytypeApi()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\StockModel($request);

        $result = $this->Model->getApplytypeApi($request);
        ajax_return($result,$request['language_type']);
    }

    //调拨分校列表
    function getChangeGoodsListView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\StockModel($request);

        $result = $this->Model->getChangeGoodsList($request);
        ajax_return($result,$request['language_type']);
    }

    //审核学校采购订单
    function ExamineScAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\StockModel($request);

        $result = $this->Model->ExamineScAction($request);
        ajax_return($result,$request['language_type']);
    }

    //校财务输入流水号
    function paynumberAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\StockModel($request);

        $result = $this->Model->paynumberAction($request);
        ajax_return($result,$request['language_type']);
    }

    //创建入库单
    function CreateBeinorderAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\StockModel($request);

        $result = $this->Model->CreateBeinorderAction($request);
        ajax_return($result,$request['language_type']);
    }

    //调拨商品
    function ChangeGoodsAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\StockModel($request);

        $result = $this->Model->ChangeGoodsAction($request);
        ajax_return($result,$request['language_type']);
    }

    function DockingTypeAction(){
        $request = Input('get.','','trim,addslashes');
//        $this->ThisVerify($request);//验证账户

        $sendinfo ="<StrXMLPara><FBrNo>06101</FBrNo>"
            ."<PROD_ID></PROD_ID>"
            ."<FBeginDate>2000-04-11</FBeginDate>"
            ."<FEndDate>2023-01-01</FEndDate>";

        $sendinfo .="</StrXMLPara>";
        /*echo "<!--";
        echo $sendinfo;
        echo "-->";*/

        $sendbak = request_by_curl("http://***********:55520/JDBBillWebservice/Webservice1.asmx/MakeAllMaterial","strXMLPara={$sendinfo}","POST",array());

        $xml = simplexml_load_string($sendbak);
        $data = json_decode(json_encode($xml),TRUE);

        $StingConter = new \Websting();
        $Success = $StingConter->getContent($sendbak,'&lt;Success&gt;','&lt;/Success&gt;') ;
        $item = $StingConter->getContentarray($data[0],'<Item>','</Item>') ;
        if($Success){
            foreach ($item as $vals){
                $data = array();
                $data['prodtype_code'] = $StingConter->getContentarray($vals,'<GATEGORY_CODE>','</GATEGORY_CODE>')[0];
                $data['prodtype_name'] = $StingConter->getContentarray($vals,'<GATEGORY_DESC>','</GATEGORY_DESC>')[0];
                $data['kdfclass'] = '06101';
                $data['company_id'] = $request['company_id'];
                if($this->DataControl->getFieldOne("smc_code_prodtype","prodtype_id","prodtype_code = '{$data['prodtype_code']}' and company_id = '{$request['company_id']}'")){
                    $this->DataControl->updateData("smc_code_prodtype","prodtype_code = '{$data['prodtype_code']}' and company_id = '{$request['company_id']}'",$data);
                }else{
                    $this->DataControl->insertData("smc_code_prodtype",$data);
                }
            }
            ajax_return(array('error' => 0,'errortip' => "更新成功！"));
        }else{
            ajax_return(array('error' => 0,'errortip' => "更新失败！"));

        }

    }

    //创建采购活动
    function addActivityBuyAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\StockModel($request);

        $result = $this->Model->addActivityBuyAction($request);
        ajax_return($result,$request['language_type']);
    }

    //获取活动商品列表
    function getActivityBuyGoodsListView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\StockModel($request);

        $result = $this->Model->getActivityBuyGoodsList($request);
        ajax_return($result,$request['language_type']);
    }

    //选择货品列表
    function getChooseGoodsListView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\StockModel($request);

        $result = $this->Model->getChooseGoodsList($request);
        ajax_return($result,$request['language_type']);
    }

    //添加活动商品
    function addActivityBuyGoodsAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\StockModel($request);

        $result = $this->Model->addActivityBuyGoodsAction($request);
        ajax_return($result,$request['language_type']);
    }

    //获取活动适用学校列表
    function getActivityBuySchoolListView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\StockModel($request);

        $result = $this->Model->getActivityBuySchoolList($request);
        ajax_return($result,$request['language_type']);
    }

    //添加活动学校
    function addActivityBuySchoolAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\StockModel($request);

        $result = $this->Model->addActivityBuySchoolAction($request);
        ajax_return($result,$request['language_type']);
    }

    //删除活动商品
    function delActivityBuyGoodsAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\StockModel($request);

        $result = $this->Model->delActivityBuyGoodsAction($request);
        ajax_return($result,$request['language_type']);
    }

    //删除活动学校
    function delActivityBuySchoolAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\StockModel($request);

        $result = $this->Model->delActivityBuySchoolAction($request);
        ajax_return($result,$request['language_type']);
    }

    //获取采购活动列表
    function getActivityBuyListView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\StockModel($request);

        $result = $this->Model->getActivityBuyList($request);
        ajax_return($result,$request['language_type']);
    }

    //删除采购活动
    function delActivityBuyAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\StockModel($request);

        $result = $this->Model->delActivityBuyAction($request);
        ajax_return($result,$request['language_type']);
    }

    //编辑采购活动
    function updateActivityBuyAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\StockModel($request);

        $result = $this->Model->updateActivityBuyAction($request);
        ajax_return($result,$request['language_type']);
    }

    //获取学校列表
    function getSchoollistView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\StockModel($request);

        $result = $this->Model->getSchoollist($request);
        ajax_return($result,$request['language_type']);
    }

    //获取学校列表
    function getSchoolClasslistView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\StockModel($request);

        $result = $this->Model->getSchoolClasslist($request);
        ajax_return($result,$request['language_type']);
    }


    //获取学校列表
    function getRepertoryListView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\StockModel($request);

        $result = $this->Model->getRepertoryList($request);
        ajax_return($result,$request['language_type']);
    }

    //采购管理列表
    function getProGoodsListView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Gmc\StockModel($request);

        $result = $Model->getProGoodsList($request);
        ajax_return($result,$request['language_type']);
    }

    //集团销货单列表
    function getSalesorderListView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);

        $Model = new \Model\Gmc\StockModel($request);
        $res = $Model->getSalesorderList($request);

        $field = array();
        $k = 0;

        $field[$k]["fieldname"] = "salesorder_id";
        $field[$k]["fieldstring"] = "销货单id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "school_cnname";
        $field[$k]["fieldstring"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "school_branch";
        $field[$k]["fieldstring"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "salesorder_pid";
        $field[$k]["fieldstring"] = "销货单号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "salesorder_from";
        $field[$k]["fieldstring"] = "销货类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        if($request['salesorder_from'] == '5'){
            $field[$k]["fieldname"] = "activitybuy_name";
            $field[$k]["fieldstring"] = "活动名称";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;
        }

        if($request['salesorder_from'] == '6'){
            $field[$k]["fieldname"] = "proorder_orderbuydate";
            $field[$k]["fieldstring"] = "预估采购月份";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;
        }

        $field[$k]["fieldname"] = "proorder_pid";
        $field[$k]["fieldstring"] = "采购单号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "num";
        $field[$k]["fieldstring"] = "销货货品数量";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "proorder_allprice";
        $field[$k]["fieldstring"] = "销货金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "salesorder_createtime";
        $field[$k]["fieldstring"] = "申请日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "sendnum";
        $field[$k]["fieldstring"] = "已发货数量";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "salesorder_status_name";
        $field[$k]["fieldstring"] = "状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;

        $result["fieldcustom"] = 1;
        $result["all_num"] = $res['all_num'];

        if ($res['list']) {
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["all_num"] = 0;
            $res = array('error' => 1, 'errortip' => '无销货单数据', 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }

    function getSalesorderOneApi()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Gmc\StockModel($request);
        $result = $Model->getSalesorderOneApi($request);

        ajax_return($result,$request['language_type']);
    }


    /**
     *库存管理->销货货品明细
     *by:qyh
     *接口391
     */
    function getSalesorderGoodsListView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);

        $Model = new \Model\Gmc\StockModel($request);
        $res = $Model->getSalesorderGoodsList($request);

        $field = array();
        $k = 0;

        $field[$k]["fieldname"] = "goods_id";
        $field[$k]["fieldstring"] = "商品id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "salesordergoods_id";
        $field[$k]["fieldstring"] = "id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "goods_pid";
        $field[$k]["fieldstring"] = "货品编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "goods_cnname";
        $field[$k]["fieldstring"] = "货品名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "prodtype_name";
        $field[$k]["fieldstring"] = "货品类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "goods_unit";
        $field[$k]["fieldstring"] = "单位";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "goods_vipprice";
        $field[$k]["fieldstring"] = "价格";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "salesordergoods_buynums";
        $field[$k]["fieldstring"] = "采购货品数量";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "price";
        $field[$k]["fieldstring"] = "金额小计";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "salesordergoods_sendnums";
        $field[$k]["fieldstring"] = "已发货数量";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        if($request['type'] == 1){
            $field[$k]["fieldname"] = "goodsNum";
            $field[$k]["fieldstring"] = "本次发货数量";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $field[$k]["isInputText"] = true;
            $k++;
        }


        $result = array();
        $result["field"] = $field;
        $result["fieldcustom"] = 1;
        if ($res['list']) {
            $result["list"] = $res['list'];
            $result["all_num"] = $res['all_num'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["all_num"] = 0;
            $res = array('error' => 1, 'errortip' => '无货品数据', 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }


    //采购管理列表
    function getProGoodsListsView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Gmc\StockModel($request);

        $result = $Model->getProGoodsLists($request);
        ajax_return($result,$request['language_type']);
    }

    //采购货品明细
    function getGoodsDetailOneView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Gmc\StockModel($request);

        $result = $Model->getGoodsDetailOne($request);
        ajax_return($result,$request['language_type']);
    }

    //下载导入模版
    function getImportApi()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\StockModel($request);

        $result = $this->Model->getImportApi($request);
        ajax_return($result,$request['language_type']);
    }

    function ImportExcelView()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $url = $request['url'];

        $ys_array = array('货品编号'=>'a','货品中文名'=>'b','货品英文名'=>'c','货品类别'=>'d','单位'=>'e','市场价'=>'f','协议价'=>'g');
        $a = pathinfo($url);
        $options=array(
            "ssl"=>array(
                "verify_peer"=>false,
                "verify_peer_name"=>false,
            ),
        );
        file_put_contents('analysis.xlsx',file_get_contents($url,false,stream_context_create($options)));
        if($a['extension'] == 'xlsx' || $a['extension'] == 'xls'){
            $sqlarray = execl_to_array("analysis.xlsx",$ys_array);
            array_shift($sqlarray);

            foreach ($sqlarray as $item) {
                $code = $this->DataControl->getFieldOne("smc_code_prodtype","prodtype_code","prodtype_name = '{$item['d']}' and company_id = '{$request['company_id']}'");
                do{
                    $prodtype_code=$this->getRandomString(6);
                }while($this->DataControl->selectOne("select prodtype_id from smc_code_prodtype where prodtype_code='{$prodtype_code}' limit 0,1"));
                $data = array();
                $data['company_id'] = $request['company_id'];
                $data['goods_outpid'] = $item['a'];
                $data['goods_pid'] = $item['a'];
                $data['goods_cnname'] = $item['b'];
                $data['goods_enname'] = $item['c'];
                $data['goods_unit'] = $item['e'];
                $data['goods_originalprice'] = $item['f'];
                $data['goods_vipprice'] = $item['g'];
                if($code){
                    $data['prodtype_code'] = $code['prodtype_code'];
                }else{
                    $data['prodtype_code'] = $prodtype_code;
                }
                $data['goods_class'] = 1;
                $data['goods_issale'] = 1;

                $ispid = $this->DataControl->getFieldOne("erp_goods","goods_id","goods_pid = '{$item['a']}' and company_id = '{$request['company_id']}'");
                if(!$ispid && $item['a'] && $item['b'] && $item['d']){
                    $this->DataControl->insertData('erp_goods',$data);

                    if(!$code){

                        $datas = array();
                        $datas['company_id'] = $request['company_id'];
                        $datas['prodtype_name'] = $item['d'];
                        $datas['prodtype_code'] = $data['prodtype_code'];

                        $this->DataControl->insertData('smc_code_prodtype',$datas);
                    }
                }
            }

            $res = array('error' => '0', 'errortip' => '导入商品成功');
            $this->addGmcWorkLog($request['company_id'], $request['staffer_id'], "集团订购管理->货品管理", '导入货品', dataEncode($request));
            ajax_return($res,$request['language_type']);
        }else{
            ajax_return(array('error' => 1,'errortip' => "文件格式有误"));

        }


    }

    function getRandomString($len, $chars=null)
    {
        if (is_null($chars)) {
            $chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        }
        mt_srand(10000000*(double)microtime());
        for ($i = 0, $str = '', $lc = strlen($chars)-1; $i < $len; $i++) {
            $str .= $chars[mt_rand(0, $lc)];
        }
        return $str;
    }

    function testView(){
        $goods_pid = $this->DataControl->getFieldOne('erp_goods', 'goods_id', "goods_pid = '110130030011' and goods_id != '15696' and company_id = '77777'");
        var_dump($goods_pid);
    }

    function ZhanQunGoodsAction(){
        $sendbak = request_by_curl("https://ptcapi.kidcastle.com.cn/Shop/getGoods","","POST",array());
        $schoolList = json_decode($sendbak,true);
        if($schoolList){
            foreach ($schoolList as $vals){
                $data = array();

                $a = $this->DataControl->getFieldOne("erp_goods","goods_id","goods_outpid = '{$vals['goods_number']}' and company_id = '8888'");
                if($a){

                    $data['goods_type'] = '1';
                    $this->DataControl->updateData("erp_goods","goods_outpid = '{$vals['goods_number']}'",$data);
                }

            }
            ajax_return(array('error' => 0,'errortip' => "更新成功！"));
        }else{
            ajax_return(array('error' => 0,'errortip' => "更新失败！"));

        }

    }

    /**
     *集团订购管理->调拨列表
     */
    function getAllotListView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);

        $Model = new \Model\Gmc\StockModel($request);
        $res = $Model->getAllotList($request);

        $field = array();
        $k = 0;

        $field[$k]["fieldstring"] = "proorder_pid";
        $field[$k]["fieldname"] = "调拨编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "from_school_cnname";
        $field[$k]["fieldname"] = "调出校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "from_school_branch";
        $field[$k]["fieldname"] = "调出校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "to_school_cnname";
        $field[$k]["fieldname"] = "调入校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "to_school_branch";
        $field[$k]["fieldname"] = "调入校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "nums";
        $field[$k]["fieldname"] = "调拨货品数量";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "proorder_createtime";
        $field[$k]["fieldname"] = "调拨时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "proorder_status";
        $field[$k]["fieldname"] = "状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;

        $result["allnum"] = $res['allnum'];
        $result["fieldcustom"] = 1;
        if ($res['list']) {
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => '无调拨数据', 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }

    //货品类别下拉列表
    function getProdtypeApi()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $sql = "select prodtype_id,prodtype_code,prodtype_name from smc_code_prodtype where company_id = '{$request['company_id']}'";
        $CompaniesList = $this->DataControl->selectClear($sql);
        if (!$CompaniesList) {
            $CompaniesList = array();
        }

        $result["list"] = $CompaniesList;
        $res = array('error' => 0, 'errortip' => '获取货品类别列表', 'result' => $result);

        ajax_return($res,$request['language_type']);
    }

    //采购活动下拉列表
    function getBuyActivityApi()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $sql = "select activitybuy_id,activitybuy_name from gmc_company_activitybuy where company_id = '{$request['company_id']}'";
        $CompaniesList = $this->DataControl->selectClear($sql);
        if (!$CompaniesList) {
            $CompaniesList = array();
        }

        $result["list"] = $CompaniesList;
        $res = array('error' => 0, 'errortip' => '获取采购活动下拉列表', 'result' => $result);

        ajax_return($res,$request['language_type']);
    }

    function getSalesorderTracksView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);

        $Model = new \Model\Gmc\StockModel($request);
        $res = $Model->getSalesorderTracks($request);

        $field = array();
        $k = 0;

        $field[$k]["fieldname"] = "tracks_title";
        $field[$k]["fieldstring"] = "操作内容";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "tracks_playname";
        $field[$k]["fieldstring"] = "操作人";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "tracks_information";
        $field[$k]["fieldstring"] = "备注";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "tracks_createtime";
        $field[$k]["fieldstring"] = "操作时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;

        $result["fieldcustom"] = 1;
        if ($res['list']) {
            $result["list"] = $res['list'];
            $result["all_num"] = $res['all_num'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["all_num"] = 0;
            $res = array('error' => 1, 'errortip' => '无跟踪数据', 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }

    //获取价格变动日志
    function getGoodsPriceLogView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);

        $Model = new \Model\Gmc\StockModel($request);
        $res = $Model->getGoodsPriceLog($request);

        ajax_return($res,$request['language_type']);
    }

    function salesorderStatusAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Gmc\StockModel($request);

        $result = $Model->salesorderStatusAction($request);

        ajax_return($result,$request['language_type']);
    }

    function sendGoodsAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Gmc\StockModel($request);

        $result = $Model->sendGoodsAction($request);

        ajax_return($result,$request['language_type']);
    }

    function getBeoutorderListView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Gmc\StockModel($request);

        $result = $Model->getBeoutorderList($request);
        ajax_return($result,$request['language_type']);
    }

    //出库单资料
    function getBeoutorderInfoView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Gmc\StockModel($request);

        $result = $Model->getBeoutorderInfo($request);
        ajax_return($result,$request['language_type']);
    }

    //出库货品明细
    function getOutGoodsListView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Gmc\StockModel($request);

        $result = $Model->getOutGoodsList($request);
        ajax_return($result,$request['language_type']);
    }

    //审核出库单
    function ExamineOutorderAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Gmc\StockModel($request);

        $result = $Model->ExamineOutorderAction($request);
        ajax_return($result,$request['language_type']);
    }



    //结尾魔术函数
    function __destruct()
    {

    }
}
