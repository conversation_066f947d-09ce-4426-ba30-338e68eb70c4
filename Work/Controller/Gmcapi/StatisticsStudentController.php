<?php


namespace Work\Controller\Gmcapi;


class StatisticsStudentController extends viewTpl
{
    public $u;
    public $t;
    public $c;

    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
    }

    function studentSurveyApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Gmc\StatisticsStudentModel($request);
        $res = $Model->studentSurvey($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }


    /**
     * 学员数量统计
     * author: ling
     * 对应接口文档 0001
     */
    function StatisticsStudentCountYearView()
    {
        $request = input("get.", '', "trim,addslashes");
        $this->ThisVerify($request);
        $Model = new \Model\Gmc\StatisticsStudentModel($request);
        $dataList = $Model->StatisticsStudentCountYear($request);
        $res = array('error' => '0', 'errortip' => '获取学员数量趋势', 'result' => $dataList);
        ajax_return($res, $request['language_type']);

    }

    /**
     * 学员数量对比
     * author: ling
     *  对应接口文档 0001
     */

    function StudentSchoolContrastView()
    {
        $request = input("get.", '', "trim,addslashes");
        $this->ThisVerify($request);
        $Model = new \Model\Gmc\StatisticsStudentModel($request);
        $dataList = $Model->StudentSchoolContrast($request);
        $field = array();
        $k = 0;
        $field[$k]["fieldname"] = "名称";
        $field[$k]["fieldstring"] = "organize_cnname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;

        $k++;
        $field[$k]["fieldname"] = "当前在籍数量";
        $field[$k]["fieldstring"] = "enroll_num";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $field[$k]["sortable"] = 1;
        $k++;
        $field[$k]["fieldname"] = "新增数量";
        $field[$k]["fieldstring"] = "add_enroll_num";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $field[$k]["sortable"] = 1;
        $k++;
        $field[$k]["fieldname"] = "流失数量";
        $field[$k]["fieldstring"] = "loss_enroll_num";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $field[$k]["sortable"] = 1;


        $result = array();
        $result['field'] =$field;
        $result['data'] =$dataList['data'];
        $result['list'] =$dataList['list'];
        $result['allnum'] =$dataList['allnum'];

        $res = array('error' => '0', 'errortip' => '获取学员数量趋势', 'result' => $result);
        ajax_return($res, $request['language_type']);
    }

    /*
     * 学员性别分布
     *
     */
    function StudentSexDistributionView()
    {
        $request = input("get.", '', "trim,addslashes");
        $this->ThisVerify($request);
        $Model = new \Model\Gmc\StatisticsStudentModel($request);
        $dataList = $Model->StudentSexDistribution($request);
        $res = array('error' => '0', 'errortip' => '学员性别分布', 'result' => $dataList);
        ajax_return($res, $request['language_type']);
    }

    /**
     * 学员年龄分布
     * author: ling
     * 对应接口文档 0001
     */
    function StudentAgeDistributionView()
    {
        $request = input("get.", '', "trim,addslashes");
        $this->ThisVerify($request);
        $Model = new \Model\Gmc\StatisticsStudentModel($request);
        $dataList = $Model->StudentAgeDistribution($request);
        $res = array('error' => '0', 'errortip' => '学员年龄分布', 'result' => $dataList);
        ajax_return($res, $request['language_type']);
    }

    //------------------------------考勤概况-------------------------------------------
    /**
     * 考勤状况
     * author: ling
     * 对应接口文档 0001
     */
    function studentClockinginView(){
        $request = input("get.", '', "trim,addslashes");
        $this->ThisVerify($request);
        $Model = new \Model\Gmc\StatisticsStudentModel($request);
        $dataList = $Model->studentClockingin($request);
        $res = array('error' => '0', 'errortip' => '考勤状况', 'result' => $dataList);
        ajax_return($res, $request['language_type']);
    }

    /**
     * 学员考勤趋势
     * author: ling
     * 对应接口文档 0001
     */
    function StudentClockinTrendView(){
        $request = input("get.", '', "trim,addslashes");
        $this->ThisVerify($request);
        $Model = new \Model\Gmc\StatisticsStudentModel($request);
        $dataList = $Model->StudentClockinTrend($request);
        $res = array('error' => '0', 'errortip' => '学员考勤趋势', 'result' => $dataList);
        ajax_return($res, $request['language_type']);

    }

    /**
     * 学员出勤红榜
     * author: ling
     * 对应接口文档 0001
     */
    function studentClockinSortView(){
        $request = input("get.", '', "trim,addslashes");
        $this->ThisVerify($request);
        $Model = new \Model\Gmc\StatisticsStudentModel($request);
        $dataList = $Model->studentClockinSort($request);

        $field = array();
        $k = 0;

        $field[$k]["fieldname"] = "排行";
        $field[$k]["fieldstring"] = "number";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $field[$k]["sortable"] = 1;
        $k++;
        $field[$k]["fieldname"] = "名称";
        $field[$k]["fieldstring"] = "organize_cnname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;
        $field[$k]["fieldname"] = "应出勤人次";
        $field[$k]["fieldstring"] = "all_clockingin_num";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $field[$k]["sortable"] = 1;
        $k++;
        $field[$k]["fieldname"] = "缺勤人次";
        $field[$k]["fieldstring"] = "no_clockingin_num";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;
        $field[$k]["fieldname"] = "实际出勤人次";
        $field[$k]["fieldstring"] = "clockingin_num";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;
        $field[$k]["fieldname"] = "出勤率";
        $field[$k]["fieldstring"] = "clockingin_rate";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $field[$k]["sortable"] = 1;
        $k++;
        $field[$k]["fieldname"] = "缺勤率";
        $field[$k]["fieldstring"] = "no_clockingin_rate";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $field[$k]["sortable"] = 1;


        $result = array();
        $result['field'] =$field;
        $result['data'] =$dataList['data'];
        $result['list'] =$dataList['list'];
        $result['allnum'] =$dataList['allnum'];

        $res = array('error' => '0', 'errortip' => '学员出勤红榜', 'result' => $result);
        ajax_return($res, $request['language_type']);
    }

    function paymentInfoApi(){
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Gmc\StatisticsStudentModel($request);
        $res = $Model->paymentInfo($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res,$request['language_type']);

    }

    function schoolBudgetApi(){
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Gmc\StatisticsStudentModel($request);
        $res = $Model->schoolBudget($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res,$request['language_type']);

    }












































}