<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/13
 * Time: 23:25
 */

namespace Work\Controller\Gmcapi;


class AffairController extends viewTpl
{
    public $u;
    public $t;
    public $c;
    public $visitType = "api";
    public $Model;

    //预加载处理类
    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
    }

    function ThisVerify($request)
    {
        $paramArray = array();
        $paramArray['staffer_id'] = $request['staffer_id'];
        $paramArray['company_id'] = $request['company_id'];
        $paramArray['token'] = $request['token'];
        if (!$this->UserLimit($paramArray)) {
            $result = array();
            $result["list"] = array();
            $result["tokeninc"] = "0";
            $res = array('error' => '1', 'errortip' => "用户token失效", 'result' => $result);
            ajax_return($res, $request['language_type']);
        }
    }


    //学员出勤类型列表
    function HomeView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\AffairModel();

        $result = $this->Model->getStuchecktypeList($request);
        ajax_return($result, $request['language_type']);
    }

    //添加出勤类型
    function addStuchecktypeAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\AffairModel($request);

        $result = $this->Model->addStuchecktypeAction($request);
        ajax_return($result, $request['language_type']);
    }

    //编辑出勤类型
    function updateStuchecktypeAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\AffairModel($request);

        $result = $this->Model->updateStuchecktypeAction($request);
        ajax_return($result, $request['language_type']);
    }

    //删除出勤类型
    function delStuchecktypeAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\AffairModel($request);

        $result = $this->Model->delStuchecktypeAction($request);
        ajax_return($result, $request['language_type']);
    }

    //学员异动代码列表
    function stuchangeView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\AffairModel($request);

        $result = $this->Model->getStuchangeList($request);
        ajax_return($result, $request['language_type']);
    }

    //添加学员异动代码
    function addStuchangeAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\AffairModel($request);

        $result = $this->Model->addStuchangeAction($request);
        ajax_return($result, $request['language_type']);
    }

    //编辑学员异动代码
    function updateStuchangeAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\AffairModel($request);

        $result = $this->Model->updateStuchangeAction($request);
        ajax_return($result, $request['language_type']);
    }

    //删除学员异动代码
    function delStuchangeAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\AffairModel($request);

        $result = $this->Model->delStuchangeAction($request);
        ajax_return($result, $request['language_type']);
    }

    //学员异动原因列表
    function stuchangeReasonView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\AffairModel($request);

        $result = $this->Model->getStuchangeReasonList($request);
        ajax_return($result, $request['language_type']);
    }

    //添加学员异动原因
    function addStuchangeReasonAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\AffairModel($request);

        $result = $this->Model->addStuchangeReasonAction($request);
        ajax_return($result, $request['language_type']);
    }

    //编辑学员异动原因
    function updateStuchangeReasonAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\AffairModel($request);

        $result = $this->Model->updateStuchangeReasonAction($request);
        ajax_return($result, $request['language_type']);
    }

    //删除学员异动原因
    function delStuchangeReasonAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\AffairModel($request);

        $result = $this->Model->delStuchangeReasonAction($request);
        ajax_return($result, $request['language_type']);
    }

    //家长职业信息列表
    function parentscareerView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\AffairModel($request);

        $result = $this->Model->getParentscareerList($request);
        ajax_return($result, $request['language_type']);
    }

    //添加家长职业信息
    function addParentscareerAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\AffairModel($request);

        $result = $this->Model->addParentscareerAction($request);
        ajax_return($result, $request['language_type']);
    }

    //编辑家长职业信息
    function updateParentscareerAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\AffairModel($request);

        $result = $this->Model->updateParentscareerAction($request);
        ajax_return($result, $request['language_type']);
    }

    //删除家长职业信息
    function delParentscareerAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\AffairModel($request);

        $result = $this->Model->delParentscareerAction($request);
        ajax_return($result, $request['language_type']);
    }

    //收费类型列表
    function feeView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FinanceModel($request);

        $result = $this->Model->getFeeList($request);
        ajax_return($result, $request['language_type']);
    }

    //添加收费类型
    function addFeeAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FinanceModel($request);

        $result = $this->Model->addFeeAction($request);
        ajax_return($result, $request['language_type']);
    }

    //编辑收费类型
    function updateFeeAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FinanceModel($request);

        $result = $this->Model->updateFeeAction($request);
        ajax_return($result, $request['language_type']);
    }

    //删除收费类型
    function delFeeAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FinanceModel($request);

        $result = $this->Model->delFeeAction($request);
        ajax_return($result, $request['language_type']);
    }

    //收费支付方式列表
    function paytypeView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FinanceModel($request);

        $result = $this->Model->getPaytypeList($request);
        ajax_return($result, $request['language_type']);
    }

    //添加支付方式类型
    function addPaytypeAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FinanceModel($request);

        $result = $this->Model->addPaytypeAction($request);
        ajax_return($result, $request['language_type']);
    }

    //编辑支付方式类型
    function updatePaytypeAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FinanceModel($request);

        $result = $this->Model->updatePaytypeAction($request);
        ajax_return($result, $request['language_type']);
    }

    //删除收费类型
    function delPaytypeAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FinanceModel($request);

        $result = $this->Model->delPaytypeAction($request);
        ajax_return($result, $request['language_type']);
    }

    //零用金列表
    function pettycashView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FinanceModel($request);

        $result = $this->Model->getPettycashList($request);
        ajax_return($result, $request['language_type']);
    }

    //新增零用金
    function addPettycashAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FinanceModel($request);

        $result = $this->Model->addPettycashAction($request);
        ajax_return($result, $request['language_type']);
    }

    //编辑零用金
    function updatePettycashAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FinanceModel($request);

        $result = $this->Model->updatePettycashAction($request);
        ajax_return($result, $request['language_type']);
    }

    //删除零用金
    function delPettycashAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FinanceModel($request);

        $result = $this->Model->delPettycashAction($request);
        ajax_return($result, $request['language_type']);
    }

    //支出类型列表
    function spendingView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FinanceModel($request);

        $result = $this->Model->getSpendingList($request);
        ajax_return($result, $request['language_type']);
    }

    //新增支出类型列表
    function addSpendingAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FinanceModel($request);

        $result = $this->Model->addSpendingAction($request);
        ajax_return($result, $request['language_type']);
    }

    //编辑支出类型
    function updateSpendingAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FinanceModel($request);

        $result = $this->Model->updateSpendingAction($request);
        ajax_return($result, $request['language_type']);
    }

    //删除支出类型
    function delSpendingAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\FinanceModel($request);

        $result = $this->Model->delSpendingAction($request);
        ajax_return($result, $request['language_type']);
    }

    //节假日管理列表
    function holidaysView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\AffairModel($request);

        $result = $this->Model->getHolidaysList($request);
        ajax_return($result, $request['language_type']);
    }

    //新增节假日
    function addHolidaysAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\AffairModel($request);

        $result = $this->Model->addHolidaysAction($request);
        ajax_return($result, $request['language_type']);
    }

    //编辑节假日
    function updateHolidaysAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\AffairModel($request);

        $result = $this->Model->updateHolidaysAction($request);
        ajax_return($result, $request['language_type']);
    }

    //删除节假日
    function delHolidaysAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\AffairModel($request);

        $result = $this->Model->delHolidaysAction($request);
        ajax_return($result, $request['language_type']);
    }


    //节假日日历列表
    function getMonthListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\AffairModel($request);

        $result = $this->Model->getMonthList($request);
        ajax_return($result, $request['language_type']);
    }


    //教室列表
    function getClassroomListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\AffairModel($request);

        $result = $this->Model->getClassroomList($request);
        ajax_return($result, $request['language_type']);
    }

    //添加教室
    function addClassroomAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\AffairModel($request);

        $result = $this->Model->addClassroomAction($request);
        ajax_return($result, $request['language_type']);
    }

    //编辑教室
    function updateClassroomAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\AffairModel($request);

        $result = $this->Model->updateClassroomAction($request);
        ajax_return($result, $request['language_type']);
    }

    //删除教室
    function delClassroomAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\AffairModel($request);

        $result = $this->Model->delClassroomAction($request);
        ajax_return($result, $request['language_type']);
    }

    //改变教室启用状态
    function updateClassroomStatusAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\AffairModel($request);

        $result = $this->Model->updateClassroomStatusAction($request);
        ajax_return($result, $request['language_type']);
    }

    //根据手机号获取集团职工信息
    function getGmcStafferInfoApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $this->Model = new \Model\Gmc\AffairModel($request);

        $result = $this->Model->getGmcStafferInfo($request);
        ajax_return($result, $request['language_type']);
    }


    function getSubtypeListApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Gmc\AffairModel($request);
        $res = $Model->getSubtypeList($request);
        $field = array();
        $k = 0;

        $field[$k]["fieldstring"] = "subtype_id";
        $field[$k]["fieldname"] = "认缴类别ID";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "subtype_name";
        $field[$k]["fieldname"] = "认缴类别";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 0;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function addSubtypeAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Gmc\AffairModel($request);
        $res = $Model->addSubtype($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '添加成功', 'result' => $result);
            $this->addGmcWorkLog($request['company_id'], $request['staffer_id'], "集团设置->班务相关设置", '新增认缴类别', dataEncode($request));
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function editSubtypeAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Gmc\AffairModel($request);
        $res = $Model->editSubtype($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '编辑成功', 'result' => $result);
            $this->addGmcWorkLog($request['company_id'], $request['staffer_id'], "集团设置->班务相关设置", '编辑认缴类别', dataEncode($request));
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function delSubtypeAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Gmc\AffairModel($request);
        $res = $Model->delSubtype($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '删除成功', 'result' => $result);
            $this->addGmcWorkLog($request['company_id'], $request['staffer_id'], "集团设置->班务相关设置", '删除认缴类别', dataEncode($request));
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function getTagsTypeListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Gmc\AffairModel($request);
        $res = $Model->getTagsTypeList($request);
        $field = array();
        $k = 0;

        $field[$k]["fieldstring"] = "tagstype_name";
        $field[$k]["fieldname"] = "标签类型名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "tags_name_count";
        $field[$k]["fieldname"] = "标签类型明细";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "tagstype_use";
        $field[$k]["fieldname"] = "标签类型使用次数";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 0;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function addTagsAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Gmc\AffairModel($request);
        $res = $Model->addTags($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '添加成功', 'result' => $result);
            $this->addGmcWorkLog($request['company_id'], $request['staffer_id'], "集团设置->班务相关设置", '新增学员标签', dataEncode($request));
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function editTagsAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Gmc\AffairModel($request);
        $res = $Model->editTags($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '编辑成功', 'result' => $result);
            $this->addGmcWorkLog($request['company_id'], $request['staffer_id'], "集团设置->班务相关设置", '编辑学员标签', dataEncode($request));
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function delTagsAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Gmc\AffairModel($request);
        $res = $Model->delTags($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '删除成功', 'result' => $result);
            $this->addGmcWorkLog($request['company_id'], $request['staffer_id'], "集团设置->班务相关设置", '删除学员标签', dataEncode($request));
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function delTagsTypeAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Gmc\AffairModel($request);
        $res = $Model->delTagsType($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '删除成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function getTagsByTypeView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Gmc\AffairModel($request);
        $res = $Model->getTagsByType($request);
        $field = array();
        $k = 0;

        $field[$k]["fieldstring"] = "tags_id";
        $field[$k]["fieldname"] = "标签id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "tags_name";
        $field[$k]["fieldname"] = "标签名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "tags_use";
        $field[$k]["fieldname"] = "标签使用次数";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 0;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function getTrackTypeListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Gmc\AffairModel($request);
        $res = $Model->getTrackTypeList($request);
        $field = array();
        $k = 0;

        $field[$k]["fieldstring"] = "tracktype_id";
        $field[$k]["fieldname"] = "沟通类型id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "tracktype_name";
        $field[$k]["fieldname"] = "沟通类型名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "tracktype_module";
        $field[$k]["fieldname"] = "应用模块";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "tracktype_smc";
        $field[$k]["fieldname"] = "校务";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "tracktype_eas";
        $field[$k]["fieldname"] = "教务";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "tracktype_remk";
        $field[$k]["fieldname"] = "备注";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "tracktype_use";
        $field[$k]["fieldname"] = "沟通类型使用次数";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 0;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function addTrackTypeAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Gmc\AffairModel($request);
        $res = $Model->addTrackType($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '添加成功', 'result' => $result);
            $this->addGmcWorkLog($request['company_id'], $request['staffer_id'], "集团设置->班务相关设置", '新增沟通类型', dataEncode($request));
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function editTrackTypeAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Gmc\AffairModel($request);
        $res = $Model->editTrackType($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => $Model->errortip, 'result' => $result);
            $this->addGmcWorkLog($request['company_id'], $request['staffer_id'], "集团设置->班务相关设置", '编辑沟通类型', dataEncode($request));
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function delTrackTypeAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Gmc\AffairModel($request);
        $res = $Model->delTrackType($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '删除成功', 'result' => $result);
            $this->addGmcWorkLog($request['company_id'], $request['staffer_id'], "集团设置->班务相关设置", '删除沟通类型', dataEncode($request));
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function getTrackResultListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Gmc\AffairModel($request);
        $res = $Model->getTrackResultList($request);
        $field = array();
        $k = 0;

        $field[$k]["fieldstring"] = "trackresult_id";
        $field[$k]["fieldname"] = "沟通结果id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "trackresult_name";
        $field[$k]["fieldname"] = "沟通结果名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "tracktype_id";
        $field[$k]["fieldname"] = "沟通类型";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "tracktype_name";
        $field[$k]["fieldname"] = "沟通类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "trackresult_remk";
        $field[$k]["fieldname"] = "备注";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "trackresult_use";
        $field[$k]["fieldname"] = "沟通结果使用次数";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 0;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function addTrackResultAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Gmc\AffairModel($request);
        $res = $Model->addTrackResult($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '添加成功', 'result' => $result);
            $this->addGmcWorkLog($request['company_id'], $request['staffer_id'], "集团设置->班务相关设置", '新增沟通结果', dataEncode($request));
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function editTrackResultAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Gmc\AffairModel($request);
        $res = $Model->editTrackResult($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => $Model->errortip, 'result' => $result);
            $this->addGmcWorkLog($request['company_id'], $request['staffer_id'], "集团设置->班务相关设置", '编辑沟通结果', dataEncode($request));
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function delTrackResultAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Gmc\AffairModel($request);
        $res = $Model->delTrackResult($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '删除成功', 'result' => $result);
            $this->addGmcWorkLog($request['company_id'], $request['staffer_id'], "集团设置->班务相关设置", '删除沟通结果', dataEncode($request));
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //结尾魔术函数
    function __destruct()
    {

    }
}
