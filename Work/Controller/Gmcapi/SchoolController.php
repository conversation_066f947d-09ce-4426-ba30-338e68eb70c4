<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/13
 * Time: 23:25
 */

namespace Work\Controller\Gmcapi;


class SchoolController extends viewTpl
{
    public $u;
    public $t;
    public $c;
    public $visitType = "api";
    public $Model;

    //预加载处理类
    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
    }

    function ThisVerify($request)
    {
        $paramArray = array();
        $paramArray['staffer_id'] = $request['staffer_id'];
        $paramArray['company_id'] = $request['company_id'];
        $paramArray['token'] = $request['token'];
        if (!$this->UserLimit($paramArray)) {
            $result = array();
            $result["list"] = array();
            $result["tokeninc"] = "0";
            $res = array('error' => '1', 'errortip' => "用户token失效", 'result' => $result);
            ajax_return($res, $request['language_type']);
        }
    }

    //分校列表
    function HomeView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\SchoolModel($request);

        $result = $this->Model->getSchoolList($request);

        ajax_return($result, $request['language_type']);
    }


    function getSchoolCompaniesApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\SchoolModel($request);

        $result = $this->Model->getSchoolCompanies($request);

        ajax_return($result, $request['language_type']);
    }


    //添加分校
    function addSchoolAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\SchoolModel($request);

        $result = $this->Model->addSchoolAction($request);
        ajax_return($result, $request['language_type']);
    }

    //查看分校
    function schoolDetailApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\SchoolModel($request);

        $result = $this->Model->schoolDetailApi($request);
        ajax_return($result, $request['language_type']);
    }

    //学校区域列表
    function districtListApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\SchoolModel($request);

        $result = $this->Model->districtListApi($request);

        ajax_return($result, $request['language_type']);
    }

    //添加区域
    function addDistrictAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\SchoolModel($request);

        $result = $this->Model->addDistrictAction($request);
        ajax_return($result, $request['language_type']);
    }

    //编辑区域
    function updateDistrictAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\SchoolModel($request);

        $result = $this->Model->updateDistrictAction($request);

        ajax_return($result, $request['language_type']);
    }

    //删除区域
    function delDistrictAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\SchoolModel($request);

        $result = $this->Model->delDistrictAction($request);

        ajax_return($result, $request['language_type']);
    }

    //编辑学校
    function updateSchoolAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\SchoolModel($request);

        $result = $this->Model->updateSchoolAction($request);

        ajax_return($result, $request['language_type']);
    }

    //改变是否学科类学校
    function ChangeSchoolSubjectAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\SchoolModel($request);

        $result = $this->Model->ChangeSchoolSubjectAction($request);
        ajax_return($result, $request['language_type']);
    }

    //删除学校
    function delSchoolAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\SchoolModel($request);

        $result = $this->Model->delSchoolAction($request);

        ajax_return($result, $request['language_type']);
    }

    //收费主体设置
    function chargingSubjectApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $StudentModel = new \Model\Gmc\SchoolModel($request);
        $res = $StudentModel->chargingSubject($request);
        $field = array();

        $k = 0;
        $field[$k]["fieldstring"] = "coursecat_cnname";
        $field[$k]["fieldname"] = "班种名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "coursetype_cnname";
        $field[$k]["fieldname"] = "所属班组";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "companies_cnname";
        $field[$k]["fieldname"] = "主体名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;


        $field[$k]["fieldstring"] = "merge_companies_cnname";
        $field[$k]["fieldname"] = "组合课程主体名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 0;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => '暂无数据', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //批量设置收费主体
    function batchSetChargingSubjectAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\SchoolModel($request);

        $res = $this->Model->batchSetChargingSubjectAction($request);

        $result = array();
        if($res){
            $result["list"] = $res;
            $res = array('error' => '0', 'errortip' => "批量设置成功", 'result' => $result);
            $this->addGmcWorkLog($request['company_id'], $request['staffer_id'], "集团架构->校园管理", '批量设置收费主体', dataEncode($request));
        }else{
            $result["list"] = array();
            $res = array('error' => '1', 'errortip' => '批量设置失败', 'result' => $result);
        }

        ajax_return($res, $request['language_type']);
    }

    //获取学校省
    function getProvinceApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\SchoolModel($request);

        $result = $this->Model->getProvinceApi($request);
        ajax_return($result, $request['language_type']);
    }

    //获取学校市
    function getCityApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\SchoolModel($request);

        $result = $this->Model->getCityApi($request);
        ajax_return($result, $request['language_type']);
    }

    //获取学校区
    function getAreaApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\SchoolModel($request);

        $result = $this->Model->getAreaApi($request);
        ajax_return($result, $request['language_type']);
    }

    //获取产品
    function getAppApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\SchoolModel($request);

        $result = $this->Model->getAppApi($request);
        ajax_return($result, $request['language_type']);
    }

    //获取产品
    function getPowerListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\SchoolModel($request);

        $result = $this->Model->getPowerList($request);
        ajax_return($result, $request['language_type']);
    }

    //获取班级
    function getClassApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\SchoolModel($request);

        $result = $this->Model->getClassApi($request);
        ajax_return($result, $request['language_type']);
    }

    //获取POS机模式
    function getPospatternApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\SchoolModel($request);

        $result = $this->Model->getPospatternApi($request);
        ajax_return($result, $request['language_type']);
    }

    //获取学校学员
    function getStudentListView()
    {
//        error_reporting(E_ALL);
//        ini_set('display_errors', '1');
        ini_set("memory_limit", '512M');
        set_time_limit(600);
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\SchoolModel($request);

        $result = $this->Model->getStudentList($request);
        ajax_return($result, $request['language_type']);
    }

    //获取流失学校学员
    function getLossStudentListApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Gmc\SchoolModel($request);
        $res = $Model->getLossStudentList($request);
        $field = array();

        $k = 0;
        $field[$k]["fieldstring"] = "school_id";
        $field[$k]["fieldname"] = "学校ID";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "流失校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "流失校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["fieldname"] = "学员中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_enname";
        $field[$k]["fieldname"] = "学员英文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_branch";
        $field[$k]["fieldname"] = "学员编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_sex";
        $field[$k]["fieldname"] = "性别";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_birthday";
        $field[$k]["fieldname"] = "生日";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "parenter_cnname";
        $field[$k]["fieldname"] = "主要联系人";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "parenter_mobile";
        $field[$k]["fieldname"] = "主要联系电话";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "enrolled_leavetime";
        $field[$k]["fieldname"] = "流失日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "changelog_note";
        $field[$k]["fieldname"] = "流失原因";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);

        }
        ajax_return($res, $request['language_type']);
    }

    function checklogoutStuApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Gmc\SchoolModel($request);
        $res = $Model->checklogoutStu($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '检测通过', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //注销学生
    function logoutStuAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Gmc\SchoolModel($request);
        $res = $Model->logoutStu($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '注销成功', 'result' => $result);
            $this->addGmcWorkLog($request['company_id'], $request['staffer_id'], "集团架构->用户管理->流失学员管理", '注销学籍', dataEncode($request));
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function getWeekLogoutStuApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);

        $Model = new \Model\Gmc\SchoolModel($request);
        $res = $Model->getWeekLogoutStu();
        if ($res) {
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $res);
        } else {
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => array());
        }
        ajax_return($res, $request['language_type']);
    }

    //获取学校注销学员
    function getLogoutStudentListApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Gmc\SchoolModel($request);
        $res = $Model->getLogoutStudentList($request);
        $field = array();

        $k = 0;
        $field[$k]["fieldstring"] = "student_id";
        $field[$k]["fieldname"] = "学员ID";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["fieldname"] = "学员中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_enname";
        $field[$k]["fieldname"] = "学员英文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_branch";
        $field[$k]["fieldname"] = "学员编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_sex";
        $field[$k]["fieldname"] = "性别";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_birthday";
        $field[$k]["fieldname"] = "生日";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "parenter_cnname";
        $field[$k]["fieldname"] = "主要联系人";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "parenter_mobile";
        $field[$k]["fieldname"] = "主要联系电话";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "outlog_createtime";
        $field[$k]["fieldname"] = "注销日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);

        }
        ajax_return($res, $request['language_type']);
    }

    //添加产品权限
    function addAppPowerAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\SchoolModel($request);

        $result = $this->Model->addAppPowerAction($request);
        ajax_return($result, $request['language_type']);
    }

    function modifyCardStatusAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\SchoolModel($request);

        $result = $this->Model->modifyCardStatus($request);
        ajax_return($result, $request['language_type']);

    }


    //删除产品权限
    function delAppPowerAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\SchoolModel($request);

        $result = $this->Model->delAppPowerAction($request);
        ajax_return($result, $request['language_type']);
    }

    //获取学校学员
    function getStudentOneView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\SchoolModel($request);

        $result = $this->Model->getStudentOne($request);
        ajax_return($result, $request['language_type']);
    }

    //删除学员
    function delStudentAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\SchoolModel($request);

        $result = $this->Model->delStudentAction($request);
        ajax_return($result, $request['language_type']);
    }

    //家长管理列表
    function getParentListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Gmc\SchoolModel($request);
        $res = $Model->getParentList($request);
        $field = array();

        $k = 0;
        $field[$k]["fieldstring"] = "parenter_id";
        $field[$k]["fieldname"] = "家长ID";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "parenter_cnname";
        $field[$k]["fieldname"] = "家长姓名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "parenter_mobile";
        $field[$k]["fieldname"] = "手机号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "wx";
        $field[$k]["fieldname"] = "是否绑定微信";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "num";
        $field[$k]["fieldname"] = "关联孩子数量";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "time";
        $field[$k]["fieldname"] = "最后登陆时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "parenter_lastip";
        $field[$k]["fieldname"] = "最后登陆IP";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);

        }
        ajax_return($res, $request['language_type']);
    }

    //关联孩子信息
    function getRelevanceStudentView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Gmc\SchoolModel($request);
        $res = $Model->getRelevanceStudent($request);
        $field = array();

        $k = 0;
        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["fieldname"] = "中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_enname";
        $field[$k]["fieldname"] = "英文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_branch";
        $field[$k]["fieldname"] = "学员编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_sex";
        $field[$k]["fieldname"] = "性别";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_birthday";
        $field[$k]["fieldname"] = "出生日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "family_isdefault";
        $field[$k]["fieldname"] = "是否主要联系人";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);

        }
        ajax_return($res, $request['language_type']);
    }

    //学生的班级列表
    function studentClassView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $StudentModel = new \Model\Gmc\SchoolModel($request);
        $res = $StudentModel->studentClass($request);
        $field = array();

        $k = 0;
        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "class_cnname";
        $field[$k]["fieldname"] = "班级名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "class_enname";
        $field[$k]["fieldname"] = "班级别名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "class_branch";
        $field[$k]["fieldname"] = "班级编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $field[$k]["haveDetail"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_type_name";
        $field[$k]["fieldname"] = "班级类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "course_cnname";
        $field[$k]["fieldname"] = "课程别名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "course_branch";
        $field[$k]["fieldname"] = "课程别编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "class_status";
        $field[$k]["fieldname"] = "班级状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "study_isreading_name";
        $field[$k]["fieldname"] = "在班状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "class_stdate";
        $field[$k]["fieldname"] = "开班日期";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "study_beginday";
        $field[$k]["fieldname"] = "入班日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "study_endday";
        $field[$k]["fieldname"] = "出班日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "class_num";
        $field[$k]["fieldname"] = "全部课次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "class_clock_num";
        $field[$k]["fieldname"] = "班级上课次数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "stu_clock_num";
        $field[$k]["fieldname"] = "学员考勤次数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;


        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 0;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => '学员暂无入班信息', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }


    //单个学员的考勤
    function studentAttendanceView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $StudentModel = new \Model\Gmc\SchoolModel($request);
        $res = $StudentModel->studentAttendance($request);
        $field = array();

        $k = 0;
        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "hour_day";
        $field[$k]["fieldname"] = "上课时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "course_cnname";
        $field[$k]["fieldname"] = "课程别名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "course_branch";
        $field[$k]["fieldname"] = "课程别编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "hour_way_name";
        $field[$k]["fieldname"] = "上课方式";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "class_cnname";
        $field[$k]["fieldname"] = "班级名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "class_branch";
        $field[$k]["fieldname"] = "班级编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "hourstudy_checkin";
        $field[$k]["fieldname"] = "出勤状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "clockinginlog_price";
        $field[$k]["fieldname"] = "平摊金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "clockinginlog_note";
        $field[$k]["fieldname"] = "缺勤原因";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "staffer_cnname";
        $field[$k]["fieldname"] = "教师";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 0;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => '学员暂无考勤记录', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //
    function orderView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $StudentModel = new \Model\Gmc\SchoolModel($request);
        $res = $StudentModel->order($request);
        $field = array();
        $k = 0;

        $field[$k]["fieldstring"] = "trading_pid";
        $field[$k]["fieldname"] = "交易编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "order_type";
        $field[$k]["fieldname"] = "订单类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        if (!isset($request['student_id']) || $request['student_id'] == '') {

            $field[$k]["fieldstring"] = "student_cnname";
            $field[$k]["fieldname"] = "学员中文名";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "student_enname";
            $field[$k]["fieldname"] = "学员英文名";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "student_branch";
            $field[$k]["fieldname"] = "学员编号";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;
        } else {
            $field[$k]["fieldstring"] = "school_cnname";
            $field[$k]["fieldname"] = "校区名称";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "school_branch";
            $field[$k]["fieldname"] = "校区编号";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;
        }


        $field[$k]["fieldstring"] = "parenter_cnname";
        $field[$k]["fieldname"] = "家长姓名";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "parenter_mobile";
        $field[$k]["fieldname"] = "下单手机号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "companies_cnname";
        $field[$k]["fieldname"] = "订单主体";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "order_status";
        $field[$k]["fieldname"] = "订单状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "order_from";
        $field[$k]["fieldname"] = "订单来源";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "order_type";
        $field[$k]["fieldname"] = "收费类型";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "order_allprice";
        $field[$k]["fieldname"] = "订单总额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coupon_price";
        $field[$k]["fieldname"] = "订单优惠金额";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "balance";
        $field[$k]["fieldname"] = "使用账户余额";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "forward";
        $field[$k]["fieldname"] = "使用结转金额";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "order_paidprice";
        $field[$k]["fieldname"] = "已付金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "order_arrears";
        $field[$k]["fieldname"] = "欠费金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "order_paymentprice";
        $field[$k]["fieldname"] = "实付金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "order_isinvoice";
        $field[$k]["fieldname"] = "开票状态";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "dealorder_balanceprice";
        $field[$k]["fieldname"] = "账户余额变动";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "dealorder_forwardprice";
        $field[$k]["fieldname"] = "结转金额";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "tradingtype_name";
        $field[$k]["fieldname"] = "交易类型";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "order_refusereason";
        $field[$k]["fieldname"] = "拒绝原因";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "is_should_pay";
        $field[$k]["fieldname"] = "是否需要支付";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "can_cancel";
        $field[$k]["fieldname"] = "是否可以取消";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "is_should_check";
        $field[$k]["fieldname"] = "是否可以审核";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "student_id";
        $field[$k]["fieldname"] = "学员ID";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "trading_createtime";
        $field[$k]["fieldname"] = "下单时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();

        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["list"] = $res['list'];
            $result["companieslist"] = $res['companieslist'];
            $result["fieldcustom"] = 0;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["companieslist"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => '暂无订单信息', 'result' => $result);

        }
        ajax_return($res, $request['language_type']);

    }

    function getOrderTradingtypeCodeApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $codeList = $this->DataControl->selectClear("select tradingtype_code,tradingtype_name from smc_code_tradingtype where tradingtype_code<>'Subscribed' and tradingtype_code<>'MonthlyShare'");
        $result = array();
        if ($codeList) {
            $result["list"] = $codeList;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);

        } else {
            $res = array('error' => 1, 'errortip' => '获取信息', 'result' => $result);
        }
        ajax_return($res, $request['language_type'], 1);
    }

    //单个学员异动列表
    function classChangeView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $StudentModel = new \Model\Gmc\SchoolModel($request);
        $res = $StudentModel->classChange($request);
        $field = array();
        $k = 0;

        $field[$k]["fieldstring"] = "changelog_id";
        $field[$k]["fieldname"] = "异动ID";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "stuchange_name";
        $field[$k]["fieldname"] = "异动类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "异动学校";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_cnname";
        $field[$k]["fieldname"] = "异动班级";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_branch";
        $field[$k]["fieldname"] = "异动班级编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "changelog_note";
        $field[$k]["fieldname"] = "异动描述";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "changelog_day";
        $field[$k]["fieldname"] = "异动日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "stustatus_isdel";
        $field[$k]["fieldname"] = "是否可以删除";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "change_pid";
        $field[$k]["fieldname"] = "异动编号";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "stuchange_code";
        $field[$k]["fieldname"] = "异动代码";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 0;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => '学员暂无异动记录', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //异动类型下拉
    function changeTypeApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $list = $this->DataControl->selectClear("select stuchange_code,stuchange_name from smc_code_stuchange");
        if ($list) {
            $result["list"] = $list;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '获取信息', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }


    //单个学生的请假记录
    function studentAbsenceView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);

        $StudentModel = new \Model\Gmc\SchoolModel($request);
        $res = $StudentModel->studentAbsence($request);

        $field = array();
        $key = 0;
        if (!isset($request['student_id']) || $request['student_id'] == '') {
            $field[$key]["fieldstring"] = "student_cnname";
            $field[$key]["fieldname"] = "学员中文名";
            $field[$key]["show"] = 1;
            $field[$key]["custom"] = 1;
            $key++;

            $field[$key]["fieldstring"] = "student_enname";
            $field[$key]["fieldname"] = "学员英文名";
            $field[$key]["show"] = 1;
            $field[$key]["custom"] = 1;
            $key++;

            $field[$key]["fieldstring"] = "student_branch";
            $field[$key]["fieldname"] = "学生编号";
            $field[$key]["show"] = 1;
            $field[$key]["custom"] = 1;
            $key++;
        }
        $field[$key]["fieldstring"] = "absence_id";
        $field[$key]["fieldname"] = "请假记录ID";
        $field[$key]["show"] = 0;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldstring"] = "school_cnname";
        $field[$key]["fieldname"] = "校区名称";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldstring"] = "school_branch";
        $field[$key]["fieldname"] = "校区编号";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;


        $field[$key]["fieldstring"] = "absence_time";
        $field[$key]["fieldname"] = "请假日期";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 1;
        $key++;

        $field[$key]["fieldstring"] = "hour_allnum";
        $field[$key]["fieldname"] = "请假课次";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 1;
        $field[$key]["ismethod"] = 1;
        $key++;

        $field[$key]["fieldstring"] = "absence_type_name";
        $field[$key]["fieldname"] = "请假类型";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 1;
        $key++;

        $field[$key]["fieldstring"] = "absence_reasonnote";
        $field[$key]["fieldname"] = "备注";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 1;
        $key++;

        $field[$key]["fieldstring"] = "absence_status_name";
        $field[$key]["fieldname"] = "请假状态";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 1;
        $key++;

        $field[$key]["fieldstring"] = "applicant";
        $field[$key]["fieldname"] = "申请人";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => '学员暂无请假记录', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }


    //单个学员的课程别余额
    function studentCoursebalanceView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $StudentModel = new \Model\Gmc\SchoolModel($request);
        $res = $StudentModel->studentCoursebalance($request);
        $field = array();

        $k = 0;
        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "course_cnname";
        $field[$k]["fieldname"] = "课程别名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "course_branch";
        $field[$k]["fieldname"] = "课程别编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "study_isreading";
        $field[$k]["fieldname"] = "在班状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "coursebalance_unitexpend";
        $field[$k]["fieldname"] = "耗课单价";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "coursebalance_unitearning";
        $field[$k]["fieldname"] = "收入单价";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "tuition_sellingprice";
        $field[$k]["fieldname"] = "销售价";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "order_paidprice";
        $field[$k]["fieldname"] = "实收金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "order_coupon_price";
        $field[$k]["fieldname"] = "优惠券金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "ordercourse_buynums";
        $field[$k]["fieldname"] = "购买课次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "coursebalance_time";
        $field[$k]["fieldname"] = "剩余课次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "coursebalance_figure";
        $field[$k]["fieldname"] = "课别余额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "course_id";
        $field[$k]["fieldname"] = "课程别id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "courseforward_price";
        $field[$k]["fieldname"] = "课程结转余额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;


        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 0;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => '学员暂无课程信息', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //课程下拉
    function getCourseInfoApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $CourseList = $this->DataControl->selectClear("SELECT c.course_id,c.course_cnname,c.course_branch
                                                      FROM smc_course AS c
				                                      LEFT JOIN smc_fee_pricing AS fp ON c.course_id=fp.course_id
				                                      LEFT JOIN smc_fee_agreement AS fa ON fa.agreement_id=fp.agreement_id
                                                      WHERE c.company_id = '{$request['company_id']}' AND c.course_status = '1' AND fa.company_id = '{$request['company_id']}' AND fa.agreement_status = 1 GROUP BY c.course_id");

        if ($CourseList) {
            $result["list"] = $CourseList;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无课程信息', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //单个学员的账户余额日志
    function studentBalanceView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $StudentModel = new \Model\Gmc\SchoolModel($request);
        $res = $StudentModel->studentBalance($request);
        $field = array();
        $k = 0;

        $field[$k]["fieldstring"] = "balancelog_id";
        $field[$k]["fieldname"] = "余额日志ID";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "balancelog_playname";
        $field[$k]["fieldname"] = "交易类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "fromamount";
        $field[$k]["fieldname"] = "原始金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $field[$k]["is_money"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "balance";
        $field[$k]["fieldname"] = "交易金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $field[$k]["is_money"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "finalamount";
        $field[$k]["fieldname"] = "完成金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $field[$k]["is_money"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "staffer_cnname";
        $field[$k]["fieldname"] = "经办人";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "balancelog_time";
        $field[$k]["fieldname"] = "操作时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 0;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => '学员暂无账户余额日志信息', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //单个学生结转余额
    function studentForwardbalanceView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $StudentModel = new \Model\Gmc\SchoolModel($request);
        $res = $StudentModel->studentForwardbalance($request);
        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "balancelog_id";
        $field[$k]["fieldname"] = "结转余额日志ID";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "balancelog_playname";
        $field[$k]["fieldname"] = "交易类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "balance";
        $field[$k]["fieldname"] = "余额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $field[$k]["is_money"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "staffer_cnname";
        $field[$k]["fieldname"] = "经办人";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "经办校区";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 0;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => '学员暂无结转余额日志信息', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //单个学员优惠券日志
    function studentCouponsView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $StudentModel = new \Model\Gmc\SchoolModel($request);
        $res = $StudentModel->studentCoupons($request);
        $field = array();
        $k = 0;
//        $field[$k]["fieldstring"] = "school_cnname";
//        $field[$k]["fieldname"] = "校区名称";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 0;
//        $k++;
//
//        $field[$k]["fieldstring"] = "school_branch";
//        $field[$k]["fieldname"] = "校区编号";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 0;
//        $k++;

        $field[$k]["fieldstring"] = "coupons_id";
        $field[$k]["fieldname"] = "优惠券ID";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "ticket_cnname";
        $field[$k]["fieldname"] = "优惠券名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "coupons_class";
        $field[$k]["fieldname"] = "来源类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "course";
        $field[$k]["fieldname"] = "适用班种";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "ticket_way";
        $field[$k]["fieldname"] = "优惠模式";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "policy_derateprice";
        $field[$k]["fieldname"] = "优惠券金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "policy_deraterate";
        $field[$k]["fieldname"] = "优惠折扣";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "policy_minprice";
        $field[$k]["fieldname"] = "最低课程金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "coupons_reason";
        $field[$k]["fieldname"] = "申请原因";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "coupons_isuse_name";
        $field[$k]["fieldname"] = "使用状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "coupons_usetime";
        $field[$k]["fieldname"] = "使用时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "coupons_exittime";
        $field[$k]["fieldname"] = "限制时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "coupons_createtime";
        $field[$k]["fieldname"] = "创建时间";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 0;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => '学员暂无优惠券信息', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //单个学员集团产品权限
    function apppropermisView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $StudentModel = new \Model\Gmc\SchoolModel($request);
        $res = $StudentModel->apppropermis($request);
        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "apppropermis_name";
        $field[$k]["fieldname"] = "产品名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "apppropermis_authcode";
        $field[$k]["fieldname"] = "授权码";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "course_cnname";
        $field[$k]["fieldname"] = "课程别名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "course_branch";
        $field[$k]["fieldname"] = "课程别编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "apppropermislog_createtime";
        $field[$k]["fieldname"] = "授权日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "staffer_cnname";
        $field[$k]["fieldname"] = "开通人";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "apppropermislog_endday";
        $field[$k]["fieldname"] = "截止日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "apppropermislog_isenabled";
        $field[$k]["fieldname"] = "是否禁用";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $field[$k]["isswitch"] = 1;


        $result = array();
        $result["field"] = $field;
        if ($res['allnum']) {
            $result["allnum"] = $res['allnum'];
        } else {
            $result["allnum"] = 0;
        }

        if ($res) {
            $result["fieldcustom"] = 0;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => '学员暂无授权产品信息', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //单个学员领用资产
    function studentGoodsView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $StudentModel = new \Model\Gmc\SchoolModel($request);
        $res = $StudentModel->studentGoods($request);
        $field = array();

        $k = 0;
        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "erpgoods_id";
        $field[$k]["fieldname"] = "资产ID";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "goods_cnname";
        $field[$k]["fieldname"] = "商品名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "goods_pid";
        $field[$k]["fieldname"] = "商品编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "order_pid";
        $field[$k]["fieldname"] = "订单编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "ordergoods_buynums";
        $field[$k]["fieldname"] = "数量";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "erpgoods_isreceive";
        $field[$k]["fieldname"] = "是否领用";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "erpgoods_receivetime";
        $field[$k]["fieldname"] = "领用时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "beoutorder_pid";
        $field[$k]["fieldname"] = "出库编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "erpgoods_createtime";
        $field[$k]["fieldname"] = "创建时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "isreceive";
        $field[$k]["fieldname"] = "是否已领用";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "isrestore";
        $field[$k]["fieldname"] = "是否归还";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "isrefund";
        $field[$k]["fieldname"] = "是否退费";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "isfree";
        $field[$k]["fieldname"] = "是否免费";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 0;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => '学员暂无领用资产信息', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //单个学员杂费余额
    function studentCourseGoodsView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $StudentModel = new \Model\Gmc\SchoolModel($request);
        $res = $StudentModel->studentCourseGoods($request);
        $field = array();

        $k = 0;
        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "itemtimes_id";
        $field[$k]["fieldname"] = "收费项目ID";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "feeitem_cnname";
        $field[$k]["fieldname"] = "收费项目名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "feeitem_branch";
        $field[$k]["fieldname"] = "收费项目编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "course_cnname";
        $field[$k]["fieldname"] = "课程别";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $field[$k]["is_money"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "course_branch";
        $field[$k]["fieldname"] = "课程别编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;

        $field[$k]["fieldstring"] = "itemtimes_figure";
        $field[$k]["fieldname"] = "项目剩余余额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "itemtimes_number";
        $field[$k]["fieldname"] = "项目剩余次数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 0;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => '学员暂无杂项余额信息', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //单个学员免费课时列表
    function studentFreeTimesView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $StudentModel = new \Model\Gmc\SchoolModel($request);
        $res = $StudentModel->studentFreeTimes($request);
        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "class_cnname";
        $field[$k]["fieldname"] = "班级名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "class_enname";
        $field[$k]["fieldname"] = "班级别名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "class_branch";
        $field[$k]["fieldname"] = "班级编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "course_cnname";
        $field[$k]["fieldname"] = "课程别名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "course_branch";
        $field[$k]["fieldname"] = "课程别编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "hour_lessontimes";
        $field[$k]["fieldname"] = "课程课时";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "hour_day";
        $field[$k]["fieldname"] = "上课日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "is_use_name";
        $field[$k]["fieldname"] = "是否使用";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "order_pid";
        $field[$k]["fieldname"] = "订单编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "coursetimes_createtime";
        $field[$k]["fieldname"] = "创建时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 0;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => $StudentModel->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //单个学员缴费明细
    function studPaymentView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $StudentModel = new \Model\Gmc\SchoolModel($request);
        $res = $StudentModel->studPayment($request);
        $field = array();

        $k = 0;
        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "school_id";
        $field[$k]["fieldname"] = "学校ID";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "student_id";
        $field[$k]["fieldname"] = "学生ID";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "play_day";
        $field[$k]["fieldname"] = "缴费日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "play_name";
        $field[$k]["fieldname"] = "缴费项目名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "play_amount";
        $field[$k]["fieldname"] = "缴费金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "remk";
        $field[$k]["fieldname"] = "备注";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 0;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => $StudentModel->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //单个学员消费明细
    function stuSpendingView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $StudentModel = new \Model\Gmc\SchoolModel($request);
        $res = $StudentModel->stuSpending($request);
        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "income_type";
        $field[$k]["fieldname"] = "消费类别";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "course_branch";
        $field[$k]["fieldname"] = "消费课程别";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "class_branch";
        $field[$k]["fieldname"] = "班级编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "class_enname";
        $field[$k]["fieldname"] = "班级别名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "play_amount";
        $field[$k]["fieldname"] = "消费金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "play_times";
        $field[$k]["fieldname"] = "消费次数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "st_date";
        $field[$k]["fieldname"] = "开始日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "end_date";
        $field[$k]["fieldname"] = "结束日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "remk";
        $field[$k]["fieldname"] = "备注";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 0;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => $StudentModel->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //单个学员积分明细
    function stuExchangeLogView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $StudentModel = new \Model\Gmc\SchoolModel($request);
        $res = $StudentModel->stuExchangeLog($request);
        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "integrallog_id";
        $field[$k]["fieldname"] = "记录ID";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "integraltype_class_name";
        $field[$k]["fieldname"] = "所属类别";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "integrallog_rule";
        $field[$k]["fieldname"] = "积分类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "integrallog_playname";
        $field[$k]["fieldname"] = "交易名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "integrallog_fromamount";
        $field[$k]["fieldname"] = "原始积分";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "integrallog_playamount";
        $field[$k]["fieldname"] = "交易积分";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "integrallog_finalamount";
        $field[$k]["fieldname"] = "完成积分";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "integrallog_reason";
        $field[$k]["fieldname"] = "交易原因";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "staffer_cnname";
        $field[$k]["fieldname"] = "操作人";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "integrallog_time";
        $field[$k]["fieldname"] = "完成时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 0;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => $StudentModel->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //积分类型下拉
    function getIntegraltypeView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $StudentModel = new \Model\Gmc\SchoolModel($request);
        $res = $StudentModel->getIntegraltype($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $StudentModel->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //单个学员的沟通列表
    function studentTrackView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $StudentModel = new \Model\Gmc\SchoolModel($request);
        $res = $StudentModel->studentTrack($request);
        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "track_id";
        $field[$k]["fieldname"] = "沟通ID";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "commode_id";
        $field[$k]["fieldname"] = "方式ID";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "track_day";
        $field[$k]["fieldname"] = "沟通日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "track_classname";
        $field[$k]["fieldname"] = "沟通类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "track_note";
        $field[$k]["fieldname"] = "沟通内容";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "commode_name";
        $field[$k]["fieldname"] = "沟通方式";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "result_name";
        $field[$k]["fieldname"] = "沟通结果";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "staffer_cnname";
        $field[$k]["fieldname"] = "沟通教师";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "track_createtime";
        $field[$k]["fieldname"] = "创建时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 0;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => '学员暂无沟通记录', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function trackLinkTypeApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $list = $this->DataControl->selectClear("select c.commode_id,c.commode_name from crm_code_commode as c where c.company_id='{$request['company_id']}' order by c.commode_id asc");
        if ($list) {
            $result["list"] = $list;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '获取信息', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function trackTypeApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
//        $list = $this->DataControl->selectClear("select v.list_name,v.list_parameter FROM cms_variablelist as v
//where v.variable_id = '2' ORDER BY v.list_weight ASC");

        $sql = "select tracktype_id as list_parameter,tracktype_name as list_name FROM smc_code_tracktype 
                where company_id = '{$request['company_id']}' 
                ORDER BY tracktype_id ASC";
        $list = $this->DataControl->selectClear($sql);
        if ($list) {
            $result["list"] = $list;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '获取信息', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function trackResultTypeApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $list = $this->DataControl->selectClear("select c.result_id,c.result_name from crm_code_result as c
where c.company_id='{$request['company_id']}' order by c.result_id asc");
        if ($list) {
            $result["list"] = $list;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '获取信息', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //带班记录列表
    function ClassListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $StudentModel = new \Model\Gmc\SchoolModel($request);
        $res = $StudentModel->ClassList($request);

        ajax_return($res, $request['language_type']);
    }

    //教师课表列表
    function TeaTableListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $StudentModel = new \Model\Gmc\SchoolModel($request);
        $res = $StudentModel->TeaTableList($request);

        ajax_return($res, $request['language_type']);
    }

    //上课记录列表
    function ClassTeachingView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $StudentModel = new \Model\Gmc\SchoolModel($request);
        $res = $StudentModel->ClassTeaching($request);

        ajax_return($res, $request['language_type']);
    }

    //单个学生信息
    function studentOneView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $StudentModel = new \Model\Gmc\SchoolModel($request);
        $res = $StudentModel->studentOne($request);
        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '获取信息', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //单个学生金额统计
    function orderInfoApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $StudentModel = new \Model\Gmc\SchoolModel($request);
        $res = $StudentModel->orderInfo($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '获取失败', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //单个学生兑换列表
    function stuExchangeListApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $StudentModel = new \Model\Gmc\SchoolModel($request);
        $res = $StudentModel->stuExchangeList($request);
        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "integralgoods_id";
        $field[$k]["fieldname"] = "兑换ID";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "goods_cnname";
        $field[$k]["fieldname"] = "兑换商品名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "integralgoods_score";
        $field[$k]["fieldname"] = "兑换积分";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "integralgoods_number";
        $field[$k]["fieldname"] = "兑换数量";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "integralgoods_createtime";
        $field[$k]["fieldname"] = "兑换时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

//		$field[$k]["fieldstring"] = "parenter_cnname";
//		$field[$k]["fieldname"] = "兑换人";
//		$field[$k]["show"] = 1;
//		$field[$k]["custom"] = 0;
//		$k++;

        $field[$k]["fieldstring"] = "integralgoods_receivetime";
        $field[$k]["fieldname"] = "领取时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "integralgoods_isreceive_name";
        $field[$k]["fieldname"] = "是否领用";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 0;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => $StudentModel->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //请假课次
    function getApprovedDetailApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);

        $StudentModel = new \Model\Gmc\SchoolModel($request);
        $res = $StudentModel->getApprovedDetail($request);

        $field = array();
        $key = 0;
        $field[$key]["fieldstring"] = "class_cnname";
        $field[$key]["fieldname"] = "班级名称";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 1;
        $key++;

        $field[$key]["fieldstring"] = "class_enname";
        $field[$key]["fieldname"] = "班级别名";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 1;
        $key++;

        $field[$key]["fieldstring"] = "class_branch";
        $field[$key]["fieldname"] = "班级编号";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 1;
        $key++;

        $field[$key]["fieldstring"] = "course_cnname";
        $field[$key]["fieldname"] = "课程别名称";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 1;
        $key++;

        $field[$key]["fieldstring"] = "course_branch";
        $field[$key]["fieldname"] = "课程别编号";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 1;
        $key++;

        $field[$key]["fieldstring"] = "hour_time";
        $field[$key]["fieldname"] = "上课时间";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 1;
        $key++;

        $field[$key]["fieldstring"] = "hour_way_name";
        $field[$key]["fieldname"] = "上课方式";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 1;
        $key++;

        $field[$key]["fieldstring"] = "staffer_cnname";
        $field[$key]["fieldname"] = "主教老师";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 1;
        $key++;

        $field[$key]["fieldstring"] = "hour_isfree_name";
        $field[$key]["fieldname"] = "是否计费";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 1;
        $key++;

        $field[$key]["fieldstring"] = "hour_status_name";
        $field[$key]["fieldname"] = "是否有效";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        $result["absence"] = $res['absence_hournum'];
        if ($res) {
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => '暂无请假课次', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //交易记录
    function studentCourseTransactionApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $StudentModel = new \Model\Gmc\SchoolModel($request);
        $res = $StudentModel->studentCourseTransaction($request);
        $field = array();
        $k = 0;

        $field[$k]["fieldstring"] = "log_playname";
        $field[$k]["fieldname"] = "交易名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "log_playclass";
        $field[$k]["fieldname"] = "交易类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "log_day";
        $field[$k]["fieldname"] = "交易日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "log_playamount";
        $field[$k]["fieldname"] = "金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "log_reason";
        $field[$k]["fieldname"] = "备注";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "log_time";
        $field[$k]["fieldname"] = "操作时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;


        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res['list']) {
            $result["fieldcustom"] = 0;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => '暂无交易记录', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function studentCourseConsumeApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $StudentModel = new \Model\Gmc\SchoolModel($request);
        $res = $StudentModel->studentCourseConsume($request);

        $field = array();
        $k = 0;

        $field[$k]["fieldstring"] = "log_day";
        $field[$k]["fieldname"] = "考勤日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "log_playamount";
        $field[$k]["fieldname"] = "金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "log_frequency";
        $field[$k]["fieldname"] = "上课堂数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "log_reason";
        $field[$k]["fieldname"] = "备注";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "log_time";
        $field[$k]["fieldname"] = "操作时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;


        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 0;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => '暂无交易记录', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function apppropermisApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $StudentModel = new \Model\Gmc\SchoolModel($request);
        $res = $StudentModel->apppropermisTwo($request);
        $field = array();
        $k = 0;

        $field[$k]["fieldstring"] = "apppropermis_name";
        $field[$k]["fieldname"] = "产品名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "apppropermislog_createtime";
        $field[$k]["fieldname"] = "授权日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "staffer_cnname";
        $field[$k]["fieldname"] = "开通人";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "apppropermislog_endday";
        $field[$k]["fieldname"] = "截止日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "apppropermislog_isenabled";
        $field[$k]["fieldname"] = "是否禁用";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;


        $result = array();
        $result["field"] = $field;
        if ($res['allnum']) {
            $result["allnum"] = $res['allnum'];
        } else {
            $result["allnum"] = 0;
        }

        if ($res) {
            $result["fieldcustom"] = 0;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => '学员暂无授权产品信息', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    function stuOtherSchoolBalanceApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $StudentModel = new \Model\Gmc\SchoolModel($request);
        $res = $StudentModel->stuOtherSchoolBalance($request);
        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "school_id";
        $field[$k]["fieldname"] = "学校ID";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "school_shortname";
        $field[$k]["fieldname"] = "校园名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "student_balance";
        $field[$k]["fieldname"] = "账户可退金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "student_withholdbalance";
        $field[$k]["fieldname"] = "账户不可退金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 0;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => '暂无数据', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function getCouponsFileView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $StudentModel = new \Model\Gmc\SchoolModel($request);
        $res = $StudentModel->getCouponsFile($request);

        ajax_return($res, $request['language_type']);
    }

    function goodsConsumeLogApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $StudentModel = new \Model\Gmc\SchoolModel($request);
        $res = $StudentModel->goodsConsumeLog($request);
        $field = array();

        $k = 0;
        $field[$k]["fieldstring"] = "feeitem_class_name";
        $field[$k]["fieldname"] = "杂费类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "log_reason";
        $field[$k]["fieldname"] = "操作明细";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "log_playclass";
        $field[$k]["fieldname"] = "操作类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "log_playamount";
        $field[$k]["fieldname"] = "交易金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $field[$k]["isRed"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "经办校区";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "log_time";
        $field[$k]["fieldname"] = "操作时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 0;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => '暂无数据', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //获取单个职工
    function getStafferOneListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $StudentModel = new \Model\Gmc\SchoolModel($request);
        $res = $StudentModel->getStafferOneList($request);

        ajax_return($res, $request['language_type']);
    }

    //单个学员详细资料
    function studentOneItemApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $StudentModel = new \Model\Gmc\SchoolModel($request);
        $res = $StudentModel->studentOneItem($request);
        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取详细信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '获取详细信息', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //亲属关系下拉
    function getFamilyRelationApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $RelationList = $this->DataControl->selectClear("select familyrelation_code,familyrelation_name from smc_code_familyrelation");
        if ($RelationList) {
            $result["list"] = $RelationList;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '获取信息', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //修改学生资料
    function studentOneEditItemAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);

        $StudentModel = new \Model\Gmc\SchoolModel($request);
        $res = $StudentModel->studentOneEditItem($request);
        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '编辑详细信息成功', 'result' => $result);
            $this->addGmcWorkLog($request['company_id'], $request['staffer_id'], "用户管理", '编辑学员资料', dataEncode($request));
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $StudentModel->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //获取目标设定列表
    function getAchieveTargetListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Gmc\SchoolModel($request);
        $res = $ReportModel->getAchieveTargetList($request);

        $field = array();

        $k = 0;

        $field[$k]["fieldstring"] = "target_id";
        $field[$k]["fieldname"] = "目标id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "school_id";
        $field[$k]["fieldname"] = "学校id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "school_name";
        $field[$k]["fieldname"] = "学校名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "学校编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursetype_id";
        $field[$k]["fieldname"] = "班组id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "coursetype_cnname";
        $field[$k]["fieldname"] = "目标所属班组";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "target_year";
        $field[$k]["fieldname"] = "目标年度";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_settle";
        $field[$k]["fieldname"] = "是否需要学校设定";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "school_settle_name";
        $field[$k]["fieldname"] = "目标状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "register_num";
        $field[$k]["fieldname"] = "年度招生目标";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "register_rate";
        $field[$k]["fieldname"] = "年度招生目标达成率";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "reading_num";
        $field[$k]["fieldname"] = "年度在读目标";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "reading_rate";
        $field[$k]["fieldname"] = "年度在读目标达成率";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "losing_num";
        $field[$k]["fieldname"] = "年度流失限额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "losing_rate";
        $field[$k]["fieldname"] = "年度流失限额率";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res['list']) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => "暂无学校目标设定信息", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function delAchieveTargetAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\SchoolModel($request);

        $result = $this->Model->delAchieveTarget($request);
        ajax_return($result, $request['language_type']);
    }

    //下载目标设定导入模版
    function downloadAchieveTargetAction()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\SchoolModel($request);

        $result = $this->Model->downloadAchieveTarget($request);
        ajax_return($result, $request['language_type']);
    }

    //上传目标设定预览
    function uploadAchieveTargetAction()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $url = $request['url'];

        $ys_array = array('学校名称' => 'school_name'
        , '学校编号' => 'school_branch'
        , '目标所属班组' => 'coursetype_cnname'
        , '目标年度' => 'target_year'
        , '年度招生目标' => 'register_num'
        , '年度在读目标' => 'reading_num'
        , '年度流失限额' => 'losing_num');
        $options=array(
            "ssl"=>array(
                "verify_peer"=>false,
                "verify_peer_name"=>false,
            ),
        );
        file_put_contents('analysis.xls', file_get_contents($url,false,stream_context_create($options)));
        $sqlarray = execl_to_array("analysis.xls", $ys_array);
        array_shift($sqlarray);

        $fieldstring = array('school_name', 'school_branch', 'coursetype_cnname', 'target_year', 'school_settle', 'register_num', 'reading_num', 'losing_num');
        $fieldname = array('学校名称', '学校编号', '目标所属班组', '目标年度', '目标状态', '年度招生目标', '年度在读目标', '年度流失限额');
        $fieldcustom = array("1", "1", "1", "1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1", "1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldname[$i]);
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $settle_status = $this->LgArraySwitch(array("0" => "无需设置", "1" => "需要设置"));

        foreach ($sqlarray as &$row) {
            $row['school_settle'] = $settle_status[$request['school_settle']];
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['list'] = $sqlarray;
        $result['coursetype_id'] = $request['coursetype_id'];
        $result['target_year'] = $request['target_year'];
        $result['school_settle'] = $request['school_settle'];

        $res = array('error' => '0', 'errortip' => '预览成功', 'result' => $result);
        ajax_return($res, $request['language_type']);
    }

    //导入目标设定预览数据
    function importAchieveTargetAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Gmc\SchoolModel($request);
        $res = $Model->importAchieveTarget($request);

        $result = array();
        $result["list"] = $res['newlist'];
        $result["suc"] = $res['suc'];
        $result["fal"] = $res['fal'];
        $result["failed_pid"] = $res['failed_pid'];
        $result["field"] = $res['field'];
        if ($res['suc'] > 0) {
            $res = array('error' => 0, 'errortip' => $Model->errortip, 'result' => $result);
        } else {
            $res = array('error' => 1, 'errortip' => '导入失败', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //获取目标设定月度列表
    function getFailedAchieveTargetView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Gmc\SchoolModel($request);
        $res = $ReportModel->downloadFailedAchieveTarget($request);

        if ($res) {
            $res = array('error' => 0, 'errortip' => '导出成功', 'result' => array());
        } else {
            $res = array('error' => 1, 'errortip' => $ReportModel->errortip, 'result' => array());
        }
        ajax_return($res, $request['language_type']);
    }

    //获取目标设定月度列表
    function getMonthAchieveTargetView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Gmc\SchoolModel($request);
        $res = $ReportModel->getMonthAchieveTarget($request);

        $field = array();
        $k = 0;

        $field[$k]["fieldstring"] = "target_month";
        $field[$k]["fieldname"] = "月份";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "month_sort";
        $field[$k]["fieldname"] = "月度";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "register_num";
        $field[$k]["fieldname"] = "招生目标";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "register_rate";
        $field[$k]["fieldname"] = "招生目标达成率";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "reading_num";
        $field[$k]["fieldname"] = "在读目标";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "reading_rate";
        $field[$k]["fieldname"] = "在读目标达成率";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "losing_num";
        $field[$k]["fieldname"] = "流失限额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "losing_rate";
        $field[$k]["fieldname"] = "流失限额率";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res['list']) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => "暂无学校目标设定信息", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function trasactiontestView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->DataControl->begintransaction();

        $sql="select a.company_id,a.companies_id,a.school_id,a.student_id,a.student_balance,a.student_withholdbalance 
        from smc_student_balance a
        left join smc_student b on a.student_id=b.student_id
        left join smc_school c on a.school_id=c.school_id
        where b.student_branch='{$request['student_branch']}'
        and c.school_branch='{$request['school_branch']}'
        and a.student_balance+a.student_withholdbalance>0
        order by a.student_balance+a.student_withholdbalance desc 
        limit 0,1";
        $balanceList = $this->DataControl->selectOne($sql);
//        var_dump($sql);
        if(!$balanceList){
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => "没有可以变更的主体余额", 'result' => $result);
        }

        $start=$request['num'];
        $sql="select a.company_id,a.companies_id,a.school_id,a.student_id,a.student_balance,a.student_withholdbalance 
        from smc_student_balance a
        where a.student_id='{$balanceList['student_id']}'
        and a.school_id='{$balanceList['school_id']}'
        and a.companies_id<>'{$balanceList['companies_id']}'
        order by a.companies_id desc 
        limit 0,1";

        $newbalanceList = $this->DataControl->selectOne($sql);
//        var_dump($sql);
        if(!$newbalanceList){
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => "没有可以变更的主体", 'result' => $result);
        }
        $data=array();
        $data['student_balance']=$newbalanceList['student_balance']+$balanceList['student_balance'];
        $data['student_withholdbalance']=$newbalanceList['student_withholdbalance']+$balanceList['student_withholdbalance'];

        $new=$this->DataControl->updateData("smc_student_balance", "student_id='{$newbalanceList['student_id']}' and school_id = '{$newbalanceList['school_id']}' and company_id='{$newbalanceList['company_id']}' and companies_id='{$newbalanceList['companies_id']}'", $data);

        $data=array();
        $data['student_balance']=0;
        $data['student_withholdbalance']=0;

        $old=$this->DataControl->updateData("smc_student_balance", "student_id='{$balanceList['student_id']}' and school_id = '{$balanceList['school_id']}' and company_id='{$balanceList['company_id']}' and companies_id='{$balanceList['companies_id']}'", $data);

        $data=array();
        $data['student_branch']=$request['student_branch'];
        $data['school_branch']=$request['school_branch'];
        $data['student_id']=$newbalanceList['student_id'];
        $data['school_id']=$newbalanceList['school_id'];
        $data['old_companies_id']=$balanceList['companies_id'];
        $data['new_companies_id']=$newbalanceList['companies_id'];
        $data['price']=$balanceList['student_balance']+$balanceList['student_withholdbalance'];

        $old=$this->DataControl->insertData("trasaction_log", $data);

        if ($new > 0 && $old > 0) {
            $res = '提交'.$balanceList['companies_id'].'->'.$newbalanceList['companies_id'];
            $this->DataControl->commit();
        } else {
            $res = '回滚';
            $this->DataControl->rollback();
        }

        ajax_return($res, $request['language_type']);
    }

    //结尾魔术函数
    function __destruct()
    {

    }
}
