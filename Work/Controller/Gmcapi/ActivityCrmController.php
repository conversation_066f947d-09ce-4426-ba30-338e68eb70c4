<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/13
 * Time: 23:25
 */

namespace Work\Controller\Gmcapi;


class ActivityCrmController extends viewTpl
{
    public $u;
    public $t;
    public $c;
    public $visitType = "api";
    public $Model;

    //预加载处理类
    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
    }

    function ThisVerify($request){
        $paramArray = array();
        $paramArray['staffer_id'] = $request['staffer_id'];
        $paramArray['company_id'] = $request['company_id'];
        $paramArray['token'] = $request['token'];
        if(!$this->UserLimit($paramArray)){
            $result = array();
            $result["list"] = array();
            $result["tokeninc"] = "0";
            $res = array('error' => '1', 'errortip' => "用户token失效", 'result' => $result);
            ajax_return($res,$request['language_type']);
        }
    }


    //活动列表
    function HomeView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        //model
        $this->CaseModel = new \Model\Gmc\ActivityCrmModel($request);
        $dataList = $this->CaseModel->sellActivity($request);

        $field = array();
        $field[0]["fieldname"] = "activity_name";
        $field[0]["fieldstring"] = "招生活动名称";
        $field[0]["show"] = 1;
        $field[0]["custom"] = 0;

        $field[1]["fieldname"] = "activity_starttime";
        $field[1]["fieldstring"] = "活动时间";
        $field[1]["show"] = 1;
        $field[1]["custom"] = 0;

        $field[2]["fieldname"] = "clientnum";
        $field[2]["fieldstring"] = "名单线索数";
        $field[2]["show"] = 1;
        $field[2]["custom"] = 0;

        $field[3]["fieldname"] = "officialnum";
        $field[3]["fieldstring"] = "转正人数";
        $field[3]["show"] = 1;
        $field[3]["custom"] = 0;

        $field[4]["fieldname"] = "percentconversion";
        $field[4]["fieldstring"] = "转化率";
        $field[4]["show"] = 1;
        $field[4]["custom"] = 0;

        $field[5]["fieldname"] = "qrcode";
        $field[5]["fieldstring"] = "二维码";
        $field[5]["show"] = 1;
        $field[5]["custom"] = 0;
        $field[5]["isqrcode"] = 1;

        $field[6]["fieldname"] = "staffer_cnname";
        $field[6]["fieldstring"] = "发布人";
        $field[6]["show"] = 1;
        $field[6]["custom"] = 0;

        $result = array();
        if($dataList){
            $result["fieldcustom"] = 1;
            $result["field"] = $field;
            $result["list"] = $dataList['datalist'];
            $res = array('error' => '0', 'errortip' => '招生活动管理','allnum' => $dataList['count'], 'result' => $result);
        }else{
            $result["list"] = array();
            $res = array('error' => '1', 'errortip' => '招生活动管理','allnum' => array(), 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }

    //某个活动的详细情况
    function ActivityOneApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        if(!isset($request['activity_id']) || $request['activity_id'] == ''){
            $res = array('error' => '1', 'errortip' => '对应活动必选', 'result' => array());
            ajax_return($res,$request['language_type']);
        }
        //model
        $this->CaseModel = new \Model\Gmc\ActivityCrmModel($request);
        $dataList = $this->CaseModel->ActivityOneApi($request);

        $field = array();
        $field["activity_id"] = "目标id";
        $field["activity_name"] = "活动名称";
        $field["staffer_id"] = "职工ID";
        $field["staffer_cnname"] = "职工姓名";
        $field["activity_starttime"] = "开始时间";
        $field["activity_endtime"] = "结束时间";
        $field["activity_img"] = "主图";
        $field["activity_content"] = "活动内容";
        $field["activity_createtime"] = "创建时间";
        $field["activity_appUrl"] = "二维码链接";

        $result = array();
        if($dataList){
            $result["field"] = $field;
            $result["data"] = $dataList;
            $res = array('error' => '0', 'errortip' => '某个活动的详细情况', 'result' => $result);
        }else{
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '某个活动的详细情况', 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }
    //二维码
    function goalActivityshowimgView(){
        header('Content-Type:image/png');
        $request = Input('get.','','trim,addslashes');
        $codeUrl = $request['imgurl'];
        require(ROOT_PATH . 'Core/Classlib/Webqrcode.php');//二维码生成
        $QRcode = new \QRcode();
        $errorCorrectionLevel = 'H';//容错级别
        $matrixPointSize = 15;//生成图片大小
        echo $QRcode->png($codeUrl, false, $errorCorrectionLevel, $matrixPointSize, 2);
    }

    //添加活动
    function addActivityAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        if(!isset($request['activity_name']) || $request['activity_name'] == ''){
            $res = array('error' => '1', 'errortip' => '活动名称不能为空', 'result' => array());
            ajax_return($res,$request['language_type']);
        }
        //model
        $this->CaseModel = new \Model\Gmc\ActivityCrmModel($request);
        $dataList = $this->CaseModel->addActivityAction($request);

        if($dataList){
            $res = array('error' => '0', 'errortip' => "活动新增成功");
        }else{
            $res = array('error' => '1', 'errortip' => '活动新增失败');
        }
        ajax_return($res,$request['language_type']);
    }

    //修改招生目标
    function updateActivityAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        if(!isset($request['activity_id']) || $request['activity_id'] == ''){
            $res = array('error' => '1', 'errortip' => '对应活动必选', 'result' => array());
            ajax_return($res,$request['language_type']);
        }
        if(!isset($request['activity_name']) || $request['activity_name'] == ''){
            $res = array('error' => '1', 'errortip' => '活动名称不能为空', 'result' => array());
            ajax_return($res,$request['language_type']);
        }

        $goalOne = $this->DataControl->getFieldOne("gmc_sell_activity","activity_id", "activity_id = '{$request['activity_id']}' and staffer_id = '{$request['staffer_id']}' ");
        if ($goalOne) {
            //model
            $this->CaseModel = new \Model\Gmc\ActivityCrmModel($request);
            $dataList = $this->CaseModel->updateActivityAction($request);
            $field = array();
            $field['activity_name'] = "活动标题";
            $field['activity_theme'] = "活动主题";
            $field['activity_starttime'] = "开始时间";
            $field['activity_endtime'] = "结束时间";
            $field['activity_img'] = "主图";
            $field['activity_content'] = "活动内容";
            $field['activity_updatetime'] = "修改时间";
            if ($dataList) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $dataList;
                $res = array('error' => '0', 'errortip' => "活动修改成功", 'result' => $result);
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '活动修改失败', 'result' => $result);
            }
        }else{
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '您没有权限修改', 'result' => $result);
        }

        ajax_return($res,$request['language_type']);
    }

    //活动学校适配表
    function ActivitySchoolApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        if(!isset($request['activity_id']) || $request['activity_id'] == ''){
            $res = array('error' => '1', 'errortip' => '对应活动必选', 'result' => array());
            ajax_return($res,$request['language_type']);
        }

        //model
        $this->CaseModel = new \Model\Gmc\ActivityCrmModel($request);
        $dataList = $this->CaseModel->ActivitySchoolApi($request);

        $field = array();
        $field['school_id'] = "学校id";
        $field['school_branch'] = "校区编号";
        $field['school_cnname'] = "校园名称";
        $field['adaptive'] = "是否适配，大于0为已经适配的";

        if ($dataList) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $dataList['datalist'];
            $res = array('error' => '0', 'errortip' => "活动修改成功", 'allnum'=>$dataList['count'], 'result' => $result);
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '活动修改失败', 'result' => $result);
        }

        ajax_return($res,$request['language_type']);
    }

    //活动学校适配表 -- 活动学校批量适配操作
    function batchSchoolAction(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $edwhere =" 1";

        if(isset($request['SchoolidList']) && count($request['SchoolidList'])>0){
            $chooseid = "";
            foreach($request['SchoolidList'] as $chick_var){
                $chooseid .= "'{$chick_var}',";
            }
            $idrange = substr($chooseid,0,-1);
            $edwhere .= " and school_id in ({$idrange})";
        }else{
            ajax_return(array('error' => 1,'errortip' => "未选择学校!","bakfuntion"=>"errormotify"));
        }

        $activityOne = $this->DataControl->selectOne("SELECT * FROM crm_sell_activity WHERE activity_id = '{$request['activity_id']}' and company_id = '{$request['company_id']}' ");
        if($request['BatchType'] == '1'){
            $copylist = $this->DataControl->getList("smc_school",$edwhere);
            if($copylist){
                foreach($copylist as $copyvar){
                    if(!$this->DataControl->getOne("crm_sell_activity_school","activity_id='{$request['activity_id']}' and school_id='{$copyvar['school_id']}' and company_id = '{$request['company_id']}' ")){
                        $data = array();
                        $data['company_id'] = $request['company_id'];
                        $data['activity_id'] = $activityOne['activity_id'];
                        $data['school_id'] = $copyvar['school_id'];
                        $this->DataControl->insertData("crm_sell_activity",$data);
                    }
                }
            }
            ajax_return(array('error' => 0,'errortip' => "适配操作成功!","bakfuntion"=>"refreshpage"));
        }elseif($request['BatchType'] == '2'){
            $copylist = $this->DataControl->getList("smc_school",$edwhere);
            if($copylist){
                foreach($copylist as $copyvar){
                    $this->SDataControl->delData('crm_sell_activity_school',"activity_id='{$request['activity_id']}' and school_id='{$copyvar['school_id']}'  and company_id = '{$request['company_id']}' ");
                }
            }
            ajax_return(array('error' => 0,'errortip' => "取消适配操作成功!","bakfuntion"=>"refreshpage"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "操作学校不存在!","bakfuntion"=>"errormotify"));
        }
    }


    //结尾魔术函数
    function __destruct()
    {

    }
}
