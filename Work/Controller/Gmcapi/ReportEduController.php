<?php


namespace Work\Controller\Gmcapi;


class ReportEduController extends viewTpl
{
    public $u;
    public $t;
    public $c;

    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
    }

    //课时数据明细报表
    function HourDataDetailView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);

        $ReportModel = new \Model\Report\Gmc\EducateReportModel($request);
        $res = $ReportModel->HourDataDetail($request);

        $field = array();
        $k = 0;
        if (isset($request['type']) && $request['type'] == '1') {
            $field[$k]["fieldstring"] = "province_name";
            $field[$k]["fieldname"] = "省份";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "school_cnname";
            $field[$k]["fieldname"] = "校区名称";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "school_branch";
            $field[$k]["fieldname"] = "校区编号";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "coursetype_cnname";
            $field[$k]["fieldname"] = "班组名称";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "coursetype_branch";
            $field[$k]["fieldname"] = "班组编号";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;
        } elseif (isset($request['type']) && $request['type'] == '2') {
            $field[$k]["fieldstring"] = "province_name";
            $field[$k]["fieldname"] = "省份";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "school_cnname";
            $field[$k]["fieldname"] = "校区名称";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "school_branch";
            $field[$k]["fieldname"] = "校区编号";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "coursecat_cnname";
            $field[$k]["fieldname"] = "班种名称";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "coursecat_branch";
            $field[$k]["fieldname"] = "班种编号";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;
        } elseif (isset($request['type']) && $request['type'] == '3') {
            $field[$k]["fieldstring"] = "province_name";
            $field[$k]["fieldname"] = "省份";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "school_cnname";
            $field[$k]["fieldname"] = "校区名称";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "school_branch";
            $field[$k]["fieldname"] = "校区编号";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "course_cnname";
            $field[$k]["fieldname"] = "课程别名称";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "course_branch";
            $field[$k]["fieldname"] = "课程别编号";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;
        } else {
            $field[$k]["fieldstring"] = "needtype_cnname";
            $field[$k]["fieldname"] = "需求类型名称";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "needtype_branch";
            $field[$k]["fieldname"] = "需求类型编号";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;
        }

        $field[$k]["fieldstring"] = "attendance_all_num";
        $field[$k]["fieldname"] = "应到人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "attendance_not_num";
        $field[$k]["fieldname"] = "缺勤人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "attendance_num";
        $field[$k]["fieldname"] = "实到人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "attendance_rate";
        $field[$k]["fieldname"] = "出勤率";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "classhour_num";
        $field[$k]["fieldname"] = "上课课时数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "consumehour_num";
        $field[$k]["fieldname"] = "学员累计耗课数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "consumehour_price";
        $field[$k]["fieldname"] = "累计耗课收入";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "consumehour_avgprice";
        $field[$k]["fieldname"] = "耗课课时均价";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res['list']) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => $ReportModel->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //教师带课明细报表
    function teacherClassView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Report\Gmc\EducateReportModel($request);
        $res = $ReportModel->teacherClass($request);
        $field = array();
        $k = 0;

        $field[$k]["fieldstring"] = "school_id";
        $field[$k]["fieldname"] = "学校id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "province_name";
        $field[$k]["fieldname"] = "省份";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "district_cnname";
        $field[$k]["fieldname"] = "所属区域";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "staffer_cnname";
        $field[$k]["fieldname"] = "教师中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "staffer_enname";
        $field[$k]["fieldname"] = "教师英文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "staffer_branch";
        $field[$k]["fieldname"] = "教师编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "teaching_class_num";
        $field[$k]["fieldname"] = "带班数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_all_num";
        $field[$k]["fieldname"] = "带班总人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_all_arrive_num";
        $field[$k]["fieldname"] = "应到人次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_no_checking_num";
        $field[$k]["fieldname"] = "缺勤人次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_is_checking_num";
        $field[$k]["fieldname"] = "实到人次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_all_arrive_rate";
        $field[$k]["fieldname"] = "出勤率";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "to_class_num";
        $field[$k]["fieldname"] = "转入人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "out_class_num";
        $field[$k]["fieldname"] = "转出人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "up_class_num";
        $field[$k]["fieldname"] = "升班人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "leave_class_num";
        $field[$k]["fieldname"] = "班内流失人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_insert_num";
        $field[$k]["fieldname"] = "插班人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res['list']) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => "暂无教师带班明细", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    //教师带课统计表
    function teacherClassStatisticView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Report\Gmc\EducateReportModel($request);
        $res = $ReportModel->teacherClassStatistic($request);
        $field = array();
        $k = 0;

        $field[$k]["fieldstring"] = "school_id";
        $field[$k]["fieldname"] = "学校id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "province_name";
        $field[$k]["fieldname"] = "省份";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "district_cnname";
        $field[$k]["fieldname"] = "所属区域";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "staffer_cnname";
        $field[$k]["fieldname"] = "教师中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "staffer_enname";
        $field[$k]["fieldname"] = "教师英文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "staffer_branch";
        $field[$k]["fieldname"] = "教师编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "staffer_native";
        $field[$k]["fieldname"] = "籍贯";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "post_name";
        $field[$k]["fieldname"] = "职务";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

//        $field[$k]["fieldstring"] = "teachtype_name";
//        $field[$k]["fieldname"] = "教师类型";
//        $field[$k]["show"] = 0;
//        $field[$k]["custom"] = 1;
//        $k++;

        $field[$k]["fieldstring"] = "class_cnname";
        $field[$k]["fieldname"] = "班级名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_enname";
        $field[$k]["fieldname"] = "班级别名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_branch";
        $field[$k]["fieldname"] = "班级编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "course_cnname";
        $field[$k]["fieldname"] = "课程别名称";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "course_branch";
        $field[$k]["fieldname"] = "课程别编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursecat_cnname";
        $field[$k]["fieldname"] = "班种名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursecat_branch";
        $field[$k]["fieldname"] = "班种编号";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursetype_cnname";
        $field[$k]["fieldname"] = "班组名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursetype_branch";
        $field[$k]["fieldname"] = "班组编号";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "main_hour_num_online";
        $field[$k]["fieldname"] = "主教线上课次数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "main_hour_times_online";
        $field[$k]["fieldname"] = "主教线上课时数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "main_hour_num_offline";
        $field[$k]["fieldname"] = "主教线下课次数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "main_hour_times_offline";
        $field[$k]["fieldname"] = "主教线下课时数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "sub_hour_num_online";
        $field[$k]["fieldname"] = "助教线上课次数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "sub_hour_times_online";
        $field[$k]["fieldname"] = "助教线上课时数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "sub_hour_num_offline";
        $field[$k]["fieldname"] = "助教线下课次数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "sub_hour_times_offline";
        $field[$k]["fieldname"] = "助教线下课时数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_num";
        $field[$k]["fieldname"] = "班级人数";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res['list']) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => "暂无教师带课明细", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    //学员课程余额统计报表
    function studentCourseSurplusView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Smc\ReportModel($request);
        $res = $ReportModel->studentCourseSurplus($request);
        $field = array();

        $k = 0;

        $field[$k]["fieldstring"] = "needtype_cnname";
        $field[$k]["fieldname"] = "需求类型名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "needtype_branch";
        $field[$k]["fieldname"] = "需求类型编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "price1";
        $field[$k]["fieldname"] = "课程余额总计";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "price2";
        $field[$k]["fieldname"] = "课程优惠总计";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "count";
        $field[$k]["fieldname"] = "课程剩余课次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res['list']) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => "暂无学员课程余额统计报表", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    //学员课程余额统计报表
    function schoolCourseConsumeView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\ReportModel($request);
        $res = $Model->schoolCourseConsume($request);

        $result = array();

        if ($res) {
            $result["field"] = $res['field'];
            $result["list"] = $res['list'];
            $result["allnum"] = $res['allnum'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $field = array();
            $k = 0;
            $field[$k]["fieldstring"] = "province_name";
            $field[$k]["fieldname"] = "省份";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "school_cnname";
            $field[$k]["fieldname"] = "校区名称";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "school_branch";
            $field[$k]["fieldname"] = "校区编号";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "cnname";
            $field[$k]["fieldname"] = "名称";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "branch";
            $field[$k]["fieldname"] = "编号";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "log_playamount";
            $field[$k]["fieldname"] = "总收入金额";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "timelog_playtimes";
            $field[$k]["fieldname"] = "总收入课次";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $result["field"] = $field;
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    //学费消耗明细报表
    function studentCourseConsumeDetlView()
    {
        $request = Input('get.', '', 'strip_tags,trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Smc\ReportModel($request);
        $res = $ReportModel->studentCourseConsumeDetl($request);

        $k = 0;
        $field = array();

        $field[$k]["fieldstring"] = "school_id";
        $field[$k]["fieldname"] = "学校";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "province_name";
        $field[$k]["fieldname"] = "省份";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_id";
        $field[$k]["fieldname"] = "学员ID";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["fieldname"] = "学员中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_enname";
        $field[$k]["fieldname"] = "学员英文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_branch";
        $field[$k]["fieldname"] = "学员编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursetype_cnname";
        $field[$k]["fieldname"] = "班组名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursetype_branch";
        $field[$k]["fieldname"] = "班组编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursecat_cnname";
        $field[$k]["fieldname"] = "班种名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursecat_branch";
        $field[$k]["fieldname"] = "班种编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "course_cnname";
        $field[$k]["fieldname"] = "课程别名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "course_branch";
        $field[$k]["fieldname"] = "课程别编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_cnname";
        $field[$k]["fieldname"] = "班级名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_enname";
        $field[$k]["fieldname"] = "班级别名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_branch";
        $field[$k]["fieldname"] = "班级编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "timelog_playtimes";
        $field[$k]["fieldname"] = "消耗课次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "log_playamount";
        $field[$k]["fieldname"] = "消耗金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "log_time";
        $field[$k]["fieldname"] = "消耗时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res['list']) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => "暂无学费消耗明细报表", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    //学员免费赠课--X
    function studentFreeCourseView()
    {
        $request = Input('get.', '', 'strip_tags,trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Report\Gmc\StudentReportModel($request);
        $res = $ReportModel->studentFreeCourseReport($request);

        $k = 0;
        $field = array();
        $field[$k]["fieldstring"] = "trading_pid";
        $field[$k]["fieldname"] = "交易编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_id";
        $field[$k]["fieldname"] = "分校id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "province_name";
        $field[$k]["fieldname"] = "省份";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "分校编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "分校名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_id";
        $field[$k]["fieldname"] = "学生id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_branch";
        $field[$k]["fieldname"] = "学生编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["fieldname"] = "学生姓名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "channel_name";
        $field[$k]["fieldname"] = "专案名称";
        $field[$k]["show"] = (isset($request["company_id"]) && $request["company_id"] == '8888') ? 1 : 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursecat_id";
        $field[$k]["fieldname"] = "班种id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursecat_branch";
        $field[$k]["fieldname"] = "班种编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "course_id";
        $field[$k]["fieldname"] = "课程别id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "course_branch";
        $field[$k]["fieldname"] = "课程别编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "order_alltimes";
        $field[$k]["fieldname"] = "赠送总课次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_id";
        $field[$k]["fieldname"] = "班级id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_branch";
        $field[$k]["fieldname"] = "班级编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_cnname";
        $field[$k]["fieldname"] = "班级名称";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_enname";
        $field[$k]["fieldname"] = "班级别名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "hour_lessontimes";
        $field[$k]["fieldname"] = "赠送指定课次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "order_updatatime";
        $field[$k]["fieldname"] = "赠送日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => "暂无免费课时信息", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }


    //主教老师留班统计表
    function bishopClassrenewView()
    {
        ini_set("memory_limit", '64M');
        set_time_limit(600);
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Report\Gmc\EducateReportModel($request);
        $res = $ReportModel->bishopClassrenew($request);

        $field = array();
        $k = 0;

        $field[$k]["fieldstring"] = "school_id";
        $field[$k]["fieldname"] = "学校id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "province_name";
        $field[$k]["fieldname"] = "省份";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_tagbak";
        $field[$k]["fieldname"] = "督导";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_name";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "taffer_id";
        $field[$k]["fieldname"] = "教师id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "staffer_cnname";
        $field[$k]["fieldname"] = "教师中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "staffer_enname";
        $field[$k]["fieldname"] = "教师英文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "staffer_branch";
        $field[$k]["fieldname"] = "教师编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "job_name";
        $field[$k]["fieldname"] = "职称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursecat_branch";
        $field[$k]["fieldname"] = "班种代码";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursecat_cnname";
        $field[$k]["fieldname"] = "班种名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "course_branch";
        $field[$k]["fieldname"] = "课程别代码";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "course_cnname";
        $field[$k]["fieldname"] = "课程别名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_branch";
        $field[$k]["fieldname"] = "班级编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_enname";
        $field[$k]["fieldname"] = "班级别名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_endmonth";
        $field[$k]["fieldname"] = "结班月份";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "all_num";
        $field[$k]["fieldname"] = "总人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "next_num";
        $field[$k]["fieldname"] = "续费人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "renew_num";
        $field[$k]["fieldname"] = "留班人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "renew_rate";
        $field[$k]["fieldname"] = "留班率";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "teach_rate";
        $field[$k]["fieldname"] = "带课比例";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res['list']) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => "暂无教师续费率信息", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function renewClasspackdetaView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Report\Gmc\EducateReportModel($request);
        $res = $ReportModel->coursepackRenewDetl($request);
        $field = array();

        $k = 0;

        $field[$k]["fieldstring"] = "school_id";
        $field[$k]["fieldname"] = "学校id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "school_tagbak";
        $field[$k]["fieldname"] = "督导";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "province_name";
        $field[$k]["fieldname"] = "省份";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_name";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["fieldname"] = "学员中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_enname";
        $field[$k]["fieldname"] = "学员英文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_branch";
        $field[$k]["fieldname"] = "学员编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursetype";
        $field[$k]["fieldname"] = "班组";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursecat";
        $field[$k]["fieldname"] = "班种";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "course_branch";
        $field[$k]["fieldname"] = "课程别编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "course_cnname";
        $field[$k]["fieldname"] = "课程别名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "hour_day";
        $field[$k]["fieldname"] = "应续包日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursebalance_figure";
        $field[$k]["fieldname"] = "班组剩余金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursebalance_time";
        $field[$k]["fieldname"] = "班组剩余次数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["ismethod"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "next_id";
        $field[$k]["fieldname"] = "是否续包";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res['list']) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => "暂无学员续包信息", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function renewTcpackdetaView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Report\Gmc\EducateReportModel($request);
        $res = $ReportModel->coursepackRenewTeach($request);
        $field = array();

        $k = 0;

        $field[$k]["fieldstring"] = "school_id";
        $field[$k]["fieldname"] = "学校id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "school_tagbak";
        $field[$k]["fieldname"] = "督导";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "province_name";
        $field[$k]["fieldname"] = "省份";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_name";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "staffer_branch";
        $field[$k]["fieldname"] = "教师编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "staffer_cnname";
        $field[$k]["fieldname"] = "教师中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursetype_branch";
        $field[$k]["fieldname"] = "班组编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursetype_cnname";
        $field[$k]["fieldname"] = "班组名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_num";
        $field[$k]["fieldname"] = "带班数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_num";
        $field[$k]["fieldname"] = "应付费人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "buying_num";
        $field[$k]["fieldname"] = "已付费人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "buying_rate";
        $field[$k]["fieldname"] = "续包率";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res['list']) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => "暂无学员续包信息", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

}
