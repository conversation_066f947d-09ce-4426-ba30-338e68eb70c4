<?php


namespace Work\Controller\Gmcapi;


class ReportController extends viewTpl
{
    public $u;
    public $t;
    public $c;

    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
    }

    function studentBalanceView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Gmc\ReportModel($request);
        $res = $ReportModel->studentBalance($request);

        $field = array();
        $field[0]["fieldstring"] = "student_id";
        $field[0]["fieldname"] = "学员ID";
        $field[0]["show"] = 0;
        $field[0]["custom"] = 0;

        $field[1]["fieldstring"] = "student_cnname";
        $field[1]["fieldname"] = "学员中文名";
        $field[1]["show"] = 1;
        $field[1]["custom"] = 1;

        $field[2]["fieldstring"] = "student_enname";
        $field[2]["fieldname"] = "学员英文名";
        $field[2]["show"] = 1;
        $field[2]["custom"] = 1;

        $field[3]["fieldstring"] = "student_branch";
        $field[3]["fieldname"] = "学员编号";
        $field[3]["show"] = 1;
        $field[3]["custom"] = 1;

        $field[4]["fieldstring"] = "student_balance";
        $field[4]["fieldname"] = "学员账户余额";
        $field[4]["show"] = 1;
        $field[4]["custom"] = 1;

        $field[5]["fieldstring"] = "course_cnname";
        $field[5]["fieldname"] = "课程别";
        $field[5]["show"] = 1;
        $field[5]["custom"] = 1;

        $field[6]["fieldstring"] = "course_branch";
        $field[6]["fieldname"] = "课程别编号";
        $field[6]["show"] = 1;
        $field[6]["custom"] = 1;

        $field[7]["fieldstring"] = "coursebalance_figure";
        $field[7]["fieldname"] = "课程余额";
        $field[7]["show"] = 1;
        $field[7]["custom"] = 1;

        $field[8]["fieldstring"] = "coursebalance_time";
        $field[8]["fieldname"] = "剩余课次";
        $field[8]["show"] = 1;
        $field[8]["custom"] = 1;

        $field[9]["fieldstring"] = "courseforward_price";
        $field[9]["fieldname"] = "结转余额";
        $field[9]["show"] = 1;
        $field[9]["custom"] = 1;


        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => $ReportModel->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }


    //学员教材购买报表
    function StuMaterialReportView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Smc\ReportModel($request);
        $res = $ReportModel->StuMaterialReport($request);

        $field = array();
        $field[0]["fieldstring"] = "student_cnname";
        $field[0]["fieldname"] = "学员中文名";
        $field[0]["show"] = 1;
        $field[0]["custom"] = 1;

        $field[1]["fieldstring"] = "student_enname";
        $field[1]["fieldname"] = "学员英文名";
        $field[1]["show"] = 1;
        $field[1]["custom"] = 1;

        $field[2]["fieldstring"] = "student_branch";
        $field[2]["fieldname"] = "学员编号";
        $field[2]["show"] = 1;
        $field[2]["custom"] = 1;

        $field[3]["fieldstring"] = "beoutorder_pid";
        $field[3]["fieldname"] = "出库编号";
        $field[3]["show"] = 1;
        $field[3]["custom"] = 1;

        $field[4]["fieldstring"] = "goods_cnname";
        $field[4]["fieldname"] = "货品名称";
        $field[4]["show"] = 1;
        $field[4]["custom"] = 1;

        $field[5]["fieldstring"] = "goods_pid";
        $field[5]["fieldname"] = "货品编号";
        $field[5]["show"] = 1;
        $field[5]["custom"] = 1;

        $field[6]["fieldstring"] = "prodtype_name";
        $field[6]["fieldname"] = "货品类别";
        $field[6]["show"] = 1;
        $field[6]["custom"] = 1;

        $field[7]["fieldstring"] = "goods_unit";
        $field[7]["fieldname"] = "单位";
        $field[7]["show"] = 1;
        $field[7]["custom"] = 1;

        $field[8]["fieldstring"] = "goods_vipprice";
        $field[8]["fieldname"] = "销售价格";
        $field[8]["show"] = 1;
        $field[8]["custom"] = 1;

        $field[9]["fieldstring"] = "ordergoods_buynums";
        $field[9]["fieldname"] = "购买数量";
        $field[9]["show"] = 1;
        $field[9]["custom"] = 1;

        $field[9]["fieldstring"] = "allprice";
        $field[9]["fieldname"] = "销售金额";
        $field[9]["show"] = 1;
        $field[9]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => "暂无学员教材购买记录", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }


    //进销存汇总报表
    function SalesOrderReportView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Smc\ReportModel($request);
        $res = $ReportModel->SalesOrderReport($request);

        $field = array();
        $field[0]["fieldstring"] = "goods_cnname";
        $field[0]["fieldname"] = "货品名称";
        $field[0]["show"] = 1;
        $field[0]["custom"] = 1;

        $field[1]["fieldstring"] = "goods_pid";
        $field[1]["fieldname"] = "货品编号";
        $field[1]["show"] = 1;
        $field[1]["custom"] = 1;

        $field[2]["fieldstring"] = "prodtype_name";
        $field[2]["fieldname"] = "货品类别";
        $field[2]["show"] = 1;
        $field[2]["custom"] = 1;

        $field[3]["fieldstring"] = "goods_unit";
        $field[3]["fieldname"] = "单位";
        $field[3]["show"] = 1;
        $field[3]["custom"] = 1;

        $field[4]["fieldstring"] = "goods_repertory";
        $field[4]["fieldname"] = "库存数量";
        $field[4]["show"] = 1;
        $field[4]["custom"] = 1;

        $field[5]["fieldstring"] = "goods_vipprice";
        $field[5]["fieldname"] = "货品价格";
        $field[5]["show"] = 1;
        $field[5]["custom"] = 1;


        $field[5]["fieldstring"] = "allprice";
        $field[5]["fieldname"] = "总金额";
        $field[5]["show"] = 1;
        $field[5]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => "暂无进销存汇总记录", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    //学员账户余额明细
    function studentTimebalanceView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Gmc\ReportModel($request);
        $res = $ReportModel->studentTimebalance($request);

        $field = array();
        $field[0]["fieldstring"] = "student_id";
        $field[0]["fieldname"] = "学员ID";
        $field[0]["show"] = 0;
        $field[0]["custom"] = 0;

        $field[1]["fieldstring"] = "student_cnname";
        $field[1]["fieldname"] = "学员中文名";
        $field[1]["show"] = 1;
        $field[1]["custom"] = 1;

        $field[2]["fieldstring"] = "student_enname";
        $field[2]["fieldname"] = "学员英文名";
        $field[2]["show"] = 1;
        $field[2]["custom"] = 1;

        $field[3]["fieldstring"] = "student_branch";
        $field[3]["fieldname"] = "学员编号";
        $field[3]["show"] = 1;
        $field[3]["custom"] = 1;

        $field[4]["fieldstring"] = "student_balance";
        $field[4]["fieldname"] = "学员账户余额";
        $field[4]["show"] = 1;
        $field[4]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => "暂无学员账户余额信息", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //学员课程余额导出
    function studentTimebalanceAction()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $schoolOne = $this->DataControl->getFieldOne('Gmc_school', "school_cnname", "school_id='{$request['school_id']}'");
        $datawhere = "s.company_id='{$request['company_id']}' and s.student_id in (SELECT e.student_id FROM Gmc_student_enrolled as e WHERE e.school_id='{$request['school_id']}')";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (s.student_cnname like '%{$request['keyword']}%' or s.student_enname like '%{$request['keyword']}%' or s.student_idcard like '%{$request['keyword']}%' or s.student_branch like '%{$request['keyword']}%')";
        }
        $endqueryday = date("Y-m-d");
        if (isset($request['endtime']) && $request['endtime'] !== '') {
            $endqueryday = $request['endtime'];
        }
        $endqueryTimes = strtotime($endqueryday) + (3600 * 24);

//        $sql="select s.student_id,s.student_cnname,s.student_enname,s.student_branch
//FROM Gmc_student as s WHERE {$datawhere} ORDER BY s.student_id desc ";


        $sql = "select s.student_id,s.student_cnname,s.student_enname,s.student_branch
                ,(SELECT l.balancelog_finalamount FROM Gmc_student_balancelog as l
WHERE l.balancelog_time < '{$endqueryTimes}' and l.student_id = s.student_id and l.school_id = '{$request['school_id']}' ORDER BY l.balancelog_time DESC,l.balancelog_id DESC limit 0,1) as student_balance
FROM Gmc_student as s WHERE {$datawhere} HAVING  student_balance>0 ORDER BY s.student_id desc";

        $dateexcelarray = $this->DataControl->selectClear($sql);
        if (!$dateexcelarray) {
            $this->error = true;
            $this->errortip = "无学员数据";
            return false;
        }

        $outexceldate = array();
        if ($dateexcelarray) {
            $outexceldate = array();
            foreach ($dateexcelarray as $dateexcelvar) {
                $datearray = array();
                $datearray['student_id'] = $dateexcelvar['student_id'];
                $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                $datearray['student_enname'] = $dateexcelvar['student_enname'];
                $datearray['student_branch'] = $dateexcelvar['student_branch'];
                $datearray['student_balance'] = $dateexcelvar['student_balance'];

                $outexceldate[] = $datearray;
            }
        }
        $excelheader = array("学员ID", "学员中文名", "学员英文名", "学员编号", "账户余额");
        $excelfileds = array('student_id', 'student_cnname', 'student_enname', 'student_branch', 'student_balance');
        query_to_excel($excelheader, $outexceldate, $excelfileds, "{$schoolOne['school_cnname']}学员账户余额明细报表{$endqueryday}.xlsx");
        exit;
    }

    //学员课程余额明细
    function studentUnpaidView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Gmc\ReportModel($request);
        $res = $ReportModel->studentUnpaid($request);

        $field = array();
        $field[0]["fieldstring"] = "student_id";
        $field[0]["fieldname"] = "学员ID";
        $field[0]["show"] = 0;
        $field[0]["custom"] = 0;

        $field[1]["fieldstring"] = "student_cnname";
        $field[1]["fieldname"] = "学员中文名";
        $field[1]["show"] = 1;
        $field[1]["custom"] = 1;

        $field[2]["fieldstring"] = "student_enname";
        $field[2]["fieldname"] = "学员英文名";
        $field[2]["show"] = 1;
        $field[2]["custom"] = 1;

        $field[3]["fieldstring"] = "student_branch";
        $field[3]["fieldname"] = "学员编号";
        $field[3]["show"] = 1;
        $field[3]["custom"] = 1;

        $field[4]["fieldstring"] = "student_balance";
        $field[4]["fieldname"] = "学员账户余额";
        $field[4]["show"] = 1;
        $field[4]["custom"] = 1;

        $field[5]["fieldstring"] = "course_cnname";
        $field[5]["fieldname"] = "课程别";
        $field[5]["show"] = 1;
        $field[5]["custom"] = 1;

        $field[6]["fieldstring"] = "course_branch";
        $field[6]["fieldname"] = "课程别编号";
        $field[6]["show"] = 1;
        $field[6]["custom"] = 1;

        $field[7]["fieldstring"] = "coursebalance_figure";
        $field[7]["fieldname"] = "课程余额";
        $field[7]["show"] = 1;
        $field[7]["custom"] = 1;

        $field[8]["fieldstring"] = "coursebalance_time";
        $field[8]["fieldname"] = "剩余课次";
        $field[8]["show"] = 1;
        $field[8]["custom"] = 1;

        /*$field[9]["fieldstring"] = "courseforward_price";
        $field[9]["fieldname"] = "结转余额";
        $field[9]["show"] = 1;
        $field[9]["custom"] = 1;*/


        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => "暂无学员课程余额明细", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //学员课程余额导出
    function studentUnpaidAction()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);

        $schoolOne = $this->DataControl->getFieldOne('Gmc_school', "school_cnname", "school_id='{$request['school_id']}'");
        $datawhere = "t.student_id = s.student_id and t.course_id = c.course_id and s.company_id='{$request['company_id']}' and s.student_id in (SELECT e.student_id FROM Gmc_student_enrolled as e WHERE e.school_id='{$request['school_id']}')";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (s.student_cnname like '%{$request['keyword']}%' or s.student_enname like '%{$request['keyword']}%' or s.student_idcard like '%{$request['keyword']}%' or s.student_branch like '%{$request['keyword']}%')";
        }
        $endqueryday = date("Y-m-d");
        if (isset($request['endtime']) && $request['endtime'] !== '') {
            $endqueryday = $request['endtime'];
        }
        if (isset($request['starttime']) && $request['starttime'] !== '') {
            $startqueryday = $request['starttime'];
        } else {
            $startqueryday = date("Y-m-d", time());
        }
        $endqueryTimes = strtotime($endqueryday) + (3600 * 24);

        $sql = "select s.student_id,s.student_cnname,s.student_enname,s.student_branch,
(SELECT l.balancelog_finalamount FROM Gmc_student_balancelog as l
WHERE l.balancelog_time < '{$endqueryTimes}' and l.student_id = s.student_id and l.school_id = '{$request['school_id']}' and l.balancelog_class='0' ORDER BY l.balancelog_time DESC,l.balancelog_id DESC limit 0,1) as balance,
(SELECT l.balancelog_finalamount FROM Gmc_student_balancelog as l
WHERE l.balancelog_time < '{$endqueryTimes}' and l.student_id = s.student_id and l.school_id = '{$request['school_id']}' and l.balancelog_class='2' ORDER BY l.balancelog_time DESC,l.balancelog_id DESC limit 0,1) as student_withholdbalance,
(SELECT l.log_finalamount FROM Gmc_student_coursebalance_log as l
WHERE l.log_time < '{$endqueryTimes}' and l.student_id = s.student_id and l.course_id = c.course_id and l.school_id = '{$request['school_id']}' and l.log_class='0' ORDER BY l.log_time DESC,l.log_id DESC limit 0,1) as coursebalance_figure
,(SELECT l.timelog_finaltimes FROM Gmc_student_coursebalance_timelog as l
WHERE l.timelog_time < '{$endqueryTimes}' and l.student_id = s.student_id and l.course_id = c.course_id and l.school_id = '{$request['school_id']}' ORDER BY l.timelog_time DESC,l.timelog_id DESC limit 0,1) as coursebalance_time,
c.course_id,c.course_cnname,c.course_branch
from Gmc_student as s,Gmc_student_coursebalance_timelog as t,Gmc_course as c
WHERE {$datawhere} GROUP BY t.student_id,t.course_id
            HAVING (coursebalance_figure>0 or coursebalance_time>0)
            ORDER BY s.student_id desc";
        $dateexcelarray = $this->DataControl->selectClear($sql);
        if (!$dateexcelarray) {
            $this->error = true;
            $this->errortip = "无学员数据";
            return false;
        }
        foreach ($dateexcelarray as &$one) {
            $one['student_balance'] = $one['balance'] + $one['student_withholdbalance'];
        }

        $outexceldate = array();
        if ($dateexcelarray) {
            $outexceldate = array();
            foreach ($dateexcelarray as $dateexcelvar) {
                $datearray = array();
                $datearray['student_id'] = $dateexcelvar['student_id'];
                $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                $datearray['student_enname'] = $dateexcelvar['student_enname'];
                $datearray['student_branch'] = $dateexcelvar['student_branch'];
                $datearray['course_cnname'] = $dateexcelvar['course_cnname'];
                $datearray['course_branch'] = $dateexcelvar['course_branch'];
                $datearray['coursebalance_figure'] = $dateexcelvar['coursebalance_figure'];
                $datearray['coursebalance_time'] = $dateexcelvar['coursebalance_time'];
                $datearray['student_balance'] = $dateexcelvar['student_balance'];
                $outexceldate[] = $datearray;
            }
        }
        $excelheader = array("学员ID", "学员中文名", "学员英文名", "学员编号", "课程别名称", "课程别编号", "课程余额", "剩余课程", "账户余额");
        $excelfileds = array('student_id', 'student_cnname', 'student_enname', 'student_branch', 'course_cnname', 'course_branch', 'coursebalance_figure', 'coursebalance_time', 'student_balance');
        $tem_name = $schoolOne['school_cnname'] . '学员课程余额明细报表' . $endqueryday . '.xls';
        query_to_excel($excelheader, $outexceldate, $excelfileds, $tem_name);
    }

    function weeklyNumberAction()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $schoolOne = $this->DataControl->getFieldOne('Gmc_school', "school_cnname", "school_id='{$request['school_id']}'");
        $datawhere = " 1 ";
        $time = date("Y-m-d", time());
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (c.class_cnname like '%{$request['keyword']}%' or c.class_branch like '%{$request['keyword']}%' or sc.course_branch like '%{$request['keyword']}%' or sc.course_cnname like '%{$request['keyword']}%')";
        }

        if (isset($request['starttime']) && $request['starttime'] !== '') {


            $time = $request['starttime'];
        }

        if (isset($request['endtime']) && $request['endtime'] !== '') {

        }


        $week_start_day = date("Y-m-d", strtotime($time));
        $week_end_day = date("Y-m-d", strtotime("$week_start_day  +  6 days"));

        $in = "'A02','A03','A04','A05'";
        $to = "'A07','B01','B02','B03','B04','B06','E01','B07'";
//        $last_week_start_day=date("Y-m-d",strtotime("$week_start_day - 7 days"));
//        $last_week_end_day=date("Y-m-d",strtotime("$week_start_day - 1 days"));

        $sql = "select cc.coursecat_cnname,cc.coursecat_branch,c.class_cnname,c.class_branch,c.class_enname,c.class_stdate,c.class_enddate,c.class_timestr,sc.course_classnum
              ,(select count(ssch.hour_id) from Gmc_class_hour as ssch where ssch.class_id=c.class_id and ssch.hour_ischecking=1 and ssch.hour_day<='{$week_end_day}') as hournum
              ,group_concat(distinct(s.staffer_cnname)) as cnteacher
              ,(select count(st.study_id) from Gmc_student_study as st where st.class_id=c.class_id and st.study_endday>='{$week_end_day}'  and st.company_id='{$request['company_id']}' and st.school_id='{$request['school_id']}' ) as weeknum
              from Gmc_class as c
              left join Gmc_course as sc on sc.course_id=c.course_id
              left join Gmc_code_coursecat as cc on cc.coursecat_id=sc.coursecat_id
              left join Gmc_class_hour_teaching as ct on ct.class_id=c.class_id
              left join Gmc_staffer as s on s.staffer_id=ct.staffer_id
              where {$datawhere} and c.company_id='{$request['company_id']}' and c.school_id='{$request['school_id']}' and c.class_stdate <= '{$week_end_day}' and  c.class_enddate >='{$week_start_day}' and c.class_status > '-1'
              group by c.class_id
              order by cc.coursecat_id desc,c.class_id DESC
              ";

        $dateexcelarray = $this->DataControl->selectClear($sql);

        if (!$dateexcelarray) {
            $this->error = true;
            $this->errortip = "无学员数据";
            return false;
        }
        foreach ($dateexcelarray as &$val) {
            $val['classTime'] = $val['class_stdate'] . '-' . $val['class_enddate'];
            $val['classInfo'] = $val['hournum'] . '/' . $val['course_classnum'];
//            $val['weeknum']=$val['inNum']-$val['toNum'];
        }

        $outexceldate = array();
        if ($dateexcelarray) {
            $outexceldate = array();
            foreach ($dateexcelarray as $dateexcelvar) {
                $datearray = array();
                $datearray['coursecat_cnname'] = $dateexcelvar['coursecat_cnname'];
                $datearray['coursecat_branch'] = $dateexcelvar['coursecat_branch'];
                $datearray['class_cnname'] = $dateexcelvar['class_cnname'];
                $datearray['class_enname'] = $dateexcelvar['class_enname'];
                $datearray['class_branch'] = $dateexcelvar['class_branch'];
                $datearray['classTime'] = $dateexcelvar['classTime'];
                $datearray['class_timestr'] = $dateexcelvar['class_timestr'];
                $datearray['classInfo'] = $dateexcelvar['classInfo'];
                $datearray['cnteacher'] = $dateexcelvar['cnteacher'];
                $datearray['weeknum'] = $dateexcelvar['weeknum'];
                $outexceldate[] = $datearray;
            }
        }

        $excelheader = array("班种", "班种编号", "班级名称", "英文名称", "班级编号", "起止日期", "上课时段", "已上课次数", "教师姓名", "本周人数");
        $excelfileds = array('coursecat_cnname', 'coursecat_branch', 'class_cnname', 'class_enname', 'class_branch', 'classTime', 'class_timestr', 'classInfo', 'cnteacher', 'weeknum');
        query_to_excel($excelheader, $outexceldate, $excelfileds, "{$schoolOne['school_cnname']}班级周人数报表{$week_start_day}-{$week_end_day}.xlsx");


    }

    function classInfoListView()
    {

        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);

        $ReportModel = new \Model\Gmc\ReportModel($request);
        $res = $ReportModel->classInfoList($request);

        $field = array();
        $field[0]["fieldstring"] = "class_cnname";
        $field[0]["fieldname"] = "班级名称";
        $field[0]["show"] = 1;
        $field[0]["custom"] = 1;

        $field[1]["fieldstring"] = "class_enname";
        $field[1]["fieldname"] = "班级别名";
        $field[1]["show"] = 1;
        $field[1]["custom"] = 1;

        $field[2]["fieldstring"] = "class_branch";
        $field[2]["fieldname"] = "班级编号";
        $field[2]["show"] = 1;
        $field[2]["custom"] = 1;

        $field[3]["fieldstring"] = "student_cnname";
        $field[3]["fieldname"] = "学员名称";
        $field[3]["show"] = 1;
        $field[3]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => "暂无班级学员信息", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    function classInfoAction()
    {

        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);


        $datawhere = "1";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (c.class_cnname like '%{$request['keyword']}%' or c.class_enname like '%{$request['keyword']}%' or c.class_branch like '%{$request['keyword']}%' )";
        }

        $schoolOne = $this->DataControl->getFieldOne('Gmc_school', "school_cnname", "school_id='{$request['school_id']}'");

        $time = date("Y-m-d", time());
        if (isset($request['starttime']) && $request['starttime'] !== '') {

            $time = $request['starttime'];
        }
        $week_start_day = date("Y-m-d", strtotime($time));
        $week_end_day = date("Y-m-d", strtotime("$week_start_day  +  6 days"));

        $sql = "select c.class_cnname,c.class_enname,c.class_branch,s.student_cnname
              from Gmc_student_study as ss
              left join Gmc_class as c on c.class_id=ss.class_id
              left join Gmc_student as s on s.student_id=ss.student_id
              where {$datawhere} and ss.company_id='{$request['company_id']}' and ss.school_id='{$request['school_id']}'
               and c.class_stdate <= '{$week_end_day}' and c.class_enddate >='{$week_start_day}' and ss.study_endday>='{$week_end_day}'
              order by ss.class_id desc
        ";

        $dateexcelarray = $this->DataControl->selectClear($sql);

        if (!$dateexcelarray) {
            $this->error = true;
            $this->errortip = "无数据";
            return false;
        }

        $outexceldate = array();
        if ($dateexcelarray) {
            $outexceldate = array();
            foreach ($dateexcelarray as $dateexcelvar) {
                $datearray = array();
                $datearray['class_cnname'] = $dateexcelvar['class_cnname'];
                $datearray['class_enname'] = $dateexcelvar['class_enname'];
                $datearray['class_branch'] = $dateexcelvar['class_branch'];
                $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                $outexceldate[] = $datearray;
            }
        }

        $excelheader = array("班级名称", '班级别名', "班级编号", "学员名称");
        $excelfileds = array('class_cnname', 'class_enname', 'class_branch', 'student_cnname');
        query_to_excel($excelheader, $outexceldate, $excelfileds, "{$schoolOne['school_cnname']}班级学员信息报表{$week_start_day}-{$week_end_day}.xlsx");
    }


    function stuOrderReportView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Gmc\ReportModel($request);
        $res = $ReportModel->stuOrderReport($request);

        $field = array();
        $field[0]["fieldstring"] = "order_pid";
        $field[0]["fieldname"] = "订单编号";
        $field[0]["show"] = 1;
        $field[0]["custom"] = 1;

        $field[1]["fieldstring"] = "student_branch";
        $field[1]["fieldname"] = "学员编号";
        $field[1]["show"] = 1;
        $field[1]["custom"] = 1;

        $field[2]["fieldstring"] = "student_cnname";
        $field[2]["fieldname"] = "学员中文名";
        $field[2]["show"] = 1;
        $field[2]["custom"] = 1;

        $field[3]["fieldstring"] = "student_enname";
        $field[3]["fieldname"] = "学员英文名";
        $field[3]["show"] = 1;
        $field[3]["custom"] = 1;

        $field[4]["fieldstring"] = "family_cnname";
        $field[4]["fieldname"] = "家长姓名";
        $field[4]["show"] = 1;
        $field[4]["custom"] = 1;

        $field[5]["fieldstring"] = "order_paymentprice";
        $field[5]["fieldname"] = "订单金额";
        $field[5]["show"] = 1;
        $field[5]["custom"] = 1;

        $field[6]["fieldstring"] = "order_status";
        $field[6]["fieldname"] = "订单状态";
        $field[6]["show"] = 1;
        $field[6]["custom"] = 1;

        $field[7]["fieldstring"] = "order_from";
        $field[7]["fieldname"] = "订单来源";
        $field[7]["show"] = 1;
        $field[7]["custom"] = 1;

        $field[8]["fieldstring"] = "order_type";
        $field[8]["fieldname"] = "收费类型";
        $field[8]["show"] = 1;
        $field[8]["custom"] = 1;

        $field[9]["fieldstring"] = "order_createtime";
        $field[9]["fieldname"] = "下单日期";
        $field[9]["show"] = 1;
        $field[9]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => $ReportModel->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    //收费明细表--X
    function stuPayReportView()
    {
        error_reporting(E_ALL);
        ini_set('display_errors', '1');
        ini_set("memory_limit", '-1');
        set_time_limit(600);
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Report\Gmc\FinanceReportModel($request);
        $res = $ReportModel->stuPayReport($request);

        $k = 0;
        $field = array();
        $field[$k]["fieldstring"] = "school_id";
        $field[$k]["fieldname"] = "校区ID";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "province_name";
        $field[$k]["fieldname"] = "省份";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "ordercourse_buynums";
        $field[$k]["fieldname"] = "课程课次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "trading_pid";
        $field[$k]["fieldname"] = "交易编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "order_pid";
        $field[$k]["fieldname"] = "订单编号";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "pay_pid";
        $field[$k]["fieldname"] = "支付编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "companies_cnname";
        $field[$k]["fieldname"] = "支付企业主体";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_branch";
        $field[$k]["fieldname"] = "学员编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["fieldname"] = "学员中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_enname";
        $field[$k]["fieldname"] = "学员英文名";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "channel_name";
        $field[$k]["fieldname"] = "专案名称";
        $field[$k]["show"] = (isset($request["company_id"]) && $request["company_id"] == '8888') ? 1 : 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "is_register";
        $field[$k]["fieldname"] = "是否新生";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "pay_price";
        $field[$k]["fieldname"] = "支付金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "pay_issuccess";
        $field[$k]["fieldname"] = "支付状态";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "order_from";
        $field[$k]["fieldname"] = "订单来源";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "order_type";
        $field[$k]["fieldname"] = "订单类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "pay_type";
        $field[$k]["fieldname"] = "收费项目类别";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "paychannel_name";
        $field[$k]["fieldname"] = "支付渠道";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "pay_typename";
        $field[$k]["fieldname"] = "支付方式";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "pay_outnumber";
        $field[$k]["fieldname"] = "关联外部单号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursecat_branch";
        $field[$k]["fieldname"] = "收费班种";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "paytype_ischarge";
        $field[$k]["fieldname"] = "收费类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coupons_name";
        $field[$k]["fieldname"] = "优惠券名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "ordercoupons_price";
        $field[$k]["fieldname"] = "优惠券金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "order_createtime";
        $field[$k]["fieldname"] = "下单日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "pay_successtime";
        $field[$k]["fieldname"] = "支付成功日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "order_note";
        $field[$k]["fieldname"] = "订单备注";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "pay_note";
        $field[$k]["fieldname"] = "支付备注";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "companies_issupervise";
        $field[$k]["fieldname"] = "是否监管主体";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "pay_issupervise";
        $field[$k]["fieldname"] = "本笔是否监管";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "main_pay_pid";
        $field[$k]["fieldname"] = "母单支付编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => "暂无学员缴费记录", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    //退费明细表--X-111
    function stuRefundReportView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Smc\ReportModel($request);
        $res = $ReportModel->stuRefundReport($request);

        $k = 0;
        $field = array();
        $field[$k]["fieldstring"] = "school_id";
        $field[$k]["fieldname"] = "学校id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "province_name";
        $field[$k]["fieldname"] = "省份";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "companies_cnname";
        $field[$k]["fieldname"] = "退费支付主体";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "refund_pid";
        $field[$k]["fieldname"] = "订单编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "from_order_pid";
        $field[$k]["fieldname"] = "来源订单号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_branch";
        $field[$k]["fieldname"] = "学员编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["fieldname"] = "学员中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_enname";
        $field[$k]["fieldname"] = "学员英文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "enrolled_status";
        $field[$k]["fieldname"] = "学员状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "enrolled_leavetime";
        $field[$k]["fieldname"] = "流失日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "last_atte_date";
        $field[$k]["fieldname"] = "最后上课日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "family_cnname";
        $field[$k]["fieldname"] = "家长姓名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "refund_price";
        $field[$k]["fieldname"] = "退费金额总计";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "refund_status";
        $field[$k]["fieldname"] = "退款状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "refund_from";
        $field[$k]["fieldname"] = "订单来源";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "refund_type";
        $field[$k]["fieldname"] = "退款类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "trading_createtime";
        $field[$k]["fieldname"] = "制单日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "trading_updatatime";
        $field[$k]["fieldname"] = "完成日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
//        $result["companieslist"] = $res['companieslist'];
        if ($res) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => "暂无学员退费记录", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    //订单欠费报表--X-111
    function stuUnpaidReportView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Report\Gmc\FinanceReportModel($request);
        $res = $ReportModel->orderUnpaidReport($request);


        $k = 0;
        $field = array();

        $field[$k]["fieldstring"] = "companies_cnname";
        $field[$k]["fieldname"] = "主体名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "province_name";
        $field[$k]["fieldname"] = "省份";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_enname";
        $field[$k]["fieldname"] = "检索代码";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_branch";
        $field[$k]["fieldname"] = "学员编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["fieldname"] = "学员中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_enname";
        $field[$k]["fieldname"] = "学员英文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "order_pid";
        $field[$k]["fieldname"] = "订单编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "order_createtime";
        $field[$k]["fieldname"] = "下单日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "order_note";
        $field[$k]["fieldname"] = "订单备注";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "order_from";
        $field[$k]["fieldname"] = "订单来源";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "order_type";
        $field[$k]["fieldname"] = "订单类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "order_status";
        $field[$k]["fieldname"] = "订单状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "order_allprice";
        $field[$k]["fieldname"] = "订单总金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "order_coupon_price";
        $field[$k]["fieldname"] = "优惠券抵扣金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "order_market_price";
        $field[$k]["fieldname"] = "营销活动抵扣金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "order_paymentprice";
        $field[$k]["fieldname"] = "订单应付总金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "order_paidprice";
        $field[$k]["fieldname"] = "订单已付总金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "order_arrearageprice";
        $field[$k]["fieldname"] = "订单欠费总金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        if (isset($request['pay_type']) && $request['pay_type'] !== '') {
            switch ($request['pay_type']) {
                case "0":
                    $field[$k]["fieldstring"] = "ordercourse_totalprice";
                    $field[$k]["fieldname"] = "课程应收金额";
                    $field[$k]["show"] = 1;
                    $field[$k]["custom"] = 1;
                    $k++;

                    $field[$k]["fieldstring"] = "ordercourse_paidprice";
                    $field[$k]["fieldname"] = "课程已付金额";
                    $field[$k]["show"] = 1;
                    $field[$k]["custom"] = 1;
                    $k++;

                    $field[$k]["fieldstring"] = "ordercourse_ownprice";
                    $field[$k]["fieldname"] = "课程欠费金额";
                    $field[$k]["show"] = 1;
                    $field[$k]["custom"] = 1;
                    $k++;
                    break;
                case "1":
                    $field[$k]["fieldstring"] = "ordergoods_totalprice";
                    $field[$k]["fieldname"] = "教材应收金额";
                    $field[$k]["show"] = 1;
                    $field[$k]["custom"] = 1;
                    $k++;

                    $field[$k]["fieldstring"] = "ordergoods_paidprice";
                    $field[$k]["fieldname"] = "教材已付金额";
                    $field[$k]["show"] = 1;
                    $field[$k]["custom"] = 1;
                    $k++;

                    $field[$k]["fieldstring"] = "ordergoods_ownprice";
                    $field[$k]["fieldname"] = "教材欠费金额";
                    $field[$k]["show"] = 1;
                    $field[$k]["custom"] = 1;
                    $k++;
                    break;
                case "2":
                    $field[$k]["fieldstring"] = "orderitem_totalprice";
                    $field[$k]["fieldname"] = "杂费应收金额";
                    $field[$k]["show"] = 1;
                    $field[$k]["custom"] = 1;
                    $k++;

                    $field[$k]["fieldstring"] = "orderitem_paidprice";
                    $field[$k]["fieldname"] = "杂费已付金额";
                    $field[$k]["show"] = 1;
                    $field[$k]["custom"] = 1;
                    $k++;

                    $field[$k]["fieldstring"] = "orderitem_ownprice";
                    $field[$k]["fieldname"] = "杂费欠费金额";
                    $field[$k]["show"] = 1;
                    $field[$k]["custom"] = 1;
                    $k++;
                    break;
                default:
                    break;
            }
        }

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => "暂无学员未缴费记录", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    //学员课次消费报表
    function stuConsumptionReportView()
    {
        //error_reporting(E_ALL);
        ini_set('display_errors', '1');
        ini_set("memory_limit", '512M');
        set_time_limit(600);
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Report\Gmc\EducateReportModel($request);
        $res = $ReportModel->stuConsumptionReport($request);

        $k = 0;
        $field = array();
        $field[$k]["fieldstring"] = "province_name";
        $field[$k]["fieldname"] = "省份";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["fieldname"] = "学员中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_enname";
        $field[$k]["fieldname"] = "学员英文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_branch";
        $field[$k]["fieldname"] = "学员编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "channel_name";
        $field[$k]["fieldname"] = "专案名称";
        $field[$k]["show"] = (isset($request["company_id"]) && $request["company_id"] == '8888') ? 1 : 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursecat_cnname";
        $field[$k]["fieldname"] = "班种名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursecat_branch";
        $field[$k]["fieldname"] = "班种编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "course_cnname";
        $field[$k]["fieldname"] = "课程别名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "course_branch";
        $field[$k]["fieldname"] = "课程别编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_branch";
        $field[$k]["fieldname"] = "班级编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_enname";
        $field[$k]["fieldname"] = "班级名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "reading_status";
        $field[$k]["fieldname"] = "在班状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "supervise_price";
        $field[$k]["fieldname"] = "监管金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "shouldcheck_times";
        $field[$k]["fieldname"] = "应考勤课次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "uncheck_times";
        $field[$k]["fieldname"] = "未考勤课次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "deduct_times";
        $field[$k]["fieldname"] = "消耗课次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "log_playamount";
        $field[$k]["fieldname"] = "消耗金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "deduct_price";
        $field[$k]["fieldname"] = "优惠金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "atte_times";
        $field[$k]["fieldname"] = "出勤课次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "unatte_times";
        $field[$k]["fieldname"] = "缺勤课次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => "暂无学员课次消费记录", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //学员课次消费报表
    function classHourLineReportView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Report\Gmc\EducateReportModel($request);
        $res = $ReportModel->classHourLineReport($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "province_name";
        $field[$k]["fieldname"] = "省份";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursetype_branch";
        $field[$k]["fieldname"] = "班组编号";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursetype_cnname";
        $field[$k]["fieldname"] = "班组名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursecat_branch";
        $field[$k]["fieldname"] = "班种编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursecat_cnname";
        $field[$k]["fieldname"] = "班种名称";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "course_branch";
        $field[$k]["fieldname"] = "班别编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "course_cnname";
        $field[$k]["fieldname"] = "班别名称";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_branch";
        $field[$k]["fieldname"] = "班级编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_cnname";
        $field[$k]["fieldname"] = "班级名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "hour_day";
        $field[$k]["fieldname"] = "上课日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "hour_times";
        $field[$k]["fieldname"] = "上课时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "hour_number";
        $field[$k]["fieldname"] = "云教室编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "bishoptename";
        $field[$k]["fieldname"] = "主教教师";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "assistanttename";
        $field[$k]["fieldname"] = "助教教师";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "hour_ischecking";
        $field[$k]["fieldname"] = "考勤状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => "暂无学员课次消费记录", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //教师代课课时统计表
    function teaTeachReportView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);

        $result = array();
        $ReportModel = new \Model\Report\Gmc\EducateReportModel($request);
        $coursetype_array = json_decode(stripslashes($request['coursetype_array_id']), 1);
        if (isset($request['coursetype_array_id']) && count($coursetype_array) == 1 && in_array('65', $coursetype_array)) {
            $res = $ReportModel->teaTeachReport_8888($request);
            $result["field"] = $res['field'];
        } else {
            $res = $ReportModel->teaTeachReport($request);

            $k = 0;
            $field = array();

            $field[$k]["fieldstring"] = "district_cnname";
            $field[$k]["fieldname"] = "所属区域";
            $field[$k]["show"] = (isset($request["company_id"]) && $request["company_id"] == '8888') ? 1 : 0;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "province_name";
            $field[$k]["fieldname"] = "省份";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "school_branch";
            $field[$k]["fieldname"] = "校区编号";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "school_cnname";
            $field[$k]["fieldname"] = "分校名称";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "staffer_employeepid";
            $field[$k]["fieldname"] = "HR编号";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "staffer_cnname";
            $field[$k]["fieldname"] = "教师中文名";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "staffer_enname";
            $field[$k]["fieldname"] = "教师英文名";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "staffer_branch";
            $field[$k]["fieldname"] = "教师编号";
            $field[$k]["show"] = 0;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "staffer_leave";
            $field[$k]["fieldname"] = "在职状态";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "staffer_isparttime";
            $field[$k]["fieldname"] = "职务类型";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "ismianjob";
            $field[$k]["fieldname"] = "是否主职";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "staffer_native";
            $field[$k]["fieldname"] = "籍贯";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "post_name";
            $field[$k]["fieldname"] = "职位";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "offline_classnums";
            $field[$k]["fieldname"] = "实体课带班数量（主教）";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "online_classnums";
            $field[$k]["fieldname"] = "线上课带班数量（主教）";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "offline_hours_main";
            $field[$k]["fieldname"] = "实体课主教小时数";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $field[$k]["ismethod"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "offline_hours_sub";
            $field[$k]["fieldname"] = "实体课助教小时数";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $field[$k]["ismethod"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "online_hours_main";
            $field[$k]["fieldname"] = "线上课主教小时数";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $field[$k]["ismethod"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "online_hours_sub";
            $field[$k]["fieldname"] = "线上课助教小时数";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $field[$k]["ismethod"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "outclasstimes_class";
            $field[$k]["fieldname"] = "班外教学课时数";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "outclasstimes_other";
            $field[$k]["fieldname"] = "班外其他课时数";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "outclasstimes_all";
            $field[$k]["fieldname"] = "班外总课时数";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "total_hours_in";
            $field[$k]["fieldname"] = "本校总小时数";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "total_hours_out";
            $field[$k]["fieldname"] = "跨校总小时数";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "total_hours";
            $field[$k]["fieldname"] = "总教学小时数";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;
            $result["field"] = $field;
        }

        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $result["teachtype"] = $res['teachtype'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $result["teachtype"] = $res['teachtype'];
            $res = array('error' => 1, 'errortip' => "暂无学员课次消费记录", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //教师代班详情
    function teaTeachClassView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Gmc\ReportModel($request);
        $res = $ReportModel->teaTeachClass($request);

        $field = array();
        $field[0]["fieldname"] = "class_cnname";
        $field[0]["fieldstring"] = "班级名称";
        $field[0]["show"] = 1;
        $field[0]["custom"] = 1;

        $field[1]["fieldname"] = "class_enname";
        $field[1]["fieldstring"] = "班级别名";
        $field[1]["show"] = 1;
        $field[1]["custom"] = 1;

        $field[2]["fieldname"] = "class_branch";
        $field[2]["fieldstring"] = "班级编号";
        $field[2]["show"] = 1;
        $field[2]["custom"] = 1;

        $field[3]["fieldname"] = "course_cnname";
        $field[3]["fieldstring"] = "课程别编号";
        $field[3]["show"] = 1;
        $field[3]["custom"] = 1;

        $field[4]["fieldname"] = "teach_type";
        $field[4]["fieldstring"] = "教师类型";
        $field[4]["show"] = 1;
        $field[4]["custom"] = 1;
//
//        $field[5]["fieldstring"] = "classtimes";
//        $field[5]["fieldname"] = "上课进度";
//        $field[5]["show"] = 1;
//        $field[5]["custom"] = 1;

        $field[5]["fieldname"] = "class_status";
        $field[5]["fieldstring"] = "班级状态";
        $field[5]["show"] = 1;
        $field[5]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        if ($res) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => "暂无教师代班详情", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    //获取老师教学时间明细
    function getTeaListByTimesView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Report\Smc\EducateReportModel($request);
        $res = $ReportModel->getTeaListByTimes($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "school_shortname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["ismethod"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "time";
        $field[$k]["fieldname"] = "上课时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "hour_way_name";
        $field[$k]["fieldname"] = "上课方式";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "teaching_type_name";
        $field[$k]["fieldname"] = "教学类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "class_cnname";
        $field[$k]["fieldname"] = "班级名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "class_branch";
        $field[$k]["fieldname"] = "班级编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "course_cnname";
        $field[$k]["fieldname"] = "课程别名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "course_branch";
        $field[$k]["fieldname"] = "课程别编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "classroom_cnname";
        $field[$k]["fieldname"] = "教室名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "is_checking_name";
        $field[$k]["fieldname"] = "上课状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => "暂无教师排课", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    //学员课次购买课表
    function stuPurchaseReportView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Gmc\ReportModel($request);
        $res = $ReportModel->stuRefundReport($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "refund_pid";
        $field[$k]["fieldname"] = "订单编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_branch";
        $field[$k]["fieldname"] = "学员编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["fieldname"] = "学员中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_enname";
        $field[$k]["fieldname"] = "学员英文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "family_cnname";
        $field[$k]["fieldname"] = "家长姓名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "refund_price";
        $field[$k]["fieldname"] = "退费金额总计";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "balance";
        $field[$k]["fieldname"] = "退账户余额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "refund_status";
        $field[$k]["fieldname"] = "订单状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "refund_from";
        $field[$k]["fieldname"] = "订单来源";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "refund_createtime";
        $field[$k]["fieldname"] = "退费日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => "暂无学员课次购买记录", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    //月收入明细表--X-111
    function monthlyApportionReportView()
    {
        error_reporting(E_ALL);
        ini_set('display_errors', '1');
        ini_set("memory_limit", '-1');
        set_time_limit(600);
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Smc\ReportModel($request);
        $res = $ReportModel->monthlyIncomeReport($request);

        $k = 0;
        $field = array();
        $field[$k]["fieldstring"] = "school_id";
        $field[$k]["fieldname"] = "分校";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "province_name";
        $field[$k]["fieldname"] = "省份";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_branch";
        $field[$k]["fieldname"] = "学员编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["fieldname"] = "学员中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_enname";
        $field[$k]["fieldname"] = "学员英文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "channel_name";
        $field[$k]["fieldname"] = "专案名称";
        $field[$k]["show"] = (isset($request["company_id"]) && $request["company_id"] == '8888') ? 1 : 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursetype_cnname";
        $field[$k]["fieldname"] = "班组名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursetype_branch";
        $field[$k]["fieldname"] = "班组代码";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursecat_cnname";
        $field[$k]["fieldname"] = "班种名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursecat_branch";
        $field[$k]["fieldname"] = "班种代码";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_branch";
        $field[$k]["fieldname"] = "班级编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_enname";
        $field[$k]["fieldname"] = "班级别名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "income_type";
        $field[$k]["fieldname"] = "收入类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "companies_cnname";
        $field[$k]["fieldname"] = "收入主体";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "income_price";
        $field[$k]["fieldname"] = "收入金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "subtype_name";
        $field[$k]["fieldname"] = "认缴类别";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "income_note";
        $field[$k]["fieldname"] = "备注";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "income_confirmtime";
        $field[$k]["fieldname"] = "分摊时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "income_audittime";
        $field[$k]["fieldname"] = "上课时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
//        $result["regionlist"] = $res['regionlist'];
        if ($res) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => "暂无收入记录", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    //缴费月分摊明细表
    function stuAdvanceReportView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Gmc\ReportModel($request);
        $res = $ReportModel->stuAdvanceReport($request);

        $field = array();
        $field[0]["fieldstring"] = "student_branch";
        $field[0]["fieldname"] = "学员编号";
        $field[0]["show"] = 1;
        $field[0]["custom"] = 1;

        $field[1]["fieldstring"] = "student_cnname";
        $field[1]["fieldname"] = "学员中文名";
        $field[1]["show"] = 1;
        $field[1]["custom"] = 1;

        $field[2]["fieldstring"] = "student_enname";
        $field[2]["fieldname"] = "学员英文名";
        $field[2]["show"] = 1;
        $field[2]["custom"] = 1;

        $field[3]["fieldstring"] = "feetype_name";
        $field[3]["fieldname"] = "收费类型";
        $field[3]["show"] = 1;
        $field[3]["custom"] = 1;

        $field[4]["fieldstring"] = "coursecat_branch";
        $field[4]["fieldname"] = "班种";
        $field[4]["show"] = 1;
        $field[4]["custom"] = 1;

        $field[5]["fieldstring"] = "coursecatbalance_month";
        $field[5]["fieldname"] = "管理费月份";
        $field[5]["show"] = 1;
        $field[5]["custom"] = 1;

        $field[6]["fieldstring"] = "coursecatbalance_figure";
        $field[6]["fieldname"] = "课程剩余余额";
        $field[6]["show"] = 1;
        $field[6]["custom"] = 1;

        $field[7]["fieldstring"] = "coursecatbalance_time";
        $field[7]["fieldname"] = "课程剩余次数";
        $field[7]["show"] = 1;
        $field[7]["custom"] = 1;

        $field[8]["fieldstring"] = "coursecatbalance_unitexpend";
        $field[8]["fieldname"] = "消耗单价";
        $field[8]["show"] = 1;
        $field[8]["custom"] = 1;

        $field[9]["fieldstring"] = "coursecatbalance_unitrefund";
        $field[9]["fieldname"] = "退费单价";
        $field[9]["show"] = 1;
        $field[9]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => "暂无缴费月分摊记录", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    //学员流失报表
    function stuEnrolledReportView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Gmc\ReportModel($request);
        $res = $ReportModel->stuEnrolledReport($request);
        $field = array();
        $field[0]["fieldstring"] = "changelog_id";
        $field[0]["fieldname"] = "id";
        $field[0]["show"] = 0;
        $field[0]["custom"] = 0;

        $field[1]["fieldstring"] = "student_cnname";
        $field[1]["fieldname"] = "学员中文名";
        $field[1]["show"] = 1;
        $field[1]["custom"] = 1;

        $field[2]["fieldstring"] = "student_enname";
        $field[2]["fieldname"] = "学员英文名";
        $field[2]["show"] = 1;
        $field[2]["custom"] = 1;

        $field[3]["fieldstring"] = "student_branch";
        $field[3]["fieldname"] = "学员编号";
        $field[3]["show"] = 1;
        $field[3]["custom"] = 1;

        $field[4]["fieldstring"] = "changelog_day";
        $field[4]["fieldname"] = "异动日期";
        $field[4]["show"] = 1;
        $field[4]["custom"] = 1;

        $field[5]["fieldstring"] = "changelog_note";
        $field[5]["fieldname"] = "异动备注";
        $field[5]["show"] = 1;
        $field[5]["custom"] = 1;

        $field[6]["fieldstring"] = "stuchange_code";
        $field[6]["fieldname"] = "异动代码";
        $field[6]["show"] = 1;
        $field[6]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        $result["message"] = '请输入。。。';
        if ($res['list']) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => "暂无学员离校记录", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }


    //学员待入班报表
    function stuEnrolledClassView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Gmc\ReportModel($request);
        $res = $ReportModel->stuEnrolledClass($request);
        $field = array();
        $field[0]["fieldstring"] = "student_id";
        $field[0]["fieldname"] = "id";
        $field[0]["show"] = 0;
        $field[0]["custom"] = 0;

        $field[1]["fieldstring"] = "student_cnname";
        $field[1]["fieldname"] = "学员中文名";
        $field[1]["show"] = 1;
        $field[1]["custom"] = 1;

        $field[2]["fieldstring"] = "student_enname";
        $field[2]["fieldname"] = "学员英文名";
        $field[2]["show"] = 1;
        $field[2]["custom"] = 1;

        $field[3]["fieldstring"] = "student_branch";
        $field[3]["fieldname"] = "学员编号";
        $field[3]["show"] = 1;
        $field[3]["custom"] = 1;

        $field[4]["fieldstring"] = "log_finalamount2";
        $field[4]["fieldname"] = " 课程余额";
        $field[4]["show"] = 1;
        $field[4]["custom"] = 1;

        $field[5]["fieldstring"] = "stuchange_code";
        $field[5]["fieldname"] = " 异动代码";
        $field[5]["show"] = 1;
        $field[5]["custom"] = 1;


        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res['list']) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => "暂无学员代入班记录", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);


    }


//	学员延班报表
    function stuWaitClassView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Gmc\ReportModel($request);
        $res = $ReportModel->stuWaitClass($request);
        $field = array();
        $field[0]["fieldstring"] = "student_id";
        $field[0]["fieldname"] = "id";
        $field[0]["show"] = 0;
        $field[0]["custom"] = 0;

        $field[1]["fieldstring"] = "student_cnname";
        $field[1]["fieldname"] = "学员中文名";
        $field[1]["show"] = 1;
        $field[1]["custom"] = 1;

        $field[2]["fieldstring"] = "student_enname";
        $field[2]["fieldname"] = "学员英文名";
        $field[2]["show"] = 1;
        $field[2]["custom"] = 1;

        $field[3]["fieldstring"] = "student_branch";
        $field[3]["fieldname"] = "学员编号";
        $field[3]["show"] = 1;
        $field[3]["custom"] = 1;


        $field[4]["fieldstring"] = "changelog_day";
        $field[4]["fieldname"] = "异动日期";
        $field[4]["show"] = 1;
        $field[4]["custom"] = 1;

        $field[5]["fieldstring"] = "changelog_note";
        $field[5]["fieldname"] = "异动备注";
        $field[5]["show"] = 1;
        $field[5]["custom"] = 1;

        $field[6]["fieldstring"] = "stuchange_code";
        $field[6]["fieldname"] = "异动代码";
        $field[6]["show"] = 1;
        $field[6]["custom"] = 1;


        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res['list']) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => "暂无学员延班记录", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    //月支出明细表--X
    function monthlyExpendReportView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Report\Gmc\FinanceReportModel($request);
        $res = $ReportModel->monthlyExpendReport($request);

        $k = 0;
        $field = array();
        $field[$k]["fieldstring"] = "school_id";
        $field[$k]["fieldname"] = "分校";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "province_name";
        $field[$k]["fieldname"] = "省份";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_branch";
        $field[$k]["fieldname"] = "学员编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["fieldname"] = "学员中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_enname";
        $field[$k]["fieldname"] = "学员英文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "expend_type";
        $field[$k]["fieldname"] = "支出类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "expend_price";
        $field[$k]["fieldname"] = "支出金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "expend_note";
        $field[$k]["fieldname"] = "备注";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "trading_pid";
        $field[$k]["fieldname"] = "关联交易单号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => "暂无支出记录", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }


    //班级结算学员收入明细报表 - X-111
    function classEndStudentIncomeView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Report\Gmc\FinanceReportModel($request);
        $res = $ReportModel->classEndStudentIncome($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "school_id";
        $field[$k]["fieldname"] = "学校id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "province_name";
        $field[$k]["fieldname"] = "省份";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_id";
        $field[$k]["fieldname"] = "班级id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "class_branch";
        $field[$k]["fieldname"] = "班级编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_cnname";
        $field[$k]["fieldname"] = "班级名称";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_enname";
        $field[$k]["fieldname"] = "班级别名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_period";
        $field[$k]["fieldname"] = "开班时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_id";
        $field[$k]["fieldname"] = "学员id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "student_branch";
        $field[$k]["fieldname"] = "学员编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["fieldname"] = "学员中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_enname";
        $field[$k]["fieldname"] = "学员英文名";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_period";
        $field[$k]["fieldname"] = "就读时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

//        $field[$k]["fieldstring"] = "leave_times";
//        $field[$k]["fieldname"] = "请假课次";
//        $field[$k]["show"] = 0;
//        $field[$k]["custom"] = 1;
//        $k++;

        $field[$k]["fieldstring"] = "rece_times";
        $field[$k]["fieldname"] = "冲销课次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "rece_amount";
        $field[$k]["fieldname"] = "冲销金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "back_times";
        $field[$k]["fieldname"] = "结算课次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "back_amount";
        $field[$k]["fieldname"] = "结算金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "used_times";
        $field[$k]["fieldname"] = "已认列课次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "used_amount";
        $field[$k]["fieldname"] = "已认列金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "left_times";
        $field[$k]["fieldname"] = "未认列课次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "left_amount";
        $field[$k]["fieldname"] = "未认列金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res['list']) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => "暂无班级结算学员收入记录", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    //在籍新生统计表报表
    function isResidenceStudentView()
    {
        error_reporting(E_ALL);
        ini_set('display_errors', '1');
        ini_set("memory_limit", '-1');
        set_time_limit(600);
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Report\Gmc\CourseReportModel($request);
        $res = $ReportModel->isResidenceStudent($request);
        $field = array();

        $k = 0;
        $field[$k]["fieldstring"] = "school_id";
        $field[$k]["fieldname"] = "学校id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "province_name";
        $field[$k]["fieldname"] = "省份";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校园名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "district_cnname";
        $field[$k]["fieldname"] = "所属区域";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "检索代码";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursetype_cnname";
        $field[$k]["fieldname"] = "班组名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursetype_branch";
        $field[$k]["fieldname"] = "班组代码";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursecat_cnname";
        $field[$k]["fieldname"] = "班种名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursecat_branch";
        $field[$k]["fieldname"] = "班种代码";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "course_cnname";
        $field[$k]["fieldname"] = "课程别名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "course_branch";
        $field[$k]["fieldname"] = "课程别代码";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_branch";
        $field[$k]["fieldname"] = "学员编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["fieldname"] = "学员中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_enname";
        $field[$k]["fieldname"] = "学员英文名";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "channel_name";
        $field[$k]["fieldname"] = "专案名称";
        $field[$k]["show"] = (isset($request["company_id"]) && $request["company_id"] == '8888') ? 1 : 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "info_type";
        $field[$k]["fieldname"] = "新生类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "tags_name";
        $field[$k]["fieldname"] = "学员标签";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "register_date";
        $field[$k]["fieldname"] = "下单日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "trading_pid";
        $field[$k]["fieldname"] = "交易单号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "pay_day";
        $field[$k]["fieldname"] = "新生日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "buy_times";
        $field[$k]["fieldname"] = "购买课次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "pay_price";
        $field[$k]["fieldname"] = "首缴金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "unpaid_price";
        $field[$k]["fieldname"] = "账单欠费金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res['list']) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => "暂无在籍新生统计记录", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    //班级实际欠费报表 - X-111
    function classArrearView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Report\Gmc\FinanceReportModel($request);
        $res = $ReportModel->classIncome($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "school_id";
        $field[$k]["fieldname"] = "分校id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "province_name";
        $field[$k]["fieldname"] = "省份";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "course_branch";
        $field[$k]["fieldname"] = "课程别";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_branch";
        $field[$k]["fieldname"] = "班级编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_cnname";
        $field[$k]["fieldname"] = "班级名称";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_enname";
        $field[$k]["fieldname"] = "班级别名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "income_type";
        $field[$k]["fieldname"] = "收入类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "income_total_count";
        $field[$k]["fieldname"] = "收入单数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "income_student_count";
        $field[$k]["fieldname"] = "涉及学员人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "income_total_price";
        $field[$k]["fieldname"] = "收入总计";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res['list']) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => "暂无班级收入数据", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    //在读新生统计表
    function readingStudentView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Gmc\ReportModel($request);
        $res = $ReportModel->readingStudent($request);
        $field = array();

        $k = 0;

        $field[$k]["fieldstring"] = "student_id";
        $field[$k]["fieldname"] = "id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "date1";
        $field[$k]["fieldname"] = "缴订日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "date2";
        $field[$k]["fieldname"] = "入班冲销日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "date3";
        $field[$k]["fieldname"] = "入班日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "date4";
        $field[$k]["fieldname"] = "第一次就读日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "count1";
        $field[$k]["fieldname"] = "插班课次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_branch";
        $field[$k]["fieldname"] = "学员编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["fieldname"] = "学员姓名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_from";
        $field[$k]["fieldname"] = "学员来源";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursetype_cnname";
        $field[$k]["fieldname"] = "班组";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_cnname";
        $field[$k]["fieldname"] = "插入班级";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "book_price";
        $field[$k]["fieldname"] = "书费";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "platform";
        $field[$k]["fieldname"] = "线上平台";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "price1";
        $field[$k]["fieldname"] = "预收金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "count2";
        $field[$k]["fieldname"] = "预收课次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "price2";
        $field[$k]["fieldname"] = "冲销金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "count3";
        $field[$k]["fieldname"] = "冲销课次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "price3";
        $field[$k]["fieldname"] = "具体学费核算";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "explain";
        $field[$k]["fieldname"] = "优惠说明";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "remark";
        $field[$k]["fieldname"] = "备注";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res['list']) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => "暂无在读新生统计", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    //学员班级流失报表
    function studentClassOffView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Gmc\ReportModel($request);
        $res = $ReportModel->studentClassOff($request);
        $field = array();

        $k = 0;

        $field[$k]["fieldstring"] = "student_id";
        $field[$k]["fieldname"] = "id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["fieldname"] = "学员中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_enname";
        $field[$k]["fieldname"] = "学员英文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_branch";
        $field[$k]["fieldname"] = "学员编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "change_day";
        $field[$k]["fieldname"] = "异动日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_cnname";
        $field[$k]["fieldname"] = "流失班级";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_branch";
        $field[$k]["fieldname"] = "班级编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "reason_note";
        $field[$k]["fieldname"] = "异动原因";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "staffer_cnname";
        $field[$k]["fieldname"] = "操作人";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "change_createtime";
        $field[$k]["fieldname"] = "操作时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res['list']) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => "暂无学员班级流失", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    //班级信息统计表
    function classInfoView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Gmc\ReportModel($request);
        $res = $ReportModel->classInfo($request);
        $field = array();

        $k = 0;

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_cnname";
        $field[$k]["fieldname"] = "班级名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_enname";
        $field[$k]["fieldname"] = "班级别名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_branch";
        $field[$k]["fieldname"] = "班级编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "count1";
        $field[$k]["fieldname"] = "班级教师累计总数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "count2";
        $field[$k]["fieldname"] = "教师人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "count3";
        $field[$k]["fieldname"] = "满班人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "count4";
        $field[$k]["fieldname"] = "在读人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "percent1";
        $field[$k]["fieldname"] = "满班率";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "count5";
        $field[$k]["fieldname"] = "转入人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "count6";
        $field[$k]["fieldname"] = "转出人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "count7";
        $field[$k]["fieldname"] = "班级流失人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "count8";
        $field[$k]["fieldname"] = "延班人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "count9";
        $field[$k]["fieldname"] = "应到人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "count10";
        $field[$k]["fieldname"] = "实到人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "percent2";
        $field[$k]["fieldname"] = "出勤率";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res['list']) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => "暂无班级信息", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    //班级升班明细表
    function classLevelUpView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Gmc\ReportModel($request);
        $res = $ReportModel->classLevelUp($request);
        $field = array();

        $k = 0;

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "from_class_cnname";
        $field[$k]["fieldname"] = "来源班级";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "from_class_branch";
        $field[$k]["fieldname"] = "来源班级编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "from_course_cnname";
        $field[$k]["fieldname"] = "来源班级课程别";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "from_staffer_cnname";
        $field[$k]["fieldname"] = "来源班级带课教师";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "count1";
        $field[$k]["fieldname"] = "班级人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "date1";
        $field[$k]["fieldname"] = "升班时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "to_class_cnname";
        $field[$k]["fieldname"] = "升班班级";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "to_class_branch";
        $field[$k]["fieldname"] = "升班班级编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "to_staffer_cnname";
        $field[$k]["fieldname"] = "升班班级带课教师";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "count2";
        $field[$k]["fieldname"] = "升班人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "percent";
        $field[$k]["fieldname"] = "升班率";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res['list']) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => "暂无班级升班明细表", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    //班级转班明细报表
    function changeClassDetailView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Gmc\ReportModel($request);
        $res = $ReportModel->changeClassDetail($request);
        $field = array();

        $k = 0;

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["fieldname"] = "学员中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_enname";
        $field[$k]["fieldname"] = "学员英文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_branch";
        $field[$k]["fieldname"] = "学员编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "from_class_cnname";
        $field[$k]["fieldname"] = "转班班级";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "from_class_branch";
        $field[$k]["fieldname"] = "班级编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "to_class_cnname";
        $field[$k]["fieldname"] = "转入班级";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "to_class_branch";
        $field[$k]["fieldname"] = "转入班级编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "change_day";
        $field[$k]["fieldname"] = "异动时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "reason_note";
        $field[$k]["fieldname"] = "异动原因";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res['list']) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => "暂无班级转班明细报表", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    //学员升班明细报表
    function studentClassUpView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Gmc\ReportModel($request);
        $res = $ReportModel->studentClassUp($request);
        $field = array();

        $k = 0;

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["fieldname"] = "学员中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_enname";
        $field[$k]["fieldname"] = "学员英文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_branch";
        $field[$k]["fieldname"] = "学员编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "from_class_cnname";
        $field[$k]["fieldname"] = "来源班级";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "from_class_branch";
        $field[$k]["fieldname"] = "来源班级编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "from_course_cnname";
        $field[$k]["fieldname"] = "来源班级课程别";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "from_staffer_cnname";
        $field[$k]["fieldname"] = "来源班级带课教师";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "date1";
        $field[$k]["fieldname"] = "升班时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "to_class_cnname";
        $field[$k]["fieldname"] = "升班班级";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "to_class_branch";
        $field[$k]["fieldname"] = "升班班级编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "to_staffer_cnname";
        $field[$k]["fieldname"] = "升班班级带课教师";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "remark";
        $field[$k]["fieldname"] = "备注";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res['list']) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => "暂无班级转班明细报表", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }


    //课时数据明细报表
    function classTimeDetailView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Gmc\ReportModel($request);
        $res = $ReportModel->classTimeDetail($request);
        $field = array();

        $k = 0;

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "needtype_cnname";
        $field[$k]["fieldname"] = "需求类型名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "needtype_branch";
        $field[$k]["fieldname"] = "需求类型编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "count1";
        $field[$k]["fieldname"] = "应到人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "count2";
        $field[$k]["fieldname"] = "实到人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "percent";
        $field[$k]["fieldname"] = "出勤率";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "count3";
        $field[$k]["fieldname"] = "上课课时数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "count4";
        $field[$k]["fieldname"] = "学员累计耗课数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "price1";
        $field[$k]["fieldname"] = "累计耗课收入";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "price2";
        $field[$k]["fieldname"] = "耗课课时均价";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res['list']) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => "暂无课时数据明细报表", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    //学费消耗明细报表
    function feeCostView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Gmc\ReportModel($request);
        $res = $ReportModel->feeCost($request);
        $field = array();

        $k = 0;

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "needtype_cnname";
        $field[$k]["fieldname"] = "需求类型名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "needtype_branch";
        $field[$k]["fieldname"] = "需求类型编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "count1";
        $field[$k]["fieldname"] = "消耗课时";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "price1";
        $field[$k]["fieldname"] = "耗课收入";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "price2";
        $field[$k]["fieldname"] = "课时收入均价";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res['list']) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => "暂无学费消耗明细报表", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    //学员课程余额统计报表
    function studentCourceSurplusView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Gmc\ReportModel($request);
        $res = $ReportModel->studentCourceSurplus($request);
        $field = array();

        $k = 0;

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "needtype_cnname";
        $field[$k]["fieldname"] = "需求类型名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "needtype_branch";
        $field[$k]["fieldname"] = "需求类型编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "price1";
        $field[$k]["fieldname"] = "课程余额总计";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "price2";
        $field[$k]["fieldname"] = "课程优惠总计";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "count";
        $field[$k]["fieldname"] = "课程剩余课次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res['list']) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => "暂无学员课程余额统计报表", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    //学员充值明细报表
    function studentInvestView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Gmc\ReportModel($request);
        $res = $ReportModel->studentInvest($request);
        $field = array();

        $k = 0;

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "order_pid";
        $field[$k]["fieldname"] = "交易订单编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["fieldname"] = "学员中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_enname";
        $field[$k]["fieldname"] = "学员英文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_branch";
        $field[$k]["fieldname"] = "学员编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "order_allprice";
        $field[$k]["fieldname"] = "订单总额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "order_status";
        $field[$k]["fieldname"] = "订单状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "order_createtime";
        $field[$k]["fieldname"] = "下单时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res['list']) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => "暂无学员充值明细报表", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    //课程冲销明细报表
    function courseChargeView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Gmc\ReportModel($request);
        $res = $ReportModel->courseCharge($request);
        $field = array();

        $k = 0;

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "order_pid";
        $field[$k]["fieldname"] = "交易订单编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["fieldname"] = "学员中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_enname";
        $field[$k]["fieldname"] = "学员英文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_branch";
        $field[$k]["fieldname"] = "学员编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "order_allprice";
        $field[$k]["fieldname"] = "订单总额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "order_status";
        $field[$k]["fieldname"] = "订单状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "count1";
        $field[$k]["fieldname"] = "冲销总课次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "course_cnname";
        $field[$k]["fieldname"] = "冲销课程";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "date1";
        $field[$k]["fieldname"] = "下单时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "date2";
        $field[$k]["fieldname"] = "冲销完成时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res['list']) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => "暂无学课程冲销明细报表", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    //校园收入统计报表 -X-111
    function schoolIncomeView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Report\Gmc\FinanceReportModel($request);
        $res = $ReportModel->schoolIncome($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "school_id";
        $field[$k]["fieldname"] = "学校id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "province_name";
        $field[$k]["fieldname"] = "省份";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "income_month";
        $field[$k]["fieldname"] = "收入月份";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "income_type";
        $field[$k]["fieldname"] = "收入类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "income_total_count";
        $field[$k]["fieldname"] = "收入单数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "income_student_count";
        $field[$k]["fieldname"] = "涉及学员人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "income_total_price";
        $field[$k]["fieldname"] = "收入总计";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res['list']) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => "暂无校园收入统计报表", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    //班级上课报表
    function classCourseView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Gmc\ReportModel($request);
        $res = $ReportModel->classCourse($request);
        $field = array();

        $k = 0;

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_cnname";
        $field[$k]["fieldname"] = "班级名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_enname";
        $field[$k]["fieldname"] = "班级别名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_branch";
        $field[$k]["fieldname"] = "班级编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "course_cnname";
        $field[$k]["fieldname"] = "课程别名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "course_branch";
        $field[$k]["fieldname"] = "课程别编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "date1";
        $field[$k]["fieldname"] = "上课时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "main_teacher";
        $field[$k]["fieldname"] = "主教";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "assistant_teacher";
        $field[$k]["fieldname"] = "助教";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "classroom_cnname";
        $field[$k]["fieldname"] = "教室";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "count1";
        $field[$k]["fieldname"] = "已上/计划课次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res['list']) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => "暂无班级上课报表", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    //教师上课报表
    function teacherCourseView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Gmc\ReportModel($request);
        $res = $ReportModel->teacherCourse($request);
        $field = array();

        $k = 0;

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "staffer_cnname";
        $field[$k]["fieldname"] = "职工中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "staffer_enname";
        $field[$k]["fieldname"] = "职工英文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "staffer_branch";
        $field[$k]["fieldname"] = "职工编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "type1";
        $field[$k]["fieldname"] = "教师类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "date1";
        $field[$k]["fieldname"] = "上课日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_cnname";
        $field[$k]["fieldname"] = "班级名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_enname";
        $field[$k]["fieldname"] = "班级别名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_branch";
        $field[$k]["fieldname"] = "班级编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res['list']) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => "暂无教师上课报表", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    //学员考勤报表
    function studentCheckView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Gmc\ReportModel($request);
        $res = $ReportModel->studentCheck($request);
        $field = array();

        $k = 0;
        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "staffer_cnname";
        $field[$k]["fieldname"] = "学员中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "staffer_enname";
        $field[$k]["fieldname"] = "学员英文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "staffer_branch";
        $field[$k]["fieldname"] = "学员编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "type1";
        $field[$k]["fieldname"] = "主要联系方式";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "date1";
        $field[$k]["fieldname"] = "上课日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "status";
        $field[$k]["fieldname"] = "出勤状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "reason";
        $field[$k]["fieldname"] = "缺勤原因";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res['list']) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => "暂无学员考勤报表", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    //借调相关报表
    function transferGoodsView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Gmc\ReportModel($request);
        $res = $ReportModel->transferGoods($request);
        $field = array();

        $k = 0;

        $field[$k]["fieldstring"] = "to_school_cnname";
        $field[$k]["fieldname"] = "借入分校";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "from_school_cnname";
        $field[$k]["fieldname"] = "借出分校";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "proorder_pid";
        $field[$k]["fieldname"] = "采购单号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "goods_cnname";
        $field[$k]["fieldname"] = "货品名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "goods_pid";
        $field[$k]["fieldname"] = "货品编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "prodtype_name";
        $field[$k]["fieldname"] = "货品类别";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "goods_unit";
        $field[$k]["fieldname"] = "单位";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "count1";
        $field[$k]["fieldname"] = "数量";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "remark";
        $field[$k]["fieldname"] = "备注";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "date1";
        $field[$k]["fieldname"] = "申请日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "date2";
        $field[$k]["fieldname"] = "完成日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "date3";
        $field[$k]["fieldname"] = "预计归还日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res['list']) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => "暂无借调相关报表", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    //领用相关报表
    function receiveGoodsView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Gmc\ReportModel($request);
        $res = $ReportModel->receiveGoods($request);
        $field = array();

        $k = 0;
        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "proorder_pid";
        $field[$k]["fieldname"] = "领用单号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "staffer_cnname";
        $field[$k]["fieldname"] = "职工中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "staffer_enname";
        $field[$k]["fieldname"] = "职工英文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "staffer_branch";
        $field[$k]["fieldname"] = "职工编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "goods_cnname";
        $field[$k]["fieldname"] = "货品名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "goods_pid";
        $field[$k]["fieldname"] = "货品编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "prodtype_name";
        $field[$k]["fieldname"] = "货品类别";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "count1";
        $field[$k]["fieldname"] = "领用数量";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "count2";
        $field[$k]["fieldname"] = "当前可用库存量";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "remark";
        $field[$k]["fieldname"] = "备注";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "date1";
        $field[$k]["fieldname"] = "领用日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res['list']) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => "暂无借调相关报表", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    //活动采购报表
    function activityGoodsView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Gmc\ReportModel($request);
        $res = $ReportModel->activityGoods($request);
        $field = array();

        $k = 0;
        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "activity_name";
        $field[$k]["fieldname"] = "活动名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "activity_branch";
        $field[$k]["fieldname"] = "活动编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "goods_cnname";
        $field[$k]["fieldname"] = "货品名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "goods_branch";
        $field[$k]["fieldname"] = "货品编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "prodtype_name";
        $field[$k]["fieldname"] = "货品类别";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "count1";
        $field[$k]["fieldname"] = "数量";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res['list']) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => "暂无活动采购报表", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    //班种下拉列表
    function getCoursecatApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);

        $sql = "select coursecat_id,coursecat_cnname,coursecat_branch from smc_code_coursecat where company_id = '{$request['company_id']}'";
        $CompaniesList = $this->DataControl->selectClear($sql);
        if (!$CompaniesList) {
            $CompaniesList = array();
        }

        $result["list"] = $CompaniesList;
        $res = array('error' => 0, 'errortip' => '获取班种下拉列表', 'result' => $result);

        ajax_return($res, $request['language_type']);
    }

    function inspectionReportApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Gmc\ReportModel($request);
        $res = $Model->inspectionReport($request);

        $field = array();
        $k = 0;

        $field[$k]["fieldstring"] = "school_id";
        $field[$k]["fieldname"] = "学校id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "province_name";
        $field[$k]["fieldname"] = "省份";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_shortname";
        $field[$k]["fieldname"] = "学校简称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "hourNum";
        $field[$k]["fieldname"] = "延迟课次数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "forwardNum";
        $field[$k]["fieldname"] = "延班人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "lowNum";
        $field[$k]["fieldname"] = "低学费人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "lostNum";
        $field[$k]["fieldname"] = "未填写流失原因人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["ismethod"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "noStafferNum";
        $field[$k]["fieldname"] = "无主教课时数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "noStafferClassNum";
        $field[$k]["fieldname"] = "无主教班级数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "orderNum";
        $field[$k]["fieldname"] = "未审订单数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "feeNum";
        $field[$k]["fieldname"] = "未审减免订单数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "dealorderNum";
        $field[$k]["fieldname"] = "未审结转订单数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "freeNum";
        $field[$k]["fieldname"] = "未审赠课订单数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "schoolRefundNum";
        $field[$k]["fieldname"] = "校未审退费订单数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "refundNum";
        $field[$k]["fieldname"] = "未审退费订单数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;


        $field[$k]["fieldstring"] = "reduceorderNum";
        $field[$k]["fieldname"] = "未审减课订单数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "applyNum";
        $field[$k]["fieldname"] = "未审优惠券数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res['list']) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function crmInspectionReportApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Gmc\ReportModel($request);
        $res = $Model->crmInspectionReport($request);

        $field = array();
        $k = 0;

        $field[$k]["fieldstring"] = "school_id";
        $field[$k]["fieldname"] = "学校id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "province_name";
        $field[$k]["fieldname"] = "省份";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_shortname";
        $field[$k]["fieldname"] = "学校简称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;

        $field[$k]["fieldstring"] = "district_cnname";
        $field[$k]["fieldname"] = "所属区域";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "post_token_num";
        $field[$k]["fieldname"] = "未激活微信账户";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "client_num";
        $field[$k]["fieldname"] = "暂未跟进名单数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "client_nopurposenum";
        $field[$k]["fieldname"] = "未设定意向课程名单数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "mian_marketer_num";
        $field[$k]["fieldname"] = "主负责人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "invite_no_num";
        $field[$k]["fieldname"] = "柜询待确认数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "audition_no_num";
        $field[$k]["fieldname"] = "试听待确认数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "client_lossnum";
        $field[$k]["fieldname"] = "无意向待审核数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "tracestus_num";
        $field[$k]["fieldname"] = "半月内未有效跟踪数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "levelnums";
        $field[$k]["fieldname"] = "未设定意向星级数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['all_num'];
        if ($res['list']) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => "暂无信息", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //学校考勤差异统计表
    function schoolMachineStuDiffView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Gmc\ReportModel($request);
        $res = $ReportModel->schoolMachineStuDiff($request);
        $field = array();

        $k = 0;
        $field[$k]["fieldstring"] = "province_name";
        $field[$k]["fieldname"] = "省份";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "MaxStuNum";
        $field[$k]["fieldname"] = "应考勤人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "MaxStuportraitNum";
        $field[$k]["fieldname"] = "人脸采集人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "allMateNum";
        $field[$k]["fieldname"] = "考勤完全匹配人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "allNotMateNum";
        $field[$k]["fieldname"] = "考勤不匹配人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res['list']) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => "暂无在籍新生统计记录", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    //教师类型下拉
    function getTeachtypeApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\ReportModel($request);

        $result = $this->Model->getTeachtypeApi($request);
        ajax_return($result, $request['language_type']);
    }
}
