<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/13
 * Time: 23:25
 */

namespace Work\Controller\Gmcapi;


class OrganizeController extends viewTpl
{
    public $u;
    public $t;
    public $c;
    public $visitType = "api";
    public $Model;

    //预加载处理类
    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
    }

    function ThisVerify($request){
        $paramArray = array();
        $paramArray['staffer_id'] = $request['staffer_id'];
        $paramArray['company_id'] = $request['company_id'];
        $paramArray['token'] = $request['token'];
        if(!$this->UserLimit($paramArray)){
            $result = array();
            $result["list"] = array();
            $result["tokeninc"] = "0";
            $res = array('error' => '1', 'errortip' => "用户token失效", 'result' => $result);
            ajax_return($res,$request['language_type']);
        }
    }


    //机构管理首页 ---一级页面
    function HomeView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model =  new \Model\Gmc\OrganizeModel($request);

        $result = $this->Model->getCompanyList($request);

        ajax_return($result,$request['language_type']);

    }

    //机构详情
    function organizeDetailApi()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model =  new \Model\Gmc\OrganizeModel($request);

        $result = $this->Model->organizeDetailApi($request);

        ajax_return($result,$request['language_type']);

    }


    //编辑机构是否默认
    function updateOrganizeStatusAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model =  new \Model\Gmc\OrganizeModel($request);

        $result = $this->Model->updateOrganizeStatusAction($request);

        ajax_return($result,$request['language_type']);
    }


    //机构管辖学校列表
    function manageSchoolView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model =  new \Model\Gmc\OrganizeModel($request);

        $result = $this->Model->manageSchoolView($request);

        ajax_return($result,$request['language_type']);

    }

    //删除机构管辖学校
    function delManageSchoolAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model =  new \Model\Gmc\OrganizeModel($request);

        $result = $this->Model->delManageSchoolAction($request);
        ajax_return($result,$request['language_type']);
    }

    //编辑机构
    function updateOrganzieView()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $this->Model = new \Model\Gmc\OrganizeModel($request);

        $result = $this->Model->updateOrganizeView($request);
        ajax_return($result,$request['language_type']);
    }


    //创建子机构
    function createOrganizeAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model =  new \Model\Gmc\OrganizeModel($request);

        $result = $this->Model->createOrganizeAction($request);
        ajax_return($result,$request['language_type']);
    }

    //删除子机构
    function delOrganizeAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model =  new \Model\Gmc\OrganizeModel($request);

        $result = $this->Model->delOrganizeAction($request);
        ajax_return($result,$request['language_type']);
    }

    //获取可添加分校分校
    function getSchoolApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model =  new \Model\Gmc\OrganizeModel($request);

        $result = $this->Model->getSchoolApi($request);
        ajax_return($result,$request['language_type']);
    }

    //组织添加学校
    function addSchoolAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model =  new \Model\Gmc\OrganizeModel($request);

        $result = $this->Model->addSchoolAction($request);
        ajax_return($result,$request['language_type']);
    }

     //组织模式选项
    function organizeClassView()
    {
        $request = Input('get.','','trim,addslashes');

        $sql = "select organizeclass_id,organizeclass_name,organizeclass_colour,organizeclass_note,organizeclass_isdefault from gmc_code_organizeclass where company_id = '{$request['company_id']}'";
        $OrganizeclassList = $this->DataControl->selectClear($sql);
        if (!$OrganizeclassList) {
            $OrganizeclassList = array();
        }

        $result["list"] = $OrganizeclassList;
        $res = array('error' => 0, 'errortip' => '获取架构模式选项', 'result' => $result);

        ajax_return($res,$request['language_type']);
    }


    //删除机构模式
    function delOrganizeClassAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model =  new \Model\Gmc\OrganizeModel($request);

        $result = $this->Model->delOrganizeClassAction($request);

        ajax_return($result,$request['language_type']);
    }

    //编辑机构模式
    function updateOrganizeClassAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model =  new \Model\Gmc\OrganizeModel($request);

        $result = $this->Model->updateOrganizeClassAction($request);

        ajax_return($result,$request['language_type']);
    }

    //创建机构模式
    function createOrganizeClassAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model =  new \Model\Gmc\OrganizeModel($request);

        $result = $this->Model->createOrganizeClassAction($request);

        ajax_return($result,$request['language_type']);
    }

    //机构模式详情
    function organizeClassDetailApi()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model =  new \Model\Gmc\OrganizeModel($request);

        $result = $this->Model->organizeClassDetailApi($request);

        ajax_return($result,$request['language_type']);
    }




    //结尾魔术函数
    function __destruct()
    {

    }
}
