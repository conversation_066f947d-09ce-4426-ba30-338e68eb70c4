<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/4/13
 * Time: 9:09
 */

namespace Work\Controller\Gmcapi;


class StatisticsInstituteController extends viewTpl
{
    public $u;
    public $t;
    public $c;

    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
    }


    //集团统计中心 - 校所概况
    function InstituteSurveyApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);

        $ReportModel = new \Model\Gmc\StatisticsInstituteModel($request);
        $dataList = $ReportModel->InstituteSurveyApi($request);

        $res = array('error' => '0', 'errortip' => '校所概况', 'result' => $dataList);
        ajax_return($res,$request['language_type']);
    }

    //集团统计中心 - 校所分布图
    function InstituteApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);

        $ReportModel = new \Model\Gmc\StatisticsInstituteModel($request);
        $dataList = $ReportModel->InstituteApi($request);

        $res = array('error' => 0, 'errortip' => '校所分布图', 'result' => $dataList);
        ajax_return($res,$request['language_type']);
    }

    //集团统计中心 - 校所分布数量
    function DistributionApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);

        $ReportModel = new \Model\Gmc\StatisticsInstituteModel($request);
        $dataList = $ReportModel->DistributionApi($request);

        $res = array('error' => 0, 'errortip' => '校所分布数量', 'result' => $dataList);
        ajax_return($res,$request['language_type']);
    }

    //集团统计中心 - 校所概况 - 查看全部
    function SchoolTotalApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);

        $ReportModel = new \Model\Gmc\StatisticsInstituteModel($request);
        $dataList = $ReportModel->SchoolTotalApi($request);

        $field = array();
        $k = 0;
        if(isset($request['type']) && ($request['type'] ==  '1' || $request['type'] ==  '2')) {
            $field[$k]["fieldstring"] = "region_name";
            $field[$k]["fieldname"] = "名称";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;
        }elseif(isset($request['type']) && $request['type'] ==  '3'){
            $field[$k]["fieldstring"] = "school_cnname";
            $field[$k]["fieldname"] = "名称";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;
        }elseif(isset($request['type']) && ($request['type'] ==  '4' || $request['type'] ==  '5')){
            $field[$k]["fieldstring"] = "organize_cnname";
            $field[$k]["fieldname"] = "名称";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;
        }

        $field[$k]["fieldstring"] = "schoolNum";
        $field[$k]["fieldname"] = "校所数量";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["sortable"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "residenceNum";
        $field[$k]["fieldname"] = "在籍生数量";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["sortable"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "studyNum";
        $field[$k]["fieldname"] = "在读学员";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["sortable"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "stafferNum";
        $field[$k]["fieldname"] = "教职工数量";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["sortable"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $dataList['allnum'];
        if($dataList['list']){
            $result['list'] = $dataList['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        }else{
            $result['list'] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => '暂无数据', 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }

    //获取有学校的省
    function getRegionApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);

//        $datalist = $this->DataControl->selectClear("SELECT region_id,region_name FROM smc_code_region WHERE region_level = '2' AND region_id IN (SELECT school_province FROM smc_school WHERE company_id = '{$request['company_id']}')");

        $info = $this->getIPView();
        $info = json_decode($info, true);
        if($info['status'] == '0'){
            $province = $info['result']['ad_info']['province'];
        }else{
            $province = '上海';
        }

        $result = array();
        $result['list'] = $province;
        $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);

        ajax_return($res,$request['language_type']);
    }

    function getSchoolAddressApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);

        $datalist = $this->DataControl->selectClear("SELECT
                                                            s.school_cnname,s.school_address,
                                                            (SELECT r.region_name FROM smc_code_region as r WHERE r.region_id = s.school_province) as province_name,
                                                            (SELECT r.region_name FROM smc_code_region as r WHERE r.region_id = s.school_city) as city_name,
                                                            (SELECT r.region_name FROM smc_code_region as r WHERE r.region_id = s.school_area) as area_name
                                                        FROM
                                                            smc_school as s
                                                        WHERE
                                                            s.company_id = '{$request['company_id']}'");
        if($datalist){
            $data = array();
            foreach($datalist as $k => $v){
                $data[$k]['name'] = $v['school_cnname'];
                $address = $v['province_name'].$v['city_name'].$v['area_name'].$v['school_address'];
                $url='http://api.map.baidu.com/geocoder/v2/?address='.$address.'&output=json&ak=aYGx2p9vyoFVh0KMI0Ds79eNSqCxPUO4';
                $ch = curl_init();
                $timeout = 5;
                curl_setopt($ch, CURLOPT_URL, $url);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
                curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, $timeout);
                $contents = curl_exec($ch);
                curl_close($ch);
                $arr = json_decode($contents, true);
                if($arr['result']){
                    $data[$k]['value'][0] = $arr['result']['location']['lng'];
                    $data[$k]['value'][1] = $arr['result']['location']['lat'];
                }else{
                    $data[$k]['value'] = array();
                }
            }
        }else{
            $data = array();
        }

        $result = array();
        if($data){
            $result['list'] = $data;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        }else{
            $result['list'] = array();
            $res = array('error' => 1, 'errortip' => '暂时数据', 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }

    function getIPView()
    {
        $ip = $_SERVER['REMOTE_ADDR'];
        $url = "https://apis.map.qq.com/ws/location/v1/ip?ip={$ip}&key=T2TBZ-JVDWU-CIYVV-2VX7S-5JHQT-XSFQN";
        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        $contents = curl_exec($ch);
        curl_close($ch);

        return $contents;
    }
}