<?php


namespace Work\Controller\Gmcapi;


class ReportFinanceController extends viewTpl
{
    public $u;
    public $t;
    public $c;

    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
    }

    //学员实际欠费明细表--X-111
    function studentUnReceView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Report\Gmc\FinanceReportModel($request);
        $res = $ReportModel->studentUnReceReport($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "school_id";
        $field[$k]["fieldname"] = "学校id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "province_name";
        $field[$k]["fieldname"] = "省份";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_id";
        $field[$k]["fieldname"] = "学员ID";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "student_branch";
        $field[$k]["fieldname"] = "学员编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["fieldname"] = "学员中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_enname";
        $field[$k]["fieldname"] = "学员英文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "channel_name";
        $field[$k]["fieldname"] = "专案名称";
        $field[$k]["show"] = (isset($request["company_id"]) && $request["company_id"] == '8888') ? 1 : 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "course_branch";
        $field[$k]["fieldname"] = "课程别";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_branch";
        $field[$k]["fieldname"] = "班级编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_cnname";
        $field[$k]["fieldname"] = "班级名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_enname";
        $field[$k]["fieldname"] = "班级别名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "ordercourse_totalprice";
        $field[$k]["fieldname"] = "购买金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "ordercourse_buynums";
        $field[$k]["fieldname"] = "购买课次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "unpaid_money";
        $field[$k]["fieldname"] = "欠费金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "unpaid_times";
        $field[$k]["fieldname"] = "欠费课次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "unpaid_day";
        $field[$k]["fieldname"] = "欠费日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res['list']) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => $ReportModel->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    //学员充值明细表
    function studentInvestView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Smc\ReportModel($request);
        $res = $ReportModel->studentInvest($request);

        $field = array();

        $k = 0;

        $field[$k]["fieldstring"] = "order_pid";
        $field[$k]["fieldname"] = "交易订单编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["fieldname"] = "学员中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_enname";
        $field[$k]["fieldname"] = "学员英文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_branch";
        $field[$k]["fieldname"] = "学员编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "order_allprice";
        $field[$k]["fieldname"] = "订单总额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "order_status";
        $field[$k]["fieldname"] = "订单状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "order_createtime";
        $field[$k]["fieldname"] = "下单时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res['list']) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => "暂无学员充值明细报表", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    //学员账单欠费明细表--X
//    function stuUnpaidReportView()
//    {
//        $request = Input('get.', '', 'trim,addslashes');
//        $this->ThisVerify($request);
//        $ReportModel = new \Model\Smc\ReportModel($request);
//        $res = $ReportModel->orderUnpaidReport($request);
//
//
//        $k = 0;
//        $field = array();
//
//        $field[$k]["fieldstring"] = "school_id";
//        $field[$k]["fieldname"] = "学校ID";
//        $field[$k]["show"] = 0;
//        $field[$k]["custom"] = 1;
//        $k++;
//
//        $field[$k]["fieldstring"] = "school_branch";
//        $field[$k]["fieldname"] = "校区编号";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//        $k++;
//
//        $field[$k]["fieldstring"] = "school_cnname";
//        $field[$k]["fieldname"] = "校区名称";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//        $k++;
//
//        $field[$k]["fieldstring"] = "school_enname";
//        $field[$k]["fieldname"] = "检索代码";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//        $k++;
//
//        $field[$k]["fieldstring"] = "student_branch";
//        $field[$k]["fieldname"] = "学员编号";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//        $k++;
//
//        $field[$k]["fieldstring"] = "student_cnname";
//        $field[$k]["fieldname"] = "学员中文名";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//        $k++;
//
//        $field[$k]["fieldstring"] = "student_enname";
//        $field[$k]["fieldname"] = "学员英文名";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//        $k++;
//
//        $field[$k]["fieldstring"] = "order_pid";
//        $field[$k]["fieldname"] = "订单编号";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//        $k++;
//
//        $field[$k]["fieldstring"] = "order_createtime";
//        $field[$k]["fieldname"] = "下单日期";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//        $k++;
//
//        $field[$k]["fieldstring"] = "order_note";
//        $field[$k]["fieldname"] = "订单备注";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//        $k++;
//
//        $field[$k]["fieldstring"] = "order_from";
//        $field[$k]["fieldname"] = "订单来源";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//        $k++;
//
//        $field[$k]["fieldstring"] = "order_type";
//        $field[$k]["fieldname"] = "订单类型";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//        $k++;
//
//        $field[$k]["fieldstring"] = "order_status";
//        $field[$k]["fieldname"] = "订单状态";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//        $k++;
//
//        $field[$k]["fieldstring"] = "order_allprice";
//        $field[$k]["fieldname"] = "订单总金额";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//        $k++;
//
//        $field[$k]["fieldstring"] = "order_coupon_price";
//        $field[$k]["fieldname"] = "优惠券抵扣金额";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//        $k++;
//
//        $field[$k]["fieldstring"] = "order_market_price";
//        $field[$k]["fieldname"] = "营销活动抵扣金额";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//        $k++;
//
//        $field[$k]["fieldstring"] = "order_paymentprice";
//        $field[$k]["fieldname"] = "订单应付总金额";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//        $k++;
//
//        $field[$k]["fieldstring"] = "order_paidprice";
//        $field[$k]["fieldname"] = "订单已付总金额";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//        $k++;
//
//        $field[$k]["fieldstring"] = "order_arrearageprice";
//        $field[$k]["fieldname"] = "订单欠费总金额";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//        $k++;
//
//        if (isset($request['pay_type']) && $request['pay_type'] !== '') {
//            switch ($request['pay_type']) {
//                case "0":
//                    $field[$k]["fieldstring"] = "ordercourse_totalprice";
//                    $field[$k]["fieldname"] = "课程应收金额";
//                    $field[$k]["show"] = 1;
//                    $field[$k]["custom"] = 1;
//                    $k++;
//
//                    $field[$k]["fieldstring"] = "ordercourse_paidprice";
//                    $field[$k]["fieldname"] = "课程已付金额";
//                    $field[$k]["show"] = 1;
//                    $field[$k]["custom"] = 1;
//                    $k++;
//
//                    $field[$k]["fieldstring"] = "ordercourse_ownprice";
//                    $field[$k]["fieldname"] = "课程欠费金额";
//                    $field[$k]["show"] = 1;
//                    $field[$k]["custom"] = 1;
//                    $k++;
//                    break;
//                case "1":
//                    $field[$k]["fieldstring"] = "ordergoods_totalprice";
//                    $field[$k]["fieldname"] = "教材应收金额";
//                    $field[$k]["show"] = 1;
//                    $field[$k]["custom"] = 1;
//                    $k++;
//
//                    $field[$k]["fieldstring"] = "ordergoods_paidprice";
//                    $field[$k]["fieldname"] = "教材已付金额";
//                    $field[$k]["show"] = 1;
//                    $field[$k]["custom"] = 1;
//                    $k++;
//
//                    $field[$k]["fieldstring"] = "ordergoods_ownprice";
//                    $field[$k]["fieldname"] = "教材欠费金额";
//                    $field[$k]["show"] = 1;
//                    $field[$k]["custom"] = 1;
//                    $k++;
//                    break;
//                case "2":
//                    $field[$k]["fieldstring"] = "orderitem_totalprice";
//                    $field[$k]["fieldname"] = "杂费应收金额";
//                    $field[$k]["show"] = 1;
//                    $field[$k]["custom"] = 1;
//                    $k++;
//
//                    $field[$k]["fieldstring"] = "orderitem_paidprice";
//                    $field[$k]["fieldname"] = "杂费已付金额";
//                    $field[$k]["show"] = 1;
//                    $field[$k]["custom"] = 1;
//                    $k++;
//
//                    $field[$k]["fieldstring"] = "orderitem_ownprice";
//                    $field[$k]["fieldname"] = "杂费欠费金额";
//                    $field[$k]["show"] = 1;
//                    $field[$k]["custom"] = 1;
//                    $k++;
//                    break;
//                default:
//                    break;
//            }
//        }
//
//        $result = array();
//        $result["field"] = $field;
//        $result["allnum"] = $res['allnum'];
//        if ($res) {
//            $result["fieldcustom"] = 1;
//            $result["list"] = $res['list'];
//            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
//        } else {
//            $result["list"] = array();
//            $result["allnum"] = 0;
//            $res = array('error' => 1, 'errortip' => "暂无学员未缴费记录", 'result' => $result);
//        }
//        ajax_return($res, $request['language_type']);
//
//    }

    //校园收入统计报表
    function schoolIncomeView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Gmc\ReportModel($request);
        $res = $ReportModel->schoolIncome($request);
        $field = array();

        $k = 0;

        $field[$k]["fieldstring"] = "school_id";
        $field[$k]["fieldname"] = "学校ID";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "income_month";
        $field[$k]["fieldname"] = "收入月份";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "income_type";
        $field[$k]["fieldname"] = "收入类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "income_total_count";
        $field[$k]["fieldname"] = "收入单数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "income_student_count";
        $field[$k]["fieldname"] = "涉及学员人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "income_total_price";
        $field[$k]["fieldname"] = "收入总计";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res['list']) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => "暂无校园收入统计报表", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //学员续费预估统计表
    function studCourseEstimateView()
    {
        error_reporting(E_ALL);
        ini_set('display_errors', '1');
        ini_set("memory_limit", '-1');
        set_time_limit(600);
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Report\Gmc\FinanceReportModel($request);
        $res = $ReportModel->studCourseEstimateSummary($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "school_id";
        $field[$k]["fieldname"] = "学校id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "province_name";
        $field[$k]["fieldname"] = "省份";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_id";
        $field[$k]["fieldname"] = "学员id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "student_branch";
        $field[$k]["fieldname"] = "学员编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["fieldname"] = "学员中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_enname";
        $field[$k]["fieldname"] = "学员英文名";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "is_renewal";
        $field[$k]["fieldname"] = "是否续费";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "renewal_times";
        $field[$k]["fieldname"] = "续费评估课次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "renewal_amount";
        $field[$k]["fieldname"] = "班组剩余金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "unpaid_price";
        $field[$k]["fieldname"] = "班组欠费金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "spend_price";
        $field[$k]["fieldname"] = "后续耗课金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "estimate_price";
        $field[$k]["fieldname"] = "续费评估金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "connect_times";
        $field[$k]["fieldname"] = "电访结果";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "connect_main";
        $field[$k]["fieldname"] = "主管电访结果";
        $field[$k]["show"] = (isset($request["company_id"]) && $request["company_id"] == '8888') ? 1 : 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "track_note";
        $field[$k]["fieldname"] = "沟通内容";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursebalance_time";
        $field[$k]["fieldname"] = "剩余课次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursebalance_figure";
        $field[$k]["fieldname"] = "剩余课时金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "left_free_times";
        $field[$k]["fieldname"] = "剩余免费课次";
        $field[$k]["show"] = (isset($request["company_id"]) && $request["company_id"] == '8888') ? 1 : 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "channel_name";
        $field[$k]["fieldname"] = "专案名称";
        $field[$k]["show"] = (isset($request["company_id"]) && $request["company_id"] == '8888') ? 1 : 0;
        $field[$k]["custom"] = 1;
        $k++;

//        $field[$k]["fieldstring"] = "free_times_left_sent";
//        $field[$k]["fieldname"] = "已赠送剩余免费课";
//        $field[$k]["show"] = (isset($request["company_id"]) && $request["company_id"] == '8888') ? 1 : 0;
//        $field[$k]["custom"] = 1;
//        $k++;
//
//        $field[$k]["fieldstring"] = "free_times_left_unsent";
//        $field[$k]["fieldname"] = "未赠送免费课次";
//        $field[$k]["show"] = (isset($request["company_id"]) && $request["company_id"] == '8888') ? 1 : 0;
//        $field[$k]["custom"] = 1;
//        $k++;
//
//        $field[$k]["fieldstring"] = "free_times_left_all";
//        $field[$k]["fieldname"] = "剩余总免费课次";
//        $field[$k]["show"] = (isset($request["company_id"]) && $request["company_id"] == '8888') ? 1 : 0;
//        $field[$k]["custom"] = 1;
//        $k++;

        $field[$k]["fieldstring"] = "class_id";
        $field[$k]["fieldname"] = "班级id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "class_branch";
        $field[$k]["fieldname"] = "班级编号";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_cnname";
        $field[$k]["fieldname"] = "班级名称";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_enname";
        $field[$k]["fieldname"] = "班级别名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_enddate";
        $field[$k]["fieldname"] = "班级结束日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "main_teacher";
        $field[$k]["fieldname"] = "主教教师";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        /*$field[$k]["fieldstring"] = "sub_teacher";
        $field[$k]["fieldname"] = "助教教师";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;*/

        $field[$k]["fieldstring"] = "course_branch";
        $field[$k]["fieldname"] = "课程别编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "course_cnname";
        $field[$k]["fieldname"] = "课程别名称";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursecat_branch";
        $field[$k]["fieldname"] = "班种编号";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursecat_cnname";
        $field[$k]["fieldname"] = "班种名称";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursetype_branch";
        $field[$k]["fieldname"] = "班组编号";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursetype_cnname";
        $field[$k]["fieldname"] = "班组名称";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_num";
        $field[$k]["fieldname"] = "班级课次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "course_class_num";
        $field[$k]["fieldname"] = "课程别设定课次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res['list']) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => "暂无学员续费预估信息", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    //学员实际留班统计表
    function studEndcalcEstimateView()
    {
        error_reporting(E_ALL);
        ini_set('display_errors', '1');
        ini_set("memory_limit", '-1');
        set_time_limit(600);
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Report\Gmc\FinanceReportModel($request);
        $res = $ReportModel->studEndcalcEstimateSummary($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "school_id";
        $field[$k]["fieldname"] = "学校id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "province_name";
        $field[$k]["fieldname"] = "省份";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_id";
        $field[$k]["fieldname"] = "学员id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "student_branch";
        $field[$k]["fieldname"] = "学员编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["fieldname"] = "学员中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_enname";
        $field[$k]["fieldname"] = "学员英文名";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "channel_name";
        $field[$k]["fieldname"] = "专案名称";
        $field[$k]["show"] = (isset($request["company_id"]) && $request["company_id"] == '8888') ? 1 : 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "is_renewal";
        $field[$k]["fieldname"] = "是否续费";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "study_nexttimes";
        $field[$k]["fieldname"] = "续费课次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "study_nextprice";
        $field[$k]["fieldname"] = "续费金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "study_upgraderate";
        $field[$k]["fieldname"] = "留班课时比";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "study_upgradeprice";
        $field[$k]["fieldname"] = "三期缴费标识列";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "connect_times";
        $field[$k]["fieldname"] = "电访结果";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "connect_main";
        $field[$k]["fieldname"] = "主管电访结果";
        $field[$k]["show"] = (isset($request["company_id"]) && $request["company_id"] == '8888') ? 1 : 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "track_note";
        $field[$k]["fieldname"] = "沟通内容";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_id";
        $field[$k]["fieldname"] = "班级id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "class_branch";
        $field[$k]["fieldname"] = "班级编号";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_cnname";
        $field[$k]["fieldname"] = "班级名称";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_enname";
        $field[$k]["fieldname"] = "班级别名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_enddate";
        $field[$k]["fieldname"] = "班级结束日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "study_endday";
        $field[$k]["fieldname"] = "学员出班日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "main_teacher";
        $field[$k]["fieldname"] = "主教教师";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "course_branch";
        $field[$k]["fieldname"] = "课程别编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "course_cnname";
        $field[$k]["fieldname"] = "课程别名称";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursecat_branch";
        $field[$k]["fieldname"] = "班种编号";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursecat_cnname";
        $field[$k]["fieldname"] = "班种名称";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursetype_branch";
        $field[$k]["fieldname"] = "班组编号";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursetype_cnname";
        $field[$k]["fieldname"] = "班组名称";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_num";
        $field[$k]["fieldname"] = "班级课次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "course_class_num";
        $field[$k]["fieldname"] = "课程别设定课次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res['list']) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => "暂无学员续费预估信息", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }


}
