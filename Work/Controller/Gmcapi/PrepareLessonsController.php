<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/13
 * Time: 23:25
 */

namespace Work\Controller\Gmcapi;


class PrepareLessonsController extends viewTpl
{
    public $u;
    public $t;
    public $c;
    public $visitType = "api";
    public $Model;

    //预加载处理类
    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
    }

    function ThisVerify($request){
        $paramArray = array();
        $paramArray['staffer_id'] = $request['staffer_id'];
        $paramArray['company_id'] = $request['company_id'];
        $paramArray['token'] = $request['token'];
        if(!$this->UserLimit($paramArray)){
            $result = array();
            $result["list"] = array();
            $result["tokeninc"] = "0";
            $res = array('error' => '1', 'errortip' => "用户token失效", 'result' => $result);
            ajax_return($res,$request['language_type']);
        }
    }

    //班别教务设置
    function HomeView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Gmc\PrepareLessonsModel($request);
        $result = $Model->getClassPostList($request);
        ajax_return($result,$request['language_type']);
    }

    //班别课次明细
    function ClassHourView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Gmc\PrepareLessonsModel($request);

        $result = $Model->ClassHourList($request);
        ajax_return($result,$request['language_type']);
    }

    //一键生成班别课次
    function AddClassHourAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Gmc\PrepareLessonsModel($request);

        $result = $Model->addClassHour($request);
        ajax_return($result,$request['language_type']);
    }

    //编辑班别课次
    function EditClassHourAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Gmc\PrepareLessonsModel($request);

        $result = $Model->editClassHour($request);
        ajax_return($result,$request['language_type']);
    }

    //删除班别课次
    function DelClassHourAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Gmc\PrepareLessonsModel($request);

        $result = $Model->delClassHour($request);
        ajax_return($result,$request['language_type']);
    }

    //教案明细管理
    function TeachPlanView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Gmc\PrepareLessonsModel($request);

        $result = $Model->TeachPlanList($request);
        ajax_return($result,$request['language_type']);
    }

    //删除教案
    function DelTeachPlanAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Gmc\PrepareLessonsModel($request);

        $result = $Model->DelTeachPlanAction($request);
        ajax_return($result,$request['language_type']);
    }

    //是否开启个性化服务
    function OpenStatusAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Gmc\PrepareLessonsModel($request);

        $result = $Model->OpenStatusAction($request);
        ajax_return($result,$request['language_type']);
    }

    //是否开启个性化服务
    function AllStatusAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Gmc\PrepareLessonsModel($request);

        $result = $Model->AllStatusAction($request);
        ajax_return($result,$request['language_type']);
    }

    //是否开启备课
    function OpenBeiKeAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Gmc\PrepareLessonsModel($request);

        $result = $Model->OpenBeiKeAction($request);
        ajax_return($result,$request['language_type']);
    }

    //是否开启小循环登记
    function OpenCourseRegisterAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Gmc\PrepareLessonsModel($request);

        $result = $Model->OpenCourseRegisterAction($request);
        ajax_return($result,$request['language_type']);
    }

    //课次是否开启小循环登记
    function OpenRegisterAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Gmc\PrepareLessonsModel($request);

        $result = $Model->OpenRegisterAction($request);
        ajax_return($result,$request['language_type']);
    }

    //课次是否开启备课
    function HourOpenBeikeAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Gmc\PrepareLessonsModel($request);

        $result = $Model->HourOpenBeikeAction($request);
        ajax_return($result,$request['language_type']);
    }

    //添加教案
    function AddTeachPlanAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Gmc\PrepareLessonsModel($request);

        $result = $Model->AddTeachPlanAction($request);
        ajax_return($result,$request['language_type']);
    }

    //编辑教案
    function EditTeachPlanAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Gmc\PrepareLessonsModel($request);

        $result = $Model->EditTeachPlanAction($request);
        ajax_return($result,$request['language_type']);
    }

    //课程资料
    function CourseMaterialsView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Gmc\PrepareLessonsModel($request);

        $result = $Model->CourseMaterials($request);
        ajax_return($result,$request['language_type']);
    }

    //上传课程资料
    function UploadFileAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Gmc\PrepareLessonsModel($request);

        $result = $Model->UploadFileApi($request);
        ajax_return($result,$request['language_type']);
    }

    //删除课程资料
    function DelCourseFileAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Gmc\PrepareLessonsModel($request);

        $result = $Model->delCourseFileApi($request);
        ajax_return($result,$request['language_type']);
    }

    //优秀视频管理
    function TempVideoView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Gmc\PrepareLessonsModel($request);

        $result = $Model->TempVideoApi($request);
        ajax_return($result,$request['language_type']);
    }

    //优秀视频审核查看
    function TempVideoDetailView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Gmc\PrepareLessonsModel($request);

        $result = $Model->TempVideoDetail($request);
        ajax_return($result,$request['language_type']);
    }

    //优秀视频审核
    function ExamineVideoAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Gmc\PrepareLessonsModel($request);

        $result = $Model->ExamineVideoApi($request);
        ajax_return($result,$request['language_type']);
    }

    //优秀教学作品管理
    function TempPostilView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Gmc\PrepareLessonsModel($request);

        $result = $Model->TempPostilApi($request);
        ajax_return($result,$request['language_type']);
    }

    //优秀教学作品审核查看
    function TempPostilDetailView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Gmc\PrepareLessonsModel($request);

        $result = $Model->TempPostilDetail($request);
        ajax_return($result,$request['language_type']);
    }

    //优秀教学作品查看教案
    function TempPostilSeeView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Gmc\PrepareLessonsModel($request);

        $result = $Model->TempPostilSee($request);
        ajax_return($result,$request['language_type']);
    }

    //优秀教学作品审核
    function ExaminePostilAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Gmc\PrepareLessonsModel($request);

        $result = $Model->ExaminePostilApi($request);
        ajax_return($result,$request['language_type']);
    }

    //班别下拉列表
    function getCourseApi()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $companiesList = $this->DataControl->selectClear("select course_id,course_cnname,course_branch
FROM smc_course where company_id = '{$request['company_id']}'");
        if (!$companiesList) {
            $companiesList = array();
        }

        $result["list"] = $companiesList;
        $res = array('error' => 0, 'errortip' => '获取班别下拉列表', 'result' => $result);
        ajax_return($res,$request['language_type']);
    }

    //班组下拉列表
    function getCourseTypeApi()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $sql = "select coursetype_id,coursetype_cnname,coursetype_branch from smc_code_coursetype where company_id = '{$request['company_id']}'";
        $CompaniesList = $this->DataControl->selectClear($sql);
        if (!$CompaniesList) {
            $CompaniesList = array();
        }

        $result["list"] = $CompaniesList;
        $res = array('error' => 0, 'errortip' => '获取班组下拉列表', 'result' => $result);

        ajax_return($res,$request['language_type']);
    }

    //班种下拉列表
    function getCourseCatApi()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $sql = "select coursecat_id,coursecat_cnname,coursecat_branch from smc_code_coursecat where company_id = '{$request['company_id']}'";
        $CompaniesList = $this->DataControl->selectClear($sql);
        if (!$CompaniesList) {
            $CompaniesList = array();
        }

        $result["list"] = $CompaniesList;
        $res = array('error' => 0, 'errortip' => '获取班种下拉列表', 'result' => $result);

        ajax_return($res,$request['language_type']);
    }

    //文件下载
    function getDownloadView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $full_path = $request['file_url'];
        $kuozhan = strrchr($full_path, '.');
        $full_name = $request['file_name'] . $kuozhan;

        header("Content-Type: video/{$kuozhan}");
        header("Content-Disposition: attachment;filename={$full_name}");
        readfile($full_path);
    }

    //结尾魔术函数
    function __destruct()
    {

    }
}
