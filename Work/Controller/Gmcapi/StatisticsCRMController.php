<?php


namespace Work\Controller\Gmcapi;


class StatisticsCRMController extends viewTpl
{
    public $u;
    public $t;
    public $c;

    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
    }

    /**
     * 招生统计状况
     * author: ling
     * 对应接口文档 0001
     */
    function CountOverCRMView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);

        $ReportModel = new \Model\Gmc\StatisticsCRMModel($request);
        $dataList = $ReportModel->CountOverCRM();
        $res = array('error' => '0', 'errortip' => '', 'result' => $dataList);
        ajax_return($res,$request['language_type']);
    }

    /**
     * 招生客户状况
     * author: ling
     * 对应接口文档 0001
     */
    function StatisticsClientView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Gmc\StatisticsCRMModel($request);
        $dataList = $ReportModel->StatisticsClient($request);
        $res = array('error' => '0', 'errortip' => '', 'result' => $dataList);
        ajax_return($res,$request['language_type']);
    }

    /**
     * 招生趋势
     * author: ling
     * 对应接口文档 0001
     */
    function CountClientByYearView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Gmc\StatisticsCRMModel($request);
        $dataList = $ReportModel->CountClientByYear($request);
        $res = array('error' => '0', 'errortip' => '获取招生趋势', 'result' => $dataList);
        ajax_return($res,$request['language_type']);
    }


    //------------------------------------招生渠道-----------------------

    //招生渠道新增
    function CountClientSourceView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Gmc\StatisticsCRMModel($request);
        $dataList = $ReportModel->CountClientSource($request);
        $res = array('error' => '0', 'errortip' => '', 'result' => $dataList);
        ajax_return($res,$request['language_type']);
    }

    /**
     * 招生渠道转化对比
     * author: ling
     * 对应接口文档 0001
     * @return array
     */
    function ClientChannelView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Gmc\StatisticsCRMModel($request);
        $dataList = $ReportModel->ClientChannel($request);
        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "frommedia_name";
        $field[$k]["fieldname"] = "招生渠道名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "channel_num";
        $field[$k]["fieldname"] = "招生渠道明细数量";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["sortable"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "client_num";
        $field[$k]["fieldname"] = "有效名单数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["sortable"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "client_intentionnum";
        $field[$k]["fieldname"] = "意向客户数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["sortable"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "client_invitenum";
        $field[$k]["fieldname"] = "柜询客户数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["sortable"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "client_auditionnum";
        $field[$k]["fieldname"] = "试听客户数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["sortable"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "client_lossnum";
        $field[$k]["fieldname"] = "无意向客户";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["sortable"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "client_positivenum";
        $field[$k]["fieldname"] = "转正客户";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["sortable"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "rate";
        $field[$k]["fieldname"] = "转化率";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["sortable"] = 1;

        $result = array();
        $result['field'] = $field;
        $result['list'] = $dataList['list'];
        $result['allnum'] = $dataList['allnum'];
        $res = array('error' => '0', 'errortip' => '招生渠道转化对比', 'result' => $result);
        ajax_return($res,$request['language_type']);
    }

    /**
     * 转正来源渠道对比
     * author: ling
     * 对应接口文档 0001
     */
    function PositiveChannelView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Gmc\StatisticsCRMModel($request);
        $dataList = $ReportModel->PositiveChannel($request);
        $res = array('error' => '0', 'errortip' => '', 'result' => $dataList);
        ajax_return($res,$request['language_type']);
    }

    /**
     * 招生渠道比列
     * author: ling
     * 对应接口文档 0001
     */
    function ChannelRateView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Gmc\StatisticsCRMModel($request);
        $dataList = $ReportModel->ChannelRate($request);
        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "frommedia_name";
        $field[$k]["fieldname"] = "招生渠道名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "channel_num";
        $field[$k]["fieldname"] = "对应渠道明细数量";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "activity_num";
        $field[$k]["fieldname"] = "活动数量";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "rate";
        $field[$k]["fieldname"] = "占比";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $result = array();
        $result['field'] = $field;
        $result['list'] = $dataList;

        $res = array('error' => '0', 'errortip' => '', 'result' => $result);
        ajax_return($res,$request['language_type']);

    }

    /**
     * J集团活动转化率
     * author: ling
     * 对应接口文档 0001
     */
    function CompanyActivityPositiveView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Gmc\StatisticsCRMModel($request);
        $dataList = $ReportModel->CompanyActivityPositive($request);
        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "activity_name";
        $field[$k]["fieldname"] = "活动名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "client_allnum";
        $field[$k]["fieldname"] = "有效名单数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["sortable"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "client_lossnum";
        $field[$k]["fieldname"] = "无效名单数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["sortable"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "client_positivenum";
        $field[$k]["fieldname"] = "转正人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "rate";
        $field[$k]["fieldname"] = "转化率";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["sortable"] = 1;
        $result = array();
        $result['field'] = $field;
        $result['list'] = $dataList['list'];
        $result['allnum'] = $dataList['allnum'];

        $res = array('error' => '0', 'errortip' => '集团活动转化率', 'result' => $result);
        ajax_return($res,$request['language_type']);
    }

    /**
     * 学校活动转化率
     * author: ling
     * 对应接口文档 0001
     */
    function SchoolActivityPositiveView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Gmc\StatisticsCRMModel($request);
        $dataList = $ReportModel->SchoolActivityPositive($request);
        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "activity_name";
        $field[$k]["fieldname"] = "活动名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "client_allnum";
        $field[$k]["fieldname"] = "有效名单数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["sortable"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "client_lossnum";
        $field[$k]["fieldname"] = "无效名单数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["sortable"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "client_positivenum";
        $field[$k]["fieldname"] = "转正人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["sortable"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "rate";
        $field[$k]["fieldname"] = "转化率";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["sortable"] = 1;
        $result = array();
        $result['field'] = $field;
        $result['list'] = $dataList['list'];
        $result['allnum'] = $dataList['allnum'];

        $res = array('error' => '0', 'errortip' => '校园活动转化率', 'result' => $result);
        ajax_return($res,$request['language_type']);
    }

    //---------------------------------------招生业绩------------------------------
    /**
     * 招生业绩
     * author: ling
     * 对应接口文档 0001
     */
    function CountClientPositiveView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Gmc\StatisticsCRMModel($request);
        $dataList = $ReportModel->CountClientPositive($request);
        $res = array('error' => '0', 'errortip' => '招生业绩统计', 'result' => $dataList);
        ajax_return($res,$request['language_type']);
    }
    /**
     * 招生跟进状况
     * author: ling
     * 对应接口文档 0001
     */
    function ClientTrackLogView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Gmc\StatisticsCRMModel($request);
        $dataList = $ReportModel->ClientTrackLog($request);
        $field = array();
        $k = 0;
        if($request['type'] ==1){
            $field[$k]["fieldstring"] = "region_name";
            $field[$k]["fieldname"] = "省";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
        }elseif($request['type'] ==2){
            $field[$k]["fieldstring"] = "region_name";
            $field[$k]["fieldname"] = "市";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
        }elseif($request['type'] ==3){
            $field[$k]["fieldstring"] = "school_cnname";
            $field[$k]["fieldname"] = "学校";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
        }elseif($request['type'] ==4){
            $field[$k]["fieldstring"] = "organize_cnname";
            $field[$k]["fieldname"] = "二级组织";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
        }elseif($request['type'] ==5){
            $field[$k]["fieldstring"] = "organize_cnname";
            $field[$k]["fieldname"] = "三级组织";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
        }else{
            $field[$k]["fieldstring"] = "school_cnname";
            $field[$k]["fieldname"] = "学校";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
        }

        $k++;
        $field[$k]["fieldstring"] = "client_num";
        $field[$k]["fieldname"] = "跟踪人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["sortable"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "track_num";
        $field[$k]["fieldname"] = "跟踪人次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["sortable"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "invite_num";
        $field[$k]["fieldname"] = "柜询预约数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["sortable"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "invite_arr_num";
        $field[$k]["fieldname"] = "柜询到访数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["sortable"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "oh_audition_num";
        $field[$k]["fieldname"] = "公开课预约数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["sortable"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "oh_audition_arr_num";
        $field[$k]["fieldname"] = "公开课到访数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["sortable"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "class_audition_num";
        $field[$k]["fieldname"] = "插班预约数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["sortable"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "class_audition_arr_num";
        $field[$k]["fieldname"] = "插班到访数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["sortable"] = 1;

        $result = array();
        $result['field'] = $field;
        $result['list'] = $dataList['list'];
        $result['allnum'] = $dataList['allnum'];
        $res = array('error' => '0', 'errortip' => '招生跟进状况', 'result' => $result);
        ajax_return($res,$request['language_type']);
    }
    /**
     * 招生转化排行
     * author: ling
     * 对应接口文档 0001
     */
    function ClientPosiviteView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        if (!$request['type']) {
            $request['type'] = 1;
        }
        $ReportModel = new \Model\Gmc\StatisticsCRMModel($request);
        $dataList = $ReportModel->ClientPosivite($request);
        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "number";
        $field[$k]["fieldname"] = "序号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        if($request['type'] ==1){
            $field[$k]["fieldstring"] = "region_name";
            $field[$k]["fieldname"] = "省";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
        }elseif($request['type'] ==2){
            $field[$k]["fieldstring"] = "region_name";
            $field[$k]["fieldname"] = "市";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
        }elseif($request['type'] ==3){
            $field[$k]["fieldstring"] = "region_name";
            $field[$k]["fieldname"] = "学校";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
        }elseif($request['type'] ==4){
            $field[$k]["fieldstring"] = "region_name";
            $field[$k]["fieldname"] = "二级组织";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
        }elseif($request['type'] ==5){
            $field[$k]["fieldstring"] = "region_name";
            $field[$k]["fieldname"] = "三级组织";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
        }

        $k++;
        $field[$k]["fieldstring"] = "client_allnum";
        $field[$k]["fieldname"] = "有效名单数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "uneffective_num";
        $field[$k]["fieldname"] = "无效名单数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "client_positivenum";
        $field[$k]["fieldname"] = "转正数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "rate";
        $field[$k]["fieldname"] = "转化率";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;


        $result = array();
        $result['field'] = $field;
        $result['list'] = $dataList['list'];
        $result['allnum'] = $dataList['allnum'];

        $res = array('error' => '0', 'errortip' => '招生转化排行', 'result' => $result);
        ajax_return($res,$request['language_type']);
    }
    /**
     * 招生转正数量排行
     * author: ling
     * 对应接口文档 0001
     */
    function schoolPositiveView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Gmc\StatisticsCRMModel($request);
        $dataList = $ReportModel->schoolPositive($request);
        $res = array('error' => '0', 'errortip' => '招生转正数量排行', 'result' => $dataList);
        ajax_return($res,$request['language_type']);

    }
}