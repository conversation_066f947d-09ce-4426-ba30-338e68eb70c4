<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/13
 * Time: 23:25
 */

namespace Work\Controller\Gmcapi;


class EasxSettingController extends viewTpl
{
    public $u;
    public $t;
    public $c;
    public $visitType = "api";
    public $Model;

    //预加载处理类
    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
    }

    function ThisVerify($request){
        $paramArray = array();
        $paramArray['staffer_id'] = $request['staffer_id'];
        $paramArray['company_id'] = $request['company_id'];
        $paramArray['token'] = $request['token'];
        if(!$this->UserLimit($paramArray)){
            $result = array();
            $result["list"] = array();
            $result["tokeninc"] = "0";
            $res = array('error' => '1', 'errortip' => "用户token失效", 'result' => $result);
            ajax_return($res,$request['language_type']);
        }
    }

    //评价维度列表
    function HomeView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\EasxSettingModel($request);

        $result = $this->Model->getSturemarktempList($request);
        ajax_return($result,$request['language_type']);
    }

    //适用课程别列表
    function SturemarktempApplyView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\EasxSettingModel($request);

        $result = $this->Model->SturemarktempApply($request);
        ajax_return($result,$request['language_type']);
    }

    //添加评价纬度
    function addSturemarktempAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\EasxSettingModel($request);

        $result = $this->Model->addSturemarktempAction($request);
        ajax_return($result,$request['language_type']);
    }

    //编辑评价纬度
    function updateSturemarktempAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\EasxSettingModel($request);

        $result = $this->Model->updateSturemarktempAction($request);
        ajax_return($result,$request['language_type']);
    }

    //删除评价纬度
    function delSturemarktempAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\EasxSettingModel($request);

        $result = $this->Model->delSturemarktempAction($request);
        ajax_return($result,$request['language_type']);
    }

    ///启用/不启用
    function OpenStatusAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\EasxSettingModel($request);

        $result = $this->Model->OpenStatusAction($request);
        ajax_return($result,$request['language_type']);
    }

    //课堂评价模版列表
    function remarktempView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\EasxSettingModel($request);

        $result = $this->Model->remarktemp($request);
        ajax_return($result,$request['language_type']);
    }

    //新增课堂评价模版
    function addRemarktempAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\EasxSettingModel($request);

        $result = $this->Model->addRemarktempAction($request);
        ajax_return($result,$request['language_type']);
    }

    //编辑课堂评价模版
    function updateRemarktempAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\EasxSettingModel($request);

        $result = $this->Model->updateRemarktempAction($request);
        ajax_return($result,$request['language_type']);
    }

    //删除课堂评价模版
    function delRemarktempAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\EasxSettingModel($request);

        $result = $this->Model->delRemarktempAction($request);
        ajax_return($result,$request['language_type']);
    }

    //启用/不启用
    function OpenStatusRemarkAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\EasxSettingModel($request);

        $result = $this->Model->OpenStatusRemarkAction($request);
        ajax_return($result,$request['language_type']);
    }

    //课堂评价类型列表
    function remarktempTypeView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\EasxSettingModel($request);

        $result = $this->Model->remarktempType($request);
        ajax_return($result,$request['language_type']);
    }

    //新增课堂类型模版
    function addRemarktempTypeAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\EasxSettingModel($request);

        $result = $this->Model->addRemarktempTypeAction($request);
        ajax_return($result,$request['language_type']);
    }

    //编辑课堂类型模版
    function updateRemarktempTypeAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\EasxSettingModel($request);

        $result = $this->Model->updateRemarktempTypeAction($request);
        ajax_return($result,$request['language_type']);
    }

    //删除课堂类型模版
    function delRemarktempTypeAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\EasxSettingModel($request);

        $result = $this->Model->delRemarktempTypeAction($request);
        ajax_return($result,$request['language_type']);
    }

    //启用/不启用
    function OpenStatusRemarkTypeAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\EasxSettingModel($request);

        $result = $this->Model->OpenStatusRemarkTypeAction($request);
        ajax_return($result,$request['language_type']);
    }

    //作业评价模版列表
    function homeworktempView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\EasxSettingModel($request);

        $result = $this->Model->homeworktemp($request);
        ajax_return($result,$request['language_type']);
    }

    //新增作业评价模版
    function addHomeworktempAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\EasxSettingModel($request);

        $result = $this->Model->addHomeworktempAction($request);
        ajax_return($result,$request['language_type']);
    }

    //编辑作业评价模版
    function updateHomeworktempAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\EasxSettingModel($request);

        $result = $this->Model->updateHomeworktempAction($request);
        ajax_return($result,$request['language_type']);
    }

    //删除作业评价模版
    function delHomeworktempAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\EasxSettingModel($request);

        $result = $this->Model->delHomeworktempAction($request);
        ajax_return($result,$request['language_type']);
    }

    //启用/不启用
    function OpenStatusHomeworkAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\EasxSettingModel($request);

        $result = $this->Model->OpenStatusHomeworkAction($request);
        ajax_return($result,$request['language_type']);
    }

    //作业评价类型列表
    function HomeworkTypeView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\EasxSettingModel($request);

        $result = $this->Model->HomeworkType($request);
        ajax_return($result,$request['language_type']);
    }

    //新增作业类型模版
    function addHomeworkTypeAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\EasxSettingModel($request);

        $result = $this->Model->addHomeworkTypeAction($request);
        ajax_return($result,$request['language_type']);
    }

    //编辑作业类型模版
    function updateHomeworkTypeAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\EasxSettingModel($request);

        $result = $this->Model->updateHomeworkTypeAction($request);
        ajax_return($result,$request['language_type']);
    }

    //删除作业类型模版
    function delHomeworkTypeAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\EasxSettingModel($request);

        $result = $this->Model->delHomeworkTypeAction($request);
        ajax_return($result,$request['language_type']);
    }

    //启用/不启用
    function OpenStatusHomeworkTypeAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\EasxSettingModel($request);

        $result = $this->Model->OpenStatusHomeworkTypeAction($request);
        ajax_return($result,$request['language_type']);
    }

    //班别下拉列表
    function getCourseApi()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $sql = "select course_id,course_cnname,course_branch from smc_course where company_id = '{$request['company_id']}'";
        $CompaniesList = $this->DataControl->selectClear($sql);
        if (!$CompaniesList) {
            $CompaniesList = array();
        }

        $result["list"] = $CompaniesList;
        $res = array('error' => 0, 'errortip' => '获取班别下拉列表', 'result' => $result);

        ajax_return($res,$request['language_type']);
    }

    //班组下拉列表
    function getCourseTypeApi()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $sql = "select coursetype_id,coursetype_cnname,coursetype_branch from smc_code_coursetype where company_id = '{$request['company_id']}'";
        $CompaniesList = $this->DataControl->selectClear($sql);
        if (!$CompaniesList) {
            $CompaniesList = array();
        }

        $result["list"] = $CompaniesList;
        $res = array('error' => 0, 'errortip' => '获取班组下拉列表', 'result' => $result);

        ajax_return($res,$request['language_type']);
    }

    //班种下拉列表
    function getCourseCatApi()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $sql = "select coursecat_id,coursecat_cnname,coursecat_branch from smc_code_coursecat where company_id = '{$request['company_id']}'";
        $CompaniesList = $this->DataControl->selectClear($sql);
        if (!$CompaniesList) {
            $CompaniesList = array();
        }

        $result["list"] = $CompaniesList;
        $res = array('error' => 0, 'errortip' => '获取班种下拉列表', 'result' => $result);

        ajax_return($res,$request['language_type']);
    }

    //课堂点评类型下拉列表
    function getRemarktempApi()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $sql = "select remarktemp_type_id,remarktemp_type_name from eas_code_remarktemp_type where company_id = '{$request['company_id']}' and remarktemp_type_status = '1'";
        $CompaniesList = $this->DataControl->selectClear($sql);
        if (!$CompaniesList) {
            $CompaniesList = array();
        }

        $result["list"] = $CompaniesList;
        $res = array('error' => 0, 'errortip' => '获取课堂点评类型下拉列表', 'result' => $result);

        ajax_return($res,$request['language_type']);
    }

    //作业点评类型下拉列表
    function getHomeworkTypeApi()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $sql = "select homeworktemp_type_id,homeworktemp_type_name from eas_code_homeworktemp_type where company_id = '{$request['company_id']}' and homeworktemp_type_status = '1'";
        $CompaniesList = $this->DataControl->selectClear($sql);
        if (!$CompaniesList) {
            $CompaniesList = array();
        }

        $result["list"] = $CompaniesList;
        $res = array('error' => 0, 'errortip' => '获取作业点评类型下拉列表', 'result' => $result);

        ajax_return($res,$request['language_type']);
    }


    //学员沟通模版列表
    function comtempListView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\EasxSettingModel($request);

        $result = $this->Model->comtempList($request);
        ajax_return($result,$request['language_type']);
    }

    //添加学员沟通模版
    function addComtempAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\EasxSettingModel($request);

        $result = $this->Model->addComtempAction($request);
        ajax_return($result,$request['language_type']);
    }

    //编辑学员沟通模版
    function updateComtempAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\EasxSettingModel($request);

        $result = $this->Model->updateComtempAction($request);
        ajax_return($result,$request['language_type']);
    }

    //删除学员沟通模版
    function delComtempAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\EasxSettingModel($request);

        $result = $this->Model->delComtempAction($request);
        ajax_return($result,$request['language_type']);
    }

    ///启用/不启用
    function OpenComtempStatusAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\EasxSettingModel($request);

        $result = $this->Model->OpenComtempStatusAction($request);
        ajax_return($result,$request['language_type']);
    }

     //补充课程课次
     function  addCourseHourBranchAction(){

         $request = Input('get.','','trim,addslashes');
         $this->ThisVerify($request);//验证账户
         $this->Model = new \Model\Gmc\EasxSettingModel($request);
         $this->Model->addCourseType($request);
         $res = array('error' =>  $this->Model->error, 'errortip' =>$this->Model->errortip, 'result' => array());
         ajax_return($res,$request['language_type']);

     }


    //形容词列表
    function nounView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\EasxSettingModel($request);

        $result = $this->Model->noun($request);
        ajax_return($result,$request['language_type']);
    }

    //添加形容词
    function addNounAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\EasxSettingModel($request);

        $result = $this->Model->addNounAction($request);
        ajax_return($result,$request['language_type']);
    }

    //编辑形容词
    function updateNounAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\EasxSettingModel($request);

        $result = $this->Model->updateNounAction($request);
        ajax_return($result,$request['language_type']);
    }

    //删除形容词
    function delNounAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\EasxSettingModel($request);

        $result = $this->Model->delNounAction($request);
        ajax_return($result,$request['language_type']);
    }



    //结尾魔术函数
    function __destruct()
    {

    }
}
