<?php


namespace Work\Controller\Gmcapi;


class OrderController extends viewTpl
{
    public $u;
    public $t;
    public $c;

    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
    }

    function renewOrderListApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $OrderModel = new \Model\Gmc\OrderModel($request);
        $res = $OrderModel->renewOrderList($request);
        $field = array();
        $field[0]["fieldname"] = "order_pid";
        $field[0]["fieldstring"] = "订单编号";
        $field[0]["show"] = 1;
        $field[0]["custom"] = 1;

        $field[1]["fieldname"] = "school_cnname";
        $field[1]["fieldstring"] = "校区名称";
        $field[1]["show"] = 1;
        $field[1]["custom"] = 1;

        $field[2]["fieldname"] = "school_branch";
        $field[2]["fieldstring"] = "校区编号";
        $field[2]["show"] = 1;
        $field[2]["custom"] = 1;

        $field[3]["fieldname"] = "student_cnname";
        $field[3]["fieldstring"] = "学员名称";
        $field[3]["show"] = 1;
        $field[3]["custom"] = 1;

        $field[4]["fieldname"] = "student_branch";
        $field[4]["fieldstring"] = "学员编号";
        $field[4]["show"] = 1;
        $field[4]["custom"] = 1;

        $field[5]["fieldname"] = "order_paymentprice";
        $field[5]["fieldstring"] = "实际支付金额";
        $field[5]["show"] = 1;
        $field[5]["custom"] = 1;

        $field[6]["fieldname"] = "order_note";
        $field[6]["fieldstring"] = "订单备注";
        $field[6]["show"] = 1;
        $field[6]["custom"] = 1;

        $field[7]["fieldname"] = "order_createtime";
        $field[7]["fieldstring"] = "申请时间";
        $field[7]["show"] = 1;
        $field[7]["custom"] = 1;

        $field[8]["fieldname"] = "order_examinetime";
        $field[8]["fieldstring"] = "审核时间";
        $field[8]["show"] = 1;
        $field[8]["custom"] = 1;

        $result = array();

        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["list"] = $res['list'];
            $result["school"] = $res['school'];
            $result["fieldcustom"] = 0;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["school"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => '暂无订单信息', 'result' => $result);

        }
        ajax_return($res, $request['language_type']);

    }

    function getOrderOneByTradingView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $publicArray = array();

        $tradingOne = $this->DataControl->getFieldOne("smc_student_trading", "company_id,school_id", "trading_pid='{$request['trading_pid']}' and company_id='{$request['company_id']}'");

        $order_pid = $request['order_pid'];
        $publicArray['company_id'] = $request['company_id'];
        $publicArray['school_id'] = $tradingOne['school_id'];
        $publicArray['staffer_id'] = $request['staffer_id'];
        $publicArray['token'] = $request['token'];
        $OrderModel = new \Model\Smc\OrderModel($publicArray, $order_pid);
        $dataList = $OrderModel->getOrderOneByTrading($request['trading_pid']);

        $v = $this->DataControl->getFieldOne("gmc_company", "company_isvoucher", "company_id = '{$request['company_id']}'");

        if ($dataList && is_array($dataList['list']['paylist'])) {
            foreach ($dataList['list']['paylist'] as &$val) {
                $val['voucher'] = $v['company_isvoucher'];
            }
        }
        $res = array('error' => 0, 'errortip' => '获取订单详情成功', 'result' => $dataList);
        ajax_return($res, $request['language_type']);
    }

    //订单合同列表
    function getProtocolListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Gmc\OrderModel($request);

        $result = $Model->getProtocolList($request);

        ajax_return($result, $request['language_type']);
    }

    //协议详情
    function protocolDetailView()
    {
        $request = Input('get.', '', 'trim,addslashes,strip_tags');
        $this->ThisVerify($request);//验证账户

        //甲方
        $PartyA = array();
        $protocol = $this->DataControl->getOne("smc_student_protocol", "protocol_id = '{$request['protocol_id']}'");
        $order = $this->DataControl->selectOne("select o.order_pid,o.trading_pid,o.companies_id,p.pay_pid,p.pay_typename,p.pay_price,o.order_coupon_price,o.order_paidprice,o.order_arrearageprice,o.order_paymentprice,c.ordercourse_buynums,c.ordercourse_totalprice,c.ordercourse_unitprice from smc_payfee_order as o left join smc_payfee_order_pay as p on o.order_pid = p.order_pid left join smc_payfee_order_course as c on c.order_pid = o.order_pid where o.order_pid = '{$protocol['order_pid']}' and c.course_id='{$protocol['course_id']}'");

        $company = $this->DataControl->getFieldOne("gmc_company", "company_cnname,company_logo", "company_id = '{$request['company_id']}'");
        $school = $this->DataControl->getFieldOne("smc_school", "school_cnname,school_signet,school_address,school_phone,companies_id,school_liaison,school_examine,school_register,school_permitbranch,school_permitstday,school_permitendday,school_icp,school_licensestday,school_licenseendday,school_society,school_licensestatus", "school_id = '{$protocol['school_id']}'");
        $course = $this->DataControl->getFieldOne("smc_course", "course_cnname,course_branch,course_classnum,course_classtimes,coursetype_id,course_inclasstype,course_perhour,coursecat_id", "course_id = '{$protocol['course_id']}'");
        $companies_id = $this->DataControl->getFieldOne("smc_school_coursecat_subject", "companies_id", "school_id = '{$protocol['school_id']}' and coursecat_id = '{$course['coursecat_id']}'");
        $companies = $this->DataControl->getFieldOne("gmc_code_companies", "companies_cnname,companies_signet,companies_liaison,companies_examine,companies_register,companies_permitbranch,companies_permitstday,companies_permitendday,companies_icp,companies_licensestday,companies_licenseendday,companies_society,companies_licensestatus,companies_supervisebank,companies_superviseaccount,companies_settlebank,companies_settleaccount,companies_permitstday,companies_licensestday", "companies_id = '{$companies_id['companies_id']}'");

        $PartyA['companies_cnname'] = $companies['companies_cnname'];
        $PartyA['companies_permitstday'] = substr($companies['companies_permitstday'], strripos($companies['companies_permitstday'], "至") + 3);
        $PartyA['companies_licensestday'] = substr($companies['companies_licensestday'], strripos($companies['companies_licensestday'], "至") + 3);
        $PartyA['companies_supervisebank'] = $companies['companies_supervisebank'];
        $PartyA['companies_superviseaccount'] = $companies['companies_superviseaccount'];
        $PartyA['companies_settlebank'] = $companies['companies_settlebank'];
        $PartyA['companies_settleaccount'] = $companies['companies_settleaccount'];
        $PartyA['school_cnname'] = $school['school_cnname'];
        $PartyA['company_cnname'] = $company['company_cnname'];
        $PartyA['school_address'] = $school['school_address'];
        $PartyA['school_phone'] = $school['school_phone'];
        if ($school['school_phone']) {
            $PartyA['school_phone'] = $school['school_phone'];
        } else {
            $PartyA['school_phone'] = '--';
        }
        if ($companies['companies_liaison']) {
            $PartyA['school_liaison'] = $companies['companies_liaison'];
        } else {
            $PartyA['school_liaison'] = '--';
        }
        if ($companies['companies_examine']) {
            $PartyA['school_examine'] = $companies['companies_examine'];
        } else {
            $PartyA['school_examine'] = '--';
        }
        if ($companies['companies_register']) {
            $PartyA['school_register'] = $companies['companies_register'];
        } else {
            $PartyA['school_register'] = '--';
        }
        if ($companies['companies_permitbranch']) {
            $PartyA['school_permitbranch'] = $companies['companies_permitbranch'];
        } else {
            $PartyA['school_permitbranch'] = '--';
        }
        if ($companies['companies_permitstday']) {
            $PartyA['school_permitstday'] = $companies['companies_permitstday'];
        } else {
            $PartyA['school_permitstday'] = '--';
        }
        if ($companies['companies_permitstday']) {
            $PartyA['school_permitstday'] = $companies['companies_permitstday'];
        } else {
            $PartyA['school_permitstday'] = '--';
        }
        if ($companies['companies_licensestday']) {
            $PartyA['school_licensestday'] = $companies['companies_licensestday'];
        } else {
            $PartyA['school_licensestday'] = '--';
        }
        if ($companies['companies_society']) {
            $PartyA['school_society'] = $companies['companies_society'];
        } else {
            $PartyA['school_society'] = '--';
        }
        $PartyA['school_signet'] = $companies['companies_signet'];
        $PartyA['school_icp'] = $companies['companies_icp'];
        $PartyA['company_logo'] = $company['company_logo'];
        $PartyA['company_shortname'] = $school['company_shortname'];

        $PartyB = array();
        $student = $this->DataControl->getFieldOne("smc_student", "student_cnname,student_enname,student_birthday,student_sex,student_branch,student_idcard", "student_id = '{$protocol['student_id']}'");
        $PartyB['student_branch'] = $student['student_branch'];
        $PartyB['student_cnname'] = $student['student_cnname'];
        $PartyB['student_sex'] = $student['student_sex'];
        $PartyB['student_birthday'] = $student['student_birthday'];
        $famliy = $this->DataControl->getFieldOne("smc_student_family", "parenter_id", "student_id = '{$protocol['student_id']}' and family_isdefault = '1'");
        $parenter = $this->DataControl->selectOne("
            SELECT
                p.parenter_id,
                p.parenter_mobile,
                p.parenter_cnname,
                p.parenter_sign,
                f.family_relation,
                f.family_cnname,
                f.family_address 
            FROM
                smc_parenter AS p
                LEFT JOIN smc_student_family AS f ON p.parenter_id = f.parenter_id
                where p.parenter_id = '{$famliy['parenter_id']}' and f.student_id = '{$protocol['student_id']}'");
        $PartyB['phone'] = $parenter['parenter_mobile'];
        $PartyB['schoolname'] = '--';
        $PartyB['address'] = '--';
        $PartyB['student_idcard'] = $student['student_idcard'];

        $guarder = array();
        $guarder['guardername'] = $parenter['family_cnname'];
        $guarder['parenter_id'] = $parenter['parenter_id'];
        $guarder['guarderphone'] = $parenter['parenter_mobile'];
        $guarder['guarderrelation'] = $parenter['family_relation'];
        $guarder['parenter_sign'] = $protocol['protocol_sign'];

        $famliys = $this->DataControl->getFieldOne("smc_student_family", "parenter_id", "student_id = '{$protocol['student_id']}' and family_isdefault = '0'");

        if ($famliys) {
            $parenters = $this->DataControl->selectOne("
            SELECT
                p.parenter_mobile,
                p.parenter_cnname,
                p.parenter_sign,
                f.family_relation,
                f.family_cnname,
                f.family_address 
            FROM
                smc_parenter AS p
                LEFT JOIN smc_student_family AS f ON p.parenter_id = f.parenter_id
                where p.parenter_id = '{$famliys['parenter_id']}' and f.student_id = '{$protocol['student_id']}'");
            $guarder['urgentname'] = $parenters['family_cnname'];
            $guarder['urgentphone'] = $parenters['parenter_mobile'];
            $guarder['urgentrelation'] = $parenters['family_relation'];
        } else {
            $guarder['urgentname'] = $parenter['family_cnname'];
            $guarder['urgentphone'] = $parenter['parenter_mobile'];
            $guarder['urgentrelation'] = $parenter['family_relation'];
        }

//        $courseInfo = array();
        $pricing_id = $this->DataControl->selectOne("select pricing_id from smc_payfee_order_course as c where c.order_pid = '{$protocol['order_pid']}' and c.course_id='{$protocol['course_id']}'");
        $agreement = $this->DataControl->getFieldOne("smc_fee_pricing", "agreement_id", "pricing_id = '{$pricing_id['pricing_id']}'");

//        if ($protocol['protocol_isaudit'] == '1') {
//            $protocolOne = $this->DataControl->getFieldOne("smc_student_protocol", "treaty_tabletip,treaty_protocol", "protocol_id = '{$protocol['protocol_id']}'");
//        } else {
//            if($course['coursecat_id'] == '135'){
//                $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '731' and coursetype_id = '79654' and t.treaty_applytype = '0'");
//            }else{
//                $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and t.treaty_applytype = '1' and a.school_id = '{$request['school_id']}'");
//                if(!$protocolOne){
//                    $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and t.treaty_applytype = '0'");
//                }
//            }
//        }

        if ($protocol['protocol_isaudit'] == '1') {
            $protocolOne = $this->DataControl->getFieldOne("smc_student_protocol", "treaty_tabletip,treaty_protocol", "protocol_id = '{$protocol['protocol_id']}'");

            if (!$protocolOne['treaty_protocol']) {
                $isprocat = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and coursecat_id = '{$course['coursecat_id']}'");
                if ($isprocat) {
                    $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and coursecat_id = '{$course['coursecat_id']}' and t.treaty_applytype = '1' and a.school_id = '{$protocol['school_id']}'");
                    if (!$protocolOne) {
                        $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and coursecat_id = '{$course['coursecat_id']}' and t.treaty_applytype = '0'");
                        if (!$protocolOne) {

                            $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and coursecat_id = '0' and t.treaty_applytype = '0'");
                            if (!$protocolOne) {
                                $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and coursecat_id = '0' and t.treaty_applytype = '1'  and a.school_id = '{$protocol['school_id']}'");
                            }
                        }
                    }
                } else {
                    $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and t.treaty_applytype = '1' and a.school_id = '{$protocol['school_id']}' and t.coursecat_id = '0'");
                    if (!$protocolOne) {
                        $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and t.treaty_applytype = '0' and t.coursecat_id = '0'");
                    }
                }
            }


        } else {
            $isprocat = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and coursecat_id = '{$course['coursecat_id']}'");
            if ($isprocat) {
                $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and coursecat_id = '{$course['coursecat_id']}' and t.treaty_applytype = '1' and a.school_id = '{$protocol['school_id']}'");
                if (!$protocolOne) {
                    $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and coursecat_id = '{$course['coursecat_id']}' and t.treaty_applytype = '0'");
                    if (!$protocolOne) {

                        $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and coursecat_id = '0' and t.treaty_applytype = '0'");
                        if (!$protocolOne) {
                            $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and coursecat_id = '0' and t.treaty_applytype = '1'  and a.school_id = '{$protocol['school_id']}'");
                        }
                    }
                }
            } else {
                $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and t.treaty_applytype = '1' and a.school_id = '{$protocol['school_id']}' and t.coursecat_id = '0'");
                if (!$protocolOne) {
                    $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and t.treaty_applytype = '0' and t.coursecat_id = '0'");
                }
            }
        }

        $courseInfo['course_branch'] = $course['course_branch'];
        $courseInfo['course_cnname'] = $course['course_cnname'];
        $courseInfo['ordercourse_unitprice'] = $order['ordercourse_unitprice'];
        if ($order['ordercourse_buynums'] == $course['course_classnum']) {
            $courseInfo['type'] = '新班';
        } else {
            $courseInfo['type'] = '插班';
        }
        if ($course['course_inclasstype'] == '2' || $course['course_inclasstype'] == '0') {
            $courseInfo['course_classnum'] = $protocol['protocol_nums'];
        } elseif ($course['course_inclasstype'] == '1') {
            $courseInfo['course_classnum'] = '';
        } else {
            $courseInfo['course_classnum'] = $course['course_classnum'];

        }

        if ($course['course_inclasstype'] == '1') {
            $courseInfo['protocol_nums'] = '';
        } else {
            $courseInfo['protocol_nums'] = $protocol['protocol_nums'];
        }

        $courseInfo['course_classtimes'] = $course['course_perhour'] . 'h';

        $priceInfo = array();
        $priceInfo['protocol_pid'] = $protocol['protocol_pid'];
        $priceInfo['courseprice'] = '¥' . $order['ordercourse_totalprice'];
        $priceInfo['sendprice'] = '¥' . $protocol['protocol_price'];

        $track = $this->DataControl->getFieldOne("smc_payfee_order_tracks", "staffer_id", "order_pid = '{$order['order_pid']}' and tracks_title = '创建订单'");
        $agent = $this->DataControl->getFieldOne("smc_staffer", "staffer_cnname", "staffer_id = '{$track['staffer_id']}'");
        $priceInfo['adviser'] = '--';
        $priceInfo['agent'] = $agent['staffer_cnname'];
        $priceInfo['principal'] = '--';

        $priceInfo['bigprice'] = $this->convert_2_cn(intval($order['ordercourse_totalprice']));

        $startday = $this->DataControl->selectOne("SELECT
	h.hour_day 
FROM
	smc_class_hour AS h
	LEFT JOIN smc_class AS c ON c.class_id = h.class_id
	LEFT JOIN smc_student_study AS sh ON sh.class_id = h.class_id 
WHERE
	sh.student_id = '{$protocol['student_id']}' 
	AND c.course_id = '{$protocol['course_id']}'
ORDER BY h.hour_lessontimes ASC limit 0,1");

        $date = $startday['hour_day'];

        $endday = date('Y-m-d', strtotime("$date +90 day"));

//
//        $endday = $this->DataControl->selectOne("SELECT
//	h.hour_day
//FROM
//	smc_class_hour AS h
//	LEFT JOIN smc_class AS c ON c.class_id = h.class_id
//	LEFT JOIN smc_student_study AS sh ON sh.class_id = h.class_id
//WHERE
//	sh.student_id = '{$protocol['student_id']}'
//	AND c.course_id = '{$protocol['course_id']}'
//ORDER BY h.hour_lessontimes DESC limit 0,1");


        if ($protocolOne) {
            $treatyArray = array();
            $treatyArray['companies_cnname'] = $PartyA['companies_cnname'];
            $treatyArray['companies_supervisebank'] = $PartyA['companies_supervisebank'];
            $treatyArray['companies_superviseaccount'] = $PartyA['companies_superviseaccount'];
            $treatyArray['companies_settlebank'] = $PartyA['companies_settlebank'];
            $treatyArray['companies_settleaccount'] = $PartyA['companies_settleaccount'];
            $treatyArray['courseprice'] = $priceInfo['courseprice'];
            $treatyArray['bigprice'] = $priceInfo['bigprice'];
            $treatyArray['school_address'] = $PartyA['school_address'];
            $treatyArray['school_examine'] = $PartyA['school_examine'];
            $treatyArray['school_register'] = $PartyA['school_register'];
            $treatyArray['school_permitbranch'] = $PartyA['school_permitbranch'];
            $treatyArray['school_permitstday'] = $PartyA['school_permitstday'];
            $treatyArray['school_icp'] = $PartyA['school_icp'];
            $treatyArray['school_licensestday'] = $PartyA['school_licensestday'];
            $treatyArray['school_society'] = $PartyA['school_society'];
            $treatyArray['school_liaison'] = $PartyA['school_liaison'];
            $treatyArray['school_phone'] = $PartyA['school_phone'];
            $treatyArray['student_branch'] = $PartyB['student_branch'];
            $treatyArray['student_cnname'] = $PartyB['student_cnname'];
            $treatyArray['student_sex'] = $PartyB['student_sex'];
            $treatyArray['student_birthday'] = $PartyB['student_birthday'];
            $treatyArray['phone'] = $PartyB['phone'];
            $treatyArray['schoolname'] = $PartyB['schoolname'];
            $treatyArray['address'] = $PartyB['address'];
            $treatyArray['student_idcard'] = $PartyB['student_idcard'];
            $treatyArray['guardername'] = $guarder['guardername'];
            $treatyArray['guarderphone'] = $guarder['guarderphone'];
            $treatyArray['guarderrelation'] = $guarder['guarderrelation'];
            $treatyArray['urgentname'] = $guarder['urgentname'];
            $treatyArray['urgentphone'] = $guarder['urgentphone'];
            $treatyArray['urgentrelation'] = $guarder['urgentrelation'];
            $treatyArray['course_branch'] = $courseInfo['course_branch'];
            $treatyArray['course_cnname'] = $courseInfo['course_cnname'];
            $treatyArray['type'] = $courseInfo['type'];
            $treatyArray['protocol_nums'] = $courseInfo['protocol_nums'];
            $treatyArray['protocol_pid'] = $priceInfo['protocol_pid'];
            $treatyArray['sendprice'] = $priceInfo['sendprice'];
            $treatyArray['agent'] = $priceInfo['agent'];
            $protocolOne['treaty_tabletip'] = $this->contractTable($protocolOne['treaty_tabletip'], $treatyArray);
        }

        if ($protocolOne) {
            $treatyArray = array();
            $treatyArray['companies_cnname'] = $PartyA['companies_cnname'];
            $treatyArray['startday'] = $startday['hour_day'];
            $treatyArray['endday'] = $endday;
            $treatyArray['companies_permitstday'] = $PartyA['companies_permitstday'];
            $treatyArray['companies_licensestday'] = $PartyA['companies_licensestday'];
            $treatyArray['course_classnum'] = $courseInfo['course_classnum'];
            $treatyArray['course_classtimes'] = $courseInfo['course_classtimes'];
            $treatyArray['ordercourse_unitprice'] = $courseInfo['ordercourse_unitprice'];
            $treatyArray['companies_supervisebank'] = $PartyA['companies_supervisebank'];
            $treatyArray['companies_superviseaccount'] = $PartyA['companies_superviseaccount'];
            $treatyArray['companies_settlebank'] = $PartyA['companies_settlebank'];
            $treatyArray['companies_settleaccount'] = $PartyA['companies_settleaccount'];
            $treatyArray['courseprice'] = $priceInfo['courseprice'];
            $treatyArray['bigprice'] = $priceInfo['bigprice'];
            $treatyArray['school_address'] = $PartyA['school_address'];
            $treatyArray['school_examine'] = $PartyA['school_examine'];
            $treatyArray['school_register'] = $PartyA['school_register'];
            $treatyArray['school_permitbranch'] = $PartyA['school_permitbranch'];
            $treatyArray['school_permitstday'] = $PartyA['school_permitstday'];
            $treatyArray['school_icp'] = $PartyA['school_icp'];
            $treatyArray['school_licensestday'] = $PartyA['school_licensestday'];
            $treatyArray['school_society'] = $PartyA['school_society'];
            $treatyArray['school_liaison'] = $PartyA['school_liaison'];
            $treatyArray['school_phone'] = $PartyA['school_phone'];
            $treatyArray['student_branch'] = $PartyB['student_branch'];
            $treatyArray['student_cnname'] = $PartyB['student_cnname'];
            $treatyArray['student_sex'] = $PartyB['student_sex'];
            $treatyArray['student_birthday'] = $PartyB['student_birthday'];
            $treatyArray['phone'] = $PartyB['phone'];
            $treatyArray['schoolname'] = $PartyB['schoolname'];
            $treatyArray['address'] = $PartyB['address'];
            $treatyArray['student_idcard'] = $PartyB['student_idcard'];
            $treatyArray['guardername'] = $guarder['guardername'];
            $treatyArray['guarderphone'] = $guarder['guarderphone'];
            $treatyArray['guarderrelation'] = $guarder['guarderrelation'];
            $treatyArray['urgentname'] = $guarder['urgentname'];
            $treatyArray['urgentphone'] = $guarder['urgentphone'];
            $treatyArray['urgentrelation'] = $guarder['urgentrelation'];
            $treatyArray['course_branch'] = $courseInfo['course_branch'];
            $treatyArray['course_cnname'] = $courseInfo['course_cnname'];
            $treatyArray['type'] = $courseInfo['type'];
            $treatyArray['protocol_nums'] = $courseInfo['protocol_nums'];
            $treatyArray['protocol_pid'] = $priceInfo['protocol_pid'];
            $treatyArray['sendprice'] = $priceInfo['sendprice'];
            $treatyArray['agent'] = $priceInfo['agent'];
            $protocolOne['treaty_protocol'] = $this->contractTable($protocolOne['treaty_protocol'], $treatyArray);
        }

        $result = array();
        $result['istable'] = $protocol['protocol_istable'];
        $result['PartyA'] = $PartyA;
        $result['student_id'] = $protocol['student_id'];
        $result['PartyB'] = $PartyB;
        $result['guarder'] = $guarder;
        $result['courseInfo'] = $courseInfo;
        $result['priceInfo'] = $priceInfo;
        $result['order'] = $order;
        $result['date'] = date('Y-m-d', $protocol['protocol_createtime']);
        $result['text'] = $protocolOne['treaty_protocol'];
        $result['tip'] = $protocolOne['treaty_tabletip'];
        $result['signtime'] = substr($protocol['protocol_signtime'], 0, 10);

        ajax_return(array('error' => 0, 'errortip' => "获取成功", 'result' => $result));

    }


    //监管协议详情
    function superviseDetailView()
    {
        $request = Input('get.', '', 'trim,addslashes,strip_tags');
        $this->ThisVerify($request);//验证账户

        //甲方
        $PartyA = array();
        $protocol = $this->DataControl->getOne("smc_student_protocol", "protocol_id = '{$request['protocol_id']}'");
        $order = $this->DataControl->selectOne("select o.order_pid,o.trading_pid,o.companies_id,p.pay_pid,p.pay_typename,p.pay_price,o.order_coupon_price,o.order_paidprice,o.order_arrearageprice,o.order_paymentprice,c.ordercourse_buynums,c.ordercourse_totalprice,c.ordercourse_unitprice from smc_payfee_order as o left join smc_payfee_order_pay as p on o.order_pid = p.order_pid left join smc_payfee_order_course as c on c.order_pid = o.order_pid where o.order_pid = '{$protocol['order_pid']}'");

        $company = $this->DataControl->getFieldOne("gmc_company", "company_cnname,company_logo", "company_id = '{$request['company_id']}'");
        $school = $this->DataControl->getFieldOne("smc_school", "school_cnname,school_signet,school_address,school_phone,companies_id,school_liaison,school_examine,school_register,school_permitbranch,school_permitstday,school_permitendday,school_icp,school_licensestday,school_licenseendday,school_society,school_licensestatus", "school_id = '{$protocol['school_id']}'");
        $course = $this->DataControl->getFieldOne("smc_course", "course_cnname,course_branch,course_classnum,course_classtimes,coursetype_id,course_inclasstype,course_perhour,coursecat_id", "course_id = '{$protocol['course_id']}'");
        $companies_id = $this->DataControl->getFieldOne("smc_school_coursecat_subject", "companies_id", "school_id = '{$protocol['school_id']}' and coursecat_id = '{$course['coursecat_id']}'");
        $companies = $this->DataControl->getFieldOne("gmc_code_companies", "companies_cnname,companies_signet,companies_liaison,companies_examine,companies_register,companies_permitbranch,companies_permitstday,companies_permitendday,companies_icp,companies_licensestday,companies_licenseendday,companies_society,companies_licensestatus,companies_supervisebank,companies_superviseaccount,companies_settlebank,companies_settleaccount,companies_permitstday,companies_licensestday", "companies_id = '{$companies_id['companies_id']}'");

        $PartyA['companies_cnname'] = $companies['companies_cnname'];
        $PartyA['companies_permitstday'] = substr($companies['companies_permitstday'], strripos($companies['companies_permitstday'], "至") + 3);
        $PartyA['companies_licensestday'] = substr($companies['companies_licensestday'], strripos($companies['companies_licensestday'], "至") + 3);
        $PartyA['companies_supervisebank'] = $companies['companies_supervisebank'];
        $PartyA['companies_superviseaccount'] = $companies['companies_superviseaccount'];
        $PartyA['companies_settlebank'] = $companies['companies_settlebank'];
        $PartyA['companies_settleaccount'] = $companies['companies_settleaccount'];
        $PartyA['school_cnname'] = $school['school_cnname'];
        $PartyA['company_cnname'] = $company['company_cnname'];
        $PartyA['school_address'] = $school['school_address'];
        $PartyA['school_phone'] = $school['school_phone'];
        if ($school['school_phone']) {
            $PartyA['school_phone'] = $school['school_phone'];
        } else {
            $PartyA['school_phone'] = '--';
        }
        if ($companies['companies_liaison']) {
            $PartyA['school_liaison'] = $companies['companies_liaison'];
        } else {
            $PartyA['school_liaison'] = '--';
        }
        if ($companies['companies_examine']) {
            $PartyA['school_examine'] = $companies['companies_examine'];
        } else {
            $PartyA['school_examine'] = '--';
        }
        if ($companies['companies_register']) {
            $PartyA['school_register'] = $companies['companies_register'];
        } else {
            $PartyA['school_register'] = '--';
        }
        if ($companies['companies_permitbranch']) {
            $PartyA['school_permitbranch'] = $companies['companies_permitbranch'];
        } else {
            $PartyA['school_permitbranch'] = '--';
        }
        if ($companies['companies_permitstday']) {
            $PartyA['school_permitstday'] = $companies['companies_permitstday'];
        } else {
            $PartyA['school_permitstday'] = '--';
        }
        if ($companies['companies_permitstday']) {
            $PartyA['school_permitstday'] = $companies['companies_permitstday'];
        } else {
            $PartyA['school_permitstday'] = '--';
        }
        if ($companies['companies_licensestday']) {
            $PartyA['school_licensestday'] = $companies['companies_licensestday'];
        } else {
            $PartyA['school_licensestday'] = '--';
        }
        if ($companies['companies_society']) {
            $PartyA['school_society'] = $companies['companies_society'];
        } else {
            $PartyA['school_society'] = '--';
        }
        $PartyA['school_signet'] = $companies['companies_signet'];
        $PartyA['school_icp'] = $companies['companies_icp'];
        $PartyA['company_logo'] = $company['company_logo'];
        $PartyA['company_shortname'] = $school['company_shortname'];

        $PartyB = array();
        $student = $this->DataControl->getFieldOne("smc_student", "student_cnname,student_enname,student_birthday,student_sex,student_branch,student_idcard", "student_id = '{$protocol['student_id']}'");
        $PartyB['student_branch'] = $student['student_branch'];
        $PartyB['student_cnname'] = $student['student_cnname'];
        $PartyB['student_sex'] = $student['student_sex'];
        $PartyB['student_birthday'] = $student['student_birthday'];
        $famliy = $this->DataControl->getFieldOne("smc_student_family", "parenter_id", "student_id = '{$protocol['student_id']}' and family_isdefault = '1'");
        $parenter = $this->DataControl->selectOne("
            SELECT
                p.parenter_id,
                p.parenter_mobile,
                p.parenter_cnname,
                p.parenter_sign,
                f.family_relation,
                f.family_cnname,
                f.family_address 
            FROM
                smc_parenter AS p
                LEFT JOIN smc_student_family AS f ON p.parenter_id = f.parenter_id
                where p.parenter_id = '{$famliy['parenter_id']}' and f.student_id = '{$protocol['student_id']}'");
        $PartyB['phone'] = $parenter['parenter_mobile'];
        $PartyB['schoolname'] = '--';
        $PartyB['address'] = '--';
        $PartyB['student_idcard'] = $student['student_idcard'];

        $guarder = array();
        $guarder['guardername'] = $parenter['family_cnname'];
        $guarder['parenter_id'] = $parenter['parenter_id'];
        $guarder['guarderphone'] = $parenter['parenter_mobile'];
        $guarder['guarderrelation'] = $parenter['family_relation'];
        $guarder['parenter_sign'] = $protocol['protocol_sign'];

        $famliys = $this->DataControl->getFieldOne("smc_student_family", "parenter_id", "student_id = '{$protocol['student_id']}' and family_isdefault = '0'");

        if ($famliys) {
            $parenters = $this->DataControl->selectOne("
            SELECT
                p.parenter_mobile,
                p.parenter_cnname,
                p.parenter_sign,
                f.family_relation,
                f.family_cnname,
                f.family_address 
            FROM
                smc_parenter AS p
                LEFT JOIN smc_student_family AS f ON p.parenter_id = f.parenter_id
                where p.parenter_id = '{$famliys['parenter_id']}' and f.student_id = '{$protocol['student_id']}'");
            $guarder['urgentname'] = $parenters['family_cnname'];
            $guarder['urgentphone'] = $parenters['parenter_mobile'];
            $guarder['urgentrelation'] = $parenters['family_relation'];
        } else {
            $guarder['urgentname'] = $parenter['family_cnname'];
            $guarder['urgentphone'] = $parenter['parenter_mobile'];
            $guarder['urgentrelation'] = $parenter['family_relation'];
        }

//        $courseInfo = array();
        $pricing_id = $this->DataControl->selectOne("select pricing_id from smc_payfee_order_course as c where c.order_pid = '{$protocol['order_pid']}'");
        $agreement = $this->DataControl->getFieldOne("smc_fee_pricing", "agreement_id", "pricing_id = '{$pricing_id['pricing_id']}'");

//        if ($protocol['protocol_isaudit'] == '1') {
//            $protocolOne = $this->DataControl->getFieldOne("smc_student_protocol", "treaty_tabletip,treaty_protocol", "protocol_id = '{$protocol['protocol_id']}'");
//        } else {
//            if($course['coursecat_id'] == '135'){
//                $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '731' and coursetype_id = '79654' and t.treaty_applytype = '0'");
//            }else{
//                $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and t.treaty_applytype = '1' and a.school_id = '{$request['school_id']}'");
//                if(!$protocolOne){
//                    $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and t.treaty_applytype = '0'");
//                }
//            }
//        }

        if ($protocol['protocol_isaudit'] == '1') {
            $protocolOne = $this->DataControl->getFieldOne("smc_student_protocol", "treaty_tabletip,treaty_protocol", "protocol_id = '{$protocol['protocol_id']}'");

            if (!$protocolOne['treaty_protocol']) {
                $isprocat = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and coursecat_id = '{$course['coursecat_id']}'");
                if ($isprocat) {
                    $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and coursecat_id = '{$course['coursecat_id']}' and t.treaty_applytype = '1' and a.school_id = '{$protocol['school_id']}'");
                    if (!$protocolOne) {
                        $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and coursecat_id = '{$course['coursecat_id']}' and t.treaty_applytype = '0'");
                        if (!$protocolOne) {

                            $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and coursecat_id = '0' and t.treaty_applytype = '0'");
                            if (!$protocolOne) {
                                $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and coursecat_id = '0' and t.treaty_applytype = '1'  and a.school_id = '{$protocol['school_id']}'");
                            }
                        }
                    }
                } else {
                    $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and t.treaty_applytype = '1' and a.school_id = '{$protocol['school_id']}' and t.coursecat_id = '0'");
                    if (!$protocolOne) {
                        $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and t.treaty_applytype = '0' and t.coursecat_id = '0'");
                    }
                }
            }


        } else {
            $isprocat = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and coursecat_id = '{$course['coursecat_id']}'");
            if ($isprocat) {
                $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and coursecat_id = '{$course['coursecat_id']}' and t.treaty_applytype = '1' and a.school_id = '{$protocol['school_id']}'");
                if (!$protocolOne) {
                    $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and coursecat_id = '{$course['coursecat_id']}' and t.treaty_applytype = '0'");
                    if (!$protocolOne) {

                        $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and coursecat_id = '0' and t.treaty_applytype = '0'");
                        if (!$protocolOne) {
                            $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and coursecat_id = '0' and t.treaty_applytype = '1'  and a.school_id = '{$protocol['school_id']}'");
                        }
                    }
                }
            } else {
                $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and t.treaty_applytype = '1' and a.school_id = '{$protocol['school_id']}' and t.coursecat_id = '0'");
                if (!$protocolOne) {
                    $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and t.treaty_applytype = '0' and t.coursecat_id = '0'");
                }
            }
        }

        $courseInfo['course_branch'] = $course['course_branch'];
        $courseInfo['course_cnname'] = $course['course_cnname'];
        $courseInfo['ordercourse_unitprice'] = $order['ordercourse_unitprice'];
        if ($order['ordercourse_buynums'] == $course['course_classnum']) {
            $courseInfo['type'] = '新班';
        } else {
            $courseInfo['type'] = '插班';
        }
        if ($course['course_inclasstype'] == '2' || $course['course_inclasstype'] == '0') {
            $courseInfo['course_classnum'] = $protocol['protocol_nums'];
        } elseif ($course['course_inclasstype'] == '1') {
            $courseInfo['course_classnum'] = '';
        } else {
            $courseInfo['course_classnum'] = $course['course_classnum'];

        }

        if ($course['course_inclasstype'] == '1') {
            $courseInfo['protocol_nums'] = '';
        } else {
            $courseInfo['protocol_nums'] = $protocol['protocol_nums'];
        }

        $courseInfo['course_classtimes'] = $course['course_perhour'] . 'h';

        $priceInfo = array();
        $priceInfo['protocol_pid'] = $protocol['protocol_pid'];
        $priceInfo['courseprice'] = '¥' . $order['ordercourse_totalprice'];
        $priceInfo['sendprice'] = '¥' . $protocol['protocol_price'];

        $track = $this->DataControl->getFieldOne("smc_payfee_order_tracks", "staffer_id", "order_pid = '{$order['order_pid']}' and tracks_title = '创建订单'");
        $agent = $this->DataControl->getFieldOne("smc_staffer", "staffer_cnname", "staffer_id = '{$track['staffer_id']}'");
        $priceInfo['adviser'] = '--';
        $priceInfo['agent'] = $agent['staffer_cnname'];
        $priceInfo['principal'] = '--';

        $priceInfo['bigprice'] = $this->convert_2_cn(intval($order['ordercourse_totalprice']));

        $startday = $this->DataControl->selectOne("SELECT
	h.hour_day 
FROM
	smc_class_hour AS h
	LEFT JOIN smc_class AS c ON c.class_id = h.class_id
	LEFT JOIN smc_student_study AS sh ON sh.class_id = h.class_id 
WHERE
	sh.student_id = '{$protocol['student_id']}' 
	AND c.course_id = '{$protocol['course_id']}'
ORDER BY h.hour_lessontimes ASC limit 0,1");

        $date = $startday['hour_day'];

        $endday = date('Y-m-d', strtotime("$date +90 day"));

//
//        $endday = $this->DataControl->selectOne("SELECT
//	h.hour_day
//FROM
//	smc_class_hour AS h
//	LEFT JOIN smc_class AS c ON c.class_id = h.class_id
//	LEFT JOIN smc_student_study AS sh ON sh.class_id = h.class_id
//WHERE
//	sh.student_id = '{$protocol['student_id']}'
//	AND c.course_id = '{$protocol['course_id']}'
//ORDER BY h.hour_lessontimes DESC limit 0,1");


        if ($protocolOne) {
            $treatyArray = array();
            $treatyArray['companies_cnname'] = $PartyA['companies_cnname'];
            $treatyArray['companies_supervisebank'] = $PartyA['companies_supervisebank'];
            $treatyArray['companies_superviseaccount'] = $PartyA['companies_superviseaccount'];
            $treatyArray['companies_settlebank'] = $PartyA['companies_settlebank'];
            $treatyArray['companies_settleaccount'] = $PartyA['companies_settleaccount'];
            $treatyArray['courseprice'] = $priceInfo['courseprice'];
            $treatyArray['bigprice'] = $priceInfo['bigprice'];
            $treatyArray['school_address'] = $PartyA['school_address'];
            $treatyArray['school_examine'] = $PartyA['school_examine'];
            $treatyArray['school_register'] = $PartyA['school_register'];
            $treatyArray['school_permitbranch'] = $PartyA['school_permitbranch'];
            $treatyArray['school_permitstday'] = $PartyA['school_permitstday'];
            $treatyArray['school_icp'] = $PartyA['school_icp'];
            $treatyArray['school_licensestday'] = $PartyA['school_licensestday'];
            $treatyArray['school_society'] = $PartyA['school_society'];
            $treatyArray['school_liaison'] = $PartyA['school_liaison'];
            $treatyArray['school_phone'] = $PartyA['school_phone'];
            $treatyArray['student_branch'] = $PartyB['student_branch'];
            $treatyArray['student_cnname'] = $PartyB['student_cnname'];
            $treatyArray['student_sex'] = $PartyB['student_sex'];
            $treatyArray['student_birthday'] = $PartyB['student_birthday'];
            $treatyArray['phone'] = $PartyB['phone'];
            $treatyArray['schoolname'] = $PartyB['schoolname'];
            $treatyArray['address'] = $PartyB['address'];
            $treatyArray['student_idcard'] = $PartyB['student_idcard'];
            $treatyArray['guardername'] = $guarder['guardername'];
            $treatyArray['guarderphone'] = $guarder['guarderphone'];
            $treatyArray['guarderrelation'] = $guarder['guarderrelation'];
            $treatyArray['urgentname'] = $guarder['urgentname'];
            $treatyArray['urgentphone'] = $guarder['urgentphone'];
            $treatyArray['urgentrelation'] = $guarder['urgentrelation'];
            $treatyArray['course_branch'] = $courseInfo['course_branch'];
            $treatyArray['course_cnname'] = $courseInfo['course_cnname'];
            $treatyArray['type'] = $courseInfo['type'];
            $treatyArray['protocol_nums'] = $courseInfo['protocol_nums'];
            $treatyArray['protocol_pid'] = $priceInfo['protocol_pid'];
            $treatyArray['sendprice'] = $priceInfo['sendprice'];
            $treatyArray['agent'] = $priceInfo['agent'];
            $protocolOne['treaty_tabletip'] = $this->contractTable($protocolOne['treaty_tabletip'], $treatyArray);
        }

        if ($protocolOne) {
            $treatyArray = array();
            $treatyArray['companies_cnname'] = $PartyA['companies_cnname'];
            $treatyArray['startday'] = $startday['hour_day'];
            $treatyArray['endday'] = $endday;
            $treatyArray['companies_permitstday'] = $PartyA['companies_permitstday'];
            $treatyArray['companies_licensestday'] = $PartyA['companies_licensestday'];
            $treatyArray['course_classnum'] = $courseInfo['course_classnum'];
            $treatyArray['course_classtimes'] = $courseInfo['course_classtimes'];
            $treatyArray['ordercourse_unitprice'] = $courseInfo['ordercourse_unitprice'];
            $treatyArray['companies_supervisebank'] = $PartyA['companies_supervisebank'];
            $treatyArray['companies_superviseaccount'] = $PartyA['companies_superviseaccount'];
            $treatyArray['companies_settlebank'] = $PartyA['companies_settlebank'];
            $treatyArray['companies_settleaccount'] = $PartyA['companies_settleaccount'];
            $treatyArray['courseprice'] = $priceInfo['courseprice'];
            $treatyArray['bigprice'] = $priceInfo['bigprice'];
            $treatyArray['school_address'] = $PartyA['school_address'];
            $treatyArray['school_examine'] = $PartyA['school_examine'];
            $treatyArray['school_register'] = $PartyA['school_register'];
            $treatyArray['school_permitbranch'] = $PartyA['school_permitbranch'];
            $treatyArray['school_permitstday'] = $PartyA['school_permitstday'];
            $treatyArray['school_icp'] = $PartyA['school_icp'];
            $treatyArray['school_licensestday'] = $PartyA['school_licensestday'];
            $treatyArray['school_society'] = $PartyA['school_society'];
            $treatyArray['school_liaison'] = $PartyA['school_liaison'];
            $treatyArray['school_phone'] = $PartyA['school_phone'];
            $treatyArray['student_branch'] = $PartyB['student_branch'];
            $treatyArray['student_cnname'] = $PartyB['student_cnname'];
            $treatyArray['student_sex'] = $PartyB['student_sex'];
            $treatyArray['student_birthday'] = $PartyB['student_birthday'];
            $treatyArray['phone'] = $PartyB['phone'];
            $treatyArray['schoolname'] = $PartyB['schoolname'];
            $treatyArray['address'] = $PartyB['address'];
            $treatyArray['student_idcard'] = $PartyB['student_idcard'];
            $treatyArray['guardername'] = $guarder['guardername'];
            $treatyArray['guarderphone'] = $guarder['guarderphone'];
            $treatyArray['guarderrelation'] = $guarder['guarderrelation'];
            $treatyArray['urgentname'] = $guarder['urgentname'];
            $treatyArray['urgentphone'] = $guarder['urgentphone'];
            $treatyArray['urgentrelation'] = $guarder['urgentrelation'];
            $treatyArray['course_branch'] = $courseInfo['course_branch'];
            $treatyArray['course_cnname'] = $courseInfo['course_cnname'];
            $treatyArray['type'] = $courseInfo['type'];
            $treatyArray['protocol_nums'] = $courseInfo['protocol_nums'];
            $treatyArray['protocol_pid'] = $priceInfo['protocol_pid'];
            $treatyArray['sendprice'] = $priceInfo['sendprice'];
            $treatyArray['agent'] = $priceInfo['agent'];
            $protocolOne['treaty_protocol'] = $this->contractTable($protocolOne['treaty_protocol'], $treatyArray);
        }

        $supervise = $this->DataControl->getFieldOne("smc_student_protocol_supervise", "supervise_protocol", "protocol_id = '{$request['protocol_id']}'");

        $result = array();
        $result['istable'] = $protocol['protocol_istable'];
        $result['PartyA'] = $PartyA;
        $result['student_id'] = $protocol['student_id'];
        $result['PartyB'] = $PartyB;
        $result['guarder'] = $guarder;
        $result['courseInfo'] = $courseInfo;
        $result['priceInfo'] = $priceInfo;
        $result['order'] = $order;
        $result['date'] = date('Y-m-d', $protocol['protocol_createtime']);
        $result['text'] = $supervise['supervise_protocol'];
        $result['signtime'] = substr($protocol['protocol_signtime'], 0, 10);

        ajax_return(array('error' => 0, 'errortip' => "获取成功", 'result' => $result));

    }


    function contractTable($tabletip, $treatyArray)
    {
        $tableNote = $tabletip;
        foreach ($treatyArray as $key => $treatyOne) {

            $tableNote = str_replace("#" . $key . "#", $treatyOne, $tableNote);
        }
        return $tableNote;
    }

//    function contractTable($tabletip,$treatyArray){
//        $tableNote = $tabletip;
//        foreach($treatyArray as $key=>$treatyOne){
//
//            $tableNote = str_replace("#".$key."#",$treatyOne,$tableNote);
//        }
//        return $tableNote;
//    }
    function convert_2_cn($num)
    {
        $convert_cn = array("零", "壹", "贰", "叁", "肆", "伍", "陆", "柒", "捌", "玖");
        $repair_number = array('零仟零佰零拾零', '万万', '零仟', '零佰', '零拾');
        $unit_cn = array("拾", "佰", "仟", "万", "亿");
        $exp_cn = array("", "万", "亿");
        $max_len = 12;
        $len = strlen($num);
        if ($len > $max_len) {
            return 'outnumber';
        }
        $num = str_pad($num, 12, '-', STR_PAD_LEFT);
        $exp_num = array();
        $k = 0;
        for ($i = 12; $i > 0; $i--) {
            if ($i % 4 == 0) {
                $k++;
            }
            $exp_num[$k][] = substr($num, $i - 1, 1);
        }
        $str = '';
        foreach ($exp_num as $key => $nums) {
            if (array_sum($nums)) {
                $str = array_shift($exp_cn) . $str;
            }
            foreach ($nums as $nk => $nv) {
                if ($nv == '-') {
                    continue;
                }
                if ($nk == 0) {
                    $str = $convert_cn[$nv] . $str;
                } else {
                    $str = $convert_cn[$nv] . $unit_cn[$nk - 1] . $str;
                }
            }
        }
        $str = str_replace($repair_number, array('万', '亿', '-'), $str);
        $str = preg_replace("/-{2,}/", "", $str);
        $str = str_replace(array('零', '-'), array('', '零'), $str);
        return $str;
    }


    function getOrderListApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $OrderModel = new \Model\Gmc\OrderModel($request);
        $res = $OrderModel->getOrderList($request);

        $result = array();

        $result["field"] = $res['field'];
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["list"] = $res['list'];
            $result["fieldcustom"] = 0;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => '暂无订单信息', 'result' => $result);

        }
        ajax_return($res, $request['language_type']);
    }


    function getSanOrderListApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $OrderModel = new \Model\Gmc\OrderModel($request);
        $res = $OrderModel->getSanOrderList($request);

        $result = array();

        $result["field"] = $res['field'];
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["list"] = $res['list'];
            $result["fieldcustom"] = 0;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => '暂无订单信息', 'result' => $result);

        }
        ajax_return($res, $request['language_type']);
    }


    function changeRegisterStatusAction()
    {

        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Gmc\OrderModel($request);
        $res = $Model->changeRegisterStatus($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => $Model->oktip, 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }


    function orderStatusListApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $OrderModel = new \Model\Gmc\OrderModel($request);
        $res = $OrderModel->orderStatusList($request);

        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $res = array('error' => 1, 'errortip' => $OrderModel->errortip, 'result' => array());
        }
        ajax_return($res, $request['language_type']);
    }

    function reduceOrderListApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $OrderModel = new \Model\Gmc\OrderModel($request);
        $res = $OrderModel->reduceOrderList($request);
        $field = array();
        $k = 0;
        $field[$k]["fieldname"] = "reduceorder_pid";
        $field[$k]["fieldstring"] = "订单编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "school_cnname";
        $field[$k]["fieldstring"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "school_branch";
        $field[$k]["fieldstring"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "course_cnname";
        $field[$k]["fieldstring"] = "课程别名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "course_branch";
        $field[$k]["fieldstring"] = "课程别编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "student_cnname";
        $field[$k]["fieldstring"] = "学员名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "student_branch";
        $field[$k]["fieldstring"] = "学员编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "reduceorder_time";
        $field[$k]["fieldstring"] = "减少课程次数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "reduceorder_figure";
        $field[$k]["fieldstring"] = "减少课程金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "reduceorder_status_name";
        $field[$k]["fieldstring"] = "订单状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "reduceorder_note";
        $field[$k]["fieldstring"] = "备注";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "reduceorder_createtime";
        $field[$k]["fieldstring"] = "申请时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "reduceorder_updatatime";
        $field[$k]["fieldstring"] = "审核时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();

        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["list"] = $res['list'];
            $result["school"] = $res['school'];
            $result["fieldcustom"] = 0;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["school"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => '暂无订单信息', 'result' => $result);

        }
        ajax_return($res, $request['language_type']);

    }

    function refundOrderListApi()
    {
        error_reporting(E_ALL);
        ini_set('display_errors', '1');
        ini_set("memory_limit", '-1');
        set_time_limit(600);
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $OrderModel = new \Model\Gmc\OrderModel($request);
        $res = $OrderModel->refundOrderList($request);
        $field = array();
        $field[0]["fieldname"] = "refund_pid";
        $field[0]["fieldstring"] = "退款订单编号";
        $field[0]["show"] = 1;
        $field[0]["custom"] = 1;

        $field[1]["fieldname"] = "school_cnname";
        $field[1]["fieldstring"] = "校区名称";
        $field[1]["show"] = 1;
        $field[1]["custom"] = 1;

        $field[2]["fieldname"] = "school_branch";
        $field[2]["fieldstring"] = "校区编号";
        $field[2]["show"] = 1;
        $field[2]["custom"] = 1;

        $field[3]["fieldname"] = "student_cnname";
        $field[3]["fieldstring"] = "学员名称";
        $field[3]["show"] = 1;
        $field[3]["custom"] = 1;

        $field[4]["fieldname"] = "student_branch";
        $field[4]["fieldstring"] = "学员编号";
        $field[4]["show"] = 1;
        $field[4]["custom"] = 1;

        $field[5]["fieldname"] = "refund_isspecial_name";
        $field[5]["fieldstring"] = "特殊申请";
        $field[5]["show"] = 1;
        $field[5]["custom"] = 1;

        $field[6]["fieldname"] = "refund_name";
        $field[6]["fieldstring"] = "退款人联系人";
        $field[6]["show"] = 1;
        $field[6]["custom"] = 1;

        $field[7]["fieldname"] = "refund_mobile";
        $field[7]["fieldstring"] = "退款人联系方式";
        $field[7]["show"] = 1;
        $field[7]["custom"] = 1;

        $field[8]["fieldname"] = "refund_bank";
        $field[8]["fieldstring"] = "开户行";
        $field[8]["show"] = 1;
        $field[8]["custom"] = 1;

        $field[9]["fieldname"] = "refund_accountname";
        $field[9]["fieldstring"] = "开户名";
        $field[9]["show"] = 1;
        $field[9]["custom"] = 1;

        $field[10]["fieldname"] = "refund_bankcard";
        $field[10]["fieldstring"] = "银行卡号";
        $field[10]["show"] = 1;
        $field[10]["custom"] = 1;

        $field[11]["fieldname"] = "refund_reason";
        $field[11]["fieldstring"] = "退款原因";
        $field[11]["show"] = 1;
        $field[11]["custom"] = 1;

        $field[12]["fieldname"] = "refund_payprice";
        $field[12]["fieldstring"] = "实际退款金额";
        $field[12]["show"] = 1;
        $field[12]["custom"] = 1;

        $field[13]["fieldname"] = "refund_specialprice";
        $field[13]["fieldstring"] = "特殊退款金额";
        $field[13]["show"] = 1;
        $field[13]["custom"] = 1;

        $field[14]["fieldname"] = "refund_tradeclass_name";
        $field[14]["fieldstring"] = "退费订单类型";
        $field[14]["show"] = 1;
        $field[14]["custom"] = 1;

        $field[15]["fieldname"] = "refund_status_name";
        $field[15]["fieldstring"] = "状态";
        $field[15]["show"] = 1;
        $field[15]["custom"] = 1;

        $field[16]["fieldname"] = "refund_createtime";
        $field[16]["fieldstring"] = "申请时间";
        $field[16]["show"] = 1;
        $field[16]["custom"] = 1;

        $field[17]["fieldname"] = "refund_updatatime";
        $field[17]["fieldstring"] = "审核时间";
        $field[17]["show"] = 1;
        $field[17]["custom"] = 1;

        $result = array();

        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["list"] = $res['list'];
            $result["school"] = $res['school'];
            $result["fieldcustom"] = 0;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["school"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => '暂无订单信息', 'result' => $result);

        }
        ajax_return($res, $request['language_type']);
    }

    function getPerformanceListApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Gmc\OrderModel($request);
        $res = $Model->getPerformanceList($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }


    function getCompanyachieveListApi()
    {
        $request = Input('get.', '', 'trim,addslashes');

        $this->ThisVerify($request);
        $Model = new \Model\Gmc\OrderModel($request);
        $res = $Model->getCompanyachieveList($request);
        $field = array();
        $k = 0;

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "staffer_cnname";
        $field[$k]["fieldname"] = "业绩教师";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "performance_name";
        $field[$k]["fieldname"] = "业绩明细";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "achieve_type_name";
        $field[$k]["fieldname"] = "业绩类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "coursetype_cnname";
        $field[$k]["fieldname"] = "所属班组";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "trading_pid";
        $field[$k]["fieldname"] = "交易编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "achieve_price";
        $field[$k]["fieldname"] = "业绩金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["fieldname"] = "学员姓名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "student_branch";
        $field[$k]["fieldname"] = "学员编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "achieve_note";
        $field[$k]["fieldname"] = "备注";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "order_status_name";
        $field[$k]["fieldname"] = "订单状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "achieve_status_name";
        $field[$k]["fieldname"] = "审核状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "achieve_iscalculated_name";
        $field[$k]["fieldname"] = "是否计业绩";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $result = array();
        $result["field"] = $field;
        if ($res) {
            $result["list"] = $res;
            $result["allnum"] = $res['allnum'];
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    function getachieveOneApi()
    {
        $request = Input('get.', '', 'trim,addslashes');

        $this->ThisVerify($request);
        $Model = new \Model\Gmc\OrderModel($request);
        $res = $Model->getachieveOne($request);
        $field = array();
        $k = 0;

        $field[$k]["fieldstring"] = "staffer_cnname";
        $field[$k]["fieldname"] = "教师姓名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "staffer_branch";
        $field[$k]["fieldname"] = "教师编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["fieldname"] = "学员姓名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "student_branch";
        $field[$k]["fieldname"] = "学员编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "achieve_number";
        $field[$k]["fieldname"] = "计业绩人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $result = array();
        $result["field"] = $field;
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function getOrderachieveListApi()
    {
        $request = Input('get.', '', 'trim,addslashes');

        $this->ThisVerify($request);
        $Model = new \Model\Gmc\OrderModel($request);
        $res = $Model->getOrderachieveList($request);
        $field = array();
        $k = 0;

        $field[$k]["fieldstring"] = "staffer_cnname";
        $field[$k]["fieldname"] = "教师姓名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "staffer_branch";
        $field[$k]["fieldname"] = "教师编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "performance_name";
        $field[$k]["fieldname"] = "业绩明细";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "achieve_number";
        $field[$k]["fieldname"] = "计业绩人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "achieve_price";
        $field[$k]["fieldname"] = "业绩金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "achieve_iscalculated_name";
        $field[$k]["fieldname"] = "是否计业绩";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "achieve_note";
        $field[$k]["fieldname"] = "备注";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "achieve_status_name";
        $field[$k]["fieldname"] = "审核状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $result = array();
        $result["field"] = $field;
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    function handleachieveAction()
    {

        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Gmc\OrderModel($request);
        $res = $Model->handleachieve($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => $Model->oktip, 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }


    function hourFreeOrderListApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $OrderModel = new \Model\Gmc\OrderModel($request);
        $res = $OrderModel->hourFreeOrderList($request);
        $field = array();
        $field[0]["fieldname"] = "order_pid";
        $field[0]["fieldstring"] = "课程赠送订单编号";
        $field[0]["show"] = 1;
        $field[0]["custom"] = 1;

        $field[1]["fieldname"] = "school_cnname";
        $field[1]["fieldstring"] = "校区名称";
        $field[1]["show"] = 1;
        $field[1]["custom"] = 1;

        $field[2]["fieldname"] = "school_branch";
        $field[2]["fieldstring"] = "校区编号";
        $field[2]["show"] = 1;
        $field[2]["custom"] = 1;

        $field[3]["fieldname"] = "student_cnname";
        $field[3]["fieldstring"] = "学员名称";
        $field[3]["show"] = 1;
        $field[3]["custom"] = 1;

        $field[4]["fieldname"] = "student_branch";
        $field[4]["fieldstring"] = "学员编号";
        $field[4]["show"] = 1;
        $field[4]["custom"] = 1;

        $field[5]["fieldname"] = "course_cnname";
        $field[5]["fieldstring"] = "课程别名称";
        $field[5]["show"] = 1;
        $field[5]["custom"] = 1;

        $field[6]["fieldname"] = "course_branch";
        $field[6]["fieldstring"] = "课程别编号";
        $field[6]["show"] = 1;
        $field[6]["custom"] = 1;

        $field[7]["fieldname"] = "order_alltimes";
        $field[7]["fieldstring"] = "赠送课次数";
        $field[7]["show"] = 1;
        $field[7]["custom"] = 1;

        $field[8]["fieldname"] = "order_status_name";
        $field[8]["fieldstring"] = "订单状态";
        $field[8]["show"] = 1;
        $field[8]["custom"] = 1;

        $field[9]["fieldname"] = "order_createtime";
        $field[9]["fieldstring"] = "申请时间";
        $field[9]["show"] = 1;
        $field[9]["custom"] = 1;

        $field[10]["fieldname"] = "order_updatatime";
        $field[10]["fieldstring"] = "审核时间";
        $field[10]["show"] = 1;
        $field[10]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["list"] = $res['list'];
            $result["school"] = $res['school'];
            $result["fieldcustom"] = 0;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["school"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => '暂无订单信息', 'result' => $result);

        }
        ajax_return($res, $request['language_type']);
    }

    function dealOrderListApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $OrderModel = new \Model\Gmc\OrderModel($request);
        $res = $OrderModel->dealOrder($request);
        $field = array();
        $k = 0;
        $field[$k]["fieldname"] = "dealorder_pid";
        $field[$k]["fieldstring"] = "结转订单编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "school_cnname";
        $field[$k]["fieldstring"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "student_cnname";
        $field[$k]["fieldstring"] = "学员中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "student_enname";
        $field[$k]["fieldstring"] = "学员英文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "student_branch";
        $field[$k]["fieldstring"] = "学员编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "course_cnname";
        $field[$k]["fieldstring"] = "课程名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "course_branch";
        $field[$k]["fieldstring"] = "课程编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "dealcourse_figure";
        $field[$k]["fieldstring"] = "结转金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "dealcourse_time";
        $field[$k]["fieldstring"] = "结转课次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "dealcourse_fromforwardprice";
        $field[$k]["fieldstring"] = "结转结转金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "dealorder_status_name";
        $field[$k]["fieldstring"] = "订单状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "dealorder_createtime";
        $field[$k]["fieldstring"] = "申请时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "dealorder_updatatime";
        $field[$k]["fieldstring"] = "审核时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["list"] = $res['list'];
            $result["school"] = $res['school'];
            $result["fieldcustom"] = 0;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["school"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => '暂无订单信息', 'result' => $result);

        }
        ajax_return($res, $request['language_type']);
    }


    //学费减免订单审核
    function FeeWaiverOrderPayApi()
    {
        error_reporting(E_ALL);
        ini_set('display_errors', '1');
        ini_set("memory_limit", '-1');
        set_time_limit(600);
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $OrderModel = new \Model\Gmc\OrderModel($request);
        $res = $OrderModel->FeeWaiverOrderPay($request);
        $field = array();
        $k = 0;
        $field[$k]["fieldname"] = "school_cnname";
        $field[$k]["fieldstring"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "school_branch";
        $field[$k]["fieldstring"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "order_pid";
        $field[$k]["fieldstring"] = "订单编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "pay_pid";
        $field[$k]["fieldstring"] = "减免编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "student_cnname";
        $field[$k]["fieldstring"] = "学员中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "student_enname";
        $field[$k]["fieldstring"] = "学员英文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "student_branch";
        $field[$k]["fieldstring"] = "学员编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "pay_price";
        $field[$k]["fieldstring"] = "减免金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "pay_note";
        $field[$k]["fieldstring"] = "减免原因";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "pay_issuccess_name";
        $field[$k]["fieldstring"] = "订单状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "pay_createtime";
        $field[$k]["fieldstring"] = "申请时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "pay_updatatime";
        $field[$k]["fieldstring"] = "审核时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["list"] = $res['list'];
            $result["school"] = $res['school'];
            $result["fieldcustom"] = 0;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["school"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => '暂无订单信息', 'result' => $result);

        }
        ajax_return($res, $request['language_type']);
    }

    //学费减免审核
    function ChangeFeeWaiverOrderAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $OrderModel = new \Model\Gmc\OrderModel($request);
        $OrderModel->ChangeFeeWaiverOrderAction($request);

        $res = array('error' => $OrderModel->error, 'errortip' => $OrderModel->errortip, 'result' => array());


        ajax_return($res, $request['language_type']);
    }

    //学费减免批量审核拒绝
    function BatchChangeFeeWaiverOrderAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $OrderModel = new \Model\Gmc\OrderModel($request);

        $pidList = json_decode(stripslashes($request['pid_list']), true);
        if ($pidList) {
            foreach ($pidList as $value) {
                $data = array();
                $data['pay_pid'] = $value['pay_pid'];
                $data['status'] = $request['status'];
                $data['reason'] = $request['reason'];
                $data['tracks_url'] = $request['tracks_url'];
                $data['create_time'] = $request['create_time'];
                $result = $OrderModel->ChangeFeeWaiverOrderAction($data);
                if (!$result) {
                    $res = array('error' => $OrderModel->error, 'errortip' => $OrderModel->errortip, 'result' => array());
                    ajax_return($res, $request['language_type']);
                }
            }
            $res = array('error' => $OrderModel->error, 'errortip' => $OrderModel->errortip, 'result' => array());
        } else {
            $res = array('error' => 1, 'errortip' => '请选择订单', 'result' => array());
        }

        ajax_return($res, $request['language_type']);
    }

    //坏账处理审核
    function canceldebtsPayApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $OrderModel = new \Model\Gmc\OrderModel($request);
        $res = $OrderModel->canceldebtsPay($request);
        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["fieldname"] = "学员中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_enname";
        $field[$k]["fieldname"] = "学员英文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_branch";
        $field[$k]["fieldname"] = "学员编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "trading_pid";
        $field[$k]["fieldname"] = "交易编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "order_allprice";
        $field[$k]["fieldname"] = "订单总额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "order_paidprice";
        $field[$k]["fieldname"] = "已付金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "order_arrearageprice";
        $field[$k]["fieldname"] = "欠费金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "order_paymentprice";
        $field[$k]["fieldname"] = "实付金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "pay_price";
        $field[$k]["fieldname"] = "坏账处理金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "pay_note";
        $field[$k]["fieldname"] = "坏账处理原因";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "pay_issuccess_name";
        $field[$k]["fieldname"] = "审核状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "pay_createtime";
        $field[$k]["fieldname"] = "申请时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "pay_updatatime";
        $field[$k]["fieldname"] = "审核时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();

        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["list"] = $res['list'];
            $result["school"] = $res['school'];
            $result["fieldcustom"] = 0;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["school"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => $OrderModel->errortip, 'result' => $result);

        }
        ajax_return($res, $request['language_type']);
    }

    function examineCanceldebtsAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $OrderModel = new \Model\Gmc\OrderModel($request);
        $res = $OrderModel->examineCanceldebts($request);
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '审核成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $OrderModel->errortip, 'result' => $result);

        }
        ajax_return($res, $request['language_type']);
    }

    function getFreeOrderItemApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $OrderModel = new \Model\Gmc\OrderExamineModel($request, $request['order_pid'], 3);
        $res = $OrderModel->getFreeOrderItem();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $OrderModel->errortip, 'result' => $result);

        }
        ajax_return($res, $request['language_type']);
    }

    function examineDealOrderAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $OrderModel = new \Model\Gmc\OrderModel($request);
        $res = $OrderModel->examineDealOrder($request);
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '审核成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $OrderModel->errortip, 'result' => $result);

        }
        ajax_return($res, $request['language_type']);
    }

    function examineRenewOrderAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $OrderModel = new \Model\Gmc\OrderModel($request);
        $res = $OrderModel->examineRenewOrder($request);
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '审核成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $OrderModel->errortip, 'result' => $result);

        }
        ajax_return($res, $request['language_type']);
    }

    function examineReduceOrderAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $OrderModel = new \Model\Gmc\OrderModel($request);
        $res = $OrderModel->examineReduceOrder($request);
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '审核成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $OrderModel->errortip, 'result' => $result);

        }
        ajax_return($res, $request['language_type']);
    }

    function examineRefundOrderAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $OrderModel = new \Model\Gmc\OrderModel($request);
        $res = $OrderModel->examineRefundOrder($request);
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '审核成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $OrderModel->errortip, 'result' => $result);

        }
        ajax_return($res, $request['language_type']);
    }

    //批量审核拒绝退款订单
    function batchExamineRefundOrderAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);

        $pidList = json_decode(stripslashes($request['pidList']), 1);
        if ($pidList) {
            foreach ($pidList as $item) {
                $request['refund_pid'] = $item['refund_pid'];
                $OrderModel = new \Model\Gmc\OrderModel($request);
                $res = $OrderModel->examineRefundOrder($request);
            }
            if ($res) {
                $result["list"] = $res;
                $res = array('error' => 0, 'errortip' => '审核成功', 'result' => $result);
            } else {
                $result["list"] = array();
                $res = array('error' => 1, 'errortip' => $OrderModel->errortip, 'result' => $result);
            }
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '请选择学员', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    /*招行快速退费流程*/
    function ZhaohangRefundOrderAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $OrderModel = new \Model\Gmc\OrderModel($request);
        $res = $OrderModel->ZhaohangRefundOrder($request);
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '审核成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $OrderModel->errortip, 'result' => $result);

        }
        ajax_return($res, $request['language_type']);
    }

    /*招行快速退费查询流程*/
    function ZhaohangRefundOrderQueryAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $OrderModel = new \Model\Gmc\OrderModel($request);
        $res = $OrderModel->ZhaohangRefundOrderQuery($request);
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '退款成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $OrderModel->errortip, 'result' => $result);

        }
        ajax_return($res, $request['language_type']);
    }

    function examineHourFreeOrderAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $OrderModel = new \Model\Gmc\OrderModel($request);
        $res = $OrderModel->examineHourFreeOrder($request);
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '审核成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $OrderModel->errortip, 'result' => $result);

        }
        ajax_return($res, $request['language_type']);
    }

    function getDealOrderInfoApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $OrderModel = new \Model\Gmc\OrderExamineModel($request, $request['dealorder_pid'], 4);
        $res = $OrderModel->getDealOrderInfo();

        if ($res) {
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $res);
        } else {
            $res = array('error' => 1, 'errortip' => $OrderModel->errortip, 'result' => array());

        }
        ajax_return($res, $request['language_type']);
    }

    function getClockorderOrderInfoApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $OrderModel = new \Model\Gmc\OrderExamineModel($request, $request['clockorder_pid'], 5);
        $res = $OrderModel->getClockorderOrderInfo();

        if ($res) {
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $res);
        } else {
            $res = array('error' => 1, 'errortip' => $OrderModel->errortip, 'result' => array());

        }
        ajax_return($res, $request['language_type']);
    }


    /**
     * 减免学费的订单
     * author: ling
     * 对应接口文档 0001
     */
    function getFeeWaiverOrderApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $OrderModel = new \Model\Gmc\OrderModel($request);

        $res = $OrderModel->getFeeWaiverOrder($request);


        if ($res) {

            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $res);
        } else {
            $res = array('error' => 1, 'errortip' => $OrderModel->errortip, 'result' => array());

        }
        ajax_return($res, $request['language_type']);
    }

    function trialFreeListApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Gmc\OrderModel($request);
        $res = $Model->trialFreeList($request);

        $k = 0;
        $field = array();
        $field[$k]["fieldname"] = "clockorder_id";
        $field[$k]["fieldstring"] = "异动免审单ID";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "clockorder_pid";
        $field[$k]["fieldstring"] = "异动免审单号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "school_cnname";
        $field[$k]["fieldstring"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "school_branch";
        $field[$k]["fieldstring"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "student_cnname";
        $field[$k]["fieldstring"] = "学员名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "student_branch";
        $field[$k]["fieldstring"] = "学员编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "class_cnname";
        $field[$k]["fieldstring"] = "班级名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "class_branch";
        $field[$k]["fieldstring"] = "班级编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "clockorder_class_name";
        $field[$k]["fieldstring"] = "异动免审类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "clockorder_status_name";
        $field[$k]["fieldstring"] = "使用状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "clockorder_reason";
        $field[$k]["fieldstring"] = "异动免审原因";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "clockorder_playname";
        $field[$k]["fieldstring"] = "申请人名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "clockorder_createtime";
        $field[$k]["fieldstring"] = "创建时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $result = array();
        $result["field"] = $field;
        if ($res) {
            $result["list"] = $res['list'];
            $result["school"] = $res['school'];
            $result["allnum"] = $res['allnum'];
            $result = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["school"] = array();
            $result["allnum"] = 0;
            $result = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($result, $request['language_type']);
    }

    function classOpenApplyListApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Gmc\OrderModel($request);
        $res = $Model->classOpenApplyList($request);

        $k = 0;
        $field = array();
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "school_tagbak";
        $field[$k]["fieldname"] = "督导区域";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "班级名称";
        $field[$k]["fieldstring"] = "class_cnname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "班级编号";
        $field[$k]["fieldstring"] = "class_branch";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "最低开班限制";
        $field[$k]["fieldstring"] = "tuition_minclassnum";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "当前开班人数";
        $field[$k]["fieldstring"] = "openapply_studynum";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "申请原因";
        $field[$k]["fieldstring"] = "openapply_note";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "附件";
        $field[$k]["fieldstring"] = "openapply_checkpicurl";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $field[$k]["isqrcode"] = 1;
        $k++;

        $field[$k]["fieldname"] = "申请人";
        $field[$k]["fieldstring"] = "staffer_cnname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "申请时间";
        $field[$k]["fieldstring"] = "openapply_createtime";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "审核状态";
        $field[$k]["fieldstring"] = "openapply_status_name";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "审核人";
        $field[$k]["fieldstring"] = "exam_staffer_cnname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "审核时间";
        $field[$k]["fieldstring"] = "openapply_updatatime";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "审核备注";
        $field[$k]["fieldstring"] = "exam_openapply_note";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $result = array();
        $result["field"] = $field;
        if ($res) {
            $result["list"] = $res['list'];
            $result["school"] = $res['school'];
            $result["allnum"] = $res['allnum'];
            $result = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["school"] = array();
            $result["allnum"] = 0;
            $result = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($result, $request['language_type']);
    }

    function examOpenClassAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $OrderModel = new \Model\Gmc\OrderModel($request);
        $res = $OrderModel->examOpenClass($request);
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '审核成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $OrderModel->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function batchExamOpenClassAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $OrderModel = new \Model\Gmc\OrderModel($request);
        $res = $OrderModel->batchExamOpenClass($request);
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => $OrderModel->oktip, 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $OrderModel->errortip, 'result' => $result);

        }
        ajax_return($res, $request['language_type']);
    }

    function studentLossClassApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Gmc\OrderModel($request);
        $res = $Model->studentLossClass($request);

        $k = 0;
        $field = array();
        $field[$k]["fieldname"] = "class_id";
        $field[$k]["fieldstring"] = "班级ID";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "class_cnname";
        $field[$k]["fieldstring"] = "班级中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "class_enname";
        $field[$k]["fieldstring"] = "班级别名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "class_branch";
        $field[$k]["fieldstring"] = "班级编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $result = array();
        $result["field"] = $field;
        if ($res) {
            $result["list"] = $res;
            $result = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
        } else {
            $result = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($result, $request['language_type']);
    }


    function applyFreeClassAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $OrderModel = new \Model\Gmc\OrderModel($request);
        $res = $OrderModel->applyFreeClass($request);
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '提交成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $OrderModel->errortip, 'result' => $result);

        }
        ajax_return($res, $request['language_type']);
    }


    function autoLossListApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Gmc\OrderModel($request);
        $res = $Model->autoLossList($request);

        $k = 0;
        $field = array();

        $field[$k]["fieldname"] = "school_branch";
        $field[$k]["fieldstring"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "school_cnname";
        $field[$k]["fieldstring"] = "学校名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "district_cnname";
        $field[$k]["fieldstring"] = "所属区域";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "student_branch";
        $field[$k]["fieldstring"] = "学员编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "student_cnname";
        $field[$k]["fieldstring"] = "学员中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "coursetype_cnname";
        $field[$k]["fieldstring"] = "班组";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "changelog_day";
        $field[$k]["fieldstring"] = "最后出班日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "hour_day";
        $field[$k]["fieldstring"] = "最后耗课日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "warning_days";
        $field[$k]["fieldstring"] = "预警天数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "balance";
        $field[$k]["fieldstring"] = "账户余额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "coursebalance_figure";
        $field[$k]["fieldstring"] = "班组剩余课程余额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $result = array();
        $result["field"] = $field;
        if ($res) {
            $result["list"] = $res;
            $result = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $result = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($result, $request['language_type']);
    }

    function stuLossAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ChangeModel = new \Model\Smc\ChangeModel($request);
//        $request['reason'] .= ",超过180天";
        $res = $ChangeModel->stuLoss($request);
        $result = array();
        if ($res) {
            $result["list"] = $res;
            $result = array('error' => 0, 'errortip' => '流失成功', 'result' => $result);
        } else {
            $result = array('error' => 1, 'errortip' => $ChangeModel->errortip, 'result' => $result);
        }
        ajax_return($result, $request['language_type']);
    }

    //自动班组流失
    //xzl-2024-08-14
    function studAutoLostAction()
    {
        $request = Input('post.', '', 'trim,addslashes');

        $ChangeModel = new \Model\Smc\ChangeModel($request);
        $res = $ChangeModel->studAutoLost($request);
        if ($res) {
            $comment = date("Y-m-d") . '班组自动流失:成功' . $res['success'] . '人,失败' . $res['failed'] . '人.' . dataEncode($res['list']);
        } else {
            $comment = date("Y-m-d") . '班组自动流失失败';
        }
        $this->addGmcWorkLog(8888, 2, "财务管理->校园运营->班组自动流失", '定时任务', $comment);
    }

    //异动申请记录
    function getGmcChangeApplyApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Gmc\OrderModel($request);
        $res = $Model->getGmcChangeApplyApi($request);
        $k = 0;
        $field = array();
        $field[$k]["fieldstring"] = "from_school_shortname";
        $field[$k]["fieldname"] = "学校名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "from_school_branch";
        $field[$k]["fieldname"] = "学校编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["fieldname"] = "学员中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_enname";
        $field[$k]["fieldname"] = "学员英文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_branch";
        $field[$k]["fieldname"] = "学员编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "stuchange_name";
        $field[$k]["fieldname"] = "异动类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "apply_status_name";
        $field[$k]["fieldname"] = "异动状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "to_school_shortname";
        $field[$k]["fieldname"] = "异动学校";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "apply_reason";
        $field[$k]["fieldname"] = "异动描述";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "apply_time";
        $field[$k]["fieldname"] = "异动日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "staffer_name";
        $field[$k]["fieldname"] = "执行人";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "to_staffer_name";
        $field[$k]["fieldname"] = "审批人";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "apply_refusereson";
        $field[$k]["fieldname"] = "拒绝原因";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();

        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["list"] = $res['list'];
            $result["fieldcustom"] = 0;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["allnum"] = 0;
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无异动申请记录', 'result' => $result);

        }
        ajax_return($res, $request['language_type']);
    }

    //逾期延班申请记录
    function getSignStuLossApplyListApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Gmc\OrderModel($request);
        $res = $Model->getSignStuLossApplyList($request);

        $k = 0;
        $field = array();

        $field[$k]["fieldname"] = "apply_id";
        $field[$k]["fieldstring"] = "申请ID";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "school_branch";
        $field[$k]["fieldstring"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "school_cnname";
        $field[$k]["fieldstring"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "district_cnname";
        $field[$k]["fieldstring"] = "所属区域";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "student_branch";
        $field[$k]["fieldstring"] = "学员编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "student_cnname";
        $field[$k]["fieldstring"] = "学员中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "apply_status_name";
        $field[$k]["fieldstring"] = "审批状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "coursetype_cnname";
        $field[$k]["fieldstring"] = "班组";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "apply_note";
        $field[$k]["fieldstring"] = "申请原因";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "staffer_cnname";
        $field[$k]["fieldstring"] = "申请人";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "apply_reason";
        $field[$k]["fieldstring"] = "审核备注";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "apply_createtime";
        $field[$k]["fieldstring"] = "申请时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "apply_refusetime";
        $field[$k]["fieldstring"] = "审核时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "study_endday";
        $field[$k]["fieldstring"] = "最后出班日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "last_atte_date";
        $field[$k]["fieldstring"] = "最后耗课日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "warning_days";
        $field[$k]["fieldstring"] = "预警天数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "left_balance";
        $field[$k]["fieldstring"] = "账户余额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "left_amount";
        $field[$k]["fieldstring"] = "班组剩余课程余额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();

        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["list"] = $res['list'];
            $result["fieldcustom"] = 0;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["allnum"] = 0;
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无异动申请记录', 'result' => $result);

        }
        ajax_return($res, $request['language_type']);
    }

    function examSignStuLossAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $OrderModel = new \Model\Gmc\OrderModel($request);
        $res = $OrderModel->examSignStuLoss($request);
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '审核成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $OrderModel->errortip, 'result' => $result);

        }
        ajax_return($res, $request['language_type']);
    }

}
