<?php

namespace Work\Controller\Gmcapi;


class CrmController extends viewTpl
{
    public $u;
    public $t;
    public $c;
    public $visitType = "api";
    public $Model;

    //预加载处理类
    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
    }

    function ThisVerify($request)
    {
        $paramArray = array();
        $paramArray['staffer_id'] = $request['staffer_id'];
        $paramArray['company_id'] = $request['company_id'];
        $paramArray['token'] = $request['token'];
        if (!$this->UserLimit($paramArray)) {
            $result = array();
            $result["list"] = array();
            $result["tokeninc"] = "0";
            $res = array('error' => '1', 'errortip' => "用户token失效了", 'result' => $result);
            ajax_return($res, $request['language_type']);
        }
    }


    //招生来源列表
    function HomeView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\CrmModel($request);

        $result = $this->Model->getFrommediaList($request);
        ajax_return($result, $request['language_type']);
    }


    //招生来源列表
    function GetmediaView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\CrmModel($request);

        $result = $this->Model->Getmedia($request);
        ajax_return($result, $request['language_type']);
    }

    //添加招生来源
    function addFrommediaAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\CrmModel($request);

        $result = $this->Model->addFrommediaAction($request);
        ajax_return($result, $request['language_type']);
    }

    //编辑招生来源
    function updateFrommediaAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\CrmModel($request);

        $result = $this->Model->updateFrommediaAction($request);
        ajax_return($result, $request['language_type']);
    }

    //删除招生来源
    function delFrommediaAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\CrmModel($request);

        $result = $this->Model->delFrommediaAction($request);
        ajax_return($result, $request['language_type']);
    }

    //沟通方式列表
    function commodeView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\CrmModel($request);

        $result = $this->Model->getCommodeList($request);
        ajax_return($result, $request['language_type']);
    }

    //编辑沟通方式
    function updateCommodeAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\CrmModel($request);

        $result = $this->Model->updateCommodeAction($request);
        ajax_return($result, $request['language_type']);
    }

    //添加沟通方式
    function addCommodeAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\CrmModel($request);

        $result = $this->Model->addCommodeAction($request);
        ajax_return($result, $request['language_type']);
    }

    //删除沟通方式
    function delCommodeAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\CrmModel($request);

        $result = $this->Model->delCommodeAction($request);
        ajax_return($result, $request['language_type']);
    }

    //变更审核列表
    function changedemoView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\CrmModel($request);

        $result = $this->Model->changedemo($request);
        ajax_return($result, $request['language_type']);
    }

    //编辑变更审核
    function updateChangedemoAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\CrmModel($request);

        $result = $this->Model->updateChangedemoAction($request);
        ajax_return($result, $request['language_type']);
    }

    //添加变更审核
    function addChangedemoAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\CrmModel($request);

        $result = $this->Model->addChangedemoAction($request);
        ajax_return($result, $request['language_type']);
    }

    //删除变更审核
    function delChangedemoAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\CrmModel($request);

        $result = $this->Model->delChangedemoAction($request);
        ajax_return($result, $request['language_type']);
    }

    //来源渠道列表
    function channelView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\CrmModel($request);

        $result = $this->Model->getChannelList($request);
        ajax_return($result, $request['language_type']);
    }

    //添加来源渠道
    function addChannelAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\CrmModel($request);

        $result = $this->Model->addChannelAction($request);
        ajax_return($result, $request['language_type']);
    }

    //编辑来源渠道
    function updateChannelAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\CrmModel($request);

        $result = $this->Model->updateChannelAction($request);
        ajax_return($result, $request['language_type']);
    }

    //删除来源渠道
    function delChannelAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\CrmModel($request);

        $result = $this->Model->delChannelAction($request);
        ajax_return($result, $request['language_type']);
    }

    //有效变更/无效变更
    function updateChannelApplyAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\CrmModel($request);

        $result = $this->Model->updateChannelApplyAction($request);
        ajax_return($result, $request['language_type']);
    }

    //有效变更/无效变更  ---- 批量处理有效无效操作
    function updateSomeChannelApplyAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\CrmModel($request);

        $result = $this->Model->updateSomeChannelApplyAction($request);
        ajax_return($result, $request['language_type']);
    }

    //招生渠道变更审核列表
    function getChannellogListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\CrmModel($request);

        $result = $this->Model->getChannellogList($request);
        ajax_return($result, $request['language_type']);
    }

    //招生渠道变更记录
    function getChannellogOneView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\CrmModel($request);

        $result = $this->Model->getChannellogOne($request);
        ajax_return($result, $request['language_type']);
    }

    //渠道变更记录
    function getChannelTrackView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Gmc\CrmModel($request);

        $result = $Model->getChannelTrack($request);
        ajax_return($result, $request['language_type']);
    }

    //渠道变更记录
    function getChannelChangeView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\CrmModel($request);

        $result = $this->Model->getChannelChange($request);
        ajax_return($result, $request['language_type']);
    }

    //CRM用户角色列表
    function postroleView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\CrmModel($request);

        $result = $this->Model->getPostroleList($request);
        ajax_return($result, $request['language_type']);
    }

    //添加CRM用户角色
    function addPostroleAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\CrmModel($request);

        $result = $this->Model->addPostroleAction($request);
        ajax_return($result, $request['language_type']);
    }

    //编辑CRM用户角色
    function updatePostroleAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\CrmModel($request);

        $result = $this->Model->updatePostroleAction($request);
        ajax_return($result, $request['language_type']);
    }

    //删除CRM用户角色
    function delPostroleAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\CrmModel($request);

        $result = $this->Model->delPostroleAction($request);
        ajax_return($result, $request['language_type']);
    }
    //无效原因模板列表
    function getInvalidnoteListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\CrmModel($request);

        $result = $this->Model->getInvalidnoteList($request);
        ajax_return($result, $request['language_type']);
    }

    //添加无效原因
    function addInvalidnoteAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\CrmModel($request);

        $result = $this->Model->addInvalidnoteAction($request);
        ajax_return($result, $request['language_type']);
    }

    //编辑无效原因
    function updateInvalidnoteAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\CrmModel($request);

        $result = $this->Model->updateInvalidnoteAction($request);
        ajax_return($result, $request['language_type']);
    }
    //无效原因开启关闭
    function isInvalidnoteOpenAction(){
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        //model
        $Model = new \Model\Gmc\CrmModel($request);
        $Model->isInvalidnoteOpenAction($request);
        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $Model->result), $request['language_type']);
    }
    //删除无效原因
    function delInvalidnoteAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\CrmModel($request);

        $result = $this->Model->delInvalidnoteAction($request);
        ajax_return($result, $request['language_type']);
    }


    //获取无效原因模板
    function getInvalidnoteApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $company_id = $request['company_id'];

        if (!$company_id) {
            $res = array('error' => '1', 'errortip' => "公司id有误", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        $Model = new \Model\Crm\PublicModel($request);
        $dataList = $Model->getInvalidnoteApi($company_id);

        $field['invalidnote_id'] = "序号id";
        $field['invalidnote_code'] = "无效编号";
        $field['invalidnote_reason'] = "无效原因";
        $result['field'] = $field;
        if ($dataList) {
            $result['list'] = $dataList;
        } else {
            $result['list'] = array();
        }

        $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        ajax_return($res, $request['language_type']);
    }


    //用户跟踪内容模板列表
    function tracenoteView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\CrmModel($request);

        $result = $this->Model->getTracenoteList($request);
        ajax_return($result, $request['language_type']);
    }

    //添加用户跟踪内容模板
    function addTracenoteAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\CrmModel($request);

        $result = $this->Model->addTracenoteAction($request);
        ajax_return($result, $request['language_type']);
    }

    //编辑用户跟踪内容模板
    function updateTracenoteAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\CrmModel($request);

        $result = $this->Model->updateTracenoteAction($request);
        ajax_return($result, $request['language_type']);
    }

    //删除用户跟踪内容模板
    function delTracenoteAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\CrmModel($request);

        $result = $this->Model->delTracenoteAction($request);
        ajax_return($result, $request['language_type']);
    }



    //97添加活动发布相关接口


    // 海报活动
    function getShareposterListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        //model
        $Model = new \Model\Gmc\ActivityCrmModel($request);
        $dataList = $Model->getShareposterList($request);

        $key = 0;
        $field = array();
        $field[$key]["fieldname"] = "activity_name";
        $field[$key]["fieldstring"] = "活动名称";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $field[$key]["ismethod"] = 1;
        $key++;

        $field[$key]["fieldname"] = "activity_starttime";
        $field[$key]["fieldstring"] = "活动时间";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "clientnum";
        $field[$key]["fieldstring"] = "有效名单数";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "actnum";
        $field[$key]["fieldstring"] = "活动意向客户";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "officialnum";
        $field[$key]["fieldstring"] = "转正人数";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "percentconversion";
        $field[$key]["fieldstring"] = "转化率";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "shareqrcode";
        $field[$key]["fieldstring"] = "二维码";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $field[$key]["isqrcode"] = 1;
        $field[$key]["isschool"] = 1;
        $key++;

        $field[$key]["fieldname"] = "shareqrcodeurl";
        $field[$key]["fieldstring"] = "二维码地址";
        $field[$key]["show"] = 0;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "staffer_cnname";
        $field[$key]["fieldstring"] = "发布人";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

//        $field[$key]["fieldname"] = "activity_type";
//        $field[$key]["fieldstring"] = "活动来源";
//        $field[$key]["show"] = 1;
//        $field[$key]["custom"] = 0;
//        $key++;

        $field[$key]["fieldname"] = "frommedia_name";
        $field[$key]["fieldstring"] = "渠道类型";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "channel_id";
        $field[$key]["fieldstring"] = "渠道明细ID";
        $field[$key]["show"] = 0;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "channel_name";
        $field[$key]["fieldstring"] = "渠道明细";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $result = array();
        if ($dataList) {
            $result["fieldcustom"] = 1;
            $result["field"] = $field;
            $result["list"] = $dataList['datalist'];
            if ($result["list"]) {
                $res = array('error' => '0', 'errortip' => '招生活动管理', 'allnum' => $dataList['count'], 'result' => $result);
            } else {
                $result["list"] = array();
                $res = array('error' => '1', 'errortip' => '暂无招生活动信息', 'allnum' => 0, 'result' => $result);
            }
        } else {
            $result["list"] = array();
            $res = array('error' => '1', 'errortip' => '暂无招生活动信息', 'allnum' => 0, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //活动列表
    function sellActivityView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        //model
        $Model = new \Model\Gmc\ActivityCrmModel($request);
        $dataList = $Model->sellActivity($request);

        $field = array();
        $key = 0;
        $field[$key]["fieldname"] = "activity_name";
        $field[$key]["fieldstring"] = "招生活动名称";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "activity_starttime";
        $field[$key]["fieldstring"] = "活动时间";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "clientnum";
        $field[$key]["fieldstring"] = "有效名单数";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "officialnum";
        $field[$key]["fieldstring"] = "转正人数";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;


        $field[$key]["fieldname"] = "percentconversion";
        $field[$key]["fieldstring"] = "转化率";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "iswxqrcode";
        $field[$key]["fieldstring"] = "二维码";
        $field[$key]["show"] = 0;
        $field[$key]["custom"] = 0;
        $field[$key]["isqrcode"] = 1;
        $key++;

        $field[$key]["fieldname"] = "QRcodeUrl";
        $field[$key]["fieldstring"] = "小程序二维码";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $field[$key]["iswxqrcode"] = 1;
        $key++;

        $field[$key]["fieldname"] = "staffer_cnname";
        $field[$key]["fieldstring"] = "发布人";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "qrcodeurl";
        $field[$key]["fieldstring"] = "二维码链接";
        $field[$key]["show"] = 0;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "frommedia_name";
        $field[$key]["fieldstring"] = "活动渠道类型";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 1;
        $key++;

        $field[$key]["fieldname"] = "channel_name";
        $field[$key]["fieldstring"] = "活动渠道明细";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 1;
        $key++;


        $result = array();
        if ($dataList) {
            $result["fieldcustom"] = 1;
            $result["field"] = $field;
            $result["list"] = $dataList['datalist'];
            $res = array('error' => '0', 'errortip' => '招生活动管理', 'allnum' => $dataList['count'], 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => '1', 'errortip' => '暂无招生活动管理信息', 'allnum' => array(), 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //招生目标 -- >> 复制活动的详细情况
    function copyActivityOneApi()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        if (!isset($request['activity_id']) || $request['activity_id'] == '') {
            $res = array('error' => '1', 'errortip' => '对应活动必选', 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        //model
        $Model = new \Model\Gmc\ActivityCrmModel($request);
        $dataList = $Model->copyActivityOneApi($request);

        $field = array();
        $field["activity_id"] = "活动id";
        $field["company_id"] = "公司";
        $field["staffer_id"] = "校务职工id";
        $field["marketer_id"] = "crm职工id";
        $field['activity_type'] = "活动来源";
        $field['activity_name'] = "名称";
        $field['activity_theme'] = "活动招生标题";
        $field['activity_starttime'] = "开始时间";
        $field['activity_endtime'] = "结束时间";
        $field['activity_istemp'] = "是否使用招生模板";
        $field['activitytemp_id'] = "招生模板id";
        $field['activity_tempurl'] = "自定义招生模板链接";
        $field['activity_contacttel'] = "联系方式";
        $field['activity_address'] = "活动地址";
        $field['activity_img'] = "主图";
        $field['activity_content'] = "活动详情";
        $field['activity_rule'] = "活动规则";
        $field['activity_aboutus'] = "关于我们";
        $field['activity_customcontent'] = "自定义详情";
        $field['activity_sharedesc'] = "课叮铛分享描述";
        $field['activity_shareimg'] = "微信分享小图标";
        $field['frommedia_name'] = "来源渠道信息";
        $field['activity_createtime'] = "创建时间";

        $field["activity_issex"] = "是否需要选择性别";
        $field["activity_issex_must"] = "是否必填";
        $field["activity_isbirthday"] = "是否需要选择生日";
        $field["activity_isbirthday_must"] = "是否必填";
        $field["activity_isaddress"] = "是否需要选择地址";
        $field["activity_isaddress_must"] = "是否必填";

        $result = array();
        if ($dataList) {
            $result["field"] = $field;
            $result["data"] = $dataList;
            if ($result["data"]) {
                $res = array('error' => '0', 'errortip' => '复制活动成功', 'result' => $result);
            } else {
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '复制活动失败', 'result' => $result);
            }
        } else {
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '复制活动失败', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //招生目标 -- >> 某个活动的详细情况
    function getActivityShareApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        if (!isset($request['activity_id']) || $request['activity_id'] == '') {
            $res = array('error' => '1', 'errortip' => '对应活动必选', 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        //model
        $Model = new \Model\Gmc\ActivityCrmModel($request);
        $dataList = $Model->getActivityShareApi($request);

        $field = array();
        $field["activity_shareimg"] = "分享小图标";
        $field["activity_sharedesc"] = "分享描述";

        $result = array();
        if ($dataList) {
            $result["field"] = $field;
            $result["data"] = $dataList;
            if ($result["data"]) {
                $res = array('error' => '0', 'errortip' => '某个活动分享的详细情况', 'result' => $result);
            } else {
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '暂无某个活动分享的详细情况', 'result' => $result);
            }
        } else {
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '暂无某个活动的详细情况', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //某个活动的详细情况
    function goalActivityOneApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        if (!isset($request['activity_id']) || $request['activity_id'] == '') {
            $res = array('error' => '1', 'errortip' => '对应活动必选', 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        //model
        $Model = new \Model\Gmc\ActivityCrmModel($request);
        $dataList = $Model->ActivityOneApi($request);

        $field = array();
        $field["activity_id"] = "目标id";
        $field["activity_name"] = "活动名称";
        $field["staffer_id"] = "职工ID";
        $field["activity_pattern"] = "验券模式";
        $field["activity_fittype"] = "适用学校   0 部分适配  1 全部适配";
        $field["staffer_cnname"] = "职工姓名";
        $field["activity_starttime"] = "开始时间";
        $field["activity_endtime"] = "结束时间";
        $field["activity_istemp"] = "是否使用招生模板:0不使用1使用";
        $field["activitytemp_id"] = "自定义招生模板URL前缀";
        $field["activity_tempurl"] = "招生活动模板ID";
        $field["activity_img"] = "主图";
        $field["activity_rule"] = "活动规则";
        $field["activity_rule"] = "活动规则";
        $field["channel_id"] = "渠道明细id";
        $field["activity_content"] = "活动详情";
        $field["activity_aboutus"] = "关于我们";
        $field["activity_contacttel"] = "联系方式";
        $field["activity_address"] = "活动地址";
        $field["activity_createtime"] = "创建时间";
        $field["activity_updatetime"] = "修改时间";

        $field["activity_Url"] = "链接";
        $field["activity_appUrl"] = "二维码链接";
        $field["activitytemp_name"] = "招生模板名称";
        $field["activitytemp_styleimg"] = "招生模板图片查看";
        $field["activitytemp_bannerimg"] = "招生模板banner示意查看";
        $field["activity_sharedesc"] = "课叮铛分享描述";
        $field["activity_shareimg"] = "微信分享小图标";

        $field["activity_issex"] = "是否需要选择性别";
        $field["activity_issex_must"] = "是否必填";
        $field["activity_isbirthday"] = "是否需要选择生日";
        $field["activity_isbirthday_must"] = "是否必填";
        $field["activity_isaddress"] = "是否需要选择地址";
        $field["activity_isaddress_must"] = "是否必填";

        $field["ishaveclient"] = "是否产生有名单";

        $result = array();
        if ($dataList) {
            $result["field"] = $field;
            $result["data"] = $dataList;
            $res = array('error' => '0', 'errortip' => '某个活动的详细情况', 'result' => $result);
        } else {
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '某个活动的详细情况', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //二维码
    function goalActivityshowimgView()
    {
        header('Content-Type:image/png');
        $request = Input('get.', '', 'trim,addslashes');
        $codeUrl = base64_decode($request['imgurl']);//."&typ=1"
        require(ROOT_PATH . 'Core/Classlib/Webqrcode.php');//二维码生成
        $QRcode = new \QRcode();
        $errorCorrectionLevel = 'H';//容错级别
        $matrixPointSize = 15;//生成图片大小
        echo $QRcode->png($codeUrl, false, $errorCorrectionLevel, $matrixPointSize, 2);
    }

    /**
     * 在二维码的中间区域镶嵌图片
     * @param $QR 二维码数据流。比如file_get_contents(imageurl)返回的东东,或者微信给返回的东东
     * @param $logo 中间显示图片的数据流。比如file_get_contents(imageurl)返回的东东
     * @return  返回图片数据流
     */
    function qrcodeWithLogo($QR,$logo){
        $QR   = imagecreatefromstring ($QR);
        $logo = imagecreatefromstring ($logo);
        $QR_width    = imagesx ( $QR );//二维码图片宽度
        $QR_height   = imagesy ( $QR );//二维码图片高度
        $logo_width  = imagesx ( $logo );//logo图片宽度
        $logo_height = imagesy ( $logo );//logo图片高度
//        $logo_qr_width  = $QR_width / 2.2;//组合之后logo的宽度(占二维码的1/2.2)
        $logo_qr_width  = $QR_width / 4.8;//组合之后logo的宽度(占二维码的1/2.2)
        $scale  = $logo_width / $logo_qr_width;//logo的宽度缩放比(本身宽度/组合后的宽度)
        $logo_qr_height = $logo_height / $scale;//组合之后logo的高度
        $from_width = ($QR_width - $logo_qr_width) / 2;//组合之后logo左上角所在坐标点
        /**
         * 重新组合图片并调整大小
         * imagecopyresampled() 将一幅图像(源图象)中的一块正方形区域拷贝到另一个图像中
         */
        imagecopyresampled ( $QR, $logo, $from_width, $from_width, 0, 0, $logo_qr_width, $logo_qr_height, $logo_width, $logo_height );
        /**
         * 如果想要直接输出图片，应该先设header。header("Content-Type: image/png; charset=utf-8");
         * 并且去掉缓存区函数
         */
        //获取输出缓存，否则imagepng会把图片输出到浏览器
        ob_start();
        imagepng ( $QR );
        imagedestroy($QR);
        imagedestroy($logo);
        $contents =  ob_get_contents();
        ob_end_clean();
        return $contents;
    }
    /**
     * 剪切图片为圆形
     * @param  $picture 图片数据流 比如file_get_contents(imageurl)返回的东东
     * @return 图片数据流
     */
    function yuanImg($picture) {
        $src_img = imagecreatefromstring($picture);
        $w   = imagesx($src_img);
        $h   = imagesy($src_img);
        $w   = min($w, $h);
        $h   = $w;
        $img = imagecreatetruecolor($w, $h);
        //这一句一定要有
        imagesavealpha($img, true);
        //拾取一个完全透明的颜色,最后一个参数127为全透明
//        $bg = imagecolorallocatealpha($img, 255, 255, 255, 127);
        $bg = imagecolorallocatealpha($img, 255, 255, 255, 0);
        imagefill($img, 0, 0, $bg);
        $r   = $w / 2; //圆半径
        $y_x = $r; //圆心X坐标
        $y_y = $r; //圆心Y坐标
        for ($x = 0; $x < $w; $x++) {
            for ($y = 0; $y < $h; $y++) {
                $rgbColor = imagecolorat($src_img, $x, $y);
                if (((($x - $r) * ($x - $r) + ($y - $r) * ($y - $r)) < ($r * $r))) {
                    imagesetpixel($img, $x, $y, $rgbColor);
                }
            }
        }
        /**
         * 如果想要直接输出图片，应该先设header。header("Content-Type: image/png; charset=utf-8");
         * 并且去掉缓存区函数
         */
        //获取输出缓存，否则imagepng会把图片输出到浏览器
        ob_start();
        imagepng ( $img );
        imagedestroy($img);
        $contents =  ob_get_contents();
        ob_end_clean();
        return $contents;
    }

    //添加活动
    function addGoalActivityAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        if (!isset($request['activity_name']) || $request['activity_name'] == '') {
            $res = array('error' => '1', 'errortip' => '活动名称不能为空', 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        if (!isset($request['company_id']) || $request['company_id'] == '') {
            $res = array('error' => '1', 'errortip' => '企业id不能为空', 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        if (!isset($request['staffer_id']) || $request['staffer_id'] == '') {
            $res = array('error' => '1', 'errortip' => '职工id不能为空', 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        if ($this->DataControl->selectOne("select activity_id from crm_sell_activity where company_id = '{$request['company_id']}' and activity_name = '{$request['activity_name']}' ")) {
            $res = array('error' => '1', 'errortip' => '活动名称已存在', 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        //model
        $Model = new \Model\Gmc\ActivityCrmModel($request);
        $dataList = $Model->addActivityAction($request);

        if ($dataList) {
            $res = array('error' => '0', 'errortip' => "活动新增成功", "activity_id" => $dataList);
        } else {
            $res = array('error' => '1', 'errortip' => '活动新增失败');
        }
        ajax_return($res, $request['language_type']);
    }

    //修改招生目标
    function updateGoalActivityAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        if (!isset($request['activity_id']) || $request['activity_id'] == '') {
            $res = array('error' => '1', 'errortip' => '对应活动必选', 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        if (!isset($request['activity_name']) || $request['activity_name'] == '') {
            $res = array('error' => '1', 'errortip' => '活动名称不能为空', 'result' => array());
            ajax_return($res, $request['language_type']);
        }

        $goalOne = $this->DataControl->getFieldOne("crm_sell_activity", "activity_id,staffer_id,activity_starttime,activity_endtime", "activity_id = '{$request['activity_id']}'");
        $staffrOne = $this->DataControl->getFieldOne("smc_staffer", "account_class", "staffer_id='{$request['staffer_id']}' and company_id='{$request['company_id']}' ");

        if ($staffrOne['account_class'] == '0' && $request['staffer_id'] !== $goalOne['staffer_id']) {
            $res = array('error' => '1', 'errortip' => "您不是活动的创建者，无法修改！", 'result' => array());
            ajax_return($res, $request['language_type']);
        }

        if (($this->DataControl->getFieldOne("crm_client", "client_id", "activity_id='{$request['activity_id']}' ")) && $goalOne['activity_starttime'] !== $request['activity_starttime']) {
            $res = array('error' => '1', 'errortip' => "该活动已产生有效名单，活动开始时间{$goalOne['activity_starttime']}不可修改为{$request['activity_starttime']}", 'result' => array());
            ajax_return($res, $request['language_type']);
        }

        if (($this->DataControl->getFieldOne("crm_client", "client_id", "activity_id='{$request['activity_id']}' ")) && $goalOne['activity_endtime'] !== $request['activity_endtime']) {
            $res = array('error' => '1', 'errortip' => "该活动已产生有效名单，活动结束时间{$goalOne['activity_endtime']}不可修改为{$request['activity_endtime']}", 'result' => array());
            ajax_return($res, $request['language_type']);
        }

        if ($staffrOne['account_class'] == '1') {
            $goalOne = TRUE;
        }
        if ($goalOne) {
            //model
            $Model = new \Model\Gmc\ActivityCrmModel($request);
            $dataList = $Model->updateActivityAction($request, $staffrOne['account_class']);
            $field = array();
            $field['activity_name'] = "活动标题";
            $field['activity_theme'] = "活动招生标题";
            $field["activity_starttime"] = "开始时间";
            $field["activity_endtime"] = "结束时间";
            $field["activity_istemp"] = "是否使用招生模板:0不使用1使用";
            $field["activitytemp_id"] = "自定义招生模板URL前缀";
            $field["activity_tempurl"] = "招生活动模板ID";
            $field["activity_img"] = "主图";
            $field["activity_rule"] = "活动规则";
            $field["activity_content"] = "活动详情";
            $field["activity_aboutus"] = "关于我们";
            $field["activity_contacttel"] = "联系方式";
            $field["activity_address"] = "活动地址";
            $field['activity_updatetime'] = "修改时间";
            $field["activity_sharedesc"] = "课叮铛分享描述";
            $field['activity_shareimg'] = "微信分享小图标";

            $field["activity_issex"] = "是否需要选择性别";
            $field["activity_issex_must"] = "是否必填";
            $field["activity_isbirthday"] = "是否需要选择生日";
            $field["activity_isbirthday_must"] = "是否必填";
            $field["activity_isaddress"] = "是否需要选择地址";
            $field["activity_isaddress_must"] = "是否必填";

            if ($dataList) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $dataList;
                $res = array('error' => '0', 'errortip' => "活动修改成功", 'result' => $result);
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '活动修改失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '您没有权限修改', 'result' => $result);
        }

        ajax_return($res, $request['language_type']);
    }

    //活动学校适配表
    function ActivitySchoolApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        if (!isset($request['activity_id']) || $request['activity_id'] == '') {
            $res = array('error' => '1', 'errortip' => '对应活动必选', 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        if (!isset($request['adaptive']) || $request['adaptive'] == '') {
            $res = array('error' => '1', 'errortip' => '筛选类型必须选择', 'result' => array());
            ajax_return($res, $request['language_type']);
        }

        //model
        $Model = new \Model\Gmc\ActivityCrmModel($request);
        $dataList = $Model->ActivitySchoolApi($request);

        $field = array();
        $field[0]["fieldname"] = "school_id";
        $field[0]["fieldstring"] = "学校id";
        $field[0]["show"] = 0;
        $field[0]["custom"] = 0;

        $field[1]["fieldname"] = "school_branch";
        $field[1]["fieldstring"] = "校区编号";
        $field[1]["show"] = 1;
        $field[1]["custom"] = 0;

        $field[2]["fieldname"] = "school_cnname";
        $field[2]["fieldstring"] = "校园名称";
        $field[2]["show"] = 1;
        $field[2]["custom"] = 0;

        $field[3]["fieldname"] = "school_enname";
        $field[3]["fieldstring"] = "检索代码";
        $field[3]["show"] = 1;
        $field[3]["custom"] = 0;

        $field[4]["fieldname"] = "district_cnname";
        $field[4]["fieldstring"] = "所在区域";
        $field[4]["show"] = 1;
        $field[4]["custom"] = 0;

        $field[5]["fieldname"] = "adaptive";
        $field[5]["fieldstring"] = "是否适配";
        $field[5]["show"] = 0;
        $field[5]["custom"] = 0;

        if ($dataList) {
            $result = array();
            $result["field"] = $field;

            $result["district"] = $dataList['district'];
            $result["data"] = $dataList['datalist'];
            if (!$result["data"]) {
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '暂无适配学校', 'result' => $result);
                ajax_return($res, $request['language_type']);
            }


            $res = array('error' => '0', 'errortip' => "获取成功", 'allnum' => $dataList['count'], 'result' => $result);
        } else {
            $result = array();

            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '暂无适配学校', 'result' => $result);
        }

        ajax_return($res, $request['language_type']);
    }

    //活动班种 适配表
    function ActivityCoursecatApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        if (!isset($request['activity_id']) || $request['activity_id'] == '') {
            $res = array('error' => '1', 'errortip' => '对应活动必选', 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        if (!isset($request['adaptive']) || $request['adaptive'] == '') {
            $res = array('error' => '1', 'errortip' => '筛选类型必须选择', 'result' => array());
            ajax_return($res, $request['language_type']);
        }

        //model
        $Model = new \Model\Gmc\ActivityCrmModel($request);
        $dataList = $Model->ActivityCoursecatApi($request);

        $field = array();
        $field[0]["fieldname"] = "coursecat_id";
        $field[0]["fieldstring"] = "班种ID";
        $field[0]["show"] = 0;
        $field[0]["custom"] = 0;

        $field[1]["fieldname"] = "coursecat_cnname";
        $field[1]["fieldstring"] = "班种名称";
        $field[1]["show"] = 1;
        $field[1]["custom"] = 0;

        $field[2]["fieldname"] = "coursecat_branch";
        $field[2]["fieldstring"] = "班种编号";
        $field[2]["show"] = 1;
        $field[2]["custom"] = 0;

        $field[3]["fieldname"] = "coursetype_cnname";
        $field[3]["fieldstring"] = "班组名称";
        $field[3]["show"] = 1;
        $field[3]["custom"] = 0;

        $field[4]["fieldname"] = "coursetype_branch";
        $field[4]["fieldstring"] = "班组编号";
        $field[4]["show"] = 1;
        $field[4]["custom"] = 0;

        if ($dataList) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $dataList['datalist'];
            if (!$result["data"]) {
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '暂无适配班种', 'result' => $result);
                ajax_return($res, $request['language_type']);
            }

            $res = array('error' => '0', 'errortip' => "获取成功", 'allnum' => $dataList['count'], 'result' => $result);
        } else {
            $result = array();

            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '暂无适配班种', 'result' => $result);
        }

        ajax_return($res, $request['language_type']);
    }

    //活动班种适配表 -- 活动班种批量适配操作
    function batchActCoursecatAction(){
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        //model
        $Model = new \Model\Gmc\ActivityCrmModel($request);
        $dataList = $Model->batchActCoursecatAction($request);

        $result = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $dataList);
        ajax_return($result,$request['language_type']);
    }

    //活动学校适配表 -- 活动学校批量适配操作
    function batchSchoolAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $edwhere = "1";

        if (isset($request['SchoolidList']) && count($request['SchoolidList']) > 0) {
            $request['SchoolidList'] = stripslashes($request['SchoolidList']);
            $request['SchoolidList'] = json_decode($request['SchoolidList'], true);
            $chooseid = "";
            foreach ($request['SchoolidList'] as $chick_var) {
                $chooseid .= "'{$chick_var['school_id']}',";
            }
            $idrange = substr($chooseid, 0, -1);
            $edwhere .= " and school_id in ({$idrange})";
        } else {
            ajax_return(array('error' => 1, 'errortip' => "未选择学校!", "bakfuntion" => "errormotify"));
        }
        $activityOne = $this->DataControl->selectOne("SELECT * FROM crm_sell_activity WHERE activity_id = '{$request['activity_id']}' and company_id = '{$request['company_id']}' ");
        if ($request['BatchType'] == '1') {
            $copylist = $this->DataControl->getList("smc_school", $edwhere);
            if ($copylist) {
                foreach ($copylist as $copyvar) {
                    if (!$this->DataControl->getOne("crm_sell_activity_school", "activity_id='{$request['activity_id']}' and school_id='{$copyvar['school_id']}' and company_id = '{$request['company_id']}' ")) {
                        $data = array();
                        $data['company_id'] = $request['company_id'];
                        $data['activity_id'] = $activityOne['activity_id'];
                        $data['school_id'] = $copyvar['school_id'];
                        $this->DataControl->insertData("crm_sell_activity_school", $data);
                    }
                }
            }
            ajax_return(array('error' => 0, 'errortip' => "适配操作成功!", "bakfuntion" => "refreshpage"));
        } elseif ($request['BatchType'] == '2') {
            $copylist = $this->DataControl->getList("smc_school", $edwhere);
            if ($copylist) {
                foreach ($copylist as $copyvar) {
                    $this->DataControl->delData('crm_sell_activity_school', "activity_id='{$request['activity_id']}' and school_id='{$copyvar['school_id']}'  and company_id = '{$request['company_id']}' ");
                }
            }
            ajax_return(array('error' => 0, 'errortip' => "取消适配操作成功!", "bakfuntion" => "refreshpage"));
        } else {
            ajax_return(array('error' => 1, 'errortip' => "操作学校不存在!", "bakfuntion" => "errormotify"));
        }
    }

    //活动创建人管理
    function ActivityFounderApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        //model
        $Model = new \Model\Gmc\ActivityCrmModel($request);
        $dataList = $Model->ActivityFounderApi($request);

        $field = array();
        $field["staffer_id"] = "职工id";
        $field["staffer_cnname"] = "中文名";
        $field["staffer_enname"] = "英文名";
        $field["staffer_branch"] = "职工编号";

        $result = array();
        if ($dataList) {
            $result["field"] = $field;
            $result["data"] = $dataList;
            $res = array('error' => '0', 'errortip' => '活动创建人', 'result' => $result);
        } else {
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '活动创建人', 'result' => $result);
        }

        ajax_return($res, $request['language_type']);
    }

    //招生活动管理  --  活动课包
    function activityPackageListApi()
    {
        $request = Input('get.', '', 'strip_tags,addslashes');
        $this->ThisVerify($request);//验证账户
        //model
        $Model = new \Model\Crm\SellgoalModel($request);
        $dataList = $Model->activityPackageListApi($request);

        //字段JSON获取
        $field = array();
        $field["package_id"] = "课包id";
        $field["package_type"] = "课包类型 0 错误 1 有赞";
        $field["package_name"] = "课包名称";
        $field["package_branch"] = "课包编号";
        $field["package_outurl"] = "课包外链";
        $field["package_outtype"] = "外链类型   0 小程序  1 html5";
        $field["package_appid"] = "小程序ID";

        $result = array();
        if ($dataList) {
            $result["field"] = $field;
            $result["list"] = $dataList;
            $res = array('error' => '0', 'errortip' => '课包列表', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => '1', 'errortip' => '暂无课包列表', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }
    //删除某个招生活动
    function delGoalActivityAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        if (!$request['activity_id']) {
            $res = array('error' => '1', 'errortip' => "招生活动id有误", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        $a = $this->DataControl->getFieldOne("crm_sell_activity", "staffer_id", "activity_id = '{$request['activity_id']}'");
        if ($a['staffer_id'] == $request['staffer_id']) {
            $b = '1';
        } else {
            $b = '0';
        }
        if ($request['re_postbe_id'] !== '0' && $b == '0') {
            ajax_return(array('error' => 1, 'errortip' => "没有删除权限"));
        }
        if ($this->DataControl->getFieldOne("crm_client", "client_id", "activity_id='{$request['activity_id']}' ")) {
            $res = array('error' => '1', 'errortip' => "该活动已产生有效名单，不予删除", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        $stafferOne = $this->DataControl->selectOne("select s.account_class from smc_staffer as s  where s.company_id = '{$request['company_id']}' and s.staffer_id = '{$request['staffer_id']}'  ");
        if ($stafferOne['account_class'] != '1') {
            if (!$this->DataControl->getFieldOne("crm_sell_activity", "activity_id", "activity_id='{$request['activity_id']}' and staffer_id='{$request['staffer_id']}'")) {
                $res = array('error' => '1', 'errortip' => "您不是创建人，没有权限删除", 'result' => array());
                ajax_return($res, $request['language_type']);
            }
        }
        $Model = new \Model\Crm\SellgoalModel($request);
        $dataList = $Model->delComGoalActivityAction($request['activity_id'], $request['staffer_id'], $stafferOne['account_class'], $request['company_id']);
        if ($dataList) {
            $res = array('error' => '0', 'errortip' => "删除成功", 'result' => array());
        } else {
            $res = array('error' => '1', 'errortip' => "删除失败", 'result' => array());
        }
        ajax_return($res, $request['language_type']);
    }


    //活动来源未分配名单信息
    function ActivityClientView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        //model
        $Model = new \Model\Gmc\ActivityCrmModel($request);
        $dataList = $Model->ActivityClient($request);


        $field = array();

        $key = 0;
        $field[$key]["fieldname"] = "client_id";
        $field[$key]["fieldstring"] = "有效名单id";
        $field[$key]["show"] = 0;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "channel_id";
        $field[$key]["fieldstring"] = "渠道";
        $field[$key]["show"] = 0;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "activity_id";
        $field[$key]["fieldstring"] = "活动id";
        $field[$key]["show"] = 0;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "activity_name";
        $field[$key]["fieldstring"] = "活动名称";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "client_cnname";
        $field[$key]["fieldstring"] = "中文名";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "client_enname";
        $field[$key]["fieldstring"] = "英文名";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "client_age";
        $field[$key]["fieldstring"] = "年龄";
        $field[$key]["show"] = 0;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "client_sex";
        $field[$key]["fieldstring"] = "性别";
        $field[$key]["show"] = 0;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "client_source";
        $field[$key]["fieldstring"] = "来源渠道";
        $field[$key]["show"] = 0;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "frommedia_name";
        $field[$key]["fieldstring"] = "活动渠道类型";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "channel_name";
        $field[$key]["fieldstring"] = "活动渠道明细";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "client_mobile";
        $field[$key]["fieldstring"] = "手机号";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "client_address";
        $field[$key]["fieldstring"] = "地址";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "marketer_name";
        $field[$key]["fieldstring"] = "录入人";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "issignup";
        $field[$key]["fieldstring"] = "是否报名";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "isaudition";
        $field[$key]["fieldstring"] = "是否试听";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "principalstr";
        $field[$key]["fieldstring"] = "负责人";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;


        $field[$key]["fieldname"] = "client_status";
        $field[$key]["fieldstring"] = "客户状态";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "client_remark";
        $field[$key]["fieldstring"] = "名单备注";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "client_createtime";
        $field[$key]["fieldstring"] = "创建时间";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "client_updatetime";
        $field[$key]["fieldstring"] = "更新时间";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "client_tmkbatch";
        $field[$key]["fieldstring"] = "批次编号";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "isbranch";
        $field[$key]["fieldstring"] = "是否分配 1分配 -1未分配";
        $field[$key]["show"] = 0;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "school_cnname";
        $field[$key]["fieldstring"] = "已分配校园";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "client_gmcmarket";
        $field[$key]["fieldstring"] = "集团电销人员";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "shengshiqu";
        $field[$key]["fieldstring"] = "省市区";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;


        $result = array();
        if ($dataList) {
            $result["field"] = $field;
            $result["list"] = ($dataList['datalist'] == '') ? array() : $dataList['datalist'];
            if ($result["list"]) {
                $res = array('error' => '0', 'errortip' => '活动有效名单管理', 'allnum' => $dataList['count'], 'result' => $result);
            } else {
                $res = array('error' => '1', 'errortip' => '暂无活动有效名单管理', 'allnum' => 0, 'result' => $result);
            }
        } else {
            $result["list"] = array();
            $res = array('error' => '1', 'errortip' => '暂无活动有效名单管理', 'allnum' => 0, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //活动来源未分配名单信息 学校适配表
    function ActivityClientSchoolApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        if (!isset($request['activity_id']) || $request['activity_id'] == '') {
            $res = array('error' => '1', 'errortip' => '对应活动必选', 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        if (!isset($request['client_id']) || $request['client_id'] == '') {
            $res = array('error' => '1', 'errortip' => '未分配名单信息必选', 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        if (!isset($request['adaptive']) || $request['adaptive'] == '') {
            $res = array('error' => '1', 'errortip' => '筛选类型必须选择', 'result' => array());
            ajax_return($res, $request['language_type']);
        }

        //model
        $Model = new \Model\Gmc\ActivityCrmModel($request);
        $dataList = $Model->ActivityClientSchoolApi($request);

        $field = array();
        $field[0]["fieldname"] = "school_id";
        $field[0]["fieldstring"] = "学校id";
        $field[0]["show"] = 0;
        $field[0]["custom"] = 0;

        $field[1]["fieldname"] = "school_branch";
        $field[1]["fieldstring"] = "校区编号";
        $field[1]["show"] = 1;
        $field[1]["custom"] = 0;

        $field[2]["fieldname"] = "school_cnname";
        $field[2]["fieldstring"] = "校园名称";
        $field[2]["show"] = 1;
        $field[2]["custom"] = 0;

        $field[3]["fieldname"] = "school_enname";
        $field[3]["fieldstring"] = "检索代码";
        $field[3]["show"] = 1;
        $field[3]["custom"] = 0;

        $field[4]["fieldname"] = "school_typename";
        $field[4]["fieldstring"] = "学校类型";
        $field[4]["show"] = 0;
        $field[4]["custom"] = 0;

        $field[5]["fieldname"] = "district_cnname";
        $field[5]["fieldstring"] = "所在区域";
        $field[5]["show"] = 1;
        $field[5]["custom"] = 0;

        $field[6]["fieldname"] = "adaptive";
        $field[6]["fieldstring"] = "是否适配";
        $field[6]["show"] = 0;
        $field[6]["custom"] = 0;

        if ($dataList) {
            $result = array();
            $result["field"] = $field;
            if (is_array($dataList['datalist'])) {
                $result["district"] = $dataList['district'];
                $result["data"] = $dataList['datalist'];
            } else {
                $result["district"] = $dataList['district'];
                $result["data"] = array();
            }
            $res = array('error' => '0', 'errortip' => "学校适配表", 'allnum' => $dataList['count'], 'result' => $result);
        } else {
            $result = array();
            $result["district"] = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '学校适配表', 'result' => $result);
        }

        ajax_return($res, $request['language_type']);
    }

    //渠道来源未分配名单信息 学校适配表
    function ChannelClientSchoolApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        if (!isset($request['client_id']) || $request['client_id'] == '') {
            $res = array('error' => '1', 'errortip' => '未分配名单信息必选', 'result' => array());
            ajax_return($res, $request['language_type']);
        }

        //model
        $Model = new \Model\Gmc\ActivityCrmModel($request);
        $dataList = $Model->ChannelClientSchoolApi($request);

        $field = array();
        $field[0]["fieldname"] = "school_id";
        $field[0]["fieldstring"] = "学校id";
        $field[0]["show"] = 0;
        $field[0]["custom"] = 0;

        $field[1]["fieldname"] = "school_branch";
        $field[1]["fieldstring"] = "校区编号";
        $field[1]["show"] = 1;
        $field[1]["custom"] = 0;

        $field[2]["fieldname"] = "school_cnname";
        $field[2]["fieldstring"] = "校园名称";
        $field[2]["show"] = 1;
        $field[2]["custom"] = 0;

        $field[3]["fieldname"] = "school_enname";
        $field[3]["fieldstring"] = "检索代码";
        $field[3]["show"] = 1;
        $field[3]["custom"] = 0;

        $field[4]["fieldname"] = "district_cnname";
        $field[4]["fieldstring"] = "所在区域";
        $field[4]["show"] = 1;
        $field[4]["custom"] = 0;

        if ($dataList) {
            $result = array();
            $result["field"] = $field;
            if (is_array($dataList['datalist'])) {
                $result["district"] = $dataList['district'];
                $result["data"] = $dataList['datalist'];
            } else {
                $result["district"] = $dataList['district'];
                $result["data"] = array();
            }
            $res = array('error' => '0', 'errortip' => "学校适配表", 'allnum' => $dataList['count'], 'result' => $result);
        } else {
            $result = array();
            $result["district"] = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '学校适配表', 'result' => $result);
        }

        ajax_return($res, $request['language_type']);
    }

    //为分配名单所对应的活动
    function UnClientActivityView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        //model
        $Model = new \Model\Gmc\ActivityCrmModel($request);
        $dataList = $Model->UnClientActivity($request);

        $field = array();
        $field["activity_name"] = "招生活动id";
        $field["activity_id"] = "招生活动名称";

        $result = array();
        if ($dataList) {
            $result["field"] = $field;
            if ($dataList['datalist'] == '') {
                $result["list"] = array();
            } else {
                $result["list"] = $dataList['datalist'];
            }
            $res = array('error' => '0', 'errortip' => '活动名单', 'allnum' => $dataList['count'], 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => '1', 'errortip' => '活动名单', 'allnum' => array(), 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //为分配名单学校适配表
    function batchClientSchoolAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $edwhere = " 1";

        if (!$request['client_id']) {
            ajax_return(array('error' => 1, 'errortip' => "请选择客户!", "bakfuntion" => "errormotify"));
        }
        if ($request['BatchType'] == '1') {
            if ($this->DataControl->getFieldOne("crm_client_schoolenter", "schoolenter_id", "client_id='{$request['client_id']}'")) {
                ajax_return(array('error' => 1, 'errortip' => "暂时只支持分配进入一所学校,该客户已被分配!", "bakfuntion" => "errormotify"));
            }
        }

        if (isset($request['SchoolidList']) && count($request['SchoolidList']) > 0) {
            $request['SchoolidList'] = stripslashes($request['SchoolidList']);
            $request['SchoolidList'] = json_decode($request['SchoolidList'], true);
            if ($request['BatchType'] == '1') {
                if (count($request['SchoolidList']) >= 2) {
                    ajax_return(array('error' => 1, 'errortip' => "暂时只支持分配进入一所学校!", "bakfuntion" => "errormotify"));
                }
            }
            $chooseid = "";
            foreach ($request['SchoolidList'] as $chick_var) {
                $chooseid .= "'{$chick_var['school_id']}',";
            }
            $idrange = substr($chooseid, 0, -1);
            $edwhere .= " and school_id in ({$idrange})";
        } else {
            ajax_return(array('error' => 1, 'errortip' => "未选择学校!", "bakfuntion" => "errormotify"));
        }

        $clientOne = $this->DataControl->selectOne("SELECT * FROM crm_client WHERE client_id = '{$request['client_id']}' and company_id = '{$request['company_id']}' ");
        if ($request['BatchType'] == '1') {//适配
            $copylist = $this->DataControl->getList("smc_school", $edwhere);
            if ($copylist) {
                foreach ($copylist as $copyvar) {
                    if (!$this->DataControl->getOne("crm_client_schoolenter", "client_id='{$request['client_id']}' and school_id='{$copyvar['school_id']}' and company_id = '{$request['company_id']}' "))
                    {
                        $data = array();
                        $data['company_id'] = $request['company_id'];
                        $data['client_id'] = $clientOne['client_id'];
                        $data['school_id'] = $copyvar['school_id'];
                        $data['is_schoolenter'] = 0;
                        $data['schoolenter_createtime'] = time();
                        $data['schoolenter_updatetime'] = time();
                        $this->DataControl->insertData('crm_client_schoolenter', $data);
                    }
                }

                //新增跟进记录
                $markertOne = $this->DataControl->getFieldOne('crm_marketer', 'marketer_id,marketer_name', "staffer_id='{$request['staffer_id']}'");
                if ($markertOne) {
                    $trackData = array();
                    $trackData['client_id'] = $clientOne['client_id'];
                    $trackData['marketer_id'] = $markertOne['marketer_id'];
                    $trackData['marketer_name'] = $markertOne['marketer_name'];
                    $trackData['track_intention_level'] = "3";
                    $trackData['track_linktype'] = "集团分配";
                    $trackData['track_note'] = "由于名单未分配校园，由集团工作人员初步筛选客户意向，分配校园进行跟踪。";
                    $trackData['track_createtime'] = time();
                    $trackData['track_type'] = 1;
                    $trackData['track_initiative'] = 0;
                    $this->DataControl->insertData('crm_client_track', $trackData);
                } else {
                    $trackData = array();
                    $trackData['client_id'] = $clientOne['client_id'];
                    $trackData['marketer_id'] = 0;
                    $trackData['marketer_name'] = "系统";
                    $trackData['track_intention_level'] = "3";
                    $trackData['track_linktype'] = "集团分配";
                    $trackData['track_note'] = "由于名单未分配校园，由集团工作人员初步筛选客户意向，分配校园进行跟踪。";
                    $trackData['track_createtime'] = time();
                    $trackData['track_type'] = 1;
                    $trackData['track_initiative'] = 0;
                    $this->DataControl->insertData('crm_client_track', $trackData);
                }
            }
            $channelOne = $this->DataControl->selectOne("select c.channel_quality from crm_client as t,crm_code_channel as c where t.client_id = '{$clientOne['client_id']}' and t.channel_id = c.channel_id limit 0,1 ");//channel_quality  0 默认毛名单 1 默认有效名单
            $clientData = array();
            $clientData['client_updatetime'] = time();
            if($channelOne['channel_quality'] == '1') {
                $clientData['client_isgross'] = 0;//不是毛名单
            }elseif($channelOne['channel_quality'] == '0' && $channelOne['channel_quality'] != '') {
                $clientData['client_isgross'] = 1;//是毛名单
            }else{
                $clientData['client_isgross'] = 0;//不是毛名单
            }
            $this->DataControl->updateData("crm_client", "client_id='{$request['client_id']}'", $clientData);


            $this->addGmcWorkLog($request['company_id'], $request['staffer_id'], "活动名单管理", '分配名单', dataEncode($request));
            ajax_return(array('error' => 0, 'errortip' => "适配操作成功!", "bakfuntion" => "refreshpage"));
        } elseif ($request['BatchType'] == '2') {//取消适配
            $copylist = $this->DataControl->getList("smc_school", $edwhere);
            if ($copylist) {
                foreach ($copylist as $copyvar) {
                    $schoolenter = array();
                    $schoolenter['is_enterstatus'] = '-1';
                    $schoolenter['schoolenter_updatetime'] = time();
                    $this->DataControl->updateData("crm_client_schoolenter", "client_id='{$request['client_id']}' and school_id = '{$copyvar['school_id']}'", $schoolenter);

                    //$this->DataControl->delData('crm_client_schoolenter', "client_id='{$request['client_id']}' and school_id='{$copyvar['school_id']}'  and company_id = '{$request['company_id']}' ");
                }
            }
            ajax_return(array('error' => 0, 'errortip' => "取消适配操作成功!", "bakfuntion" => "refreshpage"));
        } else {
            ajax_return(array('error' => 1, 'errortip' => "操作学校不存在!", "bakfuntion" => "errormotify"));
        }
    }

    //招生名单线索---分配--我要跟进  -- 权限时可能需要分开
    function batchAllotClientSchoolAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        //model
        $Model = new \Model\Gmc\ActivityCrmModel($request);
        $dataList = $Model->batchAllotClientSchool($request);

        if ($Model->error == 0) {
            $res = array('error' => '0', 'errortip' => "分配成功", 'result' => array());
        } else {
            $res = array('error' => '1', 'errortip' => $Model->errortip, 'result' => array());
        }

        ajax_return($res, $request['language_type']);
    }


    //删除某个招生活动
    function delClientActivityAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        if (!$request['client_id']) {
            $res = array('error' => '1', 'errortip' => "招生活动id有误", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        if ($request['re_postbe_id'] !== '0') {
            ajax_return(array('error' => 1, 'errortip' => "没有删除权限"));
        }
        $Model = new \Model\Gmc\ActivityCrmModel($request);
        $dataList = $Model->delClientActivityAction($request['client_id']);
        if ($dataList) {
            $res = array('error' => '0', 'errortip' => "删除成功", 'result' => array());
        } else {
            $res = array('error' => '1', 'errortip' => "删除失败", 'result' => array());
        }
        ajax_return($res, $request['language_type']);
    }

    /**
     * 作废
     */
    function nullifyClientActivityAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        if (!isset($request['client_id'])) {
            $res = array('error' => '1', 'errortip' => "招生活动id有误", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
//        if($request['re_postbe_id'] !== '0'){
//            ajax_return(array('error' => 1, 'errortip' => "没有作废权限"));
//        }
        $Model = new \Model\Gmc\ActivityCrmModel($request);
        $dataList = $Model->nullifyClientActivityAction($request);
        if ($dataList) {
            $this->addGmcWorkLog($request['company_id'], $request['staffer_id'], "活动名单管理", '作废名单', dataEncode($request));
            $res = array('error' => '0', 'errortip' => "作废成功", 'result' => array());
        } else {
            $res = array('error' => '1', 'errortip' => "作废失败", 'result' => array());
        }
        ajax_return($res, $request['language_type']);
    }
    /**
     * 批量作废
     */
    public function batchDeleteClientAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        if (!isset($request['clients_json'])) {
            $res = array('error' => '1', 'errortip' => "请选择客户!", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        $Model = new \Model\Gmc\ActivityCrmModel($request);
        $dataList = $Model->batchDeleteClient($request);
        if ($dataList) {
            $this->addGmcWorkLog($request['company_id'], $request['staffer_id'], "活动名单管理", '作废名单', dataEncode($request));
            $res = array('error' => '0', 'errortip' => "作废成功!", 'result' => array());
        } else {
            $res = array('error' => '1', 'errortip' => "作废失败:(", 'result' => array());
        }
        ajax_return($res, $request['language_type']);
    }

    //招生目标 -- >> 招生模板
    function ActivitytempApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        //model
        $Model = new \Model\Gmc\ActivityCrmModel($request);
        $dataList = $Model->ActivitytempApi($request);

        //字段JSON获取
        $field = array();
        $field["activitytemp_id"] = "招生模板ID";
        $field["activitytemp_name"] = "招生模板名称";
        $field["activitytemp_styleimg"] = "招生模板图片查看";
        $field["activitytemp_bannerimg"] = "招生模板banner示意查看";
        $field["activitytemp_url"] = "招生地址前缀";

        $result = array();
        if ($dataList) {
            $result["field"] = $field;
            $result["data"] = $dataList;
            if ($result["data"]) {
                $res = array('error' => '0', 'errortip' => '活动招生模板', 'result' => $result);
            } else {
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '暂无活动招生模板', 'result' => $result);
            }
        } else {
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '暂无活动招生模板', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //招生目标 -- >> 某个活动模板的详细情况
    function ActivitytempOneApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        if (!isset($request['activitytemp_id']) || $request['activitytemp_id'] == '') {
            $res = array('error' => '1', 'errortip' => '模板id必选', 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        //model
        $Model = new \Model\Gmc\ActivityCrmModel($request);
        $dataList = $Model->ActivitytempOneApi($request);

        $field = array();
        $field["activitytemp_id"] = "招生模板ID";
        $field["activitytemp_name"] = "招生模板名称";
        $field["activitytemp_styleimg"] = "招生模板图片查看";
        $field["activitytemp_bannerimg"] = "招生模板banner示意查看";
        $field["activitytemp_url"] = "招生地址前缀";

        $result = array();
        if ($dataList) {
            $result["field"] = $field;
            $result["data"] = $dataList;
            if ($result["data"]) {
                $res = array('error' => '0', 'errortip' => '某个模板的详细情况', 'result' => $result);
            } else {
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '暂无某个模板的详细情况', 'result' => $result);
            }
        } else {
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '暂无某个模板的详细情况', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function getChannelApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Gmc\CrmModel($request);
        $dataList = $Model->getChannelApi($request);
        $res = array('error' => '0', 'errortip' => '获取成功', 'result' => $dataList);
        ajax_return($res, $request['language_type']);

    }

    function isChanneLUseAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Gmc\CrmModel($request);
        $dataList = $Model->isChanneLUseAction($request);
        $res = array('error' => '0', 'errortip' => '修改成功', 'result' => $dataList);
        ajax_return($res, $request['language_type']);
    }


    function toEffectiveAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Gmc\CrmModel($request);
        $dataList = $Model->toEffectiveAction($request);
        $this->addGmcWorkLog($request['company_id'], $request['staffer_id'], "有效名单管理->转为效名单", '转为有效名单', dataEncode($request));
        $res = array('error' => '0', 'errortip' => '获取成功', 'result' => $dataList);
        ajax_return($res, $request['language_type']);
    }

    function delNoEffectiveAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Gmc\CrmModel($request);
        $bool = $Model->delNoEffectiveAction($request);
        $this->addGmcWorkLog($request['company_id'], $request['staffer_id'], "有效名单管理->作废名单", '删除作废名单', dataEncode($request));
        if ($bool) {
            $res = array('error' => '0', 'errortip' => '删除成功', 'result' => array());
        } else {
            $res = array('error' => '1', 'errortip' => '删除失败', 'result' => array());
        }

        ajax_return($res, $request['language_type']);
    }

    //未到访原因列表
    function isvisitreasonListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\CrmModel($request);

        $result = $this->Model->isvisitreasonList($request);
        ajax_return($result, $request['language_type']);
    }

    //编辑未到访原因
    function updateIsvisitreasonAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\CrmModel($request);

        $result = $this->Model->updateIsvisitreasonAction($request);
        ajax_return($result, $request['language_type']);
    }

    //添加未到访原因
    function addIsvisitreasonAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\CrmModel($request);

        $result = $this->Model->addIsvisitreasonAction($request);
        ajax_return($result, $request['language_type']);
    }

    //删除未到访原因
    function delIsvisitreasonAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\CrmModel($request);

        $result = $this->Model->delIsvisitreasonAction($request);
        ajax_return($result, $request['language_type']);
    }

    //附近学校管理列表
    function nearschoolListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\CrmModel($request);

        $result = $this->Model->nearschoolList($request);
        ajax_return($result, $request['language_type']);
    }

    //启用/不启用
    function nearschoolStatusAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\CrmModel($request);

        $result = $this->Model->nearschoolStatusAction($request);
        ajax_return($result, $request['language_type']);
    }

    //删除附近学校
    function delnearschoolAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\CrmModel($request);

        $result = $this->Model->delnearschoolAction($request);
        ajax_return($result, $request['language_type']);
    }

    //添加附近学校
    function addnearschoolAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\CrmModel($request);

        $result = $this->Model->addnearschoolAction($request);
        ajax_return($result, $request['language_type']);
    }

    //编辑附近学校
    function updatenearschoolAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\CrmModel($request);

        $result = $this->Model->updatenearschoolAction($request);
        ajax_return($result, $request['language_type']);
    }

    //其他参数设置 -

    function getCrmOtherSettingApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\CrmModel($request);
        $result = $this->Model->getCrmOtherSettingApi();
        $res = array('error' => '0', 'errortip' => '获取成功', 'result' => $result);
        ajax_return($res, $request['language_type']);
    }

    //编辑其他参数
    function editCrmOtherSettingAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\CrmModel($request);
        $this->Model->editCrmOtherSettingAction($request);
        $res = array('error' => $this->Model->error, 'errortip' =>$this->Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }



    //客户标签列表
    function labelTypeView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\CrmModel($request);

        $result = $this->Model->getLabelTypeList($request);
        ajax_return($result, $request['language_type']);
    }

    //检查子项是否重复
    function checkRepeatLabelApi(){
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\CrmModel($request);
        $result = $this->Model->checkRepeatLabelApi($request);
        ajax_return($result, $request['language_type']);
    }

    //标签明细列表
    function labelDetailView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\CrmModel($request);

        $result = $this->Model->labelDetail($request);
        ajax_return($result, $request['language_type']);
    }

    //添加客户标签
    function addLabelTypeAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\CrmModel($request);

        $result = $this->Model->addLabelTypeAction($request);
        ajax_return($result, $request['language_type']);
    }

    //编辑客户标签
    function updateLabelTypeAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\CrmModel($request);

        $result = $this->Model->updateLabelTypeAction($request);
        ajax_return($result, $request['language_type']);
    }

    //删除客户标签
    function delLabelTypeAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Gmc\CrmModel($request);

        $result = $this->Model->delLabelTypeAction($request);
        ajax_return($result, $request['language_type']);
    }

    /**
     * 意向星级列表
     */
    public function intentionLevelListView()
    {
        $request = Input('GET.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $crmModel = new \Model\Gmc\CrmModel($request);
        $crmModel->intentionLevelList($request);

        $field = array();

        $k=0;
        $field[$k]["fieldstring"] = "intentlevel_starnum";
        $field[$k]["fieldname"] = "意向星级";
        $field[$k]["isLevel"] = 1;
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "intentlevel_remark";
        $field[$k]["fieldname"] = "级别说明";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "intentlevel_trackday_name";
        $field[$k]["fieldname"] = "跟踪频率（天）";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "intentlevel_warningday_name";
        $field[$k]["fieldname"] = "低跟进预警天数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "intentlevel_describe";
        $field[$k]["fieldname"] = "备注";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();

        $result["field"] = $field;
        $result["list"] = $crmModel->result;

        ajax_return(array('error' => $crmModel->error, 'errortip' => $crmModel->errortip, 'result' => $result));
    }

    /**
     * 编辑意向星级注释
     */
    public function editIntentionLevelAction()
    {
        $request = Input('POST.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $crmModel = new \Model\Gmc\CrmModel($request);
        $crmModel->editIntentionLevel($request);
        $result = array();
        $field = array();

        $result["field"] = $field;
        $result["list"] = $crmModel->result;

        ajax_return(array('error' => $crmModel->error, 'errortip' => $crmModel->errortip, 'result' => $result));

    }

    //变更模版下拉
    function changedemoListView()
    {
        $paramArray = Input('GET.', '', 'trim,addslashes');
        $this->ThisVerify($paramArray);//验证账户
        $sql = "
            SELECT
                c.changedemo_id,
                c.changedemo_reason
            FROM
                gmc_code_changedemo AS c 
            WHERE
                c.changedemo_class = '{$paramArray['changedemo_class']}' and c.company_id = '{$paramArray['company_id']}'";

        $changedemo = $this->DataControl->selectClear($sql);

        $field = array();
        $field["changedemo_id"] = "变更类型ID";
        $field["changedemo_reason"] = "模版内容";
        $field["changedemo_class"] = "模版类别0 无效变更 1 有效变更";

        $result = array();
        if ($changedemo) {
            $result["field"] = $field;
            $result["data"] = $changedemo;
            $res = array('error' => '0', 'errortip' => '获取变更模版下拉成功', 'result' => $result);
        } else {
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '获取变更模版下拉失败', 'result' => $result);
        }
        ajax_return($res);
    }


    //招生活动 -- >> 地推人员管理
    function getGroundPromotionView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Crm\SellgoalModel($request);
        $dataList = $Model->getGroundPromotion($request,1);

        $field = array();
        $key = 0;
        $field[$key]["fieldname"] = "promotion_id";
        $field[$key]["fieldstring"] = "地推人员ID";
        $field[$key]["show"] = 0;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "promotion_name";
        $field[$key]["fieldstring"] = "姓名";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $field[$key]["visible"] = 1;
        $key++;

        $field[$key]["fieldname"] = "promotion_jobnumber";
        $field[$key]["fieldstring"] = "地推工号";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $field[$key]["visible"] = 1;
        $key++;

        $field[$key]["fieldname"] = "school_type";
        $field[$key]["fieldstring"] = "所属";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $field[$key]["visible"] = 1;
        $key++;

        $field[$key]["fieldname"] = "promotion_type_name";
        $field[$key]["fieldstring"] = "地推类型";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $field[$key]["visible"] = 1;
        $key++;

        $field[$key]["fieldname"] = "promotion_mobile";
        $field[$key]["fieldstring"] = "联系电话";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $field[$key]["visible"] = 1;
        $key++;

        $field[$key]["fieldname"] = "promotion_bankcard";
        $field[$key]["fieldstring"] = "银行卡号";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "promotion_bank";
        $field[$key]["fieldstring"] = "开户行";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        /*$field[$key]["fieldname"] = "open_status";
        $field[$key]["fieldstring"] = "是否启用";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;*/

        $field[$key]["fieldname"] = "promotion_isprohibit";
        $field[$key]["fieldstring"] = "是否禁用";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $field[$key]["switch"] = 1;
        $key++;

        $field[$key]["fieldname"] = "client_allnum";
        $field[$key]["fieldstring"] = "毛名单数";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "client_allvalidnum";
        $field[$key]["fieldstring"] = "有效名单数";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "client_allvalid_rate";
        $field[$key]["fieldstring"] = "名单有效率";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "positivenum";
        $field[$key]["fieldstring"] = "缴费名单数";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;

        $result = array();
        $result["field"] = $field;
        if($dataList['datalist']){
            $result["list"] = $dataList['datalist'];
            $res = array('error' => '0', 'errortip' => '地推人员管理', 'allnum' => $dataList['count'], 'result' => $result);
        }else{
            $result["list"] = array();
            $res = array('error' => '1', 'errortip' => '暂无地推人员信息', 'allnum' => 0, 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }

    //招生活动 -- >> 添加地推人员
    function addGroundPromotionAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Crm\SellgoalModel($request);
        $dataList = $Model->addGroundPromotionAction($request,'',1);

        if($dataList){
            $res = array('error' => '0', 'errortip' => "添加地推人员成功",'result' => $Model->result);
            $this->addCrmWorkLog($request['company_id'],$request['school_id'],$request['marketer_id'],"招生活动管理->地推人员管理",'新增地推人员',dataEncode($request));
        }else{
            $res = array('error' => '1', 'errortip' => '您添加的人员与集团/他校地推人员重复');
        }
        ajax_return($res,$request['language_type']);
    }

    //招生活动 -- >> 编辑地推人员
    function editGroundPromotionAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Crm\SellgoalModel($request);
        $dataList = $Model->editGroundPromotionAction($request);

        if ($dataList) {
            $res = array('error' => '0', 'errortip' => "编辑地推人员成功", 'result' => array());
            $this->addCrmWorkLog($request['company_id'],$request['school_id'],$request['marketer_id'],"招生活动管理->地推人员管理",'编辑地推人员',dataEncode($request));
        } else {
            $res = array('error' => '1', 'errortip' => "编辑地推人员失败", 'result' => array());
        }
        ajax_return($res,$request['language_type']);
    }

    //招生活动 -- >> 活动移除地推人员
    function delGroundPromotionAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        if ($this->DataControl->delData("crm_sell_activity_promotion", "activity_id = '{$request['activity_id']}' and promotion_id = '{$request['promotion_id']}'")) {
            $res = array('error' => '0', 'errortip' => "删除成功", 'result' => array());
            $this->addCrmWorkLog($request['company_id'],$request['school_id'],$request['marketer_id'],"招生活动管理->招生活动管理",'移除地推人员',dataEncode($request));
        } else {
            $res = array('error' => '1', 'errortip' => "删除失败", 'result' => array());
        }
        ajax_return($res,$request['language_type']);
    }

    //招生活动 -- >> 启用/禁用地推人员
    function openGroundPromotionAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        if(!$request['school_id']){
            $request['school_id'] = 0;
        }

        if($request['open_status']){
            $tip = "启用";
        }else{
            $tip = "禁用";
        }
        $where = "company_id = '{$request['company_id']}' and school_id = '{$request['school_id']}' and promotion_id = '{$request['promotion_id']}'";

        if ($this->DataControl->updateData("crm_ground_promotion_open", $where, array("open_status" => $request['open_status']))) {
            //修改主表的更新时间
            $this->DataControl->updateData("crm_ground_promotion", "promotion_id = '{$request['promotion_id']}'", array("promotion_updatetime" => time()));
            $res = array('error' => '0', 'errortip' => "启用成功", 'result' => array());
            $this->addCrmWorkLog($request['company_id'],$request['school_id'],$request['marketer_id'],"招生活动管理->招生活动管理","{$tip}地推人员",dataEncode($request));
        } else {
            $res = array('error' => '1', 'errortip' => "启用失败", 'result' => array());
        }

        ajax_return($res,$request['language_type']);
    }

    //招生管理 -- >> 禁用/不禁用地推人员
    function disableGroundPromotionAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        if($request['promotion_isprohibit']){
            $tip = "禁用";
        }else{
            $tip = "不禁用";
        }

        $data = array();
        $data['promotion_isprohibit'] = $request['promotion_isprohibit'];
        $data['promotion_updatetime'] = time();
        if ($this->DataControl->updateData("crm_ground_promotion", "promotion_id = '{$request['promotion_id']}'", $data)) {
            $res = array('error' => '0', 'errortip' => "修改成功", 'result' => array());
            $this->addCrmWorkLog($request['company_id'],$request['school_id'],$request['marketer_id'],"招生活动管理->招生活动管理","{$tip}地推人员",dataEncode($request));
        } else {
            $res = array('error' => '1', 'errortip' => "修改失败", 'result' => array());
        }

        ajax_return($res,$request['language_type']);
    }


    //招生活动 -- >> 地推二维码
    function GroundPromotionQRcodeApi()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Crm\SellgoalModel($request);
        $dataList = $Model->GroundPromotionQRcode($request);

        $result = array();
        if($dataList){
            $result["list"] = $dataList;
            $res = array('error' => '0', 'errortip' => '地推二维码', 'result' => $result);
        }else{
            $result["list"] = array();
            $res = array('error' => '1', 'errortip' => '暂无地推二维码', 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }

    //查询地推人员是否重复
    function getPromotionView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        if (isset($request['promotion_mobile']) && $request['promotion_mobile'] !== '') {
            $promotionOne = $this->DataControl->getFieldOne("crm_ground_promotion", "promotion_name", "company_id = '{$request['company_id']}' and school_id = '0' and promotion_mobile = '{$request['promotion_mobile']}'");
            $promotionTwo = $this->DataControl->getFieldOne("crm_ground_promotion", "promotion_name", "company_id = '{$request['company_id']}' and school_id <> '0' and promotion_mobile = '{$request['promotion_mobile']}'", "ORDER BY promotion_mobile DESC,promotion_id DESC");
        }

        if (isset($request['promotion_jobnumber']) && $request['promotion_jobnumber'] !== '') {
            $promotionOne = $this->DataControl->getFieldOne("crm_ground_promotion", "promotion_name", "company_id = '{$request['company_id']}' and school_id = '0' and promotion_jobnumber = '{$request['promotion_jobnumber']}'");
            $promotionTwo = $this->DataControl->getFieldOne("crm_ground_promotion", "promotion_name", "company_id = '{$request['company_id']}' and school_id <> '0' and promotion_jobnumber = '{$request['promotion_jobnumber']}'", "ORDER BY promotion_jobnumber DESC,promotion_id DESC");
        }

        if ($promotionOne) {
            $res = array('error' => '1', 'errortip' => "本集团人员重复", 'result' => array());
        } elseif ($promotionTwo) {
            $res = array('error' => '2', 'errortip' => "他校人员重复", 'result' => array("promotion_name" => $promotionTwo['promotion_name']));
        } else {
            $res = array('error' => '0', 'errortip' => "暂无重复数据", 'result' => array());
        }
        ajax_return($res,$request['language_type']);
    }


    //招生人员管理
    function getRecruiterView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Gmc\CrmModel($request);
        $res = $ReportModel->getRecruiter($request);

        $k = 0;
        $field[$k]["fieldname"] = "marketer_id";
        $field[$k]["fieldstring"] = "负责人ID";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;

        $k++;
        $field[$k]["fieldname"] = "staffer_cnname";
        $field[$k]["fieldstring"] = "负责人中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldname"] = "staffer_enname";
        $field[$k]["fieldstring"] = "负责人英文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldname"] = "post_name";
        $field[$k]["fieldstring"] = "集团职务";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldname"] = "leave_name";
        $field[$k]["fieldstring"] = "在职状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldname"] = "client_num";
        $field[$k]["fieldstring"] = "负责人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res['list']) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '招生人员管理', 'result' => $result, 'allnum' => $res['allnum']);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => "暂无招生人员管理", 'result' => $result, 'allnum' => $res['allnum']);
        }
        ajax_return($res, $request['language_type']);
    }


    //名单交接
    function migrationCrmApi()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        if (!$request['staffer_id']) {
            $res = array('error' => '1', 'errortip' => "操作人有误", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        if (!$request['marketer_id']) {
            $res = array('error' => '1', 'errortip' => "被交接人有误", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        if (!$request['to_staffer_id']) {
            $res = array('error' => '1', 'errortip' => "请选择交接人", 'result' => array());
            ajax_return($res, $request['language_type']);
        }

        $markertOne = $this->DataControl->getFieldOne('crm_marketer', 'marketer_id', "staffer_id='{$request['staffer_id']}'");
        $markertTwo = $this->DataControl->getFieldOne('crm_marketer', 'marketer_id', "staffer_id='{$request['to_staffer_id']}'");
        if (!$request['school_id']) {
            $request['school_id'] = 0;
        }

        //交接负责人名单
        $migrationModel = new \Model\Crm\ClientModel();
        $result = $migrationModel->migrationClient($request['marketer_id'],$markertTwo['marketer_id'],$request['school_id'],$markertOne['marketer_id']);

        if($result){
            $this->addGmcWorkLog($request['company_id'], $request['staffer_id'], "招生管理中心->招生人员管理", '名单交接', dataEncode($request));
        }

        $res = array('error' => $migrationModel->error, 'errortip' => $migrationModel->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }


    //结尾魔术函数
    function __destruct()
    {

    }
}
