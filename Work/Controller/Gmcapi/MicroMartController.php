<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2019/12/13
 * Time: 10:28
 */

namespace Work\Controller\Gmcapi;

use Model\Gmc\MicroMartModel;

class MicroMartController extends viewTpl
{
    public $u;
    public $t;
    public $c;

    //预加载处理类
    function __construct($visitType = "api")
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
    }

    //本地权限校验入口
    function ThisVerify($request)
    {
        $paramArray = array();
        $paramArray['staffer_id'] = $request['staffer_id'];
        $paramArray['school_id'] = $request['school_id'];
        $paramArray['company_id'] = $request['company_id'];
        $paramArray['token'] = $request['token'];
        if (!$this->UserLimit($paramArray)) {
            $result = array();
            $result["list"] = array();
            $result["tokeninc"] = "0";
            $res = array('error' => '1', 'errortip' => "用户token失效", 'result' => $result);
            ajax_return($res);
        }
    }

    //商品管理
    function HomeView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new MicroMartModel($request);
        $result = $Model->GoodsList($request);

        ajax_return($result,$request['language_type']);
    }

    //添加商品
    function AddGoodsAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new MicroMartModel($request);
        $result = $Model->addGoodsApi($request);

        ajax_return($result,$request['language_type']);
    }

    //编辑页面
    function EditGoodsView(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new MicroMartModel($request);
        $result = $Model->editGoods($request);

        ajax_return($result,$request['language_type']);
    }

    //编辑商品
    function EditGoodsAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new MicroMartModel($request);
        $result = $Model->editGoodsApi($request);

        ajax_return($result,$request['language_type']);
    }

    //删除商品
    function DelGoodsAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new MicroMartModel($request);
        $result = $Model->delGoodsApi($request);

        ajax_return($result,$request['language_type']);
    }

    //批量上下架/是否热销
    function BatchOperateAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new MicroMartModel($request);
        $result = $Model->batchOperateApi($request);

        ajax_return($result,$request['language_type']);
    }

    //批量删除
    function BatchDelAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new MicroMartModel($request);
        $result = $Model->batchDelApi($request);

        ajax_return($result,$request['language_type']);
    }

    //添加适用学校
    function AddApplySchoolAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new MicroMartModel($request);
        $result = $Model->addApplySchool($request);

        ajax_return($result,$request['language_type']);
    }

    //移除适用学校
    function DelApplySchoolAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new MicroMartModel($request);
        $result = $Model->delApplySchool($request);

        ajax_return($result,$request['language_type']);
    }

    //单课程商品
    function TuitionGoodsView(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new MicroMartModel($request);
        $result = $Model->TuitionGoodsList($request);

        ajax_return($result,$request['language_type']);
    }

    //适用学校列表
    function ApplySchoolView(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new MicroMartModel($request);
        $result = $Model->applySchoolList($request);

        ajax_return($result,$request['language_type']);
    }

    //同步单课程商品
    function SyncTuitionGoodsAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new MicroMartModel($request);
        $result = $Model->SyncTuitionGoodsApi($request);

        ajax_return($result,$request['language_type']);
    }

    //组合课程商品
    function CoursepacksGoodsView(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new MicroMartModel($request);
        $result = $Model->CoursepacksGoodsList($request);

        ajax_return($result,$request['language_type']);
    }

    //适用课程列表
    function ApplyCourseView(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new MicroMartModel($request);
        $result = $Model->applySourseList($request);

        ajax_return($result,$request['language_type']);
    }

    //同步组合课程商品
    function SyncCoursepacksGoodsAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new MicroMartModel($request);
        $result = $Model->SyncCoursepacksGoodsApi($request);

        ajax_return($result,$request['language_type']);
    }

    //教材商品
    function BookGoodsView(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new MicroMartModel($request);
        $result = $Model->BookGoodsList($request);

        ajax_return($result,$request['language_type']);
    }

    //同步教材商品
    function SyncBookGoodsAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new MicroMartModel($request);
        $result = $Model->SyncBookGoodsApi($request);

        ajax_return($result,$request['language_type']);
    }

    //普通商品
    function OrdinaryGoodsView(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new MicroMartModel($request);
        $result = $Model->OrdinaryGoodsList($request);

        ajax_return($result,$request['language_type']);
    }

    //同步普通商品
    function SyncShopGoodsAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new MicroMartModel($request);
        $result = $Model->SyncShopGoodsApi($request);

        ajax_return($result,$request['language_type']);
    }

    //商品分类下拉列表
    function GetClassApi()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $datawhere = "company_id = '{$request['company_id']}' AND class_isopen = '1'";
        if(isset($request['sellgoods_type']) && $request['sellgoods_type'] !== ''){
            $datawhere .= " and class_type = '{$request['sellgoods_type']}'";
        }

        $sql = "SELECT class_id,class_name FROM shop_code_class WHERE {$datawhere}";
        $ClassList = $this->DataControl->selectClear($sql);
        if (!$ClassList) {
            $ClassList = array();
        }

        $result["list"] = $ClassList;
        $res = array('error' => 0, 'errortip' => '获取商品分类下拉列表', 'result' => $result);

        ajax_return($res,$request['language_type']);
    }

    //商品二级分类下拉列表
    function GetCategoryApi()
    {
        $request = Input('get.','','trim,addslashes');
		$this->ThisVerify($request);//验证账户

        $datawhere = "company_id = '{$request['company_id']}'";
        if(isset($request['class_id']) && $request['class_id'] != ''){
            $datawhere .= " and category_class = '{$request['class_id']}'";
        }

        $sql = "SELECT category_id,category_name FROM shop_code_category WHERE {$datawhere}";
        $CategoryList = $this->DataControl->selectClear($sql);
        if (!$CategoryList) {
            $CategoryList = array();
        }

        $result["list"] = $CategoryList;
        $res = array('error' => 0, 'errortip' => '获取商品二级分类下拉列表', 'result' => $result);

        ajax_return($res,$request['language_type']);
    }

    //商品杂费下拉列表
    function GetFeeitemApi()
    {
        $request = Input('get.','','trim,addslashes');
		$this->ThisVerify($request);//验证账户


        $sql = "SELECT feeitem_cnname,feeitem_branch,feeitem_price FROM smc_code_feeitem WHERE company_id = '{$request['company_id']}' AND feeitem_expendtype = '2'";
        $FeeitemList = $this->DataControl->selectClear($sql);
        if (!$FeeitemList) {
            $FeeitemList = array();
        }

        $result["list"] = $FeeitemList;
        $res = array('error' => 0, 'errortip' => '获取商品杂费下拉列表', 'result' => $result);

        ajax_return($res,$request['language_type']);
    }

    //广告位管理
    function BannerListView(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new MicroMartModel($request);
        $result = $Model->BannerList($request);

        ajax_return($result,$request['language_type']);
    }

    //添加广告位
    function AddBannerAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new MicroMartModel($request);
        $result = $Model->addBanner($request);

        ajax_return($result,$request['language_type']);
    }

    //编辑广告位
    function EditBannerAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new MicroMartModel($request);
        $result = $Model->EditBanner($request);

        ajax_return($result,$request['language_type']);
    }

    //删除广告位
    function DelBannerAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new MicroMartModel($request);
        $result = $Model->delBanner($request);

        ajax_return($result,$request['language_type']);
    }
}