<?php

namespace Work\Controller\Gmcapi;


class ImportJdbTestController extends viewTpl
{
    public $u;
    public $t;
    public $c;

    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
    }
    //学员余额
    function ImportStubalanceView(){
        $Model = new \Model\SchoolTestModel('8888');
        $Model->ImportStubalance();
    }

    //学员课程余额
    function ImportStuCourseBalanceView(){
        $Model = new \Model\SchoolTestModel('8888');
        $Model->ImportStuCourseBalance();
    }

    //管理费
    function ImportMangepriceView(){
        $Model = new \Model\SchoolTestModel('8888');
        $Model->ImportMangeprice();
    }

    //班级详情
    function ImportClassView(){
        $Model = new \Model\SchoolTestModel('8888');
        $Model->ImportClass();
    }

    //班级学员在读详情
    function ImportStudyView(){
        $Model = new \Model\SchoolTestModel('8888');
        $Model->ImportStudy();
    }

    //学员欠费
    function ImportStuArrearsView(){
        $Model = new \Model\SchoolTestModel('8888');
        $Model->ImportStuArrears();
    }


    function GuideClassView(){
        $Model = new \Model\SchoolTestModel('8888');
        $Model->GuideClass();
    }

    function GuideStudyView(){
        $Model = new \Model\SchoolTestModel('8888');
        $Model->GuideStudy();
    }

    //导出学员课程别余额
    function GuideStuCourseBalanceView(){
        $Model = new \Model\SchoolTestModel('8888');
        $Model->GuideStuCourseBalance();
    }

    //导出学员预收
    function GuideStuAdvanceView(){
        $Model = new \Model\SchoolTestModel('8888');
        $Model->CourseCatBalance();
    }

    //导出学员异动
    function GuideStuChangeView(){
        $Model = new \Model\SchoolTestModel('8888');
        $Model->GuideStuChange();
    }

    function GuideStuCatBalanceView(){
        $Model = new \Model\SchoolTestModel('8888');
        $Model->GuideStuCatBalance();
    }


    function simulationAdvanceView(){
        $list=$this->DataControl->selectClear("select esc.student_branch,esc.coursecat_branch,cat.coursecat_id,esc.coursecatbalance_figure,esc.coursecat_time
                                                from smc_excel_studentcatbalance as esc
                                                left join smc_code_coursecat as cat on cat.coursecat_branch=esc.coursecat_branch
                                                where feetype_code='Times'
                                                group by esc.student_branch,esc.coursecat_branch");
        if($list){
            foreach($list as $one){
                $this->advanceAction($one);
            }

        }else{
            echo "无预收记录"."<br />";
        }
    }

    function advanceAction($one,$from=0){
        $hasnum=0;
        if($from==0){
            $courseOne=$this->DataControl->getFieldOne("smc_course","course_id,course_nextid,course_branch,course_classnum","coursecat_id='{$one['coursecat_id']}' and company_id='8888' order by course_id asc");
            $Model = new \Model\SchoolTestModel('8888');
            $course=$Model->simulationAdvance($one['student_branch'],$one['coursecat_branch'],'20190531');
            if($course){
                $courseOne=$this->DataControl->getFieldOne("smc_course","course_id,course_nextid,course_branch,course_classnum","course_branch='{$course['CODE_CLASS']}' and company_id='8888'");
                $hasnum=$course['TIMES_TQTY'];
//                $courseOne=$this->DataControl->getFieldOne("smc_course","course_id,course_nextid,course_branch","course_branch='LK03'");
            }
        }else{
            $courseOne=$this->DataControl->getFieldOne("smc_course","course_id,course_nextid,course_branch,course_classnum","course_id='{$one['course_nextid']}' and company_id='8888'");
        }

        if($courseOne['course_classnum']>0){
            if(($courseOne['course_classnum']-$hasnum)<=0){
                $hasnum=$courseOne['course_classnum'];
            }
            $day=date("Y-m-d",strtotime(substr($one['student_branch'],0,8)));
            $sql="SELECT
                t.course_id,
                p.pricing_id,
                t.tuition_originalprice,
                t.tuition_sellingprice,
                t.tuition_buypiece,
                t.tuition_unitprice,
                a.agreement_id,
                t.tuition_addtime
            FROM
                smc_fee_pricing_tuition AS t,
                smc_fee_pricing AS p,
                smc_fee_agreement AS a
            WHERE
                t.pricing_id = p.pricing_id
            AND p.agreement_id = a.agreement_id
            AND t.course_id = '{$courseOne['course_id']}'
            AND (
                (
                    p.pricing_applytype = '1'
                    AND p.pricing_id IN (
                        SELECT
                            pricing_id
                        FROM
                            smc_fee_pricing_apply AS a
                        WHERE
                            a.school_id = '01425'
                    )
                )
                OR (
                    p.pricing_applytype = '-1'
                    AND p.pricing_id NOT IN (
                        SELECT
                            pricing_id
                        FROM
                            smc_fee_pricing_apply AS a
                        WHERE
                            a.school_id = '01425'
                    )
                )
                OR (p.pricing_applytype = '0')
            )
            AND a.agreement_startday >= '{$day}'
            AND a.company_id = '8888'
            GROUP BY
                t.course_id
                order by a.agreement_startday asc
                ";
            $pricingOne=$this->DataControl->selectOne($sql);
            $data=array();
            $data['coursecatbalance_unitexpend']=ceil($one['coursecatbalance_figure']/$one['coursecat_time']);
            if($pricingOne){
                $data['coursecatbalance_unitrefund']=$pricingOne['tuition_unitprice'];
            }

            $data['student_branch']=$one['student_branch'];
            $data['coursecat_branch']=$one['coursecat_branch'];
            $data['course_branch']=$courseOne['course_branch'];

            if($one['coursecat_time']>($courseOne['course_classnum']-$hasnum)){
                $data['coursebalance_time']=$courseOne['course_classnum']-$hasnum;
                $data['coursebalance_figure']=$data['coursecatbalance_unitexpend']*($courseOne['course_classnum']-$hasnum);

                if($one['coursecatbalance_figure']<($data['coursecatbalance_unitexpend']*($courseOne['course_classnum']-$hasnum))){
                    $data['coursebalance_figure']=$one['coursecatbalance_figure'];
                }

                $surplusTime=$one['coursecat_time']-($courseOne['course_classnum']-$hasnum);
                $surplusFigure=$one['coursecatbalance_figure']-$data['coursebalance_figure'];
            }else{
                $data['coursebalance_time']=$one['coursecat_time'];
                $data['coursebalance_figure']=$data['coursecatbalance_unitexpend']*$one['coursecat_time'];
                if($one['coursecatbalance_figure']<($data['coursecatbalance_unitexpend']*$one['coursecat_time'])){
                    $data['coursebalance_figure']=$one['coursecatbalance_figure'];
                }
                $surplusTime=0;
                $surplusFigure=0;
            }
            if(($courseOne['course_classnum']-$hasnum)>0){
                $this->DataControl->insertData("smc_excel_simulation",$data);
            }

            if($surplusTime>0 && $surplusFigure>0 && $courseOne['course_nextid']>0){
                $return_data=array();
                $return_data['student_branch']=$one['student_branch'];
                $return_data['course_nextid']=$courseOne['course_nextid'];
                $return_data['coursecat_id']=$one['coursecat_id'];
                $return_data['coursecat_branch']=$one['coursecat_branch'];
                $return_data['coursecatbalance_figure']=$surplusFigure;
                $return_data['coursecat_time']=$surplusTime;
                $this->advanceAction($return_data,1);
            }
        }
    }





}