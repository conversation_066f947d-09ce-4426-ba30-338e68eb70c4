<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/13
 * Time: 23:25
 */

namespace Work\Controller\Gmcapi;


class ComplaintsController extends viewTpl
{
    public $u;
    public $t;
    public $c;
    public $visitType = "api";
    public $Model;

    //预加载处理类
    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
    }

    function ThisVerify($request){
        $paramArray = array();
        $paramArray['staffer_id'] = $request['staffer_id'];
        $paramArray['company_id'] = $request['company_id'];
        $paramArray['token'] = $request['token'];
        if(!$this->UserLimit($paramArray)){
            $result = array();
            $result["list"] = array();
            $result["tokeninc"] = "0";
            $res = array('error' => '1', 'errortip' => "用户token失效", 'result' => $result);
            ajax_return($res,$request['language_type']);
        }
    }

    //投诉分类管理
    function HomeView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model =  new \Model\Gmc\ComplaintsModel($request);

        $result = $this->Model->getComplaintsTypeList($request);

        ajax_return($result,$request['language_type']);
    }

    //添加投诉分类
    function addComplaintsTypeAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model =  new \Model\Gmc\ComplaintsModel($request);

        $result = $this->Model->addComplaintsType($request);
        ajax_return($result,$request['language_type']);
    }

    //编辑投诉分类
    function updateComplaintsTypeAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model =  new \Model\Gmc\ComplaintsModel($request);

        $result = $this->Model->updateComplaintsType($request);

        ajax_return($result,$request['language_type']);
    }

    //删除投诉分类
    function delComplaintsTypeAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model =  new \Model\Gmc\ComplaintsModel($request);

        $result = $this->Model->delComplaintsType($request);

        ajax_return($result,$request['language_type']);
    }

    //是否启用投诉分类
    function isOpenComplaintsAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model =  new \Model\Gmc\ComplaintsModel($request);

        $result = $this->Model->isOpenComplaints($request);

        ajax_return($result,$request['language_type']);
    }

    //投诉主题管理
    function getComplaintsThemeView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model =  new \Model\Gmc\ComplaintsModel($request);

        $result = $this->Model->getComplaintsTheme($request);

        ajax_return($result,$request['language_type']);
    }

    //添加投诉主题
    function addComplaintsThemeAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model =  new \Model\Gmc\ComplaintsModel($request);

        $result = $this->Model->addComplaintsTheme($request);
        ajax_return($result,$request['language_type']);
    }

    //编辑投诉主题
    function updateComplaintsThemeAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model =  new \Model\Gmc\ComplaintsModel($request);

        $result = $this->Model->updateComplaintsTheme($request);

        ajax_return($result,$request['language_type']);
    }

    //删除投诉主题
    function delComplaintsThemeAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model =  new \Model\Gmc\ComplaintsModel($request);

        $result = $this->Model->delComplaintsTheme($request);

        ajax_return($result,$request['language_type']);
    }

    //话术管理
    function getSpeechcraftView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model =  new \Model\Gmc\ComplaintsModel($request);

        $result = $this->Model->getSpeechcraft($request);

        ajax_return($result,$request['language_type']);
    }

    //添加话术
    function addSpeechcraftAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model =  new \Model\Gmc\ComplaintsModel($request);

        $result = $this->Model->addSpeechcraft($request);
        ajax_return($result,$request['language_type']);
    }

    //编辑话术
    function updateSpeechcraftAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model =  new \Model\Gmc\ComplaintsModel($request);

        $result = $this->Model->updateSpeechcraft($request);

        ajax_return($result,$request['language_type']);
    }

    //是否启用话术
    function isOpenSpeechcraftAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model =  new \Model\Gmc\ComplaintsModel($request);

        $result = $this->Model->isOpenSpeechcraft($request);

        ajax_return($result,$request['language_type']);
    }

    //删除话术
    function delSpeechcraftAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model =  new \Model\Gmc\ComplaintsModel($request);

        $result = $this->Model->delSpeechcraft($request);

        ajax_return($result,$request['language_type']);
    }

    //投诉分类下拉列表
    function getComplaintsApi()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $datawhere = "company_id = '{$request['company_id']}' and feedbacktype_status = '1'";

        $sql = "SELECT feedbacktype_id,feedbacktype_name,feedbacktype_belongs FROM ucs_code_feedbacktype WHERE {$datawhere}";
        $ComplaintsList = $this->DataControl->selectClear($sql);
        if (!$ComplaintsList) {
            $ComplaintsList = array();
        }

        $result["list"] = $ComplaintsList;
        $res = array('error' => 0, 'errortip' => '获取投诉分类下拉列表', 'result' => $result);

        ajax_return($res,$request['language_type']);
    }

    //投诉主题下拉列表
    function getComplaintsThemeApi()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $datawhere = "company_id = '{$request['company_id']}'";
        if(isset($request['feedbacktype_id']) && $request['feedbacktype_id'] != ''){
            $datawhere .= " and feedbacktype_id = '{$request['feedbacktype_id']}'";
        }

        $sql = "SELECT feedbacktheme_id,feedbacktheme_name FROM ucs_code_feedbacktheme WHERE {$datawhere}";
        $ComplaintsThemeList = $this->DataControl->selectClear($sql);
        if (!$ComplaintsThemeList) {
            $ComplaintsThemeList = array();
        }

        $result["list"] = $ComplaintsThemeList;
        $res = array('error' => 0, 'errortip' => '获取投诉主题下拉列表', 'result' => $result);

        ajax_return($res,$request['language_type']);
    }

    //结尾魔术函数
    function __destruct()
    {

    }
}
