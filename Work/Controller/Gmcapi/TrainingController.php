<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2019/12/13
 * Time: 10:28
 */

namespace Work\Controller\Gmcapi;

use Model\Gmc\TrainingModel;

class TrainingController extends viewTpl
{
    public $u;
    public $t;
    public $c;

    //预加载处理类
    function __construct($visitType = "api")
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
    }

    //本地权限校验入口
    function ThisVerify($request)
    {
        $paramArray = array();
        $paramArray['staffer_id'] = $request['staffer_id'];
        $paramArray['school_id'] = $request['school_id'];
        $paramArray['company_id'] = $request['company_id'];
        $paramArray['token'] = $request['token'];
        if (!$this->UserLimit($paramArray)) {
            $result = array();
            $result["list"] = array();
            $result["tokeninc"] = "0";
            $res = array('error' => '1', 'errortip' => "用户token失效", 'result' => $result);
            ajax_return($res);
        }
    }

    //职业体系管理
    function HomeView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new TrainingModel($request);
        $result = $Model->CareerSystemList($request);

        ajax_return($result,$request['language_type']);
    }

    //添加职业信息
    function AddCareerAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new TrainingModel($request);
        $result = $Model->addCareerApi($request);

        ajax_return($result,$request['language_type']);
    }

    //编辑职业信息
    function EditCareerAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new TrainingModel($request);
        $result = $Model->editCareerApi($request);

        ajax_return($result,$request['language_type']);
    }

    //删除职业信息
    function DelCareerAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new TrainingModel($request);
        $result = $Model->delCareerApi($request);

        ajax_return($result,$request['language_type']);
    }

    //职业类型下拉列表
    function GetCareertypeApi()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $datawhere = "company_id = '{$request['company_id']}'";
        if(isset($request['careertype_id']) && $request['careertype_id'] != ''){
            $datawhere .= " and careertype_id = '{$request['careertype_id']}'";
        }

        $sql = "SELECT careertype_id,careertype_cnname FROM eas_code_careertype WHERE {$datawhere}";
        $CompaniesList = $this->DataControl->selectClear($sql);
        if (!$CompaniesList) {
            $CompaniesList = array();
        }

        $result["list"] = $CompaniesList;
        $res = array('error' => 0, 'errortip' => '获取职业类型下拉列表', 'result' => $result);

        ajax_return($res,$request['language_type']);
    }

    //职业下拉列表
    function GetCareerApi()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $datawhere = "company_id = '{$request['company_id']}'";
        if(isset($request['careertype_id']) && $request['careertype_id'] != ''){
            $datawhere .= " and careertype_id = '{$request['careertype_id']}'";
        }
        if(isset($request['careerId']) && $request['careerId'] != ''){
            $datawhere .= " and career_id <> '{$request['careerId']}'";
        }

        $sql = "SELECT career_id,career_cnname,careertype_id FROM eas_career WHERE {$datawhere}";
        $CompaniesList = $this->DataControl->selectClear($sql);
        if (!$CompaniesList) {
            $CompaniesList = array();
        }

        $result["list"] = $CompaniesList;
        $res = array('error' => 0, 'errortip' => '获取职业下拉列表', 'result' => $result);

        ajax_return($res,$request['language_type']);
    }

    //职业阶段下拉列表
    function GetCareerStageApi()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $datawhere = "s.company_id = '{$request['company_id']}'";
        if(isset($request['career_id']) && $request['career_id'] != ''){
            $datawhere .= " and s.career_id = '{$request['career_id']}'";
        }

        $sql = "SELECT
                    s.stage_id,s.stage_cnname,c.career_id,ct.careertype_id
                FROM
                    eas_career_stage as s
                LEFT JOIN
                    eas_career as c ON c.career_id = s.career_id
                LEFT JOIN
                    eas_code_careertype as ct ON ct.careertype_id = c.careertype_id
                WHERE
                    {$datawhere}";
        $CompaniesList = $this->DataControl->selectClear($sql);
        if (!$CompaniesList) {
            $CompaniesList = array();
        }

        $result["list"] = $CompaniesList;
        $res = array('error' => 0, 'errortip' => '获取职业阶段下拉列表', 'result' => $result);

        ajax_return($res,$request['language_type']);
    }

    //通识课类型下拉列表
    function GetOpenClasstypeApi()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $sql = "SELECT openclasstype_id,openclasstype_cnname FROM eas_code_openclasstype WHERE company_id = '{$request['company_id']}'";
        $CompaniesList = $this->DataControl->selectClear($sql);
        if (!$CompaniesList) {
            $CompaniesList = array();
        }

        $result["list"] = $CompaniesList;
        $res = array('error' => 0, 'errortip' => '获取通识课类型下拉列表', 'result' => $result);

        ajax_return($res,$request['language_type']);
    }

    //校园职务
    function SchoolPostView(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new TrainingModel($request);
        $result = $Model->SchoolPostList($request);

        ajax_return($result,$request['language_type']);
    }

    //适配校园职务
    function AddSchoolPostAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new TrainingModel($request);
        $result = $Model->addSchoolPost($request);

        ajax_return($result,$request['language_type']);
    }

    //取消适配校园职务
    function DelSchoolPostAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new TrainingModel($request);
        $result = $Model->delSchoolPost($request);

        ajax_return($result,$request['language_type']);
    }

    //职业阶段管理
    function CareerStageView(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new TrainingModel($request);
        $result = $Model->CareerStageList($request);

        ajax_return($result,$request['language_type']);
    }

    //添加职业阶段
    function AddCareerStageAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new TrainingModel($request);
        $result = $Model->addCareerStageApi($request);

        ajax_return($result,$request['language_type']);
    }

    //编辑职业阶段
    function EditCareerStageAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new TrainingModel($request);
        $result = $Model->editCareerStageApi($request);

        ajax_return($result,$request['language_type']);
    }

    //删除职业阶段
    function DelCareerStageAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new TrainingModel($request);
        $result = $Model->delCareerStageApi($request);

        ajax_return($result,$request['language_type']);
    }

    //职业课管理
    function VocationalCoursesView(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new TrainingModel($request);
        $result = $Model->VocationalCourses($request);

        ajax_return($result,$request['language_type']);
    }

    //通识课管理
    function GeneralCoursesView(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new TrainingModel($request);
        $result = $Model->GeneralCourses($request);

        ajax_return($result,$request['language_type']);
    }

    //添加课程
    function AddVocationalCoursesAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new TrainingModel($request);
        $result = $Model->addVocationalCoursesApi($request);

        ajax_return($result,$request['language_type']);
    }

    //编辑课程
    function EditVocationalCoursesAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new TrainingModel($request);
        $result = $Model->editVocationalCoursesApi($request);

        ajax_return($result,$request['language_type']);
    }

    //删除课程
    function DelVocationalCoursesAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new TrainingModel($request);
        $result = $Model->delVocationalCoursesApi($request);

        ajax_return($result,$request['language_type']);
    }

    //职业课适配职务列表
    function GetCareerListView(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new TrainingModel($request);
        $result = $Model->GetCareerList($request);

        ajax_return($result,$request['language_type']);
    }

    //设置是否免考
    function IsExamAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new TrainingModel($request);
        $result = $Model->IsExamApi($request);

        ajax_return($result,$request['language_type']);
    }

    //职业课移除职务
    function DelAdaptiveAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new TrainingModel($request);
        $result = $Model->delAdaptiveApi($request);

        ajax_return($result,$request['language_type']);
    }

    //设置过关正确率
    function AccuracyAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new TrainingModel($request);
        $result = $Model->AccuracyApi($request);

        ajax_return($result,$request['language_type']);
    }

    //适配职业
    function AdaptiveCareerAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new TrainingModel($request);
        $result = $Model->adaptiveCareer($request);

        ajax_return($result,$request['language_type']);
    }

    //取消适配职业
    function DelAdaptiveCareerAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new TrainingModel($request);
        $result = $Model->delAdaptiveCareer($request);

        ajax_return($result,$request['language_type']);
    }

    //课程章节管理
    function CourseChapterView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new TrainingModel($request);
        $result = $Model->CourseChapterList($request);

        ajax_return($result,$request['language_type']);
    }

    //添加课程章节
    function AddChapterAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new TrainingModel($request);
        $result = $Model->addChapterApi($request);

        ajax_return($result,$request['language_type']);
    }

    //编辑课程章节
    function EditChapterAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new TrainingModel($request);
        $result = $Model->editChapterApi($request);

        ajax_return($result,$request['language_type']);
    }

    //删除课程章节
    function DelChapterAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new TrainingModel($request);
        $result = $Model->delChapterApi($request);

        ajax_return($result,$request['language_type']);
    }

    //章节内容管理
    function TrainingDataView(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new TrainingModel($request);
        $result = $Model->TrainingDataList($request);

        ajax_return($result,$request['language_type']);
    }

    //添加章节内容
    function AddTrainingDataAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new TrainingModel($request);
        $result = $Model->addTrainingDataApi($request);

        ajax_return($result,$request['language_type']);
    }

    //编辑章节内容
    function EditTrainingDataAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new TrainingModel($request);
        $result = $Model->editTrainingDataApi($request);

        ajax_return($result,$request['language_type']);
    }

    //删除章节内容
    function DelTrainingDataAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new TrainingModel($request);
        $result = $Model->delTrainingDataApi($request);

        ajax_return($result,$request['language_type']);
    }

    //培训类型设置 -- 职业课
    function TrainingTypeView(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new TrainingModel($request);
        $result = $Model->TrainingTypeList($request);

        ajax_return($result,$request['language_type']);
    }

    //添加培训类型 -- 职业课
    function AddTrainingTypeAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new TrainingModel($request);
        $result = $Model->addTrainingTypeApi($request);

        ajax_return($result,$request['language_type']);
    }

    //编辑培训类型 -- 职业课
    function EditTrainingTypeAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new TrainingModel($request);
        $result = $Model->editTrainingTypeApi($request);

        ajax_return($result,$request['language_type']);
    }

    //删除培训类型 -- 职业课
    function DelTrainingTypeAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new TrainingModel($request);
        $result = $Model->delTrainingTypeApi($request);

        ajax_return($result,$request['language_type']);
    }

    //培训类型设置 -- 通识课
    function TrainingTypeTwoView(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new TrainingModel($request);
        $result = $Model->TrainingTypeTwoList($request);

        ajax_return($result,$request['language_type']);
    }

    //添加培训类型 -- 通识课
    function AddTrainingTypeTwoAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new TrainingModel($request);
        $result = $Model->addTrainingTypeTwoApi($request);

        ajax_return($result,$request['language_type']);
    }

    //编辑培训类型 -- 通识课
    function EditTrainingTypTwoeAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new TrainingModel($request);
        $result = $Model->editTrainingTypeTwoApi($request);

        ajax_return($result,$request['language_type']);
    }

    //删除培训类型 -- 通识课
    function DelTrainingTypeTwoAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new TrainingModel($request);
        $result = $Model->delTrainingTypeTwoApi($request);

        ajax_return($result,$request['language_type']);
    }

    //职业试题设置
    function GetQuestionView(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new TrainingModel($request);
        $result = $Model->GetQuestionList($request);

        ajax_return($result,$request['language_type']);
    }

    //添加职业试题
    function AddQuestionAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new TrainingModel($request);
        $result = $Model->addQuestionApi($request);

        ajax_return($result,$request['language_type']);
    }

    //编辑职业试题
    function EditQuestionAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new TrainingModel($request);
        $result = $Model->editQuestionApi($request);

        ajax_return($result,$request['language_type']);
    }

    //删除职业试题
    function DelQuestionAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new TrainingModel($request);
        $result = $Model->delQuestionApi($request);

        ajax_return($result,$request['language_type']);
    }

    //适配职业
    function AddStageQuestionAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new TrainingModel($request);
        $result = $Model->addStageQuestion($request);

        ajax_return($result,$request['language_type']);
    }

    //取消适配职业
    function DelStageQuestionAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new TrainingModel($request);
        $result = $Model->delStageQuestion($request);

        ajax_return($result,$request['language_type']);
    }

    //职业试题选项列表
    function QuestionOptionView(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new TrainingModel($request);
        $result = $Model->QuestionOptionList($request);

        ajax_return($result,$request['language_type']);
    }

    //添加职业试题选项
    function AddQuestionOptionAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new TrainingModel($request);
        $result = $Model->addQuestionOptionApi($request);

        ajax_return($result,$request['language_type']);
    }

    //编辑职业试题选项
    function EditQuestionOptionAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new TrainingModel($request);
        $result = $Model->editQuestionOptionApi($request);

        ajax_return($result,$request['language_type']);
    }

    //删除职业试题选项
    function DelQuestionOptionAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new TrainingModel($request);
        $result = $Model->delQuestionOptionApi($request);

        ajax_return($result,$request['language_type']);
    }

}