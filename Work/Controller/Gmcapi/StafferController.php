<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/13
 * Time: 23:25
 */

namespace Work\Controller\Gmcapi;


class StafferController extends viewTpl
{
    public $u;
    public $t;
    public $c;
    public $visitType = "api";
    public $Model;

    //预加载处理类
    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
    }

    function ThisVerify($request){
        $paramArray = array();
        $paramArray['staffer_id'] = $request['staffer_id'];
        $paramArray['company_id'] = $request['company_id'];
        $paramArray['token'] = $request['token'];
        if(!$this->UserLimit($paramArray)){
            $result = array();
            $result["list"] = array();
            $result["tokeninc"] = "0";
            $res = array('error' => '1', 'errortip' => "用户token失效", 'result' => $result);
            ajax_return($res,$request['language_type']);
        }
    }


    //人脸采集 -- 查询职工是否是集团职务  -- 20230821暂不验证
    function getStaffIsGmcApi(){
        $request = Input('post.', '', 'trim,addslashes');
//        $this->ThisVerify($request);//验证账户
        $Model =  new \Model\Gmc\StafferModel($request);
        $Model->getStaffIsGmcApi($request);
        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $Model->result), $request['language_type']);
    }


    //职工管理首页 ---一级页面
    function HomeView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $this->Model =  new \Model\Gmc\StafferModel($request);

        $result = $this->Model->getStafferList($request);

        ajax_return($result,$request['language_type']);

    }

    //查看职工信息
    function StafferInfoApi()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $this->Model =  new \Model\Gmc\StafferModel($request);

        $result = $this->Model->StafferInfoApi($request);

        ajax_return($result,$request['language_type']);

    }

    //查看职工资料建档
    function StafferDataApi()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $this->Model =  new \Model\Gmc\StafferModel($request);

        $result = $this->Model->StafferDataApi($request);

        ajax_return($result,$request['language_type']);

    }

    //查看职工集团职务
    function StafferCompanyPostApi()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $this->Model =  new \Model\Gmc\StafferModel($request);

        $result = $this->Model->StafferCompanyPostApi($request);

        ajax_return($result,$request['language_type']);

    }

    //查看职工校园职务
    function StafferSchoolPostApi()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $this->Model =  new \Model\Gmc\StafferModel($request);

        $result = $this->Model->StafferSchoolPostApi($request);

        ajax_return($result,$request['language_type']);

    }

    //添加职工信息
    function addStafferInfoAction()
    {
        $request = Input('post.','','trim,addslashes');

        $this->ThisVerify($request);//验证账户

        $this->Model =  new \Model\Gmc\StafferModel($request);

        $result = $this->Model->addStafferInfoAction($request);

        ajax_return($result,$request['language_type']);

    }

    //修改职工信息
    function updateStafferInfoAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $this->Model =  new \Model\Gmc\StafferModel($request);

        $result = $this->Model->updateStafferInfoAction($request);

        ajax_return($result,$request['language_type']);
    }

    //删除职工
    function delStafferAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $this->Model =  new \Model\Gmc\StafferModel($request);

        $result = $this->Model->delStafferAction($request);

        ajax_return($result,$request['language_type']);
    }

    //删除职务
    function delPostAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $this->Model =  new \Model\Gmc\StafferModel($request);

        $result = $this->Model->delPostAction($request);

        ajax_return($result,$request['language_type']);
    }

    //添加职工集团职务
    function addStafferCompanyPostAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $StafferModel =  new \Model\Gmc\StafferModel($request);
        $result = $StafferModel->addStafferCompanyPostAction($request);
        ajax_return($result,$request['language_type']);
    }

    //编辑职工集团职务
    function updateStafferCompanyPostAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $this->Model =  new \Model\Gmc\StafferModel($request);

        $result = $this->Model->updateStafferCompanyPostAction($request);
        ajax_return($result,$request['language_type']);
    }

    //编辑职工校园职务
    function updateStafferSchoolPostAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $this->Model =  new \Model\Gmc\StafferModel($request);

        $result = $this->Model->updateStafferSchoolPostAction($request);
        ajax_return($result,$request['language_type']);
    }

    //添加职工校园职务
    function addStafferSchoolPostAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $this->Model =  new \Model\Gmc\StafferModel($request);

        $result = $this->Model->addStafferSchoolPostAction($request);
        ajax_return($result,$request['language_type']);
    }

    //获取学校
    function getSchoolApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $this->Model = new \Model\Gmc\StafferModel($request);

        $result = $this->Model->getSchoolApi($request);
        ajax_return($result,$request['language_type']);
    }

    //获取所属学校
    function findSchoolApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $this->Model = new \Model\Gmc\StafferModel($request);

        $result = $this->Model->findSchoolApi($request);
        ajax_return($result,$request['language_type']);
    }

    //添加职工资料建档
    function addStafferDataAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $this->Model =  new \Model\Gmc\StafferModel($request);

        $result = $this->Model->addStafferDataAction($request);
        ajax_return($result,$request['language_type']);
    }

    //编辑职工资料建档
    function updateStafferDataAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $this->Model =  new \Model\Gmc\StafferModel($request);

        $result = $this->Model->updateStafferDataAction($request);
        ajax_return($result,$request['language_type']);
    }

    //职工离职
    function updateLeaveAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $this->Model =  new \Model\Gmc\StafferModel($request);

        $result = $this->Model->updateLeaveAction($request);
        ajax_return($result,$request['language_type']);
    }

    //职工复职
    function updateJoinAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $this->Model =  new \Model\Gmc\StafferModel($request);

        $result = $this->Model->updateJoinAction($request);
        ajax_return($result,$request['language_type']);
    }

    //根据所选学校获取组织列表
    function getSchoolOrganizeApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $this->Model = new \Model\Gmc\StafferModel($request);

        $result = $this->Model->getSchoolOrganizeApi($request);
        ajax_return($result,$request['language_type']);
    }

    //查看职工对应的 容联七陌 的账号密码
    function getGmcCrmStafferOneApi()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $this->Model =  new \Model\Gmc\StafferModel($request);
        $result = $this->Model->getGmcCrmStafferOneApi($request);
        ajax_return($result,$request['language_type']);

    }
    //修改 容联七陌 的账号密码
    function updateGmcCrmStafferOneApi()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $this->Model =  new \Model\Gmc\StafferModel($request);
        $result = $this->Model->updateGmcCrmStafferOneApi($request);
        ajax_return($result,$request['language_type']);

    }

    //结尾魔术函数
    function __destruct()
    {

    }
}
