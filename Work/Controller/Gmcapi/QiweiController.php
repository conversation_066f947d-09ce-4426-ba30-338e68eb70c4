<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/13
 * Time: 23:25
 */
namespace Work\Controller\Gmcapi;

class QiweiController extends viewTpl
{
    public $u;
    public $t;
    public $c;
    public $visitType = "api";
    public $Model;

    //预加载处理类
    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
    }

    function getQiweiAccessTokenView($request)
    {
        $request = Input('get.', '', 'trim,addslashes');
        echo '本接口已经调试通过，关闭';
//        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Gmc\QiweiModel();
        $Model->getQiweiAccessToken();
        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $Model->result), $request['language_type']);
    }


    //获取企微 通讯录部门
    function getQwTxlDepartmentView()
    {
        $request = Input('get.', '', 'trim,addslashes');
//        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Gmc\QiweiModel($request);
        $Model->getQwTxlDepartment($request);
        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $Model->result), $request['language_type']);
    }

    //获取企微 通讯录标签数据
    function getQwTxlTagView()
    {
        $request = Input('get.', '', 'trim,addslashes');
//        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Gmc\QiweiModel($request);
        $Model->getQwTxlTag($request);
        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $Model->result), $request['language_type']);
    }

    //获取企微 通讯录 部门员工ID
    function getQwTxlStaffIdView()
    {
        $request = Input('get.', '', 'trim,addslashes');
//        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Gmc\QiweiModel($request);
        $Model->getQwTxlStaffId($request);
        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $Model->result), $request['language_type']);
    }

    //获取企微 通讯录 部门员工 详情
    function getQwTxlStaffInfoView()
    {
        $request = Input('get.', '', 'trim,addslashes');
//        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Gmc\QiweiModel($request);
        $Model->getQwTxlStaffInfo($request);
        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $Model->result), $request['language_type']);
    }



    //获取企微 员工的 客户 ID
    function getQwCustomeroIdView()
    {
        $request = Input('get.', '', 'trim,addslashes');
//        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Gmc\QiweiModel($request);
        $Model->getQwCustomeroId($request);
        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $Model->result), $request['language_type']);
    }

    //获取企微 客户 单个客户详情
    function getQwCustomeroOneInfoView()
    {
        $request = Input('get.', '', 'trim,addslashes');
//        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Gmc\QiweiModel($request);
        $Model->getQwCustomeroOneInfo($request);
        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $Model->result), $request['language_type']);
    }

    //获取企微 客户 批量获取客户详情
    function getQwCustomeroSomeInfoView()
    {
        $request = Input('get.', '', 'trim,addslashes');
//        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Gmc\QiweiModel($request);
        $Model->getQwCustomeroSomeInfo($request);
        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $Model->result), $request['language_type']);
    }

    //获取企微 客户 标签数据
    function getQwCustomeroTagView()
    {
        $request = Input('get.', '', 'trim,addslashes');
//        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Gmc\QiweiModel($request);
        $Model->getQwKhTag($request);
        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $Model->result), $request['language_type']);
    }

    //获取企微 客户 群列表
    function getQwCustomeroCrowdIdView()
    {
        $request = Input('get.', '', 'trim,addslashes');
//        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Gmc\QiweiModel($request);
        $Model->getQwCustomeroCrowdId($request);
        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $Model->result), $request['language_type']);
    }


    //获取企微 客户 群 详情
    function getQwCustomeroCrowdOneInfoView()
    {
        $request = Input('get.', '', 'trim,addslashes');
//        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Gmc\QiweiModel($request);
        $Model->getQwCustomeroCrowdOneInfo($request);
        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $Model->result), $request['language_type']);
    }

    //获取企微 朋友圈 发布的记录
    function getQwPyqListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
//        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Gmc\QiweiModel($request);
        $Model->getQwPyqList($request);
        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $Model->result), $request['language_type']);
    }

    //获取企微 朋友圈 企业发表的列表
    function getQwPyqComListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
//        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Gmc\QiweiModel($request);
        $Model->getQwPyqComList($request);
        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $Model->result), $request['language_type']);
    }

    //获取企微 朋友圈 动态可见范围
    function getQwPyqOneSeeView()
    {
        $request = Input('get.', '', 'trim,addslashes');
//        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Gmc\QiweiModel($request);
        $Model->getQwPyqOneSee($request);
        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $Model->result), $request['language_type']);
    }

    //获取企微 朋友圈 动态 可见客户
    function getQwPyqOneCustomeroSeeView()
    {
        $request = Input('get.', '', 'trim,addslashes');
//        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Gmc\QiweiModel($request);
        $Model->getQwPyqOneCustomeroSee($request);
        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $Model->result), $request['language_type']);
    }

    //获取企微 朋友圈 互动
    function getQwPyqOneExchangeView()
    {
        $request = Input('get.', '', 'trim,addslashes');
//        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Gmc\QiweiModel($request);
        $Model->getQwPyqOneExchange($request);
        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $Model->result), $request['language_type']);
    }

    //获取企微 群发 记录
    function getQwMasssendListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
//        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Gmc\QiweiModel($request);
        $Model->getQwMasssendList($request);
        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $Model->result), $request['language_type']);
    }

    //获取企微 群发 任务成员列表
    function getQwMasssendOnetMemberView()
    {
        $request = Input('get.', '', 'trim,addslashes');
//        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Gmc\QiweiModel($request);
        $Model->getQwMasssendOnetMember($request);
        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $Model->result), $request['language_type']);
    }


    //获取企微 群发 群发成员执行结果
    function getQwMasssendOnetMemberResultView()
    {
        $request = Input('get.', '', 'trim,addslashes');
//        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Gmc\QiweiModel($request);
        $Model->getQwMasssendOnetMemberResult($request);
        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $Model->result), $request['language_type']);
    }



}
