<?php
namespace Work\Controller\Gmcapi;

class StudentController extends viewTpl
{
    public $u;
    public $t;
    public $c;
    public $visitType = "api";

    function __construct()
    {
        parent::__construct();
    }

    function carryForwardAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\ForwardModel($request);

        $request['is_skip']=1;

        if($request['list']){

            $listArray = json_decode(stripslashes($request['list']), true);
            if($listArray[0]['class_id']>0){
                $sql = "select b.hour_day 
                    from smc_student_hourstudy as a,smc_class_hour as b 
                    where a.hour_id=b.hour_id and a.class_id='{$listArray[0]['class_id']}' and a.student_id='{$request['student_id']}'
                    order by a.hourstudy_id desc limit 0,1
                    ";
                $hourOne=$this->DataControl->selectOne($sql);
                if($hourOne){
                    $request['create_time']=$hourOne['hour_day'];
                }else{

                    $classOne=$this->DataControl->getFieldOne("smc_class","class_stdate","class_id='{$listArray[0]['class_id']}'");

                    $request['create_time']=$classOne['class_stdate'];

                }
            }
        }
        
        $res= $Model->carryForward($request);

        if($res){
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '结转成功', 'result' => $result);
        }else{
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);

        }
        ajax_return($res,$request['language_type']);
    }
}