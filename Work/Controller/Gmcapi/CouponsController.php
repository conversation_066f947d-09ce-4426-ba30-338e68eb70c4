<?php


namespace Work\Controller\Gmcapi;


class CouponsController extends viewTpl
{
    public $u;
    public $t;
    public $c;

    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
    }

    function getCouponsActivityApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Gmc\CouponsModel($request);
        $res = $Model->getCouponsActivity($request);

        $k=0;
        $field = array();
        $field[$k]["fieldstring"] = "ticket_id";
        $field[$k]["fieldname"] = "优惠券活动ID";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "ticket_cnname";
        $field[$k]["fieldname"] = "优惠券活动名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "validity";
        $field[$k]["fieldname"] = "活动有效期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "schooolNum";
        $field[$k]["fieldname"] = "适用学校";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $field[$k]["ismethod"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "ticket_note";
        $field[$k]["fieldname"] = "活动备注";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }

    function addCouponsActivityAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);

        $Model = new \Model\Gmc\CouponsModel($request);
        $res = $Model->addCouponsActivity($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '新增成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }

    function editCouponsActivityAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);

        $Model = new \Model\Gmc\CouponsModel($request);
        $res = $Model->editCouponsActivity($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '编辑成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }

    function delCouponsActivityAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);

        $Model = new \Model\Gmc\CouponsModel($request);
        $res = $Model->delCouponsActivity($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '删除成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }

    function getSchoolListApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Gmc\CouponsModel($request);
        $res = $Model->getSchoolList($request);

        $k=0;
        $field = array();
        $field[$k]["fieldstring"] = "school_id";
        $field[$k]["fieldname"] = "学校ID";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校园名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "school_enname";
        $field[$k]["fieldname"] = "检索代码";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "district_cnname";
        $field[$k]["fieldname"] = "所属区域";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $result = array();
        $result["field"] = $field;
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }

    function getApplySchoolListApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Gmc\CouponsModel($request);
        $res = $Model->getApplySchoolList($request);

        $k=0;
        $field = array();
        $field[$k]["fieldstring"] = "school_id";
        $field[$k]["fieldname"] = "学校ID";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校园名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "school_enname";
        $field[$k]["fieldname"] = "检索代码";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "district_cnname";
        $field[$k]["fieldname"] = "所属区域";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $result = array();
        $result["field"] = $field;
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }

    function applySchoolAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);

        $Model = new \Model\Gmc\CouponsModel($request);
        $res = $Model->applySchool($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '适配成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }

    function removeSchoolAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);

        $Model = new \Model\Gmc\CouponsModel($request);
        $res = $Model->removeSchool($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '移除成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }

    function getTicketCouponsApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Gmc\CouponsModel($request);
        $res = $Model->getTicketCoupons($request);

        $k=0;
        $field = array();
        $field[$k]["fieldstring"] = "coupons_id";
        $field[$k]["fieldname"] = "优惠券活动ID";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "setup_tagnote";
        $field[$k]["fieldname"] = "批次备注";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "applytype_cnname";
        $field[$k]["fieldname"] = "优惠券类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "allNum";
        $field[$k]["fieldname"] = "优惠券数量";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "beNum";
        $field[$k]["fieldname"] = "待激活";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $field[$k]["ismethod"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "tobeNum";
        $field[$k]["fieldname"] = "待使用";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $field[$k]["ismethod"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "hasNum";
        $field[$k]["fieldname"] = "已使用";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $field[$k]["ismethod"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "noNum";
        $field[$k]["fieldname"] = "已失效";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $field[$k]["ismethod"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "setup_endday";
        $field[$k]["fieldname"] = "截止日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "courseNum_name";
        $field[$k]["fieldname"] = "适用课程数量";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $field[$k]["ismethod"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "setup_playclass_name";
        $field[$k]["fieldname"] = "使用类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "setup_type_name";
        $field[$k]["fieldname"] = "优惠方式";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "setup_price";
        $field[$k]["fieldname"] = "金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "setup_discount";
        $field[$k]["fieldname"] = "折扣";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "setup_minprice";
        $field[$k]["fieldname"] = "最低消费金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;



        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $result["ticket_endday"] = $res['ticket_endday'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }


    function createCouponsAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);

        $Model = new \Model\Gmc\CouponsModel($request);
        $res = $Model->createCoupons($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '生成成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }

    function increaseCardAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);

        $Model = new \Model\Gmc\CouponsModel($request);
        $res = $Model->increaseCard($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '生成成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }

    function delCouponsAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);

        $Model = new \Model\Gmc\CouponsModel($request);
        $res = $Model->delCoupons($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '删除成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }

    function getCardListApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Gmc\CouponsModel($request);
        $res = $Model->getCardList($request);

        $k=0;
        $field = array();
        $field[$k]["fieldstring"] = "card_id";
        $field[$k]["fieldname"] = "优惠券活动ID";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "card_branch";
        $field[$k]["fieldname"] = "优惠券码";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "applytype_cnname";
        $field[$k]["fieldname"] = "优惠券类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "setup_playclass_name";
        $field[$k]["fieldname"] = "优惠券使用类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "setup_type_name";
        $field[$k]["fieldname"] = "折扣方式";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "setup_discount";
        $field[$k]["fieldname"] = "折扣";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "setup_price";
        $field[$k]["fieldname"] = "抵扣金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "setup_minprice";
        $field[$k]["fieldname"] = "最低消费金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "courseNum";
        $field[$k]["fieldname"] = "适用课程数量";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "time";
        $field[$k]["fieldname"] = "有效时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "staffer_cnname";
        $field[$k]["fieldname"] = "激活人";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "card_bindtime";
        $field[$k]["fieldname"] = "激活时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["fieldname"] = "学员中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "student_enname";
        $field[$k]["fieldname"] = "学员英文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "student_branch";
        $field[$k]["fieldname"] = "学员编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }

    function getSetupCourseApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Gmc\CouponsModel($request);
        $res = $Model->getSetupCourse($request);

        $k=0;
        $field = array();
        $field[$k]["fieldstring"] = "course_id";
        $field[$k]["fieldname"] = "课程ID";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "course_cnname";
        $field[$k]["fieldname"] = "课程中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "course_branch";
        $field[$k]["fieldname"] = "课程编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $result = array();
        $result["field"] = $field;
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }

}