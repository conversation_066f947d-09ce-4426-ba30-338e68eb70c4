<?php


namespace Work\Controller\Gmcapi;


class ReportClassController extends viewTpl
{
    public $u;
    public $t;
    public $c;

    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
    }

    function ThisVerify($request)
    {
        $paramArray = array();
        $paramArray['staffer_id'] = $request['staffer_id'];
        $paramArray['school_id'] = $request['school_id'];
        $paramArray['company_id'] = $request['company_id'];
        $paramArray['token'] = $request['token'];
        if (!$this->UserLimit($paramArray)) {
            $result = array();
            $result["list"] = array();
            $result["tokeninc"] = "0";
            $res = array('error' => 1, 'errortip' => "用户token失效", 'result' => $result);
            ajax_return($res, $request['language_type']);
        }
    }

    /**
     * 班级信息统计表
     * author: ling
     * 对应接口文档 0001
     */
    function classInfoView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Report\Gmc\ClassReportModel($request);
        $res = $ReportModel->classInfo($request);
        $field = array();

        $k = 0;
        $field = array();
        $field[$k]["fieldstring"] = "province_name";
        $field[$k]["fieldname"] = "省份";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "course_branch";
        $field[$k]["fieldname"] = "课程别编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_cnname";
        $field[$k]["fieldname"] = "班级名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_enname";
        $field[$k]["fieldname"] = "班级别名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_branch";
        $field[$k]["fieldname"] = "班级编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_enddate";
        $field[$k]["fieldname"] = "结班日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;


        $field[$k]["fieldstring"] = "all_teacher_num";
        $field[$k]["fieldname"] = "班级教师累计总数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "teaching_num";
        $field[$k]["fieldname"] = "教师人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_fullnums";
        $field[$k]["fieldname"] = "满班人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "study_num";
        $field[$k]["fieldname"] = "在读人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "fill_class_rate";
        $field[$k]["fieldname"] = "满班率";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "to_class_num";
        $field[$k]["fieldname"] = "转入人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "out_class_num";
        $field[$k]["fieldname"] = "转出人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "leave_class_num";
        $field[$k]["fieldname"] = "班级流失人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "wait_class_num";
        $field[$k]["fieldname"] = "延班人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "stu_all_checking_num";
        $field[$k]["fieldname"] = "应到人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "stu_right_checking_num";
        $field[$k]["fieldname"] = "实到人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "stu_checking_rate";
        $field[$k]["fieldname"] = "出勤率";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res['list']) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => "暂无班级信息", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    function weeklyNumberListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Report\Gmc\ClassReportModel($request);
        $res = $ReportModel->weeklyNumberList($request);

        $k = 0;
        $field = array();

        $field[$k]["fieldstring"] = "school_id";
        $field[$k]["fieldname"] = "学校id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "province_name";
        $field[$k]["fieldname"] = "省份";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursetype_cnname";
        $field[$k]["fieldname"] = "班组";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursetype_branch";
        $field[$k]["fieldname"] = "班组编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursecat_cnname";
        $field[$k]["fieldname"] = "班种";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursecat_branch";
        $field[$k]["fieldname"] = "班种编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "course_branch";
        $field[$k]["fieldname"] = "班别编号";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_cnname";
        $field[$k]["fieldname"] = "班级名称";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_enname";
        $field[$k]["fieldname"] = "班级别名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_branch";
        $field[$k]["fieldname"] = "班级编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_isupgrade";
        $field[$k]["fieldname"] = "新建/升级";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_isfictitious";
        $field[$k]["fieldname"] = "是否虚拟班级";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "classInfo";
        $field[$k]["fieldname"] = "已上课次数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "deduct_num";
        $field[$k]["fieldname"] = "本周考勤次数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "standard_num";
        $field[$k]["fieldname"] = "周排课标准课次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "newweeknum";
        $field[$k]["fieldname"] = "本周人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["ismethod"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "classTime";
        $field[$k]["fieldname"] = "起止日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_timestr";
        $field[$k]["fieldname"] = "上课时段";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "cnteacher";
        $field[$k]["fieldname"] = "教师姓名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_totalnum";
        $field[$k]["fieldname"] = "总课次数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_feenum";
        $field[$k]["fieldname"] = "收费课次数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_freenum";
        $field[$k]["fieldname"] = "赠送课次数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "left_totalnum";
        $field[$k]["fieldname"] = "剩余总课次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "left_feenum";
        $field[$k]["fieldname"] = "收费剩余课次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "left_freenum";
        $field[$k]["fieldname"] = "赠送剩余课次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "oldweeknum";
        $field[$k]["fieldname"] = "上周人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "innum";
        $field[$k]["fieldname"] = "转入";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["ismethod"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "outnum";
        $field[$k]["fieldname"] = "转出";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["ismethod"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "staynum";
        $field[$k]["fieldname"] = "延班";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["ismethod"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "lostnum";
        $field[$k]["fieldname"] = "流失";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["ismethod"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "freshnum";
        $field[$k]["fieldname"] = "新生";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["ismethod"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "next_class_branch";
        $field[$k]["fieldname"] = "升班编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => "暂无班级周人数信息", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    function classToStartView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Report\Gmc\ClassReportModel($request);
        $res = $ReportModel->classToStartList($request);

        $k = 0;
        $field = array();

        $field[$k]["fieldstring"] = "school_id";
        $field[$k]["fieldname"] = "学校ID";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "province_name";
        $field[$k]["fieldname"] = "省份";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursecat_cnname";
        $field[$k]["fieldname"] = "班种";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursecat_branch";
        $field[$k]["fieldname"] = "班种编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_cnname";
        $field[$k]["fieldname"] = "班级名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_enname";
        $field[$k]["fieldname"] = "班级别名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_branch";
        $field[$k]["fieldname"] = "班级编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "classTime";
        $field[$k]["fieldname"] = "起止日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_timestr";
        $field[$k]["fieldname"] = "上课时段";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "cnteacher";
        $field[$k]["fieldname"] = "教师姓名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_totalnum";
        $field[$k]["fieldname"] = "总课次数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_feenum";
        $field[$k]["fieldname"] = "收费课次数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_freenum";
        $field[$k]["fieldname"] = "赠送课次数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_studynum";
        $field[$k]["fieldname"] = "已入班人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => "暂无班级待开班信息", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    /**
     * 学员转班明细报表
     * author: wgh
     * 对应接口文档 993
     */
    function changeClassDetailView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);

        $ReportModel = new \Model\Report\Gmc\ClassReportModel($request);
        $res = $ReportModel->changeClassDetail($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "province_name";
        $field[$k]["fieldname"] = "省份";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "分校编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "分校名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["fieldname"] = "学员中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_enname";
        $field[$k]["fieldname"] = "学员英文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_branch";
        $field[$k]["fieldname"] = "学员编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "from_class_cnname";
        $field[$k]["fieldname"] = "转出班级名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "from_class_enname";
        $field[$k]["fieldname"] = "转出班级别名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "from_class_branch";
        $field[$k]["fieldname"] = "转出班级编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "from_staffer_cnname";
        $field[$k]["fieldname"] = "转出班级教师";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "to_class_cnname";
        $field[$k]["fieldname"] = "转入班级名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "to_class_enname";
        $field[$k]["fieldname"] = "转入班级别名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "to_class_branch";
        $field[$k]["fieldname"] = "转入班级编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "to_staffer_cnname";
        $field[$k]["fieldname"] = "转入班级教师";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "change_day";
        $field[$k]["fieldname"] = "异动时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "reason_note";
        $field[$k]["fieldname"] = "异动原因";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res['list']) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => "暂无班级学员转班明细报表", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    //学员升班明细报表 -- li197
    function studentClassUpView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Report\Gmc\ClassReportModel($request);
        $res = $ReportModel->studentClassUp($request);

        $field = array();
        $k = 0;

        $field[$k]["fieldstring"] = "school_id";
        $field[$k]["fieldname"] = "分校id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "province_name";
        $field[$k]["fieldname"] = "省份";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "分校编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "分校名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["fieldname"] = "学员中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_enname";
        $field[$k]["fieldname"] = "学员英文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_branch";
        $field[$k]["fieldname"] = "学员编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "from_class_cnname";
        $field[$k]["fieldname"] = "来源班级名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "from_class_enname";
        $field[$k]["fieldname"] = "来源班级别名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "from_class_branch";
        $field[$k]["fieldname"] = "来源班级编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "staffer_cnname";
        $field[$k]["fieldname"] = "班级教师";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "to_class_cnname";
        $field[$k]["fieldname"] = "升班班级名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "to_class_enname";
        $field[$k]["fieldname"] = "升班班级别名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "to_class_branch";
        $field[$k]["fieldname"] = "升班班级编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "upgradelog_createtime";
        $field[$k]["fieldname"] = "升班时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;


        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res['list']) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => "暂无班级转班明细报表", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    //班级升班明细表
    function classLevelUpView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Report\Smc\ClassReportModel($request);
        $res = $ReportModel->promotion($request);

        $field = array();
        $k = 0;

        $field[$k]["fieldstring"] = "school_id";
        $field[$k]["fieldname"] = "分校id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "province_name";
        $field[$k]["fieldname"] = "省份";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "分校编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "分校名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "from_class_cnname";
        $field[$k]["fieldname"] = "班级名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "from_class_enname";
        $field[$k]["fieldname"] = "班级别名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "from_class_branch";
        $field[$k]["fieldname"] = "班级编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "from_course_cnname";
        $field[$k]["fieldname"] = "课程别名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "from_course_branch";
        $field[$k]["fieldname"] = "课程别编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "from_staffer_cnname";
        $field[$k]["fieldname"] = "班级教师";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "from_num";
        $field[$k]["fieldname"] = "班级人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "upgradeorder_createtime";
        $field[$k]["fieldname"] = "升班时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "to_class_cnname";
        $field[$k]["fieldname"] = "升班班级名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "to_class_enname";
        $field[$k]["fieldname"] = "升班班级别名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "to_class_branch";
        $field[$k]["fieldname"] = "升班班级编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "to_staffer_cnname";
        $field[$k]["fieldname"] = "升班班级教师";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "to_num";
        $field[$k]["fieldname"] = "升班人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "promotion_rate";
        $field[$k]["fieldname"] = "升班率";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res['list']) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => "暂无班级升班明细表", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }


    //班级升班明细表
    function classBreakoffSummaryView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Report\Gmc\ClassReportModel($request);
        $res = $ReportModel->classBreakoffSummary($request);

        $field = array();
        $k = 0;

        $field[$k]["fieldstring"] = "school_id";
        $field[$k]["fieldname"] = "分校id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "province_name";
        $field[$k]["fieldname"] = "省份";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "分校编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "分校名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["fieldname"] = "学员中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_enname";
        $field[$k]["fieldname"] = "学员英文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_branch";
        $field[$k]["fieldname"] = "学员编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "enrolled_status";
        $field[$k]["fieldname"] = "学员状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "lost_reason";
        $field[$k]["fieldname"] = "流失原因";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "lost_date";
        $field[$k]["fieldname"] = "流失日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_enddate";
        $field[$k]["fieldname"] = "班级结束日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "breakoff_type";
        $field[$k]["fieldname"] = "拆班类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_cnname";
        $field[$k]["fieldname"] = "中途拆班班级名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_enname";
        $field[$k]["fieldname"] = "中途拆班班级别名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_branch";
        $field[$k]["fieldname"] = "中途拆班班级编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_times";
        $field[$k]["fieldname"] = "中途拆班班级上课课次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "after_class_cnname";
        $field[$k]["fieldname"] = "下次就读班级名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "after_class_enname";
        $field[$k]["fieldname"] = "下次就读班级别名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "after_class_branch";
        $field[$k]["fieldname"] = "下次就读班级编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "after_class_times";
        $field[$k]["fieldname"] = "下次就读班级上课课次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res['list']) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => "暂无中拆结班信息", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    //班级学年流失率
    function classYearlossrateView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Report\Gmc\ClassReportModel($request);
        $res = $ReportModel->classYearlossrate($request);

        $field = array();
        $k = 0;

        $field[$k]["fieldstring"] = "endcalc_id";
        $field[$k]["fieldname"] = "班级结算ID";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_cnname";
        $field[$k]["fieldname"] = "班级名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_enname";
        $field[$k]["fieldname"] = "班级别名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_branch";
        $field[$k]["fieldname"] = "班级编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "endcalc_startdate";
        $field[$k]["fieldname"] = "学年开班日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "endcalc_enddate";
        $field[$k]["fieldname"] = "学年结束日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "yearlocus";
        $field[$k]["fieldname"] = "学年轨迹";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;


        $field[$k]["fieldstring"] = "beginnums";
        $field[$k]["fieldname"] = "入班累计学员数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "transoutnums";
        $field[$k]["fieldname"] = "转班转出累计学员";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "deferrednums";
        $field[$k]["fieldname"] = "延班学员数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "upgradenums";
        $field[$k]["fieldname"] = "续读人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "loss_rate";
        $field[$k]["fieldname"] = "班级流失预估率";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res['list']) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => "暂无中拆结班信息", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //预制升班统计表
    function classUpgradeSummaryView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Report\Gmc\ClassReportModel($request);
        $res = $ReportModel->classUpgradeSummary($request);

        $field = array();
        $k = 0;

        $field[$k]["fieldstring"] = "class_id";
        $field[$k]["fieldname"] = "班级ID";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "province_name";
        $field[$k]["fieldname"] = "省份";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_cnname";
        $field[$k]["fieldname"] = "班级名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_enname";
        $field[$k]["fieldname"] = "班级别名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_branch";
        $field[$k]["fieldname"] = "班级编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_stdate";
        $field[$k]["fieldname"] = "开班日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_enddate";
        $field[$k]["fieldname"] = "结班日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_status";
        $field[$k]["fieldname"] = "班级状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_now";
        $field[$k]["fieldname"] = "当前在班学员";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_num";
        $field[$k]["fieldname"] = "结班前42天在班学员";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "order_num";
        $field[$k]["fieldname"] = "已下单下级别课程学员";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "pay_num";
        $field[$k]["fieldname"] = "已付款下级别课程学员";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res['list']) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => "暂无预制升班信息", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

}
