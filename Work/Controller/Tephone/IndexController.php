<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2018/12/29
 * Time: 10:09
 */

namespace Work\Controller\Tephone;


class IndexController extends viewTpl
{
	public $u;
	public $t;
	public $c;
	public $istaffer;
	public $Viewhtm;
	
	function __construct()
	{
		parent::__construct();
		if(!$this->check_login()){
			$this->LoginView();
		}
		
		$this->u = $this->router->getController();
		$this->t = $this->router->getUrl();
		$this->c = $this->router->getAction();
		$this->Viewhtm = $this->router->getController() . "/" . $this->router->getUrl() . ".htm";
		$this->smarty->assign("istaffer", $this->istaffer);
	}
	
	
	function  Homeview(){
		
		
		$parmentArray = array();
		$parmentArray['staffer_id'] = $this->istaffer['staffer_id'];
		$parmentArray['company_id'] = $this->istaffer['company_id'];
		$parmentArray['re_postbe_id'] = $this->istaffer['postbe_id'];
		
		$postData = $this->DataControl->getFieldOne("gmc_staffer_postbe","school_id","postbe_id='{$this->istaffer['postbe_id']}' ");
		if(!$postData['school_id']){
			$CommonModel = new \Model\Crm\CommonModel();
			$dataList = $CommonModel->getSchoolApi($parmentArray);
			$schoolList = array_values($dataList['datalist']);
			$datalist =$schoolList[0];
		}else{
			 $schoollist = $this->DataControl->selectClear("select school_id,school_cnname,school_branch from smc_school  where school_id='{$postData['school_id']}' ");
			$datalist = $schoollist;
			
		}
		 $stafferPost = $this->stafferPostbeApi($this->istaffer['staffer_id'],$this->istaffer['company_id'],$this->istaffer['postbe_id']);
		$this->smarty->assign("schoolList",$datalist);
	
		$this->smarty->assign("stafferPost",$stafferPost['list']);
		$datatype['school_id'] =   $this->istaffer['school_id'];
		$schoolOne =$this->DataControl->getFieldOne("smc_school","school_cnname","school_id='{$this->istaffer['school_id']}'");
		$datatype['school_cnname'] = $schoolOne['school_cnname'];
		$this->smarty->assign("datatype",$datatype);
		
	}
	
//	选择职位
	function PostView()
	{
		if($this->istaffer['account_class']==1 ){
			$parmentArray = array();
			$parmentArray['staffer_id'] = $this->istaffer['staffer_id'];
			$parmentArray['company_id'] = $this->istaffer['company_id'];
			$parmentArray['re_postbe_id'] = $this->istaffer['postbe_id'];
			
			$postData = $this->DataControl->getFieldOne("gmc_staffer_postbe","school_id","postbe_id='{$this->istaffer['postbe_id']}' ");
			if(!$postData['school_id']){
				$CommonModel = new \Model\Crm\CommonModel();
				$dataList = $CommonModel->getSchoolApi($parmentArray);
				$schoolList = array_values($dataList['datalist']);
				$datalist =$schoolList[0];
			}else{
				$schoollist = $this->DataControl->selectClear("select school_id,school_cnname,school_branch from smc_school  where school_id='{$postData['school_id']}' ");
				$datalist = $schoollist;
				
			}
			$stafferPost = $this->stafferPostbeApi($this->istaffer['staffer_id'],$this->istaffer['company_id'],$this->istaffer['postbe_id']);
			$this->smarty->assign("schoolList",$datalist);
			$this->smarty->assign("stafferPost",$stafferPost['list']);
			$datatype['school_id'] = $this->istaffer['school_id'];
			$this->smarty->assign("datatype", $datatype);
			 $this->Viewhtm  = $this->router->getController() . "/" ."Home.htm";
		     
		 exit;
		}
		
		
		$ComPost = $this->DataControl->selectClear("SELECT
                p.postbe_id,
                o.organize_cnname as cnname,
                s.post_name
            FROM
                gmc_staffer_postbe AS p
                LEFT JOIN gmc_company_organize AS o ON p.organize_id = o.organize_id
                LEFT JOIN gmc_company_post AS s ON p.post_id = s.post_id
            WHERE
                p.staffer_id = '{$this->istaffer['staffer_id']}' and p.school_id = 0");
		$company = array();
		if ($ComPost) {
			$company= $ComPost;
		}

		$SchPost = $this->DataControl->selectClear("SELECT
                p.postbe_id,
                c.school_cnname as cnname,
                s.post_name,
                c.school_id
            FROM
                gmc_staffer_postbe AS p
                left join smc_school as c on p.school_id = c.school_id
                LEFT JOIN gmc_company_post AS s ON p.post_id = s.post_id
            WHERE
                p.staffer_id = '{$this->istaffer['staffer_id']}' and p.school_id > 0");
		$school = array();
		if ($SchPost) {
			$school = $SchPost;
		}
		
		$datatype['school_id'] = $this->istaffer['school_id'];
		$this->smarty->assign("company",$company);
		$this->smarty->assign("school",$school);
		$this->smarty->assign("datatype", $datatype);
	}
	
	
	
	function  ChoosePostView()
	{
		 $this->c = "choose";
		  $request = Input('post.','','trim,addslashes');
		   if(!$request['school_id'] && !$request['postbe_id'] ){
			   ajax_return(array('error' => 1,'errortip' => "请选择职位"));
		   }

		  $PostbeOne =  $this->DataControl->getFieldOne("gmc_staffer_postbe","postbe_crmuserlevel","postbe_id='{$request['postbe_id']}'");
		
		  $itaffer = $this->intSession->getCookiearray("istaffer");
		  
		  $itaffer['postbe_id'] = $request['postbe_id'];
		  $itaffer['postbe_crmuserlevel'] = $PostbeOne['postbe_crmuserlevel'];
		
   		  $itaffer['school_id'] = $request['school_id'];
			
		  $this->intSession->setCookiearray("istaffer",$itaffer,'1');
		
	
		 ajax_return(array('error' => 0,'errortip' => "职位选择"));
		
	}
	
	function stafferPostbeApi($staffer_id,$company_id,$postbe_id){
//		$request = Input('get.','','trim,addslashes');
		$istaffer = array();
		$stafferOne = $this->DataControl->getFieldOne('smc_staffer',"staffer_id,staffer_leave,staffer_pass,account_class"," staffer_id = '{$staffer_id}' and company_id = '{$company_id}'");
		if($stafferOne['account_class'] == '1'){
			$schoolOne = $this->DataControl->getFieldOne('smc_school',"school_id","company_id = '{$company_id}'","order by school_istemp DESC");
			$istaffer['isCompany'] = '1';
			$istaffer['isSchool'] = '1';
			$istaffer['isCrm'] = '1';
		}else {
			$postroleOne = $this->DataControl->getFieldOne("gmc_staffer_postbe","postrole_id,organize_id,school_id,postbe_iscrmuser","postbe_id = '{$postbe_id}'");
			if($postroleOne['school_id'] == '0'){
				$schoolOne = $this->DataControl->selectOne("SELECT p.school_id FROM gmc_company_organizeschool AS p WHERE p.organize_id = '{$postroleOne['organize_id']}' limit 0,1");
				//集团角色
				$status = $this->DataControl->getFieldOne("gmc_company_postrole","postpart_iscompanyuser,postpart_iscmsuser,postpart_iscrmuser","postrole_id = '{$postroleOne['postrole_id']}'");
				if ($status['postpart_iscompanyuser'] == '1') {
					$istaffer['isCompany'] = '1';
				} else {
					$istaffer['isCompany'] = '0';
				}
				
				//校园角色
				$status = $this->DataControl->getFieldOne("gmc_company_postrole","postpart_iscmsuser","postrole_id = '{$postroleOne['postrole_id']}'");
				if ($status['postpart_iscmsuser'] == '1') {
					$istaffer['isSchool'] = '1';
				} else {
					$istaffer['isSchool'] = '0';
				}
				//CRM权限
				$status = $this->DataControl->getFieldOne("gmc_company_postrole","postpart_iscrmuser","postrole_id = '{$postroleOne['postrole_id']}'");
				if ($status['postpart_iscrmuser'] == '1') {
					$istaffer['isCrm'] = '1';
				} else {
					$istaffer['isCrm'] = '0';
				}
			}else{
				//集团角色
				$istaffer['isCompany'] = '0';
				//校园角色
				$istaffer['isSchool'] = '1';
				//CRM权限
				if ($postroleOne['postbe_iscrmuser'] == '1') {
					$istaffer['isCrm'] = '1';
				} else {
					$istaffer['isCrm'] = '0';
				}
				$schoolOne['school_id'] = $postroleOne['school_id'];
			}
		}
		
		$result = array();
		if ($istaffer) {
			$result["list"] = $istaffer;
			$result["school_id"] = $schoolOne['school_id'];
		} else {
			$result["list"] = array();
		}
		  return $result;
	}


	function SwitchSchoolView()
    {
        $this->c='swtich';
        $reqeust = Input("post.");

        if(isset($reqeust['school_id']) && intval($reqeust['school_id']) >0){
            $itaffer = $this->intSession->getCookiearray("istaffer");
            $itaffer['school_id'] =$reqeust['school_id'];
            $this->intSession->setCookiearray('istaffer',$itaffer,1);
            ajax_return(array('error' => 0,'errortip' => "切换成功"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "请选择学校"));
        }
    }
	
	
	
	//魔术方法
	public function __call($name, $arguments) {
		$this->Viewhtm = "under.htm";
		// Note: value of $name is case sensitive.
		//echo "Calling object method '$name' ". implode(', ', $arguments). "\n";
	}
	//魔术方法
	function __destruct()
	{
		if ($this->c =='Wait') {
			$this->smarty->assign("u", $this->u);
			$this->smarty->assign("t", $this->t);
			$this->smarty->assign("Viewhtm", $this->Viewhtm);
			$this->display($this->Viewhtm);
//            $this->display("Home.html");
			exit;
		}
	}
}