<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2018/12/29
 * Time: 10:09
 */

namespace Work\Controller\Tephone;


class LoginController extends viewTpl
{
	public $u;
	public $t;
	public $c;
	public $Viewhtm;
	
	function __construct()
	{
		parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
		$this->u = $this->router->getController();
		$this->t = $this->router->getUrl();
		$this->c = $this->router->getAction();
		$this->Viewhtm = $this->router->getController() . "/" . $this->router->getUrl() . ".htm";
	}
	
	function HomeView()
	{

	}


    function fastLoginAction()
    {
        $request = Input('get.','','trim,addslashes');
        $stafferOne = $this->DataControl->getOne("smc_staffer", "staffer_id='{$request['staffer_id']}'");
        if ($stafferOne) {
            if($stafferOne['staffer_tokenencrypt'] == $request['token']){
                $ComPost = $this->DataControl->getFieldOne("gmc_staffer_postbe","postbe_id","staffer_id = '{$stafferOne['staffer_id']}' and school_id = '0'");
                $ScPost = $this->DataControl->getFieldOne("gmc_staffer_postbe","postbe_id","staffer_id = '{$stafferOne['staffer_id']}' and school_id <> '0'");
                if($ComPost && $ScPost){
                    $status = '0';
                }
                if(!$ComPost && $ScPost){
                    $status = '2';
                }
                if($ComPost && !$ScPost){
                    $status = '1';
                }
                $itaffer = array();
                $itaffer['staffer_id'] = $stafferOne['staffer_id'];
                $itaffer['company_id'] = $stafferOne['company_id'];
                $itaffer['school_id'] = $request['school_id'];
                $itaffer['token'] = $stafferOne['staffer_tokencode'];
                $itaffer['status'] = $status;
                $itaffer['isAdmin'] = $stafferOne['account_class'];
                $this->intSession->setCookiearray("istaffer",$itaffer,'1');
                header("Location:/");
            }else{
                header("Location:/");
            }
        } else {
            header("Location:/");
        }
    }
	//魔术方法
	function __destruct()
	{
		if ($this->c =='Wait') {
			$this->smarty->assign("u", $this->u);
			$this->smarty->assign("t", $this->t);
			$this->smarty->assign("Viewhtm", $this->Viewhtm);
			$this->display($this->Viewhtm);
			exit;
		}
	}
}