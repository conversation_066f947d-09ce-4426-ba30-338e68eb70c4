<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2018/12/29
 * Time: 10:09
 */

namespace Work\Controller\Tephone;


class NoticeController extends viewTpl
{
	public $u;
	public $t;
	public $c;
	
	function __construct()
	{
		parent::__construct();
		if(!$this->check_login()){
			$this->LoginView();
		}
		$this->u = $this->router->getController();
		$this->t = $this->router->getUrl();
		$this->c = $this->router->getAction();
		$this->Viewhtm = $this->router->getController() . "/" . $this->router->getUrl() . ".htm";
		$this->smarty->assign("istaffer", $this->istaffer);
	}
	

	
	function HomeView()
	{
		
		$request = Input('get.','','trim,addslashes');
//		$this->ThisVerify($request);//验证账户
//        if ($request['school_id'] =="" ){
////
//			$school_id = $this->istaffer['school_id'];
//		}else{
//			$school_id = $request['school_id'];
//		}
//
//		$Model = new \Model\Smc\AffairsModel();
//		$pamArray['school_id'] =$school_id;
//		$pamArray['pid'] = $this->istaffer['postbe_id'];
//		$pamArray['staffer_id'] = $this->istaffer['staffer_id'];
//		$result = $Model->getNotice($pamArray);
//		ajax_return($result);
	}
	
	function SchoolNoticeView(){
		
		$request = Input('get.','','trim,addslashes');
		 $this->c="notice";
		if ($request['school_id'] =="" ){
//
			$school_id = $this->istaffer['school_id'];
		}else{
			$school_id = $request['school_id'];
		}
		
		$Model = new \Model\Smc\AffairsModel();
		$pamArray['school_id'] =$school_id;
		$pamArray['pid'] = $this->istaffer['postbe_id'];
		$pamArray['staffer_id'] = $this->istaffer['staffer_id'];
		$result = $Model->getNotice($pamArray);
		ajax_return($result);
		
	}
	
	
	
	
	function NoticeOneView()
	{
		
		 $this->c ="Notice";
		$request = Input('get.','','trim,addslashes');
//		$this->ThisVerify($request);//验证账户
//
		$Model = new \Model\Smc\AffairsModel();
		
		$result = $Model->getNoticeDetail($request);
		ajax_return($result);
	}
	
	public function __call($name, $arguments)
	{
		$this->Viewhtm = "under.htm";
		// Note: value of $name is case sensitive.
		//echo "Calling object method '$name' ". implode(', ', $arguments). "\n";
	}
	
	//魔术方法
	function __destruct()
	{
		if ($this->c == 'Wait') {
			$this->smarty->assign("u", $this->u);
			$this->smarty->assign("t", $this->t);
			$this->smarty->assign("Viewhtm", $this->Viewhtm);
			$this->display($this->Viewhtm);
//            $this->display("Home.html");
			exit;
		}
	}
	
	
}