<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2018/12/29
 * Time: 10:09
 */

namespace Work\Controller\Tephone;


class IntentionController extends viewTpl
{
	public $u;
	public $t;
	public $c;
	public $istaffer;
	public $Viewhtm;
	
	function __construct()
	{
		parent::__construct();
		if (!$this->check_login()) {
			$this->LoginView();
		}
		$this->u = $this->router->getController();
		$this->t = $this->router->getUrl();
		$this->c = $this->router->getAction();
		$this->Viewhtm = $this->router->getController() . "/" . $this->router->getUrl() . ".htm";
		$this->smarty->assign("istaffer", $this->istaffer);
	}
	
	
	function MyIntentionView()
	{
		$request = Input('get.','','trim,addslashes');

        $markerOne = $this->DataControl->getFieldOne("crm_marketer","marketer_id","staffer_id='{$this->istaffer['staffer_id']}'");
		if (isset($request['school_id']) && $request['school_id'] != '') {
		} else {
			$request['school_id'] = $this->istaffer['school_id'];
		}
		if (!isset($request['client_starttime']) || $request['client_starttime'] == '' || $request['client_endtime'] == '') {
			$request['client_starttime'] = date("Y-m-d");
			$request['client_endtime'] = date("Y-m-d");
		}
		//model
		$Model = new \Model\Crm\IndexModel();
		$paramArray['school_id'] = $request['school_id'];
		$paramArray['client_starttime'] = $request['client_starttime'];
		$paramArray['client_endtime'] = $request['client_endtime'];
		$paramArray['staffer_id'] = $this->istaffer['staffer_id'];
		$paramArray['company_id'] = $this->istaffer['company_id'];
		
		
		$dataList = $Model->ClinetCount($paramArray);
		
		if (!$dataList) {
			$dataList = array();
		}
//		$field = array();
//		$field["client_allnum"] = "客户数量";
//		$field["client_addnum"] = "新增";
//		$field["track_nonum"] = "待跟进";
//		$field["track_invitenum"] = "邀约/试听";
//		$field["track_conversionnum"] = "转正";
//
		$this->smarty->assign("dataList", $dataList);
		$weekArray['school_id'] = $request['school_id'];

		$WeekAll = GetWeekAll(date("Y-m-d",time()));
		//本周
//		$weekArray['client_starttime'] = date("Y-m-d",strtotime("this week"));
		$weekArray['client_starttime'] = $WeekAll['nowweek_start'];
		$weekArray['client_endtime'] =  date("Y-m-d",strtotime("{$weekArray['client_starttime']} + 6day"));
		$weekArray['staffer_id'] = $this->istaffer['staffer_id'];
		$weekArray['company_id'] = $this->istaffer['company_id'];
		$weekList = $Model->ClinetCount($weekArray);
	
		$this->smarty->assign("weekList", $weekList);

		//日程安排

         $today = date("Y-m-d");
          $evenList = $this->DataControl->selectClear("select e.event_time,e.event_remark from crm_event as e where  e.marketer_id='{$markerOne['marketer_id']}'and school_id='{$request['school_id']}' and event_time ='{$today}'");

        $this->smarty->assign("evenList", $evenList);

        //跟进学员
        $startTime = mktime(0, 0, 0, date("m"), date("d") - date("w") + 7 - 6, date("Y"));
        $startday =date("Y-m-d",$startTime);
        $enddayTime = mktime(23, 59, 59, date("m"), date("d",$startTime)+6  , date("Y"));
        $endday =date("Y-m-d",$enddayTime);
        $sql  ="select c.client_id,c.client_cnname,c.client_enname,c.client_img,c.client_sex,c.client_age,c.client_intention_level,
                (select track_createtime from crm_client_track as t WHERE t.client_id =r.client_id  order by track_createtime DESC limit 0,1  ) as  track_createtime,
                (select count(track_id) from crm_client_track as t WHERE t.client_id =r.client_id    limit 0,1  ) as  track_num
                from  crm_remind as  r
                Left join crm_client as c  On r.client_id = c.client_id 
                where r.client_id > 0  and  r.marketer_id ='{$markerOne['marketer_id']}' and  (r.remind_time between '{$startday}' and '{$endday}') ";

        $clientList = $this->DataControl->selectClear($sql);

        $this->smarty->assign("clientList", $clientList);

        $datatype = array();
        $datatype['school_id'] =$request['school_id'] ;

        $this->smarty->assign("clientList", $clientList);
        $this->smarty->assign("datatype", $datatype);



    }
	
	//意向客户管理 -列表
	function homeView()
	{
	
	}
	
	function getIntentionView()
	{
		$this->c = 'intent';
		$request = Input('get.','','trim,addslashes');
//		$this->ThisVerify($request);//验证账户
		
		if (!$request['school_id']) {
			$res = array('error' => '1', 'errortip' => "学校id错误", 'result' => array());
			ajax_return($res);
		}
		if (!$request['company_id']) {
			$res = array('error' => '1', 'errortip' => "公司id错误", 'result' => array());
			ajax_return($res);
		}
		
		
		
		$markerOne = $this->DataControl->getFieldOne("crm_marketer", "marketer_id", "staffer_id='{$request['staffer_id']}' and company_id='{$request['company_id']}'");
		$request['marketer_id'] = $markerOne['marketer_id'];
		
		
        $request['postbe_crmuserlevel'] =$this->istaffer['postbe_crmuserlevel'];
		
		$request['is_count']  =1;
		
		$Model = new \Model\Crm\IntentionClientModel();
		$clientList = $Model->getIntentionClientList($request);
//		c.client_sponsor,c.client_oh_month,c.client_push_month,c.client_email,c.client_createtime,c.client_address';
		$fieldstring = array('client_id', 'client_cnname', 'client_enname', 'client_sex', 'client_age', 'family_cnname', 'client_mobile','invite_visittime', 'client_intention_level', 'client_source', 'marketer_name', 'fu_marketer_name', 'client_status', 'client_sponsor', 'activity_name', 'channel_name', 'client_oh_month', 'client_push_month', 'client_email', 'client_address', 'client_createtime');
		$fieldname = array('客户id', '姓名', '英文名', '性别', '年龄', '主要联系人', '主要联系手机','柜询时间', '意向星级', '招生渠道类型', '主要负责人', '副负责人', '跟踪状态', '介绍人', '活动名称', '招生渠道明细', 'OH月份', 'PUSH月份', '邮箱', '联系地址', '创建时间', '能否跟进');
		$fieldcustom = array('0', "0", "1", "1", '1', "1", "1", "1",'1', "1", "1", '1', '1', "1", '1', '1', "0", "0", "1", "1", '0', '0');
		$fieldshow = array('0', "1", "1", "1", "1", '1', "1", "1", '1',"1", "1", '1', '1', '0', '0', '0', '0', '0', '0', '0', '0', '0');
		
		
		$field = array();
		for ($i = 0; $i < count($fieldstring); $i++) {
			$field[$i]["fieldname"] = trim($fieldstring[$i]);
			$field[$i]["fieldstring"] = trim($fieldname[$i]);
			$field[$i]["custom"] = trim($fieldcustom[$i]);
			$field[$i]["show"] = trim($fieldshow[$i]);
		}
		$field[8]["islevel"] = "true";
		
		$result = array();
		$result['fieldcustom'] = 0;
//		$result['field'] = $field;
		
		if ($clientList['list']) {
			
			$result['list'] = $clientList['list'];
		} else {
			
			$result['list'] = array();
		}
		if (isset($clientList['allnums']) && $clientList['list'] != "") {
			$allnum = intval($clientList['allnums']);
		} else {
			$allnum = 0;
		}
		if ($result['list']) {
			$res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result, "allnum" => $allnum);
		} else {
			$res = array('error' => '1', 'errortip' => "暂无意向客户信息", 'result' => $result, "allnum" => $allnum);
		}
		
		ajax_return($res);
	}
	
	//招生名单线索---查看单个客户的跟踪记录
	
	function followRecordView()
	{
		$request = Input('get.','','trim,addslashes');
		$datatype['client_id'] = $request['client_id'];
		$datatype['school_id'] = $request['school_id'];

		$ClientOne = $this->DataControl->selectOne("select client_cnname,client_enname,client_img,client_img,client_sex,client_mobile,client_birthday,client_intention_level from crm_client where client_id='{$request['client_id']}' ");
		$this->smarty->assign("ClientOne", $ClientOne);
		$this->smarty->assign("datatype", $datatype);
	}
	
	function addRecordView()
	{
		$requset = Input('get.','','trim,addslashes');
		$datatype['client_id'] =$requset['client_id'];
		$company_id = $this->istaffer['company_id'];
		$commodeList = $this->DataControl->getFieldquery('crm_code_commode', 'commode_id,commode_name', "company_id={$company_id}");
		$this->smarty->assign('commodeList',$commodeList);
		
		$comment = $this->DataControl->getFieldquery('crm_code_tracenote', 'tracenote_code,tracenote_remk', "company_id={$company_id}");
		$this->smarty->assign('comment',$comment);
		
		$where  = "1 and m.company_id='{$company_id}' and  p.school_id ='{$requset['school_id']}' and p.postbe_status =1 and postbe_iscrmuser = 1 ";
		$receptUser = $this->DataControl->selectClear("SELECT m.marketer_id ,marketer_name
			FROM
			crm_marketer AS m
			LEFT JOIN  smc_staffer AS s  ON m.staffer_id = s.staffer_id
			LEFT JOIN gmc_staffer_postbe AS p ON p.staffer_id = m.staffer_id
			WHERE {$where} and  p.postbe_isreceptionuser = 1  group by  m.marketer_id ");
		if(!$receptUser){
			$receptUser = array();
		}
		
		$objectList =  $this->DataControl->selectClear("select co.object_code,co.object_name from crm_code_object  as co where 1 ");
		
		$this->smarty->assign('objectList',$objectList);
		$this->smarty->assign('receptUser',$receptUser);
		$this->smarty->assign('datatype',$datatype);
		
		
	}
	
	
	function clientOneTrackView()
	{
		$this->c = "track";
		$request = Input('get.','','trim,addslashes');
		$client_id = $request['client_id'];
		
		if (!$client_id) {
			$res = array('error' => '1', 'errortip' => "客户id有误", 'result' => array());
			ajax_return($res);
		}
		$Model = new \Model\Crm\ClientModel();
		$dataOne = $Model->getclientTrackList($request);
		if ($dataOne) {
			foreach ($dataOne as $key => $value) {
				$value['track_createtime'] = date('Y-m-d H:i:s');
			}
		}
		$field['marketer_name'] = "负责人";
		$field['track_linktype'] = "沟通方式";
		$field['client_img'] = "客户头像";
		$field['track_followmode'] = "本次跟进模式0普通回访1柜询2视听3转正-1无意向跟进";
		$field['track_followuptype'] = "下次跟进类型";  //0普通跟进1提醒跟进
		$field['track_followuptime'] = "下次跟进时间";
		$field['commode_name'] = "沟通类型";
		$field['track_visitingtime'] = "柜询/视听时间";
		$field['main_marketer_name'] = "主负责人";
		$field['track_note'] = "沟通内容";
		$field['track_createtime'] = "创建时间";
		$field['class_cnname'] = "试听班级";
		$field['object_name'] = "沟通对象";
		$result['fieldcustom'] = 0;
		$result['field'] = $field;
		
		if ($dataOne) {
			$result['list'] = $dataOne;
			$res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
		} else {
			$result['list'] = array();
			$res = array('error' => '1', 'errortip' => "暂无跟踪记录", 'result' => $result);
		}
		
		ajax_return($res);
		
	}
	
	
	//招生名单线索 -- 跟进客户
	function trackClientView()
	{
		$this->c="trackClient";
		
		$request = Input('post.','','trim,addslashes');
	
//		$this->ThisVerify($request);//验证账户
		
		$marketer_id= $this->DataControl->getFieldOne("crm_marketer",'marketer_id',"staffer_id='{$this->istaffer['staffer_id']}' and company_id ='{$this->istaffer['company_id']}'");
		$request['marketer_id'] = $marketer_id['marketer_id'];
		if (!$request['client_id']) {
			$res = array('error' => '1', 'errortip' => "客户id有误", 'result' => array());
			ajax_return($res);
		}
		if (!$request['company_id']) {
			$res = array('error' => '1', 'errortip' => '公司id有误', 'result' => array());
			ajax_return($res);
		}
		if (($request['track_followmode'] == 1 || $request['track_followmode'] == 2) && $request['receiver_name'] == "") {
			$res = array('error' => '1', 'errortip' => "请填写接待人", 'result' => array());
			ajax_return($res);
		}
		if (!$request['school_id']) {
			$res = array('error' => '1', 'errortip' => "学校id有误", 'result' => array());
			ajax_return($res);
		}
		if (isset($request['track_followmode']) && $request['track_followmode'] == "") {
			$res = array('error' => '1', 'errortip' => "请选择跟进类型", 'result' => array());
			ajax_return($res);
		}
		if (($request['track_followmode'] == 1) && $request['invite_visittime'] == "") {
			$res = array('error' => '1', 'errortip' => "请选择柜询时间", 'result' => array());
			ajax_return($res);
		}
		if( isset($request['track_note'])  && $request['track_note']==""){
			$res = array('error' => '1', 'errortip' => "请填写沟通内容", 'result' => array());
			ajax_return($res);
		}
//		if($request['track_followmode'] == 2 && $request['class_id']==""){
//			$res = array('error' => '1', 'errortip' => "请选择班级", 'result' => array());
//			ajax_return($res);
//		}
		
		if ($request['track_followuptype'] == 1 && $request['track_followuptime'] == "") {
			$res = array('error' => '1', 'errortip' => "请选择下次跟进时间", 'result' => array());
			ajax_return($res);
		}
		 
		 $clientOne = $this->DataControl->getFieldOne("crm_client","client_tracestatus","client_id='{$request['client_id']}'");
		 if($clientOne['client_tracestatus'] == 4){
			 $res = array('error' => '1', 'errortip' => "该客已经转正", 'result' => array());
			 ajax_return($res);
		 }
		$Model = new \Model\Crm\ClientModel();
		$bool = $Model->insertTrackClientOne($request);
		
		
		if ($bool == true) {
			
			if ($request['track_followmode'] == 0) {
				$word = "回访";
			} elseif ($request['track_followmode'] == 1) {
				$word = "柜询";
			} elseif ($request['track_followmode'] == 2) {
				$word = "试听";
			} elseif ($request['track_followmode'] == 3) {
				$word = "转正";
			} elseif ($request['track_followmode'] == -1) {
				$word = "无意向";
			}
			
			$res = array('error' => $Model->error, 'errortip' => $word . $Model->errortip, 'result' => array());
		} else {
			$res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
		}
		ajax_return($res);
	}
	
	
	//意向客户管理 -新增意向客户
	function addIntentionView()
	{
		$this->c = "add";
		$request = Input('post.','','trim,addslashes');

//		$this->ThisVerify($request);//验证账户
		if (empty($request['client_cnname'])) {
			$res = array('error' => '1', 'errortip' => '请填写用户名', 'result' => array());
			ajax_return($res);
		}
        if (empty($request['client_mobile'])) {
            $res = array('error' => '1', 'errortip' => '请填写手机号', 'result' => array());
            ajax_return($res);
        }
        if (empty($request['client_sex'])) {
            $res = array('error' => '1', 'errortip' => '请选择用户性别', 'result' => array());
            ajax_return($res);
        }
        if (empty($request['client_source'])) {
            $res = array('error' => '1', 'errortip' => '请选择渠道类型', 'result' => array());
            ajax_return($res);
        }

		if (empty($request['main_marketer_id'])) {
			 $marketer_id= $this->DataControl->getFieldOne("crm_marketer",'marketer_id',"staffer_id='{$this->istaffer['staffer_id']}' and company_id ='{$request['company_id']}'");
			$request['main_marketer_id'] = $marketer_id['marketer_id'];
		}
		$request['marketer_id'] = $request['main_marketer_id'] ;


		
		$Model = new \Model\Crm\IntentionClientModel();
		$Model->addIntentionClient($request);
		$res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
	
		ajax_return($res);
	}
	
	
	function addCustomerView()
	{
		$company_id=  $this->istaffer['company_id'];
			$courseList = $this->DataControl->getFieldquery('smc_code_coursecat', 'coursecat_id,coursecat_cnname,coursecat_branch', "company_id='{$company_id}' and  coursecat_iscrmadded =1");
		
		$this->smarty->assign('courseList',$courseList);
		
		$familylist = $this->DataControl->getFieldquery( 'crm_code_familyrelation','familyrelation_id,familyrelation_code,familyrelation_name','1');
		$this->smarty->assign('familylist',$familylist);
		
		$mediaList = $this->DataControl->getFieldquery('crm_code_frommedia', 'frommedia_id,frommedia_name', "company_id={$company_id}");
		
		$this->smarty->assign('mediaList',$mediaList);
	}
	
	
	function EditAction()
	{
		$request = Input('post.','','trim,addslashes');
		
		$data["track_note"] = $request['track_note'];
		$this->DataControl->updateData('crm_client_track', "track_id='{$request['track_id']}'", $data);
		
		ajax_return(array('error' => '0', 'errortip' => "修改成功", 'result' => array()));
	}
	
	//邀约统计
	function inviteCount($paramArray)
	{
		$request = Input('get.','','trim,addslashes');
		
		// 学校有Crm权限的人
		$markert_sql = "SELECT m.marketer_id
                FROM crm_marketer as m
                LEFT JOIN smc_staffer as s ON m.staffer_id=s.staffer_id
                LEFT JOIN gmc_staffer_postbe as p ON m.staffer_id=p.staffer_id
                WHERE  s.account_class <> '1' and s.company_id='{$paramArray['company_id']}' and p.school_id='{$paramArray['school_id']}'  GROUP BY m.marketer_id ";
		$markertList = $this->DataControl->selectClear($markert_sql);
		if ($markertList) {
			$arr_marketer_id = array_column($markertList, "marketer_id");
			$str_marketer_id = implode(',', $arr_marketer_id);
		} else {
			$str_marketer_id = '0';
		}
		
//		$sql = "select  from  where ";
//		$this->DataControl->selectClear();
		
		
	}
	
	
	
	function waitFollowView(){
	  $request  = Input('get.','','trim,addslashes');
	  $datatype = $request['school_id'];
	  $this->smarty->assign("datatype",$datatype);
	}
	
	function waitClientFollowView()
	{
		 $this->c="waitClientFollow";
		$request = Input('get.','','trim,addslashes');
//		$this->ThisVerify($request);//验证账户
		
		if (!$request['school_id']) {
			$res = array('error' => '1', 'errortip' => "学校id错误", 'result' => array());
			ajax_return($res);
		}
		if (!$request['company_id']) {
			$res = array('error' => '1', 'errortip' => "公司id错误", 'result' => array());
			ajax_return($res);
		}
		
		
		$markerOne = $this->DataControl->getFieldOne("crm_marketer", "marketer_id", "staffer_id='{$request['staffer_id']}' and company_id='{$request['company_id']}'");
		$request['marketer_id'] = $markerOne['marketer_id'];
//		if (!$request['marketer_id']) {
//			$res = array('error' => '1', 'errortip' => "登录人id错误", 'result' => array());
//			ajax_return($res);
//		}
		$request['client_tracestatus'] = '0';
		
		$Model = new \Model\Crm\IntentionClientModel();
		$clientList = $Model->getIntentionClientList($request);
//		c.client_sponsor,c.client_oh_month,c.client_push_month,c.client_email,c.client_createtime,c.client_address';
		$fieldstring = array('client_id', 'client_cnname', 'client_enname', 'client_sex', 'client_age', 'family_cnname', 'client_mobile', 'client_intention_level', 'client_source', 'marketer_name', 'fu_marketer_name', 'client_status', 'client_sponsor', 'activity_name', 'channel_name', 'client_oh_month', 'client_push_month', 'client_email', 'client_address', 'client_createtime');
		$fieldname = array('客户id', '姓名', '英文名', '性别', '年龄', '主要联系人', '主要联系手机', '意向星级', '媒体来源', '主要负责人', '副负责人', '跟踪状态', '介绍人', '活动名称', '渠道来源', 'OH月份', 'PUSH月份', '邮箱', '联系地址', '创建时间', '能否跟进');
		$fieldcustom = array('0', "0", "1", "1", '1', "1", "1", "1", "1", "1", '1', '1', "1", '1', '1', "0", "0", "1", "1", '0', '0');
		$fieldshow = array('0', "1", "1", "1", "1", '1', "1", "1", "1", "1", '1', '1', '0', '0', '0', '0', '0', '0', '0', '0', '0');
		
		
		$field = array();
		for ($i = 0; $i < count($fieldstring); $i++) {
			$field[$i]["fieldname"] = trim($fieldstring[$i]);
			$field[$i]["fieldstring"] = trim($fieldname[$i]);
			$field[$i]["custom"] = trim($fieldcustom[$i]);
			$field[$i]["show"] = trim($fieldshow[$i]);
		}
		$field[7]["islevel"] = "true";
		
		$result = array();
		$result['fieldcustom'] = 0;
//		$result['field'] = $field;
		
		if ($clientList['list']) {
			
			$result['list'] = $clientList['list'];
		} else {
			
			$result['list'] = array();
		}
		if (isset($clientList['allnums']) && $clientList['list'] != "") {
			$allnum = intval($clientList['allnums']);
		} else {
			$allnum = 0;
		}
		if ($result['list']) {
			$res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result, "allnum" => $allnum);
		} else {
			$res = array('error' => '1', 'errortip' => "暂无意向客户信息", 'result' => $result, "allnum" => $allnum);
		}
		
		ajax_return($res);
	}
	
	function alreadyFollowView()
	{
	}
	
	function  alreadyClientFollowView(){
		
		 $this->c = "alreadyClientFollow";
		$request = Input('get.','','trim,addslashes');

		if (!$request['school_id']) {
			$res = array('error' => '1', 'errortip' => "学校id错误", 'result' => array());
			ajax_return($res);
		}
		if (!$request['company_id']) {
			$res = array('error' => '1', 'errortip' => "公司id错误", 'result' => array());
			ajax_return($res);
		}
		
		
		$markerOne = $this->DataControl->getFieldOne("crm_marketer", "marketer_id", "staffer_id='{$request['staffer_id']}' and company_id='{$request['company_id']}'");
		$request['marketer_id'] = $markerOne['marketer_id'];

		$request['client_tracestatus']  = '-2';
		
		$Model = new \Model\Crm\IntentionClientModel();
		$clientList = $Model->getIntentionClientList($request);
		$fieldstring = array('client_id', 'client_cnname', 'client_enname', 'client_sex', 'client_age', 'family_cnname', 'client_mobile', 'client_intention_level', 'client_source', 'marketer_name', 'fu_marketer_name', 'client_status', 'client_sponsor', 'activity_name', 'channel_name', 'client_oh_month', 'client_push_month', 'client_email', 'client_address', 'client_createtime');
		$fieldname = array('客户id', '姓名', '英文名', '性别', '年龄', '主要联系人', '主要联系手机', '意向星级', '媒体来源', '主要负责人', '副负责人', '跟踪状态', '介绍人', '活动名称', '渠道来源', 'OH月份', 'PUSH月份', '邮箱', '联系地址', '创建时间', '能否跟进');
		$fieldcustom = array('0', "0", "1", "1", '1', "1", "1", "1", "1", "1", '1', '1', "1", '1', '1', "0", "0", "1", "1", '0', '0');
		$fieldshow = array('0', "1", "1", "1", "1", '1', "1", "1", "1", "1", '1', '1', '0', '0', '0', '0', '0', '0', '0', '0', '0');
		
		
		$field = array();
		for ($i = 0; $i < count($fieldstring); $i++) {
			$field[$i]["fieldname"] = trim($fieldstring[$i]);
			$field[$i]["fieldstring"] = trim($fieldname[$i]);
			$field[$i]["custom"] = trim($fieldcustom[$i]);
			$field[$i]["show"] = trim($fieldshow[$i]);
		}
		$field[7]["islevel"] = "true";
		
		$result = array();
		$result['fieldcustom'] = 0;
//		$result['field'] = $field;
		
		if ($clientList['list']) {
			
			$result['list'] = $clientList['list'];
		} else {
			
			$result['list'] = array();
		}
		if (isset($clientList['allnums']) && $clientList['list'] != "") {
			$allnum = intval($clientList['allnums']);
		} else {
			$allnum = 0;
		}
		if ($result['list']) {
			$res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result, "allnum" => $allnum);
		} else {
			$res = array('error' => '1', 'errortip' => "暂无意向客户信息", 'result' => $result, "allnum" => $allnum);
		}
		
		ajax_return($res);
	}
	
	//修改跟进内容
	function  editTrackNoteView(){
		 $request  = Input('post.','','trim,addslashes');
		 $this->c = "edit";
		 $data['track_note'] = $request['track_note'];
		 $this->DataControl->updateData("crm_client_track","track_id='{$request['track_id']}'", $data);
		 $res = array('error' => '1', 'errortip' =>"编辑成功", 'result' => array());
		 ajax_return($res);
	}
	
	
	
	//跟进统计
	function  intviteCountView(){
		
		$request = Input('get.','','trim,addslashes');
		
		$request['staffer_id'] = $this->istaffer['staffer_id'];
		$request['company_id'] = $this->istaffer['company_id'];
		$request['school_id'] = $this->istaffer['school_id'];
		$datawhere = " 1   ";
		//关键词
		if (isset($request['keyword']) && $request['keyword'] != '') {
			$datawhere .= " and m.marketer_name like '%{$request['keyword']}%' ";
		}
		
		$sql = "SELECT m.marketer_id,m.marketer_name,
				(select count(cp.client_id) from crm_client_principal as cp where cp.principal_leave =0 and cp.marketer_id =m.marketer_id) as client_num,
				( select count(t.client_id) from (select client_id,marketer_id from crm_client_invite as ci  group by ci.client_id) as t WHERE t.marketer_id = m.marketer_id ) as invite_allnum,
				( select count(t.client_id) from (select client_id,marketer_id,invite_isvisit from crm_client_invite as ci  group by ci.client_id) as t WHERE t.marketer_id = m.marketer_id and invite_isvisit =1) as invite_num
                FROM crm_marketer as m
                LEFT JOIN smc_staffer as s ON m.staffer_id=s.staffer_id
                LEFT JOIN gmc_staffer_postbe as p ON m.staffer_id=p.staffer_id
                WHERE {$datawhere} and s.account_class <> '1' and s.company_id='{$request['company_id']}' and p.school_id='{$request['school_id']}'  GROUP BY m.marketer_id ";
		
		$countList = $this->DataControl->selectClear($sql);
		if (!$countList){
			$countList = array();
		}
		$this->smarty->assign("countList",$countList);
	}
	


	
	//魔术方法
	public function __call($name, $arguments)
	{
		$this->Viewhtm = "under.htm";
		// Note: value of $name is case sensitive.
		//echo "Calling object method '$name' ". implode(', ', $arguments). "\n";
	}
	
	//魔术方法
	function __destruct()
	{
		if ($this->c == 'Wait') {
			$this->smarty->assign("u", $this->u);
			$this->smarty->assign("t", $this->t);
			$this->smarty->assign("Viewhtm", $this->Viewhtm);
			$this->display($this->Viewhtm);
//            $this->display("Home.html");
			exit;
		}
	}
	
	
}
