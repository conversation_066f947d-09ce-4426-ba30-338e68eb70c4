<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2018/12/29
 * Time: 10:09
 */

namespace Work\Controller\Tephone;


class PersonalController extends viewTpl
{
	public $u;
	public $t;
	public $c;
	public $istaffer;
	
	
	function __construct()
	{
		parent::__construct();
		if(!$this->check_login()){
			$this->LoginView();
		}
		$this->u = $this->router->getController();
		$this->t = $this->router->getUrl();
		$this->c = $this->router->getAction();
		$this->smarty->assign("istaffer", $this->istaffer);
		$this->Viewhtm = $this->router->getController() . "/" . $this->router->getUrl() . ".htm";
		
	}
 
	function HomeView()
	{
	
//		debug($this->istaffer);
//		$Model = new \Model\Gmc\PostModel();
//		$parmentArray = array();
//		$parmentArray['staffer_id'] = $this->istaffer['staffer_id'];
//		$parmentArray['company_id'] = $this->istaffer['company_id'];
////		$parmentArray['re_postbe_id'] = $this->istaffer['re_postbe_id'];
//
//		$parmentArray['school_id'] = $this->istaffer['school_id'];
//
//
//		$result = $Model->getOwnInfoApi($parmentArray);
		
//		$this->smarty->assign("ownInfo",$result['data'][''] );
		
  
//		ajax_return($result);
		
	}
	
	function outLoginView(){
		$this->intSession->setCookiearray("istaffer",array(),'1');
		$this->Viewhtm = "Login.htm";
	}
	
	//魔术方法
	public function __call($name, $arguments) {
		$this->Viewhtm = "under.htm";
		// Note: value of $name is case sensitive.
		//echo "Calling object method '$name' ". implode(', ', $arguments). "\n";
	}
	//魔术方法
	function __destruct()
	{
		if ($this->c =='Wait') {
			$this->smarty->assign("u", $this->u);
			$this->smarty->assign("t", $this->t);
			$this->smarty->assign("Viewhtm", $this->Viewhtm);
			$this->display($this->Viewhtm);
//            $this->display("Home.html");
			exit;
		}
	}
	
//	function
	
}