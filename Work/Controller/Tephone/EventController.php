<?php
/**
 * ============================================================================
 * 版权所有 : https://www.mohism.cn
 * 网站地址 : https://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2016/12/13
 * Time: 21:06
 */

namespace Work\Controller\Tephone;

class EventController extends viewTpl
{
    public $u;
    public $t;
    public $c;
    public $istaffer;
    public $Viewhtm;

    function __construct()
    {
        parent::__construct();
        if (!$this->check_login()) {
            $this->LoginView();
        }

        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
        $this->Viewhtm = $this->router->getController() . "/" . $this->router->getUrl() . ".htm";
        $this->smarty->assign("istaffer", $this->istaffer);
    }

    function HomeView()
    {

        $request = Input('get');
        if (!isset($request['yearMonth'])) {
            $request['yearMonth'] = date("Y-m");
        }
        $markerOne = $this->DataControl->getFieldOne("crm_marketer", "marketer_id", "staffer_id='{$this->istaffer['staffer_id']}'");
        $request['yearMonth'] = $request['yearMonth'] == "" ? date("Y-m-d") : $request['yearMonth'];
        $date = getthemonth($request['yearMonth']);
        //当前日期
        $sdefaultDate = date("Y-m-d");

        $sql = "select event_id,event_time
                from crm_event 
                where marketer_id='{$markerOne['marketer_id']}'  and event_time between '{$date[0]}' and '{$date[1]}' and school_id='{$this->istaffer['school_id']}'
                GROUP BY event_time";
        $mothListArray = $this->DataControl->selectClear($sql);

        if ($mothListArray) {
            foreach ($mothListArray as $k => &$v) {
                $v['year'] = date('Y', strtotime($v['event_time']));
                $v['month'] = date('m', strtotime($v['event_time']));
                $v['day'] = date('d', strtotime($v['event_time']));
                $v['week'] = date('w', strtotime($v['year'] - $v['month'] - $v['day']));
                $v['is_have'] = 1;
                $v['forbid'] = 0;
                unset($mothListArray[$k]['event_time']);
            }
            $monthArr = array_column($mothListArray, 'day');
        }

        $count = date('j', strtotime($date[1]));
        if ($mothListArray) {
            for ($i = 1; $i <= $count; $i++) {
                if ($i < 10) {
                    $i = '0' . $i;
                }
                if (!in_array($i, $monthArr)) {
                    $data['year'] = date('Y', strtotime($date[0]));
                    $data['month'] = date('m', strtotime($date[0]));
                    $data['day'] = $i;
                    $data['week'] = date('w', strtotime($data['year'] . "-" . $data['month'] . "-" . $data['day']));

                    if (($data['year'] . "-" . $data['month'] . "-" . $data['day']) == $sdefaultDate) {
                        $dateWeek[$i]['isnow'] = 1;
                    } else {
                        $dateWeek[$i]['isnow'] = 0;
                    }

                    $data['is_have'] = strval(-1);
                    $data['forbid'] = 0;
                    array_push($mothListArray, $data);
                }
                usort($mothListArray, function ($a, $b) {
                    if ($a['day'] == $b['day']) return 0;
                    return $a['day'] > $b['day'] ? 1 : -1;
                });
            }
        } else {
            $mothListArray = array();
            for ($i = 1; $i <= $count; $i++) {
                if ($i < 10) {
                    $i = '0' . $i;
                }
                $data = array();
                $data['year'] = date('Y', strtotime($date[0]));
                $data['month'] = date('m', strtotime($date[0]));
                $data['day'] = $i;
                $data['week'] = date('w', strtotime($data['year'] . "-" . $data['month'] . "-" . $data['day']));

                if (($data['year'] . "-" . $data['month'] . "-" . $data['day']) == $sdefaultDate) {
                    $dateWeek[$i]['isnow'] = 1;
                } else {
                    $dateWeek[$i]['isnow'] = 0;
                }

                $data['is_have'] = strval(-1);
                $data['forbid'] = 0;
                array_push($mothListArray, $data);
            }
        }

        $last_mothListArray = array();
        if ($mothListArray[0]['week'] > 0) {
            $startweek = $mothListArray[0]['week'];
            for ($i = 1; $i <= $startweek; $i++) {
                $last_mothListArray[$startweek . 'a']['year'] = date('Y', strtotime("{$date[0]}-1 month"));
                $last_mothListArray[$startweek . 'a']['month'] = date('m', strtotime("{$date[0]}-1 month"));
                $last_mothListArray[$startweek . 'a']['day'] = date('d', strtotime("{$date[0]} -{$i} day"));
                $last_mothListArray[$startweek . 'a']['week'] = date('w', strtotime("{$date[0]} -{$i} day"));
                $last_mothListArray[$startweek . 'a']['is_have'] = '-2';
                $last_mothListArray[$startweek . 'a']['forbid'] = '1';
            }
        }

        $mothListArray = array_merge($last_mothListArray, $mothListArray);
        $mothListArray = array_values($mothListArray);

        $this->smarty->assign('mothList', $mothListArray);
        //今天的事项
        $today = date("Y-m-d");
        $evenList = $this->DataControl->selectClear("select e.event_time,e.event_remark from crm_event as e where  e.marketer_id='{$markerOne['marketer_id']}'and school_id='{$this->istaffer['school_id']}' and event_time ='{$today}'");

        $this->smarty->assign("evenList", $evenList);

        $datatype = array();
        $datatype['day_num'] = date('d');
        $this->smarty->assign("datatype", $datatype);
    }

    function EventOneView()
    {
        $this->c = 'EventOne';
        $request = Input('get.','','trim,addslashes');
        if (!isset($request['event_time']) || $request['event_time'] == '') {
            $res = array('error' => '1', 'errortip' => '必须输入查询时间', 'result' => array());
            ajax_return($res);
        }
        $Model = new \Model\Crm\IndexModel();
        $markerOne = $this->DataControl->getFieldOne("crm_marketer", "marketer_id", "staffer_id='{$this->istaffer['staffer_id']}'");
        $paramAarray = array();
        $paramAarray['school_id'] = $this->istaffer['school_id'];
        $paramAarray['marketer_id'] = $markerOne['marketer_id'];
        $dataList = $Model->eventOneApi($paramAarray);
        $dataList = $dataList == false ? array() : $dataList;
        $res = array('error' => '1', 'errortip' => '获取成功', 'result' => $dataList);
        ajax_return($res);
    }


    function AddEvenView()
    {
        $request = Input("get.");
        $datatype = array();
        $datatype['date'] = $datatype['date'] == '' ? date("Y-m-d") : date("Y-m-d", strtotime($request['date']));
        $this->smarty->assign("datatype", $datatype);
    }

    function AddEvenAction()
    {
        $request = Input("post.");
        if(isset($request['event_time']) && $request['event_time'] =="" ){
            $res = array('error' => '1', 'errortip' => '请输入时间', 'result' => array());
            ajax_return($res);
        }
        if(isset($request['event_remark']) && $request['event_remark'] =="" ){
            $res = array('error' => '1', 'errortip' => '请输入内容', 'result' => array());
            ajax_return($res);
        }
        $Model = new \Model\Crm\IndexModel();
        $markerOne = $this->DataControl->getFieldOne("crm_marketer", "marketer_id", "staffer_id='{$this->istaffer['staffer_id']}'");
        $paramAarray['company_id'] = $this->istaffer['company_id'];
        $paramAarray['school_id'] =  $this->istaffer['school_id'];
        $paramAarray['marketer_id'] = $markerOne['marketer_id'];
        $paramAarray['event_tag'] = $request['event_tag'];
        $paramAarray['event_time'] = $request['event_time'];
        $paramAarray['event_remark'] = $request['event_remark'];
        $bool =  $Model->addEventAction($paramAarray);
        if($bool){
            $res = array('error' => '1', 'errortip' => '新增成功', 'result' => array());
        }else{
            $res = array('error' => '1', 'errortip' => '新增失败', 'result' => array());
        }
        ajax_return($res);

    }

//魔术方法
    function __destruct()
    {
        if ($this->c == 'Wait') {
            $this->smarty->assign("u", $this->u);
            $this->smarty->assign("t", $this->t);
            $this->smarty->assign("Viewhtm", $this->Viewhtm);
            $this->display($this->Viewhtm);
//            $this->display("Home.html");
            exit;
        }
    }


}