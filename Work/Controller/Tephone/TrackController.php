<?php
/**
 * ============================================================================
 * 版权所有 : https://www.mohism.cn
 * 网站地址 : https://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2016/12/13
 * Time: 21:06
 */

namespace Work\Controller\Tephone;

class TrackController extends viewTpl
{
    public $u;
    public $t;
    public $c;
    public $istaffer;
    public $Viewhtm;

    function __construct()
    {
        parent::__construct();
        if(!$this->check_login()){
            $this->LoginView();
        }

        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
        $this->Viewhtm = $this->router->getController() . "/" . $this->router->getUrl() . ".htm";
        $this->smarty->assign("istaffer", $this->istaffer);

    }

    function  NeverTrackView(){

        $markerOne = $this->DataControl->getFieldOne("crm_marketer","marketer_id","staffer_id='{$this->istaffer['staffer_id']}'");

        $startTime = mktime(0, 0, 0, date("m"), date("d") - date("w") + 7 - 6, date("Y"));
        $startday =date("Y-m-d",$startTime);
        $enddayTime = mktime(23, 59, 59, date("m"), date("d",$startTime)+6  , date("Y"));
        $endday =date("Y-m-d",$enddayTime);
        $NeverNum = $this->DataControl->selectOne( "select count(r.client_id) as client_num from  crm_remind as r where  (r.remind_time between  '{$startday}' and '{$endday}') and  r.marketer_id ='{$markerOne['marketer_id']}' and r.client_id NOT IN (select client_id from crm_client_track where track_createtime between '{$startTime}' and '{$enddayTime}')");
        $AlreadyNum =  $this->DataControl->selectOne("select count(r.client_id) as client_num from  crm_remind as r where (r.remind_time between  '{$startday}' and '{$endday}') and  r.marketer_id ='{$markerOne['marketer_id']}' and r.client_id IN (select client_id from crm_client_track where track_createtime between  '{$startTime}' and  '{$enddayTime}') ");
        $dtatype = array();
        $dtatype['never_num'] =$NeverNum['client_num']==false?0:$NeverNum['client_num'];
        $dtatype['already_num'] =$AlreadyNum['client_num']==false?0:$AlreadyNum['client_num'];

        $sql  ="select c.client_id,c.client_cnname,c.client_img,c.client_sex,c.client_age,c.client_intention_level,
                (select track_createtime from crm_client_track as t WHERE t.client_id =r.client_id  order by track_createtime DESC limit 0,1  ) as  track_createtime,
                (select count(track_id) from crm_client_track as t WHERE t.client_id =r.client_id    limit 0,1  ) as  track_num
                from  crm_remind as  r
                Left join crm_client as c  On r.client_id = c.client_id 
                where r.client_id > 0  and  r.marketer_id ='{$markerOne['marketer_id']}' and  (r.remind_time between '{$startday}' and '{$endday}') and r.client_id NOT IN (select client_id from crm_client_track where track_createtime between '{$startTime}' and '{$enddayTime}')  ";

        $clientList = $this->DataControl->selectClear($sql);


        $this->smarty->assign("datatype", $dtatype);
        $this->smarty->assign("clientList", $clientList);

    }
    function AlreadyTrackView(){

        $markerOne = $this->DataControl->getFieldOne("crm_marketer","marketer_id","staffer_id='{$this->istaffer['staffer_id']}'");

        $startTime = mktime(0, 0, 0, date("m"), date("d") - date("w") + 7 - 6, date("Y"));
        $startday =date("Y-m-d",$startTime);
        $enddayTime = mktime(23, 59, 59, date("m"), date("d",$startTime)+6  , date("Y"));
        $endday =date("Y-m-d",$enddayTime);
        $NeverNum = $this->DataControl->selectOne( "select count(r.client_id) as client_num from  crm_remind as r where  (r.remind_time between  '{$startday}' and '{$endday}') and  r.marketer_id ='{$markerOne['marketer_id']}' and r.client_id NOT IN (select client_id from crm_client_track where track_createtime between '{$startTime}' and '{$enddayTime}')");
        $AlreadyNum =  $this->DataControl->selectOne("select count(r.client_id) as client_num from  crm_remind as r where (r.remind_time between  '{$startday}' and '{$endday}') and  r.marketer_id ='{$markerOne['marketer_id']}' and r.client_id IN (select client_id from crm_client_track where track_createtime between  '{$startTime}' and  '{$enddayTime}') ");
        $dtatype = array();
        $dtatype['never_num'] =$NeverNum['client_num']==false?0:$NeverNum['client_num'];
        $dtatype['already_num'] =$AlreadyNum['client_num']==false?0:$AlreadyNum['client_num'];

        $sql  ="select c.client_id,c.client_cnname,c.client_img,c.client_sex,c.client_age,c.client_intention_level,
                (select track_createtime from crm_client_track as t WHERE t.client_id =r.client_id  order by track_createtime DESC limit 0,1  ) as  track_createtime,
                (select count(track_id) from crm_client_track as t WHERE t.client_id =r.client_id    limit 0,1  ) as  track_num
                from  crm_remind as  r
                Left join crm_client as c  On r.client_id = c.client_id 
                where r.client_id > 0  and  r.marketer_id ='{$markerOne['marketer_id']}' and  (r.remind_time between '{$startday}' and '{$endday}')  and r.client_id IN (select client_id from crm_client_track where track_createtime between  '{$startTime}' and  '{$enddayTime}')   ";

        $clientList = $this->DataControl->selectClear($sql);


        $this->smarty->assign("datatype", $dtatype);
        $this->smarty->assign("clientList", $clientList);

    }

    //魔术方法
    function __destruct()
    {
        if ($this->c == 'Wait') {
            $this->smarty->assign("u", $this->u);
            $this->smarty->assign("t", $this->t);
            $this->smarty->assign("Viewhtm", $this->Viewhtm);
            $this->display($this->Viewhtm);
//            $this->display("Home.html");
            exit;
        }
    }


}