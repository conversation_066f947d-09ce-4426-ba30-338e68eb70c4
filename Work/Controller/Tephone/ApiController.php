<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2018/12/29
 * Time: 10:09
 */

namespace Work\Controller\Tephone;


class ApiController extends viewTpl
{
    public $u;
    public $t;
    public $c;

    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();

    }

    //用户获取token
    function getToken($params = array())
    {
        $stafferOne = $this->DataControl->getFieldOne('smc_staffer', "staffer_id,staffer_tokencode,staffer_tokenencrypt", "staffer_id='{$params['staffer_id']}'");
        if (!$stafferOne) {
            return false;
        }
        $md5tokenbar = base64_encode(md5($stafferOne["staffer_tokencode"] . date("Y-m-d")));
        if ($md5tokenbar == $stafferOne["staffer_tokenencrypt"]) {
            $token = $stafferOne["staffer_tokenencrypt"];
        } else {
            //目前这里注释是为了测试方便
            $tokencode = rand(111111, 999999);
            $md5tokenbar = base64_encode(md5($tokencode . date("Y-m-d")));
            $this->DataControl->query("UPDATE smc_staffer SET staffer_tokencode = '{$tokencode}',staffer_tokenencrypt = '{$md5tokenbar}' WHERE staffer_id ='{$stafferOne['staffer_id']}'");
            $token = $md5tokenbar;
//            $token = $stafferOne["staffer_tokenencrypt"];
        }
        return $token;
    }

    //用户获取token
    function getCompanyOneApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $companyOne = $this->DataControl->getFieldOne('gmc_company', "company_id,company_code,company_shortname,company_cnname,company_logo", "company_code = '{$request['fromcode']}'");
        if ($companyOne) {
            $field = array();
            $field["company_id"] = "序号";
            $field["company_code"] = "企业授权编号";
            $field["company_shortname"] = "企业简称";
            $field["company_cnname"] = "企业中文名称";
            $field["company_logo"] = "企业logo";

            $result = array();
            $result["field"] = $field;
            $result["data"] = $companyOne;
            ajax_return(array('error' => 0, 'errortip' => "企业信息获取成功!", 'result' => $result));
        } else {
            ajax_return(array('error' => 1, 'errortip' => "企业账户不存在，请确认授权码是否正确!"));
        }
    }

    //登陆验证
    function ThisVerify($request)
    {
        $paramArray = array();
        $paramArray['staffer_id'] = $request['staffer_id'];
        $paramArray['school_id'] = $request['school_id'];
        $paramArray['company_id'] = $request['company_id'];
        $paramArray['token'] = $request['token'];
        if (!$this->UserLimit($paramArray)) {
            $result = array();
            $result["list"] = array();
            $result["tokeninc"] = "0";
            $res = array('error' => '1', 'errortip' => "用户token失效", 'result' => $result);
            ajax_return($res);
        }
    }

    //用户的权限
    function stafferPostbeApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        if ($request['marketer_id']) {

        } else {
            $this->ThisVerify($request);//验证账户
        }
        $istaffer = array();
        $stafferOne = $this->DataControl->getFieldOne('smc_staffer', "staffer_id,staffer_leave,staffer_pass,account_class", " staffer_id = '{$request['staffer_id']}' and company_id = '{$request['company_id']}'");
        if ($stafferOne['account_class'] == '1') {
            $schoolOne = $this->DataControl->getFieldOne('smc_school', "school_id", "company_id = '{$request['company_id']}'", "order by school_istemp DESC");
            $istaffer['isCompany'] = '1';
            $istaffer['isSchool'] = '1';
            $istaffer['isCrm'] = '1';
        } else {
            $postroleOne = $this->DataControl->getFieldOne("gmc_staffer_postbe", "postrole_id,organize_id,school_id,postbe_iscrmuser", "postbe_id = '{$request['postbe_id']}'");
            if ($postroleOne['school_id'] == '0') {
                $schoolOne = $this->DataControl->selectOne("SELECT p.school_id FROM gmc_company_organizeschool AS p WHERE p.organize_id = '{$postroleOne['organize_id']}' limit 0,1");
                //集团角色
                $status = $this->DataControl->getFieldOne("gmc_company_postrole", "postpart_iscompanyuser,postpart_iscmsuser,postpart_iscrmuser", "postrole_id = '{$postroleOne['postrole_id']}'");
                if ($status['postpart_iscompanyuser'] == '1') {
                    $istaffer['isCompany'] = '1';
                } else {
                    $istaffer['isCompany'] = '0';
                }

                //校园角色
                $status = $this->DataControl->getFieldOne("gmc_company_postrole", "postpart_iscmsuser", "postrole_id = '{$postroleOne['postrole_id']}'");
                if ($status['postpart_iscmsuser'] == '1') {
                    $istaffer['isSchool'] = '1';
                } else {
                    $istaffer['isSchool'] = '0';
                }
                //CRM权限
                $status = $this->DataControl->getFieldOne("gmc_company_postrole", "postpart_iscrmuser", "postrole_id = '{$postroleOne['postrole_id']}'");
                if ($status['postpart_iscrmuser'] == '1') {
                    $istaffer['isCrm'] = '1';
                } else {
                    $istaffer['isCrm'] = '0';
                }
            } else {
                //集团角色
                $istaffer['isCompany'] = '0';
                //校园角色
                $istaffer['isSchool'] = '1';
                //CRM权限
                if ($postroleOne['postbe_iscrmuser'] == '1') {
                    $istaffer['isCrm'] = '1';
                } else {
                    $istaffer['isCrm'] = '0';
                }
                $schoolOne['school_id'] = $postroleOne['school_id'];
            }
        }

        $result = array();
        if ($istaffer) {
            $result["list"] = $istaffer;
            $result["school_id"] = $schoolOne['school_id'];
            $res = array('error' => 0, 'errortip' => '权限获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '权限获取信息', 'result' => $result);
        }
        ajax_return($res);
    }


    //账户密码登陆
    function pswdloginView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $companyOne = $this->DataControl->getFieldOne('gmc_company', "company_id", "company_code = '{$request['L_code']}'");
        if ($companyOne) {
            $stafferOne = $this->DataControl->getFieldOne('smc_staffer', "staffer_id,staffer_leave,staffer_pass,account_class", "(staffer_branch = '{$request['L_name']}' or staffer_mobile = '{$request['L_name']}') and company_id = '{$companyOne['company_id']}'");
            $ComPost = $this->DataControl->getFieldOne("gmc_staffer_postbe", "postbe_id", "staffer_id = '{$stafferOne['staffer_id']}' and school_id = '0'");
            $ScPost = $this->DataControl->getFieldOne("gmc_staffer_postbe", "postbe_id", "staffer_id = '{$stafferOne['staffer_id']}' and school_id <> '0'");
            if ($ComPost && $ScPost) {
                $status = '0';
            }
            if (!$ComPost && $ScPost) {
                $status = '2';
            }
            if ($ComPost && !$ScPost) {
                $status = '1';
            }

            $isAdmin = $stafferOne['account_class'];
            if ($stafferOne) {
                if ($stafferOne['staffer_leave'] == '0') {
                    $password = md5($request['L_pswd']);
                    if ($password == $stafferOne['staffer_pass']) {
                        $istaffer = array();
                        $istaffer['staffer_id'] = $stafferOne['staffer_id'];
                        $istaffer['company_id'] = $companyOne['company_id'];
                        if ($stafferOne['account_class'] == '1') {
                            $schoolOne = $this->DataControl->getFieldOne('smc_school', "school_id", "company_id = '{$companyOne['company_id']}'", "order by school_istemp DESC");
                            $istaffer['school_id'] = $schoolOne['school_id'];
                        } else {
                            $schoolOne = $this->DataControl->selectOne("select sp.school_id,sp.postpart_id,sp.post_id
                                FROM gmc_staffer_postbe as sp
                                where sp.staffer_id = '{$stafferOne['staffer_id']}' and sp.school_id <> '0' and sp.postbe_status = '1'
                                order by sp.postbe_ismianjob DESC,sp.postbe_ismianjob DESC limit 0,1 ");
                            if ($schoolOne && $schoolOne['postpart_id'] == '0') {
                                $postOne = $this->DataControl->getFieldOne('gmc_company_post', "postpart_id", "post_id = '{$schoolOne['post_id']}'", "order by post_id DESC limit 0,1");
                                if ($postOne['postpart_id'] !== '0') {
                                    $this->DataControl->updateData("gmc_staffer_postbe", "staffer_id = '{$stafferOne['staffer_id']}' and school_id = '{$schoolOne['school_id']}' and post_id = '{$schoolOne['post_id']}'", array("postpart_id" => $postOne['postpart_id']));
                                }
                            }

                            $istaffer['school_id'] = $schoolOne['school_id'] ? $schoolOne['school_id'] : 0;

                            if ($schoolOne['school_id'] == '0') {
                                $schoolOne = $this->DataControl->selectOne("select sp.school_id
                                FROM gmc_staffer_postbe as sp
                                where sp.staffer_id = '{$stafferOne['staffer_id']}' and sp.school_id <> '0' and sp.postbe_status = '1'
                                order by sp.postbe_ismianjob DESC limit 0,1 ");
                                $istaffer['school_id'] = $schoolOne['school_id'] ? $schoolOne['school_id'] : 0;
                            }
                            if ($schoolOne['school_id'] == '') {
                                $schoolOne = $this->DataControl->selectOne("select s.school_id FROM gmc_staffer_postbe as sp,gmc_company_organizeschool as s
where s.organize_id = sp.organize_id and sp.staffer_id = '{$stafferOne['staffer_id']}' and sp.school_id = '0' and sp.postbe_status = '1' order by sp.postbe_ismianjob DESC limit 0,1 ");
                                $istaffer['school_id'] = $schoolOne['school_id'];
                            }
                        }
                        $istaffer['token'] = $this->getToken($stafferOne);
                        $this->DataControl->updateData("smc_staffer", "staffer_id = '{$stafferOne['staffer_id']}'", array("staffer_lasttime" => time(), "staffer_lastip" => real_ip()));

                        $istaffer_info = $istaffer;
                        $istaffer_info['isAdmin'] = $isAdmin;
                        $istaffer_info['status'] = $status;
                        $this->intSession->setCookiearray("istaffer", $istaffer_info, '1');
                        ajax_return(array('error' => 0, 'errortip' => "登陆成功!", 'result' => $istaffer, 'isAdmin' => $isAdmin, 'status' => $status));
                    } else {
                        ajax_return(array('error' => 1, 'errortip' => "密码错误!"));
                    }
                } else {
                    ajax_return(array('error' => 1, 'errortip' => "您已离职，账户权限已被关闭!"));
                }
            } else {
                ajax_return(array('error' => 1, 'errortip' => "职工账户信息不存在!"));
            }
        } else {
            ajax_return(array('error' => 1, 'errortip' => "企业账户不存在，请确认授权码是否正确!"));
        }
    }

    //手机快速登录
    function mobileloginView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->c = "mobile";
        $companyOne = $this->DataControl->getFieldOne('gmc_company', "company_id", "company_code = '{$request['L_code']}'");
        if ($companyOne) {
            $stafferOne = $this->DataControl->getFieldOne('smc_staffer', "staffer_id,staffer_leave,staffer_pass,account_class", "(staffer_branch = '{$request['L_mobile']}' or staffer_mobile = '{$request['L_mobile']}') and company_id = '{$companyOne['company_id']}'");

            $ComPost = $this->DataControl->getFieldOne("gmc_staffer_postbe", "postbe_id", "staffer_id = '{$stafferOne['staffer_id']}' and school_id = '0'");
            $ScPost = $this->DataControl->getFieldOne("gmc_staffer_postbe", "postbe_id", "staffer_id = '{$stafferOne['staffer_id']}' and school_id <> '0'");
            if ($ComPost && $ScPost) {
                $status = '0';
            }
            if (!$ComPost && $ScPost) {
                $status = '2';
            }
            if ($ComPost && !$ScPost) {
                $status = '1';
            }

            $isAdmin = $stafferOne['account_class'];

            if ($stafferOne) {
                if ($stafferOne['staffer_leave'] == '0') {
                    $mobile = trim($request['L_mobile']);
                    $verifycode = trim($request['L_verifycode']);
                    $sendrz = $this->DataControl->getFieldOne('gmc_mislog', "mislog_sendcode", "mislog_mobile='{$mobile}' and mislog_tilte = '快速登录'", "order by mislog_time DESC");
                    if (!$sendrz || $sendrz['mislog_sendcode'] !== $verifycode) {
//                if(!isset($_COOKIE['mislog_sendcode']) || $_COOKIE['mislog_sendcode'] !== $verifycode){
                        $res = array('error' => '1', 'errortip' => '短信验证码错误!');
                        ajax_return($res);
                    } else {
                        $istaffer = array();
                        $istaffer['staffer_id'] = $stafferOne['staffer_id'];
                        $istaffer['company_id'] = $companyOne['company_id'];
                        $istaffer_info['isAdmin'] = $isAdmin;
                        $istaffer_info['status'] = $status;

                        if ($stafferOne['account_class'] == '1') {
                            $schoolOne = $this->DataControl->getFieldOne('smc_school', "school_id", "company_id = '{$companyOne['company_id']}'", "order by school_istemp DESC");
                            $istaffer['school_id'] = $schoolOne['school_id'];
                        } else {
                            $schoolOne = $this->DataControl->selectOne("select sp.school_id,sp.postpart_id,sp.post_id
                                FROM gmc_staffer_postbe as sp
                                where sp.staffer_id = '{$stafferOne['staffer_id']}' and sp.school_id <> '0' and sp.postbe_status = '1'
                                order by sp.postbe_ismianjob DESC,sp.postbe_ismianjob DESC limit 0,1 ");
                            if ($schoolOne && $schoolOne['postpart_id'] == '0') {
                                $postOne = $this->DataControl->getFieldOne('gmc_company_post', "postpart_id", "post_id = '{$schoolOne['post_id']}'", "order by post_id DESC limit 0,1");
                                if ($postOne['postpart_id'] !== '0') {
                                    $this->DataControl->updateData("gmc_staffer_postbe", "staffer_id = '{$stafferOne['staffer_id']}' and school_id = '{$schoolOne['school_id']}' and post_id = '{$schoolOne['post_id']}'", array("postpart_id" => $postOne['postpart_id']));
                                }
                            }

                            $istaffer['school_id'] = $schoolOne['school_id'] ? $schoolOne['school_id'] : 0;

                            if ($schoolOne['school_id'] == '0') {
                                $schoolOne = $this->DataControl->selectOne("select sp.school_id
                                FROM gmc_staffer_postbe as sp
                                where sp.staffer_id = '{$stafferOne['staffer_id']}' and sp.school_id <> '0' and sp.postbe_status = '1'
                                order by sp.postbe_ismianjob DESC limit 0,1 ");
                                $istaffer['school_id'] = $schoolOne['school_id'] ? $schoolOne['school_id'] : 0;
                            }
                            if ($schoolOne['school_id'] == '') {
                                $schoolOne = $this->DataControl->selectOne("select s.school_id FROM gmc_staffer_postbe as sp,gmc_company_organizeschool as s
where s.organize_id = sp.organize_id and sp.staffer_id = '{$stafferOne['staffer_id']}' and sp.school_id = '0' and sp.postbe_status = '1' order by sp.postbe_ismianjob DESC limit 0,1 ");
                                $istaffer['school_id'] = $schoolOne['school_id'];
                            }
                        }
                        $istaffer['token'] = $this->getToken($stafferOne);
                        $istaffer_info = $istaffer;
                        $istaffer_info['isAdmin'] = $isAdmin;
                        $istaffer_info['status'] = $status;
//
                        $this->intSession->setCookiearray("istaffer", $istaffer_info, '1');
                        $this->DataControl->updateData("smc_staffer", "staffer_id = '{$stafferOne['staffer_id']}'", array("staffer_lasttime" => time(), "staffer_lastip" => real_ip()));
                        ajax_return(array('error' => 0, 'errortip' => "登陆成功!", 'result' => $istaffer, 'isAdmin' => $isAdmin, 'status' => $status));
                    }
                } else {
                    ajax_return(array('error' => 1, 'errortip' => "您已离职，账户权限已被关闭!"));
                }
            } else {
                ajax_return(array('error' => 1, 'errortip' => "职工账户信息不存在!"));
            }
        } else {
            ajax_return(array('error' => 1, 'errortip' => "企业账户不存在，请确认授权码是否正确!"));
        }
    }

    //获取手机验证码
    function getComPostApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $ComPost = $this->DataControl->selectClear("SELECT
                p.postbe_id,
                o.organize_cnname as cnname,
                s.post_name
            FROM
                gmc_staffer_postbe AS p
                LEFT JOIN gmc_company_organize AS o ON p.organize_id = o.organize_id
                LEFT JOIN gmc_company_post AS s ON p.post_id = s.post_id
            WHERE
                p.staffer_id = '{$request['staffer_id']}' and p.school_id = 0");
        if ($ComPost) {
            $result = array();
            $result["data"] = $ComPost;
            $res = array('error' => '0', 'errortip' => '获取集团职务成功', 'result' => $result);
            ajax_return($res);
        } else {
            $res = array('error' => '1', 'errortip' => '暂无集团职务!', 'result' => array());
            ajax_return($res);
        }
    }

    function getScPostApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $ComPost = $this->DataControl->selectClear("SELECT
                p.postbe_id,
                c.school_cnname as cnname,
                s.post_name
            FROM
                gmc_staffer_postbe AS p
                left join smc_school as c on p.school_id = c.school_id
                LEFT JOIN gmc_company_post AS s ON p.post_id = s.post_id
            WHERE
                p.staffer_id = '{$request['staffer_id']}' and p.school_id > 0");
        if ($ComPost) {
            $result = array();
            $result["data"] = $ComPost;
            $res = array('error' => '0', 'errortip' => '获取校园职务成功', 'result' => $result);
            ajax_return($res);
        } else {
            $res = array('error' => '1', 'errortip' => '暂无校园职务!', 'result' => array());
            ajax_return($res);
        }
    }

    //切换职务
    function ChangePostApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $stafferOne = $this->DataControl->getFieldOne('smc_staffer', "staffer_id,staffer_leave,staffer_pass,account_class", "staffer_id = '{$request['staffer_id']}'");

        $ComPost = $this->DataControl->getFieldOne("gmc_staffer_postbe", "postbe_id", "staffer_id = '{$stafferOne['staffer_id']}' and school_id = '0'");
        $ScPost = $this->DataControl->getFieldOne("gmc_staffer_postbe", "postbe_id", "staffer_id = '{$stafferOne['staffer_id']}' and school_id > '0'");

        if ($ComPost != '' && $ScPost != '') {
            $status = '0';
        }
        if ($ComPost == '' && $ScPost != '') {
            $status = '2';
        }
        if ($ComPost != '' && $ScPost == '') {
            $status = '1';
        }

        $isAdmin = $stafferOne['account_class'];
        ajax_return(array('error' => 0, 'errortip' => "切换成功!", 'isAdmin' => $isAdmin, 'status' => $status));


    }

    function ComTokenApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $staffer_id = $this->DataControl->getFieldOne("crm_marketer", "staffer_id", "marketer_id = '{$request['marketer_id']}'");
        $token = $this->DataControl->getFieldOne("smc_staffer", "staffer_tokenencrypt", "staffer_id = '{$staffer_id['staffer_id']}'");
        ajax_return(array('error' => 0, 'errortip' => "获取成功!", 'token' => $token['staffer_tokenencrypt']));

    }


    function outLoginView()
    {
        $this->intSession->setCookiearray("istaffer", array(), '1');
    }


    function getFamilyRelationView()
    {

        $familylist = $this->DataControl->getFieldquery('crm_code_familyrelation', 'familyrelation_id,familyrelation_code,familyrelation_name', '1');
        if ($familylist) {
            $list = $familylist;
        } else {
            $list = array();
        }

        ajax_return(array('error' => 0, 'errortip' => "获取成功!", 'list' => $list));
    }


    function getClientChannelView($company_id)
    {
        $channelList = $this->DataControl->getFieldquery('crm_code_channel', 'channel_id,channel_name', "company_id={$company_id}");

        if ($channelList) {
            $list = $channelList;
        } else {
            $list = array();
        }
        ajax_return(array('error' => 0, 'errortip' => "获取成功!", 'list' => $list));
    }


//	获取意向课程
    function getIntentCourse($company_id)
    {
        $courseList = $this->DataControl->getFieldquery('smc_code_coursecat', 'coursecat_id,coursecat_cnname,coursecat_branch', "company_id='{$company_id}' and  coursecat_iscrmadded =1");

        if ($courseList) {
            $list = $courseList;
        } else {
            $list = array();
        }

        ajax_return(array('error' => 0, 'errortip' => "获取成功!", 'list' => $list));
    }

    //获取试听班级
    function getAuditionView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        if (!$request['school_id']) {
            $res = array('error' => '1', 'errortip' => "学校id错误", 'result' => array());
            ajax_return($res);
        }
        if (isset($request['audition_genre']) && $request['audition_genre'] !== '') {
            $smcData['audition_genre'] = $request['audition_genre'];
        } else {
            $res = array('error' => '1', 'errortip' => "请选择试听类型", 'result' => array());
            ajax_return($res);
        }
        $CourseModel = new \Model\Smc\CourseModel($request);
        if ($request['audition_genre'] == 0) {
            $dataList = $CourseModel->getPucList($request);
        } else {
            $dataList = $CourseModel->getAuditionHour($request);
        }
        $fieldstring = array('hour_id', 'class_id', 'course_id', 'class_cnname', 'class_branch', 'course_cnname', 'course_branch', 'hour_day', 'hour_time', 'classroom_cnname', 'staffer_cnname', 'class_num', 'hour_num');
        $fieldname = array('课时id', '班级id', '课程id', '班级名称', '班级编号', '课程名', '课程别编号', '上课日期', '上课时间', '教室', '教师', '在班人数', '计划/已上课时');
        $fieldcustom = array('0', '0', '0', '1', '1', "1", "1", "1", '1', '1', '1', '1');
        $fieldshow = array('0', '0', '0', '1', '1', "1", "1", "1", '1', '1', '1', '1');
        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["fieldname"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }
        $result['field'] = $field;
        $result['list'] = $dataList;
        if ($dataList) {

            $res = array('error' => '1', 'errortip' => "暂无试听班级信息", 'result' => $result);
        } else {
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        }

        ajax_return($res);
    }


    function getChannelView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $company_id = $request['company_id'];

        if (!$company_id) {
            $res = array('error' => '1', 'errortip' => "公司id有误", 'result' => array());
            ajax_return($res);
        }

        $Model = new \Model\Crm\PublicModel();
        $dataList = $Model->getClientChannel($request);
        $field['channel_id'] = "渠道id";
        $field['channel_name'] = "渠道名称";
        if ($dataList) {
            $result['list'] = $dataList;
        } else {
            $result['list'] = array();
        }

        $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        ajax_return($res);

    }


}
