<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2018/12/29
 * Time: 10:09
 */

namespace Work\Controller\Tephone;


class ClockingController extends viewTpl
{
	public $u;
	public $t;
	public $c;
	public $istaffer;
	public $Viewhtm;
	
	function __construct()
	{
		parent::__construct();
		if(!$this->check_login()){
			$this->LoginView();
		}
		$this->u = $this->router->getController();
		$this->t = $this->router->getUrl();
		$this->c = $this->router->getAction();
		$this->Viewhtm = $this->router->getController() . "/" . $this->router->getUrl() . ".htm";
		$this->smarty->assign("istaffer", $this->istaffer);
	}
	 
	 function HomeView(){
//		 debug($this->istaffer);
	 }
	
	function  WeekView(){
	
	}
	function  MothView(){
	
	}
	
	function  WeekClockingView(){
		  $this->c = "clock";
		  $request  = Input('get.','','trim,addslashes');
//		  $this->ThisVerify($request);
		  $Model = new \Model\Smc\ClockingModel();
		  $Clocking = $Model->getWeekClocking($request);
		
		  $fieldstring = array('teaching_id','class_id','class_cnname', 'hour_checkname', 'hour_starttime','hour_endtime','school_cnname', 'hourstudy_num', 'hour_inarrivenums');
		  $fieldname = array('ID','班级id','班级中文名', '考勤状态', '开始时间', '结束时间', '学校','活动名称','实际考勤人数','应到人数');
		  $fieldcustom = array('0','0',"1", "1", "1", "1", "1", "1", "1", "1");
		  $fieldshow  =  array('0','0',"1", "1", "1", "1", "1", "1", "1", "1");
		
		  $field = array();
		  for ($i = 0; $i < count($fieldstring); $i++) {
			  $field[$i]["fieldstring"] = trim($fieldname[$i]);
			  $field[$i]["fieldname"]   = trim($fieldstring[$i]);
			  $field[$i]["custom"]      = trim($fieldcustom[$i]);
			  $field[$i]["show"]        = trim($fieldshow[$i]);
		  }
		
		  $result['fieldcustom'] = 1;
		  $result['field'] = $field;
		  $result['list'] = $Clocking['list'];
		  $result['clocking'] = $Clocking['clocking'];
		
		  $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result,'allnum'=>0);
		  ajax_return($res);
		  
	  }
	
	function rollCallClassView()
	{
		$request = Input('get.','','trim,addslashes');
		$CourseModel = new \Model\Smc\CourseModel($request);
		$res = $CourseModel->rollCallClass($request);
		
		$fieldstring = array('hourstudy_id', 'hour_id', 'NO','student_id', 'student_branch', 'student_cnname', 'student_enname', 'family_mobile', 'fee', 'student_type', 'hourstudy_checkin','hour_checkinnum', 'clockinginlog_note', 'type');
		$fieldname   = array('记录id', '课时id', '学员id', '学员编号', '学员中文名', '学员英文名', '联系电话', '是否计费', '学员类型', '出勤状态','缺勤统计', '缺勤原因', '记录类型');
		$fieldcustom = array('0', '0', "0", "1", "1", "1", "1", "1", "1", "1", '1',"1", '0');
		$fieldshow   = array('0', '0', "0", "1", "1", "1", "1", "1", "1", "1", '1',"1", '0');
		
		$field = array();
		for ($i = 0; $i < count($fieldstring); $i++) {
			$field[$i]["fieldstring"] = trim($fieldstring[$i]);
			$field[$i]["fieldname"]   = trim($fieldname[$i]);
			$field[$i]["custom"]      = trim($fieldcustom[$i]);
			$field[$i]["show"]        = trim($fieldshow[$i]);
		}
		$field[9]["is_status"] = 1;
		$field[11]["is_reason"] = 0;
		
		$result = array();
//		$result["field"] = $field;
		if ($res['student']) {
			$result["list"] = $res['student'];
			$result["class"] = $res['class'];
			$result["is_checkin"] = $res['is_checkin'];
//			$res = array('error' => '0', 'errortip' => '获取信息', 'result' => $result);
		} else {
			$result["list"] = array();
			$result["class"] = $res['class'];
			$result["is_checkin"] = $res['is_checkin'];
			
//			$res = array('error' => '1', 'errortip' => '暂无需要点名上课的学员信息', 'result' => $result);
		}
		$this->smarty->assign("dataList", $result);
		$this->Viewhtm = $this->router->getController() . "/" . "courseNumber.htm";
		
	}
	
	
	public function __call($name, $arguments) {
		$this->Viewhtm = "under.htm";
		// Note: value of $name is case sensitive.
		//echo "Calling object method '$name' ". implode(', ', $arguments). "\n";
	}
	//魔术方法
	function __destruct()
	{
		if ($this->c =='Wait') {
			$this->smarty->assign("u", $this->u);
			$this->smarty->assign("t", $this->t);
			$this->smarty->assign("Viewhtm", $this->Viewhtm);
			$this->display($this->Viewhtm);
//            $this->display("Home.html");
			exit;
		}
	}
	
}
