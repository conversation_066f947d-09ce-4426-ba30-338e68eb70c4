<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2016/12/13
 * Time: 21:21
 */
namespace Work\Controller\Scshopapi;


class viewTpl {
    public $DataControl;
    public $router;

    public function __construct(){
        global $router;
        // 指定允许其他域名访问
        header('Access-Control-Allow-Origin:*');
        // 响应类型
        header('Access-Control-Allow-Methods:*');
        // 响应头设置
        header('Access-Control-Allow-Headers:x-requested-with,content-type');

        //数据库操
        $this->DataControl = new \Dbmysql();
        //操作类型
        $this->router = $router;
    }


    //本地权限校验入口
    function ThisVerify($request,$spl='1')
    {
        if (!intval($request['parenter_id'])) {
            $result = array();
            $result["tokeninc"] = "1";
            $result["isEnterBlank"] = "1";
            $result["tokenloss"] = "1";
            $res = array('error' => '1', 'errortip' => "家长id不存在", 'result' => $result);
            ajax_return($res);
        }
        if (empty($request['token'])) {
            $result = array();
            $result["tokeninc"] = "0";
            $result["tokenloss"] = "1";
            $res = array('error' => '1', 'errortip' => "未传入用户Token信息", 'result' => $result);
            ajax_return($res);
        }
        if($spl == '1'){
            if(isset($request['student_id']) && $request['student_id'] !==''){
                $familyOne = $this->DataControl->selectOne("select family_id from smc_student_family 
                 WHERE parenter_id = '{$request['parenter_id']}' and student_id = '{$request['student_id']}' ");
                if(!$familyOne) {
                    $result = array();
                    $result["tokeninc"] = "1";
                    $result["isEnterBlank"] = "1";
                    $result["tokenloss"] = "1";
                    $res = array('error' => '1', 'errortip' => "此学员未授权关联家长操作!", 'result' => $result);
                    ajax_return($res);
                }
            }

            $paramArray = array();
            $paramArray['parenter_id'] = $request['parenter_id'];
            $paramArray['company_id'] = $request['company_id'];
            $paramArray['token'] = $request['token'];
            if (!$this->UserLimit($paramArray)) {
                $result = array();
                $result["list"] = array();
                $result["tokeninc"] = "0";
                $result["tokenloss"] = "1";
                $res = array('error' => '1', 'errortip' => "用户token失效", 'result' => $result);
                ajax_return($res);
            }
        }else{
            $paramArray = array();
            $paramArray['parenter_id'] = $request['parenter_id'];
            $paramArray['company_id'] = $request['company_id'];
            $paramArray['token'] = $request['token'];
            if (!$this->UserLimit($paramArray)) {
                $result = array();
                $result["list"] = array();
                $result["tokeninc"] = "0";
                $result["tokenloss"] = "1";
                $res = array('error' => '1', 'errortip' => "用户token失效", 'result' => $result);
                ajax_return($res);
            }
        }
    }

    //第三方接口权限验证
    function UserLimit($paramArray){
        $stafferOne = $this->DataControl->getFieldOne("smc_parenter","parenter_tokenencrypt,parenter_tokencode","parenter_id='{$paramArray['parenter_id']}'");
        if($stafferOne){
            //token 改为半年后失效  --- 20230818改（因为 8.26开收费）
            $montharr = ['08','09','10','11','12','01'];
            $thismonth = date('m',time());
            if(in_array($thismonth,$montharr)){
                $md5tokenbar = base64_encode(md5($stafferOne["parenter_tokencode"].date("Y-07")));//-d
            }else{
                $md5tokenbar = base64_encode(md5($stafferOne["parenter_tokencode"].date("Y-01")));//-d
            }
//            $md5tokenbar = base64_encode(md5($stafferOne["parenter_tokencode"].date("Y-m")));//-d
            if($md5tokenbar != $paramArray['token']){
                return false;
            }else{
                return true;
            }
        }else{
            return false;
        }
    }

    //获取 奇趣 接口授权秘钥
    function getQiquAuthpriv(){
        $parameter = array();
        $parameter['timesteps'] = time();
        $parameter['apiuser_code'] = 'KddGmcUser';
        $parameter['company_id'] = '8888';

        $aeskey = 'KddGmc2QiQu%15tH';
        $aesiv = 'jdb2GmcWGsdffp3g';

        $aes = new \Aesencdec($aeskey, $aesiv);
        $parameterJson = json_encode($parameter, '1');
        $jmsting = $aes->encrypt($parameterJson);//解密

        $result = array();
        $result['timesteps'] = $parameter['timesteps'];
        $result['apiuser_code'] = $parameter['apiuser_code'];
        $result['veytoken'] = $jmsting;
        return $result;
    }

    //发送短信
    public function Sendmisgo($mobile,$mistxt,$tilte,$sendcode,$company_id='0'){
        $publicarray = array();
        $publicarray['company_id'] = $company_id;
        $minsendModel = new \Model\Api\SmsModel($publicarray);
        return $minsendModel->crmMisSend($mobile,$mistxt,$tilte,$sendcode);
    }

    //发送短信
    public function Sendmisgocom($mobile,$mistxt,$tilte,$sendcode,$company_id='0'){
        $publicarray = array();
        $publicarray['company_id'] = $company_id;
        $minsendModel = new \Model\Api\SmsModel($publicarray);
        return $minsendModel->gmcMisSend($mobile,$mistxt,$tilte,$sendcode);
    }

    public function addShopWorkLog($company_id,$parenter_id,$student_id,$module,$type,$content)
    {
        $logData = array();
        $logData['company_id'] = $company_id;
        $logData['parenter_id'] = $parenter_id;
        $logData['student_id'] = $student_id;
        $logData['worklog_module'] = $module;
        $logData['worklog_type'] = $type;
        $logData['worklog_content'] = $content;
        $logData['worklog_ip'] = real_ip();
        $logData['worklog_time'] = time();
        $this->DataControl->insertData('shop_parent_worklog', $logData);
    }

    function createOrderPid($initial){
        $Str = "ABCDEFGHIJKLMNOPQRSTUVWXYZ123456789";
        $rangtr = $Str[rand(0,34)].$Str[rand(0,34)].$Str[rand(0,34)].$Str[rand(0,34)];
        $rangtime = date("ymdHis",time());
        $rangnum = rand(10000,99999);
        $OrderPID = $initial.$rangtr.$rangtime.$rangnum;
        return $OrderPID;
    }

    //登录日志记录表
    function addStafferLoginLog($company_id,$staffer_id,$loginlog_type,$loginlog_source){
        $date = array();
        $date['company_id'] = $company_id;//集团ID
        $date['staffer_id'] = $staffer_id;//员工ID
        $date['loginlog_type'] = $loginlog_type;//登录端口 0 总入口  1 集团 2 校务 3 CRM 4 教务 5 助教 6 助学
        $date['loginlog_source'] = $loginlog_source;//客户端来源 0 PC 1 手机
        $date['loginlog_lastip'] = real_ip();
        $date['loginlog_time'] = time();
        $this->DataControl->insertData('imc_staffer_loginlog',$date);
        return true;
    }

    public function __call($method, $args) {
        echo "unknown method " . $method;
        return false;

    }
}
