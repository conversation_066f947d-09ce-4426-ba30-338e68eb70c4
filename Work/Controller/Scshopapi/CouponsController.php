<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/4/17
 * Time: 10:51
 */

namespace Work\Controller\Scshopapi;


class CouponsController extends viewTpl
{
    public $u;
    public $t;
    public $c;
    public $visitType = "api";
    public $DataControl;

    //预加载处理类
    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
        $this->api = $this->router->getApi();

        //数据库操作
        $this->DataControl = new \Dbmysql();
    }

    //获取手机验证码
    function getShareVerifyCodeView()
    {
        $request = Input('get.', '', 'trim,addslashes,strip_tags');
//        $this->ThisVerify($request);//验证账户
        $company_id = $request['company_id'];
        $mobile = $request['mobile'];
        //一小时内发送次数
        $mintime = time() - 3600;
        $mislognum = $this->DataControl->selectOne("select count(mislog_id) as mislognum from crm_mislog where mislog_mobile='{$mobile}' and mislog_tilte = '微商城海报分享' and mislog_time >= '{$mintime}' limit 0,1 ");
        if ($mislognum['mislognum'] > 5) {
            $res = array('error' => '1', 'errortip' => '您的手机已超出发送验证码次数，请联系客服！');
            ajax_return($res, $request['language_type']);
        }
        //最近一次发送时间
        $sendmisrz = $this->DataControl->getFieldOne('crm_mislog', "mislog_time", "mislog_mobile='{$mobile}' and mislog_tilte = '微商城海报分享'", "order by mislog_time DESC");
        if ($sendmisrz && (time() - $sendmisrz['mislog_time']) < 60) {
            $res = array('error' => '1', 'errortip' => '验证码已发送！');
            ajax_return($res, $request['language_type']);
        } else {
            $tilte = "微商城海报分享";
            $sendcode = rand(111111, 999999);
            $contxt = "用户您好！您的手机验证码为{$sendcode}，请完成验证，如非本人操作，请忽略本短信。";
            //短信发送
            if ($this->Sendmisgo($mobile, $contxt, $tilte, $sendcode, $company_id)) {
                $res = array('error' => '0', 'errortip' => '发送成功', "bakfuntion" => "okmotify");
                ajax_return($res, $request['language_type']);
            } else {
                $res = array('error' => '1', 'errortip' => '发送失败!', "bakfuntion" => "errormotify");
                ajax_return($res, $request['language_type']);
            }
        }
    }

    /**
     * 模拟post进行url请求
     * @param string $url
     * @param array $post_data
     */
    function request_post($url = '', $post_data = array())
    {
        if (empty($url) || empty($post_data)) {
            return false;
        }

        $o = "";
        foreach ($post_data as $k => $v) {
            $o .= "$k=" . urlencode($v) . "&";
        }
        $post_data = substr($o, 0, -1);

        $postUrl = $url;
        $curlPost = $post_data;
        $ch = curl_init();//初始化curl
        curl_setopt($ch, CURLOPT_URL, $postUrl);//抓取指定网页
        curl_setopt($ch, CURLOPT_HEADER, 0);//设置header
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);//要求结果为字符串且输出到屏幕上
        curl_setopt($ch, CURLOPT_POST, 1);//post提交方式
        curl_setopt($ch, CURLOPT_POSTFIELDS, $curlPost);
        $data = curl_exec($ch);//运行curl
        curl_close($ch);

        return $data;
    }

    //分享海报留名单的页面
    function getJoinStuShareView()
    {
        $request = Input('post.', '', 'trim,addslashes,strip_tags');
//        $this->ThisVerify($request);//验证账户

        if ($request['poster_id'] == '') {
            $res = array('error' => '1', 'errortip' => '请输入海报id!', "bakfuntion" => "errormotify");
            ajax_return($res);
        }
        if ($request['student_id'] == '') {
            $res = array('error' => '1', 'errortip' => '请输入学员id!', "bakfuntion" => "errormotify");
            ajax_return($res);
        }
        if ($request['parenter_id'] == '') {
            $res = array('error' => '1', 'errortip' => '请输入家长id!', "bakfuntion" => "errormotify");
            ajax_return($res);
        }
        if ($request['school_branch'] == '') {
            $res = array('error' => '1', 'errortip' => '请输入校区编号!', "bakfuntion" => "errormotify");
            ajax_return($res);
        }

        $stuOne = $this->DataControl->selectOne("select * from smc_student WHERE student_id = '{$request['student_id']}' ");
        if (!$stuOne) {
            $res = array('error' => '1', 'errortip' => '推荐学员信息不存在!', "bakfuntion" => "errormotify");
            ajax_return($res);
        }
        $clientOne = $this->DataControl->selectOne("select * from crm_client WHERE client_mobile = '{$request['adf_mobile']}' ");
        //client_cnname = '{$request['adf_kidname']}' and 戚总让调整手机号即可 20201119
        if ($clientOne) {
            $res = array('error' => '1', 'errortip' => '被推荐学员信息已存在!', "bakfuntion" => "errormotify");
            ajax_return($res);
        }

        if ($request['adf_name'] == '') {
            $res = array('error' => '1', 'errortip' => '请输入您的称呼!', "bakfuntion" => "errormotify");
            ajax_return($res);
        }
        if ($request['adf_kidname'] == '') {
            $res = array('error' => '1', 'errortip' => '请输入您的孩子姓名!', "bakfuntion" => "errormotify");
            ajax_return($res);
        }
        if ($request['adf_mobile'] == '') {
            $res = array('error' => '1', 'errortip' => '请输入您的手机号码!', "bakfuntion" => "errormotify");
            ajax_return($res);
        }
        if ($request['Authcode'] == '') {
            $res = array('error' => '1', 'errortip' => '请输入您获取的短信验证码!', "bakfuntion" => "errormotify");
            ajax_return($res);
        }

        //地址   adf_address

        $sendmisrz = $this->DataControl->getOne('crm_mislog', "mislog_mobile='{$request['adf_mobile']}' and mislog_tilte = '微商城海报分享'", "order by mislog_time DESC");
        if ($sendmisrz['mislog_sendcode'] !== $request['Authcode'] && $request['Authcode'] !== 'mohism' && $request['Authcode'] !== 'kidcastle') {
            $res = array('error' => '1', 'errortip' => '短信验证码错误!', "bakfuntion" => "errormotify");
            ajax_return($res);
        } else {
            $name = $this->DataControl->selectOne("
SELECT
	st.staffer_id, 
	st.staffer_cnname 
FROM
	smc_student_study AS s
	LEFT JOIN smc_class_teach AS ct ON s.class_id = ct.class_id
	LEFT JOIN smc_staffer AS st ON st.staffer_id = ct.staffer_id
	LEFT JOIN smc_class AS sc ON sc.class_id = s.class_id
	LEFT JOIN smc_course AS sco ON sco.course_id = sc.course_id 
WHERE
	s.student_id = '{$request['student_id']}' 
	AND s.study_isreading = 1 
	AND ct.teach_type = 0 
	AND ct.teach_status = 0 
	AND sco.coursetype_id = 65 
GROUP BY
	s.study_id");
            if(!$name){
                $name = $this->DataControl->selectOne("
SELECT
	st.staffer_id, 
	st.staffer_cnname 
FROM
	smc_student_study AS s
	LEFT JOIN smc_class_teach AS ct ON s.class_id = ct.class_id
	LEFT JOIN smc_staffer AS st ON st.staffer_id = ct.staffer_id
	LEFT JOIN smc_class AS sc ON sc.class_id = s.class_id
	LEFT JOIN smc_course AS sco ON sco.course_id = sc.course_id 
WHERE
	s.student_id = '{$request['student_id']}' 
	AND s.study_isreading = 1 
	AND ct.teach_type = 0 
	AND ct.teach_status = 0 
GROUP BY
	s.study_id");
            }

            if(!$name){
                $name = $this->DataControl->selectOne("
SELECT
	st.staffer_id, 
	st.staffer_cnname 
FROM
	smc_student_study AS s
	LEFT JOIN smc_class_teach AS ct ON s.class_id = ct.class_id
	LEFT JOIN smc_staffer AS st ON st.staffer_id = ct.staffer_id
	LEFT JOIN smc_class AS sc ON sc.class_id = s.class_id
	LEFT JOIN smc_course AS sco ON sco.course_id = sc.course_id 
WHERE
	s.student_id = '{$request['student_id']}' 
	AND s.study_isreading <> 1 
	AND ct.teach_type = 0 
	AND ct.teach_status = 0 
GROUP BY
	s.study_id");
            }
            //生成有效名单
            $url = 'https://crmapi.kedingdang.com/PhoneActivity/addPhoneActivityAction';
            $crmData = array();
            $crmData['isschool'] = 1;
            $crmData['school_id'] = $request['school_id'];
            $crmData['activity_id'] = $request['poster_id'];
            $crmData['client_acttype'] = 3;
            $crmData['client_cnname'] = $request['adf_kidname'];
            $crmData['client_mobile'] = $request['adf_mobile'];
            $crmData['client_sponsor'] = $stuOne['student_cnname'];
            $crmData['client_teachername'] = $name['staffer_cnname'];
            $crmData['client_teacherid'] = $name['staffer_id'];
            $crmData['client_stubranch'] = $stuOne['student_branch'];//推荐学员编号
            $crmData['client_address'] = $request['adf_address'];//填写的地址
            $crmData['company_id'] = $request['company_id'];//集团id
            //20211109  产品像戚总确认 默认渠道
            $crmData['client_source'] = '转介绍';//集团id
            $crmData['channel_id'] = '107';//集团id

            $crmData['client_patriarchname'] = $request['adf_name'];//家长姓名
            $res = request_by_curl($url, dataEncode($crmData), "POST", array());
            print_r($res);
            die;//不用注释目前这个是正确的，在那边给了返回值
//            $json_play = new \Webjson();
//            $cardarray = $json_play->decode($res,"1");
//            if($cardarray['error']=='1'){
//                ajax_return(array('error' => 0,'errortip' => "你的信息提交成功！",'result' => array()));
//            }else{
//                ajax_return(array('error' => 1,'errortip' => "提交失败！",'result' => array()));
//            }
        }
    }

    //创建海报
    function CreateShareposterView()
    {
        $request = Input('post.', '', 'trim,addslashes');
//        $this->ThisVerify($request);//验证账户

        $count = $this->DataControl->getFieldOne("crm_sell_activity", "activity_sharecount", "activity_id = '{$request['shareposter_id']}'");

        if ($count) {
            $data = array();
            $data['activity_sharecount'] = $count['activity_sharecount'] + 1;
            $this->DataControl->updateData("crm_sell_activity", "activity_id = '{$request['shareposter_id']}' and company_id = '{$request['company_id']}' ", $data);
            ajax_return(array('error' => 0, 'errortip' => "创建成功"));
        } else {
            ajax_return(array('error' => 1, 'errortip' => "创建失败"));
        }
    }

    function getStuNameView(){
        $request = Input('get.', '', 'trim,addslashes');
        $name = $this->DataControl->getFieldOne("smc_student","student_cnname","student_id = '{$request['student_id']}'");
        ajax_return(array('error' => 0,'errortip' => "获取成功！",'result' => $name));
    }

    //2023 - 618转介绍推荐学员 -- 判断学生是否符合领取条件（从而生成二维码）
    function getStuHaveToRecommendView(){
        $request = Input('get.', '', 'trim,addslashes,strip_tags');

        if($request['cou_type'] == '1'){
            $esql = "SELECT a.order_id 
                FROM smc_payfee_order as a 
                left join smc_payfee_order_course as b on a.order_pid = b.order_pid 
                left join smc_course as c ON b.course_id = c.course_id 
                WHERE a.company_id = '{$request['company_id']}' and a.order_status = 4 and a.student_id = '{$request['student_id']}' and c.coursetype_id in ('65','61') 
                limit 0,1";
            $data = $this->DataControl->selectOne($esql);
        }elseif($request['cou_type'] == '2'){
            $asql = "SELECT a.order_id 
                FROM smc_payfee_order as a 
                left join smc_payfee_order_course as b on a.order_pid = b.order_pid 
                left join smc_course as c ON b.course_id = c.course_id 
                WHERE a.company_id = '{$request['company_id']}' and a.order_status = 4 and a.student_id = '{$request['student_id']}' and c.coursetype_id in ('79654','79653','79660')
                limit 0,1";
            $data = $this->DataControl->selectOne($asql);
        }elseif($request['cou_type'] == '3'){
            $tsql = "SELECT a.order_id 
                FROM smc_payfee_order as a 
                left join smc_payfee_order_course as b on a.order_pid = b.order_pid 
                left join smc_course as c ON b.course_id = c.course_id 
                WHERE a.company_id = '{$request['company_id']}' and a.order_status = 4 and a.student_id = '{$request['student_id']}' and c.coursetype_id = '79655'
                limit 0,1";
            $data = $this->DataControl->selectOne($tsql);
        }

        if($data){
            ajax_return(array('error' => 0, 'errortip' => "您有购课记录", 'result' => array()));
        }else{
            ajax_return(array('error' => 1, 'errortip' => "您未购买过该产品下的课程，无法推荐哦~", 'result' => array()));
        }
    }

    //2023 - 618转介绍推荐学员 （使用一个月之后不用）
    function stuRecommendStuLYBView(){
        $request = Input('post.', '', 'trim,addslashes,strip_tags');

        if ($request['tj_mobile'] == '' || !preg_match("/^13(\d{9})$|^15(\d{9})$|^16(\d{9})$|^14(\d{9})$|^17(\d{9})$|^18(\d{9})$|^19(\d{9})$/", trim($request['tj_mobile']))) {
            $res = array('error' => '1', 'errortip' => '手机号格式不正确!', "bakfuntion" => "errormotify");
            ajax_return($res);
        }
        if ($request['tj_kidname'] == '') {
            $res = array('error' => '1', 'errortip' => '请输入推荐宝宝的姓名!', "bakfuntion" => "errormotify");
            ajax_return($res);
        }
        if ($request['tj_mobile'] == '') {
            $res = array('error' => '1', 'errortip' => '请输入推荐宝宝的手机号码!', "bakfuntion" => "errormotify");
            ajax_return($res);
        }
        if ($request['tj_birthday'] == '') {
            $res = array('error' => '1', 'errortip' => '请输入推荐宝宝的年龄!', "bakfuntion" => "errormotify");
            ajax_return($res);
        }
        if ($request['open_id'] == '') {
            $res = array('error' => '1', 'errortip' => '账号标识参数缺失!', "bakfuntion" => "errormotify");
            ajax_return($res);
        }
        $logSql= "select log_id from smc_student_recommend_log where log_openid = '{$request['open_id']}' limit 0,1";
        $logOne = $this->DataControl->selectOne($logSql);
        if($logOne){
            //方便测试 临时屏蔽
            $res = array('error' => '1', 'errortip' => '您已经为好友助力过了!', "bakfuntion" => "errormotify");
            ajax_return($res);
        }else{
            $logdata = array();
            $logdata['student_id'] = $request['student_id'];
            $logdata['log_openid'] = $request['open_id'];
            $logdata['log_mobile'] = $request['tj_mobile'];
            $logdata['log_createtime'] = time();
            $this->DataControl->insertData("smc_student_recommend_log", $logdata);
        }


        $stuOne = $this->DataControl->selectOne("select * from smc_student WHERE student_id = '{$request['student_id']}' ");

        $data = array();
        $data['company_id'] = $request['company_id'];
        $data['client_acttype'] = 4;
        $data['client_stubranch'] = $stuOne['student_branch'];//推荐学员编号
        if(!$request['tj_kidname']){
            $data['client_cnname'] = '宝宝';
        }else{
            $data['client_cnname'] = $request['tj_kidname'];
        }
        $data['client_mobile'] = $request['tj_mobile'];
        $data['client_sex'] = $request['tj_sex'];
        $data['client_address'] = $request['tj_address'];
        $data['client_frommobile'] = $request['member_mobile'];
        $data['client_sponsor'] = $stuOne['student_cnname'];
        $data['client_source'] = '转介绍';
        $channelOne = $this->DataControl->selectOne("select c.channel_id from crm_code_channel AS c WHERE
c.channel_name = '学员转介绍' AND c.company_id = '{$request['company_id']}' LIMIT 0,1");
        if ($channelOne) {
            $data['channel_id'] = $channelOne['channel_id'];
        }
        if (strlen($request['tj_birthday']) > 7) {
            $nowyear = date("Y", time());
            $getyear = date("Y", strtotime($request['tj_birthday']));
            $age = $nowyear - $getyear;
            $data['client_age'] = $age;
            $data['client_birthday'] = $request['tj_birthday'];
        }else{
            $data['client_age'] = $request['tj_birthday'];
        }
        //获取学生的带班主教   --- 就是推荐老师
        $sfwhere = ' ';
        if($request['cou_type'] == 1){//----美语
            $sfwhere .= " and f.coursetype_id in ('65','61') ";
        }elseif($request['cou_type'] == 2){//----素质
            $sfwhere .= " and f.coursetype_id in ('79654','79653','79660') ";
        }elseif($request['cou_type'] == 3){//----安亲
            $sfwhere .= " and f.coursetype_id = '79655' ";
        }
//        $schoolStaffOne  = $this->DataControl->selectOne("SELECT b.staffer_id,c.staffer_cnname
//                FROM smc_student_hourstudy as a
//                left join smc_class_hour_teaching as b on b.hour_id=a.hour_id and b.teaching_type=0
//                left join smc_staffer as c on c.staffer_id=b.staffer_id
//                left join smc_class as d on a.class_id = d.class_id
//                left join smc_course as f on d.course_id = f.course_id
//                WHERE a.student_id = '{$stuOne['student_id']}' and d.school_id = '{$request['school_id']}' and c.staffer_leave = '0'
//                and (c.staffer_native = 0 or c.staffer_native = 3)  {$sfwhere}
//                ORDER BY a.hourstudy_id DESC
//                limit 0,1");
        $schoolStaffOne  = $this->DataControl->selectOne("SELECT b.staffer_id,c.staffer_cnname 
                FROM smc_student_hourstudy as a 
                left join smc_class_hour as g on a.hour_id = g.hour_id 
                left join smc_class_teach as b on b.class_id = a.class_id 
                left join smc_staffer as c on c.staffer_id=b.staffer_id 
                left join smc_class as d on a.class_id = d.class_id 
                left join smc_course as f on d.course_id = f.course_id 
                WHERE a.student_id = '{$stuOne['student_id']}' and d.school_id = '{$request['school_id']}' and c.staffer_leave = '0' 
                and (c.staffer_native = 0 or c.staffer_native = 3) and g.hour_isfree = 0  {$sfwhere} 
                ORDER BY a.hourstudy_id DESC,b.teach_status ASC,b.teach_type ASC
                limit 0,1");
        if($schoolStaffOne){
            $data['client_teacherid'] = $schoolStaffOne['staffer_id'];
            $data['client_teachername'] = $schoolStaffOne['staffer_cnname'];
        }else{
            $clientStaffOne  = $this->DataControl->selectOne(" SELECT c.staffer_id,d.staffer_cnname 
            FROM crm_client as a ,crm_client_principal as b,crm_marketer as c,smc_staffer as d  
            WHERE a.client_id = '{$stuOne['from_client_id']}' 
            and a.client_id = b.client_id and b.school_id = '{$request['school_id']}' and b.principal_ismajor = 1 and b.principal_leave = 0 and d.staffer_leave = '0' and (d.staffer_native = 0 or d.staffer_native = 3) 
            and b.marketer_id = c.marketer_id 
            and c.staffer_id = d.staffer_id
            limit 0,1 ");
            if($clientStaffOne){
                $data['client_teacherid'] = $clientStaffOne['staffer_id'];
                $data['client_teachername'] = $clientStaffOne['staffer_cnname'];
            }
        }

//        //重复名单验证 --- 开始
//        $data['school_id'] = $request['school_id'];
//        $data['channel_medianame'] = $data['client_source'];
//        $data['cou_type'] = $request['cou_type'];//618 活动发券  撞单逻辑 补充的参数
//        $data['student_id'] = $stuOne['student_id'];//618 活动发券  撞单逻辑 补充的参数
//        $track_note = "名单通过{$data['client_source']}招生推广页面流入，";
//        $Model = new \Model\Crm\ClientModel($request);
//        if (!$Model->CrmClientVerify($data, array(), $track_note)) {
//            ajax_return(array('error' => 1, 'errortip' => $Model->errortip, 'result' => array()));
//        }


        //修改为 走新的撞单机制
        $impotParam = array();
        $impotParam['company_id'] = $request['company_id'];
        if(isset($request['activity_id'])){
            $impotParam['activity_id'] = $request['activity_id'];
        }
        if(isset($request['marketer_id'])){
            $impotParam['marketer_id'] = $request['marketer_id'];
        }
        if(isset($request['school_id'])){
            $impotParam['school_id'] = $request['school_id'];
        }
        if(isset($schoolOne['school_id'])){
            $impotParam['school_id'] = $schoolOne['school_id'];
        }
        if(isset($request['school_branch'])){
            $impotParam['school_branch'] = $request['school_branch'];
        }
        $ClientVerify = new \Model\Crm\ClientCreatedModel($impotParam);
        if (!$ClientVerify->CrmClientVerify($data)) {
            ajax_return(array('error' => 1, 'errortip' => $ClientVerify->errortip, 'result' => array()));
        }

        unset($data['channel_medianame']);
        unset($data['school_id']);
        unset($data['cou_type']);
        unset($data['student_id']);
        //重复名单验证 --- 结束


        $data['client_soursename'] = "微商城直接推荐成功";
        $data['company_id'] = "8888";
        $data['client_createtime'] = time();
        $data['client_updatetime'] = time();
        $clientId = $this->DataControl->insertData("crm_client", $data);
        if ($clientId) {
            $dataFive = array();
            $dataFive['school_id'] = $request['school_id'];
            $dataFive['client_id'] = $clientId;
            $dataFive['track_createtime'] = time();
            $dataFive['marketer_id'] = '0';
            $dataFive['marketer_name'] = '系统';
            $dataFive['track_linktype'] = '微商城新增名单';//20200109需求修改
            $dataFive['track_note'] = "名单通过微商城，家长自助推荐学员信息，系统自动登记名单！";//20200414需求修改
            $dataFive['track_state'] = 0;
            $dataFive['track_type'] = 0;
            $dataFive['track_initiative'] = 0;
            $this->DataControl->insertData("crm_client_track", $dataFive);



            //家长
            $dataThree = array();
            if(!$request['tj_name']){
                $dataThree['parenter_cnname'] = '匿名';
            }else{
                $dataThree['parenter_cnname'] = $request['tj_name'];
            }
            $dataThree['parenter_mobile'] = $request['tj_mobile'];
            $dataThree['parenter_pass'] = md5(substr($request['tj_mobile'], -6));
            $dataThree['parenter_bakpass'] = substr($request['tj_mobile'], -6);
            $dataThree['parenter_addtime'] = time();
            $parenterid = $this->DataControl->insertData("smc_parenter", $dataThree);
            if (!$parenterid) {
                $parenterOne = $this->DataControl->getFieldOne("smc_parenter", "parenter_id", "parenter_mobile='{$request['tj_mobile']}' ");
                $parenterid = $parenterOne['parenter_id'];
            }

            $dataThree = array();
            $dataThree['client_id'] = $clientId;
            $dataThree['parenter_id'] = $parenterid;
            $dataThree['family_relation'] = '家长';
            $dataThree['family_isdefault'] = 1;
            $dataThree['company_id'] = $request['company_id'];
            $dataThree['family_createtime'] = time();
            $this->DataControl->insertData("crm_client_family", $dataThree);

            $schoolenter = array();
            $schoolenter['client_id'] = $clientId;
            $schoolenter['company_id'] = $request['company_id'];
            $schoolenter['school_id'] = $request['school_id'];
            $schoolenter['is_schoolenter'] = '1';
            $schoolenter['schoolenter_createtime'] = time();
            $schoolenter['schoolenter_updatetime'] = time();
            $this->DataControl->insertData('crm_client_schoolenter', $schoolenter);

            // 618 推荐成功 自动 领券
            $receivedata = array();
            if($request['cou_type'] == 1){//----美语
                $receivedata['applytype_id'] = '676';
                $receivedata['applytype_branch'] = 'pddE';
            }elseif($request['cou_type'] == 2){//----素质
                $receivedata['applytype_id'] = '677';
                $receivedata['applytype_branch'] = 'pddSZ';
            }elseif($request['cou_type'] == 3){//----安亲
                $receivedata['applytype_id'] = '678';
                $receivedata['applytype_branch'] = 'pddT';
            }
            $receivedata['cou_type'] = $request['cou_type'];
            if($request['cou_type'] == 1 || $request['cou_type'] == 2 || $request['cou_type'] == 3) {
                //判断这个学生获取了多少张这类券
                $stuCouCount = $this->DataControl->selectOne(" select count(apply_id) as applycount from smc_student_coupons_apply where company_id = '{$request['company_id']}' and school_id = '{$request['school_id']}' and student_id = '{$stuOne['student_id']}' and applytype_branch = '{$receivedata['applytype_branch']}'  ");
                if($stuCouCount['applycount']<10) {
                    $receivedata['student_id'] = $stuOne['student_id'];
                    $receivedata['company_id'] = $request['company_id'];
                    //默认操作发券人
                    $staffcomone = $this->DataControl->selectOne(" select staffer_id from smc_staffer where company_id = '{$request['company_id']}' order by account_class desc,staffer_id ASC limit 0,1 ");
                    $receivedata['staffer_id'] = $staffcomone['staffer_id'];
                    $receivedata['school_id'] = $request['school_id'];
                    $StudentModel = new \Model\Smc\StudentModel($receivedata);
                    $StudentModel->receiveCoupons($receivedata);
                }
            }

            ajax_return(array('error' => 0, 'errortip' => "推荐成功", 'result' => array()));
        } else {
            ajax_return(array('error' => 1, 'errortip' => "推荐失败", 'result' => array()));
        }
    }


    //推荐学员名单
    function stuRecommendStuView()
    {
        $request = Input('post.', '', 'trim,addslashes,strip_tags');

        //补充的日志记录
        $logdata = array();
        $logdata['log_type'] = 1;
        $logdata['student_id'] = $request['student_id'];
        $logdata['log_text'] = json_encode($request,JSON_UNESCAPED_UNICODE);
        $logdata['log_mobile'] = $request['tj_mobile'];
        $logdata['log_createtime'] = time();
        $this->DataControl->insertData("smc_student_recommend_log", $logdata);

        if($request['language_type'] == 'tw'){
            if ($request['tj_mobile'] == '' || !preg_match('/^[0][9]\d{8}$/', trim($request['tj_mobile']))) {
                $res = array('error' => '1', 'errortip' => '手机号格式不正确!', "bakfuntion" => "errormotify");
                ajax_return($res);
            }
        }else{
            if ($request['tj_mobile'] == '' || !preg_match("/^13(\d{9})$|^15(\d{9})$|^16(\d{9})$|^14(\d{9})$|^17(\d{9})$|^18(\d{9})$|^19(\d{9})$/", trim($request['tj_mobile']))) {
                $res = array('error' => '1', 'errortip' => '手机号格式不正确!', "bakfuntion" => "errormotify");
                ajax_return($res);
            }
        }


//        //这个喆哥确认校务这边不需要这个验证
//        if ($this->DataControl->getFieldOne("crm_client", " client_id ", " client_mobile = '{$request['tj_mobile']}' ")) {
//            $res = array('error' => '1', 'errortip' => '该手机号已存在!', "bakfuntion" => "errormotify");
//            ajax_return($res);
//        }

        if ($request['tj_name'] == '') {
            $request['tj_name'] == '匿名';
        }
        if ($request['tj_kidname'] == '') {
            $res = array('error' => '1', 'errortip' => '请输入推荐宝宝的姓名!', "bakfuntion" => "errormotify");
            ajax_return($res);
        }
        if ($request['tj_mobile'] == '') {
            $res = array('error' => '1', 'errortip' => '请输入推荐宝宝的手机号码!', "bakfuntion" => "errormotify");
            ajax_return($res);
        }
        if ($request['tj_sex'] == '') {
            $res = array('error' => '1', 'errortip' => '请输入推荐宝宝的性别!', "bakfuntion" => "errormotify");
            ajax_return($res);
        }
//        if ($request['tj_birthday'] == '') {
//            $res = array('error' => '1', 'errortip' => '请输入推荐宝宝的出生年月日!', "bakfuntion" => "errormotify");
//            ajax_return($res);
//        }
//        if ($request['tj_address'] == '') {
//            $res = array('error' => '1', 'errortip' => '请输入推荐宝宝的联系地址!', "bakfuntion" => "errormotify");
//            ajax_return($res);
//        }

        if($request['student_id']){
            $stuOne = $this->DataControl->selectOne("select * from smc_student WHERE student_id = '{$request['student_id']}' ");
        }else{
            $stuOne = $this->DataControl->selectOne("select * from smc_student WHERE student_branch = '{$request['student_branch']}' ");
        }

        $data = array();
        if($request['language_type'] == 'tw'){
            $data['company_id'] = '79081';
        }else{
            $data['company_id'] = $request['company_id'];
        }
        $data['client_acttype'] = 4;
        $data['client_stubranch'] = $stuOne['student_branch'];//推荐学员编号
        if(!$request['tj_kidname']){
            $data['client_cnname'] = ($request['language_type'] == 'tw')?'寶寶':'宝宝';
        }else{
            $data['client_cnname'] = $request['tj_kidname'];
        }
        $data['client_mobile'] = $request['tj_mobile'];
        $data['client_sex'] = $request['tj_sex'];
        $data['client_address'] = $request['tj_address'];
        if(!$request['staffer_id']){
            $sbranch = $this->DataControl->getFieldOne("smc_student","student_branch","student_id = '{$request['student_id']}'");

            $teacher = $this->DataControl->selectOne(" 
           SELECT
                st.staffer_id,
                st.staffer_cnname,
                st.staffer_enname 
            FROM
                smc_student_study AS s
                LEFT JOIN smc_class_teach AS ct ON s.class_id = ct.class_id
                LEFT JOIN smc_staffer AS st ON st.staffer_id = ct.staffer_id
                LEFT JOIN smc_class AS sc ON sc.class_id = s.class_id
                LEFT JOIN smc_course AS sco ON sco.course_id = sc.course_id 
            WHERE
                s.student_id = '{$request['student_id']}'  
                AND ct.teach_type = 0 
                AND ct.teach_status = 0 
                AND (st.staffer_native = 0 or st.staffer_native = 3)
                AND st.staffer_leave = 0
            GROUP BY
                ct.teach_id UNION
            SELECT
	st.staffer_id,
	st.staffer_cnname,
	st.staffer_enname 
FROM
	crm_client AS c
	LEFT JOIN crm_client_conversionlog AS cc ON cc.client_id = c.client_id 
	LEFT JOIN crm_marketer AS mk ON mk.marketer_id = cc.marketer_id
	LEFT JOIN smc_staffer AS st ON st.staffer_id = mk.staffer_id 
WHERE cc.school_id = '{$request['school_id']}' 
	AND CC.student_branch = '{$sbranch['student_branch']}'
	and cc.conversionlog_ismajor = 1
	AND st.staffer_leave = 0 
GROUP BY
	mk.marketer_id  UNION
            SELECT s.staffer_id,s.staffer_cnname,s.staffer_enname
            from gmc_staffer_postbe as p 
            LEFT JOIN smc_staffer as s ON p.staffer_id = s.staffer_id 
            LEFT JOIN smc_school as h ON p.school_id = h.school_id 
            WHERE h.school_id = '{$request['school_id']}' 
            and p.postbe_status = '1' and p.postpart_id = '109' 
            and s.staffer_leave = '0'");
            $request['staffer_id'] = $teacher['staffer_id'];
        }
        $data['client_teacherid'] = $request['staffer_id'];

        $tname = $this->DataControl->getFieldOne("smc_staffer","staffer_cnname,staffer_enname","staffer_id = '{$request['staffer_id']}'");
        if($tname['staffer_enname']){
            $data['client_teachername'] = $tname['staffer_cnname'].'/'.$tname['staffer_enname'];
        }else{
            $data['client_teachername'] = $tname['staffer_cnname'];
        }
        $data['client_frommobile'] = $request['member_mobile'];
        $data['client_sponsor'] = $stuOne['student_cnname'];

        if($request['language_type'] == 'tw'){
            $data['client_source'] = '轉介紹';
            $channelOne = $this->DataControl->selectOne("select c.channel_id from crm_code_channel AS c WHERE
c.channel_name = '學員介紹' AND c.company_id = '{$data['company_id']}' LIMIT 0,1");
        }else{
            $data['client_source'] = '转介绍';
            $channelOne = $this->DataControl->selectOne("select c.channel_id from crm_code_channel AS c WHERE
c.channel_name = '学员转介绍' AND c.company_id = '{$data['company_id']}' LIMIT 0,1");
        }
        if ($channelOne) {
            $data['channel_id'] = $channelOne['channel_id'];
        }

        if (strlen($request['tj_birthday']) > 7) {
            $nowyear = date("Y", time());
            $getyear = date("Y", strtotime($request['tj_birthday']));
            $age = $nowyear - $getyear;
            $data['client_age'] = $age;
            $data['client_birthday'] = $request['tj_birthday'];
        }else{
            $data['client_age'] = $request['tj_birthday'];
        }
        //家校通 校务 作品分享补充了一个陌客 提交表单的操作 230807

        //重复名单验证
        $data['school_id'] = $request['school_id'];

        $data['channel_medianame'] = $data['client_source'];
        $track_note = ($request['language_type'] == 'tw')?"名單通過{$data['client_source']}招生推廣頁面流入，":"名单通过{$data['client_source']}招生推广页面流入，";
        $Model = new \Model\Crm\ClientModel($request);

        $impotParam = array();
        if($request['language_type'] == 'tw'){
            $impotParam['company_id'] = '79081';
//            $impotParam['company_id'] = $request['company_id'];

        }else{
            $impotParam['company_id'] = $request['company_id'];
        }
        if(isset($request['activity_id'])){
            $impotParam['activity_id'] = $request['activity_id'];
        }
        if(isset($request['marketer_id'])){
            $impotParam['marketer_id'] = $request['marketer_id'];
        }
        if(isset($request['school_id'])){
            $impotParam['school_id'] = $request['school_id'];
        }
        if(isset($request['school_branch'])){
            $impotParam['school_branch'] = $request['school_branch'];
        }
        $ClientVerify = new \Model\Crm\ClientCreatedModel($impotParam);
        if (!$ClientVerify->CrmClientVerify($data,$track_note)) {
            ajax_return(array('error' => 1, 'errortip' => "推荐失败", 'result' => array()));
//            ajax_return(array('error' => 1, 'errortip' => $ClientVerify->errortip, 'result' => array()));
        }

//        if (!$Model->CrmClientVerify($data, array(), $track_note)) {
////            ajax_return(array('error' => 1, 'errortip' => "推荐失败", 'result' => array()));
//            ajax_return(array('error' => 1, 'errortip' => $Model->errortip, 'result' => array()));
//        }
        unset($data['channel_medianame']);
        unset($data['school_id']);

        $data['client_soursename'] = ($request['language_type'] == 'tw')?'微商城直接推薦成功':'微商城直接推荐成功';

        if ( $request['province_id'] > 1) {
            $data['province_id'] = $request['province_id'];
        }
        if ($request['city_id'] > 1) {
            $data['city_id'] = $request['city_id'];
        }
        if ($request['area_id'] > 1) {
            $data['area_id'] = $request['area_id'];
        }

//        $data['company_id'] = '8888';
        $data['client_createtime'] = time();
        $data['client_updatetime'] = time();
        $clientId = $this->DataControl->insertData("crm_client", $data);
        if ($clientId) {
            $dataFive = array();
            $dataFive['school_id'] = $request['school_id'];
            $dataFive['client_id'] = $clientId;
            $dataFive['track_createtime'] = time();
            $dataFive['marketer_id'] = '0';
            $dataFive['marketer_name'] = ($request['language_type'] == 'tw')?'系統':'系统';
            $dataFive['track_linktype'] = ($request['language_type'] == 'tw')?'微商城新增名單':'微商城新增名单';//20200109需求修改
            $dataFive['track_note'] = ($request['language_type'] == 'tw')?'名單通過微商城，家長自助推薦營員資訊，系統自動登記名單！':'名单通过微商城，家长自助推荐学员信息，系统自动登记名单！';//20200414需求修改
            $dataFive['track_state'] = 0;
            $dataFive['track_type'] = 0;
            $dataFive['track_initiative'] = 0;
            $this->DataControl->insertData("crm_client_track", $dataFive);

            //家长
            $dataThree = array();
            if(!$request['tj_name']){
                $dataThree['parenter_cnname'] = '匿名';
            }else{
                $dataThree['parenter_cnname'] = $request['tj_name'];
            }
            $dataThree['parenter_mobile'] = $request['tj_mobile'];
            $dataThree['parenter_pass'] = md5(substr($request['tj_mobile'], -6));
            $dataThree['parenter_bakpass'] = substr($request['tj_mobile'], -6);
            $dataThree['parenter_addtime'] = time();
            $parenterid = $this->DataControl->insertData("smc_parenter", $dataThree);
            if (!$parenterid) {
                $parenterOne = $this->DataControl->getFieldOne("smc_parenter", "parenter_id", "parenter_mobile='{$request['tj_mobile']}' ");
                $parenterid = $parenterOne['parenter_id'];
            }

            $dataThree = array();
            $dataThree['client_id'] = $clientId;
            $dataThree['parenter_id'] = $parenterid;
            $dataThree['family_relation'] = ($request['language_type'] == 'tw')?'家長':'家长';
            $dataThree['family_isdefault'] = 1;
            $dataThree['company_id'] = $request['company_id'];
            $dataThree['family_createtime'] = time();
            $this->DataControl->insertData("crm_client_family", $dataThree);

            if($request['school_id'] > 1) {
                $schoolenter = array();
                $schoolenter['client_id'] = $clientId;
                $schoolenter['company_id'] = $request['company_id'];
                $schoolenter['school_id'] = $request['school_id'];
                $schoolenter['is_schoolenter'] = '1';
                $schoolenter['schoolenter_createtime'] = time();
                $schoolenter['schoolenter_updatetime'] = time();
                $this->DataControl->insertData('crm_client_schoolenter', $schoolenter);
            }
            ajax_return(array('error' => 0, 'errortip' => "推荐成功", 'result' => array()));
        } else {
            ajax_return(array('error' => 1, 'errortip' => "推荐失败", 'result' => array()));
        }
    }

    function testView(){
        $a = $this->DataControl->selectClear("SELECT
 client_id,
 client_stubranch
FROM
	crm_client
WHERE
	client_createtime > '1698768000' 
	AND client_source = '转介绍' 
	AND client_teacherid = '0'
ORDER BY
	client_id DESC ");

        foreach($a as &$val){


            $sid = $this->DataControl->getFieldOne("smc_student","student_id,student_branch","student_branch = '{$val['client_stubranch']}'");
            $school_id = $this->DataControl->getFieldOne("smc_student_study","school_id","student_id = '{$val['student_id']}' order by study_isreading DESC");

            $teacher = $this->DataControl->selectOne(" 
           SELECT
                st.staffer_id,
                st.staffer_cnname,
                st.staffer_enname 
            FROM
                smc_student_study AS s
                LEFT JOIN smc_class_teach AS ct ON s.class_id = ct.class_id
                LEFT JOIN smc_staffer AS st ON st.staffer_id = ct.staffer_id
                LEFT JOIN smc_class AS sc ON sc.class_id = s.class_id
                LEFT JOIN smc_course AS sco ON sco.course_id = sc.course_id 
            WHERE
                s.student_id = '{$sid['student_id']}'  
                AND ct.teach_type = 0 
                AND ct.teach_status = 0 
                AND (st.staffer_native = 0 or st.staffer_native = 3)
                AND st.staffer_leave = 0
            GROUP BY
                ct.teach_id UNION
            SELECT
	st.staffer_id,
	st.staffer_cnname,
	st.staffer_enname 
FROM
	crm_client AS c
	LEFT JOIN crm_client_conversionlog AS cc ON cc.client_id = c.client_id 
	LEFT JOIN crm_marketer AS mk ON mk.marketer_id = cc.marketer_id
	LEFT JOIN smc_staffer AS st ON st.staffer_id = mk.staffer_id 
WHERE cc.school_id = '{$school_id['school_id']}' 
	AND CC.student_branch = '{$sid['student_branch']}'
	and cc.conversionlog_ismajor = 1
	AND st.staffer_leave = 0 
GROUP BY
	mk.marketer_id  UNION
            SELECT s.staffer_id,s.staffer_cnname,s.staffer_enname
            from gmc_staffer_postbe as p 
            LEFT JOIN smc_staffer as s ON p.staffer_id = s.staffer_id 
            LEFT JOIN smc_school as h ON p.school_id = h.school_id 
            WHERE h.school_id = '{$school_id['school_id']}' 
            and p.postbe_status = '1' and p.postpart_id = '109' 
            and s.staffer_leave = '0'");
            $data = array();

            $data['client_teacherid'] = $teacher['staffer_id'];

            $tname = $this->DataControl->getFieldOne("smc_staffer","staffer_cnname,staffer_enname","staffer_id = '{$teacher['staffer_id']}'");
            if($tname['staffer_enname']){
                $data['client_teachername'] = $tname['staffer_cnname'].'/'.$tname['staffer_enname'];
            }else{
                $data['client_teachername'] = $tname['staffer_cnname'];
            }

            $this->DataControl->updateData("crm_client","client_id = {$val['client_id']}",$data);
        }

        echo '1';
    }

    //根据学生ID获取推荐老师姓名
    function GetTeacherNameApi(){
        $request = Input('get.', '', 'trim,addslashes,strip_tags');
        $teacher = $this->DataControl->selectOne("
            SELECT
                st.staffer_cnname
            FROM
                smc_student_study AS s
                LEFT JOIN smc_class_teach AS ct ON s.class_id = ct.class_id 
                left join smc_staffer as st on st.staffer_id = ct.staffer_id
                left join smc_class as sc on sc.class_id = s.class_id
                left join smc_course as sco on sco.course_id = sc.course_id
            WHERE
                s.student_id = '{$request['student_id']}' 
                AND s.study_isreading = 1
                AND ct.teach_type = 0
                AND ct.teach_status = 0
                AND sco.coursetype_id = 65
                GROUP BY s.study_id
                LIMIT 0,1");
        if($teacher){
            $name = $teacher['staffer_cnname'];
        }else{
            $name = '';
        }

        ajax_return(array('error' => 0, 'errortip' => "获取成功", 'result' => $name));

    }

    function aView(){
        $a = $this->DataControl->selectClear("SELECT
	c.client_id,
	c.client_teachername,
	s.staffer_id
FROM
	crm_client AS c 
	left join smc_staffer as s on s.staffer_cnname = c.client_teachername and s.company_id = 8888
WHERE
	c.client_teachername <> '' and c.company_id = 8888
	GROUP BY c.client_id");


        if($a){
            foreach($a as &$val){
                $data = array();
                $data['client_teacherid'] = $val['staffer_id'];
                $this->DataControl->updateData("crm_client","client_id = '{$val['client_id']}'",$data);
            }

        }
        ajax_return(array('error' => 0, 'errortip' => "获取成功"));

    }

    //顾问列表
    function GetConsultantTeApi(){
        $request = Input('get.', '', 'trim,addslashes,strip_tags');
        $teacher = $this->DataControl->selectClear("
            SELECT
                s.staffer_cnname
            FROM
                crm_marketer AS m
                LEFT JOIN smc_staffer AS s ON m.staffer_id = s.staffer_id
                LEFT JOIN gmc_staffer_postbe AS p ON p.staffer_id = s.staffer_id 
            WHERE
                m.company_id = '{$request['company_id']}'  
                AND s.staffer_leave = 0 
                AND p.school_id = '{$request['school_id']}' 
                and s.staffer_native = 0
            GROUP BY
                p.staffer_id");

        ajax_return(array('error' => 0, 'errortip' => "获取成功", 'result' => $teacher));

    }

    //老师列表
    function GetClassTeApi(){
        $request = Input('get.', '', 'trim,addslashes,strip_tags');
        $teacher = $this->DataControl->selectClear(" 
            SELECT
                st.staffer_id,
                st.staffer_cnname,
                st.staffer_enname 
            FROM
                smc_student_study AS s
                LEFT JOIN smc_class_teach AS ct ON s.class_id = ct.class_id
                LEFT JOIN smc_staffer AS st ON st.staffer_id = ct.staffer_id
                LEFT JOIN smc_class AS sc ON sc.class_id = s.class_id
                LEFT JOIN smc_course AS sco ON sco.course_id = sc.course_id 
            WHERE
                s.student_id = '{$request['student_id']}'  
                AND ct.teach_type = 0 
                AND ct.teach_status = 0 
                AND (st.staffer_native = 0 or st.staffer_native = 3)
                AND st.staffer_leave = 0
            GROUP BY
                ct.teach_id UNION
            SELECT
                st.staffer_id,
                st.staffer_cnname,
                st.staffer_enname 
            FROM
                crm_client AS c
                LEFT JOIN crm_client_principal AS p ON p.client_id = c.client_id
                LEFT JOIN crm_marketer AS mk ON mk.marketer_id = p.marketer_id
                LEFT JOIN crm_client_schoolenter AS s ON s.client_id = c.client_id 
                AND p.school_id = s.school_id
                LEFT JOIN smc_staffer AS st ON st.staffer_id = mk.staffer_id 
            WHERE
                s.company_id = '8888' 
                AND p.school_id = '{$request['school_id']}' 
                AND p.principal_leave = 0 
                AND c.client_isgross = '0' 
                AND c.client_tracestatus > '-1' 
                AND c.client_tracestatus <> 4 
                AND st.staffer_leave = 0 
            GROUP BY
                mk.marketer_id  UNION
            SELECT s.staffer_id,s.staffer_cnname,s.staffer_enname
            from gmc_staffer_postbe as p 
            LEFT JOIN smc_staffer as s ON p.staffer_id = s.staffer_id 
            LEFT JOIN smc_school as h ON p.school_id = h.school_id 
            WHERE h.school_id = '{$request['school_id']}' 
            and p.postbe_status = '1' and p.postpart_id = '109' 
            and s.staffer_leave = '0'");

        if($teacher){
            foreach($teacher as &$val){
                if($val['staffer_enname']){
                    $val['staffer_cnname'] = $val['staffer_cnname'].'/'.$val['staffer_enname'];
                }else{
                    $val['staffer_cnname'] = $val['staffer_cnname'];
                }
            }
        }

        ajax_return(array('error' => 0, 'errortip' => "获取成功", 'result' => $teacher));


//        $teacher = $this->DataControl->selectClear("
//            SELECT
//	st.staffer_id,
//	st.staffer_cnname,
//	st.staffer_enname
//FROM
//	smc_student_study AS s
//	LEFT JOIN smc_class_teach AS ct ON s.class_id = ct.class_id
//	LEFT JOIN smc_staffer AS st ON st.staffer_id = ct.staffer_id
//	LEFT JOIN smc_class AS sc ON sc.class_id = s.class_id
//	LEFT JOIN smc_course AS sco ON sco.course_id = sc.course_id
//WHERE
//	s.student_id = '{$request['student_id']}'
//	AND s.study_isreading = 1
//	AND ct.teach_type = 0
//	AND ct.teach_status = 0
//	AND sco.coursetype_id = 65
//	AND (st.staffer_native = 0 or st.staffer_native = 3)
//	AND st.staffer_leave = 0
//GROUP BY
//	ct.teach_id UNION
//SELECT
//	st.staffer_id,
//	st.staffer_cnname,
//	st.staffer_enname
//FROM
//	smc_student_study AS s
//	LEFT JOIN smc_class_teach AS ct ON s.class_id = ct.class_id
//	LEFT JOIN smc_staffer AS st ON st.staffer_id = ct.staffer_id
//	LEFT JOIN smc_class AS sc ON sc.class_id = s.class_id
//	LEFT JOIN smc_course AS sco ON sco.course_id = sc.course_id
//WHERE
//	s.student_id = '{$request['student_id']}'
//	AND s.study_isreading = 1
//	AND ct.teach_type = 0
//	AND ct.teach_status = 0
//	AND sco.coursetype_id <> 65
//	AND (st.staffer_native = 0 or st.staffer_native = 3)
//	AND st.staffer_leave = 0
//GROUP BY
//	ct.teach_id UNION
//SELECT
//	st.staffer_id,
//	st.staffer_cnname,
//	st.staffer_enname
//FROM
//	smc_student_study AS s
//	LEFT JOIN smc_class_teach AS ct ON s.class_id = ct.class_id
//	LEFT JOIN smc_staffer AS st ON st.staffer_id = ct.staffer_id
//	LEFT JOIN smc_class AS sc ON sc.class_id = s.class_id
//	LEFT JOIN smc_course AS sco ON sco.course_id = sc.course_id
//WHERE
//	s.student_id = '{$request['student_id']}'
//	AND s.study_isreading <> 1
//	AND ct.teach_type = 0
//	AND ct.teach_status = 0
//	AND (st.staffer_native = 0 or st.staffer_native = 3)
//	AND st.staffer_leave = 0
//GROUP BY
//	ct.teach_id UNION
//SELECT
//	st.staffer_id,
//	st.staffer_cnname,
//	st.staffer_enname
//FROM
//	crm_client AS c
//	LEFT JOIN crm_client_principal AS p ON p.client_id = c.client_id
//	LEFT JOIN crm_marketer AS mk ON mk.marketer_id = p.marketer_id
//	LEFT JOIN crm_client_schoolenter AS s ON s.client_id = c.client_id
//	AND p.school_id = s.school_id
//	LEFT JOIN smc_staffer AS st ON st.staffer_id = mk.staffer_id
//WHERE
//	s.company_id = '8888'
//	AND p.school_id = '{$request['school_id']}'
//	AND p.principal_leave = 0
//	AND c.client_isgross = '0'
//	AND c.client_tracestatus > '-1'
//	AND c.client_tracestatus <> 4
//GROUP BY
//	mk.marketer_id");
    }

    //老师列表
    function GetClassTeAppApi(){
        $request = Input('get.', '', 'trim,addslashes,strip_tags');
        $sid = $this->DataControl->getFieldOne("smc_student","student_id","student_branch = '{$request['student_branch']}'");
        $schoolid = $this->DataControl->getFieldOne("smc_school","school_id","school_branch = '{$request['school_branch']}'");
        $teacher = $this->DataControl->selectClear("
            SELECT
	st.staffer_id,
	st.staffer_cnname,
	st.staffer_enname 
FROM
	smc_student_study AS s
	LEFT JOIN smc_class_teach AS ct ON s.class_id = ct.class_id
	LEFT JOIN smc_staffer AS st ON st.staffer_id = ct.staffer_id
	LEFT JOIN smc_class AS sc ON sc.class_id = s.class_id
	LEFT JOIN smc_course AS sco ON sco.course_id = sc.course_id 
WHERE
	s.student_id = '{$sid['student_id']}' 
	AND s.study_isreading = 1 
	AND ct.teach_type = 0 
	AND ct.teach_status = 0 
	AND sco.coursetype_id = 65 
	AND (st.staffer_native = 0 or st.staffer_native = 3)
	AND st.staffer_leave = 0
GROUP BY
	ct.teach_id UNION
SELECT
	st.staffer_id,
	st.staffer_cnname,
	st.staffer_enname 
FROM
	smc_student_study AS s
	LEFT JOIN smc_class_teach AS ct ON s.class_id = ct.class_id
	LEFT JOIN smc_staffer AS st ON st.staffer_id = ct.staffer_id
	LEFT JOIN smc_class AS sc ON sc.class_id = s.class_id
	LEFT JOIN smc_course AS sco ON sco.course_id = sc.course_id 
WHERE
	s.student_id = '{$sid['student_id']}' 
	AND s.study_isreading = 1 
	AND ct.teach_type = 0 
	AND ct.teach_status = 0 
	AND sco.coursetype_id <> 65 
	AND (st.staffer_native = 0 or st.staffer_native = 3)
	AND st.staffer_leave = 0
GROUP BY
	ct.teach_id UNION
SELECT
	st.staffer_id,
	st.staffer_cnname,
	st.staffer_enname 
FROM
	smc_student_study AS s
	LEFT JOIN smc_class_teach AS ct ON s.class_id = ct.class_id
	LEFT JOIN smc_staffer AS st ON st.staffer_id = ct.staffer_id
	LEFT JOIN smc_class AS sc ON sc.class_id = s.class_id
	LEFT JOIN smc_course AS sco ON sco.course_id = sc.course_id 
WHERE
	s.student_id = '{$sid['student_id']}' 
	AND s.study_isreading <> 1
	AND ct.teach_type = 0 
	AND ct.teach_status = 0 
	AND (st.staffer_native = 0 or st.staffer_native = 3)
	AND st.staffer_leave = 0
GROUP BY
	ct.teach_id UNION
SELECT
	st.staffer_id,
	st.staffer_cnname,
	st.staffer_enname 
FROM
	crm_client AS c
	LEFT JOIN crm_client_principal AS p ON p.client_id = c.client_id
	LEFT JOIN crm_marketer AS mk ON mk.marketer_id = p.marketer_id
	LEFT JOIN crm_client_schoolenter AS s ON s.client_id = c.client_id 
	AND p.school_id = s.school_id
	LEFT JOIN smc_staffer AS st ON st.staffer_id = mk.staffer_id 
WHERE
	s.company_id = '8888' 
	AND p.school_id = '{$schoolid['school_id']}' 
	AND p.principal_leave = 0 
	AND c.client_isgross = '0' 
	AND c.client_tracestatus > '-1' 
	AND c.client_tracestatus <> 4 
GROUP BY
	mk.marketer_id");

        if($teacher){
            foreach($teacher as &$val){
                if($val['staffer_enname']){
                    $val['staffer_cnname'] = $val['staffer_cnname'].'/'.$val['staffer_enname'];
                }else{
                    $val['staffer_cnname'] = $val['staffer_cnname'];
                }
            }
        }

        ajax_return(array('error' => 0, 'errortip' => "获取成功", 'result' => $teacher));

    }


    //根据学员编号获取孩子信息
    function GetStudentOneView()
    {
        $request = Input('get.', '', 'trim,addslashes,strip_tags');
        $this->ThisVerify($request, '0');//验证账户

        //model
        $Model = new \Model\Scshop\CouponsModel();
        $dataList = $Model->GetStudentOne($request);
        $field = array();
        $field["student_id"] = "学员ID";
        $field["student_cnname"] = "姓名";
        $field["student_sex"] = "性别";
        $field["student_age"] = "年龄";
        $field["school_cnname"] = "校区名称";
        $field["school_branch"] = "校区编号";
        $field["school_id"] = "学校ID";

        $result = array();
        if ($dataList['list']) {
            $result["field"] = $field;
            $result["data"] = $dataList['list'];
            $result["other"] = $dataList['other'];
            ajax_return(array('error' => 0, 'errortip' => "获取学员数据成功", 'result' => $result));
        } else {
            ajax_return(array('error' => 1, 'errortip' => "暂无学员数据", 'result' => array()));
        }
    }

    //员工优惠券获取员工信息
    function GetStaffInfoView()
    {
        $request = Input('get.', '', 'trim,addslashes,strip_tags');
        //$this->ThisVerify($request);//验证账户
        $parenterOne = $this->DataControl->getFieldOne("smc_parenter", 'parenter_mobile', "parenter_id='{$request['parenter_id']}'");
        if ($parenterOne) {
//            $stafferOne = $this->DataControl->getFieldOne('smc_staffer','staffer_id,staffer_cnname',"staffer_mobile='{$parenterOne['parenter_mobile']}' and company_id = '{$request['company_id']}' ");
//            if ($stafferOne) {
//                $postOne = $this->DataControl->selectOne("select p.post_name from  gmc_staffer_postbe as f
//                          LEFT JOIN gmc_company_post as p ON f.post_id = p.post_id
//                          WHERE f.postbe_ismianjob = '1' and f.staffer_id = '{$stafferOne['staffer_id']}' and f.company_id = '{$request['company_id']}'  ");
//                if ($postOne) {
//                    $data = array();
//                    $data['staffer_cnname'] = $stafferOne['staffer_cnname'];
//                    $data['post_name'] = $postOne['post_name'];
//                    $data['parenter_mobile'] = $parenterOne['parenter_mobile'];
//
//                    $relativeOne = $this->DataControl->getOne('shop_coupons_apply_relative',"company_id='{$request['company_id']}' and parenter_id='{$request['parenter_id']}' ");
//                    if($relativeOne){
//                        $data['isedit'] = '1';
//                        $data['iseditname'] = '去编辑';
//                    }else{
//                        $data['isedit'] = '0';
//                        $data['iseditname'] = '去申请';
//                    }
//                    ajax_return(array('error' => 0,'errortip' => "获取成功!","result"=>$data));
//                } else {
//                    ajax_return(array('error' => 1,'errortip' => "未获取到职务信息!","result"=>array()));
//                }
//            }else{

//            $bakinfo = request_by_curl("https://api.kidcastle.com.cn/Jdbpay/hrmworker", "mobile={$parenterOne['parenter_mobile']}", "GET", array());
//            $apiArray = json_decode($bakinfo, '1');
//            if ($apiArray['error'] == '0' && $request['company_id'] == '8888') {
////            if ($apiArray['error'] == '0' ) {
//            $data = array();
//            $data['staffer_cnname'] = $apiArray['result']['worker_cnname'];
//            $data['post_name'] = $apiArray['result']['position_name'];
//            $data['parenter_mobile'] = $parenterOne['parenter_mobile'];


            $staArray = $this->DataControl->selectOne(" SELECT a.staffer_cnname
                ,(SELECT o.postrole_name FROM gmc_company_postrole as o WHERE o.postrole_id = b.postrole_id limit 0,1) as postOne
                ,(SELECT o.postpart_name FROM smc_school_postpart as o WHERE o.postpart_id = b.postpart_id limit 0,1) as postTwo 
                FROM smc_staffer as a,gmc_staffer_postbe as b  
                where a.company_id = '8888' and a.staffer_mobile='{$parenterOne['parenter_mobile']}'
                and a.staffer_id = b.staffer_id 
                ORDER BY b.postbe_ismianjob desc
                limit 0,1 ");
            if(!$staArray){
                $bakinfo = request_by_curl("https://eduappv2.kidcastle.com.cn/api/beisen/zhuzhixinxi", "mobile={$parenterOne['parenter_mobile']}", "GET", array());
                $apiArray = json_decode($bakinfo, '1');
                if ($apiArray['error'] == '0' && $request['company_id'] == '8888') {
                    //            if ($apiArray['error'] == '0' ) {
                    $staArray = array();
                    $staArray['staffer_cnname'] = $apiArray['data']['user']['name'];
                    $staArray['postOne'] = $apiArray['data']['user']['zhiwuname'];
                }
            }

            if($staArray){
                $data = array();
                $data['staffer_cnname'] = $staArray['staffer_cnname'];
                $data['post_name'] = $staArray['postOne']?$staArray['postOne']:$staArray['postTwo'];
                $data['parenter_mobile'] = $parenterOne['parenter_mobile'];

                $relativeOne = $this->DataControl->getOne('shop_coupons_apply_relative', "company_id='{$request['company_id']}' and parenter_id='{$request['parenter_id']}' ");
                if ($relativeOne) {
                    $data['isedit'] = '1';
                    $data['iseditname'] = '去编辑';
                } else {
                    $data['isedit'] = '0';
                    $data['iseditname'] = '去申请';
                }
                ajax_return(array('error' => 0, 'errortip' => "获取成功!", "result" => $data));
            } else {
                ajax_return(array('error' => 1, 'errortip' => "职工信息不存在!", "result" => array()));
            }
//            }
        } else {
            ajax_return(array('error' => 1, 'errortip' => "未获取到信息!", "result" => array()));
        }
    }

    //获取校务班组信息
    function GetCoursetypeView()
    {
        $request = Input('get.', '', 'trim,addslashes,strip_tags');
        $request['student_id'] = '';
        $this->ThisVerify($request);//验证账户 --- 吴超那边 学生id是全局的有影响故 去掉

        $clist = $this->DataControl->selectClear(" select coursetype_id,coursetype_branch,coursetype_cnname from smc_code_coursetype where company_id = '8888' ");
        if ($clist) {
            ajax_return(array('error' => 0, 'errortip' => "获取成功!", "result" => $clist));
        } else {
            ajax_return(array('error' => 1, 'errortip' => "未获取到信息!", "result" => array()));
        }
    }

    //优惠券申请 -- 同胞
    function applyCouponsSiblingsView()
    {
        $request = Input('post.', '', 'trim,addslashes,strip_tags');
        $this->ThisVerify($request);//验证账户

        if ($request['siblings_branch'] == '') {
            ajax_return(array('error' => 1, 'errortip' => "请输入同胞学号!", "result" => array()));
        }
        if ($request['siblings_cnname'] == '') {
            ajax_return(array('error' => 1, 'errortip' => "未检索到同胞姓名!", "result" => array()));
        }

        if ($request['siblings_parentname'] == '') {
            ajax_return(array('error' => 1, 'errortip' => "请输入联系人姓名!", "result" => array()));
        }
        if ($request['siblings_parentmobile'] == '') {
            ajax_return(array('error' => 1, 'errortip' => "请输入联系人手机号!", "result" => array()));
        }
        if ($request['student_prove_img'] == '') {
            ajax_return(array('error' => 1, 'errortip' => "请上传同胞证件（一）!", "result" => array()));
        }
        if ($request['siblings_prove_img'] == '') {
            ajax_return(array('error' => 1, 'errortip' => "请上传同胞证件（二）!", "result" => array()));
        }

        $couponsapplytypeOne = $this->DataControl->getFieldOne('smc_code_couponsapplytype', "applytype_playclass"
            , "company_id='{$request['company_id']}' and applytype_branch='wsctongbao'");
        if (!$couponsapplytypeOne) {
            ajax_return(array('error' => 1, 'errortip' => "同胞优惠券类型不存在无法申请!", "result" => array()));
        }

        $applyOne = $this->DataControl->getFieldOne('smc_student_coupons_apply', "apply_id"
            , "company_id='{$request['company_id']}' and applytype_branch='wsctongbao' and apply_status = '0' and school_id='{$request['school_id']}' and student_id='{$request['student_id']}'");
        if ($applyOne) {
            ajax_return(array('error' => 1, 'errortip' => "你已申请同胞优惠券，正在等待审核中，请勿重复申请!", "result" => array()));
        }

        $data = array();
        $data['company_id'] = $request['company_id'];//
        $data['school_id'] = $request['school_id'];//
        $data['student_id'] = $request['student_id'];//
        $data['applytype_branch'] = 'wsctongbao';//
        $data['apply_playclass'] = $couponsapplytypeOne['applytype_playclass'];
        $data['apply_discountstype'] = '1';//
//        $data['apply_price'] = $request['apply_price'];//
        $data['apply_discount'] = 0.9;//
//        $data['apply_minprice'] = $request['apply_minprice'];//
        $data['apply_reson'] = $request['apply_reson'];//
        $data['apply_status'] = '0';//
        $data['apply_time'] = time();
        $data['apply_cnname'] = $request['siblings_parentname']?$request['siblings_parentname']:'';//申请人 20230612补充（表字段不知什么时间加的）

        $apply_id = $this->DataControl->insertData('smc_student_coupons_apply', $data);
        if ($apply_id) {
            $dataOne = array();
            $dataOne['apply_id'] = $apply_id;//
            $dataOne['student_id'] = $request['student_id'];//
            $dataOne['student_prove_img'] = $request['student_prove_img'];//
            $dataOne['siblings_parentname'] = $request['siblings_parentname'];//
            $dataOne['siblings_parentmobile'] = $request['siblings_parentmobile'];//
            $dataOne['siblings_cnname'] = $request['siblings_cnname'];//
            $dataOne['siblings_branch'] = $request['siblings_branch'];//
            $dataOne['siblings_prove_img'] = $request['siblings_prove_img'];//
            $dataOne['siblings_createtime'] = time();//
            $applystaff_id = $this->DataControl->insertData('shop_student_coupons_apply_siblings', $dataOne);
            if ($apply_id) {
                ajax_return(array('error' => 0, 'errortip' => "已提交申请,请耐心等候!", "result" => array()));
            } else {
                ajax_return(array('error' => 1, 'errortip' => "同胞优惠券申请失败，请联系前台教师!", "result" => array()));
            }
        }
    }

    //亲戚子女 提交审核
    function applyRelativeView()
    {
        $request = Input('post.', '', 'trim,addslashes,strip_tags');
        $this->ThisVerify($request, '0');//验证账户

        if ($request['student_branch'] == '') {
            ajax_return(array('error' => 1, 'errortip' => "请输入学生编号!", "result" => array()));
        }
        if ($request['student_cnname'] == '') {
            ajax_return(array('error' => 1, 'errortip' => "请输入学生姓名!", "result" => array()));
        }

        $relativeone = $this->DataControl->selectOne("select * from shop_coupons_apply_relative WHERE parenter_id = '{$request['parenter_id']}'");
        if ($relativeone) {
            $data = array();
            $data['company_id'] = $request['company_id'];//
            $data['parenter_id'] = $request['parenter_id'];//
            $data['relative_name'] = $request['relative_name'];//
            $data['relative_mobile'] = $request['relative_mobile'];//
            $data['student_branch'] = $request['student_branch'];//
            $data['student_cnname'] = $request['student_cnname'];//
            $data['student_sex'] = $request['student_sex'];//
            $data['student_age'] = $request['student_age'];//
            $data['student_school'] = $request['student_school'];//
            $data['relative_reviewway'] = 0;//
            $data['relative_status'] = '0';//
            $data['relative_updatetime'] = time();
            $data['relative_unbound'] = '0';// 

            $relative_id = $this->DataControl->updateData('shop_coupons_apply_relative', " relative_id = '{$relativeone['relative_id']}' ", $data);
            ajax_return(array('error' => 1, 'errortip' => "亲戚子女信息更新成功!", "result" => array()));
        } else {
            $data = array();
            $data['company_id'] = $request['company_id'];//
            $data['parenter_id'] = $request['parenter_id'];//
            $data['relative_name'] = $request['relative_name'];//
            $data['relative_mobile'] = $request['relative_mobile'];//
            $data['student_branch'] = $request['student_branch'];//
            $data['student_cnname'] = $request['student_cnname'];//
            $data['student_sex'] = $request['student_sex'];//
            $data['student_age'] = $request['student_age'];//
            $data['student_school'] = $request['student_school'];//
            $data['relative_reviewway'] = 0;//
            $data['relative_status'] = '0';//
            $data['relative_createtime'] = time();
            $relative_id = $this->DataControl->insertData('shop_coupons_apply_relative', $data);
        }
        if ($relative_id) {
            ajax_return(array('error' => 0, 'errortip' => "已提交申请,请耐心等候!", "result" => array()));
        } else {
            ajax_return(array('error' => 1, 'errortip' => "申请失败!", "result" => array()));
        }
    }

    //获取 审核通过的亲戚子女信息
    function getApplyRelativeView()
    {
        $request = Input('get.', '', 'trim,addslashes,strip_tags');
        $this->ThisVerify($request, '0');//验证账户
        $sql = " SELECT r.relative_name,r.relative_mobile,r.student_branch,s.student_cnname,s.student_sex,TIMESTAMPDIFF( YEAR, s.student_birthday, CURDATE( ) ) AS student_age,c.school_cnname  
            FROM shop_coupons_apply_relative AS r 
            LEFT JOIN smc_student as s ON r.student_branch = s.student_branch  
            LEFT JOIN smc_student_enrolled as t on s.student_id = t.student_id 
            LEFT JOIN smc_school as c ON c.school_id = t.school_id 
            WHERE r.parenter_id = '{$request['parenter_id']}' and r.relative_status <> '-1' and r.relative_unbound = '0' ";
        $res = $this->DataControl->selectOne($sql);
        if ($res) {
            ajax_return(array('error' => 0, 'errortip' => "获取学员数据成功", 'result' => $res));
        } else {
            ajax_return(array('error' => 1, 'errortip' => "暂无学员数据", 'result' => array()));
        }
    }

    //优惠券申请 -- 员工、亲戚子女、特殊
    function applyCouponsChildrenView()
    {
        $request = Input('post.', '', 'trim,addslashes,strip_tags');
        $this->ThisVerify($request, '0');//验证账户

        if ($request['staff_parentname'] == '') {
            ajax_return(array('error' => 1, 'errortip' => "请输入申请人姓名!", "result" => array()));
        }
        if ($request['staff_parentmobile'] == '') {
            ajax_return(array('error' => 1, 'errortip' => "请输入申请人手机号!", "result" => array()));
        }
        $stuone = $this->DataControl->selectOne("select * from smc_student WHERE student_id = '{$request['tostudent_id']}'");

        $data = array();
        $data['parenter_id'] = $request['parenter_id'];//
        $data['company_id'] = ($request['company_id'] > 0) ? $request['company_id'] : $stuone['company_id'];//
        $data['student_id'] = $request['tostudent_id'];//
        if ($request['type'] == '1') {//员工子女

            if ($request['student_prove_img'] == '') {
                ajax_return(array('error' => 1, 'errortip' => "出生证明必须上传!", "result" => array()));
            }

            $couponsapplytypeOne = $this->DataControl->getFieldOne('smc_code_couponsapplytype', "applytype_playclass"
                , "applytype_branch='wscyuangong'");
            if (!$couponsapplytypeOne) {
                ajax_return(array('error' => 1, 'errortip' => "员工子女暂时无法申请，请联系教师!", "result" => array()));
            }
            $applyOne = $this->DataControl->getFieldOne('smc_student_coupons_apply', "apply_id"
                , "company_id='{$request['company_id']}' and applytype_branch='wscyuangong' and apply_status = '0' and school_id='{$request['school_id']}' and student_id='{$request['tostudent_id']}'");
            if ($applyOne) {
                ajax_return(array('error' => 1, 'errortip' => "你已申请员工子女优惠券，正在等待审核中，请勿重复申请!", "result" => array()));
            }

            $enrolledOne = $this->DataControl->getFieldOne('smc_student_enrolled', "school_id", "student_id='{$request['tostudent_id']}'", " ORDER BY enrolled_updatatime limit 0,1");
            if ($enrolledOne) {
                $data['school_id'] = $enrolledOne['school_id'];
            } else {
                $data['school_id'] = $request['school_id'];
            }

            $data['apply_playclass'] = $couponsapplytypeOne['applytype_playclass'];
            $data['applytype_branch'] = 'wscyuangong';//
            $data['apply_discountstype'] = 1;
            $data['apply_discount'] = 0.9;//
            $data['apply_cnname'] = $request['staff_parentname']?$request['staff_parentname']:'';//申请人 20230612补充（表字段不知什么时间加的）
        } elseif ($request['type'] == '2') {//亲戚子女
            $couponsapplytypeOne = $this->DataControl->getFieldOne('smc_code_couponsapplytype', "applytype_playclass"
                , "applytype_branch='wscqinqi'");
            if (!$couponsapplytypeOne) {
                ajax_return(array('error' => 1, 'errortip' => "亲戚子女暂时无法申请，请联系教师!", "result" => array()));
            }
            $applyOne = $this->DataControl->getFieldOne('smc_student_coupons_apply', "apply_id"
                , "company_id='{$request['company_id']}' and applytype_branch='wscqinqi' and apply_status = '0' and school_id='{$request['school_id']}' and student_id='{$request['tostudent_id']}'");
            if ($applyOne) {
                ajax_return(array('error' => 1, 'errortip' => "你已申请员工子女优惠券，正在等待审核中，请勿重复申请!", "result" => array()));
            }
            $applyOne = $this->DataControl->getFieldOne('smc_student_coupons_apply', "apply_id"
                , "company_id='{$request['company_id']}' and applytype_branch='wscqinqi' and apply_status = '0' and parenter_id='{$request['parenter_id']}'");
            if ($applyOne) {
                ajax_return(array('error' => 1, 'errortip' => "你已申请员工子女优惠券，请勿重复申请!", "result" => array()));
            }

            $enrolledOne = $this->DataControl->getFieldOne('smc_student_enrolled', "school_id", "student_id='{$request['tostudent_id']}'", " ORDER BY enrolled_updatatime limit 0,1");
            if ($enrolledOne) {
                $data['school_id'] = $enrolledOne['school_id'];
            } else {
                $data['school_id'] = $request['school_id'];
            }

            $data['apply_playclass'] = $couponsapplytypeOne['applytype_playclass'];
            $data['applytype_branch'] = 'wscqinqi';
            $data['apply_discountstype'] = 1;
            $data['apply_discount'] = 0.9;//
            $data['apply_cnname'] = $request['staff_parentname']?$request['staff_parentname']:'';//申请人 20230612补充（表字段不知什么时间加的）
        } elseif ($request['type'] == '3') {//内部折扣
//            if($request['student_prove_img'] == '' ){
//                ajax_return(array('error' => 1,'errortip' => "优惠申请单必须上传!","result"=>array() ));
//            }

            $couponsapplytypeOne = $this->DataControl->getFieldOne('smc_code_couponsapplytype', "applytype_playclass"
                , "applytype_branch='wscneibu'");
            if (!$couponsapplytypeOne) {
                ajax_return(array('error' => 1, 'errortip' => "内部折扣暂时无法申请，请联系教师!", "result" => array()));
            }
            $applyOne = $this->DataControl->getFieldOne('smc_student_coupons_apply', "apply_id"
                , "company_id='{$request['company_id']}' and applytype_branch='wscneibu' and apply_status = '0' and school_id='{$request['school_id']}' and student_id='{$request['tostudent_id']}'");
            if ($applyOne) {
                ajax_return(array('error' => 1, 'errortip' => "你已申请员工子女优惠券，正在等待审核中，请勿重复申请!", "result" => array()));
            }
            $data['apply_playclass'] = $couponsapplytypeOne['applytype_playclass'];
            if ($request['apply_discount'] > 10) {
                ajax_return(array('error' => 1, 'errortip' => "折扣比例输入错误!", "result" => array()));
            }
            $enrolledOne = $this->DataControl->getFieldOne('smc_student_enrolled', "school_id", "student_id='{$request['tostudent_id']}'", " ORDER BY enrolled_updatatime DESC limit 0,1");
            if ($enrolledOne) {
                $data['school_id'] = $enrolledOne['school_id'];
            } else {
                $data['school_id'] = $request['school_id'];
            }
            $data['applytype_branch'] = 'wscneibu';//
            $data['apply_discountstype'] = 1;
            $data['apply_discount'] = $request['apply_discount'] / 10;
            $data['apply_cnname'] = $request['staff_parentname']?$request['staff_parentname']:'';//申请人 20230612补充（表字段不知什么时间加的）
        }
        $data['apply_discountstype'] = '1';//
//        $data['apply_price'] = $request['apply_price'];//
//        $data['apply_discount'] = $request['apply_discount'];//
//        $data['apply_minprice'] = $request['apply_minprice'];//
        $data['coursetype_branch'] = $request['coursetype_branch'];//申请时选择的班组

        $data['apply_reson'] = $request['apply_reson'];//
        $data['apply_status'] = '0';//
        $data['apply_time'] = time();
        $apply_id = $this->DataControl->insertData('smc_student_coupons_apply', $data);
        if ($apply_id) {

            //以前的默认 现在要改
//            if ($request['type'] == '1' || $request['type'] == '2') {
//                $courselist = $this->DataControl->selectClear(" select c.course_id
//                                    from smc_course as c
//                                    WHERE c.coursetype_id in ('65','79675','79655','66','61') and c.company_id = '8888' ");

            if ($request['type'] == '1' || $request['type'] == '2' || $request['type'] == '3') {
//                $courselist = $this->DataControl->selectClear(" select c.course_id
//                                    from smc_code_coursetype as p,smc_course as c
//                                    WHERE p.coursetype_branch = '{$request['coursetype_branch']}' and p.company_id = '8888' and p.coursetype_id = c.coursetype_id ");
//
//                if ($courselist) {
//                    foreach ($courselist as $coursevar) {
//                        //生成审核通过的申请记录
//                        $dataotwo = array();
//                        $dataotwo['apply_id'] = $apply_id;//
//                        $dataotwo['course_id'] = $coursevar['course_id'];//
//                        $this->DataControl->insertData('', $dataotwo);
//                    }
//                }

                $coursecatList=$this->DataControl->selectClear("select a.coursecat_id from smc_code_coursecat as a,smc_code_coursetype as b where a.coursetype_id=b.coursetype_id and a.company_id='8888' and b.coursetype_branch='{$request['coursetype_branch']}'");

                if($coursecatList){
                    foreach($coursecatList as $coursecatOne){

                        $data=array();
                        $data['apply_id']=$apply_id;
                        $data['coursecat_id']=$coursecatOne['coursecat_id'];
                        $this->DataControl->insertData("smc_student_coupons_applycoursecat",$data);
                    }
                }
            }

            $dataOne = array();
            $dataOne['apply_id'] = $apply_id;//
            $dataOne['staff_parentname'] = $request['staff_parentname'];//姓名
            $dataOne['staff_parentmobile'] = $request['staff_parentmobile'];//手机号
            $dataOne['staff_workerduty'] = $request['staff_workerduty'];//职务

            $dataOne['student_prove_img'] = $request['student_prove_img'];//证明材料

            if ($request['type'] == '1') {//员工子女
                $dataOne['student_id'] = $request['tostudent_id'];//
            } elseif ($request['type'] == '2') {//亲戚子女
                $dataOne['student_id'] = $request['tostudent_id'];//
                $dataOne['relative_name'] = $request['relative_name'];//
                $dataOne['relative_mobile'] = $request['relative_mobile'];//
            } elseif ($request['type'] == '3') {//内部折扣
                $dataOne['student_id'] = $request['tostudent_id'];//
                $dataOne['relative_name'] = $request['relative_name'];//
                $dataOne['relative_mobile'] = $request['relative_mobile'];//
            }

            $dataOne['staff_createtime'] = time();//
            $applystaff_id = $this->DataControl->insertData('shop_student_coupons_apply_staff', $dataOne);
            if ($apply_id) {
                ajax_return(array('error' => 0, 'errortip' => "已提交申请,请耐心等候!", "result" => array()));
            } else {
                ajax_return(array('error' => 1, 'errortip' => "申请失败!", "result" => array()));
            }
        }
    }


    //优惠券申请 -- 商品优惠券
    function applyCouponsGoodsView()
    {
        $request = Input('post.', '', 'trim,addslashes,strip_tags');
        $this->ThisVerify($request);//验证账户

        if ($request['procou_scoreprove'] == '') {
            ajax_return(array('error' => 1, 'errortip' => "请选择成绩后提交!", "result" => array()));
        }
        if ($request['procou_scoreproveimg'] == '') {
            ajax_return(array('error' => 1, 'errortip' => "请上传成绩资料后提交!", "result" => array()));
        }
        if ($request['procou_name'] == '') {
            ajax_return(array('error' => 1, 'errortip' => "请输入申请人姓名!", "result" => array()));
        }
        if ($request['procou_mobile'] == '') {
            ajax_return(array('error' => 1, 'errortip' => "请输入申请人联系方式!", "result" => array()));
        }

        $data = array();
        $data['parenter_id'] = $request['parenter_id'];//
        $data['company_id'] = $request['company_id'];//
        $data['school_id'] = $request['school_id'];//
        $data['student_id'] = $request['student_id'];//
        $data['applytype_branch'] = 'wscshangpin';//
        $data['apply_playclass'] = 1;//
        $data['apply_discountstype'] = '0';//
//        $data['apply_price'] = $request['apply_price'];//
        if ($request['procou_scoreprove'] == 'A+' || $request['procou_scoreprove'] == '优+' || $request['procou_scoreprove'] == '100') {
            $data['apply_price'] = 50;
        } else {
            $data['apply_price'] = 20;
        }
//        $data['apply_discount'] = $request['apply_discount'];//
//        $data['apply_minprice'] = $request['apply_minprice'];//
        $data['apply_reson'] = $request['apply_reson'];//
        $data['apply_status'] = '0';//
        $data['apply_time'] = time();
        $apply_id = $this->DataControl->insertData('smc_student_coupons_apply', $data);
        if ($apply_id) {
            $dataOne = array();
            $dataOne['apply_id'] = $apply_id;//
            $dataOne['student_id'] = $request['student_id'];//
            $dataOne['procou_scoreproveimg'] = $request['procou_scoreproveimg'];//
            $dataOne['procou_scoreprove'] = $request['procou_scoreprove'];//
            $dataOne['procou_name'] = $request['procou_name'];//
            $dataOne['procou_mobile'] = $request['procou_mobile'];//
            $dataOne['procou_createtime'] = time();//
            $applystaff_id = $this->DataControl->insertData('shop_student_coupons_apply_procou', $dataOne);
            if ($apply_id) {
                ajax_return(array('error' => 0, 'errortip' => "已提交申请,请耐心等候!", "result" => array()));
            } else {
                ajax_return(array('error' => 1, 'errortip' => "申请失败!", "result" => array()));
            }
        }
    }

    //获取海报轮播图   poster
    function getSharePosterView()
    {
        $request = Input('get.', '', 'trim,addslashes,strip_tags');
//        $this->ThisVerify($request);//验证账户

        $datawhere = ' ';
//        if($request['company_id'] != '8888') {//临时
//            //学校 筛选
//            if (isset($request['school_id']) && $request['school_id'] != '') {
//                $datawhere .= " and h.school_id = '{$request['school_id']}'";
//            }
//        }

        //当前时间
        $nowtime = date("Y-m-d H:i:s", time());// H:i:s
        if ($request['isschoolcrm'] != '1') {
            $datawhere .= " and a.activity_starttime <= '{$nowtime}' and a.activity_endtime >= '{$nowtime}'";
        }


        $sql = "select a.company_id,a.activity_id as shareposter_id,a.activity_name as shareposter_name,a.activity_demoimg as shareposter_demoimg,a.activity_img as shareposter_imgurl,a.activity_sharecount as shareposter_count 
                from crm_sell_activity as a 
                LEFT join crm_sell_activity_school as h ON a.activity_id = h.activity_id
                WHERE a.company_id = '{$request['company_id']}' 
                {$datawhere}
                AND a.activity_pattern = '2' 
                GROUP BY a.activity_id ";

        $res = $this->DataControl->selectClear($sql);
        if ($res) {
            $allnum = 0;
            foreach ($res as &$resvar) {
                $resvar['school_id'] = $request['school_id'];

                $allnum += $resvar['shareposter_count'];
            }
        }
        if ($res) {
            ajax_return(array('error' => 0, 'errortip' => "海报模板数据成功", 'result' => $res, 'allnum' => $allnum));
        } else {
            ajax_return(array('error' => 1, 'errortip' => "暂无海报模板数据", 'result' => array()));
        }
    }

    //获取我的优惠券 xxxxxxxxxxxxx
    function getMyCouponsView()
    {
        $request = Input('get.', '', 'trim,addslashes,strip_tags');
        $this->ThisVerify($request);//验证账户


        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "SELECT c.coupons_id,c.coupons_class,c.coupons_type,c.coupons_playclass,c.coupons_pid,c.coupons_name,c.coupons_exittime,c.coupons_price,c.coupons_discount,c.coupons_minprice,c.coupons_isuse,c.couponsrules_id,t.applytype_play as couponsrules_play,t.applytype_id,t.applytype_coexist   
                FROM smc_student_coupons AS c 
                LEFT JOIN smc_student_coupons_apply as a ON c.apply_id = a.apply_id
                LEFT JOIN smc_code_couponsapplytype as t ON a.applytype_branch = t.applytype_branch
                WHERE c.company_id = '{$request['company_id']}' and c.student_id = '{$request['student_id']}' and c.coupons_isuse = '0' and  c.coupons_exittime > unix_timestamp()
                GROUP BY c.coupons_id 
                limit {$pagestart},{$num} ";//
//        LEFT JOIN shop_code_couponsrules as r ON t.couponsrules_id=r.couponsrules_id

        $res = $this->DataControl->selectClear($sql);

        if ($res) {
            foreach ($res as &$resvar) {
                if ($resvar['coupons_exittime'] > time()) {
                    $resvar['isinvalid'] = 0;
                } else {
                    $resvar['isinvalid'] = 1;
                }

                $resvar['coupons_exittime'] = date("Y-m-d", $resvar['coupons_exittime']) . ' ' . '00:00';

                if ($resvar['coupons_type'] == '1') {
                    $resvar['coupons_price'] = ($resvar['coupons_discount'] * 10) . "折";
                }else{
                    $resvar['coupons_price'] = (int)$resvar['coupons_price'];
                }

                $resvar['coupons_class_name'] = $resvar['coupons_name'];
//                $resvar['coupons_class_name'] = '优惠券的类型';
                if ($resvar['couponsrules_play']) {
                    $resvar['ticket_rule'] = $resvar['couponsrules_play'];
                } else {
                    $dataOne = $this->DataControl->selectOne(" select * from shop_code_couponsrules WHERE couponsrules_id = '{$resvar['couponsrules_id']}' ");
                    $resvar['ticket_rule'] = $dataOne['couponsrules_play'];
                }
            }
        }
        $allnum = $this->DataControl->selectOne(" SELECT count(c.coupons_id) as num 
                FROM smc_student_coupons AS c 
                LEFT JOIN smc_market_ticket as t ON t.ticket_id = c.ticket_id 
                WHERE c.company_id = '{$request['company_id']}'  and c.student_id = '{$request['student_id']}' 
                ");

        $field = array();
        $field["coupons_id"] = "优惠券ID";
        $field["coupons_class_name"] = "优惠券类型名称";
        $field["coupons_playclass"] = "优惠券使用类型：0项目优惠券1订单优惠券";
        $field["coupons_pid"] = "优惠券编号";
        $field["coupons_name"] = "优惠券名称";
        $field["coupons_exittime"] = "截止时间";
        $field["coupons_type"] = "优惠方式0-减价 1-折扣";
        $field["coupons_price"] = "优惠金额";
        $field["coupons_discount"] = "抵扣比率";
        $field["coupons_minprice"] = "最低消费金额";
        $field["coupons_isuse"] = "使用状态0:未用 1：已用 -1:失效";
        $field["ticket_rule"] = "优惠券使用规则（目前没有这个字段）";

        if ($res) {
            $result["field"] = $field;
            $result["data"] = $res;
            $result["allnum"] = $allnum['num'];
            ajax_return(array('error' => 0, 'errortip' => "我的优惠券数据成功", 'result' => $result));
        } else {
            $result["field"] = $field;
            $result["data"] = array();
            ajax_return(array('error' => 1, 'errortip' => "暂无可用优惠券", 'result' => $result));
        }
    }

    //获取 领取优惠券列表  xxxxxxxxxxx
    function getReceiveCouponsListView()
    {
        $request = Input('get.', '', 'trim,addslashes,strip_tags');
        $this->ThisVerify($request);//验证账户

        //喆哥屏蔽所有的 领取的券 2023-11-01
        $result = array();
        $result["data"] = array();
        ajax_return(array('error' => 1, 'errortip' => "暂无待领取优惠券", 'result' => $result));
        die;

        $now = time();
        $datawhere = " c.company_id = '{$request['company_id']}' and c.applytype_status = '1' and c.applytype_port = 1 ";
        //优惠券发放日期限制
        $datawhere .= " and c.applytype_startday <= CURDATE() and c.applytype_endday >= CURDATE()  ";
        //优惠券使用日期限制
        $datawhere .= " and (c.applytype_deadline = '' or c.applytype_deadline is null or c.applytype_deadline >= CURDATE())";

        $datawhere .= " AND NOT EXISTS ( SELECT 1 FROM smc_student_coupons AS cc,smc_student_coupons_apply AS a WHERE cc.apply_id = a.apply_id
AND a.applytype_branch = c.applytype_branch AND cc.student_id = '{$request['student_id']}'
and ((cc.coupons_isuse = '0' and cc.coupons_bindingtime<='{$now}' and cc.coupons_exittime>='{$now}') or (c.applytype_isgetdouble='0' AND cc.coupons_isuse = '1'))limit 0,1)";

        $datawhere .= "and (c.applytype_applyschool=0 or (c.applytype_applyschool=1 and exists(select 1 from smc_couponsapplytype_schoolapply as cts where cts.applytype_id=c.applytype_id and cts.school_id='{$request['school_id']}')))";

        //是否有收入
        $incomeOne = $this->DataControl->selectOne("select income_id from smc_school_income where company_id='{$request['company_id']}' and student_id='{$request['student_id']}'
and income_type = 0 and income_price > 0 limit 0,1");
        if ($incomeOne) {
            $datawhere .= " and ((c.applytype_getscope = 2 or c.applytype_getscope = 0) or (c.applytype_getscope = 3 and EXISTS(select 1 from smc_fee_policy as po,smc_fee_policy_student as pst where po.policy_id=pst.policy_id and pst.student_id='{$request['student_id']}' and po.applytype_branch=c.applytype_branch and po.policy_status=1 and po.company_id='{$request['company_id']}' and po.policy_class=1 and po.policy_startday<= CURDATE() and po.policy_endday >= CURDATE())))";
        } else {
            $datawhere .= " and ((c.applytype_getscope = 1 or c.applytype_getscope = 0) or (c.applytype_getscope = 3 and EXISTS(select 1 from smc_fee_policy as po,smc_fee_policy_student as pst where po.policy_id=pst.policy_id and pst.student_id='{$request['student_id']}' and po.applytype_branch=c.applytype_branch and po.policy_status=1 and po.company_id='{$request['company_id']}' and po.policy_class=1 and po.policy_startday<= CURDATE() and po.policy_endday >= CURDATE())))";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select c.applytype_id as couponsrules_id,
                c.applytype_cnname as couponsrules_name,
                c.applytype_remark as couponsrules_remark,
                c.applytype_price as couponsrules_price,
                c.applytype_startday as couponsrules_starttimes,
                c.applytype_endday as couponsrules_stoptimes,
                c.applytype_play as couponsrules_play,
                c.applytype_get as couponsrules_get,c.applytype_coexist,c.applytype_type,c.applytype_discount,c.applytype_branch,c.applytype_minprice  
                from smc_code_couponsapplytype as c 
                WHERE {$datawhere}
                order by c.applytype_id asc limit {$pagestart},{$num} ";

        $res = $this->DataControl->selectClear($sql);

        $dataarray = array();
        if ($res) {
            $studentOne = $this->DataControl->getFieldOne("smc_student", "from_client_id,student_cnname", "student_id = '{$request['student_id']}'");
            foreach ($res as $key => $resvar) {
                if($resvar['applytype_branch'] == 'woldstupre'){
                    if (!$this->DataControl->selectOne("SELECT i.income_id FROM smc_school_income AS i
WHERE i.student_id = '{$request['student_id']}' AND i.income_price > 0  AND FROM_UNIXTIME(i.income_confirmtime,'%Y-%m-%d') < '2021-08-06' limit 0,1")){
                        unset($res[$key]);
                    }
                }elseif($resvar['applytype_branch'] == 'btjE2' || $resvar['applytype_branch'] == 'btjTQ2' || $resvar['applytype_branch'] == 'btjSZ2'){
                    unset($res[$key]);
                }elseif($resvar['applytype_branch'] == '2024summer' || $resvar['applytype_branch'] == '2025summer2' || $resvar['applytype_branch'] == '2024HJ' || $resvar['applytype_branch'] == 'douyinE2025'){
                    unset($res[$key]);
                }elseif($resvar['applytype_branch'] == 'oldstupre'){
                    unset($res[$key]);
                }elseif($resvar['applytype_branch'] == 'pddT' || $resvar['applytype_branch'] == 'pddSZ' || $resvar['applytype_branch'] == 'pddE'){
                    unset($res[$key]);
                }elseif($resvar['applytype_branch'] == 'wkFirstpurchase' || $resvar['applytype_branch'] == 'IRCFirstpurchase' || $resvar['applytype_branch'] == 'IRCFirstpurchase1' || $resvar['applytype_branch'] == 'IRCFirstpurchase2'){
                    unset($res[$key]);
                }elseif($resvar['applytype_branch'] == 'RSFadd' || $resvar['applytype_branch'] == '50kyq'){
                    unset($res[$key]);
                }elseif($resvar['applytype_branch'] == 'summerTS' ){
                    unset($res[$key]);
                }elseif($resvar['applytype_branch'] == 'anqinbao' || $resvar['applytype_branch'] == 'anqinbaoW'){
                    unset($res[$key]);
                }elseif($resvar['applytype_branch'] == 'growreneww' || $resvar['applytype_branch'] == 'growrenew'){

                    $sql = "select a.income_confirmtime,FROM_UNIXTIME(a.income_confirmtime,'%Y-%m-%d') as createDay
                        from smc_school_income as a,smc_course as b 
                        where a.course_id=b.course_id and b.coursetype_id='65' and a.student_id='{$request['student_id']}' and a.hourstudy_id>0 and a.income_price>0
                        order by a.income_confirmtime asc,a.income_id asc 
                        limit 0,1
                        ";

                    $incomeInfo=$this->DataControl->selectOne($sql);

                    if(!$incomeInfo || $incomeInfo['createDay']<'2021-08-06' || intval((time()-$incomeInfo['income_confirmtime'])/86400)<310){
                        unset($res[$key]);
                    }

                }elseif($resvar['applytype_branch'] == 'zhanhuiD'){
                    $yztradeOne = $this->DataControl->selectOne(" SELECT c.open_id 
                            FROM smc_student_family as a,smc_parenter as b,gmc_yztrade as c 
                            WHERE a.student_id = '{$request['student_id']}' and a.family_isdefault = '1' 
                            and a.parenter_id = b.parenter_id 
                            and b.parenter_mobile = c.yztrade_mobile 
                            limit 0,1 ");
                    if(!$yztradeOne){
                        unset($res[$key]);
                    }
                }elseif($resvar['applytype_branch'] == 'ts500'){
                    if (!$this->DataControl->selectOne(" select client_id from smc_student as s,crm_client as c where s.student_id = '{$request['student_id']}' and s.from_client_id = c.client_id and c.client_id > 0 and c.channel_id = '593' ")) {
                        unset($res[$key]);
                    }
                }elseif($resvar['applytype_branch'] == 'FirstpurchaseFC' || $resvar['applytype_branch'] == 'FirstpurchaseW'){

                    $sql="select a.order_id from smc_payfee_order as a,smc_payfee_order_course as b,smc_course as c 
                        where a.order_pid=b.order_pid and b.course_id=c.course_id and a.school_id='{$request['school_id']}' and a.student_id='{$request['student_id']}' and a.order_status>=0 and c.coursetype_id=65";

                    if($this->DataControl->selectOne($sql)){
                        unset($res[$key]);
                    }else{
                        $course_array=array('92360','92361','92362','92363','92364','92365','92366','92367','92368','92369','92370','92371');

                        $courseStr=implode("','",$course_array);

                        $sql = "select a.order_id from smc_payfee_order as a,smc_payfee_order_course as b 
                        where a.order_pid=b.order_pid and a.school_id='{$request['school_id']}' and a.student_id='{$request['student_id']}' and a.order_status=4 and FROM_UNIXTIME(a.order_updatatime,'%Y-%m-%d')=CURDATE() and b.course_id in ('".$courseStr."') and exists(select 1 from smc_student_coursebalance as x where x.school_id=a.school_id and x.student_id=a.student_id and x.course_id=b.course_id and x.coursebalance_figure>0)limit 0,1";

                        if(!$this->DataControl->selectOne($sql)){
                            unset($res[$key]);
                        }
                    }

                }elseif($resvar['applytype_branch'] == 'FirstpurchaseWK' || $resvar['applytype_branch'] == 'dongbeiqihang' || $resvar['applytype_branch'] == 'dongbeiqihangWY'){

                    $sql="select a.order_id from smc_payfee_order as a,smc_payfee_order_course as b,smc_course as c 
                        where a.order_pid=b.order_pid and b.course_id=c.course_id and a.school_id='{$request['school_id']}' and a.student_id='{$request['student_id']}' and a.order_status>=0 and c.coursetype_id=65";

                    if($this->DataControl->selectOne($sql)){
                        unset($res[$key]);
                    }

                }elseif($resvar['applytype_branch'] == 'FirstpurchaseWKN' || $resvar['applytype_branch'] == 'FirstpurchaseE' || $resvar['applytype_branch'] == 'Firstpurchase1' || $resvar['applytype_branch'] == 'Firstpurchase2' || $resvar['applytype_branch'] == 'Firstpurchase3' ){
                    unset($res[$key]);
                }elseif($resvar['applytype_branch'] == 'YNZ' || $resvar['applytype_branch'] == 'YNsunmmer' || $resvar['applytype_branch'] == 'YNSZ' || $resvar['applytype_branch'] == 'YNZnanchang'){

                    unset($res[$key]);//20221025 喆哥，去除微商城的优惠券领取

//                    $chanlesql = "select c.client_id from smc_student as s ,crm_client as c ,smc_student_enrolled as e
//                            where s.student_id = '{$request['student_id']}' 
//                            and s.from_client_id = c.client_id 
//                            and s.student_id = e.student_id 
//                            and e.enrolled_status in (0,1)
//                            and (c.channel_id = '146' or (c.channel_id = '591' and e.school_id in ('584','2112','2270','2306') ) )
//                            limit 0,1 ";
//                    $chanleOne = $this->DataControl->selectOne($chanlesql);
//                    if(!$chanleOne){
//                        unset($res[$key]);
//                    }elseif($resvar['applytype_branch'] == 'YNZ'){
//
//                        $sql = "select a.coupons_usetime 
//                            from smc_student_coupons as a,smc_student_coupons_apply as b 
//                            where a.apply_id=b.apply_id and applytype_branch='YNZ' and a.student_id='{$request['student_id']}' and a.coupons_isuse=1 and b.school_id='{$request['school_id']}'
//                            order by a.coupons_usetime desc
//                            limit 0,1
//                            ";
//                        $queryOne=$this->DataControl->selectOne($sql);
//                        if($queryOne && intval((time()-$queryOne['coupons_usetime'])/86400)<330){
//                            unset($res[$key]);
//                        }
//
//                    }elseif($resvar['applytype_branch'] == 'YNSZ'){
//
//                        $sql = "select a.coupons_usetime 
//                            from smc_student_coupons as a,smc_student_coupons_apply as b 
//                            where a.apply_id=b.apply_id and applytype_branch='YNSZ' and a.student_id='{$request['student_id']}' and a.coupons_isuse=1 and b.school_id='{$request['school_id']}'
//                            order by a.coupons_usetime desc
//                            limit 0,1
//                            ";
//                        $queryOne=$this->DataControl->selectOne($sql);
//                        if($queryOne && intval((time()-$queryOne['coupons_usetime'])/86400)<330){
//                            unset($res[$key]);
//                        }
//
//                    }elseif($resvar['applytype_branch'] == 'YNZnanchang'){
//
//                        $sql = "select a.coupons_usetime 
//                            from smc_student_coupons as a,smc_student_coupons_apply as b 
//                            where a.apply_id=b.apply_id and applytype_branch='YNZnanchang' and a.student_id='{$request['student_id']}' and a.coupons_isuse=1 and b.school_id='{$request['school_id']}'
//                            order by a.coupons_usetime desc
//                            limit 0,1
//                            ";
//                        $queryOne=$this->DataControl->selectOne($sql);
//                        if($queryOne && intval((time()-$queryOne['coupons_usetime'])/86400)<330){
//                            unset($res[$key]);
//                        }
//
//                    }
                }elseif($resvar['applytype_branch'] == 'btjaqfuli' || $resvar['applytype_branch'] == 'btjszfuli' || $resvar['applytype_branch'] == 'btjHJfuliT1' || $resvar['applytype_branch'] == 'btjHJfuliT2T3' || $resvar['applytype_branch'] == 'btjEfuli' || $resvar['applytype_branch'] == 'btj3'){
                    //2022 06 14 首询券改为首购券 逻辑更改    喆哥：被推荐这个不需要微商城可领
                    unset($res[$key]);
                }
                elseif($resvar['applytype_branch'] == 'xkshouxun' || $resvar['applytype_branch'] == 'wxkshouxun' || $resvar['applytype_branch'] == 'wkshouxun'){ //2022 06 14 首询券改为首购券 逻辑更改

                    unset($res[$key]);//20221025 喆哥，去除微商城的优惠券领取

//                    $inquirywhere = ' ';
//                    if($resvar['applytype_branch'] == 'xkshouxun' || $resvar['applytype_branch'] == 'wxkshouxun'){//133   141   11352    EJ，ES，AN；
//                        $inquirywhere = " and ca.coursetype_id = '65' and ca.coursecat_id in ('133','141','11352') ";
//                    }elseif($resvar['applytype_branch'] == 'aqshouxun'){
//                        $inquirywhere = " and ca.coursetype_id = '79655' ";
//                    }elseif($resvar['applytype_branch'] == 'wkshouxun'){
//                        $inquirywhere = " and ca.coursetype_id = '65' and ca.coursecat_id = '135' ";
//                    }elseif($resvar['applytype_branch'] == 'ircshouxun'){
//                        $inquirywhere = " and ca.coursetype_id = '61' and ca.coursecat_id = '139' ";
//                    }
//                    if($incomeOne){
////                        $stoday= strtotime(date("Y-m-d",time()));
////                        $etoday= $stoday+86400;
//                        $sql = "select ca.student_id,ca.coursetype_id,ca.coursecat_id
//                    from crm_student_track as ca
//                    where ca.track_isactive = '1' and ca.coursetype_id > '0' and ca.coursecat_id > '0' and ca.student_id = '{$request['student_id']}'
//                     {$inquirywhere}
//                    order by ca.track_id DESC
//                    limit 0,1";//and ca.track_createtime >= '{$stoday}' and ca.track_createtime < '{$etoday}'
//                        $audtionOne = $this->DataControl->selectOne($sql);
////                    print_r($audtionOne);
//                        if(!$audtionOne){
//                            unset($res[$key]);
//                        }
//                    }else{
//                        $sql = "select cl.client_id,ca.track_createtime,ca.coursetype_id,ca.coursecat_id
//                from  crm_client as cl
//                LEFT JOIN crm_client_track as  ca On ca.client_id= cl.client_id
//                where ca.track_isactive = '1' and ca.coursetype_id > '0' and ca.coursecat_id > '0' and cl.client_id='{$studentOne['from_client_id']}' {$inquirywhere}
//                and exists(select 1 from smc_student_family as sf,smc_parenter as p where p.parenter_id=sf.parenter_id and sf.student_id='{$request['student_id']}' and cl.client_mobile=p.parenter_mobile)
//                order by track_id DESC
//                limit 0,1";
//                        $trackOne = $this->DataControl->selectOne($sql);
////                    print_r($trackOne);
//                        if($trackOne){
////                            $time = date("Y-m-d",$trackOne['track_createtime']);
////                            if ($time != date("Y-m-d",time())) {
////
////                                unset($res[$key]);
////                            }
//                        }else {
//
//                            unset($res[$key]);
//                        }
//                    }
                }
                elseif($resvar['applytype_branch'] == 'aqshouxun' || $resvar['applytype_branch'] == 'ircshouxun'){ //2022 06 14 首询券改为首购券 逻辑更改

                    unset($res[$key]);//20221025 喆哥，去除微商城的优惠券领取

//                    $inquirywhere = ' ';
//                    if($resvar['applytype_branch'] == 'xkshouxun' || $resvar['applytype_branch'] == 'wxkshouxun'){//133   141   11352    EJ，ES，AN；
//                        $inquirywhere = " and ca.coursetype_id = '65' and ca.coursecat_id in ('133','141','11352') ";
//                    }elseif($resvar['applytype_branch'] == 'aqshouxun'){
//                        $inquirywhere = " and ca.coursetype_id = '79655' ";
//                    }elseif($resvar['applytype_branch'] == 'wkshouxun'){
//                        $inquirywhere = " and ca.coursetype_id = '65' and ca.coursecat_id = '135' ";
//                    }elseif($resvar['applytype_branch'] == 'ircshouxun'){
//                        $inquirywhere = " and ca.coursetype_id = '61' and ca.coursecat_id = '139' ";
//                    }
//                    if($incomeOne){
//                        $stoday= strtotime(date("Y-m-d",time()));
//                        $etoday= $stoday+86400;
//                        $sql = "select ca.student_id,ca.coursetype_id,ca.coursecat_id
//                    from crm_student_track as ca
//                    where ca.track_isactive = '1' and ca.coursetype_id > '0' and ca.coursecat_id > '0' and ca.student_id = '{$request['student_id']}'
//                    and ca.track_createtime >= '{$stoday}' and ca.track_createtime < '{$etoday}' {$inquirywhere}
//                    order by ca.track_id DESC
//                    limit 0,1";
//                        $audtionOne = $this->DataControl->selectOne($sql);
////                    print_r($audtionOne);
//                        if(!$audtionOne){
//
//                        unset($res[$key]);
//                        }
//                    }else{
//                        $sql = "select cl.client_id,ca.track_createtime,ca.coursetype_id,ca.coursecat_id
//                from  crm_client as cl
//                LEFT JOIN crm_client_track as  ca On ca.client_id= cl.client_id
//                where ca.track_isactive = '1' and ca.coursetype_id > '0' and ca.coursecat_id > '0' and cl.client_id='{$studentOne['from_client_id']}' {$inquirywhere}
//                and exists(select 1 from smc_student_family as sf,smc_parenter as p where p.parenter_id=sf.parenter_id and sf.student_id='{$request['student_id']}' and cl.client_mobile=p.parenter_mobile)
//                order by track_id DESC
//                limit 0,1";
//                        $trackOne = $this->DataControl->selectOne($sql);
////                    print_r($trackOne);
//                        if($trackOne){
//                            $time = date("Y-m-d",$trackOne['track_createtime']);
//                            if ($time != date("Y-m-d",time())) {
//
//                        unset($res[$key]);
//                            }
//                        }else {
//
//                        unset($res[$key]);
//                        }
//                    }
                }elseif($resvar['applytype_branch'] == 'jdbkkshouxun'){//2022 06 14 首询券改为首购券 逻辑更改

                    unset($res[$key]);//20221025 喆哥，去除微商城的优惠券领取
//                    if($incomeOne){
//                        $stoday= strtotime(date("Y-m-d",time()));
//                        $etoday= $stoday+86400;
//                        $sql = "select t.student_id,t.coursetype_id,t.coursecat_id
//                    from crm_student_track as t
//                    where t.track_isactive = '1' and t.coursetype_id > '0' and t.coursecat_id > '0' and t.student_id = '{$request['student_id']}'
//                    and t.track_createtime >= '{$stoday}' and t.track_createtime < '{$etoday}'
//                    order by t.track_id  DESC
//                    limit 0,1";
//                        $audtionOne = $this->DataControl->selectOne($sql);
//                        if(!$audtionOne){
//
//                        unset($res[$key]);
//                        }
//                    }else{
//
//                        unset($res[$key]);
//                    }
                }
//                elseif($resvar['applytype_branch'] == 'xkshouxun' || $resvar['applytype_branch'] == 'wxkshouxun' || $resvar['applytype_branch'] == 'aqshouxun' || $resvar['applytype_branch'] == 'wkshouxun' || $resvar['applytype_branch'] == 'ircshouxun'){ //学科首询券   安亲首询券   小奇多维成长首询券    阅读首询券  (20220214改）
//
//                    $inquirywhere = ' ';
//                    if($resvar['applytype_branch'] == 'xkshouxun'|| $resvar['applytype_branch'] == 'wxkshouxun' ){//133   141   11352    EJ，ES，AN；
//                        $inquirywhere = " and ca.coursetype_id = '65' and ca.coursecat_id in ('133','141','11352') ";
//                    }elseif($resvar['applytype_branch'] == 'aqshouxun'){
//                        $inquirywhere = " and ca.coursetype_id = '79655' ";
//                    }elseif($resvar['applytype_branch'] == 'wkshouxun'){
//                        $inquirywhere = " and ca.coursetype_id = '65' and ca.coursecat_id = '135' ";
//                    }elseif($resvar['applytype_branch'] == 'ircshouxun'){
//                        $inquirywhere = " and ca.coursetype_id = '61' and ca.coursecat_id = '139' ";
//                    }
//                    if($incomeOne){
//                        $sql = " select ca.student_id,ca.class_id,ca.class_cnname,ca.audition_visittime,ca.coursetype_id
//                        from crm_student_audition as ca
//                        where ca.audition_isvisit = '1' and ca.student_id = '{$request['student_id']}' {$inquirywhere}
//                        order by ca.audition_visittime DESC,ca.audition_id DESC ";
//                    }else{
//                        $sql = "select cl.client_id,ca.class_id,ca.class_cnname,ca.audition_visittime,ca.coursetype_id
//                from  crm_client as cl
//                LEFT JOIN crm_client_audition as  ca On ca.client_id= cl.client_id
//                where ca.audition_isvisit = '1' and cl.client_id='{$studentOne['from_client_id']}' {$inquirywhere}
//                and exists(select 1 from smc_student_family as sf,smc_parenter as p where p.parenter_id=sf.parenter_id and sf.student_id='{$request['student_id']}' and cl.client_mobile=p.parenter_mobile)
//                order by audition_visittime DESC,audition_id DESC";
//                    }
//                    $audtionOne = $this->DataControl->selectOne($sql);
//                    if($audtionOne){
//                        $time = date("Y-m-d",strtotime($audtionOne['audition_visittime']));
//                        if ($time == date("Y-m-d",time())) {
//
//                            //当前 班组/班种 是否有收入
//                            $incomewhere = ' ';
//                            if ($resvar['applytype_branch'] == 'xkshouxun'|| $resvar['applytype_branch'] == 'wxkshouxun' ) {//133   141   11352    EJ，ES，AN；
//                                $incomewhere = " and ca.coursetype_id = '65' and ca.coursecat_id in ('133','141','11352') ";
//                            } elseif ($resvar['applytype_branch'] == 'aqshouxun') {
//                                $incomewhere = " and ca.coursetype_id = '79655' ";
//                            } elseif ($resvar['applytype_branch'] == 'wkshouxun') {
//                                $incomewhere = " and ca.coursetype_id = '65' and ca.coursecat_id = '135' ";
//                            } elseif ($resvar['applytype_branch'] == 'ircshouxun') {
//                                $incomewhere = " and ca.coursetype_id = '61' and ca.coursecat_id = '139' ";
//                            }
//                            if ($this->DataControl->selectOne("select income_id from smc_school_income as i
//                        left join smc_course as ca ON i.course_id = ca.course_id
//                        where i.company_id='{$request['company_id']}' and i.student_id='{$request['student_id']}'
//                        and i.income_type = 0 and i.income_price > 0 {$incomewhere}
//                        limit 0,1")) {
//                                unset($res[$key]);
//                            }
//                        }else {
//                            unset($res[$key]);
//                        }
//                    }else {
//                        unset($res[$key]);
//                    }
//
//                }
                elseif ($resvar['applytype_branch'] == 'wscshouxun' || $resvar['applytype_branch'] == 'wscydshouxun' || $resvar['applytype_branch'] == 'wscshiting' || $resvar['applytype_branch'] == 'wscxsznqjiama' || $resvar['applytype_branch'] == 'jdbshouxun' || $resvar['applytype_branch'] == 'jdbkkshouxun'  ) {
                    if( $resvar['applytype_branch'] == 'jdbkkshouxun'){
                        $sql = " select a.student_id,a.class_id,a.class_cnname,a.audition_visittime,a.coursetype_id   
                        from crm_student_audition as a 
                        where a.audition_isvisit = '1' and a.student_id = '{$request['student_id']}'
                        order by audition_visittime DESC,audition_id DESC ";
                    }else {
                        $sql = "select cl.client_id,ca.class_id,ca.class_cnname,ca.audition_visittime,ca.coursetype_id 
              from  crm_client as cl
              LEFT JOIN crm_client_audition as  ca On ca.client_id= cl.client_id
              where ca.audition_isvisit = '1' and cl.client_id='{$studentOne['from_client_id']}'
              and exists(select 1 from smc_student_family as sf,smc_parenter as p where p.parenter_id=sf.parenter_id and sf.student_id='{$request['student_id']}' and cl.client_mobile=p.parenter_mobile)
              order by audition_visittime DESC,audition_id DESC";
                    }
                    $audtionOne = $this->DataControl->selectOne($sql);

                    if ($audtionOne) {
                        if ($resvar['applytype_branch'] == 'jdbshouxun' || $resvar['applytype_branch'] == 'jdbkkshouxun') {
                            if ($audtionOne['coursetype_id'] != '61' && $audtionOne['coursetype_id'] != '65' && $audtionOne['coursetype_id'] != '79655'  ) {
                                unset($res[$key]);
                            }

                            $time = strtotime($audtionOne['audition_visittime']) + 24 * 3600;
                            if ($time < time()) {
                                unset($res[$key]);
                            }
                        }elseif ($resvar['applytype_branch'] == 'wscshouxun') {
                            //微商城美语首询券
                            $sql = "select h.hourstudy_id 
                                from smc_student_hourstudy as h,smc_class as cl,smc_course as sc 
                                where h.class_id=cl.class_id and cl.course_id=sc.course_id and cl.school_id='{$request['school_id']}' and h.student_id='{$request['student_id']}' and sc.coursetype_id='65'
                                limit 0,1";
                            if ($this->DataControl->selectOne($sql)) {
                                unset($res[$key]);
                            }
                            $time = strtotime($audtionOne['audition_visittime']) + 24 * 3600;
                            if ($time < time()) {
                                unset($res[$key]);
                            }
                        } elseif ($resvar['applytype_branch'] == 'wscydshouxun') {
                            //微商城阅读首询券
                            $sql = "select h.hourstudy_id 
                                from smc_student_hourstudy as h,smc_class as cl,smc_course as sc 
                                where h.class_id=cl.class_id and cl.course_id=sc.course_id and cl.school_id='{$request['school_id']}' and h.student_id='{$request['student_id']}' and sc.coursetype_id='61'
                                limit 0,1";
                            if ($this->DataControl->selectOne($sql)) {
                                unset($res[$key]);
                            }
                            $time = strtotime($audtionOne['audition_visittime']) + 24 * 3600;
                            if ($time < time()) {
                                unset($res[$key]);
                            }
                        } elseif ($resvar['applytype_branch'] == 'wscshiting' || $resvar['applytype_branch'] == 'wscxsznqjiama') {
                            //微商城试听券   微商城新生周年庆加码券
                            $time = strtotime($audtionOne['audition_visittime']) + 30 * 24 * 3600;
                            if ($time < strtotime(date("Y-m-d"))) {
                                unset($res[$key]);
                            }
                        }
                        if (strpos($audtionOne['class_cnname'], '课辅') || strpos($audtionOne['class_cnname'], '课后辅导')) {
                            unset($res[$key]);
                        }
                    } else {
                        unset($res[$key]);
                    }
                } elseif ($resvar['applytype_branch'] == 'wscxinxiao') {
                    //微商城新校券
                    $newSchoolOne = $this->DataControl->selectOne("select school_id from smc_school WHERE school_id='{$this->schoolOne['school_id']}' and school_isnewschool = '1' and school_newend > CURDATE() and school_newstart < CURDATE()");
                    if (!$newSchoolOne) {
                        unset($res[$key]);
                    }
                    if (!$this->DataControl->selectOne("select student_id from smc_student_enrolled WHERE school_id = '{$request['school_id']}' and student_id = '{$request['student_id']}' and (enrolled_status = '0' or enrolled_status = '1')  ")) {
                        unset($res[$key]);
                    }
                }

                // 下边是 之前的代码

//                //之前的代码
//                //首询券领取限制
//                if ($resvar['couponsrules_id'] == '210') {
//                    //判断用户是否领取过
//                    if ($this->DataControl->selectOne("select c.coupons_id from smc_student_coupons as c
//                                              LEFT JOIN smc_student_coupons_apply as a ON c.apply_id = a.apply_id
//                                              LEFT JOIN smc_code_couponsapplytype as t ON a.applytype_branch = t.applytype_branch
//                                              WHERE c.coupons_class = '3' and c.student_id='{$request['student_id']}' and t.applytype_id = '{$resvar['couponsrules_id']}' ") ||
//                        $this->DataControl->selectOne("select * from smc_student_coupons WHERE coupons_class = '3' and student_id='{$request['student_id']}' and couponsrules_id = '{$resvar['couponsrules_id']}' ")
//                    ) {
//                        $resvar["isget"] = '1';
//                    } else {
//                        $resvar["isget"] = '0';
//                    }
//
//                } elseif ($resvar['couponsrules_id'] == '212') {
//                    //判断用户是否领取过
//                    if ($this->DataControl->selectOne("select c.coupons_id from smc_student_coupons as c
//                                              LEFT JOIN smc_student_coupons_apply as a ON c.apply_id = a.apply_id
//                                              LEFT JOIN smc_code_couponsapplytype as t ON a.applytype_branch = t.applytype_branch
//                                              WHERE c.coupons_class = '3' and c.student_id='{$request['student_id']}' and t.applytype_id = '{$resvar['couponsrules_id']}' ") ||
//                        $this->DataControl->selectOne("select * from smc_student_coupons WHERE coupons_class = '3' and student_id='{$request['student_id']}' and couponsrules_id = '{$resvar['couponsrules_id']}' ")
//                    ) {
//                        $resvar["isget"] = '1';
//                    } else {
//                        $resvar["isget"] = '0';
//                    }
//
//                    $nowtime = time();
//                    $newSchoolOne = $this->DataControl->selectOne("select * from smc_school WHERE school_id='{$request['school_id']}' and school_isnewschool = '1' and UNIX_TIMESTAMP(school_newend) > '{$nowtime}' ");
//                    if ($newSchoolOne) {
//                        $resvar['isnewschool'] = 1;
//                    } else {
//                        $resvar['isnewschool'] = 0;
//                    }
//                } elseif ($resvar['couponsrules_id'] == '498') {
//                    //判断是否在8月6号之前耗过课才可以领取300优惠券
//                    if ($this->DataControl->selectOne("SELECT i.income_id FROM smc_school_income AS i
//WHERE i.student_id = '{$request['student_id']}' AND i.income_price > 0  AND FROM_UNIXTIME(i.income_confirmtime,'%Y-%m-%d') < '2021-08-06' limit 0,1")
//                    ) {
//                        $resvar["isget"] = '0';
//                    } else {
//                        $resvar["isget"] = '1';
//                    }
//
//                    //判断用户是否领取过且未使用
//                    if ($this->DataControl->selectOne("SELECT c.coupons_id FROM smc_student_coupons AS c, smc_student_coupons_apply AS a, smc_code_couponsapplytype AS t
//WHERE c.coupons_class = '3' AND c.apply_id = a.apply_id AND a.applytype_branch = t.applytype_branch AND c.student_id = '{$request[' student_id ']}' AND t.applytype_id = '{$resvar[' couponsrules_id ']}' AND c.coupons_isuse = '0'") ||
//                        $this->DataControl->selectOne("select * from smc_student_coupons WHERE coupons_class = '3' and student_id='{$request['student_id']}' and couponsrules_id = '{$resvar['couponsrules_id']}' AND coupons_isuse = '0'")
//                    ) {
//                        $resvar["isget"] = '1';
//                    } else {
//                        $resvar["isget"] = '0';
//                    }
//                }
//                $dataone = array();
//                $dataone = $resvar;
//                if ($resvar['couponsrules_id'] == '212') {
//                    if ($resvar['isnewschool'] == 1) {
//                        $dataarray[] = $dataone;
//                    }
//                } else {
//                    $dataarray[] = $dataone;
//                }
            }
        }
        $field = array();
        $field["couponsrules_id"] = "优惠券申请规则ID";
        $field["couponsrules_name"] = "商品优惠券规则名称";
        $field["couponsrules_remark"] = "优惠券描述";
        $field["couponsrules_price"] = "优惠金额";
        $field["couponsrules_starttimes"] = "开始时间";
        $field["couponsrules_stoptimes"] = "截止时间";
        $field["couponsrules_play"] = "使用规则";
        $field["couponsrules_get"] = "领用规则";
        $field["isget"] = "是否领取过  0 没有领取过  1 领取过";
        $field["applytype_type"] = "优惠方式0-减价 1-折扣";
        $field["applytype_discount"] = "抵扣比率";
        $field["applytype_minprice"] = "最低消费金额";


//        $allnum = count($dataarray);
        $allnum = count($res);

        if ($res) {
            $result["field"] = $field;
//            $result["data"] = $dataarray;
            $result["data"] = $res;
            $result["allnum"] = $allnum;
            ajax_return(array('error' => 0, 'errortip' => "获取待领取优惠券列表成功", 'result' => $result));
        } else {
            $result["field"] = $field;
            $result["data"] = array();
            ajax_return(array('error' => 1, 'errortip' => "暂无待领取优惠券", 'result' => $result));
        }
    }

    //获取 领取优惠券列表  -- //20201027 修改领取规则为新的表
    function getReceiveCouponsOneView()
    {
        $request = Input('post.', '', 'trim,addslashes,strip_tags');
        $this->ThisVerify($request);//验证账户

        $this->ThisVerify($request);//验证账户

        $name = $this->DataControl->getFieldOne("smc_student", "student_cnname", "student_id = '{$request['student_id']}'");
        $mobile = $this->DataControl->getFieldOne("smc_parenter", "parenter_mobile", "parenter_id = '{$request['parenter_id']}'");


            $applytypeOne = $this->DataControl->getOne("smc_code_couponsapplytype", "applytype_id='{$request['couponsrules_id']}' and company_id='{$request['company_id']}'");
            if (!$applytypeOne) {
                ajax_return(array('error' => 1, 'errortip' => "无该可领取优惠券", 'result' => array()));
            }

            $now=time();

            $sql="select poc.coupons_pid
              from smc_payfee_order_coupons as poc,smc_payfee_order as po,smc_student_coupons as sc,smc_student_coupons_apply as ca 
              where poc.order_pid=po.order_pid and poc.coupons_pid=sc.coupons_pid and sc.apply_id=ca.apply_id and po.student_id='{$request['student_id']}' and po.order_status>=0 and po.order_status<4 and ca.applytype_branch = '{$applytypeOne['applytype_branch']}' limit 0,1";

            if ($this->DataControl->selectOne($sql)) {
                ajax_return(array('error' => 1, 'errortip' => "该优惠券正在使用中，请勿重复领取", 'result' => array()));
            }


            $sql="SELECT c.coupons_id 
              FROM smc_student_coupons AS c,smc_student_coupons_apply AS a,smc_code_couponsapplytype as co 
              WHERE c.apply_id = a.apply_id and co.applytype_branch=a.applytype_branch
AND a.applytype_branch = '{$applytypeOne['applytype_branch']}' AND c.student_id = '{$request['student_id']}' 
              and ((c.coupons_isuse = '0' and c.coupons_bindingtime<='{$now}' and c.coupons_exittime>='{$now}') or (co.applytype_isgetdouble='0' AND c.coupons_isuse = '1')) limit 0,1";

            if ($this->DataControl->selectOne($sql)) {
                ajax_return(array('error' => 1, 'errortip' => "已存在未使用的同类优惠券，请勿重复领取", 'result' => array()));
            }

            //针对根据 跟踪班组适配优惠券
            if( $applytypeOne['applytype_id'] == '543'){
                //2022 06 14 首询券改为首购券 逻辑更改
//                $sql = " select a.student_id,a.class_id,a.class_cnname,a.audition_visittime,a.coursetype_id
//                        from crm_student_audition as a
//                        where a.audition_isvisit = '1' and a.student_id = '{$request['student_id']}'
//                        order by audition_visittime DESC,audition_id DESC ";
//                $audtionOne = $this->DataControl->selectOne($sql);

                $stoday= strtotime(date("Y-m-d",time()));
                $etoday= $stoday+86400;
                $sql = "select t.student_id,t.coursetype_id,t.coursecat_id 
                    from crm_student_track as t 
                    where t.track_isactive = '1' and t.coursetype_id > '0' and t.coursecat_id > '0' and t.student_id = '{$request['student_id']}' 
                    and t.track_createtime >= '{$stoday}' and t.track_createtime < '{$etoday}' 
                    order by t.track_id DESC 
                    limit 0,1";
                $audtionOne = $this->DataControl->selectOne($sql);
            }elseif( $applytypeOne['applytype_id'] == '542'){
                $studentOne = $this->DataControl->getFieldOne("smc_student", "from_client_id,student_cnname", "student_id = '{$request['student_id']}'");

                $sql = "select cl.client_id,ca.class_id,ca.class_cnname,ca.audition_visittime,ca.coursetype_id  
              from  crm_client as cl
              LEFT JOIN crm_client_audition as  ca On ca.client_id= cl.client_id
              where ca.audition_isvisit = '1' and cl.client_id='{$studentOne['from_client_id']}'
              and exists(select 1 from smc_student_family as sf,smc_parenter as p where p.parenter_id=sf.parenter_id and sf.student_id='{$request['student_id']}' and cl.client_mobile=p.parenter_mobile)
              order by audition_visittime DESC,audition_id DESC";
                $audtionOne = $this->DataControl->selectOne($sql);
            }

            if ($applytypeOne['applytype_id'] == '498' || $applytypeOne['applytype_id'] == '551'){
                if (!$this->DataControl->selectOne("SELECT i.income_id FROM smc_school_income AS i
WHERE i.student_id = '{$request['student_id']}' AND i.income_price > 0  AND FROM_UNIXTIME(i.income_confirmtime,'%Y-%m-%d') < '2021-08-06' limit 0,1")){
                    ajax_return(array('error' => 1, 'errortip' => "您不在2021年8月6号之前就读的学员，无法领取此续费优惠券", 'result' => array()));
                }
            }elseif($applytypeOne['applytype_id'] == '543'){
                $regOne = $this->DataControl->selectOne("select group_concat(r.coursetype_id) as coursetype_idstr
                        from smc_student_registerinfo as r 
                        where r.student_id='{$request['student_id']}' and r.info_status = '1' and  r.coursetype_id <> '{$audtionOne['coursetype_id']}' ");
                if($regOne['coursetype_idstr'] == '' || is_null($regOne['coursetype_idstr'])) {
                    ajax_return(array('error' => 1, 'errortip' => "您未报过其他班组", 'result' => array()));
                }

                $regOne = $this->DataControl->selectOne("SELECT h.hour_id 
                    from smc_student_hourstudy as h,smc_class as c,smc_course as u 
                    WHERE h.student_id = '{$request['student_id']}' and h.hourstudy_checkin = '1' and  h.class_id = c.class_id  and c.course_id = u.course_id 
                    and u.coursetype_id = '{$audtionOne['coursetype_id']}' limit 0,1");
                if($regOne['hour_id'] > 0 ) {
                    ajax_return(array('error' => 1, 'errortip' => "您已经报过该班组", 'result' => array()));
                }
            }elseif($applytypeOne['applytype_id'] == '549') {
                if (!$this->DataControl->selectOne("SELECT i.income_id FROM smc_school_income AS i 
            left join smc_course as c ON i.course_id = c.course_id
            WHERE i.student_id = '{$request['student_id']}' AND i.income_price > 0 and c.coursetype_id = '65' 
            AND FROM_UNIXTIME(i.income_confirmtime,'%Y-%m-%d') < '2022-02-17' limit 0,1")) {
                    $this->error = true;
                    $this->errortip = "您不是在2022年02月17号之前就读的美语学员，无法领取此优惠券";
                    return false;
                }

                if (!$this->DataControl->selectOne("SELECT p.pay_id  
                    FROM smc_payfee_order as o 
                    LEFT JOIN smc_payfee_order_pay as p ON o.order_pid = p.order_pid
                    LEFT JOIN smc_code_paytype as pt ON p.paytype_code = pt.paytype_code
                    LEFT JOIN smc_payfee_order_course as oc ON p.order_pid = oc.order_pid
                    LEFT JOIN smc_course as c ON oc.course_id = c.course_id
                    WHERE o.student_id = '{$request['student_id']}' 
                    and p.pay_issuccess = '1' 
                    and p.pay_successtime > '1645027200'
                    and pt.paytype_ischarge = '1' 
                    and c.coursecat_id in ('133','141','11352') limit 0,1")) {
                    $this->error = true;
                    $this->errortip = "您在2022年02月17号之后没有购买过EJ/ES/AN的课程，无法领取此优惠券";
                    return false;
                }
            }

            if($applytypeOne['applytype_getscope']==3){
                $sql="select policy_trial_num from smc_fee_policy 
                  where policy_class=1 and company_id='{$request['company_id']}' and applytype_branch='{$applytypeOne['applytype_branch']}' and policy_status=1 and policy_startday<=CURDATE() and policy_endday>=CURDATE() order by policy_createtime desc limit 0,1";

                $policyOne=$this->DataControl->selectOne($sql);

                $sql="select count(sc.coupons_id) as num from smc_student_coupons as sc,smc_student_coupons_apply as ca where sc.apply_id=ca.apply_id and sc.student_id='{$request['student_id']}' and ca.applytype_branch='{$applytypeOne['applytype_branch']}'";

                $conponsList=$this->DataControl->selectOne($sql);
                if($policyOne['policy_trial_num']<=$conponsList['num']){
                    ajax_return(array('error' => 1, 'errortip' => "领取次数不可超过".$policyOne['policy_trial_num'].'次', 'result' => array()));
                }
            }
            $data = array();
            $data['company_id'] = $request['company_id'];
            $data['school_id'] = $request['school_id'];
            $data['student_id'] = $request['student_id'];
            $data['applytype_branch'] = $applytypeOne['applytype_branch'];
            $data['apply_playclass'] = $applytypeOne['applytype_playclass'];
            $data['apply_discountstype'] = $applytypeOne['applytype_type'];
            if ($applytypeOne['applytype_type'] == '0') {
                $data['apply_price'] = $applytypeOne['applytype_price'];
            } else {
                $data['apply_discount'] = $applytypeOne['applytype_discount'];
            }
            $data['apply_minprice'] = $applytypeOne['applytype_minprice'];
            $data['apply_status'] = "4";
            $data['apply_isshop'] = $applytypeOne['applytype_isshop'];
            $data['apply_reson'] = '优惠券领取，符合领取规则，自动生成审核通过的申请单';
            $data['apply_time'] = time();
            $data['apply_cnname'] = "学生：".$name['student_cnname'].";手机号：".$mobile['parenter_mobile'];
            $data['apply_refusetime'] = time();
            $apply_id = $this->DataControl->insertData('smc_student_coupons_apply', $data);
            if ($apply_id) {
                if( ($applytypeOne['applytype_id'] == '542' || $applytypeOne['applytype_id'] == '543') && $request['company_id'] == '8888' ){
//                    $courselist = $this->DataControl->selectClear(" select c.course_id
//                                    from smc_code_coursecat as t
//                                    LEFT JOIN smc_course as c ON t.coursecat_id = c.coursecat_id
//                                    WHERE t.coursetype_id = '{$audtionOne['coursetype_id']}' and t.company_id = '8888' ");
//                    if($courselist){
//                        foreach ($courselist as $coursevar){
//                            //生成审核通过的申请记录
//                            $dataotwo = array();
//                            $dataotwo['apply_id'] = $apply_id;//
//                            $dataotwo['course_id'] = $coursevar['course_id'];//
//                            $this->DataControl->insertData('', $dataotwo);
//                        }
//                    }

                    $coursecatList=$this->DataControl->selectClear("select a.coursecat_id from smc_code_coursecat as a,smc_code_coursetype as b where a.coursetype_id=b.coursetype_id and a.company_id='8888' and b.coursetype_id='{$audtionOne['coursetype_id']}'");

                    if($coursecatList){
                        foreach($coursecatList as $coursecatOne){

                            $data=array();
                            $data['apply_id']=$apply_id;
                            $data['coursecat_id']=$coursecatOne['coursecat_id'];
                            $this->DataControl->insertData("smc_student_coupons_applycoursecat",$data);
                        }
                    }


                }elseif (($applytypeOne['applytype_id'] == '545' || $applytypeOne['applytype_id'] == '547' || $applytypeOne['applytype_id'] == '550') && $request['company_id'] == '8888') {
                    $studentOne = $this->DataControl->getFieldOne("smc_student", "from_client_id,student_cnname", "student_id = '{$request['student_id']}'");
                    //是否有收入
                    $incomeOne = $this->DataControl->selectOne("select income_id from smc_school_income
where company_id='{$request['company_id']}' and student_id='{$request['student_id']}'
and income_type = 0 and income_price > 0 limit 0,1");

                    $inquirywhere = ' ';
                    if($applytypeOne['applytype_branch'] == '545' || $applytypeOne['applytype_branch'] == '550'){//133   141   11352    EJ，ES，AN；
                        $inquirywhere = " and ca.coursetype_id = '65' and ca.coursecat_id in ('133','141','11352') ";
                    }elseif($applytypeOne['applytype_branch'] == '546'){
                        $inquirywhere = " and ca.coursetype_id = '79655' ";
                    }elseif($applytypeOne['applytype_branch'] == '547'){
                        $inquirywhere = " and ca.coursetype_id = '65' and ca.coursecat_id = '135' ";
                    }elseif($applytypeOne['applytype_branch'] == '548'){
                        $inquirywhere = " and ca.coursetype_id = '61' and ca.coursecat_id = '139' ";
                    }

                    if($incomeOne){
//                        $stoday= strtotime(date("Y-m-d",time()));
//                        $etoday= $stoday+86400;
                        $sql = "select ca.student_id,ca.coursetype_id,ca.coursecat_id,ca.track_createtime 
                    from crm_student_track as ca 
                    where ca.track_isactive = '1' and ca.coursetype_id > '0' and ca.coursecat_id > '0' and ca.student_id = '{$request['student_id']}' 
                    {$inquirywhere}
                    order by ca.track_id DESC 
                    limit 0,1";//and ca.track_createtime >= '{$stoday}' and ca.track_createtime < '{$etoday}'
                        $trackOne = $this->DataControl->selectOne($sql);
                    }else{
                        $sql = "select cl.client_id,ca.track_createtime,ca.coursetype_id,ca.coursecat_id  
                        from  crm_client as cl
                        LEFT JOIN crm_client_track as  ca On ca.client_id= cl.client_id
                        where ca.track_isactive = '1' and ca.coursetype_id > '0' and ca.coursecat_id > '0' and cl.client_id='{$studentOne['from_client_id']}' {$inquirywhere}
                        and exists(select 1 from smc_student_family as sf,smc_parenter as p where p.parenter_id=sf.parenter_id and sf.student_id='{$request['student_id']}' and cl.client_mobile=p.parenter_mobile)
                        order by track_id DESC 
                        limit 0,1";
                        $trackOne = $this->DataControl->selectOne($sql);
                    }

                    if($trackOne){
//                        $time = date("Y-m-d",$trackOne['track_createtime']);
//                        if ($time == date("Y-m-d",time())) {

                            $courseinquirywhere = ' 1 ';
                            if($applytypeOne['applytype_branch'] == '545' || $applytypeOne['applytype_branch'] == '550'){//133   141   11352    EJ，ES，AN；
                                $courseinquirywhere = " and t.coursetype_id = '65' and t.coursecat_id in ('133','141','11352') ";
                            }elseif($applytypeOne['applytype_branch'] == '546'){
                                $courseinquirywhere = " and t.coursetype_id = '79655' ";
                            }elseif($applytypeOne['applytype_branch'] == '547'){
                                $courseinquirywhere = " and t.coursetype_id = '65' and t.coursecat_id = '135' ";
                            }elseif($applytypeOne['applytype_branch'] == '548'){
                                $courseinquirywhere = " and t.coursetype_id = '61' and t.coursecat_id = '139' ";
                            }

//                            $courselist = $this->DataControl->selectClear(" select c.course_id
//                                    from smc_code_coursecat as t
//                                    LEFT JOIN smc_course as c ON t.coursecat_id = c.coursecat_id
//                                    WHERE {$courseinquirywhere} and t.company_id = '8888' ");
//                            if ($courselist) {
//                                foreach ($courselist as $coursevar) {
//                                    //生成审核通过的申请记录
//                                    $dataotwo = array();
//                                    $dataotwo['apply_id'] = $apply_id;//
//                                    $dataotwo['course_id'] = $coursevar['course_id'];//
//                                    $this->DataControl->insertData('', $dataotwo);
//                                }
//                            }

                            $coursecatList=$this->DataControl->selectClear("select a.coursecat_id from smc_code_coursecat as t,smc_code_coursetype as b where {$courseinquirywhere} and t.coursetype_id=b.coursetype_id and t.company_id='8888' ");

                            if($coursecatList){
                                foreach($coursecatList as $coursecatOne){

                                    $data=array();
                                    $data['apply_id']=$apply_id;
                                    $data['coursecat_id']=$coursecatOne['coursecat_id'];
                                    $this->DataControl->insertData("smc_student_coupons_applycoursecat",$data);
                                }
                            }

//                        }
                    }
                }elseif (($applytypeOne['applytype_id'] == '546' || $applytypeOne['applytype_id'] == '548' ) && $request['company_id'] == '8888') {
                    $studentOne = $this->DataControl->getFieldOne("smc_student", "from_client_id,student_cnname", "student_id = '{$request['student_id']}'");
                    //是否有收入
                    $incomeOne = $this->DataControl->selectOne("select income_id from smc_school_income
where company_id='{$request['company_id']}' and student_id='{$request['student_id']}'
and income_type = 0 and income_price > 0 limit 0,1");

                    $inquirywhere = ' ';
                    if($applytypeOne['applytype_branch'] == '545' || $applytypeOne['applytype_branch'] == '550'){//133   141   11352    EJ，ES，AN；
                        $inquirywhere = " and ca.coursetype_id = '65' and ca.coursecat_id in ('133','141','11352') ";
                    }elseif($applytypeOne['applytype_branch'] == '546'){
                        $inquirywhere = " and ca.coursetype_id = '79655' ";
                    }elseif($applytypeOne['applytype_branch'] == '547'){
                        $inquirywhere = " and ca.coursetype_id = '65' and ca.coursecat_id = '135' ";
                    }elseif($applytypeOne['applytype_branch'] == '548'){
                        $inquirywhere = " and ca.coursetype_id = '61' and ca.coursecat_id = '139' ";
                    }

                    if($incomeOne){
                        $stoday= strtotime(date("Y-m-d",time()));
                        $etoday= $stoday+86400;
                        $sql = "select ca.student_id,ca.coursetype_id,ca.coursecat_id,ca.track_createtime 
                    from crm_student_track as ca 
                    where ca.track_isactive = '1' and ca.coursetype_id > '0' and ca.coursecat_id > '0' and ca.student_id = '{$request['student_id']}' 
                    and ca.track_createtime >= '{$stoday}' and ca.track_createtime < '{$etoday}' {$inquirywhere}
                    order by ca.track_id DESC 
                    limit 0,1";
                        $trackOne = $this->DataControl->selectOne($sql);
                    }else{
                        $sql = "select cl.client_id,ca.track_createtime,ca.coursetype_id,ca.coursecat_id  
                        from  crm_client as cl
                        LEFT JOIN crm_client_track as  ca On ca.client_id= cl.client_id
                        where ca.track_isactive = '1' and ca.coursetype_id > '0' and ca.coursecat_id > '0' and cl.client_id='{$studentOne['from_client_id']}' {$inquirywhere}
                        and exists(select 1 from smc_student_family as sf,smc_parenter as p where p.parenter_id=sf.parenter_id and sf.student_id='{$request['student_id']}' and cl.client_mobile=p.parenter_mobile)
                        order by track_id DESC 
                        limit 0,1";
                        $trackOne = $this->DataControl->selectOne($sql);
                    }

                    if($trackOne){
                        $time = date("Y-m-d",$trackOne['track_createtime']);
                        if ($time == date("Y-m-d",time())) {

                            $courseinquirywhere = ' 1 ';
                            if($applytypeOne['applytype_branch'] == '545' || $applytypeOne['applytype_branch'] == '550'){//133   141   11352    EJ，ES，AN；
                                $courseinquirywhere = " and t.coursetype_id = '65' and t.coursecat_id in ('133','141','11352') ";
                            }elseif($applytypeOne['applytype_branch'] == '546'){
                                $courseinquirywhere = " and t.coursetype_id = '79655' ";
                            }elseif($applytypeOne['applytype_branch'] == '547'){
                                $courseinquirywhere = " and t.coursetype_id = '65' and t.coursecat_id = '135' ";
                            }elseif($applytypeOne['applytype_branch'] == '548'){
                                $courseinquirywhere = " and t.coursetype_id = '61' and t.coursecat_id = '139' ";
                            }

//                            $courselist = $this->DataControl->selectClear(" select c.course_id
//                                    from smc_code_coursecat as t
//                                    LEFT JOIN smc_course as c ON t.coursecat_id = c.coursecat_id
//                                    WHERE {$courseinquirywhere} and t.company_id = '8888' ");
//                            if ($courselist) {
//                                foreach ($courselist as $coursevar) {
//                                    //生成审核通过的申请记录
//                                    $dataotwo = array();
//                                    $dataotwo['apply_id'] = $apply_id;//
//                                    $dataotwo['course_id'] = $coursevar['course_id'];//
//                                    $this->DataControl->insertData('', $dataotwo);
//                                }
//                            }

                            $coursecatList=$this->DataControl->selectClear("select a.coursecat_id from smc_code_coursecat as t,smc_code_coursetype as b where {$courseinquirywhere} and t.coursetype_id=b.coursetype_id and t.company_id='8888' ");

                            if($coursecatList){
                                foreach($coursecatList as $coursecatOne){

                                    $data=array();
                                    $data['apply_id']=$apply_id;
                                    $data['coursecat_id']=$coursecatOne['coursecat_id'];
                                    $this->DataControl->insertData("smc_student_coupons_applycoursecat",$data);
                                }
                            }
                        }
                    }
                }else {
//                    if ($applytypeOne['applytype_applycourse'] == 1) {
//                        $courseList = $this->DataControl->selectClear("select course_id from
//where applytype_id='{$applytypeOne['applytype_id']}'");
//                        if ($courseList) {
//                            foreach ($courseList as $courseOne) {
//                                if ($courseOne['course_id'] > 0) {
//                                    $data = array();
//                                    $data['apply_id'] = $apply_id;
//                                    $data['course_id'] = $courseOne['course_id'];
//                                    $this->DataControl->insertData("", $data);
//                                }
//                            }
//                        }
//                    }
                    if ($applytypeOne['applytype_applycoursecat'] == 1) {
                        $coursecatList = $this->DataControl->selectClear("select coursecat_id from smc_couponsapplytype_coursecatapply where applytype_id='{$applytypeOne['applytype_id']}'");
                        if ($coursecatList) {
                            foreach ($coursecatList as $coursecatOne) {
                                if ($coursecatOne['coursecat_id'] > 0) {
                                    $data = array();
                                    $data['apply_id'] = $apply_id;
                                    $data['coursecat_id'] = $coursecatOne['coursecat_id'];
                                    $this->DataControl->insertData("smc_student_coupons_applycoursecat", $data);
                                }
                            }
                        }
                    }
                }

                $data = array();
                do {
                    $coupons_pid = str_pad(mt_rand(*********, *********), 9, "0", STR_PAD_BOTH);
                } while ($this->DataControl->selectOne("select coupons_id from smc_student_coupons where coupons_pid='{$coupons_pid}' and company_id='{$request['company_id']}' limit 0,1"));
                $data['coupons_pid'] = $coupons_pid;
                $data['company_id'] = $request['company_id'];
                $data['student_id'] = $request['student_id'];
                $data['apply_id'] = $apply_id;
                $data['coupons_class'] = '3';
                $data['coupons_type'] = $applytypeOne['applytype_type'];
                if ($applytypeOne['applytype_type'] == '0') {
                    $data['coupons_price'] = $applytypeOne['applytype_price'];
                } else {
                    $data['coupons_discount'] = $applytypeOne['applytype_discount'];
                }
                $data['coupons_minprice'] = $applytypeOne['applytype_minprice'];
                $data['coupons_name'] = $applytypeOne['applytype_cnname'];
                $data['coupons_reason'] = '自领优惠券';
                $data['coupons_playclass'] = $applytypeOne['applytype_playclass'];
                $data['coupons_bindingtime'] = strtotime(date("Y-m-d"));
                if ($applytypeOne['applytype_validitytype'] == 0) {
                    $data['coupons_exittime'] = strtotime("+" . $applytypeOne['applytype_validitydays'] . ' day', strtotime(date("Y-m-d"))) + 24 * 3600 - 1;
                } else {
                    $data['coupons_exittime'] = strtotime($applytypeOne['applytype_deadline'] . ' 23:59:59');
                }
                $data['coupons_createtime'] = time();
                $this->DataControl->insertData('smc_student_coupons', $data);
                ajax_return(array('error' => 0, 'errortip' => "领取成功", 'result' => array()));
            } else {
                ajax_return(array('error' => 1, 'errortip' => "领取失败", 'result' => array()));
            }
//        }
    }
    //针对

    //分享海报留名单的页面 -- 集团 学校 教师 分享出去的
    function getJoinStuComShareView(){
        $request = Input('post.','','trim,addslashes,strip_tags');

        if($request['poster_id'] == ''){
            $res = array('error' => '1', 'errortip' => '请输入海报id!',"bakfuntion"=>"errormotify");
            ajax_return($res);
        }
        $clientOne = $this->DataControl->selectOne("select * from crm_client WHERE client_mobile = '{$request['adf_mobile']}' ");
        //client_cnname = '{$request['adf_kidname']}' and  戚总让调整手机号即可 20201119
        if($clientOne){
            $res = array('error' => '1', 'errortip' => '孩子信息已存在!',"bakfuntion"=>"errormotify");
            ajax_return($res);
        }
        if($request['adf_name'] == ''){
            $res = array('error' => '1', 'errortip' => '请输入您的称呼!',"bakfuntion"=>"errormotify");
            ajax_return($res);
        }
        if($request['adf_kidname'] == ''){
            $res = array('error' => '1', 'errortip' => '请输入您的孩子姓名!',"bakfuntion"=>"errormotify");
            ajax_return($res);
        }
        if($request['adf_mobile'] == ''){
            $res = array('error' => '1', 'errortip' => '请输入您的手机号码!',"bakfuntion"=>"errormotify");
            ajax_return($res);
        }
        if($request['Authcode'] == ''){
            $res = array('error' => '1', 'errortip' => '请输入您获取的短信验证码!',"bakfuntion"=>"errormotify");
            ajax_return($res);
        }
        //地址   adf_address

        $sendmisrz = $this->DataControl->getOne('crm_mislog',"mislog_mobile='{$request['adf_mobile']}' and mislog_tilte = '微商城海报分享'","order by mislog_time DESC");
        if($sendmisrz['mislog_sendcode'] !== $request['Authcode'] && $request['Authcode'] !== 'mohism' && $request['Authcode'] !== 'kidcastle'){
            $res = array('error' => '1', 'errortip' => '短信验证码错误!',"bakfuntion"=>"errormotify");
            ajax_return($res);
        }else{
            //生成有效名单
            $url = 'https://crmapi.kedingdang.com/PhoneActivity/addPhoneActivityAction';
//            $url = 'http://crmapi.kcclassin.com/PhoneActivity/addPhoneActivityAction';
            $crmData = array();
            if($request['school_id'] > 0){
                $crmData['isschool'] = 1;//对应的学校ID
            }
            $crmData['school_id'] = $request['school_id'];
            $crmData['activity_id'] = $request['poster_id'];
            $crmData['client_acttype'] = 3;
            $crmData['client_cnname'] = $request['adf_kidname'];
            $crmData['client_mobile'] = $request['adf_mobile'];
            $crmData['client_address'] = $request['adf_address'];//填写的地址

            $crmData['marketer_id'] = $request['marketer_id'];//推荐职工id
            $crmData['company_id'] = $request['company_id'];//集团id

            $crmData['client_patriarchname'] = $request['adf_name'];//家长姓名
            //$post_data = array();
//            $res = $this->request_post($url, $crmData);
            $res = request_by_curl($url,dataEncode($crmData),"POST",array());
            print_r($res);die;//不用注释目前这个是正确的，在那边给了返回值
//            $json_play = new \Webjson();
//            $cardarray = $json_play->decode($res,"1");
//            if($cardarray['error']=='1'){
//                ajax_return(array('error' => 0,'errortip' => "你的信息提交成功！",'result' => array()));
//            }else{
//                ajax_return(array('error' => 1,'errortip' => "提交失败！",'result' => array()));
//            }
        }
    }

    //我的推荐 --- 2023六月一号起推荐提供给瞿神---- 方便发积分
    function MyRecommendLiuyiView(){
        $request = Input('get.', '', 'trim,addslashes,strip_tags');
        $this->ThisVerify($request);//验证账户

        $list = $this->DataControl->selectClear("select client_id,client_cnname,client_tracestatus,client_sex,client_img,client_age,from_unixtime(client_createtime,'%m.%d %H:%i') as client_createtime from crm_client where client_stubranch = '{$request['student_branch']}'");
        if($list){
            $num = 0;
            foreach($list as &$val){
                $isbook = $this->DataControl->getFieldOne("smc_student_registerinfo","info_id","from_client_id = '{$val['client_id']}' and info_status = '1'");
                if($isbook){
                    $val['client_tracestatus'] = '已报名';
                    $num++;
                }else{
                    $val['client_tracestatus'] = '未报名';
                }
            }
            $result = array();
            $result['list'] = $list;
            $result['allnum'] = count($list);
            $result['booknum'] = $num;
            ajax_return(array('error' => 0,'errortip' => "获取成功",'result' => $result));
        }else{
            ajax_return(array('error' => 1,'errortip' => "暂无推荐学生",'result' => array()));
        }
    }
    //我的推荐
    function MyRecommendListView(){
        $request = Input('get.', '', 'trim,addslashes,strip_tags');
//        $this->ThisVerify($request);//验证账户

        $list = $this->DataControl->selectClear("select client_id,client_cnname,client_tracestatus,client_sex,client_img,client_age,from_unixtime(client_createtime,'%m.%d %H:%i') as client_createtime from crm_client where client_stubranch = '{$request['student_branch']}'");
        if($list){
            $num = 0;
            foreach($list as &$val){
                $isbook = $this->DataControl->getFieldOne("smc_student_registerinfo","info_id","from_client_id = '{$val['client_id']}' and info_status = '1'");
                if($isbook){
                    $val['client_tracestatus'] = '已报名';
                    $num++;
                }else{
                    $val['client_tracestatus'] = '未报名';
                }
            }
            $result = array();
            $result['list'] = $list;
            $result['allnum'] = count($list);
            $result['booknum'] = $num;
            ajax_return(array('error' => 0,'errortip' => "获取成功",'result' => $result));
        }else{
            ajax_return(array('error' => 1,'errortip' => "暂无推荐学生",'result' => array()));
        }
    }

    //获取活动详情
    function getShareComPosterOneView(){
        $request = Input('get.', '', 'trim,addslashes,strip_tags');
        if($request['company_id']) {
            $comOne = $this->DataControl->selectOne("select company_id,company_code,company_shortname from gmc_company WHERE company_id = '{$request['company_id']}' ");
        }
        if($request['school_id']) {
            $schoolOne = $this->DataControl->selectOne("select school_id,school_branch,school_cnname from smc_school WHERE school_id = '{$request['school_id']}' ");
        }
        if($request['marketer_id']) {
            $marketerOne = $this->DataControl->selectOne("select marketer_id,staffer_id,marketer_name from crm_marketer WHERE marketer_id = '{$request['marketer_id']}' ");
        }
        if($request['shareposter_id']) {
            $actOne = $this->DataControl->selectOne("select activity_id,activity_name,activity_content,activity_name,activity_theme,activity_sharedesc,activity_shareimg,activity_starttime,activity_endtime from crm_sell_activity WHERE activity_id = '{$request['shareposter_id']}' ");

            if ($request['open_type'] != 'crm') {
                if (time() < (strtotime($actOne['activity_starttime']))) {
                    $res = array('error' => '1', 'errortip' => '活动尚未开始,敬请期待！', 'result' => array(), 'startend' => '1');
                    ajax_return($res, $request['language_type']);
                }
                if (time() > (strtotime($actOne['activity_endtime']) + 86400)) {
                    $res = array('error' => '1', 'errortip' => '活动已经结束！', 'result' => array(), 'startend' => '2');
                    ajax_return($res, $request['language_type']);
                }
            }
        }

        $data = array();
        $data['company_id'] = $comOne['company_id'];
        $data['company_shortname'] = $comOne['company_shortname'];

        $data['school_id'] = $schoolOne['school_id'];
        $data['school_branch'] = $schoolOne['school_branch'];
        $data['school_cnname'] = $schoolOne['school_cnname'];

        $data['marketer_id'] = $marketerOne['marketer_id'];
        $data['marketer_name'] = $marketerOne['marketer_name'];

        $data['activity_id'] = $actOne['activity_id'];
        $data['activity_name'] = $actOne['activity_name'];
        $data['activity_content'] = $actOne['activity_content']!=''?$actOne['activity_content']:0;
        $data['activity_name'] = $actOne['activity_name'];
        $data['activity_theme'] = $actOne['activity_theme'];
        $data['activity_sharedesc'] = $actOne['activity_sharedesc'];
        $data['activity_shareimg'] = $actOne['activity_shareimg'];

        $data['nowtime'] = date("Y-m-d",time());
        if($data){
            ajax_return(array('error' => 0,'errortip' => "数据获取成功",'result' => $data ));
        }else{
            ajax_return(array('error' => 1,'errortip' => "数据获取失败",'result' => array()));
        }
    }



}