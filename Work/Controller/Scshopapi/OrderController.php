<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2018/12/29
 * Time: 10:09
 */

namespace Work\Controller\Scshopapi;


class OrderController extends viewTpl
{
    public $u;
    public $t;
    public $c;

    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
    }

    //获取孩子可用商品优惠券
    function StuGoodsCouponsView()
    {
        $request = Input('get.', '', 'trim,addslashes,strip_tags');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Scshop\CouponsModel();
        $dataList = $Model->StuGoodsCoupons($request);
        $field = array();
        $field["coupons_id"] = "id";
        $field["coupons_name"] = "名称";
        $field["count"] = "数量";

        $result = array();
        if ($dataList) {
            $result["field"] = $field;
            $result["data"] = $dataList;
            ajax_return(array('error' => 0, 'errortip' => "获取孩子可用商品优惠券成功", 'result' => $result));
        } else {
            ajax_return(array('error' => 1, 'errortip' => "暂无孩子可用商品优惠券数据", 'result' => array()));
        }
    }

    //获取孩子可用订单优惠券
    function StuOrderCouponsView()
    {
        $request = Input('get.', '', 'trim,addslashes,strip_tags');
        $this->ThisVerify($request);//验证账户
//        $Model = new \Model\Scshop\OrderModel();
//        $dataList = $Model->StuOrderCoupons($request);

        $Model = new \Model\Smc\RegistrationModel($request);
        $dataList = $Model->getAvailableOrderTicketList($request);
        $field = array();
        $field["coupons_id"] = "id";
        $field["coupons_name"] = "名称";
        $field["count"] = "数量";

        $result = array();
        if ($dataList) {
            $result["field"] = $field;
            $result["data"] = $dataList;
            ajax_return(array('error' => 0, 'errortip' => "获取孩子可用订单优惠券成功", 'result' => $result));
        } else {
            ajax_return(array('error' => 1, 'errortip' => "暂无孩子可用订单优惠券数据", 'result' => array()));
        }
    }

    //订单确认商品列表
    function OrderGoodsListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Scshop\OrderModel();
        $dataList = $Model->OrderGoodsList($request);

        $field = array();
        $field["sellgoods_id"] = "商品id";
        $field["sellgoods_name"] = "名称";
        $field["price"] = "商品价格";
        $field["sellgoods_cartchange"] = "是否允许购物车加减 0不可以1可以";
        $field["sellgoods_listimg"] = "图片";

        $result = array();
        if ($dataList) {
            $result["field"] = $field;
            $result["data"] = $dataList;
            ajax_return(array('error' => 0, 'errortip' => "获取订单确认商品列表成功", 'result' => $result, 'carts_id' => stripslashes($request['carts_id'])));
        } else {
            ajax_return(array('error' => 1, 'errortip' => "暂无订单确认商品列表数据", 'result' => array()));
        }
    }

    //获取商品优惠券列表
    function GoodsCouponsListView()
    {
        $request = Input('get.', '', 'trim,addslashes,strip_tags');
        $this->ThisVerify($request);//验证账户
//        $Model = new \Model\Scshop\OrderModel();
//        $dataList = $Model->GoodsCouponsList($request);


        $Model = new \Model\Smc\RegistrationModel($request);
        $dataList = $Model->getAvailableTicketList($request);
        if($dataList){
            foreach($dataList as &$val){
                $rule = $this->DataControl->selectOne("SELECT
                                                            c.coupons_id,
                                                            co.applytype_play as couponsrules_play
                                                        FROM
                                                            smc_student_coupons AS c
                                                            LEFT JOIN smc_student_coupons_apply AS a ON a.apply_id = c.apply_id
                                                            LEFT JOIN smc_code_couponsapplytype AS co ON co.applytype_branch = a.applytype_branch
                                                            WHERE c.coupons_id = '{$val['coupons_id']}'");
//                left join shop_code_couponsrules as r on r.couponsrules_id = co.couponsrules_id
                $val['applytype_rule'] = $rule['couponsrules_play'];
            }
        }


        if ($dataList) {
            if ($request['coupons_isuse'] == 0) {
                $data = array();
                foreach ($dataList as $dataOne) {
                    if ($dataOne['can_use'] == 1) {
                        $data[] = $dataOne;
                    }
                }
                if ($data) {
                    $num = count($data);
                } else {
                    $num = 0;
                }

            } else {
                $num = count($dataList);
            }
        } else {
            $num = 0;
        }

        $field = array();
        $field["coupons_id"] = "优惠券id";
        $field["coupons_name"] = "优惠券名称";
        $field["coupons_type"] = "类型";
        $field["coupons_price"] = "减免金额";
        $field["coupons_discount"] = "减免比率";
        $field["coupons_exittime"] = "截止时间";

        $result = array();
        if ($dataList) {
            $result["field"] = $field;
            $result["data"] = $dataList;
            $result["allNum"] = $num;
            ajax_return(array('error' => 0, 'errortip' => "获取商品优惠券列表成功", 'result' => $result));
        } else {

            $result["allNum"] = 0;
            ajax_return(array('error' => 1, 'errortip' => "暂无商品优惠券列表数据", 'result' => $result));
        }
    }

    //获取订单优惠券列表
    function OrderCouponsListView()
    {
        $request = Input('get.', '', 'trim,addslashes,strip_tags');
        $this->ThisVerify($request);//验证账户
//        $Model = new \Model\Scshop\OrderModel();
//        $dataList = $Model->OrderCouponsList($request);

        $Model = new \Model\Smc\RegistrationModel($request);
        $dataList = $Model->getAvailableOrderTicketList($request);

        if($dataList){
            foreach($dataList as &$val){
                $rule = $this->DataControl->selectOne("SELECT 
                c.coupons_id,
                c.couponsrules_id,
                co.applytype_play as couponsrules_play
            FROM
                smc_student_coupons AS c
                LEFT JOIN smc_student_coupons_apply AS a ON a.apply_id = c.apply_id
                LEFT JOIN smc_code_couponsapplytype AS co ON co.applytype_branch = a.applytype_branch
                WHERE c.coupons_id = '{$val['coupons_id']}'");
//            left join shop_code_couponsrules as r on r.couponsrules_id = co.couponsrules_id

                if($rule['couponsrules_play'] != '') {
                    $val['applytype_rule'] = $rule['couponsrules_play'];
                }else{
                    $dataOne = $this->DataControl->selectOne(" select * from shop_code_couponsrules WHERE couponsrules_id = '{$rule['couponsrules_id']}' ");
                    $val['applytype_rule'] = $dataOne['couponsrules_play'];
                }
            }
        }


        $field = array();
        $field["coupons_id"] = "优惠券id";
        $field["coupons_name"] = "优惠券名称";
        $field["coupons_type"] = "类型";
        $field["coupons_price"] = "减免金额";
        $field["coupons_discount"] = "减免比率";
        $field["coupons_exittime"] = "截止时间";

        if ($dataList) {
            if ($request['coupons_isuse'] == 0) {
                $data = array();
                foreach ($dataList as $dataOne) {
                    if ($dataOne['can_use'] == 1) {
                        $data[] = $dataOne;
                    }
                }
                if ($data) {
                    $num = count($data);
                } else {
                    $num = 0;
                }

            } else {
                $num = count($dataList);
            }
        } else {
            $num = 0;
        }

        $result = array();
        if ($dataList) {
            $result["field"] = $field;
            $result["data"] = $dataList;
            $result["allNum"] = $num;
            ajax_return(array('error' => 0, 'errortip' => "获取订单优惠券列表成功", 'result' => $result));
        } else {
            $result["allNum"] = 0;
            ajax_return(array('error' => 1, 'errortip' => "暂无订单优惠券列表数据", 'result' => $result));
        }
    }

    //提交订单
    function SubmitOrderView()
    {
        $request = Input('post.', '', 'trim,addslashes,strip_tags');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Scshop\OrderModel();
        $res = $Model->SubmitOrder($request);
        ajax_return($res);
    }

    //根据订单编号获取支付信息
    function PayInfoView()
    {
        $request = Input('get.', '', 'trim,addslashes,strip_tags');
        $Model = new \Model\Scshop\OrderModel();
        $dataList = $Model->PayInfo($request);
        $field = array();
        $field["order_pid"] = "优惠券编号";
        $field["order_paymentprice"] = "订单价格";
        $field["student_cnname"] = "学员姓名";
        $field["student_branch"] = "学员编号";

        $result = array();
        if($dataList['list']['pay_issuccess'] == '0' && ($dataList['list']['order_status'] == '1' or $dataList['list']['order_status'] == '2' or $dataList['list']['order_status'] == '3') ){
            if ($dataList) {
                $result["field"] = $field;
                $result["data"] = $dataList;
                ajax_return(array('error' => 0, 'errortip' => "获取订单信息成功", 'result' => $result));
            } else {
                ajax_return(array('error' => 1, 'errortip' => "暂无订单信息错误", 'result' => array()));
            }
        }else {
            $result["field"] = $field;
            $result["data"] = $dataList;
            ajax_return(array('error' => 1, 'errortip' => "未支付订单信息已失效", 'result' => $result));
        }
    }

    //我的协议
    function MyProtocolView()
    {
        $request = Input('get.', '', 'trim,addslashes,strip_tags');
        if($request['student_id'] && $request['paypid'] == '' && $request['isapp'] !== '1'){
            $this->ThisVerify($request);//验证账户
        }

        $Model = new \Model\Scshop\OrderModel();
        $dataList = $Model->MyProtocol($request);
        $field = array();
        $field["protocol_price"] = "协议价格";
        $field["pay_pid"] = "支付编号";
        $field["protocol_createtime"] = "合同时间";

        $result = array();
        if ($dataList['list']) {
            $result["field"] = $field;
            $result["data"] = $dataList;
            ajax_return(array('error' => 0, 'errortip' => "获取我的协议成功", 'result' => $result));
        } else {
            ajax_return(array('error' => 1, 'errortip' => "未查询到您的课程协议，请确认您已购买！", 'result' => array()));
        }
    }

    //获取取消原因
    function cancelReasonListView()
    {
        $result = array();
        $result[0] = '我不想买了';
        $result[1] = '下单的商品信息有误';
        $result[2] = '孩子校区选错了';
        $result[3] = '等待的时间太长了';
        $result[4] = '其他';
        ajax_return(array('error' => 0, 'errortip' => "获取取消原因成功", 'result' => $result));

    }

    //取消订单
    function cancelOrderView()
    {
        $request = Input('post.', '', 'trim,addslashes,strip_tags');
        $this->ThisVerify($request);//验证账户

        $OrderModel = new \Model\Smc\OrderModel($request, $request['order_pid']);
        $res = $OrderModel->cancelOrder($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '取消订单成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $OrderModel->errortip, 'result' => $result);
        }
        ajax_return($res);
    }

    //判断订单是否支付成功了 -- 97
    function isPayOrderView()
    {
        $request = Input('get.', '', 'trim,addslashes,strip_tags');
        $this->ThisVerify($request);//验证账户
        $payOne = $this->DataControl->selectOne("select * from smc_payfee_order_pay WHERE pay_pid = '{$request['paypid']}' and pay_issuccess = '1' ");
        if ($payOne) {
            ajax_return(array('error' => 0, 'errortip' => "订单已支付", 'result' => $payOne));
        } else {
            ajax_return(array('error' => 1, 'errortip' => "订单未支付", 'result' => array()));
        }
    }

    //我的订单列表
    function myOrdersView()
    {
        $request = Input('get.', '', 'trim,addslashes,strip_tags');
        $this->ThisVerify($request);//验证账户

        $OrderModel = new \Model\Scshop\OrderModel();
        $result = $OrderModel->myOrders($request);
        if ($result['allnum'] > 0) {
            ajax_return(array('error' => 0, 'errortip' => "我的订单数据成功", 'result' => $result));
        } else {
            $result["data"] = array();
            $result["allnum"] = 0;
            ajax_return(array('error' => 1, 'errortip' => "未查询到订单信息", 'result' => $result));
        }
    }

    //我的订单信息
    function myOrdersListView()
    {
        $request = Input('get.', '', 'trim,addslashes,strip_tags');
        $this->ThisVerify($request);//验证账户

        $stuOne = $this->DataControl->selectOne("select * from smc_student WHERE student_id = '{$request['student_id']}' ");

        $data = array();
        $data['keyword'] = $stuOne['student_branch'];
        $data['school_id'] = $request['school_id'];
        $data['company_id'] = $request['company_id'];
        $data['p'] = $request['p'];
        $data['num'] = $request['num'];
        $data['is_count'] = 1;
        $OrderModel = new \Model\Smc\OrderModel();
        $res = $OrderModel->stuTradeList($data);
//print_r($res['list']);die;
        $dataList = array();
        if ($res['list']) {
            foreach ($res['list'] as $key => $resvar) {
                //返回的数组
                $dataList[$key]['trading_createtime'] = $resvar['trading_createtime'];
                $dataList[$key]['order_pid'] = $resvar['order_pid'];
                $dataList[$key]['order_allprice'] = $resvar['order_allprice'];
                $dataList[$key]['order_status'] = $resvar['order_status'];

                $orderOne = $this->DataControl->getFieldOne("smc_payfee_order", "company_id,school_id,order_pid", "order_pid='{$resvar['order_pid']}'");
                $stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id", "company_id='{$orderOne['company_id']}' AND account_class = '1'");

                $publicArray = array();
                $order_pid = $resvar['order_pid'];
                $publicArray['company_id'] = $orderOne['company_id'];
                $publicArray['school_id'] = $orderOne['school_id'];
                $publicArray['staffer_id'] = $stafferOne['staffer_id'];
//                $publicArray['token'] =  $request['token'];
                $OrderModel = new \Model\Smc\OrderModel($publicArray, $order_pid);
                $dataarray = $OrderModel->getOrderOneByTrading($resvar['trading_pid']);

                $tradingOne = $this->DataControl->getFieldOne("smc_student_trading", "tradingtype_code", "trading_pid='{$resvar['trading_pid']}'");
                $courseArray = array('PaynewFee', 'PayrenewFee', 'CourseMakeUp', 'CourseCatWash');
//print_r($res['list']);die;
                if ($dataarray['list']) {
                    $aadata = array();
                    if (in_array($tradingOne['tradingtype_code'], $courseArray)) {
                        if ($dataarray['list']['course']) {
                            foreach ($dataarray['list']['course'] as $val) {

                            }
                        }

                    } elseif ($tradingOne['tradingtype_code'] == 'PayitemFee') {
                        if ($dataarray['list']['items']) {//普通erp商品
                            foreach ($dataarray['list']['items'] as $val) {

                            }
                        }

                        if ($dataarray['list']['common_items']) {//课程杂费
                            foreach ($dataarray['list']['common_items'] as $val) {

                            }
                        }


                    }


                    $aadata = array();

//                        u.course_id as sellgoods_id
//                        u.course_cnname as goods_cnname
//                        u.course_branch as goods_tags
//                        u.course_branch as old_price
//                        u.course_branch as price
//                        u.course_img as goods_img
//                        u.course_img as buynums
//                        u.course_img as totalprice
//                        ,s.sellgoods_cartchange


                    $aadata['sellgoods_id'] = $dataarrayvar['company_id'];
                    $aadata['goods_cnname'] = $dataarrayvar['school_id'];
                    $aadata['goods_tags'] = $dataarrayvar['company_id'];
                    $aadata['old_price'] = $dataarrayvar['school_id'];
                    $aadata['price'] = $dataarrayvar['company_id'];
                    $aadata['goods_img'] = $dataarrayvar['school_id'];
                    $aadata['buynums'] = $dataarrayvar['school_id'];
                    $aadata['totalprice'] = $dataarrayvar['school_id'];
                    $aadata['sellgoods_cartchange'] = $dataarrayvar['school_id'];

                    $dataList['dataarray'] = $aadata;

                }
            }
        }


        if ($dataList) {
            $result["data"] = $dataList['list'];
            $result["allnum"] = $res['allnum'];
            ajax_return(array('error' => 0, 'errortip' => "我的订单数据成功", 'result' => $result));
        } else {
            $result["data"] = array();
            $result["allnum"] = 0;
            ajax_return(array('error' => 1, 'errortip' => "暂无我的订单数据", 'result' => $result));
        }
    }

    //申请退款挽留页面
    function refundToStayView()
    {
        $request = Input('get.', '', 'trim,addslashes,strip_tags');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Scshop\OrderModel();
        $dataList = $Model->refundToStay($request);
        $field = array();
        $field["sellgoods_name"] = "名字";
        $field["sellgoods_type"] = "类型";
        $field["sellgoods_listimg"] = "图片";
        $field["sellgoods_desc"] = "描述";
        $field["price"] = "价格";

        $result = array();
        if ($dataList) {
            $result["field"] = $field;
            $result["data"] = $dataList;
            ajax_return(array('error' => 0, 'errortip' => "获取申请退款挽留商品成功", 'result' => $result));
        } else {
            ajax_return(array('error' => 1, 'errortip' => "暂无申请退款挽留商品数据", 'result' => array()));
        }
    }

    //提交订单获取个人信息
    function personalInfoView()
    {
        $request = Input('get.', '', 'trim,addslashes,strip_tags');
        $this->ThisVerify($request);//验证账户
        $school = $this->DataControl->getFieldOne("smc_school", "school_address,school_cnname", "school_id = '{$request['school_id']}'");
        $student = $this->DataControl->selectOne("
            SELECT
                s.student_cnname,
                s.student_branch,
                co.course_cnname 
            FROM
                smc_student AS s
                LEFT JOIN smc_student_study AS ss ON s.student_id = ss.student_id and study_isreading = '1' 
                LEFT JOIN smc_class AS cl ON ss.class_id = cl.class_id
                LEFT JOIN smc_course AS co ON co.course_id = cl.course_id 
            WHERE
                s.student_id = '{$request['student_id']}'");

        $stublcOne = $this->DataControl->selectOne("select s.student_forwardprice,b.student_balance,b.student_withholdbalance from smc_student as s,smc_student_balance as b
WHERE b.student_id = s.student_id and b.school_id = '{$request['school_id']}' and s.student_id='{$request['student_id']}' and s.company_id='{$request['company_id']}'");

        $forword = $this->DataControl->selectOne("select student_forwardprice from smc_student where student_id = '{$request['student_id']}'");

        $result = array();
        $result['school_address'] = $school['school_address'];
        $result['school_cnname'] = $school['school_cnname'];
        $result['student_cnname'] = $student['student_cnname'];
        $result['student_branch'] = $student['student_branch'];
        $result['course_cnname'] = $student['course_cnname'];
        $result['balance'] = $stublcOne['student_balance'];
        $result['forward'] = $forword['student_forwardprice'];

        ajax_return(array('error' => 0, 'errortip' => "获取成功", 'result' => $result));

    }

    //订单详情
    function orderDetailView()
    {
        $request = Input('get.', '', 'trim,addslashes,strip_tags');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Scshop\OrderModel();
        $dataList = $Model->orderDetail($request);

        $result = array();
        if ($dataList) {
            $result["data"] = $dataList;
            ajax_return(array('error' => 0, 'errortip' => "获取订单详情成功", 'result' => $result));
        } else {
            ajax_return(array('error' => 1, 'errortip' => "暂无订单详情数据", 'result' => array()));
        }
    }

    //订单支付个人信息
    function payPersonalInfoView()
    {
        $request = Input('get.', '', 'trim,addslashes,strip_tags');
        $this->ThisVerify($request);//验证账户
        $school_id = $this->DataControl->getFieldOne("smc_payfee_order", "school_id", "order_pid = '{$request['order_pid']}'");

        $school = $this->DataControl->getFieldOne("smc_school", "school_address,school_cnname", "school_id = '{$school_id['school_id']}'");
        $student = $this->DataControl->selectOne("
            SELECT
                s.student_cnname,
                s.student_branch,
                co.course_cnname 
            FROM
                smc_student AS s
                LEFT JOIN smc_student_study AS ss ON s.student_id = ss.student_id and ss.study_isreading = '1' 
                LEFT JOIN smc_class AS cl ON ss.class_id = cl.class_id
                LEFT JOIN smc_course AS co ON co.course_id = cl.course_id 
            WHERE
                s.student_id = '{$request['student_id']}'");
        $phone = $this->DataControl->getFieldOne("smc_parenter", "parenter_mobile", "parenter_id = '{$request['parenter_id']}'");
        $result = array();
        $result['school_address'] = $school['school_address'];
        $result['school_cnname'] = $school['school_cnname'];
        $result['student_cnname'] = $student['student_cnname'];
        $result['student_branch'] = $student['student_branch'];
        $result['course_cnname'] = $student['course_cnname'];
        $result['phone'] = $phone['parenter_mobile'];

        ajax_return(array('error' => 0, 'errortip' => "获取成功", 'result' => $result));

    }

    //使用优惠券
    function useCouponsView()
    {
        $request = Input('post.', '', 'trim,addslashes,strip_tags');

        $this->ThisVerify($request);//验证账户
        $data = array();
        $data['order_pid'] = $request['order_pid'];
        $data['course_id'] = $request['course_id'];
        $data['goods_id'] = $request['goods_id'];
        $data['coupons_pid'] = $request['coupons_pid'];
        $data['ordercoupons_price'] = $request['ordercoupons_price'];
        $this->DataControl->insertData("smc_payfee_order_coupons", $data);

        $data = array();
        $data['coupons_isuse'] = '1';
        $data['coupons_usetime'] = time();

        if ($this->DataControl->updateData("smc_student_coupons", "coupons_pid = '{$request['coupons_pid']}'", $data)) {
            ajax_return(array('error' => 0, 'errortip' => "使用优惠券成功"));
        } else {
            ajax_return(array('error' => 1, 'errortip' => "使用优惠券失败"));

        }
    }

    //家长签名
    function parentSignView()
    {
        $request = Input('post.', '', 'trim,addslashes,strip_tags');
//        if($request['student_id']){
//            $this->ThisVerify($request);//验证账户
//        }
        $order_pid = $this->DataControl->getFieldOne("smc_student_protocol","order_pid","protocol_id = '{$request['protocol_id']}'");

        $data = array();
        $data['protocol_sign'] = $request['protocol_sign'];
        $data['protocol_issign'] = '1';
        $data['protocol_signip'] = real_ip();
        $data['protocol_signtime'] = date("Y-m-d H:i", time());

        if($request['type'] == '2'){
            if ($this->DataControl->updateData("smc_student_protocol","order_pid = '{$order_pid['order_pid']}' and protocol_issign = '0'",$data)) {
                $iscom = $this->DataControl->getFieldOne("smc_payfee_order","order_status","order_pid = '{$order_pid['order_pid']}'");
                if($iscom['order_status'] == '4'){
                    $data = array();
                    $data['protocol_isaudit'] = '1';
                    $this->DataControl->updateData("smc_student_protocol","order_pid = '{$order_pid['order_pid']}'",$data);
                }
                ajax_return(array('error' => 0, 'errortip' => "家长签名成功"));
            } else {
                ajax_return(array('error' => 1, 'errortip' => "家长签名失败"));
            }
        }else{
            if ($this->DataControl->updateData("smc_student_protocol","protocol_id = '{$request['protocol_id']}'",$data)) {
                $isset = $this->DataControl->getFieldOne("smc_student_protocol","protocol_id","order_pid = '{$order_pid['order_pid']}' and protocol_issign = '0'");
                if(!$isset){
                    $iscom = $this->DataControl->getFieldOne("smc_payfee_order","order_status","order_pid = '{$order_pid['order_pid']}'");
                    if($iscom['order_status'] == '4'){
                        $data = array();
                        $data['protocol_isaudit'] = '1';
                        $this->DataControl->updateData("smc_student_protocol","order_pid = '{$order_pid['order_pid']}'",$data);
                    }
                }
                ajax_return(array('error' => 0, 'errortip' => "家长签名成功"));
            } else {
                ajax_return(array('error' => 1, 'errortip' => "家长签名失败"));
            }
        }


    }

    //获取上次签名
    function getParentSignView()
    {
        $request = Input('get.', '', 'trim,addslashes,strip_tags');
        $this->ThisVerify($request);//验证账户

        $sign = $this->DataControl->getFieldOne("smc_student_protocol","protocol_sign","student_id = '{$request['student_id']}' and protocol_issign = '1' order by protocol_signtime DESC");

        if ($sign['protocol_sign']) {
            ajax_return(array('error' => 0, 'errortip' => "获取成功", 'sign' => $sign['protocol_sign']));
        } else {
            ajax_return(array('error' => 1, 'errortip' => "暂无签名"));

        }
    }

    //取消使用优惠券
    function unuseCouponsView()
    {
        $request = Input('post.', '', 'trim,addslashes,strip_tags');
        $this->ThisVerify($request);//验证账户

        $this->DataControl->delData("smc_payfee_order_coupons", "coupons_pid = '{$request['coupons_pid']}'");

        $data = array();
        $data['coupons_isuse'] = '0';
        $data['coupons_usetime'] = '0';

        if ($this->DataControl->updateData("smc_student_coupons", "coupons_pid = '{$request['coupons_pid']}'", $data)) {
            ajax_return(array('error' => 0, 'errortip' => "取消使用优惠券成功"));
        } else {
            ajax_return(array('error' => 1, 'errortip' => "取消使用优惠券失败"));

        }
    }

    //提交订单
    function createOrderView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        //创建订单 获取返回的订单pid
        $Model = new \Model\Smc\RegistrationModel($request);
        $res = $Model->createOrder($request);

        if (!$res) {
            ajax_return(array('error' => 1, 'errortip' => $Model->errortip));
        }

        $order = $this->DataControl->getFieldOne("smc_payfee_order_course", "ordercourse_unitprice,ordercourse_totalprice", "order_pid = '{$res}'");
        $type = $this->DataControl->getFieldOne("smc_payfee_order", "order_type,school_id", "order_pid = '{$res}'");
        $data = array();
        $data['order_from'] = '0';
        $this->DataControl->updateData("smc_payfee_order", "order_pid = '{$res}'", $data);
        do {
            $paypid = $this->createOrderPid('ZF');
        } while ($this->DataControl->selectOne("select pay_id from smc_payfee_order_pay where pay_pid='{$paypid}' limit 0,1"));

        if($request['price'] > '0'){
            $orderOne = $this->DataControl->getFieldOne("smc_payfee_order","companies_id","order_pid='{$res}'");
            $data = array();
            $data['order_pid'] = $res;
            $data['pay_pid'] = $paypid;
            $data['companies_id'] = $orderOne['companies_id'];
            $data['pay_price'] = $request['price'];
            $data['pay_type'] = '0';
            $data['pay_createtime'] = time();
            $this->DataControl->insertData("smc_payfee_order_pay", $data);
        }

        $schoolList = json_decode(stripslashes($request['cart_id']), true);
        foreach ($schoolList as $item) {
            $this->DataControl->delData('shop_cart', "cart_id = '{$item['cart_id']}'");
        }

        ajax_return(array('error' => 0, 'errortip' => "提交订单成功", 'order_pid' => $res, 'order_type' => $type['order_type']));


    }


    function createRechargeOrderAction(){
        $request = Input('request.', '', 'trim,addslashes,strip_tags');
        $Model = new \Model\Scshop\OrderModel();
        $res = $Model->createRechargeOrder($request);
        $result = array();
        if($res){
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '生成成功', 'result' => $result);
        }else{
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res,$request['language_type']);

    }

    function buyGoodsAction()
    {
        $request = Input('post.', '', 'trim,addslashes,strip_tags');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Smc\GoodsFeeModel();
        $res = $Model->buyGoods($request);
        $type = $this->DataControl->getFieldOne("smc_payfee_order", "order_type", "order_pid = '{$res}'");

        $data = array();
        $data['order_from'] = '0';
        $this->DataControl->updateData("smc_payfee_order", "order_pid = '{$res}'", $data);

        $schoolList = json_decode(stripslashes($request['cart_id']), true);
        foreach ($schoolList as $item) {
            $this->DataControl->delData('shop_cart', "cart_id = '{$item['cart_id']}'");
        }


        ajax_return(array('error' => 0, 'errortip' => "提交订单成功", 'order_pid' => $res, 'order_type' => $type['order_type']));

    }

    function getOrderPriceApi()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\RegistrationModel($request);
        $res = $Model->getOrderPrice($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res);

    }

    function getOrderTrackView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Scshop\OrderModel();
        $res = $Model->getOrderTrack($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res);

    }

}
