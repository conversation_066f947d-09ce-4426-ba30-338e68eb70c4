<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2018/12/29
 * Time: 10:09
 */

namespace Work\Controller\Scshopapi;


class InvoiceController extends viewTpl
{
    public $u;
    public $t;
    public $c;

    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
    }

    //选择开票课程列表
    function ChoiceInvoiceListView()
    {
        $request = Input('get.', '', 'trim,addslashes,strip_tags');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Scshop\InvoiceModel();
        $dataList = $Model->ChoiceInvoiceList($request);
        $field = array();
        $field["course_id"] = "课程id";
        $field["course_cnname"] = "课程名称";
        $field["sellgoods_name"] = "商品名称";
        $field["protocol_id"] = "协议id";
        $field["sellgoods_listimg"] = "图片";
        $field["protocol_price"] = "价格";

        $result = array();
        if ($dataList) {
            $result["field"] = $field;
            $result["data"] = $dataList;
            ajax_return(array('error' => 0, 'errortip' => "获取选择开票课程列表成功", 'result' => $result), $request['language_type']);
        } else {
            ajax_return(array('error' => 1, 'errortip' => "暂无选择开票课程列表数据", 'result' => array()), $request['language_type']);
        }
    }

    //开票课程详情
    function InvoiceDetailList()
    {
        $request = Input('get.', '', 'trim,addslashes,strip_tags');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Scshop\InvoiceModel();
        $dataList = $Model->InvoiceDetailList($request);
        $field = array();
        $field["course_id"] = "课程id";
        $field["course_cnname"] = "课程名称";
        $field["protocol_id"] = "协议id";
        $field["sellgoods_detailimg"] = "图片";
        $field["protocol_price"] = "价格";

        $result = array();
        if ($dataList) {
            $result["field"] = $field;
            $result["data"] = $dataList;
            ajax_return(array('error' => 0, 'errortip' => "获取开票课程详情成功", 'result' => $result), $request['language_type']);
        } else {
            ajax_return(array('error' => 1, 'errortip' => "暂无开票课程详情数据", 'result' => array()), $request['language_type']);
        }
    }

    //申请发票(新)
    function ApplyInvoiceNewView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户


        $sid = $this->DataControl->getFieldOne("smc_student", "student_id", "student_branch = '{$request['student_branch']}'");
        $request['student_id'] = $sid['student_id'];

        $orderOne = $this->DataControl->getFieldOne("smc_payfee_order", "school_id,companies_id,order_paymentprice", "order_pid='{$request['order_pid']}'");
//        if ($orderOne['order_paytime'] < strtotime("-0 year -1 month -0 day")) {
//            ajax_return(array('error' => 1, 'errortip' => "该订单已超出开票期限，请洽学校教师", "tokeninc" => 1));
//        }
        $protocolOne = $this->DataControl->getFieldOne("smc_student_protocol", "protocol_pid,protocol_price", "protocol_id='{$request['protocol_id']}'");
        if (!$protocolOne) {
            ajax_return(array('error' => 1, 'errortip' => "无对应合同", "tokeninc" => 1), $request['language_type']);
        }

        //$companies = $this->DataControl->getFieldOne("smc_school", "companies_id", "school_id = '{$orderOne['school_id']}'");

        $invoiceOne = $this->DataControl->getFieldOne("shop_invoice", 'invoice_id,invoice_status,student_id'
            , "invoice_status > '-1' and invoice_cancel = '0' and protocol_id = '{$request['protocol_id']}'");
        if ($invoiceOne) {
            ajax_return(array('error' => 1, 'errortip' => "您已申请电子发票成功，请勿重复申请", "tokeninc" => 1), $request['language_type']);
        } else {
            $protocolAll = $this->DataControl->selectClear("select * from smc_student_protocol where order_pid='{$request['order_pid']}' and protocol_isdel = '0'");
            $count = count($protocolAll);
            if ($protocolAll) {
                foreach ($protocolAll as &$value) {
                    $isset = $this->DataControl->selectOne("select p.pay_id from smc_payfee_order_pay as p where p.pay_issuccess = '1' and paytype_code not in ('dcep','qrcode','bankcard','cash','pos','alipay','wechat','yinlian') and order_pid = '{$request['order_pid']}'");
                    if (!$isset) {
                        $data = array();
                        $data['parenter_id'] = $request['parenter_id'];
                        $data['protocol_id'] = $value['protocol_id'];
                        $data['student_id'] = $request['student_id'];
                        $data['order_pid'] = $request['order_pid'];
                        $data['school_id'] = $orderOne['school_id'];
                        $data['invoice_allprice'] = $value['protocol_price'];
                        $data['companies_id'] = $orderOne['companies_id'];
                        $data['company_id'] = $request['company_id'];
                        $data['invoice_type'] = '2';
                        $data['invoice_status'] = '0';
                        $data['invoice_title'] = $request['title'];
                        if (isset($request['taxpayernum']) && $request['taxpayernum'] !== '') {
                            $data['invoice_taxpayernum'] = $request['taxpayernum'];
                        }
                        $data['invoice_email'] = trim($request['email']);
                        $data['invoice_createtime'] = time();
                        $invoice_id = $this->DataControl->insertData("shop_invoice", $data);
                        $datas = array();
                        $datas['protocol_isinvoice'] = 1;
                        $this->DataControl->updateData("smc_student_protocol", "protocol_id = '{$value['protocol_id']}'", $datas);

                        $datatrack = array();
                        $datatrack['invoice_id'] = $invoice_id;
                        $datatrack['tracks_title'] = '提交申请';
                        $datatrack['tracks_information'] = '家长微商城申请发票';
                        $datatrack['parenter_id'] = $request['parenter_id'];
                        $name = $this->DataControl->getFieldOne("smc_parenter", "parenter_cnname", "parenter_id = '{$request['parenter_id']}'");
                        $datatrack['tracks_playname'] = $name['parenter_cnname'];
                        $datatrack['tracks_time'] = time();
                        $this->DataControl->insertData("shop_invoice_tracks", $datatrack);

                        $orderTracksData = array();
                        $orderTracksData['order_pid'] = $request['order_pid'];
                        $orderTracksData['tracks_title'] = '申请发票';
                        $orderTracksData['tracks_information'] = '合同编号为' . $value['protocol_pid'] . '的发票申请已提交，请耐心等待审核哦～';
                        $orderTracksData['staffer_id'] = '0';
                        $orderTracksData['tracks_playname'] = '';
                        $orderTracksData['tracks_time'] = time();
                        $this->DataControl->insertData("smc_payfee_order_tracks", $orderTracksData);
                    } else {
                        $allprice = $this->DataControl->selectOne("select sum(p.pay_price) as price from smc_payfee_order_pay as p where p.pay_issuccess = '1' and paytype_code in ('dcep','qrcode','bankcard','cash','pos','alipay','wechat','yinlian') and order_pid = '{$request['order_pid']}'");
                        $allinvoiceprice = $this->DataControl->selectOne("select sum(p.invoice_allprice) as price from shop_invoice as p where invoice_cancel = '0' and order_pid = '{$request['order_pid']}' and invoice_status >= '0'");
                        $restprice = $allprice['price'] - $allinvoiceprice['price'];
                        if($restprice >= $value['protocol_price']){
                            $data = array();
                            $data['parenter_id'] = $request['parenter_id'];
                            $data['protocol_id'] = $value['protocol_id'];
                            $data['student_id'] = $request['student_id'];
                            $data['order_pid'] = $request['order_pid'];
                            $data['school_id'] = $orderOne['school_id'];
                            $data['invoice_allprice'] = $value['protocol_price'];
                            $data['companies_id'] = $orderOne['companies_id'];
                            $data['company_id'] = $request['company_id'];
                            $data['invoice_type'] = '2';
                            $data['invoice_status'] = '0';
                            $data['invoice_title'] = $request['title'];
                            if (isset($request['taxpayernum']) && $request['taxpayernum'] !== '') {
                                $data['invoice_taxpayernum'] = $request['taxpayernum'];
                            }
                            $data['invoice_email'] = trim($request['email']);
                            $data['invoice_createtime'] = time();
                            $invoice_id = $this->DataControl->insertData("shop_invoice", $data);
                            $datas = array();
                            $datas['protocol_isinvoice'] = 1;
                            $this->DataControl->updateData("smc_student_protocol", "protocol_id = '{$value['protocol_id']}'", $datas);

                            $datatrack = array();
                            $datatrack['invoice_id'] = $invoice_id;
                            $datatrack['tracks_title'] = '提交申请';
                            $datatrack['tracks_information'] = '家长微商城申请发票';
                            $datatrack['parenter_id'] = $request['parenter_id'];
                            $name = $this->DataControl->getFieldOne("smc_parenter", "parenter_cnname", "parenter_id = '{$request['parenter_id']}'");
                            $datatrack['tracks_playname'] = $name['parenter_cnname'];
                            $datatrack['tracks_time'] = time();
                            $this->DataControl->insertData("shop_invoice_tracks", $datatrack);

                            $orderTracksData = array();
                            $orderTracksData['order_pid'] = $request['order_pid'];
                            $orderTracksData['tracks_title'] = '申请发票';
                            $orderTracksData['tracks_information'] = '合同编号为' . $value['protocol_pid'] . '的发票申请已提交，请耐心等待审核哦～';
                            $orderTracksData['staffer_id'] = '0';
                            $orderTracksData['tracks_playname'] = '';
                            $orderTracksData['tracks_time'] = time();
                            $this->DataControl->insertData("smc_payfee_order_tracks", $orderTracksData);
                        }else{
                            $data = array();
                            $data['parenter_id'] = $request['parenter_id'];
                            $data['protocol_id'] = $value['protocol_id'];
                            $data['student_id'] = $request['student_id'];
                            $data['order_pid'] = $request['order_pid'];
                            $data['school_id'] = $orderOne['school_id'];
                            $data['invoice_allprice'] = $restprice;
                            $data['companies_id'] = $orderOne['companies_id'];
                            $data['company_id'] = $request['company_id'];
                            $data['invoice_type'] = '2';
                            $data['invoice_status'] = '0';
                            $data['invoice_title'] = $request['title'];
                            if (isset($request['taxpayernum']) && $request['taxpayernum'] !== '') {
                                $data['invoice_taxpayernum'] = $request['taxpayernum'];
                            }
                            $data['invoice_email'] = trim($request['email']);
                            $data['invoice_createtime'] = time();
                            $invoice_id = $this->DataControl->insertData("shop_invoice", $data);
                            $datas = array();
                            $datas['protocol_isinvoice'] = 1;
                            $this->DataControl->updateData("smc_student_protocol", "order_pid = '{$request['order_pid']}'", $datas);

                            $datatrack = array();
                            $datatrack['invoice_id'] = $invoice_id;
                            $datatrack['tracks_title'] = '提交申请';
                            $datatrack['tracks_information'] = '家长微商城申请发票';
                            $datatrack['parenter_id'] = $request['parenter_id'];
                            $name = $this->DataControl->getFieldOne("smc_parenter", "parenter_cnname", "parenter_id = '{$request['parenter_id']}'");
                            $datatrack['tracks_playname'] = $name['parenter_cnname'];
                            $datatrack['tracks_time'] = time();
                            $this->DataControl->insertData("shop_invoice_tracks", $datatrack);

                            $orderTracksData = array();
                            $orderTracksData['order_pid'] = $request['order_pid'];
                            $orderTracksData['tracks_title'] = '申请发票';
                            $orderTracksData['tracks_information'] = '合同编号为' . $value['protocol_pid'] . '的发票申请已提交，请耐心等待审核哦～';
                            $orderTracksData['staffer_id'] = '0';
                            $orderTracksData['tracks_playname'] = '';
                            $orderTracksData['tracks_time'] = time();
                            $this->DataControl->insertData("smc_payfee_order_tracks", $orderTracksData);
                            break;
                        }
                    }

                }
            }

            ajax_return(array('error' => 0, 'errortip' => "该订单共".$count."个合同开票成功！", "tokeninc" => 1), $request['language_type']);

//            $allprice = $this->DataControl->selectOne("select sum(p.pay_price) as price from smc_payfee_order_pay as p where p.pay_issuccess = '1' and paytype_code in ('dcep','qrcode','bankcard','cash','pos','alipay','wechat','yinlian') and order_pid = '{$request['order_pid']}'");

        }
    }

    //我的发票-已开票
    function AlreadyInvoiceListView()
    {
        $request = Input('get.', '', 'trim,addslashes,strip_tags');
        if ($request['isapp'] !== '1') {
            $this->ThisVerify($request);//验证账户
        }
        $Model = new \Model\Scshop\InvoiceModel();
        $dataList = $Model->AlreadyInvoiceList($request);
        $field = array();
        $field["invoice_id"] = "发票id";
        $field["invoice_title"] = "申请人";
        $field["invoice_allprice"] = "发票金额";
        $field["order_pid"] = "订单编号";
        $field["pay_pid"] = "支付编号";
        $field["invoice_code"] = "发票编号";
        $field["invoice_status"] = "0审核中1已开票";

        $result = array();
        if ($dataList['list']) {
            $result["field"] = $field;
            $result["data"] = $dataList;
            ajax_return(array('error' => 0, 'errortip' => "获取已开票列表成功", 'result' => $result), $request['language_type']);
        } else {
            ajax_return(array('error' => 1, 'errortip' => "未检测到您的开票记录，请确认您已购买课程！", 'result' => array()), $request['language_type']);
        }
    }

    //我的发票-未开票？？？？
    function UnInvoiceListView()
    {
        $request = Input('get.', '', 'trim,addslashes,strip_tags');
//        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Scshop\InvoiceModel();
        $dataList = $Model->UnInvoiceList($request);
        $field = array();
        $field["course_id"] = "课程id";
        $field["course_cnname"] = "课程名称";
        $field["protocol_id"] = "协议id";
        $field["sellgoods_detailimg"] = "图片";
        $field["protocol_price"] = "价格";

        $result = array();
        if ($dataList['list']) {
            $result["field"] = $field;
            $result["data"] = $dataList;
            ajax_return(array('error' => 0, 'errortip' => "获取未开票列表成功", 'result' => $result), $request['language_type']);
        } else {
            ajax_return(array('error' => 1, 'errortip' => "暂无未开票数据", 'result' => array()), $request['language_type']);
        }
    }

    //我的发票-查看发票
    function InvoiceDetailView()
    {
        $request = Input('get.', '', 'trim,addslashes,strip_tags');
        if ($request['isapp'] !== '1') {
            $this->ThisVerify($request);//验证账户
        }
        $Model = new \Model\Scshop\InvoiceModel();
        $dataList = $Model->InvoiceDetail($request);
        $field = array();
        $field["invoice_status"] = "发票状态0-审核中 1-已生成电子发票";
        $field["order_status"] = "订单状态：0待审核1待支付2支付中3处理中4已完成-1已取消 -2已拒绝";
        $field["order_pid"] = "订单编号";
        $field["order_createtime"] = "下单时间";
        $field["invoice_type"] = "发票类型 1-普通发票 2-电子发票";
        $field["invoice_content"] = "内容";
        $field["invoice_title"] = "抬头";
        $field["invoice_wxurl"] = "查看发票地址";

        $result = array();
        if ($dataList) {
            $result["field"] = $field;
            $result["data"] = $dataList;
            ajax_return(array('error' => 0, 'errortip' => "查看发票成功", 'result' => $result), $request['language_type']);
        } else {
            ajax_return(array('error' => 1, 'errortip' => "查看发票失败", 'result' => array()), $request['language_type']);
        }

    }


}
