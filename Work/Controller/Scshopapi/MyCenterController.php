<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/4/17
 * Time: 10:51
 */

namespace Work\Controller\Scshopapi;


class MyCenterController extends viewTpl{
    public $u;
    public $t;
    public $c;
    public $visitType="api";
    public $DataControl;

    //预加载处理类
    function __construct() {
        parent::__construct ();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
        $this->api = $this->router->getApi();

        //数据库操作
        $this->DataControl = new \Dbmysql();
    }



    //获取学员信息
    function getStudentInfoView(){
        $request = Input('get.','','trim,addslashes');
        $schoolOne = $this->DataControl->selectOne("select * from smc_school where school_id = '{$request['school_id']}' limit 0,1");
        $studentOne = $this->DataControl->selectOne("select * from smc_student where student_id = '{$request['student_id']}' limit 0,1");
        $parenterOne = $this->DataControl->selectOne("select * from smc_parenter where parenter_id = '{$request['parenter_id']}' limit 0,1");
        $context = $this->DataControl->getFieldOne("crm_sell_activity","activity_content","activity_id = '{$request['shareposter_id']}'");

        $day = date("Y.m.d");
        $result = array();
        $result['school_cnname'] = $schoolOne['school_shortname'];
        $result['school_id'] = $schoolOne['school_id'];
        $result['school_branch'] = $schoolOne['school_branch'];

        $result['student_id'] = $studentOne['student_id'];
        $result['student_branch'] = $studentOne['student_branch'];
        $result['student_cnname'] = $studentOne['student_cnname'];
        $result['student_img'] = $studentOne['student_img'];
        $result['parenter_id'] = $studentOne['parenter_id'];
        $result['parenter_cnname'] = $studentOne['parenter_cnname'];
        $result['parenter_enname'] = $studentOne['parenter_enname'];
        $result['parenter_mobile'] = $studentOne['parenter_mobile'];
        $result['shareposter_note'] =$context['activity_content'];
        $result['day'] =$day;
//        ajax_return(array('error' => 0, 'errortip' => "获取成功", 'result' => $result));

        if($result){
            $res = array('error' => '0', 'errortip' => '获取成功', 'result' => $result);
        }else{
            $res = array('error' => '1', 'errortip' => '获取失败', 'result' => array());
        }
        ajax_return($res);
    }


    //根据学员编号获取孩子信息
    function HomeView()
    {
        $request = Input('get.','','trim,addslashes,strip_tags');
        $this->ThisVerify($request);//验证账户
//        $sql="select s.student_id,s.student_cnname,s.student_enname,s.student_img,s.student_sex
//              ,(SELECT h.school_cnname FROM smc_school as h WHERE h.school_id = '{$request['school_id']}') as school_cnname
//              ,(SELECT c.company_shortname FROM gmc_company as c WHERE c.company_id = '{$request['company_id']}') as company_name
//              from smc_student as s
//              WHERE s.student_id = '{$request['student_id']}'
//              limit 0,1 ";
//
//        $myinfo = $this->DataControl->selectOne($sql);
//        if($myinfo){
//            $classOne = $this->DataControl->selectOne("select c.class_cnname from smc_student_study as s
//                        LEFT JOIN smc_class as c ON s.class_id = c.class_id
//                        WHERE s.company_id = '{$request['company_id']}' and s.school_id = '{$request['school_id']}' and s.student_id = '{$request['student_id']}'
//                        limit 0,1  ");
//            $integral = $this->DataControl->getFieldOne("smc_student_virtual_property","property_integralbalance","student_id = '{$request['student_id']}'");
//        }
//
//        $myinfo['class_cnname'] = $classOne['class_cnname'];
//
//
//        if($classOne['class_cnname']) {
//            $myinfo['formatype'] = '正式学员';
//        }else{
//            $myinfo['formatype'] = '待入班学员';
//        }
//        if($integral['property_integralbalance']){
//            $myinfo['property_integralbalance'] = $integral['property_integralbalance'];
//        }else{
//            $myinfo['property_integralbalance'] = '0';
//        }

        $sql="select s.student_id,s.student_cnname,s.student_enname,s.student_img,s.student_sex,ifnull(vp.property_integralbalance,0) as property_integralbalance
              ,sc.school_cnname,sc.school_branch,c.company_shortname as company_name,sc.school_address
              ,(select ss.study_id from smc_student_study as ss where ss.student_id=se.student_id limit 0,1) as study_id
              from smc_student_enrolled as se
              inner join smc_student as s on s.student_id=se.student_id
              inner join smc_school as sc on sc.school_id=se.school_id
              inner join gmc_company as c on c.company_id=sc.company_id
              left join smc_student_virtual_property as vp on vp.student_id=s.student_id
              where sc.company_id='{$request['company_id']}' and se.school_id='{$request['school_id']}' and se.student_id='{$request['student_id']}'
              ";
        $myinfo=$this->DataControl->selectOne($sql);

        $count_sql = "SELECT
                i.integralgoods_id
            FROM
                smc_student_integralgoods AS i
                LEFT JOIN smc_school as s on s.school_id = i.school_id
                LEFT JOIN erp_goods as g on g.goods_id = i.goods_id
            WHERE
                i.student_id = '{$request['student_id']}'";

        $db_nums = $this->DataControl->selectClear($count_sql);

        $myinfo['exnum'] = $db_nums ? count($db_nums) : 0;

        $count_sql = "select
         cp.coupons_id,
          cp.coupons_name,
          cp.coupons_activity_name,
          cp.coupons_price,
          cp.coupons_starttime,
          cp.coupons_endtime,
          cp.coupons_use_condition,
          cp.coupons_endtime
         from smc_integral_cooperation_coupons as cp where cp.student_id='{$request['student_id']}' and cp.coupons_isuse <>'-1'";

        $db_nums = $this->DataControl->selectClear($count_sql);

        $myinfo['cardnum'] = $db_nums ? count($db_nums) : 0;

        if ($myinfo['property_integralbalance']) {
            $myinfo['property_integralbalance'] = $myinfo['property_integralbalance'];
        } else {
            $myinfo['property_integralbalance'] = '0';
        }

        if(!$myinfo){
            ajax_return(array('error' => 1,'errortip' => "暂无学员数据",'result' => array()));
        }

        $myinfo['formatype']=$myinfo['study_id']?'正式学员':'待入班学员';

        //商品收藏数
        $collectionnum = $this->DataControl->selectOne("select count(sellgoods_id) as num from shop_parenter_collection WHERE company_id = '{$request['company_id']}' and parenter_id = '{$request['parenter_id']}' and student_id = '{$request['student_id']}' ");
        //浏览记录数
        $browsenum = $this->DataControl->selectOne("select count(sellgoods_id) as num from shop_parenter_browse WHERE company_id = '{$request['company_id']}' and parenter_id = '{$request['parenter_id']}' and student_id = '{$request['student_id']}' ");
        //红包卡券数
        $couponsnum = $this->DataControl->selectOne("select count(coupons_id) as num from smc_student_coupons WHERE company_id = '{$request['company_id']}' and student_id = '{$request['student_id']}' and coupons_isuse = '0' ");

        //待付款数
        $ordernum = $this->DataControl->selectOne("select count(order_id) as num from smc_payfee_order WHERE company_id = '{$request['company_id']}' and student_id = '{$request['student_id']}' and (order_status = '0' or order_status = '1' or order_status = '2' or order_status = '3') ");//and order_type <> '2'
        //已付款数
        $orderpaynum = $this->DataControl->selectOne("select count(order_id) as num from smc_payfee_order WHERE company_id = '{$request['company_id']}' and student_id = '{$request['student_id']}' and order_status = '4'  ");//and order_type <> '2'
        //已取消数
        $ordercancelnum = $this->DataControl->selectOne("select count(order_id) as num from smc_payfee_order WHERE company_id = '{$request['company_id']}' and student_id = '{$request['student_id']}' and order_status = '-1' ");//and order_type <> '2'

        $field = array();
        $field["myinfo"] = "个人信息";
        $field["collectionnum"] = "商品收藏数";
        $field["browsenum"] = "浏览记录数";
        $field["couponsnum"] = "红包卡券数";
        $field["ordernum"] = "待付款数";
        $field["orderpaynum"] = "已付款数";
        $field["ordercancelnum"] = "已取消数";

        $dataList = array();
        $dataList["myinfo"] = $myinfo;
        $dataList["collectionnum"] = $collectionnum['num'];
        $dataList["browsenum"] = $browsenum['num'];
        $dataList["couponsnum"] = $couponsnum['num'];
        $dataList["ordernum"] = $ordernum['num'];
        $dataList["orderpaynum"] = $orderpaynum['num'];
        $dataList["ordercancelnum"] = $ordercancelnum['num'];

        $result = array();
        if($dataList){
            $result["field"] = $field;
            $result["data"] = $dataList;
            ajax_return(array('error' => 0,'errortip' => "获取学员数据成功",'result' => $result));
        }else{
            ajax_return(array('error' => 1,'errortip' => "暂无学员数据",'result' => array()));
        }
    }

    //获取我的商品收藏
    function getMyCollectionView(){
        $request = Input('get.', '', 'trim,addslashes,strip_tags');
        $this->ThisVerify($request);//验证账户

        $classlist = $this->DataControl->selectClear("select class_id,class_name from shop_code_class WHERE company_id = '{$request['company_id']}' ");
        if($classlist){
            foreach ($classlist as &$classvar){
                $allnum = $this->DataControl->selectOne("select count(c.sellgoods_id) as num from shop_parenter_collection as c 
                          LEFT JOIN shop_sellgoods as g ON g.sellgoods_id = c.sellgoods_id 
                          WHERE g.class_id = '{$classvar['class_id']}'and c.parenter_id = '{$request['parenter_id']}' and c.student_id = '{$request['student_id']}'");
                if($allnum['num']) {
                    $classvar['num'] = $allnum['num'];
                }else{
                    $classvar['num'] = 0;
                }
            }
        }

        $datawhere = ' ';
        if(isset($request['keyword']) && $request['keyword'] != ''){
            $datawhere .= " and  m.sellgoods_name like '%{$request['keyword']}%' ";
        }
        if(isset($request['class_id']) && $request['class_id'] != ''){
            $datawhere .= " and  m.class_id = '{$request['class_id']}' ";
        }

        if(isset($request['p']) && $request['p'] !== ''){
            $page = $request['p'];
        }else{
            $page = '1';
        }
        if(isset($request['num']) && $request['num'] !== ''){
            $num = $request['num'];
        }else{
            $num = '10';
        }
        $pagestart = ($page-1)*$num;

        $sql = "SELECT c.*,m.sellgoods_name,m.sellgoods_listimg,FROM_UNIXTIME(c.collection_createtime, '%Y-%m-%d') as createtime,m.sellgoods_cartchange,m.sellgoods_type,m.tuition_id,m.coursepacks_id,m.goods_id,m.course_id 
                FROM shop_parenter_collection AS c 
                LEFT JOIN shop_sellgoods as m ON c.sellgoods_id = m.sellgoods_id  
                WHERE c.company_id = '{$request['company_id']}' and c.parenter_id = '{$request['parenter_id']}' and c.student_id = '{$request['student_id']}' {$datawhere}
                limit {$pagestart},{$num} ";
        $res = $this->DataControl->selectClear($sql);

        if($res){
            $alllist = array();
            foreach ($res as &$resVar){
                if ($resVar['sellgoods_type'] == '0') {
                    $a = $this->DataControl->getFieldOne("smc_fee_pricing_tuition", "tuition_originalprice,tuition_sellingprice,tuition_buypiece", "tuition_id = '{$resVar['tuition_id']}'");
                    $resVar['price'] = $a['tuition_sellingprice'];
                    $resVar['old_price'] = $a['tuition_originalprice'];
                } elseif ($resVar['sellgoods_type'] == '1') {
                    $b = $this->DataControl->selectOne("select sum(tuition_sellingprice) as price from smc_fee_warehouse_courses where coursepacks_id = '{$resVar['coursepacks_id']}'");
                    $resVar['price'] = $b['price'];
                } elseif ($resVar['sellgoods_type'] == '2') {
                    $c = $this->DataControl->selectOne("select goods_originalprice,goods_vipprice from erp_goods where goods_id = '{$resVar['goods_id']}'");
                    $resVar['price'] = $c['goods_vipprice'];
                    $resVar['old_price'] = $c['goods_originalprice'];
                } elseif ($resVar['sellgoods_type'] == '3') {
                    $resVar['price'] = $resVar['feeitem_price'];
                }

//                $resVar["old_price"] = '1000.00';
//                $resVar["price"] = '888.00';
            }
        }

        $allnum = $this->DataControl->selectOne(" SELECT count(c.collection_id) as num 
                 FROM shop_parenter_collection AS c 
                LEFT JOIN shop_sellgoods as m ON c.sellgoods_id = m.sellgoods_id  
                WHERE c.company_id = '{$request['company_id']}' and c.parenter_id = '{$request['parenter_id']}' and c.student_id = '{$request['student_id']}' {$datawhere}");

        $field = array();
        $field["company_id"] = "企业ID";
        $field["parenter_id"] = "家长ID";
        $field["student_id"] = "学员ID";
        $field["sellgoods_id"] = "商品ID";
        $field["collection_createtime"] = "收藏时间戳";
        $field["createtime"] = "收藏日期";
        $field["sellgoods_name"] = "商品名称";
        $field["sellgoods_listimg"] = "商品图片";
        $field["sellgoods_originalprice"] = "商品原价";
        $field["sellgoods_vipprice"] = "商品优惠价";
        $field["sellgoods_cartchange"] = "是否允许购物车加减 0不可以1可以";

        $result = array();
        if($res) {
            $result["field"] = $field;
            $result["data"] = $res;
            $result["allnum"] = $allnum['num'];
            $result["classlist"] = $classlist;
            ajax_return(array('error' => 0,'errortip' => "我的收藏数据成功",'result' => $result));
        } else {
            $result["field"] = $field;
            $result["data"] = array();
            $result["allnum"] = 0;
            $result["classlist"] = $classlist;
            ajax_return(array('error' => 1,'errortip' => "暂无我的收藏数据",'result' => $result));
        }
    }
    //批量取消 收藏
    function cancelMyCollectionView(){
        $request = Input('post.', '', 'trim,strip_tags');
        $this->ThisVerify($request);//验证账户
        if(isset($request['collectionid_list']) && $request['collectionid_list']!='') {
            $collectionid_list=json_decode($request['collectionid_list'],1);

            if($collectionid_list){
                foreach ($collectionid_list as $collectionid_listvar){
                    $this->DataControl->delData("shop_parenter_collection", "collection_id='{$collectionid_listvar}' and student_id = '{$request['student_id']}' and company_id = '{$request['company_id']}' " );
                }
            }
            ajax_return(array('error' => 0,'errortip' => "删除收藏数据成功",'result' => array()));
        }else{
            ajax_return(array('error' => 1,'errortip' => "数据不能为空",'result' => array()));
        }
    }

    //获取我的浏览记录
    function getMyBrowseView(){
        $request = Input('get.', '', 'trim,addslashes,strip_tags');
        $this->ThisVerify($request);//验证账户

        if(isset($request['p']) && $request['p'] !== ''){
            $page = $request['p'];
        }else{
            $page = '1';
        }
        if(isset($request['num']) && $request['num'] !== ''){
            $num = $request['num'];
        }else{
            $num = '10';
        }
        $pagestart = ($page-1)*$num;

        $sql = "SELECT c.*,m.sellgoods_name,m.sellgoods_listimg,FROM_UNIXTIME(c.browse_createtime, '%Y-%m-%d') as createtime,m.sellgoods_cartchange,m.sellgoods_type,m.tuition_id,m.coursepacks_id,m.goods_id,m.course_id 
                FROM shop_parenter_browse AS c 
                LEFT JOIN shop_sellgoods as m ON c.sellgoods_id = m.sellgoods_id  
                WHERE c.company_id = '{$request['company_id']}' and c.parenter_id = '{$request['parenter_id']}' and c.student_id = '{$request['student_id']}'
                limit {$pagestart},{$num} ";//
        $res = $this->DataControl->selectClear($sql);

        if($res){
            $alllist = array();
            foreach ($res as $resVar){
                if ($resVar['sellgoods_type'] == '0') {
                    $a = $this->DataControl->getFieldOne("smc_fee_pricing_tuition", "tuition_originalprice,tuition_sellingprice,tuition_buypiece", "tuition_id = '{$resVar['tuition_id']}'");
                    $resVar['price'] = $a['tuition_sellingprice'];
                    $resVar['old_price'] = $a['tuition_originalprice'];
                } elseif ($resVar['sellgoods_type'] == '1') {
                    $b = $this->DataControl->selectOne("select sum(tuition_sellingprice) as price from smc_fee_warehouse_courses where coursepacks_id = '{$resVar['coursepacks_id']}'");
                    $resVar['price'] = $b['price'];
                } elseif ($resVar['sellgoods_type'] == '2') {
                    $c = $this->DataControl->selectOne("select goods_originalprice,goods_vipprice from erp_goods where goods_id = '{$resVar['goods_id']}'");
                    $resVar['price'] = $c['goods_vipprice'];
                    $resVar['old_price'] = $c['goods_originalprice'];
                } elseif ($resVar['sellgoods_type'] == '3') {
                    $resVar['price'] = $resVar['feeitem_price'];
                }
//                $resVar["old_price"] = '1000.00';
//                $resVar["price"] = '888.00';

                $alllist[$resVar['createtime']]['letter'] = $resVar['createtime'];
                $alllist[$resVar['createtime']]['list'][] = $resVar;
            }
        }

        $datalist = array();
        if($alllist){
            $k = 0;
            foreach ($alllist as $alllistvar){
                $datalist[$k] = $alllistvar;
                $k++;
            }
        }

        $allnum = $this->DataControl->selectOne(" SELECT count(c.browse_id) as num 
                  FROM shop_parenter_browse AS c 
                LEFT JOIN shop_sellgoods as m ON c.sellgoods_id = m.sellgoods_id  
                WHERE c.company_id = '{$request['company_id']}' and c.parenter_id = '{$request['parenter_id']}' and c.student_id = '{$request['student_id']}' ");

        $field = array();
        $field["company_id"] = "企业ID";
        $field["parenter_id"] = "家长ID";
        $field["student_id"] = "学员ID";
        $field["sellgoods_id"] = "商品ID";
        $field["browse_createtime"] = "浏览时间戳";
        $field["createtime"] = "浏览日期";
        $field["sellgoods_name"] = "商品名称";
        $field["sellgoods_listimg"] = "商品图片";
        $field["old_price"] = "商品原价";
        $field["price"] = "商品优惠价";
        $field["sellgoods_cartchange"] = "是否允许购物车加减 0不可以1可以";

        $request = array();
        if($datalist) {
            $result["field"] = $field;
            $result["data"] = $datalist;
            $result["allnum"] = $allnum['num'];
            ajax_return(array('error' => 0,'errortip' => "我的浏览数据成功",'result' => $result));
        } else {
            $result["field"] = $field;
            $result["data"] = array();
            $result["allnum"] = 0;
            ajax_return(array('error' => 1,'errortip' => "暂无我的浏览数据",'result' => $result));
        }
    }

    //批量取消 浏览记录
    function cancelMyBrowseView(){
        $request = Input('post.', '', 'trim,strip_tags');
        $this->ThisVerify($request);//验证账户
        if(isset($request['browseid_list']) && $request['browseid_list']!='') {
            $browseid_list=json_decode($request['browseid_list'],1);

            if($browseid_list){
                foreach ($browseid_list as $browseid_listvar){
                    $this->DataControl->delData("shop_parenter_browse", "browse_id='{$browseid_listvar}' and student_id = '{$request['student_id']}' and company_id = '{$request['company_id']}' " );
                }
            }
            ajax_return(array('error' => 0,'errortip' => "删除浏览数据成功",'result' => array()));
        }else{
            ajax_return(array('error' => 1,'errortip' => "数据不能为空",'result' => array()));
        }
    }

    //清空 浏览记录
    function cancelMyBrowseAllView(){
        $request = Input('post.', '', 'trim,strip_tags');
        $this->ThisVerify($request);//验证账户
        if($this->DataControl->delData("shop_parenter_browse", "student_id = '{$request['student_id']}' and company_id = '{$request['company_id']}' and parenter_id = '{$request['parenter_id']}' ")) {
            ajax_return(array('error' => 0,'errortip' => "浏览数据清空成功",'result' => array()));
        }else{
            ajax_return(array('error' => 1,'errortip' => "数据错误",'result' => array()));
        }
    }

    //协议详情
    function protocolDetailView(){
        $request = Input('get.', '', 'trim,addslashes,strip_tags');
        if($request['student_id']){
//            $this->ThisVerify($request);//验证账户
            $pid = $request['parenter_id'];
        }else{
            $student_id = $this->DataControl->getFieldOne("smc_student_protocol","student_id,company_id","protocol_id = '{$request['protocol_id']}'");
            $parenter_id = $this->DataControl->getFieldOne("smc_student_family","parenter_id","student_id = '{$student_id['student_id']}'");
            $pid = $parenter_id['parenter_id'];
            $issign = $this->DataControl->getFieldOne("gmc_company","company_issign","company_id = '{$student_id['company_id']}'");

        }

//        var_dump($student_id);
//        var_dump($pid);die();
        $protocol = $this->DataControl->getOne("smc_student_protocol","protocol_id = '{$request['protocol_id']}'");
        if($protocol['protocol_renums'] > '0'){
            $protocol['protocol_nums'] = $protocol['protocol_renums'];
        }
        $student = $this->DataControl->getFieldOne("smc_student","student_cnname,student_enname,student_birthday,student_sex","student_id = '{$protocol['student_id']}'");
        $school = $this->DataControl->getFieldOne("smc_school","school_cnname,school_signet,companies_id","school_id = '{$protocol['school_id']}'");
        $course = $this->DataControl->getFieldOne("smc_course","course_cnname,course_branch,course_classnum,course_classtimes,coursetype_id,course_perhour,coursecat_id","course_id = '{$protocol['course_id']}'");
        if($protocol['protocol_recourse']){
            $course['course_cnname'] = $protocol['protocol_recourse'];
        }
        if($protocol['protocol_renums'] > '0'){
            $course['course_classnum'] = $protocol['protocol_renums'];
        }
        

        // $companies_id = $this->DataControl->getFieldOne("smc_school_coursecat_subject","companies_id","school_id = '{$protocol['school_id']}' and coursecat_id = '{$course['coursecat_id']}'");
        
        if($protocol['protocol_recompanies_id']>0){
            $companies_id['companies_id']=$protocol['protocol_recompanies_id'];
        }else{

            $companies_id = $this->DataControl->getFieldOne("smc_payfee_order","companies_id","order_pid = '{$protocol['order_pid']}'");
        }
            

        $companies = $this->DataControl->getFieldOne("gmc_code_companies", "companies_cnname,companies_signet,companies_liaison,companies_examine,companies_register,companies_permitbranch,companies_permitstday,companies_permitendday,companies_icp,companies_licensestday,companies_licenseendday,companies_society,companies_licensestatus", "companies_id = '{$companies_id['companies_id']}'");
        if($protocol['protocol_recompanies'] > '0'){
            $companies['companies_cnname'] = $protocol['protocol_recompanies'];
        }
        $parenter = $this->DataControl->selectOne("
            SELECT
                p.parenter_mobile,
                p.parenter_cnname,
                p.parenter_sign,
                f.family_relation,
                f.family_address 
            FROM
                smc_parenter AS p
                LEFT JOIN smc_student_family AS f ON p.parenter_id = f.parenter_id
                where p.parenter_id = '{$pid}' and f.student_id = '{$protocol['student_id']}'");
        $order = $this->DataControl->selectOne("select o.order_pid,p.pay_pid,p.pay_typename,p.pay_price from smc_payfee_order as o left join smc_payfee_order_pay as p on o.order_pid = p.order_pid where o.order_pid = '{$protocol['order_pid']}'");
        $times = $course['course_classnum'] * $course['course_perhour'];
        $time = $protocol['protocol_nums'] * $course['course_perhour'];
        $pricing_id = $this->DataControl->selectOne("select pricing_id from smc_payfee_order_course as c where c.order_pid = '{$protocol['order_pid']}'");
        $agreement = $this->DataControl->getFieldOne("smc_fee_pricing","agreement_id","pricing_id = '{$pricing_id['pricing_id']}'");

//        if ($protocol['protocol_isaudit'] == '1') {
//            $protocolOne = $this->DataControl->getFieldOne("smc_student_protocol", "treaty_tabletip,treaty_protocol", "protocol_id = '{$protocol['protocol_id']}'");
//        } else {
//            if($course['coursecat_id'] == '135'){
//                $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '731' and coursetype_id = '79654' and t.treaty_applytype = '0'");
//            }else{
//                $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and t.treaty_applytype = '1' and a.school_id = '{$protocol['school_id']}'");
//                if(!$protocolOne){
//                    $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and t.treaty_applytype = '0'");
//                }
//            }
//        }
//
//        if ($protocol['protocol_isaudit'] == '1') {
//            $protocolOne = $this->DataControl->getFieldOne("smc_student_protocol", "treaty_tabletip,treaty_protocol", "protocol_id = '{$protocol['protocol_id']}'");
//        } else {
//            $isprocat = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and coursecat_id = '{$course['coursecat_id']}'");
//            if($isprocat){
//                $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and coursecat_id = '{$course['coursecat_id']}' and t.treaty_applytype = '1' and a.school_id = '{$protocol['school_id']}'");
//                if(!$protocolOne){
//                    $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and coursecat_id = '{$course['coursecat_id']}' and t.treaty_applytype = '0'");
//                    if(!$protocolOne){
//                        $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and coursecat_id = '0' and t.treaty_applytype = '0'");
//                    }
//                }
//            }else{
//                $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and t.treaty_applytype = '1' and a.school_id = '{$protocol['school_id']}' and t.coursecat_id = '0'");
//                if(!$protocolOne){
//                    $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and t.treaty_applytype = '0' and t.coursecat_id = '0'");
//                }
//            }
//        }

        $pro = $this->gettip($request);

//        var_dump($pro);

//        var_dump($pro);die;
        $result = array();

        $result['protocol'] = $protocol;
        $result['student'] = $student;
        $result['school'] = $school;
        $result['parenter'] = $parenter;
        $result['order'] = $order;
        $result['course'] = $course;
        $result['times'] = $times;
        $result['time'] = $time;
        $result['issign'] = $issign['company_issign'];
        if($protocol['protocol_id'] == '584525'){
            $result['signet'] = 'https://pic.kedingdang.com/schoolmanage/202304131335x913507849.png';
        }else{
            $result['signet'] = $companies['companies_signet'];
        }
        if($request['isScshop'] !== '1'){
            $a = str_replace("<h2","<p",$pro['treaty_protocol']);
            $b = str_replace("</h2>","</p>",$a);
            $result['text'] = (string)$b;
        }else{
            $result['text'] = $pro['treaty_protocol'];
        }
        ajax_return(array('error' => 0,'errortip' => "获取成功",'result' => $result));

    }



    function gettip($request){
        $PartyA = array();
        $protocol = $this->DataControl->getOne("smc_student_protocol", "protocol_id = '{$request['protocol_id']}'");

        if($protocol['protocol_renums'] > '0'){
            $protocol['protocol_nums'] = $protocol['protocol_renums'];
        }
//        var_dump($request);die();

        $order = $this->DataControl->selectOne("select o.order_pid,o.trading_pid,o.companies_id,p.pay_pid,p.pay_typename,p.pay_price,o.order_coupon_price,o.order_paidprice,o.order_arrearageprice,o.order_paymentprice,c.ordercourse_buynums,c.ordercourse_totalprice,c.ordercourse_unitprice from smc_payfee_order as o left join smc_payfee_order_pay as p on o.order_pid = p.order_pid left join smc_payfee_order_course as c on c.order_pid = o.order_pid where o.order_pid = '{$protocol['order_pid']}' and c.course_id='{$protocol['course_id']}'");

        $company = $this->DataControl->getFieldOne("gmc_company", "company_cnname,company_logo", "company_id = '{$protocol['company_id']}'");
        $school = $this->DataControl->getFieldOne("smc_school", "school_cnname,school_signet,school_address,school_phone,companies_id,school_liaison,school_examine,school_register,school_permitbranch,school_permitstday,school_permitendday,school_icp,school_licensestday,school_licenseendday,school_society,school_licensestatus", "school_id = '{$protocol['school_id']}'");
        $course = $this->DataControl->getFieldOne("smc_course", "course_cnname,course_branch,course_classnum,course_classtimes,coursetype_id,course_inclasstype,course_perhour,coursecat_id", "course_id = '{$protocol['course_id']}'");
        $companies_id = $this->DataControl->getFieldOne("smc_school_coursecat_subject","companies_id","school_id = '{$protocol['school_id']}' and coursecat_id = '{$course['coursecat_id']}'");
        $companies = $this->DataControl->getFieldOne("gmc_code_companies", "companies_cnname,companies_signet,companies_liaison,companies_examine,companies_register,companies_permitbranch,companies_permitstday,companies_permitendday,companies_icp,companies_licensestday,companies_licenseendday,companies_society,companies_licensestatus,companies_supervisebank,companies_superviseaccount,companies_settlebank,companies_settleaccount,companies_permitstday,companies_licensestday", "companies_id = '{$companies_id['companies_id']}'");

        $PartyA['companies_cnname'] = $companies['companies_cnname'];
        if($protocol['protocol_recompanies']){
            $PartyA['companies_cnname'] = $protocol['protocol_recompanies'];
        }
        $PartyA['companies_permitstday'] = substr($companies['companies_permitstday'],strripos($companies['companies_permitstday'],"至")+3);
        $PartyA['companies_licensestday'] =  substr($companies['companies_licensestday'],strripos($companies['companies_licensestday'],"至")+3);
        $PartyA['companies_supervisebank'] = $companies['companies_supervisebank'];
        $PartyA['companies_superviseaccount'] = $companies['companies_superviseaccount'];
        $PartyA['companies_settlebank'] = $companies['companies_settlebank'];
        $PartyA['companies_settleaccount'] = $companies['companies_settleaccount'];
        $PartyA['school_cnname'] = $school['school_cnname'];
        $PartyA['company_cnname'] = $company['company_cnname'];
        $PartyA['school_address'] = $school['school_address'];
        if($school['school_phone']){
            $PartyA['school_phone'] = $school['school_phone'];
        }else{
            $PartyA['school_phone'] = '--';
        }
        if($companies['companies_liaison']){
            $PartyA['school_liaison'] = $companies['companies_liaison'];
        }else{
            $PartyA['school_liaison'] = '--';
        }
        if($companies['companies_examine']){
            $PartyA['school_examine'] = $companies['companies_examine'];
        }else{
            $PartyA['school_examine'] = '--';
        }
        if($companies['companies_register']){
            $PartyA['school_register'] = $companies['companies_register'];
        }else{
            $PartyA['school_register'] = '--';
        }
        if($companies['companies_permitbranch']){
            $PartyA['school_permitbranch'] = $companies['companies_permitbranch'];
        }else{
            $PartyA['school_permitbranch'] = '--';
        }
        if($companies['companies_permitstday']){
            $PartyA['school_permitstday'] = $companies['companies_permitstday'];
        }else{
            $PartyA['school_permitstday'] = '--';
        }
        if($companies['companies_licensestday']){
            $PartyA['school_licensestday'] = $companies['companies_licensestday'];
        }else{
            $PartyA['school_licensestday'] = '--';
        }
        if($companies['companies_society']){
            $PartyA['school_society'] = $companies['companies_society'];
        }else{
            $PartyA['school_society'] = '--';
        }
        $PartyA['school_signet'] = $companies['companies_signet'];
        $PartyA['school_icp'] = $companies['companies_icp'];
        $PartyA['company_logo'] = $company['company_logo'];
        $PartyA['company_shortname'] = $school['company_shortname'];

        $PartyB = array();
        $student = $this->DataControl->getFieldOne("smc_student", "student_cnname,student_enname,student_birthday,student_sex,student_branch,student_idcard", "student_id = '{$protocol['student_id']}'");
        $PartyB['student_branch'] = $student['student_branch'];
        $PartyB['student_cnname'] = $student['student_cnname'];
        $PartyB['student_sex'] = $student['student_sex'];
        $PartyB['student_birthday'] = $student['student_birthday'];
        $famliy = $this->DataControl->getFieldOne("smc_student_family", "parenter_id", "student_id = '{$protocol['student_id']}' and family_isdefault = '1'");
        $parenter = $this->DataControl->selectOne("
            SELECT
                p.parenter_mobile,
                p.parenter_cnname,
                p.parenter_sign,
                f.family_relation,
                f.family_cnname,
                f.family_address 
            FROM
                smc_parenter AS p
                LEFT JOIN smc_student_family AS f ON p.parenter_id = f.parenter_id
                where p.parenter_id = '{$famliy['parenter_id']}' and f.student_id = '{$protocol['student_id']}'");
        $PartyB['phone'] = $parenter['parenter_mobile'];
        $PartyB['schoolname'] = '--';
        $PartyB['address'] = '--';
        $PartyB['student_idcard'] = $student['student_idcard'];

        $guarder = array();
        $guarder['guardername'] = $parenter['family_cnname'];
        $guarder['guarderphone'] = $parenter['parenter_mobile'];
        $guarder['guarderrelation'] = $parenter['family_relation'];
        $guarder['parenter_sign'] = $protocol['protocol_sign'];

        $famliys = $this->DataControl->getFieldOne("smc_student_family", "parenter_id", "student_id = '{$protocol['student_id']}' and family_isdefault = '0'");

        if ($famliys) {
            $parenters = $this->DataControl->selectOne("
            SELECT
                p.parenter_mobile,
                p.parenter_cnname,
                p.parenter_sign,
                f.family_relation,
                f.family_cnname,
                f.family_address 
            FROM
                smc_parenter AS p
                LEFT JOIN smc_student_family AS f ON p.parenter_id = f.parenter_id
                where p.parenter_id = '{$famliys['parenter_id']}' and f.student_id = '{$protocol['student_id']}'");
            $guarder['urgentname'] = $parenters['family_cnname'];
            $guarder['urgentphone'] = $parenters['parenter_mobile'];
            $guarder['urgentrelation'] = $parenters['family_relation'];
        } else {
            $guarder['urgentname'] = $parenter['family_cnname'];
            $guarder['urgentphone'] = $parenter['parenter_mobile'];
            $guarder['urgentrelation'] = $parenter['family_relation'];
        }

        $courseInfo = array();
        $pricing_id = $this->DataControl->selectOne("select pricing_id,class_id from smc_payfee_order_course as c where c.order_pid = '{$protocol['order_pid']}'");
        $agreement = $this->DataControl->getFieldOne("smc_fee_pricing", "agreement_id", "pricing_id = '{$pricing_id['pricing_id']}'");

//        if ($protocol['protocol_isaudit'] == '1') {
//            $protocolOne = $this->DataControl->getFieldOne("smc_student_protocol", "treaty_tabletip,treaty_protocol", "protocol_id = '{$protocol['protocol_id']}'");
//        } else {
//            if($course['coursecat_id'] == '135'){
//                $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '731' and coursetype_id = '79654' and t.treaty_applytype = '0'");
//            }else{
//                $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and t.treaty_applytype = '1' and a.school_id = '{$request['school_id']}'");
//                if(!$protocolOne){
//                    $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and t.treaty_applytype = '0'");
//                }
//            }
//        }

//        $isprocat = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and coursecat_id = '{$course['coursecat_id']}'");
//
//        if($isprocat){
//            $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and coursecat_id = '{$course['coursecat_id']}' and t.treaty_applytype = '1' and a.school_id = '{$protocol['school_id']}'");
//            if(!$protocolOne){
//                $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and coursecat_id = '{$course['coursecat_id']}' and t.treaty_applytype = '0'");
//                if(!$protocolOne){
//                    $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and coursecat_id = '0' and t.treaty_applytype = '0'");
//                    if(!$protocolOne){
//                        $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and coursecat_id = '0' and t.treaty_applytype = '1'  and a.school_id = '{$protocol['school_id']}'");
//                    }
//                }
//            }
//        }else{
//            $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and t.treaty_applytype = '1' and a.school_id = '{$protocol['school_id']}' and t.coursecat_id = '0'");
//            if(!$protocolOne){
//                $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and t.treaty_applytype = '0' and t.coursecat_id = '0'");
//            }
//        }

        if ($protocol['protocol_isaudit'] == '1') {
            $protocolOne = $this->DataControl->getFieldOne("smc_student_protocol", "treaty_tabletip,treaty_protocol", "protocol_id = '{$protocol['protocol_id']}'");
        } else {
            $isprocat = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and coursecat_id = '{$course['coursecat_id']}'");
            if($isprocat){
                $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and coursecat_id = '{$course['coursecat_id']}' and t.treaty_applytype = '1' and a.school_id = '{$protocol['school_id']}'");
                if(!$protocolOne){
                    $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and coursecat_id = '{$course['coursecat_id']}' and t.treaty_applytype = '0'");
                    if(!$protocolOne){

                        $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and coursecat_id = '0' and t.treaty_applytype = '0'");
                        if(!$protocolOne){
                            $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and coursecat_id = '0' and t.treaty_applytype = '1'  and a.school_id = '{$protocol['school_id']}'");
                        }
                    }
                }
            }else{
                $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and t.treaty_applytype = '1' and a.school_id = '{$protocol['school_id']}' and t.coursecat_id = '0'");
                if(!$protocolOne){
                    $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and t.treaty_applytype = '0' and t.coursecat_id = '0'");
                }
            }
        }

//        var_dump($protocolOne);die();


        $courseInfo['course_branch'] = $course['course_branch'];
        if($protocol['protocol_recourse']){
            $courseInfo['course_branch'] = $protocol['protocol_recourse'];
        }
        $courseInfo['course_cnname'] = $course['course_cnname'];
        $courseInfo['ordercourse_unitprice'] = $course['ordercourse_unitprice'];
        if ($order['ordercourse_buynums'] == $course['course_classnum']) {
            $courseInfo['type'] = '新班';
        } else {
            $courseInfo['type'] = '插班';
        }
        if ($course['course_inclasstype'] == '2' || $course['course_inclasstype'] == '0') {
            $courseInfo['course_classnum'] = $protocol['protocol_nums'];
        } elseif ($course['course_inclasstype'] == '1') {
            $courseInfo['course_classnum'] = '';
        } else {
            $courseInfo['course_classnum'] = $course['course_classnum'];

        }

        if ($course['course_inclasstype'] == '1') {
            $courseInfo['protocol_nums'] = '';
        } else {
            $courseInfo['protocol_nums'] = $protocol['protocol_nums'];
        }

        $courseInfo['course_classtimes'] = $course['course_perhour'] . 'h';

        $priceInfo = array();
        $priceInfo['protocol_pid'] = $protocol['protocol_pid'];
        $priceInfo['courseprice'] = '¥' . $order['ordercourse_totalprice'];
        $priceInfo['sendprice'] = '¥' . $protocol['protocol_price'];

        $track = $this->DataControl->getFieldOne("smc_payfee_order_tracks", "staffer_id", "order_pid = '{$order['order_pid']}' and tracks_title = '创建订单'");
        $agent = $this->DataControl->getFieldOne("smc_staffer", "staffer_cnname", "staffer_id = '{$track['staffer_id']}'");
        $priceInfo['adviser'] = '--';
        $priceInfo['agent'] = $agent['staffer_cnname'];
        $priceInfo['principal'] = '--';

        $priceInfo['bigprice'] = $this->convert_2_cn(intval($order['ordercourse_totalprice']));

        $startday = $this->DataControl->selectOne("SELECT
	h.changelog_day 
FROM
	smc_student_changelog AS h
WHERE
	h.student_id = '{$protocol['student_id']}' 
	AND h.class_id = '{$pricing_id['class_id']}'
	AND h.stuchange_code = 'A02'");
        $date = $startday['changelog_day'];

        $endday = date('Y-m-d',strtotime("$date +90 day"));

        if($pricing_id['class_id'] == '0'){
            $startday['changelog_day'] = '';
            $endday = '';
        }

        if($protocolOne){
            $treatyArray = array();
            $treatyArray['companies_cnname'] = $PartyA['companies_cnname'];
            $treatyArray['school_address'] = $PartyA['school_address'];
            $treatyArray['school_examine'] = $PartyA['school_examine'];
            $treatyArray['school_register'] = $PartyA['school_register'];
            $treatyArray['school_permitbranch'] = $PartyA['school_permitbranch'];
            $treatyArray['school_permitstday'] = $PartyA['school_permitstday'];
            $treatyArray['school_icp'] = $PartyA['school_icp'];
            $treatyArray['school_licensestday'] = $PartyA['school_licensestday'];
            $treatyArray['school_society'] = $PartyA['school_society'];
            $treatyArray['school_liaison'] = $PartyA['school_liaison'];
            $treatyArray['school_phone'] = $PartyA['school_phone'];
            $treatyArray['student_branch'] = $PartyB['student_branch'];
            $treatyArray['student_cnname'] = $PartyB['student_cnname'];
            $treatyArray['student_sex'] = $PartyB['student_sex'];
            $treatyArray['student_birthday'] = $PartyB['student_birthday'];
            $treatyArray['phone'] = $PartyB['phone'];
            $treatyArray['schoolname'] = $PartyB['schoolname'];
            $treatyArray['address'] = $PartyB['address'];
            $treatyArray['student_idcard'] = $PartyB['student_idcard'];
            $treatyArray['guardername'] = $guarder['guardername'];
            $treatyArray['guarderphone'] = $guarder['guarderphone'];
            $treatyArray['guarderrelation'] = $guarder['guarderrelation'];
            $treatyArray['urgentname'] = $guarder['urgentname'];
            $treatyArray['urgentphone'] = $guarder['urgentphone'];
            $treatyArray['urgentrelation'] = $guarder['urgentrelation'];
            $treatyArray['course_branch'] = $courseInfo['course_branch'];
            $treatyArray['course_cnname'] = $courseInfo['course_cnname'];
            $treatyArray['type'] = $courseInfo['type'];
            $treatyArray['protocol_nums'] = $courseInfo['protocol_nums'];
            $treatyArray['protocol_pid'] = $priceInfo['protocol_pid'];
            $treatyArray['sendprice'] = $priceInfo['sendprice'];
            $treatyArray['agent'] = $priceInfo['agent'];
            $protocolOne['treaty_tabletip'] = $this->contractTable($protocolOne['treaty_tabletip'],$treatyArray);
        }

        if($protocolOne){
            $treatyArray = array();
            $treatyArray['companies_cnname'] = $PartyA['companies_cnname'];
            $treatyArray['startday'] = $startday['changelog_day'];
            $treatyArray['endday'] = $endday;
            $treatyArray['companies_permitstday'] = $PartyA['companies_permitstday'];
            $treatyArray['companies_licensestday'] = $PartyA['companies_licensestday'];
            $treatyArray['course_classnum'] = $courseInfo['course_classnum'];
            $treatyArray['course_classtimes'] = $courseInfo['course_classtimes'];
            $treatyArray['ordercourse_unitprice'] = $courseInfo['ordercourse_unitprice'];
            $treatyArray['companies_supervisebank'] = $PartyA['companies_supervisebank'];
            $treatyArray['companies_superviseaccount'] = $PartyA['companies_superviseaccount'];
            $treatyArray['companies_settlebank'] = $PartyA['companies_settlebank'];
            $treatyArray['companies_settleaccount'] = $PartyA['companies_settleaccount'];
            $treatyArray['courseprice'] = $priceInfo['courseprice'];
            $treatyArray['bigprice'] = $priceInfo['bigprice'];
            $treatyArray['school_address'] = $PartyA['school_address'];
            $treatyArray['school_examine'] = $PartyA['school_examine'];
            $treatyArray['school_register'] = $PartyA['school_register'];
            $treatyArray['school_permitbranch'] = $PartyA['school_permitbranch'];
            $treatyArray['school_permitstday'] = $PartyA['school_permitstday'];
            $treatyArray['school_icp'] = $PartyA['school_icp'];
            $treatyArray['school_licensestday'] = $PartyA['school_licensestday'];
            $treatyArray['school_society'] = $PartyA['school_society'];
            $treatyArray['school_liaison'] = $PartyA['school_liaison'];
            $treatyArray['school_phone'] = $PartyA['school_phone'];
            $treatyArray['student_branch'] = $PartyB['student_branch'];
            $treatyArray['student_cnname'] = $PartyB['student_cnname'];
            $treatyArray['student_sex'] = $PartyB['student_sex'];
            $treatyArray['student_birthday'] = $PartyB['student_birthday'];
            $treatyArray['phone'] = $PartyB['phone'];
            $treatyArray['schoolname'] = $PartyB['schoolname'];
            $treatyArray['address'] = $PartyB['address'];
            $treatyArray['student_idcard'] = $PartyB['student_idcard'];
            $treatyArray['guardername'] = $guarder['guardername'];
            $treatyArray['guarderphone'] = $guarder['guarderphone'];
            $treatyArray['guarderrelation'] = $guarder['guarderrelation'];
            $treatyArray['urgentname'] = $guarder['urgentname'];
            $treatyArray['urgentphone'] = $guarder['urgentphone'];
            $treatyArray['urgentrelation'] = $guarder['urgentrelation'];
            $treatyArray['course_branch'] = $courseInfo['course_branch'];
            $treatyArray['course_cnname'] = $courseInfo['course_cnname'];
            $treatyArray['type'] = $courseInfo['type'];
            $treatyArray['protocol_nums'] = $courseInfo['protocol_nums'];
            $treatyArray['protocol_pid'] = $priceInfo['protocol_pid'];
            $treatyArray['sendprice'] = $priceInfo['sendprice'];
            $treatyArray['agent'] = $priceInfo['agent'];
            $protocolOne['treaty_protocol'] = $this->contractTable($protocolOne['treaty_protocol'],$treatyArray);
        }

        return $protocolOne;
    }



    function contractTable($tabletip,$treatyArray){
        $tableNote = $tabletip;
        foreach($treatyArray as $key=>$treatyOne){

            $tableNote = str_replace("#".$key."#",$treatyOne,$tableNote);
        }
        return $tableNote;
    }


    function convert_2_cn($num) {
        $convert_cn = array("零","壹","贰","叁","肆","伍","陆","柒","捌","玖");
        $repair_number = array('零仟零佰零拾零','万万','零仟','零佰','零拾');
        $unit_cn = array("拾","佰","仟","万","亿");
        $exp_cn = array("","万","亿");
        $max_len = 12;
        $len = strlen($num);
        if($len > $max_len) {
            return 'outnumber';
        }
        $num = str_pad($num,12,'-',STR_PAD_LEFT);
        $exp_num = array();
        $k = 0;
        for($i=12;$i>0;$i--){
            if($i%4 == 0) {
                $k++;
            }
            $exp_num[$k][] = substr($num,$i-1,1);
        }
        $str = '';
        foreach($exp_num as $key=>$nums) {
            if(array_sum($nums)){
                $str = array_shift($exp_cn) . $str;
            }
            foreach($nums as $nk=>$nv) {
                if($nv == '-'){continue;}
                if($nk == 0) {
                    $str = $convert_cn[$nv] . $str;
                } else {
                    $str = $convert_cn[$nv].$unit_cn[$nk-1] . $str;
                }
            }
        }
        $str = str_replace($repair_number,array('万','亿','-'),$str);
        $str = preg_replace("/-{2,}/","",$str);
        $str = str_replace(array('零','-'),array('','零'),$str);
        return $str;
    }


}