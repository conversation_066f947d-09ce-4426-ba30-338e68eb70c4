<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/4/17
 * Time: 10:51
 */

namespace Work\Controller\Scshopapi;

use Model\Scshop\CartModel;

class CartController extends viewTpl{
    public $u;
    public $t;
    public $c;
    public $visitType="api";
    public $DataControl;

    //预加载处理类
    function __construct() {
        parent::__construct ();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
        $this->api = $this->router->getApi();
    }

    function HomeView(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Scshop\CartModel($request);
        $dataList = $Model->CartList($request);

        $result = array();
        $result['all_num'] = $dataList['allnums'];
        if($dataList['list']){
            $result['list'] = $dataList['list'];
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
        }else{
            $result['list'] = array();
            $res = array('error' => 1, 'errortip' => '购物车空空如也，去选一些商品吧', 'result' => $result);
        }
        ajax_return($res);
    }

    //添加购物车
    function AddCartApi(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Scshop\CartModel($request);
        $res = $Model->AddCart($request);

        ajax_return($res);
    }

    //购物车修改商品数量
    function EditCartApi(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Scshop\CartModel($request);
        $res = $Model->EditCart($request);

        ajax_return($res);
    }

    //移除购物车
    function RemoveCartApi(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Scshop\CartModel($request);
        $res = $Model->RemoveCart($request);

        ajax_return($res);
    }

    //转为收藏
    function CollectionApi(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Scshop\CartModel($request);
        $res = $Model->Collection($request);

        ajax_return($res);
    }

}