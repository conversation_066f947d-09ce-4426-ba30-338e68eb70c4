<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/4/27
 * Time: 17:49
 */

namespace Work\Controller\Scshopapi;


class MergeBoingPayController extends viewTpl{
    public $u;
    public $t;
    public $c;
    public $errortip = false;

    function __construct() {
        parent::__construct ();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
    }


    //招行发起支付
    function OrderPayView(){
        $request = Input('get.','','trim,addslashes');

        $pay_pid = $request['paypid'];

        $sql = "select b.company_id 
                from smc_payfee_mergeorder_mergepay as a,smc_payfee_mergeorder as b 
                where b.mergeorder_pid=a.mergeorder_pid and a.mergepay_pid='{$pay_pid}'";

        $orderOne = $this->DataControl->selectOne($sql);
//        $PayModel = new \Model\Api\MergeboingPayModel($orderOne['company_id']);
//        $result = $PayModel->OrderPay($pay_pid);

        if($orderOne){
            $PayModel = new \Model\Api\MergeboingPayModel($orderOne['company_id']);
            $result = $PayModel->OrderPay($pay_pid);
        }else{
            $orderPay = $this->DataControl->getFieldOne("smc_payfee_order_pay","pay_id,order_pid,pay_pid","pay_pid='{$pay_pid}'");
            $orderOne = $this->DataControl->getFieldOne("smc_payfee_order","company_id,order_pid","order_pid='{$orderPay['order_pid']}'");

            $PayModel = new \Model\Api\MergeboingPayModel($orderOne['company_id']);
            $result = $PayModel->ordinaryOrderPay($pay_pid);
        }



        if($PayModel->error){
            $res = array('error' => 1, 'errortip' => $PayModel->errortip, 'result' =>$result);
        }else{
            $res = array('error' =>$PayModel->error, 'errortip' => $PayModel->errortip, 'result' =>$result);
        }
        ajax_return($res,$request['language_type']);
    }

    //7支付宝服务窗支付或微信小程序支付
    function onlineOrderPayView(){
        $request = Input('get.','','trim,addslashes');

        if($request['order_type'] == 0){
            //普通订单

            $sql = "select a.pay_pid,a.paytype_code
                    from smc_payfee_order_pay as a 
                    where a.order_pid='{$request['order_pid']}' and a.pay_issuccess=0
                    ";

            $payOne = $this->DataControl->selectOne($sql);

            if($payOne){
                $pay_pid = $payOne['pay_pid'];

            }else{
                $orderOne=$this->DataControl->getFieldOne("smc_payfee_order","company_id,school_id,staffer_id","order_pid='{$request['order_pid']}'");

                $publicarray=array();
                $publicarray['company_id'] = $orderOne['company_id'];
                $publicarray['school_id'] = $orderOne['school_id'];
                $publicarray['staffer_id'] = $orderOne['staffer_id'];
                $Model = new \Model\Smc\OrderPayModel($publicarray, $request['order_pid']);
    
                $payArray=array();
                $payArray['paytype'] = 'H5Pay';
                $payArray['paytimes'] = '1';
                $payArray['paymenttype'] = $request['paymenttype'];
                $payArray['order_pid'] = $request['order_pid'];
    
                $data = $Model->createOrderPay($payArray);
    
                if(!$data){
                    ajax_return(array('error' => 1, 'errortip' => $Model->errortip),$request['language_type']);
                }
    
                $pay_pid = $data['pay_pid'];

            }

        }elseif($request['order_type'] == 1){


            $sql = "select a.mergepay_pid,a.paytype_code
            from smc_payfee_mergeorder_mergepay as a 
            where a.mergeorder_pid='{$request['order_pid']}' and a.mergepay_issuccess=0
            ";

            $payOne = $this->DataControl->selectOne($sql);

            if($payOne){
                $pay_pid = $payOne['mergepay_pid'];

            }else{
                //组合订单
                $orderOne=$this->DataControl->getFieldOne("smc_payfee_mergeorder","company_id,school_id,staffer_id","mergeorder_pid='{$request['order_pid']}'");

                $publicarray=array();
                $publicarray['company_id'] = $orderOne['company_id'];
                $publicarray['school_id'] = $orderOne['school_id'];
                $publicarray['staffer_id'] = $orderOne['staffer_id'];
                $OrderModel = new \Model\Smc\MergeOrderPayModel($publicarray, $request['order_pid']);

                $payArray=array();
                $payArray['mergeorder_pid'] = $request['order_pid'];
                $payArray['paymenttype'] = $request['paymenttype'];

                $data = $OrderModel->mergeOrderPayH5($payArray);

                if(!$data){
                    ajax_return(array('error' => 1, 'errortip' => $OrderModel->errortip),$request['language_type']);
                }

                $pay_pid = $data['mergepay_pid'];
            }

            
        }else{
            ajax_return(array('error' => 1, 'errortip' => '订单类型错误'), $request['language_type']);
        }

        $sql = "select b.company_id 
                from smc_payfee_mergeorder_mergepay as a,smc_payfee_mergeorder as b 
                where b.mergeorder_pid=a.mergeorder_pid and a.mergepay_pid='{$pay_pid}'";

        $orderOne = $this->DataControl->selectOne($sql);

        if($orderOne){
            $PayModel = new \Model\Api\MergeboingPayModel($orderOne['company_id']);
            $result = $PayModel->onlineOrderPay($pay_pid,$request['paymenttype'],$request['openid']);
        }else{
            $orderPay = $this->DataControl->getFieldOne("smc_payfee_order_pay","pay_id,order_pid,pay_pid","pay_pid='{$pay_pid}'");
            $orderOne = $this->DataControl->getFieldOne("smc_payfee_order","company_id,order_pid","order_pid='{$orderPay['order_pid']}'");

            $PayModel = new \Model\Api\MergeboingPayModel($orderOne['company_id']);
            $result = $PayModel->ordinaryOnlineOrderPay($pay_pid,$request['paymenttype'],$request['openid']);
        }

        if($PayModel->error){
            $res = array('error' => 1, 'errortip' => $PayModel->errortip, 'result' =>$result);
        }else{
            $res = array('error' =>$PayModel->error, 'errortip' => $PayModel->errortip, 'result' =>$result);
        }
        ajax_return($res,$request['language_type']);
    }

    function mergeOrderStatusQueryView(){
        $request = Input('get.','','trim,addslashes');

        $pay_pid = $request['paypid'];
        $order_pid=$request['order_pid'];

        if(isset($request['paypid']) && $request['paypid']!=''){
            $sql = "select b.company_id,a.mergepay_pid 
                from smc_payfee_mergeorder_mergepay as a,smc_payfee_mergeorder as b 
                where b.mergeorder_pid=a.mergeorder_pid and a.mergepay_pid='{$pay_pid}'";

            $orderOne = $this->DataControl->selectOne($sql);
            if($orderOne){
                $PayModel = new \Model\Api\MergeboingPayModel($orderOne['company_id']);
                $result = $PayModel->mergeOrderStatusQuery($orderOne['mergepay_pid']);
            }else{
                $orderPay = $this->DataControl->getFieldOne("smc_payfee_order_pay","pay_id,order_pid,pay_pid","pay_pid='{$pay_pid}'");
                $orderOne = $this->DataControl->getFieldOne("smc_payfee_order","company_id,order_pid","order_pid='{$orderPay['order_pid']}'");

                $PayModel = new \Model\Api\MergeboingPayModel($orderOne['company_id']);
                $result = $PayModel->mergeOrdinaryOrderStatusQuery($pay_pid);
            }

        }elseif(isset($request['order_pid']) && $request['order_pid']!=''){
            $sql = "select b.company_id,a.mergepay_pid 
                from smc_payfee_mergeorder_mergepay as a,smc_payfee_mergeorder as b 
                where b.mergeorder_pid=a.mergeorder_pid and a.mergeorder_pid='{$order_pid}'";

            $orderOne = $this->DataControl->selectOne($sql);

            if($orderOne){
                $PayModel = new \Model\Api\MergeboingPayModel($orderOne['company_id']);
                $result = $PayModel->mergeOrderStatusQuery($orderOne['mergepay_pid']);
            }else{

                $orderPay = $this->DataControl->getFieldOne("smc_payfee_order_pay","pay_id,order_pid,pay_pid","order_pid='{$order_pid}'");
                $orderOne = $this->DataControl->getFieldOne("smc_payfee_order","company_id,order_pid,mergeorder_pid","order_pid='{$order_pid}'");

                if($orderOne['mergeorder_pid']!=''){
                    $sql = "select b.company_id,a.mergepay_pid 
                from smc_payfee_mergeorder_mergepay as a,smc_payfee_mergeorder as b 
                where b.mergeorder_pid=a.mergeorder_pid and a.mergeorder_pid='{$orderOne['mergeorder_pid']}'";

                    $orderOne = $this->DataControl->selectOne($sql);

                    if($orderOne){
                        $PayModel = new \Model\Api\MergeboingPayModel($orderOne['company_id']);
                        $result = $PayModel->mergeOrderStatusQuery($orderOne['mergepay_pid']);
                    }
                }else{
                    $PayModel = new \Model\Api\MergeboingPayModel($orderOne['company_id']);
                    $result = $PayModel->mergeOrdinaryOrderStatusQuery($pay_pid);
                }
            }

        }else{
            ajax_return(array('error' =>1, 'errortip' => '参数错误', 'result' =>array()),$request['language_type']);
        }

        if($result){
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' =>array());
        }else{
            $res = array('error' =>$PayModel->error, 'errortip' => $PayModel->errortip, 'result' =>array());
        }
        ajax_return($res,$request['language_type']);
    }

    //招行支付对接
    function OrderBakView(){

        $eventJson = $_REQUEST['biz_content'];
        $eventArray = json_decode($eventJson,1);

        //支付日志
        $bakjson = json_encode($_REQUEST,JSON_UNESCAPED_UNICODE);
        $data = array();
        $data['errorlog_apiurl'] = "MergeBoingPay/OrderBak";
        $data['errorlog_parameter'] = $bakjson;
        $data['errorlog_time'] = time();
        $this->DataControl->insertData("smc_api_errorlog",$data);

        $sql = "select b.company_id 
                from smc_payfee_mergeorder_mergepay as a,smc_payfee_mergeorder as b 
                where b.mergeorder_pid=a.mergeorder_pid and a.mergepay_pid='{$eventArray['orderId']}'";

        $orderOne = $this->DataControl->selectOne($sql);

        $data = array();
        $data['baklog_apiurl'] = "MergeBoingPay/OrderBak";
        $data['baklog_class'] = $orderOne?0:1;
        $data['orderId'] = $eventArray['orderId'];
        $data['baklog_parameter'] = $bakjson;
        $data['baklog_time'] = time();
        $this->DataControl->insertData("smc_mergeorder_baklog",$data);

        if($orderOne){
            $PayModel = new \Model\Api\MergeboingPayModel($orderOne['company_id']);
            $PayModel->BoingPayBak($_REQUEST);
            exit;
        }else{

            $sql = "select b.company_id 
                from smc_payfee_order_pay as a,smc_payfee_order as b 
                where b.order_pid=a.order_pid and a.pay_pid='{$eventArray['orderId']}'";

            $orderOne = $this->DataControl->selectOne($sql);

            $PayModel = new \Model\Api\MergeboingPayModel($orderOne['company_id']);
            $PayModel->ordinaryBoingPayBak($_REQUEST);
            exit;

        }


    }


    //已取消订单发起招行撤销操作
    function OrderPayCancelView(){
        $request = Input('get.','','trim,addslashes');
        $pay_pid = $request['paypid'];

        $sql = "select b.company_id 
                from smc_payfee_mergeorder_mergepay as a,smc_payfee_mergeorder as b 
                where b.mergeorder_pid=a.mergeorder_pid and a.mergepay_pid='{$pay_pid}'";

        $orderOne = $this->DataControl->selectOne($sql);

        if($orderOne){
            $PayModel = new \Model\Api\MergeboingPayModel($orderOne['company_id']);
            $result = $PayModel->OrderPayCancel($pay_pid);
        }else{
            $orderPay = $this->DataControl->getFieldOne("smc_payfee_order_pay","pay_id,order_pid,pay_pid","pay_pid='{$pay_pid}'");
            $orderOne = $this->DataControl->getFieldOne("smc_payfee_order","company_id,order_pid","order_pid='{$orderPay['order_pid']}'");

            $PayModel = new \Model\Api\MergeboingPayModel($orderOne['company_id']);
            $result = $PayModel->ordinaryOrderPayCancel($pay_pid);
        }

        if($PayModel->error == false){
            $res = array('error' => 1, 'errortip' => $PayModel->errortip, 'result' =>$result);
        }else{
            $res = array('error' =>$PayModel->error, 'errortip' => $PayModel->errortip, 'result' =>$result);
        }
        ajax_return($res,$request['language_type']);
    }

    function getAlipayUserid(){

        $request = Input('get.','','trim,addslashes');
        
        // 获取支付宝授权码
        $auth_code = $request['auth_code'];
        
        if(empty($auth_code)){
            $res = array('error' => 1, 'errortip' => '授权码不能为空', 'result' => array());
            ajax_return($res, $request['language_type']);
            return;
        }
        
        // 通过阿里云接口获取用户信息
        try {
            // 初始化支付宝配置
            $config = array(
                'app_id' => '2021005166665258',//支付宝的appid
                'private_key' => 'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAoTpcuJNB1a8mEQvMyhNMgeYshVFjq7oD0yuyiE84LoWC2kk+hYHguUatsOVSlcSKPgHh2CvMN9WnIT2psP4RbBdamCalAlK78a9lXjVHRAquUHeWBVUJe7zdyHInE1KG4rkIfH1PbfdsFf82lUhq3C18ULrQgLO9LIy/r81pd234oZnVki8gsNKolEHtfEsK/PYTBe9fw2byzQ++VATFlkBUEqdlZHv039/aj51V/Z00Nq2/nTurlGkKqK+ijzQ5kCb9eKEKG+njfm6BUH0WEgLCCafjbM79on7SfjEDv9Hh/dMRCRoKv4/V07Hy2hEDZ9CjZB2eUvKRZ7VwsjiVyQIDAQAB',//应用公钥
                'public_key' => 'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAt7KKXdlbAn3an/2/K1kKh548cnHBa2KslmHPz5LGEHx8kMLggi4JM6mUg193GSYmbl/QOSoQkJNhYeJbZcSgYESZwcMTe1/XY1NIcwKjpeWEl96IWHFzzT9hkRdSKmt4eJsbpe+GLkZLVxdqfgeYyNtxqSc45lQ8HL2BwqdGR0LW8yp3f1ISG7ZIAC69bJvqGm506nObJ0EeZuHPI9vf0hV2VS2enGWQNW+PF7X/C58Awdr1Z1iFkCY+WlILYrgBSSc0Oni21d+peLKLGmp4xXLSvyJLod7oftc7DT4rIgKzkJ6AEFFMdeIw0MbXMjEUYaURHu2Elry0KxFDX7PX8QIDAQAB',//支付宝公钥
                'charset' => 'UTF-8',
                'sign_type' => 'RSA2',
                'gatewayUrl' => 'https://openapi.alipay.com/gateway.do'
            );
            
            // 构建请求参数
            $params = array(
                'method' => 'alipay.system.oauth.token',
                'app_id' => $config['app_id'],
                'charset' => $config['charset'],
                'sign_type' => $config['sign_type'],
                'timestamp' => date('Y-m-d H:i:s'),
                'version' => '1.0',
                'grant_type' => 'authorization_code',
                'code' => $auth_code
            );
            
            // 生成签名
            ksort($params);
            $stringToBeSigned = '';
            foreach ($params as $k => $v) {
                $stringToBeSigned .= $k . '=' . $v . '&';
            }
            $stringToBeSigned = rtrim($stringToBeSigned, '&');
            
            openssl_sign($stringToBeSigned, $sign, $config['private_key'], OPENSSL_ALGO_SHA256);
            $params['sign'] = base64_encode($sign);
            
            // 发送请求获取access_token
            $response = $this->curlPost($config['gatewayUrl'], $params);
            $result = json_decode($response, true);
            
            if(isset($result['alipay_system_oauth_token_response']['access_token'])){
                $access_token = $result['alipay_system_oauth_token_response']['access_token'];
                
                // 使用access_token获取用户信息
                $userParams = array(
                    'method' => 'alipay.user.info.share',
                    'app_id' => $config['app_id'],
                    'charset' => $config['charset'],
                    'sign_type' => $config['sign_type'],
                    'timestamp' => date('Y-m-d H:i:s'),
                    'version' => '1.0',
                    'auth_token' => $access_token
                );
                
                // 生成用户信息请求签名
                ksort($userParams);
                $userStringToBeSigned = '';
                foreach ($userParams as $k => $v) {
                    $userStringToBeSigned .= $k . '=' . $v . '&';
                }
                $userStringToBeSigned = rtrim($userStringToBeSigned, '&');
                
                openssl_sign($userStringToBeSigned, $userSign, $config['private_key'], OPENSSL_ALGO_SHA256);
                $userParams['sign'] = base64_encode($userSign);
                
                // 发送用户信息请求
                $userResponse = $this->curlPost($config['gatewayUrl'], $userParams);
                $userResult = json_decode($userResponse, true);
                
                if(isset($userResult['alipay_user_info_share_response']['user_id'])){
                    $buyer_id = $userResult['alipay_user_info_share_response']['user_id'];
                    $res = array('error' => 0, 'errortip' => '获取成功', 'result' => array('buyer_id' => $buyer_id));
                } else {
                    $res = array('error' => 1, 'errortip' => '获取buyer_id失败', 'result' => array());
                }
            } else {
                $res = array('error' => 1, 'errortip' => '获取access_token失败', 'result' => array());
            }
            
        } catch (\Exception $e) {
            $res = array('error' => 1, 'errortip' => '接口调用异常：' . $e->getMessage(), 'result' => array());
        }
        
        ajax_return($res, $request['language_type']);

    }




}