<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2018/12/29
 * Time: 10:09
 */

namespace Work\Controller\Scshopapi;


class LoginController extends viewTpl{
    public $u;
    public $t;
    public $c;

    function __construct() {
        parent::__construct ();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
    }

    //用户获取token
    function getParentToken($params=array()){
        $parenterOne = $this->DataControl->getFieldOne("smc_parenter","parenter_id,parenter_tokencode,parenter_tokenencrypt","parenter_id='{$params['parenter_id']}'");
        if(!$parenterOne)
        {
            return false;
        }
        $md5tokenbar = base64_encode(md5($parenterOne["parenter_tokencode"].date("Y-m")));//-d
        if($md5tokenbar == $parenterOne["parenter_tokenencrypt"]){
            $token = $parenterOne["parenter_tokenencrypt"];
        }else{
            $tokencode = rand(111111,999999);
            $md5tokenbar = base64_encode(md5($tokencode.date("Y-m")));//-d
            $this->DataControl->query("UPDATE smc_parenter SET parenter_tokencode = '{$tokencode}',parenter_tokenencrypt = '{$md5tokenbar}' WHERE parenter_id ='{$parenterOne['parenter_id']}'");
            $token = $md5tokenbar;
        }
        return $token;
    }
    //账户密码登陆
    function ParentpswdloginApi(){
        $request = Input('post.','','trim,addslashes');
//        $request['L_code'] = 'kedingdang';
//        $companyOne = $this->DataControl->getFieldOne('gmc_company',"company_id","company_code = '{$request['L_code']}'");
//        if($companyOne) {
            $parenterOne = $this->DataControl->getFieldOne("smc_parenter", " * ", " parenter_mobile = '{$request['L_name']}'");
            if ($parenterOne) {
                $password = md5($request['L_pswd']);
                if ($password == $parenterOne['parenter_pass']) {
                    $iparenter = array();
                    $iparenter['parenter_id'] = $parenterOne['parenter_id'];
                    $iparenter['parenter_nickname'] = $parenterOne['parenter_nickname'];
                    $iparenter['parenter_img'] = $parenterOne['parenter_img'];
                    $iparenter['parenter_cnname'] = $parenterOne['parenter_cnname'];
                    $iparenter['parenter_enname'] = $parenterOne['parenter_enname'];
                    $iparenter['token'] = $this->getParentToken($parenterOne);
                    $this->DataControl->updateData("smc_parenter", "parenter_id = '{$parenterOne['parenter_id']}'", array("parenter_lasttime" => time(), "parenter_lastip" => real_ip()));
                    $iparenter['studentone'] = $this->DataControl->selectOne("select s.*,(SELECT e.school_id FROM smc_student_enrolled as e WHERE e.student_id = s.student_id and (e.enrolled_status = '0' or enrolled_status = '1') limit 0,1) as  school_id,vp.property_integralbalance
                                from smc_student_family as f 
                                LEFT JOIN smc_student as s ON s.student_id = f.student_id
                                left join smc_student_virtual_property as vp on vp.student_id=s.student_id
                                WHERE f.parenter_id = '{$parenterOne['parenter_id']}'  ");
                    ajax_return(array('error' => 0, 'errortip' => "登陆成功!", 'result' => $iparenter));
                } else {
                    ajax_return(array('error' => 1, 'errortip' => "密码错误!"));
                }

            } else {
                ajax_return(array('error' => 1, 'errortip' => "家长账户信息不存在!"));
            }
//        }else{
//            ajax_return(array('error' => 1,'errortip' => "企业账户不存在，请确认集团编号是否正确!"));
//        }
    }
    //手机快速登录
    function ParentmobileloginApi(){
        $request = Input('post.','','trim,addslashes');
//        $request['L_code'] = 'kedingdang';
        $companyOne = $this->DataControl->getFieldOne('gmc_company',"company_id","company_code = '{$request['L_code']}'");
        if($companyOne) {
            $parenterOne = $this->DataControl->getFieldOne("smc_parenter", " parenter_id ", " parenter_mobile = '{$request['L_mobile']}'");
            if ($parenterOne) {
                $mobile = trim($request['L_mobile']);
                $verifycode = trim($request['L_verifycode']);
                $sendrz = $this->DataControl->getFieldOne('gmc_mislog', "mislog_sendcode", "mislog_mobile='{$mobile}' and mislog_tilte = '微商城手机快速登录'", "order by mislog_time DESC");
                if (!$sendrz || $sendrz['mislog_sendcode'] !== $verifycode) {
                    $res = array('error' => '1', 'errortip' => '短信验证码错误!');
                    ajax_return($res);
                } else {
                    $iparenter = array();
                    $iparenter['parenter_id'] = $parenterOne['parenter_id'];
                    $iparenter['parenter_nickname'] = $parenterOne['parenter_nickname'];
                    $iparenter['parenter_img'] = $parenterOne['parenter_img'];
                    $iparenter['parenter_cnname'] = $parenterOne['parenter_cnname'];
                    $iparenter['parenter_enname'] = $parenterOne['parenter_enname'];
                    $iparenter['token'] = $this->getParentToken($parenterOne);
                    $this->DataControl->updateData("smc_parenter", "parenter_id = '{$parenterOne['parenter_id']}'", array("parenter_lasttime" => time(), "parenter_lastip" => real_ip()));
                    $iparenter['studentone'] = $this->DataControl->selectOne("select s.*,(SELECT e.school_id FROM smc_student_enrolled as e WHERE e.student_id = s.student_id and (e.enrolled_status = '0' or enrolled_status = '1') limit 0,1) as  school_id,vp.property_integralbalance
                                     from smc_student_family as f 
                                     LEFT JOIN smc_student as s ON s.student_id = f.student_id
                                     left join smc_student_virtual_property as vp on vp.student_id=s.student_id
                                     WHERE f.parenter_id = '{$parenterOne['parenter_id']}' ");
                    ajax_return(array('error' => 0, 'errortip' => "登陆成功!", 'result' => $iparenter));
                }
            } else {
                ajax_return(array('error' => 1, 'errortip' => "家长账户信息不存在!"));
            }
        }else{
            ajax_return(array('error' => 1,'errortip' => "企业账户不存在，请确认集团编号是否正确!"));
        }
    }
    //获取手机验证码
    function getParentverifycodeApi()
    {
        $request = Input('get.','','trim,addslashes');
//        $request['L_code'] = 'kedingdang';
        $companyOne = $this->DataControl->getFieldOne('gmc_company',"company_id","company_code = '{$request['L_code']}'");
        if($companyOne) {
            $parenterOne = $this->DataControl->getFieldOne("smc_parenter", " parenter_id ", " parenter_mobile = '{$request['L_mobile']}'");
            if (!$parenterOne) {
                $res = array('error' => '1', 'errortip' => '家长账户信息不存在!');
                ajax_return($res);
            } else {
                $mobile = trim($request['L_mobile']);
                //一小时内发送次数
                $mintime = time() - 3600;
                $mislognum = $this->DataControl->selectOne("select count(mislog_id) as mislognum from gmc_mislog where mislog_mobile='{$mobile}' and mislog_tilte = '微商城手机快速登录' and mislog_time >= '{$mintime}' limit 0,1 ");
                if ($mislognum['mislognum'] > 5) {
                    $res = array('error' => '1', 'errortip' => '您的手机已超出发送验证码次数，请联系客服！');
                    ajax_return($res);
                }
                //最近一次发送时间
                $sendmisrz = $this->DataControl->getFieldOne('gmc_mislog', "mislog_time", "mislog_mobile='{$mobile}' and mislog_tilte = '微商城手机快速登录'", "order by mislog_time DESC");
                if ($sendmisrz && (time() - $sendmisrz['mislog_time']) < 60) {
                    $res = array('error' => '1', 'errortip' => '验证码已发送！');
                    ajax_return($res);
                } else {
                    $tilte = "微商城手机快速登录";
                    $sendcode = rand(111111, 999999);
                    setcookie('mislog_sendcode', $sendcode, time() + 1800);
                    $contxt = "用户您好！您的手机验证码为{$sendcode}，请完成验证，如非本人操作，请忽略本短信。";
                    //短信发送
                    if ($this->Sendmisgocom($mobile, $contxt, $tilte, $sendcode,$companyOne['company_id'])) {
                        $res = array('error' => '0', 'errortip' => '发送成功', "bakfuntion" => "okmotify");
                        ajax_return($res);
                    } else {
                        $res = array('error' => '1', 'errortip' => '发送失败!', "bakfuntion" => "errormotify");
                        ajax_return($res);
                    }
                }
            }
        }else{
            ajax_return(array('error' => 1,'errortip' => "企业账户不存在，请确认集团编号是否正确!"));
        }
    }

}
