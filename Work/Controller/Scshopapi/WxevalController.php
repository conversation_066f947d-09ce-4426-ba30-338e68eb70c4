<?php
/**
 * ============================================================================
 * 版权所有 : https://www.mohism.cn
 * 网站地址 : https://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/3/23
 * Time: 0:37
 */

namespace Work\Controller\Scshopapi;

class WxevalController extends viewTpl
{
    public $u;
    public $t;
    public $c;

//    //吉的堡教育
//    const appId = 'wx7378534679eeecdf';
//    const appSecret = '6705d7c1845f58e74dfe6aea7c1d500c';
    //叮铛助学
    const appId = 'wxff9e04b9a16746c4';
    const appSecret = '26356916bcc36a6dbe37ed06bd96a628';

    const nonceStr = 'kidcastle';
    const sha1Tool = 'Core/Tools/Eval/sig.py';
    const resultTool = 'Core/Tools/Eval/result.py';
    const evalAddress = 'https://cn-shanghai.aliyun.webginger.cloud.ssapi.cn/';

    static $wxToken = 'https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=%s&secret=%s';
    static $wxTicket = 'https://api.weixin.qq.com/cgi-bin/ticket/getticket?access_token=%s&type=jsapi';
    static $wxSig = 'jsapi_ticket=%s&noncestr=%s&timestamp=%s&url=%s';
    static $wxAudio = 'https://api.weixin.qq.com/cgi-bin/media/get/jssdk?access_token=%s&media_id=%s';

    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
    }

    //获取微信反馈数据
    public function getWeixinToken($refresh=0){
        $wxchatOne = $this->DataControl->getFieldOne("gmc_company_wxchatnumber","wxchatnumber_id,wxchatnumber_appid,wxchatnumber_appsecret"
            ,"company_id = '0' AND wxchatnumber_class = '1'");
        if($refresh == 0){
            $tokenOne = $this->DataControl->getFieldOne("scptc_weixin_token","token_failuretime,token_string"
                ,"token_type = '1' and wxchatnumber_id = '{$wxchatOne['wxchatnumber_id']}'","order by token_failuretime DESC limit 0,1");
            if($tokenOne && $tokenOne['token_failuretime'] > time()){
                return $tokenOne['token_string'];
            }else{
                $paramarray = array(
                    'appid' => $wxchatOne['wxchatnumber_appid'],
                    'secret' => $wxchatOne['wxchatnumber_appsecret'],
                    'grant_type' => "client_credential"
                );
                $getBakurl = request_by_curl("https://api.weixin.qq.com/cgi-bin/token", dataEncode($paramarray),"GET");
                $json_play = new \Webjson();
                $cardarray = $json_play->decode($getBakurl,"1");
                $data = array();
                $data['token_type'] = '1';
                $data['wxchatnumber_id'] = $wxchatOne['wxchatnumber_id'];
                $data['token_string'] = $cardarray['access_token'];
                $data['token_failuretime'] = time() + $cardarray['expires_in'];
                $this->DataControl->insertData("scptc_weixin_token",$data);
                return $cardarray['access_token'];
            }
        }else{
            $paramarray = array(
                'appid' => $wxchatOne['wxchatnumber_appid'],
                'secret' => $wxchatOne['wxchatnumber_appsecret'],
                'grant_type' => "client_credential"
            );
            $getBakurl = request_by_curl("https://api.weixin.qq.com/cgi-bin/token", dataEncode($paramarray),"GET");
            $json_play = new \Webjson();
            $cardarray = $json_play->decode($getBakurl,"1");
            $data = array();
            $data['token_type'] = '1';
            $data['wxchatnumber_id'] = $wxchatOne['wxchatnumber_id'];
            $data['token_string'] = $cardarray['access_token'];
            $data['token_failuretime'] = time() + $cardarray['expires_in'];
            $this->DataControl->insertData("scptc_weixin_token",$data);
            return $cardarray['access_token'];
        }
    }

    //获取微信签名
    public function getSigView(){
        $result = array();
        $timestamp = time();
        $ticket = $this->getTicketView();
        $url = $_POST['url'];
        $wxchatOne = $this->DataControl->getFieldOne("gmc_company_wxchatnumber","wxchatnumber_id,wxchatnumber_appid,wxchatnumber_appsecret"
            ,"company_id = '0' AND wxchatnumber_class = '1'");

        $s = sha1(sprintf(self::$wxSig, $ticket['ticket'], self::nonceStr, $timestamp, $url));
        $result['signature'] = $s;
        $result['nonceStr'] = self::nonceStr;
        $result['timestamp'] = $timestamp;
        $result['appId'] = $wxchatOne['wxchatnumber_appid'];
        $result['token'] = $ticket['token'];
        echo json_encode($result);
    }

    //获取微信ticket
    public function getTicketView(){
        $result = array();
        $at = $this->getWeixinToken();
        $wxchatOne = $this->DataControl->getFieldOne("gmc_company_wxchatnumber","wxchatnumber_id,wxchatnumber_appid,wxchatnumber_appsecret"
            ,"company_id = '0' AND wxchatnumber_class = '1'");

        $tokenOne = $this->DataControl->getFieldOne("scptc_weixin_token","token_failuretime,token_string"
            ,"token_type = '2' and wxchatnumber_id = '{$wxchatOne['wxchatnumber_id']}'","order by token_failuretime DESC limit 0,1");//and token_site ='4'
        if($tokenOne && $tokenOne['token_failuretime'] > time()){
            $result['token'] = $at;
            $result['ticket'] = $tokenOne['token_string'];
            return $result;
        }else{
            $tktUrl = sprintf(self::$wxTicket, $at);
            $ticketOne = json_decode(file_get_contents($tktUrl), true);
            if(isset($ticketOne['errcode']) && $ticketOne['errcode'] !== 0){
                $at = $this->getWeixinToken(1);
                $tktUrl = sprintf(self::$wxTicket, $at);
                $ticketOne = json_decode(file_get_contents($tktUrl), true);
                $data = array();
                $data['token_type'] = '2';
                $data['wxchatnumber_id'] = $wxchatOne['wxchatnumber_id'];
                $data['token_string'] = $ticketOne['ticket'];
                $data['token_failuretime'] = time() + $ticketOne['expires_in'];
                $this->DataControl->insertData("scptc_weixin_token",$data);
            }else{
                $data = array();
                $data['token_type'] = '2';
                $data['wxchatnumber_id'] = $wxchatOne['wxchatnumber_id'];
                $data['token_string'] = $ticketOne['ticket'];
                $data['token_failuretime'] = time() + $ticketOne['expires_in'];
                $this->DataControl->insertData("scptc_weixin_token",$data);
            }

            /**/
            $result['token'] = $at;
            $result['ticket'] = $ticketOne['ticket'];
            return $result;
        }
    }

    //微信授权信息获取 -- 97  -- 微信授权登录信息
    function wxAuthorizationView(){
        $request = Input('post.','','strip_tags,trim');
        $wxchatOne = $this->DataControl->getFieldOne("gmc_company_wxchatnumber","wxchatnumber_id,wxchatnumber_appid,wxchatnumber_appsecret"
            ,"company_id = '0' AND wxchatnumber_class = '1'");
        $paramarray = array(
            'appid' => $wxchatOne['wxchatnumber_appid'],
            'secret' => $wxchatOne['wxchatnumber_appsecret'],
            'js_code' => $request['code'],
            'grant_type' => "authorization_code"
        );

        $getBakurl = request_by_curl("https://api.weixin.qq.com/sns/jscode2session", dataEncode($paramarray),"GET");

        $json_play = new \Webjson();
        $dataArray = $json_play->decode($getBakurl,"1");

        $result["error"] = "0";
        $result["errortip"] = '用户信息存在!';
        $result["result"] = $dataArray;
        ajax_return($result);
    }

    //微信授权信息获取 -- 97
    function wxAuthorMobileView(){
        $request = Input('post.','','strip_tags,trim');
        /**
         * sessionKey/encryptedData/iv参数均从url中获取，并赋给相应变量
         */
        $wxchatOne = $this->DataControl->getFieldOne("gmc_company_wxchatnumber","wxchatnumber_id,wxchatnumber_appid,wxchatnumber_appsecret"
            ,"company_id = '0' AND wxchatnumber_class = '1'");
        $sessionKey = $request['session_key'];
        $encryptedData = $request['encryptedData'];
        $iv = $request['iv'];

        $pc = new \WXBizDataCrypt($wxchatOne['wxchatnumber_appid'], $sessionKey);
        $errCode = $pc->decryptData($encryptedData, $iv, $data );

        if ($errCode == 0) {
            $res = array('error' => 0,'errortip' => "正确!","listjson"=>$data,"tokeninc"=>"1");
            ajax_return($res,$request['language_type']);

        } else {
            $res = array('error' => 1,'errortip' => "错误!","listjson"=>$errCode,"tokeninc"=>"1");
            ajax_return($res,$request['language_type']);
        }
    }

    function __destruct()
    {
        exit;
    }

}
