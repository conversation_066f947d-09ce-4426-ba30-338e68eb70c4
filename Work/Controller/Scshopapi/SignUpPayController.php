<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/4/17
 * Time: 10:51
 */

namespace Work\Controller\Scshopapi;


class SignUpPayController extends viewTpl{
    public $u;
    public $t;
    public $c;
    public $visitType="api";
    public $DataControl;

    //预加载处理类
    function __construct() {
        parent::__construct ();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
        $this->api = $this->router->getApi();

        //数据库操作
        $this->DataControl = new \Dbmysql();
    }

    //获取学校对应的集团信息
    function getSchoolCompanyView(){
        $request = Input('get.','','trim,addslashes');

        $schoolOne = $this->DataControl->selectOne("select s.school_id,s.school_branch,s.school_shortname,s.school_cnname,c.company_id,c.company_code,c.company_cnname,c.company_shortname 
                    from smc_school as s 
                    LEFT JOIN gmc_company as c ON s.company_id=c.company_id
                    where s.school_branch = '{$request['school_branch']}' limit 0,1");

        $result = array();
        $result['school_id'] = $schoolOne['school_id'];
        $result['school_branch'] = $schoolOne['school_branch'];
        $result['school_shortname'] = $schoolOne['school_shortname'];
        $result['school_cnname'] = $schoolOne['school_cnname'];
        $result['company_id'] = $schoolOne['company_id'];
        $result['company_code'] = $schoolOne['company_code'];
        $result['company_cnname'] = $schoolOne['company_cnname'];
        $result['company_shortname'] = $schoolOne['company_shortname'];

        if($result){
            $res = array('error' => '0', 'errortip' => '学校数据获取成功', 'result' => $result);
        }else{
            $res = array('error' => '1', 'errortip' => '学校数据获取失败', 'result' => array());
        }
        ajax_return($res);
    }

    //校务学员提交报名
    //222->生成充值订单 ---  https://scshopapi.kedingdang.com/Order/createRechargeOrderAction

    //CRM学员提交报名
    function registerCrmokAction(){
        $request = Input('post.','','trim,addslashes');

        if(!isset($request['student_cnname']) || $request['student_cnname'] == ''){
            ajax_return(array('error' => 1, 'errortip' => "学生中文名必填!", "bakfuntion" => 'errormotify'));
        }

        $smcData = array();
        $smcData['school_branch'] = $request['school_branch'];
        $smcData['student_cnname'] = $request['student_cnname'];
        $smcData['student_enname'] = $request['student_enname'];
        $smcData['family_mobile'] = $request['family_mobile'];
        $smcData['client_id'] = $request['client_id'];  //有效名单编号
        $smcData['student_sex'] = $request['client_sex'];
        $smcData['student_idcard'] = $request['client_icard'];
        $smcData['student_img'] = $request['client_img'];
        $smcData['student_birthday'] = $request['client_birthday'];
        $smcData['family_relation'] = $request['family_relation'];
        $smcData['family_cnname'] = $request['parenter_cnname'];
        $smcData['is_skip'] = 1;

        $scmapijsting = request_by_curl("http://api.kcclassin.com/Smc/addCRMStuAction", dataEncode($smcData), "POST", array());

        $json_play = new \Webjson();
        $scmapiArray = $json_play->decode($scmapijsting,"1");
        if($scmapiArray['error'] == '1'){
            ajax_return(array('error' => 1, 'errortip' => $scmapiArray['errortip'], "bakfuntion" => 'errormotify'));
        }else{
            $smcData = array();
            $smcData['school_branch'] = $request['school_branch'];
            $smcData['student_branch'] = $scmapiArray['result']['list']['student_branch'];
            $smcData['mobile'] = $request['family_mobile'];
            $smcData['price'] = $request['price'];
            $smcData['companies_id'] = $request['companies_id'];
            $smcData['is_skip'] = '1';
            $order = request_by_curl("https://scshopapi.kedingdang.com/Order/createRechargeOrderAction", dataEncode($smcData), "GET", array());
            $json_play = new \Webjson();
            $cardarray = $json_play->decode($order,"1");
            if($cardarray['error'] == '1'){
                ajax_return(array('error' => 1, 'errortip' => '提交失败', 'result' => array()));
            }else {
                ajax_return(array('error' => 0, 'errortip' => '提交成功', 'result' => $cardarray));
            }
        }
    }


}