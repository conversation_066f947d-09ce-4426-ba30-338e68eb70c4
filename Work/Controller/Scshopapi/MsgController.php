<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2018/12/29
 * Time: 10:09
 */

namespace Work\Controller\Scshopapi;


class MsgController extends viewTpl
{
    public function tokenView(){

        $token = 'yikeyixiao2022';

        $signature = $_GET["signature"];
        $timestamp = $_GET["timestamp"];
        $nonce     = $_GET["nonce"];
        $str       = $_GET["echostr"];

        $tmpArr = array($token, $timestamp, $nonce); sort($tmpArr);
        $tmpStr = implode( $tmpArr );
        $tmpStr = sha1( $tmpStr );

        if( $tmpStr == $signature ){
            $result = $str;
        }else{
            $result = $signature.':signature';
        }

        foreach ($_GET as $key => $value) {
            $a = $a."\r\n ".$key.':'.$value.' ;';
        }
        echo $result;
    }

}
