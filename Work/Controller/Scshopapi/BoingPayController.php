<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/4/27
 * Time: 17:49
 */

namespace Work\Controller\Scshopapi;


class BoingPayController extends viewTpl{
    public $u;
    public $t;
    public $c;
    public $errortip = false;

    function __construct() {
        parent::__construct ();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
    }

    //招行发起支付
    function OrderPayView(){
        $request = Input('get.','','trim,addslashes');
        $paymenttype = $request['paymenttype'];
        $pay_pid = $request['paypid'];

        $orderPay = $this->DataControl->getFieldOne("smc_payfee_order_pay","pay_id,order_pid,pay_pid","pay_pid='{$pay_pid}'");
        $orderOne = $this->DataControl->getFieldOne("smc_payfee_order","company_id,order_pid","order_pid='{$orderPay['order_pid']}'");
        $PayModel = new \Model\Scshop\BoingPayModel($orderOne['company_id']);
        $result = $PayModel->OrderPay($pay_pid,$paymenttype,$request['openid']);

        if($PayModel->error){
            $res = array('error' => 1, 'errortip' => $PayModel->errortip, 'result' =>$result);
        }else{
            $res = array('error' =>$PayModel->error, 'errortip' => $PayModel->errortip, 'result' =>$result);
        }
        ajax_return($res,$request['language_type']);
    }

    //招行支付对接
    function OrderBakView(){
        $eventJson = $_REQUEST['event'];
        $eventArray = json_decode($eventJson,1);

        //支付日志
        $bakjson = json_encode($_REQUEST,JSON_UNESCAPED_UNICODE);
        $data = array();
        $data['errorlog_apiurl'] = "BoingPay/OrderBak";
        $data['errorlog_parameter'] = $bakjson;
        $this->DataControl->insertData("smc_api_errorlog",$data);

        $orderPay = $this->DataControl->getFieldOne("smc_payfee_order_pay","pay_id,order_pid,pay_pid","pay_pid='{$eventArray['out_order_no']}'");
        $orderOne = $this->DataControl->getFieldOne("smc_payfee_order","company_id,order_pid","order_pid='{$orderPay['order_pid']}'");
        $PayModel = new \Model\Scshop\BoingPayModel($orderOne['company_id']);
        $PayModel->BoingPayBak($_REQUEST);
        exit;
    }

    //衫德支付对接
    function OrderSdposBakApi(){
        $parameter = Input('get.','','trim,addslashes');
        $bakjson = json_encode($parameter,JSON_UNESCAPED_UNICODE);
        $data = array();
        $data['errorlog_apiurl'] = "BoingPay/OrderSdposBak";
        $data['errorlog_parameter'] = $bakjson;
        $this->DataControl->insertData("smc_api_errorlog",$data);

        $orderPay = $this->DataControl->getFieldOne("smc_payfee_order_pay","pay_id,order_pid,pay_pid,pay_issuccess","pay_pid='{$parameter['orderNo']}'");
        $orderOne = $this->DataControl->getFieldOne("smc_payfee_order","company_id,school_id,order_pid","order_pid='{$orderPay['order_pid']}'");
        $stafferOne = $this->DataControl->getFieldOne("smc_staffer","staffer_id","company_id='{$orderOne['company_id']}' AND account_class = '1'");
        if($orderPay && $orderPay['pay_issuccess'] !== '1'){
            $publiclist = array();
            $publiclist['company_id'] = $orderOne['company_id'];
            $publiclist['school_id'] = $orderOne['school_id'];
            $publiclist['staffer_id'] = $stafferOne['staffer_id'];
            $orderPayModel = new \Model\Smc\OrderPayModel($publiclist, $orderOne['order_pid']);

            $createtime = date("Y-m-d H:i:s",strtotime($parameter['trsDate']." ".$parameter['trsTime']));
            $paytype_code = 'pos';
            $paynote = "衫德POS{$parameter['issNO']}支付，杉德POS终端号：{$parameter['terminalID']}交易流水号：{$parameter['voucherNo']},实际支付金额：{$parameter['orderAmount']},支付时间：{$createtime}";
            $orderPay = $orderPayModel->orderPaylog($orderPay['pay_pid'],$parameter['voucherNo'],0,$paynote,$paytype_code,$createtime,'0',$bakjson,'','sdpos');
            if(!$orderPay){
                echo $orderPayModel->errortip;
            }
            exit('1');
        }else{
            exit('0');
        }
    }

    function creatPid(){
        $Str = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        $Str_num="1234567890";
        $date=date("Ymd",time());
        $randStr= $Str_num[rand(0,9)].$Str_num[rand(0,9)].$Str_num[rand(0,9)].$Str_num[rand(0,9)].$Str_num[rand(0,9)].$Str[rand(0,25)].$Str[rand(0,25)].$Str[rand(0,25)].$Str[rand(0,25)].$Str[rand(0,25)].$Str[rand(0,25)].substr($date,2).$Str[rand(0,25)].$Str[rand(0,25)].$Str[rand(0,25)].$Str[rand(0,25)].$Str[rand(0,25)].$Str[rand(0,25)];
        return $randStr;
    }

    function RefundSdBakApi(){
        $request = Input('get.','','trim,addslashes');
        if(!isset($request['orderNo']) || $request['orderNo']==''){
            $this->error = 1;
            $this->errortip = "支付编号必须传";
            ajax_return(array('error' => 1,'errortip' => $this->errortip));
        }

        $sql="select pop.*,po.student_id,po.school_id,po.company_id,po.order_paymentprice
              from smc_payfee_order_pay as pop
              left join smc_payfee_order as po on po.order_pid=pop.order_pid
              where pop.pay_pid='{$request['orderNo']}' and pop.pay_issuccess='1' and pop.pay_isrefund='0' and po.order_status='4'";
        $orderOne=$this->DataControl->selectOne($sql);
        if(!$orderOne){
            $this->error = 1;
            $this->errortip = "未查询到可退款单号";
            ajax_return(array('error' => 1,'errortip' => $this->errortip));
        }

        if($orderOne['paytype_code']!='pos'){
            $this->error = 1;
            $this->errortip = "非pos支付,不可当日退款";
            ajax_return(array('error' => 1,'errortip' => $this->errortip));
        }

        $stafferOne = $this->DataControl->getFieldOne("smc_staffer","staffer_id","company_id='{$orderOne['company_id']}' AND account_class = '1'");
        $publicarray = array();
        $publicarray['school_id'] = $orderOne['school_id'];
        $publicarray['company_id'] = $orderOne['company_id'];
        $publicarray['staffer_id'] = $stafferOne['staffer_id'];

        if($orderOne['order_paymentprice']!=$orderOne['pay_price']){
            $this->error = 1;
            $this->errortip = "分批支付暂不支持当日退款";
            ajax_return(array('error' => 1,'errortip' => $this->errortip));
        }
        $OrderModel = new \Model\Smc\OrderModel($publicarray, $orderOne['order_pid']);
        $bool=$OrderModel->checkConsume();
        if(!$bool){
            $this->error = 1;
            $this->errortip = $OrderModel->errortip;
            ajax_return(array('error' => 1,'errortip' => $this->errortip));
        }

        $RefundModel = new \Model\Smc\RefundModel($publicarray);

        $sql="select p.parenter_mobile,p.parenter_cnname
              from smc_student_family as sf
              left join smc_parenter as p on p.parenter_id=sf.parenter_id
              where sf.student_id='{$orderOne['student_id']}' and sf.family_isdefault=1";

        $memberOne=$this->DataControl->selectOne($sql);

        $refundOne=$this->DataControl->getFieldOne("smc_refund_order","refund_id,refund_pid","from_order_pid='{$orderOne['order_pid']}'");
        if($refundOne){
            $this->error = 1;
            $this->errortip = "该订单已存在退款";
            ajax_return(array('error' => 1,'errortip' => $this->errortip));
        }else{
            do{
                $order_pid = $this->creatPid();
            }while($this->DataControl->selectOne("select refund_pid from smc_refund_order where refund_pid='{$order_pid}' limit 0,1"));

            $trading_pid=$RefundModel->stuTrading($orderOne['student_id'],$orderOne['companies_id'],'Accountrefund');

            $data=array();
            $data['from_order_pid'] = $orderOne['order_pid'];
            $data['company_id'] = $orderOne['company_id'];
            $data['school_id'] = $orderOne['school_id'];
            $data['companies_id'] = $orderOne['companies_id'];
            $data['student_id'] = $orderOne['student_id'];
            $data['trading_pid'] =$trading_pid;
            $data['refund_pid'] = $order_pid;
            $data['staffer_id'] = $stafferOne['staffer_id'];
            $data['refund_name'] = $memberOne['parenter_cnname'];
            $data['refund_mobile'] = $memberOne['parenter_mobile'];
            $data['refund_reason'] = 'POS机当日退款，提交退款申请机制';
            $data['refund_price'] = $orderOne['pay_price'];
            $data['refund_payprice'] = $orderOne['pay_price'];
            $data['refund_from'] = 1;
            $data['refund_status']=4;
            $data['refund_type']=1;
            $data['refund_class']=1;
            $data['refund_createtime'] = time();
            if($this->DataControl->insertData("smc_refund_order",$data)){
                $RefundModel->refundOrderTracks($order_pid,'申请退款中','已同意您的退款,确定金额中！',"当日退款");
                $RefundModel->refundOrderTracks($order_pid,'订单退款！','订单退款成功,退款交易号'.$request['outnumber'].'！',"当日退款");

                $tradeData = array();
                $tradeData['refund_pid'] = $order_pid;
                $tradeData['pay_pid'] = $orderOne['pay_pid'];
                $tradeData['pay_outnumber'] = $request['outnumber'];
                $tradeData['trade_price'] = $orderOne['pay_price'];
                $tradeData['trade_outnumber'] = $request['outnumber'];
                $tradeData['trade_successtime'] =time();
                $tradeData['trade_createtime'] =time();
                $tradeData['trade_note'] ="当日退款";
                $this->DataControl->insertData('smc_refund_order_trade' , $tradeData);

                $tradelogData = array();
                $tradelogData['refund_pid'] = $order_pid;
                $tradelogData['tradelog_outnumber'] = $request['outnumber'];
                $tradelogData['tradelog_price'] = $orderOne['pay_price'];
                $tradelogData['tradelog_successtime'] = time();
                $tradelogData['tradelog_note'] = "当日退款";
                $tradelogData['tradelog_createtime'] = time();
                $this->DataControl->insertData('smc_refund_order_tradelog' , $tradelogData);

                $data=array();
                $data['trading_status']=1;
                $data['trading_updatatime']=time();
                $this->DataControl->updateData("smc_student_trading","trading_pid ='{$trading_pid}'",$data );

                $data = array();
                $data['pay_isrefund'] = 1;
                $data['pay_issuccess'] = -1;
                $this->DataControl->updateData("smc_payfee_order_pay","pay_pid ='{$orderOne['pay_pid']}' and  order_pid ='{$orderOne['order_pid']}'",$data );

                $OrderHandleModel = new \Model\Smc\OrderHandleModel($publicarray, $orderOne['order_pid']);
                $OrderHandleModel->orderProcess();

                $data = array();
                $data['order_status'] = '-1';
                $data['order_updatatime'] = time();

                $this->DataControl->updateData("smc_payfee_order", "order_pid='{$orderOne['order_pid']}'", $data);

                $OrderHandleModel->orderTracks('取消订单', '取消订单');

                $track_data = array();
                $track_data['trading_status'] = '-1';
                $track_data['trading_updatatime'] = time();
                $this->DataControl->updateData("smc_student_trading", "trading_pid='{$trading_pid}'",$track_data);

                $this->error = true;
                $this->errortip = "退款成功";
                ajax_return(array('error' => 0,'errortip' => $this->errortip));
            }else{
                $this->error = true;
                ajax_return(array('error' => 1,'errortip' => $this->errortip));
            }
        }
    }

    //已取消订单发起招行撤销操作
    function OrderPayCancelView(){
        $request = Input('get.','','trim,addslashes');
        $paymenttype = $request['paymenttype'];
        $pay_pid = $request['paypid'];

        $orderPay = $this->DataControl->getFieldOne("smc_payfee_order_pay","pay_id,order_pid,pay_pid,pay_issuccess","pay_pid='{$pay_pid}'");
        $orderOne = $this->DataControl->getFieldOne("smc_payfee_order","company_id,order_pid,order_status","order_pid='{$orderPay['order_pid']}'");

        if($orderPay['pay_issuccess'] != '-1'){
            $res = array('error' => 1, 'errortip' => "支付订单并未失效！", 'result' =>array());
            ajax_return($res,$request['language_type']);
        }

        $PayModel = new \Model\Scshop\BoingPayModel($orderOne['company_id']);
        $result = $PayModel->OrderPayCancel($pay_pid);

        if($PayModel->error == false){
            $res = array('error' => 1, 'errortip' => $PayModel->errortip, 'result' =>$result);
        }else{
            $res = array('error' =>$PayModel->error, 'errortip' => $PayModel->errortip, 'result' =>$result);
        }
        ajax_return($res,$request['language_type']);
    }


    //墨智反馈专用
    function MohismBakView(){
        $request = Input('get.','','trim,addslashes');
        print_r($request);
        $orderPay = $this->DataControl->getFieldOne("smc_payfee_order_pay","pay_id,order_pid,pay_pid","pay_pid='{$request['pid']}'");
        $orderOne = $this->DataControl->getFieldOne("smc_payfee_order","company_id,order_pid","order_pid='{$orderPay['order_pid']}'");
        $PayModel = new \Model\Scshop\BoingPayModel($orderOne['company_id']);
        $payArray = array();
        $payArray['pid'] = $orderPay['pay_pid'];
        $result = $PayModel->MohismPayBak($payArray);
        print_r($result);
        die;
    }
}