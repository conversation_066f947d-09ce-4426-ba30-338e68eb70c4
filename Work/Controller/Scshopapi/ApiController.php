<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/13
 * Time: 23:25
 */

namespace Work\Controller\Scshopapi;


class ApiController extends viewTpl{
    public $u;
    public $t;
    public $c;
    public $visitType="api";
    public $aeskey = 'kedingdang%C4567';

    //预加载处理类
    function __construct() {
        parent::__construct ();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
        $this->api = $this->router->getApi();
    }

    //跑数据的接口 学校对应的首字母数据处理 ----  有些数据跑不出来
    /*function schoolnametofirstcharView(){
        $schoollist = $this->DataControl->selectClear("select school_id,school_cnname from smc_school ");
        if($schoollist){
            foreach ($schoollist as $schoolvar){
                $data = array();
                $data['school_cnname_initial'] = getfirstchar($schoolvar['school_cnname']);//活动招生标题
                $this->DataControl->updateData("smc_school","school_id = '{$schoolvar['school_id']}'  ",$data);
            }
        }

    $paramarray = array(
		'output' => "json",
		'address' => $request['data'],
		'key' => "6760888f01d2996303c3c3ef3919da5c"
	);

	$getMapurl = request_by_curl("http://restapi.amap.com/v3/geocode/geo", dataEncode($paramarray),"GET");
	$json_play = new JSON();
	$PointsJson = $json_play->decode($getMapurl,"1");

	if($PointsJson['status'] == '1'){
		$res['status'] = 1;
		$res['message'] = "获取成功";
		$location = explode(",",$PointsJson['geocodes']['0']['location']);
		$res['longitude'] = $location['0'];
		$res['latitude'] = $location['1'];
	}else{
		$res['status'] = 0;
		$res['message'] = "获取失败";
	}
    }*/

    function loadSchoolLongLatView(){
        $request = Input('get.','','trim,addslashes');
        $PublicModel = new \Model\Scshop\PublicModel();
        $dataOne = $PublicModel->loadSchoolLongLat($request);
        if($dataOne){
            $res = array('error' => '0', 'errortip' => '学校经纬度信息获取成功', 'result' => $dataOne);
        }else{
            $res = array('error' => '1', 'errortip' => '学校经纬度信息获取失败', 'result' => array());
        }
        ajax_return($res);
    }


    //获取集团信息
    function getCompanyOneView(){
        $request = Input('get.','','trim,addslashes');
        $PublicModel = new \Model\Scshop\PublicModel();
        $CompanyOne = $PublicModel->getCompanyOne($request);
        if($CompanyOne){
            $res = array('error' => '0', 'errortip' => '集团信息获取成功', 'result' => $CompanyOne);
        }else{
            $res = array('error' => '1', 'errortip' => '集团信息获取失败', 'result' => array());
        }
        ajax_return($res);
    }

    /*//获取 全部 省市区
    function getProCityAreasView()
    {
        $request = Input('get.', '', 'trim,addslashes,strip_tags');
        $PublicModel = new \Model\Scshop\PublicModel();
        $Province = $PublicModel->getProCityAreas($request);
        if($Province){
            $result["data"] = array();
            $res = array('error' => '0', 'errortip' => '省市区获取成功', 'result' => $Province);
        }else{
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '省市区获取失败', 'result' => array());
        }
        ajax_return($res);
    }*/

    /*//获取 全部学校所  省市区
    function getSchoolProCityAreasView()
    {
        $request = Input('get.', '', 'trim,addslashes,strip_tags');

        $Province = $this->DataControl->selectClear("SELECT r.region_id,r.region_name 
                    from smc_code_region as r 
                    LEFT JOIN smc_school as s ON s.school_province = r.region_id
                    WHERE r.parent_id = 1 and s.school_id <> ''
                    GROUP BY r.region_id");
        if($Province){
            foreach($Province as &$Provincevar){
                $City = $this->DataControl->selectClear("SELECT r.region_id,r.region_name 
                        from smc_code_region as r 
                        LEFT JOIN smc_school as s ON s.school_city = r.region_id
                        WHERE r.parent_id = '{$Provincevar['region_id']}'  and s.school_id <> ''
                        GROUP BY r.region_id");
                $Provincevar['children'] = $City;
                if($Provincevar['children']){
                    foreach($Provincevar['children'] as &$Cityvar){
                        $Area = $this->DataControl->selectClear("SELECT r.region_id,r.region_name
                                from smc_code_region as r  
                                LEFT JOIN smc_school as s ON s.school_area = r.region_id
                                WHERE r.parent_id = '{$Cityvar['region_id']}'  and s.school_id <> ''
                                GROUP BY r.region_id");
                        $Cityvar['children'] = $Area;
                    }
                }
            }
        }
        if($Province){
            $result["data"] = array();
            $res = array('error' => '0', 'errortip' => '省市区获取成功', 'result' => $Province);
        }else{
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '省市区获取失败', 'result' => array());
        }
        ajax_return($res);
    }*/

    //获取学校省
    function getSchoolProvinceView()
    {
        $request = Input('get.', '', 'trim,addslashes,strip_tags');
        $PublicModel = new \Model\Scshop\PublicModel();
        $Province = $PublicModel->getSchoolProvince($request);
        if ($Province) {
            $res = array('error' => '0', 'errortip' => '省份数据获取成功', 'result' => $Province);
        } else {
            $res = array('error' => '1', 'errortip' => '省份数据获取失败', 'result' => array());
        }
        ajax_return($res);
    }

    //获取学校城市
    function getSchoolCityView()
    {
        $request = Input('get.', '', 'trim,addslashes,strip_tags');
        $PublicModel = new \Model\Scshop\PublicModel();
        $City = $PublicModel->getSchoolCity($request);
        if ($City) {
            $res = array('error' => '0', 'errortip' => '城市数据获取成功', 'result' => $City);
        } else {
            $res = array('error' => '1', 'errortip' => '城市数据获取失败', 'result' => array());
        }
        ajax_return($res);
    }

    //获取学校区域
    function getSchoolAreasView()
    {
        $request = Input('get.', '', 'trim,addslashes,strip_tags');
        $PublicModel = new \Model\Scshop\PublicModel();
        $Areas = $PublicModel->getSchoolAreas($request);
        if ($Areas) {
            $res = array('error' => '0', 'errortip' => '区域数据获取成功', 'result' => $Areas);
        } else {
            $res = array('error' => '1', 'errortip' => '区域数据获取失败', 'result' => array());
        }
        ajax_return($res);
    }

    //获取对应集团所有学校的城市
    function getAllSchoolCityView()
    {
        $request = Input('get.', '', 'trim,addslashes,strip_tags');
        $PublicModel = new \Model\Scshop\PublicModel();
        $schoolCity = $PublicModel->getAllSchoolCity($request);
        if ($schoolCity) {
            $res = array('error' => '0', 'errortip' => '对应集团所有学校的城市数据获取成功', 'result' => $schoolCity['result'] , 'initial' => $schoolCity['initial']);
        } else {
            $res = array('error' => '1', 'errortip' => '对应集团所有学校的城市数据获取失败', 'result' => array());
        }
        ajax_return($res);
    }


    function jmstingloginView(){
        $request = Input('post.', '', 'trim,addslashes');
        $PublicModel = new \Model\Scshop\PublicModel();
        $result = $PublicModel->jmstingLogin($request);
        if($result){
            ajax_return(array('error' => 0, 'errortip' => "登陆成功!", 'result' => $result));
        }else{
            $result = array();
            $result['lgerrortip'] = $PublicModel->errortip;
            ajax_return(array('error' => 1, 'errortip' => $PublicModel->errortip, 'result' => $result));
        }
    }

    //获取招行对账单 并 存储对账单里边的明细数据
    function getZhDuizhangdanView(){
        $request = Input('get.', '', 'trim,addslashes');

        $daytime = date("Ymd",(time()-86400));

        $comsql = "select a.companies_id,a.companies_cmbheadappid 
                ,if((select b.zhtblog_id from smc_payfee_order_pay_zhtblog as b where a.companies_id = b.companies_id and b.zhtblog_daytime = '{$daytime}' limit 0,1),1,0) as zhtblog 
                from gmc_code_companies as a 
                where a.company_id = '8888' and a.companies_chargchannel = 'cmbheadbank' and a.companies_cmbheadappid <> '' 
                having zhtblog = 0 
                limit 0,1";
        $comOne = $this->DataControl->selectOne($comsql);

        if(!$comOne){
            ajax_return(array('error' =>1, 'errortip' => '暂无需要同步的主题数据', 'result' =>array()));
        }

//        $daytime = $request['daytime']; //测试用的数据
//        $comOne['companies_cmbheadappid'] = $request['merId']; //测试用的数据
//        $comOne['companies_id'] = $request['companies_id']; //测试用的数据

        $HeadPay = new \Model\Api\HeadboingPayModel();
        $orderInfo = [
            'merId'    => $comOne['companies_cmbheadappid'],//关闭订单的订单号 30899918299084S
            'billDate'    => $daytime
        ];
        $qrcode = $HeadPay->statementurl($orderInfo,$comOne['companies_id']);

        if($qrcode){
//            print_r($daytime);
//            print_r($comOne);
//            print_r($qrcode);
//            die;
            if($qrcode['respCode'] != 'SUCCESS'){
                $tblog = array();
                $tblog['companies_id'] = $comOne['companies_id'];
                $tblog['zhtblog_daytime'] = $daytime;
                $tblog['zhtblog_notes'] = '处理失败(招行返回值1):'.$qrcode['respMsg'];
                $tblog['zhtblog_addtime'] = time();
                $this->DataControl->insertData("smc_payfee_order_pay_zhtblog", $tblog);

                echo '处理失败(招行返回值1):'.$qrcode['respMsg'];
                die;
            }

            $biz_content = json_decode($qrcode['biz_content'],1);
            $url = $biz_content['fileDownloadUrl'];

            $options=array(
                "ssl"=>array(
                    "verify_peer"=>false,
                    "verify_peer_name"=>false,
                ),
            );
            file_put_contents('importexcel/compare/analysis.xlsx', file_get_contents($url,false,stream_context_create($options)));//线上地址文件 存储到本地

//            $ys_array = array('商户订单号' => 'food_name', '手续费' => 'food_material');
//            $sqlarray = execl_to_array_zh('importexcel/compare/analysis.xlsx', $ys_array);//读取本地文件指定的数据
//            array_shift($sqlarray);

            $ys_arrayOne = array('日账单' => 'cmbheadappid');
            $sqlarrayOne = execl_to_array_zhone('importexcel/compare/analysis.xlsx', $ys_arrayOne);
            array_shift($sqlarrayOne);
            $cmbappid = $sqlarrayOne[0]['cmbheadappid'];

            $ys_array = ["商户订单号"=>"商户订单号",
                "银行流水号"=>"银行流水号",
                "第三方订单号"=>"第三方订单号",
                "支付方式"=>"支付方式",
                "交易类型"=>"交易类型",
                "交易币种"=>"交易币种",
                "商品名称"=>"商品名称",
                "交易日期"=>"交易日期",
                "交易时间"=>"交易时间",
                "门店名称"=>"门店名称",
                "门店编号"=>"门店编号",
                "收银员"=>"收银员",
                "付款人信息"=>"付款人信息",
                "付款银行"=>"付款银行",
                "交易金额"=>"交易金额",
                "手续费"=>"手续费",
                "结算金额"=>"结算金额",
                "费率（%）"=>"费率（%）",
                "订单原始金额"=>"订单原始金额",
                "免充值优惠券金额"=>"免充值优惠券金额",
                "企业红包金额"=>"企业红包金额",
                "企业红包退款金额"=>"企业红包退款金额",
                "平台优惠金额"=>"平台优惠金额",
                "账单日期"=>"账单日期",
                "完成日期"=>"完成日期",
                "清分日期"=>"清分日期",
                "清分账号"=>"清分账号",
                "清分结果"=>"清分结果",
                "原交易商户订单号"=>"原交易商户订单号",
                "原交易银行流水号"=>"原交易银行流水号",
                "原交易第三方订单号"=>"原交易第三方订单号",
                "业务种类"=>"业务种类",
                "商户保留域"=>"商户保留域",
                "收款方备注"=>"收款方备注",
                "付款方备注"=>"付款方备注",
                "退款备注"=>"退款备注",
            ];
            $sqlarray = execl_to_array_zh('importexcel/compare/analysis.xlsx', $ys_array);//读取本地文件指定的数据
            array_shift($sqlarray);

            $t_num = 0;
            $f_num = 0;
            $all_num = $sqlarray?count($sqlarray):0;
            if($sqlarray){
                foreach ($sqlarray as $key=>$sqlvar){
                    $json_allarray = array();
                    $json_allarray = json_encode($sqlvar,JSON_UNESCAPED_UNICODE);

                    $zhlogArray = array();
                    $zhlogArray['zhlog_cmbappid'] = $cmbappid;//招行商户号
                    $zhlogArray['zhlog_cmbtype'] = 1;//商户类型  0 未知  1 总行 2 分行

                    $zhlogArray['zhlog_paypid'] = $sqlvar['商户订单号'];
                    $zhlogArray['zhlog_ifee'] = $sqlvar['手续费'];
                    $zhlogArray['zhlog_rate'] = $sqlvar['费率（%）'];

                    $zhlogArray['zhlog_pay_typename'] = $sqlvar['支付方式'];
                    $zhlogArray['zhlog_pay_date'] = $sqlvar['交易日期'];
                    $zhlogArray['zhlog_pay_time'] = $sqlvar['交易时间'];
                    $zhlogArray['zhlog_pay_price'] = $sqlvar['交易金额'];

                    $zhlogArray['zhlog_jsonstr'] = $json_allarray;
                    $zhlogArray['zhlog_addtime'] = time();
                    $zhlogId = $this->DataControl->insertData("smc_payfee_order_pay_zhlog", $zhlogArray);
                    if($zhlogId){
                        $t_num++;
                    }else{
                        $logone = $this->DataControl->selectOne(" select * from smc_payfee_order_pay_zhlog where zhlog_paypid = '{$sqlvar['商户订单号']}' and zhlog_ifee = '{$sqlvar['手续费']}' and zhlog_rate = '{$sqlvar['费率（%）']}'  limit 0,1 ");
                        if($logone){
                            $t_num++;
                        }else{
                            $f_num++;
                        }
                    }
                }
            }
            if($t_num == $all_num){
                $tblog = array();
                $tblog['companies_id'] = $comOne['companies_id'];
                $tblog['zhtblog_daytime'] = $daytime;
                $tblog['zhtblog_notes'] = '处理成功';
                $tblog['zhtblog_addtime'] = time();
                $tblogId = $this->DataControl->insertData("smc_payfee_order_pay_zhtblog", $tblog);
            }

            echo '处理成功'.$comOne['companies_id'].'-'.$daytime;
        }else{
            $tblog = array();
            $tblog['companies_id'] = $comOne['companies_id'];
            $tblog['zhtblog_daytime'] = $daytime;
            $tblog['zhtblog_notes'] = '处理失败(招行返回值2):'.$HeadPay->errortip;
            $tblog['zhtblog_addtime'] = time();
            $this->DataControl->insertData("smc_payfee_order_pay_zhtblog", $tblog);

            echo $HeadPay->errortip;
        }
        die;
    }

    //结尾魔术函数
    function __destruct()
    {

    }
}
