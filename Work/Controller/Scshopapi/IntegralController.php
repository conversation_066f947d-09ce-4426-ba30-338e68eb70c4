<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2018/12/29
 * Time: 10:09
 */

namespace Work\Controller\Scshopapi;


class IntegralController extends viewTpl
{
    public $u;
    public $t;
    public $c;
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";

    const XcxappId = 'wx45d70456847ac845';
    const XcxappSecret = '7cf208b45cd418d073d7def4d6f63489';

    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
    }

    //第三方授权访问权限校验
    function UserVerify($paramArray)
    {
        $apiuserOne = $this->DataControl->getFieldOne('imc_apiuser', "apiuser_id,apiuser_aeskey,apiuser_aesiv", "apiuser_code = '{$paramArray['apiuser_code']}'");
        if (!$apiuserOne) {
            $this->errortip = "未查询到您的授权信息";
            $this->error = true;
            return false;
        }

        if (isset($paramArray['apiuser_aeskey']) && $paramArray['apiuser_aeskey'] !== '' && isset($paramArray['apiuser_aesiv']) && $paramArray['apiuser_aesiv'] !== '') {
            if ($apiuserOne['apiuser_aeskey'] == $paramArray['apiuser_aeskey'] && $apiuserOne['apiuser_aesiv'] == $paramArray['apiuser_aesiv']) {
                $baseOne = array();
                $baseOne['apiuser_id'] = $apiuserOne['apiuser_id'];
                $companyOne = $this->DataControl->selectOne("select company_id,company_cnname from gmc_company WHERE company_id = '{$paramArray['company_id']}' limit 0,1");
                if ($companyOne) {
                    $baseOne['company_id'] = $companyOne['company_id'];
                    return $baseOne;
                } else {
                    $this->errortip = '你的授权集团编号错误，请确认编号正确';
                    $this->error = true;
                    return false;
                }
            } else {
                $this->errortip = "你的授权秘钥及偏移值不正确，{$paramArray['apiuser_aeskey']}-{$paramArray['apiuser_aesiv']}";
                $this->error = true;
                return false;
            }
        }

        if (!isset($paramArray['timesteps']) || $paramArray['timesteps'] == '') {
            $this->errortip = "请传入授权时间";
            $this->error = true;
            return false;
        }

        if ($paramArray['timesteps'] + 60 * 5 < time() || $paramArray['timesteps'] - 60 > time()) {
            $maxtimes = date("Y-m-d H:i:s", $paramArray['timesteps'] + 60 * 5);
            $this->errortip = "授权时间{$maxtimes}已过期5分钟，请确认连接及时性";//,{$timesteps}--{$jmsting}
            $this->error = true;
            return false;
        }

        $aes = new \Aesencdec($apiuserOne['apiuser_aeskey'], $apiuserOne['apiuser_aesiv']);
        $xssting = $aes->decrypt($this->stringReplace($paramArray['veytoken']));//解密
        if ($paramJson = json_decode($xssting, 1)) {//转化为数组
            if ((string)$paramJson['timesteps'] !== trim($paramArray['timesteps'])) {
                $this->errortip = '授权时间和连接时间不一致';
                $this->error = true;
                return false;
            }
            $baseOne = array();
            $baseOne['apiuser_id'] = $apiuserOne['apiuser_id'];
            $baseOne['tokenstring'] = $xssting;
            $companyOne = $this->DataControl->selectOne("select company_id,company_cnname from gmc_company WHERE company_id = '{$paramJson['company_id']}' limit 0,1");
            if ($companyOne) {
                $baseOne['company_id'] = $companyOne['company_id'];
                return $baseOne;
            } else {
                $this->errortip = '你的授权集团编号错误，请确认编号正确';
                $this->error = true;
                return false;
            }
        } else {
            $this->errortip = '数据机密信息传输有误，请检查！';
            $this->error = true;
            return false;
        }
    }

    function VeryModelNums($apiuser_id, $apimodule_code, $paramArray)
    {
        $request = Input('get.', '', 'trim,addslashes');
        $apimoduleOne = $this->DataControl->getFieldOne('imc_apiuser_apimodule'
            , "apimodule_id,apiuser_id,apimodule_name,apimodule_nums", "apiuser_id = '{$apiuser_id}' AND apimodule_code = '{$apimodule_code}'");
        $stattTimes = strtotime(date("Y-m-d"));
        $apilogOne = $this->DataControl->selectOne("SELECT COUNT(l.apilog_id) AS anums FROM imc_apiuser_apilog AS l
WHERE l.apimodule_id = '{$apimoduleOne['apimodule_id']}' AND l.apilog_createtime > '{$stattTimes}'");
        if ($apilogOne['anums'] > $apimoduleOne['apimodule_nums']) {
            $this->errortip = "您接口{$apimoduleOne['apimodule_name']}的本日最大授权次数{$apimoduleOne['apimodule_nums']}已消耗完毕！";
            $this->error = true;
            return false;
        } else {
            $data = array();
            $data['apiuser_id'] = $apimoduleOne['apiuser_id'];
            $data['apimodule_id'] = $apimoduleOne['apimodule_id'];
            $data['apilog_posturl'] = "https://api.kedingdang.com/{$request['u']}/{$request['t']}";
            $data['apilog_posttype'] = 'GET';
            $data['apilog_postorgjson'] = http_build_query($paramArray);
            $data['apilog_postjson'] = $paramArray['tokenstring'];
            $data['apilog_ip'] = real_ip();
            $data['apilog_createtime'] = time();
            $this->DataControl->insertData("imc_apiuser_apilog", $data);
            return true;
        }
    }

    //获取热门积分商品
    function getIntegralGoodsView()
    {
        $request = Input('get.', '', 'trim,addslashes,strip_tags');
        if ($request['from'] !== 'app') {
            $this->ThisVerify($request);//验证账户  //小程序不需要验证
        }
        $Model = new \Model\Scshop\IntegralModel($request);
        $dataList = $Model->getIntegralGoods($request);

        $result = array();
        if ($dataList) {
            $result["list"] = $dataList['list'];
            $result["allnum"] = $dataList['allnum'];
            $res = array('error' => 0, 'errortip' => '获取热门积分商品成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }

        ajax_return($res, $request['language_type']);
    }

    function getGoodsDetailApi()
    {
        $request = Input('get.', '', 'trim,addslashes,strip_tags');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Scshop\IntegralModel($request);
        $dataList = $Model->getGoodsDetail($request);

        $result = array();
        if ($dataList) {
            $result["list"] = $dataList;
            $res = array('error' => 0, 'errortip' => '获取商品商品成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }

        ajax_return($res, $request['language_type']);
    }

    //获取积分类型
    function integraltypeApi()
    {
        $request = Input('get.', '', 'trim,addslashes,strip_tags');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Scshop\IntegralModel($request);
        $dataList = $Model->integraltype($request);
        $result = array();
        if ($dataList) {
            $result["list"] = $dataList;
            $res = array('error' => 0, 'errortip' => "获取积分类型成功", 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //学员积分明细
    function stuIntegralDetailView()
    {
        $request = Input('get.', '', 'trim,addslashes,strip_tags');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Scshop\IntegralModel($request);
        $dataList = $Model->StuIntegralDetail($request);

        $result = array();
        if ($dataList) {
            $result["list"] = $dataList['list'];
            $result["allnum"] = $dataList['allnum'];
            $res = array('error' => 0, 'errortip' => "获取学员积分明细成功", 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //学员积分明细
    function integralDetailView()
    {
        $request = Input('get.', '', 'trim,addslashes,strip_tags');

        if($request['isapp'] == '1'){
            $Model = new \Model\Scshop\IntegralModel($request);
            $dataList = $Model->integralDetail($request);
            $result = array();
            if ($dataList) {
                $result["list"] = $dataList['list'];
                $result["allnum"] = $dataList['allnum'];
                $res = array('error' => 0, 'errortip' => "获取学员积分明细成功", 'result' => $result);
            } else {
                $result["list"] = array();
                $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
            }
            ajax_return($res, $request['language_type']);
        }

        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'integralDetail', $request)) {
                $request['tokenstring'] = $pucArray['tokenstring'];
                $Model = new \Model\Scshop\IntegralModel($request);
                $dataList = $Model->integralDetail($request);
                $result = array();
                if ($dataList) {
                    $result["list"] = $dataList['list'];
                    $result["allnum"] = $dataList['allnum'];
                    $res = array('error' => 0, 'errortip' => "获取学员积分明细成功", 'result' => $result);
                } else {
                    $result["list"] = array();
                    $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
                }
                ajax_return($res, $request['language_type']);
            }else{
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }







        $this->ThisVerify($request);//验证账户


    }

    //兑换记录
    function stuIntegralExchangeView()
    {
        $request = Input('get.', '', 'trim,addslashes,strip_tags');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Scshop\IntegralModel($request);
        $dataList = $Model->StuIntegralExchange($request);

        $result = array();
        if ($dataList) {
            $result["list"] = $dataList['list'];
            $result["allnum"] = $dataList['allnum'];
            $res = array('error' => 0, 'errortip' => "获取学员积分明细成功", 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    function getProdtypeApi()
    {
        $request = Input('get.', '', 'trim,addslashes,strip_tags');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Scshop\IntegralModel($request);
        $dataList = $Model->getProdtype($request);

        $result = array();
        if ($dataList) {
            $result["list"] = $dataList;
            $res = array('error' => 0, 'errortip' => "获取货品类型成功", 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    function getSectionApi()
    {
        $request = Input('get.', '', 'trim,addslashes,strip_tags');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Scshop\IntegralModel($request);
        $dataList = $Model->getSection($request);

        $result = array();
        if ($dataList) {
            $result["list"] = $dataList;
            $res = array('error' => 0, 'errortip' => "获取积分区间成功", 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    function exchangeGoodsAction()
    {
        $request = Input('post.', '', 'trim,addslashes,strip_tags');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Scshop\IntegralModel($request);
        $dataList = $Model->exchangeGoods($request);

        $result = array();
        if ($dataList) {
            $result["list"] = $dataList;
            $res = array('error' => 0, 'errortip' => "兑换成功", 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //获取孩子可用订单优惠券
    function StuOrderCouponsView()
    {
        $request = Input('get.', '', 'trim,addslashes,strip_tags');
        $this->ThisVerify($request);//验证账户
//        $Model = new \Model\Scshop\OrderModel();
//        $dataList = $Model->StuOrderCoupons($request);

        $Model = new \Model\Smc\RegistrationModel();
        $dataList = $Model->getAvailableOrderTicketList($request);
        $field = array();
        $field["coupons_id"] = "id";
        $field["coupons_name"] = "名称";
        $field["count"] = "数量";

        $result = array();
        if ($dataList) {
            $result["field"] = $field;
            $result["data"] = $dataList;
            ajax_return(array('error' => 0, 'errortip' => "获取孩子可用订单优惠券成功", 'result' => $result));
        } else {
            ajax_return(array('error' => 1, 'errortip' => "暂无孩子可用订单优惠券数据", 'result' => array()));
        }
    }

    //订单确认商品列表
    function OrderGoodsListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Scshop\OrderModel();
        $dataList = $Model->OrderGoodsList($request);

        $field = array();
        $field["sellgoods_id"] = "商品id";
        $field["sellgoods_name"] = "名称";
        $field["price"] = "商品价格";
        $field["sellgoods_cartchange"] = "是否允许购物车加减 0不可以1可以";
        $field["sellgoods_listimg"] = "图片";

        $result = array();
        if ($dataList) {
            $result["field"] = $field;
            $result["data"] = $dataList;
            ajax_return(array('error' => 0, 'errortip' => "获取订单确认商品列表成功", 'result' => $result, 'carts_id' => stripslashes($request['carts_id'])));
        } else {
            ajax_return(array('error' => 1, 'errortip' => "暂无订单确认商品列表数据", 'result' => array()));
        }
    }

    //获取商品优惠券列表
    function GoodsCouponsListView()
    {
        $request = Input('get.', '', 'trim,addslashes,strip_tags');
        $this->ThisVerify($request);//验证账户
//        $Model = new \Model\Scshop\OrderModel();
//        $dataList = $Model->GoodsCouponsList($request);


        $Model = new \Model\Smc\RegistrationModel();
        $dataList = $Model->getAvailableTicketList($request);
        if ($dataList) {
            foreach ($dataList as &$val) {
                $rule = $this->DataControl->selectOne("SELECT
                                                            c.coupons_id,
                                                            co.applytype_play as couponsrules_play
                                                        FROM
                                                            smc_student_coupons AS c
                                                            LEFT JOIN smc_student_coupons_apply AS a ON a.apply_id = c.apply_id
                                                            LEFT JOIN smc_code_couponsapplytype AS co ON co.applytype_branch = a.applytype_branch
                                                            WHERE c.coupons_id = '{$val['coupons_id']}'");
//                left join shop_code_couponsrules as r on r.couponsrules_id = co.couponsrules_id
                $val['applytype_rule'] = $rule['couponsrules_play'];
            }
        }


        if ($dataList) {
            if ($request['coupons_isuse'] == 0) {
                $data = array();
                foreach ($dataList as $dataOne) {
                    if ($dataOne['can_use'] == 1) {
                        $data[] = $dataOne;
                    }
                }
                if ($data) {
                    $num = count($data);
                } else {
                    $num = 0;
                }

            } else {
                $num = count($dataList);
            }
        } else {
            $num = 0;
        }

        $field = array();
        $field["coupons_id"] = "优惠券id";
        $field["coupons_name"] = "优惠券名称";
        $field["coupons_type"] = "类型";
        $field["coupons_price"] = "减免金额";
        $field["coupons_discount"] = "减免比率";
        $field["coupons_exittime"] = "截止时间";

        $result = array();
        if ($dataList) {
            $result["field"] = $field;
            $result["data"] = $dataList;
            $result["allNum"] = $num;
            ajax_return(array('error' => 0, 'errortip' => "获取商品优惠券列表成功", 'result' => $result));
        } else {

            $result["allNum"] = 0;
            ajax_return(array('error' => 1, 'errortip' => "暂无商品优惠券列表数据", 'result' => $result));
        }
    }

    //获取订单优惠券列表
    function OrderCouponsListView()
    {
        $request = Input('get.', '', 'trim,addslashes,strip_tags');
        $this->ThisVerify($request);//验证账户
//        $Model = new \Model\Scshop\OrderModel();
//        $dataList = $Model->OrderCouponsList($request);

        $Model = new \Model\Smc\RegistrationModel();
        $dataList = $Model->getAvailableOrderTicketList($request);

        foreach ($dataList as &$val) {
            $rule = $this->DataControl->selectOne("SELECT 
                c.coupons_id,
                c.couponsrules_id,
                co.applytype_play as couponsrules_play
            FROM
                smc_student_coupons AS c
                LEFT JOIN smc_student_coupons_apply AS a ON a.apply_id = c.apply_id
                LEFT JOIN smc_code_couponsapplytype AS co ON co.applytype_branch = a.applytype_branch
                WHERE c.coupons_id = '{$val['coupons_id']}'");
//            left join shop_code_couponsrules as r on r.couponsrules_id = co.couponsrules_id

            if ($rule['couponsrules_play'] != '') {
                $val['applytype_rule'] = $rule['couponsrules_play'];
            } else {
                $dataOne = $this->DataControl->selectOne(" select * from shop_code_couponsrules WHERE couponsrules_id = '{$rule['couponsrules_id']}' ");
                $val['applytype_rule'] = $dataOne['couponsrules_play'];
            }
        }

        $field = array();
        $field["coupons_id"] = "优惠券id";
        $field["coupons_name"] = "优惠券名称";
        $field["coupons_type"] = "类型";
        $field["coupons_price"] = "减免金额";
        $field["coupons_discount"] = "减免比率";
        $field["coupons_exittime"] = "截止时间";

        if ($dataList) {
            if ($request['coupons_isuse'] == 0) {
                $data = array();
                foreach ($dataList as $dataOne) {
                    if ($dataOne['can_use'] == 1) {
                        $data[] = $dataOne;
                    }
                }
                if ($data) {
                    $num = count($data);
                } else {
                    $num = 0;
                }

            } else {
                $num = count($dataList);
            }
        } else {
            $num = 0;
        }

        $result = array();
        if ($dataList) {
            $result["field"] = $field;
            $result["data"] = $dataList;
            $result["allNum"] = $num;
            ajax_return(array('error' => 0, 'errortip' => "获取订单优惠券列表成功", 'result' => $result));
        } else {
            $result["allNum"] = 0;
            ajax_return(array('error' => 1, 'errortip' => "暂无订单优惠券列表数据", 'result' => $result));
        }
    }

    //提交订单
    function SubmitOrderView()
    {
        $request = Input('post.', '', 'trim,addslashes,strip_tags');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Scshop\OrderModel();
        $res = $Model->SubmitOrder($request);
        ajax_return($res);

    }

    //根据订单编号获取支付信息
    function PayInfoView()
    {
        $request = Input('get.', '', 'trim,addslashes,strip_tags');
        $Model = new \Model\Scshop\OrderModel();
        $dataList = $Model->PayInfo($request);
        $field = array();
        $field["order_pid"] = "优惠券编号";
        $field["order_paymentprice"] = "订单价格";
        $field["student_cnname"] = "学员姓名";
        $field["student_branch"] = "学员编号";

        $result = array();
        if ($dataList['list']['pay_issuccess'] == '0' && ($dataList['list']['order_status'] == '1' or $dataList['list']['order_status'] == '2' or $dataList['list']['order_status'] == '3')) {
            if ($dataList) {
                $result["field"] = $field;
                $result["data"] = $dataList;
                ajax_return(array('error' => 0, 'errortip' => "获取订单信息成功", 'result' => $result));
            } else {
                ajax_return(array('error' => 1, 'errortip' => "暂无订单信息错误", 'result' => array()));
            }
        } else {
            $result["field"] = $field;
            $result["data"] = $dataList;
            ajax_return(array('error' => 1, 'errortip' => "未支付订单信息已失效", 'result' => $result));
        }
    }

    //我的协议
    function MyProtocolView()
    {
        $request = Input('get.', '', 'trim,addslashes,strip_tags');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Scshop\OrderModel();
        $dataList = $Model->MyProtocol($request);
        $field = array();
        $field["protocol_price"] = "协议价格";
        $field["pay_pid"] = "支付编号";
        $field["protocol_createtime"] = "合同时间";

        $result = array();
        if ($dataList['list']) {
            $result["field"] = $field;
            $result["data"] = $dataList;
            ajax_return(array('error' => 0, 'errortip' => "获取我的协议成功", 'result' => $result));
        } else {
            ajax_return(array('error' => 1, 'errortip' => "未查询到您的课程协议，请确认您已购买！", 'result' => array()));
        }
    }

    //获取取消原因
    function cancelReasonListView()
    {
        $result = array();
        $result[0] = '我不想买了';
        $result[1] = '下单的商品信息有误';
        $result[2] = '孩子校区选错了';
        $result[3] = '等待的时间太长了';
        $result[4] = '其他';
        ajax_return(array('error' => 0, 'errortip' => "获取取消原因成功", 'result' => $result));

    }

    //取消订单
    function cancelOrderView()
    {
        $request = Input('post.', '', 'trim,addslashes,strip_tags');
        $this->ThisVerify($request);//验证账户

        $OrderModel = new \Model\Smc\OrderModel($request, $request['order_pid']);
        $res = $OrderModel->cancelOrder($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '取消订单成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $OrderModel->errortip, 'result' => $result);
        }
        ajax_return($res);
    }

    //判断订单是否支付成功了 -- 97
    function isPayOrderView()
    {
        $request = Input('get.', '', 'trim,addslashes,strip_tags');
//        $this->ThisVerify($request);//验证账户
        $payOne = $this->DataControl->selectOne("select * from smc_payfee_order_pay WHERE pay_pid = '{$request['paypid']}' and pay_issuccess = '1' ");
        if ($payOne) {
            ajax_return(array('error' => 0, 'errortip' => "订单已支付", 'result' => $payOne));
        } else {
            ajax_return(array('error' => 1, 'errortip' => "订单未支付", 'result' => array()));
        }
    }

    //我的订单列表
    function myOrdersView()
    {
        $request = Input('get.', '', 'trim,addslashes,strip_tags');
        $this->ThisVerify($request);//验证账户

        $OrderModel = new \Model\Scshop\OrderModel();
        $result = $OrderModel->myOrders($request);
        if ($result['allnum'] > 0) {
            ajax_return(array('error' => 0, 'errortip' => "我的订单数据成功", 'result' => $result));
        } else {
            $result["data"] = array();
            $result["allnum"] = 0;
            ajax_return(array('error' => 1, 'errortip' => "未查询到订单信息", 'result' => $result));
        }
    }

    //申请退款挽留页面
    function refundToStayView()
    {
        $request = Input('get.', '', 'trim,addslashes,strip_tags');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Scshop\OrderModel();
        $dataList = $Model->refundToStay($request);
        $field = array();
        $field["sellgoods_name"] = "名字";
        $field["sellgoods_type"] = "类型";
        $field["sellgoods_listimg"] = "图片";
        $field["sellgoods_desc"] = "描述";
        $field["price"] = "价格";

        $result = array();
        if ($dataList) {
            $result["field"] = $field;
            $result["data"] = $dataList;
            ajax_return(array('error' => 0, 'errortip' => "获取申请退款挽留商品成功", 'result' => $result));
        } else {
            ajax_return(array('error' => 1, 'errortip' => "暂无申请退款挽留商品数据", 'result' => array()));
        }
    }

    //提交订单获取个人信息
    function personalInfoView()
    {
        $request = Input('get.', '', 'trim,addslashes,strip_tags');
        $this->ThisVerify($request);//验证账户
        $school = $this->DataControl->getFieldOne("smc_school", "school_address,school_cnname", "school_id = '{$request['school_id']}'");
        $student = $this->DataControl->selectOne("
            SELECT
                s.student_cnname,
                s.student_branch,
                co.course_cnname 
            FROM
                smc_student AS s
                LEFT JOIN smc_student_study AS ss ON s.student_id = ss.student_id and study_isreading = '1' 
                LEFT JOIN smc_class AS cl ON ss.class_id = cl.class_id
                LEFT JOIN smc_course AS co ON co.course_id = cl.course_id 
            WHERE
                s.student_id = '{$request['student_id']}'");

        $stublcOne = $this->DataControl->selectOne("select s.student_forwardprice,b.student_balance,b.student_withholdbalance from smc_student as s,smc_student_balance as b
WHERE b.student_id = s.student_id and b.school_id = '{$request['school_id']}' and s.student_id='{$request['student_id']}' and s.company_id='{$request['company_id']}'");

        $result = array();
        $result['school_address'] = $school['school_address'];
        $result['school_cnname'] = $school['school_cnname'];
        $result['student_cnname'] = $student['student_cnname'];
        $result['student_branch'] = $student['student_branch'];
        $result['course_cnname'] = $student['course_cnname'];
        $result['balance'] = $stublcOne['student_balance'];
        $result['forward'] = $stublcOne['student_forwardprice'];

        ajax_return(array('error' => 0, 'errortip' => "获取成功", 'result' => $result));

    }

    //订单详情
    function orderDetailView()
    {
        $request = Input('get.', '', 'trim,addslashes,strip_tags');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Scshop\OrderModel();
        $dataList = $Model->orderDetail($request);

        $result = array();
        if ($dataList) {
            $result["data"] = $dataList;
            ajax_return(array('error' => 0, 'errortip' => "获取订单详情成功", 'result' => $result));
        } else {
            ajax_return(array('error' => 1, 'errortip' => "暂无订单详情数据", 'result' => array()));
        }
    }

    //订单支付个人信息
    function payPersonalInfoView()
    {
        $request = Input('get.', '', 'trim,addslashes,strip_tags');
        $this->ThisVerify($request);//验证账户
        $school_id = $this->DataControl->getFieldOne("smc_payfee_order", "school_id", "order_pid = '{$request['order_pid']}'");

        $school = $this->DataControl->getFieldOne("smc_school", "school_address,school_cnname", "school_id = '{$school_id['school_id']}'");
        $student = $this->DataControl->selectOne("
            SELECT
                s.student_cnname,
                s.student_branch,
                co.course_cnname 
            FROM
                smc_student AS s
                LEFT JOIN smc_student_study AS ss ON s.student_id = ss.student_id and ss.study_isreading = '1' 
                LEFT JOIN smc_class AS cl ON ss.class_id = cl.class_id
                LEFT JOIN smc_course AS co ON co.course_id = cl.course_id 
            WHERE
                s.student_id = '{$request['student_id']}'");
        $phone = $this->DataControl->getFieldOne("smc_parenter", "parenter_mobile", "parenter_id = '{$request['parenter_id']}'");
        $result = array();
        $result['school_address'] = $school['school_address'];
        $result['school_cnname'] = $school['school_cnname'];
        $result['student_cnname'] = $student['student_cnname'];
        $result['student_branch'] = $student['student_branch'];
        $result['course_cnname'] = $student['course_cnname'];
        $result['phone'] = $phone['parenter_mobile'];

        ajax_return(array('error' => 0, 'errortip' => "获取成功", 'result' => $result));

    }




    //已耗课时
    function getCostClasshourView()
    {
        $request = Input('get.', '', 'trim,addslashes,strip_tags');
//        $this->ThisVerify($request);//验证账户
        $list = $this->DataControl->selectClear("
            SELECT
	C.course_branch,B.class_enname,(
				SELECT
					count( 1 ) 
				FROM
					smc_student_coursebalance_log 
				WHERE
					student_id = A.student_id 
					AND class_id = A.class_id 
					AND hourstudy_id > 0 
					AND log_class = 0 
					AND log_playclass = '-' 
					AND log_playamount > 0 
					) AS spend_times,(
				SELECT
					coursebalance_time 
				FROM
					smc_student_coursebalance X 
				WHERE
					X.course_id = B.course_id 
					AND X.student_id = A.student_id 
					AND X.school_id = A.school_id 
				) AS coursebalance_time
			FROM
				smc_student_study A
				LEFT JOIN smc_class B ON A.class_id = B.class_id
				LEFT JOIN smc_course C ON B.course_id = C.course_id
			WHERE
						B.class_type IN ( '0' ) 
				AND A.company_id = '8888' 
				AND A.student_id = '{$request['student_id']}' 
				AND B.class_status > '-2' 
			ORDER BY
				B.class_status DESC,
				a.study_endday DESC ");

        if($list){
            foreach($list as &$val){
                $val['class_cnname'] = $val['course_branch'];
                $val['renum'] = $val['spend_times'];
                $val['allnum'] = $val['spend_times'] + $val['coursebalance_time'];
            }
            foreach($list as $k=>$v){
                if($v['allnum'] == '0'){
                    unset($list[$k]);
                }
            }
        }
        $result = array();
        $result = $list;
        if($list){
            ajax_return(array('error' => 0, 'errortip' => "获取成功", 'result' => $result));
        }else{
            ajax_return(array('error' => 1, 'errortip' => "暂无数据", 'result' => array()));

        }

    }

    //我的课表课时数
    function getTimeNumView()
    {
        $request = Input('get.', '', 'trim,addslashes,strip_tags');
//        $this->ThisVerify($request);//验证账户
        $list = $this->DataControl->selectClear("
            select g.* from (SELECT
	C.course_branch,(
	SELECT
		count( 1 ) 
	FROM
		smc_student_coursebalance_log 
	WHERE
		student_id = A.student_id 
		AND class_id = A.class_id 
		AND hourstudy_id > 0 
		AND log_class = 0 
		AND log_playclass = '-' 
		AND log_playamount > 0 
		) AS spend_times,(
	SELECT
		coursebalance_time 
	FROM
		smc_student_coursebalance X 
	WHERE
		X.course_id = B.course_id 
		AND X.student_id = A.student_id 
		AND X.school_id = A.school_id 
	) AS coursebalance_time 
FROM
	smc_student_study A
	LEFT JOIN smc_class B ON A.class_id = B.class_id
	LEFT JOIN smc_course C ON B.course_id = C.course_id 
WHERE
	B.class_type IN ( '0' ) 
	AND A.company_id = '8888' 
	AND A.student_id = '{$request['student_id']}' 
	AND B.class_status > '-2' 
ORDER BY
	spend_times DESC,
	B.class_status DESC,
	a.study_endday DESC
	) as g
GROUP BY g.course_branch
 ");

        $a = 0;
        $b = 0;
        $c = 0;
        if($list){
            foreach($list as &$val){
                $val['class_cnnmae'] = $val['course_branch'];
                $val['renum'] = $val['spend_times'];
                $val['allnum'] = $val['spend_times'] + $val['coursebalance_time'];
                $a += $val['allnum'];
                $b += $val['coursebalance_time'];
                $c += $val['renum'];
            }
        }
        $result = array();
        $result['allnum'] = $a;
        $result['restnum'] = $b;
        $result['alreadynum'] = $c;
        ajax_return(array('error' => 0, 'errortip' => "获取成功", 'result' => $result));


    }

    //使用优惠券
    function useCouponsView()
    {
        $request = Input('post.', '', 'trim,addslashes,strip_tags');

        $this->ThisVerify($request);//验证账户
        $data = array();
        $data['order_pid'] = $request['order_pid'];
        $data['course_id'] = $request['course_id'];
        $data['goods_id'] = $request['goods_id'];
        $data['coupons_pid'] = $request['coupons_pid'];
        $data['ordercoupons_price'] = $request['ordercoupons_price'];
        $this->DataControl->insertData("smc_payfee_order_coupons", $data);

        $data = array();
        $data['coupons_isuse'] = '1';
        $data['coupons_usetime'] = time();

        if ($this->DataControl->updateData("smc_student_coupons", "coupons_pid = '{$request['coupons_pid']}'", $data)) {
            ajax_return(array('error' => 0, 'errortip' => "使用优惠券成功"));
        } else {
            ajax_return(array('error' => 1, 'errortip' => "使用优惠券失败"));

        }
    }

    //取消使用优惠券
    function unuseCouponsView()
    {
        $request = Input('post.', '', 'trim,addslashes,strip_tags');
        $this->ThisVerify($request);//验证账户

        $this->DataControl->delData("smc_payfee_order_coupons", "coupons_pid = '{$request['coupons_pid']}'");

        $data = array();
        $data['coupons_isuse'] = '0';
        $data['coupons_usetime'] = '0';

        if ($this->DataControl->updateData("smc_student_coupons", "coupons_pid = '{$request['coupons_pid']}'", $data)) {
            ajax_return(array('error' => 0, 'errortip' => "取消使用优惠券成功"));
        } else {
            ajax_return(array('error' => 1, 'errortip' => "取消使用优惠券失败"));

        }
    }

    //提交订单
    function createOrderView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        //创建订单 获取返回的订单pid
        $Model = new \Model\Smc\RegistrationModel();
        $res = $Model->createOrder($request);

        if (!$res) {
            ajax_return(array('error' => 1, 'errortip' => $Model->errortip));
        }


        $orderOne = $this->DataControl->getFieldOne("smc_payfee_order", "companies_id", "order_pid='{$res}'");

        $type = $this->DataControl->getFieldOne("smc_payfee_order", "order_type", "order_pid = '{$res}'");
        $data = array();
        $data['order_from'] = '0';
        $this->DataControl->updateData("smc_payfee_order", "order_pid = '{$res}'", $data);
        do {
            $paypid = $this->createOrderPid('ZF');
        } while ($this->DataControl->selectOne("select pay_id from smc_payfee_order_pay where pay_pid='{$paypid}' limit 0,1"));
        if ($request['price'] > '0') {
            $data = array();
            $data['order_pid'] = $res;
            $data['companies_id'] = $orderOne['companies_id'];
            $data['pay_pid'] = $paypid;
            $data['pay_price'] = $request['price'];
            $data['pay_type'] = '0';
            $data['pay_createtime'] = time();
            $this->DataControl->insertData("smc_payfee_order_pay", $data);
        }

        $schoolList = json_decode(stripslashes($request['cart_id']), true);
        foreach ($schoolList as $item) {
            $this->DataControl->delData('shop_cart', "cart_id = '{$item['cart_id']}'");
        }

        ajax_return(array('error' => 0, 'errortip' => "提交订单成功", 'order_pid' => $res, 'order_type' => $type['order_type']));


    }


    function createRechargeOrderAction()
    {
        $request = Input('request.', '', 'trim,addslashes,strip_tags');
        $Model = new \Model\Scshop\OrderModel();
        $res = $Model->createRechargeOrder($request);
        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '生成成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    function buyGoodsAction()
    {
        $request = Input('post.', '', 'trim,addslashes,strip_tags');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Smc\GoodsFeeModel();
        $res = $Model->buyGoods($request);
        $type = $this->DataControl->getFieldOne("smc_payfee_order", "order_type", "order_pid = '{$res}'");

        $data = array();
        $data['order_from'] = '0';
        $this->DataControl->updateData("smc_payfee_order", "order_pid = '{$res}'", $data);

        $schoolList = json_decode(stripslashes($request['cart_id']), true);
        foreach ($schoolList as $item) {
            $this->DataControl->delData('shop_cart', "cart_id = '{$item['cart_id']}'");
        }

        ajax_return(array('error' => 0, 'errortip' => "提交订单成功", 'order_pid' => $res, 'order_type' => $type['order_type']));

    }


    function getOrderPriceApi()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Smc\RegistrationModel();
        $res = $Model->getOrderPrice($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res);

    }


    /**
     *  积分商城-福袋抽奖-活动状态
     * author: ling
     * 对应接口文档 0001
     * Date 2021/1/8 0008
     */
    function getActityView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $result = array();
        if (1 == 1) {
            $result['status'] = true;
        } else {
            $result['status'] = false;
        }
        ajax_return(array('error' => 0, 'errortip' => "获取活动状态", 'result' => $result));
    }

    /**
     * 积分商城登陆
     * author: ling
     * 对应接口文档 0001
     * Date 2021/1/5 0005
     */
    function parentpswdloginApi()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $parenterOne = $this->DataControl->getFieldOne("smc_parenter", " * ", " parenter_mobile = '{$request['L_name']}'");

        if ($parenterOne) {
            $iparenter = array();
            $iparenter['parenter_id'] = $parenterOne['parenter_id'];
            $iparenter['parenter_nickname'] = $parenterOne['parenter_nickname'];
            $iparenter['parenter_img'] = $parenterOne['parenter_img'];
            $iparenter['parenter_cnname'] = $parenterOne['parenter_cnname'];
            $iparenter['parenter_enname'] = $parenterOne['parenter_enname'];
            $iparenter['token'] = $this->getParentJFToken($parenterOne);
            $this->DataControl->updateData("smc_parenter", "parenter_id = '{$parenterOne['parenter_id']}'", array("parenter_lasttime" => time(), "parenter_lastip" => real_ip()));
            $iparenter['studentone'] = $this->DataControl->selectOne("SELECT s.student_id, s.student_img, s.student_branch, s.student_cnname, s.student_enname , s.student_sex, s.student_birthday,g.company_isstock
, d.school_id, g.company_id, g.company_cnname FROM smc_student s, smc_student_family f, smc_student_enrolled d, gmc_company g
WHERE s.student_id = f.student_id AND d.student_id = f.student_id AND s.company_id = g.company_id
AND f.parenter_id = '{$parenterOne['parenter_id']}'  and g.company_id='{$request['company_id']}' AND d.enrolled_status IN (0, 1) 

ORDER BY d.enrolled_createtime DESC, f.family_isdefault DESC LIMIT 0, 1");
            $iparenter['company_id'] = $iparenter['studentone']['company_id'];
            $iparenter['company_cnname'] = $iparenter['studentone']['company_cnname'];
            $iparenter['company_isstock'] = $iparenter['studentone']['company_isstock'];
            if (!$iparenter['studentone']) {
                ajax_return(array('error' => 1, 'errortip' => "学生暂未入校,不可登录!"));
            }

            $isset = $this->DataControl->getFieldOne("smc_parenter_wxchattoken","wxchattoken_id","parenter_id = '{$parenterOne['parenter_id']}' and wxchatnumber_id = '6' and company_id = '{$request['company_id']}'");

            if(!$isset){
                $data = array();
                $data['company_id'] = $request['company_id'];
                $data['parenter_id'] = $parenterOne['parenter_id'];
                $data['parenter_wxtoken'] = $request['openid'];
                $data['wxchatnumber_id'] = '6';
                $this->DataControl->insertData("smc_parenter_wxchattoken", $data);
            }

            ajax_return(array('error' => 0, 'errortip' => "登录成功!", 'result' => $iparenter));
        } else {
            ajax_return(array('error' => 1, 'errortip' => "家长账户信息不存在!"));
        }
    }

    function getParentJFToken($params = array())
    {
        $parenterOne = $this->DataControl->getFieldOne("smc_parenter", "parenter_id,parenter_tokencode,parenter_tokenencrypt", "parenter_id='{$params['parenter_id']}'");
        if (!$parenterOne) {
            return false;
        }

        $montharr = ['08','09','10','11','12','01'];
        $thismonth = date('m',time());
        if(in_array($thismonth,$montharr)){
            $md5tokenbar = base64_encode(md5($parenterOne["parenter_tokencode"].date("Y-07")));//-d
        }else{
            $md5tokenbar = base64_encode(md5($parenterOne["parenter_tokencode"].date("Y-01")));//-d
        }

        if ($md5tokenbar == $parenterOne["parenter_tokenencrypt"]) {
            $token = $parenterOne["parenter_tokenencrypt"];
        } else {
            //目前这里注释是为了测试方便
            $tokencode = rand(111111, 999999);
            if(in_array($thismonth,$montharr)){
                $md5tokenbar = base64_encode(md5($tokencode . date("Y-07")));
            }else{
                $md5tokenbar = base64_encode(md5($tokencode . date("Y-01")));
            }
            $this->DataControl->query("UPDATE smc_parenter SET parenter_tokencode = '{$tokencode}',parenter_tokenencrypt = '{$md5tokenbar}' WHERE parenter_id ='{$parenterOne['parenter_id']}'");
            $token = $md5tokenbar;
        }
        return $token;
    }

    /**
     * 积分商城切换孩子
     * author: ling
     * 对应接口文档 0001
     * Date 2021/1/5 0005
     */

    function changeStudentListApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $sql = "SELECT
	s.student_id,
	s.student_cnname,
	s.student_enname,
	s.student_img,
	s.student_sex,
	s.company_id,
	c.company_cnname,
	c.company_logo,
	l.school_cnname,
	l.school_shortname,
	l.school_id,
	l.school_type
FROM
	smc_student AS s,
	smc_student_enrolled AS e,
	smc_student_family AS f,
	gmc_company AS c,
	smc_school AS l
WHERE
	s.company_id = '{$request['company_id']}'
AND s.student_isdel <> '1'
AND s.student_id = f.student_id
AND s.student_id = e.student_id
AND s.company_id = c.company_id
AND e.school_id = l.school_id
AND f.parenter_id = '{$request['parenter_id']}'
AND e.enrolled_status IN (0, 1)
GROUP BY e.student_id";
        $NoticeDetail = $this->DataControl->selectClear($sql);

        $field = array();
        $field["student_cnname"] = "学员名称";
        $field["student_id"] = "学员id";

        if ($NoticeDetail) {
            foreach ($NoticeDetail as $key => $detail) {
                $NoticeDetail[$key]['property_integralbalance'] = $detail['property_integralbalance'] + 0;
                $schoolOne = $this->DataControl->selectOne("select l.school_id,l.school_cnname,l.school_shortname from smc_student_enrolled as e,smc_school as l where e.school_id=l.school_id and e.student_id='{$detail['student_id']}' and e.enrolled_status IN (0, 1) order by enrolled_createtime DESC ");
                $NoticeDetail[$key]['school_id'] = $schoolOne['school_id'];
                $NoticeDetail[$key]['school_cnname'] = $schoolOne['school_cnname'];
                $NoticeDetail[$key]['school_shortname'] = $schoolOne['school_shortname'];
            }

            $result = array();
            $result["field"] = $field;
            $result["data"] = $NoticeDetail;
            $res = array('error' => '0', 'errortip' => '获取切换孩子列表成功', 'result' => $result);
        } else {
            $result = array();
            $result["field"] = $field;
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '获取切换孩子列表失败', 'result' => $result);
        }
        ajax_return($res);
    }

    /**
     * 获取孩子的个人资料
     * author: ling
     * 对应接口文档 0001
     * Date 2021/1/5 0005
     * @param $paramArray
     * @return array
     */
    function personnalInfoApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Scptc\MineModel();
        $result = $Model->PersonnalInfo($request);
        ajax_return($result);
    }
    /**
     * 获取孩子的是否知晓通知
     * @param $paramArray
     * @return array
     */
    function StuIsknowView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Scptc\MineModel();
        $result = $Model->StuIsknow($request);
        ajax_return($result);
    }

    function testView(){
        var_dump(real_ip());
    }


    /**
     * 孩子的卡券列表
     * author: ling
     * 对应接口文档 0001
     * Date 2021/1/5 0005
     */
    function getCouponsListApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Scshop\IntegralModel($request);
        $dataList = $Model->getCouponsListApi($request);
        $result = array();
        $result['list'] = $dataList;
        if ($dataList) {
            $res = array('error' => '0', 'errortip' => '获取卡券列表', 'result' => $result);
        } else {
            $res = array('error' => '1', 'errortip' => '暂无有效卡券', 'result' => $result);
        }
        ajax_return($res);
    }

    function getCouponsOneApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Scshop\IntegralModel($request);
        $dataOne = $Model->getCouponsOneApi($request);
        $result = array();
        $result['data'] = $dataOne;
        $res = array('error' => '0', 'errortip' => '获取卡券', 'result' => $result);
        ajax_return($res);
    }


    /**
     * 福袋抽奖-获取奖励
     * author: ling
     * 对应接口文档 0001
     * Date 2021/1/6 0006
     */
    function getFuActivityRewardApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $Model = new \Model\Scshop\IntegralModel($request);
        $data = $Model->getFuActivityRewardApi($request);
        $result = array();
        $result['list'] = $data;
        $res = array('error' => 0, 'errortip' => '获取活动详情', 'result' => $result);
        ajax_return($res);
    }

    /**
     * 福袋抽奖-抽奖
     * author: ling
     * 对应接口文档 0001
     * Date 2021/1/6 0006
     */
    function getFuPackageActivityView()
    {
        $request = Input('post.', "", "trim,addslashes");
        $Model = new \Model\Scshop\IntegralModel($request);
        $data = $Model->getFuPackageActivity($request);
        $result = array();
        $result['data'] = $data;
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $result);
        ajax_return($res);
    }

    /**
     * 获取学生的抽奖次数
     * author: ling
     * 对应接口文档 0001
     * Date 2021/1/6 0006
     */
    function getStudentChanceView()
    {
        $request = Input('post.', "", "trim,addslashes");
        $Model = new \Model\Scshop\IntegralModel($request);
        $number = $Model->getStudentChance($request);
        $result = array();
        $result['chance'] = $number;
        $res = array('error' => 0, 'errortip' => '获取学生的抽奖次数', 'result' => $result);
        ajax_return($res);
    }

    /**
     * 微信授权
     * author: ling
     * 对应接口文档 0001
     * Date 2021/1/6 0006
     */
    function wxAuthorizationView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $paramarray = array(
            'appid' => self::XcxappId,
            'secret' => self::XcxappSecret,
            'js_code' => $request['code'],
            'grant_type' => "authorization_code"
        );

        $getBakurl = request_by_curl("https://api.weixin.qq.com/sns/jscode2session", dataEncode($paramarray), "GET");
        $json_play = new \Webjson();
        $dataarray = $json_play->decode($getBakurl, "1");
        $result["error"] = "0";
        $result["errortip"] = '用户信息存在!';
        $result["result"] = $dataarray;
        ajax_return($result);
    }

    /**
     *
     * author: ling
     * 对应接口文档 0001
     * Date 2021/1/6 0006
     */
    function wxAuthorMobileView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        /**
         * sessionKey/encryptedData/iv参数均从url中获取，并赋给相应变量
         */
        $appid = self::XcxappId;
        $sessionKey = $request['session_key'];
        $encryptedData = $request['encryptedData'];
        $iv = $request['iv'];
        $pc = new \WXBizDataCrypt($appid, $sessionKey);
        $errCode = $pc->decryptData($encryptedData, $iv, $data);
        if ($errCode == 0) {
            $res = array('error' => 0, 'errortip' => "用户信息获取正确!", "listjson" => $data, "tokeninc" => "1");
            ajax_return($res, $request['language_type']);
        } else {
            $res = array('error' => 1, 'errortip' => "用户信息获取失败，{$errCode}!", "listjson" => $errCode, "tokeninc" => "1");
            ajax_return($res, $request['language_type']);
        }
    }


    /**
     *
     * author: ling
     * 对应接口文档 0001
     * Date 2021/1/5 0005
     * @param $initial
     * @return string
     */
    function createReceiptPid($initial)
    {
        $charid = strtoupper(md5(uniqid(mt_rand(), true)));
        $uuid = substr($charid, 0, 4)
            . substr($charid, 4, 4)
            . substr($charid, 8, 4)
            . substr($charid, 12, 4);
        return $initial . $uuid;
    }

    //生成模拟抽奖券
//    function addActivityIntegralView()
//    {
//        return false;
//        $reqeust = Input('get.', "", "trim,addslashes");
//        if (isset($reqeust['company_id']) && $reqeust['company_id'] == '') {
//            $res = array('error' => 1, 'errortip' => '请选择所属集团', 'result' => array());
//            ajax_return($res);
//        }
//
//        if (isset($reqeust['pid_num']) && $reqeust['pid_num'] !== '') {
//            $num = 0;
//            for ($i = 0; $i < $reqeust['pid_num']; $i++) {
//                $data = array();
//                $data['company_id'] = $reqeust['company_id'];
//                do {
//                    $rand = rand(100000, 999999);
//                } while ($this->DataControl->selectOne("select coupons_id from smc_integral_cooperation_coupons where coupons_pid='{$rand}' limit 0,1"));
//
//                $data['coupons_pid'] = $rand;
//                $data['coupons_brand_name'] = '卡通尼';
//                $data['coupons_name'] = '卡通尼乐园设施体验券或嘉年华滚球项目x1';
//                $data['coupons_code'] = 'KTN02';
//                $data['coupons_starttime'] = "2021-01-01";
//                $data['coupons_endtime'] = "2021-06-30";
//                $data['coupons_img'] = "";
//                $data['coupons_createtime'] = time();
//                $this->DataControl->insertData('smc_integral_cooperation_coupons', $data);
//                $num++;
//            }
//            $res = array('error' => 0, 'errortip' => '共生成' . $num . '张抽奖券券', 'result' => array());
//            ajax_return($res);
//        } else {
//            $res = array('error' => 0, 'errortip' => '请选需要生成的张数', 'result' => array());
//            ajax_return($res);
//        }
//    }

    /**
     *  清除测试的中奖信息
     * author: ling
     * 对应接口文档 0001
     * Date 2021/1/8 0008
     */
    function clearStudentCouponsView()
    {
        $request = Input('get.', "", "trim,addslashes");
        if (!$request['student_id']) {
            $res = array('error' => 1, 'errortip' => "请选择学生");
            ajax_return($res, $request['language_type']);
        }
        $this->DataControl->delData("smc_integral_stu_couponslog", "student_id='{$request['student_id']}'");
        $data = array();
        $data['student_id'] = 0;
        $data['coupons_activity_name'] = '';
        $data['coupons_compose_package'] = '';
        $data['coupons_lotterytime'] = 0;
        $this->DataControl->updateData("smc_integral_cooperation_coupons", "student_id='{$request['student_id']}'", $data);
        $res = array('error' => 0, 'errortip' => "清除成功");
        ajax_return($res, $request['language_type']);
    }

    //导出福袋中奖学生
    function importFDExcelView()
    {
        $reqeust = Input('get.', "", "trim,addslashes");
        $datawhere = '1';
        if (isset($reqeust['couponslog_type']) && $reqeust['couponslog_type'] !== '') {
            $datawhere .= " and couponslog_type ='{$reqeust['couponslog_type']}' ";
        }
        $dataLsit = $this->DataControl->selectClear("select g.*,t.student_cnname,l.school_cnname,
          (select p.parenter_mobile from smc_student_family as f,smc_parenter as p where f.parenter_id=p.parenter_id  and f.student_id=t.student_id order by f.family_isdefault DESC limit 0,1  ) as  family_mobile
          from smc_integral_stu_couponslog as g
          left join smc_student as t ON t.student_id=g.student_id
          left join smc_school as l ON l.school_id=g.school_id
          where {$datawhere} ");
        if ($dataLsit) {
            $outexcel = array();
            foreach ($dataLsit as $dataVar) {
                $dataarray['school_cnname'] = $dataVar['school_cnname'];
                $dataarray['student_cnname'] = $dataVar['student_cnname'];
                $dataarray['family_mobile'] = $dataVar['family_mobile'];
                $dataarray['couponslog_type'] = $dataVar['couponslog_type'];
                $dataarray['couponslog_createtime'] = date('Y-m-d H:i', $dataVar['couponslog_createtime']);
                $outexcel[] = $dataarray;
            }
        }
        $excelheader = array("学校", "学生姓名", "手机号", "福袋类型", "中奖时间",);
        $excelfields = array('school_cnname', "student_cnname", 'family_mobile', "couponslog_type", "couponslog_createtime");
        $excelname = "福袋抽奖纪录.xlsx";
        if (!is_array($outexcel)) {
            jsbakerror_spl("没有数据！");
            exit;
        }
        query_to_excel($excelheader, $outexcel, $excelfields, $excelname);
        ajax_return(array("error" => 0, "errortip" => "下载完毕!", "bakfuntion" => "okmotify"));
    }

    /**
     * 获取小程序二维码
     * author: ling
     * 对应接口文档 0001
     * Date 2021/1/11 0011
     */
    function getZhuanbanQRcodeView()
    {
        header("Content-type:text/html;charset=utf-8");
        $request = Input('get.', '', 'trim,addslashes');
        $paramarray = array(
            'appid' => self::XcxappId,
            'secret' => self::XcxappSecret,
            'grant_type' => "client_credential"
        );
        $getBakurl = request_by_curl("https://api.weixin.qq.com/cgi-bin/token", dataEncode($paramarray), "GET");
        $json_play = new \Webjson();
        $dataArray = $json_play->decode($getBakurl, "1");
        $access_token = $dataArray['access_token'];
        $qcode = "https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token={$access_token}";
        $scene = "company_id=8888";
        $pageStr = "pages/LuckDraw/main";
        $param = json_encode(array("scene" => $scene, "page" => $pageStr, "width" => '800'));

        //POST参数
        $result = $this->httpRequest($qcode, $param, "POST");
        //生成图片 -- 加上头部 header
        $base64 = base64_encode($result);

        $strImg = "data:image/png" . ";base64," . $base64;
        echo "<image src= $strImg ></image>";
    }

    /**
     * 获取孩子的班级
     */
    function getStuClassApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Scptc\MineModel();
        $result = $Model->getStuClass($request);
        ajax_return($result);
    }

    /**
     * 获取孩子的课表
     */
    function getStuTimeTableApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Scptc\MineModel();
        $result = $Model->getStuTimeTable($request);

        ajax_return($result);
    }

    /**
     * 获取孩子的课表
     */
    function getStuTimeTableJxtApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Scptc\MineModel();
        $result = $Model->getStuTimeTableJxt($request);

        ajax_return($result);
    }

    /**
     * 我的课表课时数
     */
    function getTimeNumApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Scptc\MineModel();
        $result = $Model->getTimeNum($request);

        ajax_return($result);
    }

    /**
     * 获取孩子的课表详情
     */
    function getStuTimeTableDetailApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Scptc\MineModel();
        $result = $Model->getStuTimeTableDetail($request);

        ajax_return($result);
    }

    //新增预约记录
    function addBookingAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        if (!$request['class_id'] || !$request['hour_id'] || !$request['student_id']) {
            $res = array('error' => 1, 'errortip' => "参数缺失", 'result' => array());
            ajax_return($res, $request['language_type']);
        }

        $Model = new \Model\Scptc\MineModel();
        $bool = $Model->addBooking($request);
        if ($bool) {
            $res = array('error' => 0, 'errortip' => '预约成功', 'result' => array());
        } else {
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => array());
        }
        ajax_return($res, $request['language_type']);
    }

    //取消预约
    function cancelBookingAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        if (!$request['class_id'] || !$request['hour_id'] || !$request['student_id']) {
            $res = array('error' => 1, 'errortip' => "参数缺失", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        $Model = new \Model\Scptc\MineModel();
        $bool = $Model->cancelBooking($request);
        if ($bool) {
            $res = array('error' => 0, 'errortip' => '预约取消成功', 'result' => array());
        } else {
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => array());
        }
        ajax_return($res, $request['language_type']);
    }

    /**
     * 获取孩子的标题数据
     */
    function getTitleApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
//        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Scptc\MineModel();
        $result = $Model->getTitle($request);
        ajax_return($result);
    }

    /**
     * 获取确认ip
     */
    function getIpApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
//        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Scptc\MineModel();
        $result = $Model->getIp($request);
        ajax_return($result);
    }

    function confirmClassAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $isKnow = $this->DataControl->getFieldOne("smc_student","student_isknow","student_id = '{$request['student_id']}'");
        if($isKnow['student_isknow'] == '0'){
            ajax_return(array('error' => 1, 'errortip' => "尚未同意协议，无法确认上课"));
        }

        $List = json_decode(stripslashes($request['class']), true);
        foreach ($List as $item) {
            $isset = $this->DataControl->getFieldOne("cmb_trans_transfer", "transfer_id,transfer_status", "hourstudy_id = '{$item['hourstudy_id']}' and student_id = '{$request['student_id']}'");
            $data = array();
            $data['income_isconfirm'] = '1';
            $data['confirm_type'] = '1';
            $data['confirm_ip'] = real_ip();
            $data['confirm_phone'] = $request['phone'];
            $data['confirm_createtime'] = time();

            if ($isset) {
                if ($isset['transfer_status'] <= 1) {
                    $data['transfer_updatetime'] = time();
                    $this->DataControl->updateData('cmb_trans_transfer', "hourstudy_id = '{$item['hourstudy_id']}'", $data);
                }
            } else {
                $data['company_id'] = $request['company_id'];
                $data['school_id'] = $request['school_id'];
                $data['student_id'] = $request['student_id'];
                $data['companies_id'] = $item['companies_id'];
                $data['hourstudy_id'] = $item['hourstudy_id'];
                $data['coursetype_id'] = $item['coursetype_id'];
                $data['coursecat_id'] = $item['coursecat_id'];
                $data['course_id'] = $item['course_id'];
                $data['class_id'] = $item['class_id'];
                $data['hourstudy_id'] = $item['hourstudy_id'];
                $data['transfer_createtime'] = time();
                $this->DataControl->insertData('cmb_trans_transfer', $data);
            }
        }
        ajax_return(array('error' => '0', 'errortip' => "确认成功", 'result' => array()));
    }

    function setPushInfoAction(){
        $request = Input('post.','','trim,addslashes');

        $Model = new \Model\Scptc\MineModel();

        if($Model->error!=1){
            $res = $Model->setPushInfo($request);

            $result = array();
            if ($res) {
                $result["list"] = $res;
                $res = array('error' => 0, 'errortip' => '设置成功', 'result' => $result);
            } else {
                $result["list"] = array();
                $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
            }
        }else{
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res);
    }

    function httpRequest($url, $data = '', $method = 'GET')
    {
        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, 0);
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, 0);
        curl_setopt($curl, CURLOPT_USERAGENT, $_SERVER['HTTP_USER_AGENT']);
        curl_setopt($curl, CURLOPT_FOLLOWLOCATION, 1);
        curl_setopt($curl, CURLOPT_AUTOREFERER, 1);
        if ($method == 'POST') {
            curl_setopt($curl, CURLOPT_POST, 1);
            if ($data != '') {
                curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
            }
        }

        curl_setopt($curl, CURLOPT_TIMEOUT, 30);
        curl_setopt($curl, CURLOPT_HEADER, 0);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
        $result = curl_exec($curl);
        curl_close($curl);
        return $result;
    }

    function studParentBroacastView()
    {
        $request = Input('post.', "", "trim,addslashes");
        $Model = new \Model\Scshop\IntegralModel($request);
        $bool = $Model->studParentBroacast($request);
        if($bool){
            $res = array('error' => 0, 'errortip' => '推送成功');
        }else{
            $res = array('error' => 0, 'errortip' => '已全部推送完成');
        }
        ajax_return($res);

    }

    function WithdrawIntegtralListView(){
        $request = Input('get.', '', 'trim,addslashes');
        $sid = $this->DataControl->getFieldOne("smc_student","student_id","student_branch = '{$request['student_branch']}'");
        $integral = $this->DataControl->getFieldOne("smc_student_virtual_property","property_integralbalance","student_id = '{$sid['student_id']}'");

        if($integral){
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $integral['property_integralbalance']);
            ajax_return($res);
        }else{
            $integral['property_integralbalance'] = '0';
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $integral['property_integralbalance']);

            ajax_return($res);
        }
    }

    //根据手机号获取学生
    function mobileStudentApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'mobileStudent', $request)) {
                $request['tokenstring'] = $pucArray['tokenstring'];
                $sql = "SELECT
                    s.student_id,
                    s.student_branch,
                    s.student_birthday,
                    s.student_cnname,
                    s.student_enname,
                    s.student_img,
                    s.student_sex,
                    s.company_id,
                    c.company_cnname,
                    c.company_logo,
                    l.school_cnname,
                    l.school_shortname,
                    f.parenter_id,
                    l.school_id,
                    l.school_type,
                    f.family_isdefault
                FROM
                    smc_student AS s,
                    smc_student_enrolled AS e,
                    smc_student_family AS f,
                    gmc_company AS c,
                    smc_school AS l
                WHERE
                    s.company_id = '{$request['company_id']}'
                AND s.student_isdel <> '1'
                AND s.student_id = f.student_id
                AND s.student_id = e.student_id
                AND s.company_id = c.company_id
                AND e.school_id = l.school_id
                AND f.family_mobile = '{$request['mobile']}'
                AND e.enrolled_status IN (0, 1)
                AND l.school_type = '1'
                GROUP BY e.student_id";
                $StList = $this->DataControl->selectClear($sql);

                if ($StList) {
                    $result = array();
                    $result["data"] = $StList;
                    ajax_return(array('error' => '0', 'errortip' => '获取孩子列表成功', 'result' => $result));
                } else {
                    $result = array();
                    $result["data"] = array();
                    ajax_return(array('error' => '1', 'errortip' => '获取孩子列表失败', 'result' => $result));
                }
            }else{
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //获取每个学生积分总额
    function stuIntegralAllApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'stuIntegralAll', $request)) {
                $request['tokenstring'] = $pucArray['tokenstring'];
                $intgral = $this->DataControl->getFieldOne("smc_student_virtual_property","property_integralbalance","student_id = '{$request['student_id']}'");
                if(!$intgral){
                    $intgral['property_integralbalance'] = '0';
                }
                ajax_return(array('error' => '0', 'errortip' => '获取学生积分成功', 'result' => $intgral['property_integralbalance']));
            }else{
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //学生积分消费
    function stuReduceIntegralAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'stuReduceIntegral', $request)) {
                $request['tokenstring'] = $pucArray['tokenstring'];
                $BalanceModel = new \Model\Smc\BalanceModel();
                $bool = $BalanceModel->reduceStuIntegral($request['student_id'], $request['integral'], 0, '奇趣星球消费积分', 0, $request['parenter_id'], '奇趣星球消费积分', $request['note'], time(), 0 ,0);
                if ($bool) {
                    ajax_return(array('error' => '0', 'errortip' => '消费积分成功', 'result' => $bool));
                } else {
                    ajax_return(array('error' => '1', 'errortip' => '消费积分失败'));
                }
            }else{
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //学生积分获取
    function stuAddIntegralAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'stuAddIntegral', $request)) {
                $request['tokenstring'] = $pucArray['tokenstring'];
                $BalanceModel = new \Model\Smc\BalanceModel();
                $bool = $BalanceModel->addStuIntegral($request['student_id'], $request['integral'], 0, '奇趣星球获取积分', 0, $request['parenter_id'], '奇趣星球获取积分', $request['note'],time(), 0 ,0);
                if ($bool) {
                    ajax_return(array('error' => '0', 'errortip' => '积分获取成功'));
                } else {
                    ajax_return(array('error' => '1', 'errortip' => '积分获取失败'));
                }
            }else{
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    function aView(){
        $a = real_ip();
        var_dump($a);
    }

    //我已知晓
    function stuKnowAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);

        $data = array();
        $data['student_isknow'] = '1';
        $bool = $this->DataControl->updateData("smc_student","student_id = '{$request['student_id']}'",$data);

        if ($bool) {
            ajax_return(array('error' => '0', 'errortip' => '成功'));
        } else {

            ajax_return(array('error' => '1', 'errortip' => '失败'));
        }
    }

    function getipView(){
        echo real_ip();
    }

    function getAgreementApi(){
        $text = '
<div class="protocal">
    <style> 
  .bold {
    color: #333333;
    font-weight: 600;
  }
  .mb8 {
    margin-bottom: 16rpx;
  }
  .mb16 {
    margin-bottom: 32rpx;
  }
  .tc {
    text-align: center;
  }
  .fs11 {
    font-size: 22rpx;
  }</style>
    <p class="bold mb8 tc">预收费管理三方协议</p >
    <p class="mb16">
      <span
        >鉴于乙方（教培机构）向您提供培训服务（以下简称“基础交易”）需要，您和乙方共同委托甲方（招商银行股份有限公司上海分行）提供个人存款资金监管服务，为此需要签署本协议。</span
      >
      <span class="bold"
        >您通过各类售课渠道相关页面点击接受本协议，即表示您有效签署本协议，知悉并同意接受本协议的全部内容及所展示与本协议有关的各项业务规则，并认可三方关于个人存款资金监管业务的相关约定、记录均以招商银行系统所记载、存储的为准。在接受本协议前，请您仔细阅读本协议的全部内容，尤其是黑体加粗的条款。如果您不同意本协议的任何内容，或者无法准确理解相关条款，请不要进行后续操作，您可联系招商银行客服热线95555，以便为您解释和说明。</span
      >
    </p >
    <p class="fs11 bold mb8">第一条 监管范围及监管账户</p >
    <p>
      （一）个人存款资金监管：指甲方接受乙方和您共同委托，按照本协议约定对您指定个人账户（下称“消费者监管专户”）内向乙方预付的培训费（以下简称“培训资金”或“监管资金”）进行监管，协助乙方和您对监管资金进行冻结、解冻、扣划等操作。
    </p >
    <p>
      （二）个人存款资金监管业务受理币种为人民币，在指定个人借记卡账户项下实现金额冻结、解冻及扣划等操作。
    </p >
    <p>
      （三）资金监管期限：是指甲方接受乙方和您的委托，依据本协议对消费者监管专户内监管资金进行监管的期限，在此期间监管资金将由甲方进行冻结。
    </p >
    <p>
      （四）资金监管期限内，甲方对消费者监管专户内的培训资金按照培训资金存款性质及其存期对应的利率向您计付利息。
    </p >
    <p>
      （五）预收费专用账户：乙方根据与甲方签署的《上海市校外培训机构预收费管理协议》（下称“监管协议”），在甲方开立的用于接收您支付的培训资金的账户。
    </p >
    <p>
      （六）自有资金账户：乙方根据监管协议，在甲方开立的用于接收从预收费专用账户划扣的培训资金以及存储其他资金（如有）的账户。
    </p >
    <p>（七）甲方系统：甲方教培云资金管理平台。</p >
    <p class="mb16">
      （八）服务合同：您与乙方签署的《中小学生校外培训服务合同》。
    </p >
    <p class="fs11 bold mb8">第二条 资金监管</p >
    <p>（一）个人存款资金监管业务申请和冻结</p >
    <p>
      1、如您向乙方购买课时不超过三个月或60课时，且预付培训费不超过对应课程开始前的一个月，在您签署本协议后，你将通过甲方提供的服务渠道将培训资金直接支付至预收费专用账户。
    </p >
    <p>
      2、如您向乙方购买课时超过三个月或60课时，或预付培训费超过对应课程开始前的一个月，在您签署本协议后，系统将会自动跳转至招商银行个人存款资金监管业务申请页面，您需根据页面提示选择或填写相关信息，包括但不限于业务类型、乙方名称、申请人姓名、身份证件类型、证件号码、手机号码、指定银行账号等。
    </p >
    <p class="bold">
      3、您办理个人存款资金监管业务时登入账号、输入密码或进行人脸识别是甲方确认您身份的依据。任何使用您账号、密码及/或人脸识别验证通过后在招商银行手机银行项下所做的操作均视为您本人的操作，由此产生的责任和后果均由您承担。
    </p >
    <p class="bold">
      4、您登录招商银行手机银行提交个人存款资金监管业务申请时，由甲方根据您填写或确认的指定银行账号以及乙方传输的冻结金额进行冻结。您同意并授权甲方将您申请办理个人存款资金监管业务的相关信息（包括但不限于申请人姓名、身份证件类型、证件号码、手机号码、指定银行账号等）推送至甲方系统，由乙方登录甲方系统进行信息查询、业务审批通过或驳回等操作。
    </p >
    <p class="mb8">
      5、冻结金额是根据服务合同约定并由乙方通过甲方系统向甲方传输的金额，自您登录招商银行手机银行成功提交个人存款资金监管业务申请之时开始冻结，并按本协议约定进行解冻或扣划等操作。
    </p >
    <p class="bold">（二）个人存款资金监管业务解冻及扣划</p >
    <p></p >
    <p class="bold"></p >
    <p></p >
    <p></p >
    <p></p >
    <p></p >
    <p></p >
    <p></p >
    <p></p >
    <p></p >
    <p></p >
    <p></p >
    <p></p >
    <p></p >
    <p></p >
    <p></p >
    <p></p >
    <p></p >
    <p></p >
    <p></p >
    <p></p >
    <p></p >
    <p></p >
    <p></p >
    <p></p >
    <p></p >
    <p></p >
    <p></p >
    <p></p >
    <p></p >
    <p></p >
    <p></p >
    <p></p >
    <p></p >
    <p></p >
    <p></p >
    <p></p >
    <p></p >
    <p></p >
    <p></p >
    <p></p >
    <p></p >
    <p></p >
    <p></p >
    <p></p >
  </div>';
        ajax_return(array('error' => '0', 'errortip' => '成功','result' => $text));
    }

    function integralGainListView(){
        $request = Input('get.', '', 'trim,addslashes,strip_tags');
        $sid = $this->DataControl->getFieldOne("smc_student","student_id","student_branch = '{$request['student_branch']}'");
        $request['student_id'] = $sid['student_id'];
        $this->ThisVerify($request);//验证账户
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;
        $list = $this->DataControl->selectClear("select l.integrallog_playname,s.student_cnname,l.integrallog_playamount,FROM_UNIXTIME(l.integrallog_time,'%Y.%m.%d %H:%i') as integrallog_time,s.student_img,s.student_sex from smc_student_integrallog as l left join smc_student as s on s.student_id = l.student_id where s.student_branch = '{$request['student_branch']}' and l.integrallog_playclass = '+' order by l.integrallog_time DESC LIMIT {$pagestart},{$num}");
        if($list){
            foreach($list as &$value){
                $value['integrallog_playamount'] = sprintf("%.1f", $value['integrallog_playamount']);
            }
            ajax_return(array('error' => '0', 'errortip' => '成功','result' => $list));
        }else{
            ajax_return(array('error' => '0', 'errortip' => '暂无数据','result' => array()));
        }

    }

    function addparenterView(){
        $request = Input('get.', '', 'trim,addslashes,strip_tags');

        $sid = $this->DataControl->getFieldOne("smc_student","student_id","student_branch = '{$request['student_branch']}'");

        $parentOne=$this->DataControl->getFieldOne("smc_parenter","parenter_id","parenter_mobile='{$request['parenter_mobile']}'");

        if($parentOne){
            $parenter_id=$parentOne['parenter_id'];
        }else{
            $data=array();
            $data['parenter_mobile']=trim($request['parenter_mobile']);
            $data['parenter_pass'] = md5(substr(trim($request['parenter_mobile']),-6));
            $data['parenter_bakpass'] = substr(trim($request['parenter_mobile']),-6);
            $data['parenter_addtime']=time();
            $parenter_id=$this->DataControl->insertData("smc_parenter",$data);
        }

        if(!$this->DataControl->getFieldOne("smc_student_family","family_id","parenter_id='{$parenter_id}' and student_id='{$sid['student_id']}'")){
            $data = array();
            $data['student_id'] = $sid['student_id'];
            $data['parenter_id'] = $parenter_id;
//            $data['company_id'] = '8888';
            $data['family_relation'] = $request['family_relation'];
            $data['family_mobile'] = trim($request['parenter_mobile']);

//            if(!$this->DataControl->getFieldOne("smc_student_family","family_id","parenter_id='{$parenter_id}' and student_id='{$sid['student_id']}' and family_isdefault='1'")){
//                $data['family_isdefault']=1;
//            }

            $this->DataControl->insertData("smc_student_family", $data);
        }
        return true;
    }

}
