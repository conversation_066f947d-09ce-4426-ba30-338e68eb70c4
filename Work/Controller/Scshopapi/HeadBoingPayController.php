<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/4/27
 * Time: 17:49
 */

namespace Work\Controller\Scshopapi;


class HeadBoingPayController extends viewTpl{
    public $u;
    public $t;
    public $c;
    public $errortip = false;

    function __construct() {
        parent::__construct ();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
    }

    //招行发起支付
    function OrderPayView(){
        $request = Input('get.','','trim,addslashes');
        $paymenttype = $request['paymenttype'];
        $pay_pid = $request['paypid'];

        $orderPay = $this->DataControl->getFieldOne("smc_payfee_order_pay","pay_id,order_pid,pay_pid","pay_pid='{$pay_pid}'");
        $orderOne = $this->DataControl->getFieldOne("smc_payfee_order","company_id,order_pid","order_pid='{$orderPay['order_pid']}'");
        $PayModel = new \Model\Api\HeadboingPayModel($orderOne['company_id']);
        $result = $PayModel->OrderPay($pay_pid,$paymenttype,$request['openid']);

        if($PayModel->error){
            $res = array('error' => 1, 'errortip' => $PayModel->errortip, 'result' =>$result);
        }else{
            $res = array('error' =>$PayModel->error, 'errortip' => $PayModel->errortip, 'result' =>$result);
        }
        ajax_return($res,$request['language_type']);
    }

    //招行支付对接
    function OrderBakView(){

        $eventJson = $_REQUEST['event'];
        $eventArray = json_decode($eventJson,1);

        //支付日志
        $bakjson = json_encode($_REQUEST,JSON_UNESCAPED_UNICODE);
        $data = array();
        $data['errorlog_apiurl'] = "HeadBoingPay/OrderBak";
        $data['errorlog_parameter'] = $bakjson;
        $data['errorlog_time'] = time();
        $this->DataControl->insertData("smc_api_errorlog",$data);

        $orderPay = $this->DataControl->getFieldOne("smc_payfee_order_pay","pay_id,order_pid,pay_pid","pay_pid='{$eventArray['out_order_no']}'");
        $orderOne = $this->DataControl->getFieldOne("smc_payfee_order","company_id,order_pid","order_pid='{$orderPay['order_pid']}'");
        $PayModel = new \Model\Api\HeadboingPayModel($orderOne['company_id']);
        $PayModel->BoingPayBak($_REQUEST);
        exit;
    }


    //已取消订单发起招行撤销操作
    function OrderPayCancelView(){
        $request = Input('get.','','trim,addslashes');
        $paymenttype = $request['paymenttype'];
        $pay_pid = $request['paypid'];

        $orderPay = $this->DataControl->getFieldOne("smc_payfee_order_pay","pay_id,order_pid,pay_pid,pay_issuccess","pay_pid='{$pay_pid}'");
        $orderOne = $this->DataControl->getFieldOne("smc_payfee_order","company_id,order_pid,order_status","order_pid='{$orderPay['order_pid']}'");

        if($orderPay['pay_issuccess'] != '-1'){
            $res = array('error' => 1, 'errortip' => "支付订单并未失效！", 'result' =>array());
            ajax_return($res,$request['language_type']);
        }

        $PayModel = new \Model\Api\HeadboingPayModel($orderOne['company_id']);
        $result = $PayModel->OrderPayCancel($pay_pid);

        if($PayModel->error == false){
            $res = array('error' => 1, 'errortip' => $PayModel->errortip, 'result' =>$result);
        }else{
            $res = array('error' =>$PayModel->error, 'errortip' => $PayModel->errortip, 'result' =>$result);
        }
        ajax_return($res,$request['language_type']);
    }
}