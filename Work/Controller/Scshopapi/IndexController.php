<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/4/16
 * Time: 11:44
 */

namespace Work\Controller\Scshopapi;

use Model\Scshop\IndexModel;

class IndexController extends viewTpl{
    public $u;
    public $t;
    public $c;
    public $visitType="api";
    public $DataControl;

    //预加载处理类
    function __construct() {
        parent::__construct ();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
        $this->api = $this->router->getApi();

        //数据库操作
        $this->DataControl = new \Dbmysql();
    }

    //选择校区
    function SchoolView(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Scshop\IndexModel($request);
        $dataList = $Model->SchoolApi($request);

        $result = array();
        if($dataList){
            $result['list'] = $dataList;
            $res = array('error' => 0, 'errortip' => '获取校区信息', 'result' => $result);
        }else{
            $result['list'] = array();
            $res = array('error' => 1, 'errortip' => '暂无校区信息', 'result' => $result);
        }
        ajax_return($res);
    }

    //微商城首页
    function HomeView(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Scshop\IndexModel($request);
        $dataList = $Model->HomePage($request);

        $result = array();
        if($dataList){
            $result['list'] = $dataList;
            $res = array('error' => 0, 'errortip' => '获取首页商品信息', 'result' => $result);
        }else{
            $result['list'] = array();
            $res = array('error' => 1, 'errortip' => '暂无商品信息', 'result' => $result);
        }
        ajax_return($res);
    }

    //获取推荐商品
    function RecommendGoodsView(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Scshop\IndexModel($request);
        $dataList = $Model->RecommendGoods($request);

        $result = array();
        $result["allnum"] = $dataList['allnums'];
        if($dataList['list']){
            $result['list'] = $dataList['list'];
            $res = array('error' => 0, 'errortip' => '获取推荐商品信息', 'result' => $result);
        }else{
            $result['list'] = array();
            $res = array('error' => 1, 'errortip' => '暂无推荐商品信息', 'result' => $result);
        }
        ajax_return($res);
    }

    //奇瓦迪商铺/奇趣教材
    function ShopView(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Scshop\IndexModel($request);
        $dataList = $Model->ShopList($request);

        $result = array();
        $result["allnum"] = $dataList['allnums'];
        if($dataList['list']){
            $result["list"] = $dataList['list'];
            $res = array('error' => 0, 'errortip' => '获取商品列表成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无商品', 'result' => $result);
        }
        ajax_return($res);
    }

    //吉的堡学院
    function JDBCollegeView(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Scshop\IndexModel($request);
        $dataList = $Model->ClassCollege($request);
        if($dataList){
            $res = array('error' => 0, 'errortip' => '获取商品列表成功', 'result' => $dataList);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无商品', 'result' => $result);
        }
        ajax_return($res);
    }


    //活动赛事
    function ActivityMatchView(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Scshop\IndexModel($request);
        $dataList = $Model->ActivityMatch($request);

        $result = array();
        $result["allnum"] = $dataList['allnums'];
        if($dataList['list']){
            $result["list"] = $dataList['list'];
            $res = array('error' => 0, 'errortip' => '获取商品列表成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无商品', 'result' => $result);
        }
        ajax_return($res);
    }

    //商品详情
    function goodsLookView(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Scshop\IndexModel($request);
        $dataList = $Model->goodsLook($request);

        $result = array();
        if($dataList){
            $result["list"] = $dataList['list'];
            $result['cartnum'] = $dataList['allnums'];
            $res = array('error' => 0, 'errortip' => '获取商品信息成功', 'result' => $result);
        } else {
            $res = array('error' => 1, 'errortip' => '暂无商品信息', 'result' => array());
        }
        ajax_return($res);
    }

    //搜索历史
    function SearchHistoryView(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $History = $this->DataControl->selectClear("SELECT DISTINCT searchlog_keyword FROM shop_parenter_searchlog
WHERE company_id = '{$request['company_id']}' AND parenter_id = '{$request['parenter_id']}' AND student_id = '{$request['student_id']}' ORDER BY searchlog_createtime DESC LIMIT 30");

        $result = array();
        if($History){
            $result["list"] = $History;
            $res = array('error' => 0, 'errortip' => '获取搜索历史信息成功', 'result' => $result);
        } else {
            $res = array('error' => 1, 'errortip' => '暂无搜索历史信息', 'result' => array());
        }
        ajax_return($res);
    }

    //获取微商城首页分类
    function HomeCategoryView(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $datalist = $this->DataControl->selectClear("SELECT c.*,(SELECT COUNT(ct.cart_id) FROM shop_cart as ct WHERE ct.company_id = '{$request['company_id']}' AND ct.school_id = '{$request['school_id']}' AND ct.parenter_id = '{$request['parenter_id']}' AND ct.student_id = '{$request['student_id']}'
                                                       AND ct.sellgoods_id IN (SELECT g.sellgoods_id FROM shop_sellgoods as g WHERE g.class_id = c.class_id)) as cart_num
                                                       FROM shop_code_class as c WHERE c.company_id = '{$request['company_id']}' AND c.class_isopen = '1'");
        if(!$datalist){
            $datalist = array();
        }

        $result = array();
        if($datalist){
            $result["list"] = $datalist;
            $res = array('error' => 0, 'errortip' => '获取微商城首页分类成功', 'result' => $result);
        } else {
            $res = array('error' => 1, 'errortip' => '暂无微商城首页分类', 'result' => array());
        }
        ajax_return($res);
    }

    //获取商品分类
    function CategoryView(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $datalist = $this->DataControl->selectClear("SELECT c.* FROM shop_code_category as c
WHERE c.company_id = '{$request['company_id']}' AND c.category_class = '{$request['class_id']}' ORDER BY c.category_sort ASC");
        if(!$datalist){
            $datalist = array();
        }

        $result = array();
        if($datalist){
            $result["list"] = $datalist;
            $res = array('error' => 0, 'errortip' => '获取商品分类成功', 'result' => $result);
        } else {
            $res = array('error' => 1, 'errortip' => '暂无商品分类', 'result' => array());
        }
        ajax_return($res);
    }

    //搜索-输入内容
    function SearchGoodsView(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Scshop\IndexModel($request);
        $dataList = $Model->SearchGoods($request);

        $result = array();
        $result['cartnum'] = $dataList['allnums'];
        if($dataList["list"]){
            $result["list"] = $dataList['list'];
            $res = array('error' => 0, 'errortip' => '获取商品信息成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无商品信息', 'result' => $result);
        }
        ajax_return($res);
    }

    //订单消息
    function OrderMessageView(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Scshop\IndexModel($request);
        $dataList = $Model->OrderMessage($request);

        $result = array();
        $result['cartnum'] = $dataList['allnums'];
        if($dataList["list"]){
            $result["list"] = $dataList['list'];
            $res = array('error' => 0, 'errortip' => '获取订单消息成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无订单消息', 'result' => $result);
        }
        ajax_return($res);
    }

    //获取收费协议
    function GetProtocolView()
    {
        $request = Input('get.','','trim,addslashes');

        $goods_class = $this->DataControl->getFieldOne('shop_sellgoods', 'sellgoods_type,course_id,coursepacks_id', "sellgoods_id='{$request['sellgoods_id']}' and (sellgoods_type='0' or sellgoods_type='1')");
        if ($goods_class) {
            if ($goods_class['sellgoods_type'] == 0) {
                $goods_protocol = $this->DataControl->selectOne("SELECT cc.coursetype_protocol FROM smc_course as co LEFT JOIN smc_code_coursetype as cc ON cc.coursetype_id = co.coursetype_id WHERE co.course_id = '{$goods_class['course_id']}'");
            } else {
                $goods_protocol = $this->DataControl->selectOne("SELECT cc.coursetype_protocol FROM smc_fee_warehouse_courses as c LEFT JOIN smc_course as co ON co.course_id = c.course_id LEFT JOIN smc_code_coursetype as cc ON cc.coursetype_id = co.coursetype_id WHERE c.coursepacks_id = '{$goods_class['coursepacks_id']}'");
            }
            if ($goods_protocol) {
                ajax_return(array('error' => 0, 'errortip' => "获取协议成功", 'result' => str_replace("\r\n","",$goods_protocol['coursetype_protocol']), "tokeninc" => "1"));
            } else {
                ajax_return(array('error' => 1, 'errortip' => "暂无数据", 'result' => array(), "tokeninc" => 1));
            }
        } else {
            ajax_return(array('error' => 1, 'errortip' => "此商品无协议", 'result' => array(), "tokeninc" => 1));
        }
    }

    //获取退费协议
    function ReturnProtocolView()
    {
        $request = Input('get.','','trim,addslashes');

        $course_id = $this->DataControl->getFieldOne("smc_payfee_order_course","course_id","order_pid = '{$request['order_pid']}'");
        $coursetype_id = $this->DataControl->getFieldOne("smc_course","coursetype_id","course_id = '{$course_id['course_id']}'");

        $protocol = $this->DataControl->getFieldOne("smc_fee_treaty","treaty_protocol","coursetype_id = '{$coursetype_id['coursetype_id']}'");
        if ($protocol) {
            ajax_return(array('error' => 0, 'errortip' => "获取退费协议成功", 'result' => $protocol['treaty_protocol'], "tokeninc" => "1"));
        } else {
            ajax_return(array('error' => 1, 'errortip' => "暂无数据", 'result' => array(), "tokeninc" => 1));
        }
    }

    //热搜
    function HotSearchView(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new IndexModel($request);
        $dataList = $Model->HotSearch($request);

        $result = array();
        if($dataList){
            $result["list"] = $dataList;
            $res = array('error' => 0, 'errortip' => '获取热搜成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无热搜', 'result' => $result);
        }
        ajax_return($res);
    }

    //首页Banner
    function BannerView(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $imgList = $this->DataControl->selectClear("SELECT banner_img,banner_outurl FROM shop_banner WHERE company_id = '{$request['company_id']}' ORDER BY banner_sort ASC");
        ajax_return(array('error' => 0, 'errortip' => "获取Banner成功", 'result' => $imgList));
    }
}