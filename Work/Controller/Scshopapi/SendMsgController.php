<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2018/12/29
 * Time: 10:09
 */

namespace Work\Controller\Scshopapi;

class DdzjChatController extends viewTpl
{
    public $appId = 'wx45d70456847ac845';
    public $appSecret = '7cf208b45cd418d073d7def4d6f63489';

    function httpPost($data, $url)
    {
        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE);
        curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (compatible; MSIE 5.01; Windows NT 5.0)');
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
        curl_setopt($ch, CURLOPT_AUTOREFERER, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        $tmpInfo = curl_exec($ch);
        if (curl_errno($ch)) {
            return curl_errno($ch);
        }
        curl_close($ch);
        return $tmpInfo;
    }

    function httpGet($url)
    {
        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_TIMEOUT, 500);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);  // 视频教程中 这两个设为 true 我使用时会报错，改成 0 正常
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);

        /*  为保证第三方服务器与微信服务器之间数据传输的安全性，所有微信接口采用https方式调用，必须使用上面2行代码打开ssl安全校验。
            如果在部署过程中代码在此处验证失败，请到 httpL//curl.haxx.se/ca/cacert.pem 下载新的证书判别文件。

            CURLOPT_SSL_VERIFYHOST 的值

            设为 0 表示 不检查证书
            设为 1 表示 检查证书中是否有CN(common name)字段
            设为 2 表示 在1的基础上校验当前的域名是否与CN匹配  */

        $tmpInfo = curl_exec($ch);
        curl_close($ch);
        return $tmpInfo;
    }

    //
    function ComfirmCheck($openid, $keyword1, $keyword2, $keyword3, $formid)
    {
        $data = '{
        "touser"     : "' . $openid . '",
        "template_id": "_aRDpPm1SRq-4ieTn4LhFfD7SCdEPY98YUWOk7dm7q8",
        "form_id"    : "' . $formid . '",
        "page"       : "index",
        "data": {
            "keyword1": {
              "value": "' . $keyword1 . '", 
              "color": "#173177"
            }, 
            "keyword2": {
              "value": "' . $keyword2 . '", 
              "color": "#173177"
            }, 
            "keyword3": {
              "value": "' . $keyword3 . '", 
              "color": "#173177"
            } 
        },
        "emphasis_keyword": "keyword1.DATA" 
    }';
        return $this->SendWeixinMis($data, "班级通知1111");
    }

    //2
    function ComfirmCheck2($openid, $keyword1, $keyword2, $keyword3, $formid)
    {
        $data = '{
        "touser"     : "' . $openid . '",
        "template_id": "_aRDpPm1SRq-4ieTn4LhFfD7SCdEPY98YUWOk7dm7q8",
        "form_id"    : "' . $formid . '",
        "page"       : "index",
        "data": {
            "keyword1": {
              "value": "' . $keyword1 . '", 
              "color": "#173177"
            }, 
            "keyword2": {
              "value": "' . $keyword2 . '", 
              "color": "#173177"
            }, 
            "keyword3": {
              "value": "' . $keyword3 . '", 
              "color": "#173177"
            } 
        },
        "emphasis_keyword": "keyword1.DATA" 
    }';
        return $this->SendWeixinMis($data, "班级通知1111");
    }




    function SendWeixinMis($data, $log_type = '', $student_id = '0', $member_id = '0')
    {
        $url = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=". $this->appId ."&secret=". $this->appSecret ."";

        $result = $this->httpGet($url);
        print($result . "\n");

        $arr = json_decode($result, true);
        $token = $arr['access_token'];

        $url = "https://api.weixin.qq.com/cgi-bin/message/wxopen/template/send?access_token={$token}";

        $result = $this->httpPost($data, $url);
        print($result . "\n");

        $json_play = new \Webjson();
        $retueninfo = $json_play->decode($result, "1");

        var_dump($retueninfo);die();





        if ($retueninfo['errmsg'] == 'ok') {
            $date = array();
            $date['student_id'] = 1;
            $date['parenter_id'] = 1;
            $date['staffer_id'] = 1;
            $date['log_type'] = $log_type;
            $date['log_status'] = 1;
            $date['log_content'] = $data;
            $date['log_addtime'] = time();
            $this->DataControl->insertData("ptc_wxsend_log", $date);
            return true;
        } else {
            $date = array();
            $date['student_id'] = 2;
            $date['parenter_id'] = 2;
            $date['staffer_id'] = 2;
            $date['log_type'] = $log_type;
            $date['log_status'] = 0;
            $date['log_errmsg'] = $result;
            $date['log_content'] = $data;
            $date['log_addtime'] = time();
            $this->DataControl->insertData("ptc_wxsend_log", $date);
            return false;
        }
    }


}

