<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2018/12/29
 * Time: 10:09
 */

namespace Work\Controller\Scshopapi;


class ScHomeController extends viewTpl
{
    public $u;
    public $t;
    public $c;
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";

    const XcxappId = 'wx45d70456847ac845';
    const XcxappSecret = '7cf208b45cd418d073d7def4d6f63489';

    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
    }

    //第三方授权访问权限校验
    function UserVerify($paramArray)
    {
        $apiuserOne = $this->DataControl->getFieldOne('imc_apiuser', "apiuser_id,apiuser_aeskey,apiuser_aesiv", "apiuser_code = '{$paramArray['apiuser_code']}'");
        if (!$apiuserOne) {
            $this->errortip = "未查询到您的授权信息";
            $this->error = true;
            return false;
        }

        if (isset($paramArray['apiuser_aeskey']) && $paramArray['apiuser_aeskey'] !== '' && isset($paramArray['apiuser_aesiv']) && $paramArray['apiuser_aesiv'] !== '') {
            if ($apiuserOne['apiuser_aeskey'] == $paramArray['apiuser_aeskey'] && $apiuserOne['apiuser_aesiv'] == $paramArray['apiuser_aesiv']) {
                $baseOne = array();
                $baseOne['apiuser_id'] = $apiuserOne['apiuser_id'];
                $companyOne = $this->DataControl->selectOne("select company_id,company_cnname from gmc_company WHERE company_id = '{$paramArray['company_id']}' limit 0,1");
                if ($companyOne) {
                    $baseOne['company_id'] = $companyOne['company_id'];
                    return $baseOne;
                } else {
                    $this->errortip = '你的授权集团编号错误，请确认编号正确';
                    $this->error = true;
                    return false;
                }
            } else {
                $this->errortip = "你的授权秘钥及偏移值不正确，{$paramArray['apiuser_aeskey']}-{$paramArray['apiuser_aesiv']}";
                $this->error = true;
                return false;
            }
        }

        if (!isset($paramArray['timesteps']) || $paramArray['timesteps'] == '') {
            $this->errortip = "请传入授权时间";
            $this->error = true;
            return false;
        }

        if ($paramArray['timesteps'] + 60 * 5 < time() || $paramArray['timesteps'] - 60 > time()) {
            $maxtimes = date("Y-m-d H:i:s", $paramArray['timesteps'] + 60 * 5);
            $this->errortip = "授权时间{$maxtimes}已过期5分钟，请确认连接及时性";//,{$timesteps}--{$jmsting}
            $this->error = true;
            return false;
        }

        $aes = new \Aesencdec($apiuserOne['apiuser_aeskey'], $apiuserOne['apiuser_aesiv']);
        $xssting = $aes->decrypt($this->stringReplace($paramArray['veytoken']));//解密
        if ($paramJson = json_decode($xssting, 1)) {//转化为数组
            if ((string)$paramJson['timesteps'] !== trim($paramArray['timesteps'])) {
                $this->errortip = '授权时间和连接时间不一致';
                $this->error = true;
                return false;
            }
            $baseOne = array();
            $baseOne['apiuser_id'] = $apiuserOne['apiuser_id'];
            $baseOne['tokenstring'] = $xssting;
            $companyOne = $this->DataControl->selectOne("select company_id,company_cnname from gmc_company WHERE company_id = '{$paramJson['company_id']}' limit 0,1");
            if ($companyOne) {
                $baseOne['company_id'] = $companyOne['company_id'];
                return $baseOne;
            } else {
                $this->errortip = '你的授权集团编号错误，请确认编号正确';
                $this->error = true;
                return false;
            }
        } else {
            $this->errortip = '数据机密信息传输有误，请检查！';
            $this->error = true;
            return false;
        }
    }

    function VeryModelNums($apiuser_id, $apimodule_code, $paramArray)
    {
        $request = Input('get.', '', 'trim,addslashes');
        $apimoduleOne = $this->DataControl->getFieldOne('imc_apiuser_apimodule'
            , "apimodule_id,apiuser_id,apimodule_name,apimodule_nums", "apiuser_id = '{$apiuser_id}' AND apimodule_code = '{$apimodule_code}'");
        $stattTimes = strtotime(date("Y-m-d"));
        $apilogOne = $this->DataControl->selectOne("SELECT COUNT(l.apilog_id) AS anums FROM imc_apiuser_apilog AS l
WHERE l.apimodule_id = '{$apimoduleOne['apimodule_id']}' AND l.apilog_createtime > '{$stattTimes}'");
        if ($apilogOne['anums'] > $apimoduleOne['apimodule_nums']) {
            $this->errortip = "您接口{$apimoduleOne['apimodule_name']}的本日最大授权次数{$apimoduleOne['apimodule_nums']}已消耗完毕！";
            $this->error = true;
            return false;
        } else {
            $data = array();
            $data['apiuser_id'] = $apimoduleOne['apiuser_id'];
            $data['apimodule_id'] = $apimoduleOne['apimodule_id'];
            $data['apilog_posturl'] = "https://api.kedingdang.com/{$request['u']}/{$request['t']}";
            $data['apilog_posttype'] = 'GET';
            $data['apilog_postorgjson'] = http_build_query($paramArray);
            $data['apilog_postjson'] = $paramArray['tokenstring'];
            $data['apilog_ip'] = real_ip();
            $data['apilog_createtime'] = time();
            $this->DataControl->insertData("imc_apiuser_apilog", $data);
            return true;
        }
    }

    //通过学生编号获取学生班级记录
    function studentClassApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'studentClass', $request)) {
                $request['tokenstring'] = $pucArray['tokenstring'];
                $sid = $this->DataControl->getFieldOne("smc_student", "student_id", "student_branch = '{$request['student_branch']}'");
                $sql = "
                SELECT
                    sc.school_cnname,
                    sc.school_branch,
                    c.class_cnname,
                    c.class_enname,
                    c.class_id,
                    c.class_branch,
                    c.class_stdate,
                    c.class_enddate,
                    c.class_timestr,
                    ct.coursetype_cnname,
                    ct.coursetype_branch,
                    ca.coursecat_cnname,
                    ca.coursecat_branch,
                    CASE c.class_status WHEN 0 THEN '待开班' WHEN 1 THEN '进行中' WHEN -1 THEN '已结束'END AS class_status,
                    CASE ss.study_isreading WHEN 0 THEN '不在读' WHEN 1 THEN '在读' WHEN -1 THEN '已结束'END AS study_isreading,
                    cb.coursebalance_issupervise,
                       (select count(sch.hour_id) from smc_class_hour as sch where sch.course_id=c.course_id and sch.class_id=c.class_id and sch.hour_ischecking=1) as num,co.course_classnum,
                    c.school_id   
                FROM
                    smc_student_study AS ss
                    LEFT JOIN smc_class AS c ON c.class_id = ss.class_id
                    LEFT JOIN smc_school AS sc ON ss.school_id = sc.school_id
                    LEFT JOIN smc_course AS co ON c.course_id = co.course_id
                    LEFT JOIN smc_code_coursecat AS ca ON ca.coursecat_id = co.coursecat_id
                    LEFT JOIN smc_code_coursetype AS ct ON ct.coursetype_id = ct.coursetype_id 
                    LEFT JOIN smc_student_coursebalance AS cb ON cb.course_id = co.course_id and cb.student_id = ss.student_id
                WHERE
                    ss.student_id = '{$sid['student_id']}'
                    GROUP BY c.class_id
            ";
                $ClList = $this->DataControl->selectClear($sql);

                if($ClList){
                    foreach($ClList as &$val){
                        $num = $this->DataControl->selectOne("select hour_lessontimes from smc_class_hour where class_id = '{$val['class_id']}' and hour_ischecking = '1' order by hour_lessontimes DESC");
                        $val['num'] = $num['hour_lessontimes'];
                        if(!$num['hour_lessontimes']){
                            $val['num'] = 0;
                        }
                        $classlessonList = $this->DataControl->selectClear("SELECT l.lessonplan_id,l.lessonplan_week, l.lessonplan_starttime, l.lessonplan_endtime,c.classroom_cnname,s.staffer_cnname,s.staffer_enname
FROM smc_class_lessonplan AS l
LEFT JOIN smc_staffer AS s ON s.staffer_id = l.staffer_id
LEFT JOIN smc_classroom AS c ON c.classroom_id = l.classroom_id
WHERE l.class_id = '{$val['class_id']}' order by l.lessonplan_id ASC");
                        $classtimestr = "";

                        if ($classlessonList) {
                            foreach ($classlessonList as $classlessonOne) {
                                $classtimestr .= $classlessonOne['lessonplan_week'] . " " . $classlessonOne['lessonplan_starttime'] . "~" . $classlessonOne['lessonplan_endtime'] . ",";
                            }
                        }
                        $val['class_timestr'] = trim($classtimestr,",");
                    }
                }


                if ($ClList) {
                    $result = array();
                    $result["data"] = $ClList;
                    ajax_return(array('error' => '0', 'errortip' => '获取班级记录成功', 'result' => $result));
                } else {
                    $result = array();
                    $result["data"] = array();
                    ajax_return(array('error' => '1', 'errortip' => '获取班级记录失败', 'result' => $result));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //通过班级信息获取学生课时明细
    function studentHourApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'studentHour', $request)) {
                $sid = $this->DataControl->getFieldOne("smc_student", "student_id", "student_branch = '{$request['student_branch']}'");

                $datawhere = " 1 ";

                if (isset($request['starttime']) && $request['starttime'] !== '') {
                    $datawhere .= " and ch.hour_day >= '{$request['starttime']}'";
                }

                if (isset($request['endtime']) && $request['endtime'] !== '') {
                    $datawhere .= " and ch.hour_day <= '{$request['endtime']}'";
                }

                if (isset($request['hour_way']) && $request['hour_way'] !== '') {
                    $datawhere .= " and ch.hour_way = '{$request['hour_way']}'";
                }

                if (isset($request['hour_id']) && $request['hour_id'] !== '') {
                    $datawhere .= " and ch.hour_id = '{$request['hour_id']}'";
                }
                if (isset($request['class_id']) && $request['class_id'] !== '') {
                    $datawhere .= " and ch.class_id = '{$request['class_id']}'";
                }
                $havingwhere = '1';
                if ($request['status'] == '0') {
                    $datawhere .= " and ch.hour_ischecking = '1'";
                    $havingwhere .= " and income_isconfirm = '0'";
                }


                if($request['student_branch']){
                    $sql = "select ch.hour_id,ch.hour_starttime,ch.hour_endtime,co.course_branch,cl.classroom_branch,cl.classroom_cnname,ch.hour_way,ch.hour_ischecking,c.class_cnname,co.course_cnname,s.staffer_cnname,ch.hour_day,hour_noon,c.class_enname,sc.coursebalance_unitearning,s.staffer_img,(
	SELECT
		group_concat(
			DISTINCT concat(
				staffer_cnname,(
				CASE
			    	WHEN ifnull( Y.staffer_enname, '' ) = '' THEN
						'' ELSE concat( '-', Y.staffer_enname ) 
					END 
					))) 
		FROM
			smc_class_hour_teaching AS X,
			smc_staffer AS Y 
		WHERE
			X.staffer_id = Y.staffer_id 
			AND X.class_id = ch.class_id 
			AND X.teaching_isdel = 0 
			AND teaching_type = 0 
			) AS class_teacher,
        ch.hour_lessontimes AS renum,
        ch.hour_lessontimes AS num,
		co.course_classnum AS allnum,cc.companies_id,co.coursetype_id,co.coursecat_id,co.course_id,ch.class_id,sh.hourstudy_checkin,
		sh.hourstudy_id,
		IFNULL(er.income_isconfirm,0) as income_isconfirm,
                er.income_date
              from smc_class_hour as ch
              left join smc_class as c on c.class_id=ch.class_id
              left join smc_course as co on co.course_id=c.course_id	 
              left join smc_school_coursecat_subject as cs on cs.coursecat_id = co.coursecat_id and cs.school_id = '{$request['school_id']}'
		      left join gmc_code_companies as cc on cc.companies_id = cs.companies_id
              left join smc_student_study as ss on ss.class_id=c.class_id
              left join smc_classroom as cl on cl.classroom_id=ch.classroom_id
              left join smc_class_hour_teaching as cht on cht.hour_id=ch.hour_id and cht.teaching_type='0' and cht.teaching_isdel='0'
              left join smc_staffer as s on s.staffer_id=cht.staffer_id
              left join smc_student_coursebalance as sc on sc.school_id = '{$request['school_id']}' and sc.student_id = '{$sid['student_id']}' and sc.course_id = ch.course_id
		      LEFT JOIN smc_student_hourstudy AS sh ON sh.hour_id = ch.hour_id and sh.student_id = '{$sid['student_id']}'
		      left join cmb_trans_transfer as er on er.hourstudy_id = sh.hourstudy_id
              where {$datawhere} and ss.school_id='{$request['school_id']}' and ss.company_id='{$request['company_id']}'  and ss.student_id='{$sid['student_id']}'
              and ss.study_isreading=1 and ch.hour_day>=ss.study_beginday and ch.hour_day<=ss.study_endday
		      AND ch.hour_ischecking > '-1'
             -- and coursebalance_issupervise = '1' and co.course_issupervise = '1' 
              and not exists(select 1 from smc_class_hour as x where x.hour_id=ch.hour_id and x.hour_ischecking=1 and (select y.hourstudy_id from smc_student_hourstudy as y where y.hour_id=x.hour_id and y.student_id=ss.student_id ) is null)
              group by ch.hour_id
              HAVING {$havingwhere} 
              order by hour_day ASC,ch.hour_starttime ASC
        ";
                }else{
                    $sql = "SELECT
	ch.hour_id,
	ch.hour_starttime,
	ch.hour_endtime,
	co.course_branch,
	cl.classroom_branch,
	cl.classroom_cnname,
	ch.hour_way,
	ch.hour_ischecking,
	c.class_cnname,
	co.course_cnname,
	s.staffer_cnname,
	ch.hour_day,
	hour_noon,
	c.class_enname,
	sc.coursebalance_unitearning,
	s.staffer_img,(
	SELECT
		group_concat(
			DISTINCT concat(
				staffer_cnname,(
				CASE
						
						WHEN ifnull( Y.staffer_enname, '' ) = '' THEN
						'' ELSE concat( '-', Y.staffer_enname ) 
					END 
					))) 
		FROM
			smc_class_hour_teaching AS X,
			smc_staffer AS Y 
		WHERE
			X.staffer_id = Y.staffer_id 
			AND X.class_id = ch.class_id 
			AND X.teaching_isdel = 0 
			AND teaching_type = 0 
		) AS class_teacher,
		ch.hour_lessontimes AS renum,
		ch.hour_lessontimes AS num,
		co.course_classnum AS allnum,
		cc.companies_id,
		co.coursetype_id,
		co.coursecat_id,
		co.course_id,
		ch.class_id
	FROM
		smc_class_hour AS ch
		LEFT JOIN smc_class AS c ON c.class_id = ch.class_id
		LEFT JOIN smc_course AS co ON co.course_id = c.course_id
		LEFT JOIN smc_school_coursecat_subject AS cs ON cs.coursecat_id = co.coursecat_id 
		AND cs.school_id = '{$request['school_id']}'
		LEFT JOIN gmc_code_companies AS cc ON cc.companies_id = cs.companies_id
		LEFT JOIN smc_student_study AS ss ON ss.class_id = c.class_id
		LEFT JOIN smc_classroom AS cl ON cl.classroom_id = ch.classroom_id
		LEFT JOIN smc_class_hour_teaching AS cht ON cht.hour_id = ch.hour_id 
		AND cht.teaching_type = '0' 
		AND cht.teaching_isdel = '0'
		LEFT JOIN smc_staffer AS s ON s.staffer_id = cht.staffer_id
		LEFT JOIN smc_student_coursebalance AS sc ON sc.school_id = '{$request['school_id']}' 
		AND sc.student_id = '' 
		AND sc.course_id = ch.course_id
	WHERE
		1 
		AND ch.class_id = '{$request['class_id']}' 
		AND ss.school_id = '{$request['school_id']}' 
		AND ss.company_id = '{$request['company_id']}' 
		AND ss.study_isreading = 1 
		AND ch.hour_day >= ss.study_beginday 
		AND ch.hour_day <= ss.study_endday AND ch.hour_ischecking > '-1' 
		AND NOT EXISTS (
		SELECT
			1 
		FROM
			smc_class_hour AS x 
		WHERE
			x.hour_id = ch.hour_id 
			AND x.hour_ischecking = 1 
			AND ( SELECT y.hourstudy_id FROM smc_student_hourstudy AS y WHERE y.hour_id = x.hour_id AND y.student_id = ss.student_id ) IS NULL 
		) 
	GROUP BY
		ch.hour_id 
	HAVING
		1 
	ORDER BY
	hour_day ASC,
	ch.hour_starttime ASC";
                }

                $hourList = $this->DataControl->selectClear($sql);

                if ($hourList && $request['student_branch']) {
                    foreach ($hourList as &$val) {
                        if ($val['hour_ischecking'] < '1') {
                            $val['status'] = '0';
                        } else {
                            if ($val['income_isconfirm'] == '1') {
                                $val['status'] = '2';
                            } else {
                                $val['status'] = '1';

                            }
                        }
                    }
                }

                $data = array();
                $data['list'] = $hourList;

                if (!$hourList) {
                    $data['list'] = array();
                    if ($request['status'] == '0') {
                        ajax_return(array('error' => 1, 'errortip' => "您还没有待确认课程，好好放松一下吧～", 'result' => $data));
                    } else {
                        ajax_return(array('error' => 1, 'errortip' => "暂无课程", 'result' => $data));
                    }
                } else {
                    ajax_return(array('error' => 0, 'errortip' => "获取成功", 'result' => $data));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }


    function confirmClassAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'confirmClass', $request)) {
                $List = json_decode(stripslashes($request['class']), true);
                foreach ($List as $item) {
                    $sid = $this->DataControl->getFieldOne("smc_student", "student_id", "student_branch = '{$request['student_branch']}'");
                    $isset = $this->DataControl->getFieldOne("cmb_trans_transfer", "transfer_id,transfer_status", "hourstudy_id = '{$item['hourstudy_id']}' and student_id = '{$sid['student_id']}'");
                    $data = array();
                    $data['income_isconfirm'] = '1';
                    $data['confirm_type'] = '1';
                    $data['confirm_ip'] = $request['ip'];
                    $data['confirm_phone'] = $request['phone'];
                    $data['confirm_createtime'] = time();

                    if ($isset) {
                        if ($isset['transfer_status'] <= 1) {
                            $data['transfer_updatetime'] = time();
                            $this->DataControl->updateData('cmb_trans_transfer', "hourstudy_id = '{$item['hourstudy_id']}'", $data);
                        }
                    } else {
                        $data['company_id'] = $request['company_id'];
                        $data['school_id'] = $request['school_id'];
                        $data['student_id'] = $sid['student_id'];
                        $data['companies_id'] = $item['companies_id'];
                        $data['hourstudy_id'] = $item['hourstudy_id'];
                        $data['coursetype_id'] = $item['coursetype_id'];
                        $data['coursecat_id'] = $item['coursecat_id'];
                        $data['course_id'] = $item['course_id'];
                        $data['class_id'] = $item['class_id'];
                        $data['hourstudy_id'] = $item['hourstudy_id'];
                        $data['transfer_createtime'] = time();
                        $this->DataControl->insertData('cmb_trans_transfer', $data);
                    }
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
            ajax_return(array('error' => '0', 'errortip' => "确认成功", 'result' => array()));
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }


    //根据学生编号获取学生合同信息
    function studentProtocolApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'studentProtocol', $request)) {
                $request['tokenstring'] = $pucArray['tokenstring'];
                $sid = $this->DataControl->getFieldOne("smc_student", "student_id", "student_branch = '{$request['student_branch']}'");

                $datawhere = " 1 ";
                if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
                    $datawhere .= " and (c.course_cnname like '%{$paramArray['keyword']}%' or c.course_branch like '%{$paramArray['keyword']}%' or c.course_cnname like '%{$paramArray['keyword']}%')";
                }
                if (isset($paramArray['order_pid']) && $paramArray['order_pid'] !== "") {
                    $datawhere .= " and p.order_pid ='{$paramArray['order_pid']}'";
                }
//            if (isset($paramArray['p']) && $paramArray['p'] !== '') {
//                $page = $paramArray['p'];
//            } else {
//                $page = '1';
//            }
//            if (isset($paramArray['num']) && $paramArray['num'] !== '') {
//                $num = $paramArray['num'];
//            } else {
//                $num = '10';
//            }
//            $pagestart = ($page - 1) * $num;

//            if ($paramArray['student_id'] && $paramArray['paypid'] == '') {
//                $student_id = $paramArray['student_id'];
//            } else {
//                $order_pid = $this->DataControl->getFieldOne("smc_payfee_order_pay", "order_pid", "pay_pid = '{$paramArray['paypid']}'");
//                $id = $this->DataControl->getFieldOne("smc_payfee_order", "student_id,company_id", "order_pid = '{$order_pid['order_pid']}'");
//                $student_id = $id['student_id'];
//
//                $issign = $this->DataControl->getFieldOne("gmc_company", "company_issign", "company_id = '{$id['company_id']}'");
//            }
                $sql = "
            SELECT
                p.protocol_id,
                p.protocol_price,
                p.order_pid,
                p.protocol_pid,
                p.protocol_issign,
                FROM_UNIXTIME( p.protocol_createtime, '%Y-%m-%d %H:%i:%s' ) AS protocol_createtime,
                c.course_cnname,
                c.course_branch
            FROM
                smc_student_protocol AS p left join smc_course as c on p.course_id = c.course_id
            WHERE
                {$datawhere} and p.protocol_isdel = '0' and p.student_id = '{$sid['student_id']}'";
                $info = $this->DataControl->selectClear($sql);

                $sqls = "
            SELECT
                p.protocol_id,
                p.protocol_price,
                p.order_pid,
                p.protocol_pid,
                p.protocol_issign,
                FROM_UNIXTIME( p.protocol_createtime, '%Y-%m-%d %H:%i:%s' ) AS protocol_createtime,
                c.course_cnname,
                c.course_branch
            FROM
                smc_student_protocol AS p left join smc_course as c on p.course_id = c.course_id
            WHERE
                {$datawhere} and p.protocol_isdel = '0' and p.student_id = '{$sid['student_id']}'";
                $infos = $this->DataControl->selectClear($sqls);

                $all_num = count($infos);

                if (!$info) {
                    $res['list'] = array();
                    $res['allnum'] = 0;
                } else {
                    $res['list'] = $info;
                    $res['allnum'] = $all_num;
//                $res['issign'] = $issign['company_issign'];
                }
                if ($info) {
                    $result = array();
                    $result["data"] = $info;
                    ajax_return(array('error' => '0', 'errortip' => '获取合同信息成功', 'result' => $result));
                } else {
                    $result = array();
                    $result["data"] = array();
                    ajax_return(array('error' => '1', 'errortip' => '获取合同信息失败', 'result' => $result));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }


    //协议详情
    function protocolDetailView()
    {
        $request = Input('get.', '', 'trim,addslashes,strip_tags');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'protocolDetail', $request)) {
                $pro = $this->gettip($request);
                $result = $pro['treaty_protocol'];
                $sign = $this->DataControl->getFieldOne("smc_student_protocol","protocol_sign","protocol_id = '{$request['protocol_id']}'");
                if(!$sign['protocol_sign']){
                    $sign['protocol_sign'] = '';
                }
                ajax_return(array('error' => 0, 'errortip' => "获取成功", 'result' => $result, 'sign' => $sign['protocol_sign']));
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }

        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));

        }

    }


    function gettip($request)
    {
        $PartyA = array();
        $protocol = $this->DataControl->getOne("smc_student_protocol", "protocol_id = '{$request['protocol_id']}'");

        $order = $this->DataControl->selectOne("select o.order_pid,o.trading_pid,o.companies_id,p.pay_pid,p.pay_typename,p.pay_price,o.order_coupon_price,o.order_paidprice,o.order_arrearageprice,o.order_paymentprice,c.ordercourse_buynums,c.ordercourse_totalprice,c.ordercourse_unitprice from smc_payfee_order as o left join smc_payfee_order_pay as p on o.order_pid = p.order_pid left join smc_payfee_order_course as c on c.order_pid = o.order_pid where o.order_pid = '{$protocol['order_pid']}'");

        $company = $this->DataControl->getFieldOne("gmc_company", "company_cnname,company_logo", "company_id = '{$protocol['company_id']}'");
        $school = $this->DataControl->getFieldOne("smc_school", "school_cnname,school_signet,school_address,school_phone,companies_id,school_liaison,school_examine,school_register,school_permitbranch,school_permitstday,school_permitendday,school_icp,school_licensestday,school_licenseendday,school_society,school_licensestatus", "school_id = '{$protocol['school_id']}'");
        $course = $this->DataControl->getFieldOne("smc_course", "course_cnname,course_branch,course_classnum,course_classtimes,coursetype_id,course_inclasstype,course_perhour,coursecat_id", "course_id = '{$protocol['course_id']}'");
        $companies_id = $this->DataControl->getFieldOne("smc_school_coursecat_subject", "companies_id", "school_id = '{$protocol['school_id']}' and coursecat_id = '{$course['coursecat_id']}'");
        $companies = $this->DataControl->getFieldOne("gmc_code_companies", "companies_cnname,companies_signet,companies_liaison,companies_examine,companies_register,companies_permitbranch,companies_permitstday,companies_permitendday,companies_icp,companies_licensestday,companies_licenseendday,companies_society,companies_licensestatus,companies_supervisebank,companies_superviseaccount,companies_settlebank,companies_settleaccount,companies_permitstday,companies_licensestday", "companies_id = '{$companies_id['companies_id']}'");

        $PartyA['companies_cnname'] = $companies['companies_cnname'];
        $PartyA['companies_permitstday'] = substr($companies['companies_permitstday'], strripos($companies['companies_permitstday'], "至") + 3);
        $PartyA['companies_licensestday'] = substr($companies['companies_licensestday'], strripos($companies['companies_licensestday'], "至") + 3);
        $PartyA['companies_supervisebank'] = $companies['companies_supervisebank'];
        $PartyA['companies_superviseaccount'] = $companies['companies_superviseaccount'];
        $PartyA['companies_settlebank'] = $companies['companies_settlebank'];
        $PartyA['companies_settleaccount'] = $companies['companies_settleaccount'];
        $PartyA['school_cnname'] = $school['school_cnname'];
        $PartyA['company_cnname'] = $company['company_cnname'];
        $PartyA['school_address'] = $school['school_address'];
        $PartyA['school_phone'] = $school['school_phone'];
        if ($school['school_phone']) {
            $PartyA['school_phone'] = $school['school_phone'];
        } else {
            $PartyA['school_phone'] = '--';
        }
        if ($companies['companies_liaison']) {
            $PartyA['school_liaison'] = $companies['companies_liaison'];
        } else {
            $PartyA['school_liaison'] = '--';
        }
        if ($companies['companies_examine']) {
            $PartyA['school_examine'] = $companies['companies_examine'];
        } else {
            $PartyA['school_examine'] = '--';
        }
        if ($companies['companies_register']) {
            $PartyA['school_register'] = $companies['companies_register'];
        } else {
            $PartyA['school_register'] = '--';
        }
        if ($companies['companies_permitbranch']) {
            $PartyA['school_permitbranch'] = $companies['companies_permitbranch'];
        } else {
            $PartyA['school_permitbranch'] = '--';
        }
        if ($companies['companies_permitstday']) {
            $PartyA['school_permitstday'] = $companies['companies_permitstday'];
        } else {
            $PartyA['school_permitstday'] = '--';
        }
        if ($companies['companies_permitstday']) {
            $PartyA['school_permitstday'] = $companies['companies_permitstday'];
        } else {
            $PartyA['school_permitstday'] = '--';
        }
        if ($companies['companies_licensestday']) {
            $PartyA['school_licensestday'] = $companies['companies_licensestday'];
        } else {
            $PartyA['school_licensestday'] = '--';
        }
        if ($companies['companies_society']) {
            $PartyA['school_society'] = $companies['companies_society'];
        } else {
            $PartyA['school_society'] = '--';
        }
        $PartyA['school_signet'] = $companies['companies_signet'];
        $PartyA['school_icp'] = $companies['companies_icp'];
        $PartyA['company_logo'] = $company['company_logo'];
        $PartyA['company_shortname'] = $school['company_shortname'];

        $PartyB = array();
        $student = $this->DataControl->getFieldOne("smc_student", "student_cnname,student_enname,student_birthday,student_sex,student_branch,student_idcard", "student_id = '{$protocol['student_id']}'");
        $PartyB['student_branch'] = $student['student_branch'];
        $PartyB['student_cnname'] = $student['student_cnname'];
        $PartyB['student_sex'] = $student['student_sex'];
        $PartyB['student_birthday'] = $student['student_birthday'];
        $famliy = $this->DataControl->getFieldOne("smc_student_family", "parenter_id", "student_id = '{$protocol['student_id']}' and family_isdefault = '1'");
        $parenter = $this->DataControl->selectOne("
            SELECT
                p.parenter_mobile,
                p.parenter_cnname,
                p.parenter_sign,
                f.family_relation,
                f.family_cnname,
                f.family_address 
            FROM
                smc_parenter AS p
                LEFT JOIN smc_student_family AS f ON p.parenter_id = f.parenter_id
                where p.parenter_id = '{$famliy['parenter_id']}' and f.student_id = '{$protocol['student_id']}'");
        $PartyB['phone'] = $parenter['parenter_mobile'];
        $PartyB['schoolname'] = '--';
        $PartyB['address'] = '--';
        $PartyB['student_idcard'] = $student['student_idcard'];

        $guarder = array();
        $guarder['guardername'] = $parenter['family_cnname'];
        $guarder['guarderphone'] = $parenter['parenter_mobile'];
        $guarder['guarderrelation'] = $parenter['family_relation'];
        $guarder['parenter_sign'] = $protocol['protocol_sign'];

        $famliys = $this->DataControl->getFieldOne("smc_student_family", "parenter_id", "student_id = '{$protocol['student_id']}' and family_isdefault = '0'");

        if ($famliys) {
            $parenters = $this->DataControl->selectOne("
            SELECT
                p.parenter_mobile,
                p.parenter_cnname,
                p.parenter_sign,
                f.family_relation,
                f.family_cnname,
                f.family_address 
            FROM
                smc_parenter AS p
                LEFT JOIN smc_student_family AS f ON p.parenter_id = f.parenter_id
                where p.parenter_id = '{$famliys['parenter_id']}' and f.student_id = '{$protocol['student_id']}'");
            $guarder['urgentname'] = $parenters['family_cnname'];
            $guarder['urgentphone'] = $parenters['parenter_mobile'];
            $guarder['urgentrelation'] = $parenters['family_relation'];
        } else {
            $guarder['urgentname'] = $parenter['family_cnname'];
            $guarder['urgentphone'] = $parenter['parenter_mobile'];
            $guarder['urgentrelation'] = $parenter['family_relation'];
        }

        $courseInfo = array();
        $pricing_id = $this->DataControl->selectOne("select pricing_id from smc_payfee_order_course as c where c.order_pid = '{$protocol['order_pid']}'");
        $agreement = $this->DataControl->getFieldOne("smc_fee_pricing", "agreement_id", "pricing_id = '{$pricing_id['pricing_id']}'");

        if ($protocol['protocol_isaudit'] == '1') {
            $protocolOne = $this->DataControl->getFieldOne("smc_student_protocol", "treaty_tabletip,treaty_protocol", "protocol_id = '{$protocol['protocol_id']}'");
        } else {
            $isprocat = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and coursecat_id = '{$course['coursecat_id']}'");
            if ($isprocat) {
                $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and coursecat_id = '{$course['coursecat_id']}' and t.treaty_applytype = '1' and a.school_id = '{$protocol['school_id']}'");
                if (!$protocolOne) {
                    $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and coursecat_id = '{$course['coursecat_id']}' and t.treaty_applytype = '0'");
                    if (!$protocolOne) {

                        $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and coursecat_id = '0' and t.treaty_applytype = '0'");
                        if (!$protocolOne) {
                            $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and coursecat_id = '0' and t.treaty_applytype = '1'  and a.school_id = '{$protocol['school_id']}'");
                        }
                    }
                }
            } else {
                $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and t.treaty_applytype = '1' and a.school_id = '{$protocol['school_id']}' and t.coursecat_id = '0'");
                if (!$protocolOne) {
                    $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and t.treaty_applytype = '0' and t.coursecat_id = '0'");
                }
            }
        }

//        var_dump($protocolOne);die();


        $courseInfo['course_branch'] = $course['course_branch'];
        $courseInfo['course_cnname'] = $course['course_cnname'];
        $courseInfo['ordercourse_unitprice'] = $course['ordercourse_unitprice'];
        if ($order['ordercourse_buynums'] == $course['course_classnum']) {
            $courseInfo['type'] = '新班';
        } else {
            $courseInfo['type'] = '插班';
        }
        if ($course['course_inclasstype'] == '2' || $course['course_inclasstype'] == '0') {
            $courseInfo['course_classnum'] = $protocol['protocol_nums'];
        } elseif ($course['course_inclasstype'] == '1') {
            $courseInfo['course_classnum'] = '';
        } else {
            $courseInfo['course_classnum'] = $course['course_classnum'];

        }

        if ($course['course_inclasstype'] == '1') {
            $courseInfo['protocol_nums'] = '';
        } else {
            $courseInfo['protocol_nums'] = $protocol['protocol_nums'];
        }

        $courseInfo['course_classtimes'] = $course['course_perhour'] . 'h';

        $priceInfo = array();
        $priceInfo['protocol_pid'] = $protocol['protocol_pid'];
        $priceInfo['courseprice'] = '¥' . $order['ordercourse_totalprice'];
        $priceInfo['sendprice'] = '¥' . $protocol['protocol_price'];

        $track = $this->DataControl->getFieldOne("smc_payfee_order_tracks", "staffer_id", "order_pid = '{$order['order_pid']}' and tracks_title = '创建订单'");
        $agent = $this->DataControl->getFieldOne("smc_staffer", "staffer_cnname", "staffer_id = '{$track['staffer_id']}'");
        $priceInfo['adviser'] = '--';
        $priceInfo['agent'] = $agent['staffer_cnname'];
        $priceInfo['principal'] = '--';

        $priceInfo['bigprice'] = $this->convert_2_cn(intval($order['ordercourse_totalprice']));

        $startday = $this->DataControl->selectOne("SELECT
	h.hour_day 
FROM
	smc_class_hour AS h
	LEFT JOIN smc_class AS c ON c.class_id = h.class_id
	LEFT JOIN smc_student_study AS sh ON sh.class_id = h.class_id 
WHERE
	sh.student_id = '{$protocol['student_id']}' 
	AND c.course_id = '{$protocol['course_id']}'
ORDER BY h.hour_lessontimes ASC limit 0,1");


        $endday = $this->DataControl->selectOne("SELECT
	h.hour_day 
FROM
	smc_class_hour AS h
	LEFT JOIN smc_class AS c ON c.class_id = h.class_id
	LEFT JOIN smc_student_study AS sh ON sh.class_id = h.class_id 
WHERE
	sh.student_id = '{$protocol['student_id']}' 
	AND c.course_id = '{$protocol['course_id']}'
ORDER BY h.hour_lessontimes DESC limit 0,1");
        if ($protocolOne) {
            $treatyArray = array();
            $treatyArray['companies_cnname'] = $PartyA['companies_cnname'];
            $treatyArray['school_address'] = $PartyA['school_address'];
            $treatyArray['school_examine'] = $PartyA['school_examine'];
            $treatyArray['school_register'] = $PartyA['school_register'];
            $treatyArray['school_permitbranch'] = $PartyA['school_permitbranch'];
            $treatyArray['school_permitstday'] = $PartyA['school_permitstday'];
            $treatyArray['school_icp'] = $PartyA['school_icp'];
            $treatyArray['school_licensestday'] = $PartyA['school_licensestday'];
            $treatyArray['school_society'] = $PartyA['school_society'];
            $treatyArray['school_liaison'] = $PartyA['school_liaison'];
            $treatyArray['school_phone'] = $PartyA['school_phone'];
            $treatyArray['student_branch'] = $PartyB['student_branch'];
            $treatyArray['student_cnname'] = $PartyB['student_cnname'];
            $treatyArray['student_sex'] = $PartyB['student_sex'];
            $treatyArray['student_birthday'] = $PartyB['student_birthday'];
            $treatyArray['phone'] = $PartyB['phone'];
            $treatyArray['schoolname'] = $PartyB['schoolname'];
            $treatyArray['address'] = $PartyB['address'];
            $treatyArray['student_idcard'] = $PartyB['student_idcard'];
            $treatyArray['guardername'] = $guarder['guardername'];
            $treatyArray['guarderphone'] = $guarder['guarderphone'];
            $treatyArray['guarderrelation'] = $guarder['guarderrelation'];
            $treatyArray['urgentname'] = $guarder['urgentname'];
            $treatyArray['urgentphone'] = $guarder['urgentphone'];
            $treatyArray['urgentrelation'] = $guarder['urgentrelation'];
            $treatyArray['course_branch'] = $courseInfo['course_branch'];
            $treatyArray['course_cnname'] = $courseInfo['course_cnname'];
            $treatyArray['type'] = $courseInfo['type'];
            $treatyArray['protocol_nums'] = $courseInfo['protocol_nums'];
            $treatyArray['protocol_pid'] = $priceInfo['protocol_pid'];
            $treatyArray['sendprice'] = $priceInfo['sendprice'];
            $treatyArray['agent'] = $priceInfo['agent'];
            $protocolOne['treaty_tabletip'] = $this->contractTable($protocolOne['treaty_tabletip'], $treatyArray);
        }

        if ($protocolOne) {
            $treatyArray = array();
            $treatyArray['companies_cnname'] = $PartyA['companies_cnname'];
            $treatyArray['startday'] = $startday['hour_day'];
            $treatyArray['endday'] = $endday['hour_day'];
            $treatyArray['companies_permitstday'] = $PartyA['companies_permitstday'];
            $treatyArray['companies_licensestday'] = $PartyA['companies_licensestday'];
            $treatyArray['course_classnum'] = $courseInfo['course_classnum'];
            $treatyArray['course_classtimes'] = $courseInfo['course_classtimes'];
            $treatyArray['ordercourse_unitprice'] = $courseInfo['ordercourse_unitprice'];
            $treatyArray['companies_supervisebank'] = $PartyA['companies_supervisebank'];
            $treatyArray['companies_superviseaccount'] = $PartyA['companies_superviseaccount'];
            $treatyArray['companies_settlebank'] = $PartyA['companies_settlebank'];
            $treatyArray['companies_settleaccount'] = $PartyA['companies_settleaccount'];
            $treatyArray['courseprice'] = $priceInfo['courseprice'];
            $treatyArray['bigprice'] = $priceInfo['bigprice'];
            $treatyArray['school_address'] = $PartyA['school_address'];
            $treatyArray['school_examine'] = $PartyA['school_examine'];
            $treatyArray['school_register'] = $PartyA['school_register'];
            $treatyArray['school_permitbranch'] = $PartyA['school_permitbranch'];
            $treatyArray['school_permitstday'] = $PartyA['school_permitstday'];
            $treatyArray['school_icp'] = $PartyA['school_icp'];
            $treatyArray['school_licensestday'] = $PartyA['school_licensestday'];
            $treatyArray['school_society'] = $PartyA['school_society'];
            $treatyArray['school_liaison'] = $PartyA['school_liaison'];
            $treatyArray['school_phone'] = $PartyA['school_phone'];
            $treatyArray['student_branch'] = $PartyB['student_branch'];
            $treatyArray['student_cnname'] = $PartyB['student_cnname'];
            $treatyArray['student_sex'] = $PartyB['student_sex'];
            $treatyArray['student_birthday'] = $PartyB['student_birthday'];
            $treatyArray['phone'] = $PartyB['phone'];
            $treatyArray['schoolname'] = $PartyB['schoolname'];
            $treatyArray['address'] = $PartyB['address'];
            $treatyArray['student_idcard'] = $PartyB['student_idcard'];
            $treatyArray['guardername'] = $guarder['guardername'];
            $treatyArray['guarderphone'] = $guarder['guarderphone'];
            $treatyArray['guarderrelation'] = $guarder['guarderrelation'];
            $treatyArray['urgentname'] = $guarder['urgentname'];
            $treatyArray['urgentphone'] = $guarder['urgentphone'];
            $treatyArray['urgentrelation'] = $guarder['urgentrelation'];
            $treatyArray['course_branch'] = $courseInfo['course_branch'];
            $treatyArray['course_cnname'] = $courseInfo['course_cnname'];
            $treatyArray['type'] = $courseInfo['type'];
            $treatyArray['protocol_nums'] = $courseInfo['protocol_nums'];
            $treatyArray['protocol_pid'] = $priceInfo['protocol_pid'];
            $treatyArray['sendprice'] = $priceInfo['sendprice'];
            $treatyArray['agent'] = $priceInfo['agent'];
            $protocolOne['treaty_protocol'] = $this->contractTable($protocolOne['treaty_protocol'], $treatyArray);
        }


        return $protocolOne;
    }


    function convert_2_cn($num)
    {
        $convert_cn = array("零", "壹", "贰", "叁", "肆", "伍", "陆", "柒", "捌", "玖");
        $repair_number = array('零仟零佰零拾零', '万万', '零仟', '零佰', '零拾');
        $unit_cn = array("拾", "佰", "仟", "万", "亿");
        $exp_cn = array("", "万", "亿");
        $max_len = 12;
        $len = strlen($num);
        if ($len > $max_len) {
            return 'outnumber';
        }
        $num = str_pad($num, 12, '-', STR_PAD_LEFT);
        $exp_num = array();
        $k = 0;
        for ($i = 12; $i > 0; $i--) {
            if ($i % 4 == 0) {
                $k++;
            }
            $exp_num[$k][] = substr($num, $i - 1, 1);
        }
        $str = '';
        foreach ($exp_num as $key => $nums) {
            if (array_sum($nums)) {
                $str = array_shift($exp_cn) . $str;
            }
            foreach ($nums as $nk => $nv) {
                if ($nv == '-') {
                    continue;
                }
                if ($nk == 0) {
                    $str = $convert_cn[$nv] . $str;
                } else {
                    $str = $convert_cn[$nv] . $unit_cn[$nk - 1] . $str;
                }
            }
        }
        $str = str_replace($repair_number, array('万', '亿', '-'), $str);
        $str = preg_replace("/-{2,}/", "", $str);
        $str = str_replace(array('零', '-'), array('', '零'), $str);
        return $str;
    }

    function contractTable($tabletip, $treatyArray)
    {
        $tableNote = $tabletip;
        foreach ($treatyArray as $key => $treatyOne) {

            $tableNote = str_replace("#" . $key . "#", $treatyOne, $tableNote);
        }
        return $tableNote;
    }


    //根据手机号获取教师信息
    function  stafferMobileApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'stafferMobile', $request)) {
                $request['tokenstring'] = $pucArray['tokenstring'];
                $stafferOne = $this->DataControl->getFieldOne("smc_staffer","staffer_id,staffer_img,staffer_sex,staffer_cnname,staffer_enname,staffer_branch,staffer_mobile","staffer_mobile = '{$request['mobile']}' and company_id = '{$request['company_id']}'");

                if ($stafferOne) {
                    $result = $stafferOne;
                    ajax_return(array('error' => '0', 'errortip' => '获取教师信息成功', 'result' => $result));
                } else {
                    $result = array();
                    ajax_return(array('error' => '1', 'errortip' => '获取教师信息失败', 'result' => $result));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //根据教师ID获取教师带班信息
    function  stafferClassApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'stafferClass', $request)) {
                $request['tokenstring'] = $pucArray['tokenstring'];
                $sql = "
                SELECT
                    c.class_id,
                    c.class_cnname,
                    c.class_branch,
                    c.class_enname,
                    c.class_status,
                    c.class_stdate,
                    c.class_enddate,
                    c.class_timestr,
                    s.staffer_cnname,
                    s.staffer_cnname,
                    s.staffer_enname,
                    c.school_id,
                    co.course_classnum as allnum
                FROM
                    smc_class_teach AS t
                    LEFT JOIN smc_staffer AS s ON s.staffer_id = t.staffer_id
                    LEFT JOIN smc_class AS c ON c.class_id = t.class_id
                    left join smc_course as co on co.course_id = c.course_id
                WHERE
                    t.staffer_id = '{$request['staffer_id']}' 
                GROUP BY
                    t.class_id
            ";
                $ClList = $this->DataControl->selectClear($sql);

                if($ClList){
                    foreach($ClList as &$val){
                        $num = $this->DataControl->selectOne("select hour_lessontimes from smc_class_hour where class_id = '{$val['class_id']}' and hour_ischecking = '1' order by hour_lessontimes DESC");
                        $val['num'] = $num['hour_lessontimes'];
                        if(!$num['hour_lessontimes']){
                            $val['num'] = 0;
                        }
                        $classlessonList = $this->DataControl->selectClear("SELECT l.lessonplan_id,l.lessonplan_week, l.lessonplan_starttime, l.lessonplan_endtime,c.classroom_cnname,s.staffer_cnname,s.staffer_enname
FROM smc_class_lessonplan AS l
LEFT JOIN smc_staffer AS s ON s.staffer_id = l.staffer_id
LEFT JOIN smc_classroom AS c ON c.classroom_id = l.classroom_id
WHERE l.class_id = '{$val['class_id']}' order by l.lessonplan_id ASC");
                        $classtimestr = "";

                        if ($classlessonList) {
                            foreach ($classlessonList as $classlessonOne) {
                                $classtimestr .= $classlessonOne['lessonplan_week'] . " " . $classlessonOne['lessonplan_starttime'] . "~" . $classlessonOne['lessonplan_endtime'] . ",";
                            }
                        }
                        $val['class_timestr'] = trim($classtimestr,",");
                    }
                }

                if ($ClList) {
                    $result = array();
                    $result["data"] = $ClList;
                    ajax_return(array('error' => '0', 'errortip' => '获取带班信息成功', 'result' => $result));
                } else {
                    $result = array();
                    $result["data"] = array();
                    ajax_return(array('error' => '1', 'errortip' => '暂无带班信息', 'result' => $result));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //根据班级ID获取学生信息
    function  classstudentApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'classstudent', $request)) {
                $request['tokenstring'] = $pucArray['tokenstring'];
                $sql = "
                SELECT 
                    s.student_id,
                    s.student_cnname,
                    s.student_enname,
                    s.student_sex,
                    s.student_img,
                    s.student_birthday,
                    s.student_branch,
                    ss.study_isreading,
                    ss.study_beginday,
                    ss.study_endday
                FROM
                    smc_student_study AS ss
                    LEFT JOIN smc_student AS s ON ss.student_id = s.student_id 
                WHERE
                    ss.class_id = '{$request['class_id']}'
            ";
                $ClList = $this->DataControl->selectClear($sql);

                if ($ClList) {
                    $result = array();
                    $result["data"] = $ClList;
                    ajax_return(array('error' => '0', 'errortip' => '获取学生信息成功', 'result' => $result));
                } else {
                    $result = array();
                    $result["data"] = array();
                    ajax_return(array('error' => '1', 'errortip' => '暂无学生信息', 'result' => $result));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //根据课程编号获取课程信息
    function  courseInfoApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            if ($this->VeryModelNums($pucArray['apiuser_id'], 'courseInfo', $request)) {
                $request['tokenstring'] = $pucArray['tokenstring'];
                $sql = "
                SELECT 
                    c.course_id,
                    t.coursetype_cnname,
                    t.coursetype_branch,
                    a.coursecat_cnname,
                    a.coursecat_branch,
                    c.course_cnname,
                    c.course_branch,
                    c.course_classnum
                FROM
                    smc_course AS c
                    left join smc_code_coursetype as t on c.coursetype_id = t.coursetype_id
                    left join smc_code_coursecat as a on c.coursecat_id = a.coursecat_id
                WHERE
                    c.course_branch = '{$request['course_branch']}' and c.company_id = '{$request['company_id']}'
            ";
                $ClList = $this->DataControl->selectClear($sql);

                if ($ClList) {
                    $result = array();
                    $result["data"] = $ClList;
                    ajax_return(array('error' => '0', 'errortip' => '获取课程信息成功', 'result' => $result));
                } else {
                    $result = array();
                    $result["data"] = array();
                    ajax_return(array('error' => '1', 'errortip' => '暂无课程信息', 'result' => $result));
                }
            } else {
                ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }
}
