<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/13
 * Time: 23:25
 */

namespace Work\Controller\Crmapi;


class WeChatStoreController extends viewTpl
{
    public $u;
    public $t;
    public $c;
    public $visitType = "api";

    //预加载处理类
    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
        $this->api = $this->router->getApi();
    }

    //微信小店获取订单信息
    function getStoreOrderApi(){
        $request = Input('get.', '', 'trim,addslashes');

        $Model = new \Model\Api\WeChatStoreModel($request);
        $Model->getStoreOrderApi($request);
        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $Model->result));
    }

    //微信小店获取订单中收货地址中用户手机号信息
    function getStoreOrderAddressInfoApi(){
        $request = Input('get.', '', 'trim,addslashes');

        $Model = new \Model\Api\WeChatStoreModel($request);
        $Model->getStoreOrderAddressInfoApi($request);
        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $Model->result));
    }

    //结尾魔术函数
    function __destruct()
    {

    }

}
