<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/13
 * Time: 23:25
 */

namespace Work\Controller\Crmapi;


class ModuleController extends viewTpl
{
    public $u;
    public $t;
    public $c;
    public $visitType = "api";
    public $DataControl;
    public $Model;

    //预加载处理类
    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();

        //数据库操作
        $this->DataControl = new \Dbmysql();

    }

    function ThisVerify($request)
    {
        $paramArray = array();
        $paramArray['marketer_id'] = $request['marketer_id'];
        $paramArray['company_id'] = $request['company_id'];
        $paramArray['token'] = $request['token'];
        if (!$this->UserLimit($paramArray)) {
            $result = array();
            $result["list"] = array();
            $result["tokeninc"] = "0";
            $res = array('error' => '1', 'errortip' => "用户token失效", 'result' => $result);
            ajax_return($res);
        }
    }

    //权限列表
    function HomeView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $ModuleModel = new \Model\Crm\ModuleModel($request);
        $result = $ModuleModel->getModuleList($request);

        if($request['language_type'] == 'tw') {
            //转义 errortip
            $Model = new \Model\jianfanModel();
            $data = $Model->gb2312_big5(json_encode($result, JSON_UNESCAPED_UNICODE));
            $result = json_decode($data, true);
        }

        //内部处理
        $res = array('error' => 0, 'errortip' => "获取成功!", 'result' => $result, 'module' => $result['moduleCur']);
        ajax_return($res, $request['language_type'], 1);
    }

    function getLowModuleListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $ModuleModel = new \Model\Crm\ModuleModel($request);
        $result = $ModuleModel->getModuleList($request);

        $res = array('error' => 0, 'errortip' => "获取成功!", 'result' => $result, 'module' => $result['moduleCur']);
        ajax_return($res, $request['language_type'], 1);

    }

    function getCrmLevelView()
    {
        $request = Input('get.', '', 'trim,addslashes');
//        $this->ThisVerify($request);//验证账户 staffer_id

        $ModuleModel = new \Model\Crm\ModuleModel($request);
        $result = $ModuleModel->getModuleList($request);

        $res = array('error' => 0, 'errortip' => "获取成功!", 'result' => $result, 'module' => $result['moduleCur']);
        ajax_return($res, $request['language_type'], 1);

    }

    //更换地址
    function ChangeUrlView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        if ($request['postbe_crmuserlevel'] == '2') {
            if ($request['url'] != '/Customersmanage/telemarketList') {
                $res = array('error' => 1, 'errortip' => "没有权限!");
                ajax_return($res, $request['language_type'], 1);
            }
        }
        if ($request['postbe_crmuserlevel'] == '0') {
            if ($request['url'] == '/ListAllocationManagement/grossList') {
                $res = array('error' => 1, 'errortip' => "没有权限!");
                ajax_return($res, $request['language_type'], 1);
            }
        }

        $ModuleModel = new \Model\Crm\ModuleModel($request);
        $result = $ModuleModel->getModuleList($request);

        $res = array('error' => 0, 'errortip' => "获取成功!", 'result' => $result, 'module' => $result['moduleCur']);
        ajax_return($res, $request['language_type'], 1);

    }

}