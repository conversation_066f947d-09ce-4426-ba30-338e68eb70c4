<?php
/**
 * 跨界活动-公共模块
 * 无登录验证
 */

namespace Work\Controller\Crmapi;


class TransboundaryPublicController extends viewTpl
{

    /**
     * 用户小程序登录
     * @param code
     * @param iv
     * @param encryptedData
     * @param end 端 user/business
     */
    function userLoginAction()
    {
        $request = Input('post.','','trim,addslashes');

        //微信登录
        $ucsWechatModel = new \Model\Crm\TransboundaryWechatModel();
        $wxData = $ucsWechatModel->wxLogin($request);

        //获取用户信息
        $ucsUserModel = new \Model\Crm\TransboundaryUserModel();
        $userData = $ucsUserModel->getUser($wxData, $request['company_id']);

        //根据用户信息创建token
        $ucsTokenModel = new \Model\Crm\TransboundaryTokenModel();
        $userData['end'] = 'user';
        $ucsTokenModel->lssue($userData);

        $field = array();
        $field['token'] = '秘钥';
        $field['user_mobile'] = '手机号';

        $result = array();
        $result['field'] = $field;
        $result['data'] = $ucsTokenModel->result;

        ajax_return(array('error' => $ucsTokenModel->error, 'errortip' => $ucsTokenModel->errortip, 'result' => $result));
    }

    //商户小程序登录
    function businessLoginAction()
    {
        $request = Input('post.','','trim,addslashes');

        //微信登录
        $ucsWechatModel = new \Model\Crm\TransboundaryWechatModel();
        $wxData = $ucsWechatModel->wxLogin($request);

        //获取用户信息
        $ucsUserModel = new \Model\Crm\TransboundaryBusinessModel();
        $userData = $ucsUserModel->getBusiness($wxData);

        //根据用户信息创建token
        $ucsTokenModel = new \Model\Crm\TransboundaryTokenModel();
        $userData['end'] = 'business';
        $ucsTokenModel->lssue($userData);

        $field = array();
        $field['token'] = '秘钥';

        $result = array();
        $result['field'] = $field;
        $result['data'] = $ucsTokenModel->result;

        ajax_return(array('error' => $ucsTokenModel->error, 'errortip' => $ucsTokenModel->errortip, 'result' => $result));
    }

    //活动首页
    public function activityHomeView()
    {
        $request = Input('get.','','trim,addslashes');
        $transboundaryUserModel = new \Model\Crm\TransboundaryUserModel();
        $transboundaryUserModel ->activityHome($request);
        $field = array();
        $field['activity_id'] = '活动ID';
        $field['activity_name'] = '活动名称';
        $field['activity_startdate'] = '活动开始日期';
        $field['activity_enddate'] = '活动结束日期';
        $field['user_nickname'] = '中奖用户姓名';
        $field['record_winningamount'] = '中奖金额';

        $result = array();
        $result['field'] = $field;
        $result['data'] = $transboundaryUserModel->result;

        ajax_return(array('error' => $transboundaryUserModel->error, 'errortip' => $transboundaryUserModel->errortip, 'result' => $result));
    }
}