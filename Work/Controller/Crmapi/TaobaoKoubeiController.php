<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/13
 * Time: 23:25
 */

namespace Work\Controller\Crmapi;


class TaobaoKoubeiController extends viewTpl
{
    public $u;
    public $t;
    public $c;
    public $visitType = "api";

    //预加载处理类
    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
        $this->api = $this->router->getApi();
    }

    //淘宝 饿了么口碑 获取客资信息
    function getTaobaoLeadsApi(){
        $request = Input('get.', '', 'trim,addslashes');

        $Model = new \Model\Api\TaobaoKoubeiModel($request);
        if($request['type'] == '1'){
            //差找12小时前的  12 小时的数据
            $start_time =date("Y-m-d H:i:s",time()-86400); //
            $end_time = date("Y-m-d H:i:s",time()-43200); //
            $Model->getTaobaoLeadsApi($request,1,100,$start_time,$end_time);
        }elseif($request['type'] == '2'){
            //差找前一天的 一天 的数据
            $start_time =date("Y-m-d H:i:s",time()-86400-86400); //
            $end_time = date("Y-m-d H:i:s",time()-86400); //
            $Model->getTaobaoLeadsApi($request,1,100,$start_time,$end_time);
        }else{
            $Model->getTaobaoLeadsApi($request);
        }
        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $Model->result));
    }


    //结尾魔术函数
    function __destruct()
    {

    }

}
