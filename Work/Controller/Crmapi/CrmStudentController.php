<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/13
 * Time: 23:25
 */

namespace Work\Controller\Crmapi;

class CrmStudentController extends viewTpl
{
    public $u;
    public $t;
    public $c;
    public $visitType = "api";
    public $DataControl;
    public $company_isassist = 0;

    //预加载处理类
    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();

        //数据库操作
        $this->DataControl = new \Dbmysql();

    }


    //本地权限校验入口
    function ThisVerify($request)
    {

        if (!intval($request['marketer_id'])) {
            $res = array('error' => '1', 'errortip' => "未检测到招生人员信息，请重新登录操作", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        if (isset($request['company_id']) && $request['company_id'] !== '') {
            $companyOne = $this->DataControl->getFieldOne("gmc_company", "company_isassist", "company_id='{$request['company_id']}'");
            $this->company_isassist = $companyOne['company_isassist'];
        }
        //叮当助教CRM
        if (isset($request['crm_token']) && $request['crm_token'] !== '') {
            $request['token'] = $request['crm_token'];
        }
        if (empty($request['token'])) {
            $res = array('error' => '1', 'errortip' => "未传入用户Token信息", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
    }

    /**
     * 分配在籍学员
     */

    function crmAllotStudentAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Crm\CrmStudentModel($request);
        $Model->crmAllotStudentAction($request);
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    /**
     * 跟进在籍学生
     * author: ling
     * 对应接口文档 0001
     */
    function crmTrackStudentAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Crm\CrmStudentModel($request);
        $Model->crmTrackStudentAction($request);
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    /**
     * 取消跟踪记录
     * author: ling
     * 对应接口文档 0001
     */
    function crmCancelStudentTrackAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Crm\CrmStudentModel($request);
        $Model->crmCancelStudentTrackAction($request);
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

//----------------------------------------------------查看分界线--------------------------------------------------

    function getCrmStudentOneApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Crm\CrmStudentModel($request);
        $dataOne = $Model->getCrmStudentOneApi($request);
        $result = array();
        $result['student'] = $dataOne['student'];
        $result['family'] = $dataOne['family'];
        $res = array('error' => 1, 'errortip' => "获取CRM的学生信息成功", 'result' => $result);
        ajax_return($res, $request['language_type']);
    }

    // 获取学生试听列表
    function getStudentAuditionListApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Crm\CrmStudentModel($request);
        $list = $Model->getStudentAuditionList($request);
        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "试听班级";
        $field[$k]["fieldname"] = "class_cnname";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "客户意向 ";
        $field[$k]["fieldname"] = "track_intention_level";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "试听课程";
        $field[$k]["fieldname"] = "course_branch";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "试听日期";
        $field[$k]["fieldname"] = "audition_visittime";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "是否到访";
        $field[$k]["fieldname"] = "audition_isvisit_name";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';

        $result = array();
        $result['list'] = $list;
        $result['field'] = $field;
        $res = array('error' => 1, 'errortip' => "获取学生试听列表", 'result' => $result);
        ajax_return($res, $request['language_type']);
    }

    //获取学生柜询列表
    function getStudentInviteListApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Crm\CrmStudentModel($request);
        $dataList = $Model->getStudentInviteList($request);
        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "柜询类型";
        $field[$k]["fieldname"] = "invite_genre";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "客户意向 ";
        $field[$k]["fieldname"] = "invite_level";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "柜询日期";
        $field[$k]["fieldname"] = "invite_visittime";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "是否到访";
        $field[$k]["fieldname"] = "invite_isvisit_name";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';

        $result = array();
        $result['list'] = $dataList;
        $result['field'] = $field;
        $res = array('error' => 1, 'errortip' => "获取学生柜询列表", 'result' => $result);
        ajax_return($res, $request['language_type']);
    }

    //获取学生的跟进记录
    function getStudentTrackListApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Crm\CrmStudentModel($request);
        $dataList = $Model->getStudentTrackList($request);
        $result = array();
        $result['list'] = $dataList['list'];
        $res = array('error' => 1, 'errortip' => "获取学生的跟进记录", 'result' => $result);
        ajax_return($res, $request['language_type']);
    }

    //获取学生读过的班级
    function getCrmStuClassApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Crm\CrmStudentModel($request);
        $dataList = $Model->getCrmStuClass($request);
        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "班级名称";
        $field[$k]["fieldname"] = "class_cnname";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "班级别名";
        $field[$k]["fieldname"] = "class_enname";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "班级编号";
        $field[$k]["fieldname"] = "class_branch";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "课程别名称";
        $field[$k]["fieldname"] = "course_cnname";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "课程别编号";
        $field[$k]["fieldname"] = "course_branch";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "班级状态";
        $field[$k]["fieldname"] = "class_status_name";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "在班状态";
        $field[$k]["fieldname"] = "study_isreading_name";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "全部课次";
        $field[$k]["fieldname"] = "hour_num";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "班级上课次数";
        $field[$k]["fieldname"] = "hour_checkingnum";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "学生考勤次数";
        $field[$k]["fieldname"] = "hourstudy_num";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';

        $result = array();
        $result['field'] = $field;
        $result['classList'] = $dataList['classList'];
        $result['coursetypeList'] = $dataList['coursetypeList'];
        $result['coursecatList'] = $dataList['coursecatList'];
        if ($dataList['classList']) {
            $errortip = '获取学生读过的班级';
        } else {
            $errortip = '暂无在读班级';
        }
        $res = array('error' => 1, 'errortip' => $errortip, 'result' => $result);
        ajax_return($res, $request['language_type']);
    }

    //获取学生读过的课程
    function getCrmStuCourseListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Crm\CrmStudentModel($request);
        $dataList = $Model->getCrmStuCourseList($request);
        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "课程别";
        $field[$k]["fieldname"] = "course_cnname";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "课程别编号";
        $field[$k]["fieldname"] = "course_branch";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "在读状态";
        $field[$k]["fieldname"] = "study_isreading_name";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "协议名称";
        $field[$k]["fieldname"] = "agreement_cnname";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "原价";
        $field[$k]["fieldname"] = "tuition_originalprice";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "协议价";
        $field[$k]["fieldname"] = "tuition_sellingprice";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "购买课次";
        $field[$k]["fieldname"] = "ordercourse_buynums";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "剩余课次";
        $field[$k]["fieldname"] = "coursebalance_time";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "课程余额";
        $field[$k]["fieldname"] = "coursebalance_figure";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';

        $result = array();
        $result['field'] = $field;
        $result['courseList'] = $dataList['courseList'];
        $result['coursetypeList'] = $dataList['coursetypeList'];
        if ($dataList['courseList']) {
            $errortip = '获取学生读过的班级';
        } else {
            $errortip = '暂无在读课程';
        }
        $res = array('error' => 1, 'errortip' => $errortip, 'result' => $result);
        ajax_return($res, $request['language_type']);
    }
//    --------------------------------------------------------------------分割线--------------------------

    //学校班级列表
    function getSchollClassView(){
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Crm\CrmStudentModel($request);
        $dataList = $Model->getSchollClass($request);
        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "班级ID";
        $field[$k]["fieldname"] = "class_id";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "集团ID";
        $field[$k]["fieldname"] = "company_id";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "学校ID";
        $field[$k]["fieldname"] = "school_id";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "学校编号";
        $field[$k]["fieldname"] = "school_branch";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "班级中文名";
        $field[$k]["fieldname"] = "class_cnname";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "班级英文名";
        $field[$k]["fieldname"] = "class_enname";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;

        $result = array();
        $result['field'] = $field;
        $result['list'] = $dataList['list'];
        $res = array('error' => 0, 'errortip' => "获取学校班级信息", 'result' => $result);
        ajax_return($res, $request['language_type']);
    }
    //学校班级列表  --- 进行中的班级  并且过滤掉公开课
    function getSchollClassingView(){
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Crm\CrmStudentModel($request);
        $dataList = $Model->getSchollClassing($request);
        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "班级ID";
        $field[$k]["fieldname"] = "class_id";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "集团ID";
        $field[$k]["fieldname"] = "company_id";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "学校ID";
        $field[$k]["fieldname"] = "school_id";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "学校编号";
        $field[$k]["fieldname"] = "school_branch";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "班级中文名";
        $field[$k]["fieldname"] = "class_cnname";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "班级英文名";
        $field[$k]["fieldname"] = "class_enname";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;

        $result = array();
        $result['field'] = $field;
        $result['list'] = $dataList['list'];
        $res = array('error' => 0, 'errortip' => "获取学校班级信息", 'result' => $result);
        ajax_return($res, $request['language_type']);
    }

    // 在籍学员列表
    function getCrmStudyStudentListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Crm\CrmStudentModel($request);
        $dataList = $Model->getCrmStudyStudentList($request);
        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "中文名";
        $field[$k]["fieldname"] = "student_cnname";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "英文名";
        $field[$k]["fieldname"] = "student_enname";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "标签";
        $field[$k]["fieldname"] = "student_crmtag";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $field[$k]["istag"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "学员编号";
        $field[$k]["fieldname"] = "student_branch";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "在读班组";
        $field[$k]["fieldname"] = "study_coursetype_cnname";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "在读班种";
        $field[$k]["fieldname"] = "study_coursecat_cnname";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "意向课程";
        $field[$k]["fieldname"] = "course_cnname";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $field[$k]["istag"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "性别";
        $field[$k]["fieldname"] = "student_sex";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "出生日期";
        $field[$k]["fieldname"] = "student_birthday";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "主要联系人";
        $field[$k]["fieldname"] = "parenter_cnname";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "主要联系电话";
        $field[$k]["fieldname"] = "parenter_mobile";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "Email";
        $field[$k]["fieldname"] = "parenter_email";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';

        $result = array();
        $result['field'] = $field;
        $result['list'] = $dataList['list'];
        $result['allnum'] = $dataList['all_num'];
        $res = array('error' => 0, 'errortip' => "获取CRM在籍学生", 'result' => $result);
        ajax_return($res, $request['language_type']);

    }

    // 扩科名单待分配
    function getCrmStudentExpandListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Crm\CrmStudentModel($request);
        $dataList = $Model->getCrmStudentExpandList($request);
        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "中文名";
        $field[$k]["fieldname"] = "student_cnname";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "英文名";
        $field[$k]["fieldname"] = "student_enname";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "标签";
        $field[$k]["fieldname"] = "student_crmtag";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $field[$k]["istag"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "学员编号";
        $field[$k]["fieldname"] = "student_branch";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "在读班组";
        $field[$k]["fieldname"] = "study_coursetype_cnname";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "意向课程";
        $field[$k]["fieldname"] = "course_cnname";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $field[$k]["istag"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "性别";
        $field[$k]["fieldname"] = "student_sex";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "出生日期";
        $field[$k]["fieldname"] = "student_birthday";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "主要联系人";
        $field[$k]["fieldname"] = "parenter_cnname";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "主要联系电话";
        $field[$k]["fieldname"] = "parenter_mobile";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "Email";
        $field[$k]["fieldname"] = "parenter_email";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';

        $result = array();
        $result['field'] = $field;
        $result['list'] = $dataList['list'];
        $result['allnum'] = $dataList['all_num'];
        $res = array('error' => 0, 'errortip' => "获取CRM在籍学生", 'result' => $result);
        ajax_return($res, $request['language_type']);

    }

    //流失列表
    function getCrmLossStudentListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Crm\CrmStudentModel($request);
        $dataList = $Model->getCrmLossStudentList($request);
        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "中文名";
        $field[$k]["fieldname"] = "student_cnname";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "英文名";
        $field[$k]["fieldname"] = "student_enname";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "标签";
        $field[$k]["fieldname"] = "student_crmtag";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $field[$k]["istag"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "意向课程";
        $field[$k]["fieldname"] = "course_cnname";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $field[$k]["istag"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "学员编号";
        $field[$k]["fieldname"] = "student_branch";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "性别";
        $field[$k]["fieldname"] = "student_sex";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "出生日期";
        $field[$k]["fieldname"] = "student_birthday";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "主要联系人";
        $field[$k]["fieldname"] = "parenter_cnname";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "主要联系电话";
        $field[$k]["fieldname"] = "parenter_mobile";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "Email";
        $field[$k]["fieldname"] = "parenter_email";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "流失原因";
        $field[$k]["fieldname"] = "changelog_note";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "流失时间";
        $field[$k]["fieldname"] = "changelog_day";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
//        $k++;
//        $field[$k]["fieldstring"] = "流失时间";
//        $field[$k]["fieldname"] = "enrolled_createtime";
//        $field[$k]["show"] = '1';
//        $field[$k]["custom"] = '1';

        $result = array();
        $result['field'] = $field;
        $result['list'] = $dataList['list'];
//        $result['note_list'] = $dataList['note_list'];
        $result['allnum'] = $dataList['all_num'];
        $res = array('error' => 0, 'errortip' => "获取CRM流失学生", 'result' => $result);
        ajax_return($res, $request['language_type']);
    }

    //添加客户标签
    function addCrmStudentTagsAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        if (!$request['student_id']) {
            $res = array('error' => '1', 'errortip' => "名单id有误", 'result' => array());
            ajax_return($res,$request['language_type']);
        }
        if($request['label_list']){
            $tagarray = json_decode(stripslashes($request['label_list']),true);
            if(is_array($tagarray)) {
                $tagstr = implode(',', $tagarray);
            }else{
                $tagstr = $request['label_list'];
            }
            $data = array();
            $data['student_crmtag'] = $tagstr;
            $this->DataControl->updateData("smc_student", "student_id='{$request['student_id']}' and company_id = '{$request['company_id']}' ", $data);

            $res = array('error' => 0, 'errortip' => '添加标签成功', 'result' => array());
            ajax_return($res, $request['language_type']);
        }else{
            $res = array('error' => '1', 'errortip' => "标签数据有误", 'result' => array());
            ajax_return($res,$request['language_type']);
        }
    }

    //在籍学员跟进列表
    function getCrmStudyStuTrackListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Crm\CrmStudentModel($request);
        $dataList = $Model->getCrmStudyStuTrackList($request);
        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "中文名";
        $field[$k]["fieldname"] = "student_cnname";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "英文名";
        $field[$k]["fieldname"] = "student_enname";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "标签";
        $field[$k]["fieldname"] = "student_crmtag";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $field[$k]["istag"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "意向课程";
        $field[$k]["fieldname"] = "course_cnname";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $field[$k]["istag"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "学员编号";
        $field[$k]["fieldname"] = "student_branch";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "跟踪状态";
        $field[$k]["fieldname"] = "student_tracestatus_name";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';

        $k++;
        $field[$k]["fieldstring"] = "意向星级";
        $field[$k]["fieldname"] = "student_intention_level";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $field[$k]["islevel"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "性别";
        $field[$k]["fieldname"] = "student_sex";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "出生日期";
        $field[$k]["fieldname"] = "student_birthday";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "主要联系人";
        $field[$k]["fieldname"] = "parenter_cnname";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "主要联系电话";
        $field[$k]["fieldname"] = "parenter_mobile";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "Email";
        $field[$k]["fieldname"] = "parenter_email";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';

        if ($request['student_type'] == 0) {
        }
            $k++;
            $field[$k]["fieldstring"] = "在读班组";
            $field[$k]["fieldname"] = "study_coursetype_cnname";
            $field[$k]["show"] = '1';
            $field[$k]["custom"] = '1';
        if ($request['student_type'] == 0) {
        }
            $k++;
            $field[$k]["fieldstring"] = "跟进班组";
            $field[$k]["fieldname"] = "track_coursetype_cnname";
            $field[$k]["show"] = '1';
            $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "跟踪次数";
        $field[$k]["fieldname"] = "track_num";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        if ($request['student_type'] == 1) {
            $k++;
            $field[$k]["fieldstring"] = "流失原因";
            $field[$k]["fieldname"] = "changelog_note";
            $field[$k]["show"] = '1';
            $field[$k]["custom"] = '1';
            $k++;
            $field[$k]["fieldstring"] = "流失日期";
            $field[$k]["fieldname"] = "changelog_day";
            $field[$k]["show"] = '1';
            $field[$k]["custom"] = '1';
        }

        $k++;
        $field[$k]["fieldstring"] = "主要负责人";
        $field[$k]["fieldname"] = "zhu_marketer_name";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        if ($this->company_isassist == 1) {
            $k++;
            $field[$k]["fieldstring"] = "协助负责人";
            $field[$k]["fieldname"] = "fu_marketer_name";
            $field[$k]["show"] = '1';
            $field[$k]["custom"] = '1';
        }
        $k++;
        $field[$k]["fieldstring"] = "最后跟踪内容";
        $field[$k]["fieldname"] = "last_track_note";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "分配时间";
        $field[$k]["fieldname"] = "principal_createtime";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';

        if ($request['student_type'] == 1) {
//            $k++;
//            $field[$k]["fieldstring"] = "最后跟踪时间";
//            $field[$k]["fieldname"] = "last_tracktime";
//            $field[$k]["show"] = '1';
//            $field[$k]["custom"] = '1';
            $k++;
            $field[$k]["fieldstring"] = "入校时间";
            $field[$k]["fieldname"] = "enrolled_createtime";
            $field[$k]["show"] = '1';
            $field[$k]["custom"] = '1';
        }

        $result = array();
        $result['field'] = $field;
        $result['list'] = $dataList['list'];
        $result['allnum'] = $dataList['allnum'];
        $res = array('error' => 0, 'errortip' => "获取CRM跟进列表", 'result' => $result);
        ajax_return($res, $request['language_type']);

    }


    /**
     * 获取课程班组
     * author: ling
     * 对应接口文档 0001
     */
    function getCourseTypeView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);

        if(isset($request['ishaveopenclass']) && $request['ishaveopenclass'] == '1'){
            $datawhere = '1 ';
        }else{
            $datawhere = '1 and coursetype_isopenclass =0';
        }
        if (isset($request['zhu_coursetype_id']) && $request['zhu_coursetype_id'] !== '') {
            $datawhere .= " and coursetype_id<>'{$request['zhu_coursetype_id']}' ";
        }
        if (isset($request['fu_coursetype_id']) && $request['fu_coursetype_id'] !== '') {
            $datawhere .= " and coursetype_id<>'{$request['fu_coursetype_id']}' ";

        }
        $dataList = $this->DataControl->selectClear("select coursetype_cnname,coursetype_id,coursetype_branch from smc_code_coursetype where company_id='{$request['company_id']}' and {$datawhere} ");
        if (!$dataList) {
            $dataList = array();
        }
        $result['list'] = $dataList;
        $res = array('error' => 0, 'errortip' => "获取课程班组", 'result' => $result);
        ajax_return($res, $request['language_type']);
    }

}