<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/13
 * Time: 23:25
 */

namespace Work\Controller\Crmapi;


class LossclientController extends viewTpl
{
    public $u;
    public $t;
    public $c;
    public $visitType = "api";
    public $DataControl;
    public $company_isassist = 0;

    //预加载处理类
    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();

        //数据库操作
        $this->DataControl = new \Dbmysql();

    }

    function ThisVerify($request)
    {
        if (!intval($request['marketer_id'])) {
            $res = array('error' => '1', 'errortip' => "未检测到招生人员信息，请重新登录操作", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        if (empty($request['token'])) {
            $res = array('error' => '1', 'errortip' => "未传入用户Token信息", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        if (isset($request['company_id']) && $request['company_id'] !== '') {
            $companyOne = $this->DataControl->getFieldOne("gmc_company", "company_isassist", "company_id='{$request['company_id']}'");
            $this->company_isassist = $companyOne['company_isassist'];
        }
        $paramArray = array();
        $paramArray['marketer_id'] = $request['marketer_id'];
        $paramArray['token'] = $request['token'];
        if (!$this->UserLimit($paramArray)) {
            $result = array();
            $result["list"] = array();
            $result["tokeninc"] = "0";
            $res = array('error' => '1', 'errortip' => "用户token失效", 'result' => $result);
            ajax_return($res, $request['language_type']);
        }
    }

    //流失客户管理
    function homeView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Crm\LossclientModel($request);
        if (!$request['school_id']) {
            $res = array('error' => '1', 'errortip' => "学校id错误", 'result' => 0);
            ajax_return($res, $request['language_type']);
        }
        if (!$request['company_id']) {
            $res = array('error' => '1', 'errortip' => "公司id错误", 'result' => 0);
            ajax_return($res, $request['language_type']);
        }

        $clientList = $Model->getLossclientList($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "中文名";
        $field[$k]["fieldname"] = "client_cnname";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '0';
        $field[$k]["is_method"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "英文名";
        $field[$k]["fieldname"] = "client_enname";
        $field[$k]["show"] = '0';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "标签";
        $field[$k]["fieldname"] = "client_tag";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $field[$k]["istag"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "意向课程";
        $field[$k]["fieldname"] = "course_cnname";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $field[$k]["istag"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "性别";
        $field[$k]["fieldname"] = "client_sex";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "年龄";
        $field[$k]["fieldname"] = "client_age";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "主要联系人";
        $field[$k]["fieldname"] = "family_cnname";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "主要联系电话";
        $field[$k]["fieldname"] = "client_mobile";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $field[$k]["isTypeTab"] = 1;
        $k++;
//        if($request['language_type'] == 'tw'){
        $field[$k]["fieldstring"] = "Email";
        $field[$k]["fieldname"] = "client_email";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
//        }
        $field[$k]["fieldstring"] = "历史最高意向星级";
        $field[$k]["fieldname"] = "client_intention_maxlevel";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $field[$k]["islevel"] = 'true';
        $k++;
        $field[$k]["fieldstring"] = "是否参与试听";
        $field[$k]["fieldname"] = "ishaveaudition";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "最后跟踪时间";
        $field[$k]["fieldname"] = "last_tracktime";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "最后跟踪内容";
        $field[$k]["fieldname"] = "last_track_note";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "主要负责人";
        $field[$k]["fieldname"] = "marketer_name";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        if ($this->company_isassist == 1) {
            $k++;
            $field[$k]["fieldstring"] = "协助负责人";
            $field[$k]["fieldname"] = "fu_marketer_name";
            $field[$k]["show"] = '1';
            $field[$k]["custom"] = '1';
        }

        $k++;
        $field[$k]["fieldstring"] = "活动";
        $field[$k]["fieldname"] = "activity_name";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "渠道类型";
        $field[$k]["fieldname"] = "client_source";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "渠道明细";
        $field[$k]["fieldname"] = "channel_name";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "接触点";
        $field[$k]["fieldname"] = "client_frompage";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "推荐人";
        $field[$k]["fieldname"] = "client_sponsor";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "招生来源类型";
        $field[$k]["fieldname"] = "client_fromtype_name";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "审核人";
        $field[$k]["fieldname"] = "shenhe_name";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "审核时间";
        $field[$k]["fieldname"] = "shenhe_time";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';

        $k++;
        $field[$k]["fieldstring"] = "名单创建时间";
        $field[$k]["fieldname"] = "client_createtime";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "联系地址";
        $field[$k]["fieldname"] = "client_address";
        $field[$k]["show"] = '0';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "省";
        $field[$k]["fieldname"] = "province_name";
        $field[$k]["show"] = '0';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "市";
        $field[$k]["fieldname"] = "city_name";
        $field[$k]["show"] = '0';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "区";
        $field[$k]["fieldname"] = "area_name";
        $field[$k]["show"] = '0';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "备注";
        $field[$k]["fieldname"] = "client_remark";
        $field[$k]["show"] = '0';
        $field[$k]["custom"] = '1';

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;


        if ($clientList['list']) {

            $result['list'] = $clientList['list'];
        } else {

            $result['list'] = array();
        }

        if (isset($clientList['allnums']) && $clientList['allnums'] != "") {
            $allnum = $clientList['allnums'];
        } else {
            $allnum = 0;
        }
        if ($result['list']) {
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result, 'allnum' => $allnum);
        } else {
            $res = array('error' => '1', 'errortip' => "暂无流失客户信息", 'result' => $result, 'allnum' => $allnum);
        }

        ajax_return($res, $request['language_type']);
    }

    //流失客户管理 转为招生目标
    function changeLossClientApi()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $request['client_distributionstatus'] = 0;
        $request['client_tracestatus'] = 0;
        $request['client_ischaserlapsed'] = 0;
        $request['client_isinvalidreview'] = 0;
        $request['invalidnote_code'] = '';

        if (!$request['client_id']) {
            $res = array('error' => '1', 'errortip' => "客户id错误", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        $Model = new \Model\Crm\LossclientModel($request);
        $bools = $Model->changeLossClientToClient($request);

        if ($bools == true) {
            $this->addCrmWorkLog($request['company_id'], $request['school_id'], $request['marketer_id'], "流失客户管理->流失客户管理", '转为招生目标', dataEncode($request));
            $res = array('error' => '0', 'errortip' => "流转成功", 'result' => array());
        } else {
            $res = array('error' => '1', 'errortip' => "流转失败", 'result' => array());
        }

        ajax_return($res, $request['language_type']);
    }


    //流失客户管理 转为 毛名单
    function changeLossClientTmkApi()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Crm\LossclientModel($request);
        $bools = $Model->changeLossClientTmkApi($request);

        if ($bools == true) {
            $this->addCrmWorkLog($request['company_id'], $request['school_id'], $request['marketer_id'], "流失客户管理->流失客户管理", '转回毛名单', dataEncode($request));
            $res = array('error' => '0', 'errortip' => $Model->errortip, 'result' => array());
        } else {
            $res = array('error' => '1', 'errortip' => $Model->errortip, 'result' => array());
        }

        ajax_return($res, $request['language_type']);

    }

    //流失客户管理 转为我的意向客户
    function changeLossIntentionApi()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        if (!$request['client_id']) {
            $res = array('error' => '1', 'errortip' => "客户id错误", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        if (!$request['marketer_id']) {
            $res = array('error' => '1', 'errortip' => "marketer_id错误", 'result' => array());
            ajax_return($res, $request['language_type']);
        }

        $request['client_distributionstatus'] = 1;
        $request['client_tracestatus'] = 0;
        $request['client_ischaserlapsed'] = 0;
        $request['client_isinvalidreview'] = 0;
        $request['invalidnote_code'] = '';
        $Model = new \Model\Crm\LossclientModel($request);
        $bools = $Model->changeLossClientToIntention($request);
        if ($bools) {
            $this->addCrmWorkLog($request['company_id'], $request['school_id'], $request['marketer_id'], "流失客户管理->流失客户管理", '转为意向客户', dataEncode($request));
            $res = array('error' => '0', 'errortip' => "已成功转为您的意向客户", 'result' => array());
        } else {
            $res = array('error' => '1', 'errortip' => "流转失败", 'result' => array());
        }
        ajax_return($res, $request['language_type']);

    }

    function lossExamineAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Crm\LossclientModel($request);
        $bools = $Model->lossExamine($request);
        if ($bools) {
            $res = array('error' => '0', 'errortip' => "审核通过", 'result' => array());
            $this->addCrmWorkLog($request['company_id'], $request['school_id'], $request['marketer_id'], "流失客户管理->流失客户管理", '流失客户审核', dataEncode($request));

        } else {
            $res = array('error' => '1', 'errortip' => "审核失败", 'result' => array());
        }
        ajax_return($res, $request['language_type']);
    }

    function lossInvalidExamineAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Crm\LossclientModel($request);
        $bools = $Model->lossInvalidExamine($request);
        if ($bools) {
            $res = array('error' => '0', 'errortip' => "审核通过", 'result' => array());
            $this->addCrmWorkLog($request['company_id'], $request['school_id'], $request['marketer_id'], "无效客户管理->无效客户管理", '无效客户审核', dataEncode($request));

        } else {
            $res = array('error' => '1', 'errortip' => "审核失败", 'result' => array());
        }
        ajax_return($res, $request['language_type']);
    }

    function getInvalidClientListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Crm\LossclientModel($request);
        if (!$request['school_id']) {
            $res = array('error' => '1', 'errortip' => "学校id错误", 'result' => 0);
            ajax_return($res, $request['language_type']);
        }
        if (!$request['company_id']) {
            $res = array('error' => '1', 'errortip' => "公司id错误", 'result' => 0);
            ajax_return($res, $request['language_type']);
        }

        $clientList = $Model->getInvalidClientList($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "姓名";
        $field[$k]["fieldname"] = "client_allname";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '0';
        $field[$k]["is_method"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "英文名";
        $field[$k]["fieldname"] = "client_enname";
        $field[$k]["show"] = '0';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "标签";
        $field[$k]["fieldname"] = "client_tag";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $field[$k]["istag"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "意向课程";
        $field[$k]["fieldname"] = "course_cnname";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $field[$k]["istag"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "性别";
        $field[$k]["fieldname"] = "client_sex";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "年龄";
        $field[$k]["fieldname"] = "client_age";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "主要联系人";
        $field[$k]["fieldname"] = "family_cnname";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "主要联系电话";
        $field[$k]["fieldname"] = "client_mobile";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $field[$k]["isTypeTab"] = 1;
        $k++;
//        if($request['language_type'] == 'tw'){
        $field[$k]["fieldstring"] = "Email";
        $field[$k]["fieldname"] = "client_email";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
//        }
        $field[$k]["fieldstring"] = "历史最高意向星级";
        $field[$k]["fieldname"] = "client_intention_maxlevel";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $field[$k]["islevel"] = 'true';
        $k++;
        $field[$k]["fieldstring"] = "是否参与试听";
        $field[$k]["fieldname"] = "ishaveaudition";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "最后跟踪时间";
        $field[$k]["fieldname"] = "last_tracktime";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "最后跟踪内容";
        $field[$k]["fieldname"] = "last_track_note";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "主要负责人";
        $field[$k]["fieldname"] = "marketer_name";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        if ($this->company_isassist == 1) {
            $k++;
            $field[$k]["fieldstring"] = "协助负责人";
            $field[$k]["fieldname"] = "fu_marketer_name";
            $field[$k]["show"] = '1';
            $field[$k]["custom"] = '1';
        }

        $k++;
        $field[$k]["fieldstring"] = "活动";
        $field[$k]["fieldname"] = "activity_name";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "渠道类型";
        $field[$k]["fieldname"] = "client_source";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "渠道明细";
        $field[$k]["fieldname"] = "channel_name";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "接触点";
        $field[$k]["fieldname"] = "client_frompage";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "推荐人";
        $field[$k]["fieldname"] = "client_sponsor";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "招生来源类型";
        $field[$k]["fieldname"] = "client_fromtype_name";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;

        $field[$k]["fieldstring"] = "无效原因";
        $field[$k]["fieldname"] = "invalid_tj_reason";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "无效提交时间";
        $field[$k]["fieldname"] = "invalid_tj_time";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;

        if($request['client_isinvalidreview'] == '1') {
            $field[$k]["fieldstring"] = "审核人";
            $field[$k]["fieldname"] = "shenhe_name";
            $field[$k]["show"] = '1';
            $field[$k]["custom"] = '1';
            $k++;
            $field[$k]["fieldstring"] = "审核时间";
            $field[$k]["fieldname"] = "shenhe_time";
            $field[$k]["show"] = '1';
            $field[$k]["custom"] = '1';
            $k++;
        }
        $field[$k]["fieldstring"] = "名单创建时间";
        $field[$k]["fieldname"] = "client_createtime";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "联系地址";
        $field[$k]["fieldname"] = "client_address";
        $field[$k]["show"] = '0';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "省";
        $field[$k]["fieldname"] = "province_name";
        $field[$k]["show"] = '0';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "市";
        $field[$k]["fieldname"] = "city_name";
        $field[$k]["show"] = '0';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "区";
        $field[$k]["fieldname"] = "area_name";
        $field[$k]["show"] = '0';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "备注";
        $field[$k]["fieldname"] = "client_remark";
        $field[$k]["show"] = '0';
        $field[$k]["custom"] = '1';

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;


        if ($clientList['list']) {

            $result['list'] = $clientList['list'];
        } else {

            $result['list'] = array();
        }

        if (isset($clientList['allnums']) && $clientList['allnums'] != "") {
            $allnum = $clientList['allnums'];
        } else {
            $allnum = 0;
        }
        if ($result['list']) {
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result, 'allnum' => $allnum);
        } else {
            $res = array('error' => '1', 'errortip' => "暂无流失客户信息", 'result' => $result, 'allnum' => $allnum);
        }

        ajax_return($res, $request['language_type']);
    }


    /**
     * 无效名单转为有效名单
     * author: ling
     * 对应接口文档 0001
     */
    function toEffectiveAction()
    {

        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        if (!$request['client_id']) {
            $res = array('error' => '1', 'errortip' => "客户id错误", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        $Model = new \Model\Crm\LossclientModel($request);
        $bools = $Model->toEffectiveAction($request);

        if ($bools == true) {
            $this->addCrmWorkLog($request['company_id'], $request['school_id'], $request['marketer_id'], "无意向客户管理->无意向客户管理", '转为招生有效名单', dataEncode($request));
            $res = array('error' => '0', 'errortip' => "流转成功", 'result' => array());
        } else {
            $res = array('error' => '1', 'errortip' => "流转失败", 'result' => array());
        }

        ajax_return($res, $request['language_type']);

    }

}
