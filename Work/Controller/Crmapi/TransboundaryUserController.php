<?php
/**
 * 跨界活动-用户端
 * 须登录
 */

namespace Work\Controller\Crmapi;

class TransboundaryUserController extends viewTpl
{
    public $u;
    public $t;
    public $c;
    public $user_id;

    //预加载处理类
    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
        $request = Input('POST.', '', 'trim,addslashes');
        $this->verifyLogin($request['token'], 'user');
    }

    /**
     * 秘钥校验及解析
     * @param $token
     */
    function verifyLogin($token, $end)
    {
        if (empty($token) || empty($end)) {
            $this->error = 1;
            $this->errortip = "token必须传入!";
            $this->result = array();
            ajax_return(array('error' => $this->error, 'errortip' => $this->errortip, 'result' => $this->result));
        }
        $param = array();
        $param['token'] = $token;
        $param['end'] = $end;

        //校验解析token
        $ucsTokenModel = new \Model\Crm\TransboundaryTokenModel();
        $data = $ucsTokenModel->verification($param);

        return $data;
    }

    /**
     * 更新用户信息
     */
    public function updateUserInfoAction()
    {
        $request = Input('POST.', '', 'trim,addslashes');
        $transboundaryUserModel = new \Model\Crm\TransboundaryUserModel();
        $transboundaryUserModel->updateUserInfo($request);
        $field = array();
        $field['user_id'] = 'ID';
        $field['user_mobile'] = '手机号';
        $field['user_nickname'] = '昵称';
        $field['user_img'] = '头像';
        $field['user_name'] = '姓名';
        $field['user_age'] = '年龄';
        $field['user_sex'] = '性别';

        $result = array();
        $result['field'] = $field;
        $result['data'] = $transboundaryUserModel->result;

        ajax_return(array('error' => $transboundaryUserModel->error, 'errortip' => $transboundaryUserModel->errortip, 'result' => $result));

    }

    /**
     * 我的奖品列表
     */
    public function myPrizeListView()
    {
        $request = Input('POST.', '', 'trim,addslashes');
        $transboundaryUserModel = new \Model\Crm\TransboundaryUserModel();
        $transboundaryUserModel->myPrizeList($request);
        $field = array();
        $field['activity_name'] = '活动名称';
        $field['record_winningamount'] = '中奖金额';
        $field['record_createtime'] = '中奖时间';
        $field['record_settlementstatus_name'] = '结算状态';

        $result = array();
        $result['field'] = $field;
        $result['data'] = $transboundaryUserModel->result;

        ajax_return(array('error' => $transboundaryUserModel->error, 'errortip' => $transboundaryUserModel->errortip, 'result' => $result));

    }

    /**
     * 参与抽奖
     */
    public function drawPrizesAction()
    {
        $request = Input('POST.', '', 'trim,addslashes');
        $transboundaryUserModel = new \Model\Crm\TransboundaryUserModel();
        $transboundaryUserModel->drawPrizes($request);
        $field = array();
        $field['winning_amount'] = '中奖金额';
        $field['is_winning'] = '中奖结果 1:中奖了 0:没有中(谢谢惠顾)';

        $result = array();
        $result['field'] = $field;
        $result['data'] = $transboundaryUserModel->result;

        ajax_return(array('error' => $transboundaryUserModel->error, 'errortip' => $transboundaryUserModel->errortip, 'result' => $result));

    }




}