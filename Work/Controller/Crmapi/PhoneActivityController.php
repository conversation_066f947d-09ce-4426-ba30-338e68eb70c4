<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/13
 * Time: 23:25
 */

namespace Work\Controller\Crmapi;


class PhoneActivityController extends viewTpl{
    public $u;
    public $t;
    public $c;
    public $visitType="api";
    public $DataControl;

    //预加载处理类
    function __construct() {
        parent::__construct ();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
        $this->api = $this->router->getApi();

        //数据库操作
        $this->DataControl = new \Dbmysql();
    }

    //本地权限校验入口
    function ThisVerify($request)
    {

        if (!intval($request['marketer_id'])) {
            $res = array('error' => '1', 'errortip' => "未检测到招生人员信息，请重新登录操作", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        //叮当助教CRM
        if (isset($request['crm_token']) && $request['crm_token'] !== '') {
            $request['token'] = $request['crm_token'];
        }
        if (isset($request['company_id']) && $request['company_id'] !== '') {
            $companyOne = $this->DataControl->getFieldOne("gmc_company", "company_isassist", "company_id='{$request['company_id']}'");
            $this->company_isassist = $companyOne['company_isassist'];
        }
        if (empty($request['token'])) {
            $res = array('error' => '1', 'errortip' => "未传入用户Token信息", 'result' => array());
            ajax_return($res, $request['language_type']);
        }

        $paramArray = array();
        $paramArray['marketer_id'] = $request['marketer_id'];
        $paramArray['token'] = $request['token'];
        if (!$this->UserLimit($paramArray)) {
            $result = array();
            $result["list"] = array();
            $result["tokeninc"] = "0";
            $res = array('error' => '1', 'errortip' => "用户token失效", 'result' => $result);
            ajax_return($res, $request['language_type']);
        }
    }

    //招生目标 -- >> 某个活动的详细情况
    function goalPhoneActivityOneApi(){
        $request = Input('get.','','trim,addslashes');
        if(!isset($request['activity_id']) || $request['activity_id'] == ''){
            $res = array('error' => '1', 'errortip' => '对应活动必选', 'result' => array());
            ajax_return($res,$request['language_type']);
        }
        $goalOne = $this->DataControl->getFieldOne("crm_sell_activity","activity_id,activity_starttime,activity_endtime", "activity_id = '{$request['activity_id']}' ");
        $starttime = strtotime($goalOne['activity_starttime']);
        $endtime = strtotime($goalOne['activity_endtime']);
        $nowtime = time();

        //model
        $Model = new \Model\Crm\SellgoalModel($request);
        $dataList = $Model->goalPhoneActivityOneApi($request);

        $field = array();
        $field["activity_id"] = "目标id";
        $field["activity_name"] = "活动名称";
        $field["activity_type"] = "是否集团活动";
        $field["marketer_id"] = "职工ID";
        $field["marketer_name"] = "职工姓名";
        $field["marketer_mobile"] = "职工手机号";
        $field["activity_starttime"] = "开始时间";
        $field["activity_endtime"] = "结束时间";
        $field["activity_img"] = "主图";
        $field["activity_content"] = "活动内容";
        $field["activity_createtime"] = "创建时间";
        $field["company_id"] = "企业id";
        $field["company_cnname"] = "企业名称";
        $field["company_logo"] = "企业LOGO";
        $field["school_id"] = "学校ID";
        $field["school_address"] = "学校地址";

        if ($starttime < $nowtime && $nowtime < $endtime) {
            $result = array();
            if($dataList){
                $result["field"] = $field;
                $result["data"] = $dataList;
                $res = array('error' => '0', 'errortip' => '某个活动的详细情况', 'isend' => 0 , 'result' => $result);
            }else{
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '数据不存在', 'isend' => 0 , 'result' => $result);
            }
        }elseif($starttime > $nowtime){
            $result = array();
            $result["field"] = $field;
            $result["data"] = $dataList;
            $res = array('error' => '0', 'errortip' => '活动未开始', 'isend' => 2 ,'result' => $result);
        }else{
            $result = array();
            $result["field"] = $field;
            $result["data"] = $dataList;
            $res = array('error' => '0', 'errortip' => '活动已经结束', 'isend' => 1 ,'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }
    //招生目标 -- >> 添加活动报名人员信息
    function addPhoneActivityAction(){
        $request = Input('post.','','trim,addslashes');
        if(!isset($request['client_cnname']) || $request['client_cnname'] == '' || $request['client_mobile'] == ''){
            $res = array('error' => '1', 'errortip' => '宝宝姓名和联系手机不能为空！', 'result' => array());
            ajax_return($res,$request['language_type']);
        }
        if( $request['activity_id'] == '' ){
            $res = array('error' => '1', 'errortip' => '活动id不能为空！', 'result' => array());
            ajax_return($res,$request['language_type']);
        }
        //出生日期不能限制必填
        /*if( $request['client_age'] == '' ){
            $res = array('error' => '1', 'errortip' => '出生日期不能为空！', 'result' => array());
            ajax_return($res,$request['language_type']);
        }*/

        /*if( $activityOne['company_id'] != '9999' && $activityOne['company_id'] != '8888') {
            $sendrz = $this->DataControl->getFieldOne('crm_mislog', "mislog_sendcode,mislog_time", "mislog_mobile='{$request['client_mobile']}' and mislog_tilte = 'CRM手机活动报名'", "order by mislog_time DESC");
            if ($sendrz['mislog_time'] < (time() - 600)) {
                $res = array('error' => '1', 'errortip' => '验证码已失效（验证码10分钟内有效）!');
                ajax_return($res,$request['language_type']);
            }
            if (!$sendrz || $sendrz['mislog_sendcode'] !== $request['L_verifycode']) {
                $res = array('error' => '1', 'errortip' => '短信验证码错误!');
                ajax_return($res,$request['language_type']);
            }
        }*/


        $ClientModel = new \Model\Crm\ClientModel($request);
        $dataList = $ClientModel->addPhoneActivityAction($request);
        if($dataList){
            $res = array('error' => '0', 'errortip' => "你的信息提交成功，请耐心等待教师回电。");
        }else{
            $res = array('error' => $ClientModel->error, 'errortip' => $ClientModel->errortip);
        }
        ajax_return($res,$request['language_type']);
    }

    //招生渠道-》名单提交接口
    function addPhoneChannelAction(){
        $request = Input('post.','','trim,addslashes');
        if(!isset($request['client_cnname']) || $request['client_cnname'] == '' || $request['client_mobile'] == ''){
            $res = array('error' => '1', 'errortip' => '宝宝姓名和联系手机不能为空！', 'result' => array());
            ajax_return($res,$request['language_type']);
        }
        if($request['client_source'] == '' && $request['channel_name'] == '微信原生推广页'){
            $request['client_source'] = '微信';
            $request['channel_name'] = '朋友圈广告';
        }

        if (isset($request['school_branch']) && $request['school_branch'] !== '') {
            $schoolOne = $this->DataControl->getFieldOne("smc_school", "school_id", "company_id = '{$request['company_id']}' and school_branch = '{$request['school_branch']}'");
            if ($schoolOne) {
                $request['school_id'] = $schoolOne['school_id'];
            }
        }else{
            $request['school_id'] = 0;
        }

        //model
        $Model = new \Model\Crm\ClientModel($request);
        $dataList = $Model->addPhoneChannelAction($request);
        if($dataList){
            $res = array('error' => '0', 'errortip' => "你的信息提交成功，请耐心等待教师回电。");
        }else{
            $res = array('error' => '1', 'errortip' => '你的信息提交失败。','modelerror'=>$Model->error,'modelerrortip'=>$Model->errortip);
        }
        ajax_return($res,$request['language_type']);
    }


    //获取手机验证码
    function getverifycodeApi()
    {
        $request = Input('get.','','trim,addslashes');
        $company_id = trim($request['company_id']);
        $mobile = trim($request['client_mobile']);
        //一小时内发送次数
        $mintime = time()-3600;
        $mislognum = $this->DataControl->selectOne("select count(mislog_id) as mislognum from crm_mislog where mislog_mobile='{$mobile}' and mislog_tilte = 'CRM手机活动报名' and mislog_time >= '{$mintime}' limit 0,1 ");
        if($mislognum['mislognum'] > 5){
            $res = array('error' => '1', 'errortip' => '您的手机已超出发送验证码次数，请联系客服！');
            ajax_return($res,$request['language_type']);
        }
        //最近一次发送时间
        $sendmisrz = $this->DataControl->getFieldOne('crm_mislog',"mislog_time","mislog_mobile='{$mobile}' and mislog_tilte = 'CRM手机活动报名'","order by mislog_time DESC");
        if($sendmisrz && ( time() - $sendmisrz['mislog_time']) < 60){
            $res = array('error' => '1', 'errortip' => '验证码已发送！');
            ajax_return($res,$request['language_type']);
        }else{
            $tilte = "CRM手机活动报名";
            $sendcode = rand(111111,999999);
            $contxt = "用户您好！您的手机验证码为{$sendcode}，请完成验证，如非本人操作，请忽略本短信。";
            //短信发送
            if($this->Sendmisgo($mobile,$contxt,$tilte,$sendcode,$company_id)){
                $res = array('error' => '0', 'errortip' => '发送成功',"bakfuntion"=>"okmotify");
                ajax_return($res,$request['language_type']);
            }else{
                $res = array('error' => '1', 'errortip' => '发送失败!',"bakfuntion"=>"errormotify");
                ajax_return($res,$request['language_type']);
            }
        }

    }


    //招生目标 -- >> 添加活动报名人员信息  -- 微信小程序
    function addWeChatActivityAction(){
        $request = Input('post.','','trim,addslashes');
        //$QRcode  20241017区域联合活动补充的 弹窗二维码
//        $QRcode = "https://pic.kedingdang.com/schoolmanage/202410171417x911063871.png";

        //$QRcode  20241017区域联合活动补充的 弹窗二维码
        $QRcode = "";
        $schoolOne = $this->DataControl->selectOne("select school_branch from smc_school where company_id = '8888' and school_id = '{$request['school_id']}' ");
        if($schoolOne['school_branch']){
            $data = array();
            $data['xiaoqubianhao'] = $schoolOne['school_branch'];
            $data['qudao'] = 1;
            $getBackurl = request_by_curl("https://eduappv2.kidcastle.com.cn/api/kddqiwei/lianxiwoerweima", dataEncode($data), "GET");
            $bakData = json_decode($getBackurl, true);
            if ($bakData['error'] == '0') {
                $QRcode = $bakData['data']['xiaoqu'][$schoolOne['school_branch']]['erweimadizhi'];
            }else{
                $QRcode = "https://pic.kedingdang.com/schoolmanage/202410171417x911063871.png";
            }
        }else{
            $QRcode = "https://pic.kedingdang.com/schoolmanage/202410171417x911063871.png";
        }

        $activityOne  = $this->DataControl->getOne('crm_sell_activity',"activity_id='{$request['activity_id']}'");
        if($activityOne['company_id'] != '8888' && $activityOne['company_id'] != '1001'){
            $QRcode = '';
        }

        if(!isset($request['client_cnname']) || $request['client_cnname'] == '' || $request['client_mobile'] == ''){
            $res = array('error' => '1', 'errortip' => '宝宝姓名和联系手机不能为空！', 'result' => array(),'QRcode'=>$QRcode);
            ajax_return($res,$request['language_type']);
        }
        if( $request['activity_id'] == '' ){
            $res = array('error' => '1', 'errortip' => '活动id不能为空！', 'result' => array(),'QRcode'=>$QRcode);
            ajax_return($res,$request['language_type']);
        }
//        if( $request['L_verifycode'] == '' ){
//            $res = array('error' => '1', 'errortip' => '短信验证码不能为空！', 'result' => array());
//            ajax_return($res,$request['language_type']);
//        }
        //如果是验券模式，必须上传券码编号
        if($activityOne['activity_pattern'] == '1'){
            $request['freevoucher_pid'] = trim($request['freevoucher_pid']);
            if(!isset($request['freevoucher_pid']) || $request['freevoucher_pid'] == '') {
                $res = array('error' => '1', 'errortip' => '请输入您的券码编号！', 'result' => array(),'QRcode'=>$QRcode);
                ajax_return($res,$request['language_type']);
            }
            $nowtime = date("Y-m-d",time());
            if(!$this->DataControl->selectOne("select freevoucher_id from crm_sell_activity_freevoucher WHERE freevoucher_pid = '{$request['freevoucher_pid']}' and freevoucher_starttime <= '{$nowtime}' and freevoucher_exittime >= '{$nowtime}'")){
                $res = array('error' => '1', 'errortip' => '该券已经超过有效期！', 'result' => array(),'QRcode'=>$QRcode);
                ajax_return($res,$request['language_type']);
            }
            if($this->DataControl->selectOne("select freevoucher_id from crm_sell_activity_freevoucher WHERE freevoucher_pid = '{$request['freevoucher_pid']}' and freevoucher_isuse = '1'")){
                $res = array('error' => '1', 'errortip' => '您所输入的券码已被使用！', 'result' => array(),'QRcode'=>$QRcode);
                ajax_return($res,$request['language_type']);
            }
            if(!$this->DataControl->selectOne("select freevoucher_id from crm_sell_activity_freevoucher WHERE freevoucher_pid = '{$request['freevoucher_pid']}' and activity_id = '{$request['activity_id']}'")){
                $res = array('error' => '1', 'errortip' => '您所输入的券码编号不属于该活动！', 'result' => array(),'QRcode'=>$QRcode);
                ajax_return($res,$request['language_type']);
            }
        }

//        $sendrz = $this->DataControl->getFieldOne('crm_mislog', "mislog_sendcode,mislog_time", "mislog_mobile='{$request['client_mobile']}' and mislog_tilte = 'CRM微信小程序活动报名'", "order by mislog_time DESC");
//        if ($sendrz['mislog_time'] < (time() - 600)) {
//            $res = array('error' => '1', 'errortip' => '验证码已失效（验证码10分钟内有效）!');
//            ajax_return($res,$request['language_type']);
//        }
//        if (!$sendrz || $sendrz['mislog_sendcode'] !== $request['L_verifycode']) {
//            $res = array('error' => '1', 'errortip' => '短信验证码错误!');
//            ajax_return($res,$request['language_type']);
//        }

        $clientOne = $this->DataControl->getFieldOne("crm_client", "client_id", "client_mobile='{$request['client_mobile']}' and activity_id = '{$request['activity_id']}' and company_id = '{$activityOne['company_id']}'", "order by client_updatetime DESC");
        if ($clientOne) {
            if($request['activity_id'] == '89'){
                $res = array('error' => '1', 'errortip' => '您已報名參加本次的活動，請耐心等待分校老師與您電話聯繫，無需重覆報名，謝謝！','QRcode'=>$QRcode);
                ajax_return($res,$request['language_type']);
            }else {
                $res = array('error' => '1', 'errortip' => '亲您已报名参加本次活动,耐心等待工作人员电话沟通,无需重复报名!','QRcode'=>$QRcode);
                ajax_return($res,$request['language_type']);
            }
        }

        //model
        $request['company_id'] = $activityOne['company_id'];
        $Model = new \Model\Crm\ClientModel($request);
        $dataList = $Model->addPhoneActivityAction($request,1);

        //是否购买课包  0 否 1 是
        if($request['is_buy_package'] == '1'){
            //前端跳转课包链接时，会判断字段为空 并 字段 is_buy_package = 1 时跳转
            //如果提交失败的话  不跳转  --- 产品要求
            $QRcode = $dataList?'':$QRcode;
        }
        if($dataList){
            $res = array('error' => '0', 'errortip' => "你的信息提交成功，请耐心等待教师回电。",'QRcode'=>$QRcode, 'result' => $Model->result);
        }else{
            if($Model->errortip){
                $res = array('error' => $Model->error, 'errortip' => $Model->errortip,'QRcode'=>$QRcode, 'result' => $Model->result);
            }else {
                $res = array('error' => '1', 'errortip' => '您的信息已存在，请勿重复提交您的信息。。','QRcode'=>$QRcode, 'result' => $Model->result);
            }
        }
        ajax_return($res,$request['language_type']);
    }
    //获取手机验证码 -- 微信小程序
    function getWeChatCodeApi()
    {
        $request = Input('get.','','trim,addslashes');

        $mobile = trim($request['client_mobile']);

        if( $request['activity_id'] == '' ){
            $res = array('error' => '1', 'errortip' => '活动id不能为空！', 'result' => array());
            ajax_return($res,$request['language_type']);
        }
        $activityOne  = $this->DataControl->getOne('crm_sell_activity',"activity_id='{$request['activity_id']}'");
        $company_id = $activityOne['company_id'];
        //一小时内发送次数
        $mintime = time()-3600;
        $mislognum = $this->DataControl->selectOne("select count(mislog_id) as mislognum from crm_mislog where mislog_mobile='{$mobile}' and mislog_tilte = 'CRM微信小程序活动报名' and mislog_time >= '{$mintime}' limit 0,1 ");
        if($mislognum['mislognum'] > 5){
            $res = array('error' => '1', 'errortip' => '您的手机已超出发送验证码次数，请联系客服！');
            ajax_return($res,$request['language_type']);
        }
        //最近一次发送时间
        $sendmisrz = $this->DataControl->getFieldOne('crm_mislog',"mislog_time","mislog_mobile='{$mobile}' and mislog_tilte = 'CRM微信小程序活动报名'","order by mislog_time DESC");
        if($sendmisrz && ( time() - $sendmisrz['mislog_time']) < 60){
            $res = array('error' => '1', 'errortip' => '验证码已发送！');
            ajax_return($res,$request['language_type']);
        }else{
            $tilte = "CRM微信小程序活动报名";
            $sendcode = rand(111111,999999);
            $contxt = "用户您好！您的手机验证码为{$sendcode}，请完成验证，如非本人操作，请忽略本短信。";
            //短信发送
            if($this->Sendmisgo($mobile,$contxt,$tilte,$sendcode,$company_id)){
                $res = array('error' => '0', 'errortip' => '发送成功',"bakfuntion"=>"okmotify");
                ajax_return($res,$request['language_type']);
            }else{
                $res = array('error' => '1', 'errortip' => '发送失败!',"bakfuntion"=>"errormotify");
                ajax_return($res,$request['language_type']);
            }
        }

    }


    //活动班种 适配表
    function ActivityCoursecatApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        if (!isset($request['activity_id']) || $request['activity_id'] == '') {
            $res = array('error' => '1', 'errortip' => '对应活动必选', 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        if (!isset($request['adaptive']) || $request['adaptive'] == '') {
            $res = array('error' => '1', 'errortip' => '筛选类型必须选择', 'result' => array());
            ajax_return($res, $request['language_type']);
        }

        //model
        $Model = new \Model\Gmc\ActivityCrmModel($request);
        $dataList = $Model->ActivityCoursecatApi($request);

        $field = array();
        $field[0]["fieldname"] = "coursecat_id";
        $field[0]["fieldstring"] = "班种ID";
        $field[0]["show"] = 0;
        $field[0]["custom"] = 0;

        $field[1]["fieldname"] = "coursecat_cnname";
        $field[1]["fieldstring"] = "班种名称";
        $field[1]["show"] = 1;
        $field[1]["custom"] = 0;

        $field[2]["fieldname"] = "coursecat_branch";
        $field[2]["fieldstring"] = "班种编号";
        $field[2]["show"] = 1;
        $field[2]["custom"] = 0;

        $field[3]["fieldname"] = "coursetype_cnname";
        $field[3]["fieldstring"] = "班组名称";
        $field[3]["show"] = 1;
        $field[3]["custom"] = 0;

        $field[4]["fieldname"] = "coursetype_branch";
        $field[4]["fieldstring"] = "班组编号";
        $field[4]["show"] = 1;
        $field[4]["custom"] = 0;

        if ($dataList) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $dataList['datalist'];
            if (!$result["data"]) {
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '暂无适配班种', 'result' => $result);
                ajax_return($res, $request['language_type']);
            }

            $res = array('error' => '0', 'errortip' => "获取成功", 'allnum' => $dataList['count'], 'result' => $result);
        } else {
            $result = array();

            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '暂无适配班种', 'result' => $result);
        }

        ajax_return($res, $request['language_type']);
    }

    //活动班种适配表 -- 活动班种批量适配操作
    function batchActCoursecatAction(){
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        //model
        $Model = new \Model\Gmc\ActivityCrmModel($request);
        $dataList = $Model->batchActCoursecatAction($request);

        $result = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $dataList);
        ajax_return($result,$request['language_type']);
    }


    //结尾魔术函数
    function __destruct()
    {

    }
}
