<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/13
 * Time: 23:25
 */

namespace Work\Controller\Crmapi;


class CallCenterController extends viewTpl
{
    public $u;
    public $t;
    public $c;
    public $visitType = "api";
    public $DataControl;
    public $company_isassist = 0;

    //预加载处理类
    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();

        //数据库操作
        $this->DataControl = new \Dbmysql();
    }

    //本地权限校验入口
    function ThisVerify($request)
    {
        if (!intval($request['marketer_id'])) {
            $res = array('error' => '1', 'errortip' => "未检测到招生人员信息，请重新登录操作", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        //叮当助教CRM
        if (isset($request['crm_token']) && $request['crm_token'] !== '') {
            $request['token'] = $request['crm_token'];
        }
        if (isset($request['company_id']) && $request['company_id'] !== '') {
            $companyOne = $this->DataControl->getFieldOne("gmc_company", "company_isassist", "company_id='{$request['company_id']}'");
            $this->company_isassist = $companyOne['company_isassist'];
        }
        if (empty($request['token'])) {
            $res = array('error' => '1', 'errortip' => "未传入用户Token信息", 'result' => array());
            ajax_return($res, $request['language_type']);
        }

        $paramArray = array();
        $paramArray['marketer_id'] = $request['marketer_id'];
        $paramArray['token'] = $request['token'];
        if (!$this->UserLimit($paramArray)) {
            $result = array();
            $result["list"] = array();
            $result["tokeninc"] = "0";
            $res = array('error' => '1', 'errortip' => "用户token失效", 'result' => $result);
            ajax_return($res, $request['language_type']);
        }
    }

    //慧捷双呼 -- 获取平台后端接口访问token  -- 其实不需要本接口 controller  -- model中设置即可调用
    function getHjTokenView(){
        $request = Input('get.', '', 'trim,addslashes');
        $Model = new \Model\Crm\CallCenterModel();
        $result = $Model->getHjToken($request);
        print_r($result);die;
    }
    //慧捷双呼 -- 双呼接口
    function getHjTwoCallView(){
        $request = Input('get.', '', 'trim,addslashes');
        $Model = new \Model\Crm\CallCenterModel($request);
        $result = $Model->getHjTwoCall($request);
        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $Model->result), $request['language_type']);
    }
    //慧捷事件订阅 -- 事件回调接口
    function callBackHjDataView(){
        //获取 RAW 传递的参数
        $request = file_get_contents("php://input");
        if(!$request){
            $request = Input('get.', '', 'trim,addslashes');
        }
        $Model = new \Model\Crm\CallCenterModel($request);
        $result = $Model->callBackHjData($request);
        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $Model->result), $request['language_type']);
    }
    //慧捷 -- 根据callId查询录音记录
    function getHjCallidRecordingView(){
        $request = Input('get.', '', 'trim,addslashes');
        $Model = new \Model\Crm\CallCenterModel($request);
        $result = $Model->getHjCallidRecording($request);
        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $Model->result), $request['language_type']);
    }
    //慧捷 -- 通过callId调听录音
    function getHjCallidRecordingFileView(){
        $request = Input('get.', '', 'trim,addslashes');
        $Model = new \Model\Crm\CallCenterModel($request);
        $result = $Model->getHjCallidRecordingFile($request);
        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $Model->result), $request['language_type']);
    }


    //合力 -- 获取平台后端接口访问token  -- 其实不需要本接口 controller  -- model中设置即可调用
    function getHliTokenView(){
        $request = Input('get.', '', 'trim,addslashes');
        $Model = new \Model\Crm\CallCenterModel();
        $result = $Model->getHliToken($request);
        print_r($result);die;
    }
    //合力 IVR -- 双呼接口
    function getHliTwoCallView(){
        $request = Input('get.', '', 'trim,addslashes');
        $Model = new \Model\Crm\CallCenterModel($request);
        $result = $Model->getHliTwoCall($request);
        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $Model->result), $request['language_type']);
    }
    //合力事件订阅 -- 事件回调接口
    function callBackHliDataView(){
        //获取 RAW 传递的参数
        $request = Input('get.', '', 'trim,addslashes');
        $Model = new \Model\Crm\CallCenterModel($request);
        $result = $Model->callBackHliData($request);
        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $Model->result), $request['language_type']);
    }
    //合力 -- 查询录音记录
    function getHliCallidRecordingView(){
        $request = Input('get.', '', 'trim,addslashes');
        $Model = new \Model\Crm\CallCenterModel($request);
        $result = $Model->getHliCallidRecording($request);
        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $Model->result), $request['language_type']);
    }




    //士决  -- 双呼接口
    function getSjiTwoCallView(){
        $request = Input('get.', '', 'trim,addslashes');
        $Model = new \Model\Crm\CallCenterModel($request);
        $result = $Model->getSjiTwoCall($request);
        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $Model->result), $request['language_type']);
    }



}
