<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong <PERSON>
 * Date: 2017/4/13
 * Time: 23:25
 */

namespace Work\Controller\Crmapi;


class RedbookController extends viewTpl
{
    public $u;
    public $t;
    public $c;
    public $visitType = "api";

    //预加载处理类
    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
        $this->api = $this->router->getApi();
    }

    //小红书 获取客资信息   https://crmapi.kedingdang.com/Redbook/addRedbookLeadsApi 推送地址
    function addRedbookLeadsApi(){
        //获取 RAW 传递的参数
        $request = file_get_contents("php://input");//小红书 推过来的数据这个方式接收的
        if(!$request){
            $request = Input('request.', '', 'trim,addslashes');
        }

        $Model = new \Model\Api\RedbookModel($request);
        $Model->addRedbookLeadsApi($request);
        if($Model->error == '0'){
            $data = ['responsecode'=>'200'];
            echo json_encode($data);die;
        }else{
            ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $Model->result));
        }
    }

    //小红书推送 表单数据
    function addRedbookFormApi(){
        //获取 RAW 传递的参数
        $request = file_get_contents("php://input");
        if(!$request){
            $request = Input('request.', '', 'trim,addslashes');//小红书表单应该是 post 推过来的数据这个方式接收的
        }

        $Model = new \Model\Api\RedbookModel($request);
        $Model->addRedbookFormApi($request);
        if($Model->error == '0'){
            $data = ['responsecode'=>'200'];
            echo json_encode($data);die;
        }else{
            ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $Model->result));
        }
    }

    //结尾魔术函数
    function __destruct()
    {

    }

}
