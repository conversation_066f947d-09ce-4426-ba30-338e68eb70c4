<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/13
 * Time: 23:25
 */

namespace Work\Controller\Crmapi;


class SellgoalController extends viewTpl{
    public $u;
    public $t;
    public $c;
    public $visitType="api";
    public $DataControl;

    //预加载处理类
    function __construct() {
        parent::__construct ();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
        $this->api = $this->router->getApi();

        //数据库操作
        $this->DataControl = new \Dbmysql();
    }

    //本地权限校验入口
    function ThisVerify($request){
        $paramArray = array();
        $paramArray['marketer_id'] = $request['marketer_id'];
        $paramArray['token'] = $request['token'];
        if(!$this->UserLimit($paramArray)){
            $result = array();
            $result["list"] = array();
            $result["tokeninc"] = "0";
            $res = array('error' => '1', 'errortip' => "用户token失效", 'result' => $result);
            ajax_return($res);
        }
    }

    function aaaView()
    {
        $field = array();
        $field['activity_name'] = "活动标题";
        $field['activity_theme'] = "活动主题";
        $field['activity_starttime'] = "开始时间";
        $field['activity_endtime'] = "结束时间";
        $field['activity_img'] = "主图";
        $field['activity_content'] = "活动内容";
        $field['activity_updatetime'] = "修改时间";

        echo json_encode($field,JSON_UNESCAPED_UNICODE);
    }

    //招生目标 -- 招生目标管理  -- 我的招生目标
    function HomeView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        //model
        $Model = new \Model\Crm\SellgoalModel($request);
        $dataList = $Model->goalList($request);
//        //字段JSON获取
//        $fucMarkString = "/{$this->u}/{$this->t}";
//        $FunctionModel = new \Model\Imc\FunctionModel($request);
//        $field = $FunctionModel->getFunctionField($fucMarkString);

        $field = array();
        $field[0]["fieldname"] = "goal_name";
        $field[0]["fieldstring"] = "招生目标名称";
        $field[0]["show"] = 1;
        $field[0]["custom"] = 0;

        $field[1]["fieldname"] = "goal_mintrack";
        $field[1]["fieldstring"] = "跟踪小目标";
        $field[1]["show"] = 1;
        $field[1]["custom"] = 0;

        $field[2]["fieldname"] = "goal_normtrack";
        $field[2]["fieldstring"] = "跟踪达标";
        $field[2]["show"] = 1;
        $field[2]["custom"] = 0;

        $field[3]["fieldname"] = "goal_mininvite";
        $field[3]["fieldstring"] = "柜询/试听小目标";
        $field[3]["show"] = 1;
        $field[3]["custom"] = 0;

        $field[4]["fieldname"] = "goal_norminvite";
        $field[4]["fieldstring"] = "柜询/试听达标";
        $field[4]["show"] = 1;
        $field[4]["custom"] = 0;

        $field[5]["fieldname"] = "goal_minofficial";
        $field[5]["fieldstring"] = "转正小目标";
        $field[5]["show"] = 1;
        $field[5]["custom"] = 0;

        $field[6]["fieldname"] = "goal_normofficial";
        $field[6]["fieldstring"] = "转正达标";
        $field[6]["show"] = 1;
        $field[6]["custom"] = 0;

        $field[7]["fieldname"] = "goal_starttime";
        $field[7]["fieldstring"] = "开始执行时间";
        $field[7]["show"] = 1;
        $field[7]["custom"] = 0;

        $field[8]["fieldname"] = "goal_endtime";
        $field[8]["fieldstring"] = "计划结束时间";
        $field[8]["show"] = 1;
        $field[8]["custom"] = 0;

        $field[9]["fieldname"] = "participant";
        $field[9]["fieldstring"] = "目标参与人";
        $field[9]["show"] = 1;
        $field[9]["custom"] = 0;

        $field[10]["fieldname"] = "marketer_name";
        $field[10]["fieldstring"] = "创建人";
        $field[10]["show"] = 1;
        $field[10]["custom"] = 0;

        if($dataList){
            $result["fieldcustom"] = 1;
            $result["field"] = $field;
            $result["list"] = $dataList['datalist'];
            if($result["list"]){
                $res = array('error' => '0', 'errortip' => '获取招生目标信息成功', 'allnum' => $dataList['count'], 'result' => $result);
            }else{
                $result["list"] = array();
                $res = array('error' => '1', 'errortip' => '暂无招生目标信息', 'allnum' => 0, 'result' => $result);
            }
        }else{
            $result["list"] = array();
            $res = array('error' => '1', 'errortip' => '暂无招生目标信息', 'allnum' => 0, 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }
    //招生目标 -- 某个目标的详细情况
    function goalOneApi()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        if(!isset($request['goal_id']) || $request['goal_id'] == ''){
            $res = array('error' => '1', 'errortip' => '对应目标必选', 'result' => array());
            ajax_return($res);
        }
        //model
        $Model = new \Model\Crm\SellgoalModel($request);
        $dataList = $Model->goalOneApi($request);

//        //字段JSON获取
//        $fucMarkString = "/{$this->u}/{$this->api}Api";
//        $FunctionModel = new \Model\Imc\FunctionModel($request);
//        $field = $FunctionModel->getFunctionField($fucMarkString);
        $field = array();
        $field["goal_id"] = "目标id";
        $field["goal_name"] = "目标名称";
        $field["marketer_id"] = "职工ID";
        $field["marketer_name"] = "职工姓名";
        $field["goal_starttime"] = "开始时间";
        $field["goal_endtime"] = "结束时间";
        $field["goal_mintrack"] = "跟踪小目标";
        $field["goal_normtrack"] = "跟踪达标数";
        $field["goal_mininvite"] = "柜讯/试听小目标";
        $field["goal_norminvite"] = "柜讯/试听达标数";
        $field["goal_minofficial"] = "转正小目标";
        $field["goal_normofficial"] = "转正达标数";
        $field["participant"] = "参与人";
        $field["participantsex"] = "参与人性别";
        $field["participantid"] = "参与人 id";
        $field["goal_maxtracknumber"] = "自主跟踪最大人次";

        $result = array();
        if($dataList){
            $result["field"] = $field;
            $result["data"] = $dataList;
            $res = array('error' => '0', 'errortip' => '获取某个招生目标信息成功', 'result' => $result);
        }else{
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '获取某个招生目标信息失败', 'result' => $result);
        }

        ajax_return($res,$request['language_type']);
    }
    //招生目标 -- 参与目标考核的所有员工
    function goalCheckMarketerApi()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        if(!isset($request['company_id']) || $request['company_id'] == ''){
            $res = array('error' => '1', 'errortip' => '企业 id 不能为空', 'result' => array());
            ajax_return($res);
        }
        //model
        $Model = new \Model\Crm\SellgoalModel($request);
        $dataList = $Model->goalCheckMarketerApi($request);

//        //字段JSON获取
//        $fucMarkString = "/{$this->u}/{$this->api}Api";
//        $FunctionModel = new \Model\Imc\FunctionModel($request);
//        $field = $FunctionModel->getFunctionField($fucMarkString);
        $field = array();
        $field["marketer_id"] = "职工id";
        $field["marketer_name"] = "职工姓名";
        $field["marketer_sex"] = "职工性别";

        $result = array();
        if($dataList){
            $result["field"] = $field;
            $result["data"] = $dataList;
            $res = array('error' => '0', 'errortip' => '参与目标考核的所有员工', 'result' => $result);
        }else{
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '参与目标考核的所有员工', 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }
    //招生目标 -- 某目标参与成员的线索转正数
    function goalFinishStatusApi()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        if(!isset($request['goal_id']) || $request['goal_id'] == ''){
            $res = array('error' => '1', 'errortip' => '对应目标必选', 'result' => array());
            ajax_return($res);
        }
        //model
        $Model = new \Model\Crm\SellgoalModel($request);
        $dataList = $Model->goalFinishStatusApi($request);

//        //字段JSON获取
//        $fucMarkString = "/{$this->u}/{$this->api}Api";
//        $FunctionModel = new \Model\Imc\FunctionModel($request);
//        $field = $FunctionModel->getFunctionField($fucMarkString);
        $field = array();
        $field["marketer_id"] = "职工 id";
        $field["marketer_name"] = "职工姓名";
        $field["conversionnum"] = "转正数量";

        $result = array();
        if($dataList){
            $result["field"] = $field;
            $result["data"] = $dataList;
            $res = array('error' => '0', 'errortip' => '某目标参与成员的线索转正数', 'result' => $result);
        }else{
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '某目标参与成员的线索转正数', 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }
    //招生目标 -- 某目标参与成员的跟踪柜询完成率
    function goalFinishRateApi()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        if(!isset($request['goal_id']) || $request['goal_id'] == ''){
            $res = array('error' => '1', 'errortip' => '对应目标必选', 'result' => array());
            ajax_return($res);
        }
        //model
        $Model = new \Model\Crm\SellgoalModel($request);
        $dataList = $Model->goalFinishRateApi($request);

//        //字段JSON获取
//        $fucMarkString = "/{$this->u}/{$this->api}Api";
//        $FunctionModel = new \Model\Imc\FunctionModel($request);
//        $field = $FunctionModel->getFunctionField($fucMarkString);
        $field = array();
        $field["marketer_id"] = "职工 id";
        $field["marketer_name"] = "职工姓名";
        $field["goal_starttime"] = "开始时间";
        $field["goal_endtime"] = "结束时间";
        $field["goal_normtrack"] = "跟踪达标";
        $field["goal_norminvite"] = "柜讯/试听达标";
        $field["tracknum"] = "跟踪完成率";
        $field["nottracknum"] = "未完成跟踪率";
        $field["invitenum"] = "柜讯完成率";
        $field["notinvitenum"] = "未完成柜讯率";

        $result = array();
        if($dataList){
            $result["field"] = $field;
            $result["data"] = $dataList;
            if($result["data"]){
                $res = array('error' => '0', 'errortip' => '某目标参与成员的跟踪柜询完成率', 'result' => $result);
            }else{
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '暂无某目标参与成员的跟踪柜询完成率', 'result' => $result);
            }
        }else{
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '暂无某目标参与成员的跟踪柜询完成率', 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }
    //招生目标 -- 添加招生目标
    function addGoalAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        if(!isset($request['goal_name']) || $request['goal_name'] == ''){
            $res = array('error' => '1', 'errortip' => '目标名称不能为空', 'result' => array());
            ajax_return($res);
        }
        if($request['goal_mintrack'] > $request['goal_normtrack']){
            $res = array('error' => '1', 'errortip' => '跟踪最少人次不能大于跟踪达标人次', 'result' => array());
            ajax_return($res);
        }
        if($request['goal_mininvite'] > $request['goal_norminvite']){
            $res = array('error' => '1', 'errortip' => '柜询/试听最少人次不能大于柜询/试听达标人次', 'result' => array());
            ajax_return($res);
        }
        if($request['goal_minofficial'] > $request['goal_normofficial']){
            $res = array('error' => '1', 'errortip' => '转正最少人次不能大于转正达标人次', 'result' => array());
            ajax_return($res);
        }
        if(!isset($request['checkMarketer']) || $request['checkMarketer'] == ''){
            $res = array('error' => '1', 'errortip' => '目标参与人不能为空', 'result' => array());
            ajax_return($res);
        }
        //model
        $Model = new \Model\Crm\SellgoalModel($request);
        $dataList = $Model->addGoalAction($request);

        if($dataList){
			$this->addCrmWorkLog($request['company_id'],$request['school_id'],$request['marketer_id'],"招生目标管理->目标管理",'新增招生目标',dataEncode($request));
            $res = array('error' => '0', 'errortip' => "销售目标新增成功");
        }else{
            $res = array('error' => '1', 'errortip' => '销售目标新增失败');
        }
        ajax_return($res,$request['language_type']);
    }
    //招生目标 -- 修改招生目标
    function updateGoalAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        if(!isset($request['goal_id']) || $request['goal_id'] == ''){
            $res = array('error' => '1', 'errortip' => '对应目标必选', 'result' => array());
            ajax_return($res);
        }
        if(!isset($request['goal_name']) || $request['goal_name'] == ''){
            $res = array('error' => '1', 'errortip' => '目标名称不能为空', 'result' => array());
            ajax_return($res);
        }
        if($request['goal_mintrack'] > $request['goal_normtrack']){
            $res = array('error' => '1', 'errortip' => '跟踪最少人次不能大于跟踪达标人次', 'result' => array());
            ajax_return($res);
        }
        if($request['goal_mininvite'] > $request['goal_norminvite']){
            $res = array('error' => '1', 'errortip' => '柜询/试听最少人次不能大于柜询/试听达标人次', 'result' => array());
            ajax_return($res);
        }
        if($request['goal_minofficial'] > $request['goal_normofficial']){
            $res = array('error' => '1', 'errortip' => '转正最少人次不能大于转正达标人次', 'result' => array());
            ajax_return($res);
        }
        if(!isset($request['checkMarketer']) || $request['checkMarketer'] == ''){
            $res = array('error' => '1', 'errortip' => '目标参与人不能为空', 'result' => array());
            ajax_return($res);
        }

        $goalOne = false;
        $marketerOne = $this->DataControl->selectOne("select s.account_class,p.school_id,p.postbe_crmuserlevel from crm_marketer as m 
                left join smc_staffer as s ON m.staffer_id = s.staffer_id 
                LEFT join gmc_staffer_postbe as p ON s.staffer_id = p.staffer_id 
                where m.company_id = '{$request['company_id']}' and m.marketer_id = '{$request['marketer_id']}'  ");
        if($marketerOne['account_class'] == '1') {
            $goalOne = TRUE;
        }else{
            if($marketerOne['school_id'] == $request['school_id'] && $marketerOne['postbe_crmuserlevel'] == '1'){
                $goalOne = TRUE;
            }else {
                $goalOne = $this->DataControl->getFieldOne("crm_sell_goal","goal_id", "goal_id = '{$request['goal_id']}' and marketer_id = '{$request['marketer_id']}' ");
            }
        }

        if ($goalOne) {
            //model
            $Model = new \Model\Crm\SellgoalModel($request);
            $dataList = $Model->updateGoalAction($request,$marketerOne['account_class']);

//            //字段JSON获取
//            $fucMarkString = "/{$this->u}/{$this->c}Action";
//            $FunctionModel = new \Model\Imc\FunctionModel($request);
//            $field = $FunctionModel->getFunctionField($fucMarkString);
            $field = array();
            $field['goal_name'] = "目标名称";
            $field['goal_starttime'] = "开始时间";
            $field['goal_endtime'] = "结束时间";
            $field['goal_normtrack'] = "跟踪达标";
            $field['goal_mintrack'] = "跟踪小目标";
            $field['goal_norminvite'] = "柜讯/试听达标";
            $field['goal_mininvite'] = "柜讯/试听小目标";
            $field['goal_normofficial'] = "转正达标";
            $field['goal_minofficial'] = "转正小目标";
            $field['goal_createtime'] = "创建时间";
            $field['goal_upatetime'] = "修改时间";

            if ($dataList) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $dataList;
				$this->addCrmWorkLog($request['company_id'],$request['school_id'],$request['marketer_id'],"招生目标管理->目标管理",'修改招生目标',dataEncode($request));
                $res = array('error' => '0', 'errortip' => "招生目标修改成功", 'result' => $result);
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '招生目标修改失败', 'result' => $result);
            }
        }else{
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '您没有修改权限', 'result' => $result);
        }

        ajax_return($res,$request['language_type']);
    }
    //招生目标 -- 删除招生目标
    function delGoalAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        if (!$request['goal_id']) {
            $res = array('error' => '1', 'errortip' => "招生目标id不能为空", 'result' => array());
            ajax_return($res);
        }

        $marketerOne = $this->DataControl->selectOne("select s.account_class,p.school_id,p.postbe_crmuserlevel from crm_marketer as m 
                left join smc_staffer as s ON m.staffer_id = s.staffer_id 
                LEFT join gmc_staffer_postbe as p ON s.staffer_id = p.staffer_id 
                where m.company_id = '{$request['company_id']}' and m.marketer_id = '{$request['marketer_id']}'  ");
        if($marketerOne['account_class'] != '1') {
            if($marketerOne['school_id'] == $request['school_id'] && $marketerOne['postbe_crmuserlevel'] == '1'){

            }else {
                if(!$this->DataControl->getFieldOne("crm_sell_goal","goal_id","goal_id='{$request['goal_id']}' and marketer_id='{$request['marketer_id']}'")){
                    $res = array('error' => '1', 'errortip' => "您不是创建人，没有权限删除", 'result' => array());
                    ajax_return($res);
                }
            }
        }

        //model
        $Model = new \Model\Crm\SellgoalModel($request);
        $bools = $Model->delGoalAction($request['goal_id'],$request['marketer_id'],$marketerOne['account_class']);

        if ($bools) {
			$this->addCrmWorkLog($request['company_id'],$request['school_id'],$request['marketer_id'],"招生目标管理->目标管理",'删除招生目标',dataEncode($request));
            $res = array('error' => '0', 'errortip' => "删除成功", 'result' => array());
        } else {
            $res = array('error' => '1', 'errortip' => "删除失败", 'result' => array());
        }

        ajax_return($res,$request['language_type']);
    }
    //招生目标 -- 目标创建人管理
    function goalFounderApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        //model
        $Model = new \Model\Crm\SellgoalModel($request);
        $dataList = $Model->goalFounderApi($request);

//        //字段JSON获取
//        $fucMarkString = "/{$this->u}/{$this->api}Api";
//        $FunctionModel = new \Model\Imc\FunctionModel($request);
//        $field = $FunctionModel->getFunctionField($fucMarkString);
        $field = array();
        $field["marketer_id"] = "创建人职工id";
        $field["marketer_name"] = "创建人职工姓名";

        $result = array();
        if($dataList){
            $result["field"] = $field;
            $result["data"] = $dataList;
            if($result["data"]){
                $res = array('error' => '0', 'errortip' => '目标创建人', 'result' => $result);
            }else{
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '暂无目标创建人', 'result' => $result);
            }
        }else{
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '暂无目标创建人', 'result' => $result);
        }

        ajax_return($res,$request['language_type']);
    }
    //招生目标 -- >> 我的招生目标
    function goalMyView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        //model
        $Model = new \Model\Crm\SellgoalModel($request);
        $dataList = $Model->goalMy($request);

//        //字段JSON获取
//        $fucMarkString = "/{$this->u}/{$this->t}";
//        $FunctionModel = new \Model\Imc\FunctionModel($request);
//        $field = $FunctionModel->getFunctionField($fucMarkString);
        $field = array();
        $field[0]["fieldname"] = "goal_name";
        $field[0]["fieldstring"] = "招生目标名称";
        $field[0]["show"] = 1;
        $field[0]["custom"] = 0;

        $field[1]["fieldname"] = "goal_mintrack";
        $field[1]["fieldstring"] = "考核指标";
        $field[1]["show"] = 1;
        $field[1]["custom"] = 0;

        $field[2]["fieldname"] = "goal_starttime";
        $field[2]["fieldstring"] = "开始执行时间";
        $field[2]["show"] = 1;
        $field[2]["custom"] = 0;

        $field[3]["fieldname"] = "goal_endtime";
        $field[3]["fieldstring"] = "计划结束时间";
        $field[3]["show"] = 1;
        $field[3]["custom"] = 0;

        $field[4]["fieldname"] = "participant";
        $field[4]["fieldstring"] = "目标参与人";
        $field[4]["show"] = 1;
        $field[4]["custom"] = 0;

        $field[5]["fieldname"] = "marketer_name";
        $field[5]["fieldstring"] = "创建人";
        $field[5]["show"] = 1;
        $field[5]["custom"] = 0;

        $result = array();
        if($dataList){
            $result["fieldcustom"] = 1;
            $result["field"] = $field;
            $result["list"] = $dataList['datalist'];
            if($result["list"]){
                $res = array('error' => '0', 'errortip' => '获取招生目标信息','allnum' => $dataList['count'], 'result' => $result);
            }else{
                $result["list"] = array();
                $res = array('error' => '1', 'errortip' => '暂无个人招生目标信息','allnum' => 0, 'result' => $result);
            }
        }else{
            $result["list"] = array();
            $res = array('error' => '1', 'errortip' => '暂无个人招生目标信息','allnum' => 0, 'result' => $result);
        }

        ajax_return($res,$request['language_type']);
    }
    //招生目标 -- 目标创建人管理
    function goalFounderMyApi(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        //model
        $Model = new \Model\Crm\SellgoalModel($request);
        $dataList = $Model->goalFounderMyApi($request);

//        //字段JSON获取
//        $fucMarkString = "/{$this->u}/{$this->api}Api";
//        $FunctionModel = new \Model\Imc\FunctionModel($request);
//        $field = $FunctionModel->getFunctionField($fucMarkString);
        $field = array();
        $field["marketer_id"] = "创建人职工id";
        $field["marketer_name"] = "创建人职工姓名";

        $result = array();
        if($dataList){
            $result["field"] = $field;
            $result["data"] = $dataList;
            if($result["data"]){
                $res = array('error' => '0', 'errortip' => '我的目标创建人管理', 'result' => $result);
            }else{
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '暂无我的目标创建人管理', 'result' => $result);
            }
        }else{
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '暂无我的目标创建人管理', 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }
    //招生活动 -- >> 活动创建人管理
    function ActivityFounderApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        //model
        $Model = new \Model\Crm\SellgoalModel($request);
        $dataList = $Model->ActivityFounderApi($request);

        //字段JSON获取
        $field = array();
        $field["marketer_id"] = "职工id";
        $field["marketer_name"] = "中文名";
        $field["staffer_enname"] = "英文名";

        $result = array();
        if($dataList){
            $result["field"] = $field;
            $result["data"] = $dataList;
            if($result["data"]){
                $res = array('error' => '0', 'errortip' => '活动创建人管理', 'result' => $result);
            }else{
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '暂无活动创建人管理', 'result' => $result);
            }
        }else{
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '暂无活动创建人管理', 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }

    //招生活动管理  --  活动课包
    function activityPackageListApi()
    {
        $request = Input('get.', '', 'strip_tags,addslashes');
        $this->ThisVerify($request);//验证账户
        //model
        $Model = new \Model\Crm\SellgoalModel($request);
        $dataList = $Model->activityPackageListApi($request);

        //字段JSON获取
        $field = array();
        $field["package_id"] = "课包id";
        $field["package_type"] = "课包类型 0 错误 1 有赞";
        $field["package_name"] = "课包名称";
        $field["package_branch"] = "课包编号";
        $field["package_outurl"] = "课包外链";
        $field["package_outtype"] = "外链类型   0 小程序  1 html5";
        $field["package_appid"] = "小程序ID";

        $result = array();
        if ($dataList) {
            $result["field"] = $field;
            $result["list"] = $dataList;
            $res = array('error' => '0', 'errortip' => '课包列表', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => '1', 'errortip' => '暂无课包列表', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }


    //招生目标 -- >> 招生模板
    function ActivitytempApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        //model
        $Model = new \Model\Crm\SellgoalModel($request);
        $dataList = $Model->ActivitytempApi($request);

        //字段JSON获取
        $field = array();
        $field["activitytemp_id"] = "招生模板ID";
        $field["activitytemp_name"] = "招生模板名称";
        $field["activitytemp_styleimg"] = "招生模板图片查看";
        $field["activitytemp_bannerimg"] = "招生模板banner示意查看";
        $field["activitytemp_url"] = "招生地址前缀";

        $result = array();
        if($dataList){
            $result["field"] = $field;
            $result["data"] = $dataList;
            if($result["data"]){
                $res = array('error' => '0', 'errortip' => '活动招生模板', 'result' => $result);
            }else{
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '暂无活动招生模板', 'result' => $result);
            }
        }else{
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '暂无活动招生模板', 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }
    //招生目标 -- >> 招生模板
    function getFrommediaApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        //model
        $Model = new \Model\Crm\SellgoalModel($request);
        $dataList = $Model->getFrommediaApi($request);
        ajax_return($dataList,$request['language_type']);

//        //字段JSON获取
//        $field = array();
//        $field["activitytemp_id"] = "招生模板ID";
//        $field["activitytemp_name"] = "招生模板名称";
//        $field["activitytemp_styleimg"] = "招生模板图片查看";
//        $field["activitytemp_bannerimg"] = "招生模板banner示意查看";
//        $field["activitytemp_url"] = "招生地址前缀";
//
//        $result = array();
//        if($dataList){
//            $result["field"] = $field;
//            $result["data"] = $dataList;
//            if($result["data"]){
//                $res = array('error' => '0', 'errortip' => '活动招生模板', 'result' => $result);
//            }else{
//                $result["data"] = array();
//                $res = array('error' => '1', 'errortip' => '暂无活动招生模板', 'result' => $result);
//            }
//        }else{
//            $result["data"] = array();
//            $res = array('error' => '1', 'errortip' => '暂无活动招生模板', 'result' => $result);
//        }
//        ajax_return($res);
    }
    //招生目标 -- >> 某个活动模板的详细情况
    function ActivitytempOneApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        if(!isset($request['activitytemp_id']) || $request['activitytemp_id'] == ''){
            $res = array('error' => '1', 'errortip' => '模板id必选', 'result' => array());
            ajax_return($res);
        }
        //model
        $Model = new \Model\Crm\SellgoalModel($request);
        $dataList = $Model->ActivitytempOneApi($request);

        $field = array();
        $field["activitytemp_id"] = "招生模板ID";
        $field["activitytemp_name"] = "招生模板名称";
        $field["activitytemp_styleimg"] = "招生模板图片查看";
        $field["activitytemp_bannerimg"] = "招生模板banner示意查看";
        $field["activitytemp_url"] = "招生地址前缀";

        $result = array();
        if($dataList){
            $result["field"] = $field;
            $result["data"] = $dataList;
            if($result["data"]){
                $res = array('error' => '0', 'errortip' => '某个模板的详细情况', 'result' => $result);
            }else{
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '暂无某个模板的详细情况', 'result' => $result);
            }
        }else{
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '暂无某个模板的详细情况', 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }
    //招生目标 -- >> 海报活动
    function getShareposterListView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        //model
        $Model = new \Model\Crm\SellgoalModel($request);
        $dataList = $Model->getShareposterList($request);

        $key = 0;
        $field = array();
        $field[$key]["fieldname"] = "activity_name";
        $field[$key]["fieldstring"] = "活动名称";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $field[$key]["ismethod"] = 1;
        $key++;

        $field[$key]["fieldname"] = "activity_starttime";
        $field[$key]["fieldstring"] = "活动时间";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "clientnum";
        $field[$key]["fieldstring"] = "有效名单数";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "actnum";
        $field[$key]["fieldstring"] = "活动意向客户";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "officialnum";
        $field[$key]["fieldstring"] = "转正人数";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "percentconversion";
        $field[$key]["fieldstring"] = "转化率";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "shareqrcode";
        $field[$key]["fieldstring"] = "二维码";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $field[$key]["isqrcode"] = 1;
        $field[$key]["isschool"] = 1;
        $key++;

        $field[$key]["fieldname"] = "shareqrcodeurl";
        $field[$key]["fieldstring"] = "二维码地址";
        $field[$key]["show"] = 0;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "personalshareqrcode";
        $field[$key]["fieldstring"] = "个人二维码";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $field[$key]["isqrcode"] = 1;
        $field[$key]["isschool"] = 1;
        $key++;

        $field[$key]["fieldname"] = "personalshareqrcodeurl";
        $field[$key]["fieldstring"] = "个人二维码地址";
        $field[$key]["show"] = 0;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "marketer_name";
        $field[$key]["fieldstring"] = "发布人";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "activity_type";
        $field[$key]["fieldstring"] = "活动来源int";
        $field[$key]["show"] = 0;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "activity_typename";
        $field[$key]["fieldstring"] = "活动来源";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "frommedia_name";
        $field[$key]["fieldstring"] = "渠道类型";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "channel_id";
        $field[$key]["fieldstring"] = "渠道明细ID";
        $field[$key]["show"] = 0;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "channel_name";
        $field[$key]["fieldstring"] = "渠道明细";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $result = array();
        if($dataList){
            $result["fieldcustom"] = 1;
            $result["field"] = $field;
            $result["list"] = $dataList['datalist'];
            if($result["list"]){
                $res = array('error' => '0', 'errortip' => '招生活动管理','allnum' => $dataList['count'], 'result' => $result);
            }else{
                $result["list"] = array();
                $res = array('error' => '1', 'errortip' => '暂无招生活动信息','allnum' => 0, 'result' => $result);
            }
        }else{
            $result["list"] = array();
            $res = array('error' => '1', 'errortip' => '暂无招生活动信息','allnum' => 0, 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }
    //招生目标 -- >> 招生活动管理
    function sellActivityView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Crm\SellgoalModel($request);
        $dataList = $Model->sellActivity($request);

        $field = array();
        $key = 0;
        $field[$key]["fieldname"] = "activity_name";
        $field[$key]["fieldstring"] = "招生活动名称";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "activity_starttime";
        $field[$key]["fieldstring"] = "活动时间";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "clientnum";
        $field[$key]["fieldstring"] = "有效名单数";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "actnum";
        $field[$key]["fieldstring"] = "活动意向客户";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "officialnum";
        $field[$key]["fieldstring"] = "转正人数";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "percentconversion";
        $field[$key]["fieldstring"] = "转化率";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "qrcode";
        $field[$key]["fieldstring"] = "学校二维码";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $field[$key]["isqrcode"] = 1;
        $field[$key]["isschool"] = 1;
        $key++;

        $field[$key]["fieldname"] = "personalqrcode";
        $field[$key]["fieldstring"] = "个人二维码";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $field[$key]["isqrcode"] = 1;
        $field[$key]["isschool"] = 0;
        $key++;

        $field[$key]["fieldname"] = "marketer_name";
        $field[$key]["fieldstring"] = "发布人";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "activity_type";
        $field[$key]["fieldstring"] = "活动来源int";
        $field[$key]["show"] = 0;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "activity_typename";
        $field[$key]["fieldstring"] = "活动来源";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "activity_pattern";
        $field[$key]["fieldstring"] = "招生活动模式";
        $field[$key]["show"] = 0;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "qrcodeurl";
        $field[$key]["fieldstring"] = "学校二维码地址";
        $field[$key]["show"] = 0;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "personalqrcodeurl";
        $field[$key]["fieldstring"] = "个人二维码地址";
        $field[$key]["show"] = 0;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "frommedia_name";
        $field[$key]["fieldstring"] = "渠道类型";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "channel_id";
        $field[$key]["fieldstring"] = "渠道明细ID";
        $field[$key]["show"] = 0;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "channel_name";
        $field[$key]["fieldstring"] = "渠道明细";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "QRcodeUrl";
        $field[$key]["fieldstring"] = "学校小程序地址";
        $field[$key]["show"] = 0;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "personalQRcodeUrl";
        $field[$key]["fieldstring"] = "个人小程序地址";
        $field[$key]["show"] = 0;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "now_marketer_name ";
        $field[$key]["fieldstring"] = "当前用户CRM职工名称";
        $field[$key]["show"] = 0;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "now_school_cnname ";
        $field[$key]["fieldstring"] = "当前学校名称";
        $field[$key]["show"] = 0;
        $field[$key]["custom"] = 0;
        $key++;


        $result = array();
        if($dataList){
            $result["fieldcustom"] = 1;
            $result["field"] = $field;
            $result["list"] = $dataList['datalist'];
            if($result["list"]){
                $res = array('error' => '0', 'errortip' => '招生活动管理','allnum' => $dataList['count'], 'result' => $result);
            }else{
                $result["list"] = array();
                $res = array('error' => '1', 'errortip' => '暂无招生活动信息','allnum' => 0, 'result' => $result);
            }
        }else{
            $result["list"] = array();
            $res = array('error' => '1', 'errortip' => '暂无招生活动信息','allnum' => 0, 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }
    //招生目标 -- >> 某个活动的详细情况
    function getActivityShareApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        if(!isset($request['activity_id']) || $request['activity_id'] == ''){
            $res = array('error' => '1', 'errortip' => '对应活动必选', 'result' => array());
            ajax_return($res);
        }
        //model
        $Model = new \Model\Crm\SellgoalModel($request);
        $dataList = $Model->getActivityShareApi($request);

        $field = array();
        $field["activity_shareimg"] = "分享小图标";
        $field["activity_sharedesc"] = "分享描述";

        $result = array();
        if($dataList){
            $result["field"] = $field;
            $result["data"] = $dataList;
            if($result["data"]){
                $res = array('error' => '0', 'errortip' => '某个活动分享的详细情况', 'result' => $result);
            }else{
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '暂无某个活动分享的详细情况', 'result' => $result);
            }
        }else{
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '暂无某个活动的详细情况', 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }
    //招生目标 -- >> 复制活动的详细情况
    function copyActivityOneApi(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        if(!isset($request['activity_id']) || $request['activity_id'] == ''){
            $res = array('error' => '1', 'errortip' => '对应活动必选', 'result' => array());
            ajax_return($res);
        }
        //model
        $Model = new \Model\Crm\SellgoalModel($request);
        $dataList = $Model->copyActivityOneApi($request);

        $field = array();
        $field["activity_id"] = "活动id";
        $field["company_id"] = "公司";
        $field["staffer_id"] = "校务职工id";
        $field["marketer_id"] = "crm职工id";
        $field['activity_type'] = "活动来源";
        $field['activity_pattern'] = "招生活动模式";
        $field['activity_name'] = "名称";
        $field['activity_theme'] = "活动招生标题";
        $field['activity_starttime'] = "开始时间";
        $field['activity_endtime'] = "结束时间";
        $field['activity_istemp'] = "是否使用招生模板";
        $field['activitytemp_id'] = "招生模板id";
        $field['activity_tempurl'] = "自定义招生模板链接";
        $field['activity_contacttel'] = "联系方式";
        $field['activity_address'] = "活动地址";
        $field['activity_img'] = "主图";
        $field['activity_content'] = "活动详情";
        $field['activity_rule'] = "活动规则";
        $field['activity_aboutus'] = "关于我们";
        $field['activity_customcontent'] = "自定义详情";
        $field['activity_sharedesc'] = "课叮铛分享描述";
        $field['activity_shareimg'] = "微信分享小图标";
        $field['frommedia_name'] = "来源渠道信息";
        $field['channel_id'] = "渠道明细ID";
        $field['activity_createtime'] = "创建时间";
        $field["activity_codetype"] = "二维码类型  0 默认(地推码)  1 企微";

        $result = array();
        if($dataList){
            $result["field"] = $field;
            $result["data"] = $dataList;
            if($result["data"]){
                $res = array('error' => '0', 'errortip' => '复制活动成功', 'result' => $result);
				$this->addCrmWorkLog($request['company_id'],$request['school_id'],$request['marketer_id'],"招生活动管理->招生活动管理",'复制招生活动',dataEncode($request));
            }else{
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '复制活动失败', 'result' => $result);
            }
        }else{
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '复制活动失败', 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }

    //招生目标 -- >> 某个活动的详细情况
    function goalActivityOneApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        if(!isset($request['activity_id']) || $request['activity_id'] == ''){
            $res = array('error' => '1', 'errortip' => '对应活动必选', 'result' => array());
            ajax_return($res);
        }
        //model
        $Model = new \Model\Crm\SellgoalModel($request);
        $dataList = $Model->goalActivityOneApi($request);

//        //字段JSON获取
//        $fucMarkString = "/{$this->u}/{$this->api}Api";
//        $FunctionModel = new \Model\Imc\FunctionModel($request);
//        $field = $FunctionModel->getFunctionField($fucMarkString);
        $field = array();
        $field["activity_id"] = "目标id";
        $field["activity_name"] = "活动名称";
        $field["marketer_id"] = "职工ID";
        $field["marketer_name"] = "职工姓名";
        $field["activity_starttime"] = "开始时间";
        $field["activity_endtime"] = "结束时间";
        $field["activity_istemp"] = "是否使用招生模板:0不使用1使用";
        $field["activitytemp_id"] = "自定义招生模板URL前缀";
        $field["activity_tempurl"] = "招生活动模板ID";
        $field["activity_img"] = "主图";
        $field["activity_rule"] = "活动规则";
        $field["activity_content"] = "活动详情";
        $field["activity_aboutus"] = "关于我们";
        $field["activity_contacttel"] = "联系方式";
        $field["activity_address"] = "活动地址";
        $field["activity_createtime"] = "创建时间";
        $field["activity_updatetime"] = "修改时间";

        $field["activity_type"] = "活动来源";
        $field["activity_SchoolUrl"] = "学校链接地址";
        $field["activity_PersonalUrl"] = "个人链接地址";
        $field["activity_appSchoolUrl"] = "学校二维码";
        $field["activity_appPersonalSchoolUrl"] = "个人二维码";
        $field["activitytemp_name"] = "招生模板名称";
        $field["activitytemp_styleimg"] = "招生模板图片查看";
        $field["activitytemp_bannerimg"] = "招生模板banner示意查看";
        $field["activity_sharedesc"] = "课叮铛分享描述";
        $field["activity_shareimg"] = "微信分享小图标";
        $field["frommedia_name"] = "来源渠道信息";

        $result = array();
        if($dataList){
            $result["field"] = $field;
            $result["data"] = $dataList;
            if($result["data"]){
                $res = array('error' => '0', 'errortip' => '某个活动的详细情况', 'result' => $result);
            }else{
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '暂无某个活动的详细情况', 'result' => $result);
            }
        }else{
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '暂无某个活动的详细情况', 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }
    function goalActivityshowimgView(){
        header('Content-Type:image/png');
        $request = Input('get.','','trim,addslashes');
        $codeUrl = base64_decode($request['imgurl']);//."&school_id={$request['school_id']}&typ=0"
        require(ROOT_PATH . 'Core/Classlib/Webqrcode.php');//二维码生成
        $QRcode = new \QRcode();
        $errorCorrectionLevel = 'H';//容错级别
        $matrixPointSize = 15;//生成图片大小
        echo $QRcode->png($codeUrl, false, $errorCorrectionLevel, $matrixPointSize, 2);
    }
    // 获取渠道类型
    function getSourceApi()
    {
        $request = Input('get.','','trim,addslashes');
           $company_id = $request['company_id'];
        if (!$company_id) {
            $res = array('error' => '1', 'errortip' => "公司id有误", 'result' => array());
            ajax_return($res);
        }
        $Model = new \Model\Crm\PublicModel($request);
        $dataList = $Model->getSourceFromMedia($company_id);
        $field['frommedia_id'] = "序号id";
        $field['commode_name'] = "来源名称";
        $result['field'] = $field;
        if ($dataList) {
            $result['list'] = $dataList;
        } else {
            $result['list'] = array();
        }
        $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        ajax_return($res,$request['language_type']);
    }
    // 获取渠道类型 -- 吉的堡去除转介绍
    function getSourceTwoApi()
    {
        $request = Input('get.','','trim,addslashes');
           $company_id = $request['company_id'];
        if (!$company_id) {
            $res = array('error' => '1', 'errortip' => "公司id有误", 'result' => array());
            ajax_return($res);
        }
        $Model = new \Model\Crm\PublicModel($request);
        $dataList = $Model->getSourceFromMediaTwo($company_id);
        $field['frommedia_id'] = "序号id";
        $field['commode_name'] = "来源名称";
        $result['field'] = $field;
        if ($dataList) {
            $result['list'] = $dataList;
        } else {
            $result['list'] = array();
        }
        $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        ajax_return($res,$request['language_type']);
    }
    //获取渠道明细
    function getChannelApi()
    {
        $request = Input('get.','','trim,addslashes');
           $company_id = $request['company_id'];
        if (!$company_id) {
            $res = array('error' => '1', 'errortip' => "公司id有误", 'result' => array());
            ajax_return($res);
        }
        $Model = new \Model\Crm\PublicModel($request);
        $dataList = $Model->getClientChannel($request);
        $field['channel_id'] = "渠道id";
        $field['channel_name'] = "渠道名称";
        if ($dataList) {
            $result['list'] = $dataList;
        } else {
            $result['list'] = array();
        }
        $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        ajax_return($res,$request['language_type']);
    }
    //获取渠道明细 -- 吉的堡去除转介绍
    function getChannelTwoApi()
    {
        $request = Input('get.','','trim,addslashes');
           $company_id = $request['company_id'];
        if (!$company_id) {
            $res = array('error' => '1', 'errortip' => "公司id有误", 'result' => array());
            ajax_return($res);
        }
        $Model = new \Model\Crm\PublicModel($request);
        $dataList = $Model->getClientChannelTwo($request);
        $field['channel_id'] = "渠道id";
        $field['channel_name'] = "渠道名称";
        if ($dataList) {
            $result['list'] = $dataList;
        } else {
            $result['list'] = array();
        }
        $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        ajax_return($res,$request['language_type']);
    }
    //招生目标 -- >> 添加活动
    function addGoalActivityAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        if(!isset($request['activity_name']) || $request['activity_name'] == ''){
            $res = array('error' => '1', 'errortip' => '活动名称不能为空', 'result' => array());
            ajax_return($res);
        }
        if(!isset($request['activity_pattern']) || $request['activity_pattern'] == ''){
            $res = array('error' => '1', 'errortip' => '活动类型不能为空', 'result' => array());
            ajax_return($res);
        }
        if($this->DataControl->selectOne("select activity_id from crm_sell_activity where company_id = '{$request['company_id']}' and activity_name = '{$request['activity_name']}' ")){
            $res = array('error' => '1', 'errortip' => '活动名称已存在', 'result' => array());
            ajax_return($res);
        }
        if($request['activity_pattern'] == '2'){
            if(!isset($request['activity_demoimg']) || $request['activity_demoimg'] == ''){
                $res = array('error' => '1', 'errortip' => '海报活动海报封面图不能为空', 'result' => array());
                ajax_return($res);
            }
            if(!isset($request['activity_img']) || $request['activity_img'] == ''){
                $res = array('error' => '1', 'errortip' => '海报活动海报主图不能为空（png图）', 'result' => array());
                ajax_return($res);
            }
        }

        //非专业版本 只可以创建三个进行中的活动
        $company_ismajor = $this->DataControl->getFieldOne("gmc_company","company_ismajor","company_id = '{$request['company_id']}'");
        if($company_ismajor['company_ismajor'] < '2') {
            $todaytime = date("Y-m-d");
            $actnum = $this->DataControl->selectOne("select count(activity_id) as num from crm_sell_activity where company_id = '{$request['company_id']}'  and FROM_UNIXTIME(unix_timestamp(activity_starttime),'%Y-%m-%d' ) <='{$todaytime}' and  FROM_UNIXTIME(unix_timestamp(activity_endtime),'%Y-%m-%d') >='{$todaytime}' AND activity_pattern = '{$request['activity_pattern']}' ");
            if ($actnum['num'] > 2) {
                $res = array('error' => '1', 'errortip' => '已超过拥有进行中的活动数，不可以再添加', 'result' => array());
                ajax_return($res);
            }
        }

        //model
        $Model = new \Model\Crm\SellgoalModel($request);
        $dataList = $Model->addGoalActivityAction($request);

        if($dataList){
            $res = array('error' => '0', 'errortip' => "活动新增成功",'result' => $dataList);
			$this->addCrmWorkLog($request['company_id'],$request['school_id'],$request['marketer_id'],"招生活动管理->招生活动管理",'新增招生活动',dataEncode($request));
        }else{
            $res = array('error' => '1', 'errortip' => '活动新增失败','result' => array());
        }
        ajax_return($res,$request['language_type']);
    }
    //招生目标 -- 修改招生活动
    function updateGoalActivityAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        if(!isset($request['activity_id']) || $request['activity_id'] == ''){
            $res = array('error' => '1', 'errortip' => '对应活动必选', 'result' => array());
            ajax_return($res);
        }
        if(!isset($request['activity_name']) || $request['activity_name'] == ''){
            $res = array('error' => '1', 'errortip' => '活动名称不能为空', 'result' => array());
            ajax_return($res);
        }

        $goalOne = false;
        $marketerOne = $this->DataControl->selectOne("select s.account_class,p.school_id,p.postbe_crmuserlevel from crm_marketer as m 
                left join smc_staffer as s ON m.staffer_id = s.staffer_id 
                LEFT join gmc_staffer_postbe as p ON s.staffer_id = p.staffer_id 
                where m.company_id = '{$request['company_id']}' and m.marketer_id = '{$request['marketer_id']}'  ");

        $actOne = $this->DataControl->selectOne("select activity_type,activity_id,activity_starttime,activity_endtime from crm_sell_activity WHERE activity_id = '{$request['activity_id']}' ");
        if($marketerOne['account_class'] == '1') {
            $goalOne = TRUE;
        }else{
            //是否集团活动
            if ($actOne['activity_type'] == '1') {
//                $goalOne = false;
                $goalOne = TRUE; //------------ ********允许所有人修改，地推人员
            }elseif($this->DataControl->getFieldOne("crm_sell_activity", "activity_id", "activity_id='{$request['activity_id']}' and marketer_id='{$request['marketer_id']}'")){
                $goalOne = TRUE;
            }
        }

        //非专业版本 只可以创建三个进行中的活动
        $company_ismajor = $this->DataControl->getFieldOne("gmc_company","company_ismajor","company_id = '{$request['company_id']}'");
        if($company_ismajor['company_ismajor'] < '1') {
            $todaytime = date("Y-m-d");
            $actnum = $this->DataControl->selectOne("select count(activity_id) as num,group_concat(activity_id) as actid from crm_sell_activity where company_id = '{$request['company_id']}'  and FROM_UNIXTIME(unix_timestamp(activity_starttime),'%Y-%m-%d' ) <='{$todaytime}' and  FROM_UNIXTIME(unix_timestamp(activity_endtime),'%Y-%m-%d') >='{$todaytime}' ");
            if ($actnum['num'] > 2 && strpos($actnum['actid'],$request['activity_id']) == false) {
                $res = array('error' => '1', 'errortip' => '进行中活动已满三个，非进行中活动不可编辑', 'result' => array());
                ajax_return($res);
            }
        }

        if ($goalOne) {
            //model
            $Model = new \Model\Crm\SellgoalModel($request);
            $dataList = $Model->updateGoalActivityAction($request,$marketerOne['account_class'],$actOne['activity_type']);

//            //字段JSON获取Action
//            $fucMarkString = "/{$this->u}/{$this->c}Action";
//            $FunctionModel = new \Model\Imc\FunctionModel($request);
//            $field = $FunctionModel->getFunctionField($fucMarkString);
            $field = array();
            $field['activity_name'] = "活动标题";
            $field['activity_theme'] = "活动招生标题";
            $field["activity_starttime"] = "开始时间";
            $field["activity_endtime"] = "结束时间";
            $field["activity_istemp"] = "是否使用招生模板:0不使用1使用";
            $field["activitytemp_id"] = "自定义招生模板URL前缀";
            $field["activity_tempurl"] = "招生活动模板ID";
            $field["activity_img"] = "主图";
            $field["activity_rule"] = "活动规则";
            $field["activity_content"] = "活动详情";
            $field["activity_aboutus"] = "关于我们";
            $field["activity_contacttel"] = "联系方式";
            $field["activity_address"] = "活动地址";
            $field['activity_updatetime'] = "修改时间";
            $field["activity_sharedesc"] = "课叮铛分享描述";
            $field['activity_shareimg'] = "微信分享小图标";
            $field["frommedia_name"] = "来源渠道信息";
            $field["activity_codetype"] = "二维码类型  0 默认(地推码)  1 企微";

            $field["activity_issex"] = "是否需要选择性别";
            $field["activity_issex_must"] = "是否必填";
            $field["activity_isbirthday"] = "是否需要选择生日";
            $field["activity_isbirthday_must"] = "是否必填";
            $field["activity_isaddress"] = "是否需要选择地址";
            $field["activity_isaddress_must"] = "是否必填";

            if ($dataList) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $dataList;
                $res = array('error' => '0', 'errortip' => "活动修改成功", 'result' => $result);
				$this->addCrmWorkLog($request['company_id'],$request['school_id'],$request['marketer_id'],"招生活动管理->招生活动管理",'修改招生活动',dataEncode($request));
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '活动修改失败', 'result' => $result);
            }
        }else{
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '您没有权限修改', 'result' => $result);
        }

        ajax_return($res,$request['language_type']);
    }

    //招生目标 -- 删除某个招生活动
    function delGoalActivityAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        if (!$request['activity_id']) {
            $res = array('error' => '1', 'errortip' => "招生活动id有误", 'result' => array());
            ajax_return($res);
        }
        if ($this->DataControl->getFieldOne("crm_client", "client_id", "activity_id='{$request['activity_id']}' ")) {
            $res = array('error' => '1', 'errortip' => "该活动已产生有效名单，不予删除", 'result' => array());
            ajax_return($res);
        }
        $marketerOne = $this->DataControl->selectOne("select s.account_class,p.school_id,p.postbe_crmuserlevel from crm_marketer as m 
                left join smc_staffer as s ON m.staffer_id = s.staffer_id 
                LEFT join gmc_staffer_postbe as p ON s.staffer_id = p.staffer_id 
                where m.company_id = '{$request['company_id']}' and m.marketer_id = '{$request['marketer_id']}'  ");
        if($marketerOne['account_class'] != '1') {
            //是否集团活动
            $actOne = $this->DataControl->selectOne("select activity_type from crm_sell_activity WHERE activity_id = '{$request['activity_id']}' ");
            if ($actOne['activity_type'] == '1') {
                $res = array('error' => '1', 'errortip' => "您没有权限删除", 'result' => array());
                ajax_return($res);
            }
            //判断是不是自己发布的活动
            if (!$this->DataControl->getFieldOne("crm_sell_activity", "activity_id", "activity_id='{$request['activity_id']}' and marketer_id='{$request['marketer_id']}'")) {
                $res = array('error' => '1', 'errortip' => "您不是创建人，没有权限删除", 'result' => array());
                ajax_return($res);
            }
        }

        $Model = new \Model\Crm\SellgoalModel($request);
        $dataList = $Model->delGoalActivityAction($request['activity_id'], $request['marketer_id'],$marketerOne['account_class'],$request['company_id']);
        if ($dataList) {
            $res = array('error' => '0', 'errortip' => "删除成功", 'result' => array());
			$this->addCrmWorkLog($request['company_id'],$request['school_id'],$request['marketer_id'],"招生活动管理->招生活动管理",'删除招生活动',dataEncode($request));
        } else {
            $res = array('error' => '1', 'errortip' => "删除失败", 'result' => array());
        }
        ajax_return($res,$request['language_type']);
    }



    //招生活动 -- >> 地推人员管理
    function getGroundPromotionView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Crm\SellgoalModel($request);
        $dataList = $Model->getGroundPromotion($request);

        $field = array();
        $key = 0;
        $field[$key]["fieldname"] = "promotion_id";
        $field[$key]["fieldstring"] = "地推人员ID";
        $field[$key]["show"] = 0;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "promotion_name";
        $field[$key]["fieldstring"] = "姓名";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $field[$key]["visible"] = 1;
        $key++;

        $field[$key]["fieldname"] = "promotion_jobnumber";
        $field[$key]["fieldstring"] = "地推工号";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $field[$key]["visible"] = 1;
        $key++;

        $field[$key]["fieldname"] = "promotion_type_name";
        $field[$key]["fieldstring"] = "地推类型";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $field[$key]["visible"] = 1;
        $key++;

        $field[$key]["fieldname"] = "promotion_mobile";
        $field[$key]["fieldstring"] = "联系电话";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $field[$key]["visible"] = 1;
        $key++;

        $field[$key]["fieldname"] = "school_allname";
        $field[$key]["fieldstring"] = "负责校区";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $field[$key]["visible"] = 1;
        $key++;

        $field[$key]["fieldname"] = "promotion_bankcard";
        $field[$key]["fieldstring"] = "银行卡号";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "promotion_bank";
        $field[$key]["fieldstring"] = "开户行";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "open_status";
        $field[$key]["fieldstring"] = "是否使用";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "client_allnum";
        $field[$key]["fieldstring"] = "毛名单数";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "client_allvalidnum";
        $field[$key]["fieldstring"] = "有效名单数";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "client_allvalid_rate";
        $field[$key]["fieldstring"] = "名单有效率";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;
        $key++;

        $field[$key]["fieldname"] = "positivenum";
        $field[$key]["fieldstring"] = "缴费名单数";
        $field[$key]["show"] = 1;
        $field[$key]["custom"] = 0;

        $result = array();
        $result["field"] = $field;
        if($dataList){
            $result["list"] = $dataList['datalist'];
            $res = array('error' => '0', 'errortip' => '地推人员管理', 'allnum' => $dataList['count'], 'result' => $result);
        }else{
            $result["list"] = array();
            $res = array('error' => '1', 'errortip' => '暂无地推人员信息', 'allnum' => 0, 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }

    //招生活动 -- >> 添加地推人员
    function addGroundPromotionAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Crm\SellgoalModel($request);
        $dataList = $Model->addGroundPromotionAction($request);

        if($dataList){
            $res = array('error' => '0', 'errortip' => "添加地推人员成功");
            $this->addCrmWorkLog($request['company_id'],$request['school_id'],$request['marketer_id'],"招生活动管理->地推人员管理",'新增地推人员',dataEncode($request));
        }else{
            $res = array('error' => '1', 'errortip' => '您添加的人员与集团/他校地推人员重复');
        }
        ajax_return($res,$request['language_type']);
    }

    //招生活动 -- >> 编辑地推人员
    function editGroundPromotionAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Crm\SellgoalModel($request);
        $editPromotion = $Model->editGroundPromotionAction($request);

        if ($editPromotion) {
            $res = array('error' => '0', 'errortip' => "编辑地推人员成功", 'result' => array());
            $this->addCrmWorkLog($request['company_id'],$request['school_id'],$request['marketer_id'],"招生活动管理->地推人员管理",'编辑地推人员',dataEncode($editPromotion));
        } else {
            $res = array('error' => '1', 'errortip' => $Model->errortip, 'result' => array());
        }
        ajax_return($res,$request['language_type']);
    }

    //招生活动 -- >> 活动移除地推人员
    function delGroundPromotionAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        if ($this->DataControl->delData("crm_sell_activity_promotion", "activity_id = '{$request['activity_id']}' and promotion_id = '{$request['promotion_id']}'")) {
            $res = array('error' => '0', 'errortip' => "删除成功", 'result' => array());
            $this->addCrmWorkLog($request['company_id'],$request['school_id'],$request['marketer_id'],"招生活动管理->招生活动管理",'移除地推人员',dataEncode($request));
        } else {
            $res = array('error' => '1', 'errortip' => "删除失败", 'result' => array());
        }
        ajax_return($res,$request['language_type']);
    }

    //招生活动 -- >> 启用/禁用地推人员
    function openGroundPromotionAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        if($request['open_status']){
            $tip = "启用";
        }else{
            $tip = "禁用";
        }
        if(!$this->DataControl->selectOne("select * from crm_ground_promotion_open where company_id = '{$request['company_id']}' and school_id = '{$request['school_id']}' and promotion_id = '{$request['promotion_id']}' limit 0,1")){
            $list = array();
            $list['company_id'] = $request['company_id'];
            $list['school_id'] = $request['school_id'];
            $list['promotion_id'] = $request['promotion_id'];
            $this->DataControl->insertData('crm_ground_promotion_open', $list);
        }

        $where = "company_id = '{$request['company_id']}' and school_id = '{$request['school_id']}' and promotion_id = '{$request['promotion_id']}'";
        if ($this->DataControl->updateData("crm_ground_promotion_open", $where, array("open_status" => $request['open_status']))) {
            //修改主表的更新时间
            $this->DataControl->updateData("crm_ground_promotion", "company_id = '{$request['company_id']}' and promotion_id = '{$request['promotion_id']}'", array("promotion_updatetime" => time() ));
            $res = array('error' => '0', 'errortip' => "修改成功", 'result' => array());
            $this->addCrmWorkLog($request['company_id'],$request['school_id'],$request['marketer_id'],"招生活动管理->招生活动管理","{$tip}地推人员",dataEncode($request));
        } else {
            $res = array('error' => '1', 'errortip' => "修改失败", 'result' => array());
        }

        ajax_return($res,$request['language_type']);
    }


    //招生管理 -- >> 地推二维码 -- 企业微信二维码参数解析
    function getPromotionQRcodeParamApi()
    {
        $request = Input('get.','','trim,addslashes');
//        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Crm\SellgoalModel($request);
        $dataList = $Model->getPromotionQRcodeParamApi($request);
        if($dataList){
            $res = array('error' => '0', 'errortip' => '解析成功','result' => $dataList);
        }else{
            $res = array('error' => '1', 'errortip' => '解析失败','result' => $dataList);
        }
        ajax_return($res,$request['language_type']);
    }
    //招生活动 -- >> 地推二维码
    function GroundPromotionQRcodeApi()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Crm\SellgoalModel($request);
        $dataList = $Model->GroundPromotionQRcode($request);

        $result = array();
        if($dataList){
            $result["list"] = $dataList;
            $res = array('error' => '0', 'errortip' => '地推二维码', 'result' => $result);
        }else{
            $result["list"] = array();
            $res = array('error' => '1', 'errortip' => '暂无地推二维码', 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }

    //查询地推人员是否重复
    function getPromotionView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        if (isset($request['promotion_mobile']) && $request['promotion_mobile'] !== '') {
            $promotionOne = $this->DataControl->getFieldOne("crm_ground_promotion", "promotion_name", "company_id = '{$request['company_id']}' and school_id = '{$request['school_id']}' and promotion_mobile = '{$request['promotion_mobile']}'");
            $promotionTwo = $this->DataControl->getFieldOne("crm_ground_promotion", "promotion_name", "company_id = '{$request['company_id']}' and promotion_mobile = '{$request['promotion_mobile']}'", "ORDER BY promotion_mobile DESC,promotion_id DESC");
        }

        if (isset($request['promotion_jobnumber']) && $request['promotion_jobnumber'] !== '') {
            $promotionOne = $this->DataControl->getFieldOne("crm_ground_promotion", "promotion_name", "company_id = '{$request['company_id']}' and school_id = '{$request['school_id']}' and promotion_jobnumber = '{$request['promotion_jobnumber']}'");
            $promotionTwo = $this->DataControl->getFieldOne("crm_ground_promotion", "promotion_name", "company_id = '{$request['company_id']}' and promotion_jobnumber = '{$request['promotion_jobnumber']}'", "ORDER BY promotion_jobnumber DESC,promotion_id DESC");
        }

        if ($promotionOne) {
            $res = array('error' => '1', 'errortip' => "本校人员重复", 'result' => array());
        } elseif ($promotionTwo) {
            $res = array('error' => '2', 'errortip' => "他校人员重复", 'result' => array("promotion_name" => $promotionTwo['promotion_name']));
        } else {
            $res = array('error' => '0', 'errortip' => "暂无重复数据", 'result' => array());
        }
        ajax_return($res,$request['language_type']);
    }


    //结尾魔术函数
    function __destruct()
    {

    }
}
