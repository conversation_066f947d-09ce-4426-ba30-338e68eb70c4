<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/10/13
 * Time: 16:13
 */

namespace Work\Controller\Crmapi;


class routerController extends viewTpl
{
    public $u;
    public $t;
    public $c;

    //预加载处理类
    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
        $this->api = $this->router->getApi();
    }

    //点评顾客信息获取
    function customerView(){
        $request = Input('post.', '', 'trim,addslashes');
        $DpingModel = new \Model\Api\DpingModel();
        $result = $DpingModel->customer($request['open_shop_uuid'],$request['message']);
        if ($result) {
            $res = array('msg' => 'success','code'=>'200', "data" => "");
            ajax_return($res, $request['language_type']);
        } else {
            $res = array('msg' => 'error','code'=>'5001', "data" => "");
            ajax_return($res, $request['language_type']);
        }
    }


    //点评顾客信息获取
    function customercenterView(){
        $request = Input('post.', '', 'trim,addslashes');
        $DpingModel = new \Model\Api\DpingModel();
        $result = $DpingModel->customercenter($request['open_shop_uuid'],$request['message']);
        if ($result) {
            $res = array('msg' => 'success','code'=>'200', "data" => "");
            ajax_return($res, $request['language_type']);
        } else {
            $res = array('msg' => 'error','code'=>'5001', "data" => "");
            ajax_return($res, $request['language_type']);
        }
    }
}