<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/13
 * Time: 23:25
 */

namespace Work\Controller\Crmapi;


use Model\Crm\IntentionClientModel;

class IntentionclientController extends viewTpl
{
    public $u;
    public $t;
    public $c;
    public $visitType = "api";
    public $DataControl;
    public $company_isassist;

    //预加载处理类
    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();

        //数据库操作
        $this->DataControl = new \Dbmysql();

    }

    //本地权限校验入口
    function ThisVerify($request)
    {
        if (!intval($request['marketer_id'])) {
            $res = array('error' => '1', 'errortip' => "未检测到招生人员信息，请重新登录操作", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        if (isset($request['crm_token']) && $request['crm_token'] !== '') {
            $request['token'] = $request['crm_token'];
        }
        if (empty($request['token'])) {
            $res = array('error' => '1', 'errortip' => "未传入用户Token信息", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        if (isset($request['company_id']) && $request['company_id'] !== '') {
            $companyOne = $this->DataControl->getFieldOne("gmc_company", "company_isassist", "company_id='{$request['company_id']}'");
            $this->company_isassist = $companyOne['company_isassist'];
        }
        $paramArray = array();
        $paramArray['marketer_id'] = $request['marketer_id'];
        $paramArray['token'] = $request['token'];
        if (!$this->UserLimit($paramArray)) {
            $result = array();
            $result["list"] = array();
            $result["tokeninc"] = "0";
            $res = array('error' => '1', 'errortip' => "用户token失效", 'result' => $result);
            ajax_return($res, $request['language_type']);
        }
    }

    //意向客户管理 -首页
    function HomeView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Crm\IntentionClientModel($request);
        $clientList = $Model->getIntentionClientList($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "姓名";
        $field[$k]["fieldname"] = "client_cnname";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '0';
        $field[$k]["is_method"] = 1;
        $field[$k]["isShowRule"] = 1;//前端为了显示保护法的图标
        $field[$k]["isReadStatus"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "英文名";
        $field[$k]["fieldname"] = "client_enname";
        $field[$k]["show"] = '0';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "标签";
        $field[$k]["fieldname"] = "client_tag";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $field[$k]["istag"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "性别";
        $field[$k]["fieldname"] = "client_sex";
        $field[$k]["show"] = '0';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "年龄";
        $field[$k]["fieldname"] = "client_age";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "主要联系人";
        $field[$k]["fieldname"] = "family_cnname";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "主要联系电话";
        $field[$k]["fieldname"] = "client_mobile";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $field[$k]["isTypeTab"] = 1;
        $k++;
//        if($request['language_type'] == 'tw'){
            $field[$k]["fieldstring"] = "Email";
            $field[$k]["fieldname"] = "client_email";
            $field[$k]["show"] = '1';
            $field[$k]["custom"] = '1';
            $k++;
//        }
        $field[$k]["fieldstring"] = "意向星级";
        $field[$k]["fieldname"] = "client_intention_level";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $field[$k]["islevel"] = 'true';
        $k++;
        $field[$k]["fieldstring"] = "客户状态";
        $field[$k]["fieldname"] = "client_status";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
//        $field[$k]["fieldstring"] = "跟进次数";
//        $field[$k]["fieldname"] = "track_count";
//        $field[$k]["show"] = '0';
//        $field[$k]["custom"] = '1';
//        $k++;
        $field[$k]["fieldstring"] = "接通状态";
        $field[$k]["fieldname"] = "client_answerphone";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "主要负责人";
        $field[$k]["fieldname"] = "marketer_name";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        if ($this->company_isassist == 1) {
            $k++;
            $field[$k]["fieldstring"] = "协助负责人";
            $field[$k]["fieldname"] = "fu_marketer_name";
            $field[$k]["show"] = '1';
            $field[$k]["custom"] = '1';
        }
        $k++;
        $field[$k]["fieldstring"] = "最后跟踪内容";
        $field[$k]["fieldname"] = "track_note";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "渠道类型";
        $field[$k]["fieldname"] = "client_source";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "渠道明细";
        $field[$k]["fieldname"] = "channel_name";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "接触点";
        $field[$k]["fieldname"] = "client_frompage";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "地推工号";
        $field[$k]["fieldname"] = "promotion_jobnumber";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "联系地址";
        $field[$k]["fieldname"] = "client_address";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "活动";
        $field[$k]["fieldname"] = "activity_name";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "省";
        $field[$k]["fieldname"] = "province_name";
        $field[$k]["show"] = '0';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "市";
        $field[$k]["fieldname"] = "city_name";
        $field[$k]["show"] = '0';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "区";
        $field[$k]["fieldname"] = "area_name";
        $field[$k]["show"] = '0';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "推荐人";
        $field[$k]["fieldname"] = "client_sponsor";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "推荐教师";
        $field[$k]["fieldname"] = "client_teachername";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "意向课程";
        $field[$k]["fieldname"] = "course_cnname";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $field[$k]["istag"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "创建时间";
        $field[$k]["fieldname"] = "client_createtime";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "最后跟踪时间";
        $field[$k]["fieldname"] = "track_createtime";
        $field[$k]["show"] = '0';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "附近学校";
        $field[$k]["fieldname"] = "nearschool_name";
        $field[$k]["show"] = '0';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "备注";
        $field[$k]["fieldname"] = "client_remark";
        $field[$k]["show"] = '0';
        $field[$k]["custom"] = '1';


        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($clientList['list']) {

            $result['notreadnum'] = $clientList['notreadnum'];//集团跟进后，学校这边没有查看的数据统计
            $result['list'] = $clientList['list'];
        } else {

            $result['list'] = array();
        }
        if (isset($clientList['allnums']) && $clientList['list'] != "") {
            $allnum = intval($clientList['allnums']);
        } else {
            $allnum = 0;
        }

//        if (isset($request['postbe_crmuserlevel']) && $request['postbe_crmuserlevel'] == 1) {
//
//            $datawhere = " 1  and c.client_distributionstatus = 1 and s.company_id ='{$request['company_id']}' and s.school_id='{$request['school_id']}' and p.principal_leave=0 and  c.client_ischaserlapsed =0 and c.client_tracestatus <> 4";
//
//        } else {
//            $datawhere = " 1 and c.client_distributionstatus = 1  and s.company_id ='{$request['company_id']}' and s.school_id='{$request['school_id']}' and mk.marketer_id='{$request['marketer_id']}'  and p.principal_leave=0  and  c.client_ischaserlapsed =0 and c.client_tracestatus <> 4 ";
//        }
//
//
//        $num = $this->DataControl->selectClear("
// 				select count(c.client_id) as client_num
//				from  crm_client as c
//				Left JOIN  crm_client_principal as  p ON p.client_id = c.client_id
//				Left JOIN  crm_marketer as mk ON mk.marketer_id = p.marketer_id
//				Left JOIN  crm_client_schoolenter as s  ON s.client_id=c.client_id AND s.school_id = p.school_id
//				where {$datawhere} and c.client_tracestatus <> '-1' and c.client_tracestatus <> '-2' and c.client_isnewtip = '1' Group by c.client_id
//				");
//        //and c.client_tracestatus <> '-1' and c.client_tracestatus <> '-2'  为后期补充的 20220310
//        if ($num) {
//            $result['notreadnum'] = count($num);
//        } else {
//            $result['notreadnum'] = '0';
//        }
//        $result['notreadnum'] =111;
        if ($result['list']) {
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result, "allnum" => $allnum);
        } else {
            $res = array('error' => '1', 'errortip' => "暂无意向客户信息", 'result' => $result, "allnum" => $allnum);
        }

        ajax_return($res, $request['language_type']);
    }
    //获取意向客户 分类的统计数字
    function getIntentionNumView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        //model
        $Model = new \Model\Crm\IntentionClientModel($request);
        $Model->getIntentionNum($request);
        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $Model->result), $request['language_type']);
    }
    //手机端 拨打电话操作
    function callClientMobileAction(){
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Crm\IntentionClientModel($request);
        $Model->callClientMobileAction($request);
        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $Model->result), $request['language_type']);
    }

    //慧捷 -- 根据 callid -- 查询录音记录
    function getHuijieCallidRecordingView(){
        $request = Input('get.', '', 'trim,addslashes');
        echo "没有传主叫和被叫不使用";
        die;
//        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Crm\PhoneOutboundModel($request);
        $result = $Model->getHuijieCallidRecording($request);
        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $Model->result), $request['language_type']);
    }
    //慧捷 -- 根据 callid -- 查询录音调听资源
    function getHuijieCallidRecordingFileView(){
        $request = Input('get.', '', 'trim,addslashes');
//        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Crm\PhoneOutboundModel($request);
        $result = $Model->getHuijieCallidRecordingFile($request['callid']);
        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $Model->result), $request['language_type']);
    }
    //慧捷 -- 根据 callid -- 查询录音调听资源 ----- 测试返回值
    function getHuijieCallidRecordingFileTestView(){
        $request = Input('get.', '', 'trim,addslashes');
//        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Crm\PhoneOutboundModel($request);
        $result = $Model->getHuijieCallidRecordingFileTest($request['callid']);
        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $Model->result), $request['language_type']);
    }
    //合力 -- 查询录音记录  ---  没有查出来数据 暂时没有使用
    function getHeliCallRecordingView(){
        $request = Input('get.', '', 'trim,addslashes');
//        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Crm\PhoneOutboundModel($request);
        $result = $Model->getHeliCallRecording($request);
        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $Model->result), $request['language_type']);
    }

    //获取tmk跟踪名单
    function getOneTmkClientView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Crm\IntentionClientModel($request);
        $clientList = $Model->getOneTmkClient($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "姓名";
        $field[$k]["fieldname"] = "client_cnname";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '0';
        $field[$k]["is_method"] = 1;
        $field[$k]["isShowRule"] = 1;//前端为了显示保护法的图标
        $field[$k]["isReadStatus"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "标签";
        $field[$k]["fieldname"] = "client_tag";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "意向课程";
        $field[$k]["fieldname"] = "course_cnname";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $field[$k]["istag"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "性别";
        $field[$k]["fieldname"] = "client_sex";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "年龄";
        $field[$k]["fieldname"] = "client_age";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "主要联系人";
        $field[$k]["fieldname"] = "family_cnname";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "主要联系手机";
        $field[$k]["fieldname"] = "client_mobile";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
//        if($request['language_type'] == 'tw'){
        $field[$k]["fieldstring"] = "Email";
        $field[$k]["fieldname"] = "client_email";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
//        }
        $field[$k]["fieldstring"] = "意向星级";
        $field[$k]["fieldname"] = "client_intention_level";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $field[$k]["islevel"] = 'true';
        $k++;
        $field[$k]["fieldstring"] = "客户状态";
        $field[$k]["fieldname"] = "client_status";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "接通状态";
        $field[$k]["fieldname"] = "client_answerphone";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "筛选人";
        $field[$k]["fieldname"] = "marketer_tmkname";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "最后跟踪内容";
        $field[$k]["fieldname"] = "track_note";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "来源活动";
        $field[$k]["fieldname"] = "activity_name";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "渠道类型";
        $field[$k]["fieldname"] = "client_source";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "渠道明细";
        $field[$k]["fieldname"] = "channel_name";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "创建时间";
        $field[$k]["fieldname"] = "client_createtime";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;

        $resultarray = array();
        $resultarray['field'] = $field;
        $resultarray['all_num'] = $clientList['all_num']>0?$clientList['all_num']:'0';
        $resultarray['list'] = is_array($clientList['list'])?$clientList['list']:array();

        $result = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $resultarray);
        ajax_return($result,$request['language_type']);
    }
    //获取tmk跟踪名单 -- 记录
    function getOneTmkClientRecordView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Crm\IntentionClientModel($request);
        $clientList = $Model->getOneTmkClientRecord($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "姓名";
        $field[$k]["fieldname"] = "client_cnname";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '0';
        $field[$k]["is_method"] = 1;
        $field[$k]["isShowRule"] = 1;//前端为了显示保护法的图标
        $field[$k]["isReadStatus"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "标签";
        $field[$k]["fieldname"] = "client_tag";
        $field[$k]["show"] = '0';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "性别";
        $field[$k]["fieldname"] = "client_sex";
        $field[$k]["show"] = '0';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "年龄";
        $field[$k]["fieldname"] = "client_age";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "主要联系人";
        $field[$k]["fieldname"] = "family_cnname";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "主要联系手机";
        $field[$k]["fieldname"] = "client_mobile";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
//        if($request['language_type'] == 'tw'){
        $field[$k]["fieldstring"] = "Email";
        $field[$k]["fieldname"] = "client_email";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
//        }
        $field[$k]["fieldstring"] = "意向星级";
        $field[$k]["fieldname"] = "client_intention_level";
        $field[$k]["show"] = '0';
        $field[$k]["custom"] = '1';
        $field[$k]["islevel"] = 'true';
        $k++;
        $field[$k]["fieldstring"] = "客户状态";
        $field[$k]["fieldname"] = "client_status";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "接通状态";
        $field[$k]["fieldname"] = "client_answerphone";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "筛选人";
        $field[$k]["fieldname"] = "marketer_tmkname";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "最后跟踪内容";
        $field[$k]["fieldname"] = "track_note";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "来源活动";
        $field[$k]["fieldname"] = "activity_name";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "渠道类型";
        $field[$k]["fieldname"] = "client_source";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "渠道明细";
        $field[$k]["fieldname"] = "channel_name";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "创建时间";
        $field[$k]["fieldname"] = "client_createtime";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;

        $resultarray = array();
        $resultarray['field'] = $field;
        $resultarray['all_num'] = $clientList['all_num']>0?$clientList['all_num']:'0';
        $resultarray['list'] = is_array($clientList['list'])?$clientList['list']:array();

        $result = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $resultarray);
        ajax_return($result,$request['language_type']);
    }

    function getAuditionTypesApi(){
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ClassModel = new \Model\Crm\IntentionClientModel($request);
        $res = $ClassModel->getAuditionTypes($request);

        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $ClassModel->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //意向客户管理 -新增意向客户
    function addIntentionclientAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        if (empty($request['client_cnname'])) {
            $res = array('error' => '1', 'errortip' => '用户名为空', 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        if (empty($request['main_marketer_id'])) {
            $res = array('error' => '1', 'errortip' => '主负责人为空', 'result' => array());
            ajax_return($res, $request['language_type']);
        }

        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Crm\IntentionClientModel($request);
        $Model->addIntentionClient($request);

        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $Model->result?$Model->result:array());
        $this->addCrmWorkLog($request['company_id'], $request['school_id'], $request['marketer_id'], "意向客户管理->意向客户管理", '新增意向客户', dataEncode($request));

        ajax_return($res, $request['language_type']);
    }

    //意向客户管理 - 转为招生线索  -转为流失客户  type = 0  -1 无意向  -2 无效
    function changeIntentionClientApi()
    {
        $request = Input('post.', 'trim');
        $this->ThisVerify($request);//验证账户
        if (!$request['client_id']) {
            $res = array('error' => '1', 'errortip' => "客户id错误", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        if (!$request['marketer_id']) {
            $res = array('error' => '1', 'errortip' => "登陆人id错误", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        if (!isset($request['type']) || empty($request['type'])) {
            $res = array('error' => '1', 'errortip' => "流转类型为空", 'result' => array());
            ajax_return($res, $request['language_type']);
        }

        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Crm\IntentionClientModel($request);
        $bool = $Model->changeIntentionClientStatus($request);
        if ($request['type'] == 0) {
            $this->addCrmWorkLog($request['company_id'], $request['school_id'], $request['marketer_id'], "意向客户管理->意向客户管理", '批量转为招生有效名单', dataEncode($request));
        } else {
            $this->addCrmWorkLog($request['company_id'], $request['school_id'], $request['marketer_id'], "意向客户管理->意向客户管理", '批量转为无意向客户', dataEncode($request));
        }


        if ($bool == true) {
            $res = array('error' => '0', 'errortip' => $Model->errortip, 'result' => array());
        } else {
            $res = array('error' => '1', 'errortip' => $Model->errortip, 'result' => array());
        }
        ajax_return($res, $request['language_type']);
    }
    //高管分配单个 待分配名单 转 无效名单  -------- 20240923台湾需求补充
    function changeIntentionClientOneApi()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        if (!$request['client_id']) {
            $res = array('error' => '1', 'errortip' => "客户id错误", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        if (!$request['marketer_id']) {
            $res = array('error' => '1', 'errortip' => "登陆人id错误", 'result' => array());
            ajax_return($res, $request['language_type']);
        }

        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Crm\IntentionClientModel($request);
        $bool = $Model->changeIntentionClientOneStatus($request);

        $this->addCrmWorkLog($request['company_id'], $request['school_id'], $request['marketer_id'], "名单分配管理->待分配有效名单", '转为无效客户', dataEncode($request));
        if ($bool == true) {
            $res = array('error' => '0', 'errortip' => $Model->errortip, 'result' => array());
        } else {
            $res = array('error' => '1', 'errortip' => $Model->errortip, 'result' => array());
        }
        ajax_return($res, $request['language_type']);
    }

    // 意向客户管理 -检测重名
    function checkIntentionClientApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        if (empty($request['client_cnname'])) {
            $res = array('error' => '1', 'errortip' => "用户名为空", 'result' => array());
            ajax_return($res, $request['language_type']);

        }
        if (empty($request['client_mobile'])) {
            $res = array('error' => '1', 'errortip' => "手机号为空", 'result' => array());
            ajax_return($res, $request['language_type']);
        }

        $Model = new \Model\Crm\IntentionClientModel($request);
        $clientList = $Model->checkIntentionClient($request);

        if ($clientList) {
            $fieldname = array('client_id ', 'client_img', 'client_enname', 'client_cnname', 'client_sex',
                'client_age', 'school_cnname','client_source','channel_name','marketer_name',
                'client_tracestatus_name', 'client_mobile',  'client_status');
            $fieldstring = array('客户id', '头像', '英文名', '姓名', '性别', '年龄', '所属学校', '渠道类型', '渠道明细','主要负责人', '客户状态',  '主要联系手机', '客户状态ID');
            $fieldcustom = array('0', "1", '0', "1", "1",     "1", "1", "1", "1", "1",     "1", "1", "0");
            $fieldshow = array('0', "0", '0', "1", "1",      "1", "1", "1", "1", "1",     "1", "0", "0");

            $field = array();
            for ($i = 0; $i < count($fieldstring); $i++) {
                $field[$i]["fieldname"] = trim($fieldstring[$i]);
                $field[$i]["fieldstring"] = trim($fieldname[$i]);
                $field[$i]["custom"] = trim($fieldcustom[$i]);
                $field[$i]["show"] = trim($fieldshow[$i]);
            }

            $result['fieldcustom'] = 0;
            $result['field'] = $field;

            $result['list'] = $clientList;

            $res = array('error' => '1', 'errortip' => "拥有重复的数据,请确认", 'result' => $result);
        } else {
            $res = array('error' => '0', 'errortip' => "唯一的记录", 'result' => array());
        }

        ajax_return($res, $request['language_type']);
    }

    // 意向客户管理 - 跟进
    function trackIntentionClientApi()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        if (!isset($request['client_answerphone'])) {
            $res = array('error' => '1', 'errortip' => "请选择是否接通", 'result' => array());
            ajax_return($res, $request['language_type']);
        }

        if ($request['client_answerphone'] <> 0) {
            if (($request['track_followmode'] == 1) && $request['invite_visittime'] == "") {
                $res = array('error' => '1', 'errortip' => "请选择柜询时间", 'result' => array());
                ajax_return($res, $request['language_type']);
            }
    //		if($request['track_followmode'] == 2 && $request['audition_visittime']==""){
    //			$res = array('error' => '1', 'errortip' => "请选择试听时间", 'result' => array());
    //			ajax_return($res,$request['language_type']);
    //		}
    //		if($request['track_followmode'] == 2 && $request['class_id']==""){
    //			$res = array('error' => '1', 'errortip' => "请选择班级", 'result' => array());
    //			ajax_return($res,$request['language_type']);
    //		}
            if ($request['track_followuptype'] == 1 && $request['track_followuptime'] == "") {
                $res = array('error' => '1', 'errortip' => "请选择下次跟进时间", 'result' => array());
                ajax_return($res, $request['language_type']);
            }
        }

        $Model = new \Model\Crm\ClientModel($request);
        $bool = $Model->insertTrackClientOne($request);
        if ($bool == true) {
            $res = array('error' => '0', 'errortip' => "跟进成功", 'result' => array());
        } else {
            $res = array('error' => '1', 'errortip' => $Model->errortip, 'result' => array());
        }
        ajax_return($res, $request['language_type']);

    }

    //重复列表 我要跟踪
    function trackRepeatClientAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new IntentionClientModel();
        $Model->trackRepeatClient($request);
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);

    }

    //在Crm直接转正  CRM转正
    function conversionAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        if (!$request['client_id']) {
            $res = array('error' => '1', 'errortip' => "请选择学员", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        $this->ThisVerify($request);//验证账户
        $Model = new IntentionClientModel();
        $Model->Conversion($request);
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        $this->addCrmWorkLog($request['company_id'], $request['school_id'], $request['marketer_id'], "意向客户管理->意向客户管理", '转正', dataEncode($request));
        ajax_return($res, $request['language_type']);

    }

    //获取转正课程
    function conversionCourseApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new IntentionClientModel();
        $dataList = $Model->conversionCourseApi($request);
        $res = array('error' => 0, 'errortip' => "获取成功", 'result' => $dataList);
        ajax_return($res, $request['language_type']);
    }

    //获取排序字段
    function getOrderFieldApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $data = array();
        $data[0]['name'] = "智能排序";
        $data[0]['field'] = "1";

        $data[1]['name'] = "按名单最新创建时间";
        $data[1]['field'] = "client_createtime";

        $data[2]['name'] = "按名单最新更新时间";
        $data[2]['field'] = "client_updatetime";

        $res = array('error' => 0, 'errortip' => "获取成功", 'result' => $data);
        ajax_return($res, $request['language_type'], 1);

    }

    /**
     * 主管审阅点评记录
     * author: ling
     * 对应接口文档 0001
     */
    function levelReadTrackAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new IntentionClientModel();
        $Model->levelReadTrackOne($request);
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);

    }

    /**
     * 主管回复跟踪记录
     * author: ling
     * 对应接口文档 0001
     */
    function levelReplayTrackAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new IntentionClientModel();
        $Model->levelReplayTrack($request);
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }
}
