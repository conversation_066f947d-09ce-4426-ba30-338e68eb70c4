<?php

namespace Work\Controller\Crmapi;


class ConversationController extends viewTpl
{
    public $u;
    public $t;
    public $c;
    public $visitType = "api";
    public $DataControl;
    public $Model;

    //预加载处理类
    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();

        //数据库操作
        $this->DataControl = new \Dbmysql();
    }

    //验证
    function ThisVerify($request)
    {
        if(!intval($request['marketer_id'])){
            $res = array('error' => '1', 'errortip' => "未检测到招生人员信息，请重新登录操作", 'result' => array());
            ajax_return($res,$request['language_type']);
        }
        if(isset($request['crm_token']) && $request['crm_token'] !=='' ){
            $request['token'] = $request['crm_token'];
        }
        if(empty($request['token'])){
            $res = array('error' => '1', 'errortip' => "未传入用户Token信息", 'result' => array());
            ajax_return($res,$request['language_type']);
        }
        $paramArray = array();
        $paramArray['marketer_id'] = $request['marketer_id'];
        $paramArray['token'] = $request['token'];
        if (!$this->UserLimit($paramArray)) {
            $result = array();
            $result["list"] = array();
            $result["tokeninc"] = "0";
            $res = array('error' => '1', 'errortip' => "用户token失效", 'result' => $result);
            ajax_return($res,$request['language_type']);
        }
    }

    function CallRecordView(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model=  new \Model\Crm\ConversationModel($request);
        $result = $Model->CallRecordList($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldname"] = "calllog_id";
        $field[$k]["fieldstring"] = "通话记录ID";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "calllog_type";
        $field[$k]["fieldstring"] = "呼叫类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "calllog_outnumber";
        $field[$k]["fieldstring"] = "主叫号码";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "calllog_mobile";
        $field[$k]["fieldstring"] = "被叫号码";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "calllog_begintime";
        $field[$k]["fieldstring"] = "通话开始时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "calllog_endtime";
        $field[$k]["fieldstring"] = "通话结束时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;

        if($result){
            $res = array('error' => '0', 'errortip' => "获取记录成功", 'result' => array());
        }else{
            $res = array('error' => '1', 'errortip' => "获取记录失败", 'result' => array());
        }
        ajax_return($res,$request['language_type']);
    }

    // 外呼通话
    function ConversationAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model=  new \Model\Crm\ConversationModel($request);
        $result = $Model->CallAction($request);

        if($result['Succeed'] == true){
            $res = array('error' => '0', 'errortip' => "正在呼叫", 'result' => array());
        }elseif($result['code'] == '405'){
            $res = array('error' => '1', 'errortip' => "呼叫失败,同一号码每天最多呼叫5次！", 'result' => array());
        }else{
            $res = array('error' => '1', 'errortip' => "呼叫失败", 'result' => array());
        }
        ajax_return($res,$request['language_type']);
    }

    //接收推送
    function ReceivingPushView(){
        $request = Input('get.','','trim,addslashes');

        $Model=  new \Model\Crm\ConversationModel($request);
        $dataList = $Model->CallRecord($request);

        ajax_return($dataList);
    }
}