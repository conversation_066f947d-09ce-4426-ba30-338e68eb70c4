<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/13
 * Time: 23:25
 */

namespace Work\Controller\Crmapi;


class ClientController extends viewTpl
{
    public $u;
    public $t;
    public $c;
    public $visitType = "api";
    public $DataControl;
    public $company_isassist = 0;

    //预加载处理类
    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();

        //数据库操作
        $this->DataControl = new \Dbmysql();

    }


    //本地权限校验入口
    function ThisVerify($request)
    {

        if (!intval($request['marketer_id'])) {
            $res = array('error' => '1', 'errortip' => "未检测到招生人员信息，请重新登录操作", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        //叮当助教CRM
        if (isset($request['crm_token']) && $request['crm_token'] !== '') {
            $request['token'] = $request['crm_token'];
        }
        if (isset($request['company_id']) && $request['company_id'] !== '') {
            $companyOne = $this->DataControl->getFieldOne("gmc_company", "company_isassist", "company_id='{$request['company_id']}'");
            $this->company_isassist = $companyOne['company_isassist'];
        }
        if (empty($request['token'])) {
            $res = array('error' => '1', 'errortip' => "未传入用户Token信息", 'result' => array());
            ajax_return($res, $request['language_type']);
        }

        $paramArray = array();
        $paramArray['marketer_id'] = $request['marketer_id'];
        $paramArray['token'] = $request['token'];
        if (!$this->UserLimit($paramArray)) {
            $result = array();
            $result["list"] = array();
            $result["tokeninc"] = "0";
            $res = array('error' => '1', 'errortip' => "用户token失效", 'result' => $result);
            ajax_return($res, $request['language_type']);
        }
    }

    //招生名单线索 -- 招生名单线索管理
    function HomeView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Crm\ClientModel($request);
        if (!$request['school_id']) {
            $res = array('error' => '1', 'errortip' => "学校id错误", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        if (!$request['company_id']) {
            $res = array('error' => '1', 'errortip' => "公司id错误", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        $datalist = $Model->getClientList($request);
        if($request['isgrossmenu'] != 1) {
            $k = 0;
            $field[$k]["fieldstring"] = "姓名";
            $field[$k]["fieldname"] = "client_cnname";
            $field[$k]["show"] = '1';
            $field[$k]["custom"] = '0';
            $field[$k]["is_method"] = 1;
            $field[$k]["isShowRule"] = 1;//前端为了显示保护法的图标
            $field[$k]["isReadStatus"] = 1;//已读未读标识，集团跟进 - 学校是否已读
            $k++;
            $field[$k]["fieldstring"] = "英文名";
            $field[$k]["fieldname"] = "client_enname";
            $field[$k]["show"] = '0';
            $field[$k]["custom"] = '1';
            $k++;
            $field[$k]["fieldstring"] = "标签";
            $field[$k]["fieldname"] = "client_tag";
            $field[$k]["show"] = '1';
            $field[$k]["custom"] = '1';
            $field[$k]["istag"] = '1';
            $k++;
            $field[$k]["fieldstring"] = "性别";
            $field[$k]["fieldname"] = "client_sex";
            $field[$k]["show"] = '1';
            $field[$k]["custom"] = '1';
            $k++;
            $field[$k]["fieldstring"] = "年龄";
            $field[$k]["fieldname"] = "client_age";
            $field[$k]["show"] = '1';
            $field[$k]["custom"] = '1';
            $k++;
            $field[$k]["fieldstring"] = "主要联系人";
            $field[$k]["fieldname"] = "family_cnname";
            $field[$k]["show"] = '1';
            $field[$k]["custom"] = '1';
            $k++;
            $field[$k]["fieldstring"] = "主要联系电话";
            $field[$k]["fieldname"] = "client_mobile";
            $field[$k]["show"] = '1';
            $field[$k]["custom"] = '1';
            $field[$k]["isTypeTab"] = 1;//前端为了区分是不是本地手机号
            $k++;
//            if($request['language_type'] == 'tw'){
                $field[$k]["fieldstring"] = "Email";
                $field[$k]["fieldname"] = "client_email";
                $field[$k]["show"] = '1';
                $field[$k]["custom"] = '1';
                $k++;
//            }
            $field[$k]["fieldstring"] = "活动";
            $field[$k]["fieldname"] = "activity_name";
            $field[$k]["show"] = '1';
            $field[$k]["custom"] = '1';
            $k++;
            $field[$k]["fieldstring"] = "渠道类型";
            $field[$k]["fieldname"] = "client_source";
            $field[$k]["show"] = '1';
            $field[$k]["custom"] = '1';
            $k++;
            $field[$k]["fieldstring"] = "渠道明细";
            $field[$k]["fieldname"] = "channel_name";
            $field[$k]["show"] = '1';
            $field[$k]["custom"] = '1';
            $k++;
            $field[$k]["fieldstring"] = "接触点";
            $field[$k]["fieldname"] = "client_frompage";
            $field[$k]["show"] = '1';
            $field[$k]["custom"] = '1';
            $k++;
            $field[$k]["fieldstring"] = "联系地址";
            $field[$k]["fieldname"] = "client_address";
            $field[$k]["show"] = '1';
            $field[$k]["custom"] = '1';
            $k++;
            $field[$k]["fieldstring"] = "省";
            $field[$k]["fieldname"] = "province_name";
            $field[$k]["show"] = '0';
            $field[$k]["custom"] = '1';
            $k++;
            $field[$k]["fieldstring"] = "市";
            $field[$k]["fieldname"] = "city_name";
            $field[$k]["show"] = '0';
            $field[$k]["custom"] = '1';
            $k++;
            $field[$k]["fieldstring"] = "区";
            $field[$k]["fieldname"] = "area_name";
            $field[$k]["show"] = '0';
            $field[$k]["custom"] = 1;
            $k++;
            $field[$k]["fieldstring"] = "推荐人";
            $field[$k]["fieldname"] = "client_sponsor";
            $field[$k]["show"] = '1';
            $field[$k]["custom"] = '1';
            $k++;
            $field[$k]["fieldstring"] = "推荐教师";
            $field[$k]["fieldname"] = "client_teachername";
            $field[$k]["show"] = '1';
            $field[$k]["custom"] = '1';
            $k++;
            $field[$k]["fieldstring"] = "意向课程";
            $field[$k]["fieldname"] = "course_cnname";
            $field[$k]["show"] = '1';
            $field[$k]["custom"] = '1';
            $field[$k]["istag"] = '1';
            $k++;
            $field[$k]["fieldstring"] = "创建时间";
            $field[$k]["fieldname"] = "client_createtime";
            $field[$k]["show"] = '1';
            $field[$k]["custom"] = '1';
            $k++;
            $field[$k]["fieldstring"] = "附近学校";
            $field[$k]["fieldname"] = "nearschool_name";
            $field[$k]["show"] = '0';
            $field[$k]["custom"] = '1';
            $k++;
            $field[$k]["fieldstring"] = "备注";
            $field[$k]["fieldname"] = "client_remark";
            $field[$k]["show"] = '0';
            $field[$k]["custom"] = '1';
            $k++;
            $field[$k]["fieldstring"] = "小学";
            $field[$k]["fieldname"] = "client_primaryschool";
            $field[$k]["show"] = '0';
            $field[$k]["custom"] = '1';
            $k++;
            $field[$k]["fieldstring"] = "小学年级";
            $field[$k]["fieldname"] = "client_primaryschoolgrade";
            $field[$k]["show"] = '0';
            $field[$k]["custom"] = '1';
            $k++;
        }else{
            $k = 0;
            $field[$k]["fieldstring"] = "姓名";
            $field[$k]["fieldname"] = "client_cnname";
            $field[$k]["show"] = '1';
            $field[$k]["custom"] = '0';
            $field[$k]["is_method"] = 1;
            $field[$k]["isShowRule"] = 1;//前端为了显示保护法的图标
            $field[$k]["isReadStatus"] = 1;//已读未读标识，集团跟进 - 学校是否已读
            $k++;
            $field[$k]["fieldstring"] = "性别";
            $field[$k]["fieldname"] = "client_sex";
            $field[$k]["show"] = '1';
            $field[$k]["custom"] = '1';
            $k++;
            $field[$k]["fieldstring"] = "年龄";
            $field[$k]["fieldname"] = "client_age";
            $field[$k]["show"] = '1';
            $field[$k]["custom"] = '1';
            $k++;
            $field[$k]["fieldstring"] = "主要联系人";
            $field[$k]["fieldname"] = "family_cnname";
            $field[$k]["show"] = '1';
            $field[$k]["custom"] = '1';
            $k++;
            $field[$k]["fieldstring"] = "主要联系手机";
            $field[$k]["fieldname"] = "client_mobile";
            $field[$k]["show"] = '1';
            $field[$k]["custom"] = '1';
            $field[$k]["isTypeTab"] = 1;//前端为了区分是不是本地手机号
            $k++;
//            if($request['language_type'] == 'tw'){
            $field[$k]["fieldstring"] = "Email";
            $field[$k]["fieldname"] = "client_email";
            $field[$k]["show"] = '1';
            $field[$k]["custom"] = '1';
            $k++;
//            }
            $field[$k]["fieldstring"] = "来源活动";
            $field[$k]["fieldname"] = "activity_name";
            $field[$k]["show"] = '1';
            $field[$k]["custom"] = '1';
            $k++;
            $field[$k]["fieldstring"] = "渠道类型";
            $field[$k]["fieldname"] = "client_source";
            $field[$k]["show"] = '1';
            $field[$k]["custom"] = '1';
            $k++;
            $field[$k]["fieldstring"] = "渠道明细";
            $field[$k]["fieldname"] = "channel_name";
            $field[$k]["show"] = '1';
            $field[$k]["custom"] = '1';
            $k++;
            $field[$k]["fieldstring"] = "接触点";
            $field[$k]["fieldname"] = "client_frompage";
            $field[$k]["show"] = '1';
            $field[$k]["custom"] = '1';
            $k++;
            $field[$k]["fieldstring"] = "创建时间";
            $field[$k]["fieldname"] = "client_createtime";
            $field[$k]["show"] = '1';
            $field[$k]["custom"] = '1';
            $k++;
        }

        $result = array();
        $result['fieldcustom'] = 0;   //支持自定义
        $result['field'] = $field;

        if ($datalist['list']) {
            $result['list'] = $datalist['list'];
        } else {
            $result['list'] = array();
        }
        if (isset($datalist['allnums']) && $datalist['allnums'] != "") {
            $allnum = intval($datalist['allnums']);
        } else {
            $allnum = 0;
        }
        $all_num = $this->DataControl->selectClear("
 				SELECT c.client_id 
                FROM crm_client as c
				Left JOIN crm_client_schoolenter as s  ON s.client_id=c.client_id
				left JOIN crm_client_family as  f ON f.client_id= c.client_id
				LEFT JOIN smc_parenter  as sp ON sp.parenter_id = f.parenter_id
                WHERE c.client_isnewtip = '1' and s.is_enterstatus <> '-1' and c.client_distributionstatus = 0 and c.client_tracestatus > -1 and c.client_tracestatus < 4 and c.company_id='{$request['company_id']}' and s.school_id='{$request['school_id']}'  
                GROUP by c.client_id
                 ");

        if ($all_num) {
            $result['notreadnum'] = count($all_num);
        } else {
            $result['notreadnum'] = '0';
        }
        $result['activity_list'] = $datalist['activity_list'];

        if ($result['list']) {
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result, "allnum" => $allnum);
        } else {
            $res = array('error' => '1', 'errortip' => "暂无有效名单", 'result' => $result, "allnum" => $allnum);
        }
        ajax_return($res, $request['language_type']);

    }

    //有效名单 批量转为 毛名单
    function changeClientToScreenedApi()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Crm\ClientModel($request);
        $bools = $Model->changeClientToScreenedApi($request);

        if ($bools == true) {
            $this->addCrmWorkLog($request['company_id'], $request['school_id'], $request['marketer_id'], "待分配有效名单批量转回毛名单", '批量转回毛名单', dataEncode($request));
            $res = array('error' => '0', 'errortip' => $Model->errortip, 'result' => array());
        } else {
            $res = array('error' => '1', 'errortip' => $Model->errortip, 'result' => array());
        }

        ajax_return($res, $request['language_type']);
    }

    //毛名单 批量转为 有效名单
    function changeClientToValidApi()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Crm\ClientModel($request);
        $bools = $Model->changeClientToValidApi($request);

        if ($bools == true) {
            $this->addCrmWorkLog($request['company_id'], $request['school_id'], $request['marketer_id'], "毛名单批量转为待分配有效名单", '批量转为待分配有效名单', dataEncode($request));
            $res = array('error' => '0', 'errortip' => $Model->errortip, 'result' => array());
        } else {
            $res = array('error' => '1', 'errortip' => $Model->errortip, 'result' => array());
        }

        ajax_return($res, $request['language_type']);
    }

    function SereachOneView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $where = "c.company_id='{$request['company_id']}'";
        $whereTwo = " 1 ";
        $this->ThisVerify($request);//验证账户

        $fieldstring = array('client_id', 'client_cnname', 'client_enname', 'client_sex', 'client_age', 'channel_medianame', 'channel_name', 'client_mobile', 'client_statusname', 'school_cnname', 'tmkmarketer_name', 'marketer_name');
        $fieldname = array('ID', '姓名', '英文名', '性别', '年龄', '渠道类型', '渠道明细', '主要联系手机', '名单状态', '所在学校', '电销负责人', '招生主要负责人');
        $fieldcustom = array('0', "1", "0", "1", "1", "1","1", "0", "1", "1", "1", "1");
        $fieldshow = array('0', "1", "0", "1", "1", "1", "1", "0", "1", "1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }
        $result['list'] = array();
        $result['field'] = $field;

        if (isset($request['keyword']) && trim($request['keyword']) !== "") {
            //sql优化
            $where .= " AND (  ";

            $keyword = str_replace(' ', '', $request['keyword']);
            $arr_keyword = explode('|', $keyword);
            if (is_array($arr_keyword)) {
                for ($i = 0; $i < count($arr_keyword); $i++) {
                    if($arr_keyword[$i]){
                        if (!checkMobile($arr_keyword[$i])) {
                            $mbnum = $this->DataControl->selectOne(" select NEW_DECRYPTP('{$arr_keyword[$i]}') as mobile limit 0,1 ");
                            if($mbnum['mobile']) {
                                $arr_keyword[$i] = $mbnum['mobile'];
                            }
                        }
                    }
                    $tem[] = "'{$arr_keyword[$i]}'";
                }
            }
            $strKeyword = implode($tem, ',');

            $where .= " (c.client_mobile IN ({$strKeyword}) OR c.client_cnname IN ({$strKeyword}) or c.client_id = '{$request['keyword']}'  )";
            $whereTwo .= " AND ( a.parenter_mobile IN ({$strKeyword}) )";
        } else {
            $res = array('error' => '1', 'errortip' => "请输入手机号", 'result' => $result, "allnum" => 0);
            ajax_return($res, $request['language_type']);
        }

        $someclient = $this->DataControl->selectOne(" SELECT GROUP_CONCAT(b.client_id) as clientstr 
            FROM smc_parenter as a,crm_client_family as b,crm_client as c  
            WHERE {$whereTwo} and  a.parenter_id = b.parenter_id and b.client_id = c.client_id and c.company_id = '{$request['company_id']}' ");
        if($someclient['clientstr']){
            $where .= " or c.client_id in ({$someclient['clientstr']})  ";
        }
        //sql优化
        $where .= ")";

        $clientOne = $this->DataControl->selectClear("SELECT c.client_id, c.client_cnname, c.client_enname, c.client_mobile, c.client_distributionstatus,c.client_age, c.client_tracestatus, c.client_sex, c.client_createtime, l.channel_medianame, l.channel_name,'0' as issmcstu 
        , ( SELECT mm.marketer_name FROM crm_client_tmkprincipal tm,crm_marketer mm WHERE tm.client_id = c.client_id and tm.school_id = '{$request['school_id']}' and tm.tmkprincipal_leave = '0' and tm.marketer_id = mm.marketer_id order by tm.tmkprincipal_id desc limit 0,1) AS tmkmarketer_name
        , ( SELECT a.audition_isvisit FROM crm_client_audition a WHERE a.client_id = c.client_id ORDER BY a.audition_id DESC LIMIT 0, 1 ) AS isvisit
        , ( SELECT s.school_cnname FROM smc_school s, crm_client_schoolenter e WHERE s.school_id = e.school_id AND e.client_id = c.client_id AND e.is_enterstatus = '1' LIMIT 0, 1 ) AS school_cnname
        , ( SELECT group_concat(concat(m.marketer_name)) FROM crm_client_principal a, crm_marketer m WHERE a.marketer_id = m.marketer_id AND a.client_id = c.client_id AND a.principal_leave = 0 AND a.principal_ismajor = 1 ORDER BY principal_id DESC LIMIT 0, 1 ) AS marketer_name
        , ( SELECT e.school_id FROM smc_school s, crm_client_schoolenter e WHERE s.school_id = e.school_id AND e.client_id = c.client_id AND e.is_enterstatus = '1' and e.school_id = '{$request['school_id']}' LIMIT 0, 1 ) AS school_id_in 
        FROM crm_client c LEFT JOIN crm_code_channel l ON c.channel_id = l.channel_id 
        WHERE {$where}");

//        0待跟踪1持续跟踪2已柜询3已试听4已转正-1已流失-2已无效' xxx
        if ($request['language_type'] == 'tw') {
            $tracestatusArray = array('0' => '待跟蹤', '1' => '持續跟蹤', '2' => '已櫃詢', '3' => '已試聽', '4' => '已轉正', '-1' => '無意向', '-2' => '已作廢');
        } else {
            $tracestatusArray = array('0' => '待跟踪', '1' => '持续跟踪', '2' => '已柜询', '3' => '已试听', '4' => '已转正', '-1' => '无意向', '-2' => '已无效');
        }
        if ($clientOne) {
            $clientidarray = array_column($clientOne,'client_id');
            $clientidsstr = implode(",",$clientidarray);
            foreach ($clientOne as &$val) {
                $val['client_statusname'] = $tracestatusArray[$val['client_tracestatus']];
                $val['school_cnname'] = $val['school_cnname'] == null ? '--' : $val['school_cnname'];
                $val['marketer_name'] = $val['marketer_name'] == null ? '--' : $val['marketer_name'];
                $val['tmkmarketer_name'] = $val['tmkmarketer_name'] == null ? '--' : $val['tmkmarketer_name'];
                $val['fu_marketer_name'] = $val['fu_marketer_name'] == null ? '--' : $val['fu_marketer_name'];
            }
        }else{
            $clientOne = array();
        }


        $sturwhere = ' ';
        if (isset($request['keyword']) && trim($request['keyword']) !== "") {
            $keyword = str_replace(' ', '', $request['keyword']);
            $arr_keyword = explode('|', $keyword);
            if (is_array($arr_keyword)) {
                for ($i = 0; $i < count($arr_keyword); $i++) {
                    if($arr_keyword[$i]){
                        if (!checkMobile($arr_keyword[$i])) {
                            $mbnum = $this->DataControl->selectOne(" select NEW_DECRYPTP('{$arr_keyword[$i]}') as mobile limit 0,1 ");
                            if($mbnum['mobile']) {
                                $arr_keyword[$i] = $mbnum['mobile'];
                            }
                        }
                    }
                    $tem[] = "'{$arr_keyword[$i]}'";
                }
            }
            $strKeyword = implode($tem, ',');
            $sturwhere .= " AND (sf.family_mobile IN ({$strKeyword}) OR s.student_cnname IN ({$strKeyword}) )";
        }
        if($clientidsstr){
            $sturwhere .= " and s.from_client_id not in ($clientidsstr) ";
        }
        //点击会有错误显示  select  s.student_id as client_id,
        $schsql = " select  s.from_client_id as client_id,
                        s.student_cnname as client_cnname,
                        s.student_enname as client_enname,
                        s.student_sex as client_sex,
                        s.student_birthday,
                        '--' as channel_medianame,
                        '--' as channel_name,
                        sf.family_mobile as client_mobile, 
                        '已转正' as client_statusname,
                        h.school_cnname,
                        '--' as tmkmarketer_name,
                        '--' as marketer_name,
                        '1' as issmcstu, 
                        se.school_id as school_id_in
                        FROM smc_student_enrolled as se
                        left join smc_student as s on se.student_id=s.student_id
                        left join smc_student_family as sf on sf.student_id=s.student_id 
                        left join smc_school as h on h.school_id = se.school_id
                        where sf.family_isdefault=1 {$sturwhere} and h.company_id = '{$request['company_id']}' 
                        order by field(se.enrolled_status,'1','0','2','3','-1',NULL),s.student_id ASC 
                        limit 0,1 ";
        $clientOneTwo = $this->DataControl->selectClear($schsql);

        if($clientOneTwo) {
            foreach ($clientOne as &$clientVar) {
                $clientVar['client_age'] = birthdaytoage($clientVar['student_birthday']);
            }
        }else{
            $clientOneTwo = array();
        }

        $clientAll = array_merge($clientOne,$clientOneTwo);
        if(!$clientAll){
            $sturwhere = ' ';
            if (isset($request['keyword']) && trim($request['keyword']) !== "") {
                $keyword = str_replace(' ', '', $request['keyword']);
                $arr_keyword = explode('|', $keyword);
                if (is_array($arr_keyword)) {
                    for ($i = 0; $i < count($arr_keyword); $i++) {
                        if($arr_keyword[$i]){
                            if (!checkMobile($arr_keyword[$i])) {
                                $mbnum = $this->DataControl->selectOne(" select NEW_DECRYPTP('{$arr_keyword[$i]}') as mobile limit 0,1 ");
                                if($mbnum['mobile']) {
                                    $arr_keyword[$i] = $mbnum['mobile'];
                                }
                            }
                        }
                        $tem[] = "'{$arr_keyword[$i]}'";
                    }
                }
                $strKeyword = implode($tem, ',');
                $sturwhere .= " AND (sf.family_mobile IN ({$strKeyword}) OR s.student_cnname IN ({$strKeyword}) )";
            }
            //select  s.student_id as client_id,
            $schsql = " select  s.from_client_id as client_id,
                        s.student_cnname as client_cnname,
                        s.student_enname as client_enname,
                        s.student_sex as client_sex,
                        s.student_birthday,
                        '--' as channel_medianame,
                        '--' as channel_name,
                        sf.family_mobile as client_mobile, 
                        '已转正' as client_statusname,
                        h.school_cnname,
                        '--' as tmkmarketer_name,
                        '--' as marketer_name,
                        '1' as issmcstu, 
                        se.school_id as school_id_in 
                        FROM smc_student_enrolled as se
                        left join smc_student as s on se.student_id=s.student_id
                        left join smc_student_family as sf on sf.student_id=s.student_id 
                        left join smc_school as h on h.school_id = se.school_id
                        where sf.family_isdefault=1 {$sturwhere} and h.company_id = '{$request['company_id']}' 
                        order by field(se.enrolled_status,'1','0','2','3','-1',NULL),s.student_id ASC 
                        limit 0,1 ";
            $clientAll = $this->DataControl->selectClear($schsql);

            if($clientAll) {
                foreach ($clientAll as &$clientVar) {
                    $clientVar['client_age'] = birthdaytoage($clientVar['student_birthday']);
                }
            }else{
                $clientAll = array();
            }
        }
        if($clientAll){
            foreach ($clientAll as &$clientVar){
                if($clientVar['school_id_in'] == $request['school_id']){
                    $clientVar['isThisSchool'] = 1;
                }else{
                    $clientVar['isThisSchool'] = 0;
                }
            }
        }

        $result['list'] = $clientAll;
        $result['field'] = $field;
        $allnum = 0;

        if ($result['list']) {
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result, "allnum" => $allnum);
        } else {
            $res = array('error' => '1', 'errortip' => "未查询到有效名单", 'result' => $result, "allnum" => $allnum);
        }

        ajax_return($res, $request['language_type']);
    }

    //招生名单线索---获取单个的客户的基本信息
    function clientOneApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $client_id = $request['client_id'];
        $marketer_id = $request['marketer_id'];
        $from = $request['from'];    //柜询 1 试听 1 其余为 0
        if (!$marketer_id) {
            $res = array('error' => '1', 'errortip' => "登录人id错误", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        if (!$client_id) {
            $res = array('error' => '1', 'errortip' => "客户id错误", 'result' => array());
            ajax_return($res, $request['language_type']);
        }

        $Model = new \Model\Crm\ClientModel($request);

        $dataOne = $Model->getClientOne($client_id, $request['school_id']);
        $field['client_id'] = "客户id";
        $field['client_img'] = "头像";
        $field['client_sex'] = "性别";
        $field['client_cnname'] = "中文名";
        $field['client_enname'] = "英文名";
        $field['client_mobile'] = "主要联系手机";
        $field['main_family_cnname'] = "主要联系人";
        $field['client_source'] = "招生渠道类型";
        $field['channel_name'] = "招生渠道类型";
        $field['client_birthday'] = "出生日期";
        $field['client_icard'] = "身份证号";
        $field['client_intention_level'] = "意向等级";
        $field['client_oh_month'] = "OH月份";
        $field['client_push_month'] = "PUSH月份";
        $field['client_sponsor'] = "介绍人";
        $field['client_remark'] = "备注";
        $field['main_marketer_name'] = "主负责人";
        $field['nearschool_name'] = "学校";

        $field['client_family_list']['family_cnname'] = "中文名";
        $field['client_family_list']['family_mobile'] = "联系电话";
        $field['client_family_list']['family_email'] = "邮箱";
        $field['client_tracestatus'] = "客户状态";

        $field['client_principal_name']['principal_id'] = "负责人记录id";
        $field['client_principal_name']['marketer_name'] = "副负责人";
        $field['client_family_list']['family_id'] = "亲属关系id";
        $field['client_family_list']['family_relation'] = "亲属关系";
        $field['client_family_list']['family_isdefault'] = "是否为主要联系人";

        $field['client_course_list']['intention_id'] = "意向课程id";
        $field['client_course_list']['course_cnname'] = "意向课程";

        $field['client_tag']['label_name'] = "标签名称";

        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['list'] = $dataOne;

        if ($from == 1)   //柜询与试听 的跟进
        {
            //检测是否显示跟进按钮  //检测是否可以跟进  // 不是别人的意向客户, // 判断 他是线索 可以跟进 他是自己的意向客户 可以跟进

            $isTrack = $Model->checkIsTrack($client_id, $marketer_id);

            if ($isTrack == false) {
                $result['is_track'] = 0;
            } else {
                $result['is_track'] = 1;   //显示
            }
        }
        $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);


        ajax_return($res, $request['language_type']);
    }

    //招生渠道变更审核列表
    function getChannellogListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Crm\ClientModel($request);

        $result = $this->Model->getChannellogList($request);
        ajax_return($result, $request['language_type']);
    }

    //申请变更渠道
    function applyChannelAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Crm\ClientModel();

        $result = $this->Model->applyChannelAction($request);
        ajax_return($result, $request['language_type']);
    }

    //渠道变更记录
    function getChannelTrackView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Crm\ClientModel($request);

        $result = $Model->getChannelTrack($request);
        ajax_return($result, $request['language_type']);
    }

    //招生名单线索---查看单个客户的跟踪记录
    function clientOneTrackApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $client_id = $request['client_id'];
        if (!$client_id) {
            $res = array('error' => '1', 'errortip' => "客户id有误", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        $Model = new \Model\Crm\ClientModel($request);
        $dataOne = $Model->getclientTrackList($request);
        if ($dataOne) {
            foreach ($dataOne as $key => $value) {
                $value['track_createtime'] = date('Y-m-d H:i:s');
            }
        }
        $field['marketer_name'] = "负责人";
        $field['track_linktype'] = "沟通方式";
        $field['client_img'] = "客户头像";
        $field['track_followmode'] = "本次跟进模式0普通回访1柜询2视听3转正-1无意向跟进";
        $field['track_followuptype'] = "下次跟进类型";  //0普通跟进1提醒跟进
        $field['track_followuptime'] = "下次跟进时间";
        $field['commode_name'] = "沟通类型";
        $field['track_visitingtime'] = "柜询/视听时间";
        $field['main_marketer_name'] = "主负责人";
        $field['track_note'] = "沟通内容";
        $field['track_createtime'] = "创建时间";
        $field['class_cnname'] = "试听班级";
        $field['object_name'] = "沟通对象";
        $field['track_validinc_name'] = '沟通效果';
        $field['track_invalidreason'] = '无效时选择的原因';
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($dataOne) {
            $result['list'] = $dataOne;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无跟踪记录", 'result' => $result);
        }

        ajax_return($res, $request['language_type']);

    }

    //招生名单线索 -- 查看单个客户的柜询记录
    function clientOneInviteApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $client_id = $request['client_id'];
        if (!$client_id) {
            $res = array('error' => '1', 'errortip' => "客户id有误", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        $Model = new \Model\Crm\ClientModel($request);
        $datalist = $Model->getclientInviteList($client_id);

        $fieldstring = array('invite_genre', 'invite_level', 'invite_visittime', 'invite_isvisit', 'invite_novisitreason');
        $fieldname = array('柜询类型', '客户意向', '柜询到访日', '是否到访', '原因');
        $fieldcustom = array("1", "1", "1", "1", '0');
        $fieldshow = array("1", "1", "1", '1', '0');


        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
            if ($field[$i]["fieldname"] == "invite_level") {
                $field[$i]["islevel"] = 'true';
            }

        }

        $result = array();

        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($datalist) {
            $result['list'] = $datalist;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无柜询记录", 'result' => $result);
        }

        ajax_return($res, $request['language_type']);
    }

    //招生名单线索 -- 查看单个客户的试听记录
    function clientOneAuditionApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $client_id = $request['client_id'];
        if (!$client_id) {
            $res = array('error' => '1', 'errortip' => "客户id有误", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        $Model = new \Model\Crm\ClientModel($request);
        $datalist = $Model->getclinetAuditionList($client_id);

        $fieldstring = array('class_cnname', 'audition_visittime', 'course_cnname', 'audition_isvisit', 'invite_novisitreason');
        $fieldname = array('试听班级', "试听时间", '试听课程', '是否到访', '原因');
        $fieldcustom = array("1", "1", "1", "1", '0');
        $fieldshow = array("1", "1", "1", "1", '0');

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }
        $result = array();

        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['list'] = $datalist;
        if ($datalist) {
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无试听记录", 'result' => $result);
        }

        ajax_return($res, $request['language_type']);

    }

    //招生名单线索 -- 跟进客户
    function addInviteIsvisitTrackOneView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        if (!$request['client_id']) {
            $res = array('error' => '1', 'errortip' => "客户id有误", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        if (!$request['company_id']) {
            $res = array('error' => '1', 'errortip' => '公司id有误', 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        if ($request['postbe_crmuserlevel'] == 3) {
            $res = array('error' => '1', 'errortip' => "您没有权限跟踪操作", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        if (!$request['school_id']) {
            $res = array('error' => '1', 'errortip' => "学校id有误", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        if (!$request['school_id']) {
            $res = array('error' => '1', 'errortip' => "学校id有误", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        if (!$request['promotion_id']) {
            $res = array('error' => '1', 'errortip' => "地推人员不能为空", 'result' => array());
            ajax_return($res, $request['language_type']);
        }

        $Model = new \Model\Crm\ClientModel($request);
        $bool = $Model->addInviteIsvisitTrackOne($request);

        if ($bool == true) {
            if ($request['track_followmode'] == 0) {
                $word = "回访";
            } elseif ($request['track_followmode'] == 1) {
                $word = "柜询";
            } elseif ($request['track_followmode'] == 2) {
                $word = "试听";
            } elseif ($request['track_followmode'] == 3) {
                $word = "转正";
            } elseif ($request['track_followmode'] == -1) {
                $word = "无意向";
            }
            $res = array('error' => $Model->error,'erroraa' => $Model->errortipaa, 'errortip' => $word . $Model->errortip, 'result' => array());
            $this->addCrmWorkLog($request['company_id'], $request['school_id'], $request['marketer_id'], "待分配名单-到访", '到访-推带到访' . $word, dataEncode($request));
        } else {
            $res = array('error' => $Model->error,'erroraa' => $Model->errortipaa, 'errortip' => $Model->errortip, 'result' => array());
        }
        ajax_return($res, $request['language_type']);
    }

    //招生名单线索 -- 跟进客户
    function trackClientAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        if (!$request['client_id']) {
            $res = array('error' => '1', 'errortip' => "客户id有误", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        if (!$request['company_id']) {
            $res = array('error' => '1', 'errortip' => '公司id有误', 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        if ($request['postbe_crmuserlevel'] == 3) {
            $res = array('error' => '1', 'errortip' => "您没有权限跟踪操作", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        if (!$request['school_id']) {
            $res = array('error' => '1', 'errortip' => "学校id有误", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        if (!isset($request['client_answerphone'])) {
            $res = array('error' => '1', 'errortip' => "请选择是否接通", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        if ($request['client_answerphone'] <> 0 && $request['track_answerphone'] <> '-1') {
            if (($request['track_followmode'] == 1 || $request['track_followmode'] == 2) && $request['receiver_name'] == "") {
                $res = array('error' => '1', 'errortip' => "请填写接待人", 'result' => array());
                ajax_return($res, $request['language_type']);
            }
            if (isset($request['track_followmode']) && $request['track_followmode'] == "") {
                $res = array('error' => '1', 'errortip' => "请选择跟进类型", 'result' => array());
                ajax_return($res, $request['language_type']);
            }
            if (($request['track_followmode'] == 1) && $request['invite_visittime'] == "") {
                $res = array('error' => '1', 'errortip' => "请选择柜询时间", 'result' => array());
                ajax_return($res, $request['language_type']);
            }
            if($request['track_followmode'] != '-2') {
                if (isset($request['track_note']) && $request['track_note'] == "") {
                    $res = array('error' => '1', 'errortip' => "请填写沟通内容", 'result' => array());
                    ajax_return($res, $request['language_type']);
                }
            }else{
                if (isset($request['invalidnote_code']) && $request['invalidnote_code'] == "") {
                    $res = array('error' => '1', 'errortip' => "请填写无效原因", 'result' => array());
                    ajax_return($res, $request['language_type']);
                }
            }
//		if($request['track_followmode'] == 2 && $request['audition_visittime']==""){
//			$res = array('error' => '1', 'errortip' => "请选择试听时间", 'result' => array());
//			ajax_return($res,$request['language_type']);
//		}
//		if($request['track_followmode'] == 2 && $request['class_id']==""){
//			$res = array('error' => '1', 'errortip' => "请选择班级", 'result' => array());
//			ajax_return($res,$request['language_type']);
//		}
            if ($request['track_followuptype'] == 1 && $request['track_followuptime'] == "") {
                $res = array('error' => '1', 'errortip' => "请选择下次跟进时间", 'result' => array());
                ajax_return($res, $request['language_type']);
            }
        }

        $Model = new \Model\Crm\ClientModel($request);
        $bool = $Model->insertTrackClientOne($request);

        if ($bool == true) {
            if ($request['track_followmode'] == 0) {
                $word = "回访";
            } elseif ($request['track_followmode'] == 1) {
                $word = "柜询";
            } elseif ($request['track_followmode'] == 2) {
                $word = "试听";
            } elseif ($request['track_followmode'] == 3) {
                $word = "转正";
            } elseif ($request['track_followmode'] == -1) {
                $word = "无意向";
            }

            $res = array('error' => $Model->error,'erroraa' => $Model->errortipaa, 'errortip' => $word . $Model->errortip, 'result' => array());
            $this->addCrmWorkLog($request['company_id'], $request['school_id'], $request['marketer_id'], "意向客户管理->意向客户管理", '跟进-' . $word, dataEncode($request));
        } else {
            $res = array('error' => $Model->error,'erroraa' => $Model->errortipaa, 'errortip' => $Model->errortip, 'result' => array());
        }
        ajax_return($res, $request['language_type']);
    }

    //招生名单线索 -- 绑定学员
    function bindingClientAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        if (!$request['from_client_id']) {
            $res = array('error' => '1', 'errortip' => "客户from_id有误", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        if (!$request['marketer_id']) {
            $res = array('error' => '1', 'errortip' => "登录人ID", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        if (!$request['to_client_id']) {
            $res = array('error' => '1', 'errortip' => "请选择绑定学员名称", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        if (!$request['binding_friend_relation']) {
            $res = array('error' => '1', 'errortip' => "请选择绑定关系", 'result' => array());
            ajax_return($res, $request['language_type']);
        }

        $Model = new \Model\Crm\ClientModel($request);
        $bool = $Model->insertBindingClient($request);

        if ($bool == true) {
            $this->addCrmWorkLog($request['company_id'], $request['school_id'], $request['marketer_id'], "招生有效名单->招生有效名单", '绑定客户', dataEncode($request));
            $res = array('error' => '0', 'errortip' => $Model->errortip, 'result' => array());

        } else {
            $res = array('error' => '1', 'errortip' => $Model->errortip, 'result' => array());
        }

        ajax_return($res, $request['language_type']);
    }

    //招生名单线索 -- 获取绑定学员
    function getBindingClientApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $client_id = $request['client_id'];
        if (!$client_id) {
            $res = array('error' => '1', 'errortip' => "客户id有误", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        $Model = new \Model\Crm\ClientModel($request);
        $dataList = $Model->getBindingClientOne($client_id);
        $field['client_id'] = "客户id";
        $field['client_cnname'] = "中文名";
        $field['client_img'] = "头像";
        $field['client_sex'] = "性别";
        $field['client_age'] = "年龄";
        $field['client_mobile'] = "联系电话";
        $field['binding_relation'] = "绑定关系";

        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        if ($dataList) {
            $result['list'] = $dataList;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无绑定学员", 'result' => $result);
        }

        ajax_return($res, $request['language_type']);
    }

    //招生名单线索---分配--我要跟进  -- 权限时可能需要分开
    function allotClientApi()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        if (!$request['client_id']) {
            $res = array('error' => '1', 'errortip' => "客户id有误", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        if (!$request['main_marketer_id']) {
            $res = array('error' => '1', 'errortip' => "请选择主负责人", 'result' => array());
            ajax_return($res, $request['language_type']);
        }

        $Model = new \Model\Crm\ClientModel($request);
        $bool = $Model->allotClientToIntention($request);
        if ($bool) {
            $this->addCrmWorkLog($request['company_id'], $request['school_id'], $request['marketer_id'], "招生有效名单->招生有效名单", '分配', dataEncode($request));
        }

        $field['client_cnname'] = "客户中文名";
        $field['marketer'] = "负责人";

        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['list'] = $Model->errortip;
        $result['tip'] = $Model->allottip;
        $result['allot_num'] = $Model->allotnum;
        if (empty($result['tip'])) {
            $result['tip'] = "分配成功";
        }

        if ($Model->error == 0) {
            $res = array('error' => '0', 'errortip' => "分配成功", 'result' => $result);
        } else {
            $res = array('error' => '1', 'errortip' => $Model->errortip, 'result' => $result);
        }


        ajax_return($res, $request['language_type']);
    }

    //招生名单线索---分配--我要跟进  -- 权限时可能需要分开
    function allotTmkClientApi()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        if (!$request['main_marketer_id']) {
            $res = array('error' => '1', 'errortip' => "请选择负责人", 'result' => array());
            ajax_return($res, $request['language_type']);
        }

        $Model = new \Model\Crm\ClientModel($request);
        $bool = $Model->allotTmkClientToIntention($request);
        if ($bool) {
            $this->addCrmWorkLog($request['company_id'], $request['school_id'], $request['marketer_id'], "招生有效名单->待筛选名单", '分配', dataEncode($request));
        }

        $result['fieldcustom'] = 0;
        $result['list'] = $Model->errortip;
        $result['tip'] = $Model->allottip;
        $result['allot_num'] = $Model->allotnum;
        if (empty($result['tip'])) {
            $result['tip'] = "分配成功";
        }

        if ($Model->error == 0) {
            $res = array('error' => '0', 'errortip' => "分配成功", 'result' => $result);
        } else {
            $res = array('error' => '1', 'errortip' => $Model->errortip, 'result' => $result);
        }

        ajax_return($res, $request['language_type']);
    }

    //招生名单线索-我要跟进
    function trackClientApi()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        if (!$request['client_id']) {
            $res = array('error' => '1', 'errortip' => "客户id有误", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        if (!$request['main_marketer_id']) {
            $res = array('error' => '1', 'errortip' => "请选择主负责人", 'result' => array());
            ajax_return($res, $request['language_type']);
        }

        $Model = new \Model\Crm\ClientModel($request);
        $bool = $Model->allotClientToIntention($request);


        $field['client_cnname'] = "客户中文名";
        $field['marketer'] = "负责人";

        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['list'] = $Model->errortip;
        $result['tip'] = $Model->allottip;
        $result['allot_num'] = $Model->allotnum;
        if (empty($result['tip'])) {
            $result['tip'] = "分配成功";
        }


        if ($Model->error == 0) {
            $res = array('error' => '0', 'errortip' => "分配成功", 'result' => $result);
        } else {
            $res = array('error' => '1', 'errortip' => $Model->errortip, 'result' => array());
        }


        ajax_return($res, $request['language_type']);
    }

    //招生名单线索---转为我的意向客户
    function toMyClientApi()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        if (!$request['client_id']) {
            $res = array('error' => '1', 'errortip' => "客户id有误", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        if (!is_array($request['client_id'])) {
            $request['client_id'] = [$request['client_id']];
        }
        $request['client_distributionstatus'] = 1;     //分配状态为1
        $request['client_tracestatus'] = 0;                //客户状态
        $request['client_ischaserlapsed'] = 0;
//		$request['track_note'] = '流转:招生线索转为我的意向客户';
        $request['type'] = 0;
        $Model = new \Model\Crm\ClientModel($request);
        $bool = $Model->changeClientStatus($request);
        if ($bool == true) {
            $this->addCrmWorkLog($request['company_id'], $request['school_id'], $request['marketer_id'], "招生有效名单->招生有效名单", '转为我的意向客户', dataEncode($request));
            $res = array('error' => '0', 'errortip' => "流转成功", 'result' => array());
        } else {
            $res = array('error' => '1', 'errortip' => $Model->errortip, 'result' => array());
        }
        ajax_return($res, $request['language_type']);
    }

    //招生名单线索---转为无意向客户
    function toLossClientApi()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        if (!$request['client_id']) {
            $res = array('error' => '1', 'errortip' => "客户id有误", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        if (!is_array($request['client_id'])) {
            $request['client_id'] = [$request['client_id']];
        }
        $request['client_distributionstatus'] = 0;     //分配状态为1
        $request['client_tracestatus'] = -1;
        $request['client_ischaserlapsed'] = 0;
//		$request['track_note'] = '流转:招生线索转为无意向客户';
        $request['type'] = -1;
        $Model = new \Model\Crm\ClientModel($request);
        $bool = $Model->changeClientStatus($request);
        if ($bool == true) {
            $this->addCrmWorkLog($request['company_id'], $request['school_id'], $request['marketer_id'], "招生有效名单->招生有效名单", '转为无意向客户', dataEncode($request));
            $res = array('error' => '0', 'errortip' => $Model->errortip, 'result' => array());
        } else {
            $res = array('error' => '1', 'errortip' => $Model->errortip, 'result' => array());
        }
        ajax_return($res, $request['language_type']);
    }

    //招生名单线索---转为 无效
    function toInvalidLossClientApi()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        if (!$request['client_id']) {
            $res = array('error' => '1', 'errortip' => "客户id有误", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        if (!is_array($request['client_id'])) {
            $request['client_id'] = [$request['client_id']];
        }
        $request['client_distributionstatus'] = 0;
        $request['client_tracestatus'] = -2;
        $request['client_isinvalidreview'] = 0;
        $request['type'] = -2;
        $Model = new \Model\Crm\ClientModel($request);
        $bool = $Model->changeClientStatus($request);
        if ($bool == true) {
            $this->addCrmWorkLog($request['company_id'], $request['school_id'], $request['marketer_id'], "招生有效名单->招生有效名单", '转为无效客户', dataEncode($request));
            $res = array('error' => '0', 'errortip' => $Model->errortip, 'result' => array());
        } else {
            $res = array('error' => '1', 'errortip' => $Model->errortip, 'result' => array());
        }
        ajax_return($res, $request['language_type']);
    }

    //招生名单线索 -- 添加招生名单线索
    function addClientAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        if (empty($request['client_cnname'])) {
            $res = array('error' => '1', 'errortip' => '用户名为空', 'result' => array());
            ajax_return($res, $request['language_type']);
        }

        $Model = new \Model\Crm\ClientModel($request);
        $bools = $Model->insertClientOne($request);
        $this->addCrmWorkLog($request['company_id'], $request['school_id'], $request['marketer_id'], "招生有效名单->招生有效名单", '添加有效名单', dataEncode($request));

        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $Model->result?$Model->result:array());

        ajax_return($res, $request['language_type']);
    }

    //招生名单线索 -- 修改招生名单线索
    function editClientAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Crm\ClientModel($request);
        $ClientOne = $this->DataControl->getFieldOne("crm_client", 'client_id', "client_id = '{$request['client_id']}'  ");
        if ($ClientOne) {
            $bool = $Model->editClientOne($request);
            $data = array();
            $data['client_cnname'] = $request['client_cnname'];
            $data['client_enname'] = $request['client_enname'];
            $data['client_img'] = $request['client_img'];
            $data['client_birthday'] = $request['client_birthday'];
            $data['client_sex'] = $request['client_sex'];
            $data['client_mobile'] = $request['client_mobile'];
            $data['client_icard'] = $request['client_icard'];
            $data['client_remark'] = $request['client_remark'];
            $data['client_source'] = $request['client_source'];
            $data['client_push_month'] = $request['client_push_month'];
            $data['client_oh_month'] = $request['client_oh_month'];
            $data['client_sponsor'] = $request['client_sponsor'];
            $data['client_teachername'] = $request['client_teachername'];
            $field = array();
            $field['client_cnname'] = "中文名";
            $field['client_enname'] = "英文名";
            $field['client_birthday'] = "生日";
            $field['client_sex'] = "性别";
            $field['client_mobile'] = "联系电话";
            $field['client_remark'] = "备注";
            $field['client_source'] = "来源";
            $field['client_sponsor'] = "介绍人";
            $field['client_oh_month'] = "OH月份";
            $field['client_push_month'] = "PUSH月份";
            $field['client_intention_level'] = "意向等级";

            $result['fieldcustom'] = 0;
            $result['field'] = $field;
            $result['list'] = $data;

            if ($bool == true) {
                $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $result);
                $this->addCrmWorkLog($request['company_id'], $request['school_id'], $request['marketer_id'], "招生有效名单->招生有效名单", '编辑有效名单', dataEncode($request));
            } else {
                $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
            }
        } else {
            $res = array('error' => '1', 'errortip' => '未查询到该客户', 'result' => array());

        }

        ajax_return($res, $request['language_type']);
    }

    //绑顶客户-获取客户列表
    function getClientNameApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        if (!$request['company_id']) {
            $res = array('error' => '1', 'errortip' => "公司id有误", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        if (!$request['marketer_id']) {
            $res = array('error' => '1', 'errortip' => "登录人id", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        if (!$request['school_id']) {
            $res = array('error' => '1', 'errortip' => "学校id有误", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        $Model = new \Model\Crm\ClientModel($request);
        $dataList = $Model->getClientNameList($request);

        $field['client_id'] = "客户id";
        $field['client_cnname'] = "中文名";
        $result['fieldcustom'] = 1;
        $result['field'] = $field;
        $result['list'] = $dataList;

        $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        ajax_return($res, $request['language_type']);

    }

    //获取学校活动
    function getActivityApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $school_id = Input('get.school_id');
        $company_id = $request['company_id'];

        if (!$school_id) {
            $res = array('error' => '1', 'errortip' => "学校id有误", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        if (!$company_id) {
            $res = array('error' => '1', 'errortip' => "公司id有误", 'result' => array());
        }
        $Model = new \Model\Crm\ClientModel($request);
        $dataList = $Model->getSchoolActivity($school_id, $company_id);

        $field['activity_id'] = "客户id";
        $field['activity_name'] = "中文名";
        $result['field'] = $field;
        $result['list'] = $dataList;
        $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        ajax_return($res, $request['language_type']);
    }

    function transferSchoolAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Crm\ClientModel($request);
        $bool = $Model->transferSchool($request);
        if ($bool) {
            $this->addCrmWorkLog($request['company_id'], $request['school_id'], $request['marketer_id'], "招生有效名单->招生有效名单", 'crm转校', dataEncode($request));
            $res = array('error' => '0', 'errortip' => $Model->errortip, 'result' => array());
        } else {
            $res = array('error' => '1', 'errortip' => $Model->errortip, 'result' => array());
        }
        ajax_return($res, $request['language_type']);


    }

    //设置负责招生状态
    function setMarketerFollowupOneView(){
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Crm\ClientModel($request);
        $bool = $Model->setMarketerFollowupOne($request);
        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $Model->result));
    }

    //招生人员管理
    function getRecruiterView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Crm\ClientModel($request);
        $res = $ReportModel->getRecruiter($request);

        $k = 0;
        $field[$k]["fieldname"] = "marketer_id";
        $field[$k]["fieldstring"] = "负责人ID";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;

        $k++;
        $field[$k]["fieldname"] = "staffer_cnname";
        $field[$k]["fieldstring"] = "负责人中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldname"] = "staffer_enname";
        $field[$k]["fieldstring"] = "负责人英文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldname"] = "post_name";
        $field[$k]["fieldstring"] = "本校职务";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldname"] = "leave_name";
        $field[$k]["fieldstring"] = "在职状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldname"] = "outside_mainnum";
        $field[$k]["fieldstring"] = "校外招主负责人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldname"] = "outside_funum";
        $field[$k]["fieldstring"] = "校外招协助负责人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldname"] = "inner_mainnum";
        $field[$k]["fieldstring"] = "校内招主负责人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldname"] = "inner_funum";
        $field[$k]["fieldstring"] = "校内招协助负责人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res['list']) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '招生人员管理', 'result' => $result, 'allnum' => $res['allnum']);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => "暂无招生人员管理", 'result' => $result, 'allnum' => $res['allnum']);
        }
        ajax_return($res, $request['language_type']);
    }


    //名单交接
    function migrationCrmApi()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        if (!$request['marketer_id']) {
            $res = array('error' => '1', 'errortip' => "操作人有误", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        if (!$request['frommarketer_id']) {
            $res = array('error' => '1', 'errortip' => "被交接人有误", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        //校外招
        if (!$request['tomarketer_id']) {
            $res = array('error' => '1', 'errortip' => "请选择校外招交接人", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        //校内招
        if (!$request['inmarketer_id']) {
            $res = array('error' => '1', 'errortip' => "请选择校内招交接人", 'result' => array());
            ajax_return($res, $request['language_type']);
        }

        //交接负责人名单
        $migrationModel = new \Model\Crm\ClientModel();
        $res = $migrationModel->migrationClient($request['frommarketer_id'],$request['tomarketer_id'],$request['school_id'],$request['marketer_id']);

        $result = $migrationModel->migrationStudent($request['frommarketer_id'],$request['inmarketer_id'],$request['school_id'],$request['marketer_id']);

        if($res && $result){
            $this->addCrmWorkLog($request['company_id'], $request['school_id'], $request['marketer_id'], "名单分配管理->招生人员管理", '名单交接', dataEncode($request));
        }

        $res = array('error' => $migrationModel->error, 'errortip' => $migrationModel->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }



    //---------------------------------------------------------------------------
    //转正客户  已报名名单
    function sucessclientView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        if (!$request['school_id']) {
            $res = array('error' => '0', 'errortip' => "学校id错误", 'result' => array());
            ajax_return($res, $request['language_type']);
        }

        if (!$request['marketer_id']) {
            $res = array('error' => '0', 'errortip' => "未检测到招生人员信息，请重新登录操作", 'result' => array());
            ajax_return($res, $request['language_type']);
        }

        $Model = new \Model\Crm\ClientModel($request);
        $clientList = $Model->getSucessclientList($request);

        $companyOne=$this->DataControl->getFieldOne("gmc_company","company_isopenspecialnumber","company_id='{$request['company_id']}'");

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "中文名";
        $field[$k]["fieldname"] = "client_cnname";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '0';
        $field[$k]["is_method"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "英文名";
        $field[$k]["fieldname"] = "client_enname";
        $field[$k]["show"] = '0';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "标签";
        $field[$k]["fieldname"] = "client_tag";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $field[$k]["istag"] = '1';
        $k++;

        if($companyOne['company_isopenspecialnumber']==1){
            $field[$k]["fieldname"] = "student_thirdbranch";
            $field[$k]["fieldstring"] = "学号";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;
        }

        $field[$k]["fieldstring"] = "学员编号";
        $field[$k]["fieldname"] = "student_branch";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "性别";
        $field[$k]["fieldname"] = "client_sex";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "年龄";
        $field[$k]["fieldname"] = "client_age";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "登记日期";
        $field[$k]["fieldname"] = "positivelog_time";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "主要联系电话";
        $field[$k]["fieldname"] = "client_mobile";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $field[$k]["isTypeTab"] = 1;
        $k++;
//        if($request['language_type'] == 'tw'){
        $field[$k]["fieldstring"] = "Email";
        $field[$k]["fieldname"] = "client_email";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
//        }
        $field[$k]["fieldstring"] = "主要负责人";
        $field[$k]["fieldname"] = "marketer_name";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        if ($this->company_isassist == 1) {
            $k++;
            $field[$k]["fieldstring"] = "协助负责人";
            $field[$k]["fieldname"] = "fu_marketer_name";
            $field[$k]["show"] = '1';
            $field[$k]["custom"] = '1';
        }
        $k++;
        $field[$k]["fieldstring"] = "活动";
        $field[$k]["fieldname"] = "activity_name";
        $field[$k]["show"] = '0';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "地推工号";
        $field[$k]["fieldname"] = "promotion_jobnumber";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "职务";
        $field[$k]["fieldname"] = "post_name";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "渠道类型";
        $field[$k]["fieldname"] = "client_source";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "渠道明细";
        $field[$k]["fieldname"] = "channel_name";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "接触点";
        $field[$k]["fieldname"] = "client_frompage";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "推荐人";
        $field[$k]["fieldname"] = "client_sponsor";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "推荐老师";
        $field[$k]["fieldname"] = "client_teachername";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "招生来源类型";
        $field[$k]["fieldname"] = "client_fromtype";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "联系地址";
        $field[$k]["fieldname"] = "client_address";
        $field[$k]["show"] = '0';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "省";
        $field[$k]["fieldname"] = "province_name";
        $field[$k]["show"] = '0';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "市";
        $field[$k]["fieldname"] = "city_name";
        $field[$k]["show"] = '0';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "区";
        $field[$k]["fieldname"] = "area_name";
        $field[$k]["show"] = '0';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "备注";
        $field[$k]["fieldname"] = "client_remark";
        $field[$k]["show"] = '0';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "名单创建时间";
        $field[$k]["fieldname"] = "client_createtime";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "到访时间";
        $field[$k]["fieldname"] = "inv_aud_time";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';


        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($clientList['list']) {
            $result['list'] = $clientList['list'];
        } else {
            $result['list'] = array();
        }

        if (isset($clientList['allnums']) && $clientList['allnums'] != "") {
            $allnum = $clientList['allnums'];
        } else {
            $allnum = 0;
        }
        if ($result['list']) {
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result, 'allnum' => $allnum);
        } else {
            $res = array('error' => '1', 'errortip' => "暂无已报名名单", 'result' => $result, 'allnum' => $allnum);
        }
        ajax_return($res, $request['language_type']);
    }

    function getCompanyClientView()
    {
        $request = Input("get.");
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Crm\ClientModel($request);
        $clientList = $Model->getCompanyClientList($request);

        $fieldstring = array('client_id', 'activity_id', 'client_cnname', 'client_enname', 'client_sex', 'client_age', 'client_mobile', 'activity_name', 'client_createtime');
        $fieldname = array('ID', '活动id', '中文名', '英文名', '性别', '年龄', '联系手机', '活动名称', '创建时间');
        $fieldcustom = array('0', '0', "1", "1", "1", "1", "1", "1", "1");
        $fieldshow = array('0', '0', "1", "1", "1", "1", "1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result['fieldcustom'] = 1;
        $result['field'] = $field;

        if ($clientList['list']) {
            $result['list'] = $clientList['list'];
        } else {
            $result['list'] = array();
        }

        if (isset($clientList['allnums']) && $clientList['allnums'] != "") {
            $allnum = $clientList['allnums'];
        } else {
            $allnum = 0;
        }
        $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result, 'allnum' => $allnum);
        ajax_return($res, $request['language_type']);

    }

    //获取对应分配的学校
    function getSchoolByActivityApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        if (!$request['activity_id']) {
            $res = array('error' => '1', 'errortip' => "活动id错误", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        $Model = new \Model\Crm\ClientModel($request);
        $schooList = $Model->getSchoolByActivity($request);

        $fieldstring = array('school_id', 'school_cnname', 'school_branch', 'activity_name');
        $fieldname = array('ID', '校园名称', '校区编号', '活动名称', '活动名称');
        $fieldcustom = array('0', '1', "1", "1", "1");
        $fieldshow = array('0', '1', "1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        $data['field'] = $field;
        $data['list'] = $schooList['list'];

        if (isset($schooList['allnums']) && $schooList['allnums'] != "") {
            $allnum = $schooList['allnums'];
        } else {
            $allnum = 0;
        }

        $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $data, 'allnum' => $allnum);
        ajax_return($res, $request['language_type']);

    }

    function allotSchoolAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        if (!$request['re_school_id']) {
            $res = array('error' => '1', 'errortip' => "请选择学校", 'result' => array());
            ajax_return($res, $request['language_type']);
        }

        $Model = new \Model\Crm\ClientModel($request);

        $bools = $Model->allotSchool($request);
        if ($bools) {

            $res = array('error' => '0', 'errortip' => "分配成功", 'result' => array());
        } else {
            $res = array('error' => '1', 'errortip' => "分配失败", 'result' => array());
        }

        ajax_return($res, $request['language_type']);
    }

    //招生目标 -- >> 招生活动管理
    function sellActivityView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        //model
        $Model = new \Model\Crm\SellgoalModel($request);
        $dataList = $Model->sellActivity($request);

//        //字段JSON获取
//        $fucMarkString = "/{$this->u}/{$this->t}";
//        $FunctionModel = new \Model\Imc\FunctionModel($request);
//        $field = $FunctionModel->getFunctionField($fucMarkString);
        $field = array();
        $field[0]["fieldname"] = "activity_name";
        $field[0]["fieldstring"] = "招生活动名称";
        $field[0]["show"] = 1;
        $field[0]["custom"] = 0;

        $field[1]["fieldname"] = "activity_starttime";
        $field[1]["fieldstring"] = "活动时间";
        $field[1]["show"] = 1;
        $field[1]["custom"] = 0;

        $field[2]["fieldname"] = "clientnum";
        $field[2]["fieldstring"] = "有效名单数";
        $field[2]["show"] = 1;
        $field[2]["custom"] = 0;

        $field[3]["fieldname"] = "actnum";
        $field[3]["fieldstring"] = "活动意向客户";
        $field[3]["show"] = 1;
        $field[3]["custom"] = 0;

        $field[4]["fieldname"] = "officialnum";
        $field[4]["fieldstring"] = "转正人数";
        $field[4]["show"] = 1;
        $field[4]["custom"] = 0;

        $field[5]["fieldname"] = "percentconversion";
        $field[5]["fieldstring"] = "转化率";
        $field[5]["show"] = 1;
        $field[5]["custom"] = 0;

        $field[6]["fieldname"] = "qrcode";
        $field[6]["fieldstring"] = "学校二维码";
        $field[6]["show"] = 1;
        $field[6]["custom"] = 0;
        $field[6]["isqrcode"] = 1;
        $field[6]["isschool"] = 1;

        $field[7]["fieldname"] = "personalqrcode";
        $field[7]["fieldstring"] = "个人二维码";
        $field[7]["show"] = 1;
        $field[7]["custom"] = 0;
        $field[7]["isqrcode"] = 1;
        $field[7]["isschool"] = 0;

        $field[8]["fieldname"] = "marketer_name";
        $field[8]["fieldstring"] = "发布人";
        $field[8]["show"] = 1;
        $field[8]["custom"] = 0;

        $field[9]["fieldname"] = "activity_type";
        $field[9]["fieldstring"] = "活动来源";
        $field[9]["show"] = 1;
        $field[9]["custom"] = 0;

        $field[10]["fieldname"] = "activity_pattern";
        $field[10]["fieldstring"] = "招生活动模式";
        $field[10]["show"] = 1;
        $field[10]["custom"] = 0;

        $field[11]["fieldname"] = "qrcodeurl";
        $field[11]["fieldstring"] = "学校二维码地址";
        $field[11]["show"] = 0;
        $field[11]["custom"] = 0;

        $field[12]["fieldname"] = "personalqrcodeurl";
        $field[12]["fieldstring"] = "个人二维码地址";
        $field[12]["show"] = 0;
        $field[12]["custom"] = 0;

        $field[13]["fieldname"] = "frommedia_name";
        $field[13]["fieldstring"] = "渠道类型";
        $field[13]["show"] = 1;
        $field[13]["custom"] = 0;

        $field[14]["fieldname"] = "channel_id";
        $field[14]["fieldstring"] = "渠道明细ID";
        $field[14]["show"] = 0;
        $field[14]["custom"] = 0;

        $field[15]["fieldname"] = "channel_name";
        $field[15]["fieldstring"] = "渠道明细";
        $field[15]["show"] = 1;
        $field[15]["custom"] = 0;

        $result = array();
        if ($dataList) {
            $result["fieldcustom"] = 1;
            $result["field"] = $field;
            $result["list"] = $dataList['datalist'];
            if ($result["list"]) {
                $res = array('error' => '0', 'errortip' => '招生活动管理', 'allnum' => $dataList['count'], 'result' => $result);
            } else {
                $result["list"] = array();
                $res = array('error' => '1', 'errortip' => '暂无招生活动信息', 'allnum' => 0, 'result' => $result);
            }
        } else {
            $result["list"] = array();
            $res = array('error' => '1', 'errortip' => '暂无招生活动信息', 'allnum' => 0, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //招生券管理列表
    function enrollTicketListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Crm\ClientModel($request);

        $result = $this->Model->enrollTicketList($request);
        ajax_return($result, $request['language_type']);
    }

    //招生名单列表
    function freevoucherListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Crm\ClientModel($request);

        $result = $this->Model->freevoucherList($request);
        ajax_return($result, $request['language_type']);
    }

    //生成招生券
    function createEnrollTicketAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $this->Model = new \Model\Crm\ClientModel($request);

        $result = $this->Model->createEnrollTicketAction($request);
        $this->addCrmWorkLog($request['company_id'], $request['school_id'], $request['marketer_id'], "招生活动管理->招生券活动管理", '生成招生券', dataEncode($request));
        ajax_return($result, $request['language_type']);
    }

    //删除转正名单
    function delDonversionAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $ClientModel = new \Model\Crm\ClientModel($request);
        $ClientModel->delDonversionAction($request);
        $res = array('error' => $ClientModel->error, 'errortip' => $ClientModel->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //删除重复名单
    function delRepeatAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $res = array('error' => 1, 'errortip' => '该功能正在维护', 'result' => array());
        ajax_return($res, $request['language_type']);
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $ClientModel = new \Model\Crm\ClientModel($request);
        $ClientModel->delRepeatAction($request);

        $res = array('error' => $ClientModel->error, 'errortip' => $ClientModel->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //下载导入模板
    function getTmkImportTrackApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        if ($request['language_type'] == 'zh') {
            $result = 'https://gmcapi.kedingdang.com/importexcel/crm/校园待筛选毛名单导入模板.xlsx?v=t3';
        } else {
            $result = 'https://gmcapi.kedingdang.com/importexcel/crm/校園待篩選毛名單導入模板.xlsx?v=t3';
        }

        $res = array('error' => '0', 'errortip' => '下载导入模版成功', 'result' => $result);

        ajax_return($res, $request['language_type']);
    }
    //下载导入模板
    function getImportTrackApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        if ($request['language_type'] == 'zh') {
            $result = 'https://gmcapi.kedingdang.com/importexcel/校园有效名单导入模板.xlsx?v=t11111';
        } else {
            $result = 'https://gmcapi.kedingdang.com/importexcel/校園有效名單導入模板.xlsx?v=t22222';
        }

        $res = array('error' => '0', 'errortip' => '下载导入模版成功', 'result' => $result);

        ajax_return($res, $request['language_type']);
    }

    //导入
    function ImportTrackView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $url = $request['url'];
        if ($request['language_type'] == 'zh') {
            $ys_array = array('中文名' => 'client_cnname', '英文名' => 'client_enname', '性别' => 'client_sex', '出生年月日' => 'client_birthday', '家长姓名' => 'parenter_cnname', '手机号码' => 'client_mobile', '招生渠道明细' => 'channel_name', '接触点' => 'client_frompage', '招生来源类型' => 'client_fromtype_name','意向班种代码' => 'coursercatstr', '备注' => 'client_remark');
        } else {
            $ys_array = array('中文名' => 'client_cnname', '英文名' => 'client_enname', '性別' => 'client_sex', '出生年月日' => 'client_birthday', '家長姓名' => 'parenter_cnname', '手機號碼' => 'client_mobile', '招生渠道明細' => 'channel_name', '接觸點' => 'client_frompage', '招生來源類型' => 'client_fromtype_name','意向班種代碼' => 'coursercatstr', '備註' => 'client_remark');
        }

        $options=array(
            "ssl"=>array(
                "verify_peer"=>false,
                "verify_peer_name"=>false,
            ),
        );
        file_put_contents('analysis.xls', file_get_contents($url,false,stream_context_create($options)));


        $sqlarray = execl_to_array("analysis.xls", $ys_array);
        array_shift($sqlarray);

        $Model = new \Model\Crm\ClientModel($request);
        $res = $Model->ImportTrack($request, $sqlarray);

        $field = array();
        $k = 0;

        $field[$k]["fieldname"] = "client_cnname";
        $field[$k]["fieldstring"] = "中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "client_enname";
        $field[$k]["fieldstring"] = "英文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "client_sex";
        $field[$k]["fieldstring"] = "性别";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "client_birthday";
        $field[$k]["fieldstring"] = "出生年月日";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "parenter_cnname";
        $field[$k]["fieldstring"] = "家长姓名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "client_mobile";
        $field[$k]["fieldstring"] = "手机号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["isTypeTab"] = 1;
        $k++;

//        $field[$k]["fieldname"] = "client_source";
//        $field[$k]["fieldstring"] = "招生渠道类型";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//        $k++;

        $field[$k]["fieldname"] = "channel_name";
        $field[$k]["fieldstring"] = "招生渠道明细";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "client_frompage";
        $field[$k]["fieldstring"] = "接触点";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "client_fromtype_name";
        $field[$k]["fieldstring"] = "招生来源类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "coursercatstr";
        $field[$k]["fieldstring"] = "意向班种代码";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "client_remark";
        $field[$k]["fieldstring"] = "备注";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        if ($request['step'] == '1') {
            $field[$k]["fieldname"] = "reason";
            $field[$k]["fieldstring"] = "错误原因";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;
        }

//        if ($request['language_type'] == 'tw') {
//            //model
//            $Model = new \Model\jianfanModel($request);
//            //转义 field
//            $field = $Model->gb2312_big5(json_encode($field, JSON_UNESCAPED_UNICODE));
//            $field = json_decode($field, true);
//        }

        $result = array();
        $result["field"] = $field;
        if ($res) {
            if ($request['step'] == '0') {
                $result["list"] = $res;
            } else {
                $result["suc"] = $res['suc'];
                $result["fal"] = $res['fal'];
                $result["falarray"] = $res['falarray'];
            }

            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
            $this->addCrmWorkLog($request['company_id'], $request['school_id'], $request['marketer_id'], "招生有效名单->招生有效名单", '导入招生有效名单', dataEncode($request));
        } else {
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

//    function gettrackWrongView()
//    {
//        $list = $this->DataControl->selectClear("
//            select t.track_id,t.marketer_id,m.marketer_name from crm_client_track as t
//            left join crm_marketer as m ON m.marketer_id = t.marketer_id
//            where track_linktype='确认试听'
//        ");
//        $num = 0;
//        if($list){
//            foreach($list as  $key => $value){
//                $num++;
//                $data = array();
//                $data['marketer_name'] = $value['marketer_name'];
//                $this->DataControl->updateData('crm_client_track',"track_id='{$value['track_id']}'",$data);
//            }
//        }
//        debug($num);
//    }

    /**
     * 获取可选的推荐人
     * author: ling
     * 对应接口文档 0001
     */
    function getClientStudentApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $datawhere = "t.company_id = '{$request['company_id']}'";

        if (isset($request['keyword']) && $request['keyword'] != '') {
            $datawhere .= " and  (t.student_branch like '%{$request['keyword']}%' or t.student_cnname like '%{$request['keyword']}%' or t.student_enname like '%{$request['keyword']}%' or p.parenter_mobile  like '%{$request['keyword']}%' ) ";

        } else {
            $res = array('error' => 1, 'errortip' => '请输入关键字', 'result' => array());
            ajax_return($res);
        }
        if (isset($request['client_id']) && $request['client_id'] != '') {
            $datawhere .= " and from_client_id <> '{$request['client_id']}' ";
        }
        $sql = "select t.student_id ,t.student_branch,t.student_cnname,t.student_enname, insert(p.parenter_mobile, 4, 4, '*****') as parenter_mobile,
              (select school_cnname from  smc_student_enrolled as d ,smc_school as l WHERE d.school_id = l.school_id and d.student_id=t.student_id and d.enrolled_status > '-1' limit 0,1) as school_cnname
            from smc_student as t 
            left join smc_student_family as f ON f.student_id =t.student_id and  family_isdefault = 1
            left join smc_parenter as p ON f.parenter_id = p.parenter_id
            left join smc_student_enrolled as se on se.student_id = t.student_id
            where {$datawhere} and se.enrolled_status > '-1'
            LIMIT 0,10";
        $dataList = $this->DataControl->selectClear($sql);
        if (!$dataList) {
            $dataList = array();
        }
        $field = array();
        $k = 0;
        $field[$k]["fieldname"] = "student_cnname";
        $field[$k]["fieldstring"] = "中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "student_enname";
        $field[$k]["fieldstring"] = "英文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "student_branch";
        $field[$k]["fieldstring"] = "学员编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "parenter_mobile";
        $field[$k]["fieldstring"] = "联系方式";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result['field'] = $field;
        $result['list'] = $dataList;
        $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        ajax_return($res, $request['language_type']);
    }

    /*
     *  获取第三方外播记录 ---- 不用了，用下边的方法
     */
    function getThreeTrackBakApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Crm\ClientModel($request);
        $dataList = $Model->getThreeTrackApiBak($request);
        $result = array();
        $result['list'] = $dataList;
        $res = array('error' => 0, 'errortip' => '获取外播记录成功', 'result' => $result);
        ajax_return($res, $request['language_type']);
    }

    //获取第三方外播记录 -- 230810
    function getThreeTrackApi(){
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Crm\ClientModel($request);
        $Model->getThreeTrackApi($request);
        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $Model->result), $request['language_type']);
    }

    // 获取录音记录
    function getCallidRecordingApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Crm\ClientModel($request);
        $dataList = $Model->getCallidRecordingApi($request);
        $field = [
            "client_id" => "名单ID",
            "client_cnname" => "名单中文名",
            "client_enname" => "名单英文名",
            "client_img" => "名单头像",
            "client_age" => "名单年龄",
            "outcall_class" => "厂商 0 默认（容联七陌） 1 慧捷  2 合力  3仕决",
            "outcall_uuid" => "通话ID（唯一）",
            "outcall_playurls" => "录音地址",
            "outcall_createtime" => "通话回调记录时间",
            "outcall_duration" => "通话时长",
        ];
        $result = array();
        $result['field'] = $field;
        $result['list'] = $dataList;
        $res = array('error' => 0, 'errortip' => '获取外播记录成功', 'result' => $result);
        ajax_return($res, $request['language_type']);
    }

    //添加推荐人
    function addClientSponsorAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $data = array();
        $data['client_sponsor'] = $request['client_sponsor'];
        $data['client_stubranch'] = $request['student_branch'];
        $this->DataControl->updateData("crm_client", "client_id='{$request['client_id']}'", $data);

        $marketer_nanme = $this->DataControl->getFieldOne('crm_marketer', 'marketer_name', "marketer_id={$request['marketer_id']}");
        $data = array();
        if ($marketer_nanme) {
            $data['marketer_name'] = $marketer_nanme['marketer_name'];
        }
        $data['marketer_id'] = $request['marketer_id'];
        $data['client_id'] = $request['client_id'];
        $data['track_linktype'] = "添加推荐人";
        $data['school_id'] = $request['school_id'];
        $studentOne = $this->DataControl->selectOne("select t.student_id,t.student_cnname,t.student_enname,sl.school_cnname 
          from smc_student as t 
          left join  smc_student_enrolled as l ON t.student_id=l.student_id 
          left join smc_school as sl ON l.school_id=sl.school_id
          where t.student_branch='{$request['student_branch']}' limit 0,1 ");
        $parenterOne = $this->DataControl->selectOne("select p.parenter_mobile from smc_student_family as f,smc_parenter as p where f.parenter_id=p.parenter_id and  f.student_id='{$studentOne['student_id']}'
 and f.student_id >0 order by family_isdefault DESC limit 0,1");

        $data['track_note'] = $data['marketer_name'] . "添加推荐人," . $studentOne['student_cnname'] . "推荐人信息(" . $studentOne['student_cnname'] . $studentOne['student_enname'] . ' ' . $parenterOne['parenter_mobile'] . " " . $studentOne['school_cnanme'] . ")";
        $data['object_code'] = $request['object_code'];
        $data['track_createtime'] = time();
        $this->DataControl->insertData('crm_client_track', $data);
        $res = array('error' => 0, 'errortip' => '添加推荐人成功', 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //添加客户标签
    function addClientTagsAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        if (!$request['client_id']) {
            $res = array('error' => '1', 'errortip' => "名单id有误", 'result' => array());
            ajax_return($res,$request['language_type']);
        }
        if($request['label_list']){

            //覆盖的标签 减 1  -- 开始
            $clientOne = $this->DataControl->selectOne("select client_tag from crm_client where company_id = '{$request['company_id']}' and client_id = '{$request['client_id']}' limit 0,1 ");
            if($clientOne['client_tag']){
                $clientTag = explode(',',$clientOne['client_tag']);
                foreach ($clientTag as $clientTagvar){
                    $sql = "update crm_client_label set label_usenum=label_usenum-1 where company_id = '{$request['company_id']}' and label_name = '{$clientTagvar}' ";
                    $this->DataControl->selectClear($sql);
                }
            }
            //覆盖的标签 减 1  -- 结束

            $tagarray = json_decode(stripslashes($request['label_list']),true);
            if(is_array($tagarray)) {
                $tagstr = implode(',', $tagarray);
            }else{
                $tagstr = $request['label_list'];
            }
            $data = array();
            $data['client_tag'] = $tagstr;
            $this->DataControl->updateData("crm_client", "client_id='{$request['client_id']}'", $data);

            //添加的标签 加 1  -- 开始
            if($tagarray){
                foreach ($tagarray as $tagvar){
                    $sql = "update crm_client_label set label_usenum=label_usenum+1 where company_id = '{$request['company_id']}' and label_name = '{$tagvar}' ";
                    $this->DataControl->selectClear($sql);
                }
            }
            //添加的标签 加 1  -- 结束

            $res = array('error' => 0, 'errortip' => '添加标签成功', 'result' => array());
            ajax_return($res, $request['language_type']);
        }else{
            $res = array('error' => '1', 'errortip' => "标签数据有误", 'result' => array());
            ajax_return($res,$request['language_type']);
        }
    }

    //添加待拨打
    function addClientDialRecordAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $ClientModel = new \Model\Crm\ClientModel($request);
        $ClientModel->addClientDialRecordAction($request);

        $res = array('error' => $ClientModel->error, 'errortip' => $ClientModel->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //获取待拨打/已通话列表
    function getDialRecordView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        if (!$request['school_id']) {
            $res = array('error' => '0', 'errortip' => "学校id错误", 'result' => array());
            ajax_return($res, $request['language_type']);
        }

        if (!$request['marketer_id']) {
            $res = array('error' => '0', 'errortip' => "未检测到招生人员信息，请重新登录操作", 'result' => array());
            ajax_return($res, $request['language_type']);
        }

        $Model = new \Model\Crm\ClientModel($request);
        $dataList = $Model->getDialRecord($request);

        $result = array();
        $result['allnum'] = $dataList['allnum'];
        $result['allnums'] = $dataList['allnums'];
        if ($dataList['list']) {
            $result['list'] = $dataList['list'];
            $res = array('error' => 0, 'errortip' => '获取拨打列表成功', 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => 0, 'errortip' => '暂无拨打信息', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }


    //拨打记录管理
    function dialrecordListView()
    {
        $request = Input('get.', '', 'trim,addslashes');

        if (!$request['school_id']) {
            $res = array('error' => '0', 'errortip' => "学校id错误", 'result' => array());
            ajax_return($res, $request['language_type']);
        }

        if (!$request['marketer_id']) {
            $res = array('error' => '0', 'errortip' => "未检测到招生人员信息，请重新登录操作", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
//        $clientList = array();
        $Model = new \Model\Crm\ClientModel($request);
        $clientList = $Model->getDialrecordList($request);
        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "中文名";
        $field[$k]["fieldname"] = "client_cnname";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '0';
        $k++;
        $field[$k]["fieldstring"] = "英文名";
        $field[$k]["fieldname"] = "client_enname";
        $field[$k]["show"] = '0';
        $field[$k]["custom"] = '0';
        $k++;
        $field[$k]["fieldstring"] = "标签";
        $field[$k]["fieldname"] = "client_tag";
        $field[$k]["show"] = '0';
        $field[$k]["custom"] = '1';
        $field[$k]["istag"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "意向课程";
        $field[$k]["fieldname"] = "course_cnname";
        $field[$k]["show"] = '0';
        $field[$k]["custom"] = '1';
        $field[$k]["istag"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "记录来源";
        $field[$k]["fieldname"] = "track_from";
        $field[$k]["show"] = '0';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "性别";
        $field[$k]["fieldname"] = "client_sex";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "年龄";
        $field[$k]["fieldname"] = "client_age";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "主要联系人";
        $field[$k]["fieldname"] = "family_cnname";
        $field[$k]["show"] = '0';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "联系电话";
        $field[$k]["fieldname"] = "client_mobile";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
//        if($request['language_type'] == 'tw'){
        $field[$k]["fieldstring"] = "Email";
        $field[$k]["fieldname"] = "client_email";
        $field[$k]["show"] = '0';
        $field[$k]["custom"] = '1';
        $k++;
//        }
        $field[$k]["fieldstring"] = "渠道类型";
        $field[$k]["fieldname"] = "client_source";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "渠道明细";
        $field[$k]["fieldname"] = "channel_name";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "客户状态";
        $field[$k]["fieldname"] = "client_tracestatus_name";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "意向星级";
        $field[$k]["fieldname"] = "client_intention_level";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $field[$k]["islevel"] = 'true';
        $k++;
        $field[$k]["fieldstring"] = "联系类型";
        $field[$k]["fieldname"] = "outcall_call_type";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "拨打分机号";
        $field[$k]["fieldname"] = "outcall_caller";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "拨打人";
        $field[$k]["fieldname"] = "marketer_name";
        $field[$k]["show"] = '0';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "通话时长";
        $field[$k]["fieldname"] = "outcall_duration";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "拨打时间";
        $field[$k]["fieldname"] = "outcall_createtime";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "通话录音";
        $field[$k]["fieldname"] = "outcall_playurl";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "录音地址";
        $field[$k]["fieldname"] = "outcall_playurlurl";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';

        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['allnum'] = $clientList['allnums'];
        if ($clientList['list']) {
            $result['list'] = $clientList['list'];
            $res = array('error' => '0', 'errortip' => "获取拨打记录成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无拨打记录", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //更新电话接通状态
    function updateClientAnswerPhoneAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $ClientModel = new \Model\Crm\ClientModel($request);
        $ClientModel->updateClientAnswerPhone($request);

        $res = array('error' => $ClientModel->error, 'errortip' => $ClientModel->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //新招改为扩科
    function changeXzToKzApi(){
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Crm\ClientModel($request);
        $bools = $Model->changeXzToKzApi($request);

        if ($bools == true) {
            $res = array('error' => '0', 'errortip' => $Model->errortip, 'result' => array());
        } else {
            $res = array('error' => '1', 'errortip' => $Model->errortip, 'result' => array());
        }

        ajax_return($res, $request['language_type']);
    }

    function changeStafferApi(){
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Crm\ClientModel($request);
        $bools = $Model->changeStafferApi($request);

        if ($bools == true) {
            $res = array('error' => '0', 'errortip' => $Model->errortip, 'result' => array());
        } else {
            $res = array('error' => '1', 'errortip' => $Model->errortip, 'result' => array());
        }

        ajax_return($res, $request['language_type']);
    }

    //沟通类型明细表报表

    //结尾魔术函数
    function __destruct()
    {

    }
}
