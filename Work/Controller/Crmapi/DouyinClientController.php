<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/13
 * Time: 23:25
 */

namespace Work\Controller\Crmapi;


class DouyinClientController extends viewTpl
{
    public $u;
    public $t;
    public $c;
    public $visitType = "api";

    //预加载处理类
    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
        $this->api = $this->router->getApi();
    }

    //抖音 获取客资信息
    function getDouyinTokenApi(){
        $request = Input('get.', '', 'trim,addslashes');

        $Model = new \Model\Api\DouyinModel($request);
        $Model->getDyClientToken($request);
        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $Model->result));
    }

    //抖音 获取客资信息
    function getDouyinLeadsApi(){
        $request = Input('get.', '', 'trim,addslashes');

        $Model = new \Model\Api\DouyinModel($request);
        if($request['type'] == '1'){
            //差找12小时前的  12 小时的数据
            $start_time =date("Y-m-d H:i:s",time()-86400); //
            $end_time = date("Y-m-d H:i:s",time()-43200); //
            $Model->getDouyinLeadsApi($request,1,100,$start_time,$end_time);
        }elseif($request['type'] == '2'){
            //差找前一天的 一天 的数据
            $start_time =date("Y-m-d H:i:s",time()-86400-86400); //
            $end_time = date("Y-m-d H:i:s",time()-86400); //
            $Model->getDouyinLeadsApi($request,1,100,$start_time,$end_time);
        }elseif($request['type'] == '3'){
            //差找前一天的 一个小时的数据
            $start_time =date("Y-m-d H:i:s",time()-86400-3605); //
            $end_time = date("Y-m-d H:i:s",time()-86400); //
            $Model->getDouyinLeadsApi($request,1,100,$start_time,$end_time);
        }else{
            $Model->getDouyinLeadsApi($request);
        }
        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $Model->result));
    }

    //抖音 店铺
    function upDouyinShopApi(){
        $request = Input('get.', '', 'trim,addslashes');

        $Model = new \Model\Api\DouyinModel($request);
        $Model->upDouyinShopApi($request);
        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $Model->result));
    }

    //抖音获取 订单信息
    function getDouyinOrderApi(){
        $request = Input('get.', '', 'trim,addslashes');

        $Model = new \Model\Api\DouyinModel($request);
        if($request['type'] == '1'){
            //差找12小时前的  12 小时的数据
            $start_time =time()-86400; //
            $end_time = time()-43200; //
            $Model->getDouyinOrderApi($request,1,100,$start_time,$end_time,'0');
        }elseif($request['type'] == '2'){
            //差找前一天的 一天 的数据
            $start_time =time()-86400-86400; //
            $end_time = time()-86400; //
            $Model->getDouyinOrderApi($request,1,100,$start_time,$end_time,'0');
        }else{
            $Model->getDouyinOrderApi($request);
        }
        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $Model->result));

//        $Model = new \Model\Api\DouyinModel($request);
//        $Model->getDouyinOrderApi($request);
//        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $Model->result));
    }

    //抖音获取 订单信息 -- 更新
    function upDouyinOrderApi(){
        $request = Input('get.', '', 'trim,addslashes');

        if(!$request['isupdate']=='1'){
            ajax_return(array('error' => 1, 'errortip' => '抖音订单号必传', 'result' => array() ));
        }

        $Model = new \Model\Api\DouyinModel($request);
        if($request['type'] == '1'){
            //差找12小时前的  12 小时的数据
            $start_time =time()-86400; //
            $end_time = time()-43200; //
            $Model->getDouyinOrderApi($request,1,100,$start_time,$end_time);
        }elseif($request['type'] == '2'){
            //差找前一天的 一天 的数据
            $start_time =time()-86400-86400; //
            $end_time = time()-86400; //
            $Model->getDouyinOrderApi($request,1,100,$start_time,$end_time);
        }else{
            $Model->getDouyinOrderApi($request);
        }
        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $Model->result));
    }

    //抖音获取 验券准备工作
    function upDouyinCertificatePlanApi(){
        $request = Input('get.', '', 'trim,addslashes');

        $Model = new \Model\Api\DouyinModel($request);
        $Model->upDouyinCertificatePlanApi($request);
        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $Model->result));
    }

    //抖音获取 核销 - 验券
    function getDouyinCertificateVerifyApi(){
        $request = Input('get.', '', 'trim,addslashes');

        $Model = new \Model\Api\DouyinModel($request);
        $Model->getDouyinCertificateVerifyApi($request);
        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $Model->result));
    }

    //抖音获取 核销 - 验券 -- 撤销
    function getDouyinCertificateVerifyRevokeApi(){
        $request = Input('get.', '', 'trim,addslashes');

        $Model = new \Model\Api\DouyinModel($request);
        $Model->getDouyinCertificateVerifyRevokeApi($request);
        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $Model->result));
    }

    //抖音获取 券状态查询
    function getDouyinCertificateStateApi(){
        $request = Input('get.', '', 'trim,addslashes');

        $Model = new \Model\Api\DouyinModel($request);
        $Model->getDouyinCertificateStateApi($request);
        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $Model->result));
    }

    //抖音获取 线上商品数据列表
    function getDouyinGoodOnlineApi(){
        $request = Input('get.', '', 'trim,addslashes');

        $Model = new \Model\Api\DouyinModel($request);
        $Model->getDouyinGoodOnlineApi($request);
        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $Model->result));
    }





    //------------------------ 老的账号同步客资信息   开始 ---------------------------
//    //抖音 获取客资信息 --- 老
//    function getDouyinLeadsOldApi(){
//        $request = Input('get.', '', 'trim,addslashes');
//
//        $Model = new \Model\Api\DouyinModel($request);
//        if($request['type'] == '1'){
//            //差找12小时前的  12 小时的数据
//            $start_time =date("Y-m-d H:i:s",time()-86400); //
//            $end_time = date("Y-m-d H:i:s",time()-43200); //
//            $Model->getDouyinLeadsOldApi($request,1,100,$start_time,$end_time);
//        }elseif($request['type'] == '2'){
//            //差找前一天的 一天 的数据
//            $start_time =date("Y-m-d H:i:s",time()-86400-86400); //
//            $end_time = date("Y-m-d H:i:s",time()-86400); //
//            $Model->getDouyinLeadsOldApi($request,1,100,$start_time,$end_time);
//        }else{
//            $Model->getDouyinLeadsOldApi($request);
//        }
//        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $Model->result));
//    }
    //------------------------ 老的账号同步客资信息   结束 ---------------------------





    //结尾魔术函数
    function __destruct()
    {

    }

}
