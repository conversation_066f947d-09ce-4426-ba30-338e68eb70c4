<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/13
 * Time: 23:25
 */

namespace Work\Controller\Crmapi;


use Model\Crm\CommonModel;

class PhoneCrmCountController extends viewTpl
{
    public $u;
    public $t;
    public $c;
    public $visitType = "api";
    public $DataControl;
    public $crmuserlevel;

    //预加载处理类
    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();

        //数据库操作
        $this->DataControl = new \Dbmysql();

    }

    //本地权限校验入口
    function ThisVerify($request)
    {
        if (!intval($request['marketer_id'])) {
            $res = array('error' => '1', 'errortip' => "未检测到招生人员信息，请重新登录操作", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        if (empty($request['crm_token'])) {
            $res = array('error' => '1', 'errortip' => "未传入用户Token信息", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        $paramArray = array();
        $paramArray['marketer_id'] = $request['marketer_id'];
        $paramArray['token'] = $request['crm_token'];
        if (!$this->UserLimit($paramArray)) {
            $result = array();
            $result["list"] = array();
            $result["tokeninc"] = "0";
            $res = array('error' => '1', 'errortip' => "用户token失效", 'result' => $result);
            ajax_return($res, $request['language_type']);
        }else{
            $crmlevelOne  = $this->DataControl->selectOne("select p.postbe_crmuserlevel 
            from gmc_staffer_postbe as p 
            left join crm_marketer as m On m.staffer_id  = m.staffer_id 
            where p.school_id='{$request['school_id']}' and p.school_id >0 and m.marketer_id='{$request['marketer_id']}'  ");
            $this->crmuserlevel = $crmlevelOne['postbe_crmuserlevel'];
        }
    }

    //手机端->客户统计->跟踪统计
    function trackCountView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Crm\ClientModel($request);
        $dataList = $Model->TrackCount($request);
        $res = array('error' => '0', 'errortip' => "获取跟踪统计", 'result' => $dataList);
        ajax_return($res, $request['language_type']);
    }

    //手机端->客户统计->邀约统计
    function inViteCountView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Crm\ClientModel($request);
        $dataList = $Model->InViteCount($request);
        $res = array('error' => '0', 'errortip' => "获取邀约统计", 'result' => $dataList);
        ajax_return($res, $request['language_type']);

    }

    //手机端->客户统计->转正统计
    function positiveCountView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Crm\ClientModel($request);
        $dataList = $Model->PositiveCount($request);
        $res = array('error' => '0', 'errortip' => "获取转正统计", 'result' => $dataList);
        ajax_return($res, $request['language_type']);
    }

    // 手机端->客户分析->意向星级分析
    function intentionLevelView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $request['postbe_crmuserlevel'] = $this->crmuserlevel;
        $Model = new \Model\Crm\ClientModel($request);
        $dataList = $Model->intentionLevel($request);
        $res = array('error' => '0', 'errortip' => "获取转正统计", 'result' => $dataList);
        ajax_return($res, $request['language_type']);
    }

    //手机端->客户分析->招生渠道分析
    function intetionClientChannelView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Crm\ClientModel($request);
        $dataList = $Model->intetionClientChannel($request);
        $res = array('error' => '0', 'errortip' => "招生渠道分析", 'result' => $dataList);
        ajax_return($res, $request['language_type']);
    }

    //将助教的端口token转为crmToken
    function changeToCRMTokenView()
    {
        $request = Input('get.', '', 'trim,addslashes');

        $stafferOne = $this->DataControl->getOne('smc_staffer', "staffer_id='{$request['staffer_id']}'");
        if ($stafferOne) {
            $md5tokenbar = base64_encode(md5($stafferOne["staffer_tokencode"] . date("Y-m-d")));
            if ($md5tokenbar == $request['token']) {
                $marketerOne = $this->DataControl->getFieldOne("crm_marketer", "marketer_id,marketer_tokenencrypt", "staffer_id='{$request['staffer_id']}'");
                if (!$marketerOne){
                    $marketer_data = array();
                    $marketer_data['staffer_id'] =$request['staffer_id'];
                    $marketer_data['company_id'] =$request['company_id'];
                    $marketer_data['marketer_name'] =$stafferOne['staffer_cnname'];
                    $marketer_data['marketer_mobile'] =$stafferOne['staffer_mobile'];
                    $marketer_data['marketer_img'] =$stafferOne['staffer_img'];
                    $marketer_data['marketer_createtime'] =time();
                    $marketer_id =  $this->DataControl->insertData('crm_marketer',$marketer_data);
                    $marketerOne['marketer_id'] = $marketer_id;
                }
                $param = array();
                $param['marketer_id'] = $marketerOne['marketer_id'];
                $commentModel = new CommonModel();
                $token = $commentModel->getToken($param);
                $marketerOne['marketer_tokenencrypt'] = $token;
                $res = array('error' => '0', 'errortip' => "获取CRMtoken成功", 'result' => $marketerOne);
                ajax_return($res, $request['language_type']);
            } else {
                $res = array('error' => '1', 'errortip' => "获取CRMtoken失败", 'result' => array());
                ajax_return($res, $request['language_type']);
            }
        } else {
            $res = array('error' => '1', 'errortip' => "未查询到职工信息", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
    }

    //编辑客户信息
    function editTephoneClientAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Crm\ClientModel($request);
        $bool = $Model->editTephoneClient($request);
        if ($bool) {
            $res = array('error' => '0', 'errortip' => "修改成功", 'result' => array());
            ajax_return($res, $request['language_type']);
        } else {
            $res = array('error' => '1', 'errortip' => "修改失败", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
    }

    /**
     * 获取未读数量
     * author: ling
     * 对应接口文档 0001
     */
    function getRemindNewTipsApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Crm\ClientModel($request);
        $result = $Model->getRemindNewTipsApi($request);
        $res = array('error' => '0', 'errortip' => "获取未读消息成功", 'result' => $result);
        ajax_return($res, $request['language_type']);
    }

    // 手机端招生概览
     function getTephoneCrmStaticApi(){
         $request = Input('get.', '', 'trim,addslashes');
         $this->ThisVerify($request);//验证账户
         $Model = new \Model\Crm\ClientModel($request);
         $data = $Model->getTephoneCrmStaticApi($request);
         $field = array();
         $field["intentionNum"] = "新增意向客户数";
         $field["notrack_principalNum"] = "待跟进意向客户数";
         $field["inviteClient"] = "柜询客户数";
         $field["auditionClient"] = "试听客户数";
         $field["track_clientNum"] = "已跟进客户数";
         $field["conversion_num"] = "转正数";
         $result["field"] = $field;
         $result["data"] = $data;
         $res = array('error' => '0', 'errortip' => "获取手机端招生概览", 'result' => $result);
         ajax_return($res, $request['language_type']);
     }


    /**
     * 获取班组
     * author: ling
     * 对应接口文档 0001
     */
    function getCourseTypeApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $datawhere = '1';
        if (isset($request['coursetype_isopenclass']) && $request['coursetype_isopenclass'] !== '') {
            $datawhere .= " and coursetype_isopenclass ='{$request['coursetype_isopenclass']}' ";
        }
        $this->ThisVerify($request);//验证账户
        $dataList = $this->DataControl->selectClear("select coursetype_cnname,coursetype_id from smc_code_coursetype  where company_id='{$request['company_id']}' and {$datawhere}");
        $result = array();
        $result['list'] = $dataList;
        $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $dataList);
        ajax_return($res, $request['language_type']);
    }


    /**
     * 更新提醒已读
     * author: ling
     * 对应接口文档 0001
     */
    function updateRemindReadApi(){
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
               $Model = new \Model\Crm\ClientModel($request);
       $Model->updateRemindReadApi($request);
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    /**
     * 招生转化分析
     * author: ling
     * 对应接口文档 0001
     */
    function  conversionClientAnalysisApi(){
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Crm\ClientModel($request);
        $data = $Model->conversionClientAnalysisApi($request);
        $result = array();
        $result['data'] =$data;
        $res = array('error' => 0, 'errortip' => '获取招生转化成功', 'result' =>$result);
        ajax_return($res, $request['language_type']);
    }



}