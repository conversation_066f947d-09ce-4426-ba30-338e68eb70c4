<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/13
 * Time: 23:25
 */

namespace Work\Controller\Crmapi;


class DdMessageController extends viewTpl
{
    public $u;
    public $t;
    public $c;
    public $visitType = "api";

    //预加载处理类
    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
        $this->api = $this->router->getApi();
    }


    //通知消息类 -- 发送Markdown消息
    function sendNoticeMarkDownView(){
        $request = Input('post.', '', 'trim,addslashes');

        $Model = new \Model\Crm\DdMessageModel($request);
        $Model->sendNoticeMarkDown($request);
        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $Model->result));
    }

    //通知消息类 -- 发送 卡片 消息
    function sendNoticeCardView(){
        $request = Input('post.', '', 'trim,addslashes');

        $Model = new \Model\Crm\DdMessageModel($request);
        $Model->sendNoticeCard($request);
        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $Model->result));
    }

    //结尾魔术函数
    function __destruct()
    {

    }

}
