<?php
/**
 * 跨界活动-后台管理
 * 须登录
 */

namespace Work\Controller\Crmapi;


class TransboundaryController extends viewTpl
{
    public $u;
    public $t;
    public $c;
    public $visitType = "api";
    public $DataControl;
    public $company_isassist = 0;

    //预加载处理类
    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();

        //数据库操作
        $this->DataControl = new \Dbmysql();

    }
    //本地权限校验入口
    function ThisVerify($request)
    {

        if (!intval($request['marketer_id'])) {
            $res = array('error' => '1', 'errortip' => "未检测到招生人员信息，请重新登录操作", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        //叮当助教CRM
        if (isset($request['crm_token']) && $request['crm_token'] !== '') {
            $request['token'] = $request['crm_token'];
        }
        if (isset($request['company_id']) && $request['company_id'] !== '') {
            $companyOne = $this->DataControl->getFieldOne("gmc_company", "company_isassist", "company_id='{$request['company_id']}'");
            $this->company_isassist = $companyOne['company_isassist'];
        }
        if (empty($request['token'])) {
            $res = array('error' => '1', 'errortip' => "未传入用户Token信息", 'result' => array());
            ajax_return($res, $request['language_type']);
        }

        $paramArray = array();
        $paramArray['marketer_id'] = $request['marketer_id'];
        $paramArray['token'] = $request['token'];
        if (!$this->UserLimit($paramArray)) {
            $result = array();
            $result["list"] = array();
            $result["tokeninc"] = "0";
            $res = array('error' => '1', 'errortip' => "用户token失效", 'result' => $result);
            ajax_return($res, $request['language_type']);
        }

    }

    /**
     * 添加跨界活动
     */
    public function addActivityAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);
        $TransboundaryModel = new \Model\Crm\TransboundaryModel();
        $TransboundaryModel ->addActivity($request);
        $field = array();
        $result = array();
        $result['field'] = $field;
        $result['data'] = $TransboundaryModel->result;

        ajax_return(array('error' => $TransboundaryModel->error, 'errortip' => $TransboundaryModel->errortip, 'result' => $result));

    }

    /**
     * 编辑跨界活动
     */
    public function editActivityAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);
        $TransboundaryModel = new \Model\Crm\TransboundaryModel();
        $TransboundaryModel ->editActivity($request);
        $field = array();
        $result = array();
        $result['field'] = $field;
        $result['data'] = $TransboundaryModel->result;

        ajax_return(array('error' => $TransboundaryModel->error, 'errortip' => $TransboundaryModel->errortip, 'result' => $result));

    }

    /**
     * 跨界活动列表
     */
    public function activityListView()
    {
        $request = Input('GET.','','trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Crm\TransboundaryModel();
        $Model->activityList($request);
        $field = array();

        $k=0;
        $field[$k]["fieldstring"] = "activity_name";
        $field[$k]["fieldname"] = "活动名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "frommedia_name";
        $field[$k]["fieldname"] = "渠道类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "channel_name";
        $field[$k]["fieldname"] = "渠道明细";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "activity_contract";
        $field[$k]["fieldname"] = "联系人";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "business_mobile";
        $field[$k]["fieldname"] = "手机号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "activity_validusernum";
        $field[$k]["fieldname"] = "有效名单数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "activity_date";
        $field[$k]["fieldname"] = "活动时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "activity_createdate";
        $field[$k]["fieldname"] = "创建时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "business_qrcode";
        $field[$k]["fieldname"] = "商家二维码";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["isqrcode"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "activity_qrcode";
        $field[$k]["fieldname"] = "活动二维码";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["isqrcode"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "activity_statusname";
        $field[$k]["fieldname"] = "活动状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "activity_amounttypename";
        $field[$k]["fieldname"] = "中奖类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "activity_amountrange";
        $field[$k]["fieldname"] = "中奖金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "activity_amount";
        $field[$k]["fieldname"] = "活动预算";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "activity_paymentamount";
        $field[$k]["fieldname"] = "已消耗金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "activity_leftamount";
        $field[$k]["fieldname"] = "剩余金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "activity_settleamount";
        $field[$k]["fieldname"] = "已结算金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();

        $result["field"] = $field;
        $result["data"] = $Model->result;

        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $result));
    }

    /**
     * 活动参与名单
     */
    public function activityRecordListView()
    {
        $request = Input('GET.','','trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Crm\TransboundaryModel();
        $Model->activityRecordList($request);
        $field = array();
        $k=0;
        $field[$k]["fieldstring"] = "activity_name";
        $field[$k]["fieldname"] = "活动名称";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "user_name";
        $field[$k]["fieldname"] = "姓名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "user_sex";
        $field[$k]["fieldname"] = "性别";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "user_age";
        $field[$k]["fieldname"] = "年龄";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "user_mobile";
        $field[$k]["fieldname"] = "手机号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "record_createdate";
        $field[$k]["fieldname"] = "创建时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "record_winningamount";
        $field[$k]["fieldname"] = "中奖金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "record_settlementstatus_name";
        $field[$k]["fieldname"] = "是否结算";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();

        $result["field"] = $field;
        $result["data"] = $Model->result;

        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $result));

    }

    /**
     * 批量结算
     */
    public function batchSettlementAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);
        $TransboundaryModel = new \Model\Crm\TransboundaryModel();
        $TransboundaryModel ->batchSettlement($request);
        $field = array();
        $result = array();
        $result['field'] = $field;
        $result['data'] = $TransboundaryModel->result;

        ajax_return(array('error' => $TransboundaryModel->error, 'errortip' => $TransboundaryModel->errortip, 'result' => $result));

    }

    /**
     * 结束活动
     */
    public function closeActivityAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);
        $TransboundaryModel = new \Model\Crm\TransboundaryModel();
        $TransboundaryModel->closeActivity($request);

        if($TransboundaryModel->error == 0){
            $mistxt = "尊敬的商户您好，您参与的{$TransboundaryModel->result['activity_name']}已经提前结束，请及时下架店内的红包抽奖二维码";
            $this->Sendmisgo($TransboundaryModel->result['business_mobile'], $mistxt, '跨界活动', rand(111111, 999999), $request['company_id']);
        }
        ajax_return(array('error' => $TransboundaryModel->error, 'errortip' => $TransboundaryModel->errortip, 'result' => $TransboundaryModel->result));

    }

    /**
     * 删除活动
     */
    public function deleteActivityAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);
        $TransboundaryModel = new \Model\Crm\TransboundaryModel();
        $TransboundaryModel ->deleteActivity($request);
        $field = array();
        $result = array();
        $result['field'] = $field;
        $result['data'] = $TransboundaryModel->result;

        ajax_return(array('error' => $TransboundaryModel->error, 'errortip' => $TransboundaryModel->errortip, 'result' => $result));
    }


}