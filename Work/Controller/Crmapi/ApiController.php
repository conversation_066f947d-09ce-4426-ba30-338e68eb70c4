<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/13
 * Time: 23:25
 */

namespace Work\Controller\Crmapi;


class ApiController extends viewTpl
{
    public $u;
    public $t;
    public $c;
    public $visitType = "api";
    //课叮铛公众号
    const appId = 'wx35cebda0ba68f126';
    const appSecret = 'bca6ef71a0dd7cb5f0b6850d8bf05bed';
    //课叮铛小程序
    const XcxappId = 'wx550be80d3f68edb1';
    const XcxappSecret = '049263d2b71d977bc8186179dbbdd795';
    //吉的堡企微小程序
    const JdbXcxappId = 'wx5145dddf2a1667eb';
    const JdbXcxappSecret = '2065151667178bde1f85b88bbf017392';

    const nonceStr = 'kidcastle';
    static $wxToken = 'https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=%s&secret=%s';
    static $wxTicket = 'https://api.weixin.qq.com/cgi-bin/ticket/getticket?access_token=%s&type=jsapi';
    static $wxSig = 'jsapi_ticket=%s&noncestr=%s&timestamp=%s&url=%s';
    //容联 七陌
    const ACCOUNTID = "N00000053984";
    const APISecret = "40f2b520-8165-11eb-a875-4b9f1970e55c";
    const HOST = "https://apis.7moor.com";

    //预加载处理类
    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
        $this->api = $this->router->getApi();
    }

    //微信授权信息获取 -- 97吉的堡企微小程序
    function JdbWxAuthorizationView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $paramarray = array(
            'appid' => self::JdbXcxappId,
            'secret' => self::JdbXcxappSecret,
            'js_code' => $request['code'],
            'grant_type' => "authorization_code"
        );

        $getBakurl = request_by_curl("https://api.weixin.qq.com/sns/jscode2session", dataEncode($paramarray), "GET");
        $json_play = new \Webjson();
        $dataarray = $json_play->decode($getBakurl, "1");
        $result["error"] = "0";
        $result["errortip"] = '用户信息存在!';
        $result["result"] = $dataarray;
        ajax_return($result);
    }
    //微信授权信息获取 -- 97吉的堡企微小程序
    function JdbWxAuthorMobileView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        //sessionKey/encryptedData/iv参数均从url中获取，并赋给相应变量
        $appid = self::JdbXcxappId;
        $sessionKey = $request['session_key'];
        $encryptedData = $request['encryptedData'];
        $iv = $request['iv'];
        $pc = new \WXBizDataCrypt($appid, $sessionKey);
        $errCode = $pc->decryptData($encryptedData, $iv, $data);
        if ($errCode == 0) {
            $res = array('error' => 0, 'errortip' => "用户信息获取正确!", "listjson" => $data, "tokeninc" => "1");
            ajax_return($res, $request['language_type']);
        } else {
            $res = array('error' => 1, 'errortip' => "用户信息获取失败，{$errCode}!", "listjson" => $errCode, "tokeninc" => "1");
            ajax_return($res, $request['language_type']);
        }
    }

    //点评授权信息获取
    //https://e.dianping.com/dz-open/merchant/auth?app_key=a4181ad88e60a9a2&state=teststate&redirect_url=https://crmapi.kedingdang.com/Api/dpingAuthcode
    function dpingAuthcodeView(){
        $request = Input('get.', '', 'trim,addslashes');
        $DpingModel = new \Model\Api\DpingModel();
        $result = $DpingModel->Authcode($request['auth_code']);
        if ($result) {
            $res = array('error' => 0, 'errortip' => "授权信息记录成功!", "result" => array());
            ajax_return($res, $request['language_type']);
        } else {
            $res = array('error' => 0, 'errortip' => $DpingModel->errortip, "result" => array());
            ajax_return($res, $request['language_type']);
        }
    }

    function dpingRefreshtokenView(){
        $request = Input('get.', '', 'trim,addslashes');
        $DpingModel = new \Model\Api\DpingModel();
        $result = $DpingModel->Refreshtoken($request['company_id']);
        if ($result) {
            $res = array('error' => 0, 'errortip' => "授权刷新记录成功!", "result" => array());
            ajax_return($res, $request['language_type']);
        } else {
            $res = array('error' => 0, 'errortip' => $DpingModel->errortip, "result" => array());
            ajax_return($res, $request['language_type']);
        }
    }
    //获取功能清单
    //https://crmapi.kedingdang.com/Api/dpingSessionquery?company_id=8888
    function dpingSessionqueryView(){
        $request = Input('get.', '', 'trim,addslashes');
        $DpingModel = new \Model\Api\DpingModel();
        $result = $DpingModel->Sessionquery($request['company_id']);
        if ($result) {
            $res = array('error' => 0, 'errortip' => "获取授权功能信息成功!", "result" => $result);
            ajax_return($res, $request['language_type']);
        } else {
            $res = array('error' => 0, 'errortip' => $DpingModel->errortip, "result" => array());
            ajax_return($res, $request['language_type']);
        }
    }
    //获取功能店铺接口
    //https://crmapi.kedingdang.com/Api/dpingSessionscope?company_id=8888
    function dpingSessionscopeView(){
        $request = Input('get.', '', 'trim,addslashes');
        $DpingModel = new \Model\Api\DpingModel();
        $result = $DpingModel->Sessionscope($request['company_id'],$request['offset']);
        if ($result) {
            $res = array('error' => 0, 'errortip' => "获取适用店铺信息成功!", "result" => $result);
            ajax_return($res, $request['language_type']);
        } else {
            $res = array('error' => 0, 'errortip' => $DpingModel->errortip, "result" => array());
            ajax_return($res, $request['language_type']);
        }
    }

    function dpingQueryorderView(){
        $request = Input('get.', '', 'trim,addslashes');
        $DpingModel = new \Model\Api\DpingModel();
        $result = $DpingModel->Queryorder($request['company_id'],1);
        if ($result) {
            if($result >= 50){
                $result = $DpingModel->Queryorder($request['company_id'],2);
                if($result >= 50){
                    $result = $DpingModel->Queryorder($request['company_id'],3);
                }
            }
            $res = array('error' => 0, 'errortip' => "查询订单信息成功!", "result" => $result);
            ajax_return($res, $request['language_type']);
        } else {
            $res = array('error' => 0, 'errortip' => $DpingModel->errortip, "result" => array());
            ajax_return($res, $request['language_type']);
        }
    }

    //历史客资处理
    function dpingLeadsView(){
        $request = Input('get.', '', 'trim,addslashes');
        $DpingModel = new \Model\Api\DpingModel();
        $result = $DpingModel->dpingLeads($request['company_id']);
        if ($result) {
            $res = array('error' => 0, 'errortip' => "客资处理成功!", "result" => $result);
            ajax_return($res, $request['language_type']);
        } else {
            $res = array('error' => 0, 'errortip' => $DpingModel->errortip, "result" => array());
            ajax_return($res, $request['language_type']);
        }
    }
    //获取微信反馈数据
    public function getWeixinToken($refresh = 0)
    {
        if ($refresh == 0) {
            $token = $this->DataControl->getFieldOne("crm_weixin_token", "token_failuretime,token_string", "token_type = '1' AND token_site = '0'", "order by token_failuretime DESC limit 0,1");
            if ($token && $token['token_failuretime'] > time()) {
                return $token['token_string'];
            } else {
                $paramarray = array(
                    'appid' => self::appId,
                    'secret' => self::appSecret,
                    'grant_type' => "client_credential"
                );
                $getBakurl = request_by_curl("https://api.weixin.qq.com/cgi-bin/token", dataEncode($paramarray), "GET");
                $json_play = new \Webjson();
                $cardarray = $json_play->decode($getBakurl, "1");
                $data = array();
                $data['token_site'] = '0';
                $data['token_type'] = '1';
                $data['token_string'] = $cardarray['access_token'];
                $data['token_failuretime'] = time() + $cardarray['expires_in'];
                $this->DataControl->insertData("crm_weixin_token", $data);
                return $cardarray['access_token'];
            }
        } else {
            $paramarray = array(
                'appid' => self::appId,
                'secret' => self::appSecret,
                'grant_type' => "client_credential"
            );
            $getBakurl = request_by_curl("https://api.weixin.qq.com/cgi-bin/token", dataEncode($paramarray), "GET");
            $json_play = new \Webjson();
            $cardarray = $json_play->decode($getBakurl, "1");
            $data = array();
            $data['token_site'] = '0';
            $data['token_type'] = '1';
            $data['token_string'] = $cardarray['access_token'];
            $data['token_failuretime'] = time() + $cardarray['expires_in'];
            $this->DataControl->insertData("crm_weixin_token", $data);
            return $cardarray['access_token'];
        }
    }
    /*
     * 获取微信签名
     *
     */
    public function getSigView()
    {
        $result = array();
        $timestamp = time();
        $ticket = $this->getTicketView();
        $url = $_POST['url'];

        $s = sha1(sprintf(self::$wxSig, $ticket['ticket'], self::nonceStr, $timestamp, $url));
        $result['signature'] = $s;
        $result['nonceStr'] = self::nonceStr;
        $result['timestamp'] = $timestamp;
        $result['appId'] = self::appId;
        $result['token'] = $ticket['token'];
        echo json_encode($result);
    }
    /*
     * 获取微信ticket
     */
    public function getTicketView()
    {
        $result = array();
        $at = $this->getWeixinToken();

        $tokenOne = $this->DataControl->getFieldOne("crm_weixin_token", "token_failuretime,token_string", "token_type = '2' AND token_site = '0'", "order by token_failuretime DESC limit 0,1");
        if ($tokenOne && $tokenOne['token_failuretime'] > time()) {
            $result['token'] = $at;
            $result['ticket'] = $tokenOne['token_string'];
            return $result;
        } else {
            $tktUrl = sprintf(self::$wxTicket, $at);
            $ticketOne = json_decode(file_get_contents($tktUrl), true);
            if (isset($ticketOne['errcode']) && $ticketOne['errcode'] !== 0) {
                $at = $this->getWeixinToken(1);
                $tktUrl = sprintf(self::$wxTicket, $at);
                $ticketOne = json_decode(file_get_contents($tktUrl), true);
                $data = array();
                $data['token_site'] = '0';
                $data['token_type'] = '2';
                $data['token_string'] = $ticketOne['ticket'];
                $data['token_failuretime'] = time() + $ticketOne['expires_in'];
                $this->DataControl->insertData("crm_weixin_token", $data);
            } else {
                $data = array();
                $data['token_site'] = '0';
                $data['token_type'] = '2';
                $data['token_string'] = $ticketOne['ticket'];
                $data['token_failuretime'] = time() + $ticketOne['expires_in'];
                $this->DataControl->insertData("crm_weixin_token", $data);
            }

            /**/
            $result['token'] = $at;
            $result['ticket'] = $ticketOne['ticket'];
            return $result;
        }
    }
    //课叮铛小程序token同步  园务部分同步到校务
    function getTicketWxTokenView(){
        $request = Input('post.', '', 'trim,addslashes');
        $tokenOne = $this->DataControl->getFieldOne("crm_weixin_token", "token_failuretime,token_string"
            , "token_type = '2' AND token_site = '0'", "order by token_failuretime DESC limit 0,1");
        $data = array();
        $data['token_site'] = '0';
        $data['token_type'] = '2';
        $data['token_string'] = $tokenOne['token_string'];
        $data['token_failuretime'] = $tokenOne['token_failuretime'];

        $res = array('error' => '1', 'errortip' => "获取成功", 'result' => $data);
        ajax_return($res, $request['language_type']);
    }

    /**
     * 微信授权信息获取 -- 97
     * 20191010 添加
     **/
    function wxAuthorizationView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $paramarray = array(
            'appid' => self::XcxappId,
            'secret' => self::XcxappSecret,
            'js_code' => $request['code'],
            'grant_type' => "authorization_code"
        );

        $getBakurl = request_by_curl("https://api.weixin.qq.com/sns/jscode2session", dataEncode($paramarray), "GET");
        $json_play = new \Webjson();
        $dataarray = $json_play->decode($getBakurl, "1");
        $result["error"] = "0";
        $result["errortip"] = '用户信息存在!';
        $result["result"] = $dataarray;
        ajax_return($result);
    }
    /**
     * 微信授权信息获取 -- 97
     * 20191010 添加
     **/
    function wxAuthorMobileView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        /**
         * sessionKey/encryptedData/iv参数均从url中获取，并赋给相应变量
         */
        $appid = self::XcxappId;
        $sessionKey = $request['session_key'];
        $encryptedData = $request['encryptedData'];
        $iv = $request['iv'];
        $pc = new \WXBizDataCrypt($appid, $sessionKey);
        $errCode = $pc->decryptData($encryptedData, $iv, $data);
        if ($errCode == 0) {
            $res = array('error' => 0, 'errortip' => "用户信息获取正确!", "listjson" => $data, "tokeninc" => "1");
            ajax_return($res, $request['language_type']);
        } else {
            $res = array('error' => 1, 'errortip' => "用户信息获取失败，{$errCode}!", "listjson" => $errCode, "tokeninc" => "1");
            ajax_return($res, $request['language_type']);
        }
    }
    /**
     * 微信小程序二维码 -- 97 ( 2021 年初  幸运转盘二维码生成 和 红包抽奖 )
     * 20191010 添加
     **/
    function getZhuanbanQRcodeView()
    {
        header("Content-type:text/html;charset=utf-8");
        $request = Input('get.', '', 'trim,addslashes');

        //如果使用了缓存的值，二维码刷新不显示  所有做的实时请求
        $tokenOne = $this->DataControl->getFieldOne("crm_weixin_token", "token_id,token_failuretime,token_string"
            , "token_type = '1' AND token_site = '2'", "order by token_failuretime DESC limit 0,1");

        if ($tokenOne && $tokenOne['token_failuretime'] > time()) {
            $access_token = $tokenOne['token_string'];
        } else {
            $paramarray = array(
                'appid' => self::XcxappId,
                'secret' => self::XcxappSecret,
                'grant_type' => "client_credential"
            );
            $getBakurl = request_by_curl("https://api.weixin.qq.com/cgi-bin/token", dataEncode($paramarray), "GET");
            $json_play = new \Webjson();
            $dataArray = $json_play->decode($getBakurl, "1");
            $data = array();
            $data['token_site'] = '2';
            $data['token_type'] = '1';
            $data['token_string'] = $dataArray['access_token'];
            $data['token_failuretime'] = time() + $dataArray['expires_in'] - 60;
            $this->DataControl->insertData("crm_weixin_token", $data);
            $access_token = $dataArray['access_token'];
        }

        $qcode = "https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token={$access_token}";
        $scene = "company_id=8888";
        $pageStr = "pages/Luckdraw/main";
        $param = json_encode(array("scene" => $scene,"page" => $pageStr,"width" => '800'));

        //POST参数
        $result = $this->httpRequest($qcode, $param, "POST");
//        print_r($result);die;
        //生成图片 -- 加上头部 header
        $base64 = base64_encode($result);

        $strImg = "data:image/png" . ";base64," . $base64;
        echo "<image src= $strImg ></image>";

    }
    /**
     * 微信小程序二维码 -- 97
     * 20191010 添加
     **/
    function getQRcodeView()
    {
        header("Content-type:text/html;charset=utf-8");
        $request = Input('get.', '', 'trim,addslashes');

        if ($_SERVER['SERVER_NAME'] != 'crmapi.kedingdang.com'  &&  $_SERVER['SERVER_NAME'] != 'gmcapi.kedingdang.com') {//优化测试服小程序访问线上的 token
            $tokenOnestr = request_by_curl("https://crmapi.kedingdang.com/Api/getNewWxToken",'',"POST",array());
            $tokenOne = json_decode($tokenOnestr,true);
            $tokenOne = $tokenOne['result'];
        } else {
            $tokenOne = $this->DataControl->getFieldOne("crm_weixin_token", "token_failuretime,token_string"
                , "token_type = '1' AND token_site = '2'", "order by token_failuretime DESC limit 0,1");
        }
        if ($tokenOne && $tokenOne['token_failuretime'] > time()) {
            $access_token = $tokenOne['token_string'];
        } else {
            $paramarray = array(
                'appid' => self::XcxappId,
                'secret' => self::XcxappSecret,
                'grant_type' => "client_credential"
            );
            $getBakurl = request_by_curl("https://api.weixin.qq.com/cgi-bin/token", dataEncode($paramarray), "GET");
            $json_play = new \Webjson();
            $dataArray = $json_play->decode($getBakurl, "1");
            $data = array();
            $data['token_site'] = '2';
            $data['token_type'] = '1';
            $data['token_string'] = $dataArray['access_token'];
            $data['token_failuretime'] = time() + $dataArray['expires_in'] - 60;
            $this->DataControl->insertData("crm_weixin_token", $data);
            $access_token = $dataArray['access_token'];
        }

        //构建请求二维码参数
        //path是扫描二维码跳转的小程序路径，可以带参数?id=xxx
        //width是二维码宽度
        $qcode = "https://api.weixin.qq.com/cgi-bin/wxaapp/createwxaqrcode?access_token={$access_token}";
        $pathStr = "pages/share/main?activity_id=" . $request['activity_id'] . "&school_id=" . $request['school_id'] . "&marketer_id=" . $request['marketer_id'] . "&promotion_id=" . $request['promotion_id'];
        $param = json_encode(array("path" => $pathStr, "width" => 1500));
        //POST参数
        $result = $this->httpRequest($qcode, $param, "POST");
        //生成图片 -- 加上头部 header
        $base64 = base64_encode($result);
// //        echo $base64;
//        $strImg = "data:image/png" . ";base64," . $base64;
//        echo "<image src= $strImg width='180px' height='200px'></image>";


        $ComOne = $this->DataControl->selectOne("select c.company_minlogo from crm_sell_activity as a,gmc_company as c
WHERE a.company_id = c.company_id AND a.activity_id = '{$request['activity_id']}' limit 0,1 ");

        if($ComOne['company_minlogo']) {
            $avatarUrl = $ComOne['company_minlogo'];//用户头像url
            //用户头像图片变圆形
            $avatar = file_get_contents($avatarUrl);
            $logo = $this->yuanImg($avatar);//返回的是图片数据流

            //二维码与头像结合
            $sharePic = $this->qrcodeWithLogo($result, $logo);

            //这里为了看效果，直接输出图片了
            if($request['iswidth']){
                echo "<image src='data:image/png;base64," . base64_encode($sharePic) . "' style='width:{$request['iswidth']}'>";
            }else{
                echo "<image src='data:image/png;base64," . base64_encode($sharePic) . "' style='width: 180px; height: 200px;'>";
            }
        }else{
            $strImg = "data:image/png" . ";base64," . $base64;
            if($request['iswidth']){
                echo "<image src= $strImg style='width:{$request['iswidth']};'></image>";
            }else{
                echo "<image src= $strImg style='width: 180px; height: 200px;'></image>";
            }
        }
    }
    /**
     * 微信小程序二维码 -- 97
     * 20191010 添加
     **/
    function getWXQRcodeApi()
    {
        header("Content-type:text/html;charset=utf-8");
        $request = Input('get.', '', 'trim,addslashes');

        $tokenOne = $this->DataControl->getFieldOne("crm_weixin_token", "token_failuretime,token_string"
            , "token_type = '1' AND token_site = '2'", "order by token_failuretime DESC limit 0,1");
        if ($tokenOne && $tokenOne['token_failuretime'] > time()) {
            $access_token = $tokenOne['token_string'];
        } else {
            $paramarray = array(
                'appid' => self::XcxappId,
                'secret' => self::XcxappSecret,
                'grant_type' => "client_credential"
            );
            $getBakurl = request_by_curl("https://api.weixin.qq.com/cgi-bin/token", dataEncode($paramarray), "GET");
            $json_play = new \Webjson();
            $dataArray = $json_play->decode($getBakurl, "1");
            $data = array();
            $data['token_site'] = '2';
            $data['token_type'] = '1';
            $data['token_string'] = $dataArray['access_token'];
            $data['token_failuretime'] = time() + $dataArray['expires_in'] - 60;
            $this->DataControl->insertData("crm_weixin_token", $data);
            $access_token = $dataArray['access_token'];
        }

        //构建请求二维码参数
        //path是扫描二维码跳转的小程序路径，可以带参数?id=xxx
        //width是二维码宽度
        $qcode = "https://api.weixin.qq.com/cgi-bin/wxaapp/createwxaqrcode?access_token={$access_token}";
        $pathStr = "pages/transboundary/customer/main?company_id=".$request['company_id']."&school_id=".$request['school_id']."&activity_id=".$request['activity_id'];
        $param = json_encode(array("path" => $pathStr, "width" => 1500));
        //POST参数
        $result = $this->httpRequest($qcode, $param, "POST");
        //生成图片 -- 加上头部 header
        $base64 = base64_encode($result);

        $strImg = "data:image/png" . ";base64," . $base64;
        echo "<image src= $strImg width='360px' height='380px'></image>";
    }
    /**
     * 微信小程序二维码 -- 97
     * 20191010 添加
     **/
    function getWXQRcodesApi()
    {
        header("Content-type:text/html;charset=utf-8");
        $request = Input('get.', '', 'trim,addslashes');

        $tokenOne = $this->DataControl->getFieldOne("crm_weixin_token", "token_failuretime,token_string"
            , "token_type = '1' AND token_site = '2'", "order by token_failuretime DESC limit 0,1");
        if ($tokenOne && $tokenOne['token_failuretime'] > time()) {
            $access_token = $tokenOne['token_string'];
        } else {
            $paramarray = array(
                'appid' => self::XcxappId,
                'secret' => self::XcxappSecret,
                'grant_type' => "client_credential"
            );
            $getBakurl = request_by_curl("https://api.weixin.qq.com/cgi-bin/token", dataEncode($paramarray), "GET");
            $json_play = new \Webjson();
            $dataArray = $json_play->decode($getBakurl, "1");
            $data = array();
            $data['token_site'] = '2';
            $data['token_type'] = '1';
            $data['token_string'] = $dataArray['access_token'];
            $data['token_failuretime'] = time() + $dataArray['expires_in'] - 60;
            $this->DataControl->insertData("crm_weixin_token", $data);
            $access_token = $dataArray['access_token'];
        }

        //构建请求二维码参数
        //path是扫描二维码跳转的小程序路径，可以带参数?id=xxx
        //width是二维码宽度
        $qcode = "https://api.weixin.qq.com/cgi-bin/wxaapp/createwxaqrcode?access_token={$access_token}";
        $pathStr = "pages/transboundary/business/main";
        $param = json_encode(array("path" => $pathStr, "width" => 1500));
        //POST参数
        $result = $this->httpRequest($qcode, $param, "POST");
        //生成图片 -- 加上头部 header
        $base64 = base64_encode($result);

        $strImg = "data:image/png" . ";base64," . $base64;
        echo "<image src= $strImg width='360px' height='380px'></image>";
    }
    //课叮铛小程序token同步  园务部分同步到校务
    function getNewWxTokenView(){
        $request = Input('post.', '', 'trim,addslashes');
//        $tokenOne = $this->DataControl->getFieldOne("crm_weixin_token", "token_failuretime,token_string"
//            , "token_type = '1' AND token_site = '2'", "order by token_failuretime DESC limit 0,1");
//        $data = array();
//        $data['token_site'] = '2';
//        $data['token_type'] = '1';
//        $data['token_string'] = $tokenOne['token_string'];
//        $data['token_failuretime'] = $tokenOne['token_failuretime'];
        $tokenOne = $this->DataControl->getFieldOne("crm_weixin_token", "token_failuretime,token_string"
                , "token_type = '1' AND token_site = '2'", "order by token_failuretime DESC limit 0,1");
        if ($tokenOne && $tokenOne['token_failuretime'] > time()) {
            $data = array();
            $data['token_site'] = '2';
            $data['token_type'] = '1';
            $data['token_string'] = $tokenOne['token_string'];
            $data['token_failuretime'] = $tokenOne['token_failuretime'];
        } else {
            $paramarray = array(
                'appid' => self::XcxappId,
                'secret' => self::XcxappSecret,
                'grant_type' => "client_credential"
            );
            $getBakurl = request_by_curl("https://api.weixin.qq.com/cgi-bin/token", dataEncode($paramarray), "GET");
            $json_play = new \Webjson();
            $dataArray = $json_play->decode($getBakurl, "1");
            $data = array();
            $data['token_site'] = '2';
            $data['token_type'] = '1';
            $data['token_string'] = $dataArray['access_token'];
            $data['token_failuretime'] = time() + $dataArray['expires_in'] - 60;
            $this->DataControl->insertData("crm_weixin_token", $data);
        }

        $res = array('error' => '1', 'errortip' => "获取成功", 'result' => $data);
        ajax_return($res, $request['language_type']);
    }
    //课叮铛小程序token同步  园务部分同步到校务
    function addNewWxTokenView(){
        $request = Input('post.', '', 'trim,addslashes');
        $data = array();
        $data['token_site'] = '2';
        $data['token_type'] = '1';
        $data['token_string'] = $request['token_string'];
        $data['token_failuretime'] = $request['token_failuretime'];
        $tokenOne = $this->DataControl->insertData("crm_weixin_token", $data);
        if($tokenOne){
            echo 1;
            exit;
        }else{
            echo 0;
            exit;
        }
    }

//    //课叮铛公众号 token同步  园务部分同步校务
//    function getNewWxGzhTokenView(){
//        $request = Input('post.', '', 'trim,addslashes');
//        $tokenOne = $this->DataControl->getFieldOne("crm_weixin_token", "token_failuretime,token_string"
//            , "token_type = '1' AND token_site = '0'", "order by token_failuretime DESC limit 0,1");
//        $data = array();
//        $data['token_site'] = '0';
//        $data['token_type'] = '1';
//        $data['token_string'] = $tokenOne['token_string'];
//        $data['token_failuretime'] = $tokenOne['token_failuretime'];
//
//        $res = array('error' => '1', 'errortip' => "获取成功", 'result' => $data);
//        ajax_return($res, $request['language_type']);
//    }
//    //课叮铛公众号 token同步  园务部分同步到校务
//    function addNewWxGzhTokenView(){
//        $request = Input('post.', '', 'trim,addslashes');
//        $data = array();
//        $data['token_site'] = '0';
//        $data['token_type'] = '1';
//        $data['token_string'] = $request['token_string'];
//        $data['token_failuretime'] = $request['token_failuretime'];
//        $tokenOne = $this->DataControl->insertData("crm_weixin_token", $data);
//        if($tokenOne){
//            echo 1;
//            exit;
//        }else{
//            echo 0;
//            exit;
//        }
//    }
    //把请求发送到微信服务器换取二维码 -- 微信小程序二维码 -- 97
    function httpRequest($url, $data = '', $method = 'GET')
    {
        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, 0);
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, 0);
        curl_setopt($curl, CURLOPT_USERAGENT, $_SERVER['HTTP_USER_AGENT']);
        curl_setopt($curl, CURLOPT_FOLLOWLOCATION, 1);
        curl_setopt($curl, CURLOPT_AUTOREFERER, 1);
        if ($method == 'POST') {
            curl_setopt($curl, CURLOPT_POST, 1);
            if ($data != '') {
                curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
            }
        }

        curl_setopt($curl, CURLOPT_TIMEOUT, 30);
        curl_setopt($curl, CURLOPT_HEADER, 0);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
        $result = curl_exec($curl);
        curl_close($curl);
        return $result;
    }
    /**
     * 在二维码的中间区域镶嵌图片
     * @param $QR 二维码数据流。比如file_get_contents(imageurl)返回的东东,或者微信给返回的东东
     * @param $logo 中间显示图片的数据流。比如file_get_contents(imageurl)返回的东东
     * @return  返回图片数据流
     */
    function qrcodeWithLogo($QR,$logo){
        $QR   = imagecreatefromstring ($QR);
        $logo = imagecreatefromstring ($logo);
        $QR_width    = imagesx ( $QR );//二维码图片宽度
        $QR_height   = imagesy ( $QR );//二维码图片高度
        $logo_width  = imagesx ( $logo );//logo图片宽度
        $logo_height = imagesy ( $logo );//logo图片高度
//        $logo_qr_width  = $QR_width / 2.2;//组合之后logo的宽度(占二维码的1/2.2)
        $logo_qr_width  = $QR_width / 4.8;//组合之后logo的宽度(占二维码的1/2.2)
        $scale  = $logo_width / $logo_qr_width;//logo的宽度缩放比(本身宽度/组合后的宽度)
        $logo_qr_height = $logo_height / $scale;//组合之后logo的高度
        $from_width = ($QR_width - $logo_qr_width) / 2;//组合之后logo左上角所在坐标点
        /**
         * 重新组合图片并调整大小
         * imagecopyresampled() 将一幅图像(源图象)中的一块正方形区域拷贝到另一个图像中
         */
        imagecopyresampled ( $QR, $logo, $from_width, $from_width, 0, 0, $logo_qr_width, $logo_qr_height, $logo_width, $logo_height );
        /**
         * 如果想要直接输出图片，应该先设header。header("Content-Type: image/png; charset=utf-8");
         * 并且去掉缓存区函数
         */
        //获取输出缓存，否则imagepng会把图片输出到浏览器
        ob_start();
        imagepng ( $QR );
        imagedestroy($QR);
        imagedestroy($logo);
        $contents =  ob_get_contents();
        ob_end_clean();
        return $contents;
    }
    /**
     * 剪切图片为圆形
     * @param  $picture 图片数据流 比如file_get_contents(imageurl)返回的东东
     * @return 图片数据流
     */
    function yuanImg($picture) {
        $src_img = imagecreatefromstring($picture);
        $w   = imagesx($src_img);
        $h   = imagesy($src_img);
        $w   = min($w, $h);
        $h   = $w;
        $img = imagecreatetruecolor($w, $h);
        //这一句一定要有
        imagesavealpha($img, true);
        //拾取一个完全透明的颜色,最后一个参数127为全透明
//        $bg = imagecolorallocatealpha($img, 255, 255, 255, 127);
        $bg = imagecolorallocatealpha($img, 255, 255, 255, 0);
        imagefill($img, 0, 0, $bg);
        $r   = $w / 2; //圆半径
        $y_x = $r; //圆心X坐标
        $y_y = $r; //圆心Y坐标
        for ($x = 0; $x < $w; $x++) {
            for ($y = 0; $y < $h; $y++) {
                $rgbColor = imagecolorat($src_img, $x, $y);
                if (((($x - $r) * ($x - $r) + ($y - $r) * ($y - $r)) < ($r * $r))) {
                    imagesetpixel($img, $x, $y, $rgbColor);
                }
            }
        }
        /**
         * 如果想要直接输出图片，应该先设header。header("Content-Type: image/png; charset=utf-8");
         * 并且去掉缓存区函数
         */
        //获取输出缓存，否则imagepng会把图片输出到浏览器
        ob_start();
        imagepng ( $img );
        imagedestroy($img);
        $contents =  ob_get_contents();
        ob_end_clean();
        return $contents;
    }
    //园务访客小程序二维码
    function getVisitorQRcodeView()
    {
        header("Content-type:text/html;charset=utf-8");
        $request = Input('get.', '', 'trim,addslashes');

        $tokenOne = $this->DataControl->getFieldOne("crm_weixin_token", "token_failuretime,token_string"
            , "token_type = '1' AND token_site = '2'", "order by token_failuretime DESC limit 0,1");
        if ($tokenOne && $tokenOne['token_failuretime'] > time()) {
            $access_token = $tokenOne['token_string'];
        } else {
            $paramarray = array(
                'appid' => self::XcxappId,
                'secret' => self::XcxappSecret,
                'grant_type' => "client_credential"
            );
            $getBakurl = request_by_curl("https://api.weixin.qq.com/cgi-bin/token", dataEncode($paramarray), "GET");
            $json_play = new \Webjson();
            $dataArray = $json_play->decode($getBakurl, "1");
            $data = array();
            $data['token_site'] = '2';
            $data['token_type'] = '1';
            $data['token_string'] = $dataArray['access_token'];
            $data['token_failuretime'] = time() + $dataArray['expires_in'] - 60;
            $this->DataControl->insertData("crm_weixin_token", $data);
            $access_token = $dataArray['access_token'];
        }

        //构建请求二维码参数
        //path是扫描二维码跳转的小程序路径，可以带参数?id=xxx
        //width是二维码宽度
        $qcode = "https://api.weixin.qq.com/cgi-bin/wxaapp/createwxaqrcode?access_token={$access_token}";
        //页面地址 "pages/share/main?activity_id=156&school_id=670&marketer_id=0"
        $pathStr = "pages/Visitors/index/main?company_id=" . $request['company_id'] . "&school_id=" . $request['school_id'];
        $param = json_encode(array("path" => $pathStr, "width" => 1500));

        //POST参数
        $result = $this->httpRequest($qcode, $param, "POST");

        //生成图片 -- 加上头部 header
        $base64 = base64_encode($result);
//        echo $base64;
        $strImg = "data:image/png" . ";base64," . $base64;
        echo "<image src= $strImg width='180px' height='200px'></image>";
    }

    //获取某个学校的详细信息（3.0校务系统）
    function getSchoolOneApi()
    {
        $request = Input('get.', '', 'trim,addslashes');

        //model
        $Model = new \Model\Crm\CommonModel();
        $dataList = $Model->getSchoolOneApi($request);

        $field = array();
        $field["school_id"] = "学校id";
        $field["school_branch"] = "校区编号";
        $field["school_shortname"] = "校园简称";
        $field["school_cnname"] = "校园名称称";
        $field["school_enname"] = "检索代码称";
        $field["school_phone"] = "学校联系电话";
        $field["school_address"] = "学校联系地址";
        $field["school_province"] = "省";
        $field["school_city"] = "城市";
        $field["school_area"] = "区域";

        if ($dataList) {
            $res = array('error' => '0', 'errortip' => '学校信息获取成功', 'result' => $dataList);
        } else {
            $res = array('error' => '1', 'errortip' => '学校信息获取失败', 'result' => array());
        }
        ajax_return($res, $request['language_type']);
    }

    //官网学校筛选
    function getGwSchoolApi()
    {
        $request = Input('get.', '', 'trim,addslashes');

        //model
        $Model = new \Model\Crm\CommonModel();
        $dataList = $Model->getGwSchoolApi($request);

        $field = array();
        $field["school_id"] = "学校id";
        $field["school_branch"] = "校区编号";
        $field["school_shortname"] = "校园简称";
        $field["school_cnname"] = "校园名称称";
        $field["school_enname"] = "检索代码称";
        $field["school_phone"] = "学校联系电话";
        $field["school_address"] = "学校联系地址";

        if ($dataList) {
            $res = array('error' => '0', 'errortip' => '筛选学校机构成功', 'result' => $dataList);
        } else {
            $res = array('error' => '1', 'errortip' => '筛选学校机构失败', 'result' => array());
        }
        ajax_return($res, $request['language_type']);
    }

    //官网学校筛选 -- 地址列表
    function getGwSchoolAreaApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $Model = new \Model\Crm\CommonModel();
        $dataList = $Model->getGwSchoolAreaApi($request);

        $field = array();
        $field["school_city"] = "学校地址";

        if ($dataList) {
            $res = array('error' => '0', 'errortip' => '筛选学校机构地址成功', 'result' => $dataList);
        } else {
            $res = array('error' => '1', 'errortip' => '筛选学校机构地址失败', 'result' => array());
        }
        ajax_return($res, $request['language_type']);
    }
    function getObjectApi()
    {
        $request = Input('get.', '', 'trim,addslashes');

        $dataList = $this->DataControl->selectClear("select co.object_code,co.object_name from crm_code_object  as co where 1 ");
        if($request['language_type'] == 'tw') {
            //转义 errortip
            $Model = new \Model\jianfanModel();
            $data = $Model->gb2312_big5(json_encode($dataList, JSON_UNESCAPED_UNICODE));
            $dataList = json_decode($data, true);
        }
        $result['list'] = $dataList;
        if ($dataList) {

            $res = array('error' => '0', 'errortip' => '获取沟通对象成功', 'result' => $result);
        } else {
            $res = array('error' => '0', 'errortip' => '获取沟通对象成功', 'result' => array());
        }
        ajax_return($res, $request['language_type']);

    }
    function getReceiverListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $where = " s.staffer_leave =0 and m.company_id='{$request['company_id']}' and  p.school_id ='{$request['school_id']}' and p.postbe_status =1 and postbe_iscrmuser = 1 ";
        $dataList = $this->DataControl->selectClear("SELECT m.marketer_id ,marketer_name,s.staffer_enname
			FROM crm_marketer AS m
			LEFT JOIN smc_staffer AS s  ON m.staffer_id = s.staffer_id
			LEFT JOIN gmc_staffer_postbe AS p ON p.staffer_id = m.staffer_id
			WHERE {$where} and  p.postbe_isreceptionuser = 1  group by  m.marketer_id ");

        if (!$dataList) {
            $result['list'] = array();
        } else {
            foreach ($dataList as &$value){
                $value['marketer_name'] = $value['marketer_name'].($value['staffer_enname']?'('.$value['staffer_enname'].')':'');
            }

            $result['list'] = $dataList;

        }
        $res = array('error' => '0', 'errortip' => '获取成功', 'result' => $result);

        ajax_return($res, $request['language_type']);
    }
    function getArealyCourseApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $datawhere = "c.course_inclasstype = 0 and c.company_id ='{$request['company_id']}'and cc.coursetype_isopenclass = 0";
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (c.course_branch  like '%{$request['keyword']}%' or c.course_cnname like '%{$request['keyword']}%') ";
        }

        $dataList = $this->DataControl->selectClear("select c.course_id,c.course_branch,c.course_cnname
            from smc_course as c left join smc_code_coursetype as  cc ON c.coursetype_id = cc.coursetype_id
            where {$datawhere}");
        if (!$dataList) {
            $result['list'] = array();
        } else {
            $result['list'] = $dataList;
        }
        $res = array('error' => '0', 'errortip' => '获取成功', 'result' => $result);

        ajax_return($res, $request['language_type']);
    }
    function getTransferCnameApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $tranferList = $this->DataControl->selectClear("select transfer_name,transfer_code from crm_code_transfer where company_id='{$request['company_id']}'");
        if (!$tranferList) {
            $data['list'] = array();
        } else {
            $data['list'] = $tranferList;
        }
        $res = array('error' => '0', 'errortip' => '获取成功', 'result' => $data);

        ajax_return($res, $request['language_type']);
    }
    /**待检查**/
    function getAllSchoolApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $schoolList = $this->DataControl->selectClear("select school_id,school_branch,(case when school_shortname='' then school_cnname else school_shortname end) as school_cnname from smc_school
where school_id <> '{$request['school_id']}' and company_id ='{$request['company_id']}' and  school_isclose =0");
        if (!$schoolList) {
            $data['list'] = array();
        } else {
            $data['list'] = $schoolList;
        }
        $res = array('error' => '0', 'errortip' => '获取成功', 'result' => $data);
        ajax_return($res, $request['language_type']);
    }
    function getActivityView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $activityOne = $this->DataControl->selectOne("select a.*,c.company_code,c.company_shortname,t.activitytemp_theme from crm_sell_activity as a,gmc_company as c,crm_code_activitytemp as t where c.company_id = a.company_id and a.activity_id ='{$request['activity_id']}' and a.activitytemp_id = t.activitytemp_id limit 0,1");

        $schoolOne = array();
        if($request['school_id']){
            $schoolOne = $this->DataControl->selectOne("select s.school_id,s.school_branch,s.school_shortname,s.school_province,s.school_city,s.school_area, 
                        (SELECT r.region_name FROM smc_code_region as r WHERE r.region_id = s.school_province) as school_provincename,
                        (SELECT r.region_name FROM smc_code_region as r WHERE r.region_id = s.school_city) as school_cityname,
                        (SELECT r.region_name FROM smc_code_region as r WHERE r.region_id = s.school_area) as school_areaname
                        from smc_school as s 
                        WHERE s.school_id = '{$request['school_id']}' and s.company_id = '{$activityOne['company_id']}' limit 0,1 ");
        }

        if ($request['open_type'] != 'crm') {
            if (time() < (strtotime($activityOne['activity_starttime']))) {
                $res = array('error' => '1', 'errortip' => '活动尚未开始,敬请期待！', 'result' => array(), 'startend' => '1');
                ajax_return($res, $request['language_type']);
            }
            if (time() > (strtotime($activityOne['activity_endtime']) + 86400)) {
                $res = array('error' => '1', 'errortip' => '活动已经结束！', 'result' => array(), 'startend' => '2');
                ajax_return($res, $request['language_type']);
            }
        }

        $field = array();
        $field['isfitschool'] = '是否适配了学校   0 无  1 有';
        $field['activity_id'] = '活动ID';
        $field['company_id'] = '集团ID';
        $field['company_code'] = '集团Code';
        $field['activity_type'] = '活动来源  0 学校  1 集团';
        $field['activity_fittype'] = '适用学校   0 部分适配  1 全部适配';
        $field['activity_pattern'] = '招生活动模式：0普招模式1验券模式2海报模式';
        $field['company_shortname'] = '集团简称';
        $field['activity_theme'] = '活动招生标题';
        $field['activity_starttime'] = '开始时间';
        $field['activity_endtime'] = '结束时间';
        $field['activity_img'] = '主图';
        $field['activity_shareimg'] = '微信分享小图标';
        $field['activity_rule'] = '活动规则';
        $field['activity_content'] = '活动详情';
        $field['activity_aboutus'] = '关于我们';
        $field['activity_contacttel'] = '联系方式';
        $field['activity_address'] = '活动地址';
        $field['activity_isspecial'] = '是否特殊活动';
        $field['activity_customcontent'] = '自定义详情';
        $field['activity_sharedesc'] = '吉的堡分享描述';
        $field['client_source'] = '来源渠道信息类型';
        $field['activity_ischoiceschool'] = '是否需要选择学校';
        $field['activity_ischoicecity'] = '是否开启城市区域选择  1 是   0 否';
        $field['school_branch'] = '校区编号';
        $field['school_shortname'] = '校园简称';
        $field['school_province'] = '省';
        $field['school_city'] = '市';
        $field['school_area'] = '区';
        $field['school_provincename'] = '省名';
        $field['school_cityname'] = '市名';
        $field['school_areaname'] = '区名';
        $field['act_coursecat'] = '活动适配的班种';
        $field["activity_issex"] = "是否需要选择性别";
        $field["activity_issex_must"] = "是否必填";
        $field["activity_isbirthday"] = "是否需要选择生日";
        $field["activity_isbirthday_must"] = "是否必填";
        $field["activity_isaddress"] = "是否需要选择地址";
        $field["activity_isaddress_must"] = "是否必填";
        $field["activitytemp_theme"] = "模板主题";

        if ($activityOne) {
            $actschoolOne = $this->DataControl->selectOne(" select actschool_id from crm_sell_activity_school WHERE activity_id = '{$activityOne['activity_id']}' limit 0,1 ");

            $data = array();
            $data['isfitschool'] = ($actschoolOne['actschool_id']>0)?1:0;//是否适配了学校
            $data['activity_id'] = $activityOne['activity_id'];
            $data['company_id'] = $activityOne['company_id'];
            $data['company_code'] = $activityOne['company_code'];
            $data['activity_type'] = $activityOne['activity_type'];
            $data['activity_fittype'] = $activityOne['activity_fittype'];//适用学校   0 部分适配  1 全部适配
            $data['activity_pattern'] = $activityOne['activity_pattern'];
            $data['company_shortname'] = $activityOne['company_shortname'];
            $data['activity_theme'] = $activityOne['activity_theme'];
            $data['activity_starttime'] = date("m-d", strtotime($activityOne['activity_starttime']));
            $data['activity_endtime'] = date("m-d", strtotime($activityOne['activity_endtime']));
            $data['activity_img'] = $activityOne['activity_img'];
            $data['activity_shareimg'] = $activityOne['activity_shareimg'];
            $data['activity_rule'] = nl2br($activityOne['activity_rule']);
            $data['activity_content'] = $activityOne['activity_content'];
            $data['activity_aboutus'] = $activityOne['activity_aboutus'];
            $data['activity_contacttel'] = $activityOne['activity_contacttel'];
            $data['activity_address'] = $activityOne['activity_address'];
            $data['activity_isspecial'] = $activityOne['activity_isspecial'];
            $data['activity_customcontent'] = $activityOne['activity_customcontent'];
            $data['activity_sharedesc'] = $activityOne['activity_sharedesc'];
            $data['client_source'] = $activityOne['frommedia_name'];
            $data['activity_ischoiceschool'] = $activityOne['activity_ischoiceschool'];
            $data['activity_ischoicecity'] = $activityOne['activity_ischoicecity'];

            $data['activity_issex'] = $activityOne['activity_issex'];//
            $data['activity_issex_must'] = $activityOne['activity_issex_must'];//
            $data['activity_isbirthday'] = $activityOne['activity_isbirthday'];//
            $data['activity_isbirthday_must'] = $activityOne['activity_isbirthday_must'];//
            $data['activity_isaddress'] = $activityOne['activity_isaddress'];//
            $data['activity_isaddress_must'] = $activityOne['activity_isaddress_must'];//

            $data['activitytemp_theme'] = $activityOne['activitytemp_theme'];

            $data['school_branch'] = $schoolOne['school_branch'];
            $data['school_shortname'] = $schoolOne['school_shortname'];
            $data['school_province'] = $schoolOne['school_province'];
            $data['school_city'] = $schoolOne['school_city'];
            $data['school_area'] = $schoolOne['school_area'];
            $data['school_provincename'] = $schoolOne['school_provincename'];
            $data['school_cityname'] = $schoolOne['school_cityname'];
            $data['school_areaname'] = $schoolOne['school_areaname'];

            $data['package_branch'] = $activityOne['package_branch'];

            $actcoursecatArray = $this->DataControl->selectClear("select t.coursecat_id,t.coursecat_cnname,t.coursecat_branch from crm_sell_activity_coursecat as c left join smc_code_coursecat as t ON c.coursecat_id = t.coursecat_id where c.company_id = '{$activityOne['company_id']}' and c.activity_id = '{$activityOne['activity_id']}' ");
            $data['act_coursecat'] = $actcoursecatArray?$actcoursecatArray:array();

            //判断中 地推二维码或个人二维码 的员工 姓名脱敏
            if(isset($request['marketer_id']) && $request['marketer_id'] > 1 ){
                $marketerOne = $this->DataControl->selectOne(" select marketer_id,marketer_name from crm_marketer where company_id = '{$activityOne['company_id']}' and marketer_id = '{$request['marketer_id']}' ");

                $data['now_marketer_name'] = checkComplexPass($marketerOne['marketer_name']);
            }
            if(isset($request['promotion_id']) && $request['promotion_id'] > 1 ){
                $promotionOne = $this->DataControl->selectOne(" select promotion_id,promotion_name from crm_ground_promotion where company_id = '{$activityOne['company_id']}' and promotion_id = '{$request['promotion_id']}' ");

                $data['now_promotion_name'] = checkComplexPass($promotionOne['promotion_name']);;
            }

            //20250606 $resulttwo 这组数据是为了 20250606左右课包活动 展示二维码的判断
            $resulttwo = array();
            $resulttwo['qrcode'] = '';
            $resulttwo['schoollist'] = array();
            if($activityOne['activity_type'] == '0'){//学校

                $schoolList = $this->DataControl->selectOne(" 
                    select b.school_branch,b.school_shortname,b.school_cnname 
                    from crm_sell_activity_school as a,smc_school as b 
                    WHERE a.activity_id = '{$activityOne['activity_id']}' and a.school_id = b.school_id  and b.school_isclose = '0'    
                    limit 0,1 
                    ");
                if($schoolList['school_branch']){
                    $datasome = array();
                    $datasome['xiaoqubianhao'] = $schoolList['school_branch'];
                    $datasome['qudao'] = 1;
                    $getBackurl = request_by_curl("https://eduappv2.kidcastle.com.cn/api/kddqiwei/lianxiwoerweima", dataEncode($datasome), "GET");
                    $bakData = json_decode($getBackurl, true);
                    if ($bakData['error'] == '0') {
                        $QRcode = $bakData['data']['xiaoqu'][$schoolList['school_branch']]['erweimadizhi'];
                    }else{
                        $QRcode = "https://pic.kedingdang.com/schoolmanage/202410171417x911063871.png";
                    }
                }else{
                    $QRcode = "";
                }

                $resulttwo['qrcode'] = $QRcode;
                $resulttwo['schoollist'] = $schoolList;
            }else{
                if($activityOne['activity_fittype'] == '0' ){
                    $schoolList = $this->DataControl->selectClear(" 
                    select b.school_branch,b.school_shortname,b.school_cnname
                    from crm_sell_activity_school as a,smc_school as b 
                    WHERE a.activity_id = '{$activityOne['activity_id']}' and a.school_id = b.school_id and b.school_isclose = '0'  
                    ");
                    if(count($schoolList) == '1'){
                        if($schoolList[0]['school_branch']){
                            $datasome = array();
                            $datasome['xiaoqubianhao'] = $schoolList[0]['school_branch'];
                            $datasome['qudao'] = 1;
                            $getBackurl = request_by_curl("https://eduappv2.kidcastle.com.cn/api/kddqiwei/lianxiwoerweima", dataEncode($datasome), "GET");
                            $bakData = json_decode($getBackurl, true);
                            if ($bakData['error'] == '0') {
                                $QRcode = $bakData['data']['xiaoqu'][$schoolList[0]['school_branch']]['erweimadizhi'];
                            }else{
                                $QRcode = "https://pic.kedingdang.com/schoolmanage/202410171417x911063871.png";
                            }
                        }else{
                            $QRcode = "";
                        }
                        $resulttwo['qrcode'] = $QRcode;
                        $resulttwo['schoollist'] = $schoolList[0];
                    }
                }
            }

            $res = array('error' => '0', 'errortip' => '获取成功', 'field' => $field, 'result' => $data,'resulttwo' => $resulttwo );
            ajax_return($res, $request['language_type']);
        } else {
            $res = array('error' => '1', 'errortip' => '活动不存在，请检查活动是否存在！', 'result' => array());
            ajax_return($res, $request['language_type']);
        }
    }


    //获取 某活动的学校列表
    function getActivitySchoolListView(){
        $request = Input('get.','','trim,addslashes');

        if(!$request['activity_id']){
            $res = array('error' => '1', 'errortip' => '请传入活动id！', 'result' => array());
            ajax_return($res,$request['language_type']);
        }

        $datawhere = "";

        if(isset($request['keyword']) && $request['keyword'] != ''){
            $datawhere .= " and (b.school_cnname like '%{$request['keyword']}%' or b.school_shortname like '%{$request['keyword']}%' or b.school_branch like '%{$request['keyword']}%')";
        }

        $schoolList = $this->DataControl->selectClear(" 
                    select b.school_branch,b.school_shortname,b.school_cnname,'' as false_qrcode
                    from crm_sell_activity_school as a,smc_school as b 
                    WHERE a.activity_id = '{$request['activity_id']}' and a.school_id = b.school_id and b.school_isclose = '0' {$datawhere}
                    ");

        $res = array('error' => '0', 'errortip' => '获取成功', 'result' => $schoolList?$schoolList:array() );
        ajax_return($res,$request['language_type']);
    }

    //获取某个学校的企微二维码
    function getActivitySchoolQrcodeOneView(){
        $request = Input('get.','','trim,addslashes');

        if(!$request['activity_id']){
            $res = array('error' => '1', 'errortip' => '请传入活动id！', 'result' => array());
            ajax_return($res,$request['language_type']);
        }
        if(!$request['school_branch']){
            $res = array('error' => '1', 'errortip' => '请传入学校编号！', 'result' => array());
            ajax_return($res,$request['language_type']);
        }

        $activityOne = $this->DataControl->selectOne("select a.activity_id,a.company_id  from crm_sell_activity as a where a.activity_id ='{$request['activity_id']}' limit 0,1");

        if(!$activityOne){
            $res = array('error' => '1', 'errortip' => '活动不存在！', 'result' => array());
            ajax_return($res,$request['language_type']);
        }

        $schoolList = $this->DataControl->selectOne("select school_branch,school_shortname,school_cnname from smc_school where school_branch = '{$request['school_branch']}' and school_isclose = '0' and company_id = '{$activityOne['company_id']}'   ");

        if($schoolList['school_branch']){
            $datasome = array();
            $datasome['xiaoqubianhao'] = $schoolList['school_branch'];
            $datasome['qudao'] = 1;
            $getBackurl = request_by_curl("https://eduappv2.kidcastle.com.cn/api/kddqiwei/lianxiwoerweima", dataEncode($datasome), "GET");
            $bakData = json_decode($getBackurl, true);
            if ($bakData['error'] == '0') {
                $QRcode = $bakData['data']['xiaoqu'][$schoolList['school_branch']]['erweimadizhi'];
            }else{
                $QRcode = "https://pic.kedingdang.com/schoolmanage/202410171417x911063871.png";
            }
        }else{
            $QRcode = "https://pic.kedingdang.com/schoolmanage/202410171417x911063871.png";
        }
        $result = array();
        $result['qrcode'] = $QRcode;
        $res = array('error' => '0', 'errortip' => '二维码获取成功！', 'result' => $result);
        ajax_return($res,$request['language_type']);
    }


    //活动学校适配表
    function ActivitySchoolView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        if (!isset($request['activity_id']) || $request['activity_id'] == '') {
            $res = array('error' => '1', 'errortip' => '对应活动必选', 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        if (!isset($request['adaptive']) || $request['adaptive'] == '') {
            $res = array('error' => '1', 'errortip' => '筛选类型必须选择', 'result' => array());
            ajax_return($res, $request['language_type']);
        }

        //model
        $dataList = $this->ActivitySchoolApis($request);

        $field = array();
        $field[0]["fieldname"] = "school_id";
        $field[0]["fieldstring"] = "学校id";
        $field[0]["show"] = 0;
        $field[0]["custom"] = 0;

        $field[1]["fieldname"] = "school_branch";
        $field[1]["fieldstring"] = "校区编号";
        $field[1]["show"] = 1;
        $field[1]["custom"] = 0;

        $field[2]["fieldname"] = "school_cnname";
        $field[2]["fieldstring"] = "校园名称";
        $field[2]["show"] = 1;
        $field[2]["custom"] = 0;

        $field[3]["fieldname"] = "school_enname";
        $field[3]["fieldstring"] = "检索代码";
        $field[3]["show"] = 1;
        $field[3]["custom"] = 0;

        $field[4]["fieldname"] = "district_cnname";
        $field[4]["fieldstring"] = "所在区域";
        $field[4]["show"] = 1;
        $field[4]["custom"] = 0;

        $field[5]["fieldname"] = "adaptive";
        $field[5]["fieldstring"] = "是否适配";
        $field[5]["show"] = 0;
        $field[5]["custom"] = 0;

        if ($dataList) {
            $result = array();
            $result["field"] = $field;
            if (is_array($dataList['datalist'])) {
//                $result["district"] = $dataList['district'];
                $result["data"] = $dataList['datalist'];
            } else {
                $result["data"] = array();
            }
            $res = array('error' => '0', 'errortip' => "活动修改成功", 'allnum' => $dataList['count'], 'result' => $result);
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '活动修改失败', 'result' => $result);
        }

        ajax_return($res, $request['language_type']);
    }
    //活动学校适配表
    function ActivitySchoolApis($paramArray)
    {
        $actOne = $this->DataControl->selectOne("select company_id from crm_sell_activity WHERE activity_id = '{$paramArray['activity_id']}'");
        $paramArray['company_id'] = $actOne['company_id'];
        $datawhere = "s.company_id = '{$paramArray['company_id']}'";
        //关键词
        if (isset($paramArray['keyword']) && $paramArray['keyword'] != '') {
            $datawhere .= " and (s.school_cnname like '%{$paramArray['keyword']}%' or s.school_shortname like '%{$paramArray['keyword']}%' or s.school_branch like '%{$paramArray['keyword']}%')";
        }
        //省份
        if (isset($paramArray['school_province']) && $paramArray['school_province'] != '') {
            $datawhere .= " and s.school_province = '{$paramArray['school_province']}'";
        }
        //城市
        if (isset($paramArray['school_city']) && $paramArray['school_city'] != '') {
            $datawhere .= " and s.school_city = '{$paramArray['school_city']}'";
        }
        //区
        if (isset($paramArray['school_area']) && $paramArray['school_area'] != '') {
            $datawhere .= " and s.school_area = '{$paramArray['school_area']}'";
        }
        //区域
        if (isset($paramArray['district_id']) && $paramArray['district_id'] != '') {
            $datawhere .= " and s.district_id = '{$paramArray['district_id']}'";
        }
        //类型
        if (isset($paramArray['school_type']) && $paramArray['school_type'] != '') {
            $datawhere .= " and s.school_type = '{$paramArray['school_type']}'";
        }

        if (isset($paramArray['adaptive']) && $paramArray['adaptive'] != '') {
            $activityList = $this->DataControl->selectClear("select a.school_id from crm_sell_activity_school as a WHERE  a.activity_id = '{$paramArray['activity_id']}' and  a.company_id = '{$paramArray['company_id']}' GROUP BY a.school_id ");
            if (is_array($activityList)) {
                $schoolid = '';
                foreach ($activityList as $activityVar) {
                    $schoolid .= $activityVar['school_id'] . ',';
                }
                $schoolid = substr($schoolid, 0, -1);
            }

            $schoolid = ($schoolid == '') ? 0 : $schoolid;

            if ($paramArray['adaptive'] == '0') {
                $datawhere .= " and s.school_id not in ($schoolid)";
            } else {
                $datawhere .= " and s.school_id in ($schoolid)";
            }
        }

        if (isset($paramArray['is_count']) && $paramArray['is_count'] == '1') {
            $sql = "SELECT  COUNT(s.school_id)  as datanum
                 FROM smc_school AS s
                 WHERE{$datawhere} ";
            $count = $this->DataControl->selectOne($sql);
            $count = $count['datanum'] + 0;
        } else {
            $count = '';
        }

        $sqlfields = "s.school_id,s.school_branch,s.school_type,s.school_cnname,s.school_enname,s.school_shortname,
                    (select d.district_cnname from gmc_company_district as d where s.district_id = d.district_id) as district_cnname,
                    (SELECT count(a.activity_id) from crm_sell_activity_school as a WHERE s.school_id = a.school_id  and a.activity_id = '{$paramArray['activity_id']}') as adaptive ";
        $dataList = $this->DataControl->selectClear("SELECT {$sqlfields} FROM smc_school AS s WHERE {$datawhere} and s.school_isclose = '0' ORDER BY s.school_id DESC");

        if (is_array($dataList)) {
            foreach ($dataList as &$dataListVar) {
                if ($dataListVar['school_type'] == '1') {
                    $dataListVar['school_typename'] = '直营校';
                } elseif ($dataListVar['school_type'] == '2') {
                    $dataListVar['school_typename'] = '直营园';
                } elseif ($dataListVar['school_type'] == '3') {
                    $dataListVar['school_typename'] = '加盟校';
                } elseif ($dataListVar['school_type'] == '4') {
                    $dataListVar['school_typename'] = '加盟园';
                }
            }
        }

        //区域
        $district = $this->DataControl->selectClear("select district_id,district_cnname from gmc_company_district ");
        $result = array();
        $result["district"] = $district;
        $result["datalist"] = $dataList;
        $result["count"] = $count;
        return $result;
    }
    //获取 全部 省市区
    function getProCityAreasView()
    {
        $request = Input('get.', '', 'trim,addslashes');

        $Province = $this->DataControl->selectClear("SELECT region_id,region_name from smc_code_region WHERE parent_id = 1");
        if ($Province) {
            foreach ($Province as &$Provincevar) {
                $City = $this->DataControl->selectClear("SELECT region_id,region_name from smc_code_region WHERE parent_id = '{$Provincevar['region_id']}'");
                $Provincevar['children'] = $City;
                if ($Provincevar['children']) {
                    foreach ($Provincevar['children'] as &$Cityvar) {
                        $Area = $this->DataControl->selectClear("SELECT region_id,region_name from smc_code_region WHERE parent_id = '{$Cityvar['region_id']}'");
                        $Cityvar['children'] = $Area;
                    }
                }
            }
        }
        if ($Province) {
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '省市区获取成功', 'result' => $Province);
        } else {
            $result["data"] = array();
            $res = array('error' => '0', 'errortip' => '省市区获取失败', 'result' => array());
        }
        ajax_return($res, $request['language_type']);
    }
    //获取学校省
    function getActProvinceView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $activityOne = $this->DataControl->selectOne("select a.activity_id,a.company_id,a.activity_ischoiceschool from crm_sell_activity as a
where a.activity_id ='{$request['activity_id']}' limit 0,1");
        if ($activityOne['activity_ischoiceschool'] == '1') {
            $Province = $this->DataControl->selectClear("SELECT r.region_id,r.region_name FROM crm_sell_activity_school as a
                  LEFT JOIN smc_school as s ON a.school_id = s.school_id
                  LEFT JOIN smc_code_region as r ON s.school_province = r.region_id
                  WHERE a.activity_id = '{$request['activity_id']}' and s.school_province <> '' and r.region_id <> ''
                  GROUP BY r.region_id ");
        } else {
            if ($activityOne['company_id'] == '78531') {
                $Province = $this->DataControl->selectClear("SELECT r.region_id,r.region_name FROM smc_code_region as r WHERE r.region_id = '5005'");
            } else {
                $Province = $this->DataControl->selectClear("SELECT r.region_id,r.region_name FROM smc_code_region as r WHERE r.region_id <> '' and r.parent_id = '1'");
            }
        }
        if ($Province) {
            $res = array('error' => '0', 'errortip' => '省份数据获取成功', 'result' => $Province);
        } else {
            $res = array('error' => '0', 'errortip' => '省份数据获取成功', 'result' => array());
        }
        ajax_return($res, $request['language_type']);
    }
    //获取学校城市
    function getActCityView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $activityOne = $this->DataControl->selectOne("select a.activity_id,a.company_id,a.activity_ischoiceschool from crm_sell_activity as a
where a.activity_id ='{$request['activity_id']}' limit 0,1");
        if ($activityOne['activity_ischoiceschool'] == '1') {
            $Province = $this->DataControl->selectClear("SELECT r.region_id,r.region_name
                  from crm_sell_activity_school as a 
                  LEFT JOIN smc_school as s ON a.school_id = s.school_id
                  LEFT JOIN smc_code_region as r ON s.school_city = r.region_id
                  WHERE a.activity_id = '{$request['activity_id']}'  and s.school_city <> '' and r.parent_id = '{$request['region_id']}' and r.region_id <> '' 
                  GROUP BY r.region_id 
                  ORDER BY r.region_sort ASC ");
        } else {
            $Province = $this->DataControl->selectClear("SELECT r.region_id,r.region_name FROM smc_code_region as r WHERE r.parent_id = '{$request['region_id']}' 
                  ORDER BY r.region_sort ASC ");
        }
        if ($Province) {
            $res = array('error' => '0', 'errortip' => '城市数据获取成功', 'result' => $Province);
        } else {
            $res = array('error' => '0', 'errortip' => '城市数据获取成功', 'result' => array());
        }
        ajax_return($res, $request['language_type']);
    }
    //获取学校区域
    function getActAreasView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $activityOne = $this->DataControl->selectOne("select a.activity_id,a.company_id,a.activity_ischoiceschool from crm_sell_activity as a
where a.activity_id ='{$request['activity_id']}' limit 0,1");
        if ($activityOne['activity_ischoiceschool'] == '1') {
            $Province = $this->DataControl->selectClear("SELECT r.region_id,r.region_name
                  from crm_sell_activity_school as a 
                  LEFT JOIN smc_school as s ON a.school_id = s.school_id
                  LEFT JOIN smc_code_region as r ON s.school_area = r.region_id
                  WHERE a.activity_id = '{$request['activity_id']}' and s.school_area <> '' and r.parent_id  = '{$request['region_id']}' and r.region_id <> '' 
                  GROUP BY r.region_id 
                  ORDER BY r.region_sort ASC ");
        } else {
            $Province = $this->DataControl->selectClear("SELECT r.region_id,r.region_name FROM smc_code_region as r WHERE r.parent_id = '{$request['region_id']}' ORDER BY r.region_sort ASC ");
        }
        if ($Province) {
            $res = array('error' => '0', 'errortip' => '区域数据获取成功', 'result' => $Province);
        } else {
            $res = array('error' => '0', 'errortip' => '区域数据获取成功', 'result' => array());
        }
        ajax_return($res, $request['language_type']);
    }
    //与站群对接 获取试听记录
    function getAudOneByMoblieApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $datawhere = "ca.audition_isvisit = '1'";

        if (isset($request['company_id']) && $request['company_id'] != '') {
            $datawhere .= " and cl.company_id ='{$request['company_id']}'";
        }
        if (isset($request['client_cnname']) && $request['client_cnname'] != '') {
            $datawhere .= " and cl.client_cnname  = '{$request['client_cnname']}'";
        }
        if (isset($request['client_mobile']) && $request['client_mobile'] != '') {
            $datawhere .= " AND (cl.client_mobile = '{$request['client_mobile']}' or p.parenter_mobile = '{$request['client_mobile']}')";
        }
        if ($request['client_mobile'] == "" || $request['client_cnname'] == "" || $request['company_id'] == "") {
            $res = array('error' => '1', 'errortip' => '请选择手机号或姓名', 'result' => array());
            ajax_return($res, $request['language_type']);
        }

        $sql = "select cl.client_id,ca.class_id,ca.class_cnname,ca.audition_visittime,
            (select sf.staffer_cnname from  smc_class_hour_teaching  as  ht   
                         LEFT JOIN smc_staffer as  sf ON sf.staffer_id =ht.staffer_id
                         where  ht.hour_id=ca.hour_id  and ca.class_id =ht.class_id  and ht.teaching_type=0 limit 0,1 )  as staffer_cnname
              from  crm_client as cl
              LEFT JOIN crm_client_audition as  ca On ca.client_id= cl.client_id
              LEFT JOIN crm_client_family as f ON f.client_id = cl.client_id
              LEFT JOIN smc_parenter as p ON p.parenter_id = f.parenter_id
              where {$datawhere}
              order by audition_visittime DESC";
        $audtionOne = $this->DataControl->selectOne($sql);
        if ($audtionOne && $audtionOne['staffer_cnname'] == "") {
            $audtionOne['staffer_cnname'] = '--';
        }
        if ($audtionOne) {
            $res = array('error' => '0', 'errortip' => '获取试听数据成功', 'result' => $audtionOne);
        } else {
            $res = array('error' => '1', 'errortip' => '暂无试听数据', 'result' => array(), "sql" => $sql);
        }

        ajax_return($res, $request['language_type']);

    }
    //与站群对接 获取到访记录 20191205  --  97添加
    function getInviteOneByMoblieApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $datawhere = "i.invite_isvisit = '1'";

        if (isset($request['company_id']) && $request['company_id'] != '') {
            $datawhere .= " and c.company_id ='{$request['company_id']}'";
        }
        if (isset($request['client_cnname']) && $request['client_cnname'] != '') {
            $datawhere .= " and c.client_cnname  = '{$request['client_cnname']}'";
        }
        if (isset($request['client_mobile']) && $request['client_mobile'] != '') {
            $datawhere .= " AND (c.client_mobile = '{$request['client_mobile']}' or p.parenter_mobile = '{$request['client_mobile']}')";
        }
        if ($request['client_mobile'] == "" || $request['client_cnname'] == "" || $request['company_id'] == "") {
            $res = array('error' => '1', 'errortip' => '请选择手机号或姓名', 'result' => array());
            ajax_return($res, $request['language_type']);
        }

        $sql = "select c.client_id,i.invite_isvisit,(SELECT s.staffer_cnname FROM crm_marketer as m LEFT JOIN smc_staffer as s ON s.staffer_id = m.staffer_id WHERE m.marketer_id = i.marketer_id AND m.company_id = '{$request['company_id']}') as staffer_cnname
              from  crm_client as c
              LEFT JOIN crm_client_invite as  i On i.client_id= c.client_id
              LEFT JOIN crm_client_family as f ON f.client_id = c.client_id
              LEFT JOIN smc_parenter as p ON p.parenter_id = f.parenter_id
              where {$datawhere}
              order by invite_createtime DESC";
        $inviteOne = $this->DataControl->selectOne($sql);
        if ($inviteOne && $inviteOne['staffer_cnname'] == "") {
            $inviteOne['staffer_cnname'] = '--';
        }
        if ($inviteOne) {
            $res = array('error' => '0', 'errortip' => '获取试听数据成功', 'result' => $inviteOne);
        } else {
            $res = array('error' => '1', 'errortip' => '暂无试听数据', 'result' => array(), "sql" => $sql);
        }
        ajax_return($res, $request['language_type']);
    }
    //与站群对接 获取有效名单是否存在CRM 20191205  --  97添加
    function getClientOneByMoblieApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $datawhere = "1 ";

        if (isset($request['company_id']) && $request['company_id'] != '') {
            $datawhere .= " and c.company_id ='{$request['company_id']}'";
        }
        if (isset($request['client_cnname']) && $request['client_cnname'] != '') {
            $datawhere .= " and c.client_cnname  = '{$request['client_cnname']}'";
        }
        if (isset($request['client_mobile']) && $request['client_mobile'] != '') {
            $datawhere .= " AND (c.client_mobile = '{$request['client_mobile']}' or p.parenter_mobile = '{$request['client_mobile']}')";
        }
        if ($request['client_mobile'] == "" || $request['client_cnname'] == "" || $request['company_id'] == "") {
            $res = array('error' => '1', 'errortip' => '请选择手机号或姓名', 'result' => array());
            ajax_return($res, $request['language_type']);
        }

        $sql = "select c.client_id,c.client_mobile,p.parenter_mobile 
              from  crm_client as c
              LEFT JOIN crm_client_family as f ON f.client_id = c.client_id
              LEFT JOIN smc_parenter as p ON p.parenter_id = f.parenter_id  
              where {$datawhere} ";
        $ClientOne = $this->DataControl->selectOne($sql);
        if ($ClientOne) {
            $res = array('error' => '0', 'errortip' => '获取有效名单数据成功', 'result' => $ClientOne);
        } else {
            $res = array('error' => '1', 'errortip' => '暂无有效名单数据', 'result' => array(), "sql" => $sql);
        }
        ajax_return($res, $request['language_type']);
    }
    //与CRM检测
    function getPositiveRecordApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $datawhere = "c.client_id = p.client_id";

        if (isset($request['company_id']) && $request['company_id'] != '') {
            $datawhere .= " and c.company_id ='{$request['company_id']}'";
        }
        if (isset($request['client_cnname']) && $request['client_cnname'] != '') {
            $datawhere .= " and c.client_cnname ='{$request['student_cnname']}'";
        }
        if (isset($request['student_branch']) && $request['student_branch'] != '') {
            $datawhere .= " and p.student_branch ='{$request['student_branch']}'";
        }
        if ($request['student_branch'] == "" || $request['student_cnname'] == "") {
            $res = array('error' => '1', 'errortip' => '请选择学员姓名和编号', 'result' => array());
            ajax_return($res, $request['language_type']);
        }

        $sql = "SELECT c.client_id,c.channel_id FROM crm_client AS c, crm_client_positivelog AS p
WHERE {$datawhere} ORDER BY p.positvelog_addtime DESC";
        $audtionOne = $this->DataControl->selectOne($sql);
        if ($audtionOne) {
            $res = array('error' => '0', 'errortip' => '获取转正数据成功', 'result' => $audtionOne);
        } else {
            $res = array('error' => '1', 'errortip' => '无转正记录', 'result' => array(), "sql" => $sql);
        }

        ajax_return($res, $request['language_type']);

    }
    function getCrmOtherSchoolView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $schoolJson = $this->DataControl->selectOne("SELECT a.activity_chosejson FROM crm_sell_activity AS a
WHERE a.activity_id = '{$request['activity_id']}'");
        exit($schoolJson['activity_chosejson']);
    }
    function getCrmPositivelogView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $dataList = $this->DataControl->selectClear("SELECT
	c.client_cnname,
	l.student_branch,
	(
		SELECT
			d.channel_name
		FROM
			crm_code_channel AS d
		WHERE
			d.channel_id = c.channel_id
	) as channel_name
FROM
	crm_client AS c,
	crm_client_positivelog AS l
WHERE
	c.client_id = l.client_id
AND c.channel_id in (138,139) AND c.company_id='{$request['company_id']}'
GROUP BY
	l.student_branch");

        if (!$dataList) {
            $result = array();
        } else {
            $result = $dataList;
        }
        $res = array('error' => '0', 'errortip' => '获取成功', 'result' => $result);
        ajax_return($res);
    }

    /*function updataClientView()
    {
        $sql = "SELECT
             e.outthree_id AS outthree_userid,
             e.company_id,
             e.client_patriarchname,
             e.client_cnname,
             e.client_mobile,
             e.client_age,
             e.client_sex,
             e.client_address,
             e.client_gmcmarket,
             e.client_remark,
             e.client_one AS client_source,
             c.channel_id,
             e.client_createtime
            FROM
             crm_outthree AS e,
             crm_code_channel AS c
            WHERE
             e.client_two = c.channel_name
            AND e.company_id = c.company_id
            AND e.company_id = '8888'
            AND e.client_isstud = '0' AND e.client_isinset = '0' limit 0,100";
        $dataList = $this->DataControl->selectClear($sql);
        if ($dataList) {
            $count = 0;
            foreach ($dataList as $key => $value) {
                $clientOne = $this->DataControl->getFieldOne("crm_client", "client_id", "outthree_userid='{$value['outthree_userid']}'");
                if ($clientOne) {
                    $threeData = array();
                    $threeData['client_isinset'] = '1';
                    $this->DataControl->updateData("crm_outthree", "outthree_id='{$value['outthree_userid']}'", $threeData);
                    continue;
                }
                $clientData = array();
                $clientData['company_id'] = $value['company_id'];
                $clientData['client_patriarchname'] = $value['client_patriarchname'];
                $clientData['outthree_userid'] = $value['outthree_userid'];
                $clientData['client_cnname'] = $value['client_cnname']==''?$value['client_patriarchname'] : $value['client_cnname'] ;
                $clientData['client_mobile'] = $value['client_mobile'];
                $clientData['client_age'] = $value['client_age'];
                $clientData['client_sex'] = $value['client_sex'];
                $clientData['client_address'] = $value['client_address'];
                $clientData['client_gmcmarket'] = $value['client_gmcmarket'];
                $clientData['client_remark'] = $value['client_remark'];
                $clientData['client_source'] = $value['client_source'];
                $clientData['channel_id'] = $value['channel_id'];
                $clientData['client_updatetime'] = strtotime($value['client_createtime']);
                $clientData['client_createtime'] = strtotime($value['client_createtime']);
                if ($client_id = $this->DataControl->insertData("crm_client", $clientData)) {
                    $count++;
                    $parenterOne = $this->DataControl->getFieldOne("smc_parenter", "parenter_id", "parenter_mobile='{$value['client_mobile']}'");
                    if ($parenterOne) {
                        $memberData = array();
                        $memberData['client_id'] = $client_id;
                        $memberData['parenter_id'] = $parenterOne['parenter_id'];
                        $memberData['family_relation'] = '未知';
                        $memberData['family_isdefault'] = 1;
                        $this->DataControl->insertData('crm_client_family', $memberData);
                    } else {
                        $parentData = array();
                        $parentData['parenter_cnname'] = $value['client_patriarchname'];
                        $parentData['parenter_mobile'] = $value['client_mobile'];
                        $parentData['parenter_pass'] = md5(substr($value['client_mobile'],-6));
                        $parentData['parenter_bakpass'] = substr($value['client_mobile'],-6);
                        $parenter_id = $this->DataControl->insertData("smc_parenter",$parentData);

                        $memberData = array();
                        $memberData['client_id'] = $client_id;
                        $memberData['parenter_id'] = $parenter_id;
                        $memberData['family_relation'] = '未知';
                        $memberData['family_isdefault'] = 1;
                        $this->DataControl->insertData('crm_client_family', $memberData);
                    }
                    $threeData = array();
                    $threeData['client_isinset'] = '1';
                    $this->DataControl->updateData("crm_outthree", "outthree_id='{$value['outthree_userid']}'", $threeData);
                }
            }
            echo '<script language="javascript" type="text/javascript">
var i = 1;
var intervalid;
intervalid = setInterval("fun()", 1000);
function fun() {
	if (i == 0) {
		window.location.href = "/Api/updataClient";
		clearInterval(intervalid);
	}
	document.getElementById("mes").innerHTML = i;
	i--;
}
</script>
<div id="error">
	<p>将在 <span id="mes">2</span> 秒钟后返回首页！</p>
</div> ';

            $res = array('error' => '0', 'errortip' => '同步成功,共同步'.$count,'条数据 ','result' => array());
            ajax_return($res);
        }else{
            $res = array('error' => '1', 'errortip' => '暂无需要同步的数据', 'result' => array());
            ajax_return($res);
        }
    }*/


    //对接百度推广留名单导入到 crm 有效名单 -- 97
    function addBaiduClientAction(){
        $request = file_get_contents("php://input");
 
        //获得的数据
        $bakarray = json_decode($request,true);
        if(!is_array($bakarray)){
            header('HTTP/1.1 400 BadRequest');
            exit;
        }

        if($bakarray['手机号'] && preg_match("/^(861(\d){10})$|^(8609(\d){8})$/",$bakarray['手机号'])){
            $bakarray['手机号'] = substr($bakarray['手机号'], 2);
        }

        //存储到 CRM
        $parameter = array();
        $parameter['client_frompage'] = $bakarray['名单备注']."（百度Api传值）";
        $parameter['client_tag'] = "百度Api传值";
        $parameter['client_source'] = "搜索引擎";
        if($bakarray['招生渠道明细']=='其他'){
            $parameter['channel_name'] = '百度品专';
        }elseif($bakarray['招生渠道明细']=='搜索推广'){
            $parameter['channel_name'] = '百度竞价';
        }
//        $parameter['channel_name'] = $bakarray['招生渠道明细']=='其他'?'百度品专':$bakarray['招生渠道明细'];
//        $parameter['client_remark'] = "搜索引擎获取名单回传到CRM的名单";
        $parameter['client_patriarchname'] = '匿名';
        $parameter['client_cnname'] = $bakarray['中文名']==''?"主动百度搜索":$bakarray['中文名'];
        $parameter['client_mobile'] = $bakarray['手机号'];
        $parameter['client_remark'] = $bakarray['来源页面'];
        $parameter['company_id'] = '8888';
        request_by_curl("https://crmapi.kedingdang.com/PhoneActivity/addPhoneChannelAction",dataEncode($parameter),"POST",array());

        $data = array();
        $data['baidulog_datajson'] = $request;
        $data['baidulog_addtime'] = time();
        if ($this->DataControl->insertData("crm_request_baidulog",$data)) {
            $res = array('error' => '0', 'errortip' => '数据存储成功', 'result' => $data);
        } else {
            $res = array('error' => '1', 'errortip' => '数据存储失败', 'result' => array());
        }
        ajax_return($res);
    }

    //对接教育宝 20210422 (陆晶走之后写）
    function addJiaoYuBaoToCrmAction(){
        $request = Input('post.', '', 'trim,addslashes');

        $parameter = array();
        $parameter['client_tag'] = "教育宝";
        $parameter['client_source'] = "代理招生";
        $parameter['channel_name'] = "教育宝";
        $parameter['client_patriarchname'] = '匿名';
        $parameter['company_id'] = '8888';
        $parameter['client_cnname'] = $request['client_cnname'];
        $parameter['client_mobile'] = $request['client_mobile'];
        $parameter['client_remark'] = ($request['client_remark']?$request['client_remark']:'教育宝获取名单回传到CRM的名单').($request['client_course']?'('.$request['client_course'].')':'');
        $parameter['client_address'] = $request['client_address'];
        $parameter['school_branch'] = $request['school_branch'];
//        $parameter['client_birthday'] = $request['client_birthday'];
//        $parameter['client_age'] = birthdaytoage($request['client_birthday']);
        $bool = request_by_curl("https://crmapi.kedingdang.com/PhoneActivity/addPhoneChannelAction",dataEncode($parameter),"POST",array());
        $boolarray = json_decode($bool,true);
        if($boolarray['error'] == '0'){
            $res = array('error' => '0', 'errortip' => '回传数据成功', 'result' => array());
        }else{
            $res = array('error' => '1', 'errortip' => '回传数据失败', 'result' => array());
        }
        ajax_return($res);
    }

    /**
     * 对接教育宝
     * author: ling
     * 对应接口文档 0001
     */
    function addJiaoYuBaoClientAction(){
        $request = Input('post.', '', 'trim,addslashes');
        $parameter = array();
        $parameter['client_tag'] = "教育宝";
        $parameter['client_source'] = "代理招生";
        $parameter['channel_name'] = "教育宝";
        $parameter['client_remark'] = "教育宝获取名单回传到CRM的名单";
        $parameter['client_patriarchname'] = '匿名';
        $parameter['company_id'] = '8888';
        $parameter['client_cnname'] = $request['client_cnname'];
        $parameter['client_mobile'] = $request['client_mobile'];
        $parameter['client_remark'] = $request['client_remark'];
        $parameter['client_birthday'] = $request['client_birthday'];
        $parameter['client_age'] = birthdaytoage($request['client_birthday']);
        $bool = request_by_curl("https://crmapi.kedingdang.com/PhoneActivity/addPhoneChannelAction",dataEncode($parameter),"POST",array());
        if($bool){
            $res = array('error' => '0', 'errortip' => '回传数据成功', 'result' => array());
        }else{
            $res = array('error' => '1', 'errortip' => '回传数据失败', 'result' => array());
        }
        ajax_return($res);
    }

    function testAction(){
        return true;
    }

    function PayAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $fee = 0.01;//举例充值0.01
        $appid = self::XcxappId;//如果是公众号 就是公众号的appid
        $body = '充值';
        $mch_id = '1602629681';
        $nonce_str = $this->create_guid();//随机字符串
        $notify_url = 'https://pay.kedingdang.com/orderPay/Api/testAction';//通知地址
        $openid = $request['openid'];
        $out_trade_no = $this->order_number($openid);//商户订单号
        $spbill_create_ip = real_ip();//服务器的ip
        $total_fee = $fee * 100;//因为充值金额最小是1 而且单位为分 如果是充值1元所以这里需要*100
        $trade_type = 'JSAPI';//交易类型 默认

        //这里是按照顺序的 因为下面的签名是按照顺序 排序错误 肯定出错
        $post['appid'] = $appid;
        $post['body'] = $body;
        $post['mch_id'] = $mch_id;
        $post['nonce_str'] = $nonce_str;//随机字符串
        $post['notify_url'] = $notify_url;
        $post['out_trade_no'] = $out_trade_no;
        $post['openid'] = $openid;
        $post['spbill_create_ip'] = $spbill_create_ip;//终端的ip
        $post['total_fee'] = $total_fee;//总金额 最低为一块钱 必须是整数
        $post['trade_type'] = $trade_type;
        $sign = $this->sign($post);//签名

        $post_xml = '<xml>
           <appid>' . $appid . '</appid>
           <body>' . $body . '</body>
           <mch_id>' . $mch_id . '</mch_id>
           <nonce_str>' . $nonce_str . '</nonce_str>
           <notify_url>' . $notify_url . '</notify_url>
           <openid>' . $openid . '</openid>
           <out_trade_no>' . $out_trade_no . '</out_trade_no>
           <spbill_create_ip>' . $spbill_create_ip . '</spbill_create_ip>
           <total_fee>' . $total_fee . '</total_fee>
           <trade_type>' . $trade_type . '</trade_type>
           <sign>' . $sign . '</sign>
        </xml> ';
        //统一接口prepay_id
        $url = 'https://api.mch.weixin.qq.com/pay/unifiedorder';
        var_dump($post_xml);
        $xml = $this->http_request($url, $post_xml);
        $array = $this->xml($xml);//全要大写
        if ($array['RETURN_CODE'] == 'SUCCESS' && $array['RESULT_CODE'] == 'SUCCESS') {
            $time = time();
            $tmp = '';//临时数组用于签名
            $tmp['appId'] = $appid;
            $tmp['nonceStr'] = $nonce_str;
            $tmp['package'] = 'prepay_id=' . $array['PREPAY_ID'];
            $tmp['signType'] = 'MD5';
            $tmp['timeStamp'] = "$time";

            $data['state'] = 1;
            $data['timeStamp'] = "$time";//时间戳
            $data['nonceStr'] = $nonce_str;//随机字符串
            $data['signType'] = 'MD5';//签名算法，暂支持 MD5
            $data['package'] = 'prepay_id=' . $array['PREPAY_ID'];//统一下单接口返回的 prepay_id 参数值，提交格式如：prepay_id=*
            $data['paySign'] = $this->sign($tmp);//签名,具体签名方案参见微信公众号支付帮助文档;
            $data['out_trade_no'] = $out_trade_no;

        } else {
            $data['state'] = 0;
            $data['text'] = "错误";
            $data['RETURN_CODE'] = $array['RETURN_CODE'];
            $data['RETURN_MSG'] = $array['RETURN_MSG'];
        }
        echo json_encode($data);
    }

    //随机32位字符串
    function nonce_str()
    {
        $result = '';
        $str = 'QWERTYUIOPASDFGHJKLZXVBNMqwertyuioplkjhgfdsamnbvcxz';
        for ($i = 0; $i < 32; $i++) {
            $result .= $str[rand(0, 48)];
        }
        return $result;
    }

    function create_guid() {
        $charid = strtoupper(md5(uniqid(mt_rand(), true)));
        $uuid =  substr($charid, 0, 8)
            .substr($charid, 8, 4)
            .substr($charid,12, 4)
            .substr($charid,16, 4)
            .substr($charid,20,12);
        return $uuid;
    }

    //生成订单号
    function order_number($openid)
    {
        return md5($openid . time() . rand(10, 99));//32位
    }

    //签名 $data要先排好顺序
    function sign($data)
    {
        $stringA = '';
        foreach ($data as $key => $value) {
            if (!$value) continue;
            if ($stringA) $stringA .= '&' . $key . "=" . $value;
            else $stringA = $key . "=" . $value;
        }

        return strtoupper(md5($stringA));
    }

    function http_request($url,$data = null,$headers=array()) {
        $curl = curl_init();
        if( count($headers) >= 1 ){
            curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);
        }
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, FALSE);
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, FALSE);

        if (!empty($data)){
            curl_setopt($curl, CURLOPT_POST, 1);
            curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
        }
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);

        $output = curl_exec($curl);  curl_close($curl);
        return $output;
    }

    function xml($xml){
        $p = xml_parser_create();
        xml_parse_into_struct($p, $xml, $vals, $index);
        xml_parser_free($p);  $data = "";
        foreach ($index as $key=>$value) {
            if($key == 'xml' || $key == 'XML'){
                continue;
            }
            $tag = $vals[$value[0]]['tag'];
            $value = $vals[$value[0]]['value'];  $data[$tag] = $value;
        }
        return $data;
    }

    /**
     * 容联七陌-外呼接口模式
     */
    public function dialoutAction()
    {

        $request = Input('POST.', '', 'trim,addslashes');

        $fromExten = $request['fromExten'];//坐席工号
        $exten = $request['exten'];//被叫号码

        $Model = new \Model\Crm\SevenMoorModel();
        $result = $Model->dialout($fromExten,$exten);
        ajax_return(['error' => '0', 'errortip' => '已获取到返回值', 'result' =>$result]);

    }

    /**
     * 容联七陌-隐私模式(小号绑定接口)
     */
    public function midNumBindAction()
    {
        $request = Input('POST.', '', 'trim,addslashes');
        $midNum = $request['midNum'];
        $called = $request['called'];
        $Model = new \Model\Crm\SevenMoorModel();
        $result = $Model->midNumBind($midNum,$called);
        ajax_return(['error' => '0', 'errortip' => '已获取到返回值', 'result' =>$result]);

    }

    /**
     * 容联七陌-隐私模式(小号解绑接口)
     */
    public function midNumUnBindingAction()
    {
        $request = Input('POST.', '', 'trim,addslashes');
        $midNum = $request['midNum'];
        $mappingId = $request['mappingId'];
        $Model = new \Model\Crm\SevenMoorModel();
        $result = $Model->midNumUnBinding($midNum,$mappingId);

        ajax_return(['error' => '0', 'errortip' => '已获取到返回值', 'result' =>$result]);

    }

    /**
     * 容联七陌-小号话单数据推送接口
     */
    public function callbackAction()
    {
        $callbacklog_data = $_SERVER["QUERY_STRING"];//获取所有get参数字符串
        $request = Input('GET.', '', 'trim,addslashes');
        $request['callbacklog_data'] = $callbacklog_data;
        $Model = new \Model\Crm\SevenMoorModel();
        $Model->addCallbackLog($request);
        ajax_return(['error' => '0', 'errortip' => 'received!', 'result' =>[]]);

    }

    /**
     * 容联七陌-挂机接口
     */
    public function hangupAction()
    {
        $request = Input('POST.', '', 'trim,addslashes');

        $CallID = $request['CallID'];//通话ID
        $Agent = $request['Agent'];//坐席工号
        $ActionID = $request['ActionID'];//随机码

        $Model = new \Model\Crm\SevenMoorModel();
        $result = $Model->hangup($CallID,$Agent,$ActionID);
        ajax_return(['error' => '0', 'errortip' => '已获取到返回值', 'result' =>$result]);
    }

    //针对积分商城的接口 -- 有推荐人的有效名单
    public function getEffectiveClientView(){
        $request = Input('get.', '', 'trim,addslashes');

        $Model = new \Model\Crm\ApiModel($request);
        $res = $Model->getEffectiveClient($request);

        $fieldstring = array('client_id','client_cnname','client_enname','client_sponsor','client_stubranch','client_createtime');
        $fieldname = array('名单ID', '名单中文名', '名单英文名', '推荐人姓名','推荐学生编号', '名单创建时间');
        $fieldcustom = array("1", "1", "1", "1", "1", "1", "1");
        $fieldshow = array("0", "1", "1", "1", "1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $resultarray = array();
        $resultarray['field'] = $field;
        $resultarray['all_num'] = $res['all_num']>0?$res['all_num']:'0';
        $resultarray['list'] = is_array($res['list'])?$res['list']:array();

        $result = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $resultarray);
        ajax_return($result,$request['language_type']);
    }

    //针对积分商城的接口 -- 有推荐人的有效名单 有到访的名单
    public function getEffectiveClientVisitView(){
        $request = Input('get.', '', 'trim,addslashes');

        $Model = new \Model\Crm\ApiModel($request);
        $res = $Model->getEffectiveClientVisit($request);

        $fieldstring = array('client_id','client_cnname','client_enname','client_sponsor','client_stubranch','client_createtime','visittime','track_id','coursetype_id');
        $fieldname = array('名单ID', '名单中文名', '名单英文名', '推荐人姓名','推荐学生编号','名单创建时间','到访时间','跟踪ID','课程ID');
        $fieldcustom = array("1", "1", "1", "1", "1", "1","1","1","1","1");
        $fieldshow = array("0", "1", "1", "1", "1", "1","1","1","1","1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $resultarray = array();
        $resultarray['field'] = $field;
        $resultarray['all_num'] = $res['all_num']>0?$res['all_num']:'0';
        $resultarray['list'] = is_array($res['list'])?$res['list']:array();

        $result = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $resultarray);
        ajax_return($result,$request['language_type']);
    }

    //针对积分商城的接口 -- 有推荐人的有效名单 报名名单
    public function getEffectiveClientRegView(){
        $request = Input('get.', '', 'trim,addslashes');

        $Model = new \Model\Crm\ApiModel($request);
        $res = $Model->getEffectiveClientReg($request);

        $fieldstring = array('client_id','client_cnname','client_enname','client_sponsor','client_stubranch','client_createtime','coursetype_id','pay_firsttime');
        $fieldname = array('名单ID', '名单中文名', '名单英文名', '推荐人姓名','推荐学生编号','名单创建时间','班组ID','报名时间');
        $fieldcustom = array("1", "1", "1", "1", "1", "1","1","1","1");
        $fieldshow = array("0", "1", "1", "1", "1", "1","1","1","1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $resultarray = array();
        $resultarray['field'] = $field;
        $resultarray['all_num'] = $res['all_num']>0?$res['all_num']:'0';
        $resultarray['list'] = is_array($res['list'])?$res['list']:array();

        $result = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $resultarray);
        ajax_return($result,$request['language_type']);
    }


    //名单迁移实例
    function migrationCrmView(){
        $paramArray = array();
        $paramArray['school_id'] = '1245';
        $paramArray['frommarketer_id'] = '4074';
        $paramArray['tomarketer_id'] = '240';

        //交接负责人名单
        $migrationModel = new \Model\Crm\ClientModel();
        $migrationModel->migrationClient($paramArray['frommarketer_id'],$paramArray['tomarketer_id'],$paramArray['school_id'],'15');
    }



    //应急 BI 报表数据 --  销售招生分析表
    function getTeachersAdmissionsView()
    {
        $nowday = date("Y-m-d");
        $sql = "SELECT f.* 
FROM(SELECT s.school_id, s.school_cnname, c.coursetype_id, c.coursetype_cnname
FROM smc_school AS s, smc_code_coursetype AS c
WHERE s.company_id = c.company_id AND c.company_id = '8888' AND s.school_isclose = '0' AND c.coursetype_id = '65'
AND s.school_istest = '0' AND c.coursetype_isrecruit = '1' AND c.coursetype_isopenclass = '0') AS f 
WHERE NOT EXISTS (SELECT 1 FROM crm_client_bilog as c WHERE c.bilog_type = '1' AND c.coursetype_id = f.coursetype_id 
		AND c.school_id = f.school_id AND c.bilog_day >= '{$nowday}') limit 0,1";
        $schoolList = $this->DataControl->selectClear($sql);

        $paramArray = array();
        $paramArray['company_id'] = 8888;
        $ReportModel = new \Model\Crm\ReportModel($paramArray);
        if($schoolList){
            foreach ($schoolList as $schoolOne){
                $paramArray = array();
                $paramArray['company_id'] = '8888';
                $paramArray['school_id'] = $schoolOne['school_id'];
                $paramArray['coursetype_id'] = $schoolOne['coursetype_id'];
                $datalist = $ReportModel->getTeachersAdmissions($paramArray);
                $datajson = json_encode($datalist['list'],JSON_UNESCAPED_UNICODE);

                $data = array();
                $data['school_id'] = $schoolOne['school_id'];
                $data['coursetype_id'] = $schoolOne['coursetype_id'];
                $data['bilog_jsontxt'] = $datajson;
                $data['bilog_type'] = 1;
                $data['bilog_updatetime'] = time();
                $data['bilog_day'] = date("Y-m-d");
                $this->DataControl->insertData('crm_client_bilog',$data);
            }
        }

        $res = array('error' => '1', 'errortip' => "更新成功");
        ajax_return($res);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "school_shortname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "staffer_cnname";
        $field[$k]["fieldname"] = "教师中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "staffer_enname";
        $field[$k]["fieldname"] = "教师英文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "post_name";
        $field[$k]["fieldname"] = "本校职务";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "clientnum";
        $field[$k]["fieldname"] = "系统名单总数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "princ_clientnum";
        $field[$k]["fieldname"] = "报名/当年";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "princ_upmonth_num";
        $field[$k]["fieldname"] = "报名/上月";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "princ_nowmonth_num";
        $field[$k]["fieldname"] = "报名/当月";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "princ_month_num";
        $field[$k]["fieldname"] = "报名/月";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "princ_upweek_num";
        $field[$k]["fieldname"] = "报名/上周";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "princ_nowweek_num";
        $field[$k]["fieldname"] = "报名/当周";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "princ_nowweek_num";
        $field[$k]["fieldname"] = "报名/周";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "track_month_num";
        $field[$k]["fieldname"] = "追踪人数/月";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "track_month_time";
        $field[$k]["fieldname"] = "追踪人次/月";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "track_week_num";
        $field[$k]["fieldname"] = "追踪人数/周";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "track_week_time";
        $field[$k]["fieldname"] = "追踪人次/周";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "invite_upmonth_num";
        $field[$k]["fieldname"] = "邀约诺访/上月";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "ohaudition_upmonth_num";
        $field[$k]["fieldname"] = "OH邀约诺访/上月";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "audition_upmonth_num";
        $field[$k]["fieldname"] = "试听邀约诺访/上月";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "invitetwo_upmonth_num";
        $field[$k]["fieldname"] = "柜询插测邀约诺访/上月";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "invitesan_upmonth_num";
        $field[$k]["fieldname"] = "柜询邀约诺访/上月";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "arrive_upmonth_num";
        $field[$k]["fieldname"] = "邀约到访/上月";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "oharrive_upmonth_num";
        $field[$k]["fieldname"] = "OH邀约到访/上月";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "auditiontwo_upmonth_num";
        $field[$k]["fieldname"] = "试听邀约到访/上月";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "invitesix_upmonth_num";
        $field[$k]["fieldname"] = "柜询插测邀约到访/上月";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "inviteseven_upmonth_num";
        $field[$k]["fieldname"] = "柜询邀约到访/上月";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "arrive_upmonth_rate";
        $field[$k]["fieldname"] = "邀约到访率/上月";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "invite_nowmonth_num";
        $field[$k]["fieldname"] = "邀约诺访/当月";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "ohaudition_nowmonth_num";
        $field[$k]["fieldname"] = "OH邀约诺访/当月";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "audition_nowmonth_num";
        $field[$k]["fieldname"] = "试听邀约诺访/当月";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "invitetwo_nowmonth_num";
        $field[$k]["fieldname"] = "柜询插测邀约诺访/当月";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "invitesan_nowmonth_num";
        $field[$k]["fieldname"] = "柜询邀约诺访/当月";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "arrive_nowmonth_num";
        $field[$k]["fieldname"] = "邀约到访/当月";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "oharrive_nowmonth_num";
        $field[$k]["fieldname"] = "OH邀约到访/当月";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "auditiontwo_nowmonth_num";
        $field[$k]["fieldname"] = "试听邀约到访/当月";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "invitesix_nowmonth_num";
        $field[$k]["fieldname"] = "柜询插测邀约到访/当月";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "inviteseven_nowmonth_num";
        $field[$k]["fieldname"] = "柜询邀约到访/当月";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "arrive_nowmonth_rate";
        $field[$k]["fieldname"] = "邀约到访率/当月";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "invite_upweek_num";
        $field[$k]["fieldname"] = "邀约诺访/上周";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "ohaudition_upweek_num";
        $field[$k]["fieldname"] = "OH邀约诺访/上周";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "audition_upweek_num";
        $field[$k]["fieldname"] = "试听邀约诺访/上周";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "invitetwo_upweek_num";
        $field[$k]["fieldname"] = "柜询插测邀约诺访/上周";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "invitesan_upweek_num";
        $field[$k]["fieldname"] = "柜询邀约诺访/上周";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "arrive_upweek_num";
        $field[$k]["fieldname"] = "邀约到访/上周";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "oharrive_upweek_num";
        $field[$k]["fieldname"] = "OH邀约到访/上周";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "auditiontwo_upweek_num";
        $field[$k]["fieldname"] = "试听邀约到访/上周";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "invitesix_upweek_num";
        $field[$k]["fieldname"] = "柜询插测邀约到访/上周";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "inviteseven_upweek_num";
        $field[$k]["fieldname"] = "柜询邀约到访/上周";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "arrive_upweek_rate";
        $field[$k]["fieldname"] = "邀约到访率/上周";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "invite_nowweek_num";
        $field[$k]["fieldname"] = "邀约诺访/当周";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "ohaudition_nowweek_num";
        $field[$k]["fieldname"] = "OH邀约诺访/当周";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "audition_nowweek_num";
        $field[$k]["fieldname"] = "试听邀约诺访/当周";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "invitetwo_nowweek_num";
        $field[$k]["fieldname"] = "柜询插测邀约诺访/当周";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "invitesan_nowweek_num";
        $field[$k]["fieldname"] = "柜询邀约诺访/当周";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "arrive_nowweek_num";
        $field[$k]["fieldname"] = "邀约到访/当周";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "oharrive_nowweek_num";
        $field[$k]["fieldname"] = "OH邀约到访/当周";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "auditiontwo_nowweek_num";
        $field[$k]["fieldname"] = "试听邀约到访/当周";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "invitesix_nowweek_num";
        $field[$k]["fieldname"] = "柜询插测邀约到访/当周";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "inviteseven_nowweek_num";
        $field[$k]["fieldname"] = "柜询邀约到访/当周";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "arrive_nowweek_rate";
        $field[$k]["fieldname"] = "邀约到访率/当周";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "invite_month_num";
        $field[$k]["fieldname"] = "主动到访/月";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "enroll_month_num";
        $field[$k]["fieldname"] = "主动到访报名/月";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "enroll_month_rate";
        $field[$k]["fieldname"] = "主动到访报名率/月";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "invite_week_num";
        $field[$k]["fieldname"] = "主动到访/周";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "enroll_week_num";
        $field[$k]["fieldname"] = "主动到访报名/周";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "enroll_week_rate";
        $field[$k]["fieldname"] = "主动到访报名率/周";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["list"] = array();
        $res = array('error' => 1, 'errortip' => "教师招生数据储存成功", 'result' => $result);
        ajax_return($res);
    }


    //结尾魔术函数
    function __destruct()
    {

    }

}
