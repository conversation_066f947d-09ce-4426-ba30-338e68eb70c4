<?php

namespace Work\Controller\Crmapi;


class TimetableController extends viewTpl
{
    public $u;
    public $t;
    public $c;
    public $visitType = "api";
    public $DataControl;
    public $company_isassist = 0;

    //预加载处理类
    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();

        //数据库操作
        $this->DataControl = new \Dbmysql();

    }


    //本地权限校验入口
    function ThisVerify($request)
    {

        if (!intval($request['marketer_id'])) {
            $res = array('error' => '1', 'errortip' => "未检测到招生人员信息，请重新登录操作", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        if (isset($request['company_id']) && $request['company_id'] !== '') {
            $companyOne = $this->DataControl->getFieldOne("gmc_company", "company_isassist", "company_id='{$request['company_id']}'");
            $this->company_isassist = $companyOne['company_isassist'];
        }
        //叮当助教CRM
        if (isset($request['crm_token']) && $request['crm_token'] !== '') {
            $request['token'] = $request['crm_token'];
        }
        if (empty($request['token'])) {
            $res = array('error' => '1', 'errortip' => "未传入用户Token信息", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
    }


    function inviteTimetableApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);

        $TimetableModel = new \Model\Crm\TimetableModel($request);
        $dataList = $TimetableModel->inviteTimetable($request);

        $result = array();
        $field = array();

        $k=0;
        $field[$k]["fieldname"] = "周一";
        $field[$k]["fieldstring"] = "Monday";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "周二";
        $field[$k]["fieldstring"] = "Tuesday";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "周三";
        $field[$k]["fieldstring"] = "Wednesday";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "周四";
        $field[$k]["fieldstring"] = "Thursday";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "周五";
        $field[$k]["fieldstring"] = "Friday";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "周六";
        $field[$k]["fieldstring"] = "Saturday";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "周日";
        $field[$k]["fieldstring"] = "Sunday";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;


        $result['field'] = $field;
        if ($dataList) {
            $result['list'] = $dataList;
        } else {
            $result['list'] = array();
        }
        if (!$dataList) {
            $res = array('error' => '1', 'errortip' => $TimetableModel->errortip, 'result' => $result);
        } else {
            $res = array('error' => '0', 'errortip' => '获取信息', 'result' => $result);
        }

        ajax_return($res, $request['language_type']);

    }




}