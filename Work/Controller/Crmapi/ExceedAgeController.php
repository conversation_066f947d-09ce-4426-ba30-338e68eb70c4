<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/13
 * Time: 23:25
 */

namespace Work\Controller\Crmapi;


class ExceedAgeController extends viewTpl
{
    public $u;
    public $t;
    public $c;
    public $visitType = "api";
    public $DataControl;

    //预加载处理类
    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
        $this->api = $this->router->getApi();

        //数据库操作
        $this->DataControl = new \Dbmysql();
    }

    //本地权限校验入口
    function ThisVerify($request)
    {
        $paramArray = array();
        $paramArray['marketer_id'] = $request['marketer_id'];
        $paramArray['token'] = $request['token'];
        if (!$this->UserLimit($paramArray)) {
            $result = array();
            $result["list"] = array();
            $result["tokeninc"] = "0";
            $res = array('error' => '1', 'errortip' => "用户token失效", 'result' => $result);
            ajax_return($res);
        }
    }

    //超龄名单 -- 超龄列表
    function HomeView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        if (!isset($request['exceedagetime']) || $request['exceedagetime'] == '') {
            $res = array('error' => '1', 'errortip' => '超龄日期不能为空', 'result' => array());
            ajax_return($res);
        }

        //model
        $Model = new \Model\Crm\ExceedAgeModel($request);
        $dataList = $Model->getExceedAgeList($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldname"] = "client_id";
        $field[$k]["fieldstring"] = "ID";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "client_cnname";
        $field[$k]["fieldstring"] = "中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "client_enname";
        $field[$k]["fieldstring"] = "英文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "client_actisagree";
        $field[$k]["fieldstring"] = "是否同意政策";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "client_sex";
        $field[$k]["fieldstring"] = "性别";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "client_age";
        $field[$k]["fieldstring"] = "年龄";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "client_birthday";
        $field[$k]["fieldstring"] = "出生日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "client_tracestatus";
        $field[$k]["fieldstring"] = "跟踪状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "family_cnname";
        $field[$k]["fieldstring"] = "主要联系人";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "client_mobile";
        $field[$k]["fieldstring"] = "联系电话";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;
//        if($request['language_type'] == 'tw'){
        $field[$k]["fieldstring"] = "Email";
        $field[$k]["fieldname"] = "client_email";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
//        }

        $field[$k]["fieldname"] = "client_source";
        $field[$k]["fieldstring"] = "招生渠道类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "channel_name";
        $field[$k]["fieldstring"] = "招生渠道明细";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "client_createtime";
        $field[$k]["fieldstring"] = "创建日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        if ($dataList) {
            $result["fieldcustom"] = 1;
            $result["field"] = $field;
            $result["list"] = $dataList['datalist'];
            if ($result["list"]) {
                $res = array('error' => '0', 'errortip' => '获取超龄名单成功', 'allnum' => $dataList['count'], 'result' => $result);
            } else {
                $result["list"] = array();
                $res = array('error' => '1', 'errortip' => '暂无超龄名单信息', 'allnum' => 0, 'result' => $result);
            }
        } else {
            $result["list"] = array();
            $res = array('error' => '1', 'errortip' => '暂无超龄名单信息', 'allnum' => 0, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //超龄名单  --  超龄日期
    function getExceedAgeApi()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        if (!$request['school_id'] || $request['school_id'] == '') {
            $res = array('error' => '1', 'errortip' => "学校id不能为空", 'result' => array());
            ajax_return($res);
        }

        //model
        $Model = new \Model\Crm\ExceedAgeModel($request);
        $dataList = $Model->getExceedAgeApi($request);

        $field = array();
        $field["school_id"] = "学校id";
        $field["exceed_time"] = "超龄日期";

        $result = array();
        if ($dataList) {
            $result["field"] = $field;
            $result["list"] = $dataList;
            $res = array('error' => '0', 'errortip' => '获取超龄日期成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => '1', 'errortip' => '获取超龄日期失败', 'result' => $result);
        }

        ajax_return($res, $request['language_type']);
    }

    //超龄名单  --  编辑超龄列表
    function updateExceedAgeAction()
    {
        $request = Input('post.', '', 'strip_tags,trim,addslashes');
        $this->ThisVerify($request);//验证账户

        if (!$request['school_id'] || $request['school_id'] == '') {
            $res = array('error' => '1', 'errortip' => "学校id不能为空", 'result' => array());
            ajax_return($res);
        }
        //model
        $Model = new \Model\Crm\ExceedAgeModel($request);
        $dataList = $Model->updateExceedAgeAction($request);

        $field = array();
        $field["exceed_time"] = "超龄日期";

        if ($dataList) {
            $result = array();
            $result["field"] = $field;
            $result["list"] = $dataList;
            $this->addCrmWorkLog($request['company_id'], $request['school_id'], $request['marketer_id'], "超龄名单", '修改超龄日期', dataEncode($request));
            $res = array('error' => '0', 'errortip' => "超龄日期修改成功", 'result' => $result);
        } else {
            $result = array();
            $result["list"] = array();
            $res = array('error' => '1', 'errortip' => '超龄日期修改失败', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //超龄名单  --  添加超龄列表
    function addExceedAgeAction()
    {
        $request = Input('post.', '', 'strip_tags,trim,addslashes');
        $this->ThisVerify($request);//验证账户
        if (!$request['school_id'] || $request['school_id'] == '') {
            $res = array('error' => '1', 'errortip' => "学校id不能为空", 'result' => array());
            ajax_return($res);
        }
        if (!isset($request['exceed_time']) || $request['exceed_time'] == '') {
            $res = array('error' => '1', 'errortip' => '超龄日期不能为空', 'result' => array());
            ajax_return($res);
        }
        if ($this->DataControl->selectOne("select * from crm_client_exceedage WHERE school_id = '{$request['school_id']}' ")) {
            $res = array('error' => '1', 'errortip' => '超龄日期已经存在,请去修改', 'result' => array());
            ajax_return($res);
        }

        //model
        $Model = new \Model\Crm\ExceedAgeModel($request);
        $dataList = $Model->addExceedAgeAction($request);

        if ($dataList) {
            $this->addCrmWorkLog($request['company_id'], $request['school_id'], $request['marketer_id'], "超龄名单", '新增超龄日期', dataEncode($request));
            $res = array('error' => '0', 'errortip' => "超龄日期新增成功");
        } else {
            $res = array('error' => '1', 'errortip' => '超龄日期新增失败');
        }
        ajax_return($res, $request['language_type']);
    }

    //龄名单  --  删除超龄列表
    function delExceedAgeAction()
    {
        $request = Input('post.', '', 'strip_tags,trim,addslashes');
        $this->ThisVerify($request);//验证账户
        if (!$request['school_id'] || $request['school_id'] == '') {
            $res = array('error' => '1', 'errortip' => "学校id不能为空", 'result' => array());
            ajax_return($res);
        }
        //model
        $Model = new \Model\Crm\ExceedAgeModel($request);
        $bools = $Model->delExceedAgeAction($request['school_id']);

        if ($bools) {
            $this->addCrmWorkLog($request['company_id'], $request['school_id'], $request['marketer_id'], "超龄名单", '删除超龄日期', dataEncode($request));
            $res = array('error' => '0', 'errortip' => "删除成功", 'result' => array());
        } else {
            $res = array('error' => '1', 'errortip' => "删除失败", 'result' => array());
        }
        ajax_return($res, $request['language_type']);
    }
    /**
     * 转校
     * author: ling
     * 对应接口文档 0001
     */
    function transferEAClientSchoolAction()
    {
        $request = Input('post.', '', 'strip_tags,trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Crm\ExceedAgeModel($request);
        $bools = $Model->transferEAClientSchool($request);
        if ($bools) {
            $this->addCrmWorkLog($request['company_id'], $request['school_id'], $request['marketer_id'], "超龄名单", '超龄名单转校', dataEncode($request));
            $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        } else {
            $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        }
        ajax_return($res, $request['language_type']);
    }


    //结尾魔术函数
    function __destruct()
    {

    }
}
