<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/13
 * Time: 23:25
 */

namespace Work\Controller\Crmapi;


class PublicController extends viewTpl
{
    public $u;
    public $t;
    public $c;
    public $visitType = "api";
    public $DataControl;

    //预加载处理类
    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();

        //数据库操作
        $this->DataControl = new \Dbmysql();

    }

    //本地权限校验入口
    function ThisVerify($request)
    {
        $paramArray = array();
        $paramArray['marketer_id'] = $request['marketer_id'];
        if (isset($request['crm_token']) && $request['crm_token'] !== '') {
            $request['token'] = $request['crm_token'];
        }
        $paramArray['token'] = $request['token'];
        if (!$this->UserLimit($paramArray)) {
            $result = array();
            $result["list"] = array();
            $result["tokeninc"] = "0";
            $res = array('error' => '1', 'errortip' => "用户token失效", 'result' => $result);
            ajax_return($res, $request['language_type']);
        }
    }

    //用户学校切换记录
    function addStafferSchoolAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $company_id = Input('post.company_id');

        if (!$company_id) {
            $res = array('error' => '1', 'errortip' => "公司id有误", 'result' => array());
            ajax_return($res, $request['language_type']);
        }

        $school = $this->DataControl->getFieldOne("smc_school", "school_istemporaryclose,school_temporaryclosetip", "school_id = '{$request['newschool_id']}'");

        $Model = new \Model\Crm\PublicModel($request);
        $dataList = $Model->addStafferSchoolAction($request);

        if ($dataList) {
            $this->addCrmWorkLog($request['company_id'], $request['newschool_id'], $request['marketer_id'], "用户切换学校", '用户切换学校', dataEncode($request));
            $res = array('error' => '0', 'errortip' => "切换成功", 'school' => $school);
        } else {
            $res = array('error' => '1', 'errortip' => '切换失败', 'school' => $school);
        }
        ajax_return($res, $request['language_type']);
    }

    function courseTypeApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        // isFromTrack = 1  跟进这边调取班组接口时添加一个这样的参数
        $datawhere = '';
        if(isset($request['coursetype_isrecruit']) && $request['coursetype_isrecruit'] != ''){
            $datawhere .= " and c.coursetype_isrecruit = '{$request['coursetype_isrecruit']}'";
        }

        $now = date("Y-m-d", time());
        $sql = "SELECT c.coursetype_id, c.coursetype_cnname, c.coursetype_branch
FROM smc_code_coursetype c, smc_course u, smc_fee_pricing p, smc_fee_agreement a
WHERE c.company_id = '{$request['company_id']}' AND c.coursetype_id = u.coursetype_id AND u.course_status <> '-1'
AND p.agreement_id = a.agreement_id AND u.course_id = p.course_id
AND ( ( p.pricing_applytype = '1' AND p.pricing_id IN ( SELECT pricing_id FROM smc_fee_pricing_apply AS a WHERE a.school_id = '{$request['school_id']}' ) )
	OR ( p.pricing_applytype = '-1' AND p.pricing_id NOT IN ( SELECT pricing_id FROM smc_fee_pricing_apply AS a WHERE a.school_id = '{$request['school_id']}' ) )
	OR (p.pricing_applytype = '0')
)
AND a.agreement_startday <= '{$now}'
AND a.agreement_endday >= '{$now}'
AND a.agreement_status = '1' 
{$datawhere}
GROUP BY c.coursetype_id";
        $typeList = $this->DataControl->selectClear($sql);

        if ($typeList) {
            $result["list"] = $typeList;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '无班组信息,获取失败', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    //集团招生课程字段
    function courseTypeRecruitApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        // isFromTrack = 1  跟进这边调取班组接口时添加一个这样的参数
        $datawhere = '';
        if(isset($request['coursetype_isrecruit']) && $request['coursetype_isrecruit'] != ''){
            $datawhere .= " and u.coursetype_isrecruit = '{$request['coursetype_isrecruit']}'";
        }

        $now = date("Y-m-d", time());
        $sql = "SELECT u.coursetype_id,u.coursetype_cnname,u.coursetype_branch from smc_code_coursetype as u WHERE u.company_id = '{$request['company_id']}' and u.coursetype_isopenclass = '0' {$datawhere} order by coursetype_id asc";
        $typeList = $this->DataControl->selectClear($sql);

        if ($typeList) {
            $result["list"] = $typeList;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '无班组信息,获取失败', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);

    }

    //获取全部 班组
    function getAllCourseTypeApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $now = date("Y-m-d", time());

        //是否包含公开课班组
        if($request['isopenclass'] == '1' ){
            $datawhere = " and c.coursetype_isopenclass = '1' ";
        }elseif($request['isopenclass'] == '0' ){
            $datawhere = " and c.coursetype_isopenclass = '0' ";
        }

        $sql = "SELECT c.coursetype_id, c.coursetype_cnname, c.coursetype_branch
FROM smc_code_coursetype c, smc_course u, smc_fee_pricing p, smc_fee_agreement a
WHERE c.company_id = '{$request['company_id']}' AND c.coursetype_id = u.coursetype_id AND u.course_status <> '-1'
AND p.agreement_id = a.agreement_id AND u.course_id = p.course_id
AND ( ( p.pricing_applytype = '1' AND p.pricing_id IN ( SELECT pricing_id FROM smc_fee_pricing_apply AS a WHERE a.school_id = '{$request['school_id']}' ) )
	OR ( p.pricing_applytype = '-1' AND p.pricing_id NOT IN ( SELECT pricing_id FROM smc_fee_pricing_apply AS a WHERE a.school_id = '{$request['school_id']}' ) )
	OR (p.pricing_applytype = '0')
)
AND a.agreement_startday <= '{$now}'
AND a.agreement_endday >= '{$now}'
AND a.agreement_status = '1' {$datawhere}
GROUP BY c.coursetype_id";
        $typeList = $this->DataControl->selectClear($sql);

        if ($typeList) {
            $result["list"] = $typeList;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '无班组信息,获取失败', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //获取 班种接口
    function getTypeCourseCatApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $now = date("Y-m-d", time());

        $datawhere = " c.company_id = '{$request['company_id']}' and c.coursecat_iscrmadded = '1' and c.coursetype_id = t.coursetype_id and t.coursetype_isopenclass = '0' ";
        if(isset($request['coursetype_id']) && $request['coursetype_id'] != ''){
            $datawhere .= " and c.coursetype_id = '{$request['coursetype_id']}'";
        }

        $sql = " select c.coursecat_id,c.coursetype_id,c.coursecat_cnname,c.coursecat_branch from smc_code_coursecat as c,smc_code_coursetype as t where {$datawhere}";
        $typeList = $this->DataControl->selectClear($sql);

        if ($typeList) {
            $result["list"] = $typeList;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '无班种信息,获取失败', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }



    //获取沟通方式
    function getCommodeApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $company_id = $request['company_id'];

        if (!$company_id) {
            $res = array('error' => '1', 'errortip' => "公司id有误", 'result' => array());
            ajax_return($res, $request['language_type']);
        }

        $Model = new \Model\Crm\PublicModel($request);
        $dataList = $Model->getCommedeByCompanyId($company_id);

        $field['commode_id'] = "序号id";
        $field['commode_name'] = "方式名称";
        $result['field'] = $field;
        $result['fieldcustom'] = 1;
        if ($dataList) {
            $result['list'] = $dataList;
        } else {
            $result['list'] = array();
        }

        $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        ajax_return($res, $request['language_type']);
    }

    //获取无效原因模板
    function getInvalidnoteApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $company_id = $request['company_id'];

        if (!$company_id) {
            $res = array('error' => '1', 'errortip' => "公司id有误", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        $Model = new \Model\Crm\PublicModel($request);
        $dataList = $Model->getInvalidnoteApi($company_id);

        $field['invalidnote_id'] = "序号id";
        $field['invalidnote_code'] = "无效编号";
        $field['invalidnote_reason'] = "无效原因";
        $result['field'] = $field;
        if ($dataList) {
            $result['list'] = $dataList;
        } else {
            $result['list'] = array();
        }

        $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        ajax_return($res, $request['language_type']);
    }

    //获取沟通模板
    function getTraceNoteApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $company_id = $request['company_id'];

        if (!$company_id) {
            $res = array('error' => '1', 'errortip' => "公司id有误", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        $Model = new \Model\Crm\PublicModel($request);
        $dataList = $Model->getTraceNote($company_id);

        $field['tracenote_id'] = "序号id";
        $field['tracenote_code'] = "模板编号";
        $field['tracenote_remk'] = "模板内容";
        $result['field'] = $field;
        if ($dataList) {
            $result['list'] = $dataList;
        } else {
            $result['list'] = array();
        }

        $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        ajax_return($res, $request['language_type']);
    }

    //获附近学校
    function getNearSchoolApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $school_id = Input('get.school_id');

        $Model = new \Model\Crm\PublicModel($request);
        $dataList = $Model->getNearSchoolApi($school_id);

        $field['nearschool_id'] = "序号id";
        $field['nearschool_name'] = "校区名称";
        $field['nearschool_shortname'] = "学校简称";
        $result['field'] = $field;
        if ($dataList) {
            $result['list'] = $dataList;
        } else {
            $result['list'] = array();
        }

        $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        ajax_return($res, $request['language_type']);
    }

    //获取绑定关系
    function getFriendRelationApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Crm\PublicModel($request);
        $dataList = $Model->getFriendRelation();
        $field['friendrelation_id'] = "序号id";
        $field['friendrelation_name'] = "关系名称";
        $result['field'] = $field;
        if ($dataList) {
            $result['list'] = $dataList;
        } else {
            $result['list'] = array();
        }

        $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        ajax_return($res, $request['language_type'], 1);

    }

    //获取招生媒体来源
    function getSourceApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $company_id = $request['company_id'];

        if (!$company_id) {
            $res = array('error' => '1', 'errortip' => "公司id有误", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        $Model = new \Model\Crm\PublicModel($request);
        $dataList = $Model->getSourceFromMedia($company_id, '1');

        $field['frommedia_id'] = "序号id";
        $field['commode_name'] = "来源名称";
        $result['field'] = $field;
        if ($dataList) {
            $result['list'] = $dataList;
        } else {
            $result['list'] = array();
        }

        $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        ajax_return($res, $request['language_type']);
    }

    //获取渠道
    function getChannelApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $company_id = $request['company_id'];
        if (!$company_id) {
            $res = array('error' => '1', 'errortip' => "公司id有误", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        $Model = new \Model\Crm\PublicModel($request);
        $dataList = $Model->getClientChannel($request);
        $field['channel_id'] = "渠道id";
        $field['channel_name'] = "渠道名称";
        if ($dataList) {
            $result['list'] = $dataList;
        } else {
            $result['list'] = array();
        }
        $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        ajax_return($res, $request['language_type']);
    }

    //获取课程
    function getIntentCourseApi()
    {
//		course_cnname
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $company_id = $request['company_id'];
        if (!$company_id) {
            $res = array('error' => '1', 'errortip' => "公司id有误", 'result' => array());
            ajax_return($res, $request['language_type']);
        }

        $smcData['company_id'] = $company_id;
        $data = request_by_curl("https://smcapi.kedingdang.com/Course/getCousreApi", dataEncode($smcData), "GET", array());

        $data = json_decode($data, true);

        $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $data);
        ajax_return($res, $request['language_type']);
//		$Model= new \Model\Crm\PublicModel($request);
//		$dataList =$Model->getIntentCourse($company_id);
//
//		$field['course_id'] = "课程id";
//		$field['course_cnname'] = "课程名";
//		$result['field'] = $field;
//		if ($dataList) {
//			$result['list'] = $dataList;
//		} else {
//			$result['list'] = array();
//		}
//
//		$res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
//		ajax_return($res,$request['language_type']);
    }

    //获取负责人
    function getMarketerApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $company_id = Input("get.company_id");
        $marketer_id = Input('get.main_marketer_id');
        if (!$company_id) {
            $res = array('error' => '1', 'errortip' => "公司id有误", 'result' => array());
            ajax_return($res, $request['language_type']);
        }

        $Model = new \Model\Crm\PublicModel($request);

        $dataList = $Model->getMarketerList($company_id, $marketer_id, $request['school_id'],$request);

        if ($dataList) {
            $result['list'] = $dataList;
        } else {
            $result['list'] = array();
        }

        $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        ajax_return($res, $request['language_type']);

    }

    //获取负责人
    function getReMarketerApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $company_id = Input("get.company_id");
        $marketer_id = Input('get.main_marketer_id');
        if (!$company_id) {
            $res = array('error' => '1', 'errortip' => "公司id有误", 'result' => array());
            ajax_return($res, $request['language_type']);
        }

        $Model = new \Model\Crm\PublicModel($request);

        $dataList = $Model->getReMarketer($company_id, $marketer_id, $request['school_id'],$request);

        if ($dataList) {
            $result['list'] = $dataList;
        } else {
            $result['list'] = array();
        }

        $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        ajax_return($res, $request['language_type']);

    }

    //获取负责人    tmk
    function getTmkMarketerApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $company_id = Input("get.company_id");
        if (!$company_id) {
            $res = array('error' => '1', 'errortip' => "公司id有误", 'result' => array());
            ajax_return($res, $request['language_type']);
        }

        $Model = new \Model\Crm\PublicModel($request);

        $dataList = $Model->getTmkMarketerApi($company_id, $request['school_id']);

        if ($dataList) {
            $result['list'] = $dataList;
        } else {
            $result['list'] = array();
        }

        $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        ajax_return($res, $request['language_type']);

    }
    //获取柜询的类型
    function getInviteGenreApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $company_id = Input("get.company_id");
        if (!$company_id) {
            $res = array('error' => '1', 'errortip' => "公司id有误", 'result' => array());
            ajax_return($res, $request['language_type']);
        }

        $Model = new \Model\Crm\PublicModel($request);
        $dataList = $Model->getInviteGenreApi($company_id);
        if ($dataList) {
            $result['list'] = $dataList;
        } else {
            $result['list'] = array();
        }

        $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        ajax_return($res, $request['language_type']);
    }
    //获取未到访原因
    function getIsvisterReasonApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $company_id = Input("get.company_id");
        if (!$company_id) {
            $res = array('error' => '1', 'errortip' => "公司id有误", 'result' => array());
            ajax_return($res, $request['language_type']);
        }

        $Model = new \Model\Crm\PublicModel($request);

        $dataList = $Model->getIsvisterReasonList($company_id);
        if ($dataList) {
            $result['list'] = $dataList;
        } else {
            $result['list'] = array();
        }

        $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        ajax_return($res, $request['language_type']);
    }

    //删除客户亲属记录-添加
    function delFamilyRelationApi()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $family_id = Input('post.family_id');
        if (!$family_id) {
            $res = array('error' => '1', 'errortip' => "亲属关系id有误", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        $Model = new \Model\crm\PublicModel($request);
        $bools = $Model->deleteFamilyRelation($family_id);
        if ($bools) {
            $res = array('error' => '0', 'errortip' => "删除成功", 'result' => array());
        } else {
            $res = array('error' => '1', 'errortip' => "删除失败", 'result' => array());
        }
        ajax_return($res, $request['language_type']);

    }

    //删除客户的意向课程记录
    function delIntentionSourceApi()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $intention_id = Input('post.intention_id');
        if (!$intention_id) {
            $res = array('error' => '1', 'errortip' => "课程意向记录id有误", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        $Model = new \Model\crm\PublicModel($request);
        $bools = $Model->deleteIntentionSource($intention_id);
        if ($bools) {
            $res = array('error' => '0', 'errortip' => "删除成功", 'result' => array());
        } else {
            $res = array('error' => '1', 'errortip' => "删除失败", 'result' => array());
        }

        ajax_return($res, $request['language_type']);
    }

    //获取家庭成员关系-添加时选择家庭信息
    function getFamilyRelationApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Crm\PublicModel($request);
        $datalist = $Model->getFamilyRelation();

        $field = array();
        $field['familyrelation_id'] = '亲属关系类别id';
        $field['familyrelation_code'] = '关系编号';
        $field['familyrelation_name'] = '关系名称';
        $result['field'] = $field;
        if ($datalist) {
            if($request['language_type'] == 'tw') {
                //转义 errortip
                $Model = new \Model\jianfanModel();
                $data = $Model->gb2312_big5(json_encode($datalist, JSON_UNESCAPED_UNICODE));
                $datalist = json_decode($data, true);
            }
            $result['list'] = $datalist;
        } else {
            $result['list'] = array();
        }
        $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        ajax_return($res, $request['language_type']);

    }

    //获取学校班级
    function getSchoolClassApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $school_id = Input("get.school_id");
        $company_id = Input("get.company_id");
        if (!$school_id) {
            $res = array('error' => '1', 'errortip' => "学校id有误", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        if (!$company_id) {
            $res = array('error' => '1', 'errortip' => "公司id有误", 'result' => array());
            ajax_return($res, $request['language_type']);
        }

        $Model = new \Model\Crm\PublicModel();

        $dataList = $Model->getSchoolClassList($school_id, $company_id);
        if ($dataList) {
            $result['list'] = $dataList;
        } else {
            $result['list'] = array();
        }

        $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        ajax_return($res, $request['language_type']);
    }


    //获取客户标签
    function getLabelApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $company_id = $request['company_id'];
        $client_id = $request['client_id'];
        if (!$company_id) {
            $res = array('error' => '1', 'errortip' => "公司id有误", 'result' => array());
            ajax_return($res, $request['language_type']);
        }

        $Model = new \Model\Crm\PublicModel();
        $dataList = $Model->getLabelApi($company_id, $client_id,$request);

        if ($dataList['label']) {
            $result['list'] = $dataList;
            ajax_return(array('error' => '0', 'errortip' => "获取成功", 'result' => $result), $request['language_type']);
        } else {
            $result['list'] = $dataList;
            ajax_return(array('error' => '1', 'errortip' => "暂无常用标签哦~", 'result' => $result), $request['language_type']);
        }

    }


    //获取地推人员
    function getGroundPromotionApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        if (!$request['company_id']) {
            $res = array('error' => '1', 'errortip' => "公司id有误", 'result' => array());
            ajax_return($res, $request['language_type']);
        }

        $Model = new \Model\Crm\PublicModel();
        $dataList = $Model->getGroundPromotionApi($request);

        if ($dataList['list']) {
            $result['list'] = $dataList['list'];
            ajax_return(array('error' => '0', 'errortip' => "获取成功", 'result' => $result), $request['language_type']);
        } else {
            $result['list'] = array();
            ajax_return(array('error' => '1', 'errortip' => "暂无地推人员信息", 'result' => $result), $request['language_type']);
        }
    }

    /**
     * 意向星级列表
     */
    public function getIntentionLevelListView()
    {
        $request = Input('GET.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $crmModel = new \Model\Crm\PublicModel();
        $crmModel->getIntentionLevelList($request['company_id']);

        $field = array();
        $field["intentlevel_starnum"] = "意向星级";
        $field["intentlevel_remark"] = "意向星级注释（级别说明）";
        $field["intentlevel_trackday"] = "跟踪频率（天）";
        $field["intentlevel_warningday"] = "预警设定（天）";
        $field["intentlevel_describe"] = "说明（备注）";

        $result = array();

        $result["field"] = $field;
        $result["list"] = $crmModel->result;

        ajax_return(array('error' => $crmModel->error, 'errortip' => $crmModel->errortip, 'result' => $result));
    }


}