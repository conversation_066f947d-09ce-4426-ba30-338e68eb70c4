<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/13
 * Time: 23:25
 * crm-首页
 */

namespace Work\Controller\Crmapi;

class LoginController extends viewTpl
{
    public $u;
    public $t;
    public $c;
    public $visitType = "api";
    public $Show_css;


    //预加载处理类
    function __construct($visitType = "api")
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();

        //数据库操作
        $this->DataControl = new \Dbsqlplay();
    }

    //本地权限校验入口
    function ThisVerify($request)
    {
        $paramArray = array();
        $paramArray['company_id'] = $request['company_id'];
        $paramArray['school_id'] = $request['school_id'];
        $paramArray['staffer_id'] = $request['staffer_id'];
        $paramArray['token'] = $request['token'];
        $apiuser = $this->StafferLimit($paramArray);
        if (!$apiuser) {
            $result = array();
            $result["list"] = array();
            $result["tokeninc"] = "0";
            $res = array('error' => '1', 'errortip' => "用户token失效", 'result' => $result);
            ajax_return($res, $request['language_type']);
        } else {
            if ($apiuser['account_class'] == '1') {
                //主账号直接通过
            } else {
                if (isset($request['re_postbe_id']) && $request['re_postbe_id']) {
                    $postrole = $this->DataControl->getFieldOne("gmc_staffer_postbe", "postbe_id,school_id,postbe_iscrmuser,postrole_id", "postbe_id = '{$request['re_postbe_id']}'");
                    if ($postrole['school_id'] == '0') {
                        $status = $this->DataControl->getFieldOne("gmc_company_postrole", "postpart_iscrmuser", "postrole_id = '{$postrole['postrole_id']}'");
                        if ($status['postpart_iscrmuser'] !== '1') {
                            $result = array();
                            $result["isCrm"] = "-1";
                            $result["data"] = array();
                            $res = array('error' => '1', 'errortip' => '您的集团职务没有CRM权限', 'result' => $result);
                            ajax_return($res, $request['language_type']);
                        }
                    } else {
                        if ($postrole['postbe_iscrmuser'] !== '1') {
                            $result = array();
                            $result["isCrm"] = "-1";
                            $result["data"] = array();
                            $res = array('error' => '1', 'errortip' => '您的校园职务没有CRM权限', 'result' => $result);
                            ajax_return($res, $request['language_type']);
                        }
                    }
                }
            }
        }
    }

    //首页 -- 用户登陆获取信息
    function HomeView()
    {
        $request = Input('get.', '', 'trim,addslashes');
//        $this->ThisVerify($request);//验证账户 staffer_id

        $Model = new \Model\Crm\CommonModel($request);
        $marketerOne = $this->DataControl->getFieldOne("crm_marketer", "marketer_id", "staffer_id = '{$request['staffer_id']}'");

        if (!$marketerOne) {
            $Model->addMarketerLoginApi($request);
        }
        $dataList = $Model->marketerLoginApi($request);

        $field = array();
        $field["marketer_id"] = "教师ID";
        $field["company_id"] = "所属集团";
        $field["staffer_id"] = "所属教师ID";
        $field["postrole_id"] = "权限角色ID";
        $field["marketer_name"] = "销售姓名";
        $field["marketer_mobile"] = "销售手机";
        $field["marketer_img"] = "头像";
        $field["marketer_tokenencrypt"] = "加密值";
        $field["school_id"] = "默认学校id";
        $field["school_name"] = "默认校区名称";
        $field["staffer_sex"] = "教师性别";
        $field["postbe_crmuserlevel"] = "是否高管账号";

        $post = $this->DataControl->selectClear("
            SELECT
                b.postbe_id,
                b.postbe_ismianjob,
                s.school_cnname,
                p.post_name,m.marketer_id,m.company_id,m.staffer_id,m.postrole_id,m.marketer_name,m.marketer_mobile,m.marketer_img,m.marketer_tokenencrypt,b.school_id,b.postbe_crmuserlevel,t.staffer_sex,t.staffer_enname,t.staffer_branch 
            FROM
                gmc_staffer_postbe AS b
                LEFT JOIN smc_school AS s ON s.school_id = b.school_id
                left join gmc_company_post as p on b.post_id = p.post_id
                left join smc_staffer as t on t.staffer_id = b.staffer_id
                left join crm_marketer as m on m.staffer_id = b.staffer_id
            WHERE
                b.postbe_id = '{$request['re_postbe_id']}' and b.school_id > 0 and s.school_isclose = '0'
                order by b.postbe_ismianjob DESC");

        $img = $this->DataControl->getFieldOne("gmc_company", "company_logo", "company_id = '{$request['company_id']}'");

        if ($post) {
            foreach ($post as &$v) {
                $postroleOne = $this->DataControl->selectOne(" select postpart_iscrmuser,postpart_crmuserlevel from gmc_company_postrole where postrole_id = '{$v['postrole_id']}' ");//有crm访问权限时的权限：0普通权限1高管权限2电销权限 3市场  20230330补充
                if($postroleOne['postpart_iscrmuser'] == '1' && isset($postroleOne['postpart_crmuserlevel'])){//有crm访问权限时的权限：0普通权限1高管权限2电销权限 3市场 20230330补充
                    $v['postbe_crmuserlevel'] = $postroleOne['postpart_crmuserlevel'];
                }

                if ($v['marketer_img'] == '') {
                    $a = $this->DataControl->getFieldOne("smc_staffer", "staffer_img", "staffer_id = '{$request['staffer_id']}'");
                    $v['marketer_img'] = $a['staffer_img'];
                }
                $v['company_logo'] = $img['company_logo'];
                $v['marketer_name'] = $v['marketer_name'] . ((isset($v['staffer_enname']) && $v['staffer_enname'] != '') ? '-' . $v['staffer_enname'] : '');
            }
        } else {
            $post = array();
        }

        if ($request['re_postbe_id'] == '0') {
            $status = '1';
        } else {
            $a = $this->DataControl->getFieldOne("gmc_staffer_postbe", "school_id,postrole_id,postbe_iscrmuser", "postbe_id = '{$request['re_postbe_id']}'");
            if ($a['school_id'] == '0') {
                $b = $this->DataControl->getFieldOne("gmc_company_postrole", "postpart_iscmsuser", "postrole_id = '{$a['postrole_id']}'");
                if ($b['postpart_iscmsuser'] == '1') {
                    $status = '1';
                } else {
                    $status = '0';
                }
            } else {
                $status = '1';
            }
        }

        $schoolOne = $this->DataControl->getFieldOne("smc_school", "school_istemporaryclose,school_temporaryclosetip", "school_id = '{$request['school_id']}'");

        $result = array();

        $result["field"] = $field;
        $result["data"] = $dataList;
        $result["post"] = $post;
        $result["school"] = $schoolOne;
        $res = array('error' => '0', 'errortip' => '获取用户基本信息成功', 'result' => $result, 'status' => $status);
        ajax_return($res, $request['language_type']);
    }

}