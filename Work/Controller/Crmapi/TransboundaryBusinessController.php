<?php
/**
 * 跨界活动-商户端
 * 须登录
 */

namespace Work\Controller\Crmapi;

class TransboundaryBusinessController extends viewTpl
{
    public $u;
    public $t;
    public $c;
    public $business_id;

    //预加载处理类
    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
        $request = Input('POST.','','trim,addslashes');
        $info = $this->verifyLogin($request['token'],'business');
        $this->business_id = $info['business_id'];
    }

    /**
     * 秘钥校验及解析
     * @param $token
     */
    function verifyLogin($token,$end)
    {
        if(empty($token) || empty($end)){
            $this->error = 1;
            $this->errortip = "token和end必须传入";
            $this->result = array();
            ajax_return(array('error' => $this->error, 'errortip' => $this->errortip, 'result' => $this->result));
        }
        $param = array();
        $param['token'] = $token;
        $param['end'] = $end;

        //校验解析token
        $ucsTokenModel = new \Model\Crm\TransboundaryTokenModel();
        $data = $ucsTokenModel->verification($param);

        return $data;
    }

    /**
     * 商家活动列表
     */
    public function activityListView()
    {
        $request = Input('POST.', '', 'trim,addslashes');
        $BusinessModel = new \Model\Crm\TransboundaryBusinessModel();
        $request['business_id'] = $this->business_id;
        $BusinessModel->activityList($request);

        $field = array();
        $field['activity_id'] = '活动ID';
        $field['activity_name'] = '活动名称';
        $field['activity_startdate'] = '开始日期';
        $field['activity_enddate'] = '结束日期';
        $field['activity_settleamount'] = '已结算金额';
        $field['activity_paymentamount'] = '已发放金额';
        $field['activity_unsettleamount'] = '未结算金额';
        $field['activity_status'] = '状态 0:未开始 1:进行中 2:已结束';

        $result = array();
        $result['field'] = $field;
        $result['data'] = $BusinessModel->result;

        ajax_return(array('error' => $BusinessModel->error, 'errortip' => $BusinessModel->errortip, 'result' => $result));
    }

    /**
     * 活动参与列表
     */
    public function activityRecordListView()
    {
        $request = Input('POST.', '', 'trim,addslashes');
        $BusinessModel = new \Model\Crm\TransboundaryBusinessModel();
        $BusinessModel->activityRecordList($request);

        $field = array();
        $field['user_nickname'] = '昵称';
        $field['user_mobile'] = '手机号';
        $field['record_createdate'] = '抽奖时间';
        $field['record_winningamount'] = '中奖金额';
        $field['record_settlementstatus'] = '结算状态 0:未结算 1:已结算';

        $result = array();
        $result['field'] = $field;
        $result['data'] = $BusinessModel->result;

        ajax_return(array('error' => $BusinessModel->error, 'errortip' => $BusinessModel->errortip, 'result' => $result));
    }


}