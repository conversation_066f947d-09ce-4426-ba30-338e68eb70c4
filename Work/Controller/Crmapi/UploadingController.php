<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn 
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2016/12/16
 * Time: 14:45
 */

namespace Work\Controller\Crmapi;
use OSS\OssClient;
use OSS\Core\OssException;


class UploadingController  extends viewTpl{
    public $data;
    public $u;
    public $t;
    public $c;
    //预加载处理类
    function __construct() {
        parent::__construct ();
    }
    //上传图片路径
    function PictureView()
    {
        $request = Input('post.','','trim,addslashes');
        if (!$request['company_id']) {
            $res = array('error' => '1', 'errortip' => "请传入公司ID", 'result' => array());
            ajax_return($res,$request['language_type']);
        }
        if (! empty ( $_FILES ["ossfile"] )) {
            $md5file = md5_file($_FILES['ossfile']['tmp_name']);
            $getTimg = $this->DataControl->getFieldOne('gmc_upload_picture',"picture_id,picture_imgurl,picture_thumburl","picture_md5='{$md5file}' and company_id = '{$request['company_id']}'");
            if($getTimg){
                $result = array();
                $result['picture_imgurl'] = $getTimg['picture_imgurl'];
                $result['picture_thumburl'] = $getTimg['picture_thumburl'];
                $res = array('error' => 0,'errortip' => "图片上传成功!","result"=>$result);
                ajax_return($res,$request['language_type']);
            }else{
                $md5file = md5_file($_FILES['ossfile']['tmp_name']);
                $imglink = UpOssFile($_FILES);
                if($imglink){
                    $date =array();
                    $date['company_id'] = $request['company_id'];
                    $date['picture_name'] = $_FILES['ossfile']['tmp_name'];
                    $date['picture_imgurl'] = $imglink;
                    $date['picture_thumburl'] = $imglink;
                    $date['picture_md5'] = $md5file;
                    $this->DataControl->insertData('gmc_upload_picture',$date);

                    $result = array();
                    $result['picture_imgurl'] = $date['picture_imgurl'];
                    $result['picture_thumburl'] = $date['picture_thumburl'];
                    $res = array('error' => 0,'errortip' => "图片上传成功!","result"=>$result);
                    ajax_return($res,$request['language_type']);
                }else{
                    $res = array('error' => 1,'errortip' => "图片上传失败!","result"=>array());
                    ajax_return($res,$request['language_type']);
                }
            }
        }else{
            $res = array('error' => 1,'errortip' => "您未选择任何图片上传!","result"=>array());
            ajax_return($res,$request['language_type']);
        }
    }
    //OSS上传文件路径
    function FileView(){
        $request = Input('post.','','trim,addslashes');

        if (!$request['company_id']) {
            $res = array('error' => '1', 'errortip' => "请传入公司ID", 'result' => array());
            ajax_return($res);
        }
        $this->c="Oss";
        $md5file = md5_file($_FILES['ossfile']['tmp_name']);
        $fileurl = UpOssFile($_FILES);

        $date = array();
        $date['company_id'] = $request['company_id'];
        $date['file_name'] = $_FILES['ossfile']['filename'];
        $date['file_url'] = $fileurl;
        $date['file_md5'] = $md5file;
        $this->DataControl->insertData('gmc_upload_file', $date);

        $result = array();
        $result['file_url'] = $fileurl;
        $res = array('error' => 0,'errortip' => "文件上传成功!","result"=>$result);
        ajax_return($res);
    }
}
