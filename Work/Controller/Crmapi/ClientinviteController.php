<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/13
 * Time: 23:25
 */

namespace Work\Controller\Crmapi;


class ClientinviteController extends viewTpl
{
    public $u;
    public $t;
    public $c;
    public $visitType = "api";
    public $DataControl;
    public $Model;
    public $company_isassist;

    //预加载处理类
    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();

        //数据库操作
        $this->DataControl = new \Dbmysql();
    }

    //验证
    function ThisVerify($request)
    {
        if (!intval($request['marketer_id'])) {
            $res = array('error' => '1', 'errortip' => "未检测到招生人员信息，请重新登录操作", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        if (isset($request['crm_token']) && $request['crm_token'] !== '') {
            $request['token'] = $request['crm_token'];
        }
        if (isset($request['company_id']) && $request['company_id'] !== '') {
            $companyOne = $this->DataControl->getFieldOne("gmc_company", "company_isassist", "company_id='{$request['company_id']}'");
            $this->company_isassist = $companyOne['company_isassist'];
        }
        if (empty($request['token'])) {
            $res = array('error' => '1', 'errortip' => "未传入用户Token信息", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        $paramArray = array();
        $paramArray['marketer_id'] = $request['marketer_id'];
        $paramArray['token'] = $request['token'];
        if (!$this->UserLimit($paramArray)) {
            $result = array();
            $result["list"] = array();
            $result["tokeninc"] = "0";
            $res = array('error' => '1', 'errortip' => "用户token失效", 'result' => $result);
            ajax_return($res, $request['language_type']);
        }
    }

//	 柜询管理首页 ---一级页面
    function HomeView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        if ($request['school_id'] == "") {
            $res = array('error' => '1', 'errortip' => "学校id错误", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        if ($request['company_id'] == "") {
            $res = array('error' => '1', 'errortip' => "公司id错误", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        if ($request['marketer_id'] == "") {
            $res = array('error' => '1', 'errortip' => "登录人id错误", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        $Model = new \Model\Crm\ClientinviteModel($request);
        $dataList = $Model->getClientinviteList($request);

        $k = 0;
        $field[$k]["fieldstring"] = "姓名";
        $field[$k]["fieldname"] = "client_cnname";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '0';
        $field[$k]["is_method"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "英文名";
        $field[$k]["fieldname"] = "client_enname";
        $field[$k]["show"] = '0';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "标签";
        $field[$k]["fieldname"] = "client_tag";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $field[$k]["istag"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "性别";
        $field[$k]["fieldname"] = "client_sex";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "年龄";
        $field[$k]["fieldname"] = "client_age";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "学生类型";
        $field[$k]["fieldname"] = "invite_type_name";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "意向星级";
        $field[$k]["fieldname"] = "client_intention_level";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $field[$k]["islevel"] = 'true';
        $k++;
        $field[$k]["fieldstring"] = "柜询状态";
        $field[$k]["fieldname"] = "invite_isvisit_name";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "柜询班组";
        $field[$k]["fieldname"] = "coursetype_cnname";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "柜询类型";
        $field[$k]["fieldname"] = "invite_genre";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "柜询日期";
        $field[$k]["fieldname"] = "invite_visittime";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "未到访原因";
        $field[$k]["fieldname"] = "invite_novisitreason";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        if (isset($request['invite_isvisit']) && ($request['invite_isvisit'] == '2' || $request['invite_isvisit'] == '')) {
            $field[$k]["fieldstring"] = "改约原因";
            $field[$k]["fieldname"] = "invite_novisitreason_two";
            $field[$k]["show"] = '1';
            $field[$k]["custom"] = '1';
            $k++;
        }
        $field[$k]["fieldstring"] = "报名状态";
        $field[$k]["fieldname"] = "conversionlog_id_name";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "主要负责人";
        $field[$k]["fieldname"] = "marketer_name";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "协助负责人";
        $field[$k]["fieldname"] = "fu_marketer_name";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "接待人";
        $field[$k]["fieldname"] = "receiver_name";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "主要联系人";
        $field[$k]["fieldname"] = "family_cnname";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "主要联系电话";
        $field[$k]["fieldname"] = "client_mobile";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $field[$k]["isTypeTab"] = 1;
        $k++;
//        if($request['language_type'] == 'tw'){
        $field[$k]["fieldstring"] = "Email";
        $field[$k]["fieldname"] = "client_email";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
//        }
        $field[$k]["fieldstring"] = "意向课程";
        $field[$k]["fieldname"] = "course_cnname";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $field[$k]["istag"] = '1';
        $k++;
        if (isset($request['invite_isvisit']) && $request['invite_isvisit'] == 1) {
            $field[$k]["fieldstring"] = "地推工号";
            $field[$k]["fieldname"] = "promotion_jobnumber";
            $field[$k]["show"] = '1';
            $field[$k]["custom"] = '1';
            $k++;
            $field[$k]["fieldstring"] = "职务";
            $field[$k]["fieldname"] = "post_name";
            $field[$k]["show"] = '1';
            $field[$k]["custom"] = '1';
            $k++;
        }
        $field[$k]["fieldstring"] = "渠道类型";
        $field[$k]["fieldname"] = "client_source";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "渠道明细";
        $field[$k]["fieldname"] = "channel_name";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "接触点";
        $field[$k]["fieldname"] = "client_frompage";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldname"] = "client_createtime";
        $field[$k]["show"] = '1';
        $field[$k]["fieldstring"] = "创建时间";
        $field[$k]["custom"] = '1';
        $k++;

        $field[$k]["fieldstring"] = "活动";
        $field[$k]["fieldname"] = "activity_name";
        $field[$k]["show"] = '0';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "推荐人";
        $field[$k]["fieldname"] = "client_sponsor";
        $field[$k]["show"] = '0';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "联系地址";
        $field[$k]["fieldname"] = "client_address";
        $field[$k]["show"] = '0';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "省";
        $field[$k]["fieldname"] = "province_name";
        $field[$k]["show"] = '0';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "市";
        $field[$k]["fieldname"] = "city_name";
        $field[$k]["show"] = '0';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "区";
        $field[$k]["fieldname"] = "area_name";
        $field[$k]["show"] = '0';
        $field[$k]["custom"] = '1';

        if ($request['invite_isvisit'] <> 0) {
            $k++;
            $field[$k]["fieldstring"] = "附近的学校";
            $field[$k]["fieldname"] = "nearschool_name";
            $field[$k]["show"] = '0';
            $field[$k]["custom"] = '1';
        }

        $k++;
        $field[$k]["fieldname"] = "client_remark";
        $field[$k]["show"] = '0';
        $field[$k]["fieldstring"] = "备注";
        $field[$k]["custom"] = '1';
//        $k++;
//        $field[$k]["fieldname"] = "marketer_writername";
//        $field[$k]["show"] = '1';
//        $field[$k]["fieldstring"] = "录入人";
//        $field[$k]["custom"] = '1';


        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($dataList['list']) {
            $result['list'] = $dataList['list'];
        } else {
            $result['list'] = array();
        }
        if (isset($dataList['allnums']) && $dataList['allnums'] != "") {
            $allnum = intval($dataList['allnums']);
        } else {
            $allnum = '0';
        }
        if ($result['list']) {
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result, 'allnum' => $allnum);
        } else {
            if ($request['invite_isvisit'] == '1') {
                $res = array('error' => '1', 'errortip' => "暂无已到访过的柜询记录", 'result' => $result, 'allnum' => $allnum);
            } elseif ($request['invite_isvisit'] == '-1') {
                $res = array('error' => '1', 'errortip' => "暂无未到访的柜询记录", 'result' => $result, 'allnum' => $allnum);
            } else {
                $res = array('error' => '1', 'errortip' => "暂无柜询记录", 'result' => $result, 'allnum' => $allnum);
            }
        }
        ajax_return($res, $request['language_type']);
    }

//	 视听管理首页 ---一级页面
    function auditionView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        if ($request['school_id'] == "") {
            $res = array('error' => '1', 'errortip' => "学校id错误", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        if ($request['company_id'] == "") {
            $res = array('error' => '1', 'errortip' => "公司id错误", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        if ($request['marketer_id'] == "") {
            $res = array('error' => '1', 'errortip' => "登录人id错误", 'result' => array());
            ajax_return($res, $request['language_type']);
        }

        $Model = new \Model\Crm\ClientinviteModel($request);
        $auditionList = $Model->getClientauditionList($request);

        $k = 0;
        $field[$k]["fieldstring"] = "姓名";
        $field[$k]["fieldname"] = "client_cnname";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '0';
        $field[$k]["is_method"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "英文名";
        $field[$k]["fieldname"] = "client_enname";
        $field[$k]["show"] = '0';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "标签";
        $field[$k]["fieldname"] = "client_tag";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $field[$k]["istag"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "性别";
        $field[$k]["fieldname"] = "client_sex";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "年龄";
        $field[$k]["fieldname"] = "client_age";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "学员类型";
        $field[$k]["fieldname"] = "audition_type_name";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "意向星级";
        $field[$k]["fieldname"] = "client_intention_level";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $field[$k]["islevel"] = 'true';
        $k++;
        $field[$k]["fieldstring"] = "试听状态";
        $field[$k]["fieldname"] = "visitstate";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "试听班组";
        $field[$k]["fieldname"] = "coursetype_cnname";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "试听类型";
        $field[$k]["fieldname"] = "audition_genre";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "试听日期";
        $field[$k]["fieldname"] = "audition_visittime";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "试听班级";
        $field[$k]["fieldname"] = "class_cnname";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "未试听原因";
        $field[$k]["fieldname"] = "audition_novisitreason";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        if (isset($request['audition_isvisit']) && ($request['audition_isvisit'] == '2' || $request['audition_isvisit'] == '')) {
            $field[$k]["fieldstring"] = "改约原因";
            $field[$k]["fieldname"] = "audition_novisitreason_two";
            $field[$k]["show"] = '1';
            $field[$k]["custom"] = '1';
            $k++;
        }
        $field[$k]["fieldstring"] = "报名状态";
        $field[$k]["fieldname"] = "conversionlog_id_name";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "主要负责人";
        $field[$k]["fieldname"] = "marketer_name";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "协助负责人";
        $field[$k]["fieldname"] = "fu_marketer_name";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "接待人";
        $field[$k]["fieldname"] = "receiver_name";
        $field[$k]["show"] = '0';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "主要联系人";
        $field[$k]["fieldname"] = "parenter_cnname";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "主要联系手机";
        $field[$k]["fieldname"] = "client_mobile";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $field[$k]["isTypeTab"] = 1;
        $k++;
//        if($request['language_type'] == 'tw'){
        $field[$k]["fieldstring"] = "Email";
        $field[$k]["fieldname"] = "client_email";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
//        }
        $field[$k]["fieldstring"] = "意向课程";
        $field[$k]["fieldname"] = "course_cnname";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $field[$k]["istag"] = '1';
        $k++;
        if (isset($request['audition_isvisit']) && $request['audition_isvisit'] == 1) {
            $field[$k]["fieldstring"] = "地推工号";
            $field[$k]["fieldname"] = "promotion_jobnumber";
            $field[$k]["show"] = '1';
            $field[$k]["custom"] = '1';
            $k++;
            $field[$k]["fieldstring"] = "职务";
            $field[$k]["fieldname"] = "post_name";
            $field[$k]["show"] = '1';
            $field[$k]["custom"] = '1';
            $k++;
        }
        $field[$k]["fieldstring"] = "渠道类型";
        $field[$k]["fieldname"] = "client_source";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "渠道明细";
        $field[$k]["fieldname"] = "channel_name";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "接触点";
        $field[$k]["fieldname"] = "client_frompage";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "创建时间";
        $field[$k]["fieldname"] = "client_createtime";
        $field[$k]["show"] = '1 ';
        $field[$k]["custom"] = '1';

//        $k++;
//        if ($this->company_isassist == 1) {
//            $k++;
//            $field[$k]["fieldstring"] = "协助负责人";
//            $field[$k]["fieldname"] = "fu_marketer_name";
//            $field[$k]["show"] = '1';
//            $field[$k]["custom"] = '1';
//        }
//        $k++;
//        $field[$k]["fieldstring"] = "活动";
//        $field[$k]["fieldname"] = "activity_name";
//        $field[$k]["show"] = '0';
//        $field[$k]["custom"] = '1';
//        $k++;
//        $field[$k]["fieldstring"] = "推荐人";
//        $field[$k]["fieldname"] = "client_sponsor";
//        $field[$k]["show"] = '0';
//        $field[$k]["custom"] = '1';
//        $k++;
//        $field[$k]["fieldstring"] = "联系地址";
//        $field[$k]["fieldname"] = "client_address";
//        $field[$k]["show"] = '0';
//        $field[$k]["custom"] = '1';
//        $k++;
//        $field[$k]["fieldstring"] = "省";
//        $field[$k]["fieldname"] = "province_name";
//        $field[$k]["show"] = '0';
//        $field[$k]["custom"] = '1';
//        $k++;
//        $field[$k]["fieldstring"] = "市";
//        $field[$k]["fieldname"] = "city_name";
//        $field[$k]["show"] = '0';
//        $field[$k]["custom"] = '1';
//        $k++;
//        $field[$k]["fieldstring"] = "区";
//        $field[$k]["fieldname"] = "area_name";
//        $field[$k]["show"] = '0';
//        $field[$k]["custom"] = '1';
//        $k++;
//        $field[$k]["fieldstring"] = "附近学校";
//        $field[$k]["fieldname"] = "nearschool_name";
//        $field[$k]["show"] = '0';
//        $field[$k]["custom"] = '1';
//        $k++;
//        $field[$k]["fieldstring"] = "备注";
//        $field[$k]["fieldname"] = "client_remark";
//        $field[$k]["show"] = '0 ';
//        $field[$k]["custom"] = '1';
//        $k++;
//        $field[$k]["fieldstring"] = "录入人";
//        $field[$k]["fieldname"] = "marketer_writername";
//        $field[$k]["show"] = '1 ';
//        $field[$k]["custom"] = '1';

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($auditionList['list']) {
            $result['list'] = $auditionList['list'];
            $result['classList'] = $auditionList['classList'];
        } else {
            $result['list'] = array();
            $result['classList'] = array();
        }
        if (isset($auditionList['allnums']) && $auditionList['allnums'] != "") {
            $allnum = intval($auditionList['allnums']);
        } else {
            $allnum = 0;
        }
        if ($result['list']) {
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result, 'allnum' => $allnum);
        } else {
            $res = array('error' => '1', 'errortip' => "暂无试听记录", 'result' => $result, 'allnum' => $allnum);
        }
        ajax_return($res, $request['language_type']);

    }

    //柜询到访或未到访设置
    function isvisitApi()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        if (!$request['invite_id']) {
            $res = array('error' => '1', 'errortip' => "id错误", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        if ($request['invite_isvisit'] == "") {
            $res = array('error' => '1', 'errortip' => "请填入到访状态", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        $Model = new \Model\Crm\ClientinviteModel($request);
        $Model->setIsVisit($request);
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $Model->result);
        if ($request['invite_isvisit'] == '1') {
            $word = '柜询确认到访';
        } elseif ($request['invite_isvisit'] == '-1') {
            $word = '柜询确认未到访';
        }
        $this->addCrmWorkLog($request['company_id'], $request['school_id'], $request['marketer_id'], "柜询试听管理->柜询记录管理", $word, dataEncode($request));

        ajax_return($res, $request['language_type']);
    }

    //试听成功 或 取消试听
    function isauditionApi()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        if (!$request['audition_id']) {
            $res = array('error' => '1', 'errortip' => "id错误", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        if ($request['audition_isvisit'] == "") {
            $res = array('error' => '1', 'errortip' => "请填入试听状态", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        $Model = new \Model\Crm\ClientinviteModel($request);
        $Model->setIsaudition($request);

        if ($request['audition_isvisit'] == '1') {
            $word = '试听确认到访';
        } elseif ($request['audition_isvisit'] == '-1') {
            $word = '试听确认取消';
        }
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $Model->result);
        $this->addCrmWorkLog($request['company_id'], $request['school_id'], $request['marketer_id'], "柜询试听管理->柜询记录管理", $word, dataEncode($request));

        ajax_return($res, $request['language_type']);
    }

    // 批量設置試聽到訪
    function batchSettingAuditionAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        if (!$request['audition_id']) {
            $res = array('error' => '1', 'errortip' => "id错误", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        if ($request['audition_isvisit'] == "") {
            $res = array('error' => '1', 'errortip' => "请填入试听状态", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        $Model = new \Model\Crm\ClientinviteModel($request);
        $Model->batchSettingAudition($request);
        if ($request['audition_isvisit'] == '1') {
            $word = '批量试听成功';
        } elseif ($request['audition_isvisit'] == '-1') {
            $word = '批量取消试听';
        }
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        $this->addCrmWorkLog($request['company_id'], $request['school_id'], $request['marketer_id'], "柜询试听管理->柜询试听管理", $word, dataEncode($request));
        ajax_return($res, $request['language_type']);
    }

    // 批量设置邀约到访成功
    function batchSetIsVisitAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        if (!$request['invite_id']) {
            $res = array('error' => '1', 'errortip' => "id错误", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        if ($request['invite_isvisit'] == "") {
            $res = array('error' => '1', 'errortip' => "请填入柜询状态", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        $Model = new \Model\Crm\ClientinviteModel($request);
        $Model->batchSetIsVisit($request);
        if ($request['invite_isvisit'] == '1') {
            $word = '批量设置柜询成功';
        } elseif ($request['invite_isvisit'] == '-1') {
            $word = '批量设置柜询试听';
        }
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        $this->addCrmWorkLog($request['company_id'], $request['school_id'], $request['marketer_id'], "柜询试听管理->柜询试听管理", $word, dataEncode($request));
        ajax_return($res, $request['language_type']);
    }

    //获取试听班级
    function getAuditionApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        if (!$request['school_id']) {
            $res = array('error' => '1', 'errortip' => "学校id错误", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        if (isset($request['audition_genre']) && $request['audition_genre'] !== '') {
            $smcData['audition_genre'] = $request['audition_genre'];
        } else {
            $res = array('error' => '1', 'errortip' => "请选择试听类型", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        $CourseModel = new \Model\Smc\CourseModel($request);
        if ($request['audition_genre'] == 0 || $request['audition_genre'] == 2) {
            $dataList = $CourseModel->getPucList($request,'crm');
        } else {
            $dataList = $CourseModel->getAuditionHour($request);
        }

        $fieldstring = array('class_cnname', 'class_branch', 'class_appointnum', 'course_cnname', 'course_branch', 'hour_day', 'hour_time', 'classroom_cnname', 'staffer_cnname', 'class_num', 'hour_num');
        $fieldname = array('班级名称', '班级编号', '可预约人数', '课程名', '课程别编号', '上课日期', '上课时间', '教室', '教师', '在班人数', '计划/已上课时');
        $fieldcustom = array('1', '1', "1", "1", "1", '1', '1', '1', '1', '1');
        $fieldshow = array('1', '1', "1", "1", "1", '1', '1', '1', '1', '1');
        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["fieldname"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }
        $result['field'] = $field;
        $result['list'] = $dataList['data'];
        $result['courselist'] = $dataList['courselist'];
        $res = array('error' => '0', 'errortip' => "获取试听课", 'result' => $result);
        ajax_return($res, $request['language_type']);
    }


    //获取学校的教师
    function getAuditionStafferApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        if (!$request['school_id']) {
            $res = array('error' => '1', 'errortip' => "学校id错误", 'result' => array());
            ajax_return($res, $request['language_type']);
        }

        $CourseModel = new \Model\Smc\CourseModel($request);
        $dataList = $CourseModel->getAuditionTeacher($request);
        $field[0]["fieldstring"] = 'staffer_id';
        $field[0]["fieldname"] = '教师id';
        $field[0]["custom"] = '0';
        $field[0]["show"] = '1';
        $field[1]["fieldstring"] = 'staffer_cnname';
        $field[1]["fieldname"] = '教师';
        $field[1]["custom"] = '0';
        $field[1]["show"] = '1';

        $result['field'] = $field;
        $result['list'] = $dataList;
        $res = array('error' => '0', 'errortip' => "获取学校的教师", 'result' => $result);
        ajax_return($res, $request['language_type']);

    }

    //试听 与 柜询的跟进
    function inviteTrackApi()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        if (!$request['client_id']) {
            $res = array('error' => '1', 'errortip' => "客户id错误", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        if (!$request['marketer_id']) {
            $res = array('error' => '1', 'errortip' => "负责人id错误", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        if (!isset($request['client_answerphone'])) {
            $res = array('error' => '1', 'errortip' => "请选择是否接通", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        if ($request['client_answerphone'] <> 0) {
            if (($request['track_followmode'] == 1) && $request['invite_visittime'] == "") {
                $res = array('error' => '1', 'errortip' => "请选择柜询时间", 'result' => array());
                ajax_return($res, $request['language_type']);
            }
    //		if($request['track_followmode'] == 2 && $request['audition_visittime']==""){
    //			$res = array('error' => '1', 'errortip' => "请选择试听时间", 'result' => array());
    //			ajax_return($res,$request['language_type']);
    //		}
    //		if($request['track_followmode'] == 2 && $request['class_id']==""){
    //			$res = array('error' => '1', 'errortip' => "请选择班级", 'result' => array());
    //			ajax_return($res,$request['language_type']);
    //		}
            if ($request['track_followuptype'] == 1 && $request['track_followuptime'] == "") {
                $res = array('error' => '1', 'errortip' => "请选择下次跟进时间", 'result' => array());
                ajax_return($res, $request['language_type']);
            }
        }

        // 检测是否可以跟进
        $ClientModel = new \Model\Crm\ClientModel($request);

        if (!$ClientModel->checkIsTrack($request['client_id'], $request['marketer_id'])) {
            $res = array('error' => '1', 'errortip' => "非您的意向客户", 'result' => array());
            ajax_return($res, $request['language_type']);
        }

        $insert_bools = $ClientModel->insertTrackClientOne($request);
        if ($insert_bools == true) {
            $res = array('error' => '0', 'errortip' => "跟进成功", 'result' => array());
            ajax_return($res, $request['language_type']);
        } else {
            $res = array('error' => '1', 'errortip' => $ClientModel->errortip, 'result' => array());
            ajax_return($res, $request['language_type']);
        }
    }

    //招生柜询报表
    function inviteFormsView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        //字段JSON获取
        $fucMarkString = "/{$this->u}/{$this->t}";
        $FunctionModel = new \Model\Imc\FunctionModel($request);
        $field = $FunctionModel->getFunctionField($fucMarkString);

        //model
        $Model = new \Model\Crm\ClientinviteModel($request);
        $dataList = $Model->inviteForms($request);

        if ($dataList) {
            $result["fieldcustom"] = 1;
            $result["field"] = $field;
            $result["list"] = $dataList['datalist'];
            $res = array('error' => '0', 'errortip' => '获取招生目标信息成功', 'allnum' => $dataList['count'], 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => '1', 'errortip' => '获取招生目标信息失败', 'allnum' => array(), 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //柜询改约的信息
    function getInviteOneApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $result = $this->DataControl->selectOne("
            SELECT
                ( CASE i.invite_genre WHEN '0' THEN '普通柜询' WHEN '1' THEN '能力测试' WHEN '2' THEN '推带到访' WHEN '3' THEN '主动到访' END ) AS invite_genre,
                t.coursetype_cnname,
                c.coursecat_cnname,
                k.marketer_name,
                invite_visittime,
                k.track_id,
                i.invite_id
            FROM
                crm_client_invite AS i 
                left join smc_code_coursetype as t on t.coursetype_id = i.coursetype_id
                left join smc_code_coursecat as c on c.coursecat_id = i.coursecat_id
                left join crm_client_track as k on i.track_id = k.track_id
            WHERE
                i.invite_id = '{$request['invite_id']}'");

        $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        ajax_return($res, $request['language_type']);
    }

    //试听改约的信息
    function getAuditionOneApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $result = $this->DataControl->selectOne("
            SELECT
                ( CASE i.audition_genre WHEN '0' THEN '普通公开课试听' WHEN '1' THEN '插班试听' WHEN '2' THEN '试读公开课' END ) AS audition_genre,
                t.coursetype_cnname,
                t.coursetype_cnname,
                c.coursecat_cnname,
                k.marketer_name,
                i.audition_visittime,
                i.course_id,
                i.class_id,
                i.hour_id,
                i.class_cnname,
                i.audition_genre as audition_genre_num,
                k.track_id,
                i.audition_id,
                i.coursetype_id,
                i.coursecat_id
            FROM
                crm_client_audition AS i 
                left join smc_code_coursetype as t on t.coursetype_id = i.coursetype_id
                left join smc_code_coursecat as c on c.coursecat_id = i.coursecat_id
                left join crm_client_track as k on i.track_id = k.track_id
            WHERE
                i.audition_id = '{$request['audition_id']}'");

        $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        ajax_return($res, $request['language_type']);
    }


    // 改约柜询
    function changeInviteAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        if(!isset($request['changereason']) || $request['changereason'] == '' ){
            $res = array('error' => '1', 'errortip' => '请填写改约原因', 'result' => array());
            ajax_return($res, $request['language_type']);
        }

        $marketerOne = $this->DataControl->getFieldOne('crm_marketer', 'marketer_name', "marketer_id={$request['marketer_id']}");

        $oldt = $this->DataControl->getFieldOne("crm_client_track"," * ", "track_id = '{$request['track_id']}'");
        $data = array();
        $data['client_id'] = $oldt['client_id'];

        $data['school_id'] = $request['school_id'];
        $data['marketer_id'] = $request['marketer_id'];
        $data['marketer_name'] = $marketerOne['marketer_name'];

        $data['track_validinc'] = $oldt['track_validinc'];
        $data['object_code'] = $oldt['object_code'];
        $data['track_intention_level'] = $oldt['track_intention_level'];
        $data['coursetype_id'] = $oldt['coursetype_id'];
        $data['coursecat_id'] = $oldt['coursecat_id'];
//        $data['track_linktype'] = $oldt['track_linktype'];
        if($request['invite_id']){
            $data['track_linktype'] = ($request['language_type'] == 'tw')?'櫃詢改約':"柜询改约";
        }else{
            $data['track_linktype'] = ($request['language_type'] == 'tw')?'試聽改約':"试听改约";
        }
        $data['track_followuptype'] = $oldt['track_followuptype'];
        $data['track_followmode'] = $oldt['track_followmode'];
        $data['track_followuptime'] = $oldt['track_followuptime'];

        $data['track_visitingtime'] = $request['time'];

//        $data['track_note'] = $oldt['track_note']."-改约";
        $data['track_note'] = $request['changereason'].(($request['language_type'] == 'tw')?'【改約原因】':"【改约原因】");
        $data['track_state'] = $oldt['track_state'];
        $data['track_type'] = 0;
//        $data['track_isschoolread'] = $oldt['track_isschoolread'];
//        $data['track_initiative'] = $oldt['track_initiative'];
//        $data['track_isschooltmk'] = $oldt['track_isschooltmk'];
        $data['track_isactive'] = 1;
        $data['track_isgmcactive'] = 0;
        $data['update_note'] = $oldt['update_note'];
        $data['track_createtime'] = time();
        $tid = $this->DataControl->insertData("crm_client_track",$data);

        if($request['invite_id']){
            $old = $this->DataControl->getFieldOne("crm_client_invite"," * ","invite_id = '{$request['invite_id']}'");
            $data = array();
            $data['company_id'] = $request['company_id'];
            $data['school_id'] = $request['school_id'];
            $data['marketer_id'] = $request['marketer_id'];

            $data['receiver_name'] = $old['receiver_name'];
            $data['client_id'] = $old['client_id'];
            $data['track_id'] = $tid;
            $data['coursetype_id'] = $old['coursetype_id'];
            $data['coursecat_id'] = $old['coursecat_id'];
            $data['invite_level'] = $old['invite_level'];
            $data['invite_genre'] = $old['invite_genre'];
            $data['invite_isschooltmk'] = $old['invite_isschooltmk'];
            $data['invite_isvisit'] = '0';
            $data['invite_visittime'] = $request['time'];
            $data['invite_daytime'] = $old['invite_daytime'];
            $data['invite_createtime'] = time();
            $this->DataControl->insertData("crm_client_invite",$data);

            $oldInvite=array();
            $oldInvite['invite_isvisit']=2;
            $oldInvite['invite_novisitreason']=$request['changereason'];
            $oldInvite['invite_updatetime'] = time();
            $this->DataControl->updateData("crm_client_invite","invite_id = '{$request['invite_id']}'",$oldInvite);
        }else{
            $old = $this->DataControl->getFieldOne("crm_client_audition"," * ","audition_id = '{$request['audition_id']}'");

            if($this->DataControl->selectOne("select 1 from crm_client_audition where company_id = '{$request['company_id']}' and client_id = '{$old['client_id']}' and hour_id = '{$request['hour_id']}' and audition_isvisit = '0'  ")){
                $res = array('error' => '1', 'errortip' => '改约失败：（已存在本课时待确认的邀约）！', 'result' => array());
                ajax_return($res, $request['language_type']);
            }

            $data = array();
            $data['company_id'] = $request['company_id'];
            $data['school_id'] = $request['school_id'];
            $data['marketer_id'] = $request['marketer_id'];

            $data['receiver_name'] = $old['receiver_name'];
            $data['client_id'] = $old['client_id'];
            $data['track_id'] = $tid;
            $data['object_code'] = $old['object_code'];
            $data['coursetype_id'] = $old['coursetype_id'];
            $data['coursecat_id'] = $old['coursecat_id'];

            $data['class_id'] = $request['class_id'];
            $data['class_cnname'] = $request['class_cnname'];

            $data['course_id'] = $request['course_id'];
            $data['hour_id'] = $request['hour_id'];

            $data['audition_genre'] = $old['audition_genre'];
            $data['audition_isschooltmk'] = $old['audition_isschooltmk'];
            $data['audition_isvisit'] = 0;
            $data['audition_visittime'] = $request['time'];
            $data['outthree_bookid'] = $old['outthree_bookid'];
            $data['audition_createtime'] = time();
            $this->DataControl->insertData("crm_client_audition",$data);

            $oldAudition=array();
            $oldAudition['audition_isvisit']=2;
            $oldAudition['audition_novisitreason']=$request['changereason'];
            $oldAudition['audition_updatetime'] = time();
            $this->DataControl->updateData("crm_client_audition","audition_id = '{$request['audition_id']}'",$oldAudition);


            if( $request['hour_id']) {
                //240315 补充 校的数据
                $oldAudition=array();
                $oldAudition['audition_isvisit']=-1;
                $oldAudition['audition_novisitreason']=$request['changereason'].(($request['language_type'] == 'tw')?'【CRM改約操作】':"【CRM改约操作】");
                $oldAudition['audition_updatatime'] = time();
                $oldAudition['audition_updatetime'] = time();
                $this->DataControl->updateData("smc_class_hour_audition","client_id = '{$old['client_id']}' and hour_id = '{$old['hour_id']}'  and audition_isvisit = '0'",$oldAudition);

                $crmClientOne = $this->DataControl->selectOne("select client_cnname,client_enname from crm_client  where client_id='{$old['client_id']}'");

                $data = array();
                $data['company_id'] = $request['company_id'];
                $data['school_id'] = $request['school_id'];
                $data['hour_id'] = $request['hour_id'];
                $data['class_id'] = $request['class_id'];
                $data['client_id'] = $old['client_id'];
                $data['audition_cnname'] = $crmClientOne['client_cnname'];
                $data['audition_enname'] = $crmClientOne['client_enname'];
                $data['audition_isvisit'] = 0;
                $data['audition_createtime'] = time();
                $this->DataControl->insertData("smc_class_hour_audition", $data);
            }
        }

        $res = array('error' => '0', 'errortip' => '改约成功', 'result' => array());
        $this->addCrmWorkLog($request['company_id'], $request['school_id'], $request['marketer_id'], "柜询试听管理->柜询试听管理", '改约', dataEncode($request));
        ajax_return($res, $request['language_type']);
    }


    //结尾魔术函数
    function __destruct()
    {

    }
}
