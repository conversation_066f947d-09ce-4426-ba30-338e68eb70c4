<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/13
 * Time: 23:25
 */

namespace Work\Controller\Crmapi;


class DpingMtController extends viewTpl
{
    public $u;
    public $t;
    public $c;
    public $visitType = "api";

    //预加载处理类
    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
        $this->api = $this->router->getApi();
    }

    //美团技术服务合作中心 后台配置的地址 --- 业务授权码回调地址
    function getMtDpingAuthcodeView(){
//        $request = Input('get.', '', 'trim,addslashes');

        $request = file_get_contents("php://input");
        if(!$request){
            $request = Input('request.', '', 'trim,addslashes');
        }
        $Model = new \Model\Api\DpingModel($request);
        $Model->getMtDpingAuthcode($request);
        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $Model->result));
    }

    //美团技术服务合作中心 后台配置的地址 --- 客资中心-推送顾客线索消息
    function getMtDpingClientView(){
//        $request = Input('get.', '', 'trim,addslashes');

        $request = file_get_contents("php://input");
        if(!$request){
            $request = Input('request.', '', 'trim,addslashes');
        }
        $Model = new \Model\Api\DpingModel($request);
        $Model->getMtDpingClient($request);

        exit(json_encode(array('code' => (int)$Model->error, 'message' => $Model->errortip)));
    }

    //美团技术服务合作中心 --- 查询订单数据
    function getMtDpingOrderApi(){
        $request = Input('get.', '', 'trim,addslashes');
        $request['company_id'] = '8888';
        $Model = new \Model\Api\DpingModel($request);

//        $Model->getMtDpingOrderApi($request);
//        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $Model->result));


        if($request['type'] == '1'){
            //差找12小时前的  12 小时的数据
            $start_time =time()-86400; //
            $end_time = time()-43200; //
            $Model->getMtDpingOrderApi($request,1,100,$start_time,$end_time,'0');
        }elseif($request['type'] == '2'){
            //差找前一天的 一天 的数据
            $start_time =time()-86400-86400; //
            $end_time = time()-86400; //
            $Model->getMtDpingOrderApi($request,1,100,$start_time,$end_time,'0');
        }else{
            $Model->getMtDpingOrderApi($request);
        }
        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $Model->result));
    }

    //美团技术服务合作中心 --- 店铺数据
    function getMtDpingStoreApi(){
        $request = Input('get.', '', 'trim,addslashes');

        $Model = new \Model\Api\DpingModel($request);
        $Model->getMtDpingStoreApi($request);
        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $Model->result));
    }

    //美团技术服务合作中心 --- 店铺数据 --- 对应客户门店ID映射关系
    function getBjxToMtDpingStoreApi(){
        $request = Input('get.', '', 'trim,addslashes');

        $Model = new \Model\Api\DpingModel($request);
        $Model->getBjxToMtDpingStoreApi($request);
        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $Model->result));
    }

    //美团技术服务合作中心 --- 测试授权 code 获取 token 的操作  ----- 测试
    function getMtDpingCodeToTokenView(){
        $request = Input('get.', '', 'trim,addslashes');

        $Model = new \Model\Api\DpingModel($request);
        $Model->getMtDpingCodeToToken($request);
        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $Model->result));
    }

    //美团技术服务合作中心 --- 定时更新已经生成的 token
    function getMtDpingTokenToRefreshView(){
        $request = Input('get.', '', 'trim,addslashes');

        $Model = new \Model\Api\DpingModel($request);
        $Model->getMtDpingTokenToRefresh($request);
        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $Model->result));
    }



    //结尾魔术函数
    function __destruct()
    {

    }

}
