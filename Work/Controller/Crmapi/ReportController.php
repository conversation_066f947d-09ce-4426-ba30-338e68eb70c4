<?php

namespace Work\Controller\Crmapi;


class ReportController extends viewTpl
{
    public $u;
    public $t;
    public $c;
    public $visitType = "api";
    public $DataControl;
    public $company_isassist = 0;

    //预加载处理类
    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();

        //数据库操作
        $this->DataControl = new \Dbmysql();

    }


    //本地权限校验入口
    function ThisVerify($request)
    {

        if (!intval($request['marketer_id'])) {
            $res = array('error' => '1', 'errortip' => "未检测到招生人员信息，请重新登录操作", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        if (empty($request['token'])) {
            $res = array('error' => '1', 'errortip' => "未传入用户Token信息", 'result' => array());
            ajax_return($res, $request['language_type']);
        }
        if (isset($request['company_id']) && $request['company_id'] !== '') {
            $companyOne = $this->DataControl->getFieldOne("gmc_company", "company_isassist", "company_id='{$request['company_id']}'");
            $this->company_isassist = $companyOne['company_isassist'];
        }
        $paramArray = array();
        $paramArray['marketer_id'] = $request['marketer_id'];
        $paramArray['token'] = $request['token'];
        if (!$this->UserLimit($paramArray)) {
            $result = array();
            $result["list"] = array();
            $result["tokeninc"] = "0";
            $res = array('error' => '1', 'errortip' => "用户token失效", 'result' => $result);
            ajax_return($res, $request['language_type']);
        }
    }

    // 12/23号 修订
    //校招生咨询报表
    function schoolClientRerportView()
    {
        $request = Input('get.', '', 'trim,addslashes');

        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Crm\ReportModel($request);
        $datalist = $Model->schClientTrack($request);

        $k = 0;
        $field[$k]["fieldstring"] = "client_cnname";
        $field[$k]["fieldname"] = "中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "client_enname";
        $field[$k]["fieldname"] = "英文名";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "client_createtime";
        $field[$k]["fieldname"] = "创建日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "client_tracestatus_name";
        $field[$k]["fieldname"] = "客户状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "track_num";
        $field[$k]["fieldname"] = "跟进次数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "client_age";
        $field[$k]["fieldname"] = "年龄";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "client_sex";
        $field[$k]["fieldname"] = "性别";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "client_address";
        $field[$k]["fieldname"] = "联系地址";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "client_source";
        $field[$k]["fieldname"] = "渠道类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "channel_name";
        $field[$k]["fieldname"] = "渠道明细";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "client_intention_level";
        $field[$k]["fieldname"] = '意向星级';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "client_intention_maxlevel";
        $field[$k]["fieldname"] = '最高意向星级';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "track_createtime";
        $field[$k]["fieldname"] = '最后跟踪时间';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "track_detail";
        $field[$k]["fieldname"] = '跟踪明细';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "is_invite_name";
        $field[$k]["fieldname"] = '是否邀约柜询';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "invite_visittime";
        $field[$k]["fieldname"] = '柜询时间';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "receiver_name";
        $field[$k]["fieldname"] = '柜询接待人';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "invite_isvisit";
        $field[$k]["fieldname"] = '柜询是否到访';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "is_audition_name";
        $field[$k]["fieldname"] = '是否邀约试听';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "audition_visittime";
        $field[$k]["fieldname"] = '试听时间';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "au_receiver_name";
        $field[$k]["fieldname"] = '试听接待人';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "audition_isvisit";
        $field[$k]["fieldname"] = '试听是否到访';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "mian_marketer_name";
        $field[$k]["fieldname"] = '主招老师';
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        if ($this->company_isassist == 1) {
            $k++;
            $field[$k]["fieldstring"] = "fu_marketer_name";
            $field[$k]["fieldname"] = '辅招老师';
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
        }

        $k++;
        $field[$k]["fieldstring"] = "client_sponsor";
        $field[$k]["fieldname"] = '介绍人';
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;

        $result = array();
        $result['list'] = $datalist['list'];
        $result['field'] = $field;
        $result['allnum'] = $datalist['all_num'];
        $allnum = $datalist['all_num'];
        if ($result['list']) {
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result, "allnum" => $allnum);
        } else {
            $res = array('error' => '1', 'errortip' => "暂无校招生咨询报表信息", 'result' => $result, "allnum" => $allnum);
        }
        ajax_return($res, $request['language_type']);
    }

    //个人业绩报表
    function marketerClientReportView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $schoolOne = $this->DataControl->getFieldOne("smc_school", "school_openclass", "school_id='{$request['school_id']}'");

        $Model = new \Model\Crm\ReportModel($request);
        $datalist = $Model->marketerOneClientReport($request);
        $k = 0;
        $field[$k]["fieldstring"] = "marketer_name";
        $field[$k]["fieldname"] = "教师中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "staffer_enname";
        $field[$k]["fieldname"] = "教师英文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "staffer_branch";
        $field[$k]["fieldname"] = "教师编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "track_client_num";
        $field[$k]["fieldname"] = "沟通人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "track_num";
        $field[$k]["fieldname"] = "沟通人次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        if ($schoolOne['school_openclass'] == 1) {
            $k++;
            $field[$k]["fieldstring"] = "OH_audition_num";
            $field[$k]["fieldname"] = "OH邀约名单数";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;
            $field[$k]["fieldstring"] = "OH_audition_arrivenum";
            $field[$k]["fieldname"] = "OH到访名单数";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
        }

        $k++;
        $field[$k]["fieldstring"] = "Class_audition_num";
        $field[$k]["fieldname"] = "插班试听邀约数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "Class_audition_arrivenum";
        $field[$k]["fieldname"] = "插班试听到访数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "invire_num";
        $field[$k]["fieldname"] = "柜询邀约名单数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "invire_arrivenum";
        $field[$k]["fieldname"] = "柜询到访名单数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
//        if ($schoolOne['school_openclass'] == 1) {
//            $k++;
//            $field[$k]["fieldstring"] = "OH_audition_positivenum";
//            $field[$k]["fieldname"] = "新增OH邀约报名数";
//            $field[$k]["show"] = 1;
//            $field[$k]["custom"] = 1;
//            $k++;
//            $field[$k]["fieldstring"] = "OH_audition_allpositivenum";
//            $field[$k]["fieldname"] = "累计新增OH邀约报名数";
//            $field[$k]["show"] = 1;
//            $field[$k]["custom"] = 1;
//        }
//        $k++;
//        $field[$k]["fieldstring"] = "Class_audition_positivenum";
//        $field[$k]["fieldname"] = "新增插班试听邀约转正数";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//        $k++;
//        $field[$k]["fieldstring"] = "Class_audition_allpositivenum";
//        $field[$k]["fieldname"] = "累计新增插班试听邀约报名数";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//        $k++;
//        $field[$k]["fieldstring"] = "invire_positivenum";
//        $field[$k]["fieldname"] = "新增柜询邀约报名数";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//        $k++;
//        $field[$k]["fieldstring"] = "invire_all_positivenum";
//        $field[$k]["fieldname"] = "累计新增柜询邀约报名数";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;


        $k++;
        $field[$k]["fieldstring"] = "main_conversionlog_num";
//        $field[$k]["fieldname"] = "主招转正数";
        $field[$k]["fieldname"] = "新招报名数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        if ($this->company_isassist == 1) {
            $k++;
            $field[$k]["fieldstring"] = "fu_conversionlog_num";
//            $field[$k]["fieldname"] = "辅招转正数";
            $field[$k]["fieldname"] = "扩科报名数";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
        }
        $k++;
        /*$field[$k]["fieldstring"] = "unefftive_num";
        $field[$k]["fieldname"] = "新增无效名单数";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;*/

        $result = array();
        $result['field'] = $field;
        $result['list'] = $datalist['list'] == false ? array() : $datalist['list'];
        $allnum = $datalist['allnums'] == false ? 0 : $datalist['allnums'];
        if ($result['list']) {
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result, "allnum" => $allnum);
        } else {
            $res = array('error' => '1', 'errortip' => "暂无校招生咨询报表信息", 'result' => $result, "allnum" => $allnum);
        }
        ajax_return($res, $request['language_type']);

    }

    //渠道业绩统计报表
    function channelClientReportView()
    {
        $request = Input('get.', '', 'trim,addslashes');

        $this->ThisVerify($request);//验证账户
        $schoolOne = $this->DataControl->getFieldOne("smc_school", "school_openclass", "school_id='{$request['school_id']}'");
        $Model = new \Model\Crm\ReportModel($request);
        $datalist = $Model->schChannelClientReport($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "frommedia_name";
        $field[$k]["fieldname"] = "招生渠道类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "channel_num";
        $field[$k]["fieldname"] = "招生渠道明细数量";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "client_allnum";
        $field[$k]["fieldname"] = "新增毛名单数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "track_num";
        $field[$k]["fieldname"] = "跟踪人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        if ($schoolOne['school_openclass'] == 1) {
            $k++;
            $field[$k]["fieldstring"] = "HO_aud_num";
            $field[$k]["fieldname"] = "新增OH邀约名单数";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;
            $field[$k]["fieldstring"] = "HO_aud_arrivenum";
            $field[$k]["fieldname"] = "新增OH邀约到访名单数";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
        }
        $k++;
        $field[$k]["fieldstring"] = "Class_aud_num";
        $field[$k]["fieldname"] = "新增插班邀约名单数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "Class_aud_arrivenum";
        $field[$k]["fieldname"] = "新增插班邀约到访名单数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "invite_num";
        $field[$k]["fieldname"] = "新增柜询邀约名单数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "invite_arrivenum";
        $field[$k]["fieldname"] = "新增柜询邀约到访名单数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "inv_positive_num";
        $field[$k]["fieldname"] = "新增邀约转正人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "positive_num";
        $field[$k]["fieldname"] = "新增报名人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "client_uneffive_num";
        $field[$k]["fieldname"] = "新增无效名单数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "client_nointention_num";
        $field[$k]["fieldname"] = "新增无意向名单";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "school_positive_rate";
        $field[$k]["fieldname"] = "转正率";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result['field'] = $field;
        $result['list'] = $datalist['list'] == false ? array() : $datalist['list'];
        $allnum = $datalist['all_num'] == false ? 0 : $datalist['all_num'];

        if ($result['list']) {
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result, "allnum" => $allnum);
        } else {
            $res = array('error' => '1', 'errortip' => "暂无渠道业绩统计报表记录", 'result' => $result, "allnum" => $allnum);
        }
        ajax_return($res, $request['language_type']);

    }

    //招生咨询明细表
    function schoolMothReportView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $request['from'] = 1;

        $Model = new \Model\Crm\ReportModel($request);
        $datalist = $Model->schoolMothReport($request);


        if ($this->company_isassist == 0) {
            $fieldstring = array('client_cnname', 'client_enname', 'client_mobile', 'client_tracestatus', 'client_source', 'channel_name', 'client_intention_level', 'client_intention_maxlevel', 'client_createtime', 'is_invite', 'is_invite_arrive', 'is_audition', 'is_audition_arrive', 'main_marketer_name', 'track_createtime', 'marketer_name', 'track_note');
            $fieldname = array('中文名', '英文名', '手机号', '客户状态', '渠道类型', '渠道明细', '意向星级', '最高意向星级', '名单创建时间', '是否邀约柜询', '是否柜询到访', '是否邀约试听', '是否试听到访', '主招教师', '最后跟踪时间', '最后跟踪教师', '最后跟踪内容');
        } else {
            $fieldstring = array('client_cnname', 'client_enname', 'client_mobile', 'client_tracestatus', 'client_source', 'channel_name', 'client_intention_level', 'client_intention_maxlevel', 'client_createtime', 'is_invite', 'is_invite_arrive', 'is_audition', 'is_audition_arrive', 'main_marketer_name', 'fu_marketer_name', 'track_createtime', 'marketer_name', 'track_note');
            $fieldname = array('中文名', '英文名', '手机号', '客户状态', '渠道类型', '渠道明细', '意向星级', '最高意向星级', '名单创建时间', '是否邀约柜询', '是否柜询到访', '是否邀约试听', '是否试听到访', '主招教师', '辅招教师', '最后跟踪时间', '最后跟踪教师', '最后跟踪内容');
        }
        $fieldcustom = array('1', "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", '1', '1', '1', '1', '1', '1', '1', '1');
        $fieldshow = array('1', "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", '1', '1', '1', '1', '1', '1', '1', '1');

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["fieldname"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['field'] = $field;
        $result['allnum'] = $datalist['allnums'] + 0;
        $result['list'] = $datalist['list'] == false ? array() : $datalist['list'];
        $allnum = $datalist['allnums'] == false ? 0 : $datalist['allnums'];

        if ($result['list']) {
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result, "allnum" => $allnum);
        } else {
            $res = array('error' => '1', 'errortip' => "暂无校招生咨询报表信息", 'result' => $result, "allnum" => $allnum);
        }
        ajax_return($res, $request['language_type']);
    }

    //渠道名单跟踪状况表
    function channelConsultView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Crm\ReportModel($request);
        $datalist = $Model->channelConsult($request);
        $fieldname = array('渠道类型', '明细名称', '渠道方式', '中文名', '性别', ' 年龄', '电话', "备注", '名单创建时间', '主负责人', '是否邀约柜询', '柜询时间', '柜询是否到访', '是否邀约试听', '试听时间', '试听接待人', '试听是否到访 ', '客户状态');
        $fieldstring = array('client_source', 'channel_name', 'channel_way', 'client_cnname', 'client_sex', 'client_age', 'client_mobile', 'client_remark', 'client_createtime', 'marketer_name', 'is_invite', 'invite_visittime', 'invite_isvisit', 'is_audition', 'audition_visittime', 'au_receiver_name', 'audition_isvisit', 'tracestatus_name');
        $fieldcustom = array('1', "1", "1", "1", "1", "1", "1", "1", "1", '1', '1', '1', '1', '1', '1', '1', '1', '1');
        $fieldshow = array('1', "1", "1", "1", "1", "1", "1", "1", "1", '1', '1', '1', '1', '1', '1', '1', '1', '1');

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldname[$i]);
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['field'] = $field;
        $result['list'] = $datalist['list'] == false ? array() : $datalist['list'];
        $allnum = $datalist['allnums'] == false ? 0 : $datalist['allnums'];
        if ($result['list']) {
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result, "allnum" => $allnum);
        } else {
            $res = array('error' => '1', 'errortip' => "暂无渠道咨询报表信息", 'result' => $result, "allnum" => $allnum);
        }
        ajax_return($res, $request['language_type']);


    }

    //渠道招生明细
    function detailsByChannelView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Report\Gmc\GmcCrmReportModel($request);
        $res = $ReportModel->detailsByChannel($request);

        $k = 0;
        $field[$k]["fieldstring"] = "client_cnname";
        $field[$k]["fieldname"] = "中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "client_enname";
        $field[$k]["fieldname"] = "英文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "client_sex";
        $field[$k]["fieldname"] = "性别";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "client_age";
        $field[$k]["fieldname"] = "年龄";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "parenter_cnname";
        $field[$k]["fieldname"] = "主要联系人";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "client_mobile";
        $field[$k]["fieldname"] = "联系手机";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "client_tracestatus";
        $field[$k]["fieldname"] = "客户状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "client_intention_level";
        $field[$k]["fieldname"] = "意向星级";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "client_source";
        $field[$k]["fieldname"] = "招生渠道类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "channel_name";
        $field[$k]["fieldname"] = "招生渠道明细";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "promotion_jobnumber";
        $field[$k]["fieldname"] = "地推工号";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "post_name";
        $field[$k]["fieldname"] = "职务";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldstring"] = "client_frompage";
        $field[$k]["fieldname"] = "接触点";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "client_createtime";
        $field[$k]["fieldname"] = "创建时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "client_updatetime";
        $field[$k]["fieldname"] = "更新时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "zhu_marketer_name";
        $field[$k]["fieldname"] = "主要负责人";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        if ($this->company_isassist == 1) {
            $k++;
            $field[$k]["fieldstring"] = "fu_marketer_name";
            $field[$k]["fieldname"] = "协助负责人";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $field[$k]["custom"] = 1;
        }
        $k++;
        $field[$k]["fieldstring"] = "track_createtime";
        $field[$k]["fieldname"] = "最后跟踪时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "track_note";
        $field[$k]["fieldname"] = "跟踪明细";
        $field[$k]["show"] = 1;

        $result = array();
        $result["field"] = $field;
//        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'allnum' => $res['allnum'], 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => "暂无渠道招生明细", 'allnum' => 0, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //单校班组招生统计报表
    function courseRecruitCountView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Crm\ReportModel($request);
        $datalist = $Model->courseRecruitCount($request);

        $k = 0;
        $field = array();
        $field[$k]["fieldname"] = "班组名称";
        $field[$k]["fieldstring"] = "coursetype_cnname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "新增毛名单数";
        $field[$k]["fieldstring"] = "client_maonum";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "新增有效名单数";
        $field[$k]["fieldstring"] = "client_validnum";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "新增三星及以上有效名单数";
        $field[$k]["fieldstring"] = "client_level_validnum";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "新增跟踪人数";
        $field[$k]["fieldstring"] = "client_tracknum";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "新增跟踪人次";
        $field[$k]["fieldstring"] = "school_track_num";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "新增电话沟通人次";
        $field[$k]["fieldstring"] = "track_moblie_num";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "新增累计报名人数";
        $field[$k]["fieldstring"] = "positive_num";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result['field'] = $field;
        $result['list'] = $datalist['list'] == false ? array() : $datalist['list'];
        $result['list'] = $datalist['list'] == false ? array() : $datalist['list'];
        $allnum = $datalist['allnums'] == false ? 0 : $datalist['allnums'];
        $result['allnum'] = $allnum;

        if ($result['list']) {
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result, "allnum" => $allnum);
        } else {
            $res = array('error' => '1', 'errortip' => "单校班组招生统计报表", 'result' => $result, "allnum" => $allnum);
        }
        ajax_return($res, $request['language_type']);
    }


    function gainsByChannelView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Crm\ReportModel($request);
        $res = $ReportModel->marketerGains($request);

        $k = 0;
        $field = array();
        $field[$k]["fieldstring"] = "marketer_name";
        $field[$k]["fieldname"] = "教师中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "staffer_enname";
        $field[$k]["fieldname"] = "教师英文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "staffer_branch";
        $field[$k]["fieldname"] = "教师编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "post_name";
        $field[$k]["fieldname"] = "在校职务";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "principal_num";
        $field[$k]["fieldname"] = "意向名单数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "goal_name";
        $field[$k]["fieldname"] = "招生目标名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "goal_time";
        $field[$k]["fieldname"] = "目标执行时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "track_goal_num";
        $field[$k]["fieldname"] = "跟踪人次/目标跟踪人次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "invite_aud_goal_num";
        $field[$k]["fieldname"] = "柜询试听人次/目标柜询试听人次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "positive_goal_num";
        $field[$k]["fieldname"] = "转化人次/目标转化人次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "conversion_rate";
        $field[$k]["fieldname"] = "转化率";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => "暂无渠道统计分析", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //校务追踪大表
    function getClientHighestReportView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Report\Gmc\GmcCrmReportModel($request);
        $temp_school_id = $request['school_id'];
        if (isset($request['type']) && $request['type'] == 'admTrackReport') {
            $request['school_id'] = '';
        }
        $datalist = $Model->getClientHighestReport($request);

        $fieldname = array('校区名称', '校区编号', '报名/周', '报名/月', '新增毛名单/月', '新增线下名单/周', '新增线下名单/月', '新增线上名单/周', '新增线上名单/月', '新增名单/季', '推荐名单/月', '3个月内有效名单', '6个月内有效名单', '总有效名单', '待分配名单数', '追踪名单数/周', '追踪人次/周', "电询/周", "电询/月", '未确认邀约数/月', '柜询/周', '柜询/月', 'OH邀约/当周', 'OH邀约/上周', 'OH邀约到访/上周', 'OH邀约/自然周', 'OH邀约/自然月', "OH到访/自然周", "OH到访/自然月", "试听/周", "试听/月", "OH转正数/月", "OH转正率/月", '内部招生数', '外部招生数', "专案招生数");
        $fieldstring = array('school_shortname', 'school_branch', 'positivelog_week_num', 'positivelog_month_num', 'mao_month_client_num', 'client_under_weeknum', 'client_under_monthnum', 'client_up_weeknum', 'client_up_monthnum', 'client_up_quarternum', 'recomend_clientnum', 'threemoth_clientnum', 'sixmoth_clientnum', 'client_all_num', 'client_noallot_num', 'track_client_num', 'track_tracknum', 'track_week_tephonenum', 'track_month_tephonenum', 'no_confirm', 'invite_week_num', 'invite_month_num', 'ohaudition_curweek_num', 'ohaudition_lastweek_num', 'ohaudition_lastweek_postnum', 'ohaudition_week_num', 'ohaudition_month_num', 'ohaudition_week_arrnum', 'ohaudition_month_arrnum', 'audition_week_num', 'audition_month_num', 'ohpostive_month_arrnum', 'ohpostive_month_rate', 'client_innernum', 'client_outnum', 'client_casenum');

        $fieldcustom = array('1', '1', '1', '1', "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1');
        $fieldshow = array('1', '1', '1', '1', "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1');

        $field = array();
        $field_array = array('positivelog_week_num', 'positivelog_month_num', 'client_under_weeknum', 'client_under_monthnum', 'client_up_weeknum', 'client_up_monthnum', 'client_up_quarternum', 'ohaudition_week_num', 'ohaudition_month_num', 'ohaudition_week_arrnum', 'ohaudition_month_arrnum', 'audition_week_num', 'audition_month_num', 'ohpostive_month_arrnum');
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["fieldname"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
            if (in_array($field[$i]["fieldstring"], $field_array)) {
                $field[$i]["ismethod"] = 1;
                $field[$i]["table_name"] = $field[$i]['fieldname'] . '统计表';
            }
        }

        $result = array();
        $result['field'] = $field;
        $result['list'] = $datalist['list'] == false ? array() : $datalist['list'];
        $allnum = $datalist['allnum'] == false ? 0 : $datalist['allnum'];
        $result['allnum'] = $allnum;

        if ($result['list']) {
            foreach ($result['list'] as &$dataOne) {
                if ($dataOne['school_id'] <> $temp_school_id) {
                    $dataOne['not_click'] = true;
                }
            }
            $res = array('error' => '0', 'errortip' => "获取招生追踪大表成功", 'result' => $result, "allnum" => $allnum);
        } else {
            $res = array('error' => '1', 'errortip' => "暂无记录", 'result' => $result, "allnum" => $allnum);
        }
        ajax_return($res, $request['language_type']);

    }


    //校区追踪大表  --- 集团新的表
    function getClientRecruitReportView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Report\Gmc\GmcCrmReportModel($request);
        $datalist = $Model->getClientRecruitReport($request, 1);
        //20250723 之前注释的
//        $fieldname = array("地区","校区名称","督导区","在籍","在读",
//            "专案免费在读人数","报名/去年","专案报名/去年",
//            "报名/当年","报名/周","报名/月","上月招生数","上上月招生数",
//            "新增总毛名单/周","新增总毛名单/月",
//            "新增陆军有效名单/周","新增陆军有效名单/月","新增空军有效名单/周","新增空军有效名单/月","新增有效名单/月（空+陆）","新增有效名单/季",
//            "新增转介绍名单/周","转介绍名单报名/周","新增转介绍名单/月","转介绍名单报名/月","新增转介绍名单/季",
//
//            "园内招（名单/月）","园内招（名单/季）","园内招（名单/当年）","园内招（报名/月）","园内招（报名/季）","园内招（报名/当年）",
//            "可追踪有效名单","待分配名单数","追踪人数/周","追踪人次/周",
//            "追踪人数/月","追踪人次/月","邀约诺访/上周","邀约到访/上周",
//            "邀约诺访/当周","邀约到访/当周","邀约诺访/月","邀约到访/月","主动到访/周",
//            "主动到访/月","试读/周","试读/月");
//        $fieldstring = array("school_districtname","school_shortname","school_tagbak","absenteenums","readingnums",
//            "feereadingnums",'prelog_year_num','prelog_caseyear_num',
//            "nowlog_year_num","positivelog_week_num","positivelog_month_num","positivelog_one_month_num","positivelog_two_month_num",
//            "mao_week_client_num","mao_month_client_num",
//            "client_under_weeknum","client_under_monthnum","client_up_weeknum","client_up_monthnum","client_upunder_monthnum","client_up_quarternum",
//            "client_referral_weeknum","client_referral_regweeknum","client_referral_monthnum","client_referral_regmonthnum","client_referral_quarternum",
//
//            "register_client_monthnum","register_client_seasonnum","register_client_yearnum","info_month_num","info_season_num","info_year_num",
//            "client_all_num","client_noallot_num","track_week_client_num","track_week_tracknum",
//            "track_client_num","track_tracknum","invitelog_lastweek_num","invitelog_lastweek_isvisitnum",
//            "invitelog_thisweek_num","invitelog_thisweek_isvisitnum","invitelog_month_num","invitelog_month_isvisitnum","invite_week_num",
//            "invite_month_num","audition_week_num","audition_month_num");
//
//        $fieldcustom = array('1','1', '1', '1', '1',
//            "1", '1', "1",
//            '1', '1', '1', '1', "1", '1', "1",
//            '1', '1', '1', '1', "1", '1',
//            "1","1","1","1","1",
//            '1', '1', '1', '1', "1", '1',
//            '1', '1', '1', '1',
//            "1", '1', "1", '1',
//            '1', '1', '1', '1', "1",
//            '1', '1', '1');
//        $fieldshow = array('1', '1', '1', '1', "1",
//            "1", '1', "1",
//            '1', '1', '1', '1', "1", '1', "1",
//            '1', '1', '1', '1', "1", '1',
//            "1","1","1","1","1",
//            '1', '1', '1', '1', "1", '1',
//            '1', '1', '1', '1',
//            "1", '1', "1", '1',
//            '1', '1', '1', '1', "1",
//            '1', '0', '0');
        $fieldname = array("地区", "校区名称", "督导区",
            "报名/去年",
            "报名/当年", "报名/周", "报名/月",
            "新增总毛名单/月",
            "新增陆军有效名单/月", "新增空军有效名单/月",
            "新增转介绍名单/月", "转介绍名单报名/月",
            "待分配名单数" );
        $fieldstring = array("school_districtname", "school_shortname", "school_tagbak",
            'prelog_year_num',
            "nowlog_year_num", "positivelog_week_num", "positivelog_month_num",
            "mao_month_client_num",
            "client_under_monthnum","client_up_monthnum",
            "client_referral_monthnum", "client_referral_regmonthnum"
            , "client_noallot_num" );

        $fieldcustom = array('1', '1', '1',
            "1",
            '1', '1', '1',
            '1',
            "1", "1",
            '1', '1',
            '1' );
        $fieldshow = array('1', '1', '1',
            "1",
            '1', '1', '1',
            '1',
            "1", "1",
            '1', '1',
            '1'  );
        //20250724 备份的注释
//        $fieldname = array("地区", "校区名称", "督导区", "在籍", "在读",
//            "报名/去年",
//            "报名/当年", "报名/周", "报名/月",
//            "新增总毛名单/月",
//            "新增陆军有效名单/月", "新增空军有效名单/月", "新增有效名单/月（空+陆）", "新增有效名单/季",
//            "新增转介绍名单/月", "转介绍名单报名/月",
//
//            "园内招（名单/月）", "园内招（报名/月）",
//            "可追踪有效名单", "待分配名单数",
//            "追踪人数/月",
//            "邀约诺访/月", "邀约到访/月");
//        $fieldstring = array("school_districtname", "school_shortname", "school_tagbak", "absenteenums", "readingnums",
//            'prelog_year_num',
//            "nowlog_year_num", "positivelog_week_num", "positivelog_month_num",
//            "mao_month_client_num",
//            "client_under_monthnum","client_up_monthnum", "client_upunder_monthnum", "client_up_quarternum",
//            "client_referral_monthnum", "client_referral_regmonthnum",
//
//            "register_client_monthnum", "info_month_num",
//            "client_all_num", "client_noallot_num",
//            "track_client_num",
//            "invitelog_month_num", "invitelog_month_isvisitnum");
//
//        $fieldcustom = array('1', '1', '1', '1', '1',
//            "1",
//            '1', '1', '1',
//            '1',
//            "1", "1", "1", "1",
//            '1', '1',
//            '1', '1',
//            "1", '1',
//            '1',
//            '1', '1');
//        $fieldshow = array('1', '1', '1', '1', '1',
//            "1",
//            '1', '1', '1',
//            '1',
//            "1", "1", "1", "1",
//            '1', '1',
//            '1', '1',
//            "1", '1',
//            '1',
//            '1', '1');

        $field = array();
//        $field_array = array('positivelog_week_num', 'positivelog_month_num', 'client_under_weeknum', 'client_under_monthnum', 'client_up_weeknum', 'client_up_monthnum', 'client_up_quarternum', 'ohaudition_week_num', 'ohaudition_month_num', 'ohaudition_week_arrnum', 'ohaudition_month_arrnum', 'audition_week_num', 'audition_month_num', 'ohpostive_month_arrnum');
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["fieldname"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
//            if (in_array($field[$i]["fieldstring"], $field_array)) {
//                $field[$i]["ismethod"] = 1;
//                $field[$i]["table_name"] = $field[$i]['fieldname'] . '统计表';
//            }
        }

        $result = array();
        $result['field'] = $field;
        $result['list'] = $datalist['list'] == false ? array() : $datalist['list'];
        $allnum = $datalist['allnum'] == false ? 0 : $datalist['allnum'];
        $result['allnum'] = $allnum;

        if ($result['list']) {
            $res = array('error' => '0', 'errortip' => "获取校区招生追踪大表成功", 'result' => $result, "allnum" => $allnum);
        } else {
            $res = array('error' => '1', 'errortip' => "暂无记录", 'result' => $result, "allnum" => $allnum);
        }
        ajax_return($res, $request['language_type']);

    }

    /**
     * 需要传入对应的字段,根据字段进行对应的查询数据详情
     * author: ling
     * 对应接口文档 0001
     */
    function getReportClientListApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Report\Gmc\GmcCrmReportModel($request);
        $result = $Model->getReportClientList($request);
        $allnum = 0;
        $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result, "allnum" => $allnum);
        ajax_return($res, $request['language_type']);
    }

    //沟通类型明细表
    function trackDetailByLinkTypeView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Report\Gmc\GmcCrmReportModel($request);
        $res = $ReportModel->trackDetailByLinkType($request);

        $k = 0;
        $field[$k]["fieldstring"] = "client_cnname";
        $field[$k]["fieldname"] = "中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "client_enname";
        $field[$k]["fieldname"] = "英文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "client_mobile";
        $field[$k]["fieldname"] = "联系手机";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "client_source";
        $field[$k]["fieldname"] = "招生渠道类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "channel_name";
        $field[$k]["fieldname"] = "招生渠道明细";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "main_marketer_name";
        $field[$k]["fieldname"] = "主负责人";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        if ($this->company_isassist == 1) {
            $k++;
            $field[$k]["fieldstring"] = "sub_marketer_name";
            $field[$k]["fieldname"] = "协助负责人";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
        }

        $k++;
        $field[$k]["fieldstring"] = "client_tracestatus";
        $field[$k]["fieldname"] = "客户状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "track_linktype";
        $field[$k]["fieldname"] = "沟通类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "track_createtime";
        $field[$k]["fieldname"] = "跟踪时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "client_createtime";
        $field[$k]["fieldname"] = "创建时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "track_detail";
        $field[$k]["fieldname"] = "有效跟踪明细";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "track_note";
        $field[$k]["fieldname"] = "跟踪备注";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "client_remark";
        $field[$k]["fieldname"] = "备注";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
//        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'allnum' => $res['allnum'], 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => "暂无沟通类型明细", 'allnum' => 0, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //活动业绩统计表
    function activityPositiveReportView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $schoolOne = $this->DataControl->getFieldOne("smc_school", "school_openclass", "school_id='{$request['school_id']}'");
        $ReportModel = new \Model\Crm\ReportModel($request);
        $res = $ReportModel->activityPositiveReport($request);
        $k = 0;
        $field[$k]["fieldstring"] = "activity_name";
        $field[$k]["fieldname"] = "活动名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "activity_type_name";
        $field[$k]["fieldname"] = "活动类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "frommedia_name";
        $field[$k]["fieldname"] = "招生渠道类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "channel_name";
        $field[$k]["fieldname"] = "招生渠道明细";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "client_allnum";
        $field[$k]["fieldname"] = "新增毛名单";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "新增有效名单数";
        $field[$k]["fieldstring"] = "client_validnum";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "新增三星及以上有效名单数";
        $field[$k]["fieldstring"] = "client_level_validnum";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "client_invite_num";
        $field[$k]["fieldname"] = "新增邀约名单数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "inv_aud_arrivenum";
        $field[$k]["fieldname"] = "新增邀约到访数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "inv_aud_positivenum";
        $field[$k]["fieldname"] = "新增报名数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "positive_validnum";
        $field[$k]["fieldname"] = "累计报名数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result, 'allnum' => $result["allnum"]);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => "暂无沟通类型明细", 'result' => $result, 'allnum' => $result["allnum"]);
        }
        ajax_return($res, $request['language_type']);

    }

    //活动名单明细
    function activityClientListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Crm\ReportModel($request);
        $res = $ReportModel->activityClientList($request);
        $k = 0;
        $field[$k]["fieldname"] = "招生活动名称";
        $field[$k]["fieldstring"] = "activity_name";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "招生活动模式";
        $field[$k]["fieldstring"] = "activity_pattern_name";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldname"] = "招生活动时间";
        $field[$k]["fieldstring"] = "activity_time";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldname"] = "招生活动类型";
        $field[$k]["fieldstring"] = "activity_type_name";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldname"] = "中文名";
        $field[$k]["fieldstring"] = "client_cnname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldname"] = "英文名";
        $field[$k]["fieldstring"] = "client_enname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldname"] = "性别";
        $field[$k]["fieldstring"] = "client_sex";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldname"] = "年龄";
        $field[$k]["fieldstring"] = "client_age";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldname"] = "主要联系人";
        $field[$k]["fieldstring"] = "parenter_cnname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "联系号码";
        $field[$k]["fieldstring"] = "client_mobile";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "接通状态";
        $field[$k]["fieldstring"] = "client_answerphone_name";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "客户状态";
        $field[$k]["fieldstring"] = "client_tracestatus_name";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "地推工号";
        $field[$k]["fieldstring"] = "promotion_jobnumber";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldname"] = "职务";
        $field[$k]["fieldstring"] = "post_name";
        $field[$k]["show"] = '1';
        $field[$k]["custom"] = '1';
        $k++;
        $field[$k]["fieldname"] = "接触点";
        $field[$k]["fieldstring"] = "client_frompage";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "最后跟踪时间";
        $field[$k]["fieldstring"] = "track_createtime";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "最后跟踪内容";
        $field[$k]["fieldstring"] = "track_note";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "创建时间";
        $field[$k]["fieldstring"] = "client_createtime";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "是否邀约";
        $field[$k]["fieldstring"] = "is_aud_invite";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "是否到访";
        $field[$k]["fieldstring"] = "is_aud_invite_arrive";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "是否试听";
        $field[$k]["fieldstring"] = "is_audition";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "是否转正";
        $field[$k]["fieldstring"] = "is_positive";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "是否缴费";
        $field[$k]["fieldstring"] = "is_pay";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "录入人";
        $field[$k]["fieldstring"] = "lrmarketer_name";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "渠道类型";
        $field[$k]["fieldstring"] = "client_source";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldname"] = "渠道明细";
        $field[$k]["fieldstring"] = "channel_name";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
//        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'allnum' => $res['allnum'], 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => "暂无活动名单明细报表", 'allnum' => 0, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //个人执行检核表
    function marketerMothRrportView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Crm\ReportModel($request);
        $res = $ReportModel->marketerExecute($request);
        $k = 0;
        $field[$k]["fieldstring"] = "marketer_name";
        $field[$k]["fieldname"] = "教师姓名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "staffer_branch";
        $field[$k]["fieldname"] = "教师编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        
        $k++;
        $field[$k]["fieldstring"] = "staffer_leave";
        $field[$k]["fieldname"] = "在职状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "principal_num";
        $field[$k]["fieldname"] = "意向客户数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "principal_stunum";
        $field[$k]["fieldname"] = "扩科意向客户数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "principal_no_tracknum";
        $field[$k]["fieldname"] = "待有效跟踪客户数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "principal_no_aud_invitenum";
        $field[$k]["fieldname"] = "待邀约客户数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "principal_no_invite_num";
        $field[$k]["fieldname"] = "柜询待确认数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "principal_no_audition_num";
        $field[$k]["fieldname"] = "试听待确认数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "principal_no_positive_num";
        $field[$k]["fieldname"] = "到访未转正数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "principal_stunumthan";
        $field[$k]["fieldname"] = "扩科客户跟踪比";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "principal_notrack_num";
        $field[$k]["fieldname"] = "15日内未进行跟踪客户数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "principal_notthreerack_num";
        $field[$k]["fieldname"] = "低频跟踪客户数(<4次)";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res['list']) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '个人招生执行检核表', 'result' => $result, 'allnum' => $res['allnum']);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => "暂无个人招生执行检核表信息", 'result' => $result, 'allnum' => $res['allnum']);
        }
        ajax_return($res, $request['language_type']);

    }


    //招生业绩年度汇总表
    function getPerformanceYearView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Crm\ReportModel($request);
        $res = $ReportModel->getPerformanceYear($request);

        $k = 0;
        $field[$k]["fieldstring"] = "school_districtname";
        $field[$k]["fieldname"] = "地区";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "school_shortname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "district_cnname";
        $field[$k]["fieldname"] = "所属区域";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "organization";
        $field[$k]["fieldname"] = "校务编制";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "staffer_cnname";
        $field[$k]["fieldname"] = "中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "staffer_enname";
        $field[$k]["fieldname"] = "英文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "post_name";
        $field[$k]["fieldname"] = "本校职务";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "clientnum";
        $field[$k]["fieldname"] = "可追踪有效名单";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "princ_clientnum";
        $field[$k]["fieldname"] = "报名/当年";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res['list']) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '招生业绩年度汇总表', 'result' => $result, 'allnum' => $res['allnum']);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => "暂无招生业绩年度汇总表", 'result' => $result, 'allnum' => $res['allnum']);
        }
        ajax_return($res, $request['language_type']);
    }


    //招生业绩月度汇总表
    function getPerformanceMonthView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Crm\ReportModel($request);
        $res = $ReportModel->getPerformanceMonth($request);

        $k = 0;
        $field[$k]["fieldstring"] = "school_districtname";
        $field[$k]["fieldname"] = "地区";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "school_shortname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "district_cnname";
        $field[$k]["fieldname"] = "所属区域";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "organization";
        $field[$k]["fieldname"] = "校务编制";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "staffer_cnname";
        $field[$k]["fieldname"] = "中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "staffer_enname";
        $field[$k]["fieldname"] = "英文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "post_name";
        $field[$k]["fieldname"] = "本校职务";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "clientnum";
        $field[$k]["fieldname"] = "可追踪有效名单";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "princ_upmonth_num";
        $field[$k]["fieldname"] = "报名/上月";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "princ_nowmonth_num";
        $field[$k]["fieldname"] = "报名/当月";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "princ_month_num";
        $field[$k]["fieldname"] = "报名/月";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res['list']) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '招生业绩月度汇总表', 'result' => $result, 'allnum' => $res['allnum']);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => "暂无招生业绩月度汇总表", 'result' => $result, 'allnum' => $res['allnum']);
        }
        ajax_return($res, $request['language_type']);
    }


    //招生业绩周度汇总表
    function getPerformanceWeekView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Crm\ReportModel($request);
        $res = $ReportModel->getPerformanceWeek($request);

        $k = 0;
        $field[$k]["fieldstring"] = "school_districtname";
        $field[$k]["fieldname"] = "地区";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "school_shortname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "district_cnname";
        $field[$k]["fieldname"] = "所属区域";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "organization";
        $field[$k]["fieldname"] = "校务编制";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "staffer_cnname";
        $field[$k]["fieldname"] = "中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "staffer_enname";
        $field[$k]["fieldname"] = "英文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "post_name";
        $field[$k]["fieldname"] = "本校职务";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "clientnum";
        $field[$k]["fieldname"] = "可追踪有效名单";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "princ_upweek_num";
        $field[$k]["fieldname"] = "报名/上周";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "princ_nowweek_num";
        $field[$k]["fieldname"] = "报名/当周";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "princ_week_num";
        $field[$k]["fieldname"] = "报名/周";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res['list']) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '招生业绩周度汇总表', 'result' => $result, 'allnum' => $res['allnum']);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => "暂无招生业绩周度汇总表", 'result' => $result, 'allnum' => $res['allnum']);
        }
        ajax_return($res, $request['language_type']);
    }

    //招生邀约状况统计表
    function getInviteView()
    {
        $request = Input('get.', '', 'trim,addslashes');
//        $this->ThisVerify($request);
        $ReportModel = new \Model\Crm\ReportModel($request);
        $res = $ReportModel->getInvite($request);

        $k = 0;
        $field[$k]["fieldstring"] = "region_name";
        $field[$k]["fieldname"] = "地区";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "district_cnname";
        $field[$k]["fieldname"] = "所属区域";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "staffer_cnname";
        $field[$k]["fieldname"] = "中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "staffer_enname";
        $field[$k]["fieldname"] = "英文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "post_name";
        $field[$k]["fieldname"] = "本校职务";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "clientnum";
        $field[$k]["fieldname"] = "可追踪有效名单";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "yallnum";
        $field[$k]["fieldname"] = "邀约诺访";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "num3";
        $field[$k]["fieldname"] = "OH邀约诺访";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "num2";
        $field[$k]["fieldname"] = "试听邀约诺访";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "num4";
        $field[$k]["fieldname"] = "柜询插测邀约诺访";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "num13";
        $field[$k]["fieldname"] = "柜询邀约诺访";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "tallnum";
        $field[$k]["fieldname"] = "邀约到访";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "num9";
        $field[$k]["fieldname"] = "OH邀约到访";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "num8";
        $field[$k]["fieldname"] = "试听邀约到访";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "num10";
        $field[$k]["fieldname"] = "柜询插测邀约到访";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "num14";
        $field[$k]["fieldname"] = "柜询邀约到访";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "rate1";
        $field[$k]["fieldname"] = "邀约到访率";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "pallnum";
        $field[$k]["fieldname"] = "邀约到访报名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "rate2";
        $field[$k]["fieldname"] = "邀约到访报名率";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "num5";
        $field[$k]["fieldname"] = "主动到访";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "num6";
        $field[$k]["fieldname"] = "主动到访报名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "rate3";
        $field[$k]["fieldname"] = "主动到访报名率";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        if ($res['allnum']) {
            $result["allnum"] = $res['allnum'];

        } else {
            $result["allnum"] = 0;

        }
        if ($res['list']) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '招生邀约状况统计表', 'result' => $result, 'allnum' => $result["allnum"]);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => "暂无招生邀约状况统计表", 'result' => $result, 'allnum' => $result["allnum"]);
        }
        ajax_return($res, $request['language_type']);
    }

    //招生主动到访统计表
    function getInviteSelfView()
    {
        $request = Input('get.', '', 'trim,addslashes');
//        $this->ThisVerify($request);
        $ReportModel = new \Model\Crm\ReportModel($request);
        $res = $ReportModel->getInviteSelf($request);

        $k = 0;
        $field[$k]["fieldstring"] = "region_name";
        $field[$k]["fieldname"] = "地区";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "district_cnname";
        $field[$k]["fieldname"] = "所属区域";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "staffer_cnname";
        $field[$k]["fieldname"] = "中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "staffer_enname";
        $field[$k]["fieldname"] = "英文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "post_name";
        $field[$k]["fieldname"] = "本校职务";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "clientnum";
        $field[$k]["fieldname"] = "可追踪有效名单";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "num1";
        $field[$k]["fieldname"] = "主动到访/上周";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "num2";
        $field[$k]["fieldname"] = "主动到访报名/上周";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "rate1";
        $field[$k]["fieldname"] = "主动到访报名率/上周";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "num3";
        $field[$k]["fieldname"] = "主动到访/当周";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "num4";
        $field[$k]["fieldname"] = "主动到访报名/当周";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "rate2";
        $field[$k]["fieldname"] = "主动到访报名率/当周";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "num5";
        $field[$k]["fieldname"] = "主动到访/月";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "num6";
        $field[$k]["fieldname"] = "主动到访报名/月";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "rate3";
        $field[$k]["fieldname"] = "主动到访报名率/月";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;


        $result = array();
        $result["field"] = $field;
        if ($res['allnum']) {
            $result["allnum"] = $res['allnum'];

        } else {
            $result["allnum"] = 0;

        }
        if ($res['list']) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '招生主动到访统计表', 'result' => $result, 'allnum' => $result["allnum"]);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => "暂无招生主动到访统计表", 'result' => $result, 'allnum' => $result["allnum"]);
        }
        ajax_return($res, $request['language_type']);
    }

    function getCenterTrackWeekView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Crm\ReportModel($request);
        $res = $Model->getCenterTrackWeek($request);

        $k = 0;
        $field = array();
        $field[$k]["fieldstring"] = "region_name";
        $field[$k]["fieldname"] = "地区";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "district_cnname";
        $field[$k]["fieldname"] = "所属区域";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "staffer_cnname";
        $field[$k]["fieldname"] = "中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "staffer_enname";
        $field[$k]["fieldname"] = "英文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "post_name";
        $field[$k]["fieldname"] = "本校职务";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "listNum";
        $field[$k]["fieldname"] = "可追踪有效名单";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "StuNum";
        $field[$k]["fieldname"] = "追踪人数/周";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "StuTime";
        $field[$k]["fieldname"] = "追踪人次/周";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result, 'allnum' => $res['allnum']);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result, 'allnum' => 0);
        }
        ajax_return($res, $request['language_type']);
    }

    function getCenterTrackMonthView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Crm\ReportModel($request);
        $res = $Model->getCenterTrackMonth($request);

        $k = 0;
        $field = array();
        $field[$k]["fieldstring"] = "region_name";
        $field[$k]["fieldname"] = "地区";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "school_tagbak";
        $field[$k]["fieldname"] = "所属区域";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "staffer_cnname";
        $field[$k]["fieldname"] = "中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "staffer_enname";
        $field[$k]["fieldname"] = "英文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "post_name";
        $field[$k]["fieldname"] = "本校职务";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "listNum";
        $field[$k]["fieldname"] = "可追踪有效名单";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "StuNum";
        $field[$k]["fieldname"] = "追踪人数/月";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
        $field[$k]["fieldstring"] = "StuTime";
        $field[$k]["fieldname"] = "追踪人次/月";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result, 'allnum' => $res['allnum']);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result, 'allnum' => 0);
        }
        ajax_return($res, $request['language_type']);
    }

    function getRegisterInfoView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Crm\ReportModel($request);
        $res = $Model->getRegisterInfo($request);

        //20220815陈喆要求  我、孙亚东和戚总
        $staffer = array(19578, 20458, 12357);

        $k = 0;
        $field = array();
        $field[$k]["fieldstring"] = "info_id";
        $field[$k]["fieldname"] = "数据序号";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_tagbak";
        $field[$k]["fieldname"] = "所属区域";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_branch";
        $field[$k]["fieldname"] = "学员编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["fieldname"] = "中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursetype_cnname";
        $field[$k]["fieldname"] = "报名班组";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "info_type";
        $field[$k]["fieldname"] = "报名类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["ismethod"] = in_array($request['staffer_id'], $staffer) ? 1 : 0;
        $k++;

        $field[$k]["fieldstring"] = "regi_time";
        $field[$k]["fieldname"] = "报名时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "regi_price";
        $field[$k]["fieldname"] = "报名金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "main_marketer";
        $field[$k]["fieldname"] = "主负责人";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["ismethod"] = in_array($request['staffer_id'], $staffer) ? 1 : 0;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res) {
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result, 'allnum' => $res['allnum']);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result, 'allnum' => 0);
        }
        ajax_return($res, $request['language_type']);
    }


    //教师招生分析表
    function getTeachersAdmissionsView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $ReportModel = new \Model\Crm\ReportModel($request);
        $res = $ReportModel->getTeachersAdmissions($request);

        $k = 0;
        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校区编号";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "school_shortname";
        $field[$k]["fieldname"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "staffer_cnname";
        $field[$k]["fieldname"] = "教师中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "staffer_enname";
        $field[$k]["fieldname"] = "教师英文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "post_name";
        $field[$k]["fieldname"] = "本校职务";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "clientnum";
        $field[$k]["fieldname"] = "可追踪有效名单";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "princ_clientnum";
        $field[$k]["fieldname"] = "报名/当年";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "princ_upmonth_num";
        $field[$k]["fieldname"] = "报名/上月";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "princ_nowmonth_num";
        $field[$k]["fieldname"] = "报名/当月";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "princ_month_num";
        $field[$k]["fieldname"] = "报名/月";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "princ_upweek_num";
        $field[$k]["fieldname"] = "报名/上周";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "princ_nowweek_num";
        $field[$k]["fieldname"] = "报名/当周";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "princ_nowweek_num";
        $field[$k]["fieldname"] = "报名/周";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "track_month_num";
        $field[$k]["fieldname"] = "追踪人数/月";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "track_month_time";
        $field[$k]["fieldname"] = "追踪人次/月";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "track_week_num";
        $field[$k]["fieldname"] = "追踪人数/周";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "track_week_time";
        $field[$k]["fieldname"] = "追踪人次/周";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

//        $k++;
//        $field[$k]["fieldstring"] = "invite_upmonth_num";
//        $field[$k]["fieldname"] = "邀约诺访/上月";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//
//        $k++;
//        $field[$k]["fieldstring"] = "ohaudition_upmonth_num";
//        $field[$k]["fieldname"] = "OH邀约诺访/上月";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//
//        $k++;
//        $field[$k]["fieldstring"] = "audition_upmonth_num";
//        $field[$k]["fieldname"] = "试听邀约诺访/上月";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//
//        $k++;
//        $field[$k]["fieldstring"] = "invitetwo_upmonth_num";
//        $field[$k]["fieldname"] = "柜询插测邀约诺访/上月";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//
//        $k++;
//        $field[$k]["fieldstring"] = "invitesan_upmonth_num";
//        $field[$k]["fieldname"] = "柜询邀约诺访/上月";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//
//        $k++;
//        $field[$k]["fieldstring"] = "arrive_upmonth_num";
//        $field[$k]["fieldname"] = "邀约到访/上月";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//
//        $k++;
//        $field[$k]["fieldstring"] = "oharrive_upmonth_num";
//        $field[$k]["fieldname"] = "OH邀约到访/上月";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//
//        $k++;
//        $field[$k]["fieldstring"] = "auditiontwo_upmonth_num";
//        $field[$k]["fieldname"] = "试听邀约到访/上月";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//
//        $k++;
//        $field[$k]["fieldstring"] = "invitesix_upmonth_num";
//        $field[$k]["fieldname"] = "柜询插测邀约到访/上月";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//
//        $k++;
//        $field[$k]["fieldstring"] = "inviteseven_upmonth_num";
//        $field[$k]["fieldname"] = "柜询邀约到访/上月";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//
//        $k++;
//        $field[$k]["fieldstring"] = "arrive_upmonth_rate";
//        $field[$k]["fieldname"] = "邀约到访率/上月";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "invite_nowmonth_num";
        $field[$k]["fieldname"] = "邀约诺访/当月";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "ohaudition_nowmonth_num";
        $field[$k]["fieldname"] = "OH邀约诺访/当月";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "audition_nowmonth_num";
        $field[$k]["fieldname"] = "试听邀约诺访/当月";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "invitetwo_nowmonth_num";
        $field[$k]["fieldname"] = "柜询插测邀约诺访/当月";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "invitesan_nowmonth_num";
        $field[$k]["fieldname"] = "柜询邀约诺访/当月";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "arrive_nowmonth_num";
        $field[$k]["fieldname"] = "邀约到访/当月";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "oharrive_nowmonth_num";
        $field[$k]["fieldname"] = "OH邀约到访/当月";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "auditiontwo_nowmonth_num";
        $field[$k]["fieldname"] = "试听邀约到访/当月";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "invitesix_nowmonth_num";
        $field[$k]["fieldname"] = "柜询插测邀约到访/当月";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "inviteseven_nowmonth_num";
        $field[$k]["fieldname"] = "柜询邀约到访/当月";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "arrive_nowmonth_rate";
        $field[$k]["fieldname"] = "邀约到访率/当月";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

//        $k++;
//        $field[$k]["fieldstring"] = "invite_upweek_num";
//        $field[$k]["fieldname"] = "邀约诺访/上周";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//
//        $k++;
//        $field[$k]["fieldstring"] = "ohaudition_upweek_num";
//        $field[$k]["fieldname"] = "OH邀约诺访/上周";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//
//        $k++;
//        $field[$k]["fieldstring"] = "audition_upweek_num";
//        $field[$k]["fieldname"] = "试听邀约诺访/上周";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//
//        $k++;
//        $field[$k]["fieldstring"] = "invitetwo_upweek_num";
//        $field[$k]["fieldname"] = "柜询插测邀约诺访/上周";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//
//        $k++;
//        $field[$k]["fieldstring"] = "invitesan_upweek_num";
//        $field[$k]["fieldname"] = "柜询邀约诺访/上周";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//
//        $k++;
//        $field[$k]["fieldstring"] = "arrive_upweek_num";
//        $field[$k]["fieldname"] = "邀约到访/上周";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//
//        $k++;
//        $field[$k]["fieldstring"] = "oharrive_upweek_num";
//        $field[$k]["fieldname"] = "OH邀约到访/上周";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//
//        $k++;
//        $field[$k]["fieldstring"] = "auditiontwo_upweek_num";
//        $field[$k]["fieldname"] = "试听邀约到访/上周";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//
//        $k++;
//        $field[$k]["fieldstring"] = "invitesix_upweek_num";
//        $field[$k]["fieldname"] = "柜询插测邀约到访/上周";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//
//        $k++;
//        $field[$k]["fieldstring"] = "inviteseven_upweek_num";
//        $field[$k]["fieldname"] = "柜询邀约到访/上周";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//
//        $k++;
//        $field[$k]["fieldstring"] = "arrive_upweek_rate";
//        $field[$k]["fieldname"] = "邀约到访率/上周";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "invite_nowweek_num";
        $field[$k]["fieldname"] = "邀约诺访/当周";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "ohaudition_nowweek_num";
        $field[$k]["fieldname"] = "OH邀约诺访/当周";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "audition_nowweek_num";
        $field[$k]["fieldname"] = "试听邀约诺访/当周";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "invitetwo_nowweek_num";
        $field[$k]["fieldname"] = "柜询插测邀约诺访/当周";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "invitesan_nowweek_num";
        $field[$k]["fieldname"] = "柜询邀约诺访/当周";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "arrive_nowweek_num";
        $field[$k]["fieldname"] = "邀约到访/当周";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "oharrive_nowweek_num";
        $field[$k]["fieldname"] = "OH邀约到访/当周";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "auditiontwo_nowweek_num";
        $field[$k]["fieldname"] = "试听邀约到访/当周";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "invitesix_nowweek_num";
        $field[$k]["fieldname"] = "柜询插测邀约到访/当周";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "inviteseven_nowweek_num";
        $field[$k]["fieldname"] = "柜询邀约到访/当周";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "arrive_nowweek_rate";
        $field[$k]["fieldname"] = "邀约到访率/当周";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "invite_month_num";
        $field[$k]["fieldname"] = "主动到访/月";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

//        $k++;
//        $field[$k]["fieldstring"] = "enroll_month_num";
//        $field[$k]["fieldname"] = "主动到访报名/月";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//
//        $k++;
//        $field[$k]["fieldstring"] = "enroll_month_rate";
//        $field[$k]["fieldname"] = "主动到访报名率/月";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;

        $k++;
        $field[$k]["fieldstring"] = "invite_week_num";
        $field[$k]["fieldname"] = "主动到访/周";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

//        $k++;
//        $field[$k]["fieldstring"] = "enroll_week_num";
//        $field[$k]["fieldname"] = "主动到访报名/周";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//
//        $k++;
//        $field[$k]["fieldstring"] = "enroll_week_rate";
//        $field[$k]["fieldname"] = "主动到访报名率/周";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $res['allnum'];
        if ($res['list']) {
            $result["fieldcustom"] = 1;
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '教师招生分析表', 'result' => $result, 'allnum' => $res['allnum']);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => "暂无教师招生分析表", 'result' => $result, 'allnum' => $res['allnum']);
        }
        ajax_return($res, $request['language_type']);
    }


    //结尾魔术函数
    function __destruct()
    {

    }
}
