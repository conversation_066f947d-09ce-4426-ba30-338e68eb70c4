<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> <PERSON><PERSON>
 * Date: 2017/4/13
 * Time: 23:25
 * crm-首页
 */

namespace Work\Controller\Crmapi;

class IndexController extends viewTpl
{
	public $u;
	public $t;
	public $c;
	public $visitType = "api";
	
	
	//预加载处理类
	function __construct($visitType = "api")
	{
		parent::__construct ();
		$this->u = $this->router->getController();
		$this->t = $this->router->getUrl();
		$this->c = $this->router->getAction();
	}

    function omitUpdataView(){
        $sql = "SELECT
	o.*
FROM
	crm_remind_org AS o
WHERE
	o.remind_id NOT IN (
		SELECT
			m.remind_id
		FROM
			crm_remind AS m
	)
AND o.marketer_id in (29,60,73,82,87,88,91,95,100,110,111,129,130,131,133,163,167,171,174,200,205,207,208,216,223,244
,298,326,327,429,450,454,481,560,561,632,642)";
        $oldData = $this->DataControl->selectClear($sql);
        if($oldData){
            foreach($oldData as $oldDataOne){
                print_r($oldDataOne);
                $this->DataControl->insertData('crm_remind', $oldDataOne);
            }
        }
    }


	//本地权限校验入口
	function ThisVerify($request){
        $paramArray = array();
        $paramArray['marketer_id'] = $request['marketer_id'];
        if(isset($request['crm_token']) && $request['crm_token'] !=='' ){
            $request['token'] = $request['crm_token'];
        }
        $paramArray['token'] = $request['token'];
        if(!$this->UserLimit($paramArray)){
            $result = array();
            $result["list"] = array();
            $result["tokeninc"] = "0";
            $res = array('error' => '1', 'errortip' => "用户token失效", 'result' => $result);
            ajax_return($res,$request['language_type']);
        }
	}
	//因为加了验证，www那边token不统一迁移到这边来了
    function ComTokenApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $staffer_id = $this->DataControl->getFieldOne("crm_marketer", "staffer_id", "marketer_id = '{$request['marketer_id']}'");
        $token = $this->DataControl->getFieldOne("smc_staffer", "staffer_tokenencrypt,company_id", "staffer_id = '{$staffer_id['staffer_id']}'");
        $companyOne = $this->DataControl->getFieldOne('gmc_company', "company_id,company_language", "company_id = '{$token['company_id']}'");
        ajax_return(array('error' => 0, 'errortip' => "获取成功!", 'token' => $token['staffer_tokenencrypt']), $companyOne['company_language']);

    }
    //切换职务
    function ChangePostApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $stafferOne = $this->DataControl->getFieldOne('smc_staffer', "staffer_id,staffer_leave,staffer_pass,account_class,company_id", "staffer_id = '{$request['staffer_id']}'");

        $companyOne = $this->DataControl->getFieldOne('gmc_company', "company_id,company_language", "company_id = '{$stafferOne['company_id']}'");

        $ComPost = $this->DataControl->getFieldOne("gmc_staffer_postbe", "postbe_id", "staffer_id = '{$stafferOne['staffer_id']}' and school_id = '0'");
        $ScPost = $this->DataControl->getFieldOne("gmc_staffer_postbe", "postbe_id", "staffer_id = '{$stafferOne['staffer_id']}' and school_id > '0'");

        if ($ComPost != '' && $ScPost != '') {
            $status = '0';
        }
        if ($ComPost == '' && $ScPost != '') {
            $status = '2';
        }
        if ($ComPost != '' && $ScPost == '') {
            $status = '1';
        }

        $isAdmin = $stafferOne['account_class'];
        ajax_return(array('error' => 0, 'errortip' => "切换成功!", 'isAdmin' => $isAdmin, 'status' => $status), $companyOne['company_language']);


    }

	//获取对应职工信息   -- 模块跳转
	function stafferApi(){
		$request = Input('get.','','trim,addslashes');
		$this->ThisVerify($request);//验证账户 staffer_id
		
		//model
		$Model = new \Model\Crm\CommonModel($request);
		$dataList = $Model->stafferApi($request);
		
		$field = array();
		$field["staffer_id"] = "职工id";
		$field["token"] = "职工token";
		
		$result = array();
		if($dataList){
			$result["field"] = $field;
			$result["data"] = $dataList;
			$res = array('error' => '0', 'errortip' => '获取职工基本信息成功', 'result' => $result);
		}else{
			$res = array('error' => '1', 'errortip' => '获取职工基本信息失败', 'result' => $result);
		}
		ajax_return($res,$request['language_type']);
	}
	
	
	//首页 -- 招生简章
	function HomeView()
	{
		$request = Input('get.','','trim,addslashes');
		$this->ThisVerify($request);//验证账户
		if(!isset($request['school_id']) || $request['school_id'] == ''){
			$res = array('error' => '1', 'errortip' => '必须选择对应学校', 'result' => array());
			ajax_return($res,$request['language_type']);
		}
		if(!isset($request['client_starttime']) || $request['client_starttime'] == '' || $request['client_endtime'] == ''){
			$res = array('error' => '1', 'errortip' => '必须选择查询时间段', 'result' => array());
			ajax_return($res,$request['language_type']);
		}
		//model
		$Model = new \Model\Crm\IndexModel($request);
		$dataList = $Model->recruitStudent($request);
		
		$field = array();
        $field["addGrossClient"] = "招生统计--新增毛名单";
        $field["yaoyueClient"] = "招生统计--新增邀约客户";
        $field["wuxiaoClient"] = "招生统计--新增无效客户";
        $field["zhengshiClient"] = "招生统计--新增正式学员";

        $field["addGrossClient"] = "招生转化统计--毛名单";
        $field["intentionClient"] = "招生转化统计--意向客户";
        $field["allInviteClient"] = "招生转化统计--柜询客户";
        $field["auditionClient"] = "招生转化统计--试听客户";
        $field["officialClient"] = "招生转化统计--转正客户";

		//转化率
		$field["maoToYiRate"] = "转化率--毛名单转化为意向客户数量/转化率";
        $field["yiToShiRate"] = "转化率--意向客户转化为试听客户数量/转化率";
        $field["yiToZhengRate"] = "转化率--意向客户转化为正式客户数量/转化率";
        $field["maoToWuRate"] = "转化率--毛名单转化为无效客户数量/转化率";


		$result = array();
		if($dataList){
			$result["field"] = $field;
			$result["data"] = $dataList;
			$res = array('error' => '0', 'errortip' => '首页招生简章数据统计', 'result' => $result);
		}else{
			$result["data"] = array();
			$res = array('error' => '1', 'errortip' => '首页招生简章数据统计', 'result' => $result);
		}
		ajax_return($res,$request['language_type']);
	}
	//首页 -- 跟踪提醒
	function remindApi()
	{
		$request = Input('get.','','trim,addslashes');
		$this->ThisVerify($request);//验证账户
//        if(!isset($request['remind_starttime']) || $request['remind_starttime'] == '' || $request['remind_endtime'] == ''){
//            $res = array('error' => '1', 'errortip' => '必须选择查询时间段', 'result' => array());
//            ajax_return($res,$request['language_type']);
//        }
		//model
		$Model = new \Model\Crm\IndexModel($request);
		$dataList = $Model->remindApi($request);
		
		$field = array();
		$field[0]["fieldname"] = "client_cnname";
		$field[0]["fieldstring"] = "姓名";
		$field[0]["show"] = 1;
		$field[0]["custom"] = 0;
		
		$field[1]["fieldname"] = "client_sex";
		$field[1]["fieldstring"] = "性别";
		$field[1]["show"] = 1;
		$field[1]["custom"] = 0;
		
		$field[2]["fieldname"] = "trackNum";
		$field[2]["fieldstring"] = "跟踪次数";
		$field[2]["show"] = 1;
		$field[2]["custom"] = 0;
		
		$field[3]["fieldname"] = "client_mobile";
		$field[3]["fieldstring"] = "联系手机";
		$field[3]["show"] = 0;
		$field[3]["custom"] = 0;
		
		$field[4]["fieldname"] = "client_tracestatus";
		$field[4]["fieldstring"] = "客户状态";
		$field[4]["show"] = 1;
		$field[4]["custom"] = 0;
		
		$field[5]["fieldname"] = "lastTrackTime";
		$field[5]["fieldstring"] = "上次跟踪时间";
		$field[5]["show"] = 0;
		$field[5]["custom"] = 0;
		
		$field[6]["fieldname"] = "nextTrackTime";
		$field[6]["fieldstring"] = "下次跟踪时间";
		$field[6]["show"] = 1;
		$field[6]["custom"] = 0;
		
		$field[7]["fieldname"] = "remind_id";
		$field[7]["fieldstring"] = "序号";
		$field[7]["show"] = 0;
		$field[7]["custom"] = 0;
		
		$field[8]["fieldname"] = "remind_time";
		$field[8]["fieldstring"] = "提醒时间";
		$field[8]["show"] = 1;
		$field[8]["custom"] = 0;
		
		$field[9]["fieldname"] = "remind_remark";
		$field[9]["fieldstring"] = "提醒备注";
		$field[9]["show"] = 0;
		$field[9]["custom"] = 0;
		
		$field[10]["fieldname"] = "client_enname";
		$field[10]["fieldstring"] = "客户英文名";
		$field[10]["show"] = 0;
		$field[10]["custom"] = 0;
		
		$field[11]["fieldname"] = "todayTrackNum";
		$field[11]["fieldstring"] = "是否已经跟踪";
		$field[11]["show"] = 0;
		$field[11]["custom"] = 0;
		
		$field[12]["fieldname"] = "isTrack";
		$field[12]["fieldstring"] = "是否跟踪";
		$field[12]["show"] = 0;
		$field[12]["custom"] = 0;
		
		$field[13]["fieldname"] = "isTracknum";
		$field[13]["fieldstring"] = "标签：跟踪名称";
		$field[13]["show"] = 0;
		$field[13]["custom"] = 0;
		
		$field[14]["fieldname"] = "client_age";
		$field[14]["fieldstring"] = "年龄";
		$field[14]["show"] = 0;
		$field[14]["custom"] = 0;
		
		$field[15]["fieldname"] = "client_id";
		$field[15]["fieldstring"] = "客户id";
		$field[15]["show"] = 0;
		$field[15]["custom"] = 0;

		$field[15]["fieldname"] = "stuisloss";
		$field[15]["fieldstring"] = "是否流失  0 没有  1 已流失  空是crm";
		$field[15]["show"] = 0;
		$field[15]["custom"] = 0;
		
		$result = array();
		if($dataList){
			$result["fieldcustom"] = 1;
			$result["field"] = $field;
			if(is_array($dataList["datalist"])) {
				$result["data"] = $dataList["datalist"];
			}else{
				$result["data"] = array();
			}
			if($result["data"]){
				$res = array('error' => '0', 'errortip' => '跟踪提醒','allnum' => $dataList['count'], 'result' => $result);
			}else{
				$res = array('error' => '1', 'errortip' => '本周暂无招生跟踪提醒','allnum' => 0, 'result' => $result);
			}
		}else{
			$result["data"] = array();
			$res = array('error' => '1', 'errortip' => '本周暂无招生跟踪提醒', 'result' => $result);
		}
		ajax_return($res,$request['language_type']);
	}
	//首页 -- 新增日程安排
	function addEventAction(){
		$request = Input('post.','','trim,addslashes');
		$this->ThisVerify($request);//验证账户
		//model
		$Model = new \Model\Crm\IndexModel($request);
		$dataList = $Model->addEventAction($request);
		
		if($dataList){
			$res = array('error' => '0', 'errortip' => "日程安排新增成功");
		}else{
			$res = array('error' => '1', 'errortip' => '日程安排新增失败');
		}
		ajax_return($res,$request['language_type']);
	}
	//首页 -- 某日的日程安排
	function eventOneApi(){
		$request = Input('get.','','trim,addslashes');
		$this->ThisVerify($request);//验证账户
		if(!isset($request['event_time']) || $request['event_time'] == ''){
			$res = array('error' => '1', 'errortip' => '必须输入查询时间', 'result' => array());
		}
		//model
		$Model = new \Model\Crm\IndexModel($request);
		$dataList = $Model->eventOneApi($request);
		
		$field = array();
		$field["event_tag"] = "事件标签：字符串";
		$field["event_remark"] = "跟进提醒备注";
        $field["client_id"] = "名单id";
		
		$result = array();
		if($dataList){
			$result["field"] = $field;
			$result["data"] = $dataList;
			$res = array('error' => '0', 'errortip' => '某日的日程安排', 'result' => $result);
		}else{
			$result["data"] = array();
			$res = array('error' => '1', 'errortip' => '某日的日程安排', 'result' => $result);
		}
		ajax_return($res,$request['language_type']);
	}
	//首页 -- 月份列表日程展示
	function monthEventApi()
	{
		$request = Input('get.','','trim,addslashes');
		$this->ThisVerify($request);//验证账户
		//model
		$Model = new \Model\Crm\IndexModel($request);
		$dataList = $Model->monthEventApi($request);
		
		$field = array();
		$field["event_id"] = "事件序号";
		$field["year"] = "年";
		$field["month"] = "月";
		$field["day"] = "日";
		$field["is_have"] = "是否有事件：-1 没有";
		
		$result = array();
		if($dataList){
			$result["field"] = $field;
			$result["theWeek"] = $dataList['week'];
			$result["data"] = $dataList['mothList'];
			$res = array('error' => '0', 'errortip' => '某日的日程安排', 'result' => $result);
		}else{
			$result["data"] = array();
			$res = array('error' => '1', 'errortip' => '某日的日程安排', 'result' => $result);
		}
		ajax_return($res,$request['language_type']);
	}
	//首页 -- 校园教师
	function schoolMarketerApi()
	{
		$request = Input('get.','','trim,addslashes');
		$this->ThisVerify($request);//验证账户
		//model
		$Model = new \Model\Crm\IndexModel($request);
		$dataList = $Model->schoolMarketerApi($request);
		
		$field = array();
		$field["marketer_id"] = "员工id";
		$field["marketer_name"] = "员工姓名";
		
		$result = array();
		if($dataList){
			$result["field"] = $field;
			$result["data"] = $dataList;
			$res = array('error' => '0', 'errortip' => '校园教师获取成功', 'result' => $result);
		}else{
			$result["data"] = array();
			$res = array('error' => '1', 'errortip' => '校园教师获取失败', 'result' => $result);
		}
		ajax_return($res,$request['language_type']);
	}
	
	
	
	//首页 -- 职工对应学校
	function getMarketerSchoolApi()
	{
		$request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
		//model
		$Model = new \Model\Crm\CommonModel($request);
		$dataList = $Model->getMarketerSchoolApi($request);
		
		$field = array();
		$field["school_id"] = "序号";
		$field["school_cnname"] = "校园名称";
		$field["school_enname"] = "检索代码";
		
		$result = array();
		if($dataList){
			$result["field"] = $field;
			$result["data"] = $dataList;
			$res = array('error' => '0', 'errortip' => '职工对应学校获取成功', 'result' => $result);
		}else{
			$result["data"] = array();
			$res = array('error' => '1', 'errortip' => '职工对应学校获取失败', 'result' => $result);
		}
		ajax_return($res,$request['language_type']);
	}
	//首页 -- 筛选学校11
	function getSchoolApi()
	{
		$request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
		//model
		$Model = new \Model\Crm\CommonModel($request);
		$dataList = $Model->getSchoolApi($request);

        if ($dataList['datalist'][$request['company_id']]) {
            foreach ($dataList['datalist'][$request['company_id']] as &$val) {
                if($request['re_postbe_id'] == '0'){
                    $status = '1';
                }else{
                    $a = $this->DataControl->getFieldOne("gmc_staffer_postbe","school_id,postrole_id,postbe_iscrmuser","postbe_id = '{$val['postbe_id']}'");
                    if($a['school_id'] == '0'){
                        $b = $this->DataControl->getFieldOne("gmc_company_postrole","postpart_iscmsuser","postrole_id = '{$a['postrole_id']}'");
                        if($b['postpart_iscmsuser'] == '1'){
                            $status = '1';
                        }else{
                            $status = '0';
                        }
                    }else{
                        $status = '1';
                    }
                }
                $val['status'] = $status;
            }
        }
		
		$field = array();
		$field["school_id"] = "序号";
		$field["company_id"] = "所属公司";
		$field["district_id"] = "所属集团区域ID";
		$field["school_branch"] = "校区编号";
		$field["school_shortname"] = "校园简称";
		$field["school_cnname"] = "校园名称称";
		$field["school_enname"] = "检索代码称";
		$field["company_cnname"] = "机构名称";
		
		$result = array();
		if($dataList){
			$result["field"] = $field;
			$result["data"] = $dataList['datalist'];
			$res = array('error' => '0', 'errortip' => '筛选学校成功', 'initial' =>  $dataList['initial'],'allid' =>  $dataList['allid'], 'result' => $result);
		}else{
			$result["data"] = array();
			$res = array('error' => '1', 'errortip' => '筛选学校失败', 'result' => $result);
		}
		ajax_return($res,$request['language_type']);
	}
	//首页 -- 筛选学校机构
	function getSchooOrganizelApi()
	{
		$request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
		//model
		$Model = new \Model\Crm\CommonModel($request);
		$dataList = $Model->getSchooOrganizelApi($request);
		
		$field = array();
		$field["organize_id"] = "序号";
		$field["organize_cnname"] = "机构中文名称";
		$field["organize_enname"] = "机构英文名称";
		
		$result = array();
		if($dataList){
			$result["field"] = $field;
			$result["data"] = $dataList;
			$res = array('error' => '0', 'errortip' => '筛选学校机构成功', 'result' => $result);
		}else{
			$result["data"] = array();
			$res = array('error' => '1', 'errortip' => '筛选学校机构失败', 'result' => $result);
		}
		ajax_return($res,$request['language_type']);
	}
	
	
	
	//首页 -- 个人详细信息
	function getMarketerApi()
	{
		$request = Input('get.','','trim,addslashes');
//		$this->ThisVerify($request);//验证账户
		//model
		$Model = new \Model\Crm\CommonModel($request);
		$dataList = $Model->getMarketerApi($request);


		$field = array();
		$field["marketer_name"] = "姓名";
		$field["marketer_mobile"] = "手机号";
		$field["marketer_img"] = "头像";
        $field["staffer_sex"] = "性别";
        $field["company_logo"] = "企业Logo";
        $field["nearnum"] = "附近学校数量";
        $field["isopennearschool"] = "是否开启：1开启0为开启";
        $field["company_ismajor"] = "集团版本";
        $field["company_isnointention"] = "跟进类型是否出现无意向 0 不出现 1 出现";
        $field["seats_intentionlevel"] = "400电话允许使用的星级";
		if($dataList){
			$result["field"] = $field;
			$result["data"] = $dataList;
			$res = array('error' => '0', 'errortip' => '用户详情信息获取成功', 'result' => $result);
		}else{
			$result["data"] = array();
			$res = array('error' => '1', 'errortip' => '用户详情信息获取失败', 'result' => $result);
		}
		ajax_return($res,$request['language_type']);
	}
	//首页 -- 获取当前用户联系人
	function getLinkmanApi()
	{
		$request = Input('get.','','trim,addslashes');
		$this->ThisVerify($request);//验证账户
		$school_id = $request['school_id'];
		$company_id = $request['company_id'];
		if(!isset($school_id) || $school_id == '' || $company_id == ''){
			$res = array('error' => '1', 'errortip' => '集团和学校必传', 'result' => array());
			ajax_return($res,$request['language_type']);
		}
		
		//model
		$Model = new \Model\Crm\CommonModel($request);
		$dataList = $Model->getLinkmanApi($request);
		
		$field = array();
		$field["staffer_id"] = "职工序号";
		$field["staffer_img"] = "职工头像";
		$field["staffer_cnname"] = "职工姓名";
		$field["staffer_enname"] = "职工英文姓名";
		$field["staffer_sex"] = "职工性别";
		$field["post_id"] = "职工职务id";
		$field["post_name"] = "职工职务名称";
		$field["staffer_mobile"] = "职工手机号";
		$field["staffer_branch"] = "职工编号";
		
		if($dataList){
			$result["field"] = $field;
			$result["data"] = $dataList;
			$res = array('error' => '0', 'errortip' => '筛选联系人成功', 'result' => $result);
		}else{
			$result["data"] = array();
			$res = array('error' => '1', 'errortip' => '筛选联系人失败', 'result' => $result);
		}
		ajax_return($res,$request['language_type']);
	}

    //首页 -- 筛选区域
    function getDistrictApi()
    {
        $request = Input('get.','','trim,addslashes');

        //model
        $Model = new \Model\Smc\CommonModel($request);
        $dataList = $Model->getDistrictApi($request);

        $field = array();
        $field["district_id"] = "序号";
        $field["company_id"] = "所属公司";
        $field["district_branch"] = "区域代码";
        $field["district_cnname"] = "区域名称";
        $field["district_sort"] = "区域排序";
        $field["district_content"] = "区域描述";

        if($dataList){
            $result["field"] = $field;
            $result["data"] = $dataList;
            $res = array('error' => '0', 'errortip' => '筛选区域成功', 'result' => $result);
        }else{
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '筛选区域失败', 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }
    //首页 -- 筛选省份
    function getProvinceApi()
    {
        $request = Input('get.','','trim,addslashes');

        //model
        $Model = new \Model\Smc\CommonModel($request);
        $dataList = $Model->getProvinceApi($request);

        $field = array();
        $field["region_id"] = "序号";
        $field["region_initial"] = "首字母";
        $field["region_iscrown"] = "直辖";
        $field["region_code"] = "区域代码";
        $field["region_name"] = "地理名称";
        $field["region_enname"] = "英文名称";
        $field["region_shortenname"] = "英文简称";

        if($dataList){
            $result["field"] = $field;
            $result["data"] = $dataList;
            $res = array('error' => '0', 'errortip' => '筛选省份成功', 'result' => $result);
        }else{
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '筛选省份失败', 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }
    //首页 -- 筛选城市
    function getCityApi()
    {
        $request = Input('get.','','trim,addslashes');

        $region_id = $request['region_id'];
        if(!isset($region_id) || $region_id == '' || $region_id == '0'){
            $res = array('error' => '1', 'errortip' => '必须选择省份', 'result' => array());
            ajax_return($res,$request['language_type']);
        }

        //model
        $Model = new \Model\Smc\CommonModel($request);
        $dataList = $Model->getCityApi($request);

        $field = array();
        $field["region_id"] = "序号";
        $field["region_initial"] = "首字母";
        $field["region_iscrown"] = "直辖";
        $field["region_code"] = "区域代码";
        $field["region_name"] = "地理名称";
        $field["region_enname"] = "英文名称";
        $field["region_shortenname"] = "英文简称";

        if($dataList){
            $result["field"] = $field;
            $result["data"] = $dataList;
            $res = array('error' => '0', 'errortip' => '筛选城市成功', 'result' => $result);
        }else{
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '筛选城市失败', 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }
    //首页 -- 筛选区域
    function getAreaApi()
    {
        $request = Input('get.','','trim,addslashes');

        $region_id = $request['region_id'];
        if(!isset($region_id) || $region_id == '' || $region_id == '0'){
            $res = array('error' => '1', 'errortip' => '必须选择城市', 'result' => array());
            ajax_return($res,$request['language_type']);
        }

        //model
        $Model = new \Model\Smc\CommonModel($request);
        $dataList = $Model->getAreaApi($request);

        $field = array();
        $field["region_id"] = "序号";
        $field["region_initial"] = "首字母";
        $field["region_iscrown"] = "直辖";
        $field["region_code"] = "区域代码";
        $field["region_name"] = "地理名称";
        $field["region_enname"] = "英文名称";
        $field["region_shortenname"] = "英文简称";

        if($dataList){
            $result["field"] = $field;
            $result["data"] = $dataList;
            $res = array('error' => '0', 'errortip' => '筛选城市成功', 'result' => $result);
        }else{
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '筛选城市失败', 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }
    //首页--统计现在的招生数据
    function getEnrollStuCountApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Smc\CommonModel($request);
        $dataList = $Model->getEnrollStuCount($request);
        $field = array();
        $field["clientgross_num"] = $this->ConStringSwitch("待筛选毛名单",$request['language_type']);
        $field["client_num"] = $this->ConStringSwitch("待分配有效名单",$request['language_type']);//当前有效名单数/
        $field["intention_num"] = $this->ConStringSwitch("意向客户",$request['language_type']);//当前意向客户数/
        $field["notrack_principalNum"] = $this->ConStringSwitch("待跟踪名单",$request['language_type']);//待跟踪客户数/
//        $field["seven_notrack_num"] = $this->ConStringSwitch("7日内未跟踪名单",$request['language_type']);//7日及以上未跟踪客户数/
        if($dataList['companyOne']['company_isnointention'] == '1') {
            $field["all_islossNum"] = $this->ConStringSwitch("待审核无意向名单", $request['language_type']);
        }

        $field["all_isinvalidNum"] = $this->ConStringSwitch("待审核无效名单", $request['language_type']);

//        $field["moth_auditionNum"] = $this->ConStringSwitch("本月累计邀约试听名单",$request['language_type']);
//        $field["moth_positiveNum"] = $this->ConStringSwitch("本月累计转正人数",$request['language_type']);
        $field["yuqi"] = $this->ConStringSwitch("低跟进名单",$request['language_type']);
        $field["yujing"] = $this->ConStringSwitch("低跟进预警名单",$request['language_type']);

//        $field["moth_lossNum"] = $this->ConStringSwitch("30日累计无意向客户数",$request['language_type']);
//        $field["invite_num"] = $this->ConStringSwitch(" 未柜询客户数",$request['language_type']);
//        $field["audition_num"] = $this->ConStringSwitch("未试听人数",$request['language_type']);
//        $field["loss_num"] = $this->ConStringSwitch(" 历史累计无意向人数",$request['language_type']);
//        $field["conversion_num"] = $this->ConStringSwitch("累计转正人数",$request['language_type']);
//        $field["marketer_num"] = $this->ConStringSwitch("招生负责人数",$request['language_type']);
//        $field["activity_num"] = $this->ConStringSwitch("正在进行活动数",$request['language_type']);
//        $field["no_auditionclientNum"] = $this->ConStringSwitch("未试听意向客户",$request['language_type']);
//        $field["no_inviteclientNum"] = $this->ConStringSwitch("未邀约意向客户",$request['language_type']);
//        $field["uneffective_num"] = $this->ConStringSwitch("累计无效客户",$request['language_type']);

        //前端现在要的格式 -- 首页统计信息 8 个数字

        $key = 0;
        $nowdatalist = array();
        $nowdatalist[$key]['text'] = $field["clientgross_num"];
        $nowdatalist[$key]['num'] = $dataList['clientgross_num'];
        $nowdatalist[$key]['url'] = "/ListAllocationManagement/grossList";
        $nowdatalist[$key]['content'] = $this->ConStringSwitch("显示所有待筛选毛名单数",$request['language_type']);
        $nowdatalist[$key]['imgurl'] = "https://pic.kedingdang.com/schoolmanage/crmhomeicons/count1.png";
        $key++;

        $nowdatalist[$key]['text'] = $field["client_num"];
        $nowdatalist[$key]['num'] = $dataList['client_num'];
        $nowdatalist[$key]['url'] = "/ListAllocationManagement/preSetValidList";
        $nowdatalist[$key]['content'] = $this->ConStringSwitch("显示所有待分配有效名单数",$request['language_type']);
        $nowdatalist[$key]['imgurl'] = "https://pic.kedingdang.com/schoolmanage/crmhomeicons/count2.png";
        $key++;

        $nowdatalist[$key]['text'] = $field["intention_num"];
        $nowdatalist[$key]['num'] = $dataList['intention_num'];
        $nowdatalist[$key]['url'] = "/Customersmanage/custoMersmanage";
        $nowdatalist[$key]['content'] = $this->ConStringSwitch("普通权限显示我的意向客户总数，TMK和高管权限显示所有意向客户总数",$request['language_type']);
        $nowdatalist[$key]['imgurl'] = "https://pic.kedingdang.com/schoolmanage/crmhomeicons/count3.png";
        $key++;

        $nowdatalist[$key]['text'] = $field["notrack_principalNum"];
        $nowdatalist[$key]['num'] = $dataList['notrack_principalNum'];
        if($request['postbe_crmuserlevel'] == '2'){
            $nowdatalist[$key]['url'] = "/Customersmanage/telemarketList?client_tracestatus=0";
        }else {
            $nowdatalist[$key]['url'] = "/Customersmanage/custoMersmanage?client_tracestatus=0";
        }
        $nowdatalist[$key]['content'] = $this->ConStringSwitch("普通权限显示我的意向客户待跟踪数，TMK权限显示我的待跟踪毛名单数，高管权限显示所有待跟踪意向客户数",$request['language_type']);
        $nowdatalist[$key]['imgurl'] = "https://pic.kedingdang.com/schoolmanage/crmhomeicons/count4.png";
        $key++;

//        $nowdatalist[$key]['text'] = $field["seven_notrack_num"];
//        $nowdatalist[$key]['num'] = $dataList['seven_notrack_num'];
//        if($request['postbe_crmuserlevel'] == '2') {
//            $nowdatalist[$key]['url'] = "/Customersmanage/telemarketList?nohavetrack=1";
//        }else{
//            $nowdatalist[$key]['url'] = "/Customersmanage/custoMersmanage?nohavetrack=1";//client_tracestatus=0&
//        }
//        $nowdatalist[$key]['content'] = $this->ConStringSwitch("普通权限显示我的7日未跟踪意向客户数，TMK权限显示我的7日未跟踪毛名单数，高管权限显示所有7日未跟踪意向客户数",$request['language_type']);
//        $nowdatalist[$key]['imgurl'] = "https://pic.kedingdang.com/schoolmanage/crmhomeicons/count5.png";
//        $key++;

        if($dataList['companyOne']['company_isnointention'] == '1') {
            $nowdatalist[$key]['text'] = $field["all_islossNum"];
            $nowdatalist[$key]['num'] = $dataList['all_islossNum'];
            $nowdatalist[$key]['url'] = "/Losscustomermanage/losscustomerManage";
            $nowdatalist[$key]['content'] = $this->ConStringSwitch("显示所有待审核无意向名单总数", $request['language_type']);
            $nowdatalist[$key]['imgurl'] = "https://pic.kedingdang.com/schoolmanage/crmhomeicons/count6.png";
            $key++;
        }

        //待审核无效名单总数
        if($request['postbe_crmuserlevel'] == '1') {
            $nowdatalist[$key]['text'] = $field["all_isinvalidNum"];
            $nowdatalist[$key]['num'] = $dataList['all_isinvalidNum'];
            $nowdatalist[$key]['url'] = "/Losscustomermanage/invalidCustomer";
            $nowdatalist[$key]['content'] = $this->ConStringSwitch("显示所有待审核无效名单总数", $request['language_type']);
            $nowdatalist[$key]['imgurl'] = "https://pic.kedingdang.com/schoolmanage/crmhomeicons/count6.png";
            $key++;
        }

//        $nowdatalist[$key]['text'] = $field["moth_auditionNum"];
//        $nowdatalist[$key]['num'] = $dataList['moth_auditionNum'];
//        $nowdatalist[$key]['url'] = "/Invitationauditionmanage/auditionManage?current=3";
//        $nowdatalist[$key]['content'] = $this->ConStringSwitch("显示本月累计邀约试听名单记录",$request['language_type']);
//        $nowdatalist[$key]['type'] = "999";
//        $nowdatalist[$key]['imgurl'] = "https://pic.kedingdang.com/schoolmanage/crmhomeicons/count7.png";
//        $key++;

//        $monthstart_time = date( 'Y-m-01', time() );
//        $mdays = date( 't', time());
//        $monthend_time = date( 'Y-m-' . $mdays, time() );
//        $nowdatalist[$key]['text'] = $field["moth_positiveNum"];
//        $nowdatalist[$key]['num'] = $dataList['moth_positiveNum'];
//        $nowdatalist[$key]['url'] = "/Registeredlist/registeredList?conversionlog_startday={$monthstart_time}&conversionlog_endday={$monthend_time}";
//        $nowdatalist[$key]['content'] = $this->ConStringSwitch("显示本月累计转正名单数",$request['language_type']);
//        $nowdatalist[$key]['imgurl'] = "https://pic.kedingdang.com/schoolmanage/crmhomeicons/count8.png";
//        $key++;

        //跟进逾期名单 250708
        if($request['postbe_crmuserlevel'] == '1' || $request['postbe_crmuserlevel'] == '0' ) {
            $nowdatalist[$key]['text'] = $field["yuqi"];
            $nowdatalist[$key]['num'] = $dataList['yuqi'];
            $nowdatalist[$key]['url'] = "/Customersmanage/custoMersmanage?type=2";
            $nowdatalist[$key]['content'] = $this->ConStringSwitch("普通权限显示自己的意向客户低跟进名单数，高管权限显示所有意向客户低跟进名单数", $request['language_type']);
            $nowdatalist[$key]['imgurl'] = "https://oss.kidcastle.cn/xy-asset/images/yuqi-icon.png";
            $key++;

            //低频跟进预警名单 250708
            $nowdatalist[$key]['text'] = $field["yujing"];
            $nowdatalist[$key]['num'] = $dataList['yujing'];
            $nowdatalist[$key]['url'] = "/Customersmanage/custoMersmanage?type=3";
            $nowdatalist[$key]['content'] = $this->ConStringSwitch("普通权限显示自己的意向客户低跟进预警名单数，高管权限显示所有意向客户低跟进预警名单数", $request['language_type']);
            $nowdatalist[$key]['imgurl'] = "https://oss.kidcastle.cn/xy-asset/images/yuqi-warning-icon.png";
            $key++;
        }
        $result["datanow"] = $nowdatalist;//前端现在要的格式  -- 首页统计信息 8 个数字
        $result["data"] = $dataList;//手机 CRM 使用
        $res = array('error' => '0', 'errortip' => '获取成功', 'result' => $result);
        ajax_return($res,$request['language_type']);
    }

    //首页--统计现在的招生数据  --- 之前的写法
	function getEnrollStuCountBakApi(){
		$request = Input('get.','','trim,addslashes');
		$this->ThisVerify($request);//验证账户
		$Model = new \Model\Smc\CommonModel($request);
		$dataList = $Model->getEnrollStuCount($request);
		$field = array();
		$field["clientgross_num"] = "待筛选毛名单";
		$field["client_num"] = "待分配有效名单";//当前有效名单数/
		$field["intention_num"] = "意向客户";//当前意向客户数/
        $field["notrack_principalNum"] = "待跟踪名单";//待跟踪客户数/
        $field["seven_notrack_num"] = "7日内未跟踪名单";//7日及以上未跟踪客户数/
        $field["all_islossNum"] = "待审核无意向名单";
        $field["moth_auditionNum"] = "本月累计邀约试听名单";
        $field["moth_positiveNum"] = "本月累计转正人数";

        $field["moth_lossNum"] = "30日累计无意向客户数";
		$field["invite_num"] = " 未柜询客户数";
		$field["audition_num"] = "未试听人数";
		$field["loss_num"] = " 历史累计无意向人数";
		$field["conversion_num"] = "累计转正人数";
		$field["marketer_num"] = "招生负责人数";
		$field["activity_num"] = "正在进行活动数";
		$field["no_auditionclientNum"] = "未试听意向客户";
		$field["no_inviteclientNum"] = "未邀约意向客户";
		$field["uneffective_num"] = "累计无效客户";


		$result["field"] = $field;
		$result["data"] = $dataList;
		$res = array('error' => '0', 'errortip' => '获取成功', 'result' => $result);
		ajax_return($res,$request['language_type']);
	}
	
	
	//校园名称 与 首字母 转换
	function getSchoolInitialAction()
	{
//        $request = Input('get.','','trim,addslashes');
////        $this->ThisVerify($request);//验证账户
//
//        $sqlfields = " s.school_cnname,s.school_id ";
//        $datawhere = " s.school_cnname_initial = '' ";
//        $schoolList = $this->DataControl->selectClear("SELECT {$sqlfields} FROM smc_school as s where {$datawhere} ");;
//
//        if($schoolList){
//            foreach ($schoolList as $schoolVar){
//                $initial = getfirstchar($schoolVar['school_cnname']);
//                $data = array();
//                $data['school_cnname_initial'] = $initial;
//                $this->DataControl->updateData("smc_school"," school_id = '{$schoolVar['school_id']}' ",$data);
//            }
//        }
//
//        $res = array('error' => '0', 'errortip' => '筛选城市成功', 'result' => array());
//        ajax_return($res,$request['language_type']);
	}

	//获取集团招生跟踪明细
    function  getTmkClientRemindApi(){
        $request = Input('get.','','trim,addslashes');
		$this->ThisVerify($request);//验证账户
        $Model = new \Model\Crm\ClientModel($request);
        $dataList = $Model->getTmkClientRemindApi($request);
        $field = array();
        $field["client_id"] = '客户id';
        $field["client_cnname"] = "客户中文名";
        $field["client_enname"] = " 客户英文名";
        $field["client_createtime"] = "创建时间";
        $field["client_tracestatus_name"] = "状态";
        $field["client_source"] = "渠道类型";
        $field["channel_name"] = "渠道明细";
        $field["client_distributionstatus_name"] = "分配状态";
        $result["field"] = $field;
        $result["data"] = $dataList;
        $res = array('error' => '0', 'errortip' => '获取成功', 'result' => $result);
        ajax_return($res,$request['language_type'],1);

    }

    //单校教师业绩统计
    function getSchMarketerEnrollApi(){
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        //model
        $Model = new \Model\Crm\IndexModel($request);
        $res = $Model->getSchMarketerEnrollApi($request);

        $fieldstring = array('marketer_name','staffer_branch','clientnum');
        $fieldname = array('教师姓名', '教师编号', '转化报名人数');
        $fieldcustom = array("1", "1", "1");
        $fieldshow = array("1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }
        if($res['typefield']){
            $field = array_merge($field,$res['typefield']);
        }

        $resultarray = array();
        $resultarray['field'] = $field;
        $resultarray['all_num'] = $res['all_num']>0?$res['all_num']:'0';
        $resultarray['list'] = is_array($res['list'])?$res['list']:array();

        $result = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $resultarray);
        ajax_return($result,$request['language_type']);
    }


}
