<?php


namespace Work\Controller\Scptcapi;


class StuHomeworkController extends viewTpl
{
    public $u;
    public $t;
    public $c;

    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
    }

    function stuMonthListApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Scptc\TimetableModel();
        $res = $Model->stuMonthList($request);
        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res);
    }

    function stuTimetableListApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Scptc\TimetableModel();
        $res = $Model->stuTimetableList($request);
        $result = array();
        if ($res) {
            $result["list"] = $res['list'];
            $result["allnum"] = $res['allnum'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res);

    }

    function isHourApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Scptc\TimetableModel();
        $res = $Model->isHourApi($request);
        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res);

    }

    function stuHourItemApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Scptc\TimetableModel();
        $res = $Model->stuHourItem($request);
        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res);

    }

    function commentHourAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Scptc\TimetableModel();
        $res = $Model->commentHour($request);
        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '评论成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res);

    }

    function getStuHomeworkListApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Scptc\HomeworkModel();
        $res = $Model->getStuHomeworkList($request);
        $result = array();
        if ($res) {
            $result["list"] = $res['list'];
            $result["allnum"] = $res['allnum'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res);

    }

    function getStuDoneHomeworkListApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Scptc\HomeworkModel();
        $res = $Model->getStuDoneHomeworkList($request);
        $result = array();
        if ($res) {
            $result["list"] = $res['list'];
            $result["allnum"] = $res['allnum'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["allnum"] = 0;
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res);

    }

    function homeworkItemApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Scptc\HomeworkModel();
        $res = $Model->homeworkItem($request);
        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res);

    }

    //查看作业
    function HomeworkDetailApi(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Scptc\HomeworkModel();
        $res = $Model->HomeworkDetailApi($request);
        $result = array();
        if ($res) {
            $result["list"] = $res['list'];
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res);
    }

    function handHomeworkAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Scptc\HomeworkModel();
        $res = $Model->handHomework($request);
        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '提交成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res);

    }

    function updateHomeworkAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Scptc\HomeworkModel();
        $res = $Model->updateHomework($request);
        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '提交成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res);

    }

    function updateStatusAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Scptc\HomeworkModel();
        $res = $Model->updateStatusAction($request);
        $result = array();
        if ($res) {
            $result["list"] = $res;
            $res = array('error' => 0, 'errortip' => '提交成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => $result);
        }
        ajax_return($res);

    }

}