<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/13
 * Time: 23:25
 */

namespace Work\Controller\Scptcapi;


use Model\Scptc\MessageModel;

class MessageController extends viewTpl
{
    public $u;
    public $t;
    public $c;
    public $visitType = "api";
    public $DataControl;
    public $backData;

    //预加载处理类
    function __construct($visitType = "api")
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();

        //数据库操作
        $this->DataControl = new \Dbmysql();

    }

    //班级通知列表
    function ClassMessageView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Scptc\MessageModel();

        $result = $Model->ClassMessage($request);
        ajax_return($result);
    }

    //查看阅读情况
    function ReadSituationView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Scptc\MessageModel();

        $result = $Model->ReadSituation($request);
        ajax_return($result);
    }

    //选择接收人班级
    function ChoiceSelecterClassView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Scptc\MessageModel();

        $result = $Model->ChoiceSelecterClass($request);
        ajax_return($result);
    }

    //选择接收人学员
    function ChoiceSelecterStudentView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Scptc\MessageModel();

        $result = $Model->ChoiceSelecterStudent($request);
        ajax_return($result);
    }

    //发布通知
    function SendMessageAction()
    {
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);///验证账户

        $Model = new \Model\Scptc\MessageModel();

        $result = $Model->SendMessageAction($request);
        ajax_return($result);
    }

    //查看接收人
    function GetSelectorView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Scptc\MessageModel();

        $result = $Model->GetSelector($request);
        ajax_return($result);
    }

    //修改密码
    function updatePassAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Scptc\MessageModel();

        $result = $Model->updatePassAction($request);
        ajax_return($result);
    }

    //修改头像
    function updateImgAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Scptc\MessageModel();

        $result = $Model->updateImgAction($request);
        ajax_return($result);
    }

    //班级通知详情
    function MessageDetailView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Scptc\MessageModel();

        $result = $Model->MessageDetail($request);
        ajax_return($result);
    }

    //根据student_id查学员名字
    function StudentCnnameView(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Scptc\MessageModel();

        $result = $Model->StudentCnname($request);
        ajax_return($result);
    }

    //个人通知列表
    function PersonnalMessageView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Scptc\MessageModel();

        $result = $Model->PersonnalMessage($request);
        ajax_return($result);
    }

    //学校公告列表
    function SchoolMessageView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Scptc\MessageModel();

        $result = $Model->SchoolMessage($request);
        ajax_return($result);
    }

    //阅读通知
    function ReadMessageAction(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Scptc\MessageModel();

        $result = $Model->ReadMessageAction($request);
        ajax_return($result);
    }

    function bindView(){
        $request = Input('post.','','trim,addslashes');
//        $this->ThisVerify($request);//验证账户

        $data = array();

        $token = $this->DataControl->getFieldOne("smc_parenter_wxchattoken","wxchattoken_id","parenter_id = '{$request['parenter_id']}' and company_id = '{$request['company_id']}'");
        if($token){
            ajax_return(array('error' => 0, 'errortip' => "已绑定过!"));
        }else{
            $numid = $this->DataControl->getFieldOne("gmc_company_wxchatnumber","wxchatnumber_id","company_id = '{$request['company_id']}' and wxchatnumber_class = '1'");

            $data['company_id'] = $request['company_id'];
            $data['parenter_id'] = $request['parenter_id'];
            $data['parenter_wxtoken'] = $request['wxtoken'];
            $data['parenter_wximg'] = $request['imghead'];
            if($numid){
                $data['wxchatnumber_id'] = $numid['wxchatnumber_id'];
            }else{
                $data['wxchatnumber_id'] = '2';
            }

            $this->DataControl->insertData("smc_parenter_wxchattoken", $data);
            ajax_return(array('error' => 0, 'errortip' => "绑定成功!"));
        }



    }


    function parenterWxView(){
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $numid = $this->DataControl->getFieldOne("gmc_company_wxchatnumber","wxchatnumber_id","company_id = '{$request['company_id']}' and wxchatnumber_class = '1'");
        if($numid){
            $parenter = $this->DataControl->getFieldOne("smc_parenter_wxchattoken","parenter_wximg,parenter_wxtoken","parenter_id = '{$request['parenter_id']}' and wxchatnumber_id = '{$numid['wxchatnumber_id']}'");
        }else{
            $parenter = $this->DataControl->getFieldOne("smc_parenter_wxchattoken","parenter_wximg,parenter_wxtoken","parenter_id = '{$request['parenter_id']}' and wxchatnumber_id = '2'");
        }
        if($parenter['parenter_wxtoken']){
            $res = array('error' => 0, 'errortip' => "获取成功", 'result' => $parenter);
        }else{
            $res = array('error' => 1, 'errortip' => "尚未绑定", 'result' => array());
        }
        ajax_return($res);
    }

    function unbindAction(){
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $numid = $this->DataControl->getFieldOne("gmc_company_wxchatnumber","wxchatnumber_id","company_id = '{$request['company_id']}' and wxchatnumber_class = '1'");
        if($numid){
            $this->DataControl->delData("smc_parenter_wxchattoken","parenter_id = '{$request['parenter_id']}' and company_id = '{$request['company_id']}' and wxchatnumber_id = '{$numid['wxchatnumber_id']}'");
        }else{
            $this->DataControl->delData("smc_parenter_wxchattoken","parenter_id = '{$request['parenter_id']}' and company_id = '{$request['company_id']}' and wxchatnumber_id = '2'");
        }
        ajax_return(array('error' => 0, 'errortip' => "解绑成功!"));
    }


//结尾魔术函数
    function __destruct()
    {

    }
}
