<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/13
 * Time: 23:25
 */

namespace Work\Controller\Scptcapi;


use Model\Scptc\MineModel;

class MineController extends viewTpl
{
    public $u;
    public $t;
    public $c;
    public $visitType = "api";
    public $DataControl;
    public $backData;

    //预加载处理类
    function __construct($visitType = "api")
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();

        //数据库操作
        $this->DataControl = new \Dbmysql();

    }


//    //本地权限校验入口
//    function ThisVerify($request)
//    {
//        if (!intval($request['parenter_id'])) {
//            $res = array('error' => '1', 'errortip' => "家长id不存在", 'result' => array());
//            ajax_return($res);
//        }
//        if (empty($request['token'])) {
//            $res = array('error' => '1', 'errortip' => "未传入用户Token信息", 'result' => array());
//            ajax_return($res);
//        }
//        $familyOne = $this->DataControl->selectOne("select family_id from smc_student_family WHERE parenter_id = '{$request['parenter_id']}' and student_id = '{$request['student_id']}' ");
//        if($familyOne) {
//            $paramArray = array();
//            $paramArray['parenter_id'] = $request['parenter_id'];
//            $paramArray['company_id'] = $request['company_id'];
//            $paramArray['token'] = $request['token'];
//            if (!$this->UserLimit($paramArray)) {
//                $result = array();
//                $result["list"] = array();
//                $result["tokeninc"] = "0";
//                $res = array('error' => '1', 'errortip' => "用户token失效", 'result' => $result);
//                ajax_return($res);
//            }
//        }else{
//            $res = array('error' => '1', 'errortip' => "学员家长信息并未绑定", 'result' => array());
//            ajax_return($res);
//        }
//    }

    //个人资料
    function PersonnalInfoView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Scptc\MineModel();

        $result = $Model->PersonnalInfo($request);
        ajax_return($result);
    }

    //修改出生日期
    function ChangeBirthdayAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Scptc\MineModel();

        $result = $Model->ChangeBirthdayAction($request);
        ajax_return($result);
    }

    //查看家庭联系信息
    function FamilyInfoView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Scptc\MineModel();

        $result = $Model->FamilyInfo($request);
        ajax_return($result);
    }

    //反馈学校列表
    function ComplainSchoolView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Scptc\MineModel();

        $result = $Model->ComplainSchool($request);
        ajax_return($result);
    }

    //反馈班级列表
    function ComplainClassView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Scptc\MineModel();

        $result = $Model->ComplainClass($request);
        ajax_return($result);
    }

    //反馈教师列表
    function ComplainTeacherView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Scptc\MineModel();

        $result = $Model->ComplainTeacher($request);
        ajax_return($result);
    }

    //发布投诉
    function SendComplainAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Scptc\MineModel();

        $result = $Model->SendComplainAction($request);
        ajax_return($result);
    }

    //切换孩子列表
    function ChangeStudentListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Scptc\MineModel();

        $result = $Model->ChangeStudentList($request);
        ajax_return($result);
    }

    //修改学员头像
    function ChangeImgAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Scptc\MineModel();

        $result = $Model->ChangeImgAction($request);
        ajax_return($result);
    }

//    //我的成长
//    function MyGrowUpView()
//    {
//        $request = Input('get.','','trim,addslashes');
//        $this->ThisVerify($request);//验证账户
//
//        $Model = new \Model\Scptc\MineModel();
//
//        $result = $Model->MyGrowUp($request);
//        ajax_return($result);
//    }

    //我的成长
    function MyGrowUpView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Scptc\MineModel();
        $datalist = $Model->MyGrowUp($request);

        $result = array();
        $result['allnum'] = $datalist['allnums'];
        if ($datalist['list']) {
            $result['list'] = $datalist['list'];
            $res = array('error' => 0, 'errortip' => '获取我的成长信息成功', 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => 1, 'errortip' => '暂无我的成长信息', 'result' => $result);
        }
        ajax_return($res);
    }

    //上课统计
    function ClassStatisticsView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Scptc\MineModel();

        $result = $Model->ClassStatistics($request);
        ajax_return($result);
    }

    //课消详情
    function ClassPayDetailView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Scptc\MineModel();

        $result = $Model->ClassPayDetail($request);
        ajax_return($result);
    }

    //投诉列表(学校)
    function ScComplainListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Scptc\MineModel();

        $datalist = $Model->ScComplainList($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldname"] = "投诉id";
        $field[$k]["fieldstring"] = "complain_id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "学校";
        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "投诉与建议";
        $field[$k]["fieldstring"] = "complain_content";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "学员中文名";
        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "学员英文名";
        $field[$k]["fieldstring"] = "student_enname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "学员编号";
        $field[$k]["fieldstring"] = "student_branch";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "主要联系电话";
        $field[$k]["fieldstring"] = "family_mobile";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "创建时间";
        $field[$k]["fieldstring"] = "complain_createtime";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result['all_num'] = $datalist['all_num'];
        if ($datalist['list']) {
            $result['list'] = $datalist['list'];
            $res = array('error' => 0, 'errortip' => '获取投诉列表成功', 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => 1, 'errortip' => '暂无投诉列表信息', 'result' => $result);
        }
        ajax_return($res);
    }

    //投诉列表(教师)
    function TeaComplainListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Scptc\MineModel();

        $datalist = $Model->TeaComplainList($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldname"] = "投诉id";
        $field[$k]["fieldstring"] = "complain_id";
        $field[$k]["show"] = 0;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldname"] = "教师姓名";
        $field[$k]["fieldstring"] = "staffer_cnnname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "职工编号";
        $field[$k]["fieldstring"] = "staffer_branch";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "投诉与建议";
        $field[$k]["fieldstring"] = "complain_content";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "学员中文名";
        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "学员英文名";
        $field[$k]["fieldstring"] = "student_enname";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "学员编号";
        $field[$k]["fieldstring"] = "student_branch";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "主要联系电话";
        $field[$k]["fieldstring"] = "family_mobile";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "创建时间";
        $field[$k]["fieldstring"] = "complain_createtime";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result['all_num'] = $datalist['all_num'];
        if ($datalist['list']) {
            $result['list'] = $datalist['list'];
            $res = array('error' => 0, 'errortip' => '获取投诉列表成功', 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => 1, 'errortip' => '暂无投诉列表信息', 'result' => $result);
        }
        ajax_return($res);
    }

    //投诉详情
    function ComplainDetailView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Scptc\MineModel();

        $result = $Model->ComplainDetail($request);
        ajax_return($result);
    }


    //通讯录
    function TimListView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Scptc\MineModel();

        $result = $Model->TimList($request);
        ajax_return($result);
    }


    //我的班级 --- 97--20200303
    function myClassListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Scptc\MineModel();

        $dataList = $Model->myClassList($request);

        $field = array();
        $field["class_id"] = "班级ID";
        $field["class_type"] = "班级类型：0父班1子班";
        $field["class_branch"] = "班级编号";
        $field["class_cnname"] = "班级中文名";
        $field["class_enname"] = "班级英文名";
        $field["class_stdate"] = "班级开始时间";
        $field["class_enddate"] = "班级结束时间";
        $field["class_status"] = "班级状态:0待开班1进行中-1已结束-2已删除";
        $field["class_fullnums"] = "满班人数";
        $field["course_branch"] = "课程编号";
        $field["course_cnname"] = "课程名称";
        $field["school_cnname"] = "校区名称";
        $field["readallnum"] = "实际上课人数";
        $field["planhournum"] = "计划课时";
        $field["uphournum"] = "已上课时";
        $field["teachername"] = "教师名称";

        $result = array();
        $result["field"] = $field;
        $result['all_num'] = $dataList['all_num'];
        if ($dataList['list']) {
            $result['list'] = $dataList['list'];
            $res = array('error' => 0, 'errortip' => '我的班级信息', 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => 1, 'errortip' => '暂无我的班级信息', 'result' => $result);
        }
        ajax_return($res);
    }

    //我的班级课程别 --- 97--20200303
    function myClassCourseListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Scptc\MineModel();

        $dataList = $Model->myClassCourseList($request);

        $field = array();
        $field["course_id"] = "课程别ID";
        $field["course_branch"] = "课程别编号";
        $field["course_cnname"] = "课程别名称";

        $result = array();
        $result["field"] = $field;
        $result['all_num'] = $dataList['all_num'];
        if ($dataList['list']) {
            $result['list'] = $dataList['list'];
            $res = array('error' => 0, 'errortip' => '我的班级课程别信息', 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => 1, 'errortip' => '暂无我的班级课程别信息', 'result' => $result);
        }
        ajax_return($res);
    }

    //
    function LineThreeStuUrlApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Scptc\AppraiseModel($request);
        $resUrl = $Model->LineThreeStuUrl($request);
        if ($resUrl) {
            $res = array('error' => 0, 'errortip' => '获取回放课程', 'result' => $resUrl);
            ajax_return($res);
        } else {
            $res = array('error' => 1, 'errortip' => $Model->errortip, 'result' => array());
            ajax_return($res);
        }
    }

    //回放课程
    function LineThreeRecordPlaybackApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);
        $Model = new \Model\Scptc\AppraiseModel($request);
        $result = $Model->LineThreeRecordPlayback($request);
        if($result){
            $res = array('error' => 0, 'errortip' => '获取回放课程', 'result' => $result);
        }else{
            $res = array('error' => 1, 'errortip' => '视频正在录制中～', 'result' => array());
        }
        ajax_return($res);
    }

    //我的班级 -- 课时  --- 97--20200303
    function myClassHourListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Scptc\MineModel();

        $dataList = $Model->myClassHourList($request);

        $field = array();
        $field["hour_id"] = "课时ID";
        $field["hour_way"] = "上课方式";
        $field["hour_ischecking"] = "是否上课";
        $field["hour_number"] = "网课编号";
        $field["classroom_id"] = "实体教室ID";
        $field["hour_day"] = "课时日期";
        $field["hour_starttime"] = "课时开始时间";
        $field["hour_endtime"] = "课时结束时间";
        $field["staffer_id"] = "教师ID";
        $field["staffer_cnname"] = "教师中文名";
        $field["classroom_cnname"] = "实体教室名称";
        $field["comment"] = "教师点评:固定";
        $field["evaluate"] = "评价教师:固定";

        $result = array();
        $result["field"] = $field;
        if ($dataList['list']) {
            $result['list'] = $dataList['list'];
            $result['all_num'] = $dataList['all_num'];
            $res = array('error' => 0, 'errortip' => '班级课时信息', 'result' => $result);
        } else {
            $result['list'] = array();
            $result['all_num'] = '0';

            $res = array('error' => 1, 'errortip' => '暂无班级课时信息', 'result' => $result);
        }
        ajax_return($res);
    }


    //我的班级 -- 教师  --- 97--20200303
    function myClassTeacherListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Scptc\MineModel();

        $dataList = $Model->myClassTeacherList($request);

        $field = array();
        $field["staffer_id"] = "教师ID";
        $field["staffer_cnname"] = "教师中文名";
        $field["staffer_enname"] = "教师英文名";
        $field["staffer_branch"] = "教师编号";
        $field["staffer_sex"] = "教师性别";
        $field["staffer_mobile"] = "教师手机号";
        $field["teachtype_name"] = "教师类型";

        $result = array();
        $result["field"] = $field;
        $result['all_num'] = $dataList['all_num'];
        if ($dataList['list']) {
            $result['list'] = $dataList['list'];
            $res = array('error' => 0, 'errortip' => '班级教师信息', 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => 1, 'errortip' => '暂无班级教师信息', 'result' => $result);
        }
        ajax_return($res);
    }

    function testView()
    {
        $a = strtotime('11:00');
        $b = strtotime('12:20');

        $c = ($b - $a) / 60;

        var_dump($c);
    }


//结尾魔术函数
    function __destruct()
    {

    }
}
