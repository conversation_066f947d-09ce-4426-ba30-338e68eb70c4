<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2016/12/13
 * Time: 21:21
 */
namespace Work\Controller\Schelp;


class viewTpl {
	public $DataControl;
	public $router;
	public $smarty;
	public $Static;
	public $intSession;
	public $istaffer;
	
	public function __construct(){
		global $router;
		global $smarty;
		global $viewControl;
		
		//数据库操作
		$this->DataControl = new \Dbmysql();
		//操作类型
		$this->router = $router;
		
		
		//模板引擎开启
		$this->smarty = new \Smarty();
		//Session引擎开启
		$this->intSession = new \Incsession();
		//数据库操作
		//操作类型
		$this->router = $router;
		
		$this->smarty->template_dir = BASEDIR.'/Work/View/Schelp/templ';
		$this->smarty->compile_dir = BASEDIR.'/Temp/Compiled/Schelp/';
		$this->smarty->config_dir = BASEDIR.'/Common/';
		$this->smarty->cache_dir = BASEDIR.'/Temp/Caches/';
		
		//指定定界符
		$this->smarty->left_delimiter="{";	//左定界符
		$this->smarty->right_delimiter="}";	//右定界符
		
		$this->smarty->compile_check = true;
		$this->smarty->debugging = true;
		
		$this->UserLogin = false;
		
		
		$viewControl = $this->DataControl;
		$smarty = $this->smarty;
		include(ROOT_PATH . "Core/Smarty/int.class.php");
		
		//静态资源加载
		$this->smarty->assign("CssUrl", "/Work/View/Schelp/css/", true);
		$this->smarty->assign("VideoUrl", "/Work/View/Schelp/video/", true);
		$this->smarty->assign("SrcUrl", "/Work/View/Schelp/src/", true);
		$this->smarty->assign("JsUrl", "/Work/View/Schelp/js/", true);
		$this->smarty->assign("ImgUrl", "/Work/View/Schelp/images/", true);
		$this->smarty->assign("PluginsUrl", "/Work/View/Schelp/plugins/", true);
		$this->smarty->assign("StaticUrl", IMG_PATH, true);
		$this->smarty->assign("versiontiem", date("Ymd"), true);
		
	}
	
	public function check_login(){
		
		if($this->intSession->getCookiearray('istaffer') && count($this->intSession->getCookiearray('istaffer')) > 0){
			$login_user = $this->intSession->getCookiearray('istaffer');
			$this->istaffer = $login_user;
			
			if(!empty($login_user) && $login_user){
				$istaffer = $this->DataControl->getOne("smc_staffer","staffer_id='{$login_user['staffer_id']}'");
				$istaffer = array_merge($istaffer,$login_user);
				
				if(!$istaffer){
					$this->intSession->setCookiearray("istaffer",array(),'1');
					return false;
				}else{
					$this->istaffer = $istaffer;
					return true;
				}
			}else{
				return false;
			}
		}else{
			return false;
		}
	}
	
	public function display($tempview=""){
		
		return $this->smarty->display($tempview);
	}
	
	
	public function __call($method, $args) {
		echo "unknown method " . $method;
		return false;
		
	}
	
	public function LoginView() {
		
		$this->display("login.htm");
		exit;
	}
}
