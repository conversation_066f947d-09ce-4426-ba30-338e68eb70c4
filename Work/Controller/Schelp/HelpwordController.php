<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2018/12/29
 * Time: 10:09
 */

namespace Work\Controller\Schelp;


class HelpwordController extends viewTpl
{
	public $u;
	public $t;
	public $c;
	public $istaffer;
	public $Viewhtm;
	
	function __construct()
	{
		parent::__construct();
//		if(!$this->check_login()){
//			$this->LoginView();
//		}
		
		$this->u = $this->router->getController();
		$this->t = $this->router->getUrl();
		$this->c = $this->router->getAction();
		$this->Viewhtm = $this->router->getController() . "/" . $this->router->getUrl() . ".htm";

//		$this->smarty->assign("istaffer", $this->istaffer);
	}
	
	
	function  HomeView(){
		$request= Input('get.','','trim,addslashes');
		$datawhere ="1";
		
		if(isset($request['keyword']) && $request['keyword'] !="" ){
		 	$datawhere .=" and (i.module_name like '%{$request['keyword']}%' or h.handbook_name like '%{$request['keyword']}%')";
		 }
		 $sql = "select i.module_id,i.module_name,father_id,module_class,i.module_level,h.handbook_name,h.handbook_note from  imc_module as i,imc_module_handbook as h where  module_class = '1' and  i.module_id = h.module_id and {$datawhere} Order by module_class ASC,module_id DESC    ";
	
		$com_moduelList = $this->DataControl->selectClear($sql);
		
		$comData = array();
		if($com_moduelList){
			$arr_moudle_id = array_column($com_moduelList,"module_id");
			$data[0]['module_name'] = "集团管理系统";
			 foreach($com_moduelList as $key => $value){
				 if(!$value['father_id']){
					 $data[$value['module_id']]['module_id'] = $value['module_id'];
					 $data[$value['module_id']]['module_name'] = $value['module_name'];
					 $data[$value['module_id']]['father_id'] = $value['father_id'];
					 $data[$value['module_id']]['module_level'] = $value['module_level'];
					 $data[$value['module_id']]['module_class'] = $value['module_class'];
					 $data[$value['module_id']]['child_array'][]=$value;
//
				 }else{
				 	
				 	if( !in_array($value['father_id'],$arr_moudle_id)){
				 		$moudleOne = $this->DataControl->getFieldOne('imc_module',"module_id,module_name,father_id,module_class,module_level","module_id='{$value['father_id']}'");
						$data[$moudleOne['module_id']]['module_id'] = $moudleOne['module_id'];
						$data[$moudleOne['module_id']]['module_name'] = $moudleOne['module_name'];
						$data[$moudleOne['module_id']]['father_id'] = $moudleOne['father_id'];
						$data[$moudleOne['module_id']]['module_level'] = $moudleOne['module_level'];
						$data[$moudleOne['module_id']]['module_class'] = $moudleOne['module_class'];
					}
					 $data[$value['father_id']]['child_array'][]= $value;
					 unset($value);
				 }
			 }
			$data = array_values($data);
			$comData['1'] = $data;
		}
		
	
		$data = array();
		
		$sql = "select i.module_id,i.module_name,father_id,module_class,i.module_level,h.handbook_name,h.handbook_note from  imc_module as i,imc_module_handbook as h where  module_class = '2' and  i.module_id = h.module_id  and {$datawhere} Order by module_weight DESC    ";
		
		$com_moduelList = $this->DataControl->selectClear($sql);
		if($com_moduelList){
			$arr_moudle_id = array_column($com_moduelList,"module_id");
			$data[0]['module_name'] = "校务管理系统";
			foreach($com_moduelList as $key => $value){
				if(!$value['father_id']){
					$data[$value['module_id']]['module_id'] = $value['module_id'];
					$data[$value['module_id']]['module_name'] = $value['module_name'];
					$data[$value['module_id']]['father_id'] = $value['father_id'];
					$data[$value['module_id']]['module_level'] = $value['module_level'];
					$data[$value['module_id']]['module_class'] = $value['module_class'];
					$data[$value['module_id']]['child_array'][]=$value;
				}else{
					
					if( !in_array($value['father_id'],$arr_moudle_id)){
						$moudleOne = $this->DataControl->getFieldOne('imc_module',"module_id,module_name,father_id,module_class,module_level","module_id='{$value['father_id']}'");
						$data[$moudleOne['module_id']]['module_id'] = $moudleOne['module_id'];
						$data[$moudleOne['module_id']]['module_name'] = $moudleOne['module_name'];
						$data[$moudleOne['module_id']]['father_id'] = $moudleOne['father_id'];
						$data[$moudleOne['module_id']]['module_level'] = $moudleOne['module_level'];
						$data[$moudleOne['module_id']]['module_class'] = $moudleOne['module_class'];
					}
					$data[$value['father_id']]['child_array'][]= $value;
					unset($value);
				}
			}
			$data = array_values($data);
			$comData['2'] = $data;
		}
		
		
	
		$data = array();
		$sql = "select i.module_id,i.module_name,father_id,module_class,i.module_level,h.handbook_name,h.handbook_note from  imc_module as i,imc_module_handbook as h where  module_class = '3' and  i.module_id = h.module_id and {$datawhere}  Order by module_weight DESC    ";
		
		
		$com_moduelList = $this->DataControl->selectClear($sql);
		
		if($com_moduelList){
			$arr_moudle_id = array_column($com_moduelList,"module_id");
			$data[0]['module_name']= "CRM管理系统";
			foreach($com_moduelList as $key => $value){
				if(!$value['father_id']){
					$data[$value['module_id']]['module_id'] = $value['module_id'];
					$data[$value['module_id']]['module_name'] = $value['module_name'];
					$data[$value['module_id']]['father_id'] = $value['father_id'];
					$data[$value['module_id']]['module_level'] = $value['module_level'];
					$data[$value['module_id']]['module_class'] = $value['module_class'];
					$data[$value['module_id']]['child_array'][]=$value;
				}else{
					
					if( !in_array($value['father_id'],$arr_moudle_id)){
						$moudleOne = $this->DataControl->getFieldOne('imc_module',"module_id,module_name,father_id,module_class,module_level","module_id='{$value['father_id']}'");
						$data[$moudleOne['module_id']]['module_id'] = $moudleOne['module_id'];
						$data[$moudleOne['module_id']]['module_name'] = $moudleOne['module_name'];
						$data[$moudleOne['module_id']]['father_id'] = $moudleOne['father_id'];
						$data[$moudleOne['module_id']]['module_level'] = $moudleOne['module_level'];
						$data[$moudleOne['module_id']]['module_class'] = $moudleOne['module_class'];
					}
					$data[$value['father_id']]['child_array'][]= $value;
					unset($value);
				}
			}
			$data = array_values($data);
			$comData['3'] = $data;
		}
		
		$data = array();
		$sql = "select i.module_id,i.module_name,father_id,module_class,i.module_level,h.handbook_name,h.handbook_note from  imc_module as i,imc_module_handbook as h where  module_class = '4' and  i.module_id = h.module_id and {$datawhere} ORDER BY module_weight DESC    ";
		
		$com_moduelList = $this->DataControl->selectClear($sql);
		if($com_moduelList){
			$arr_moudle_id = array_column($com_moduelList,"module_id");
			$data[0]['module_name'] = "家校互动";
			foreach($com_moduelList as $key => $value){
				if(!$value['father_id']){
					$data[$value['module_id']]['module_id'] = $value['module_id'];
					$data[$value['module_id']]['module_name'] = $value['module_name'];
					$data[$value['module_id']]['father_id'] = $value['father_id'];
					$data[$value['module_id']]['module_level'] = $value['module_level'];
					$data[$value['module_id']]['module_class'] = $value['module_class'];
					$data[$value['module_id']]['child_array'][]=$value;
				}else{
					if( !in_array($value['father_id'],$arr_moudle_id)){
						$moudleOne = $this->DataControl->getFieldOne('imc_module',"module_id,module_name,father_id,module_class,module_level","module_id='{$value['father_id']}'");
						$data[$moudleOne['module_id']]['module_id'] = $moudleOne['module_id'];
						$data[$moudleOne['module_id']]['module_name'] = $moudleOne['module_name'];
						$data[$moudleOne['module_id']]['father_id'] = $moudleOne['father_id'];
						$data[$moudleOne['module_id']]['module_level'] = $moudleOne['module_level'];
						$data[$moudleOne['module_id']]['module_class'] = $moudleOne['module_class'];
					}
					$data[$value['father_id']]['child_array'][]= $value;
					unset($value);
				}
			}
			$data = array_values($data);
			$comData['4'] = $data;
		}
		
//		debug($comData);
		$this->smarty->assign("SchmoList",$comData);
		$datatype = array();
		$datatype['keyword'] = $request['keyword'];
		$this->smarty->assign("datatype",$datatype);
		
	}
	
	function HelpvideoView()
	{
		$request= Input('get.','','trim,addslashes');
		$datawhere ="1";
		
		if(isset($request['keyword']) && $request['keyword'] !="" ){
			$datawhere .=" and (i.module_name like '%{$request['keyword']}%' or h.handbook_name like '%{$request['keyword']}%')";
		}
		$sql = "select i.module_id,i.module_name,father_id,module_class,i.module_level,h.handbook_name,h.handbook_note,handbook_videourl from  imc_module as i,imc_module_handbook as h where  module_class = '1' and  i.module_id = h.module_id and h.handbook_videourl <>'' and {$datawhere} Order by module_class ASC,module_id ASC    ";
		
		$com_moduelList = $this->DataControl->selectClear($sql);
		$comData = array();
		if($com_moduelList){
			$arr_moudle_id = array_column($com_moduelList,"module_id");
			$data[0]['module_name'] = "集团管理系统";
			foreach($com_moduelList as $key => $value){
				if(!$value['father_id']){
					$data[$value['module_id']]['module_id'] = $value['module_id'];
					$data[$value['module_id']]['module_name'] = $value['module_name'];
					$data[$value['module_id']]['father_id'] = $value['father_id'];
					$data[$value['module_id']]['module_level'] = $value['module_level'];
					$data[$value['module_id']]['module_class'] = $value['module_class'];
//
					$data[$value['module_id']]['child_array'][]=$value;
//
				}else{
					
					if( !in_array($value['father_id'],$arr_moudle_id)){
						$moudleOne = $this->DataControl->getFieldOne('imc_module',"module_id,module_name,father_id,module_class,module_level","module_id='{$value['father_id']}'");
						$data[$moudleOne['module_id']]['module_id'] = $moudleOne['module_id'];
						$data[$moudleOne['module_id']]['module_name'] = $moudleOne['module_name'];
						$data[$moudleOne['module_id']]['father_id'] = $moudleOne['father_id'];
						$data[$moudleOne['module_id']]['module_level'] = $moudleOne['module_level'];
						$data[$moudleOne['module_id']]['module_class'] = $moudleOne['module_class'];
						$data[$moudleOne['module_id']]['handbook_videourl'] = $moudleOne['handbook_videourl'];
					}
					$data[$value['father_id']]['child_array'][]= $value;
					unset($value);
				}
			}
			$data = array_values($data);
			$comData['1'] = $data;
		}
		
		$data = array();
		
		$sql = "select i.module_id,i.module_name,father_id,module_class,i.module_level,h.handbook_name,h.handbook_note,h.handbook_videourl from  imc_module as i,imc_module_handbook as h where  module_class = '2' and  i.module_id = h.module_id and h.handbook_videourl <>''  and {$datawhere} Order by module_class ASC,module_id ASC    ";
		
		$com_moduelList = $this->DataControl->selectClear($sql);
		if($com_moduelList){
			$arr_moudle_id = array_column($com_moduelList,"module_id");
			$data[0]['module_name'] = "校务管理系统";
			foreach($com_moduelList as $key => $value){
				if(!$value['father_id']){
					$data[$value['module_id']]['module_id'] = $value['module_id'];
					$data[$value['module_id']]['module_name'] = $value['module_name'];
					$data[$value['module_id']]['father_id'] = $value['father_id'];
					$data[$value['module_id']]['module_level'] = $value['module_level'];
					$data[$value['module_id']]['module_class'] = $value['module_class'];
//
					$data[$value['module_id']]['child_array'][]=$value;
				}else{
					
					if( !in_array($value['father_id'],$arr_moudle_id)){
						$moudleOne = $this->DataControl->getFieldOne('imc_module',"module_id,module_name,father_id,module_class,module_level","module_id='{$value['father_id']}'");
						$data[$moudleOne['module_id']]['module_id'] = $moudleOne['module_id'];
						$data[$moudleOne['module_id']]['module_name'] = $moudleOne['module_name'];
						$data[$moudleOne['module_id']]['father_id'] = $moudleOne['father_id'];
						$data[$moudleOne['module_id']]['module_level'] = $moudleOne['module_level'];
						$data[$moudleOne['module_id']]['module_class'] = $moudleOne['module_class'];
						$data[$moudleOne['module_id']]['handbook_videourl'] = $moudleOne['handbook_videourl'];
					}
					$data[$value['father_id']]['child_array'][]= $value;
					unset($value);
				}
			}
			$data = array_values($data);
			$comData['2'] = $data;
		}
		
		$data = array();
		$sql = "select i.module_id,i.module_name,father_id,module_class,i.module_level,h.handbook_name,h.handbook_note,h.handbook_videourl from  imc_module as i,imc_module_handbook as h where  module_class = '3' and  i.module_id = h.module_id and h.handbook_videourl <>'' and {$datawhere}  Order by module_class ASC,module_id ASC    ";
		
		
		$com_moduelList = $this->DataControl->selectClear($sql);
		if($com_moduelList){
			$arr_moudle_id = array_column($com_moduelList,"module_id");
			$data[0]['module_name']= "CRM管理系统";
			foreach($com_moduelList as $key => $value){
				if(!$value['father_id']){
					$data[$value['module_id']]['module_id'] = $value['module_id'];
					$data[$value['module_id']]['module_name'] = $value['module_name'];
					$data[$value['module_id']]['father_id'] = $value['father_id'];
					$data[$value['module_id']]['module_level'] = $value['module_level'];
					$data[$value['module_id']]['module_class'] = $value['module_class'];
//
					$data[$value['module_id']]['child_array'][]=$value;
				}else{
					
					if( !in_array($value['father_id'],$arr_moudle_id)){
						$moudleOne = $this->DataControl->getFieldOne('imc_module',"module_id,module_name,father_id,module_class,module_level","module_id='{$value['father_id']}'");
						$data[$moudleOne['module_id']]['module_id'] = $moudleOne['module_id'];
						$data[$moudleOne['module_id']]['module_name'] = $moudleOne['module_name'];
						$data[$moudleOne['module_id']]['father_id'] = $moudleOne['father_id'];
						$data[$moudleOne['module_id']]['module_level'] = $moudleOne['module_level'];
						$data[$moudleOne['module_id']]['module_class'] = $moudleOne['module_class'];
						$data[$moudleOne['module_id']]['handbook_videourl'] = $moudleOne['handbook_videourl'];
					}
					$data[$value['father_id']]['child_array'][]= $value;
					unset($value);
				}
			}
			$data = array_values($data);
			$comData['3'] = $data;
		}
		$data = array();
		$sql = "select i.module_id,i.module_name,father_id,module_class,i.module_level,h.handbook_name,h.handbook_note,h.handbook_videourl from  imc_module as i,imc_module_handbook as h where  module_class = '4' and  i.module_id = h.module_id and h.handbook_videourl <> '' and {$datawhere} Order by module_class ASC,module_id ASC    ";
		
		
		$com_moduelList = $this->DataControl->selectClear($sql);
		if($com_moduelList){
			$arr_moudle_id = array_column($com_moduelList,"module_id");
			$data[0]['module_name'] = "家校互动";
			foreach($com_moduelList as $key => $value){
				if(!$value['father_id']){
					$data[$value['module_id']]['module_id'] = $value['module_id'];
					$data[$value['module_id']]['module_name'] = $value['module_name'];
					$data[$value['module_id']]['father_id'] = $value['father_id'];
					$data[$value['module_id']]['module_level'] = $value['module_level'];
					$data[$value['module_id']]['module_class'] = $value['module_class'];
//
					$data[$value['module_id']]['child_array'][]=$value;
				}else{
					if( !in_array($value['father_id'],$arr_moudle_id)){
						$moudleOne = $this->DataControl->getFieldOne('imc_module',"module_id,module_name,father_id,module_class,module_level","module_id='{$value['father_id']}'");
						$data[$moudleOne['module_id']]['module_id'] = $moudleOne['module_id'];
						$data[$moudleOne['module_id']]['module_name'] = $moudleOne['module_name'];
						$data[$moudleOne['module_id']]['father_id'] = $moudleOne['father_id'];
						$data[$moudleOne['module_id']]['module_level'] = $moudleOne['module_level'];
						$data[$moudleOne['module_id']]['module_class'] = $moudleOne['module_class'];
						$data[$moudleOne['module_id']]['handbook_videourl'] = $moudleOne['handbook_videourl'];
					}
					$data[$value['father_id']]['child_array'][]= $value;
					unset($value);
				}
			}
			$data = array_values($data);
			$comData['4'] = $data;
		}

//		debug($comData);
		$this->smarty->assign("SchmoList",$comData);
		 $datatype['keyword'] = $request['keyword'];
		$this->smarty->assign("datatype",$datatype);
	}
	
	function HelpproblemView()
	{
		
		$request= Input('get.','','trim,addslashes');
		$datawhere ="1";
		
		if(isset($request['keyword']) && $request['keyword'] !="" ){
			$datawhere .=" and (i.module_name like '%{$request['keyword']}%' or f.faq_name like '%{$request['keyword']}%')";
		}
		$comData = array();
		$sql = "select i.module_id,i.module_name,father_id,module_class,i.module_level,f.faq_name,f.faq_note from  imc_module as i ,imc_module_faq as f where i.module_id =f.module_id and module_class = '1'  and {$datawhere} order by module_class ASC, faq_weight DESC  ";
		$com_moduelList = $this->DataControl->selectClear($sql);
		if($com_moduelList){
			$arr_moudle_id = array_column($com_moduelList,"module_id");
			$data[0]['module_name'] = "集团管理系统";
			foreach($com_moduelList as $key => $value){
				if(!$value['father_id']){
					$data[$value['module_id']]['module_id'] = $value['module_id'];
					$data[$value['module_id']]['module_name'] = $value['module_name'];
					$data[$value['module_id']]['father_id'] = $value['father_id'];
					$data[$value['module_id']]['module_level'] = $value['module_level'];
					$data[$value['module_id']]['module_class'] = $value['module_class'];
					
					$data[$value['module_id']]['child_array'][]=$value;
//
				}else{
					
					if( !in_array($value['father_id'],$arr_moudle_id)){
						$moudleOne = $this->DataControl->getFieldOne('imc_module',"module_id,module_name,father_id,module_class,module_level","module_id='{$value['father_id']}'");
						$data[$moudleOne['module_id']]['module_id'] = $moudleOne['module_id'];
						$data[$moudleOne['module_id']]['module_name'] = $moudleOne['module_name'];
						$data[$moudleOne['module_id']]['father_id'] = $moudleOne['father_id'];
						$data[$moudleOne['module_id']]['module_level'] = $moudleOne['module_level'];
						$data[$moudleOne['module_id']]['module_class'] = $moudleOne['module_class'];
						
					}
					$data[$value['father_id']]['child_array'][]= $value;
					unset($value);
				}
			}
			$data = array_values($data);
			$comData['1'] = $data;
		}
		$data = array();
		$sql = "select i.module_id,i.module_name,father_id,module_class,i.module_level,f.faq_name,f.faq_note from  imc_module as i ,imc_module_faq as f where i.module_id =f.module_id and module_class = '2' and {$datawhere}  order by module_class ASC, module_id ASC,faq_weight DESC  ";
		
		$com_moduelList = $this->DataControl->selectClear($sql);
		if($com_moduelList){
			$arr_moudle_id = array_column($com_moduelList,"module_id");
			$data[0]['module_name'] = "校务管理系统";
			foreach($com_moduelList as $key => $value){
				if(!$value['father_id']){
					$data[$value['module_id']]['module_id'] = $value['module_id'];
					$data[$value['module_id']]['module_name'] = $value['module_name'];
					$data[$value['module_id']]['father_id'] = $value['father_id'];
					$data[$value['module_id']]['module_level'] = $value['module_level'];
					$data[$value['module_id']]['module_class'] = $value['module_class'];
					$data[$value['module_id']]['child_array'][]=$value;
				}else{
					
					if( !in_array($value['father_id'],$arr_moudle_id)){
						$moudleOne = $this->DataControl->getFieldOne('imc_module',"module_id,module_name,father_id,module_class,module_level","module_id='{$value['father_id']}'");
						$data[$moudleOne['module_id']]['module_id'] = $moudleOne['module_id'];
						$data[$moudleOne['module_id']]['module_name'] = $moudleOne['module_name'];
						$data[$moudleOne['module_id']]['father_id'] = $moudleOne['father_id'];
						$data[$moudleOne['module_id']]['module_level'] = $moudleOne['module_level'];
						$data[$moudleOne['module_id']]['module_class'] = $moudleOne['module_class'];
					}
					
					$data[$value['father_id']]['child_array'][]= $value;
					unset($value);
				}
			}
			$data = array_values($data);
			$comData['2'] = $data;
		}
		
		$data = array();
		$sql = "select i.module_id,i.module_name,father_id,module_class,i.module_level,f.faq_name,f.faq_note from  imc_module as i ,imc_module_faq as f where i.module_id =f.module_id and module_class = '3' and {$datawhere}  order by module_class ASC, faq_weight DESC  ";
		$com_moduelList = $this->DataControl->selectClear($sql);
		if($com_moduelList){
			$arr_moudle_id = array_column($com_moduelList,"module_id");
			$data[0]['module_name'] = "CRM管理系统";
			foreach($com_moduelList as $key => $value){
				if(!$value['father_id']){
					$data[$value['module_id']]['module_id'] = $value['module_id'];
					$data[$value['module_id']]['module_name'] = $value['module_name'];
					$data[$value['module_id']]['father_id'] = $value['father_id'];
					$data[$value['module_id']]['module_level'] = $value['module_level'];
					$data[$value['module_id']]['module_class'] = $value['module_class'];
					
					$data[$value['module_id']]['child_array'][]=$value;

				}else{
					
					if( !in_array($value['father_id'],$arr_moudle_id)){
						$moudleOne = $this->DataControl->getFieldOne('imc_module',"module_id,module_name,father_id,module_class,module_level","module_id='{$value['father_id']}'");
						$data[$moudleOne['module_id']]['module_id'] = $moudleOne['module_id'];
						$data[$moudleOne['module_id']]['module_name'] = $moudleOne['module_name'];
						$data[$moudleOne['module_id']]['father_id'] = $moudleOne['father_id'];
						$data[$moudleOne['module_id']]['module_level'] = $moudleOne['module_level'];
						$data[$moudleOne['module_id']]['module_class'] = $moudleOne['module_class'];
						
					}
					$data[$value['father_id']]['child_array'][]= $value;
					unset($value);
				}
			}
			$data = array_values($data);
			$comData['3'] = $data;
		}
		$data = array();
		$sql = "select i.module_id,i.module_name,father_id,module_class,i.module_level,f.faq_name,f.faq_note from  imc_module as i ,imc_module_faq as f where i.module_id =f.module_id and module_class = '4' and {$datawhere}  order by module_class ASC, faq_weight DESC  ";
		$com_moduelList = $this->DataControl->selectClear($sql);
		if($com_moduelList){
			$arr_moudle_id = array_column($com_moduelList,"module_id");
			$data[0]['module_name'] = "家校互动";
			foreach($com_moduelList as $key => $value){
				if(!$value['father_id']){
					$data[$value['module_id']]['module_id'] = $value['module_id'];
					$data[$value['module_id']]['module_name'] = $value['module_name'];
					$data[$value['module_id']]['father_id'] = $value['father_id'];
					$data[$value['module_id']]['module_level'] = $value['module_level'];
					$data[$value['module_id']]['module_class'] = $value['module_class'];
					
					$data[$value['module_id']]['child_array'][]=$value;
//					 $data[$value['module_id']]['child_array'][]=$value['handbook_note'];
				
				}else{
					
					if( !in_array($value['father_id'],$arr_moudle_id)){
						$moudleOne = $this->DataControl->getFieldOne('imc_module',"module_id,module_name,father_id,module_class,module_level","module_id='{$value['father_id']}'");
						$data[$moudleOne['module_id']]['module_id'] = $moudleOne['module_id'];
						$data[$moudleOne['module_id']]['module_name'] = $moudleOne['module_name'];
						$data[$moudleOne['module_id']]['father_id'] = $moudleOne['father_id'];
						$data[$moudleOne['module_id']]['module_level'] = $moudleOne['module_level'];
						$data[$moudleOne['module_id']]['module_class'] = $moudleOne['module_class'];
					}
					$data[$value['father_id']]['child_array'][]= $value;
					unset($value);
				}
			}
			$data = array_values($data);
			$comData['4'] = $data;
		}
		
		$this->smarty->assign("SchmoList",$comData);
		$datatype  = array();
		$datatype["keyword"]  = $request['keyword'];
		$this->smarty->assign("datatype",$datatype);
	}
	function HelppersonView()
	{
		$datatype  = array();
		$this->smarty->assign("datatype",$datatype);
	}
	
	
	
	
	//魔术方法
	public function __call($name, $arguments) {
		$this->Viewhtm = "under.htm";
		// Note: value of $name is case sensitive.
		//echo "Calling object method '$name' ". implode(', ', $arguments). "\n";
	}
	//魔术方法
	function __destruct()
	{
		if ($this->c =='Wait') {
			$this->smarty->assign("u", $this->u);
			$this->smarty->assign("t", $this->t);
			$this->smarty->assign("Viewhtm", $this->Viewhtm);
			$this->display($this->Viewhtm);
//            $this->display("Home.html");
			exit;
		}
	}
}