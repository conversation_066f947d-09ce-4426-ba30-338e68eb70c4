<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2018/12/29
 * Time: 10:09
 */

namespace Work\Controller\Schelp;


class HelpwController extends viewTpl
{
    public $u;
    public $t;
    public $c;
    public $istaffer;
    public $Viewhtm;

    function __construct()
    {
        parent::__construct();

        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
        $this->Viewhtm = $this->router->getController() . "/" . $this->router->getUrl() . ".htm";
    }

    //菜单 -- 培训手册
    function menuList(){
        $comData = array();

        $sql = "select i.module_id,i.module_name,i.father_id,i.module_class,i.module_level 
                from  imc_module as i
                where  i.module_class = '1'  and i.module_level = '1' 
                Order by i.module_weight ASC,i.module_id DESC";
        $comDataOne = $this->DataControl->selectClear($sql);

        $sql = "select i.module_id,i.module_name,i.father_id,i.module_class,i.module_level 
                from  imc_module as i
                where  i.module_class = '2'   and i.module_level = '1' 
                Order by i.module_weight ASC,i.module_id DESC";
        $comDataTwo = $this->DataControl->selectClear($sql);

        $sql = "select i.module_id,i.module_name,i.father_id,i.module_class,i.module_level 
                from  imc_module as i
                where  i.module_class = '3'   and i.module_level = '1' 
                Order by i.module_weight ASC,i.module_id DESC";
        $comDataThree = $this->DataControl->selectClear($sql);

        $sql = "select i.module_id,i.module_name,i.father_id,i.module_class,i.module_level 
                from  imc_module as i
                where  i.module_class = '4'   and i.module_level = '1' 
                Order by i.module_weight ASC,i.module_id DESC";
        $comDataFour = $this->DataControl->selectClear($sql);

        $comData[0]['name'] = "集团管理系统";
        $comData[0]['list'] = $comDataOne;

        $comData[1]['name'] = "校务管理系统";
        $comData[1]['list'] = $comDataTwo;

        $comData[2]['name'] = "CRM管理系统";
        $comData[2]['list'] = $comDataThree;

        $comData[3]['name'] = "家校互动";
        $comData[3]['list'] = $comDataFour;

        return $comData;
    }
    //菜单 -- 培训视频
    function menuVideoList(){
        $comData = array();

        $sql = "select i.module_id,i.module_name,i.father_id,i.module_class,i.module_level 
                from  imc_module as i 
                LEFT JOIN imc_module as m ON m.father_id = i.module_id
                LEFT JOIN imc_module_handbook as h ON (m.module_id = h.module_id or i.module_id = h.module_id) 
                where  i.module_class = '1'  and i.module_level = '1'  and h.handbook_videourl <>'' 
                GROUP by i.module_id
                Order by i.module_weight ASC,i.module_id DESC";
        $comDataOne = $this->DataControl->selectClear($sql);

        $sql = "select i.module_id,i.module_name,i.father_id,i.module_class,i.module_level 
                from  imc_module as i
                LEFT JOIN imc_module as m ON m.father_id = i.module_id
                LEFT JOIN imc_module_handbook as h ON (m.module_id = h.module_id or i.module_id = h.module_id) 
                where  i.module_class = '2'   and i.module_level = '1'  and h.handbook_videourl <>'' 
                GROUP by i.module_id
                Order by i.module_weight ASC,i.module_id DESC";
        $comDataTwo = $this->DataControl->selectClear($sql);

        $sql = "select i.module_id,i.module_name,i.father_id,i.module_class,i.module_level 
                from  imc_module as i 
                LEFT JOIN imc_module as m ON m.father_id = i.module_id
                LEFT JOIN imc_module_handbook as h ON (m.module_id = h.module_id or i.module_id = h.module_id) 
                where  i.module_class = '3'   and i.module_level = '1'  and h.handbook_videourl <>'' 
                GROUP by i.module_id
                Order by i.module_weight ASC,i.module_id DESC";
        $comDataThree = $this->DataControl->selectClear($sql);

        $sql = "select i.module_id,i.module_name,i.father_id,i.module_class,i.module_level 
                from  imc_module as i
                LEFT JOIN imc_module as m ON m.father_id = i.module_id
                LEFT JOIN imc_module_handbook as h ON (m.module_id = h.module_id or i.module_id = h.module_id) 
                where  i.module_class = '4'   and i.module_level = '1'  and h.handbook_videourl <>'' 
                GROUP by i.module_id
                Order by i.module_weight ASC,i.module_id DESC";
        $comDataFour = $this->DataControl->selectClear($sql);

        $comData[0]['name'] = "集团管理系统";
        $comData[0]['list'] = $comDataOne;

        $comData[1]['name'] = "校务管理系统";
        $comData[1]['list'] = $comDataTwo;

        $comData[2]['name'] = "CRM管理系统";
        $comData[2]['list'] = $comDataThree;

        $comData[3]['name'] = "家校互动";
        $comData[3]['list'] = $comDataFour;

        return $comData;
    }
    //菜单 -- 培训手册
    function menuHelpList(){
        $comData = array();

        $sql = "select i.module_id,i.module_name,i.father_id,i.module_class,i.module_level 
                from  imc_module as i 
                LEFT JOIN imc_module as m ON m.father_id = i.module_id
                LEFT JOIN imc_module_faq as h ON (m.module_id = h.module_id or i.module_id = h.module_id) 
                where  i.module_class = '1'  and i.module_level = '1'  and h.faq_name <>'' 
                GROUP by i.module_id
                Order by i.module_weight ASC,i.module_id DESC";
        $comDataOne = $this->DataControl->selectClear($sql);

        $sql = "select i.module_id,i.module_name,i.father_id,i.module_class,i.module_level 
                from  imc_module as i
                LEFT JOIN imc_module as m ON m.father_id = i.module_id
                LEFT JOIN imc_module_faq as h ON (m.module_id = h.module_id or i.module_id = h.module_id) 
                where  i.module_class = '2'   and i.module_level = '1'  and h.faq_name <>'' 
                GROUP by i.module_id
                Order by i.module_weight ASC,i.module_id DESC";
        $comDataTwo = $this->DataControl->selectClear($sql);

        $sql = "select i.module_id,i.module_name,i.father_id,i.module_class,i.module_level 
                from  imc_module as i 
                LEFT JOIN imc_module as m ON m.father_id = i.module_id
                LEFT JOIN imc_module_faq as h ON (m.module_id = h.module_id or i.module_id = h.module_id) 
                where  i.module_class = '3'   and i.module_level = '1'  and h.faq_name <>'' 
                GROUP by i.module_id
                Order by i.module_weight ASC,i.module_id DESC";
        $comDataThree = $this->DataControl->selectClear($sql);

        $sql = "select i.module_id,i.module_name,i.father_id,i.module_class,i.module_level 
                from  imc_module as i
                LEFT JOIN imc_module as m ON m.father_id = i.module_id
                LEFT JOIN imc_module_faq as h ON (m.module_id = h.module_id or i.module_id = h.module_id) 
                where  i.module_class = '4'   and i.module_level = '1'  and h.faq_name <>'' 
                GROUP by i.module_id
                Order by i.module_weight ASC,i.module_id DESC";
        $comDataFour = $this->DataControl->selectClear($sql);

        $comData[0]['name'] = "集团管理系统";
        $comData[0]['list'] = $comDataOne;

        $comData[1]['name'] = "校务管理系统";
        $comData[1]['list'] = $comDataTwo;

        $comData[2]['name'] = "CRM管理系统";
        $comData[2]['list'] = $comDataThree;

        $comData[3]['name'] = "家校互动";
        $comData[3]['list'] = $comDataFour;

        return $comData;
    }

    //培训手册首页
    function HomeView(){
        $request= Input('get.','','trim,addslashes');

        //菜单部分
        $menuList = $this->menuList();
        $this->smarty->assign("menuList",$menuList);

        $datatype = array();
        //展示某菜单下的数据
        if(!isset($request['id']) && $request['id'] == ''){
            $request['id'] = '191';
            $datatype['id'] = '191';
        }else{
            $datatype['id'] = $request['id'];
        }
        $sql = "select  i.* from imc_module as i WHERE i.module_id = '{$request['id']}'";
        $moudleListOne = $this->DataControl->selectOne($sql);

        if($moudleListOne['module_class'] == '1'){
            $moudleListOne['module_class_name'] = '集团管理系统';
        }elseif($moudleListOne['module_class'] == '2'){
            $moudleListOne['module_class_name'] = '校务管理系统';
        }elseif($moudleListOne['module_class'] == '3'){
            $moudleListOne['module_class_name'] = 'CRM管理系统';
        }elseif($moudleListOne['module_class'] == '4'){
            $moudleListOne['module_class_name'] = '家校互动';
        }else{
            $moudleListOne['module_class_name'] = '未知';
        }
        $sql = "select i.module_id,i.module_name,i.father_id,i.module_class,i.module_level,h.handbook_name,h.handbook_id,h.handbook_videourl 
                from imc_module as i,imc_module_handbook as h
                where i.module_id = h.module_id and i.father_id = '{$request['id']}'
                ORDER BY i.module_weight ASC,h.handbook_id ASC    ";
        $moudleOneList = $this->DataControl->selectClear($sql);

        $this->smarty->assign("moudleListOne",$moudleListOne);
        $this->smarty->assign("moudleOneList",$moudleOneList);
        $this->smarty->assign("datatype",$datatype);
    }
    //培训手册详情页
    function HelpDetailsView(){
        $request= Input('get.','','trim,addslashes');

        //菜单部分
        $menuList = $this->menuList();
        $this->smarty->assign("menuList",$menuList);

        $datatype = array();
        //单个手册的详情
        $handBookOne = $this->DataControl->selectOne("select h.*,i.module_name,i.father_id   
                    from imc_module_handbook as h  
                    LEFT JOIN imc_module as i ON i.module_id = h.module_id 
                    WHERE h.handbook_id = '{$request['id']}' 
                    ORDER BY h.handbook_id ASC");

        //找到对应的上级菜单
        $sql = "select  m.* from imc_module as i LEFT JOIN imc_module as m ON m.module_id = i.father_id WHERE i.module_id = '{$handBookOne['module_id']}'";
        $moudleListOne = $this->DataControl->selectOne($sql);
        $datatype['menunoduleid'] = $moudleListOne['module_id'];

        //对应的上一个下一个
        $upOne = $this->DataControl->selectOne("select h.handbook_id,h.handbook_name 
                        from imc_module as m 
                        LEFT JOIN imc_module_handbook as h ON m.module_id = h.module_id 
                        WHERE m.father_id = '{$moudleListOne['module_id']}' 
                        and h.handbook_id < '{$request['id']}'
                        ORDER BY handbook_id DESC limit 0,1 ");
        $downOne = $this->DataControl->selectOne("select h.handbook_id,h.handbook_name 
                        from imc_module as m 
                        LEFT JOIN imc_module_handbook as h ON m.module_id = h.module_id 
                        WHERE m.father_id = '{$moudleListOne['module_id']}' 
                        and h.handbook_id > '{$request['id']}'
                        ORDER BY handbook_id ASC limit 0,1 ");
        $handBookOne['upid'] = $upOne['handbook_id'];
        $handBookOne['downid'] = $downOne['handbook_id'];


        if($moudleListOne['module_class'] == '1'){
            $moudleListOne['module_class_name'] = '集团管理系统';
        }elseif($moudleListOne['module_class'] == '2'){
            $moudleListOne['module_class_name'] = '校务管理系统';
        }elseif($moudleListOne['module_class'] == '3'){
            $moudleListOne['module_class_name'] = 'CRM管理系统';
        }elseif($moudleListOne['module_class'] == '4'){
            $moudleListOne['module_class_name'] = '家校互动';
        }else{
            $moudleListOne['module_class_name'] = '未知';
        }

        $this->smarty->assign("moudleListOne",$moudleListOne);
        $this->smarty->assign("handBookOne",$handBookOne);
        $this->smarty->assign("datatype",$datatype);
    }
    //培训手册搜索页
    function HomeSeachView(){
        $request= Input('get.','','trim,addslashes');

        //菜单部分
        $menuList = $this->menuList();
        $this->smarty->assign("menuList",$menuList);

        //筛选出来 关键词 对应的手册
        if(!isset($request['p'])){
            $p = '1';
        }else{
            $p= $request['p'];
        }

        $datawhere ="1";
        $datatype = array();
        $pageurl = "/{$this->u}/{$this->t}?site_id={$request['site_id']}";

        if(isset($request['keyword']) && $request['keyword'] !="" ){
            $datawhere .=" and  h.handbook_name like '%{$request['keyword']}%' ";
            $pageurl .="&keyword={$request['keyword']}";
            $datatype['keyword'] = $request['keyword'];
        }
        $sql = "select h.handbook_id,h.handbook_name,h.handbook_note 
                from imc_module_handbook as h 
                where  {$datawhere} ";

        $db_nums = $this->DataControl->selectOne("SELECT COUNT(h.handbook_id) as allnum FROM imc_module_handbook as h  where {$datawhere}");//相关条件下的总记录数COUNT(*)
        $allnum = $db_nums[allnum];

        $datalist = $this->DataControl->dbwherePage($sql,$allnum,'10',$pageurl.'&p=',$p,'10','8');
        $this->smarty->assign("pagelist",$datalist['pages']);
        $this->smarty->assign("dataList",$datalist['cont']);
        $this->smarty->assign("allnum",$allnum);
        $this->smarty->assign("datatype",$datatype);
    }

    //培训视频
    function HelpvView(){
        $request= Input('get.','','trim,addslashes');

        //菜单部分
        $menuList = $this->menuVideoList();
        $this->smarty->assign("menuList",$menuList);

        $datatype = array();
        //展示某菜单下的数据
        if(!isset($request['id']) && $request['id'] == ''){
            $request['id'] = '23';
            $datatype['id'] = '23';
        }else{
            $datatype['id'] = $request['id'];
        }
        $sql = "select  i.* from imc_module as i WHERE i.module_id = '{$request['id']}'";
        $moudleListOne = $this->DataControl->selectOne($sql);

        if($moudleListOne['module_class'] == '1'){
            $moudleListOne['module_class_name'] = '集团管理系统';
        }elseif($moudleListOne['module_class'] == '2'){
            $moudleListOne['module_class_name'] = '校务管理系统';
        }elseif($moudleListOne['module_class'] == '3'){
            $moudleListOne['module_class_name'] = 'CRM管理系统';
        }elseif($moudleListOne['module_class'] == '4'){
            $moudleListOne['module_class_name'] = '家校互动';
        }else{
            $moudleListOne['module_class_name'] = '未知';
        }
        $sql = "select i.module_id,i.module_name,i.father_id,i.module_class,i.module_level,h.handbook_name,h.handbook_id,h.handbook_videourl
                from imc_module as i,imc_module_handbook as h
                where (i.module_id = h.module_id or i.father_id = h.module_id) and i.father_id = '{$request['id']}' and h.handbook_videourl <>'' 
                ORDER BY i.module_weight ASC,h.handbook_id ASC    ";
        $moudleOneList = $this->DataControl->selectClear($sql);

        $this->smarty->assign("moudleListOne",$moudleListOne);
        $this->smarty->assign("moudleOneList",$moudleOneList);
        $this->smarty->assign("datatype",$datatype);
    }
    //培训手册搜索页
    function HelpvSeachView(){
        $request= Input('get.','','trim,addslashes');

        //菜单部分
        $menuList = $this->menuVideoList();
        $this->smarty->assign("menuList",$menuList);

        //筛选出来 关键词 对应的手册
        if(!isset($request['p'])){
            $p = '1';
        }else{
            $p= $request['p'];
        }

        $datawhere ="1";
        $datatype = array();
        $pageurl = "/{$this->u}/{$this->t}?site_id={$request['site_id']}";

        if(isset($request['keyword']) && $request['keyword'] !="" ){
            $datawhere .=" and  h.handbook_name like '%{$request['keyword']}%' ";
            $pageurl .="&keyword={$request['keyword']}";
            $datatype['keyword'] = $request['keyword'];
        }
        $sql = "select h.handbook_id,h.handbook_name,h.handbook_note 
                from imc_module_handbook as h 
                where  {$datawhere} and h.handbook_videourl <>''  ";

        $db_nums = $this->DataControl->selectOne("SELECT COUNT(h.handbook_id) as allnum FROM imc_module_handbook as h  where {$datawhere} and h.handbook_videourl <>'' ");//相关条件下的总记录数COUNT(*)
        $allnum = $db_nums[allnum];

        $datalist = $this->DataControl->dbwherePage($sql,$allnum,'10',$pageurl.'&p=',$p,'10','2');
        $this->smarty->assign("pagelist",$datalist['pages']);
        $this->smarty->assign("dataList",$datalist['cont']);
        $this->smarty->assign("allnum",$allnum);
        $this->smarty->assign("datatype",$datatype);
    }

    //常见问题
    function HelppView(){
        $request= Input('get.','','trim,addslashes');

        //菜单部分
        $menuList = $this->menuHelpList();
        $this->smarty->assign("menuList",$menuList);

        $datatype = array();
        //展示某菜单下的数据
        if(!isset($request['id']) && $request['id'] == ''){
            $request['id'] = '53';
            $datatype['id'] = '53';
        }else{
            $datatype['id'] = $request['id'];
        }
        $sql = "select  i.* from imc_module as i WHERE i.module_id = '{$request['id']}'";
        $moudleListOne = $this->DataControl->selectOne($sql);

        if($moudleListOne['module_class'] == '1'){
            $moudleListOne['module_class_name'] = '集团管理系统';
        }elseif($moudleListOne['module_class'] == '2'){
            $moudleListOne['module_class_name'] = '校务管理系统';
        }elseif($moudleListOne['module_class'] == '3'){
            $moudleListOne['module_class_name'] = 'CRM管理系统';
        }elseif($moudleListOne['module_class'] == '4'){
            $moudleListOne['module_class_name'] = '家校互动';
        }else{
            $moudleListOne['module_class_name'] = '未知';
        }
        $sql = "select i.module_id,i.module_name,i.father_id,i.module_class,i.module_level,h.faq_name,h.faq_id 
                from imc_module as i,imc_module_faq as h
                where (i.module_id = h.module_id ) and i.module_id = '{$request['id']}' and h.faq_name <>'' 
                ORDER BY i.module_weight ASC,h.faq_id ASC    ";
        $moudleOneList = $this->DataControl->selectClear($sql);

        $this->smarty->assign("moudleListOne",$moudleListOne);
        $this->smarty->assign("moudleOneList",$moudleOneList);
        $this->smarty->assign("datatype",$datatype);
    }
    //常见问题详情页
    function HelppDetailsView(){
        $request= Input('get.','','trim,addslashes');

        //菜单部分
        $menuList = $this->menuHelpList();
        $this->smarty->assign("menuList",$menuList);

        $datatype = array();
        //单个手册的详情
        $handBookOne = $this->DataControl->selectOne("select h.*,i.module_name,i.father_id   
                    from imc_module_faq as h  
                    LEFT JOIN imc_module as i ON i.module_id = h.module_id 
                    WHERE h.faq_id = '{$request['id']}' 
                    ORDER BY h.faq_id ASC");
        //找到对应的上级菜单
        $sql = "select  m.* from imc_module as i 
                LEFT JOIN imc_module as m ON m.module_id = i.module_id 
                WHERE i.module_id = '{$handBookOne['module_id']}'";
        $moudleListOne = $this->DataControl->selectOne($sql);
        $datatype['menunoduleid'] = $moudleListOne['module_id'];

        //对应的上一个下一个
        $upOne = $this->DataControl->selectOne("select h.faq_id,h.faq_name 
                        from imc_module as m 
                        LEFT JOIN imc_module_faq as h ON m.module_id = h.module_id 
                        WHERE m.module_id = '{$moudleListOne['module_id']}' 
                        and h.faq_id < '{$request['id']}'
                        ORDER BY faq_id DESC limit 0,1 ");
        $downOne = $this->DataControl->selectOne("select h.faq_id,h.faq_name 
                        from imc_module as m 
                        LEFT JOIN imc_module_faq as h ON m.module_id = h.module_id 
                        WHERE m.module_id = '{$moudleListOne['module_id']}' 
                        and h.faq_id > '{$request['id']}'
                        ORDER BY faq_id ASC limit 0,1 ");
        $handBookOne['upid'] = $upOne['faq_id'];
        $handBookOne['downid'] = $downOne['faq_id'];


        if($moudleListOne['module_class'] == '1'){
            $moudleListOne['module_class_name'] = '集团管理系统';
        }elseif($moudleListOne['module_class'] == '2'){
            $moudleListOne['module_class_name'] = '校务管理系统';
        }elseif($moudleListOne['module_class'] == '3'){
            $moudleListOne['module_class_name'] = 'CRM管理系统';
        }elseif($moudleListOne['module_class'] == '4'){
            $moudleListOne['module_class_name'] = '家校互动';
        }else{
            $moudleListOne['module_class_name'] = '未知';
        }

        $this->smarty->assign("moudleListOne",$moudleListOne);
        $this->smarty->assign("handBookOne",$handBookOne);
        $this->smarty->assign("datatype",$datatype);
    }
    //常见问题搜多页面
    function HelppSeachView(){
        $request= Input('get.','','trim,addslashes');

        //菜单部分
        $menuList = $this->menuHelpList();
        $this->smarty->assign("menuList",$menuList);

        //筛选出来 关键词 对应的手册
        if(!isset($request['p'])){
            $p = '1';
        }else{
            $p= $request['p'];
        }

        $datawhere ="1";
        $datatype = array();
        $pageurl = "/{$this->u}/{$this->t}?site_id={$request['site_id']}";

        if(isset($request['keyword']) && $request['keyword'] !="" ){
            $datawhere .=" and  h.faq_name like '%{$request['keyword']}%' ";
            $pageurl .="&keyword={$request['keyword']}";
            $datatype['keyword'] = $request['keyword'];
        }
        $sql = "select h.faq_id,h.faq_name,h.faq_note 
                from imc_module_faq as h 
                where  {$datawhere} ";

        $db_nums = $this->DataControl->selectOne("SELECT COUNT(h.faq_id) as allnum FROM imc_module_faq as h  where {$datawhere}");//相关条件下的总记录数COUNT(*)
        $allnum = $db_nums[allnum];

        $datalist = $this->DataControl->dbwherePage($sql,$allnum,'10',$pageurl.'&p=',$p,'10','8');
        $this->smarty->assign("pagelist",$datalist['pages']);
        $this->smarty->assign("dataList",$datalist['cont']);
        $this->smarty->assign("allnum",$allnum);
        $this->smarty->assign("datatype",$datatype);
    }
    function HelppersonView()
    {
        $datatype  = array();
        $this->smarty->assign("datatype",$datatype);
    }




    //魔术方法
    public function __call($name, $arguments) {
        $this->Viewhtm = "under.htm";
    }
    //魔术方法
    function __destruct()
    {
        if ($this->c =='Wait') {
            $this->smarty->assign("u", $this->u);
            $this->smarty->assign("t", $this->t);
            $this->smarty->assign("Viewhtm", $this->Viewhtm);
            $this->display($this->Viewhtm);
            exit;
        }
    }
}