<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2018/12/29
 * Time: 10:09
 */

namespace Work\Controller\Schelp;


class SolutionController extends viewTpl
{
	public $u;
	public $t;
	public $c;
	public $istaffer;
	public $Viewhtm;
	
	function __construct()
	{
		parent::__construct();
//		if(!$this->check_login()){
//			$this->LoginView();
//		}
		
		$this->u = $this->router->getController();
		$this->t = $this->router->getUrl();
		$this->c = $this->router->getAction();
		$this->Viewhtm = $this->router->getController() . "/" . $this->router->getUrl() . ".htm";

//		$this->smarty->assign("istaffer", $this->istaffer);
	}
	
	
	function  HomeView(){
		$datatype  = array();
		$this->smarty->assign("datatype",$datatype);
	}
	
	function SolutionDetailView(){
	
	}
	
	
	
	
	
	
	//魔术方法
	public function __call($name, $arguments) {
		$this->Viewhtm = "under.htm";
		// Note: value of $name is case sensitive.
		//echo "Calling object method '$name' ". implode(', ', $arguments). "\n";
	}
	//魔术方法
	function __destruct()
	{
		if ($this->c =='Wait') {
			$this->smarty->assign("u", $this->u);
			$this->smarty->assign("t", $this->t);
			$this->smarty->assign("Viewhtm", $this->Viewhtm);
			$this->display($this->Viewhtm);
//            $this->display("Home.html");
			exit;
		}
	}
}