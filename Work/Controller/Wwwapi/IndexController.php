<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2018/9/1
 * Time: 11:24
 */

namespace Work\Controller\Wwwapi;


class IndexController extends viewTpl{
    public $u;
    public $t;
    public $c;

    function __construct() {
        parent::__construct ();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
    }

    //登陆验证
    function ThisVerify($request)
    {
        $paramArray = array();
        $paramArray['staffer_id'] = $request['staffer_id'];
        $paramArray['school_id'] = $request['school_id'];
        $paramArray['company_id'] = $request['company_id'];
        $paramArray['token'] = $request['token'];
        if (!$this->UserLimit($paramArray)) {
            $result = array();
            $result["list"] = array();
            $result["tokeninc"] = "0";
            $res = array('error' => '1', 'errortip' => "用户token失效", 'result' => $result);
            ajax_return($res);
        }
    }

    function downexceView(){
        $ys_array = array('K3货号'=>'a','帐列数'=>'b','盘点数'=>'c','差异数'=>'d','差异原因'=>'e');
        $options=array(
            "ssl"=>array(
                "verify_peer"=>false,
                "verify_peer_name"=>false,
            ),
        );
        file_put_contents('analysis.xls',file_get_contents("https://pic.kedingdang.com/schoolmanage/201906282051x771257804.xlsx",false,stream_context_create($options)));
        $sqlarray = execl_to_array("analysis.xlsx",$ys_array);
        array_shift($sqlarray);
        print_r($sqlarray);
    }

    function agebrView(){
        $request = Input('get.','','trim,addslashes');
        echo $this->getAgeByBirth($request['day']);
    }
    function getAgeByBirth($date,$type = 1){
        $nowYear = date("Y",time());
        $nowMonth = date("m",time());
        $nowDay = date("d",time());
        $birthYear = date("Y",$date);
        $birthMonth = date("m",$date);
        $birthDay = date("d",$date);
        if($type == 1){
            $age = $nowYear - ($birthYear - 1);
        }else{
            if($nowMonth<$birthMonth){
                $age = $nowYear - $birthYear - 1;
            }elseif($nowMonth==$birthMonth){
                if($nowDay<$birthDay){
                    $age = $nowYear - $birthYear - 1;
                }else{
                    $age = $nowYear - $birthYear;
                }
            }else{
                $age = $nowYear - $birthYear;
            }
        }
        return $age;
    }



    //呼叫中心手机版本获取 crm 意向名单 -- 97 - 230515
    function getIntentionclientView(){
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $staffOne = $this->DataControl->getFieldOne("smc_staffer","account_class,staffer_img,staffer_enname","staffer_id = '{$request['staffer_id']}' and company_id = '{$request['company_id']}'");
        $postbeOne = $this->DataControl->getFieldOne("gmc_staffer_postbe","school_id,postrole_id","postbe_id = '{$request['re_postbe_id']}'");
        if($staffOne['account_class'] == 1){
            $datawhere = " m.staffer_id = '{$request['staffer_id']}' and  m.marketer_status = 1 ";
        }else {
            if($postbeOne && $postbeOne['school_id'] !== '0') {
                $datawhere = " m.staffer_id = '{$request['staffer_id']}' and p.school_id = '{$postbeOne['school_id']}' and  m.marketer_status = 1 ";
            }else{
                $datawhere = " m.staffer_id = '{$request['staffer_id']}' and  m.marketer_status = 1 ";
            }
        }
        $sqlfields = " m.marketer_id,p.postbe_crmuserlevel  ";
        $marketerOne = $this->DataControl->selectOne("select {$sqlfields},g.company_logo
            from crm_marketer as m
            LEFT JOIN gmc_staffer_postbe as p on m.staffer_id = p.staffer_id and m.company_id = p.company_id 
            left join gmc_company as g on g.company_id = m.company_id
            WHERE {$datawhere} limit 0,1");
        if($staffOne['account_class'] == 1){
            $marketerOne['postbe_crmuserlevel'] = '1';
        }
        if($postbeOne['school_id'] == '0'){
            $postroleOne = $this->DataControl->selectOne(" select postpart_iscrmuser,postpart_crmuserlevel from gmc_company_postrole where postrole_id = '{$postbeOne['postrole_id']}' ");//有crm访问权限时的权限：0普通权限1高管权限2电销权限 3市场  ********补充
            if($postroleOne['postpart_iscrmuser'] == '1' && isset($postroleOne['postpart_crmuserlevel'])){//有crm访问权限时的权限：0普通权限1高管权限2电销权限 3市场 ********补充
                $marketerOne['postbe_crmuserlevel'] = $postroleOne['postpart_crmuserlevel'];
            }else {
                if ($marketerOne['postbe_crmuserlevel'] == '2') {
                    $marketerOne['postbe_crmuserlevel'] = '2';
                } else {
                    $marketerOne['postbe_crmuserlevel'] = '1';
                }
            }
        }

        $request['postbe_crmuserlevel'] = $marketerOne['postbe_crmuserlevel'];
        $request['marketer_id'] = $marketerOne['marketer_id'];
        $request['client_id'] = $request['client_id'];

        $Model = new \Model\Crm\IntentionClientModel($request);
        $clientList = $Model->getIntentionClientListMobile($request);
        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $Model->result), $request['language_type']);
    }

    //呼叫中心手机版本获取 TMK 意向名单 -- 97 - 230515
    function getClientFollowUpMobileListView(){
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new \Model\Gmc\CrmClientModel($request);
        $clientList = $Model->getClientFollowUpMobileList($request);
        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $Model->result), $request['language_type']);
    }

    //慧捷 电话外呼(TMK) 对接 吴磊简版 职工点击事件 -- 231016
    function addCallClickAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new \Model\Crm\PhoneOutboundModel($request);
        $Model->addCallClickAction($request);
        ajax_return(array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $Model->result), $request['language_type']);
    }



}