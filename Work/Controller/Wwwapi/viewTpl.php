<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2016/12/13
 * Time: 21:21
 */
namespace Work\Controller\Wwwapi;


class viewTpl {
    public $Show_css;
    public $router;
	public $intSession;

    public function __construct(){
        global $router;
        // 指定允许其他域名访问
        header('Access-Control-Allow-Origin:*');
        // 响应类型
        header('Access-Control-Allow-Methods:*');
        // 响应头设置
        header('Access-Control-Allow-Headers:x-requested-with,content-type');

        //数据库操作
        $this->DataControl = new \Dbmysql();
        //操作类型
        $this->router = $router;
	
		$this->intSession = new \Incsession();
    }

    //发送短信
    public function Sendmisgo($mobile,$mistxt,$tilte,$sendcode,$company_id='0'){
        $publicarray = array();
        $publicarray['company_id'] = $company_id;
        $minsendModel = new \Model\Api\SmsModel($publicarray);
        return $minsendModel->gmcMisSend($mobile,$mistxt,$tilte,$sendcode);
    }

    //第三方接口权限验证
    function UserLimit($paramArray){
//        print_r($paramArray);
        $stafferOne = $this->DataControl->getFieldOne('smc_staffer',"staffer_id,staffer_leave,staffer_pass,account_class","company_id = '{$paramArray['company_id']}' and staffer_id='{$paramArray['staffer_id']}'");
//        print_r($stafferOne);
        if($stafferOne['account_class'] == '1'){
            $sql="select s.staffer_id,s.staffer_cnname,s.staffer_branch,s.staffer_pass,s.staffer_mobile,s.staffer_tokencode
                                                      from smc_staffer as s
                                                      where s.company_id='{$paramArray['company_id']}' and s.staffer_id='{$paramArray['staffer_id']}' limit 0,1 ";
        }else{
            $sql="select s.staffer_id,s.staffer_cnname,s.staffer_branch,s.staffer_pass,sp.school_id,s.staffer_mobile,s.staffer_tokencode
                                                      from smc_staffer as s
                                                      left join gmc_staffer_postbe as sp on s.staffer_id=sp.staffer_id
                                                      where sp.company_id='{$paramArray['company_id']}'  and s.staffer_id='{$paramArray['staffer_id']}' limit 0,1 ";
        }

        $apiuser = $this->DataControl->selectOne($sql);
        if($apiuser){
            $md5tokenbar = base64_encode(md5($apiuser["staffer_tokencode"].date("Y-m-d")));
            if($md5tokenbar != $paramArray['token']){
                return false;
            }else{
                return true;
            }
        }else{
            return false;
        }
    }

    //登录日志记录表
    function addStafferLoginLog($company_id,$staffer_id,$loginlog_type,$loginlog_source){
        $date = array();
        $date['company_id'] = $company_id;//集团ID
        $date['staffer_id'] = $staffer_id;//员工ID
        $date['loginlog_type'] = $loginlog_type;//登录端口 0 总入口  1 集团 2 校务 3 CRM 4 教务 5 助教 6 助学
        $date['loginlog_source'] = $loginlog_source;//客户端来源 0 PC 1 手机
        $date['loginlog_lastip'] = real_ip();
        $date['loginlog_time'] = time();
        $this->DataControl->insertData('imc_staffer_loginlog',$date);
        return true;
    }

    public function __call($method, $args) {
        echo "unknown method " . $method;
        return false;

    }
}
