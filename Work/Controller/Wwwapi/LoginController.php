<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2018/12/29
 * Time: 10:09
 */

namespace Work\Controller\Wwwapi;


class LoginController extends viewTpl
{
    public $u;
    public $t;
    public $c;

    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();

    }

    //用户获取token11
    function getToken($params = array())
    {
        $stafferOne = $this->DataControl->getFieldOne('smc_staffer', "staffer_id,staffer_tokencode,staffer_tokenencrypt", "staffer_id='{$params['staffer_id']}'");
        if (!$stafferOne) {
            return false;
        }
        $md5tokenbar = base64_encode(md5($stafferOne["staffer_tokencode"] . date("Y-m-d")));
        if ($md5tokenbar == $stafferOne["staffer_tokenencrypt"]) {
            $token = $stafferOne["staffer_tokenencrypt"];
        } else {
            //目前这里注释是为了测试方便
            $tokencode = rand(111111, 999999);
            $md5tokenbar = base64_encode(md5($tokencode . date("Y-m-d")));
            $this->DataControl->query("UPDATE smc_staffer SET staffer_tokencode = '{$tokencode}',staffer_tokenencrypt = '{$md5tokenbar}' WHERE staffer_id ='{$stafferOne['staffer_id']}'");
            $token = $md5tokenbar;
//            $token = $stafferOne["staffer_tokenencrypt"];
        }
        return $token;
    }

    //用户获取token
    function getCompanyOneApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $companyOne = $this->DataControl->getFieldOne('gmc_company', "company_id,company_code,company_shortname,company_cnname,company_logo,company_language", "company_code = '{$request['fromcode']}'");
        if ($companyOne) {
            $field = array();
            $field["company_id"] = "序号";
            $field["company_code"] = "企业授权编号";
            $field["company_shortname"] = "企业简称";
            $field["company_cnname"] = "企业中文名称";
            $field["company_logo"] = "企业logo";
            $field["company_language"] = "语言类型";

            $result = array();
            $result["field"] = $field;
            $result["data"] = $companyOne;
            ajax_return(array('error' => 0, 'errortip' => "企业信息获取成功!", 'result' => $result), $companyOne['company_language']);
        } else {
            ajax_return(array('error' => 1, 'errortip' => "企业账户不存在，请确认授权码是否正确!"), $companyOne['company_language']);
        }
    }

    //登陆验证
    function ThisVerify($request)
    {
        $paramArray = array();
        $paramArray['staffer_id'] = $request['staffer_id'];
        $paramArray['school_id'] = $request['school_id'];
        $paramArray['company_id'] = $request['company_id'];
        $paramArray['token'] = $request['token'];
        if (!$this->UserLimit($paramArray)) {
            $result = array();
            $result["list"] = array();
            $result["tokeninc"] = "0";
            $res = array('error' => '1', 'errortip' => "用户token失效", 'result' => $result);
            ajax_return($res);
        }
    }

    //登录日志记录表
    function addUsersLoginLog($user_id)
    {
        $date = array();
        $date['user_id'] = $user_id;
        $date['loginlog_lastip'] = real_ip();
        $date['loginlog_time'] = time();
        $this->Show_css->insertData('cms_users_loginlog', $date);
        return true;
    }

    //用户的权限
    function stafferPostbeApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        if ($request['marketer_id']) {

        } else {
            $this->ThisVerify($request);//验证账户
        }
        $companyOne = $this->DataControl->getFieldOne('gmc_company', "company_id,company_ismajor,company_language", "company_id = '{$request['company_id']}'");
        $istaffer = array();
        $stafferOne = $this->DataControl->getFieldOne('smc_staffer', "staffer_id,staffer_leave,staffer_pass,account_class", " staffer_id = '{$request['staffer_id']}' and company_id = '{$request['company_id']}'");

        //三端  上次登录进入的学校 -- ********
        if ($request['postbe_id'] > 0) {
            $schoolpostbewhere = " and postbe_id = '{$request['postbe_id']}' ";
        }

        $schoolcrmone = $this->DataControl->selectOne("select s.school_id
                      from imc_staffer_schoollog as s ,crm_marketer as m,smc_school as sl
                      WHERE s.company_id = '{$request['company_id']}' and s.school_id=sl.school_id and s.schoollog_port = '1' and m.staffer_id = '{$request['staffer_id']}'  and s.marketer_id = m.marketer_id {$schoolpostbewhere} and sl.school_type =1 ORDER BY schoollog_id DESC limit 0,1 ");
        $schoolsmcone = $this->DataControl->getFieldOne('imc_staffer_schoollog', 'school_id', " company_id='{$request['company_id']}' and staffer_id='{$request['staffer_id']}' and schoollog_port = '2' {$schoolpostbewhere}  order by schoollog_id desc ");
        $schooleascone = $this->DataControl->getFieldOne('imc_staffer_schoollog', 'school_id', " company_id='{$request['company_id']}' and staffer_id='{$request['staffer_id']}' and schoollog_port = '3' {$schoolpostbewhere}  order by schoollog_id desc ");
        $schoolucsone = $this->DataControl->getFieldOne('imc_staffer_schoollog', 'school_id', " company_id='{$request['company_id']}' and staffer_id='{$request['staffer_id']}' and schoollog_port = '4' {$schoolpostbewhere}  order by schoollog_id desc ");

        if ($stafferOne['account_class'] == '1') {
            $schoolOne = $this->DataControl->getFieldOne('smc_school', "school_id", "company_id = '{$request['company_id']}' and  school_isclose = '0' ", "order by school_istemp DESC");
            $istaffer['isCompany'] = '1';
            $istaffer['isSchool'] = '1';
            if ($companyOne['company_ismajor'] > 0) {
                $istaffer['isCrm'] = '1';
            } else {
                $istaffer['isCrm'] = '0';
            }
            //客诉
            if ($request['company_id'] == '1001') {
                $istaffer['isUcs'] = '1';
            } else {
                $istaffer['isUcs'] = '0';
            }

            $schoolcrmone['school_id'] = $schoolcrmone['school_id'] > 0 ? $schoolcrmone['school_id'] : $schoolOne['school_id'];
            $schoolsmcone['school_id'] = $schoolsmcone['school_id'] > 0 ? $schoolsmcone['school_id'] : $schoolOne['school_id'];
            $schooleascone['school_id'] = $schooleascone['school_id'] > 0 ? $schooleascone['school_id'] : $schoolOne['school_id'];
            $schoolucsone['school_id'] = $schoolucsone['school_id'] > 0 ? $schoolucsone['school_id'] : $schoolOne['school_id'];
        } else {
            $postroleOne = $this->DataControl->getFieldOne("gmc_staffer_postbe", "postrole_id,organize_id,school_id,postbe_iscrmuser,postbe_isucsuser", "postbe_id = '{$request['postbe_id']}'");

            if ($postroleOne['school_id'] == '0') {
                $schoolOne = $this->DataControl->selectOne("SELECT p.school_id FROM gmc_company_organizeschool AS p WHERE p.organize_id = '{$postroleOne['organize_id']}' limit 0,1");

                //判断 进入那个学校 -- ********
                $crmOne = $this->DataControl->getFieldOne("gmc_company_organizeschool", "organize_id", "organize_id = '{$postroleOne['organize_id']}' and school_id = '{$schoolcrmone['school_id']}' ");
                $smcOne = $this->DataControl->getFieldOne("gmc_company_organizeschool", "organize_id", "organize_id = '{$postroleOne['organize_id']}' and school_id = '{$schoolsmcone['school_id']}' ");
                $eascOne = $this->DataControl->getFieldOne("gmc_company_organizeschool", "organize_id", "organize_id = '{$postroleOne['organize_id']}' and school_id = '{$schooleascone['school_id']}' ");
                $ucsOne = $this->DataControl->getFieldOne("gmc_company_organizeschool", "organize_id", "organize_id = '{$postroleOne['organize_id']}' and school_id = '{$schoolucsone['school_id']}' ");

                $schoolcrmone['school_id'] = $crmOne['organize_id'] > 0 ? $schoolcrmone['school_id'] : $postroleOne['school_id'];
                $schoolsmcone['school_id'] = $smcOne['organize_id'] > 0 ? $schoolsmcone['school_id'] : $postroleOne['school_id'];
                $schooleascone['school_id'] = $eascOne['organize_id'] > 0 ? $schooleascone['school_id'] : $postroleOne['school_id'];
                $schoolucsone['school_id'] = $ucsOne['organize_id'] > 0 ? $schoolucsone['school_id'] : $postroleOne['school_id'];


                //集团角色
                $status = $this->DataControl->getFieldOne("gmc_company_postrole", "postpart_iscompanyuser,postpart_iscmsuser,postpart_iscrmuser", "postrole_id = '{$postroleOne['postrole_id']}'");
                if ($status['postpart_iscompanyuser'] == '1') {
                    $istaffer['isCompany'] = '1';
                } else {
                    $istaffer['isCompany'] = '0';
                }

                //校园角色
                $status = $this->DataControl->getFieldOne("gmc_company_postrole", "postpart_iscmsuser", "postrole_id = '{$postroleOne['postrole_id']}'");
                if ($status['postpart_iscmsuser'] == '1') {
                    $istaffer['isSchool'] = '1';
                } else {
                    $istaffer['isSchool'] = '0';
                }
                //CRM权限
                $status = $this->DataControl->getFieldOne("gmc_company_postrole", "postpart_iscrmuser", "postrole_id = '{$postroleOne['postrole_id']}'");
                if ($status['postpart_iscrmuser'] == '1') {
                    if ($companyOne['company_ismajor'] > 0) {
                        $istaffer['isCrm'] = '1';
                    } else {
                        $istaffer['isCrm'] = '0';
                    }
                } else {
                    $istaffer['isCrm'] = '0';
                }
                //客诉权限
                $status = $this->DataControl->getFieldOne("gmc_company_postrole", "postpart_isucsuser", "postrole_id = '{$postroleOne['postrole_id']}'");
                if ($status['postpart_isucsuser'] == '1') {
                    $istaffer['isUcs'] = '1';
                } else {
                    $istaffer['isUcs'] = '0';
                }

                $schoolcrmone['school_id'] = $schoolcrmone['school_id'] > 0 ? $schoolcrmone['school_id'] : $schoolOne['school_id'];
                $schoolsmcone['school_id'] = $schoolsmcone['school_id'] > 0 ? $schoolsmcone['school_id'] : $schoolOne['school_id'];
                $schooleascone['school_id'] = $schooleascone['school_id'] > 0 ? $schooleascone['school_id'] : $schoolOne['school_id'];
                $schoolucsone['school_id'] = $schoolucsone['school_id'] > 0 ? $schoolucsone['school_id'] : $schoolOne['school_id'];
            } else {
                //集团角色
                $istaffer['isCompany'] = '0';
                //校园角色
                $istaffer['isSchool'] = '1';
                //CRM权限
                if ($postroleOne['postbe_iscrmuser'] == '1') {
                    if ($companyOne['company_ismajor'] > 0) {
                        $istaffer['isCrm'] = '1';
                    } else {
                        $istaffer['isCrm'] = '0';
                    }
                } else {
                    $istaffer['isCrm'] = '0';
                }
                //客诉权限
                if ($postroleOne['postbe_isucsuser'] == '1') {
                    $istaffer['isUcs'] = '1';
                } else {
                    $istaffer['isUcs'] = '0';
                }
                $schoolOne['school_id'] = $postroleOne['school_id'];

                //判断 进入那个学校 -- ********
                $crmOne = $this->DataControl->getFieldOne("gmc_staffer_postbe", "postrole_id", "postbe_id = '{$request['postbe_id']}' and school_id = '{$schoolcrmone['school_id']}' ");
                $smcOne = $this->DataControl->getFieldOne("gmc_staffer_postbe", "postrole_id", "postbe_id = '{$request['postbe_id']}' and school_id = '{$schoolsmcone['school_id']}' ");
                $eascOne = $this->DataControl->getFieldOne("gmc_staffer_postbe", "postrole_id", "postbe_id = '{$request['postbe_id']}' and school_id = '{$schooleascone['school_id']}' ");
                $ucsOne = $this->DataControl->getFieldOne("gmc_staffer_postbe", "postrole_id", "postbe_id = '{$request['postbe_id']}' and school_id = '{$schoolucsone['school_id']}' ");

                $schoolcrmone['school_id'] = $crmOne['postrole_id'] > 0 ? $schoolcrmone['school_id'] : $postroleOne['school_id'];
                $schoolsmcone['school_id'] = $smcOne['postrole_id'] > 0 ? $schoolsmcone['school_id'] : $postroleOne['school_id'];
                $schooleascone['school_id'] = $eascOne['postrole_id'] > 0 ? $schooleascone['school_id'] : $postroleOne['school_id'];
                $schoolucsone['school_id'] = $ucsOne['postrole_id'] > 0 ? $schoolucsone['school_id'] : $postroleOne['school_id'];
            }

        }

        $school = $this->DataControl->getFieldOne("smc_school", "school_id", "company_id = '{$request['company_id']}'");
        if (!$school) {
            $istaffer['isUcs'] = '0';
            $istaffer['isCrm'] = '0';
            $istaffer['isSchool'] = '0';
        }

        $result = array();
        if ($istaffer) {
            $result["list"] = $istaffer;
            $result["school_id"] = $schoolOne['school_id'];
            $result["crmschool_id"] = $schoolcrmone['school_id'];
            $result["smcschool_id"] = $schoolsmcone['school_id'];
            $result["eascschool_id"] = $schooleascone['school_id'];
            $result["ucsschool_id"] = $schoolucsone['school_id'];
            $res = array('error' => 0, 'errortip' => '权限获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '权限获取信息', 'result' => $result);
        }
        ajax_return($res);
    }

    //用户信息
    function stafferInfoApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $isaccount = $this->DataControl->getFieldOne("smc_staffer","account_class","staffer_id = '{$request['staffer_id']}'");

        $datawhere = " 1 ";

        if (isset($request['postbe_id']) && $request['postbe_id'] !== "" && $request['postbe_id'] !== "0") {
            $datawhere .= " and sp.postbe_id ='{$request['postbe_id']}'";
        } else {
            if($isaccount['account_class'] == '0'){
                $datawhere .= " and sp.postbe_ismianjob ='1'";
            }
        }

        $staffer = $this->DataControl->selectOne("
                SELECT
                    s.staffer_cnname,
                    sp.postbe_id,
                    s.staffer_img,
                    s.staffer_sex,
                    s.company_id,
                    s.staffer_edition,
                    cp.post_name,
                    c.company_logo,
                    sp.organize_id,
                    sp.school_id,
                    o.organize_cnname,
                    sc.school_cnname,
                    s.account_class
                FROM
                    smc_staffer AS s 
                    left join gmc_staffer_postbe as sp on s.staffer_id = sp.staffer_id
                    left join gmc_company_post as cp on sp.post_id = cp.post_id
                    left join gmc_company as c on s.company_id = c.company_id 
                    left join smc_school as sc on sc.school_id = sp.school_id 
                    left join gmc_company_organize as o on sp.organize_id = o.organize_id 
                WHERE
                    {$datawhere} and s.staffer_id = '{$request['staffer_id']}' order by sp.school_id ASC");
        if($staffer['school_id'] == '0'){
            $staffer['post_name'] = $staffer['organize_cnname'].'-'.$staffer['post_name'];
        }else{
            $staffer['post_name'] = $staffer['school_cnname'].'-'.$staffer['post_name'];
        }
        if(!$staffer['school_id']){
            $school_id = $this->DataControl->getFieldOne("smc_school","school_id","company_id = '{$staffer['company_id']}'");
            $staffer['school_id'] = $school_id['school_id'];
        }
        if(!$staffer['postbe_id']){
            $staffer['re_postbe_id'] = 0;
            $staffer['postbe_id'] = 0;
        }
        $result = array();
        if ($staffer) {
            $result["staffer"] = $staffer;
            $res = array('error' => 0, 'errortip' => '获取个人信息成功', 'result' => $result);
        } else {
            $res = array('error' => 1, 'errortip' => '获取个人信息失败', 'result' => $result);
        }
        ajax_return($res);
    }

    //切换版本
    function changeEditionAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $data = array();
        $data['staffer_edition'] = $request['staffer_edition'];
        $data['staffer_updatetime'] = time();
        if ($this->DataControl->updateData("smc_staffer", "staffer_id = '{$request['staffer_id']}'", $data)) {
            $res = array('error' => 0, 'errortip' => '切换成功');
        } else {
            $res = array('error' => 1, 'errortip' => '切换失败');
        }
        ajax_return($res);

    }


    //账户密码登陆
    function pswdloginApi()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $companyOne = $this->DataControl->getFieldOne('gmc_company', "company_id,company_language", "company_code = '{$request['L_code']}' or company_id = '{$request['L_code']}'");
        if ($companyOne) {
            $nowtime = time();
            $contract = $this->DataControl->selectOne("select contract_id from imc_sales_contract where company_id = '{$companyOne['company_id']}' and UNIX_TIMESTAMP(contract_endtime) < '{$nowtime}' order by contract_id desc ");
            if ($contract['contract_id'] > 0) {
                ajax_return(array('error' => 1, 'errortip' => "您的合同已到期，请联系客服进行续费!"), $companyOne['company_language']);
            }

            $stafferOne = $this->DataControl->getFieldOne('smc_staffer', "staffer_id,staffer_leave,staffer_pass,account_class,staffer_istest,staffer_wxtoken"
                , "(staffer_branch = '{$request['L_name']}' or staffer_mobile = '{$request['L_name']}') and company_id = '{$companyOne['company_id']}'");
            $ComPost = $this->DataControl->getFieldOne("gmc_staffer_postbe", "postbe_id", "staffer_id = '{$stafferOne['staffer_id']}' and school_id = '0'");
            $ScPost = $this->DataControl->getFieldOne("gmc_staffer_postbe", "postbe_id", "staffer_id = '{$stafferOne['staffer_id']}' and school_id <> '0'");
            if ($ComPost && $ScPost) {
                $status = '0';
            }
            if (!$ComPost && $ScPost) {
                $status = '2';
            }
            if ($ComPost && !$ScPost) {
                $status = '1';
            }

            $isAdmin = $stafferOne['account_class'];
            if ($stafferOne) {
                if ($stafferOne['staffer_leave'] == '0') {
                    $password = md5($request['L_pswd']);
                    if ($password == $stafferOne['staffer_pass']) {
                        $istaffer = array();
                        $istaffer['staffer_id'] = $stafferOne['staffer_id'];
                        $istaffer['company_id'] = $companyOne['company_id'];
                        $istaffer['company_language'] = $companyOne['company_language'];
                        $istaffer['staffer_istest'] = $stafferOne['staffer_istest'];
                        if ($stafferOne['account_class'] == '1') {
                            $schoolOne = $this->DataControl->getFieldOne('smc_school', "school_id", "company_id = '{$companyOne['company_id']}' and  school_isclose = '0'", "order by school_id ASC,school_istemp DESC");
                            $istaffer['school_id'] = $schoolOne['school_id'];
                        } else {
                            $schoolOne = $this->DataControl->selectOne("select sp.school_id,sp.postpart_id,sp.post_id
                                FROM gmc_staffer_postbe as sp 
                                where sp.staffer_id = '{$stafferOne['staffer_id']}' and sp.school_id <> '0' and sp.postbe_status = '1'
                                order by sp.postbe_ismianjob DESC,sp.postbe_ismianjob DESC limit 0,1 ");
                            if ($schoolOne && $schoolOne['postpart_id'] == '0') {
                                $postOne = $this->DataControl->getFieldOne('gmc_company_post', "postpart_id", "post_id = '{$schoolOne['post_id']}'", "order by post_id DESC limit 0,1");
                                if ($postOne['postpart_id'] !== '0') {
                                    $this->DataControl->updateData("gmc_staffer_postbe", "staffer_id = '{$stafferOne['staffer_id']}' and school_id = '{$schoolOne['school_id']}' and post_id = '{$schoolOne['post_id']}'", array("postpart_id" => $postOne['postpart_id']));
                                }
                            }

                            $istaffer['school_id'] = $schoolOne['school_id'] ? $schoolOne['school_id'] : 0;

                            if ($schoolOne['school_id'] == '0') {
                                $schoolOne = $this->DataControl->selectOne("select sp.school_id
                                FROM gmc_staffer_postbe as sp 
                                where sp.staffer_id = '{$stafferOne['staffer_id']}' and sp.school_id <> '0' and sp.postbe_status = '1'
                                order by sp.postbe_ismianjob DESC limit 0,1 ");
                                $istaffer['school_id'] = $schoolOne['school_id'] ? $schoolOne['school_id'] : 0;
                            }
                            if ($schoolOne['school_id'] == '') {
                                $schoolOne = $this->DataControl->selectOne("select s.school_id FROM gmc_staffer_postbe as sp,gmc_company_organizeschool as s
where s.organize_id = sp.organize_id and sp.staffer_id = '{$stafferOne['staffer_id']}' and sp.school_id = '0' and sp.postbe_status = '1' order by sp.postbe_ismianjob DESC limit 0,1 ");
                                $istaffer['school_id'] = $schoolOne['school_id'];
                            }
                        }
                        $istaffer['token'] = $this->getToken($stafferOne);

                        if ($stafferOne['staffer_wxtoken'] !== '') {
                            $masterplateOne = $this->DataControl->getFieldOne("gmc_company_masterplate", "masterplate_wxid", "company_id = '{$companyOne['company_id']}' and masterplate_name = '用户登录通知' and masterplate_class = '0'");
                            if ($masterplateOne) {
                                $wxid = $masterplateOne['masterplate_wxid'];
                            } else {
                                $masterplateOne = $this->DataControl->getFieldOne("gmc_company_masterplate", "masterplate_wxid", "company_id = '0' and masterplate_name = '用户登录通知' and masterplate_class = '0'");
                                $wxid = $masterplateOne['masterplate_wxid'];
                            }
                            $wxteModel = new \Model\Api\ZjwxChatModel($stafferOne['staffer_id']);
                            $wxteModel->LoginNotice($wxid);
                        }

                        $this->DataControl->updateData("smc_staffer", "staffer_id = '{$stafferOne['staffer_id']}'", array("staffer_lasttime" => time(), "staffer_lastip" => real_ip()));
                        //登录日志记录表
                        $this->addStafferLoginLog($companyOne['company_id'], $stafferOne['staffer_id'], 0, 0);

                        if ($stafferOne['staffer_istest'] == '0' && $_SERVER['SERVER_NAME'] != 'scloginapi.kedingdang.com') {
                            $istaffer['isTestTips'] = 1;
                            ajax_return(array('error' => 0, 'errortip' => "您所登录的是测试服，请尽快切换到正式服，以免数据丢失！正式服登录地址为：https://sclogin.kedingdang.com/", 'result' => $istaffer, 'isAdmin' => $isAdmin, 'status' => $status), $companyOne['company_language']);
                        } else {
                            $istaffer['isTestTips'] = 0;
                            ajax_return(array('error' => 0, 'errortip' => "登录成功!", 'result' => $istaffer, 'isAdmin' => $isAdmin, 'status' => $status), $companyOne['company_language']);
                        }
                    } else {
                        ajax_return(array('error' => 1, 'errortip' => "密码错误!"), $companyOne['company_language']);
                    }
                } else {
                    ajax_return(array('error' => 1, 'errortip' => "您已离职，账户权限已被关闭!"), $companyOne['company_language']);
                }
            } else {
                ajax_return(array('error' => 1, 'errortip' => "职工账户信息不存在!"), $companyOne['company_language']);
            }
        } else {
            ajax_return(array('error' => 1, 'errortip' => "企业账户不存在，请确认授权码是否正确!"), $companyOne['company_language']);
        }
    }

    //手机快速登录
    function mobileloginApi()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $companyOne = $this->DataControl->getFieldOne('gmc_company', "company_id,company_language", "company_code = '{$request['L_code']}'");
        if ($companyOne) {
            $nowtime = time();
            $contract = $this->DataControl->selectOne("select contract_id from imc_sales_contract where company_id = '{$companyOne['company_id']}' and UNIX_TIMESTAMP(contract_endtime) < '{$nowtime}' order by contract_id desc ");
            if ($contract['contract_id'] > 0) {
                ajax_return(array('error' => 1, 'errortip' => "您的合同已到期，请联系客服进行续费!"), $companyOne['company_language']);
            }

            $stafferOne = $this->DataControl->getFieldOne('smc_staffer', "staffer_id,staffer_leave,staffer_pass,account_class,staffer_istest", "(staffer_branch = '{$request['L_mobile']}' or staffer_mobile = '{$request['L_mobile']}') and company_id = '{$companyOne['company_id']}'");
            $ComPost = $this->DataControl->getFieldOne("gmc_staffer_postbe", "postbe_id", "staffer_id = '{$stafferOne['staffer_id']}' and school_id = '0'");
            $ScPost = $this->DataControl->getFieldOne("gmc_staffer_postbe", "postbe_id", "staffer_id = '{$stafferOne['staffer_id']}' and school_id <> '0'");
            if ($ComPost && $ScPost) {
                $status = '0';
            }
            if (!$ComPost && $ScPost) {
                $status = '2';
            }
            if ($ComPost && !$ScPost) {
                $status = '1';
            }

            $isAdmin = $stafferOne['account_class'];
            if ($stafferOne) {
                if ($stafferOne['staffer_leave'] == '0') {
                    $mobile = trim($request['L_mobile']);
                    $verifycode = trim($request['L_verifycode']);
                    $sendrz = $this->DataControl->getFieldOne('gmc_mislog', "mislog_sendcode", "mislog_mobile='{$mobile}' and mislog_tilte = '快速登录'", "order by mislog_time DESC");
                    if (!$sendrz || $sendrz['mislog_sendcode'] !== $verifycode) {
//                if(!isset($_COOKIE['mislog_sendcode']) || $_COOKIE['mislog_sendcode'] !== $verifycode){
                        $res = array('error' => '1', 'errortip' => '短信验证码错误!');
                        ajax_return($res, $companyOne['company_language']);
                    } else {
                        $istaffer = array();
                        $istaffer['staffer_id'] = $stafferOne['staffer_id'];
                        $istaffer['company_id'] = $companyOne['company_id'];
                        $istaffer['company_language'] = $companyOne['company_language'];
                        $istaffer['staffer_istest'] = $stafferOne['staffer_istest'];
                        $istaffer_info['isAdmin'] = $isAdmin;
                        $istaffer_info['status'] = $status;

                        if ($stafferOne['account_class'] == '1') {
                            $schoolOne = $this->DataControl->getFieldOne('smc_school', "school_id", "company_id = '{$companyOne['company_id']}' and school_isclose = '0' ", "order by school_istemp DESC");
                            $istaffer['school_id'] = $schoolOne['school_id'];
                        } else {
                            $schoolOne = $this->DataControl->selectOne("select sp.school_id,sp.postpart_id,sp.post_id
                                FROM gmc_staffer_postbe as sp 
                                where sp.staffer_id = '{$stafferOne['staffer_id']}' and sp.school_id <> '0' and sp.postbe_status = '1'
                                order by sp.postbe_ismianjob DESC,sp.postbe_ismianjob DESC limit 0,1 ");
                            if ($schoolOne && $schoolOne['postpart_id'] == '0') {
                                $postOne = $this->DataControl->getFieldOne('gmc_company_post', "postpart_id", "post_id = '{$schoolOne['post_id']}'", "order by post_id DESC limit 0,1");
                                if ($postOne['postpart_id'] !== '0') {
                                    $this->DataControl->updateData("gmc_staffer_postbe", "staffer_id = '{$stafferOne['staffer_id']}' and school_id = '{$schoolOne['school_id']}' and post_id = '{$schoolOne['post_id']}'", array("postpart_id" => $postOne['postpart_id']));
                                }
                            }

                            $istaffer['school_id'] = $schoolOne['school_id'] ? $schoolOne['school_id'] : 0;

                            if ($schoolOne['school_id'] == '0') {
                                $schoolOne = $this->DataControl->selectOne("select sp.school_id
                                FROM gmc_staffer_postbe as sp 
                                where sp.staffer_id = '{$stafferOne['staffer_id']}' and sp.school_id <> '0' and sp.postbe_status = '1'
                                order by sp.postbe_ismianjob DESC limit 0,1 ");
                                $istaffer['school_id'] = $schoolOne['school_id'] ? $schoolOne['school_id'] : 0;
                            }
                            if ($schoolOne['school_id'] == '') {
                                $schoolOne = $this->DataControl->selectOne("select s.school_id FROM gmc_staffer_postbe as sp,gmc_company_organizeschool as s
where s.organize_id = sp.organize_id and sp.staffer_id = '{$stafferOne['staffer_id']}' and sp.school_id = '0' and sp.postbe_status = '1' order by sp.postbe_ismianjob DESC limit 0,1 ");
                                $istaffer['school_id'] = $schoolOne['school_id'];
                            }
                        }
                        $istaffer['token'] = $this->getToken($stafferOne);
                        $istaffer_info['isAdmin'] = $isAdmin;
                        $istaffer_info['status'] = $status;
                        $this->DataControl->updateData("smc_staffer", "staffer_id = '{$stafferOne['staffer_id']}'", array("staffer_lasttime" => time(), "staffer_lastip" => real_ip()));
                        //登录日志记录表
                        $this->addStafferLoginLog($companyOne['company_id'], $stafferOne['staffer_id'], 0, 0);

                        if ($stafferOne['staffer_istest'] == '0' && $_SERVER['SERVER_NAME'] != 'scloginapi.kedingdang.com') {
                            $istaffer['isTestTips'] = 1;
                            ajax_return(array('error' => 0, 'errortip' => "您所登录的是测试服，请尽快切换到正式服，以免数据丢失！正式服登录地址为：https://sclogin.kedingdang.com/", 'result' => $istaffer, 'isAdmin' => $isAdmin, 'status' => $status), $companyOne['company_language']);
                        } else {
                            $istaffer['isTestTips'] = 0;
                            ajax_return(array('error' => 0, 'errortip' => "登录成功!", 'result' => $istaffer, 'isAdmin' => $isAdmin, 'status' => $status), $companyOne['company_language']);
                        }
                    }
                } else {
                    ajax_return(array('error' => 1, 'errortip' => "您已离职，账户权限已被关闭!"), $companyOne['company_language']);
                }
            } else {
                ajax_return(array('error' => 1, 'errortip' => "职工账户信息不存在!"), $companyOne['company_language']);
            }
        } else {
            ajax_return(array('error' => 1, 'errortip' => "企业账户不存在，请确认授权码是否正确!"), $companyOne['company_language']);
        }
    }

    //获取手机验证码
    function getverifycodeApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $companyOne = $this->DataControl->getFieldOne('gmc_company', "company_id,company_language", "company_code = '{$request['L_code']}'");
        if ($companyOne) {

            if($companyOne['company_id']=='8888'){
                $res = array('error' => '1', 'errortip' => '直营校短信登录已关闭,请从钉钉登录!');
                ajax_return($res, $companyOne['company_language']);
            }


            $stafferOne = $this->DataControl->getFieldOne('smc_staffer', "staffer_id,staffer_leave,staffer_pass,staffer_istest", "(staffer_branch = '{$request['L_mobile']}' or staffer_mobile = '{$request['L_mobile']}') and company_id = '{$companyOne['company_id']}'");
            if (!$stafferOne) {
                $res = array('error' => '1', 'errortip' => '职工账户信息不存在!');
                ajax_return($res, $companyOne['company_language']);
            } else {
                $mobile = trim($request['L_mobile']);
                //一小时内发送次数
                $mintime = time() - 3600;
                $mislognum = $this->DataControl->selectOne("select count(mislog_id) as mislognum from gmc_mislog where mislog_mobile='{$mobile}' and mislog_tilte = '快速登录' and mislog_time >= '{$mintime}' limit 0,1 ");
                if ($mislognum['mislognum'] > 5) {
                    $res = array('error' => '1', 'errortip' => '您的手机已超出发送验证码次数，请联系客服！');
                    ajax_return($res, $companyOne['company_language']);
                }
                //最近一次发送时间
                $sendmisrz = $this->DataControl->getFieldOne('gmc_mislog', "mislog_time", "mislog_mobile='{$mobile}' and mislog_tilte = '快速登录'", "order by mislog_time DESC");
                if ($sendmisrz && (time() - $sendmisrz['mislog_time']) < 60) {
                    $res = array('error' => '1', 'errortip' => '验证码已发送！');
                    ajax_return($res, $companyOne['company_language']);
                } else {
                    $tilte = "快速登录";
                    $sendcode = rand(111111, 999999);
                    setcookie('mislog_sendcode', $sendcode, time() + 1800);
                    $contxt = "用户您好！您的手机验证码为{$sendcode}，请完成验证，如非本人操作，请忽略本短信。";
                    if ($stafferOne['staffer_istest'] == '0' && $_SERVER['SERVER_NAME'] != 'scloginapi.kedingdang.com') {
                        $contxt .= "本次登录为测试服，请尽快切换到正式服，以免数据丢失！";
                    }
                    //短信发送
                    if ($this->Sendmisgo($mobile, $contxt, $tilte, $sendcode, $companyOne['company_id'])) {
                        $res = array('error' => '0', 'errortip' => '发送成功', "bakfuntion" => "okmotify");
                        ajax_return($res, $companyOne['company_language']);
                    } else {
                        $res = array('error' => '1', 'errortip' => '发送失败!', "bakfuntion" => "errormotify");
                        ajax_return($res, $companyOne['company_language']);
                    }
                }
            }
        } else {
            ajax_return(array('error' => 1, 'errortip' => "企业账户不存在，请确认授权码是否正确!"), $companyOne['company_language']);
        }
    }

    function getComPostApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $ComPost = $this->DataControl->selectClear("SELECT
                p.postbe_id,
                o.organize_cnname as cnname,
                s.post_name,
                (select school_id from gmc_company_organizeschool as a WHERE a.organize_id = o.organize_id limit 0,1) as school_id
            FROM
                gmc_staffer_postbe AS p
                LEFT JOIN gmc_company_organize AS o ON p.organize_id = o.organize_id
                LEFT JOIN gmc_company_post AS s ON p.post_id = s.post_id 
            WHERE
                p.staffer_id = '{$request['staffer_id']}' and p.school_id = 0");
        $info = $this->DataControl->getFieldOne("smc_staffer", "staffer_sex,staffer_mobile,staffer_enname,staffer_branch,staffer_img,staffer_cnname,company_id", "staffer_id = '{$request['staffer_id']}'");

        $companyOne = $this->DataControl->getFieldOne('gmc_company', "company_id,company_language", "company_id = '{$info['company_id']}'");
        if ($ComPost) {
            $result = array();
            $result["data"] = $ComPost;
            $result["info"] = $info;
            $res = array('error' => '0', 'errortip' => '获取集团职务成功', 'result' => $result);
            ajax_return($res, $companyOne['company_language'], 1);
        } else {
            $res = array('error' => '1', 'errortip' => '暂无集团职务!', 'result' => array());
            ajax_return($res, $companyOne['company_language'], 1);
        }
    }

    function getPostApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $ComPost = $this->DataControl->selectClear("
            SELECT
                p.postbe_id,
                c.school_shortname AS cnname,
                c.school_id as school_id,
                s.post_name
            FROM
                gmc_staffer_postbe AS p
                LEFT JOIN smc_school AS c ON p.school_id = c.school_id
                LEFT JOIN gmc_company_post AS s ON p.post_id = s.post_id 
            WHERE
                p.staffer_id = '{$request['staffer_id']}' 
                AND p.school_id > 0 UNION
            SELECT
                p.postbe_id,
                o.organize_cnname AS cnname,
                ( SELECT school_id FROM gmc_company_organizeschool AS a WHERE a.organize_id = o.organize_id LIMIT 0, 1 ) AS school_id,
                s.post_name 
            FROM
                gmc_staffer_postbe AS p
                LEFT JOIN gmc_company_organize AS o ON p.organize_id = o.organize_id
                LEFT JOIN gmc_company_post AS s ON p.post_id = s.post_id 
            WHERE
                p.staffer_id = '{$request['staffer_id']}' 
                AND p.school_id = 0
            ORDER BY
                school_id ASC");

        $info = $this->DataControl->getFieldOne("smc_staffer", "staffer_sex,staffer_mobile,staffer_enname,staffer_branch,staffer_img,staffer_cnname,company_id", "staffer_id = '{$request['staffer_id']}'");

        $companyOne = $this->DataControl->getFieldOne('gmc_company', "company_id,company_language", "company_id = '{$info['company_id']}'");
        if ($ComPost) {
            $result = array();
            $result["data"] = $ComPost;
            $result["info"] = $info;
            $res = array('error' => '0', 'errortip' => '获取职务成功', 'result' => $result);
            ajax_return($res, $companyOne['company_language'], 1);
        } else {
            $res = array('error' => '1', 'errortip' => '暂无职务!', 'result' => array());
            ajax_return($res, $companyOne['company_language'], 1);
        }
    }

    function getScPostApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $ComPost = $this->DataControl->selectClear("SELECT
                p.postbe_id,
                c.school_shortname as cnname,
                c.school_address,
                c.school_id,
                s.post_name 
            FROM
                gmc_staffer_postbe AS p
                left join smc_school as c on p.school_id = c.school_id
                LEFT JOIN gmc_company_post AS s ON p.post_id = s.post_id 
            WHERE
                p.staffer_id = '{$request['staffer_id']}' and p.school_id > 0");

        $info = $this->DataControl->getFieldOne("smc_staffer", "staffer_sex,staffer_mobile,staffer_enname,staffer_branch,staffer_img,staffer_cnname,company_id", "staffer_id = '{$request['staffer_id']}'");

        $companyOne = $this->DataControl->getFieldOne('gmc_company', "company_id,company_language", "company_id = '{$info['company_id']}'");
        if ($ComPost) {
            $result = array();
            $result["data"] = $ComPost;
            $result["info"] = $info;
            $res = array('error' => '0', 'errortip' => '获取校园职务成功', 'result' => $result);
            ajax_return($res, $companyOne['company_language'], 1);
        } else {
            $res = array('error' => '1', 'errortip' => '暂无校园职务!', 'result' => array());
            ajax_return($res, $companyOne['company_language'], 1);
        }
    }

    //切换职务
    function ChangePostApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $stafferOne = $this->DataControl->getFieldOne('smc_staffer', "staffer_id,staffer_leave,staffer_pass,account_class,company_id", "staffer_id = '{$request['staffer_id']}'");

        $companyOne = $this->DataControl->getFieldOne('gmc_company', "company_id,company_language", "company_id = '{$stafferOne['company_id']}'");

        $ComPost = $this->DataControl->getFieldOne("gmc_staffer_postbe", "postbe_id", "staffer_id = '{$stafferOne['staffer_id']}' and school_id = '0'");
        $ScPost = $this->DataControl->getFieldOne("gmc_staffer_postbe", "postbe_id", "staffer_id = '{$stafferOne['staffer_id']}' and school_id > '0'");

        if ($ComPost != '' && $ScPost != '') {
            $status = '0';
        }
        if ($ComPost == '' && $ScPost != '') {
            $status = '2';
        }
        if ($ComPost != '' && $ScPost == '') {
            $status = '1';
        }

        $isAdmin = $stafferOne['account_class'];
        ajax_return(array('error' => 0, 'errortip' => "切换成功!", 'isAdmin' => $isAdmin, 'status' => $status), $companyOne['company_language']);


    }

    function ComTokenApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $staffer_id = $this->DataControl->getFieldOne("crm_marketer", "staffer_id", "marketer_id = '{$request['marketer_id']}'");
        $token = $this->DataControl->getFieldOne("smc_staffer", "staffer_tokenencrypt,company_id", "staffer_id = '{$staffer_id['staffer_id']}'");
        $companyOne = $this->DataControl->getFieldOne('gmc_company', "company_id,company_language", "company_id = '{$token['company_id']}'");
        ajax_return(array('error' => 0, 'errortip' => "获取成功!", 'token' => $token['staffer_tokenencrypt']), $companyOne['company_language']);

    }

    //CRM 手机端登陆
    function CrmLoginApi()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $companyOne = $this->DataControl->getFieldOne('gmc_company', "company_id,company_language", "company_code = '{$request['L_code']}'");
        if ($companyOne) {
            $stafferOne = $this->DataControl->getFieldOne('smc_staffer', "staffer_id,staffer_leave,staffer_pass,account_class,staffer_istest", "(staffer_branch = '{$request['L_name']}' or staffer_mobile = '{$request['L_name']}') and company_id = '{$companyOne['company_id']}'");
            $ComPost = $this->DataControl->getFieldOne("gmc_staffer_postbe", "postbe_id", "staffer_id = '{$stafferOne['staffer_id']}' and school_id = '0' and postbe_iscrmuser =1");
            $ScPost = $this->DataControl->getFieldOne("gmc_staffer_postbe", "postbe_id", "staffer_id = '{$stafferOne['staffer_id']}' and school_id <> '0' and postbe_iscrmuser =1");
            if ($ComPost && $ScPost) {
                $status = '0';
            }
            if (!$ComPost && $ScPost) {
                $status = '2';
            }
            if ($ComPost && !$ScPost) {
                $status = '1';
            }
            if (!$ComPost && !$ScPost) {
                $status = '-1';
                ajax_return(array('error' => 1, 'errortip' => "您没有CRM权限"), $companyOne['company_language']);
            }


            $isAdmin = $stafferOne['account_class'];
            if ($stafferOne) {
                if ($stafferOne['staffer_leave'] == '0') {
                    $password = md5($request['L_pswd']);
                    if ($password == $stafferOne['staffer_pass']) {
                        $istaffer = array();
                        $istaffer['staffer_id'] = $stafferOne['staffer_id'];
                        $istaffer['company_id'] = $companyOne['company_id'];
                        if ($stafferOne['account_class'] == '1') {
                            $schoolOne = $this->DataControl->getFieldOne('smc_school', "school_id", "company_id = '{$companyOne['company_id']}'", "order by school_istemp DESC");
                            $istaffer['school_id'] = $schoolOne['school_id'];
                        } else {
                            $schoolOne = $this->DataControl->selectOne("select sp.school_id,sp.postpart_id,sp.post_id
                                FROM gmc_staffer_postbe as sp
                                where sp.staffer_id = '{$stafferOne['staffer_id']}' and sp.school_id <> '0' and sp.postbe_status = '1'
                                order by sp.postbe_ismianjob DESC,sp.postbe_ismianjob DESC limit 0,1 ");
                            if ($schoolOne && $schoolOne['postpart_id'] == '0') {
                                $postOne = $this->DataControl->getFieldOne('gmc_company_post', "postpart_id", "post_id = '{$schoolOne['post_id']}'", "order by post_id DESC limit 0,1");
                                if ($postOne['postpart_id'] !== '0') {
                                    $this->DataControl->updateData("gmc_staffer_postbe", "staffer_id = '{$stafferOne['staffer_id']}' and school_id = '{$schoolOne['school_id']}' and post_id = '{$schoolOne['post_id']}'", array("postpart_id" => $postOne['postpart_id']));
                                }
                            }

                            $istaffer['school_id'] = $schoolOne['school_id'] ? $schoolOne['school_id'] : 0;

                            if ($schoolOne['school_id'] == '0') {
                                $schoolOne = $this->DataControl->selectOne("select sp.school_id
                                FROM gmc_staffer_postbe as sp
                                where sp.staffer_id = '{$stafferOne['staffer_id']}' and sp.school_id <> '0' and sp.postbe_status = '1'
                                order by sp.postbe_ismianjob DESC limit 0,1 ");
                                $istaffer['school_id'] = $schoolOne['school_id'] ? $schoolOne['school_id'] : 0;
                            }
                            if ($schoolOne['school_id'] == '') {
                                $schoolOne = $this->DataControl->selectOne("select s.school_id FROM gmc_staffer_postbe as sp,gmc_company_organizeschool as s
where s.organize_id = sp.organize_id and sp.staffer_id = '{$stafferOne['staffer_id']}' and sp.school_id = '0' and sp.postbe_status = '1' order by sp.postbe_ismianjob DESC limit 0,1 ");
                                $istaffer['school_id'] = $schoolOne['school_id'];
                            }
                        }
                        $istaffer['token'] = $this->getToken($stafferOne);
                        $this->DataControl->updateData("smc_staffer", "staffer_id = '{$stafferOne['staffer_id']}'", array("staffer_lasttime" => time(), "staffer_lastip" => real_ip()));
                        ajax_return(array('error' => 0, 'errortip' => "登录成功!", 'result' => $istaffer, 'isAdmin' => $isAdmin, 'status' => $status), $companyOne['company_language']);
                    } else {
                        ajax_return(array('error' => 1, 'errortip' => "密码错误!"), $companyOne['company_language']);
                    }
                } else {
                    ajax_return(array('error' => 1, 'errortip' => "您已离职，账户权限已被关闭!"), $companyOne['company_language']);
                }
            } else {
                ajax_return(array('error' => 1, 'errortip' => "职工账户信息不存在!"), $companyOne['company_language']);
            }
        } else {
            ajax_return(array('error' => 1, 'errortip' => "企业账户不存在，请确认授权码是否正确!"), $companyOne['company_language']);
        }
    }

    function jdbssoApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $userAttribute = $request['userAttribute'];
        $userSign = $request['userSign'];
        $Workeross = new \Model\Api\JdbssoModel();
        $userArray = $Workeross->getDataToInfp($userAttribute);
        if (!$Workeross->getDataVerify($userAttribute, $userSign)) {
            ajax_return(array('error' => 1, 'errortip' => "授权信息不存在，禁止登录!"), 'cn');
        } else {
            $companyOne = $this->DataControl->getFieldOne('gmc_company', "company_id,company_language", "company_id = '8888'");
            if ($companyOne) {
                $stafferOne = $this->DataControl->getFieldOne('smc_staffer', "staffer_id,staffer_leave,staffer_pass,account_class,staffer_istest", "staffer_employeepid = '{$userArray['jobnumber']}'");
                $ComPost = $this->DataControl->getFieldOne("gmc_staffer_postbe", "postbe_id", "staffer_id = '{$stafferOne['staffer_id']}' and school_id = '0'");
                $ScPost = $this->DataControl->getFieldOne("gmc_staffer_postbe", "postbe_id", "staffer_id = '{$stafferOne['staffer_id']}' and school_id <> '0'");
                if ($ComPost && $ScPost) {
                    $status = '0';
                }
                if (!$ComPost && $ScPost) {
                    $status = '2';
                }
                if ($ComPost && !$ScPost) {
                    $status = '1';
                }

                $isAdmin = $stafferOne['account_class'];
                if ($stafferOne) {
                    if ($stafferOne['staffer_leave'] == '0') {
                        $istaffer = array();
                        $istaffer['staffer_id'] = $stafferOne['staffer_id'];
                        $istaffer['company_id'] = $companyOne['company_id'];
                        $istaffer['company_language'] = $companyOne['company_language'];
                        $istaffer['staffer_istest'] = $stafferOne['staffer_istest'];
                        if ($stafferOne['account_class'] == '1') {
                            $schoolOne = $this->DataControl->getFieldOne('smc_school', "school_id", "company_id = '{$companyOne['company_id']}' and  school_isclose = '0'", "order by school_id ASC,school_istemp DESC");
                            $istaffer['school_id'] = $schoolOne['school_id'];
                        } else {
                            $schoolOne = $this->DataControl->selectOne("select sp.school_id,sp.postpart_id,sp.post_id
                                FROM gmc_staffer_postbe as sp
                                where sp.staffer_id = '{$stafferOne['staffer_id']}' and sp.school_id <> '0' and sp.postbe_status = '1'
                                order by sp.postbe_ismianjob DESC,sp.postbe_ismianjob DESC limit 0,1 ");
                            if ($schoolOne && $schoolOne['postpart_id'] == '0') {
                                $postOne = $this->DataControl->getFieldOne('gmc_company_post', "postpart_id", "post_id = '{$schoolOne['post_id']}'", "order by post_id DESC limit 0,1");
                                if ($postOne['postpart_id'] !== '0') {
                                    $this->DataControl->updateData("gmc_staffer_postbe", "staffer_id = '{$stafferOne['staffer_id']}' and school_id = '{$schoolOne['school_id']}' and post_id = '{$schoolOne['post_id']}'", array("postpart_id" => $postOne['postpart_id']));
                                }
                            }

                            $istaffer['school_id'] = $schoolOne['school_id'] ? $schoolOne['school_id'] : 0;

                            if ($schoolOne['school_id'] == '0') {
                                $schoolOne = $this->DataControl->selectOne("select sp.school_id
                                FROM gmc_staffer_postbe as sp
                                where sp.staffer_id = '{$stafferOne['staffer_id']}' and sp.school_id <> '0' and sp.postbe_status = '1'
                                order by sp.postbe_ismianjob DESC limit 0,1 ");
                                $istaffer['school_id'] = $schoolOne['school_id'] ? $schoolOne['school_id'] : 0;
                            }
                            if ($schoolOne['school_id'] == '') {
                                $schoolOne = $this->DataControl->selectOne("select s.school_id FROM gmc_staffer_postbe as sp,gmc_company_organizeschool as s
where s.organize_id = sp.organize_id and sp.staffer_id = '{$stafferOne['staffer_id']}' and sp.school_id = '0' and sp.postbe_status = '1' order by sp.postbe_ismianjob DESC limit 0,1 ");
                                $istaffer['school_id'] = $schoolOne['school_id'];
                            }
                        }
                        $istaffer['token'] = $this->getToken($stafferOne);
                        $this->DataControl->updateData("smc_staffer", "staffer_id = '{$stafferOne['staffer_id']}'", array("staffer_lasttime" => time(), "staffer_lastip" => real_ip()));
                        $istaffer['isTestTips'] = 0;
                        $InOurtUrl = "https://sclogin.kedingdang.com/choose?token={$istaffer['token']}&staffer_id={$istaffer['staffer_id']}&company_id={$istaffer['company_id']}&status={$status}&isAdmin={$isAdmin}&language=zh";
                        header("Location:{$InOurtUrl}");
                        exit;
                    } else {
                        ajax_return(array('error' => 1, 'errortip' => "您已离职，账户权限已被关闭!"), $companyOne['company_language']);
                    }
                } else {
                    ajax_return(array('error' => 1, 'errortip' => "职工账户信息不存在!"), $companyOne['company_language']);
                }
            } else {
                ajax_return(array('error' => 1, 'errortip' => "企业账户不存在，请确认授权码是否正确!"), $companyOne['company_language']);
            }
        }
    }


    //获取登录功能
    function getSignApi()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $GmcSign = $this->DataControl->selectClear("
            SELECT
                s.* 
            FROM
                imc_sign AS s
            WHERE sign_type = '1'");

        $post = $this->DataControl->getFieldOne("gmc_staffer_postbe", "postrole_id,postpart_id,postbe_crmuserlevel,school_id,postbe_iscrmuser", "postbe_id = '{$request['postbe_id']}'");
        $role = $this->DataControl->getFieldOne("gmc_company_postrole","postpart_iscompanyuser,postpart_iscmsuser,postpart_iscrmuser","postrole_id = '{$post['postrole_id']}'");
        $part = $this->DataControl->getFieldOne("smc_school_postpart","postpart_isbeike,postpart_istraining,postpart_isregister,postpart_isucsuser","postpart_id = '{$post['postpart_id']}'");

        $account = $this->DataControl->getFieldOne("smc_staffer","account_class","staffer_id = '{$request['staffer_id']}'");


        if ($GmcSign) {

            foreach ($GmcSign as &$val) {
                if ($this->DataControl->selectClear("select module_id from gmc_staffer_usermodule where postrole_id = '{$post['postrole_id']}' and module_id = '{$val['module_id']}' and company_id = '{$request['company_id']}'")) {
                    $val['type'] = '0';
                }else{
                    $val['type'] = '1';
                }
                if($post['school_id'] == '0'){
                    if($role['postpart_iscompanyuser'] == '0'){
                        $val['type'] = '1';
                    }
                }
                if($account['account_class'] == '1'){
                    $val['type'] = '0';
                }
            }
        }
        if($post['school_id'] == '0'){
            $gmc = $role['postpart_iscompanyuser'];
            $smc = $role['postpart_iscmsuser'];
            $crm = $role['postpart_iscrmuser'];
        }else{
            $gmc = '0';
            $smc = '1';
            $crm = $post['postbe_iscrmuser'];
        }
        if($account['account_class'] == '1'){
            $gmc = '1';
            $smc = '1';
            $crm = '1';
        }
        $CrmSign = $this->DataControl->selectClear("
            SELECT
                s.* 
            FROM
                imc_sign AS s
            WHERE sign_type = '2'");

        if ($CrmSign) {
            if($post['school_id'] != '0'){
                foreach ($CrmSign as &$val) {
                    if($crm == '1'){
                        if ($post['postbe_crmuserlevel'] == '1') {
                            $val['type'] = '0';
                        }

                        if ($post['postbe_crmuserlevel'] == '0') {
                            if ($val['module_id'] == '4' || $val['module_id'] == '292' || $val['module_id'] == '685') {
                                $val['type'] = '1';
                            } else {
                                $val['type'] = '0';
                            }
                        }

                        if ($post['postbe_crmuserlevel'] == '2') {
                            if ($val['module_id'] == '1' || $val['module_id'] == '2' || $val['module_id'] == '9' || $val['module_id'] == '683') {
                                $val['type'] = '0';
                            } else {
                                $val['type'] = '1';
                            }
                        }
                        if($account['account_class'] == '1'){
                            $val['type'] = '0';
                        }
                    }else{
                        $val['type'] = '1';
                    }

                }
            }else{
                $iscrm = $this->DataControl->getFieldOne("gmc_company_postrole","postpart_iscrmuser","postrole_id = '{$post['postrole_id']}'");
                if($iscrm['postpart_iscrmuser'] == '1'){
                    foreach ($CrmSign as &$val) {
                        $val['type'] = '0';
                        if($role['postpart_iscrmuser'] == '0'){
                            $val['type'] = '1';
                        }
                        if($account['account_class'] == '1'){
                            $val['type'] = '0';
                        }
                    }
                }else{
                    foreach ($CrmSign as &$val) {
                        $val['type'] = '1';
                        if($account['account_class'] == '1'){
                            $val['type'] = '0';
                        }
                    }
                }
            }

        }
        $ScSign = $this->DataControl->selectClear("
            SELECT
                s.* 
            FROM
                imc_sign AS s
            WHERE sign_type = '3'");

        if ($ScSign) {
            foreach ($ScSign as &$val) {
                if ($this->DataControl->selectClear("select module_id from smc_staffer_usermodule where postpart_id = '{$post['postpart_id']}' and module_id = '{$val['module_id']}' and company_id = '{$request['company_id']}'")) {
                    $val['type'] = '0';
                    if($post['school_id'] == '0'){
                        if($role['postpart_iscmsuser'] == '0'){
                            $val['type'] = '1';
                        }
                    }
                    if($account['account_class'] == '1'){
                        $val['type'] = '0';
                    }
                }else{
                    $val['type'] = '1';
                    if($account['account_class'] == '1'){
                        $val['type'] = '0';
                    }
                }
            }
        }
        $OtSign = $this->DataControl->selectClear("
            SELECT
                s.* 
            FROM
                imc_sign AS s
            WHERE sign_type = '4'");

        if ($OtSign) {
            foreach ($OtSign as &$val) {
                $val['type'] = '0';
            }
        }

        $notice = $this->DataControl->getFieldOne("gmc_company", "company_notice", "company_id = '{$request['company_id']}'");
        $stafferOne = $this->DataControl->getFieldOne('smc_staffer', "staffer_id,staffer_leave,staffer_pass,account_class,staffer_istest", "staffer_id = '{$request['staffer_id']}'");
        $companyOne = $this->DataControl->getFieldOne('gmc_company', "company_id,company_code,company_shortname,company_cnname,company_logo,company_language", "company_id = '{$request['company_id']}'");

        $easx = array();
        $easx['path_url'] = 'https://easx.kedingdang.com';
        $easx['sign_url'] = '/Educational/mySchedule';
        $easx['sign_img'] = 'https://pic.kedingdang.com/schoolmanage/**********25x351280625.png';
        $easx['sign_lockimg'] = 'https://pic.kedingdang.com/schoolmanage/**********25x337497781.png';
        $easx['sign_noimg'] = 'https://pic.kedingdang.com/schoolmanage/**********25x560213819.png';
        if($part['postpart_isregister'] == '1'){
            $easx['type'] = '0';
        }else{
            $easx['type'] = '1';
        }
        if($account['account_class'] == '1'){
            $easx['type'] = '0';
        }
        $easx['sign_name'] = '教务管理';


        $inter = array();
        $inter['path_url'] = 'https://easx.kedingdang.com/';
        $inter['sign_url'] = '/Interesting/index';
        $inter['sign_img'] = 'https://pic.kedingdang.com/schoolmanage/**********28x295193591.png';
        $inter['sign_lockimg'] = 'https://pic.kedingdang.com/schoolmanage/**********28x110921442.png';
        $inter['sign_noimg'] = 'https://pic.kedingdang.com/schoolmanage/**********28x358091145.png';
        if($part['postpart_isbeike'] == '1'){
            $inter['type'] = '0';
        }else{
            $inter['type'] = '1';
        }
        if($account['account_class'] == '1'){
            $inter['type'] = '0';
        }
        $inter['sign_name'] = '备课管理';

        $training = array();
        $training['path_url'] = 'https://easx.kedingdang.com';
        $training['sign_url'] = '/Training/CareerGrowth';
        $training['sign_img'] = 'https://pic.kedingdang.com/schoolmanage/**********29x340243621.png';
        $training['sign_lockimg'] = 'https://pic.kedingdang.com/schoolmanage/**********29x389729791.png';
        $training['sign_noimg'] = 'https://pic.kedingdang.com/schoolmanage/**********29x102056318.png';
        if($part['postpart_istraining'] == '1'){
            $training['type'] = '0';
        }else{
            $training['type'] = '1';
        }
        if($account['account_class'] == '1'){
            $training['type'] = '0';
        }
        $training['sign_name'] = '培训管理';


//        var_dump($part['postpart_isucsuser']);die();

        $usc = array();
        $usc['path_url'] = 'https://ucs.kedingdang.com';
        $usc['sign_url'] = '/home';
        $usc['sign_img'] = 'https://pic.kedingdang.com/schoolmanage/**********29x440196999.png';
        $usc['sign_lockimg'] = 'https://pic.kedingdang.com/schoolmanage/**********30x588077319.png';
        $usc['sign_noimg'] = 'https://pic.kedingdang.com/schoolmanage/**********29x309175781.png';
        if($part['postpart_isucsuser'] == '1'){
            $usc['type'] = '0';
        }else{
            $usc['type'] = '1';
        }
        if($account['account_class'] == '1'){
            $usc['type'] = '0';
        }
        $usc['sign_name'] = '客诉管理';

        $integral = array();
        $integral['path_url'] = 'https://gmc.kedingdang.com';
        $integral['sign_url'] = '/ProductManagement';
        $integral['sign_img'] = 'https://pic.kedingdang.com/schoolmanage/**********31x297973181.png';
        $integral['sign_lockimg'] = 'https://pic.kedingdang.com/schoolmanage/**********31x980506358.png';
        $integral['sign_noimg'] = 'https://pic.kedingdang.com/schoolmanage/**********31x636762002.png';
        $islogin = $this->DataControl->selectOne("select module_id from gmc_staffer_usermodule where postrole_id = '{$post['postrole_id']}' and module_id = '35' and company_id = '{$request['company_id']}'");
        if($islogin){
            $integral['type'] = '0';
        }else{
            $integral['type'] = '1';
        }
        if($account['account_class'] == '1'){
            $integral['type'] = '0';
        }
        $integral['sign_name'] = '积分商城';

        $scshop = array();
        $scshop['path_url'] = 'https://gmc.kedingdang.com';
        $scshop['sign_url'] = '/ScshopCenter/goodsManage';
        $scshop['sign_img'] = 'https://pic.kedingdang.com/schoolmanage/**********32x479769395.png';
        $scshop['sign_lockimg'] = 'https://pic.kedingdang.com/schoolmanage/**********32x566832046.png';
        $scshop['sign_noimg'] = 'https://pic.kedingdang.com/schoolmanage/**********32x221707372.png';
        $islogin = $this->DataControl->selectOne("select module_id from gmc_staffer_usermodule where postrole_id = '{$post['postrole_id']}' and module_id = '526' and company_id = '{$request['company_id']}'");
        if($islogin){
            $scshop['type'] = '0';
        }else{
            $scshop['type'] = '1';
        }
        if($account['account_class'] == '1'){
            $scshop['type'] = '0';
        }
        $scshop['sign_name'] = '微商城';

        $lesson = array();
        $lesson['path_url'] = 'https://talkline.kedingdang.com';
        $lesson['sign_url'] = '/Financial/Home';
        $lesson['sign_img'] = 'https://pic.kedingdang.com/schoolmanage/**********30x188705097.png';
        $lesson['sign_lockimg'] = 'https://pic.kedingdang.com/schoolmanage/**********30x227443908.png';
        $lesson['sign_noimg'] = 'https://pic.kedingdang.com/schoolmanage/**********30x020660589.png';
        $lesson['type'] = '0';
        if($account['account_class'] == '1'){
            $lesson['type'] = '0';
        }
        $lesson['sign_name'] = '网课管理';

        $other['easx'] = $easx;
        $other['inter'] = $inter;
        $other['training'] = $training;
        $other['usc'] = $usc;
        $other['integral'] = $integral;
        $other['scshop'] = $scshop;
        $other['lesson'] = $lesson;


        $result = array();
        $result["GmcSign"] = $GmcSign;
        $result["CrmSign"] = $CrmSign;
        $result["ScSign"] = $ScSign;
        $result["OtSign"] = $OtSign;
        $result["other"] = $other;
        $result["gmc"] = $gmc;
        $result["smc"] = $smc;
        $result["crm"] = $crm;
        $result["notice"] = $notice['company_notice'];
        $result["staffer_id"] = $request['staffer_id'];
        $result["postbe_id"] = $request['postbe_id'];
        $result["company_id"] = $request['company_id'];
        $result["language"] = $companyOne['company_language'];



        if ($request['postbe_id'] == '0') {
            $result["school_id"] = $request['school_id'];
        } else {
            $postroleOne = $this->DataControl->getFieldOne("gmc_staffer_postbe", "postrole_id,organize_id,school_id,postbe_iscrmuser,postbe_isucsuser", "postbe_id = '{$request['postbe_id']}'");
            if($postroleOne['school_id'] > 0){
                $result["school_id"] = $postroleOne['school_id'];
            }else{
                $schoolOne = $this->DataControl->selectOne("SELECT p.school_id FROM gmc_company_organizeschool AS p WHERE p.organize_id = '{$postroleOne['organize_id']}' limit 0,1");
                $result["school_id"] = $schoolOne['school_id'];
            }
        }
        $result["token"] = $this->getToken($stafferOne);

        ajax_return(array('error' => '0', 'errortip' => '获取职务成功', 'result' => $result));
    }


    //添加试用
    function addApplyAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->Model = new \Model\Gmc\PostModel($request);

        $result = $this->Model->addApplyAction($request);
        ajax_return($result, $request['language_type']);
    }


}
