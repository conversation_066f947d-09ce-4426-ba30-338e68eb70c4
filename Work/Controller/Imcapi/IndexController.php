<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2018/9/1
 * Time: 11:24
 */

namespace Work\Controller\Imcapi;


class IndexController extends viewTpl
{
    public $u;
    public $t;
    public $c;

    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
    }

    function HomeView()
    {

    }


    function EidtDealOrderTimeView()
    {
        $reqeust = Input('get.','','trim,addslashes');
        $Model = new \Model\Imc\EditOperaModel();
        $Model->EidtDealOrderTime($reqeust['trading_pid'], $reqeust['date']);
        var_dump($Model->error);
        var_dump($Model->errortip);
    }


    function EditHourDayView()
    {
        $Model = new \Model\Imc\EditOperaModel();
        $Model->EditHourDay(492020, '2020-04-01');
        var_dump($Model->error);
        var_dump($Model->errortip);
    }

    /**
     *  更新订单
     * author: ling
     * 对应接口文档 0001
     */
    function EditStuOrdertCreateTimeView()
    {
        $reqeust = Input('get.','','trim,addslashes');
        $Model = new \Model\Imc\EditOperaModel();
        $Model->EditStuOrdertCreateTime($reqeust['trading_pid'], $reqeust['date']);
        var_dump($Model->error);
        var_dump($Model->errortip);
    }

    /**
     *   删除学员考勤
     */
     function  delStudentHourStudyView(){
         $reqeust = Input('get.','','trim,addslashes');
         if(!$reqeust['student_id']){
             debug("学员id ");
         }
         if(!$reqeust['class_id']){
             debug("班级id");
         }
         if(!$reqeust['from']){
             debug("来源");
         }
         $Model = new \Model\Imc\EditOperaModel();
         $Model->delStudentHourStudy($reqeust['student_id'], $reqeust['class_id'],$reqeust['start_day'],$reqeust['end_day'],$reqeust['from']);
         var_dump($Model->error);
         var_dump($Model->errortip);
     }


     function  EditReduceFeeOrderTimeView(){
         $reqeust = Input('get.','','trim,addslashes');
         $Model = new \Model\Imc\EditOperaModel();
//         $Model->EditReduceOrderTime($reqeust['student_id'], $reqeust['class_id'],$reqeust['start_day'],$reqeust['end_day'],$reqeust['from']);
         $Model->EditReduceOrderTime($reqeust['trading_pid'], $reqeust['date']);
         var_dump($Model->error);
         var_dump($Model->errortip);
     }



}