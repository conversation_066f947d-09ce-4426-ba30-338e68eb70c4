<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2016/12/13
 * Time: 21:21
 */

namespace Work\Controller\Talkline;


class viewTpl {
    public $smarty;
    public $Static;
    public $intSession;
    public $DataControl;
    public $router;
    public $StafferLogin=false;//管理用户
    public $variableAll;//系统变量

    public function __construct(){
        global $router;
        global $smarty;
        global $viewControl;
        //模板引擎开启
        $this->smarty = new \Smarty();
        //Session引擎开启
        $this->intSession = new \Incsession();
        //数据库操作
        $this->DataControl = new \Dbsqlplay();
        //操作类型
        $this->router = $router;

        $this->smarty->template_dir = BASEDIR.'/Work/View/Talkline/';
        $this->smarty->compile_dir = BASEDIR.'/Temp/Compiled/Talkline/';
        $this->smarty->config_dir = BASEDIR.'/Common/';
        $this->smarty->cache_dir = BASEDIR.'/Temp/Caches/';

        //指定定界符
        $this->smarty->left_delimiter="{";	//左定界符
        $this->smarty->right_delimiter="}";	//右定界符

        $this->smarty->compile_check = true;
        $this->smarty->debugging = true;

        $this->StafferLogin = false;

        $viewControl = $this->DataControl;
        $smarty = $this->smarty;
        include(ROOT_PATH . "Core/Smarty/int.class.php");

        $webUrl = "/";

        //静态资源加载
        $this->smarty->assign("CssUrl", $webUrl."Work/Static/Talkline/css/", true);
        $this->smarty->assign("JsUrl", $webUrl."Work/Static/Talkline/js/", true);
        $this->smarty->assign("ImgUrl", $webUrl."Work/Static/Talkline/images/", true);
        $this->smarty->assign("PluginsUrl", $webUrl."Work/Static/Talkline/plugins/", true);
        $this->smarty->assign("StaticUrl", IMG_PATH, true);
    }
    //检测用户是否登录 session 检测stafferLogin
    public function check_login(){
        if($this->intSession->getCookiearray('staffer') && count($this->intSession->getCookiearray('staffer')) > 0){
            $login_staffer = $this->intSession->getCookiearray('staffer');
            if(!empty($login_staffer) && $login_staffer){
                $istaffer = $this->DataControl->selectOne("SELECT u.*,c.company_cnname FROM smc_staffer as u,gmc_company as c where c.company_id = u.company_id AND u.staffer_id='{$login_staffer['staffer_id']}'");
                if(!$istaffer){
                    $this->intSession->setCookiearray("staffer",array(),'1');
                    return false;
                }else{
                    $datewhere = "s.company_id = '{$istaffer['company_id']}'";
                    if($istaffer['account_class'] == '0'){
                        $datewhere .= " AND (s.school_id in (SELECT p.school_id FROM gmc_staffer_postbe as p WHERE p.staffer_id = '{$istaffer['staffer_id']}')
                        or s.school_id in (SELECT o.school_id FROM gmc_staffer_postbe as sp,gmc_company_organizeschool as o
where o.organize_id = sp.organize_id and sp.staffer_id = '{$istaffer['staffer_id']}' and sp.school_id = '0' and sp.postbe_status = '1' order by sp.postbe_ismianjob DESC))";
                    }

                    $istaffer['schoolList'] = $this->DataControl->selectClear("SELECT s.school_branch,s.school_cnname
FROM smc_school AS s WHERE {$datewhere} GROUP BY s.school_id ORDER BY s.school_branch ASC");

                    $this->StafferLogin = $istaffer;
                    return true;
                }
            }else{
                return false;
            }
        }else{
            return false;
        }
    }

    //管理操作日志
    public function Recordweblog($module,$actiontype,$content){
        if($this->StafferLogin){
            $date = array();
            $date['staffer_id'] = $this->StafferLogin['staffer_id'];
            $date['company_id'] = $this->StafferLogin['company_id'];
            $date['userlog_module'] = $module;
            $date['userlog_type'] = $actiontype;
            $date['userlog_content'] = $content;
            $date['userlog_ip'] = real_ip();
            $date['userlog_time'] = time();
            $this->DataControl->insertData('tkl_userlog',$date);
            return true;
        }else{
            return false;
        }
    }

    //发送短信
    public function Sendmisgo($mobile,$mistxt,$tilte,$sendcode,$company_id='0',$mislog_type,$student_id,$hourrooms_id){
        $publicarray = array();
        $publicarray['company_id'] = $company_id;
        $minsendModel = new \Model\Api\SmsModel($publicarray);
        return $minsendModel->tklMisSend($mobile,$mistxt,$tilte,$sendcode,$mislog_type,$student_id,$hourrooms_id);
    }

    public function display($tempview=""){
        return $this->smarty->display($tempview);
    }
    public function __call($method, $args) {
        echo "unknown method " . $method;
        return false;

    }
    //后台登录文件
    public function LoginView() {
        $this->display("login.htm");
        exit;
    }
}