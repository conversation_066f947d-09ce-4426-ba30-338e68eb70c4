<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/3/11
 * Time: 14:05
 */

namespace Work\Controller\Talkline;


class lineCourseController extends viewTpl{
    public $data;
    public $Viewhtm;
    public $u;
    public $t;
    public $c;
//    public $FYCOMID = '79080';
//    public $FYWKPID= '122042';
    public $FYCOMID = '8888';
    public $FYWKPID= '122042';
//吉的堡网校    fyjx  122042
//陕西向桂教育科技有限公司（吉的堡） shjdbjy     11171
//陕西向桂教育科技有限公司加盟校区（吉的堡）  shjdbjm   100558
    function __construct() {
        parent::__construct ();
        if(!$this->check_login()){
            $this->LoginView();
        }
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
        $this->istaffer = $this->StafferLogin;
        $this->smarty->assign("istaffer", $this->StafferLogin);
        $this->smarty->assign("FYWKPID", $this->FYWKPID);
        $this->Viewhtm = $this->router->getController()."/".$this->router->getUrl().".htm";

    }

    //主页
    function LineclassView()
    {
        $request = Input('get.','','trim,addslashes');
        $smarty = $this->smarty;

        if(!isset($request['p'])){
            $p = '1';
        }else{
            $p= $request['p'];
        }

        $datawhere = "c.company_id = '{$this->istaffer['company_id']}' AND c.lineclass_isdel = '0'";
        $pageurl = "/{$this->u}/{$this->t}?";

        $datatype = array();
        if(isset($request['keyword']) && $request['keyword'] !==''){
            $datawhere .= " and (c.lineclass_branch = '{$request['keyword']}' or c.lineclass_cnname like '%{$request['keyword']}%' or c.lineclass_enname like '%{$request['keyword']}%')";
            $pageurl .="&keyword={$request['keyword']}";
            $datatype['keyword'] = $request['keyword'];
        }

        if (isset($request['starttime']) && $request['starttime'] !== '') {
            $datawhere .= " and c.lineclass_enddate >= '{$request['starttime']}'";
            $pageurl .= "&starttime={$request['starttime']}";
            $datatype['starttime'] = $request['starttime'];
        }

        if (isset($request['endtime']) && $request['endtime'] !== '') {
            $datawhere .= " and c.lineclass_stdate <= '{$request['endtime']}'";
            $pageurl .= "&endtime={$request['endtime']}";
            $datatype['endtime'] = $request['endtime'];
        }

        $sql = "SELECT c.*,
                ( SELECT COUNT(h.hourrooms_id) FROM tkl_lineclass_hourrooms AS h WHERE h.lineclass_id = c.lineclass_id ) AS hourroomsnums,
                ( SELECT COUNT(h.hourroomstudy_id) FROM tkl_lineclass_hourroomstudy AS h WHERE h.lineclass_id = c.lineclass_id ) AS studynums
                FROM tkl_lineclass AS c WHERE {$datawhere} order by c.lineclass_id DESC";

        $db_nums = $this->DataControl->selectOne("SELECT COUNT(c.lineclass_id) as countnums FROM tkl_lineclass AS c 
                                          WHERE {$datawhere}");//相关条件下的总记录数COUNT(*)
        $allnum = $db_nums['countnums'];

        $datalist = $this->DataControl->dbwherePage($sql,$allnum,'20',$pageurl.'&p=',$p,'10','2');
        $smarty->assign("pagelist",$datalist['pages']);//筛选信息

        $smarty->assign("dataList",$datalist['cont']);
        $smarty->assign("datatype",$datatype);
    }

    //排课管理
    function HourroomsView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $DataControl = $this->DataControl;
        $smarty = $this->smarty;

        if (!isset($request['p'])) {
            $p = '1';
        } else {
            $p = $request['p'];
        }
        $datawhere = "c.lineclass_id = h.lineclass_id AND c.company_id = '{$this->istaffer['company_id']}'";
        $pageurl = "/{$this->u}/{$this->t}?";
        $lineclassOne = $this->DataControl->getOne("tkl_lineclass", "lineclass_id='{$request['lineclass_id']}'");
        $this->smarty->assign("lineclassOne", $lineclassOne);
        $datawhere .= " AND c.lineclass_id = '{$lineclassOne['lineclass_id']}'";
        $pageurl .= "&lineclass_id={$request['lineclass_id']}";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (h.hourrooms_name = '{$request['keyword']}' or h.hourrooms_threenumber like '%{$request['keyword']}%' or c.lineclass_branch like '%{$request['keyword']}%' or c.lineclass_enname like '%{$request['keyword']}%')";
            $pageurl .= "&keyword={$request['keyword']}";
            $datatype['keyword'] = $request['keyword'];
        }
        if (isset($request['hourrooms_maxvideo']) && $request['hourrooms_maxvideo'] !== '') {
            $maxvideo = $request['hourrooms_maxvideo'] + 1;
            $datawhere .= " and h.hourrooms_maxvideo = '{$maxvideo}'";
            $pageurl .= "&hourrooms_maxvideo={$request['hourrooms_maxvideo']}";
            $datatype['hourrooms_maxvideo'] = $request['hourrooms_maxvideo'];
        }

        $MonthArray = GetTheMonth(date("Y-m-d"));

        if (isset($request['starttime']) && $request['starttime'] !== '') {
            $starttime = strtotime($request['starttime']);
            $datawhere .= " and h.hourrooms_endtime >= '{$starttime}'";
            $pageurl .= "&starttime={$request['starttime']}";
            $datatype['starttime'] = $request['starttime'];
        }

        if (isset($request['endtime']) && $request['endtime'] !== '') {
            $endtime = strtotime($request['endtime']) + 3600 * 23;
            $datawhere .= " and h.hourrooms_starttime <= '{$endtime}'";
            $pageurl .= "&editendtime={$request['endtime']}";
            $datatype['editendtime'] = $request['endtime'];
        }

        $sql = "SELECT h.*,c.lineclass_branch,c.lineclass_enname ,
                ( SELECT COUNT(s.hourroomstudy_id) FROM tkl_lineclass_hourroomstudy AS s WHERE s.hourrooms_id = h.hourrooms_id ) AS studynums
                 FROM tkl_lineclass_hourrooms as h,tkl_lineclass AS c
                 where {$datawhere} order by h.hourrooms_starttime ASC";

        $companyOne = $this->DataControl->getFieldOne("gmc_company", "company_tkyapino", "company_id='{$this->istaffer['company_id']}'");

        if (isset($request['is_export']) && $request['is_export'] == 1) {
//            $this->c = 'export';
//            $sql .= " limit 0,2000";
//            $dateexcelarray = $this->DataControl->selectClear($sql);
//            if ($dateexcelarray) {
//                $outexceldate = array();
//                foreach ($dateexcelarray as $dateexcelvar) {
//                    $datearray = array();
//                    $datearray['school_cnname'] = $dateexcelvar['school_cnname'];
//                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
//                    $datearray['class_enname'] = $dateexcelvar['class_enname'];
//                    $datearray['class_branch'] = $dateexcelvar['class_branch'];
//                    $datearray['hourrooms_threenumber'] = $dateexcelvar['hourrooms_threenumber'];
//                    $datearray['hourrooms_name'] = $dateexcelvar['hourrooms_name'];
//                    $datearray['hourrooms_fromclass'] = $dateexcelvar['hourrooms_fromclass'] == 0 ? '拓课云' : '云枢';
//                    $datearray['hourrooms_maxvideo'] = $dateexcelvar['hourrooms_maxvideo'] - 1;
//                    $datearray['hourrooms_date'] = $dateexcelvar['hourrooms_starttime'] == false ? '--' : date("Y-m-d", $dateexcelvar['hourrooms_starttime']);
//                    $datearray['hourrooms_week'] = $dateexcelvar['hourrooms_starttime'] == false ? '--' : $dateexcelvar['hourrooms_week'];
//                    $datearray['hourrooms_starttime'] = date("H:i", $dateexcelvar['hourrooms_starttime']);
//                    $datearray['hourrooms_endtime'] = date("H:i", $dateexcelvar['hourrooms_endtime']);
//                    $datearray['hourrooms_chairmanpwd'] = $dateexcelvar['hourrooms_chairmanpwd'];
//                    $datearray['hourrooms_assistantpwd'] = $dateexcelvar['hourrooms_assistantpwd'];
//                    $datearray['hourrooms_patrolpwd'] = $dateexcelvar['hourrooms_patrolpwd'];
//                    $datearray['hourrooms_confuserpwd'] = $dateexcelvar['hourrooms_confuserpwd'];
//                    if ($dateexcelvar['hourrooms_threenumber']) {
//                        $datearray['hourrooms_chairman_url'] = "https://global.talk-cloud.net/" . $dateexcelvar['hourrooms_threenumber'] . '/' . $companyOne['company_tkyapino'] . '/1/0';
//                        $datearray['hourrooms_assistan_url'] = "https://global.talk-cloud.net/" . $dateexcelvar['hourrooms_threenumber'] . '/' . $companyOne['company_tkyapino'] . '/1/0';
//                        $datearray['hourrooms_patrol_url'] = "https://global.talk-cloud.net/" . $dateexcelvar['hourrooms_threenumber'] . '/' . $companyOne['company_tkyapino'] . '/1/0';
//                        $datearray['hourrooms_confuser_url'] = "https://global.talk-cloud.net/" . $dateexcelvar['hourrooms_threenumber'] . '/' . $companyOne['company_tkyapino'] . '/' . $dateexcelvar['hourrooms_passwordrequired'] . '/2';
//                    }
//                    $outexceldate[] = $datearray;
//                }
//                $excelheader = array("校园名称", "校园编号", "班级名称", "班级编号", "教室号", "教室名称", "所属平台", "最大上台数", "上课日期", "上课周次", "开始时间", "结束时间", "教师密码", "助教密码", "巡课密码", "上课密码", "教师上课地址", "助教上课地址", "主管巡课地址", "学员上课地址");
//                $excelfileds = array('school_cnname', 'school_branch', 'class_enname', 'class_branch', 'hourrooms_threenumber', 'hourrooms_name', 'hourrooms_name', 'hourrooms_fromclass', 'hourrooms_maxvideo',
//                    'hourrooms_date', 'hourrooms_week', 'hourrooms_starttime', 'hourrooms_endtime', 'hourrooms_chairmanpwd', 'hourrooms_assistantpwd', 'hourrooms_patrolpwd', 'hourrooms_confuserpwd', 'hourrooms_chairman_url', 'hourrooms_assistan_url', 'hourrooms_patrol_url', 'hourrooms_confuser_url');
//                query_to_excel($excelheader, $outexceldate, $excelfileds, $request['editstarttime'] . '-' . $request['editendtime'] . "网课系统排课明细.xls");
//                ajax_return(array('error' => 0, 'errortip' => "导出完毕!", "bakfuntion" => "okmotify"));
//            }
        }
        else {
            $db_nums = $DataControl->selectOne("SELECT COUNT(h.hourrooms_id) as countnums 
                FROM tkl_lineclass_hourrooms as h,tkl_lineclass AS c where {$datawhere}");

            $allnum = $db_nums['countnums'];
            $datalist = $DataControl->dbwherePage($sql, $allnum, '10', $pageurl . '&p=', $p, '10', '2');
            $smarty->assign("pagelist", $datalist['pages']);//筛选信息
            $smarty->assign("dataList", $datalist['cont']);
            $smarty->assign("datatype", $datatype);
        }
    }

    //删除某排课
    function DelhourroomOneAction()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $hourroomstudyOne = $this->DataControl->getOne("tkl_lineclass_hourrooms", "hourrooms_id='{$request['hourrooms_id']}'");
        if($hourroomstudyOne['hourrooms_ischecking'] != '0'){
            ajax_return(array('error' => 1, 'errortip' => "已考勤课时禁止删除!", "bakfuntion" => "dangerFromTip"));
        }
        $lineclassOne = $this->DataControl->getOne("tkl_lineclass", "lineclass_id='{$hourroomstudyOne['lineclass_id']}'");

        if ($this->DataControl->delData('tkl_lineclass_hourrooms', "hourrooms_id='{$request['hourrooms_id']}'")) {
            $this->Recordweblog("全国班课管理", "全国班课管理", "删除课时数据,全国班课编号：{$lineclassOne['lineclass_branch']}，CID:{$hourroomstudyOne['hourrooms_id']}");
            ajax_return(array('error' => 0, 'errortip' => "提交成功!", "bakfuntion" => "okmotify"));
        } else {
            ajax_return(array('error' => 1, 'errortip' => "教是删除失败!", "bakfuntion" => "errormotify"));
        }
    }

    function classHourView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $DataControl = $this->DataControl;
        $smarty = $this->smarty;

        if (!isset($request['p'])) {
            $p = '1';
        } else {
            $p = $request['p'];
        }
        $datawhere = "c.lineclass_id = h.lineclass_id AND c.company_id = '{$this->istaffer['company_id']}'";
        $pageurl = "/{$this->u}/{$this->t}?";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (h.hourrooms_name = '{$request['keyword']}' or h.hourrooms_threenumber like '%{$request['keyword']}%' 
            or c.lineclass_branch like '%{$request['keyword']}%' or c.lineclass_enname like '%{$request['keyword']}%' or c.lineclass_cnname like '%{$request['keyword']}%')";
            $pageurl .= "&keyword={$request['keyword']}";
            $datatype['keyword'] = $request['keyword'];
        }
        if (isset($request['hourrooms_maxvideo']) && $request['hourrooms_maxvideo'] !== '') {
            $maxvideo = $request['hourrooms_maxvideo'] + 1;
            $datawhere .= " and h.hourrooms_maxvideo = '{$maxvideo}'";
            $pageurl .= "&hourrooms_maxvideo={$request['hourrooms_maxvideo']}";
            $datatype['hourrooms_maxvideo'] = $request['hourrooms_maxvideo'];
        }

        $MonthArray = GetTheMonth(date("Y-m-d"));

        if (isset($request['starttime']) && $request['starttime'] !== '') {
            $starttime = strtotime($request['starttime']);
            $datawhere .= " and h.hourrooms_endtime >= '{$starttime}'";
            $pageurl .= "&starttime={$request['starttime']}";
            $datatype['starttime'] = $request['starttime'];
        }

        if (isset($request['endtime']) && $request['endtime'] !== '') {
            $endtime = strtotime($request['endtime']) + 3600 * 23;
            $datawhere .= " and h.hourrooms_starttime <= '{$endtime}'";
            $pageurl .= "&editendtime={$request['endtime']}";
            $datatype['endtime'] = $request['endtime'];
        }

        $sql = "SELECT h.*,c.lineclass_branch,c.lineclass_cnname,c.lineclass_enname,
                ( SELECT COUNT(s.hourroomstudy_id) FROM tkl_lineclass_hourroomstudy AS s WHERE s.hourrooms_id = h.hourrooms_id ) AS studynums
                 FROM tkl_lineclass_hourrooms as h,tkl_lineclass AS c
                 where {$datawhere} order by h.hourrooms_id DESC";

        $companyOne = $this->DataControl->getFieldOne("gmc_company", "company_tkyapino", "company_id='{$this->istaffer['company_id']}'");

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $this->c = 'export';
            $sql .= " limit 0,2000";
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['school_cnname'] = $dateexcelvar['school_cnname'];
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $datearray['class_enname'] = $dateexcelvar['class_enname'];
                    $datearray['class_branch'] = $dateexcelvar['class_branch'];
                    $datearray['hourrooms_threenumber'] = $dateexcelvar['hourrooms_threenumber'];
                    $datearray['hourrooms_name'] = $dateexcelvar['hourrooms_name'];
                    $datearray['hourrooms_fromclass'] = $dateexcelvar['hourrooms_fromclass'] == 0 ? '拓课云' : '云枢';
                    $datearray['hourrooms_maxvideo'] = $dateexcelvar['hourrooms_maxvideo'] - 1;
                    $datearray['hourrooms_date'] = $dateexcelvar['hourrooms_starttime'] == false ? '--' : date("Y-m-d", $dateexcelvar['hourrooms_starttime']);
                    $datearray['hourrooms_week'] = $dateexcelvar['hourrooms_starttime'] == false ? '--' : $dateexcelvar['hourrooms_week'];
                    $datearray['hourrooms_starttime'] = date("H:i", $dateexcelvar['hourrooms_starttime']);
                    $datearray['hourrooms_endtime'] = date("H:i", $dateexcelvar['hourrooms_endtime']);
                    $datearray['hourrooms_chairmanpwd'] = $dateexcelvar['hourrooms_chairmanpwd'];
                    $datearray['hourrooms_assistantpwd'] = $dateexcelvar['hourrooms_assistantpwd'];
                    $datearray['hourrooms_patrolpwd'] = $dateexcelvar['hourrooms_patrolpwd'];
                    $datearray['hourrooms_confuserpwd'] = $dateexcelvar['hourrooms_confuserpwd'];
                    if ($dateexcelvar['hourrooms_threenumber']) {
                        $datearray['hourrooms_chairman_url'] = "https://global.talk-cloud.net/" . $dateexcelvar['hourrooms_threenumber'] . '/' . $companyOne['company_tkyapino'] . '/1/0';
                        $datearray['hourrooms_assistan_url'] = "https://global.talk-cloud.net/" . $dateexcelvar['hourrooms_threenumber'] . '/' . $companyOne['company_tkyapino'] . '/1/0';
                        $datearray['hourrooms_patrol_url'] = "https://global.talk-cloud.net/" . $dateexcelvar['hourrooms_threenumber'] . '/' . $companyOne['company_tkyapino'] . '/1/0';
                        $datearray['hourrooms_confuser_url'] = "https://global.talk-cloud.net/" . $dateexcelvar['hourrooms_threenumber'] . '/' . $companyOne['company_tkyapino'] . '/' . $dateexcelvar['hourrooms_passwordrequired'] . '/2';
                    }
                    $outexceldate[] = $datearray;
                }
                $excelheader = array("校园名称", "校园编号", "班级名称", "班级编号", "教室号", "教室名称", "所属平台", "最大上台数", "上课日期", "上课周次", "开始时间", "结束时间", "教师密码", "助教密码", "巡课密码", "上课密码", "教师上课地址", "助教上课地址", "主管巡课地址", "学员上课地址");
                $excelfileds = array('school_cnname', 'school_branch', 'class_enname', 'class_branch', 'hourrooms_threenumber', 'hourrooms_name', 'hourrooms_name', 'hourrooms_fromclass', 'hourrooms_maxvideo',
                    'hourrooms_date', 'hourrooms_week', 'hourrooms_starttime', 'hourrooms_endtime', 'hourrooms_chairmanpwd', 'hourrooms_assistantpwd', 'hourrooms_patrolpwd', 'hourrooms_confuserpwd', 'hourrooms_chairman_url', 'hourrooms_assistan_url', 'hourrooms_patrol_url', 'hourrooms_confuser_url');
                query_to_excel($excelheader, $outexceldate, $excelfileds, $request['editstarttime'] . '-' . $request['editendtime'] . "网课系统排课明细.xls");
                ajax_return(array('error' => 0, 'errortip' => "导出完毕!", "bakfuntion" => "okmotify"));
            }


        }
        else {
            $db_nums = $DataControl->selectOne("SELECT COUNT(h.hourrooms_id) as countnums 
                FROM tkl_lineclass_hourrooms as h,tkl_lineclass AS c where {$datawhere}");

            $allnum = $db_nums['countnums'];
            $datalist = $DataControl->dbwherePage($sql, $allnum, '10', $pageurl . '&p=', $p, '10', '2');
            $smarty->assign("pagelist", $datalist['pages']);//筛选信息
            $smarty->assign("dataList", $datalist['cont']);
            $smarty->assign("datatype", $datatype);
        }
    }

    function HourstudyView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $DataControl = $this->DataControl;
        $smarty = $this->smarty;

        if (!isset($request['p'])) {
            $p = '1';
        } else {
            $p = $request['p'];
        }
        $datawhere = "s.student_id = d.student_id AND d.hourrooms_id = h.hourrooms_id AND c.lineclass_id = h.lineclass_id AND c.company_id = '{$this->istaffer['company_id']}'";
        $pageurl = "/{$this->u}/{$this->t}?";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (s.student_cnname = '{$request['keyword']}' or s.student_branch = '{$request['keyword']}' or h.hourrooms_name = '{$request['keyword']}' or h.hourrooms_threenumber like '%{$request['keyword']}%' or c.lineclass_branch like '%{$request['keyword']}%' or c.lineclass_enname like '%{$request['keyword']}%')";
            $pageurl .= "&keyword={$request['keyword']}";
            $datatype['keyword'] = $request['keyword'];
        }

        $MonthArray = GetTheMonth(date("Y-m-d"));

        if (isset($request['starttime']) && $request['starttime'] !== '') {
            $starttime = strtotime($request['starttime']);
            $datawhere .= " and h.hourrooms_endtime >= '{$starttime}'";
            $pageurl .= "&starttime={$request['starttime']}";
            $datatype['starttime'] = $request['starttime'];
        } else {
            $starttime = strtotime($MonthArray['0']);
            $datawhere .= " and h.hourrooms_endtime >= '{$starttime}'";
            $pageurl .= "&starttime={$MonthArray['0']}";
            $datatype['starttime'] = $MonthArray['0'];
        }

        if (isset($request['endtime']) && $request['endtime'] !== '') {
            $endtime = strtotime($request['endtime']) + 3600 * 23;
            $datawhere .= " and h.hourrooms_starttime <= '{$endtime}'";
            $pageurl .= "&endtime={$request['endtime']}";
            $datatype['endtime'] = $request['endtime'];
        } else {
            $endtime = strtotime($MonthArray['1']) + 3600 * 23;
            $datawhere .= " and h.hourrooms_starttime <= '{$endtime}'";
            $pageurl .= "&endtime={$MonthArray['1']}";
            $datatype['endtime'] = $MonthArray['1'];
        }


        $sql = "SELECT s.*,d.hourroomstudy_id,d.hourrooms_id,h.hourrooms_name,h.hourrooms_threenumber,h.hourrooms_starttime,h.hourrooms_endtime,h.hourrooms_confuserpwd,
                c.lineclass_branch,c.lineclass_enname,d.hourroomstudy_checkin 
                 FROM smc_student as s,tkl_lineclass_hourroomstudy as d,tkl_lineclass_hourrooms as h,tkl_lineclass AS c
                 where {$datawhere} 
                 order by c.lineclass_id desc,h.hourrooms_starttime desc,d.hourroomstudy_id ASC";

        $db_nums = $DataControl->selectOne("SELECT COUNT(d.hourroomstudy_id) as countnums 
                FROM smc_student as s,tkl_lineclass_hourroomstudy as d,tkl_lineclass_hourrooms as h,tkl_lineclass AS c where {$datawhere}");

        $allnum = $db_nums['countnums'];
        $datalist = $DataControl->dbwherePage($sql, $allnum, '10', $pageurl . '&p=', $p, '10', '2');
        $smarty->assign("pagelist", $datalist['pages']);//筛选信息
        $smarty->assign("dataList", $datalist['cont']);
        $smarty->assign("datatype", $datatype);
    }

    //网课家长
    function LineparenterView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $DataControl = $this->DataControl;
        $smarty = $this->smarty;

        if (!isset($request['p'])) {
            $p = '1';
        } else {
            $p = $request['p'];
        }
        $datawhere = "p.parenter_id = f.parenter_id AND f.student_id = d.student_id AND d.student_id = s.student_id AND s.company_id = '{$this->istaffer['company_id']}'";
        $pageurl = "/{$this->u}/{$this->t}?";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (s.student_cnname = '{$request['keyword']}' or s.student_branch = '{$request['keyword']}' or p.parenter_mobile like '%{$request['keyword']}%')";
            $pageurl .= "&keyword={$request['keyword']}";
            $datatype['keyword'] = $request['keyword'];
        }

        $sql = "SELECT p.parenter_id,parenter_bakpass,parenter_mobile,parenter_lasttime
     ,COUNT(DISTINCT f.student_id) as studentnums FROM smc_parenter as p ,smc_student_family as f,tkl_lineclass_hourroomstudy as d, smc_student AS s
        WHERE {$datawhere} GROUP BY p.parenter_id";

        $db_nums = $DataControl->selectOne("SELECT COUNT(DISTINCT p.parenter_id) as countnums 
                FROM smc_parenter as p ,smc_student_family as f,tkl_lineclass_hourroomstudy as d, smc_student AS s where {$datawhere}");

        $allnum = $db_nums['countnums'];
        $datalist = $DataControl->dbwherePage($sql, $allnum, '10', $pageurl . '&p=', $p, '10', '2');
        $smarty->assign("pagelist", $datalist['pages']);//筛选信息
        $smarty->assign("dataList", $datalist['cont']);
        $smarty->assign("datatype", $datatype);
    }

    function ResetPasswordAction()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $parenterOne = $this->DataControl->getOne("smc_parenter", "parenter_id='{$request['parenter_id']}'");
        if (!$parenterOne) {
            $res = array('error' => '1', 'errortip' => "未查询到家长信息!", "bakfuntion" => "errormotify");
            ajax_return($res);
        }
        $data = array();
        $data['parenter_bakpass'] = substr($parenterOne['parenter_mobile'],-6);
        $data['parenter_pass'] = md5(substr($parenterOne['parenter_mobile'],-6));
        $data['parenter_updatetime'] = time();
        if ($this->DataControl->updateData("smc_parenter", "parenter_id = '{$request['parenter_id']}'", $data)) {
            $this->Recordweblog("家长管理", "密码充值成功", "重置家长{$request['parenter_id']}密码为{$data['parenter_bakpass']}");
            ajax_return(array('error' => 0, 'errortip' => "密码充值成功!", "bakfuntion" => "okmotify"));
        } else {
            ajax_return(array('error' => 1, 'errortip' => "密码充值失败!", "bakfuntion" => "errormotify"));
        }
    }

    //导入班课学员
    function ImportStudyView(){
        $request = Input('get.','','trim,addslashes');
        $this->smarty->assign("act", "schedulingHour");
        $lineclassOne = $this->DataControl->getOne("tkl_lineclass", "lineclass_id='{$request['lineclass_id']}'");
        $this->smarty->assign("lineclassOne", $lineclassOne);

        if(isset($request['hourrooms_id'])){
            $hourroomsOne = $this->DataControl->getOne("tkl_lineclass_hourrooms", "hourrooms_id='{$request['hourrooms_id']}'");
            $this->smarty->assign("hourroomsOne", $hourroomsOne);
        }

        $hourroomswhere = "lineclass_id = '{$lineclassOne['lineclass_id']}' AND company_id = '{$this->istaffer['company_id']}'";
        $hourroomsList = $this->DataControl->selectClear("SELECT * FROM tkl_lineclass_hourrooms WHERE {$hourroomswhere} ORDER BY hourrooms_lessontimes ASC");
        $this->smarty->assign("hourroomsList", $hourroomsList);
    }

    //导入网课学员名单
    function ImportroomStudyView(){
        $request = Input('post.','','trim,addslashes');
        $lineclassOne = $this->DataControl->getOne("tkl_lineclass", "lineclass_id='{$request['lineclass_id']}'");
        $this->smarty->assign("lineclassOne", $lineclassOne);

        if($request['st_lessontimes'] && $request['end_lessontimes']) {
            if ($request['st_lessontimes'] > $request['end_lessontimes']) {
                $PlayInfoVar = array();
                $PlayInfoVar['roster_mobile'] = '';
                $PlayInfoVar['error'] = "1";
                $PlayInfoVar['errortip'] = "开始课时不能大于结束课时";
                $PlayInfo[] = $PlayInfoVar;
                $this->smarty->assign("PlayInfo", $PlayInfo);
                exit;
            }
        }
        $hourroomswhere = "lineclass_id = '{$lineclassOne['lineclass_id']}' AND company_id = '{$this->istaffer['company_id']}'";

        if (isset($request['st_lessontimes']) && $request['st_lessontimes'] !== '') {
            $hourroomswhere .= " and hourrooms_lessontimes >= '{$request['st_lessontimes']}' ";
        }
        if (isset($request['end_lessontimes']) && $request['end_lessontimes'] !== '') {
            $hourroomswhere .= " and hourrooms_lessontimes <= '{$request['end_lessontimes']}' ";
        }

        if(isset($request['hourrooms_id'])){
            $hourroomswhere .= " AND hourrooms_id='{$request['hourrooms_id']}'";
        }

        $hourroomsList = $this->DataControl->selectClear("SELECT * FROM tkl_lineclass_hourrooms WHERE {$hourroomswhere} ORDER BY hourrooms_lessontimes ASC");

        if(!$hourroomsList){
            $PlayInfoVar = array();
            $PlayInfoVar['roster_mobile'] = '';
            $PlayInfoVar['error'] = "1";
            $PlayInfoVar['errortip'] = "请先给班级排课！";
            $PlayInfo[] = $PlayInfoVar;
            $this->smarty->assign("PlayInfo", $PlayInfo);
            exit;
        }

        if($_FILES['uploadFile']['name'] == '' || $_FILES['uploadFile']['error'] == '1'){
            $PlayInfoVar = array();
            $PlayInfoVar['roster_mobile'] = '';
            $PlayInfoVar['error'] = "1";
            $PlayInfoVar['errortip'] = "文件不存在";
            $PlayInfo[] = $PlayInfoVar;
            $this->smarty->assign("PlayInfo", $PlayInfo);
            exit;
        }

        $fileType = array('xls','csv','xlsx');
        $uploadfile = new \Webfile($_FILES['uploadFile'],'../static/excelfile', $size = 2097152*10,$fileType);
        $up_file = $uploadfile->upload();

        $PlayInfo = array();
        if($up_file && $uploadfile->updatastatus) {
            $ExeclName = array();
            $ExeclName['学员中文名'] = "student_cnname";
            $ExeclName['学员编号'] = "student_branch";

            $studentList = execl_to_array($up_file, $ExeclName);
            unset($studentList[0]);
            $studentPros=array();
            if ($studentList) {
                foreach ($studentList as $studentOne) {
                    if ($studentOne['student_branch'] !== '') {
                        $studentPros[] = $studentOne;
                    }
                }
            }
            if (count($studentPros) > 200) {
                ajax_return(array('error' => 1, 'errortip' => "导入学员数量不能大于200!", "bakfuntion" => "errormotify"));
            }
            if($studentPros) {
                foreach ($studentPros as $studentOne) {
                    $PlayInfoVar = array();
                    $PlayInfoVar['student_cnname'] = $studentOne['student_cnname'];
                    $PlayInfoVar['student_branch'] = $studentOne['student_branch'];
                    $studentInfo = $this->DataControl->getFieldOne("smc_student", "student_id"
                        , "company_id='{$this->istaffer['company_id']}' and student_branch='{$studentOne['student_branch']}'");
                    if(!$studentInfo){
                        $PlayInfoVar['error'] = "1";
                        $PlayInfoVar['errortip'] = "学员信息本集团不存在,不可导入";
                        $PlayInfo[] = $PlayInfoVar;
                        continue;
                    }
                    foreach ($hourroomsList as $hourroomsOne){
                        if ($this->DataControl->getFieldOne("tkl_lineclass_hourroomstudy", "hourroomstudy_id"
                            , "student_id='{$studentInfo['student_id']}'
                             and lineclass_id='{$lineclassOne['lineclass_id']}' and hourrooms_id='{$hourroomsOne['hourrooms_id']}'")) {
                            $PlayInfoVar['hourrooms_name'] = $hourroomsOne['hourrooms_name'];
                            $PlayInfoVar['error'] = "1";
                            $PlayInfoVar['errortip'] = "学员信息课时已导入已存在,不可重复导入";
                            $PlayInfo[] = $PlayInfoVar;
                            continue;
                        } else {
                            $data = array();
                            $data['student_id'] = $studentInfo['student_id'];
                            $data['lineclass_id'] = $lineclassOne['lineclass_id'];
                            $data['hourrooms_id'] = $hourroomsOne['hourrooms_id'];
                            $data['hourroomstudy_updatatime'] = time();
                            $data['hourroomstudy_createtime'] = time();
                            if ($this->DataControl->insertData("tkl_lineclass_hourroomstudy", $data)) {
                                $PlayInfoVar['hourrooms_name'] = $hourroomsOne['hourrooms_name'];
                                $PlayInfoVar['error'] = "0";
                                $PlayInfoVar['errortip'] = "导入成功";
                            } else {
                                $PlayInfoVar['hourrooms_name'] = '--';
                                $PlayInfoVar['error'] = "1";
                                $PlayInfoVar['errortip'] = "导入失败";
                            }
                        }
                        $PlayInfo[] = $PlayInfoVar;
                    }
                }
            }
            else{
                $PlayInfoVar = array();
                $PlayInfoVar['roster_mobile'] = '';
                $PlayInfoVar['error'] = "1";
                $PlayInfoVar['errortip'] = "没有获得导入的数据";
                $PlayInfo[] = $PlayInfoVar;
            }
        }
        else{
            $PlayInfoVar = array();
            $PlayInfoVar['roster_mobile'] = '';
            $PlayInfoVar['error'] = "1";
            $PlayInfoVar['errortip'] = "文件导入失败".$uploadfile->last_error.$uploadfile->updatastatus;
            $PlayInfo[] = $PlayInfoVar;
        }

        $this->Recordweblog("班课管理",$this->c,"集团:{$this->istaffer['company_id']},班课:{$lineclassOne['lineclass_branch']},导入班课学员失败");

        $this->smarty->assign("PlayInfo", $PlayInfo);
    }

    function schedulingHourView(){
        $request = Input('get.', '', 'trim,addslashes');
        $this->smarty->assign("act", "schedulingHour");
        $lineclassOne = $this->DataControl->getOne("tkl_lineclass", "lineclass_id='{$request['lineclass_id']}'");
        $this->smarty->assign("lineclassOne", $lineclassOne);
    }

    function schedulingHourAction(){
        $request = Input('post.', '', 'trim,addslashes');
        $lineclassOne = $this->DataControl->getOne("tkl_lineclass", "lineclass_id='{$request['lineclass_id']}'");
        $this->smarty->assign("lineclassOne", $lineclassOne);

        $time_start = strtotime($request['hour_stdate']);
        $time_end = strtotime($request['hour_enddate']);
        $date = array();
        while ($time_start <= $time_end) {
            $date[] = date('Y-m-d', $time_start);
            $time_start = strtotime('+1 day', $time_start);
        }
        if (count($date) < 0) {
            $this->error = 1;
            $this->errortip = "请选择正确的日期";die;
            return false;
        }

        $endDate = '';
        $stDate = $lineclassOne['lineclass_stdate'];
        $hourOne = array();
        $hourOne['company_id'] = $this->istaffer['company_id'];
        $hourOne['lineclass_id'] = $request['lineclass_id'];
        $hourOne['hourrooms_createtime'] = time();

        //跳过周末
        $arrWeek = explode(',',$request['hour_weeklimit']);
        $scheduDate = array();
        for ($i = 0; $i < count($date); $i++) {
            if (in_array(date('w', strtotime($date[$i])), $arrWeek) && count($scheduDate) < $request['hourroomsnum']) {
                $scheduDate[] = $date[$i];
            }
        }

        $hournum = $this->DataControl->selectOne("select count(hourrooms_id) as num from tkl_lineclass_hourrooms where lineclass_id = '{$request['lineclass_id']}' ");

        $hourlesson = $hournum['num'];
        foreach ($scheduDate as $day) {
            $hourlesson++;
            $hourOne['hourrooms_lessontimes'] = $hourlesson;
            $hourOne['hourrooms_name'] = 'Lesson' . ' ' .$hourlesson;
            $hourOne['hourrooms_starttime'] = strtotime($day . $request['starttime']);
            $hourOne['hourrooms_endtime'] = strtotime($day . $request['endtime']);
            $this->DataControl->insertData("tkl_lineclass_hourrooms", $hourOne);
            if ($hourlesson == 1) {
                $stDate = $day;
            }
            $endDate = $day;
        }

        //调整顺序
        $sql = "call RefreshLineClass('{$request['lineclass_id']}')";
        $this->DataControl->query($sql);

        $data = array();
        $data['lineclass_stdate'] = $stDate;
        $data['lineclass_enddate'] = $endDate;
        $data['lineclass_updatatime'] = time();
        $this->DataControl->updateData("tkl_lineclass", "lineclass_id='{$request['lineclass_id']}'", $data);

        $this->Recordweblog("班课管理", "新增排课", "新增排课数据");
        ajax_return(array('error' => 0, 'errortip' => "提交成功!", "bakfuntion" => "successFromTip", "bakurl" => "/{$this->u}/Hourrooms?lineclass_id={$request['lineclass_id']}"));
    }

    function CreateRoomsAction()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $dataVar = $this->DataControl->getOne("tkl_lineclass_hourrooms", "hourrooms_id='{$request['hourrooms_id']}'");
        if ($dataVar['hourrooms_threenumber'] !== '') {
            $res = array('error' => '1', 'errortip' => '请勿重复生成!', "bakfuntion" => "errormotify");
            ajax_return($res);
        }
        $TalkcloudModel = new \Model\Tlk\TalkcloudModel($this->FYCOMID);
        $putArray = array();
        $putArray['roomname'] = $dataVar['hourrooms_name'];
        $putArray['roomtype'] = '3';
        $putArray['fromclass'] = '0';
        $putArray['starttime'] = $dataVar['hourrooms_starttime'];
        $putArray['endtime'] = $dataVar['hourrooms_endtime'];
        $putArray['chairmanpwd'] = 't123456';
        $putArray['assistantpwd'] = 'z123456';
        $putArray['patrolpwd'] = 'x123456';
        $putArray['passwordrequired'] = 1;
        $putArray['confuserpwd'] = rand(11111, 999999);
        $putArray['autoopenav'] = '1';
        $putArray['maxvideo'] = '7';
        $putArray['sharedesk'] = '1';
        $putArray['sidelineuserpwd'] = rand(11111, 999999);
        $modelArray = $TalkcloudModel->exroomCreate($putArray);
        if ($modelArray['result'] == '0') {
            $data = array();
            $data['hourrooms_threenumber'] = $modelArray['serial'];
            $data['hourrooms_type'] = '3';
            $data['hourrooms_chairmanpwd'] = 't123456';
            $data['hourrooms_assistantpwd'] = 'z123456';
            $data['hourrooms_patrolpwd'] = 'x123456';
            $data['hourrooms_passwordrequired'] = 1;
            $data['hourrooms_confuserpwd'] = $putArray['confuserpwd'];
            $data['hourrooms_autoopenav'] = '1';
            $data['hourrooms_maxvideo'] = '13';
            $data['hourrooms_sharedesk'] = '1';
            $data['hourrooms_updatatime'] = time();
            $data['hourrooms_issync'] = "1";
            if ($this->DataControl->updateData("tkl_lineclass_hourrooms", "hourrooms_id = '{$request['hourrooms_id']}'", $data)) {
                $ediInfo = http_build_query($data);
                $this->Recordweblog("教室管理", "创建网课课时", "班课网课信息创建教室成功，ID:{$request['hourrooms_id']},{$ediInfo}");
                ajax_return(array('error' => 0, 'errortip' => "班课网课课时创建教室成功!", "bakfuntion" => "okmotify"));
            } else {
                ajax_return(array('error' => 1, 'errortip' => "班课网课课时创建教室成功!", "bakfuntion" => "errormotify"));
            }
        } else {
            $errortipOne = $this->DataControl->getOne("tkl_errortip", "errortip_code='{$modelArray['result']}'");
            ajax_return(array('error' => 1, 'errortip' => "网课教室创建失败，错误码：{$errortipOne['errortip_txt']}!", 'putArray' => $putArray, 'result' => $modelArray, "bakfuntion" => "errormotify"));
        }
    }

    function hourroomsLookView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $dataVar = $this->DataControl->getOne("tkl_lineclass_hourrooms", "hourrooms_id='{$request['hourrooms_id']}'");

        $TalkcloudModel = new \Model\Tlk\TalkcloudModel($this->FYCOMID);

        $putArray = array();
        $putArray['serial'] = $dataVar['hourrooms_threenumber'];
        $putArray['fromclass'] = $dataVar['hourrooms_fromclass'];
        $Exroominfo = $TalkcloudModel->getExroominfo($putArray);
        $dataVar['hourrooms_starttime'] = $Exroominfo['starttime'];
        $dataVar['hourrooms_endtime'] = $Exroominfo['endtime'];


        $putArray = array();
        $putArray['serial'] = $dataVar['hourrooms_threenumber'];
        $putArray['fromclass'] = $dataVar['hourrooms_fromclass'];
        $putArray['type'] = 1; //选填 参加用户类型 0: 当前在线人数  1：登录人数
        $modelArray = $TalkcloudModel->exroomonLinenum($putArray);
        if ($modelArray['result'] == '0') {
            $dataVar['loginnums'] = $modelArray['room'][0]['num'];
        } else {
            $dataVar['loginnums'] = 0;
        }
        $putArray = array();
        $putArray['serial'] = $dataVar['hourrooms_threenumber'];
        $putArray['fromclass'] = $dataVar['hourrooms_fromclass'];
        $putArray['type'] = 0; //选填 参加用户类型 0: 当前在线人数  1：登录人数
        $modelArray = $TalkcloudModel->exroomonLinenum($putArray);
        if ($modelArray['result'] == '0') {
            $dataVar['linenums'] = $modelArray['room'][0]['num'];
        } else {
            $dataVar['linenums'] = 0;
        }
        $this->smarty->assign("dataVar", $dataVar);


        $putArray = array();
        $putArray['serial'] = $dataVar['hourrooms_threenumber'];
        $putArray['fromclass'] = $dataVar['hourrooms_fromclass'];
        $modelArray = $TalkcloudModel->exgetonLineuser($putArray);
        if ($modelArray['result'] == '0') {
            $this->smarty->assign("onlineUser", $modelArray['onlineuser']);
        }
    }

    function hourEditView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $DataControl = $this->DataControl;
        $smarty = $this->smarty;

        $smarty->assign("act", "hourEdit");
        $dataVar = $DataControl->getOne("tkl_lineclass_hourrooms", "hourrooms_id='{$request['hourrooms_id']}'");
        $smarty->assign("dataVar", $dataVar);

        $this->Viewhtm = $this->router->getController() . "/" . "hourEdit.htm";
    }

    function hourEditAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $DataControl = $this->DataControl;

        $data = array();
        $data['hourrooms_starttime'] = strtotime($request['startday'] . " " . $request['starttime']);
        $data['hourrooms_endtime'] = strtotime($request['endday'] . " " . $request['endtime']);
        $data['hourrooms_updatatime'] = time();
        if ($DataControl->updateData("tkl_lineclass_hourrooms", "hourrooms_id = '{$request['hourrooms_id']}'", $data)) {
            $ediInfo = http_build_query($data);
            $this->Recordweblog("班课管理", "班课教室编辑", "修改班课教室数据，ID:{$request['hourrooms_id']},{$ediInfo}");
            ajax_return(array('error' => 0, 'errortip' => "网课教室信息修改成功!", "bakfuntion" => "successFromTip"));
        } else {
            ajax_return(array('error' => 1, 'errortip' => "网课教室信息修改失败!", "bakfuntion" => "dangerFromTip"));
        }
    }

    function hourroomsEditView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $DataControl = $this->DataControl;
        $smarty = $this->smarty;

        $smarty->assign("act", "hourroomsEdit");
        $dataVar = $DataControl->getOne("tkl_lineclass_hourrooms", "hourrooms_id='{$request['hourrooms_id']}'");
        $smarty->assign("dataVar", $dataVar);

        $this->Viewhtm = $this->router->getController() . "/" . "hourroomsEdit.htm";
    }

    function hourroomsEditAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $DataControl = $this->DataControl;
        $dataVar = $this->DataControl->getOne("tkl_lineclass_hourrooms", "hourrooms_id='{$request['hourrooms_id']}'");

        if ($request['hourrooms_passwordrequired'] == '1' && trim($request['hourrooms_confuserpwd']) == '') {
            $res = array('error' => '1', 'errortip' => '请设定学员上课密码!', "bakfuntion" => "errormotify");
            ajax_return($res);
        }

        $TalkcloudModel = new \Model\Tlk\TalkcloudModel($this->FYCOMID);
        $putArray = array();
        $putArray['roomname'] = $dataVar['hourrooms_name'];
        $putArray['serial'] = $request['hourrooms_threenumber'];
        $putArray['fromclass'] = $dataVar['hourrooms_fromclass'];
        $putArray['roomtype'] = $dataVar['hourrooms_type'];
        $putArray['starttime'] = strtotime($request['startday'] . " " . $request['starttime']);
        $putArray['endtime'] = strtotime($request['endday'] . " " . $request['endtime']);
        $putArray['chairmanpwd'] = $request['hourrooms_chairmanpwd'];
        $putArray['assistantpwd'] = $request['hourrooms_assistantpwd'];
        $putArray['patrolpwd'] = $request['hourrooms_patrolpwd'];
        $putArray['passwordrequired'] = $request['hourrooms_passwordrequired'];
        $putArray['confuserpwd'] = $request['hourrooms_confuserpwd'];
        $putArray['autoopenav'] = $request['hourrooms_autoopenav'];
        $putArray['maxvideo'] = $request['hourrooms_maxvideo'];
        $putArray['sharedesk'] = $request['hourrooms_sharedesk'];
        $putArray['sidelineuserpwd'] = rand(11111, 999999);
        $modelArray = $TalkcloudModel->exroomModify($putArray);
        if ($modelArray['result'] == '0') {
            $data = array();
            $data['hourrooms_starttime'] = strtotime($request['startday'] . " " . $request['starttime']);
            $data['hourrooms_endtime'] = strtotime($request['endday'] . " " . $request['endtime']);
            $data['hourrooms_chairmanpwd'] = $request['hourrooms_chairmanpwd'];
            $data['hourrooms_assistantpwd'] = $request['hourrooms_assistantpwd'];
            $data['hourrooms_patrolpwd'] = $request['hourrooms_patrolpwd'];
            $data['hourrooms_passwordrequired'] = $request['hourrooms_passwordrequired'];
            $data['hourrooms_confuserpwd'] = $request['hourrooms_confuserpwd'];
            $data['hourrooms_autoopenav'] = $request['hourrooms_autoopenav'];
            $data['hourrooms_maxvideo'] = $request['hourrooms_maxvideo'];
            $data['hourrooms_sharedesk'] = $request['hourrooms_sharedesk'];
            $data['hourrooms_updatatime'] = time();
            $data['hourrooms_issync'] = "1";
            if ($DataControl->updateData("tkl_lineclass_hourrooms", "hourrooms_id = '{$request['hourrooms_id']}'", $data)) {
                $ediInfo = http_build_query($data);
                $this->Recordweblog("班课管理", "班课教室编辑", "修改班课教室数据，ID:{$request['hourrooms_id']},{$ediInfo}");
                ajax_return(array('error' => 0, 'errortip' => "网课教室信息修改成功!", "bakfuntion" => "successFromTip"));
            } else {
                ajax_return(array('error' => 1, 'errortip' => "网课教室信息修改失败!", "bakfuntion" => "dangerFromTip"));
            }
        } else {
            $errortipOne = $DataControl->getOne("tkl_errortip", "errortip_code='{$modelArray['result']}'");
            ajax_return(array('error' => 1, 'errortip' => "网课教室修改失败，错误码：{$errortipOne['errortip_txt']}!", "bakfuntion" => "dangerFromTip"));
        }
    }

    //添加全国班课
    function AddLineclassView()
    {
        $smarty = $this->smarty;
        $smarty->assign("act", "AddLineclass");

        $courseList = $this->DataControl->selectClear("SELECT c.* FROM smc_course as c 
       where c.company_id = '{$this->istaffer['company_id']}' AND c.coursetype_id = '79699'");
        $smarty->assign("courseList", $courseList);


        $this->Viewhtm = $this->router->getController() . "/" . "EditLineclass.htm";
    }

    //新增班课管理
    function AddLineclassAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $DataControl = $this->DataControl;

        if ($this->DataControl->getOne('tkl_lineclass', "company_id = '{$this->istaffer['company_id']}' and lineclass_enname='{$request['lineclass_enname']}'")) {
            $res = array('error' => '1', 'errortip' => '班课别名已存在，建议加上相关标识!', "bakfuntion" => "errormotify");
            ajax_return($res);
        }

        if ($request['lineclass_stdate'] == '' || trim($request['lineclass_enddate']) == '') {
            $res = array('error' => '1', 'errortip' => '请设定班课规划开始结束日期!', "bakfuntion" => "errormotify");
            ajax_return($res);
        }

        $data = array();
        $data['company_id'] = $this->istaffer['company_id'];
        $like = date("Ymd", time());
        do {
            $classInfo = $this->DataControl->selectOne("select lineclass_branch from tkl_lineclass 
                        where lineclass_branch like '{$like}%' AND LENGTH(lineclass_branch) = '14' order by lineclass_branch DESC limit 0,1");
            if ($classInfo) {
                $data['lineclass_branch'] = $classInfo['lineclass_branch'] + 1;
            } else {
                $data['lineclass_branch'] = $like . '000001';
            }
        } while ($this->DataControl->getFieldOne("tkl_lineclass", "lineclass_id", "lineclass_branch='{$data['lineclass_branch']}'"));
        $data['lineclass_cnname'] = $request['lineclass_cnname'];
        $data['lineclass_enname'] = $request['lineclass_enname'];
        $data['course_id'] = $request['course_id'];
        $data['lineclass_stdate'] = $request['lineclass_stdate'];
        $data['lineclass_enddate'] = $request['lineclass_enddate'];
        $data['lineclass_createtime'] = time();
        $data['lineclass_updatatime'] = time();
        if ($DataControl->insertData("tkl_lineclass", $data)) {
            $this->Recordweblog("班课管理", "创建班课", "新增班课数据");
            ajax_return(array('error' => 0, 'errortip' => "提交成功!", "bakfuntion" => "successFromTip", "bakurl" => "/{$this->u}/Lineclass"));
        } else {
            ajax_return(array('error' => 1, 'errortip' => "提交失败!", "bakfuntion" => "dangerFromTip"));
        }
    }

    function DelhourroomstudyAction()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $hourroomstudyOne = $this->DataControl->getOne("tkl_lineclass_hourroomstudy", "hourroomstudy_id='{$request['hourroomstudy_id']}'");
        if($hourroomstudyOne['hourroomstudy_checkin'] != '0'){
            ajax_return(array('error' => 1, 'errortip' => "已考勤学员禁止删除!", "bakfuntion" => "dangerFromTip"));
        }
        $lineclassOne = $this->DataControl->getOne("tkl_lineclass", "lineclass_id='{$hourroomstudyOne['lineclass_id']}'");
        if ($this->DataControl->delData('tkl_lineclass_hourroomstudy', "hourroomstudy_id='{$request['hourroomstudy_id']}'")) {
            $this->Recordweblog("全国班课管理", "全国班课管理", "删除学员约课数据,全国班课编号：{$lineclassOne['lineclass_branch']}，SID:{$hourroomstudyOne['student_id']}");
            ajax_return(array('error' => 0, 'errortip' => "提交成功!", "bakfuntion" => "okmotify"));
        } else {
            ajax_return(array('error' => 1, 'errortip' => "教是删除失败!", "bakfuntion" => "errormotify"));
        }
    }

    function DelLineclassAction()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $lineclassOne = $this->DataControl->getOne("tkl_lineclass", "lineclass_id='{$request['lineclass_id']}'");
        $data = array();
        $data['lineclass_isdel'] = '1';
        if ($this->DataControl->updateData('tkl_lineclass', "lineclass_id='{$request['lineclass_id']}'", $data)) {
            $this->Recordweblog("全国班课管理", "全国班课管理", "删除全国班课数据,全国班课编号：{$lineclassOne['lineclass_branch']}，ID:{$lineclassOne['lineclass_id']}");
            ajax_return(array('error' => 0, 'errortip' => "提交成功!", "bakfuntion" => "okmotify"));
        } else {
            ajax_return(array('error' => 1, 'errortip' => "教是删除失败!", "bakfuntion" => "errormotify"));
        }
    }

    function EditLineclassView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $DataControl = $this->DataControl;
        $smarty = $this->smarty;

        $smarty->assign("act", "EditLineclass");
        $dataVar = $DataControl->getOne("tkl_lineclass", "lineclass_id='{$request['lineclass_id']}'");
        $smarty->assign("dataVar", $dataVar);

        $courseList = $this->DataControl->selectClear("SELECT c.* FROM smc_course as c 
       where c.company_id = '{$this->istaffer['company_id']}' AND c.coursetype_id = '79699'");
        $smarty->assign("courseList", $courseList);

        $this->Viewhtm = $this->router->getController() . "/" . "EditLineclass.htm";
    }

    function EditLineclassAction(){
        $request = Input('post.', '', 'trim,addslashes');
        $lineclassOne = $this->DataControl->getOne("tkl_lineclass", "lineclass_id='{$request['lineclass_id']}'");
        if ($this->DataControl->getOne('tkl_lineclass', "company_id = '{$this->istaffer['company_id']}' and lineclass_enname='{$request['lineclass_enname']}' and lineclass_id <> '{$request['lineclass_id']}'")) {
            $res = array('error' => '1', 'errortip' => '班课别名已存在，建议加上相关标识!', "bakfuntion" => "errormotify");
            ajax_return($res);
        }
        if(trim($request['lineclass_stdate']) > trim($request['lineclass_enddate']) ){
            $res = array('error' => '1', 'errortip' => '开始时间不能大于结束时间!', "bakfuntion" => "errormotify");
            ajax_return($res);
        }

        if ($request['lineclass_stdate'] == '' || trim($request['lineclass_enddate']) == '') {
            $res = array('error' => '1', 'errortip' => '请设定班课规划开始结束日期!', "bakfuntion" => "errormotify");
            ajax_return($res);
        }

        $data = array();
        $data['company_id'] = $this->istaffer['company_id'];
        $data['course_id'] = $request['course_id'];
        $data['lineclass_cnname'] = $request['lineclass_cnname'];
        $data['lineclass_enname'] = $request['lineclass_enname'];
        $data['lineclass_stdate'] = $request['lineclass_stdate'];
        $data['lineclass_enddate'] = $request['lineclass_enddate'];
        $data['lineclass_updatatime'] = time();
        if ($this->DataControl->updateData("tkl_lineclass", "lineclass_id = '{$lineclassOne['lineclass_id']}'", $data)) {
            $ediInfo = http_build_query($data);
            $this->Recordweblog("班课管理", "班课编辑", "修改班课数据，ID:{$lineclassOne['lineclass_id']},{$ediInfo}");
            ajax_return(array('error' => 0, 'errortip' => "班课信息修改成功!", "bakfuntion" => "successFromTip"));
        } else {
            ajax_return(array('error' => 1, 'errortip' => "班课信息修改失败!", "bakfuntion" => "dangerFromTip"));
        }
    }

    //班课列表 - 某班课学员列表
    function classOneStudyView(){
        $request = Input('get.', '', 'trim,addslashes');
        $DataControl = $this->DataControl;
        $smarty = $this->smarty;

        if (!isset($request['p'])) {
            $p = '1';
        } else {
            $p = $request['p'];
        }
        $datawhere = "a.company_id = '{$this->istaffer['company_id']}'
                    and a.hourrooms_id = b.hourrooms_id 
                    and a.lineclass_id = c.lineclass_id 
                    and b.student_id = d.student_id and a.company_id = d.company_id 
                    and d.student_id = e.student_id and e.study_isreading = '1' 
                    and e.school_id = f.school_id ";
        $pageurl = "/{$this->u}/{$this->t}?";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (d.student_cnname = '{$request['keyword']}' or d.student_branch = '{$request['keyword']}' or a.hourrooms_name = '{$request['keyword']}' or a.hourrooms_threenumber like '%{$request['keyword']}%' or c.lineclass_branch like '%{$request['keyword']}%' or c.lineclass_enname like '%{$request['keyword']}%')";
            $pageurl .= "&keyword={$request['keyword']}";
            $datatype['keyword'] = $request['keyword'];
        }
        if (isset($request['lineclass_id']) && $request['lineclass_id'] !== '') {
            $datawhere .= " and a.lineclass_id = '{$request['lineclass_id']}' ";
            $pageurl .= "&lineclass_id={$request['lineclass_id']}";
            $datatype['lineclass_id'] = $request['lineclass_id'];
        }else{
            ajax_return(array('error' => 1, 'errortip' => "参数有误!", "bakfuntion" => "errormotify"));
        }

        $MonthArray = GetTheMonth(date("Y-m-d"));

        if (isset($request['starttime']) && $request['starttime'] !== '') {
            $starttime = strtotime($request['starttime']);
            $datawhere .= " and a.hourrooms_endtime >= '{$starttime}'";
            $pageurl .= "&starttime={$request['starttime']}";
            $datatype['starttime'] = $request['starttime'];
        } else {
            $starttime = strtotime($MonthArray['0']);
            $datawhere .= " and a.hourrooms_endtime >= '{$starttime}'";
            $pageurl .= "&starttime={$MonthArray['0']}";
            $datatype['starttime'] = $MonthArray['0'];
        }

        if (isset($request['endtime']) && $request['endtime'] !== '') {
            $endtime = strtotime($request['endtime']) + 3600 * 23;
            $datawhere .= " and a.hourrooms_starttime <= '{$endtime}'";
            $pageurl .= "&endtime={$request['endtime']}";
            $datatype['endtime'] = $request['endtime'];
        } else {
            $endtime = strtotime($MonthArray['1']) + 3600 * 23;
            $datawhere .= " and a.hourrooms_starttime <= '{$endtime}'";
            $pageurl .= "&endtime={$MonthArray['1']}";
            $datatype['endtime'] = $MonthArray['1'];
        }

        $sql = "
            select * from (
                SELECT a.hourrooms_starttime,a.hourrooms_endtime,a.hourrooms_confuserpwd,b.hourroomstudy_id,b.hourroomstudy_checkin,f.school_cnname,f.school_branch,a.hourrooms_name,a.hourrooms_threenumber,c.lineclass_branch,c.lineclass_cnname,c.lineclass_enname,d.student_cnname,d.student_enname,d.student_branch,b.student_id,a.lineclass_id,a.hourrooms_id 
                    ,(select count(o.hourrooms_id) from tkl_lineclass_hourrooms as o WHERE a.lineclass_id = o.lineclass_id) as allhour 
                    ,(select count(o.hourrooms_id) from tkl_lineclass_hourrooms as o,tkl_lineclass_hourroomstudy as p WHERE a.lineclass_id = o.lineclass_id and a.hourrooms_id = o.hourrooms_id and o.hourrooms_id = p.hourrooms_id and b.student_id = p.student_id and  p.hourrooms_id > 0) as succhour
                    
                    ,(select o.hourrooms_name from tkl_lineclass_hourrooms as o,tkl_lineclass_hourroomstudy as p WHERE a.lineclass_id = o.lineclass_id and o.lineclass_id = p.lineclass_id and b.student_id = p.student_id order by hourrooms_lessontimes ASC limit 0,1) as first_hourrooms_name 
                    ,(select o.hourrooms_name from tkl_lineclass_hourrooms as o,tkl_lineclass_hourroomstudy as p WHERE a.lineclass_id = o.lineclass_id and o.lineclass_id = p.lineclass_id  and b.student_id = p.student_id order by hourrooms_lessontimes DESC limit 0,1) as last_hourrooms_name 
                    ,(select count(p.hourroomstudy_id) from tkl_lineclass_hourroomstudy as p WHERE a.lineclass_id = p.lineclass_id and b.student_id = p.student_id and p.hourroomstudy_checkin = '1'  limit 0,1) as arrivenum
                    ,(select count(p.hourroomstudy_id) from tkl_lineclass_hourroomstudy as p WHERE a.lineclass_id = p.lineclass_id and b.student_id = p.student_id and p.hourroomstudy_checkin = '-1'  limit 0,1) as lacknum
                    
                FROM tkl_lineclass_hourrooms as a,tkl_lineclass_hourroomstudy as b,tkl_lineclass AS c,smc_student as d,smc_student_study as e ,smc_school as f 
                WHERE {$datawhere}
                GROUP BY b.student_id 
            ) as x order by x.hourrooms_starttime,x.hourroomstudy_id ASC
        ";

        $db_nums = $DataControl->selectOne("
            select COUNT(x.hourroomstudy_id) as countnums from (
                SELECT b.hourroomstudy_id 
                FROM tkl_lineclass_hourrooms as a,tkl_lineclass_hourroomstudy as b,tkl_lineclass AS c,smc_student as d,smc_student_study as e ,smc_school as f 
                WHERE {$datawhere}
                GROUP BY b.student_id 
            ) as x  ");

        $allnum = $db_nums['countnums'];
        $datalist = $DataControl->dbwherePage($sql, $allnum, '10', $pageurl . '&p=', $p, '10', '2');
        $smarty->assign("pagelist", $datalist['pages']);//筛选信息
        $smarty->assign("dataList", $datalist['cont']);
        $smarty->assign("datatype", $datatype);
    }

    //课时列表 - 某课时学员列表
    function hourOneStudyView(){
        $request = Input('get.', '', 'trim,addslashes');
        $DataControl = $this->DataControl;
        $smarty = $this->smarty;

        if (!isset($request['p'])) {
            $p = '1';
        } else {
            $p = $request['p'];
        }
        $datawhere = "a.company_id = '{$this->istaffer['company_id']}'
                    and a.hourrooms_id = b.hourrooms_id 
                    and a.lineclass_id = c.lineclass_id 
                    and b.student_id = d.student_id and a.company_id = d.company_id ";
//        and d.student_id = e.student_id and e.study_isreading = '1'
//        and e.school_id = f.school_id
        $pageurl = "/{$this->u}/{$this->t}?";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (d.student_cnname = '{$request['keyword']}' or d.student_branch = '{$request['keyword']}' or a.hourrooms_name = '{$request['keyword']}' or a.hourrooms_threenumber like '%{$request['keyword']}%' or c.lineclass_branch like '%{$request['keyword']}%' or c.lineclass_enname like '%{$request['keyword']}%')";
            $pageurl .= "&keyword={$request['keyword']}";
            $datatype['keyword'] = $request['keyword'];
        }
        if (isset($request['hourrooms_id']) && $request['hourrooms_id'] !== '') {
            $datawhere .= " and a.hourrooms_id = '{$request['hourrooms_id']}' ";
            $pageurl .= "&hourrooms_id={$request['hourrooms_id']}";
            $datatype['hourrooms_id'] = $request['hourrooms_id'];
        }else{
            ajax_return(array('error' => 1, 'errortip' => "参数有误!", "bakfuntion" => "errormotify"));
        }

        $MonthArray = GetTheMonth(date("Y-m-d"));

        if (isset($request['starttime']) && $request['starttime'] !== '') {
            $starttime = strtotime($request['starttime']);
            $datawhere .= " and a.hourrooms_endtime >= '{$starttime}'";
            $pageurl .= "&starttime={$request['starttime']}";
            $datatype['starttime'] = $request['starttime'];
        } else {
            $starttime = strtotime($MonthArray['0']);
            $datawhere .= " and a.hourrooms_endtime >= '{$starttime}'";
            $pageurl .= "&starttime={$MonthArray['0']}";
            $datatype['starttime'] = $MonthArray['0'];
        }

        if (isset($request['endtime']) && $request['endtime'] !== '') {
            $endtime = strtotime($request['endtime']) + 3600 * 23;
            $datawhere .= " and a.hourrooms_starttime <= '{$endtime}'";
            $pageurl .= "&endtime={$request['endtime']}";
            $datatype['endtime'] = $request['endtime'];
        } else {
            $endtime = strtotime($MonthArray['1']) + 3600 * 23;
            $datawhere .= " and a.hourrooms_starttime <= '{$endtime}'";
            $pageurl .= "&endtime={$MonthArray['1']}";
            $datatype['endtime'] = $MonthArray['1'];
        }

        $sql = "
            select * from (
                SELECT a.hourrooms_starttime,a.hourrooms_endtime,a.hourrooms_confuserpwd,b.hourroomstudy_id,b.hourroomstudy_checkin,a.hourrooms_name,a.hourrooms_threenumber,c.lineclass_branch,c.lineclass_cnname,c.lineclass_enname,d.student_cnname,d.student_branch,b.student_id,a.lineclass_id,a.hourrooms_id
                    ,(select count(o.hourrooms_id) from tkl_lineclass_hourrooms as o WHERE a.lineclass_id = o.lineclass_id) as allhour
                    ,(select count(o.hourrooms_id) from tkl_lineclass_hourrooms as o,tkl_lineclass_hourroomstudy as p WHERE a.lineclass_id = o.lineclass_id and a.hourrooms_id = o.hourrooms_id and o.hourrooms_id = p.hourrooms_id and b.student_id = p.student_id ) as succhour
                FROM tkl_lineclass_hourrooms as a,tkl_lineclass_hourroomstudy as b,tkl_lineclass AS c,smc_student as d
                WHERE {$datawhere}
                GROUP BY b.student_id 
            ) as x order by x.hourrooms_starttime,x.hourroomstudy_id ASC
        ";
//        echo $sql;die;
        //f.school_cnname,f.school_branch,
        //,smc_student_study as e ,smc_school as f

        $db_nums = $DataControl->selectOne("
            select COUNT(x.hourroomstudy_id) as countnums from (
                SELECT b.hourroomstudy_id 
                FROM tkl_lineclass_hourrooms as a,tkl_lineclass_hourroomstudy as b,tkl_lineclass AS c,smc_student as d
                WHERE {$datawhere}
                GROUP BY b.student_id 
            ) as x  ");
        //,smc_student_study as e ,smc_school as f

        $allnum = $db_nums['countnums'];
        $datalist = $DataControl->dbwherePage($sql, $allnum, '10', $pageurl . '&p=', $p, '10', '2');
        $smarty->assign("pagelist", $datalist['pages']);//筛选信息
        $smarty->assign("dataList", $datalist['cont']);
        $smarty->assign("datatype", $datatype);

        $hourroomsOne = $this->DataControl->selectOne("select a.hourrooms_id,a.hourrooms_name,a.lineclass_id,b.lineclass_cnname,b.lineclass_branch from tkl_lineclass_hourrooms as a,tkl_lineclass as b where a.hourrooms_id = '{$request['hourrooms_id']}' and a.lineclass_id = b.lineclass_id  ");
        $smarty->assign("hourroomsOne", $hourroomsOne);
    }

    //课时列表 - 某课时学员列表 -- 导出
    function ExportHourOneStudyAction()
    {
        $request = Input('post.', '', 'trim,addslashes');

        $datawhere = "a.company_id = '{$this->istaffer['company_id']}'
                    and a.hourrooms_id = b.hourrooms_id 
                    and a.lineclass_id = c.lineclass_id 
                    and b.student_id = d.student_id and a.company_id = d.company_id 
                    and d.student_id = e.student_id and e.study_isreading = '1' 
                    and e.school_id = f.school_id ";
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (d.student_cnname = '{$request['keyword']}' or d.student_branch = '{$request['keyword']}' or a.hourrooms_name = '{$request['keyword']}' or a.hourrooms_threenumber like '%{$request['keyword']}%' or c.lineclass_branch like '%{$request['keyword']}%' or c.lineclass_enname like '%{$request['keyword']}%')";
        }
        if (isset($request['hourrooms_id']) && $request['hourrooms_id'] !== '') {
            $datawhere .= " and a.hourrooms_id = '{$request['hourrooms_id']}' ";
        }
        if (isset($request['starttime']) && $request['starttime'] !== '') {
            $starttime = strtotime($request['starttime']);
            $datawhere .= " and a.hourrooms_endtime >= '{$starttime}'";
        }
        if (isset($request['endtime']) && $request['endtime'] !== '') {
            $endtime = strtotime($request['endtime']) + 3600 * 23;
            $datawhere .= " and a.hourrooms_starttime <= '{$endtime}'";
        }

        $dateexcelarray = $this->DataControl->selectClear("select * from (
                SELECT a.hourrooms_starttime,a.hourrooms_endtime,a.hourrooms_confuserpwd,b.student_id,b.hourroomstudy_id,b.hourroomstudy_checkin,f.school_cnname,f.school_branch,a.hourrooms_name,a.hourrooms_threenumber,c.lineclass_branch,c.lineclass_cnname,c.lineclass_enname,d.student_cnname,d.student_branch
                    ,(select count(o.hourrooms_id) from tkl_lineclass_hourrooms as o WHERE a.lineclass_id = o.lineclass_id) as allhour
                    ,(select count(o.hourrooms_id) from tkl_lineclass_hourrooms as o,tkl_lineclass_hourroomstudy as p WHERE a.lineclass_id = o.lineclass_id and a.hourrooms_id = o.hourrooms_id and o.hourrooms_id = p.hourrooms_id and b.student_id = p.student_id ) as succhour
                FROM tkl_lineclass_hourrooms as a,tkl_lineclass_hourroomstudy as b,tkl_lineclass AS c,smc_student as d,smc_student_study as e ,smc_school as f 
                WHERE {$datawhere}
                GROUP BY b.student_id 
            ) as x order by x.hourrooms_starttime,x.hourroomstudy_id ASC");

        if ($dateexcelarray) {
            $outexceldate = array();
            $state = ["0"=>"待考勤","1"=>"出勤","-1"=>"缺勤"];
            foreach ($dateexcelarray as $dateexcelvar) {
                $datearray = array();
                $datearray['school_cnname'] = $dateexcelvar['school_cnname'];//所属校区
                $datearray['school_branch'] = $dateexcelvar['school_branch'];//校区编号

                $datearray['lineclass_cnname'] = $dateexcelvar['lineclass_cnname'];//班课名称
                $datearray['lineclass_branch'] = $dateexcelvar['lineclass_branch'];//班课编号
                $datearray['lineclass_enname'] = $dateexcelvar['lineclass_enname'];//班课别名

                $datearray['student_cnname'] = $dateexcelvar['student_cnname'];//学员名称
                $datearray['student_branch'] = $dateexcelvar['student_branch'];//学员编号

                $datearray['succhour'] = $dateexcelvar['succhour'];//本班已上课次
                $datearray['failhour'] = $dateexcelvar['allhour'] - $dateexcelvar['succhour'];//本班待上课次

                $datearray['statename'] = $state[$dateexcelvar['hourroomstudy_checkin']] ;//出勤状态
                $outexceldate[] = $datearray;
            }
        }

        $excelheader = array("所属校区", "校区编号", "班课名称", "班课编号", "班课别名", "学员名称", "学员编号", "本班已上课次", "本班待上课次", "出勤状态");
        $excelfileds = array('school_cnname', "school_branch", "lineclass_cnname", 'lineclass_branch', 'lineclass_enname', "student_cnname", "student_branch", 'succhour', 'failhour', "statename");

        query_to_excel($excelheader, $outexceldate, $excelfileds, "课时学生名单.xlsx");
    }

    //课时列表 - 班级学员列表 -- 导出
    function ExportClassOneStudyAction()
    {
        $request = Input('post.', '', 'trim,addslashes');

        $datawhere = "a.company_id = '{$this->istaffer['company_id']}'
                    and a.hourrooms_id = b.hourrooms_id 
                    and a.lineclass_id = c.lineclass_id 
                    and b.student_id = d.student_id and a.company_id = d.company_id 
                    and d.student_id = e.student_id and e.study_isreading = '1' 
                    and e.school_id = f.school_id ";
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (d.student_cnname = '{$request['keyword']}' or d.student_branch = '{$request['keyword']}' or a.hourrooms_name = '{$request['keyword']}' or a.hourrooms_threenumber like '%{$request['keyword']}%' or c.lineclass_branch like '%{$request['keyword']}%' or c.lineclass_enname like '%{$request['keyword']}%')";
        }
        if (isset($request['lineclass_id']) && $request['lineclass_id'] !== '') {
            $datawhere .= " and a.lineclass_id = '{$request['lineclass_id']}' ";
        }
        if (isset($request['starttime']) && $request['starttime'] !== '') {
            $starttime = strtotime($request['starttime']);
            $datawhere .= " and a.hourrooms_endtime >= '{$starttime}'";
        }
        if (isset($request['endtime']) && $request['endtime'] !== '') {
            $endtime = strtotime($request['endtime']) + 3600 * 23;
            $datawhere .= " and a.hourrooms_starttime <= '{$endtime}'";
        }

        $dateexcelarray = $this->DataControl->selectClear("
            select * from (
                SELECT a.hourrooms_starttime,a.hourrooms_endtime,a.hourrooms_confuserpwd,b.hourroomstudy_id,b.hourroomstudy_checkin,f.school_cnname,f.school_branch,a.hourrooms_name,a.hourrooms_threenumber,c.lineclass_branch,c.lineclass_cnname,c.lineclass_enname,d.student_cnname,d.student_enname,d.student_branch,b.student_id,a.lineclass_id,a.hourrooms_id 
                    ,(select count(o.hourrooms_id) from tkl_lineclass_hourrooms as o WHERE a.lineclass_id = o.lineclass_id) as allhour 
                    ,(select count(o.hourrooms_id) from tkl_lineclass_hourrooms as o,tkl_lineclass_hourroomstudy as p WHERE a.lineclass_id = o.lineclass_id and a.hourrooms_id = o.hourrooms_id and o.hourrooms_id = p.hourrooms_id and b.student_id = p.student_id and  p.hourrooms_id > 0) as succhour
                    
                    ,(select o.hourrooms_name from tkl_lineclass_hourrooms as o,tkl_lineclass_hourroomstudy as p WHERE a.lineclass_id = o.lineclass_id and o.lineclass_id = p.lineclass_id and b.student_id = p.student_id order by hourrooms_lessontimes ASC limit 0,1) as first_hourrooms_name 
                    ,(select o.hourrooms_name from tkl_lineclass_hourrooms as o,tkl_lineclass_hourroomstudy as p WHERE a.lineclass_id = o.lineclass_id and o.lineclass_id = p.lineclass_id  and b.student_id = p.student_id order by hourrooms_lessontimes DESC limit 0,1) as last_hourrooms_name 
                    ,(select count(p.hourroomstudy_id) from tkl_lineclass_hourroomstudy as p WHERE o.lineclass_id = p.lineclass_id and b.student_id = p.student_id and p.hourroomstudy_checkin = '1'  limit 0,1) as arrivenum
                    ,(select count(p.hourroomstudy_id) from tkl_lineclass_hourroomstudy as p WHERE o.lineclass_id = p.lineclass_id and b.student_id = p.student_id and p.hourroomstudy_checkin = '-1'  limit 0,1) as lacknum
                    
                FROM tkl_lineclass_hourrooms as a,tkl_lineclass_hourroomstudy as b,tkl_lineclass AS c,smc_student as d,smc_student_study as e ,smc_school as f 
                WHERE {$datawhere}
                GROUP BY b.student_id 
            ) as x order by x.hourrooms_starttime,x.hourroomstudy_id ASC
            ");

        if ($dateexcelarray) {
            $outexceldate = array();
            $state = ["0"=>"待考勤","1"=>"出勤","-1"=>"缺勤"];
            foreach ($dateexcelarray as $dateexcelvar) {
                $datearray = array();
                $datearray['school_branch'] = $dateexcelvar['school_branch'];//校区编号
                $datearray['school_cnname'] = $dateexcelvar['school_cnname'];//校区名称
                $datearray['student_cnname'] = $dateexcelvar['student_cnname'];//学员中文名
                $datearray['student_enname'] = $dateexcelvar['student_enname'];//学员英文名
                $datearray['student_branch'] = $dateexcelvar['student_branch'];//学员编号
                $datearray['allhour'] = $dateexcelvar['allhour'];//排班次数
                $datearray['arrivenum'] = $dateexcelvar['arrivenum'];//出勤次数
                $datearray['lacknum'] = $dateexcelvar['lacknum'];//缺勤次数
                $datearray['first_hourrooms_name'] = $dateexcelvar['first_hourrooms_name'];//首次排班课时
                $datearray['last_hourrooms_name'] = $dateexcelvar['last_hourrooms_name'];//最后排班课时
                $outexceldate[] = $datearray;
            }
        }

        $excelheader = array("校区编号","校区名称","学员中文名","学员英文名","学员编号","排班次数","出勤次数","缺勤次数","首次排班课时","最后排班课时");
        $excelfileds = array('school_branch', 'school_cnname','student_cnname','student_enname','student_branch','allhour','arrivenum','lacknum', "first_hourrooms_name", "last_hourrooms_name");

        query_to_excel($excelheader, $outexceldate, $excelfileds, "班级学员名单.xlsx");

//        if ($dateexcelarray) {
//            $outexceldate = array();
//            $state = ["0"=>"待考勤","1"=>"出勤","-1"=>"缺勤"];
//            foreach ($dateexcelarray as $dateexcelvar) {
//                $datearray = array();
//                $datearray['school_cnname'] = $dateexcelvar['school_cnname'];//所属校区
//                $datearray['school_branch'] = $dateexcelvar['school_branch'];//校区编号
//                $datearray['lineclass_cnname'] = $dateexcelvar['lineclass_cnname'];//班课名称
//                $datearray['lineclass_branch'] = $dateexcelvar['lineclass_branch'];//班课编号
//                $datearray['lineclass_enname'] = $dateexcelvar['lineclass_enname'];//班课别名
//                $datearray['student_cnname'] = $dateexcelvar['student_cnname'];//学员名称
//                $datearray['student_branch'] = $dateexcelvar['student_branch'];//学员编号
//                $datearray['succhour'] = $dateexcelvar['succhour'];//本班已上课次
//                $datearray['failhour'] = $dateexcelvar['allhour'] - $dateexcelvar['succhour'];//本班待上课次
//                $datearray['statename'] = $state[$dateexcelvar['hourroomstudy_checkin']] ;//出勤状态
//                $outexceldate[] = $datearray;
//            }
//        }
//
//        $excelheader = array("所属校区", "校区编号", "班课名称", "班课编号", "班课别名", "学员名称", "学员编号", "本班已上课次", "本班待上课次", "出勤状态");
//        $excelfileds = array('school_cnname', "school_branch", "lineclass_cnname", 'lineclass_branch', 'lineclass_enname', "student_cnname", "student_branch", 'succhour', 'failhour', "statename");
//
//        query_to_excel($excelheader, $outexceldate, $excelfileds, "班级学生名单.xlsx");
    }

    //学员列表  -- 导出数据
    function ExportHourstudyAction(){
        $request = Input('post.', '', 'trim,addslashes');

        $datawhere = "s.student_id = d.student_id AND d.hourrooms_id = h.hourrooms_id AND c.lineclass_id = h.lineclass_id AND c.company_id = '{$this->istaffer['company_id']}'";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (s.student_cnname = '{$request['keyword']}' or s.student_branch = '{$request['keyword']}' or h.hourrooms_name = '{$request['keyword']}' or h.hourrooms_threenumber like '%{$request['keyword']}%' or c.lineclass_branch like '%{$request['keyword']}%' or c.lineclass_enname like '%{$request['keyword']}%')";
        }
        if (isset($request['starttime']) && $request['starttime'] !== '') {
            $starttime = strtotime($request['starttime']);
            $datawhere .= " and h.hourrooms_endtime >= '{$starttime}'";
        }
        if (isset($request['endtime']) && $request['endtime'] !== '') {
            $endtime = strtotime($request['endtime']) + 3600 * 23;
            $datawhere .= " and h.hourrooms_starttime <= '{$endtime}'";
        }

        $dateexcelarray = $this->DataControl->selectClear("SELECT s.*,d.hourroomstudy_id,d.hourrooms_id,h.hourrooms_name,h.hourrooms_threenumber,h.hourrooms_starttime,h.hourrooms_endtime,h.hourrooms_confuserpwd,
                c.lineclass_branch,c.lineclass_enname
                 FROM smc_student as s,tkl_lineclass_hourroomstudy as d,tkl_lineclass_hourrooms as h,tkl_lineclass AS c
                 where {$datawhere} order by h.hourrooms_starttime,d.hourroomstudy_id ASC");

        if ($dateexcelarray) {
            $outexceldate = array();
            foreach ($dateexcelarray as $dateexcelvar) {
                $datearray = array();
                $datearray['student_cnname'] = $dateexcelvar['student_cnname'];//学员名称
                $datearray['student_branch'] = $dateexcelvar['student_branch'];//学员编号

                $datearray['lineclass_enname'] = $dateexcelvar['lineclass_enname'];//班课别名
                $datearray['lineclass_branch'] = $dateexcelvar['lineclass_branch'];//班课编号

                $datearray['hourrooms_name'] = $dateexcelvar['hourrooms_name'];//教室名称
                $datearray['hourrooms_threenumber'] = $dateexcelvar['hourrooms_threenumber'];//教室号
                $datearray['hourrooms_starttime'] = date("Y-m-d",$dateexcelvar['hourrooms_starttime']);//开始时间
                $datearray['hourrooms_endtime'] = date("Y-m-d",$dateexcelvar['hourrooms_endtime']);//结束时间
                $datearray['hourrooms_confuserpwd'] = $dateexcelvar['hourrooms_confuserpwd'];//学员上课密码
                $outexceldate[] = $datearray;
            }
        }
        $excelheader = array("学员名称", "学员编号", "班课别名", "班课编号", "教室名称", "教室号", "开始时间", "结束时间", "学员上课密码");
        $excelfileds = array('student_cnname', "student_branch", "lineclass_enname", 'lineclass_branch', 'hourrooms_name', "hourrooms_threenumber", "hourrooms_starttime", 'hourrooms_endtime', 'hourrooms_confuserpwd');

        query_to_excel($excelheader, $outexceldate, $excelfileds, "学员管理.xlsx");
    }

    //课时管理  -- 导出数据
    function ExportClassHourAction(){
        $request = Input('post.', '', 'trim,addslashes');

        $datawhere = "c.lineclass_id = h.lineclass_id AND c.company_id = '{$this->istaffer['company_id']}'";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (h.hourrooms_name = '{$request['keyword']}' or h.hourrooms_threenumber like '%{$request['keyword']}%' or c.lineclass_branch like '%{$request['keyword']}%' or c.lineclass_enname like '%{$request['keyword']}%')";
        }
        if (isset($request['hourrooms_maxvideo']) && $request['hourrooms_maxvideo'] !== '') {
            $maxvideo = $request['hourrooms_maxvideo'] + 1;
            $datawhere .= " and h.hourrooms_maxvideo = '{$maxvideo}'";
        }
        if (isset($request['starttime']) && $request['starttime'] !== '') {
            $starttime = strtotime($request['starttime']);
            $datawhere .= " and h.hourrooms_endtime >= '{$starttime}'";
        }
        if (isset($request['endtime']) && $request['endtime'] !== '') {
            $endtime = strtotime($request['endtime']) + 3600 * 23;
            $datawhere .= " and h.hourrooms_starttime <= '{$endtime}'";
        }

        $dateexcelarray = $this->DataControl->selectClear("SELECT h.*,c.lineclass_branch,c.lineclass_enname ,
                ( SELECT COUNT(s.hourroomstudy_id) FROM tkl_lineclass_hourroomstudy AS s WHERE s.hourrooms_id = h.hourrooms_id ) AS studynums
                 FROM tkl_lineclass_hourrooms as h,tkl_lineclass AS c
                 where {$datawhere} order by h.hourrooms_starttime ASC");

        if ($dateexcelarray) {
            $outexceldate = array();
            foreach ($dateexcelarray as $dateexcelvar) {
                $datearray = array();
                $datearray['lineclass_branch'] = $dateexcelvar['lineclass_branch'];//班级编号
                $datearray['lineclass_enname'] = $dateexcelvar['lineclass_enname'];//班级别名

                $datearray['hourrooms_threenumber'] = $dateexcelvar['hourrooms_threenumber'];//教室号
                $datearray['hourrooms_name'] = $dateexcelvar['hourrooms_name'];//教室名称

                $datearray['studynums'] = $dateexcelvar['studynums'];//预约学员数
                $datearray['hourrooms_maxvideo'] = $dateexcelvar['hourrooms_maxvideo']-1;//最大上台数

                $datearray['hourrooms_starttime'] = date("Y-m-d",$dateexcelvar['hourrooms_starttime']);//开始时间
                $datearray['hourrooms_endtime'] = date("Y-m-d",$dateexcelvar['hourrooms_endtime']);//结束时间

                $datearray['hourrooms_chairmanpwd'] = $dateexcelvar['hourrooms_chairmanpwd'];//教师密码
                $datearray['hourrooms_assistantpwd'] = $dateexcelvar['hourrooms_assistantpwd'];//助教密码
                $datearray['hourrooms_patrolpwd'] = $dateexcelvar['hourrooms_patrolpwd'];//巡课密码
                $datearray['hourrooms_confuserpwd'] = $dateexcelvar['hourrooms_confuserpwd'];//上课密码
                $outexceldate[] = $datearray;
            }
        }
        $excelheader = array("班级编号", "班级别名", "教室号", "教室名称", "预约学员数", "最大上台数", "开始时间", "结束时间", "教师密码", "助教密码", "巡课密码", "上课密码");
        $excelfileds = array('lineclass_branch', "lineclass_enname", "hourrooms_threenumber", 'hourrooms_name', 'studynums', "hourrooms_maxvideo", "hourrooms_starttime", 'hourrooms_endtime', 'hourrooms_chairmanpwd', 'hourrooms_assistantpwd', 'hourrooms_patrolpwd', 'hourrooms_confuserpwd');

        query_to_excel($excelheader, $outexceldate, $excelfileds, "课时管理列表.xlsx");
    }

    //班课管理  -- 导出数据
    function ExportLineclassAction(){
        $request = Input('post.', '', 'trim,addslashes');


        $datawhere = "c.company_id = '{$this->istaffer['company_id']}' AND c.lineclass_isdel = '0'";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (c.lineclass_branch = '{$request['keyword']}' or c.lineclass_cnname like '%{$request['keyword']}%' or c.lineclass_enname like '%{$request['keyword']}%')";
        }
        if (isset($request['starttime']) && $request['starttime'] !== '') {
            $datawhere .= " and c.lineclass_enddate >= '{$request['starttime']}'";
        }

        if (isset($request['endtime']) && $request['endtime'] !== '') {
            $datawhere .= " and c.lineclass_stdate <= '{$request['endtime']}'";
        }

        $dateexcelarray = $this->DataControl->selectClear("SELECT c.*,
                ( SELECT COUNT(h.hourrooms_id) FROM tkl_lineclass_hourrooms AS h WHERE h.lineclass_id = c.lineclass_id ) AS hourroomsnums,
                ( SELECT COUNT(h.hourroomstudy_id) FROM tkl_lineclass_hourroomstudy AS h WHERE h.lineclass_id = c.lineclass_id ) AS studynums
                FROM tkl_lineclass AS c WHERE {$datawhere} order by c.lineclass_id DESC");

        if ($dateexcelarray) {
            $outexceldate = array();
            foreach ($dateexcelarray as $dateexcelvar) {
                $datearray = array();
                $datearray['lineclass_cnname'] = $dateexcelvar['lineclass_cnname'];//班课名称
                $datearray['lineclass_enname'] = $dateexcelvar['lineclass_enname'];//班课别名

                $datearray['lineclass_branch'] = $dateexcelvar['lineclass_branch'];//班课编号
                $datearray['hourroomsnums'] = $dateexcelvar['hourroomsnums'];//排课数

                $datearray['studynums'] = $dateexcelvar['studynums'];//排课人次
                $datearray['hourroomsnumsTwo'] = ($dateexcelvar['hourroomsnums']!=0?sprintf("%.2f",($dateexcelvar['studynums']/$dateexcelvar['hourroomsnums'])):0);//平均上课班平

                $datearray['lineclass_stdate'] = $dateexcelvar['lineclass_stdate'];//开始时间
                $datearray['lineclass_enddate'] = $dateexcelvar['lineclass_enddate'];//结束时间

                $datearray['lineclass_updatatime'] = date("Y-m-d H:i:s",$dateexcelvar['lineclass_updatatime']);//更新时间
                $datearray['lineclass_createtime'] = date("Y-m-d H:i:s",$dateexcelvar['lineclass_createtime']);//创建时间
                $outexceldate[] = $datearray;
            }
        }
        $excelheader = array("班课名称", "班课别名", "班课编号", "排课数", "排课人次", "平均上课班平", "开始时间", "结束时间", "更新时间", "创建时间");
        $excelfileds = array('lineclass_cnname', "lineclass_enname", "lineclass_branch", 'hourroomsnums', 'studynums', "hourroomsnumsTwo", "lineclass_stdate", 'lineclass_enddate', 'lineclass_updatatime', 'lineclass_createtime');

        query_to_excel($excelheader, $outexceldate, $excelfileds, "班课管理列表.xlsx");
    }

    //课时学员考勤
    function checkStuAttendanceAction()
    {
        $request = Input("get.", '', 'trim,addslashes');

        $data = array();
        $data['hourroomstudy_checkin'] = $request['hourroomstudy_checkin'];

        $this->DataControl->updateData('tkl_lineclass_hourroomstudy', "student_id='{$request['student_id']}' and lineclass_id='{$request['lineclass_id']}' and hourrooms_id='{$request['hourrooms_id']}'  ", $data);

        $datatwo = array();
        $datatwo['hourrooms_ischecking'] = 1;
        $datatwo['hourrooms_checktimes'] = time();
        $this->DataControl->updateData('tkl_lineclass_hourrooms', "lineclass_id='{$request['lineclass_id']}' and hourrooms_id='{$request['hourrooms_id']}'  ", $datatwo);
        ajax_return(array('error' => 0, 'errortip' => "提交成功!", 'state' => $request['hourroomstudy_checkin']));

    }

    //课时学员批量考勤处理
    function batchSetStudyCheckinAction()
    {
        $request = Input('post.','','trim,addslashes');

        if (isset($request['tab_list']) && count($request['tab_list']) > 0) {
            $succ = 0;
            $fail = 0;
            if ($request['ischeckin'] == 1) {
                //出勤
                foreach ($request['tab_list'] as $v) {
                    $data = array();
                    $data['hourroomstudy_checkin'] = 1;
                    $data['hourroomstudy_updatatime'] = time();
                    $this->DataControl->updateData('tkl_lineclass_hourroomstudy', "student_id='{$v}' and lineclass_id='{$request['lineclass_id']}' and hourrooms_id='{$request['hourrooms_id']}'  ", $data);
                    $succ++;
                }
                if($succ > 0){
                    $datatwo = array();
                    $datatwo['hourrooms_ischecking'] = 1;
                    $datatwo['hourrooms_checktimes'] = time();
                    $this->DataControl->updateData('tkl_lineclass_hourrooms', "lineclass_id='{$request['lineclass_id']}' and hourrooms_id='{$request['hourrooms_id']}'  ", $datatwo);
                }
                ajax_return(array('error' => 0, 'errortip' => "批量出勤成功!", "bakfuntion" => "refreshpage"));
            } elseif ($request['ischeckin'] == '-1') {
                //缺勤
                foreach ($request['tab_list'] as $v) {
                    $data = array();
                    $data['hourroomstudy_checkin'] = -1;
                    $data['hourroomstudy_updatatime'] = time();
                    $this->DataControl->updateData('tkl_lineclass_hourroomstudy', "student_id='{$v}' and lineclass_id='{$request['lineclass_id']}' and hourrooms_id='{$request['hourrooms_id']}'  ", $data);
                    $succ++;
                }
                if($succ > 0){
                    $datatwo = array();
                    $datatwo['hourrooms_ischecking'] = 1;
                    $datatwo['hourrooms_checktimes'] = time();
                    $this->DataControl->updateData('tkl_lineclass_hourrooms', "lineclass_id='{$request['lineclass_id']}' and hourrooms_id='{$request['hourrooms_id']}'  ", $datatwo);
                }
                ajax_return(array('error' => 0, 'errortip' => "批量缺勤成功!", "bakfuntion" => "refreshpage"));
            }
        }
    }

    //班课管理 -- 排课管理 - 班级课时导出
    function ExportHourroomsAction(){
        $request = Input('post.', '', 'trim,addslashes');

        $datawhere = "c.lineclass_id = h.lineclass_id AND c.company_id = '{$this->istaffer['company_id']}'";

        $lineclassOne = $this->DataControl->getOne("tkl_lineclass", "lineclass_id='{$request['lineclass_id']}'");
        $datawhere .= " AND c.lineclass_id = '{$lineclassOne['lineclass_id']}'";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (h.hourrooms_name = '{$request['keyword']}' or h.hourrooms_threenumber like '%{$request['keyword']}%' or c.lineclass_branch like '%{$request['keyword']}%' or c.lineclass_enname like '%{$request['keyword']}%')";
        }
        if (isset($request['hourrooms_maxvideo']) && $request['hourrooms_maxvideo'] !== '') {
            $maxvideo = $request['hourrooms_maxvideo'] + 1;
            $datawhere .= " and h.hourrooms_maxvideo = '{$maxvideo}'";
        }
        if (isset($request['starttime']) && $request['starttime'] !== '') {
            $starttime = strtotime($request['starttime']);
            $datawhere .= " and h.hourrooms_endtime >= '{$starttime}'";
        }
        if (isset($request['endtime']) && $request['endtime'] !== '') {
            $endtime = strtotime($request['endtime']) + 3600 * 23;
            $datawhere .= " and h.hourrooms_starttime <= '{$endtime}'";
        }

        $dateexcelarray = $this->DataControl->selectClear("SELECT h.*,c.lineclass_branch,c.lineclass_enname ,
                ( SELECT COUNT(s.hourroomstudy_id) FROM tkl_lineclass_hourroomstudy AS s WHERE s.hourrooms_id = h.hourrooms_id ) AS studynums
                 FROM tkl_lineclass_hourrooms as h,tkl_lineclass AS c
                 where {$datawhere} order by h.hourrooms_starttime ASC");

        if ($dateexcelarray) {
            $outexceldate = array();
            foreach ($dateexcelarray as $dateexcelvar) {
                $datearray = array();
                $datearray['lineclass_enname'] = $dateexcelvar['lineclass_enname'];//班级别名
                $datearray['hourrooms_threenumber'] = $dateexcelvar['hourrooms_threenumber'];//教室号

                $datearray['hourrooms_name'] = $dateexcelvar['hourrooms_name'];//教室名称
                $datearray['studynums'] = $dateexcelvar['studynums'];//学员数

                $datearray['hourrooms_starttime'] = date("Y-m-d H:i:s",$dateexcelvar['hourrooms_starttime']);//开始时间
                $datearray['hourrooms_endtime'] = date("Y-m-d H:i:s",$dateexcelvar['hourrooms_endtime']);//结束时间

                $datearray['hourrooms_chairmanpwd'] = $dateexcelvar['hourrooms_chairmanpwd'];//教师密码
                $datearray['hourrooms_assistantpwd'] = $dateexcelvar['hourrooms_assistantpwd'];//助教密码
                $datearray['hourrooms_patrolpwd'] = $dateexcelvar['hourrooms_patrolpwd'];//巡课密码
                $datearray['hourrooms_confuserpwd'] = $dateexcelvar['hourrooms_confuserpwd'];//上课密码

                $outexceldate[] = $datearray;
            }
        }
        $excelheader = array("班级别名", "教室号", "教室名称", "学员数", "开始时间", "结束时间", "教师密码", "助教密码", "巡课密码", "上课密码");
        $excelfileds = array('lineclass_enname', "hourrooms_threenumber", "hourrooms_name", 'studynums', 'hourrooms_starttime', "hourrooms_endtime", "hourrooms_chairmanpwd", 'hourrooms_assistantpwd', 'hourrooms_patrolpwd', 'hourrooms_confuserpwd');

        query_to_excel($excelheader, $outexceldate, $excelfileds, "班级课时导出.xlsx");
    }

    //课时管理  -- 课时礼物详情 题、红包、礼物
    function ExportClassHourQRGAction(){
        $request = Input('get.', '', 'trim,addslashes');

        $serialOne = $this->DataControl->selectOne(" select hourrooms_threenumber from tkl_lineclass_hourrooms where hourrooms_id = '{$request['hourrooms_id']}' and company_id = '8888' ");
        $DataInfo = array();
        if($serialOne['hourrooms_threenumber'] && $serialOne['hourrooms_threenumber'] != '0') {
            //获取上台和举手次数
            $TalkcloudModel = new \Model\Tlk\TalkcloudModel($this->FYCOMID);
            $putArray = array();
            $putArray['serial'] = $serialOne['hourrooms_threenumber'];
            $DataInfo = $TalkcloudModel->getStuRoomActiveUser($putArray);
        }

        //课时学员明细
        $sql = " 
                select b.student_cnname,b.student_branch,a.hourroomstudy_duration
                ,(select group_concat(distinct p.school_shortname) as school_shortname from smc_student_enrolled as o,smc_school as p where a.student_id = o.student_id and o.enrolled_status in ('0','1') and o.school_id = p.school_id limit 0,1) as school_shortname 
                ,(select sum(u.gift_giftnumber) from tkl_lineclass_hourrooms_gift as u where b.student_branch = u.gift_receiveid and u.hourrooms_threenumber = c.hourrooms_threenumber limit 0,1) as gift_num
                ,(select sum(u.redpacket_packetNum) from tkl_lineclass_hourrooms_redpacket as u where b.student_branch = u.redpacket_userId and u.hourrooms_threenumber = c.hourrooms_threenumber limit 0,1) as redpacket_num  
                
                ,(select count(u.accesslog_id) from tkl_lineclass_hourrooms_accesslog as u where b.student_branch = u.accesslog_userid and u.accesslog_serial = c.hourrooms_threenumber limit 0,1) as accesslogNum  
                ,(select u.accesslog_timestamp from tkl_lineclass_hourrooms_accesslog as u where b.student_branch = u.accesslog_userid and u.accesslog_status = 1 and u.accesslog_serial = c.hourrooms_threenumber order by u.accesslog_timestamp asc limit 0,1) as fist_join_time   
                ,(select u.accesslog_timestamp from tkl_lineclass_hourrooms_accesslog as u where b.student_branch = u.accesslog_userid and u.accesslog_status = 0 and u.accesslog_serial = c.hourrooms_threenumber order by u.accesslog_timestamp desc limit 0,1) as end_out_time   
                from tkl_lineclass_hourroomstudy as a,smc_student as b,tkl_lineclass_hourrooms as c 
                where  a.hourrooms_id = '{$request['hourrooms_id']}' and a.student_id = b.student_id and a.hourrooms_id = c.hourrooms_id 
        ";
        $studentAll = $this->DataControl->selectClear($sql);
        if($studentAll){
            //学生答题明细
            $qsql = "
            select b.student_cnname,b.student_branch,d.question_id,d.question_questionDesc,d.question_rightOptions  
                ,IFNULL((select stulist_isRight from tkl_lineclass_hourrooms_question_stulist as u WHERE d.question_id = u.question_id and b.student_branch = u.stulist_userId  and u.hourrooms_threenumber = c.hourrooms_threenumber order by u.stulist_isRight desc limit 0,1 ),'') as stulist_isRight 
                ,IFNULL((select stulist_options from tkl_lineclass_hourrooms_question_stulist as u WHERE d.question_id = u.question_id and b.student_branch = u.stulist_userId  and u.hourrooms_threenumber = c.hourrooms_threenumber order by u.stulist_isRight desc limit 0,1 ),'') as stulist_options 
            from tkl_lineclass_hourroomstudy as a
            left join smc_student as b on a.student_id = b.student_id 
            left join tkl_lineclass_hourrooms as c ON a.hourrooms_id = c.hourrooms_id  
            left join tkl_lineclass_hourrooms_question as d ON c.hourrooms_threenumber = d.hourrooms_threenumber  
            where  a.hourrooms_id = '{$request['hourrooms_id']}' and d.question_id > 0 
            ORDER BY b.student_branch,d.question_id 
            ";
            $questAll = $this->DataControl->selectClear($qsql);
            //学生答题明细 -- 新组合
            $stuquestarr = array();
            if($questAll){
                for ($i = 0; $i < count($questAll); $i++) {
                    $sex = $questAll[$i]["student_branch"];
                    $stuquestarr[$sex][] = $questAll[$i];
                }
            }

            $headerSome = array();
            $filedsSome = array();
            $dateexcelarray = $studentAll;
            $outexceldate = array();
            foreach ($dateexcelarray as $dateexcelvar) {
                $datearray = array();
                $datearray['school_shortname'] = $dateexcelvar['school_shortname'];//校区
                $datearray['student_cnname'] = $dateexcelvar['student_cnname'];//学员姓名
                $datearray['student_branch'] = $dateexcelvar['student_branch'];//学员编号
                $datearray['gift_num'] = $dateexcelvar['gift_num'];//奖杯数量
                $datearray['redpacket_num'] = $dateexcelvar['redpacket_num'];//红包代币总数
                $datearray['accesslogNum'] = $dateexcelvar['accesslogNum'];//总进出次数

                $datearray['publishstate'] = $DataInfo['publishstate'][$dateexcelvar['student_branch']]?$DataInfo['publishstate'][$dateexcelvar['student_branch']]:0;//上台次数
                $datearray['raisehand'] = $DataInfo['raisehand'][$dateexcelvar['student_branch']]?$DataInfo['raisehand'][$dateexcelvar['student_branch']]:0;//举手次数

                $datearray['fist_join_time'] = $dateexcelvar['fist_join_time']?date("Y-m-d H:i:s",$dateexcelvar['fist_join_time']):'--';//首次进入教室时间
                $datearray['end_out_time'] = $dateexcelvar['end_out_time']?date("Y-m-d H:i:s",$dateexcelvar['end_out_time']):'--';//末次离开教室时间
                $datearray['hourroomstudy_duration'] = $dateexcelvar['hourroomstudy_duration'];//总在线时长

                if($stuquestarr){
                    foreach ($stuquestarr[$dateexcelvar['student_branch']] as $key=>$stuquestarrVar){
                        $datearray['question_questionDesc'.$key] = $stuquestarrVar['question_questionDesc'];//题目说明
                        $datearray['question_rightOptions'.$key] = $stuquestarrVar['question_rightOptions'];//正确答案
                        $datearray['stulist_isRight'.$key] = $stuquestarrVar['stulist_isRight'];//学生对错
                        $datearray['stulist_options'.$key] = $stuquestarrVar['stulist_options'];//学生答案
                    }
                }
                $outexceldate[] = $datearray;
            }

            if($stuquestarr){
                $forkey = 1;
                for($i = 0; $i < count($stuquestarr[$dateexcelvar['student_branch']]); $i++){
                    $headerSome[] = "题目". $forkey ."-题目说明";
                    $headerSome[] = "题目". $forkey ."-正确答案";
                    $headerSome[] = "题目". $forkey ."-学生对错";
                    $headerSome[] = "题目". $forkey ."-学生答案";

                    $filedsSome[] = 'question_questionDesc'.$i;
                    $filedsSome[] = 'question_rightOptions'.$i;
                    $filedsSome[] = 'stulist_isRight'.$i;
                    $filedsSome[] = 'stulist_options'.$i;

                    $forkey++;
                }
            }

//            print_r($headerSome);
//            print_r($filedsSome);
//            print_r($outexceldate);
//            die;
            $excelheader = array("校区", "学员姓名", "学员编号", "奖杯数量", "红包代币总数", "总进出次数","上台次数","举手次数", "首次进入教室时间", "末次离开教室时间", "总在线时长");
            $excelfileds = array("school_shortname", "student_cnname", "student_branch", "gift_num", "redpacket_num", "accesslogNum","publishstate","raisehand", "fist_join_time", "end_out_time", "hourroomstudy_duration");

            $excelheaderEnd = array_merge($excelheader,$headerSome);
            $excelfiledsEnd = array_merge($excelfileds,$filedsSome);

//            print_r($excelheaderEnd);
//            print_r($excelfiledsEnd);
//            die;

            query_to_excel($excelheaderEnd, $outexceldate, $excelfiledsEnd, "教室号{$request['hourrooms_threenumber']}的学员上课数据.xlsx");
        }
    }

    //魔术方法
    function __destruct()
    {
        if ($this->c =='Wait') {
            $this->smarty->assign("u", $this->u);
            $this->smarty->assign("t", $this->t);
            $this->display($this->Viewhtm);
            exit;
        }
    }
}