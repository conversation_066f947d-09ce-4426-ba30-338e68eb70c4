<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2016/12/13
 * Time: 21:06
 */
namespace Work\Controller\Talkline;

class ApiController extends viewTpl{
    public $data;
    public $Viewhtm;
    public $u;
    public $t;
    public $c;
    public $FYCOMID = '8888';
    public $FYWKPID= '122042';

    function __construct() {
        parent::__construct ();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
    }

    /**拓课云流量更新**/
    function TkyuserlogView(){
        $minTimes = time() - 3600*1;
        $request = Input('get.','','trim,addslashes');
        if(isset($request['linerooms_number']) && $request['linerooms_number'] !==''){
            $datawhere = " r.linerooms_fromclass = '0' AND r.linerooms_number = '{$request['linerooms_number']}' AND LENGTH(r.linerooms_number) > 5";
        }else{
            $datawhere = " r.linerooms_fromclass = '0' AND r.linerooms_flowtime < '{$minTimes}' AND r.linerooms_isdel <> '1'";
        }

        $lineroomsArray = $this->DataControl->selectClear("SELECT r.* FROM tkl_linerooms as r
WHERE {$datawhere} ORDER BY r.linerooms_flowtime ASC,r.linerooms_id ASC limit 0,1");
        if($lineroomsArray){
            foreach($lineroomsArray as $lineroomsOne){
                $TalkcloudModel = new \Model\Tlk\TalkcloudModel($lineroomsOne['company_id']);
                $putArray = array();
                $putArray['serial'] = $lineroomsOne['linerooms_number'];
                $putArray['fromclass'] = $lineroomsOne['linerooms_fromclass'];
                $roomsapiOne = $TalkcloudModel->getExroominfo($putArray);

                $parenter = array();
                $parenter['linerooms_flowtime'] = time();
                $parenter['linerooms_createtime'] = $roomsapiOne['createtime'];
                $this->DataControl->updateData("tkl_linerooms","linerooms_id = '{$lineroomsOne['linerooms_id']}'",$parenter);

                $putArray = array();
                $putArray['serial'] = $lineroomsOne['linerooms_number'];
                $putArray['startdate'] = date("Y-m-d H:i:s",$roomsapiOne['createtime']);
                $putArray['enddate'] = date("Y-m-d H:i:s");
                $putArray['fromclass'] = $lineroomsOne['linerooms_fromclass'];
                $loginArray = $TalkcloudModel->exgetLogininfo($putArray);

                $modelnums = 0;
                $updatanums = 0;
                if($loginArray['result'] == '0'){
                    $infoArray = $loginArray['logininfo'];
                    if(is_array($infoArray) && count($infoArray) > 0){
                        foreach($infoArray as $infoOne){
                            if(!$this->DataControl->getFieldOne("tkl_linerooms_tkylog","tkylog_id"
                                ,"tkylog_userid = '{$infoOne['userid']}' and linerooms_id = '{$lineroomsOne['linerooms_id']}' and tkylog_entertime = '{$infoOne['entertime']}'")){
                                $loginlog = array();
                                $loginlog['company_id'] = $lineroomsOne['company_id'];
                                $loginlog['linerooms_id'] = $lineroomsOne['linerooms_id'];
                                $loginlog['tkylog_fromclass'] = $lineroomsOne['linerooms_fromclass'];
                                $loginlog['tkylog_userid'] = $infoOne['userid'];
                                $loginlog['tkylog_username'] = $infoOne['username'];
                                $loginlog['tkylog_entertime'] = $infoOne['entertime'];
                                $loginlog['tkylog_outtime'] = $infoOne['outtime'];
                                $loginlog['tkylog_duration'] = $infoOne['duration'];
                                $loginlog['tkylog_ipaddress'] = $infoOne['ipaddress'];
                                $loginlog['tkylog_ostype'] = $infoOne['ostype'];
                                $loginlog['tkylog_paytime'] = $infoOne['paytime'];
                                $loginlog['tkylog_userroleid'] = $infoOne['userroleid'];
                                $loginlog['tkylog_videotype'] = $infoOne['videotype'];
                                $loginlog['tkylog_createtimetime'] = time();
                                $this->DataControl->insertData("tkl_linerooms_tkylog",$loginlog);
                                $modelnums ++;
                            }else{
                                $loginlog = array();
                                $loginlog['tkylog_entertime'] = $infoOne['entertime'];
                                $loginlog['tkylog_outtime'] = $infoOne['outtime'];
                                $loginlog['tkylog_duration'] = $infoOne['duration'];
                                $loginlog['tkylog_paytime'] = $infoOne['paytime'];
                                $this->DataControl->updateData("tkl_linerooms_tkylog"
                                    ,"tkylog_userid = '{$infoOne['userid']}' and linerooms_id = '{$lineroomsOne['linerooms_id']}' and tkylog_entertime = '{$infoOne['entertime']}'",$loginlog);
                                $updatanums ++;
                            }
                        }
                    }
                }

                $logcount = count($infoArray);
                exit("房间:{$lineroomsOne['linerooms_number']},获得日志数量：{$logcount},新增用户日志：{$modelnums}个，更新用户日志{$updatanums}个<br />");
            }
        }else{
            exit("更新已完毕");
        }
    }

    //测试获取答题数据
    function getStuAnswersView(){
        $request = Input('get.','','trim,addslashes');
        $TalkcloudModel = new \Model\Tlk\TalkcloudModel($this->FYCOMID);

        $putArray = array();
        $putArray['serial'] = $request['serial'];
        $Exroominfo = $TalkcloudModel->getStuAnswers($putArray);

        print_r($Exroominfo);die;

        $ExroominfoArr =  json_decode($Exroominfo, true);
    }

    //测试获取 红包
    function getStuRedpacketView(){
        $request = Input('get.','','trim,addslashes');
        $TalkcloudModel = new \Model\Tlk\TalkcloudModel($this->FYCOMID);

        $putArray = array();
        $putArray['serial'] = $request['serial'];
        $Exroominfo = $TalkcloudModel->getStuRedpacket($putArray);

        print_r($Exroominfo);die;

        $ExroominfoArr =  json_decode($Exroominfo, true);
    }

    //测试获取 礼物
    function getStuGiftView(){
        $request = Input('get.','','trim,addslashes');
        $TalkcloudModel = new \Model\Tlk\TalkcloudModel($this->FYCOMID);

        $putArray = array();
        $putArray['serial'] = $request['serial'];
        $Exroominfo = $TalkcloudModel->getStuGift($putArray);

        print_r($Exroominfo);die;

        $ExroominfoArr =  json_decode($Exroominfo, true);
    }


    function HomeView(){
        exit("接口已暂停");
    }

    function YunshuView(){
        $minTimes = time() - 3600*3;
        $lineroomsArray = $this->DataControl->selectClear("SELECT r.* FROM tkl_linerooms as r
WHERE r.linerooms_fromclass = '1' and r.linerooms_isdel <> '1' and r.company_id <> '8888' and r.linerooms_flowtime < '{$minTimes}' ORDER BY r.linerooms_flowtime ASC,r.linerooms_id ASC limit 0,1");
        if($lineroomsArray) {
            foreach ($lineroomsArray as $lineroomsOne) {
                if($lineroomsOne['linerooms_flowtime'] == '0'){
                    $queryTimes = strtotime(date("Y-m-d",$lineroomsOne['linerooms_createtime']));
                }else{
                    $queryTimes = $lineroomsOne['linerooms_flowtime']+3600*24;
                }
                $queryDay = date("Y-m-d",$queryTimes);
                echo "更新教室{$lineroomsOne['linerooms_number']},日期{$queryDay}用户进出记录<br />";

                $parenter = array();
                $parenter['linerooms_flowtime'] = $queryTimes;
                $this->DataControl->updateData("tkl_linerooms", "linerooms_id = '{$lineroomsOne['linerooms_id']}'", $parenter);

                $TalkcloudModel = new \Model\Tlk\TalkcloudModel($lineroomsOne['company_id']);
                $putArray = array();
                $putArray['serial'] = $lineroomsOne['linerooms_number'];
                $putArray['fromclass'] = $lineroomsOne['linerooms_fromclass'];
                $putArray['starttime'] = $queryTimes;
                $putArray['endtime'] = $queryTimes+(3600*24)-1;
                echo "更新".date("Y-m-d H:i:s",$putArray['starttime'])."到".date("Y-m-d H:i:s",$putArray['endtime'])."数据<br />";
                $modelArray = $TalkcloudModel->exgetLogininfo($putArray);
                $modelnums = 0;
                $updatanums = 0;
                if($modelArray['result'] == '0'){
                    $infoArray = $modelArray['list'];
                    if($infoArray){
                        foreach($infoArray as $infoOne){
                            if(!$this->DataControl->getFieldOne("tkl_linerooms_tkylog","tkylog_id"
                                ,"tkylog_userid = '{$infoOne['userid']}' and linerooms_id = '{$lineroomsOne['linerooms_id']}' and tkylog_entertime = '{$infoOne['entertime']}'")){
                                $loginlog = array();
                                $loginlog['company_id'] = $lineroomsOne['company_id'];
                                $loginlog['linerooms_id'] = $lineroomsOne['linerooms_id'];
                                $loginlog['tkylog_fromclass'] = $lineroomsOne['linerooms_fromclass'];
                                $loginlog['tkylog_userid'] = $infoOne['userid'];
                                $loginlog['tkylog_username'] = $infoOne['username'];
                                $loginlog['tkylog_entertime'] = $infoOne['entertime'];
                                $loginlog['tkylog_outtime'] = $infoOne['outtime'];
                                $loginlog['tkylog_duration'] = strtotime($infoOne['outtime'])-strtotime($infoOne['entertime']);
                                $loginlog['tkylog_ipaddress'] = $infoOne['ipaddress'];
                                $loginlog['tkylog_ostype'] = $infoOne['devicetype'];
                                $loginlog['tkylog_paytime'] = strtotime($infoOne['outtime'])-strtotime($infoOne['entertime']);
                                $loginlog['tkylog_userroleid'] = $infoOne['userroleid'];
                                $loginlog['tkylog_videotype'] = '320*240';
                                $loginlog['tkylog_createtimetime'] = time();
                                $this->DataControl->insertData("tkl_linerooms_tkylog",$loginlog);
                                $modelnums ++;
                            }else{
                                $loginlog = array();
                                $loginlog['tkylog_paytime'] = $infoOne['duration'];
                                $this->DataControl->updateData("tkl_linerooms_tkylog"
                                    ,"tkylog_userid = '{$infoOne['userid']}' and linerooms_id = '{$lineroomsOne['linerooms_id']}' and tkylog_entertime = '{$infoOne['entertime']}'",$loginlog);
                                $updatanums ++;
                            }
                        }
                    }
                }
                echo "房间ID:{$lineroomsOne['linerooms_id']}新增用户日志：{$modelnums}个，更新用户日志{$updatanums}个<br />";
            }
        }



        echo '<script language="javascript" type="text/javascript">
var i = 1;
var intervalid;
intervalid = setInterval("fun()", 1000);
function fun() {
	if (i == 0) {
		window.location.href = "/Api/Yunshu";
		clearInterval(intervalid);
	}
	document.getElementById("mes").innerHTML = i;
	i--;
}
</script>
<div id="error">
	<p>将在 <span id="mes">5</span> 秒钟后返回首页！</p>
</div> ';
    }

    function YunshulogView(){
        $minTimes = time() - 3600*24*1;
        $lineroomsArray = $this->DataControl->selectClear("SELECT r.* FROM tkl_linerooms as r
WHERE r.linerooms_fromclass = '1' and r.linerooms_pricelogtime < '{$minTimes}' ORDER BY r.linerooms_pricelogtime ASC,r.linerooms_id ASC limit 0,1");
        if($lineroomsArray) {
            foreach ($lineroomsArray as $lineroomsOne) {
                if($lineroomsOne['linerooms_pricelogtime'] == '0'){
                    $startTimes = $lineroomsOne['linerooms_createtime'];
                }else{
                    $startTimes = $lineroomsOne['linerooms_pricelogtime'];
                }
                $startDay = date("Y-m-d",$startTimes);
                $endTimes = $startTimes + (3600*24)*7;
                $endDay = date("Y-m-d",$endTimes);
                echo "更新教室{$lineroomsOne['linerooms_number']},日期{$startDay}至{$endDay}教室金额统计<br />";

                $parenter = array();
                $parenter['linerooms_pricelogtime'] = $endTimes;
                $this->DataControl->updateData("tkl_linerooms", "linerooms_id = '{$lineroomsOne['linerooms_id']}'", $parenter);

                $TalkcloudModel = new \Model\Tlk\TalkcloudModel($lineroomsOne['company_id']);
                $putArray = array();
                $putArray['serial'] = $lineroomsOne['linerooms_number'];
                $putArray['fromclass'] = $lineroomsOne['linerooms_fromclass'];
                $putArray['starttime'] = $startTimes;
                $putArray['endtime'] = $endTimes;
                echo "更新".date("Y-m-d H:i:s",$startTimes)."到".date("Y-m-d H:i:s",$endTimes)."上课数据<br />";
                $modelArray = $TalkcloudModel->exroomonGetRoomInfo($putArray);
                $modelnums = 0;
                if($modelArray['result'] == '0'){
                    $infoArray = $modelArray['data'];
                    if($infoArray){
                        foreach($infoArray as $infoOne){
                            if($this->Intoyunshulog($infoOne,$lineroomsOne['company_id'],$lineroomsOne['linerooms_id'])){
                                $modelnums ++;
                            }
                        }
                    }

                    if($modelArray['pageinfo']['count'] > 20){
                        $twoArray = $TalkcloudModel->exroomonGetRoomInfo($putArray,2);
                        $twoinfoArray = $twoArray['data'];
                        if($twoinfoArray){
                            foreach($twoinfoArray as $infoOne){
                                if($this->Intoyunshulog($infoOne,$lineroomsOne['company_id'],$lineroomsOne['linerooms_id'])){
                                    $modelnums ++;
                                }
                            }
                        }
                    }
                    if($modelArray['pageinfo']['count'] > 40){
                        $threeArray = $TalkcloudModel->exroomonGetRoomInfo($putArray,3);
                        $threeinfoArray = $threeArray['data'];
                        if($threeinfoArray){
                            foreach($threeinfoArray as $infoOne){
                                if($this->Intoyunshulog($infoOne,$lineroomsOne['company_id'],$lineroomsOne['linerooms_id'])){
                                    $modelnums ++;
                                }
                            }
                        }
                    }
                    if($modelArray['pageinfo']['count'] > 60){
                        $fourArray = $TalkcloudModel->exroomonGetRoomInfo($putArray,4);
                        $fourinfoArray = $fourArray['data'];
                        if($fourinfoArray){
                            foreach($fourinfoArray as $infoOne){
                                if($this->Intoyunshulog($infoOne,$lineroomsOne['company_id'],$lineroomsOne['linerooms_id'])){
                                    $modelnums ++;
                                }
                            }
                        }
                    }
                }
                echo "房间ID:{$lineroomsOne['linerooms_id']}新增上课日志：{$modelnums}个<br />";
            }
        }


        echo '<script language="javascript" type="text/javascript">
            var i = 1;
            var intervalid;
            intervalid = setInterval("fun()", 100);
            function fun() {
                if (i == 0) {
                    window.location.href = "/Api/Yunshulog";
                    clearInterval(intervalid);
                }
                document.getElementById("mes").innerHTML = i;
                i--;
            }
            </script>
            <div id="error">
                <p>将在 <span id="mes">5</span> 秒钟后返回首页！</p>
            </div> ';
    }


    function Intoyunshulog($infoOne,$company_id,$linerooms_id){
        if(!$this->DataControl->getFieldOne("tkl_linerooms_yunshulog","yunshulog_id"
            ,"yunshulog_houerid = '{$infoOne['id']}' and linerooms_id = '{$linerooms_id}'")){
            $loginlog = array();
            $loginlog['company_id'] = $company_id;
            $loginlog['linerooms_id'] = $linerooms_id;
            $loginlog['yunshulog_houerid'] = $infoOne['id'];
            $loginlog['yunshulog_begintime'] = $infoOne['begintime'];
            $loginlog['yunshulog_endtime'] = $infoOne['endtime'];
            $loginlog['yunshulog_duration'] = $infoOne['duration'];
            $loginlog['yunshulog_durationSecond'] = $infoOne['durationSecond'];
            $loginlog['yunshulog_usernum'] = $infoOne['usernum'];
            $loginlog['yunshulog_studentsCost'] = $infoOne['studentsCost'];
            $loginlog['yunshulog_unitprice'] = $infoOne['unitprice'];
            $loginlog['yunshulog_taduration'] = $infoOne['taduration'];
            $loginlog['yunshulog_tadurationSecond'] = $infoOne['tadurationSecond'];
            $loginlog['yunshulog_taprice'] = $infoOne['taprice'];
            $loginlog['yunshulog_taCost'] = $infoOne['taCost'];
            $loginlog['yunshulog_dis'] = $infoOne['dis'];
            $loginlog['yunshulog_totalprice'] = $infoOne['totalprice'];
            $loginlog['yunshulog_createtimetime'] = time();
            $this->DataControl->insertData("tkl_linerooms_yunshulog",$loginlog);
            return true;
        }else{
            return false;
        }
    }


    function getroomRecordView(){
        $request = Input('get.','','trim,addslashes');
        $dataVar = $this->DataControl->getOne("tkl_linerooms","linerooms_number='{$request['linerooms_number']}'");
        $this->smarty->assign("dataVar",$dataVar);

        $noquery = array(144215771,2094006303,1735739007,1598674129,485398333,1327366259,1896097159,*********,1886515600,1695549573,667372180,1633720868,895018678,1136201765,998459175,1284824907,188778810,1123976840,1160895370,1481836158,306824996,2089455749,562320441,299002788,1526988047,1882246201,1524893064,188926746,1836200693,669684824,1961703760,747677822,2083505731,1934690309,1254648355,631372909,1852486688,453920900,1273855632,1426998208,1604495355,407449733,1518801214,418252013,2002964702,2038102465,1650007839,569509556,921533510,2129705022,2033909714,444289917,786197211,826233186,1541799776,512474144,1808306239,94100773,348582657,1827024592,676324134,617156463,939473620,1449118718,2012845451,667372180,1327366259,485398333,1441766183,472332879,1152246602,874281702,2020615181,1139141177,452197389,804394714,1125044075,1706994926,851642295,240180962,244139450,1937489705,1138748111,144215771,920801307,1531027550,1161272517,1136201765,2134282725,1032309619,2072062015,262170972,661698395,2130219584,1896097159,1695549573,881398418,717310727,369505624,370920433,1733133333,1074064661,895018678,53829828,28704225,13139475,153626244,1065705121,442414621,429006790,1667824039,464283647,1657198501,1624574673,2066648824,1233733885,490727813,24323738,*********,1347410068,1633720868,1213596793,1851327898,*********,*********,2056138956,1886515600,95590147,12528599,1735739007,*********,1598674129,1445632237,*********,*********,29699796,1351104924,1331966707,1535916219,*********,*********,*********,1740370956,1044890559,1982010135,1928472836,*********,*********,*********,*********,1643635818,1255441911,2016298971,1940187173,1681621243,1479202643,*********,*********,*********);


        $TalkcloudModel = new \Model\Tlk\TalkcloudModel($dataVar['company_id']);
        $putArray = array();
        $putArray['serial'] = $dataVar['linerooms_number'];
        $putArray['fromclass'] = $dataVar['linerooms_fromclass'];

        if(isset($request['date']) && $request['date'] !==''){
            if(isset($request['starttime']) && $request['starttime'] !== ''){
                $putArray['starttime'] = strtotime($request['date'] . $request['starttime']);
            }else{
                $putArray['starttime'] = strtotime($request['date']);
            }

            if(isset($request['endtime']) && $request['endtime'] !== ''){
                $putArray['endtime'] = strtotime($request['date'] . $request['endtime']);
            }else{
                $putArray['endtime'] = strtotime($request['date'])+3600*24;
            }
        }else{
//            $minLogOne = $this->DataControl->getFieldOne("tkl_linerooms_tkylog","tkylog_entertime","linerooms_id='{$dataVar['linerooms_id']}' and tkylog_duration > '500'"," ORDER BY tkylog_entertime DESC limit 0,1");
//            $request['date'] = date("Y-m-d",strtotime($minLogOne['tkylog_entertime']));
//            $putArray['starttime'] = strtotime($request['date']);
//            $putArray['endtime'] = strtotime($request['date'])+3600*24;
            $maxtime = date("Y-m-d H:i:s",time()-3600*24*15);
            $putArray = array();
            $putArray['serial'] = $dataVar['linerooms_number'];
            $putArray['fromclass'] = $dataVar['linerooms_fromclass'];
            $putArray['starttime'] = time()-3600*24*15;
            $putArray['endtime'] = time();
            $modelArray = $TalkcloudModel->exgetLogininfo($putArray);
            if($modelArray['result'] == '0'){
                if($dataVar['linerooms_fromclass'] == '0'){
                    $apiArray = $modelArray['logininfo'];
                }else{
                    $apiArray = $modelArray['list'];
                }
                foreach($apiArray as $apiOne){
                    if($apiOne['userroleid'] == '2' && trim($apiOne['outtime']) !== '' && $apiOne['entertime'] > $maxtime){
                        $maxtime = $apiOne['entertime'];
                    }
                }
            }
            $request['date'] = date("Y-m-d",strtotime($maxtime));
            $putArray['starttime'] = strtotime($request['date']);
            $putArray['endtime'] = strtotime($request['date'])+3600*24;
        }

        $modelArray = $TalkcloudModel->exroomonGetvideolist($putArray);
        if($modelArray['result'] == '0'){
            if($dataVar['linerooms_fromclass'] == '0'){
                $recordList = $modelArray['recordlist'];
            }else{
                $recordList = $modelArray['list'];
            }
            $recordArray = array();
            if($recordList) {
                foreach ($recordList as $roomOne) {
                    $recordOne = array();
                    $recordOne['staffer_cnname'] = '吉的堡';
                    $recordOne['starttime'] = date("Y-m-d H:i:s",$roomOne['starttime']);
                    $recordOne['linerooms_name'] = $dataVar['linerooms_name'];
                    if ($dataVar['linerooms_fromclass'] == '0') {
                        $recordOne['playurl'] = $roomOne['https_playpath_mp4'];
                        $recordOne['duration'] = $this->secToTime($roomOne['duration']);
                    } else {
                        $recordOne['playurl'] = $roomOne['https_playpath_mp4'];
                        $recordOne['duration'] = $this->secToTime($roomOne['duration']);
                    }
                    $recordOne['size'] = $this->switchSize($roomOne['size']);
                    if($dataVar['company_id'] == '8888'){
                        if(in_array($dataVar['linerooms_number'],$noquery) && $recordOne['starttime'] > '2020-02-29 23:59:59'){
                            $recordArray[] = $recordOne;
                        }elseif(!in_array($dataVar['linerooms_number'],$noquery) && $recordOne['starttime'] > '2020-03-08 23:59:59'){
                            $recordArray[] = $recordOne;
                        }
                    }else{
                        $recordArray[] = $recordOne;
                    }
                }
            }
        }
        ajax_return(array('error' => 0,'errortip' => "录播记录查询完毕!".$maxtime,'result' => $recordArray));
    }

    function minvideoToView(){
        $lineroomsArray = $this->DataControl->selectClear("SELECT r.* FROM tkl_linerooms as r
WHERE r.linerooms_maxvideo < '8' and r.linerooms_maxvideo > '4' ORDER BY r.linerooms_id ASC limit 0,30");
        if($lineroomsArray){
            foreach($lineroomsArray as $lineroomsOne){
                $TalkcloudModel = new \Model\Tlk\TalkcloudModel($lineroomsOne['company_id']);
                $putArray = array();
                $putArray['roomname'] = $lineroomsOne['linerooms_name'];
                $putArray['serial'] = $lineroomsOne['linerooms_number'];
                $putArray['fromclass'] = $lineroomsOne['linerooms_fromclass'];
                $putArray['roomtype'] = $lineroomsOne['linerooms_type'];
                $putArray['starttime'] = $lineroomsOne['linerooms_starttime'];
                $putArray['endtime'] = $lineroomsOne['linerooms_endtime'];
                $putArray['chairmanpwd'] = $lineroomsOne['linerooms_chairmanpwd'];
                $putArray['assistantpwd'] = $lineroomsOne['linerooms_assistantpwd'];
                $putArray['patrolpwd'] = $lineroomsOne['linerooms_patrolpwd'];
                $putArray['passwordrequired'] = $lineroomsOne['linerooms_passwordrequired'];
                $putArray['confuserpwd'] = $lineroomsOne['linerooms_confuserpwd'];
                $putArray['autoopenav'] = $lineroomsOne['linerooms_autoopenav'];
                $putArray['maxvideo'] = 8;
                $putArray['sharedesk'] = $lineroomsOne['linerooms_sharedesk'];
                $putArray['sidelineuserpwd'] = rand(11111,999999);
                $modelArray = $TalkcloudModel->exroomModify($putArray);
                if($modelArray['result'] == '0'){
                    $parenter = array();
                    $parenter['linerooms_maxvideo'] = 8;
                    $this->DataControl->updateData("tkl_linerooms","linerooms_id = '{$lineroomsOne['linerooms_id']}'",$parenter);
                    echo "房间ID:{$lineroomsOne['linerooms_id']}修改数量{$lineroomsOne['linerooms_maxvideo']}到8成功<br />";
                }else{
                    echo "房间ID:{$lineroomsOne['linerooms_id']}修改数量{$lineroomsOne['linerooms_maxvideo']}到8失败<br />";
                }
            }
        }else{
            exit("更新已完毕");
        }


        echo '<script language="javascript" type="text/javascript">
var i = 1;
var intervalid;
intervalid = setInterval("fun()", 1000);
function fun() {
	if (i == 0) {
		window.location.href = "/Api/minvideoTo";
		clearInterval(intervalid);
	}
	document.getElementById("mes").innerHTML = i;
	i--;
}
</script>
<div id="error">
	<p>将在 <span id="mes">5</span> 秒钟后返回首页！</p>
</div> ';
    }


    function secToTime($times){
        $result = '00:00:00';
        if ($times>0) {
            $hour = floor($times/3600);
            $minute = floor(($times-3600 * $hour)/60);
            $second = floor((($times-3600 * $hour) - 60 * $minute) % 60);
            $result = $hour.':'.$minute.':'.$second;
        }
        return $result;
    }
    function switchSize($size) {
        $units = array(' B', ' KB', ' MB', ' GB', ' TB');
        for ($i = 0; $size >= 1024 && $i < 4; $i++) {
            $size /= 1024;
        }
        return round($size, 2).$units[$i];
    }

    function ForeignView(){
        $request = Input('get.','','trim,addslashes');
        if(isset($request['day']) && $request['day'] !=='0'){
            $thisweek = GetWeekAll($request['day']);
            $this->smarty->assign("day",$request['day']);
        }else{
            $thisweek = GetWeekAll(date("Y-m-d",time()));
            $this->smarty->assign("day",date("Y-m-d",time()));
        }
        $this->smarty->assign("thisweek",$thisweek);

        $schoolList = $this->DataControl->selectClear("SELECT s.school_id,s.school_cnname
FROM smc_school AS s WHERE s.company_id = '8888' AND s.school_istest = '0' GROUP BY s.school_id ORDER BY s.school_id ASC");
        $this->smarty->assign("schoolList",$schoolList);

        $organizeClass = $this->DataControl->selectClear("SELECT s.organize_id,s.organize_cnname FROM gmc_company_organize AS s
WHERE s.company_id = '8888' AND s.organizeclass_id = '186' ORDER BY s.father_id ASC");
        $this->smarty->assign("organizeClass",$organizeClass);

        $datatype = array();
        $datawhere = "t.class_id = c.class_id AND t.staffer_id = s.staffer_id AND t.hour_id = h.hour_id
AND s.staffer_id = p.staffer_id AND p.school_id = l.school_id AND p.school_id <> '0'
AND c.company_id = '8888' AND s.staffer_leave <> '1' AND p.postbe_ismianjob = '1'
AND h.hour_day <= '{$thisweek['nowweek_end']}' AND h.hour_day >= '{$thisweek['nowweek_start']}' and p.post_id = '227'";

        if(isset($request['keyword']) && $request['keyword'] !==''){
            $datawhere .= " and (s.staffer_cnname = '{$request['keyword']}' or s.staffer_branch like '%{$request['keyword']}%')";
            $datatype['keyword'] = $request['keyword'];
        }

        if(isset($request['school_id']) && $request['school_id'] !==''){
            $datawhere .= " and c.school_id = '{$request['school_id']}'";
            $datatype['school_id'] = $request['school_id'];
        }

        //区域类型
        if(isset($request['organize_id']) && $request['organize_id'] !==''){
            $datawhere .= " and c.school_id in (SELECT sc.school_id FROM gmc_company_organizeschool as ss
        LEFT JOIN smc_school as sc on sc.school_id = ss.school_id WHERE ss.organize_id = '{$request['organize_id']}')";
            $datatype['organize_id'] = $request['organize_id'];
        }


        if(isset($request['staffer_isparttime']) && $request['staffer_isparttime'] !== ''){
            $datawhere .= " and s.staffer_isparttime = '{$request['staffer_isparttime']}'";
            $datatype['staffer_isparttime'] = $request['staffer_isparttime'];
        }

        $stafferList = $this->DataControl->selectClear("SELECT s.staffer_id, l.school_cnname, s.staffer_cnname, s.staffer_enname, s.staffer_branch, s.staffer_isparttime
FROM smc_class_hour_teaching AS t, smc_class_hour AS h, smc_class AS c, smc_staffer AS s, gmc_staffer_postbe AS p, smc_school AS l
WHERE {$datawhere} GROUP BY s.staffer_id ORDER BY l.school_id ASC");

        if($stafferList){
            foreach($stafferList as &$stafferOne){
                $hourCount = $this->DataControl->selectOne("SELECT sum(h.hour_classtimes) AS classhours,COUNT(DISTINCT c.class_id) AS classnums
FROM smc_class_hour_teaching AS t, smc_class_hour AS h, smc_class AS c,smc_school AS s
WHERE t.class_id = c.class_id AND t.hour_id = h.hour_id AND c.school_id = s.school_id AND h.hour_day <= '{$thisweek['nowweek_end']}'
AND h.hour_day >= '{$thisweek['nowweek_start']}' AND t.staffer_id = '{$stafferOne['staffer_id']}' limit 0,1");
                $stafferOne['classhours'] = round($hourCount['classhours']/3600,2);
                $stafferOne['classnums'] = $hourCount['classnums'];

                $hourList = $this->DataControl->selectClear("SELECT
    s.school_cnname, c.class_cnname, c.class_enname, c.class_branch, h.hour_name, h.hour_day, h.hour_starttime, h.hour_endtime
FROM smc_class_hour_teaching AS t, smc_class_hour AS h, smc_class AS c,smc_school AS s
WHERE t.class_id = c.class_id AND t.hour_id = h.hour_id AND c.school_id = s.school_id AND h.hour_day <= '{$thisweek['nowweek_end']}'
AND h.hour_day >= '{$thisweek['nowweek_start']}' AND t.staffer_id = '{$stafferOne['staffer_id']}'");

                $weekArray = array();
                for($i=0;$i<7;$i++){
                    $weekOne = array();
                    $weekday = date("Y-m-d",strtotime($thisweek['nowweek_start'])+3600*24*$i);
                    $jinDay = mktime(0,0,0,date("m",time()),date("d",time()),date("Y",time()));
                    for ($t=16;$t<41;$t++) {
                        $statTimes = date("H:i",$jinDay+$t*30*60);
                        $endTimes = date("H:i",$jinDay+($t+1)*30*60);
                        $timecouse = '';
                        foreach($hourList as $hourOne){
                            if($hourOne['hour_day'] == $weekday && is_time_cross(strtotime($statTimes),strtotime($endTimes),strtotime($hourOne['hour_starttime']),strtotime($hourOne['hour_endtime']))){
                                $timecouse = "校区：{$hourOne['school_cnname']}\n班级：{$hourOne['class_cnname']}{$hourOne['class_enname']} \n时间：({$hourOne['hour_starttime']}-{$hourOne['hour_endtime']})";
                            }
                        }
                        $weekOne[] = $timecouse;
                    }
                    $weekArray[] = $weekOne;
                }

                $stafferOne['weekjson'] = json_encode($weekArray,JSON_UNESCAPED_UNICODE);
            }
        }
        $this->smarty->assign("stafferList",$stafferList);
        $this->smarty->assign("datatype",$datatype);
        $this->display("Api/Foreign.htm");
    }

    function CnseniorView(){
        $request = Input('get.','','trim,addslashes');
        if(isset($request['day']) && $request['day'] !=='0'){
            $thisweek = GetWeekAll($request['day']);
            $this->smarty->assign("day",$request['day']);
        }else{
            $thisweek = GetWeekAll(date("Y-m-d",time()));
            $this->smarty->assign("day",date("Y-m-d",time()));
        }
        $this->smarty->assign("thisweek",$thisweek);

        $schoolList = $this->DataControl->selectClear("SELECT s.school_id,s.school_cnname
FROM smc_school AS s WHERE s.company_id = '8888' AND s.school_istest = '0' GROUP BY s.school_id ORDER BY s.school_id ASC");
        $this->smarty->assign("schoolList",$schoolList);

        $organizeClass = $this->DataControl->selectClear("SELECT s.organize_id,s.organize_cnname FROM gmc_company_organize AS s
WHERE s.company_id = '8888' ORDER BY s.father_id ASC");
        $this->smarty->assign("organizeClass",$organizeClass);



        $postList = $this->DataControl->selectClear("SELECT p.post_id,p.post_name FROM gmc_company_post AS p
WHERE p.company_id = '8888' AND p.post_isteaching = '1' ORDER BY p.post_id ASC");
        $this->smarty->assign("postList",$postList);

        $datawhere = "t.class_id = c.class_id
AND t.staffer_id = s.staffer_id
AND t.hour_id = h.hour_id
AND s.staffer_id = p.staffer_id
AND p.school_id = l.school_id
AND p.school_id <> '0'
AND c.company_id = '8888'
AND h.hour_day <= '{$thisweek['nowweek_end']}'
AND h.hour_day >= '{$thisweek['nowweek_start']}'";

        $datatype = array();
        if(isset($request['keyword']) && $request['keyword'] !==''){
            $datawhere .= " and (s.staffer_cnname = '{$request['keyword']}' or s.staffer_branch like '%{$request['keyword']}%')";
            $datatype['keyword'] = $request['keyword'];
        }

        if(isset($request['school_id']) && $request['school_id'] !==''){
            $datawhere .= " and c.school_id = '{$request['school_id']}'";
            $datatype['school_id'] = $request['school_id'];
        }


        if(isset($request['post_id']) && $request['post_id'] !==''){
            $datawhere .= " and p.post_id = '{$request['post_id']}'";
            $datatype['post_id'] = $request['post_id'];
        }

        //区域类型
        if(isset($request['organize_id']) && $request['organize_id'] !==''){
            $datawhere .= " and c.school_id in (SELECT sc.school_id FROM gmc_company_organizeschool as ss
        LEFT JOIN smc_school as sc on sc.school_id = ss.school_id WHERE ss.organize_id = '{$request['organize_id']}')";
            $datatype['organize_id'] = $request['organize_id'];
        }

        if(isset($request['teachtype_code']) && $request['teachtype_code'] !=='0'){
            $datawhere .= " and t.teachtype_code = '{$request['teachtype_code']}'";
            $datatype['teachtype_code'] = $request['teachtype_code'];
        }else{
            $datawhere .= " AND (t.teachtype_code = '05CC' OR t.teachtype_code = '05CM')";
        }

        if(isset($request['staffer_isparttime']) && $request['staffer_isparttime'] !==''){
            $datawhere .= " and s.staffer_isparttime = '{$request['staffer_isparttime']}'";
            $datatype['staffer_isparttime'] = $request['staffer_isparttime'];
        }

        $stafferList = $this->DataControl->selectClear("SELECT
	s.staffer_id,
l.school_cnname,
	s.staffer_cnname,
	s.staffer_enname,
	s.staffer_branch,
	s.staffer_isparttime
FROM
	smc_class_hour_teaching AS t,
	smc_class_hour AS h,
	smc_class AS c,
	smc_staffer AS s,
	gmc_staffer_postbe AS p,
	smc_school AS l
WHERE
	{$datawhere}
GROUP BY
	s.staffer_id
ORDER BY l.school_id ASC");
        if($stafferList){
            foreach($stafferList as &$stafferOne){
                $hourList = $this->DataControl->selectClear("SELECT
    s.school_cnname,
	c.class_cnname,
	c.class_enname,
	c.class_branch,
	h.hour_name,
	h.hour_day,
	h.hour_starttime,
	h.hour_endtime
FROM
	smc_class_hour_teaching AS t,
	smc_class_hour AS h,
	smc_class AS c,smc_school AS s
WHERE
	t.class_id = c.class_id
AND t.hour_id = h.hour_id AND c.school_id = s.school_id
AND h.hour_day <= '{$thisweek['nowweek_end']}'
AND h.hour_day >= '{$thisweek['nowweek_start']}'
AND t.staffer_id = '{$stafferOne['staffer_id']}'");
                $weekArray = array();
                for($i=0;$i<7;$i++){
                    $weekOne = array();
                    $weekday = date("Y-m-d",strtotime($thisweek['nowweek_start'])+3600*24*$i);
                    $jinDay = mktime(0,0,0,date("m",time()),date("d",time()),date("Y",time()));
                    for ($t=16;$t<41;$t++) {
                        $statTimes = date("H:i",$jinDay+$t*30*60);
                        $endTimes = date("H:i",$jinDay+($t+1)*30*60);
                        $timecouse = '';
                        foreach($hourList as $hourOne){
                            if($hourOne['hour_day'] == $weekday && is_time_cross(strtotime($statTimes),strtotime($endTimes),strtotime($hourOne['hour_starttime']),strtotime($hourOne['hour_endtime']))){
                                $timecouse = "校区：{$hourOne['school_cnname']}\n班级：{$hourOne['class_cnname']}{$hourOne['class_enname']} \n时间：({$hourOne['hour_starttime']}-{$hourOne['hour_endtime']})";
                            }
                        }
                        $weekOne[] = $timecouse;
                    }
                    $weekArray[] = $weekOne;
                }

                $stafferOne['weekjson'] = json_encode($weekArray,JSON_UNESCAPED_UNICODE);
            }
        }
        $this->smarty->assign("stafferList",$stafferList);
        $this->smarty->assign("datatype",$datatype);
        $this->display("Api/Cnsenior.htm");
    }

    function ClassLogView(){
        $request = Input('get.','','trim,addslashes');
        if(isset($request['class_branch']) && $request['class_branch'] !=='' && $request['pswd'] == '4427'){
            $datawhere = "s.student_id = d.student_id
AND d.class_id = c.class_id
AND b.student_id = s.student_id
AND b.course_id = c.course_id
AND d.study_isreading = '1'
AND c.company_id = '8888'";
            $datatype = array();
            if(isset($request['class_branch']) && $request['class_branch'] !==''){
                $datawhere .= " and c.class_branch = '{$request['class_branch']}'";
                $datatype['class_branch'] = $request['class_branch'];
            }
            $datatype['pswd'] = $request['pswd'];

            if(isset($request['keyword']) && $request['keyword'] !==''){
                $datawhere .= " and (s.student_branch = '{$request['keyword']}' or s.student_cnname like '%{$request['keyword']}%')";
                $datatype['keyword'] = $request['keyword'];
            }

            $studentList = $this->DataControl->selectClear("SELECT
	s.student_branch,
	s.student_cnname,
	s.student_enname,s.student_sex,
	(
		SELECT
			f.family_mobile
		FROM
			smc_student_family AS f
		WHERE
			f.student_id = s.student_id
		ORDER BY
			f.family_isdefault DESC
		LIMIT 0,
		1
	) AS family_mobile,
	d.study_beginday,
	d.study_endday,
	b.coursebalance_figure,
	b.coursebalance_unitexpend,
	b.coursebalance_time
FROM
	smc_student AS s,
	smc_student_study AS d,
	smc_class AS c,
	smc_student_coursebalance AS b
WHERE {$datawhere} ");
            if($studentList) {
                foreach ($studentList as &$studentOne) {
                    $studentOne['family_mobile'] = hideNumberString($studentOne['family_mobile']);
                }
            }
            $this->smarty->assign("studentList",$studentList);
        }
        $this->smarty->assign("datatype",$datatype);
        $this->display("Api/ClassLog.htm");
    }

    //网课时间处理
    function lockinTimeView(){
        $nowDay = date("Y-m-d");
        $nextDay = date('Y-m-d',strtotime('+15 day'));
        $lineList = $this->DataControl->selectClear("SELECT
	h.hour_id,
	h.hour_lessontimes,
	h.hour_number,
	h.hour_name,
	h.hour_day,
	h.hour_starttime,
	h.hour_endtime,
	from_unixtime(
		UNIX_TIMESTAMP(
			CONCAT(
				h.hour_day,
				' ',
				h.hour_starttime,
				':00'
			)
		),
		'%Y-%m-%d %H:%i:%s'
	) AS startTimes,
	from_unixtime(
		UNIX_TIMESTAMP(
			CONCAT(
				h.hour_day,
				' ',
				h.hour_endtime,
				':00'
			)
		),
		'%Y-%m-%d %H:%i:%s'
	) AS endTimes,l.linerooms_id,l.linerooms_threenumber,l.company_id,l.school_id
FROM
	smc_class_hour AS h
LEFT JOIN smc_linerooms AS l ON l.hour_id = h.hour_id
WHERE
	h.hour_way = '1'
AND l.company_id <> '8888'
AND h.hour_ischecking <> '-1'
AND (l.linerooms_issync <> '1' OR h.hour_day <> FROM_UNIXTIME(l.linerooms_starttime, '%Y-%m-%d'))
AND h.hour_day >= '{$nowDay}'
AND h.hour_day <= '{$nextDay}'
limit 0,6");
        $updatanums = 0;
        if($lineList) {
            foreach ($lineList as $lineOne) {
                $updatanums ++;
                if(trim($lineOne['linerooms_threenumber']) == ''){
                    $publicarray = array();
                    $publicarray['company_id'] = $lineOne['company_id'];
                    $publicarray['school_id'] = $lineOne['school_id'];
                    $LineClassModel = new \Model\Smc\LineClassModel($publicarray);
                    $ApiJson = $LineClassModel->getLineThreenumber($lineOne['hour_id']);
                    if($ApiJson){
                        //print_r($ApiJson);
                    }else{
                        echo "企业ID:{$lineOne['company_id']}-".$LineClassModel->errortip."<br />";
                    }
                }else{
                    $TalkcloudModel = new \Model\Tlk\TalkcloudModel($lineOne['company_id']);
                    $putArray = array();
                    $putArray['serial'] = $lineOne['linerooms_threenumber'];
                    $putArray['roomname'] = $lineOne['hour_name'] ;
                    $putArray['starttime'] = strtotime($lineOne['startTimes']);
                    $putArray['endtime'] = strtotime($lineOne['endTimes']) + 3600 * 2 - 1;
                    $putArray['fromclass'] = '0';
                    $putArray['sharedesk'] = '1';
                    $ApiJson = $TalkcloudModel->exroomTimesModify($putArray);
                    if($ApiJson){
                        $lineData = array();
                        $lineData['linerooms_starttime'] = strtotime($lineOne['startTimes']);
                        $lineData['linerooms_endtime'] = strtotime($lineOne['endTimes']);
                        $lineData['linerooms_issync'] = "1";
                        $this->DataControl->updateData("smc_linerooms", "linerooms_id='{$lineOne['linerooms_id']}'", $lineData);
                    }else{
                        echo $TalkcloudModel->errortip;
                    }
                }
            }
        }
        exit("更新{$nowDay}至{$nextDay}已完毕{$updatanums}");
    }

    function lockinCoursewareView(){
        $nowDay = date("Y-m-d");
        $nextDay = date('Y-m-d',strtotime('+2 day'));
        $sql = "SELECT h.hour_id, h.class_id, h.hour_lessontimes, h.hour_number,l.linerooms_id,l.linerooms_threenumber,l.company_id,l.school_id
FROM smc_class_hour AS h
LEFT JOIN smc_linerooms AS l ON l.hour_id = h.hour_id
WHERE h.hour_way = '1'
AND l.company_id <> '8888'
AND h.hour_ischecking <> '-1'
AND l.linerooms_issync = '1' AND h.hour_lessontimes <> l.hour_lessontimes
AND h.hour_day >= '{$nowDay}'
AND h.hour_day <= '{$nextDay}'
limit 0,6";
        $lineList = $this->DataControl->selectClear($sql);
        $updatanums = 0;
        if($lineList) {
            foreach ($lineList as $lineOne) {
                $updatanums ++;

                $TalkcloudModel = new \Model\Tlk\TalkcloudModel($lineOne['company_id']);

                $file_putArray = array();
                $file_putArray['serial'] = $lineOne['linerooms_threenumber'];
                $roomfile = $TalkcloudModel->exroomonGetroomfile($file_putArray);
                print_r($roomfile);

                // 匹配课件
                /*$wordFiledId = $this->DataControl->selectClear("
              select  DISTINCT cd.line_fileid
             from eas_coursepackage_lessonword as cd    
             left join eas_coursepackage_lesson as cn On cn.lesson_id = cd.lesson_id and cn.package_id = cd.package_id          
             left join eas_coursepackage_apply as cy ON cy.package_id= cn.package_id
             left join smc_class as c ON cy.course_id =c.course_id
             left join smc_class_hour as ch On ch.class_id =c.class_id and cn.lesson_sort = ch.hour_lessontimes
             where c.class_id='{$lineOne['class_id']}' and ch.hour_id = '{$lineOne['hour_id']}' and cn.is_synchro = 1");
                if (is_array($wordFiledId)) {
                    $array_field = array_column($wordFiledId, 'line_fileid');
                    $file_putArray = array();
                    $file_putArray['serial'] = $lineOne['linerooms_threenumber'];
                    $file_putArray['fileidarr'] = $array_field;
                    $TalkcloudModel->ex_roombindfile($file_putArray);
                }



                $lineData = array();
                $lineData['hour_lessontimes'] = $lineOne['hour_lessontimes'];
                $this->DataControl->updateData("smc_linerooms", "linerooms_id='{$lineOne['linerooms_id']}'", $lineData);*/
            }
        }
        exit("更新{$nowDay}至{$nextDay}已完毕{$updatanums}");
    }

    function lockinTimesroomView(){
        $nowDay = date("Y-m-d");
        $lineList = $this->DataControl->selectClear("SELECT
	h.hour_id,
	h.hour_lessontimes,
	h.hour_number,
	h.hour_name,
	h.hour_day,
	h.hour_starttime,
	h.hour_endtime,
	from_unixtime(
		UNIX_TIMESTAMP(
			CONCAT(
				h.hour_day,
				' ',
				h.hour_starttime,
				':00'
			)
		),
		'%Y-%m-%d %H:%i:%s'
	) AS startTimes,
	from_unixtime(
		UNIX_TIMESTAMP(
			CONCAT(
				h.hour_day,
				' ',
				h.hour_endtime,
				':00'
			)
		),
		'%Y-%m-%d %H:%i:%s'
	) AS endTimes,
	l.linerooms_id,
	l.linerooms_threenumber,
	l.company_id,
	l.school_id,
	from_unixtime(
		l.linerooms_starttime,
		'%Y-%m-%d %H:%i:%s'
	) AS LinestartTimes,
	from_unixtime(
		l.linerooms_endtime,
		'%Y-%m-%d %H:%i:%s'
	) AS LineendTimes
FROM
	smc_class_hour AS h
LEFT JOIN smc_linerooms AS l ON l.hour_id = h.hour_id
WHERE
	h.hour_way = '1'
AND h.hour_ischecking <> '-1'
AND h.hour_day >= '{$nowDay}'
HAVING
	startTimes <> LinestartTimes
OR endTimes <> LineendTimes");
        if($lineList) {
            foreach ($lineList as $lineOne) {
                $lineData = array();
                $lineData['linerooms_starttime'] = strtotime($lineOne['startTimes']);
                $lineData['linerooms_endtime'] = strtotime($lineOne['endTimes']);
                $lineData['linerooms_issync'] = "0";
                $this->DataControl->updateData("smc_linerooms", "hour_id='{$lineOne['hour_id']}'", $lineData);
            }
        }
        exit("更新已完毕");
    }

    //学生进入教室的回调接口
    function backStuHourAccessLogView(){
        $request = Input('get.', '', 'trim,addslashes');
        $requestdata = json_encode($request, JSON_UNESCAPED_UNICODE);

//        $recordlogOne = $this->DataControl->selectOne("select accesslog_id from tkl_lineclass_hourrooms_accesslog where accesslog_identification = '{$request['identification']}'");
//        if($recordlogOne['accesslog_id']){
//            $this->error = 1;
//            $this->errortip = "已存在数据，无需存储！";
//            return false;
//        }

        $list = array();
        $list['accesslog_apiurl'] = "/Api/backStuHourAccessLog";
        $list['accesslog_serial'] = $request['serial'];
        $list['accesslog_userid'] = $request['userid'];
        $list['accesslog_role'] = $request['role'];
        $list['accesslog_status'] = $request['status'];
        $list['accesslog_username'] = $request['username'];
        $list['accesslog_timestamp'] = $request['timestamp'];
        $list['accesslog_markts'] = $request['markts'];
        $list['accesslog_identification'] = $request['identification'];
        $list['accesslog_bakjson'] = $requestdata;
        $list['accesslog_time'] = time();
        $listOne = $this->DataControl->insertData("tkl_lineclass_hourrooms_accesslog",$list);
        if($listOne) {
            //记录通话记录
            ajax_return(array('error' => 1, 'errortip' => "记录添加成功"));
        } else{
            ajax_return(array('error' => 1, 'errortip' => "记录添加失败"));
        }
    }


    //针对自动考勤 -- 创建需要推送考勤的班级
    function addCheckHourView(){
        //当前时间
        $nowtime = time();
        $uptotime = time()+86400;

        $someHour = $this->DataControl->selectOne("
            SELECT a.hourrooms_id 
            FROM tkl_lineclass_hourrooms as a
            WHERE  a.hourrooms_ischecking = 0  
                and (a.hourrooms_endtime+300) < '{$nowtime}' and (a.hourrooms_endtime+1800) > '{$nowtime}' and a.hourrooms_threenumber <> ''  
                and not exists ( select 1 from tkl_lineclass_hourrooms_check_log as b where a.hourrooms_id = b.hourrooms_id and b.log_type = '1' limit 0,1)
        ");

        if($someHour){
            $remindlog = $this->DataControl->query("
                        insert into tkl_lineclass_hourrooms_check_log(hourrooms_id,hourrooms_starttime,hourrooms_endtime,log_type,log_tilte,log_createtime,log_uptotime) 
                        SELECT a.hourrooms_id,a.hourrooms_starttime,a.hourrooms_endtime,'1','课时考勤','{$nowtime}','{$uptotime}'
                        FROM tkl_lineclass_hourrooms as a
                        WHERE  a.hourrooms_ischecking = 0 and (a.hourrooms_endtime+300) < '{$nowtime}' and (a.hourrooms_endtime+1800) > '{$nowtime}' and a.hourrooms_threenumber <> ''  
                        and not exists ( select 1 from tkl_lineclass_hourrooms_check_log as b where a.hourrooms_id = b.hourrooms_id and b.log_type = '1' limit 0,1)
                    ");
            if($remindlog){
                echo '队列添加成功';
            }else{
                echo '队列添加失败';
            }
        }else{
            echo '暂无需要考勤的课时';
        }
    }
    //计算在线时长
    function countOnlineTimeView(){
        //当前时间
        $nowtime = time();

        $checklogOne = $this->DataControl->selectOne(" SELECT a.log_id,a.hourrooms_id,a.hourrooms_starttime,a.hourrooms_endtime 
            FROM tkl_lineclass_hourrooms_check_log as a 
            WHERE  a.log_type = '1' and a.log_ischeck = '0' and a.log_uptotime > '{$nowtime}' 
            limit 0,1
            ");
        if(!$checklogOne){
            $res = array('error' => '1', 'errortip' => '暂无需要自动考勤的课程');
            ajax_return($res);
        }
        $someCheckStu = $this->DataControl->selectClear("
            select a.hourroomstudy_id,a.student_id,b.student_branch,e.hourrooms_starttime,e.hourrooms_endtime,e.hourrooms_threenumber  
            from tkl_lineclass_hourroomstudy as a, smc_student as b,tkl_lineclass_hourrooms as e  
            where a.hourrooms_id = '{$checklogOne['hourrooms_id']}' and a.hourroomstudy_checkin = '0' 
                and a.student_id = b.student_id 
                and a.hourrooms_id = e.hourrooms_id 
                and exists ( select 1 from tkl_lineclass_hourrooms_accesslog as c where c.accesslog_serial = e.hourrooms_threenumber and c.accesslog_userid = b.student_branch limit 0,1)
            limit 0,25
        ");
        if(!$someCheckStu){
            $mlog = array();
            $mlog['hourroomstudy_checkin'] = '-1';
            $mlog['hourroomstudy_updatatime'] = time();
            $this->DataControl->updateData("tkl_lineclass_hourroomstudy","hourrooms_id = '{$checklogOne['hourrooms_id']}' and hourroomstudy_checkin = '0'  ",$mlog);

            $mlog = array();
            $mlog['log_ischeck'] = 1;
            $mlog['log_checktime'] = time();
            $this->DataControl->updateData("tkl_lineclass_hourrooms_check_log","log_id = '{$checklogOne['log_id']}'",$mlog);

            $hourone = array();
            $hourone['hourrooms_ischecking'] = 1;
            $hourone['hourrooms_updatatime'] = time();
            $this->DataControl->updateData("tkl_lineclass_hourrooms","hourrooms_id = '{$checklogOne['hourrooms_id']}'",$hourone);

            $res = array('error' => '1', 'errortip' => '暂无需要自动考勤的学生');
            ajax_return($res);
        }

        $succ = 0;
        $fail = 0;
        foreach ($someCheckStu as $someCheckVar){

            $hourStime = $someCheckVar['hourrooms_starttime'];
            $hourEtime = $someCheckVar['hourrooms_endtime'];

//            $sql = "
//            SELECT x.accesslog_identification,sum(if((x.laveaa = 0  or x.laveaa>'{$hourEtime}'),'{$hourEtime}',laveaa)-if((x.joinaa = 0 or x.joinaa<'{$hourStime}'),'{$hourStime}',joinaa)) as allsecond
//            FROM (
//                SELECT accesslog_identification,
//                max(CASE `accesslog_status` WHEN '1' THEN if(((accesslog_timestamp>'{$hourStime}' and accesslog_timestamp<'{$hourEtime}') or accesslog_timestamp<'{$hourStime}') ,accesslog_timestamp,0) ELSE 0 END) as 'joinaa',
//                max(CASE `accesslog_status` WHEN '0' THEN if(((accesslog_timestamp>'{$hourStime}' and accesslog_timestamp<'{$hourEtime}') or accesslog_timestamp>'{$hourEtime}'),accesslog_timestamp,0) ELSE 0 END) as 'laveaa'
//                FROM tkl_lineclass_hourrooms_accesslog
//                where accesslog_userid = '{$someCheckVar['student_branch']}' and accesslog_serial = '{$someCheckVar['hourrooms_threenumber']}'
//                GROUP BY accesslog_identification
//            ) as x WHERE (x.joinaa >= 0 or x.laveaa >= 0) and x.accesslog_identification <> '' and (x.joinaa+x.laveaa)>='{$hourStime}' and x.accesslog_identification is not NULL
//            ";
            //整节课没有进出过的 没计算到 该上边的算法

            $onesql = "
            SELECT x.accesslog_identification,x.joinaa,x.laveaa,(if(x.laveaa>'{$hourEtime}','{$hourEtime}',laveaa)-if(x.joinaa<'{$hourStime}','{$hourStime}',joinaa)) as allsecond 
            FROM ( 
                SELECT accesslog_identification,
                max(CASE `accesslog_status` WHEN '1' THEN if(accesslog_timestamp<'{$hourStime}',accesslog_timestamp,0) ELSE 0 END) as 'joinaa',
                max(CASE `accesslog_status` WHEN '0' THEN if(accesslog_timestamp>'{$hourEtime}',accesslog_timestamp,0) ELSE 0 END) as 'laveaa'
                FROM tkl_lineclass_hourrooms_accesslog  
                where accesslog_userid = '{$someCheckVar['student_branch']}' and accesslog_serial = '{$someCheckVar['hourrooms_threenumber']}'
                GROUP BY accesslog_identification  
            ) as x WHERE  x.joinaa <='{$hourStime}' and x.laveaa >='{$hourEtime}' limit 0,1
            ";
            $StuOne = $this->DataControl->selectOne($onesql);
            if(!$StuOne['allsecond']) {
                //整节课没有进出过的 没计算到
                $sql = "
                SELECT x.accesslog_identification,sum(if(x.laveaa = 0,'{$hourEtime}',laveaa)-if(x.joinaa = 0,'{$hourStime}',joinaa)) as allsecond
                FROM (
                    SELECT accesslog_identification,
                    max(CASE `accesslog_status` WHEN '1' THEN if((accesslog_timestamp>'{$hourStime}' and accesslog_timestamp<'{$hourEtime}'),accesslog_timestamp,0) ELSE 0 END) as 'joinaa',
                    max(CASE `accesslog_status` WHEN '0' THEN if((accesslog_timestamp>'{$hourStime}' and accesslog_timestamp<'{$hourEtime}'),accesslog_timestamp,0) ELSE 0 END) as 'laveaa'
                    FROM tkl_lineclass_hourrooms_accesslog
                    where accesslog_userid = '{$someCheckVar['student_branch']}' and accesslog_serial = '{$someCheckVar['hourrooms_threenumber']}'
                    GROUP BY accesslog_identification
                ) as x WHERE (x.joinaa >= 0 or x.laveaa >= 0) and x.accesslog_identification <> '' and (x.joinaa+x.laveaa)>='{$hourStime}' and x.accesslog_identification is not NULL
                ";
                $StuOne = $this->DataControl->selectOne($sql);
            }
            if($StuOne['allsecond'] && $StuOne['allsecond']>0){
                $theminute = sprintf("%.2f",$StuOne['allsecond']/60);
                if($theminute <= 5){
                    $checkin = '-1';
                }elseif($theminute > 45){
                    $checkin = '1';
                }else{
                    $checkin = '2';
                }
                $mlog = array();
                $mlog['hourroomstudy_checkin'] = $checkin;
                $mlog['hourroomstudy_duration'] = $theminute;
                $mlog['hourroomstudy_updatatime'] = time();
                $this->DataControl->updateData("tkl_lineclass_hourroomstudy","hourroomstudy_id = '{$someCheckVar['hourroomstudy_id']}'",$mlog);
            }else{
                $mlog = array();
                $mlog['hourroomstudy_checkin'] = -1;
                $mlog['hourroomstudy_updatatime'] = time();
                $this->DataControl->updateData("tkl_lineclass_hourroomstudy","hourroomstudy_id = '{$someCheckVar['hourroomstudy_id']}'",$mlog);
            }
        }
        $mlog = array();
        $mlog['log_succnum'] = $succ+$checklogOne['log_succnum'];
        $mlog['log_failnum'] = $fail+$checklogOne['log_failnum'];
        $this->DataControl->updateData("tkl_lineclass_hourrooms_check_log","log_id = '{$checklogOne['log_id']}'",$mlog);

    }

    //针对短信提醒 -- 创建需要推送短信的数据信息
    //提前 12 小时
    function addRemindOnedayView(){
        //当前时间
        $nowtime = time();
        $uptotime = time()+21600;
        //提前 12 小时
        $remindtime = time()+43200;
        $remindtimetwo = time()+43200+200;

        $someOneDayHour = $this->DataControl->selectOne("
            SELECT a.hourrooms_id 
            FROM tkl_lineclass_hourrooms as a
            WHERE  a.hourrooms_ischecking = 0 and a.hourrooms_starttime >= '{$remindtime}' and a.hourrooms_starttime < '{$remindtimetwo}' and a.hourrooms_threenumber <> ''
            and not exists ( select 1 from tkl_lineclass_hourrooms_remind_log as b where a.hourrooms_id = b.hourrooms_id and b.log_type = '1' limit 0,1)
            limit 0,1
        ");
        if($someOneDayHour){
            $remindlog = $this->DataControl->query("
                        insert into tkl_lineclass_hourrooms_remind_log(hourrooms_id,hourrooms_starttime,log_type,log_tilte,log_createtime,log_uptotime) 
                        SELECT a.hourrooms_id,a.hourrooms_starttime,'1','上课提醒','{$nowtime}','{$uptotime}'
                        FROM tkl_lineclass_hourrooms as a
                        WHERE  a.hourrooms_ischecking = 0 and a.hourrooms_starttime >= '{$remindtime}' and a.hourrooms_starttime < '{$remindtimetwo}' 
                        and not exists ( select 1 from tkl_lineclass_hourrooms_remind_log as b where a.hourrooms_id = b.hourrooms_id and b.log_type = '1' limit 0,1)
                    ");
            if($remindlog){
                echo '队列添加成功';
            }else{
                echo '队列添加失败';
            }
        }else{
            echo '暂无需要提前一天提醒的课时';
        }
    }
    //提前两小时
    function addRemindTwoHourView(){
        //当前时间
        $nowtime = time();
        $uptotime = time()+1800;
        //提前2小时
        $remindtime = time()+7200;
        $remindtimetwo = time()+7200+200;

        $someOneDayHour = $this->DataControl->selectOne("
            SELECT a.hourrooms_id 
            FROM tkl_lineclass_hourrooms as a
            WHERE  a.hourrooms_ischecking = 0 and a.hourrooms_starttime >= '{$remindtime}' and a.hourrooms_starttime < '{$remindtimetwo}'  and a.hourrooms_threenumber <> '' 
                and not exists ( select 1 from tkl_lineclass_hourrooms_remind_log as b where a.hourrooms_id = b.hourrooms_id and b.log_type = '2' limit 0,1)
            limit 0,1
        ");
        if($someOneDayHour){
            $remindlog = $this->DataControl->query("
                        insert into tkl_lineclass_hourrooms_remind_log(hourrooms_id,hourrooms_starttime,log_type,log_tilte,log_createtime,log_uptotime) 
                        SELECT a.hourrooms_id,a.hourrooms_starttime,'2','上课提醒','{$nowtime}','{$uptotime}'
                        FROM tkl_lineclass_hourrooms as a
                        WHERE  a.hourrooms_ischecking = 0 and a.hourrooms_starttime >= '{$remindtime}' and a.hourrooms_starttime < '{$remindtimetwo}' 
                        and not exists ( select 1 from tkl_lineclass_hourrooms_remind_log as b where a.hourrooms_id = b.hourrooms_id and b.log_type = '2' limit 0,1)
                    ");
            if($remindlog){
                echo '队列添加成功';
            }else{
                echo '队列添加失败';
            }
        }else{
            echo '暂无需要提前两小时提醒的课时';
        }
    }
    //提前五分钟
    function addRemindFiveMinView(){
        //当前时间
        $nowtime = time();
        $uptotime = time()+200;
        //提前5分钟
        $remindtime = time()+300;
        $remindtimetwo = time()+300+200;

        $someOneDayHour = $this->DataControl->selectOne("
            SELECT a.hourrooms_id 
            FROM tkl_lineclass_hourrooms as a
            WHERE  a.hourrooms_ischecking = 0 and a.hourrooms_starttime >= '{$remindtime}' and a.hourrooms_starttime < '{$remindtimetwo}'  and a.hourrooms_threenumber <> ''
                        and not exists ( select 1 from tkl_lineclass_hourrooms_remind_log as b where a.hourrooms_id = b.hourrooms_id and b.log_type = '3' limit 0,1)
            limit 0,1
        ");
        if($someOneDayHour){
            $remindlog = $this->DataControl->query("
                        insert into tkl_lineclass_hourrooms_remind_log(hourrooms_id,hourrooms_starttime,log_type,log_tilte,log_createtime,log_uptotime) 
                        SELECT a.hourrooms_id,a.hourrooms_starttime,'3','上课提醒','{$nowtime}','{$uptotime}' 
                        FROM tkl_lineclass_hourrooms as a
                        WHERE  a.hourrooms_ischecking = 0 and a.hourrooms_starttime >= '{$remindtime}' and a.hourrooms_starttime < '{$remindtimetwo}' 
                        and not exists ( select 1 from tkl_lineclass_hourrooms_remind_log as b where a.hourrooms_id = b.hourrooms_id and b.log_type = '3' limit 0,1)
                    ");
            if($remindlog){
                echo '队列添加成功';
            }else{
                echo '队列添加失败';
            }
        }else{
            echo '暂无需要提前五分钟提醒的课时';
        }
    }
    //开课后 5 分钟发短信
    function addRemindHourFiveMinView(){
        //当前时间
        $nowtime = time();
        $uptotime = time()+200;
        //开课后 5 分钟发短信
        $remindtime = time()-300;
        $remindtimetwo = time()-300-200;

        $someOneDayHour = $this->DataControl->selectOne("
            SELECT a.hourrooms_id 
            FROM tkl_lineclass_hourrooms as a
            WHERE  a.hourrooms_ischecking = 0 and a.hourrooms_starttime >= '{$remindtimetwo}' and a.hourrooms_starttime < '{$remindtime}'  and a.hourrooms_threenumber <> ''
                        and not exists ( select 1 from tkl_lineclass_hourrooms_remind_log as b where a.hourrooms_id = b.hourrooms_id and b.log_type = '4' limit 0,1)
            limit 0,1
        ");
        if($someOneDayHour){
            $remindlog = $this->DataControl->query("
                        insert into tkl_lineclass_hourrooms_remind_log(hourrooms_id,hourrooms_starttime,log_type,log_tilte,log_createtime,log_uptotime) 
                        SELECT a.hourrooms_id,a.hourrooms_starttime,'4','上课提醒','{$nowtime}','{$uptotime}' 
                        FROM tkl_lineclass_hourrooms as a
                        WHERE  a.hourrooms_ischecking = 0 and a.hourrooms_starttime >= '{$remindtimetwo}' and a.hourrooms_starttime < '{$remindtime}' 
                        and not exists ( select 1 from tkl_lineclass_hourrooms_remind_log as b where a.hourrooms_id = b.hourrooms_id and b.log_type = '4' limit 0,1)
                    ");
            if($remindlog){
                echo '队列添加成功';
            }else{
                echo '队列添加失败';
            }
        }else{
            echo '暂无需要提前五分钟提醒的课时';
        }
    }

    //学生  课时短信提醒业务 -- 课时提前一天 系统自动发生短信提醒
    function remindOneDayHourStudyView(){
        $type = '1';
        //当前时间
        $nowtime = time();

        $remindlogOne = $this->DataControl->selectOne(" SELECT a.log_id,a.hourrooms_id,a.hourrooms_starttime,a.log_succnum,a.log_failnum 
            FROM tkl_lineclass_hourrooms_remind_log as a 
            WHERE  a.log_type = '{$type}' and a.log_isremind = '0' and a.log_uptotime > '{$nowtime}' 
            limit 0,1
            ");
        if(!$remindlogOne){
            $res = array('error' => '1', 'errortip' => '暂无需要提前一天通知的课程');
            ajax_return($res);
        }
        $someOneDayStu = $this->DataControl->selectClear("
            SELECT b.hourrooms_id,'{$remindlogOne['hourrooms_starttime']}' as hourrooms_starttime,b.student_id,c.family_mobile,b.hourroomstudy_id,d.student_cnname,d.student_branch
            FROM tkl_lineclass_hourroomstudy as b,smc_student_family as c,smc_student as d
            WHERE  b.hourrooms_id = '{$remindlogOne['hourrooms_id']}'
                and not exists(select 1 from tkl_mislog as m where b.hourrooms_id = m.hourrooms_id and m.mislog_type = '{$type}' and b.student_id = m.student_id  limit 0,1)
                and b.student_id = c.student_id and c.family_isdefault = 1   
                and b.student_id = d.student_id 
            limit 0,25
        ");
        if(!$someOneDayStu){
            $mlog = array();
            $mlog['log_isremind'] = 1;
            $mlog['log_remindtime'] = time();
            $this->DataControl->updateData("tkl_lineclass_hourrooms_remind_log","log_id = '{$remindlogOne['log_id']}'",$mlog);

            $res = array('error' => '1', 'errortip' => '暂无需要提前一天通知的课程');
            ajax_return($res);
        }
        $succ = 0;
        $fail = 0;
        foreach ($someOneDayStu as $someOneDayStuVar){
            //调取发短信的函数
            $sendmisrz = $this->DataControl->getFieldOne('tkl_mislog', "mislog_time", "student_id='{$someOneDayStuVar['student_id']}' and hourrooms_id='{$someOneDayStuVar['hourrooms_id']}' and mislog_type='{$type}' and mislog_mobile='{$someOneDayStuVar['family_mobile']}' and mislog_tilte = '上课提醒'", "order by mislog_time DESC");
            if ($sendmisrz && (time() - $sendmisrz['mislog_time']) < 300) {
                $fail++;
            } else {
                $hourStime = date("m月d日H时i分",$someOneDayStuVar['hourrooms_starttime']);
                $mobile = trim($someOneDayStuVar['family_mobile']);
                $tilte = "上课提醒";
                $contxt = "尊敬的吉的堡学员家长您好，{$someOneDayStuVar['student_cnname']}同学在{$hourStime}有一节线上课程，为确保学习效果，烦请您提前做好准备，确保孩子在安静，网络顺畅的环境中参与课堂，感谢您的配合！";
                //短信发送
                if ($this->Sendmisgo($mobile, $contxt, $tilte, '', '8888',$type,$someOneDayStuVar['student_id'],$someOneDayStuVar['hourrooms_id'])) {
                    $succ++;
                } else {
                    $fail++;
                }
            }
        }
        $mlog = array();
        $mlog['log_succnum'] = $succ+$remindlogOne['log_succnum'];
        $mlog['log_failnum'] = $fail+$remindlogOne['log_failnum'];
        $this->DataControl->updateData("tkl_lineclass_hourrooms_remind_log","log_id = '{$remindlogOne['log_id']}'",$mlog);
    }
    //课时短信提醒业务 -- 课时提前2小时 系统自动发生短信提醒
    function remindTwoHoursHourStudyView(){
        $type = '2';
        //当前时间
        $nowtime = time();

        $remindlogOne = $this->DataControl->selectOne(" SELECT a.log_id,a.hourrooms_id,a.hourrooms_starttime,a.log_succnum,a.log_failnum  
            FROM tkl_lineclass_hourrooms_remind_log as a 
            WHERE  a.log_type = '{$type}' and a.log_isremind = '0' and a.log_uptotime > '{$nowtime}' 
            limit 0,1
            ");
        if(!$remindlogOne){
            $res = array('error' => '1', 'errortip' => '暂无需要提前两小时通知的课程');
            ajax_return($res);
        }
        $someOneDayStu = $this->DataControl->selectClear("
            SELECT b.hourrooms_id,'{$remindlogOne['hourrooms_starttime']}' as hourrooms_starttime,b.student_id,c.family_mobile,b.hourroomstudy_id,d.student_cnname,d.student_branch
            FROM tkl_lineclass_hourroomstudy as b,smc_student_family as c,smc_student as d
            WHERE  b.hourrooms_id = '{$remindlogOne['hourrooms_id']}'
                and not exists(select 1 from tkl_mislog as m where b.hourrooms_id = m.hourrooms_id and m.mislog_type = '{$type}' and b.student_id = m.student_id  limit 0,1)
                and b.student_id = c.student_id and c.family_isdefault = 1   
                and b.student_id = d.student_id 
            limit 0,25
        ");
        if(!$someOneDayStu){
            $mlog = array();
            $mlog['log_isremind'] = 1;
            $mlog['log_remindtime'] = time();
            $this->DataControl->updateData("tkl_lineclass_hourrooms_remind_log","log_id = '{$remindlogOne['log_id']}'",$mlog);

            $res = array('error' => '1', 'errortip' => '暂无需要提前两小时通知的课程');
            ajax_return($res);
        }
        $succ = 0;
        $fail = 0;
        foreach ($someOneDayStu as $someOneDayStuVar){
            //调取发短信的函数
            $sendmisrz = $this->DataControl->getFieldOne('tkl_mislog', "mislog_time", "student_id='{$someOneDayStuVar['student_id']}' and hourrooms_id='{$someOneDayStuVar['hourrooms_id']}' and mislog_type='{$type}' and mislog_mobile='{$someOneDayStuVar['family_mobile']}' and mislog_tilte = '上课提醒'", "order by mislog_time DESC");
            if ($sendmisrz && (time() - $sendmisrz['mislog_time']) < 300) {
                $fail++;
            } else {
                $mobile = trim($someOneDayStuVar['family_mobile']);
                $tilte = "上课提醒";
                $contxt = "尊敬的吉的堡学员家长您好，{$someOneDayStuVar['student_cnname']}同学今天的线上课会在2小时后开始，为确保学习效果，烦请您提前做好准备，确保孩子在安静，网络顺畅的环境中参与课堂，感谢您的配合！";
                //短信发送
                if ($this->Sendmisgo($mobile, $contxt, $tilte, '', '8888',$type,$someOneDayStuVar['student_id'],$someOneDayStuVar['hourrooms_id'])) {
                    $succ++;
                } else {
                    $fail++;
                }
            }
        }
        $mlog = array();
        $mlog['log_succnum'] = $succ+$remindlogOne['log_succnum'];
        $mlog['log_failnum'] = $fail+$remindlogOne['log_failnum'];
        $this->DataControl->updateData("tkl_lineclass_hourrooms_remind_log","log_id = '{$remindlogOne['log_id']}'",$mlog);
    }
    //课时短信提醒业务 -- 课时提前5分钟 系统自动发生短信提醒
    function remindFiveMinHourStudyView(){
        $type = '3';
        //当前时间
        $nowtime = time();

        $remindlogOne = $this->DataControl->selectOne(" SELECT a.log_id,a.hourrooms_id,a.hourrooms_starttime,a.log_succnum,a.log_failnum 
            FROM tkl_lineclass_hourrooms_remind_log as a 
            WHERE  a.log_type = '{$type}' and a.log_isremind = '0' and a.log_uptotime > '{$nowtime}' 
            limit 0,1
            ");
        if(!$remindlogOne){
            $res = array('error' => '1', 'errortip' => '暂无需要提前5分钟通知的课程');
            ajax_return($res);
        }
        $someOneDayStu = $this->DataControl->selectClear("
            SELECT b.hourrooms_id,'{$remindlogOne['hourrooms_starttime']}' as hourrooms_starttime,b.student_id,c.family_mobile,b.hourroomstudy_id,d.student_cnname,d.student_branch
            FROM tkl_lineclass_hourroomstudy as b,smc_student_family as c,smc_student as d
            WHERE  b.hourrooms_id = '{$remindlogOne['hourrooms_id']}'
                and not exists(select 1 from tkl_mislog as m where b.hourrooms_id = m.hourrooms_id and m.mislog_type = '{$type}' and b.student_id = m.student_id  limit 0,1)
                and b.student_id = c.student_id and c.family_isdefault = 1   
                and b.student_id = d.student_id 
            limit 0,25
        ");
        if(!$someOneDayStu){
            $mlog = array();
            $mlog['log_isremind'] = 1;
            $mlog['log_remindtime'] = time();
            $this->DataControl->updateData("tkl_lineclass_hourrooms_remind_log","log_id = '{$remindlogOne['log_id']}'",$mlog);

            $res = array('error' => '1', 'errortip' => '暂无需要5分钟通知的课程');
            ajax_return($res);
        }
        $succ = 0;
        $fail = 0;
        foreach ($someOneDayStu as $someOneDayStuVar){
            //调取发短信的函数
            $sendmisrz = $this->DataControl->getFieldOne('tkl_mislog', "mislog_time", "student_id='{$someOneDayStuVar['student_id']}' and hourrooms_id='{$someOneDayStuVar['hourrooms_id']}' and mislog_type='{$type}' and mislog_mobile='{$someOneDayStuVar['family_mobile']}' and mislog_tilte = '上课提醒'", "order by mislog_time DESC");
            if ($sendmisrz && (time() - $sendmisrz['mislog_time']) < 300) {
                $fail++;
            } else {
                $mobile = trim($someOneDayStuVar['family_mobile']);
                $tilte = "上课提醒";
                $contxt = "尊敬的吉的堡学员家长您好，{$someOneDayStuVar['student_cnname']}同学今天的线上课还有5分钟就要开始啦，请让孩子做好课前准备，提前进入教室，愉快地享受今天的课程吧~";
                //短信发送
                if ($this->Sendmisgo($mobile, $contxt, $tilte, '', '8888',$type,$someOneDayStuVar['student_id'],$someOneDayStuVar['hourrooms_id'])) {
                    $succ++;
                } else {
                    $fail++;
                }
            }
        }
        $mlog = array();
        $mlog['log_succnum'] = $succ+$remindlogOne['log_succnum'];
        $mlog['log_failnum'] = $fail+$remindlogOne['log_failnum'];
        $this->DataControl->updateData("tkl_lineclass_hourrooms_remind_log","log_id = '{$remindlogOne['log_id']}'",$mlog);
    }
    //课时短信提醒业务 -- 课时开始5分钟后 系统自动发生短信提醒，未进入教室的家长
    function remindHourFiveMinHourStudyView(){
        $type = '4';
        //当前时间
        $nowtime = time();

        $remindlogOne = $this->DataControl->selectOne(" SELECT a.log_id,a.hourrooms_id,a.hourrooms_starttime,a.log_succnum,a.log_failnum 
            FROM tkl_lineclass_hourrooms_remind_log as a 
            WHERE  a.log_type = '{$type}' and a.log_isremind = '0' and a.log_uptotime > '{$nowtime}' 
            limit 0,1
            ");
        if(!$remindlogOne){
            $res = array('error' => '1', 'errortip' => '暂无需要开课后提醒上课的课程');
            ajax_return($res);
        }
        $someOneDayStu = $this->DataControl->selectClear("
            SELECT b.hourrooms_id,b.student_id,c.family_mobile,b.hourroomstudy_id,d.student_cnname,d.student_branch
            FROM tkl_lineclass_hourroomstudy as b,smc_student_family as c,smc_student as d,tkl_lineclass_hourrooms as e  
            WHERE  b.hourrooms_id = '{$remindlogOne['hourrooms_id']}'
                and not exists(select 1 from tkl_mislog as m where b.hourrooms_id = m.hourrooms_id and m.mislog_type = '{$type}' and b.student_id = m.student_id  limit 0,1)
                and b.student_id = c.student_id and c.family_isdefault = 1   
                and b.student_id = d.student_id 
                and b.hourrooms_id = e.hourrooms_id 
                and not exists ( select 1 from tkl_lineclass_hourrooms_accesslog as h where h.accesslog_serial = e.hourrooms_threenumber and h.accesslog_userid = d.student_branch limit 0,1)
            limit 0,25
        ");
        if(!$someOneDayStu){
            $mlog = array();
            $mlog['log_isremind'] = 1;
            $mlog['log_remindtime'] = time();
            $this->DataControl->updateData("tkl_lineclass_hourrooms_remind_log","log_id = '{$remindlogOne['log_id']}'",$mlog);

            $res = array('error' => '1', 'errortip' => '暂无需要开课后提醒上课的课程');
            ajax_return($res);
        }
        $succ = 0;
        $fail = 0;
        foreach ($someOneDayStu as $someOneDayStuVar){
            //调取发短信的函数
            $sendmisrz = $this->DataControl->getFieldOne('tkl_mislog', "mislog_time", "student_id='{$someOneDayStuVar['student_id']}' and hourrooms_id='{$someOneDayStuVar['hourrooms_id']}' and mislog_type='{$type}' and mislog_mobile='{$someOneDayStuVar['family_mobile']}' and mislog_tilte = '上课提醒'", "order by mislog_time DESC");
            if ($sendmisrz && (time() - $sendmisrz['mislog_time']) < 300) {
                $fail++;
            } else {
                $mobile = trim($someOneDayStuVar['family_mobile']);
                $tilte = "上课提醒";
                $contxt = "尊敬的吉的堡学员家长您好，线上课程已经开始5分钟了，后台看到{$someOneDayStuVar['student_cnname']}同学还未进入教室，烦请督促孩子尽快进入教室，参与今天课程的学习哦~";
                //短信发送
                if ($this->Sendmisgo($mobile, $contxt, $tilte, '', '8888',$type,$someOneDayStuVar['student_id'],$someOneDayStuVar['hourrooms_id'])) {
                    $succ++;
                } else {
                    $fail++;
                }
            }
        }
        $mlog = array();
        $mlog['log_succnum'] = $succ+$remindlogOne['log_succnum'];
        $mlog['log_failnum'] = $fail+$remindlogOne['log_failnum'];
        $this->DataControl->updateData("tkl_lineclass_hourrooms_remind_log","log_id = '{$remindlogOne['log_id']}'",$mlog);
    }


    //针对课时结束获取  2 答题、 3 红包、 4 礼物详情
    function addGethourroomsTwoListView(){
        //当前时间
        $nowtime = time();
        $uptotime = time()+86400;

        $someHour = $this->DataControl->selectOne("
            SELECT a.hourrooms_id 
            FROM tkl_lineclass_hourrooms as a
            WHERE (a.hourrooms_endtime+600) < '{$nowtime}' and (a.hourrooms_endtime+1800) > '{$nowtime}' and a.hourrooms_threenumber <> ''  
                and not exists ( select 1 from tkl_lineclass_hourrooms_check_log as b where a.hourrooms_id = b.hourrooms_id and b.log_type = '2' limit 0,1)
        ");

        if($someHour){
            $remindlog = $this->DataControl->query("
                        insert into tkl_lineclass_hourrooms_check_log(hourrooms_id,hourrooms_starttime,hourrooms_endtime,log_type,log_tilte,log_createtime,log_uptotime) 
                        SELECT a.hourrooms_id,a.hourrooms_starttime,a.hourrooms_endtime,'2','统计答题','{$nowtime}','{$uptotime}'
                        FROM tkl_lineclass_hourrooms as a
                        WHERE (a.hourrooms_endtime+600) < '{$nowtime}' and (a.hourrooms_endtime+1800) > '{$nowtime}' and a.hourrooms_threenumber <> ''  
                        and not exists ( select 1 from tkl_lineclass_hourrooms_check_log as b where a.hourrooms_id = b.hourrooms_id and b.log_type = '2' limit 0,1)
                    ");
            if($remindlog){
                echo '队列添加成功';
            }else{
                echo '队列添加失败';
            }
        }else{
            echo '暂无需要统计答题的课时';
        }
    }
    //针对课时结束获取  2 答题、 3 红包、 4 礼物详情
    function addGethourroomsThreeListView(){
        //当前时间
        $nowtime = time();
        $uptotime = time()+86400;

        $someHour = $this->DataControl->selectOne("
            SELECT a.hourrooms_id 
            FROM tkl_lineclass_hourrooms as a
            WHERE  (a.hourrooms_endtime+600) < '{$nowtime}' and (a.hourrooms_endtime+1800) > '{$nowtime}' and a.hourrooms_threenumber <> ''  
                and not exists ( select 1 from tkl_lineclass_hourrooms_check_log as b where a.hourrooms_id = b.hourrooms_id and b.log_type = '3' limit 0,1)
        ");

        if($someHour){
            $remindlog = $this->DataControl->query("
                        insert into tkl_lineclass_hourrooms_check_log(hourrooms_id,hourrooms_starttime,hourrooms_endtime,log_type,log_tilte,log_createtime,log_uptotime) 
                        SELECT a.hourrooms_id,a.hourrooms_starttime,a.hourrooms_endtime,'3','统计红包','{$nowtime}','{$uptotime}'
                        FROM tkl_lineclass_hourrooms as a
                        WHERE  (a.hourrooms_endtime+600) < '{$nowtime}' and (a.hourrooms_endtime+1800) > '{$nowtime}' and a.hourrooms_threenumber <> ''  
                        and not exists ( select 1 from tkl_lineclass_hourrooms_check_log as b where a.hourrooms_id = b.hourrooms_id and b.log_type = '3' limit 0,1)
                    ");
            if($remindlog){
                echo '队列添加成功';
            }else{
                echo '队列添加失败';
            }
        }else{
            echo '暂无需要统计红包的课时';
        }
    }
    //针对课时结束获取  2 答题、 3 红包、 4 礼物详情
    function addGethourroomsFourListView(){
        //当前时间
        $nowtime = time();
        $uptotime = time()+86400;

        $someHour = $this->DataControl->selectOne("
            SELECT a.hourrooms_id 
            FROM tkl_lineclass_hourrooms as a
            WHERE  (a.hourrooms_endtime+600) < '{$nowtime}' and (a.hourrooms_endtime+1800) > '{$nowtime}' and a.hourrooms_threenumber <> ''  
                and not exists ( select 1 from tkl_lineclass_hourrooms_check_log as b where a.hourrooms_id = b.hourrooms_id and b.log_type = '4' limit 0,1)
        ");

        if($someHour){
            $remindlog = $this->DataControl->query("
                        insert into tkl_lineclass_hourrooms_check_log(hourrooms_id,hourrooms_starttime,hourrooms_endtime,log_type,log_tilte,log_createtime,log_uptotime) 
                        SELECT a.hourrooms_id,a.hourrooms_starttime,a.hourrooms_endtime,'4','统计礼物','{$nowtime}','{$uptotime}'
                        FROM tkl_lineclass_hourrooms as a
                        WHERE (a.hourrooms_endtime+600) < '{$nowtime}' and (a.hourrooms_endtime+1800) > '{$nowtime}' and a.hourrooms_threenumber <> ''  
                        and not exists ( select 1 from tkl_lineclass_hourrooms_check_log as b where a.hourrooms_id = b.hourrooms_id and b.log_type = '4' limit 0,1)
                    ");
            if($remindlog){
                echo '队列添加成功';
            }else{
                echo '队列添加失败';
            }
        }else{
            echo '暂无需要统计礼物的课时';
        }
    }


    //记录答题信息
    function addHourroomsQuestionView(){
        //当前时间
        $nowtime = time();

        $checklogOne = $this->DataControl->selectOne(" SELECT a.log_id,a.hourrooms_id,a.hourrooms_starttime,a.hourrooms_endtime,b.hourrooms_threenumber
            FROM tkl_lineclass_hourrooms_check_log as a,tkl_lineclass_hourrooms as b
            WHERE  a.log_type = '2' and a.log_ischeck = '0' and a.log_uptotime > '{$nowtime}' and a.hourrooms_id = b.hourrooms_id
            limit 0,1
            ");
        if(!$checklogOne){
            $res = array('error' => '1', 'errortip' => '暂无需要记录答题信息的课程');
            ajax_return($res);
        }

//        $checklogOne['log_id']= '300';
//        $checklogOne['hourrooms_threenumber']= '1989506245';
        //获取答题信息
        $TalkcloudModel = new \Model\Tlk\TalkcloudModel($this->FYCOMID);
        $putArray = array();
        $putArray['serial'] = $checklogOne['hourrooms_threenumber'];
        $putArray['log_id'] = $checklogOne['log_id'];
        $DataInfo = $TalkcloudModel->getStuAnswers($putArray);
        echo $DataInfo;
    }

    //记录红包信息
    function addHourroomsRedpacketView(){
        //当前时间
        $nowtime = time();

        $checklogOne = $this->DataControl->selectOne(" SELECT a.log_id,a.hourrooms_id,a.hourrooms_starttime,a.hourrooms_endtime,b.hourrooms_threenumber
            FROM tkl_lineclass_hourrooms_check_log as a,tkl_lineclass_hourrooms as b
            WHERE  a.log_type = '3' and a.log_ischeck = '0' and a.log_uptotime > '{$nowtime}' and a.hourrooms_id = b.hourrooms_id
            limit 0,1
            ");
        if(!$checklogOne){
            $res = array('error' => '1', 'errortip' => '暂无需要记录红包信息的课程');
            ajax_return($res);
        }

//        $checklogOne['log_id']= '1';
//        $checklogOne['hourrooms_threenumber']= '1555444745';
        //获取答题信息
        $TalkcloudModel = new \Model\Tlk\TalkcloudModel($this->FYCOMID);

        $putArray = array();
        $putArray['serial'] = $checklogOne['hourrooms_threenumber'];
        $putArray['page'] = 1;
        $putArray['log_id'] = $checklogOne['log_id'];
        $DataInfo = $TalkcloudModel->getStuRedpacket($putArray);
        echo $DataInfo;
    }

    //记录礼物信息
    function addHourroomsGiftView(){
        //当前时间
        $nowtime = time();

        $checklogOne = $this->DataControl->selectOne(" SELECT a.log_id,a.hourrooms_id,a.hourrooms_starttime,a.hourrooms_endtime,b.hourrooms_threenumber
            FROM tkl_lineclass_hourrooms_check_log as a,tkl_lineclass_hourrooms as b
            WHERE  a.log_type = '4' and a.log_ischeck = '0' and a.log_uptotime > '{$nowtime}' and a.hourrooms_id = b.hourrooms_id
            limit 0,1
            ");
        if(!$checklogOne){
            $res = array('error' => '1', 'errortip' => '暂无需要记录礼物信息的课程');
            ajax_return($res);
        }

//        $checklogOne['log_id']= '602';
//        $checklogOne['hourrooms_threenumber']= '815028006';
        //获取答题信息
        $TalkcloudModel = new \Model\Tlk\TalkcloudModel($this->FYCOMID);

        $putArray = array();
        $putArray['serial'] = $checklogOne['hourrooms_threenumber'];
        $putArray['page'] = 1;
        $putArray['log_id'] = $checklogOne['log_id'];
        $DataInfo = $TalkcloudModel->getStuGift($putArray);
        echo $DataInfo;
    }




    //计算在线时长
    function countOnlineTimebakView(){
        //当前时间
        $nowtime = time();


        $someCheckStu = $this->DataControl->selectClear("
            select a.hourroomstudy_id,a.student_id,b.student_branch,e.hourrooms_starttime,e.hourrooms_endtime,e.hourrooms_threenumber  
            from tkl_lineclass_hourroomstudy as a, smc_student as b,tkl_lineclass_hourrooms as e  
            where a.hourrooms_id = '605'
                and a.student_id = b.student_id 
                and a.hourrooms_id = e.hourrooms_id  
            limit 0,25
        ");
//        if(!$someCheckStu){
//            $mlog = array();
//            $mlog['hourroomstudy_checkin'] = '-1';
//            $mlog['hourroomstudy_updatatime'] = time();
//            $this->DataControl->updateData("tkl_lineclass_hourroomstudy","hourrooms_id = '{$checklogOne['hourrooms_id']}' and hourroomstudy_checkin = '0'  ",$mlog);
//
//            $mlog = array();
//            $mlog['log_ischeck'] = 1;
//            $mlog['log_checktime'] = time();
//            $this->DataControl->updateData("tkl_lineclass_hourrooms_check_log","log_id = '{$checklogOne['log_id']}'",$mlog);
//
//            $hourone = array();
//            $hourone['hourrooms_ischecking'] = 1;
//            $hourone['hourrooms_updatatime'] = time();
//            $this->DataControl->updateData("tkl_lineclass_hourrooms","hourrooms_id = '{$checklogOne['hourrooms_id']}'",$hourone);
//
//            $res = array('error' => '1', 'errortip' => '暂无需要自动考勤的学生');
//            ajax_return($res);
//        }

        $succ = 0;
        $fail = 0;
        foreach ($someCheckStu as $someCheckVar){

            $hourStime = $someCheckVar['hourrooms_starttime'];
            $hourEtime = $someCheckVar['hourrooms_endtime'];

//            $sql = "
//            SELECT x.accesslog_identification,sum(if((x.laveaa = 0  or x.laveaa>'{$hourEtime}'),'{$hourEtime}',laveaa)-if((x.joinaa = 0 or x.joinaa<'{$hourStime}'),'{$hourStime}',joinaa)) as allsecond
//            FROM (
//                SELECT accesslog_identification,
//                max(CASE `accesslog_status` WHEN '1' THEN if(((accesslog_timestamp>'{$hourStime}' and accesslog_timestamp<'{$hourEtime}') or accesslog_timestamp<'{$hourStime}') ,accesslog_timestamp,0) ELSE 0 END) as 'joinaa',
//                max(CASE `accesslog_status` WHEN '0' THEN if(((accesslog_timestamp>'{$hourStime}' and accesslog_timestamp<'{$hourEtime}') or accesslog_timestamp>'{$hourEtime}'),accesslog_timestamp,0) ELSE 0 END) as 'laveaa'
//                FROM tkl_lineclass_hourrooms_accesslog
//                where accesslog_userid = '{$someCheckVar['student_branch']}' and accesslog_serial = '{$someCheckVar['hourrooms_threenumber']}'
//                GROUP BY accesslog_identification
//            ) as x WHERE (x.joinaa >= 0 or x.laveaa >= 0) and x.accesslog_identification <> '' and (x.joinaa+x.laveaa)>='{$hourStime}' and x.accesslog_identification is not NULL
//            ";

            $sql = "
            SELECT x.accesslog_identification,x.joinaa,x.laveaa,(if(x.laveaa>'{$hourEtime}','{$hourEtime}',laveaa)-if(x.joinaa<'{$hourStime}','{$hourStime}',joinaa)) as allsecond 
            FROM ( 
                SELECT accesslog_identification,
                max(CASE `accesslog_status` WHEN '1' THEN if(accesslog_timestamp<'{$hourStime}',accesslog_timestamp,0) ELSE 0 END) as 'joinaa',
                max(CASE `accesslog_status` WHEN '0' THEN if(accesslog_timestamp>'{$hourEtime}',accesslog_timestamp,0) ELSE 0 END) as 'laveaa'
                FROM tkl_lineclass_hourrooms_accesslog  
                where accesslog_userid = '{$someCheckVar['student_branch']}' and accesslog_serial = '{$someCheckVar['hourrooms_threenumber']}'
                GROUP BY accesslog_identification  
            ) as x WHERE  x.joinaa <='{$hourStime}' and x.laveaa >='{$hourEtime}' limit 0,1
            ";
            echo $sql;die;
            //整节课没有进出过的 没计算到 该上边的算法
//            $sql = "
//            SELECT x.accesslog_identification,sum(if(x.laveaa = 0,'{$hourEtime}',laveaa)-if(x.joinaa = 0,'{$hourStime}',joinaa)) as allsecond
//            FROM (
//                SELECT accesslog_identification,
//                max(CASE `accesslog_status` WHEN '1' THEN if((accesslog_timestamp>'{$hourStime}' and accesslog_timestamp<'{$hourEtime}'),accesslog_timestamp,0) ELSE 0 END) as 'joinaa',
//                max(CASE `accesslog_status` WHEN '0' THEN if((accesslog_timestamp>'{$hourStime}' and accesslog_timestamp<'{$hourEtime}'),accesslog_timestamp,0) ELSE 0 END) as 'laveaa'
//                FROM tkl_lineclass_hourrooms_accesslog
//                where accesslog_userid = '{$someCheckVar['student_branch']}' and accesslog_serial = '{$someCheckVar['hourrooms_threenumber']}'
//                GROUP BY accesslog_identification
//            ) as x WHERE (x.joinaa >= 0 or x.laveaa >= 0) and x.accesslog_identification <> '' and (x.joinaa+x.laveaa)>='{$hourStime}' and x.accesslog_identification is not NULL
//            ";
            $StuOne = $this->DataControl->selectOne($sql);
            if($StuOne['allsecond']){
                $theminute = sprintf("%.2f",$StuOne['allsecond']/60);
                if($theminute <= 5){
                    $checkin = '-1';
                }elseif($theminute > 45){
                    $checkin = '1';
                }else{
                    $checkin = '2';
                }
                $mlog = array();
                $mlog['hourroomstudy_checkin'] = $checkin;
                $mlog['hourroomstudy_duration'] = $theminute;
                $mlog['hourroomstudy_updatatime'] = time();
                $this->DataControl->updateData("tkl_lineclass_hourroomstudy","hourroomstudy_id = '{$someCheckVar['hourroomstudy_id']}'",$mlog);
            }else{
                $mlog = array();
                $mlog['hourroomstudy_checkin'] = -1;
                $mlog['hourroomstudy_updatatime'] = time();
                $this->DataControl->updateData("tkl_lineclass_hourroomstudy","hourroomstudy_id = '{$someCheckVar['hourroomstudy_id']}'",$mlog);
            }
        }
//        $mlog = array();
//        $mlog['log_succnum'] = $succ+$checklogOne['log_succnum'];
//        $mlog['log_failnum'] = $fail+$checklogOne['log_failnum'];
//        $this->DataControl->updateData("tkl_lineclass_hourrooms_check_log","log_id = '{$checklogOne['log_id']}'",$mlog);

    }
}