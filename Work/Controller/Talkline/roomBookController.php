<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/3/11
 * Time: 15:30
 */

namespace Work\Controller\Talkline;


class roomBookController extends viewTpl{
    public $data;
    public $Viewhtm;
    public $u;
    public $t;
    public $c;
    public $iparenter;

    function __construct() {
        parent::__construct ();
        if(!$this->checkParenter() && $this->router->getUrl() !=='Entry'){
            $this->LoginView();
        }
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
        $this->iparenter = $this->StafferLogin;
        $this->Viewhtm = $this->router->getController()."/".$this->router->getUrl().".htm";

        $this->Jdbstuapp = new \Dbsqlplay("rm-bp100z52ieb406w73uo.mysql.rds.aliyuncs.com", "qiquonlineuser", "KidTimeqiqu2018", "stuappdata");
    }
    //检测用户是否登录 session 检测parenterLogin
    function checkParenter(){
        if($this->intSession->getCookiearray('parenter') && count($this->intSession->getCookiearray('parenter')) > 0){
            $login_parenter = $this->intSession->getCookiearray('parenter');
            if(!empty($login_parenter) && $login_parenter){
                $iparenter = $this->DataControl->selectOne("SELECT s.student_id FROM
	smc_student AS s,
	smc_student_study AS d,
	smc_class AS c
WHERE
	s.student_id = d.student_id
AND d.class_id = c.class_id
AND c.class_branch = '{$login_parenter['class_branch']}'
AND  s.student_branch = '{$login_parenter['student_branch']}'");
                if(!$iparenter){
                    return false;
                }else{
                    return true;
                }
            }else{
                return false;
            }
        }else{
            return false;
        }
    }
    function HomeView(){
        $login_parenter = $this->intSession->getCookiearray('parenter');
        $iparenter = $this->DataControl->selectOne("SELECT
	s.student_id,s.student_branch,s.student_cnname,s.student_enname,c.class_cnname,c.class_enname
FROM
	smc_student AS s,
	smc_student_study AS d,
	smc_class AS c
WHERE
	s.student_id = d.student_id
AND d.class_id = c.class_id
AND c.class_branch = '{$login_parenter['class_branch']}'
AND  s.student_branch = '{$login_parenter['student_branch']}'");
        $this->smarty->assign("iparenter",$iparenter);

        $classOne = $this->DataControl->selectOne("SELECT
	c.class_id,c.class_cnname,c.class_enname,o.course_branch
FROM smc_class AS c,smc_course AS o
WHERE
	c.course_id = o.course_id AND c.class_branch = '{$login_parenter['class_branch']}'");
        $this->smarty->assign("classOne",$classOne);


        $mediaList = $this->Jdbstuapp->getList("app_classcodehour_media","classcode_branch='{$classOne['course_branch']}'"," ORDER BY media_sort ASC");
        $this->smarty->assign("mediaList",$mediaList);

    }

    function mediaLookView(){
        $request = Input('get.','','trim,addslashes');
        $login_parenter = $this->intSession->getCookiearray('parenter');
        $iparenter = $this->DataControl->selectOne("SELECT
	s.student_id,s.student_branch,s.student_cnname,s.student_enname,c.class_cnname,c.class_enname
FROM
	smc_student AS s,
	smc_student_study AS d,
	smc_class AS c
WHERE
	s.student_id = d.student_id
AND d.class_id = c.class_id
AND c.class_branch = '{$login_parenter['class_branch']}'
AND  s.student_branch = '{$login_parenter['student_branch']}'");
        $this->smarty->assign("iparenter",$iparenter);

        $classOne = $this->DataControl->selectOne("SELECT
	c.class_id,c.class_cnname,c.class_enname,o.course_branch
FROM smc_class AS c,smc_course AS o
WHERE
	c.course_id = o.course_id AND c.class_branch = '{$login_parenter['class_branch']}'");
        $this->smarty->assign("classOne",$classOne);

        $dataVar = $this->Jdbstuapp->selectOne("SELECT m.* FROM app_classcodehour_media AS m WHERE m.media_id='{$request['media_id']}' AND m.classcode_branch='{$classOne['course_branch']}' limit 0,1");
        $this->smarty->assign("dataVar",$dataVar);
        $mediaList = $this->Jdbstuapp->getList("app_classcodehour_media","classcode_branch='{$classOne['course_branch']}'"," ORDER BY media_sort ASC");
        $this->smarty->assign("mediaList",$mediaList);
    }

    function EntryView(){
        $this->c = 'Entry';
        $request = Input('post.','','trim,addslashes');
        if(!isset($request['school_branch']) || $request['school_branch'] ==''){
            ajax_return(array('error' => 1,'errortip' => "请输入您的校园编号!","bakfuntion"=>"errormotify"));
        }
        if(!isset($request['student_branch']) || $request['student_branch'] ==''){
            ajax_return(array('error' => 1,'errortip' => "请输入您的联系手机或学员编号!","bakfuntion"=>"errormotify"));
        }
        if(!isset($request['class_branch']) || $request['class_branch'] ==''){
            ajax_return(array('error' => 1,'errortip' => "请输入您班级编号代码!","bakfuntion"=>"errormotify"));
        }

        if(!$this->DataControl->getFieldOne("smc_school","school_id","school_branch='{$request['school_branch']}'")){
            ajax_return(array('error' => 1,'errortip' => "请确认您的校园编号是否正确!","bakfuntion"=>"errormotify"));
        }

        $studentOne = $this->DataControl->selectOne("SELECT
	s.student_id,s.student_branch
FROM
	smc_student AS s,
	smc_student_study AS d,
	smc_class AS c
WHERE
	s.student_id = d.student_id
AND d.class_id = c.class_id
AND c.class_branch = '{$request['class_branch']}'
AND (
	s.student_branch = '{$request['student_branch']}'
	OR s.student_id IN (
		SELECT
			f.student_id
		FROM
			smc_student_family AS f
		WHERE
			f.family_mobile = '{$request['student_branch']}'
	)
)");
        if(!$studentOne){
            ajax_return(array('error' => 1,'errortip' => "请确定你输入的手机号码或学员编号是否正确，系统未检测到您的权限!","bakfuntion"=>"errormotify"));
        }

        if(!$this->DataControl->getFieldOne("smc_class","class_id","class_branch='{$request['class_branch']}'")){
            ajax_return(array('error' => 1,'errortip' => "请确认您的班级编号是否正确!","bakfuntion"=>"errormotify"));
        }

        $parenter = array();
        $parenter['school_branch'] = $request['school_branch'];
        $parenter['class_branch'] = $request['class_branch'];
        $parenter['student_branch'] = $studentOne['student_branch'];
        $this->intSession->setCookiearray("parenter", $parenter, '1');

        ajax_return(array('error' => 0,'errortip' => "登录成功!","bakfuntion"=>"okmotify","bakurl"=>"/roomBook/"));
    }

    function LoginView(){
        $this->Viewhtm = $this->router->getController()."/Login.htm";
        $this->display($this->Viewhtm);
        exit;
    }
    //魔术方法
    function __destruct()
    {
        if ($this->c =='Wait') {
            $this->smarty->assign("u", $this->u);
            $this->smarty->assign("t", $this->t);
            $this->display($this->Viewhtm);
            exit;
        }
    }
}