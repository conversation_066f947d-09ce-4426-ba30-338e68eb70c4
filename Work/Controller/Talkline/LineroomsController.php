<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong <PERSON>
 * Date: 2018/3/10
 * Time: 16:49
 */

namespace Work\Controller\Talkline;


class LineroomsController extends viewTpl
{
    public $data;
    public $Viewhtm;
    public $istaffer;
    public $u;
    public $t;
    public $c;
    public $FYWKPID= '122042';

    function __construct()
    {
        parent::__construct();
        if (!$this->check_login()) {
            $this->LoginView();
        }
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
        $this->istaffer = $this->StafferLogin;
        $this->smarty->assign("istaffer", $this->StafferLogin);
        $this->smarty->assign("FYWKPID", $this->FYWKPID);
        $this->Viewhtm = $this->router->getController() . "/" . $this->router->getUrl() . ".htm";
    }

    //主页
    function HomeView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $DataControl = $this->DataControl;
        $smarty = $this->smarty;

        if (!isset($request['p'])) {
            $p = '1';
        } else {
            $p = $request['p'];
        }

        $datawhere = "s.school_branch = o.school_branch AND o.company_id = '{$this->istaffer['company_id']}'";
        if ($this->istaffer['account_class'] == '0') {
            $datawhere .= " AND (s.school_id in (SELECT p.school_id FROM gmc_staffer_postbe as p WHERE p.staffer_id = '{$this->istaffer['staffer_id']}')
                        or s.school_id in (SELECT o.school_id FROM gmc_staffer_postbe as sp,gmc_company_organizeschool as o
where o.organize_id = sp.organize_id and sp.staffer_id = '{$this->istaffer['staffer_id']}' and sp.school_id = '0' and sp.postbe_status = '1' order by sp.postbe_ismianjob DESC))";
        }
        $pageurl = "/{$this->u}/{$this->t}?";

        $datatype = array();
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (o.linerooms_number = '{$request['keyword']}' or o.linerooms_name like '%{$request['keyword']}%')";
            $pageurl .= "&keyword={$request['keyword']}";
            $datatype['keyword'] = $request['keyword'];
        }
        if (isset($request['linerooms_maxvideo']) && $request['linerooms_maxvideo'] !== '') {
            $maxvideo = $request['linerooms_maxvideo'] + 1;
            $datawhere .= " and o.linerooms_maxvideo = '{$maxvideo}'";
            $pageurl .= "&linerooms_maxvideo={$request['linerooms_maxvideo']}";
            $datatype['linerooms_maxvideo'] = $request['linerooms_maxvideo'];
        }

        if (isset($request['linerooms_isdel']) && $request['linerooms_isdel'] !== '') {
            $endtimes = time();
            if ($request['linerooms_isdel'] == '0') {
                $datawhere .= " and o.linerooms_isdel = '0' and o.linerooms_endtime > '{$endtimes}'";
            } elseif ($request['linerooms_isdel'] == '1') {
                $datawhere .= " and o.linerooms_isdel = '0' and o.linerooms_endtime <= '{$endtimes}'";
            } elseif ($request['linerooms_isdel'] == '2') {
                $datawhere .= " and o.linerooms_isdel = '1'";
            }
            $pageurl .= "&linerooms_isdel={$request['linerooms_isdel']}";
            $datatype['linerooms_isdel'] = $request['linerooms_isdel'];
        } else {
            $datawhere .= " and o.linerooms_isdel = '0'";
        }

        if (isset($request['school_branch']) && $request['school_branch'] !== '') {
            $datawhere .= " and o.school_branch = '{$request['school_branch']}'";
            $pageurl .= "&school_branch={$request['school_branch']}";
            $datatype['school_branch'] = $request['school_branch'];
        }

        if (isset($request['linerooms_fromclass']) && $request['linerooms_fromclass'] !== '') {
            $datawhere .= " and o.linerooms_fromclass = '{$request['linerooms_fromclass']}'";
            $pageurl .= "&linerooms_fromclass={$request['linerooms_fromclass']}";
            $datatype['linerooms_fromclass'] = $request['linerooms_fromclass'];
        }

        if (isset($request['starttime']) && $request['starttime'] !== '') {
            $starttime = strtotime($request['starttime']);
            $datawhere .= " and o.linerooms_createtime >= '{$starttime}'";
            $pageurl .= "&starttime={$request['starttime']}";
            $datatype['starttime'] = $request['starttime'];
        }

        if (isset($request['endtime']) && $request['endtime'] !== '') {
            $endtime = strtotime($request['endtime']) + 3600 * 23;
            $datawhere .= " and o.linerooms_createtime <= '{$endtime}'";
            $pageurl .= "&endtime={$request['endtime']}";
            $datatype['endtime'] = $request['endtime'];
        }

        if (isset($request['editstarttime']) && $request['editstarttime'] !== '') {
            $starttime = strtotime($request['editstarttime']);
            $datawhere .= " and o.linerooms_endtime >= '{$starttime}'";
            $pageurl .= "&editstarttime={$request['editstarttime']}";
            $datatype['editstarttime'] = $request['editstarttime'];
        }

        if (isset($request['editendtime']) && $request['editendtime'] !== '') {
            $endtime = strtotime($request['editendtime']) + 3600 * 23;
            $datawhere .= " and o.linerooms_starttime <= '{$endtime}'";
            $pageurl .= "&editendtime={$request['editendtime']}";
            $datatype['editendtime'] = $request['editendtime'];
        }

        $sql = "SELECT o.*,s.school_cnname FROM tkl_linerooms as o,smc_school as s where {$datawhere}  order by o.linerooms_endtime DESC";

        $db_nums = $DataControl->selectOne("SELECT COUNT(o.linerooms_id) as countnums FROM tkl_linerooms as o,smc_school as s where {$datawhere}");//相关条件下的总记录数COUNT(*)
        $allnum = $db_nums['countnums'];

        $datalist = $DataControl->dbwherePage($sql, $allnum, '10', $pageurl . '&p=', $p, '10', '2');

        $smarty->assign("pagelist", $datalist['pages']);//筛选信息

        $smarty->assign("dataList", $datalist['cont']);
        $smarty->assign("datatype", $datatype);
    }

    function LinehourView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $DataControl = $this->DataControl;
        $smarty = $this->smarty;

        if (!isset($request['p'])) {
            $p = '1';
        } else {
            $p = $request['p'];
        }
        $datawhere = "s.school_id = o.school_id AND c.class_id = o.class_id AND h.hour_id = o.hour_id AND c.class_status IN (0,1) AND h.hour_way = '1' AND o.company_id = '{$this->istaffer['company_id']}'";
        if ($this->istaffer['account_class'] == '0') {
            $datawhere .= " AND (s.school_id in (SELECT p.school_id FROM gmc_staffer_postbe as p WHERE p.staffer_id = '{$this->istaffer['staffer_id']}')
                        or s.school_id in (SELECT o.school_id FROM gmc_staffer_postbe as sp,gmc_company_organizeschool as o
where o.organize_id = sp.organize_id and sp.staffer_id = '{$this->istaffer['staffer_id']}' and sp.school_id = '0' and sp.postbe_status = '1' order by sp.postbe_ismianjob DESC))";
        }
        $pageurl = "/{$this->u}/{$this->t}?";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (o.linerooms_number = '{$request['keyword']}' or o.linerooms_name like '%{$request['keyword']}%' or  c.class_branch like '%{$request['keyword']}%'  or  o.linerooms_threenumber like '%{$request['keyword']}%')";
            $pageurl .= "&keyword={$request['keyword']}";
            $datatype['keyword'] = $request['keyword'];
        }
        if (isset($request['linerooms_maxvideo']) && $request['linerooms_maxvideo'] !== '') {
            $maxvideo = $request['linerooms_maxvideo'] + 1;
            $datawhere .= " and o.linerooms_maxvideo = '{$maxvideo}'";
            $pageurl .= "&linerooms_maxvideo={$request['linerooms_maxvideo']}";
            $datatype['linerooms_maxvideo'] = $request['linerooms_maxvideo'];
        }

        if (isset($request['linerooms_isdel']) && $request['linerooms_isdel'] !== '') {
            if ($request['linerooms_isdel'] == '0') {
                $datawhere .= " and o.linerooms_isdel = '0'";
            } elseif ($request['linerooms_isdel'] == '1') {
                $datawhere .= " and o.linerooms_isdel = '0'";
            } elseif ($request['linerooms_isdel'] == '2') {
                $datawhere .= " and o.linerooms_isdel = '1'";
            }
            $pageurl .= "&linerooms_isdel={$request['linerooms_isdel']}";
            $datatype['linerooms_isdel'] = $request['linerooms_isdel'];
        } else {
            $datawhere .= " and o.linerooms_isdel = '0'";
        }

        if (isset($request['school_branch']) && $request['school_branch'] !== '') {
            $datawhere .= " and s.school_branch = '{$request['school_branch']}'";
            $pageurl .= "&school_branch={$request['school_branch']}";
            $datatype['school_branch'] = $request['school_branch'];
        }

        if (isset($request['linerooms_fromclass']) && $request['linerooms_fromclass'] !== '') {
            $datawhere .= " and o.linerooms_fromclass = '{$request['linerooms_fromclass']}'";
            $pageurl .= "&linerooms_fromclass={$request['linerooms_fromclass']}";
            $datatype['linerooms_fromclass'] = $request['linerooms_fromclass'];
        }

        $MonthArray = GetTheMonth(date("Y-m-d"));

        if (isset($request['editstarttime']) && $request['editstarttime'] !== '') {
            $starttime = strtotime($request['editstarttime']);
            $datawhere .= " and o.linerooms_endtime >= '{$starttime}'";
            $pageurl .= "&editstarttime={$request['editstarttime']}";
            $datatype['editstarttime'] = $request['editstarttime'];
        } else {
            $starttime = strtotime($MonthArray['0']);
            $datawhere .= " and o.linerooms_endtime >= '{$starttime}'";
            $pageurl .= "&editstarttime={$MonthArray['0']}";
            $datatype['editstarttime'] = $MonthArray['0'];
        }

        if (isset($request['editendtime']) && $request['editendtime'] !== '') {
            $endtime = strtotime($request['editendtime']) + 3600 * 23;
            $datawhere .= " and o.linerooms_starttime <= '{$endtime}'";
            $pageurl .= "&editendtime={$request['editendtime']}";
            $datatype['editendtime'] = $request['editendtime'];
        } else {
            $endtime = strtotime($MonthArray['1']) + 3600 * 23;
            $datawhere .= " and o.linerooms_starttime <= '{$endtime}'";
            $pageurl .= "&editendtime={$MonthArray['1']}";
            $datatype['editendtime'] = $MonthArray['1'];
        }

        $sql = "SELECT o.*,s.school_cnname,c.class_branch,c.class_enname,o.linerooms_passwordrequired,
                 (select week_cnname from  smc_code_week where week_id=WEEKDAY(FROM_UNIXTIME(o.linerooms_starttime))) as linerooms_week
                 FROM smc_linerooms as o,smc_school as s,smc_class AS c,smc_class_hour AS h 
                 where {$datawhere}  
                 order by o.linerooms_starttime ASC ";

        $companyOne = $this->DataControl->getFieldOne("gmc_company", "company_tkyapino", "company_id='{$this->istaffer['company_id']}'");

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $this->c = 'export';
            $sql .= " limit 0,2000";
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['school_cnname'] = $dateexcelvar['school_cnname'];
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $datearray['class_enname'] = $dateexcelvar['class_enname'];
                    $datearray['class_branch'] = $dateexcelvar['class_branch'];
                    $datearray['linerooms_threenumber'] = $dateexcelvar['linerooms_threenumber'];
                    $datearray['linerooms_name'] = $dateexcelvar['linerooms_name'];
                    $datearray['linerooms_fromclass'] = $dateexcelvar['linerooms_fromclass'] == 0 ? '拓课云' : '云枢';
                    $datearray['linerooms_maxvideo'] = $dateexcelvar['linerooms_maxvideo'] - 1;
                    $datearray['linerooms_date'] = $dateexcelvar['linerooms_starttime'] == false ? '--' : date("Y-m-d", $dateexcelvar['linerooms_starttime']);
                    $datearray['linerooms_week'] = $dateexcelvar['linerooms_starttime'] == false ? '--' : $dateexcelvar['linerooms_week'];
                    $datearray['linerooms_starttime'] = date("H:i", $dateexcelvar['linerooms_starttime']);
                    $datearray['linerooms_endtime'] = date("H:i", $dateexcelvar['linerooms_endtime']);
                    $datearray['linerooms_chairmanpwd'] = $dateexcelvar['linerooms_chairmanpwd'];
                    $datearray['linerooms_assistantpwd'] = $dateexcelvar['linerooms_assistantpwd'];
                    $datearray['linerooms_patrolpwd'] = $dateexcelvar['linerooms_patrolpwd'];
                    $datearray['linerooms_confuserpwd'] = $dateexcelvar['linerooms_confuserpwd'];
                    if ($dateexcelvar['linerooms_threenumber']) {
                        $datearray['linerooms_chairman_url'] = "https://global.talk-cloud.net/" . $dateexcelvar['linerooms_threenumber'] . '/' . $companyOne['company_tkyapino'] . '/1/0';
                        $datearray['linerooms_assistan_url'] = "https://global.talk-cloud.net/" . $dateexcelvar['linerooms_threenumber'] . '/' . $companyOne['company_tkyapino'] . '/1/0';
                        $datearray['linerooms_patrol_url'] = "https://global.talk-cloud.net/" . $dateexcelvar['linerooms_threenumber'] . '/' . $companyOne['company_tkyapino'] . '/1/0';
                        $datearray['linerooms_confuser_url'] = "https://global.talk-cloud.net/" . $dateexcelvar['linerooms_threenumber'] . '/' . $companyOne['company_tkyapino'] . '/' . $dateexcelvar['linerooms_passwordrequired'] . '/2';
                    }
                    $outexceldate[] = $datearray;
                }
                $excelheader = array("校园名称", "校园编号", "班级名称", "班级编号", "教室号", "教室名称", "所属平台", "最大上台数", "上课日期", "上课周次", "开始时间", "结束时间", "教师密码", "助教密码", "巡课密码", "上课密码", "教师上课地址", "助教上课地址", "主管巡课地址", "学员上课地址");
                $excelfileds = array('school_cnname', 'school_branch', 'class_enname', 'class_branch', 'linerooms_threenumber', 'linerooms_name', 'linerooms_name', 'linerooms_fromclass', 'linerooms_maxvideo',
                    'linerooms_date', 'linerooms_week', 'linerooms_starttime', 'linerooms_endtime', 'linerooms_chairmanpwd', 'linerooms_assistantpwd', 'linerooms_patrolpwd', 'linerooms_confuserpwd', 'linerooms_chairman_url', 'linerooms_assistan_url', 'linerooms_patrol_url', 'linerooms_confuser_url');
                query_to_excel($excelheader, $outexceldate, $excelfileds, $request['editstarttime'] . '-' . $request['editendtime'] . "网课系统排课明细.xls");
                ajax_return(array('error' => 0, 'errortip' => "导出完毕!", "bakfuntion" => "okmotify"));
            }


        } else {
            $db_nums = $DataControl->selectOne("SELECT COUNT(o.linerooms_id) as countnums FROM smc_linerooms as o,smc_school as s,smc_class AS c,smc_class_hour AS h  where {$datawhere}");//相关条件下的总记录数COUNT(*)

            $allnum = $db_nums['countnums'];
            $datalist = $DataControl->dbwherePage($sql, $allnum, '10', $pageurl . '&p=', $p, '10', '2');
            $smarty->assign("pagelist", $datalist['pages']);//筛选信息
            $smarty->assign("dataList", $datalist['cont']);
            $smarty->assign("datatype", $datatype);
        }
    }

    function RecordlistView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $DataControl = $this->DataControl;
        $smarty = $this->smarty;

        if (!isset($request['p'])) {
            $p = '1';
        } else {
            $p = $request['p'];
        }

        $datawhere = "s.school_branch = o.school_branch AND o.company_id = '{$this->istaffer['company_id']}'";
        if ($this->istaffer['account_class'] == '0') {
            $datawhere .= " AND (s.school_id in (SELECT p.school_id FROM gmc_staffer_postbe as p WHERE p.staffer_id = '{$this->istaffer['staffer_id']}')
                        or s.school_id in (SELECT o.school_id FROM gmc_staffer_postbe as sp,gmc_company_organizeschool as o
where o.organize_id = sp.organize_id and sp.staffer_id = '{$this->istaffer['staffer_id']}' and sp.school_id = '0' and sp.postbe_status = '1' order by sp.postbe_ismianjob DESC))";
        }
        $pageurl = "/{$this->u}/{$this->t}?";

        $datatype = array();
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (o.linerooms_number = '{$request['keyword']}' or o.linerooms_name like '%{$request['keyword']}%' or o.linerooms_maxvideo = '{$request['keyword']}')";
            $pageurl .= "&keyword={$request['keyword']}";
            $datatype['keyword'] = $request['keyword'];
        }

        if (isset($request['school_branch']) && $request['school_branch'] !== '') {
            $datawhere .= " and o.school_branch = '{$request['school_branch']}'";
            $pageurl .= "&school_branch={$request['school_branch']}";
            $datatype['school_branch'] = $request['school_branch'];
        }

        if (isset($request['linerooms_fromclass']) && $request['linerooms_fromclass'] !== '') {
            $datawhere .= " and o.linerooms_fromclass = '{$request['linerooms_fromclass']}'";
            $pageurl .= "&linerooms_fromclass={$request['linerooms_fromclass']}";
            $datatype['linerooms_fromclass'] = $request['linerooms_fromclass'];
        }

        if (isset($request['editstarttime']) && $request['editstarttime'] !== '') {
            $starttime = strtotime($request['editstarttime']);
            $datawhere .= " and o.linerooms_endtime >= '{$starttime}'";
            $pageurl .= "&editstarttime={$request['editstarttime']}";
            $datatype['editstarttime'] = $request['editstarttime'];
        }

        if (isset($request['editendtime']) && $request['editendtime'] !== '') {
            $endtime = strtotime($request['editendtime']) + 3600 * 23;
            $datawhere .= " and o.linerooms_starttime <= '{$endtime}'";
            $pageurl .= "&editendtime={$request['editendtime']}";
            $datatype['editendtime'] = $request['editendtime'];
        }

        $sql = "SELECT o.*,s.school_cnname FROM tkl_linerooms as o,smc_school as s where {$datawhere}  order by o.linerooms_endtime DESC";

        $db_nums = $DataControl->selectOne("SELECT COUNT(o.linerooms_id) as countnums FROM tkl_linerooms as o,smc_school as s where {$datawhere}");//相关条件下的总记录数COUNT(*)
        $allnum = $db_nums['countnums'];

        $datalist = $DataControl->dbwherePage($sql, $allnum, '10', $pageurl . '&p=', $p, '10', '2');

        $smarty->assign("pagelist", $datalist['pages']);//筛选信息

        $smarty->assign("dataList", $datalist['cont']);
        $smarty->assign("datatype", $datatype);
    }

    function ExportAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $datawhere = "s.school_branch = o.school_branch AND o.company_id = '{$this->istaffer['company_id']}' AND o.linerooms_isdel = '0'";
        if ($this->istaffer['account_class'] == '0') {
            $datawhere .= " AND (s.school_id in (SELECT p.school_id FROM gmc_staffer_postbe as p WHERE p.staffer_id = '{$this->istaffer['staffer_id']}')
                        or s.school_id in (SELECT o.school_id FROM gmc_staffer_postbe as sp,gmc_company_organizeschool as o
where o.organize_id = sp.organize_id and sp.staffer_id = '{$this->istaffer['staffer_id']}' and sp.school_id = '0' and sp.postbe_status = '1' order by sp.postbe_ismianjob DESC))";
        }

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (o.linerooms_number like '%{$request['keyword']}%' or o.linerooms_name like '%{$request['keyword']}%')";
        }

        if (isset($request['school_branch']) && $request['school_branch'] !== '') {
            $datawhere .= " and o.school_branch = '{$request['school_branch']}'";
        }

        if (isset($request['starttime']) && $request['starttime'] !== '') {
            $starttime = strtotime($request['starttime']);
            $datawhere .= " and o.linerooms_createtime >= '{$starttime}'";
        }

        if (isset($request['linerooms_isdel']) && $request['linerooms_isdel'] !== '') {
            $endtimes = time();
            if ($request['linerooms_isdel'] == '0') {
                $datawhere .= " and o.linerooms_isdel = '0' and o.linerooms_endtime > '{$endtimes}'";
            } elseif ($request['linerooms_isdel'] == '1') {
                $datawhere .= " and o.linerooms_isdel = '0' and o.linerooms_endtime <= '{$endtimes}'";
            } elseif ($request['linerooms_isdel'] == '2') {
                $datawhere .= " and o.linerooms_isdel = '1'";
            }
        } else {
            $datawhere .= " and o.linerooms_isdel = '0'";
        }

        if (isset($request['endtime']) && $request['endtime'] !== '') {
            $endtime = strtotime($request['endtime']) + 3600 * 23;
            $datawhere .= " and o.linerooms_createtime <= '{$endtime}'";
        }

        if (isset($request['editstarttime']) && $request['editstarttime'] !== '') {
            $starttime = strtotime($request['editstarttime']);
            $datawhere .= " and o.linerooms_endtime >= '{$starttime}'";
        }

        if (isset($request['editendtime']) && $request['editendtime'] !== '') {
            $endtime = strtotime($request['editendtime']) + 3600 * 23;
            $datawhere .= " and o.linerooms_starttime <= '{$endtime}'";
        }

        $dateexcelarray = $this->DataControl->selectClear("SELECT o.*,s.school_cnname FROM tkl_linerooms as o,smc_school as s where {$datawhere}  order by o.linerooms_endtime DESC");

        if ($dateexcelarray) {
            $outexceldate = array();
            foreach ($dateexcelarray as $dateexcelvar) {
                $datearray = array();
                $datearray['school_cnname'] = $dateexcelvar['school_cnname'];//校区名称
                $datearray['linerooms_number'] = $dateexcelvar['linerooms_number'];//教室号
                $datearray['linerooms_name'] = $dateexcelvar['linerooms_name'];//教室名称
                if ($dateexcelvar['linerooms_type'] == 0) {
                    $datearray['linerooms_type'] = "1对1";
                } elseif ($dateexcelvar['linerooms_type'] == 3) {
                    $datearray['linerooms_type'] = "1对N";
                }
                $datearray['linerooms_starttime'] = date("Y-m-d H:i:s", $dateexcelvar['linerooms_starttime']);//开始时间
                $datearray['linerooms_endtime'] = date("Y-m-d H:i:s", $dateexcelvar['linerooms_endtime']);//结束时间
                $datearray['linerooms_chairmanpwd'] = $dateexcelvar['linerooms_chairmanpwd'];//教师密码
                $datearray['linerooms_assistantpwd'] = $dateexcelvar['linerooms_assistantpwd'];//助教密码
                $datearray['linerooms_patrolpwd'] = $dateexcelvar['linerooms_patrolpwd'];//巡课密码
                $datearray['linerooms_confuserpwd'] = $dateexcelvar['linerooms_confuserpwd'];//上课密码

                $outexceldate[] = $datearray;
            }
        }

        $excelheader = array("校区名称", "教室号", "教室名称", "上课类型", "开始时间", "结束时间", "教师密码", "助教密码", "巡课密码", "上课密码");
        $excelfileds = array('school_cnname', "linerooms_number", "linerooms_name", 'linerooms_type', 'linerooms_starttime', "linerooms_endtime", "linerooms_chairmanpwd", 'linerooms_assistantpwd', 'linerooms_patrolpwd', "linerooms_confuserpwd");

        query_to_excel($excelheader, $outexceldate, $excelfileds, "网课直播教室.xlsx");
    }

    //线上课导出
    function LineHourExportAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $datawhere = "s.school_id = o.school_id AND o.company_id = '{$this->istaffer['company_id']}' AND o.linerooms_isdel = '0'";
        if ($this->istaffer['account_class'] == '0') {
            $datawhere .= " AND (s.school_id in (SELECT p.school_id FROM gmc_staffer_postbe as p WHERE p.staffer_id = '{$this->istaffer['staffer_id']}')
                        or s.school_id in (SELECT o.school_id FROM gmc_staffer_postbe as sp,gmc_company_organizeschool as o
where o.organize_id = sp.organize_id and sp.staffer_id = '{$this->istaffer['staffer_id']}' and sp.school_id = '0' and sp.postbe_status = '1' order by sp.postbe_ismianjob DESC))";
        }

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (o.linerooms_number like '%{$request['keyword']}%' or o.linerooms_name like '%{$request['keyword']}%')";
        }

        if (isset($request['school_branch']) && $request['school_branch'] !== '') {
            $datawhere .= " and s.school_branch = '{$request['school_branch']}'";
        }

        if (isset($request['starttime']) && $request['starttime'] !== '') {
            $starttime = strtotime($request['starttime']);
            $datawhere .= " and o.linerooms_createtime >= '{$starttime}'";
        }

        if (isset($request['linerooms_isdel']) && $request['linerooms_isdel'] !== '') {
            $endtimes = time();
            if ($request['linerooms_isdel'] == '0') {
                $datawhere .= " and o.linerooms_isdel = '0' and o.linerooms_endtime > '{$endtimes}'";
            } elseif ($request['linerooms_isdel'] == '1') {
                $datawhere .= " and o.linerooms_isdel = '0' and o.linerooms_endtime <= '{$endtimes}'";
            } elseif ($request['linerooms_isdel'] == '2') {
                $datawhere .= " and o.linerooms_isdel = '1'";
            }
        } else {
            $datawhere .= " and o.linerooms_isdel = '0'";
        }

        if (isset($request['endtime']) && $request['endtime'] !== '') {
            $endtime = strtotime($request['endtime']) + 3600 * 23;
            $datawhere .= " and o.linerooms_createtime <= '{$endtime}'";
        }

        if (isset($request['editstarttime']) && $request['editstarttime'] !== '') {
            $starttime = strtotime($request['editstarttime']);
            $datawhere .= " and o.linerooms_endtime >= '{$starttime}'";
        }

        if (isset($request['editendtime']) && $request['editendtime'] !== '') {
            $endtime = strtotime($request['editendtime']) + 3600 * 23;
            $datawhere .= " and o.linerooms_starttime <= '{$endtime}'";
        }

        $dateexcelarray = $this->DataControl->selectClear("SELECT o.*,s.school_cnname FROM smc_linerooms as o,smc_school as s where {$datawhere}  order by o.linerooms_endtime DESC");

        if ($dateexcelarray) {
            $outexceldate = array();
            foreach ($dateexcelarray as $dateexcelvar) {
                $datearray = array();
                $datearray['school_cnname'] = $dateexcelvar['school_cnname'];//校区名称
                $datearray['linerooms_number'] = $dateexcelvar['linerooms_number'];//教室号
                $datearray['linerooms_name'] = $dateexcelvar['linerooms_name'];//教室名称
                if ($dateexcelvar['linerooms_type'] == 0) {
                    $datearray['linerooms_type'] = "1对1";
                } elseif ($dateexcelvar['linerooms_type'] == 3) {
                    $datearray['linerooms_type'] = "1对N";
                }
                $datearray['linerooms_starttime'] = date("Y-m-d H:i:s", $dateexcelvar['linerooms_starttime']);//开始时间
                $datearray['linerooms_endtime'] = date("Y-m-d H:i:s", $dateexcelvar['linerooms_endtime']);//结束时间
                $datearray['linerooms_chairmanpwd'] = $dateexcelvar['linerooms_chairmanpwd'];//教师密码
                $datearray['linerooms_assistantpwd'] = $dateexcelvar['linerooms_assistantpwd'];//助教密码
                $datearray['linerooms_patrolpwd'] = $dateexcelvar['linerooms_patrolpwd'];//巡课密码
                $datearray['linerooms_confuserpwd'] = $dateexcelvar['linerooms_confuserpwd'];//上课密码

                $outexceldate[] = $datearray;
            }
        }

        $excelheader = array("校区名称", "教室号", "教室名称", "上课类型", "开始时间", "结束时间", "教师密码", "助教密码", "巡课密码", "上课密码");
        $excelfileds = array('school_cnname', "linerooms_number", "linerooms_name", 'linerooms_type', 'linerooms_starttime', "linerooms_endtime", "linerooms_chairmanpwd", 'linerooms_assistantpwd', 'linerooms_patrolpwd', "linerooms_confuserpwd");

        query_to_excel($excelheader, $outexceldate, $excelfileds, "网课直播教室.xlsx");
    }

    function LineLookView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $dataVar = $this->DataControl->getOne("smc_linerooms", "linerooms_id='{$request['linerooms_id']}'");
        $companyOne = $this->DataControl->getFieldOne("gmc_company", "company_tkyapino", "company_id='{$this->istaffer['company_id']}'");

        $TalkcloudModel = new \Model\Tlk\TalkcloudModel($this->istaffer['company_id']);

        $putArray = array();
        $putArray['serial'] = $dataVar['linerooms_threenumber'];
        $putArray['fromclass'] = $dataVar['linerooms_fromclass'];
        $Exroominfo = $TalkcloudModel->getExroominfo($putArray);
        $dataVar['linerooms_starttime'] = $Exroominfo['starttime'];
        $dataVar['linerooms_endtime'] = $Exroominfo['endtime'];


        $putArray = array();
        $putArray['serial'] = $dataVar['linerooms_threenumber'];
        $putArray['fromclass'] = $dataVar['linerooms_fromclass'];
        $putArray['type'] = 1; //选填 参加用户类型 0: 当前在线人数  1：登录人数
        $modelArray = $TalkcloudModel->exroomonLinenum($putArray);
        if ($modelArray['result'] == '0') {
            $dataVar['loginnums'] = $modelArray['room'][0]['num'];
        } else {
            $dataVar['loginnums'] = 0;
        }
        $putArray = array();
        $putArray['serial'] = $dataVar['linerooms_threenumber'];
        $putArray['fromclass'] = $dataVar['linerooms_fromclass'];
        $putArray['type'] = 0; //选填 参加用户类型 0: 当前在线人数  1：登录人数
        $modelArray = $TalkcloudModel->exroomonLinenum($putArray);
        if ($modelArray['result'] == '0') {
            $dataVar['linenums'] = $modelArray['room'][0]['num'];
        } else {
            $dataVar['linenums'] = 0;
        }
        $this->smarty->assign("dataVar", $dataVar);


        $putArray = array();
        $putArray['serial'] = $dataVar['linerooms_threenumber'];
        $putArray['fromclass'] = $dataVar['linerooms_fromclass'];
        $modelArray = $TalkcloudModel->exgetonLineuser($putArray);
        if ($modelArray['result'] == '0') {
            $this->smarty->assign("onlineUser", $modelArray['onlineuser']);
        }
        $this->smarty->assign("companyOne", $companyOne);
    }


    function RecordView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $dataVar = $this->DataControl->getOne("tkl_linerooms", "linerooms_id='{$request['linerooms_id']}'");
        $this->smarty->assign("dataVar", $dataVar);

        $datatype = array();
        $TalkcloudModel = new \Model\Tlk\TalkcloudModel($this->istaffer['company_id']);
        $putArray = array();
        $putArray['serial'] = $dataVar['linerooms_number'];
        $putArray['fromclass'] = $dataVar['linerooms_fromclass'];

        if (isset($request['starttime']) && $request['starttime'] !== '') {
            $putArray['starttime'] = strtotime($request['starttime']);
        } else {
            $putArray['starttime'] = time() - 3600 * 24 * 10;
        }
        $datatype['starttime'] = $putArray['starttime'];

        if (isset($request['endtime']) && $request['endtime'] !== '') {
            $putArray['endtime'] = strtotime($request['endtime']) + 3600 * 23;
        } else {
            $putArray['endtime'] = time();
        }
        $datatype['endtime'] = $putArray['endtime'];

        $modelArray = $TalkcloudModel->exroomonGetrecordlist($putArray);
        if ($modelArray['result'] == '0') {
            if ($dataVar['linerooms_fromclass'] == '0') {
                $recordList = $modelArray['recordlist'];
            } else {
                $recordList = $modelArray['list'];
            }
            if ($recordList) {
                foreach ($recordList as &$roomOne) {
                    if ($dataVar['linerooms_fromclass'] == '0') {
                        $roomOne['playpath'] = $roomOne['playpath'];
                        $roomOne['duration'] = round(($roomOne['duration']) / 60, 2);
                    } else {
                        $roomOne['playpath'] = $roomOne['videoplayback_mp4'];
                        $roomOne['duration'] = round(($roomOne['duration']) / 60, 2);
                    }
                    $roomOne['size'] = $this->switchSize($roomOne['size']);
                }
            }
            $this->smarty->assign("recordList", $recordList);
        }
        $this->smarty->assign("datatype", $datatype);
    }

    function RecordvideoView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $dataVar = $this->DataControl->getOne("tkl_linerooms", "linerooms_id='{$request['linerooms_id']}'");
        $this->smarty->assign("dataVar", $dataVar);

        $datatype = array();
        $TalkcloudModel = new \Model\Tlk\TalkcloudModel($this->istaffer['company_id']);
        $putArray = array();
        $putArray['serial'] = $dataVar['linerooms_number'];
        $putArray['fromclass'] = $dataVar['linerooms_fromclass'];

        if (isset($request['starttime']) && $request['starttime'] !== '') {
            $putArray['starttime'] = strtotime($request['starttime']);
        } else {
            $putArray['starttime'] = time() - 3600 * 24 * 10;
        }
        $datatype['starttime'] = $putArray['starttime'];

        if (isset($request['endtime']) && $request['endtime'] !== '') {
            $putArray['endtime'] = strtotime($request['endtime']) + 3600 * 23;
        } else {
            $putArray['endtime'] = time();
        }
        $datatype['endtime'] = $putArray['endtime'];

        $modelArray = $TalkcloudModel->exroomonGetvideolist($putArray);
        if ($modelArray['result'] == '0') {
            if ($dataVar['linerooms_fromclass'] == '0') {
                $recordList = $modelArray['recordlist'];
            } else {
                $recordList = $modelArray['list'];
            }
            if ($recordList) {
                foreach ($recordList as &$roomOne) {
                    if ($dataVar['linerooms_fromclass'] == '0') {
                        $roomOne['playurl'] = $roomOne['https_playpath_mp4'];
                        $roomOne['duration'] = round(($roomOne['duration']) / 60, 2);
                    } else {
                        $roomOne['playurl'] = $roomOne['https_playpath_mp4'];
                        $roomOne['duration'] = round(($roomOne['duration']) / 60, 2);
                    }
                    $roomOne['size'] = $this->switchSize($roomOne['size']);
                }
            }
            $this->smarty->assign("recordList", $recordList);
        }
        $this->smarty->assign("datatype", $datatype);
    }


    //删除报名活动
    function DelRecordAction()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $lineroomsOne = $this->DataControl->getOne("tkl_linerooms", "linerooms_id='{$request['linerooms_id']}'");
        $TalkcloudModel = new \Model\Tlk\TalkcloudModel($this->istaffer['company_id']);
        $putArray = array();
        $putArray['serial'] = $lineroomsOne['linerooms_number'];
        $putArray['fromclass'] = $lineroomsOne['linerooms_fromclass'];
        $putArray['recordtitle'] = $request['recordtitle'];
        $modelArray = $TalkcloudModel->exroomVideoDelete($putArray);
        if ($modelArray['result'] == '0') {
            $this->Recordweblog("网课教室管理", "教室管理", "删除教室录播Mp4文件,教室编号：{$lineroomsOne['linerooms_number']}，Recordtitle:{$request['recordtitle']}");
            ajax_return(array('error' => 0, 'errortip' => "文件删除成功!", "bakfuntion" => "okmotify"));
        } else {
            ajax_return(array('error' => 1, 'errortip' => "文件删除失败!", "bakfuntion" => "errormotify"));
        }
    }

    function EditView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $DataControl = $this->DataControl;
        $smarty = $this->smarty;

        $smarty->assign("act", "Edit");
        $dataVar = $DataControl->getOne("tkl_linerooms", "linerooms_id='{$request['linerooms_id']}'");
        $smarty->assign("dataVar", $dataVar);

        $this->Viewhtm = $this->router->getController() . "/" . "Edit.htm";
    }

    function EditAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $DataControl = $this->DataControl;
        $lineroomsOne = $DataControl->getOne("tkl_linerooms", "linerooms_id='{$request['linerooms_id']}'");
        if ($lineroomsOne['linerooms_starttime'] < time()) {
            $request['startday'] = date("Y-m-d", $lineroomsOne['linerooms_starttime']);
            $request['starttime'] = date("H:i:s", $lineroomsOne['linerooms_starttime']);
        }

        if ($this->DataControl->getOne('tkl_linerooms', "company_id = '{$this->istaffer['company_id']}' and linerooms_name='{$request['linerooms_name']}' and linerooms_id <> '{$request['linerooms_id']}'")) {
            $res = array('error' => '1', 'errortip' => '教室名称已存在，建议加上课程日期!', "bakfuntion" => "errormotify");
            ajax_return($res);
        }

        if ($request['linerooms_passwordrequired'] == '1' && trim($request['linerooms_confuserpwd']) == '') {
            $res = array('error' => '1', 'errortip' => '请设定学员上课密码!', "bakfuntion" => "errormotify");
            ajax_return($res);
        }
        $TalkcloudModel = new \Model\Tlk\TalkcloudModel($this->istaffer['company_id']);
        $putArray = array();
        $putArray['roomname'] = $request['linerooms_name'];
        $putArray['serial'] = $request['linerooms_number'];
        $putArray['fromclass'] = $request['linerooms_fromclass'];
        $putArray['roomtype'] = $request['linerooms_type'];
        $putArray['starttime'] = strtotime($request['startday'] . " " . $request['starttime']);
        $putArray['endtime'] = strtotime($request['endday'] . " " . $request['endtime']);

        $chaday = ($putArray['endtime'] - $putArray['starttime']) / (24 * 3600);
        if ($chaday > 90 && $request['endday'] >= '2021-01-28') {
            $putArray['endtime'] = time() + 3600 * 24 * 60;
        }
        $putArray['chairmanpwd'] = $request['linerooms_chairmanpwd'];
        $putArray['assistantpwd'] = $request['linerooms_assistantpwd'];
        $putArray['patrolpwd'] = $request['linerooms_patrolpwd'];
        $putArray['passwordrequired'] = $request['linerooms_passwordrequired'];
        $putArray['confuserpwd'] = $request['linerooms_confuserpwd'];
        $putArray['autoopenav'] = $request['linerooms_autoopenav'];
        $putArray['maxvideo'] = $request['linerooms_maxvideo'];
        $putArray['sharedesk'] = $request['linerooms_sharedesk'];
        $putArray['sidelineuserpwd'] = rand(11111, 999999);
        $modelArray = $TalkcloudModel->exroomModify($putArray);
        if ($modelArray['result'] == '0') {
            $data = array();
            $data['linerooms_name'] = $request['linerooms_name'];
            $data['school_branch'] = $request['school_branch'];
            $data['linerooms_fromclass'] = $request['linerooms_fromclass'];
            $data['linerooms_starttime'] = $putArray['starttime'];
            $data['linerooms_endtime'] = $putArray['endtime'];
            $data['linerooms_chairmanpwd'] = $request['linerooms_chairmanpwd'];
            $data['linerooms_assistantpwd'] = $request['linerooms_assistantpwd'];
            $data['linerooms_patrolpwd'] = $request['linerooms_patrolpwd'];
            $data['linerooms_passwordrequired'] = $request['linerooms_passwordrequired'];
            $data['linerooms_confuserpwd'] = $request['linerooms_confuserpwd'];
            $data['linerooms_autoopenav'] = $request['linerooms_autoopenav'];
            $data['linerooms_maxvideo'] = $request['linerooms_maxvideo'];
            $data['class_branch'] = $request['class_branch'];
            $data['linerooms_sharedesk'] = $request['linerooms_sharedesk'];
            $data['linerooms_updatatime'] = time();
            if ($DataControl->updateData("tkl_linerooms", "linerooms_id = '{$request['linerooms_id']}'", $data)) {
                $ediInfo = http_build_query($data);
                $this->Recordweblog("教室管理", "教室编辑", "修改教室数据，ID:{$request['linerooms_id']},{$ediInfo}");
                ajax_return(array('error' => 0, 'errortip' => "网课教室信息修改成功!", "bakfuntion" => "successFromTip"));
            } else {
                ajax_return(array('error' => 1, 'errortip' => "网课教室信息修改失败!", "bakfuntion" => "dangerFromTip"));
            }
        } else {
            $errortipOne = $DataControl->getOne("tkl_errortip", "errortip_code='{$modelArray['result']}'");
            ajax_return(array('error' => 1, 'errortip' => "网课教室修改失败，错误码：{$errortipOne['errortip_txt']}!", "bakfuntion" => "dangerFromTip"));
        }


    }


    function LineEditView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $DataControl = $this->DataControl;
        $smarty = $this->smarty;

        $smarty->assign("act", "LineEdit");
        $dataVar = $DataControl->getOne("smc_linerooms", "linerooms_id='{$request['linerooms_id']}'");
        $smarty->assign("dataVar", $dataVar);

        $this->Viewhtm = $this->router->getController() . "/" . "LineEdit.htm";
    }

    function LineEditAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $DataControl = $this->DataControl;
        $dataVar = $this->DataControl->getOne("smc_linerooms", "linerooms_id='{$request['linerooms_id']}'");

        if ($request['linerooms_passwordrequired'] == '1' && trim($request['linerooms_confuserpwd']) == '') {
            $res = array('error' => '1', 'errortip' => '请设定学员上课密码!', "bakfuntion" => "errormotify");
            ajax_return($res);
        }

        $TalkcloudModel = new \Model\Tlk\TalkcloudModel($this->istaffer['company_id']);
        $putArray = array();
        $putArray['roomname'] = $dataVar['linerooms_name'];
        $putArray['serial'] = $request['linerooms_threenumber'];
        $putArray['fromclass'] = $dataVar['linerooms_fromclass'];
        $putArray['roomtype'] = $dataVar['linerooms_type'];
        $putArray['starttime'] = strtotime($request['startday'] . " " . $request['starttime']);
        $putArray['endtime'] = strtotime($request['endday'] . " " . $request['endtime']);
        $putArray['chairmanpwd'] = $request['linerooms_chairmanpwd'];
        $putArray['assistantpwd'] = $request['linerooms_assistantpwd'];
        $putArray['patrolpwd'] = $request['linerooms_patrolpwd'];
        $putArray['passwordrequired'] = $request['linerooms_passwordrequired'];
        $putArray['confuserpwd'] = $request['linerooms_confuserpwd'];
        $putArray['autoopenav'] = $request['linerooms_autoopenav'];
        $putArray['maxvideo'] = $request['linerooms_maxvideo'];
        $putArray['sharedesk'] = $request['linerooms_sharedesk'];
        $putArray['sidelineuserpwd'] = rand(11111, 999999);
        $modelArray = $TalkcloudModel->exroomModify($putArray);
        if ($modelArray['result'] == '0') {
            $data = array();
            $data['linerooms_starttime'] = strtotime($request['startday'] . " " . $request['starttime']);
            $data['linerooms_endtime'] = strtotime($request['endday'] . " " . $request['endtime']);
            $data['linerooms_chairmanpwd'] = $request['linerooms_chairmanpwd'];
            $data['linerooms_assistantpwd'] = $request['linerooms_assistantpwd'];
            $data['linerooms_patrolpwd'] = $request['linerooms_patrolpwd'];
            $data['linerooms_passwordrequired'] = $request['linerooms_passwordrequired'];
            $data['linerooms_confuserpwd'] = $request['linerooms_confuserpwd'];
            $data['linerooms_autoopenav'] = $request['linerooms_autoopenav'];
            $data['linerooms_maxvideo'] = $request['linerooms_maxvideo'];
            $data['linerooms_sharedesk'] = $request['linerooms_sharedesk'];
            $data['linerooms_updatatime'] = time();
            $data['linerooms_issync'] = "1";
            if ($DataControl->updateData("smc_linerooms", "linerooms_id = '{$request['linerooms_id']}'", $data)) {
                $ediInfo = http_build_query($data);
                $this->Recordweblog("教室管理", "教室编辑", "修改教室数据，ID:{$request['linerooms_id']},{$ediInfo}");
                ajax_return(array('error' => 0, 'errortip' => "网课教室信息修改成功!", "bakfuntion" => "successFromTip"));
            } else {
                ajax_return(array('error' => 1, 'errortip' => "网课教室信息修改失败!", "bakfuntion" => "dangerFromTip"));
            }
        } else {
            $errortipOne = $DataControl->getOne("tkl_errortip", "errortip_code='{$modelArray['result']}'");
            ajax_return(array('error' => 1, 'errortip' => "网课教室修改失败，错误码：{$errortipOne['errortip_txt']}!", "bakfuntion" => "dangerFromTip"));
        }


    }

    //添加客户
    function AddView()
    {
        $smarty = $this->smarty;
        $smarty->assign("act", "Add");

        $dataVar = array();
        if ($this->istaffer['company_id'] == '78531') {
            $dataVar['linerooms_chairmanpwd'] = 't22185996';
            $dataVar['linerooms_assistantpwd'] = 'z22185996';
        } else {
            $dataVar['linerooms_chairmanpwd'] = 't123456';
            $dataVar['linerooms_assistantpwd'] = 'z123456';
        }
        $dataVar['linerooms_patrolpwd'] = 'x123456';
        $dataVar['linerooms_confuserpwd'] = '';
        $smarty->assign("dataVar", $dataVar);

        $this->Viewhtm = $this->router->getController() . "/" . "Edit.htm";
    }


    function CreateAction()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $dataVar = $this->DataControl->getOne("smc_linerooms", "linerooms_id='{$request['linerooms_id']}'");
        if ($dataVar['linerooms_threenumber'] !== '') {
            $res = array('error' => '1', 'errortip' => '请勿重复生成!', "bakfuntion" => "errormotify");
            ajax_return($res);
        }
        $companyOne = $this->DataControl->getFieldOne("gmc_company", "company_code", "company_id='{$this->istaffer['company_id']}'");
        $TalkcloudModel = new \Model\Tlk\TalkcloudModel($this->istaffer['company_id']);
        $putArray = array();
        $putArray['roomname'] = $dataVar['linerooms_name'];
        $putArray['roomtype'] = '3';
        $putArray['fromclass'] = '0';
        $putArray['starttime'] = $dataVar['linerooms_starttime'];
        $putArray['endtime'] = $dataVar['linerooms_endtime'];
        if ($companyOne['company_code'] == 'kctw') {
            $putArray['chairmanpwd'] = 't22185996';
            $putArray['assistantpwd'] = 'z22185996';
            $putArray['patrolpwd'] = ' 22185996';
            $putArray['passwordrequired'] = 0;
        } else {
            $putArray['chairmanpwd'] = 't123456';
            $putArray['assistantpwd'] = 'z123456';
            $putArray['patrolpwd'] = 'x123456';
            $putArray['passwordrequired'] = 1;
        }
        $putArray['confuserpwd'] = rand(11111, 999999);
        $putArray['autoopenav'] = '1';
        $putArray['maxvideo'] = '7';
        $putArray['sharedesk'] = '1';
        $putArray['sidelineuserpwd'] = rand(11111, 999999);
        $modelArray = $TalkcloudModel->exroomCreate($putArray);
        if ($modelArray['result'] == '0') {
            // 匹配课件
            $wordFiledId = $this->DataControl->selectClear("
                 select  DISTINCT cd.line_fileid
                 from eas_coursepackage_lessonword as cd
                 left join eas_coursepackage_lesson as cn On cn.lesson_id = cd.lesson_id and cn.package_id = cd.package_id
                 left join eas_coursepackage_apply as cy ON cy.package_id= cn.package_id
                 left join smc_class as c ON cy.course_id =c.course_id
                 left join smc_class_hour as ch On ch.class_id =c.class_id and cn.lesson_sort = ch.hour_lessontimes
                 where c.class_id='{$dataVar['class_id']}' and ch.hour_id = '{$dataVar['hour_id']}' and cn.is_synchro =1
                 ");
            if (is_array($wordFiledId)) {
                $array_field = array_column($wordFiledId, 'line_fileid');
                $file_putArray = array();
                $file_putArray['serial'] = $modelArray['serial'];
                $file_putArray['fileidarr'] = $array_field;
                $TalkcloudModel->ex_roombindfile($file_putArray);
            }
            $data = array();
            $data['linerooms_threenumber'] = $modelArray['serial'];
            $data['linerooms_fromclass'] = '0';
            $data['linerooms_type'] = '3';
            if ($companyOne['company_code'] == 'kctw') {
                $data['linerooms_chairmanpwd'] = 't22185996';
                $data['linerooms_assistantpwd'] = 'z22185996';
                $data['linerooms_patrolpwd'] = '22185996';
                $data['linerooms_passwordrequired'] = 0;
            } else {
                $data['linerooms_chairmanpwd'] = 't123456';
                $data['linerooms_assistantpwd'] = 'z123456';
                $data['linerooms_patrolpwd'] = 'x123456';
                $data['linerooms_passwordrequired'] = 1;
            }
            $data['linerooms_confuserpwd'] = $putArray['confuserpwd'];
            $data['linerooms_autoopenav'] = '1';
            $data['linerooms_maxvideo'] = '13';
            $data['linerooms_sharedesk'] = '1';
            $data['linerooms_updatatime'] = time();
            $data['linerooms_issync'] = "1";
            if ($this->DataControl->updateData("smc_linerooms", "linerooms_id = '{$request['linerooms_id']}'", $data)) {
                $ediInfo = http_build_query($data);
                $this->Recordweblog("教室管理", "教室编辑", "课时网课信息创建教室成功，ID:{$request['linerooms_id']},{$ediInfo}");
                ajax_return(array('error' => 0, 'errortip' => "课时网课信息创建教室成功!", "bakfuntion" => "okmotify"));
            } else {
                ajax_return(array('error' => 1, 'errortip' => "课时网课信息创建教室成功!", "bakfuntion" => "errormotify"));
            }
        } else {
            $errortipOne = $this->DataControl->getOne("tkl_errortip", "errortip_code='{$modelArray['result']}'");
            ajax_return(array('error' => 1, 'errortip' => "网课教室创建失败，错误码：{$errortipOne['errortip_txt']}!", 'putArray' => $putArray, 'result' => $modelArray, "bakfuntion" => "errormotify"));
        }
    }


    function LineRecordvideoView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $dataVar = $this->DataControl->getOne("smc_linerooms", "linerooms_id='{$request['linerooms_id']}'");
        $this->smarty->assign("dataVar", $dataVar);


        $datatype = array();
        $TalkcloudModel = new \Model\Tlk\TalkcloudModel($this->istaffer['company_id']);
        $putArray = array();
        $putArray['serial'] = $dataVar['linerooms_threenumber'];
        $putArray['fromclass'] = $dataVar['linerooms_fromclass'];
        if (isset($request['starttime']) && $request['starttime'] !== '') {
            $putArray['starttime'] = strtotime($request['starttime']);
        } else {
            $putArray['starttime'] = $dataVar['linerooms_starttime'];
        }
        $datatype['starttime'] = $putArray['starttime'];

        if (isset($request['endtime']) && $request['endtime'] !== '') {
            $putArray['endtime'] = strtotime($request['endtime']) + 3600 * 23;
        } else {
            $putArray['endtime'] = $dataVar['linerooms_endtime'];
        }
        $datatype['endtime'] = $putArray['endtime'];
        $modelArray = $TalkcloudModel->exroomonGetvideolist($putArray);
        if ($modelArray['result'] == '0') {
            if ($dataVar['linerooms_fromclass'] == '0') {
                $recordList = $modelArray['recordlist'];
            } else {
                $recordList = $modelArray['list'];
            }
            if ($recordList) {
                foreach ($recordList as &$roomOne) {
                    if ($dataVar['linerooms_fromclass'] == '0') {
                        $roomOne['playurl'] = $roomOne['https_playpath_mp4'];
                        $roomOne['duration'] = round(($roomOne['duration']) / 60, 2);
                    } else {
                        $roomOne['playurl'] = $roomOne['https_playpath_mp4'];
                        $roomOne['duration'] = round(($roomOne['duration']) / 60, 2);
                    }
                    $roomOne['size'] = $this->switchSize($roomOne['size']);
                }
            }
            $this->smarty->assign("recordList", $recordList);
        }
        $this->smarty->assign("datatype", $datatype);
    }

    //新增客户
    function AddAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $DataControl = $this->DataControl;

        if ($this->DataControl->getOne('tkl_linerooms', "company_id = '{$this->istaffer['company_id']}' and linerooms_name='{$request['linerooms_name']}'")) {
            $res = array('error' => '1', 'errortip' => '教室名称已存在，建议加上课程日期!', "bakfuntion" => "errormotify");
            ajax_return($res);
        }

        if ($request['linerooms_passwordrequired'] == '1' && trim($request['linerooms_confuserpwd']) == '') {
            $res = array('error' => '1', 'errortip' => '请设定学员上课密码!', "bakfuntion" => "errormotify");
            ajax_return($res);
        }

        $TalkcloudModel = new \Model\Tlk\TalkcloudModel($this->istaffer['company_id']);
        $putArray = array();
        $putArray['roomname'] = $request['linerooms_name'];
        $putArray['roomtype'] = $request['linerooms_type'];
        $putArray['fromclass'] = $request['linerooms_fromclass'];
        $putArray['starttime'] = strtotime($request['startday'] . " " . $request['starttime']);
        $putArray['endtime'] = strtotime($request['endday'] . " " . $request['endtime']);
        $putArray['chairmanpwd'] = $request['linerooms_chairmanpwd'];
        $putArray['assistantpwd'] = $request['linerooms_assistantpwd'];
        $putArray['patrolpwd'] = $request['linerooms_patrolpwd'];
        $putArray['passwordrequired'] = $request['linerooms_passwordrequired'];
        $putArray['confuserpwd'] = $request['linerooms_confuserpwd'];
        $putArray['autoopenav'] = $request['linerooms_autoopenav'];
        $putArray['maxvideo'] = $request['linerooms_maxvideo'];
        $putArray['sharedesk'] = $request['linerooms_sharedesk'];
        $putArray['sidelineuserpwd'] = rand(11111, 999999);
        $modelArray = $TalkcloudModel->exroomCreate($putArray);
        if ($modelArray['result'] == '0') {
            $data = array();
            $data['company_id'] = $this->istaffer['company_id'];
            $data['linerooms_number'] = $modelArray['serial'];
            $data['linerooms_name'] = $request['linerooms_name'];
            $data['school_branch'] = $request['school_branch'];
            $data['linerooms_fromclass'] = $request['linerooms_fromclass'];
            $data['linerooms_type'] = $request['linerooms_type'];
            $data['linerooms_starttime'] = strtotime($request['startday'] . " " . $request['starttime']);
            $data['linerooms_endtime'] = strtotime($request['endday'] . " " . $request['endtime']);
            $data['linerooms_chairmanpwd'] = $request['linerooms_chairmanpwd'];
            $data['linerooms_assistantpwd'] = $request['linerooms_assistantpwd'];
            $data['linerooms_patrolpwd'] = $request['linerooms_patrolpwd'];
            $data['linerooms_passwordrequired'] = $request['linerooms_passwordrequired'];
            $data['linerooms_confuserpwd'] = $request['linerooms_confuserpwd'];
            $data['linerooms_autoopenav'] = $request['linerooms_autoopenav'];
            $data['linerooms_maxvideo'] = $request['linerooms_maxvideo'];
            $data['class_branch'] = $request['class_branch'];
            $data['linerooms_sharedesk'] = $request['linerooms_sharedesk'];
            $data['linerooms_createtime'] = time();
            $data['linerooms_updatatime'] = time();
            if ($DataControl->insertData("tkl_linerooms", $data)) {
                $this->Recordweblog("教室管理", "创建教室", "新增教室数据");
                ajax_return(array('error' => 0, 'errortip' => "提交成功!", "bakfuntion" => "successFromTip", "bakurl" => "/{$this->u}"));
            } else {
                ajax_return(array('error' => 1, 'errortip' => "提交失败!", "bakfuntion" => "dangerFromTip"));
            }
        } else {
            $errortipOne = $DataControl->getOne("tkl_errortip", "errortip_code='{$modelArray['result']}'");
            ajax_return(array('error' => 1, 'errortip' => "网课教室创建失败，错误码：{$errortipOne['errortip_txt']}!", "bakfuntion" => "dangerFromTip"));
        }
    }

    function DelAction()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $lineroomsOne = $this->DataControl->getOne("tkl_linerooms", "linerooms_id='{$request['linerooms_id']}'");
        $TalkcloudModel = new \Model\Tlk\TalkcloudModel($this->istaffer['company_id']);
        $putArray = array();
        $putArray['serial'] = $lineroomsOne['linerooms_number'];
        $putArray['fromclass'] = $lineroomsOne['linerooms_fromclass'];
        $modelArray = $TalkcloudModel->exroomDelete($putArray);
        if ($modelArray['result'] == '0') {
            $data = array();
            $data['linerooms_isdel'] = '1';
            if ($this->DataControl->updateData('tkl_linerooms', "linerooms_id='{$request['linerooms_id']}'", $data)) {
                $this->Recordweblog("网课教室管理", "教室管理", "删除教室数据,教室编号：{$lineroomsOne['linerooms_number']}，ID:{$request['linerooms_id']}");
                ajax_return(array('error' => 0, 'errortip' => "提交成功!", "bakfuntion" => "okmotify"));
            } else {
                ajax_return(array('error' => 1, 'errortip' => "教是删除失败!", "bakfuntion" => "errormotify"));
            }
        } else {
            ajax_return(array('error' => 1, 'errortip' => "网课接口删除失败!", "bakfuntion" => "errormotify"));
        }
    }

    function LineDelAction()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $lineroomsOne = $this->DataControl->getOne("smc_linerooms", "linerooms_id='{$request['linerooms_id']}'");
        $TalkcloudModel = new \Model\Tlk\TalkcloudModel($this->istaffer['company_id']);
        $putArray = array();
        $putArray['serial'] = $lineroomsOne['linerooms_number'];
        $putArray['fromclass'] = $lineroomsOne['linerooms_fromclass'];
        $modelArray = $TalkcloudModel->exroomDelete($putArray);
        if ($modelArray['result'] == '0') {
            $data = array();
            $data['linerooms_isdel'] = '1';
            if ($this->DataControl->updateData('tkl_linerooms', "linerooms_id='{$request['linerooms_id']}'", $data)) {
                $this->Recordweblog("网课教室管理", "教室管理", "删除教室数据,教室编号：{$lineroomsOne['linerooms_number']}，ID:{$request['linerooms_id']}");
                ajax_return(array('error' => 0, 'errortip' => "提交成功!", "bakfuntion" => "okmotify"));
            } else {
                ajax_return(array('error' => 1, 'errortip' => "教是删除失败!", "bakfuntion" => "errormotify"));
            }
        } else {
            ajax_return(array('error' => 1, 'errortip' => "网课接口删除失败!", "bakfuntion" => "errormotify"));
        }

    }

    function DelFileAction()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $TalkcloudModel = new \Model\Tlk\TalkcloudModel($this->istaffer['company_id']);
        $putArray = array();
        $putArray['fileid'] = $request['fileid'];
        $putArray['fromclass'] = $request['fromclass'];
        $modelArray = $TalkcloudModel->roomDeletefile($putArray);
        if ($modelArray['result'] == '0') {
            $this->Recordweblog("网课教室管理", "课件管理", "删除教室课件数据，ID:{$request['fileid']}");
            ajax_return(array('error' => 0, 'errortip' => "删除课件成功!", "bakfuntion" => "okmotify"));
        } else {
            ajax_return(array('error' => 1, 'errortip' => "删除课件失败!", "bakfuntion" => "errormotify"));
        }
    }

    function LookView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $dataVar = $this->DataControl->getOne("tkl_linerooms", "linerooms_id='{$request['linerooms_id']}'");

        $TalkcloudModel = new \Model\Tlk\TalkcloudModel($this->istaffer['company_id']);
        $putArray = array();
        $putArray['serial'] = $dataVar['linerooms_number'];
        $putArray['fromclass'] = $dataVar['linerooms_fromclass'];
        $putArray['type'] = 1; //选填 参加用户类型 0: 当前在线人数  1：登录人数
        $modelArray = $TalkcloudModel->exroomonLinenum($putArray);
        if ($modelArray['result'] == '0') {
            $dataVar['loginnums'] = $modelArray['room'][0]['num'];
        } else {
            $dataVar['loginnums'] = 0;
        }
        $putArray = array();
        $putArray['serial'] = $dataVar['linerooms_number'];
        $putArray['fromclass'] = $dataVar['linerooms_fromclass'];
        $putArray['type'] = 0; //选填 参加用户类型 0: 当前在线人数  1：登录人数
        $modelArray = $TalkcloudModel->exroomonLinenum($putArray);
        if ($modelArray['result'] == '0') {
            $dataVar['linenums'] = $modelArray['room'][0]['num'];
        } else {
            $dataVar['linenums'] = 0;
        }
        $this->smarty->assign("dataVar", $dataVar);


        $putArray = array();
        $putArray['serial'] = $dataVar['linerooms_number'];
        $putArray['fromclass'] = $dataVar['linerooms_fromclass'];
        $modelArray = $TalkcloudModel->exgetonLineuser($putArray);
        if ($modelArray['result'] == '0') {
            $this->smarty->assign("onlineUser", $modelArray['onlineuser']);
        }
    }

    function DocumentView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $dataVar = $this->DataControl->getOne("tkl_linerooms", "linerooms_id='{$request['linerooms_id']}'");
        $this->smarty->assign("dataVar", $dataVar);

        $this->smarty->assign("act", "DocumentUpdata");

        $TalkcloudModel = new \Model\Tlk\TalkcloudModel($this->istaffer['company_id']);
        $putArray = array();
        $putArray['serial'] = $dataVar['linerooms_number'];
        $putArray['fromclass'] = $dataVar['linerooms_fromclass'];
        $modelArray = $TalkcloudModel->exroomonGetroomfile($putArray);
        if ($modelArray['result'] == '0') {
            if ($dataVar['linerooms_fromclass'] == '0') {
                $roomFile = $modelArray['roomfile'];
            } else {
                $roomFile = $modelArray['list'];
            }
            foreach ($roomFile as &$roomOne) {
                $roomOne['size'] = $this->switchSize($roomOne['size']);
            }
            $this->smarty->assign("roomFile", $roomFile);
        }
    }

    function LineDocumentView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $dataVar = $this->DataControl->getOne("smc_linerooms", "linerooms_id='{$request['linerooms_id']}'");
        $this->smarty->assign("dataVar", $dataVar);

        // 匹配课件
        $wordFiledId = $this->DataControl->selectClear("
             select DISTINCT cd.line_fileid
             from eas_coursepackage_lessonword as cd
             left join  eas_coursepackage_lesson as cn On cn.lesson_id = cd.lesson_id and cn.package_id = cd.package_id
             left join  eas_coursepackage_apply as cy ON cy.package_id= cn.package_id
             left join smc_class as c ON cy.course_id =c.course_id
             left join smc_class_hour as ch On ch.class_id =c.class_id and cn.lesson_sort = ch.hour_lessontimes
             where c.class_id='{$dataVar['class_id']}' and ch.hour_id = '{$dataVar['hour_id']}' and cn.is_synchro =1
             and cd.line_fileid >0");

        if (is_array($wordFiledId)) {
            $array_field = array_column($wordFiledId, 'line_fileid');
            $file_putArray = array();
            $file_putArray['serial'] = $dataVar['linerooms_threenumber'];
            $file_putArray['fileidarr'] = $array_field;
            $TalkcloudModel = new \Model\Tlk\TalkcloudModel($this->istaffer['company_id']);
            $TalkcloudModel->ex_roombindfile($file_putArray);
        }

        $this->smarty->assign("act", "LineDocumentUpdata");
        $TalkcloudModel = new \Model\Tlk\TalkcloudModel($this->istaffer['company_id']);
        $putArray = array();
        $putArray['serial'] = $dataVar['linerooms_threenumber'];
        $putArray['fromclass'] = $dataVar['linerooms_fromclass'];
        $modelArray = $TalkcloudModel->exroomonGetroomfile($putArray);
        if ($modelArray['result'] == '0') {
            if ($dataVar['linerooms_fromclass'] == '0') {
                $roomFile = $modelArray['roomfile'];
            } else {
                $roomFile = $modelArray['list'];
            }
            foreach ($roomFile as &$roomOne) {
                $roomOne['size'] = $this->switchSize($roomOne['size']);
            }
            $this->smarty->assign("roomFile", $roomFile);
        }

    }

    function LogininfoView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $dataVar = $this->DataControl->getOne("tkl_linerooms", "linerooms_id='{$request['linerooms_id']}'");
        $this->smarty->assign("dataVar", $dataVar);

        $TalkcloudModel = new \Model\Tlk\TalkcloudModel($this->istaffer['company_id']);
        $putArray = array();
        $putArray['serial'] = $dataVar['linerooms_number'];
        $putArray['fromclass'] = $dataVar['linerooms_fromclass'];
        $putArray['starttime'] = time() - 3600 * 24 * 30;
        $putArray['endtime'] = time();
        $modelArray = $TalkcloudModel->exgetLogininfo($putArray);
        if ($modelArray['result'] == '0') {
            if ($dataVar['linerooms_fromclass'] == '0') {
                $apiArray = $modelArray['logininfo'];
            } else {
                $apiArray = $modelArray['list'];
            }
            $this->smarty->assign("loginInfo", $apiArray);
        }
    }

    function LineLogininfoView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $dataVar = $this->DataControl->getOne("smc_linerooms", "linerooms_id='{$request['linerooms_id']}'");
        $this->smarty->assign("dataVar", $dataVar);

        $TalkcloudModel = new \Model\Tlk\TalkcloudModel($this->istaffer['company_id']);
        $putArray = array();
        $putArray['serial'] = $dataVar['linerooms_threenumber'];
        $putArray['fromclass'] = $dataVar['linerooms_fromclass'];
        $putArray['starttime'] = time() - 3600 * 24 * 30;
        $putArray['endtime'] = time();
        $modelArray = $TalkcloudModel->exgetLogininfo($putArray);
        if ($modelArray['result'] == '0') {
            if ($dataVar['linerooms_fromclass'] == '0') {
                $apiArray = $modelArray['logininfo'];
            } else {
                $apiArray = $modelArray['list'];
            }
            $this->smarty->assign("loginInfo", $apiArray);
        }

    }

    function DocumentUpdataAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $dataVar = $this->DataControl->getOne("tkl_linerooms", "linerooms_id='{$request['linerooms_id']}'");
        $TalkcloudModel = new \Model\Tlk\TalkcloudModel($this->istaffer['company_id']);
        $putArray = array();
        $putArray['serial'] = $dataVar['linerooms_number'];
        $putArray['fromclass'] = $dataVar['linerooms_fromclass'];
        $putArray['filedata'] = $_FILES['filedata'];
        $modelArray = $TalkcloudModel->exuploadFile($putArray);
        if ($modelArray['result'] == '0') {
            jserror_return("文件上传成功！");
        } else {
            jserror_return("文件上传失败！");
        }
    }

    function LineDocumentUpdataAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $dataVar = $this->DataControl->getOne("smc_linerooms", "linerooms_id='{$request['linerooms_id']}'");
        $TalkcloudModel = new \Model\Tlk\TalkcloudModel($this->istaffer['company_id']);
        $putArray = array();
        $putArray['serial'] = $dataVar['linerooms_threenumber'];
        $putArray['fromclass'] = $dataVar['linerooms_fromclass'];
        $putArray['filedata'] = $_FILES['filedata'];
        $modelArray = $TalkcloudModel->exuploadFile($putArray);
        if ($modelArray['result'] == '0') {
            jserror_return("文件上传成功！");
        } else {
            jserror_return("文件上传失败！");
        }

    }

    function switchSize($size)
    {
        $units = array(' B', ' KB', ' MB', ' GB', ' TB');
        for ($i = 0; $size >= 1024 && $i < 4; $i++) {
            $size /= 1024;
        }
        return round($size, 2) . $units[$i];
    }

    //商品订单
    function TracksView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $DataControl = $this->DataControl;
        $smarty = $this->smarty;

        if (!isset($request['p'])) {
            $p = '1';
        } else {
            $p = $request['p'];
        }

        $datawhere = "1";
        $pageurl = "/{$this->u}/{$this->t}?";

        $datatype = array();
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and t.tracks_title like '%{$request['keyword']}%' ";
            $pageurl .= "&keyword={$request['keyword']}";
            $datatype['keyword'] = $request['keyword'];
        }

        if (isset($request['order_pid'])) {
            $datawhere .= " and t.order_pid = '{$request['order_pid']}'";
            $pageurl .= "&order_pid={$request['order_pid']}";
            $datatype['order_pid'] = $request['order_pid'];
        }

        $sql = "SELECT t.* FROM wel_claims_ordertracks as t where {$datawhere} order by t.tracks_id DESC";

        $db_nums = $DataControl->select("SELECT COUNT(*) FROM wel_claims_ordertracks as t where {$datawhere}");//相关条件下的总记录数COUNT(*)
        $allnum = $db_nums[0][0];

        $datalist = $DataControl->dbwherePage($sql, $allnum, '10', $pageurl . '&p=', $p, '10', '2');

        $smarty->assign("pagelist", $datalist['pages']);//筛选信息

        $smarty->assign("dataList", $datalist['cont']);
        $smarty->assign("datatype", $datatype);
    }

    function okPlayAction()
    {
        $request = Input('get.', '', 'trim,addslashes');

        if ($request['order_id'] == '') {
            ajax_return(array('error' => 1, 'errortip' => "未传入订单ID！", "bakfuntion" => "errormotify"));
        }
        $claimsorderOne = $this->DataControl->getOne("wel_claims_order", "order_id='{$request['order_id']}'");
        /**更新订单**/
        $upordering = array();
        $upordering['order_serviceState'] = '2';
        $upordering['order_edittime'] = time();
        if (!$this->DataControl->updateData('wel_claims_order', "order_id='{$claimsorderOne['order_id']}'", $upordering)) {
            ajax_return(array('error' => 1, 'errortip' => "处理失败！", "bakfuntion" => "errormotify"));
        } else {
            /**订单跟踪**/
            $date = array();
            $date['order_pid'] = $claimsorderOne['order_pid'];
            $date['tracks_title'] = '完成理赔订单';
            $date['tracks_information'] = '工作人员已审核理赔资料，并确认完成理赔订单！';
            $date['tracks_playname'] = $this->istaffer['user_name'];
            $date['tracks_time'] = time();
            $this->DataControl->insertData("wel_claims_ordertracks", $date);

            $this->Recordweblog("理赔管理", "理赔订单管理", "完成理赔订单，理赔订单编号:{$claimsorderOne['order_pid']}");
            ajax_return(array('error' => 0, 'errortip' => "处理成功!", "bakfuntion" => "okmotify"));
        }
    }

    //魔术方法
    function __destruct()
    {
        if ($this->c == 'Wait') {
            $this->smarty->assign("u", $this->u);
            $this->smarty->assign("t", $this->t);
            $this->display($this->Viewhtm);
            exit;
        }
    }
}