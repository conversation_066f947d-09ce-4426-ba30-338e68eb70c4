<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/3/11
 * Time: 14:05
 */

namespace Work\Controller\Talkline;


class SuperviseController extends viewTpl{
    public $data;
    public $ishare;
    public $Viewhtm;
    public $u;
    public $t;
    public $c;

    function __construct() {
        parent::__construct ();
        if(!$this->check_login()){
            $this->LoginView();
        }
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
        $this->istaffer = $this->StafferLogin;
        $this->smarty->assign("istaffer", $this->StafferLogin);
        $this->Viewhtm = $this->router->getController()."/".$this->router->getUrl().".htm";

    }

    //主页
    function HomeView()
    {
        $request = Input('get.','','trim,addslashes');
        $smarty = $this->smarty;

        if(!isset($request['p'])){
            $p = '1';
        }else{
            $p= $request['p'];
        }

        $datawhere = "c.school_id = s.school_id AND c.student_id = d.student_id";
        $pageurl = "/{$this->u}/{$this->t}?";

        $datatype = array();
        if(isset($request['keyword']) && $request['keyword'] !==''){
            $datawhere .= " and (c.order_pid = '{$request['keyword']}' or d.student_branch like '%{$request['keyword']}%')";
            $pageurl .="&keyword={$request['keyword']}";
            $datatype['keyword'] = $request['keyword'];
        }

        if(isset($request['charge_type']) && $request['charge_type'] !==''){
            $datawhere .= " and c.charge_type = '{$request['charge_type']}'";
            $pageurl .="&charge_type={$request['charge_type']}";
            $datatype['charge_type'] = $request['charge_type'];
        }

        $sql = "SELECT c.charge_id,
	s.school_branch,
	s.school_cnname,
	(
		SELECT
			g.companies_cnname
		FROM
			gmc_code_companies AS g
		WHERE
			g.companies_id = c.companies_id
	) AS companies_cnname,
	d.student_branch,
	d.student_cnname,
	(
		SELECT
			u.course_branch
		FROM
			smc_course AS u
		WHERE
			u.course_id = c.course_id
	) AS course_branch,
	c.charge_type,
	c.order_pid,
	c.pay_pid,
	c.pay_price,
	pay_successtime
FROM
	cmb_trans_charge AS c,
	smc_school AS s,
	smc_student AS d WHERE {$datawhere}  order by c.pay_successtime DESC";

        $db_nums = $this->DataControl->selectOne("SELECT COUNT(c.charge_id) as countnums FROM cmb_trans_charge AS c, smc_school AS s, smc_student AS d WHERE {$datawhere}");//相关条件下的总记录数COUNT(*)
        $allnum = $db_nums['countnums'];

        $datalist = $this->DataControl->dbwherePage($sql,$allnum,'20',$pageurl.'&p=',$p,'10','2');

        $smarty->assign("pagelist",$datalist['pages']);//筛选信息

        $smarty->assign("dataList",$datalist['cont']);
        $smarty->assign("datatype",$datatype);
    }

    function ForeignView(){
        $request = Input('get.','','trim,addslashes');
        if(isset($request['day']) && $request['day'] !=='0'){
            $thisweek = GetWeekAll($request['day']);
            $this->smarty->assign("day",$request['day']);
        }else{
            $thisweek = GetWeekAll(date("Y-m-d",time()));
            $this->smarty->assign("day",date("Y-m-d",time()));
        }
        $this->smarty->assign("thisweek",$thisweek);


        $stafferList = $this->DataControl->selectClear("SELECT s.staffer_id, l.school_cnname, s.staffer_cnname, s.staffer_enname, s.staffer_branch
FROM smc_class_hour_teaching AS t, smc_class_hour AS h, smc_class AS c, smc_staffer AS s, gmc_staffer_postbe AS p, smc_school AS l
WHERE t.class_id = c.class_id
AND t.staffer_id = s.staffer_id
AND t.hour_id = h.hour_id
AND s.staffer_id = p.staffer_id
AND p.school_id = l.school_id
AND p.school_id <> '0'
AND s.staffer_isparttime = '0'
AND c.company_id = '{$this->istaffer['company_id']}'
AND t.teachtype_code = '05EM'
AND h.hour_day <= '{$thisweek['nowweek_end']}'
AND h.hour_day >= '{$thisweek['nowweek_start']}'
GROUP BY s.staffer_id ORDER BY l.school_id DESC");
        if($stafferList){
            foreach($stafferList as &$stafferOne){
                $hourList = $this->DataControl->selectClear("SELECT
    s.school_cnname,
	c.class_cnname,
	c.class_enname,
	c.class_branch,
	h.hour_name,
	h.hour_day,
	h.hour_starttime,
	h.hour_endtime
FROM
	smc_class_hour_teaching AS t,
	smc_class_hour AS h,
	smc_class AS c,smc_school AS s
WHERE
	t.class_id = c.class_id
AND t.hour_id = h.hour_id AND c.school_id = s.school_id
AND h.hour_day <= '{$thisweek['nowweek_end']}'
AND h.hour_day >= '{$thisweek['nowweek_start']}'
AND t.staffer_id = '{$stafferOne['staffer_id']}'");

                $weekArray = array();
                for($i=0;$i<7;$i++){
                    $weekOne = array();
                    $weekday = date("Y-m-d",strtotime($thisweek['nowweek_start'])+3600*24*$i);
                    $jinDay = mktime(0,0,0,date("m",time()),date("d",time()),date("Y",time()));
                    for ($t=16;$t<41;$t++) {
                        $statTimes = date("H:i",$jinDay+$t*30*60);
                        $endTimes = date("H:i",$jinDay+($t+1)*30*60);
                        $timecouse = '';
                        foreach($hourList as $hourOne){
                            if($hourOne['hour_day'] == $weekday && is_time_cross(strtotime($statTimes),strtotime($endTimes),strtotime($hourOne['hour_starttime']),strtotime($hourOne['hour_endtime']))){
                                $timecouse = "校区：{$hourOne['school_cnname']}\n班级：{$hourOne['class_cnname']}{$hourOne['class_enname']} \n时间：({$hourOne['hour_starttime']}-{$hourOne['hour_endtime']})";
                            }
                        }
                        $weekOne[] = $timecouse;
                    }
                    $weekArray[] = $weekOne;
                }

                $stafferOne['weekjson'] = json_encode($weekArray,JSON_UNESCAPED_UNICODE);
            }
        }
        $this->smarty->assign("stafferList",$stafferList);
    }


    //魔术方法
    function __destruct()
    {
        if ($this->c =='Wait') {
            $this->smarty->assign("u", $this->u);
            $this->smarty->assign("t", $this->t);
            $this->display($this->Viewhtm);
            exit;
        }
    }
}