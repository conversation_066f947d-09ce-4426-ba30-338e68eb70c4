<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/2/7
 * Time: 11:33
 */

namespace Work\Controller\Talkline;


class FinancialController extends viewTpl{
    public $data;
    public $ishare;
    public $Viewhtm;
    public $u;
    public $t;
    public $c;

    function __construct() {
        parent::__construct ();
        if(!$this->check_login()){
            $this->LoginView();
        }
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
        $this->istaffer = $this->StafferLogin;
        $this->smarty->assign("istaffer", $this->StafferLogin);
        $this->Viewhtm = $this->router->getController()."/".$this->router->getUrl().".htm";
    }

    //主页
    function HomeView()
    {
        $request = Input('get.','','trim,addslashes');
        $DataControl = $this->DataControl;
        $smarty = $this->smarty;

        if(!isset($request['p'])){
            $p = '1';
        }else{
            $p= $request['p'];
        }

        $datawhere = "o.company_id = '{$this->istaffer['company_id']}'";
        $pageurl = "/{$this->u}/{$this->t}?";

        $datatype = array();
        if(isset($request['keyword']) && $request['keyword'] !==''){
            $datawhere .= " and (o.log_reason like '%{$request['keyword']}%' or o.log_playname like '%{$request['keyword']}%')";
            $pageurl .="&keyword={$request['keyword']}";
            $datatype['keyword'] = $request['keyword'];
        }

        if(isset($request['log_class']) && $request['log_class'] !==''){
            $datawhere .= " and o.log_class = '{$request['log_class']}'";
            $pageurl .="&log_class={$request['log_class']}";
            $datatype['log_class'] = $request['log_class'];
        }

        if(isset($request['editstarttime']) && $request['editstarttime'] !== ''){
            $starttime = strtotime($request['editstarttime']);
            $datawhere .= " and o.log_time >= '{$starttime}'";
            $pageurl .="&editstarttime={$request['editstarttime']}";
            $datatype['editstarttime'] = $request['editstarttime'];
        }

        if(isset($request['editendtime']) && $request['editendtime'] !== ''){
            $endtime = strtotime($request['editendtime'])+3600*23;
            $datawhere .= " and o.log_time <= '{$endtime}'";
            $pageurl .="&editendtime={$request['editendtime']}";
            $datatype['editendtime'] = $request['editendtime'];
        }

        $sql = "SELECT o.* FROM tkl_balance_log as o where {$datawhere}  order by o.log_id DESC";

        $db_nums = $DataControl->selectOne("SELECT COUNT(o.log_id) as countnums FROM tkl_balance_log as o where {$datawhere}");//相关条件下的总记录数COUNT(*)
        $allnum = $db_nums['countnums'];

        $datalist = $DataControl->dbwherePage($sql,$allnum,'10',$pageurl.'&p=',$p,'10','2');

        $smarty->assign("pagelist",$datalist['pages']);//筛选信息

        $smarty->assign("dataList",$datalist['cont']);
        $smarty->assign("datatype",$datatype);


        $priceArray = array();
        $priceArray['all']['cnname'] = '费用总计';
        $priceArray['2020']['cnname'] = '2020年';
        $priceArray['2021']['cnname'] = '2021年';
        $priceArray['2022']['cnname'] = '2022年';

        $balanceOne = $this->DataControl->selectOne("SELECT sum(l.log_playamount) as allplayamount
        FROM tkl_balance_log AS l WHERE l.company_id='{$this->istaffer['company_id']}' limit 0,1");
        $priceArray['all']['balanceOne'] = $balanceOne;

        $balanceOne = $this->DataControl->selectOne("SELECT sum(l.log_playamount) as allplayamount
        FROM tkl_balance_log AS l WHERE l.company_id='{$this->istaffer['company_id']}'
        AND from_unixtime(l.log_time,'%Y-%m-%d') < '2021-01-01' AND from_unixtime(l.log_time,'%Y-%m-%d') >= '2020-01-01'  limit 0,1");
        $priceArray['2020']['balanceOne'] = $balanceOne;

        $balanceOne = $this->DataControl->selectOne("SELECT sum(l.log_playamount) as allplayamount
        FROM tkl_balance_log AS l WHERE l.company_id='{$this->istaffer['company_id']}'
        AND from_unixtime(l.log_time,'%Y-%m-%d') < '2022-01-01' AND from_unixtime(l.log_time,'%Y-%m-%d') >= '2021-01-01'  limit 0,1");
        $priceArray['2021']['balanceOne'] = $balanceOne;

        $balanceOne = $this->DataControl->selectOne("SELECT sum(l.log_playamount) as allplayamount
        FROM tkl_balance_log AS l WHERE l.company_id='{$this->istaffer['company_id']}'
        AND from_unixtime(l.log_time,'%Y-%m-%d') < '2023-01-01' AND from_unixtime(l.log_time,'%Y-%m-%d') >= '2022-01-01'  limit 0,1");
        $priceArray['2022']['balanceOne'] = $balanceOne;


        $priceOne = $this->DataControl->selectOne("SELECT
        ROUND((sum(l.tkylog_duration)/60),2) AS durationnums,
        ROUND((sum(l.tkylog_duration)/3600),2) AS hoursnums,
        ROUND((sum(l.tkylog_paytime)/60),2) AS paytimenums,
        ROUND((sum(l.tkylog_paytime)/3600),2) AS payhoursnums,
        ROUND(((sum(l.tkylog_paytime)/60)*0.0392),2) AS payprice
        FROM tkl_linerooms_tkylog AS l WHERE l.company_id='{$this->istaffer['company_id']}' AND l.tkylog_fromclass = '0' limit 0,1");
        $priceArray['all']['tkyOne'] = $priceOne;

        $priceOne = $this->DataControl->selectOne("SELECT
        ROUND((sum(l.tkylog_duration)/60),2) AS durationnums,
        ROUND((sum(l.tkylog_duration)/3600),2) AS hoursnums,
        ROUND((sum(l.tkylog_paytime)/60),2) AS paytimenums,
        ROUND((sum(l.tkylog_paytime)/3600),2) AS payhoursnums,
        ROUND(((sum(l.tkylog_paytime)/60)*0.0392),2) AS payprice
        FROM tkl_linerooms_tkylog AS l WHERE l.company_id='{$this->istaffer['company_id']}' AND l.tkylog_fromclass = '0'
        AND l.tkylog_entertime < '2021-01-01 00:00:01' AND l.tkylog_entertime >= '2020-01-01 00:00:01'
        limit 0,1");
        $priceArray['2020']['tkyOne'] = $priceOne;

        $priceOne = $this->DataControl->selectOne("SELECT
        ROUND((sum(l.tkylog_duration)/60),2) AS durationnums,
        ROUND((sum(l.tkylog_duration)/3600),2) AS hoursnums,
        ROUND((sum(l.tkylog_paytime)/60),2) AS paytimenums,
        ROUND((sum(l.tkylog_paytime)/3600),2) AS payhoursnums,
        ROUND(((sum(l.tkylog_paytime)/60)*0.0392),2) AS payprice
        FROM tkl_linerooms_tkylog AS l WHERE l.company_id='{$this->istaffer['company_id']}' AND l.tkylog_fromclass = '0'
        AND l.tkylog_entertime < '2022-01-01 00:00:01' AND l.tkylog_entertime >= '2021-01-01 00:00:01'
        limit 0,1");
        $priceArray['2021']['tkyOne'] = $priceOne;

        $priceOne = $this->DataControl->selectOne("SELECT
        ROUND((sum(l.tkylog_duration)/60),2) AS durationnums,
        ROUND((sum(l.tkylog_duration)/3600),2) AS hoursnums,
        ROUND((sum(l.tkylog_paytime)/60),2) AS paytimenums,
        ROUND((sum(l.tkylog_paytime)/3600),2) AS payhoursnums,
        ROUND(((sum(l.tkylog_paytime)/60)*0.0392),2) AS payprice
        FROM tkl_linerooms_tkylog AS l WHERE l.company_id='{$this->istaffer['company_id']}' AND l.tkylog_fromclass = '0'
        AND l.tkylog_entertime < '2023-01-01 00:00:01' AND l.tkylog_entertime >= '2022-01-01 00:00:01'
        limit 0,1");
        $priceArray['2022']['tkyOne'] = $priceOne;



        $YspriceOne = $this->DataControl->selectOne("SELECT
        ROUND((sum(l.yunshulog_durationSecond)/60),2) AS durationnums,
        ROUND((sum(l.yunshulog_durationSecond)/3600),2) AS hoursnums,
        ROUND((sum(l.yunshulog_tadurationSecond)/60),2) AS paytimenums,
        ROUND((sum(l.yunshulog_tadurationSecond)/3600),2) AS payhoursnums,
        ROUND(sum(l.yunshulog_totalprice),2) AS payprice
        FROM tkl_linerooms_yunshulog AS l WHERE l.company_id='{$this->istaffer['company_id']}' limit 0,1");
        $priceArray['all']['ysOne'] = $YspriceOne;

        $YspriceOne = $this->DataControl->selectOne("SELECT
        ROUND((sum(l.yunshulog_durationSecond)/60),2) AS durationnums,
        ROUND((sum(l.yunshulog_durationSecond)/3600),2) AS hoursnums,
        ROUND((sum(l.yunshulog_tadurationSecond)/60),2) AS paytimenums,
        ROUND((sum(l.yunshulog_tadurationSecond)/3600),2) AS payhoursnums,
        ROUND(sum(l.yunshulog_totalprice),2) AS payprice
        FROM tkl_linerooms_yunshulog AS l WHERE l.company_id='{$this->istaffer['company_id']}'
        AND l.yunshulog_endtime < '2021-01-01 00:00:01' AND l.yunshulog_endtime >= '2020-01-01 00:00:01'
        limit 0,1");
        $priceArray['2020']['ysOne'] = $YspriceOne;

        $YspriceOne = $this->DataControl->selectOne("SELECT
        ROUND((sum(l.yunshulog_durationSecond)/60),2) AS durationnums,
        ROUND((sum(l.yunshulog_durationSecond)/3600),2) AS hoursnums,
        ROUND((sum(l.yunshulog_tadurationSecond)/60),2) AS paytimenums,
        ROUND((sum(l.yunshulog_tadurationSecond)/3600),2) AS payhoursnums,
        ROUND(sum(l.yunshulog_totalprice),2) AS payprice
        FROM tkl_linerooms_yunshulog AS l WHERE l.company_id='{$this->istaffer['company_id']}'
        AND l.yunshulog_endtime < '2022-01-01 00:00:01' AND l.yunshulog_endtime >= '2021-01-01 00:00:01'
        limit 0,1");
        $priceArray['2021']['ysOne'] = $YspriceOne;

        $YspriceOne = $this->DataControl->selectOne("SELECT
        ROUND((sum(l.yunshulog_durationSecond)/60),2) AS durationnums,
        ROUND((sum(l.yunshulog_durationSecond)/3600),2) AS hoursnums,
        ROUND((sum(l.yunshulog_tadurationSecond)/60),2) AS paytimenums,
        ROUND((sum(l.yunshulog_tadurationSecond)/3600),2) AS payhoursnums,
        ROUND(sum(l.yunshulog_totalprice),2) AS payprice
        FROM tkl_linerooms_yunshulog AS l WHERE l.company_id='{$this->istaffer['company_id']}'
        AND l.yunshulog_endtime < '2023-01-01 00:00:01' AND l.yunshulog_endtime >= '2022-01-01 00:00:01'
        limit 0,1");
        $priceArray['2022']['ysOne'] = $YspriceOne;

        $smarty->assign("priceArray",$priceArray);
    }

    function ExpendView(){
        $request = Input('get.','','trim,addslashes');
        $DataControl = $this->DataControl;
        $smarty = $this->smarty;

        if(!isset($request['p'])){
            $p = '1';
        }else{
            $p= $request['p'];
        }

        $datawhere = "o.linerooms_id = r.linerooms_id AND r.school_branch = s.school_branch AND o.tkylog_fromclass = '0'
        and o.company_id = '{$this->istaffer['company_id']}'";
        $pageurl = "/{$this->u}/{$this->t}?";

        $datatype = array();
        if(isset($request['keyword']) && $request['keyword'] !==''){
            $datawhere .= " and (r.linerooms_name like '%{$request['keyword']}%' or r.linerooms_number like '%{$request['keyword']}%')";
            $pageurl .="&keyword={$request['keyword']}";
            $datatype['keyword'] = $request['keyword'];
        }

        if(isset($request['linerooms_id']) && $request['linerooms_id'] !==''){
            $datawhere .= " and o.linerooms_id = '{$request['linerooms_id']}'";
            $pageurl .="&linerooms_id={$request['linerooms_id']}";
            $datatype['linerooms_id'] = $request['linerooms_id'];

            $lineroomsOne = $this->DataControl->selectOne("SELECT r.* FROM tkl_linerooms AS r WHERE r.linerooms_id='{$request['linerooms_id']}' limit 0,1");
            if($lineroomsOne){
                $datatype['keyword'] = $lineroomsOne['linerooms_number'];
            }
        }
        if(isset($request['school_branch']) && $request['school_branch'] !==''){
            $datawhere .= " and r.school_branch = '{$request['school_branch']}'";
            $pageurl .="&school_branch={$request['school_branch']}";
            $datatype['school_branch'] = $request['school_branch'];
        }

        if(isset($request['linerooms_fromclass']) && $request['linerooms_fromclass'] !==''){
            $datawhere .= " and r.linerooms_fromclass = '{$request['linerooms_fromclass']}'";
            $pageurl .="&linerooms_fromclass={$request['linerooms_fromclass']}";
            $datatype['linerooms_fromclass'] = $request['linerooms_fromclass'];
        }

        if(isset($request['tkylog_userroleid']) && $request['tkylog_userroleid'] !==''){
            $datawhere .= " and o.tkylog_userroleid = '{$request['tkylog_userroleid']}'";
            $pageurl .="&tkylog_userroleid={$request['tkylog_userroleid']}";
            $datatype['tkylog_userroleid'] = $request['tkylog_userroleid'];
        }

        if(isset($request['editstarttime']) && $request['editstarttime'] !== ''){
            $starttime = "{$request['editstarttime']} 00:00:01";
            $datawhere .= " and o.tkylog_entertime >= '{$starttime}'";
            $pageurl .="&editstarttime={$request['editstarttime']}";
            $datatype['editstarttime'] = $request['editstarttime'];
        }

        if(isset($request['editendtime']) && $request['editendtime'] !== ''){
            $endtime = "{$request['editendtime']} 23:59:59";
            $datawhere .= " and o.tkylog_entertime <= '{$endtime}'";
            $pageurl .="&editendtime={$request['editendtime']}";
            $datatype['editendtime'] = $request['editendtime'];
        }

        $sql = "SELECT o.*,r.linerooms_number,r.linerooms_fromclass,s.school_cnname FROM tkl_linerooms_tkylog as o,tkl_linerooms as r,
	smc_school AS s where {$datawhere}  order by o.tkylog_id DESC";

        $db_nums = $DataControl->selectOne("SELECT COUNT(o.tkylog_id) as countnums
FROM tkl_linerooms_tkylog as o,tkl_linerooms as r, smc_school AS s where {$datawhere} limit 0,1");//相关条件下的总记录数COUNT(*)
        $allnum = $db_nums['countnums'];

        $datalist = $DataControl->dbwherePage($sql,$allnum,'15',$pageurl.'&p=',$p,'10','2');

        $smarty->assign("pagelist",$datalist['pages']);//筛选信息

        $dataList = $datalist['cont'];
        if($dataList){
            foreach($dataList as &$dataOne){
                $dataOne['tkylog_duration'] = round($dataOne['tkylog_duration']/60,2);
                $dataOne['tkylog_payprice'] = round(($dataOne['tkylog_paytime']/60)*0.0392,2);
                $dataOne['tkylog_paytime'] = round($dataOne['tkylog_paytime']/60,2);
            }
        }

        $smarty->assign("dataList",$dataList);
        $smarty->assign("datatype",$datatype);


        $tkylogwhere = "l.company_id='{$this->istaffer['company_id']}' AND l.tkylog_fromclass = '0'";
        if(isset($request['editstarttime']) && $request['editstarttime'] !== ''){
            $starttime = "{$request['editstarttime']} 00:00:01";
            $tkylogwhere .= " and l.tkylog_entertime >= '{$starttime}'";
        }

        if(isset($request['editendtime']) && $request['editendtime'] !== ''){
            $endtime = "{$request['editendtime']} 23:59:59";
            $tkylogwhere .= " and l.tkylog_entertime <= '{$endtime}'";
        }

        $priceOne = $this->DataControl->selectOne("SELECT
        (sum(l.tkylog_duration)/60) AS durationnums
        ,(sum(l.tkylog_paytime)/60) AS paytimenums
        ,ROUND(((sum(l.tkylog_paytime)/60)*0.0392),2) AS payprice
        FROM tkl_linerooms_tkylog AS l WHERE {$tkylogwhere} limit 0,1");
        $smarty->assign("priceOne",$priceOne);
    }

    function ExpendExportAction(){
        $request = Input('post.','','trim,addslashes');
        $datawhere = "o.linerooms_id = r.linerooms_id AND r.school_branch = s.school_branch AND o.tkylog_fromclass = '0'
         and o.company_id = '{$this->istaffer['company_id']}'";

        if(isset($request['keyword']) && $request['keyword'] !==''){
            $datawhere .= " and (r.linerooms_name like '%{$request['keyword']}%' or r.linerooms_number like '%{$request['keyword']}%')";
        }

        if(isset($request['linerooms_id']) && $request['linerooms_id'] !==''){
            $datawhere .= " and o.linerooms_id = '{$request['linerooms_id']}'";
        }else{
            exit("数据量过大，仅支持单教室导出");
        }

        if(isset($request['school_branch']) && $request['school_branch'] !==''){
            $datawhere .= " and r.school_branch = '{$request['school_branch']}'";
        }

        if(isset($request['linerooms_fromclass']) && $request['linerooms_fromclass'] !==''){
            $datawhere .= " and r.linerooms_fromclass = '{$request['linerooms_fromclass']}'";
        }


        if(isset($request['tkylog_userroleid']) && $request['tkylog_userroleid'] !==''){
            $datawhere .= " and o.tkylog_userroleid = '{$request['tkylog_userroleid']}'";
        }

        if(isset($request['editstarttime']) && $request['editstarttime'] !== ''){
            $starttime = "{$request['editstarttime']} 00:00:01";
            $datawhere .= " and o.tkylog_entertime >= '{$starttime}'";
        }

        if(isset($request['editendtime']) && $request['editendtime'] !== ''){
            $endtime = "{$request['editendtime']} 23:59:59";
            $datawhere .= " and o.tkylog_entertime <= '{$endtime}'";
        }

        $sql = "SELECT o.tkylog_id,o.tkylog_username,o.tkylog_userroleid,o.tkylog_entertime
,o.tkylog_outtime,o.tkylog_duration,o.tkylog_paytime,o.tkylog_ipaddress,o.tkylog_createtimetime
,r.linerooms_number,r.linerooms_fromclass,s.school_cnname FROM tkl_linerooms_tkylog as o,tkl_linerooms as r,
	smc_school AS s where {$datawhere}  order by o.tkylog_id DESC";

        $dateexcelarray = $this->DataControl->selectClear($sql);

        if($dateexcelarray) {
            $outexceldate = array();
            foreach ($dateexcelarray as $dateexcelvar) {
                $datearray = array();
                $datearray['tkylog_id'] = $dateexcelvar['tkylog_id'];
                $datearray['school_cnname'] = $dateexcelvar['school_cnname'];
                $datearray['linerooms_number'] = $dateexcelvar['linerooms_number'];
                if($dateexcelvar['linerooms_fromclass'] == '1'){
                    $datearray['linerooms_fromclass'] = '云枢';
                }else{
                    $datearray['linerooms_fromclass'] = '拓课云';
                }
                $datearray['tkylog_username'] = $dateexcelvar['tkylog_username'];
                if($dateexcelvar['tkylog_userroleid'] == '0'){
                    $datearray['tkylog_userroleid'] = '主教';
                }elseif($dateexcelvar['tkylog_userroleid'] == '1'){
                    $datearray['tkylog_userroleid'] = '助教';
                }elseif($dateexcelvar['tkylog_userroleid'] == '2'){
                    $datearray['tkylog_userroleid'] = '学员';
                }else{
                    $datearray['tkylog_userroleid'] = '巡堂';
                }
                $datearray['tkylog_entertime'] = $dateexcelvar['tkylog_entertime'];
                $datearray['tkylog_outtime'] = $dateexcelvar['tkylog_outtime'];

                $datearray['tkylog_duration'] = round($dateexcelvar['tkylog_duration']/60,2);
                $datearray['tkylog_paytime'] = round($dateexcelvar['tkylog_paytime']/60,2);
                $datearray['tkylog_payprice'] = round(($dateexcelvar['tkylog_paytime']/60)*0.0392,2);
                $datearray['tkylog_ipaddress'] = $dateexcelvar['tkylog_ipaddress'];
                $datearray['tkylog_createtimetime'] = date("Y-m-d H:i:s",$dateexcelvar['tkylog_createtimetime']);

                $outexceldate[] = $datearray;
            }
        }

        $excelheader = array("日志ID","校区名称","教室编号","平台类型","用户名","用户角色","进入时间","离开时间","在线时长统计(分)","上课时长统计(分)","结算费用","IP","结算更新时间");
        $excelfileds = array('tkylog_id',"school_cnname",'linerooms_number','linerooms_fromclass','tkylog_username','tkylog_userroleid','tkylog_entertime','tkylog_outtime',"tkylog_duration","tkylog_paytime","tkylog_payprice","tkylog_ipaddress","tkylog_createtimetime");
        $datime = date("mdHi");
        $lineroomsOne = $this->DataControl->selectOne("SELECT r.* FROM tkl_linerooms AS r WHERE r.linerooms_id='{$request['linerooms_id']}' limit 0,1");
        if($lineroomsOne){
            query_to_excel($excelheader,$outexceldate,$excelfileds,"{$lineroomsOne['linerooms_name']}流量消耗明细{$datime}.xlsx");
        }else{
            query_to_excel($excelheader,$outexceldate,$excelfileds,"拓课云教室流量消耗明细{$datime}.xlsx");
        }
    }

    function LineroomsView(){
        $request = Input('get.','','trim,addslashes');
        $DataControl = $this->DataControl;
        $smarty = $this->smarty;

        if(!isset($request['p'])){
            $p = '1';
        }else{
            $p= $request['p'];
        }

        $datawhere = "o.linerooms_id = r.linerooms_id AND r.school_branch = s.school_branch AND o.tkylog_fromclass = '0'
        and o.company_id = '{$this->istaffer['company_id']}'";
        $pageurl = "/{$this->u}/{$this->t}?";

        $datatype = array();
        if(isset($request['keyword']) && $request['keyword'] !==''){
            $datawhere .= " and (r.linerooms_name like '%{$request['keyword']}%' or r.linerooms_number like '%{$request['keyword']}%')";
            $pageurl .="&keyword={$request['keyword']}";
            $datatype['keyword'] = $request['keyword'];
        }

        if(isset($request['school_branch']) && $request['school_branch'] !==''){
            $datawhere .= " and r.school_branch = '{$request['school_branch']}'";
            $pageurl .="&school_branch={$request['school_branch']}";
            $datatype['school_branch'] = $request['school_branch'];
        }
        if(isset($request['linerooms_isdel']) && $request['linerooms_isdel'] !==''){
            $datawhere .= " and r.linerooms_isdel = '{$request['linerooms_isdel']}'";
            $pageurl .="&linerooms_isdel={$request['linerooms_isdel']}";
            $datatype['linerooms_isdel'] = $request['linerooms_isdel'];
        }

        if(isset($request['linerooms_fromclass']) && $request['linerooms_fromclass'] !==''){
            $datawhere .= " and r.linerooms_fromclass = '{$request['linerooms_fromclass']}'";
            $pageurl .="&linerooms_fromclass={$request['linerooms_fromclass']}";
            $datatype['linerooms_fromclass'] = $request['linerooms_fromclass'];
        }

        if(isset($request['editstarttime']) && $request['editstarttime'] !== ''){
            $starttime = date("Y-m-d H:i:s",strtotime($request['editstarttime']));
            $datawhere .= " and o.tkylog_entertime >= '{$starttime}'";
            $pageurl .="&editstarttime={$request['editstarttime']}";
            $datatype['editstarttime'] = $request['editstarttime'];
        }

        if(isset($request['editendtime']) && $request['editendtime'] !== ''){
            $endtime = date("Y-m-d H:i:s",strtotime($request['editendtime'])+3600*23);
            $datawhere .= " and o.tkylog_entertime <= '{$endtime}'";
            $pageurl .="&editendtime={$request['editendtime']}";
            $datatype['editendtime'] = $request['editendtime'];
        }

        $sql = "SELECT r.linerooms_id,
sum(o.tkylog_duration) as tkylog_duration,
sum(o.tkylog_paytime) as tkylog_paytime,
r.linerooms_number,r.linerooms_name,r.linerooms_fromclass,r.linerooms_flowtime,s.school_cnname
FROM tkl_linerooms_tkylog as o,tkl_linerooms as r,smc_school AS s where {$datawhere} GROUP BY r.linerooms_id order by o.tkylog_id DESC";
        $db_nums = $DataControl->selectOne("SELECT COUNT(DISTINCT r.linerooms_id) AS  nums
FROM tkl_linerooms_tkylog as o,tkl_linerooms as r, smc_school AS s where {$datawhere} limit 0,1");//相关条件下的总记录数COUNT(*)
        $allnum = $db_nums['nums'];

        $datalist = $DataControl->dbwherePage($sql,$allnum,'15',$pageurl.'&p=',$p,'10','2');

        $smarty->assign("pagelist",$datalist['pages']);//筛选信息

        $dataList = $datalist['cont'];
        if($dataList){
            foreach($dataList as &$dataOne){
                $dataOne['tkylog_duration'] = round($dataOne['tkylog_duration']/60,2);
                $dataOne['tkylog_payprice'] = round(($dataOne['tkylog_paytime']/60)*0.0392,2);
                $dataOne['tkylog_paytime'] = round($dataOne['tkylog_paytime']/60,2);
            }
        }

        $smarty->assign("dataList",$dataList);
        $smarty->assign("datatype",$datatype);


        $priceOne = $this->DataControl->selectOne("SELECT
        (sum(l.tkylog_duration)/60) AS durationnums
        ,(sum(l.tkylog_paytime)/60) AS paytimenums
        ,ROUND(((sum(l.tkylog_paytime)/60)*0.0392),2) AS payprice
        FROM tkl_linerooms_tkylog AS l WHERE l.company_id='{$this->istaffer['company_id']}' AND l.tkylog_fromclass = '0' limit 0,1");
        $smarty->assign("priceOne",$priceOne);
    }

    function ExportLineroomsAction(){
        $request = Input('post.','','trim,addslashes');
        $datawhere = "o.linerooms_id = r.linerooms_id AND r.school_branch = s.school_branch AND o.tkylog_fromclass = '0'
        and o.company_id = '{$this->istaffer['company_id']}'";

        if(isset($request['keyword']) && $request['keyword'] !==''){
            $datawhere .= " and (r.linerooms_name like '%{$request['keyword']}%' or r.linerooms_number like '%{$request['keyword']}%')";
        }

        if(isset($request['school_branch']) && $request['school_branch'] !==''){
            $datawhere .= " and r.school_branch = '{$request['school_branch']}'";
        }
        if(isset($request['linerooms_isdel']) && $request['linerooms_isdel'] !==''){
            $datawhere .= " and r.linerooms_isdel = '{$request['linerooms_isdel']}'";
        }

        if(isset($request['linerooms_fromclass']) && $request['linerooms_fromclass'] !==''){
            $datawhere .= " and r.linerooms_fromclass = '{$request['linerooms_fromclass']}'";
        }

        if(isset($request['editstarttime']) && $request['editstarttime'] !== ''){
            $starttime = date("Y-m-d H:i:s",strtotime($request['editstarttime']));
            $datawhere .= " and o.tkylog_entertime >= '{$starttime}'";
        }

        if(isset($request['editendtime']) && $request['editendtime'] !== ''){
            $endtime = date("Y-m-d H:i:s",strtotime($request['editendtime'])+3600*23);
            $datawhere .= " and o.tkylog_entertime <= '{$endtime}'";
        }

        $sql = "SELECT r.linerooms_id,
 sum(o.tkylog_duration) as tkylog_duration, sum(o.tkylog_paytime) as tkylog_paytime, r.linerooms_name, r.linerooms_number,r.linerooms_fromclass,s.school_cnname
FROM tkl_linerooms_tkylog as o,tkl_linerooms as r,smc_school AS s where {$datawhere} GROUP BY r.linerooms_id order by o.tkylog_id DESC";

        $dateexcelarray = $this->DataControl->selectClear($sql);

        if($dateexcelarray) {
            $outexceldate = array();
            foreach ($dateexcelarray as $dateexcelvar) {
                $datearray = array();
                $datearray['linerooms_id'] = $dateexcelvar['linerooms_id'];
                $datearray['school_cnname'] = $dateexcelvar['school_cnname'];
                $datearray['linerooms_name'] = $dateexcelvar['linerooms_name'];
                $datearray['linerooms_number'] = $dateexcelvar['linerooms_number'];
                if($dateexcelvar['linerooms_fromclass'] == '1'){
                    $datearray['linerooms_fromclass'] = '云枢';
                }else{
                    $datearray['linerooms_fromclass'] = '拓课云';
                }
                $datearray['tkylog_duration'] = round($dateexcelvar['tkylog_duration']/60,2);
                $datearray['tkylog_paytime'] = round($dateexcelvar['tkylog_paytime']/60,2);
                $datearray['tkylog_payprice'] = round(($dateexcelvar['tkylog_paytime']/60)*0.0392,2);

                $outexceldate[] = $datearray;
            }
        }

        $excelheader = array("教室ID","校区名称","教室名称","教室编号","平台类型","在线时长统计(分)","上课时长统计(分)","结算费用");
        $excelfileds = array('linerooms_id',"school_cnname","linerooms_name",'linerooms_number','linerooms_fromclass',"tkylog_duration","tkylog_paytime","tkylog_payprice");
        $datime = date("mdHi");
        query_to_excel($excelheader,$outexceldate,$excelfileds,"拓课云校园教室流量使用统计{$datime}.xlsx");
    }

    function YunshuroomsView(){
        $request = Input('get.','','trim,addslashes');
        $DataControl = $this->DataControl;
        $smarty = $this->smarty;

        if(!isset($request['p'])){
            $p = '1';
        }else{
            $p= $request['p'];
        }

        $datawhere = "r.linerooms_fromclass = '1' and r.company_id = '{$this->istaffer['company_id']}'";
        $pageurl = "/{$this->u}/{$this->t}?";

        $datatype = array();
        if(isset($request['keyword']) && $request['keyword'] !==''){
            $datawhere .= " and (r.linerooms_name like '%{$request['keyword']}%' or r.linerooms_number like '%{$request['keyword']}%')";
            $pageurl .="&keyword={$request['keyword']}";
            $datatype['keyword'] = $request['keyword'];
        }

        if(isset($request['school_branch']) && $request['school_branch'] !==''){
            $datawhere .= " and r.school_branch = '{$request['school_branch']}'";
            $pageurl .="&school_branch={$request['school_branch']}";
            $datatype['school_branch'] = $request['school_branch'];
        }
        if(isset($request['linerooms_isdel']) && $request['linerooms_isdel'] !==''){
            $datawhere .= " and r.linerooms_isdel = '{$request['linerooms_isdel']}'";
            $pageurl .="&linerooms_isdel={$request['linerooms_isdel']}";
            $datatype['linerooms_isdel'] = $request['linerooms_isdel'];
        }

        $sql = "SELECT r.linerooms_id,r.linerooms_number,r.linerooms_name,r.linerooms_fromclass,r.linerooms_createtime,s.school_cnname,
sum(o.yunshulog_durationSecond) as duration,
sum(o.yunshulog_studentsCost) as studentsCost,
sum(o.yunshulog_taCost) as taCost,
sum(o.yunshulog_totalprice) as totalprice
FROM tkl_linerooms as r
LEFT JOIN smc_school AS s ON r.school_branch = s.school_branch
LEFT JOIN tkl_linerooms_yunshulog AS o  ON o.linerooms_id = r.linerooms_id
where {$datawhere} GROUP BY r.linerooms_id order by r.linerooms_id DESC";
        $db_nums = $DataControl->selectOne("SELECT COUNT(DISTINCT r.linerooms_id) AS  nums
FROM tkl_linerooms as r
LEFT JOIN smc_school AS s ON r.school_branch = s.school_branch
LEFT JOIN tkl_linerooms_yunshulog AS o ON o.linerooms_id = r.linerooms_id where {$datawhere} limit 0,1");//相关条件下的总记录数COUNT(*)
        $allnum = $db_nums['nums'];

        $datalist = $DataControl->dbwherePage($sql,$allnum,'20',$pageurl.'&p=',$p,'10','2');

        $smarty->assign("pagelist",$datalist['pages']);//筛选信息

        $dataList = $datalist['cont'];
        if($dataList){
            foreach($dataList as &$dataOne){
                $dataOne['duration'] = round($dataOne['duration']/60,2);
            }
        }

        $smarty->assign("dataList",$dataList);
        $smarty->assign("datatype",$datatype);


        $priceOne = $this->DataControl->selectOne("SELECT
        (sum(l.yunshulog_durationSecond)/60) AS durationnums,
        (sum(l.yunshulog_durationSecond)/3600) AS hoursnums,
        (sum(l.yunshulog_tadurationSecond)/60) AS tutorialnums,
        (sum(l.yunshulog_tadurationSecond)/3600) AS tutorialhournums,
        ROUND(sum(l.yunshulog_totalprice),2) AS payprice
        FROM tkl_linerooms_yunshulog AS l WHERE l.company_id='{$this->istaffer['company_id']}' limit 0,1");
        $smarty->assign("priceOne",$priceOne);
    }


    function YsexpendView(){
        $request = Input('get.','','trim,addslashes');
        $dataVar = $this->DataControl->getOne("tkl_linerooms","linerooms_id='{$request['linerooms_id']}'");
        $this->smarty->assign("dataVar",$dataVar);


        if(!isset($request['p'])){
            $p = '1';
        }else{
            $p = $request['p'];
        }

        $datatype = array();
        $TalkcloudModel = new \Model\Tlk\TalkcloudModel($dataVar['company_id']);
        $putArray = array();
        $putArray['serial'] = $dataVar['linerooms_number'];
        $putArray['fromclass'] = $dataVar['linerooms_fromclass'];

        if(isset($request['starttime']) && $request['starttime'] !== ''){
            $putArray['starttime'] = strtotime($request['starttime']);
        }else{
            $putArray['starttime'] = $dataVar['linerooms_createtime'];
        }
        $datatype['starttimeName'] = date("Y-m-d H:i:s",$putArray['starttime']);
        $datatype['starttime'] = $putArray['starttime'];

        if(isset($request['endtime']) && $request['endtime'] !== ''){
            $putArray['endtime'] = strtotime($request['endtime'])+3600*23;
        }else{
            $putArray['endtime'] = time();
        }
        $datatype['endtimeName'] = date("Y-m-d H:i:s",$putArray['endtime']);
        $datatype['endtime'] = $putArray['endtime'];

        $modelArray = $TalkcloudModel->exroomonGetRoomInfo($putArray,$p);


        if($modelArray['result'] == '0'){
            if($dataVar['linerooms_fromclass'] == '0'){
                $recordList = $modelArray['recordlist'];
            }else{
                $recordList = $modelArray['data'];
            }
            if($recordList){
                foreach($recordList as $infoOne){
                    if($this->Intoyunshulog($infoOne,$dataVar['company_id'],$dataVar['linerooms_id'])){

                    }
                }
            }
            $this->smarty->assign("recordList",$recordList);
        }
        $this->smarty->assign("datatype",$datatype);


        /**分页处理**/
        $db_nums = $modelArray['pageinfo']['count'];//相关条件下的总记录数
        $page_size = 20;//每页显示的条数
        $sub_pages = 20;//每次显示的页
        if($p=='') {
            $pageCurrent=1;
        }else {
            $pageCurrent=$p;//得到当前是第几页数
        }
        $subPages = new \SetPages($page_size,$db_nums,$pageCurrent,$sub_pages,"/Financial/Ysexpend?linerooms_id={$request['linerooms_id']}&p=",'2','');
        $pagelist = $subPages->show_SubPages();
        $this->smarty->assign("pagelist",$pagelist);
    }

    function Intoyunshulog($infoOne,$company_id,$linerooms_id){
        if(!$this->DataControl->getFieldOne("tkl_linerooms_yunshulog","yunshulog_id"
            ,"yunshulog_houerid = '{$infoOne['id']}' and linerooms_id = '{$linerooms_id}'")){
            $loginlog = array();
            $loginlog['company_id'] = $company_id;
            $loginlog['linerooms_id'] = $linerooms_id;
            $loginlog['yunshulog_houerid'] = $infoOne['id'];
            $loginlog['yunshulog_begintime'] = $infoOne['begintime'];
            $loginlog['yunshulog_endtime'] = $infoOne['endtime'];
            $loginlog['yunshulog_duration'] = $infoOne['duration'];
            $loginlog['yunshulog_durationSecond'] = $infoOne['durationSecond'];
            $loginlog['yunshulog_usernum'] = $infoOne['usernum'];
            $loginlog['yunshulog_studentsCost'] = $infoOne['studentsCost'];
            $loginlog['yunshulog_unitprice'] = $infoOne['unitprice'];
            $loginlog['yunshulog_taduration'] = $infoOne['taduration'];
            $loginlog['yunshulog_tadurationSecond'] = $infoOne['tadurationSecond'];
            $loginlog['yunshulog_taprice'] = $infoOne['taprice'];
            $loginlog['yunshulog_taCost'] = $infoOne['taCost'];
            $loginlog['yunshulog_dis'] = $infoOne['dis'];
            $loginlog['yunshulog_totalprice'] = $infoOne['totalprice'];
            $loginlog['yunshulog_createtimetime'] = time();
            $this->DataControl->insertData("tkl_linerooms_yunshulog",$loginlog);
            return true;
        }else{
            return false;
        }
    }

    function ExportYunshuroomsAction(){
        $request = Input('post.','','trim,addslashes');
        $datawhere = "r.linerooms_fromclass = '1' and r.company_id = '{$this->istaffer['company_id']}'";

        if(isset($request['keyword']) && $request['keyword'] !==''){
            $datawhere .= " and (r.linerooms_name like '%{$request['keyword']}%' or r.linerooms_number like '%{$request['keyword']}%')";
        }

        if(isset($request['school_branch']) && $request['school_branch'] !==''){
            $datawhere .= " and r.school_branch = '{$request['school_branch']}'";
        }
        if(isset($request['linerooms_isdel']) && $request['linerooms_isdel'] !==''){
            $datawhere .= " and r.linerooms_isdel = '{$request['linerooms_isdel']}'";
        }

        $sql = "SELECT r.linerooms_id,r.linerooms_number,r.linerooms_name,r.linerooms_fromclass,r.linerooms_createtime,s.school_cnname,
sum(o.yunshulog_durationSecond) as duration,
sum(o.yunshulog_studentsCost) as studentsCost,
sum(o.yunshulog_taCost) as taCost,
sum(o.yunshulog_totalprice) as totalprice
FROM tkl_linerooms as r
LEFT JOIN smc_school AS s ON r.school_branch = s.school_branch
LEFT JOIN tkl_linerooms_yunshulog AS o  ON o.linerooms_id = r.linerooms_id
where {$datawhere} GROUP BY r.linerooms_id order by r.linerooms_id DESC";

        $dateexcelarray = $this->DataControl->selectClear($sql);

        if($dateexcelarray) {
            $outexceldate = array();
            foreach ($dateexcelarray as $dateexcelvar) {
                $datearray = array();
                $datearray['linerooms_id'] = $dateexcelvar['linerooms_id'];
                $datearray['school_cnname'] = $dateexcelvar['school_cnname'];
                $datearray['linerooms_name'] = $dateexcelvar['linerooms_name'];
                $datearray['linerooms_number'] = $dateexcelvar['linerooms_number'];
                if($dateexcelvar['linerooms_fromclass'] == '1'){
                    $datearray['linerooms_fromclass'] = '云枢';
                }else{
                    $datearray['linerooms_fromclass'] = '拓课云';
                }
                $datearray['duration'] = round($dateexcelvar['duration']/60,2);
                $datearray['hours'] = round($dateexcelvar['duration']/3600,2);
                $datearray['studentsCost'] = $dateexcelvar['studentsCost'];
                $datearray['taCost'] = $dateexcelvar['taCost'];
                $datearray['totalprice'] = $dateexcelvar['totalprice'];

                $outexceldate[] = $datearray;
            }
        }

        $excelheader = array("教室ID","校区名称","教室名称","教室编号","平台类型","上课总时长教师+学员(分钟)","上课总时长教师+学员(小时)","教师学员费用总计","巡课助教费用总计","结算费用");
        $excelfileds = array('linerooms_id',"school_cnname","linerooms_name",'linerooms_number','linerooms_fromclass',"duration","hours","studentsCost","taCost","totalprice");
        $datime = date("mdHi");
        query_to_excel($excelheader,$outexceldate,$excelfileds,"云枢校园教室流量使用统计{$datime}.xlsx");
    }

    //魔术方法
    function __destruct()
    {
        if ($this->c =='Wait') {
            $this->smarty->assign("u", $this->u);
            $this->smarty->assign("t", $this->t);
            $this->display($this->Viewhtm);
            exit;
        }
    }
}