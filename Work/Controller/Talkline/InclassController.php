<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/3/11
 * Time: 14:05
 */

namespace Work\Controller\Talkline;


class InclassController extends viewTpl{
    public $data;
    public $ishare;
    public $Viewhtm;
    public $u;
    public $t;
    public $c;

    function __construct() {
        parent::__construct ();
        if(!$this->check_login()){
            $this->LoginView();
        }
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
        $this->istaffer = $this->StafferLogin;
        $this->smarty->assign("istaffer", $this->StafferLogin);
        $this->Viewhtm = $this->router->getController()."/".$this->router->getUrl().".htm";


        $this->Jdbstuapp = new \Dbsqlplay("rm-bp100z52ieb406w73uo.mysql.rds.aliyuncs.com", "qiquonlineuser", "KidTimeqiqu2018", "stuappdata");
    }

    //主页
    function HomeView()
    {
        $request = Input('get.','','trim,addslashes');
        $DataControl = $this->Jdbstuapp;
        $smarty = $this->smarty;

        if(!isset($request['p'])){
            $p = '1';
        }else{
            $p= $request['p'];
        }

        $datawhere = "m.classcode_branch = c.classcode_branch AND m.classcode_branch <> '********' AND m.media_type = '1'";
        if($this->istaffer['account_class'] == '0'){
            $datawhere .= " AND (s.school_id in (SELECT p.school_id FROM gmc_staffer_postbe as p WHERE p.staffer_id = '{$this->istaffer['staffer_id']}')
                        or s.school_id in (SELECT o.school_id FROM gmc_staffer_postbe as sp,gmc_company_organizeschool as o
where o.organize_id = sp.organize_id and sp.staffer_id = '{$this->istaffer['staffer_id']}' and sp.school_id = '0' and sp.postbe_status = '1' order by sp.postbe_ismianjob DESC))";
        }
        $pageurl = "/{$this->u}/{$this->t}?";

        $datatype = array();
        if(isset($request['keyword']) && $request['keyword'] !==''){
            $datawhere .= " and (m.media_name = '{$request['keyword']}' or m.classcode_branch like '%{$request['keyword']}%')";
            $pageurl .="&keyword={$request['keyword']}";
            $datatype['keyword'] = $request['keyword'];
        }

        if(isset($request['classcode_branch']) && $request['classcode_branch'] !==''){
            $datawhere .= " and m.classcode_branch = '{$request['classcode_branch']}'";
            $pageurl .="&classcode_branch={$request['classcode_branch']}";
            $datatype['classcode_branch'] = $request['classcode_branch'];
        }

        $sql = "SELECT m.*,c.classcode_name FROM app_classcodehour_media as m,app_classcode as c where {$datawhere}  order by m.media_sort ASC";

        $db_nums = $DataControl->selectOne("SELECT COUNT(m.media_id) as countnums FROM app_classcodehour_media as m,app_classcode as c where {$datawhere}");//相关条件下的总记录数COUNT(*)
        $allnum = $db_nums['countnums'];

        $datalist = $DataControl->dbwherePage($sql,$allnum,'20',$pageurl.'&p=',$p,'10','2');

        $smarty->assign("pagelist",$datalist['pages']);//筛选信息

        $smarty->assign("dataList",$datalist['cont']);
        $smarty->assign("datatype",$datatype);



        $classcodeList = $DataControl->selectClear("SELECT c.classcode_name, c.classcode_branch FROM app_classcodehour_media AS m, app_classcode AS c
WHERE c.classcode_branch = m.classcode_branch AND m.classcode_branch <> '********' GROUP BY m.classcode_branch ORDER BY m.classcode_branch DESC");
        $this->smarty->assign("classcodeList",$classcodeList);
    }

    function mediaLookView(){
        $request = Input('get.','','trim,addslashes');
        $DataControl = $this->Jdbstuapp;
        $dataVar = $DataControl->getOne("app_classcodehour_media","media_id='{$request['media_id']}'");
        $this->smarty->assign("dataVar",$dataVar);


        $mediaList = $DataControl->getList("app_classcodehour_media","classcode_branch='{$dataVar['classcode_branch']}'"," ORDER BY media_sort ASC");
        $this->smarty->assign("mediaList",$mediaList);
    }

    function ForeignView(){
        $request = Input('get.','','trim,addslashes');
        if(isset($request['day']) && $request['day'] !=='0'){
            $thisweek = GetWeekAll($request['day']);
            $this->smarty->assign("day",$request['day']);
        }else{
            $thisweek = GetWeekAll(date("Y-m-d",time()));
            $this->smarty->assign("day",date("Y-m-d",time()));
        }
        $this->smarty->assign("thisweek",$thisweek);


        $stafferList = $this->DataControl->selectClear("SELECT s.staffer_id, l.school_cnname, s.staffer_cnname, s.staffer_enname, s.staffer_branch
FROM smc_class_hour_teaching AS t, smc_class_hour AS h, smc_class AS c, smc_staffer AS s, gmc_staffer_postbe AS p, smc_school AS l
WHERE t.class_id = c.class_id
AND t.staffer_id = s.staffer_id
AND t.hour_id = h.hour_id
AND s.staffer_id = p.staffer_id
AND p.school_id = l.school_id
AND p.school_id <> '0'
AND s.staffer_isparttime = '0'
AND c.company_id = '{$this->istaffer['company_id']}'
AND t.teachtype_code = '05EM'
AND h.hour_day <= '{$thisweek['nowweek_end']}'
AND h.hour_day >= '{$thisweek['nowweek_start']}'
GROUP BY s.staffer_id ORDER BY l.school_id DESC");
        if($stafferList){
            foreach($stafferList as &$stafferOne){
                $hourList = $this->DataControl->selectClear("SELECT
    s.school_cnname,
	c.class_cnname,
	c.class_enname,
	c.class_branch,
	h.hour_name,
	h.hour_day,
	h.hour_starttime,
	h.hour_endtime
FROM
	smc_class_hour_teaching AS t,
	smc_class_hour AS h,
	smc_class AS c,smc_school AS s
WHERE
	t.class_id = c.class_id
AND t.hour_id = h.hour_id AND c.school_id = s.school_id
AND h.hour_day <= '{$thisweek['nowweek_end']}'
AND h.hour_day >= '{$thisweek['nowweek_start']}'
AND t.staffer_id = '{$stafferOne['staffer_id']}'");

                $weekArray = array();
                for($i=0;$i<7;$i++){
                    $weekOne = array();
                    $weekday = date("Y-m-d",strtotime($thisweek['nowweek_start'])+3600*24*$i);
                    $jinDay = mktime(0,0,0,date("m",time()),date("d",time()),date("Y",time()));
                    for ($t=16;$t<41;$t++) {
                        $statTimes = date("H:i",$jinDay+$t*30*60);
                        $endTimes = date("H:i",$jinDay+($t+1)*30*60);
                        $timecouse = '';
                        foreach($hourList as $hourOne){
                            if($hourOne['hour_day'] == $weekday && is_time_cross(strtotime($statTimes),strtotime($endTimes),strtotime($hourOne['hour_starttime']),strtotime($hourOne['hour_endtime']))){
                                $timecouse = "校区：{$hourOne['school_cnname']}\n班级：{$hourOne['class_cnname']}{$hourOne['class_enname']} \n时间：({$hourOne['hour_starttime']}-{$hourOne['hour_endtime']})";
                            }
                        }
                        $weekOne[] = $timecouse;
                    }
                    $weekArray[] = $weekOne;
                }

                $stafferOne['weekjson'] = json_encode($weekArray,JSON_UNESCAPED_UNICODE);
            }
        }
        $this->smarty->assign("stafferList",$stafferList);
    }


    //魔术方法
    function __destruct()
    {
        if ($this->c =='Wait') {
            $this->smarty->assign("u", $this->u);
            $this->smarty->assign("t", $this->t);
            $this->display($this->Viewhtm);
            exit;
        }
    }
}