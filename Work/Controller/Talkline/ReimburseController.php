<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2021/10/3
 * Time: 23:34
 */

namespace Work\Controller\Talkline;


class ReimburseController extends viewTpl
{
    public $data;
    public $istaffer;
    public $Viewhtm;
    public $u;
    public $t;
    public $c;

    function __construct()
    {
        parent::__construct();
        if (!$this->check_login()) {
            $this->LoginView();
        }
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
        $this->istaffer = $this->StafferLogin;
        $this->smarty->assign("istaffer", $this->StafferLogin);
        $this->Viewhtm = $this->router->getController() . "/" . $this->router->getUrl() . ".htm";
    }

    //主页
    function HomeView()
    {
        $request = Input('get.');
        $DataControl = $this->DataControl;
        $smarty = $this->smarty;

        if (!isset($request['p'])) {
            $p = '1';
        } else {
            $p = $request['p'];
        }

        $datawhere = "s.company_id = '{$this->istaffer['company_id']}'";
        $pageurl = "/{$this->u}/{$this->t}?";

        $datatype = array();
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (s.reimburse_cnname like '%{$request['keyword']}%' or s.reimburse_payeename like '%{$request['keyword']}%' or s.school_name like '%{$request['keyword']}%' or s.reimburse_mobile like '%{$request['keyword']}%')";
            $pageurl .= "&keyword={$request['keyword']}";
            $datatype['keyword'] = $request['keyword'];
        }

        if (isset($request['reimburse_status']) && $request['reimburse_status'] !== '') {
            $datawhere .= " and s.reimburse_status = '{$request['reimburse_status']}'";
            $pageurl .= "&reimburse_status={$request['reimburse_status']}";
            $datatype['reimburse_status'] = $request['reimburse_status'];
        }


        $sql = "SELECT s.* FROM tkl_reimburse as s where {$datawhere} order by s.reimburse_id DESC";

        $db_nums = $DataControl->select("SELECT COUNT(s.reimburse_id) FROM tkl_reimburse as s where {$datawhere}");//相关条件下的总记录数COUNT(*)
        $allnum = $db_nums[0][0];

        $datalist = $DataControl->dbwherePage($sql, $allnum, '13', $pageurl . '&p=', $p, '12', '2');

        $smarty->assign("pagelist", $datalist['pages']);//筛选信息

        $smarty->assign("dataList", $datalist['cont']);
        $smarty->assign("datatype", $datatype);
    }


    //批量成员进程处理
    function BatchUserAction()
    {
        $request = Input('post.');
        $DataControl = $this->DataControl;

        $edwhere = " 1";
        if (isset($request['tab_list']) && count($request['tab_list']) > 0) {
            $chooseid = "";
            foreach ($request['tab_list'] as $chick_var) {
                $chooseid .= "'{$chick_var}',";
            }
            $idrange = substr($chooseid, 0, -1);
            $edwhere .= " and reimburse_id in ({$idrange})";
        } else {
            ajax_return(array('error' => 1, 'errortip' => "未选择任何成员!", "bakfuntion" => "errormotify"));
        }

        if ($request['ActionType'] == '1') {
            $copylist = $DataControl->getFieldquery("tkl_reimburse", "reimburse_id,group_id", $edwhere);
            if ($copylist) {
                foreach ($copylist as $copyvar) {
                    $insetcopy = array();
                    $insetcopy['group_id'] = $request['group_id'];
                    $this->DataControl->updateData("tkl_reimburse", "reimburse_id = '{$copyvar['reimburse_id']}'", $insetcopy);
                }
            }
            $this->Recordweblog("用户管理", "批量操作", "批量设置用户群组");
            ajax_return(array('error' => 0, 'errortip' => "批量操作成功!", "bakfuntion" => "refreshpage"));
        } else {
            ajax_return(array('error' => 1, 'errortip' => "批量操作失败!", "bakfuntion" => "errormotify"));
        }
    }

    function LimitsView()
    {
        $request = Input('get.');
        $DataControl = $this->DataControl;
        $smarty = $this->smarty;

        $smarty->assign("act", "Limits");
        $dataVar = $DataControl->getOne("tkl_reimburse", "reimburse_id='{$request['id']}'");
        $dataVar['Userlimit'] = json_decode($dataVar['user_limit'], true);
        $smarty->assign("dataVar", $dataVar);

        $this->Viewhtm = $this->router->getController() . "/" . "Limits.htm";
    }

    //活动编辑
    function EditView()
    {
        $request = Input('get.');
        $DataControl = $this->DataControl;
        $smarty = $this->smarty;

        $smarty->assign("act", "Edit");
        $dataVar = $DataControl->getOne("tkl_reimburse", "reimburse_id='{$request['reimburse_id']}'");
        $smarty->assign("dataVar", $dataVar);

        $this->Viewhtm = $this->router->getController() . "/" . "Edit.htm";
    }

    //活动编辑
    function EditAction()
    {
        $request = Input('post.');
        $DataControl = $this->DataControl;

        if ($this->DataControl->getOne('tkl_reimburse',  "company_id = '{$this->istaffer['company_id']}' AND reimburse_cnname='{$request['reimburse_cnname']}' AND reimburse_enname='{$request['reimburse_enname']}' and reimburse_id <> '{$request['reimburse_id']}'")) {
            $res = array('error' => '1', 'errortip' => '退款信息已存在!', "bakfuntion" => "errormotify");
            ajax_return($res);
        }

        $data = array();
        $data['school_name'] = $request['school_name'];
        $data['reimburse_cnname'] = $request['reimburse_cnname'];
        $data['reimburse_enname'] = $request['reimburse_enname'];
        $data['reimburse_isicept'] = $request['reimburse_isicept'];
        $data['reimburse_okprice'] = $request['reimburse_okprice'];
        $data['reimburse_bankname'] = $request['reimburse_bankname'];
        $data['reimburse_bankcode'] = $request['reimburse_bankcode'];
        $data['reimburse_banknode'] = $request['reimburse_banknode'];
        $data['reimburse_payeename'] = $request['reimburse_payeename'];
        $data['reimburse_mobile'] = $request['reimburse_mobile'];
        $data['reimburse_applytime'] = $request['reimburse_applytime'];
        $data['reimburse_status'] = $request['reimburse_status'];
        $data['reimburse_playtime'] = $request['reimburse_playtime'];
        $data['reimburse_playnote'] = $request['reimburse_playnote'];
        $data['reimburse_signaturepic'] = $request['reimburse_signaturepic'];
        $data['reimburse_cardpic'] = $request['reimburse_cardpic'];
        $data['reimburse_bandpic'] = $request['reimburse_bandpic'];
        if ($DataControl->updateData("tkl_reimburse", "reimburse_id = '{$request['reimburse_id']}'", $data)) {
            $this->Recordweblog("用户管理", "用户编辑", "修改退款信息数据，ID:{$request['reimburse_id']}");
            ajax_return(array('error' => 0, 'errortip' => "提交成功!", "bakfuntion" => "successFromTip"));
        } else {
            ajax_return(array('error' => 1, 'errortip' => "提交失败!", "bakfuntion" => "dangerFromTip"));
        }
    }

    //添加客户
    function AddView()
    {
        $smarty = $this->smarty;
        $smarty->assign("act", "Add");

        $this->Viewhtm = $this->router->getController() . "/" . "Edit.htm";
    }

    //新增客户
    function AddAction()
    {
        $request = Input('post.');
        $DataControl = $this->DataControl;

        if ($this->DataControl->getOne('tkl_reimburse', "company_id = '{$this->istaffer['company_id']}' AND reimburse_cnname='{$request['reimburse_cnname']}' AND reimburse_enname='{$request['reimburse_enname']}'")) {
            $res = array('error' => '1', 'errortip' => '退款信息已存在，请勿重复添加!', "bakfuntion" => "errormotify");
            ajax_return($res);
        }

        $data = array();
        $data['company_id'] = $this->istaffer['company_id'];
        $data['school_name'] = $request['school_name'];
        $data['reimburse_cnname'] = $request['reimburse_cnname'];
        $data['reimburse_enname'] = $request['reimburse_enname'];
        $data['reimburse_isicept'] = $request['reimburse_isicept'];
        $data['reimburse_okprice'] = $request['reimburse_okprice'];
        $data['reimburse_bankname'] = $request['reimburse_bankname'];
        $data['reimburse_bankcode'] = $request['reimburse_bankcode'];
        $data['reimburse_banknode'] = $request['reimburse_banknode'];
        $data['reimburse_payeename'] = $request['reimburse_payeename'];
        $data['reimburse_mobile'] = $request['reimburse_mobile'];
        $data['reimburse_applytime'] = $request['reimburse_applytime'];
        $data['reimburse_status'] = $request['reimburse_status'];
        $data['reimburse_playtime'] = $request['reimburse_playtime'];
        $data['reimburse_playnote'] = $request['reimburse_playnote'];
        $data['reimburse_signaturepic'] = $request['reimburse_signaturepic'];
        $data['reimburse_cardpic'] = $request['reimburse_cardpic'];
        $data['reimburse_bandpic'] = $request['reimburse_bandpic'];
        $data['reimburse_createtime'] = time();
        if ($DataControl->insertData("tkl_reimburse", $data)) {
            $this->Recordweblog("用户管理", "用户编辑", "新增退款信息数据");
            ajax_return(array('error' => 0, 'errortip' => "提交成功!", "bakfuntion" => "successFromTip", "bakurl" => "/{$this->u}"));
        } else {
            ajax_return(array('error' => 1, 'errortip' => "提交失败!", "bakfuntion" => "dangerFromTip"));
        }
    }

    //删除报名活动
    function DelAction()
    {
        $list_id = Input('get.reimburse_id', 0);
        if ($this->DataControl->delData('tkl_reimburse', "reimburse_id='{$list_id}'")) {
            $this->Recordweblog("用户管理", "用户编辑", "删除退款数据，ID:{$list_id}");
            ajax_return(array('error' => 0, 'errortip' => "提交成功!", "bakfuntion" => "okmotify"));
        } else {
            ajax_return(array('error' => 1, 'errortip' => "新增失败!", "bakfuntion" => "errormotify"));
        }
    }


    function ImportOrderView(){

    }

    //订单导入
    function ImportOrderToView(){
        $fileType = array('xls','csv','xlsx');
        $uploadfile = new \Webfile($_FILES['uploadFile'],$files_dir='../static/file', $size = 2097152*10,$fileType);
        $up_file = $uploadfile->upload();

        $PlayInfo = array();
        if($up_file && $uploadfile->updatastatus) {

            $ExeclName = array();
            $ExeclName['提交时间'] = "reimburse_applytime";
            $ExeclName['校区'] = "school_name";
            $ExeclName['学员中文名'] = "reimburse_cnname";
            $ExeclName['学员英文名'] = "reimburse_enname";
            $ExeclName['是否有ICEPT退费'] = "reimburse_isicept";
            $ExeclName['双方已确认ICEPT退费金额'] = "reimburse_iceptprice";
            $ExeclName['双方已确认退费金额'] = "reimburse_okprice";
            $ExeclName['退费收款银行'] = "reimburse_bankname";
            $ExeclName['收款人姓名'] = "reimburse_payeename";
            $ExeclName['退费收款银行所属支行'] = "reimburse_banknode";
            $ExeclName['其他银行'] = "reimburse_ohterbank";
            $ExeclName['退费收款银行所属省/市'] = "reimburse_bankcity";
            $ExeclName['退费收款银行卡号'] = "reimburse_bankcode";
            $ExeclName['上传签字单据'] = "reimburse_signaturepic";
            $ExeclName['上传身份证'] = "reimburse_cardpic";
            $ExeclName['上传银行卡正面'] = "reimburse_bandpic";
            $OrderList = execl_to_array($up_file, $ExeclName);
            unset($OrderList[0]);
            if ($OrderList) {
                foreach ($OrderList as $RefundrVar) {
                    if ($RefundrVar['学员中文名'] !== '' && $RefundrVar['收款人姓名'] !== '' && $RefundrVar['退费收款银行卡号'] !== '') {
                        $refundList[] = $RefundrVar;
                    } else {
                        $PlayInfoVar = array();
                        $PlayInfoVar['school_name'] = $RefundrVar['school_name'];
                        $PlayInfoVar['reimburse_cnname'] = $RefundrVar['reimburse_cnname'];
                        $PlayInfoVar['reimburse_payeename'] = $RefundrVar['reimburse_payeename'];
                        $PlayInfoVar['error'] = "1";
                        $PlayInfoVar['errortip'] = "导入信息不完整";
                        $PlayInfo[] = $PlayInfoVar;
                    }
                }
            }

            if($refundList) {
                foreach ($refundList as $refundVar) {
                    $PlayInfoVar = array();
                    $PlayInfoVar['school_name'] = $refundVar['school_name'];
                    $PlayInfoVar['reimburse_cnname'] = $refundVar['reimburse_cnname'];
                    $PlayInfoVar['reimburse_payeename'] = $refundVar['reimburse_payeename'];

                    if($this->DataControl->getFieldOne("tkl_reimburse","reimburse_id","school_name = '{$refundVar['school_name']}' AND reimburse_cnname = '{$refundVar['reimburse_cnname']}' AND reimburse_enname = '{$refundVar['reimburse_enname']}'")){
                        $PlayInfoVar = array();
                        $PlayInfoVar['school_name'] = $refundVar['school_name'];
                        $PlayInfoVar['reimburse_cnname'] = $refundVar['reimburse_cnname'];
                        $PlayInfoVar['reimburse_payeename'] = $refundVar['reimburse_payeename'];
                        $PlayInfoVar['error'] = "1";
                        $PlayInfoVar['errortip'] = "退费信息请勿重复上传！";
                        $PlayInfo[] = $PlayInfoVar;
                        continue;
                    }

                    $data = array();
                    $data['company_id'] = $this->istaffer['company_id'];
                    $data['school_name'] = $refundVar['school_name'];
                    $data['reimburse_cnname'] = $refundVar['reimburse_cnname'];
                    $data['reimburse_enname'] = $refundVar['reimburse_enname'];
                    $data['reimburse_isicept'] = $refundVar['reimburse_isicept']=='是'?'1':'0';
                    $data['reimburse_iceptprice'] = $refundVar['reimburse_iceptprice'];
                    $data['reimburse_okprice'] = $refundVar['reimburse_okprice'];
                    if($refundVar['reimburse_bankname'] == '其他银行'){
                        $data['reimburse_bankname'] = $refundVar['reimburse_ohterbank'];
                    }else{
                        $data['reimburse_bankname'] = $refundVar['reimburse_bankname'];
                    }
                    $data['reimburse_bankcode'] = $refundVar['reimburse_bankcode'];
                    $data['reimburse_bankcity'] = $refundVar['reimburse_bankcity'];
                    $data['reimburse_banknode'] = $refundVar['reimburse_banknode'];
                    $data['reimburse_payeename'] = $refundVar['reimburse_payeename'];
                    $data['reimburse_applytime'] = $refundVar['reimburse_applytime'];
                    $data['reimburse_signaturepic'] = $refundVar['reimburse_signaturepic'];
                    $data['reimburse_cardpic'] = $refundVar['reimburse_cardpic'];
                    $data['reimburse_bandpic'] = $refundVar['reimburse_bandpic'];
                    $data['reimburse_createtime'] = time();
                    if ($this->DataControl->insertData("tkl_reimburse",$data)) {
                        $PlayInfoVar['error'] = "0";
                        $PlayInfoVar['errortip'] = "导入成功";
                    }else{
                        $PlayInfoVar['error'] = "1";
                        $PlayInfoVar['errortip'] = "导入失败";
                    }
                    $PlayInfo[] = $PlayInfoVar;
                }
            }
        }else{
            $PlayInfoVar = array();
            $PlayInfoVar['school_name'] = "导入出错";
            $PlayInfoVar['reimburse_cnname'] = "导入出错";
            $PlayInfoVar['reimburse_payeename'] = "导入出错";
            $PlayInfoVar['error'] = "1";
            $PlayInfoVar['errortip'] = "文件不存在";
            $PlayInfo[] = $PlayInfoVar;
        }
        $this->smarty->assign("PlayInfo", $PlayInfo);
    }

    //魔术方法
    function __destruct()
    {
        if ($this->c == 'Wait') {
            $this->smarty->assign("u", $this->u);
            $this->smarty->assign("t", $this->t);
            $this->display($this->Viewhtm);
            exit;
        }
    }
}