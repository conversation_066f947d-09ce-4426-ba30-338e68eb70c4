<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2018/9/1
 * Time: 11:24
 */

namespace Work\Controller\Fmcapi;


class IndexController extends viewTpl{
    public $u;
    public $t;
    public $c;

    function __construct() {
        parent::__construct ();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
    }

    function HomeView(){
        $familyArray = $this->DataControl->selectClear("SELECT
	f.family_cnname,f.family_enname,f.family_mobile,f.family_id
FROM
	smc_student_family AS f
WHERE
	f.family_mobile NOT IN (
		SELECT
			p.parenter_mobile
		FROM
			smc_parenter AS p
	) AND LENGTH(f.family_mobile) = '11' AND f.family_iserror = '0' limit 0,1000");
        if($familyArray){
            foreach($familyArray as $familyOne){
                if(checkMobile($familyOne['family_mobile'])){
                    $parenter = array();
                    $parenter['parenter_cnname'] = addslashes($familyOne['family_cnname']);
                    $parenter['parenter_enname'] = addslashes($familyOne['family_enname']);
                    $parenter['parenter_mobile'] = $familyOne['family_mobile'];
                    $parenter['parenter_pass'] = md5(substr($familyOne['family_mobile'],-6));
                    $parenter['parenter_bakpass'] = substr($familyOne['family_mobile'],-6);
                    $parenter['parenter_addtime'] = time();
                    $parenter_id = $this->DataControl->insertData("smc_parenter",$parenter);

                    $parenter = array();
                    $parenter['parenter_id'] = $parenter_id;
                    $this->DataControl->updateData("smc_student_family","family_id = '{$familyOne['family_id']}'",$parenter);
                    echo "{$familyOne['family_mobile']}导入成功<br />";
                }else{
                    $parenter = array();
                    $parenter['family_iserror'] = '1';
                    $this->DataControl->updateData("smc_student_family","family_id = '{$familyOne['family_id']}'",$parenter);
                    echo "{$familyOne['family_mobile']}号码错误<br />";
                }
            }
        }else{
            exit("更新已完毕");
        }


        echo '<script language="javascript" type="text/javascript">
var i = 1;
var intervalid;
intervalid = setInterval("fun()", 100);
function fun() {
	if (i == 0) {
		window.location.href = "/Index/Home";
		clearInterval(intervalid);
	}
	document.getElementById("mes").innerHTML = i;
	i--;
}
</script>
<div id="error">
	<p>将在 <span id="mes">2</span> 秒钟后返回首页！</p>
</div> ';

    }

    function parenTerView(){
        $familyArray = $this->DataControl->selectClear("SELECT f.family_mobile,f.family_id FROM smc_student_family AS f
WHERE f.parenter_id  = '0' AND f.family_iserror = '0' limit 0,100");
        if($familyArray){
            foreach($familyArray as $familyOne){
                $parenterOne = $this->DataControl->getFieldOne("smc_parenter","parenter_id","parenter_mobile = '{$familyOne['family_mobile']}'");
                if($parenterOne){
                    $parenter = array();
                    $parenter['parenter_id'] = $parenterOne['parenter_id'];
                    $this->DataControl->updateData("smc_student_family","family_id = '{$familyOne['family_id']}'",$parenter);
                    echo "{$familyOne['family_mobile']}导入成功<br />";
                }else{
                    $parenter = array();
                    $parenter['family_iserror'] = '1';
                    $this->DataControl->updateData("smc_student_family","family_id = '{$familyOne['family_id']}'",$parenter);
                    echo "{$familyOne['family_mobile']}号码错误<br />";
                }
            }
        }else{
            exit("更新已完毕");
        }


        echo '<script language="javascript" type="text/javascript">
var i = 1;
var intervalid;
intervalid = setInterval("fun()", 100);
function fun() {
	if (i == 0) {
		window.location.href = "/Index/parenTer";
		clearInterval(intervalid);
	}
	document.getElementById("mes").innerHTML = i;
	i--;
}
</script>
<div id="error">
	<p>将在 <span id="mes">2</span> 秒钟后返回首页！</p>
</div> ';

    }

    function PtcAcView(){
        $familyArray = $this->DataControl->selectClear("SELECT
	f.family_cnname,f.family_enname,f.family_mobile,f.family_id
FROM
	smc_student_family AS f
WHERE
	f.family_mobile NOT IN (
		SELECT
			p.parenter_mobile
		FROM
			smc_parenter AS p
	) AND LENGTH(f.family_mobile) = '11' AND f.family_iserror = '0' limit 0,50");
        if($familyArray){
            foreach($familyArray as $familyOne){
                if(checkMobile($familyOne['family_mobile'])){
                    $parenter = array();
                    $parenter['parenter_cnname'] = $familyOne['family_cnname'];
                    $parenter['parenter_enname'] = $familyOne['family_enname'];
                    $parenter['parenter_mobile'] = $familyOne['family_mobile'];
                    $parenter['parenter_pass'] = md5(substr($familyOne['family_mobile'],-6));
                    $parenter['parenter_bakpass'] = substr($familyOne['family_mobile'],-6);
                    $parenter['parenter_addtime'] = time();
                    $parenter_id = $this->DataControl->insertData("smc_parenter",$parenter);

                    $parenter = array();
                    $parenter['parenter_id'] = $parenter_id;
                    $this->DataControl->updateData("smc_student_family","family_id = '{$familyOne['family_id']}'",$parenter);
                    echo "{$familyOne['family_mobile']}导入成功<br />";
                }else{
                    $parenter = array();
                    $parenter['family_iserror'] = '1';
                    $this->DataControl->updateData("smc_student_family","family_id = '{$familyOne['family_id']}'",$parenter);
                    echo "{$familyOne['family_mobile']}号码错误<br />";
                }
            }
        }


        echo '<script language="javascript" type="text/javascript">
var i = 1;
var intervalid;
intervalid = setInterval("fun()", 100);
function fun() {
	if (i == 0) {
		window.location.href = "/Index/PtcAc";
		clearInterval(intervalid);
	}
	document.getElementById("mes").innerHTML = i;
	i--;
}
</script>
<div id="error">
	<p>将在 <span id="mes">2</span> 秒钟后返回首页！</p>
</div> ';

    }
}