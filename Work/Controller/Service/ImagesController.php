<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn 
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2016/12/16
 * Time: 14:45
 */

namespace Work\Controller\Service;


class ImagesController  extends viewTpl{
    public $data;
    public $u;
    public $t;
    public $c;
    public $Viewhtm;
    //预加载处理类
    function __construct() {
        parent::__construct ();
        // if(!$this->check_login()){
        //     ajax_return(array('error' => 1,'errortip' => "您未登录禁止上传图片!","bakfuntion"=>"dangerFromTip"));
        // }
    }
    //主页
    function ListView()
    {
        ajax_return(array('error' => 1,'errortip' => "系统未收到您任何操作!","bakfuntion"=>"warningFromTip"));
    }
    //图片管理
    function ManageView(){
        $imgList = $this->DataControl->select("SELECT u.tb_imgurl FROM cms_uploadimg as u order by u.upload_id DESC limit 0 ,20");
        $imgarray = array();
        if($imgList){
            foreach($imgList as $imgVar){
                $imgarray[] = imggetpath($imgVar['tb_imgurl']);
            }
        }
        ajax_return($imgarray);}
    //主页
    function UploadingView()
    {
        $this->display($this->router->getController()."/".$this->router->getUrl().".htm");
        exit;
    }
    //上传图片路径
    function UpdataAction()
    {
        $upimg = '../static/pic';
        $upplay = new \Webimage ( "jpg gif png", $upimg , 'smallimg', 'markimg', 0.5 * 1024 );

        if (! empty ( $_FILES ["image"] )) {
            $md5file = md5_file($_FILES['image']['tmp_name']);
            $getTimg = $this->DataControl->getOne('cms_uploadimg',"tb_md5='{$md5file}'");
            if($getTimg){

                $res = array('error' => 0,'errortip' => "图片上传成功!",'originalimg'=>imggetpath($getTimg['tb_imgurl']),'thumbnailimg'=>imggetpath($getTimg['tb_thumburl']),"bakfuntion"=>"successFromTip");
                ajax_return($res);
            }else{
                $imglink = $upplay->upload_image ($_FILES['image']);
                if($imglink){
                    $thumburl = $upplay->smallImg($imglink,'300','300');//缩略图
                    $date =array();
                    $date['tb_imgurl'] = str_replace('../static/',"",$imglink);
                    $date['tb_thumburl'] = str_replace('../static/',"",$thumburl);
                    $date['tb_md5'] = $md5file;

                    $this->DataControl->insertData('cms_uploadimg',$date);

                    $res = array('error' => 0,'errortip' => "图片上传成功!",'originalimg'=>imggetpath($date['tb_imgurl']),'thumbnailimg'=>imggetpath($date['tb_thumburl']),"bakfuntion"=>"successFromTip");
                    ajax_return($res);
                }else{
                    $res = array('error' => 1,'errortip' => $upplay->errormsg,"bakfuntion"=>"dangerFromTip");
                    ajax_return($res);
                }
            }
        }else{
            $res = array('error' => 1,'errortip' => "您未选择任何图片上传","bakfuntion"=>"dangerFromTip");
            ajax_return($res);
        }
    }
    //上传图片路径
    function ImguploadView()
    {
        $upimg = '../static/pic';
        $upplay = new \Webimage ( "jpg gif png", $upimg , 'smallimg', 'markimg', 0.5 * 1024 );

        if (! empty ( $_FILES ["file"] )) {
            $md5file = md5_file($_FILES['file']['tmp_name']);
            $getTimg = $this->DataControl->getOne('cms_uploadimg',"tb_md5='{$md5file}'");
            if($getTimg){
                $res = array('error' => 0,'errortip' => "图片上传成功!",'link'=>imggetpath($getTimg['tb_imgurl']),'thumbnailimg'=>imggetpath($getTimg['tb_thumburl']),"bakfuntion"=>"successFromTip");
                ajax_return($res);
            }else{
                $imglink = $upplay->upload_image ($_FILES['file']);
                if($imglink){
                    $thumburl = $upplay->smallImg($imglink,'300','300');//缩略图
                    $date =array();
                    $date['tb_imgurl'] = str_replace('../static/',"",$imglink);
                    $date['tb_thumburl'] = str_replace('../static/',"",$thumburl);
                    $date['tb_md5'] = $md5file;

                    $this->DataControl->insertData('cms_uploadimg',$date);

                    $res = array('error' => 0,'errortip' => "图片上传成功!",'link'=>imggetpath($date['tb_imgurl']),'thumbnailimg'=>imggetpath($date['tb_thumburl']),"bakfuntion"=>"successFromTip");
                    ajax_return($res);
                }else{
                    $res = array('error' => 1,'errortip' => $upplay->errormsg,"bakfuntion"=>"dangerFromTip");
                    ajax_return($res);
                }
            }
        }else{
            $res = array('error' => 1,'errortip' => "您未选择任何图片上传","bakfuntion"=>"dangerFromTip");
            ajax_return($res);
        }
    }

    //上传图片路径
    function PictureUploadView(){
        $request = Input('get.','','trim,addslashes');
        $upimg = '../static/pic';
        $upplay = new \Webimage ( "jpg gif png", $upimg , 'smallimg', 'markimg', 0.5 * 1024 );

        if (! empty ( $_FILES ["file"] )) {
            $md5file = md5_file($_FILES['file']['tmp_name']);
            $getTimg = $this->DataControl->getOne('cms_uploadimg',"tb_md5='{$md5file}'");
            if($getTimg){
                $res = array('error' => 0,'errortip' => "文件上传成功!",'link'=>imggetpath($getTimg['tb_imgurl']),'filename'=>$getTimg['tb_name'],'fileid'=>$getTimg['upload_id']);
                ajax_return($res);
            }else{
                $imglink = $upplay->upload_image ($_FILES['file']);
                if($imglink){
                    $thumburl = $upplay->smallImg($imglink,'300','300');//缩略图
                    $date =array();
                    $date['tb_name'] = $request['filename'];
                    $date['tb_imgurl'] = str_replace('../static/',"",$imglink);
                    $date['tb_thumburl'] = str_replace('../static/',"",$thumburl);
                    $date['tb_md5'] = $md5file;

                    $this->DataControl->insertData('cms_uploadimg',$date);

                    $res = array('error' => 0,'errortip' => "文件上传成功!",'link'=>imggetpath($date['tb_imgurl']),'filename'=>$date['tb_name'],'fileid'=>$date['upload_id']);
                    ajax_return($res);
                }else{
                    $res = array('error' => 1,'errortip' => $upplay->errormsg);
                    ajax_return($res);
                }
            }
        }else{
            $res = array('error' => 1,'errortip' => "您未选择任何图片上传");
            ajax_return($res);
        }
    }
    //上传文件路径
    function FilesUploadView(){
        $request = Input('get.','','trim,addslashes');
        $fileType = array('pdf','jpg','gif','png','doc','xls','zip','docx','xlsx','ppt','pptx','mp3','mp4');
        $md5file = md5_file($_FILES['file']['tmp_name']);
        $uploadfile = new \Webfile($_FILES['file'],$files_dir='../static/file', $size = 2097152,$fileType);
        $fileurl = str_replace('../static/',"",$uploadfile->upload());

        $getTfile = $this->DataControl->getOne('cms_uploadfile',"tb_md5='{$md5file}'");
        if($getTfile){
            $res = array('error' => 0,'errortip' => "文件上传成功!",'link'=>imggetpath($getTfile['tb_fileurl']),'filename'=>$getTfile['tb_name'],'fileid'=>$getTfile['upfile_id']);
            ajax_return($res);
        }else {
            $date = array();
            $date['tb_name'] = $request['filename'];
            $date['tb_fileurl'] = $fileurl;
            $date['tb_md5'] = $md5file;
            $this->DataControl->insertData('cms_uploadfile', $date);
            $getTfile = $this->DataControl->getOne('cms_uploadfile',"tb_md5='{$md5file}'");
            $res = array('error' => 0,'errortip' => "文件上传成功!",'link'=>imggetpath($getTfile['tb_fileurl']),'filename'=>$getTfile['tb_name'],'fileid'=>$getTfile['upfile_id']);
            ajax_return($res);
        }
    }

    //会员中心头像上传
    function upCanvasimgAction(){
        $request = Input('post.','','trim,addslashes');
        if (! empty ($request['imagedata'])) {
            $upimg = '../static/pic';
            $upplay = new \Webimage ( "jpg gif png", $upimg , 'smallimg', 'markimg', 2 * 1024 );
            $imagedata = $_POST['imagedata'];
            $imagedata = str_replace('data:image/jpg;base64,', '', $imagedata);
            $imagedata = str_replace(' ', '+', $imagedata);
            $iptdata = base64_decode($imagedata);

            $pic_name = $this->randomname() . ".png" ;//图片名称
            $pic_url = $upimg ."/" . $pic_name;//上传后图片路径+名称
            if (file_put_contents($pic_url, $iptdata)) {
                $date = array();
                $pic_url = $pic_url;
                $thumburl = $upplay->smallImg($pic_url,'300','300');

                $date['tb_imgurl'] = str_replace('../static/',"",$pic_url);
                $date['tb_thumburl'] = str_replace('../static/',"",$thumburl);

                $res = array('error' => 0,'errortip' => "图片上传成功!",'originalimg'=>imggetpath($date['tb_imgurl']),'thumbnailimg'=>imggetpath($date['tb_thumburl']),);
                ajax_return($res);
            }else{
                $res = array('error' => 1,'errortip' => "上传图片失败","bakfuntion"=>"dangerFromTip");
                ajax_return($res);
            }
        }else{
            $res = array('error' => 1,'errortip' => "未上传任何文件","bakfuntion"=>"dangerFromTip");
            ajax_return($res);
        }
    }

    function randomname(){
        $str = 'x';
        for($i = 0; $i < 9; $i++)
        {
            $str .= mt_rand(0, 9);
        }

        return time() . $str;
    }
    
}
