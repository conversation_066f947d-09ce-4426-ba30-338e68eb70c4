<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2018/8/25
 * Time: 15:15
 */

namespace Work\Controller\Service;


class UserlogController extends viewTpl{
    public $data;
    public $ishare;
    public $Viewhtm;
    public $u;
    public $t;
    public $c;

    function __construct() {
        parent::__construct ();
        if(!$this->check_login()){
            $this->LoginView();
        }
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
        $this->istaffer = $this->StafferLogin;
        $this->smarty->assign("istaffer", $this->StafferLogin);
        $this->Viewhtm = $this->router->getController()."/".$this->router->getUrl().".htm";
    }

    //主页
    function HomeView()
    {
        $request = Input('get.','','trim,addslashes');
        $DataControl = $this->DataControl;
        $smarty = $this->smarty;

        if(!isset($request['p'])){
            $p = '1';
        }else{
            $p= $request['p'];
        }

        $datawhere = "l.staffer_id = s.staffer_id and l.company_id = '{$this->istaffer['company_id']}'";
        $pageurl = "/{$this->u}/{$this->t}?";

        if ($request['site_id']) {
            $pageurl .= "&site_id={$request['site_id']}";
        }

        $datatype = array();
        if(isset($request['keyword']) && $request['keyword'] !==''){
            $datawhere .= " and (l.userlog_content like '%{$request['keyword']}%' or s.staffer_cnname like '%{$request['keyword']}%')";
            $pageurl .="&keyword={$request['keyword']}";
            $datatype['keyword'] = $request['keyword'];
        }

        if(isset($request['starttime']) && $request['starttime'] !== ''){
            $pageurl .="&starttime={$request['starttime']}";
            $datatype['starttime'] = $request['starttime'];
        }
        if(isset($request['endtime']) && $request['endtime'] !== ''){
            $pageurl .="&endtime={$request['endtime']}";
            $datatype['endtime'] = $request['endtime'];
        }

        $sql = "SELECT l.*,s.staffer_cnname FROM tkl_userlog as l,smc_staffer as s where {$datawhere} order by l.userlog_id DESC";

        $db_nums = $DataControl->select("SELECT COUNT(l.userlog_id) FROM tkl_userlog as l,smc_staffer as s where {$datawhere}");//相关条件下的总记录数COUNT(*)
        $allnum = $db_nums[0][0];

        $datalist = $DataControl->dbwherePage($sql,$allnum,'15',$pageurl.'&p=',$p,'10','2');

        $smarty->assign("pagelist",$datalist['pages']);//筛选信息

        $smarty->assign("dataList",$datalist['cont']);
        $smarty->assign("datatype",$datatype);
    }

    function LookView(){
        $request = Input('get.','','trim,addslashes');
        $DataControl = $this->DataControl;
        $smarty = $this->smarty;

        $smarty->assign("act","Edit");
        $dataVar = $DataControl->getOne("tkl_userlog","userlog_id='{$request['userlog_id']}'");
        $smarty->assign("dataVar",$dataVar);

        $this->Viewhtm = $this->router->getController()."/"."Edit.htm";
    }
    //魔术方法
    function __destruct()
    {
        if ($this->c =='Wait') {
            $this->smarty->assign("u", $this->u);
            $this->smarty->assign("t", $this->t);
            $this->display($this->Viewhtm);
            exit;
        }
    }
}