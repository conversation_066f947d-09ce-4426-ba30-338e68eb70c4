<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn 
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/2/13
 * Time: 17:07
 */
namespace Work\Controller\Service;

class HeatstartController extends viewTpl{
    public $data;
    public $isaleman;

    function __construct() {
        parent::__construct ();
    }

    //体检渠道用户
    function LoginAction(){
        $request = Input('post.','','trim,addslashes');

        $L_user=$this->DataControl->getFieldOne("imc_saleman","saleman_id,saleman_pass,saleman_cnname","saleman_mobile='{$request['L_name']}'");

        $password = md5(trim($request['L_pass']));
        //判断密码
        if ($password == $L_user['saleman_pass']) {
            $saleman = array();
            $saleman['saleman_id'] = $L_user['saleman_id'];
            $saleman['saleman_cnname'] = $L_user['saleman_cnname'];
            $this->intSession->setCookiearray("saleman", $saleman, '1');

            $this->DataControl->updateData("imc_saleman", "saleman_id = '{$L_user['saleman_id']}'", array("saleman_lasttime" => time(), "saleman_lastip" => real_ip()));

            ajax_return(array('error' => 0,'errortip' => "登录成功!","bakfuntion"=>"okmotify","bakurl"=>"/"));
        } else {
            ajax_return(array('error' => 1,'errortip' => "账户密码错误，请重新登录!","bakfuntion"=>"errormotify"));
        }
    }

    //初始化退出函数
    function outloginAction(){
        $this->intSession->setCookiearray("saleman",array(),'1');
        jslocal_spl("/");
    }



    //初始化退出函数
    function outroomAction(){
        $this->intSession->setCookiearray("parenter",array(),'1');
        jslocal_spl("/roomBook/");
    }


}