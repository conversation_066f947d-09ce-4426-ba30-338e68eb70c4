<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn 
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong <PERSON>
 * Date: 2018/3/10
 * Time: 16:49
 */

namespace Work\Controller\Service;


class CompanyController extends viewTpl{
    public $data;
    public $ishare;
    public $Viewhtm;
    public $isaleman;
    public $u;
    public $t;
    public $c;

    function __construct() {
        parent::__construct ();
        if(!$this->check_login()){
            $this->LoginView();
        }
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
        $this->isaleman = $this->SalemanLogin;
        $this->smarty->assign("isaleman", $this->SalemanLogin);
        $this->Viewhtm = $this->router->getController()."/".$this->router->getUrl().".htm";
    }

    //主页
    function HomeView()
    {
        $request = Input('get.','','trim,addslashes');
        $DataControl = $this->DataControl;
        $smarty = $this->smarty;

        if(!isset($request['p'])){
            $p = '1';
        }else{
            $p= $request['p'];
        }

        $datawhere = " 1 ";
        $pageurl = "/{$this->u}/{$this->t}?";

        $datatype = array();
        if(isset($request['keyword']) && $request['keyword'] !==''){
            $datawhere .= " and (B.company_shortname = '{$request['keyword']}' or B.company_code like '%{$request['keyword']}%')";
            $pageurl .="&keyword={$request['keyword']}";
            $datatype['keyword'] = $request['keyword'];
        }
        if(isset($request['company_ismajor']) && $request['company_ismajor'] !==''){
            $datawhere .= " and B.company_ismajor = '{$request['company_ismajor']}'";
            $pageurl .="&company_ismajor={$request['company_ismajor']}";
            $datatype['company_ismajor'] = $request['company_ismajor'];
        }

        $sql = "select B.company_id,B.company_code,B.company_ismajor,B.company_language,B.company_shortname,B.company_cnname,B.company_name,B.company_mobile,B.company_address,A.contract_starttime,A.contract_endtime
                from imc_sales_contract as A,gmc_company as B where {$datawhere} and A.company_id=B.company_id and A.saleman_id='{$this->isaleman['saleman_id']}'";

        $db_nums = $DataControl->selectOne("select count(B.company_id) as countnums from imc_sales_contract as A,gmc_company as B where {$datawhere} and A.company_id=B.company_id and A.saleman_id='{$this->isaleman['saleman_id']}'");//相关条件下的总记录数COUNT(*)
        $allnum = $db_nums['countnums'];

        $datalist = $DataControl->dbwherePage($sql,$allnum,'10',$pageurl.'&p=',$p,'10','2');
        $smarty->assign("pagelist",$datalist['pages']);//筛选信息

        $smarty->assign("dataList",$datalist['cont']);
        $smarty->assign("datatype",$datatype);
    }

    function ImportView(){
        $request = Input('get.','','trim,addslashes');
        $smarty = $this->smarty;
        $smarty->assign("company_id",$request['company_id']);
    }

    function ImportCourseGuideView(){
        $request = Input('get.','','trim,addslashes');
        $smarty = $this->smarty;
        $smarty->assign("company_id",$request['company_id']);
    }

    function ImportCourseView(){
        $request = Input('post.','','trim,addslashes');
        if(!isset($request['company_id']) || $request['company_id']==''){
            ajax_return(array('error' => 1, 'errortip' => "集团ID必须传", "bakfuntion" => "errormotify"));
        }

        if($_FILES['uploadFile']['name'] == '' || $_FILES['uploadFile']['error'] == '1'){
            $PlayInfoVar = array();
            $PlayInfoVar['coursetype_cnname'] = '';
            $PlayInfoVar['coursetype_branch'] = '';
            $PlayInfoVar['coursecat_cnname'] = '';
            $PlayInfoVar['coursecat_branch'] = '';
            $PlayInfoVar['course_cnname'] = '';
            $PlayInfoVar['course_branch'] = '';
            $PlayInfoVar['course_inclasstype'] = '';
            $PlayInfoVar['course_checkingintype'] = '';
            $PlayInfoVar['course_classnum'] = '';
            $PlayInfoVar['course_freenums'] = '';
            $PlayInfoVar['course_islimittimes'] = '';
            $PlayInfoVar['course_issesson'] = '';
            $PlayInfoVar['course_isrenew'] = '';
            $PlayInfoVar['error'] = "1";
            $PlayInfoVar['errortip'] = "文件不存在";
            $PlayInfo[] = $PlayInfoVar;
            $this->smarty->assign("PlayInfo", $PlayInfo);
            exit;
        }

        $fileType = array('xls','csv','xlsx');
        $uploadfile = new \Webfile($_FILES['uploadFile'],$files_dir='../static/file', $size = 2097152*10,$fileType);
        $up_file = $uploadfile->upload();

        $PlayInfo = array();
        if($up_file && $uploadfile->updatastatus) {
            $ExeclName = array();
            $ExeclName['班组名称'] = "coursetype_cnname";
            $ExeclName['班组编号'] = "coursetype_branch";
            $ExeclName['班种名称'] = "coursecat_cnname";
            $ExeclName['班种编号'] = "coursecat_branch";
            $ExeclName['课程别名称'] = "course_cnname";
            $ExeclName['课程别编号'] = "course_branch";
            $ExeclName['课程类型'] = "course_inclasstype";//0课次类课程1期度类课程2预约类课程'
            $ExeclName['考勤方式'] = "course_checkingintype";//0-课次考勤(缺勤计费),1-连续缺勤,2-自然周考勤,3-月度考勤,4-累计缺勤,5-课次考勤(缺勤免费)
            $ExeclName['实际课次'] = "course_classnum";
            $ExeclName['免手续费课次数'] = "course_freenums";
            $ExeclName['是否限制入班课次相等'] = "course_islimittimes";
            $ExeclName['是否季度课程'] = "course_issesson";
            $ExeclName['是否留续班'] = "course_isrenew";


            $WorkerList = execl_to_array($up_file, $ExeclName);
            unset($WorkerList[0]);

            $workersList=array();

            if ($WorkerList) {
                foreach ($WorkerList as $workersVar) {
                    if ($workersVar['coursetype_cnname'] !== '' && $workersVar['coursetype_branch'] !== '' && $workersVar['coursecat_cnname'] !== '' && $workersVar['coursecat_branch'] !== '' && $workersVar['course_cnname'] !== '' && $workersVar['course_branch'] !== '' && $workersVar['course_inclasstype'] !== '' && $workersVar['course_checkingintype'] !== '' && $workersVar['course_classnum'] !== '' && $workersVar['course_freenums'] !== '') {
                        $workersList[] = $workersVar;
                    } else {
                        $PlayInfoVar = array();
                        $PlayInfoVar['coursetype_cnname'] = $workersVar['coursetype_cnname'];
                        $PlayInfoVar['coursetype_branch'] = $workersVar['coursetype_branch'];
                        $PlayInfoVar['coursecat_cnname'] = $workersVar['coursecat_cnname'];
                        $PlayInfoVar['coursecat_branch'] = $workersVar['coursecat_branch'];
                        $PlayInfoVar['course_cnname'] = $workersVar['course_cnname'];
                        $PlayInfoVar['course_branch'] = $workersVar['course_branch'];
                        $PlayInfoVar['course_inclasstype'] = $workersVar['course_inclasstype'];
                        $PlayInfoVar['course_checkingintype'] = $workersVar['course_checkingintype'];
                        $PlayInfoVar['course_classnum'] = $workersVar['course_classnum'];
                        $PlayInfoVar['course_freenums'] = $workersVar['course_freenums'];
                        $PlayInfoVar['course_islimittimes'] = $workersVar['course_islimittimes'];
                        $PlayInfoVar['course_issesson'] = $workersVar['course_issesson'];
                        $PlayInfoVar['course_isrenew'] = $workersVar['course_isrenew'];
                        $PlayInfoVar['error'] = "1";
                        $PlayInfoVar['errortip'] = "导入信息不完整";
                        $PlayInfo[] = $PlayInfoVar;
                    }
                }
            }
            if (count($workersList) > 100000) {
                ajax_return(array('error' => 1, 'errortip' => "导入数量不能大于100000!", "bakfuntion" => "errormotify"));
            }
            if($workersList) {
                foreach ($workersList as $workersVar) {
                    $PlayInfoVar=array();
                    $PlayInfoVar['coursetype_cnname'] = $workersVar['coursetype_cnname'];
                    $PlayInfoVar['coursetype_branch'] = $workersVar['coursetype_branch'];
                    $PlayInfoVar['coursecat_cnname'] = $workersVar['coursecat_cnname'];
                    $PlayInfoVar['coursecat_branch'] = $workersVar['coursecat_branch'];
                    $PlayInfoVar['course_cnname'] = $workersVar['course_cnname'];
                    $PlayInfoVar['course_branch'] = $workersVar['course_branch'];
                    $PlayInfoVar['course_inclasstype'] = $workersVar['course_inclasstype'];
                    $PlayInfoVar['course_checkingintype'] = $workersVar['course_checkingintype'];
                    $PlayInfoVar['course_classnum'] = $workersVar['course_classnum'];
                    $PlayInfoVar['course_freenums'] = $workersVar['course_freenums'];
                    $PlayInfoVar['course_islimittimes'] = $workersVar['course_islimittimes'];
                    $PlayInfoVar['course_issesson'] = $workersVar['course_issesson'];
                    $PlayInfoVar['course_isrenew'] = $workersVar['course_isrenew'];

                    if($this->DataControl->getFieldOne("smc_course","course_id","company_id='{$request['company_id']}' and course_branch='{$workersVar['course_branch']}'")){

                        $PlayInfoVar['error'] = "1";
                        $PlayInfoVar['errortip'] = "课程已存在,不可重复导入";
                        $PlayInfo[] = $PlayInfoVar;
                        continue;
                    }else{
                        $coursetypeOne=$this->DataControl->getFieldOne("smc_code_coursetype","coursetype_id","company_id='{$request['company_id']}' and coursetype_branch='{$workersVar['coursetype_branch']}'");

                        if(!$coursetypeOne){
                            $data=array();
                            $data['company_id']=$request['company_id'];
                            $data['coursetype_isimport']=1;
                            $data['coursetype_cnname']=trim($workersVar['coursetype_cnname']);
                            $data['coursetype_branch']=trim($workersVar['coursetype_branch']);
                            if(!$this->DataControl->insertData("smc_code_coursetype",$data)){
                                $PlayInfoVar['error'] = "1";
                                $PlayInfoVar['errortip'] = "班组导入失败";
                                $PlayInfo[] = $PlayInfoVar;
                                continue;
                            }

                            $coursetypeOne=$this->DataControl->getFieldOne("smc_code_coursetype","coursetype_id","company_id='{$request['company_id']}' and coursetype_branch='{$workersVar['coursetype_branch']}'");
                        }

                        $coursetype_id=$coursetypeOne['coursetype_id'];

                        if(!$coursetype_id){
                            $PlayInfoVar['error'] = "1";
                            $PlayInfoVar['errortip'] = "班组导入失败";
                            $PlayInfo[] = $PlayInfoVar;
                            continue;
                        }
                        $coursecatOne=$this->DataControl->getFieldOne("smc_code_coursecat","coursecat_id","company_id='{$request['company_id']}' and coursecat_branch='{$workersVar['coursecat_branch']}'");

                        if(!$coursecatOne){
                            $data=array();
                            $data['company_id']=$request['company_id'];
                            $data['coursecat_isimport']=1;
                            $data['coursetype_id']=$coursetype_id;
                            $data['coursecat_cnname']=trim($workersVar['coursecat_cnname']);
                            $data['coursecat_branch']=trim($workersVar['coursecat_branch']);
                            if(!$this->DataControl->insertData("smc_code_coursecat",$data)){
                                $PlayInfoVar['error'] = "1";
                                $PlayInfoVar['errortip'] = "班种导入失败";
                                $PlayInfo[] = $PlayInfoVar;
                                continue;
                            }

                            $coursecatOne=$this->DataControl->getFieldOne("smc_code_coursecat","coursecat_id","company_id='{$request['company_id']}' and coursecat_branch='{$workersVar['coursecat_branch']}'");
                        }

                        $coursecat_id=$coursecatOne['coursecat_id'];

                        if(!$coursecat_id){
                            $PlayInfoVar['error'] = "1";
                            $PlayInfoVar['errortip'] = "班种导入失败";
                            $PlayInfo[] = $PlayInfoVar;
                            continue;
                        }


                        $data=array();
                        $data['company_id']=$request['company_id'];
                        $data['coursetype_id']=$coursetype_id;
                        $data['coursecat_id']=$coursecat_id;
                        $data['course_isimport']=1;
                        $data['course_cnname']=trim($workersVar['course_cnname']);
                        $data['course_branch']=trim($workersVar['course_branch']);
                        $data['course_freenums']=$workersVar['course_freenums'];
                        $data['course_inclasstype']=substr($workersVar['course_inclasstype'],0,1);
                        $data['course_classnum']=$workersVar['course_classnum'];
                        $data['course_checkingintype']=substr($workersVar['course_checkingintype'],0,1);

                        $data['course_schedule']=0;//是否允许重新排课 0-允许 1-不允许

                        $data['course_islimittimes'] = trim($workersVar['course_islimittimes']);
                        $data['course_issesson'] = trim($workersVar['course_issesson']);
                        $data['course_isrenew'] = trim($workersVar['course_isrenew']);
                        $data['course_islimittime'] = 1;
                        $data['course_limittime'] = 45;

                        if ($this->DataControl->insertData("smc_course", $data)) {
                            $PlayInfoVar['error'] = "0";
                            $PlayInfoVar['errortip'] = "导入成功";
                        } else {
                            $PlayInfoVar['error'] = "1";
                            $PlayInfoVar['errortip'] = "导入失败";
                        }
                    }

                    $PlayInfo[] = $PlayInfoVar;
                }
            }

        }else{
            $PlayInfoVar = array();
            $PlayInfoVar['coursetype_cnname'] = '';
            $PlayInfoVar['coursetype_branch'] = '';
            $PlayInfoVar['coursecat_cnname'] = '';
            $PlayInfoVar['coursecat_branch'] = '';
            $PlayInfoVar['course_cnname'] = '';
            $PlayInfoVar['course_branch'] = '';
            $PlayInfoVar['course_inclasstype'] = '';
            $PlayInfoVar['course_checkingintype'] = '';
            $PlayInfoVar['course_classnum'] = '';
            $PlayInfoVar['course_freenums'] = '';
            $PlayInfoVar['course_islimittimes'] = '';
            $PlayInfoVar['course_issesson'] = '';
            $PlayInfoVar['course_isrenew'] = '';
            $PlayInfoVar['error'] = "1";
            $PlayInfoVar['errortip'] = "文件不存在";
            $PlayInfo[] = $PlayInfoVar;
        }

        $this->Recordweblog("集团管理",$this->c,"集团:{$request['company_id']},导入课程资料");

        $this->smarty->assign("PlayInfo", $PlayInfo);

    }

    function ImportStafferGuideView(){
        $request = Input('get.','','trim,addslashes');
        $smarty = $this->smarty;
        $smarty->assign("company_id",$request['company_id']);

    }

    function ImportStafferView(){
        $request = Input('post.','','trim,addslashes');
        if(!isset($request['company_id']) || $request['company_id']==''){
            ajax_return(array('error' => 1, 'errortip' => "集团ID必须传", "bakfuntion" => "errormotify"));
        }

        if($_FILES['uploadFile']['name'] == '' || $_FILES['uploadFile']['error'] == '1'){
            $PlayInfoVar = array();
            $PlayInfoVar['staffer_cnname'] = '';
            $PlayInfoVar['staffer_enname'] = '';
            $PlayInfoVar['staffer_branch'] = '';
            $PlayInfoVar['staffer_sex'] = '';
            $PlayInfoVar['staffer_mobile'] = '';
            $PlayInfoVar['info_birthday'] = '';
            $PlayInfoVar['staffer_jointime'] = '';
            $PlayInfoVar['staffer_email'] = '';
            $PlayInfoVar['error'] = "1";
            $PlayInfoVar['errortip'] = "文件不存在";
            $PlayInfo[] = $PlayInfoVar;
            $this->smarty->assign("PlayInfo", $PlayInfo);
            exit;
        }

        $fileType = array('xls','csv','xlsx');
        $uploadfile = new \Webfile($_FILES['uploadFile'],$files_dir='../static/file', $size = 2097152*10,$fileType);
        $up_file = $uploadfile->upload();

        $PlayInfo = array();
        if($up_file && $uploadfile->updatastatus) {
            $ExeclName = array();
            $ExeclName['职工中文名'] = "staffer_cnname";
            $ExeclName['职工英文名'] = "staffer_enname";
            $ExeclName['职工编号'] = "staffer_employeepid";
            $ExeclName['性别'] = "staffer_sex";
            $ExeclName['手机号'] = "staffer_mobile";
            $ExeclName['出生日期'] = "info_birthday";
            $ExeclName['入职时间'] = "staffer_jointime";
            $ExeclName['职工邮箱'] = "staffer_email";
            $ExeclName['初始密码'] = "staffer_pass";

            $WorkerList = execl_to_array($up_file, $ExeclName);
            unset($WorkerList[0]);

            $workersList=array();


            if ($WorkerList) {
                foreach ($WorkerList as $workersVar) {
                    if ($workersVar['staffer_cnname'] != '' && $workersVar['staffer_employeepid'] != '' && $workersVar['staffer_sex'] != '' && $workersVar['staffer_mobile'] != '') {
                        $workersList[] = $workersVar;
                    } else {
                        $PlayInfoVar = array();
                        $PlayInfoVar['staffer_cnname'] = $workersVar['staffer_cnname'];
                        $PlayInfoVar['staffer_enname'] = $workersVar['staffer_enname'];
                        $PlayInfoVar['staffer_employeepid'] = $workersVar['staffer_employeepid'];
                        $PlayInfoVar['staffer_sex'] = $workersVar['staffer_sex'];
                        $PlayInfoVar['staffer_mobile'] = $workersVar['staffer_mobile'];
                        $PlayInfoVar['info_birthday'] = $workersVar['info_birthday'];
                        $PlayInfoVar['staffer_jointime'] = $workersVar['staffer_jointime'];
                        $PlayInfoVar['staffer_email'] = $workersVar['staffer_email'];
                        $PlayInfoVar['staffer_pass'] = $workersVar['staffer_pass'];
                        $PlayInfoVar['error'] = "1";
                        $PlayInfoVar['errortip'] = "导入信息不完整";
                        $PlayInfo[] = $PlayInfoVar;
                    }
                }
            }
            if (count($workersList) > 100000) {
                ajax_return(array('error' => 1, 'errortip' => "导入数量不能大于100000!", "bakfuntion" => "errormotify"));
            }
            if($workersList) {
                foreach ($workersList as $workersVar) {

                    if(is_numeric($workersVar['info_birthday']) && strlen($workersVar['info_birthday'])==5){
                        $workersVar['info_birthday']= gmdate('Y-m-d',  intval(($workersVar['info_birthday'] - 25569) * 3600 * 24));
                    }

                    if(is_numeric($workersVar['staffer_jointime']) && strlen($workersVar['staffer_jointime'])==5){
                        $workersVar['staffer_jointime']= gmdate('Y-m-d',  intval(($workersVar['staffer_jointime'] - 25569) * 3600 * 24));
                    }


                    $PlayInfoVar=array();
                    $PlayInfoVar['staffer_cnname'] = $workersVar['staffer_cnname'];
                    $PlayInfoVar['staffer_enname'] = $workersVar['staffer_enname'];
                    $PlayInfoVar['staffer_employeepid'] = $workersVar['staffer_employeepid'];
                    $PlayInfoVar['staffer_sex'] = $workersVar['staffer_sex'];
                    $PlayInfoVar['staffer_mobile'] = $workersVar['staffer_mobile'];
                    $PlayInfoVar['info_birthday'] = $workersVar['info_birthday'];
                    $PlayInfoVar['staffer_jointime'] = $workersVar['staffer_jointime'];
                    $PlayInfoVar['staffer_email'] = $workersVar['staffer_email'];

                    if($this->DataControl->getFieldOne("smc_staffer","staffer_id","staffer_employeepid='{$workersVar['staffer_employeepid']}' and company_id='{$request['company_id']}'")){

                        $PlayInfoVar['error'] = "1";
                        $PlayInfoVar['errortip'] = "职工编号已存在,不可重复导入";
                        $PlayInfo[] = $PlayInfoVar;
                        continue;
                    }else{




                        $workersVar['info_birthday']=str_replace("/","-",$workersVar['info_birthday']);
                        $workersVar['staffer_jointime']=str_replace("/","-",$workersVar['staffer_jointime']);

                        if($this->checkMobile($workersVar['staffer_mobile'])){
                            $PlayInfoVar['error'] = "1";
                            $PlayInfoVar['errortip'] = "手机格式不符";
                            $PlayInfo[] = $PlayInfoVar;
                            continue;
                        }
                        if($workersVar['staffer_email']){
                            if($this->checkEmail($workersVar['staffer_email'])){
                                $PlayInfoVar['error'] = "1";
                                $PlayInfoVar['errortip'] = "邮箱格式不符";
                                $PlayInfo[] = $PlayInfoVar;
                                continue;
                            }
                        }

                        if($this->DataControl->getFieldOne("smc_staffer","staffer_id","staffer_mobile='{$workersVar['staffer_mobile']}' and company_id='{$request['company_id']}'")){

                            $PlayInfoVar['error'] = "1";
                            $PlayInfoVar['errortip'] = "职工手机号已存在,不可重复导入";
                            $PlayInfo[] = $PlayInfoVar;
                            continue;
                        }


                        $like=date("Ymd",time());

                        $data=array();
                        $stuInfo=$this->DataControl->selectOne("select staffer_branch from smc_staffer where staffer_branch like '{$like}%' AND LENGTH(staffer_branch) = '14' order by staffer_branch DESC limit 0,1");
                        if($stuInfo){
                            $data['staffer_branch']=$stuInfo['staffer_branch']+1;
                        }else{
                            $data['staffer_branch'] =$like.'000001';
                        }
                        $data['company_id']=$request['company_id'];
                        $data['staffer_cnname']=$workersVar['staffer_cnname'];
                        $data['staffer_enname']=$workersVar['staffer_enname'];
                        $data['staffer_employeepid']=$workersVar['staffer_employeepid'];
                        $data['staffer_sex']=$workersVar['staffer_sex'];
                        $data['staffer_mobile']=$workersVar['staffer_mobile'];

                        $data['staffer_jointime']=strtotime($workersVar['staffer_jointime'])?date("Y-m-d",strtotime($workersVar['staffer_jointime'])):'';

                        $data['staffer_email']=$workersVar['staffer_email'];
                        $data['staffer_pass']=$workersVar['staffer_pass']?md5(trim($workersVar['staffer_pass'])):md5(substr($workersVar['staffer_mobile'],-6));
                        $data['staffer_bakpass']=$workersVar['staffer_pass']?trim($workersVar['staffer_pass']):substr($workersVar['staffer_mobile'],-6);
                        $data['staffer_createtime']=time();
                        if ($this->DataControl->insertData("smc_staffer", $data)) {
                            $stafferOne=$this->DataControl->getFieldOne("smc_staffer","staffer_id","company_id='{$request['company_id']}' and staffer_employeepid='{$workersVar['staffer_employeepid']}'");
                            if(isset($workersVar['info_birthday']) && $workersVar['info_birthday']!=''){
                                $data=array();
                                $data['staffer_id']=$stafferOne['staffer_id'];
                                $data['info_birthday']=strtotime($workersVar['info_birthday'])?date("Y-m-d",strtotime($workersVar['info_birthday'])):'';
                                $data['info_createtime']=time();
                                $this->DataControl->insertData("smc_staffer_info",$data);
                            }

                            $PlayInfoVar['error'] = "0";
                            $PlayInfoVar['errortip'] = "导入成功";
                        } else {
                            $PlayInfoVar['error'] = "1";
                            $PlayInfoVar['errortip'] = "导入失败";
                        }
                    }

                    $PlayInfo[] = $PlayInfoVar;
                }
            }

        }else{
            $PlayInfoVar = array();
            $PlayInfoVar['staffer_cnname'] = '';
            $PlayInfoVar['staffer_enname'] = '';
            $PlayInfoVar['staffer_employeepid'] = '';
            $PlayInfoVar['staffer_sex'] = '';
            $PlayInfoVar['staffer_mobile'] = '';
            $PlayInfoVar['info_birthday'] = '';
            $PlayInfoVar['staffer_jointime'] = '';
            $PlayInfoVar['staffer_email'] = '';
            $PlayInfoVar['error'] = "1";
            $PlayInfoVar['errortip'] = "文件不存在";
            $PlayInfo[] = $PlayInfoVar;
        }
        $this->Recordweblog("集团管理",$this->c,"集团:{$request['company_id']},导入职工资料");
        $this->smarty->assign("PlayInfo", $PlayInfo);

    }

    function ImportSchoolView(){
        $request = Input('get.','','trim,addslashes');
        $smarty = $this->smarty;
        $smarty->assign("company_id",$request['company_id']);
        $smarty->assign("school_id",$request['school_id']);
    }

    function ImportSchoolGuideView(){
        $request = Input('get.','','trim,addslashes');
        $smarty = $this->smarty;
        $smarty->assign("company_id",$request['company_id']);
    }

    function ImportSchoolInfoView(){
        $request = Input('post.','','trim,addslashes');
        if(!isset($request['company_id']) || $request['company_id']==''){
            ajax_return(array('error' => 1, 'errortip' => "集团ID必须传", "bakfuntion" => "errormotify"));
        }

        if($_FILES['uploadFile']['name'] == '' || $_FILES['uploadFile']['error'] == '1'){
            $PlayInfoVar = array();
            $PlayInfoVar['school_cnname'] = '';
            $PlayInfoVar['school_enname'] = '';
            $PlayInfoVar['school_shortname'] = '';
            $PlayInfoVar['district_cnname'] = '';
            $PlayInfoVar['district_branch'] = '';
            $PlayInfoVar['companies_cnname'] = '';
            $PlayInfoVar['school_address'] = '';
            $PlayInfoVar['school_phone'] = '';
            $PlayInfoVar['error'] = "1";
            $PlayInfoVar['errortip'] = "文件不存在";
            $PlayInfo[] = $PlayInfoVar;
            $this->smarty->assign("PlayInfo", $PlayInfo);
            exit;
        }

        $fileType = array('xls','csv','xlsx');
        $uploadfile = new \Webfile($_FILES['uploadFile'],$files_dir='../static/file', $size = 2097152*10,$fileType);
        $up_file = $uploadfile->upload();

        $PlayInfo = array();
        if($up_file && $uploadfile->updatastatus) {
            $ExeclName = array();
            $ExeclName['校区名称'] = "school_cnname";
            $ExeclName['检索代码'] = "school_enname";
            $ExeclName['学校简称'] = "school_shortname";
            $ExeclName['所在区域'] = "district_cnname";
            $ExeclName['区域编号'] = "district_branch";
            $ExeclName['所属企业'] = "companies_cnname";
            $ExeclName['校园地址'] = "school_address";
            $ExeclName['联系电话'] = "school_phone";

            $WorkerList = execl_to_array($up_file, $ExeclName);
            unset($WorkerList[0]);

            $workersList=array();

            if ($WorkerList) {
                foreach ($WorkerList as $workersVar) {
                    if ($workersVar['school_cnname'] !== '' && $workersVar['school_enname'] !== '' && $workersVar['school_shortname'] !== '' && $workersVar['district_cnname'] !== '' && $workersVar['district_branch'] !== '' && $workersVar['companies_cnname'] !== '' && $workersVar['school_address'] !== '' && $workersVar['school_phone'] !== '') {
                        $workersList[] = $workersVar;
                    } else {
                        $PlayInfoVar = array();
                        $PlayInfoVar['school_cnname'] = $workersVar['school_cnname'];
                        $PlayInfoVar['school_enname'] = $workersVar['school_enname'];
                        $PlayInfoVar['school_shortname'] = $workersVar['school_shortname'];
                        $PlayInfoVar['district_cnname'] = $workersVar['district_cnname'];
                        $PlayInfoVar['district_branch'] = $workersVar['district_branch'];
                        $PlayInfoVar['companies_cnname'] = $workersVar['companies_cnname'];
                        $PlayInfoVar['school_address'] = $workersVar['school_address'];
                        $PlayInfoVar['school_phone'] = $workersVar['school_phone'];
                        $PlayInfoVar['error'] = "1";
                        $PlayInfoVar['errortip'] = "导入信息不完整";
                        $PlayInfo[] = $PlayInfoVar;
                    }
                }
            }
            if (count($workersList) > 100000) {
                ajax_return(array('error' => 1, 'errortip' => "导入数量不能大于100000!", "bakfuntion" => "errormotify"));
            }

            $like = substr(date("Ymd", time()), 2, 6);

            if($workersList) {
                foreach ($workersList as $workersVar) {
                    $PlayInfoVar=array();
                    $PlayInfoVar['school_cnname'] = $workersVar['school_cnname'];
                    $PlayInfoVar['school_enname'] = $workersVar['school_enname'];
                    $PlayInfoVar['school_shortname'] = $workersVar['school_shortname'];
                    $PlayInfoVar['district_cnname'] = $workersVar['district_cnname'];
                    $PlayInfoVar['district_branch'] = $workersVar['district_branch'];
                    $PlayInfoVar['companies_cnname'] = $workersVar['companies_cnname'];
                    $PlayInfoVar['school_address'] = $workersVar['school_address'];
                    $PlayInfoVar['school_phone'] = $workersVar['school_phone'];

                    if($this->DataControl->getFieldOne("smc_school","school_id","company_id='{$request['company_id']}' and school_cnname='{$workersVar['school_cnname']}'")){

                        $PlayInfoVar['error'] = "1";
                        $PlayInfoVar['errortip'] = "学校已存在,不可重复导入";
                        $PlayInfo[] = $PlayInfoVar;
                        continue;
                    }else{

                        $districtOne=$this->DataControl->getFieldOne("gmc_company_district","district_id","company_id='{$request['company_id']}' and district_branch='{$workersVar['district_branch']}' and district_cnname='{$workersVar['district_cnname']}'");

                        if(!$districtOne){
                            $data=array();
                            $data['company_id']=$request['company_id'];
                            $data['district_branch']=$workersVar['district_branch'];
                            $data['district_cnname']=$workersVar['district_cnname'];
                            $district_id=$this->DataControl->insertData("gmc_company_district",$data);
                        }else{
                            $district_id=$districtOne['district_id'];
                        }

                        $companiesOne=$this->DataControl->getFieldOne("gmc_code_companies","companies_id","company_id='{$request['company_id']}' and companies_cnname='{$workersVar['companies_cnname']}'");

                        if(!$companiesOne){
                            $data=array();
                            $data['company_id']=$request['company_id'];
                            $data['companies_cnname']=$workersVar['companies_cnname'];
                            $data['companies_createtime']=time();
                            $companies_id=$this->DataControl->insertData("gmc_code_companies",$data);
                        }else{
                            $companies_id=$companiesOne['companies_id'];
                        }

                        $data=array();

                        $stuInfo = $this->DataControl->selectOne("select school_branch from smc_school where school_branch like '{$like}%' order by school_branch DESC limit 0,1");
                        if ($stuInfo) {
                            $data['school_branch'] = $stuInfo['school_branch'] + 1;
                        } else {
                            $data['school_branch'] = $like . '001';
                        }

                        $data['company_id']=$request['company_id'];
                        $data['companies_id']=$companies_id;
                        $data['district_id']=$district_id;
                        $data['school_cnname']=$workersVar['school_cnname'];
                        $data['school_enname']=$workersVar['school_enname'];
                        $data['school_shortname']=$workersVar['school_shortname'];
                        $data['school_address']=$workersVar['school_address'];
                        $data['school_phone']=$workersVar['school_phone'];
                        $data['school_createtime']=time();
                        if ($this->DataControl->insertData("smc_school", $data)) {
                            $PlayInfoVar['error'] = "0";
                            $PlayInfoVar['errortip'] = "导入成功";
                        } else {
                            $PlayInfoVar['error'] = "1";
                            $PlayInfoVar['errortip'] = "导入失败";
                        }
                    }

                    $PlayInfo[] = $PlayInfoVar;
                }
            }

        }else{
            $PlayInfoVar = array();
            $PlayInfoVar['school_cnname'] = '';
            $PlayInfoVar['school_enname'] = '';
            $PlayInfoVar['school_shortname'] = '';
            $PlayInfoVar['district_cnname'] = '';
            $PlayInfoVar['district_branch'] = '';
            $PlayInfoVar['companies_cnname'] = '';
            $PlayInfoVar['school_address'] = '';
            $PlayInfoVar['school_phone'] = '';
            $PlayInfoVar['error'] = "1";
            $PlayInfoVar['errortip'] = "文件不存在";
            $PlayInfo[] = $PlayInfoVar;
        }

        $this->Recordweblog("集团管理",$this->c,"集团:{$request['company_id']},导入分校资料");

        $this->smarty->assign("PlayInfo", $PlayInfo);

    }

    function SchoolView()
    {
        $request = Input('get.','','trim,addslashes');
        $DataControl = $this->DataControl;
        $smarty = $this->smarty;


        if(!isset($request['p'])){
            $p = '1';
        }else{
            $p= $request['p'];
        }

        $datawhere = " 1 ";
        $pageurl = "/{$this->u}/{$this->t}?company_id='{$request['company_id']}'";
        $datatype = array();
        if(isset($request['company_id']) && $request['company_id']!=''){
            $pageurl .="&company_id={$request['company_id']}";
            $datatype['company_id'] = $request['company_id'];
        }

        if(isset($request['keyword']) && $request['keyword'] !==''){
            $datawhere .= " and (s.school_cnname = '{$request['keyword']}' or s.school_enname like '%{$request['keyword']}%' or s.school_shortname like '%{$request['keyword']}%' or s.school_phone like '%{$request['keyword']}%' or s.school_branch like '%{$request['keyword']}%')";
            $pageurl .="&keyword={$request['keyword']}";
            $datatype['keyword'] = $request['keyword'];
        }

        $sql = "select s.school_id,s.school_cnname,s.school_enname,s.school_shortname,s.school_branch,s.school_address,s.school_phone,cd.district_cnname,cc.companies_cnname,s.school_createtime
                from smc_school as s
                left join gmc_company_district as cd on cd.district_id=s.district_id
                left join gmc_code_companies as cc on cc.companies_id=s.companies_id
                where {$datawhere} and s.company_id='{$request['company_id']}'
                and s.company_id in (select B.company_id
                                    from imc_sales_contract as A,gmc_company as B where A.company_id=B.company_id and A.saleman_id='{$this->isaleman['saleman_id']}')
                order by s.school_createtime desc";

        $db_nums = $DataControl->selectOne("select count(s.school_id) as countnums
                from smc_school as s
                left join gmc_company_district as cd on cd.district_id=s.district_id
                left join gmc_code_companies as cc on cc.companies_id=s.companies_id
                where {$datawhere} and s.company_id='{$request['company_id']}'
                and s.company_id in (select B.company_id
                                    from imc_sales_contract as A,gmc_company as B where A.company_id=B.company_id and A.saleman_id='{$this->isaleman['saleman_id']}')
                ");//相关条件下的总记录数COUNT(*)
        $allnum = $db_nums['countnums'];

        $datalist = $DataControl->dbwherePage($sql,$allnum,'10',$pageurl.'&p=',$p,'10','2');
        $smarty->assign("pagelist",$datalist['pages']);//筛选信息

        $smarty->assign("dataList",$datalist['cont']);
        $smarty->assign("datatype",$datatype);
    }


    function ImportStudentGuideView(){
        $request = Input('get.','','trim,addslashes');
        $smarty = $this->smarty;
        $smarty->assign("school_id",$request['school_id']);
    }

    function ImportStudentView(){
        $request = Input('post.','','trim,addslashes');
        if(!isset($request['school_id']) || $request['school_id']==''){
            ajax_return(array('error' => 1, 'errortip' => "学校ID必须传", "bakfuntion" => "errormotify"));
        }

        $schoolOne=$this->DataControl->getFieldOne("smc_school","company_id,school_id,companies_id","school_id='{$request['school_id']}'");
        if(!$schoolOne){
            ajax_return(array('error' => 1, 'errortip' => "学校不存在", "bakfuntion" => "errormotify"));
        }

        if($_FILES['uploadFile']['name'] == '' || $_FILES['uploadFile']['error'] == '1'){
            $PlayInfoVar = array();
            $PlayInfoVar['student_cnname'] = '';
            $PlayInfoVar['student_enname'] = '';
            $PlayInfoVar['student_idcard'] = '';
            $PlayInfoVar['student_sex'] = '';
            $PlayInfoVar['student_birthday'] = '';
            $PlayInfoVar['change_day'] = '';
            $PlayInfoVar['parenter_mobile'] = '';
            $PlayInfoVar['parenter_cnname'] = '';
            $PlayInfoVar['family_relation'] = '';
            $PlayInfoVar['course_cnname'] = '';
            $PlayInfoVar['course_branch'] = '';
            $PlayInfoVar['coursebalance_time'] = '';
            $PlayInfoVar['coursebalance_figure'] = '';
            $PlayInfoVar['student_balance'] = '';
            $PlayInfoVar['error'] = "1";
            $PlayInfoVar['errortip'] = "文件不存在";
            $PlayInfo[] = $PlayInfoVar;
            $this->smarty->assign("PlayInfo", $PlayInfo);
            exit;
        }

        $fileType = array('xls','csv','xlsx');
        $uploadfile = new \Webfile($_FILES['uploadFile'],$files_dir='../static/file', $size = 2097152*10,$fileType);
        $up_file = $uploadfile->upload();

        $PlayInfo = array();
        if($up_file && $uploadfile->updatastatus) {
            $ExeclName = array();
            $ExeclName['学员中文名'] = "student_cnname";
            $ExeclName['学员英文名'] = "student_enname";
            $ExeclName['身份证号码'] = "student_idcard";
            $ExeclName['性别'] = "student_sex";
            $ExeclName['生日'] = "student_birthday";
            $ExeclName['入校时间'] = "change_day";
            $ExeclName['联系电话'] = "parenter_mobile";
            $ExeclName['联系人姓名'] = "parenter_cnname";
            $ExeclName['亲属关系'] = "family_relation";
            $ExeclName['课程名称'] = "course_cnname";
            $ExeclName['课程别编号'] = "course_branch";
            $ExeclName['剩余课次'] = "coursebalance_time";
            $ExeclName['剩余课程金额'] = "coursebalance_figure";
            $ExeclName['账户余额'] = "student_balance";
            $ExeclName['标签'] = "tags_name";

            $WorkerList = execl_to_array($up_file, $ExeclName);
            unset($WorkerList[0]);

            $workersList=array();

            if ($WorkerList) {
                foreach ($WorkerList as $workersVar) {
                    if ($workersVar['student_cnname'] !== '' && $workersVar['student_sex'] !== '' && $workersVar['change_day'] !== '' && $workersVar['parenter_mobile'] !== '' && $workersVar['student_idcard'] !== '') {
                        $workersList[] = $workersVar;
                    } else {
                        $PlayInfoVar = array();
                        $PlayInfoVar['student_cnname'] = $workersVar['student_cnname'];
                        $PlayInfoVar['student_enname'] = $workersVar['student_enname'];
                        $PlayInfoVar['student_idcard'] = $workersVar['student_idcard'];
                        $PlayInfoVar['student_sex'] = $workersVar['student_sex'];
                        $PlayInfoVar['student_birthday'] = $workersVar['student_birthday'];
                        $PlayInfoVar['change_day'] = $workersVar['change_day'];
                        $PlayInfoVar['parenter_mobile'] = $workersVar['parenter_mobile'];
                        $PlayInfoVar['parenter_cnname'] = $workersVar['parenter_cnname'];
                        $PlayInfoVar['family_relation'] = $workersVar['family_relation'];
                        $PlayInfoVar['course_cnname'] = $workersVar['course_cnname'];
                        $PlayInfoVar['course_branch'] = $workersVar['course_branch'];
                        $PlayInfoVar['coursebalance_time'] = $workersVar['coursebalance_time'];
                        $PlayInfoVar['coursebalance_figure'] = $workersVar['coursebalance_figure'];
                        $PlayInfoVar['student_balance'] = $workersVar['student_balance'];
                        $PlayInfoVar['tags_name'] = $workersVar['tags_name'];
                        $PlayInfoVar['error'] = "1";
                        $PlayInfoVar['errortip'] = "导入信息不完整";
                        $PlayInfo[] = $PlayInfoVar;
                    }
                }
            }
            if (count($workersList) > 100000) {
                ajax_return(array('error' => 1, 'errortip' => "导入数量不能大于100000!", "bakfuntion" => "errormotify"));
            }

            $like = date("Ymd", time());

            if($workersList) {
                foreach ($workersList as $workersVar) {
                    $workersVar['student_cnname'] = trim($workersVar['student_cnname']);
                    $workersVar['student_enname'] = trim($workersVar['student_enname']);
                    $workersVar['student_idcard'] = trim($workersVar['student_idcard']);
                    $workersVar['student_sex'] = trim($workersVar['student_sex']);
                    $workersVar['student_birthday'] = trim($workersVar['student_birthday']);
                    $workersVar['change_day'] = trim($workersVar['change_day']);
                    $workersVar['parenter_mobile'] = trim($workersVar['parenter_mobile']);
                    $workersVar['parenter_cnname'] = trim($workersVar['parenter_cnname']);
                    $workersVar['family_relation'] = trim($workersVar['family_relation']);
                    $workersVar['course_cnname'] = trim($workersVar['course_cnname']);
                    $workersVar['course_branch'] = trim($workersVar['course_branch']);
                    $workersVar['coursebalance_time'] = trim($workersVar['coursebalance_time']);
                    $workersVar['coursebalance_figure'] = trim($workersVar['coursebalance_figure']);
                    $workersVar['student_balance'] = trim($workersVar['student_balance']);
                    $workersVar['tags_name'] = trim($workersVar['tags_name']);

                    if(is_numeric($workersVar['student_birthday']) && strlen($workersVar['student_birthday'])==5){
                        $workersVar['student_birthday']= gmdate('Y-m-d',  intval(($workersVar['student_birthday'] - 25569) * 3600 * 24));
                    }

                    if(is_numeric($workersVar['change_day']) && strlen($workersVar['change_day'])==5){
                        $workersVar['change_day']= gmdate('Y-m-d',  intval(($workersVar['change_day'] - 25569) * 3600 * 24));
                    }

                    $PlayInfoVar=array();
                    $PlayInfoVar['student_cnname'] = $workersVar['student_cnname'];
                    $PlayInfoVar['student_enname'] = $workersVar['student_enname'];
                    $PlayInfoVar['student_idcard'] = $workersVar['student_idcard'];
                    $PlayInfoVar['student_sex'] = $workersVar['student_sex'];
                    $PlayInfoVar['student_birthday'] = $workersVar['student_birthday'];
                    $PlayInfoVar['change_day'] = $workersVar['change_day'];
                    $PlayInfoVar['parenter_mobile'] = $workersVar['parenter_mobile'];
                    $PlayInfoVar['parenter_cnname'] = $workersVar['parenter_cnname'];
                    $PlayInfoVar['family_relation'] = $workersVar['family_relation'];
                    $PlayInfoVar['course_cnname'] = $workersVar['course_cnname'];
                    $PlayInfoVar['course_branch'] = $workersVar['course_branch'];
                    $PlayInfoVar['coursebalance_time'] = $workersVar['coursebalance_time'];
                    $PlayInfoVar['coursebalance_figure'] = $workersVar['coursebalance_figure'];
                    $PlayInfoVar['student_balance'] = $workersVar['student_balance'];
                    $PlayInfoVar['tags_name'] = $workersVar['tags_name'];


                    $workersVar['student_birthday']=str_replace("/","-",$workersVar['student_birthday']);
                    $workersVar['change_day']=str_replace("/","-",$workersVar['change_day']);

                    $sql="select st.student_id,st.student_branch
                          from smc_student as st
                          where st.student_idcard='{$workersVar['student_idcard']}' and st.company_id='{$schoolOne['company_id']}'
                          ";

                    $studentOne=$this->DataControl->selectOne($sql);
                    if($studentOne){
                        $student_id=$studentOne['student_id'];
                        $student_branch=$studentOne['student_branch'];
                    }else{
                        $student_data = array();
                        $stuInfo = $this->DataControl->selectOne("select student_branch from smc_student
where student_branch like '{$like}%' AND LENGTH(student_branch) = '14' order by student_branch DESC limit 0,1");
                        if ($stuInfo) {
                            $student_branch = $stuInfo['student_branch'] + 1;
                        } else {
                            $student_branch = $like . '000001';
                        }

                        $student_data['student_branch'] = $student_branch;
                        $student_data['student_cnname'] = $workersVar['student_cnname'];
                        $student_data['student_enname'] = $workersVar['student_enname'];
                        $student_data['student_sex'] = $workersVar['student_sex'];
                        $student_data['student_idcard'] = $workersVar['student_idcard'];
                        $student_data['company_id'] = $schoolOne['company_id'];
                        $student_data['student_birthday'] = $workersVar['student_birthday'];
                        $student_data['student_createtime'] = time();
                        $student_data['student_updatatime'] = time();
                        $student_id = $this->DataControl->insertData("smc_student", $student_data);
                    }

                    $parentOne=$this->DataControl->getFieldOne("smc_parenter","parenter_id","parenter_mobile='{$workersVar['parenter_mobile']}'");

                    if($parentOne){
                        $parenter_id=$parentOne['parenter_id'];
                    }else{

                        if($this->checkMobile($workersVar['parenter_mobile'])){
                            $PlayInfoVar['error'] = "1";
                            $PlayInfoVar['errortip'] = "手机格式不符";
                            $PlayInfo[] = $PlayInfoVar;
                            continue;
                        }
                        $data=array();
                        $data['parenter_mobile']=trim($workersVar['parenter_mobile']);
                        $data['parenter_cnname']=$workersVar['parenter_cnname'];
                        $data['parenter_pass'] = md5(substr(trim($workersVar['parenter_mobile']),-6));
                        $data['parenter_bakpass'] = substr(trim($workersVar['parenter_mobile']),-6);
                        $data['parenter_addtime']=time();
                        $parenter_id=$this->DataControl->insertData("smc_parenter",$data);
                    }

                    if(!$this->DataControl->getFieldOne("smc_student_family","family_id","parenter_id='{$parenter_id}' and student_id='{$student_id}'")){
                        $data = array();
                        $data['student_id'] = $student_id;
                        $data['parenter_id'] = $parenter_id;
                        $data['family_relation'] = $workersVar['family_relation'];
                        $data['family_mobile'] = trim($workersVar['parenter_mobile']);
                        $data['family_cnname'] = $workersVar['parenter_cnname'];

                        if(!$this->DataControl->getFieldOne("smc_student_family","family_id","parenter_id='{$parenter_id}' and student_id='{$student_id}' and family_isdefault='1'")){
                            $data['family_isdefault']=1;
                        }

                        $this->DataControl->insertData("smc_student_family", $data);
                    }

                    if (!$this->DataControl->getFieldOne("smc_student_enrolled", "student_id", "school_id = '{$schoolOne['school_id']}' and student_id = '{$student_id}'")) {
                        //新增学员记录
                        $data = array();
                        $data['school_id'] = $schoolOne['school_id'];
                        $data['student_id'] =$student_id;
                        $data['enrolled_status'] =0;
                        $data['enrolled_createtime'] = time();
                        $this->DataControl->insertData("smc_student_enrolled", $data);

                        $changeInfo = $this->DataControl->selectOne("select change_pid from smc_student_change where change_pid like '{$like}%' and company_id='{$schoolOne['company_id']}' order by change_pid DESC limit 0,1");
                        $stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_cnname", "staffer_id='12357' and company_id='{$schoolOne['company_id']}'");
                        $data = array();
                        $data['company_id'] = $schoolOne['company_id'];
                        $data['student_id'] = $student_id;
                        if ($changeInfo) {
                            $data['change_pid'] = $changeInfo['change_pid'] + 1;
                        } else {
                            $data['change_pid'] = $like . '000001';
                        }
                        $data['to_stuchange_code'] = 'A01';
                        $data['to_school_id'] = $schoolOne['school_id'];
                        $data['change_status'] = 1;
                        $data['change_day'] = date("Y-m-d", strtotime($workersVar['change_day']));
                        $data['change_reason'] = '入校';
                        $data['change_workername'] = $stafferOne['staffer_cnname'];
                        $data['change_createtime'] = time();
                        $this->DataControl->insertData("smc_student_change", $data);

                        $log_data = array();
                        $log_data['change_pid'] = $data['change_pid'];
                        $log_data['company_id'] = $schoolOne['company_id'];
                        $log_data['student_id'] = $student_id;
                        $log_data['changelog_type'] = 1;
                        $log_data['stuchange_code'] = 'A01';
                        $log_data['school_id'] = $schoolOne['school_id'];
                        $log_data['changelog_note'] = '入校';
                        $log_data['changelog_day'] = date("Y-m-d", strtotime($workersVar['change_day']));
                        $log_data['staffer_id'] = '';
                        $log_data['changelog_createtime'] =time();
                        $this->DataControl->insertData("smc_student_changelog", $log_data);

                    }

                    if($workersVar['tags_name']!=''){

                        if(!$this->DataControl->getFieldOne("smc_student_tags","tags_id","student_id='{$student_id}' and school_id='{$schoolOne['school_id']}' and tags_name='{$workersVar['tags_name']}'")){
                            $data=array();
                            $data['company_id']= $schoolOne['company_id'];
                            $data['school_id']= $schoolOne['school_id'];
                            $data['tags_class']= 1;
                            $data['student_id']= $student_id;
                            $data['tags_name']= $workersVar['tags_name'];
                            $data['tags_createtime']= strtotime($workersVar['change_day']);
                            $this->DataControl->insertData("smc_student_tags",$data);
                        }
                    }

                    if($workersVar['coursebalance_figure']>0 && $workersVar['coursebalance_time']>0){

                        $courseOne=$this->DataControl->getFieldOne("smc_course","course_id,course_branch,coursecat_id,course_classnum","course_branch='{$workersVar['course_branch']}' and company_id='{$schoolOne['company_id']}'");
                        if(!$courseOne){
                            $PlayInfoVar['error'] = "1";
                            $PlayInfoVar['errortip'] = "不存在课程数据";
                            $PlayInfo[] = $PlayInfoVar;
                            continue;
                        }else{
                            $sql="select scb.coursebalance_id,scb.coursebalance_figure,scb.coursebalance_time
                              from smc_student_coursebalance as scb
                              where scb.student_id='{$student_id}' and scb.course_id='{$courseOne['course_id']}' and scb.school_id='{$schoolOne['school_id']}'";
                            $courseListOne=$this->DataControl->selectOne($sql);

                            $courseCompaniesOne=$this->getSchoolCourseCompanies($schoolOne['school_id'],0,$courseOne['course_id']);


                            $schoolOne['companies_id']=$courseCompaniesOne['companies_id'];

                            $one=$this->getCoursePricing($courseOne['course_branch'],$schoolOne['company_id'],$schoolOne['school_id']);

                            if(!$one){
                                $PlayInfoVar['error'] = "1";
                                $PlayInfoVar['errortip'] = "不存在课程定价数据";
                                $PlayInfo[] = $PlayInfoVar;
                                continue;
                            }
                            $coursebalance_time=$workersVar['coursebalance_time']?$workersVar['coursebalance_time']:0;
                            $coursebalance_figure=$workersVar['coursebalance_figure']?$workersVar['coursebalance_figure']:0;

                            $student_coursebalance_data=array();

                            $random = $this->create_guid();

                            if($courseListOne){
                                $PlayInfoVar['error'] = "1";
                                $PlayInfoVar['errortip'] = "课程已存在";
                                $PlayInfo[] = $PlayInfoVar;
                                continue;

                            }else{
                                $student_coursebalance_data['student_id']=$student_id;
                                $student_coursebalance_data['course_id']=$courseOne['course_id'];
                                $student_coursebalance_data['school_id']=$schoolOne['school_id'];
                                $student_coursebalance_data['companies_id']=$schoolOne['companies_id'];
                                $student_coursebalance_data['company_id']=$schoolOne['company_id'];
                                $student_coursebalance_data['coursebalance_figure']=$coursebalance_figure;
                                $student_coursebalance_data['coursebalance_unitexpend']=ceil($coursebalance_figure/$coursebalance_time);//消耗单价
                                $student_coursebalance_data['coursebalance_unitearning']=ceil($coursebalance_figure/$coursebalance_time);//消耗单价
                                $student_coursebalance_data['coursebalance_unitrefund']=$one['tuition_unitprice'];//退费单价
                                $student_coursebalance_data['coursebalance_time']=$coursebalance_time;
                                $student_coursebalance_data['coursebalance_createtime']=time();
                                $student_coursebalance_data['coursebalance_updatatime'] =time();
                                $this->DataControl->insertData("smc_student_coursebalance",$student_coursebalance_data);

                                $courselog_data=array();
                                $courselog_data['student_id']=$student_id;
                                $courselog_data['log_class']=0;
                                $courselog_data['school_id']=$schoolOne['school_id'];
                                $courselog_data['companies_id']=$schoolOne['companies_id'];
                                $courselog_data['course_id']=$courseOne['course_id'];
                                $courselog_data['log_random']=$random;
                                $courselog_data['log_playname']='导入课程余额';
                                $courselog_data['log_playclass']='+';
                                $courselog_data['log_fromamount']=0;
                                $courselog_data['log_playamount']=$coursebalance_figure;
                                $courselog_data['log_finalamount']=$coursebalance_figure;

                                $courselog_data['log_fromtimes']=0;
                                $courselog_data['log_playtimes']=$coursebalance_time;
                                $courselog_data['log_finaltimes']=$coursebalance_time;

                                $courselog_data['log_reason']='导入课程余额';
                                $courselog_data['log_time']=time();
                                $this->DataControl->insertData("smc_student_coursebalance_log",$courselog_data);

                                $time_data=array();
                                $time_data['student_id']=$student_id;
                                $time_data['school_id']=$schoolOne['school_id'];
                                $time_data['companies_id']=$schoolOne['companies_id'];
                                $time_data['course_id']=$courseOne['course_id'];
                                $time_data['log_random']=$random;
                                $time_data['timelog_playname']='导入课程余额';
                                $time_data['timelog_playclass']='+';
                                $time_data['timelog_fromtimes']=0;
                                $time_data['timelog_playtimes']=$coursebalance_time;
                                $time_data['timelog_finaltimes']=$coursebalance_time;
                                $time_data['timelog_reason']='导入课程余额';
                                $time_data['timelog_time']=time();
                                $this->DataControl->insertData("smc_student_coursebalance_timelog",$time_data);
                            }
                        }
                    }else{
                        if($workersVar['course_branch']){
                            $PlayInfoVar['error'] = "1";
                            $PlayInfoVar['errortip'] = "金额或课次错误,导入失败";
                            $PlayInfo[] = $PlayInfoVar;
                            continue;
                        }
                    }

                    if(isset($workersVar['student_balance']) && $workersVar['student_balance']>0){
                        $stublcOne=$this->DataControl->getFieldOne("smc_student_balance","student_balance","student_id='{$student_id}' and school_id='{$schoolOne['school_id']}' and company_id='{$schoolOne['company_id']}'");
                        if($stublcOne['student_balance']<$workersVar['student_balance']){
                            $balancelog_data=array();
                            $balancelog_data['company_id']=$schoolOne['company_id'];
                            $balancelog_data['school_id']=$schoolOne['school_id'];
                            $balancelog_data['companies_id']=$schoolOne['companies_id'];
                            $balancelog_data['staffer_id']='';
                            $balancelog_data['student_id']=$student_id;
                            $balancelog_data['balancelog_class']=0;
                            $balancelog_data['balancelog_playname']='导入账户余额';
                            $balancelog_data['balancelog_reason']='导入账户余额';
                            $balancelog_data['balancelog_playclass']='+';
                            $balancelog_data['balancelog_fromamount']=$stublcOne['student_balance'];
                            $balancelog_data['balancelog_playamount']=$workersVar['student_balance']-$stublcOne['student_balance'];
                            $balancelog_data['balancelog_finalamount']=$workersVar['student_balance'];
                            $balancelog_data['balancelog_time']=time();
                            $this->DataControl->insertData("smc_student_balancelog",$balancelog_data);

                            $this->getStuBalance($student_id,$schoolOne['company_id'],$schoolOne['school_id'],$schoolOne['companies_id']);

                            //结算账户余额
                            $data = array();
                            $data['student_balance'] = $workersVar['student_balance'];
                            $this->DataControl->updateData("smc_student_balance","student_id='{$student_id}' and school_id = '{$schoolOne['school_id']}' and company_id='{$schoolOne['company_id']}' and companies_id='{$schoolOne['companies_id']}'",$data);
                        }
                    }

                    if(!isset($PlayInfoVar['error']) || $PlayInfoVar['error']=='0'){
                        $PlayInfoVar['error'] = "0";
                        $PlayInfoVar['errortip'] = "导入成功";
                    }


                    $PlayInfo[] = $PlayInfoVar;
                }
            }

        }else{
            $PlayInfoVar = array();
            $PlayInfoVar['student_cnname'] = '';
            $PlayInfoVar['student_enname'] = '';
            $PlayInfoVar['student_idcard'] = '';
            $PlayInfoVar['student_sex'] = '';
            $PlayInfoVar['student_birthday'] = '';
            $PlayInfoVar['change_day'] = '';
            $PlayInfoVar['parenter_mobile'] = '';
            $PlayInfoVar['parenter_cnname'] = '';
            $PlayInfoVar['family_relation'] = '';
            $PlayInfoVar['course_cnname'] = '';
            $PlayInfoVar['course_branch'] = '';
            $PlayInfoVar['coursebalance_time'] = '';
            $PlayInfoVar['coursebalance_figure'] = '';
            $PlayInfoVar['student_balance'] = '';
            $PlayInfoVar['error'] = "1";
            $PlayInfoVar['errortip'] = "文件不存在";
            $PlayInfo[] = $PlayInfoVar;
        }
        $this->Recordweblog("集团管理",$this->c,"分校:{$request['school_id']},导入学生资料");

        $this->smarty->assign("PlayInfo", $PlayInfo);

    }


    function getStuBalance($student_id, $company_id, $school_id, $companies_id)
    {
        $sql = "select s.student_forwardprice,b.* from smc_student as s,smc_student_balance as b
        WHERE b.student_id = s.student_id 
        and b.school_id = '{$school_id}' 
        and s.student_id='{$student_id}' 
        and b.companies_id='{$companies_id}' 
        and s.company_id='{$company_id}'";
        $stublcOne = $this->DataControl->selectOne($sql);

        if (!$stublcOne) {
            $data = array();
            $data['company_id'] = $company_id;
            $data['companies_id'] = $companies_id;
            $data['school_id'] = $school_id;
            $data['student_id'] = $student_id;
            $this->DataControl->insertData("smc_student_balance", $data);

            $stublcOne = $this->DataControl->selectOne($sql);
        }
        return $stublcOne;
    }

    function getSchoolCourseCompanies($school_id, $coursecat_id = 0, $course_id = 0)
    {
        if ($coursecat_id == 0 && $course_id == 0) {
            return false;
        }
        $datawhere = "c.coursecat_id = s.coursecat_id AND s.school_id = '{$school_id}'";
        if ($course_id !== 0) {
            $datawhere .= " AND c.course_id = '{$course_id}'";
        }
        if ($coursecat_id !== 0) {
            $datawhere .= " AND s.coursecat_id = '{$coursecat_id}'";
        }
        $companiesOne = $this->DataControl->selectOne("SELECT s.companies_id FROM smc_school_coursecat_subject AS s, smc_course AS c WHERE {$datawhere} limit 0,1");
        if ($companiesOne) {
            return $companiesOne;
        } else {
            $companiesOne = $this->DataControl->selectOne("SELECT s.companies_id FROM smc_school AS s WHERE s.school_id = '{$school_id}' limit 0,1");
            return $companiesOne;
        }
    }

    function checkEmail($email)
    {
        if (!preg_match("/([\w\-]+\@[\w\-]+\.[\w\-]+)/", $email)) {
            return false;
        } else {
            return true;
        }
    }

    function checkMobile($mobile)
    {
        if (!preg_match("/^(((d{3}))|(d{3}-))?((0d{2,3})|0d{2,3}-)?[1-9]d{6,8}$/", $mobile)) {
            return false;
        } else {
            return true;
        }
    }

    function getCoursePricing($course_branch,$company_id,$school_id){
        $day=date("Y-m-d",time());
        $sql="SELECT
                    t.course_id,
                    p.pricing_id,
                    t.tuition_originalprice,
                    t.tuition_sellingprice,
                    t.tuition_buypiece,
                    t.tuition_unitprice,
                    a.agreement_id,
                    t.tuition_addtime
                FROM
                    smc_fee_pricing_tuition AS t,
                    smc_fee_pricing AS p,
                    smc_fee_agreement AS a,
                    smc_course as sc
                WHERE
                    t.pricing_id = p.pricing_id
                AND p.agreement_id = a.agreement_id
                AND sc.course_id = t.course_id
                AND sc.course_branch = '{$course_branch}'
                AND (
                    (
                        p.pricing_applytype = '1'
                        AND p.pricing_id IN (
                            SELECT
                                pricing_id
                            FROM
                                smc_fee_pricing_apply AS a
                            WHERE
                                a.school_id = '{$school_id}'
                        )
                    )
                    OR (
                        p.pricing_applytype = '-1'
                        AND p.pricing_id NOT IN (
                            SELECT
                                pricing_id
                            FROM
                                smc_fee_pricing_apply AS a
                            WHERE
                                a.school_id = '{$school_id}'
                        )
                    )
                    OR (p.pricing_applytype = '0')
                )
                AND a.company_id = '{$company_id}'
                GROUP BY
                    t.course_id ORDER BY a.agreement_endday DESC";

        $pricingOne=$this->DataControl->selectOne($sql);
        if($pricingOne){
            return $pricingOne;
        }else{
//            echo $sql;
            return array();
        }
    }

    function createStuRandom($student_branch){
        $Str = "ABCDEFGHIJKLMNOPQRSTUVWXYZ123456789";
        $rangtr = $Str[rand(0,34)].$Str[rand(0,34)].$Str[rand(0,34)].$Str[rand(0,34)];
        $rangtime = substr(date("ymdHis",time()),2);
        $Random = $student_branch.$rangtime.$rangtr;
        return $Random;
    }

    function create_guid() {
        $charid = strtoupper(md5(uniqid(mt_rand(), true)));
        $uuid =  substr($charid, 0, 8)
            .substr($charid, 8, 4)
            .substr($charid,12, 4)
            .substr($charid,16, 4)
            .substr($charid,20,12);// "}"
        return $uuid;
    }




    //微商城部分设置
    function ShopingView(){

    }


    //魔术方法
    function __destruct()
    {
        if ($this->c =='Wait') {
            $this->smarty->assign("u", $this->u);
            $this->smarty->assign("t", $this->t);
            $this->display($this->Viewhtm);
            exit;
        }
    }
}