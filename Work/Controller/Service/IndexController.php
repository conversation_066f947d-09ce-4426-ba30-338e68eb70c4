<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn 
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong <PERSON>
 * Date: 2016/12/13
 * Time: 21:06
 */
namespace Work\Controller\Service;

class IndexController extends viewTpl{
    public $data;
    public $isaleman;
    public $Viewhtm;
    public $u;
    public $t;
    public $c;

    function __construct() {
        parent::__construct ();
        if(!$this->check_login()){
            $this->LoginView();
        }
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
        $this->isaleman = $this->SalemanLogin;
        $this->smarty->assign("isaleman", $this->SalemanLogin);
        $this->Viewhtm = $this->router->getController()."/".$this->router->getUrl().".htm";
    }

    //主页
    function HomeView()
    {

        $total = array();
        $sql="select count(co.contract_id) as allusernums from imc_sales_contract as co where co.saleman_id='{$this->isaleman['saleman_id']}'";

        $webtotal = $this->DataControl->selectOne("$sql");
        $total['allusernums'] = $webtotal['allusernums']+0;

        $total['ordernums'] = 0;

        $this->smarty->assign("alltotal",$total);

        $weblogList = $this->DataControl->select("SELECT w.*,u.saleman_cnname FROM imc_userlog as w LEFT JOIN imc_saleman as u ON u.saleman_id = w.saleman_id WHERE w.saleman_id = '{$this->isaleman['saleman_id']}' ORDER BY w.userlog_time DESC limit 0,6");
        $this->smarty->assign("weblogList", $weblogList);

        $this->Viewhtm = "index.htm";
    }

    function myInfoView(){

    }

    function EditpswdView(){

        $this->smarty->assign("act", "Edit");
    }
    //提交处理机制
    function EditAction()
    {
        $request = Input('post.','','trim,addslashes');

        $data = array();
        if($request['user_pswd'] !== $this->isaleman['user_pswd']){
            ajax_return(array('error' => 1,'errortip' => "原始密码不正确!","bakfuntion"=>"errormotify"));
        }
        if($request['user_pass'] !==''){
            $data['user_pswd'] = $request['user_pass'];
        }
        if($this->DataControl->updateData("wel_claims_channel_user","staffer_id = '{$this->isaleman['staffer_id']}'",$data)){
            ajax_return(array('error' => 0,'errortip' => "密码修改成功!","bakfuntion"=>"refreshpage"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "新增失败!","bakfuntion"=>"errormotify"));
        }
    }

    //魔术方法
    function __destruct()
    {
        if ($this->c =='Wait') {
            $this->smarty->assign("u", $this->u);
            $this->smarty->assign("t", $this->t);
            $this->display($this->Viewhtm);
            exit;
        }
    }
}