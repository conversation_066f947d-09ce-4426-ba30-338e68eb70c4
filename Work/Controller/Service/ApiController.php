<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn 
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2016/12/13
 * Time: 21:06
 */
namespace Work\Controller\Service;

class ApiController extends viewTpl{
    public $data;
    public $Viewhtm;
    public $u;
    public $t;
    public $c;

    function __construct() {
        parent::__construct ();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
    }

    //获取集团信息
    function getCompanyView(){
        $request = Input('get.','','trim,addslashes');
        $DataControl = $this->DataControl;
        $datawhere = " 1 ";
        if(isset($request['keyword']) && $request['keyword'] !== ''){
            $datawhere .= " and (c.company_cnname like '%{$request['keyword']}%' or c.company_shortname like '%{$request['keyword']}%' or c.company_code like '%{$request['keyword']}%') ";
        }
        if(isset($request['p']) && $request['p'] !== ''){
            $page = $request['p'];
        }else{
            $page = '1';
        }
        if(isset($request['num']) && $request['num'] !== ''){
            $num = $request['num'];
        }else{
            $num = '20';
        }
        $pagestart = ($page-1)*$num;
        $itemList = $DataControl->selectClear("SELECT c.company_id,c.company_code,c.company_cnname,c.company_shortname 
                    FROM gmc_company AS c
                    where {$datawhere} ORDER BY c.company_id ASC limit {$pagestart},{$num} ");//and c.company_status='1'
        if($itemList){
            $result = array();
            $result["list"] = $itemList;
            $res = array('error' => '0', 'errortip' => '获取集团信息', 'result' => $result);
            ajax_return($res);
        }else{
            $data=array();
            $result["list"] = $data;
            $res = array('error' => '1', 'errortip' => '暂无集团信息', 'result' => $result);
            ajax_return($res);
        }
    }

    //获取集团对应的学校信息
    function getSchoolView(){
        $request = Input('get.','','trim,addslashes');
        $DataControl = $this->DataControl;
        $datawhere = " 1 ";
        if(isset($request['keyword']) && $request['keyword'] !== ''){
            $datawhere .= " and (s.school_branch like '%{$request['keyword']}%' or s.school_shortname like '%{$request['keyword']}%' or s.school_cnname like '%{$request['keyword']}%') ";
        }
        if(isset($request['company_id']) && $request['company_id'] !== '0' && $request['company_id'] !== ''){
            $datawhere .= " and s.company_id = '{$request['company_id']}'";
        }else{
            $res = array('error' => '1', 'errortip' => '请先选择集团' );
            ajax_return($res);
        }
        if(isset($request['p']) && $request['p'] !== ''){
            $page = $request['p'];
        }else{
            $page = '1';
        }
        if(isset($request['num']) && $request['num'] !== ''){
            $num = $request['num'];
        }else{
            $num = '20';
        }
        $pagestart = ($page-1)*$num;
        $itemList = $DataControl->selectClear("SELECT s.school_id,s.school_branch,s.school_shortname,s.school_cnname FROM smc_school AS s
 where {$datawhere} ORDER BY s.school_id ASC limit {$pagestart},{$num} ");
        if($itemList){
            $result = array();
            $result["list"] = $itemList;
            $res = array('error' => '0', 'errortip' => '获取学校信息', 'result' => $result);
            ajax_return($res);
        }else{
            $data=array();
            $result["list"] = $data;
            $res = array('error' => '1', 'errortip' => '暂无学校信息', 'result' => $result);
            ajax_return($res);
        }
    }


}