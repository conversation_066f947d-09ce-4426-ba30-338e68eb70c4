<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2016/12/13
 * Time: 21:21
 */

namespace Work\Controller\Service;


class viewTpl {
    public $smarty;
    public $Static;
    public $intSession;
    public $DataControl;
    public $router;
    public $SalemanLogin=false;//管理用户
    public $variableAll;//系统变量

    public function __construct(){
        global $router;
        global $smarty;
        global $viewControl;
        //模板引擎开启
        $this->smarty = new \Smarty();
        //Session引擎开启
        $this->intSession = new \Incsession();
        //数据库操作
        $this->DataControl = new \Dbsqlplay();
        //操作类型
        $this->router = $router;

        $this->smarty->template_dir = BASEDIR.'/Work/View/Service/';
        $this->smarty->compile_dir = BASEDIR.'/Temp/Compiled/Service/';
        $this->smarty->config_dir = BASEDIR.'/Common/';
        $this->smarty->cache_dir = BASEDIR.'/Temp/Caches/';

        //指定定界符
        $this->smarty->left_delimiter="{";	//左定界符
        $this->smarty->right_delimiter="}";	//右定界符

        $this->smarty->compile_check = true;
        $this->smarty->debugging = true;

        $this->StafferLogin = false;

        $viewControl = $this->DataControl;
        $smarty = $this->smarty;
        include(ROOT_PATH . "Core/Smarty/int.class.php");

        $webUrl = "/";

        //静态资源加载
        $this->smarty->assign("CssUrl", $webUrl."Work/Static/Service/css/", true);
        $this->smarty->assign("JsUrl", $webUrl."Work/Static/Service/js/", true);
        $this->smarty->assign("ImgUrl", $webUrl."Work/Static/Service/images/", true);
        $this->smarty->assign("PluginsUrl", $webUrl."Work/Static/Service/plugins/", true);
        $this->smarty->assign("StaticUrl", IMG_PATH, true);
    }
    //检测用户是否登录 session 检测stafferLogin
    public function check_login(){
        if($this->intSession->getCookiearray('saleman') && count($this->intSession->getCookiearray('saleman')) > 0){
            $login_saleman = $this->intSession->getCookiearray('saleman');
            if(!empty($login_saleman) && $login_saleman){
                $isaleman = $this->DataControl->selectOne("select * from imc_saleman where saleman_id='{$login_saleman['saleman_id']}'");
                if(!$isaleman){
                    $this->intSession->setCookiearray("saleman",array(),'1');
                    return false;
                }else{
                    $this->SalemanLogin = $isaleman;
                    return true;
                }
            }else{
                return false;
            }
        }else{
            return false;
        }
    }

    //管理操作日志
    public function Recordweblog($module,$actiontype,$content){
        if($this->SalemanLogin){
            $date = array();
            $date['saleman_id'] = $this->SalemanLogin['saleman_id'];
            $date['userlog_module'] = $module;
            $date['userlog_type'] = $actiontype;
            $date['userlog_content'] = $content;
            $date['userlog_ip'] = real_ip();
            $date['userlog_time'] = time();
            $this->DataControl->insertData('imc_userlog',$date);
            return true;
        }else{
            return false;
        }
    }

    //工单编号
    function createCasePid($initial){
        $Str = "ABCDEFGHIJKLMNOPQRSTUVWXYZ123456789";
        $rangtr = $Str[rand(0,34)].$Str[rand(0,34)].$Str[rand(0,34)].$Str[rand(0,34)];
        $rangtime = date("ymdHis",time());
        $rangnum = rand(10000,99999);
        $OrderPID = $initial.$rangtr.$rangtime.$rangnum;
        return $OrderPID;
    }

    //登录日志记录表
    function addStafferLoginLog($company_id,$staffer_id,$loginlog_type,$loginlog_source){
        $date = array();
        $date['company_id'] = $company_id;//集团ID
        $date['staffer_id'] = $staffer_id;//员工ID
        $date['loginlog_type'] = $loginlog_type;//登录端口 0 总入口  1 集团 2 校务 3 CRM 4 教务 5 助教 6 助学
        $date['loginlog_source'] = $loginlog_source;//客户端来源 0 PC 1 手机
        $date['loginlog_lastip'] = real_ip();
        $date['loginlog_time'] = time();
        $this->DataControl->insertData('imc_staffer_loginlog',$date);
        return true;
    }

    public function display($tempview=""){
        return $this->smarty->display($tempview);
    }
    public function __call($method, $args) {
        echo "unknown method " . $method;
        return false;

    }
    //后台登录文件
    public function LoginView() {
        $this->display("login.htm");
        exit;
    }
}