<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn 
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong <PERSON>
 * Date: 2018/3/10
 * Time: 16:49
 */

namespace Work\Controller\Service;


class CaseController extends viewTpl{
    public $data;
    public $ishare;
    public $Viewhtm;
    public $isaleman;
    public $u;
    public $t;
    public $c;

    function __construct() {
        parent::__construct ();
        if(!$this->check_login()){
            $this->LoginView();
        }
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
        $this->isaleman = $this->SalemanLogin;
        $this->smarty->assign("isaleman", $this->SalemanLogin);
        $this->Viewhtm = $this->router->getController()."/".$this->router->getUrl().".htm";
    }

    //主页
    function HomeView()
    {
        $request = Input('get.','','trim,addslashes');
        $DataControl = $this->DataControl;
        $smarty = $this->smarty;

        if(!isset($request['p'])){
            $p = '1';
        }else{
            $p= $request['p'];
        }

        $datawhere = " 1 ";
        $pageurl = "/{$this->u}/{$this->t}?";

        $datatype = array();
        if(isset($request['keyword']) && $request['keyword'] !=''){
            $datawhere .= " and (A.case_title like '%{$request['keyword']}%'  )";
            $pageurl .="&keyword={$request['keyword']}";
            $datatype['keyword'] = $request['keyword'];
        }
        if(isset($request['case_state']) && $request['case_state'] !=''){
            $datawhere .= " and A.case_state = '{$request['case_state']}'";
            $pageurl .="&case_state={$request['case_state']}";
            $datatype['case_state'] = $request['case_state'];
        }
        if(isset($request['case_level']) && $request['case_level'] !=''){
            $datawhere .= " and A.case_level = '{$request['case_level']}'";
            $pageurl .="&case_level={$request['case_level']}";
            $datatype['case_level'] = $request['case_level'];
        }

        if(isset($request['company_id']) && $request['company_id'] !=''){
            $datawhere .= " and B.company_id = '{$request['company_id']}'";
            $pageurl .="&company_id={$request['company_id']}";
            $datatype['company_id'] = $request['company_id'];

            //集团
            $companyOne = $DataControl->getOne("gmc_company", "company_id='{$request['company_id']}'");
            $smarty->assign("companyOne", $companyOne);

        }
        if(isset($request['school_id']) && $request['school_id'] !=''){
            $datawhere .= " and C.school_id = '{$request['school_id']}'";
            $pageurl .="&school_id={$request['school_id']}";
            $datatype['school_id'] = $request['school_id'];

            //学校
            $schoolOne = $DataControl->getOne("smc_school", "school_id='{$request['school_id']}'");
            $smarty->assign("schoolOne", $schoolOne);
        }

        $sql = "select  A.*,B.company_code,B.company_cnname,C.school_branch,C.school_cnname 
                from imc_saleman_case as A 
                LEFT JOIN gmc_company as B ON A.company_id=B.company_id 
                LEFT JOIN smc_school as C ON A.school_id=C.school_id 
                where {$datawhere} and A.saleman_id='{$this->isaleman['saleman_id']}'";

        $db_nums = $DataControl->selectOne("select count(A.case_id) as countnums 
                from imc_saleman_case as A 
                LEFT JOIN gmc_company as B ON A.company_id=B.company_id 
                LEFT JOIN smc_school as C ON A.school_id=C.school_id 
                where {$datawhere} and A.saleman_id='{$this->isaleman['saleman_id']}'");//相关条件下的总记录数COUNT(*)
        $allnum = $db_nums['countnums'];

        $datalist = $DataControl->dbwherePage($sql,$allnum,'10',$pageurl.'&p=',$p,'10','2');

        if($datalist['cont']){
            $state = array('0'=>'待受理','1'=>'处理中','2'=>'业务流转','3'=>'已处理','4'=>'已完结','-1'=>'不能处理');
            $level = array('0'=>'初级','1'=>'中级','2'=>'高级');
            foreach ($datalist['cont'] as &$datavar){
                $datavar['case_level_name'] = $level[$datavar['case_level']];
                $datavar['case_state_name'] = $state[$datavar['case_state']];
            }
        }

        $smarty->assign("pagelist",$datalist['pages']);//筛选信息

        $smarty->assign("dataList",$datalist['cont']);
        $smarty->assign("datatype",$datatype);
    }

    function AddView(){
        $request = Input('get.','','trim,addslashes');
        $DataControl = $this->DataControl;
        $smarty = $this->smarty;

        $this->smarty->assign("act","Add");
        $this->Viewhtm = $this->router->getController()."/"."Manage.htm";
    }

    function AddAction(){
        $request = Input('post.','','trim,addslashes');
        $DataControl = $this->DataControl;
        $smarty = $this->smarty;

//            if(trim($request['article_title']) == ''){
//                ajax_return(array('error' => 1,'errortip' => "请填写文章标题!","bakfuntion"=>"warningFromTip"));
//            }
        do{
            $case_pid=$this->createCasePid('GD');
        }while($this->DataControl->selectOne("select case_pid from imc_saleman_case where case_pid='{$case_pid}' limit 0,1"));

        $data = array();
        $data['saleman_id'] = $this->isaleman['saleman_id'];
        $data['case_pid'] = $case_pid;
        $data['case_title'] = $request['case_title'];
        $data['case_content'] = $request['case_content'];
        $data['company_id'] = $request['company_id'];
        $data['school_id'] = $request['school_id'];
        $data['case_level'] = $request['case_level'];
        $data['case_cnname'] = $request['case_cnname'];
        $data['case_mobile'] = $request['case_mobile'];
        $data['case_address'] = $request['case_address'];
        $data['case_state'] = 1;
        $data['case_addtime'] = time();
        if($DataControl->insertData("imc_saleman_case",$data)){

            $dataOne = array();
            $dataOne['case_pid'] = $case_pid;
            $dataOne['dosaleman_id'] = $this->isaleman['saleman_id'];
            $dataOne['fromsaleman_id'] = $this->isaleman['saleman_id'];
            $dataOne['tracks_state'] = 1;
            $dataOne['tracks_information'] = "客服人员录入工单";
            $dataOne['tracks_playname'] = $this->isaleman['saleman_cnname'];
            $dataOne['tracks_time'] = time();
            $DataControl->insertData("imc_saleman_case_tracks",$dataOne);

            $this->Recordweblog("工单管理",$this->c,"新增工单数据");
            ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"successFromTip","bakurl"=>"/{$this->u}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "新增失败!","bakfuntion"=>"dangerFromTip"));
        }
    }

    //修改工单
    function EditView(){
        $request = Input('get.','','trim,addslashes');
        $DataControl = $this->DataControl;
        $smarty = $this->smarty;

        $usersOne = $DataControl->getOne("imc_saleman_case", "case_pid='{$request['case_pid']}'");
        $smarty->assign("dataVar", $usersOne);
        $smarty->assign("act", "Edit");

        //集团
        $companyOne = $DataControl->getOne("gmc_company", "company_id='{$usersOne['company_id']}'");
        $smarty->assign("companyOne", $companyOne);
        //学校
        $schoolOne = $DataControl->getOne("smc_school", "school_id='{$usersOne['school_id']}'");
        $smarty->assign("schoolOne", $schoolOne);

        $this->Viewhtm = $this->router->getController()."/"."Manage.htm";
    }
    //提交处理机制
    function EditAction()
    {
        $request = Input('post.','','trim,addslashes');
        $DataControl = $this->DataControl;
        $smarty = $this->smarty;

        $data = array();
        $data['case_title'] = $request['case_title'];
        $data['case_content'] = $request['case_content'];
        $data['company_id'] = $request['company_id'];
        $data['school_id'] = $request['school_id'];
        $data['case_level'] = $request['case_level'];
        $data['case_cnname'] = $request['case_cnname'];
        $data['case_mobile'] = $request['case_mobile'];
        $data['case_address'] = $request['case_address'];
        $data['case_updatetime'] = time();

        if($DataControl->updateData("imc_saleman_case","case_pid = '{$request['case_pid']}'",$data)){

            $dataOne = array();
            $dataOne['case_pid'] = $request['case_pid'];
            $dataOne['dosaleman_id'] = $this->isaleman['saleman_id'];
            $dataOne['fromsaleman_id'] = $this->isaleman['saleman_id'];
            $dataOne['tracks_information'] = "客服人员修改工单";
            $dataOne['tracks_playname'] = $this->isaleman['saleman_cnname'];
            $dataOne['tracks_time'] = time();
            $DataControl->insertData("imc_saleman_case_tracks",$dataOne);

            $this->Recordweblog("工单管理",$this->c,"编辑工单数据：case_pid为{$request['case_pid']}");
            ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"okmotify","bakurl"=>"/{$this->u}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "新增失败!","bakfuntion"=>"errormotify"));
        }
    }


    //工单跟进
    function TracksView(){
        $request = Input('get.','','trim,addslashes');
        $DataControl = $this->DataControl;
        $smarty = $this->smarty;

        $usersOne = $DataControl->getOne("imc_saleman_case", "case_pid='{$request['case_pid']}'");
        $smarty->assign("dataVar", $usersOne);

        $this->smarty->assign("act","TracksAdd");
    }
    function TracksAddAction(){
        $request = Input('post.','','trim,addslashes');
        $DataControl = $this->DataControl;
        $smarty = $this->smarty;

        $dataOne = array();
        $dataOne['case_pid'] = $request['case_pid'];
        $dataOne['dosaleman_id'] = $this->isaleman['saleman_id'];
        $dataOne['fromsaleman_id'] = $this->isaleman['saleman_id'];
        $dataOne['tracks_state'] = $request['tracks_state'];
        $dataOne['tracks_information'] = $request['tracks_information'];
        $dataOne['tracks_playname'] = $this->isaleman['saleman_cnname'];
        $dataOne['tracks_time'] = time();
        if($DataControl->insertData("imc_saleman_case_tracks",$dataOne)){
            $this->Recordweblog("工单管理->添加跟踪记录",$this->c,"添加跟踪记录：case_pid为{$request['case_pid']}");
            ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"successFromTip","bakurl"=>"/{$this->u}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "新增失败!","bakfuntion"=>"dangerFromTip"));
        }
    }

    //工单已处理标记
    function HandleAction(){
        $request = Input('get.','','trim,addslashes');
        if(isset($request['case_pid']) && $request['case_pid'] != ''){
            $dataid = $this->DataControl->updateData("imc_saleman_case","case_pid='{$request['case_pid']}'",array('case_state'=>'3','case_updatetime'=>time()));
            if($dataid){

                $dataOne = array();
                $dataOne['case_pid'] = $request['case_pid'];
                $dataOne['dosaleman_id'] = $this->isaleman['saleman_id'];
                $dataOne['fromsaleman_id'] = $this->isaleman['saleman_id'];
                $dataOne['tracks_state'] = 3;
                $dataOne['tracks_information'] = "客服人员把工单（{$request['case_pid']}）标记为已处理";
                $dataOne['tracks_playname'] = $this->isaleman['saleman_cnname'];
                $dataOne['tracks_time'] = time();
                $this->DataControl->insertData("imc_saleman_case_tracks",$dataOne);

                ajax_return(array('error' => 0,'errortip' => "标记成功!","bakfuntion"=>"refreshpage"));
            }else{
                ajax_return(array('error' => 1,'errortip' => "标记失败!","bakfuntion"=>"errormotify"));
            }
        }else{
            ajax_return(array('error' => 1,'errortip' => "参数错误!","bakfuntion"=>"errormotify"));
        }
    }

    //工单 结案标记
    function CloseAction(){
        $request = Input('get.','','trim,addslashes');
        if(isset($request['case_pid']) && $request['case_pid'] != ''){
            $dataid = $this->DataControl->updateData("imc_saleman_case","case_pid='{$request['case_pid']}'",array('case_state'=>'4','case_updatetime'=>time(),'case_exittime'=>time()));
            if($dataid){

                $dataOne = array();
                $dataOne['case_pid'] = $request['case_pid'];
                $dataOne['dosaleman_id'] = $this->isaleman['saleman_id'];
                $dataOne['fromsaleman_id'] = $this->isaleman['saleman_id'];
                $dataOne['tracks_state'] = 4;
                $dataOne['tracks_information'] = "客服人员把工单（{$request['case_pid']}）标记为已结案";
                $dataOne['tracks_playname'] = $this->isaleman['saleman_cnname'];
                $dataOne['tracks_time'] = time();
                $this->DataControl->insertData("imc_saleman_case_tracks",$dataOne);

                ajax_return(array('error' => 0,'errortip' => "标记成功!","bakfuntion"=>"refreshpage"));
            }else{
                ajax_return(array('error' => 1,'errortip' => "标记失败!","bakfuntion"=>"errormotify"));
            }
        }else{
            ajax_return(array('error' => 1,'errortip' => "参数错误!","bakfuntion"=>"errormotify"));
        }
    }

    //查看订单详情
    function LookView(){
        $request = Input('get.','','trim,addslashes');
        $DataControl = $this->DataControl;
        $smarty = $this->smarty;

        $dataOne = $DataControl->selectOne("select  A.*,B.company_code,B.company_cnname,C.school_branch,C.school_cnname 
                from imc_saleman_case as A 
                LEFT JOIN gmc_company as B ON A.company_id=B.company_id 
                LEFT JOIN smc_school as C ON A.school_id=C.school_id 
                where case_pid='{$request['case_pid']}' and A.saleman_id='{$this->isaleman['saleman_id']}'");

        $state = array('0'=>'待受理','1'=>'处理中','2'=>'业务流转','3'=>'已处理','4'=>'已完结','-1'=>'不能处理');
        $level = array('0'=>'初级','1'=>'中级','2'=>'高级');
        $dataOne['case_level_name'] = $level[$dataOne['case_level']];
        $dataOne['case_state_name'] = $state[$dataOne['case_state']];

        $tracklist = $DataControl->selectClear("select t.* from imc_saleman_case_tracks as t WHERE t.case_pid = '{$dataOne['case_pid']}'");
        if($tracklist){
            foreach ($tracklist as &$trackvar){
                $trackvar['tracks_state_name'] = $state[$trackvar['tracks_state']];
            }
        }

        $smarty->assign("dataVar", $dataOne);
        $smarty->assign("tracklist", $tracklist);

    }


    //魔术方法
    function __destruct()
    {
        if ($this->c =='Wait') {
            $this->smarty->assign("u", $this->u);
            $this->smarty->assign("t", $this->t);
            $this->display($this->Viewhtm);
            exit;
        }
    }
}