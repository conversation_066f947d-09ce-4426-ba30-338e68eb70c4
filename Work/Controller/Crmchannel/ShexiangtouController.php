<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn 
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> <PERSON><PERSON>
 * Date: 2016/12/13
 * Time: 21:06
 */
namespace Work\Controller\Crmchannel;

class ShexiangtouController extends viewTpl{
    public $data;
    public $channelname;
    public $Viewhtm;
    public $u;
    public $t;
    public $c;

    function __construct() {
        parent::__construct ();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
        $this->Viewhtm = $this->router->getController()."/".$this->router->getUrl().".htm";
    }

    //主页
    function HomeView()
    {

        $schoolstr = "'654','648','653','690','652','1198','665','681','679','678','656','650','649','685','660','693','692','689','674','671','669','2350','661','673','662','847','1199','846','2309','2308','676','2346','2369','1196','684','667','1032','1245','688','683','658','664','2342','666','677','2367','848','2320','2331'";

        $schoolList = $this->DataControl->selectClear("SELECT a.school_id,a.school_shortname,a.school_cnname
            ,(select GROUP_CONCAT(b.machine_id) from gmc_machine_monitor as b where a.school_id = b.school_id and b.machine_id > 1 ) as codejson
            FROM smc_school as a WHERE a.company_id = '8888' and a.school_id in ($schoolstr) ");

        if($schoolList) {
            $sql = "SELECT a.school_id,a.school_shortname,a.school_cnname,b.machine_id,b.machine_code,b.machine_name  
                FROM smc_school as a 
                left join gmc_machine_monitor as b ON a.school_id = b.school_id 
                WHERE a.company_id = '8888' and a.school_id in ($schoolstr) and b.machine_id > 1 
                group by b.machine_code ";
            $dataList = $this->DataControl->selectClear($sql);
            //$this->smarty->assign("dataList", $dataList);
            $machineOne = array_column($dataList, 'machine_code', 'machine_id');
            $machineTwo = array_column($dataList, 'machine_name', 'machine_id');

            foreach ($schoolList as &$schoolVar){
                $machinedata = array();
                if($schoolVar['codejson']){
                    $codedata = explode(',',$schoolVar['codejson']);
                    foreach ($codedata as $key=>$codedatavar){
                        $machinedata[$key]['code'] = $machineOne[$codedatavar];
                        $machinedata[$key]['name'] = $machineTwo[$codedatavar];
                    }
                    $schoolVar['codedata'] = $machinedata;
                }else{
                    $schoolVar['codedata'] = $machinedata;
                }
            }
            //print_r($schoolList);die;
        }

        $schooljson = json_encode($schoolList,JSON_UNESCAPED_UNICODE);
        $this->smarty->assign("schooljson", $schooljson);

        $this->Viewhtm = "shexiangtou.htm";
    }



    //魔术方法
    function __destruct()
    {
        if ($this->c =='Wait') {
            $this->smarty->assign("u", $this->u);
            $this->smarty->assign("t", $this->t);
            $this->display($this->Viewhtm);
            exit;
        }
    }
}