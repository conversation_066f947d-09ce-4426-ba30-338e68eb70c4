<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2016/12/13
 * Time: 21:21
 */

namespace Work\Controller\Crmchannel;


class viewTpl {
    public $smarty;
    public $Static;
    public $intSession;
    public $DataControl;
    public $router;
    public $ChannelLogin=false;//管理用户
    public $variableAll;//系统变量

    public function __construct(){
        global $router;
        global $smarty;
        global $viewControl;
        //模板引擎开启
        $this->smarty = new \Smarty();
        //Session引擎开启
        $this->intSession = new \Incsession();
        //数据库操作
        $this->DataControl = new \Dbsqlplay();
        //操作类型
        $this->router = $router;

        $this->smarty->template_dir = BASEDIR.'/Work/View/Crmchannel/';
        $this->smarty->compile_dir = BASEDIR.'/Temp/Compiled/Crmchannel/';
        $this->smarty->config_dir = BASEDIR.'/Common/';
        $this->smarty->cache_dir = BASEDIR.'/Temp/Caches/';

        //指定定界符
        $this->smarty->left_delimiter="{";	//左定界符
        $this->smarty->right_delimiter="}";	//右定界符

        $this->smarty->compile_check = true;
        $this->smarty->debugging = true;

        $this->StafferLogin = false;

        $viewControl = $this->DataControl;
        $smarty = $this->smarty;
        include(ROOT_PATH . "Core/Smarty/int.class.php");

        $webUrl = "/";

        //静态资源加载
        $this->smarty->assign("CssUrl", $webUrl."Work/Static/Crmchannel/css/", true);
        $this->smarty->assign("JsUrl", $webUrl."Work/Static/Crmchannel/js/", true);
        $this->smarty->assign("ImgUrl", $webUrl."Work/Static/Crmchannel/images/", true);
        $this->smarty->assign("PluginsUrl", $webUrl."Work/Static/Crmchannel/plugins/", true);
        $this->smarty->assign("StaticUrl", IMG_PATH, true);
    }
    //检测用户是否登录 session 检测stafferLogin
    public function check_login(){
        if($this->intSession->getCookiearray('channelname') && count($this->intSession->getCookiearray('channelname')) > 0){
            $login_channel = $this->intSession->getCookiearray('channelname');
            if(!empty($login_channel) && $login_channel){
                $ischannel = $this->DataControl->selectOne("select * from crm_code_channel where channel_id='{$login_channel['channel_id']}'");
                if(!$ischannel){
                    $this->intSession->setCookiearray("channelname",array(),'1');
                    return false;
                }else{
                    $this->ChannelLogin = $ischannel;
                    return true;
                }
            }else{
                return false;
            }
        }else{
            return false;
        }
    }

    //管理操作日志
    public function Recordweblog($module,$actiontype,$content){
        if($this->ChannelLogin){
            $date = array();
            $date['saleman_id'] = $this->ChannelLogin['channel_id'];
            $date['userlog_module'] = $module;
            $date['userlog_type'] = $actiontype;
            $date['userlog_content'] = $content;
            $date['userlog_ip'] = real_ip();
            $date['userlog_time'] = time();
            $this->DataControl->insertData('imc_channellog',$date);
            return true;
        }else{
            return false;
        }
    }

    //工单编号
    function createCasePid($initial){
        $Str = "ABCDEFGHIJKLMNOPQRSTUVWXYZ123456789";
        $rangtr = $Str[rand(0,34)].$Str[rand(0,34)].$Str[rand(0,34)].$Str[rand(0,34)];
        $rangtime = date("ymdHis",time());
        $rangnum = rand(10000,99999);
        $OrderPID = $initial.$rangtr.$rangtime.$rangnum;
        return $OrderPID;
    }

    public function display($tempview=""){
        return $this->smarty->display($tempview);
    }
    public function __call($method, $args) {
        echo "unknown method " . $method;
        return false;
    }
    //后台登录文件
    public function LoginView() {
        $this->display("login.htm");
        exit;
    }
}