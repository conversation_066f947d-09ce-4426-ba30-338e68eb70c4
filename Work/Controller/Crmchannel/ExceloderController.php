<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn 
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong <PERSON>
 * Date: 2018/3/10
 * Time: 16:49
 */

namespace Work\Controller\Crmchannel;


class ExceloderController extends viewTpl{
    public $data;
    public $ishare;
    public $Viewhtm;
    public $channelname;
    public $u;
    public $t;
    public $c;

    function __construct() {
        parent::__construct ();
        if(!$this->check_login()){
            $this->LoginView();
        }
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
        $this->channelname = $this->ChannelLogin;
        $this->smarty->assign("channelname", $this->ChannelLogin);
        $this->Viewhtm = $this->router->getController()."/".$this->router->getUrl().".htm";
    }

    //主页
    function HomeView()
    {
        $request = Input('get.','','trim,addslashes');
        $DataControl = $this->DataControl;
        $smarty = $this->smarty;

        if(!isset($request['p'])){
            $p = '1';
        }else{
            $p= $request['p'];
        }

        $datawhere = " 1 ";
        $pageurl = "/{$this->u}/{$this->t}?";

        $datatype = array();
        if(isset($request['keyword']) && $request['keyword'] !==''){
            $datawhere .= " and a.exceloder_pid = '{$request['keyword']}' ";
            $pageurl .="&keyword={$request['keyword']}";
            $datatype['keyword'] = $request['keyword'];
        }

        $sql = "select a.*
                from crm_channel_exceloder as a  
                where {$datawhere} 
                and a.company_id='{$this->channelname['company_id']}' 
                and a.channel_id='{$this->channelname['channel_id']}' 
                and a.exceloder_isdel = '0'
                order by a.exceloder_id desc ";

        $db_nums = $DataControl->selectOne("select count(a.exceloder_id) as countnums 
                from crm_channel_exceloder as a  
                where {$datawhere} 
                and a.company_id='{$this->channelname['company_id']}' 
                and a.channel_id='{$this->channelname['channel_id']}' 
                and a.exceloder_isdel = '0' ");//相关条件下的总记录数COUNT(*)
        $allnum = $db_nums['countnums'];

        $datalist = $DataControl->dbwherePage($sql,$allnum,'10',$pageurl.'&p=',$p,'10','2');
        $smarty->assign("pagelist",$datalist['pages']);//筛选信息

        $smarty->assign("dataList",$datalist['cont']);
        $smarty->assign("datatype",$datatype);
    }

    //导入手机号选择页面
    function ImportView(){
        $request = Input('get.','','trim,addslashes');
        $smarty = $this->smarty;
        $smarty->assign("company_id",$request['company_id']);
    }

    //导入手机号选择页面 -- 提交页面
    function ImportMobileView(){
        $request = Input('post.','','trim,addslashes');
//        if(!isset($request['company_id']) || $request['company_id']==''){
//            ajax_return(array('error' => 1, 'errortip' => "集团ID必须传", "bakfuntion" => "errormotify"));
//        }
        if($_FILES['uploadFile']['name'] == '' || $_FILES['uploadFile']['error'] == '1'){
            $PlayInfoVar = array();
            $PlayInfoVar['roster_mobile'] = '';
            $PlayInfoVar['error'] = "1";
            $PlayInfoVar['errortip'] = "文件不存在";
            $PlayInfo[] = $PlayInfoVar;
            $this->smarty->assign("PlayInfo", $PlayInfo);
            exit;
        }

        $fileType = array('xls','csv','xlsx');
        $uploadfile = new \Webfile($_FILES['uploadFile'],$files_dir='../static/file', $size = 2097152*10,$fileType);
        $up_file = $uploadfile->upload();

        $PlayInfo = array();
        if($up_file && $uploadfile->updatastatus) {
            $ExeclName = array();
            $ExeclName['手机号'] = "roster_mobile";

            $WorkerList = execl_to_array($up_file, $ExeclName);
            unset($WorkerList[0]);

            $workersList=array();

            if ($WorkerList) {
                foreach ($WorkerList as $workersVar) {
                    if ($workersVar['roster_mobile'] !== '') {
                        $workersList[] = $workersVar;
                    } else {
                        $PlayInfoVar = array();
                        $PlayInfoVar['roster_mobile'] = $workersVar['roster_mobile'];
                        $PlayInfoVar['error'] = "1";
                        $PlayInfoVar['errortip'] = "导入信息不完整";
                        $PlayInfo[] = $PlayInfoVar;
                    }
                }
            }
            if (count($workersList) > 3000) {
                ajax_return(array('error' => 1, 'errortip' => "导入数量不能大于3000!", "bakfuntion" => "errormotify"));
            }
            if($workersList) {
                //添加导入的批次
                do {
                    $exceloder_pid = getTmkBatchId();
                } while ($this->DataControl->selectOne("select exceloder_id from crm_channel_exceloder where exceloder_pid='{$exceloder_pid}'  limit 0,1"));
                $oderdata=array();
                $oderdata['company_id']=$this->channelname['company_id'];
                $oderdata['channel_id']=$this->channelname['channel_id'];
                $oderdata['exceloder_pid']=$exceloder_pid;
                $oderdata['exceloder_status']=0;
                $oderdata['exceloder_createtime']=time();
                $exceloder_id = $this->DataControl->insertData("crm_channel_exceloder",$oderdata);

                if($exceloder_id) {
                    foreach ($workersList as $workersVar) {
                        $PlayInfoVar = array();
                        $PlayInfoVar['roster_mobile'] = $workersVar['roster_mobile'];

                        if ($this->DataControl->getFieldOne("crm_channel_exceloder_roster", "roster_id", "company_id='{$this->channelname['company_id']}' and exceloder_pid='{$workersVar['exceloder_pid']}' and roster_mobile='{$workersVar['roster_mobile']}' ")) {
                            $PlayInfoVar['error'] = "1";
                            $PlayInfoVar['errortip'] = "批次名单已存在,不可重复导入";
                            $PlayInfo[] = $PlayInfoVar;
                            continue;
                        } else {
                            $data = array();
                            $data['company_id'] = $this->channelname['company_id'];
                            $data['channel_id'] = $this->channelname['channel_id'];
                            $data['exceloder_pid'] = $exceloder_pid;
                            $data['roster_mobile'] = $workersVar['roster_mobile'];
                            $data['roster_status'] = 0;
                            $data['roster_createtime'] = time();
                            if ($this->DataControl->insertData("crm_channel_exceloder_roster", $data)) {
                                $PlayInfoVar['error'] = "0";
                                $PlayInfoVar['errortip'] = "导入成功";
                            } else {
                                $PlayInfoVar['error'] = "1";
                                $PlayInfoVar['errortip'] = "导入失败";
                            }
                        }
                        $PlayInfo[] = $PlayInfoVar;
                    }
                }else{
                    $PlayInfoVar = array();
                    $PlayInfoVar['roster_mobile'] = '';
                    $PlayInfoVar['error'] = "1";
                    $PlayInfoVar['errortip'] = "批次导入失败";
                    $PlayInfo[] = $PlayInfoVar;
                }
            }else{
                $PlayInfoVar = array();
                $PlayInfoVar['roster_mobile'] = '';
                $PlayInfoVar['error'] = "1";
                $PlayInfoVar['errortip'] = "没有获得导入的数据";
                $PlayInfo[] = $PlayInfoVar;
            }
        }else{
            $PlayInfoVar = array();
            $PlayInfoVar['roster_mobile'] = '';
            $PlayInfoVar['error'] = "1";
            $PlayInfoVar['errortip'] = "文件不存在";
            $PlayInfo[] = $PlayInfoVar;
        }

        $this->Recordweblog("名单管理",$this->c,"集团:{$this->channelname['company_id']},渠道:{$this->channelname['channel_id']},导入批次导入名单");

        $this->smarty->assign("PlayInfo", $PlayInfo);
    }

    //名单列表
    function RosterView(){
        $request = Input('get.','','trim,addslashes');
        $DataControl = $this->DataControl;
        $smarty = $this->smarty;

        if(!isset($request['p'])){
            $p = '1';
        }else{
            $p= $request['p'];
        }

        $datawhere = " 1 ";
        $pageurl = "/{$this->u}/{$this->t}?";
        $datatype = array();
        if(isset($request['keyword']) && $request['keyword'] !==''){
            $datawhere .= " and (a.roster_mobile = '{$request['keyword']}')";
            $pageurl .="&keyword={$request['keyword']}";
            $datatype['keyword'] = $request['keyword'];
        }
        if(isset($request['exceloder_pid']) && $request['exceloder_pid'] !==''){
            $datawhere .= " and a.exceloder_pid = '{$request['exceloder_pid']}' ";
            $pageurl .="&exceloder_pid={$request['exceloder_pid']}";
            $datatype['exceloder_pid'] = $request['exceloder_pid'];
        }

        $sql = "select a.*,b.channel_name 
                from crm_channel_exceloder_roster as a 
                left join crm_code_channel as b on a.channel_id=b.channel_id
                where {$datawhere} and b.company_id='{$this->channelname['company_id']}' 
                order by a.roster_id desc";

        $db_nums = $DataControl->selectOne("select count(a.roster_id) as countnums
                from crm_channel_exceloder_roster as a 
                left join crm_code_channel as b on a.channel_id=b.channel_id
                where {$datawhere} and b.company_id='{$this->channelname['company_id']}' 
                order by a.roster_id desc
                ");//相关条件下的总记录数COUNT(*)
        $allnum = $db_nums['countnums'];

        $datalist = $DataControl->dbwherePage($sql,$allnum,'10',$pageurl.'&p=',$p,'10','2');
        $smarty->assign("pagelist",$datalist['pages']);//筛选信息

        $smarty->assign("dataList",$datalist['cont']);
        $smarty->assign("datatype",$datatype);
    }

    //下载名单
    function ExportRosterAction(){
        $request = Input('get.','','trim,addslashes');
        $sql = "select * 
                from crm_channel_exceloder_roster as a 
                where exceloder_pid = '{$request['exceloder_pid']}' and channel_id = '{$this->channelname['channel_id']}' and company_id = '{$this->channelname['company_id']}' and a.roster_isexceltocrm = '0' ";
        $rosterData = $this->DataControl->selectClear($sql);
        if($rosterData){
            $outexceldate = array();
            if ($rosterData) {
                foreach ($rosterData as $dateexcelvar) {
                    $datearray = array();
                    $datearray['exceloder_pid'] = $dateexcelvar['exceloder_pid'];
                    $datearray['roster_mobile'] = $dateexcelvar['roster_mobile'];
                    if($dateexcelvar['roster_status'] == '0'){
                        $datearray['roster_status'] = '待校验';
                    }elseif($dateexcelvar['roster_status'] == '1'){
                        $datearray['roster_status'] = '已完成';
                    }
                    if($dateexcelvar['roster_isexceltocrm'] == '0'){
                        $datearray['roster_isexceltocrm'] = '未撞单';
                    }elseif($dateexcelvar['roster_isexceltocrm'] == '1'){
                        $datearray['roster_isexceltocrm'] = '已撞单';
                    }
                    $datearray['roster_note'] = $dateexcelvar['roster_note'];
                    $datearray['roster_completetime'] = date("Y-m-d H:i:s",$dateexcelvar['roster_completetime']);
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = array("校验批次单号", "校验手机", "校验状态", "是否撞单", "撞单原因", "校验完成时间" );
            $excelfileds = array('exceloder_pid', 'roster_mobile', 'roster_status', "roster_isexceltocrm", 'roster_note', 'roster_completetime' );
            $tem_name = "批次:".$request['exceloder_pid']."下名单校验明细.xlsx";
            query_to_excel($excelheader, $outexceldate, $excelfileds, $tem_name);
            exit;
        }
    }

    //假删除  批次
    function delRosterAction(){
        $request = Input('get.','','trim,addslashes');
        $data = array();
        $data['exceloder_isdel'] = 1;
        $data['exceloder_deltime'] = time();
        if($this->DataControl->updateData("crm_channel_exceloder"," exceloder_pid = '{$request['exceloder_pid']}' and exceloder_id = '{$request['exceloder_id']}' ",$data)){
            $this->Recordweblog($request['site_id'],$this->Module['module_id'],$this->c,"假删除批次，ID：{$request['exceloder_id']} -- {$request['exceloder_pid']}");
            ajax_return(array('error' => 0,'errortip' => "修改成功!","bakfuntion"=>"okmotify"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "修改失败!","bakfuntion"=>"errormotify"));
        }
    }

    function checkEmail($email)
    {
        if (!preg_match("/([\w\-]+\@[\w\-]+\.[\w\-]+)/", $email)) {
            return false;
        } else {
            return true;
        }
    }

    function checkMobile($mobile)
    {
        if (!preg_match("/^(((d{3}))|(d{3}-))?((0d{2,3})|0d{2,3}-)?[1-9]d{6,8}$/", $mobile)) {
            return false;
        } else {
            return true;
        }
    }

    function createStuRandom($student_branch){
        $Str = "ABCDEFGHIJKLMNOPQRSTUVWXYZ123456789";
        $rangtr = $Str[rand(0,34)].$Str[rand(0,34)].$Str[rand(0,34)].$Str[rand(0,34)];
        $rangtime = substr(date("ymdHis",time()),2);
        $Random = $student_branch.$rangtime.$rangtr;
        return $Random;
    }

    function create_guid() {
        $charid = strtoupper(md5(uniqid(mt_rand(), true)));
        $uuid =  substr($charid, 0, 8)
            .substr($charid, 8, 4)
            .substr($charid,12, 4)
            .substr($charid,16, 4)
            .substr($charid,20,12);// "}"
        return $uuid;
    }


    //魔术方法
    function __destruct()
    {
        if ($this->c =='Wait') {
            $this->smarty->assign("u", $this->u);
            $this->smarty->assign("t", $this->t);
            $this->display($this->Viewhtm);
            exit;
        }
    }
}