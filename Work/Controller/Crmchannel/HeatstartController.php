<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn 
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/2/13
 * Time: 17:07
 */
namespace Work\Controller\Crmchannel;

class HeatstartController extends viewTpl{
    public $data;
    public $channelname;

    function __construct() {
        parent::__construct ();
    }

    //体检渠道用户
    function LoginAction(){
        $request = Input('post.','','trim,addslashes');

        $L_user=$this->DataControl->getFieldOne("crm_code_channel","company_id,channel_id,channel_name,channel_medianame,channel_tel,channel_amdpswd"," (channel_tel='{$request['L_name']}' or channel_name='{$request['L_name']}') and company_id = '8888' ");
        if($L_user){
            if($L_user['channel_tel'] && $L_user['channel_amdpswd'] == ''){
                $L_user['channel_amdpswd'] = substr($L_user['channel_tel'], -6);
                //补充缺失的密码
                $channeldata = array();
                $channeldata['channel_amdpswd'] = substr($L_user['channel_tel'], -6);
                $this->DataControl->updateData("crm_code_channel", "channel_id = '{$L_user['channel_id']}'", $channeldata);
            }
            if($L_user['channel_tel'] == '' && $L_user['channel_amdpswd'] == ''){
                $L_user['channel_amdpswd'] = 'qd2023';
                //补充缺失的密码
                $channeldata = array();
                $channeldata['channel_amdpswd'] = 'qd2023';
                $this->DataControl->updateData("crm_code_channel", "channel_id = '{$L_user['channel_id']}'", $channeldata);
            }

            $password = md5(trim($request['L_pass']));
            //判断密码
            if ($password == md5($L_user['channel_amdpswd'])) {
                $channelman = array();
                $channelman['company_id'] = $L_user['company_id'];
                $channelman['channel_id'] = $L_user['channel_id'];
                $channelman['channel_name'] = $L_user['channel_name'];
                $this->intSession->setCookiearray("channelname", $channelman, '1');

//                $this->DataControl->updateData("crm_code_channel", "channel_id = '{$L_user['channel_id']}'", array("saleman_lasttime" => time(), "saleman_lastip" => real_ip()));
                ajax_return(array('error' => 0,'errortip' => "登录成功!","bakfuntion"=>"okmotify","bakurl"=>"/Exceloder/Home"));
            } else {
                ajax_return(array('error' => 1,'errortip' => "账户密码错误，请重新登录!","bakfuntion"=>"errormotify"));
            }
        }else{
            ajax_return(array('error' => 1,'errortip' => "账户不存在，请联系管理员!","bakfuntion"=>"errormotify"));
        }
    }




    //人脸稽核核查数据处理
    function getSchoolMachoneAction(){
        $request = Input('get.','','trim,addslashes');

        $nowtime = time();
        if($request['date'] != ''){
            $nowtime = strtotime($request['date']) + ( time() - strtotime(date("Y-m-d",time())) );
        }

        $schoolstr = "'654','648','653','690','652','1198','665','681','679','678','656','650','649','685','660','693','692','689','674','671','669','2350','661','673','662','847','1199','846','2309','2308','676','2346','2369','1196','684','667','1032','1245','688','683','658','664','2342','666','677','2367','848','2320','2331'";

        $msql = " select mokelog_id as id,mokelog_img as img,FROM_UNIXTIME(mokelog_creattime) as ctime, '陌客' as type from gmc_machine_monitor_mokelog where school_id in ($schoolstr) and school_id = '{$request['school_id']}' and machine_code = '{$request['machine_code']}' and mokelog_creattime < '{$nowtime}' order by mokelog_creattime desc limit 0,1 ";
        $mokeOne = $this->DataControl->selectOne($msql);

        $isql = " select identifylog_id as id,identifylog_img as img,FROM_UNIXTIME(identifylog_creattime) as ctime,'学生' as type from gmc_machine_monitor_identifylog where school_id in ($schoolstr) and school_id = '{$request['school_id']}' and machine_code = '{$request['machine_code']}' and student_id > 1 and identifylog_creattime < '{$nowtime}'  order by identifylog_creattime desc limit 0,1 ";
        $identifyOne = $this->DataControl->selectOne($isql);

        $isql = " select identifylog_id as id,identifylog_img as img,FROM_UNIXTIME(identifylog_creattime) as ctime,'老师' as type from gmc_machine_monitor_identifylog where school_id in ($schoolstr) and school_id = '{$request['school_id']}' and machine_code = '{$request['machine_code']}' and staffer_id > 1 and identifylog_creattime < '{$nowtime}'  order by identifylog_creattime desc limit 0,1 ";
        $identifyTeaOne = $this->DataControl->selectOne($isql);

        $bsql = " select backimagelog_img as img,FROM_UNIXTIME(backimagelog_creattime) as ctime,'背景' as type from gmc_machine_monitor_backimagelog where school_id in ($schoolstr) and school_id = '{$request['school_id']}' and machine_code = '{$request['machine_code']}' and backimagelog_creattime < '{$nowtime}' order by backimagelog_creattime desc limit 0,1 ";
        $backOne = $this->DataControl->selectOne($bsql);
        if($backOne['img']){
            $backOne['img'] = $backOne['img']."?x-oss-process=image/resize,m_lfit,w_1080,limit_0/auto-orient,1/quality,q_99";
        }


        $time7 = strtotime(date('Y-m-d',($nowtime - (86400 * 6))));//加上当天，向前数六天，的零晨
//        echo $time7;die;
        $bjcount = $this->DataControl->selectOne("select count(backimagelog_id) as num from gmc_machine_monitor_backimagelog where company_id = '8888' and school_id = '{$request['school_id']}' and machine_code = '{$request['machine_code']}' and backimagelog_creattime>='{$time7}' and backimagelog_creattime<='{$nowtime}' ");

        $xycount = $this->DataControl->selectOne("select count(identifylog_id) as num from gmc_machine_monitor_identifylog where company_id = '8888' and school_id = '{$request['school_id']}' and machine_code = '{$request['machine_code']}' and student_id > 1 and identifylog_creattime>='{$time7}' and identifylog_creattime<='{$nowtime}' ");

        $teacount = $this->DataControl->selectOne("select count(identifylog_id) as num from gmc_machine_monitor_identifylog where company_id = '8888' and school_id = '{$request['school_id']}' and machine_code = '{$request['machine_code']}' and staffer_id > 1 and identifylog_creattime>='{$time7}' and identifylog_creattime<='{$nowtime}' ");

        $mkcount = $this->DataControl->selectOne("select count(mokelog_id) as num from gmc_machine_monitor_mokelog where company_id = '8888' and school_id = '{$request['school_id']}' and machine_code = '{$request['machine_code']}' and mokelog_creattime>='{$time7}' and mokelog_creattime<='{$nowtime}'");


        $result = array();
        $result['shibieimg'] = $identifyOne;//($mokeOne['ctime'] > $identifyOne['ctime'])?$mokeOne:$identifyOne;
        $result['shibieteaimg'] = $identifyTeaOne;//($mokeOne['ctime'] > $identifyOne['ctime'])?$mokeOne:$identifyOne;
        $result['shibiemkimg'] = $mokeOne;//($mokeOne['ctime'] > $identifyOne['ctime'])?$mokeOne:$identifyOne;
        $result['backimg'] = $backOne;
        $result['count']['bjcount'] = $bjcount['num'];
        $result['count']['xycount'] = $xycount['num'];
        $result['count']['teacount'] = $teacount['num'];
        $result['count']['mkcount'] = $mkcount['num'];

        ajax_return(array('error' => 0,'errortip' => "数据查询成功!","result"=>$result));
    }

    //初始化退出函数
    function outloginAction(){
        $this->intSession->setCookiearray("channelname",array(),'1');
        jslocal_spl("/");
    }

    //初始化退出函数
    function outroomAction(){
        $this->intSession->setCookiearray("channelname",array(),'1');
        jslocal_spl("/roomBook/");
    }


}