<div class="content">
    <div id="container">
        <div class="row tc cz-area f14 cz-area-diy ">
            {if $moduleList}
            {foreach from=$moduleList item=moduleVar}
            {if $moduleVar.module_styletype eq '1'}
            <div class="col-md-1 mb10 {$moduleVar.module_style}"><div class="bg-f py16"><a href="{$moduleVar.module_link}?site_id={$websites.site_id}" class="{$moduleVar.module_color}">{$moduleVar.module_name}</a></div></div>
            {else}
            <div class="col-md-1 mb10"><div class="bg-f py16"><a href="{$moduleVar.module_link}?site_id={$websites.site_id}" class="{$moduleVar.module_color}"><span class="{$moduleVar.module_style} f40"></span>{$moduleVar.module_name}</a></div></div>
            {/if}
            {/foreach}
            {/if}
        </div>
        {if $websites.site_id != '4'}
        <div class="row mt20">
            <div class="col-md-4">
                <a href="/Website/Feedback/id-{$websites.site_id}" class="c-f">
                    <div class="tiles purple mb10">
                        <div class="tiles-body">
                            <p class="f16 pt10 pb30"><span class="animate-number f30 fr" data-value="{$alltotal.feedback}" data-animation-duration="1200">0</span>未读反馈</p>
                            <p>各站点访客信息反馈统计</p>
                        </div>
                    </div>
                </a>
                <a href="/Website/Feedback/id-{$websites.site_id}" class="c-f">
                    <div class="tiles green mb10">
                        <div class="tiles-body">
                            <p class="f16 pt10 pb30"><span class="animate-number f30 fr" data-value="16" data-animation-duration="1200">0</span>未读消息</p>
                            <p>系统应用实时提示提醒消息</p>
                        </div>
                    </div>
                </a>
            </div>
            <div class="col-md-8 bg-f">
                <div class="bg-f cz-rz p18">
                    <div class="f14 pb18">操作日志</div>
                    <ul>
                        {if $weblogList}
                        {foreach from=$weblogList item=weblogVar key=key}
                        <li class="bg{$key%4+1}">
                            <div class="p18">
                                <div class="user-info mb18">
                                    <div class="img fl"><img src="{if $weblogVar.user_imghead != ''}{$weblogVar.user_imghead}{else}{$ImgUrl}imghead.png{/if}" width="35" height="35"></div>
                                    <div class="text pt5">
                                        <p class="f14">{$weblogVar.user_cnname}</p>
                                    </div>
                                    <div align="right" class="fr c-8">{$weblogVar.tb_time|date_format:"%Y-%m-%d %H:%M:%S"}</div>
                                    <div class="clear"></div>
                                </div>
                                <p>模块：{$weblogVar.module_name}，操作类型：{$weblogVar.actiontype}，备注：{$weblogVar.tb_content}</p>
                            </div>
                        </li>
                        {/foreach}
                        {/if}
                    </ul>
                </div>
            </div>
        </div>
        {/if}
    </div>
</div>