<div class="content">
    <div class="bg-f">
        <h2 class="p20"><span class="fr">
            <button type="button" class="btn btn-primary ml10 bakFromurl">返回</button></span>站点日志</h2>
        <div class="p20 f14">
            <div class="form-group">
                <form action="?" method="get" accept-charset="utf-8">
                    <input name="site_id" value="{$websites.site_id}" type="hidden">
                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tr>
                            <td width="4%">搜索：</td>
                            <td width="15%"><input class="form-control input-sm" id="inputEmail3" placeholder="请输入模块名称、操作内容" value="{$datatype['keyword']}" type="text" name="keyword"></td>
                            <td width="63%"><button type="submit" class="btn btn-primary ml10"><span class="glyphicon glyphicon-search c-f"></span> 搜索</button> </td>
                        </tr>
                    </table>
                </form>
            </div>
            <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table tc table-hover table-bordered">
                <thead>
                <tr>
                    <th>ID</th>
                    <th>模块名称</th>
                    <th>操作种类</th>
                    <th>日志</th>
                    <th>操作者</th>
                    <th>Ip</th>
                    <th>操作时间</th>
                </tr>
                </thead>
                <tbody>
                {if $dataList}
                {foreach from=$dataList item=dataVar key=key}
                <tr>
                    <td>{$key+1}</td>
                    <td>{$dataVar.module_name}</td>
                    <td>{$dataVar.actiontype}</td>
                    <td>{$dataVar.tb_content}</td>
                    <td>{$dataVar.user_cnname}</td>
                    <td>{$dataVar.tb_ip}</td>
                    <td>{$dataVar.tb_time|date_format:"%Y-%m-%d %H:%M:%S"}</td>
                </tr>
                {/foreach}
                {/if}
                </tbody>
            </table>
            <div class="pagemenu">{$pagelist}</div>
        </div>
    </div>
</div>
