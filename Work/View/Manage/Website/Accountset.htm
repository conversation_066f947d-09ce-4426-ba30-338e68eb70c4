<div class="content">
    <div id="container">
        <div class="bg-f row py20">
            <form action="/{$u}?c={$act}" role="form" method="post" enctype="application/x-www-form-urlencoded" class="AjaxForm form-horizontal">
                <h2 class="p20">用户设置<span class="fr">
                <button type="submit" class=" btn btn-success btn-cons"><i class="fa fa-check"></i>提交保存</button>
                <button type="button" class="btn btn-primary ml10 bakFromurl">返回</button></span>
                <input name="user_id" type="hidden" value="{$iuser.user_id}">
                </h2>
                <div class="col-md-6" id="Form-Box-Operating">
                    <div class="pl20 py20 f14">
                        <div class="form-group">
                            <label for="user_name" class="col-sm-2 control-label">用户名</label>
                            <div class="col-sm-4">
                                <fieldset disabled="">
                                    <input id="user_name" value="{$iuser.user_name}" type="text" class="form-control" placeholder="请输入用户名">
                                </fieldset>
                            </div>
                            <label for="user_pass" class="col-sm-2 control-label">密码</label>
                            <div class="col-sm-4">
                                <input name="user_pass" id="user_pass" type="text" class="form-control" placeholder="请输入新密码">
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="user_cnname" class="col-sm-2 control-label">中文名</label>
                            <div class="col-sm-4">
                                <input name="user_cnname" id="user_cnname" value="{$iuser.user_cnname}" type="text" class="form-control" placeholder="请输入中文名">
                            </div>
                            <label for="user_enname" class="col-sm-2 control-label">英文名</label>
                            <div class="col-sm-4">
                                <input name="user_enname" id="user_enname" value="{$iuser.user_enname}" type="text" class="form-control" placeholder="请输入英文名">
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="user_email" class="col-sm-2 control-label">邮箱</label>
                            <div class="col-sm-4">
                                <input name="user_email" id="user_email" value="{$iuser.user_email}" type="text" class="form-control" placeholder="请输入邮箱">
                            </div>
                            <label for="user_mobile" class="col-sm-2 control-label">手机</label>
                            <div class="col-sm-4">
                                <input name="user_mobile" id="user_mobile" value="{$iuser.user_mobile}" type="text" class="form-control" placeholder="请输入手机">
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="user_imghead" class="col-sm-2 control-label">图片上传</label>
                            <div class="col-sm-7">
                                <input name="user_imghead" id="user_imghead" value="{$iuser.user_imghead}" type="text" class="form-control" placeholder="先点击右侧按钮上传图片，或直接书写图片网址">
                            </div>
                            <div class="col-md-3">
                                <a data-color="rgb(255, 255, 255)" data-color-format="hex" id="cp4" class="btn btn-primary my-colorpicker-control upload-btn" data-colorpicker-guid="1">
                                    <span class="glyphicon glyphicon-floppy-open"></span>
                                    选择图片上传<input type="file" class="ipt-img-click" data-element="list_img_view" data-originalipt="user_imghead" data-thumbnailipt="user_imgheadthum"></a>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="upload-img" id="list_img_view">
                        <div class="f16 tc default {if $iuser.user_imghead}none{/if}">
                            <p class="f30"><span class="glyphicon glyphicon-picture"></span></p>
                            <p>请先上传图片后预览</p>
                        </div>
                        <div class="img set {if !$iuser.user_imghead}none{/if}"><img src="{$iuser.user_imghead}"></div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>