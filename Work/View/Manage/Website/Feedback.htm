<div class="content">
    <div class="bg-f">
        <h2 class="p20"><span class="fr">
            <button type="button" class="btn btn-primary ml10 bakFromurl">返回</button></span>站点用户反馈</h2>
        <div class="p20 f14">
            <div class="form-group">
                <form action="?" method="get" accept-charset="utf-8">
                    <input name="site_id" value="{$websites.site_id}" type="hidden">
                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tr>
                            <td width="4%">搜索：</td>
                            <td width="15%"><input class="form-control input-sm" id="inputEmail3" placeholder="请输入模块名称、操作内容" value="{$datatype['keyword']}" type="text" name="keyword"></td>
                            <td width="63%"><button type="submit" class="btn btn-primary ml10"><span class="glyphicon glyphicon-search c-f"></span> 搜索</button> </td>
                        </tr>
                    </table>
                </form>
            </div>
            <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table tc table-hover table-bordered">
                <thead>
                <tr>
                    <th>ID</th>
                    <th>留言名称</th>
                    <th>姓名</th>
                    <th>邮箱</th>
                    <th>手机</th>
                    <th>审核</th>
                    <th>操作时间</th>
                </tr>
                </thead>
                <tbody>
                {if $dataList}
                {foreach from=$dataList item=dataVar key=key}
                <tr>
                    <td>{$key+1}</td>
                    <td>{$dataVar.feedback_title}</td>
                    <td>{$dataVar.feedback_name}</td>
                    <td>{$dataVar.feedback_email}</td>
                    <td>{$dataVar.feedback_mobile}</td>
                    <td>{if $dataVar.feedback_examine == '1'}<a class="cp"><span class="glyphicon glyphicon-ok-circle f18 text-primary"></span></a>{elseif $dataVar.feedback_examine == '0'}<a class="cp"><span class="glyphicon glyphicon-remove-circle f18 text-danger"></span></a>{/if}</td>
                    <td>{$dataVar.feedback_time|date_format:"%Y-%m-%d %H:%M:%S"}</td>
                </tr>
                {/foreach}
                {/if}
                </tbody>
            </table>
            <div class="pagemenu">{$pagelist}</div>
        </div>
    </div>
</div>
