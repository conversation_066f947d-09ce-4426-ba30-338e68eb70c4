<div class="content">
    <div class="bg-f">
        <h2 class="p20">{$moduleOne.module_name}
            <span class="fr">
                <a href="javascript:;" data-tiptitle="随机TMK名单进孕育" data-url="/{$u}?c=Breedtmk"
                           class="btn btn-success btn-confirm-action mt5 btn-sm"><span class="glyphicon c-f"></span> 随机TMK名单</a>
                <a href="javascript:;" data-tiptitle="随机TMK名单进孕育" data-url="/{$u}?c=Breedsusheng"
                   class="btn btn-success btn-confirm-action mt5 btn-sm"><span class="glyphicon c-f"></span> 随机速胜名单</a>
                <a href="javascript:;" data-tiptitle="随机开心豆名单进孕育" data-url="/{$u}?c=Breedkxd"
                           class="btn btn-success btn-confirm-action mt5 btn-sm"><span class="glyphicon c-f"></span> 随机开心豆名单</a>
                <a class="btn btn-primary bakFromurl btn-sm ml10">
                    <span class="glyphicon glyphicon-share-alt  c-f"></span>
                    返回
                </a>
            </span>
        </h2>
        <div class="p20 f14">
            <div class="form-group">
                <form action="/{$u}/{$t}" method="get" accept-charset="utf-8">
                    <input name="site_id" value="{$websites.site_id}" type="hidden">
                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tr>
                            <td width="5%">搜索：</td>
                            <td width="10%" class="pr10">
                                <select name="breed_status" id="breed_status" class="form-control">
                                    <option value="">名单孕育状态</option>
                                    <option value="0" {if $datatype.breed_status == '0'}selected{/if}>待孕育</option>
                                    <option value="1" {if $datatype.breed_status == '1'}selected{/if}>孕育中</option>
                                    <option value="2" {if $datatype.breed_status == '2'}selected{/if}>跟踪中</option>
                                    <option value="3" {if $datatype.breed_status == '3'}selected{/if}>已转正</option>
                                    <option value="-1" {if $datatype.breed_status == '-1'}selected{/if}>禁孕育</option>
                                </select>
                            </td>
                            <td width="10%" class="pr10">
                                <select name="readtimes" id="readtimes" class="form-control">
                                    <option value="">短信点击状态</option>
                                    <option value="0" {if $datatype.readtimes == '0'}selected{/if}>未点击</option>
                                    <option value="1" {if $datatype.readtimes == '1'}selected{/if}>已点击</option>
                                </select>
                            </td>
                            <td width="10%" class="pr10">
                                <select name="buytimes" id="buytimes" class="form-control">
                                    <option value="">购买点击状态</option>
                                    <option value="0" {if $datatype.buytimes == '0'}selected{/if}>未点击</option>
                                    <option value="1" {if $datatype.buytimes == '1'}selected{/if}>已点击</option>
                                </select>
                            </td>
                            <td width="10%" class="pr10">
                                <select name="leavetimes" id="leavetimes" class="form-control">
                                    <option value="">OH提交状态</option>
                                    <option value="0" {if $datatype.leavetimes == '0'}selected{/if}>未点击</option>
                                    <option value="1" {if $datatype.leavetimes == '1'}selected{/if}>已点击</option>
                                </select>
                            </td>

                            <td width="30%">
                                <div class="col-md-6">
                                    <input autocomplete="off" name="starttime" id="starttime" class="form-control form_datetime" value="{$datatype['starttime']}" placeholder="开始时间{$datatype['starttime']}" type="text">
                                </div>
                                <div class="col-md-6">
                                    <input autocomplete="off" name="endtime" id="endtime" class="form-control form_datetime" value="{$datatype['endtime']}" placeholder="结束时间{$datatype['endtime']}" type="text">
                                </div>
                            </td>

                            <td width="15%" class="pr10">
                                <input name="keyword" class="form-control input-sm" placeholder="请输入客户姓名/手机" value="{$datatype['keyword']}" type="text">
                            </td>
                            <td width="25%">
                                <button type="submit" class="btn btn-primary ml10">
                                    <span class="glyphicon glyphicon-search c-f"></span> 搜索</button>
                            </td>
                            <td></td>
                        </tr>
                    </table>
                </form>
            </div>
            <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table tc table-hover table-bordered">
                <thead>
                <tr>
                    <th>ID</th>
                    <th>家长称呼</th>
                    <th>手机号码</th>
                    <th>渠道1</th>
                    <th>渠道2</th>
                    <th>孕育状态</th>
                    <th>孕育级别</th>
                    <th>短信打开时间</th>
                    <th>点击购买菜单时间</th>
                    <th>CRM留名单时间</th>
                    <th>名单创建时间</th>
                    <th>激活试卷</th>
                    <th>操作</th>
                </tr>
                </thead>
                <tbody>
                {if $dataList}
                {foreach from=$dataList item=dataVar}
                <tr id="list-{$dataVar.breed_id}">
                    <td>{$dataVar.breed_id}</td>
                    <td>{$dataVar.client_cnname}</td>
                    <td>{$dataVar.client_mobile}</td>
                    <td>{$dataVar.channel_medianame}</td>
                    <td>{$dataVar.channel_name}</td>
                    <td>{if $dataVar.breed_status eq 0}<span class="label label-default">待孕育</span>
                        {elseif $dataVar.breed_status eq 1}<span class="label label-default">孕育中</span>
                        {elseif $dataVar.breed_status eq 2}<span class="label label-success">跟踪中</span>
                        {elseif $dataVar.breed_status eq 3}<span class="label label-success">已转正</span>
                        {elseif $dataVar.breed_status eq -1}<span class="label label-warning">黑名单</span>{/if}
                    </td>
                    <td>{$dataVar.breed_level}</td>
                    <td>{if $dataVar.breed_readtimes != 0}{$dataVar.breed_readtimes|date_format:'%Y-%m-%d %H:%M:%S'}{else}---{/if}</td>
                    <td>{if $dataVar.breed_buytimes != 0}{$dataVar.breed_buytimes|date_format:'%Y-%m-%d %H:%M:%S'}{else}---{/if}</td>
                    <td>{if $dataVar.breed_leavetimes != 0}{$dataVar.breed_leavetimes|date_format:'%Y-%m-%d %H:%M:%S'}{else}---{/if}</td>
                    <td>{$dataVar.breed_createtime|date_format:'%Y-%m-%d'}</td>
                    <td>{$dataVar.breed_updatetime|date_format:'%Y-%m-%d'}</td>
                    <td>
                        {if $dataVar.breed_status == 0}
                        <a href="javascript:;" data-tiptitle="发送短信" data-url="/{$u}?c=SendSms&breed_id={$dataVar.breed_id}"
                           class="btn btn-success btn-send-action mt5 btn-sm"><span class="glyphicon  c-f"></span> 发送短信</a>
                        {/if}
                    </td>
                </tr>
                {/foreach}
                {/if}
                </tbody>
            </table>
            <div class="pagemenu">{$pagelist}</div>
        </div>
    </div>
</div>