<div class="content">
    <div id="container">
        <div class="bg-f row py20">
            <form action="/{$u}?c={$act}" method="post" enctype="application/x-www-form-urlencoded" class="AjaxForm">
                <input name="saleman_id" type="hidden" value="{$dataVar.saleman_id}">
                <input name="from" type="hidden" value="{$datatype.from}">
                <input name="site_id" type="hidden" value="{$websites.site_id}">
                <h2 class="p20">{$moduleOne.module_name}<span class="fr">
                <button type="submit" class=" btn btn-success btn-cons"><i class="fa fa-check"></i>提交保存</button>
                <button type="button" class="btn btn-primary ml10 bakFromurl">返回</button></span>
                </h2>
                <div class="col-md-8" id="Form-Box-Operating">
                    <div class="pl20 py20 f14">
                        <div class="tab-content">
                            <div class="form-group col-md-4">
                                <label for="saleman_cnname">中文名</label>
                                <input name="saleman_cnname" id="saleman_cnname" value="{$dataVar.saleman_cnname}" reg="[^ \f\n\r\t\v]" tip="请输入中文名" type="text" class="form-control" placeholder="请输入中文名">
                            </div>
                            <div class="form-group col-md-4">
                                <label for="saleman_enname">英文名</label>
                                <input name="saleman_enname" id="saleman_enname" value="{$dataVar.saleman_enname}" reg="[^ \f\n\r\t\v]"  tip="请输入英文名" type="text" class="form-control" placeholder="请输入英文名"  min="1">
                            </div>
                            <div class="form-group col-md-4">
                                <label for="saleman_jobnumber">工号</label>
                                <input name="saleman_jobnumber" id="saleman_jobnumber" value="{$dataVar.saleman_jobnumber}" reg="[^ \f\n\r\t\v]"  tip="自动生成工号" type="text" class="form-control" placeholder="自动生成工号"  min="1">
                            </div>
                            <div class="form-group col-md-4">
                                <label for="saleman_mobile">移动电话</label>
                                <input name="saleman_mobile" id="saleman_mobile" value="{$dataVar.saleman_mobile}" type="number" class="form-control" placeholder="请输入手机号"   maxlength="11">
                            </div>
                            <div class="form-group col-md-4">
                                <label for="saleman_tephone">固定联系电话</label>
                                <input name="saleman_tephone" id="saleman_tephone" value="{$dataVar.saleman_tephone}" type="text"   maxlength="11" class="form-control " placeholder="请输入固定联系电话">
                            </div>
                            <div class="form-group col-md-4">
                                <label for="saleman_pass">密码(不做修改时请勿填写)</label>
                                <input name="saleman_pass" id="saleman_pass" value="" type="password"   maxlength="11" class="form-control">
                            </div>
                            <div class="form-group col-md-4">
                                <label for="saleman_isleave">请选择在职状态</label>
                                <br>
                                <select name="saleman_isleave" id="saleman_isleave" >
                                    <option value="0" > 在职</option>
                                    <option value="1" {if $dataVar.saleman_isleave == 1}selected{/if}> 离职</option>
                                </select>
                            </div>
                            <!--<div class="form-group col-md-4">-->
                            <!--<label for="company_homeurl">企业官网</label>-->
                            <!--<input name="company_homeurl" id="company_homeurl" value="{$dataVar.company_homeurl}" type="text" class="form-control" placeholder="请输入企业官网">-->
                            <!--</div>-->
                            <!--<div class="form-group">-->
                            <!--<div class="col-md-8  mb10">-->
                            <!--<label for="company_logo">登录Logo (500px*300px)</label>-->
                            <!--<input name="company_logo" id="company_logo" value="{$dataVar.company_logo}" type="text" class="form-control" placeholder="先点击右侧按钮上传图片，或直接书写图片网址">-->
                            <!--</div>-->
                            <!--<div class="col-md-4">-->
                            <!--<label for="up_img">&nbsp;</label>-->
                            <!--<a data-color="rgb(255, 255, 255)" data-color-format="hex" id="cp4" class="btn btn-primary my-colorpicker-control upload-btn" data-colorpicker-guid="1">-->
                            <!--<span class="glyphicon glyphicon-floppy-open"></span>-->
                            <!--选择图片上传<input type="file" id="up_img" class="ipt-img-click" data-element="list_img_view" data-originalipt="company_logo" data-thumbnailipt="tb_imgthum"></a>-->
                            <!--</div>-->
                            <!--</div>-->
                            <div class="clear"></div>
                        </div>
                    </div>
                </div>
                <!--<div class="col-md-4">-->
                <!--<div class="upload-img col-md-6" id="list_img_view">-->
                <!--<div class="f16 tc default {if $dataVar.company_logo}none{/if}">-->
                <!--<p class="f30"><span class="glyphicon glyphicon-picture"></span></p>-->
                <!--<p>请先上传图片后预览</p>-->
                <!--</div>-->
                <!--<div class="img set {if !$dataVar.company_logo}none{/if}"><img src="{$dataVar.company_logo}"></div>-->
                <!--</div>-->
                <!--</div>-->
            </form>
        </div>
    </div>
</div>