<div class="content">
    <div class="bg-f">
        <h2 class="p20">{$moduleOne.module_name}
            <span class="fr">
                <a href="/{$u}/Add?site_id={$websites.site_id}" class="btn btn-success dropdown-toggle btn-demo-space">+新增数据</a>
                <a class="btn btn-primary bakFromurl ml10"><span class="glyphicon glyphicon-share-alt c-f"></span> 返回</a>
            </span>
        </h2>
        <div class="p20 f14">
            <div class="form-group">
                <form action="{$moduleOne.module_link}" method="get" accept-charset="utf-8">
                    <input name="site_id" value="{$websites.site_id}" type="hidden">
                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tr>
                            <td width="4%">搜索：</td>
                            <td width="15%"><input name="keyword" class="form-control input-sm" placeholder="请输入名称" value="{$datatype['keyword']}" type="text"></td>
                            <td width="63%"><button type="submit" class="btn btn-primary ml10"><span class="glyphicon glyphicon-search c-f"></span> 搜索</button> </td>
                        </tr>
                    </table>
                </form>
            </div>
            <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table tc table-hover table-bordered">
                <thead>
                <tr>
                    <th>ID</th>
                    <th>中文名</th>
                    <th>英文名</th>
                    <th>工号</th>
                    <th>手机号</th>
                    <th>固定电话</th>
                    <th>是否离职</th>
                    <th>创建时间</th>
                    <th>操作</th>
                </tr>
                </thead>
                <tbody>
                {if $dataList}
                {foreach from=$dataList item=dataVar}
                <tr id="list-{$dataVar.client_id}">
                    <td>{$dataVar.saleman_id}</td>
                    <td>{$dataVar.saleman_cnname}</td>
                    <td>{$dataVar.saleman_enname}</td>
                    <td>{$dataVar.saleman_jobnumber}</td>
                    <td>{$dataVar.saleman_mobile}</td>
                    <td>{$dataVar.saleman_tephone}</td>
                    <td>{if $dataVar.saleman_isleave == 1 }已离职{else}在职{/if}</td>
                    <td>{$dataVar.saleman_createtime|date_format:"%Y-%m-%d"}</td>

                    <td align="left">
                        <a href="/{$u}/Edit?id={$dataVar.saleman_id}&site_id={$websites.site_id}" class="btn btn-primary btn-sm">
                            <span class="glyphicon glyphicon-pencil c-f"></span> 编辑</a>

                        <!--<a href="javascript:;" data-element="list-{$dataVar.client_id}" data-url="/{$u}?c=Del&id={$dataVar.client_id}" class="btn btn-danger btn-sm btn-del-action">-->
                            <!--<span class="glyphicon glyphicon-remove c-f"></span> 删除</a>-->

                    </td>
                </tr>
                {/foreach}
                {/if}
                </tbody>
            </table>
            <div class="pagemenu">{$pagelist}</div>
        </div>
    </div>
</div>