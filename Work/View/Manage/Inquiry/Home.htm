<div class="content">
    <div class="bg-f">
        <h2 class="p20">{$moduleOne.module_name}
            <span class="fr">
                <!--<a href="/{$u}/Add?site_id={$websites.site_id}" class="btn btn-success dropdown-toggle btn-demo-space">+新增数据</a>-->
                <a class="btn btn-primary bakFromurl ml10"><span class="glyphicon glyphicon-share-alt c-f"></span> 返回</a>
            </span>
        </h2>
        <div class="p20 f14">
            <div class="form-group">
                <form action="{$moduleOne.module_link}" method="get" accept-charset="utf-8">
                    <input name="site_id" value="{$websites.site_id}" type="hidden">
                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tr>
                            <td width="4%">搜索：</td>
                            <td width="15%"><input name="keyword" class="form-control input-sm" placeholder="请输入采购商名称/编号" value="{$datatype['keyword']}" type="text"></td>
                            <td width="63%"><button type="submit" class="btn btn-primary ml10"><span class="glyphicon glyphicon-search c-f"></span> 搜索</button> </td>
                        </tr>
                    </table>
                </form>
            </div>
            <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table tc table-hover table-bordered">
                <thead>
                <tr>
                    <th>ID</th>
                    <th>询价单编号</th>
                    <th>采购商</th>
                    <th>子账号id</th>
                    <th>操作</th>
                </tr>
                </thead>
                <tbody>
                {if $dataList}
                {foreach from=$dataList item=dataVar}
                <tr id="list-{$dataVar.inquiry_id}">
                    <td>{$dataVar.inquiry_id}</td>
                    <td>{$dataVar.inquiry_pid}</td>
                    <td>{$dataVar.pur_companyname}</td>
                    <td>{$dataVar.purchaser_junior_id}</td>
                    <td align="left">
                        <a href="/{$u}/Detail?inquiry_pid={$dataVar.inquiry_pid}&site_id={$websites.site_id}" class="btn btn-primary btn-sm">
                            <span class="glyphicon glyphicon-leaf c-f"></span> 询价单明细</a>
                        <a href="javascript:;" data-element="list-{$dataVar.inquiry_id}" data-url="/{$u}?c=Del&id={$dataVar.inquiry_id}&site_id={$websites.site_id}" class="btn btn-danger btn-sm btn-del-action">
                        <span class="glyphicon glyphicon-remove c-f"></span> 删除</a>
                    </td>
                </tr>
                {/foreach}
                {/if}
                </tbody>
            </table>
            <div class="pagemenu">{$pagelist}</div>
        </div>
    </div>
</div>