<div class="content">
    <div class="bg-f">
        <h2 class="p20">{$moduleOne.module_name}
            <span class="fr">
                <a href="/{$u}/Add?site_id={$websites.site_id}" class="btn btn-success dropdown-toggle btn-demo-space">+新增数据</a>
                <a class="btn btn-primary bakFromurl ml10"><span class="glyphicon glyphicon-share-alt c-f"></span> 返回</a>
            </span>
        </h2>
        <div class="p20 f14">
            <div class="form-group">
                <form action="{$moduleOne.module_link}" method="get" accept-charset="utf-8">
                    <input name="site_id" value="{$websites.site_id}" type="hidden">
                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tr>
                            <td width="4%">搜索：</td>
                            <td width="15%"><input name="keyword" class="form-control input-sm" placeholder="请输入客户名称" value="{$datatype['keyword']}" type="text"></td>
                            <td width="63%"><button type="submit" class="btn btn-primary ml10"><span class="glyphicon glyphicon-search c-f"></span> 搜索</button> </td>
                        </tr>
                    </table>
                </form>
            </div>
            <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table tc table-hover table-bordered">
                <thead>
                <tr>
                    <th>ID</th>
                    <th>首字母</th>
                    <th>客户名称</th>
                    <th>联系方式</th>
                    <th>更新时间</th>
                    <th>客户状态</th>
                    <th>操作</th>
                </tr>
                </thead>
                <tbody>
                {if $dataList}
                {foreach from=$dataList item=dataVar}
                <tr id="list-{$dataVar.client_id}">
                    <td>{$dataVar.client_id}</td>
                    <td>{$dataVar.company_sname}</td>
                    <td>{$dataVar.company_name}</td>
                    <td>{$dataVar.mobile}</td>
                    <td>{$dataVar.amend_time|date_format:"%Y-%m-%d"}</td>
                    <td>{if $dataVar.trace_type eq '1'}已签约{elseif $dataVar.trace_type eq '-1'}已失效{elseif $dataVar.trace_type eq '0'}跟踪中{/if}</td>
                    <td align="left">
                        {if $iuser.user_limitsinc =='1'}
                        <a href="/{$u}/Edit?id={$dataVar.client_id}&site_id={$websites.site_id}" class="btn btn-primary btn-sm">
                            <span class="glyphicon glyphicon-pencil c-f"></span> 编辑</a>
                        {/if}
                        <a href="/Crmitem?client_id={$dataVar.client_id}" class="btn btn-primary btn-sm">
                            <span class="glyphicon glyphicon-leaf c-f"></span> 客户项目</a>
                        <a href="/Tracktrace?client_id={$dataVar.client_id}" class="btn btn-primary btn-sm">
                            <span class="glyphicon glyphicon-phone c-f"></span> 咨询跟踪</a>
                        <a href="/Workorder?client_id={$dataVar.client_id}"  class="btn btn-primary btn-sm">
                            <span class="icon-question-sign c-f"></span> 客户工单</a>
                        {if $iuser.user_limitsinc =='1'}
                        <a href="/Crmcontacts?client_id={$dataVar.client_id}" class="btn btn-primary btn-sm">
                            <span class="glyphicon glyphicon-phone c-f"></span> 客户联系人</a>
                        <a href="javascript:;" data-element="list-{$dataVar.client_id}" data-url="/{$u}?c=Del&id={$dataVar.client_id}" class="btn btn-danger btn-sm btn-del-action">
                        <span class="glyphicon glyphicon-remove c-f"></span> 删除</a>
                        {/if}
                    </td>
                </tr>
                {/foreach}
                {/if}
                </tbody>
            </table>
            <div class="pagemenu">{$pagelist}</div>
        </div>
    </div>
</div>