<div class="content">
    <div id="container">
        <div class="bg-f row py20">
            <form action="/{$u}?c={$act}" method="post" enctype="application/x-www-form-urlencoded" class="AjaxForm">
                <input name="client_id" type="hidden" value="{$dataVar.client_id}">
                <input name="site_id" type="hidden" value="{$websites.site_id}">
                <h2 class="p20">{$moduleOne.module_name}<span class="fr">
                <button type="submit" class=" btn btn-success btn-cons"><i class="fa fa-check"></i>提交保存</button>
                <button type="button" class="btn btn-primary ml10 bakFromurl">返回</button></span>
                </h2>
                <div class="col-md-6" id="Form-Box-Operating">
                    <div class="pl20 py20 f14 col-md-12">
                        <div class="form-group col-md-9">
                            <label for="company_name">客户名称</label>
                            <input name="company_name" id="company_name" value="{$dataVar.company_name}" type="text" class="form-control" placeholder="请输入客户名称">
                        </div>
                        <div class="form-group col-md-3">
                            <label for="type">客户类型</label>
                            <select name="type" id="type" class="form-control">
                                <option value="1" {if $dataVar.type == '1'}selected{/if}>线上客户</option>
                                <option value="2" {if $dataVar.type == '2'}selected{/if}>线下客户</option>
                                <option value="3" {if $dataVar.type == '3'}selected{/if}>友情客户</option>
                                <option value="4" {if $dataVar.type == '4'}selected{/if}>内推客户</option>
                            </select>
                        </div>
                        <div class="form-group col-md-4">
                            <label for="employee_id">客户负责人</label>
                            <select name="employee_id" id="employee_id" class="form-control">
                                {if $employeeList}
                                {foreach from=$employeeList item=Tbvar key=key}
                                <option value="{$Tbvar.employee_id}" {if $Tbvar.employee_id == $dataVar.employee_id}selected{/if}>{$Tbvar.cnname}</option>
                                {/foreach}
                                {/if}
                            </select>
                        </div>
                        <div class="form-group col-md-4">
                            <label for="trace_type">客户状态</label>
                            <select name="trace_type" id="trace_type" class="form-control">
                                <option value="0" {if $dataVar.trace_type == '0'}selected{/if}>跟踪中</option>
                                <option value="1" {if $dataVar.trace_type == '1'}selected{/if}>已签约</option>
                                <option value="2" {if $dataVar.trace_type == '-1'}selected{/if}>已失效</option>
                            </select>
                        </div>
                        <div class="form-group col-md-4">
                            <label for="trace_time">跟踪时间</label>
                            <input name="trace_time" id="trace_time" value="{$dataVar.trace_time}" type="text" class="form-control form_datetime" placeholder="请输入跟进时间">
                        </div>
                        <div class="form-group col-md-4">
                            <label for="mobile">联系手机</label>
                            <input name="mobile" id="mobile" value="{$dataVar.mobile}" type="text" class="form-control" placeholder="请输入联系手机或账户名">
                        </div>
                        <div class="form-group col-md-4">
                            <label for="password">登录密码</label>
                            <input name="password" id="password" type="text" class="form-control" placeholder="请输入更新密码">
                        </div>
                        <div class="form-group col-md-4">
                            <label for="phone">电话</label>
                            <input name="phone" id="phone" value="{$dataVar.phone}" type="text" class="form-control " placeholder="请输入电话">
                        </div>
                        <div class="form-group col-md-4">
                            <label for="fax">传真</label>
                            <input name="fax" id="fax" value="{$dataVar.fax}" type="text" class="form-control" placeholder="请输入传真号">
                        </div>   
                        <div class="form-group col-md-8">
                            <label for="address">地址</label>
                            <input name="address" id="address" value="{$dataVar.address}" type="text" class="form-control" placeholder="请输入地址">
                        </div>                                              
                        <div class="form-group">
                            <div class="col-md-9  mb10">
                                <label for="client_img">头像上传 (300px*300px)</label>
                                <input name="client_img" id="client_img" value="{$dataVar.client_img}" type="text" class="form-control" placeholder="先点击右侧按钮上传图片，或直接书写图片网址">
                            </div>
                            <div class="col-md-3">
                                <label for="up_img">&nbsp;</label>
                                <a data-color="rgb(255, 255, 255)" data-color-format="hex" id="cp4" class="btn btn-primary my-colorpicker-control upload-btn" data-colorpicker-guid="1">
                                    <span class="glyphicon glyphicon-floppy-open"></span>
                                    选择图片上传<input type="file" id="up_img" class="ipt-img-click" data-element="list_img_view" data-originalipt="client_img" data-thumbnailipt="tb_imgthum"></a>
                            </div>
                        </div>
                        <div class="form-group col-md-12">
                            <label for="qeditor_body">备注</label>
                            <div class="control-box  debug-gray">
                                <div class="box-editor">
                                    <textarea name="remark" id="qeditor_body" class="textarea qeditor" rows="3">{$dataVar.remark}</textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="upload-img" id="list_img_view">
                        <div class="f16 tc default {if $dataVar.client_img}none{/if}">
                            <p class="f30"><span class="glyphicon glyphicon-picture"></span></p>
                            <p>请先上传图片后预览</p>
                        </div>
                        <div class="img set {if !$dataVar.client_img}none{/if}"><img src="{$dataVar.client_img}"></div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>