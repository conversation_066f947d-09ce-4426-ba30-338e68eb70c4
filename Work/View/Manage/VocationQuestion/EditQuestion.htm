<div class="content">
    <div id="container">
        <div class="bg-f row p20">
            <ul class="nav nav-tabs">
                <li class="active">
                    <a href="/{$u}/{$t}?category_id=1&site_id={$websites.site_id}" >选择题</a>
                </li>
            </ul>
            <div class="tab-content">
                <div class="tab-pane fade in active">
                    <form action="/{$u}?c=EditQuestion" role="form" method="post" enctype="application/x-www-form-urlencoded" class="AjaxForm">
                        <input name="question_id" type="hidden" value="{$dataVar.question_id}">
                        <input name="site_id" type="hidden" value="{$websites.site_id}">
                        <h2 class="p20">{$moduleOne.module_name}--编辑试题<span class="fr">
                            <button type="submit" class=" btn btn-success btn-cons"><i class="fa fa-check"></i>提交保存</button>
                            <button type="button" class="btn btn-primary ml10 bakFromurl">返回</button></span>
                        </h2>
                        <div class="row">
                            <div class="col-md-5" id="Form-Box-Operating">
                                <div class="pl20 py20 f14 col-md-12">
                                    <div class="form-group col-md-12">
                                        <label for="question_title">题目标题</label>
                                        <input name="question_title" id="question_title" value="{$dataVar.question_title}" type="text" class="form-control" placeholder="请输入题目标题">
                                    </div>

                                    <div class="form-group col-md-6">
                                        <label for="question_pid">题目编号</label>
                                        <input name="question_pid" id="question_pid" value="{$dataVar.question_pid}" type="text" class="form-control" placeholder="请输入题目编号">
                                    </div>

                                    <div class="form-group col-md-6">
                                        <label for="question_correct">正确答案储存</label>
                                        <input name="question_correct" id="question_correct" value="{$dataVar.question_correct}" type="text" class="form-control" placeholder="请输入正确答案:A">
                                    </div>

                                    <div class="form-group col-md-6">
                                        <label for="question_time">考试时间（分钟）</label>
                                        <input name="question_time" id="question_time" value="{$dataVar.question_time}" type="text" class="form-control" placeholder="请输入考试时间（分钟）">
                                    </div>

                                    <div class="form-group col-md-6">
                                        <label for="stage_id">职业阶段</label>
                                        <select name="stage_id" id="stage_id" class="form-control">
                                            <option value="0">请选择年级</option>
                                            {if $stageList}
                                            {foreach from=$stageList item=tbvar}
                                            <option value="{$tbvar.stage_id}" {if $dataVar.stage_id eq $tbvar.stage_id} selected{/if}>{$tbvar.stage_cnname}</option>
                                            {/foreach}
                                            {/if}
                                        </select>
                                    </div>

                                    <!--<div class="form-group col-md-6">-->
                                        <!--<label for="question_issoldout">题目是否下架</label>-->
                                        <!--<select name="question_issoldout" id="question_issoldout" class="form-control">-->
                                            <!--<option value="0">否</option>-->
                                            <!--<option value="1">是</option>-->
                                        <!--</select>-->
                                    <!--</div>-->

                                    <div class="form-group col-md-12">
                                        <label for="question_content">题目内容   (#)为下划线</label>
                                        <textarea name="question_content" id="question_content" class="form-control" placeholder="请输入题目内容">{$dataVar.question_content}</textarea>
                                    </div>

                                    <div class="form-group col-md-12">
                                        <label>题目解析</label>
                                        <textarea name="question_analysis" class="form-control" rows="5">{$dataVar.question_analysis}</textarea>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-7">
                                <div class="alert alert-warning alert-dismissible mt5" role="alert">
                                    <span class="glyphicon glyphicon-exclamation-sign f16 vam mr6"></span>
                                    警告！各选项文字不可超过30个字，选项至少填写2个，最多填写4个，如无对应选项可不填！
                                </div>
                                <div id="operateType12">
                                    {foreach from=$answer item=data key='k'}
                                    <div class="upload-img col-md-3 {if $k>1}extend{/if} {if $k>3}none{/if}" style="min-height: 50px; padding: 2px;">
                                        {if $k>1}<a href="javascript:;" class="close">&times;</a>{/if}
                                        <input name="answers_optionname[]" value="{$menu.$k}" type="hidden">
                                        <input name="answers_sort[]" value="{$k+1}" type="hidden">
                                        <div class="form-group col-md-12">
                                            <label>选项 {$menu.$k}</label>
                                            <input name="answers_option[]" type="text" value="{$data.answers_option}" class="form-control" placeholder="请输入选项文本">
                                        </div>
                                    </div>
                                    {/foreach}
                                    <div class="upload-img col-md-3 add" style="min-height: 50px; padding: 2px;">
                                        <div class="form-group col-md-12">
                                            <label>添加选项</label>
                                            <div>
                                                <input class="action" type="button" value="Add" style="width: 50%;height: 37px;">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                    <div class="clear"></div>
                </div>
            </div>
        </div>
    </div>
</div>