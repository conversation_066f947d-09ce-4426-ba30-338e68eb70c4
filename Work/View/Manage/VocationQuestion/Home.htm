<div class="content">
    <div class="bg-f">
        <h2 class="p20">
            <span class="fr">
                <a href="/{$u}/Add?site_id={$websites.site_id}" class="btn btn-success dropdown-toggle btn-demo-space">+新增资料</a>
                <button type="button" class="btn btn-primary ml10 bakFromurl">返回</button>
            </span>{$moduleOne.module_name}</h2>
        <div class="p20 f14">
            <div class="form-group">
                <form action="/{$u}" method="get" accept-charset="utf-8">
                    <input name="site_id" value="{$websites.site_id}" type="hidden">
                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tr>
                            <td width="4%">职业阶段：</td>
                            <td width="15%">
                                <select name="stage_id" class="form-control">
                                    <option value="">选择职业阶段</option>
                                    {if $stageList}
                                    {foreach from=$stageList item=tbvar}
                                    <option value="{$tbvar.stage_id}" {if $datatype.stage_id eq $tbvar.stage_id}selected{/if}>{$tbvar.stage_cnname}</option>
                                    {/foreach}
                                    {/if}
                                </select>
                            </td>
                            <td width="4%">搜索：</td>
                            <td width="15%"><input class="form-control input-sm" id="inputEmail3" placeholder="试题ID/编号/标题" value="{$datatype['keyword']}" type="text" name="keyword"></td>
                            <td width="63%"><button type="submit" class="btn btn-primary ml10"><span class="glyphicon glyphicon-search c-f"></span> 搜索</button> </td>
                        </tr>
                    </table>
                </form>
            </div>

            <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table tc table-hover table-bordered">
                <thead>
                <tr>
                    <th>试题ID</th>
                    <th>试题编号</th>
                    <th>所属职业阶段</th>
                    <th width="40%">试题标题</th>
                    <!--<th width="15%">题目内容</th>-->
                    <th>考试时间（分钟）</th>
                    <th>正确答案储存</th>
                    <th>选项数量</th>
                    <!--<th>题目是否下架</th>-->
                    <th>操作</th>
                </tr>
                </thead>
                <tbody>
                {if $dataList}
                {foreach from=$dataList item=dataVar}
                <tr id="list-{$dataVar.question_id}">
                    <td>{$dataVar.question_id}</td>
                    <td>{$dataVar.question_pid}</td>
                    <td>
                        {if $stageList}
                        {foreach from=$stageList item=tbvar}
                        {if $tbvar.stage_id eq $dataVar.stage_id}{$tbvar.stage_cnname}{/if}
                        {/foreach}
                        {/if}
                    </td>
                    <td>{$dataVar.question_title}</td>
                    <!--<td>{$dataVar.question_content}</td>-->
                    <td>{$dataVar.question_time}</td>
                    <td>{$dataVar.question_correct}</td>
                    <td>{$dataVar.num}</td>
                    <!--<td>-->
                        <!--{if $dataVar.question_issoldout eq 0}-->
                        <!--<span class="glyphicon glyphicon-remove-circle f18 text-danger"></span>-->
                        <!--{elseif $dataVar.question_issoldout eq 1}-->
                        <!--<span class="glyphicon glyphicon-ok-circle f18 text-primary"></span>-->
                        <!--{/if}-->
                    <!--</td>-->
                    <td>
                        <a href="/{$u}/EditQuestion?question_id={$dataVar.question_id}&site_id={$websites.site_id}" class="btn btn-primary btn-sm"><span class="glyphicon glyphicon-pencil c-f"></span> 编辑</a>
                        <a  href="javascript:;" data-element="list-{$dataVar.question_id}" data-url="/{$u}?c=Del&id={$dataVar.question_id}" class="btn btn-danger btn-sm btn-del-action"><span class="glyphicon glyphicon-remove c-f"></span> 删除</a>
                    </td>
                </tr>
                {/foreach}
                {/if}
                </tbody>
            </table>
            <div class="pagemenu">{$pagelist}</div>
        </div>
    </div>
</div>
