<div class="content">
    <div class="bg-f">
        <h2 class="p20">{$moduleOne.module_name}
            <span class="fr">
                <a href="/{$u}?c=Export&site_id={$websites.site_id}" class="btn btn-success dropdown-toggle btn-demo-space">导出统计数据</a>
                <a class="btn btn-primary bakFromurl ml10"><span class="glyphicon glyphicon-share-alt c-f"></span> 返回</a>
            </span>
        </h2>
        <div class="p20 f14">
            <div class="form-group">
                <form action="{$moduleOne.module_link}" method="get" accept-charset="utf-8">
                    <input name="site_id" value="{$websites.site_id}" type="hidden">
                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tr>
                            <td width="4%">搜索：</td>
                            <td width="15%">
                                <input name="keyword" class="form-control input-sm" placeholder="请输入企业名称/编号 " value="{$datatype['keyword']}" type="text">
                            </td>
                            <td width="63%"><button type="submit" class="btn btn-primary ml10"><span class="glyphicon glyphicon-search c-f"></span> 搜索</button> </td>
                        </tr>
                    </table>
                </form>
            </div>
            <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table tc table-hover table-bordered">
                <thead>
                <tr>
                    <th>集团ID</th>
                    <th>集团名称</th>
                    <th>集团编号</th>
                    <th>管理员密码</th>
                    <th>在线时长（分钟）</th>
                    <th>上课时长（分钟）</th>
                    <th>累计充值金额</th>
                    <th>累计消费金额</th>
                    <th>账户余额</th>
                </tr>
                </thead>
                <tbody>
                {if $dataList}
                {foreach from=$dataList item=dataVar}
                <tr id="list-{$dataVar.company_id}">
                    <td>{$dataVar.company_id}</td>
                    <td>{$dataVar.company_cnname}</td>
                    <td>{$dataVar.company_code}</td>
                    <td>{$dataVar.staffer_bakpass}</td>
                    <td>{$dataVar.duration}</td>
                    <td>{$dataVar.paytime}</td>
                    <td>{$dataVar.leijicz}</td>
                    <td>{$dataVar.leijixf}</td>
                    <td>{$dataVar.zhanghuye}</td>
                </tr>
                {/foreach}
                {/if}
                </tbody>
            </table>
            <div class="pagemenu">{$pagelist}</div>
        </div>
    </div>
</div>