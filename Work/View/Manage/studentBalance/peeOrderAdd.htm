<div class="content">
    <div id="container">
        <div class="bg-f row py20">
            <form action="/{$u}?c={$act}" method="post" enctype="application/x-www-form-urlencoded" class="AjaxForm">
                <input name="companies_id" type="hidden" value="{$balanceOne.companies_id}">
                <input name="student_id" type="hidden" value="{$balanceOne.student_id}">
                <input name="school_id" type="hidden" value="{$balanceOne.school_id}">
                <input name="site_id" type="hidden" value="{$websites.site_id}">
                <h2 class="p20">{$moduleOne.module_name}<span class="fr">
                <button type="submit" class=" btn btn-success btn-cons"><i class="fa fa-check"></i>提交保存</button>
                <button type="button" class="btn btn-primary ml10 bakFromurl">返回</button></span>
                </h2>
                <div class="col-md-4" id="Form-Box-Operating">
                    <div class="pl20 py20 f14">
                        <div class="tab-content">
                            <div class="form-group col-md-12">
                                <label for="companies_cnname">主体名称</label>
                                <input id="companies_cnname" value="{$companiesOne.companies_cnname}" type="text" class="form-control">
                            </div>
                            <div class="form-group col-md-6">
                                <label for="school_branch">校区编号</label>
                                <input id="school_branch" value="{$schoolOne.school_branch}" type="text" class="form-control">
                            </div>
                            <div class="form-group col-md-6">
                                <label for="school_cnname">校区名称</label>
                                <input id="school_cnname" value="{$schoolOne.school_cnname}" type="text" class="form-control">
                            </div>
                            <div class="form-group col-md-6">
                                <label for="student_branch">学生编号</label>
                                <input id="student_branch" value="{$studentOne.student_branch}" type="text" class="form-control">
                            </div>
                            <div class="form-group col-md-6">
                                <label for="student_cnname">学生名称</label>
                                <input id="student_cnname" value="{$studentOne.student_cnname}" type="text" class="form-control">
                            </div>
                            <div class="form-group col-md-12">
                                <label for="student_balance">学生账户余额</label>
                                <input name="student_balance" id="student_balance" value="{$balanceOne.student_balance}" type="text" class="form-control">
                            </div>
                            <div class="form-group col-md-12">
                                <label for="order_note">课程下单备注</label>
                                <input name="order_note" id="order_note" value="" reg="[^ \f\n\r\t\v]" tip="请填写课程下单备注" type="text" class="form-control" placeholder="请输入课程下单备注">
                            </div>
                            <div class="clear"></div>
                        </div>
                    </div>
                </div>

                <div class="col-md-8">
                    <h2 class="p20">课程制单明细</h2>
                    <table class="table tc table-hover table-bordered" border="0" cellspacing="0" cellpadding="0">
                        <thead>
                        <tr>
                            <th width="20%">课程代码</th>
<!--                            <th width="20%">定价序号</th>-->
                            <th width="15%">课时次数</th>
                            <th width="15%">课时金额</th>
                            <th width="5%">操作</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr class="tablelist">
                            <td align="center" class="control-box">
                                <input type="text" name="courseList[]" class="box-xs-9 ipt-put" placeholder="请填写课程别编号"></td>
<!--                            <td align="center" class="control-box">-->
<!--                                <input type="text" name="parameter[]" class="box-xs-9 ipt-put" placeholder="请填写参数"></td>-->
                            <td align="center" class="control-box">
                                <input type="text" name="numberList[]" class="box-xs-9 ipt-put" placeholder="请填写购买数量"></td>
                            <td align="center" class="control-box">
                                <input type="text" name="priceList[]" class="box-xs-9 ipt-put" placeholder="请填写价格"></td>
                            <td align="center" class="control-box">
                                <a href="javascript:;" class="copyTableToNext">[+]</a>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </form>
        </div>
    </div>
</div>