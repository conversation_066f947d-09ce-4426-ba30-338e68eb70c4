<div class="content">
    <div class="bg-f">
        <h2 class="p20">{$moduleOne.module_name}
            <span class="fr">
                <button type="button" class="btn btn-primary ml10 bakFromurl">返回</button>
            </span>
        </h2>
        <div class="p20 f14">
            <div class="form-group">
                <form action="?" method="get" accept-charset="utf-8">
                    <input name="site_id" type="hidden" value="{$websites.site_id}">
                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tr>
                            <td width="4%">搜索：</td>
                            <td width="15%"><input class="form-control input-sm" id="inputEmail3" placeholder="请输入推荐校区名称/学生编号/名称" value="{$datatype['keyword']}" type="text" name="keyword"></td>
                            <td width="63%"><button type="submit" class="btn btn-primary ml10"><span class="glyphicon glyphicon-search c-f"></span> 搜索</button>
                        </tr>
                    </table>
                </form>
            </div>
            <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table tc table-hover table-bordered">
                <thead>
                <tr>
                    <th>序号</th>
                    <th>主体名称</th>
                    <th>校区编号</th>
                    <th>校区名称</th>
                    <th>学生名称</th>
                    <th>学生编号</th>
                    <th>学生余额</th>
                    <th>操作</th>
                </tr>
                </thead>
                <tbody>
                {if $dataList}
                {foreach from=$dataList item=dataVar key=key}
                <tr id="list-{$dataVar.student_id}">
                    <td>{$dataVar.student_id}</td>
                    <td>{$dataVar.companies_cnname}</td>
                    <td>{$dataVar.school_branch}</td>
                    <td>{$dataVar.school_cnname}</td>
                    <td>{$dataVar.student_cnname}</td>
                    <td>{$dataVar.student_branch}</td>
                    <td>{$dataVar.student_balance}</td>
                    <td>
                        <a href="/studentBalance/peeOrderAdd?school_id={$dataVar.school_id}&companies_id={$dataVar.companies_id}&student_id={$dataVar.student_id}&site_id={$websites.site_id}&from=company" class="btn btn-primary btn-sm">
                            <span class="glyphicon glyphicon-list-alt c-f"></span> 快速课程制单</a>
                        </td>
                    </tr>
                    {/foreach}
                    {/if}
                    </tbody>
                </table>
                <div class="pagemenu">{$pagelist}</div>
            </div>
        </div>
    </div>