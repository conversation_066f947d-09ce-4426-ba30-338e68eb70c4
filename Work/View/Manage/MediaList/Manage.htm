<div class="content">
    <div id="container">
        <div class="bg-f row py20">
            <form action="/{$u}?c={$act}" role="form" method="post" enctype="application/x-www-form-urlencoded" class="AjaxForm">
                <input name="list_id" type="hidden" value="{$dataVar.list_id}">
                <input name="site_id" type="hidden" value="{$websites.site_id}">
                <h2 class="p20">{$moduleOne.module_name}<span class="fr">
                <button type="submit" class=" btn btn-success btn-cons"><i class="fa fa-check"></i>提交保存</button>
                <button type="button" class="btn btn-primary ml10 bakFromurl">返回</button></span>
                </h2>
                <div class="col-md-6" id="Form-Box-Operating">
                    <div class="pl20 py20 f14 col-md-12">
                        <div class="form-group col-md-12">
                            <label for="list_title">广告名称</label>
                            <input name="list_title" id="list_title" value="{$dataVar.list_title}" type="text" class="form-control" placeholder="请输入广告名称">
                        </div>
                        <div class="form-group col-md-8">
                            <label for="place_id">所属广告位</label>
                            <select name="place_id" id="place_id" class="form-control">
                                {foreach from=$placeList item=placeVar}
                                <option value="{$placeVar.place_id}">{$placeVar.site_title}->{$placeVar.place_name}->{$placeVar.place_width}px*{$placeVar.place_height}px</option>
                                {/foreach}
                            </select>
                        </div>
                        <div class="form-group col-md-4">
                            <label for="list_weight">排序</label>
                            <input name="list_weight" id="list_weight" value="{$dataVar.list_weight}" type="text" class="form-control" placeholder="越小越在前">
                        </div>
                        <div class="form-group">
                            <div class="col-md-9  mb10">
                                <label for="list_img">图片上传</label>
                                <input name="list_img" id="list_img" value="{$dataVar.list_img}" type="text" class="form-control" placeholder="先点击右侧按钮上传图片，或直接书写图片网址">
                            </div>
                            <div class="col-md-3">
                                <label for="list_img">&nbsp;</label>
                                <a data-color="rgb(255, 255, 255)" data-color-format="hex" id="cp4" class="btn btn-primary my-colorpicker-control upload-btn" data-colorpicker-guid="1">
                                    <span class="glyphicon glyphicon-floppy-open"></span>
                                    选择图片上传<input type="file" class="ipt-img-click" data-element="list_img_view" data-originalipt="list_img" data-thumbnailipt="list_imgthum"></a>
                            </div>
                        </div>
                        <div class="form-group col-md-12">
                            <label for="list_imgthum">缩略图</label>
                            <input name="list_imgthum" id="list_imgthum" value="{$dataVar.list_imgthum}" type="text" class="form-control" placeholder="通过系统上传图片后自动获得"readonly>
                        </div>
                        <div class="form-group col-md-12">
                            <label for="list_outlink">广告链接</label>
                            <input name="list_outlink" id="list_outlink" value="{$dataVar.list_outlink}" type="text" class="form-control" placeholder="请输入广告链接，如：http://xxx.com/">
                        </div>
                        <div class="form-group col-md-12">
                            <label for="list_mediaurl">媒体视频连接</label>
                            <input name="list_mediaurl" id="list_mediaurl" value="{$dataVar.list_mediaurl}" type="text" class="form-control" placeholder="请输入媒体视频连接，如：http://xxx.com/text.mp3">
                        </div>
                        <div class="form-group col-md-12">
                            <label for="list_intro">广告设置或内容</label>
                            <textarea name="list_intro" id="list_intro" class="form-control" rows="3">{$dataVar.list_intro}</textarea>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="upload-img" id="list_img_view">

                        <div class="f16 tc default">
                            <p class="f30"><span class="glyphicon glyphicon-picture"></span></p>
                            <p>请先上传图片后预览</p>
                        </div>
                        <div class="img set {if !$dataVar.list_img}none{/if}"><img src="{$dataVar.list_img}"></div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>