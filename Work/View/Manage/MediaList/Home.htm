<div class="content">
    <div class="bg-f">
        <h2 class="p20">
            <span class="fr">
                <a href="/{$u}/Add?site_id={$websites.site_id}" class="btn btn-success dropdown-toggle btn-demo-space">+新增数据</a>
                <button type="button" class="btn btn-primary ml10 bakFromurl">返回</button>
            </span>{$moduleOne.module_name}</h2>
        <div class="p20 f14">
            <div class="form-group">
                <form action="/MediaList" method="get" accept-charset="utf-8">
                    <input name="site_id" value="{$websites.site_id}" type="hidden">
                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tr>
                            {if $placeList}
                            <td width="4%">广告位：</td>
                            <td width="15%">
                                <select name="place_id" class="form-control dropdown">
                                    <option value="0">选择广告位</option>
                                    {foreach from=$placeList item=placeVar}
                                    <option value="{$placeVar.place_id}">{$placeVar.place_name}</option>
                                    {/foreach}
                                </select>
                            </td>
                            {/if}
                            <td width="4%">搜索：</td>
                            <td width="15%"><input class="form-control input-sm" id="inputEmail3" placeholder="广告名称" value="{$datatype['keyword']}" type="text" name="keyword"></td>
                            <td width="63%"><button type="submit" class="btn btn-primary ml10"><span class="glyphicon glyphicon-search c-f"></span> 搜索</button> </td>
                        </tr>
                    </table>
                </form>
            </div>

            <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table tc table-hover table-bordered">
                <thead>
                <tr>
                    <th>ID</th>
                    <th>广告标题</th>
                    <th>广告位</th>
                    <th>宽度</th>
                    <th>高度</th>
                    <th>图片预览</th>
                    <th>发布日期</th>
                    <th>操作</th>
                </tr>
                </thead>
                <tbody>
                {if $dataList}
                {foreach from=$dataList item=dataVar}
                <tr id="list-{$dataVar.list_id}">
                    <td>{$dataVar.list_id}</td>
                    <td>{$dataVar.list_title}</td>
                    <td>{$dataVar.place_name}</td>
                    <td>{$dataVar.place_width}px</td>
                    <td>{$dataVar.place_height}px</td>
                    <td>{if $dataVar.list_img != ''}<a data-imgurl="{$dataVar.list_img}" class="cp Opon-Img-View showico" title="查看效果"></a>{/if}</td>
                    <td>{$dataVar.list_time|date_format:'%Y-%m-%d %H:%M:%S'}</td>
                    <td>
                        <a href="/{$u}/Edit?id={$dataVar.list_id}&site_id={$websites.site_id}" class="btn btn-primary btn-sm"><span class="glyphicon glyphicon-pencil c-f"></span> 编辑</a>
                        <a  href="javascript:;" data-element="list-{$dataVar.list_id}" data-url="/{$u}?c=Del&id={$dataVar.list_id}" class="btn btn-danger btn-sm btn-del-action"><span class="glyphicon glyphicon-remove c-f"></span> 删除</a>
                    </td>
                </tr>
                {/foreach}
                {/if}
                </tbody>
            </table>
            <div class="pagemenu">{$pagelist}</div>
        </div>
    </div>
</div>