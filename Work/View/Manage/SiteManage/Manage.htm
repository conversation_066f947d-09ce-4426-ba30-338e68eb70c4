<div class="content">
    <div id="container">
        <div class="bg-f row py20">
            <form action="/{$u}?c={$act}" role="form" method="post" enctype="application/x-www-form-urlencoded" class="AjaxForm form-horizontal">
                <input name="site_id" type="hidden" value="{$dataVar.site_id}">
                <h2 class="p20">站点管理<span class="fr">
                <button type="submit" class=" btn btn-success btn-cons"><i class="fa fa-check"></i>提交保存</button>
                <button type="button" class="btn btn-primary ml10 bakFromurl">返回</button></span>
                </h2>
                <div class="col-md-6" id="Form-Box-Operating">
                    <div class="pl20 py20 f14">
                        <div class="form-group">
                            <label for="site_title" class="col-sm-2 control-label">站点名称</label>
                            <div class="col-sm-10">
                                <input name="site_title" id="site_title" value="{$dataVar.site_title}" type="text" class="form-control" placeholder="请输入站点名称">
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="site_type" class="col-sm-2 control-label">模块类型</label>
                            <div class="col-sm-4">
                                <select name="site_type" id="site_type" class="form-control">
                                    <option value="0"  {if $dataVar.site_type == '0'}selected{/if}>内部站点</option>
                                    <option value="1"  {if $dataVar.site_type == '1'}selected{/if}>外部站点</option>
                                </select>
                            </div>
                            <label for="site_weight" class="col-sm-2 control-label">模块排序</label>
                            <div class="col-sm-4">
                                <input name="site_weight" id="site_weight" value="{$dataVar.site_weight}" type="text" class="form-control" placeholder="越小越在前，如1">
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="site_type" class="col-sm-2 control-label">图片上传</label>
                            <div class="col-sm-7">
                                <input name="site_img" id="site_img" value="{$dataVar.site_img}" type="text" class="form-control" placeholder="先点击右侧按钮上传图片，或直接书写图片网址">
                            </div>
                            <div class="col-md-3">
                                <a data-color="rgb(255, 255, 255)" data-color-format="hex" id="cp4" class="btn btn-primary my-colorpicker-control upload-btn" data-colorpicker-guid="1">
                                    <span class="glyphicon glyphicon-floppy-open"></span>
                                    选择图片上传<input type="file" class="ipt-img-click" data-element="list_img_view" data-originalipt="site_img" data-thumbnailipt="site_imgthum"></a>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="site_url" class="col-sm-2 control-label">站点域名</label>
                            <div class="col-sm-10">
                                <input name="site_url" id="site_url" value="{$dataVar.site_url}" type="text" class="form-control" placeholder="请输入站点连接">
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="site_manageurl" class="col-sm-2 control-label">站点管理地址</label>
                            <div class="col-sm-10">
                                <input name="site_manageurl" id="site_manageurl" value="{$dataVar.site_manageurl}" type="text" class="form-control" placeholder="请输入站点连接">
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="site_name" class="col-sm-2 control-label">外部站点用户名</label>
                            <div class="col-sm-2">
                                <input name="site_name" id="site_name" value="{$dataVar.site_name}" type="text" class="form-control" placeholder="请输入用户名">
                            </div>
                            <label for="site_pswd" class="col-sm-2 control-label">外部站点密码</label>
                            <div class="col-sm-2">
                                <input name="site_pswd" id="site_pswd" value="{$dataVar.site_pswd}" type="password" class="form-control" placeholder="请输入密码">
                            </div>
                            <label for="site_dynamicpswd" class="col-sm-2 control-label">动态密码</label>
                            <div class="col-sm-2">
                                <input name="site_dynamicpswd" id="site_dynamicpswd" value="{$dataVar.site_dynamicpswd}" type="text" class="form-control" placeholder="请输入动态密码">
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="site_details"class="col-sm-2 control-label">站点简介</label>
                            <div class="col-sm-10">
                                <textarea name="site_details" id="site_details" class="form-control" rows="3">{$dataVar.site_details}</textarea>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="upload-img" id="list_img_view">
                        <div class="f16 tc default {if $dataVar.site_img}none{/if}">
                            <p class="f30"><span class="glyphicon glyphicon-picture"></span></p>
                            <p>请先上传图片后预览</p>
                        </div>
                        <div class="img set {if !$dataVar.site_img}none{/if}"><img src="{$dataVar.site_img}"></div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>