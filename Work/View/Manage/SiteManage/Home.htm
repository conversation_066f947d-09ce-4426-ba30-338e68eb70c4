<div class="content">
    <div class="bg-f">
        <h2 class="p20">站点管理
            <span class="fr">
                <a href="/{$u}/Add" class="btn btn-success dropdown-toggle btn-demo-space">+新增站点</a>
                <button type="button" class="btn btn-primary ml10 bakFromurl">返回</button>
            </span>
        </h2>
        <div class="p20 f14">
            <div class="form-group">
                <form action="?" method="get" accept-charset="utf-8">
                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tr>
                            <td width="4%">分类：</td>
                            <td width="14%">
                                <select name="type" class="form-control dropdown">
                                    <option value="">选择类型</option>
                                    <option value="0">内部站点</option>
                                    <option value="1">外部站点</option>
                                </select>
                            </td>
                            <td width="4%">搜索：</td>
                            <td width="15%"><input class="form-control input-sm" placeholder="请输入站点名称或域名关键词" value="{$datatype['keyword']}" type="text" name="keyword"></td>
                            <td width="63%"><button type="submit" class="btn btn-primary ml10"><span class="glyphicon glyphicon-search c-f"></span> 搜索</button> </td>
                        </tr>
                    </table>
                </form>
            </div>
            <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table tc table-hover table-bordered">
                <thead>
                <tr>
                    <th>序号</th>
                    <th>站点名称</th>
                    <th>站点类型</th>
                    <th>站点标志</th>
                    <th>站点连接</th>
                    <th>排序</th>
                    <th>操作</th>
                </tr>
                </thead>
                <tbody>
                {if $dataList}
                {foreach from=$dataList item=dataVar key=key}
                <tr id="list-{$dataVar.site_id}">
                    <td>{$key+1}</td>
                    <td>{$dataVar.site_title}</td>
                    <td>{if $dataVar.site_type == '0'}内部站点{else}外部站点{/if}</td>
                    <td>{if $dataVar.site_img != ''}<a data-imgurl="{$dataVar.site_img}" class="cp Opon-Img-View showico" title="查看效果"></a>{/if}</td>
                    <td>{$dataVar.site_manageurl}</td>
                    <td>{$dataVar.site_weight}</td>
                    <td align="left">
                        <a href="/{$u}/Edit?id={$dataVar.site_id}" class="btn btn-primary btn-sm"><span class="glyphicon glyphicon-pencil c-f"></span> 编辑</a>
                        {if $dataVar.site_type == '0'}
                        <a href="/Website/id-{$dataVar.site_id}" class="btn btn-primary btn-sm"><span class="glyphicon glyphicon-eye-close c-f"></span> 站点管理</a>
                        {else}
                        <a href="{$dataVar.site_manageurl}" target="_blank" class="btn btn-primary btn-sm"><span class="glyphicon glyphicon-eye-close c-f"></span> 站点管理</a>
                        {/if}
                        <a  href="javascript:;" data-element="list-{$dataVar.site_id}" data-url="/{$u}?c=Del&id={$dataVar.site_id}" class="btn btn-danger btn-sm btn-del-action"><span class="glyphicon glyphicon-remove c-f"></span> 删除</a>
                    </td>
                </tr>
                {/foreach}
                {/if}
                </tbody>
            </table>
            <div class="pagemenu">{$pagelist}</div>
        </div>
    </div>
</div>