<div class="content">
    <div id="container">
        <div class="bg-f row py20">
            <form action="/{$u}?c={$act}" method="post" enctype="application/x-www-form-urlencoded" class="AjaxForm">
                <input name="help_id" type="hidden" value="{$dataVar.help_id}">
                <input name="site_id" type="hidden" value="{$websites.site_id}">
                <h2 class="p20">{$moduleOne.module_name}<span class="fr">
                <button type="submit" class=" btn btn-success btn-cons"><i class="fa fa-check"></i>提交保存</button>
                <button type="button" class="btn btn-primary ml10 bakFromurl">返回</button></span>
                </h2>

                <div class="col-md-8" id="Form-Box-Operating">
                    <div class="form-group col-md-4">
                        <label for="list_parameter">文章大分类</label>
                        <select name="list_parameter" id="list_parameter" class="form-control">
                            {if $menulist}
                            {foreach from=$menulist item=tbvar key=key}
                            <option value="{$tbvar.list_parameter}" {if $datatype.list_parameter==$tbvar.list_parameter}selected{/if}>{$tbvar.list_name}</option>
                            {/foreach}
                            {/if}
                        </select>
                    </div>
                    <div class="form-group col-md-4">
                        <label for="help_menuname">菜单名称</label>
                        <input name="help_menuname" id="help_menuname" value="{$dataVar.help_menuname}" type="text" class="form-control" placeholder="请输入菜单名称">
                    </div>
                    <div class="form-group col-md-4">
                        <label for="help_title">文章标题</label>
                        <input name="help_title" id="help_title" value="{$dataVar.help_title}" type="text" class="form-control" placeholder="请输入文章标题">
                    </div>
                    <div class="form-group col-md-12">
                        <label for="qeditor_body">文章内容</label>
                        <div class="control-box  debug-gray">
                            <div class="box-editor">
                                <textarea name="help_content" id="qeditor_body" class="textarea qeditor" rows="3">{$dataVar.help_content}</textarea>
                            </div>
                        </div>
                    </div>
                    <div class="clear"></div>
                </div>

            </form>
        </div>
    </div>
</div>
<script src="{$JsUrl}jquery.min.js" type="text/javascript"></script>
{if $dataVar.goods_day == '1' || !isset($dataVar.goods_day)}
<script>
    $('#goods_starttime').hide();
    $('#goods_endtime').hide();
    $("#goods_day").change(function(){
        var v = $("#goods_day").val();
        if (v == '0') {
            $('#goods_starttime').show();
            $('#goods_endtime').show();
        }else{
            $('#goods_starttime').hide();
            $('#goods_endtime').hide();
        }
    });
</script>
{elseif $dataVar.goods_day == '0'}
<script>
    $("#goods_day").change(function(){
        var v = $("#goods_day").val();
        if (v == '0') {
            $('#goods_starttime').show();
            $('#goods_endtime').show();
        }else{
            $('#goods_starttime').hide();
            $('#goods_endtime').hide();
        }
    });
</script>
{/if}
