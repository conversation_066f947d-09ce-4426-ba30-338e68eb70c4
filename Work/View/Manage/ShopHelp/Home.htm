<div class="content">
    <div class="bg-f">
        <h2 class="p20">{$moduleOne.module_name}
            <span class="fr">
                <a href="/{$u}/Add?site_id={$websites.site_id}" class="btn btn-success dropdown-toggle btn-demo-space">+新增数据</a>
                <a class="btn btn-primary bakFromurl ml10"><span class="glyphicon glyphicon-share-alt c-f"></span> 返回</a>
            </span>
        </h2>
        <div class="p20 f14">
            <div class="form-group">
                <form action="/{$u}" method="get" accept-charset="utf-8">
                    <input name="site_id" value="{$websites.site_id}" type="hidden">
                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tr>
                            <td width="5%">上架状态：</td>
                            <td width="10%">
                                <select name="list_parameter" class="form-control ">
                                    <option value="">菜单大分类</option>
                                    {if $menulist}
                                    {foreach from=$menulist item=tbvar key=key}
                                    <option value="{$tbvar.list_parameter}" {if $datatype.list_parameter==$tbvar.list_parameter}selected{/if}>{$tbvar.list_name}</option>
                                    {/foreach}
                                    {/if}
                                </select>
                            </td>
                            <td width="4%"></td>
                            <td width="15%"><input name="keyword" class="form-control input-sm" placeholder="请输入菜单名称/文章标题" value="{$datatype['keyword']}" type="text"></td>
                            <td width="63%"><button type="submit" class="btn btn-primary ml10"><span class="glyphicon glyphicon-search c-f"></span> 搜索</button> </td>
                        </tr>
                    </table>
                </form>
            </div>

            <div class="form-group">
                <form role="form" action="/{$u}?c=SetUnder" method="post" enctype="application/x-www-form-urlencoded" class="AjaxForm">
                    <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table tc table-hover table-bordered">
                        <thead>
                        <tr>
                            <!--<th>选择</th>-->
                            <th>ID</th>
                            <th>菜单分类</th>
                            <th>菜单名称</th>
                            <th>文章标题</th>
                            <th>操作</th>
                        </tr>
                        </thead>
                        <tbody>
                        {if $dataList}
                        {foreach from=$dataList item=dataVar}
                        <tr id="list-{$dataVar.help_id}">
                           <!-- <td>
                                <input name="tab_list[]" value="{$dataVar.help_id}" type="checkbox" class="ace">
                                <span class="lbl"></span>
                            </td>-->
                            <td>{$dataVar.help_id}</td>
                            <td>{$dataVar.list_name}</td>
                            <td>{$dataVar.help_menuname}</td>
                            <td>{$dataVar.help_title}</td>
                            <td align="left">
                                <a href="/{$u}/Edit?id={$dataVar.help_id}&site_id={$websites.site_id}" class="btn btn-primary btn-sm">
                                    <span class="glyphicon glyphicon-pencil c-f"></span> 编辑</a>
                                <a href="javascript:;" data-element="list-{$dataVar.help_id}" data-url="/{$u}?c=Del&id={$dataVar.help_id}" class="btn btn-danger btn-sm btn-del-action">
                                    <span class="glyphicon glyphicon-remove c-f"></span> 删除</a>
                            </td>
                        </tr>
                        {/foreach}
                        {/if}

                        <!--<tr>
                            <td><label form="Choice_All">全选
                                <input name="form-field-checkbox" id="Choice_All" type="checkbox" class="ace">
                                <span class="lbl"></span></label></td>
                            <td colspan="14">
                                <div class="col-md-2">
                                    <select name="goods_under" id="goods_under" class="form-control">
                                        <option value="1">批量上架</option>
                                        <option value="0">批量下架</option>
                                    </select>
                                </div>
                                <button type="submit" class="fl btn btn-primary ml20">
                                    批量操作
                                </button>
                            </td>
                        </tr>-->
                        </tbody>
                    </table>
                </form>
                <div class="pagemenu">{$pagelist}</div>
            </div>
        </div>
    </div>
</div>