<div class="content">
    <div id="container">
        <div class="bg-f row py20">
            <form action="/{$u}?c={$act}" method="post" enctype="application/x-www-form-urlencoded" class="AjaxForm">
                <input name="stage_id" type="hidden" value="{$dataVar.stage_id}">
                <input name="site_id" type="hidden" value="{$websites.site_id}">
                <input name="career_id" type="hidden" value="{$career_id}">
                <h2 class="p20">{$moduleOne.module_name}<span class="fr">
                <button type="submit" class=" btn btn-success btn-cons"><i class="fa fa-check"></i>提交保存</button>
                <button type="button" class="btn btn-primary ml10 bakFromurl">返回</button></span>
                </h2>
                <div class="col-md-6" id="Form-Box-Operating">
                    <div class="pl20 f14">
                        <div class="form-group col-md-5">
                            <label for="stage_cnname">职业阶段中文名</label>
                            <input name="stage_cnname" id="stage_cnname" value="{$dataVar['stage_cnname']}" type="text" class="form-control" placeholder="请输入职业阶段中文名">
                        </div>
                        <div class="form-group col-md-5">
                            <label for="stage_enname">职业阶段英文名</label>
                            <input name="stage_enname" id="stage_enname" value="{$dataVar['stage_enname']}" type="text" class="form-control" placeholder="请输入职业阶段英文名">
                        </div>
                        <div class="form-group col-md-5">
                            <label for="careertype-id">下一阶段</label>
                            <select name="stage_nextid" id="careertype-id" class="form-control">
                                <option value="">请选择下一阶段</option>
                                {if $stage}
                                {foreach from=$stage item=Tbvar key=key}
                                <option value="{$Tbvar.stage_id}" {if $dataVar.stage_nextid == $Tbvar.stage_id}selected{/if}>{$Tbvar.stage_cnname}</option>
                                {/foreach}
                                {/if}
                            </select>
                        </div>
                        <div class="form-group col-md-5">
                            <label for="stage_sort">职业阶段排序</label>
                            <input name="stage_sort" id="stage_sort" value="{$dataVar['stage_sort']}" type="text" class="form-control" placeholder="请输入职业阶段排序">
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
