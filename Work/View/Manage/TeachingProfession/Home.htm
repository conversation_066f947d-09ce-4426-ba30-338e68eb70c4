<div class="content">
    <div class="bg-f">
        <h2 class="p20">
            <span class="fr">
                <a href="/{$u}/Add?site_id={$websites.site_id}" class="btn btn-success dropdown-toggle btn-demo-space">+新增资料</a>
                <button type="button" class="btn btn-primary ml10 bakFromurl">返回</button>
            </span>{$moduleOne.module_name}</h2>
        <div class="p20 f14">
            <div class="form-group">
                <form action="/{$u}" method="get" accept-charset="utf-8">
                    <input name="site_id" value="{$websites.site_id}" type="hidden">
                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tr>
                            <td width="15%"><input class="form-control input-sm" id="inputEmail3" placeholder="职业中文名/英文名/编号" value="{$datatype['keyword']}" type="text" name="keyword"></td>
                            <td width="63%"><button type="submit" class="btn btn-primary ml10"><span class="glyphicon glyphicon-search c-f"></span> 搜索</button> </td>
                        </tr>
                    </table>
                </form>
            </div>

            <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table tc table-hover table-bordered">
                <thead>
                <tr>
                    <th>职业ID</th>
                    <th>职业类型</th>
                    <th>职业编号</th>
                    <th>职业中文名</th>
                    <th>职业英文名</th>
                    <th>下一级别职业</th>
                    <th>职业排序</th>
                    <th>更新时间</th>
                    <th>创建时间</th>
                    <th>操作</th>
                </tr>
                </thead>
                <tbody>
                {if $dataList}
                {foreach from=$dataList item=dataVar}
                <tr id="list-{$dataVar.career_id}">
                    <td>{$dataVar.career_id}</td>
                    <td>{$dataVar.careertype_cnname}</td>
                    <td>{$dataVar.career_branch}</td>
                    <td>{$dataVar.career_cnname}</td>
                    <td>{$dataVar.career_enname}</td>
                    <td>{$dataVar.career_name}</td>
                    <td>{$dataVar.career_sort}</td>
                    <td>{$dataVar.career_updatatime|date_format:'%Y-%m-%d %H:%M:%S'}</td>
                    <td>{$dataVar.career_createtime|date_format:'%Y-%m-%d %H:%M:%S'}</td>
                    <td>
                        <a href="/{$u}/SetPost?career_id={$dataVar.career_id}&site_id={$websites.site_id}&company_id={$dataVar.company_id}" class="btn btn-warning btn-sm"><span class="glyphicon  c-f"></span> 适配职务</a>
                        <a href="/{$u}/CareerStage?site_id={$websites.site_id}&career_id={$dataVar.career_id}" class="btn btn-primary btn-sm"><span class="glyphicon glyphicon-certificate c-f"></span> 职业阶段管理</a>
                        <a href="/{$u}/Edit?career_id={$dataVar.career_id}&site_id={$websites.site_id}" class="btn btn-primary btn-sm"><span class="glyphicon glyphicon-pencil c-f"></span> 编辑</a>
                        <a  href="javascript:;" data-element="list-{$dataVar.career_id}" data-url="/{$u}?c=Del&id={$dataVar.career_id}" class="btn btn-danger btn-sm btn-del-action"><span class="glyphicon glyphicon-remove c-f"></span> 删除</a>
                    </td>
                </tr>
                {/foreach}
                {/if}
                </tbody>
            </table>
            <div class="pagemenu">{$pagelist}</div>
        </div>
    </div>
</div>
