<div class="content">
    <div class="bg-f">
        <h2 class="p20">
            <span class="fr">
                <a href="/{$u}/AddCareerStage?site_id={$websites.site_id}&career_id={$career_id}" class="btn btn-success dropdown-toggle btn-demo-space">+新增资料</a>
                <button type="button" class="btn btn-primary ml10 bakFromurl">返回</button>
            </span>{$moduleOne.module_name}</h2>
        <div class="p20 f14">
            <div class="form-group">
                <form action="/{$u}/CareerStage" method="get" accept-charset="utf-8">
                    <input name="site_id" value="{$websites.site_id}" type="hidden">
                    <input name="career_id" value="{$career_id}" type="hidden">
                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tr>
                            <!--<td width="4%">职业阶段：</td>-->
                            <!--<td width="15%">-->
                                <!--<select name="teachplan_class" class="form-control">-->
                                    <!--<option value="">选择职业阶段</option>-->
                                    <!--{if $stageList}-->
                                    <!--{foreach from=$stageList item=tbvar}-->
                                    <!--<option value="{$tbvar.stage_id}" {if $datatype.stage_id eq $tbvar.stage_id}selected{/if}>{$tbvar.stage_cnname}</option>-->
                                    <!--{/foreach}-->
                                    <!--{/if}-->
                                <!--</select>-->
                            <!--</td>-->
                            <td width="15%"><input class="form-control input-sm" id="inputEmail3" placeholder="职业中文名/英文名/编号" value="{$datatype['keyword']}" type="text" name="keyword"></td>
                            <td width="63%"><button type="submit" class="btn btn-primary ml10"><span class="glyphicon glyphicon-search c-f"></span> 搜索</button> </td>
                        </tr>
                    </table>
                </form>
            </div>

            <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table tc table-hover table-bordered">
                <thead>
                <tr>
                    <th>职业阶段ID</th>
                    <th>职业名称</th>
                    <th>职业阶段中文名</th>
                    <th>职业阶段英文名</th>
                    <th>下一阶段</th>
                    <th>职业阶段排序</th>
                    <th>更新时间</th>
                    <th>创建时间</th>
                    <th>操作</th>
                </tr>
                </thead>
                <tbody>
                {if $dataList}
                {foreach from=$dataList item=dataVar}
                <tr id="list-{$dataVar.stage_id}">
                    <td>{$dataVar.stage_id}</td>
                    <td>{$dataVar.career_cnname}</td>
                    <td>{$dataVar.stage_cnname}</td>
                    <td>{$dataVar.stage_enname}</td>
                    <td>{if $dataVar.stage_name}{$dataVar.stage_name}{else}暂无{/if}</td>
                    <td>{$dataVar.stage_sort}</td>
                    <td>{$dataVar.stage_updatatime|date_format:'%Y-%m-%d %H:%M:%S'}</td>
                    <td>{$dataVar.stage_createtime|date_format:'%Y-%m-%d %H:%M:%S'}</td>
                    <td>
                        <a href="/{$u}/EditCareerStage?stage_id={$dataVar.stage_id}&site_id={$websites.site_id}&career_id={$career_id}" class="btn btn-primary btn-sm"><span class="glyphicon glyphicon-pencil c-f"></span> 编辑</a>
                        <a  href="javascript:;" data-element="list-{$dataVar.stage_id}" data-url="/{$u}?c=DelCareerStage&id={$dataVar.stage_id}&career_id={$career_id}" class="btn btn-danger btn-sm btn-del-action"><span class="glyphicon glyphicon-remove c-f"></span> 删除</a>
                    </td>
                </tr>
                {/foreach}
                {/if}
                </tbody>
            </table>
            <div class="pagemenu">{$pagelist}</div>
        </div>
    </div>
</div>
