<div class="content">
    <div id="container">
        <div class="bg-f row py20">
            <form action="/{$u}?c={$act}" method="post" enctype="application/x-www-form-urlencoded" class="AjaxForm">
                <input name="career_id" type="hidden" value="{$dataVar.career_id}">
                <input name="site_id" type="hidden" value="{$websites.site_id}">
                <h2 class="p20">{$moduleOne.module_name}<span class="fr">
                <button type="submit" class=" btn btn-success btn-cons"><i class="fa fa-check"></i>提交保存</button>
                <button type="button" class="btn btn-primary ml10 bakFromurl">返回</button></span>
                </h2>
                <div class="col-md-6" id="Form-Box-Operating">
                    <div class="pl20 f14">
                        <div class="form-group col-md-5">
                            <label for="careertype_id">职业类型</label>
                            <select name="careertype_id" id="careertype_id" class="form-control">
                                <option value="">请选择职业类型</option>
                                {if $careertype}
                                {foreach from=$careertype item=Tbvar key=key}
                                <option value="{$Tbvar.careertype_id}" {if $dataVar.careertype_id == $Tbvar.careertype_id}selected{/if}>{$Tbvar.careertype_cnname}</option>
                                {/foreach}
                                {/if}
                            </select>
                        </div>
                        <div class="form-group col-md-5">
                            <label for="career_cnname">职业中文名</label>
                            <input name="career_cnname" id="career_cnname" value="{$dataVar['career_cnname']}" type="text" class="form-control" placeholder="请输入职业中文名">
                        </div>
                        <div class="form-group col-md-5">
                            <label for="career_enname">职业英文名</label>
                            <input name="career_enname" id="career_enname" value="{$dataVar['career_enname']}" type="text" class="form-control" placeholder="请输入职业英文名">
                        </div>
                        <div class="form-group col-md-5">
                            <label for="career_branch">职业编号</label>
                            <input name="career_branch" id="career_branch" value="{$dataVar['career_branch']}" type="text" class="form-control" placeholder="请输入职业编号">
                        </div>
                        <div class="form-group col-md-5">
                            <label for="careertype-id">下一级别职业</label>
                            <select name="career_nextid" id="careertype-id" class="form-control">
                                <option value="">请选择下一级别职业</option>
                                {if $career}
                                {foreach from=$career item=Tbvar key=key}
                                <option value="{$Tbvar.career_id}" {if $dataVar.career_nextid == $Tbvar.career_id}selected{/if}>{$Tbvar.career_cnname}</option>
                                {/foreach}
                                {/if}
                            </select>
                        </div>
                        <div class="form-group col-md-5">
                            <label for="career_sort">职业排序</label>
                            <input name="career_sort" id="career_sort" value="{$dataVar['career_sort']}" type="text" class="form-control" placeholder="请输入职业排序">
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
