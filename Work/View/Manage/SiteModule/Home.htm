<div class="content">
    <div class="bg-f">
        <h2 class="p20">{$moduleOne.module_name}
            <span class="fr">
                <a href="/{$u}/Add?site_id={$websites.site_id}" class="btn btn-success dropdown-toggle btn-demo-space">+新增数据</a>
                <button type="button" class="btn btn-primary ml10 bakFromurl">返回</button>
            </span>
        </h2>
        <div class="p20 f14">
            <div class="form-group">
                <form action="?" method="get" accept-charset="utf-8">
                    <input name="site_id" type="hidden" value="{$websites.site_id}">
                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tr>
                            <td width="5%">站点：</td>
                            <td width="15%">
                                <select name="module_class" class="form-control dropdown">
                                    <option value="" >选择所属站点</option>
                                    <option value="1" {if $datatype.module_class==1}selected{/if}>集团功能</option>
                                    <option value="2" {if $datatype.module_class==2}selected{/if}>校务功能</option>
                                    <option value="3" {if $datatype.module_class==3}selected{/if}>CRM管理</option>
                                    <option value="4" {if $datatype.module_class==4}selected{/if}>家校互动</option>
                                    <option value="5" {if $datatype.module_class==5}selected{/if}>客诉功能</option>
                                </select>
                            </td>
                            <td width="5%">菜单等级：</td>
                            <td width="15%">
                                <select name="module_level" class="form-control dropdown">
                                    <option value="" >选择菜单等级</option>
                                    <option value="1" {if $datatype.module_level==1}selected{/if}>一级菜单</option>
                                    <option value="2" {if $datatype.module_level==2}selected{/if}>二级菜单</option>
                                    <option value="3" {if $datatype.module_level==3}selected{/if}>三级菜单</option>
                                    <option value="4" {if $datatype.module_level==4}selected{/if}>四级菜单</option>
                                </select>
                            </td>
                            <td width="15%"><input class="form-control input-sm" id="inputEmail3" placeholder="请输入模块名称" value="{$datatype['keyword']}" type="text" name="keyword"></td>
                            <td width="30%"><button type="submit" class="btn btn-primary ml10"><span class="glyphicon glyphicon-search c-f"></span> 搜索</button> </td>
                            <td></td>
                        </tr>
                    </table>
                </form>
            </div>
            <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table tc table-hover table-bordered">
                <thead>
                <tr>
                    <th>序号</th>
                    <th>模块名称</th>
                    <th>版本</th>
                    <th>所属站点</th>
                    <th>模块标示字符串</th>
                    <th>模块标识</th>
                    <th>模块链接</th>
                    <th>权重</th>
                    <th>上级模块ID</th>
                    <th>是否进行权限限制</th>
                    <th>是否系统设置</th>
                    <th>操作</th>
                </tr>
                </thead>
                <tbody>
                {if $dataList}
                {foreach from=$dataList item=dataVar key=key}
                <tr id="list-{$dataVar.module_id}">
                    <td>{$dataVar.module_id}</td>
                    <td>{$dataVar.module_name}</td>
                    <td>
                        {if $dataVar.module_ismajor == '0'}
                        免费版
                        {elseif $dataVar.module_ismajor == '1'}
                        普通版
                        {elseif $dataVar.module_ismajor == '2'}
                        专业版
                        {/if}
                    </td>
                    <td>
                        {if $dataVar.module_class == '1'}
                        集团功能
                        {elseif $dataVar.module_class == '2'}
                        校务功能
                        {elseif $dataVar.module_class == '3'}
                        CRM管理
                        {elseif $dataVar.module_class == '4'}
                        家校互动
                        {elseif $dataVar.module_class == '5'}
                        客诉功能
                        {/if}
                    </td>
                    <td>{if $dataVar.module_markstring}{$dataVar.module_markstring}{else}无{/if}</td>
                    <th>{$dataVar.module_icon}</th>
                    <td>{if $dataVar.module_markurl}{$dataVar.module_markurl}{else}无{/if}</td>
                    <td>{$dataVar.module_weight}</td>
                    <td>{if $dataVar.father_name}{$dataVar.father_name}{else}无{/if}</td>
                    <td>{$dataVar.module_access}</td>
                    <td>{$dataVar.module_isset}</td>
                    <td class="tl">
                        <a href="/{$u}/Edit?site_id={$websites.site_id}&module_id={$dataVar.module_id}" class="btn btn-primary btn-sm"><span class="glyphicon glyphicon-pencil c-f"></span> 编辑</a>
                        <a href="/{$u}/SiteModuleChild?site_id={$websites.site_id}&module_id={$dataVar.module_id}" class="btn btn-primary btn-sm"><span class="glyphicon glyphicon-indent-left c-f"></span> 下级管理</a><!--
                        <a href="/{$u}/ModuleFunction?site_id={$websites.site_id}&module_id={$dataVar.module_id}" class="btn btn-primary btn-sm"><span class="glyphicon glyphicon-sort-by-attributes c-f"></span> 功能模块管理</a>-->
                        <a href="javascript:;" data-element="list-{$dataVar.module_id}" data-url="/{$u}?site_id={$websites.site_id}&c=Del&id={$dataVar.module_id}" class="btn btn-danger btn-sm btn-del-action"><span class="glyphicon glyphicon-remove c-f"></span> 删除</a>

                        <a href="/{$u}/HandbookLook?site_id={$websites.site_id}&module_id={$dataVar.module_id}" class="btn btn-primary btn-sm"><span class="glyphicon glyphicon-eye-open c-f"></span> 模块培训管理</a>
                        <a href="/{$u}/Faquestion?site_id={$websites.site_id}&module_id={$dataVar.module_id}" class="btn btn-primary btn-sm"><span class="glyphicon glyphicon-eye-open c-f"></span> 模块常见问题</a>
                    </td>
                </tr>
                {/foreach}
                {/if}
                </tbody>
            </table>
            <div class="pagemenu">{$pagelist}</div>
        </div>
    </div>
</div>