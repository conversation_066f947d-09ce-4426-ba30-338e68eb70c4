<div class="content">
    <div class="bg-f">
        <h2 class="p20">{$moduleOne.module_name}--模块手册
            <span class="fr">
                <a href="/{$u}/HandbookAdd?site_id={$websites.site_id}&module_id={$module_id}" class="btn btn-success dropdown-toggle btn-demo-space">+新增数据</a>
                <button type="button" class="btn btn-primary ml10 bakFromurl">返回</button>
            </span>
        </h2>
        <div class="p20 f14">
            <!--<div class="form-group">
                <form action="?" method="get" accept-charset="utf-8">
                    <input name="site_id" type="hidden" value="{$websites.site_id}">
                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tr>
                            <td width="4%">站点：</td>
                            <td width="14%">
                                <select name="module_class" class="form-control dropdown">
                                    <option value="" >选择所属站点</option>
                                    <option value="1" {if $datatype.module_class==1}selected{/if}>集团功能</option>
                                    <option value="2" {if $datatype.module_class==2}selected{/if}>校务功能</option>
                                    <option value="3" {if $datatype.module_class==3}selected{/if}>CRM管理</option>
                                    <option value="4" {if $datatype.module_class==4}selected{/if}>家校互动</option>
                                </select>
                            </td>
                            <td width="4%">菜单等级：</td>
                            <td width="14%">
                                <select name="module_level" class="form-control dropdown">
                                    <option value="" >选择菜单等级</option>
                                    <option value="1" {if $datatype.module_level==1}selected{/if}>一级菜单</option>
                                    <option value="2" {if $datatype.module_level==2}selected{/if}>二级菜单</option>
                                    <option value="3" {if $datatype.module_level==3}selected{/if}>三级菜单</option>
                                    <option value="4" {if $datatype.module_level==4}selected{/if}>四级菜单</option>
                                </select>
                            </td>
                            <td width="4%">搜索：</td>
                            <td width="15%"><input class="form-control input-sm" id="inputEmail3" placeholder="请输入模块名称" value="{$datatype['keyword']}" type="text" name="keyword"></td>
                            <td width="63%"><button type="submit" class="btn btn-primary ml10"><span class="glyphicon glyphicon-search c-f"></span> 搜索</button> </td>
                        </tr>
                    </table>
                </form>
            </div>-->
            <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table tc table-hover table-bordered">
                <thead>
                <tr>
                    <th>ID</th>
                    <th>模块名称</th>
                    <th>手册名称</th>
                    <th>培训视频路径</th>
                    <th>权重</th>
                    <th>创建时间</th>
                    <th>操作</th>
                </tr>
                </thead>
                <tbody>
                {if $dataList}
                {foreach from=$dataList item=dataVar key=key}
                <tr id="list-{$dataVar.handbook_id}">
                    <td>{$dataVar.handbook_id}</td>
                    <td>{$dataVar.module_name}</td>
                    <td>{$dataVar.handbook_name}</td>
                    <td>{$dataVar.handbook_videourl}</td>
                    <td>{$dataVar.handbook_weight}</td>
                    <td>{$dataVar.handbook_createtime|date_format:'%Y-%m-%d %H:%M:%S'}</td>
                    <td>
                        <a href="/{$u}/HandbookEdit?site_id={$websites.site_id}&handbook_id={$dataVar.handbook_id}&module_id={$module_id}" class="btn btn-primary btn-sm"><span class="glyphicon glyphicon-pencil c-f"></span> 编辑</a>
                        <a href="javascript:;" data-element="list-{$dataVar.handbook_id}" data-url="/{$u}?site_id={$websites.site_id}&c=Delhandbook&handbook_id={$dataVar.handbook_id}&module_id={$module_id}" class="btn btn-danger btn-sm btn-del-action"><span class="glyphicon glyphicon-remove c-f"></span> 删除</a>
                    </td>
                </tr>
                {/foreach}
                {/if}
                </tbody>
            </table>
            <div class="pagemenu">{$pagelist}</div>
        </div>
    </div>
</div>