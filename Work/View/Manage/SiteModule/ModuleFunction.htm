<div class="content">
    <div class="bg-f">
        <h2 class="p20">{$moduleOne.module_name}
            <span class="fr">
                <a href="/{$u}/FunctionAdd?site_id={$websites.site_id}&module_id={$datatype['module_id']}" class="btn btn-success dropdown-toggle btn-demo-space">+新增数据</a>
                <button type="button" class="btn btn-primary ml10 bakFromurl">返回</button>
            </span>
        </h2>
        <div class="p20 f14">
            <div class="form-group">
                <form action="?" method="get" accept-charset="utf-8">
                    <input name="module_id" type="hidden" value="{$datatype['module_id']}">
                    <input name="site_id" type="hidden" value="{$websites.site_id}">
                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tr>
                            <td width="4%">搜索：</td>
                            <td width="15%"><input class="form-control input-sm" id="inputEmail3" placeholder="请输入功能名称" value="{$datatype['keyword']}" type="text" name="keyword"></td>
                            <td width="63%"><button type="submit" class="btn btn-primary ml10"><span class="glyphicon glyphicon-search c-f"></span> 搜索</button> </td>
                        </tr>
                    </table>
                </form>
            </div>
            <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table tc table-hover table-bordered">
                <thead>
                <tr>
                    <th>序号</th>
                    <th>功能名称</th>
                    <th>功能标示符</th>
                    <th>是否进行权限限制</th>
                    <th>操作</th>
                </tr>
                </thead>
                <tbody>
                {if $dataList}
                {foreach from=$dataList item=dataVar key=key}
                <tr id="list-{$dataVar.function_id}">
                    <td>{$dataVar.function_id}</td>
                    <td>{$dataVar.function_name}</td>
                    <td>{$dataVar.function_markstring}</td>
                    <td>{if $dataVar.function_access == '1'}限制{else}不限制{/if}</td>
                    <td>
                        <a href="/{$u}/FunctionEdit?site_id={$websites.site_id}&function_id={$dataVar.function_id}&module_id={$dataVar.module_id}" class="btn btn-primary btn-sm"><span class="glyphicon glyphicon-pencil c-f"></span> 编辑</a>
                        <a href="javascript:;" data-element="list-{$dataVar.function_id}" data-url="/{$u}?site_id={$websites.site_id}&c=FunctionDel&id={$dataVar.function_id}&module_id={$dataVar.module_id}" class="btn btn-danger btn-sm btn-del-action"><span class="glyphicon glyphicon-remove c-f"></span> 删除</a>
                    </td>
                </tr>
                {/foreach}
                {/if}
                </tbody>
            </table>
            <div class="pagemenu">{$pagelist}</div>
        </div>
    </div>
</div>