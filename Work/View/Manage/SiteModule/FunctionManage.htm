<div class="content">
    <div id="container">
        <div class="bg-f row py20">
            <form action="/{$u}?c={$act}" role="form" method="post" enctype="application/x-www-form-urlencoded" class="AjaxForm">
                <input name="module_id" type="hidden" value="{if $father_id}{$father_id}{else}{$dataVar.module_id}{/if}">
                <input name="site_id" type="hidden" value="{$websites.site_id}">
                <input name="function_id" type="hidden" value="{$dataVar.function_id}">
                <h2 class="p20">{$moduleOne.module_name} -- 功能模块添加<span class="fr">
                <button type="submit" class=" btn btn-success btn-cons"><i class="fa fa-check"></i>提交保存</button>
                <button type="button" class="btn btn-primary ml10 bakFromurl">返回</button></span>
                </h2>
                <div class="col-md-12" id="Form-Box-Operating">
                    <div class="pl20 py20 f14 col-md-6">
                        <div class="form-group col-md-12">
                            <label for="function_name">功能名称</label>
                            <input name="function_name" id="function_name" value="{$dataVar.function_name}" type="text" class="form-control" placeholder="请输入功能名称">
                        </div>
                        <div class="form-group col-md-8">
                            <label for="function_markstring">功能标示符</label>
                            <input name="function_markstring" id="function_markstring" value="{$dataVar.function_markstring}" type="text" class="form-control" placeholder="请输入功能标示符">
                        </div>
                        <div class="form-group col-md-4">
                            <label for="function_access">是否进行权限限制</label>
                            <select name="function_access" id="function_access" class="form-control">
                                <option value="0"  {if $dataVar.function_access == '0'}selected{/if}>不限制</option>
                                <option value="1"  {if $dataVar.function_access == '1'}selected{/if}>限制</option>
                            </select>
                        </div>
                        <div class="form-group col-md-12">
                            <label for="function_fieldjson">功能介绍</label>
                            <textarea name="function_fieldjson" id="function_fieldjson" class="form-control" rows="3">{$dataVar.function_fieldjson}</textarea>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>