<div class="content">
    <div id="container">
        <div class="bg-f row py20">
            <form action="/{$u}?c={$act}" role="form" method="post" enctype="application/x-www-form-urlencoded" class="AjaxForm">
                <input name="module_id" type="hidden" value="{$dataVar.module_id}">
                <input name="site_id" type="hidden" value="{$websites.site_id}">
                <input name="handbook_id" type="hidden" value="{$dataVar.handbook_id}">
                <h2 class="p20">{$moduleOne.module_name}--模块手册<span class="fr">
                <button type="submit" class=" btn btn-success btn-cons"><i class="fa fa-check"></i>提交保存</button>
                <button type="button" class="btn btn-primary ml10 bakFromurl">返回</button></span>
                </h2>
                <div class="col-md-12" id="Form-Box-Operating">
                    <div class="pl20 py20 f14 col-md-6">
                        <div class="form-group col-md-9">
                            <label for="handbook_name">手册名称</label>
                            <input name="handbook_name" id="handbook_name" value="{$dataVar.handbook_name}" type="text" class="form-control" placeholder="请输入手册名称">
                        </div>
                        <div class="form-group col-md-3">
                            <label for="handbook_weight">权重</label>
                            <input name="handbook_weight" id="handbook_weight" value="{$dataVar.handbook_weight}" type="text" class="form-control" placeholder="请输入权重">
                        </div>

                        <div class="form-group">
                            <div class="col-md-9  mb10">
                                <label for="handbook_videourl">培训视频上传</label>
                                <input name="handbook_videourl" id="handbook_videourl" value="{$dataVar.handbook_videourl}" type="text" class="form-control" placeholder="先点击右侧按钮上传文件，或直接输入培训视频地址">
                            </div>
                            <div class="col-md-3">
                                <label>&nbsp;</label>
                                <a data-color="rgb(255, 255, 255)" class="btn btn-primary my-colorpicker-control upload-btn" data-colorpicker-guid="1">
                                    <span class="glyphicon glyphicon-export"></span>
                                    培训视频上传<input type="file" class="oss-file-click-video" data-originalipt="handbook_videourl" data-vtype="{$dataVar.vtype}" ></a>
                            </div>
                        </div>
                        <!--<div class="form-group col-md-12">
                            <label for="handbook_videourl">培训视频路径</label>
                            <input name="handbook_videourl" id="handbook_videourl" value="{$dataVar.handbook_videourl}" type="text" class="form-control" placeholder="请输入培训视频路径">
                        </div>-->


                        <div class="form-group col-md-12">
                            <label for="qeditor_body">手册内容</label>
                            <div class="control-box  debug-gray">
                                <div class="box-editor">
                                    <textarea name="handbook_note" id="qeditor_body" class="textarea qeditor" rows="3">{$dataVar.handbook_note}</textarea>
                                </div>
                            </div>
                        </div>

                    </div>

                </div>
            </form>
        </div>
    </div>
</div>