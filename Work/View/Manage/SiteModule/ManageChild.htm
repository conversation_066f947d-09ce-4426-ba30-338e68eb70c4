<div class="content">
    <div id="container">
        <div class="bg-f row py20">
            <form action="/{$u}?c={$act}" role="form" method="post" enctype="application/x-www-form-urlencoded" class="AjaxForm">
                <input name="module_id" type="hidden" value="{$dataVar.module_id}">
                <input name="site_id" type="hidden" value="{$websites.site_id}">
                <input name="father_id" type="hidden" value="{$father_id}">
                <h2 class="p20">{$moduleOne.module_name}<span class="fr">
                <button type="submit" class=" btn btn-success btn-cons"><i class="fa fa-check"></i>提交保存</button>
                <button type="button" class="btn btn-primary ml10 bakFromurl">返回</button></span>
                </h2>
                <div class="col-md-12" id="Form-Box-Operating">
                    <div class="pl20 py20 f14 col-md-6">
                        <div class="form-group col-md-8">
                            <label for="module_name">模块名称</label>
                            <input name="module_name" id="module_name" value="{$dataVar.module_name}" type="text" class="form-control" placeholder="请输入模块名称">
                        </div>
                        <div class="form-group col-md-4">
                            <label for="module_ismajor">所属版本</label>
                            <select name="module_ismajor" id="module_ismajor" class="form-control">
                                <option value="0" {if $dataVar.module_ismajor == '0'}selected{/if}>免费版</option>
                                <option value="1" {if $dataVar.module_ismajor == '1'}selected{/if}>普通版</option>
                                <option value="2" {if $dataVar.module_ismajor == '2'}selected{/if}>专业版</option>
                            </select>
                        </div>
                        <div class="form-group col-md-4">
                            <label for="module_class">所属站点</label>
                            <select name="module_class" id="module_class" class="form-control">
                                <option value="1"  {if $dataVar.module_class == '1'}selected{/if}>集团功能</option>
                                <option value="2"  {if $dataVar.module_class == '2'}selected{/if}>校务功能</option>
                                <option value="3"  {if $dataVar.module_class == '3'}selected{/if}>CRM管理</option>
                                <option value="4"  {if $dataVar.module_class == '4'}selected{/if}>家校互动</option>
                            </select>
                        </div>

                        <div class="form-group col-md-4">
                            <label for="product_id">所属产品</label>
                            <select name="product_id" id="product_id" class="form-control">
                                <option value="0" >请选择所属产品</option>
                                {foreach from=$productList item=Var}
                                <option value="{$Var.product_id}"  {if $Var.product_id == $dataVar.product_id}selected{/if}>{$Var.product_name}</option>
                                {/foreach}

                            </select>
                        </div>
                        <div class="form-group col-md-4">
                            <label for="module_access">是否进行权限限制</label>
                            <select name="module_access" id="module_access" class="form-control">
                                <option {if $dataVar.module_access == '0'}selected{/if}>不限制</option>
                                <option {if $dataVar.module_access == '1'}selected{/if}>限制</option>
                            </select>
                        </div>
                        <div class="form-group col-md-4">
                            <label for="module_weight">权重</label>
                            <input name="module_weight" id="module_weight" value="{$dataVar.module_weight}" type="text" class="form-control" placeholder="请输入权重">
                        </div>
                        <div class="form-group col-md-4">
                            <label for="module_markstring">模块标示字符串</label>
                            <input name="module_markstring" id="module_markstring" value="{$dataVar.module_markstring}" type="text" class="form-control" placeholder="请输入模块标示字符串">
                        </div>
                        <div class="form-group col-md-4">
                            <label for="module_icon">模块图标标识</label>
                            <input name="module_icon" id="module_icon" value="{$dataVar.module_icon}" type="text" class="form-control" placeholder="请输入模块图标标识">
                        </div>
                        <div class="form-group col-md-4">
                            <label for="module_isshow">是否显示</label>
                            <select name="module_isshow" id="module_isshow" class="form-control">
                                <option value="">是否显示</option>
                                <option value="0" {if $dataVar.module_isshow eq '0'}selected{/if}>不显示</option>
                                <option value="1" {if $dataVar.module_isshow eq 1}selected{/if}>显示</option>
                            </select>
                        </div>
                        <div class="form-group col-md-4">
                            <label for="module_isset">是否系统设置</label>
                            <select name="module_isset" id="module_isset" class="form-control">
                                <option value="">是否显示</option>
                                <option value="0" {if $dataVar.module_isset eq '0'}selected{/if}>否</option>
                                <option value="1" {if $dataVar.module_isset eq 1}selected{/if}>是</option>
                            </select>
                        </div>
                        <div class="form-group col-md-12">
                            <label for="module_markurl">模块链接</label>
                            <input name="module_markurl" id="module_markurl" value="{$dataVar.module_markurl}" type="text" class="form-control" placeholder="请输入模块链接">
                        </div>


                        <div class="form-group">
                            <div class="col-md-8  mb10">
                                <label for="module_img">菜单图标 (500px*300px)</label>
                                <input name="module_img" id="module_img" value="{$dataVar.module_img}" type="text" class="form-control" placeholder="先点击右侧按钮上传图片，或直接书写图片网址">
                            </div>
                            <div class="col-md-4">
                                <label for="up_img">&nbsp;</label>
                                <a data-color="rgb(255, 255, 255)" data-color-format="hex" id="cp4" class="btn btn-primary my-colorpicker-control upload-btn" data-colorpicker-guid="1">
                                    <span class="glyphicon glyphicon-floppy-open"></span>
                                    选择图片上传<input type="file" id="up_img" class="ipt-img-click" data-element="list_img_view" data-originalipt="module_img" data-thumbnailipt="tb_imgthum"></a>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="upload-img col-md-6" id="list_img_view">
                            <div class="f16 tc default {if $dataVar.module_img}none{/if}">
                                <p class="f30"><span class="glyphicon glyphicon-picture"></span></p>
                                <p>请先上传图片后预览</p>
                            </div>
                            <div class="img set {if !$dataVar.module_img}none{/if}"><img src="{$dataVar.module_img}"></div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>