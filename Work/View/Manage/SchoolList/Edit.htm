<div class="content">
    <div id="container">
        <div class="bg-f row py20">
            <form action="/{$u}?c={$act}" method="post" enctype="application/x-www-form-urlencoded" class="AjaxForm">
                <input name="school_id" type="hidden" value="{$dataVar.school_id}">
                <input name="company_id" type="hidden" value="{$datatype.company_id}">
                <input name="site_id" type="hidden" value="{$websites.site_id}">
                <h2 class="p20">{$moduleOne.module_name}<span class="fr">
                <button type="submit" class=" btn btn-success btn-cons"><i class="fa fa-check"></i>提交保存</button>
                <button type="button" class="btn btn-primary ml10 bakFromurl">返回</button></span>
                </h2>
                <div class="col-md-8" id="Form-Box-Operating">
                    <div class="pl20 py20 f14">
                        <div class="tab-content">
                            <div class="form-group col-md-4">
                                <label for="company_cnname">所属集团</label>
                                <input name="" id="company_cnname" value="{$datatype.company_cnname}" reg="[^ \f\n\r\t\v]" tip="请输入所属集团" type="text" class="form-control" placeholder="请输入所属集团" disabled>
                            </div>
                            <div class="form-group col-md-4">
                                <label for="school_branch">校区编号</label>
                                <input name="school_branch" id="school_branch" value="{$schoolOne.school_branch}" reg="[^ \f\n\r\t\v]" tip="自动生成校园编号" type="text" class="form-control" placeholder="自动生成校园编号" disabled>
                            </div>
                            <div class="form-group col-md-4">
                                <label for="school_cnname">校区名称</label>
                                <input name="school_cnname" id="school_cnname" value="{$schoolOne.school_cnname}" reg="[^ \f\n\r\t\v]" tip="请输入校区名称" type="text" class="form-control" placeholder="请输入校区名称">
                            </div>
                            <div class="form-group col-md-4">
                                <label for="school_enname">学校英文名</label>
                                <input name="school_enname" id="school_enname" value="{$schoolOne.school_enname}" reg="[^ \f\n\r\t\v]" tip="请输入学校英文名" type="text" class="form-control" placeholder="请输入学校英文名">
                            </div>
                            <div class="form-group col-md-4">
                                <label for="companies_id">学校所属企业</label>

                                <select name="companies_id" id="companies_id" class="form-control">
                                    <option value="">请选择学校所属企业</option>
                                    {if $companies}
                                    {foreach from=$companies  item=dataVar }
                                    <option value="{$dataVar.companies_id}" {if $dataVar.companies_id =$schoolOne.companies_id  }selected{/if}>{$dataVar.companies_cnname}</option>
                                    <option value="{$dataVar.companies_id}" {if $dataVar.companies_id =$schoolOne.companies_id  }selected{/if}>{$dataVar.companies_cnname}</option>
                                    {/foreach}
                                    {/if}

                                </select>
                            </div>

                            <div class="form-group col-md-4">
                                <label for="school_cnname_initial">学校首字母</label>
                                <input name="school_cnname_initial" id="school_cnname_initial" value="{$schoolOne.school_cnname_initial}" reg="[^ \f\n\r\t\v]" tip="请输入学校首字母" type="text" class="form-control" placeholder="请输入学校首字母">
                            </div>
                            <div class="form-group col-md-4">
                                <label for="school_shortname">学校简称</label>
                                <input name="school_shortname" id="school_shortname" value="{$schoolOne.school_shortname}" reg="[^ \f\n\r\t\v]" tip="请输入学校简称" type="text" class="form-control" placeholder="请输入学校简称">
                            </div>

                            <div class="form-group col-md-4">
                                <label for="school_type">学校类型</label>
                                <select name="school_type" id="school_type" class="form-control">
                                    <option value="1"  {if $schoolOne.school_type ==1}selected{/if}>直营校</option>
                                    <option value="2"  {if $schoolOne.school_type ==2}selected{/if}>直营园</option>
                                    <option value="3"  {if $schoolOne.school_type ==3}selected{/if}>加盟校</option>
                                    <option value="4"  {if $schoolOne.school_type ==4}selected{/if}>加盟园</option>
                                </select>
                            </div>
                            <div class="form-group col-md-4">
                                <label for="school_phone">学校电话</label>
                                <input name="school_phone" id="school_phone" value="{$schoolOne.school_phone}" reg="[^ \f\n\r\t\v]" tip="请输入学校电话" type="text" class="form-control" placeholder="请输入学校电话" maxlength="11">
                            </div>
                            <div class="form-group col-md-4">
                                <label for="school_cmbshopcode">招行门店编号</label>
                                <input name="school_cmbshopcode" id="school_cmbshopcode" value="{$schoolOne.school_cmbshopcode}" reg="[^ \f\n\r\t\v]" tip="请输入招行门店编号" type="text" class="form-control" placeholder="请输入招行门店编号">
                            </div>
                            <div class="form-group col-md-4">
                                <label for="school_upcshopcode">银联门店编号</label>
                                <input name="school_upcshopcode" id="school_upcshopcode" value="{$schoolOne.school_cmbshopcode}" reg="[^ \f\n\r\t\v]" tip="请输入银联门店编号" type="text" class="form-control" placeholder="请输入银联门店编号">
                            </div>
                            <div class="form-group col-md-4">
                                <label for="school_erpshopcode">校园ERP对接编号</label>
                                <input name="school_erpshopcode" id="school_erpshopcode" value="{$schoolOne.school_cmbshopcode}" reg="[^ \f\n\r\t\v]" tip="请输入校园ERP对接编号" type="text" class="form-control" placeholder="请输入校园ERP对接编号">
                            </div>
                            <div class="form-group col-md-4">
                                <label for="school_province">所属省</label>
                                <select name="school_province" id="school_province" class="provinceAjax">
                                    <option value="">所属省</option>
                                    {if $schoolOne.school_province}
                                    <option value="{$schoolOne.school_province}" selected>{$schoolOne.province_name}</option>
                                    {/if}
                                </select>
                            </div>
                            <div class="form-group col-md-4">
                                <label for="school_city">所属市</label>
                                <select name="school_city" id="school_city" class="cityAjax">
                                    <option value="">所属市</option>
                                    {if $schoolOne.school_city}
                                    <option value="{$schoolOne.school_city}" selected>{$schoolOne.city_name}</option>
                                    {/if}
                                </select>
                            </div>
                            <div class="form-group col-md-4">
                                <label for="school_area">所属区/县</label>
                                <select name="school_area" id="school_area" class="areaAjax">
                                    <option value="">所属区/县</option>
                                    {if $schoolOne.school_area}
                                    <option value="{$schoolOne.school_area}" selected>{$schoolOne.area_name}</option>
                                    {/if}
                                </select>
                            </div>
                            <div class="form-group col-md-4">
                                <label for="district_id">所属地区</label>
                                <br>
                                <select name="district_id" id="district_id" >
                                    <option value="">所属地区</option>
                                    {if $district}
                                    {foreach from=$district item=itemVar}
                                    <option value="{$itemVar.district_id}" {if $itemVar.district_id == $schoolOne.district_id }selected{/if}>{$itemVar.district_cnname}</option>
                                    {/foreach}
                                    {/if}
                                </select>
                            </div>
                            <div class="form-group col-md-4">
                                <label for="school_istest">是否为测试校</label>
                                <br>
                                <select name="school_istest" id="school_istest" >
                                    <option value="0">不是</option>
                                    <option value="1" {if $schoolOne.school_istest ==1}selected{/if}>是</option>
                                </select>
                            </div>
                            <div class="form-group col-md-4">
                                <label for="school_address">学校是否关闭</label>
                                <br>
                                <select name="school_isclose" id="school_isclose" >
                                    <option value="0">未关闭</option>
                                    <option value="1" {if $schoolOne.school_isclose ==1}selected{/if}>已关闭</option>
                                </select>
                            </div>
                            <div class="form-group col-md-10">
                                <label for="school_address">校园地址</label>
                                <input name="school_address" id="school_address" value="{$schoolOne.school_address}" type="text" class="form-control" placeholder="请输入校园地址">
                            </div>

                            <div class="form-group col-md-4">
                                <label for="school_synchro_day">同步2.0数据时间</label>
                                <input name="school_synchro_day" id="school_synchro_day" value="{$schoolOne.school_synchro_day}"  type="date" class="form-control" >
                            </div>

                            <div class="clear"></div>
                        </div>
                    </div>
                </div>

            </form>
        </div>
    </div>
</div>