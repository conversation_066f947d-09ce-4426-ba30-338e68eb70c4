<div class="content">
    <div class="bg-f">
        <h2 class="p20">{$moduleOne.module_name}
            <span class="fr">
                <a href="/{$u}/Add?company_id={$datatype.company_id}&site_id={$websites.site_id}" class="btn btn-success dropdown-toggle btn-demo-space">+新增数据</a>
                <a class="btn btn-primary bakFromurl ml10"><span class="glyphicon glyphicon-share-alt c-f"></span> 返回</a>
            </span>
        </h2>
        <div class="p20 f14">
            <div class="form-group">
                <form action="{$moduleOne.module_link}/SchoolList" method="get" accept-charset="utf-8">
                    <input name="site_id" value="{$websites.site_id}" type="hidden">
                    <input name="company_id" value="{$datatype.company_id}" type="hidden">
                    <input name="from" value="{$datatype.from}" type="hidden">
                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tr>
                            <td width="4%">搜索：</td>
                            <td width="15%">
                                <input name="keyword" class="form-control input-sm" placeholder="请输入校区编号/校区名称" value="{$datatype['keyword']}" type="text">
                            </td>
                            <td width="63%"><button type="submit" class="btn btn-primary ml10"><span class="glyphicon glyphicon-search c-f"></span> 搜索</button> </td>
                        </tr>
                    </table>
                </form>
            </div>
            <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table tc table-hover table-bordered">
                <thead>
                <tr>
                    <th>ID</th>
                    <th>校区名称</th>
                    <th>校区编号</th>
                    <th>所属集团</th>
                    <th>学校电话</th>
                    <th>所在省市</th>
                    <th>具体地址</th>
                    <th>学校类型</th>
                    <th>是否关闭</th>
                    <th>创建时间</th>
                    <th>操作</th>
                </tr>
                </thead>
                <tbody>
                {if $dataList}
                {foreach from=$dataList item=dataVar}
                <tr id="list-{$dataVar.school_id}">
                    <td>{$dataVar.school_id}</td>
                    <td>{$dataVar.school_cnname}</td>
                    <td>{$dataVar.school_branch}</td>
                    <td>{$dataVar.company_cnname}</td>
                    <td>{$dataVar.school_phone}</td>
                    <td>{$dataVar.school_cityaddress}</td>
                    <td>{$dataVar.school_address}</td>
                    <td>{$dataVar.school_typename}</td>
                    <td>{if $dataVar.school_isclose==0}未关闭{else}已关闭{/if}</td>
                    <td>{$dataVar.school_createtime|date_format:'%Y-%m-%d'}</td>
                    <td align="left">
                        <a href="/{$u}/Edit?school_id={$dataVar.school_id}&site_id={$websites.site_id}" class="btn btn-primary btn-sm">
                        <span class="glyphicon glyphicon-pencil c-f"></span> 编辑</a>
                        <a href="/Student?school_id={$dataVar.school_id}&site_id={$websites.site_id}" class="btn btn-primary btn-sm">
                            <span class="glyphicon  c-f"></span> 学员管理</a>
                        <!--<a href="/Staffer?school_id={$dataVar.school_id}&site_id={$websites.site_id}" class="btn btn-primary btn-sm">-->
                            <!--<span class="glyphicon  c-f"></span>职工管理</a>-->
                    </td>
                </tr>
                {/foreach}
                {/if}
                </tbody>
            </table>
            <div class="pagemenu">{$pagelist}</div>
        </div>
    </div>
</div>