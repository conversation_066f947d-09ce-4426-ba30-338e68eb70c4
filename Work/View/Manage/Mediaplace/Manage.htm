<div class="content">
    <div id="container">
        <div class="bg-f row py20">
            <form action="/{$u}?c={$act}" role="form" method="post" enctype="application/x-www-form-urlencoded" class="AjaxForm">
                <input name="place_id" type="hidden" value="{$dataVar.place_id}">
                <h2 class="p20">{$moduleOne.module_name}<span class="fr">
                <button type="submit" class=" btn btn-success btn-cons"><i class="fa fa-check"></i>提交保存</button>
                <button type="button" class="btn btn-primary ml10 bakFromurl">返回</button></span>
                </h2>
                <div class="col-md-8" id="Form-Box-Operating">
                    <div class="pl20 py20 f14 col-md-6">
                        <div class="form-group col-md-12">
                            <label for="place_name">广告位名称</label>
                            <input name="place_name" id="place_name" value="{$dataVar.place_name}" type="text" class="form-control" placeholder="请输入广告位名称">
                        </div>
                        <div class="form-group col-md-6">
                            <label for="place_type">广告类型</label>
                            <select name="place_type" id="place_type" class="form-control">
                                <option {if $dataVar.place_type == '文字链接'}selected{/if}>文字链接</option>
                                <option {if $dataVar.place_type == '图片广告'}selected{/if}>图片广告</option>
                                <option {if $dataVar.place_type == '视频广告'}selected{/if}>视频广告</option>
                            </select>
                        </div>
                        <div class="form-group col-md-6">
                            <label for="site_id">所属站点</label>
                            <select name="site_id" id="site_id" class="form-control">
                                {if $siteList}
                                {foreach from=$siteList item=siteVar}
                                <option value="{$siteVar.site_id}" {if $siteVar.site_id == $dataVar.site_id}selected{/if}>{$siteVar.site_title}</option>
                                {/foreach}
                                {/if}
                            </select>
                        </div>
                        <div class="form-group col-md-6">
                            <label for="place_width">宽度</label>
                            <input name="place_width" id="place_width" value="{$dataVar.place_width}" type="text" class="form-control" placeholder="请输入宽度">
                        </div>
                        <div class="form-group col-md-6">
                            <label for="place_height">高度</label>
                            <input name="place_height" id="place_height" value="{$dataVar.place_height}" type="text" class="form-control" placeholder="请输入高度">
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>