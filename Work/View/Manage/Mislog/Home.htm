<div class="content">
    <div class="bg-f">
        <h2 class="p20">{$moduleOne.module_name}
            <span class="fr">
                <!-- <a href="/{$u}/Add?site_id={$websites.site_id}" class="btn btn-success dropdown-toggle btn-demo-space">+新增数据</a> -->
                <a class="btn btn-primary bakFromurl ml10"><span class="glyphicon glyphicon-share-alt c-f"></span> 返回</a>
            </span>
        </h2>
        <div class="p20 f14">
            <div class="form-group">
                <form action="/{$u}" method="get" accept-charset="utf-8">
                    <input name="site_id" value="{$websites.site_id}" type="hidden">
                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tr>
                            <td width="4%">搜索：</td>
                            <td class="pr10">
                                <select name="mislog_sendinc" class="form-control dropdown">
                                    <option value="">发送状态</option>
                                    <option value="1" {if '1' == $datatype.mislog_sendinc}selected{/if}>成功</option>
                                    <option value="0" {if '0' == $datatype.mislog_sendinc}selected{/if}>失败</option>
                                </select>
                            </td>
                            <td width="15%"><input name="keyword" class="form-control input-sm" placeholder="请输入客户名称" value="{$datatype['keyword']}" type="text"></td>
                            <td width="63%"><button type="submit" class="btn btn-primary ml10"><span class="glyphicon glyphicon-search c-f"></span> 搜索</button> </td>
                        </tr>
                    </table>
                </form>
            </div>
            <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table tc table-hover table-bordered">
                <thead>
                <tr>
                    <th width="5%">ID</th>
                    <th width="8%">名称</th>
                    <th width="10%">手机号</th>
                    <th width="10%">验证码</th>
                    <th width="35%">发送内容</th>
                    <th width="5%">状态</th>
                    <th width="5%">重发</th>
                    <th width="10%">时间</th>
                    <th width="10%">操作</th>
                </tr>
                </thead>
                <tbody>
                {if $dataList}
                {foreach from=$dataList item=dataVar}
                <tr id="list-{$dataVar.mislog_id}">
                    <td>{$dataVar.mislog_id}</td>
                    <td>{$dataVar.mislog_tilte}</td>
                    <td>{$dataVar.mislog_mobile}</td>
                    <td>{$dataVar.mislog_sendcode}</td>
                    <td>{$dataVar.mislog_mistxt}</td>
                    <td>
                        {if $dataVar.mislog_sendinc == '0'}
                        <a class="cp"><span class="glyphicon glyphicon-remove-circle f18 text-danger"></span></a>
                        {elseif $dataVar.mislog_sendinc == '1'}
                        <a class="cp"><span class="glyphicon glyphicon-ok-circle f18 text-primary"></span></a>
                        {/if}
                    </td>
                    <td>
                        {if $dataVar.mislog_retry == '0'}
                        <a class="cp"><span class="glyphicon glyphicon-remove-circle f18 text-danger"></span></a>
                        {elseif $dataVar.mislog_retry == '1'}
                        <a class="cp"><span class="glyphicon glyphicon-ok-circle f18 text-primary"></span></a>
                        {/if}
                    </td>
                    <td>{$dataVar.mislog_time|date_format:'%Y-%m-%d %H:%M:%S'}</td>
                    <td>
                        <a href="javascript:;" data-url="/{$u}?c=Todeal&mislog_id={$dataVar.mislog_id}" class="btn btn-success btn-sm btn-send-action">
                            <span class="glyphicon glyphicon-screenshot"></span> 重新发送</a>
                    </td>
                </tr>
                {/foreach}
                {/if}
                </tbody>
            </table>
            <div class="pagemenu">{$pagelist}</div>
        </div>
    </div>
</div>