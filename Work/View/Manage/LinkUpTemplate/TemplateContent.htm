<div class="content">
    <div class="bg-f">
        <h2 class="p20">
            <span class="fr">
                <a href="/{$u}/TemplateAdd?site_id={$websites.site_id}&template_id={$template_id}" class="btn btn-success dropdown-toggle btn-demo-space">+新增资料</a>
                <button type="button" class="btn btn-primary ml10 bakFromurl">返回</button>
            </span>沟通模板管理</h2>
        <div class="p20 f14">
            <div class="form-group">
                <form action="/{$u}/Chapter" method="get" accept-charset="utf-8">
                    <input name="site_id" value="{$websites.site_id}" type="hidden">
                </form>
            </div>

            <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table tc table-hover table-bordered">
                <thead>
                <tr>
                    <th>ID</th>
                    <th>沟通模板名称</th>
                    <th width="45%">沟通模板内容</th>
                    <th>创建时间</th>
                    <th>操作</th>
                </tr>
                </thead>
                <tbody>
                {if $dataList}
                {foreach from=$dataList item=dataVar key=key}
                <tr id="list-{$dataVar.content_id}">
                    <td>{$key+1}</td>
                    <td>{$dataVar.template_name}</td>
                    <td>{$dataVar.content}</td>
                    <td>{$dataVar.content_addtime|date_format:'%Y-%m-%d %H:%M:%S'}</td>
                    <td>
                        <a href="/{$u}/TemplateEdit?content_id={$dataVar.content_id}&site_id={$websites.site_id}&template_id={$template_id}" class="btn btn-primary btn-sm"><span class="glyphicon glyphicon-pencil c-f"></span> 编辑</a>
                        <a  href="javascript:;" data-element="list-{$dataVar.content_id}" data-url="/{$u}?c=TemplateDel&id={$dataVar.content_id}&site_id={$websites.site_id}" class="btn btn-danger btn-sm btn-del-action"><span class="glyphicon glyphicon-remove c-f"></span> 删除</a>
                    </td>
                </tr>
                {/foreach}
                {/if}
                </tbody>
            </table>
            <div class="pagemenu">{$pagelist}</div>
        </div>
    </div>
</div>
