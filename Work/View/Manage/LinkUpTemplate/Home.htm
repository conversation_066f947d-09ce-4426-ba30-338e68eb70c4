<div class="content">
    <div class="bg-f">
        <h2 class="p20">
            <span class="fr">
                <a href="/{$u}/Add?site_id={$websites.site_id}" class="btn btn-success dropdown-toggle btn-demo-space">+新增资料</a>
                <button type="button" class="btn btn-primary ml10 bakFromurl">返回</button>
            </span>沟通模板管理</h2>
        <div class="p20 f14">
            <div class="form-group">
                <form action="/{$u}/Chapter" method="get" accept-charset="utf-8">
                    <input name="site_id" value="{$websites.site_id}" type="hidden">
                </form>
            </div>

            <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table tc table-hover table-bordered">
                <thead>
                <tr>
                    <th>模板ID</th>
                    <th>课程别名称</th>
                    <th>沟通模板名称</th>
                    <th>操作</th>
                </tr>
                </thead>
                <tbody>
                {if $dataList}
                {foreach from=$dataList item=dataVar}
                <tr id="list-{$dataVar.template_id}">
                    <td>{$dataVar.template_id}</td>
                    <td>{$dataVar.course_cnname}</td>
                    <td>{$dataVar.template_name}</td>
                    <td>
                        <a href="/{$u}/TemplateContent?site_id={$websites.site_id}&template_id={$dataVar.template_id}" class="btn btn-primary btn-sm">
                            <span class="glyphicon glyphicon-certificate c-f"></span> 沟通模板内容管理</a>
                        <a href="/{$u}/Edit?template_id={$dataVar.template_id}&site_id={$websites.site_id}" class="btn btn-primary btn-sm"><span class="glyphicon glyphicon-pencil c-f"></span> 编辑</a>
                        <a  href="javascript:;" data-element="list-{$dataVar.template_id}" data-url="/{$u}?c=Del&id={$dataVar.template_id}&site_id={$websites.site_id}" class="btn btn-danger btn-sm btn-del-action"><span class="glyphicon glyphicon-remove c-f"></span> 删除</a>
                    </td>
                </tr>
                {/foreach}
                {/if}
                </tbody>
            </table>
            <div class="pagemenu">{$pagelist}</div>
        </div>
    </div>
</div>
