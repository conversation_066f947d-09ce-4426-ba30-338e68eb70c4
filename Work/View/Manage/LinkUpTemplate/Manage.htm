<div class="content">
    <div id="container">
        <div class="bg-f row py20">
            <form action="/{$u}?c={$act}" method="post" enctype="application/x-www-form-urlencoded" class="AjaxForm">
                <input name="template_id" type="hidden" value="{$dataVar.template_id}">
                <input name="site_id" type="hidden" value="{$websites.site_id}">
                <input name="course_id" type="hidden" value="{$course_id}">
                <h2 class="p20">沟通模板管理<span class="fr">
                <button type="submit" class=" btn btn-success btn-cons"><i class="fa fa-check"></i>提交保存</button>
                <button type="button" class="btn btn-primary ml10 bakFromurl">返回</button></span>
                </h2>
                <div class="col-md-6" id="Form-Box-Operating">
                    <div class="pl20 f14">
                        <div class="form-group col-md-5">
                            <label for="template_name">沟通模板名称</label>
                            <input name="template_name" id="template_name" value="{$dataVar['template_name']}" type="text" class="form-control" placeholder="请输入沟通模板名称">
                        </div>
                        <div class="form-group col-md-5">
                            <label for="course_id">课程别</label>
                            <select name="course_id" id="course_id" class="form-control">
                                <option value="">请选择课程别</option>
                                {if $course}
                                {foreach from=$course item=Tbvar}
                                <option value="{$Tbvar.course_id}" {if $Tbvar.course_id == $dataVar.course_id}selected{/if}>{$Tbvar.course_cnname} ({$Tbvar.course_branch})</option>
                                {/foreach}
                                {/if}
                            </select>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
