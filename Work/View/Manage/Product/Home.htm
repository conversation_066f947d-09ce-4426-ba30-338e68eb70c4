<div class="content">
    <div class="bg-f">
        <h2 class="p20">{$moduleOne.module_name}
            <span class="fr">
                <a href="/{$u}/Add?site_id={$websites.site_id}" class="btn btn-success dropdown-toggle btn-demo-space">+新增数据</a>
                <a class="btn btn-primary bakFromurl ml10"><span class="glyphicon glyphicon-share-alt c-f"></span> 返回</a>
            </span>
        </h2>
        <div class="p20 f14">
            <div class="form-group">
                <form action="{$moduleOne.module_link}" method="get" accept-charset="utf-8">
                    <input name="site_id" value="{$websites.site_id}" type="hidden">
                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tr>
                            <td width="4%">搜索：</td>
                            <td width="15%">
                                <input name="keyword" class="form-control input-sm" placeholder="请输入产品名称/产品编号" value="{$datatype['keyword']}" type="text">
                            </td>
                            <td width="63%"><button type="submit" class="btn btn-primary ml10"><span class="glyphicon glyphicon-search c-f"></span> 搜索</button> </td>
                        </tr>
                    </table>
                </form>
            </div>
            <table width="100%"  class="table tc table-hover table-bordered">
                <thead>
                <tr>
                    <th>产品名称</th>
                    <th>产品编号</th>
                    <th>是否启用模块权限</th>
                    <th>操作</th>
                </tr>
                </thead>
                <tbody>
                {if $dataList}
                {foreach from=$dataList item=dataVar}
                <tr id="list-{$dataVar.product_id}">
                    <td>{$dataVar.product_name}</td>
                    <td>{$dataVar.product_branch}</td>
                    <td>
                        {if $dataVar.product_openmodule == '1'}
                        <a  href="javascript:;" data-url="/{$u}?c=Changestatus&id={$dataVar.product_id}" class="cp btn-state-send">
                            <span class="glyphicon glyphicon-ok-circle f18 text-primary"></span></a>
                        {elseif $dataVar.product_openmodule == '0'}
                        <a href="javascript:;" data-url="/{$u}?c=Changestatus&id={$dataVar.product_id}" class="cp btn-state-send">
                            <span class="glyphicon glyphicon-remove-circle f18 text-danger"></span></a>
                        {/if}
                    </td>

                    <td align="left">
                        <a href="/{$u}/Edit?product_id={$dataVar.product_id}&site_id={$websites.site_id}" class="btn btn-primary btn-sm">
                            <span class="glyphicon glyphicon-pencil c-f"></span> 编辑</a>
                        {if $dataVar.product_id>3}
                        <a href="javascript:;" data-element="list-{$dataVar.product_id}" data-url="/{$u}?c=Del&id={$dataVar.product_id}" class="btn btn-danger btn-sm btn-del-action">
                            <span class="glyphicon glyphicon-remove c-f"></span> 删除</a>
                        {/if}

                    </td>
                </tr>
                {/foreach}
                {/if}
                </tbody>
            </table>
            <div class="pagemenu">{$pagelist}</div>
        </div>
    </div>
</div>