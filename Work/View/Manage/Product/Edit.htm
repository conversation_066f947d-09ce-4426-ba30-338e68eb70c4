<div class="content">
    <div id="container">
        <div class="bg-f row py20">
            <form action="/{$u}?c={$act}" method="post" enctype="application/x-www-form-urlencoded" class="AjaxForm">
                <input name="site_id" type="hidden" value="{$websites.site_id}">
                <input name="product_id" type="hidden" value="{$dataVar.product_id}">
                <h2 class="p20">{$moduleOne.module_name}<span class="fr">
                <button type="submit" class=" btn btn-success btn-cons"><i class="fa fa-check"></i>提交保存</button>
                <button type="button" class="btn btn-primary ml10 bakFromurl">返回</button></span>
                </h2>
                <div class="col-md-8" id="Form-Box-Operating">
                    <div class="pl20 py20 f14">
                        <div class="tab-content">
                            <div class="form-group col-md-6">
                                <label for="product_name">产品名称</label>
                                <input name="product_name" id="product_name" value="{$dataVar.product_name}" reg="[^ \f\n\r\t\v]" tip="请填写产品名称" type="text" class="form-control" placeholder="请输入产品名称">
                            </div>
                            <div class="form-group col-md-6">
                                <label for="product_branch">产品编号</label>
                                <input name="product_branch" id="product_branch" value="{$dataVar.product_branch}" reg="[^ \f\n\r\t\v]" tip="请填写产品编号" type="text" class="form-control" placeholder="请输入产品编号">
                            </div>
                            <div class="form-group col-md-6">
                                <label for="product_openmodule">是否启用模块权限</label>
                                <select name="product_openmodule" id="product_openmodule" class="form-control">
                                    <option value="1" {if $dataVar.product_openmodule == '1' }selected {/if}>启用</option>
                                    <option value="0" {if $dataVar.product_openmodule == '0' && $dataVar.product_openmodule != ''}selected {/if}>关闭</option>
                                </select>
                            </div>

                            <div class="clear"></div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>