<div class="content">
    <div class="bg-f">
        <h2 class="p20">用户管理
            <span class="fr">
                <a href="/{$u}/Add" class="btn btn-success dropdown-toggle btn-demo-space">+新增用户</a>
                <button type="button" class="btn btn-primary ml10 bakFromurl">返回</button>
            </span>
        </h2>
        <div class="p20 f14">
            <div class="form-group">
                <form action="?" method="get" accept-charset="utf-8">
                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tr>
                            <td width="4%">分类：</td>
                            <td width="14%">
                                <select name="type" class="form-control dropdown">
                                    <option value="">选择类型</option>
                                    <option value="0">站点管理员</option>
                                    <option value="1">系统管理员</option>
                                </select>
                            </td>
                            <td width="4%">搜索：</td>
                            <td width="15%"><input class="form-control input-sm" id="inputEmail3" placeholder="请输入用户名、姓名、手机号" value="{$datatype['keyword']}" type="text" name="keyword"></td>
                            <td width="63%"><button type="submit" class="btn btn-primary ml10"><span class="glyphicon glyphicon-search c-f"></span> 搜索</button> </td>
                        </tr>
                    </table>
                </form>
            </div>
            <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table tc table-hover table-bordered">
                <thead>
                <tr>
                    <th>序号</th>
                    <th>账户</th>
                    <th>中文名</th>
                    <th>英文名</th>
                    <th>邮箱</th>
                    <th>最后登录时间</th>
                    <th>操作</th>
                </tr>
                </thead>
                <tbody>
                {if $dataList}
                {foreach from=$dataList item=dataVar key=key}
                <tr id="list-{$dataVar.user_id}">
                    <td>{$key+1}</td>
                    <td>{$dataVar.user_name}</td>
                    <td>{$dataVar.user_cnname}</td>
                    <td>{$dataVar.user_enname}</td>
                    <td>{$dataVar.user_email}</td>
                    <td>{$dataVar.user_lasttime|date_format:"%Y-%m-%d %H:%M:%S"}</td>
                    <td align="left">
                        <a href="/{$u}/Edit?id={$dataVar.user_id}" class="btn btn-primary btn-sm"><span class="glyphicon glyphicon-pencil c-f"></span> 编辑</a>
                        {if $dataVar.user_limitsinc != '1'}
                        <a href="/{$u}/Limits?user_id={$dataVar.user_id}" class="btn btn-primary btn-sm"><span class="glyphicon glyphicon-eye-close c-f"></span> 权限配置</a>
                        {/if}
                        <a  href="javascript:;" data-element="list-{$dataVar.user_id}" data-url="/{$u}?c=Del&id={$dataVar.user_id}" class="btn btn-danger btn-sm btn-del-action"><span class="glyphicon glyphicon-remove c-f"></span> 删除</a>
                    </td>
                </tr>
                {/foreach}
                {/if}
                </tbody>
            </table>
            <div class="pagemenu">{$pagelist}</div>
        </div>
    </div>
</div>