<div class="content">
    <div class="bg-f">
        <h2 class="p20">站点权限设置
            <span class="fr">
                <button type="button" class="btn btn-primary ml10 bakFromurl">返回</button>
            </span>
        </h2>
        <div class="p20 f14">
            <div class="form-group">
                <form action="/UsersManage/Limits?id=2" method="get" accept-charset="utf-8">
                    <input name="user_id" type="hidden" value="" >
                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tr>
                            <td width="4%">搜索：</td>
                            <td width="15%"><input class="form-control input-sm" placeholder="请输入站点名称关键词" value="{$datatype['keyword']}" type="text" name="keyword"></td>
                            <td width="63%"><button type="submit" class="btn btn-primary ml10"><span class="glyphicon glyphicon-search c-f"></span> 搜索</button> </td>
                        </tr>
                    </table>
                </form>
            </div>
            <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table tc table-hover table-bordered">
                <thead>
                <tr>
                    <th>序号</th>
                    <th>站点名称</th>
                    <th>排序</th>
                    <th>开发权限</th>
                    <th>操作</th>
                </tr>
                </thead>
                <tbody>
                {if $dataList}
                {foreach from=$dataList item=dataVar key=key}
                <tr>
                    <td>{$key+1}</td>
                    <td>{$dataVar.site_title}</td>
                    <td>{$dataVar.site_weight}</td>
                    <td>
                        {if $dataVar.thestatus == '1'}<a class="cp"><span class="glyphicon glyphicon-ok-circle f18 text-primary"></span></a>
                        {else}<a class="cp"><span class="glyphicon glyphicon-remove-circle f18 text-danger"></span></a>{/if}
                    </td>
                    <td align="left">
                        {if $dataVar.thestatus == '1'}
                        <a href="/{$u}/Module?site_id={$dataVar.site_id}&user_id={$user_id}" class="btn btn-primary btn-sm"><span class="glyphicon glyphicon-eye-close c-f"></span> 站点功能配置</a>
                        <a href="/{$u}?c=DelManage&site_id={$dataVar.site_id}&user_id={$user_id}" class="btn btn-danger btn-sm"><span class="glyphicon glyphicon-export c-f"></span> 取消站点权限</a>
                        {else}
                        <a href="/{$u}?c=Manage&site_id={$dataVar.site_id}&user_id={$user_id}" class="btn btn-success btn-sm"><span class="glyphicon glyphicon-saved c-f"></span> 开放站点权限</a>
                        {/if}
                    </td>
                </tr>
                {/foreach}
                {/if}
                </tbody>
            </table>
            <div class="pagemenu">{$pagelist}</div>
        </div>
    </div>
</div>