<div class="content">
    <div class="bg-f">
        <h2 class="p20">{$moduleOne.module_name}
        </h2>
        <div class="p20 f14">
            <div class="form-group">
                <form action="/{$u}" method="get" accept-charset="utf-8">
                    <input name="site_id" value="{$websites.site_id}" type="hidden">
                    <input name="company_id" value="{$datatype.company_id}" type="hidden">
                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tr>
                            <td width="4%">搜索：</td>
                            <td width="15%">
                                <input name="keyword" class="form-control input-sm" placeholder="请输入学生信息、卡号" value="{$datatype['keyword']}" type="text">
                            </td>
                            <td width="63%"><button type="submit" class="btn btn-primary ml10"><span class="glyphicon glyphicon-search c-f"></span> 搜索</button> </td>
                        </tr>
                    </table>
                </form>
            </div>
            <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table tc table-hover table-bordered">
                <thead>
                <tr>
                    <th>ID</th>
                    <th>产品名称</th>
                    <th>产品编号</th>
                    <th>卡号</th>
                    <th>校区名称</th>
                    <th>校区编号</th>
                    <th>学生名称</th>
                    <th>学生编号</th>
                    <th>是否禁用</th>
                    <th>权限截止日期</th>
                    <th>创建时间</th>
                    <th>操作</th>
                </tr>
                </thead>
                <tbody>
                {if $dataList}
                {foreach from=$dataList item=dataVar}
                <tr id="list-{$dataVar.apppropermislog_id}">
                    <td>{$dataVar.apppropermislog_id}</td>
                    <td>{$dataVar.apppropermis_name}</td>
                    <td>{$dataVar.apppropermis_code}</td>
                    <td>{$dataVar.apppropermis_authcode}</td>
                    <td>{$dataVar.school_cnname}</td>
                    <td>{$dataVar.school_branch}</td>
                    <td>{$dataVar.student_cnname}</td>
                    <td>{$dataVar.student_branch}</td>
                    <td>{if $dataVar.apppropermislog_isenabled==0}
                        否
                        {else}
                        是
                        {/if}
                    </td>
                    <td>{$dataVar.apppropermislog_endday}</td>
                    <td>{$dataVar.apppropermislog_createtime|date_format:'%Y-%m-%d'}</td>
                    <td align="left">
                        <a href="javascript:;" data-element="list-{$dataVar.apppropermislog_id}" data-tiptitle="你确定解除此项产品权限吗？" data-url="/{$u}?c=Relieve&id={$dataVar.apppropermislog_id}" class="btn btn-danger btn-sm btn-confirm-action">
                            <span class="glyphicon glyphicon-remove c-f"></span> 解除</a>
                    </td>
                </tr>
                {/foreach}
                {/if}
                </tbody>
            </table>
            <div class="pagemenu">{$pagelist}</div>
        </div>
    </div>
</div>