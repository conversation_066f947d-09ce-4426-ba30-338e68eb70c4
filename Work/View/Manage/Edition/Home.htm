<div class="content">
    <div class="bg-f">
        <h2 class="p20">{$moduleOne.module_name}
            <span class="fr">
                <a href="/{$u}/Add?site_id={$websites.site_id}" class="btn btn-success dropdown-toggle btn-demo-space">+新增数据</a>
                <a class="btn btn-primary bakFromurl ml10"><span class="glyphicon glyphicon-share-alt c-f"></span> 返回</a>
            </span>
        </h2>
        <div class="p20 f14">
            <div class="form-group">
                <form action="{$moduleOne.module_link}" method="get" accept-charset="utf-8">
                    <input name="site_id" value="{$websites.site_id}" type="hidden">
                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tr>
                            <td width="4%">搜索：</td>
                            <td width="15%">
                                <input name="keyword" class="form-control input-sm" placeholder="请输入版本名称/版本编号" value="{$datatype['keyword']}" type="text">
                            </td>
                            <td width="63%"><button type="submit" class="btn btn-primary ml10"><span class="glyphicon glyphicon-search c-f"></span> 搜索</button> </td>
                        </tr>
                    </table>
                </form>
            </div>
            <table width="100%"  class="table tc table-hover table-bordered">
                <thead>
                <tr>
                    <th>版本名称</th>
                    <th>版本编号</th>
                    <th>适配产品数</th>
                    <th>操作</th>
                </tr>
                </thead>
                <tbody>
                {if $dataList}
                {foreach from=$dataList item=dataVar}
                <tr id="list-{$dataVar.edition_id}">
                    <td>{$dataVar.edition_name}</td>
                    <td>{$dataVar.edition_code}</td>
                    <td>{$dataVar.num}</td>
                    <td align="left">
                        {if $dataVar.edition_id>5}
                        <a href="/{$u}/Edit?edition_id={$dataVar.edition_id}&site_id={$websites.site_id}" class="btn btn-primary btn-sm">
                            <span class="glyphicon glyphicon-pencil c-f"></span> 编辑</a>
                        {/if}
                        <a href="/{$u}/SetProduct?edition_id={$dataVar.edition_id}&site_id={$websites.site_id}" class="btn btn-primary btn-sm">
                            <span class="glyphicon glyphicon-book c-f"></span> 适配产品</a>

                        {if $dataVar.edition_id>5}
                        <a href="javascript:;" data-element="list-{$dataVar.edition_id}" data-url="/{$u}?c=Del&id={$dataVar.edition_id}" class="btn btn-danger btn-sm btn-del-action">
                            <span class="glyphicon glyphicon-remove c-f"></span> 删除</a>
                        {/if}

                    </td>
                </tr>
                {/foreach}
                {/if}
                </tbody>
            </table>
            <div class="pagemenu">{$pagelist}</div>
        </div>
    </div>
</div>