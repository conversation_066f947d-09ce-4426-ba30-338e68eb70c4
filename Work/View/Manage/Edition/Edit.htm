<div class="content">
    <div id="container">
        <div class="bg-f row py20">
            <form action="/{$u}?c={$act}" method="post" enctype="application/x-www-form-urlencoded" class="AjaxForm">
                <input name="site_id" type="hidden" value="{$websites.site_id}">
                <input name="edition_id" type="hidden" value="{$dataVar.edition_id}">
                <h2 class="p20">{$moduleOne.module_name}<span class="fr">
                <button type="submit" class=" btn btn-success btn-cons"><i class="fa fa-check"></i>提交保存</button>
                <button type="button" class="btn btn-primary ml10 bakFromurl">返回</button></span>
                </h2>
                <div class="col-md-8" id="Form-Box-Operating">
                    <div class="pl20 py20 f14">
                        <div class="tab-content">
                            <div class="form-group col-md-4">
                                <label for="edition_name">版本名称</label>
                                <input name="edition_name" id="edition_name" value="{$dataVar.edition_name}" reg="[^ \f\n\r\t\v]" tip="请填写版本名称" type="text" class="form-control" placeholder="请输入版本名称">
                            </div>
                            <div class="form-group col-md-4">
                                <label for="edition_code">版本编号</label>
                                <input name="edition_code" id="edition_code" value="{$dataVar.edition_code}" reg="[^ \f\n\r\t\v]" tip="请填写版本编号" type="text" class="form-control" placeholder="请输入版本编号">
                            </div>

                            <div class="clear"></div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>