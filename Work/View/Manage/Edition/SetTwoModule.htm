<div class="content">
    <div class="bg-f">
        <h2 class="p20">
            <span class="fr">
                <button type="button" class="btn btn-primary ml10 bakFromurl">返回</button>
            </span>{$moduleOne.module_name}
        </h2>
        <div class="p20 f14">


            <div class="form-group">
                <form role="form" action="/{$u}?c=batchSetModule" method="post" enctype="application/x-www-form-urlencoded" class="AjaxForm">
                    <input type="hidden" name="product_id" value="{$product_id}">
                    <input type="hidden" name="edition_id" value="{$edition_id}">
                    <input name="site_id" value="{$websites.site_id}" type="hidden">
                    <table width="100%" border="0" cellspacing="0" cellpadding="0"
                           class="table tc table-hover table-bordered">
                        <thead>
                        <tr>
                            <th>选择</th>
                            <th>模块ID</th>
                            <th>模块名称</th>
                            <th>操作</th>
                        </tr>
                        </thead>
                        <tbody>
                        {if $dataList}
                        {foreach from=$dataList item=dataVar key='k'}
                        <tr id="list-{$dataVar.module_id}">
                            <td>
                                <span class="select-chkBox">
                                    <input name="tab_list[]" value="{$dataVar.module_id}" type="checkbox">
                                    <em></em>
                                </span>
                            </td>
                            <td>{$dataVar.module_id}</td>
                            <td>{$dataVar.module_name}</td>
                            <td>
                                {if $dataVar.status >= 1}
                                <a href="javascript:;" data-url="/{$u}?c=UnSetModule&module_id={$dataVar.module_id}&product_id={$product_id}&edition_id={$edition_id}&site_id={$websites.site_id}" class="btn btn-danger btn-send-action">
                                    <span class="glyphicon c-f"></span> 取消适配</a>
                                {else}
                                <a href="javascript:;" data-url="/{$u}?c=SetModule&module_id={$dataVar.module_id}&product_id={$product_id}&edition_id={$edition_id}&site_id={$websites.site_id}" class="btn btn-primary btn-sm btn-send-action">
                                    <span class="glyphicon  c-f"></span> 适配</a>
                                {/if}
                            </td>
                        </tr>
                        {/foreach}
                        {/if}

                        <tr>
                            <td>全选
                                <input name="form-field-checkbox" id="Choice_All" type="checkbox" class="ace">
                                <span class="lbl"></span></td>
                            <td colspan="14">
                                <div class="col-md-2">
                                    <select name="type" id="type" class="form-control">
                                        <option value="1">批量适配</option>
                                        <option value="2">批量取消</option>
                                    </select>
                                </div>
                                <button type="submit" class="fl btn btn-primary ml20">
                                    批量操作
                                </button>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </form>
                <div class="pagemenu">{$pagelist}</div>
            </div>
        </div>
    </div>
</div>