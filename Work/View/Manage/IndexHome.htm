<div class="content">
    <div id="container">
        {if $iuser.user_limitsinc == '1'}
        <div class="row spacing-bottom">
            <div class="col-md-3 col-sm-6">
                <a href="/SiteManage">
                <div class="tiles blue added-margin">
                    <div class="tiles-body">
                        <p class="f16 pt10 pb30"><span class="animate-number f30 fr" data-value="{$alltotal.website}" data-animation-duration="1200">0</span>站点总数</p>
                        <p>当前可管理应用网站统计</p>
                    </div>
                </div>
                </a>
            </div>
            <div class="col-md-3 col-sm-6">
                <div class="tiles green added-margin">
                    <div class="tiles-body">
                        <p class="f16 pt10 pb30"><span class="animate-number f30 fr" data-value="16" data-animation-duration="1200">0</span>未读消息</p>
                        <p>系统应用实时提示提醒消息</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3 col-sm-6">
                <a href="/UsersManage">
                <div class="tiles red added-margin">
                    <div class="tiles-body">
                        <p class="f16 pt10 pb30"><span class="animate-number f30 fr" data-value="{$alltotal.user}" data-animation-duration="1200">0</span>管理员</p>
                        <p>站群系统全部管理员数量统计</p>
                    </div>
                </div>
                </a>
            </div>
            <div class="col-md-3 col-sm-6">
                <div class="tiles purple added-margin">
                    <div class="tiles-body">
                        <p class="f16 pt10 pb30"><span class="animate-number f30 fr" data-value="{$alltotal.feedback}" data-animation-duration="1200">0</span>未读反馈</p>
                        <p>各站点访客信息反馈统计</p>
                    </div>
                </div>
            </div>
        </div>
        {/if}
        <div class="row">
            <div class="col-md-9">
                <div class="row">
                    <div class="">
                        {if $websiteList}
                        {foreach from=$websiteList item=websiteVar}
                        <div class="col-md-3 mb16" style="padding-right:0;">
                        <div class="bg-f index-weblist">
                            <div class="img" style="height: 120px;overflow:hidden;">
                                {if $websiteVar.site_img !=''}
                                <img src="{$websiteVar.site_img}" width="100%" height="">
                                {else}
                                <div class="webtitle px16">{$websiteVar.site_title}</div>
                                {/if}
                                {if $websiteVar.site_type =='1'}
                                <span class="px16">外部站点</span>
                                {else}
                                <span class="px16">内部站点</span>
                                {/if}
                            </div>
                            <div class="text p10">
                                <h2 class="f18 mb10"><a href="{$websiteVar.site_url}" target="_blank">{$websiteVar.site_title}</a></h2>
                                <p class="f14 mb10" style="overflow:hidden;">{$websiteVar.site_details}</p>
                                <p align="right">
                                    <a href="/SiteManage/Edit?id={$websiteVar.site_id}" class="btn btn-warning btn-sm">编 辑</a>
                                    {if $websiteVar.site_type !='1'}
                                    <a href="/Website/id-{$websiteVar.site_id}" class="btn btn-warning btn-sm">管理内容</a>
                                    {else}
                                    <a href="{$websiteVar.site_manageurl}" target="_blank" class="btn btn-warning btn-sm">管理内容</a>
                                    {/if}
                                </p>
                            </div>
                            </div>
                        </div>
                        {/foreach}
                        {/if}
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="bg-f cz-rz p18">
                    <div class="f14 pb18">操作日志</div>
                    <ul>
                        {if $weblogList}
                        {foreach from=$weblogList item=weblogVar key=key}
                        <li class="bg{$key%4+1}">
                            <div class="p18">
                                <div class="user-info mb18">
                                    <div class="img fl"><img src="{if $weblogVar.user_imghead != ''}{$weblogVar.user_imghead}{else}{$ImgUrl}imghead.png{/if}" width="35" height="35"></div>
                                    <div class="text pt5">
                                        <p class="f14">{$weblogVar.user_cnname}</p>
                                    </div>
                                    <div align="right" class="fr c-8">{$weblogVar.tb_time|date_format:"%Y-%m-%d %H:%M:%S"}</div>
                                    <div class="clear"></div>
                                </div>
                                <p>站点：{$weblogVar.site_title}，模块：{$weblogVar.module_name}，操作类型：{$weblogVar.actiontype}，备注：{$weblogVar.tb_content}</p>
                            </div>
                        </li>
                        {/foreach}
                        {/if}
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>