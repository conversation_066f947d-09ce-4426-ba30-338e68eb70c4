<div class="content">
    <div class="bg-f">
        <h2 class="p20">{$moduleOne.module_name}
            <span class="fr">
                <a class="btn btn-primary bakFromurl ml10"><span class="glyphicon glyphicon-share-alt c-f"></span> 返回</a>
            </span>
        </h2>
        <div class="p20 f14">
            <div class="form-group">
                <form action="{$moduleOne.module_link}" method="get" accept-charset="utf-8">
                    <input name="site_id" value="{$websites.site_id}" type="hidden">
                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tr>
                            <td width="4%">搜索：</td>
                            <td width="10%">
                                <select name="models_type" class="form-control">
                                    <option value="">请选择收费类型</option>
                                    <option value="0" {if $datatype.models_type === '0'}selected{/if}>美语</option>
                                    <option value="1" {if $datatype.models_type === '1'}selected{/if}>课辅</option>
                                    <option value="2" {if $datatype.models_type === '2'}selected{/if}>托育</option>
                                </select>
                            </td>
                            <td width="10%">
                                <select name="models_spl" class="form-control">
                                    <option value="">请选择是否同步</option>
                                    <option value="0" {if $datatype.models_spl === '0'}selected{/if}>未同步</option>
                                    <option value="1" {if $datatype.models_spl === '1'}selected{/if}>已同步</option>
                                </select>
                            </td>
                            <td width="50%"><button type="submit" class="btn btn-primary ml10"><span class="glyphicon glyphicon-search c-f"></span> 搜索</button> </td>
                        </tr>
                    </table>
                </form>
            </div>
            <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table tc table-hover table-bordered">
                <thead>
                <tr>
                    <th>ID</th>
                    <th>商品名称</th>
                    <th>规格ID</th>
                    <th>收费类型</th>
                    <th>规格编号</th>
                    <th>规格名称</th>
                    <th>原价</th>
                    <th>协议价</th>
                    <th>课次</th>
                    <th>单价</th>
                    <th>是否同步</th>
                    <th>适配学校</th>
                    <th>操作</th>
                </tr>
                </thead>
                <tbody>
                {if $dataList}
                {foreach from=$dataList item=dataVar}
                <tr id="list-{$dataVar.agreemen_id}">
                    <td>{$dataVar.agreemen_id}</td>
                    <td>{$dataVar.goods_name}</td>
                    <td>{$dataVar.models_id}</td>
                    <td>{if $dataVar.models_type == '0'}美语{elseif $dataVar.models_type == '1'}课辅{elseif $dataVar.models_type == '2'}托育{/if}</td>
                    <td>{$dataVar.models_number}</td>
                    <td>{$dataVar.models_name}</td>
                    <td>{$dataVar.models_originalprice}</td>
                    <td>{$dataVar.models_vipprice}</td>
                    <td>{$dataVar.models_unitpiece}</td>
                    <td>{$dataVar.models_unitprice}</td>
                    <td>{if $dataVar.models_spl == '0'}未同步{else}已同步{/if}</td>
                    <td>{$dataVar.schoolbranch}</td>
                    <td align="left">
                        {if $dataVar.models_spl == 0}
                        <a href="javascript:;" data-url="/{$u}?c=Handle&agreemen_id={$dataVar.agreemen_id}" class="btn btn-primary ml10 btn-send-action">
                            <span class="glyphicon glyphicon-link"></span> 同步</a>
                        {/if}
                        <a href="javascript:;" data-element="list-{$dataVar.agreemen_id}" data-url="/{$u}?c=Del&id={$dataVar.agreemen_id}" class="btn btn-danger btn-sm btn-del-action">
                            <span class="glyphicon glyphicon-remove c-f"></span> 删除</a>
                    </td>
                </tr>
                {/foreach}
                {/if}
                </tbody>
            </table>
            <div class="pagemenu">{$pagelist}</div>
        </div>
    </div>
</div>