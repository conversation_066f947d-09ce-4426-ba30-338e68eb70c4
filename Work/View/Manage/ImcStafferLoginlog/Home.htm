<div class="content">
    <div class="bg-f">
        <h2 class="p20">{$moduleOne.module_name}
            <span class="fr">
                <a class="btn btn-primary bakFromurl ml10"><span class="glyphicon glyphicon-share-alt c-f"></span> 返回</a>
            </span>
        </h2>
        <div class="p20 f14">
            <div class="form-group">
                <form action="{$moduleOne.module_link}" method="get" accept-charset="utf-8">
                    <input name="site_id" value="{$websites.site_id}" type="hidden">
                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tr>
                            <td width="4%">搜索：</td>
                            <td width="10%">
                                <select name="loginlog_type" class="form-control">
                                    <option value="">登录端口</option>
                                    <option value="0" {if $datatype.loginlog_type == '0'}selected{/if}>总入口</option>
                                    <option value="1" {if $datatype.loginlog_type == '1'}selected{/if}>集团</option>
                                    <option value="2" {if $datatype.loginlog_type == '2'}selected{/if}>校务</option>
                                    <option value="3" {if $datatype.loginlog_type == '3'}selected{/if}>CRM</option>
                                    <option value="4" {if $datatype.loginlog_type == '4'}selected{/if}>教务</option>
                                    <option value="5" {if $datatype.loginlog_type == '5'}selected{/if}>助教</option>
                                    <option value="6" {if $datatype.loginlog_type == '6'}selected{/if}>助学</option>
                                </select>
                            </td>
                            <td width="1%" ></td>
                            <td width="10%">
                                <select name="loginlog_source" class="form-control">
                                    <option value="">用户端来源</option>
                                    <option value="0" {if $datatype.loginlog_source == '0'}selected{/if}>PC</option>
                                    <option value="1" {if $datatype.loginlog_source == '1'}selected{/if}>手机</option>
                                </select>
                            </td>
                            <td width="1%" ></td>
                            <td width="15%"><input name="keyword" class="form-control input-sm" placeholder="请输入IP/职工名称/职工编号" value="{$datatype['keyword']}" type="text"></td>
                            <td width="63%"><button type="submit" class="btn btn-primary ml10"><span class="glyphicon glyphicon-search c-f"></span> 搜索</button> </td>
                        </tr>
                    </table>
                </form>
            </div>
            <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table tc table-hover table-bordered">
                <thead>
                <tr>
                    <th>ID</th>
                    <th>职工ID</th>
                    <th>职工名</th>
                    <th>职工编号</th>
                    <th>登录IP</th>
                    <th>登录时间</th>
                </tr>
                </thead>
                <tbody>
                {if $dataList}
                {foreach from=$dataList item=dataVar}
                <tr id="list-{$dataVar.loginlog_id}">
                    <td>{$dataVar.loginlog_id}</td>
                    <td>{$dataVar.staffer_id}</td>
                    <td>{$dataVar.staffer_cnname}</td>
                    <td>{$dataVar.staffer_branch}</td>
                    <td>{$dataVar.loginlog_lastip}</td>
                    <td>{$dataVar.loginlog_time|date_format:"%Y-%m-%d %H:%M:%S"}</td>
                </tr>
                {/foreach}
                {/if}
                </tbody>
            </table>
            <div class="pagemenu">{$pagelist}</div>
        </div>
    </div>
</div>