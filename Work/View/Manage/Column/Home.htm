<div class="content">
    <div class="bg-f">
        <h2 class="p20">栏目管理
            <span class="fr">
                <a href="/{$u}/Add?site_id=14" class="btn btn-success dropdown-toggle btn-demo-space">+添加栏目</a>
                <button type="button" class="btn btn-primary ml10 bakFromurl">返回</button>
            </span>
        </h2>
        <div class="p20 f14">
            <div class="form-group">
                <form action="?" method="get" accept-charset="utf-8">
                    <input type="hidden" name="site_id" value="{$websites.site_id}">
                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tr>
                            <td width="4%">分类：</td>
                            <td width="10%">
                                <select name="column_type" class="form-control">
                                    <option value="0">选择类型</option>
                                    {VariableList string='ColumnType' listName='tblist'}
                                    {if $tblist}
                                    {foreach from=$tblist item=tbvar key=key}
                                    <option value="{$tbvar.list_parameter}"{if $websites.column_type == $tbvar.list_parameter} selected{/if}>{$tbvar.list_name}</option>
                                    {/foreach}
                                    {/if}
                                    {/VariableList}
                                </select>
                            </td>
                            <td width="1%"></td>
                            <td width="4%">搜索：</td>
                            <td width="15%"><input class="form-control input-sm" id="inputEmail3" placeholder="请输入栏目名" value="{$websites.keyword}" type="text" name="keyword"></td>
                            <td width="63%"><button type="submit" class="btn btn-primary ml10"><span class="glyphicon glyphicon-search c-f"></span> 搜索</button> </td>
                        </tr>
                    </table>
                </form>
            </div>
            <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table tc table-hover table-bordered">
                <thead>
                <tr>
                    <th width="15%">展开/缩放</th>
                    <th>序号</th>
                    <th width="12%">栏目名</th>
                    <th>栏目类型</th>
                    <th>所属站点</th>
                    <th>连接标示</th>
                    <th>栏目标题</th>
                    <th width="25%">操作</th>
                </tr>
                </thead>
                <tbody>
                {if $dataList}
                {foreach from=$dataList item=dataVar key=key}
                <tr class="OneMenu" id="list-{$dataVar.column_id}">
                    <td><a href="javascript:;" class="glyphicon glyphicon-eye-open OneMenuClick f18 color2"></a></td>
                    <td>{$dataVar.column_id}</td>
                    <td>{$dataVar.column_name}</td>
                    <td>
                        {VariableList string='ColumnType' listName='tblist'}
                        {if $tblist}
                        {foreach from=$tblist item=tbvar key=key}
                        {if $tbvar.list_parameter==$dataVar.column_type}{$tbvar.list_name}{/if}
                        {/foreach}
                        {/if}
                        {/VariableList}
                    </td>
                    <td>{$dataVar.site_id}</td>
                    <td>{$dataVar.column_outlink}</td>
                    <td>{$dataVar.column_title}</td>
                    <td align="left">
                        <a href="/{$u}/Edit?id={$dataVar.column_id}&site_id={$smarty.get.site_id}" class="btn btn-primary btn-sm"><span class="glyphicon glyphicon-pencil c-f"></span> 编辑</a>
                        <a href="/{$u}/Add?id={$dataVar.column_id}&site_id={$smarty.get.site_id}" class="btn btn-primary btn-sm"> + 新增二级栏目</a>
                        <a  href="javascript:;" data-element="list-{$dataVar.column_id}" data-url="/{$u}?c=Del&id={$dataVar.column_id}" class="btn btn-danger btn-sm btn-del-action"><span class="glyphicon glyphicon-remove c-f"></span> 删除</a>
                    </td>
                </tr>

                    {if $dataVar.twocat}
                    {foreach from=$dataVar.twocat item=twoVar}
                    <tr class="TwoMenu none" id="list-{$twoVar.column_id}">
                        <td>　　<a href="javascript:;" class="glyphicon glyphicon-eye-open TwoMenuClick f18 color2"></a></td>
                        <td>{$twoVar.column_id}</td>
                        <td>{$twoVar.column_name}</td>
                        <td>
                            {VariableList string='ColumnType' listName='tblist'}
                            {if $tblist}
                            {foreach from=$tblist item=tbvar key=key}
                            {if $tbvar.list_parameter==$twoVar.column_type}{$tbvar.list_name}{/if}
                            {/foreach}
                            {/if}
                            {/VariableList}
                        </td>
                        <td>{$twoVar.site_id}</td>
                        <td>{$twoVar.column_outlink}</td>
                        <td>{$twoVar.column_title}</td>
                        <td align="left">
                            <a href="/{$u}/Edit?id={$twoVar.column_id}&site_id={$smarty.get.site_id}" class="btn btn-primary btn-sm"><span class="glyphicon glyphicon-pencil c-f"></span> 编辑</a>
                            {if $twoVar.column_type=='about'}
                            <a href="/{$u}/About?id={$twoVar.column_id}&site_id={$smarty.get.site_id}" class="btn btn-primary btn-sm"><span class="glyphicon glyphicon-qrcode c-f"></span> 简介</a>
                            {elseif $twoVar.column_type=='article'}
                            <a href="/Article?cpat_twocolumn={$twoVar.column_id}&site_id={$smarty.get.site_id}" class="btn btn-primary btn-sm"><span class="glyphicon glyphicon-list-alt c-f"></span> 文章</a>
                            {/if}

                            <a href="/{$u}/Add?id={$twoVar.column_id}&site_id={$smarty.get.site_id}" class="btn btn-primary btn-sm"> + 新增三级栏目</a>
                            <a  href="javascript:;" data-element="list-{$twoVar.column_id}" data-url="/{$u}?c=Del&id={$twoVar.column_id}" class="btn btn-danger btn-sm btn-del-action"><span class="glyphicon glyphicon-remove c-f"></span> 删除</a>
                        </td>
                    </tr>
                        {if $twoVar.threecat}
                        {foreach from=$twoVar.threecat item=threeVar}
                        <tr class="ThreeMenu none" id="list-{$threeVar.class_id}">
                            <td>　　　33</td>
                            <td>{$threeVar.column_id}</td>
                            <td>{$threeVar.column_name}</td>
                            <td>
                                {VariableList string='ColumnType' listName='tblist'}
                                {if $tblist}
                                {foreach from=$tblist item=tbvar key=key}
                                {if $tbvar.list_parameter==$threeVar.column_type}{$tbvar.list_name}{/if}
                                {/foreach}
                                {/if}
                                {/VariableList}
                            </td>
                            <td>{$threeVar.site_id}</td>
                            <td>{$threeVar.column_outlink}</td>
                            <td>{$threeVar.column_title}</td>
                            <td align="left">
                                <a href="/{$u}/Edit?id={$threeVar.column_id}&site_id={$smarty.get.site_id}" class="btn btn-primary btn-sm"><span class="glyphicon glyphicon-pencil c-f"></span> 编辑</a>
                                {if $threeVar.column_type=='about'}
                                <a href="/{$u}/About?id={$threeVar.column_id}&site_id={$smarty.get.site_id}" class="btn btn-primary btn-sm"><span class="glyphicon glyphicon-qrcode c-f"></span> 简介</a>
                                {elseif $threeVar.column_type=='article'}
                                <a href="/Article?cpat_threecolumn={$threeVar.column_id}&site_id={$smarty.get.site_id}" class="btn btn-primary btn-sm"><span class="glyphicon glyphicon-list-alt c-f"></span> 文章</a>
                                {/if}
                                <a  href="javascript:;" data-element="list-{$threeVar.column_id}" data-url="/{$u}?c=Del&id={$threeVar.column_id}" class="btn btn-danger btn-sm btn-del-action"><span class="glyphicon glyphicon-remove c-f"></span> 删除</a>
                            </td>
                        </tr>
                        {/foreach}
                        {/if}
                    {/foreach}
                    {/if}
                {/foreach}
                {/if}
                </tbody>
            </table>
        </div>
    </div>
</div>
