<div class="content">
    <div id="container">
        <div class="bg-f row py20">
            <form action="/{$u}?c={$act}&site_id{$smarty.get.site_id}" role="form" method="post" enctype="application/x-www-form-urlencoded" class="AjaxForm form-horizontal">
                <input name="column_id" type="hidden" value="{$dataVar.column_id}">
                <h2 class="p20">栏目管理<span class="fr">
                <button type="submit" class=" btn btn-success btn-cons"><i class="fa fa-check"></i>提交保存</button>
                <button type="button" class="btn btn-primary ml10 bakFromurl">返回</button></span>
                </h2>
                <div class="col-md-9" id="Form-Box-Operating">
                    <div class="pl20 py20 f14">
                        <div class="form-group">
                            <label for="column_name" class="col-sm-1 control-label">栏目名</label>
                            <div class="col-sm-3">
                                <input name="column_name" id="column_name" value="{$dataVar.column_name}" type="text" class="form-control" placeholder="请输入栏目名">
                            </div>

                            <label for="column_type" class="col-sm-1 control-label">栏目类型</label>
                            <div class="col-sm-3">
                                {if $act='Add' && $smarty.get.id==''}
                                <select name="column_type" id="column_type" class="form-control">
                                    <option value="column">栏目标示</option>
                                </select>
                                {else if $dataVar.column_fatherid==='0'}
                                <select name="column_type" id="" class="form-control">
                                    <option value="column">栏目标示</option>
                                </select>
                                {else}
                                <select name="column_type" id="" class="form-control">
                                    <option value="0">栏目类型</option>
                                    {VariableList string='ColumnType' listName='tblist'}
                                    {if $tblist}
                                    {foreach from=$tblist item=tbvar key=key}
                                    <option value="{$tbvar.list_parameter}"{if $dataVar.column_type == $tbvar.list_parameter} selected{/if}>{$tbvar.list_name}</option>
                                    {/foreach}
                                    {/if}
                                    {/VariableList}
                                </select>
                                {/if}

                            </div>

                            <label for="column_weight" class="col-sm-1 control-label">排序</label>
                            <div class="col-sm-3">
                                <input name="column_weight" id="column_weight" value="{$dataVar.column_weight}" type="text" class="form-control" placeholder="请输入排序">
                            </div>

                            <input name="site_id" id="site_id" value="{if $dataVar.site_id}{$dataVar.site_id}{else}{$websites.site_id}{/if}" type="hidden" class="form-control" placeholder="">
                        </div>
                        <div class="form-group">
                            <!--<label for="column_fatherid" class="col-sm-2 control-label">上级栏目</label>-->
                            <!--<div class="col-sm-4">-->
                                {if $act='Add' && $smarty.get.id==''}
                                <input type="hidden" name="column_fatherid" value="0">
                                {elseif $act='Add' && $smarty.get.id!=''}
                                <input type="hidden" name="column_fatherid" value="{$smarty.get.id}">
                                <!--<select name="column_fatherid" id="column_fatherid" class="form-control">-->
                                    <!--<option value="0"  {if $dataVar.column_fatherid == '0'}selected{/if}>无</option>-->
                                    <!--{foreach from=$columnArr item=columnVar}-->
                                    <!--<option value="{$columnVar.column_id}"  {if $dataVar.column_fatherid == $columnVar.column_id}selected{/if}>{$columnVar.column_name}</option>-->
                                    <!--{/foreach}-->
                                <!--</select>-->
                                {/if}

                            <!--</div>-->
                        </div>

                        <div class="form-group">

                            <label for="column_outlink" class="col-sm-1 control-label">连接标示</label>
                            <div class="col-sm-3">
                                <input name="column_outlink" id="column_outlink" value="{$dataVar.column_outlink}" type="text" class="form-control" placeholder="请输入连接标示">
                            </div>
                            <label for="column_static" class="col-sm-1 control-label">伪静态</label>
                            <div class="col-sm-3">
                                <input name="column_static" id="column_static" value="{$dataVar.column_static}" type="text" class="form-control" placeholder="请输入自定义伪静态">
                            </div>
                            <label for="column_pctemplate" class="col-sm-1 control-label">模板</label>
                            <div class="col-sm-3">
                                <input name="column_pctemplate" id="column_pctemplate" value="{$dataVar.column_pctemplate}" type="text" class="form-control" placeholder="请输入模板">
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="column_apptemplate" class="col-sm-1 control-label">移动模板文件</label>
                            <div class="col-sm-3">
                                <input name="column_apptemplate" id="column_apptemplate" value="{$dataVar.column_apptemplate}" type="text" class="form-control" placeholder="请输入移动模板文件">
                            </div>
                            <label for="column_pagearticlenums" class="col-sm-1 control-label">每页文章数量</label>
                            <div class="col-sm-3">
                                <input name="column_pagearticlenums" id="column_pagearticlenums" value="{$dataVar.column_pagearticlenums}" type="text" class="form-control" placeholder="请输入每页文章数量">
                            </div>
                            <label for="" class="col-sm-1 control-label">是否栏目显示</label>
                            <div class="col-sm-3">
                                <select name="column_showsinc" id="column_showsinc" class="form-control">
                                    <option value="0"  {if $dataVar.column_showsinc == '0'}selected{/if}>否</option>
                                    <option value="1"  {if $dataVar.column_showsinc == '1'}selected{/if}>是</option>
                                </select>
                            </div>

                        </div>

                        <div class="form-group">
                            <label for="" class="col-sm-1 control-label">打开方式</label>
                            <div class="col-sm-3">

                                <select name="column_open" id="column_open" class="form-control">
                                    <option value="_self"  {if $dataVar.column_open == '_self'}selected{/if}>当前窗口打开</option>
                                    <option value="_blank"  {if $dataVar.column_open == '_blank'}selected{/if}>新窗口打开</option>
                                </select>
                            </div>


                            <label for="" class="col-sm-1 control-label">访问限制</label>
                            <div class="col-sm-3">
                                <select name="column_access" id="column_access" class="form-control">
                                    <option value="0"  {if $dataVar.column_access == '0'}selected{/if}>一级独立访问</option>
                                    <option value="1"  {if $dataVar.column_access == '1'}selected{/if}>二级直接访问</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="column_title" class="col-sm-1 control-label">栏目标题</label>
                            <div class="col-sm-5">
                                <input name="column_title" id="column_title" value="{$dataVar.column_title}" type="text" class="form-control" placeholder="请输入栏目标题">
                            </div>
                            <label for="column_key" class="col-sm-1 control-label">栏目关键词</label>
                            <div class="col-sm-5">
                                <input name="column_key" id="column_key" value="{$dataVar.column_key}" type="text" class="form-control" placeholder="请输入栏目关键词">
                            </div>

                        </div>

                        <div class="form-group">
                            <label for="column_connet" class="col-sm-1 control-label">栏目介绍</label>
                            <div class="col-sm-11">
                                <textarea name="column_connet" id="column_connet" class="form-control" rows="5">{$dataVar.column_connet}</textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
