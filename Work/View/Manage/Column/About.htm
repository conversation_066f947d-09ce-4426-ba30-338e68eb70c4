<div class="content">
    <div id="container">
        <div class="bg-f row py20">
            <form action="/{$u}?c=about" role="form" method="post" enctype="application/x-www-form-urlencoded" class="AjaxForm form-horizontal">
                <input name="about_id" type="hidden" value="{$dataVar.about_id}">
                <input name="site_id" type="hidden" value="{$websites.site_id}">
                <h2 class="p20">栏目简介管理<span class="fr">
                <button type="submit" class=" btn btn-success btn-cons"><i class="fa fa-check"></i>提交保存</button>
                <button type="button" class="btn btn-primary ml10 bakFromurl">返回</button></span>
                </h2>
                <div class="col-md-6" id="Form-Box-Operating">
                    <div class="pl20 py20 f14">
                        <div class="form-group">
                            <label for="column_name" class="col-sm-2 control-label">栏目名</label>
                            <div class="col-sm-4">
                                <input name="column_name" id="column_name" disabled value="{$dataVar.column_name}" type="text" class="form-control" placeholder="请输入栏目名">
                                <input name="column_id" id="column_id" value="{$dataVar.column_id}" type="hidden">
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="column_name" class="col-sm-2 control-label">栏目简介</label>
                            <div class="col-md-10">
                                <div class="box-editor">
                                    <textarea name="about_content" id="qeditor_body" class="textarea qeditor" rows="3">{$dataVar.about_content}</textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
