<div class="content">
    <div class="bg-f">
        <h2 class="p20">{$moduleOne.module_name} -- 导出会员</h2>
        <div class="p20 f14">
            <div class="form-group">
                <!--<form action="/Worker" method="get" accept-charset="utf-8" enctype="application/x-www-form-urlencoded" class="AjaxForm">-->
                    <input name="site_id" type="hidden" id="site_id" value="{$websites.site_id}">
                    <input name="c" type="hidden" value="WorkerOrganExcelpre">
                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tr>
                            <td width="4%">机构：</td>
                            <td width="19%">
                                <select name="organ_id" id="organ_id" class="form-control chosen-select">
                                    <option value="">请选择所属分院</option>
                                    {if $organlist}
                                    {foreach from=$organlist item=organVar}
                                    <option value="{$organVar.organ_id}" {if $datatype.organ_id == $organVar.organ_id}selected{/if}>{$organVar.organ_name}</option>
                                    {/foreach}
                                    {/if}
                                </select>
                            </td>
                            <td width="10%">
                                <select name="excelpreType" id="excelpreType" class="form-control">
                                    <option value="1">只导出本机构</option>
                                    <option value="2">本机构和下属机构</option>
                                </select>
                            </td>
                            <td width="45%"><button type="button" class="btn btn-primary ml10" id="WorkerOrganExcelpre"><span class="glyphicon glyphicon-search c-f"></span> 导出</button> </td>
                        </tr>
                    </table>
                <!--</form>-->
            </div>
        </div>
    </div>
</div>
