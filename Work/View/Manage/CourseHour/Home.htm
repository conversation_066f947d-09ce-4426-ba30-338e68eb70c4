<div class="content">
    <div class="bg-f">
        <h2 class="p20">
            <span class="fr">

                <button type="button" class="btn btn-primary ml10 bakFromurl">返回</button>
            </span>{$moduleOne.module_name}
        </h2>
        <div class="p20 f14">
            <div class="form-group">
                <form action="/{$u}" method="get" accept-charset="utf-8">
                    <input name="site_id" value="{$websites.site_id}" type="hidden" />
                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tr>
                            <td width="3%">集团：</td>
                            <td width="15%" class="pr10">
                                <select name="company_id" id="company_id" class="form-control companyAjax">
                                    <option value="0">请选择所属集团</option>
                                    {if $datatype.company_id}
                                    <option value="{$datatype.company_id}" selected >{$datatype.company_cnname}</option>
                                    {/if}
                                </select>
                            </td>
                            <td width="3%">课程：</td>
                            <td width="15%">
                                <select name="course_id" id="course_id" class="form-control courseAjax">
                                    <option value="0">请选择所属课程</option>
                                    {if $datatype.course_id}
                                    <option value="{$datatype.course_id}" selected >{$datatype.course_cnname}</option>
                                    {/if}
                                </select>
                            </td>
                            <td width="3%"></td>
                            <td width="15%">
                                <!--<input class="form-control input-sm" id="inputEmail3" placeholder="课程名/课程编号"-->
                                                   <!--value="{$datatype['keyword']}" type="text" name="keyword"/>-->
                            </td>
                            <td width="63%">
                                <button type="submit" class="btn btn-primary ml10"><span class="glyphicon glyphicon-search c-f">搜索</span>
                                </button>
                            </td>
                        </tr>
                    </table>
                </form>
            </div>

            <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table tc table-hover table-bordered">
                <thead>
                <tr>
                    <th>ID</th>
                    <th>所属集团</th>
                    <th>集团授权号</th>
                    <th>所属班种</th>
                    <th>课程名称</th>
                    <th>课程编号</th>
                    <th>是否使用教务系统</th>
                    <th>操作</th>

                </tr>
                </thead>
                <tbody>
                {if $dataList}
                {foreach from=$dataList item=dataVar}
                <tr id="list-{$dataVar.coursehour_id}">
                    <td>{$dataVar.course_id}</td>
                    <td>{$dataVar.company_cnname}</td>
                    <td>{$dataVar.company_code}</td>
                    <td>{$dataVar.coursecat_cnname}/{$dataVar.coursecat_branch}</td>
                    <td>{$dataVar.course_cnname}</td>
                    <td>{$dataVar.course_branch}</td>
                    <td>
                        {if $dataVar.course_isuseeas ==1}
                        <a href="javascript:;" data-url="/{$u}?c=changeCourseEas&course_id={$dataVar.course_id}"
                           class="cp btn-state-send">
                            <span class="glyphicon glyphicon-ok-circle f18 text-primary"></span></a>
                        {else}
                        <a href="javascript:;" data-url="/{$u}?c=changeCourseEas&course_id={$dataVar.course_id}"
                           class="cp btn-state-send">
                            <span class="glyphicon glyphicon-remove-circle f18 text-danger"></span></a>
                        {/if}
                    </td>
                    <td>
                        <a href="/{$u}/courseHour?course_id={$dataVar.course_id}&site_id={$websites.site_id}"
                           class="btn btn-primary btn-sm">查看课次使用状况</a>
                    </td>

                </tr>
                {/foreach}
                {/if}
                </tbody>
            </table>
            <div class="pagemenu">{$pagelist}</div>
        </div>
    </div>
</div>