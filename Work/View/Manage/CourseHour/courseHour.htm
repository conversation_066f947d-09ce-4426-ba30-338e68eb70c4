<div class="content">
    <div class="bg-f">
        <h2 class="p20">
            <span class="fr">

                <button type="button" class="btn btn-primary ml10 bakFromurl">返回</button>
            </span>{$moduleOne.module_name}
        </h2>
        <div class="p20 f14">

            <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table tc table-hover table-bordered">
                <thead>
                <tr>
                    <th>课时ID</th>
                    <th>所属集团</th>
                    <th>所属班种</th>
                    <th>课程名称</th>
                    <th>课程编号</th>
                    <th>课时编号</th>
                    <th>是否开启教务登记</th>
                </tr>
                </thead>
                <tbody>
                {if $dataList}
                {foreach from=$dataList item=dataVar}
                <tr id="list-{$dataVar.coursehour_id}">
                    <td>{$dataVar.coursehour_id}</td>
                    <td>{$dataVar.company_cnname}</td>
                    <td>{$dataVar.coursecat_branch}</td>
                    <td>{$dataVar.course_cnname}</td>
                    <td>{$dataVar.course_branch}</td>
                    <td>{$dataVar.coursehour_branch}</td>
                    <td>
                        {if $dataVar.coursehour_isfilleas ==1}
                        <a href="javascript:;" data-url="/{$u}?c=changeFillEas&coursehour_id={$dataVar.coursehour_id}"
                           class="cp btn-state-send">
                            <span class="glyphicon glyphicon-ok-circle f18 text-primary"></span></a>
                        {else}
                        <a href="javascript:;" data-url="/{$u}?c=changeFillEas&coursehour_id={$dataVar.coursehour_id}"
                           class="cp btn-state-send">
                            <span class="glyphicon glyphicon-remove-circle f18 text-danger"></span></a>
                        {/if}
                    </td>
                </tr>
                {/foreach}
                {/if}
                </tbody>
            </table>
            <div class="pagemenu">{$pagelist}</div>
        </div>
    </div>
</div>