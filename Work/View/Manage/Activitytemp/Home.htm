<div class="content">
    <div class="bg-f">
        <h2 class="p20">{$moduleOne.module_name}
            <span class="fr">
                <a href="/{$u}/Add?site_id={$websites.site_id}" class="btn btn-success dropdown-toggle btn-demo-space">+新增数据</a>
                <button type="button" class="btn btn-primary ml10 bakFromurl">返回</button>
            </span>
        </h2>
        <div class="p20 f14">
            <div class="form-group">
                <form action="?" method="get" accept-charset="utf-8">
                    <input name="site_id" type="hidden" value="{$websites.site_id}">
                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tr>
                            <td width="4%">搜索：</td>
                            <td width="15%"  class="pr10 "><input class="form-control input-sm" id="inputEmail3" placeholder="请输入模块名称" value="{$datatype['keyword']}" type="text" name="keyword"></td>
                            <td width ="6%"> 是否上架:</td>
                            <td width="6%"  class="pr10 ">
                                <select name="activitytemp_putaway">
                                    <option value="">不限</option>
                                    <option value="1" {if $datatype.activitytemp_putaway ==1}selected{/if}>上架</option>
                                    <option value="0"  {if $datatype.activitytemp_putaway =='0'}selected{/if}>暂未上架</option>
                                </select>
                            </td>
                            <td width ="5%"> 简繁体:</td>
                            <td width="6%" >
                                <select name="activitytemp_type" >
                                    <option value="">不限</option>
                                    <option value="1" {if $datatype.activitytemp_type ==1}selected{/if}>繁体</option>
                                    <option value="0"  {if $datatype.activitytemp_type =='0'}selected{/if}>简体</option>
                                </select>
                            </td>
                            <td width="63%"><button type="submit" class="btn btn-primary ml10"><span class="glyphicon glyphicon-search c-f"></span> 搜索</button> </td>
                        </tr>
                    </table>
                </form>
            </div>
            <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table tc table-hover table-bordered">
                <thead>
                <tr>
                    <th>序号</th>
                    <th>招生模板名称</th>
                    <th>主题</th>
                    <th>简繁体</th>
                    <th>招生模板图片</th>
                    <th>招生模板banner示意图</th>
                    <th>招生地址前缀</th>
                    <th>是否上架</th>
                    <th>操作</th>
                </tr>
                </thead>
                <tbody>
                {if $dataList}
                {foreach from=$dataList item=dataVar key=key}
                <tr id="list-{$dataVar.activitytemp_id}">
                    <td>{$dataVar.activitytemp_id}</td>
                    <td>{$dataVar.activitytemp_name}</td>
                    <td>{if $dataVar.activitytemp_theme}{$dataVar.activitytemp_theme}{else}--{/if}</td>
                    <td>{if $dataVar.activitytemp_type == '0'}简{elseif $dataVar.activitytemp_type == '1'}繁{/if}</td>
                    <td>{if $dataVar.activitytemp_styleimg != ''}<a data-imgurl="{$dataVar.activitytemp_styleimg}" class="cp Opon-Img-View showico" title="查看效果"></a>{/if}</td>
                    <td>{if $dataVar.activitytemp_bannerimg != ''}<a data-imgurl="{$dataVar.activitytemp_bannerimg}" class="cp Opon-Img-View showico" title="查看效果"></a>{/if}</td>
                    <td>{$dataVar.activitytemp_url}</td>
                    <td>  {if $dataVar.activitytemp_putaway == '1'}上架{else}暂未上架{/if}</td>
                    <td>
                        <a href="/{$u}/Edit?site_id={$websites.site_id}&activitytemp_id={$dataVar.activitytemp_id}" class="btn btn-primary btn-sm"><span class="glyphicon glyphicon-pencil c-f"></span> 编辑</a>
                        <a href="javascript:;" data-element="list-{$dataVar.activitytemp_id}" data-url="/{$u}?site_id={$websites.site_id}&c=Del&id={$dataVar.activitytemp_id}" class="btn btn-danger btn-sm btn-del-action"><span class="glyphicon glyphicon-remove c-f"></span> 删除</a>
                    </td>
                </tr>
                {/foreach}
                {/if}
                </tbody>
            </table>
            <div class="pagemenu">{$pagelist}</div>
        </div>
    </div>
</div>
