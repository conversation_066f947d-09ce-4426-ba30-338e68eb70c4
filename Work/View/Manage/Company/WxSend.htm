<div class="content">
    <div class="bg-f">
        <h2 class="p20">{$moduleOne.module_name}--推送日志
            <span class="fr">
                <a class="btn btn-primary bakFromurl ml10"><span class="glyphicon glyphicon-share-alt c-f"></span> 返回</a>
            </span>
        </h2>
        <div class="p20 f14">
            <div class="form-group">
                <form action="/{$u}/WxSend" method="get" accept-charset="utf-8">
                    <input name="site_id" value="{$websites.site_id}" type="hidden">
                    <input name="company_id" value="{$datatype.company_id}" type="hidden">
                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tr>
                            <td width="4%">搜索：</td>
                            <td width="15%"><input name="keyword" class="form-control input-sm" placeholder="请输入学生中文名/编号/家长手机号" value="{$datatype['keyword']}" type="text"></td>
                            <td width="63%"><button type="submit" class="btn btn-primary ml10"><span class="glyphicon glyphicon-search c-f"></span> 搜索</button> </td>
                        </tr>
                    </table>
                </form>
            </div>
            <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table tc table-hover table-bordered">
                <thead>
                <tr>
                    <th>学生中文名</th>
                    <th>学生编号</th>
                    <th>家长手机号</th>
                    <th>推送类型</th>
                    <th>推送日期</th>
                    <th>是否成功</th>
                    <th>失败原因</th>
                </tr>
                </thead>
                <tbody>
                {if $dataList}
                {foreach from=$dataList item=dataVar}
                <tr id="list-{$dataVar.log_id}">
                    <td>{$dataVar.student_cnname}</td>
                    <td>{$dataVar.student_branch}</td>
                    <td>{$dataVar.parenter_mobile}</td>
                    <td>{$dataVar.log_type}</td>
                    <td>{$dataVar.log_day}</td>
                    <td>{if $dataVar.log_status == '1'}成功{else}失败{/if}</td>
                    <td>{$dataVar.log_errmsg}</td>
                </tr>
                {/foreach}
                {/if}
                </tbody>
            </table>
            <div class="pagemenu">{$pagelist}</div>
        </div>
    </div>
</div>