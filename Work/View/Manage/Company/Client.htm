<div class="content">
    <div class="bg-f">
        <h2 class="p20">{$moduleOne.module_name}---客户管理
            <span class="fr">
                <a class="btn btn-primary bakFromurl ml10"><span class="glyphicon glyphicon-share-alt c-f"></span> 返回</a>
            </span>
        </h2>
        <div class="p20 f14">
            <div class="form-group">
                <form action="/{$u}/Client" method="get" accept-charset="utf-8">
                    <input name="site_id" value="{$websites.site_id}" type="hidden">
                    <input name="marketer_id" value="{$datatype.marketer_id}" type="hidden">
                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tr>
                            <td width="4%">搜索：</td>
                            <td width="15%" class="pr10"><input name="keyword" class="form-control input-sm" placeholder="请输入客户账户/姓名" value="{$datatype['keyword']}" type="text"></td>
                            <td width="35%"><button type="submit" class="btn btn-primary ml10"><span class="glyphicon glyphicon-search c-f"></span> 搜索</button> </td>
                        </tr>
                    </table>
                </form>
            </div>
            <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table tc table-hover table-bordered">
                <thead>
                <tr>
                    <th>ID</th>
                    <th>客户账户</th>
                    <th>客户姓名</th>
                    <th>订单数量</th>
                    <th>关联时间</th>
                    <th>操作</th>
                </tr>
                </thead>
                <tbody>
                {if $dataList}
                {foreach from=$dataList item=dataVar}
                <tr id="list-{$dataVar.sharer_id}">
                    <td>{$dataVar.sharer_id}</td>
                    <td>{$dataVar.mobile}</td>
                    <td>{$dataVar.cnname}</td>
                    <td>{$dataVar.ordernums}</td>
                    <td>{$dataVar.client_time|date_format:'%Y-%m-%d %H:%M:%S'}</td>
                    <td>
                    <a href="javascript:;" data-element="list-{$dataVar.channel_id}" data-errortip="您确定解除此营销员与客户关联么！" data-url="/{$u}?c=DelClient&id={$dataVar.client_id}" class="btn btn-danger btn-sm btn-del-action">
                        <span class="glyphicon glyphicon-resize-full c-f"></span> 解除关联</a>
                        <a href="/{$u}/EditClient?client_id={$dataVar.client_id}&site_id={$websites.site_id}" class="btn btn-primary btn-sm">
                            <span class="glyphicon glyphicon-pencil c-f"></span> 编辑</a>
                    </td>
                </tr>
                {/foreach}
                {/if}
                </tbody>
            </table>
            <div class="pagemenu">{$pagelist}</div>
        </div>
    </div>
</div>