<div class="content">
    <div class="bg-f">
        <h2 class="p20">{$moduleOne.module_name}
            <span class="fr">
                <a href="/{$u}/Add?site_id={$websites.site_id}" class="btn btn-success dropdown-toggle btn-demo-space">+新增数据</a>
                <a class="btn btn-primary bakFromurl ml10"><span class="glyphicon glyphicon-share-alt c-f"></span> 返回</a>
            </span>
        </h2>
        <div class="p20 f14">
            <div class="form-group">
                <form action="{$moduleOne.module_link}" method="get" accept-charset="utf-8">
                    <input name="site_id" value="{$websites.site_id}" type="hidden">
                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tr>
                            <td width="4%">搜索：</td>
                            <td width="8%" class="pr10">
                                <select name="company_id" id="company_id">
                                    <option value="">请选择属集团</option>
                                    {if $companyList}
                                    {foreach from=$companyList item=companyVar}
                                    <option value="{$companyVar.company_id}" {if $companyVar.company_id eq $datatype.company_id}selected{/if}>{$companyVar.company_cnname}({$companyVar.company_code})</option>
                                    {/foreach}
                                    {/if}
                                </select>
                            </td>
                            <td width="15%">
                                <input name="keyword" class="form-control input-sm" placeholder="请输入合同名称" value="{$datatype['keyword']}" type="text">
                            </td>
                            <td width="63%"><button type="submit" class="btn btn-primary ml10"><span class="glyphicon glyphicon-search c-f"></span> 搜索</button> </td>
                        </tr>
                    </table>
                </form>
            </div>
            <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table tc table-hover table-bordered">
                <thead>
                <tr>
                    <th>ID</th>
                    <th>合同名称</th>
                    <th>所属集团</th>
                    <th>所属版本</th>
                    <th>最大创建校园数</th>
                    <th>最大创建学员数</th>
                    <th>起效时间</th>
                    <th>失效时间</th>
                    <th>销售合同金额</th>
                    <th>销售合同优惠价金额</th>
                    <th>销售合同实际价钱</th>
                    <th>创建时间</th>
                    <th>操作</th>
                </tr>
                </thead>
                <tbody>
                {if $dataList}
                {foreach from=$dataList item=dataVar}
                <tr id="list-{$dataVar.contract_id}">
                    <td>{$dataVar.contract_id}</td>
                    <td>{$dataVar.contract_name}</td>
                    <td>{$dataVar.company_cnname}</td>
                    <td>{$dataVar.edition_name}</td>
                    <td>{$dataVar.contract_maxschoolnum}</td>
                    <td>{$dataVar.contract_maxstudentnum}</td>
                    <td>{$dataVar.contract_starttime}</td>
                    <td>{$dataVar.contract_endtime}</td>
                    <td>{$dataVar.contract_allprice}</td>
                    <td>{$dataVar.contract_couponmoney}</td>
                    <td>{$dataVar.contract_paymentprice}</td>
                    <td>{$dataVar.contract_createtime|date_format:'%Y-%m-%d'}</td>
                    <td align="left">
                        <a href="/{$u}/Edit?contract_id={$dataVar.contract_id}&site_id={$websites.site_id}" class="btn btn-primary btn-sm">
                            <span class="glyphicon glyphicon-pencil c-f"></span> 编辑</a>
                    </td>
                </tr>
                {/foreach}
                {/if}
                </tbody>
            </table>
            <div class="pagemenu">{$pagelist}</div>
        </div>
    </div>
</div>