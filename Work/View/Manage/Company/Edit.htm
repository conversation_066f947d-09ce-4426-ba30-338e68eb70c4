<div class="content">
    <div id="container">
        <div class="bg-f row py20">
            <form action="/{$u}?c={$act}" method="post" enctype="application/x-www-form-urlencoded" class="AjaxForm">
                <input name="company_id" type="hidden" value="{$dataVar.company_id}">
                <input name="site_id" type="hidden" value="{$websites.site_id}">
                <h2 class="p20">{$moduleOne.module_name}<span class="fr">
                <button type="submit" class=" btn btn-success btn-cons"><i class="fa fa-check"></i>提交保存</button>
                <button type="button" class="btn btn-primary ml10 bakFromurl">返回</button></span>
                </h2>
                <div class="col-md-8" id="Form-Box-Operating">
                    <div class="pl20 py20 f14">
                        <div class="tab-content">
                            <div class="form-group col-md-4">
                                <label for="company_code">企业授权编号</label>
                                <input name="company_code" id="company_code" value="{$dataVar.company_code}" reg="[^ \f\n\r\t\v]" tip="请填写企业授权编号" type="text" class="form-control" placeholder="请输入企业编号">
                            </div>
                            <div class="form-group col-md-4">
                                <label for="company_shortname">企业简称</label>
                                <input name="company_shortname" id="company_shortname" value="{$dataVar.company_shortname}" reg="[^ \f\n\r\t\v]" tip="请填写企业简称" type="text" class="form-control" placeholder="请输入企业名称">
                            </div>
                            <div class="form-group col-md-4">
                                <label for="company_ismajor">集团开通版本</label>
                                <select name="company_ismajor" id="company_ismajor" class="form-control">
                                    <option value="0" {if $dataVar.company_ismajor == '0'}selected{/if}>免费版</option>
                                    <option value="1" {if $dataVar.company_ismajor == '1'}selected{/if}>普通版</option>
                                    <option value="2" {if $dataVar.company_ismajor == '2'}selected{/if}>专业版</option>
                                </select>
                            </div>

                            <div class="form-group col-md-4">
                                <label for="company_isinvoice">集团是否允许开发票</label>
                                <select name="company_isinvoice" id="company_isinvoice" class="form-control">
                                    <option value="0" {if $dataVar.company_isinvoice == '0'}selected{/if}>否</option>
                                    <option value="1" {if $dataVar.company_isinvoice == '1'}selected{/if}>是</option>
                                </select>
                            </div>

                            <div class="form-group col-md-4">
                                <label for="company_cnname">企业中文名称</label>
                                <input name="company_cnname" id="company_cnname" value="{$dataVar.company_cnname}" reg="[^ \f\n\r\t\v]" tip="请填写企业名称" type="text" class="form-control" placeholder="请输入企业名称">
                            </div>
                            <div class="form-group col-md-4">
                                <label for="company_enname">企业英文名称</label>
                                <input name="company_enname" id="company_enname" value="{$dataVar.company_enname}" reg="[^ \f\n\r\t\v]" tip="请填写企业英文名称" type="text" class="form-control" placeholder="请输入企业名称">
                            </div>

                            <div class="form-group col-md-4">
                                <label for="company_language">企业默认语言</label>
                                <select name="company_language" id="company_language" class="form-control">
                                    <option value="zh" {if $dataVar.company_language == 'zh'}selected{/if}>简体</option>
                                    <option value="tw" {if $dataVar.company_language == 'tw'}selected{/if}>繁体</option>
                                </select>
                            </div>

                            <div class="form-group col-md-4">
                                <label for="company_name">企业负责人</label>
                                <input name="company_name" id="company_name" value="{$dataVar.company_name}" type="text" class="form-control" placeholder="请输入企业负责人">
                            </div>
                            <div class="form-group col-md-4">
                                <label for="company_position">企业负责人职位</label>
                                <input name="company_position" id="company_position" value="{$dataVar.company_position}" type="text" class="form-control" placeholder="请输入企业负责人职位">
                            </div>
                            <div class="form-group col-md-4">
                                <label for="company_mobile">企业联系手机</label>
                                <input name="company_mobile" id="company_mobile" value="{$dataVar.company_mobile}" type="text" class="form-control" placeholder="请输入企业联系手机">
                            </div>
                            <div class="form-group col-md-4">
                                <label for="company_phone">客服电话</label>
                                <input name="company_phone" id="company_phone" value="{$dataVar.company_phone}" reg="[^ \f\n\r\t\v]" tip="请填写客服电话" type="text" class="form-control" placeholder="请输入客服电话">
                            </div>
                            <div class="form-group col-md-4">
                                <label for="company_fax">客服传真</label>
                                <input name="company_fax" id="company_fax" value="{$dataVar.company_fax}" reg="[^ \f\n\r\t\v]" tip="请填写客服传真" type="text" class="form-control" placeholder="请输入客服传真">
                            </div>
                            <div class="form-group col-md-4">
                                <label for="company_email">联系邮箱</label>
                                <input name="company_email" id="company_email" value="{$dataVar.company_email}" type="text" class="form-control" placeholder="请输入联系邮箱">
                            </div>
                            <div class="form-group col-md-4">
                                <label for="company_homeurl">企业官网</label>
                                <input name="company_homeurl" id="company_homeurl" value="{$dataVar.company_homeurl}" type="text" class="form-control" placeholder="请输入企业官网">
                            </div>

                            <div class="form-group col-md-4">
                                <label for="company_isshowhour">叮当助学是否展示课时统计</label>
                                <select name="company_isshowhour" id="company_isshowhour" class="form-control">
                                    <option value="0" {if $dataVar.company_isshowhour == '0'}selected{/if}>否</option>
                                    <option value="1" {if $dataVar.company_isshowhour == '1'}selected{/if}>是</option>
                                </select>
                            </div>


                            <div class="form-group col-md-4">
                                <label for="company_isshowpay">是否展示缴费金额</label>
                                <select name="company_isshowpay" id="company_isshowpay" class="form-control">
                                    <option value="0" {if $dataVar.company_isshowpay == '0'}selected{/if}>否</option>
                                    <option value="1" {if $dataVar.company_isshowpay == '1'}selected{/if}>是</option>
                                </select>
                            </div>

                            <div class="form-group col-md-4">
                                <label for="company_iseditimportcourse">导入课程是否允许修改</label>
                                <select name="company_iseditimportcourse" id="company_iseditimportcourse" class="form-control">
                                    <option value="0" {if $dataVar.company_iseditimportcourse == '0'}selected{/if}>不允许</option>
                                    <option value="1" {if $dataVar.company_iseditimportcourse == '1'}selected{/if}>允许</option>
                                </select>
                            </div>

                            <div class="form-group col-md-4">
                                <label for="company_issign">课程协议是否需要签字</label>
                                <select name="company_issign" id="company_issign" class="form-control">
                                    <option value="0" {if $dataVar.company_issign == '0'}selected{/if}>否</option>
                                    <option value="1" {if $dataVar.company_issign == '1'}selected{/if}>是</option>
                                </select>
                            </div>

                            <div class="form-group col-md-4">
                                <label for="company_isstock">是否限制库存数量</label>
                                <select name="company_isstock" id="company_isstock" class="form-control">
                                    <option value="0" {if $dataVar.company_isstock == '0'}selected{/if}>否</option>
                                    <option value="1" {if $dataVar.company_isstock == '1'}selected{/if}>是</option>
                                </select>
                            </div>

                            <div class="form-group col-md-4">
                                <label for="company_logoutday">可注销学员时间(天)</label>
                                <input name="company_logoutday" id="company_logoutday" value="{$dataVar.company_logoutday}" type="text" class="form-control" placeholder="请输入流失后可注销天数">
                            </div>

                            <div class="form-group col-md-4">
                                <label for="company_issupportpos">是否支持POS支付</label>
                                <select name="company_issupportpos" id="company_issupportpos" class="form-control">
                                    <option value="0" {if $dataVar.company_issupportpos == '0'}selected{/if}>否</option>
                                    <option value="1" {if $dataVar.company_issupportpos == '1'}selected{/if}>是</option>
                                </select>
                            </div>

                            <div class="form-group col-md-4">
                                <label for="company_issupportqr">是否支持二维码支付</label>
                                <select name="company_issupportqr" id="company_issupportqr" class="form-control">
                                    <option value="0" {if $dataVar.company_issupportqr == '0'}selected{/if}>否</option>
                                    <option value="1" {if $dataVar.company_issupportqr == '1'}selected{/if}>是</option>
                                </select>
                            </div>

                            <div class="form-group col-md-4">
                                <label for="company_issupportptc">是否支持发送家长端</label>
                                <select name="company_issupportptc" id="company_issupportptc" class="form-control">
                                    <option value="0" {if $dataVar.company_issupportptc == '0'}selected{/if}>否</option>
                                    <option value="1" {if $dataVar.company_issupportptc == '1'}selected{/if}>是</option>
                                </select>
                            </div>
                            <div class="form-group col-md-12">
                                <label for="company_code">集团公告</label>
                                <input name="company_notice" id="company_notice" value="{$dataVar.company_notice}" reg="[^ \f\n\r\t\v]" tip="请填写集团公告" type="text" class="form-control" placeholder="请填写集团公告">
                            </div>

                            <div class="form-group">
                                <div class="col-md-8  mb10">
                                    <label for="company_logo">登录Logo (500px*300px)</label>
                                    <input name="company_logo" id="company_logo" value="{$dataVar.company_logo}" type="text" class="form-control" placeholder="先点击右侧按钮上传图片，或直接书写图片网址">
                                </div>
                                <div class="col-md-4">
                                    <label for="up_img">&nbsp;</label>
                                    <a data-color="rgb(255, 255, 255)" data-color-format="hex" id="cp4" class="btn btn-primary my-colorpicker-control upload-btn" data-colorpicker-guid="1">
                                        <span class="glyphicon glyphicon-floppy-open"></span>
                                        选择图片上传<input type="file" id="up_img" class="ipt-img-click" data-element="list_img_view" data-originalipt="company_logo" data-thumbnailipt="tb_imgthum"></a>
                                </div>
                            </div>
                            <div class="clear"></div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="upload-img col-md-6" id="list_img_view">
                        <div class="f16 tc default {if $dataVar.company_logo}none{/if}">
                            <p class="f30"><span class="glyphicon glyphicon-picture"></span></p>
                            <p>请先上传图片后预览</p>
                        </div>
                        <div class="img set {if !$dataVar.company_logo}none{/if}"><img src="{$dataVar.company_logo}"></div>
                    </div>
                </div>

            </form>
        </div>
    </div>
</div>