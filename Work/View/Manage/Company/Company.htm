<div class="content">
    <div class="bg-f">
        <h2 class="p20">{$moduleOne.module_name}
            <span class="fr">
                <!--<a href="/{$u}/ImportCompanySchool?site_id={$websites.site_id}" class="btn btn-success dropdown-toggle btn-demo-space">导入集团和对应学校</a>-->
                <a href="/{$u}/ImportCompany?site_id={$websites.site_id}" class="btn btn-success dropdown-toggle btn-demo-space">导入集团</a>
                <a href="/{$u}/Add?site_id={$websites.site_id}" class="btn btn-success dropdown-toggle btn-demo-space">+新增数据</a>
                <a class="btn btn-primary bakFromurl ml10"><span class="glyphicon glyphicon-share-alt c-f"></span> 返回</a>
            </span>
        </h2>
        <div class="p20 f14">
            <div class="form-group">
                <form action="{$moduleOne.module_link}/Company" method="get" accept-charset="utf-8">
                    <input name="site_id" value="{$websites.site_id}" type="hidden">
                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tr>
                            <td width="4%">搜索：</td>
                            <td width="15%">
                                <input name="keyword" class="form-control input-sm" placeholder="请输入企业名称/编号/企业管理员" value="{$datatype['keyword']}" type="text">
                            </td>
                            <td width="63%"><button type="submit" class="btn btn-primary ml10"><span class="glyphicon glyphicon-search c-f"></span> 搜索</button> </td>
                        </tr>
                    </table>
                </form>
            </div>
            <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table tc table-hover table-bordered">
                <thead>
                <tr>
                    <th>ID</th>
                    <th>企业编号</th>
                    <th>企业简称</th>
                    <th>企业名称</th>
                    <th>网课余额</th>
                    <!--<th>联系人姓名</th>
                    <th>联系人职务</th>-->
                    <th>联系手机</th>
                    <th>创建时间</th>
                    <th>是否允许手动添加学员</th>
                    <th>分校数量</th>
                    <th>是否需要上传凭证</th>
                    <th>操作</th>
                </tr>
                </thead>
                <tbody>
                {if $dataList}
                {foreach from=$dataList item=dataVar}
                <tr id="list-{$dataVar.company_id}">
                    <td>{$dataVar.company_id}</td>
                    <td>{$dataVar.company_code}</td>
                    <td>{$dataVar.company_shortname}</td>
                    <td>{$dataVar.company_cnname}</td>
                    <td>{$dataVar.log_finalamount}</td>
                   <!-- <td>{$dataVar.company_name}</td>
                    <td>{$dataVar.company_position}</td>-->
                    <td>{$dataVar.company_mobile}</td>
                    <td>{$dataVar.company_addtime|date_format:'%Y-%m-%d'}</td>
                    <td>
                        {if $dataVar.company_isaddstudent ==1}
                        <a  href="javascript:;" data-url="/{$u}?c=isAddStudent&company_id={$dataVar.company_id}" class="cp btn-state-send">
                            <span class="glyphicon glyphicon-ok-circle f18 text-primary"></span></a>
                        {else}
                        <a href="javascript:;" data-url="/{$u}?c=isAddStudent&company_id={$dataVar.company_id}" class="cp btn-state-send">
                            <span class="glyphicon glyphicon-remove-circle f18 text-danger"></span></a>
                        {/if}
                    </td>
                    <td>{$dataVar.schoolnum}</td>
                    <td>
                        {if $dataVar.company_isvoucher ==1}
                        <a  href="javascript:;" data-url="/{$u}?c=isAddVoucher&company_id={$dataVar.company_id}" class="cp btn-state-send">
                            <span class="glyphicon glyphicon-ok-circle f18 text-primary"></span></a>
                        {else}
                        <a href="javascript:;" data-url="/{$u}?c=isAddVoucher&company_id={$dataVar.company_id}" class="cp btn-state-send">
                            <span class="glyphicon glyphicon-remove-circle f18 text-danger"></span></a>
                        {/if}
                    </td>
                    <td align="left">
                        <a href="/{$u}/Edit?company_id={$dataVar.company_id}&site_id={$websites.site_id}" class="btn btn-primary btn-sm">
                            <span class="glyphicon glyphicon-pencil c-f"></span> 编辑</a>
                        <a href="/{$u}/Staffer?company_id={$dataVar.company_id}&site_id={$websites.site_id}" class="btn btn-primary btn-sm">
                            <span class="glyphicon glyphicon-certificate c-f"></span> 职工明细</a>
                        <a href="/Contract?company_id={$dataVar.company_id}&site_id={$websites.site_id}&from=company" class="btn btn-primary btn-sm">
                            <span class="glyphicon glyphicon-phone c-f"></span> 合同管理</a>
                        <a href="/SchoolList?company_id={$dataVar.company_id}&site_id={$websites.site_id}&from=company" class="btn btn-primary btn-sm">
                            <span class="glyphicon  c-f"></span> 分校管理</a>
                        <a href="/{$u}/LineRecharge?company_id={$dataVar.company_id}&site_id={$websites.site_id}" class="btn btn-primary btn-sm">
                            <span class="glyphicon glyphicon-pencil c-f"></span> 网课充值</a>
                        <br>
                        <a href="/{$u}/LineRechargelog?company_id={$dataVar.company_id}&site_id={$websites.site_id}" class="btn btn-primary btn-sm">
                            <span class="glyphicon  c-f"></span> 充值记录</a>
                        <a href="/{$u}/PermissionConfigure?company_id={$dataVar.company_id}&site_id={$websites.site_id}" class="btn btn-primary btn-sm">
                            <span class="glyphicon  c-f"></span> 权限配置</a>
                        <a href="/{$u}/WxSend?company_id={$dataVar.company_id}&site_id={$websites.site_id}" class="btn btn-primary btn-sm">
                            <span class="glyphicon  c-f"></span> 推送日志</a>
                        <a href="javascript:;" data-url="/{$u}?c=givePrice&company_id={$dataVar.company_id}" class="btn btn-primary btn-sm btn-send-action">
                            <span class="glyphicon glyphicon-sort"></span> 赠送网费</a>
                        <a href="javascript:;" data-url="/{$u}?c=changeToEasy&company_id={$dataVar.company_id}" class="btn btn-primary btn-sm btn-send-action">转简</a>
                    </td>
                </tr>
                {/foreach}
                {/if}
                </tbody>
            </table>
            <div class="pagemenu">{$pagelist}</div>
            <p>半年卡（22次课），使用期限：缴费日期365天内使用完毕。一年卡（44次课），使用期限：缴费日期550天内使用完毕。</p>
        </div>
    </div>
</div>