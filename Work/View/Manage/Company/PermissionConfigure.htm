<div class="content">
    <div class="bg-f">
        <h2 class="p20">{$moduleOne.module_name}--权限配置
            <span class="fr">
                <a class="btn btn-primary bakFromurl ml10"><span class="glyphicon glyphicon-share-alt c-f"></span> 返回</a>
            </span>
        </h2>
        <div class="p20 f14">
            <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table tc table-hover table-bordered">
                <thead>
                <tr>
                    <th>产品名称</th>
                    <th>是否开启</th>
                    <th>操作</th>
                </tr>
                </thead>
                <tbody>
                {if $dataList}
                {foreach from=$dataList item=dataVar}
                <tr id="list-{$dataVar.product_id}">
                    <td>{$dataVar.product_name}</td>
                    <td>

                        {if $dataVar.status == '1'}
                        <a href="javascript:;" data-url="/{$u}?c=Changestatus&id={$dataVar.product_id}&company_id={$datatype.company_id}" class="cp btn-state-send">
                            <span class="glyphicon glyphicon-ok-circle f18 text-primary"></span></a>
                        {elseif $dataVar.status == '0'}
                        <a href="javascript:;" data-url="/{$u}?c=Changestatus&id={$dataVar.product_id}&company_id={$datatype.company_id}" class="cp btn-state-send">
                            <span class="glyphicon glyphicon-remove-circle f18 text-danger"></span></a>
                        {/if}

                    </td>
                    <td>
                        {if $dataVar.product_openmodule==1}
                        <a href="/{$u}/edition?company_id={$datatype.company_id}&product_id={$dataVar.product_id}&edition_id={$dataVar.edition_id}&site_id={$websites.site_id}" class="btn btn-primary btn-sm">
                            <span class="glyphicon glyphicon-phone c-f"></span> 功能权限</a>
                        {/if}
                        
                    </td>
                </tr>
                {/foreach}
                {/if}
                </tbody>
            </table>
            <div class="pagemenu">{$pagelist}</div>
        </div>
    </div>
</div>