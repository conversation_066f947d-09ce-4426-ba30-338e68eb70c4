<div class="content">
    <div class="bg-f">
        <h2 class="p20">{$moduleOne.module_name} -- 充值明细
            <span class="fr">
                <a href="/{$u}/LineRecharge?company_id={$datatype.company_id}&site_id={$websites.site_id}" class="btn btn-success dropdown-toggle btn-demo-space">+网课充值</a>
                <a class="btn btn-primary bakFromurl ml10"><span class="glyphicon glyphicon-share-alt c-f"></span> 返回</a>
            </span>
        </h2>
        <div class="p20 f14">
            <!--<div class="form-group">
                <form action="{$moduleOne.module_link}/Company" method="get" accept-charset="utf-8">
                    <input name="site_id" value="{$websites.site_id}" type="hidden">
                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tr>
                            <td width="4%">搜索：</td>
                            <td width="15%">
                                <input name="keyword" class="form-control input-sm" placeholder="请输入企业名称/编号/企业管理员" value="{$datatype['keyword']}" type="text">
                            </td>
                            <td width="63%"><button type="submit" class="btn btn-primary ml10"><span class="glyphicon glyphicon-search c-f"></span> 搜索</button> </td>
                        </tr>
                    </table>
                </form>
            </div>-->
            <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table tc table-hover table-bordered">
                <thead>
                <tr>
                    <th>ID</th>
                    <th>所属企业</th>
                    <th>充值学校</th>
                    <th>交易类型</th>
                    <th>交易名称</th>
                    <th>原金额</th>
                    <th>操作金额</th>
                    <th>操作后金额</th>
                    <th>记录明细</th>
                    <th>记录时间</th>
                </tr>
                </thead>
                <tbody>
                {if $dataList}
                {foreach from=$dataList item=dataVar}
                <tr id="list-{$dataVar.log_id}">
                    <td>{$dataVar.log_id}</td>
                    <td>{$dataVar.company_cnname}</td>
                    <td>{$dataVar.school_cnname}</td>
                    <td>{if $dataVar.log_class == '1'}账户结算{elseif $dataVar.log_class == '0'}账户充值{/if}</td>
                    <td>{$dataVar.log_playname}</td>
                    <td>{$dataVar.log_fromamount}</td>
                    <td>{$dataVar.log_playclass}{$dataVar.log_playamount}</td>
                    <td>{$dataVar.log_finalamount}</td>
                    <td>{$dataVar.log_reason}</td>
                    <td>{$dataVar.log_time|date_format:'%Y-%m-%d %H:%M:%S'}</td>
                </tr>
                {/foreach}
                {/if}
                </tbody>
            </table>
            <div class="pagemenu">{$pagelist}</div>
        </div>
    </div>
</div>