<div class="content">
    <div class="bg-f">
        <h2 class="p20">{$moduleOne.module_name}--职工用户明细
            <span class="fr">
                <a href="/{$u}/importStaffer?company_id={$datatype.company_id}&site_id={$websites.site_id}" class="btn btn-success dropdown-toggle btn-demo-space">导入职工</a>
                <a class="btn btn-primary bakFromurl ml10"><span class="glyphicon glyphicon-share-alt c-f"></span> 返回</a>
            </span>
        </h2>
        <div class="p20 f14">
            <div class="form-group">
                <form action="/{$u}/Staffer" method="get" accept-charset="utf-8">
                    <input name="site_id" value="{$websites.site_id}" type="hidden">
                    <input name="company_id" value="{$datatype.company_id}" type="hidden">
                    <input name="channel_id" value="{$tjchannelOne.channel_id}" type="hidden">
                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tr>
                            <td width="4%">搜索：</td>
                            <td width="15%"><input name="keyword" class="form-control input-sm" placeholder="请输入中文名称/员工编号" value="{$datatype['keyword']}" type="text"></td>
                            <td width="63%"><button type="submit" class="btn btn-primary ml10"><span class="glyphicon glyphicon-search c-f"></span> 搜索</button> </td>
                        </tr>
                    </table>
                </form>
            </div>
            <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table tc table-hover table-bordered">
                <thead>
                <tr>
                    <th>ID</th>
                    <th>职工编号</th>
                    <th>职工中文名</th>
                    <th>职工英文名</th>
                    <th>账户类型</th>
                    <th>是否离职</th>
                    <th>手机号码</th>
                    <th>密码</th>
                    <th>更新时间</th>
                    <th>操作</th>
                </tr>
                </thead>
                <tbody>
                {if $dataList}
                {foreach from=$dataList item=dataVar}
                <tr id="list-{$dataVar.staffer_id}">
                    <td>{$dataVar.staffer_id}</td>
                    <td>{$dataVar.staffer_branch}</td>
                    <td>{$dataVar.staffer_cnname}</td>
                    <td>{$dataVar.staffer_enname}</td>
                    <td>{if $dataVar.account_class == '1'}管理员账户{else}普通职工{/if}</td>
                    <td>{if $dataVar.staffer_leave == '1'}<span class="glyphicon glyphicon-ok-circle f18 text-primary"></span>{else}<span class="glyphicon glyphicon-remove-circle f18 text-danger"></span>{/if}</td> 
                    <td>{$dataVar.staffer_mobile}</td>
                    <td>{$dataVar.staffer_bakpass}</td>
                    <td>{$dataVar.staffer_updatetime|date_format:"%Y-%m-%d"}</td>
                    <td align="left">
                        <a href="/{$u}/worklog?staffer_id={$dataVar.staffer_id}&site_id={$websites.site_id}" class="btn btn-primary btn-sm">
                            <span class="glyphicon glyphicon-leaf c-f"></span> 职工日志</a>
                    </td>
                </tr>
                {/foreach}
                {/if}
                </tbody>
            </table>
            <div class="pagemenu">{$pagelist}</div>
        </div>
    </div>
</div>