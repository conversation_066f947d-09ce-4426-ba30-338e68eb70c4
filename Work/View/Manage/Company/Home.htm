<div class="content">
    <div class="bg-f">
        <h2 class="p20">{$moduleOne.module_name}
            <span class="fr">
                <a href="/{$u}/ImportTeaching?site_id={$websites.site_id}" class="btn btn-success dropdown-toggle btn-demo-space">导入教材班级</a>
                <a href="/{$u}/Add?site_id={$websites.site_id}" class="btn btn-success dropdown-toggle btn-demo-space">+新增数据</a>
                <a class="btn btn-primary bakFromurl ml10"><span class="glyphicon glyphicon-share-alt c-f"></span> 返回</a>
            </span>
        </h2>
        <div class="p20 f14">
            <div class="form-group">
                <form action="{$moduleOne.module_link}" method="get" accept-charset="utf-8">
                    <input name="site_id" value="{$websites.site_id}" type="hidden">
                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tr>
                            <td width="4%">搜索：</td>
                            <td width="15%">
                                <input name="keyword" class="form-control input-sm" placeholder="请输入企业名称/编号/企业管理员" value="{$datatype['keyword']}" type="text">
                            </td>
                            <td width="63%"><button type="submit" class="btn btn-primary ml10"><span class="glyphicon glyphicon-search c-f"></span> 搜索</button> </td>
                        </tr>
                    </table>
                </form>
            </div>
            <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table tc table-hover table-bordered">
                <thead>
                <tr>
                    <th>ID</th>
                    <th>企业编号</th>
                    <th>企业简称</th>
                    <th>企业名称</th>
                    <th>密码</th>
                    <th>联系人姓名</th>
                    <th>联系人职务</th>
                    <th>联系手机</th>
                    <th>创建时间</th>
                    <th>操作</th>
                </tr>
                </thead>
                <tbody>
                {if $dataList}
                {foreach from=$dataList item=dataVar}
                <tr id="list-{$dataVar.company_id}">
                    <td>{$dataVar.company_id}</td>
                    <td>{$dataVar.company_code}</td>
                    <td>{$dataVar.company_shortname}</td>
                    <td>{$dataVar.company_cnname}</td>
                    <td>{$dataVar.staffer_bakpass}</td>
                    <td>{$dataVar.company_name}</td>
                    <td>{$dataVar.company_position}</td>
                    <td>{$dataVar.company_mobile}</td>
                    <td>{$dataVar.company_addtime|date_format:'%Y-%m-%d'}</td>
                    <td align="left">
                        <a href="/{$u}/Edit?company_id={$dataVar.company_id}&site_id={$websites.site_id}" class="btn btn-primary btn-sm">
                            <span class="glyphicon glyphicon-pencil c-f"></span> 编辑</a>
                        <a href="/{$u}/Staffer?company_id={$dataVar.company_id}&site_id={$websites.site_id}" class="btn btn-primary btn-sm">
                            <span class="glyphicon glyphicon-certificate c-f"></span> 职工明细</a>
                        <a href="/{$u}/Worklog?company_id={$dataVar.company_id}&site_id={$websites.site_id}" class="btn btn-primary btn-sm">
                            <span class="glyphicon glyphicon-phone c-f"></span> 职工日志</a>
                        <a href="/Contract?company_id={$dataVar.company_id}&site_id={$websites.site_id}" class="btn btn-primary btn-sm">
                            <span class="glyphicon glyphicon-phone c-f"></span> 合同管理</a>
                        <a href="javascript:;" data-url="/{$u}/Datameans?company_id={$dataVar.company_id}" class="btn btn-primary ml10 btn-send-action">
                            <span class="glyphicon glyphicon-sort"></span> 更新班种数据</a>
                        <a href="javascript:;" data-element="list-{$dataVar.company_id}" data-url="/{$u}?c=Del&id={$dataVar.company_id}" class="btn btn-danger btn-sm btn-del-action"><span class="glyphicon glyphicon-remove c-f"></span> 删除</a>
                        <a href="javascript:;" data-element="list-{$dataVar.company_id}" data-tiptitle="您确定将该集团默认管理员密码重置为'123456'？" data-url="/{$u}?c=ResetPwd&id={$dataVar.company_id}" class="btn btn-danger btn-sm btn-confirm-action"><span class="glyphicon glyphicon-remove c-f"></span> 重置密码</a>

                    </td>
                </tr>
                {/foreach}
                {/if}
                </tbody>
            </table>
            <div class="pagemenu">{$pagelist}</div>
        </div>
    </div>
</div>