<div class="content">
    <div class="bg-f">
        <h2 class="p20">
            <span class="fr">
                <a href="/{$u}/Add?site_id={$websites.site_id}" class="btn btn-success dropdown-toggle btn-demo-space">+新增资料</a>
                <button type="button" class="btn btn-primary ml10 bakFromurl">返回</button>
            </span>{$moduleOne.module_name}</h2>
        <div class="p20 f14">
            <div class="form-group">
                <form action="/{$u}" method="get" accept-charset="utf-8">
                    <input name="site_id" value="{$websites.site_id}" type="hidden">
                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tr>
                            <td width="4%">搜索：</td>
                            <td width="15%"><input class="form-control input-sm" id="inputEmail3" placeholder="职业类型中文名/英文名/编号" value="{$datatype['keyword']}" type="text" name="keyword"></td>
                            <td width="63%"><button type="submit" class="btn btn-primary ml10"><span class="glyphicon glyphicon-search c-f"></span> 搜索</button> </td>
                        </tr>
                    </table>
                </form>
            </div>

            <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table tc table-hover table-bordered">
                <thead>
                <tr>
                    <th>职业类型ID</th>
                    <th>职业类型编号</th>
                    <th>职业类型中文名</th>
                    <th>职业类型英文名</th>
                    <th>操作</th>
                </tr>
                </thead>
                <tbody>
                {if $dataList}
                {foreach from=$dataList item=dataVar}
                <tr id="list-{$dataVar.careertype_id}">
                    <td>{$dataVar.careertype_id}</td>
                    <td>{$dataVar.careertype_branch}</td>
                    <td>{$dataVar.careertype_cnname}</td>
                    <td>{$dataVar.careertype_enname}</td>
                    <td>
                        <a href="/{$u}/Edit?careertype_id={$dataVar.careertype_id}&site_id={$websites.site_id}" class="btn btn-primary btn-sm"><span class="glyphicon glyphicon-pencil c-f"></span> 编辑</a>
                        <a  href="javascript:;" data-element="list-{$dataVar.careertype_id}" data-url="/{$u}?c=Del&id={$dataVar.careertype_id}" class="btn btn-danger btn-sm btn-del-action"><span class="glyphicon glyphicon-remove c-f"></span> 删除</a>
                    </td>
                </tr>
                {/foreach}
                {/if}
                </tbody>
            </table>
            <div class="pagemenu">{$pagelist}</div>
        </div>
    </div>
</div>
