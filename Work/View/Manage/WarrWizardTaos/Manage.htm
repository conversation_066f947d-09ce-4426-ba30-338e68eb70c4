<div class="content">
    <div id="container">
        <div class="bg-f row py20">
            <form action="/{$u}?c={$act}" method="post" enctype="application/x-www-form-urlencoded" class="AjaxForm">
                <input name="careertype_id" type="hidden" value="{$dataVar.careertype_id}">
                <input name="site_id" type="hidden" value="{$websites.site_id}">
                <h2 class="p20">{$moduleOne.module_name}<span class="fr">
                <button type="submit" class=" btn btn-success btn-cons"><i class="fa fa-check"></i>提交保存</button>
                <button type="button" class="btn btn-primary ml10 bakFromurl">返回</button></span>
                </h2>
                <div class="col-md-6" id="Form-Box-Operating">
                    <div class="pl20 f14">
                        <div class="form-group col-md-4">
                            <label for="careertype_cnname">职业类型中文名</label>
                            <input name="careertype_cnname" id="careertype_cnname" value="{$dataVar['careertype_cnname']}" type="text" class="form-control" placeholder="请输入职业类型中文名">
                        </div>
                        <div class="form-group col-md-4">
                            <label for="careertype_enname">职业类型英文名</label>
                            <input name="careertype_enname" id="careertype_enname" value="{$dataVar['careertype_enname']}" type="text" class="form-control" placeholder="请输入职业类型英文名">
                        </div>
                        <div class="form-group col-md-4">
                            <label for="careertype_branch">职业类型编号</label>
                            <input name="careertype_branch" id="careertype_branch" value="{$dataVar['careertype_branch']}" type="text" class="form-control" placeholder="请输入职业类型编号">
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
