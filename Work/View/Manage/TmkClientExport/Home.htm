<div class="content">
    <div class="bg-f">
        <h2 class="p20">{$moduleOne.module_name}
            <span class="fr">
                <a class="btn btn-primary bakFromurl btn-sm ml10">
                    <span class="glyphicon glyphicon-share-alt  c-f"></span>
                    返回
                </a>
            </span>
        </h2>
        <div class="p20 f14">
            <div class="form-group">
                <form action="/{$u}/{$t}" method="get" accept-charset="utf-8">
                    <input name="site_id" value="{$websites.site_id}" type="hidden">
                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tr>
                            <td width="5%">搜索：</td>
                            <td width="30%">
                                <div class="col-md-6">
                                    <input autocomplete="off" name="starttime" id="starttime" class="form-control form_datetime" value="{$datatype['starttime']}" placeholder="开始时间{$datatype['starttime']}" type="text">
                                </div>
                                <div class="col-md-6">
                                    <input autocomplete="off" name="endtime" id="endtime" class="form-control form_datetime" value="{$datatype['endtime']}" placeholder="结束时间{$datatype['endtime']}" type="text">
                                </div>
                            </td>

                            <td width="15%" class="pr10">
                                <input name="keyword" class="form-control input-sm" placeholder="请输入客户姓名/手机" value="{$datatype['keyword']}" type="text">
                            </td>
                            <td width="25%">
                                <button type="submit" class="btn btn-primary ml10">
                                    <span class="glyphicon glyphicon-search c-f"></span> 搜索</button>
                                <a href="/{$u}?c=ReportExcel&keyword={$datatype.keyword}&starttime={$datatype.starttime}&endtime={$datatype.endtime}"><span class="btn btn-primary ml10"><span class="glyphicon glyphicon-save c-f"></span>Excel导出
                                        </span></a>
                            </td>
                            <td></td>
                        </tr>
                    </table>
                </form>
            </div>
            <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table tc table-hover table-bordered">
                <thead>
                <tr>
                    <th>ID</th>
                    <th>姓名</th>
                    <th>标签</th>
                    <th>意向课程</th>
                    <th>家长称呼</th>
                    <th>手机号</th>
                    <th>创建时间</th>
                    <th>备注</th>
                </tr>
                </thead>
                <tbody>
                {if $dataList}
                {foreach from=$dataList item=dataVar}
                <tr id="list-{$dataVar.client_id}">
                    <td>{$dataVar.client_id}</td>
                    <td>{$dataVar.client_cnname}</td>
                    <td>{$dataVar.client_tag}</td>
                    <td>{$dataVar.course_cnname}</td>
                    <td>{$dataVar.client_patriarchname}</td>
                    <td>{$dataVar.client_mobile}</td>
                    <td>{$dataVar.client_createtime|date_format:'%Y-%m-%d'}</td>
                    <td>{$dataVar.client_remark}</td>
                </tr>
                {/foreach}
                {/if}
                </tbody>
            </table>
            <div class="pagemenu">{$pagelist}</div>
        </div>
    </div>
</div>