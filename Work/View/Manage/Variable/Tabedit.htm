<div class="content">
    <div id="container">
        <div class="bg-f row py20">
            <form action="/{$u}?c={$act}" method="post" enctype="application/x-www-form-urlencoded" class="AjaxForm">
                <input name="variable_id" type="hidden" value="{$dataVar.variable_id}">
                <h2 class="p20">{$moduleOne.module_name}<span class="fr">
                <button type="submit" class=" btn btn-success btn-cons"><i class="fa fa-check"></i>提交保存</button>
                <button type="button" class="btn btn-primary ml10 bakFromurl">返回</button></span>
                </h2>
                <div class="col-md-9" id="Form-Box-Operating">
                    <div class="form-group col-md-12">
                        <table class="table tc table-hover table-bordered" border="0" cellspacing="0" cellpadding="0">
                            <thead>
                            <tr>
                                <th>选项名称</th>
                                <th>参数</th>
                                <th>数值</th>
                                <th>排序</th>
                                <th>操作</th>
                            </tr>
                            </thead>
                            <tbody>
                            {if $variablelist}
                            {foreach from=$variablelist item=listVar key=key}
                            <tr class="tablelist" id="list-{$listVar.list_id}">
                                <td align="center" class="control-box">
                                    <input type="text" data-fuc="EditTabList" data-field="list_name" data-id="{$listVar.list_id}" value="{$listVar.list_name}" class="box-xs-9 ipt-put edit-input iptAjaxEdit" placeholder="请填写选项名称"></td>
                                <td align="center" class="control-box">
                                    <input type="text" data-fuc="EditTabList" data-field="list_parameter" data-id="{$listVar.list_id}" value="{$listVar.list_parameter}" class="box-xs-9 ipt-put edit-input iptAjaxEdit" placeholder="请填写参数">
                                </td>
                                <td align="center" class="control-box">
                                    <input type="text" data-fuc="EditTabList" data-field="list_number" data-id="{$listVar.list_id}" value="{$listVar.list_number}" class="box-xs-9 ipt-put edit-input iptAjaxEdit" placeholder="请填写选项数值">
                                </td>
                                <td align="center" class="control-box">
                                    <input type="text" data-fuc="EditTabList" data-field="list_weight" data-id="{$listVar.list_id}" value="{$listVar.list_weight}" class="box-xs-9 ipt-put edit-input iptAjaxEdit" placeholder="请填写排序数值">
                                </td>
                                <td align="center" class="control-box">
                                    <a href="javascript:;" data-element="list-{$listVar.list_id}" data-url="/{$u}?c=DelTab&id={$listVar.list_id}" class="btn-del-action">[x]</a>
                                </td>
                            </tr>
                            {/foreach}
                            {/if}
                            <tr class="tablelist">
                                <td align="center" class="control-box">
                                    <input type="text" name="name[]" class="box-xs-9 ipt-put" placeholder="请填写选项名称"></td>
                                <td align="center" class="control-box">
                                    <input type="text" name="parameter[]" class="box-xs-9 ipt-put" placeholder="请填写参数"></td>
                                <td align="center" class="control-box">
                                    <input type="text" name="number[]" class="box-xs-9 ipt-put" placeholder="请填写选项数值"></td>
                                <td align="center" class="control-box">
                                    <input type="text" name="weight[]" class="box-xs-9 ipt-put" placeholder="请填写排序数值"></td>
                                <td align="center" class="control-box">
                                    <a href="javascript:;" class="copyTableToNext">[+]</a>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>