<div class="content">
    <div id="container">
        <div class="bg-f row py20">
            <form action="/{$u}?c={$act}" method="post" enctype="application/x-www-form-urlencoded" class="AjaxForm">
                <input name="variable_id" type="hidden" value="{$dataVar.variable_id}">
                <h2 class="p20">{$moduleOne.module_name}<span class="fr">
                <button type="submit" class=" btn btn-success btn-cons"><i class="fa fa-check"></i>提交保存</button>
                <button type="button" class="btn btn-primary ml10 bakFromurl">返回</button></span>
                </h2>
                <div class="col-md-12" id="Form-Box-Operating">
                    <div class="pl20 py20 f14 col-md-12">
                        <div class="form-group col-md-12">
                            <label for="variable_name">变量名称</label>
                            <input id="variable_name" value="{$dataVar.variable_name}" type="text" class="form-control" readonly>
                        </div>
                        {if $dataVar.variable_type == 'html'}
                        <div class="form-group col-md-12">
                            <label for="Ueditorbody">变量详情</label>
                            <div class="control-box  debug-gray">
                                <div class="box-editor">
                                    <textarea name="content" id="Ueditorbody" class="textarea qeditor" rows="3">{$dataVar.content}</textarea>
                                </div>
                            </div>
                        </div>
                        {else}
                        <div class="form-group col-md-12">
                            <label for="content">变量详情</label>
                            <textarea name="content" id="content" class="form-control" rows="3">{$dataVar.content}</textarea>
                        </div>
                        {/if}
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 配置文件 -->
<script type="text/javascript" src="/Ueditor/ueditor.config.js"></script>
<!-- 编辑器源码文件 -->
<script type="text/javascript" src="/Ueditor/ueditor.all.js"></script>