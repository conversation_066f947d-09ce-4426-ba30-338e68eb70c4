<div class="content">
    <div class="bg-f">
        <h2 class="p20">
            {$moduleOne.module_name}
            <span class="fr">
                <a href="/{$u}/Add" class="btn btn-success dropdown-toggle btn-demo-space">+新增功能</a>
                <button type="button" class="btn btn-primary ml10 bakFromurl">返回</button>
            </span>
        </h2>
        <div class="p20 f14">
            <div class="form-group">
                <form action="?" method="get" accept-charset="utf-8">
                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tr>
                            <td width="4%">分类：</td>
                            <td width="14%">
                                <select name="type" class="form-control dropdown">
                                    <option value="">选择类型</option>
                                    <option value="html">网页内容</option>
                                    <option value="txt">文本内容</option>
                                    <option value="parameter">参数设置</option>
                                    <option value="tab">选项明细</option>
                                </select>
                            </td>
                            <td width="4%">搜索：</td>
                            <td width="15%"><input class="form-control input-sm" id="inputEmail3" placeholder="请输入模块名称或标示" value="{$datatype['keyword']}" type="text" name="keyword"></td>
                            <td width="63%"><button type="submit" class="btn btn-primary ml10"><span class="glyphicon glyphicon-search c-f"></span> 搜索</button> </td>
                        </tr>
                    </table>
                </form>
            </div>
            <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table tc table-hover table-bordered">
                <thead>
                <tr>
                    <th>序号</th>
                    <th>变量名称</th>
                    <th>标示</th>
                    <th>变量类型</th>
                    <th>变量操作</th>
                    <th>发布时间</th>
                    <th>操作</th>
                </tr>
                </thead>
                <tbody>
                {if $dataList}
                {foreach from=$dataList item=dataVar key=key}
                <tr id="list-{$dataVar.variable_id}">
                    <td>{$key+1}</td>
                    <td>{$dataVar.variable_name}</td>
                    <td>{$dataVar.variable_string}</td>
                    <td>{if $dataVar.variable_type eq 'html'}
                        网页{elseif $dataVar.variable_type eq 'txt'}
                        文本{elseif $dataVar.variable_type eq 'parameter'}
                        参数{elseif $dataVar.variable_type eq 'tab'}选项卡{/if}</td>
                    <td>{if $dataVar.variable_type eq 'parameter' or $dataVar.variable_type eq 'tab' }
                        程序调用{elseif $dataVar.variable_type eq 'txt' or  $dataVar.variable_type eq 'html'}
                        {literal}{baseGet var='{/literal}{$dataVar.variable_string}{literal}'}{/literal}
                        {/if}</td>
                    <td>{$dataVar.addtime|date_format:"%Y-%m-%d %H:%M:%S"}</td>
                    <td align="left">
                        <a href="/{$u}/Edit?id={$dataVar.variable_id}" class="btn btn-primary btn-sm"><span class="glyphicon glyphicon-pencil c-f"></span> 变量设置</a>
                        {if $dataVar.variable_type == 'tab'}
                        <a href="/{$u}/Tabedit?id={$dataVar.variable_id}" class="btn btn-primary btn-sm"><span class="icon-book c-f"></span> 录入选项</a>
                        {else}
                        <a href="/{$u}/Perfect?id={$dataVar.variable_id}" class="btn btn-primary btn-sm"><span class="icon-book c-f"></span> 完善内容</a>
                        {/if}
                        <a  href="javascript:;" data-element="list-{$dataVar.variable_id}" data-url="/{$u}?c=Del&id={$dataVar.variable_id}" class="btn btn-danger btn-sm btn-del-action"
                        <span class="glyphicon glyphicon-remove c-f"></span> 删除</a>
                    </td>
                </tr>
                {/foreach}
                {/if}
                </tbody>
            </table>
            <div class="pagemenu">{$pagelist}</div>
        </div>
    </div>
</div>