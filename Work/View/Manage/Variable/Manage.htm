<div class="content">
    <div id="container">
        <div class="bg-f row py20">
            <form action="/{$u}?c={$act}" role="form" method="post" enctype="application/x-www-form-urlencoded" class="AjaxForm form-horizontal">
                <input name="variable_id" type="hidden" value="{$dataVar.variable_id}">
                <h2 class="p20">系统功能模块<span class="fr">
                <button type="submit" class=" btn btn-success btn-cons"><i class="fa fa-check"></i>提交保存</button>
                <button type="button" class="btn btn-primary ml10 bakFromurl">返回</button></span>
                </h2>
                <div class="col-md-6" id="Form-Box-Operating">
                    <div class="pl20 py20 f14">
                        <div class="form-group">
                            <label for="variable_name" class="col-sm-2 control-label">变量名称</label>
                            <div class="col-sm-4">
                                <input name="variable_name" id="variable_name" value="{$dataVar.variable_name}" type="text" class="form-control" placeholder="请输入变量名称">
                            </div>
                            <label for="variable_string" class="col-sm-2 control-label">变量标示</label>
                            <div class="col-sm-4">
                                <input name="variable_string" id="variable_string" value="{$dataVar.variable_string}" type="text" class="form-control" placeholder="请输入变量标示">
                            </div>
                        </div>
                        <div class="form-group">

                            <label for="type1" class="col-sm-2 control-label">网页内容</label>
                            <div class="col-sm-1 pt7">
                                <input name="variable_type" id="type1" value="html" {if $dataVar.variable_type eq 'html' or !$dataVar.variable_type}checked{/if} type="radio" class="ace">
                                <span class="lbl"></span>
                            </div>
                            <label for="type2" class="col-sm-2 control-label">文本内容</label>
                            <div class="col-sm-1 pt7">
                                <input name="variable_type" id="type2" value="txt" {if $dataVar.variable_type eq 'txt'}checked{/if} type="radio" class="ace">
                                <span class="lbl"></span>
                            </div>
                            <label for="type3" class="col-sm-2 control-label">参数设置</label>
                            <div class="col-sm-1 pt7">
                                <input name="variable_type" id="type3" value="parameter" {if $dataVar.variable_type eq 'parameter'}checked{/if} type="radio" class="ace">
                                <span class="lbl"></span>
                            </div>
                            <label for="type1" class="col-sm-2 control-label">选项明细</label>
                            <div class="col-sm-1 pt7">
                                <input name="variable_type" id="type4" value="tab" {if $dataVar.variable_type eq 'tab'}checked{/if} type="radio" class="ace">
                                <span class="lbl"></span>
                            </div>

                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>