<div class="content">
    <div class="bg-f">
        <h2 class="p20">{$moduleOne.module_name}
            <span class="fr">
                <button type="button" class="btn btn-primary ml10 bakFromurl">返回</button>
            </span>
        </h2>
        <div class="p20 f14">
            <div class="form-group">
                <form action="?" method="get" accept-charset="utf-8">
                    <input name="site_id" type="hidden" value="{$websites.site_id}">
                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tr>
                            <td width="6%"  class="pr10 ">
                                <select name="student_isnew">
                                    <option value="">是否成为积分商城会员</option>
                                    <option value="1" {if $datatype.student_isnew ==1}selected{/if}>是</option>
                                    <option value="0"  {if $datatype.student_isnew =='0'}selected{/if}>否</option>
                                </select>
                            </td>
                            <td width="6%"  class="pr10 ">
                                <select name="islogin">
                                    <option value="">是否激活微商城</option>
                                    <option value="1" {if $datatype.islogin ==1}selected{/if}>是</option>
                                </select>
                            </td>
                            <td width="4%">搜索：</td>
                            <td width="15%"><input class="form-control input-sm" id="inputEmail3" placeholder="请输入推荐学生编号/名称" value="{$datatype['keyword']}" type="text" name="keyword"></td>
                            <td width="63%"><button type="submit" class="btn btn-primary ml10"><span class="glyphicon glyphicon-search c-f"></span> 搜索</button>
                                {if $datatype.student_isnew eq '1' || $datatype.islogin eq '1'}
                                <a href="/StudentVip?c=ReportExcel&keyword={$datatype.keyword}&student_isnew={$datatype.student_isnew}&islogin={$datatype.islogin}"><span class="btn btn-primary ml10"><span class="glyphicon glyphicon-save c-f"></span>Excel导出 </span></a> </td>
                                {/if}
                        </tr>
                    </table>
                </form>
            </div>
            <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table tc table-hover table-bordered">
                <thead>
                <tr>
                    <th>序号</th>
                    <th>学生名称</th>
                    <th>学生编号</th>
                    <th>是否成为积分商城会员</th>
                    <th>拓课班种</th>
                    <th>成为会员后的首次登录时间</th>
                    <th>上次登录时间</th>
                    <th>登录总次数</th>
                </tr>
                </thead>
                <tbody>
                {if $dataList}
                {foreach from=$dataList item=dataVar key=key}
                <tr id="list-{$dataVar.student_id}">
                    <td>{$dataVar.student_id}</td>
                    <td>{$dataVar.student_cnname}</td>
                    <td>{$dataVar.student_branch}</td>
                    <td>{if $dataVar.student_isnew eq '1'}是{elseif $dataVar.student_isnew eq '0'}否{/if}</td>
                    <td>{$dataVar.coursecat_cnname}</td>
                    <td>{if $dataVar.student_logintime eq '1970-01-01 08:00:00'}--{else} {$dataVar.student_logintime}{/if}</td>
                    <td>{if $dataVar.student_lastlogintime eq '1970-01-01 08:00:00'}--{else} {$dataVar.student_lastlogintime}{/if}</td>
                    <td>{$dataVar.student_loginnum}</td>
                </tr>
                {/foreach}
                {/if}
                </tbody>
            </table>
            <div class="pagemenu">{$pagelist}</div>
        </div>
    </div>
</div>