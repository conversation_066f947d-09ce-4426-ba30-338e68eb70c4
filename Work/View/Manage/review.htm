<link rel="stylesheet" href="{$CssUrl}reviewQuestion.css?time=11100100" />
{literal}
<!-- 图片选择 -->
<script id="imgChoose-template" type="text/x-handlebars-template">
    <div class="reviewMask" id="imgChoose">
        <div class="reviewContent">
            <div class="review-tit">{{ category_name }} <span class="close-icon"></span></div>
            <div class="reivew-text">
                <p>{{ tips_enname }}</p>
                <p>{{ tips_cnname }}</p>
                {{#if has_audio}}
                <span class="noice"></span>
                <audio id="audio" src="{{question_audio}}" style="opacity: 0;"></audio>
                {{/if}}
            </div>
            <div class="contents">
                {{#if question_title}}
                <p class="tips">{{ question_title }}</p>
                {{/if}}
                <div class="imgs-content">
                    {{#if has_img}}
                    <div class="row-1">
                        <div class="imgs">
                            <img src="{{ question_titleimg }}" alt="">
                        </div>
                    </div>
                    {{/if}}
                    <div class="row-2">
                        {{#each option}}
                        <div class="imgs">
                            <img src="{{ answers_optionimg }}" alt="">
                            <p>{{ answers_optionname }}</p>
                        </div>
                        {{/each}}
                    </div>
                </div>
            </div>
            <div class="analysis">{{ question_analysis }}</div>
        </div>
    </div>
</script>
<!-- 文字选择 -->
<script id="txtChoose-template" type="text/x-handlebars-template">
    <div class="reviewMask" id="txtChoose">
        <div class="reviewContent">
            <div class="review-tit">{{ category_name }} <span class="close-icon"></span></div>
            <div class="reivew-text">
                <p>{{ tips_enname }}</p>
                <p>{{ tips_cnname }}</p>
                {{#if has_audio}}
                <span class="noice"></span>
                <audio id="audio" src="{{question_audio}}" style="opacity: 0;"></audio>
                {{/if}}
            </div>
            <div class="contents">
                {{#if question_title}}
                <p class="tips">{{ question_title }}</p>
                {{/if}}
                <div class="txt-content">
                    {{#if has_img}}
                    <div class="row-1">
                        <div class="imgs">
                            <img src="{{ question_titleimg }}" alt="">
                        </div>
                    </div>
                    {{/if}}
                    <div class="row-2">
                        {{#each option}}
                        <div class="querstion">
                            <span>{{ answers_option }}</span>
                            <p>{{ answers_optionname }}</p>
                        </div>
                        {{/each}}
                    </div>
                </div>
            </div>
            <div class="analysis">{{ question_analysis }}</div>
        </div>
    </div>
</script>
<!-- 文字排序 -->
<script id="txtSort-template" type="text/x-handlebars-template">
    <div class="reviewMask" id="txtSort">
        <div class="reviewContent">
            <div class="review-tit">{{ category_name }} <span class="close-icon"></span></div>
            <div class="reivew-text">
                <p>{{ tips_enname }}</p>
                <p>{{ tips_cnname }}</p>
                {{#if has_audio}}
                <span class="noice"></span>
                <audio id="audio" src="{{question_audio}}" style="opacity: 0;"></audio>
                {{/if}}
            </div>
            <div class="contents">
                {{#if question_title}}
                <p class="tips">{{ question_title }}</p>
                {{/if}}
                <div class="sort-cont">
                    <p class="line"></p>
                    <p class="line"></p>
                    <div class="list">
                        {{#each option}}
                        <span>{{ answers_option }}</span>
                        {{/each}}
                    </div>
                </div>
            </div>
            <div class="analysis">{{ question_analysis }}</div>
        </div>
    </div>
</script>
<!-- 图片排序 -->
<script id="imgSort-template" type="text/x-handlebars-template">
    <div class="reviewMask" id="imgSort">
        <div class="reviewContent">
            <div class="review-tit">{{ category_name }} <span class="close-icon"></span></div>
            <div class="reivew-text">
                <p>{{ tips_enname }}</p>
                <p>{{ tips_cnname }}</p>
                {{#if has_audio}}
                <span class="noice"></span>
                <audio id="audio" src="{{question_audio}}" style="opacity: 0;"></audio>
                {{/if}}
            </div>
            <div class="contents">
                {{#if question_title}}
                <p class="tips">{{ question_title }}</p>
                {{/if}}
                <div class="sort-cont">
                    <div class="imgSortlist">
                        <div class="item sort-num"><span>1</span></div>
                        <div class="item sort-num"><span>2</span></div>
                        <div class="item sort-num"><span>3</span></div>
                        <div class="item sort-num"><span>4</span></div>
                    </div>
                    <div class="imgSortlist">
                        {{#each option}}
                        <div class="item">
                            <img src="{{ answers_optionimg }}" alt="">
                        </div>
                        {{/each}}
                    </div>
                </div>
            </div>
            <div class="analysis">{{ question_analysis }}</div>
        </div>
    </div>
</script>
<!-- 单词补全 -->
<script id="missingLetter-template" type="text/x-handlebars-template">
    <div class="reviewMask" id="missingLetter">
        <div class="reviewContent">
            <div class="review-tit">{{ category_name }} <span class="close-icon"></span></div>
            <div class="reivew-text">
                <p>{{ tips_enname }}</p>
                <p>{{ tips_cnname }}</p>
                {{#if has_audio}}
                <span class="noice"></span>
                <audio id="audio" src="{{question_audio}}" style="opacity: 0;"></audio>
                {{/if}}
            </div>
            <div class="contents">
                {{#if question_title}}
                <p class="tips">{{ question_title }}</p>
                {{/if}}
                <div class="missingLetterfill-cont">
                    {{#if has_img}}
                    <div class="imgs">
                        <img src="{{ question_titleimg }}" alt="">
                    </div>
                    {{/if}}
                    <p class="words">{{ question_content }}</p>
                    <span class="fill-ipt"></span>
                </div>
            </div>
            <div class="analysis">{{ question_analysis }}</div>
        </div>
    </div>
</script>
<!-- 句子填空 -->
<script id="missingSentence-template" type="text/x-handlebars-template">
    <div class="reviewMask" id="missingSentence">
        <div class="reviewContent">
            <div class="review-tit">{{ category_name }} <span class="close-icon"></span></div>
            <div class="reivew-text">
                <p>{{ tips_enname }}</p>
                <p>{{ tips_cnname }}</p>
                {{#if has_audio}}
                <span class="noice"></span>
                <audio id="audio" src="{{question_audio}}" style="opacity: 0;"></audio>
                {{/if}}
            </div>
            <div class="contents">
                {{#if question_title}}
                <p class="tips">{{ question_title }}</p>
                {{/if}}
                <div class="missingSentencefill-cont">
                    {{#if has_img}}
                    <div class="imgs">
                        <img src="{{ question_titleimg }}" alt="">
                    </div>
                    {{/if}}
                    <div class="missingSentence-sentenct">
                        <p>{{ question_content }}</p>
                    </div>
                </div>
            </div>
            <div class="analysis">{{ question_analysis }}</div>
        </div>
    </div>
</script>

<!-- 连连看 -->
<script id="linkTolink-template" type="text/x-handlebars-template">
    <div class="reviewMask" id="linkTolink">
        <div class="reviewContent">
            <div class="review-tit">{{ category_name }} <span class="close-icon"></span></div>
            <div class="reivew-text">
                <p>{{ tips_enname }}</p>
                <p>{{ tips_cnname }}</p>
                {{#if has_audio}}
                <span class="noice"></span>
                <audio id="audio" src="{{question_audio}}" style="opacity: 0;"></audio>
                {{/if}}
            </div>
            <div class="contents">
                {{#if question_title}}
                <p class="tips">{{ question_title }}</p>
                {{/if}}
                <div class="link-cont">
                    <div class="row">
                        {{#with option}}
                        {{#each top}}
                        {{#if_eq answers_isimg '1'}}
                        <div class="item">
                            <em>A{{answers_optionname}}</em>
                            <img src="{{ answers_optionimg }}" alt="">
                        </div>
                        {{else}}
                        <div class="item">
                            <em>A{{answers_optionname}}</em>
                            <span class="txt">{{ answers_option }}</span>
                        </div>
                        {{/if_eq}}
                        {{/each}}
                        {{/with}}
                    </div>

                    <div class="row">
                        {{#with option}}
                        {{#each bottom}}
                        {{#if_eq answers_isimg '1'}}
                        <div class="item">
                            <em>B{{answers_optionname}}</em>
                            <img src="{{ answers_optionimg }}" alt="">
                        </div>
                        {{else}}
                        <div class="item">
                            <em>B{{answers_optionname}}</em>
                            <span class="txt">{{ answers_option }}</span>
                        </div>
                        {{/if_eq}}
                        {{/each}}
                        {{/with}}
                    </div>
                </div>
            </div>
            <div class="analysis">{{ question_analysis }}</div>
        </div>
    </div>
</script>

<!-- 文字 判断题 -->
<script id="TxtJudge-template" type="text/x-handlebars-template">
    <div class="reviewMask" id="TxtJudge">
        <div class="reviewContent">
            <div class="review-tit">{{ category_name }} <span class="close-icon"></span></div>
            <div class="reivew-text">
                <p>{{ tips_enname }}</p>
                <p>{{ tips_cnname }}</p>
                {{#if has_audio}}
                <span class="noice"></span>
                <audio id="audio" src="{{question_audio}}" style="opacity: 0;"></audio>
                {{/if}}
            </div>
            <div class="contents">
                {{#if question_title}}
                <p class="tips">{{ question_title }}</p>
                {{/if}}
                <div class="judge-cont">
                    {{#if has_img}}
                    <div class="imgs">
                        <img src="{{ question_titleimg }}" alt="">
                    </div>
                    {{/if}}
                    {{#if question_content}}
                    <p>{{ question_content }}</p>
                    {{/if}}
                    <div class="judge">
                        <span class="wrong">X</span>
                        <span class="ok">O</span>
                    </div>
                </div>
            </div>
            <div class="analysis">{{ question_analysis }}</div>
        </div>
    </div>
</script>

<!-- 图片点击题 -->
<script id="ImgClick-template" type="text/x-handlebars-template">
    <div class="reviewMask" id="ImgClick">
        <div class="reviewContent">
            <div class="review-tit">{{ category_name }} <span class="close-icon"></span></div>
            <div class="reivew-text">
                <p>{{ tips_enname }}</p>
                <p>{{ tips_cnname }}</p>
                {{#if has_audio}}
                <span class="noice"></span>
                <audio id="audio" src="{{question_audio}}" style="opacity: 0;"></audio>
                {{/if}}
            </div>
            <div class="contents">
                {{#if question_title}}
                <p class="tips">{{ question_title }}</p>
                {{/if}}
                <div class="imgsList">
                    {{#each option}}
                    <div class="item">
                        <img src="{{ answers_optionimg }}" alt="">
                        <span>{{ answers_optionname }}</span>
                    </div>
                    {{/each}}
                </div>
            </div>
            <div class="analysis">{{ question_analysis }}</div>
        </div>
    </div>
</script>

<!-- 图片拖拽题 -->
<script id="ImgDrag-template" type="text/x-handlebars-template">
    <div class="reviewMask" id="ImgDrag">
        <div class="reviewContent">
            <div class="review-tit">{{ category_name }} <span class="close-icon"></span></div>
            <div class="reivew-text">
                <p>{{ tips_enname }}</p>
                <p>{{ tips_cnname }}</p>
                {{#if has_audio}}
                <span class="noice"></span>
                <audio id="audio" src="{{question_audio}}" style="opacity: 0;"></audio>
                {{/if}}
            </div>
            <div class="contents">
                {{#if question_title}}
                <p class="tips">{{ question_title }}</p>
                {{/if}}
                <div class="drag-cont">
                    {{#if has_img}}
                    <div class="rows head-rows">
                        <div class="item">
                            <img src="{{ question_titleimg }}" alt="">
                        </div>
                    </div>
                    {{/if}}
                    <div class="rows">
                        {{#each option}}
                        {{#if answers_optionimg}}
                        <div class="item">
                            <img src="{{ answers_optionimg }}" alt="">
                            <p>{{ answers_optionname }}</p>
                        </div>
                        {{else}}
                        <div class="item">
                            <span>{{ answers_option }}</span>
                            <p>{{ answers_optionname }}</p>
                        </div>
                        {{/if}}
                        {{/each}}
                    </div>
                    <div class="rows next-rows">
                        <div class="item"></div>
                    </div>
                </div>
                <div class="analysis">{{ question_analysis }}</div>
            </div>
        </div>
    </div>
</script>

<!-- 句子 排序 题 -->
<script id="SentenceSort-template" type="text/x-handlebars-template">
    <div class="reviewMask" id="SentenceSort">
        <div class="reviewContent">
            <div class="review-tit">{{ category_name }} <span class="close-icon"></span></div>
            <div class="reivew-text">
                <p>{{ tips_enname }}</p>
                <p>{{ tips_cnname }}</p>
                {{#if has_audio}}
                <span class="noice"></span>
                {{/if}}
            </div>
            <div class="contents">
                {{#if question_title}}
                <p class="tips">{{ question_title }}</p>
                {{/if}}
                <div class="SentenceSort-cont">
                    <ul>
                        {{#each option}}
                        <li>
                            <span>{{ answers_optionname }}</span>
                            <p>{{ answers_option }}</p>
                        </li>
                        {{/each}}
                    </ul>
                </div>
            </div>
            <div class="analysis">{{ question_analysis }}</div>
        </div>
    </div>
</script>

<!-- 阅读 题 -->
<script id="readArt-template" type="text/x-handlebars-template">
    <div class="reviewMask" id="SentenceSort">
        <div class="reviewContent">
            <div class="review-tit">{{ category_name }} <span class="close-icon"></span></div>
            <div class="reivew-text">
                <p>{{ tips_enname }}</p>
                <p>{{ tips_cnname }}</p>
                {{#if has_audio}}
                <span class="noice"></span>
                {{/if}}
            </div>
            <div class="readArtContent contents">
                {{#if question_readtext}}
                <p class="tips">{{ question_readtext }}</p>
                {{/if}}
                <div class="readArt-cont"></div>
            </div>
            <div class="analysis">{{ question_analysis }}</div>
        </div>
    </div>
</script>

<script id="readArtCont-11" type="text/x-handlebars-template">
    <div class="contents">
        {{#if question_title}}
        <p class="tips">{{ question_title }}</p>
        {{/if}}
        <div class="imgs-content">
            {{#if has_img}}
            <div class="row-1">
                <div class="imgs">
                    <img src="{{ question_titleimg }}" alt="">
                </div>
            </div>
            {{/if}}
            <div class="row-2">
                {{#each option}}
                <div class="imgs">
                    <img src="{{ answers_optionimg }}" alt="">
                    <p>{{ answers_optionname }}</p>
                </div>
                {{/each}}
            </div>
        </div>
        <div class="analysis">{{ question_analysis }}</div>
    </div>
</script>
<script id="readArtCont-12" type="text/x-handlebars-template">
    <div class="contents">
        {{#if question_title}}
        <p class="tips">{{ question_title }}</p>
        {{/if}}
        <div class="txt-content">
            {{#if has_img}}
            <div class="row-1">
                <div class="imgs">
                    <img src="{{ question_titleimg }}" alt="">
                </div>
            </div>
            {{/if}}
            <div class="row-2">
                {{#each option}}
                <div class="querstion">
                    <span>{{ answers_option }}</span>
                    <p>{{ answers_optionname }}</p>
                </div>
                {{/each}}
            </div>
        </div>
        <div class="analysis">{{ question_analysis }}</div>
    </div>
</script>
<script id="readArtCont-13" type="text/x-handlebars-template">
    <div class="contents">
        {{#if question_title}}
        <p class="tips">{{ question_title }}</p>
        {{/if}}
        <div class="sort-cont">
            <p class="line"></p>
            <p class="line"></p>
            <div class="list">
                {{#each option}}
                <span>{{ answers_option }}</span>
                {{/each}}
            </div>
        </div>
        <div class="analysis">{{ question_analysis }}</div>
    </div>
</script>
<script id="readArtCont-14" type="text/x-handlebars-template">
    <div class="contents">
        {{#if question_title}}
        <p class="tips">{{ question_title }}</p>
        {{/if}}
        <div class="sort-cont">
            <div class="list">
                <div class="item sort-num">1</div>
                <div class="item sort-num">2</div>
                <div class="item sort-num">3</div>
                <div class="item sort-num">4</div>
            </div>
            <div class="list">
                {{#each option}}
                <div class="item">
                    <img src="{{ answers_optionimg }}" alt="">
                </div>
                {{/each}}
            </div>
        </div>
        <div class="analysis">{{ question_analysis }}</div>
    </div>
</script>
<script id="readArtCont-15" type="text/x-handlebars-template">
    <div class="contents">
        {{#if question_title}}
        <p class="tips">{{ question_title }}</p>
        {{/if}}
        <div class="fill-cont">
            {{#if has_img}}
            <div class="imgs">
                <img src="{{ question_titleimg }}" alt="">
            </div>
            {{/if}}
            <p class="words">{{ question_content }}</p>
            <span class="fill-ipt"></span>
        </div>
        <div class="analysis">{{ question_analysis }}</div>
    </div>
</script>
<script id="readArtCont-16" type="text/x-handlebars-template">
    <div class="contents">
        {{#if question_title}}
        <p class="tips">{{ question_title }}</p>
        {{/if}}
        <div class="fill-cont">
            {{#if has_img}}
            <div class="imgs">
                <img src="{{ question_titleimg }}" alt="">
            </div>
            {{/if}}
            <div class="sentenct">
                <p>{{ question_content }}</p>
            </div>
        </div>
        <div class="analysis">{{ question_analysis }}</div>
    </div>
</script>
<script id="readArtCont-17" type="text/x-handlebars-template">
    <div class="contents">
        {{#if question_title}}
        <p class="tips">{{ question_title }}</p>
        {{/if}}
        <div class="link-cont">
            <div class="row">
                {{#with option}}
                {{#each top}}
                {{#if_eq answers_isimg '1'}}
                <div class="item">
                    <em>A{{answers_optionname}}</em>
                    <img src="{{ answers_optionimg }}" alt="">
                </div>
                {{else}}
                <div class="item">
                    <em>A{{answers_optionname}}</em>
                    <span class="txt">{{ answers_option }}</span>
                </div>
                {{/if_eq}}
                {{/each}}
                {{/with}}
            </div>

            <div class="row">
                {{#with option}}
                {{#each bottom}}
                {{#if_eq answers_isimg '1'}}
                <div class="item">
                    <em>B{{answers_optionname}}</em>
                    <img src="{{ answers_optionimg }}" alt="">
                </div>
                {{else}}
                <div class="item">
                    <em>B{{answers_optionname}}</em>
                    <span class="txt">{{ answers_option }}</span>
                </div>
                {{/if_eq}}
                {{/each}}
                {{/with}}
            </div>
        </div>
    </div>
</script>
<script id="readArtCont-18" type="text/x-handlebars-template">
    <div class="contents">
        {{#if question_title}}
        <p class="tips">{{ question_title }}</p>
        {{/if}}
        <div class="judge-cont">
            {{#if has_img}}
            <div class="imgs">
                <img src="{{ question_titleimg }}" alt="">
            </div>
            {{/if}}
            {{#if question_content}}
            <p>{{ question_content }}</p>
            {{/if}}
            <div class="judge">
                <span class="wrong">X</span>
                <span class="ok">O</span>
            </div>
        </div>
    </div>
</script>
<script id="readArtCont-19" type="text/x-handlebars-template">
    <div class="contents">
        {{#if question_title}}
        <p class="tips">{{ question_title }}</p>
        {{/if}}
        <div class="imgsList">
            {{#each option}}
            <div class="item">
                <img src="{{ answers_optionimg }}" alt="">
                <span>{{ answers_optionname }}</span>
            </div>
            {{/each}}
        </div>
    </div>
</script>
<script id="readArtCont-20" type="text/x-handlebars-template">
    <div class="contents">
        {{#if question_title}}
        <p class="tips">{{ question_title }}</p>
        {{/if}}
        <div class="drag-cont">
            {{#if has_img}}
            <div class="rows head-rows">
                <div class="item">
                    <img src="{{ question_titleimg }}" alt="">
                </div>
            </div>
            {{/if}}
            <div class="rows">
                {{#each option}}
                {{#if answers_optionimg}}
                <div class="item">
                    <img src="{{ answers_optionimg }}" alt="">
                    <p>{{ answers_optionname }}</p>
                </div>
                {{else}}
                <div class="item">
                    <span>{{ answers_option }}</span>
                    <p>{{ answers_optionname }}</p>
                </div>
                {{/if}}
                {{/each}}
            </div>
            <div class="rows next-rows">
                <div class="item"></div>
            </div>
        </div>
    </div>
</script>
<script id="readArtCont-21" type="text/x-handlebars-template">
    <div class="contents">
        {{#if question_title}}
        <p class="tips">{{ question_title }}</p>
        {{/if}}
        <div class="sort-cont">
            <ul>
                {{#each option}}
                <li>
                    <span>{{ answers_optionname }}</span>
                    <p>{{ answers_option }}</p>
                </li>
                {{/each}}
            </ul>
        </div>
    </div>
</script>
{/literal}