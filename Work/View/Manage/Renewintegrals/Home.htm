<div class="content">
    <div class="bg-f">
        <h2 class="p20">{$moduleOne.module_name}
            <span class="fr">
                <button type="button" class="btn btn-primary ml10 bakFromurl">返回</button>
            </span>
        </h2>
        <div class="p20 f14">
            <div class="form-group">
                <form action="?" method="get" accept-charset="utf-8">
                    <input name="site_id" type="hidden" value="{$websites.site_id}">
                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tr>
                            <td width="6%"  class="pr10 ">
                                <select name="coursetype_id">
                                    <option value="">班组类别</option>
                                    <option value="65" {if $datatype.coursetype_id =='65'}selected{/if}>美语</option>
                                    <option value="64" {if $datatype.coursetype_id =='64'}selected{/if}>课辅</option>
                                    <option value="61" {if $datatype.coursetype_id =='61'}selected{/if}>阅读班</option>
                                </select>
                            </td>
                            <td width="6%"  class="pr10 ">
                                <select name="bookintegral_isaccount">
                                    <option value="">是否结算</option>
                                    <option value="1" {if $datatype.renewintegral_isaccount ==1}selected{/if}>已结算</option>
                                    <option value="0"  {if $datatype.renewintegral_isaccount =='0'}selected{/if}>未结算</option>
                                </select>
                            </td>
                            <td width="4%">搜索：</td>
                            <td width="15%"><input class="form-control input-sm" id="inputEmail3" placeholder="请输入学生编号/订单编号" value="{$datatype['keyword']}" type="text" name="keyword"></td>
                            <td width="63%"><button type="submit" class="btn btn-primary ml10"><span class="glyphicon glyphicon-search c-f"></span> 搜索</button> </td>
                        </tr>
                    </table>
                </form>
            </div>
            <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table tc table-hover table-bordered">
                <thead>
                <tr>
                    <th>序号</th>
                    <th>推荐学生编号</th>
                    <th>交易编号</th>
                    <th>班组</th>
                    <th>课时总数</th>
                    <th>剩余课时数</th>
                    <th>支付日期</th>
                    <th>支付金额</th>
                    <th>积分数量</th>
                    <th>结算状态</th>
                    <th>操作</th>
                </tr>
                </thead>
                <tbody>
                {if $dataList}
                {foreach from=$dataList item=dataVar key=key}
                <tr id="list-{$dataVar.renewintegral_id}">
                    <td>{$dataVar.renewintegral_id}</td>
                    <td>{$dataVar.student_branch}</td>
                    <td>{$dataVar.trading_pid}</td>
                    <td>{if $dataVar.coursetype_id eq '65'}美语{elseif $dataVar.coursetype_id eq '64'}课辅{elseif $dataVar.coursetype_id eq '61'}阅读班{/if}</td>
                    <td>{$dataVar.renewintegral_buytimes}</td>
                    <td>{$dataVar.renewintegral_lefttimes}</td>
                    <td>{$dataVar.renewintegral_day}</td>
                    <td>{$dataVar.renewintegral_price}</td>
                    <td>{$dataVar.renewintegral_integral}</td>
                    <td>{if $dataVar.renewintegral_isaccount eq '1'}已结算{elseif $dataVar.renewintegral_isaccount eq '0'}未结算{/if}</td>
                    <td>
                        {if $dataVar.renewintegral_isaccount eq '0'}
                        <a href="javascript:;" data-element="list-{$dataVar.renewintegral_id}" data-tiptitle="你确定要结算吗？" data-url="/{$u}?c=account&student_branch={$dataVar.student_branch}&integral={$dataVar.renewintegral_integral}&renewintegral_id={$dataVar.renewintegral_id}" class="btn btn-primary btn-sm btn-confirm-action">
                            <span class="glyphicon glyphicon-eye-open c-f"></span> 结算</a>
                        {/if}
                    </td>
                </tr>
                {/foreach}
                {/if}
                </tbody>
            </table>
            <div class="pagemenu">{$pagelist}</div>
        </div>
    </div>
</div>