<div class="content">
    <div id="container">
        <div class="bg-f row py20">
            <form action="/{$u}?c={$act}" method="post" enctype="application/x-www-form-urlencoded" class="AjaxForm">
                <input name="openclasstype_id" type="hidden" value="{$dataVar.openclasstype_id}">
                <input name="site_id" type="hidden" value="{$websites.site_id}">
                <h2 class="p20">{$moduleOne.module_name}<span class="fr">
                <button type="submit" class=" btn btn-success btn-cons"><i class="fa fa-check"></i>提交保存</button>
                <button type="button" class="btn btn-primary ml10 bakFromurl">返回</button></span>
                </h2>
                <div class="col-md-6" id="Form-Box-Operating">
                    <div class="pl20 f14">
                        <div class="form-group col-md-4">
                            <label for="openclasstype_cnname">公开课类型中文名</label>
                            <input name="openclasstype_cnname" id="openclasstype_cnname" value="{$dataVar['openclasstype_cnname']}" type="text" class="form-control" placeholder="请输入公开课类型中文名">
                        </div>
                        <div class="form-group col-md-4">
                            <label for="openclasstype_enname">公开课类型英文名</label>
                            <input name="openclasstype_enname" id="openclasstype_enname" value="{$dataVar['openclasstype_enname']}" type="text" class="form-control" placeholder="请输入公开课类型英文名">
                        </div>
                        <div class="form-group col-md-4">
                            <label for="openclasstype_branch">公开课类型编号</label>
                            <input name="openclasstype_branch" id="openclasstype_branch" value="{$dataVar['openclasstype_branch']}" type="text" class="form-control" placeholder="请输入公开课类型编号">
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
