<div class="content">
    <div id="container">
        <div class="bg-f row py20">
            <form action="/{$u}?c={$act}" role="form" method="post" enctype="application/x-www-form-urlencoded" class="AjaxForm form-horizontal">
                <input name="module_id" type="hidden" value="{$dataVar.module_id}">
                <h2 class="p20">系统功能模块<span class="fr">
                <button type="submit" class=" btn btn-success btn-cons"><i class="fa fa-check"></i>提交保存</button>
                <button type="button" class="btn btn-primary ml10 bakFromurl">返回</button></span>
                </h2>
                <div class="col-md-6" id="Form-Box-Operating">
                    <div class="pl20 py20 f14">
                        <div class="form-group">
                            <label for="module_name" class="col-sm-2 control-label">模块名称</label>
                            <div class="col-sm-10">
                                <input name="module_name" id="module_name" value="{$dataVar.module_name}" type="text" class="form-control" placeholder="请输入模块名称">
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="module_type" class="col-sm-2 control-label">模块类型</label>
                            <div class="col-sm-4">
                                <select name="module_type" id="module_type" class="form-control">
                                    <option value="0"  {if $dataVar.module_type == '0'}selected{/if}>系统功能</option>
                                    <option value="1"  {if $dataVar.module_type == '1'}selected{/if}>站群功能</option>
                                </select>
                            </div>
                            <label for="module_weight" class="col-sm-2 control-label">模块排序</label>
                            <div class="col-sm-4">
                                <input name="module_weight" id="module_weight" value="{$dataVar.module_weight}" type="text" class="form-control" placeholder="越小越在前，如1">
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="module_mark" class="col-sm-2 control-label">模块标识</label>
                            <div class="col-sm-4">
                                <input name="module_mark" id="module_mark" value="{$dataVar.module_mark}" type="text" class="form-control" placeholder="请输入模块标识">
                            </div>
                            <label for="module_link" class="col-sm-2 control-label">模块连接</label>
                            <div class="col-sm-4">
                                <input name="module_link" id="module_link" value="{$dataVar.module_link}" type="text" class="form-control" placeholder="请输入模块连接">
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="module_style" class="col-sm-2 control-label">模块样式</label>
                            <div class="col-sm-4">
                                <input name="module_style" id="module_style" value="{$dataVar.module_style}" type="text" class="form-control" placeholder="请输入模块样式">
                            </div>
                            <label for="module_styletype" class="col-sm-2 control-label">DIY样式</label>
                            <div class="col-sm-4 pt7">
                                <input name="module_styletype" id="module_styletype" value="1"  {if $dataVar.module_styletype == '1'}checked{/if} type="checkbox" class="ace">
                                <span class="lbl"></span>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="color1" class="col-sm-2 color1 control-label">模块颜色1</label>
                            <div class="col-sm-1 pt7">
                                <input name="module_color" id="color1" value="color1" {if $dataVar.module_color == 'color1' or !$dataVar.module_color}checked{/if} type="radio" class="ace">
                                <span class="lbl"></span>
                            </div>
                            <label for="color2" class="col-sm-2 color2 control-label">模块颜色2</label>
                            <div class="col-sm-1 pt7">
                                <input name="module_color" id="color2" value="color2" {if $dataVar.module_color == 'color2'}checked{/if} type="radio" class="ace">
                                <span class="lbl"></span>
                            </div>
                            <label for="color3" class="col-sm-2 color3 control-label">模块颜色3</label>
                            <div class="col-sm-1 pt7">
                                <input name="module_color" id="color3" value="color3" {if $dataVar.module_color == 'color3'}checked{/if} type="radio" class="ace">
                                <span class="lbl"></span>
                            </div>
                            <label for="color4" class="col-sm-2 color4 control-label">模块颜色4</label>
                            <div class="col-sm-1 pt7">
                                <input name="module_color" id="color4" value="color4" {if $dataVar.module_color == 'color4'}checked{/if} type="radio" class="ace">
                                <span class="lbl"></span>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="module_introduce"class="col-sm-2 control-label">功能介绍</label>
                            <div class="col-sm-10">
                                <textarea name="module_introduce" id="module_introduce" class="form-control" rows="3">{$dataVar.module_introduce}</textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>