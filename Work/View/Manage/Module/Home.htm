<div class="content">
    <div class="bg-f">
        <h2 class="p20">系统功能模块
            <span class="fr">
                <a href="/{$u}/Add" class="btn btn-success dropdown-toggle btn-demo-space">+新增功能</a>
                <button type="button" class="btn btn-primary ml10 bakFromurl">返回</button>
            </span>
        </h2>
        <div class="p20 f14">
            <div class="form-group">
                <form action="?" method="get" accept-charset="utf-8">
                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tr>
                            <td width="4%">分类：</td>
                            <td width="14%">
                                <select name="type" class="form-control dropdown">
                                    <option value="">选择类型</option>
                                    <option value="0">系统功能</option>
                                    <option value="1">站群功能</option>
                                </select>
                            </td>
                            <td width="4%">搜索：</td>
                            <td width="15%"><input class="form-control input-sm" id="inputEmail3" placeholder="请输入模块名称或标示" value="{$datatype['keyword']}" type="text" name="keyword"></td>
                            <td width="63%"><button type="submit" class="btn btn-primary ml10"><span class="glyphicon glyphicon-search c-f"></span> 搜索</button> </td>
                        </tr>
                    </table>
                </form>
            </div>
            <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table tc table-hover table-bordered">
                <thead>
                <tr>
                    <th>序号</th>
                    <th>模块名称</th>
                    <th>模块类型</th>
                    <th>模块标识</th>
                    <th>模块连接</th>
                    <th>排序</th>
                    <th>操作</th>
                </tr>
                </thead>
                <tbody>
                {if $dataList}
                {foreach from=$dataList item=dataVar key=key}
                <tr id="list-{$dataVar.module_id}">
                    <td>{$key+1}</td>
                    <td>{$dataVar.module_name}</td>
                    <td>{if $dataVar.module_type == '0'}系统应用{else}分站应用{/if}</td>
                    <td>{$dataVar.module_mark}</td>
                    <td>{$dataVar.module_link}</td>
                    <td>{$dataVar.module_weight}</td>
                    <td>
                        <a href="/{$u}/Edit?id={$dataVar.module_id}" class="btn btn-primary btn-sm"><span class="glyphicon glyphicon-pencil c-f"></span> 编辑</a>
                        <a href="{$dataVar.module_link}" class="btn btn-primary btn-sm"><span class="glyphicon glyphicon-magnet c-f"></span> 管理内容</a>
                        <a href="/{$u}/Websites?id={$dataVar.module_id}" class="btn btn-primary btn-sm"><span class="glyphicon glyphicon-link c-f"></span> 站点配置</a>
                        <a  href="javascript:;" data-element="list-{$dataVar.module_id}" data-url="/{$u}?c=Del&id={$dataVar.module_id}" class="btn btn-danger btn-sm btn-del-action"><span class="glyphicon glyphicon-remove c-f"></span> 删除</a>
                    </td>
                </tr>
                {/foreach}
                {/if}
                </tbody>
            </table>
            <div class="pagemenu">{$pagelist}</div>
        </div>
    </div>
</div>