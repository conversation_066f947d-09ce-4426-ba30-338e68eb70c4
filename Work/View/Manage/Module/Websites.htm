<div class="content">
    <div class="bg-f">
        <h2 class="p20">系统功能模块
            <span class="fr">
                <a href="/{$u}/Add" class="btn btn-success dropdown-toggle btn-demo-space">+新增功能</a>
                <button type="button" class="btn btn-primary ml10 bakFromurl">返回</button>
            </span>
        </h2>
        <div class="p20 f14">
            <div class="form-group">
                <form action="?" method="get" accept-charset="utf-8">
                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tr>
                            <td width="4%">分类：</td>
                            <td width="14%">
                                <select name="type" class="form-control dropdown">
                                    <option value="">选择类型</option>
                                    <option value="0">系统功能</option>
                                    <option value="1">站群功能</option>
                                </select>
                            </td>
                            <td width="4%">搜索：</td>
                            <td width="15%"><input class="form-control input-sm" id="inputEmail3" placeholder="请输入昵称或者手机号" value="{$datatype['keyword']}" type="text" name="keyword"></td>
                            <td width="63%"><button type="submit" class="btn btn-primary ml10"><span class="glyphicon glyphicon-search c-f"></span> 搜索</button> </td>
                        </tr>
                    </table>
                </form>
            </div>
            <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table tc table-hover table-bordered">
                <thead>
                <tr>
                    <th>序号</th>
                    <th>站点名称</th>
                    <th>排序</th>
                    <th>开放</th>
                    <th>操作</th>
                </tr>
                </thead>
                <tbody>
                {if $dataList}
                {foreach from=$dataList item=dataVar key=key}
                <tr>
                    <td>{$key+1}</td>
                    <td>{$dataVar.site_title}</td>
                    <td>{$dataVar.site_weight}</td>
                    <td>
                        {if $dataVar.thestatus == '1'}<a class="cp"><span class="glyphicon glyphicon-ok-circle f18 text-primary"></span></a>
                        {else}<a class="cp"><span class="glyphicon glyphicon-remove-circle f18 text-danger"></span></a>{/if}
                    </td>
                    <td>
                        {if $dataVar.thestatus == '1'}
                        <a href="javascript:;" data-url="/{$u}?c=DelApply&site_id={$dataVar.site_id}&module_id={$module_id}" class="btn btn-send-action btn-danger btn-sm">
                            <span class="glyphicon glyphicon-export c-f"></span> 取消</a>
                        {else}
                        <a href="javascript:;" data-url="/{$u}?c=Apply&site_id={$dataVar.site_id}&module_id={$module_id}" class="btn btn-send-action btn-success btn-sm">
                            <span class="glyphicon glyphicon-saved c-f"></span> 开放</a>
                        {/if}
                    </td>
                </tr>
                {/foreach}
                {/if}
                </tbody>
            </table>
            <div class="pagemenu">{$pagelist}</div>
        </div>
    </div>
</div>