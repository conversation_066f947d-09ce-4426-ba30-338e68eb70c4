<div class="content">
    <div class="bg-f">
        <h2 class="p20">
            <span class="fr">
                <a href="/{$u}/AddChapter?site_id={$websites.site_id}&course_id={$course_id}" class="btn btn-success dropdown-toggle btn-demo-space">+新增资料</a>
                <button type="button" class="btn btn-primary ml10 bakFromurl">返回</button>
            </span>课程章节明细管理</h2>
        <div class="p20 f14">
            <div class="form-group">
                <form action="/{$u}/Chapter" method="get" accept-charset="utf-8">
                    <input name="site_id" value="{$websites.site_id}" type="hidden">
                    <input name="course_id" value="{$course_id}" type="hidden">
                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tr>
                            <!--<td width="4%">课程类型：</td>-->
                            <!--<td width="15%">-->
                                <!--<select name="course_type" class="form-control">-->
                                    <!--<option value="">选择课程类型</option>-->
                                    <!--<option value="0" {if $datatype.course_type eq '0'}selected{/if}>公开课</option>-->
                                    <!--<option value="1" {if $datatype.course_type eq '1'}selected{/if}>职业课</option>-->
                                <!--</select>-->
                            <!--</td>-->
                            <td width="4%">搜索：</td>
                            <td width="15%"><input class="form-control input-sm" id="inputEmail3" placeholder="章节名称" value="{$datatype['keyword']}" type="text" name="keyword"></td>
                            <td width="63%"><button type="submit" class="btn btn-primary ml10"><span class="glyphicon glyphicon-search c-f"></span> 搜索</button> </td>
                        </tr>
                    </table>
                </form>
            </div>

            <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table tc table-hover table-bordered">
                <thead>
                <tr>
                    <th>章节ID</th>
                    <th>课程名称</th>
                    <th>章节名称</th>
                    <th>章节排序</th>
                    <th>更新时间</th>
                    <th>创建时间</th>
                    <th>操作</th>
                </tr>
                </thead>
                <tbody>
                {if $dataList}
                {foreach from=$dataList item=dataVar}
                <tr id="list-{$dataVar.chapter_id}">
                    <td>{$dataVar.chapter_id}</td>
                    <td>{$dataVar.course_name}</td>
                    <td>{$dataVar.chapter_name}</td>
                    <td>{$dataVar.chapter_sort}</td>
                    <td>{$dataVar.chapter_updatatime|date_format:'%Y-%m-%d %H:%M:%S'}</td>
                    <td>{$dataVar.chapter_createtime|date_format:'%Y-%m-%d %H:%M:%S'}</td>
                    <td>
                        <a href="/{$u}/TrainingVideo?site_id={$websites.site_id}&chapter_id={$dataVar.chapter_id}" class="btn btn-primary btn-sm"><span class="glyphicon glyphicon-certificate c-f"></span> 培训视频明细管理</a>
                        <a href="/{$u}/EditChapter?chapter_id={$dataVar.chapter_id}&site_id={$websites.site_id}&course_id={$course_id}" class="btn btn-primary btn-sm"><span class="glyphicon glyphicon-pencil c-f"></span> 编辑</a>
                        <a  href="javascript:;" data-element="list-{$dataVar.chapter_id}" data-url="/{$u}?c=DelChapter&id={$dataVar.chapter_id}&site_id={$websites.site_id}&course_id={$course_id}" class="btn btn-danger btn-sm btn-del-action"><span class="glyphicon glyphicon-remove c-f"></span> 删除</a>
                    </td>
                </tr>
                {/foreach}
                {/if}
                </tbody>
            </table>
            <div class="pagemenu">{$pagelist}</div>
        </div>
    </div>
</div>
