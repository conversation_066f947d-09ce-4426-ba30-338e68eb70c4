<div class="content">
    <div id="container">
        <div class="bg-f row py20">
            <form action="/{$u}?c={$act}" method="post" enctype="application/x-www-form-urlencoded" class="AjaxForm">
                <input name="chapter_id" type="hidden" value="{$dataVar.chapter_id}">
                <input name="site_id" type="hidden" value="{$websites.site_id}">
                <input name="course_id" type="hidden" value="{$course_id}">
                <h2 class="p20">课程章节明细管理<span class="fr">
                <button type="submit" class=" btn btn-success btn-cons"><i class="fa fa-check"></i>提交保存</button>
                <button type="button" class="btn btn-primary ml10 bakFromurl">返回</button></span>
                </h2>
                <div class="col-md-6" id="Form-Box-Operating">
                    <div class="pl20 f14">
                        <div class="form-group col-md-5">
                            <label for="chapter_name">章节名称</label>
                            <input name="chapter_name" id="chapter_name" value="{$dataVar['chapter_name']}" type="text" class="form-control" placeholder="请输入章节名称">
                        </div>
                        <div class="form-group col-md-5">
                            <label for="chapter_sort">章节排序</label>
                            <input name="chapter_sort" id="chapter_sort" value="{$dataVar['chapter_sort']}" type="text" class="form-control" placeholder="请输入章节排序">
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
