<div class="content">
    <div class="bg-f">
        <h2 class="p20">
            <span class="fr">
                <a href="/{$u}/AddTrainingVideo?site_id={$websites.site_id}&chapter_id={$chapter_id}" class="btn btn-success dropdown-toggle btn-demo-space">+新增资料</a>
                <button type="button" class="btn btn-primary ml10 bakFromurl">返回</button>
            </span>培训视频明细管理</h2>
        <div class="p20 f14">
            <div class="form-group">
                <form action="/{$u}/TrainingVideo" method="get" accept-charset="utf-8">
                    <input name="site_id" value="{$websites.site_id}" type="hidden">
                    <input name="chapter_id" value="{$chapter_id}" type="hidden">
                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tr>
                            <td width="4%">教案模式：</td>
                            <td width="15%">
                                <select name="trainhour_class" class="form-control">
                                    <option value="">选择教案模式</option>
                                    <option value="0" {if $datatype.trainhour_class eq '0'}selected{/if}>视频模式</option>
                                    <option value="1" {if $datatype.trainhour_class eq '1'}selected{/if}>音频模式</option>
                                    <option value="2" {if $datatype.trainhour_class eq '2'}selected{/if}>PPT模式</option>
                                </select>
                            </td>
                            <td width="4%">搜索：</td>
                            <td width="15%"><input class="form-control input-sm" id="inputEmail3" placeholder="培训名称" value="{$datatype['keyword']}" type="text" name="keyword"></td>
                            <td width="63%"><button type="submit" class="btn btn-primary ml10"><span class="glyphicon glyphicon-search c-f"></span> 搜索</button> </td>
                        </tr>
                    </table>
                </form>
            </div>

            <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table tc table-hover table-bordered">
                <thead>
                <tr>
                    <th>培训ID</th>
                    <th>章节名称</th>
                    <th>培训名称</th>
                    <th>教案模式</th>
                    <th>教案封面</th>
                    <th>文件地址</th>
                    <th>更新时间</th>
                    <th>创建时间</th>
                    <th>操作</th>
                </tr>
                </thead>
                <tbody>
                {if $dataList}
                {foreach from=$dataList item=dataVar}
                <tr id="list-{$dataVar.trainhour_id}">
                    <td>{$dataVar.trainhour_id}</td>
                    <td>{$dataVar.chapter_name}</td>
                    <td>{$dataVar.trainhour_name}</td>
                    <td>
                        {if $dataVar.trainhour_class eq '0'}视频模式
                        {elseif $dataVar.trainhour_class eq '1'}音频模式
                        {elseif $dataVar.trainhour_class eq '2'}PPT模式
                        {/if}
                    </td>
                    <td>{if $dataVar.trainhour_coverimg != ''}<a data-imgurl="{$dataVar.trainhour_coverimg}" class="cp Opon-Img-View showico" title="查看效果"></a>{/if}</td>
                    <td>
                        {if $dataVar.trainhour_fileurl}
                        <video src="{$dataVar.trainhour_fileurl}" controls="controls" style="width:200px;height:100px;">
                        {/if}
                    </td>
                    <td>{$dataVar.trainhour_updatatime|date_format:'%Y-%m-%d %H:%M:%S'}</td>
                    <td>{$dataVar.trainhour_createtime|date_format:'%Y-%m-%d %H:%M:%S'}</td>
                    <td>
                        <a href="/{$u}/EditTrainingVideo?trainhour_id={$dataVar.trainhour_id}&site_id={$websites.site_id}&chapter_id={$chapter_id}" class="btn btn-primary btn-sm"><span class="glyphicon glyphicon-pencil c-f"></span> 编辑</a>
                        <a  href="javascript:;" data-element="list-{$dataVar.trainhour_id}" data-url="/{$u}?c=DelTrainingVideo&id={$dataVar.trainhour_id}&chapter_id={$chapter_id}" class="btn btn-danger btn-sm btn-del-action"><span class="glyphicon glyphicon-remove c-f"></span> 删除</a>
                    </td>
                </tr>
                {/foreach}
                {/if}
                </tbody>
            </table>
            <div class="pagemenu">{$pagelist}</div>
        </div>
    </div>
</div>
