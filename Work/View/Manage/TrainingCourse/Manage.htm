<div class="content">
    <div id="container">
        <div class="bg-f row py20">
            <form action="/{$u}?c={$act}" method="post" enctype="application/x-www-form-urlencoded" class="AjaxForm">
                <input name="course_id" type="hidden" value="{$dataVar.course_id}">
                <input name="site_id" type="hidden" value="{$websites.site_id}">
                <input name="company_id" type="hidden" value="{$dataVar.company_id}">
                <h2 class="p20">{$moduleOne.module_name}<span class="fr">
                <button type="submit" class=" btn btn-success btn-cons"><i class="fa fa-check"></i>提交保存</button>
                <button type="button" class="btn btn-primary ml10 bakFromurl">返回</button></span>
                </h2>
                <div class="col-md-6" id="Form-Box-Operating">
                    <div class="pl20 f14">
                        <div class="form-group col-md-5" id="">
                            <label for="course_name">课程名称</label>
                            <input name="course_name" id="course_name" value="{$dataVar['course_name']}" type="text" class="form-control" placeholder="请输入课程名称">
                        </div>
                        <div class="form-group col-md-5">
                            <label for="course_type">课程类型</label>
                            <select name="course_type" class="form-control" id="course_type">
                                <option value="">请选择课程类型</option>
                                <option value="0" {if $dataVar.course_type eq '0'}selected{/if}>公开课</option>
                                <option value="1" {if $dataVar.course_type eq '1'}selected{/if}>职业课</option>
                            </select>
                        </div>
                        <div class="form-group col-md-5" id="career_id" {if $dataVar.course_type == '1'}style="display: block"{else}style="display: none"{/if}>
                            <label for="career_id">职业课所属职业</label>
                            <select name="career_id" class="form-control">
                                <option value="">请选择所属职业</option>
                                {if $career}
                                {foreach from=$career item=Tbvar key=key}
                                <option value="{$Tbvar.career_id}" {if $dataVar.career_id == $Tbvar.career_id}selected{/if}>{$Tbvar.career_cnname}</option>
                                {/foreach}
                                {/if}
                            </select>
                        </div>
                        <div class="form-group col-md-5" id="stage_id" {if $dataVar.course_type == '1'}style="display: block"{else}style="display: none"{/if}>
                            <label for="stage_id">职业课所属阶段</label>
                            <select name="stage_id" class="form-control">
                                <option value="">请选择所属阶段</option>
                                {if $stage}
                                {foreach from=$stage item=Tbvar key=key}
                                <option value="{$Tbvar.stage_id}" {if $dataVar.stage_id == $Tbvar.stage_id}selected{/if}>{$Tbvar.stage_cnname}</option>
                                {/foreach}
                                {/if}
                            </select>
                        </div>
                        <div class="form-group col-md-5" id="openclasstype_id" {if $dataVar.course_type == '0'}style="display: block"{else}style="display: none"{/if}>
                            <label for="openclasstype_id">公开课所属类型</label>
                            <select name="openclasstype_id" class="form-control">
                                <option value="">请选择所属类型</option>
                                {if $openclasstype}
                                {foreach from=$openclasstype item=Tbvar key=key}
                                <option value="{$Tbvar.openclasstype_id}" {if $dataVar.openclasstype_id == $Tbvar.openclasstype_id}selected{/if}>{$Tbvar.openclasstype_cnname}</option>
                                {/foreach}
                                {/if}
                            </select>
                        </div>
                        <div class="clear"></div>
                        <div class="form-group col-md-5">
                            <label for="course_recommend">是否推荐课程</label>
                            <select name="course_recommend" class="form-control" id="course_recommend">
                                <option value="0" {if $dataVar.course_recommend eq '0'}selected{/if}>否</option>
                                <option value="1" {if $dataVar.course_recommend eq '1'}selected{/if}>是</option>
                            </select>
                        </div>
                        <div class="form-group col-md-5">
                            <label for="course_popular">是否热门课程</label>
                            <select name="course_popular" class="form-control" id="course_popular">
                                <option value="0" {if $dataVar.course_popular eq '0'}selected{/if}>否</option>
                                <option value="1" {if $dataVar.course_popular eq '1'}selected{/if}>是</option>
                            </select>
                        </div>
                        <div class="form-group col-md-10">
                            <label for="question_content">课程介绍</label>
                            <textarea name="course_intro" id="question_content" class="form-control" placeholder="请输入课程介绍" rows="3">{$dataVar.course_intro}</textarea>
                        </div>
                        <div class="form-group">
                            <div class="col-md-9  mb10">
                                <label for="course_img">课程图片</label>
                                <input name="course_img" id="course_img" value="{$dataVar.course_img}" type="text" class="form-control" placeholder="先点击右侧按钮上传图片，或直接书写图片网址">
                            </div>
                            <div class="col-md-3">
                                <label for="up_img">&nbsp;</label>
                                <a data-color="rgb(255, 255, 255)" data-color-format="hex" id="cp4" class="btn btn-primary my-colorpicker-control upload-btn" data-colorpicker-guid="1">
                                    <span class="glyphicon glyphicon-floppy-open"></span>
                                    选择图片上传<input type="file" id="up_img" class="ipt-img-click" data-element="list_img_view" data-originalipt="course_img" data-thumbnailipt="tb_imgthum"></a>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="upload-img" id="list_img_view">
                                <div class="f16 tc default {if $dataVar.course_img}none{/if}">
                                    <p class="f30"><span class="glyphicon glyphicon-picture"></span></p>
                                    <p>请先上传 课程图片图片 后预览</p>
                                </div>
                                <div class="img set {if !$dataVar.course_img}none{/if}"><img src="{$dataVar.course_img}"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
<script src="{$JsUrl}jquery.min.js" type="text/javascript"></script>
{literal}
<script>
    $("#course_type").change(function(){
        var v = $("#course_type").val();
        if (v == '0') {
            $('#career_id').hide();
            $('#stage_id').hide();
            $('#openclasstype_id').show();
        } else if (v == '1') {
            $('#career_id').show();
            $('#stage_id').show();
            $('#openclasstype_id').hide();
        }
    });
</script>
{/literal}
