<div class="content">
    <div class="bg-f">
        <h2 class="p20">
            <span class="fr">
                <button type="button" class="btn btn-primary ml10 bakFromurl">返回</button>
            </span>适配职务
        </h2>
        <div class="p20 f14">
            <div class="form-group">
                <form role="form" action="/{$u}?c=batchSetQuestion" method="post" enctype="application/x-www-form-urlencoded" class="AjaxForm">
                    <input name="course_id" type="hidden" value="{$course_id}">
                    <table width="100%" border="0" cellspacing="0" cellpadding="0"
                           class="table tc table-hover table-bordered">
                        <thead>
                        <tr>
                            <th>选择</th>
                            <th>职业ID</th>
                            <th>职业名称</th>
                            <th>职业编号</th>
                            <th>是否适配</th>
                            <th>操作</th>
                        </tr>
                        </thead>
                        <tbody>
                        {if $dataList}
                        {foreach from=$dataList item=dataVar key='k'}
                        <tr id="list-{$dataVar.career_id}">
                            <td>
                                <input name="tab_list[]" value="{$dataVar.career_id}" type="checkbox" class="ace">
                                <span class="lbl"></span>
                            </td>
                            <td>{$dataVar.career_id}</td>
                            <td>{$dataVar.career_cnname}</td>
                            <td>{$dataVar.career_branch}</td>
                            <td>{if $dataVar.thestatus eq '0'}未适配{elseif $dataVar.thestatus eq '1'}已适配{/if}</td>

                            <td>
                                {if $dataVar.thestatus eq '0'}
                                <a href="javascript:;" data-url="/{$u}?c=Atapply&course_id={$course_id}&career_id={$dataVar.career_id}"
                                   class="btn-send-action">
                                    <button type="button" class="btn btn-primary btn-sm">适配</button>
                                </a>
                                {else}
                                <a href="javascript:;"
                                   data-url="/{$u}?c=Atapplydel&course_id={$course_id}&career_id={$dataVar.career_id}"
                                   class="btn-send-action">
                                    <button type="button" class="btn btn-danger btn-sm">取消适配</button>
                                </a>
                                {/if}
                            </td>
                        </tr>
                        {/foreach}
                        {/if}

                        <tr>
                            <td><label form="Choice_All">全选
                                <input name="form-field-checkbox" id="Choice_All" type="checkbox" class="ace">
                                <span class="lbl"></span></label></td>
                            <td colspan="14">
                                <div class="col-md-2">
                                    <select name="type" id="type" class="form-control">
                                        <option value="1">批量适配</option>
                                        <option value="2">批量取消适配</option>
                                    </select>
                                </div>
                                <button type="submit" class="fl btn btn-primary ml20">
                                    批量操作
                                </button>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </form>
            </div>
        </div>
    </div>
</div>
{include file="review.htm"}