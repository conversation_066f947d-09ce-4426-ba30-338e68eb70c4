<div class="content">
    <div id="container">
        <div class="bg-f row py20">
            <form action="/{$u}?c={$act}" method="post" enctype="application/x-www-form-urlencoded" class="AjaxForm">
                <input name="trainhour_id" type="hidden" value="{$dataVar.trainhour_id}">
                <input name="site_id" type="hidden" value="{$websites.site_id}">
                <input name="chapter_id" type="hidden" value="{$chapter_id}">
                <h2 class="p20">培训视频明细管理<span class="fr">
                <button type="submit" class=" btn btn-success btn-cons"><i class="fa fa-check"></i>提交保存</button>
                <button type="button" class="btn btn-primary ml10 bakFromurl">返回</button></span>
                </h2>
                <div class="col-md-6" id="Form-Box-Operating">
                    <div class="pl20 f14">
                        <div class="form-group col-md-5">
                            <label for="trainhour_name">培训名称</label>
                            <input name="trainhour_name" id="trainhour_name" value="{$dataVar['trainhour_name']}" type="text" class="form-control" placeholder="请输入培训名称">
                        </div>
                        <div class="form-group">
                            <div class="col-md-9  mb10">
                                <label for="trainhour_coverimg">教案封面</label>
                                <input name="trainhour_coverimg" id="trainhour_coverimg" value="{$dataVar.trainhour_coverimg}" type="text" class="form-control" placeholder="先点击右侧按钮上传图片，或直接书写图片网址">
                            </div>
                            <div class="col-md-3">
                                <label for="up_img">&nbsp;</label>
                                <a data-color="rgb(255, 255, 255)" data-color-format="hex" id="cp4" class="btn btn-primary my-colorpicker-control upload-btn" data-colorpicker-guid="1">
                                    <span class="glyphicon glyphicon-floppy-open"></span>
                                    选择图片上传<input type="file" id="up_img" class="ipt-img-click" data-element="list_img_view" data-originalipt="trainhour_coverimg" data-thumbnailipt="tb_imgthum"></a>
                            </div>
                        </div>
                        <div class="form-group col-md-4">
                            <label for="trainhour_class">教案模式</label>
                            <select name="trainhour_class" class="form-control" id="trainhour_class">
                                <option value="">请选择教案模式</option>
                                <option value="0" {if $dataVar.trainhour_class eq '0'}selected{/if}>视频模式</option>
                                <option value="1" {if $dataVar.trainhour_class eq '1'}selected{/if}>音频模式</option>
                                <option value="2" {if $dataVar.trainhour_class eq '2'}selected{/if}>PPT模式</option>
                            </select>
                        </div>
                        <div class="clear"></div>
                        <div class="form-group col-md-10" id="Trainhour-Fileurl" {if $dataVar.trainhour_class == '0'}style="display: block"{else}style="display: none"{/if}>
                        <label for="Trainhour-Fileurl">视频地址</label>
                        <input name="trainhourFileurl" value="{$dataVar.trainhour_fileurl}" type="text" class="form-control" placeholder="请输入视频地址">
                    </div>
                    <div class="form-group" id="trainhourFileurl" {if $dataVar.trainhour_class == '1'}style="display: block"{else}style="display: none"{/if}>
                    <div class="col-md-9  mb10">
                        <label for="trainhour_fileurl">音频地址</label>
                        <input name="trainhour_fileurl" id="trainhour_fileurl" value="{$dataVar.trainhour_fileurl}" type="text" class="form-control" placeholder="先点击右侧按钮上传文件，或直接输入音频地址">
                    </div>
                    <div class="col-md-3">
                        <label>&nbsp;</label>
                        <a data-color="rgb(255, 255, 255)" class="btn btn-primary my-colorpicker-control upload-btn" data-colorpicker-guid="1">
                            <span class="glyphicon glyphicon-export"></span>
                            选择资料上传<input type="file" class="oss-file-click" data-originalipt="trainhour_fileurl"></a>
                    </div>
                </div>
                <div role="tabpanel" class="tab-pane">
                    <div class="form-group">
                        <div class="col-md-12">
                            <div class="upload-img" id="list_file_views" {if $dataVar.trainhour_class == '2'}style="display: block"{else}style="display: none"{/if}>
                            <div class="f16 tf default">
                                <div style="width: 100%;">
                                    <ul class="FileList fix">
                                        {if $imglist}
                                        {foreach from=$imglist item=imgvar}
                                        <li><a href="{$imgvar.pptpage_imgurl}" target="_blank"><img src="{$imgvar.pptpage_imgurl}"></a>
                                            <i class="glyphicon glyphicon-remove delThis"></i>
                                            <input name="affix_img[]" type="hidden" value="{$imgvar.pptpage_imgurl}">
                                            <input name="affix_thumburl[]" type="hidden" value="{$imgvar.pptpage_thumburl}">
                                            <input name="affix_imgname[]" type="text" value="{$imgvar.pptpage_name}" style="width:100%">
                                        </li>
                                        {/foreach}
                                        {/if}
                                    </ul>
                                </div>
                                <div class="upload-btn">
                                    <p class="f30"><span class="glyphicon glyphicon-file"></span></p>
                                    <p>请点击上传照片<input type="file" class="ipt-imgs-click" data-element="list_file_views"></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
        </div>
    </div>
</div>
<div class="col-md-6">
    <div class="row">
        <div class="col-md-6">
            <div class="upload-img" id="list_img_view">
                <div class="f16 tc default {if $dataVar.trainhour_coverimg}none{/if}">
                    <p class="f30"><span class="glyphicon glyphicon-picture"></span></p>
                    <p>请先上传 教案封面图片 后预览</p>
                </div>
                <div class="img set {if !$dataVar.trainhour_coverimg}none{/if}"><img src="{$dataVar.trainhour_coverimg}"></div>
            </div>
        </div>
    </div>
</div>
</form>
</div>
</div>
</div>
<script src="{$JsUrl}jquery.min.js" type="text/javascript"></script>
{literal}
<script>
    //上传图片插件
    $(document).on("change", ".ipt-imgs-click",function() {
        $(".loader-inner").removeClass('none');
        var imgelement = $(this).data("element"),imgoriginalipt = $(this).data("originalipt"),imgthumbnailipt = $(this).data("thumbnailipt");
        var file = this.files[0];
        if (!file || !file.type.match(/image.*/)) {
            warningFromTip("只可以上传图像文件");
            return false;
        }

        var fd = new FormData();
        fd.append("ossfile", file);
        var xhr = new XMLHttpRequest();
        xhr.open("POST", '/Images?c=Updata');

        xhr.send(fd);

        //获取执行状态
        xhr.onreadystatechange = function() {
            //如果执行状态成功，那么就把返回信息写到指定的层里
            if (xhr.readyState == 4) {
                if (xhr.status == 200) {
                    eval("var bakjson = " + xhr.responseText + ";");
                    if (bakjson.error == '1') {
                        dangerFromTip(bakjson.errortip);
                    } else {
                        var Filehtml = '<li><img src="'+bakjson.originalimg+'" target="_blank">'
                            +'<i class="glyphicon glyphicon-remove delThis"></i>'
                            +'<input name="affix_img[]" type="hidden" value="'+bakjson.originalimg+'">'
                            +'<input name="affix_thumburl[]" type="hidden" value="'+bakjson.thumbnailimg+'">'
                            +'<input name="affix_imgname[]" type="text" value="'+bakjson.imgname+'" style="width:100%"></li>';
                        $("#" + imgelement).find(".FileList").append(Filehtml);
                    }
                }
            }
        }
    });

    $("#trainhour_class").change(function(){
        var v = $("#trainhour_class").val();
        if (v == '0') {
            $('#Trainhour-Fileurl').show();
            $('#trainhourFileurl').hide();
            $('#list_file_views').hide();
        } else if (v == '1') {
            $('#Trainhour-Fileurl').hide();
            $('#trainhourFileurl').show();
            $('#list_file_views').hide();
        } else {
            $('#Trainhour-Fileurl').hide();
            $('#trainhourFileurl').hide();
            $('#list_file_views').show();
        }
    });
</script>
{/literal}

