<div class="content">
    <div class="bg-f">
        <h2 class="p20">
            <span class="fr">
                <a href="/{$u}/Add?site_id={$websites.site_id}" class="btn btn-success dropdown-toggle btn-demo-space">+新增资料</a>
                <button type="button" class="btn btn-primary ml10 bakFromurl">返回</button>
            </span>{$moduleOne.module_name}</h2>
        <div class="p20 f14">
            <div class="form-group">
                <form action="/{$u}" method="get" accept-charset="utf-8">
                    <input name="site_id" value="{$websites.site_id}" type="hidden">
                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tr>
                            <td width="4%">课程类型：</td>
                            <td width="15%">
                                <select name="course_type" class="form-control">
                                    <option value="">选择课程类型</option>
                                    <option value="0" {if $datatype.course_type eq '0'}selected{/if}>公开课</option>
                                    <option value="1" {if $datatype.course_type eq '1'}selected{/if}>职业课</option>
                                </select>
                            </td>
                            <td width="4%">搜索：</td>
                            <td width="15%"><input class="form-control input-sm" id="inputEmail3" placeholder="课程名称" value="{$datatype['keyword']}" type="text" name="keyword"></td>
                            <td width="63%"><button type="submit" class="btn btn-primary ml10"><span class="glyphicon glyphicon-search c-f"></span> 搜索</button> </td>
                        </tr>
                    </table>
                </form>
            </div>

            <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table tc table-hover table-bordered">
                <thead>
                <tr>
                    <th>课程ID</th>
                    <th>课程类型</th>
                    <th>所属职业</th>
                    <th>所属阶段</th>
                    <th>所属类型</th>
                    <th>课程名称</th>
                    <th>课程图片</th>
                    <th>是否推荐</th>
                    <th>是否热门</th>
                    <th>更新时间</th>
                    <th>创建时间</th>
                    <th>操作</th>
                </tr>
                </thead>
                <tbody>
                {if $dataList}
                {foreach from=$dataList item=dataVar}
                <tr id="list-{$dataVar.course_id}">
                    <td>{$dataVar.course_id}</td>
                    <td>
                        {if $dataVar.course_type eq '0'}公开课
                        {elseif $dataVar.course_type eq '1'}职业课
                        {/if}
                    </td>
                    <td>{$dataVar.career_cnname}</td>
                    <td>{$dataVar.stage_cnname}</td>
                    <td>{$dataVar.openclasstype_cnname}</td>
                    <td>{$dataVar.course_name}</td>
                    <td>{if $dataVar.course_img != ''}<a data-imgurl="{$dataVar.course_img}" class="cp Opon-Img-View showico" title="查看效果"></a>{/if}</td>
                    <td>
                        {if $dataVar.course_recommend eq 0}
                        <span class="glyphicon glyphicon-remove-circle f18 text-danger"></span>
                        {elseif $dataVar.course_recommend eq 1}
                        <span class="glyphicon glyphicon-ok-circle f18 text-primary"></span>
                        {/if}
                    </td>
                    <td>
                        {if $dataVar.course_popular eq 0}
                        <span class="glyphicon glyphicon-remove-circle f18 text-danger"></span>
                        {elseif $dataVar.course_popular eq 1}
                        <span class="glyphicon glyphicon-ok-circle f18 text-primary"></span>
                        {/if}
                    </td>
                    <td>{$dataVar.course_updatatime|date_format:'%Y-%m-%d %H:%M:%S'}</td>
                    <td>{$dataVar.course_createtime|date_format:'%Y-%m-%d %H:%M:%S'}</td>
                    <td>
                        {if $dataVar.course_type eq '1'}
                        <a href="/{$u}/SetCareer?course_id={$dataVar.course_id}&site_id={$websites.site_id}&company_id={$dataVar.company_id}" class="btn btn-warning btn-sm"><span class="glyphicon  c-f"></span> 适配职务</a>
                        {/if}
                        <a href="/{$u}/Chapter?site_id={$websites.site_id}&course_id={$dataVar.course_id}" class="btn btn-primary btn-sm"><span class="glyphicon glyphicon-certificate c-f"></span> 课程章节明细管理</a>
                        <a href="/{$u}/Edit?course_id={$dataVar.course_id}&site_id={$websites.site_id}&company_id={$dataVar.company_id}" class="btn btn-primary btn-sm"><span class="glyphicon glyphicon-pencil c-f"></span> 编辑</a>
                        <a  href="javascript:;" data-element="list-{$dataVar.course_id}" data-url="/{$u}?c=Del&id={$dataVar.course_id}&site_id={$websites.site_id}" class="btn btn-danger btn-sm btn-del-action"><span class="glyphicon glyphicon-remove c-f"></span> 删除</a>
                    </td>
                </tr>
                {/foreach}
                {/if}
                </tbody>
            </table>
            <div class="pagemenu">{$pagelist}</div>
        </div>
    </div>
</div>
