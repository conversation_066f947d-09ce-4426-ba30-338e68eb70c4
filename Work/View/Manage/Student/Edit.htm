
<div class="content">
    <div id="container">
        <div class="bg-f row py20">
            <form action="/{$u}?c={$act}" method="post" enctype="application/x-www-form-urlencoded" class="AjaxForm">
                <input name="school_id" type="hidden" value="{$datatype.school_id}">
                <input name="company_id" type="hidden" value="{$datatype.company_id}">
                <input name="site_id" type="hidden" value="{$websites.site_id}">
                <input name="student_id" type="hidden" value="{$dataVar.student_id}">
                <h2 class="p20">{$moduleOne.module_name}<span class="fr">
                <button type="submit" class=" btn btn-success btn-cons"><i class="fa fa-check"></i>提交保存</button>
                <button type="button" class="btn btn-primary ml10 bakFromurl">返回</button></span>
                </h2>
                <div class="col-md-8" id="Form-Box-Operating">
                    <div class="pl20 py20 f14">
                        <div class="tab-content">
                            <div class="form-group col-md-4">
                                <label for="school_cnname">所属学校</label>
                                <input name="school_cnname" id="school_cnname" value="{$schoolOne.school_cnname}" reg="[^ \f\n\r\t\v]" tip="" type="text" class="form-control" placeholder="校区名称" disabled>
                            </div>
                            <div class="form-group col-md-4">
                                <label for="student_branch">学员编号</label>
                                <input name="student_branch" id="student_branch" value="{$dataVar.student_branch}" reg="[^ \f\n\r\t\v]" tip="自动生成学员编号" type="text" class="form-control" placeholder="自动生成学员编号" disabled>
                            </div>
                            <div class="form-group col-md-4">
                                <label for="student_cnname">学员姓名</label>
                                <input name="student_cnname" id="student_cnname" value="{$dataVar.student_cnname}" reg="[^ \f\n\r\t\v]" tip="请输入学员姓名" type="text" class="form-control" placeholder="请输入学员姓名">
                            </div>
                            <div class="form-group col-md-4">
                                <label for="student_enname">学员英文名</label>
                                <input name="student_enname" id="student_enname" value="{$dataVar.student_enname}" reg="[^ \f\n\r\t\v]" tip="请输入学员英文名" type="text" class="form-control" placeholder="请输入学员英文名">
                            </div>
                            <div class="form-group col-md-4">
                                <label for="student_birthday">生日</label>
                                <input name="student_birthday" id="student_birthday" value="{$dataVar.student_birthday}" reg="[^ \f\n\r\t\v]" tip="生日" type="text" class="form-control form_datetime" placeholder="生日">
                            </div>

                            <div class="form-group col-md-4">
                                <label for="student_sex">性别</label>
                                <br>
                                <select name="student_sex" id="student_sex" class="form-control">
                                    <option value="男">男</option>
                                    <option value="女">女</option>
                                </select>
                            </div>

                            <div class="form-group col-md-4">
                                <input name="family_id" id="family_id" value="{$dataVar.family_id}" type="hidden"    >
                                <label for="family_mobile">联系号码</label>
                                <input name="family_mobile" id="family_mobile" value="{$dataVar.family_mobile}" type="text" class="form-control" placeholder="请输入联系号码"  required="required">
                            </div>

                            <div class="form-group col-md-4">
                                <label for="family_cnname">联系家长姓名</label>
                                <input name="family_cnname" id="family_cnname" value="{$dataVar.family_cnname}" type="text" class="form-control" placeholder="请输入身联系家长姓名">
                            </div>

                            <div class="form-group col-md-4">
                                <label for="family_relation">亲属关系</label>
                                <br>
                                <select name="family_relation" id="family_relation" >
                                <option value="">亲属关系</option>
                                {if $relation}
                                {foreach from=$relation item=relationVar}
                                <option value="{$relationVar.familyrelation_name}" {if $relationVar.familyrelation_name eq $datatype.familyrelation_name}selected{/if}>{$relationVar.familyrelation_name}</option>
                                {/foreach}
                                {/if}
                                </select>
                            </div>
                            <div class="form-group col-md-10">
                                <label for="student_idcard">身份证号码</label>
                                <input name="student_idcard" id="student_idcard" value="{$dataVar.student_idcard}" type="text" class="form-control" placeholder="请输入身份证号码址">
                            </div>
                            <!--<div class="form-group col-md-4">-->
                            <!--<label for="company_phone">客服电话</label>-->
                            <!--<input name="company_phone" id="company_phone" value="{$dataVar.company_phone}" reg="[^ \f\n\r\t\v]" tip="请填写客服电话" type="text" class="form-control" placeholder="请输入客服电话">-->
                            <!--</div>-->
                            <!--<div class="form-group col-md-4">-->
                            <!--<label for="company_fax">客服传真</label>-->
                            <!--<input name="company_fax" id="company_fax" value="{$dataVar.company_fax}" reg="[^ \f\n\r\t\v]" tip="请填写客服传真" type="text" class="form-control" placeholder="请输入客服传真">-->
                            <!--</div>-->
                            <!--<div class="form-group col-md-4">-->
                            <!--<label for="company_email">联系邮箱</label>-->
                            <!--<input name="company_email" id="company_email" value="{$dataVar.company_email}" type="text" class="form-control" placeholder="请输入联系邮箱">-->
                            <!--</div>-->
                            <!--<div class="form-group col-md-4">-->
                            <!--<label for="company_homeurl">企业官网</label>-->
                            <!--<input name="company_homeurl" id="company_homeurl" value="{$dataVar.company_homeurl}" type="text" class="form-control" placeholder="请输入企业官网">-->
                            <!--</div>-->
                            <!--<div class="form-group">-->
                            <!--<div class="col-md-8  mb10">-->
                            <!--<label for="company_logo">登录Logo (500px*300px)</label>-->
                            <!--<input name="company_logo" id="company_logo" value="{$dataVar.company_logo}" type="text" class="form-control" placeholder="先点击右侧按钮上传图片，或直接书写图片网址">-->
                            <!--</div>-->
                            <!--<div class="col-md-4">-->
                            <!--<label for="up_img">&nbsp;</label>-->
                            <!--<a data-color="rgb(255, 255, 255)" data-color-format="hex" id="cp4" class="btn btn-primary my-colorpicker-control upload-btn" data-colorpicker-guid="1">-->
                            <!--<span class="glyphicon glyphicon-floppy-open"></span>-->
                            <!--选择图片上传<input type="file" id="up_img" class="ipt-img-click" data-element="list_img_view" data-originalipt="company_logo" data-thumbnailipt="tb_imgthum"></a>-->
                            <!--</div>-->
                            <!--</div>-->
                            <div class="clear"></div>
                        </div>
                    </div>
                </div>
                <!--<div class="col-md-4">-->
                <!--<div class="upload-img col-md-6" id="list_img_view">-->
                <!--<div class="f16 tc default {if $dataVar.company_logo}none{/if}">-->
                <!--<p class="f30"><span class="glyphicon glyphicon-picture"></span></p>-->
                <!--<p>请先上传图片后预览</p>-->
                <!--</div>-->
                <!--<div class="img set {if !$dataVar.company_logo}none{/if}"><img src="{$dataVar.company_logo}"></div>-->
                <!--</div>-->
                <!--</div>-->
            </form>
        </div>
    </div>
</div>