<div class="content">
    <div class="bg-f">
        <h2 class="p20">{$moduleOne.module_name}
            <span class="fr">
                <a href="/{$u}/Add?school_id={$datatype.school_id}&site_id={$websites.site_id}" class="btn btn-success dropdown-toggle btn-demo-space">+新增数据</a>
                <a href="/{$u}/Import?school_id={$datatype.school_id}&site_id={$websites.site_id}" class="btn btn-success dropdown-toggle btn-demo-space">+导入学员数据</a>
                <a class="btn btn-primary bakFromurl ml10"><span class="glyphicon glyphicon-share-alt c-f"></span> 返回</a>
            </span>
        </h2>
        <div class="p20 f14">
            <div class="form-group">
                <form action="{$moduleOne.module_link}/Student" method="get" accept-charset="utf-8">
                    <input name="site_id" value="{$websites.site_id}" type="hidden">
                    <input name="company_id" value="{$datatype.company_id}" type="hidden">
                    <input name="from" value="{$datatype.from}" type="hidden">
                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tr>
                            <td width="4%">搜索：</td>
                            <td width="15%">
                                <input name="keyword" class="form-control input-sm" placeholder="请输入校区编号/校区名称" value="{$datatype['keyword']}" type="text">
                            </td>
                            <td width="63%"><button type="submit" class="btn btn-primary ml10"><span class="glyphicon glyphicon-search c-f"></span> 搜索</button> </td>
                        </tr>
                    </table>
                </form>
            </div>
            <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table tc table-hover table-bordered">
                <thead>
                <tr>
                    <th>ID</th>
                    <th>学员姓名</th>
                    <th>学员英文名</th>
                    <th>学员编号</th>
                    <th>性别</th>
                    <th>出生日期</th>
                    <!--<th>联系人</th>-->
                    <!--<th>联系电话</th>-->
                    <th>证件号码</th>
                    <th>创建时间</th>
                    <th>操作</th>
                </tr>
                </thead>
                <tbody>
                {if $dataList}
                {foreach from=$dataList item=dataVar}
                <tr id="list-{$dataVar.student_id}">
                    <td>{$dataVar.student_id}</td>
                    <td>{$dataVar.student_cnname}</td>
                    <td>{$dataVar.student_enname}</td>
                    <td>{$dataVar.student_branch}</td>
                    <td>{$dataVar.student_sex}</td>
                    <td>{$dataVar.student_birthday}</td>
                    <td>{$dataVar.student_idcard}</td>
                    <!--<td>{$dataVar.student_idcard}</td>-->
                    <!--<td>{$dataVar.student_idcard}</td>-->
                    <td>{$dataVar.student_createtime|date_format:'%Y-%m-%d'}</td>
                    <td align="left">
                        <a href="/{$u}/Edit?student_id={$dataVar.student_id}&school_id={$datatype.school_id}&site_id={$websites.site_id}" class="btn btn-primary btn-sm">
                            <span class="glyphicon glyphicon-pencil c-f"></span> 编辑</a>
                        <!--<a href="/{$u}/Staffer?company_id={$dataVar.company_id}&site_id={$websites.site_id}" class="btn btn-primary btn-sm">-->
                        <!--<span class="glyphicon glyphicon-certificate c-f"></span> 职工明细</a>-->
                        <!--<a href="/{$u}/Worklog?company_id={$dataVar.company_id}&site_id={$websites.site_id}" class="btn btn-primary btn-sm">-->
                        <!--<span class="glyphicon glyphicon-phone c-f"></span> 职工日志</a>-->
                        <!--<a href="/Contract?company_id={$dataVar.company_id}&site_id={$websites.site_id}&from=company" class="btn btn-primary btn-sm">-->
                        <!--<span class="glyphicon glyphicon-phone c-f"></span> 合同管理</a>-->
                        <!--<a href="/SchoolList?company_id={$dataVar.company_id}&site_id={$websites.site_id}&from=company" class="btn btn-primary btn-sm">-->
                            <!--<span class="glyphicon  c-f"></span> 导入学员</a>-->
                        <!--<a href="/SchoolList?company_id={$dataVar.company_id}&site_id={$websites.site_id}&from=company" class="btn btn-primary btn-sm">-->
                            <!--<span class="glyphicon  c-f"></span> 导入职工</a>-->
                    </td>
                </tr>
                {/foreach}
                {/if}
                </tbody>
            </table>
            <div class="pagemenu">{$pagelist}</div>
        </div>
    </div>
</div>