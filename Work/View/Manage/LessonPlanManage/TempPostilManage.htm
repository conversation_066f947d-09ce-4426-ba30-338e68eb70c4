<div class="content">
    <div id="container">
        <div class="bg-f row py20">
            <form action="/{$u}?c={$act}" method="post" enctype="application/x-www-form-urlencoded" class="AjaxForm">
                <input name="temppostil_id" type="hidden" value="{$dataVar.temppostil_id}">
                <input name="site_id" type="hidden" value="{$websites.site_id}">
                <input name="classcode_branch" type="hidden" value="{$classcode_branch}">
                <input name="teachhour_branch" type="hidden" value="{$teachhour_branch}">
                <h2 class="p20">优质批注管理<span class="fr">
                <button type="submit" class=" btn btn-success btn-cons"><i class="fa fa-check"></i>提交保存</button>
                <button type="button" class="btn btn-primary ml10 bakFromurl">返回</button></span>
                </h2>
                <div class="col-md-6" id="Form-Box-Operating">
                    <div class="pl20 f14">
                        <div class="form-group col-md-5">
                            <label for="temppostil_tilte">批注名称</label>
                            <input name="temppostil_tilte" id="temppostil_tilte" value="{$dataVar['temppostil_tilte']}" type="text" class="form-control" placeholder="请输入批注名称">
                        </div>
                        <div class="form-group col-md-5">
                            <label for="temppostil_author">批注作者</label>
                            <input name="temppostil_author" id="temppostil_author" value="{$dataVar['temppostil_author']}" type="text" class="form-control" placeholder="请输入批注作者">
                        </div>
                        <div class="form-group col-md-5">
                            <label for="temppostil_isPopular">是否热门批注</label>
                            <select name="temppostil_isPopular" class="form-control" id="temppostil_isPopular">
                                <option value="0" {if $dataVar.temppostil_isPopular eq '0'}selected{/if}>否</option>
                                <option value="1" {if $dataVar.temppostil_isPopular eq '1'}selected{/if}>是</option>
                            </select>
                        </div>
                        <div class="form-group col-md-5">
                            <label for="temppostil_isPerfect">是否优质批注</label>
                            <select name="temppostil_isPerfect" class="form-control" id="temppostil_isPerfect">
                                <option value="0" {if $dataVar.temppostil_isPerfect eq '0'}selected{/if}>否</option>
                                <option value="1" {if $dataVar.temppostil_isPerfect eq '1'}selected{/if}>是</option>
                            </select>
                        </div>
                        <div class="form-group col-md-10">
                            <label for="temppostil_details">中心批注</label>
                            <textarea name="temppostil_details" id="temppostil_details" class="form-control" placeholder="请输入中心批注" rows="3">{$dataVar.temppostil_details}</textarea>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
