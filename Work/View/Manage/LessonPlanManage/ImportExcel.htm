<div class="content">
    <div class="bg-f">
        <h2 class="p20"><span class="fr"><button type="button" class="btn btn-primary ml10 bakFromurlTwo">返回</button></span>导入商品信息</h2>
        <div class="p20 f14">
            {if $PlayInfo}
            <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table tc table-hover table-bordered">
                <thead>
                <tr>
                    <th>教案名称</th>
                    <th>中心批注</th>
                    <th>教案注意事项</th>
                    <th>教案封面</th>
                    <th>视频地址</th>
                    <th>图片名称</th>
                    <th>图片链接</th>
                    <th>反馈信息</th>
                </tr>
                </thead>
                <tbody>
                {foreach from=$PlayInfo item=dataVar}
                <tr>
                    <td>{$dataVar.teachplan_name}</td>
                    <td>{$dataVar.teachplan_postil}</td>
                    <td>{$dataVar.teachplan_matters}</td>
                    <td>{if $dataVar.teachplan_coverimg != ''}<a data-imgurl="{$dataVar.teachplan_coverimg}" class="cp Opon-Img-View showico" title="查看效果"></a>{/if}</td>
                    <td>{$dataVar.teachplan_videourl}</td>
                    <td>{$dataVar.teachplan_imgname}</td>
                    <td>{if $dataVar.teachplan_img != ''}<a data-imgurl="{$dataVar.teachplan_img}" class="cp Opon-Img-View showico" title="查看效果"></a>{/if}</td>
                    <td>
                        {$dataVar.errortip}
                    </td>
                </tr>
                {/foreach}
                </tbody>
            </table>
            {/if}
        </div>
    </div>
</div>
<script src="{$JsUrl}jquery.min.js" type="text/javascript"></script>
<script>
    $(document).ready(function() {
        $(".bakFromurlTwo").click(function() {
            window.history.go(-2);
        });
    });
</script>