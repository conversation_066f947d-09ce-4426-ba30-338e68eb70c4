<div class="content">
    <div class="bg-f">
        <h2 class="p20">
            <span class="fr">
                <a href="/{$u}/CourseMaterialsAdd?site_id={$websites.site_id}&classcode_branch={$classcode_branch}&teachhour_branch={$teachhour_branch}" class="btn btn-success dropdown-toggle btn-demo-space">+新增资料</a>
                <button type="button" class="btn btn-primary ml10 bakFromurl">返回</button>
            </span>课程资料管理</h2>
        <div class="p20 f14">
            <div class="form-group">
                <form action="/{$u}/CourseMaterials" method="get" accept-charset="utf-8">
                    <input name="site_id" value="{$websites.site_id}" type="hidden">
                    <input name="classcode_branch" value="{$classcode_branch}" type="hidden">
                    <input name="teachhour_branch" value="{$teachhour_branch}" type="hidden">
                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tr>
                            <td width="4%">教案模式：</td>
                            <td width="15%">
                                <select name="tempfiles_class" class="form-control">
                                    <option value="">选择教案模式</option>
                                    <option value="0" {if $datatype.tempfiles_class eq '0'}selected{/if}>OFFICE档案</option>
                                    <option value="1" {if $datatype.tempfiles_class eq '1'}selected{/if}>影音档案</option>
                                    <option value="2" {if $datatype.tempfiles_class eq '2'}selected{/if}>图档</option>
                                </select>
                            </td>
                            <td width="2%"></td>
                            <td width="15%"><input class="form-control input-sm" id="inputEmail3" placeholder="文件名称" value="{$datatype['keyword']}" type="text" name="keyword"></td>
                            <td width="63%"><button type="submit" class="btn btn-primary ml10"><span class="glyphicon glyphicon-search c-f"></span> 搜索</button> </td>
                        </tr>
                    </table>
                </form>
            </div>

            <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table tc table-hover table-bordered">
                <thead>
                <tr>
                    <th>资料ID</th>
                    <th>班别编号</th>
                    <th>课时编号</th>
                    <th>教案模式</th>
                    <th>文件名称</th>
                    <th>文件地址</th>
                    <th>创建时间</th>
                    <th>操作</th>
                </tr>
                </thead>
                <tbody>
                {if $dataList}
                {foreach from=$dataList item=dataVar}
                <tr id="list-{$dataVar.tempfiles_id}">
                    <td>{$dataVar.tempfiles_id}</td>
                    <td>{$dataVar.classcode_branch}</td>
                    <td>{$dataVar.teachhour_branch}</td>
                    <td>
                        {if $dataVar.tempfiles_class eq '0'}OFFICE档案
                        {elseif $dataVar.tempfiles_class eq '1'}影音档案
                        {elseif $dataVar.tempfiles_class eq '2'}图档
                        {/if}
                    </td>
                    <td>{$dataVar.tempfiles_name}</td>
                    <td>{$dataVar.tempfiles_url}</td>
                    <td>{$dataVar.tempfiles_createtime|date_format:'%Y-%m-%d %H:%M:%S'}</td>
                    <td>
                        <a href="/{$u}/CourseMaterialsEdit?tempfiles_id={$dataVar.tempfiles_id}&site_id={$websites.site_id}&classcode_branch={$dataVar.classcode_branch}&teachhour_branch={$dataVar.teachhour_branch}" class="btn btn-primary btn-sm"><span class="glyphicon glyphicon-pencil c-f"></span> 编辑</a>
                        <a  href="javascript:;" data-element="list-{$dataVar.tempfiles_id}" data-url="/{$u}?c=CourseMaterialsDel&id={$dataVar.tempfiles_id}&classcode_branch={$dataVar.classcode_branch}&teachhour_branch={$dataVar.teachhour_branch}" class="btn btn-danger btn-sm btn-del-action"><span class="glyphicon glyphicon-remove c-f"></span> 删除</a>
                    </td>
                </tr>
                {/foreach}
                {/if}
                </tbody>
            </table>
            <div class="pagemenu">{$pagelist}</div>
        </div>
    </div>
</div>
