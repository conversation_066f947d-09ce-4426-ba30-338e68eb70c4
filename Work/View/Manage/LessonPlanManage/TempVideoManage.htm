<div class="content">
    <div id="container">
        <div class="bg-f row py20">
            <form action="/{$u}?c={$act}" method="post" enctype="application/x-www-form-urlencoded" class="AjaxForm">
                <input name="tempvideo_id" type="hidden" value="{$dataVar.tempvideo_id}">
                <input name="site_id" type="hidden" value="{$websites.site_id}">
                <input name="classcode_branch" type="hidden" value="{$classcode_branch}">
                <input name="teachhour_branch" type="hidden" value="{$teachhour_branch}">
                <h2 class="p20">优质视频管理<span class="fr">
                <button type="submit" class=" btn btn-success btn-cons"><i class="fa fa-check"></i>提交保存</button>
                <button type="button" class="btn btn-primary ml10 bakFromurl">返回</button></span>
                </h2>
                <div class="col-md-6" id="Form-Box-Operating">
                    <div class="pl20 f14">
                        <div class="form-group col-md-5">
                            <label for="tempvideo_name">视频名称</label>
                            <input name="tempvideo_name" id="tempvideo_name" value="{$dataVar['tempvideo_name']}" type="text" class="form-control" placeholder="请输入视频名称">
                        </div>
                        <div class="form-group col-md-5">
                            <label for="tempvideo_author">视频作者</label>
                            <input name="tempvideo_author" id="tempvideo_author" value="{$dataVar['tempvideo_author']}" type="text" class="form-control" placeholder="请输入视频作者">
                        </div>
                        <div class="form-group col-md-5">
                            <label for="tempvideo_isPerfect">是否优质视频</label>
                            <select name="tempvideo_isPerfect" class="form-control" id="tempvideo_isPerfect">
                                <option value="0" {if $dataVar.tempvideo_isPerfect eq '0'}selected{/if}>否</option>
                                <option value="1" {if $dataVar.tempvideo_isPerfect eq '1'}selected{/if}>是</option>
                            </select>
                        </div>
                        <div class="form-group col-md-12">
                            <label for="tempvideo_details">教学中心批注</label>
                            <textarea name="tempvideo_details" id="tempvideo_details" class="form-control" placeholder="请输入教学中心批注" rows="3">{$dataVar.tempvideo_details}</textarea>
                        </div>
                        <div class="form-group">
                            <div class="col-md-9  mb10">
                                <label for="tempvideo_videourl">视频地址</label>
                                <input name="tempvideo_videourl" id="tempvideo_videourl" value="{$dataVar.tempvideo_videourl}" type="text" class="form-control" placeholder="先点击右侧按钮上传文件，或直接输入视频地址">
                            </div>
                            <div class="col-md-3">
                                <label>&nbsp;</label>
                                <a data-color="rgb(255, 255, 255)" class="btn btn-primary my-colorpicker-control upload-btn" data-colorpicker-guid="1">
                                    <span class="glyphicon glyphicon-export"></span>
                                    选择视频上传<input type="file" class="oss-file-click" data-originalipt="tempvideo_videourl"></a>
                            </div>
                        </div>
                        <div class="form-group">
                            <div class="col-md-9  mb10">
                                <label for="tempvideo_coverimg">视频封面</label>
                                <input name="tempvideo_coverimg" id="tempvideo_coverimg" value="{$dataVar.tempvideo_coverimg}" type="text" class="form-control" placeholder="先点击右侧按钮上传图片，或直接书写图片网址">
                            </div>
                            <div class="col-md-3">
                                <label for="up_img">&nbsp;</label>
                                <a data-color="rgb(255, 255, 255)" data-color-format="hex" id="cp4" class="btn btn-primary my-colorpicker-control upload-btn" data-colorpicker-guid="1">
                                    <span class="glyphicon glyphicon-floppy-open"></span>
                                    选择图片上传<input type="file" id="up_img" class="ipt-img-click" data-element="list_img_view" data-originalipt="tempvideo_coverimg" data-thumbnailipt="tb_imgthum"></a>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="upload-img" id="list_img_view">
                                <div class="f16 tc default {if $dataVar.tempvideo_coverimg}none{/if}">
                                    <p class="f30"><span class="glyphicon glyphicon-picture"></span></p>
                                    <p>请先上传 教案封面 后预览</p>
                                </div>
                                <div class="img set {if !$dataVar.tempvideo_coverimg}none{/if}"><img src="{$dataVar.tempvideo_coverimg}"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
