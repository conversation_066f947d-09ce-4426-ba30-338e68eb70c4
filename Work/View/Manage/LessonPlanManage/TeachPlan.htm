<div class="content">
    <div class="bg-f">
        <h2 class="p20">
            <span class="fr">
                <a href="/{$u}/Import?site_id={$websites.site_id}&classcode_branch={$classcode_branch}&teachhour_branch={$teachhour_branch}" class="btn btn-success dropdown-toggle btn-demo-space">导入资料</a>
                <a href="/{$u}/TeachPlanAdd?site_id={$websites.site_id}&classcode_branch={$classcode_branch}&teachhour_branch={$teachhour_branch}" class="btn btn-success dropdown-toggle btn-demo-space">+新增教案</a>
                <button type="button" class="btn btn-primary ml10 bakFromurl">返回</button>
            </span>教案明细管理</h2>
        <div class="p20 f14">
            <div class="form-group">
                <form action="/{$u}/TeachPlan" method="get" accept-charset="utf-8">
                    <input name="site_id" value="{$websites.site_id}" type="hidden">
                    <input name="classcode_branch" value="{$classcode_branch}" type="hidden">
                    <input name="teachhour_branch" value="{$teachhour_branch}" type="hidden">
                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tr>
                            <td width="4%">课程类型：</td>
                            <td width="15%">
                                <select name="teachplan_class" class="form-control">
                                    <option value="">选择教案模式</option>
                                    <option value="0" {if $datatype.teachplan_class eq '0'}selected{/if}>文件模式</option>
                                    <option value="1" {if $datatype.teachplan_class eq '1'}selected{/if}>图片模式</option>
                                </select>
                            </td>
                            <td width="2%"></td>
                            <td width="15%"><input class="form-control input-sm" id="inputEmail3" placeholder="教案名称" value="{$datatype['keyword']}" type="text" name="keyword"></td>
                            <td width="63%"><button type="submit" class="btn btn-primary ml10"><span class="glyphicon glyphicon-search c-f"></span> 搜索</button> </td>
                        </tr>
                    </table>
                </form>
            </div>

            <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table tc table-hover table-bordered">
                <thead>
                <tr>
                    <th>ID</th>
                    <th>班别编号</th>
                    <th>课时编号</th>
                    <th>教案名称</th>
                    <th>教案模式</th>
                    <th>教案封面</th>
                    <th>创建时间</th>
                    <th>操作</th>
                </tr>
                </thead>
                <tbody>
                {if $dataList}
                {foreach from=$dataList item=dataVar key=key}
                <tr id="list-{$dataVar.teachplan_id}">
                    <td>{$key+1}</td>
                    <td>{$dataVar.classcode_branch}</td>
                    <td>{$dataVar.teachhour_branch}</td>
                    <td>{$dataVar.teachplan_name}</td>
                    <td>
                        {if $dataVar.teachplan_class eq '0'}文件模式
                        {elseif $dataVar.teachplan_class eq '1'}图片模式
                        {/if}
                    </td>
                    <td>{if $dataVar.teachplan_coverimg != ''}<a data-imgurl="{$dataVar.teachplan_coverimg}" class="cp Opon-Img-View showico" title="查看效果"></a>{/if}</td>
                    <td>{$dataVar.teachplan_createtime|date_format:'%Y-%m-%d %H:%M:%S'}</td>
                    <td>
                        <a href="/{$u}/TeachPlanEdit?teachplan_id={$dataVar.teachplan_id}&site_id={$websites.site_id}&classcode_branch={$dataVar.classcode_branch}&teachhour_branch={$dataVar.teachhour_branch}" class="btn btn-primary btn-sm"><span class="glyphicon glyphicon-pencil c-f"></span> 编辑</a>
                        <a href="javascript:;" data-element="list-{$dataVar.teachplan_id}" data-url="/{$u}?c=TeachPlanDel&id={$dataVar.teachplan_id}&classcode_branch={$dataVar.classcode_branch}&teachhour_branch={$dataVar.teachhour_branch}" class="btn btn-danger btn-sm btn-del-action"><span class="glyphicon glyphicon-remove c-f"></span> 删除</a>
                    </td>
                </tr>
                {/foreach}
                {/if}
                </tbody>
            </table>
            <div class="pagemenu">{$pagelist}</div>
        </div>
    </div>
</div>
