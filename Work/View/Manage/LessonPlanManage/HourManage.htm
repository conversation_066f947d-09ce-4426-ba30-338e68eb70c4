<div class="content">
    <div id="container">
        <div class="bg-f row py20">
            <form action="/{$u}?c={$act}" method="post" enctype="application/x-www-form-urlencoded" class="AjaxForm">
                <input name="tempfiles_id" type="hidden" value="{$dataVar.tempfiles_id}">
                <input name="site_id" type="hidden" value="{$websites.site_id}">
                <input name="classcode_branch" type="hidden" value="{$classcode_branch}">
                <h2 class="p20">班别课时明细管理<span class="fr">
                <button type="submit" class=" btn btn-success btn-cons"><i class="fa fa-check"></i>提交保存</button>
                <button type="button" class="btn btn-primary ml10 bakFromurl">返回</button></span>
                </h2>
                <div class="col-md-6" id="Form-Box-Operating">
                    <div class="pl20 f14">
                        <div class="form-group col-md-4">
                            <label for="teachhour_branch">课时编号</label>
                            <input name="teachhour_branch" id="teachhour_branch" value="{$dataVar['teachhour_branch']}" type="text" class="form-control" placeholder="请输入课时编号">
                        </div>
                        <div class="form-group col-md-4">
                            <label for="teachhour_name">课时名称</label>
                            <input name="teachhour_name" id="teachhour_name" value="{$dataVar['teachhour_name']}" type="text" class="form-control" placeholder="请输入课时名称">
                        </div>
                        <div class="form-group col-md-4">
                            <label for="teachhour_sort">课时排序</label>
                            <input name="teachhour_sort" id="teachhour_sort" value="{$dataVar['teachhour_sort']}" type="text" class="form-control" placeholder="请输入课时排序">
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
