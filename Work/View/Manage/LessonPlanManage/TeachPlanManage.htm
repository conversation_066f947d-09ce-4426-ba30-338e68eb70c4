<div class="content">
    <div id="container">
        <div class="bg-f row py20">
            <form action="/{$u}?c={$act}" method="post" enctype="application/x-www-form-urlencoded" class="AjaxForm">
                <input name="teachplan_id" type="hidden" value="{$dataVar.teachplan_id}">
                <input name="site_id" type="hidden" value="{$websites.site_id}">
                <input name="classcode_branch" type="hidden" value="{$classcode_branch}">
                <input name="teachhour_branch" type="hidden" value="{$teachhour_branch}">
                <h2 class="p20">教案明细管理<span class="fr">
                <button type="submit" class=" btn btn-success btn-cons"><i class="fa fa-check"></i>提交保存</button>
                <button type="button" class="btn btn-primary ml10 bakFromurl">返回</button></span>
                </h2>
                <div class="col-md-6" id="Form-Box-Operating">
                    <div class="pl20 f14">
                        <div class="form-group col-md-4">
                            <label for="teachplan_name">教案名称</label>
                            <input name="teachplan_name" id="teachplan_name" value="{$dataVar['teachplan_name']}" type="text" class="form-control" placeholder="请输入教案名称">
                        </div>
                        <div class="form-group">
                            <div class="col-md-9  mb10">
                                <label for="teachplan_coverimg">教案封面</label>
                                <input name="teachplan_coverimg" id="teachplan_coverimg" value="{$dataVar.teachplan_coverimg}" type="text" class="form-control" placeholder="先点击右侧按钮上传图片，或直接书写图片网址">
                            </div>
                            <div class="col-md-3">
                                <label for="up_img">&nbsp;</label>
                                <a data-color="rgb(255, 255, 255)" data-color-format="hex" id="cp4" class="btn btn-primary my-colorpicker-control upload-btn" data-colorpicker-guid="1">
                                    <span class="glyphicon glyphicon-floppy-open"></span>
                                    选择图片上传<input type="file" id="up_img" class="ipt-img-click" data-element="list_img_view" data-originalipt="teachplan_coverimg" data-thumbnailipt="tb_imgthum"></a>
                            </div>
                        </div>
                        <div class="form-group">
                            <div class="col-md-9  mb10">
                                <label for="teachplan_videourl">视频地址</label>
                                <input name="teachplan_videourl" id="teachplan_videourl" value="{$dataVar.teachplan_videourl}" type="text" class="form-control" placeholder="先点击右侧按钮上传文件，或直接输入视频地址">
                            </div>
                            <div class="col-md-3">
                                <label>&nbsp;</label>
                                <a data-color="rgb(255, 255, 255)" class="btn btn-primary my-colorpicker-control upload-btn" data-colorpicker-guid="1">
                                    <span class="glyphicon glyphicon-export"></span>
                                    选择视频上传<input type="file" class="oss-file-click" data-originalipt="teachplan_videourl"></a>
                            </div>
                        </div>
                        <div class="form-group col-md-12">
                            <label for="teachplan_postil">中心批注</label>
                            <textarea name="teachplan_postil" id="teachplan_postil" class="form-control" placeholder="请输入中心批注" rows="3">{$dataVar.teachplan_postil}</textarea>
                        </div>
                        <div class="form-group col-md-12">
                            <label for="teachplan_matters">教案注意事项</label>
                            <textarea name="teachplan_matters" id="teachplan_matters" class="form-control" placeholder="请输入教案注意事项" rows="4">{$dataVar.teachplan_matters}</textarea>
                        </div>
                        <div class="form-group col-md-9  mb10">
                            <label for="teachplan_fileurl">文件地址</label>
                            <input name="teachplan_fileurl" id="teachplan_fileurl" value="{$dataVar.teachplan_fileurl}" type="text" class="form-control" placeholder="先点击右侧按钮上传文件，或直接输入文件地址">
                        </div>
                        <div class="col-md-3">
                            <label>&nbsp;</label>
                            <a data-color="rgb(255, 255, 255)" class="btn btn-primary my-colorpicker-control upload-btn" data-colorpicker-guid="1">
                                <span class="glyphicon glyphicon-export"></span>
                                选择资料上传<input type="file" class="oss-file-click" data-originalipt="teachplan_fileurl"></a>
                        </div>
                        <div class="form-group col-md-5">
                            <label for="teachplan_class">教案模式</label>
                            <select name="teachplan_class" class="form-control" id="teachplan_class">
                                <option value="1">图片模式</option>
                            </select>
                        </div>
                        <div role="tabpanel" class="tab-pane">
                            <div class="form-group">
                                <div class="col-md-12">
                                    <div class="upload-img" id="list_file_views">
                                        <div class="f16 tf default">
                                            <div style="width: 100%;">
                                                <ul class="FileList fix">
                                                    {if $imglist}
                                                    {foreach from=$imglist item=imgvar}
                                                    <li><a href="{$imgvar.teachpics_url}" target="_blank"><img src="{$imgvar.teachpics_url}"></a>
                                                        <i class="glyphicon glyphicon-remove delThis"></i>
                                                        <input name="affix_img[]" type="hidden" value="{$imgvar.teachpics_url}">
                                                        <input name="affix_imgname[]" type="text" value="{$imgvar.teachpics_name}" style="width:100%">
                                                    </li>
                                                    {/foreach}
                                                    {/if}
                                                </ul>
                                            </div>
                                            <div class="upload-btn">
                                                <p class="f30"><span class="glyphicon glyphicon-file"></span></p>
                                                <p>请点击上传照片<input type="file" class="ipt-imgs-click" data-element="list_file_views"></p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="upload-img" id="list_img_view">
                                <div class="f16 tc default {if $dataVar.teachplan_coverimg}none{/if}">
                                    <p class="f30"><span class="glyphicon glyphicon-picture"></span></p>
                                    <p>请先上传 教案封面 后预览</p>
                                </div>
                                <div class="img set {if !$dataVar.teachplan_coverimg}none{/if}"><img src="{$dataVar.teachplan_coverimg}"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
<script src="{$JsUrl}jquery.min.js" type="text/javascript"></script>
{literal}
<script>
    //上传图片插件
    $(document).on("change", ".ipt-imgs-click",function() {
        $(".loader-inner").removeClass('none');
        var imgelement = $(this).data("element"),imgoriginalipt = $(this).data("originalipt"),imgthumbnailipt = $(this).data("thumbnailipt");
        var file = this.files[0];
        if (!file || !file.type.match(/image.*/)) {
            warningFromTip("只可以上传图像文件");
            return false;
        }

        var fd = new FormData();
        fd.append("ossfile", file);
        var xhr = new XMLHttpRequest();
        xhr.open("POST", '/Images?c=Updata');

        xhr.send(fd);

        //获取执行状态
        xhr.onreadystatechange = function() {
            //如果执行状态成功，那么就把返回信息写到指定的层里
            if (xhr.readyState == 4) {
                if (xhr.status == 200) {
                    eval("var bakjson = " + xhr.responseText + ";");
                    if (bakjson.error == '1') {
                        dangerFromTip(bakjson.errortip);
                    } else {
                        var Filehtml = '<li><img src="'+bakjson.originalimg+'" target="_blank">'
                            +'<i class="glyphicon glyphicon-remove delThis"></i>'
                            +'<input name="affix_img[]" type="hidden" value="'+bakjson.originalimg+'">'
                            +'<input name="affix_imgname[]" type="text" value="'+bakjson.imgname+'" style="width:100%"></li>';
                        $("#" + imgelement).find(".FileList").append(Filehtml);
                    }
                }
            }
        }
    });
</script>
{/literal}
