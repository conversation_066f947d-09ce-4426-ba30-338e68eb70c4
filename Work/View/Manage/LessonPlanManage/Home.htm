<div class="content">
    <div class="bg-f">
        <h2 class="p20">
            <span class="fr">
                <a href="/{$u}/Add?site_id={$websites.site_id}" class="btn btn-success dropdown-toggle btn-demo-space">+新增班别</a>
                <button type="button" class="btn btn-primary ml10 bakFromurl">返回</button>
            </span>{$moduleOne.module_name}</h2>
        <div class="p20 f14">
            <div class="form-group">
                <form action="/{$u}" method="get" accept-charset="utf-8">
                    <input name="site_id" value="{$websites.site_id}" type="hidden">
                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tr>
                            <td width="15%"><input class="form-control input-sm" id="inputEmail3" placeholder="班别名称/班别编号" value="{$datatype['keyword']}" type="text" name="keyword"></td>
                            <td width="63%"><button type="submit" class="btn btn-primary ml10"><span class="glyphicon glyphicon-search c-f"></span> 搜索</button> </td>
                        </tr>
                    </table>
                </form>
            </div>

            <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table tc table-hover table-bordered">
                <thead>
                <tr>
                    <th>班种编号</th>
                    <th>班别编号</th>
                    <th>班别名称</th>
                    <th>班别课时数量</th>
                    <th>是否开启个性化教案</th>
                    <th>是否开启备课</th>
                    <th>操作</th>
                </tr>
                </thead>
                <tbody>
                {if $dataList}
                {foreach from=$dataList item=dataVar}
                <tr>
                    <td>{$dataVar.catcode_branch}</td>
                    <td>{$dataVar.classcode_branch}</td>
                    <td>{$dataVar.classcode_name}</td>
                    <td>{$dataVar.classcode_hour}</td>
                    <td>
                        {if $dataVar.classcode_isopen eq 0}
                        <span class="glyphicon glyphicon-remove-circle f18 text-danger"></span>
                        {elseif $dataVar.classcode_isopen eq 1}
                        <span class="glyphicon glyphicon-ok-circle f18 text-primary"></span>
                        {/if}
                    </td>
                    <td>
                        {if $dataVar.classcode_isbeike eq 0}
                        <span class="glyphicon glyphicon-remove-circle f18 text-danger"></span>
                        {elseif $dataVar.classcode_isbeike eq 1}
                        <span class="glyphicon glyphicon-ok-circle f18 text-primary"></span>
                        {/if}
                    </td>
                    <td>
                        <a href="/{$u}/ClassCodeHour?company_id={$dataVar.company_id}&classcode_branch={$dataVar.classcode_branch}&site_id={$websites.site_id}" class="btn btn-primary btn-sm">
                            <span class="glyphicon glyphicon-certificate c-f"></span> 班别课时明细管理</a>
                        <a href="/{$u}/Edit?classcode_id={$dataVar.classcode_id}&site_id={$websites.site_id}" class="btn btn-primary btn-sm"><span class="glyphicon glyphicon-pencil c-f"></span> 编辑</a>
                        <a href="javascript:;" data-element="list-{$dataVar.classcode_id}" data-url="/{$u}?c=Del&classcode_id={$dataVar.classcode_id}" class="btn btn-danger btn-sm btn-del-action">
                            <span class="glyphicon glyphicon-remove c-f"></span> 删除</a>
                    </td>
                </tr>
                {/foreach}
                {/if}
                </tbody>
            </table>
            <div class="pagemenu">{$pagelist}</div>
        </div>
    </div>
</div>
