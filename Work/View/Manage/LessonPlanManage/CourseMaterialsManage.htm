<div class="content">
    <div id="container">
        <div class="bg-f row py20">
            <form action="/{$u}?c={$act}" method="post" enctype="application/x-www-form-urlencoded" class="AjaxForm">
                <input name="tempfiles_id" type="hidden" value="{$dataVar.tempfiles_id}">
                <input name="site_id" type="hidden" value="{$websites.site_id}">
                <input name="classcode_branch" type="hidden" value="{$classcode_branch}">
                <input name="teachhour_branch" type="hidden" value="{$teachhour_branch}">
                <h2 class="p20">课程资料管理<span class="fr">
                <button type="submit" class=" btn btn-success btn-cons"><i class="fa fa-check"></i>提交保存</button>
                <button type="button" class="btn btn-primary ml10 bakFromurl">返回</button></span>
                </h2>
                <div class="col-md-6" id="Form-Box-Operating">
                    <div class="pl20 f14">
                        <div class="form-group col-md-4">
                            <label for="tempfiles_name">文件名称</label>
                            <input name="tempfiles_name" id="tempfiles_name" value="{$dataVar['tempfiles_name']}" type="text" class="form-control" placeholder="请输入文件名称">
                        </div>
                        <div class="form-group col-md-4">
                            <label for="tempfiles_class">教案模式</label>
                            <select name="tempfiles_class" class="form-control" id="tempfiles_class">
                                <option value="">请选择教案模式</option>
                                <option value="0" {if $dataVar.tempfiles_class eq '0'}selected{/if}>OFFICE档案</option>
                                <option value="1" {if $dataVar.tempfiles_class eq '1'}selected{/if}>影音档案</option>
                                <option value="2" {if $dataVar.tempfiles_class eq '2'}selected{/if}>图档</option>
                            </select>
                        </div>
                        <div class="form-group" id="Trainhour-Fileurl" {if $dataVar.tempfiles_class == '0'}style="display: block"{else}style="display: none"{/if}>
                            <div class="col-md-9  mb10">
                                <label for="Trainhour-Fileurl">OFFICE档案地址</label>
                                <input name="TempfilesUrl" id="tempfiles-url" value="{$dataVar.tempfiles_url}" type="text" class="form-control" placeholder="先点击右侧按钮上传文件，或直接输入OFFICE档案地址">
                                <input name="Tempfiles-Size" id="Tempfiles-Size" value="" type="hidden" class="form-control">
                            </div>
                            <div class="col-md-3">
                                <label>&nbsp;</label>
                                <a data-color="rgb(255, 255, 255)" class="btn btn-primary my-colorpicker-control upload-btn" data-colorpicker-guid="1">
                                    <span class="glyphicon glyphicon-export"></span>
                                    选择相应文件上传<input type="file" class="oss-files-click" data-originalipt="tempfiles-url" data-fileSize="Tempfiles-Size"></a>
                            </div>
                        </div>
                        <div class="form-group" id="trainhourFileurl" {if $dataVar.tempfiles_class == '1'}style="display: block"{else}style="display: none"{/if}>
                            <div class="col-md-9  mb10">
                                <label for="tempfiles_url">影音档案地址</label>
                                <input name="tempfiles_url" id="tempfiles_url" value="{$dataVar.tempfiles_url}" type="text" class="form-control" placeholder="先点击右侧按钮上传文件，或直接输入影音档案地址">
                                <input name="tempfiles_size" id="tempfiles_size" value="" type="hidden" class="form-control">
                            </div>
                            <div class="col-md-3">
                                <label>&nbsp;</label>
                                <a data-color="rgb(255, 255, 255)" class="btn btn-primary my-colorpicker-control upload-btn" data-colorpicker-guid="1">
                                    <span class="glyphicon glyphicon-export"></span>
                                    选择影音档案文件上传<input type="file" class="oss-files-click" data-originalipt="tempfiles_url" data-fileSize="tempfiles_size"></a>
                            </div>
                        </div>
                        <div class="form-group" id="list_file_views" {if $dataVar.tempfiles_class == '2'}style="display: block"{else}style="display: none"{/if}>
                            <div class="col-md-9  mb10">
                                <label for="list_file_views">图档地址</label>
                                <input name="Tempfiles-Url" id="tempfilesurl" value="{$dataVar.tempfiles_url}" type="text" class="form-control" placeholder="先点击右侧按钮上传文件，或直接输入图档地址">
                                <input name="tempfilesSize" id="tempfilesSize" value="" type="hidden" class="form-control">
                            </div>
                            <div class="col-md-3">
                                <label>&nbsp;</label>
                                <a data-color="rgb(255, 255, 255)" class="btn btn-primary my-colorpicker-control upload-btn" data-colorpicker-guid="1">
                                    <span class="glyphicon glyphicon-export"></span>
                                    选择图档文件上传<input type="file" class="oss-files-click" data-originalipt="tempfilesurl" data-fileSize="tempfilesSize"></a>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
<script src="{$JsUrl}jquery.min.js" type="text/javascript"></script>
{literal}
<script>
    //上传OSS插件
    $(document).on("change", ".oss-files-click",function() {
        var fileoriginalipt = $(this).data("originalipt");
        var fileSize = $(this).attr('data-fileSize');
        var file = this.files[0];
        var fd = new FormData();
        fd.append("ossfile", file);
        var xhr = new XMLHttpRequest();
        xhr.open("POST", '/Images/OssUpload');

        xhr.send(fd);

        //获取执行状态
        xhr.onreadystatechange = function() {
            //如果执行状态成功，那么就把返回信息写到指定的层里
            if (xhr.readyState == 4) {
                if (xhr.status == 200) {
                    eval("var bakjson = " + xhr.responseText + ";");
                    if (bakjson.error == '1') {
                        errormotify(bakjson.errortip);
                    } else {
                        $("#" + fileoriginalipt).val(bakjson.link);
                        $("#" + fileSize).val(bakjson.filesize);
                    }
                }
            }
        }
    });

    $("#tempfiles_class").change(function(){
        var v = $("#tempfiles_class").val();
        if (v == '0') {
            $('#Trainhour-Fileurl').show();
            $('#trainhourFileurl').hide();
            $('#list_file_views').hide();
        } else if (v == '1') {
            $('#Trainhour-Fileurl').hide();
            $('#trainhourFileurl').show();
            $('#list_file_views').hide();
        } else {
            $('#Trainhour-Fileurl').hide();
            $('#trainhourFileurl').hide();
            $('#list_file_views').show();
        }
    });
</script>
{/literal}
