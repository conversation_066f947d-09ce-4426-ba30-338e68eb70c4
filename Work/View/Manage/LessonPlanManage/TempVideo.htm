<div class="content">
    <div class="bg-f">
        <h2 class="p20">
            <span class="fr">
                <a href="/{$u}/TempVideoAdd?site_id={$websites.site_id}&classcode_branch={$classcode_branch}&teachhour_branch={$teachhour_branch}" class="btn btn-success dropdown-toggle btn-demo-space">+新增视频</a>
                <button type="button" class="btn btn-primary ml10 bakFromurl">返回</button>
            </span>优质视频管理</h2>
        <div class="p20 f14">
            <div class="form-group">
                <form action="/{$u}/TempVideo" method="get" accept-charset="utf-8">
                    <input name="site_id" value="{$websites.site_id}" type="hidden">
                    <input name="classcode_branch" value="{$classcode_branch}" type="hidden">
                    <input name="teachhour_branch" value="{$teachhour_branch}" type="hidden">
                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tr>
                            <td width="4%">审核状态：</td>
                            <td width="15%">
                                <select name="tempvideo_status" class="form-control">
                                    <option value="">选择审核状态</option>
                                    <option value="0" {if $datatype.tempvideo_status eq '0'}selected{/if}>待审核</option>
                                    <option value="1" {if $datatype.tempvideo_status eq '1'}selected{/if}>已审核</option>
                                    <option value="-1" {if $datatype.tempvideo_status eq '-1'}selected{/if}>已拒绝</option>
                                </select>
                            </td>
                            <td width="2%"></td>
                            <td width="15%"><input class="form-control input-sm" id="inputEmail3" placeholder="视频名称" value="{$datatype['keyword']}" type="text" name="keyword"></td>
                            <td width="63%"><button type="submit" class="btn btn-primary ml10"><span class="glyphicon glyphicon-search c-f"></span> 搜索</button> </td>
                        </tr>
                    </table>
                </form>
            </div>

            <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table tc table-hover table-bordered">
                <thead>
                <tr>
                    <th>视频ID</th>
                    <th>班别编号</th>
                    <th>课时编号</th>
                    <th>审核状态</th>
                    <th>视频名称</th>
                    <th>视频作者</th>
                    <th>视频封面</th>
                    <th>是否优质视频</th>
                    <th>创建时间</th>
                    <th>操作</th>
                </tr>
                </thead>
                <tbody>
                {if $dataList}
                {foreach from=$dataList item=dataVar}
                <tr id="list-{$dataVar.tempvideo_id}">
                    <td>{$dataVar.tempvideo_id}</td>
                    <td>{$dataVar.classcode_branch}</td>
                    <td>{$dataVar.teachhour_branch}</td>
                    <td>
                        {if $dataVar.tempvideo_status eq '0'}待审核
                        {elseif $dataVar.tempvideo_status eq '1'}已审核
                        {elseif $dataVar.tempvideo_status eq '-1'}已拒绝
                        {/if}
                    </td>
                    <td>{$dataVar.tempvideo_name}</td>
                    <td>{$dataVar.tempvideo_author}</td>
                    <td>{if $dataVar.tempvideo_coverimg != ''}<a data-imgurl="{$dataVar.tempvideo_coverimg}" class="cp Opon-Img-View showico" title="查看效果"></a>{/if}</td>
                    <td>
                        {if $dataVar.tempvideo_isPerfect eq 0}
                        <span class="glyphicon glyphicon-remove-circle f18 text-danger"></span>
                        {elseif $dataVar.tempvideo_isPerfect eq 1}
                        <span class="glyphicon glyphicon-ok-circle f18 text-primary"></span>
                        {/if}
                    </td>
                    <td>{$dataVar.tempvideo_createtime|date_format:'%Y-%m-%d %H:%M:%S'}</td>
                    <td>
                        {if $dataVar.tempvideo_status eq '0'}
                        <a href="javascript:;" data-element="list-{$dataVar.tempvideo_id}" data-url="/{$u}?c=Examine&id={$dataVar.tempvideo_id}&classcode_branch={$classcode_branch}&teachhour_branch={$dataVar.teachhour_branch}&code=1" class="btn btn-success btn-sm btn-confirm-action"><span class="glyphicon glyphicon-road c-f"></span> 审核通过</a>
                        <a href="javascript:;" data-element="list-{$dataVar.tempvideo_id}" data-url="/{$u}?c=Examine&id={$dataVar.tempvideo_id}&classcode_branch={$classcode_branch}&teachhour_branch={$dataVar.teachhour_branch}&code=-1" class="btn btn-danger btn-sm btn-confirm-action"><span class="glyphicon glyphicon-road c-f"></span> 审核拒绝</a>
                        {/if}
                        <a href="/{$u}/TempVideoEdit?tempvideo_id={$dataVar.tempvideo_id}&site_id={$websites.site_id}&classcode_branch={$classcode_branch}&teachhour_branch={$dataVar.teachhour_branch}" class="btn btn-primary btn-sm"><span class="glyphicon glyphicon-pencil c-f"></span> 编辑</a>
                        <a href="javascript:;" data-element="list-{$dataVar.tempvideo_id}" data-url="/{$u}?c=TempVideoDel&id={$dataVar.tempvideo_id}&classcode_branch={$classcode_branch}&teachhour_branch={$dataVar.teachhour_branch}" class="btn btn-danger btn-sm btn-del-action"><span class="glyphicon glyphicon-remove c-f"></span> 删除</a>
                    </td>
                </tr>
                {/foreach}
                {/if}
                </tbody>
            </table>
            <div class="pagemenu">{$pagelist}</div>
        </div>
    </div>
</div>
