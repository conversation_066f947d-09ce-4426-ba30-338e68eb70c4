<div class="content">
    <div class="bg-f">
        <h2 class="p20">
            <span class="fr">
                <a href="/{$u}/TempPostilAdd?site_id={$websites.site_id}&classcode_branch={$classcode_branch}&teachhour_branch={$teachhour_branch}" class="btn btn-success dropdown-toggle btn-demo-space">+新增批注</a>
                <button type="button" class="btn btn-primary ml10 bakFromurl">返回</button>
            </span>优质批注管理</h2>
        <div class="p20 f14">
            <div class="form-group">
                <form action="/{$u}/TempPostil" method="get" accept-charset="utf-8">
                    <input name="site_id" value="{$websites.site_id}" type="hidden">
                    <input name="classcode_branch" value="{$classcode_branch}" type="hidden">
                    <input name="teachhour_branch" value="{$teachhour_branch}" type="hidden">
                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tr>
                            <td width="6%">是否热门批注：</td>
                            <td width="15%">
                                <select name="temppostil_isPopular" class="form-control">
                                    <option value="">选择是否热门批注</option>
                                    <option value="0" {if $datatype.temppostil_isPopular eq '0'}selected{/if}>否</option>
                                    <option value="1" {if $datatype.temppostil_isPopular eq '1'}selected{/if}>是</option>
                                </select>
                            </td>
                            <td width="6%">是否优质批注：</td>
                            <td width="15%">
                                <select name="temppostil_isPerfect" class="form-control">
                                    <option value="">选择是否优质批注</option>
                                    <option value="0" {if $datatype.temppostil_isPerfect eq '0'}selected{/if}>否</option>
                                    <option value="1" {if $datatype.temppostil_isPerfect eq '1'}selected{/if}>是</option>
                                </select>
                            </td>
                            <td width="2%"></td>
                            <td width="15%"><input class="form-control input-sm" id="inputEmail3" placeholder="批注名称" value="{$datatype['keyword']}" type="text" name="keyword"></td>
                            <td width="63%"><button type="submit" class="btn btn-primary ml10"><span class="glyphicon glyphicon-search c-f"></span> 搜索</button> </td>
                        </tr>
                    </table>
                </form>
            </div>

            <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table tc table-hover table-bordered">
                <thead>
                <tr>
                    <th>批注ID</th>
                    <th>班别编号</th>
                    <th>课时编号</th>
                    <th>批注名称</th>
                    <th>批注作者</th>
                    <th>中心批注</th>
                    <th>是否热门批注</th>
                    <th>是否优质批注</th>
                    <th>创建时间</th>
                    <th>操作</th>
                </tr>
                </thead>
                <tbody>
                {if $dataList}
                {foreach from=$dataList item=dataVar}
                <tr id="list-{$dataVar.temppostil_id}">
                    <td>{$dataVar.temppostil_id}</td>
                    <td>{$dataVar.classcode_branch}</td>
                    <td>{$dataVar.teachhour_branch}</td>
                    <td>{$dataVar.temppostil_tilte}</td>
                    <td>{$dataVar.temppostil_author}</td>
                    <td>{$dataVar.temppostil_details}</td>
                    <td>
                        {if $dataVar.temppostil_isPopular eq '0'}
                        <span class="glyphicon glyphicon-remove-circle f18 text-danger"></span>
                        {elseif $dataVar.temppostil_isPopular eq '1'}
                        <span class="glyphicon glyphicon-ok-circle f18 text-primary"></span>
                        {/if}
                    </td>
                    <td>
                        {if $dataVar.temppostil_isPerfect eq '0'}
                        <span class="glyphicon glyphicon-remove-circle f18 text-danger"></span>
                        {elseif $dataVar.temppostil_isPerfect eq '1'}
                        <span class="glyphicon glyphicon-ok-circle f18 text-primary"></span>
                        {/if}
                    </td>
                    <td>{$dataVar.temppostil_createtime|date_format:'%Y-%m-%d %H:%M:%S'}</td>
                    <td>
                        <a href="/{$u}/TempPostilEdit?temppostil_id={$dataVar.temppostil_id}&site_id={$websites.site_id}&classcode_branch={$classcode_branch}&teachhour_branch={$dataVar.teachhour_branch}" class="btn btn-primary btn-sm"><span class="glyphicon glyphicon-pencil c-f"></span> 编辑</a>
                        <a  href="javascript:;" data-element="list-{$dataVar.temppostil_id}" data-url="/{$u}?c=TempPostilDel&id={$dataVar.temppostil_id}&classcode_branch={$classcode_branch}&teachhour_branch={$dataVar.teachhour_branch}" class="btn btn-danger btn-sm btn-del-action"><span class="glyphicon glyphicon-remove c-f"></span> 删除</a>
                    </td>
                </tr>
                {/foreach}
                {/if}
                </tbody>
            </table>
            <div class="pagemenu">{$pagelist}</div>
        </div>
    </div>
</div>
