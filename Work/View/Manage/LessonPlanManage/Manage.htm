<div class="content">
    <div id="container">
        <div class="bg-f row py20">
            <form action="/{$u}?c={$act}" method="post" enctype="application/x-www-form-urlencoded" class="AjaxForm">
                <input name="classcode_id" type="hidden" value="{$dataVar.classcode_id}">
                <input name="company_id" type="hidden" value="{$dataVar.company_id}">
                <input name="site_id" type="hidden" value="{$websites.site_id}">
                <h2 class="p20">{$moduleOne.module_name}<span class="fr">
                <button type="submit" class=" btn btn-success btn-cons"><i class="fa fa-check"></i>提交保存</button>
                <button type="button" class="btn btn-primary ml10 bakFromurl">返回</button></span>
                </h2>
                <div class="col-md-6" id="Form-Box-Operating">
                    <div class="pl20 f14">
                        <div class="form-group col-md-5">
                            <label for="catcode_branch">班种编号</label>
                            <input name="catcode_branch" id="catcode_branch" value="{$dataVar['catcode_branch']}" type="text" class="form-control" placeholder="请输入班种编号">
                        </div>
                        <div class="form-group col-md-5">
                            <label for="classcode_branch">班别编号</label>
                            <input name="classcode_branch" id="classcode_branch" value="{$dataVar['classcode_branch']}" type="text" class="form-control" placeholder="请输入班别编号">
                        </div>
                        <div class="form-group col-md-5">
                            <label for="classcode_name">班别名称</label>
                            <input name="classcode_name" id="classcode_name" value="{$dataVar['classcode_name']}" type="text" class="form-control" placeholder="请输入班别名称">
                        </div>
                        <div class="form-group col-md-5">
                            <label for="classcode_hour">班别课时数量</label>
                            <input name="classcode_hour" id="classcode_hour" value="{$dataVar['classcode_hour']}" type="text" class="form-control" placeholder="请输入班别课时数量">
                        </div>
                        <div class="form-group col-md-5">
                            <label for="classcode_isopen">是否开启个性化教案</label>
                            <select name="classcode_isopen" class="form-control" id="classcode_isopen">
                                <option value="0" {if $dataVar.classcode_isopen eq '0'}selected{/if}>否</option>
                                <option value="1" {if $dataVar.classcode_isopen eq '1'}selected{/if}>是</option>
                            </select>
                        </div>
                        <div class="form-group col-md-5">
                            <label for="classcode_isbeike">是否开启备课</label>
                            <select name="classcode_isbeike" class="form-control" id="classcode_isbeike">
                                <option value="0" {if $dataVar.classcode_isbeike eq '0'}selected{/if}>否</option>
                                <option value="1" {if $dataVar.classcode_isbeike eq '1'}selected{/if}>是</option>
                            </select>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
