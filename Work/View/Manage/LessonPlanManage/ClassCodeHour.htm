<div class="content">
    <div class="bg-f">
        <h2 class="p20">
            <span class="fr">
                <a href="javascript:;" data-url="/{$u}?c=UpdateClassData&site_id={$websites.site_id}&classcode_branch={$classcode_branch}" class="btn btn-primary dropdown-toggle btn-demo-space btn-send-action"><span class="glyphicon glyphicon-sort">一键生成课时明细</span></a>
                <!--<a href="/{$u}/HourAdd?site_id={$websites.site_id}&classcode_branch={$classcode_branch}" class="btn btn-success dropdown-toggle btn-demo-space">+新增课时</a>-->
                <button type="button" class="btn btn-primary ml10 bakFromurl">返回</button>
            </span>班别课时明细管理</h2>
        <div class="p20 f14">
            <div class="form-group">
                <form action="/{$u}/ClassCodeHour" method="get" accept-charset="utf-8">
                    <input name="site_id" value="{$websites.site_id}" type="hidden">
                    <input name="classcode_branch" value="{$classcode_branch}" type="hidden">
                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tr>
                            <td width="15%"><input class="form-control input-sm" id="inputEmail3" placeholder="课时编号/课时名称" value="{$datatype['keyword']}" type="text" name="keyword"></td>
                            <td width="63%"><button type="submit" class="btn btn-primary ml10"><span class="glyphicon glyphicon-search c-f"></span> 搜索</button> </td>
                        </tr>
                    </table>
                </form>
            </div>

            <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table tc table-hover table-bordered">
                <thead>
                <tr>
                    <th>课时ID</th>
                    <th>班别编号</th>
                    <th>课时编号</th>
                    <th>课时名称</th>
                    <th>课时排序</th>
                    <th>操作</th>
                </tr>
                </thead>
                <tbody>
                {if $dataList}
                {foreach from=$dataList item=dataVar}
                <tr id="list-{$dataVar.teachhour_id}">
                    <td>{$dataVar.teachhour_id}</td>
                    <td>{$dataVar.classcode_branch}</td>
                    <td>{$dataVar.teachhour_branch}</td>
                    <td>{$dataVar.teachhour_name}</td>
                    <td>{$dataVar.teachhour_sort}</td>
                    <td class="tl">
                        <a href="/{$u}/TeachPlan?site_id={$websites.site_id}&classcode_branch={$classcode_branch}&teachhour_branch={$dataVar.teachhour_branch}" class="btn btn-primary btn-sm"><span class="glyphicon glyphicon-certificate c-f"></span> 教案明细管理</a>
                        <a href="/{$u}/CourseMaterials?site_id={$websites.site_id}&classcode_branch={$classcode_branch}&teachhour_branch={$dataVar.teachhour_branch}" class="btn btn-primary btn-sm"><span class="glyphicon glyphicon-certificate c-f"></span> 课程资料管理</a>
                        <a href="/{$u}/TempVideo?site_id={$websites.site_id}&classcode_branch={$classcode_branch}&teachhour_branch={$dataVar.teachhour_branch}" class="btn btn-primary btn-sm"><span class="glyphicon glyphicon-certificate c-f"></span> 优质视频管理</a>
                        <a href="/{$u}/TempPostil?site_id={$websites.site_id}&classcode_branch={$classcode_branch}&teachhour_branch={$dataVar.teachhour_branch}" class="btn btn-primary btn-sm"><span class="glyphicon glyphicon-certificate c-f"></span> 优质批注管理</a>
                        <a href="/{$u}/HourEdit?teachhour_id={$dataVar.teachhour_id}&site_id={$websites.site_id}" class="btn btn-primary btn-sm"><span class="glyphicon glyphicon-pencil c-f"></span> 编辑</a>
                        <!--<a  href="javascript:;" data-element="list-{$dataVar.teachhour_id}" data-url="/{$u}?c=HourDel&id={$dataVar.teachhour_id}&classcode_branch={$classcode_branch}" class="btn btn-danger btn-sm btn-del-action"><span class="glyphicon glyphicon-remove c-f"></span> 删除</a>-->
                    </td>
                </tr>
                {/foreach}
                {/if}
                </tbody>
            </table>
            <div class="pagemenu">{$pagelist}</div>
        </div>
    </div>
</div>
