<div class="content">
    <div class="bg-f">
        <h2 class="p20">{$moduleOne.module_name}
            <span class="fr">
                <button type="button" class="btn btn-primary ml10 bakFromurl">返回</button>
            </span>
        </h2>
        <div class="p20 f14">
            <div class="form-group">
                <form action="?" method="get" accept-charset="utf-8">
                    <input name="site_id" type="hidden" value="{$websites.site_id}">
                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tr>
                            <td width="4%">集团：</td>
                            <td width="15%">
                                <select name="company_id" class="form-control ">
                                    <option value="" >选择所属集团</option>
                                    {if $companyList}
                                    {foreach from=$companyList item=companyVar}
                                    <option value="{$companyVar.company_id}" {if $companyVar.company_id==$datatype.company_id}selected{/if}>{$companyVar.company_cnname}</option>
                                    {/foreach}
                                    {/if}
                                </select>
                            </td>
                            <td width="1%"></td>
                            <td width="4%">学校类型：</td>
                            <td width="14%">
                                <select name="school_type" class="form-control">
                                    <option value="" >选择学校类型</option>
                                    <option value="1" {if $datatype.school_type==1}selected{/if}>直营校</option>
                                    <option value="2" {if $datatype.school_type==2}selected{/if}>直营园</option>
                                    <option value="3" {if $datatype.school_type==3}selected{/if}>加盟校</option>
                                    <option value="4" {if $datatype.school_type==4}selected{/if}>加盟园</option>
                                </select>
                            </td>
                            <td width="1%"></td>
                            <td width="4%">搜索：</td>
                            <td width="15%"><input class="form-control input-sm" id="inputEmail3" placeholder="请输入校区编号/学校名字" value="{$datatype['keyword']}" type="text" name="keyword"></td>
                            <td width="63%"><button type="submit" class="btn btn-primary ml10"><span class="glyphicon glyphicon-search c-f"></span> 搜索</button> </td>
                        </tr>
                    </table>
                </form>
            </div>
            <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table tc table-hover table-bordered">
                <thead>
                <tr>
                    <th>序号</th>
                    <th>所属集团</th>
                    <th>集团编号</th>
                    <th>管理员账号</th>
                    <th>管理员密码</th>
                    <th>学校类型</th>
                    <th>校区编号</th>
                    <th>校园名称称</th>
                    <th>招行门店编号</th>
                    <th>银联门店编号</th>
                    <th>校园地址</th>
                </tr>
                </thead>
                <tbody>
                {if $dataList}
                {foreach from=$dataList item=dataVar key=key}
                <tr id="list-{$dataVar.school_id}">
                    <td>{$dataVar.school_id}</td>
                    <td>{$dataVar.company_cnname}</td>
                    <td>{$dataVar.company_code}</td>
                    <td>{$dataVar.staffer_branch}</td>
                    <td>{$dataVar.staffer_bakpass}</td>
                    <td>
                        {if $dataVar.school_type == '1'}
                        直营校
                        {elseif $dataVar.school_type == '2'}
                        直营园
                        {elseif $dataVar.school_type == '3'}
                        加盟校
                        {elseif $dataVar.school_type == '4'}
                        加盟园
                        {/if}
                    </td>
                    <td>{$dataVar.school_branch}</td>
                    <th>{$dataVar.school_cnname}</th>
                    <td>{$dataVar.school_cmbshopcode}</td>
                    <td>{$dataVar.school_upcshopcode}</td>
                    <td>{$dataVar.school_address}</td>
                </tr>
                {/foreach}
                {/if}
                </tbody>
            </table>
            <div class="pagemenu">{$pagelist}</div>
        </div>
    </div>
</div>