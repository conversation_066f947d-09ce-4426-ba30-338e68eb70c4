<div class="content">
    <div id="container">
        <div class="bg-f row py20">
            <div class="col-md-12">
                <div class="pl20 py20 f14">
                    <form action="/{$u}?c=Export" accept-charset="utf-8" method="post">
                        <div class="title f14 py16">暖心店活动报名信息导出
                            <div class="fr">
                                <button type="submit" class=" btn btn-success btn-cons"><span class="glyphicon glyphicon-check"></span> 导出报表</button>
                                <a href="/PtcOrders?site_id=19" class="btn btn-primary ">返回</a>
                            </div>
                        </div>
                        <div class="col-md-9">

                            <!--<div class="form-group col-md-3">
                                <label for="city_id">县市</label>
                                <select name="city_id"  id="city_id" class="CityTw-select form-control">
                                    <option value="">选择县市</option>
                                    {if $cityList}
                                    {foreach from=$cityList item=Tbvar key=key}
                                    <option value="{$Tbvar.region_id}" {if $dataVar.city_id == $Tbvar.region_id}selected{/if}>{$Tbvar.region_name}</option>
                                    {/foreach}
                                    {/if}
                                </select>
                            </div>
                            <div class="form-group col-md-3">
                                <label for="area_id">市区</label>
                                <select name="area_id"  id="area_id" class="AreaTw-select form-control" disabled>
                                    <option value="">选择市区</option>
                                    {if $dataVar.area_id}
                                    {if $areaList}
                                    {foreach from=$areaList item=Tbvar key=key}
                                    <option value="{$Tbvar.region_id}" {if $dataVar.area_id == $Tbvar.region_id}selected{/if}>{$Tbvar.region_name}</option>
                                    {/foreach}
                                    {/if}
                                    {/if}
                                </select>
                            </div>-->

                            <div class="form-group col-md-3">
                                <label for="starttime">开始时间</label>
                                <input autocomplete="off" name="starttime" id="starttime" class="form-control form_datetime" value="{$datatype['starttime']}" placeholder="开始时间" type="text">
                            </div>
                            <div class="form-group col-md-3">
                                <label for="endtime">结束时间</label>
                                <input autocomplete="off" name="endtime" id="endtime" class="form-control form_datetime" value="{$datatype['endtime']}" placeholder="结束时间" type="text">
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
