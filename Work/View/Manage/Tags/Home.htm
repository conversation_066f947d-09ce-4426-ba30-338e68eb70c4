<div class="content">
    <div class="bg-f">
        <h2 class="p20">标签管理
            <span class="fr">
                <a href="/{$u}/Add?site_id={$websites.site_id}" class="btn btn-success dropdown-toggle btn-demo-space">+新增标签</a>
                <button type="button" class="btn btn-primary ml10 bakFromurl">返回</button>
            </span>
        </h2>
        <div class="p20 f14">
            <div class="form-group">
                <form action="?" method="get" accept-charset="utf-8">
                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tr>
                            <td width="4%">搜索：</td>
                            <td width="15%"><input class="form-control input-sm" id="inputEmail3" placeholder="请输入关键词" value="{$datatype['keyword']}" type="text" name="keyword"></td>
                            <td width="63%"><button type="submit" class="btn btn-primary ml10"><span class="glyphicon glyphicon-search c-f"></span> 搜索</button> </td>
                        </tr>
                    </table>
                </form>
            </div>
            <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table tc table-hover table-bordered">
                <thead>
                <tr>
                    <th>ID</th>
                    <th>关键词</th>
                    <th>权重</th>
                    <th>替换频率</th>
                    <th>替换链接</th>
                    <th>添加时间</th>
                    <th>操作</th>
                </tr>
                </thead>
                <tbody>
                {if $dataList}
                {foreach from=$dataList item=dataVar key=key}
                <tr id="list-{$dataVar.tags_id}">
                    <td>{$dataVar.tags_id}</td>
                    <td>{$dataVar.tags_keyword}</td>
                    <td>{$dataVar.tags_weight}</td>
                    <td>{$dataVar.tags_replacenum}</td>
                    <td>{$dataVar.tags_replaceurl}</td>
                    <td>{$dataVar.tags_time|date_format:"%Y-%m-%d %T"}</td>
                    <td align="left">
                        <a href="/{$u}/Edit?site_id={$websites.site_id}&id={$dataVar.tags_id}" class="btn btn-primary btn-sm"><span class="glyphicon glyphicon-pencil c-f"></span> 编辑</a>
                        <a  href="javascript:;" data-element="list-{$dataVar.tags_id}" data-url="/{$u}?c=Del&id={$dataVar.tags_id}" class="btn btn-danger btn-sm btn-del-action"><span class="glyphicon glyphicon-remove c-f"></span> 删除</a>
                    </td>
                </tr>
                {/foreach}
                {/if}
                </tbody>
            </table>
            <div class="pagemenu">{$pagelist}</div>
        </div>
    </div>
</div>
