<div class="content">
    <div id="container">
        <div class="bg-f row py20">
            <form action="/{$u}/ImportContractExcel?site_id={$websites.site_id}" method="post" enctype="multipart/form-data">
                <h2 class="p20">{$moduleOne.module_name}--合同导入<span class="fr">
                <button type="submit" class=" btn btn-success btn-cons"><i class="fa fa-check"></i>立即导入</button>
                <button type="button" class="btn btn-primary ml10 bakFromurl">返回</button></span>
                </h2>
                <div class="col-md-10" id="Form-Box-Operating">
                    <!--<div class="form-group">-->
                        <!--<div class="col-md-6">-->
                            <!--<table width="50%" border="0" cellspacing="0" cellpadding="0" class="table tc table-hover table-bordered">-->
                                <!--<thead>-->
                                <!--<tr>-->
                                    <!--<th>ID</th>-->
                                    <!--<th>分类名称</th>-->
                                <!--</tr>-->
                                <!--</thead>-->
                                <!--<tbody>-->
                                <!--{if $dataList}-->
                                <!--{foreach from=$dataList item=dataVar key=key}-->
                                <!--<tr>-->
                                    <!--<td>{$dataVar.category_id}</td>-->
                                    <!--<td>{$dataVar.category_name}</td>-->
                                <!--</tbody>-->
                                <!--{/foreach}-->
                                <!--{/if}-->
                            <!--</table>-->
                        <!--</div>-->
                        <!--<div class="col-md-6">-->
                            <!--<table width="50%" border="0" cellspacing="0" cellpadding="0" class="table tc table-hover table-bordered">-->
                                <!--<thead>-->
                                <!--<tr>-->
                                    <!--<th>ID</th>-->
                                    <!--<th>城市</th>-->
                                <!--</tr>-->
                                <!--</thead>-->
                                <!--<tbody>-->
                                <!--{if $city}-->
                                <!--{foreach from=$city item=cityVar key=key}-->
                                <!--<tr>-->
                                    <!--<td>{$cityVar.list_parameter}</td>-->
                                    <!--<td>{$cityVar.list_name}</td>-->
                                <!--</tbody>-->
                                <!--{/foreach}-->
                                <!--{/if}-->
                            <!--</table>-->
                        <!--</div>-->

                        <div class="col-md-2 mb10">
                            <label for="down_file"></label>
                            <div class="form-control" style="border: none;    box-shadow: none;">
                                <a id="down_file" href="/Contract.xlsx" class="btn btn-primary dropdown-toggle btn-demo-space">下载导入模板</a>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <label for="uploadFile">&nbsp;</label>
                            <a data-color="rgb(255, 255, 255)" data-color-format="hex" id="cp4" class="btn btn-primary my-colorpicker-control upload-btn" data-colorpicker-guid="1">
                                <span class="glyphicon glyphicon-floppy-open"></span>
                                选择文件上传<input type="file" accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" id="uploadFile" name="uploadFile" class="ipt-file-click-one" data-originalipt="report_file"></a>
                        </div>


                    </div>
            </form>
        </div>
    </div>
</div>
