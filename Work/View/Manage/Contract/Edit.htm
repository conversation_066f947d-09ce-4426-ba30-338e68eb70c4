<div class="content">
    <div id="container">
        <div class="bg-f row py20">
            <form action="/{$u}?c={$act}" method="post" enctype="application/x-www-form-urlencoded" class="AjaxForm">
                <input name="contract_id" type="hidden" value="{$dataVar.contract_id}">
                <input name="from" type="hidden" value="{$datatype.from}">
                <input name="site_id" type="hidden" value="{$websites.site_id}">
                <h2 class="p20">{$moduleOne.module_name}<span class="fr">
                <button type="submit" class=" btn btn-success btn-cons"><i class="fa fa-check"></i>提交保存</button>
                <button type="button" class="btn btn-primary ml10 bakFromurl">返回</button></span>
                </h2>
                <div class="col-md-8" id="Form-Box-Operating">
                    <div class="pl20 py20 f14">
                        <div class="tab-content">
                            <div class="form-group col-md-4">
                                <label for="company_id">请选择属集团</label>
                                <br>
                                <select name="company_id" id="company_id"  {if $datatype.company_id && $datatype.from && $act!="Add"}disabled{/if}>
                                    <option value="">请选择属集团</option>
                                    {if $companyList}
                                    {foreach from=$companyList item=companyVar}
                                    <option value="{$companyVar.company_id}" {if $companyVar.company_id eq $datatype.company_id}selected{/if}>{$companyVar.company_cnname}({$companyVar.company_code})</option>
                                    {/foreach}
                                    {/if}
                                </select>
                            </div>

                            <div class="form-group col-md-4">
                                <label for="edition_id">请选择版本</label>
                                <br>
                                <select name="edition_id" id="edition_id">
                                <option value="">请选择版本</option>
                                {if $editionList}
                                {foreach from=$editionList item=editionVar}
                                <option value="{$editionVar.edition_id}" {if $editionVar.edition_id eq $dataVar.edition_id}selected{/if}>{$editionVar.edition_name}({$editionVar.edition_code})</option>
                                {/foreach}
                                {/if}
                                </select>
                            </div>

                            <div class="form-group col-md-4">
                                <label for="contract_name">合同名称</label>
                                <input name="contract_name" id="contract_name" value="{$dataVar.contract_name}" reg="[^ \f\n\r\t\v]" tip="请输入合同名称" type="text" class="form-control" placeholder="请输入合同名称">
                            </div>
                            <div class="form-group col-md-4">
                                <label for="contract_maxschoolnum">最大创建学园</label>
                                <input name="contract_maxschoolnum" id="contract_maxschoolnum" value="{$dataVar.contract_maxschoolnum}" reg="[^ \f\n\r\t\v]"  tip="请输入最大创建学园数量" type="number" class="form-control" placeholder="请输入最大创建学园数量"  min="1">
                            </div>
                            <div class="form-group col-md-4">
                                <label for="contract_maxstudentnum">最大创建学员数量</label>
                                <input name="contract_maxstudentnum" id="contract_maxstudentnum" value="{$dataVar.contract_maxstudentnum}" type="number" class="form-control" placeholder="最大创建学员数量"  min="1">
                            </div>
                            <div class="form-group col-md-4">
                                <label for="contract_starttime">起效时间</label>
                                <input name="contract_starttime" id="contract_starttime" value="{$dataVar.contract_starttime}" type="text" class="form-control form_datetime" placeholder="请选择起效时间">
                            </div>
                            <div class="form-group col-md-4">
                                <label for="contract_endtime">失效时间</label>
                                <input name="contract_endtime" id="contract_endtime" value="{$dataVar.contract_endtime}" type="text" class="form-control form_datetime" placeholder="请选择失效时间">
                            </div>
                            <div class="form-group col-md-4">
                                <label for="contract_allprice">销售合同金额</label>
                                <input name="contract_allprice" id="contract_allprice" value="{$dataVar.contract_allprice}" reg="[^ \f\n\r\t\v]" tip="请输入销售合同金额" type="number" class="form-control" placeholder="请输入销售合同金额"  min="0.00" step="0.01">
                            </div>
                            <div class="form-group col-md-4">
                                <label for="contract_couponmoney">销售合同优惠价金额</label>
                                <input name="contract_couponmoney" id="contract_couponmoney" value="{$dataVar.contract_couponmoney}" reg="[^ \f\n\r\t\v]" tip="请填写销售合同优惠价金额" type="number" class="form-control" placeholder="请输入销售合同优惠价金额"  min="0.00"  step="0.01">
                            </div>
                            <div class="form-group col-md-4">
                                <label for="saleman_id">选择销售人员</label>
                                <br>
                                <select name="saleman_id" id="saleman_id" >
                                <option value="">选择销售人员</option>
                                {if $saleMans}
                                {foreach from=$saleMans item=saleVar}
                                <option value="{$saleVar.saleman_id}" {if $saleVar.saleman_id eq $datatype.saleman_id}selected{/if}>{$saleVar.saleman_cnname} {if $saleVar.saleman_enname}({$saleVar.saleman_enname}){/if}</option>
                                {/foreach}
                                {/if}
                                </select>
                            </div>
                            <!--<div class="form-group col-md-4">-->
                                <!--<label for="company_homeurl">企业官网</label>-->
                                <!--<input name="company_homeurl" id="company_homeurl" value="{$dataVar.company_homeurl}" type="text" class="form-control" placeholder="请输入企业官网">-->
                            <!--</div>-->
                            <!--<div class="form-group">-->
                                <!--<div class="col-md-8  mb10">-->
                                    <!--<label for="company_logo">登录Logo (500px*300px)</label>-->
                                    <!--<input name="company_logo" id="company_logo" value="{$dataVar.company_logo}" type="text" class="form-control" placeholder="先点击右侧按钮上传图片，或直接书写图片网址">-->
                                <!--</div>-->
                                <!--<div class="col-md-4">-->
                                    <!--<label for="up_img">&nbsp;</label>-->
                                    <!--<a data-color="rgb(255, 255, 255)" data-color-format="hex" id="cp4" class="btn btn-primary my-colorpicker-control upload-btn" data-colorpicker-guid="1">-->
                                        <!--<span class="glyphicon glyphicon-floppy-open"></span>-->
                                        <!--选择图片上传<input type="file" id="up_img" class="ipt-img-click" data-element="list_img_view" data-originalipt="company_logo" data-thumbnailipt="tb_imgthum"></a>-->
                                <!--</div>-->
                            <!--</div>-->
                            <div class="clear"></div>
                        </div>
                    </div>
                </div>
                <!--<div class="col-md-4">-->
                    <!--<div class="upload-img col-md-6" id="list_img_view">-->
                        <!--<div class="f16 tc default {if $dataVar.company_logo}none{/if}">-->
                            <!--<p class="f30"><span class="glyphicon glyphicon-picture"></span></p>-->
                            <!--<p>请先上传图片后预览</p>-->
                        <!--</div>-->
                        <!--<div class="img set {if !$dataVar.company_logo}none{/if}"><img src="{$dataVar.company_logo}"></div>-->
                    <!--</div>-->
                <!--</div>-->
            </form>
        </div>
    </div>
</div>