<div class="content">
    <div class="bg-f">
        <h2 class="p20"><span class="fr"><button type="button" class="btn btn-primary ml10 bakFromurl">返回</button></span>{$moduleOne.module_name}</h2>
        <div class="p20 f14 col-md-12">
            <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table tc table-hover table-bordered">
                <thead>
                <tr>
                    <th>教师名称</th>
                    <th>教师编号</th>
                    <th>职工编号</th>
                    <th>手机号码</th>
                    <th>在职状态</th>
                </tr>
                </thead>
                <tbody>
                <tr>
                    <td>{$stafferOne.staffer_cnname}</td>
                    <th><small>{$stafferOne.staffer_branch}</small></th>
                    <th><small>{$stafferOne.staffer_employeepid}</small></th>
                    <td>{$stafferOne.staffer_mobile}</td>
                    <td>
                        {if $stafferOne.staffer_leave == '1'}
                        <a href="javascript:;" data-url="/{$u}?c=UnderIsleave&staffer_id={$stafferOne.staffer_id}" class="cp btn-state-send">
                            <span class="glyphicon glyphicon-remove-circle f18 text-danger"></span></a>
                        {else}
                        <a  href="javascript:;" data-url="/{$u}?c=UnderIsleave&staffer_id={$stafferOne.staffer_id}" class="cp btn-state-send">
                            <span class="glyphicon glyphicon-ok-circle f18 text-primary"></span></a>
                        {/if}
                    </td>
                </tr>
                </tbody>
            </table>
        </div>
        <h2 class="pb20 pl20">任职明细</h2>
        <div class="pb20">
            <div class="col-md-6">
                <h2 class="pb20 pl20">课叮铛</h2>
                <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table tc table-hover table-bordered">
                    <thead>
                    <tr>
                        <th>校园名称</th>
                        <th>校园编码</th>
                        <th>任职职务</th>
                        <th>任职角色</th>
                        <th>是否主职</th>
                    </tr>
                    </thead>
                    <tbody>
                    {if $stafferpostbeList}
                    {foreach from=$stafferpostbeList item=stafferpostbeOne}
                    <tr>
                        <td>{$stafferpostbeOne.school_cnname}</td>
                        <td>{$stafferpostbeOne.school_branch}</td>
                        <td>{$stafferpostbeOne.post_name}</td>
                        <td>{$stafferpostbeOne.postpart_name}</td>
                        <td>
                            {if $stafferpostbeOne.postbe_ismianjob == '1'}
                            <a  href="javascript:;" data-url="/{$u}?c=UnderIsmianjob&postbe_id={$stafferpostbeOne.postbe_id}" class="cp btn-state-send">
                                <span class="glyphicon glyphicon-ok-circle f18 text-primary"></span></a>
                            {else}
                            <a href="javascript:;" data-url="/{$u}?c=UnderIsmianjob&postbe_id={$stafferpostbeOne.postbe_id}" class="cp btn-state-send">
                                <span class="glyphicon glyphicon-remove-circle f18 text-danger"></span></a>
                            {/if}
                        </td>
                    </tr>
                    {/foreach}
                    {/if}
                    </tbody>
                </table>
            </div>
            <div class="col-md-6">
                <h2 class="pb20 pl20">人资系统</h2>
                <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table tc table-hover table-bordered">
                    <thead>
                    <tr>
                        <th>校园名称</th>
                        <th>校园编码</th>
                        <th>任职职务</th>
                        <th>是否主职</th>
                    </tr>
                    </thead>
                    <tbody>
                    {if $hrpostbeList}
                    {foreach from=$hrpostbeList item=hrpostbeOne}
                    <tr>
                        <td>{$hrpostbeOne.school_cnname}</td>
                        <td>{$hrpostbeOne.school_branch}</td>
                        <td>{$hrpostbeOne.FName_L2}</td>
                        <td>{$hrpostbeOne.FIsPrimary}</td>
                    </tr>
                    {/foreach}
                    {/if}
                    </tbody>
                </table>
            </div>
        </div>
        <div class="clear"></div>
    </div>
    <div class="clear"></div>
</div>
