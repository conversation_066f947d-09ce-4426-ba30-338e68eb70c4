<div class="content">
    <div class="bg-f">
        <h2 class="p20">
            <span class="fr">
                <button type="button" class="btn btn-primary ml10 bakFromurl">返回</button>
            </span>
            {$moduleOne.module_name}
        </h2>
        <div class="p20 f14">
            <div class="form-group">
                <form action="/{$u}" method="get" accept-charset="utf-8">
                    <input name="site_id" value="{$websites.site_id}" type="hidden">
                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tr>
                            <td width="3%">搜索：</td>
                            <td width="30%" colspan="1">
                                <div class="col-md-12">
                                    <input name="keyword" class="form-control input-sm" placeholder="请输入教师名称、教师编号、职工编号、手机号码" value="{$datatype['keyword']}" type="text">
                                </div>
                            </td>
                            <td><button type="submit" class="btn btn-primary ml10"><span class="glyphicon glyphicon-search c-f"></span> 搜索</button> </td>
                        </tr>
                    </table>
                </form>
            </div>
            <form role="form" action="/{$u}?c=batchWork" method="post" enctype="application/x-www-form-urlencoded" class="AjaxForm">
                <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table tc table-hover table-bordered">
                    <thead>
                    <tr>
                        <th>序号</th>
                        <th>教师名称</th>
                        <th>教师编号</th>
                        <th>职工编号</th>
                        <th>手机号码</th>
                        <th>主职校园</th>
                        <th>校园任职数</th>
                        <th width="15%">操作</th>
                    </tr>
                    </thead>
                    <tbody>
                    {if $dataList}
                    {foreach from=$dataList item=dataVar key=key}
                    <tr id="list-{$dataVar.staffer_id}">
                        <td>{$dataVar.staffer_id}</td>
                        <td>{$dataVar.staffer_cnname}</td>
                        <th><small>{$dataVar.staffer_branch}</small></th>
                        <th><small>{$dataVar.staffer_employeepid}</small></th>
                        <td>{$dataVar.staffer_mobile}</td>
                        <td>{$dataVar.school_cnname}</td>
                        <td>{$dataVar.pnums}</td>
                        <td align="left">
                            <a href="/{$u}/Browse?staffer_id={$dataVar.staffer_id}&site_id={$websites.site_id}" class="btn btn-primary btn-sm">
                                <span class="glyphicon glyphicon-barcode"></span> 查看详情</a>
                            <a href="javascript:void(0)" data-url="/{$u}?c=Audit&order_pid={$dataVar.order_pid}" data-tiptitle="确定更新?" class="btn btn-primary btn-sm btn-okconfirm-action">确定更新</a>

                        </td>
                    </tr>
                    {/foreach}
                    {/if}
                    </tbody>
                </table>
            </form>
            <div class="pagemenu">{$pagelist}</div>
        </div>
    </div>
</div>
