<div class="content">
    <div class="bg-f">
        <h2 class="p20">
            <span class="fr">
                <a href="javascript:;" data-url="https://gmcapi.kedingdang.com/Oldsys/SynClassStudent?class_branch={$classOne.class_branch}" class="btn btn-primary btn-send-action">
                    <span class="glyphicon glyphicon-sort"></span>1、更新学员数据</a>
                <a href="javascript:;" data-url="https://gmcapi.kedingdang.com/Oldsys/SynStudentChangelog?class_branch={$classOne.class_branch}" class="btn btn-primary btn-send-action">
                    <span class="glyphicon glyphicon-sort"></span>2、更新学员异动</a>
                <button type="button" class="btn btn-primary ml10 bakFromurl">返回</button>
            </span>{$moduleOne.module_name}-学员明细</h2>
        <div class="p20 f14">
            <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table tc table-hover table-bordered">
                <thead>
                <tr>
                    <th>序号</th>
                    <th>学员编号</th>
                    <th>中文名</th>
                    <th>英文名</th>
                    <th>性别</th>
                    <th>生日</th>
                </tr>
                </thead>
                <tbody>
                {if $dataList}
                {foreach from=$dataList item=dataVar key='k'}
                <tr id="list-{$dataVar.student_id}">
                    <td>{$k+1}</td>
                    <td>{$dataVar.student_branch}</td>
                    <td>{$dataVar.student_cnname}</td>
                    <td>{$dataVar.student_enname}</td>
                    <td>{$dataVar.student_sex}</td>
                    <td>{$dataVar.student_birthday}</td>
                </tr>
                {/foreach}
                {/if}
                </tbody>
            </table>
        </div>
    </div>
</div>