<div class="content">
    <div class="bg-f">
        <h2 class="p20">
            <span class="fr">
                <a href="javascript:;" data-url="https://gmcapi.kedingdang.com/Oldsys/SynClassHour?class_branch={$classOne.class_branch}" class="btn btn-primary btn-send-action">
                    <span class="glyphicon glyphicon-sort"></span>1、更新课时数据</a>
                <button type="button" class="btn btn-primary ml10 bakFromurl">返回</button>
            </span>{$moduleOne.module_name}-({$classOne.class_branch})课时明细</h2>
        <div class="p20 f14">
            <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table tc table-hover table-bordered">
                <thead>
                <tr>
                    <th>序号</th>
                    <th>课时名称</th>
                    <th>上课日期</th>
                    <th>课时开始</th>
                    <th>课时结束</th>
                    <th>时段</th>
                </tr>
                </thead>
                <tbody>
                {if $hourList}
                {foreach from=$hourList item=dataVar key='k'}
                <tr id="list-{$dataVar.student_id}">
                    <td>{$k+1}</td>
                    <td>{$dataVar.hour_name}</td>
                    <td>{$dataVar.hour_day}</td>
                    <td>{$dataVar.hour_starttime}</td>
                    <td>{$dataVar.hour_endtime}</td>
                    <td>{$dataVar.hour_noon}</td>
                </tr>
                {/foreach}
                {/if}
                </tbody>
            </table>
        </div>
    </div>
</div>