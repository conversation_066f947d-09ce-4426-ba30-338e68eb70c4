<div class="content">
    <div class="bg-f">
        <h2 class="p20">
            <span class="fr">
                <a href="javascript:;" data-url="https://gmcapi.kedingdang.com/Oldsys/UpdataSchool" class="btn btn-primary ml10 btn-send-action">
                    <span class="glyphicon glyphicon-sort"></span> 更新校园数据</a>
                <a class="btn btn-primary bakFromurl ml10"><span class="glyphicon glyphicon-share-alt c-f"></span> 返回</a>
            </span>{$moduleOne.module_name}</h2>
        <div class="p20 f14">
            <div class="form-group">
                <form action="/{$u}/{$t}" method="get" accept-charset="utf-8">
                    <input name="site_id" value="{$websites.site_id}" type="hidden">
                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tr>
                            <td width="4%">搜索：</td>
                            <td width="10%" class="pr10">
                                <select name="school_class" class="form-control">
                                    <option value="">校园种类</option>
                                    <option value="1" {if $datatype.school_class == 1}selected{/if}>直营校</option>
                                    <option value="2" {if $datatype.school_class == 2}selected{/if}>加盟校</option>
                                    <option value="3" {if $datatype.school_class == 3}selected{/if}>直营园</option>
                                    <option value="4" {if $datatype.school_class == 4}selected{/if}>加盟园</option>
                                </select>
                            </td>
                            <td width="10%" class="pr10">
                                <select name="school_nature" class="form-control">
                                    <option value="">校园类型</option>
                                    <option value="school">校</option>
                                    <option value="kid">园</option>
                                </select>
                            </td>
                            <td width="15%"><input class="form-control input-sm" id="inputEmail3" placeholder="请输入校园名称、编号" value="{$datatype['keyword']}" type="text" name="keyword"></td>
                            <td width="63%"><button type="submit" class="btn btn-primary ml10"><span class="glyphicon glyphicon-search c-f"></span> 搜索</button> </td>
                        </tr>
                    </table>
                </form>
            </div>
            <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table tc table-hover table-bordered">
                <thead>
                <tr>
                    <th>ID</th>
                    <th>校园编号</th>
                    <th>校园类型</th>
                    <th>校园中文名</th>
                    <th>所在地区</th>
                    <th>所在地址</th>
                    <th>班级数</th>
                    <th>操作</th>
                </tr>
                </thead>
                <tbody>
                {if $dataList}
                {foreach from=$dataList item=dataVar}
                <tr id="list-{$dataVar.school_id}">
                    <td>{$dataVar.school_id}</td>
                    <td>{$dataVar.school_branch}</td>
                    <td>{if $dataVar.school_type == 1}直营校
                        {elseif $dataVar.school_type == 2}加盟校
                        {elseif $dataVar.school_type == 3}直营园
                        {elseif $dataVar.school_type == 4}加盟园
                        {/if}
                    </td>
                    <td>{$dataVar.school_cnname}</td>
                    <td>
                        {$dataVar.province_name}
                        {$dataVar.city_name}
                        {$dataVar.area_name}
                    </td>
                    <td>{$dataVar.school_address}</td>
                    <td>{$dataVar.classnums}</td>
                    <td class="tl">
                        <a href="/Jidebaoupdate/Class?school_id={$dataVar.school_id}&site_id={$websites.site_id}" class="btn btn-primary btn-sm">
                            <span class="glyphicon glyphicon-list-alt c-f"></span> 查看班级</a>
                        <a href="javascript:;" data-url="https://gmcapi.kedingdang.com/Oldsys/UpdataSchoolTeacher?school_branch={$dataVar.school_branch}" class="btn btn-primary ml10 btn-send-action">
                            <span class="glyphicon glyphicon-sort"></span> 更新教师数据</a>
                        <a  href="javascript:;" data-element="list-{$dataVar.school_id}" data-url="/{$u}?c=Del&school_id={$dataVar.school_id}&site_id={$websites.site_id}" class="btn btn-danger btn-sm btn-del-action">
                            <span class="glyphicon glyphicon-remove c-f"></span> 删除</a>
                    </td>
                </tr>
                {/foreach}
                {/if}
                </tbody>
            </table>
            <div class="pagemenu">{$pagelist}</div>
        </div>
    </div>
</div>
