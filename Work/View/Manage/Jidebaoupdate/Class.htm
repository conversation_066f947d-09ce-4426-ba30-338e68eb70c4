<div class="content">
    <div class="bg-f">
        <h2 class="p20">
            <span class="fr">
                <a href="javascript:;" data-url="https://gmcapi.kedingdang.com/Oldsys/UpdateClassCatcode" class="btn btn-primary ml10 btn-send-action">
                    <span class="glyphicon glyphicon-sort"></span> 更新班种数据</a>
                <a href="javascript:;" data-url="https://gmcapi.kedingdang.com/Oldsys/UpdateCourse" class="btn btn-primary ml10 btn-send-action">
                    <span class="glyphicon glyphicon-sort"></span> 更新班别数据</a>
                <a href="javascript:;" data-url="https://gmcapi.kedingdang.com/Oldsys/UpdataSchoolClass?school_branch={$schoolOne.school_branch}" class="btn btn-primary ml10 btn-send-action">
                    <span class="glyphicon glyphicon-sort"></span> 更新班级数据</a>
                <a href="javascript:;" data-url="https://gmcapi.kedingdang.com/Oldsys/UpdataStudentEnrolled?school_branch={$schoolOne.school_branch}" class="btn btn-primary ml10 btn-send-action">
                    <span class="glyphicon glyphicon-sort"></span> 更新入校记录</a>
                <button type="button" class="btn btn-primary ml10 bakFromurl">返回</button>
            </span>{$moduleOne.module_name}</h2>
        <div class="p20 f14">
            <div class="form-group">
                <form action="/{$u}/{$t}" method="get" accept-charset="utf-8">
                    <input name="site_id" value="{$websites.site_id}" type="hidden">
                    <input name="school_id" value="{$schoolOne.school_id}" type="hidden">
                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tr>
                            <td width="4%">搜索 ：</td>
                            <td width="15%"><input class="form-control input-sm" id="inputEmail3" placeholder="请输入班级名称或编号" value="{$datatype['keyword']}" type="text" name="keyword"></td>
                            <td width="63%"><button type="submit" class="btn btn-primary ml10"><span class="glyphicon glyphicon-search c-f"></span> 搜索</button> </td>
                        </tr>
                    </table>
                </form>
            </div>

            <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table tc table-hover table-bordered">
                <thead>
                <tr>
                    <th>序号</th>
                    <th>班级中文名称</th>
                    <th>班级英文名称</th>
                    <th>所属班别</th>
                    <th>班级编号</th>
                    <th>开班起始日期</th>
                    <th>开班结束日期</th>
                    <th>教师数</th>
                    <th>学员数</th>
                    <th>操作</th>
                </tr>
                </thead>
                <tbody>
                {if $dataList}
                {foreach from=$dataList item=dataVar key='k'}
                <tr id="list-{$dataVar.class_id}">
                    <td>{$dataVar.class_id}</td>
                    <td>{$dataVar.class_cnname}</td>
                    <td>{$dataVar.class_enname}</td>
                    <td>{$dataVar.course_cnname}</td>
                    <td>{$dataVar.class_branch}</td>
                    <td>{$dataVar.class_stdate}</td>
                    <td>{$dataVar.class_enddate}</td>
                    <td>{$dataVar.teanums}</td>
                    <td>{$dataVar.stunums}</td>
                    <td class="tl">
                        <a href="/{$u}/Teacher?class_id={$dataVar.class_id}&site_id={$websites.site_id}" class="btn btn-primary">
                            <span class="glyphicon glyphicon-screenshot"></span> 查看教师</a>
                        <a href="/{$u}/Student?class_id={$dataVar.class_id}&site_id={$websites.site_id}" class="btn btn-primary">
                            <span class="glyphicon glyphicon-screenshot"></span> 查看学员</a>
                        <a href="/{$u}/Classhour?class_id={$dataVar.class_id}&site_id={$websites.site_id}" class="btn btn-primary">
                            <span class="glyphicon glyphicon-screenshot"></span> 查看课表</a>
                    </td>
                </tr>
                {/foreach}
                {/if}
                </tbody>
            </table>
            <div class="pagemenu">{$pagelist}</div>
        </div>
    </div>
</div>