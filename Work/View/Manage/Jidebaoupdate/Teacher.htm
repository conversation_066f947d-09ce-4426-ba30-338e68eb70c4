<div class="content">
    <div class="bg-f">
        <h2 class="p20">
            <span class="fr">
                <a href="javascript:;" data-url="https://gmcapi.kedingdang.com/Oldsys/UpdataClassTeacher?class_branch={$classOne.class_branch}" class="btn btn-primary btn-send-action">
                            <span class="glyphicon glyphicon-sort"></span> 更新教师数据</a>
                <button type="button" class="btn btn-primary ml10 bakFromurl">返回</button>
            </span>{$moduleOne.module_name}-教师明细</h2>
        <div class="p20 f14">
            <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table tc table-hover table-bordered">
                <thead>
                <tr>
                    <th>教师ID</th>
                    <th>教师编号</th>
                    <th>中文名</th>
                    <th>英文名</th>
                    <th>手机</th>
                    <th>性别</th>
                </tr>
                </thead>
                <tbody>
                {if $dataList}
                {foreach from=$dataList item=dataVar key='k'}
                <tr id="list-{$dataVar.staffer_id}">
                    <td>{$dataVar.staffer_id}</td>
                    <td>{$dataVar.staffer_branch}</td>
                    <td>{$dataVar.staffer_cnname}</td>
                    <td>{$dataVar.staffer_enname}</td>
                    <td>{$dataVar.staffer_mobile}</td>
                    <td>{$dataVar.staffer_sex}</td>
                </tr>
                {/foreach}
                {/if}
                </tbody>
            </table>
        </div>
    </div>
</div>