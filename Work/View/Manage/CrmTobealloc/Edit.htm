<div class="content">
    <div id="container">
        <div class="bg-f row py20">
            <form action="/{$u}?c={$act}" method="post" enctype="application/x-www-form-urlencoded" class="AjaxForm">
                <input name="client_id" type="hidden" value="{$dataVar.client_id}">
                <input name="site_id" type="hidden" value="{$websites.site_id}">
                <h2 class="p20">{$moduleOne.module_name}<span class="fr">
                <button type="submit" class=" btn btn-success btn-cons"><i class="fa fa-check"></i>提交保存</button>
                <button type="button" class="btn btn-primary ml10 bakFromurl">返回</button></span>
                </h2>
                <div class="col-md-4" id="Form-Box-Operating">
                    <div class="pl20 py20 f14">
                        <div class="tab-content">
                            <div class="form-group col-md-6">
                                <label for="client_cnname">用户名称</label>
                                <input name="client_cnname" id="client_cnname" value="{$dataVar.client_cnname}" reg="[^ \f\n\r\t\v]" tip="请填写用户名称" type="text" class="form-control" placeholder="请输入用户名称">
                            </div>
                            <div class="form-group col-md-6">
                                <label for="client_mobile">用户手机</label>
                                <input name="client_mobile" id="client_mobile" value="{$dataVar.client_mobile}" reg="[^ \f\n\r\t\v]" tip="请填写用户手机" type="text" class="form-control" placeholder="请输入用户手机">
                            </div>
                            <div class="form-group col-md-12">
                                <label for="client_remark">客户备注</label>
                                <input id="client_remark" value="{$dataVar.client_remark}" reg="[^ \f\n\r\t\v]" tip="请填写客户备注" type="text" class="form-control" placeholder="请输入客户备注">
                            </div>
                            <div class="form-group col-md-6">
                                <label for="channel_medianame">原渠道类型</label>
                                <input name="channel_medianame" id="channel_medianame" value="{$dataVar.channel_medianame}" reg="[^ \f\n\r\t\v]" tip="请填写原渠道类型" type="text" class="form-control" placeholder="请输入原渠道类型">
                            </div>
                            <div class="form-group col-md-6">
                                <label for="channel_name">原渠道明细</label>
                                <input name="channel_name" id="channel_name" value="{$dataVar.channel_name}" reg="[^ \f\n\r\t\v]" tip="请填写原渠道明细" type="text" class="form-control" placeholder="请输入原渠道明细">
                            </div>
                            <div class="form-group col-md-6">
                                <label for="channel_editname">修改渠道明细</label>
                                <input name="channel_editname" id="channel_editname" value="{$dataVar.channel_editname}" reg="[^ \f\n\r\t\v]" tip="请填写修改渠道明细" type="text" class="form-control" placeholder="请填写修改渠道明细">
                            </div>
                            <div class="form-group col-md-6">
                                <label for="client_createtime">变更时间</label>
                                <input name="client_createtime" id="client_createtime" value="{$dataVar.client_createtime|date_format:'%Y-%m-%d'}" type="text" class="form-control form_datetime" placeholder="请输入渠道变更时间">
                            </div>
                            <div class="form-group col-md-12">
                                <label for="channellog_note">修改备注</label>
                                <input name="channellog_note" id="channellog_note" value="{$dataVar.channellog_note}" reg="[^ \f\n\r\t\v]" tip="请填写修改备注" type="text" class="form-control" placeholder="请输入修改备注">
                            </div>
                            <div class="clear"></div>
                        </div>
                    </div>
                </div>

                <div class="col-md-8">
                    <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table tc table-hover table-bordered">
                        <thead>
                        <tr>
                            <th>沟通员工</th>
                            <th>沟通内容</th>
                            <th>沟通时间</th>
                        </tr>
                        </thead>
                        <tbody>
                        {if $trackList}
                        {foreach from=$trackList item=trackOne}
                        <tr>
                            <td>{$trackOne.marketer_name}</td>
                            <td class="tl">{$trackOne.track_note}</td>
                            <td>{$trackOne.track_createtime|date_format:'%Y-%m-%d %H:%M:%S'}</td>
                        </tr>
                        {/foreach}
                        {/if}
                        </tbody>
                    </table>
                </div>
            </form>
        </div>
    </div>
</div>