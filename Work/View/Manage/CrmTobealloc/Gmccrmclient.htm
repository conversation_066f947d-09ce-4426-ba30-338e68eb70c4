<div class="content">
    <div class="bg-f">
        <h2 class="p20">{$moduleOne.module_name}
            <span class="fr">
                <a class="btn btn-primary bakFromurl ml10"><span class="glyphicon glyphicon-share-alt c-f"></span> 返回</a>
            </span>
        </h2>
        <div class="p20 f14">
            <div class="form-group">
                <form action="/{$u}/{$t}" method="get" accept-charset="utf-8">
                    <input name="site_id" value="{$websites.site_id}" type="hidden">
                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tr>
                            <td width="4%">搜索：</td>
                            <td width="15%" class="pr10">
                                <select name="isseles" id="companies_type" class="form-control">
                                    <option value="">是否有SalefacesID</option>
                                    <option value="0" {if '0' == $datatype.isseles}selected{/if}>有</option>
                                    <option value="1" {if 1 == $datatype.isseles}selected{/if}>否</option>
                                </select>
                            </td>
                            <td width="15%" class="pr10">
                                <input name="keyword" class="form-control input-sm" placeholder="请输入企业名称/编号/企业管理员" value="{$datatype['keyword']}" type="text">
                            </td>
                            <td width="25%"><button type="submit" class="btn btn-primary ml10"><span class="glyphicon glyphicon-search c-f"></span> 搜索</button> </td>
                            <td></td>
                        </tr>
                    </table>
                </form>
            </div>
            <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table tc table-hover table-bordered">
                <thead>
                <tr>
                    <th>ID</th>
                    <th>家长称呼/手机号码</th>
                    <th>中文名/英文名</th>
                    <th>渠道1/渠道2</th>
                    <th>状态</th>
                    <th>年龄</th>
                    <th>已分配校区</th>
                    <th>SalesFaceID</th>
                    <th>创建时间</th>
                    <th>操作</th>
                </tr>
                </thead>
                <tbody>
                {if $dataList}
                {foreach from=$dataList item=dataVar}
                <tr id="list-{$dataVar.client_id}">
                    <td>{$dataVar.client_id}</td>
                    <td>{$dataVar.client_patriarchname}<br />{$dataVar.client_mobile}</td>
                    <td>{$dataVar.client_cnname}/{$dataVar.client_enname}</td>
                    <td>{$dataVar.channel_medianame}<br />{$dataVar.channel_name}</td>
                    <td>{$dataVar.client_tracestatus}</td>
                    <td>{$dataVar.client_age}</td>
                    <td>{$dataVar.school_cnname}</td>
                    <td>{$dataVar.outthree_userid}</td>
                    <td>{$dataVar.client_createtime|date_format:'%Y-%m-%d %H:%M:%S'}</td>
                    <td align="left">
                        <a href="javascript:;" data-tiptitle="您确定将此例子送入Salefore？" data-url="/{$u}?c=Salefores&client_id={$dataVar.client_id}"
                           class="btn btn-success btn-send-action mt5 btn-sm"><span class="glyphicon  c-f"></span> 送入Salefores</a>
                        <a href="/{$u}/Edit?client_id={$dataVar.client_id}&site_id={$websites.site_id}" class="btn btn-primary btn-sm">
                            <span class="glyphicon glyphicon-pencil c-f"></span>修改渠道</a>
                    </td>
                </tr>
                {/foreach}
                {/if}
                </tbody>
            </table>
            <div class="pagemenu">{$pagelist}</div>
        </div>
    </div>
</div>