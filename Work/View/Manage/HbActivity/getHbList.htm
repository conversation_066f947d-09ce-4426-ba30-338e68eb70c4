<div class="content">
    <div class="bg-f">
        <h2 class="p20">{$moduleOne.module_name}
            <span class="fr">
                   <a href="http://manage.kidcastle.org/HbActivity/importExcel?is_lottery={$datatype.is_lottery}" class="btn btn-success dropdown-toggle btn-demo-space">导出</a>
            </span>
        </h2>
        <div class="p20 f14">
            <div class="form-group">
                <form action="{$moduleOne.module_link}" method="get" accept-charset="utf-8">
                    <input name="site_id" value="{$websites.site_id}" type="hidden">
                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tr>
                            <td width="4%">搜索：</td>
                            <td width="15%">
                                <input name="keyword" class="form-control input-sm" placeholder="请输入手机号" value="{$datatype.keyword}" type="text">
                            </td>

                            <td width="15%">
                                <select name="is_lottery" id="is_lottery" class="form-control">
                                    <option value="" >全部</option>
                                    <option value="1" {if $datatype.is_lottery == '1'}selected{/if}>已中奖</option>
                                    <option value="2" {if $datatype.is_lottery == '2'}selected{/if}>未中奖</option>
                                </select>
                            </td>
                            <td width="63%">
                                <button type="submit" class="btn btn-primary ml10"><span
                                        class="glyphicon glyphicon-search c-f"></span> 搜索
                                </button>
                            </td>
                        </tr>
                    </table>
                </form>
            </div>
            <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table tc table-hover table-bordered">
                <thead>
                <tr>
                    <th>ID</th>
                    <th>抽奖码</th>
                    <th>中奖手机号</th>
                    <th>中奖券码</th>
                    <th>奖券价值</th>
                    <th>抽奖时间</th>
                    <th>状态</th>
                </tr>
                </thead>
                <tbody>
                {if $dataList}
                {foreach from=$dataList item=dataVar}
                <tr id="list-{$dataVar.couponscode_id}">
                    <td>{$dataVar.couponscode_id}</td>
                    <td>{$dataVar.couponscode_lotterypid}</td>
                    <td>{$dataVar.couponscode_mobile}</td>
                    <td>{$dataVar.couponscode_pid}</td>
                    <td>{if $dataVar.couponscode_price >0}{$dataVar.couponscode_price}{/if}</td>
                    <td>{if $dataVar.couponscode_lotterytime}{$dataVar.couponscode_lotterytime|date_format:'%Y-%m-%d %H:%M'}{/if}</td>
                    <td>{if $dataVar.couponscode_status==1}已使用{else}未使用{/if}</td>

                </tr>
                {/foreach}
                {/if}
                </tbody>
            </table>
            <div class="pagemenu">{$pagelist}</div>
        </div>
    </div>
</div>