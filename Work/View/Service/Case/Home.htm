<!DOCTYPE HTML>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    {include file="key.htm"}
</head>

<body>

<div class="page-container row condensed">
    {include file="page-sidebar.htm"}
    {include file="footer.htm"}
    <div class="page-content">
        {include file="header.htm"}
        <div class="mx20 breadcrumb-w">
            <div class="breadcrumb fl">
                <span>工单管理</span>
            </div>
            <a href="javascript:" class="fr bakFromurl mt6"><i class="icon-back mr5"></i>返回</a>
            <div class="clear"></div>
        </div>
        <div class="content">
            <div id="container">
                <div class="row">
                    <div class="bg-f">
                        <div class="p20 f14">
                            <div class="form-group">
                                <form action="/{$u}?c={$act}" method="get" accept-charset="utf-8">
                                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                        <tr>
                                            <td width="4%">搜索：</td>
                                            <td width="16%" class="pr10">
                                                <select name="company_id" id="company_id" class="form-control CompanyAjax">
                                                    <option value="">请选择集团</option>
                                                    {if $companyOne.company_id}
                                                    <option value="{$companyOne.company_id}" selected>{$companyOne.company_cnname}</option>
                                                    {/if}
                                                </select>
                                            </td>
                                            <td width="16%" class="pr10">
                                                <select name="school_id" id="school_id" class="form-control SchoolAjax">
                                                    <option value="">请先选择集团后选择学校</option>
                                                    {if $schoolOne.school_cnname}
                                                    <option value="{$schoolOne.school_id}" selected>{$schoolOne.school_cnname}</option>
                                                    {/if}
                                                </select>
                                            </td>

                                            <td width="16%" class="pr10">
                                                <select name="case_level" id="case_level" class="form-control">
                                                    <option value="">处理等级</option>
                                                    <option value="0" {if $datatype.case_level == '0'}selected{/if}>初级</option>
                                                    <option value="1" {if $datatype.case_level == '1'}selected{/if}>中级</option>
                                                    <option value="2" {if $datatype.case_level == '2'}selected{/if}>高级</option>
                                                </select>
                                            </td>
                                            <td width="16%" class="pr10">
                                                <select name="case_state" id="case_state" class="form-control">
                                                    <option value="">处理状态</option>
                                                    <option value="0" {if $datatype.case_state == '0'}selected{/if}>待受理</option>
                                                    <option value="1" {if $datatype.case_state == '1'}selected{/if}>处理中</option>
                                                    <!--<option value="2" {if $datatype.case_state == '2'}selected{/if}>业务流转</option>-->
                                                    <option value="3" {if $datatype.case_state == '3'}selected{/if}>已处理</option>
                                                    <!--<option value="4" {if $datatype.case_state == '4'}selected{/if}>已完结</option>-->
                                                    <option value="-1" {if $datatype.case_state == '-1'}selected{/if}>不能处理</option>
                                                </select>
                                            </td>
                                            <td width="15%" class="pr10"><input name="keyword" class="form-control input-sm" placeholder="请输入工单标题" value="{$datatype['keyword']}" type="text"></td>
                                            <td width="20%">
                                                <button type="submit" id="FromSubmit" class="btn btn-primary ml10"><span class="glyphicon glyphicon-search c-f"></span> 搜索</button>
                                            </td>
                                            <td width="20%">
                                                <a href="/{$u}/Add?site_id={$websites.site_id}" class="btn btn-success dropdown-toggle btn-demo-space">+新增数据</a>
                                            </td>
                                        </tr>
                                    </table>
                                </form>
                            </div>
                            <form role="form" action="/{$u}?c=batchWork" method="post" enctype="application/x-www-form-urlencoded" class="AjaxForm">
                                <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table tc table-hover table-bordered">
                                    <thead>
                                    <tr>
                                        <th>工单标题</th>
                                        <th>工单优先级别</th>
                                        <th>处理状态</th>
                                        <th>企业编号</th>
                                        <th>企业名称</th>
                                        <th>校区名称</th>
                                        <th>联系人姓名</th>
                                        <th>联系人电话</th>
                                        <th>联系人地址</th>
                                        <th>操作</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    {if $dataList}
                                    {foreach from=$dataList item=dataVar}
                                    <tr id="list-{$dataVar.case_pid}">
                                        <td>{$dataVar.case_title}</td>
                                        <td>{$dataVar.case_level_name}</td>
                                        <td>{$dataVar.case_state_name}</td>
                                        <td>{$dataVar.company_code}</td>
                                        <td>{$dataVar.company_cnname}</td>
                                        <td>{$dataVar.school_cnname}</td>
                                        <td>{$dataVar.case_cnname}</td>
                                        <td>{$dataVar.case_mobile}</td>
                                        <td>{$dataVar.case_address}</td>
                                        <td align="left">
                                            <a href="/{$u}/Look?case_pid={$dataVar.case_pid}" class="btn btn-success btn-sm">查看</a>
                                            {if $dataVar.case_state < '3'}
                                            <a href="/{$u}/Edit?case_pid={$dataVar.case_pid}" class="btn btn-primary btn-sm">编辑</a>
                                            {/if}
                                            {if $dataVar.case_state == '0'}
                                            <a href="/{$u}/Accept?case_pid={$dataVar.case_pid}" class="btn btn-primary btn-sm">受理</a>
                                            {elseif $dataVar.case_state == '1'}
                                            <a href="/{$u}/Tracks?case_pid={$dataVar.case_pid}" class="btn btn-primary btn-sm">跟进</a>
                                            <a href="javascript:;" data-element="list-{$dataVar.case_pid}" data-url="/{$u}?c=Handle&case_pid={$dataVar.case_pid}" data-tiptitle="您确定工单已经处理完毕了么？" class="btn btn-warning btn-sm btn-confirm-action">已处理</a>
                                            {elseif $dataVar.case_state == '3'}
                                            <a href="javascript:;" data-element="list-{$dataVar.case_pid}" data-url="/{$u}?c=Close&case_pid={$dataVar.case_pid}" data-tiptitle="您确定工单已经可以结案了么？" class="btn btn-danger btn-sm btn-confirm-action">结案</a>
                                            {/if}
                                        </td>
                                    </tr>
                                    {/foreach}
                                    {/if}
                                    </tbody>
                                </table>
                            </form>
                            <div class="pagemenu">{$pagelist}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{include file="jscom.htm"}
<script src="{$PluginsUrl}jquery-form/jquery.form.js" type="text/javascript"></script>
<script src="{$JsUrl}int.form.js" type="text/javascript"></script>
</body>
</html>
