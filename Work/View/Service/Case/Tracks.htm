<!DOCTYPE HTML>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    {include file="key.htm"}
</head>

<body>

<div class="page-container row condensed">
    {include file="page-sidebar.htm"}
    {include file="footer.htm"}
    <div class="page-content">
        {include file="header.htm"}
        <div class="mx20 breadcrumb-w">
            <div class="breadcrumb fl">
                <a href="/Case">工单管理</a>
                <span>&gt; {$dataVar.case_title} > 工单跟踪</span>
            </div>
            <a href="javascript:" class="fr bakFromurl mt6"><i class="icon-back mr5"></i>返回</a>
            <div class="clear"></div>
        </div>
        <div class="content">
            <div id="container">
                <div class="bg-f py20">
                    <form action="/{$u}?c={$act}" method="post" enctype="application/x-www-form-urlencoded" class="AjaxForm">
                        <input name="case_pid" type="hidden" value="{$dataVar.case_pid}">
                        <div id="Form-Box-Operating">
                            <div class="px20 py20 f14 row">
                                <div class="form-group col-md-2">
                                    <label for="tracks_state"><em></em>订单状态</label>
                                    <select name="tracks_state" id="tracks_state" class="form-control">
                                        <option value="1" >处理中</option>
                                        <option value="3" >已处理</option>
                                    </select>
                                </div>
                                <div class="clear"></div>
                                <div class="col-md-6">
                                    <label >跟踪内容</label>
                                    <div class="control-box  debug-gray">
                                        <div class="box-editor">
                                            <textarea name="tracks_information" id="qeditor_body" class="textarea qeditor"></textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group txtrg pr30">
                                <button type="button" class="btn btn-default mr40 bakFromurl">返回</button>
                                <button type="submit" class=" btn btn-primary btn-cons">提交保存</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{include file="jscom.htm"}
<script src="{$PluginsUrl}jquery-form/jquery.form.js" type="text/javascript"></script>
<script src="{$JsUrl}int.form.js" type="text/javascript"></script>
</body>
</html>
