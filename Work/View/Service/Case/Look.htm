<!DOCTYPE HTML>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    {include file="key.htm"}
</head>

<body>

<div class="page-container row condensed">
    {include file="page-sidebar.htm"}
    {include file="footer.htm"}
    <div class="page-content">
        {include file="header.htm"}
        <div class="mx20 breadcrumb-w">
            <div class="breadcrumb fl">
                <span>工单管理 -> {$dataVar.case_title} -> 查看详情</span>
            </div>
            <a href="javascript:" class="fr bakFromurl mt6"><i class="icon-back mr5"></i>返回</a>
            <div class="clear"></div>
        </div>
        <div class="content">
            <div id="container">
                <div class="row">
                    <div class="bg-f">
                        <div class="p20 f14">
                            <form role="form" action="/{$u}?c=batchWork" method="post" enctype="application/x-www-form-urlencoded" class="AjaxForm">
                                <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table tc table-hover table-bordered">
                                    <thead>
                                    <tr>
                                        <th>工单标题</th>
                                        <th>工单优先级别</th>
                                        <th>处理状态</th>
                                        <th>企业编号</th>
                                        <th>企业名称</th>
                                        <th>校区名称</th>
                                        <th>联系人姓名</th>
                                        <th>联系人电话</th>
                                        <th>联系人地址</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <tr id="list-{$dataVar.case_pid}">
                                        <td>{$dataVar.case_title}</td>
                                        <td>{$dataVar.case_level_name}</td>
                                        <td>{$dataVar.case_state_name}</td>
                                        <td>{$dataVar.company_code}</td>
                                        <td>{$dataVar.company_cnname}</td>
                                        <td>{$dataVar.school_cnname}</td>
                                        <td>{$dataVar.case_cnname}</td>
                                        <td>{$dataVar.case_mobile}</td>
                                        <td>{$dataVar.case_address}</td>
                                    </tr>
                                    </tbody>
                                </table>
                            </form>
                        </div>

                        <div class="p20 f14">
                            <form role="form" action="/{$u}?c=batchWork" method="post" enctype="application/x-www-form-urlencoded" class="AjaxForm">
                                <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table tc table-hover table-bordered">
                                    <thead>
                                    <tr>
                                        <th>工单编号</th>
                                        <th>工单状态</th>
                                        <th>跟踪信息</th>
                                        <th>操作人</th>
                                        <th>操作时间</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    {if $tracklist}
                                    {foreach from=$tracklist item=dataVar}
                                    <tr>
                                        <td>{$dataVar.case_pid}</td>
                                        <td>{$dataVar.tracks_state_name}</td>
                                        <td>{$dataVar.tracks_information}</td>
                                        <td>{$dataVar.tracks_playname}</td>
                                        <td>{$dataVar.tracks_time|date_format:'%Y-%m-%d %H:%M:%S'}</td>
                                    </tr>
                                    {/foreach}
                                    {/if}
                                    </tbody>
                                </table>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{include file="jscom.htm"}
<script src="{$PluginsUrl}jquery-form/jquery.form.js" type="text/javascript"></script>
<script src="{$JsUrl}int.form.js" type="text/javascript"></script>
</body>
</html>
