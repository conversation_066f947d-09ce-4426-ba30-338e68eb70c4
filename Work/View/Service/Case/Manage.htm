<!DOCTYPE HTML>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    {include file="key.htm"}
</head>

<body>

<div class="page-container row condensed">
    {include file="page-sidebar.htm"}
    {include file="footer.htm"}
    <div class="page-content">
        {include file="header.htm"}
        <div class="mx20 breadcrumb-w">
            <div class="breadcrumb fl">
                <a href="/Case">工单管理</a>
                <span>&gt; {if $act == 'Add'}添加工单{else}编辑工单{/if}</span>
            </div>
            <a href="javascript:" class="fr bakFromurl mt6"><i class="icon-back mr5"></i>返回</a>
            <div class="clear"></div>
        </div>
        <div class="content">
            <div id="container">
                <div class="bg-f py20">
                    <form action="/{$u}?c={$act}" method="post" enctype="application/x-www-form-urlencoded" class="AjaxForm">
                        <input name="case_pid" type="hidden" value="{$dataVar.case_pid}">
                        <div id="Form-Box-Operating">
                            <div class="px20 py20 f14 row">
                                <div class="form-group col-md-3">
                                    <label for="case_title"><em>*</em>工单标题</label>
                                    <input name="case_title" id="case_title" value="{$dataVar.case_title}" type="text" reg="[^ \f\n\r\t\v]" tip="工单标题" class="form-control" placeholder="请输入工单标题">
                                </div>
                                <div class="form-group col-md-3">
                                    <label for="company_id"><em>*</em>请选择集团</label>
                                    <select name="company_id" id="company_id" class="form-control CompanyAjax">
                                        <option value="">请选择集团</option>
                                        {if $companyOne.company_id}
                                        <option value="{$companyOne.company_id}" selected>{$companyOne.company_cnname}</option>
                                        {/if}
                                        <!--<option value="0" {if $dataVar.company_id == '0'}selected{/if}>无需密码</option>-->
                                    </select>
                                </div>
                                <div class="form-group col-md-3">
                                    <label for="school_id"><em>*</em>请先选择集团后选择学校</label>
                                    <select name="school_id" id="school_id" class="form-control SchoolAjax">
                                        <option value="">请先选择集团后选择学校</option>
                                        {if $schoolOne.school_cnname}
                                        <option value="{$schoolOne.school_id}" selected>{$schoolOne.school_cnname}</option>
                                        {/if}
                                        <!--<option value="0" {if $dataVar.school_id == '0'}selected{/if}>无需密码</option>-->
                                    </select>
                                </div>
                                <div class="clear"></div>
                                <div class="form-group col-md-2">
                                    <label for="case_level"><em></em>优先级</label>
                                    <select name="case_level" id="case_level" class="form-control">
                                        <option value="0" {if $dataVar.case_level == '0'}selected{/if}>初级</option>
                                        <option value="1" {if $dataVar.case_level == '1'}selected{/if}>中级</option>
                                        <option value="2" {if $dataVar.case_level == '2'}selected{/if}>高阶</option>
                                    </select>
                                </div>
                                <div class="form-group col-md-2">
                                    <label for="case_cnname"><em></em>反馈人姓名</label>
                                    <input name="case_cnname" id="case_cnname" value="{$dataVar.case_cnname}" type="text" tip="反馈人姓名" class="form-control" placeholder="请输入反馈人姓名">
                                </div>
                                <div class="form-group col-md-2">
                                    <label for="case_mobile"><em></em>反馈人姓手机</label>
                                    <input name="case_mobile" id="case_mobile" value="{$dataVar.case_mobile}" type="text" tip="反馈人姓手机" class="form-control" placeholder="请输入反馈人姓手机">
                                </div>
                                <div class="form-group col-md-3">
                                    <label for="case_address"><em></em>反馈人姓地址</label>
                                    <input name="case_address" id="case_address" value="{$dataVar.case_address}" type="text" tip="反馈人姓地址" class="form-control" placeholder="请输入反馈人姓地址">
                                </div>
                                <div class="clear"></div>
                                <div class="col-md-6">
                                    <label >反馈内容</label>
                                    <div class="control-box  debug-gray">
                                        <div class="box-editor">
                                            <textarea name="case_content" id="qeditor_body" class="textarea qeditor">{$dataVar.case_content}</textarea>
                                        </div>
                                    </div>
                                </div>
                                <!--<div class="col-md-6">
                                    <label >反馈内容补充说明</label>
                                    <div class="control-box  debug-gray">
                                        <div class="box-editor">
                                            <textarea name="case_remark" id="qeditor_body1" class="textarea qeditor">{$dataVar.case_remark}</textarea>
                                        </div>
                                    </div>
                                </div>-->

                            </div>
                            <div class="form-group txtrg pr30">
                                <button type="button" class="btn btn-default mr40 bakFromurl">返回</button>
                                <button type="submit" class=" btn btn-primary btn-cons">提交保存</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{include file="jscom.htm"}
<script src="{$PluginsUrl}jquery-form/jquery.form.js" type="text/javascript"></script>
<script src="{$JsUrl}int.form.js" type="text/javascript"></script>
</body>
</html>
