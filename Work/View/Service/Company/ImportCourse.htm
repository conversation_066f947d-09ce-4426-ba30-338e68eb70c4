<!DOCTYPE HTML>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    {include file="key.htm"}
</head>

<body>

<div class="page-container row condensed">
    {include file="page-sidebar.htm"}
    {include file="footer.htm"}
    <div class="page-content">
        {include file="header.htm"}
        <div class="mx20 breadcrumb-w">
            <div class="breadcrumb fl">
                <span>集团管理->课程资料导入</span>
            </div>
            <a href="javascript:" class="fr bakFromurl mt6"><i class="icon-back mr5"></i>返回</a>
            <div class="clear"></div>
        </div>

        <div class="content">
            <div class="bg-f">
                <h2 class="p20"><span class="fr"><button type="button" class="btn btn-primary ml10 bakFromurl">返回</button></span>导入课程信息</h2>
                <div class="p20 f14">
                    {if $PlayInfo}
                    <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table tc table-hover table-bordered">
                        <thead>
                        <tr>
                            <th>班组名称</th>
                            <th>班组编号</th>
                            <th>班种名称</th>
                            <th>班种编号</th>
                            <th>课程别名称</th>
                            <th>课程别编号</th>
                            <th>课程类型</th>
                            <th>考勤方式</th>
                            <th>实际课次</th>
                            <th>免手续费课次数</th>
                            <th>导入信息</th>
                            <th>反馈信息</th>
                        </tr>
                        </thead>
                        <tbody>
                        {foreach from=$PlayInfo item=dataVar}
                        <tr>
                            <td>{$dataVar.coursetype_cnname}</td>
                            <td>{$dataVar.coursetype_branch}</td>
                            <td>{$dataVar.coursecat_cnname}</td>
                            <td>{$dataVar.coursecat_branch}</td>
                            <td>{$dataVar.course_cnname}</td>
                            <td>{$dataVar.course_branch}</td>
                            <td>{$dataVar.course_inclasstype}</td>
                            <td>{$dataVar.course_checkingintype}</td>
                            <td>{$dataVar.course_classnum}</td>
                            <td>{$dataVar.course_freenums}</td>
                            <td>
                                {if $dataVar.error == '1'}
                                <a class="cp"><span class="glyphicon glyphicon-remove-circle f18 text-danger"></span></a>
                                {else $dataVar.error == '0'}
                                <a class="cp"><span class="glyphicon glyphicon-ok-circle f18 text-primary"></span></a>
                                {/if}
                            </td>
                            <td>
                                {$dataVar.errortip}
                            </td>
                        </tr>
                        {/foreach}
                        </tbody>
                    </table>
                    {/if}
                </div>
            </div>
        </div>


    </div>
</div>
{include file="jscom.htm"}
<script src="{$PluginsUrl}jquery-form/jquery.form.js" type="text/javascript"></script>
<script src="{$JsUrl}int.form.js" type="text/javascript"></script>
</body>
</html>





