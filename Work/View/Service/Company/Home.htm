<!DOCTYPE HTML>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    {include file="key.htm"}
</head>

<body>

<div class="page-container row condensed">
    {include file="page-sidebar.htm"}
    {include file="footer.htm"}
    <div class="page-content">
        {include file="header.htm"}
        <div class="mx20 breadcrumb-w">
            <div class="breadcrumb fl">
                <span>集团管理</span>
            </div>
            <a href="javascript:" class="fr bakFromurl mt6"><i class="icon-back mr5"></i>返回</a>
            <div class="clear"></div>
        </div>
        <div class="content">
            <div id="container">
                <div class="row">
                    <div class="bg-f">
                        <div class="p20 f14">
                            <div class="form-group">
                                <form action="/{$u}" method="get" accept-charset="utf-8">
                                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                        <tr>
                                            <td width="5%">搜索：</td>
                                            <td width="8%" class="pr10">
                                                <select name="company_ismajor" id="company_ismajor" class="form-control">
                                                    <option value="">请选择版本</option>
                                                    <option value="0" {if $datatype.company_ismajor == '0'}selected{/if}>免费版集团</option>
                                                    <option value="1" {if $datatype.company_ismajor == '1'}selected{/if}>普通版集团</option>
                                                    <option value="2" {if $datatype.company_ismajor == '2'}selected{/if}>专业版集团</option>
                                                </select>
                                            </td>
                                            <td width="15%" class="pr10"><input name="keyword" class="form-control input-sm" placeholder="请输入集团信息" value="{$datatype['keyword']}" type="text"></td>
                                            <td width="50%">
                                                <button type="submit" id="FromSubmit" class="btn btn-primary ml10"><span class="glyphicon glyphicon-search c-f"></span> 搜索</button>
                                            </td>
                                        </tr>
                                    </table>
                                </form>
                            </div>
                            <form role="form" action="/{$u}?c=batchWork" method="post" enctype="application/x-www-form-urlencoded" class="AjaxForm">
                                <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table tc table-hover table-bordered">
                                    <thead>
                                    <tr>
                                        <th>企业授权编号</th>
                                        <th>版本</th>
                                        <th>语言</th>
                                        <th>企业简称</th>
                                        <th>企业中文名称</th>
                                        <th>联系人姓名</th>
                                        <th>联系人电话</th>
                                        <th>所在地址</th>
                                        <th>有效期</th>
                                        <th>操作</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    {if $dataList}
                                    {foreach from=$dataList item=dataVar}
                                    <tr id="list-{$dataVar.company_id}">
                                        <td>{$dataVar.company_code}</td>
                                        <td>{if $dataVar.company_ismajor == '0'}
                                            <span class="c-red">免费版集团</span>
                                            {elseif $dataVar.company_ismajor == '1'}
                                            <span class="c-red">普通版集团</span>
                                            {elseif $dataVar.company_ismajor == '2'}
                                            <span class="c-red">专业版集团</span>
                                            {/if}</td>
                                        <td>{if $dataVar.company_language == 'zh'}
                                            简体
                                            {elseif $dataVar.company_language == 'tw'}
                                            繁体
                                            {/if}</td>
                                        <td>{$dataVar.company_shortname}</td>
                                        <td>{$dataVar.company_cnname}</td>
                                        <td>{$dataVar.company_name}</td>
                                        <td>{$dataVar.company_mobile}</td>
                                        <td>{$dataVar.company_address}</td>
                                        <td>{$dataVar.contract_starttime}-{$dataVar.contract_endtime}</td>
                                        <td align="left">
                                            <a href="/{$u}/Import?company_id={$dataVar.company_id}" class="btn btn-primary btn-sm">资料导入</a>
                                            <a href="/{$u}/School?company_id={$dataVar.company_id}" class="btn btn-primary btn-sm">分校管理</a>
                                            <a href="/{$u}/Shoping?company_id={$dataVar.company_id}" class="btn btn-primary btn-sm">微商城管理</a>
                                        </td>
                                    </tr>
                                    {/foreach}
                                    {/if}
                                    </tbody>
                                </table>
                            </form>
                            <div class="pagemenu">{$pagelist}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{include file="jscom.htm"}
<script src="{$PluginsUrl}jquery-form/jquery.form.js" type="text/javascript"></script>
<script src="{$JsUrl}int.form.js" type="text/javascript"></script>
</body>
</html>
