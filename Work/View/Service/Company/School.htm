<!DOCTYPE HTML>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    {include file="key.htm"}
</head>

<body>

<div class="page-container row condensed">
    {include file="page-sidebar.htm"}
    {include file="footer.htm"}
    <div class="page-content">
        {include file="header.htm"}
        <div class="mx20 breadcrumb-w">
            <div class="breadcrumb fl">
                <span>集团管理->分校管理</span>
            </div>
            <a href="javascript:" class="fr bakFromurl mt6"><i class="icon-back mr5"></i>返回</a>
            <div class="clear"></div>
        </div>
        <div class="content">
            <div id="container">
                <div class="row">
                    <div class="bg-f">
                        <div class="p20 f14">
                            <div class="form-group">
                                <form action="/{$u}/School" method="get" accept-charset="utf-8">
                                    <input name="company_id" value="{$datatype['company_id']}" type="hidden">
                                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                        <tr>
                                            <td width="5%">搜索：</td>
                                            <td width="15%" class="pr10"><input name="keyword" class="form-control input-sm" placeholder="请输入学校信息" value="{$datatype['keyword']}" type="text"></td>
                                            <td width="50%">
                                                <button type="submit" id="FromSubmit" class="btn btn-primary ml10"><span class="glyphicon glyphicon-search c-f"></span> 搜索</button>

                                                <a href="/{$u}/ImportSchool?company_id={$datatype['company_id']}" class="btn btn-primary btn-sm">分校导入</a>
                                            </td>
                                        </tr>
                                    </table>
                                </form>
                            </div>
                            <form role="form" action="/{$u}?c=batchWork" method="post" enctype="application/x-www-form-urlencoded" class="AjaxForm">
                                <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table tc table-hover table-bordered">
                                    <thead>
                                    <tr>
                                        <th>校区名称</th>
                                        <th>学校英文名称</th>
                                        <th>校区编号</th>
                                        <th>校园简称</th>
                                        <th>所属区域</th>
                                        <th>所属主体</th>
                                        <th>地址</th>
                                        <th>电话</th>
                                        <th>创建时间</th>
                                        <th>操作</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    {if $dataList}
                                    {foreach from=$dataList item=dataVar}
                                    <tr id="list-{$dataVar.school_id}">
                                        <td>{$dataVar.school_cnname}</td>
                                        <td>{$dataVar.school_enname}</td>
                                        <td>{$dataVar.school_branch}</td>
                                        <td>{$dataVar.school_shortname}</td>
                                        <td>{$dataVar.district_cnname}</td>
                                        <td>{$dataVar.companies_cnname}</td>
                                        <td>{$dataVar.school_address}</td>
                                        <td>{$dataVar.school_phone}</td>
                                        <td>{$dataVar.school_createtime|date_format:'%Y-%m-%d %H:%M:%S'}</td>

                                        <td align="left">
                                            <a href="/{$u}/ImportSchool?school_id={$dataVar.school_id}" class="btn btn-primary btn-sm">资料导入</a>
                                        </td>
                                    </tr>
                                    {/foreach}
                                    {/if}
                                    </tbody>
                                </table>
                            </form>
                            <div class="pagemenu">{$pagelist}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{include file="jscom.htm"}
<script src="{$PluginsUrl}jquery-form/jquery.form.js" type="text/javascript"></script>
<script src="{$JsUrl}int.form.js" type="text/javascript"></script>
</body>
</html>
