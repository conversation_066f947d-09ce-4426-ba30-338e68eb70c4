<!DOCTYPE HTML>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    {include file="key.htm"}
</head>

<body>

<div class="page-container row condensed">
    {include file="page-sidebar.htm"}
    {include file="footer.htm"}
    <div class="page-content">
        {include file="header.htm"}
        <div class="mx20 breadcrumb-w">
            <div class="breadcrumb fl">
                <span>集团管理->分校资料导入</span>
            </div>
            <a href="javascript:" class="fr bakFromurl mt6"><i class="icon-back mr5"></i>返回</a>
            <div class="clear"></div>
        </div>

        <div class="content">
            <div class="bg-f">
                <h2 class="p20"><span class="fr"><button type="button" class="btn btn-primary ml10 bakFromurl">返回</button></span>导入分校信息</h2>
                <div class="p20 f14">
                    {if $PlayInfo}
                    <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table tc table-hover table-bordered">
                        <thead>
                        <tr>
                            <th>校区名称</th>
                            <th>学校英文名</th>
                            <th>学校简称</th>
                            <th>所在区域</th>
                            <th>区域编号</th>
                            <th>所属企业</th>
                            <th>校园地址</th>
                            <th>联系电话</th>
                            <th>导入信息</th>
                            <th>反馈信息</th>
                        </tr>
                        </thead>
                        <tbody>
                        {foreach from=$PlayInfo item=dataVar}
                        <tr>
                            <td>{$dataVar.school_cnname}</td>
                            <td>{$dataVar.school_enname}</td>
                            <td>{$dataVar.school_shortname}</td>
                            <td>{$dataVar.district_cnname}</td>
                            <td>{$dataVar.district_branch}</td>
                            <td>{$dataVar.companies_cnname}</td>
                            <td>{$dataVar.school_address}</td>
                            <td>{$dataVar.school_phone}</td>
                            <td>
                                {if $dataVar.error == '1'}
                                <a class="cp"><span class="glyphicon glyphicon-remove-circle f18 text-danger"></span></a>
                                {else $dataVar.error == '0'}
                                <a class="cp"><span class="glyphicon glyphicon-ok-circle f18 text-primary"></span></a>
                                {/if}
                            </td>
                            <td>
                                {$dataVar.errortip}
                            </td>
                        </tr>
                        {/foreach}
                        </tbody>
                    </table>
                    {/if}
                </div>
            </div>
        </div>


    </div>
</div>
{include file="jscom.htm"}
<script src="{$PluginsUrl}jquery-form/jquery.form.js" type="text/javascript"></script>
<script src="{$JsUrl}int.form.js" type="text/javascript"></script>
</body>
</html>





