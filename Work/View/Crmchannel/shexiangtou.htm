<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <title>校区识别系统</title>
    <!-- 引入 Bootstrap -->
    <link href="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet">
    <link href="/Work/Static/Crmchannel/css/select2.css" rel="stylesheet" />
    <link href="/Work/Static/Crmchannel/js/mui.picker.min.css" rel="stylesheet" />
    <!-- 引入 Bootstrap JS -->
    <script src="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>
    <script src="/Work/Static/Crmchannel/js/jquery.min.js" type="text/javascript"></script>
    <script src="/Work/Static/Crmchannel/js/select2.js?timevie=20190323"></script>
    <script src="/Work/Static/Crmchannel/js/mui.min.js"></script>
    <script src="/Work/Static/Crmchannel/js/mui.picker.min.js"></script>

    <style>
        .result-card {
            background-size: cover;
            background-position: center;
            padding: 10px 15px 5px 15px;
            border-radius: 10px;
            color: black;
            border: 1px solid #ddd;
            border-radius: 6px;
        }
        .preview-image {
            width: 100%;
            height: 200px;
            object-fit: cover;
            border-radius: 8px;
            margin: 10px 0;
        }

        #date-picker-demo {
            width: 100%;
             border: 1px solid #aaa;
            border-radius: 5px;
            outline: none;
            text-align: left;
        }

        .mui-btn {
            border: none;
        }
        .btn-cancel {
            color: red;
        }
        .mui-backdrop {
            width: 100%;
            height: 100%;
            position: fixed;
            bottom: 0;
            background: rgba(0, 0, 0, .6);
        }
    </style>
</head>
<body>
    <div class="container">
        <div style="position: sticky; top: 0; padding: 20px 0; background: #fff">
            <h3 class="mb-4">校区识别查询</h3>

            <!-- 筛选条件 -->
            <div class="row g-3">
                <div class="col-12">
                    <select class="form-select from-select-school" id="campusSelect">
                        <option value="">请选择校区</option>
                    </select>
                </div>
                <div class="col-12">
                    <select class="form-select from-select-device" id="cameraSelect" disabled>
                        <option value="">请先选择设备</option>
                    </select>
                </div>


                {literal}
                <div class="col-12">
                    <input id="date-picker-demo"
                        type="text"
                        data-options='{"type":"date","beginYear":"2000"}'
                        class="btn mui-btn mui-btn-block clientAge"
                        lay-key="1"
                        readonly
                        i18n="placeholder11"
                        placeholder="请选择日期"
                    />
                </div>
                {/literal}
                <!--            <div class="col-12">-->
                <!--                <button class="btn btn-primary w-100" onclick="search()">查询</button>-->
                <!--            </div>-->
            </div>
        </div>
        <!-- 结果显示 -->
        <div id="result3" class="d-none">
            <div class="result-card">
                <h5>近7天数据</h5>
                <p>背景数：<span id="result3-one"></span></p>
                <p>学生识别数：<span id="result3-two"></span></p>
                <p>老师识别数：<span id="result3-two2"></span></p>
                <p>陌课数：<span id="result3-three"></span></p>
            </div>
        </div>

        <div id="result1" class="d-none">
            <div class="result-card mt-4">
                <h5>识别结果(学生)</h5>
                <img id="result1-img" src="" class="preview-image">
                <p>识别时间：<span id="result1-time"></span></p>
                <p>识别类型：<span id="result1-type"></span></p>
            </div>
        </div>

        <div id="result4" class="d-none">
            <div class="result-card mt-4">
                <h5>识别结果(老师)</h5>
                <img id="result4-img" src="" class="preview-image">
                <p>识别时间：<span id="result4-time"></span></p>
                <p>识别类型：<span id="result4-type"></span></p>
            </div>
        </div>

        <div id="result5" class="d-none">
            <div class="result-card mt-4">
                <h5>识别结果(陌客)</h5>
                <img id="result5-img" src="" class="preview-image">
                <p>识别时间：<span id="result5-time"></span></p>
                <p>识别类型：<span id="result5-type"></span></p>
            </div>
        </div>

        <div id="result2" class="d-none">
            <div class="result-card mt-4">
                <h5>背景结果</h5>
                <img id="result2-img" src="" class="preview-image">
                <p>识别时间：<span id="result2-time"></span></p>
                <p>识别类型：<span id="result2-type"></span></p>
            </div>
        </div>
        <div>&nbsp;</div>
    </div>


{literal}
    <script>

        // 模拟数据
        const campusData = {/literal} {$schooljson} {literal};
        // 初始化校区选择
        const campusSelect = document.getElementById('campusSelect');
        // console.log(campusData, 'campusData')
        Object.keys(campusData).forEach(campus => {
            const option = document.createElement('option');
            option.value = campusData[campus]['school_id'];
            option.textContent = campusData[campus]['school_shortname'];
            campusSelect.appendChild(option);
        });
        // 校区选择事件
        campusSelect.addEventListener('change', function() {
            const cameraSelect = document.getElementById('cameraSelect');
            cameraSelect.innerHTML = '<option value="">请选择摄像头</option>';
            
            if(this.value) {
                cameraSelect.disabled = false;

                campusData.forEach(val => {
                    // console.log(val, 'val')
                    if (this.value == val.school_id) {
                        val.codedata.forEach(childValue => {
                            const option = document.createElement('option');
                            option.value = childValue.code;
                            option.textContent = childValue.name;
                            cameraSelect.appendChild(option);
                        })
                    }
                })
                // campusData[this.value].forEach(camera => {
                //
                // });
            } else {
                cameraSelect.disabled = true;
            }
        });
        // 搜索功能
        function search() {
            const campus = campusSelect.value;
            const camera = document.getElementById('cameraSelect').value;
            
            if(!campus || !camera) {
                alert('请先选择校区和摄像头');
                return;
            }

            var schoolId = document.getElementById("campusSelect").value;
            var machineCode = document.getElementById("cameraSelect").value;
            var date = document.getElementById('date-picker-demo').value
            // console.log(schoolId)

            // 创建一个新的 XMLHttpRequest 对象
            var xhr = new XMLHttpRequest();
            // 配置请求类型、URL 以及是否异步
            // xhr.open('GET', 'https://api.example.com/data', true);
            xhr.open('GET', '/Heatstart?c=getSchoolMachone&school_id='+schoolId+'&machine_code='+machineCode+'&date='+date, true);

            xhr.send();
            // 设置请求完成后的回调函数
            xhr.onreadystatechange  = function() {
                // console.log(xhr, 'xhr')
                if (xhr.readyState === XMLHttpRequest.DONE) {
                    if (xhr.status === 200) {
                        var respon = JSON.parse(xhr.responseText)

                        var shibieimg = respon.result.shibieimg
                        var shibieteaimg = respon.result.shibieteaimg
                        var shibiemkimg = respon.result.shibiemkimg
                        var backimg = respon.result.backimg
                        var countnum = respon.result.count
                        // console.log(countnum)
                        if (respon.error == 0) {
                            const resultDiv1 = document.getElementById('result1');
                            resultDiv1.classList.remove('d-none');
                            // resultDiv1.style.background = shibieimg.img;
                            document.getElementById('result1-img').src = shibieimg.img;
                            document.getElementById('result1-time').textContent = shibieimg.ctime;
                            document.getElementById('result1-type').textContent = shibieimg.type;

                            const resultDiv4 = document.getElementById('result4');
                            resultDiv4.classList.remove('d-none');
                            // resultDiv1.style.background = shibieteaimg.img;
                            document.getElementById('result4-img').src = shibieteaimg.img;
                            document.getElementById('result4-time').textContent = shibieteaimg.ctime;
                            document.getElementById('result4-type').textContent = shibieteaimg.type;

                            const resultDiv5 = document.getElementById('result5');
                            resultDiv5.classList.remove('d-none');
                            // resultDiv1.style.background = shibieteaimg.img;
                            document.getElementById('result5-img').src = shibiemkimg.img;
                            document.getElementById('result5-time').textContent = shibiemkimg.ctime;
                            document.getElementById('result5-type').textContent = shibiemkimg.type;

                            const resultDiv2 = document.getElementById('result2');
                            resultDiv2.classList.remove('d-none');
                            // resultDiv2.style.background = backimg.img;
                            document.getElementById('result2-img').src = backimg.img;
                            document.getElementById('result2-time').textContent = backimg.ctime;
                            document.getElementById('result2-type').textContent = backimg.type;

                            const resultDiv3 = document.getElementById('result3');
                            resultDiv3.classList.remove('d-none');
                            // resultDiv2.style.background = backimg.img;
                            document.getElementById('result3-one').textContent = countnum.bjcount;
                            document.getElementById('result3-two').textContent = countnum.xycount;
                            document.getElementById('result3-two2').textContent = countnum.teacount;
                            document.getElementById('result3-three').textContent = countnum.mkcount;
                        }
                    } else {
                        console.error('Request failed with status', xhr.status); // 请求失败，处理错误
                    }
                }

                // if (xhr.error >= 200 && xhr.status < 300) {
                //     // 请求成功，处理返回的数据
                //     console.log('Response:', xhr.responseText);
                //

                //
                //
                // } else {
                //     // 请求失败，处理错误
                //     console.error('Request failed with status:', xhr.status);
                // }
            };

            xhr.onerror = function() {
                console.error('Request failed');
            };
            // // 模拟结果数据
            // const results = {
            //     image: 'https://via.placeholder.com/300x200',
            //     time: new Date().toLocaleString(),
            //     type: Math.random() > 0.5 ? '学生' : '陌客',
            //     background: 'linear-gradient(to right, #4facfe, #00f2fe)'
            // };
            //
            // // 显示结果
            // const resultDiv = document.getElementById('result');
            // resultDiv.classList.remove('d-none');
            // resultDiv.style.background = results.background;
            // document.getElementById('time').textContent = results.time;
            // document.getElementById('type').textContent = results.type;
        }


        $(document).ready(function () {
            //教材检索
           $('.from-select-school').select2().change(function() {

               $('#result1').addClass('d-none')
               $('#result2').addClass('d-none')
               $('#result3').addClass('d-none')

               var schoolId = $(this).val()
               var schoolList = campusData.filter(val => val.school_id == schoolId)[0]['codedata']

               var html = '<option value="">请先选择设备</option>'

               schoolList.forEach(val => {
                   var item = '<option value="'+ val.code +'">'+ val.name +'</option>'
                    html += item
               })
               $('.from-select-device').html(html).select2().attr('disabled', false).change(function () {
                   search()
               })
           })
        })
    </script>

    <script>
        // mui 日期选择器
        (function ($) {
            $.init();
            // var result = $("#result")[0];
            var btns = $(".btn");

            btns.each(function (i, btn) {
                btn.addEventListener(
                    "tap",
                    function () {
                        var optionsJson = this.getAttribute("data-options") || "{}";
                        var options = JSON.parse(optionsJson);
                        var id = this.getAttribute("id");
                        var picker = new $.DtPicker(options);
                        picker.show(function (rs) {
                            document.getElementById(id).value = rs.text;
                            var machineCode = document.getElementById("cameraSelect").value;
                            if (machineCode) {
                                jQuery('.mask').removeClass('d-none')
                                search()
                            }
                        });
                    },
                    false
                );
            });
        })(mui);

        // 解决 input失焦后页面返回顶部的问题
        $("input").on("blur", function () {
            $("body").removeClass("mui-focusin");
        });
    </script>

    {/literal}

</body>
</html>