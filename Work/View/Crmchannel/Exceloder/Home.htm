<!DOCTYPE HTML>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    {include file="key.htm"}
</head>

<body>

<div class="page-container row condensed">
    {include file="page-sidebar.htm"}
    {include file="footer.htm"}
    <div class="page-content">
        {include file="header.htm"}
        <div class="mx20 breadcrumb-w">
            <div class="breadcrumb fl">
                <span>批次管理</span>
            </div>
            <a href="javascript:" class="fr bakFromurl mt6"><i class="icon-back mr5"></i>返回</a>
            <div class="clear"></div>
        </div>
        <div class="content">
            <div id="container">
                <div class="row">
                    <div class="bg-f">
                        <div class="p20 f14">
                            <div class="form-group">
                                <form action="/{$u}" method="get" accept-charset="utf-8">
                                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                        <tr>
                                            <td width="5%">搜索：</td>
                                            <td width="20%" class="pr10"><input name="keyword" class="form-control input-sm" placeholder="请输入批次编号" value="{$datatype['keyword']}" type="text"></td>
                                            <td width="50%">
                                                <button type="submit" id="FromSubmit" class="btn btn-primary ml10"><span class="glyphicon glyphicon-search c-f"></span> 搜索</button>

                                                <a href="/{$u}/Import?channel_id={$channelname.channel_id}" class="btn btn-success dropdown-toggle btn-demo-space fr">+导入数据</a>
                                            </td>
                                        </tr>
                                    </table>
                                </form>
                            </div>
                            <form role="form" action="/{$u}?c=batchWork" method="post" enctype="application/x-www-form-urlencoded" class="AjaxForm">
                                <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table tc table-hover table-bordered">
                                    <thead>
                                    <tr>
                                        <th>渠道名称</th>
                                        <th>批次编号</th>
                                        <th>导入时间</th>
                                        <th>是否全部检测</th>
                                        <th>操作</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    {if $dataList}
                                    {foreach from=$dataList item=dataVar}
                                    <tr id="list-{$dataVar.exceloder_id}">
                                        <td>{$channelname.channel_name}</td>
                                        <td>{$dataVar.exceloder_pid}</td>
                                        <td>{$dataVar.exceloder_createtime|date_format:'%Y-%m-%d %H:%M:%S'}</td>
                                        <td>{if $dataVar.exceloder_status == '0'}<span class="glyphicon glyphicon-remove-circle f18 text-danger"></span>
                                            {elseif $dataVar.exceloder_status == '1'}<span class="glyphicon glyphicon-remove-circle f18 text-danger"></span>
                                            {elseif $dataVar.exceloder_status == '2'}<span class="glyphicon glyphicon-ok-circle f18 text-primary"></span>
                                            {/if}
                                        </td>
                                        <td align="left">
                                            <a href="/{$u}/Roster?exceloder_pid={$dataVar.exceloder_pid}" class="btn btn-primary btn-sm">名单查看</a>
                                            {if  $dataVar.exceloder_status == '2'}
                                            <a href="/{$u}?c=ExportRoster&exceloder_pid={$dataVar.exceloder_pid}" class="btn btn-primary btn-sm">下载未撞单名单</a>
                                            {/if}
                                            <a  href="javascript:;" data-element="list-{$dataVar.exceloder_id}" data-url="/{$u}?c=delRoster&exceloder_id={$dataVar.exceloder_id}&exceloder_pid={$dataVar.exceloder_pid}" class="btn btn-danger btn-sm btn-del-action"><span class="glyphicon glyphicon-remove c-f"></span> 删除</a>
                                        </td>
                                    </tr>
                                    {/foreach}
                                    {/if}
                                    </tbody>
                                </table>
                            </form>
                            <div class="pagemenu">{$pagelist}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{include file="jscom.htm"}
<script src="{$PluginsUrl}jquery-form/jquery.form.js" type="text/javascript"></script>
<script src="{$JsUrl}int.form.js" type="text/javascript"></script>
</body>
</html>
