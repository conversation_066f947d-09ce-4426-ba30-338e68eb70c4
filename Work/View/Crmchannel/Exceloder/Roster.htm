<!DOCTYPE HTML>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    {include file="key.htm"}
</head>

<body>

<div class="page-container row condensed">
    {include file="page-sidebar.htm"}
    {include file="footer.htm"}
    <div class="page-content">
        {include file="header.htm"}
        <div class="mx20 breadcrumb-w">
            <div class="breadcrumb fl">
                <span>批次管理->批次：{$datatype.exceloder_pid}</span>
            </div>
            <a href="javascript:" class="fr bakFromurl mt6"><i class="icon-back mr5"></i>返回</a>
            <div class="clear"></div>
        </div>
        <div class="content">
            <div id="container">
                <div class="row">
                    <div class="bg-f">
                        <div class="p20 f14">
                            <div class="form-group">
                                <form action="/{$u}/Roster" method="get" accept-charset="utf-8">
                                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                        <tr>
                                            <td width="5%">搜索：</td>
                                            <td width="15%" class="pr10"><input name="keyword" class="form-control input-sm" placeholder="请输入手机号" value="{$datatype['keyword']}" type="text"></td>
                                            <td width="50%">
                                                <button type="submit" id="FromSubmit" class="btn btn-primary ml10"><span class="glyphicon glyphicon-search c-f"></span> 搜索</button>
                                            </td>
                                        </tr>
                                    </table>
                                </form>
                            </div>
                            <form role="form" action="/{$u}?c=batchWork" method="post" enctype="application/x-www-form-urlencoded" class="AjaxForm">
                                <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table tc table-hover table-bordered">
                                    <thead>
                                    <tr>
                                        <th>渠道名称</th>
                                        <th>批次编号</th>
                                        <th>校验手机</th>
                                        <th>校验状态</th>
                                        <th>是否撞单</th>
                                        <th>撞单原因</th>
                                        <th>校验完成时间</th>
                                        <th>导入时间</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    {if $dataList}
                                    {foreach from=$dataList item=dataVar}
                                    <tr id="list-{$dataVar.school_id}">
                                        <td>{$dataVar.channel_name}</td>
                                        <td>{$dataVar.exceloder_pid}</td>
                                        <td>{$dataVar.roster_mobile}</td>
                                        <td>{if $dataVar.roster_status == '0'}<span class="glyphicon glyphicon-remove-circle f18 text-danger"></span>
                                            {elseif  $dataVar.roster_status == '1'}<span class="glyphicon glyphicon-ok-circle f18 text-primary"></span>
                                            {/if}
                                        </td>
                                        <td>{if $dataVar.roster_isexceltocrm == '0'}<span class="glyphicon glyphicon-remove-circle f18 text-danger"></span>
                                            {elseif  $dataVar.roster_isexceltocrm == '1'}<span class="glyphicon glyphicon-ok-circle f18 text-primary"></span>
                                            {/if}
                                        </td>
                                        <td>{$dataVar.roster_note}</td>
                                        <td>{if $dataVar.roster_completetime}{$dataVar.roster_completetime|date_format:'%Y-%m-%d %H:%M:%S'}{else}--{/if}</td>
                                        <td>{if $dataVar.roster_createtime}{$dataVar.roster_createtime|date_format:'%Y-%m-%d %H:%M:%S'}{else}--{/if}</td>
                                    </tr>
                                    {/foreach}
                                    {/if}
                                    </tbody>
                                </table>
                            </form>
                            <div class="pagemenu">{$pagelist}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{include file="jscom.htm"}
<script src="{$PluginsUrl}jquery-form/jquery.form.js" type="text/javascript"></script>
<script src="{$JsUrl}int.form.js" type="text/javascript"></script>
</body>
</html>
