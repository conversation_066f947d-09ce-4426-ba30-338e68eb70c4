<!DOCTYPE HTML>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    {include file="key.htm"}
</head>

<body>

<div class="page-container row condensed">
    {include file="page-sidebar.htm"}
    <div class="footer-widget">
        <p align="" class="f16"><a class="cp c-9"><span class="glyphicon glyphicon-off"></span></a></p>
    </div>
    <div class="page-content">
        {include file="header.htm"}
        <div class="mx20 breadcrumb-w">
            <div class="breadcrumb fl">
                <span>个人信息管理</span>
                <span>&gt; 密码信息修改</span> </li>
            </div>
            <a href="javascript:" class="fr bakFromurl mt6"><i class="icon-back mr5"></i>返回</a>
            <div class="clear"></div>
        </div>
        <div class="content">
            <div id="container">
                <div class="row">
                    <div class="bg-f row py20">
                        <form action="/{$u}?c={$act}" role="form" method="post" enctype="application/x-www-form-urlencoded" class="AjaxForm form-horizontal">
                            <div class="col-md-6" id="Form-Box-Operating">
                                <div class="py20 f14">
                                    <div class="form-group">
                                        <div class="row mb20">
                                            <label for="user_pswd" class="col-sm-2 control-label">原密码</label>
                                            <div class="col-sm-4">
                                                <input name="user_pswd" id="user_pswd" value="" type="text" class="form-control" placeholder="请输入原密码">
                                            </div>
                                        </div>
                                        <div class="row">
                                            <label for="user_pass" class="col-sm-2 control-label">新密码</label>
                                            <div class="col-sm-4">
                                                <input name="user_pass" id="user_pass" type="text" class="form-control" placeholder="请输入新密码">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-sm-4 col-sm-offset-2">
                                        <button type="submit" class=" btn btn-primary btn-cons">提交保存</button>
                                        <button type="button" class="btn btn-default ml10 bakFromurl">返回</button></span>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{include file="jscom.htm"}
<script src="{$PluginsUrl}jquery-form/jquery.form.js" type="text/javascript"></script>
<script src="{$JsUrl}int.form.js" type="text/javascript"></script>
</body>
</html>
