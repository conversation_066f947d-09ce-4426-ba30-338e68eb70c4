.c-aaa {
  color: #aaa;
}

.c-333 {
  color: #333;
}

.f14 {
  font-size: 14px;
}

.paging-box {
  display: flex;
  align-items: center;
  justify-content: center;

  .paging {
    display: flex;
    align-items: center;
    justify-content: center;

    a {
      padding: 3px 4px;
      margin-right: 5px;
      font-size: 18px;
      border: 2px solid transparent;

      &:last-child {
        margin-right: 0;
      }

      &:hover,
      &.active {
        border-bottom: 2px solid #3CA3FF;
      }

      &.next {

        &:hover,
        &.active {
          border-bottom: 2px solid transparent;
        }
      }
    }
  }
}

.news-page {

  // 新闻banner
  .news-banner {
    padding-top: 50px;
    // height: 650px;
    position: relative;

    .banner-item {
      // height: 100%;
      // width: 1920px;
      // position: absolute;
      // top: 0;
      // left: 50%;
      // transform: translateX(-50%);
      // background: url(../images/page-news/banner.png) center top no-repeat;
      background-size: auto 100%;
      background-color: #f8fbfe;

      img {
        width: 100%;
        max-width: 1920px;
      }
    }
    .btn-contact {
      width: 100%;
      position: absolute;
      left: 0%;
      bottom: 20%;
      .layui-btn {
        display: block;
        margin-left: 15%;
      }
      a {
        display: block;
        color: #fff;
      }
    }
  }

  // 新闻列表
  .news-list {
    max-width: 1100px;
    margin: auto;
    padding: 0 0 70px 0;

    ul {
      padding: 60px 0;

      a {
        display: flex;
        background-color: #FAFAFA;

        .imgs {
          width: 267px;
          height: 267px;
        }

        .conts {
          display: flex;
          padding: 30px;
          flex-direction: column;
          justify-content: space-between;
          line-height: 28px;
        }

        strong {
          font-size: 22px;
          color: #333;
        }

        .desc {
          line-height: 19px;

        }

        .title {}
      }
    }
  }

  // 新闻详情
  .news-detail {
    max-width: 1100px;
    margin: auto;
    padding: 50px 20px 70px;

    h1 {
      font-size: 36px;
      color: #333;
    }

    .time {
      color: #aaa;
      font-size: 14px;
    }

    .conts {
      color: #333;
      line-height: 24px;
      font-size: 14px;

      img {
        display: block;
        max-width: 100%;
        margin: 20px auto;
      }
    }
    .tit{
      font-size:18px;
      color:rgba(0,109,223,1);
      line-height:24px;
      text-align: center;
    }

    .back {
      display: flex;
      align-items: center;
      color: #006DDF;
      font-size: 14px;
      font-weight: bold;

      em {
        width: 26px;
        height: 26px;
        margin-right: 6px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
        border-radius: 50%;
        color: #fff;
        background-color: #3CA3FF;
      }
    }
  }

  // 联系我们
  .connect-us {
    max-width: 1200px;
    margin: 0 auto;

    .connect-info {
      padding: 60px 20px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      border-bottom: 1px solid #ddd;

      .item {
        display: flex;
        align-items: center;

        .icon {
          width: 50px;
          height: 50px;
          background-color: red;
        }

        .conts {
          a {
            color: #006DDF;
            font-size: 36px;
          }

          .time {
            color: #aaa;
            font-size: 14px;
          }
        }

        &:first-child {
          .conts {
            a {
              color: #333;
            }
          }
        }
      }
    }

    .connet-address {
      padding: 60px 0 70px;

      .title {
        font-size: 36px;
        color: #333;
      }

      .address-detail {
        display: flex;
        align-items: center;

        .map {
          width: 493px;
          height: 431px;
          background-color: #f5f5f5;
        }

        .company-detail {
          flex: 1;
          display: flex;
          align-items: center;

          ul {

            li {
              margin-bottom: 30px;

              p {
                margin-bottom: 10px;
                color: #333;
                font-size: 18px;
              }

              span {
                color: #aaa;
                font-size: 14px;
              }
            }

            &:first-child {
              flex: 3;
              padding-left: 50px;
              border-right: 1px solid #ddd;
            }

            &:last-child {
              flex: 2;
              padding-left: 106px;
              border-right: none;
            }
          }
        }
      }
    }
  }
}