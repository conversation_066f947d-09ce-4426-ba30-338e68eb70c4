<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <meta
            name="viewport"
            content="width=device-width, initial-scale=1, maximum-scale=1"
    />
    <title>课叮当品牌官网</title>
    <meta name="keywords" content="课叮当品牌官网" />
    {include file='css.htm'}

</head>

<body class="body-color">
<!--首页-->
<div class="page-comon-box news-page">
    <!-- 头部*******开始 -->
    {include file='./header.htm'}

    <div class="news-banner">
        <div class="banner-item">
            <img src="{$ImgUrl}page-news/banner.png" alt="" />
        </div>
        <div class="btn-contact">
            <button class="layui-btn layui-btn-normal">
                <a href="">立即咨询</a>
            </button>
        </div>
    </div>

    <div class="connect-us">
        <div class="connect-info">
            <div class="item">
                <div class="icon mr20"></div>
                <div class="conts">
                    <a href="javascript:;">24小时客服热线</a>
                    <p class="time mt10">免费咨询帮助</p>
                </div>
            </div>
            <div class="item">
                <div class="conts">
                    <a href="tel:************">00-888-5288</a>
                    <p class="time mt10">(周一至周日 8:30–6:00)</p>
                </div>
            </div>
            <div class="item">
                <div class="conts">
                    <a href="tel:************">00-888-5288</a>
                    <p class="time mt10">(周一至周日 8:30—0:00)</p>
                </div>
            </div>
        </div>

        <div class="connet-address">
            <p class="title mb20">公司地址</p>
            <div class="address-detail">
                <div class="map" id="map"></div>
                <div class="company-detail">
                    <ul>
                        <li>
                            <p>上海总部</p>
                            <span>上海市普陀区新村路423弄2号口1006室</span>
                        </li>
                        <li>
                            <p>客服热线</p>
                            <span>************</span>
                        </li>
                        <li>
                            <p>售前热线</p>
                            <span>************</span>
                        </li>
                        <li>
                            <p>值班热线</p>
                            <span>13378983745/18676573547</span>
                        </li>
                    </ul>
                    <ul>
                        <li>
                            <p>招贤纳士</p>
                            <span><EMAIL></span>
                        </li>
                        <li>
                            <p>客服热线</p>
                            <span>************</span>
                        </li>
                        <li>
                            <p>售前热线</p>
                            <span>************</span>
                        </li>
                        <li>
                            <p>值班热线</p>
                            <span>13378983745/18676573547</span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
<!--页面加载的js-->
{include file='json.htm'}
<script
        type="text/javascript"
        src="http://api.map.baidu.com/api?v=2.0&ak=aYGx2p9vyoFVh0KMI0Ds79eNSqCxPUO4"
></script>
<script>
    //表单提交 图片验证码
    $(function() {
        // 百度地图API功能
        function changepoint() {
            // 百度地图API功能
            var map = new BMap.Map("map"); // 创建Map实例
            var temp = map.centerAndZoom(
                new BMap.Point(121.451722, 31.244071),
                19
            ); // 初始化地图,设置中心点坐标和地图级别
            //添加地图类型控件
            map.addControl(
                new BMap.MapTypeControl({
                    mapTypes: [BMAP_NORMAL_MAP, BMAP_HYBRID_MAP]
                })
            );
            map.enableScrollWheelZoom(true); //开启鼠标滚轮缩放

            var point = new BMap.Point(121.451722, 31.244071);
            var marker = new BMap.Marker(point); // 创建标注
            map.addOverlay(marker); // 将标注添加到地图中
            map.centerAndZoom(point, 18);
            var opts = {
                width: 200, // 信息窗口宽度
                height: 100, // 信息窗口高度
                title: "吉的堡", // 信息窗口标题
                enableMessage: true, //设置允许信息窗发送短息
                message: "吉的堡"
            };
            var infoWindow = new BMap.InfoWindow(
                "上海市嘉定区金沙江西路1069号11层",
                opts
            ); // 创建信息窗口对象
            marker.addEventListener("click", function() {
                map.openInfoWindow(infoWindow, point); //开启信息窗口
            });
        }
        changepoint();

        $(window).resize(function() {
            changepoint();
        });
    });
</script>

</body>
</html>
