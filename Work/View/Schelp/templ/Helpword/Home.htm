<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <meta
            name="viewport"
            content="width=device-width, initial-scale=1, maximum-scale=1"
    />
    <title>课叮铛常见问题</title>
    <meta name="keywords" content="课叮铛"/>
    {include file='css.htm'}

</head>
<body class="body-color">
<!--首页-->
<div class="help-page page-comon-box">
    <!-- 头部*******开始 -->
    {include file='./header.htm'}
    <!-- 头部*******结束 -->
    <!-- 主体内容 -->
    <div class="body">
        <div class="content">
            {include file='./top.htm'}
            <!-- 培训手册 -->
            <div class="modal-cont training-manual">
                <form action="/Helpword/Home" method="get">
                    <div class="cont">
                        <div class="search">
                            <input
                                    type="text"
                                    name="keyword"
                                    placeholder="搜索你想要的帮助"
                                    autocomplete="off"
                                    class="layui-input"
                                    value ="{$datatype.keyword}"
                            />
                            <i class="layui-icon layui-icon-search"></i>
                        </div>
                        <div class="layui-tab layui-tab-brief middle" lay-filter="docDemoTabBrief" >
                            <div class="left">
                                {if $SchmoList}
                                <ul class="layui-tab-title">
                                    {foreach from=$SchmoList item=DataVar}
                                    <p class="title">{$DataVar.0.module_name}</p>
                                    {foreach from=$DataVar item=itemVar key=key }
                                    {if $key>0}
                                    <li {if $key==1 && $itemVar.module_class==1 }class="layui-this"{/if}>{$itemVar.module_name}</li>
                                    {/if}
                                    {/foreach}
                                    {/foreach}
                                </ul>
                                {/if}
                            </div>
                            <div class="layui-tab-content right">
                                {if $SchmoList}
                                {foreach from=$SchmoList item=DataVar}
                                {foreach from=$DataVar item=itemVar key=k}
                                {if $k>0}
                                <div class="layui-tab-item  {if $k==1 && $itemVar.module_class==1 }layui-show{/if}">

                                    <div class="top-item mb10">
                                        <div class="sm-t"> 培训手册 > {$DataVar.0.module_name} > {$itemVar.module_name}</div>
                                        <div class="tips">
                                            <i class="layui-icon layui-icon-help"></i>本页常见问题
                                        </div>
                                    </div>

                                    <!-- 搜索显示 -->
                                    <div class="top-item mb10 font-333 h2 none">
                                        共为您找到了5条结果
                                    </div>
                                    <ul class="tab-cont">
                                        {if $itemVar.child_array}
                                        {foreach from=$itemVar.child_array item=arrayVar key=key }
                                        <li>
                                            <p>{$key+1}、{$arrayVar.handbook_name}</p>
                                            <ul class="detail">
                                                <li>
                                                    {$arrayVar.handbook_note}
                                                </li>
                                            </ul>
                                        </li>
                                        {/foreach}
                                        {/if}
                                    </ul>
                                </div>
                                {/if}
                                {/foreach}
                                {/foreach}
                                {/if}
                                <!--<div class="layui-tab-item layui-show">-->
                                <!--<div class="top-item mb10">-->
                                <!--<div class="sm-t">培训手册 > 校务管理系统 > 课务管理</div>-->
                                <!--<div class="tips">-->
                                <!--<i class="layui-icon layui-icon-help"></i>本页常见问题-->
                                <!--</div>-->
                                <!--</div>-->
                                <!--&lt;!&ndash; 搜索显示 &ndash;&gt;-->
                                <!--<div class="top-item mb10 font-333 h2 none">-->
                                <!--共为您找到了5条结果-->
                                <!--</div>-->
                                <!--<ul class="tab-cont">-->
                                <!--<li>-->
                                <!--<p>1、如何进行课表排课</p>-->
                                <!--<ul class="detail">-->
                                <!--<li>-->
                                <!--点击课表操作里“修改排课”进入修改排课界面。排课支持多维度修改，修改完毕后点击确认保存-->
                                <!--</li>-->
                                <!--</ul>-->
                                <!--</li>-->
                                <!--<li>-->
                                <!--<p>2、如何修改排课</p>-->
                                <!--<ul class="detail">-->
                                <!--<li></li>-->
                                <!--</ul>-->
                                <!--</li>-->
                                <!--</ul>-->
                                <!--</div>-->
                                <!--<div class="layui-tab-item">内容2</div>-->
                                <!--<div class="layui-tab-item">内容3</div>-->
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <!-- 底部*******开始 -->
    <div class="footer">
        <div class="layui-row">
            <div class="layui-col-xs12 layui-col-sm12 layui-col-md6 left">
                <div class="layui-row">
                    <div class="layui-col-xs12 layui-col-sm12 layui-col-md6">
                        <p class="tit">公司</p>
                        <ul>
                            <li>案例展示</li>
                            <li>新闻动态</li>
                            <li>团队介绍</li>
                        </ul>
                    </div>
                    <div class="layui-col-xs12 layui-col-sm12 layui-col-md6">
                        <p class="tit">公司产品</p>
                        <ul>
                            <li>课叮铛园务系统</li>
                            <li>课叮铛校务系统</li>
                            <li>客服投诉管理系统</li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="layui-col-xs12 layui-col-sm12 layui-col-md6">
                <div class="layui-row">
                    <div class="layui-col-xs12 layui-col-sm12 layui-col-md6">
                        <p class="tit">联系方式</p>
                        <ul>
                            <li><span class="phone">021-60345123</span></li>
                            <li>上海市普陀区新村路423号2幢1006室</li>
                        </ul>
                    </div>
                    <div class="layui-col-xs12 layui-col-sm12 layui-col-md6">
                        <div class="wechat">
                            <img src="{$ImgUrl}code-img1.png"/>
                            <p>企业官微</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!--页面加载的js-->
{include file='json.htm'}
</body>
</html>
