<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <meta
            name="viewport"
            content="width=device-width, initial-scale=1, maximum-scale=1"
    />
    <title>课叮铛品牌官网</title>
    <meta name="keywords" content="课叮铛品牌官网" />
    {include file='css.htm'}
</head>
<body class="body-color">
<!--首页-->
<div class="solution-detail-page page-comon-box">
    <!-- 头部*******开始 -->
    {include file='header.htm'}
    <!-- 头部*******结束 -->
    <!-- 主体内容 -->
    <div class="body">
        <div class="solution-banner content content1">
            <div class="banner-item">
                <img src="{$ImgUrl}page-news/banner.png" alt="" />
                <div class="btn-contact">
                    <button class="layui-btn layui-btn-normal">
                        <a href="">立即试用</a>
                    </button>
                </div>
            </div>
            <div class="top-logo">
                <div class="layui-row">
                    <div class="layui-col-xs12 layui-col-sm6 layui-col-md3">
                        <div class="col-cont">
                            <img src="{$ImgUrl}top-logo1.png" />
                            <p class="cont">申请免费试用，体验智能园所</p>
                        </div>
                    </div>
                    <div class="layui-col-xs12 layui-col-sm6 layui-col-md3">
                        <div class="col-cont">
                            <img src="{$ImgUrl}top-logo1.png" />
                            <p class="cont">申请免费试用，体验智能园所</p>
                        </div>
                    </div>
                    <div class="layui-col-xs12 layui-col-sm6 layui-col-md3">
                        <div class="col-cont">
                            <img src="{$ImgUrl}top-logo1.png" />
                            <p class="cont">申请免费试用，体验智能园所</p>
                        </div>
                    </div>
                    <div class="layui-col-xs12 layui-col-sm6 layui-col-md3">
                        <div class="col-cont">
                            <img src="{$ImgUrl}top-logo1.png" />
                            <p class="cont">申请免费试用，体验智能园所</p>
                        </div>
                    </div>
                </div>
                <div class="line"></div>
            </div>
        </div>
        <div class="content content2">
            <div class="top-theme">
                <div class="tit-bg">01</div>
                <p class="tit">招生用叮当秀，招生方案炫翻天</p>
            </div>
            <div class="modal-contain">
                <ul class="solution-list clearFloat">
                    <li>
                        <a href="javascript:;">
                            <div class="top mb20">
                                <div><i class="layui-icon layui-icon-read"></i></div>
                                <div class="ml10">
                                    <p class="big-tit h3 font-333">微信互动嗨翻天</p>
                                </div>
                            </div>
                            <div class="cont text-1 font-333">
                                利用微信平台用户群渗透率较高的特点，开发了包括单不限于h5小游戏，小活动等小应用。
                                让家长与孩子在游戏或者活动的互动过程中感受您的园所的教学魅力。充分提高招生的成功率
                            </div>
                            <ul class="tag-list">
                                <li><span class="icon"></span>h5游戏</li>
                                <li><span class="icon"></span>微信活动页</li>
                                <li><span class="icon"></span>个性化招生互动</li>
                            </ul>
                        </a>
                    </li>
                    <li>
                        <a href="javascript:;">
                            <div class="top mb20">
                                <div><i class="layui-icon layui-icon-read"></i></div>
                                <div class="ml10">
                                    <p class="big-tit h3 font-333">微信互动嗨翻天</p>
                                </div>
                            </div>
                            <div class="cont text-1 font-333">
                                利用微信平台用户群渗透率较高的特点，开发了包括单不限于h5小游戏，小活动等小应用。
                                让家长与孩子在游戏或者活动的互动过程中感受您的园所的教学魅力。充分提高招生的成功率
                            </div>
                            <ul class="tag-list">
                                <li><span class="icon"></span>h5游戏</li>
                                <li><span class="icon"></span>微信活动页</li>
                                <li><span class="icon"></span>个性化招生互动</li>
                            </ul>
                        </a>
                    </li>
                    <li>
                        <a href="javascript:;">
                            <div class="top mb20">
                                <div><i class="layui-icon layui-icon-read"></i></div>
                                <div class="ml10">
                                    <p class="big-tit h3 font-333">微信互动嗨翻天</p>
                                </div>
                            </div>
                            <div class="cont text-1 font-333">
                                利用微信平台用户群渗透率较高的特点，开发了包括单不限于h5小游戏，小活动等小应用。
                                让家长与孩子在游戏或者活动的互动过程中感受您的园所的教学魅力。充分提高招生的成功率
                            </div>
                            <ul class="tag-list">
                                <li><span class="icon"></span>h5游戏</li>
                                <li><span class="icon"></span>微信活动页</li>
                                <li><span class="icon"></span>个性化招生互动</li>
                            </ul>
                        </a>
                    </li>
                    <li>
                        <a href="javascript:;">
                            <div class="top mb20">
                                <div><i class="layui-icon layui-icon-read"></i></div>
                                <div class="ml10">
                                    <p class="big-tit h3 font-333">微信互动嗨翻天</p>
                                </div>
                            </div>
                            <div class="cont text-1 font-333">
                                利用微信平台用户群渗透率较高的特点，开发了包括单不限于h5小游戏，小活动等小应用。
                                让家长与孩子在游戏或者活动的互动过程中感受您的园所的教学魅力。充分提高招生的成功率
                            </div>
                            <ul class="tag-list">
                                <li><span class="icon"></span>h5游戏</li>
                                <li><span class="icon"></span>微信活动页</li>
                                <li><span class="icon"></span>个性化招生互动</li>
                            </ul>
                        </a>
                    </li>
                </ul>
                <a href="">立即体验叮当秀>></a>
            </div>
        </div>
        <div class="content content3">
            <div class="top-theme">
                <div class="tit-bg">02</div>
                <p class="tit">新时代招生，流量转化是关键</p>
            </div>
            <div class="modal-contain">
                <div class="left">
                    <p class="tit">用户面临的问题与挑战</p>
                    <div class="cont">
                        <p><span class="icon"></span>新时代招生，招生人员成本高，学校投入大</p>
                        <p><span class="icon"></span>同行同业纷纷抢占市场，市场环境竞争压力大</p>
                        <p><span class="icon"></span>互联网行业爆发性增长，传统招生方式用户渗透率低</p>
                        <p><span class="icon"></span>招生转化率低，招生后期客户难以维护，学员流失率较高</p>
                    </div>
                    <p class="tit">场景解决方案</p>
                    <div class="cont">
                        <p><span class="icon"></span>充分利用微信用户渗透率高的特点，直达目标用户群体</p>
                        <p><span class="icon"></span>开发具有趣味性与互动性的小游戏、微网页等产品，使家长以及孩子充分融入教学场景之中，在互动中完成招生的转化</p>
                        <p><span class="icon"></span>校园微官网模板的设计，让每一个传统的教育机构都能够快速拥有自己的官网网页。能够为自身品牌宣传奠定良好的基础</p>
                        <p><span class="icon"></span>我们会根据招生季推出一系列在线宣传新方式，包括单不限于微游戏、微活动、微传单等模式。充分利用互联网的平台， 多渠道、多平台进行品牌的传播以及宣传。充分让家长了解自身品牌，实现招生的转化</p>
                    </div>
                </div>
                <div class="right">
                    <img src="{$ImgUrl}solution/detail-img.png">
                </div>
            </div>
        </div>
    </div>
    <!-- 底部*******开始 -->
    {include file='footer.htm'}
</div>

<!--页面加载的js-->
{include file='json.htm'}
</body>
</html>
