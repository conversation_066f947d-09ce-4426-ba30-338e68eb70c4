.c-aaa {
  color: #aaa;
}
.c-333 {
  color: #333;
}
.f14 {
  font-size: 14px;
}
.paging-box {
  display: flex;
  align-items: center;
  justify-content: center;
}
.paging-box .paging {
  display: flex;
  align-items: center;
  justify-content: center;
}
.paging-box .paging a {
  padding: 3px 4px;
  margin-right: 5px;
  font-size: 18px;
  border: 2px solid transparent;
}
.paging-box .paging a:last-child {
  margin-right: 0;
}
.paging-box .paging a:hover,
.paging-box .paging a.active {
  border-bottom: 2px solid #3CA3FF;
}
.paging-box .paging a.next:hover,
.paging-box .paging a.next.active {
  border-bottom: 2px solid transparent;
}
.news-page .news-banner {
  padding-top: 50px;
  position: relative;
}
.news-page .news-banner .banner-item {
  background-size: auto 100%;
  background-color: #f8fbfe;
}
.news-page .news-banner .banner-item img {
  width: 100%;
  max-width: 1920px;
}
.news-page .news-banner .btn-contact {
  width: 100%;
  position: absolute;
  left: 0%;
  bottom: 20%;
}
.news-page .news-banner .btn-contact .layui-btn {
  display: block;
  margin-left: 15%;
}
.news-page .news-banner .btn-contact a {
  display: block;
  color: #fff;
}
.news-page .news-list {
  max-width: 1100px;
  margin: auto;
  padding: 0 0 70px 0;
}
.news-page .news-list ul {
  padding: 60px 0;
}
.news-page .news-list ul a {
  display: flex;
  background-color: #FAFAFA;
}
.news-page .news-list ul a .imgs {
  width: 267px;
  height: 267px;
}
.news-page .news-list ul a .conts {
  display: flex;
  padding: 30px;
  flex-direction: column;
  justify-content: space-between;
  line-height: 28px;
}
.news-page .news-list ul a strong {
  font-size: 22px;
  color: #333;
}
.news-page .news-list ul a .desc {
  line-height: 19px;
}
.news-page .news-detail {
  max-width: 1100px;
  margin: auto;
  padding: 50px 20px 70px;
}
.news-page .news-detail h1 {
  font-size: 36px;
  color: #333;
}
.news-page .news-detail .time {
  color: #aaa;
  font-size: 14px;
}
.news-page .news-detail .conts {
  color: #333;
  line-height: 24px;
  font-size: 14px;
}
.news-page .news-detail .conts img {
  display: block;
  max-width: 100%;
  margin: 20px auto;
}
.news-page .news-detail .tit {
  font-size: 18px;
  color: #006ddf;
  line-height: 24px;
  text-align: center;
}
.news-page .news-detail .back {
  display: flex;
  align-items: center;
  color: #006DDF;
  font-size: 14px;
  font-weight: bold;
}
.news-page .news-detail .back em {
  width: 26px;
  height: 26px;
  margin-right: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  border-radius: 50%;
  color: #fff;
  background-color: #3CA3FF;
}
.news-page .connect-us {
  max-width: 1200px;
  margin: 0 auto;
}
.news-page .connect-us .connect-info {
  padding: 60px 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #ddd;
}
.news-page .connect-us .connect-info .item {
  display: flex;
  align-items: center;
}
.news-page .connect-us .connect-info .item .icon {
  width: 50px;
  height: 50px;
  background-color: red;
}
.news-page .connect-us .connect-info .item .conts a {
  color: #006DDF;
  font-size: 36px;
}
.news-page .connect-us .connect-info .item .conts .time {
  color: #aaa;
  font-size: 14px;
}
.news-page .connect-us .connect-info .item:first-child .conts a {
  color: #333;
}
.news-page .connect-us .connet-address {
  padding: 60px 0 70px;
}
.news-page .connect-us .connet-address .title {
  font-size: 36px;
  color: #333;
}
.news-page .connect-us .connet-address .address-detail {
  display: flex;
  align-items: center;
}
.news-page .connect-us .connet-address .address-detail .map {
  width: 493px;
  height: 431px;
  background-color: #f5f5f5;
}
.news-page .connect-us .connet-address .address-detail .company-detail {
  flex: 1;
  display: flex;
  align-items: center;
}
.news-page .connect-us .connet-address .address-detail .company-detail ul li {
  margin-bottom: 30px;
}
.news-page .connect-us .connet-address .address-detail .company-detail ul li p {
  margin-bottom: 10px;
  color: #333;
  font-size: 18px;
}
.news-page .connect-us .connet-address .address-detail .company-detail ul li span {
  color: #aaa;
  font-size: 14px;
}
.news-page .connect-us .connet-address .address-detail .company-detail ul:first-child {
  flex: 3;
  padding-left: 50px;
  border-right: 1px solid #ddd;
}
.news-page .connect-us .connet-address .address-detail .company-detail ul:last-child {
  flex: 2;
  padding-left: 106px;
  border-right: none;
}
html,
body {
  background-color: #f3f3f3;
  min-height: 100%;
}
.body-color {
  background-color: #fff;
}
* {
  font-family: Helvetica, Tahoma, Arial, "PingFang SC", "Hiragino Sans GB", "Heiti SC", "Microsoft YaHei", "WenQuanYi Micro Hei";
}
/* 修改占位文字的默认样式 */
input::-moz-placeholder,
textarea::-moz-placeholder {
  color: #666666;
  font-size: 14px;
}
input:-ms-input-placeholder,
textarea:-ms-input-placeholder {
  color: #666666;
  font-size: 14px;
}
input::-webkit-input-placeholder,
textarea::-webkit-input-placeholder {
  color: #666666;
  font-size: 14px;
}
.tc {
  text-align: center;
}
.tl {
  text-align: left;
}
.tr {
  text-align: right;
}
.none {
  display: none;
}
.mt20 {
  margin-top: 20px;
}
.mb20 {
  margin-bottom: 20px;
}
.ml20 {
  margin-left: 20px;
}
.mr20 {
  margin-right: 20px;
}
.clearFloat::after {
  content: "";
  height: 0;
  display: block;
  overflow: hidden;
  line-height: 0;
  clear: both;
}
.clearFloat {
  zoom: 1;
}
.pull-left {
  float: left;
}
.pull-right {
  float: right;
}
.page-comon-box .header {
  background: #0d1924;
  height: 50px;
  line-height: 50px;
  display: flex;
  width: 100%;
  padding: 0 15%;
  justify-content: space-between;
  position: fixed;
  top: 0px;
  left: 0px;
  z-index: 100;
}
.page-comon-box .header a:hover {
  color: #fff;
}
.page-comon-box .header .logo {
  width: 120px;
}
/*.page-comon-box .header .logo img {*/
  /*width: 100%;*/
/*}*/
.page-comon-box .header .menu-list {
  display: inline-block;
}
.page-comon-box .header .menu-list .itemOne {
  display: inline-block;
  position: relative;
}
.page-comon-box .header .menu-list .itemOne > a {
  display: block;
  color: #fff;
  position: relative;
  padding: 0px 10px;
  text-decoration: none;
}
.page-comon-box .header .menu-list .itemOne .second-list {
  position: absolute;
  width: 150px;
  padding-top: 10px;
  top: 50px;
}
.page-comon-box .header .menu-list .itemOne .second-list a {
  color: #333;
}
.page-comon-box .header .menu-list .itemOne .second-list > ul {
  width: 100%;
  height: 100%;
  padding: 20px 0px;
  background: #f1f6fb;
  text-align: left;
  min-height: 80px;
  max-height: 200px;
  position: relative;
}
.page-comon-box .header .menu-list .itemOne .second-list > ul > li {
  line-height: 30px;
  padding-left: 10px;
  padding: 0px 10px;
  cursor: pointer;
}
.page-comon-box .header .menu-list .itemOne .second-list > ul > li:hover {
  color: #fff;
  background: #3ca3ff;
}
.page-comon-box .header .menu-list .itemOne .second-list > ul > li:hover a {
  color: #fff;
}
.page-comon-box .header .menu-list .itemOne .second-list > ul > li:hover .three-list {
  display: block;
}
.page-comon-box .header .menu-list .itemOne .second-list > ul > li:hover .three-list a {
  color: #333;
}
.page-comon-box .header .menu-list .itemOne .second-list > ul::after {
  content: "";
  position: absolute;
  top: -5px;
  right: 110px;
  border-bottom: 6px solid #f1f6fb;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
}
.page-comon-box .header .menu-list .itemOne .three-list {
  position: absolute;
  width: 150px;
  background: #fff;
  text-align: left;
  min-height: 80px;
  max-height: 200px;
  padding: 20px 10px;
  top: 0px;
  left: 150px;
  color: #333;
}
.page-comon-box .header .menu-list .itemOne .three-list a {
  color: #333;
}
.page-comon-box .header .menu-list .itemOne .three-list ul > li {
  line-height: 30px;
  padding-left: 10px;
  cursor: pointer;
}
.page-comon-box .header .menu-list .itemOne .three-list ul > li .cicle {
  display: inline-block;
  width: 4px;
  height: 4px;
  margin-right: 5px;
  border-radius: 50%;
  background: #3ca3ff;
}
.page-comon-box .header .menu-list .itemOne .three-list .titele {
  font-size: 16px;
  color: #333333;
  line-height: 21px;
  padding: 0px 0px 10px 0px;
  margin-bottom: 10px;
  border-bottom: 1px dashed #ddd;
}
.page-comon-box .header .menu-list .itemOne:hover a:after {
  position: absolute;
  content: "";
  bottom: 0;
  left: 50%;
  width: 100%;
  height: 3px;
  -webkit-transform: translateX(-50%);
  transform: translateX(-50%);
  background-color: #fff;
}
.page-comon-box .header .menu-list .itemOne:hover .second-list {
  display: block;
}
.page-comon-box .header .menu-list .active a:after {
  position: absolute;
  content: "";
  bottom: 0;
  left: 50%;
  width: 100%;
  height: 3px;
  -webkit-transform: translateX(-50%);
  transform: translateX(-50%);
  background-color: #fff;
}
.page-comon-box .header .btn-grap {
  display: inline-block;
}
.page-comon-box .content .big-title {
  font-size: 36px;
  color: #333;
  text-align: center;
  line-height: 47px;
}
.page-comon-box .content .small-title {
  font-size: 18px;
  line-height: 26px;
  margin-top: 10px;
  color: #aaa;
  text-align: center;
}
.page-comon-box .content .modal-contain {
  max-width: 1200px;
}
.page-comon-box .content .top-theme {
  text-align: center;
  position: relative;
}
.page-comon-box .content .top-theme .tit-bg {
  font-size: 116px;
  font-weight: bold;
  color: #e2e2e2;
  line-height: 135px;
}
.page-comon-box .content .top-theme .tit {
  font-size: 36px;
  color: #333333;
  line-height: 47px;
  position: absolute;
  width: 100%;
  top: 32%;
}
.page-comon-box .footer {
  padding: 30px 10%;
  background: #0d1924;
  color: #fff;
}
.page-comon-box .footer .keyword-list {
  display: flex;
  padding: 20px 0px;
}
.page-comon-box .footer .keyword-list li {
  flex: 1;
  line-height: 40px;
  font-size: 18px;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  border-right: 1px solid #2f3943;
}
.page-comon-box .footer .keyword-list li i {
  font-size: 40px;
  display: inline-block;
  margin-right: 20px;
}
.page-comon-box .footer .keyword-list li span {
  display: inline-block;
  line-height: 40px;
}
.page-comon-box .footer .keyword-list li:nth-of-type(1) {
  justify-content: flex-start;
}
.page-comon-box .footer .keyword-list li:nth-of-type(4) {
  justify-content: flex-end;
  border-right: none;
}
.page-comon-box .footer .middle {
  padding: 30px 0px;
  border-top: 1px solid #2f3943;
  border-bottom: 1px solid #2f3943;
}
.page-comon-box .footer .middle .tit {
  font-size: 16px;
  color: #ffffff;
  line-height: 21px;
  margin-bottom: 10px;
}
.page-comon-box .footer .middle .list a,
.page-comon-box .footer .middle .list > p {
  display: block;
  font-size: 14px;
  color: #aaaaaa;
  line-height: 19px;
  margin-bottom: 10px;
}
.page-comon-box .footer .middle .contact {
  margin-left: 10%;
}
.page-comon-box .footer .middle .contact .list .layui-btn {
  background: #0d1924;
  color: #fff;
}
.page-comon-box .footer .middle .contact .list .layui-btn-primary:hover {
  border-color: #fff;
}
.page-comon-box .footer .middle .apply .layui-form-item {
  width: 350px;
}
.page-comon-box .footer .middle .apply .layui-form-item .layui-input,
.page-comon-box .footer .middle .apply .layui-form-item .layui-select,
.page-comon-box .footer .middle .apply .layui-form-item .layui-textarea {
  border-radius: 16px;
  border: 1px solid #aaaaaa;
  background: #0d1924;
  color: #aaaaaa;
}
.page-comon-box .footer .middle .apply .select-city select {
  display: inline-block;
  width: 173px;
  border-radius: 16px;
  border: 1px solid #aaaaaa;
  background: #0d1924;
  color: #aaaaaa;
  height: 38px;
  padding-left: 10px;
  line-height: 38px;
}
.page-comon-box .footer .middle .apply .phone {
  position: relative;
}
.page-comon-box .footer .middle .apply .phone input {
  padding-right: 45px;
}
.page-comon-box .footer .middle .apply .phone .help {
  width: 25px;
  height: 25px;
  background: url("/Work/View/Guanwang/images/icon/icon-shape.png") no-repeat;
  background-size: 100% 100%;
  position: absolute;
  top: 6px;
  right: 15px;
}
.page-comon-box .footer .foot-bottom {
  padding: 30px 0px 0px 0px;
}
.page-comon-box .footer .foot-bottom .left {
  width: 60%;
}
.page-comon-box .footer .foot-bottom .left p {
  font-size: 16px;
  color: #aaaaaa;
  line-height: 21px;
  margin-bottom: 15px;
}
.page-comon-box .footer .foot-bottom .right a {
  display: inline-block;
  margin-right: 20px;
}
.page-comon-box .footer .foot-bottom .right a img {
  width: 90px;
}
.page-comon-box .footer .foot-bottom .right a p {
  font-size: 14px;
  color: #aaaaaa;
  line-height: 19px;
  text-align: center;
}
.page-comon-box .footer .foot-bottom .right a:nth-last-of-type(1) {
  margin-right: 0px;
}
.page-comon-box .Suspension {
  width: 60px;
  position: fixed;
  bottom: 100px;
  right: 20px;
  z-index: 50;
  background: #ffffff;
  border: 1px solid #aaaaaa;
}
.page-comon-box .Suspension a {
  font-size: 16px;
  color: #666666;
  line-height: 21px;
  display: block;
  padding: 10px 20px;
}
.page-comon-box .Suspension .btn-try {
  color: #ffffff;
  background: #000000;
}
.page-comon-box .Suspension .icon {
  width: 28px;
  height: 31px;
  background: url("/Work/View/Guanwang/images/icon/icon-customer.png") no-repeat;
  background-size: 100% 100%;
  display: block;
  margin: 5px auto;
}
.page-comon-box .Suspension .btn-label > span {
  width: 6px;
  height: 6px;
  background: #666666;
  border-radius: 50%;
  display: block;
  margin: 5px auto;
}
.frame-page {
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  position: fixed;
  z-index: 100;
  top: 0;
  left: 0;
}
.frame-page .frame-video-box {
  width: 50%;
  max-width: 998px;
  margin: 0 auto;
  padding-top: 15%;
  position: relative;
}
.frame-page .frame-video-box video {
  width: 100%;
  margin: 0 auto;
  display: block;
}
.frame-page .frame-video-box .btn-colse {
  width: 33px;
  height: 33px;
  line-height: 33px;
  text-align: center;
  border-radius: 50%;
  color: #fff;
  position: absolute;
  top: 31%;
  right: -33px;
  z-index: 200;
  cursor: pointer;
  background: #8ea3a4;
}
.frame-page .frame-video-box .btn-colse i {
  font-size: 18px;
  color: #fff;
}
.home-page .header {
  background: rgba(13, 25, 36, 0);
}
.home-page .body .content1 {
  position: relative;
}
.home-page .body .content1 a:hover {
  color: #fff;
}
.home-page .body .content1 .swiper-container-horizontal > .swiper-pagination-bullets,
.home-page .body .content1 .swiper-pagination-custom,
.home-page .body .content1 .swiper-pagination-fraction {
  bottom: 55px;
}
.home-page .body .content1 .swiper-container-horizontal > .swiper-pagination-bullets .swiper-pagination-bullet {
  width: 16px;
  height: 6px;
  background: #ffffff;
  border-radius: 5px;
  background: #65d0f4;
  opacity: 1;
}
.home-page .body .content1 .swiper-container-horizontal > .swiper-pagination-bullets .swiper-pagination-bullet-active {
  background: #fff;
}
.home-page .body .content1 .swiper-slide img {
  width: 100%;
}
.home-page .body .content1 .btn-top {
  font-size: 18px;
  position: absolute;
  bottom: 36%;
  left: 7.5%;
  z-index: 50;
}
.home-page .body .content1 .btn-arrow {
  position: absolute;
  bottom: 8px;
  left: 0;
  color: #fff;
  z-index: 50;
  width: 100%;
}
.home-page .body .content1 .btn-arrow p {
  margin: 0px;
  text-align: center;
}
.home-page .body .content1 .top-logo .layui-row > div {
  padding: 20px 0px;
  border-left: 1px solid #ddd;
  background: #f8f8f8;
}
.home-page .body .content1 .top-logo .layui-row > div .col-cont {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 330px;
  margin: 0 auto;
}
.home-page .body .content1 .top-logo .layui-row > div .col-cont .col-left img {
  width: 90px;
}
.home-page .body .content1 .top-logo .layui-row > div .col-cont .col-right {
  width: 230px;
}
.home-page .body .content1 .top-logo .layui-row > div .col-cont .col-right .title {
  font-size: 20px;
}
.home-page .body .content1 .top-logo .layui-row > div .col-cont .col-right .cont {
  font-size: 14px;
  color: #888888;
}
.home-page .body .content1 .top-logo .layui-row > div::nth-last-of-type(1) {
  border-left: none;
}
.home-page .body .content2 {
  padding: 70px 20px;
  background: #fff;
}
.home-page .body .content2 .modal-contain {
  width: 75%;
  margin: 60px auto 0 auto;
  display: flex;
  justify-content: space-between;
}
.home-page .body .content2 .modal-contain .left {
  width: 60%;
}
.home-page .body .content2 .modal-contain .left video {
  width: 100%;
}
.home-page .body .content2 .modal-contain .right {
  width: 40%;
  padding: 0px 0px 0px 20px;
}
.home-page .body .content2 .modal-contain .right .tit {
  font-size: 18px;
  color: #333333;
  line-height: 24px;
  margin-bottom: 5px;
}
.home-page .body .content2 .modal-contain .right .cont {
  font-size: 14px;
  color: #aaaaaa;
  line-height: 24px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  overflow: hidden;
}
.home-page .body .content2 .modal-contain .right ul li {
  display: flex;
  justify-content: flex-start;
  margin-bottom: 20px;
  cursor: pointer;
}
.home-page .body .content2 .modal-contain .right ul li:nth-last-of-type(1) {
  margin-bottom: 0px;
}
.home-page .body .content2 .modal-contain .right ul li:hover .icon {
  background: url("/Work/View/Guanwang/images/icon/icon-play.png") no-repeat;
  background-size: 100% 100%;
}
.home-page .body .content2 .modal-contain .right .icon {
  display: inline-block;
  width: 30px;
  height: 30px;
  background: url("/Work/View/Guanwang/images/icon/icon-pause.png") no-repeat;
  background-size: 100% 100%;
}
.home-page .body .content2 .modal-contain .right .active .icon {
  background: url("/Work/View/Guanwang/images/icon/icon-play.png") no-repeat;
  background-size: 100% 100%;
}
.home-page .body .content2 .modal-contain .right .active .tit {
  color: #2a5082;
}
.home-page .body .content2 .modal-contain .right .detail {
  width: calc(100% - 30px);
  padding-left: 10px;
}
.home-page .body .content3 {
  padding: 70px 20px;
  background: #f1f6fb;
}
.home-page .body .content3 .modal-contain {
  width: 75%;
  margin: 60px auto 0 auto;
}
.home-page .body .content3 .modal-contain .list-row {
  display: flex;
}
.home-page .body .content3 .modal-contain .list-row .col-list {
  flex: 1;
}
.home-page .body .content3 .modal-contain .list-row .col-list .icon {
  width: 163px;
  height: 163px;
  border-radius: 50%;
  padding-top: 35px;
  margin: 0 auto;
  background: #ffffff;
  box-shadow: 0px 0px 25px 0px rgba(0, 78, 137, 0.08);
}
.home-page .body .content3 .modal-contain .list-row .col-list .icon img {
  width: 96px;
  margin: 0 auto;
  display: block;
}
.home-page .body .content3 .modal-contain .list-row .col-list p {
  width: 95%;
  text-align: center;
  margin: 0 auto;
}
.home-page .body .content3 .modal-contain .list-row .col-list .tit {
  font-size: 18px;
  color: #333333;
  line-height: 24px;
  margin: 10px auto;
}
.home-page .body .content3 .modal-contain .list-row .col-list .cont {
  font-size: 14px;
  color: #666666;
  line-height: 19px;
}
.home-page .body .content4 {
  padding: 70px 20px;
  background: #fff;
}
.home-page .body .content4 .modal-contain {
  width: 75%;
  margin: 60px auto 0 auto;
  display: flex;
}
.home-page .body .content4 .modal-contain .left {
  min-width: 150px;
  background: #ffffff;
  box-shadow: 0px 0px 26px 0px rgba(0, 0, 0, 0.08);
}
.home-page .body .content4 .modal-contain .left li {
  padding: 20px;
  cursor: pointer;
}
.home-page .body .content4 .modal-contain .left li img {
  width: 64px;
  display: block;
  margin: 0 auto 10px auto;
}
.home-page .body .content4 .modal-contain .left li p {
  font-size: 24px;
  color: #333333;
  line-height: 31px;
  text-align: center;
}
.home-page .body .content4 .modal-contain .left .active {
  background: #3ca3ff;
  position: relative;
}
.home-page .body .content4 .modal-contain .left .active p {
  color: #fff;
}
.home-page .body .content4 .modal-contain .left .active::after {
  content: "";
  position: absolute;
  top: 60px;
  right: -15px;
  border-top: 10px solid transparent;
  border-left: 15px solid #3ca3ff;
  border-bottom: 10px solid transparent;
}
.home-page .body .content4 .modal-contain .right {
  display: flex;
  padding-left: 30px;
}
.home-page .body .content4 .modal-contain .right > div {
  flex: 1;
}
.home-page .body .content4 .modal-contain .right .r-left {
  padding-right: 30px;
  position: relative;
}
.home-page .body .content4 .modal-contain .right .r-left .title {
  font-size: 34px;
  color: #333333;
  line-height: 45px;
}
.home-page .body .content4 .modal-contain .right .r-left .line {
  width: 62px;
  height: 2px;
  background: #3ca3ff;
  margin: 10px 0px 30px 0px;
}
.home-page .body .content4 .modal-contain .right .r-left .cont {
  font-size: 18px;
  color: #666666;
  line-height: 24px;
}
.home-page .body .content4 .modal-contain .right .r-left .cont p {
  margin-bottom: 15px;
}
.home-page .body .content4 .modal-contain .right .r-left .tip {
  font-size: 28px;
  color: #666666;
  line-height: 37px;
  margin-bottom: 20px;
}
.home-page .body .content4 .modal-contain .right .r-left .tip span {
  color: #df825a;
}
.home-page .body .content4 .modal-contain .right .r-left .layui-btn {
  position: absolute;
  bottom: 0px;
  left: 0px;
  padding: 0px 30px;
}
.home-page .body .content4 .modal-contain .right .r-right {
  position: relative;
}
.home-page .body .content4 .modal-contain .right .r-right img {
  position: absolute;
  bottom: 0px;
  left: 0px;
}
.home-page .body .content5 {
  padding: 70px 20px;
  background: #f1f6fb;
}
.home-page .body .content5 .modal-contain {
  width: 75%;
  margin: 60px auto 0 auto;
}
.home-page .body .content5 .modal-contain .news-list li {
  float: left;
  width: 20%;
}
.home-page .body .content5 .modal-contain .news-list li a {
  display: block;
  width: 100%;
}
.home-page .body .content5 .modal-contain .news-list li a .img {
  position: relative;
}
.home-page .body .content5 .modal-contain .news-list li a .img .gb {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
}
.home-page .body .content5 .modal-contain .news-list li a .cont {
  width: 100%;
  background: #ffffff;
  box-shadow: 0px 0px 26px 0px rgba(0, 0, 0, 0.08);
  font-size: 30px;
  color: #333333;
  line-height: 45px;
  text-align: center;
  position: relative;
}
.home-page .body .content5 .modal-contain .news-list li a .cont img {
  opacity: 0;
}
.home-page .body .content5 .modal-contain .news-list li a .cont .text {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  vertical-align: middle;
  padding-top: 35%;
}
.home-page .body .content5 .modal-contain .news-list .item1 a .cont .text {
  padding-top: 25%;
}
.home-page .body .content5 .modal-contain .news-list li:hover a .cont {
  color: #fff;
  background: #3ca3ff;
}
.home-page .body .content5 .modal-contain .news-list .active a .cont {
  color: #fff;
  background: #3ca3ff;
}
.home-page .body .content5 .modal-contain .news-list .item2,
.home-page .body .content5 .modal-contain .news-list .item4 {
  margin-top: 19.6%;
}
.home-page .body .content6 {
  padding: 70px 20px;
  background: #fff;
}
.home-page .body .content6 .btn-more {
  display: block;
  text-align: center;
  margin-top: 10px;
}
.home-page .body .content6 .btn-more span {
  font-size: 18px;
  color: #006ddf;
  line-height: 24px;
}
.home-page .body .content6 .btn-more span i {
  font-size: 18px;
  width: 24px;
  height: 24px;
  display: inline-block;
  background: #006ddf;
  color: #fff;
  border-radius: 50%;
}
.home-page .body .content6 .modal-contain {
  width: 95%;
  margin: 60px auto 0 auto;
}
.home-page .body .content6 .modal-contain .swiper-slide {
  padding: 20px;
}
.home-page .body .content6 .modal-contain .swiper-slide .slider-cont {
  padding: 30px 20px 20px 20px;
  background: #ffffff;
  box-shadow: 0px 0px 26px 0px rgba(0, 0, 0, 0.08);
  text-align: center;
}
.home-page .body .content6 .modal-contain .swiper-slide .slider-cont .time {
  font-size: 14px;
  color: #333333;
  line-height: 19px;
  margin-bottom: 20px;
}
.home-page .body .content6 .modal-contain .swiper-slide .slider-cont .tit {
  font-size: 18px;
  color: #333333;
  line-height: 24px;
}
.home-page .body .content6 .modal-contain .swiper-slide .slider-cont a {
  color: #3ca3ff;
  font-size: 14px;
  margin: 20px 0px;
  display: block;
}
.home-page .body .content6 .modal-contain .swiper-slide .slider-cont .cont {
  font-size: 14px;
  color: #666666;
  line-height: 19px;
  text-align: left;
  margin-bottom: 20px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  overflow: hidden;
}
.home-page .body .content6 .modal-contain .swiper-container-horizontal > .swiper-scrollbar {
  opacity: 1 !important;
  height: 2px;
}
.home-page .body .content6 .modal-contain .swiper-button-prev {
  width: 48px;
  height: 48px;
  background: url("/Work/View/Guanwang/images/icon/icon-prev-active.png") no-repeat;
  background-size: 48px 48px;
}
.home-page .body .content6 .modal-contain .swiper-button-next {
  width: 48px;
  height: 48px;
  background: url("/Work/View/Guanwang/images/icon/icon-next-active.png") no-repeat;
  background-size: 48px 48px;
}
.login-page {
  background-color: #f3f3f3;
  position: relative;
  min-height: calc(100vh - 50px);
}
.login-page .content {
  width: 75%;
  max-width: 1200px;
  margin: 50px auto 0 auto;
  padding: 14% 0;
}
.login-page .content .title {
  text-align: center;
  font-size: 28px;
  color: #333333;
  line-height: 37px;
  margin-bottom: 5%;
}
.login-page .content .layui-row > div {
  position: relative;
}
.login-page .content .layui-row > div a {
  display: block;
  width: 85%;
  margin: 0 auto;
}
.login-page .content .layui-row > div .tit {
  position: absolute;
  top: 40%;
  left: 37%;
  text-align: center;
  font-size: 28px;
  color: #ffffff;
  line-height: 37px;
}
.login-page .bottom {
  width: 400px;
  position: absolute;
  bottom: 0;
  right: 0;
}
.login-page .bottom img {
  width: 100%;
}
.help-page,
.help-video,
.help-person,
.help-problem {
  background-color: #f3f3f3;
  min-height: calc(100vh - 50px);
}
.help-page .header,
.help-video .header,
.help-person .header,
.help-problem .header {
  background: #fff;
  box-shadow: 0px 0px 26px 0px rgba(0, 0, 0, 0.08);
}
.help-page .header .menu-list li,
.help-video .header .menu-list li,
.help-person .header .menu-list li,
.help-problem .header .menu-list li {
  display: inline-block;
}
.help-page .header .menu-list li:nth-of-type(1),
.help-video .header .menu-list li:nth-of-type(1),
.help-person .header .menu-list li:nth-of-type(1),
.help-problem .header .menu-list li:nth-of-type(1) {
  margin-right: 20px;
}
.help-page .header .menu-list li a,
.help-video .header .menu-list li a,
.help-person .header .menu-list li a,
.help-problem .header .menu-list li a {
  color: #333;
}
.help-page .bg-1,
.help-video .bg-1,
.help-person .bg-1,
.help-problem .bg-1 {
  background: #84c5ff;
}
.help-page .bg-2,
.help-video .bg-2,
.help-person .bg-2,
.help-problem .bg-2 {
  background: #ffda99;
}
.help-page .bg-3,
.help-video .bg-3,
.help-person .bg-3,
.help-problem .bg-3 {
  background: #abf2ff;
}
.help-page .bg-4,
.help-video .bg-4,
.help-person .bg-4,
.help-problem .bg-4 {
  background: #ffc3c6;
}
.help-page .content,
.help-video .content,
.help-person .content,
.help-problem .content {
  width: 100%;
  margin: 50px auto 0 auto;
}
.help-page .content .top,
.help-video .content .top,
.help-person .content .top,
.help-problem .content .top {
  padding: 30px 12.5%;
  margin: 0 auto;
}
.help-page .content .top .layui-row,
.help-video .content .top .layui-row,
.help-person .content .top .layui-row,
.help-problem .content .top .layui-row {
  max-width: 1200px;
  margin: 0 auto;
}
.help-page .content .top .layui-row > div,
.help-video .content .top .layui-row > div,
.help-person .content .top .layui-row > div,
.help-problem .content .top .layui-row > div {
  max-width: 1200px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.help-page .content .top .layui-row > div .img,
.help-video .content .top .layui-row > div .img,
.help-person .content .top .layui-row > div .img,
.help-problem .content .top .layui-row > div .img {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 10%;
}
.help-page .content .top .layui-row > div .img img,
.help-video .content .top .layui-row > div .img img,
.help-person .content .top .layui-row > div .img img,
.help-problem .content .top .layui-row > div .img img {
  width: 40px;
  display: block;
  margin: 28% auto 0 auto;
}
.help-page .content .top .layui-row > div .tit,
.help-video .content .top .layui-row > div .tit,
.help-person .content .top .layui-row > div .tit,
.help-problem .content .top .layui-row > div .tit {
  font-size: 18px;
  color: #333333;
  line-height: 24px;
}
.help-page .content .top .layui-row > div:nth-of-type(1),
.help-video .content .top .layui-row > div:nth-of-type(1),
.help-person .content .top .layui-row > div:nth-of-type(1),
.help-problem .content .top .layui-row > div:nth-of-type(1) {
  justify-content: flex-start;
}
.help-page .content .top .layui-row > div:nth-of-type(4),
.help-video .content .top .layui-row > div:nth-of-type(4),
.help-person .content .top .layui-row > div:nth-of-type(4),
.help-problem .content .top .layui-row > div:nth-of-type(4) {
  justify-content: flex-end;
  border-right: none;
}
.help-page .content .modal-cont,
.help-video .content .modal-cont,
.help-person .content .modal-cont,
.help-problem .content .modal-cont {
  background: #f1f6fb;
  padding: 30px 12.5%;
  margin-bottom: 50px;
}
.help-page .content .modal-cont input::-moz-placeholder,
.help-video .content .modal-cont input::-moz-placeholder,
.help-person .content .modal-cont input::-moz-placeholder,
.help-problem .content .modal-cont input::-moz-placeholder,
.help-page .content .modal-cont textarea::-moz-placeholder,
.help-video .content .modal-cont textarea::-moz-placeholder,
.help-person .content .modal-cont textarea::-moz-placeholder,
.help-problem .content .modal-cont textarea::-moz-placeholder {
  color: #aaa;
  font-size: 14px;
}
.help-page .content .modal-cont input:-ms-input-placeholder,
.help-video .content .modal-cont input:-ms-input-placeholder,
.help-person .content .modal-cont input:-ms-input-placeholder,
.help-problem .content .modal-cont input:-ms-input-placeholder,
.help-page .content .modal-cont textarea:-ms-input-placeholder,
.help-video .content .modal-cont textarea:-ms-input-placeholder,
.help-person .content .modal-cont textarea:-ms-input-placeholder,
.help-problem .content .modal-cont textarea:-ms-input-placeholder {
  color: #aaa;
  font-size: 14px;
}
.help-page .content .modal-cont input::-webkit-input-placeholder,
.help-video .content .modal-cont input::-webkit-input-placeholder,
.help-person .content .modal-cont input::-webkit-input-placeholder,
.help-problem .content .modal-cont input::-webkit-input-placeholder,
.help-page .content .modal-cont textarea::-webkit-input-placeholder,
.help-video .content .modal-cont textarea::-webkit-input-placeholder,
.help-person .content .modal-cont textarea::-webkit-input-placeholder,
.help-problem .content .modal-cont textarea::-webkit-input-placeholder {
  color: #aaa;
  font-size: 14px;
}
.help-page .content .modal-cont .cont,
.help-video .content .modal-cont .cont,
.help-person .content .modal-cont .cont,
.help-problem .content .modal-cont .cont {
  max-width: 1200px;
  margin: 0 auto;
}
.help-page .content .modal-cont .cont .search,
.help-video .content .modal-cont .cont .search,
.help-person .content .modal-cont .cont .search,
.help-problem .content .modal-cont .cont .search {
  width: 100%;
  max-width: 540px;
  margin: 0 auto;
  position: relative;
}
.help-page .content .modal-cont .cont .search input,
.help-video .content .modal-cont .cont .search input,
.help-person .content .modal-cont .cont .search input,
.help-problem .content .modal-cont .cont .search input {
  padding: 10px 10px 10px 40px;
  border: 2px solid #3ca3ff;
}
.help-page .content .modal-cont .cont .search input:hover,
.help-video .content .modal-cont .cont .search input:hover,
.help-person .content .modal-cont .cont .search input:hover,
.help-problem .content .modal-cont .cont .search input:hover {
  border: 2px solid #3ca3ff;
}
.help-page .content .modal-cont .cont .search > i,
.help-video .content .modal-cont .cont .search > i,
.help-person .content .modal-cont .cont .search > i,
.help-problem .content .modal-cont .cont .search > i {
  color: #aaa;
  font-size: 23px;
  position: absolute;
  display: block;
  top: 6px;
  left: 10px;
}
.help-page .content .modal-cont .cont .middle,
.help-video .content .modal-cont .cont .middle,
.help-person .content .modal-cont .cont .middle,
.help-problem .content .modal-cont .cont .middle {
  display: flex;
  background: #fff;
  margin-top: 30px;
}
.help-page .content .modal-cont .cont .middle .title,
.help-video .content .modal-cont .cont .middle .title,
.help-person .content .modal-cont .cont .middle .title,
.help-problem .content .modal-cont .cont .middle .title {
  font-size: 16px;
  color: #3ca3ff;
  line-height: 21px;
  margin-bottom: 15px;
  padding: 0 15px;
}
.help-page .content .modal-cont .cont .middle .left,
.help-video .content .modal-cont .cont .middle .left,
.help-person .content .modal-cont .cont .middle .left,
.help-problem .content .modal-cont .cont .middle .left {
  padding: 20px 0;
  min-width: 150px;
  text-align: center;
  border-right: 1px solid #ddd;
}
.help-page .content .modal-cont .cont .middle .layui-tab-title,
.help-video .content .modal-cont .cont .middle .layui-tab-title,
.help-person .content .modal-cont .cont .middle .layui-tab-title,
.help-problem .content .modal-cont .cont .middle .layui-tab-title {
  border: none;
  height: auto;
}
.help-page .content .modal-cont .cont .middle .layui-tab-title li,
.help-video .content .modal-cont .cont .middle .layui-tab-title li,
.help-person .content .modal-cont .cont .middle .layui-tab-title li,
.help-problem .content .modal-cont .cont .middle .layui-tab-title li {
  display: block;
  position: static;
  font-size: 14px;
  position: relative;
  line-height: 20px;
  margin-bottom: 15px;
}
.help-page .content .modal-cont .cont .middle .layui-tab-title .layui-this,
.help-video .content .modal-cont .cont .middle .layui-tab-title .layui-this,
.help-person .content .modal-cont .cont .middle .layui-tab-title .layui-this,
.help-problem .content .modal-cont .cont .middle .layui-tab-title .layui-this {
  color: #3ca3ff;
  border-left: 2px solid #3ca3ff;
}
.help-page .content .modal-cont .cont .middle .layui-tab-title .layui-this:after,
.help-video .content .modal-cont .cont .middle .layui-tab-title .layui-this:after,
.help-person .content .modal-cont .cont .middle .layui-tab-title .layui-this:after,
.help-problem .content .modal-cont .cont .middle .layui-tab-title .layui-this:after {
  display: none;
}
.help-page .content .modal-cont .cont .middle .right,
.help-video .content .modal-cont .cont .middle .right,
.help-person .content .modal-cont .cont .middle .right,
.help-problem .content .modal-cont .cont .middle .right {
  width: 100%;
  padding: 20px 20px;
}
.help-page .content .modal-cont .cont .middle .right .top-item,
.help-video .content .modal-cont .cont .middle .right .top-item,
.help-person .content .modal-cont .cont .middle .right .top-item,
.help-problem .content .modal-cont .cont .middle .right .top-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.help-page .content .modal-cont .cont .middle .right .top-item .sm-t,
.help-video .content .modal-cont .cont .middle .right .top-item .sm-t,
.help-person .content .modal-cont .cont .middle .right .top-item .sm-t,
.help-problem .content .modal-cont .cont .middle .right .top-item .sm-t {
  color: #aaaaaa;
  font-size: 14px;
}
.help-page .content .modal-cont .cont .middle .right .top-item .tips,
.help-video .content .modal-cont .cont .middle .right .top-item .tips,
.help-person .content .modal-cont .cont .middle .right .top-item .tips,
.help-problem .content .modal-cont .cont .middle .right .top-item .tips {
  color: #3ca3ff;
  font-size: 14px;
}
.help-page .content .modal-cont .cont .middle .right .none,
.help-video .content .modal-cont .cont .middle .right .none,
.help-person .content .modal-cont .cont .middle .right .none,
.help-problem .content .modal-cont .cont .middle .right .none {
  display: none;
}
.help-page .content .modal-cont .cont .middle .right .tab-cont,
.help-video .content .modal-cont .cont .middle .right .tab-cont,
.help-person .content .modal-cont .cont .middle .right .tab-cont,
.help-problem .content .modal-cont .cont .middle .right .tab-cont {
  color: #333;
  font-size: 16px;
}
.help-page .content .modal-cont .cont .middle .right .tab-cont > li,
.help-video .content .modal-cont .cont .middle .right .tab-cont > li,
.help-person .content .modal-cont .cont .middle .right .tab-cont > li,
.help-problem .content .modal-cont .cont .middle .right .tab-cont > li {
  line-height: 30px;
}
.help-page .content .modal-cont .cont .middle .right .tab-cont > li p,
.help-video .content .modal-cont .cont .middle .right .tab-cont > li p,
.help-person .content .modal-cont .cont .middle .right .tab-cont > li p,
.help-problem .content .modal-cont .cont .middle .right .tab-cont > li p {
  cursor: pointer;
}
.help-page .content .modal-cont .cont .middle .right .tab-cont > li .detail,
.help-video .content .modal-cont .cont .middle .right .tab-cont > li .detail,
.help-person .content .modal-cont .cont .middle .right .tab-cont > li .detail,
.help-problem .content .modal-cont .cont .middle .right .tab-cont > li .detail {
  color: #666666;
  line-height: 24px;
  padding-left: 20px;
  font-size: 14px;
  display: none;
}
.help-page .footer,
.help-video .footer,
.help-person .footer,
.help-problem .footer {
  background: #fff;
  color: #666666;
  font-size: 16px;
}
.help-page .footer .left,
.help-video .footer .left,
.help-person .footer .left,
.help-problem .footer .left {
  border-right: 1px solid #ddd;
}
.help-page .footer .tit,
.help-video .footer .tit,
.help-person .footer .tit,
.help-problem .footer .tit {
  font-size: 18px;
  color: #000000;
  line-height: 26px;
  margin-bottom: 15px;
}
.help-page .footer ul li,
.help-video .footer ul li,
.help-person .footer ul li,
.help-problem .footer ul li {
  margin-bottom: 15px;
}
.help-page .footer ul li:nth-last-of-type(1),
.help-video .footer ul li:nth-last-of-type(1),
.help-person .footer ul li:nth-last-of-type(1),
.help-problem .footer ul li:nth-last-of-type(1) {
  margin-bottom: 0px;
}
.help-page .footer > .layui-row > div,
.help-video .footer > .layui-row > div,
.help-person .footer > .layui-row > div,
.help-problem .footer > .layui-row > div {
  padding-left: 8%;
}
.help-page .footer > .layui-row .wechat,
.help-video .footer > .layui-row .wechat,
.help-person .footer > .layui-row .wechat,
.help-problem .footer > .layui-row .wechat {
  text-align: center;
}
.help-page .footer > .layui-row .wechat img,
.help-video .footer > .layui-row .wechat img,
.help-person .footer > .layui-row .wechat img,
.help-problem .footer > .layui-row .wechat img {
  width: 120px;
  margin: 0 auto;
  display: block;
}
/*底部样式*/
footer,footer .footer-container {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}
footer{
  height: 158px;
  background: #fff;
}
footer .footer-container {
  width: 100%;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  padding: 0 100px;
  font-size: 14px;
  line-height: 26px;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  color: #000;
}
footer .footer-container .left {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}
footer .footer-container .left .row+.row {
  margin-left: 40px;
}
footer .footer-container label {
  font-size: 16px;
  color: #aaa;
  margin-right: 10px;
}
footer .footer-container .right .ewm {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}
footer .footer-container .right .ewm>span {
  color: #666;
  margin-left: 20px;
}
footer .footer-container .right .img img {
  width: 82px;
}
.help-video .tab-cont li {
  width: 26%;
  float: left;
  margin-left: 5%;
  margin-bottom: 10px;
}
.help-video .tab-cont li video {
  width: 100%;
}
.help-video .tab-cont li p {
  font-size: 14px;
  color: #333333;
  line-height: 19px;
  text-align: center;
}
.help-person .middle {
  display: block;
}
.help-person .middle .layui-row {
  width: 100%;
  padding: 10% 5%;
}
.help-person .middle .layui-row table td {
  font-size: 18px;
  color: #333333;
  line-height: 40px;
  vertical-align: baseline;
}
.help-person .middle .layui-row table td span {
  color: #aaaaaa;
}
.help-person .middle .layui-row .wechat-list {
  display: flex;
  justify-content: space-between;
}
.help-person .middle .layui-row .wechat-list > div {
  width: 45%;
  font-size: 14px;
  color: #666666;
  line-height: 19px;
}
.help-person .middle .layui-row .wechat-list > div p {
  margin-top: 20px;
  text-align: center;
}
.product-page .product-banner {
  position: relative;
}
.product-page .product-banner .layui-btn {
  position: absolute;
  left: 18%;
  bottom: 20%;
}
.product-page .content .modal-contain {
  width: 75%;
  max-width: 1200px;
  margin: 0px auto 0 auto;
  padding: 40px 0;
}
.product-page .content2 {
  background: #f1f6fb;
}
.product-page .content2 .tit {
  background: #fff;
  padding: 10px 0;
}
.product-page .content2 .tit > div {
  width: 75%;
  max-width: 1200px;
  margin: 0 auto;
  color: #333333;
  line-height: 24px;
}
.product-page .content2 .modal-contain .left {
  position: relative;
  padding-left: 50px;
}
.product-page .content2 .modal-contain .left .cont {
  font-size: 14px;
  color: #666666;
  line-height: 24px;
  margin: 20px 0 40px 0;
  padding-right: 15%;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  overflow: hidden;
}
.product-page .content2 .modal-contain .left .message-list {
  padding-right: 15%;
}
.product-page .content2 .modal-contain .left .message-list li {
  margin-bottom: 5px;
}
.product-page .content2 .modal-contain .left .message-list li a {
  font-size: 14px;
  color: #3ca3ff;
  line-height: 19px;
  padding-left: 15px;
  position: relative;
}
.product-page .content2 .modal-contain .left .message-list li a > span {
  position: absolute;
  top: 0px;
  left: 0px;
  width: 10px;
  height: 11px;
  display: inline-block;
}
.product-page .content2 .modal-contain .left .message-list .icon-map {
  background: url("/Work/View/Guanwang/images/icon/icon-map.png") no-repeat;
  background-size: 10px 11px;
}
.product-page .content2 .modal-contain .left .message-list .icon-fire {
  background: url("/Work/View/Guanwang/images/icon/icon-fire.png") no-repeat;
  background-size: 10px 11px;
}
.product-page .content2 .modal-contain .left::before {
  content: "";
  position: absolute;
  top: 0px;
  left: 5px;
  width: 40px;
  height: 40px;
  cursor: pointer;
  display: inline-block;
  background: url("/Work/View/Guanwang/images/icon/icon-intrudce.png") no-repeat;
  background-size: 40px 40px;
}
.product-page .content2 .modal-contain .video-box {
  width: 450px;
  background: #fff;
}
.product-page .content2 .modal-contain .video-box .video-info {
  padding: 20px;
  font-size: 14px;
  color: #aaaaaa;
  line-height: 19px;
  padding-left: 60px;
  position: relative;
}
.product-page .content2 .modal-contain .video-box .video-info .icon-video {
  position: absolute;
  top: 18px;
  left: 20px;
  width: 23px;
  height: 20px;
  cursor: pointer;
  display: inline-block;
  background: url("/Work/View/Guanwang/images/product/video-pause.png") no-repeat;
  background-size: 23px 20px;
}
.product-page .content3 {
  padding: 30px 0px;
}
.product-page .content3 .list-row {
  display: flex;
  margin-bottom: 30px;
}
.product-page .content3 .list-row .col-list {
  flex: 1;
}
.product-page .content3 .list-row .col-list .icon {
  width: 90px;
  height: 90px;
  border-radius: 50%;
  margin: 0 auto;
}
.product-page .content3 .list-row .col-list .icon img {
  width: 100%;
  margin: 0 auto;
  display: block;
}
.product-page .content3 .list-row .col-list p {
  width: 80%;
  text-align: center;
  margin: 0 auto;
}
.product-page .content3 .list-row .col-list .tit {
  font-size: 18px;
  color: #333333;
  line-height: 24px;
  margin: 10px auto;
}
.product-page .content3 .list-row .col-list .cont {
  font-size: 14px;
  color: #666666;
  line-height: 19px;
  max-height: 100px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  overflow: hidden;
}
.product-page .content4 {
  background: #f1f6fb;
  padding: 30px 0px;
}
.product-page .content4 .layui-tab-title li {
  padding: 0 65px;
  font-size: 16px;
}
.product-page .content4 .layui-tab-title .layui-this:after {
  left: 34%;
  width: 30%;
}
.product-page .content4 .layui-tab-content .layui-tab-item .left {
  width: 60%;
  padding-left: 5%;
  padding-right: 10%;
}
.product-page .content4 .layui-tab-content .layui-tab-item .left .cont {
  font-size: 14px;
  color: #666666;
  line-height: 19px;
  margin-bottom: 20px;
}
.product-page .content4 .layui-tab-content .layui-tab-item .right {
  width: 40%;
  position: relative;
}
.product-page .content4 .layui-tab-content .layui-tab-item .right img {
  width: 300px;
}
.product-page .content4 .layui-tab-content .layui-tab-item .right .info {
  position: absolute;
  top: 15%;
  left: 8%;
  color: #fff;
}
.product-page .content4 .layui-tab-content .layui-tab-item .right .info .tit {
  position: relative;
  font-size: 16px;
  color: #ffffff;
  line-height: 21px;
  padding-left: 28px;
}
.product-page .content4 .layui-tab-content .layui-tab-item .right .info .tit > span {
  position: absolute;
  top: 0px;
  left: 0px;
  width: 21px;
  height: 21px;
  display: inline-block;
  background: url("/Work/View/Guanwang/images/icon/icon-intrudce.png") no-repeat;
  background-size: 21px 21px;
}
.product-page .content4 .layui-tab-content .layui-tab-item .right .info ul li {
  font-size: 14px;
  color: #ffffff;
  line-height: 24px;
}
.product-page .content4 .layui-tab-content .layui-tab-item .right .info ul li .icon {
  display: inline-block;
  width: 6px;
  height: 6px;
  background: #ffffff;
  margin-right: 10px;
}
.product-page .content4 .layui-tab-content .layui-tab-item .right .info a {
  display: block;
  font-size: 14px;
  color: #3ca3ff;
  line-height: 19px;
}
.solution-page .solution-banner {
  position: relative;
}
.solution-page .solution-banner .btn-contact {
  width: 100%;
  position: absolute;
  left: 0%;
  bottom: 20%;
}
.solution-page .solution-banner .btn-contact .layui-btn {
  display: block;
  margin: 0 auto;
}
.solution-page .solution-banner .btn-contact a {
  display: block;
  color: #fff;
}
.solution-page .content a {
  display: block;
}
.solution-page .content .modal-contain {
  width: 75%;
  margin: 0px auto 0 auto;
  padding: 40px 0;
}
.solution-page .content .modal-contain .state1 {
  background: linear-gradient(315deg, #06a1ff 0%, #5dc4ff 100%);
}
.solution-page .content .modal-contain .state2 {
  background: linear-gradient(315deg, #0458cf 0%, #7297ff 100%);
}
.solution-page .content2 {
  padding: 30px 0px;
}
.solution-page .content2 .layui-row {
  display: flex;
}
.solution-page .content2 .layui-row .big-img,
.solution-page .content2 .layui-row .state {
  position: relative;
}
.solution-page .content2 .layui-row .big-img .info,
.solution-page .content2 .layui-row .state .info {
  color: #fff;
  position: absolute;
  top: 30px;
  left: 30px;
}
.solution-page .content2 .layui-row .big-img .info .tit,
.solution-page .content2 .layui-row .state .info .tit {
  font-size: 24px;
  color: #ffffff;
  line-height: 31px;
  margin-bottom: 30px;
}
.solution-page .content2 .layui-row .big-img .info .cont,
.solution-page .content2 .layui-row .state .info .cont {
  max-width: 100%;
}
.solution-page .content2 .layui-row .state {
  width: 100%;
  height: 100%;
}
.solution-page .content2 .layui-row .state .info {
  left: 0px;
  padding: 0 30px;
}
.solution-page .content3 {
  background: #f1f6fb;
  padding: 30px 0px;
}
.solution-page .content3 .layui-tab-title li {
  padding: 0 20px;
  font-size: 16px;
}
.solution-page .content3 .layui-tab-title .layui-this:after {
  left: 34%;
  width: 30%;
}
.solution-page .content3 .layui-tab-content .layui-tab-item .solution-list li {
  width: 30%;
  float: left;
  background: #ffffff;
  box-shadow: 0px 0px 16px 0px rgba(131, 131, 131, 0.08);
  margin-right: 3%;
}
.solution-page .content3 .layui-tab-content .layui-tab-item .solution-list li .top {
  display: flex;
}
.solution-page .content3 .layui-tab-content .layui-tab-item .solution-list li .top i {
  font-size: 30px;
}
.solution-page .content3 .layui-tab-content .layui-tab-item .solution-list li a {
  padding: 20px;
  min-height: 200px;
}
.solution-page .content3 .layui-tab-content .layui-tab-item .solution-list .empty {
  padding: 20px;
  min-height: 200px;
  padding-top: 6%;
  padding-left: 6%;
  color: #aaaaaa;
}
.solution-page .content3 .layui-tab-content .layui-tab-item .solution-list .empty img {
  width: 70px;
  display: inline-block;
}
.solution-detail-page .solution-banner .banner-item {
  position: relative;
}
.solution-detail-page .solution-banner .btn-contact {
  width: 100%;
  position: absolute;
  left: 0%;
  bottom: 20%;
  padding-left: 10%;
}
.solution-detail-page .solution-banner .btn-contact .layui-btn {
  display: block;
}
.solution-detail-page .solution-banner .btn-contact a {
  display: block;
  color: #fff;
}
.solution-detail-page .content a {
  display: block;
}
.solution-detail-page .content .modal-contain {
  width: 75%;
  margin: 0px auto 0 auto;
  padding: 40px 0;
}
.solution-detail-page .content1 {
  background: #fff;
}
.solution-detail-page .content1 .top-logo {
  padding: 20px 0;
}
.solution-detail-page .content1 .top-logo .layui-row {
  width: 75%;
  margin: 0px auto 0 auto;
  max-width: 1200px;
}
.solution-detail-page .content1 .top-logo .line {
  background: #ddd;
  width: 100%;
  height: 1px;
  margin: 10px 0px 20px 0px;
}
.solution-detail-page .content1 .top-logo .layui-row > div .col-cont img {
  display: block;
  width: 88px;
  margin: 0 auto;
}
.solution-detail-page .content1 .top-logo .layui-row > div .col-cont .cont {
  max-width: 200px;
  margin: 0 auto;
  font-size: 14px;
  text-align: center;
  color: #333;
}
.solution-detail-page .content2 {
  background: #f1f6fb;
  padding: 30px 0px;
}
.solution-detail-page .content2 .solution-list > li {
  width: 23.8%;
  float: left;
  background: #ffffff;
  box-shadow: 0px 0px 16px 0px rgba(131, 131, 131, 0.08);
  margin-right: 1.5%;
}
.solution-detail-page .content2 .solution-list > li .top {
  display: flex;
  align-items: center;
}
.solution-detail-page .content2 .solution-list > li .top i {
  font-size: 30px;
}
.solution-detail-page .content2 .solution-list > li a {
  padding: 20px;
  min-height: 350px;
}
.solution-detail-page .content2 .solution-list > li .tag-list {
  margin-top: 10px;
}
.solution-detail-page .content2 .solution-list > li .tag-list li {
  float: none;
  color: #666666;
  line-height: 19px;
}
.solution-detail-page .content2 .solution-list > li .tag-list li .icon {
  display: inline-block;
  width: 10px;
  height: 10px;
  background: #3ca3ff;
  margin-right: 10px;
}
.solution-detail-page .content2 .solution-list li:nth-last-of-type(1) {
  margin-right: 0px;
}
.solution-detail-page .content2 .modal-contain > a {
  font-size: 14px;
  text-align: center;
  color: #3ca3ff;
  line-height: 19px;
  margin-top: 60px;
}
.solution-detail-page .content3 {
  background: #fff;
  padding: 30px 0px;
}
.solution-detail-page .content3 .modal-contain {
  display: flex;
}
.solution-detail-page .content3 .modal-contain .left {
  padding-right: 10%;
}
.solution-detail-page .content3 .modal-contain .left .tit {
  font-size: 16px;
  color: #333333;
  line-height: 21px;
  margin-bottom: 15px;
}
.solution-detail-page .content3 .modal-contain .left .cont p {
  font-size: 14px;
  color: #666666;
  line-height: 19px;
  margin-bottom: 5px;
}
.about-us-page .solution-banner {
  position: relative;
}
.about-us-page .solution-banner .menu-tab {
  width: 100%;
  position: absolute;
  left: 0%;
  bottom: 0%;
  height: 50px;
  background: rgba(0, 0, 0, 0.5928);
  padding: 0 12.5%;
}
.about-us-page .solution-banner .menu-tab a {
  color: #fff;
  text-align: center;
  font-size: 16px;
  line-height: 50px;
  width: 49%;
  display: inline-block;
}
.about-us-page .solution-banner .menu-tab .active {
  background: #3ca3ff;
}
.about-us-page .content a {
  display: block;
}
.about-us-page .content .modal-contain {
  width: 75%;
  margin: 0px auto 0 auto;
  padding: 40px 0;
}
.about-us-page .content1 {
  background: #fff;
}
.about-us-page .content1 .top-logo {
  padding: 20px 0;
}
.about-us-page .content1 .top-logo .layui-row {
  width: 75%;
  margin: 0px auto 0 auto;
  max-width: 1200px;
}
.about-us-page .content1 .top-logo .line {
  background: #ddd;
  width: 100%;
  height: 1px;
  margin: 10px 0px 20px 0px;
}
.about-us-page .content1 .top-logo .layui-row > div .col-cont img {
  display: block;
  width: 88px;
  margin: 0 auto;
}
.about-us-page .content1 .top-logo .layui-row > div .col-cont .cont {
  max-width: 200px;
  margin: 0 auto;
  font-size: 14px;
  text-align: center;
  color: #333;
}
.about-us-page .content2 {
  background: #ffffff;
  padding: 30px 0px;
}
.about-us-page .content2 .layui-row {
  text-align: center;
}
.about-us-page .content2 .layui-row > div {
  padding-bottom: 10px;
}
.about-us-page .content2 .layui-row > div:nth-last-of-type(2) {
  border-left: 1px solid #ddd;
  border-right: 1px solid #ddd;
}
.about-us-page .content2 .layui-row .time {
  font-size: 74px;
  font-weight: bold;
  color: #333333;
  line-height: 86px;
}
.about-us-page .content3 {
  background: #f1f6fb;
  padding: 30px 0px;
}
.about-us-page .content3 .modal-contain .layui-row > div .col-cont {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 250px;
  margin: 0 auto;
}
.about-us-page .content3 .modal-contain .layui-row > div .col-cont .col-left img {
  width: 66px;
}
.about-us-page .content3 .modal-contain .layui-row > div .col-cont .col-right {
  width: 150px;
}
.about-us-page .content3 .modal-contain .layui-row > div .col-cont .col-right .title {
  font-size: 20px;
}
.about-us-page .content3 .modal-contain .layui-row > div .col-cont .col-right .cont {
  font-size: 14px;
  color: #888888;
}
.about-us-page .content3 .modal-contain > .layui-row:nth-of-type(1) {
  margin-bottom: 40px;
}
.about-us-page .content4 {
  position: relative;
}
.about-us-page .content4 .bg-cont {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  padding: 30px 0px;
}
.about-us-page .content4 .bg-cont .top-theme .tit-bg {
  color: #707070;
}
.about-us-page .content4 .bg-cont .top-theme .tit {
  color: #fff;
}
.about-us-page .content4 .bg-cont .col-cont {
  width: 75%;
  margin: 0 auto;
  min-height: 300px;
  padding: 20px 30px;
  background: #333333;
  color: #fff;
  text-align: center;
}
.about-us-page .content4 .bg-cont .col-cont .tit {
  font-size: 24px;
  color: #ffffff;
  line-height: 31px;
}
.about-us-page .content4 .bg-cont .col-cont .cont {
  font-size: 16px;
  line-height: 21px;
  margin-top: 20px;
}
.about-us-page .content5 {
  padding: 30px 0px;
  background: #f1f6fb;
}
.about-us-page .content5 .modal-contain .col-cont {
  float: left;
  width: 20%;
  text-align: center;
}
.about-us-page .content5 .modal-contain .col-cont img {
  width: 66px;
  margin: 0 auto;
}
.about-us-page .content5 .modal-contain .col-cont .title {
  font-size: 20px;
  line-height: 24px;
  margin: 15px 0px 5px 0px;
}
.about-us-page .content5 .modal-contain .col-cont .cont {
  font-size: 14px;
  line-height: 19px;
  color: #888888;
}
.about-us-page .content6 {
  padding: 30px 0px;
  background: #fff;
}
.about-us-page .content6 .news-list li {
  float: left;
  width: 20%;
}
.about-us-page .content6 .news-list li a {
  display: block;
  width: 100%;
}
.about-us-page .content6 .news-list li a .img {
  position: relative;
}
.about-us-page .content6 .news-list li a .img .gb {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
}
.about-us-page .content6 .news-list li a .cont {
  width: 100%;
  background: #ffffff;
  box-shadow: 0px 0px 26px 0px rgba(0, 0, 0, 0.08);
  font-size: 30px;
  color: #333333;
  line-height: 45px;
  text-align: center;
  position: relative;
}
.about-us-page .content6 .news-list li a .cont img {
  opacity: 0;
}
.about-us-page .content6 .news-list li a .cont .text {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  vertical-align: middle;
  padding-top: 35%;
}
.about-us-page .content6 .news-list .item1 a .cont .text {
  padding-top: 25%;
}
.about-us-page .content6 .news-list .active a .cont {
  color: #fff;
  background: #3ca3ff;
}
.about-us-page .content6 .news-list .item2,
.about-us-page .content6 .news-list .item4 {
  margin-top: 19.6%;
}
.product-service-page .service-banner-container .swiper-pagination-bullet {
  width: 20px;
  height: 4px;
  border-radius: 0;
  background: #ddd;
}
.product-service-page .service-banner-container > .swiper-pagination-bullets,
.product-service-page .service-banner-container .swiper-pagination-custom,
.product-service-page .service-banner-container .swiper-pagination-fraction {
  bottom: 40px;
}
.product-service-page .content a {
  display: block;
}
.product-service-page .content .modal-contain {
  width: 75%;
  margin: 0px auto 0 auto;
  padding: 40px 0;
}
.product-service-page .content2 {
  background: #ffffff;
  padding: 30px 0px;
}
.product-service-page .content2 .bg {
  background: #0d1924;
}
.product-service-page .content2 .layui-row {
  text-align: center;
}
.product-service-page .content2 .layui-row > div {
  position: relative;
}
.product-service-page .content2 .layui-row > div .info {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  padding: 0px 10px;
  padding-top: 35%;
  background: rgba(0, 0, 0, 0.7);
  transition: background 200ms;
  -moz-transition: background 200ms;
  /* Firefox 4 */
  -webkit-transition: background 200ms;
  /* Safari 和 Chrome */
  -o-transition: background 200ms;
  /* Opera */
}
.product-service-page .content2 .layui-row > div .info .icon {
  width: 64px;
  margin: 0 auto;
}
.product-service-page .content2 .layui-row > div .info .line {
  width: 40px;
  height: 3px;
  margin: 20px auto;
  background: #ffffff;
}
.product-service-page .content2 .layui-row > div .info .tit {
  font-size: 22px;
  color: #ffffff;
  line-height: 29px;
  margin-bottom: 20px;
}
.product-service-page .content2 .layui-row > div .info .cont {
  display: none;
  transition: display 200ms;
  -moz-transition: display 200ms;
  /* Firefox 4 */
  -webkit-transition: display 200ms;
  /* Safari 和 Chrome */
  -o-transition: display 200ms;
  /* Opera */
}
.product-service-page .content2 .layui-row > div .info .cont p {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  font-size: 16px;
  color: #ffffff;
  line-height: 24px;
}
.product-service-page .content2 .layui-row > div .info a {
  margin: 0 auto;
  margin-top: 20px;
  display: none;
  transition: display 200ms;
  -moz-transition: display 200ms;
  /* Firefox 4 */
  -webkit-transition: display 200ms;
  /* Safari 和 Chrome */
  -o-transition: display 200ms;
  /* Opera */
  width: 120px;
  height: 30px;
  line-height: 30px;
  color: #fff;
  border: 1px solid #ffffff;
}
.product-service-page .content2 .layui-row .info:hover {
  background: rgba(60, 163, 255, 0.8);
  padding-top: 20%;
}
.product-service-page .content2 .layui-row .info:hover .cont,
.product-service-page .content2 .layui-row .info:hover a {
  display: block;
}
.product-service-page .content3 {
  background: #ffffff;
  padding: 30px 0px 0px 0px;
}
.product-service-page .content3 .layui-row {
  text-align: center;
}
.product-service-page .content3 .layui-row > div {
  cursor: pointer;
}
.product-service-page .content3 .layui-row .icon {
  width: 64px;
  margin: 0 auto;
}
.product-service-page .content3 .layui-row .tit {
  font-size: 14px;
  line-height: 19px;
  margin-top: 10px;
}
.product-service-page .content3 .layui-row .active {
  color: #006ddf;
}
.product-service-page .content3 .bg {
  background: #f1f6fb;
}
.product-service-page .content3 .bg .modal-contain {
  display: flex;
}
.product-service-page .content3 .bg .left {
  width: 40%;
  max-height: 386px;
  overflow-y: auto;
}
.product-service-page .content3 .bg .left li {
  display: block;
  padding: 0px 30px 0px 0px;
  padding-bottom: 40px;
  cursor: pointer;
}
.product-service-page .content3 .bg .left li .tit {
  font-size: 34px;
  color: #333333;
  line-height: 45px;
}
.product-service-page .content3 .bg .left li .tit span {
  position: relative;
}
.product-service-page .content3 .bg .left li .tit span::after {
  content: "Hot";
  display: inline-block;
  background: #df825a;
  font-size: 18px;
  color: #ffffff;
  line-height: 24px;
  padding: 0px 6px;
  position: absolute;
  right: -45px;
  top: 0px;
}
.product-service-page .content3 .bg .left li .cont {
  font-size: 18px;
  color: #666666;
  line-height: 25px;
  margin: 20px 0;
}
.product-service-page .content3 .bg .left li a {
  width: 80px;
  height: 30px;
  line-height: 30px;
  text-align: center;
  color: #3ca3ff;
  border: 1px solid #3ca3ff;
}
.product-service-page .content3 .bg .left li:nth-last-of-type(1) {
  padding-bottom: 0px;
}
.product-service-page .content3 .bg .left .is-active {
  border-right: 5px solid #b4dcff;
}
.product-service-page .content3 .bg .left::-webkit-scrollbar {
  /*滚动条整体样式*/
  width: 5px;
  /*高宽分别对应横竖滚动条的尺寸*/
  height: 1px;
}
.product-service-page .content3 .bg .left::-webkit-scrollbar-thumb {
  /*滚动条里面小方块*/
  border-radius: 5px;
  background: #B4DCFF;
}
.product-service-page .content3 .bg .left::-webkit-scrollbar-track {
  /*滚动条里面轨道*/
  border-radius: 5px;
  background: #E7E7E7;
}
.product-service-page .content3 .bg .right-content {
  width: 60%;
  padding: 20px 0px 20px 35px;
}
.product-service-page .content3 .bg .right-content .modal-list li {
  width: 40%;
  margin-right: 30px;
  margin-bottom: 20px;
}
.product-service-page .content3 .bg .right-content .modal-list li .tit {
  font-size: 16px;
  color: #333333;
  line-height: 21px;
  margin-bottom: 5px;
}
.product-service-page .content3 .bg .right-content .modal-list li .cont {
  font-size: 14px;
  color: #aaaaaa;
  line-height: 19px;
}
.product-service-page .content3 .bg .right-content .modal-list li .btn-more {
  font-size: 14px;
  color: #3ca3ff;
  line-height: 19px;
  margin-top: 24px;
}
