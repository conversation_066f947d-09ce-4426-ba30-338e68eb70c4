
* {
    padding: 0;
    margin: 0;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
    outline: 0;
    font-family: 'Microsoft Yahei', 'Arial', 'Helvetica', 'sans-serif';
}
html, body {
    font-size: 12px;
}

input::-moz-placeholder {
    color: #999;
    font-size: 14px;
}

input::-webkit-input-placeholder {
    color: #999;
    font-size: 14px;
}

body {
    position: relative;
    -webkit-font-smoothing: antialiased;
    background-color: #ececec;
}

ul,
li {
    list-style: none;
}

input,
select,
textarea {
    font-family: 'Microsoft Yahei', 'Arial', 'Helvetica', 'sans-serif';
    border: none;
    outline: none;
}

a {
    text-decoration: none;
}

img {
    border: none;
    vertical-align: middle;
}

h1,
h2,
h3,
h4,
h5,
h6 {
    font-size: 14px;
    font-weight: normal;
}

pre {
    white-space: normal;
}

button {
    cursor: pointer;
    background-color: #fff;
    border: none;
}

em,
cite {
    font-style: normal;
}

.clearfixed:after {
    content: ".";
    clear: both;
    display: block;
    height: 0;
    overflow: hidden;
}

.clearfixed {
    zoom: 1;
}

.none {
    display: none;
}

table {
    border-collapse: collapse;
}
.noboborder{
    border: none;
}

.tl {
    text-align: left;
}

.tc {
    text-align: center;
}

.tr {
    text-align: right;
}

.fl {
    float: left;
}

.fr {
    float: right;
}

.radius1 {
    border-radius: 1px;
}

.radius2 {
    border-radius: 2px;
}

.radius3 {
    border-radius: 3px;
}

.radius4 {
    border-radius: 4px;
}

.radius5 {
    border-radius: 5px;
}

.radius10 {
    border-radius: 10px;
}

.radius-half {
    border-radius: 50%;
}

.container {
    width: 1200px;
    margin: auto;
}

.slider-lf {
    width: 185px;
}

.slider-rt {
    width: 896px;
}

.impor {
    color: #e60012;
}

.letter5 {
    letter-spacing: 5px;
}

.letter3 {
    letter-spacing: 3px;
}

.c-f {
    color: #fff;
}

.c-d {
    color: #ddd;
}

.c-9 {
    color: #999;
}

.c-8 {
    color: #888;
}

.c-7 {
    color: #777;
}

.c-65 {
    color: #656565;
}

.c-5 {
    color: #555;
}

.c-41 {
    color: #414141;
}

.c-3 {
    color: #333;
}

.c-2 {
    color: #222;
}

.c-active {
    color: #02b0f8;
}

.c-notice {
    color: #ff9801;
}

.c-a {
    color: #0bb7ff;
}

.c-pink {
    color: #ff7352;
}

.c-link {
    color: #f29b33;
}

.c-blue {
    color: #09a3e3;
}

.c-blue-006 {
    color: #0065ac;
}

.c-ff9 {
    color: #ff9000 !important
}

.f-12 {
    font-size: 12px;
}

.f-14 {
    font-size: 14px;
}

.f-15 {
    font-size: 15px;
}

.f-16 {
    font-size: 16px;
}

.f-18 {
    font-size: 18px;
}

.f-20 {
    font-size: 20px;
}

.f-22 {
    font-size: 22px;
}

.f-25 {
    font-size: 25px;
}

.f-28 {
    font-size: 28px;
}

.f-30 {
    font-size: 30px;
}

.f-52 {
    font-size: 52px;
}

.f-60 {
    font-size: 60px;
}

.f-bold {
    font-weight: bold;
}

.tr10 {
    height: 10px;
}

.tr15 {
    height: 15px;
}

.tr20 {
    height: 20px;
}

.tr30 {
    height: 30px;
}

.b-fb {
    background-color: #fbfbfb;
}

.b-ff {
    background-color: #fff;
}

.b-f8 {
    background-color: #f8f8f8;
}

.b-link {
    background-color: #f29b33;
}

.b-pink {
    background-color: #ff7352;
}

.b-blue {
    background-color: #09a3e3;
}

.b-update {
    background-color: #ff9800;
}

.pb2 {
    padding-bottom: 2px;
}

.pb5 {
    padding-bottom: 5px;
}

.pb10 {
    padding-bottom: 10px;
}

.pb14 {
    padding-bottom: 14px;
}

.pb15 {
    padding-bottom: 15px;
}

.pb16 {
    padding-bottom: 16px;
}

.pb27 {
    padding-bottom: 27px;
}

.pb30 {
    padding-bottom: 30px;
}

.pb34 {
    padding-bottom: 34px;
}

.pb47 {
    padding-bottom: 47px;
}

.pb61 {
    padding-bottom: 61px;
}

.pb63 {
    padding-bottom: 63px;
}

.pb81 {
    padding-bottom: 81px;
}

.pl10 {
    padding-left: 10px;
}

.pl12 {
    padding-left: 12px;
}

.pl15 {
    padding-left: 15px;
}
.pl20 {
    padding-left: 20px;
}

.pl22 {
    padding-left: 22px;
}

.pl208 {
    padding-left: 208px;
}

.pr6 {
    padding-right: 6px;
}

.pr10 {
    padding-right: 10px;
}

.pr12 {
    padding-right: 12px;
}

.pr15 {
    padding-right: 15px;
}

.pt2 {
    padding-top: 2px;
}

.pt5 {
    padding-top: 5px;
}

.pt10 {
    padding-top: 10px;
}

.pt15 {
    padding-top: 15px;
}

.pt16 {
    padding-top: 16px;
}

.pt18 {
    padding-top: 18px;
}

.pt20 {
    padding-top: 20px;
}

.pt25 {
    padding-top: 25px;
}

.pt34 {
    padding-top: 34px;
}

.pt40 {
    padding-top: 40px;
}

.pt89 {
    padding-top: 89px;
}

.mt2 {
    margin-top: 2px;
}

.mt5 {
    margin-top: 5px;
}

.mt6 {
    margin-top: 6px;
}

.mt10 {
    margin-top: 10px;
}

.mt20 {
    margin-top: 20px;
}

.mt22 {
    margin-top: 22px;
}

.mt25 {
    margin-top: 25px;
}

.mt30 {
    margin-top: 30px;
}

.mt42 {
    margin-top: 42px;
}

.mb2 {
    margin-bottom: 2px;
}

.mb5 {
    margin-bottom: 5px;
}

.mb10 {
    margin-bottom: 10px;
}

.mb15 {
    margin-bottom: 15px;
}

.mb20 {
    margin-bottom: 20px;
}

.mb24 {
    margin-bottom: 24px;
}

.mb30 {
    margin-bottom: 30px;
}

.mb36 {
    margin-bottom: 36px;
}

.mb38 {
    margin-bottom: 38px;
}

.mb39 {
    margin-bottom: 39px;
}

.mb45 {
    margin-bottom: 45px;
}

.mb59 {
    margin-bottom: 59px;
}

.ml10 {
    margin-left: 10px;
}

.mr2 {
    margin-right: 2px;
}

.mr5 {
    margin-right: 5px;
}

.mr10 {
    margin-right: 10px;
}

.mr16 {
    margin-right: 16px;
}

.mr20 {
    margin-right: 20px;
}

.mr24 {
    margin-right: 24px;
}

.mr30 {
    margin-right: 30px;
}

.mr39 {
    margin-right: 39px;
}

.mr40 {
    margin-right: 40px;
}

.base-input {
    border: 1px solid #ddd;
    padding-left: 20px;
}

.base-input:focus {
    border-color: #09a3e3;
}

.base-btn {
    display: inline-block;
    width: 80px;
    height: 35px;
    line-height: 35px;
    text-align: center;
    border-radius: 5px;
}

.mid-btn {
    display: inline-block;
    width: 120px;
    height: 35px;
    line-height: 35px;
    text-align: center;
    border-radius: 5px;
}

.control-box {
    position: relative;
}

.form-tip {
    display: none;
    position: absolute;
    top: 11px;
    left: 315px;
    min-width: 300px;
    padding-left: 20px;
}

.form-tip em {
    position: absolute;
    left: 0;
    top: -1px;
    width: 16px;
    height: 16px;
    background-position: center center;
    background-repeat: no-repeat;
}

.form-tip.error-tip {
    display: block;
    color: #ff9800;
}

.form-tip.error-tip em {
    background-image: url(../images/mistake.png);
}

.form-tip.ok-tip {
    display: block;
    color: #41c004;
}

.form-tip.ok-tip em {
    background-image: url(../images/ok.png);
}

.form-group .control-box input.error-ipt-tip {
    border: 1px solid #f00;
}

.form-group .control-box input.ok-ipt-tip {
    border: 1px solid #0a1;
}

.bb {
    border-bottom: 1px solid #ddd;
}

.bt {
    border-top: 1px solid #ddd;
}

.w210 {
    width: 210px;
}

input:-webkit-autofill {
    -webkit-animation: autofill-fix 1s infinite;
}

@-webkit-keyframes autofill-fix {
    from {
        background-color: transparent
    }
    to {
        background-color: transparent
    }
}