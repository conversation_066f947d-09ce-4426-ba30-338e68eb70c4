/**
 * Created by Administrator on 2016/12/14.
 */
window.console = window.console || {};
console.log || (console.log = opera.postError);

/*
 * JDB 1.0
 * version: 1.0
 * http://www.baisui.la
 * Copyright 2016, 2018 mohism [ <EMAIL> ]
 *
 */
// Handlebars.registerHelper("if_eq", function(v1, v2, opts) {
//   if (v1 == v2) return opts.fn(this);
//   else return opts.inverse(this);
// });
// Handlebars.registerHelper("addOne", function(index) {
//   return index + 1;
// });

(function($) {
  CRMJS.Website = {};
  CRMJS.WebsiteStart = {
    GoStart: function() {
      // 首页
      if($(".home-page").length!=0){
        // swiperBanner轮播
        var mySwiper1 = new Swiper(".home-banner-swiper-container", {
          autoplay: {
            disableOnInteraction: false
          },
          pagination: {
            el: ".home-banner-swiper-container .swiper-pagination"
          }
        });
        var mySwiper2 = new Swiper(".home-News-swiper-container", {
          autoplay: {
              disableOnInteraction: false,
          },
          slidesPerView: 4.5,
          slidesPerGroup : 4,
          scrollbar: {
            el: ".home-News-swiper-container .swiper-scrollbar",
            hide: true
          },
          navigation: {
            nextEl: ".home-News-swiper-container .swiper-button-next",
            prevEl: ".home-News-swiper-container .swiper-button-prev"
          }
        });
        // 滚动控制
        $(window).scroll(function() {
          //为了保证兼容性，这里取两个值，哪个有值取哪一个
          //scrollTop就是触发滚轮事件时滚轮的高度
          var scrollTop = document.documentElement.scrollTop || document.body.scrollTop;
          var swiperHeight=$(".home-banner-swiper-container").height();
          var h2=swiperHeight+$(".home-page .content1 .top-logo").height();
          var _header=$(".home-page .header");
          if(scrollTop>swiperHeight){
            if(scrollTop>(swiperHeight+h2)){
              _header.css("background","rgba(13,25,36,1)");
            }else{
              _header.css("background","rgba(13,25,36,0.5)");
            }
          }else{
            _header.css("background","rgba(13,25,36,0)");
          }
         
        })
        // 切换视频
        $(document).on("click",".home-page .video-list li",function(){
          var _index=$(this).attr("data-index");
          $(".home-page .video-list li").removeClass("active");
          $(this).addClass("active");
        })
        // 切换系统介绍内容
        $(document).on("click",".home-page .system-list li",function(){
          var _index=$(this).attr("data-index");
          $(".home-page .system-list li").removeClass("active");
          $(this).addClass("active");
        })
        
      }
      // 关于我们页面
      if($(".about-us-page").length!=0){
        $(document).on("click",".menu-tab a",function(){
          var id=$(this).attr("data-id");
          $(".menu-tab a").removeClass("active");
          $(this).addClass("active");
          if(id=="1"){
            $(".about-us-page .advantage").hide();
            $(".about-us-page .company-profile").show();
          }else{
            $(".about-us-page .company-profile").hide();
            $(".about-us-page .advantage").show();
          }
        })
      }
      // 帮助页面
      if($(".help-page").length!=0 ||$(".help-video").length!=0||$(".help-person").length!=0||$(".help-problem").length!=0){
        $(document).on("click",".modal-cont .tab-cont li p",function(){
          var p_li=$(this).parent("li").find(".detail");
          if(p_li.css("display")=="none"){
            $(".modal-cont .tab-cont li .detail").hide();
            p_li.show();
          }else{
            p_li.hide();
          }
        })
      }
      // 产品服务页面
      if($(".product-service-page").length!=0){
        $(document).on("click",".product-service-page .content3 .layui-row .item",function(){
          var index=$(this).attr("data-index");
          $(".product-service-page .content3 .layui-row .item").removeClass("active");
          $(this).addClass("active");
        })
      }
    }
  };
  $(document).ready(function() {
    CRMJS.WebsiteStart.GoStart();
    // 产品页面点击显示播放视频
    $(".product-page .btn-video-paly").on("click",function(){
      $(".product-page .frame-page").show();
    })
    // 弹框关闭
    $(".frame-page .btn-colse").on("click",function(){
      $(this).parents(".frame-page").hide();
    })
  });
})(jQuery);
