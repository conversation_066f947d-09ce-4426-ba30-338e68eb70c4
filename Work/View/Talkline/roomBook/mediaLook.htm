<!DOCTYPE HTML>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>吉的堡 名师课堂在线视频</title>
    <link rel="shortcut icon" href="/favicon.ico">
    <!-- 预加载 -->
    <link href="{$CssUrl}editor-awesome.min.css" rel="stylesheet">
    <link href="{$CssUrl}bootstrap.min.css" rel="stylesheet" type="text/css">
    <link href="{$CssUrl}font-awesome.min.css" rel="stylesheet" type="text/css">
    <link href="{$CssUrl}jquery.sidr.light.css" rel="stylesheet" type="text/css">
    <link href="{$CssUrl}jquery.datetimepicker.css" rel="stylesheet" type="text/css">
    <link href="{$CssUrl}jquery.easydropdown.css" rel="stylesheet" type="text/css">
    <link href="{$PluginsUrl}froala-editor/froala_editor.min.css" rel="stylesheet" type="text/css">
    <link href="{$PluginsUrl}jquery-chosen/jquery-chosen.css" rel="stylesheet" type="text/css">
    <!-- diy -->
    <link href="{$CssUrl}style.css" rel="stylesheet" type="text/css">
    <link href="{$CssUrl}pages.css" rel="stylesheet" type="text/css">
    <link href="{$CssUrl}ls.css" rel="stylesheet" type="text/css">
    <link href="{$CssUrl}int.timepicker.css" rel="stylesheet" type="text/css">
    <link href="{$CssUrl}jquery.monthpicker.css" rel="stylesheet" type="text/css">
    <link rel="stylesheet" href="https://g.alicdn.com/de/prismplayer/2.8.7/skins/default/aliplayer-min.css" />
    <script type="text/javascript" charset="utf-8" src="https://g.alicdn.com/de/prismplayer/2.8.7/aliplayer-min.js"></script>
</head>

<body>

<div class="page-container row condensed">
    <div class="page-sidebar c-f" id="main-menu">
        <div class="user-info tc">
            <div class="img">
                <a href="/"><img src="/Work/Static/Manage/images/imghead.png" width="69" height="69"></a>
            </div>
            <div class="text">
                <p class="f18">{$istaffer.user_name}</p>
            </div>
        </div>
        <div class="side-seprator"></div>
        <ul>
            <li {if $u == 'roomBook'}class="cur open"{/if}>
            <a href="javascript:;">
                <span class="icon glyphicon glyphicon-flag"></span>
                <span class="title">名师课堂</span>
            </a>
            <ul class="sub-menu" {if $u == 'roomBook'}style=" display: block;"{/if}>
            <li {if $t == 'Home'}class="cur"{/if}>
            <a href="/roomBook/Home">
                <span class="title">课程视频</span>
            </a>
            </li>
        </ul>
        </li>
        </ul>
    </div>
    <div class="footer-widget">
        <p align="" class="f16"><a href="/Heatstart?c=outlogin" class="cp c-9"><span class="glyphicon glyphicon-off"></span></a></p>
    </div>
    <div class="page-content">
        <div class="header navbar navbar-inverse">
            <div class="navbar-inner">
                <div class="header-quick-nav">
                    <div class="pull-left">
                        <ul class="nav quick-section f16">
                            <li class="quicklinks"><a class=" c-8 cp" id="layout-condensed-toggle"> <span class="icon-menu"></span> </a></li>
                        </ul>
                    </div>
                    <div class="pull-right">
                        <ul class="nav quick-section ">
                            <li>{$iparenter.class_cnname}---{$iparenter.student_cnname}({$iparenter.student_enname})</li>
                            <li class="quicklinks">
                                <a class="pull-right c-8 f16 " id="user-options">
                                    <span class="glyphicon glyphicon-cog"></span>
                                </a>
                            </li>
                            <li class="quicklinks"> <span class="h-seperate"></span></li>
                            <li class="quicklinks">
                                <a href="/Heatstart?c=outroom" class="f16 c-8">
                                    <div class="glyphicon glyphicon-log-out "></div>
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <div class="mx20 breadcrumb-w">
            <div class="breadcrumb fl">
                <span>{$dataVar.classcode_branch} 名师课堂</span>
            </div>
            <a href="javascript:" class="fr bakFromurl mt6"><i class="icon-back mr5"></i>返回</a>
            <div class="clear"></div>
        </div>
        <div class="content">
            <div id="container">
                <div class="row">
                    <div class="bg-f">
                        <div class="p20 f14">
                            <div class="form-group col-md-8">
                                <div class="prism-player" id="player-con">
                                    {if !$dataVar}
                                    <a style="display: block; height: 300px; text-align: center; width: 100%; background: #fff;">你没有权限访问此视频！</a>
                                    {/if}
                                </div>
                            </div>
                            <div class="form-group col-md-4" style="height: 700px; overflow-y: scroll;">
                                <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table tc table-hover table-bordered">
                                    <thead>
                                    <tr>
                                        <th>单元名称</th>
                                        <th>视频播放</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    {if $mediaList}
                                    {foreach from=$mediaList item=mediaOne}
                                    <tr id="list-{$mediaOne.media_id}">
                                        <td>{$mediaOne.media_name}</td>
                                        <td>
                                            {if $mediaOne.media_id == $dataVar.media_id}
                                            <a class="btn btn-success btn-sm">正在播放</a>
                                            {else}
                                            <a href="/{$u}/mediaLook?media_id={$mediaOne.media_id}" class="btn btn-primary btn-sm">浏览视频</a>
                                            {/if}
                                        </td>
                                    </tr>
                                    {/foreach}
                                    {/if}
                                    </tbody>
                                </table>
                            </div>
                            <div class="clear"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{include file="jscom.htm"}
{if $dataVar}
<script>
    var player = new Aliplayer({
                "id": "player-con",
                "source": "{$dataVar.media_url}",
                "width": "100%",
                "height": "700px",
                "autoplay": true,
                "isLive": false,
                "cover": "{$dataVar.media_coverimg}",
                "rePlay": false,
                "playsinline": true,
                "preload": true,
                "controlBarVisibility": "hover",
                "useH5Prism": true,
                "skinLayout": [
                    {
                        "name": "bigPlayButton",
                        "align": "blabs",
                        "x": 30,
                        "y": 80
                    },
                    {
                        "name": "H5Loading",
                        "align": "cc"
                    },
                    {
                        "name": "controlBar",
                        "align": "blabs",
                        "x": 0,
                        "y": 0,
                        "children": [
                            {
                                "name": "progress",
                                "align": "blabs",
                                "x": 0,
                                "y": 44
                            },
                            {
                                "name": "playButton",
                                "align": "tl",
                                "x": 15,
                                "y": 12
                            },
                            {
                                "name": "timeDisplay",
                                "align": "tl",
                                "x": 10,
                                "y": 7
                            },
                            {
                                "name": "fullScreenButton",
                                "align": "tr",
                                "x": 10,
                                "y": 12
                            },
                            {
                                "name": "volume",
                                "align": "tr",
                                "x": 5,
                                "y": 10
                            }
                        ]
                    }
                ]
            }, function (player) {
                console.log("The player is created");
            }
    );
</script>
{/if}
</body>
</html>
