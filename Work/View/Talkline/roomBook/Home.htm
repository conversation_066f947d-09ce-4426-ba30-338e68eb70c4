<!DOCTYPE HTML>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>吉的堡 名师课堂在线视频</title>
    <link rel="shortcut icon" href="/favicon.ico">
    <!-- 预加载 -->
    <link href="{$CssUrl}editor-awesome.min.css" rel="stylesheet">
    <link href="{$CssUrl}bootstrap.min.css" rel="stylesheet" type="text/css">
    <link href="{$CssUrl}font-awesome.min.css" rel="stylesheet" type="text/css">
    <link href="{$CssUrl}jquery.sidr.light.css" rel="stylesheet" type="text/css">
    <link href="{$CssUrl}jquery.datetimepicker.css" rel="stylesheet" type="text/css">
    <link href="{$CssUrl}jquery.easydropdown.css" rel="stylesheet" type="text/css">
    <link href="{$PluginsUrl}froala-editor/froala_editor.min.css" rel="stylesheet" type="text/css">
    <link href="{$PluginsUrl}jquery-chosen/jquery-chosen.css" rel="stylesheet" type="text/css">
    <!-- diy -->
    <link href="{$CssUrl}style.css" rel="stylesheet" type="text/css">
    <link href="{$CssUrl}pages.css" rel="stylesheet" type="text/css">
    <link href="{$CssUrl}ls.css" rel="stylesheet" type="text/css">
    <link href="{$CssUrl}int.timepicker.css" rel="stylesheet" type="text/css">
    <link href="{$CssUrl}jquery.monthpicker.css" rel="stylesheet" type="text/css">
</head>

<body>

<div class="page-container row condensed">
    <div class="page-sidebar c-f" id="main-menu">
        <div class="user-info tc">
            <div class="img">
                <a href="/"><img src="/Work/Static/Manage/images/imghead.png" width="69" height="69"></a>
            </div>
            <div class="text">
                <p class="f18">{$istaffer.user_name}</p>
            </div>
        </div>
        <div class="side-seprator"></div>
        <ul>
            <li {if $u == 'roomBook'}class="cur open"{/if}>
                <a href="javascript:;">
                    <span class="icon glyphicon glyphicon-flag"></span>
                    <span class="title">名师课堂</span>
                </a>
                <ul class="sub-menu" {if $u == 'roomBook'}style=" display: block;"{/if}>
                    <li {if $t == 'Home'}class="cur"{/if}>
                        <a href="/roomBook/Home">
                            <span class="title">课程视频</span>
                        </a>
                    </li>
                </ul>
            </li>
        </ul>
    </div>
    <div class="footer-widget">
        <p align="" class="f16"><a href="/Heatstart?c=outlogin" class="cp c-9"><span class="glyphicon glyphicon-off"></span></a></p>
    </div>
    <div class="page-content">
        <div class="header navbar navbar-inverse">
            <div class="navbar-inner">
                <div class="header-quick-nav">
                    <div class="pull-left">
                        <ul class="nav quick-section f16">
                            <li class="quicklinks"><a class=" c-8 cp" id="layout-condensed-toggle"> <span class="icon-menu"></span> </a></li>
                        </ul>
                    </div>
                    <div class="pull-right">
                        <ul class="nav quick-section ">
                            <li>{$iparenter.class_cnname}---{$iparenter.student_cnname}({$iparenter.student_enname})</li>
                            <li class="quicklinks">
                                <a class="pull-right c-8 f16 " id="user-options">
                                    <span class="glyphicon glyphicon-cog"></span>
                                </a>
                            </li>
                            <li class="quicklinks"> <span class="h-seperate"></span></li>
                            <li class="quicklinks">
                                <a href="/Heatstart?c=outroom" class="f16 c-8">
                                    <div class="glyphicon glyphicon-log-out "></div>
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <div class="mx20 breadcrumb-w">
            <div class="breadcrumb fl">
                <span>名师课堂查看</span>
            </div>
            <a href="javascript:" class="fr bakFromurl mt6"><i class="icon-back mr5"></i>返回</a>
            <div class="clear"></div>
        </div>
        <div class="content">
            <div id="container">
                <div class="row">
                    <div class="bg-f">
                        <div class="p20 f14">
                            {if $mediaList}
                            {foreach from=$mediaList item=mediaOne}
                            <div class="col-md-3 mb16" style="padding-right:0;">
                                <div class="bg-f index-weblist">
                                    <div class="img">
                                        <img src="{$mediaOne.media_coverimg}" width="100%">
                                    </div>
                                    <div class="text p10">
                                        <h2 class="f18 mb10">
                                            <a>{$mediaOne.media_name}</a>
                                            <a href="/roomBook/mediaLook?media_id={$mediaOne.media_id}" class="btn fr btn-warning btn-sm">浏览视频</a>
                                        </h2>
                                    </div>
                                </div>
                            </div>
                            {/foreach}
                            {/if}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</body>
</html>
