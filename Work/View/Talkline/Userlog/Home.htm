<!DOCTYPE HTML>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    {include file="key.htm"}
</head>

<body>

<div class="page-container row condensed">
    {include file="page-sidebar.htm"}
    {include file="footer.htm"}
    <div class="page-content">
        {include file="header.htm"}
        <div class="mx20 breadcrumb-w">
            <div class="breadcrumb fl">
                <span>用户操作日志</span>
            </div>
            <a href="javascript:" class="fr bakFromurl mt6"><i class="icon-back mr5"></i>返回</a>
            <div class="clear"></div>
        </div>
        <div class="content">
            <div id="container">
                <div class="row">
                    <div class="bg-f">
                        <div class="p20 f14">
                            <div class="form-group">
                                <form action="{$u}" method="get" accept-charset="utf-8">
                                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                        <tr>
                                            <td width="25%" class="pr10">
                                                <input name="keyword" class="form-control input-sm" placeholder="请输入日志关键字" value="{$datatype['keyword']}" type="text">
                                            </td>
                                            <td width="9%" class="pr10">
                                                <input name="starttime" id="starttime" value="{$datatype['starttime']}" class="form-control input-sm form_datetime" autocomplete="off" placeholder="开始时间" type="text">
                                            </td>
                                            <td width="9%" class="pr10">
                                                <input name="endtime" id="endtime" value="{$datatype['endtime']}" class="form-control input-sm form_datetime" autocomplete="off" placeholder="结束时间" type="text">
                                            </td>
                                            <td width="30%">
                                                <button type="submit" class="btn btn-default ml10">搜索</button>
                                            </td>
                                            <td>
                                            </td>
                                        </tr>
                                    </table>
                                </form>
                            </div>
                            <form role="form" action="/{$u}?c=BatchUser" method="post" enctype="application/x-www-form-urlencoded" class="AjaxForm">
                                <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table tc table-hover table-bordered">
                                    <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>操作人</th>
                                        <th>模块</th>
                                        <th>操作类型</th>
                                        <th>操作IP</th>
                                        <th>时间</th>
                                        <th>操作</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    {if $dataList}
                                    {foreach from=$dataList item=dataVar}
                                    <tr>
                                        <td>{$dataVar.userlog_id}</td>
                                        <td>{$dataVar.staffer_cnname}</td>
                                        <td>{$dataVar.userlog_module}</td>
                                        <td>{$dataVar.userlog_type}</td>
                                        <td>{$dataVar.userlog_ip}</td>
                                        <td>{$dataVar.userlog_time|date_format:'%Y-%m-%d %H:%M:%S'}</td>
                                        <td><a href="/{$u}/Look?userlog_id={$dataVar.userlog_id}" title="{$dataVar.userlog_content}">查看</a></td>
                                    </tr>
                                    {/foreach}
                                    {/if}
                                    </tbody>
                                </table>
                            </form>
                            <div class="pagemenu">{$pagelist}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{include file="jscom.htm"}
<script src="{$PluginsUrl}jquery-form/jquery.form.js" type="text/javascript"></script>
<script src="{$JsUrl}int.form.js" type="text/javascript"></script>
</body>
</html>
