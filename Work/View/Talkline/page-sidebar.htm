<div class="page-sidebar c-f" id="main-menu">
    <div class="user-info tc">
        <div class="img">
            <a href="/"><img src="/Work/Static/Manage/images/imghead.png" width="69" height="69"></a>
        </div>
        <div class="text">
            <p class="f18">{$istaffer.user_name}</p>
        </div>
    </div>
    <div class="side-seprator"></div>
    <ul>
        <li {if $u == 'Linerooms'}class="cur open"{/if}>
            <a href="javascript:;">
                <span class="icon glyphicon glyphicon-th-large"></span>
                <span class="title">教室管理</span>
            </a>
            <ul class="sub-menu" {if $u == 'Linerooms'}style=" display: block;"{/if}>
                <li {if $t == 'Home'}class="cur"{/if}>
                    <a href="/Linerooms/Home">
                        <span class="title">教室管理</span>
                    </a>
                </li>
                <li {if $t == 'Recordlist'}class="cur"{/if}>
                <a href="/Linerooms/Recordlist">
                    <span class="title">录制文件</span>
                </a>
                </li>
                <li {if $t == 'Linehour'}class="cur"{/if}>
                <a href="/Linerooms/Linehour">
                    <span class="title">校线上课时管理</span>
                </a>
                </li>
            </ul>
        </li>
        {if $istaffer.company_id == '8888'}
        <li {if $u == 'lineCourse'}class="cur open"{/if}>
            <a href="javascript:;">
                <span class="icon glyphicon glyphicon-road"></span>
                <span class="title">风咏网课</span>
            </a>
            <ul class="sub-menu" {if $u == 'lineCourse'}style=" display: block;"{/if}>
                <li {if $t == 'Lineclass' || $t == 'Hourrooms' || $t == 'AddLineclass' || $t == 'EditLineclass' || $t == 'classOneStudy'}class="cur"{/if}>
                    <a href="/lineCourse/Lineclass">
                        <span class="title">班课管理</span>
                    </a>
                </li>
                <li {if $t == 'classHour' || $t == 'hourroomsLook' || $t == 'hourOneStudy' }class="cur"{/if}>
                    <a href="/lineCourse/classHour">
                        <span class="title">课时管理</span>
                    </a>
                </li>
                <li {if $t == 'Hourstudy' || $t == 'ImportStudy'}class="cur"{/if}>
                    <a href="/lineCourse/Hourstudy">
                        <span class="title">学员管理</span>
                    </a>
                </li>
                <li {if $t == 'Lineparenter'}class="cur"{/if}>
                    <a href="/lineCourse/Lineparenter">
                        <span class="title">学员家长</span>
                    </a>
                </li>
            </ul>
        </li>
        {/if}
        {if $istaffer.company_id == '10001'}
        <li {if $u == 'Reimburse'}class="cur open"{/if}>
            <a href="javascript:;">
                <span class="icon glyphicon glyphicon-euro"></span>
                <span class="title">校区退费管理</span>
            </a>
            <ul class="sub-menu" {if $u == 'Reimburse'}style=" display: block;"{/if}>
            <li {if $t == 'Home'}class="cur"{/if}>
            <a href="/Reimburse/Home">
                <span class="title">退费管理</span>
            </a>
            </li>
            </ul>
        </li>
        {/if}
        <li {if $u == 'Inclass'}class="cur open"{/if}>
            <a href="javascript:;">
                <span class="icon glyphicon glyphicon-flag"></span>
                <span class="title">名师课堂</span>
            </a>
            <ul class="sub-menu" {if $u == 'Inclass'}style=" display: block;"{/if}>
                <li {if $t == 'Home'}class="cur"{/if}>
                    <a href="/Inclass/Home">
                        <span class="title">视频查看</span>
                    </a>
                </li>
            </ul>
        </li>
        <li {if $u == 'Financial'}class="cur open"{/if}>
            <a href="javascript:;">
                <span class="icon icon-tjm"></span>
                <span class="title">财务管理</span>
            </a>
            <ul class="sub-menu" {if $u == 'Financial'}style=" display: block;"{/if}>
                <li {if $t == 'Home'}class="cur"{/if}>
                    <a href="/Financial/Home">
                        <span class="title">账户交易明细</span>
                    </a>
                </li>
                <li {if $t == 'Expend'}class="cur"{/if}>
                    <a href="/Financial/Expend">
                        <span class="title">拓课云流量明细</span>
                    </a>
                </li>
                <li {if $t == 'Linerooms'}class="cur"{/if}>
                <a href="/Financial/Linerooms">
                    <span class="title">拓课云消耗统计</span>
                </a>
                </li>
                <li {if $t == 'Yunshurooms' || $t == 'Ysexpend'}class="cur"{/if}>
                <a href="/Financial/Yunshurooms">
                    <span class="title">云枢消耗统计</span>
                </a>
                </li>
            </ul>
        </li>
        {if $istaffer.company_id == '8888' AND $istaffer.account_class == '1'}
        <li {if $u == 'Supervise'}class="cur open"{/if}>
            <a href="javascript:;">
                <span class="icon glyphicon glyphicon-euro"></span>
                <span class="title">监管管理</span>
            </a>
            <ul class="sub-menu" {if $u == 'Supervise'}style=" display: block;"{/if}>
                <li {if $t == 'Home'}class="cur"{/if}>
                <a href="/Supervise/Home">
                    <span class="title">账户交易明细</span>
                </a>
                </li>
                <li {if $t == 'Student'}class="cur"{/if}>
                <a href="/Supervise/Student">
                    <span class="title">学员明细</span>
                </a>
                </li>
                <li {if $t == 'Class'}class="cur"{/if}>
                <a href="/Supervise/Class">
                    <span class="title">班级明细</span>
                </a>
                </li>
            </ul>
        </li>
        {/if}
        <li {if $u == 'Channer' or $u == 'Channergroup'}class="cur open"{/if}>
            <a href="javascript:;"><span class="icon icon-hst"></span><span class="title">系统设置</span></a>
            <ul class="sub-menu">
                <li {if $u == 'Userlog'}class="cur"{/if}>
                <a href="/Userlog">
                    <span class="title">操作日志</span>
                </a>
                </li>
            </ul>
        </li>
    </ul>
</div>