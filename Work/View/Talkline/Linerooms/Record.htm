<!DOCTYPE HTML>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    {include file="key.htm"}
</head>

<body>

<div class="page-container row condensed">
    {include file="page-sidebar.htm"}
    {include file="footer.htm"}
    <div class="page-content">
        {include file="header.htm"}
        <div class="mx20 breadcrumb-w">
            <div class="breadcrumb fl">
                <span>网课教室管理</span>
            </div>
            <a href="javascript:" class="fr bakFromurl mt6"><i class="icon-back mr5"></i>返回</a>
            <div class="clear"></div>
        </div>
        <div class="content">
            <div id="container">
                <div class="row">
                    <div class="bg-f">
                        <div class="p20 f14">
                            <div class="form-group">
                                <form action="/{$u}/{$t}" method="get" accept-charset="utf-8" id="RoomsForm">
                                    <input type="hidden" value="{$dataVar.linerooms_id}" name="linerooms_id">
                                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                        <tr>
                                            <td width="10%">检索房间录制记录：</td>
                                            <td width="8%" class="pr10">
                                                <input name="linerooms_name" id="linerooms_name" value="{$dataVar.linerooms_name}" class="form-control input-sm"type="text">
                                            </td>
                                            <td width="8%" class="pr10">
                                                <input name="starttime" id="starttime" value="{$datatype.starttime|date_format:'%Y-%m-%d'}" class="form-control input-sm form_datetime" placeholder="网课开始时间" type="text">
                                            </td>
                                            <td width="8%" class="pr10">
                                                <input name="endtime" id="endtime" value="{$datatype.endtime|date_format:'%Y-%m-%d'}" class="form-control input-sm form_datetime" placeholder="网课结束时间" type="text">
                                            </td>
                                            <td width="30%">
                                                <button type="submit" id="FromSubmit" class="btn btn-primary ml10"><span class="glyphicon glyphicon-search c-f"></span> 搜索</button>
                                            </td>
                                            <td></td>
                                        </tr>
                                    </table>
                                </form>
                            </div>
                            <form role="form" action="/{$u}?c=batchWork" method="post" enctype="application/x-www-form-urlencoded" class="AjaxForm">
                                <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table tc table-hover table-bordered">
                                    <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>教室号</th>
                                        <th>录制时长</th>
                                        <th>文件大小</th>
                                        <th>回放地址</th>
                                        <th>视频状态</th>
                                        <th>上课时间</th>
                                        <th>操作</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    {if $recordList}
                                    {foreach from=$recordList item=dataVar key=key}
                                    <tr id="list-{$dataVar.recordid}">
                                        <td>{$dataVar.key+1}</td>
                                        <td>{$dataVar.serial}</td>
                                        <td>{$dataVar.duration}分钟</td>
                                        <td>{$dataVar.size}</td>
                                        <td>{$dataVar.playpath}</td>
                                        <td>{if $dataVar.state == '0'}有效{else}删除{/if}</td>
                                        <td>{$dataVar.starttime|date_format:'%Y-%m-%d %H:%M:%S'}</td>
                                        <td align="left">
                                            <a href="{$dataVar.playpath}" target="_blank" class="btn btn-primary btn-sm">
                                                浏览录制视频</a>
                                        </td>
                                    </tr>
                                    {/foreach}
                                    {/if}
                                    </tbody>
                                </table>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{include file="jscom.htm"}
{literal}
<script>
    $(document).ready(function() {
        //体检渠道管理--销售统计  导出出订单
        $("#FromSubmit").click(function (e) {
            $("#RoomsForm").attr("action", "/Linerooms/Record");
            $("#RoomsForm").attr("method", "get");
            $("#RoomsForm").submit();
        });
    })
</script>
{/literal}
<script src="{$PluginsUrl}jquery-form/jquery.form.js" type="text/javascript"></script>
<script src="{$JsUrl}int.form.js" type="text/javascript"></script>
</body>
</html>
