<!DOCTYPE HTML>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    {include file="key.htm"}
</head>

<body>

<div class="page-container row condensed">
    {include file="page-sidebar.htm"}
    {include file="footer.htm"}
    <div class="page-content">
        {include file="header.htm"}
        <div class="mx20 breadcrumb-w">
            <div class="breadcrumb fl">
                <span>网课教室管理</span>
            </div>
            <a href="javascript:" class="fr bakFromurl mt6"><i class="icon-back mr5"></i>返回</a>
            <div class="clear"></div>
        </div>
        <div class="content">
            <div id="container">
                <div class="row">
                    <div class="bg-f">
                        <div class="p20 f14">
                            <div class="form-group">

                                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                        <tr>
                                            <td width="5%">搜索：</td>
                                            <form action="/{$u}/Linehour" method="get" accept-charset="utf-8" id="RoomsForm">
                                            <td width="15%">
                                                <select name="school_branch" id="school_branch" class="form-control chosen-select">
                                                    <option value="">请选择校园</option>
                                                    {if $istaffer.schoolList}
                                                    {foreach from=$istaffer.schoolList item=schoolOne}
                                                    <option value="{$schoolOne.school_branch}" {if $schoolOne.school_branch == $datatype.school_branch}selected{/if}>{$schoolOne.school_cnname}</option>
                                                    {/foreach}
                                                    {/if}
                                                </select>
                                            </td>
                                            <td width="8%" class="pr10">
                                                <select name="linerooms_isdel" id="linerooms_isdel" class="form-control">
                                                    <option value="0" {if $datatype.linerooms_isdel == '0'}selected{/if}>有效教室</option>
                                                    <option value="1" {if $datatype.linerooms_isdel == '1'}selected{/if}>过期教室</option>
                                                    <option value="2" {if $datatype.linerooms_isdel == '2'}selected{/if}>删除教室</option>
                                                </select>
                                            </td>
                                            <td width="10%" class="pr10">
                                                <select name="linerooms_fromclass" id="linerooms_fromclass" class="form-control">
                                                    <option value="">请选择平台</option>
                                                    <option value="0" {if $datatype.linerooms_fromclass == '0'}selected{/if}>拓课云平台</option>
                                                    <option value="1" {if $datatype.linerooms_fromclass == '1'}selected{/if}>云枢平台</option>
                                                </select>
                                            </td>
                                            <td width="9%" class="pr10">
                                                <input name="editstarttime" id="editstarttime" value="{$datatype['editstarttime']}" class="form-control input-sm form_datetime" autocomplete="off" placeholder="上课开始时间" type="text">
                                            </td>
                                            <td width="9%" class="pr10">
                                                <input name="editendtime" id="editendtime" value="{$datatype['editendtime']}" class="form-control input-sm form_datetime" autocomplete="off" placeholder="上课结束时间" type="text">
                                            </td>
                                            <td width="15%" class="pr10"><input name="keyword" class="form-control input-sm" placeholder="请输入教室名称/教室号" value="{$datatype['keyword']}" type="text"></td>
                                            <td width="5%" class="pr10"><input name="linerooms_maxvideo" class="form-control input-sm" placeholder="上台数" value="{$datatype['linerooms_maxvideo']}" type="text"></td>
                                            <td width="20%">
                                                <button type="submit" id="FromSubmit" class="btn btn-primary ml10"><span class="glyphicon glyphicon-search c-f"></span> 搜索</button>
                                            </td>
                                           </form>
                                            <td>
                                                <form action="/{$u}/Linehour">
                                                    <input type="hidden" value="{$datatype.school_branch}" name ="school_branch">
                                                    <input type="hidden" value="{$datatype.linerooms_isdel}" name ="linerooms_isdel">
                                                    <input type="hidden" value="{$datatype.linerooms_fromclass}" name ="linerooms_fromclass">
                                                    <input type="hidden" value="{$datatype.editstarttime}" name ="editstarttime">
                                                    <input type="hidden" value="{$datatype.editendtime}" name ="editendtime">
                                                    <input type="hidden" value="{$datatype.keyword}" name ="keyword">
                                                    <input type="hidden" value="{$datatype.linerooms_maxvideo}" name ="linerooms_maxvideo">
                                                    <input type="hidden" value="1" name ="is_export">
                                                    <button type="submit" id="Submit" class="btn btn-primary ml0"><span class="glyphicon  c-f"></span> 导出</button>
                                                </form>
                                            </td>
                                        </tr>
                                    </table>

                            </div>
                            <form role="form" action="/{$u}?c=batchWork" method="post" enctype="application/x-www-form-urlencoded" class="AjaxForm">
                                <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table tc table-hover table-bordered">
                                    <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>校区名称<br />班级编号</th>
                                        <th>英文名</th>
                                        <th>教室号</th>
                                        <th>教室名称</th>
                                        <th>所属平台</th>
                                        <th>最大上台数</th>
                                        <th>开始时间/结束时间</th>
                                        <th>教师密码</th>
                                        <th>助教密码</th>
                                        <th>巡课密码</th>
                                        <th>上课密码</th>
                                        <th width="15%">操作</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    {if $dataList}
                                    {foreach from=$dataList item=dataVar}
                                    <tr id="list-{$dataVar.linerooms_id}">
                                        <td>{$dataVar.linerooms_id}</td>
                                        <td>{if $dataVar.school_cnname}{$dataVar.school_cnname}{else}已删除教室{/if}<br />{$dataVar.class_branch}</td>
                                        <td>{$dataVar.class_enname}</td>
                                        <td>{$dataVar.linerooms_number}</br>{$dataVar.linerooms_threenumber}</td>
                                        <td>{$dataVar.linerooms_name}</td>
                                        <td>{if $dataVar.linerooms_fromclass == '0'}
                                            <span class="c-red">拓课云</span>
                                            {elseif $dataVar.linerooms_fromclass == '1'}
                                            <span class="c-red">云枢</span>
                                            {/if}</td>
                                        <td>{$dataVar.linerooms_maxvideo-1}</td>
                                        <td>{$dataVar.linerooms_starttime|date_format:'%Y-%m-%d %H:%M:%S'}<br />{$dataVar.linerooms_endtime|date_format:'%Y-%m-%d %H:%M:%S'}</td>
                                        <td>{$dataVar.linerooms_chairmanpwd}</td>
                                        <td>{$dataVar.linerooms_assistantpwd}</td>
                                        <td>{$dataVar.linerooms_patrolpwd}</td>
                                        <td>{$dataVar.linerooms_confuserpwd}</td>
                                        <td align="left">
                                            {if $dataVar.linerooms_threenumber !==''}
                                            <a href="/{$u}/LineLook?linerooms_id={$dataVar.linerooms_id}" class="btn btn-primary mb10 btn-sm">查看</a>
                                            <a href="/{$u}/LineEdit?linerooms_id={$dataVar.linerooms_id}" class="btn btn-primary mb10 btn-sm">编辑</a>
                                            <a href="/{$u}/LineDocument?linerooms_id={$dataVar.linerooms_id}" class="btn btn-primary mb10 btn-sm"><span class="glyphicon glyphicon-th-list c-f"></span> 课件管理</a>
                                            <a href="/{$u}/LineLogininfo?linerooms_id={$dataVar.linerooms_id}" class="btn btn-primary mb10 btn-sm"><span class="glyphicon glyphicon-th-list c-f"></span> 用户日志</a>
                                            <a href="/{$u}/LineRecordvideo?linerooms_id={$dataVar.linerooms_id}" class="btn btn-primary mb10 btn-sm">
                                                <span class="glyphicon glyphicon-th-list c-f"></span> 录播管理</a>
                                            {else}
                                            <a href="javascript:;" data-element="list-{$dataVar.linerooms_id}" data-url="/{$u}?c=Create&linerooms_id={$dataVar.linerooms_id}"
                                                 class="btn btn-primary btn-send-action btn-sm">生成拓课云教室号</a>
                                            {/if}
                                        </td>
                                    </tr>
                                    {/foreach}
                                    {/if}
                                    </tbody>
                                </table>
                            </form>
                            <div class="pagemenu">{$pagelist}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{include file="jscom.htm"}
{literal}
<script>
    $(document).ready(function() {
        //体检渠道管理--销售统计  导出出订单
        $("#FromSubmit").click(function (e) {
            $("#RoomsForm").attr("action", "/Linerooms/Linehour");
            $("#RoomsForm").attr("method", "get");
            $("#RoomsForm").submit();
        })
        $("#FromExport").click(function (e) {
            $("#RoomsForm").attr("action", "/Linerooms?c=Export");
            $("#RoomsForm").attr("method", "post");
            $("#RoomsForm").submit();
        })
    })
</script>
{/literal}
<script src="{$PluginsUrl}jquery-form/jquery.form.js" type="text/javascript"></script>
<script src="{$JsUrl}int.form.js" type="text/javascript"></script>
</body>
</html>
