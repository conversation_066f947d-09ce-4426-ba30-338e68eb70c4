<!DOCTYPE HTML>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    {include file="key.htm"}
</head>

<body>

<div class="page-container row condensed">
    {include file="page-sidebar.htm"}
    {include file="footer.htm"}
    <div class="page-content">
        {include file="header.htm"}
        <div class="mx20 breadcrumb-w">
            <div class="breadcrumb fl">
                <span>网课教室管理</span>
            </div>
            <a href="javascript:" class="fr bakFromurl mt6"><i class="icon-back mr5"></i>返回</a>
            <div class="clear"></div>
        </div>
        <div class="content">
            <div id="container">
                <div class="row">
                    <div class="bg-f">
                        <div class="p20 f14">
                            <div class="form-group">
                                <form action="/{$u}" method="get" accept-charset="utf-8" id="RoomsForm">
                                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                        <tr>
                                            <td width="4%">搜索：</td>
                                            <td width="15%" class="pr10">
                                                <select name="school_branch" id="school_branch" class="form-control chosen-select">
                                                    <option value="">请选择校园</option>
                                                    {if $istaffer.schoolList}
                                                    {foreach from=$istaffer.schoolList item=schoolOne}
                                                    <option value="{$schoolOne.school_branch}" {if $schoolOne.school_branch == $datatype.school_branch}selected{/if}>{$schoolOne.school_cnname}</option>
                                                    {/foreach}
                                                    {/if}
                                                </select>
                                            </td>
                                            <td width="10%" class="pr10">
                                                <select name="linerooms_fromclass" id="linerooms_fromclass" class="form-control">
                                                    <option value="">请选择平台</option>
                                                    <option value="0" {if $datatype.linerooms_fromclass == '0'}selected{/if}>拓课云平台</option>
                                                    <option value="1" {if $datatype.linerooms_fromclass == '1'}selected{/if}>云枢平台</option>
                                                </select>
                                            </td>
                                            <td width="12%" class="pr10">
                                                <input name="editstarttime" id="editstarttime" value="{$datatype['editstarttime']}" class="form-control input-sm form_datetime" autocomplete="off" placeholder="网课开始时间" type="text">
                                            </td>
                                            <td width="12%" class="pr10">
                                                <input name="editendtime" id="editendtime" value="{$datatype['editendtime']}" class="form-control input-sm form_datetime" autocomplete="off" placeholder="网课结束时间" type="text">
                                            </td>
                                            <td width="15%"><input name="keyword" class="form-control input-sm" placeholder="请输入房间名称/房间号" value="{$datatype['keyword']}" type="text"></td>
                                            <td width="23%">
                                                <button type="submit" id="FromSubmit" class="btn btn-primary ml10"><span class="glyphicon glyphicon-search c-f"></span> 搜索</button>
                                            </td>
                                            <td></td>
                                        </tr>
                                    </table>
                                </form>
                            </div>
                            <form role="form" action="/{$u}?c=batchWork" method="post" enctype="application/x-www-form-urlencoded" class="AjaxForm">
                                <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table tc table-hover table-bordered">
                                    <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>校区名称</th>
                                        <th>教室号</th>
                                        <th>教室名称</th>
                                        <th>所属平台</th>
                                        <th>上课类型</th>
                                        <th>开始时间</th>
                                        <th>结束时间</th>
                                        <th>操作</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    {if $dataList}
                                    {foreach from=$dataList item=dataVar}
                                    <tr id="list-{$dataVar.linerooms_id}">
                                        <td>{$dataVar.linerooms_id}</td>
                                        <td>{$dataVar.school_cnname}</td>
                                        <td>{$dataVar.linerooms_number}</td>
                                        <td>{$dataVar.linerooms_name}</td>
                                        <td>{if $dataVar.linerooms_fromclass == '0'}
                                            <span class="c-red">拓课云</span>
                                            {elseif $dataVar.linerooms_fromclass == '1'}
                                            <span class="c-red">云枢</span>
                                            {/if}</td>
                                        <td>{if $dataVar.linerooms_type == '0'}
                                            <span class="c-red">1对1</span>
                                            {elseif $dataVar.linerooms_type == '3'}
                                            <span class="c-red">1对N</span>
                                            {/if}</td>
                                        <td>{$dataVar.linerooms_starttime|date_format:'%Y-%m-%d %H:%M:%S'}</td>
                                        <td>{$dataVar.linerooms_endtime|date_format:'%Y-%m-%d %H:%M:%S'}</td>
                                        <td align="left">
                                            <a href="/{$u}/Look?linerooms_id={$dataVar.linerooms_id}" class="btn btn-primary btn-sm">
                                                查看</a>
                                            <a href="/{$u}/Record?linerooms_id={$dataVar.linerooms_id}" class="btn btn-primary btn-sm">
                                                <span class="glyphicon glyphicon-th-list c-f"></span> 录播记录管理</a>
                                            <a href="/{$u}/Recordvideo?linerooms_id={$dataVar.linerooms_id}" class="btn btn-primary btn-sm">
                                                <span class="glyphicon glyphicon-th-list c-f"></span> 录播Mp4管理</a>
                                            <a href="https://ptc.kidcastle.com.cn/OnlineCourse/{$dataVar.linerooms_number}" target="_blank" class="btn btn-primary btn-sm">
                                                <span class="glyphicon glyphicon-film c-f"></span> 学员录播课堂</a>
                                        </td>
                                    </tr>
                                    {/foreach}
                                    {/if}
                                    </tbody>
                                </table>
                            </form>
                            <div class="pagemenu">{$pagelist}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{include file="jscom.htm"}
{literal}
<script>
    $(document).ready(function() {
        //体检渠道管理--销售统计  导出出订单
        $("#FromSubmit").click(function (e) {
            $("#RoomsForm").attr("action", "/Linerooms/Recordlist");
            $("#RoomsForm").attr("method", "get");
            $("#RoomsForm").submit();
        })
        $("#FromExport").click(function (e) {
            $("#RoomsForm").attr("action", "/Linerooms?c=Export");
            $("#RoomsForm").attr("method", "post");
            $("#RoomsForm").submit();
        })
    })
</script>
{/literal}
<script src="{$PluginsUrl}jquery-form/jquery.form.js" type="text/javascript"></script>
<script src="{$JsUrl}int.form.js" type="text/javascript"></script>
</body>
</html>
