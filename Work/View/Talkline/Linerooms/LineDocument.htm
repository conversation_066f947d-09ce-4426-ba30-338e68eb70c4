<!DOCTYPE HTML>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    {include file="key.htm"}
</head>

<body>

<div class="page-container row condensed">
    {include file="page-sidebar.htm"}
    {include file="footer.htm"}
    <div class="page-content">
        {include file="header.htm"}
        <div class="mx20 breadcrumb-w">
            <div class="breadcrumb fl">
                <a href="/{$u}">网课教室管理</a>
                <span>&gt; 网课文档管理</span>
            </div>
            <a href="javascript:" class="fr bakFromurl mt6"><i class="icon-back mr5"></i>返回</a>
            <div class="clear"></div>
        </div>
        <div class="content">
            <div id="container">
                <div class="row">
                    <div class="bg-f">
                        <div class="p20 f14">
                            <div class="col-md-12">
                                <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table tc table-hover table-bordered">
                                    <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>文件名</th>
                                        <th>文件大小</th>
                                        <th>文件路径</th>
                                        <th>下载路径</th>
                                        <th>文件类型</th>
                                        <th>操作</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    {if $roomFile}
                                    {foreach from=$roomFile item=FileVar}
                                    <tr id="list-{$FileVar.fileid}">
                                        <td>{$FileVar.fileid}</td>
                                        <td>{$FileVar.filename}</td>
                                        <td>{$FileVar.size}</td>
                                        <td>{$FileVar.filepath}</td>
                                        <td>{$FileVar.downloadpath}</td>
                                        <td>{$FileVar.filetype}</td>
                                        <td><a href="javascript:;" data-element="list-{$FileVar.fileid}" data-url="/{$u}?c=DelFile&fileid={$FileVar.fileid}&fromclass={$dataVar.linerooms_fromclass}" class="btn btn-danger btn-sm btn-del-action">
                                            <span class="glyphicon glyphicon-remove c-f"></span> 删除</a></td>
                                    </tr>
                                    {/foreach}
                                    {/if}
                                    </tbody>
                                </table>
                            </div>
                            <div class="col-md-4">
                                <form action="/{$u}?c={$act}" method="post" enctype="multipart/form-data">
                                    <input name="linerooms_id" type="hidden" value="{$dataVar.linerooms_id}">
                                    <input name="fromclass" type="hidden" value="{$dataVar.linerooms_fromclass}">
                                    <div id="Form-Box-Operating">
                                        <div class="px20 py20 f14 row">
                                            <div class="form-group">
                                                <label for="linerooms_name"><em>*</em>教室名称</label>
                                                <input id="linerooms_name" value="{$dataVar.linerooms_name}" type="text" reg="[^ \f\n\r\t\v]" tip="房间名称" class="form-control" placeholder="请输入房间名称">
                                            </div>
                                            <div class="form-group">
                                                <label for="linerooms_threenumber"><em>*</em>教室编号</label>
                                                <input name="serial" id="linerooms_threenumber" value="{$dataVar.linerooms_threenumber}" type="text" reg="[^ \f\n\r\t\v]" tip="房间名称" class="form-control" placeholder="请输入房间名称">
                                            </div>
                                            <div class="form-group">
                                                <div class="col-md-12" style="padding-left: 0px;">
                                                    <label for="list_img">选择文件上传</label>
                                                    <input name="filedata" type="file">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="form-group txtrg pr30">
                                            <button type="button" class="btn btn-default mr40 bakFromurl">返回</button>
                                            <button type="submit" class=" btn btn-primary btn-cons">上传文件</button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                            <div class="clear"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{include file="jscom.htm"}
<script src="{$PluginsUrl}jquery-form/jquery.form.js" type="text/javascript"></script>
<script src="{$JsUrl}int.form.js" type="text/javascript"></script>
</body>
</html>
