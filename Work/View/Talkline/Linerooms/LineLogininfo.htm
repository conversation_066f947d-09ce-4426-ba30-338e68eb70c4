<!DOCTYPE HTML>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    {include file="key.htm"}
</head>

<body>

<div class="page-container row condensed">
    {include file="page-sidebar.htm"}
    {include file="footer.htm"}
    <div class="page-content">
        {include file="header.htm"}
        <div class="mx20 breadcrumb-w">
            <div class="breadcrumb fl">
                <a href="/{$u}">网课教室管理</a>
                <span>&gt; 网课用户日志管理</span>
            </div>
            <a href="javascript:" class="fr bakFromurl mt6"><i class="icon-back mr5"></i>返回</a>
            <div class="clear"></div>
        </div>
        <div class="content">
            <div id="container">
                <div class="row">
                    <div class="bg-f">
                        <div class="p20 f14">
                            <div class="col-md-12">
                                <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table tc table-hover table-bordered">
                                    <thead>
                                    <tr>
                                        <th>用户名</th>
                                        <th>进入时间</th>
                                        <th>退出时间</th>
                                        <th>时长</th>
                                        <th>ip地址</th>
                                        <th>客户端类型</th>
                                        <th>客户角色</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    {if $loginInfo}
                                    {foreach from=$loginInfo item=FileVar}
                                    <tr id="list-{$FileVar.userid}">
                                        <td>{$FileVar.username}</td>
                                        <td>{$FileVar.entertime}</td>
                                        <td>{$FileVar.outtime}</td>
                                        <td>{$FileVar.duration}秒</td>
                                        <td>{$FileVar.ipaddress}</td>
                                        <td>{$FileVar.ostype}{$FileVar.devicetype}</td>
                                        <td>
                                            {if $FileVar.userroleid == '0'}主讲
                                            {elseif $FileVar.userroleid == '1'}助教
                                            {elseif $FileVar.userroleid == '2'}学员
                                            {elseif $FileVar.userroleid == '3'}直播用户
                                            {elseif $FileVar.userroleid == '4'}巡检员{/if}
                                        </td>
                                    </tr>
                                    {/foreach}
                                    {/if}
                                    </tbody>
                                </table>
                            </div>
                            <div class="clear"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{include file="jscom.htm"}
<script src="{$PluginsUrl}jquery-form/jquery.form.js" type="text/javascript"></script>
<script src="{$JsUrl}int.form.js" type="text/javascript"></script>
</body>
</html>
