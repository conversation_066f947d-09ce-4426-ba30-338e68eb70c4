<!DOCTYPE HTML>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    {include file="key.htm"}
</head>

<body>

<div class="page-container row condensed">
    {include file="page-sidebar.htm"}
    {include file="footer.htm"}
    <div class="page-content">
        {include file="header.htm"}
        <div class="mx20 breadcrumb-w">
            <div class="breadcrumb fl">
                <a href="/{$u}">网课教室管理</a>
                <span>&gt; 网课教室详情</span>
            </div>
            <a href="javascript:" class="fr bakFromurl mt6"><i class="icon-back mr5"></i>返回</a>
            <div class="clear"></div>
        </div>
        <div class="content">
            <div id="container">
                <div class="row">
                    <div class="bg-f">
                        <div class="p20 f14">
                            <h2>{$moduleOne.module_name}</h2>
                            <div class="col-md-12" id="Form-Box-Operating">
                                <div class="panel-body">
                                    <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table tc table-hover table-bordered">
                                        <thead>
                                        <tr>
                                            <th>课时编号</th>
                                            <th>上课时间</th>
                                            <th>下课时间</th>
                                            <th>上课时长</th>
                                            <th>用户数</th>
                                            <th>教师+学员(单价)</th>
                                            <th>教师+学员费用</th>
                                            <th>巡课助教(单价)</th>
                                            <th>巡课助教时长</th>
                                            <th>巡课助教费用</th>
                                            <th>折扣率</th>
                                            <th>合计教室费用</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        {foreach from=$recordList item=onlineOne}
                                        <tr>
                                            <td>{$onlineOne.id}</td>
                                            <td>{$onlineOne.begintime}</td>
                                            <td>{$onlineOne.endtime}</td>
                                            <td>{$onlineOne.duration}</td>
                                            <td>{$onlineOne.usernum}</td>
                                            <td>{$onlineOne.unitprice}</td>
                                            <td>￥{$onlineOne.studentsCost}</td>
                                            <td>{$onlineOne.taprice}</td>
                                            <td>{$onlineOne.tadurationSecond}</td>
                                            <td>￥{$onlineOne.taCost}</td>
                                            <td>{$onlineOne.dis}</td>
                                            <td>￥{$onlineOne.totalprice}</td>
                                        </tr>
                                        {/foreach}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            <div class="clear"></div>
                            <div class="">
                                <div class="pagemenu col-md-8">{$pagelist}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{include file="jscom.htm"}
<script src="{$PluginsUrl}jquery-form/jquery.form.js" type="text/javascript"></script>
<script src="{$JsUrl}int.form.js" type="text/javascript"></script>
</body>
</html>
