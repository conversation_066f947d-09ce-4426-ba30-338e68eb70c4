<!DOCTYPE HTML>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    {include file="key.htm"}
</head>

<body>

<div class="page-container row condensed">
    {include file="page-sidebar.htm"}
    {include file="footer.htm"}
    <div class="page-content">
        {include file="header.htm"}
        <div class="mx20 breadcrumb-w">
            <div class="breadcrumb fl">
                <span>网课教室管理</span>
            </div>
            <a href="javascript:" class="fr bakFromurl mt6"><i class="icon-back mr5"></i>返回</a>
            <div class="clear"></div>
        </div>
        <div class="content">
            <div id="container">
                <div class="row">
                    <div class="bg-f">
                        <div class="p20 f14">
                            <div class="form-group">
                                <form action="/{$u}" method="get" accept-charset="utf-8" id="RoomsForm">
                                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                        <tr>
                                            <td width="4%">搜索：</td>
                                            <td width="10%" class="pr10">
                                                <select name="log_class" id="log_class" class="form-control">
                                                    <option value="">账户交易类型</option>
                                                    <option value="0" {if $datatype.log_class == '0'}selected{/if}>账户充值</option>
                                                    <option value="1" {if $datatype.log_class == '1'}selected{/if}>账户结算</option>
                                                </select>
                                            </td>
                                            <td width="8%" class="pr10">
                                                <input name="editstarttime" id="editstarttime" value="{$datatype['editstarttime']}" class="form-control input-sm form_datetime" placeholder="网站开始时间" type="text">
                                            </td>
                                            <td width="8%" class="pr10">
                                                <input name="editendtime" id="editendtime" value="{$datatype['editendtime']}" class="form-control input-sm form_datetime" placeholder="网站结束时间" type="text">
                                            </td>
                                            <td width="15%"><input name="keyword" class="form-control input-sm" placeholder="请输入房间名称/房间号" value="{$datatype['keyword']}" type="text"></td>
                                            <td width="23%">
                                                <button type="submit" id="FromSubmit" class="btn btn-primary ml10"><span class="glyphicon glyphicon-search c-f"></span> 搜索</button>
                                            </td>
                                        </tr>
                                    </table>
                                </form>
                            </div>
                            <form role="form" action="/{$u}?c=batchWork" method="post" enctype="application/x-www-form-urlencoded" class="AjaxForm">
                                <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table tc table-hover table-bordered">
                                    <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>交易类型</th>
                                        <th>交易名称</th>
                                        <th>原账户金额</th>
                                        <th>交易金额</th>
                                        <th>剩余金额</th>
                                        <th>交易时间</th>
                                        <th>交易备注</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    {if $dataList}
                                    {foreach from=$dataList item=dataVar}
                                    <tr id="list-{$dataVar.log_id}">
                                        <td>{$dataVar.log_id}</td>
                                        <td>{if $dataVar.log_class == '0'}
                                            <span class="c-red">账户充值</span>
                                            {elseif $dataVar.log_class == '1'}
                                            <span class="c-red">账户结算</span>
                                            {/if}</td>
                                        <td>{$dataVar.log_playname}</td>
                                        <td>{$dataVar.log_fromamount}</td>
                                        <td>{$dataVar.log_playclass}{$dataVar.log_playamount}</td>
                                        <td>{$dataVar.log_finalamount}</td>
                                        <td>{$dataVar.log_time|date_format:'%Y-%m-%d %H:%M:%S'}</td>
                                        <td>{$dataVar.log_reason}</td>
                                    </tr>
                                    {/foreach}
                                    {/if}
                                    </tbody>
                                </table>
                            </form>
                            <div class="">
                                <div class="pagemenu col-md-7">{$pagelist}</div>
                            </div>
                            <div class="clear"></div>
                            <div class="pl20 py20 f14">
                                <!-- Nav tabs -->
                                <ul class="nav nav-tabs" role="tablist">
                                    <li><a >{$istaffer.company_cnname}</a></li>
                                    {if $priceArray}
                                    {foreach from=$priceArray item=priceOne key=key}
                                    <li role="presentation" {if $key=='all'}class="active"{/if}><a href="#tab{$key}" role="tab" data-toggle="tab">{$priceOne.cnname}</a></li>
                                    {/foreach}
                                    {/if}
                                </ul>

                                <!-- Tab panes -->
                                <div class="tab-content">
                                    {if $priceArray}
                                    {foreach from=$priceArray item=priceOne key=key}
                                    <div role="tabpanel" class="tab-pane fade {if $key=='all'}active in{/if}" id="tab{$key}">
                                        <div>{$priceOne.cnname}累计账户充值：{if $priceOne.balanceOne}{$priceOne.balanceOne.allplayamount}{else}0{/if}元</div>
                                        <div class="panel panel-primary">
                                            <div class="panel-heading">拓课云费用统计</div>
                                            <div class="panel-body">
                                                <div class=" " style="text-align: left; color: #FF0000;">
                                                    累计在线时间：{$priceOne.tkyOne.durationnums}分钟,{$priceOne.tkyOne.hoursnums}小时
                                                </div>
                                                <div class=" " style="text-align: left; color: #FF0000;">
                                                    累计上课时间：{$priceOne.tkyOne.paytimenums}分钟,{$priceOne.tkyOne.payhoursnums}小时
                                                </div>
                                                <div class=" " style="text-align: left; color: #FF0000;">
                                                    已消耗：{$priceOne.tkyOne.payprice}元 （计算公式：上课时间*0.0392元）
                                                </div>
                                            </div>
                                        </div>
                                        <div class="panel panel-primary">
                                            <div class="panel-heading">云枢费用统计</div>
                                            <div class="panel-body">
                                                <div class=" " style="text-align: left; color: #FF0000;">
                                                    累计上课课时{$priceOne.ysOne.durationnums}分钟(教师+学员),{$priceOne.ysOne.hoursnums}小时
                                                </div>
                                                <div class=" " style="text-align: left; color: #FF0000;">
                                                    累计上课课时{$priceOne.ysOne.paytimenums}分钟(助教),{$priceOne.ysOne.payhoursnums}小时
                                                </div>
                                                <div class=" " style="text-align: left; color: #FF0000;">
                                                    已消耗：{$priceOne.ysOne.payprice}元 （计算公式：云枢系统结算）
                                                </div>
                                            </div>
                                        </div>
                                        <div class="panel panel-primary">
                                            <div class="panel-heading">剩余账户费用</div>
                                            <div class="panel-body">
                                                <div class=" " style="text-align: left; color: #FF0000;">
                                                    剩余总额：{$priceOne.balanceOne.allplayamount-$priceOne.tkyOne.payprice-$priceOne.ysOne.payprice}元
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    {/foreach}
                                    {/if}

                                    <div role="tabpanel" class="tab-pane fade" id="profile">

                                    </div>
                                    <div role="tabpanel" class="tab-pane fade" id="messages">

                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{include file="jscom.htm"}
{literal}
<script>
    $(document).ready(function() {
        //体检渠道管理--销售统计  导出出订单
        $("#FromSubmit").click(function (e) {
            $("#RoomsForm").attr("action", "/Financial/Home");
            $("#RoomsForm").attr("method", "get");
            $("#RoomsForm").submit();
        })
        $("#FromExport").click(function (e) {
            $("#RoomsForm").attr("action", "/Financial?c=Export");
            $("#RoomsForm").attr("method", "post");
            $("#RoomsForm").submit();
        })
    })
</script>
{/literal}
<script src="{$PluginsUrl}jquery-form/jquery.form.js" type="text/javascript"></script>
<script src="{$JsUrl}int.form.js" type="text/javascript"></script>
</body>
</html>
