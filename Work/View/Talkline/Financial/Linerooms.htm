<!DOCTYPE HTML>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    {include file="key.htm"}
</head>

<body>

<div class="page-container row condensed">
    {include file="page-sidebar.htm"}
    {include file="footer.htm"}
    <div class="page-content">
        {include file="header.htm"}
        <div class="mx20 breadcrumb-w">
            <div class="breadcrumb fl">
                <span>网课教室管理</span>
            </div>
            <a href="javascript:" class="fr bakFromurl mt6"><i class="icon-back mr5"></i>返回</a>
            <div class="clear"></div>
        </div>
        <div class="content">
            <div id="container">
                <div class="row">
                    <div class="bg-f">
                        <div class="p20 f14">
                            <div class="form-group">
                                <form action="/{$u}/{$t}" method="get" accept-charset="utf-8" id="RoomsForm">
                                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                        <tr>
                                            <td width="5%">搜索：</td>
                                            <td width="15%" class="pr10">
                                                <select name="school_branch" id="school_branch" class="form-control chosen-select">
                                                    <option value="">请选择校园</option>
                                                    {if $istaffer.schoolList}
                                                    {foreach from=$istaffer.schoolList item=schoolOne}
                                                    <option value="{$schoolOne.school_branch}" {if $schoolOne.school_branch == $datatype.school_branch}selected{/if}>{$schoolOne.school_cnname}</option>
                                                    {/foreach}
                                                    {/if}
                                                </select>
                                            </td>
                                            <td width="8%" class="pr10">
                                                <select name="linerooms_isdel" id="linerooms_isdel" class="form-control">
                                                    <option value="0" {if $datatype.linerooms_isdel == '0'}selected{/if}>有效教室</option>
                                                    <option value="1" {if $datatype.linerooms_isdel == '1'}selected{/if}>删除教室</option>
                                                </select>
                                            </td>
                                            <td width="8%" class="pr10">
                                                <input name="editstarttime" id="editstarttime" value="{$datatype['editstarttime']}" class="form-control input-sm form_datetime" placeholder="开始时间" type="text">
                                            </td>
                                            <td width="8%" class="pr10">
                                                <input name="editendtime" id="editendtime" value="{$datatype['editendtime']}" class="form-control input-sm form_datetime" placeholder="结束时间" type="text">
                                            </td>
                                            <td width="15%"><input name="keyword" class="form-control input-sm" placeholder="请输入房间名称/房间号" value="{$datatype['keyword']}" type="text"></td>
                                            <td width="23%">
                                                <button type="submit" id="FromSubmit" class="btn btn-primary ml10"><span class="glyphicon glyphicon-search c-f"></span> 搜索</button>
                                            </td>
                                            <td>
                                                <button type="submit" id="FromExport" class="btn btn-primary ml10 fr">
                                                    <span class="glyphicon glyphicon-sort-by-attributes"></span> 导出教室消耗统计</button>
                                            </td>
                                        </tr>
                                    </table>
                                </form>
                            </div>
                            <form role="form" action="/{$u}?c=batchWork" method="post" enctype="application/x-www-form-urlencoded" class="AjaxForm">
                                <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table tc table-hover table-bordered">
                                    <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>校园名称</th>
                                        <th>房间号</th>
                                        <th>房间名称</th>
                                        <th>交易平台</th>
                                        <th>在线时长（分钟）</th>
                                        <th>上课时长（分钟）</th>
                                        <th>消耗金额</th>
                                        <th>流量更新时间</th>
                                        <th>操作</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    {if $dataList}
                                    {foreach from=$dataList item=dataVar}
                                    <tr id="list-{$dataVar.linerooms_id}">
                                        <td>{$dataVar.linerooms_id}</td>
                                        <td>{$dataVar.school_cnname}</td>
                                        <td>{$dataVar.linerooms_number}</td>
                                        <td>{$dataVar.linerooms_name}</td>
                                        <td>{if $dataVar.linerooms_fromclass == '0'}
                                            <span class="c-red">拓课云</span>
                                            {elseif $dataVar.linerooms_fromclass == '1'}
                                            <span class="c-red">云枢</span>
                                            {/if}</td>
                                        <td>{$dataVar.tkylog_duration}</td>
                                        <td>{$dataVar.tkylog_paytime}</td>
                                        <td class="tl">￥{$dataVar.tkylog_payprice}元</td>
                                        <td>{$dataVar.linerooms_flowtime|date_format:'%Y-%m-%d %H:%M:%S'}</td>
                                        <td>
                                            <a href="/{$u}/Expend?linerooms_id={$dataVar.linerooms_id}" class="btn btn-primary btn-sm">
                                                <span class="glyphicon glyphicon-th-list c-f"></span> 查看明细</a>
                                        </td>

                                    </tr>
                                    {/foreach}
                                    {/if}
                                    </tbody>
                                </table>
                            </form>
                            <div class="">
                                <div class="pagemenu col-md-4" style="text-align: left; color: #FF0000;">
                                    您当前的账户累计消耗￥{$priceOne.payprice}元（已结算部分，实际以最终结算为准）
                                </div>
                                <div class="pagemenu col-md-8">{$pagelist}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{include file="jscom.htm"}
{literal}
<script>
    $(document).ready(function() {
        //体检渠道管理--销售统计  导出出订单
        $("#FromSubmit").click(function (e) {
            $("#RoomsForm").attr("action", "/Financial/Linerooms");
            $("#RoomsForm").attr("method", "get");
            $("#RoomsForm").submit();
        })
        $("#FromExport").click(function (e) {
            $("#RoomsForm").attr("action", "/Financial?c=ExportLinerooms");
            $("#RoomsForm").attr("method", "post");
            $("#RoomsForm").submit();
        })
    })
</script>
{/literal}
<script src="{$PluginsUrl}jquery-form/jquery.form.js" type="text/javascript"></script>
<script src="{$JsUrl}int.form.js" type="text/javascript"></script>
</body>
</html>
