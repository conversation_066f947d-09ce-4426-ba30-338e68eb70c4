<!DOCTYPE HTML>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    {include file="key.htm"}
</head>

<body>

<div class="page-container row condensed">
    {include file="page-sidebar.htm"}
    {include file="footer.htm"}
    <div class="page-content">
        {include file="header.htm"}
        <div class="mx20 breadcrumb-w">
            <div class="breadcrumb fl">
                <a href="/lineCourse/Lineclass">全国班课管理</a>
                <span>&gt; 班课排课管理</span>
            </div>
            <a href="javascript:" class="fr bakFromurl mt6"><i class="icon-back mr5"></i>返回</a>
            <div class="clear"></div>
        </div>
        <div class="content">
            <div id="container">
                <div class="row">
                    <div class="bg-f">
                        <div class="p20 f14">
                            <div class="form-group">
                                <form action="/{$u}/Hourrooms" method="get" accept-charset="utf-8" id="RoomsForm">
                                    <input name="lineclass_id" type="hidden" value="{$lineclassOne.lineclass_id}">
                                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                        <tr>
                                            <td width="5%">搜索：</td>
                                            <td width="9%" class="pr10">
                                                <input name="starttime" id="starttime" value="{$datatype['starttime']}" class="form-control input-sm form_datetime" autocomplete="off" placeholder="班课开始时间" type="text">
                                            </td>
                                            <td width="9%" class="pr10">
                                                <input name="endtime" id="endtime" value="{$datatype['endtime']}" class="form-control input-sm form_datetime" autocomplete="off" placeholder="班课结束时间" type="text">
                                            </td>
                                            <td width="15%" class="pr10"><input name="keyword" class="form-control input-sm" placeholder="请输入教室名称/教室号" value="{$datatype['keyword']}" type="text"></td>
                                            <td width="5%" class="pr10"><input name="hourrooms_maxvideo" class="form-control input-sm" placeholder="上台数" value="{$datatype['hourrooms_maxvideo']}" type="text"></td>
                                            <td width="20%">
                                                <button type="submit" id="FromSubmit" class="btn btn-primary ml10"><span class="glyphicon glyphicon-search c-f"></span> 搜索</button>
                                            </td>
                                            <td class="tl">
                                            </td>
                                            <td><button type="submit" id="FromExport" class="btn btn-primary ml10 fr"><span class="glyphicon glyphicon-sort-by-attributes"></span> 导出课时</button></td>
                                        </tr>
                                    </table>
                                </form>
                            </div>
                            <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table tc table-hover table-bordered">
                                <thead>
                                <tr>
                                    <th>班级名称</th>
                                    <th>班级别名</th>
                                    <th>班级编号</th>
                                    <th>教室号</th>
                                    <th>教室名称</th>
                                    <th>预约学员数</th>
                                    <th>最大上台数</th>
                                    <th>开始时间/结束时间</th>
                                    <th>教师密码</th>
                                    <th>助教密码</th>
                                    <th>巡课密码</th>
                                    <th>上课密码</th>
                                    <th>操作</th>
                                </tr>
                                </thead>
                                <tbody>
                                {if $dataList}
                                {foreach from=$dataList item=dataVar}
                                <tr id="list-{$dataVar.hourrooms_id}">
                                    <td>{$dataVar.lineclass_cnname}</td>
                                    <td>{$dataVar.lineclass_enname}</td>
                                    <td>{$dataVar.lineclass_branch}</td>
                                    <td>{$dataVar.hourrooms_threenumber}</td>
                                    <td>{$dataVar.hourrooms_name}</td>
                                    <td>{$dataVar.studynums}</td>
                                    <td>{$dataVar.hourrooms_maxvideo-1}</td>
                                    <td>{$dataVar.hourrooms_starttime|date_format:'%Y-%m-%d %H:%M:%S'}<br />{$dataVar.hourrooms_endtime|date_format:'%Y-%m-%d %H:%M:%S'}</td>
                                    <td>{$dataVar.hourrooms_chairmanpwd}</td>
                                    <td>{$dataVar.hourrooms_assistantpwd}</td>
                                    <td>{$dataVar.hourrooms_patrolpwd}</td>
                                    <td>{$dataVar.hourrooms_confuserpwd}</td>
                                    <td align="left">
                                        <a href="/{$u}/ImportStudy?lineclass_id={$dataVar.lineclass_id}&hourrooms_id={$dataVar.hourrooms_id}" class="btn btn-success btn-sm dropdown-toggle btn-demo-space">导入临时学员</a>
                                        <a href="/{$u}/hourOneStudy?hourrooms_id={$dataVar.hourrooms_id}" class="btn btn-primary btn-sm">课时学员</a>
                                        {if $dataVar.hourrooms_threenumber !==''}
                                        <a href="/{$u}/hourroomsEdit?hourrooms_id={$dataVar.hourrooms_id}" class="btn btn-primary btn-sm">编辑</a>
                                        <a href="/{$u}/hourroomsLook?hourrooms_id={$dataVar.hourrooms_id}" class="btn btn-primary btn-sm">查看</a>
                                        {else}
                                        <a href="/{$u}/hourEdit?hourrooms_id={$dataVar.hourrooms_id}" class="btn btn-primary btn-sm">编辑</a>
                                        <a href="javascript:;" data-element="list-{$dataVar.hourrooms_id}" data-url="/{$u}?c=CreateRooms&hourrooms_id={$dataVar.hourrooms_id}"
                                           class="btn btn-primary btn-send-action btn-sm">生成网课号</a>
                                        {/if}
                                        {if $dataVar.hourrooms_ischecking == '1' && ($dataVar.hourrooms_endtime+1800) < time() && $dataVar.hourrooms_endtime > '1720758600' && $dataVar.hourrooms_threenumber !=''}
                                        <a target="_blank" href="/{$u}/ExportClassHourQRGAction?hourrooms_id={$dataVar.hourrooms_id}&hourrooms_threenumber={$dataVar.hourrooms_threenumber}" class="btn btn-success btn-sm">导出课时报表</a>
                                        {/if}
                                    </td>
                                </tr>
                                {/foreach}
                                {/if}
                                </tbody>
                            </table>
                            <div class="pagemenu">{$pagelist}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{include file="jscom.htm"}
{literal}
<script>
    $(document).ready(function() {
        //体检渠道管理--销售统计  导出出订单
        $("#FromSubmit").click(function (e) {
            $("#RoomsForm").attr("action", "/lineCourse/classHour");
            $("#RoomsForm").attr("method", "get");
            $("#RoomsForm").submit();
        })
        $("#FromExport").click(function (e) {
            $("#RoomsForm").attr("action", "/lineCourse?c=ExportClassHour");
            $("#RoomsForm").attr("method", "post");
            $("#RoomsForm").submit();
        })
    })
</script>
{/literal}
<script src="{$PluginsUrl}jquery-form/jquery.form.js" type="text/javascript"></script>
<script src="{$JsUrl}int.form.js" type="text/javascript"></script>
</body>
</html>
