<!DOCTYPE HTML>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    {include file="key.htm"}
</head>

<body>

<div class="page-container row condensed">
    {include file="page-sidebar.htm"}
    {include file="footer.htm"}
    <div class="page-content">
        {include file="header.htm"}
        <div class="mx20 breadcrumb-w">
            <div class="breadcrumb fl">
                <a href="/lineCourse/Lineclass">全国班课管理</a>
                <span>&gt; 班课排课管理</span>
                <span>&gt; 课时学员管理</span>
                {if $hourroomsOne}
                <span>&gt; {$hourroomsOne['lineclass_cnname']}【{$hourroomsOne['hourrooms_name']}】</span>
                {/if}
            </div>
            <a href="javascript:" class="fr bakFromurl mt6"><i class="icon-back mr5"></i>返回</a>
            <div class="clear"></div>
        </div>
        <div class="content">
            <div id="container">
                <div class="row">
                    <div class="bg-f">
                        <div class="p20 f14">
                            <div class="form-group">
                                <form action="/{$u}/hourOneStudy" method="get" accept-charset="utf-8" id="RoomsForm">
                                    <input type="hidden" name="hourrooms_id" value="{$datatype['hourrooms_id']}">
                                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                        <tr>
                                            <td width="5%">搜索：</td>
                                            <td width="9%" class="pr10">
                                                <input name="starttime" id="starttime" value="{$datatype['starttime']}" class="form-control input-sm form_datetime" autocomplete="off" placeholder="班课开始时间" type="text">
                                            </td>
                                            <td width="9%" class="pr10">
                                                <input name="endtime" id="endtime" value="{$datatype['endtime']}" class="form-control input-sm form_datetime" autocomplete="off" placeholder="班课结束时间" type="text">
                                            </td>
                                            <td width="25%" class="pr10"><input name="keyword" class="form-control input-sm" placeholder="请输入学员名称/学员编号/班课编号/教室名称/教室号" value="{$datatype['keyword']}" type="text"></td>
                                            <td width="20%">
                                                <button type="submit" id="FromSubmit" class="btn btn-primary ml10"><span class="glyphicon glyphicon-search c-f"></span> 搜索</button>
                                            </td>
                                            <td class="tl">
                                            </td>

                                            <td><button type="submit" id="FromExport" class="btn btn-primary ml10 fr"><span class="glyphicon glyphicon-sort-by-attributes"></span> 导出学员</button></td>
                                        </tr>
                                    </table>
                                </form>
                            </div>
                            <form  role="form" action="/{$u}?c=batchSetStudyCheckin" method="post" enctype="application/x-www-form-urlencoded" class="AjaxForm">
                                <input name="hourrooms_id" type="hidden" value="{$datatype.hourrooms_id}">
                                <input name="lineclass_id" type="hidden" value="{$hourroomsOne.lineclass_id}">
                                <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table tc table-hover table-bordered">
                                    <thead>
                                    <tr>
                                        <th>选择</th>
                                        <th>学员名称</th>
                                        <th>学员编号</th>
                                        <th>出勤状态</th>
                                        <th>班课别名</th>
                                        <th>班课编号</th>
                                        <th>教室名称</th>
                                        <th>教室号</th>
                                        <th>开始时间/结束时间</th>
                                        <th>学员上课密码</th>
                                        <th>单个考勤</th>
                                        <th>操作</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    {if $dataList}
                                    {foreach from=$dataList item=dataVar}
                                    <tr id="list-{$dataVar.student_id}">
                                        <td>
                                            <input name="tab_list[]" value="{$dataVar.student_id}" type="checkbox" class="ace">
                                            <span class="lbl"></span>
                                        </td>
                                        <td>{$dataVar.student_cnname}</td>
                                        <td>{$dataVar.student_branch}</td>
                                        <td>{if $dataVar.hourroomstudy_checkin == 1}出勤{elseif $dataVar.hourroomstudy_checkin == -1}缺勤{elseif $dataVar.hourroomstudy_checkin == 2}异常{else}--{/if}</td>
                                        <td>{$dataVar.lineclass_branch}</td>
                                        <td>{$dataVar.lineclass_enname}</td>
                                        <td>{$dataVar.hourrooms_name}</td>
                                        <td>{$dataVar.hourrooms_threenumber}</td>
                                        <td>{$dataVar.hourrooms_starttime|date_format:'%Y-%m-%d %H:%M:%S'}<br />{$dataVar.hourrooms_endtime|date_format:'%Y-%m-%d %H:%M:%S'}</td>
                                        <td>{$dataVar.hourrooms_confuserpwd}</td>
                                        <td align="left" class="lookCheckin">
                                            {if $dataVar.hourroomstudy_checkin == 0}
                                            <a  href="javascript:;" data-stuid="{$dataVar.student_id}" data-url="/{$u}?c=checkStuAttendance&hourroomstudy_checkin=1&student_id={$dataVar.student_id}&lineclass_id={$dataVar.lineclass_id}&hourrooms_id={$dataVar.hourrooms_id}" class="cp btn-state-sendtwo">
                                                <span class="btn btn-success btn-sm btn-down-action">出勤</span>
                                            </a>
                                            <a href="javascript:;" data-stuid="{$dataVar.student_id}" data-url="/{$u}?c=checkStuAttendance&hourroomstudy_checkin=-1&student_id={$dataVar.student_id}&lineclass_id={$dataVar.lineclass_id}&hourrooms_id={$dataVar.hourrooms_id}" class="cp btn-state-sendtwo">
                                                <span class="btn btn-danger btn-sm btn-down-action">缺勤</span>
                                            </a>
                                            {else}
                                            已经考勤
                                            {/if}
                                        </td>
                                        <td align="left">
                                            {if $dataVar.hourrooms_threenumber !==''}
                                            <a href="/{$u}/hourroomsLook?hourrooms_id={$dataVar.hourrooms_id}" class="btn btn-primary btn-sm">查看上课信息</a>
                                            {/if}
                                            {if $dataVar.hourroomstudy_checkin == 0}
                                            <a href="javascript:;" data-element="list-{$dataVar.hourroomstudy_id}" data-url="/{$u}?c=Delhourroomstudy&hourroomstudy_id={$dataVar.hourroomstudy_id}" class="btn btn-danger btn-sm btn-del-action">
                                                <span class="glyphicon glyphicon-remove c-f"></span> 删除约课</a>
                                            {/if}
                                        </td>
                                    </tr>
                                    {/foreach}
                                    {/if}

                                    <tr>
                                        <td><label form="Choice_All">全选
                                            <input name="form-field-checkbox" id="Choice_All" type="checkbox" class="ace">
                                            <span class="lbl"></span></label>
                                        </td>
                                        <td colspan="14">
                                            <div class="col-md-2">
                                                <select name="ischeckin" id="type" class="form-control">
                                                    <option value="1">出勤</option>
                                                    <option value="-1">缺勤</option>
                                                </select>
                                            </div>
                                            <button type="submit" class="fl btn btn-primary ml20">
                                                批量操作
                                            </button>
                                        </td>
                                    </tr>

                                    </tbody>
                                </table>
                            </form>
                            <div class="pagemenu">{$pagelist}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{include file="jscom.htm"}
{literal}
<script>
    $(document).ready(function() {
        //体检渠道管理--销售统计  导出出订单
        $("#FromSubmit").click(function (e) {
            $("#RoomsForm").attr("action", "/lineCourse/hourOneStudy");
            $("#RoomsForm").attr("method", "get");
            $("#RoomsForm").submit();
        })
        $("#FromExport").click(function (e) {
            $("#RoomsForm").attr("action", "/lineCourse?c=ExportHourOneStudy");
            $("#RoomsForm").attr("method", "post");
            $("#RoomsForm").submit();
        })
    })
</script>
{/literal}
<script src="{$PluginsUrl}jquery-form/jquery.form.js" type="text/javascript"></script>
<script src="{$JsUrl}int.form.js" type="text/javascript"></script>
</body>
</html>
