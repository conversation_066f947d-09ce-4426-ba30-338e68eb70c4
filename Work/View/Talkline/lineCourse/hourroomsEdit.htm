<!DOCTYPE HTML>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    {include file="key.htm"}
</head>

<body>

<div class="page-container row condensed">
    {include file="page-sidebar.htm"}
    {include file="footer.htm"}
    <div class="page-content">
        {include file="header.htm"}
        <div class="mx20 breadcrumb-w">
            <div class="breadcrumb fl">
                <a href="/Linerooms">网课教室管理</a>
                <span>&gt; 教室信息管理</span>
            </div>
            <a href="javascript:" class="fr bakFromurl mt6"><i class="icon-back mr5"></i>返回</a>
            <div class="clear"></div>
        </div>
        <div class="content">
            <div id="container">
                <div class="bg-f py20">
                    {if !$dataVar}
                    <div class="alert alert-warning alert-dismissible m10" role="alert">
                        <button type="button" class="close" data-dismiss="alert"><span aria-hidden="true">×</span><span class="sr-only">Close</span></button>
                        <span class="glyphicon glyphicon-exclamation-sign f16 vam mr6"></span> 注：新建用户后可为其设定自定义权限</div>
                    {/if}
                    <form action="/{$u}?c={$act}" method="post" enctype="application/x-www-form-urlencoded" class="AjaxForm">
                        <input name="hourrooms_id" type="hidden" value="{$dataVar.hourrooms_id}">
                        <input name="hourrooms_threenumber" type="hidden" value="{$dataVar.hourrooms_threenumber}">
                        {if $dataVar.hourrooms_fromclass == '0'}
                        <input name="hourrooms_fromclass" type="hidden" value="0">
                        {elseif $dataVar.hourrooms_fromclass == '1'}
                        <input name="hourrooms_fromclass" type="hidden" value="1">
                        {/if}
                        <div id="Form-Box-Operating">
                            <div class="px20 py20 f14 row">
                                <div class="form-group col-md-3">
                                    <label for="hourrooms_name"><em>*</em>房间名称</label>
                                    <input name="hourrooms_name" id="hourrooms_name" value="{$dataVar.hourrooms_name}" type="text" reg="[^ \f\n\r\t\v]" tip="房间名称" class="form-control" placeholder="请输入房间名称" disabled>
                                </div>
                                <div class="form-group col-md-3">
                                    <label for="hourrooms_chairmanpwd"><em>*</em>教师上课密码</label>
                                    <input name="hourrooms_chairmanpwd" id="hourrooms_chairmanpwd" value="{$dataVar.hourrooms_chairmanpwd}" type="text" class="form-control" reg="[^ \f\n\r\t\v]" tip="教师上课密码不能为空" placeholder="请输入教师上课密码">
                                </div>
                                <div class="form-group col-md-3">
                                    <label for="hourrooms_assistantpwd"><em>*</em>助教上课密码</label>
                                    <input name="hourrooms_assistantpwd" id="hourrooms_assistantpwd" value="{$dataVar.hourrooms_assistantpwd}" type="text" class="form-control" reg="[^ \f\n\r\t\v]" tip="助教上课密码不能为空" placeholder="请输入助教上课密码">
                                </div>
                                <div class="form-group col-md-3">
                                    <label for="hourrooms_patrolpwd"><em>*</em>主管巡课密码</label>
                                    <input name="hourrooms_patrolpwd" id="hourrooms_patrolpwd" value="{$dataVar.hourrooms_patrolpwd}" type="text" class="form-control" reg="[^ \f\n\r\t\v]" tip="主管巡课密码不能为空" placeholder="请输入主管巡课密码">
                                </div>
                                <div class="clear"></div>
                                <div class="form-group col-md-3">
                                    <label for="hourrooms_passwordrequired"><em>*</em>学员是否需要密码</label>
                                    <select name="hourrooms_passwordrequired" id="hourrooms_passwordrequired" class="form-control">
                                        <option value="1" {if $dataVar.hourrooms_passwordrequired == '1'}selected{/if}>需要密码</option>
                                        <option value="0" {if $dataVar.hourrooms_passwordrequired == '0'}selected{/if}>无需密码</option>
                                    </select>
                                </div>
                                <div class="form-group col-md-3">
                                    <label for="hourrooms_confuserpwd">学员上课密码</label>
                                    <input name="hourrooms_confuserpwd" id="hourrooms_confuserpwd" value="{$dataVar.hourrooms_confuserpwd}" type="text" class="form-control" tip="学员上课密码不能为空" placeholder="请输入学员上课密码">
                                </div>

                                <div class="form-group col-md-3">
                                    <label for="hourrooms_autoopenav"><em>*</em>是否自动开启音频</label>
                                    <select name="hourrooms_autoopenav" id="hourrooms_autoopenav" class="form-control">
                                        <option value="1" {if $dataVar.hourrooms_autoopenav == '1'}selected{/if}>开启</option>
                                        <option value="0" {if $dataVar.hourrooms_autoopenav == '0'}selected{/if}>不开启</option>
                                    </select>
                                </div>
                                <div class="form-group col-md-3">
                                    <label for="hourrooms_type"><em>*</em>房间类型</label>
                                    <select name="hourrooms_type" id="hourrooms_type" class="form-control">
                                        <option value="3" {if $dataVar.hourrooms_type == '3'}selected{/if}>1对多</option>
                                        <option value="0" {if $dataVar.hourrooms_type == '0'}selected{/if}>1对1</option>
                                    </select>
                                </div>
                                <div class="clear"></div>

                                <div class="form-group col-md-3">
                                    <label for="startday"><em>*</em>课程开始时间</label>
                                    <div class="form-group">
                                        <div class="col-md-6" style="padding-left: 0px!important;">
                                            <input name="startday" id="startday" value="{$dataVar.hourrooms_starttime|date_format:'%Y-%m-%d'}" type="text" autocomplete="off" class="form-control form_datetime" reg="[^ \f\n\r\t\v]" tip="上课日期" placeholder="请选择上课日期">
                                        </div>
                                        <div class="col-md-6" style="padding-right: 0px!important;">
                                            <input name="starttime" id="starttime" value="{$dataVar.hourrooms_starttime|date_format:'%H:%M:%S'}" type="text" autocomplete="off" class="form-control form_datetimes" reg="[^ \f\n\r\t\v]" tip="上课开始时间" placeholder="请选择上课开始时间">
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group col-md-3">
                                    <label for="endday"><em>*</em>课程结束时间</label>
                                    <div class="form-group">
                                        <div class="col-md-6" style="padding-left: 0px!important;">
                                            <input name="endday" id="endday" value="{$dataVar.hourrooms_endtime|date_format:'%Y-%m-%d'}" type="text" autocomplete="off" class="form-control form_datetime" reg="[^ \f\n\r\t\v]" tip="上课日期" placeholder="请选择上课日期">
                                        </div>
                                        <div class="col-md-6" style="padding-right: 0px!important;">
                                            <input name="endtime" id="endtime" value="{$dataVar.hourrooms_endtime|date_format:'%H:%M:%S'}" type="text" autocomplete="off" class="form-control form_datetimes" reg="[^ \f\n\r\t\v]" tip="上课结束时间" placeholder="请选择上课结束时间">
                                        </div>
                                    </div>
                                </div>


                                <div class="form-group col-md-3">
                                    <label for="hourrooms_maxvideo"><em>*</em>同时上台人数</label>
                                    <select name="hourrooms_maxvideo" id="hourrooms_maxvideo" class="form-control">
                                        <option value="8" {if $dataVar.hourrooms_maxvideo == '8'}selected{/if}>7</option>
                                        <option value="9" {if $dataVar.hourrooms_maxvideo == '9'}selected{/if}>8</option>
                                        <option value="10" {if $dataVar.hourrooms_maxvideo == '10'}selected{/if}>9</option>
                                        <option value="11" {if $dataVar.hourrooms_maxvideo == '11'}selected{/if}>10</option>
                                        <option value="12" {if $dataVar.hourrooms_maxvideo == '12'}selected{/if}>11</option>
                                        <option value="13" {if $dataVar.hourrooms_maxvideo == '13'}selected{/if}>12</option>
                                        <option value="2" {if $dataVar.hourrooms_maxvideo == '2'}selected{/if}>1</option>
                                        <option value="3" {if $dataVar.hourrooms_maxvideo == '3'}selected{/if}>2</option>
                                        <option value="4" {if $dataVar.hourrooms_maxvideo == '4'}selected{/if}>3</option>
                                        <option value="5" {if $dataVar.hourrooms_maxvideo == '5'}selected{/if}>4</option>
                                        <option value="6" {if $dataVar.hourrooms_maxvideo == '6'}selected{/if}>5</option>
                                        <option value="7" {if $dataVar.hourrooms_maxvideo == '7'}selected{/if}>6</option>
                                    </select>
                                </div>
                                <div class="form-group col-md-3">
                                    <label for="hourrooms_sharedesk"><em>*</em>是否开启桌面共享</label>
                                    <select name="hourrooms_sharedesk" id="hourrooms_sharedesk" class="form-control">
                                        <option value="1" {if $dataVar.hourrooms_sharedesk == '1'}selected{/if}>允许</option>
                                        <option value="0" {if $dataVar.hourrooms_sharedesk == '0'}selected{/if}>禁止</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-group txtrg pr30">
                                <button type="button" class="btn btn-default mr40 bakFromurl">返回</button>
                                <button type="submit" class=" btn btn-primary btn-cons">提交保存</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{include file="jscom.htm"}
<script src="{$PluginsUrl}jquery-form/jquery.form.js" type="text/javascript"></script>
<script src="{$JsUrl}int.form.js" type="text/javascript"></script>
</body>
</html>
