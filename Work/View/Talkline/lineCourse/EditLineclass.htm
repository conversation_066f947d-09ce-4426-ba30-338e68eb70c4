<!DOCTYPE HTML>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    {include file="key.htm"}
</head>

<body>

<div class="page-container row condensed">
    {include file="page-sidebar.htm"}
    {include file="footer.htm"}
    <div class="page-content">
        {include file="header.htm"}
        <div class="mx20 breadcrumb-w">
            <div class="breadcrumb fl">
                <a href="/lineCourse/Lineclass">全国班课管理</a>
                <span>&gt; 班课信息管理</span>
            </div>
            <a href="javascript:" class="fr bakFromurl mt6"><i class="icon-back mr5"></i>返回</a>
            <div class="clear"></div>
        </div>
        <div class="content">
            <div id="container">
                <div class="bg-f py20">
                    {if !$dataVar}
                    <div class="alert alert-warning alert-dismissible m10" role="alert">
                        <button type="button" class="close" data-dismiss="alert"><span aria-hidden="true">×</span><span class="sr-only">Close</span></button>
                        <span class="glyphicon glyphicon-exclamation-sign f16 vam mr6"></span> 注：创建班课后，可以排上课安排</div>
                    {/if}
                    <form action="/{$u}?c={$act}" method="post" enctype="application/x-www-form-urlencoded" class="AjaxForm">
                        <input name="lineclass_id" type="hidden" value="{$dataVar.lineclass_id}">
                        <div id="Form-Box-Operating">
                            <div class="px20 py20 f14 row">
                                <div class="form-group col-md-3">
                                    <label for="lineclass_cnname"><em>*</em>班课名称</label>
                                    <input name="lineclass_cnname" id="lineclass_cnname" value="{$dataVar.lineclass_cnname}" type="text" reg="[^ \f\n\r\t\v]" tip="班课名称" class="form-control" placeholder="请输入班课名称">
                                </div>
                                <div class="form-group col-md-3">
                                    <label for="lineclass_enname"><em>*</em>班课别名</label>
                                    <input name="lineclass_enname" id="lineclass_enname" value="{$dataVar.lineclass_enname}" type="text" reg="[^ \f\n\r\t\v]" tip="班课别名" class="form-control" placeholder="请输入班课别名">
                                </div>
                                <div class="form-group col-md-3">
                                    <label for="lineclass_stdate"><em>*</em>班课开始日期</label>
                                    <div class="form-group">
                                        <input name="lineclass_stdate" id="lineclass_stdate" value="{$dataVar.lineclass_stdate}" type="text" autocomplete="off" class="form-control form_datetime" reg="[^ \f\n\r\t\v]" tip="开始日期" placeholder="请选择开始日期">
                                    </div>
                                </div>
                                <div class="form-group col-md-3">
                                    <label for="lineclass_enddate"><em>*</em>班课结束日期</label>
                                    <div class="form-group">
                                        <input name="lineclass_enddate" id="lineclass_enddate" value="{$dataVar.lineclass_enddate}" type="text" autocomplete="off" class="form-control form_datetime" reg="[^ \f\n\r\t\v]" tip="结束日期" placeholder="请选择结束日期">
                                    </div>
                                </div>
                                <div class="form-group col-md-3">
                                    <label for="course_id"><em>*</em>所属课别</label>
                                    <div class="form-group">
                                        <select name="course_id" id="course_id" class="form-control chosen-select">
                                            <option value="">请选择所属课别</option>
                                            {if $courseList}
                                            {foreach from=$courseList item=courseOne}
                                            <option value="{$courseOne.course_id}" {if $courseOne.course_id == $dataVar.course_id}selected{/if}>{$courseOne.course_cnname}</option>
                                            {/foreach}
                                            {/if}
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group txtrg pr30">
                                <button type="button" class="btn btn-default mr40 bakFromurl">返回</button>
                                <button type="submit" class=" btn btn-primary btn-cons">提交保存</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{include file="jscom.htm"}
<script src="{$PluginsUrl}jquery-form/jquery.form.js" type="text/javascript"></script>
<script src="{$JsUrl}int.form.js" type="text/javascript"></script>
</body>
</html>
