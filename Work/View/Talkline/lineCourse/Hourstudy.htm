<!DOCTYPE HTML>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    {include file="key.htm"}
</head>

<body>

<div class="page-container row condensed">
    {include file="page-sidebar.htm"}
    {include file="footer.htm"}
    <div class="page-content">
        {include file="header.htm"}
        <div class="mx20 breadcrumb-w">
            <div class="breadcrumb fl">
                <a href="/lineCourse/Lineclass">学员管理</a>
                <span>&gt; 学员课时学习明细</span>
            </div>
            <a href="javascript:" class="fr bakFromurl mt6"><i class="icon-back mr5"></i>返回</a>
            <div class="clear"></div>
        </div>
        <div class="content">
            <div id="container">
                <div class="row">
                    <div class="bg-f">
                        <div class="p20 f14">
                            <div class="form-group">
                                <form action="/{$u}/Hourstudy" method="get" accept-charset="utf-8" id="RoomsForm">
                                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                        <tr>
                                            <td width="5%">搜索：</td>
                                            <td width="9%" class="pr10">
                                                <input name="starttime" id="starttime" value="{$datatype['starttime']}" class="form-control input-sm form_datetime" autocomplete="off" placeholder="班课开始时间" type="text">
                                            </td>
                                            <td width="9%" class="pr10">
                                                <input name="endtime" id="endtime" value="{$datatype['endtime']}" class="form-control input-sm form_datetime" autocomplete="off" placeholder="班课结束时间" type="text">
                                            </td>
                                            <td width="25%" class="pr10"><input name="keyword" class="form-control input-sm" placeholder="请输入班课编号/房间名称/房间号/学员名称/学员编号" value="{$datatype['keyword']}" type="text"></td>
                                            <td width="20%">
                                                <button type="submit" id="FromSubmit" class="btn btn-primary ml10"><span class="glyphicon glyphicon-search c-f"></span> 搜索</button>
                                            </td>
                                            <td class="tl">
                                            </td>

                                            <td><button type="submit" id="FromExport" class="btn btn-primary ml10 fr"><span class="glyphicon glyphicon-sort-by-attributes"></span> 导出学员</button></td>
                                        </tr>
                                    </table>
                                </form>
                            </div>
                            <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table tc table-hover table-bordered">
                                <thead>
                                <tr>
                                    <th>学员名称</th>
                                    <th>学员编号</th>
                                    <th>班课别名</th>
                                    <th>班课编号</th>
                                    <th>教室名称</th>
                                    <th>教室号</th>
                                    <th>开始时间/结束时间</th>
                                    <th>学员上课密码</th>
                                    <th width="15%">操作</th>
                                </tr>
                                </thead>
                                <tbody>
                                {if $dataList}
                                {foreach from=$dataList item=dataVar}
                                <tr id="list-{$dataVar.hourroomstudy_id}">
                                    <td>{$dataVar.student_cnname}</td>
                                    <td>{$dataVar.student_branch}</td>
                                    <td>{$dataVar.lineclass_enname}</td>
                                    <td>{$dataVar.lineclass_branch}</td>
                                    <td>{$dataVar.hourrooms_name}</td>
                                    <td>{$dataVar.hourrooms_threenumber}</td>
                                    <td>{$dataVar.hourrooms_starttime|date_format:'%Y-%m-%d %H:%M:%S'}<br />{$dataVar.hourrooms_endtime|date_format:'%Y-%m-%d %H:%M:%S'}</td>
                                    <td>{$dataVar.hourrooms_confuserpwd}</td>
                                    <td align="left">
                                        {if $dataVar.hourrooms_threenumber !==''}
                                        <a href="/{$u}/hourroomsLook?hourrooms_id={$dataVar.hourrooms_id}" class="btn btn-primary btn-sm">查看上课信息</a>
                                        {/if}
                                        {if $dataVar.hourroomstudy_checkin == 0 || $dataVar.hourrooms_starttime > time() }
                                        <a href="javascript:;" data-element="list-{$dataVar.hourroomstudy_id}" data-url="/{$u}?c=Delhourroomstudy&hourroomstudy_id={$dataVar.hourroomstudy_id}" class="btn btn-danger btn-sm btn-del-action">
                                            <span class="glyphicon glyphicon-remove c-f"></span> 删除约课</a>
                                        {/if}
                                    </td>
                                </tr>
                                {/foreach}
                                {/if}
                                </tbody>
                            </table>
                            <div class="pagemenu">{$pagelist}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{include file="jscom.htm"}
{literal}
<script>
    $(document).ready(function() {
        //体检渠道管理--销售统计  导出出订单
        $("#FromSubmit").click(function (e) {
            $("#RoomsForm").attr("action", "/lineCourse/Hourstudy");
            $("#RoomsForm").attr("method", "get");
            $("#RoomsForm").submit();
        })
        $("#FromExport").click(function (e) {
            $("#RoomsForm").attr("action", "/lineCourse?c=ExportHourstudy");
            $("#RoomsForm").attr("method", "post");
            $("#RoomsForm").submit();
        })
    })
</script>
{/literal}
<script src="{$PluginsUrl}jquery-form/jquery.form.js" type="text/javascript"></script>
<script src="{$JsUrl}int.form.js" type="text/javascript"></script>
</body>
</html>
