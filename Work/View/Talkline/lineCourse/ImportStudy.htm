<!DOCTYPE HTML>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    {include file="key.htm"}
</head>

<body>

<div class="page-container row condensed">
    {include file="page-sidebar.htm"}
    {include file="footer.htm"}
    <div class="page-content">
        {include file="header.htm"}
        <div class="mx20 breadcrumb-w">
            <div class="breadcrumb fl">
                <a href="/lineCourse/Lineclass">全国班课管理</a>
                <span>&gt; 班课学员导入</span>
            </div>
            <a href="javascript:" class="fr bakFromurl mt6"><i class="icon-back mr5"></i>返回</a>
            <div class="clear"></div>
        </div>

        <div class="content">
            <div id="container">
                <div class="bg-f row py20">
                    <form action="/{$u}/ImportroomStudy" method="post" enctype="multipart/form-data">
                        <input name="lineclass_id" value="{$lineclassOne.lineclass_id}" type="hidden">
                        <h2 class="p20">班课学员导入<span class="fr">
                        <button type="submit" class=" btn btn-success btn-cons"><i class="fa fa-check"></i>立即导入</button>
                        <button type="button" class="btn btn-primary ml10 bakFromurl">返回</button></span>
                        </h2>

                        <div class="form-group pl16">
                            <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                <tr>
                                    <td width="10%">请选择开始课时：</td>
                                    <td width="20%" class="pr10">
                                        <select name="st_lessontimes" id="st_lessontimes" class="form-control">
                                            <option value="" >请选择开始课时</option>
                                            {if $hourroomsList}
                                            {foreach from=$hourroomsList item=hourroomsVar key=key}
                                            <option value="{$hourroomsVar.hourrooms_lessontimes}">{$hourroomsVar.hourrooms_name}</option>
                                            {/foreach}
                                            {/if}
                                        </select>
                                    </td>
                                    <td width="10%">请选择结束课时：</td>
                                    <td width="20%" class="pr10">
                                        <select name="end_lessontimes" id="end_lessontimes" class="form-control">
                                            <option value="" >请选择结束课时</option>
                                            {if $hourroomsList}
                                            {foreach from=$hourroomsList item=hourroomsVar key=key}
                                            <option value="{$hourroomsVar.hourrooms_lessontimes}">{$hourroomsVar.hourrooms_name}</option>
                                            {/foreach}
                                            {/if}
                                        </select>
                                    </td>
                                    <td> </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-10" id="Form-Box-Operating">
                            {if $hourroomsOne}
                            <input name="hourrooms_id" value="{$hourroomsOne.hourrooms_id}" type="hidden">
                            <div class="panel-body">
                                <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table tc table-hover table-bordered">
                                    <thead>
                                    <tr>
                                        <th>教室号</th>
                                        <th>教室名称</th>
                                        <th>上课类型</th>
                                        <th>开始时间</th>
                                        <th>结束时间</th>
                                        <th>教师密码</th>
                                        <th>助教密码</th>
                                        <th>巡课密码</th>
                                        <th>上课密码</th>
                                        <th>当前在线数</th>
                                        <th>累计登陆人数</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <tr>
                                        <td>{$hourroomsOne.hourrooms_threenumber}</td>
                                        <td>{$hourroomsOne.hourrooms_name}</td>
                                        <td>{if $hourroomsOne.hourrooms_type == '0'}
                                            <span class="c-red">1对1</span>
                                            {elseif $hourroomsOne.hourrooms_type == '3'}
                                            <span class="c-red">1对N</span>
                                            {/if}</td>
                                        <td>{$hourroomsOne.hourrooms_starttime|date_format:'%Y-%m-%d %H:%M:%S'}</td>
                                        <td>{$hourroomsOne.hourrooms_endtime|date_format:'%Y-%m-%d %H:%M:%S'}</td>
                                        <td>{$hourroomsOne.hourrooms_chairmanpwd}</td>
                                        <td>{$hourroomsOne.hourrooms_assistantpwd}</td>
                                        <td>{$hourroomsOne.hourrooms_patrolpwd}</td>
                                        <td>{$hourroomsOne.hourrooms_confuserpwd}</td>
                                        <td>{$hourroomsOne.loginnums}</td>
                                        <td>{$hourroomsOne.linenums}</td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                            {/if}
                            <div class="form-group">
                                <div style="display: flex;align-items: center">
                                    <a id="down_file" href="/importexcel/talkline/网课学员模板.xlsx" class="btn btn-primary dropdown-toggle btn-demo-space">下载网课导入学员模板</a>
                                    <span class="" style="margin-left: 10px;"></span>
                                </div>
                            </div>
                            <div class="form-group">

                                <div style="display: flex;align-items: center">
                                    <!--<label for="uploadFile">&nbsp;</label>-->
                                    <a style="width:200px" data-color="rgb(255, 255, 255)" data-color-format="hex" id="cp4" class="btn btn-primary my-colorpicker-control upload-btn" data-colorpicker-guid="1">
                                        <span class="glyphicon glyphicon-floppy-open"></span>
                                        选择文件上传<input type="file" accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" id="uploadFile" name="uploadFile" class="ipt-file-click-one uploadFileComm" data-originalipt="report_file"></a>
                                    <span class="fileNameBox" style="margin-left: 10px;"></span>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>


    </div>
</div>
{include file="jscom.htm"}
<script src="{$PluginsUrl}jquery-form/jquery.form.js" type="text/javascript"></script>
<script src="{$JsUrl}int.form.js" type="text/javascript"></script>
</body>
</html>


