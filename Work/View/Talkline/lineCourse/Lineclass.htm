<!DOCTYPE HTML>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    {include file="key.htm"}
</head>

<body>

<div class="page-container row condensed">
    {include file="page-sidebar.htm"}
    {include file="footer.htm"}
    <div class="page-content">
        {include file="header.htm"}
        <div class="mx20 breadcrumb-w">
            <div class="breadcrumb fl">
                <span>全国班课管理</span>
            </div>
            <a href="javascript:" class="fr bakFromurl mt6"><i class="icon-back mr5"></i>返回</a>
            <div class="clear"></div>
        </div>
        <div class="content">
            <div id="container">
                <div class="row">
                    <div class="bg-f">
                        <div class="p20 f14">
                            <div class="form-group">
                                <form action="/{$u}" method="get" accept-charset="utf-8" id="RoomsForm">
                                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                        <tr>
                                            <td width="5%">搜索：</td>
                                            <td width="9%" class="pr10">
                                                <input name="starttime" id="starttime" value="{$datatype['starttime']}" class="form-control input-sm form_datetime" autocomplete="off" placeholder="班课开始时间" type="text">
                                            </td>
                                            <td width="9%" class="pr10">
                                                <input name="endtime" id="endtime" value="{$datatype['endtime']}" class="form-control input-sm form_datetime" autocomplete="off" placeholder="班课结束时间" type="text">
                                            </td>
                                            <td width="15%" class="pr10"><input name="keyword" class="form-control input-sm" placeholder="请输入班级名称" value="{$datatype['keyword']}" type="text"></td>
                                            <td width="20%">
                                                <button type="submit" id="FromSubmit" class="btn btn-primary ml10"><span class="glyphicon glyphicon-search c-f"></span> 搜索</button>
                                                <a type="button" class="btn btn-danger ml20" href="/{$u}/AddLineclass">新增全国班课</a>
                                            </td>
                                            <td><button type="submit" id="FromExport" class="btn btn-primary ml10 fr"><span class="glyphicon glyphicon-sort-by-attributes"></span> 导出课时</button></td>
                                        </tr>
                                    </table>
                                </form>
                            </div>
                            <form role="form" action="/{$u}?c=batchWork" method="post" enctype="application/x-www-form-urlencoded" class="AjaxForm">
                                <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table tc table-hover table-bordered">
                                    <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>班课名称</th>
                                        <th>班课别名</th>
                                        <th>班课编号</th>
                                        <th>排课数</th>
                                        <th>排课人次</th>
                                        <th>平均上课班平</th>
                                        <th>开始时间/结束时间</th>
                                        <th>更新时间</th>
                                        <th>创建时间</th>
                                        <th>操作</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    {if $dataList}
                                    {foreach from=$dataList item=dataVar}
                                    <tr id="list-{$dataVar.lineclass_id}">
                                        <td>{$dataVar.lineclass_id}</td>
                                        <td>{$dataVar.lineclass_cnname}</td>
                                        <td>{$dataVar.lineclass_enname}</td>
                                        <td>{$dataVar.lineclass_branch}</td>
                                        <td>{$dataVar.hourroomsnums}</td>
                                        <td>{$dataVar.studynums}</td>
                                        <td>{if $dataVar.hourroomsnums != 0}{($dataVar.studynums/$dataVar.hourroomsnums)|string_format:"%.2f"}{else}0{/if}</td>
                                        <td>{$dataVar.lineclass_stdate} ~ {$dataVar.lineclass_enddate}</td>
                                        <td>{$dataVar.lineclass_updatatime|date_format:'%Y-%m-%d %H:%M:%S'}</td>
                                        <td>{$dataVar.lineclass_createtime|date_format:'%Y-%m-%d %H:%M:%S'}</td>
                                        <td align="left">
                                            <a href="/{$u}/EditLineclass?lineclass_id={$dataVar.lineclass_id}" class="btn btn-primary btn-sm">编辑</a>
                                            <a href="/{$u}/Hourrooms?lineclass_id={$dataVar.lineclass_id}" class="btn btn-primary btn-sm"><span class="glyphicon glyphicon-th-list c-f"></span> 排课管理</a>
                                            <a href="/{$u}/ImportStudy?lineclass_id={$dataVar.lineclass_id}" class="btn btn-success btn-sm dropdown-toggle btn-demo-space">导入固定学员</a>
                                            <a href="/{$u}/classOneStudy?lineclass_id={$dataVar.lineclass_id}" class="btn btn-primary btn-sm">班课学员</a>

                                            {if $dataVar.studynums == 0 }
                                            <a href="javascript:;" data-element="list-{$dataVar.lineclass_id}" data-url="/{$u}?c=DelLineclass&lineclass_id={$dataVar.lineclass_id}" class="btn btn-danger btn-sm btn-del-action">
                                                <span class="glyphicon glyphicon-remove c-f"></span> 删除</a>
                                            {/if}
                                        </td>
                                    </tr>
                                    {/foreach}
                                    {/if}
                                    </tbody>
                                </table>
                            </form>
                            <div class="pagemenu">{$pagelist}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{include file="jscom.htm"}
{literal}
<script>
    $(document).ready(function() {
        //体检渠道管理--销售统计  导出出订单
        $("#FromSubmit").click(function (e) {
            $("#RoomsForm").attr("action", "/lineCourse/Lineclass");
            $("#RoomsForm").attr("method", "get");
            $("#RoomsForm").submit();
        })
        $("#FromExport").click(function (e) {
            $("#RoomsForm").attr("action", "/lineCourse?c=ExportLineclass");
            $("#RoomsForm").attr("method", "post");
            $("#RoomsForm").submit();
        })
    })
</script>
{/literal}
<script src="{$PluginsUrl}jquery-form/jquery.form.js" type="text/javascript"></script>
<script src="{$JsUrl}int.form.js" type="text/javascript"></script>
</body>
</html>
