<!DOCTYPE HTML>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    {include file="key.htm"}
</head>

<body>

<div class="page-container row condensed">
    {include file="page-sidebar.htm"}
    {include file="footer.htm"}
    <div class="page-content">
        {include file="header.htm"}
        <div class="mx20 breadcrumb-w">
            <div class="breadcrumb fl">
                <a href="/lineCourse/Lineclass">全国班课管理</a>
                <span>&gt; 班课信息管理</span>
            </div>
            <a href="javascript:" class="fr bakFromurl mt6"><i class="icon-back mr5"></i>返回</a>
            <div class="clear"></div>
        </div>
        <div class="content">
            <div id="container">
                <div class="bg-f py20">
                    {if !$dataVar}
                    <div class="alert alert-warning alert-dismissible m10" role="alert">
                        <button type="button" class="close" data-dismiss="alert"><span aria-hidden="true">×</span><span class="sr-only">Close</span></button>
                        <span class="glyphicon glyphicon-exclamation-sign f16 vam mr6"></span> 注：周排课时间 可按照周次以逗号隔开，如：周一/周三输入1,3;周日为0</div>
                    {/if}
                    <form action="/{$u}?c={$act}" method="post" enctype="application/x-www-form-urlencoded" class="AjaxForm">
                        <input name="lineclass_id" type="hidden" value="{$lineclassOne.lineclass_id}">
                        <div id="Form-Box-Operating">
                            <div class="px20 py20 f14 row">
                                <div class="form-group col-md-3">
                                    <label for="hourroomsnum"><em>*</em>排课数量</label>
                                    <input name="hourroomsnum" id="hourroomsnum" value="{$dataVar.hourroomsnum}" type="text" reg="[^ \f\n\r\t\v]" tip="排课数量" class="form-control" placeholder="请输入排课数量">
                                </div>
                                <div class="form-group col-md-3">
                                    <label for="hour_stdate"><em>*</em>排课开始日期</label>
                                    <div class="form-group">
                                        <input name="hour_stdate" id="hour_stdate" value="{$lineclassOne.lineclass_stdate}" type="text" autocomplete="off" class="form-control form_datetime" reg="[^ \f\n\r\t\v]" tip="排课开始日期" placeholder="请选择排课开始日期">
                                    </div>
                                </div>
                                <div class="form-group col-md-3">
                                    <label for="hour_enddate"><em>*</em>排课结束日期</label>
                                    <div class="form-group">
                                        <input name="hour_enddate" id="hour_enddate" value="{$lineclassOne.lineclass_enddate}" type="text" autocomplete="off" class="form-control form_datetime" reg="[^ \f\n\r\t\v]" tip="排课结束日期" placeholder="请选择排课结束日期">
                                    </div>
                                </div>
                                <div class="form-group col-md-3">
                                    <label for="hour_weeklimit"><em>*</em>周排课时间</label>
                                    <div class="form-group">
                                        <input name="hour_weeklimit" id="hour_weeklimit" value="{$lineclassOne.hour_weeklimit}" type="text" autocomplete="off" class="form-control" reg="[^ \f\n\r\t\v]" tip="周排课时间" placeholder="请输入周排课时间">
                                    </div>
                                </div>
                                <div class="form-group col-md-3">
                                    <label for="starttime"><em>*</em>课程开始时间</label>
                                    <div class="form-group">
                                        <input name="starttime" id="starttime" type="text" autocomplete="off" class="form-control form_datetimes" reg="[^ \f\n\r\t\v]" tip="上课开始时间" placeholder="请选择上课开始时间">
                                    </div>
                                </div>
                                <div class="form-group col-md-3">
                                    <label for="endtime"><em>*</em>课程结束时间</label>
                                    <div class="form-group">
                                        <input name="endtime" id="endtime" type="text" autocomplete="off" class="form-control form_datetimes" reg="[^ \f\n\r\t\v]" tip="上课结束时间" placeholder="请选择上课结束时间">
                                    </div>
                                </div>
                            </div>
                            <div class="form-group txtrg pr30">
                                <button type="button" class="btn btn-default mr40 bakFromurl">返回</button>
                                <button type="submit" class=" btn btn-primary btn-cons">提交保存</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{include file="jscom.htm"}
<script src="{$PluginsUrl}jquery-form/jquery.form.js" type="text/javascript"></script>
<script src="{$JsUrl}int.form.js" type="text/javascript"></script>
</body>
</html>
