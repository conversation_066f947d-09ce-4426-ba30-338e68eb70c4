<!DOCTYPE HTML>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    {include file="key.htm"}
</head>

<body>

<div class="page-container row condensed">
    {include file="page-sidebar.htm"}
    {include file="footer.htm"}
    <div class="page-content">
        {include file="header.htm"}
        <div class="mx20 breadcrumb-w">
            <div class="breadcrumb fl">
                <a href="/lineCourse/Lineclass">学员管理</a>
                <span>&gt; 学员课时学习明细</span>
            </div>
            <a href="javascript:" class="fr bakFromurl mt6"><i class="icon-back mr5"></i>返回</a>
            <div class="clear"></div>
        </div>
        <div class="content">
            <div id="container">
                <div class="row">
                    <div class="bg-f">
                        <div class="p20 f14">
                            <div class="form-group">
                                <form action="/{$u}/Lineparenter" method="get" accept-charset="utf-8" id="RoomsForm">
                                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                        <tr>
                                            <td width="5%">搜索：</td>
                                            <td width="25%" class="pr10"><input name="keyword" class="form-control input-sm" placeholder="请输入家长手机号/学员名称/学员编号" value="{$datatype['keyword']}" type="text"></td>
                                            <td width="20%">
                                                <button type="submit" id="FromSubmit" class="btn btn-primary ml10"><span class="glyphicon glyphicon-search c-f"></span> 搜索</button>
                                            </td>
                                            <td class="tl">
                                            </td>
                                        </tr>
                                    </table>
                                </form>
                            </div>
                            <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table tc table-hover table-bordered">
                                <thead>
                                <tr>
                                    <th>手机号</th>
                                    <th>密码</th>
                                    <th>绑定学员数量</th>
                                    <th>最后登录密码</th>
                                    <th width="15%">操作</th>
                                </tr>
                                </thead>
                                <tbody>
                                {if $dataList}
                                {foreach from=$dataList item=dataVar}
                                <tr id="list-{$dataVar.parenter_id}">
                                    <td>{HideStaring str=$dataVar.parenter_mobile}</td>
                                    <td>{$dataVar.parenter_bakpass}</td>
                                    <td>{$dataVar.studentnums}</td>
                                    <td>{$dataVar.parenter_lasttime|date_format:'%Y-%m-%d %H:%M:%S'}</td>
                                    <td align="left">
                                        <a href="javascript:;" data-element="list-{$dataVar.parenter_id}"
                                           data-url="/{$u}?c=ResetPassword&parenter_id={$dataVar.parenter_id}"
                                           class="btn btn-primary btn-send-action btn-sm">重置密码</a>
                                    </td>
                                </tr>
                                {/foreach}
                                {/if}
                                </tbody>
                            </table>
                            <div class="pagemenu">{$pagelist}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{include file="jscom.htm"}
{literal}
<script>
    $(document).ready(function() {
        //体检渠道管理--销售统计  导出出订单
        $("#FromSubmit").click(function (e) {
            $("#RoomsForm").attr("action", "/lineCourse/Lineparenter");
            $("#RoomsForm").attr("method", "get");
            $("#RoomsForm").submit();
        });
    });
</script>
{/literal}
<script src="{$PluginsUrl}jquery-form/jquery.form.js" type="text/javascript"></script>
<script src="{$JsUrl}int.form.js" type="text/javascript"></script>
</body>
</html>
