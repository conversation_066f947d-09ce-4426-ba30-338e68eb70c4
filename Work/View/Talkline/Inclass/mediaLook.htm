<!DOCTYPE HTML>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    {include file="key.htm"}
    <link rel="stylesheet" href="https://g.alicdn.com/de/prismplayer/2.8.7/skins/default/aliplayer-min.css" />
    <script type="text/javascript" charset="utf-8" src="https://g.alicdn.com/de/prismplayer/2.8.7/aliplayer-min.js"></script>
</head>

<body>

<div class="page-container row condensed">
    {include file="page-sidebar.htm"}
    {include file="footer.htm"}
    <div class="page-content">
        {include file="header.htm"}
        <div class="mx20 breadcrumb-w">
            <div class="breadcrumb fl">
                <span>{$dataVar.classcode_branch} 名师课堂</span>
            </div>
            <a href="javascript:" class="fr bakFromurl mt6"><i class="icon-back mr5"></i>返回</a>
            <div class="clear"></div>
        </div>
        <div class="content">
            <div id="container">
                <div class="row">
                    <div class="bg-f">
                        <div class="p20 f14">
                            <div class="form-group col-md-8">
                                <div class="prism-player" id="player-con"></div>
                            </div>
                            <div class="form-group col-md-4" style="height: 700px; overflow-y: scroll;">
                                <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table tc table-hover table-bordered">
                                    <thead>
                                    <tr>
                                        <th>单元名称</th>
                                        <th>视频播放</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    {if $mediaList}
                                    {foreach from=$mediaList item=mediaOne}
                                    <tr id="list-{$mediaOne.media_id}">
                                        <td>{$mediaOne.media_name}</td>
                                        <td>
                                            {if $mediaOne.media_id == $dataVar.media_id}
                                            <a class="btn btn-success btn-sm">正在播放</a>
                                            {else}
                                            <a href="/{$u}/mediaLook?media_id={$mediaOne.media_id}" class="btn btn-primary btn-sm">浏览视频</a>
                                            {/if}
                                        </td>
                                    </tr>
                                    {/foreach}
                                    {/if}
                                    </tbody>
                                </table>
                            </div>
                            <div class="clear"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{include file="jscom.htm"}
<script>
    var player = new Aliplayer({
                "id": "player-con",
                "source": "{$dataVar.media_url}",
                "width": "100%",
                "height": "700px",
                "autoplay": true,
                "isLive": false,
                "cover": "{$dataVar.media_coverimg}",
                "rePlay": false,
                "playsinline": true,
                "preload": true,
                "controlBarVisibility": "hover",
                "useH5Prism": true,
                "skinLayout": [
                    {
                        "name": "bigPlayButton",
                        "align": "blabs",
                        "x": 30,
                        "y": 80
                    },
                    {
                        "name": "H5Loading",
                        "align": "cc"
                    },
                    {
                        "name": "controlBar",
                        "align": "blabs",
                        "x": 0,
                        "y": 0,
                        "children": [
                            {
                                "name": "progress",
                                "align": "blabs",
                                "x": 0,
                                "y": 44
                            },
                            {
                                "name": "playButton",
                                "align": "tl",
                                "x": 15,
                                "y": 12
                            },
                            {
                                "name": "timeDisplay",
                                "align": "tl",
                                "x": 10,
                                "y": 7
                            },
                            {
                                "name": "fullScreenButton",
                                "align": "tr",
                                "x": 10,
                                "y": 12
                            },
                            {
                                "name": "volume",
                                "align": "tr",
                                "x": 5,
                                "y": 10
                            }
                        ]
                    }
                ]
            }, function (player) {
                console.log("The player is created");
            }
    );
</script>
</body>
</html>
