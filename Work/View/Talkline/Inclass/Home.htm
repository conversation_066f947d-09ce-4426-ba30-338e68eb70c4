<!DOCTYPE HTML>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    {include file="key.htm"}
</head>

<body>

<div class="page-container row condensed">
    {include file="page-sidebar.htm"}
    {include file="footer.htm"}
    <div class="page-content">
        {include file="header.htm"}
        <div class="mx20 breadcrumb-w">
            <div class="breadcrumb fl">
                <span>名师课堂查看</span>
            </div>
            <a href="javascript:" class="fr bakFromurl mt6"><i class="icon-back mr5"></i>返回</a>
            <div class="clear"></div>
        </div>
        <div class="content">
            <div id="container">
                <div class="row">
                    <div class="bg-f">
                        <div class="p20 f14">
                            <div class="form-group">
                                <form action="/Inclass/Home" method="get" accept-charset="utf-8" id="RoomsForm">
                                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                        <tr>
                                            <td width="5%">搜索：</td>
                                            <td width="35%" class="pr10">
                                                <select name="classcode_branch" id="linerooms_fromclass" class="form-control chosen-select">
                                                    <option value="">请检索班别</option>
                                                    {if $classcodeList}
                                                    {foreach from=$classcodeList item=classcodeOne}
                                                    <option value="{$classcodeOne.classcode_branch}" {if $classcodeOne.classcode_branch == $datatype.classcode_branch}selected{/if}>{$classcodeOne.classcode_name} ({$classcodeOne.classcode_branch})</option>
                                                    {/foreach}
                                                    {/if}
                                                </select>
                                            </td>
                                            <td width="25%"><input name="keyword" class="form-control input-sm" placeholder="请输入单元名称" value="{$datatype['keyword']}" type="text"></td>
                                            <td width="15%">
                                                <button type="submit" id="FromSubmit" class="btn btn-primary ml10">
                                                    <span class="glyphicon glyphicon-search c-f"></span> 搜索
                                                </button>
                                            </td>
                                            <td></td>
                                        </tr>
                                    </table>
                                </form>
                            </div>
                            <form role="form" action="/{$u}?c=batchWork" method="post" enctype="application/x-www-form-urlencoded" class="AjaxForm">
                                <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table tc table-hover table-bordered">
                                    <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>班别名称</th>
                                        <th>班别编号</th>
                                        <th>视频名称</th>
                                        <th>视频封面</th>
                                        <th>操作</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    {if $dataList}
                                    {foreach from=$dataList item=dataVar}
                                    <tr id="list-{$dataVar.media_id}">
                                        <td>{$dataVar.media_id}</td>
                                        <td>{$dataVar.classcode_name}</td>
                                        <td>{$dataVar.classcode_branch}</td>
                                        <td>{$dataVar.media_name}</td>
                                        <td>
                                            {if $dataVar.media_coverimg != ''}
                                            <a data-imgurl="{$dataVar.media_coverimg}" class="cp Opon-Img-View showico" title="查看效果"></a>
                                            {/if}
                                        </td>
                                        <td style="text-align: center;">
                                            <a href="/{$u}/mediaLook?media_id={$dataVar.media_id}" class="btn btn-primary btn-sm">浏览视频</a>
                                        </td>
                                    </tr>
                                    {/foreach}
                                    {/if}
                                    </tbody>
                                </table>
                            </form>
                            <div class="pagemenu">{$pagelist}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{include file="jscom.htm"}
{literal}
<script>
    $(document).ready(function() {
        //体检渠道管理--销售统计  导出出订单
        $("#FromSubmit").click(function (e) {
            $("#RoomsForm").attr("action", "/Inclass/Home");
            $("#RoomsForm").attr("method", "get");
            $("#RoomsForm").submit();
        })
    })
</script>
{/literal}
<script src="{$PluginsUrl}jquery-form/jquery.form.js" type="text/javascript"></script>
<script src="{$JsUrl}int.form.js" type="text/javascript"></script>
</body>
</html>
