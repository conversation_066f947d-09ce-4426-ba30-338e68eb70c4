<!DOCTYPE HTML>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    {include file="key.htm"}
</head>

<body>

<div class="page-container row condensed">
    {include file="page-sidebar.htm"}
    {include file="footer.htm"}
    <div class="page-content">
        {include file="header.htm"}
        <div class="mx20 breadcrumb-w">
            <div class="breadcrumb fl">
                <span>教师排课明细表</span>
            </div>
            <a href="javascript:" class="fr bakFromurl mt6"><i class="icon-back mr5"></i>返回</a>
            <div class="clear"></div>
        </div>
        <div class="content">
            <div id="container">
                <div class="bg-f table_zx">
                    <div class="data_box">
                        <a class="prev_ico" href="/Inclass/Foreign?day={$thisweek.lastweek_start}"></a>
                        <h4>{$thisweek.nowweek_cnname}</h4>
                        <a class="next_ico" href="/Inclass/Foreign?day={$thisweek.nextweek_start}"></a>
                    </div>
                    <script>
                        var courseList = [];
                    </script>
                    {if $stafferList}
                    {foreach from=$stafferList item=stafferOne key=key}
                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                        <thead>
                        <tr>
                            <th width="30%" style="text-align: left;">所在校区：{$stafferOne.school_cnname} </th>
                            <th width="40%" style="text-align: left;">教师姓名：{$stafferOne.staffer_cnname} </th>
                            <th width="30%" style="text-align: left;"> 教师编号：{$stafferOne.staffer_branch} </th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr>
                            <td colspan="3">
                                <div id="coursesTable{{$stafferOne.staffer_branch}}" class="coursesTableList"></div>
                                <div class="clear"></div>
                                <script>
                                    courseList.push({$stafferOne.weekjson});
                                </script>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                    {/foreach}
                    {/if}
                </div>
            </div>
        </div>
    </div>
</div>
{include file="jscom.htm"}
<script src="{$JsUrl}Timetables.min.js" type="text/javascript"></script>
{literal}
<script>
    function generateTimeList (delta) {
        var result = [], date = new Date();
        var startTimes = '08:00';
        date.setHours(8);
        date.setMinutes(delta);
        var day = date.getDate();
        var index = 1;
        while (date.getDate() === day && startTimes < '20:30') {
            var h = date.getHours();
            var m = date.getMinutes();
            if (h <= 9) {
                h = "0" + h;
            } else {
                h = h;
            }
            if (m <= 9) {
                m = "0" + m;
            } else {
                m = m;
            }
            var endTimes = h+':'+m;
            var courseName = startTimes+'-'+endTimes;
            result.push([{index: index, name: courseName}, 1]);
            index++;
            date.setMinutes(m + delta);
            startTimes = endTimes;
        }
        return result
    }
    /*var courseList = [
        ['大学英语(Ⅳ)@10203', '大学英语(Ⅳ)@10203', '', '', '', '', '毛概@14208', '毛概@14208', '', '', '', '选修', '选修', '选修', '选修', '选修', '选修', '选修', '选修', '选修', '选修', '选修', '选修', '选修'],
    ];*/
    var courseType = generateTimeList(30);
    // 实例化(初始化课表)
    /**/
    $(".coursesTableList").each(function(index,element){
        new Timetables({
            el: '#'+$(this).attr("id"),
            timetables: courseList[index],
            week: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
            timetableType: courseType,
            gridOnClick: function (e) {
                errormotify(e.name + '  ' + e.week + ', 第' + e.index + '节课, 课长' + e.length + '节');
                console.log(e);
            },
            styles: {
                Gheight: 30,
                leftHandWidth:150
            }
        });
    })
</script>
{/literal}
</body>
</html>
