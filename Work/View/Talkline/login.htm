<!DOCTYPE HTML>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>课叮铛 网课管理系统</title>
    <link href="{$CssUrl}style.css" rel="stylesheet" type="text/css">
    <link href="{$CssUrl}pages.css" rel="stylesheet" type="text/css">
    <link href="{$CssUrl}ls.css" rel="stylesheet" type="text/css">
    <style>
    html,body {
        width: 100%;
        height: 100%;
    }
    @media screen and (max-width: 1400px) {
       .body-main {
           background-size: 36% !important;
           background-position: center bottom !important;
       }
       .login-box .login-form {
           width: 320px;
           margin: 0 auto;
           padding: 10px 40px 20px;
       }
       .login-form .login-title {
           padding-bottom: 20px;
       }
       .login-form .login-title span {
           font-size: 24px;
           padding-top: 10px;
       }
       .login-box .login-form .input-mk .mks {
           padding: 5px 7px;
       } 
    }
    </style>
</head>

<body>
<div class="body-main" style="width: 100%;height: 100%;background-color:#f0f1f5;background-image:url({$ImgUrl}/bg-term1.jpg);background-repeat:no-repeat;background-position: center 580px;background-size:595px 395px;">
    <div class="login-box">
        <div class="login-form">
            <div class="login-title tc pb50">
                <span class="f30 dib pt20 vam">课叮铛 网课管理系统</span>
            </div>
            <form action="/Heatstart?c=Login" method="post" enctype="application/x-www-form-urlencoded" class="AjaxForm">
                <div class="input-mk mb20">
                    <div class="mks">
                        <input name="company_code" type="text" class="inputtip" placeholder="请输入集团编号">
                    </div>
                </div>
                <div class="input-mk mb20">
                    <div class="mks">
                        <input name="staffer_branch" type="text" class="inputtip" placeholder="请输入管理员账号">
                    </div>
                </div>
                <div class="input-mk mb20">
                    <div class="mks">
                        <input name="staffer_pass" type="password" class="inputtip" placeholder="请输入管理员密码">
                    </div>
                </div>
                <div class="input-mk mb20">
                    <input type="submit" class="login-btn" value="登 录">
                </div>
            </form>
        </div>
    </div>
</div>
{include file="jscom.htm"}
<script src="{$JsUrl}int.com.js" type="text/javascript"></script>
<script src="{$PluginsUrl}jquery-form/jquery.form.js" type="text/javascript"></script>
<script src="{$JsUrl}int.form.js" type="text/javascript"></script>
</body>
</html>
