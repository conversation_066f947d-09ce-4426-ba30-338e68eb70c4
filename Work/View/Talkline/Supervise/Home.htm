<!DOCTYPE HTML>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    {include file="key.htm"}
</head>

<body>

<div class="page-container row condensed">
    {include file="page-sidebar.htm"}
    {include file="footer.htm"}
    <div class="page-content">
        {include file="header.htm"}
        <div class="mx20 breadcrumb-w">
            <div class="breadcrumb fl">
                <span>学员监管账户交易订单</span>
            </div>
            <a href="javascript:" class="fr bakFromurl mt6"><i class="icon-back mr5"></i>返回</a>
            <div class="clear"></div>
        </div>
        <div class="content">
            <div id="container">
                <div class="row">
                    <div class="bg-f">
                        <div class="p20 f14">
                            <div class="form-group">
                                <form action="/{$u}" method="get" accept-charset="utf-8" id="RoomsForm">
                                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                        <tr>
                                            <td width="5%">搜索：</td>
                                            <td width="10%" class="pr10">
                                                <select name="charge_type" id="charge_type" class="form-control">
                                                    <option value="">订单类型</option>
                                                    <option value="0" {if $datatype.charge_type == '0'}selected{/if}>缴费订单</option>
                                                    <option value="1" {if $datatype.charge_type == '1'}selected{/if}>退款订单</option>
                                                </select>
                                            </td>
                                            <td width="9%" class="pr10">
                                                <input name="editstarttime" id="editstarttime" value="{$datatype['editstarttime']}" class="form-control input-sm form_datetime" autocomplete="off" placeholder="网课开始时间" type="text">
                                            </td>
                                            <td width="9%" class="pr10">
                                                <input name="editendtime" id="editendtime" value="{$datatype['editendtime']}" class="form-control input-sm form_datetime" autocomplete="off" placeholder="网课结束时间" type="text">
                                            </td>
                                            <td width="15%" class="pr10"><input name="keyword" class="form-control input-sm" placeholder="请输入订单号/学生编号" value="{$datatype['keyword']}" type="text"></td>
                                            <td width="20%">
                                                <button type="submit" id="FromSubmit" class="btn btn-primary ml10"><span class="glyphicon glyphicon-search c-f"></span> 搜索</button>
                                            </td>
                                            <td></td>
                                        </tr>
                                    </table>
                                </form>
                            </div>
                            <form role="form" action="/{$u}?c=batchWork" method="post" enctype="application/x-www-form-urlencoded" class="AjaxForm">
                                <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table tc table-hover table-bordered">
                                    <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>校区编号</th>
                                        <th>校区名称<br />主体名称</th>
                                        <th>学员姓名<br />学员编号</th>
                                        <th>交易类型/时间</th>
                                        <th>课程代码</th>
                                        <th>单号/支付编号</th>
                                        <th>支付金额</th>
                                        <th>操作</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    {if $dataList}
                                    {foreach from=$dataList item=dataVar}
                                    <tr id="list-{$dataVar.linerooms_id}">
                                        <td>{$dataVar.charge_id}</td>
                                        <td>{$dataVar.school_branch}</td>
                                        <td>{$dataVar.school_cnname}<br />{$dataVar.companies_cnname}</td>
                                        <td>{$dataVar.student_branch}<br />{$dataVar.student_cnname}</td>
                                        <td>{if $dataVar.charge_type == '0'}
                                            <span class="c-red">缴费</span>
                                            {elseif $dataVar.charge_type == '1'}
                                            <span class="c-red">退费</span>
                                            {/if}<br />{$dataVar.pay_successtime|date_format:'%Y-%m-%d %H:%M:%S'}</td>
                                        <td>{$dataVar.course_branch}</td>
                                        <td>{$dataVar.order_pid}<br />{$dataVar.pay_pid}</td>
                                        <td>{$dataVar.pay_price}</td>
                                        <td align="left">
                                            <a href="/{$u}/Look?linerooms_id={$dataVar.linerooms_id}" class="btn btn-primary btn-sm">查看</a>
                                        </td>
                                    </tr>
                                    {/foreach}
                                    {/if}
                                    </tbody>
                                </table>
                            </form>
                            <div class="pagemenu">{$pagelist}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{include file="jscom.htm"}
<script src="{$PluginsUrl}jquery-form/jquery.form.js" type="text/javascript"></script>
<script src="{$JsUrl}int.form.js" type="text/javascript"></script>
</body>
</html>
