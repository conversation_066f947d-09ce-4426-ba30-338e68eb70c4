<!DOCTYPE HTML>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>吉的堡成长中心 学生情况表</title>
    <link rel="shortcut icon" href="/favicon.ico">
    <!-- 预加载 -->
    <link href="{$CssUrl}editor-awesome.min.css" rel="stylesheet">
    <link href="{$CssUrl}bootstrap.min.css" rel="stylesheet" type="text/css">
    <link href="{$CssUrl}font-awesome.min.css" rel="stylesheet" type="text/css">
    <link href="{$CssUrl}jquery.sidr.light.css" rel="stylesheet" type="text/css">
    <link href="{$CssUrl}jquery.datetimepicker.css" rel="stylesheet" type="text/css">
    <link href="{$CssUrl}jquery.easydropdown.css" rel="stylesheet" type="text/css">
    <link href="{$PluginsUrl}froala-editor/froala_editor.min.css" rel="stylesheet" type="text/css">
    <link href="{$PluginsUrl}jquery-chosen/jquery-chosen.css" rel="stylesheet" type="text/css">
    <!-- diy -->
    <link href="{$CssUrl}style.css" rel="stylesheet" type="text/css">
    <link href="{$CssUrl}pages.css" rel="stylesheet" type="text/css">
    <link href="{$CssUrl}ls.css" rel="stylesheet" type="text/css">
    <link href="{$CssUrl}int.timepicker.css" rel="stylesheet" type="text/css">
    <link href="{$CssUrl}jquery.monthpicker.css" rel="stylesheet" type="text/css">
</head>

<body>
<div class="page-container row condensed">
    <div id="container" class="p20">
        <div class="form-group">
            <form action="/Api/ClassLog" method="get" accept-charset="utf-8">
                <table width="100%" border="0" cellspacing="0" cellpadding="0">
                    <tr>
                        <td width="5%" style="color: #FFFFFF;">搜索：</td>
                        <td width="15%" class="pr10"><input name="class_branch" class="form-control input-sm" placeholder="请输入班级编号" value="{$datatype['class_branch']}" type="text"></td>
                        <td width="15%" class="pr10"><input name="keyword" class="form-control input-sm" placeholder="请输入学员姓名/学编号" value="{$datatype['keyword']}" type="text"></td>
                        <td width="15%" class="pr10"><input name="pswd" class="form-control input-sm" placeholder="请输入动态查询密码" value="{$datatype['pswd']}" type="text"></td>
                        <td width="20%">
                            <button type="submit"  class="btn btn-primary ml10"><span class="glyphicon glyphicon-search c-f"></span> 搜索</button>
                        </td>
                        <td>
                        </td>
                    </tr>
                </table>
            </form>
        </div>
        <div class="bg-f p20">
            <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table tc table-hover table-bordered">
                <thead>
                <tr>
                    <th>学员编号</th>
                    <th>中文名</th>
                    <th>英文名</th>
                    <th>性别</th>
                    <th>联系电话</th>
                    <th>入班时间</th>
                    <th>结班时间</th>
                    <th>消耗单价</th>
                    <th>课程余额</th>
                    <th>剩余课次</th>
                </tr>
                </thead>
                <tbody>
                {if $studentList}
                {foreach from=$studentList item=dataVar}
                <tr id="list-{$dataVar.student_branch}">
                    <td>{$dataVar.student_branch}</td>
                    <td>{$dataVar.student_cnname}</td>
                    <td>{$dataVar.student_enname}</td>
                    <td><span class="c-red">{$dataVar.student_sex}</span></td>
                    <td>{$dataVar.family_mobile}</td>
                    <td>{$dataVar.study_beginday}</td>
                    <td>{$dataVar.study_endday}</td>
                    <td>{$dataVar.coursebalance_figure}</td>
                    <td>{$dataVar.coursebalance_unitexpend}</td>
                    <td>{$dataVar.coursebalance_time}</td>
                </tr>
                {/foreach}
                {/if}
                </tbody>
            </table>
        </div>
    </div>
</div>
{include file="jscom.htm"}
</body>
</html>
