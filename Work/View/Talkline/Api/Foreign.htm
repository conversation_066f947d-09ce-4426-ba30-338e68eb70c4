<!DOCTYPE HTML>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>吉的堡 外师排课明细图</title>
    <link rel="shortcut icon" href="/favicon.ico">
    <!-- 预加载 -->
    <link href="{$CssUrl}editor-awesome.min.css" rel="stylesheet">
    <link href="{$CssUrl}bootstrap.min.css" rel="stylesheet" type="text/css">
    <link href="{$CssUrl}font-awesome.min.css" rel="stylesheet" type="text/css">
    <link href="{$CssUrl}jquery.sidr.light.css" rel="stylesheet" type="text/css">
    <link href="{$CssUrl}jquery.datetimepicker.css" rel="stylesheet" type="text/css">
    <link href="{$CssUrl}jquery.easydropdown.css" rel="stylesheet" type="text/css">
    <link href="{$PluginsUrl}froala-editor/froala_editor.min.css" rel="stylesheet" type="text/css">
    <link href="{$PluginsUrl}jquery-chosen/jquery-chosen.css" rel="stylesheet" type="text/css">
    <!-- diy -->
    <link href="{$CssUrl}style.css" rel="stylesheet" type="text/css">
    <link href="{$CssUrl}pages.css" rel="stylesheet" type="text/css">
    <link href="{$CssUrl}ls.css" rel="stylesheet" type="text/css">
    <link href="{$CssUrl}int.timepicker.css" rel="stylesheet" type="text/css">
    <link href="{$CssUrl}jquery.monthpicker.css" rel="stylesheet" type="text/css">
</head>

<body>

<div class="page-container row condensed">
    <div id="container" class="p20">
        <div class="form-group">
            <form action="/Api/Foreign" method="get" accept-charset="utf-8">
                <table width="100%" border="0" cellspacing="0" cellpadding="0">
                    <tr>
                        <td width="5%">搜索：</td>
                        <td width="10%" class="pr10">
                            <select name="organize_id" id="organize_id" class="form-control">
                                <option value="">请选择督导区域</option>
                                {if $organizeClass}
                                {foreach from=$organizeClass item=organizeOne}
                                <option value="{$organizeOne.organize_id}" {if $organizeOne.organize_id == $datatype.organize_id}selected{/if}>{$organizeOne.organize_cnname}</option>
                                {/foreach}
                                {/if}
                            </select>
                        </td>
                        <td width="18%">
                            <select name="school_id" id="school_id" class="form-control chosen-select">
                                <option value="">请选择校园</option>
                                {if $schoolList}
                                {foreach from=$schoolList item=schoolOne}
                                <option value="{$schoolOne.school_id}" {if $schoolOne.school_id == $datatype.school_id}selected{/if}>{$schoolOne.school_cnname}</option>
                                {/foreach}
                                {/if}
                            </select>
                        </td>
                        <td width="10%" class="pr10">
                            <select name="staffer_isparttime" id="staffer_isparttime" class="form-control">
                                <option value="" {if !$datatype.staffer_isparttime == ''}selected{/if}>请选择教师类型</option>
                                <option value="0" {if $datatype.staffer_isparttime == '0'}selected{/if}>全职教师</option>
                                <option value="1" {if $datatype.staffer_isparttime == '1'}selected{/if}>兼职教师</option>
                            </select>
                        </td>
                        <td width="15%" class="pr10"><input name="keyword" class="form-control input-sm" placeholder="请输入教师名称/教师编号" value="{$datatype['keyword']}" type="text"></td>
                        <td width="20%">
                            <button type="submit" class="btn btn-primary ml10"><span class="glyphicon glyphicon-search c-f"></span> 搜索</button>
                        </td>
                        <td>
                            <a href="/Api/Foreign" class="btn btn-primary ml10 fr"><span class="glyphicon glyphicon-sort-by-attributes"></span> 返回</a>
                            <a href="/Api/Cnsenior" class="btn btn-primary ml10 fr"><span class="glyphicon glyphicon-indent-left"></span> 中师课表</a>
                        </td>
                    </tr>
                </table>
            </form>
        </div>
        <div class="bg-f table_zx p20">
            <div class="data_box">
                <a class="prev_ico" href="/Api/Foreign?school_id={$datatype.school_id}&staffer_isparttime={$datatype.staffer_isparttime}&keyword={$datatype.keyword}&day={$thisweek.lastweek_start}"></a>
                <h4>{$thisweek.nowweek_cnname}</h4>
                <a class="next_ico" href="/Api/Foreign?school_id={$datatype.school_id}&staffer_isparttime={$datatype.staffer_isparttime}&keyword={$datatype.keyword}&day={$thisweek.nextweek_start}"></a>
            </div>
            <script>
                var courseList = [];
            </script>
            {if $stafferList}
            {foreach from=$stafferList item=stafferOne key=key}
            <table width="100%" border="0" cellspacing="0" cellpadding="0">
                <thead>
                <tr>
                    <th width="20%" style="text-align: left;">所在校区：{$stafferOne.school_cnname} </th>
                    <th width="15%" style="text-align: left;">教师姓名：{$stafferOne.staffer_cnname}-{$stafferOne.staffer_id} </th>
                    <th width="15%" style="text-align: left;">教师类型：{if $stafferOne.staffer_isparttime == '1'}兼职教师{else}全职教师{/if} </th>
                    <th width="15%" style="text-align: left;"> 教师编号：{$stafferOne.staffer_branch} </th>
                    <th width="20%" style="text-align: left;">带班数：{$stafferOne.classnums};课时总数：{$stafferOne.classhours}小时</th>
                </tr>
                </thead>
                <tbody>
                <tr>
                    <td colspan="5">
                        <div id="coursesTable{{$stafferOne.staffer_branch}}" class="coursesTableList"></div>
                        <div class="clear"></div>
                        <script>
                            courseList.push({$stafferOne.weekjson});
                        </script>
                    </td>
                </tr>
                </tbody>
            </table>
            {/foreach}
            {/if}
        </div>
    </div>
</div>
{include file="jscom.htm"}
<script src="{$JsUrl}Timetables.min.js" type="text/javascript"></script>
{literal}
<script>
    function generateTimeList (delta) {
        var result = [], date = new Date();
        var startTimes = '08:00';
        date.setHours(8);
        date.setMinutes(delta);
        var day = date.getDate();
        var index = 1;
        while (date.getDate() === day && startTimes < '20:30') {
            var h = date.getHours();
            var m = date.getMinutes();
            if (h <= 9) {
                h = "0" + h;
            } else {
                h = h;
            }
            if (m <= 9) {
                m = "0" + m;
            } else {
                m = m;
            }
            var endTimes = h+':'+m;
            var courseName = startTimes+'-'+endTimes;
            result.push([{index: index, name: courseName}, 1]);
            index++;
            date.setMinutes(m + delta);
            startTimes = endTimes;
        }
        return result
    }
    /*var courseList = [
        ['大学英语(Ⅳ)@10203', '大学英语(Ⅳ)@10203', '', '', '', '', '毛概@14208', '毛概@14208', '', '', '', '选修', '选修', '选修', '选修', '选修', '选修', '选修', '选修', '选修', '选修', '选修', '选修', '选修'],
    ];*/
    var courseType = generateTimeList(30);
    // 实例化(初始化课表)
    /**/
    $(".coursesTableList").each(function(index,element){
        new Timetables({
            el: '#'+$(this).attr("id"),
            timetables: courseList[index],
            week: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
            timetableType: courseType,
            gridOnClick: function (e) {
                if(e.name !== ''){
                    errormotify(e.name + '  ' + e.week + ', 第' + e.index + '节课, 课长' + e.length + '节');
                }
                console.log(e);
            },
            styles: {
                Gheight: 30,
                leftHandWidth:150
            }
        });
    })
</script>
{/literal}
</body>
</html>
