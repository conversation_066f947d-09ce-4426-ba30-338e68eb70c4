<!DOCTYPE HTML>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    {include file="key.htm"}
</head>

<body>

<div class="page-container row condensed">
    {include file="page-sidebar.htm"}
    {include file="footer.htm"}
    <div class="page-content">
        {include file="header.htm"}
        <div class="mx20 breadcrumb-w">
            <div class="breadcrumb fl">
                <a href="/{$u}">退款信息管理</a>
                <span>&gt; 导入退款信息</span>
            </div>
            <a href="javascript:" class="fr bakFromurl mt6"><i class="icon-back mr5"></i>返回</a>
            <div class="clear"></div>
        </div>
        <div class="content">
            <div id="container">
                <div class="row">
                    <div class="bg-f row py20">
                        <form action="/{$u}/ImportOrderTo?site_id={$websites.site_id}" method="post" enctype="multipart/form-data">
                            <h2 class="p20">退款信息管理--退款信息导入<span class="fr">
                                <button type="submit" class=" btn btn-success btn-cons"><i class="fa fa-check"></i>立即导入</button>
                                <button type="button" class="btn btn-primary ml10 bakFromurl">返回</button></span>
                            </h2>
                            <div class="col-md-8" id="Form-Box-Operating">
                                <div class="form-group">
                                    <div class="col-md-3">
                                        <label for="uploadFile">&nbsp;</label>
                                        <a data-color="rgb(255, 255, 255)" data-color-format="hex" id="cp4" class="btn btn-primary my-colorpicker-control upload-btn" data-colorpicker-guid="1">
                                            <span class="glyphicon glyphicon-floppy-open"></span>
                                            选择文件上传<input type="file" accept=".csv, application/vnd.ms-excel, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" id="uploadFile" name="uploadFile" class="ipt-fileone-click" data-originalipt="report_file"></a>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{include file="jscom.htm"}
</body>
</html>
