<!DOCTYPE HTML>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    {include file="key.htm"}
</head>

<body>

<div class="page-container row condensed">
    {include file="page-sidebar.htm"}
    {include file="footer.htm"}
    <div class="page-content">
        {include file="header.htm"}
        <div class="mx20 breadcrumb-w">
            <div class="breadcrumb fl">
                <a href="/Reimburse">退款信息管理</a>
                <span>&gt; 退款进度编辑</span>
            </div>
            <a href="javascript:" class="fr bakFromurl mt6"><i class="icon-back mr5"></i>返回</a>
            <div class="clear"></div>
        </div>
        <div class="content">
            <div id="container">
                <div class="bg-f py20">
                    {if !$dataVar}
                    <div class="alert alert-warning alert-dismissible m10" role="alert">
                        <button type="button" class="close" data-dismiss="alert"><span aria-hidden="true">×</span><span class="sr-only">Close</span></button>
                        <span class="glyphicon glyphicon-exclamation-sign f16 vam mr6"></span> 注：同中文名英文名仅限添加一次，如重复，请删除再添加！</div>
                    {/if}
                    <form action="/{$u}?c={$act}" method="post" enctype="application/x-www-form-urlencoded" class="AjaxForm">
                        <input name="reimburse_id" type="hidden" value="{$dataVar.reimburse_id}">
                        <div id="Form-Box-Operating">
                            <div class="col-md-9">
                                <div class="px20 py20 f14 row">
                                    <div class="form-group col-md-3">
                                        <label for="school_name"><em>*</em>请选择退费校区</label>
                                        <select name="school_name" reg="[^0]" id="school_name" class="form-control">
                                            <option value="0">请选择退费校区</option>
                                            <option value="城北校区" {if $dataVar.school_name == '城北校区'}selected{/if}>城北校区</option>
                                            <option value="城东校区" {if $dataVar.school_name == '城东校区'}selected{/if}>城东校区</option>
                                            <option value="城南校区" {if $dataVar.school_name == '城南校区'}selected{/if}>城南校区</option>
                                            <option value="城西校区" {if $dataVar.school_name == '城西校区'}selected{/if}>城西校区</option>
                                            <option value="中楠校区" {if $dataVar.school_name == '中楠校区'}selected{/if}>中楠校区</option>
                                            <option value="张浦校区" {if $dataVar.school_name == '张浦校区'}selected{/if}>张浦校区</option>
                                        </select>
                                    </div>
                                    <div class="form-group col-md-3">
                                        <label for="reimburse_cnname"><em>*</em>中文名</label>
                                        <input name="reimburse_cnname" id="reimburse_cnname" value="{$dataVar.reimburse_cnname}" type="text" reg="[^ \f\n\r\t\v]" tip="中文名不能为空" class="form-control" placeholder="请输入中文名">
                                    </div>
                                    <div class="form-group col-md-3">
                                        <label for="reimburse_enname">英文名</label>
                                        <input name="reimburse_enname" id="reimburse_enname" value="{$dataVar.reimburse_enname}" type="text" class="form-control" placeholder="请输入英文名">
                                    </div>
                                    <div class="form-group col-md-3">
                                        <label for="reimburse_isicept"><em>*</em>是否包含ICEPT费用</label>
                                        <select name="reimburse_isicept" reg="[^ \f\n\r\t\v]" id="reimburse_isicept" class="form-control">
                                            <option value="">请选择是否包含ICEPT费用</option>
                                            <option value="0" {if $dataVar.reimburse_isicept == 0}selected{/if}>不包含</option>
                                            <option value="1" {if $dataVar.reimburse_isicept == 1}selected{/if}>包含</option>
                                        </select>
                                    </div>
                                    <div class="clear"></div>
                                    <div class="form-group col-md-3">
                                        <label for="reimburse_okprice"><em>*</em>双方确认退款金额</label>
                                        <input name="reimburse_okprice" id="reimburse_okprice" value="{$dataVar.reimburse_okprice}" type="text" class="form-control" reg="[^ \f\n\r\t\v]" tip="双方确认退款金额不能为空" placeholder="请输入双方确认退款金额">
                                    </div>
                                    <div class="form-group col-md-3">
                                        <label for="reimburse_bankname"><em>*</em>退费银行名称</label>
                                        <input name="reimburse_bankname" id="reimburse_bankname" value="{$dataVar.reimburse_bankname}" type="text" class="form-control" reg="[^ \f\n\r\t\v]" tip="退费银行名称不能为空" placeholder="请输入退费银行名称">
                                    </div>
                                    <div class="form-group col-md-3">
                                        <label for="reimburse_bankcode"><em>*</em>退费银行卡号</label>
                                        <input name="reimburse_bankcode" id="reimburse_bankcode" value="{$dataVar.reimburse_bankcode}" type="text" class="form-control" reg="[^ \f\n\r\t\v]" tip="退费银行卡号不能为空" placeholder="请输入退费银行卡号">
                                    </div>
                                    <div class="form-group col-md-3">
                                        <label for="reimburse_banknode"><em>*</em>退费银行开户点</label>
                                        <input name="reimburse_banknode" id="reimburse_banknode" value="{$dataVar.reimburse_banknode}" type="text" class="form-control" reg="[^ \f\n\r\t\v]" tip="退费银行开户点不能为空" placeholder="请输入退费银行开户点">
                                    </div>
                                    <div class="clear"></div>
                                    <div class="form-group col-md-3">
                                        <label for="reimburse_payeename"><em>*</em>收款人姓名</label>
                                        <input name="reimburse_payeename" id="reimburse_payeename" value="{$dataVar.reimburse_payeename}" type="text" class="form-control" reg="[^ \f\n\r\t\v]" tip="收款人姓名不能为空" placeholder="请输入收款人姓名">
                                    </div>
                                    <div class="form-group col-md-3">
                                        <label for="reimburse_mobile">收款人手机</label>
                                        <input name="reimburse_mobile" id="reimburse_mobile" value="{$dataVar.reimburse_mobile}" type="text" class="form-control" tip="收款人手机不能为空" placeholder="请输入收款人手机">
                                    </div>
                                    <div class="form-group col-md-3">
                                        <label for="reimburse_applytime"><em>*</em>退款申请时间</label>
                                        <input name="reimburse_applytime" id="reimburse_applytime" value="{$dataVar.reimburse_applytime}" type="text" class="form-control" tip="退款申请时间不能为空" placeholder="请输入退款申请时间">
                                    </div>
                                    <div class="form-group col-md-3">
                                        <label for="reimburse_status"><em>*</em>请选择退款进度</label>
                                        <select name="reimburse_status" reg="[^ \f\n\r\t\v]" id="reimburse_status" class="form-control">
                                            <option value="">请选择退款进度</option>
                                            <option value="0" {if $dataVar.reimburse_status == '0'}selected{/if}>待处理</option>
                                            <option value="1" {if $dataVar.reimburse_status == '1'}selected{/if}>处理中</option>
                                            <option value="2" {if $dataVar.reimburse_status == '2'}selected{/if}>已退款</option>
                                            <option value="-1" {if $dataVar.reimburse_status == '-1'}selected{/if}>退款被退回</option>
                                        </select>
                                    </div>
                                    <div class="clear"></div>
                                    <div class="form-group col-md-3">
                                        <label for="reimburse_playtime">退款完成时间</label>
                                        <input name="reimburse_playtime" id="reimburse_playtime" value="{$dataVar.reimburse_playtime}" type="text" class="form-control" tip="退款完成时间不能为空" placeholder="请输入退款完成时间">
                                    </div>
                                    <div class="form-group col-md-9">
                                        <label for="reimburse_playnote">退款完成备注</label>
                                        <input name="reimburse_playnote" id="reimburse_playnote" value="{$dataVar.reimburse_playnote}" type="text" class="form-control" tip="退款完成备注不能为空" placeholder="请输入退款完成备注">
                                    </div>
                                    <div class="clear"></div>
                                    <div class="form-group col-md-12">
                                        <label for="reimburse_signaturepic">签字凭据</label>
                                        <input name="reimburse_signaturepic" id="reimburse_signaturepic" value="{$dataVar.reimburse_signaturepic}" type="text" class="form-control" tip="退款完成备注不能为空" placeholder="请输入签字凭据地址">
                                    </div>
                                    <div class="form-group col-md-12">
                                        <label for="reimburse_cardpic">证据凭据</label>
                                        <input name="reimburse_cardpic" id="reimburse_cardpic" value="{$dataVar.reimburse_cardpic}" type="text" class="form-control" tip="退款完成备注不能为空" placeholder="请输入证据凭据地址">
                                    </div>
                                    <div class="form-group col-md-12">
                                        <label for="reimburse_bandpic">银行卡</label>
                                        <input name="reimburse_bandpic" id="reimburse_bandpic" value="{$dataVar.reimburse_bandpic}" type="text" class="form-control" tip="退款完成备注不能为空" placeholder="请输入银行卡地址">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="upload-img col-md-6" style="min-height: 250px;">
                                    <div class="f16 tc default {if $dataVar.reimburse_signaturepic}none{/if}">
                                        <p class="f30"><span class="glyphicon glyphicon-picture"></span></p>
                                        <p>请先上传图片后预览</p>
                                    </div>
                                    <div class="img set {if !$dataVar.reimburse_signaturepic}none{/if}"><img src="{$dataVar.reimburse_signaturepic}"></div>
                                </div>
                                <div class="upload-img col-md-6" style="min-height: 250px;">
                                    <div class="f16 tc default {if $dataVar.reimburse_cardpic}none{/if}">
                                        <p class="f30"><span class="glyphicon glyphicon-picture"></span></p>
                                        <p>请先上传图片后预览</p>
                                    </div>
                                    <div class="img set {if !$dataVar.reimburse_cardpic}none{/if}"><img src="{$dataVar.reimburse_cardpic}"></div>
                                </div>
                                <div class="upload-img col-md-6" style="min-height: 250px;">
                                    <div class="f16 tc default {if $dataVar.reimburse_bandpic}none{/if}">
                                        <p class="f30"><span class="glyphicon glyphicon-picture"></span></p>
                                        <p>请先上传图片后预览</p>
                                    </div>
                                    <div class="img set {if !$dataVar.reimburse_bandpic}none{/if}"><img src="{$dataVar.reimburse_bandpic}"></div>
                                </div>
                            </div>
                            <div class="clear"></div>
                            <div class="form-group txtrg pr30">
                                <button type="button" class="btn btn-default mr40 bakFromurl">返回</button>
                                <button type="submit" class=" btn btn-primary btn-cons">提交保存</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{include file="jscom.htm"}
<script src="{$PluginsUrl}jquery-form/jquery.form.js" type="text/javascript"></script>
<script src="{$JsUrl}int.form.js" type="text/javascript"></script>
</body>
</html>
