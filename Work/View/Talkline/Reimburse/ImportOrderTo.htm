<!DOCTYPE HTML>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    {include file="key.htm"}
</head>

<body>

<div class="page-container row condensed">
    {include file="page-sidebar.htm"}
    {include file="footer.htm"}
    <div class="page-content">
        {include file="header.htm"}
        <div class="mx20 breadcrumb-w">
            <div class="breadcrumb fl">
                <a href="/{$u}">退款信息管理</a>
                <span>&gt; 导入退款信息</span>
            </div>
            <a href="javascript:" class="fr bakFromurl mt6"><i class="icon-back mr5"></i>返回</a>
            <div class="clear"></div>
        </div>
        <div class="content">
            <div id="container">
                <div class="row">
                    <div class="p20 f14">
                        {if $PlayInfo}
                        <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table tc table-hover table-bordered">
                            <thead>
                            <tr>
                                <th>校区名称</th>
                                <th>中文名</th>
                                <th>收款人姓名</th>
                                <th>导入成功</th>
                                <th>反馈信息</th>
                            </tr>
                            </thead>
                            <tbody>
                            {foreach from=$PlayInfo item=dataVar}
                            <tr>
                                <td>{$dataVar.school_name}</td>
                                <td>{$dataVar.reimburse_cnname}</td>
                                <td>{$dataVar.reimburse_payeename}</td>
                                <td>
                                    {if $dataVar.error == '1'}
                                    <a class="cp"><span class="glyphicon glyphicon-remove-circle f18 text-danger"></span></a>
                                    {else $dataVar.error == '0'}
                                    <a class="cp"><span class="glyphicon glyphicon-ok-circle f18 text-primary"></span></a>
                                    {/if}
                                </td>
                                <td>
                                    {$dataVar.errortip}
                                </td>
                            </tr>
                            {/foreach}
                            </tbody>
                        </table>
                        {/if}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{include file="jscom.htm"}
</body>
</html>
