<!DOCTYPE HTML>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    {include file="key.htm"}
</head>

<body>

<div class="page-container row condensed">
    {include file="page-sidebar.htm"}
    {include file="footer.htm"}
    <div class="page-content">
        {include file="header.htm"}
        <div class="mx20 breadcrumb-w">
            <div class="breadcrumb fl">
                <span>退款进度管理</span>
            </div>
            <a href="javascript:" class="fr bakFromurl mt6"><i class="icon-back mr5"></i>返回</a>
            <div class="clear"></div>
        </div>
        <div class="content">
            <div id="container">
                <div class="row">
                    <div class="bg-f">
                        <div class="p20 f14">
                            <div class="form-group">
                                <form action="/{$u}/Home" method="get" accept-charset="utf-8">
                                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                        <tr>
                                            <td class="pr10">
                                                <select name="reimburse_status" class="form-control dropdown">
                                                    <option value="">请选择退款状态</option>
                                                    <option value="0" {if $datatype.reimburse_status == '0'}selected{/if}>待处理</option>
                                                    <option value="1" {if $datatype.reimburse_status == '1'}selected{/if}>处理中</option>
                                                    <option value="2" {if $datatype.reimburse_status == '2'}selected{/if}>已完成</option>
                                                    <option value="-1" {if $datatype.reimburse_status == '-1'}selected{/if}>已退回</option>
                                                </select>
                                            </td>
                                            <td width="25%">
                                                <input name="keyword" class="form-control input-sm" placeholder="请输入学员姓名、收款人姓名、学校名称" value="{$datatype['keyword']}" type="text">
                                            </td>
                                            <td width="65%">
                                                <button type="submit" class="btn btn-default ml10">搜索</button>
                                                <a href="/{$u}/ImportOrder" class="btn fr btn-success dropdown-toggle btn-demo-space">导入退款信息</a>
                                                <a type="button" class="btn btn-danger ml20" href="/{$u}/Add">新增退款信息</a>
                                            </td>
                                        </tr>
                                    </table>
                                </form>
                            </div>
                            <form role="form" action="/{$u}?c=BatchUser" method="post" enctype="application/x-www-form-urlencoded" class="AjaxForm">
                                <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table tc table-hover table-bordered">
                                    <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>校区名称</th>
                                        <th>学员姓名</th>
                                        <th>退款金额</th>
                                        <th>收款人</th>
                                        <th>银行卡号</th>
                                        <th>银行信息</th>
                                        <th>处理进度</th>
                                        <th>申请时间</th>
                                        <th>操作</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    {if $dataList}
                                    {foreach from=$dataList item=dataVar}
                                    <tr id="list-{$dataVar.reimburse_id}">
                                        <td>{$dataVar.reimburse_id}</td>
                                        <td>{$dataVar.school_name}</td>
                                        <td>{$dataVar.reimburse_cnname} / {$dataVar.reimburse_enname}</td>
                                        <td>{$dataVar.reimburse_okprice}</td>
                                        <td>{$dataVar.reimburse_payeename}</td>
                                        <td>{$dataVar.reimburse_bankcode}</td>
                                        <td>{$dataVar.reimburse_bankname}{$dataVar.reimburse_banknode}</td>
                                        <td>{if $dataVar.reimburse_status == '0'}待处理
                                            {elseif $dataVar.reimburse_status == '1'}处理中
                                            {elseif $dataVar.reimburse_status == '2'}已完成
                                            {elseif $dataVar.reimburse_status == '-1'}已退回
                                        {/if}</td>
                                        <td>{$dataVar.reimburse_applytime}</td>
                                        <td>
                                            <a href="/{$u}/Edit?reimburse_id={$dataVar.reimburse_id}" class="btn btn-primary btn-sm">编辑</a>
                                            <a href="javascript:;" data-element="list-{$dataVar.reimburse_id}" data-url="/{$u}?c=Del&reimburse_id={$dataVar.reimburse_id}" data-tipname="消息提示" data-tiptitle="请确定删除此退款信息？" class="btn btn-default btn-sm btn-confirm-action">删除</a>
                                        </td>
                                    </tr>
                                    {/foreach}
                                    {/if}
                                    </tbody>
                                </table>
                            </form>
                            <div class="pagemenu">{$pagelist}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{include file="jscom.htm"}
<script src="{$PluginsUrl}jquery-form/jquery.form.js" type="text/javascript"></script>
<script src="{$JsUrl}int.form.js" type="text/javascript"></script>
</body>
</html>
