<!DOCTYPE HTML>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    {include file="key.htm"}
</head>

<body>

<div class="page-container row condensed">
    {include file="page-sidebar.htm"}
    {include file="footer.htm"}
    <div class="page-content">
        {include file="header.htm"}
        <div class="mx20 breadcrumb-w">
            <div class="breadcrumb fl">
                <a href="/Channer">用户信息</a>
                <span>&gt; 自定义信息</span>
            </div>
            <a href="javascript:" class="fr bakFromurl mt6"><i class="icon-back mr5"></i>返回</a>
            <div class="clear"></div>
        </div>
        <div class="content">
            <div id="container">
                <div class="row">
                    <div class="bg-f">
                        <div class="bg-f row pb20">
                            <form action="/{$u}?c={$act}" role="form" method="post" enctype="application/x-www-form-urlencoded" class="AjaxForm form-horizontal">
                                <input name="user_id" type="hidden" value="{$dataVar.user_id}">
                                <div class="col-md-8" id="Form-Box-Operating">
                                    <div class="py20 f14">
                                        <div class="alert alert-warning alert-dismissible" role="alert">
                                            <button type="button" class="close" data-dismiss="alert"><span aria-hidden="true">×</span><span class="sr-only">Close</span></button>
                                            <span class="glyphicon glyphicon-exclamation-sign f16 vam mr6"></span> 注：仅分组权限指只能管理所在分组下的方案或订单</div>
                                        <div class="form-group">
                                            <div class="row mb20">
                                                <label class="col-sm-2 control-label">企业体检管理</label>
                                                <div class="col-sm-8">
                                                    <label class="control-label col-sm-4 no-padding-right">
                                                        <input name="firmexamine" value="1" {if $dataVar.Userlimit.firmexamine == '1'}checked{/if} type="checkbox" class="ace">
                                                        <span class="lbl"></span>
                                                        管理权限
                                                    </label>
                                                    <label class="control-label col-sm-4 no-padding-right">
                                                        <input name="firmgroup" value="1" {if $dataVar.Userlimit.firmgroup == '1'}checked{/if} type="checkbox" class="ace">
                                                        <span class="lbl"></span>
                                                        仅分组权限
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="row mb20">
                                                <label class="col-sm-2 control-label">个人体检管理</label>
                                                <div class="col-sm-8">
                                                    <label class="control-label col-sm-4 no-padding-right">
                                                        <input name="shareexamine" value="1" {if $dataVar.Userlimit.shareexamine == '1'}checked{/if} type="checkbox" class="ace">
                                                        <span class="lbl"></span>
                                                        管理权限
                                                    </label>
                                                    <label class="control-label col-sm-4 no-padding-right">
                                                        <input name="sharegroup" value="1" {if $dataVar.Userlimit.sharegroup == '1'}checked{/if} type="checkbox" class="ace">
                                                        <span class="lbl"></span>
                                                        仅分组权限
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="row mb20">
                                                <label class="col-sm-2 control-label">第三方体检管理</label>
                                                <div class="col-sm-8">
                                                    <label class="control-label col-sm-4 no-padding-right">
                                                        <input name="threeexamine" value="1" {if $dataVar.Userlimit.threeexamine == '1'}checked{/if} type="checkbox" class="ace">
                                                        <span class="lbl"></span>
                                                        管理权限
                                                    </label>
                                                    <label class="control-label col-sm-4 no-padding-right">
                                                        <input name="threegroup" value="1" {if $dataVar.Userlimit.threegroup == '1'}checked{/if} type="checkbox" class="ace">
                                                        <span class="lbl"></span>
                                                        仅分组权限
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="row mb20">
                                                <label class="col-sm-2 control-label">核磁体检管理</label>
                                                <div class="col-sm-8">
                                                    <label class="control-label col-sm-4 no-padding-right">
                                                        <input name="nmrexamine" value="1" {if $dataVar.Userlimit.nmrexamine == '1'}checked{/if} type="checkbox" class="ace">
                                                        <span class="lbl"></span>
                                                        管理权限
                                                    </label>
                                                    <label class="control-label col-sm-4 no-padding-right">
                                                        <input name="nmrgroup" value="1" {if $dataVar.Userlimit.nmrgroup == '1'}checked{/if} type="checkbox" class="ace">
                                                        <span class="lbl"></span>
                                                        仅分组权限
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="row mb20">
                                                <label class="col-sm-2 control-label">预约订单管理</label>
                                                <div class="col-sm-8">
                                                    <label class="control-label col-sm-4 no-padding-right">
                                                        <input name="orderlimit" value="1" {if $dataVar.Userlimit.orderlimit == '1'}checked{/if} type="checkbox" class="ace">
                                                        <span class="lbl"></span>
                                                        管理权限
                                                    </label>
                                                    <label class="control-label col-sm-4 no-padding-right">
                                                        <input name="ordergroup" value="1" {if $dataVar.Userlimit.ordergroup == '1'}checked{/if} type="checkbox" class="ace">
                                                        <span class="lbl"></span>
                                                        仅分组权限
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="row mb20">
                                                <label class="col-sm-2 control-label">微商城</label>
                                                <div class="col-sm-8">
                                                    <label class="control-label col-sm-3 no-padding-right">
                                                        <input name="wshopedit" value="1" {if $dataVar.Userlimit.wshopedit == '1'}checked{/if} type="checkbox" class="ace">
                                                        <span class="lbl"></span>
                                                        基本设置
                                                    </label>
                                                    <label class="control-label col-sm-3 no-padding-right">
                                                        <input name="wshoporder" value="1" {if $dataVar.Userlimit.wshoporder == '1'}checked{/if} type="checkbox" class="ace">
                                                        <span class="lbl"></span>
                                                        微商城订单
                                                    </label>
                                                    <label class="control-label col-sm-3 no-padding-right">
                                                        <input name="wshopmarketer" value="1" {if $dataVar.Userlimit.wshopmarketer == '1'}checked{/if} type="checkbox" class="ace">
                                                        <span class="lbl"></span>
                                                        营销员管理
                                                    </label>
                                                    <label class="control-label col-sm-3 no-padding-right">
                                                        <input name="wshopperformance" value="1" {if $dataVar.Userlimit.wshopperformance == '1'}checked{/if} type="checkbox" class="ace">
                                                        <span class="lbl"></span>
                                                        销售统计
                                                    </label>
                                                    <label class="control-label col-sm-3 no-padding-right">
                                                        <input name="norders" value="1" {if $dataVar.Userlimit.norders == '1'}checked{/if} type="checkbox" class="ace">
                                                        <span class="lbl"></span>
                                                        商品订单-新
                                                    </label>
                                                    <!-- <label class="control-label col-sm-3 no-padding-right">
                                                        <input name="nstatistics" value="1" {if $dataVar.Userlimit.nstatistics == '1'}checked{/if} type="checkbox" class="ace">
                                                        <span class="lbl"></span>
                                                        商品销售统计
                                                    </label>
                                                    <label class="control-label col-sm-3 no-padding-right">
                                                        <input name="outstatistics" value="1" {if $dataVar.Userlimit.outstatistics == '1'}checked{/if} type="checkbox" class="ace">
                                                        <span class="lbl"></span>
                                                        外部销售统计
                                                    </label> -->
                                                </div>
                                            </div>
                                            <div class="row mb20">
                                                <label class="col-sm-2 control-label">用户管理</label>
                                                <div class="col-sm-8">
                                                    <label class="control-label col-sm-4 no-padding-right">
                                                        <input name="userlimit" value="1" {if $dataVar.Userlimit.userlimit == '1'}checked{/if} type="checkbox" class="ace">
                                                        <span class="lbl"></span>
                                                        管理权限
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="row mb20">
                                                <label class="col-sm-2 control-label">分组管理</label>
                                                <div class="col-sm-8">
                                                    <label class="control-label col-sm-4 no-padding-right">
                                                        <input name="grouplimit" value="1" {if $dataVar.Userlimit.grouplimit == '1'}checked{/if} type="checkbox" class="ace">
                                                        <span class="lbl"></span>
                                                        管理权限
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="row mb20">
                                                <label class="col-sm-2 control-label">体检卡管理</label>
                                                <div class="col-sm-8">
                                                    <label class="control-label col-sm-4 no-padding-right">
                                                        <input name="medicallimit" value="1" {if $dataVar.Userlimit.medicallimit == '1'}checked{/if} type="checkbox" class="ace">
                                                        <span class="lbl"></span>
                                                        管理权限
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="row mb20">
                                                <label class="col-sm-2 control-label">操作日志</label>
                                                <div class="col-sm-8">
                                                    <label class="control-label col-sm-4 no-padding-right">
                                                        <input name="userloglimit" value="1" {if $dataVar.Userlimit.userloglimit == '1'}checked{/if} type="checkbox" class="ace">
                                                        <span class="lbl"></span>
                                                        管理权限
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-sm-4 col-sm-offset-2">
                                            <button type="submit" class=" btn btn-primary btn-cons">提交保存</button>
                                            <button type="button" class="btn btn-default ml10 bakFromurl">取消返回</button></span>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{include file="jscom.htm"}
<script src="{$PluginsUrl}jquery-form/jquery.form.js" type="text/javascript"></script>
<script src="{$JsUrl}int.form.js" type="text/javascript"></script>
</body>
</html>
