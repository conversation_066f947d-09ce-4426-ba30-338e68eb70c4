<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>个人页面</title>
     <link rel="stylesheet" href="{$CssUrl}main.css">
    <!--<link rel="stylesheet/less" type="text/css" href="./css/main.less">-->
</head>

<body>
<div class="my-box">
    <div class="quit">
      <!-- <a href="/{$u}/outLogin">
        <em></em>
          <p> 退出</p>
      </a> -->
      <a href="javascript:;" class="quitBox">
        <em></em>
          <p> 退出</p>
      </a>
    </div>
    
    <form action="" class="form">
        <div class="my-picture">
            <div class="img-upload">
                <!--/Work/View/Tephone/images/man.png-->
                {if $istaffer.staffer_img}
                <img src="{$istaffer.staffer_img}" alt="" style="">
                {else}
                 {if $istaffer.staffer_sex =="女"}
                    <img src="/Work/View/Tephone/images/woman.png" alt="" style="">
                 {else}
                    <img src="/Work/View/Tephone/images/man.png" alt="" style="">
                 {/if}
                {/if}
                <!--<input type="file" class="img-file">-->
                <!--<em class="edit"></em>-->
            </div>
        </div>
        <div class="my-info">
            <div class="form-group">
                <label for="">手机号码</label>
                <input type="text" class="ipt" value="{$istaffer.staffer_mobile}" readonly />
            </div>
            <div class="form-group">
                <label for="">编号</label>
                <input type="text" class="ipt" value="{$istaffer.staffer_branch}" readonly />
            </div>
            <div class="form-group">
                <label for="">中文名称</label>
                <input type="text" class="ipt" value="{$istaffer.staffer_cnname}" disabled />
            </div>
            <div class="form-group">
                <label for="">英文名称</label>
                <input type="text" class="ipt" value="{$istaffer.staffer_enname}" disabled />
            </div>
            <!--<div class="form-group mb20">-->
                <!--<a href="#" class="modify">-->
                    <!--修改密码-->
                    <!--<em class="right-arrow"></em>-->
                <!--</a>-->
            <!--</div>-->
            <div class="form-group">
                <a href="/Index/Post" class="modify">
                    选择职务
                    <em class="right-arrow"></em>
                </a>
            </div>
        </div>
        <div class="action">
            <button type="submit" class="btn">
                确认
            </button>
        </div>
    </form>
    <!-- 底部 -->
    {include file='footer.htm'}
</div>

<script data-main="{$JsUrl}pages/my.js?keytime={$versiontiem}" src="{$JsUrl}/lib/require.js"></script>
</body>

</html>