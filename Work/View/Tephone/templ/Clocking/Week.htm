<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>考勤状况</title>
     <link rel="stylesheet" href="{$CssUrl}main.css">
     <link rel="stylesheet" href="{$CssUrl}mui.min.css">
    <!--<link rel="stylesheet/less" type="text/css" href="./css/main.less">-->
    <style>
      .mui-scroll-wrapper{
        z-index: 50 !important;
        overflow: auto;
        top: 270px;
        left: 10px;
        right: 10px;
        width: calc(100% - 20px);
      }
    </style>
</head>

<body>
<div class="attend-box">
    <div class="time-change">
        <span class="arrow arrow-left"></span>
        <span class="detail-time">
        <em class="start" id="start"></em>
         —
        <em class="end" id="end"></em>
      </span>
        <span class="arrow arrow-right"></span>
    </div>
    <div class="time-tabs">
        <a href="/{$u}">今日</a>
        <a href="/{$u}/Week" class="cur">按周</a>
        <!-- <a href="/{$u}/Moth">按月</a> -->
    </div>
</div>
<div class="attend-rate-add"  id="popMto">

</div>
<!-- 上课列表 -->
<div class="courseList">
  <div 
      id="pullrefresh" 
      class="mui-content mui-scroll-wrapper"
      style="top:270px;"
    >
        <div class="mui-scroll">
          <div class="mui-table-view mui-table-view-chevron" id="popMto1">
            
          </div>
        </div>
    </div>
</div>

<!-- 返回首页按钮 -->
<div class="gobackIndex">
  <a href="/Index">
    <!-- <em></em>
    首页 -->
    <img src="{$ImgUrl}goIndex.png" alt="">
  </a>
</div>
{literal}
<script id="allNumberTmpl" type="text/x-dot-template">
  <ul class="attend-rate" id='allNumber'>
      <li>
          <span class="num">{{=it.clocking.clock_percent}}</span>
          <span class="word">出勤率</span>
      </li>
      <li>
          <span class="num">{{=it.clocking.class_num}}</span>
          <span class="word">班级数</span>
      </li>
      <li>
          <span class="num">{{=it.clocking.hour_inarrivenums}}</span>
          <span class="word">实到人数</span>
      </li>
      <li>
          <span class="num">{{=it.clocking.hourstudy_num}}</span>
          <span class="word">应到人数</span>
      </li>
  </ul>
</script>
{/literal}

{literal}
<script id="familyStuTmpl" type="text/x-dot-template">
  <div id='familyStu'>
      <div class="title">
          <span>上课列表</span>
      </div>
      <div class="subjectList">
          {{~it.list: item}}
          <a class="list" href="/Clocking/rollCallClass?hour_id={{=item.hour_id}}&class_id={{=item.class_id}}">
            {{? item.hour_ischecking == 1}}
            <div class="flag flag1">{{=item.hour_checkname}}</div>
            {{?? item.hour_ischecking == 0}}
            <div class="flag flag2">{{=item.hour_checkname}}</div>
            {{?? item.hour_ischecking == -1}}
            <div class="flag flag3">{{=item.hour_checkname}}</div>
            {{?}}
              
              <div class="classInfo">
                  <h3>{{=item.class_cnname}}</h3>
                  <h4>{{=item.classroom_cnname}}</h4>
                  <div class="address">
                      <span class="area"><em></em>{{=item.school_cnname}}</span>
                      <span class="bell"><em></em>{{=item.hour_starttime}} - {{=item.hour_endtime}}</span>
                  </div>
              </div>
              <div class="rating">{{=item.hourstudy_num}}/{{=item.hour_inarrivenums}}</div>
          </a>
          {{~}}
      </div>
  </div>  
</script>
{/literal}
<!-- 底部 -->

<script data-main="{$JsUrl}pages/week.js?keytime={$versiontiem}" src="{$JsUrl}lib/require.js"></script>
<script src="{$JsUrl}lib/jquery.js"></script>
<script src="{$JsUrl}lib/mui.min.js"></script>
</body>

</html>