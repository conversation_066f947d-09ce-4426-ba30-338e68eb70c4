<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>考勤状况-上课人数</title>
     <link rel="stylesheet" href="{$CssUrl}/main.css">
    <!--<link rel="stylesheet/less" type="text/css" href="./css/main.less">-->
</head>

<body>
<div class="attend-box rollcall-box">
    <div class="time-change">
        <h3 class="tit">{$dataList.class.class_cnname}</h3>
    </div>
</div>
<div class="attend-rate-box">
    <ul class="attend-rate">
        <li>
            <span class="num">{$dataList.is_checkin.is_checkin}</span>
            <span class="word">实到</span>
        </li>
        <li>
            <span class="num">{$dataList.is_checkin.all_checkin}</span>
            <span class="word">应到</span>
        </li>
        <li>
            <span class="num">{$dataList.is_checkin.student_num}</span>
            <span class="word">计费</span>
        </li>
        <li>
            <span class="num">{$dataList.is_checkin.student_austu}</span>
            <span class="word">试听</span>
        </li>
    </ul>
    <div class="period-box">
        <em></em>{$dataList.class.hour_day} {$dataList.class.hour_starttime} — {$dataList.class.hour_endtime}
    </div>
</div>
<div class="studentInfo-box">
    {if $dataList.list}
    {foreach from=$dataList.list item=dataVar}
    <div class="item">
        <div class="title">
            {$dataVar.student_cnname}
            <em></em>
        </div>
        <div class="record-box">
            <div class="phone">
                <em></em>
                {$dataVar.family_mobile}
            </div>
            <div class="type-box">
                <span class="type type1"><em></em>{if $dataVar.hour_isfree ==0}是{else}否{/if}</span>
                <span class="type type2"><em></em>{$dataVar.student_type}</span>
                <span class="type type3"><em></em>{if $dataVar.hourstudy_checkin}出勤{else}缺勤{/if}</span>
            </div>
            <!-- 如果没有缺勤就不显示下面的 -->
            <div class="reason">
                <span>{$dataVar.clockinginlog_note}</span>
            </div>
        </div>
    </div>
    {/foreach}
    {/if}
</div>
<!-- 底部 -->
{include file='footer.htm'}

<script data-main="{$JsUrl}pages/courseNumber.js?keytime={$versiontiem}" src="{$JsUrl}lib/require.js"></script>
</body>

</html>