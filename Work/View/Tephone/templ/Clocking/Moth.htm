<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>考勤状况</title>
     <link rel="stylesheet" href="{$CssUrl}main.css">
    <link rel="stylesheet" type="text/css" href="{$CssUrl}bootstrap-grid.min.css">
    <link rel="stylesheet" type="text/css" href="{$CssUrl}htmleaf-demo.css">
    <link rel="stylesheet" type="text/css" href="{$CssUrl}calendar.css">
    <link rel="stylesheet" href="{$CssUrl}loading.css">
    <link rel="stylesheet" href="{$CssUrl}dropload.css">
    <script src="{$JsUrl}lib/jquery.js"></script>
    <script src="{$JsUrl}lib/moment.min.js"></script>
    <script src="{$JsUrl}lib/es6.js"></script>

    <script src="{$JsUrl}lib/calendar.js"></script>

</head>

<body>
<div class="attend-box">
    <!-- <div class="time-change">
      <span class="arrow arrow-left"></span>
      <span class="detail-time">
        <em class="time">2018/11/12 </em>
        <em class="week">周二</em>
      </span>
      <span class="arrow arrow-right"></span>
    </div> -->
    <div class="time-tabs">
        <a href="/{$u}">今日</a>
        <a href="/{$u}/Week">按周</a>
        <!-- <a href="/{$u}/Moth"  class="cur">按月</a> -->
    </div>
</div>
<div class="attend-rate-add"  id="popMto">

</div>
<!-- <ul class="attend-rate">
    <li>
        <span class="num">100%</span>
        <span class="word">出勤率</span>
    </li>
    <li>
        <span class="num">4</span>
        <span class="word">班级数</span>
    </li>
    <li>
        <span class="num">20</span>
        <span class="word">实到人数</span>
    </li>
    <li>
        <span class="num">20</span>
        <span class="word">应到人数</span>
    </li>
</ul> -->
<div class="month-calendar">
    <!-- <input type="text" class="none" placeholder="Date picker" id="singleDateRange"> -->
    <input type="text" class="none" id="dt" placeholder="trigger calendar">
    <div id="dd"></div>
</div>

<!-- 上课列表 -->
<!-- <div class="courseList">
    <div class="title">
        <span>2018/11/12&nbsp;周二</span>
        <span>上课列表(4)</span>
    </div>
    <div class="subjectList">
        <a class="list" href="./courseNumber.html">
            <div class="flag flag1">已上课</div>
            <div class="classInfo">
                <h3>2018小儿童美语新生代1班</h3>
                <h4>110教室</h4>
                <div class="address">
                    <span class="area"><em></em>上海新村路校区</span>
                    <span class="bell"><em></em>08:30-11:00</span>
                </div>
            </div>
            <div class="rating">12/16</div>
        </a>
        <a class="list" href="./courseNumber.html">
            <div class="flag flag2">未上课</div>
            <div class="classInfo">
                <h3>2018小儿童美语新生代1班</h3>
                <h4>110教室</h4>
                <div class="address">
                    <span class="area"><em></em>上海新村路校区</span>
                    <span class="bell"><em></em>08:30-11:00</span>
                </div>
            </div>
            <div class="rating">12/16</div>
        </a>
        <a class="list" href="./courseNumber.html">
            <div class="flag flag3">已取消</div>
            <div class="classInfo">
                <h3>2018小儿童美语新生代1班</h3>
                <h4>110教室</h4>
                <div class="address">
                    <span class="area"><em></em>上海新村路校区</span>
                    <span class="bell"><em></em>08:30-11:00</span>
                </div>
            </div>
            <div class="rating">12/16</div>
        </a>
    </div>
</div> -->
<!-- 上课列表 -->
<div class="courseList content" id="popMto1">
    
</div>
<!-- 返回首页按钮 -->
<div class="gobackIndex">
  <a href="/Index">
    <!-- <em></em>
    首页 -->
    <img src="{$ImgUrl}goIndex.png" alt="">
  </a>
</div>
{literal}
<script id="allNumberTmpl" type="text/x-dot-template">
  <ul class="attend-rate" id='allNumber'>
      <li>
          <span class="num">{{=it.clocking.clock_percent}}</span>
          <span class="word">出勤率</span>
      </li>
      <li>
          <span class="num">{{=it.clocking.class_num}}</span>
          <span class="word">班级数</span>
      </li>
      <li>
          <span class="num">{{=it.clocking.hour_inarrivenums}}</span>
          <span class="word">实到人数</span>
      </li>
      <li>
          <span class="num">{{=it.clocking.hourstudy_num}}</span>
          <span class="word">应到人数</span>
      </li>
  </ul>
</script>
{/literal}

{literal}
<script id="familyStuTmpl" type="text/x-dot-template">
  <div id='familyStu'>
      <div class="title">
          <span>上课列表</span>
      </div>
      <div class="subjectList">
          {{~it.list: item}}
          <a class="list" href="/Clocking/rollCallClass?hour_id={{=item.hour_id}}">
            {{? item.hour_ischecking == 1}}
            <div class="flag flag1">{{=item.hour_checkname}}</div>
            {{?? item.hour_ischecking == 0}}
            <div class="flag flag2">{{=item.hour_checkname}}</div>
            {{?? item.hour_ischecking == -1}}
            <div class="flag flag3">{{=item.hour_checkname}}</div>
            {{?}}
              
              <div class="classInfo">
                  <h3>{{=item.class_cnname}}</h3>
                  <h4>{{=item.classroom_cnname}}</h4>
                  <div class="address">
                      <span class="area"><em></em>{{=item.school_cnname}}</span>
                      <span class="bell"><em></em>{{=item.hour_starttime}} - {{=item.hour_endtime}}</span>
                  </div>
              </div>
              <div class="rating">{{=item.hourstudy_num}}/{{=item.hour_inarrivenums}}</div>
          </a>
          {{~}}
      </div>
  </div>  
</script>
{/literal}
<script>
    //       	'use strict';

    // $(function () {
    //     'use strict';

    //     $('#singleDateRange').DatePicker({
    //         startDate: moment()
    //     });
    // });
    $('#dd').calendar({
        trigger: '#dt',
        zIndex: 999,
        format: 'yyyy-mm-dd',
        onSelected: function (view, date, data) {
            console.log('event: onSelected')
        },
        onClose: function (view, date, data) {
            console.log('event: onClose')
            console.log('view:' + view)
            console.log('date:' + date)
            console.log('data:' + (data || 'None'));
        }
    });
</script>

<script data-main="{$JsUrl}pages/month.js?keytime={$versiontiem}" src="{$JsUrl}lib/require.js"></script>
<script src="{$JsUrl}/lib/jquery.js"></script>
<script src="{$JsUrl}/lib/dropload.min.js"></script>

</body>

</html>