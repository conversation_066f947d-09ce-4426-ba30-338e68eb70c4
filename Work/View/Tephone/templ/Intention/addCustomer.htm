<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>添加客户</title>
     <link rel="stylesheet" href="{$CssUrl}main.css">
     <script type="text/javascript" src="{$JsUrl}lib/jquery.js"></script>
    <link rel="stylesheet" href="{$CssUrl}swiper.min.css">
    <!--<link rel="stylesheet/less" type="text/css" href="./css/main.less">-->
    <script src="{$JsUrl}lib/swiper.min.js"></script>
    <script src="{$JsUrl}lib/rolldate.min.js"></script>
</head>

<body>
<!-- 添加跟进 -->
<div class="addRecord-box addRecord-box1">
    <form action="">
        <div class="basicInfo">
            基本信息
        </div>
        <div class="choose-ipt mb20">
            <p class="type-title start">姓名</p>
            <input type="text" class="ipt" name="name" id="name" placeholder="请输入姓名">
        </div>
        <div class="choose-ipt mb20">
            <p class="type-title start">手机号</p>
            <input type="text" class="ipt" name="phone" id="phone" placeholder="请输入手机号码" maxlength="11" >
        </div>
        <div class="choose-ipt mb20">
            <p class="type-title start">性别</p>
            <input type="text" class="ipt" id="scrollProp1"  name=""  placeholder="请选择">
        </div>
        <div class="follow-date choose-ipt mb20">
            <p class="type-title">出生日期</p>
            <input
                    class="date ipt"
                    type="text"
                    id="date-group1-1"
                    placeholder="请选择"
            >
        </div>
        <div class="basicInfo">
            家庭联系信息
            <em class="addInfo"></em>
        </div>
        <div class="contact-box">
          <div class="contact" data-order="0" id="add-main">
              <div class="choose-ipt mb20">
                  <p class="type-title start">亲属关系</p>
                  <input type="text" class="ipt specileId" id="order-0" data-indexId="0" name=""  placeholder="请选择" readonly>
              </div>
              <div class="choose-ipt mb20">
                  <p class="type-title start">亲属姓名</p>
                  <input type="text" class="ipt relativeName" name="" id="" placeholder="请填写">
              </div>
              <div class="choose-ipt mb20">
                  <p class="type-title start">请输入联系电话</p>
                  <input type="text" class="ipt addphone" name="" id="" placeholder="请填写" maxlength="11" minlength="11">
              </div>
              <div class="main-contact">
                  <div class="exchange">
                      <input type="radio" class="radio" name="mainPerson" checked>
                      <label for="">设置为主要联系人</label>
                  </div>
                  <div class="delete"></div>
              </div>
          </div>
          <!-- <div class="contact" data-order="1">
              <div class="choose-ipt mb20">
                  <p class="type-title">亲属关系</p>
                  <input type="text" class="ipt order" id="order-1"  name=""  placeholder="请选择" readonly>
              </div>
              <div class="choose-ipt mb20">
                  <p class="type-title">亲属姓名</p>
                  <input type="text" class="ipt" name="" id="" placeholder="请填写">
              </div>
              <div class="choose-ipt mb20">
                  <p class="type-title">请输入联系电话</p>
                  <input type="text" class="ipt order" name="" id="" placeholder="请填写">
              </div>
              <div class="main-contact">
                  <div class="exchange">
                      <input type="radio" class="radio" name="mainPerson" checked>
                      <label for="">设置为主要联系人</label>
                  </div>
                  <div class="delete"></div>
              </div>
          </div> -->
        </div>
        
        <div class="basicInfo">
            招生信息
        </div>
        <div class="choose-ipt mb20">
            <p class="type-title start">渠道类型</p>
            <input type="text" class="ipt" id="scrollProp2"  name=""  placeholder="请选择" readonly>
        </div>
        <div class="choose-ipt mb20">
          <p class="type-title">渠道明细</p>
          <input type="text" class="ipt" id="scrollProp2-1" name="" placeholder="请选择" readonly>
        </div>
        <div class="loveLevel mb20">
            <p class="type-title">意向星级</p>
            <div class="Love-box">
                <span class="love"></span>
                <span class="love"></span>
                <span class="love"></span>
                <span class="love"></span>
                <span class="love"></span>
            </div>
        </div>
        <div class="choose-ipt mb20">
            <p class="type-title">请输入介绍人</p>
            <input type="text" class="ipt" id="scrollProp3"  name=""  placeholder="请输入介绍人">
        </div>
        <div class="basicInfo">
            意向课程
        </div>
        <div class="choose-ipt mb20">
            <input type="text" class="ipt" id="scrollProp4"  name=""  placeholder="请选择" readonly>
        </div>
        <div class="basicInfo">
            备注信息
        </div>
        <div class="area-box mb20">
            <textarea class="area" id="area" placeholder="请输入备注信息" name=""  ></textarea>
        </div>
    </form>
    <div class="review-footer-box">
        <div class="footer">
            <button type="submit" class="btn next" data-student="127631" data-class="20010" data-hour="412"  data-url="/Intention/addIntention">
                保存
            </button>
            <a href="javascript:;" class="goback">
                取消并返回
            </a>
        </div>
    </div>
</div>
<!-- swiper1 性别选择-->
<a class="swiper-box-prop swiper-box-prop1 none">
    <div class="scroll-choose">
        <div class="place">请选择</div>
        <div class="confirm">确认</div>
        <div class="swiper-box">
            <span class="line"></span>
            <div class="swiper-container">
                <div class="swiper-wrapper">
                    <div class="swiper-slide siwper-item " data-ele="0">男</div>
                    <div class="swiper-slide siwper-item " data-ele="1">女</div>
                </div>
            </div>
        </div>
    </div>
</a>
<!-- swiper 亲属关系选折-->
<a class="swiper-box-prop order-prop none" data-propId="0">
    <div class="scroll-choose">
        <div class="place">请选择</div>
        <div class="confirm">确认</div>
        <div class="swiper-box">
            <span class="line"></span>
            <div class="swiper-container">
                <div class="swiper-wrapper">
                     {if $familylist}
                      {foreach from=$familylist item=dataVar}
                    <div class="swiper-slide siwper-item " data-ele="0" data-code="{$dataVar.familyrelation_code}">{$dataVar.familyrelation_name}</div>
                    {/foreach}
                    {/if}
                </div>
            </div>
        </div>
    </div>
</a>
<!-- swiper2 招生来源-->
<a class="swiper-box-prop swiper-box-prop2 none">
    <div class="scroll-choose">
        <div class="place">请选择</div>
        <div class="confirm">确认</div>
        <div class="swiper-box">
            <span class="line"></span>
            <div class="swiper-container">
                <div class="swiper-wrapper">
                    {if $mediaList}
                    {foreach from=$mediaList item=dataVar}
                    <div class="swiper-slide siwper-item " data-ele="0"data-frommedia_id="{$dataVar.frommedia_id}">{$dataVar.frommedia_name}</div>
                    {/foreach}
                   {/if}
                </div>
            </div>
        </div>
    </div>
</a>
<!-- swiper2-1 渠道明细-->
<a class="swiper-box-prop swiper-box-prop2-1 none" id="popMto">
  <!-- <div class="scroll-choose">
    <div class="place">请选择</div>
    <div class="confirm">确认</div>
    <div class="swiper-box">
      <span class="line"></span>
      <div class="swiper-container">
        <div class="swiper-wrapper">
          <div class="swiper-slide siwper-item " data-ele="0" >1111</div>
          <div class="swiper-slide siwper-item " data-ele="0">2222</div>
          <div class="swiper-slide siwper-item " data-ele="0">3333</div>
        </div>
      </div>
    </div>
  </div> -->
</a>
{literal}
<script id="allNumberTmpl" type="text/x-dot-template">
  <div id='allNumber'>
      <div class="scroll-choose">
          <div class="place">请选择</div>
          <div class="confirm">确认</div>
          <div class="swiper-box">
              <span class="line"></span>
              <div class="swiper-container">
                  <div class="swiper-wrapper">
                      {{~it.list: item}}
                        <div class="swiper-slide siwper-item"
                          data-channelid="{{=item.channel_id}}"
                        >
                          {{=item.channel_name}}
                        </div>
                      {{~}}
                  </div>
              </div>
          </div>
      </div>
    
  </div>
</script>
{/literal}
<!-- swiper3 介绍人-->
<a class="swiper-box-prop swiper-box-prop3 none">
    <div class="scroll-choose">
        <div class="place">请选择</div>
        <div class="confirm">确认</div>
        <div class="swiper-box">
            <span class="line"></span>
            <div class="swiper-container">
                <div class="swiper-wrapper">
                    <div class="swiper-slide siwper-item " data-ele="0">爸爸</div>
                    <div class="swiper-slide siwper-item " data-ele="1">妈妈</div>
                </div>
            </div>
        </div>
    </div>
</a>
<!-- swiper4 意向课程-->
<a class="swiper-box-prop swiper-box-prop4 none">
    <div class="scroll-choose">
        <div class="place">请选择</div>
        <div class="confirm">确认</div>
        <div class="swiper-box">
            <span class="line"></span>
            <div class="swiper-container">
                <div class="swiper-wrapper">
                    {if $courseList}
                    {foreach from=$courseList item=dataVar }
                    <div class="swiper-slide siwper-item " data-ele="0"  data-coursecatid="{$dataVar.coursecat_id}"> {$dataVar.coursecat_cnname}({$dataVar.coursecat_branch})</div>
                    {/foreach}
                    {/if}
                </div>
            </div>
        </div>
    </div>
</a>

<!-- 返回首页按钮 -->
<div class="gobackIndex">
    <a href="/Index">
        <!-- <em></em>
        首页 -->
        <img src="{$ImgUrl}goIndex.png" alt="">
    </a>
</div>

<script>
    window.onload = function() {
        // 格式
        new Rolldate({
            el: '#date-group1-1',
            format: 'YYYY-MM-DD',
            beginYear: 2000,
            endYear: 2100
        })
    }
</script>

<script data-main="{$JsUrl}pages/addCustomer.js?keytime={$versiontiem}"  defer async="true" src="{$JsUrl}lib/require.js"></script>

</body>

</html>