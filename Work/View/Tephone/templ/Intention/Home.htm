<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>客户管理</title>
    
     <link rel="stylesheet" href="{$CssUrl}main.css">
     <link rel="stylesheet" href="{$CssUrl}mui.min.css">
     <!-- <link rel="stylesheet" href="{$CssUrl}loading.css">
      <link rel="stylesheet" href="{$CssUrl}dropload.css"> -->
    <!--<link rel="stylesheet/less" type="text/css" href="./css/main.less">-->
    <style>
      .mui-scroll-wrapper{
        z-index: 50 !important;
        overflow: auto;
        /* left: 10px;
        right: 10px;
        width: calc(100% - 20px); */
      }
      .addCustom #pullrefresh{
        top:60px;
      }
      #allNumber{
        padding-left:10px;
        padding-right: 10px;
      }
    </style>
</head>

<body>
<!-- 客户管理 -->
<div class="sb-search sb-search-add">
    <form method="" id="" class="beauty" action="">
        <input type="hidden" name="class_id" value="">
        <div class="search-box">
            <span class="search-icon"></span>
            <input 
              type="text" 
              id="keyword" 
              class="find" autocomplete="off" 
              name="keyword" 
              placeholder="请输入客户姓名/手机号等" 
              >
            <button type="button" class="beauty search comm">搜索</button>
            <span class="filter-box comm">筛选</span>
        </div>
    </form>
</div>
<div class="addCustom">
  <!--下拉刷新容器-->
  <div id="pullrefresh" class="mui-content mui-scroll-wrapper">
    <div class="mui-scroll">
      <div class="mui-table-view mui-table-view-chevron" id="popMto">
        
      </div>
    </div>
  </div>
</div>
<!-- 筛选弹窗 -->
<div class="addCustom-prop none">
    <div class="level-box">
        <h2>筛选</h2>
        <div class="IntenteLevel">
            <p class="tit">意向星级</p>
            <div class="Love-box">
                <span class="love"></span>
                <span class="love"></span>
                <span class="love"></span>
                <span class="love"></span>
                <span class="love"></span>
            </div>
        </div>
        <div class="customState">
            <h3>客户状态</h3>
            <div class="state-sort">
                <span class="state" data-state='0'>待跟踪</span>
                <span class="state" data-state='1'>持续跟踪</span>
                <span class="state" data-state='2'>已柜询</span>
                <span class="state" data-state='3'>已试听</span>
                <span class="state" data-state='4'>已转正</span>
                <span class="state" data-state='-1'>无意向</span>
            </div>
        </div>
    </div>
</div>



<!-- 返回首页按钮 -->
<div class="gobackIndex">
    <a href="/index">
        <!-- <em></em>
        首页 -->
        <img src="{$ImgUrl}goIndex.png" alt="">
    </a>
</div>
{literal}
<script id="allNumberTmpl" type="text/x-dot-template">
  <div id='allNumber'>
      {{~it.list: item}}
      <div class="item">
        <a href="/Intention/followRecord?client_id={{=item.client_id}}&school_id={{=item.school_id}}">
          <div class="myInfo">
              <div class="img">
                {{? item.client_img == '' && item.client_sex == '男'}}
                  <img src="/Work/View/Tephone/images/man.png" alt="">
                {{?? item.client_img == '' && item.client_sex == '女'}}
                  <img src="/Work/View/Tephone/images/woman.png" alt="">
                {{?? item.client_img == '' && item.client_sex == ''}}
                  <img src="/Work/View/Tephone/images/default-img.png" alt="">
                {{?? item.client_img != '' }}
                  <img src="{{=item.client_img}}" alt="">
                {{?}}
              </div>
              <div class="product">
                  <div class="title">
                      {{=item.client_cnname}}
                      {{? item.client_enname !=''}}
                        /{{=item.client_enname}}
                      {{?}}
                     
                  </div>
                  {{? item.client_intention_level == 0}}
                    <div class="Love-box">
                        <span class="love"></span>
                        <span class="love"></span>
                        <span class="love"></span>
                        <span class="love"></span>
                        <span class="love"></span>
                    </div>
                  {{?? item.client_intention_level == 1}}
                    <div class="Love-box">
                        <span class="love cur"></span>
                        <span class="love"></span>
                        <span class="love"></span>
                        <span class="love"></span>
                        <span class="love"></span>
                    </div>
                  {{?? item.client_intention_level == 2}}
                    <div class="Love-box">
                        <span class="love cur"></span>
                        <span class="love cur"></span>
                        <span class="love"></span>
                        <span class="love"></span>
                        <span class="love"></span>
                    </div>
                  {{?? item.client_intention_level == 3}}
                    <div class="Love-box">
                        <span class="love cur"></span>
                        <span class="love cur"></span>
                        <span class="love cur"></span>
                        <span class="love"></span>
                        <span class="love"></span>
                    </div>
                  {{?? item.client_intention_level == 4}}
                    <div class="Love-box">
                        <span class="love cur"></span>
                        <span class="love cur"></span>
                        <span class="love cur"></span>
                        <span class="love cur"></span>
                        <span class="love"></span>
                    </div>
                  {{?? item.client_intention_level == 5}}
                    <div class="Love-box">
                        <span class="love cur"></span>
                        <span class="love cur"></span>
                        <span class="love cur"></span>
                        <span class="love cur"></span>
                        <span class="love cur"></span>
                    </div>
                  {{?}}
              </div>
          </div>
          <div class="phoneAll">
              <span class="area"><em></em><object><a href="tel:{{=item.client_mobile}}">{{=item.client_mobile}}</a></object></span>
              <span class="bell"><em></em>{{=item.client_birthday}}</span>
          </div>
          {{? item.client_tracestatus != 4}}
            <div  class="follow" >继续跟踪</div>
          {{?}}
          <!-- 状态 -->
          <div class="followState">
              <span>{{=item.client_status}}</span>
          </div>
        </a>
      </div>
      {{~}}
  </div>
</script>
{/literal}

<script data-main="{$JsUrl}pages/addNew.js?keytime={$versiontiem}" src="{$JsUrl}lib/require.js?keytime={$versiontiem}"></script>
<script src="{$JsUrl}lib/jquery.js"></script>
<!-- <script src="{$JsUrl}/lib/dropload.min.js"></script> -->
<script src="{$JsUrl}lib/mui.min.js"></script>
<!-- <script>
  $(document).on('touchend','#allNumber',function(){
    alert(1)
  })

</script> -->
</body>

</html>