<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>跟进记录</title>
     <link rel="stylesheet" href="{$CssUrl}main.css">
    <script src="{$JsUrl}lib/rolldate.min.js"></script>
    <link rel="stylesheet" href="{$CssUrl}mui.min.css">
    <!-- <script type="text/javascript" src="./js/lib/jquery.js"></script> -->
    <!--<link rel="stylesheet/less" type="text/css" href="./css/main.less">-->
    <style>
      .mui-scroll-wrapper{
        z-index: 50 !important;
        overflow: auto;
        /* top: 70px; */
        top:156px;
        left: 10px;
        right: 10px;
        bottom: 60px;
        width: calc(100% - 20px);
      }
    </style>
</head>

<body>
<!-- 跟进记录 -->
<div class="byDay-box" data-clientid="{$datatype.client_id}">
    <!-- <div class="select">
        <div class="title">请选择时间</div>
        <ul class="option none">
            <li>请选择时间</li>
            <li>按日</li>
            <li>按周</li>
            <li>按月</li>
        </ul>
    </div> -->
    <!--<div class="specificDate">-->
        <!--<div class="one">-->
            <!--<input-->
                    <!--class="form-control date"-->
                    <!--type="text"-->
                    <!--id="date-group1-1"-->
                    <!--placeholder="开始时间"-->
            <!--&gt;-->
        <!--</div>-->
        <!--<div class="one">-->
            <!--<input-->
                    <!--class="form-control date"-->
                    <!--type="text"-->
                    <!--id="date-group1-2"-->
                    <!--placeholder="结束时间"-->
            <!--&gt;-->
        <!--</div>-->

    <!--</div>-->
</div>
<div class="byDay-follow ">
    <div class="item item-info">
        <div class="myInfo">
            <div class="img">

                {if $ClientOne.client_img}
                <img src="{$ClientOne.client_img}" alt="">
                {else}
                  {if $ClientOne.client_sex =='女'}
                <img src="/Work/View/Tephone/images/woman.png" alt="">
                 {elseif $ClientOne.client_sex =='男'}
                <img src="/Work/View/Tephone/images/man.png" alt="">
                {else}
                <img src="/Work/View/Tephone/images/default-img.png" alt="">
                   {/if}
                {/if}

            </div>
            <div class="product">
              <div class="title">
                  {$ClientOne.client_cnname}
                  {if $ClientOne.client_enname}
                    /{$ClientOne.client_enname}
                  {/if}
                  <!-- <em></em> -->
              </div>

                <div class="Love-box">
                    {if $ClientOne.client_intention_level ==1}
                    <span class="love cur"></span>
                    {/if}

                    {if $ClientOne.client_intention_level ==2}
                    <span class="love cur"></span>
                    <span class="love cur"></span>
                    {/if}
                    {if $ClientOne.client_intention_level ==3}
                    <span class="love cur"></span>
                    <span class="love cur"></span>
                    <span class="love cur"></span>
                    {/if}
                    {if $ClientOne.client_intention_level ==4}
                    <span class="love cur"></span>
                    <span class="love cur"></span>
                    <span class="love cur"></span>
                    <span class="love cur"></span>
                    {/if}
                    {if $ClientOne.client_intention_level ==5}
                    <span class="love"></span>
                    <span class="love"></span>
                    <span class="love"></span>
                    <span class="love"></span>
                    <span class="love"></span>
                    {/if}
                </div>

            </div>
        </div>
        <div class="phoneAll">
            <span class="area"><em></em><object><a href="tel:13696753335">{$ClientOne.client_mobile}</a></object></span>
            <span class="bell"><em></em>{$ClientOne.client_birthday}</span>
        </div>
      </div>
  <div 
    id="pullrefresh" 
    class="mui-content mui-scroll-wrapper"
  >
      <div class="mui-scroll">
        <div class="mui-table-view mui-table-view-chevron" id="popMto">
          
        </div>
      </div>
  </div>
</div>
<!-- 底部添加跟进 -->
<div class="footer-addFollow">
    <a href="/Intention/addRecord?client_id={$datatype.client_id}&school_id={$datatype.school_id}">添加跟进</a>
</div>
<!-- 选择时间-遮罩层-->
<div class="brand-cover-props none">
    <a href="javascript:void(0);"></a>
</div>
<!-- 编辑 -->
<div class="SpringFestival-prop none">
    <div class="hot-box">
        <p class="title">修改沟通内容</p>
        <div class="content-box">
        <textarea class="area"  name="" id="">
          客户沟通积极热情，期待下次跟进客户沟通积极热情，期待下次跟进客户沟通积极热情，期待下次跟进客户沟通…
        </textarea>
        </div>
        <div class="action">
            <a href="javascript:;" class="save">保存</a>
            <a href="javascript:;" class="cancle">取消</a>
        </div>
    </div>
</div>
<!-- 返回首页按钮 -->
<div class="gobackIndex">
    <a href="/Index">
        <!-- <em></em>
        首页 -->
        <img src="{$ImgUrl}goIndex.png" alt="">
    </a>
</div>

{literal}
<script id="allNumberTmpl" type="text/x-dot-template">
  <div id="allNumber">
      {{~it.list: item :index}}
      <div class="item" data-order="{{=index}}" data-trackId="{{=item.track_id}}">
          <div class="myInfo">
              <div class="img">
                {{? item.client_img == '' && item.client_sex == '男'}}
                  <img src="/Work/View/Tephone/images/man.png" alt="">
                {{?? item.client_img == '' && item.client_sex == '女'}}
                  <img src="/Work/View/Tephone/images/woman.png" alt="">
                {{?? item.client_img == '' && item.client_sex == ''}}
                  <img src="/Work/View/Tephone/images/default-img.png" alt="">
                {{?? item.client_img != '' }}
                  <img src="{{=item.client_img}}" alt="">
                {{?}}
              </div>
              <div class="product">
                  <div class="title">
                      <span class="teach">{{=item.marketer_name}}</span>
                      <div class="theWay">
                          {{? item.track_followmode_name !=''}}
                          <span class="telephone"><em></em>{{=item.track_followmode_name}}</span>
                          {{?}}
                          {{? item.track_linktype!=''}}
                            <span class="ordinary"><em></em>{{=item.track_linktype}}</span>
                          {{?}}
                      </div>
                  </div>
                  <div class="Love-box">
                      {{=item.track_createtime}}
                  </div>
              </div>
          </div>
            <div class="phoneAll">
                {{? item.track_followmode == '1' && item.track_visitingtime !=''}}
                  <div class="area areaAdd mb10"><span>柜询时间：</span><em>{{=item.track_visitingtime}}</em></div>
                {{?? item.track_followmode == '2' }}
                  {{? item.track_visitingtime !=''}}
                    <div class="area areaAdd mb10"><span>试听日期：</span><em>{{=item.track_visitingtime}}</em></div>
                  {{?}}
                  {{? item.class_cnname !=''}}
                    <div class="area areaAdd mb10"><span>试听班级：</span><em>{{=item.class_cnname}}</em></div>
                  {{?}}
                {{?}}
                
                
                {{? item.track_followuptime !=''}}
                <span class="area">下次跟进时间：<em>{{=item.track_followuptime}}</em></span>
                {{?}}
            </div>   
          <div class="follow">
              <p>{{=item.track_note}}</p>
          </div>
          <a href="javascript:;" class="edit"></a>
      </div>
      {{~}}
  </div>
</script>
{/literal}

<script>
    window.onload = function() {

        // 格式
        new Rolldate({
            el: '#date-group1-1',
            format: 'YYYY-MM-DD',
            beginYear: 2000,
            endYear: 2100
        })
        new Rolldate({
            el: '#date-group1-2',
            format: 'YYYY-MM-DD',
            beginYear: 2000,
            endYear: 2100
        })
    }
</script>

<script data-main="{$JsUrl}/pages/followRecord.js?keytime={$versiontiem}"  defer async="true" src="{$JsUrl}lib/require.js"></script>
<script src="{$JsUrl}lib/jquery.js"></script>
<script src="{$JsUrl}lib/mui.min.js"></script>
</body>

</html>