<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>添加跟进</title>
     <link rel="stylesheet" href="{$CssUrl}/main.css">
    <!-- <script type="text/javascript" src="./js/lib/jquery.js"></script> -->
    <link rel="stylesheet" href="{$CssUrl}/swiper.min.css">
    <!--<link rel="stylesheet/less" type="text/css" href="./css/main.less">-->
    <script src="{$JsUrl}lib/swiper.min.js"></script>
    <script src="{$JsUrl}lib/rolldate.min.js"></script>
</head>

<body>
<!-- 添加跟进 -->
<div class="addRecord-box addRecord-box1" data-clientid="{$datatype.client_id}">
    <form action="">
        <div class="choose-ipt mb20">
            <p class="type-title">沟通类型</p>
            <input type="text" class="ipt" name="" id="scrollProp1" placeholder="请选择" readonly>
        </div>
        <div class="choose-ipt mb20">
            <p class="type-title">沟通对象</p>
            <input type="text" class="ipt" name="" id="scrollProp-add" placeholder="请选择" readonly>
        </div>
        <div class="choose-radio mb20">
            <p class="type-title">沟通效果</p>
            <div class="exchange-box exchange-box-result">
                <div class="exchange">
                    <input type="radio" class="radio" name="choose" data-result="1">
                    <label for="">有效沟通</label>
                </div>
                <div class="exchange">
                    <input type="radio" class="radio" name="choose" data-result="0">
                    <label for="">无效沟通</label>
                </div>
            </div>
        </div>
        <div class="loveLevel mb20">
            <p class="type-title">意向星级</p>
            <div class="Love-box">
                <span class="love"></span>
                <span class="love"></span>
                <span class="love"></span>
                <span class="love"></span>
                <span class="love"></span>
            </div>
        </div>
        <div class="choose-radio mb20">
            <p class="type-title">本次跟进类型</p>
            <div class="exchange-box exchange-box-type">
                <div class="exchange" data-type="1">
                    <input type="radio" class="radio" name="choose1" data-type="1">
                    <label for="">柜询</label>
                </div>
                <div class="exchange" data-type="2">
                    <input type="radio" class="radio" name="choose1" data-type="2">
                    <label for="">试听</label>
                </div>
                <div class="exchange" data-type="0">
                    <input type="radio" class="radio" name="choose1" data-type="0">
                    <label for="">普通回访</label>
                </div>
                <div class="exchange" data-type="-1">
                    <input type="radio" class="radio" name="choose1" data-type="-1">
                    <label for="">已确认无意向</label>
                </div>
            </div>
        </div>
        <div class="followType none">
          <div class="choose-ipt mb20">
              <p class="type-title start">接待人</p>
              <input type="text" class="ipt" id="type1-add-1"  name=""  placeholder="请选择接待人" readonly>
          </div>
          <div class="choose-ipt mb20">
              <p class="type-title">柜询类型</p>
              <input type="text" class="ipt" id="type1-add-2"  name=""  placeholder="请选择柜询类型" readonly>
          </div>
          <div class="follow-date choose-ipt mb20">
              <p class="type-title start">柜询到访日</p>
              <input
                      class="form-control date ipt"
                      type="text"
                      id="date-visiter-1"
                      placeholder="请选择柜询到访日"
              >
          </div>
        </div>
        <div class="followType none">
          <div class="choose-ipt mb20">
              <p class="type-title start">试听类型</p>
              <input type="text" class="ipt" id="type2-add-1"  name=""  placeholder="请选择试听类型" readonly>
          </div>
          <div class="choose-ipt mb20">
              <p class="type-title">接待人</p>
              <input type="text" class="ipt" id="type2-add-2"  name=""  placeholder="请选择接待人" readonly>
          </div>
          <div class="follow-date choose-ipt mb20">
              <p class="type-title start">试听日期</p>
              <input
                      class="form-control date ipt"
                      type="text"
                      id="date-visiter-2"
                      placeholder="请选择试听日期"
              >
          </div>
          <div class="choose-ipt mb20 none" id="leasenClass">
              <p class="type-title">试听班级</p>
              <input type="text" class="ipt" id="type2-add-3"  name=""  placeholder="请选择试听班级" readonly>
          </div>
          <div class="choose-ipt mb20 none" id="leasenClass1">
              <input type="text" class="ipt" id="leasenClass1-1"  name=""  placeholder="请输入试听班级">
          </div>
        </div>
        <div class="followType none"></div>
        <div class="followType none">
          <div class="choose-ipt mb20">
              <p class="type-title">流失类型</p>
              <input type="text" class="ipt" id="type4-add-1"  name=""  placeholder="请选择流失类型" readonly>
          </div>
        </div>
        <div class="choose-ipt mb20">
            <p class="type-title">沟通内容</p>
            <input type="text" class="ipt" id="scrollProp2"  name=""  placeholder="请选择模版" readonly>
        </div>
        <div class="area-box mb20">
            <textarea class="area" id="area-box" placeholder="请输入沟通内容" name="" id="" ></textarea>
        </div>
        <div class="choose-ipt mb20">
            <p class="type-title">下次跟进类型</p>
            <input type="text" class="ipt" id="scrollProp3"  name=""  placeholder="请选择" readonly>
        </div>
        <div class="follow-date follow-dateShow choose-ipt mb20 none">
            <p class="type-title">下次跟进日期</p>
            <input
                    class="form-control date ipt"
                    type="text"
                    id="date-group1-1"
                    placeholder="请选择"
            >
        </div>
    </form>
    <div class="review-footer-box">
        <div class="footer">
            <button type="submit" class="btn next" data-student="127631" data-class="20010" data-hour="412">
                保存
            </button>
            <a href="javascript:;" class="goback">
                取消并返回
            </a>
        </div>
    </div>
</div>
<!-- swiper1 沟通类型-->
<a class="swiper-box-prop swiper-box-prop1 none">
    <div class="scroll-choose">
        <div class="place">请选择</div>
        <div class="confirm">确认</div>
        <div class="swiper-box">
            <span class="line"></span>
            <div class="swiper-container">
                <div class="swiper-wrapper">
                     {if $commodeList}
                    {foreach from=$commodeList item=dataVar }
                    <div class="swiper-slide siwper-item " data-ele="88">{$dataVar.commode_name}</div>
                    {/foreach}
                    {/if}
                    <!--<div class="swiper-slide siwper-item " data-ele="88">微信沟通</div>-->
                    <!--<div class="swiper-slide siwper-item " data-ele="88">面聊</div>-->
                </div>
            </div>
        </div>
    </div>
</a>

<!-- swiper2  沟通内容-->
<a class="swiper-box-prop swiper-box-prop2 none" data-url>
    <div class="scroll-choose">
        <div class="place">请选择</div>
        <div class="confirm">确认</div>
        <div class="swiper-box">
            <span class="line"></span>
            <div class="swiper-container">
                <div class="swiper-wrapper">
                     {if $comment}
                     {foreach from=$comment item=dataVar }
                    <div class="swiper-slide siwper-item " data-ele="0" >{$dataVar.tracenote_remk}</div>
                    {/foreach}
                    {/if}
                </div>
            </div>
        </div>
    </div>
</a>
<!-- swiper3 下次跟进类型-->
<a class="swiper-box-prop swiper-box-prop3 none">
    <div class="scroll-choose">
        <div class="place">请选择</div>
        <div class="confirm">确认</div>
        <div class="swiper-box">
            <span class="line"></span>
            <div class="swiper-container">
                <div class="swiper-wrapper">
                    <div class="swiper-slide siwper-item " data-ele="0">普通跟进</div>
                    <div class="swiper-slide siwper-item " data-ele="1">提醒跟进</div>
                </div>
            </div>
        </div>
    </div>
</a>
<!-- type1-add-1-prop 柜询-接待人-->
<a class="swiper-box-prop type1-add-1-prop none">
    <div class="scroll-choose">
        <div class="place">请选择</div>
        <div class="confirm">确认</div>
        <div class="swiper-box">
            <span class="line"></span>
            <div class="swiper-container">
                <div class="swiper-wrapper">
                     {if $receptUser}
                     {foreach from=$receptUser item=dataVar}
                    <div class="swiper-slide siwper-item " data-ele="{$dataVar.marketer_id}">{$dataVar.marketer_name}</div>
                    {/foreach}
                    {/if}
                </div>
            </div>
        </div>
    </div>
</a>
<!-- type1-add-2-prop 柜询-柜询类型-->
<a class="swiper-box-prop type1-add-2-prop none">
    <div class="scroll-choose">
        <div class="place">请选择</div>
        <div class="confirm">确认</div>
        <div class="swiper-box">
            <span class="line"></span>
            <div class="swiper-container">
                <div class="swiper-wrapper">
                    <div class="swiper-slide siwper-item " data-ele="0">普通柜询</div>
                    <div class="swiper-slide siwper-item " data-ele="1">能力测试</div>
                </div>
            </div>
        </div>
    </div>
</a>
<!-- type2-add-1-prop 试听-试听类型-->
<a class="swiper-box-prop type2-add-1-prop none">
    <div class="scroll-choose">
        <div class="place">请选择</div>
        <div class="confirm">确认</div>
        <div class="swiper-box">
            <span class="line"></span>
            <div class="swiper-container">
                <div class="swiper-wrapper">
                    <div class="swiper-slide siwper-item " data-ele="0">公开课试听</div>
                    <div class="swiper-slide siwper-item " data-ele="1">插班试听</div>
                </div>
            </div>
        </div>
    </div>
</a>
<!-- type2-add-2-prop 试听-接待人-->
<a class="swiper-box-prop type2-add-2-prop none">
    <div class="scroll-choose">
        <div class="place">请选择</div>
        <div class="confirm">确认</div>
        <div class="swiper-box">
            <span class="line"></span>
            <div class="swiper-container">
                <div class="swiper-wrapper">
                    {if $receptUser}
                    {foreach from=$receptUser item=dataVar}
                    <div class="swiper-slide siwper-item " data-ele="{$dataVar.marketer_id}">{$dataVar.marketer_name}</div>
                    {/foreach}
                    {/if}
                </div>
            </div>
        </div>
    </div>
</a>
<!-- type2-add-3-prop 试听-试听班级-->
<a class="swiper-box-prop type2-add-3-prop none" id="popMto">
    <!-- <div class="scroll-choose">
        <div class="place">请选择</div>
        <div class="confirm">确认</div>
        <div class="swiper-box">
            <span class="line"></span>
            <div class="swiper-container">
                <div class="swiper-wrapper">
                    <div class="swiper-slide siwper-item " data-ele="0">普通跟进</div>
                    <div class="swiper-slide siwper-item " data-ele="1">提醒跟进</div>
                </div>
            </div>
        </div>
    </div> -->
</a>
<!-- type4-add-1-prop 已确认无意向-流失类型-->
<a class="swiper-box-prop type4-add-1-prop none">
    <div class="scroll-choose">
        <div class="place">请选择</div>
        <div class="confirm">确认</div>
        <div class="swiper-box">
            <span class="line"></span>
            <div class="swiper-container">
                <div class="swiper-wrapper">
                    <div class="swiper-slide siwper-item " data-ele="0">已报异业</div>
                    <div class="swiper-slide siwper-item " data-ele="1">强烈抵触</div>
                    <div class="swiper-slide siwper-item " data-ele="2">其他</div>
                </div>
            </div>
        </div>
    </div>
</a>

<!-- scrollProp-add-prop 沟通对象-->
<a class="swiper-box-prop scrollProp-add-prop none">
    <div class="scroll-choose">
        <div class="place">请选择</div>
        <div class="confirm">确认</div>
        <div class="swiper-box">
            <span class="line"></span>
            <div class="swiper-container">
                <div class="swiper-wrapper">
                     {if $objectList}
                     {foreach from=$objectList item=dataVar}
                    <div class="swiper-slide siwper-item " data-ele="{$dataVar.object_code}">{$dataVar.object_name}</div>
                    {/foreach}
                    {/if}
                </div>
            </div>
        </div>
    </div>
</a>
<!-- 返回首页按钮 -->
<div class="gobackIndex">
    <a href="/Index">
        <!-- <em></em>
        首页 -->
        <img src="{$ImgUrl}/goIndex.png" alt="">
    </a>
</div>
{literal}
<script id="allNumberTmpl" type="text/x-dot-template">
  <div id='allNumber'>
      <div class="scroll-choose">
          <div class="place">请选择</div>
          <div class="confirm">确认</div>
          <div class="swiper-box">
              <span class="line"></span>
              <div class="swiper-container">
                  <div class="swiper-wrapper">
                      {{~it.list: item}}
                      <div class="swiper-slide siwper-item " 
                        data-ele="0"
                        data-hourId="{{=item.hour_id}}"
                        data-classId="{{=item.class_id}}"
                        data-courseId="{{=item.course_id}}"
                        ><span class="cname-value">{{=item.class_cnname}}</span>  &nbsp; {{=item.hour_day}}
                      </div>
                      {{~}}
                  </div>
              </div>
          </div>
      </div>
    
  </div>
</script>
{/literal}
<script>
    window.onload = function() {
        // 格式
        new Rolldate({
            el: '#date-group1-1',
            format: 'YYYY-MM-DD',
            beginYear: 2000,
            endYear: 2100
        })
        new Rolldate({
            el: '#date-visiter-1',
            format: 'YYYY-MM-DD',
            beginYear: 2000,
            endYear: 2100
        })
        new Rolldate({
            el: '#date-visiter-2',
            format: 'YYYY-MM-DD',
            beginYear: 2000,
            endYear: 2100
        })
    }
</script>

<script data-main="{$JsUrl}pages/addRecord.js?keytime={$versiontiem}"  defer async="true" src="{$JsUrl}/lib/require.js"></script>

</body>

</html>