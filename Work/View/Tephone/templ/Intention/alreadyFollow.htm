<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>待跟进</title>
    <link rel="stylesheet" href="{$CssUrl}main.css">
    <link rel="stylesheet" href="{$CssUrl}mui.min.css">
    <!--<link rel="stylesheet/less" type="text/css" href="./css/main.less">-->
    <style>
      .mui-scroll-wrapper{
        z-index: 50 !important;
        overflow: auto;
        top: 70px;
        /* left: 10px;
        right: 10px; */
        /* bottom: 60px; */
        /* width: calc(100% - 20px); */
      }
      #allNumber{
        padding-left:10px;
        padding-right: 10px;
      }
    </style>
</head>

<body>
<div class="tabs">
    <ul>
        <li><a href="/Intention/waitFollow">待跟进客户</a></li>
        <li class="cur"><a href="/Intention/alreadyFollow">已跟进客户</a></li>
    </ul>
</div>
<div class="addCustom addCustom-add">
  <div 
    id="pullrefresh" 
    class="mui-content mui-scroll-wrapper"
  >
      <div class="mui-scroll">
        <div class="mui-table-view mui-table-view-chevron" id="popMto">
          
        </div>
      </div>
  </div>
</div>

<!-- 返回首页按钮 -->
<div class="gobackIndex">
    <a href="/Index">
        <!-- <em></em>
        首页 -->
        <img src="{$ImgUrl}goIndex.png" alt="">
    </a>
</div>
{literal}
<script id="allNumberTmpl" type="text/x-dot-template">
  <div id='allNumber'>
      {{~it.list: item}}
      <div class="item">
          <div class="myInfo">
              <div class="img">
                {{? item.client_img == '' && item.client_sex == '男'}}
                  <img src="/Work/View/Tephone/images/man.png" alt="">
                {{?? item.client_img == '' && item.client_sex == '女'}}
                  <img src="/Work/View/Tephone/images/woman.png" alt="">
                {{?? item.client_img == '' && item.client_sex == ''}}
                  <img src="/Work/View/Tephone/images/default-img.png" alt="">
                {{?? item.client_img != '' }}
                  <img src="{{=item.client_img}}" alt="">
                {{?}}
              </div>
              <div class="product">
                  <div class="title">
                      {{=item.client_cnname}}
                      {{? item.client_enname !=''}}
                        /{{=item.client_enname}}
                      {{?}}
                    
                  </div>
                  {{? item.client_intention_level == 0}}
                    <div class="Love-box">
                        <span class="love"></span>
                        <span class="love"></span>
                        <span class="love"></span>
                        <span class="love"></span>
                        <span class="love"></span>
                    </div>
                  {{?? item.client_intention_level == 1}}
                    <div class="Love-box">
                        <span class="love cur"></span>
                        <span class="love"></span>
                        <span class="love"></span>
                        <span class="love"></span>
                        <span class="love"></span>
                    </div>
                  {{?? item.client_intention_level == 2}}
                    <div class="Love-box">
                        <span class="love cur"></span>
                        <span class="love cur"></span>
                        <span class="love"></span>
                        <span class="love"></span>
                        <span class="love"></span>
                    </div>
                  {{?? item.client_intention_level == 3}}
                    <div class="Love-box">
                        <span class="love cur"></span>
                        <span class="love cur"></span>
                        <span class="love cur"></span>
                        <span class="love"></span>
                        <span class="love"></span>
                    </div>
                  {{?? item.client_intention_level == 4}}
                    <div class="Love-box">
                        <span class="love cur"></span>
                        <span class="love cur"></span>
                        <span class="love cur"></span>
                        <span class="love cur"></span>
                        <span class="love"></span>
                    </div>
                  {{?? item.client_intention_level == 5}}
                    <div class="Love-box">
                        <span class="love cur"></span>
                        <span class="love cur"></span>
                        <span class="love cur"></span>
                        <span class="love cur"></span>
                        <span class="love cur"></span>
                    </div>
                  {{?}}
              </div>
          </div>
          <div class="phoneAll">
              <span class="area"><em></em><a href="tel:{{=item.client_mobile}}">{{=item.client_mobile}}</a></span>
              <span class="bell"><em></em>{{=item.client_birthday}}</span>
          </div>
          {{? item.client_tracestatus != 4}}
          <a href="/Intention/followRecord?client_id={{=item.client_id}}&school_id={{=item.school_id}}" class="follow">跟进记录</a>
          {{?}}
          <!-- 状态 -->
          <div class="followState">
              <span>{{=item.client_status}}</span>
          </div>
      </div>
      {{~}}
  </div>
</script>
{/literal}
<script data-main="{$JsUrl}pages/aleadyFollow.js?keytime={$versiontiem}" src="{$JsUrl}lib/require.js"></script>
<script src="{$JsUrl}lib/jquery.js"></script>
<script src="{$JsUrl}lib/mui.min.js"></script>
</body>

</html>