<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>意向客户</title>
    <link rel="stylesheet" href="{$CssUrl}main.css">
    <!--<link rel="stylesheet/less" type="text/css" href="./css/main.less">-->
</head>

<body>
<!-- 意向客户 -->
<div class="intent-box">
    <div class="title">
        <span>我的意向客户</span>
    </div>
    <div class="clientNum">
        <span class="num">{$dataList.client_allnum}</span>
        <span class="word">我的客户数</span>
    </div>
    <div class="img">
        <img src="{$ImgUrl}/love-bg.png" alt="">
    </div>
</div>
<div class="customeBrief-box">
    <div class="changes-box">
        <div class="brief">客户简报</div>
        <div class="brief-change">
            <span class="change cur">今日</span>
            <span class="change">本周</span>
        </div>
    </div>
    <div class="texts-box-add">
        <!-- 今日 -->
        <div class="texts-item">
            <div class="texts-box">
                <a href="#" class="link">
                    <span>{$dataList.client_addnum}</span>
                    <span>新增</span>
                </a>
                <a href="/Intention/waitFollow" class="link">
                    <span>{$dataList.track_nonum}</span>
                    <span>待跟进</span>
                </a>
                <a href="#" class="link">
                    <span>{$dataList.track_invitenum}</span>
                    <span>柜询/试听</span>
                </a>
                <a href="/Intention/alreadyFollow" class="link">
                    <span>{$dataList.track_alnum}</span>
                    <span>已跟进</span>
                </a>
                <a href="#" class="link">
                    <span>{$dataList.track_conversionnum}</span>
                    <span>已转正</span>
                </a>
            </div>
        </div>
        <!-- 本周 -->
        <div class="texts-item none">
            <div class="texts-box">
                <a href="./addNew.html" class="link">
                    <span>{$weekList.client_addnum}</span>
                    <span>新增</span>
                </a>
                <a href="./waitFollow.html" class="link">
                    <span>{$weekList.track_nonum}</span>
                    <span>待跟进</span>
                </a>
                <a href="#" class="link">
                    <span>{$weekList.track_invitenum}</span>
                    <span>柜询/试听</span>
                </a>
                <a href="./aleadyFollow.html" class="link">
                    <span>{$weekList.track_alnum}</span>
                    <span>已跟进</span>
                </a>
                <a href="#" class="link">
                    <span>{$weekList.track_conversionnum}</span>
                    <span>已转正</span>
                </a>
            </div>
        </div>

    </div>
</div>
<div class="features-sort features-sort1">
    <ul class="features">
        <li class="item item1">
            <a href="/{$u}/addCustomer">
                <div class="img-box">
                    <img src="{$ImgUrl}/love1.png" alt="" class="img">
                </div>
                <span>添加客户</span>
            </a>
        </li>
        <li class="item item2">
            <a href="/Intention">
                <div class="img-box">
                    <img src="{$ImgUrl}/love2.png" alt="" class="img">
                </div>
                <span>客户管理</span>
            </a>
        </li>
        <!--<li class="item item3">-->
        <!--<a href="/{$u}/intviteCount">-->
        <!--<div class="img-box">-->
        <!--<img src="{$ImgUrl}/love3.png" alt="" class="img">-->
        <!--</div>-->
        <!--<span>跟进统计</span>-->
        <!--</a>-->
        <!--</li>-->
        <!--<li class="item item4">-->
        <!--<a href="/{$u}/clientCount">-->
        <!--<div class="img-box">-->
        <!--<img src="{$ImgUrl}/love3.png" alt="" class="img">-->
        <!--</div>-->
        <!--<span>客户分析</span>-->
        <!--</a>-->
        <!--</li>-->
    </ul>

</div>
<div class="compont-calendar">
    <div id="cal" class="Calendar calendarList calendarList-add">
        {if $evenList}
        <div class="event-list text-1">

            <div class="list-tit">
                <span>日程安排</span>
            </div>

            <div class="event-box">
                {if $evenList}
                {foreach from =$evenList item =DataVar}
                <div class="event-row">
                    <span class="detail-time">{$DataVar.event_time|date_format:'%Y-%m-%d'}</span>
                    <span class="detail-event">{$DataVar.event_remark}</span>
                </div>
                {/foreach}
                {/if}
            </div>
        </div>
        {/if}
    </div>
</div>
<div class="wait-follow">

    {if $clientList}
    {foreach from=$clientList item=dataVar }
    <div class="item">
        <a href="/{$u}/followRecord?client_id={$dataVar.client_id}&school_id={$datatype.school_id}" class="item-link">
            <div class="myInfo">
                <div class="img">
                    {if $dataVar.client_img }
                    <img src="{$dataVar.client_img}" alt="">
                    {elseif $dataVar.client_sex=='男' }
                    <img src="/Work/View/Tephone/images/man.png" alt="">
                    {elseif $dataVar.client_sex=='女' }
                    <img src="/Work/View/Tephone/images/woman.png" alt="">
                    {else}
                    <img src="/Work/View/Tephone/images/default-img.png" alt="">
                    {/if}
                </div>
                <div class="product">
                    <div class="title">
                        <div class="teach">
                            <span class="name">{$dataVar.client_cnname}
                              {if $dataVar.client_enname !=''}
                                /{$dataVar.client_enname}
                              {/if}
                            </span>
                            <!-- <em class="mark"></em> -->
                            <span class="age">{$dataVar.client_age}岁</span>
                        </div>
                        <span class="ordinary ordinary1"><em></em>未跟进</span>
                        <!-- <span class="ordinary ordinary2"><em></em>已跟进</span>  -->
                    </div>
                    <div class="follow-times">
                        <div class="Love-box">
                            {if $dataVar.client_intention_level==0}
                            <span class="love  "></span>
                            <span class="love "></span>
                            <span class="love "></span>
                            <span class="love"></span>
                            <span class="love"></span>
                            {elseif $dataVar.client_intention_level==1 }
                            <span class="love cur"></span>
                            <span class="love "></span>
                            <span class="love "></span>
                            <span class="love"></span>
                            <span class="love"></span>
                            {elseif $dataVar.client_intention_level==2 }
                            <span class="love cur"></span>
                            <span class="love cur"></span>
                            <span class="love "></span>
                            <span class="love"></span>
                            <span class="love"></span>
                            {elseif $dataVar.client_intention_level==3 }
                            <span class="love cur"></span>
                            <span class="love cur"></span>
                            <span class="love cur"></span>
                            <span class="love"></span>
                            <span class="love"></span>
                            {elseif $dataVar.client_intention_level==4 }
                            <span class="love cur"></span>
                            <span class="love cur"></span>
                            <span class="love cur"></span>
                            <span class="love cur"></span>
                            <span class="love"></span>
                            {elseif $dataVar.client_intention_level==5 }
                            <span class="love cur"></span>
                            <span class="love cur"></span>
                            <span class="love cur"></span>
                            <span class="love cur"></span>
                            <span class="love cur"></span>
                            {/if}
                        </div>
                        <span class="recurrence">已跟进{$dataVar.track_num}次</span>
                    </div>
                </div>
            </div>
            <div class="phoneAll">
                <span class="area"><em></em>上次跟进时间：{$dataVar.track_createtime|date_format:'%Y-%m-%d'}</span>
            </div>
            <div class="follow">
                跟进记录
            </div>
        </a>
    </div>
    {/foreach}
    {/if}

</div>
<!-- 返回首页按钮 -->
<div class="gobackIndex">
    <a href="/Index">
        <!-- <em></em>
        首页 -->
        <img src="{$ImgUrl}goIndex.png" alt="">
    </a>
</div>


<script data-main="{$JsUrl}pages/intent.js?keytime={$versiontiem}" src="{$JsUrl}lib/require.js"></script>
</body>

</html>