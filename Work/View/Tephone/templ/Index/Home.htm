<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>首页</title>
    <link rel="stylesheet" href="{$CssUrl}main.css">
    <!--   <link rel="stylesheet/less" type="text/css" href="./css/main.less"> -->
    <link rel="stylesheet" href="{$CssUrl}swiper.min.css">
    <script src="{$JsUrl}lib/swiper.min.js"></script>
</head>

<body>
<!-- 隐藏域 -->
<form action="">
    <input type="hidden" id="staffer_id"  name="staffer_id" value ="{$istaffer.staffer_id}" >
    <input type="hidden" id="company_id"  name="company_id" value ="{$istaffer.company_id}" >
    <input type="hidden" id="school_id"  name="school_id" value ="{$datatype.school_id}" >
</form>
<div class="index-box">
    <div class="add-swiper-position">
        <div class="school-select">
            <div class="select">
                {if $schoolList}
                <div class="title" data-schoolid="{$datatype.school_id}">{$datatype.school_cnname}</div>
                {/if}
                <ul class="option none">
                    <!-- <li data-schoolid="{$datatype.school_id}">请选择学校</li> -->
                    {if $schoolList}
                    {foreach from=$schoolList item=dataVar}
                    <li data-schoolid="{$dataVar.school_id}">{$dataVar.school_cnname}</li>
                    {/foreach}
                    {/if}
                </ul>
            </div>
        </div>
        <!-- 下拉的遮罩层 -->
        <div class="cover-props none">
            <a href="javascript:void(0);"></a>
        </div>

    </div>
    <!-- 轮播 -->
    <div class="index-slider" style="margin-top:-68px;">
        <div class="swiper-container swiper-container4">
            <div class="swiper-wrapper">
                <div class="swiper-slide"><img src="{$ImgUrl}slider1.jpg" alt=""></div>
                <div class="swiper-slide"><img src="{$ImgUrl}slider1.jpg" alt=""></div>
                <div class="swiper-slide"><img src="{$ImgUrl}slider1.jpg" alt=""></div>
            </div>
            <!-- Add Pagination -->
            <div class="swiper-pagination">
                <span class="swiper-pagination-bullet"></span>
                <span class="swiper-pagination-bullet"></span>
                <span class="swiper-pagination-bullet"></span>
            </div>
        </div>
    </div>
    <!-- 功能分类 -->
    <div class="features-sort">
        <ul class="features">
            {if $stafferPost.isSchool==1}
            <li class="item item2">
                <a href="/Clocking">
                    <div class="img-box">
                        <img src="{$ImgUrl}nav2.png" alt="" class="img">
                    </div>
                    <span>考勤状况</span>
                </a>
            </li>
            {/if}
            {if $stafferPost.isCrm==1}
            <li class="item item3">
                <a href="/Intention/MyIntention">
                    <div class="img-box">
                        <img src="{$ImgUrl}nav3.png" alt="" class="img">
                    </div>
                    <span>招生CRM</span>
                </a>
            </li>

            <li class="item item4">
                <a href="/Track/NeverTrack">
                    <div class="img-box">
                        <img src="{$ImgUrl}nav3.png" alt="" class="img">
                    </div>
                    <span>跟踪提醒</span>
                </a>
            </li>
            <li class="item item5">
                <a href="/Event">
                    <div class="img-box">
                        <img src="{$ImgUrl}nav3.png" alt="" class="img">
                    </div>
                    <span>我的日程</span>
                </a>
            </li>
            {/if}
        </ul>

    </div>
</div>
<!-- 选择编号弹窗 -->
<div class="choose-brand-box none">
    <div class="phone-box">
        <div class="phone">
            <ul class="clearfix" id="phone-ul">
                <li class="cur">
                    <p>
                        201810310009-活动专用园
                    </p>
                </li>
                <li>
                    <p>201810310009-活动专用校，培训联系测试学校</p>
                </li>
                <li>
                    <p>201810310009-活动专用校，培训联系测试学校</p>
                </li>
            </ul>
        </div>
        <div class="confirm">
            <button type="button" id="Tmobile">确定</button>
        </div>
    </div>
</div>
<!-- 选择编号遮罩层 -->
<div class="brand-cover-props none">
    <a href="javascript:void(0);"></a>
</div>
<!-- 底部 -->
{include file='footer.htm'}
<script data-main="{$JsUrl}pages/index.js?keytime={$versiontiem}" src="{$JsUrl}lib/require.js"></script>
</body>

</html>