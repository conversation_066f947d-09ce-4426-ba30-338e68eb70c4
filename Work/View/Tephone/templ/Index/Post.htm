<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>选择职位</title>
     <link rel="stylesheet" href="{$CssUrl}main.css">
    <!--<link rel="stylesheet/less" type="text/css" href="./css/main.less">-->
</head>

<body style="background-color:#fff;height: 100vh;">
<div class="post-change-banner">
    <p class="title">请选择职务</p>
    <img src="{$ImgUrl}post-banner.png" alt="">
</div>
<div class="detail-post">
    <div class="tab-post">
        {if $company}
        <span class="postComm post-name cur">集团职务</span>
        {/if}
        {if $school}
        <span class="postComm post-compute {if !$company}cur{/if}">校园职务</span>
        {/if}
    </div>
    <div class="tab-lists">
        {if $company}
        <div class="list list1 ">

            {foreach from=$company item=dataVar}
            <div class="item"  data-postbe_id ='{$dataVar.postbe_id}' data-school_id ='{$datatype.school_id}'>
                <div class="areas">
                    <em></em>
                    <span>{$dataVar.cnname}</span>
                </div>
                <p class="supervise">{$dataVar.post_name}</p>
            </div>
            {/foreach}

        </div>
        {/if}
        {if $school}
        <div class="list   {if $company}none list2{else}list1 {/if}">

            {foreach from=$school item=dataVar}
            <div class="item" data-postbe_id ='{$dataVar.postbe_id}' data-school_id="{$dataVar.school_id}">
                <div class="areas">
                    <em></em>
                    <span>{$dataVar.cnname}</span>
                </div>
                <p class="supervise">{$dataVar.post_name}</p>
            </div>
            {/foreach}
        </div>
        {/if}
    </div>
    <div class="action">
        <a href="javascript:;" class="confirm">确认</a>
    </div>
</div>

<script data-main="{$JsUrl}pages/post.js?keytime={$versiontiem}" src="{$JsUrl}lib/require.js"></script>

</body>

</html>