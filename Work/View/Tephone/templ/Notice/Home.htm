<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>学校公告</title>
     <link rel="stylesheet" href="{$CssUrl}main.css">
     <link rel="stylesheet" href="{$CssUrl}mui.min.css">
    <!--<link rel="stylesheet/less" type="text/css" href="./css/main.less">-->
    <style>
      .mui-scroll-wrapper{
        z-index: 50 !important;
        overflow: auto;
        top: 70px;
        left: 10px;
        right: 10px;
        bottom: 60px;
        width: calc(100% - 20px);
      }
    </style>
</head>
<body>
<div class="schoolAnnounce-box schoolAnnounce-box-add">
    <div class="tabs">
        <ul>
            <!-- <li><a href="./xx-teachingNotice.html">教学通知</a></li>
            <li><a href="./xx-message.html">家长反馈</a></li> -->
            <li  class="cur"><a href="#">学校公告</a></li>
        </ul>
    </div>
    <div class="schoolAnnounce-lists">
      <div 
        id="pullrefresh" 
        class="mui-content mui-scroll-wrapper"
      >
          <div class="mui-scroll">
            <div class="mui-table-view mui-table-view-chevron" id="popMto">
              
            </div>
          </div>
      </div>
        <!-- <ul class="items">
            <li class="i-first">
                <a href="javascript:;">
                    <div class="tits">
                        <span class="tit">寒潮预警</span>
                    </div>
                    <div class="personnel">
                        <p class="see">
                            人事部：<span class="name">Eric</span>
                        </p>
                        <p class="edition">
                            <span class="time">2018-12-12&nbsp;&nbsp;13:30</span>
                            <span>发布</span>
                        </p>
                    </div>
                    <span class="dot"></span>
                </a>
            </li>
            <li class="i-first">
                <a href="javascript:;">
                    <div class="tits">
                        <span class="tit">第十二号台风榴莲来了，请大家注意安全第十二号台风榴莲来了，请大家注意安全</span>
                    </div>
                    <div class="personnel">
                        <p class="see">
                            人事部：<span class="name">Eric</span>
                        </p>
                        <p class="edition">
                            <span class="time">2018-12-12&nbsp;&nbsp;13:30</span>
                            <span>发布</span>
                        </p>
                    </div>
                </a>
            </li>
        </ul> -->
    </div>
</div>

{include file='footer.htm'}

<div class="cover-props none"><a href="javascript:;"></a></div>
<!-- 学校公告弹窗 -->
<div class="announce-pop none">
    <div class="close"><a href="javascript:;"></a></div>
    <!-- 数据渲染到这里 -->
    <div id="popMto1" class="popMto1">

    </div>
</div>
<!-- 弹窗1 -->
<!-- <script id="taskDetail-template" type="text/x-handlebars-template">
    <div class="task-detail" id="taskDetail">
        <div class="announce">
            <h3 class="title">寒潮预警</h3>
            <p class="person">人事部：<span>Eric</span> </p>
            <p class="time">2018-12-12 13:30发布</p>
        </div>
        <div class="img">
            <img src="{$ImgUrl}/slider1.jpg" alt="">
        </div>
        <div class="article">
            Donec facilisis tortor ut augue lacinia, at viverra est semper. Sed sapien metus, scelerisque nec pharetra id, tempor a tortor. Pellentesque non dignissim neque. Ut porta viverra est, ut dignissim elit elementum ut. Nunc vel rhoncus nibh, ut tincidunt turpis. Integer ac enim pellentesque, adipiscing metus id, pharetra odio. Donec bibendum nunc sit amet tortor scelerisque luctus et sit amet mauris. Suspendisse felis sem, condimentum ullamcorper est sit amet, molestie mollis nulla. Etiam lorem orci, consequat ac magna quis, facilisis vehicula neque.
        </div>
        <div class="download">
            <a href="#">
                <span class="loadName">附件：关于自然遭害预防文件.doc</span>
                <span class="btn-load">下载</span>
            </a>
        </div>
    </div>
</script> -->
{literal}
<script id="taskDetailTmpl" type="text/x-dot-template">
  <div class="task-detail" id="taskDetail">
      {{~it.list: item}}
      <div class="announce">
          <h3 class="title">{{=item.notice_title}}</h3>
          <p class="person">发布人：<span>{{=item.staffer_cnname}}</span> </p>
          <p class="time">{{=item.notice_createtime}}发布</p>
      </div>
      <div class="article">
          {{=item.notice_connet}}
      </div>
      {{? item.notice_filelist != 0}}
      <div class="download">
          <a href="{{=item.notice_filelist}}">
              <span class="loadName">附件:</span>
              <span class="btn-load">下载</span>
          </a>
      </div>
      {{?}}
      {{~}}
  </div>
</script>
{/literal}
{literal}
<script id="allNumberTmpl" type="text/x-dot-template">
  <div id='allNumber'>
      <ul class="items">
        {{~it.list: item}}
          <li class="i-first" data-noticeId="{{=item.notice_id}}">
              <a href="javascript:;">
                  <div class="tits">
                      <div class="tit">{{=item.notice_title}}</div>
                  </div>
                  <div class="personnel">
                      <p class="see">
                          发布人: <span class="name">{{=item.staffer_cnname}}</span>
                      </p>
                      <p class="edition">
                          <span class="time">{{=item.notice_createtime}}</span>
                          <span>发布</span>
                      </p>
                  </div>
              </a>
          </li>
        {{~}}
      </ul>
  </div>
</script>
{/literal}
<script data-main="{$JsUrl}pages/schoolAnnounce.js?keytime={$versiontiem}" src="{$JsUrl}lib/require.js"></script>
<script src="{$JsUrl}lib/jquery.js"></script>
<script src="{$JsUrl}lib/mui.min.js"></script>
</body>
</html>