<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>我的日程</title>
    <link rel="stylesheet" href="{$CssUrl}main.css">

    <script src="{$JsUrl}lib/rolldate.min.js"></script>
</head>

<body style="background:#fff !important;">
<div class="compont-calendar">
    <div id="cal" class="Calendar calendarList">
        <div id="top" class="month-top clearFloat">
            <div class="month-box">
                <a class="btn prev" href="">
                    <i class="el-icon-arrow-left"></i>
                </a>
                <div class="time">
                    <span class="month"><span class="month-add"></span>月</span>
                    <span class="year"><span class="year-add"></span>年</span>
                    <input type="text" id="date-group1-1" placeholder="1111">
                </div>
                <a class="btn next" href="">
                    <i class="el-icon-arrow-right"></i>
                </a>
            </div>
        </div>
        <ul id="wk" class="week-list">

            <li>日</li>
            <li>一</li>
            <li>二</li>
            <li>三</li>
            <li>四</li>
            <li>五</li>
            <li>六</li>

        </ul>
        <div id="cm">
            <ul class="month-list clearFloat">
                {if $mothList}
                {foreach from=$mothList item=DataVar}
                <li class="{if $DataVar.is_have==1}isevent{/if}"><span class="{if $DataVar.forbid==1}preDay{/if} unactive {if $DataVar.day == $datatype.day_num} active{/if}" >{$DataVar.day}</span></li>
                {/foreach}
                {/if}
            </ul>
        </div>


        <div class="event-list text-1">

            <div class="list-tit">
                <span>日程安排</span>
                <a href="/{$u}/AddEven" class="add"></a>
            </div>
            {if $evenList}
            <div class="event-box">
                {if $evenList}
                {foreach from =$evenList item =DataVar}
                <div class="event-row">
                    <span class="detail-time">{$DataVar.event_time|date_format:'%Y-%m-%d'}</span>
                    <span class="detail-event">{$DataVar.event_remark}</span>
                </div>
                {/foreach}
                {/if}
            </div>
            {/if}
        </div>


</div>

<!-- 返回首页按钮 -->
<div class="gobackIndex">
    <a href="/Index">
        <!-- <em></em>
        首页 -->
        <img src="{$ImgUrl}goIndex.png" alt="">
    </a>
</div>

<script data-main="{$JsUrl}pages/mySchedule.js" src="{$JsUrl}lib/require.js"></script>

</body>

</html>