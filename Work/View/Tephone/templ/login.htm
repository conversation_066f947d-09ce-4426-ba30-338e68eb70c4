<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>校务管理中心</title>
    <link rel="stylesheet" href="{$CssUrl}main.css">
    <!-- <link rel="stylesheet/less" type="text/css" href="./css/main.less"> -->
</head>

<body>
<div class="login">
    <div class="img-logo"><img src="{$ImgUrl}/logo.png" alt=""></div>
    <div class="account-box">
        <ul class="account">
            <li class="tent cur">账号登录</li>
            <li class="tent">验证码登录</li>
        </ul>
        <div class="account-list">
            <div class="list account-login active">
                <form action="post" id="loginform">
                    <div class="form-group mb20">
                        <label for="">集团编号</label>
                        <input type="text" id="gmcBrand" name="L_code" class="ipt" placeholder="请输入集团编号" />
                    </div>
                    <div class="form-group mb20">
                        <label for="">账号</label>
                        <input type="text" id="usr" name="L_name" class="ipt" placeholder="请输入账号" />
                    </div>
                    <div class="form-group mb20">
                        <label for="">密码</label>
                        <input type="password" id="password" name="L_pswd" class="ipt" placeholder="请输入密码" />
                    </div>
                    <div class="psw-box">
                        <!-- <div class="remember">
                          <input type="checkbox" id="keep" class="checkbox">
                          <label for="keep">记住密码</label>
                        </div> -->
                        <!--<a href="{$ImgUrl}/forgetPassword.html" class="forget">忘记密码</a>-->
                    </div>
                    <div class="action">
                        <div class="tips"></div>
                        <button type="submit" class="btn">登录</button>
                    </div>
                </form>
            </div>
            <div class="list phone-login none">
                <form method="post" id="phone-form">
                    <div class="form-group mb20">
                        <label for="">集团编号</label>
                        <input type="text" id="gmcBrand" class="ipt" name="L_code"  placeholder="请输入集团编号" />
                    </div>
                    <div class="form-group mb20">
                        <label for="">手机号</label>
                        <input type="number" id="phone" class="ipt" name="L_mobile" placeholder="请输入手机号" />
                    </div>
                    <div class="form-group vetry">
                        <label for="">验证码</label>
                        <div class="verification verification-code">
                            <input type="text" id="verification" name="L_verifycode" class="ipt" placeholder="请输入验证码" />
                            <button type="button" class="btn getCode">发送验证码</button>
                            <!-- <button  type="button" class="getCode disabled"></button> -->
                        </div>
                    </div>
                    <div class="action">
                        <button type="submit" class="btn">登录</button>
                    </div>
                </form>

            </div>
        </div>
    </div>
    <div class="bottom-img">
        <img src="{$ImgUrl}login-city.png" alt="">
    </div>
</div>

<!--   <script>
    less = {
      env: 'development'
    };
  </script>
  <script src="./dev/less.min.js"></script>
  <script>
    less.watch();
  </script> -->
<script data-main="{$JsUrl}pages/login.js?keytime={$versiontiem}" src="{$JsUrl}lib/require.js?keytime={$versiontiem}"></script>
</body>

</html>