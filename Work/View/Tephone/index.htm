<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta http-equiv="X-UA-Compatible" content="ie=edge">
  <title>课叮铛校园管理中心</title>
  <link rel="stylesheet" href="./css/main.css">
<!--   <link rel="stylesheet/less" type="text/css" href="./css/main.less"> -->
  <link rel="stylesheet" href="./css/swiper.min.css">
  <script src="./js/lib/swiper.min.js"></script>
</head>

<body>
  <div class="index-box">
    <div class="add-swiper-position">
      <div class="school-select">
        <div class="select">
          <div class="title">课叮铛幼儿园</div>
          <ul class="option none">
            <li>课叮铛幼儿园</li>
            <li>课叮铛幼儿园</li>
            <li>课叮铛幼儿园</li>
          </ul>
        </div>
      </div>
      <!-- 下拉的遮罩层 -->
      <div class="cover-props none">
        <a href="javascript:void(0);"></a>
      </div>
      <!-- 轮播 -->
      <div class="index-slider">
        <div class="swiper-container swiper-container4">
          <div class="swiper-wrapper">
            <div class="swiper-slide"><img src="./images/slider1.jpg" alt=""></div>
            <div class="swiper-slide"><img src="./images/slider1.jpg" alt=""></div>
            <div class="swiper-slide"><img src="./images/slider1.jpg" alt=""></div>
          </div>
          <!-- Add Pagination -->
          <div class="swiper-pagination">
            <span class="swiper-pagination-bullet"></span>
            <span class="swiper-pagination-bullet"></span>
            <span class="swiper-pagination-bullet"></span>
          </div>
        </div>
      </div>
    </div>
    <!-- 功能分类 -->
    <div class="features-sort">
      <ul class="features">
        <li class="item item1">
          <a href="#">
            <div class="img-box">
              <img src="./images/nav1.png" alt="" class="img">
            </div>
            <span>我的课表</span>
          </a>
        </li>
        <li class="item item2">
          <a href="./today.html">
            <div class="img-box">
              <img src="./images/nav2.png" alt="" class="img">
            </div>
            <span>考勤状况</span>
          </a>
        </li>
        <li class="item item3">
          <a href="./intent.html">
            <div class="img-box">
              <img src="./images/nav3.png" alt="" class="img">
            </div>
            <span>意向客户</span>
          </a>
        </li>
      </ul>

    </div>
  </div>
  <!-- 选择编号弹窗 -->
  <div class="choose-brand-box none">
    <div class="phone-box">
      <div class="phone">
        <ul class="clearfix" id="phone-ul">
          <li class="cur">
            <p>
              201810310009-活动专用园
            </p>
          </li>
          <li>
            <p>201810310009-活动专用校，培训联系测试学校</p>
          </li>
          <li>
            <p>201810310009-活动专用校，培训联系测试学校</p>
          </li>
        </ul>
      </div>
      <div class="confirm">
        <button type="button" id="Tmobile">确定</button>
      </div>
    </div>
  </div>
  <!-- 选择编号遮罩层 -->
  <div class="brand-cover-props none">
    <a href="javascript:void(0);"></a>
  </div>
  <!-- 底部 -->
  <div class="footer-box">
    <div class="footer">
      <a href="./index.html" class="icon icon1 cur">
        <span></span>
        <p>首页</p>
      </a>
      <a href="./home.html" class="icon icon2">
        <span></span>
        <p>班级</p>
      </a>
      <a href="#" class="icon icon3">
        <span></span>
        <p>消息</p>
      </a>
      <a href="./my.html" class="icon icon4">
        <span></span>
        <p>个人</p>
      </a>
    </div>
  </div>

<!--   <script>
    less = {
      env: 'development'
    };
  </script>
  <script src="./dev/less.min.js"></script>
  <script>
    less.watch();
  </script> -->
  <script data-main="./js/pages/index.js" src="./js/lib/require.js"></script>
</body>

</html>