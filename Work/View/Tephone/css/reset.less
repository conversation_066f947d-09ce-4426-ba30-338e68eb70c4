* {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  line-height: 100%;
  outline: none;
  -webkit-tap-highlight-color: rgba(0,0,0,0);
  -webkit-font-smoothing: antialiased; /*chrome、safari*/
  -moz-osx-font-smoothing: grayscale;/*firefox*/
  font-family: -apple-system, BlinkMacSystemFont, "PingFang SC","Helvetica Neue",STHeiti,"Microsoft Yahei",Tahoma,Simsun,sans-serif;
}
html, body {
  // height: 100%;
  // min-height: 100vh;
  // background-color: #ecf0f1;
}
input[disabled]::-webkit-input-placeholder,
input::-webkit-input-placeholder {
  font-size: 30px;
  color: #a4a4a4;
  opacity: 1;
}
input[disabled],
textarea[disabled] {
  opacity: 1;
  background-color: #fff;
  resize: none;
}
input,select,textarea,input[disabled],select[disabled],textarea[disabled] {
  font-size: 30px;
  color: #a4a4a4;
  background-color: #fff;
  resize: none;
}
h1,h2,h3,h4,h5,h6,strong {
  font-size: 14px;
  font-weight: normal;
}
a {
  text-decoration: none;
}
ul,ol,li {
  list-style: none;
}
.bg-f {
  background-color: #fff;
}
.mt20 {
  margin-top: 20px;
}
.mt30 {
  margin-top: 30px;
}
.mt70 {
  margin-top: 70px;
}
.pl20 {
  padding-left: 20px;
}
.mb10{
  margin-bottom: 10px;
}
i, em {
  font-style: normal;
}
input,select,textarea,button,input[disabled],select[disabled],textarea[disabled]  {
  appearance: none;
  outline: none;
  border: none;
}
.tr {
  text-align: right;
}
.tc {
  text-align: center;
}
.tl {
  text-align: left;
}

img {
  max-width: 100%;
}


