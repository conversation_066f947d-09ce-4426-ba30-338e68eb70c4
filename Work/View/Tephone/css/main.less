@import './theme.less';
@import './reset.less';
@import './utils.less';
@import './tips.less';
// z-index: 1 3  5 7 9;
@baseFontSize: 37.5;
@baseEmFontSize: 24;
html,
body {
  background:rgba(248,248,248,1) !important;
  // height: 100%; // .p2r(padding-bottom, 100);
}

// var
@active: #f84c5a;
@mb20: 20;
// name: value;
.p2r(@name, @px) {
  @{name}: @px / @baseFontSize * 1rem;
}

// name: value1 value2;
.p4r(@name, @v, @h) {
  @v0: @v / @baseFontSize * 1rem;
  @h0: @h / @baseFontSize * 1rem;
  @{name}: @v0 @h0;
}

// name: v1 v2 v3 v4;
.p8r(@name, @u, @r, @d, @l) {
  @u0: @u / @baseFontSize * 1rem;
  @r0: @r / @baseFontSize * 1rem;
  @d0: @d / @baseFontSize * 1rem;
  @l0: @l / @baseFontSize * 1rem;
  @{name}: @u0 @r0 @d0 @l0;
}

// mixin
.clearfix() {
  &:before,
  &:after {
    content: " ";
    display: table;
  }
  &:after {
    clear: both;
  }
}

.elips() {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  .p2r(line-height, 32);
  vertical-align: middle;
}

// pub css
* {
  font-family: Helvetica, Tahoma, Arial, "PingFang SC", "Hiragino Sans GB", "Heiti SC", "Microsoft YaHei", "WenQuanYi Micro Hei";
}

/* 修改占位文字的默认样式 */

input::-moz-placeholder,
textarea::-moz-placeholder {
  color: #BFBFBF;
  .p2r(font-size, 12);
}

input:-ms-input-placeholder,
textarea:-ms-input-placeholder {
  color: #BFBFBF;
   .p2r(font-size, 12);
}

input::-webkit-input-placeholder,
textarea::-webkit-input-placeholder {
  color: #BFBFBF;
  .p2r(font-size, 12);
}

//登录
.login {
  background-color: #E7E7E7;
  height: 100vh;
  .p2r(padding-top, 10);

  .img-logo {
    .p2r(width, 141);
    .p2r(height, 50);
    text-align: center;
    margin: 0 auto;
    // .p2r(padding-top, 10);
    .p2r(margin-bottom, 20);

    img {
      width: 100%;
    }
  }

  .account-box {
    margin: 20px 48px;
    .p8r(margin, 20, 48, 20, 48);
    .p8r(padding, 20, 20, 20, 20);
    background-color: #fff;
    box-shadow: 0 12px 20px 4px rgba(0, 0, 0, 0.06);
    .p2r(border-radius, 8);
    position: relative;
    z-index: 2;

    .account {
      display: flex;
      align-items: center;

      li {
        .p2r(font-size, 12);
        color: #999999;
        position: relative;
        .p2r(margin-bottom, 40);

        &:first-child {
          .p2r(margin-right, 20);
        }

        &.cur {
          .p2r(font-size, 18);
          color: #333333;

          &::after {
            content: '';
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            .p2r(width, 22);
            .p2r(height, 8);
            background: #4875E7;
            .p2r(border-radius, 4);
            .p2r(bottom, -12);
          }
        }
      }
    }

    .account-list {
      .list {
        display: none;

        &.active {
          display: block;
        }

        .form-group {

          // .p2r(margin-bottom, 20);
          label {
            .p2r(font-size, 12);
            color: #999999;
            display: block;
            .p2r(margin-bottom, 10);
          }

          .ipt {
            .p2r(font-size, 16);
            color: #333333;
            .p2r(width, 240);
            .p2r(height, 44);
            border: 1px solid #D5D5D5;
            .p8r(padding, 12, 12, 12, 12);
          }

          input::-webkit-input-placeholder {
            .p2r(font-size, 16);
            color: #bfbfbf;
          }

          &.vetry {
            .p2r(margin-bottom, 80);
          }

          .verification {
            display: flex;
            align-items: center;
          }

          #verification {
            .p2r(width, 130);
            .p2r(height, 44);
            .p2r(line-height, 44);
            border: 1px solid #D5D5D5;
            // .p2r(margin-bottom, 10);
          }

          .btn {
            .p2r(width, 100);
            .p2r(height, 44);
            .p2r(line-height, 44);
            .p2r(font-size, 14);
            color: #FFFFFF;
            .p2r(margin-left, 10);
          }

          .getCode {
            background-color: #4875E7;
          }

          .cur {
            background-color: rgba(216, 216, 216, 1);
          }
        }

        .psw-box {
          display: flex;
          justify-content: space-between;
          align-items: center;
          .p2r(margin-bottom, 47);
          .p2r(font-size, 12);

          .remember {
            .checkbox {
              -webkit-appearance: none;
              display: inline-block;
              .p2r(width, 20);
              .p2r(height, 20);
              background: url(../images/word-checkbox.png) no-repeat center center;
              .p4r(background-size, 20, 20);
              vertical-align: middle;

              &:checked {
                display: inline-block;
                .p2r(width, 20);
                .p2r(height, 20);
                background: url(../images/word-checkbox-cur.png) no-repeat center center;
                .p4r(background-size, 20, 20);
              }
            }
          }

          .forget {
            color: #333333;
          }
        }

        .action {
          text-align: center;
          position: relative;

          .tips {
            position: absolute;
            top: -16px;
            left: 50%;
            transform: translateX(-50%);
            .p2r(font-size, 12);
            color: #D0021B;
            .p2r(margin-bottom, 2);
          }

          .btn {
            .p2r(width, 175);
            .p2r(height, 45);
            .p2r(line-height, 45);
            background-color: #4875E7;
            .p2r(font-size, 20);
            color: #FFFFFF;
            .p2r(border-radius, 24);
            box-shadow: 0 12px 16px 4px rgba(15, 15, 19, 0.12);
          }
        }
      }
    }
  }

  .bottom-img {
    width: 100%;
    height: 100px;
    position: fixed;
    left: 0;
    bottom: 0;
    z-index: 1;

    img {
      display: inline-block;
      width: 100%;
      height: 100%;
    }
  }
}

//忘记密码
.forget-psw {
  .p8r(padding, 10, 10, 10, 10);
  .form-enter();

  .verification {
    display: flex;
    align-items: center;
    .p2r(font-size, 16);

    .ipt {
      .p2r(width, 243);
      .p2r(height, 44);
      .p2r(margin-right, 12);
    }

    .getCode {
      .p2r(width, 100);
      .p2r(height, 44);
      background: rgba(72, 117, 231, 1);
      color: rgba(255, 255, 255, 1);
    }
  }

}

//确认密码
.confirm-psw {
  .p8r(padding, 10, 10, 10, 10);

  .fist-login {
    .p2r(font-size, 17);
    color: rgba(51, 51, 51, 1);
    .p2r(line-height, 24);
  }

  .form-enter();
}

.form-enter() {
  .form-group {
    position: relative;

    label {
      display: block;
      .p2r(font-size, 12);
      color: rgba(51, 51, 51, 1);
      .p2r(margin-bottom, 12);
    }

    .ipt {
      .p8r(padding, 0, 10, 0, 10);
      .p2r(font-size, 16);
      .p2r(width, 355);
      .p2r(height, 44);
      .p2r(line-height, 44);
      background: rgba(255, 255, 255, 1);
      border: 1px solid rgba(213, 213, 213, 1);
    }

    input::-webkit-input-placeholder {
      .p2r(font-size, 16);
      color: #bfbfbf;

    }

    .tips {
      position: absolute;
      .p2r(font-size, 12);
      .p2r(margin-top, 10);
      font-weight: 400;
      color: rgba(208, 2, 27, 1);
    }
  }

  .action {
    .p2r(margin-top, 70);

    .next {
      display: inline-block;
      .p2r(width, 355);
      .p2r(height, 44);
      .p2r(line-height, 44);
      .p2r(font-size, 20);
      background: rgba(72, 117, 231, 1);
      .p2r(border-radius, 355);
      color: #fff;
      text-align: center;
    }
  }
}

//首页
.index-box {
  background-color: #fff;
  height: 100vh;
}

//下拉选项
.add-swiper-position {
  display: inline-block;
  .p2r(width, 375);
  .p2r(height, 132);
  background: url(../images/net-bg.png) no-repeat center center;
  .p4r(background-size, 375, 132);
}

.school-select {
  position: relative;
  z-index: 101;
  box-shadow: 0px 12px 20px 3px rgba(0, 0, 0, 0.08);
  .p2r(margin-bottom, 20);
  .select {
    .p2r(width, 355);
    .p2r(height, 34);
    .p2r(line-height, 34);
    .p2r(padding-left, 10);
    .p2r(padding-right, 10);
    .p2r(margin-left, 10);
    .p2r(margin-right, 10);
    .p2r(top, 12);
    background: rgba(216, 216, 216, 0.2);
    .p2r(font-size, 12);
    color: #fff;
    position: relative;
    z-index: 3;
    .p2r(border-radius, 20);
    .title {
      .p2r(height, 34);
      .p2r(line-height, 34);
    }
    &:after {
      content: "";
      position: absolute;
      right: 0;
      top: 50%;
      transform: translateY(-50%);
      .p2r(width, 32);
      .p2r(height, 18);
      background: url(../images/down.png) no-repeat center center;
      .p4r(background-size, 16, 9);
    }
    .option {
      // width:100%;
      .p2r(width, 375);
      .p2r(height, 352);
      overflow-y: auto;
      position: absolute;
      top: 100%;
      .p2r(left, -10);
      z-index: 4;
      li {
        .p2r(height, 44);
        .p2r(line-height, 44);
        background: rgba(245, 248, 254, 1);
        .p2r(padding-left, 10);
        .p2r(padding-right, 10);
        background: rgba(245, 248, 254, 1);
        box-shadow: 0px 1px 0px 0px rgba(221, 221, 221, 1);
        border-bottom: 1px solid #ddd;
        color: rgba(51, 51, 51, 1);
        &:last-child {
          border-bottom: 0;
        }
        &.active{
          background-color: #4875E7;
          color: #fff;
        }
      }
    }
  }
}

//下拉的遮罩层 
.cover-props {
  a {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.2);
    z-index: 100;
  }
}

//轮播
.index-slider {
  position: relative;
  .p2r(min-width, 127);
  z-index: 7;
}

//功能分类
.features-sort {
  .features {
    .clearfix();
    .p8r(padding, 10, 10, 10, 10);
    li {
      width: 25%;
      float: left;
      .p2r(margin-top, 15);
      a {
        display: block;
        text-align: center;
        .img-box {
          .p2r(width, 55);
          .p2r(height, 55);
          border-radius: 50%;
          text-align: center;
          margin: 0 auto;
          box-shadow: 0px 12px 20px 3px rgba(0, 0, 0, 0.08);
          .img {
            width: 100%;
          }
        }
        span {
          display: block;
          .p2r(font-size, 12);
          .p2r(margin-top, 15);
          color: rgba(51, 51, 51, 1);
        }
      }
    }
  }
}

//选择编号弹窗
.choose-brand-box {
  position: absolute;
  .p2r(width, 245);
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  background-color: #fff;
  .p8r(padding, 10, 10, 10, 10);
  z-index: 999;
  border-radius: 5px;
  .phone-box {
    .p2r(width, 245);
    .phone {
      ul {
        .clearfix();
        li {
          float: left;
          .p2r(width, 105);
          .p2r(height, 80);
          .p2r(line-height, 80);
          overflow-y: auto;
          .p2r(margin-bottom, 10);
          .p2r(margin-right, 12);
          color: rgba(191, 191, 191, 1);
          .p2r(font-size, 12);
          background: rgba(236, 236, 236, 1);
          border-radius: 4px;
          text-align: center;
          .p8r(padding, 5, 5, 5, 5);
          display: table;
          p {
            display: table-cell;
            vertical-align: middle;
            .p2r(line-height, 14);
          }
          &.cur {
            color: #fff;
            background: rgba(72, 117, 231, 1);
          }
        }
      }
    }
    .confirm {
      .p2r(margin-bottom, -10);
      #Tmobile {
        .p2r(width, 245);
        .p2r(height, 40);
        .p2r(line-height, 40);
        .p2r(margin-left, -10);
        background: rgba(245, 248, 254, 1);
        border-radius: 0px 0px 8px 8px;
        color: rgba(72, 117, 231, 1);
        .p2r(font-size, 14);
      }
    }
  }
}

//选择编号遮罩层
.brand-cover-props {
  a {
    width: 100%;
    height: 100%;
    position: fixed;
    top: 0;
    bottom: 0;
    z-index: 3;
    background: rgba(51, 51, 51, 0.3);
  }
}

//搜索
.sb-search {
  .p2r(padding-left, 10);
  .p2r(padding-right, 10);
  .p2r(margin-top, 10);
  .p2r(margin-bottom, 15);
  .search-box {
    position: relative;
    .search-icon {
      position: absolute;
      .p2r(left, 12);
      top: 50%;
      transform: translateY(-50%); // .p2r(top, 22);
      .p2r(width, 14);
      .p2r(height, 14);
      background: url(../images/search.png) no-repeat center center;
      .p4r(background-size, 14, 14);
    }
    .find {
      .p2r(width, 310);
      .p2r(height, 34);
      .p2r(font-size, 12);
      .p2r(padding-left, 40);
      font-weight: 400;
      color: rgba(191, 191, 191, 1);
      background: rgba(255, 255, 255, 1);
      box-shadow: 0px 12px 20px 3px rgba(0, 0, 0, 0.08);
      .p2r(border-radius, 19);
    }
    input::-webkit-input-placeholder {
      .p2r(font-size, 12);
      color: rgba(191, 191, 191, 1);
    }
    .txt {
      .p2r(font-size, 16);
      color: rgba(72, 117, 231, 1);
    }
  }
}

//搜索
.campus-list {
  .p2r(padding-bottom, 50);
  .list-box {
    li {
      margin: 0 auto;
      .p8r(padding, 20, 0, 0, 0);
      .p2r(width, 355);
      .p2r(height, 131);
      background: rgba(255, 255, 255, 1);
      box-shadow: 0px 12px 20px 3px rgba(0, 0, 0, 0.08);
      .p2r(border-radius, 8);
      .p2r(margin-bottom, 30);
      .tit {
        .p8r(padding, 0, 13, 0, 13);
        .p2r(font-size, 16);
        font-weight: 600;
        color: rgba(51, 51, 51, 1);
      }
      .info-box {
        display: flex;
        justify-content: space-between;
        align-items: center;
        .p2r(margin-top, 8);
        .p2r(margin-bottom, 15);
        .p8r(padding, 0, 13, 0, 13);
        .info-left {
          .p2r(font-size, 12);
          .state {
            background-color: rgba(193, 210, 255, 1);
            color: rgba(255, 255, 255, 1);
            .p8r(padding, 2, 2, 2, 2);
          }
          .brand {
            .p2r(margin-left, 5);
            .p2r(margin-right, 10);
          }
        }
        .info-right {
          a {
            display: flex;
            align-items: center;
            .edit {
              .p2r(width, 14);
              .p2r(height, 14);
              background: url(../images/edit.png) no-repeat center center;
              .p4r(background-size, 14, 14);
              .p2r(margin-right, 2);
            }
            .publish {
              .p2r(font-size, 12);
              font-weight: 600;
              color: rgba(255, 92, 103, 1);
            }
          }
        }
      }
      .classInfo {
        .p2r(font-size, 12);
        .p2r(margin-bottom, 14);
        .p8r(padding, 0, 13, 0, 13);
        em {
          display: inline-block;
          .p2r(width, 14);
          .p2r(height, 14);
          background: url(../images/tag.png) no-repeat center center;
          .p4r(background-size, 14, 14);
          .p2r(margin-right, 2);
          vertical-align: middle;
        }
        span {}
      }
      .module-box {
        background: rgba(245, 248, 254, 1);
        border-radius: 0px 0px 8px 8px;
        display: flex;
        align-items: center;
        .p2r(height, 40);
        a {
          position: relative;
          flex: 1;
          .p2r(font-size, 12);
          font-weight: 600;
          color: rgba(72, 117, 231, 1);
          text-align: center;
          height: 1.06666667rem;
          line-height: 1.06666667rem;
          &:after {
            content: '';
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            right: 0;
            .p2r(width, 2);
            .p2r(height, 11);
            background-color: rgba(216, 216, 216, 1);
          }
          &:last-of-type::after {
            content: '';
            display: none;
          }
        }
      }
    }
  }
}

//底部
.footer-box {
  .p2r(width, 375); // .p2r(height, 56);
  // .p2r(margin-top, 56);
  position: fixed;
  bottom: 0;
  left: 0;
  background: rgba(255, 255, 255, 1);
  box-shadow: 0px -12px 20px 3px rgba(0, 0, 0, 0.08);
  z-index: 99;
  .footer {
    display: flex;
    align-items: center;
    a {
      // .p2r(height, 56);
      flex: 1;
      text-align: center;
      .p2r(padding-top, 4);
      .p2r(padding-bottom, 4);
      span {
        display: inline-block;
        .p2r(width, 27);
        .p2r(height, 27);
        .p2r(line-height, 27);
        vertical-align: middle;
      }
      p {
        .p2r(font-size, 12);
        font-weight: 400;
        color: rgba(191, 191, 191, 1);
        .p2r(padding-top, 3);
      }
      &.cur {
        p {
          color: rgba(72, 117, 231, 1);
        }
      }
      &.icon1 {
        span {
          background: url(/Work/View/Tephone/images/bottom1.png) no-repeat center center;
          .p4r(background-size, 27, 27);
        }
        &.cur {
          span {
            background: url(/Work/View/Tephone/images/bottom1-active.png) no-repeat center center;
            .p4r(background-size, 27, 27);
          }
        }
      }
      &.icon2 {
        span {
          background: url(/Work/View/Tephone/images/bottom2.png) no-repeat center center;
          .p4r(background-size, 27, 27);
        }
        &.cur {
          span {
            background: url(/Work/View/Tephone/images/bottom2-active.png) no-repeat center center;
            .p4r(background-size, 27, 27);
          }
        }
      }
      &.icon3 {
        span {
          background: url(/Work/View/Tephone/images/bottom3.png) no-repeat center center;
          .p4r(background-size, 27, 27);
        }
        &.cur {
          span {
            background: url(/Work/View/Tephone/images/bottom3-active.png) no-repeat center center;
            .p4r(background-size, 27, 27);
          }
        }
      }
      &.icon4 {
        span {
          background: url(/Work/View/Tephone/images/bottom4.png) no-repeat center center;
          .p4r(background-size, 27, 27);
        }
        &.cur {
          span {
            background: url(/Work/View/Tephone/images/bottom4-active.png) no-repeat center center;
            .p4r(background-size, 27, 27);
          }
        }
      }
    }
  }
}

.footer-com() {
  .footer {
    display: flex;
    align-items: center;
    a {
      .p2r(font-size, 16);
      text-align: center;
    }
    .next {
      .p2r(font-size, 16);
      .p2r(width, 265);
      .p2r(height, 56);
      .p2r(line-height, 56);
      background-color: rgba(72, 117, 231, 1);
      color: #fff;
    }
    .goback {
      .p2r(font-size, 16);
      .p2r(width, 110);
      .p2r(height, 56);
      .p2r(line-height, 56);
      background-color: rgba(233, 239, 254, 1);
      color: rgba(102, 102, 102, 1);
    }
  }
}

//个人页面
.my-box {
  background-color: #fff;
  .p2r(padding-top, 20);
  .quit {
    float: right;
    padding-right: 10px;

    a {
      color: rgba(72, 117, 231, 1);
      .p2r(font-size, 14);

      em {
        display: inline-block;
        width: 20px;
        height: 20px;
        background-image: url(../images/quit.png);
        background-repeat: no-repeat;
        background-position: center center;
        background-size: 20px 20px;
        margin-left: 4px;
      }
    }
  }
  .form {
    .p2r(padding-bottom, 50);
  }
  .my-picture {
    .img-upload {
      position: relative;
      .p2r(width, 80);
      .p2r(height, 80);
      margin: 0px auto 20px;
      border-radius: 50%;
      background-color: #ddd;
      img {
        width: 100%;
        border-radius: 50%;
        height: 100%;
      }
    }
    .img-file {
      .p2r(width, 80);
      .p2r(height, 80);
      border-radius: 50%;
      position: absolute;
      top: 0;
      opacity: 0;
      z-index: 100;
    }
    .edit {
      position: absolute;
      .p2r(right, 0);
      .p2r(top, 8);
      .p2r(width, 16);
      .p2r(height, 16);
      background: url(../images/edit-1.png) no-repeat center center;
      .p4r(background-size, 16, 16);
      vertical-align: middle;
    }
  }
  .my-info {
    .p2r(padding-left, 10);
    .p2r(padding-right, 10);
    .form-group {
      label {
        .p2r(font-size, 12);
        color: rgba(153, 153, 153, 1);
        display: block;
        .p2r(margin-bottom, 5);
      }
      .ipt {
        .p2r(font-size, 14);
        .p2r(width, 355);
        .p2r(height, 38);
        font-weight: 600;
        color: rgba(51, 51, 51, 1);
        .p2r(margin-bottom, 15);
        background: rgba(250, 250, 250, 1);
        border: 1px solid rgba(238, 238, 238, 1);
        .p2r(padding-left, 10);
        .p2r(padding-right, 10);
        &:read-only {
          background: #fff;
          border: none;
          .p2r(padding-left, 0);
          .p2r(padding-right, 0);
        }
      }
      .modify {
        display: flex;
        justify-content: space-between;
        align-items: center;
        .p2r(width, 355);
        .p2r(height, 44);
        .p2r(line-height, 44);
        .p2r(padding-left, 10);
        .p2r(font-size, 16);
        background: rgba(243, 246, 255, 1);
        color: rgba(51, 51, 51, 1);
        .right-arrow {
          display: inline-block;
          .p2r(width, 32);
          .p2r(height, 18);
          background: url(../images/down.png) no-repeat center center;
          .p4r(background-size, 16, 9);
          transform: rotate(-90deg);
        }
      }
    }
  }
  .action {
    .p2r(padding-top, 40);
    .p2r(padding-bottom, 40);
    text-align: center;
    .btn {
      // display:inline-block;
      .p2r(width, 148);
      .p2r(height, 44);
      background: rgba(72, 117, 231, 1);
      border-radius: 23px;
      .p2r(font-size, 16);
      color: #fff;
    }
  }
}

//消息
.c-blue {
  color: rgba(72, 117, 231, 1);
}

.c-black {
  color: rgba(153, 153, 153, 1);
}

.messages-box {
  .message-lists {
    .p2r(margin-top, 70);
    .p2r(padding-bottom, 56);
    .items {
      .i-first {
        .p2r(width,355);
        background: rgba(255, 255, 255, 1);
        box-shadow: 0px 12px 20px 3px rgba(0, 0, 0, 0.08);
        .p2r(border-radius,8);
        .p2r(margin-left,10);
        .p2r(margin-right,10);
        .p2r(margin-bottom,20);
        a {
          width: 100%;
          display: inline-block;
          position: relative;
          .p8r(padding,15,15,15,20);
          .tits {
            display: flex;
            justify-content: space-between;
            .tit {
              .p2r(font-size,16);
              font-weight: bold;
              color: rgba(51, 51, 51, 1);
              .p2r(margin-bottom,10);
            }
            .goTime {
              .p2r(font-size,12);
            }
          }
          .see {
            .p2r(font-size,12);
            color: rgba(51, 51, 51, 1);
            .p2r(padding-bottom,10);
          }
          .dot {
            position: absolute;
            .p2r(left,6);
            .p2r(top,6);
            .p2r(width,9);
            .p2r(height,9);
            background: rgba(250, 190, 0, 1);
            border-radius: 50%;
          }
        }
      }
    }
  }
}

.tabs {
  width: 100%;
  .p2r(height,50);
  .p2r(line-height,50);
  background: rgba(255, 255, 255, 1);
  box-shadow: 0px 12px 20px 3px rgba(0, 0, 0, 0.08);
  .p2r(margin-bottom,20);
  position: fixed;
  top: 0;
  left: 0;
  z-index: 100;
  ul {
    display: flex;
    align-items: center; // justify-content: space-around;
    li {
      flex: 1;
      text-align: center;
      a {
        display: inline-block;
        width: 100%;
        .p2r(height,50);
        .p2r(line-height,50);
        color: rgba(191, 191, 191, 1);
        .p2r(font-size,12);
      }
      &.cur {
        a {
          position: relative;
          font-weight: bold;
          color: rgba(72, 117, 231, 1);
          .p2r(font-size,14);
          &:after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            .p2r(width,20);
            .p2r(height,8);
            background: rgba(72, 117, 231, 1);
            border-radius: 5px;
          }
        }
      }
    }
  }
}

//学校公告
.schoolAnnounce-box{
  .schoolAnnounce-lists {
    .p2r(padding-bottom, 56);
    .p2r(margin-top, 70);
    .items {
      .i-first {
        .p2r(width,355);
        background: rgba(255, 255, 255, 1);
        box-shadow: 0px 12px 20px 3px rgba(0, 0, 0, 0.08);
        .p2r(border-radius,8);
        .p2r(margin-left,10);
        .p2r(margin-right,10);
        .p2r(margin-bottom,20);
        a {
          width: 100%;
          display: inline-block;
          position: relative;
          .p8r(padding,15,15,15,20);
          .tits {
            display: flex;
            justify-content: space-between;
            .tit {
              .p2r(font-size,16);
              font-weight: bold;
              color: rgba(51, 51, 51, 1);
              .p2r(margin-bottom,10);
              overflow: hidden;
              text-overflow:ellipsis;//文本溢出显示省略号
              white-space:nowrap;//文本不会换行（单行文本溢出）
              // width:130px;
            }
          }
          .personnel{
            display: flex;
            .see {
              .p2r(font-size,12);
              color: rgba(51, 51, 51, 1);
              .p2r(padding-bottom,10);
              .p2r(margin-right,24);
            }
            .edition{
              .p2r(font-size,12);
              color:rgba(153,153,153,1);
              .time{}
            }
          }
          .dot {
            position: absolute;
            .p2r(left,6);
            .p2r(top,6);
            .p2r(width,9);
            .p2r(height,9);
            background: rgba(250, 190, 0, 1);
            border-radius: 50%;
          }
        }
      }
    }
  }
}
//学校通知弹窗
.announce-pop {
  width: 100%;
  .p2r(height,490);
  position: fixed;
  bottom: 0;
  background: rgba(255, 255, 255, 1);
  border-radius: 20px 20px 0px 0px;
  z-index: 101;
  .p8r(padding, 10,10,10,10);
  .close {
    position: absolute;
    .p2r(top,15);
    .p2r(right,15);
    a {
      display: inline-block;
      .p2r(width,30);
      .p2r(height,30);
      background-image: url('../images/close.png');
      background-repeat: no-repeat;
      background-position: center center;
      .p4r(background-size,20,20);
    }
  }
  .announce{
    .p2r(margin-bottom,25);
    text-align: center;
      .title {
      text-align: center;
      .p2r(margin-top,10);
      .p2r(margin-bottom,8);
      font-weight:600;
      color:rgba(51,51,51,1);
    }
    .person{
      color:rgba(51,51,51,1);
      .p2r(font-size,12);
      .p2r(margin-bottom,6);
    }
    .time{
      .p2r(font-size,12);
      color:rgba(153,153,153,1);
    }
  }
  .img{
    width:100%;
    img{}
  }
  .article{
    .p2r(margin-top,15);
    .p2r(margin-bottom,20);
    .p2r(font-size,12);
    .p2r(line-height,17);
    color:rgba(51,51,51,1);
  }
  .download{
    margin-bottom: 20px;
    a{
      display: block;
      .p2r(font-size, 12);
      margin-bottom: 5px;
      .loadName{
        color:rgba(51,51,51,1);
        .p2r(margin-right,30);
      }
      .btn-load{
        font-weight:600;
        color:rgba(72,117,231,1);
      }
    }
  }
}

//搜索空白页
.blank-box{
  color:rgba(153,153,153,1);
  text-align: center;
  .img{
    width:189px;
    margin: 0 auto;
    .p2r(padding-top,120);
    .p2r(padding-bottom,15);
    img{
      width:100%;
    }
  }
  p{
    .p2r(font-size,20);
  }
}
//正在建设中空白页
.buliding-box{
  color:rgba(153,153,153,1);
  text-align: center;
  .img{
    .p2r(width,321);
    margin: 0 auto;
    .p2r(padding-top,30);
    .p2r(padding-bottom,15);
    img{
      width:100%;
    }
  }
  .no-content{
    .p2r(font-size,21);
    color:rgba(102,102,102,1);
  }
  .wait{
    .p2r(font-size,16);
    color:rgba(153,153,153,1);
    .p2r(margin-top,10);
    .p2r(margin-bottom,24);
  }
  .goback{
    display: block;
    .p2r(width,126);
    .p2r(height,50);
    .p2r(line-height,50);
    background:rgba(255,255,255,1);
    box-shadow:0px 12px 20px 3px rgba(0,0,0,0.08);
    border-radius:100px;
    color:rgba(27,53,84,1);
    .p2r(font-size,17);
    margin:0 auto;
  }
}
//考勤状况
.attend-box{
  background:rgba(235,240,255,1);
  .p2r(padding-top,15);
  .p2r(padding-bottom,48);
  .time-change{
    text-align: center;
    .p2r(padding-bottom,28);
    .arrow{
      display: inline-block;
      .p2r(width,44);
      .p2r(height,44);
    }
    .arrow-left{
      background: url(../images/time-left.png) no-repeat center center;
      .p4r(background-size, 44, 44);
      vertical-align: middle;
    }
    .arrow-right{
      background: url(../images/time-right.png) no-repeat center center;
      .p4r(background-size, 44, 44);
      vertical-align: middle;
    }
    .detail-time{
      color:rgba(51,51,51,1);
      .p2r(font-size,12);
      .p2r(padding-left,12);
      .p2r(padding-right,12);
      .time{}
      .week{}
    }
  }
  .time-tabs{
    text-align: center;
    a{
      display: inline-block;
      .p2r(font-size,14);
      color:rgba(51,51,51,1);
      background-color: #fff;
      .p8r(padding,12,20,12,20);
      border-radius:17px;
      .p2r(margin-right,12);
      &.cur{
        .p8r(padding,12,60,12,60);
        color: #fff;
        background:rgba(72,117,231,1);
        box-shadow:0px 6px 10px 3px rgba(72,117,231,0.31);
      }
      &::last-child{
        margin-right: 0;
      }
    }
  }
}
.attend-rate{
  display: flex;
  justify-content: center;
  background:rgba(255,255,255,1);
  border-radius:8px;
  border:1px solid #f1f1f1;
  width:90%;
  margin: 0 auto;
  position: relative;
  .p2r(margin-top,-26);
  .p8r(padding,15,20,15,20);
  li{
    .p2r(margin-right,20);
    text-align: center;
    .p2r(min-width,46);
    .num{
      display: block;
      .p8r(padding,12,8,12,8);
      background:rgba(245,245,245,1);
      border-radius:8px;
      font-weight:bold;
      .p2r(font-size,20);
    }
    .word{
      display: block;
      .p2r(font-size,12);
      color:rgba(102,102,102,1);
      .p2r(margin-top,8);
    }
    &:last-child{
      margin-right:0;
    }
  }
}
.courseList{
  .p8r(padding,30,10,56,10);
  .title{
    color: #333333;
    font-weight:bold;
    .p2r(font-size,20);
    .p2r(margin-bottom,20);
    display: flex;
    justify-content: space-between;
    align-items: center;
    // .p2r(padding-top,10);
    // .p2r(padding-left,10);
    span{
      &:nth-child(2){
        color: #666666;
        .p2r(font-size,14);
        font-weight: 400;
      }
    }
  }
  .subjectList{
    .list{
      .p2r(margin-bottom,20);
      display: block;
      box-shadow: 0px 0px 20px 3px rgba(0, 0, 0, 0.1);
      border-radius: 8px;
      .flag{
        .p2r(font-size,12);
        .p2r(height,40);
        .p2r(line-height,40);
        border-radius:8px 8px 0px 0px;
        .p8r(padding,0,10,0,10);
      }
      .flag1{
        background:rgba(72,117,231,1);
        color: #fff;
      }
      .flag2{
        background:rgba(255,204,2,1);
        color:rgba(51,51,51,1);
      }
      .flag3{
        background:rgba(191,191,191,1);
        color: #fff;
      }
      .classInfo{
        .p8r(padding,15,10,15,10);
        background-color: #fff;
        h3{
          color:#333333;
          .p2r(font-size,16);
          font-weight: bold;
          .p2r(margin-bottom,15);
        }
        h4{
          color:#333333;
          .p2r(font-size,16);
          .p2r(margin-bottom,10);
        }
        .address{
          color: #666666;
          .p2r(font-size,12);
          .area{
            .p2r(margin-right,40);
            em{
              display: inline-block;
              .p2r(width,14);
              .p2r(height,16);
              background: url(../images/address.png) no-repeat center center;
              .p4r(background-size, 14, 16);
              vertical-align: middle;
              .p2r(margin-right,5);
            }
          }
          .bell{
            em{
              display: inline-block;
              .p2r(width,16);
              .p2r(height,16);
              background: url(/Work/View/Tephone/images/clock.png) no-repeat center center;
              .p4r(background-size, 16, 16);
              vertical-align: middle;
              .p2r(margin-right,5);
            }
          }
        }
      }
      .rating{
        .p2r(font-size,16);
        .p2r(height,34);
        .p2r(line-height,34);
        color: #4875E7;
        text-align: center;
        background:rgba(245,248,255,1);
        border-radius:0px 0px 8px 8px;
      }
    } 
  }
}
// 点名上课日历
.month-calendar{
  position: relative;
  height: 362px;
  #singleDateRange{
    
  }
  .dt{
    overflow: inherit;
    max-width: 600px;
    max-height: 520px;
    opacity: 1;
    display: block;
    border-radius: 0;
    top: 20px;
    left:12%;
    z-index:90;
  }
  .calendar-modal{
    display: block !important;
    top:20px !important;
    left: 13% !important;
    height: 340px !important;
  }
  .calendar-hd{
    height: 50px!important;
    line-height: 50px!important;
  }
}
//上课人数
.rollcall-box{
  .p8r(padding,20,0,40,0);
  .time-change{}
  .tit{
    .p2r(font-size,20);
  }
}
.attend-rate-box{
  width:90%;
  margin:0 auto;
  .attend-rate{
    width:100%;
    .p2r(margin-top,-50);
    border-radius: 8px 8px 0 0;
  }
  .period-box{
    color:rgba(153,153,153,1);
    .p2r(font-size,12);
    background:rgba(233,233,233,1);
    border-radius:0px 0px 8px 8px;
    text-align: center;
    .p8r(padding,10,0,10,0);
    em{
      display: inline-block;
      .p2r(width, 16);
      .p2r(height, 16);
      background: url(/Work/View/Tephone/images/clock.png) no-repeat center center;
      .p4r(background-size, 16, 16);
      vertical-align: middle;
      .p2r(margin-right, 10);
    }
  }
}
.studentInfo-box{
  .p2r(margin-top, 30);
  .p8r(padding,0,10,56,10);
  .clearfix();
  .item{
    float: left;
    width: 50%;
    .p8r(padding,0,10,0,10);
    .p2r(margin-bottom,20);
    .title{
      .p2r(height, 50);
      .p2r(line-height, 50);
      background:rgba(233,233,233,1);
      border-radius:8px 8px 0px 0px;
      .p8r(padding,0,10,0,10);
      .p2r(font-size,16);
      em{
        display: inline-block;
        .p2r(width, 12);
        .p2r(height, 12);
        background: url(/Work/View/Tephone/images/male.png) no-repeat center center;
        .p4r(background-size, 12, 12);
      }
    }
    .record-box{
      position: relative;
      background:rgba(255,255,255,1);
      box-shadow:0px 12px 20px 3px rgba(0,0,0,0.08);
      border-radius:8px;
      .p8r(padding,15,10,15,10);
      .p2r(height, 108);
      .phone{
        color:rgba(51,51,51,1);
        .p2r(font-size,12);
        .p2r(padding-bottom,12);
        em{
          display: inline-block;
          .p2r(width, 10);
          .p2r(height, 17);
          background: url(/Work/View/Tephone/images/phone.png) no-repeat center center;
          .p4r(background-size, 10, 17);
          vertical-align: middle;
          .p2r(margin-right,5);
        }
      }
      .type-box{
        .type{
          .p2r(font-size,12);
          em{
            display: inline-block;
            width:6px;
            height:6px;  
            border-radius: 50%;
            .p2r(margin-right,5);
          }
        }
        .type1{
          color: rgba(255,92,103,1);
          em{
             background:rgba(255,92,103,1);
          }
         
        }
        .type2{
          color: #FABE00;
          em{
             background:#FABE00;
          }
        }
        .type3{
          color: #50E3C2;
          em{
             background:#50E3C2;
          }
        }
      }
      .reason{
        width:100%;
        position: absolute;
        left: 0;
        bottom: 0;
        .p2r(font-size,12);
        .p2r(height, 30);
        .p2r(line-height, 30);
        background:rgba(245,248,255,1);
        color:rgba(51,51,51,1);
        border-radius:0px 0px 8px 8px;
        .p8r(padding,0,10,0,10);
        span{}
      }
    }
  }
}
//意向客户
.intent-box{
  position: relative;
  .p2r(padding-bottom, 50);
 .title{
  .p8r(padding,15,0,10,0);
  text-align: center;
  position: relative;
  z-index: 2;
   span{
     display: inline-block;
      background:rgba(145,169,252,1);
      box-shadow:0px 12px 20px 3px rgba(0,0,0,0.08);
      border-radius:19px;
      .p8r(padding,10,15,10,15);
      color: #fff;
      .p2r(font-size,14);
   }
  
 } 
 .clientNum{
   display: flex;
   justify-content: center;
   flex-direction: column;
   align-items: center;
   background-color: #fff;
   border-radius: 50%;
  .p2r(width, 180);
  .p2r(height, 180);
  background: url(../images/clientNum.png) no-repeat center center;
  .p4r(background-size, 180, 180);

  margin: 0 auto;
  position: relative;
  z-index: 2;
   .num{
    .p2r(font-size,44);
    color:rgba(68,111,220,1);
    .p2r(margin-bottom,5);
    .p2r(margin-top,-18);
   }
   .word{
    .p2r(font-size,12);
    color:rgba(68,111,220,1);
   }
 }
 .img{
   position: absolute;
   top:0;
   left:0;
   width:100%;
   height: 100%;
   z-index: 1;
   img{
    width:100%;
    height: 100%;
   }
 }
}
.customeBrief-box{
  width:90%;
  margin: 0 auto;
  border-radius:8px;
  background:rgba(255,255,255,1);
  box-shadow:0px 12px 20px 3px rgba(0,0,0,0.08);
  border-radius:8px;
  position: relative;
  z-index: 3;
  .p2r(margin-top,-55);
  .changes-box{
    background: rgba(245,245,245,1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-radius:8px 8px 0px 0px;
    .p2r(height,60);
    .p8r(padding,15,0,15,10);
    .brief{
      .p2r(font-size,12);
      color:rgba(153,153,153,1);
    }
    .brief-change{
      .change{
        display: inline-block;
        .p2r(font-size,14);
        color:#999999;
        background-color: #DDDDDD;
        .p8r(padding,12,20,12,20);
        border-radius:17px;
        .p2r(margin-right,12);
        &.cur{
          .p8r(padding,12,30,12,30);
          color: #fff;
          background:rgba(72,117,231,1);
          box-shadow:0px 6px 10px 3px rgba(72,117,231,0.31);
        }
      }
    }
  }
  .texts-box{
    display: flex;
    justify-content: center;
    align-items: center;
    .p8r(padding,15,0,15,0);
    .link{
      .p2r(margin-right,15);
      text-align:center;
      span{
        text-align: center;
        &:first-child{
          display: block;
          .p2r(width,50);
          .p2r(height,50);
          .p2r(line-height,50);
          color: #333333;
          .p2r(font-size,20);
          background:rgba(245,245,245,1);
          border-radius: 50%;
          .p2r(margin-bottom,8);
        }
        &:last-child{
          color:#666666;
          .p2r(font-size,12);
        }
      }
      &:last-child{
        .p2r(margin-right,0);
      }
    }
  }
}
//返回首页按钮
* { touch-action: pan-y; } 
.gobackIndex{
  position:fixed;
  .p2r(bottom,60);
  right:5px;
  width:80px;
  height:80px;
  z-index:54;
  a{
    display:block;
    width:100%;
    height:100%;
    img{
      width:100%;
      border-radius: 50%;
    }
  }
}
.features-sort1 {
  .features {
    li {
      a {
        .img-box {
          .p2r(width, 54);
          .p2r(height, 54);
          box-shadow: 0px 12px 20px 3px rgba(0, 0, 0, 0.08);
          .img {
            width: 100%;
            border-radius: 50%;
          }
        }
      }
    }
  }
}
//搜索
.sb-search {
  .p2r(padding-left, 10);
  .p2r(padding-right, 10);
  .p2r(margin-top, 10);
  .p2r(margin-bottom, 15);

  .search-box {
    position: relative;

    .search-icon {
      position: absolute;
      .p2r(left, 12);
      top: 50%;
      transform: translateY(-50%);
      // .p2r(top, 22);
      .p2r(width, 14);
      .p2r(height, 14);
      background: url(../images/search.png) no-repeat center center;
      .p4r(background-size, 14, 14);
    }

    .find {
      .p2r(width, 310);
      .p2r(height, 34);
      .p2r(font-size, 12);
      .p2r(padding-left, 40);
      font-weight: 400;
      color: #333;
      background: rgba(255, 255, 255, 1);
      box-shadow: 0px 12px 20px 3px rgba(0, 0, 0, 0.08);
      .p2r(border-radius, 19);
    }

    input::-webkit-input-placeholder {
      .p2r(font-size, 12);
      color: rgba(191, 191, 191, 1);
    }

    .beauty {
      .p2r(font-size, 16);
      color: rgba(72, 117, 231, 1);
      -webkit-appearance: none;
      background-color: #f8f8f8;
      .p2r(padding-left, 7);
    }
  }
}
//新增客户-搜索
.sb-search-add{
  .search-box{
    .find{
      .p2r(width, 214);
      margin-bottom: 0;
      border: none;
    }
    .comm{
      .p2r(width, 54);
      .p2r(height, 30);
      .p2r(line-height, 30);
      border-radius:20px;
      color:#fff;
      .p2r(font-size, 12);
      text-align: center;
    }
    .search{
      background:rgba(72,117,231,1);
      box-shadow:0px 6px 10px 3px rgba(72,117,231,0.31);
      padding-left:0;
      .p2r(margin-left, 5);
      padding:0 !important;
      margin-top: 1.3px;
    }
    .filter-box{
      display: inline-block;
      background:rgba(250,190,0,1);
      box-shadow:0px 6px 10px 3px rgba(250,190,0,0.27);
      .p2r(margin-left, 10);
    }
  }
}
.Love-box-cover(){
  .Love-box{
    .love{
      display: inline-block;
      .p2r(width, 18);
      .p2r(height, 14);
      background: url(/Work/View/Tephone/images/myLove.png) no-repeat center center;
      .p4r(background-size, 18, 14);
      &.cur{
        background: url(/Work/View/Tephone/images/myLove-cur.png) no-repeat center center;
      .p4r(background-size, 18, 14);
      }
    }
  }
}
//新增客户-列表
.addCustom{
  .p8r(padding, 15,10,56,10);
  .item{
    position: relative;
    background:rgba(255,255,255,1);
    box-shadow:0px 12px 20px 3px rgba(0,0,0,0.08);
    border-radius:8px;
    .p2r(margin-bottom,20);
    .myInfo{
      display: flex;
      align-items: center;
      .p8r(padding, 15,10,15,15);
      .img{
        .p2r(width, 50);
        .p2r(height, 50);
        .p2r(margin-right, 15);
        img{
          width: 100%;
          height: 100%;
          border-radius: 50%;
        }
      }
      .product{
        flex: 1;
        .title{
          .p2r(font-size,16);
          .p2r(margin-bottom,10);
          color:#333;
          em{
            display: inline-block;
            .p2r(width, 12);
            .p2r(height, 12);
            background: url(/Work/View/Tephone/images/male.png) no-repeat center center;
            .p4r(background-size, 12, 12);
          }
        }
        .Love-box-cover();
        
      }
    }
    .phoneAll{
      color: #666666;
      .p2r(font-size,12);
      .p8r(padding, 5,10,15,25);
      .area{
        .p2r(margin-right,40);
        em{
          display: inline-block;
          .p2r(width,18);
          .p2r(height,16);
          background: url(../images/myPhone.png) no-repeat center center;
          .p4r(background-size, 18, 16);
          vertical-align: middle;
          .p2r(margin-right,5);
        }
        a{
          color: #666666;
          .p2r(font-size,12);
        }
      }
      .bell{
        em{
          display: inline-block;
          .p2r(width,16);
          .p2r(height,17);
          background: url(../images/birth.png) no-repeat center center;
          .p4r(background-size, 16, 17);
          vertical-align: middle;
          .p2r(margin-right,5);
        }
      }
    }
    .follow{
      display: block;
      width:100%;
      .p2r(height,40);
      .p2r(line-height,40);
      text-align: center;
      background:#F5F8FF;
      border-radius:0px 0px 8px 8px;
      color: #446FDC;
      .p2r(font-size,14);
    }
    .news-box{
      position: absolute;
      left:0;
      top:0;
      .p2r(width,42);
      .p2r(height,44);
      background: url(../images/news.png) no-repeat center center;
      .p4r(background-size, 42, 44);
    }
    .followState{
      position: absolute;
      .p2r(top,18);
      .p2r(right,12);
      span{
        display: block;
        .p8r(padding,10,20,10,10);
        background:rgba(255,255,255,1);
        box-shadow:0px 6px 10px 3px rgba(0,0,0,0.08);
        border-radius:16px;
        color: #446FDC;
        .p2r(font-size,12);
        font-weight:550;
      }
    }
  }
}
//新增客户-筛选弹窗
.addCustom-prop{
  width:100%;
  height:100%;
  position: fixed;
  top:0;
  left:0;
  background:rgba(51,51,51,0.3);
  z-index:500;
  .level-box{
    position: fixed;
    top:0;
    right:0;
    .p2r(width,225);
    height:100%;
    background:rgba(255,255,255,1);
    .p8r(padding,20,20,20,20);
    h2{
      .p2r(font-size,20);
      font-weight:bold;
      color:rgba(51,51,51,1);
      .p2r(margin-bottom,30);
    }
    .IntenteLevel{
      .tit{
        .p2r(font-size,14);
        color: #333333;
        .p2r(margin-bottom,12);
      }
      .Love-box-cover();
    }
    .customState{
      h3{
        .p2r(font-size,14);
        color: #333333;
        .p2r(margin-top,30);
        .p2r(margin-bottom,12);
      }
      .state-sort{
       .state{
         display: inline-block;
        .p8r(padding,8,15,8,15);
        background:rgba(245,245,245,1);
        border-radius:16px;
        text-align: center;
        .p2r(margin-right,20);
        .p2r(margin-bottom,12);
        .p2r(font-size,12);
        color:#333333;
         &.cur{
           color: #fff;
          background:rgba(72,117,231,1);
          box-shadow:0px 6px 10px 3px rgba(72,117,231,0.31);
         }
         &:nth-child(2n){
          margin-right:0;
         }
       } 
      }
    }
  }
}
//跟进记录-头部搜索
.byDay-box{
  display: flex;
  justify-content: space-between;
  align-items: center;
  .p8r(padding,20,10,20,10);
  .select {
    .p2r(width, 70);
    .p2r(height, 34);
    .p2r(line-height, 34);
    .p2r(padding-left, 5);
    .p2r(padding-right, 5);
    .p2r(margin-left, 10);
    .p2r(margin-right, 10);
    background: #fff;
    .p2r(font-size, 12);
    // color: #fff;
    position: relative;
    z-index: 3;
    .p2r(border-radius, 20);
    .title {
      .p2r(height, 34);
      .p2r(line-height, 34);
      color:#333333;
      
    }
    &:after {
      content: "";
      position: absolute;
      right: 0;
      top: 50%;
      transform: translateY(-50%);
      .p2r(width, 13);
      .p2r(height, 9);
      background: url(../images/down-follow.png) no-repeat center center;
      .p4r(background-size, 13, 9);
    }
    .option {
      // width:100%;
      .p2r(width, 70);
      position: absolute;
      top: 100%;
      .p2r(left, 0);
      z-index: 4;
      box-shadow: 0px 12px 20px 3px rgba(0, 0, 0, 0.08);
      li {
        .p2r(height, 44);
        .p2r(line-height, 44);
        background: #fff;
        .p2r(padding-left, 10);
        .p2r(padding-right, 10);
        box-shadow: 0px 1px 0px 0px rgba(221, 221, 221, 1);
        border-bottom: 1px solid #ddd;
        color: rgba(51, 51, 51, 1);
        text-align: center;
        .p2r(font-size, 12);
        &:last-child {
          border-bottom: 0;
          box-shadow: none;
        }
        &.cur{
          color: #446FDC;
        }
      }
    }
  }
  .specificDate{
    display: flex;
    align-items: center;
    .one{
      position: relative;
      &:after {
        content: "";
        position: absolute;
        .p2r(right, 10);
        top: 50%;
        transform: translateY(-50%);
        .p2r(width, 13);
        .p2r(height, 13);
        background: url(../images/follow-cala.png) no-repeat center center;
        .p4r(background-size, 13, 13);
      }
      &:first-child{
        .p2r(margin-right, 6);
      }
    }
    .date{
      .p2r(width, 120);
      .p2r(height, 34);
      .p2r(line-height, 34);
      .p8r(padding, 0,15,0,10);
      box-shadow:0px 12px 20px 3px rgba(0,0,0,0.08);
      border-radius:19px;
      border:1px solid rgba(248,248,248,1);
      color:#333333;
      .p2r(font-size, 12);
    }
  }
}
//跟进记录-列表
.byDay-follow{
  .p8r(padding, 0,10,56,10);
  .item{
    position: relative;
    background:rgba(255,255,255,1);
    box-shadow:0px 12px 20px 3px rgba(0,0,0,0.08);
    border-radius:8px;
    .p2r(margin-bottom,20);
    .myInfo{
      display: flex;
      align-items: center;
      .p8r(padding, 15,10,15,10);
      .img{
        .p2r(width, 50);
        .p2r(height, 50);
        .p2r(margin-right, 15);
        img{
          width: 100%;
          height: 100%;
          border-radius: 50%;
        }
      }
      .product{
        flex: 1;
        .title{
          display: flex;
          justify-content: space-between;
          align-items: center;
          .p2r(font-size,17);
          .p2r(margin-bottom,10);
          .teach{
            font-weight:500;
          }
          .theWay{
            .p2r(font-size,12);
            em{
              display: inline-block;
              .p2r(width,6);
              .p2r(height,6);
              border-radius: 50%;
              .p2r(margin-right,5);
            }
            .telephone{
              color:rgba(250,190,0,1);
              .p2r(margin-right,15);
              em{
                background:rgba(250,190,0,1);
              }
            }
            .ordinary{
              color:#FF5C67;
              em{
                background:#FF5C67;
              }
            }
          }
        }
        
       .Love-box{
        color: #999999;
        .p2r(font-size,12);
       } 
      }
    }
    .phoneAll{
      .p8r(padding, 0,10,20,10);
      .areaAdd {
        display: flex;
      }
      .area{
        color:#BFBFBF;
        .p2r(font-size,12);
      
        span{

        }
        em{
          font-style: normal;
          color:#333333;
          .p2r(font-size,14);
          .p2r(padding-left,10);
          flex:1;
        }
      }
    }
    .follow{
      display: block;
      width:100%;
      .p2r(height,70);   
      background:rgba(245,246,250,1);
      border-radius:0px 0px 8px 8px;
      color: #677498;
      .p2r(font-size,12);
      .p8r(padding, 15,10,15,10);
      p{
        .p2r(line-height,20);
        overflow: hidden;
        text-overflow:ellipsis;//文本溢出显示省略号
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
      }
    }
    .edit{
      position: absolute;
      .p2r(top,60);
      .p2r(right,12);
      .p2r(width,30);
      .p2r(height,30);
      background: url(../images/follow-edit.png) no-repeat center center;
      .p4r(background-size, 30, 30);
    }
  }
  .item-info{
    position: relative;
    background:rgba(255,255,255,1);
    box-shadow:0px 12px 20px 3px rgba(0,0,0,0.08);
    border-radius:8px;
    .p2r(margin-bottom,20);
    .myInfo{
      display: flex;
      align-items: center;
      .p8r(padding, 15,10,15,25);
      .img{
        .p2r(width, 50);
        .p2r(height, 50);
        .p2r(margin-right, 15);
        img{
          width: 100%;
          height: 100%;
          border-radius: 50%;
        }
      }
      .product{
        flex: 1;
        .title{
          .p2r(font-size,16);
          .p2r(margin-bottom,10);
          color:#333;
          justify-content: flex-start !important;
          em{
            display: inline-block;
            .p2r(width, 12);
            .p2r(height, 12);
            background: url(/Work/View/Tephone/images/male.png) no-repeat center center;
            .p4r(background-size, 12, 12);
            margin-left:5px;
          }
        }
        .Love-box-cover();
      }
    }
    .phoneAll{
      color: #666666;
      .p2r(font-size,12);
      .p8r(padding, 5,10,15,25);
      .area{
        .p2r(margin-right,40);
        em{
          display: inline-block;
          .p2r(width,18);
          .p2r(height,16);
          background: url(../images/myPhone.png) no-repeat center center;
          .p4r(background-size, 18, 16);
          vertical-align: middle;
          .p2r(margin-right,5);
        }
        a{
          color: #666666;
          .p2r(font-size,12);
        }
      }
      .bell{
        em{
          display: inline-block;
          .p2r(width,16);
          .p2r(height,17);
          background: url(../images/birth.png) no-repeat center center;
          .p4r(background-size, 16, 17);
          vertical-align: middle;
          .p2r(margin-right,5);
        }
      }
    }
    .follow{
      display: block;
      width:100%;
      .p2r(height,40);
      .p2r(line-height,40);
      text-align: center;
      background:#F5F8FF;
      border-radius:0px 0px 8px 8px;
      color: #446FDC;
      .p2r(font-size,14);
    }
    .news-box{
      position: absolute;
      left:0;
      top:0;
      .p2r(width,42);
      .p2r(height,44);
      background: url(../images/news.png) no-repeat center center;
      .p4r(background-size, 42, 44);
    }
    .followState{
      position: absolute;
      .p2r(top,18);
      .p2r(right,12);
      span{
        display: block;
        .p8r(padding,10,20,10,10);
        background:rgba(255,255,255,1);
        box-shadow:0px 6px 10px 3px rgba(0,0,0,0.08);
        border-radius:16px;
        color: #446FDC;
        .p2r(font-size,12);
        font-weight:550;
      }
    }
  }
}
//跟进记录-选择时间插件-样式修改
.rolldate-container .rolldate-btn{
  line-height: 60px !important;
}
.rolldate-container .rolldate-wrapper li{
  line-height: 36px !important;
}

//跟进记录- 底部添加跟进
.footer-addFollow{
  width:100%;
  position: fixed;
  bottom: 0;
  left: 0;
  background: rgba(255, 255, 255, 1);
  box-shadow: 0px -12px 20px 3px rgba(0, 0, 0, 0.08);
  z-index: 99;
  a{
    display: block;
    width:100%;
    .p2r(height,56);
    .p2r(line-height,56);
    text-align: center;
    color: #fff;
    .p2r(font-size,16);
    background-color: #4875E7;
  }
}
//添加跟进,底部弹窗
.swiper-box-prop{
  // display: none;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.4);
  z-index: 100;
  .place{
    .p2r(height, 50);
    .p2r(line-height, 50);
    background:rgba(245,248,255,1);
    box-shadow:0px 1px 0px 0px rgba(238,238,238,1);
    text-align: center;
    .p2r(border-top-left-radius, 6);
    .p2r(border-top-right-radius, 6);
    color: #333333;
    .p2r(font-size, 16);
  }
  .scroll-choose{
    width: 100%;
    position: fixed;
    bottom: 0;
    left: 0;
    .p2r(font-size, 16);
    .p2r(border-top-left-radius, 6);
    .p2r(border-top-right-radius, 6);
    background-color: #fff;
  }
  .swiper-box {
    .p2r(height, 220);
    .p8r(padding, 10,0,10,0);
    position: relative;
    .line {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate3d(-50%, -50%, 0);
      width: 90%;
      .p2r(height, 35);
      border-radius: 4px;
      background-color: rgba(0,0,0,.1);
    }
    .swiper-container{
      height:100%;
      .swiper-wrapper{
        // .p2r(margin-top,-20);
        .siwper-item {
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
    }
  }
  
  .confirm {
    position: absolute;
    z-index:200;
    .p2r(top, 15);
    .p2r(right, 10);
    color:#446FDC;
  }
}
//添加跟进-底部按钮
.review-footer-box {
  width: 100%;
  position: fixed;
  bottom: 0;
  left: 0;
  box-shadow: 0px -12px 20px 3px rgba(0, 0, 0, 0.08);
  .footer-com();
}
.footer-com() {
  .footer {
    display: flex;
    align-items: center;

    a {
      .p2r(font-size, 16);
      text-align: center;
    }

    .next {
      .p2r(font-size, 16);
      .p2r(width, 265);
      .p2r(height, 56);
      .p2r(line-height, 56);
      background-color: rgba(72, 117, 231, 1);
      color: #fff;
    }

    .goback {
      .p2r(font-size, 16);
      .p2r(width, 110);
      .p2r(height, 56);
      .p2r(line-height, 56);
      background-color: rgba(233, 239, 254, 1);
      color: rgba(102, 102, 102, 1);
    }

    .gobacking {
      .p2r(font-size, 16);
      .p2r(width, 110);
      .p2r(height, 56);
      .p2r(line-height, 56);
      background-color: rgba(233, 239, 254, 1);
      color: rgba(102, 102, 102, 1);
    }
  }
}
//添加跟进-编辑的内容
.addRecord-box{
  .p2r(padding-bottom, 56);
  form{
    .p8r(padding, 15,10,15,10);
  }
  .basicInfo{
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: #333333;
    .p2r(font-size, 16);
    .p2r(margin-bottom, 15);
    font-weight: 600;
    .addInfo{
      .p2r(width,30);
      .p2r(height,30);
      background: url(/Work/View/Tephone/images/adds.png) no-repeat center center;
      .p4r(background-size, 30, 30);
      border-radius: 50%;
    }
  }
  .type-title{
    .p2r(font-size, 15);
    color:rgba(102,102,102,1);
    .p2r(margin-bottom, 8);
  }
  .start{
    position: relative;
    &::before{
      content: '*';
      font-size: 14px;
      color:#FF5C67;
      position: absolute;
      left:0;
      top:50%;
      transform: translateY(-50%);
    }
  }
  input::-webkit-input-placeholder,
  textarea::-webkit-input-placeholder {
    color: #BFBFBF;
    .p2r(font-size, 16);
  }
  .choose-ipt{
    width:100%;
    .ipt{
      width:100%;
      .p2r(height, 40);
      .p2r(line-height, 40);
      background:rgba(255,255,255,1);
      border:1px solid rgba(221,221,221,1);
      .p8r(padding, 0,10,0,10);
      .p2r(font-size, 16);
      color: #333;
    }
  }
  .choose-radio{ 
    .exchange-box{
      display: flex;
      flex-wrap: wrap;
    }
    .exchange{
      .p2r(margin-right, 30);
      .p2r(margin-bottom, 15);
      .p2r(font-size, 16);
      .radio{
        .p2r(width, 20);
        .p2r(height, 20);
        background-image: url('/Work/View/Tephone/images/radio.png');
        background-repeat: no-repeat;
        background-position: center center;
        vertical-align: middle;
        .p4r(background-size, 20, 20);
        -webkit-appearance: none;
        border-radius: 50%;
        &:checked{
          background-image: url('/Work/View/Tephone/images/radio-active.png');
          .p4r(background-size, 20, 20);
        }
      }
      .label{
        .p2r(font-size, 16);
        color: #333;
      }
      &:last-child{
        margin-right: 0;
      }
    }
  }
  .loveLevel{
    .Love-box{
      .love{
        display: inline-block;
        .p2r(width, 27);
        .p2r(height, 21);
        background: url(/Work/View/Tephone/images/myLove.png) no-repeat center center;
        .p4r(background-size, 27, 21);
        &.cur{
          background: url(/Work/View/Tephone/images/myLove-cur.png) no-repeat center center;
          .p4r(background-size, 27, 21);
        }
      }
    }
  }
  .area-box{
    width: 100%;
    .area{
      width: 100%;
      .p8r(padding, 10,10,10,10);
      .p2r(height, 110);
      background:rgba(255,255,255,1);
      border:1px solid rgba(221,221,221,1);
      .p2r(font-size, 16);
      color: #333;
    }
  }
  .follow-date{

  }
}
//添加客户
.addRecord-box1{
  .type-title{
    .p2r(padding-left, 8);
  }
  .contact{
    .p2r(padding-top, 15);
    .p2r(margin-bottom, 15);
    border-bottom: 1px solid #ddd;
   .main-contact{
     display: flex;
     align-items: center;
     justify-content: space-between;
     .p2r(margin-bottom, 10);
    .exchange{
      .p2r(margin-right, 30);
      .p2r(font-size, 16);
      color: #333;
      // .p2r(margin-bottom, 15);
      .p2r(font-size, 16);
      .radio{
        .p2r(width, 20);
        .p2r(height, 20);
        background-image: url('/Work/View/Tephone/images/radio.png');
        background-repeat: no-repeat;
        background-position: center center;
        vertical-align: middle;
        .p4r(background-size, 20, 20);
        -webkit-appearance: none;
        border-radius: 50%;
        &:checked{
          background-image: url('/Work/View/Tephone/images/radio-active.png');
          .p4r(background-size, 20, 20);
        }
      }
      .label{
        .p2r(font-size, 15);
        color: #333;
      }
    }
    .delete{
      .p2r(width,30);
      .p2r(height,30);
      background: url(/Work/View/Tephone/images/delete.png) no-repeat center center;
      .p4r(background-size, 30, 30);
      border-radius: 50%;
    }
   } 
  }
}
.byDay-box{
  display: flex;
  justify-content: space-between;
  align-items: center;
  .p8r(padding,0,10,20,10);
  .select {
    .p2r(width, 80);
    .p2r(height, 34);
    .p2r(line-height, 34);
    .p2r(padding-left, 5);
    .p2r(padding-right, 5);
    .p2r(margin-left, 10);
    .p2r(margin-right, 10);
    background: #fff;
    .p2r(font-size, 12);
    // color: #fff;
    position: relative;
    z-index: 3;
    .p2r(border-radius, 20);
    .title {
      .p2r(height, 34);
      .p2r(line-height, 34);
      color:#333333;
      
    }
    &:after {
      content: "";
      position: absolute;
      right: 0;
      top: 50%;
      transform: translateY(-50%);
      .p2r(width, 13);
      .p2r(height, 9);
      background: url(../images/down-follow.png) no-repeat center center;
      .p4r(background-size, 13, 9);
    }
    .option {
      // width:100%;
      .p2r(width, 80);
      position: absolute;
      top: 100%;
      .p2r(left, 0);
      z-index: 4;
      box-shadow: 0px 12px 20px 3px rgba(0, 0, 0, 0.08);
      li {
        .p2r(height, 44);
        .p2r(line-height, 44);
        background: #fff;
        .p2r(padding-left, 10);
        .p2r(padding-right, 10);
        box-shadow: 0px 1px 0px 0px rgba(221, 221, 221, 1);
        border-bottom: 1px solid #ddd;
        color: rgba(51, 51, 51, 1);
        text-align: center;
        .p2r(font-size, 12);
        &:last-child {
          border-bottom: 0;
          box-shadow: none;
        }
        &.cur{
          color: #446FDC;
        }
      }
    }
  }
  .specificDate{
    display: flex;
    align-items: center;
    margin: 0 auto;
    .one{
      position: relative;
      &:after {
        content: "";
        position: absolute;
        .p2r(right, 10);
        top: 50%;
        transform: translateY(-50%);
        .p2r(width, 13);
        .p2r(height, 13);
        background: url(../images/follow-cala.png) no-repeat center center;
        .p4r(background-size, 13, 13);
      }
      &:first-child{
        .p2r(margin-right, 6);
      }
    }
    .date{
      .p2r(width, 120);
      .p2r(height, 34);
      .p2r(line-height, 34);
      .p8r(padding, 0,15,0,10);
      box-shadow:0px 12px 20px 3px rgba(0,0,0,0.08);
      border-radius:19px;
      border:1px solid rgba(248,248,248,1);
      color:#333333;
      .p2r(font-size, 12);
    }
  }
}
//待跟进
.addCustom-add{
  .p2r(padding-top, 70);
}
//跟踪统计
.timeCount{
  display: flex;
  align-items: center;
  .p8r(padding, 70,10,20,10);
  // .p2r(padding-bottom, 56);
  .time{
    position: relative;
    .p2r(margin-right, 10);
    &:after {
      content: "";
      position: absolute;
      .p2r(right, 10);
      top: 50%;
      transform: translateY(-50%);
      .p2r(width, 13);
      .p2r(height, 13);
      background: url(../images/follow-cala.png) no-repeat center center;
      .p4r(background-size, 13, 13);
    }
    .ipt{
      .p2r(width, 120);
      .p2r(height, 34);
      .p2r(line-height, 34);
      .p8r(padding, 0,15,0,10);
      box-shadow:0px 12px 20px 3px rgba(0,0,0,0.08);
      border-radius:19px;
      border:1px solid rgba(248,248,248,1);
      color:#333333;
      .p2r(font-size, 12);
    }
  }
  .time1{
    .p2r(margin-right, 28);
    .ipt{
      .p2r(width, 78);
      .p2r(padding-left, 5);
      .p2r(padding-right, 5);
    }
  }
}
.timeCount1{
  display: flex;
  justify-content: space-between;
  .p2r(padding-top, 15);
  .time{
    .ipt{
       .p2r(width, 160);
    }
  }
}
.track-box{
  .p8r(padding, 0,10,56,10);
  .item{
    background:rgba(255,255,255,1);
    box-shadow:0px 12px 20px 3px rgba(0,0,0,0.08);
    border-radius:8px;
    .p2r(margin-bottom, 20);
    .title{
      .p2r(height, 50);
      .p2r(line-height, 50);
      background:rgba(230,230,230,1);
      border-radius:8px 8px 0px 0px;
      font-weight:bold;
      color:rgba(51,51,51,1);
      .p8r(padding, 0,10,0,10);
    }
    .listOfName{
      .p8r(padding, 20,10,20,10);
      display: flex;
      align-items: center;
      justify-content: space-between;
      .list-left{
        p{
          span{
            display: inline-block;
            &:first-child{
              .p2r(width, 66);
              color: #999999;;
              .p2r(font-size, 12);
              text-align: right;
            }
            &:last-child{
              color:#333333;
              .p2r(font-size, 14);
              font-weight:bold;
            }
            
          }
        }
      }
      .list-right{
        display: flex;
        align-items: center;
        .ratings{
          .statistics{
            .p2r(width, 100);
            .p2r(height, 100);
          }
          .rate{
            color:#333333;
            .p2r(font-size, 12);
            text-align: center;
          }
        }
      }
    }
  }
}
// 跟进统计
.track-box-three .item-one .listOfName .list-right .ratings .statistics{
  .p2r(width, 350);
  .p2r(height, 180);
}
.track-box-three .item-two .listOfName .list-right .ratings .statistics{
  .p2r(width, 350);
  .p2r(height, 200);
}
//跟进记录-编辑
.SpringFestival-prop{
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  z-index: 102;
  .hot-box{
    .p2r(width, 340);
    position: absolute;
    top: 30%;
    left: 50%;
    transform: translateX(-50%);
    background-color: #fff;
    border-radius: 0.16rem;
    .p8r(padding, 20,20,20,20);
    .title{
      .p2r(font-size, 14);
      font-weight:600;
      color:rgba(51,51,51,1);
      .p2r(margin-bottom, 14);
    }
    .content-box{
      .area{
        .p2r(width, 300);
        .p2r(height, 156);
        .p2r(line-height, 20);
        background:rgba(248,247,247,1);
        border-radius:5px;
        border:1px solid rgba(221,221,221,1);
        .p2r(font-size, 14);
        color: #333;
        .p8r(padding, 15,15,15,15);
      }
    }
    .action{
      .p2r(margin-top, 20);
      a{
        display: inline-block;
        .p2r(font-size, 14);
        text-align: center;
      }
      .save{
        .p2r(width, 196);
        .p2r(height, 44);
        .p2r(line-height, 44);
        background:rgba(88,202,224,1);
        box-shadow:0px 5px 10px 3px rgba(0,0,0,0.08);
        border-radius:25px;
        font-weight:600;
        color: #fff;
      }
      .cancle{
        .p2r(width, 84);
        .p2r(height, 44);
        .p2r(line-height, 44);
        background:rgba(221,221,221,1);
        border-radius:25px;
        color: #666666;
      }
    }
  }
}
//请选择职务
.post-change-banner{
  width:100%;
  .p2r(height, 132);
  position: relative;
  .title{
    position: relative;
    .p2r(font-size, 20);
    color:rgba(248,248,248,1);
    z-index:2;
    text-align: center;
    .p2r(padding-top, 54);
  }
  img{
    width:100%;
    height:100%;
    position: absolute;
    left:0;
    top:0;
    z-index:1;
  }
}
.detail-post{
 .tab-post{
  .p8r(padding, 20,20,20,20);
   .postComm{
     display: inline-block;
    .p2r(width, 140);
    .p2r(height, 32);
    .p2r(line-height, 32);
    border:1px solid rgba(221,221,221,1);
    border-radius:4px;
    color: #BFBFBF;
    .p2r(font-size, 14);
    text-align: center;
      &.cur{
        background:rgba(72,117,231,1);
        color: #fff;
        border: none;
      }
   }
   .post-name{
    .p2r(margin-right, 40);
   }
 } 
 .tab-lists{
   .list{}
    .item{
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px dotted #DDDDDD;
      .p8r(padding, 10,10,10,10);
      .areas{
        em{
          display: inline-block;
          .p2r(width, 20);
          .p2r(height, 20);
          background-image: url('../images/radio.png');
          background-repeat: no-repeat;
          background-position: center center;
          vertical-align: middle;
          .p4r(background-size, 20, 20);
          border-radius: 50%;
        }
        span{
          display: inline-block;
          color: #333333;
          .p2r(font-size, 14);
          .p2r(padding-left,60);
        }
      }
      .supervise{
        color: #333333;
        .p2r(font-size, 14);
      }
      &.cur{
        .areas{
          em{
            background-image: url('../images/radio-active.png');
            .p4r(background-size, 20, 20);
          }
        }
      }
    }
 }
 .action{
  .p2r(width, 336);
    margin: 0 auto;
    .p2r(margin-top, 40);
   .confirm{
     display: block;
     .p2r(font-size, 14);
     .p2r(height, 40);
     .p2r(line-height, 40);
     background:#4875E7;
     border-radius:6px;
     color: #fff;
     .p2r(font-size, 14);
     text-align: center;
   }
 }
}
//滑动加载
.mui-table-view{
  // background-color: #efeff4 !important;
}

.schoolAnnounce-box-add .schoolAnnounce-lists .items .i-first a .tits .tit{
  overflow: auto;
  text-overflow: unset;
  white-space: normal;
  font-weight: normal;
}
.schoolAnnounce-box-add .schoolAnnounce-lists .items .i-first{
  box-shadow:none;
}

/*第十八种效果*/
.loading-style-18 {
  width: 1.33333333rem;
  height: 1.33333333rem;
  margin-top: -0.66666667rem;
  position: relative;
  /* top: 32%; */
  margin-left: auto;
  margin-right: auto;
  position: fixed;
  left: 50%;
  top: 50%;
  margin: -22px 0 0 -30px;
}

.loading-style-18 span {
  display: inline-block;
  width: 0.21333333rem;
  height: 0.21333333rem;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.7);
  opacity: 0.2;
  position: absolute;
  -webkit-animation: loading-style-18 1.04s ease infinite;
  animation: loading-style-18 1.04s ease infinite;
}

.loading-style-18 span:nth-child(1) {
  left: 0;
  top: 50%;
  margin-top: -0.10666667rem;
  -webkit-animation-delay: 0.13s;
  animation-delay: 0.13s;
}

.loading-style-18 span:nth-child(2) {
  left: 0.18666667rem;
  top: 0.18666667rem;
  -webkit-animation-delay: 0.26s;
  animation-delay: 0.26s;
}

.loading-style-18 span:nth-child(3) {
  left: 50%;
  top: 0;
  margin-left: -0.10666667rem;
  -webkit-animation-delay: 0.39s;
  animation-delay: 0.39s;
}

.loading-style-18 span:nth-child(4) {
  top: 0.18666667rem;
  right: 0.18666667rem;
  -webkit-animation-delay: 0.52s;
  animation-delay: 0.52s;
}

.loading-style-18 span:nth-child(5) {
  right: 0;
  top: 50%;
  margin-top: -0.10666667rem;
  -webkit-animation-delay: 0.65s;
  animation-delay: 0.65s;
}

.loading-style-18 span:nth-child(6) {
  right: 0.18666667rem;
  bottom: 0.18666667rem;
  -webkit-animation-delay: 0.78s;
  animation-delay: 0.78s;
}

.loading-style-18 span:nth-child(7) {
  bottom: 0;
  left: 50%;
  margin-left: -0.10666667rem;
  -webkit-animation-delay: 0.91s;
  animation-delay: 0.91s;
}

.loading-style-18 span:nth-child(8) {
  bottom: 0.18666667rem;
  left: 0.18666667rem;
  -webkit-animation-delay: 1.04s;
  animation-delay: 1.04s;
}

@-webkit-keyframes loading-style-18 {
  0% {
    opacity: 1;
  }

  100% {
    opacity: 0.2;
  }
}

@keyframes loading-style-18 {
  0% {
    opacity: 1;
  }

  100% {
    opacity: 0.2;
  }
}
// 新增的跟踪提醒相关样式
.Love-box-cover1(){
  .Love-box{
    .love{
      display: inline-block;
      .p2r(width, 18);
      .p2r(height, 14);
      background: url(../images/myLove.png) no-repeat center center;
      .p4r(background-size, 18, 14);
      &.cur{
        background: url(../images/myLove-cur.png) no-repeat center center;
      .p4r(background-size, 18, 14);
      }
    }
  }
}
.pt70{
  .p2r(padding-top, 70) !important;
}
.wait-follow{
  .p8r(padding, 15,10,56,10);
  .item{
    position: relative;
    background:rgba(255,255,255,1);
    box-shadow:0px 12px 20px 3px rgba(0,0,0,0.08);
    border-radius:8px;
    .p2r(margin-bottom,20);
  }
  .item-link{
    display: block;
    .myInfo{
      display: flex;
      align-items: center;
      .p8r(padding, 15,10,15,15);
      .img{
        .p2r(width, 50);
        .p2r(height, 50);
        .p2r(margin-right, 15);
        img{
          width: 100%;
          height: 100%;
          border-radius: 50%;
        }
      }
      .product{
        flex: 1;
        .title{
          display: flex;
          align-items: center;
          justify-content: space-between;
          .p2r(font-size,16);
          .p2r(margin-bottom,10);
          color:#333;
          .teach{

            .name{}
            .mark{
              display: inline-block;
              .p2r(width, 12);
              .p2r(height, 12);
              background: url(../images/male.png) no-repeat center center;
              .p4r(background-size, 12, 12);
            }
            .age{
              .p2r(font-size, 12);
              .p2r(margin-left, 4);
            }
          }
          .ordinary{
            .p2r(font-size, 12);
           
            em{
              display: inline-block;
              .p2r(width, 6);
              .p2r(height, 6);
              border-radius: 50%;
              .p2r(margin-right, 4);
             
            }
          }
          .ordinary1{
            color:rgba(255,92,103,1);
            em{
              background: #FF5C67;
            }
          }
          .ordinary2{
            color:#446FDC;
            em{
              background: #446FDC;
            }
          }
        }
        .follow-times{
          display: flex;
          align-items: center;
          justify-content: space-between;
          .Love-box-cover1();
          .recurrence{
            .p2r(font-size, 12);
            color:rgba(102,102,102,1);
          }
        }
      }
    }
    .phoneAll{
      .p8r(padding, 0,10,15,15);
      .p2r(font-size, 12);
      color:rgba(102,102,102,1);
      .area{}
      em{
        display: inline-block;
        .p2r(width,16);
        .p2r(height,16);
        background: url(../images/clock.png) no-repeat center center;
        .p4r(background-size, 16, 16);
        vertical-align: middle;
        .p2r(margin-right,5);
      }
    }
    .follow{
      display: block;
      width:100%;
      .p2r(height,40);
      .p2r(line-height,40);
      text-align: center;
      background:#F5F8FF;
      border-radius:0px 0px 8px 8px;
      color: #446FDC;
      .p2r(font-size,14);
    }
  }
}
//我的日程
.compont-calendar{
  .Calendar{}
  .month-top{
    .p2r(height,40);
    .p2r(line-height,40);
  }
  .calendarList-add{
    .p8r(padding,0,15,0,15);
    .p2r(margin-top,20);
    .p2r(margin-bottom,20);
    .event-list{
      padding: .8rem .4rem .4rem .4rem;
      background: rgba(255,255,255,1);
      box-shadow: 0px -1px 15px 0px rgba(0,0,0,0.08);
      border-radius: 8px;
    }
  }
  .month-box{
    display:flex;
    align-items: center;
    justify-content: center;
    .p2r(padding-top,10);
    .p2r(padding-bottom,10);
    .btn{
      display: inline-block;
      
      i{
        .p2r(width,44);
        .p2r(height,44);
        display: inline-block;
      }
      &.prev{
        .el-icon-arrow-left{
          background: url(../images/time-left.png) no-repeat center center;
          .p4r(background-size, 44, 44);
          vertical-align: middle; 
        }
      }
      &.next{
        .el-icon-arrow-right{
          background: url(../images/time-right.png) no-repeat center center;
          .p4r(background-size, 44, 44);
          vertical-align: middle;
        }
      }
    }
    .time{
      color: #333333;
      .p2r(margin-left,45);
      .p2r(margin-right,45);
      text-align: center;
      position: relative;
      .month{
        display: block;
        .p2r(font-size,24);
        .p2r(margin-bottom,5);
      }
      .year{
        .p2r(font-size,14);
      }
      #date-group1-1{
        opacity: 0;
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
      }
    }
  }
  .week-list{
    height: 35px;
    width: 100%;
    margin: 0 auto;
    color: #666;
    .p2r(font-size,16);
    .clearfix();
    li{
      float: left;
      width: 14%;
      text-align: center;
      line-height: 25px;
      list-style: none;
    }
  }
  #cm{
    .month-list{
      .clearfix();
      color: #333;
      width: 100%;
      margin: 0 auto;
      // margin-top: 10px;
      li{
        float: left;
        width: 14%;
        cursor: pointer;
        text-align: center;
        line-height: 25px;
        list-style: none;
        span{
          display: inline-block;
          position: relative;
          height: 20px;
          line-height: 20px;
          width: 30px;
        }
        .preDay{
          color: #ddd;
        }
        &.isevent{
          .unactive{
            position: relative;
            &:after{
              content: "";
              position: absolute;
              top: 18px;
              left: 45%;
              display: inline-block;
              width: 5px;
              height: 5px;
              border-radius: 50%;
              background: #446FDC;
            }
          }
          
        }
        .active{
          color: #fff;
          box-shadow:0px 6px 10px 3px rgba(72,117,231,0.31);
          border-radius:4px;
          background: #446FDC;
        }
      }
    }
  }
  .event-list{
    .p8r(padding,30,15,15,15);
    .list-tit{
      display: flex;
      align-content: center;
      justify-content: space-between;
      color: #333333;
      .p2r(font-size,16);
      .add{
        .p2r(width,25);
        .p2r(height,25);
        display: inline-block;
        background: url(../images/add-follow.png) no-repeat center center;
        .p4r(background-size, 25, 25);
        vertical-align: middle; 
      }
    }
    .event-box{
      .event-row{
        display: flex;
        justify-content: space-between;
        align-items: center;
        .p8r(padding,20,0,20,0);
        border-bottom: 1px dashed #DDDDDD;
        .detail-time{
          .p2r(margin-right,20);
          .p2r(font-size,14);
          .p2r(width,80);
        }
        .detail-event{
          .p2r(line-height,24);
          .p2r(font-size,16);
          flex: 1;
        }
        &:last-child{
          border-bottom: none;
        }
      }
    }
    
  }
}
//新增日程
.new-schedule{
  .tit{
    .p8r(padding,20,15,10,15);
    .p2r(font-size,24);
    color:rgba(51,51,51,1);
    font-weight: bold;
  }
  .program-box{
    background-color: #fff;
    .p8r(padding,0,15,0,15);
    .program{
      position: relative;
      display:flex;
      align-items: center;
      border-bottom: 1px solid #E5E5E5;
      &:after{
        content: '*';
        font-size: 14px;
        color: #FF5C67;
        position: absolute;
        left: 0;
        .p2r(top,20);
      }
      span{
        color: #000000;
        .p2r(font-size,14);
        flex:1;
        .p2r(padding-left,10);
        .p2r(padding-right, 20);
      }
      .describe{ 
        .p2r(margin-top, -100);
      }
      input::-webkit-input-placeholder,
      textarea::-webkit-input-placeholder {
        color: #B2B2B2;
        .p2r(font-size, 16);
      }
      .ipt{
        color: #000000;
        .p2r(width,250);
        .p2r(font-size, 16);
        .p8r(padding,15,0,15,0);
      }
      .textarea{
        color: #000000;
        .p2r(padding-bottom, 100);
      }
      &:last-child{
        border-bottom: none;
      }
    }
  }
  .action{
    text-align: center;
    .p2r(margin-top,40);
    .btn{
      width:355px;
      height:44px;
      background:rgba(72,117,231,1);
      border-radius:25px;
      color:#fff;
      .p2r(font-size,18);
    }
  }
}