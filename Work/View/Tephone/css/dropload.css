.dropload-up,.dropload-down{
    position: relative;
    height: 0;
    overflow: hidden;
    font-size: 12px;
    /* 开启硬件加速 */
    -webkit-transform:translateZ(0);
    transform:translateZ(0);
}
.dropload-down{
    height: 50px;
}
.dropload-refresh,.dropload-update,.dropload-load,.dropload-noData{
    height: 50px;
    line-height: 50px;
    text-align: center;
}
.dropload-load .loading{
    display: inline-block;
    height: 15px;
    width: 15px;
    border-radius: 100%;
    margin: 6px;
    border: 2px solid #666;
    border-bottom-color: transparent;
    vertical-align: middle;
    -webkit-animation: rotate 0.75s linear infinite;
    animation: rotate 0.75s linear infinite;
}
@-webkit-keyframes rotate {
    0% {
        -webkit-transform: rotate(0deg);
    }
    50% {
        -webkit-transform: rotate(180deg);
    }
    100% {
        -webkit-transform: rotate(360deg);
    }
}
@keyframes rotate {
    0% {
        transform: rotate(0deg);
    }
    50% {
        transform: rotate(180deg);
    }
    100% {
        transform: rotate(360deg);
    }
}