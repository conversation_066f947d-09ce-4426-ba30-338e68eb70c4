// utilities
// 预设字体大小
.fontSize-loop(@n, @i: 12) when (@i <= @n){
    .f@{i} {
        font-size: @i * 1px;
    }
    .fontSize-loop(@n,(@i + 1));
}
.fontSize-loop(22);
// 浮动
.float() {
    .fl {
        float: left;
        min-height: 1px;
    }
    .fr {
        float: right;
        min-height: 1px;
    }
    .clearfix {
        &::after {
        display: block;
        content: "";
        clear: both;
        }
    }
    .clear {
        clear: both;
    }
}
.float();
// 对齐方式
.align() {
    .tl {
        text-align: left;
    }
    .tr {
        text-align: right;
    }
    .tc {
        text-align: center;
    }
    .vm {
        vertical-align: middle;
    }
    .vt {
        vertical-align: top;
    }
    .vb {
        vertical-align: bottom;
    }
}
.align();
// display
.display() {
    .none {
        display: none;
    }
    .block {
        display: block;
    }
    .table {
        display: table;
    }
}
.display();
// margin paddng
.margin-padding-loop(@n, @step, @i: 5) when (@i <= @n){
    .m@{i}, .mt@{i}, .my@{i} {
        margin-top: @i * 1px;
    }
    .m@{i}, .ml@{i}, .mx@{i} {
        margin-left: @i * 1px;
    }
    .m@{i}, .mr@{i}, .mx@{i} {
        margin-right: @i * 1px;
    }
    .m@{i}, .mb@{i}, .my@{i} {
        margin-bottom: @i * 1px;
    }
    .p@{i}, .pt@{i}, .py@{i} {
        padding-top: @i * 1px;
    }
    .p@{i}, .pb@{i}, .py@{i} {
        padding-bottom: @i * 1px;
    }
    .p@{i}, .pl@{i}, .px@{i} {
        padding-left: @i * 1px;
    }
    .p@{i}, .pr@{i}, .px@{i} {
        padding-right: @i * 1px;
    }
    .margin-padding-loop(@n, @step, @i + @step);
}
.margin-padding-loop(20, 5);
.margin-padding-loop(30, 10, 30);

// bg colors
@colors: #01c2e1, #ec8586, #f6b460;
.colorset(@i: length(@colors)) when (@i > 0) {
    @name: extract(@colors, @i);
    .bc@{i} {
        background-color: @name;
    }
    .colorset((@i - 1));
}
// background color
.bg-white, .bg-f {
    background-color: #fff;
}
// border
.border-gray {
    border: 1px solid #ddd;
}
// elips
.elips {
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
}
// cursor
.cursor {
    cursor: pointer;
}
.box-shadow() {
    box-shadow: 1px 1px 1px rgba(0, 0, 0, .1)
}
.rad6() {
    border-radius: 6px;
}
// flex
.flex-ct() {
    display: flex;
    align-items:center;/*垂直居中*/
    justify-content: center;
}
.flex-vt() {
    display: flex;
    align-items:center;/*垂直居中*/
}