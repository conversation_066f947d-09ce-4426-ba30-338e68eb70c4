require.config({
  paths: {}
})
define([
  'require',
  'jquery'
], function (require, $) {
  $('body').on('touchstart', function(e) {
    var touch = e.originalEvent,
        startX = touch.changedTouches[0].pageX;
    startY = touch.changedTouches[0].pageY;
    $('body').on('touchmove', function(e) {
        touch = e.originalEvent.touches[0] ||
            e.originalEvent.changedTouches[0];
        if (touch.pageY - startY > 10) {
            console.log("下划");
            $('body').off('touchmove');
            $('.gobackIndex').hide();
        } else if (touch.pageY - startY < -10) {
            console.log("上划");
            $('body').off('touchmove');
            $('.gobackIndex').show();
        };
    });

    // Return false to prevent image
    // highlighting on Android
    return false;

}).on('touchend', function() {
    $('body').off('touchmove');
});

});