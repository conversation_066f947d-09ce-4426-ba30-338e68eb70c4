/**
 * @author: 鲜成 <PERSON>
 * @since: 2018-10-14
 * @description: doT 模板渲染 样板代码, 并指定了 第二个参数data 为数据接口
 */

require.config({
  paths:{
    doT: 'lib/doT.min'
  }
})
define([
  'require',
  'doT',
  'utils/i18n-render'
], function(require, doT, i18nExt) {
  return function(target, data, ctn) {
    var htmlTmpl,
        doTeval,
        html;
    
    htmlTmpl = getEle(target+'Tmpl').innerHTML;
    doTeval = doT.template(htmlTmpl);
    html = doTeval(data);
    getEle(!!ctn ? ctn : target).innerHTML = html;

    i18nExt.renderPopMto();
    //helper
    function getEle (ele) {
      return document.querySelector(ele);
    }
  };
});