/**
 * @author: 鲜成 <PERSON>
 * @since: 2018-10-14
 * @description: handlebars 模板渲染 样板代码, 并指定了 第二个参数data 为数据接口
 */

require.config({
  paths:{
    Handlebars: 'lib/handlebars.min'
  }
})
define([
  'require',
  'Handlebars'
], function(require, Handlebars) {
  return function(tmpl, data, ctn) {
    var htmlTmpl = getEle(tmpl).innerHTML,
        handlebarsEval = Handlebars.compile(htmlTmpl),
        html = handlebarsEval(data);
    getEle(ctn).innerHTML = html;

    //helper
    function getEle (ele) {
      return document.querySelector(ele);
    }
  };
});