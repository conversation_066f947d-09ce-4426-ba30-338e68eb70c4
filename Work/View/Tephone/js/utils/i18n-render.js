/**
 * @author: 鲜成(<PERSON><PERSON><PERSON>)
 * @since: 2018-10-14
 * @description: 通过页面上的【i18n-text/i18n-hd】属性和data-key查询i18n语言信息并输出到页面
 */

// language init
var blang = window.navigator.language,
    preferlang = localStorage.getItem('language'),
    lang = preferlang ? preferlang : blang,
    local = lang.toLocaleLowerCase();

require.config({
  paths: {
    i18n: 'lib/i18n'
  },
  config: {
    i18n: {
      locale: local
    }
  }
})
define([
  'require',
  'jquery',
  'i18n!nls/messages'
], function (require, $, i18n) {
  $("[i18n-text], [i18n-hd], [i18n-warn]").each(function () {
    var $t = $(this),
        isText = $t.is('[i18n-text]'),
        isHd = $t.is('[i18n-hd]'),
        keyChain = $t.data('key'),
        i18nVal = getI18nVal(keyChain);

    if (i18nVal) {
      if (isText) {
        $t.text(i18nVal);
      } else if(isHd){
        $t.attr('placeholder', i18nVal);
      } else {
        $t.data('warn', i18nVal);
      }
    }
  });

  // helper: i18n getter
  function getI18nVal(keyChain) {
    var keys = keyChain.split('::');
    return getVal(keys, i18n);
  }

  function renderPopMto() {
    $("#popMto [i18n-text], #popMto [i18n-wd],#popMto1 [i18n-text], #popMto1 [i18n-wd],#popMto2 [i18n-text], #popMto2 [i18n-wd],#popMto3 [i18n-text], #popMto3 [i18n-wd]").each(function () {
      var $t = $(this),
          isText = $t.is('[i18n-text]'),
          isWd = $t.is('[i18n-wd]'),
          keyChain = $t.data('key'),
          i18nVal = getI18nVal(keyChain);
  
      if (i18nVal) {
        if (isText) {
          $t.text(i18nVal);
        } else if (isWd) {
          $t.data('words', i18nVal);
        }
      }
    });
  }
  /** 
   * @description get Object deep value
   * @param {Array} keys = ['a', 'b', 'c']
   * @param {Object} deep = {a: {b: {c: 99}}}
   * @return deep[a][b][c] = 99
  */
  function getVal(keys, deep) {
    var key = keys.splice(0, 1)[0],
        inner = deep[key];
    if (keys.length > 0 && inner) {
      return getVal(keys, inner)
    } else {
      return inner;
    }
  };
  //exports
  return {
    locale: local,
    i18n: i18n,
    renderPopMto: renderPopMto
  };
});