require.config({
  paths: {
  }
})
define([
  'require',
  'jquery'
], function(require, $) {
  var messageHtml = suc(),
  $mes = $(messageHtml);

  $('body').append($mes);
  setTimeout(function() {
    $mes.remove();
    // cb && cb();
  }, 600);

  // helper
  function suc() {
    return '<div class="loading-style-18 loader-inner"><span></span><span></span><span></span><span></span><span></span><span></span><span></span><span></span></div>'
    // return '<div class="res-suc">'+info+'</div>'
  }

  // history back
  $('.goback').click(function() {
    history.go(-1);
  })
  return 'comon loaded.'
});