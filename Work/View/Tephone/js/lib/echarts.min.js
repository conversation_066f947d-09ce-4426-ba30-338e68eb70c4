!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e(t.echarts={})}(this,function(t){"use strict";function e(t){var e={},i={},n=t.match(/Firefox\/([\d.]+)/),r=t.match(/MSIE\s([\d.]+)/)||t.match(/Trident\/.+?rv:(([\d.]+))/),o=t.match(/Edge\/([\d.]+)/),a=/micromessenger/i.test(t);return n&&(i.firefox=!0,i.version=n[1]),r&&(i.ie=!0,i.version=r[1]),o&&(i.edge=!0,i.version=o[1]),a&&(i.weChat=!0),{browser:i,os:e,node:!1,canvasSupported:!!document.createElement("canvas").getContext,svgSupported:"undefined"!=typeof SVGRect,touchEventsSupported:"ontouchstart"in window&&!i.ie&&!i.edge,pointerEventsSupported:"onpointerdown"in window&&(i.edge||i.ie&&i.version>=11)}}function i(t){if(null==t||"object"!=typeof t)return t;var e=t,n=Fh.call(t);if("[object Array]"===n){e=[];for(var r=0,o=t.length;o>r;r++)e[r]=i(t[r])}else if(Nh[n]){var a=t.constructor;if(t.constructor.from)e=a.from(t);else{e=new a(t.length);for(var r=0,o=t.length;o>r;r++)e[r]=i(t[r])}}else if(!Rh[n]&&!P(t)&&!S(t)){e={};for(var s in t)t.hasOwnProperty(s)&&(e[s]=i(t[s]))}return e}function n(t,e,r){if(!w(e)||!w(t))return r?i(e):t;for(var o in e)if(e.hasOwnProperty(o)){var a=t[o],s=e[o];!w(s)||!w(a)||y(s)||y(a)||S(s)||S(a)||b(s)||b(a)||P(s)||P(a)?!r&&o in t||(t[o]=i(e[o],!0)):n(a,s,r)}return t}function r(t,e){for(var i=t[0],r=1,o=t.length;o>r;r++)i=n(i,t[r],e);return i}function o(t,e){for(var i in e)e.hasOwnProperty(i)&&(t[i]=e[i]);return t}function a(t,e,i){for(var n in e)e.hasOwnProperty(n)&&(i?null!=e[n]:null==t[n])&&(t[n]=e[n]);return t}function s(){return Bh||(Bh=Yh().getContext("2d")),Bh}function l(t,e){if(t){if(t.indexOf)return t.indexOf(e);for(var i=0,n=t.length;n>i;i++)if(t[i]===e)return i}return-1}function h(t,e){function i(){}var n=t.prototype;i.prototype=e.prototype,t.prototype=new i;for(var r in n)t.prototype[r]=n[r];t.prototype.constructor=t,t.superClass=e}function u(t,e,i){t="prototype"in t?t.prototype:t,e="prototype"in e?e.prototype:e,a(t,e,i)}function c(t){return t?"string"==typeof t?!1:"number"==typeof t.length:void 0}function d(t,e,i){if(t&&e)if(t.forEach&&t.forEach===Vh)t.forEach(e,i);else if(t.length===+t.length)for(var n=0,r=t.length;r>n;n++)e.call(i,t[n],n,t);else for(var o in t)t.hasOwnProperty(o)&&e.call(i,t[o],o,t)}function f(t,e,i){if(t&&e){if(t.map&&t.map===Xh)return t.map(e,i);for(var n=[],r=0,o=t.length;o>r;r++)n.push(e.call(i,t[r],r,t));return n}}function g(t,e,i,n){if(t&&e){if(t.reduce&&t.reduce===qh)return t.reduce(e,i,n);for(var r=0,o=t.length;o>r;r++)i=e.call(n,i,t[r],r,t);return i}}function p(t,e,i){if(t&&e){if(t.filter&&t.filter===Wh)return t.filter(e,i);for(var n=[],r=0,o=t.length;o>r;r++)e.call(i,t[r],r,t)&&n.push(t[r]);return n}}function v(t,e){var i=Gh.call(arguments,2);return function(){return t.apply(e,i.concat(Gh.call(arguments)))}}function m(t){var e=Gh.call(arguments,1);return function(){return t.apply(this,e.concat(Gh.call(arguments)))}}function y(t){return"[object Array]"===Fh.call(t)}function x(t){return"function"==typeof t}function _(t){return"[object String]"===Fh.call(t)}function w(t){var e=typeof t;return"function"===e||!!t&&"object"==e}function b(t){return!!Rh[Fh.call(t)]}function S(t){return"object"==typeof t&&"number"==typeof t.nodeType&&"object"==typeof t.ownerDocument}function M(t){return t!==t}function T(){for(var t=0,e=arguments.length;e>t;t++)if(null!=arguments[t])return arguments[t]}function I(t,e){return null!=t?t:e}function C(t,e,i){return null!=t?t:null!=e?e:i}function A(){return Function.call.apply(Gh,arguments)}function k(t){if("number"==typeof t)return[t,t,t,t];var e=t.length;return 2===e?[t[0],t[1],t[0],t[1]]:3===e?[t[0],t[1],t[2],t[1]]:t}function L(t,e){if(!t)throw new Error(e)}function D(t){t[Uh]=!0}function P(t){return t[Uh]}function O(t){t&&d(t,function(t,e){this.set(e,t)},this)}function z(t){return new O(t)}function B(){}function E(t,e){var i=new Kh(2);return null==t&&(t=0),null==e&&(e=0),i[0]=t,i[1]=e,i}function R(t){var e=new Kh(2);return e[0]=t[0],e[1]=t[1],e}function N(t,e,i){return t[0]=e[0]+i[0],t[1]=e[1]+i[1],t}function F(t,e,i){return t[0]=e[0]-i[0],t[1]=e[1]-i[1],t}function H(t){return Math.sqrt(V(t))}function V(t){return t[0]*t[0]+t[1]*t[1]}function W(t,e,i){return t[0]=e[0]*i,t[1]=e[1]*i,t}function G(t,e){var i=H(e);return 0===i?(t[0]=0,t[1]=0):(t[0]=e[0]/i,t[1]=e[1]/i),t}function X(t,e){return Math.sqrt((t[0]-e[0])*(t[0]-e[0])+(t[1]-e[1])*(t[1]-e[1]))}function q(t,e){return(t[0]-e[0])*(t[0]-e[0])+(t[1]-e[1])*(t[1]-e[1])}function Y(t,e,i){var n=e[0],r=e[1];return t[0]=i[0]*n+i[2]*r+i[4],t[1]=i[1]*n+i[3]*r+i[5],t}function U(t,e,i){return t[0]=Math.min(e[0],i[0]),t[1]=Math.min(e[1],i[1]),t}function Z(t,e,i){return t[0]=Math.max(e[0],i[0]),t[1]=Math.max(e[1],i[1]),t}function j(){this.on("mousedown",this._dragStart,this),this.on("mousemove",this._drag,this),this.on("mouseup",this._dragEnd,this),this.on("globalout",this._dragEnd,this)}function $(t,e){return{target:t,topTarget:e&&e.topTarget}}function K(t,e,i){return{type:t,event:i,target:e.target,topTarget:e.topTarget,cancelBubble:!1,offsetX:i.zrX,offsetY:i.zrY,gestureEvent:i.gestureEvent,pinchX:i.pinchX,pinchY:i.pinchY,pinchScale:i.pinchScale,wheelDelta:i.zrDelta,zrByTouch:i.zrByTouch,which:i.which}}function Q(){}function J(t,e,i){if(t[t.rectHover?"rectContain":"contain"](e,i)){for(var n,r=t;r;){if(r.clipPath&&!r.clipPath.contain(e,i))return!1;r.silent&&(n=!0),r=r.parent}return n?iu:!0}return!1}function te(){var t=new ou(6);return ee(t),t}function ee(t){return t[0]=1,t[1]=0,t[2]=0,t[3]=1,t[4]=0,t[5]=0,t}function ie(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4],t[5]=e[5],t}function ne(t,e,i){var n=e[0]*i[0]+e[2]*i[1],r=e[1]*i[0]+e[3]*i[1],o=e[0]*i[2]+e[2]*i[3],a=e[1]*i[2]+e[3]*i[3],s=e[0]*i[4]+e[2]*i[5]+e[4],l=e[1]*i[4]+e[3]*i[5]+e[5];return t[0]=n,t[1]=r,t[2]=o,t[3]=a,t[4]=s,t[5]=l,t}function re(t,e,i){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4]+i[0],t[5]=e[5]+i[1],t}function oe(t,e,i){var n=e[0],r=e[2],o=e[4],a=e[1],s=e[3],l=e[5],h=Math.sin(i),u=Math.cos(i);return t[0]=n*u+a*h,t[1]=-n*h+a*u,t[2]=r*u+s*h,t[3]=-r*h+u*s,t[4]=u*o+h*l,t[5]=u*l-h*o,t}function ae(t,e,i){var n=i[0],r=i[1];return t[0]=e[0]*n,t[1]=e[1]*r,t[2]=e[2]*n,t[3]=e[3]*r,t[4]=e[4]*n,t[5]=e[5]*r,t}function se(t,e){var i=e[0],n=e[2],r=e[4],o=e[1],a=e[3],s=e[5],l=i*a-o*n;return l?(l=1/l,t[0]=a*l,t[1]=-o*l,t[2]=-n*l,t[3]=i*l,t[4]=(n*s-a*r)*l,t[5]=(o*r-i*s)*l,t):null}function le(t){return t>su||-su>t}function he(t){this._target=t.target,this._life=t.life||1e3,this._delay=t.delay||0,this._initialized=!1,this.loop=null==t.loop?!1:t.loop,this.gap=t.gap||0,this.easing=t.easing||"Linear",this.onframe=t.onframe,this.ondestroy=t.ondestroy,this.onrestart=t.onrestart,this._pausedTime=0,this._paused=!1}function ue(t){return t=Math.round(t),0>t?0:t>255?255:t}function ce(t){return 0>t?0:t>1?1:t}function de(t){return ue(t.length&&"%"===t.charAt(t.length-1)?parseFloat(t)/100*255:parseInt(t,10))}function fe(t){return ce(t.length&&"%"===t.charAt(t.length-1)?parseFloat(t)/100:parseFloat(t))}function ge(t,e,i){return 0>i?i+=1:i>1&&(i-=1),1>6*i?t+(e-t)*i*6:1>2*i?e:2>3*i?t+(e-t)*(2/3-i)*6:t}function pe(t,e,i,n,r){return t[0]=e,t[1]=i,t[2]=n,t[3]=r,t}function ve(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t}function me(t,e){xu&&ve(xu,e),xu=yu.put(t,xu||e.slice())}function ye(t,e){if(t){e=e||[];var i=yu.get(t);if(i)return ve(e,i);t+="";var n=t.replace(/ /g,"").toLowerCase();if(n in mu)return ve(e,mu[n]),me(t,e),e;if("#"!==n.charAt(0)){var r=n.indexOf("("),o=n.indexOf(")");if(-1!==r&&o+1===n.length){var a=n.substr(0,r),s=n.substr(r+1,o-(r+1)).split(","),l=1;switch(a){case"rgba":if(4!==s.length)return void pe(e,0,0,0,1);l=fe(s.pop());case"rgb":return 3!==s.length?void pe(e,0,0,0,1):(pe(e,de(s[0]),de(s[1]),de(s[2]),l),me(t,e),e);case"hsla":return 4!==s.length?void pe(e,0,0,0,1):(s[3]=fe(s[3]),xe(s,e),me(t,e),e);case"hsl":return 3!==s.length?void pe(e,0,0,0,1):(xe(s,e),me(t,e),e);default:return}}pe(e,0,0,0,1)}else{if(4===n.length){var h=parseInt(n.substr(1),16);return h>=0&&4095>=h?(pe(e,(3840&h)>>4|(3840&h)>>8,240&h|(240&h)>>4,15&h|(15&h)<<4,1),me(t,e),e):void pe(e,0,0,0,1)}if(7===n.length){var h=parseInt(n.substr(1),16);return h>=0&&16777215>=h?(pe(e,(16711680&h)>>16,(65280&h)>>8,255&h,1),me(t,e),e):void pe(e,0,0,0,1)}}}}function xe(t,e){var i=(parseFloat(t[0])%360+360)%360/360,n=fe(t[1]),r=fe(t[2]),o=.5>=r?r*(n+1):r+n-r*n,a=2*r-o;return e=e||[],pe(e,ue(255*ge(a,o,i+1/3)),ue(255*ge(a,o,i)),ue(255*ge(a,o,i-1/3)),1),4===t.length&&(e[3]=t[3]),e}function _e(t,e){var i=ye(t);if(i){for(var n=0;3>n;n++)i[n]=0>e?i[n]*(1-e)|0:(255-i[n])*e+i[n]|0;return be(i,4===i.length?"rgba":"rgb")}}function we(t){var e=ye(t);return e?((1<<24)+(e[0]<<16)+(e[1]<<8)+ +e[2]).toString(16).slice(1):void 0}function be(t,e){if(t&&t.length){var i=t[0]+","+t[1]+","+t[2];return("rgba"===e||"hsva"===e||"hsla"===e)&&(i+=","+t[3]),e+"("+i+")"}}function Se(t,e){return t[e]}function Me(t,e,i){t[e]=i}function Te(t,e,i){return(e-t)*i+t}function Ie(t,e,i){return i>.5?e:t}function Ce(t,e,i,n,r){var o=t.length;if(1==r)for(var a=0;o>a;a++)n[a]=Te(t[a],e[a],i);else for(var s=o&&t[0].length,a=0;o>a;a++)for(var l=0;s>l;l++)n[a][l]=Te(t[a][l],e[a][l],i)}function Ae(t,e,i){var n=t.length,r=e.length;if(n!==r){var o=n>r;if(o)t.length=r;else for(var a=n;r>a;a++)t.push(1===i?e[a]:_u.call(e[a]))}for(var s=t[0]&&t[0].length,a=0;a<t.length;a++)if(1===i)isNaN(t[a])&&(t[a]=e[a]);else for(var l=0;s>l;l++)isNaN(t[a][l])&&(t[a][l]=e[a][l])}function ke(t,e,i){if(t===e)return!0;var n=t.length;if(n!==e.length)return!1;if(1===i){for(var r=0;n>r;r++)if(t[r]!==e[r])return!1}else for(var o=t[0].length,r=0;n>r;r++)for(var a=0;o>a;a++)if(t[r][a]!==e[r][a])return!1;return!0}function Le(t,e,i,n,r,o,a,s,l){var h=t.length;if(1==l)for(var u=0;h>u;u++)s[u]=De(t[u],e[u],i[u],n[u],r,o,a);else for(var c=t[0].length,u=0;h>u;u++)for(var d=0;c>d;d++)s[u][d]=De(t[u][d],e[u][d],i[u][d],n[u][d],r,o,a)}function De(t,e,i,n,r,o,a){var s=.5*(i-t),l=.5*(n-e);return(2*(e-i)+s+l)*a+(-3*(e-i)-2*s-l)*o+s*r+e}function Pe(t){if(c(t)){var e=t.length;if(c(t[0])){for(var i=[],n=0;e>n;n++)i.push(_u.call(t[n]));return i}return _u.call(t)}return t}function Oe(t){return t[0]=Math.floor(t[0]),t[1]=Math.floor(t[1]),t[2]=Math.floor(t[2]),"rgba("+t.join(",")+")"}function ze(t){var e=t[t.length-1].value;return c(e&&e[0])?2:1}function Be(t,e,i,n,r,o){var a=t._getter,s=t._setter,l="spline"===e,h=n.length;if(h){var u,d=n[0].value,f=c(d),g=!1,p=!1,v=f?ze(n):0;n.sort(function(t,e){return t.time-e.time}),u=n[h-1].time;for(var m=[],y=[],x=n[0].value,_=!0,w=0;h>w;w++){m.push(n[w].time/u);var b=n[w].value;if(f&&ke(b,x,v)||!f&&b===x||(_=!1),x=b,"string"==typeof b){var S=ye(b);S?(b=S,g=!0):p=!0}y.push(b)}if(o||!_){for(var M=y[h-1],w=0;h-1>w;w++)f?Ae(y[w],M,v):!isNaN(y[w])||isNaN(M)||p||g||(y[w]=M);f&&Ae(a(t._target,r),M,v);var T,I,C,A,k,L,D=0,P=0;if(g)var O=[0,0,0,0];var z=function(t,e){var i;if(0>e)i=0;else if(P>e){for(T=Math.min(D+1,h-1),i=T;i>=0&&!(m[i]<=e);i--);i=Math.min(i,h-2)}else{for(i=D;h>i&&!(m[i]>e);i++);i=Math.min(i-1,h-2)}D=i,P=e;var n=m[i+1]-m[i];if(0!==n)if(I=(e-m[i])/n,l)if(A=y[i],C=y[0===i?i:i-1],k=y[i>h-2?h-1:i+1],L=y[i>h-3?h-1:i+2],f)Le(C,A,k,L,I,I*I,I*I*I,a(t,r),v);else{var o;if(g)o=Le(C,A,k,L,I,I*I,I*I*I,O,1),o=Oe(O);else{if(p)return Ie(A,k,I);o=De(C,A,k,L,I,I*I,I*I*I)}s(t,r,o)}else if(f)Ce(y[i],y[i+1],I,a(t,r),v);else{var o;if(g)Ce(y[i],y[i+1],I,O,1),o=Oe(O);else{if(p)return Ie(y[i],y[i+1],I);o=Te(y[i],y[i+1],I)}s(t,r,o)}},B=new he({target:t._target,life:u,loop:t._loop,delay:t._delay,onframe:z,ondestroy:i});return e&&"spline"!==e&&(B.easing=e),B}}}function Ee(t,e,i,n){0>i&&(t+=i,i=-i),0>n&&(e+=n,n=-n),this.x=t,this.y=e,this.width=i,this.height=n}function Re(t){for(var e=0;t>=Ou;)e|=1&t,t>>=1;return t+e}function Ne(t,e,i,n){var r=e+1;if(r===i)return 1;if(n(t[r++],t[e])<0){for(;i>r&&n(t[r],t[r-1])<0;)r++;Fe(t,e,r)}else for(;i>r&&n(t[r],t[r-1])>=0;)r++;return r-e}function Fe(t,e,i){for(i--;i>e;){var n=t[e];t[e++]=t[i],t[i--]=n}}function He(t,e,i,n,r){for(n===e&&n++;i>n;n++){for(var o,a=t[n],s=e,l=n;l>s;)o=s+l>>>1,r(a,t[o])<0?l=o:s=o+1;var h=n-s;switch(h){case 3:t[s+3]=t[s+2];case 2:t[s+2]=t[s+1];case 1:t[s+1]=t[s];break;default:for(;h>0;)t[s+h]=t[s+h-1],h--}t[s]=a}}function Ve(t,e,i,n,r,o){var a=0,s=0,l=1;if(o(t,e[i+r])>0){for(s=n-r;s>l&&o(t,e[i+r+l])>0;)a=l,l=(l<<1)+1,0>=l&&(l=s);l>s&&(l=s),a+=r,l+=r}else{for(s=r+1;s>l&&o(t,e[i+r-l])<=0;)a=l,l=(l<<1)+1,0>=l&&(l=s);l>s&&(l=s);var h=a;a=r-l,l=r-h}for(a++;l>a;){var u=a+(l-a>>>1);o(t,e[i+u])>0?a=u+1:l=u}return l}function We(t,e,i,n,r,o){var a=0,s=0,l=1;if(o(t,e[i+r])<0){for(s=r+1;s>l&&o(t,e[i+r-l])<0;)a=l,l=(l<<1)+1,0>=l&&(l=s);l>s&&(l=s);var h=a;a=r-l,l=r-h}else{for(s=n-r;s>l&&o(t,e[i+r+l])>=0;)a=l,l=(l<<1)+1,0>=l&&(l=s);l>s&&(l=s),a+=r,l+=r}for(a++;l>a;){var u=a+(l-a>>>1);o(t,e[i+u])<0?l=u:a=u+1}return l}function Ge(t,e){function i(t,e){l[c]=t,h[c]=e,c+=1}function n(){for(;c>1;){var t=c-2;if(t>=1&&h[t-1]<=h[t]+h[t+1]||t>=2&&h[t-2]<=h[t]+h[t-1])h[t-1]<h[t+1]&&t--;else if(h[t]>h[t+1])break;o(t)}}function r(){for(;c>1;){var t=c-2;t>0&&h[t-1]<h[t+1]&&t--,o(t)}}function o(i){var n=l[i],r=h[i],o=l[i+1],u=h[i+1];h[i]=r+u,i===c-3&&(l[i+1]=l[i+2],h[i+1]=h[i+2]),c--;var d=We(t[o],t,n,r,0,e);n+=d,r-=d,0!==r&&(u=Ve(t[n+r-1],t,o,u,u-1,e),0!==u&&(u>=r?a(n,r,o,u):s(n,r,o,u)))}function a(i,n,r,o){var a=0;for(a=0;n>a;a++)d[a]=t[i+a];var s=0,l=r,h=i;if(t[h++]=t[l++],0!==--o){if(1===n){for(a=0;o>a;a++)t[h+a]=t[l+a];return void(t[h+o]=d[s])}for(var c,f,g,p=u;;){c=0,f=0,g=!1;do if(e(t[l],d[s])<0){if(t[h++]=t[l++],f++,c=0,0===--o){g=!0;break}}else if(t[h++]=d[s++],c++,f=0,1===--n){g=!0;break}while(p>(c|f));if(g)break;do{if(c=We(t[l],d,s,n,0,e),0!==c){for(a=0;c>a;a++)t[h+a]=d[s+a];if(h+=c,s+=c,n-=c,1>=n){g=!0;break}}if(t[h++]=t[l++],0===--o){g=!0;break}if(f=Ve(d[s],t,l,o,0,e),0!==f){for(a=0;f>a;a++)t[h+a]=t[l+a];if(h+=f,l+=f,o-=f,0===o){g=!0;break}}if(t[h++]=d[s++],1===--n){g=!0;break}p--}while(c>=zu||f>=zu);if(g)break;0>p&&(p=0),p+=2}if(u=p,1>u&&(u=1),1===n){for(a=0;o>a;a++)t[h+a]=t[l+a];t[h+o]=d[s]}else{if(0===n)throw new Error;for(a=0;n>a;a++)t[h+a]=d[s+a]}}else for(a=0;n>a;a++)t[h+a]=d[s+a]}function s(i,n,r,o){var a=0;for(a=0;o>a;a++)d[a]=t[r+a];var s=i+n-1,l=o-1,h=r+o-1,c=0,f=0;if(t[h--]=t[s--],0!==--n){if(1===o){for(h-=n,s-=n,f=h+1,c=s+1,a=n-1;a>=0;a--)t[f+a]=t[c+a];return void(t[h]=d[l])}for(var g=u;;){var p=0,v=0,m=!1;do if(e(d[l],t[s])<0){if(t[h--]=t[s--],p++,v=0,0===--n){m=!0;break}}else if(t[h--]=d[l--],v++,p=0,1===--o){m=!0;break}while(g>(p|v));if(m)break;do{if(p=n-We(d[l],t,i,n,n-1,e),0!==p){for(h-=p,s-=p,n-=p,f=h+1,c=s+1,a=p-1;a>=0;a--)t[f+a]=t[c+a];if(0===n){m=!0;break}}if(t[h--]=d[l--],1===--o){m=!0;break}if(v=o-Ve(t[s],d,0,o,o-1,e),0!==v){for(h-=v,l-=v,o-=v,f=h+1,c=l+1,a=0;v>a;a++)t[f+a]=d[c+a];if(1>=o){m=!0;break}}if(t[h--]=t[s--],0===--n){m=!0;break}g--}while(p>=zu||v>=zu);if(m)break;0>g&&(g=0),g+=2}if(u=g,1>u&&(u=1),1===o){for(h-=n,s-=n,f=h+1,c=s+1,a=n-1;a>=0;a--)t[f+a]=t[c+a];t[h]=d[l]}else{if(0===o)throw new Error;for(c=h-(o-1),a=0;o>a;a++)t[c+a]=d[a]}}else for(c=h-(o-1),a=0;o>a;a++)t[c+a]=d[a]}var l,h,u=zu,c=0,d=[];l=[],h=[],this.mergeRuns=n,this.forceMergeRuns=r,this.pushRun=i}function Xe(t,e,i,n){i||(i=0),n||(n=t.length);var r=n-i;if(!(2>r)){var o=0;if(Ou>r)return o=Ne(t,i,n,e),void He(t,i,n,i+o,e);var a=new Ge(t,e),s=Re(r);do{if(o=Ne(t,i,n,e),s>o){var l=r;l>s&&(l=s),He(t,i,i+l,i+o,e),o=l}a.pushRun(i,o),a.mergeRuns(),r-=o,i+=o}while(0!==r);a.forceMergeRuns()}}function qe(t,e){return t.zlevel===e.zlevel?t.z===e.z?t.z2-e.z2:t.z-e.z:t.zlevel-e.zlevel}function Ye(t,e,i){var n=null==e.x?0:e.x,r=null==e.x2?1:e.x2,o=null==e.y?0:e.y,a=null==e.y2?0:e.y2;e.global||(n=n*i.width+i.x,r=r*i.width+i.x,o=o*i.height+i.y,a=a*i.height+i.y);var s=t.createLinearGradient(n,o,r,a);return s}function Ue(t,e,i){var n=i.width,r=i.height,o=Math.min(n,r),a=null==e.x?.5:e.x,s=null==e.y?.5:e.y,l=null==e.r?.5:e.r;e.global||(a=a*n+i.x,s=s*r+i.y,l*=o);var h=t.createRadialGradient(a,s,0,a,s,l);return h}function Ze(){return!1}function je(t,e,i){var n=Yh(),r=e.getWidth(),o=e.getHeight(),a=n.style;return a.position="absolute",a.left=0,a.top=0,a.width=r+"px",a.height=o+"px",n.width=r*i,n.height=o*i,n.setAttribute("data-zr-dom-id",t),n}function $e(t){if("string"==typeof t){var e=Xu.get(t);return e&&e.image}return t}function Ke(t,e,i,n,r){if(t){if("string"==typeof t){if(e&&e.__zrImageSrc===t||!i)return e;var o=Xu.get(t),a={hostEl:i,cb:n,cbPayload:r};return o?(e=o.image,!Je(e)&&o.pending.push(a)):(!e&&(e=new Image),e.onload=Qe,Xu.put(t,e.__cachedImgObj={image:e,pending:[a]}),e.src=e.__zrImageSrc=t),e}return t}return e}function Qe(){var t=this.__cachedImgObj;this.onload=this.__cachedImgObj=null;for(var e=0;e<t.pending.length;e++){var i=t.pending[e],n=i.cb;n&&n(this,i.cbPayload),i.hostEl.dirty()}t.pending.length=0}function Je(t){return t&&t.width&&t.height}function ti(t,e){e=e||ju;var i=t+":"+e;if(qu[i])return qu[i];for(var n=(t+"").split("\n"),r=0,o=0,a=n.length;a>o;o++)r=Math.max($u(n[o],e).width,r);return Yu>Uu&&(Yu=0,qu={}),Yu++,qu[i]=r,r}function ei(t,e,i,n,r,o,a){return o?ni(t,e,i,n,r,o,a):ii(t,e,i,n,r,a)}function ii(t,e,i,n,r,o){var a=di(t,e,r,o),s=ti(t,e);r&&(s+=r[1]+r[3]);var l=a.outerHeight,h=ri(0,s,i),u=oi(0,l,n),c=new Ee(h,u,s,l);return c.lineHeight=a.lineHeight,c}function ni(t,e,i,n,r,o,a){var s=fi(t,{rich:o,truncate:a,font:e,textAlign:i,textPadding:r}),l=s.outerWidth,h=s.outerHeight,u=ri(0,l,i),c=oi(0,h,n);return new Ee(u,c,l,h)}function ri(t,e,i){return"right"===i?t-=e:"center"===i&&(t-=e/2),t}function oi(t,e,i){return"middle"===i?t-=e/2:"bottom"===i&&(t-=e),t}function ai(t,e,i){var n=e.x,r=e.y,o=e.height,a=e.width,s=o/2,l="left",h="top";switch(t){case"left":n-=i,r+=s,l="right",h="middle";break;case"right":n+=i+a,r+=s,h="middle";break;case"top":n+=a/2,r-=i,l="center",h="bottom";break;case"bottom":n+=a/2,r+=o+i,l="center";break;case"inside":n+=a/2,r+=s,l="center",h="middle";break;case"insideLeft":n+=i,r+=s,h="middle";break;case"insideRight":n+=a-i,r+=s,l="right",h="middle";break;case"insideTop":n+=a/2,r+=i,l="center";break;case"insideBottom":n+=a/2,r+=o-i,l="center",h="bottom";break;case"insideTopLeft":n+=i,r+=i;break;case"insideTopRight":n+=a-i,r+=i,l="right";break;case"insideBottomLeft":n+=i,r+=o-i,h="bottom";break;case"insideBottomRight":n+=a-i,r+=o-i,l="right",h="bottom"}return{x:n,y:r,textAlign:l,textVerticalAlign:h}}function si(t,e,i,n,r){if(!e)return"";var o=(t+"").split("\n");r=li(e,i,n,r);for(var a=0,s=o.length;s>a;a++)o[a]=hi(o[a],r);return o.join("\n")}function li(t,e,i,n){n=o({},n),n.font=e;var i=I(i,"...");n.maxIterations=I(n.maxIterations,2);var r=n.minChar=I(n.minChar,0);n.cnCharWidth=ti("国",e);var a=n.ascCharWidth=ti("a",e);n.placeholder=I(n.placeholder,"");for(var s=t=Math.max(0,t-1),l=0;r>l&&s>=a;l++)s-=a;var h=ti(i);return h>s&&(i="",h=0),s=t-h,n.ellipsis=i,n.ellipsisWidth=h,n.contentWidth=s,n.containerWidth=t,n}function hi(t,e){var i=e.containerWidth,n=e.font,r=e.contentWidth;if(!i)return"";var o=ti(t,n);if(i>=o)return t;for(var a=0;;a++){if(r>=o||a>=e.maxIterations){t+=e.ellipsis;break}var s=0===a?ui(t,r,e.ascCharWidth,e.cnCharWidth):o>0?Math.floor(t.length*r/o):0;t=t.substr(0,s),o=ti(t,n)}return""===t&&(t=e.placeholder),t}function ui(t,e,i,n){for(var r=0,o=0,a=t.length;a>o&&e>r;o++){var s=t.charCodeAt(o);r+=s>=0&&127>=s?i:n}return o}function ci(t){return ti("国",t)}function di(t,e,i,n){null!=t&&(t+="");var r=ci(e),o=t?t.split("\n"):[],a=o.length*r,s=a;if(i&&(s+=i[0]+i[2]),t&&n){var l=n.outerHeight,h=n.outerWidth;if(null!=l&&s>l)t="",o=[];else if(null!=h)for(var u=li(h-(i?i[1]+i[3]:0),e,n.ellipsis,{minChar:n.minChar,placeholder:n.placeholder}),c=0,d=o.length;d>c;c++)o[c]=hi(o[c],u)}return{lines:o,height:a,outerHeight:s,lineHeight:r}}function fi(t,e){var i={lines:[],width:0,height:0};if(null!=t&&(t+=""),!t)return i;for(var n,r=Zu.lastIndex=0;null!=(n=Zu.exec(t));){var o=n.index;o>r&&gi(i,t.substring(r,o)),gi(i,n[2],n[1]),r=Zu.lastIndex}r<t.length&&gi(i,t.substring(r,t.length));var a=i.lines,s=0,l=0,h=[],u=e.textPadding,c=e.truncate,d=c&&c.outerWidth,f=c&&c.outerHeight;u&&(null!=d&&(d-=u[1]+u[3]),null!=f&&(f-=u[0]+u[2]));for(var g=0;g<a.length;g++){for(var p=a[g],v=0,m=0,y=0;y<p.tokens.length;y++){var x=p.tokens[y],_=x.styleName&&e.rich[x.styleName]||{},w=x.textPadding=_.textPadding,b=x.font=_.font||e.font,S=x.textHeight=I(_.textHeight,ci(b));if(w&&(S+=w[0]+w[2]),x.height=S,x.lineHeight=C(_.textLineHeight,e.textLineHeight,S),x.textAlign=_&&_.textAlign||e.textAlign,x.textVerticalAlign=_&&_.textVerticalAlign||"middle",null!=f&&s+x.lineHeight>f)return{lines:[],width:0,height:0};x.textWidth=ti(x.text,b);var M=_.textWidth,T=null==M||"auto"===M;if("string"==typeof M&&"%"===M.charAt(M.length-1))x.percentWidth=M,h.push(x),M=0;else{if(T){M=x.textWidth;var A=_.textBackgroundColor,k=A&&A.image;k&&(k=$e(k),Je(k)&&(M=Math.max(M,k.width*S/k.height)))}var L=w?w[1]+w[3]:0;M+=L;var D=null!=d?d-m:null;null!=D&&M>D&&(!T||L>D?(x.text="",x.textWidth=M=0):(x.text=si(x.text,D-L,b,c.ellipsis,{minChar:c.minChar}),x.textWidth=ti(x.text,b),M=x.textWidth+L))}m+=x.width=M,_&&(v=Math.max(v,x.lineHeight))}p.width=m,p.lineHeight=v,s+=v,l=Math.max(l,m)}i.outerWidth=i.width=I(e.textWidth,l),i.outerHeight=i.height=I(e.textHeight,s),u&&(i.outerWidth+=u[1]+u[3],i.outerHeight+=u[0]+u[2]);for(var g=0;g<h.length;g++){var x=h[g],P=x.percentWidth;x.width=parseInt(P,10)/100*l}return i}function gi(t,e,i){for(var n=""===e,r=e.split("\n"),o=t.lines,a=0;a<r.length;a++){var s=r[a],l={styleName:i,text:s,isLineHolder:!s&&!n};if(a)o.push({tokens:[l]});else{var h=(o[o.length-1]||(o[0]={tokens:[]})).tokens,u=h.length;1===u&&h[0].isLineHolder?h[0]=l:(s||!u||n)&&h.push(l)}}}function pi(t){return(t.fontSize||t.fontFamily)&&[t.fontStyle,t.fontWeight,(t.fontSize||12)+"px",t.fontFamily||"sans-serif"].join(" ")||t.textFont||t.font}function vi(t,e){var i,n,r,o,a=e.x,s=e.y,l=e.width,h=e.height,u=e.r;0>l&&(a+=l,l=-l),0>h&&(s+=h,h=-h),"number"==typeof u?i=n=r=o=u:u instanceof Array?1===u.length?i=n=r=o=u[0]:2===u.length?(i=r=u[0],n=o=u[1]):3===u.length?(i=u[0],n=o=u[1],r=u[2]):(i=u[0],n=u[1],r=u[2],o=u[3]):i=n=r=o=0;var c;i+n>l&&(c=i+n,i*=l/c,n*=l/c),r+o>l&&(c=r+o,r*=l/c,o*=l/c),n+r>h&&(c=n+r,n*=h/c,r*=h/c),i+o>h&&(c=i+o,i*=h/c,o*=h/c),t.moveTo(a+i,s),t.lineTo(a+l-n,s),0!==n&&t.quadraticCurveTo(a+l,s,a+l,s+n),t.lineTo(a+l,s+h-r),0!==r&&t.quadraticCurveTo(a+l,s+h,a+l-r,s+h),t.lineTo(a+o,s+h),0!==o&&t.quadraticCurveTo(a,s+h,a,s+h-o),t.lineTo(a,s+i),0!==i&&t.quadraticCurveTo(a,s,a+i,s)}function mi(t){return yi(t),d(t.rich,yi),t}function yi(t){if(t){t.font=pi(t);var e=t.textAlign;"middle"===e&&(e="center"),t.textAlign=null==e||Qu[e]?e:"left";var i=t.textVerticalAlign||t.textBaseline;"center"===i&&(i="middle"),t.textVerticalAlign=null==i||Ju[i]?i:"top";var n=t.textPadding;n&&(t.textPadding=k(t.textPadding))}}function xi(t,e,i,n,r){n.rich?wi(t,e,i,n,r):_i(t,e,i,n,r)}function _i(t,e,i,n,r){var o=ki(e,"font",n.font||ju),a=n.textPadding,s=t.__textCotentBlock;(!s||t.__dirty)&&(s=t.__textCotentBlock=di(i,o,a,n.truncate));var l=s.outerHeight,h=s.lines,u=s.lineHeight,c=Ai(l,n,r),d=c.baseX,f=c.baseY,g=c.textAlign,p=c.textVerticalAlign;Si(e,n,r,d,f);var v=oi(f,l,p),m=d,y=v,x=Ti(n);if(x||a){var _=ti(i,o),w=_;a&&(w+=a[1]+a[3]);var b=ri(d,w,g);x&&Ii(t,e,n,b,v,w,l),a&&(m=Oi(d,g,a),y+=a[0])}ki(e,"textAlign",g||"left"),ki(e,"textBaseline","middle"),ki(e,"shadowBlur",n.textShadowBlur||0),ki(e,"shadowColor",n.textShadowColor||"transparent"),ki(e,"shadowOffsetX",n.textShadowOffsetX||0),ki(e,"shadowOffsetY",n.textShadowOffsetY||0),y+=u/2;var S=n.textStrokeWidth,M=Li(n.textStroke,S),T=Di(n.textFill);M&&(ki(e,"lineWidth",S),ki(e,"strokeStyle",M)),T&&ki(e,"fillStyle",T);for(var I=0;I<h.length;I++)M&&e.strokeText(h[I],m,y),T&&e.fillText(h[I],m,y),y+=u}function wi(t,e,i,n,r){var o=t.__textCotentBlock;(!o||t.__dirty)&&(o=t.__textCotentBlock=fi(i,n)),bi(t,e,o,n,r)}function bi(t,e,i,n,r){var o=i.width,a=i.outerWidth,s=i.outerHeight,l=n.textPadding,h=Ai(s,n,r),u=h.baseX,c=h.baseY,d=h.textAlign,f=h.textVerticalAlign;Si(e,n,r,u,c);var g=ri(u,a,d),p=oi(c,s,f),v=g,m=p;l&&(v+=l[3],m+=l[0]);var y=v+o;Ti(n)&&Ii(t,e,n,g,p,a,s);for(var x=0;x<i.lines.length;x++){for(var _,w=i.lines[x],b=w.tokens,S=b.length,M=w.lineHeight,T=w.width,I=0,C=v,A=y,k=S-1;S>I&&(_=b[I],!_.textAlign||"left"===_.textAlign);)Mi(t,e,_,n,M,m,C,"left"),T-=_.width,C+=_.width,I++;for(;k>=0&&(_=b[k],"right"===_.textAlign);)Mi(t,e,_,n,M,m,A,"right"),T-=_.width,A-=_.width,k--;for(C+=(o-(C-v)-(y-A)-T)/2;k>=I;)_=b[I],Mi(t,e,_,n,M,m,C+_.width/2,"center"),C+=_.width,I++;m+=M}}function Si(t,e,i,n,r){if(i&&e.textRotation){var o=e.textOrigin;"center"===o?(n=i.width/2+i.x,r=i.height/2+i.y):o&&(n=o[0]+i.x,r=o[1]+i.y),t.translate(n,r),t.rotate(-e.textRotation),t.translate(-n,-r)}}function Mi(t,e,i,n,r,o,a,s){var l=n.rich[i.styleName]||{},h=i.textVerticalAlign,u=o+r/2;"top"===h?u=o+i.height/2:"bottom"===h&&(u=o+r-i.height/2),!i.isLineHolder&&Ti(l)&&Ii(t,e,l,"right"===s?a-i.width:"center"===s?a-i.width/2:a,u-i.height/2,i.width,i.height);var c=i.textPadding;c&&(a=Oi(a,s,c),u-=i.height/2-c[2]-i.textHeight/2),ki(e,"shadowBlur",C(l.textShadowBlur,n.textShadowBlur,0)),ki(e,"shadowColor",l.textShadowColor||n.textShadowColor||"transparent"),ki(e,"shadowOffsetX",C(l.textShadowOffsetX,n.textShadowOffsetX,0)),ki(e,"shadowOffsetY",C(l.textShadowOffsetY,n.textShadowOffsetY,0)),ki(e,"textAlign",s),ki(e,"textBaseline","middle"),ki(e,"font",i.font||ju);var d=Li(l.textStroke||n.textStroke,g),f=Di(l.textFill||n.textFill),g=I(l.textStrokeWidth,n.textStrokeWidth);d&&(ki(e,"lineWidth",g),ki(e,"strokeStyle",d),e.strokeText(i.text,a,u)),f&&(ki(e,"fillStyle",f),e.fillText(i.text,a,u))}function Ti(t){return t.textBackgroundColor||t.textBorderWidth&&t.textBorderColor}function Ii(t,e,i,n,r,o,a){var s=i.textBackgroundColor,l=i.textBorderWidth,h=i.textBorderColor,u=_(s);if(ki(e,"shadowBlur",i.textBoxShadowBlur||0),ki(e,"shadowColor",i.textBoxShadowColor||"transparent"),ki(e,"shadowOffsetX",i.textBoxShadowOffsetX||0),ki(e,"shadowOffsetY",i.textBoxShadowOffsetY||0),u||l&&h){e.beginPath();var c=i.textBorderRadius;c?vi(e,{x:n,y:r,width:o,height:a,r:c}):e.rect(n,r,o,a),e.closePath()}if(u)ki(e,"fillStyle",s),e.fill();else if(w(s)){var d=s.image;d=Ke(d,null,t,Ci,s),d&&Je(d)&&e.drawImage(d,n,r,o,a)}l&&h&&(ki(e,"lineWidth",l),ki(e,"strokeStyle",h),e.stroke())}function Ci(t,e){e.image=t}function Ai(t,e,i){var n=e.x||0,r=e.y||0,o=e.textAlign,a=e.textVerticalAlign;if(i){var s=e.textPosition;if(s instanceof Array)n=i.x+Pi(s[0],i.width),r=i.y+Pi(s[1],i.height);else{var l=ai(s,i,e.textDistance);n=l.x,r=l.y,o=o||l.textAlign,a=a||l.textVerticalAlign}var h=e.textOffset;h&&(n+=h[0],r+=h[1])}return{baseX:n,baseY:r,textAlign:o,textVerticalAlign:a}}function ki(t,e,i){return t[e]=i,t[e]}function Li(t,e){return null==t||0>=e||"transparent"===t||"none"===t?null:t.image||t.colorStops?"#000":t}function Di(t){return null==t||"none"===t?null:t.image||t.colorStops?"#000":t}function Pi(t,e){return"string"==typeof t?t.lastIndexOf("%")>=0?parseFloat(t)/100*e:parseFloat(t):t}function Oi(t,e,i){return"right"===e?t-i[1]:"center"===e?t+i[3]/2-i[1]/2:t+i[3]}function zi(t,e){return null!=t&&(t||e.textBackgroundColor||e.textBorderWidth&&e.textBorderColor||e.textPadding)}function Bi(t){t=t||{},Au.call(this,t);for(var e in t)t.hasOwnProperty(e)&&"style"!==e&&(this[e]=t[e]);this.style=new Ru(t.style,this),this._rect=null,this.__clipPaths=[]}function Ei(t){Bi.call(this,t)}function Ri(t){return parseInt(t,10)}function Ni(t){return t?t.__builtin__?!0:"function"!=typeof t.resize||"function"!=typeof t.refresh?!1:!0:!1}function Fi(t){t.__unusedCount++}function Hi(t){1==t.__unusedCount&&t.clear()}function Vi(t,e,i){return nc.copy(t.getBoundingRect()),t.transform&&nc.applyTransform(t.transform),rc.width=e,rc.height=i,!nc.intersect(rc)}function Wi(t,e){if(t==e)return!1;if(!t||!e||t.length!==e.length)return!0;for(var i=0;i<t.length;i++)if(t[i]!==e[i])return!0}function Gi(t,e){for(var i=0;i<t.length;i++){var n=t[i];n.setTransform(e),e.beginPath(),n.buildPath(e,n.shape),e.clip(),n.restoreTransform(e)}}function Xi(t,e){var i=document.createElement("div");return i.style.cssText=["position:relative","overflow:hidden","width:"+t+"px","height:"+e+"px","padding:0","margin:0","border-width:0"].join(";")+";",i}function qi(t){return t.getBoundingClientRect?t.getBoundingClientRect():{left:0,top:0}}function Yi(t,e,i,n){return i=i||{},n||!Eh.canvasSupported?Ui(t,e,i):Eh.browser.firefox&&null!=e.layerX&&e.layerX!==e.offsetX?(i.zrX=e.layerX,i.zrY=e.layerY):null!=e.offsetX?(i.zrX=e.offsetX,i.zrY=e.offsetY):Ui(t,e,i),i}function Ui(t,e,i){var n=qi(t);i.zrX=e.clientX-n.left,i.zrY=e.clientY-n.top}function Zi(t,e,i){if(e=e||window.event,null!=e.zrX)return e;var n=e.type,r=n&&n.indexOf("touch")>=0;if(r){var o="touchend"!=n?e.targetTouches[0]:e.changedTouches[0];o&&Yi(t,o,e,i)}else Yi(t,e,e,i),e.zrDelta=e.wheelDelta?e.wheelDelta/120:-(e.detail||0)/3;var a=e.button;return null==e.which&&void 0!==a&&sc.test(e.type)&&(e.which=1&a?1:2&a?3:4&a?2:0),e}function ji(t,e,i){ac?t.addEventListener(e,i):t.attachEvent("on"+e,i)}function $i(t,e,i){ac?t.removeEventListener(e,i):t.detachEvent("on"+e,i)}function Ki(t){var e=t[1][0]-t[0][0],i=t[1][1]-t[0][1];return Math.sqrt(e*e+i*i)}function Qi(t){return[(t[0][0]+t[1][0])/2,(t[0][1]+t[1][1])/2]}function Ji(t){return"mousewheel"===t&&Eh.browser.firefox?"DOMMouseScroll":t}function tn(t,e,i){var n=t._gestureMgr;"start"===i&&n.clear();var r=n.recognize(e,t.handler.findHover(e.zrX,e.zrY,null).target,t.dom);if("end"===i&&n.clear(),r){var o=r.type;e.gestureEvent=o,t.handler.dispatchToElement({target:r.target},o,r.event)}}function en(t){t._touching=!0,clearTimeout(t._touchTimer),t._touchTimer=setTimeout(function(){t._touching=!1},700)}function nn(t){var e=t.pointerType;return"pen"===e||"touch"===e}function rn(t){function e(t,e){return function(){return e._touching?void 0:t.apply(e,arguments)}}d(gc,function(e){t._handlers[e]=v(mc[e],t)}),d(vc,function(e){t._handlers[e]=v(mc[e],t)}),d(fc,function(i){t._handlers[i]=e(mc[i],t)})}function on(t){function e(e,i){d(e,function(e){ji(t,Ji(e),i._handlers[e])},i)}eu.call(this),this.dom=t,this._touching=!1,this._touchTimer,this._gestureMgr=new uc,this._handlers={},rn(this),Eh.pointerEventsSupported?e(vc,this):(Eh.touchEventsSupported&&e(gc,this),e(fc,this))}function an(t,e){var i=new bc(Oh(),t,e);return i}function sn(t,e){_c[t]=e}function ln(t){return t.replace(/^\s+/,"").replace(/\s+$/,"")}function hn(t,e,i,n){var r=e[1]-e[0],o=i[1]-i[0];if(0===r)return 0===o?i[0]:(i[0]+i[1])/2;if(n)if(r>0){if(t<=e[0])return i[0];if(t>=e[1])return i[1]}else{if(t>=e[0])return i[0];if(t<=e[1])return i[1]}else{if(t===e[0])return i[0];if(t===e[1])return i[1]}return(t-e[0])/r*o+i[0]}function un(t,e){switch(t){case"center":case"middle":t="50%";break;case"left":case"top":t="0%";break;case"right":case"bottom":t="100%"}return"string"==typeof t?ln(t).match(/%$/)?parseFloat(t)/100*e:parseFloat(t):null==t?0/0:+t}function cn(t,e,i){return null==e&&(e=10),e=Math.min(Math.max(0,e),20),t=(+t).toFixed(e),i?t:+t}function dn(t){var e=t.toString(),i=e.indexOf("e");if(i>0){var n=+e.slice(i+1);return 0>n?-n:0}var r=e.indexOf(".");return 0>r?0:e.length-1-r}function fn(t,e){var i=Math.log,n=Math.LN10,r=Math.floor(i(t[1]-t[0])/n),o=Math.round(i(Math.abs(e[1]-e[0]))/n),a=Math.min(Math.max(-r+o,0),20);return isFinite(a)?a:20}function gn(t,e,i){if(!t[e])return 0;var n=g(t,function(t,e){return t+(isNaN(e)?0:e)},0);if(0===n)return 0;for(var r=Math.pow(10,i),o=f(t,function(t){return(isNaN(t)?0:t)/n*r*100}),a=100*r,s=f(o,function(t){return Math.floor(t)}),l=g(s,function(t,e){return t+e},0),h=f(o,function(t,e){return t-s[e]});a>l;){for(var u=Number.NEGATIVE_INFINITY,c=null,d=0,p=h.length;p>d;++d)h[d]>u&&(u=h[d],c=d);++s[c],h[c]=0,++l}return s[e]/r}function pn(t){var e=2*Math.PI;return(t%e+e)%e}function vn(t){return t>-Sc&&Sc>t}function mn(t){if(t instanceof Date)return t;if("string"==typeof t){var e=Mc.exec(t);if(!e)return new Date(0/0);if(e[8]){var i=+e[4]||0;return"Z"!==e[8].toUpperCase()&&(i-=e[8].slice(0,3)),new Date(Date.UTC(+e[1],+(e[2]||1)-1,+e[3]||1,i,+(e[5]||0),+e[6]||0,+e[7]||0))
}return new Date(+e[1],+(e[2]||1)-1,+e[3]||1,+e[4]||0,+(e[5]||0),+e[6]||0,+e[7]||0)}return new Date(null==t?0/0:Math.round(t))}function yn(t){return Math.pow(10,xn(t))}function xn(t){return Math.floor(Math.log(t)/Math.LN10)}function _n(t,e){var i,n=xn(t),r=Math.pow(10,n),o=t/r;return i=e?1.5>o?1:2.5>o?2:4>o?3:7>o?5:10:1>o?1:2>o?2:3>o?3:5>o?5:10,t=i*r,n>=-20?+t.toFixed(0>n?-n:0):t}function wn(t){return isNaN(t)?"-":(t=(t+"").split("."),t[0].replace(/(\d{1,3})(?=(?:\d{3})+(?!\d))/g,"$1,")+(t.length>1?"."+t[1]:""))}function bn(t,e){return t=(t||"").toLowerCase().replace(/-(.)/g,function(t,e){return e.toUpperCase()}),e&&t&&(t=t.charAt(0).toUpperCase()+t.slice(1)),t}function Sn(t){return String(t).replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#39;")}function Mn(t,e,i){y(e)||(e=[e]);var n=e.length;if(!n)return"";for(var r=e[0].$vars||[],o=0;o<r.length;o++){var a=Ic[o],s=Cc(a,0);t=t.replace(Cc(a),i?Sn(s):s)}for(var l=0;n>l;l++)for(var h=0;h<r.length;h++){var s=e[l][r[h]];t=t.replace(Cc(Ic[h],l),i?Sn(s):s)}return t}function Tn(t,e){return t?'<span style="display:inline-block;margin-right:5px;border-radius:10px;width:9px;height:9px;background-color:'+Sn(t)+";"+(e||"")+'"></span>':""}function In(t,e,i){("week"===t||"month"===t||"quarter"===t||"half-year"===t||"year"===t)&&(t="MM-dd\nyyyy");var n=mn(e),r=i?"UTC":"",o=n["get"+r+"FullYear"](),a=n["get"+r+"Month"]()+1,s=n["get"+r+"Date"](),l=n["get"+r+"Hours"](),h=n["get"+r+"Minutes"](),u=n["get"+r+"Seconds"]();return t=t.replace("MM",Ac(a)).replace("M",a).replace("yyyy",o).replace("yy",o%100).replace("dd",Ac(s)).replace("d",s).replace("hh",Ac(l)).replace("h",l).replace("mm",Ac(h)).replace("m",h).replace("ss",Ac(u)).replace("s",u)}function Cn(t,e,i){return t[Pc+e]=i}function An(t,e){return t[Pc+e]}function kn(t,e){return t.hasOwnProperty(Pc+e)}function Ln(t){var e={main:"",sub:""};return t&&(t=t.split(Lc),e.main=t[0]||"",e.sub=t[1]||""),e}function Dn(t){L(/^[a-zA-Z0-9_]+([.][a-zA-Z0-9_]+)?$/.test(t),'componentType "'+t+'" illegal')}function Pn(t,e){t.$constructor=t,t.extend=function(t){d(e,function(e){t[e]||console.warn("Method `"+e+"` should be implemented"+(t.type?" in "+t.type:"")+".")});var i=this,n=function(){t.$constructor?t.$constructor.apply(this,arguments):i.apply(this,arguments)};return o(n.prototype,t),n.extend=this.extend,n.superCall=On,n.superApply=zn,h(n,this),n.superClass=i,n}}function On(t,e){var i=A(arguments,2);return this.superClass.prototype[e].apply(t,i)}function zn(t,e,i){return this.superClass.prototype[e].apply(t,i)}function Bn(t,e){function i(t){var e=n[t.main];return e&&e[Dc]||(e=n[t.main]={},e[Dc]=!0),e}e=e||{};var n={};if(t.registerClass=function(t,e){if(e)if(Dn(e),e=Ln(e),e.sub){if(e.sub!==Dc){var r=i(e);r[e.sub]=t}}else n[e.main]&&console.warn(e.main+" exists."),n[e.main]=t;return t},t.getClass=function(t,e,i){var r=n[t];if(r&&r[Dc]&&(r=e?r[e]:null),i&&!r)throw new Error(e?"Component "+t+"."+(e||"")+" not exists. Load it first.":t+".type should be specified.");return r},t.getClassesByMainType=function(t){t=Ln(t);var e=[],i=n[t.main];return i&&i[Dc]?d(i,function(t,i){i!==Dc&&e.push(t)}):e.push(i),e},t.hasClass=function(t){return t=Ln(t),!!n[t.main]},t.getAllClassMainTypes=function(){var t=[];return d(n,function(e,i){t.push(i)}),t},t.hasSubTypes=function(t){t=Ln(t);var e=n[t.main];return e&&e[Dc]},t.parseClassType=Ln,e.registerWhenExtend){var r=t.extend;r&&(t.extend=function(e){var i=r.call(this,e);return t.registerClass(i,e.type)})}return t}function En(t){return t>-Hc&&Hc>t}function Rn(t){return t>Hc||-Hc>t}function Nn(t,e,i,n,r){var o=1-r;return o*o*(o*t+3*r*e)+r*r*(r*n+3*o*i)}function Fn(t,e,i,n,r){var o=1-r;return 3*(((e-t)*o+2*(i-e)*r)*o+(n-i)*r*r)}function Hn(t,e,i,n,r,o){var a=n+3*(e-i)-t,s=3*(i-2*e+t),l=3*(e-t),h=t-r,u=s*s-3*a*l,c=s*l-9*a*h,d=l*l-3*s*h,f=0;if(En(u)&&En(c))if(En(s))o[0]=0;else{var g=-l/s;g>=0&&1>=g&&(o[f++]=g)}else{var p=c*c-4*u*d;if(En(p)){var v=c/u,g=-s/a+v,m=-v/2;g>=0&&1>=g&&(o[f++]=g),m>=0&&1>=m&&(o[f++]=m)}else if(p>0){var y=Fc(p),x=u*s+1.5*a*(-c+y),_=u*s+1.5*a*(-c-y);x=0>x?-Nc(-x,Gc):Nc(x,Gc),_=0>_?-Nc(-_,Gc):Nc(_,Gc);var g=(-s-(x+_))/(3*a);g>=0&&1>=g&&(o[f++]=g)}else{var w=(2*u*s-3*a*c)/(2*Fc(u*u*u)),b=Math.acos(w)/3,S=Fc(u),M=Math.cos(b),g=(-s-2*S*M)/(3*a),m=(-s+S*(M+Wc*Math.sin(b)))/(3*a),T=(-s+S*(M-Wc*Math.sin(b)))/(3*a);g>=0&&1>=g&&(o[f++]=g),m>=0&&1>=m&&(o[f++]=m),T>=0&&1>=T&&(o[f++]=T)}}return f}function Vn(t,e,i,n,r){var o=6*i-12*e+6*t,a=9*e+3*n-3*t-9*i,s=3*e-3*t,l=0;if(En(a)){if(Rn(o)){var h=-s/o;h>=0&&1>=h&&(r[l++]=h)}}else{var u=o*o-4*a*s;if(En(u))r[0]=-o/(2*a);else if(u>0){var c=Fc(u),h=(-o+c)/(2*a),d=(-o-c)/(2*a);h>=0&&1>=h&&(r[l++]=h),d>=0&&1>=d&&(r[l++]=d)}}return l}function Wn(t,e,i,n,r,o){var a=(e-t)*r+t,s=(i-e)*r+e,l=(n-i)*r+i,h=(s-a)*r+a,u=(l-s)*r+s,c=(u-h)*r+h;o[0]=t,o[1]=a,o[2]=h,o[3]=c,o[4]=c,o[5]=u,o[6]=l,o[7]=n}function Gn(t,e,i,n,r,o,a,s,l,h,u){var c,d,f,g,p,v=.005,m=1/0;Xc[0]=l,Xc[1]=h;for(var y=0;1>y;y+=.05)qc[0]=Nn(t,i,r,a,y),qc[1]=Nn(e,n,o,s,y),g=Jh(Xc,qc),m>g&&(c=y,m=g);m=1/0;for(var x=0;32>x&&!(Vc>v);x++)d=c-v,f=c+v,qc[0]=Nn(t,i,r,a,d),qc[1]=Nn(e,n,o,s,d),g=Jh(qc,Xc),d>=0&&m>g?(c=d,m=g):(Yc[0]=Nn(t,i,r,a,f),Yc[1]=Nn(e,n,o,s,f),p=Jh(Yc,Xc),1>=f&&m>p?(c=f,m=p):v*=.5);return u&&(u[0]=Nn(t,i,r,a,c),u[1]=Nn(e,n,o,s,c)),Fc(m)}function Xn(t,e,i,n){var r=1-n;return r*(r*t+2*n*e)+n*n*i}function qn(t,e,i,n){return 2*((1-n)*(e-t)+n*(i-e))}function Yn(t,e,i,n,r){var o=t-2*e+i,a=2*(e-t),s=t-n,l=0;if(En(o)){if(Rn(a)){var h=-s/a;h>=0&&1>=h&&(r[l++]=h)}}else{var u=a*a-4*o*s;if(En(u)){var h=-a/(2*o);h>=0&&1>=h&&(r[l++]=h)}else if(u>0){var c=Fc(u),h=(-a+c)/(2*o),d=(-a-c)/(2*o);h>=0&&1>=h&&(r[l++]=h),d>=0&&1>=d&&(r[l++]=d)}}return l}function Un(t,e,i){var n=t+i-2*e;return 0===n?.5:(t-e)/n}function Zn(t,e,i,n,r){var o=(e-t)*n+t,a=(i-e)*n+e,s=(a-o)*n+o;r[0]=t,r[1]=o,r[2]=s,r[3]=s,r[4]=a,r[5]=i}function jn(t,e,i,n,r,o,a,s,l){var h,u=.005,c=1/0;Xc[0]=a,Xc[1]=s;for(var d=0;1>d;d+=.05){qc[0]=Xn(t,i,r,d),qc[1]=Xn(e,n,o,d);var f=Jh(Xc,qc);c>f&&(h=d,c=f)}c=1/0;for(var g=0;32>g&&!(Vc>u);g++){var p=h-u,v=h+u;qc[0]=Xn(t,i,r,p),qc[1]=Xn(e,n,o,p);var f=Jh(qc,Xc);if(p>=0&&c>f)h=p,c=f;else{Yc[0]=Xn(t,i,r,v),Yc[1]=Xn(e,n,o,v);var m=Jh(Yc,Xc);1>=v&&c>m?(h=v,c=m):u*=.5}}return l&&(l[0]=Xn(t,i,r,h),l[1]=Xn(e,n,o,h)),Fc(c)}function $n(t,e,i,n,r,o){r[0]=Uc(t,i),r[1]=Uc(e,n),o[0]=Zc(t,i),o[1]=Zc(e,n)}function Kn(t,e,i,n,r,o,a,s,l,h){var u,c=Vn,d=Nn,f=c(t,i,r,a,ed);for(l[0]=1/0,l[1]=1/0,h[0]=-1/0,h[1]=-1/0,u=0;f>u;u++){var g=d(t,i,r,a,ed[u]);l[0]=Uc(g,l[0]),h[0]=Zc(g,h[0])}for(f=c(e,n,o,s,id),u=0;f>u;u++){var p=d(e,n,o,s,id[u]);l[1]=Uc(p,l[1]),h[1]=Zc(p,h[1])}l[0]=Uc(t,l[0]),h[0]=Zc(t,h[0]),l[0]=Uc(a,l[0]),h[0]=Zc(a,h[0]),l[1]=Uc(e,l[1]),h[1]=Zc(e,h[1]),l[1]=Uc(s,l[1]),h[1]=Zc(s,h[1])}function Qn(t,e,i,n,r,o,a,s){var l=Un,h=Xn,u=Zc(Uc(l(t,i,r),1),0),c=Zc(Uc(l(e,n,o),1),0),d=h(t,i,r,u),f=h(e,n,o,c);a[0]=Uc(t,r,d),a[1]=Uc(e,o,f),s[0]=Zc(t,r,d),s[1]=Zc(e,o,f)}function Jn(t,e,i,n,r,o,a,s,l){var h=U,u=Z,c=Math.abs(r-o);if(1e-4>c%Kc&&c>1e-4)return s[0]=t-i,s[1]=e-n,l[0]=t+i,void(l[1]=e+n);if(Qc[0]=$c(r)*i+t,Qc[1]=jc(r)*n+e,Jc[0]=$c(o)*i+t,Jc[1]=jc(o)*n+e,h(s,Qc,Jc),u(l,Qc,Jc),r%=Kc,0>r&&(r+=Kc),o%=Kc,0>o&&(o+=Kc),r>o&&!a?o+=Kc:o>r&&a&&(r+=Kc),a){var d=o;o=r,r=d}for(var f=0;o>f;f+=Math.PI/2)f>r&&(td[0]=$c(f)*i+t,td[1]=jc(f)*n+e,h(s,td,s),u(l,td,l))}function tr(t,e,i,n,r,o,a){if(0===r)return!1;var s=r,l=0,h=t;if(a>e+s&&a>n+s||e-s>a&&n-s>a||o>t+s&&o>i+s||t-s>o&&i-s>o)return!1;if(t===i)return Math.abs(o-t)<=s/2;l=(e-n)/(t-i),h=(t*n-i*e)/(t-i);var u=l*o-a+h,c=u*u/(l*l+1);return s/2*s/2>=c}function er(t,e,i,n,r,o,a,s,l,h,u){if(0===l)return!1;var c=l;if(u>e+c&&u>n+c&&u>o+c&&u>s+c||e-c>u&&n-c>u&&o-c>u&&s-c>u||h>t+c&&h>i+c&&h>r+c&&h>a+c||t-c>h&&i-c>h&&r-c>h&&a-c>h)return!1;var d=Gn(t,e,i,n,r,o,a,s,h,u,null);return c/2>=d}function ir(t,e,i,n,r,o,a,s,l){if(0===a)return!1;var h=a;if(l>e+h&&l>n+h&&l>o+h||e-h>l&&n-h>l&&o-h>l||s>t+h&&s>i+h&&s>r+h||t-h>s&&i-h>s&&r-h>s)return!1;var u=jn(t,e,i,n,r,o,s,l,null);return h/2>=u}function nr(t){return t%=vd,0>t&&(t+=vd),t}function rr(t,e,i,n,r,o,a,s,l){if(0===a)return!1;var h=a;s-=t,l-=e;var u=Math.sqrt(s*s+l*l);if(u-h>i||i>u+h)return!1;if(Math.abs(n-r)%md<1e-4)return!0;if(o){var c=n;n=nr(r),r=nr(c)}else n=nr(n),r=nr(r);n>r&&(r+=md);var d=Math.atan2(l,s);return 0>d&&(d+=md),d>=n&&r>=d||d+md>=n&&r>=d+md}function or(t,e,i,n,r,o){if(o>e&&o>n||e>o&&n>o)return 0;if(n===e)return 0;var a=e>n?1:-1,s=(o-e)/(n-e);(1===s||0===s)&&(a=e>n?.5:-.5);var l=s*(i-t)+t;return l>r?a:0}function ar(t,e){return Math.abs(t-e)<xd}function sr(){var t=wd[0];wd[0]=wd[1],wd[1]=t}function lr(t,e,i,n,r,o,a,s,l,h){if(h>e&&h>n&&h>o&&h>s||e>h&&n>h&&o>h&&s>h)return 0;var u=Hn(e,n,o,s,h,_d);if(0===u)return 0;for(var c,d,f=0,g=-1,p=0;u>p;p++){var v=_d[p],m=0===v||1===v?.5:1,y=Nn(t,i,r,a,v);l>y||(0>g&&(g=Vn(e,n,o,s,wd),wd[1]<wd[0]&&g>1&&sr(),c=Nn(e,n,o,s,wd[0]),g>1&&(d=Nn(e,n,o,s,wd[1]))),f+=2==g?v<wd[0]?e>c?m:-m:v<wd[1]?c>d?m:-m:d>s?m:-m:v<wd[0]?e>c?m:-m:c>s?m:-m)}return f}function hr(t,e,i,n,r,o,a,s){if(s>e&&s>n&&s>o||e>s&&n>s&&o>s)return 0;var l=Yn(e,n,o,s,_d);if(0===l)return 0;var h=Un(e,n,o);if(h>=0&&1>=h){for(var u=0,c=Xn(e,n,o,h),d=0;l>d;d++){var f=0===_d[d]||1===_d[d]?.5:1,g=Xn(t,i,r,_d[d]);a>g||(u+=_d[d]<h?e>c?f:-f:c>o?f:-f)}return u}var f=0===_d[0]||1===_d[0]?.5:1,g=Xn(t,i,r,_d[0]);return a>g?0:e>o?f:-f}function ur(t,e,i,n,r,o,a,s){if(s-=e,s>i||-i>s)return 0;var l=Math.sqrt(i*i-s*s);_d[0]=-l,_d[1]=l;var h=Math.abs(n-r);if(1e-4>h)return 0;if(1e-4>h%yd){n=0,r=yd;var u=o?1:-1;return a>=_d[0]+t&&a<=_d[1]+t?u:0}if(o){var l=n;n=nr(r),r=nr(l)}else n=nr(n),r=nr(r);n>r&&(r+=yd);for(var c=0,d=0;2>d;d++){var f=_d[d];if(f+t>a){var g=Math.atan2(s,f),u=o?1:-1;0>g&&(g=yd+g),(g>=n&&r>=g||g+yd>=n&&r>=g+yd)&&(g>Math.PI/2&&g<1.5*Math.PI&&(u=-u),c+=u)}}return c}function cr(t,e,i,n,r){for(var o=0,a=0,s=0,l=0,h=0,u=0;u<t.length;){var c=t[u++];switch(c===nd.M&&u>1&&(i||(o+=or(a,s,l,h,n,r))),1==u&&(a=t[u],s=t[u+1],l=a,h=s),c){case nd.M:l=t[u++],h=t[u++],a=l,s=h;break;case nd.L:if(i){if(tr(a,s,t[u],t[u+1],e,n,r))return!0}else o+=or(a,s,t[u],t[u+1],n,r)||0;a=t[u++],s=t[u++];break;case nd.C:if(i){if(er(a,s,t[u++],t[u++],t[u++],t[u++],t[u],t[u+1],e,n,r))return!0}else o+=lr(a,s,t[u++],t[u++],t[u++],t[u++],t[u],t[u+1],n,r)||0;a=t[u++],s=t[u++];break;case nd.Q:if(i){if(ir(a,s,t[u++],t[u++],t[u],t[u+1],e,n,r))return!0}else o+=hr(a,s,t[u++],t[u++],t[u],t[u+1],n,r)||0;a=t[u++],s=t[u++];break;case nd.A:var d=t[u++],f=t[u++],g=t[u++],p=t[u++],v=t[u++],m=t[u++],y=(t[u++],1-t[u++]),x=Math.cos(v)*g+d,_=Math.sin(v)*p+f;u>1?o+=or(a,s,x,_,n,r):(l=x,h=_);var w=(n-d)*p/g+d;if(i){if(rr(d,f,p,v,v+m,y,e,w,r))return!0}else o+=ur(d,f,p,v,v+m,y,w,r);a=Math.cos(v+m)*g+d,s=Math.sin(v+m)*p+f;break;case nd.R:l=a=t[u++],h=s=t[u++];var b=t[u++],S=t[u++],x=l+b,_=h+S;if(i){if(tr(l,h,x,h,e,n,r)||tr(x,h,x,_,e,n,r)||tr(x,_,l,_,e,n,r)||tr(l,_,l,h,e,n,r))return!0}else o+=or(x,h,x,_,n,r),o+=or(l,_,l,h,n,r);break;case nd.Z:if(i){if(tr(a,s,l,h,e,n,r))return!0}else o+=or(a,s,l,h,n,r);a=l,s=h}}return i||ar(s,h)||(o+=or(a,s,l,h,n,r)||0),0!==o}function dr(t,e,i){return cr(t,0,!1,e,i)}function fr(t,e,i,n){return cr(t,e,!0,i,n)}function gr(t){Bi.call(this,t),this.path=null}function pr(t,e,i,n,r,o,a,s,l,h,u){var c=l*(Od/180),d=Pd(c)*(t-i)/2+Dd(c)*(e-n)/2,f=-1*Dd(c)*(t-i)/2+Pd(c)*(e-n)/2,g=d*d/(a*a)+f*f/(s*s);g>1&&(a*=Ld(g),s*=Ld(g));var p=(r===o?-1:1)*Ld((a*a*s*s-a*a*f*f-s*s*d*d)/(a*a*f*f+s*s*d*d))||0,v=p*a*f/s,m=p*-s*d/a,y=(t+i)/2+Pd(c)*v-Dd(c)*m,x=(e+n)/2+Dd(c)*v+Pd(c)*m,_=Ed([1,0],[(d-v)/a,(f-m)/s]),w=[(d-v)/a,(f-m)/s],b=[(-1*d-v)/a,(-1*f-m)/s],S=Ed(w,b);Bd(w,b)<=-1&&(S=Od),Bd(w,b)>=1&&(S=0),0===o&&S>0&&(S-=2*Od),1===o&&0>S&&(S+=2*Od),u.addData(h,y,x,a,s,_,S,c,o)}function vr(t){if(!t)return[];var e,i=t.replace(/-/g," -").replace(/  /g," ").replace(/ /g,",").replace(/,,/g,",");for(e=0;e<kd.length;e++)i=i.replace(new RegExp(kd[e],"g"),"|"+kd[e]);var n,r=i.split("|"),o=0,a=0,s=new pd,l=pd.CMD;for(e=1;e<r.length;e++){var h,u=r[e],c=u.charAt(0),d=0,f=u.slice(1).replace(/e,-/g,"e-").split(",");f.length>0&&""===f[0]&&f.shift();for(var g=0;g<f.length;g++)f[g]=parseFloat(f[g]);for(;d<f.length&&!isNaN(f[d])&&!isNaN(f[0]);){var p,v,m,y,x,_,w,b=o,S=a;switch(c){case"l":o+=f[d++],a+=f[d++],h=l.L,s.addData(h,o,a);break;case"L":o=f[d++],a=f[d++],h=l.L,s.addData(h,o,a);break;case"m":o+=f[d++],a+=f[d++],h=l.M,s.addData(h,o,a),c="l";break;case"M":o=f[d++],a=f[d++],h=l.M,s.addData(h,o,a),c="L";break;case"h":o+=f[d++],h=l.L,s.addData(h,o,a);break;case"H":o=f[d++],h=l.L,s.addData(h,o,a);break;case"v":a+=f[d++],h=l.L,s.addData(h,o,a);break;case"V":a=f[d++],h=l.L,s.addData(h,o,a);break;case"C":h=l.C,s.addData(h,f[d++],f[d++],f[d++],f[d++],f[d++],f[d++]),o=f[d-2],a=f[d-1];break;case"c":h=l.C,s.addData(h,f[d++]+o,f[d++]+a,f[d++]+o,f[d++]+a,f[d++]+o,f[d++]+a),o+=f[d-2],a+=f[d-1];break;case"S":p=o,v=a;var M=s.len(),T=s.data;n===l.C&&(p+=o-T[M-4],v+=a-T[M-3]),h=l.C,b=f[d++],S=f[d++],o=f[d++],a=f[d++],s.addData(h,p,v,b,S,o,a);break;case"s":p=o,v=a;var M=s.len(),T=s.data;n===l.C&&(p+=o-T[M-4],v+=a-T[M-3]),h=l.C,b=o+f[d++],S=a+f[d++],o+=f[d++],a+=f[d++],s.addData(h,p,v,b,S,o,a);break;case"Q":b=f[d++],S=f[d++],o=f[d++],a=f[d++],h=l.Q,s.addData(h,b,S,o,a);break;case"q":b=f[d++]+o,S=f[d++]+a,o+=f[d++],a+=f[d++],h=l.Q,s.addData(h,b,S,o,a);break;case"T":p=o,v=a;var M=s.len(),T=s.data;n===l.Q&&(p+=o-T[M-4],v+=a-T[M-3]),o=f[d++],a=f[d++],h=l.Q,s.addData(h,p,v,o,a);break;case"t":p=o,v=a;var M=s.len(),T=s.data;n===l.Q&&(p+=o-T[M-4],v+=a-T[M-3]),o+=f[d++],a+=f[d++],h=l.Q,s.addData(h,p,v,o,a);break;case"A":m=f[d++],y=f[d++],x=f[d++],_=f[d++],w=f[d++],b=o,S=a,o=f[d++],a=f[d++],h=l.A,pr(b,S,o,a,_,w,m,y,x,h,s);break;case"a":m=f[d++],y=f[d++],x=f[d++],_=f[d++],w=f[d++],b=o,S=a,o+=f[d++],a+=f[d++],h=l.A,pr(b,S,o,a,_,w,m,y,x,h,s)}}("z"===c||"Z"===c)&&(h=l.Z,s.addData(h)),n=h}return s.toStatic(),s}function mr(t,e){var i=vr(t);return e=e||{},e.buildPath=function(t){if(t.setData){t.setData(i.data);var e=t.getContext();e&&t.rebuildPath(e)}else{var e=t;i.rebuildPath(e)}},e.applyTransform=function(t){Ad(i,t),this.dirty(!0)},e}function yr(t,e){return new gr(mr(t,e))}function xr(t,e){return gr.extend(mr(t,e))}function _r(t,e){for(var i=[],n=t.length,r=0;n>r;r++){var o=t[r];o.path||o.createPathProxy(),o.__dirtyPath&&o.buildPath(o.path,o.shape,!0),i.push(o.path)}var a=new gr(e);return a.createPathProxy(),a.buildPath=function(t){t.appendPath(i);var e=t.getContext();e&&t.rebuildPath(e)},a}function wr(t,e,i,n,r,o,a){var s=.5*(i-t),l=.5*(n-e);return(2*(e-i)+s+l)*a+(-3*(e-i)-2*s-l)*o+s*r+e}function br(t,e,i){var n=e.points,r=e.smooth;if(n&&n.length>=2){if(r&&"spline"!==r){var o=Xd(n,r,i,e.smoothConstraint);t.moveTo(n[0][0],n[0][1]);for(var a=n.length,s=0;(i?a:a-1)>s;s++){var l=o[2*s],h=o[2*s+1],u=n[(s+1)%a];t.bezierCurveTo(l[0],l[1],h[0],h[1],u[0],u[1])}}else{"spline"===r&&(n=Gd(n,i)),t.moveTo(n[0][0],n[0][1]);for(var s=1,c=n.length;c>s;s++)t.lineTo(n[s][0],n[s][1])}i&&t.closePath()}}function Sr(t,e,i){var n=t.cpx2,r=t.cpy2;return null===n||null===r?[(i?Fn:Nn)(t.x1,t.cpx1,t.cpx2,t.x2,e),(i?Fn:Nn)(t.y1,t.cpy1,t.cpy2,t.y2,e)]:[(i?qn:Xn)(t.x1,t.cpx1,t.x2,e),(i?qn:Xn)(t.y1,t.cpy1,t.y2,e)]}function Mr(t){return gr.extend(t)}function Tr(t,e){return xr(t,e)}function Ir(t,e,i,n){var r=yr(t,e),o=r.getBoundingRect();return i&&("center"===n&&(i=Ar(i,o)),kr(r,i)),r}function Cr(t,e,i){var n=new Ei({style:{image:t,x:e.x,y:e.y,width:e.width,height:e.height},onload:function(t){if("center"===i){var r={width:t.width,height:t.height};n.setStyle(Ar(e,r))}}});return n}function Ar(t,e){var i,n=e.width/e.height,r=t.height*n;r<=t.width?i=t.height:(r=t.width,i=r/n);var o=t.x+t.width/2,a=t.y+t.height/2;return{x:o-r/2,y:a-i/2,width:r,height:i}}function kr(t,e){if(t.applyTransform){var i=t.getBoundingRect(),n=i.calculateTransform(e);t.applyTransform(n)}}function Lr(t){var e=t.shape,i=t.style.lineWidth;return nf(2*e.x1)===nf(2*e.x2)&&(e.x1=e.x2=Pr(e.x1,i,!0)),nf(2*e.y1)===nf(2*e.y2)&&(e.y1=e.y2=Pr(e.y1,i,!0)),t}function Dr(t){var e=t.shape,i=t.style.lineWidth,n=e.x,r=e.y,o=e.width,a=e.height;return e.x=Pr(e.x,i,!0),e.y=Pr(e.y,i,!0),e.width=Math.max(Pr(n+o,i,!1)-e.x,0===o?0:1),e.height=Math.max(Pr(r+a,i,!1)-e.y,0===a?0:1),t}function Pr(t,e,i){var n=nf(2*t);return(n+nf(e))%2===0?n/2:(n+(i?1:-1))/2}function Or(t){return null!=t&&"none"!=t}function zr(t){return"string"==typeof t?_e(t,-.1):t}function Br(t){if(t.__hoverStlDirty){var e=t.style.stroke,i=t.style.fill,n=t.__hoverStl;n.fill=n.fill||(Or(i)?zr(i):null),n.stroke=n.stroke||(Or(e)?zr(e):null);var r={};for(var o in n)null!=n[o]&&(r[o]=t.style[o]);t.__normalStl=r,t.__hoverStlDirty=!1}}function Er(t){if(!t.__isHover){if(Br(t),t.useHoverLayer)t.__zr&&t.__zr.addHover(t,t.__hoverStl);else{var e=t.style,i=e.insideRollbackOpt;i&&to(e),e.extendFrom(t.__hoverStl),i&&(Jr(e,e.insideOriginalTextPosition,i),null==e.textFill&&(e.textFill=i.autoColor)),t.dirty(!1),t.z2+=1}t.__isHover=!0}}function Rr(t){if(t.__isHover){var e=t.__normalStl;t.useHoverLayer?t.__zr&&t.__zr.removeHover(t):(e&&t.setStyle(e),t.z2-=1),t.__isHover=!1}}function Nr(t){"group"===t.type?t.traverse(function(t){"group"!==t.type&&Er(t)}):Er(t)}function Fr(t){"group"===t.type?t.traverse(function(t){"group"!==t.type&&Rr(t)}):Rr(t)}function Hr(t,e){t.__hoverStl=t.hoverStyle||e||{},t.__hoverStlDirty=!0,t.__isHover&&Br(t)}function Vr(t){this.__hoverSilentOnTouch&&t.zrByTouch||!this.__isEmphasis&&Nr(this)}function Wr(t){this.__hoverSilentOnTouch&&t.zrByTouch||!this.__isEmphasis&&Fr(this)}function Gr(){this.__isEmphasis=!0,Nr(this)}function Xr(){this.__isEmphasis=!1,Fr(this)}function qr(t,e,i){t.__hoverSilentOnTouch=i&&i.hoverSilentOnTouch,"group"===t.type?t.traverse(function(t){"group"!==t.type&&Hr(t,e)}):Hr(t,e),t.on("mouseover",Vr).on("mouseout",Wr),t.on("emphasis",Gr).on("normal",Xr)}function Yr(t,e,i,n,r,o,a){r=r||af;var s=r.labelFetcher,l=r.labelDataIndex,h=r.labelDimIndex,u=i.getShallow("show"),c=n.getShallow("show"),d=u||c?I(s?s.getFormattedLabel(l,"normal",null,h):null,r.defaultText):null,f=u?d:null,g=c?I(s?s.getFormattedLabel(l,"emphasis",null,h):null,d):null;(null!=f||null!=g)&&(Ur(t,i,o,r),Ur(e,n,a,r,!0)),t.text=f,e.text=g}function Ur(t,e,i,n,r){return jr(t,e,n,r),i&&o(t,i),t.host&&t.host.dirty&&t.host.dirty(!1),t}function Zr(t,e,i){var n,r={isRectText:!0};i===!1?n=!0:r.autoColor=i,jr(t,e,r,n),t.host&&t.host.dirty&&t.host.dirty(!1)}function jr(t,e,i,n){if(i=i||af,i.isRectText){var r=e.getShallow("position")||(n?null:"inside");"outside"===r&&(r="top"),t.textPosition=r,t.textOffset=e.getShallow("offset");var o=e.getShallow("rotate");null!=o&&(o*=Math.PI/180),t.textRotation=o,t.textDistance=I(e.getShallow("distance"),n?null:5)}var a,s=e.ecModel,l=s&&s.option.textStyle,h=$r(e);if(h){a={};for(var u in h)if(h.hasOwnProperty(u)){var c=e.getModel(["rich",u]);Kr(a[u]={},c,l,i,n)}}return t.rich=a,Kr(t,e,l,i,n,!0),i.forceRich&&!i.textStyle&&(i.textStyle={}),t}function $r(t){for(var e;t&&t!==t.ecModel;){var i=(t.option||af).rich;if(i){e=e||{};for(var n in i)i.hasOwnProperty(n)&&(e[n]=1)}t=t.parentModel}return e}function Kr(t,e,i,n,r,o){if(i=!r&&i||af,t.textFill=Qr(e.getShallow("color"),n)||i.color,t.textStroke=Qr(e.getShallow("textBorderColor"),n)||i.textBorderColor,t.textStrokeWidth=I(e.getShallow("textBorderWidth"),i.textBorderWidth),!r){if(o){var a=t.textPosition;t.insideRollback=Jr(t,a,n),t.insideOriginalTextPosition=a,t.insideRollbackOpt=n}null==t.textFill&&(t.textFill=n.autoColor)}t.fontStyle=e.getShallow("fontStyle")||i.fontStyle,t.fontWeight=e.getShallow("fontWeight")||i.fontWeight,t.fontSize=e.getShallow("fontSize")||i.fontSize,t.fontFamily=e.getShallow("fontFamily")||i.fontFamily,t.textAlign=e.getShallow("align"),t.textVerticalAlign=e.getShallow("verticalAlign")||e.getShallow("baseline"),t.textLineHeight=e.getShallow("lineHeight"),t.textWidth=e.getShallow("width"),t.textHeight=e.getShallow("height"),t.textTag=e.getShallow("tag"),o&&n.disableBox||(t.textBackgroundColor=Qr(e.getShallow("backgroundColor"),n),t.textPadding=e.getShallow("padding"),t.textBorderColor=Qr(e.getShallow("borderColor"),n),t.textBorderWidth=e.getShallow("borderWidth"),t.textBorderRadius=e.getShallow("borderRadius"),t.textBoxShadowColor=e.getShallow("shadowColor"),t.textBoxShadowBlur=e.getShallow("shadowBlur"),t.textBoxShadowOffsetX=e.getShallow("shadowOffsetX"),t.textBoxShadowOffsetY=e.getShallow("shadowOffsetY")),t.textShadowColor=e.getShallow("textShadowColor")||i.textShadowColor,t.textShadowBlur=e.getShallow("textShadowBlur")||i.textShadowBlur,t.textShadowOffsetX=e.getShallow("textShadowOffsetX")||i.textShadowOffsetX,t.textShadowOffsetY=e.getShallow("textShadowOffsetY")||i.textShadowOffsetY}function Qr(t,e){return"auto"!==t?t:e&&e.autoColor?e.autoColor:null}function Jr(t,e,i){var n,r=i.useInsideStyle;return null==t.textFill&&r!==!1&&(r===!0||i.isRectText&&e&&"string"==typeof e&&e.indexOf("inside")>=0)&&(n={textFill:null,textStroke:t.textStroke,textStrokeWidth:t.textStrokeWidth},t.textFill="#fff",null==t.textStroke&&(t.textStroke=i.autoColor,null==t.textStrokeWidth&&(t.textStrokeWidth=2))),n}function to(t){var e=t.insideRollback;e&&(t.textFill=e.textFill,t.textStroke=e.textStroke,t.textStrokeWidth=e.textStrokeWidth)}function eo(t,e){var i=e||e.getModel("textStyle");return[t.fontStyle||i&&i.getShallow("fontStyle")||"",t.fontWeight||i&&i.getShallow("fontWeight")||"",(t.fontSize||i&&i.getShallow("fontSize")||12)+"px",t.fontFamily||i&&i.getShallow("fontFamily")||"sans-serif"].join(" ")}function io(t,e,i,n,r,o){"function"==typeof r&&(o=r,r=null);var a=n&&n.isAnimationEnabled();if(a){var s=t?"Update":"",l=n.getShallow("animationDuration"+s),h=n.getShallow("animationEasing"+s),u=n.getShallow("animationDelay"+s);"function"==typeof u&&(u=u(r,n.getAnimationDelayParams?n.getAnimationDelayParams(e,r):null)),"function"==typeof l&&(l=l(r)),l>0?e.animateTo(i,l,u||0,h,o,!!o):(e.stopAnimation(),e.attr(i),o&&o())}else e.stopAnimation(),e.attr(i),o&&o()}function no(t,e,i,n,r){io(!0,t,e,i,n,r)}function ro(t,e,i,n,r){io(!1,t,e,i,n,r)}function oo(t,e){for(var i=ee([]);t&&t!==e;)ne(i,t.getLocalTransform(),i),t=t.parent;return i}function ao(t,e,i){return e&&!c(e)&&(e=lu.getLocalTransform(e)),i&&(e=se([],e)),Y([],t,e)}function so(t,e,i){var n=0===e[4]||0===e[5]||0===e[0]?1:Math.abs(2*e[4]/e[0]),r=0===e[4]||0===e[5]||0===e[2]?1:Math.abs(2*e[4]/e[2]),o=["left"===t?-n:"right"===t?n:0,"top"===t?-r:"bottom"===t?r:0];return o=ao(o,e,i),Math.abs(o[0])>Math.abs(o[1])?o[0]>0?"right":"left":o[1]>0?"bottom":"top"}function lo(t,e,i){function n(t){var e={};return t.traverse(function(t){!t.isGroup&&t.anid&&(e[t.anid]=t)}),e}function r(t){var e={position:R(t.position),rotation:t.rotation};return t.shape&&(e.shape=o({},t.shape)),e}if(t&&e){var a=n(t);e.traverse(function(t){if(!t.isGroup&&t.anid){var e=a[t.anid];if(e){var n=r(t);t.attr(r(e)),no(t,n,i,t.dataIndex)}}})}}function ho(t,e){return f(t,function(t){var i=t[0];i=rf(i,e.x),i=of(i,e.x+e.width);var n=t[1];return n=rf(n,e.y),n=of(n,e.y+e.height),[i,n]})}function uo(t,e){var i=rf(t.x,e.x),n=of(t.x+t.width,e.x+e.width),r=rf(t.y,e.y),o=of(t.y+t.height,e.y+e.height);return n>=i&&o>=r?{x:i,y:r,width:n-i,height:o-r}:void 0}function co(t,e,i){e=o({rectHover:!0},e);var n=e.style={strokeNoScale:!0};return i=i||{x:-1,y:-1,width:2,height:2},t?0===t.indexOf("image://")?(n.image=t.slice(8),a(n,i),new Ei(e)):Ir(t.replace("path://",""),e,i,"center"):void 0}function fo(t,e,i){this.parentModel=e,this.ecModel=i,this.option=t}function go(t,e,i){for(var n=0;n<e.length&&(!e[n]||(t=t&&"object"==typeof t?t[e[n]]:null,null!=t));n++);return null==t&&i&&(t=i.get(e)),t}function po(t,e){var i=An(t,"getParent");return i?i.call(t,e):t.parentModel}function vo(t){return t instanceof Array?t:null==t?[]:[t]}function mo(t,e){if(t)for(var i=t.emphasis=t.emphasis||{},n=t.normal=t.normal||{},r=0,o=e.length;o>r;r++){var a=e[r];!i.hasOwnProperty(a)&&n.hasOwnProperty(a)&&(i[a]=n[a])}}function yo(t){return t&&(null==t.value?t:t.value)}function xo(t){return pf(t)&&!(t instanceof Array)}function _o(t,e){var i=e&&e.type;return"ordinal"===i?t:("time"===i&&"number"!=typeof t&&null!=t&&"-"!==t&&(t=+mn(t)),null==t||""===t?0/0:+t)}function wo(t,e){e=(e||[]).slice();var i=f(t||[],function(t){return{exist:t}});return gf(e,function(t,n){if(pf(t)){for(var r=0;r<i.length;r++)if(!i[r].option&&null!=t.id&&i[r].exist.id===t.id+"")return i[r].option=t,void(e[n]=null);for(var r=0;r<i.length;r++){var o=i[r].exist;if(!(i[r].option||null!=o.id&&null!=t.id||null==t.name||So(t)||So(o)||o.name!==t.name+""))return i[r].option=t,void(e[n]=null)}}}),gf(e,function(t){if(pf(t)){for(var e=0;e<i.length;e++){var n=i[e].exist;if(!i[e].option&&!So(n)&&null==t.id){i[e].option=t;break}}e>=i.length&&i.push({option:t})}}),i}function bo(t){var e=z();gf(t,function(t){var i=t.exist;i&&e.set(i.id,t)}),gf(t,function(t){var i=t.option;L(!i||null==i.id||!e.get(i.id)||e.get(i.id)===t,"id duplicates: "+(i&&i.id)),i&&null!=i.id&&e.set(i.id,t),!t.keyInfo&&(t.keyInfo={})}),gf(t,function(t){var i=t.exist,n=t.option,r=t.keyInfo;if(pf(n)){if(r.name=null!=n.name?n.name+"":i?i.name:"\x00-",i)r.id=i.id;else if(null!=n.id)r.id=n.id+"";else{var o=0;do r.id="\x00"+r.name+"\x00"+o++;while(e.get(r.id))}e.set(r.id,t)}})}function So(t){return pf(t)&&t.id&&0===(t.id+"").indexOf("\x00_ec_\x00")}function Mo(t,e){return null!=e.dataIndexInside?e.dataIndexInside:null!=e.dataIndex?y(e.dataIndex)?f(e.dataIndex,function(e){return t.indexOfRawIndex(e)}):t.indexOfRawIndex(e.dataIndex):null!=e.name?y(e.name)?f(e.name,function(e){return t.indexOfName(e)}):t.indexOfName(e.name):void 0}function To(t,e,i){if(_(e)){var n={};n[e+"Index"]=0,e=n}var r=i&&i.defaultMainType;!r||ko(e,r+"Index")||ko(e,r+"Id")||ko(e,r+"Name")||(e[r+"Index"]=0);var o={};return gf(e,function(n,r){var n=e[r];if("dataIndex"===r||"dataIndexInside"===r)return void(o[r]=n);var a=r.match(/^(\w+)(Index|Id|Name)$/)||[],s=a[1],h=(a[2]||"").toLowerCase();if(!(!s||!h||null==n||"index"===h&&"none"===n||i&&i.includeMainTypes&&l(i.includeMainTypes,s)<0)){var u={mainType:s};("index"!==h||"all"!==n)&&(u[h]=n);var c=t.queryComponents(u);o[s+"Models"]=c,o[s+"Model"]=c[0]}}),o}function Io(t,e){var i=t.dimensions;e=t.getDimension(e);for(var n=0;n<i.length;n++){var r=t.getDimensionInfo(i[n]);if(r.name===e)return r.coordDim}}function Co(t,e){var i=[];return gf(t.dimensions,function(n){var r=t.getDimensionInfo(n);r.coordDim===e&&(i[r.coordDimIndex]=r.name)}),i}function Ao(t,e){var i=[];return gf(t.dimensions,function(n){var r=t.getDimensionInfo(n),o=r.otherDims,a=o[e];null!=a&&a!==!1&&(i[a]=r.name)}),i}function ko(t,e){return t&&t.hasOwnProperty(e)}function Lo(t){return[t||"",xf++,Math.random()].join(_f)}function Do(t){var e={};return t.registerSubTypeDefaulter=function(t,i){t=Ln(t),e[t.main]=i},t.determineSubType=function(i,n){var r=n.type;if(!r){var o=Ln(i).main;t.hasSubTypes(i)&&e[o]&&(r=e[o](n))}return r},t}function Po(t,e){function i(t){var i={},o=[];return d(t,function(a){var s=n(i,a),h=s.originalDeps=e(a),u=r(h,t);s.entryCount=u.length,0===s.entryCount&&o.push(a),d(u,function(t){l(s.predecessor,t)<0&&s.predecessor.push(t);var e=n(i,t);l(e.successor,t)<0&&e.successor.push(a)})}),{graph:i,noEntryList:o}}function n(t,e){return t[e]||(t[e]={predecessor:[],successor:[]}),t[e]}function r(t,e){var i=[];return d(t,function(t){l(e,t)>=0&&i.push(t)}),i}t.topologicalTravel=function(t,e,n,r){function o(t){l[t].entryCount--,0===l[t].entryCount&&h.push(t)}function a(t){u[t]=!0,o(t)}if(t.length){var s=i(e),l=s.graph,h=s.noEntryList,u={};for(d(t,function(t){u[t]=!0});h.length;){var c=h.pop(),f=l[c],g=!!u[c];g&&(n.call(r,c,f.originalDeps.slice()),delete u[c]),d(f.successor,g?a:o)}d(u,function(){throw new Error("Circle dependency may exists")})}}}function Oo(t,e,i,n,r){var o=0,a=0;null==n&&(n=1/0),null==r&&(r=1/0);var s=0;e.eachChild(function(l,h){var u,c,d=l.position,f=l.getBoundingRect(),g=e.childAt(h+1),p=g&&g.getBoundingRect();if("horizontal"===t){var v=f.width+(p?-p.x+f.x:0);u=o+v,u>n||l.newline?(o=0,u=v,a+=s+i,s=f.height):s=Math.max(s,f.height)}else{var m=f.height+(p?-p.y+f.y:0);c=a+m,c>r||l.newline?(o+=s+i,a=0,c=m,s=f.width):s=Math.max(s,f.width)}l.newline||(d[0]=o,d[1]=a,"horizontal"===t?o=u+i:a=c+i)})}function zo(t,e,i){i=Tc(i||0);var n=e.width,r=e.height,o=un(t.left,n),a=un(t.top,r),s=un(t.right,n),l=un(t.bottom,r),h=un(t.width,n),u=un(t.height,r),c=i[2]+i[0],d=i[1]+i[3],f=t.aspect;switch(isNaN(h)&&(h=n-s-d-o),isNaN(u)&&(u=r-l-c-a),null!=f&&(isNaN(h)&&isNaN(u)&&(f>n/r?h=.8*n:u=.8*r),isNaN(h)&&(h=f*u),isNaN(u)&&(u=h/f)),isNaN(o)&&(o=n-s-h-d),isNaN(a)&&(a=r-l-u-c),t.left||t.right){case"center":o=n/2-h/2-i[3];break;case"right":o=n-h-d}switch(t.top||t.bottom){case"middle":case"center":a=r/2-u/2-i[0];break;case"bottom":a=r-u-c}o=o||0,a=a||0,isNaN(h)&&(h=n-d-o-(s||0)),isNaN(u)&&(u=r-c-a-(l||0));var g=new Ee(o+i[3],a+i[0],h,u);return g.margin=i,g}function Bo(t,e,i){function n(i,n){var a={},l=0,h={},u=0,c=2;if(wf(i,function(e){h[e]=t[e]}),wf(i,function(t){r(e,t)&&(a[t]=h[t]=e[t]),o(a,t)&&l++,o(h,t)&&u++}),s[n])return o(e,i[1])?h[i[2]]=null:o(e,i[2])&&(h[i[1]]=null),h;if(u!==c&&l){if(l>=c)return a;for(var d=0;d<i.length;d++){var f=i[d];if(!r(a,f)&&r(t,f)){a[f]=t[f];break}}return a}return h}function r(t,e){return t.hasOwnProperty(e)}function o(t,e){return null!=t[e]&&"auto"!==t[e]}function a(t,e,i){wf(t,function(t){e[t]=i[t]})}!w(i)&&(i={});var s=i.ignoreSize;!y(s)&&(s=[s,s]);var l=n(Sf[0],0),h=n(Sf[1],1);a(Sf[0],t,l),a(Sf[1],t,h)}function Eo(t){return Ro({},t)}function Ro(t,e){return e&&t&&wf(bf,function(i){e.hasOwnProperty(i)&&(t[i]=e[i])}),t}function No(t){var e=[];return d(Cf.getClassesByMainType(t),function(t){If.apply(e,t.prototype.dependencies||[])}),f(e,function(t){return Ln(t).main})}function Fo(t,e){d(e,function(e,r){Cf.hasClass(r)||("object"==typeof e?t[r]=t[r]?n(t[r],e,!1):i(e):null==t[r]&&(t[r]=e))})}function Ho(t){t=t,this.option={},this.option[Rf]=1,this._componentsMap=z({series:[]}),this._seriesIndices=null,Fo(t,this._theme.option),n(t,kf,!1),this.mergeOption(t)}function Vo(t,e){y(e)||(e=e?[e]:[]);var i={};return Df(e,function(e){i[e]=(t.get(e)||[]).slice()}),i}function Wo(t,e,i){var n=e.type?e.type:i?i.subType:Cf.determineSubType(t,e);return n}function Go(t){return Of(t,function(t){return t.componentIndex})||[]}function Xo(t,e){return e.hasOwnProperty("subType")?Pf(t,function(t){return t.subType===e.subType}):t}function qo(t){if(!t._seriesIndices)throw new Error("Option should contains series.")}function Yo(t){d(Ff,function(e){this[e]=v(t[e],t)},this)}function Uo(){this._coordinateSystems=[]}function Zo(t){this._api=t,this._timelineOptions=[],this._mediaList=[],this._mediaDefault,this._currentMediaIndices=[],this._optionBackup,this._newBaseOption}function jo(t,e,i){var n,r,o=[],a=[],s=t.timeline;if(t.baseOption&&(r=t.baseOption),(s||t.options)&&(r=r||{},o=(t.options||[]).slice()),t.media){r=r||{};var l=t.media;Vf(l,function(t){t&&t.option&&(t.query?a.push(t):n||(n=t))})}return r||(r=t),r.timeline||(r.timeline=s),Vf([r].concat(o).concat(f(a,function(t){return t.option})),function(t){Vf(e,function(e){e(t,i)})}),{baseOption:r,timelineOptions:o,mediaDefault:n,mediaList:a}}function $o(t,e,i){var n={width:e,height:i,aspectratio:e/i},r=!0;return d(t,function(t,e){var i=e.match(qf);if(i&&i[1]&&i[2]){var o=i[1],a=i[2].toLowerCase();Ko(n[a],t,o)||(r=!1)}}),r}function Ko(t,e,i){return"min"===i?t>=e:"max"===i?e>=t:t===e}function Qo(t,e){return t.join(",")===e.join(",")}function Jo(t,e){e=e||{},Vf(e,function(e,i){if(null!=e){var n=t[i];if(Cf.hasClass(i)){e=vo(e),n=vo(n);var r=wo(n,e);t[i]=Gf(r,function(t){return t.option&&t.exist?Xf(t.exist,t.option,!0):t.exist||t.option})}else t[i]=Xf(n,e,!0)}})}function ta(t){var e=t&&t.itemStyle;if(e)for(var i=0,r=Zf.length;r>i;i++){var o=Zf[i],a=e.normal,s=e.emphasis;a&&a[o]&&(t[o]=t[o]||{},t[o].normal?n(t[o].normal,a[o]):t[o].normal=a[o],a[o]=null),s&&s[o]&&(t[o]=t[o]||{},t[o].emphasis?n(t[o].emphasis,s[o]):t[o].emphasis=s[o],s[o]=null)}}function ea(t,e){var i=Uf(t)&&t[e],n=Uf(i)&&i.textStyle;if(n)for(var r=0,o=vf.length;o>r;r++){var e=vf[r];n.hasOwnProperty(e)&&(i[e]=n[e])}}function ia(t){Uf(t)&&(ea(t,"normal"),ea(t,"emphasis"))}function na(t){if(Uf(t)){ta(t),ia(t.label),ia(t.upperLabel),ia(t.edgeLabel);
var e=t.markPoint;ta(e),ia(e&&e.label);var i=t.markLine;ta(t.markLine),ia(i&&i.label);var n=t.markArea;ia(n&&n.label),ea(t,"axisLabel"),ea(t,"title"),ea(t,"detail");var r=t.data;if(r)for(var o=0;o<r.length;o++)ta(r[o]),ia(r[o]&&r[o].label);var e=t.markPoint;if(e&&e.data)for(var a=e.data,o=0;o<a.length;o++)ta(a[o]),ia(a[o]&&a[o].label);var i=t.markLine;if(i&&i.data)for(var s=i.data,o=0;o<s.length;o++)y(s[o])?(ta(s[o][0]),ia(s[o][0]&&s[o][0].label),ta(s[o][1]),ia(s[o][1]&&s[o][1].label)):(ta(s[o]),ia(s[o]&&s[o].label))}}function ra(t){return y(t)?t:t?[t]:[]}function oa(t){return(y(t)?t[0]:t)||{}}function aa(t,e){e=e.split(",");for(var i=t,n=0;n<e.length&&(i=i&&i[e[n]],null!=i);n++);return i}function sa(t,e,i,n){e=e.split(",");for(var r,o=t,a=0;a<e.length-1;a++)r=e[a],null==o[r]&&(o[r]={}),o=o[r];(n||null==o[e[a]])&&(o[e[a]]=i)}function la(t){d($f,function(e){e[0]in t&&!(e[1]in t)&&(t[e[1]]=t[e[0]])})}function ha(){this.group=new Pu,this.uid=Lo("viewChart")}function ua(t,e){if(t&&(t.trigger(e),"group"===t.type))for(var i=0;i<t.childCount();i++)ua(t.childAt(i),e)}function ca(t,e,i){var n=Mo(t,e);null!=n?d(vo(n),function(e){ua(t.getItemGraphicEl(e),i)}):t.eachItemGraphicEl(function(t){ua(t,i)})}function da(t,e,i){function n(){u=(new Date).getTime(),c=null,t.apply(a,s||[])}var r,o,a,s,l,h=0,u=0,c=null;e=e||0;var d=function(){r=(new Date).getTime(),a=this,s=arguments;var t=l||e,d=l||i;l=null,o=r-(d?h:u)-t,clearTimeout(c),d?c=setTimeout(n,t):o>=0?n():c=setTimeout(n,-o),h=r};return d.clear=function(){c&&(clearTimeout(c),c=null)},d.debounceNextCall=function(t){l=t},d}function fa(t,e,i,n){var r=t[e];if(r){var o=r[rg]||r,a=r[ag],s=r[og];if(s!==i||a!==n){if(null==i||!n)return t[e]=o;r=t[e]=da(o,i,"debounce"===n),r[rg]=o,r[ag]=n,r[og]=i}return r}}function ga(t){return function(e,i,n){e=e&&e.toLowerCase(),eu.prototype[t].call(this,e,i,n)}}function pa(){eu.call(this)}function va(t,e,n){function r(t,e){return t.prio-e.prio}n=n||{},"string"==typeof e&&(e=Bg[e]),this.id,this.group,this._dom=t;var o="canvas";o=("undefined"==typeof window?global:window).__ECHARTS__DEFAULT__RENDERER__||o;var a=this._zr=an(t,{renderer:n.renderer||o,devicePixelRatio:n.devicePixelRatio,width:n.width,height:n.height});this._throttledZrFlush=da(v(a.flush,a),17);var e=i(e);e&&Jf(e,!0),this._theme=e,this._chartsViews=[],this._chartsMap={},this._componentsViews=[],this._componentsMap={},this._coordSysMgr=new Uo,this._api=Pa(this),eu.call(this),this._messageCenter=new pa,this._initEvents(),this.resize=v(this.resize,this),this._pendingActions=[],Xe(zg,r),Xe(Dg,r),a.animation.on("frame",this._onframe,this),D(this)}function ma(t,e,i){var n,r=this._model,o=this._coordSysMgr.getCoordinateSystems();e=To(r,e);for(var a=0;a<o.length;a++){var s=o[a];if(s[t]&&null!=(n=s[t](r,e,i)))return n}console.warn("No coordinate system that supports "+t+" found by the given finder.")}function ya(t,e,i,n,r){function o(n){n&&n.__alive&&n[e]&&n[e](n.__model,a,t._api,i)}var a=t._model;if(!n)return void ug(t._componentsViews.concat(t._chartsViews),o);var s={};s[n+"Id"]=i[n+"Id"],s[n+"Index"]=i[n+"Index"],s[n+"Name"]=i[n+"Name"];var l={mainType:n,query:s};r&&(l.subType=r),a&&a.eachComponent(l,function(e){o(t["series"===n?"_chartsMap":"_componentsMap"][e.__viewId])},t)}function xa(t,e){var i=t.type,n=t.escapeConnect,r=kg[i],s=r.actionInfo,l=(s.update||"update").split(":"),h=l.pop();l=null!=l[0]&&cg(l[0]),this[bg]=!0;var u=[t],c=!1;t.batch&&(c=!0,u=f(t.batch,function(e){return e=a(o({},e),t),e.batch=null,e}));var d,g=[],p="highlight"===i||"downplay"===i;ug(u,function(t){d=r.action(t,this._model,this._api),d=d||o({},t),d.type=s.event||d.type,g.push(d),p?ya(this,h,t,"series"):l&&ya(this,h,t,l.main,l.sub)},this),"none"===h||p||l||(this[Mg]?(Cg.prepareAndUpdate.call(this,t),this[Mg]=!1):Cg[h].call(this,t)),d=c?{type:s.event||i,escapeConnect:n,batch:g}:g[0],this[bg]=!1,!e&&this._messageCenter.trigger(d.type,d)}function _a(t){for(var e=this._pendingActions;e.length;){var i=e.shift();xa.call(this,i,t)}}function wa(t){!t&&this.trigger("updated")}function ba(t,e,i){var n=this._api;ug(this._componentsViews,function(r){var o=r.__model;r[t](o,e,n,i),Da(o,r)},this),e.eachSeries(function(r){var o=this._chartsMap[r.__viewId];o[t](r,e,n,i),Da(r,o),La(r,o)},this),ka(this._zr,e),ug(Og,function(t){t(e,n)})}function Sa(t,e){for(var i="component"===t,n=i?this._componentsViews:this._chartsViews,r=i?this._componentsMap:this._chartsMap,o=this._zr,a=0;a<n.length;a++)n[a].__alive=!1;e[i?"eachComponent":"eachSeries"](function(t,a){if(i){if("series"===t)return}else a=t;var s="_ec_"+a.id+"_"+a.type,l=r[s];if(!l){var h=cg(a.type),u=i?eg.getClass(h.main,h.sub):ha.getClass(h.sub);if(!u)return;l=new u,l.init(e,this._api),r[s]=l,n.push(l),o.add(l.group)}a.__viewId=l.__id=s,l.__alive=!0,l.__model=a,l.group.__ecComponentInfo={mainType:a.mainType,index:a.componentIndex}},this);for(var a=0;a<n.length;){var s=n[a];s.__alive?a++:(o.remove(s.group),s.dispose(e,this._api),n.splice(a,1),delete r[s.__id],s.__id=s.group.__ecComponentInfo=null)}}function Ma(t,e){ug(Dg,function(i){i.func(t,e)})}function Ta(t){var e={};t.eachSeries(function(t){var i=t.get("stack"),n=t.getData();if(i&&"list"===n.type){var r=e[i];e.hasOwnProperty(i)&&r&&(n.stackedOn=r),e[i]=n}})}function Ia(t,e){var i=this._api;ug(zg,function(n){n.isLayout&&n.func(t,i,e)})}function Ca(t,e,i){var n=this._api;t.clearColorPalette(),t.eachSeries(function(t){t.clearColorPalette()}),ug(zg,function(r){(!i||!r.isLayout)&&r.func(t,n,e)})}function Aa(t,e){var i=this._api;ug(this._componentsViews,function(n){var r=n.__model;n.render(r,t,i,e),Da(r,n)},this),ug(this._chartsViews,function(t){t.__alive=!1},this),t.eachSeries(function(n){var r=this._chartsMap[n.__viewId];r.__alive=!0,r.render(n,t,i,e),r.group.silent=!!n.get("silent"),Da(n,r),La(n,r)},this),ka(this._zr,t),ug(this._chartsViews,function(e){e.__alive||e.remove(t,i)},this)}function ka(t,e){var i=t.storage,n=0;i.traverse(function(t){t.isGroup||n++}),n>e.get("hoverLayerThreshold")&&!Eh.node&&i.traverse(function(t){t.isGroup||(t.useHoverLayer=!0)})}function La(t,e){var i=0;e.group.traverse(function(t){"group"===t.type||t.ignore||i++});var n=+t.get("progressive"),r=i>t.get("progressiveThreshold")&&n&&!Eh.node;r&&e.group.traverse(function(t){t.isGroup||(t.progressive=r?Math.floor(i++/n):-1,r&&t.stopAnimation(!0))});var o=t.get("blendMode")||null;!Eh.canvasSupported&&o&&"source-over"!==o&&console.warn("Only canvas support blendMode"),e.group.traverse(function(t){t.isGroup||t.setStyle("blend",o)})}function Da(t,e){var i=t.get("z"),n=t.get("zlevel");e.group.traverse(function(t){"group"!==t.type&&(null!=i&&(t.z=i),null!=n&&(t.zlevel=n))})}function Pa(t){var e=t._coordSysMgr;return o(new Yo(t),{getCoordinateSystems:v(e.getCoordinateSystems,e),getComponentByElement:function(e){for(;e;){var i=e.__ecComponentInfo;if(null!=i)return t._model.getComponent(i.mainType,i.index);e=e.parent}}})}function Oa(t){function e(t,e){for(var i=0;i<t.length;i++){var n=t[i];n[o]=e}}var i=0,n=1,r=2,o="__connectUpdateStatus";d(Lg,function(a,s){t._messageCenter.on(s,function(a){if(Ng[t.group]&&t[o]!==i){if(a&&a.escapeConnect)return;var s=t.makeActionFromEvent(a),l=[];d(Rg,function(e){e!==t&&e.group===t.group&&l.push(e)}),e(l,i),ug(l,function(t){t[o]!==n&&t.dispatchAction(s)}),e(l,r)}})})}function za(t,e,i){if(wc.replace(".","")-0<fg.zrender.replace(".","")-0)throw new Error("zrender/src "+wc+" is too old for ECharts "+dg+". Current version need ZRender "+fg.zrender+"+");if(!t)throw new Error("Initialize failed: invalid dom.");var n=Na(t);if(n)return console.warn("There is a chart instance already initialized on the dom."),n;!S(t)||"CANVAS"===t.nodeName.toUpperCase()||(t.clientWidth||i&&null!=i.width)&&(t.clientHeight||i&&null!=i.height)||console.warn("Can't get dom width or height");var r=new va(t,e,i);return r.id="ec_"+Fg++,Rg[r.id]=r,t.setAttribute?t.setAttribute(Vg,r.id):t[Vg]=r.id,Oa(r),r}function Ba(t){if(y(t)){var e=t;t=null,d(e,function(e){null!=e.group&&(t=e.group)}),t=t||"g_"+Hg++,d(e,function(e){e.group=t})}return Ng[t]=!0,t}function Ea(t){Ng[t]=!1}function Ra(t){"string"==typeof t?t=Rg[t]:t instanceof va||(t=Na(t)),t instanceof va&&!t.isDisposed()&&t.dispose()}function Na(t){var e;return e=t.getAttribute?t.getAttribute(Vg):t[Vg],Rg[e]}function Fa(t){return Rg[t]}function Ha(t,e){Bg[t]=e}function Va(t){Pg.push(t)}function Wa(t,e){if("function"==typeof t&&(e=t,t=gg),isNaN(t))throw new Error("Unkown processor priority");Dg.push({prio:t,func:e})}function Ga(t){Og.push(t)}function Xa(t,e,i){"function"==typeof e&&(i=e,e="");var n=w(t)?t.type:[t,t={event:e}][0];t.event=(t.event||n).toLowerCase(),e=t.event,L(Tg.test(n)&&Tg.test(e)),kg[n]||(kg[n]={action:i,actionInfo:t}),Lg[e]=n}function qa(t,e){Uo.register(t,e)}function Ya(t){var e=Uo.get(t);return e?e.getDimensionsInfo?e.getDimensionsInfo():e.dimensions.slice():void 0}function Ua(t,e){if("function"==typeof t&&(e=t,t=vg),isNaN(t))throw new Error("Unkown layout priority");zg.push({prio:t,func:e,isLayout:!0})}function Za(t,e){if("function"==typeof t&&(e=t,t=yg),isNaN(t))throw new Error("Unkown visual priority");zg.push({prio:t,func:e})}function ja(t,e){Eg[t]=e}function $a(t){return Cf.extend(t)}function Ka(t){return eg.extend(t)}function Qa(t){return tg.extend(t)}function Ja(t){return ha.extend(t)}function ts(t){$h.createCanvas(t)}function es(t){return t.get("stack")||Xg+t.seriesIndex}function is(t){return t.dim+t.index}function ns(t,e){var i=[],n=t.axis,r="axis0";if("category"===n.type){for(var o=n.getBandWidth(),s=0;s<t.count;s++)i.push(a({bandWidth:o,axisKey:r,stackId:Xg+s},t));for(var l=os(i,e),h=[],s=0;s<t.count;s++){var u=l[r][Xg+s];u.offsetCenter=u.offset+u.width/2,h.push(u)}return h}}function rs(t,e){var i=f(t,function(t){var e=t.getData(),i=t.coordinateSystem,n=i.getBaseAxis(),r=n.getExtent(),o="category"===n.type?n.getBandWidth():Math.abs(r[1]-r[0])/e.count(),a=un(t.get("barWidth"),o),s=un(t.get("barMaxWidth"),o),l=t.get("barGap"),h=t.get("barCategoryGap");return{bandWidth:o,barWidth:a,barMaxWidth:s,barGap:l,barCategoryGap:h,axisKey:is(n),stackId:es(t)}});return os(i,e)}function os(t){var e={};d(t,function(t){var i=t.axisKey,n=t.bandWidth,r=e[i]||{bandWidth:n,remainedWidth:n,autoWidthCount:0,categoryGap:"20%",gap:"30%",stacks:{}},o=r.stacks;e[i]=r;var a=t.stackId;o[a]||r.autoWidthCount++,o[a]=o[a]||{width:0,maxWidth:0};var s=t.barWidth;s&&!o[a].width&&(o[a].width=s,s=Math.min(r.remainedWidth,s),r.remainedWidth-=s);var l=t.barMaxWidth;l&&(o[a].maxWidth=l);var h=t.barGap;null!=h&&(r.gap=h);var u=t.barCategoryGap;null!=u&&(r.categoryGap=u)});var i={};return d(e,function(t,e){i[e]={};var n=t.stacks,r=t.bandWidth,o=un(t.categoryGap,r),a=un(t.gap,1),s=t.remainedWidth,l=t.autoWidthCount,h=(s-o)/(l+(l-1)*a);h=Math.max(h,0),d(n,function(t){var e=t.maxWidth;e&&h>e&&(e=Math.min(e,s),t.width&&(e=Math.min(e,t.width)),s-=e,t.width=e,l--)}),h=(s-o)/(l+(l-1)*a),h=Math.max(h,0);var u,c=0;d(n,function(t){t.width||(t.width=h),u=t,c+=t.width*(1+a)}),u&&(c-=u.width*a);var f=-c/2;d(n,function(t,n){i[e][n]=i[e][n]||{offset:f,width:t.width},f+=t.width*(1+a)})}),i}function as(t,e){var i=rs(p(e.getSeriesByType(t),function(t){return!e.isSeriesFiltered(t)&&t.coordinateSystem&&"cartesian2d"===t.coordinateSystem.type})),n={},r={};e.eachSeriesByType(t,function(t){if("cartesian2d"===t.coordinateSystem.type){var e=t.getData(),o=t.coordinateSystem,a=o.getBaseAxis(),s=es(t),l=i[is(a)][s],h=l.offset,u=l.width,c=o.getOtherAxis(a),d=t.get("barMinHeight")||0,f=a.onZero?c.toGlobalCoord(c.dataToCoord(0)):c.getGlobalExtent()[0],g=[t.coordDimToDataDim("x")[0],t.coordDimToDataDim("y")[0]],p=e.mapArray(g,function(t,e){return o.dataToPoint([t,e])},!0);n[s]=n[s]||[],r[s]=r[s]||[],e.setLayout({offset:h,size:u}),e.each(t.coordDimToDataDim(c.dim)[0],function(t,i){if(!isNaN(t)){n[s][i]||(n[s][i]={p:f,n:f},r[s][i]={p:f,n:f});var o,a,l,g,v=t>=0?"p":"n",m=p[i],y=n[s][i][v],x=r[s][i][v];c.isHorizontal()?(o=y,a=m[1]+h,l=m[0]-x,g=u,r[s][i][v]+=l,Math.abs(l)<d&&(l=(0>l?-1:1)*d),n[s][i][v]+=l):(o=m[0]+h,a=y,l=u,g=m[1]-x,r[s][i][v]+=g,Math.abs(g)<d&&(g=(0>=g?-1:1)*d),n[s][i][v]+=g),e.setItemLayout(i,{x:o,y:a,width:l,height:g})}},!0)}},this)}function ss(t){this._setting=t||{},this._extent=[1/0,-1/0],this._interval=0,this.init&&this.init.apply(this,arguments)}function ls(t,e,i,n){var r={},o=t[1]-t[0],a=r.interval=_n(o/e,!0);null!=i&&i>a&&(a=r.interval=i),null!=n&&a>n&&(a=r.interval=n);var s=r.intervalPrecision=hs(a),l=r.niceTickExtent=[Zg(Math.ceil(t[0]/a)*a,s),Zg(Math.floor(t[1]/a)*a,s)];return cs(l,t),r}function hs(t){return dn(t)+2}function us(t,e,i){t[e]=Math.max(Math.min(t[e],i[1]),i[0])}function cs(t,e){!isFinite(t[0])&&(t[0]=e[0]),!isFinite(t[1])&&(t[1]=e[1]),us(t,0,e),us(t,1,e),t[0]>t[1]&&(t[0]=t[1])}function ds(t,e,i,n){var r=[];if(!t)return r;var o=1e4;e[0]<i[0]&&r.push(e[0]);for(var a=i[0];a<=i[1]&&(r.push(a),a=Zg(a+t,n),a!==r[r.length-1]);)if(r.length>o)return[];return e[1]>(r.length?r[r.length-1]:i[1])&&r.push(e[1]),r}function fs(t,e){return up(t,hp(e))}function gs(t,e){var i,n,r,o=t.type,a=e.getMin(),s=e.getMax(),l=null!=a,h=null!=s,u=t.getExtent();return"ordinal"===o?i=(e.get("data")||[]).length:(n=e.get("boundaryGap"),y(n)||(n=[n||0,n||0]),"boolean"==typeof n[0]&&(console.warn('Boolean type for boundaryGap is only allowed for ordinal axis. Please use string in percentage instead, e.g., "20%". Currently, boundaryGap is set to be 0.'),n=[0,0]),n[0]=un(n[0],1),n[1]=un(n[1],1),r=u[1]-u[0]||Math.abs(u[0])),null==a&&(a="ordinal"===o?i?0:0/0:u[0]-n[0]*r),null==s&&(s="ordinal"===o?i?i-1:0/0:u[1]+n[1]*r),"dataMin"===a?a=u[0]:"function"==typeof a&&(a=a({min:u[0],max:u[1]})),"dataMax"===s?s=u[1]:"function"==typeof s&&(s=s({min:u[0],max:u[1]})),(null==a||!isFinite(a))&&(a=0/0),(null==s||!isFinite(s))&&(s=0/0),t.setBlank(M(a)||M(s)),e.getNeedCrossZero()&&(a>0&&s>0&&!l&&(a=0),0>a&&0>s&&!h&&(s=0)),[a,s]}function ps(t,e){var i=gs(t,e),n=null!=e.getMin(),r=null!=e.getMax(),o=e.get("splitNumber");"log"===t.type&&(t.base=e.get("logBase"));var a=t.type;t.setExtent(i[0],i[1]),t.niceExtent({splitNumber:o,fixMin:n,fixMax:r,minInterval:"interval"===a||"time"===a?e.get("minInterval"):null,maxInterval:"interval"===a||"time"===a?e.get("maxInterval"):null});var s=e.get("interval");null!=s&&t.setInterval&&t.setInterval(s)}function vs(t,e){if(e=e||t.get("type"))switch(e){case"category":return new Ug(t.getCategories(),[1/0,-1/0]);case"value":return new $g;default:return(ss.getClass(e)||$g).create(t)}}function ms(t){var e=t.scale.getExtent(),i=e[0],n=e[1];return!(i>0&&n>0||0>i&&0>n)}function ys(t,e,i,n,r){var o,a=0,s=0,l=(n-r)/180*Math.PI,h=1;e.length>40&&(h=Math.floor(e.length/40));for(var u=0;u<t.length;u+=h){var c=t[u],d=ei(e[u],i,"center","top");d.x+=c*Math.cos(l),d.y+=c*Math.sin(l),d.width*=1.3,d.height*=1.3,o?o.intersect(d)?(s++,a=Math.max(a,s)):(o.union(d),s=0):o=d.clone()}return 0===a&&h>1?h:(a+1)*h-1}function xs(t,e){var i=t.scale,n=i.getTicksLabels(),r=i.getTicks();return"string"==typeof e?(e=function(t){return function(e){return t.replace("{value}",null!=e?e:"")}}(e),f(n,e)):"function"==typeof e?f(r,function(i,n){return e(_s(t,i),n)},this):n}function _s(t,e){return"category"===t.type?t.scale.getLabel(e):e}function ws(t){return this._axes[t]}function bs(t){vp.call(this,t)}function Ss(t,e){var i=t[1]-t[0],n=e,r=i/n/2;t[0]+=r,t[1]-=r}function Ms(t){return w(t)&&null!=t.value?t.value:t+""}function Ts(t,e){return e.type||(e.data?"category":"value")}function Is(t,e){return t.getCoordSysModel()===e}function Cs(t,e){var i=e*Math.PI/180,n=t.plain(),r=n.width,o=n.height,a=r*Math.cos(i)+o*Math.sin(i),s=r*Math.sin(i)+o*Math.cos(i),l=new Ee(n.x,n.y,a,s);return l}function As(t){var e,i=t.model,n=i.getFormattedLabels(),r=i.getModel("axisLabel"),o=1,a=n.length;a>40&&(o=Math.ceil(a/40));for(var s=0;a>s;s+=o)if(!t.isLabelIgnored(s)){var l=r.getTextRect(n[s]),h=Cs(l,r.get("rotate")||0);e?e.union(h):e=h}return e}function ks(t,e,i){this._coordsMap={},this._coordsList=[],this._axesMap={},this._axesList=[],this._initCartesian(t,e,i),this.model=t}function Ls(t,e,i){var n=t[e];if(i.onZero){var r=i.onZeroAxisIndex;if(null!=r){var o=n[r];return void(o&&Ds(o)&&(i.onZero=!1))}for(var a in n)if(n.hasOwnProperty(a)){var o=n[a];if(o&&!Ds(o)){r=+a;break}}null==r&&(i.onZero=!1),i.onZeroAxisIndex=r}}function Ds(t){return"category"===t.type||"time"===t.type||!kp(t)}function Ps(t,e){var i=t.getExtent(),n=i[0]+i[1];t.toGlobalCoord="x"===t.dim?function(t){return t+e}:function(t){return n-t+e},t.toLocalCoord="x"===t.dim?function(t){return t-e}:function(t){return n-t+e}}function Os(t){return f(Pp,function(e){var i=t.getReferringComponents(e)[0];if(!i)throw new Error(e+' "'+T(t.get(e+"Index"),t.get(e+"Id"),0)+'" not found');return i})}function zs(t){return"cartesian2d"===t.get("coordinateSystem")}function Bs(t){return t}function Es(t,e,i,n,r){this._old=t,this._new=e,this._oldKeyGetter=i||Bs,this._newKeyGetter=n||Bs,this.context=r}function Rs(t,e,i,n,r){for(var o=0;o<t.length;o++){var a="_ec_"+r[n](t[o],o),s=e[a];null==s?(i.push(a),e[a]=o):(s.length||(e[a]=s=[s]),s.push(o))}}function Ns(t,e){d(Rp.concat(e.__wrappedMethods||[]),function(i){e.hasOwnProperty(i)&&(t[i]=e[i])}),t.__wrappedMethods=e.__wrappedMethods}function Fs(t){this._array=t||[]}function Hs(t){return y(t)||(t=[t]),t}function Vs(t,e){var i=t.dimensions,n=new Np(f(i,t.getDimensionInfo,t),t.hostModel);Ns(n,t);for(var r=n._storage={},o=t._storage,a=0;a<i.length;a++){var s=i[a],h=o[s];r[s]=l(e,s)>=0?new h.constructor(o[s].length):o[s]}return n}function Ws(t,e,n){function r(t,e,i){Xp[e]?t.otherDims[e]=i:(t.coordDim=e,t.coordDimIndex=i,h.set(e,!0))}function o(t,e,i){if(i||null!=e.get(t)){for(var n=0;null!=e.get(t+n);)n++;t+=n}return e.set(t,!0),t}e=e||[],n=n||{},t=(t||[]).slice();var a=(n.dimsDef||[]).slice(),s=z(n.encodeDef),l=z(),h=z(),u=[],c=n.dimCount;if(null==c){var d=Gs(e[0]);c=Math.max(y(d)&&d.length||1,t.length,a.length),Vp(t,function(t){var e=t.dimsDef;e&&(c=Math.max(c,e.length))})}for(var f=0;c>f;f++){var g=Wp(a[f])?{name:a[f]}:a[f]||{},p=g.name,v=u[f]={otherDims:{}};null!=p&&null==l.get(p)&&(v.name=v.tooltipName=p,l.set(p,f)),null!=g.type&&(v.type=g.type)}s.each(function(t,e){t=s.set(e,vo(t).slice()),Vp(t,function(i,n){Wp(i)&&(i=l.get(i)),null!=i&&c>i&&(t[n]=i,r(u[i],e,n))})});var m=0;Vp(t,function(t){var e,t,n,o;Wp(t)?(e=t,t={}):(e=t.name,t=i(t),n=t.dimsDef,o=t.otherDims,t.name=t.coordDim=t.coordDimIndex=t.dimsDef=t.otherDims=null);var a=vo(s.get(e));if(!a.length)for(var l=0;l<(n&&n.length||1);l++){for(;m<u.length&&null!=u[m].coordDim;)m++;m<u.length&&a.push(m++)}Vp(a,function(i,a){var s=u[i];r(Gp(s,t),e,a),null==s.name&&n&&(s.name=s.tooltipName=n[a]),o&&Gp(s.otherDims,o)})});for(var x=n.extraPrefix||"value",_=0;c>_;_++){var v=u[_]=u[_]||{},w=v.coordDim;null==w&&(v.coordDim=o(x,h,n.extraFromZero),v.coordDimIndex=0,v.isExtraCoord=!0),null==v.name&&(v.name=o(v.coordDim,l)),null==v.type&&qp(e,_)&&(v.type="ordinal")}return u}function Gs(t){return y(t)?t:w(t)?t.value:t}function Xs(t){for(var e=0;e<t.length&&null==t[e];)e++;return t[e]}function qs(t){var e=Xs(t);return null!=e&&!y(yo(e))}function Ys(t,e,i){if(t=t||[],!y(t))throw new Error("Invalid data.");var n=e.get("coordinateSystem"),r=Yp[n],o=Uo.get(n),a={encodeDef:e.get("encode"),dimsDef:e.get("dimensions")},s=r&&r(t,e,i,a),h=s&&s.dimensions;h||(h=o&&(o.getDimensionsInfo?o.getDimensionsInfo():o.dimensions.slice())||["x","y"],h=Ws(h,t,a));var u=s?s.categoryIndex:-1,c=new Np(h,e),d=js(s,t),f={},g=u>=0&&qs(t)?function(t,e,i,n){return xo(t)&&(c.hasItemOption=!0),n===u?i:_o(yo(t),h[n])}:function(t,e,i,n){var r=yo(t),o=_o(r&&r[n],h[n]);xo(t)&&(c.hasItemOption=!0);var a=s&&s.categoryAxesModels;return a&&a[e]&&"string"==typeof o&&(f[e]=f[e]||a[e].getCategories(),o=l(f[e],o),0>o&&!isNaN(o)&&(o=+o)),o};return c.hasItemOption=!1,c.initData(t,d,g),c}function Us(t){return"category"!==t&&"time"!==t}function Zs(t){return"category"===t?"ordinal":"time"===t?"time":"float"}function js(t,e){var i,n=[],r=t&&t.dimensions[t.categoryIndex];if(r&&(i=t.categoryAxesModels[r.name]),i){var o=i.getCategories();if(o){var a=e.length;if(y(e[0])&&e[0].length>1){n=[];for(var s=0;a>s;s++)n[s]=o[e[s][t.categoryIndex||0]]}else n=o.slice(0)}}return n}function $s(t,e,i,n,r,o){var a=i.getModel("label.normal"),s=i.getModel("label.emphasis");Yr(t,e,a,s,{labelFetcher:r,labelDataIndex:o,defaultText:r.getRawValue(o),isRectText:!0,autoColor:n}),Ks(t),Ks(e)}function Ks(t,e){"outside"===t.textPosition&&(t.textPosition=e)}function Qs(t,e,i){i.style.text=null,no(i,{shape:{width:0}},e,t,function(){i.parent&&i.parent.remove(i)})}function Js(t,e,i){i.style.text=null,no(i,{shape:{r:i.shape.r0}},e,t,function(){i.parent&&i.parent.remove(i)})}function tl(t,e,i,n,r,o,s,l){var h=e.getItemVisual(i,"color"),u=e.getItemVisual(i,"opacity"),c=n.getModel("itemStyle.normal"),d=n.getModel("itemStyle.emphasis").getBarItemStyle();l||t.setShape("r",c.get("barBorderRadius")||0),t.useStyle(a({fill:h,opacity:u},c.getBarItemStyle()));var f=n.getShallow("cursor");f&&t.attr("cursor",f);var g=s?r.height>0?"bottom":"top":r.width>0?"left":"right";l||$s(t.style,d,n,h,o,i,g),qr(t,d)}function el(t,e){var i=t.get($p)||0;return Math.min(i,Math.abs(e.width),Math.abs(e.height))}function il(t,e){if("image"!==this.type){var i=this.style,n=this.shape;n&&"line"===n.symbolType?i.stroke=t:this.__isEmptyBrush?(i.stroke=t,i.fill=e||"#fff"):(i.fill&&(i.fill=t),i.stroke&&(i.stroke=t)),this.dirty(!1)}}function nl(t,e,i,n,r,o,a){var s=0===t.indexOf("empty");s&&(t=t.substr(5,1).toLowerCase()+t.substr(6));var l;return l=0===t.indexOf("image://")?Cr(t.slice(8),new Ee(e,i,n,r),a?"center":"cover"):0===t.indexOf("path://")?Ir(t.slice(7),{},new Ee(e,i,n,r),a?"center":"cover"):new av({shape:{symbolType:t,x:e,y:i,width:n,height:r}}),l.__isEmptyBrush=s,l.setColor=il,l.setColor(o),l}function rl(t){var e={componentType:t.mainType};return e[t.mainType+"Index"]=t.componentIndex,e}function ol(t,e,i,n){var r,o,a=pn(i-t.rotation),s=n[0]>n[1],l="start"===e&&!s||"start"!==e&&s;return vn(a-sv/2)?(o=l?"bottom":"top",r="center"):vn(a-1.5*sv)?(o=l?"top":"bottom",r="center"):(o="middle",r=1.5*sv>a&&a>sv/2?l?"left":"right":l?"right":"left"),{rotation:a,textAlign:r,textVerticalAlign:o}}function al(t){var e=t.get("tooltip");return t.get("silent")||!(t.get("triggerEvent")||e&&e.show)}function sl(t,e,i){var n=t.get("axisLabel.showMinLabel"),r=t.get("axisLabel.showMaxLabel");e=e||[],i=i||[];var o=e[0],a=e[1],s=e[e.length-1],l=e[e.length-2],h=i[0],u=i[1],c=i[i.length-1],d=i[i.length-2];n===!1?(ll(o),ll(h)):hl(o,a)&&(n?(ll(a),ll(u)):(ll(o),ll(h))),r===!1?(ll(s),ll(c)):hl(l,s)&&(r?(ll(l),ll(d)):(ll(s),ll(c)))}function ll(t){t&&(t.ignore=!0)}function hl(t,e){var i=t&&t.getBoundingRect().clone(),n=e&&e.getBoundingRect().clone();if(i&&n){var r=ee([]);return oe(r,r,-t.rotation),i.applyTransform(ne([],r,t.getLocalTransform())),n.applyTransform(ne([],r,e.getLocalTransform())),i.intersect(n)}}function ul(t){return"middle"===t||"center"===t}function cl(t,e,i){var n=e.axis;if(e.get("axisTick.show")&&!n.scale.isBlank()){for(var r=e.getModel("axisTick"),o=r.getModel("lineStyle"),s=r.get("length"),l=dv(r,i.labelInterval),h=n.getTicksCoords(r.get("alignWithLabel")),u=n.scale.getTicks(),c=e.get("axisLabel.showMinLabel"),d=e.get("axisLabel.showMaxLabel"),f=[],g=[],p=t._transform,v=[],m=h.length,y=0;m>y;y++)if(!cv(n,y,l,m,c,d)){var x=h[y];f[0]=x,f[1]=0,g[0]=x,g[1]=i.tickDirection*s,p&&(Y(f,f,p),Y(g,g,p));var _=new Zd(Lr({anid:"tick_"+u[y],shape:{x1:f[0],y1:f[1],x2:g[0],y2:g[1]},style:a(o.getLineStyle(),{stroke:e.get("axisLine.lineStyle.color")}),z2:2,silent:!0}));t.group.add(_),v.push(_)}return v}}function dl(t,e,i){var n=e.axis,r=T(i.axisLabelShow,e.get("axisLabel.show"));if(r&&!n.scale.isBlank()){var o=e.getModel("axisLabel"),a=o.get("margin"),s=n.scale.getTicks(),l=e.getFormattedLabels(),h=(T(i.labelRotate,o.get("rotate"))||0)*sv/180,u=uv(i.rotation,h,i.labelDirection),c=e.get("data"),f=[],g=al(e),p=e.get("triggerEvent"),v=e.get("axisLabel.showMinLabel"),m=e.get("axisLabel.showMaxLabel");return d(s,function(r,h){if(!cv(n,h,i.labelInterval,s.length,v,m)){var d=o;c&&c[r]&&c[r].textStyle&&(d=new fo(c[r].textStyle,o,e.ecModel));var y=d.getTextColor()||e.get("axisLine.lineStyle.color"),x=n.dataToCoord(r),_=[x,i.labelOffset+i.labelDirection*a],w=n.scale.getLabel(r),b=new Rd({anid:"label_"+r,position:_,rotation:u.rotation,silent:g,z2:10});Ur(b.style,d,{text:l[h],textAlign:d.getShallow("align",!0)||u.textAlign,textVerticalAlign:d.getShallow("verticalAlign",!0)||d.getShallow("baseline",!0)||u.textVerticalAlign,textFill:"function"==typeof y?y("category"===n.type?w:"value"===n.type?r+"":r,h):y}),p&&(b.eventData=rl(e),b.eventData.targetType="axisLabel",b.eventData.value=w),t._dumbGroup.add(b),b.updateTransform(),f.push(b),t.group.add(b),b.decomposeTransform()}}),f}}function fl(t,e){var i={axesInfo:{},seriesInvolved:!1,coordSysAxesInfo:{},coordSysMap:{}};return gl(i,t,e),i.seriesInvolved&&vl(i,t),i}function gl(t,e,i){var n=e.getComponent("tooltip"),r=e.getComponent("axisPointer"),o=r.get("link",!0)||[],a=[];fv(i.getCoordinateSystems(),function(i){function s(n,s,l){var u=l.model.getModel("axisPointer",r),d=u.get("show");if(d&&("auto"!==d||n||bl(u))){null==s&&(s=u.get("triggerTooltip")),u=n?pl(l,c,r,e,n,s):u;var f=u.get("snap"),g=Sl(l.model),p=s||f||"category"===l.type,v=t.axesInfo[g]={key:g,axis:l,coordSys:i,axisPointerModel:u,triggerTooltip:s,involveSeries:p,snap:f,useHandle:bl(u),seriesModels:[]};h[g]=v,t.seriesInvolved|=p;var m=ml(o,l);if(null!=m){var y=a[m]||(a[m]={axesInfo:{}});y.axesInfo[g]=v,y.mapper=o[m].mapper,v.linkGroup=y}}}if(i.axisPointerEnabled){var l=Sl(i.model),h=t.coordSysAxesInfo[l]={};t.coordSysMap[l]=i;var u=i.model,c=u.getModel("tooltip",n);if(fv(i.getAxes(),gv(s,!1,null)),i.getTooltipAxes&&n&&c.get("show")){var d="axis"===c.get("trigger"),f="cross"===c.get("axisPointer.type"),g=i.getTooltipAxes(c.get("axisPointer.axis"));(d||f)&&fv(g.baseAxes,gv(s,f?"cross":!0,d)),f&&fv(g.otherAxes,gv(s,"cross",!1))}}})}function pl(t,e,n,r,o,s){var l=e.getModel("axisPointer"),h={};fv(["type","snap","lineStyle","shadowStyle","label","animation","animationDurationUpdate","animationEasingUpdate","z"],function(t){h[t]=i(l.get(t))}),h.snap="category"!==t.type&&!!s,"cross"===l.get("type")&&(h.type="line");var u=h.label||(h.label={});if(null==u.show&&(u.show=!1),"cross"===o&&(u.show=!0,!s)){var c=h.lineStyle=l.get("crossStyle");c&&a(u,c.textStyle)}return t.model.getModel("axisPointer",new fo(h,n,r))}function vl(t,e){e.eachSeries(function(e){var i=e.coordinateSystem,n=e.get("tooltip.trigger",!0),r=e.get("tooltip.show",!0);i&&"none"!==n&&n!==!1&&"item"!==n&&r!==!1&&e.get("axisPointer.show",!0)!==!1&&fv(t.coordSysAxesInfo[Sl(i.model)],function(t){var n=t.axis;i.getAxis(n.dim)===n&&(t.seriesModels.push(e),null==t.seriesDataCount&&(t.seriesDataCount=0),t.seriesDataCount+=e.getData().count())})},this)}function ml(t,e){for(var i=e.model,n=e.dim,r=0;r<t.length;r++){var o=t[r]||{};if(yl(o[n+"AxisId"],i.id)||yl(o[n+"AxisIndex"],i.componentIndex)||yl(o[n+"AxisName"],i.name))return r}}function yl(t,e){return"all"===t||y(t)&&l(t,e)>=0||t===e}function xl(t){var e=_l(t);if(e){var i=e.axisPointerModel,n=e.axis.scale,r=i.option,o=i.get("status"),a=i.get("value");null!=a&&(a=n.parse(a));var s=bl(i);null==o&&(r.status=s?"show":"hide");var l=n.getExtent().slice();l[0]>l[1]&&l.reverse(),(null==a||a>l[1])&&(a=l[1]),a<l[0]&&(a=l[0]),r.value=a,s&&(r.status=e.axis.scale.isBlank()?"hide":"show")}}function _l(t){var e=(t.ecModel.getComponent("axisPointer")||{}).coordSysAxesInfo;return e&&e.axesInfo[Sl(t)]}function wl(t){var e=_l(t);return e&&e.axisPointerModel}function bl(t){return!!t.get("handle.show")}function Sl(t){return t.type+"||"+t.id}function Ml(t,e,i,n,r,o){var a=pv.getAxisPointerClass(t.axisPointerClass);if(a){var s=wl(e);s?(t._axisPointer||(t._axisPointer=new a)).render(e,s,n,o):Tl(t,n)}}function Tl(t,e,i){var n=t._axisPointer;n&&n.dispose(e,i),t._axisPointer=null}function Il(t,e,i){i=i||{};var n=t.coordinateSystem,r=e.axis,o={},a=r.position,s=r.onZero?"onZero":a,l=r.dim,h=n.getRect(),u=[h.x,h.x+h.width,h.y,h.y+h.height],c={left:0,right:1,top:0,bottom:1,onZero:2},d=e.get("offset")||0,f="x"===l?[u[2]-d,u[3]+d]:[u[0]-d,u[1]+d];if(r.onZero){var g=n.getAxis("x"===l?"y":"x",r.onZeroAxisIndex),p=g.toGlobalCoord(g.dataToCoord(0));f[c.onZero]=Math.max(Math.min(p,f[1]),f[0])}o.position=["y"===l?f[c[s]]:u[0],"x"===l?f[c[s]]:u[3]],o.rotation=Math.PI/2*("x"===l?0:1);var v={top:-1,bottom:1,left:-1,right:1};o.labelDirection=o.tickDirection=o.nameDirection=v[a],o.labelOffset=r.onZero?f[c[a]]-f[c.onZero]:0,e.get("axisTick.inside")&&(o.tickDirection=-o.tickDirection),T(i.labelInside,e.get("axisLabel.inside"))&&(o.labelDirection=-o.labelDirection);var m=e.get("axisLabel.rotate");return o.labelRotate="top"===s?-m:m,o.labelInterval=r.getLabelInterval(),o.z2=1,o}function Cl(t,e,i,n){var r=e.getData(),o=this.dataIndex,a=r.getName(o),s=e.get("selectedOffset");n.dispatchAction({type:"pieToggleSelect",from:t,name:a,seriesId:e.id}),r.each(function(t){Al(r.getItemGraphicEl(t),r.getItemLayout(t),e.isSelected(r.getName(t)),s,i)})}function Al(t,e,i,n,r){var o=(e.startAngle+e.endAngle)/2,a=Math.cos(o),s=Math.sin(o),l=i?n:0,h=[a*l,s*l];r?t.animate().when(200,{position:h}).start("bounceOut"):t.attr("position",h)}function kl(t,e){function i(){o.ignore=o.hoverIgnore,a.ignore=a.hoverIgnore}function n(){o.ignore=o.normalIgnore,a.ignore=a.normalIgnore}Pu.call(this);var r=new Vd({z2:2}),o=new Yd,a=new Rd;this.add(r),this.add(o),this.add(a),this.updateData(t,e,!0),this.on("emphasis",i).on("normal",n).on("mouseover",i).on("mouseout",n)}function Ll(t,e,i,n,r,o,a){function s(e,i,n){for(var r=e;i>r;r++)if(t[r].y+=n,r>e&&i>r+1&&t[r+1].y>t[r].y+t[r].height)return void l(r,n/2);l(i-1,n/2)}function l(e,i){for(var n=e;n>=0&&(t[n].y-=i,!(n>0&&t[n].y>t[n-1].y+t[n-1].height));n--);}function h(t,e,i,n,r,o){for(var a=o>0?e?Number.MAX_VALUE:0:e?Number.MAX_VALUE:0,s=0,l=t.length;l>s;s++)if("center"!==t[s].position){var h=Math.abs(t[s].y-n),u=t[s].len,c=t[s].len2,d=r+u>h?Math.sqrt((r+u+c)*(r+u+c)-h*h):Math.abs(t[s].x-i);e&&d>=a&&(d=a-10),!e&&a>=d&&(d=a+10),t[s].x=i+d*o,a=d}}t.sort(function(t,e){return t.y-e.y});for(var u,c=0,d=t.length,f=[],g=[],p=0;d>p;p++)u=t[p].y-c,0>u&&s(p,d,-u,r),c=t[p].y+t[p].height;0>a-c&&l(d-1,c-a);for(var p=0;d>p;p++)t[p].y>=i?g.push(t[p]):f.push(t[p]);h(f,!1,e,i,n,r),h(g,!0,e,i,n,r)}function Dl(t,e,i,n,r,o){for(var a=[],s=[],l=0;l<t.length;l++)t[l].x<e?a.push(t[l]):s.push(t[l]);Ll(s,e,i,n,1,r,o),Ll(a,e,i,n,-1,r,o);for(var l=0;l<t.length;l++){var h=t[l].linePoints;if(h){var u=h[1][0]-h[2][0];h[2][0]=t[l].x<e?t[l].x+3:t[l].x-3,h[1][1]=h[2][1]=t[l].y,h[1][0]=h[2][0]+u}}}function Pl(t,e,i){var n,r={},o="toggleSelected"===t;return i.eachComponent("legend",function(i){o&&null!=n?i[n?"select":"unSelect"](e.name):(i[t](e.name),n=i.isSelected(e.name));var a=i.getData();d(a,function(t){var e=t.get("name");if("\n"!==e&&""!==e){var n=i.isSelected(e);r[e]=r.hasOwnProperty(e)?r[e]&&n:n}})}),{name:e.name,selected:r}}function Ol(t,e){var i=Tc(e.get("padding")),n=e.getItemStyle(["color","opacity"]);n.fill=e.get("backgroundColor");var t=new Ud({shape:{x:t.x-i[3],y:t.y-i[0],width:t.width+i[1]+i[3],height:t.height+i[0]+i[2],r:e.get("borderRadius")},style:n,silent:!0,z2:-1});return t}function zl(t,e){e.dispatchAction({type:"legendToggleSelect",name:t})}function Bl(t,e,i){var n=i.getZr().storage.getDisplayList()[0];n&&n.useHoverLayer||t.get("legendHoverLink")&&i.dispatchAction({type:"highlight",seriesName:t.name,name:e})}function El(t,e,i){var n=i.getZr().storage.getDisplayList()[0];n&&n.useHoverLayer||t.get("legendHoverLink")&&i.dispatchAction({type:"downplay",seriesName:t.name,name:e})}function Rl(t,e,i){var n=t.getOrient(),r=[1,1];r[n.index]=0,Bo(e,i,{type:"box",ignoreSize:r})}function Nl(t,e,i,n,r){var a=t.axis;if(!a.scale.isBlank()&&a.containData(e)){if(!t.involveSeries)return void i.showPointer(t,e);var s=Fl(e,t),l=s.payloadBatch,h=s.snapToValue;l[0]&&null==r.seriesIndex&&o(r,l[0]),!n&&t.snap&&a.containData(h)&&null!=h&&(e=h),i.showPointer(t,e,l,r),i.showTooltip(t,s,h)}}function Fl(t,e){var i=e.axis,n=i.dim,r=t,o=[],a=Number.MAX_VALUE,s=-1;
return Xv(e.seriesModels,function(e){var l,h,u=e.coordDimToDataDim(n);if(e.getAxisTooltipData){var c=e.getAxisTooltipData(u,t,i);h=c.dataIndices,l=c.nestestValue}else{if(h=e.getData().indicesOfNearest(u[0],t,!1,"category"===i.type?.5:null),!h.length)return;l=e.getData().get(u[0],h[0])}if(null!=l&&isFinite(l)){var d=t-l,f=Math.abs(d);a>=f&&((a>f||d>=0&&0>s)&&(a=f,s=d,r=l,o.length=0),Xv(h,function(t){o.push({seriesIndex:e.seriesIndex,dataIndexInside:t,dataIndex:e.getData().getRawIndex(t)})}))}}),{payloadBatch:o,snapToValue:r}}function Hl(t,e,i,n){t[e.key]={value:i,payloadBatch:n}}function Vl(t,e,i,n){var r=i.payloadBatch,o=e.axis,a=o.model,s=e.axisPointerModel;if(e.triggerTooltip&&r.length){var l=e.coordSys.model,h=Sl(l),u=t.map[h];u||(u=t.map[h]={coordSysId:l.id,coordSysIndex:l.componentIndex,coordSysType:l.type,coordSysMainType:l.mainType,dataByAxis:[]},t.list.push(u)),u.dataByAxis.push({axisDim:o.dim,axisIndex:a.componentIndex,axisType:a.type,axisId:a.id,value:n,valueLabelOpt:{precision:s.get("label.precision"),formatter:s.get("label.formatter")},seriesDataIndices:r.slice()})}}function Wl(t,e,i){var n=i.axesInfo=[];Xv(e,function(e,i){var r=e.axisPointerModel.option,o=t[i];o?(!e.useHandle&&(r.status="show"),r.value=o.value,r.seriesDataIndices=(o.payloadBatch||[]).slice()):!e.useHandle&&(r.status="hide"),"show"===r.status&&n.push({axisDim:e.axis.dim,axisIndex:e.axis.model.componentIndex,value:r.value})})}function Gl(t,e,i,n){if(Ul(e)||!t.list.length)return void n({type:"hideTip"});var r=((t.list[0].dataByAxis[0]||{}).seriesDataIndices||[])[0]||{};n({type:"showTip",escapeConnect:!0,x:e[0],y:e[1],tooltipOption:i.tooltipOption,position:i.position,dataIndexInside:r.dataIndexInside,dataIndex:r.dataIndex,seriesIndex:r.seriesIndex,dataByCoordSys:t.list})}function Xl(t,e,i){var n=i.getZr(),r="axisPointerLastHighlights",o=Yv(n)[r]||{},a=Yv(n)[r]={};Xv(t,function(t){var e=t.axisPointerModel.option;"show"===e.status&&Xv(e.seriesDataIndices,function(t){var e=t.seriesIndex+" | "+t.dataIndex;a[e]=t})});var s=[],l=[];d(o,function(t,e){!a[e]&&l.push(t)}),d(a,function(t,e){!o[e]&&s.push(t)}),l.length&&i.dispatchAction({type:"downplay",escapeConnect:!0,batch:l}),s.length&&i.dispatchAction({type:"highlight",escapeConnect:!0,batch:s})}function ql(t,e){for(var i=0;i<(t||[]).length;i++){var n=t[i];if(e.axis.dim===n.axisDim&&e.axis.model.componentIndex===n.axisIndex)return n}}function Yl(t){var e=t.axis.model,i={},n=i.axisDim=t.axis.dim;return i.axisIndex=i[n+"AxisIndex"]=e.componentIndex,i.axisName=i[n+"AxisName"]=e.name,i.axisId=i[n+"AxisId"]=e.id,i}function Ul(t){return!t||null==t[0]||isNaN(t[0])||null==t[1]||isNaN(t[1])}function Zl(t,e,i){if(!Eh.node){var n=e.getZr();Zv(n).records||(Zv(n).records={}),jl(n,e);var r=Zv(n).records[t]||(Zv(n).records[t]={});r.handler=i}}function jl(t,e){function i(i,n){t.on(i,function(i){var r=Jl(e);jv(Zv(t).records,function(t){t&&n(t,i,r.dispatchAction)}),$l(r.pendings,e)})}Zv(t).initialized||(Zv(t).initialized=!0,i("click",m(Ql,"click")),i("mousemove",m(Ql,"mousemove")),i("globalout",Kl))}function $l(t,e){var i,n=t.showTip.length,r=t.hideTip.length;n?i=t.showTip[n-1]:r&&(i=t.hideTip[r-1]),i&&(i.dispatchAction=null,e.dispatchAction(i))}function Kl(t,e,i){t.handler("leave",null,i)}function Ql(t,e,i,n){e.handler(t,i,n)}function Jl(t){var e={showTip:[],hideTip:[]},i=function(n){var r=e[n.type];r?r.push(n):(n.dispatchAction=i,t.dispatchAction(n))};return{dispatchAction:i,pendings:e}}function th(t,e){if(!Eh.node){var i=e.getZr(),n=(Zv(i).records||{})[t];n&&(Zv(i).records[t]=null)}}function eh(){}function ih(t,e,i,n){nh(Kv(i).lastProp,n)||(Kv(i).lastProp=n,e?no(i,n,t):(i.stopAnimation(),i.attr(n)))}function nh(t,e){if(w(t)&&w(e)){var i=!0;return d(e,function(e,n){i=i&&nh(t[n],e)}),!!i}return t===e}function rh(t,e){t[e.get("label.show")?"show":"hide"]()}function oh(t){return{position:t.position.slice(),rotation:t.rotation||0}}function ah(t,e,i){var n=e.get("z"),r=e.get("zlevel");t&&t.traverse(function(t){"group"!==t.type&&(null!=n&&(t.z=n),null!=r&&(t.zlevel=r),t.silent=i)})}function sh(t){var e,i=t.get("type"),n=t.getModel(i+"Style");return"line"===i?(e=n.getLineStyle(),e.fill=null):"shadow"===i&&(e=n.getAreaStyle(),e.stroke=null),e}function lh(t,e,i,n,r){var o=i.get("value"),a=uh(o,e.axis,e.ecModel,i.get("seriesDataIndices"),{precision:i.get("label.precision"),formatter:i.get("label.formatter")}),s=i.getModel("label"),l=Tc(s.get("padding")||0),h=s.getFont(),u=ei(a,h),c=r.position,d=u.width+l[1]+l[3],f=u.height+l[0]+l[2],g=r.align;"right"===g&&(c[0]-=d),"center"===g&&(c[0]-=d/2);var p=r.verticalAlign;"bottom"===p&&(c[1]-=f),"middle"===p&&(c[1]-=f/2),hh(c,d,f,n);var v=s.get("backgroundColor");v&&"auto"!==v||(v=e.get("axisLine.lineStyle.color")),t.label={shape:{x:0,y:0,width:d,height:f,r:s.get("borderRadius")},position:c.slice(),style:{text:a,textFont:h,textFill:s.getTextColor(),textPosition:"inside",fill:v,stroke:s.get("borderColor")||"transparent",lineWidth:s.get("borderWidth")||0,shadowBlur:s.get("shadowBlur"),shadowColor:s.get("shadowColor"),shadowOffsetX:s.get("shadowOffsetX"),shadowOffsetY:s.get("shadowOffsetY")},z2:10}}function hh(t,e,i,n){var r=n.getWidth(),o=n.getHeight();t[0]=Math.min(t[0]+e,r)-e,t[1]=Math.min(t[1]+i,o)-i,t[0]=Math.max(t[0],0),t[1]=Math.max(t[1],0)}function uh(t,e,i,n,r){var o=e.scale.getLabel(t,{precision:r.precision}),a=r.formatter;if(a){var s={value:_s(e,t),seriesData:[]};d(n,function(t){var e=i.getSeriesByIndex(t.seriesIndex),n=t.dataIndexInside,r=e&&e.getDataParams(n);r&&s.seriesData.push(r)}),_(a)?o=a.replace("{value}",o):x(a)&&(o=a(s))}return o}function ch(t,e,i){var n=te();return oe(n,n,i.rotation),re(n,n,i.position),ao([t.dataToCoord(e),(i.labelOffset||0)+(i.labelDirection||1)*(i.labelMargin||0)],n)}function dh(t,e,i,n,r,o){var a=lv.innerTextLayout(i.rotation,0,i.labelDirection);i.labelMargin=r.get("label.margin"),lh(e,n,r,o,{position:ch(n.axis,t,i),align:a.textAlign,verticalAlign:a.textVerticalAlign})}function fh(t,e,i){return i=i||0,{x1:t[i],y1:t[1-i],x2:e[i],y2:e[1-i]}}function gh(t,e,i){return i=i||0,{x:t[i],y:t[1-i],width:e[i],height:e[1-i]}}function ph(t,e){var i={};return i[e.dim+"AxisIndex"]=e.index,t.getCartesian(i)}function vh(t){return"x"===t.dim?0:1}function mh(t){var e="cubic-bezier(0.23, 1, 0.32, 1)",i="left "+t+"s "+e+",top "+t+"s "+e;return f(rm,function(t){return t+"transition:"+i}).join(";")}function yh(t){var e=[],i=t.get("fontSize"),n=t.getTextColor();return n&&e.push("color:"+n),e.push("font:"+t.getFont()),i&&e.push("line-height:"+Math.round(3*i/2)+"px"),im(["decoration","align"],function(i){var n=t.get(i);n&&e.push("text-"+i+":"+n)}),e.join(";")}function xh(t){var e=[],i=t.get("transitionDuration"),n=t.get("backgroundColor"),r=t.getModel("textStyle"),o=t.get("padding");return i&&e.push(mh(i)),n&&(Eh.canvasSupported?e.push("background-Color:"+n):(e.push("background-Color:#"+we(n)),e.push("filter:alpha(opacity=70)"))),im(["width","color","radius"],function(i){var n="border-"+i,r=nm(n),o=t.get(r);null!=o&&e.push(n+":"+o+("color"===i?"":"px"))}),e.push(yh(r)),null!=o&&e.push("padding:"+Tc(o).join("px ")+"px"),e.join(";")+";"}function _h(t,e){var i=document.createElement("div"),n=this._zr=e.getZr();this.el=i,this._x=e.getWidth()/2,this._y=e.getHeight()/2,t.appendChild(i),this._container=t,this._show=!1,this._hideTimeout;var r=this;i.onmouseenter=function(){r._enterable&&(clearTimeout(r._hideTimeout),r._show=!0),r._inContent=!0},i.onmousemove=function(e){if(e=e||window.event,!r._enterable){var i=n.handler;Zi(t,e,!0),i.dispatch("mousemove",e)}},i.onmouseleave=function(){r._enterable&&r._show&&r.hideLater(r._hideDelay),r._inContent=!1}}function wh(t){for(var e=t.pop();t.length;){var i=t.pop();i&&(i instanceof fo&&(i=i.get("tooltip",!0)),"string"==typeof i&&(i={formatter:i}),e=new fo(i,e,e.ecModel))}return e}function bh(t,e){return t.dispatchAction||v(e.dispatchAction,e)}function Sh(t,e,i,n,r,o,a){var s=Th(i),l=s.width,h=s.height;return null!=o&&(t+l+o>n?t-=l+o:t+=o),null!=a&&(e+h+a>r?e-=h+a:e+=a),[t,e]}function Mh(t,e,i,n,r){var o=Th(i),a=o.width,s=o.height;return t=Math.min(t+a,n)-a,e=Math.min(e+s,r)-s,t=Math.max(t,0),e=Math.max(e,0),[t,e]}function Th(t){var e=t.clientWidth,i=t.clientHeight;if(document.defaultView&&document.defaultView.getComputedStyle){var n=document.defaultView.getComputedStyle(t);n&&(e+=parseInt(n.paddingLeft,10)+parseInt(n.paddingRight,10)+parseInt(n.borderLeftWidth,10)+parseInt(n.borderRightWidth,10),i+=parseInt(n.paddingTop,10)+parseInt(n.paddingBottom,10)+parseInt(n.borderTopWidth,10)+parseInt(n.borderBottomWidth,10))}return{width:e,height:i}}function Ih(t,e,i){var n=i[0],r=i[1],o=5,a=0,s=0,l=e.width,h=e.height;switch(t){case"inside":a=e.x+l/2-n/2,s=e.y+h/2-r/2;break;case"top":a=e.x+l/2-n/2,s=e.y-r-o;break;case"bottom":a=e.x+l/2-n/2,s=e.y+h+o;break;case"left":a=e.x-n-o,s=e.y+h/2-r/2;break;case"right":a=e.x+l+o,s=e.y+h/2-r/2}return[a,s]}function Ch(t){return"center"===t||"middle"===t}function Ah(){if(!fm&&gm){fm=!0;var t=gm.styleSheets;t.length<31?gm.createStyleSheet().addRule(".zrvml","behavior:url(#default#VML)"):t[0].addRule(".zrvml","behavior:url(#default#VML)")}}function kh(t){return parseInt(t,10)}function Lh(t,e){Ah(),this.root=t,this.storage=e;var i=document.createElement("div"),n=document.createElement("div");i.style.cssText="display:inline-block;overflow:hidden;position:relative;width:300px;height:150px;",n.style.cssText="position:absolute;left:0;top:0;",t.appendChild(i),this._vmlRoot=n,this._vmlViewport=i,this.resize();var r=e.delFromStorage,o=e.addToStorage;e.delFromStorage=function(t){r.call(e,t),t&&t.onRemove&&t.onRemove(n)},e.addToStorage=function(t){t.onAdd&&t.onAdd(n),o.call(e,t)},this._firstPaint=!0}function Dh(t){return function(){Iu('In IE8.0 VML mode painter not support method "'+t+'"')}}var Ph=2311,Oh=function(){return Ph++},zh={};zh="undefined"==typeof navigator?{browser:{},os:{},node:!0,canvasSupported:!0,svgSupported:!0}:e(navigator.userAgent);var Bh,Eh=zh,Rh={"[object Function]":1,"[object RegExp]":1,"[object Date]":1,"[object Error]":1,"[object CanvasGradient]":1,"[object CanvasPattern]":1,"[object Image]":1,"[object Canvas]":1},Nh={"[object Int8Array]":1,"[object Uint8Array]":1,"[object Uint8ClampedArray]":1,"[object Int16Array]":1,"[object Uint16Array]":1,"[object Int32Array]":1,"[object Uint32Array]":1,"[object Float32Array]":1,"[object Float64Array]":1},Fh=Object.prototype.toString,Hh=Array.prototype,Vh=Hh.forEach,Wh=Hh.filter,Gh=Hh.slice,Xh=Hh.map,qh=Hh.reduce,Yh=function(){return document.createElement("canvas")},Uh="__ec_primitive__",Zh="_ec_",jh=4;O.prototype={constructor:O,get:function(t){return this[Zh+t]},set:function(t,e){return this[Zh+t]=e,e},each:function(t,e){void 0!==e&&(t=v(t,e));for(var i in this)this.hasOwnProperty(i)&&t(this[i],i.slice(jh))},removeKey:function(t){delete this[Zh+t]}};var $h={createCanvas:function(t){Yh=t}},Kh="undefined"==typeof Float32Array?Array:Float32Array,Qh=X,Jh=q;j.prototype={constructor:j,_dragStart:function(t){var e=t.target;e&&e.draggable&&(this._draggingTarget=e,e.dragging=!0,this._x=t.offsetX,this._y=t.offsetY,this.dispatchToElement($(e,t),"dragstart",t.event))},_drag:function(t){var e=this._draggingTarget;if(e){var i=t.offsetX,n=t.offsetY,r=i-this._x,o=n-this._y;this._x=i,this._y=n,e.drift(r,o,t),this.dispatchToElement($(e,t),"drag",t.event);var a=this.findHover(i,n,e).target,s=this._dropTarget;this._dropTarget=a,e!==a&&(s&&a!==s&&this.dispatchToElement($(s,t),"dragleave",t.event),a&&a!==s&&this.dispatchToElement($(a,t),"dragenter",t.event))}},_dragEnd:function(t){var e=this._draggingTarget;e&&(e.dragging=!1),this.dispatchToElement($(e,t),"dragend",t.event),this._dropTarget&&this.dispatchToElement($(this._dropTarget,t),"drop",t.event),this._draggingTarget=null,this._dropTarget=null}};var tu=Array.prototype.slice,eu=function(){this._$handlers={}};eu.prototype={constructor:eu,one:function(t,e,i){var n=this._$handlers;if(!e||!t)return this;n[t]||(n[t]=[]);for(var r=0;r<n[t].length;r++)if(n[t][r].h===e)return this;return n[t].push({h:e,one:!0,ctx:i||this}),this},on:function(t,e,i){var n=this._$handlers;if(!e||!t)return this;n[t]||(n[t]=[]);for(var r=0;r<n[t].length;r++)if(n[t][r].h===e)return this;return n[t].push({h:e,one:!1,ctx:i||this}),this},isSilent:function(t){var e=this._$handlers;return e[t]&&e[t].length},off:function(t,e){var i=this._$handlers;if(!t)return this._$handlers={},this;if(e){if(i[t]){for(var n=[],r=0,o=i[t].length;o>r;r++)i[t][r].h!=e&&n.push(i[t][r]);i[t]=n}i[t]&&0===i[t].length&&delete i[t]}else delete i[t];return this},trigger:function(t){if(this._$handlers[t]){var e=arguments,i=e.length;i>3&&(e=tu.call(e,1));for(var n=this._$handlers[t],r=n.length,o=0;r>o;){switch(i){case 1:n[o].h.call(n[o].ctx);break;case 2:n[o].h.call(n[o].ctx,e[1]);break;case 3:n[o].h.call(n[o].ctx,e[1],e[2]);break;default:n[o].h.apply(n[o].ctx,e)}n[o].one?(n.splice(o,1),r--):o++}}return this},triggerWithContext:function(t){if(this._$handlers[t]){var e=arguments,i=e.length;i>4&&(e=tu.call(e,1,e.length-1));for(var n=e[e.length-1],r=this._$handlers[t],o=r.length,a=0;o>a;){switch(i){case 1:r[a].h.call(n);break;case 2:r[a].h.call(n,e[1]);break;case 3:r[a].h.call(n,e[1],e[2]);break;default:r[a].h.apply(n,e)}r[a].one?(r.splice(a,1),o--):a++}}return this}};var iu="silent";Q.prototype.dispose=function(){};var nu=["click","dblclick","mousewheel","mouseout","mouseup","mousedown","mousemove","contextmenu"],ru=function(t,e,i,n){eu.call(this),this.storage=t,this.painter=e,this.painterRoot=n,i=i||new Q,this.proxy=i,i.handler=this,this._hovered={},this._lastTouchMoment,this._lastX,this._lastY,j.call(this),d(nu,function(t){i.on&&i.on(t,this[t],this)},this)};ru.prototype={constructor:ru,mousemove:function(t){var e=t.zrX,i=t.zrY,n=this._hovered,r=n.target;r&&!r.__zr&&(n=this.findHover(n.x,n.y),r=n.target);var o=this._hovered=this.findHover(e,i),a=o.target,s=this.proxy;s.setCursor&&s.setCursor(a?a.cursor:"default"),r&&a!==r&&this.dispatchToElement(n,"mouseout",t),this.dispatchToElement(o,"mousemove",t),a&&a!==r&&this.dispatchToElement(o,"mouseover",t)},mouseout:function(t){this.dispatchToElement(this._hovered,"mouseout",t);var e,i=t.toElement||t.relatedTarget;do i=i&&i.parentNode;while(i&&9!=i.nodeType&&!(e=i===this.painterRoot));!e&&this.trigger("globalout",{event:t})},resize:function(){this._hovered={}},dispatch:function(t,e){var i=this[t];i&&i.call(this,e)},dispose:function(){this.proxy.dispose(),this.storage=this.proxy=this.painter=null},setCursorStyle:function(t){var e=this.proxy;e.setCursor&&e.setCursor(t)},dispatchToElement:function(t,e,i){t=t||{};var n=t.target;if(!n||!n.silent){for(var r="on"+e,o=K(e,t,i);n&&(n[r]&&(o.cancelBubble=n[r].call(n,o)),n.trigger(e,o),n=n.parent,!o.cancelBubble););o.cancelBubble||(this.trigger(e,o),this.painter&&this.painter.eachOtherLayer(function(t){"function"==typeof t[r]&&t[r].call(t,o),t.trigger&&t.trigger(e,o)}))}},findHover:function(t,e,i){for(var n=this.storage.getDisplayList(),r={x:t,y:e},o=n.length-1;o>=0;o--){var a;if(n[o]!==i&&!n[o].ignore&&(a=J(n[o],t,e))&&(!r.topTarget&&(r.topTarget=n[o]),a!==iu)){r.target=n[o];break}}return r}},d(["click","mousedown","mouseup","mousewheel","dblclick","contextmenu"],function(t){ru.prototype[t]=function(e){var i=this.findHover(e.zrX,e.zrY),n=i.target;if("mousedown"===t)this._downEl=n,this._downPoint=[e.zrX,e.zrY],this._upEl=n;else if("mosueup"===t)this._upEl=n;else if("click"===t){if(this._downEl!==this._upEl||!this._downPoint||Qh(this._downPoint,[e.zrX,e.zrY])>4)return;this._downPoint=null}this.dispatchToElement(i,t,e)}}),u(ru,eu),u(ru,j);var ou="undefined"==typeof Float32Array?Array:Float32Array,au=ee,su=5e-5,lu=function(t){t=t||{},t.position||(this.position=[0,0]),null==t.rotation&&(this.rotation=0),t.scale||(this.scale=[1,1]),this.origin=this.origin||null},hu=lu.prototype;hu.transform=null,hu.needLocalTransform=function(){return le(this.rotation)||le(this.position[0])||le(this.position[1])||le(this.scale[0]-1)||le(this.scale[1]-1)},hu.updateTransform=function(){var t=this.parent,e=t&&t.transform,i=this.needLocalTransform(),n=this.transform;return i||e?(n=n||te(),i?this.getLocalTransform(n):au(n),e&&(i?ne(n,t.transform,n):ie(n,t.transform)),this.transform=n,this.invTransform=this.invTransform||te(),void se(this.invTransform,n)):void(n&&au(n))},hu.getLocalTransform=function(t){return lu.getLocalTransform(this,t)},hu.setTransform=function(t){var e=this.transform,i=t.dpr||1;e?t.setTransform(i*e[0],i*e[1],i*e[2],i*e[3],i*e[4],i*e[5]):t.setTransform(i,0,0,i,0,0)},hu.restoreTransform=function(t){var e=t.dpr||1;t.setTransform(e,0,0,e,0,0)};var uu=[];hu.decomposeTransform=function(){if(this.transform){var t=this.parent,e=this.transform;t&&t.transform&&(ne(uu,t.invTransform,e),e=uu);var i=e[0]*e[0]+e[1]*e[1],n=e[2]*e[2]+e[3]*e[3],r=this.position,o=this.scale;le(i-1)&&(i=Math.sqrt(i)),le(n-1)&&(n=Math.sqrt(n)),e[0]<0&&(i=-i),e[3]<0&&(n=-n),r[0]=e[4],r[1]=e[5],o[0]=i,o[1]=n,this.rotation=Math.atan2(-e[1]/n,e[0]/i)}},hu.getGlobalScale=function(){var t=this.transform;if(!t)return[1,1];var e=Math.sqrt(t[0]*t[0]+t[1]*t[1]),i=Math.sqrt(t[2]*t[2]+t[3]*t[3]);return t[0]<0&&(e=-e),t[3]<0&&(i=-i),[e,i]},hu.transformCoordToLocal=function(t,e){var i=[t,e],n=this.invTransform;return n&&Y(i,i,n),i},hu.transformCoordToGlobal=function(t,e){var i=[t,e],n=this.transform;return n&&Y(i,i,n),i},lu.getLocalTransform=function(t,e){e=e||[],au(e);var i=t.origin,n=t.scale||[1,1],r=t.rotation||0,o=t.position||[0,0];return i&&(e[4]-=i[0],e[5]-=i[1]),ae(e,e,n),r&&oe(e,e,r),i&&(e[4]+=i[0],e[5]+=i[1]),e[4]+=o[0],e[5]+=o[1],e};var cu={linear:function(t){return t},quadraticIn:function(t){return t*t},quadraticOut:function(t){return t*(2-t)},quadraticInOut:function(t){return(t*=2)<1?.5*t*t:-.5*(--t*(t-2)-1)},cubicIn:function(t){return t*t*t},cubicOut:function(t){return--t*t*t+1},cubicInOut:function(t){return(t*=2)<1?.5*t*t*t:.5*((t-=2)*t*t+2)},quarticIn:function(t){return t*t*t*t},quarticOut:function(t){return 1- --t*t*t*t},quarticInOut:function(t){return(t*=2)<1?.5*t*t*t*t:-.5*((t-=2)*t*t*t-2)},quinticIn:function(t){return t*t*t*t*t},quinticOut:function(t){return--t*t*t*t*t+1},quinticInOut:function(t){return(t*=2)<1?.5*t*t*t*t*t:.5*((t-=2)*t*t*t*t+2)},sinusoidalIn:function(t){return 1-Math.cos(t*Math.PI/2)},sinusoidalOut:function(t){return Math.sin(t*Math.PI/2)},sinusoidalInOut:function(t){return.5*(1-Math.cos(Math.PI*t))},exponentialIn:function(t){return 0===t?0:Math.pow(1024,t-1)},exponentialOut:function(t){return 1===t?1:1-Math.pow(2,-10*t)},exponentialInOut:function(t){return 0===t?0:1===t?1:(t*=2)<1?.5*Math.pow(1024,t-1):.5*(-Math.pow(2,-10*(t-1))+2)},circularIn:function(t){return 1-Math.sqrt(1-t*t)},circularOut:function(t){return Math.sqrt(1- --t*t)},circularInOut:function(t){return(t*=2)<1?-.5*(Math.sqrt(1-t*t)-1):.5*(Math.sqrt(1-(t-=2)*t)+1)},elasticIn:function(t){var e,i=.1,n=.4;return 0===t?0:1===t?1:(!i||1>i?(i=1,e=n/4):e=n*Math.asin(1/i)/(2*Math.PI),-(i*Math.pow(2,10*(t-=1))*Math.sin(2*(t-e)*Math.PI/n)))},elasticOut:function(t){var e,i=.1,n=.4;return 0===t?0:1===t?1:(!i||1>i?(i=1,e=n/4):e=n*Math.asin(1/i)/(2*Math.PI),i*Math.pow(2,-10*t)*Math.sin(2*(t-e)*Math.PI/n)+1)},elasticInOut:function(t){var e,i=.1,n=.4;return 0===t?0:1===t?1:(!i||1>i?(i=1,e=n/4):e=n*Math.asin(1/i)/(2*Math.PI),(t*=2)<1?-.5*i*Math.pow(2,10*(t-=1))*Math.sin(2*(t-e)*Math.PI/n):i*Math.pow(2,-10*(t-=1))*Math.sin(2*(t-e)*Math.PI/n)*.5+1)},backIn:function(t){var e=1.70158;return t*t*((e+1)*t-e)},backOut:function(t){var e=1.70158;return--t*t*((e+1)*t+e)+1},backInOut:function(t){var e=2.5949095;return(t*=2)<1?.5*t*t*((e+1)*t-e):.5*((t-=2)*t*((e+1)*t+e)+2)},bounceIn:function(t){return 1-cu.bounceOut(1-t)},bounceOut:function(t){return 1/2.75>t?7.5625*t*t:2/2.75>t?7.5625*(t-=1.5/2.75)*t+.75:2.5/2.75>t?7.5625*(t-=2.25/2.75)*t+.9375:7.5625*(t-=2.625/2.75)*t+.984375},bounceInOut:function(t){return.5>t?.5*cu.bounceIn(2*t):.5*cu.bounceOut(2*t-1)+.5}};he.prototype={constructor:he,step:function(t,e){if(this._initialized||(this._startTime=t+this._delay,this._initialized=!0),this._paused)return void(this._pausedTime+=e);var i=(t-this._startTime-this._pausedTime)/this._life;if(!(0>i)){i=Math.min(i,1);var n=this.easing,r="string"==typeof n?cu[n]:n,o="function"==typeof r?r(i):i;return this.fire("frame",o),1==i?this.loop?(this.restart(t),"restart"):(this._needsRemove=!0,"destroy"):null}},restart:function(t){var e=(t-this._startTime-this._pausedTime)%this._life;this._startTime=t-e+this.gap,this._pausedTime=0,this._needsRemove=!1},fire:function(t,e){t="on"+t,this[t]&&this[t](this._target,e)},pause:function(){this._paused=!0},resume:function(){this._paused=!1}};var du=function(){this.head=null,this.tail=null,this._len=0},fu=du.prototype;fu.insert=function(t){var e=new gu(t);return this.insertEntry(e),e},fu.insertEntry=function(t){this.head?(this.tail.next=t,t.prev=this.tail,t.next=null,this.tail=t):this.head=this.tail=t,this._len++},fu.remove=function(t){var e=t.prev,i=t.next;e?e.next=i:this.head=i,i?i.prev=e:this.tail=e,t.next=t.prev=null,this._len--},fu.len=function(){return this._len},fu.clear=function(){this.head=this.tail=null,this._len=0};var gu=function(t){this.value=t,this.next,this.prev},pu=function(t){this._list=new du,this._map={},this._maxSize=t||10,this._lastRemovedEntry=null},vu=pu.prototype;vu.put=function(t,e){var i=this._list,n=this._map,r=null;if(null==n[t]){var o=i.len(),a=this._lastRemovedEntry;if(o>=this._maxSize&&o>0){var s=i.head;i.remove(s),delete n[s.key],r=s.value,this._lastRemovedEntry=s}a?a.value=e:a=new gu(e),a.key=t,i.insertEntry(a),n[t]=a}return r},vu.get=function(t){var e=this._map[t],i=this._list;return null!=e?(e!==i.tail&&(i.remove(e),i.insertEntry(e)),e.value):void 0},vu.clear=function(){this._list.clear(),this._map={}};var mu={transparent:[0,0,0,0],aliceblue:[240,248,255,1],antiquewhite:[250,235,215,1],aqua:[0,255,255,1],aquamarine:[127,255,212,1],azure:[240,255,255,1],beige:[245,245,220,1],bisque:[255,228,196,1],black:[0,0,0,1],blanchedalmond:[255,235,205,1],blue:[0,0,255,1],blueviolet:[138,43,226,1],brown:[165,42,42,1],burlywood:[222,184,135,1],cadetblue:[95,158,160,1],chartreuse:[127,255,0,1],chocolate:[210,105,30,1],coral:[255,127,80,1],cornflowerblue:[100,149,237,1],cornsilk:[255,248,220,1],crimson:[220,20,60,1],cyan:[0,255,255,1],darkblue:[0,0,139,1],darkcyan:[0,139,139,1],darkgoldenrod:[184,134,11,1],darkgray:[169,169,169,1],darkgreen:[0,100,0,1],darkgrey:[169,169,169,1],darkkhaki:[189,183,107,1],darkmagenta:[139,0,139,1],darkolivegreen:[85,107,47,1],darkorange:[255,140,0,1],darkorchid:[153,50,204,1],darkred:[139,0,0,1],darksalmon:[233,150,122,1],darkseagreen:[143,188,143,1],darkslateblue:[72,61,139,1],darkslategray:[47,79,79,1],darkslategrey:[47,79,79,1],darkturquoise:[0,206,209,1],darkviolet:[148,0,211,1],deeppink:[255,20,147,1],deepskyblue:[0,191,255,1],dimgray:[105,105,105,1],dimgrey:[105,105,105,1],dodgerblue:[30,144,255,1],firebrick:[178,34,34,1],floralwhite:[255,250,240,1],forestgreen:[34,139,34,1],fuchsia:[255,0,255,1],gainsboro:[220,220,220,1],ghostwhite:[248,248,255,1],gold:[255,215,0,1],goldenrod:[218,165,32,1],gray:[128,128,128,1],green:[0,128,0,1],greenyellow:[173,255,47,1],grey:[128,128,128,1],honeydew:[240,255,240,1],hotpink:[255,105,180,1],indianred:[205,92,92,1],indigo:[75,0,130,1],ivory:[255,255,240,1],khaki:[240,230,140,1],lavender:[230,230,250,1],lavenderblush:[255,240,245,1],lawngreen:[124,252,0,1],lemonchiffon:[255,250,205,1],lightblue:[173,216,230,1],lightcoral:[240,128,128,1],lightcyan:[224,255,255,1],lightgoldenrodyellow:[250,250,210,1],lightgray:[211,211,211,1],lightgreen:[144,238,144,1],lightgrey:[211,211,211,1],lightpink:[255,182,193,1],lightsalmon:[255,160,122,1],lightseagreen:[32,178,170,1],lightskyblue:[135,206,250,1],lightslategray:[119,136,153,1],lightslategrey:[119,136,153,1],lightsteelblue:[176,196,222,1],lightyellow:[255,255,224,1],lime:[0,255,0,1],limegreen:[50,205,50,1],linen:[250,240,230,1],magenta:[255,0,255,1],maroon:[128,0,0,1],mediumaquamarine:[102,205,170,1],mediumblue:[0,0,205,1],mediumorchid:[186,85,211,1],mediumpurple:[147,112,219,1],mediumseagreen:[60,179,113,1],mediumslateblue:[123,104,238,1],mediumspringgreen:[0,250,154,1],mediumturquoise:[72,209,204,1],mediumvioletred:[199,21,133,1],midnightblue:[25,25,112,1],mintcream:[245,255,250,1],mistyrose:[255,228,225,1],moccasin:[255,228,181,1],navajowhite:[255,222,173,1],navy:[0,0,128,1],oldlace:[253,245,230,1],olive:[128,128,0,1],olivedrab:[107,142,35,1],orange:[255,165,0,1],orangered:[255,69,0,1],orchid:[218,112,214,1],palegoldenrod:[238,232,170,1],palegreen:[152,251,152,1],paleturquoise:[175,238,238,1],palevioletred:[219,112,147,1],papayawhip:[255,239,213,1],peachpuff:[255,218,185,1],peru:[205,133,63,1],pink:[255,192,203,1],plum:[221,160,221,1],powderblue:[176,224,230,1],purple:[128,0,128,1],red:[255,0,0,1],rosybrown:[188,143,143,1],royalblue:[65,105,225,1],saddlebrown:[139,69,19,1],salmon:[250,128,114,1],sandybrown:[244,164,96,1],seagreen:[46,139,87,1],seashell:[255,245,238,1],sienna:[160,82,45,1],silver:[192,192,192,1],skyblue:[135,206,235,1],slateblue:[106,90,205,1],slategray:[112,128,144,1],slategrey:[112,128,144,1],snow:[255,250,250,1],springgreen:[0,255,127,1],steelblue:[70,130,180,1],tan:[210,180,140,1],teal:[0,128,128,1],thistle:[216,191,216,1],tomato:[255,99,71,1],turquoise:[64,224,208,1],violet:[238,130,238,1],wheat:[245,222,179,1],white:[255,255,255,1],whitesmoke:[245,245,245,1],yellow:[255,255,0,1],yellowgreen:[154,205,50,1]},yu=new pu(20),xu=null,_u=Array.prototype.slice,wu=function(t,e,i,n){this._tracks={},this._target=t,this._loop=e||!1,this._getter=i||Se,this._setter=n||Me,this._clipCount=0,this._delay=0,this._doneList=[],this._onframeList=[],this._clipList=[]};wu.prototype={when:function(t,e){var i=this._tracks;for(var n in e)if(e.hasOwnProperty(n)){if(!i[n]){i[n]=[];var r=this._getter(this._target,n);if(null==r)continue;0!==t&&i[n].push({time:0,value:Pe(r)})}i[n].push({time:t,value:e[n]})}return this},during:function(t){return this._onframeList.push(t),this},pause:function(){for(var t=0;t<this._clipList.length;t++)this._clipList[t].pause();this._paused=!0},resume:function(){for(var t=0;t<this._clipList.length;t++)this._clipList[t].resume();this._paused=!1},isPaused:function(){return!!this._paused},_doneCallback:function(){this._tracks={},this._clipList.length=0;for(var t=this._doneList,e=t.length,i=0;e>i;i++)t[i].call(this)},start:function(t,e){var i,n=this,r=0,o=function(){r--,r||n._doneCallback()};for(var a in this._tracks)if(this._tracks.hasOwnProperty(a)){var s=Be(this,t,o,this._tracks[a],a,e);s&&(this._clipList.push(s),r++,this.animation&&this.animation.addClip(s),i=s)}if(i){var l=i.onframe;i.onframe=function(t,e){l(t,e);for(var i=0;i<n._onframeList.length;i++)n._onframeList[i](t,e)}}return r||this._doneCallback(),this},stop:function(t){for(var e=this._clipList,i=this.animation,n=0;n<e.length;n++){var r=e[n];t&&r.onframe(this._target,1),i&&i.removeClip(r)}e.length=0},delay:function(t){return this._delay=t,this},done:function(t){return t&&this._doneList.push(t),this},getClips:function(){return this._clipList}};var bu=1;"undefined"!=typeof window&&(bu=Math.max(window.devicePixelRatio||1,1));var Su=0,Mu=bu,Tu=function(){};1===Su?Tu=function(){for(var t in arguments)throw new Error(arguments[t])}:Su>1&&(Tu=function(){for(var t in arguments)console.log(arguments[t])});var Iu=Tu,Cu=function(){this.animators=[]};Cu.prototype={constructor:Cu,animate:function(t,e){var i,n=!1,r=this,o=this.__zr;if(t){var a=t.split("."),s=r;n="shape"===a[0];for(var h=0,u=a.length;u>h;h++)s&&(s=s[a[h]]);s&&(i=s)}else i=r;if(!i)return void Iu('Property "'+t+'" is not existed in element '+r.id);var c=r.animators,d=new wu(i,e);return d.during(function(){r.dirty(n)}).done(function(){c.splice(l(c,d),1)}),c.push(d),o&&o.animation.addAnimator(d),d},stopAnimation:function(t){for(var e=this.animators,i=e.length,n=0;i>n;n++)e[n].stop(t);return e.length=0,this},animateTo:function(t,e,i,n,r,o){function a(){l--,l||r&&r()}_(i)?(r=n,n=i,i=0):x(n)?(r=n,n="linear",i=0):x(i)?(r=i,i=0):x(e)?(r=e,e=500):e||(e=500),this.stopAnimation(),this._animateToShallow("",this,t,e,i);var s=this.animators.slice(),l=s.length;l||r&&r();for(var h=0;h<s.length;h++)s[h].done(a).start(n,o)},_animateToShallow:function(t,e,i,n,r){var o={},a=0;for(var s in i)if(i.hasOwnProperty(s))if(null!=e[s])w(i[s])&&!c(i[s])?this._animateToShallow(t?t+"."+s:s,e[s],i[s],n,r):(o[s]=i[s],a++);else if(null!=i[s])if(t){var l={};l[t]={},l[t][s]=i[s],this.attr(l)}else this.attr(s,i[s]);return a>0&&this.animate(t,!1).when(null==n?500:n,o).delay(r||0),this}};var Au=function(t){lu.call(this,t),eu.call(this,t),Cu.call(this,t),this.id=t.id||Oh()};Au.prototype={type:"element",name:"",__zr:null,ignore:!1,clipPath:null,drift:function(t,e){switch(this.draggable){case"horizontal":e=0;break;case"vertical":t=0}var i=this.transform;i||(i=this.transform=[1,0,0,1,0,0]),i[4]+=t,i[5]+=e,this.decomposeTransform(),this.dirty(!1)},beforeUpdate:function(){},afterUpdate:function(){},update:function(){this.updateTransform()},traverse:function(){},attrKV:function(t,e){if("position"===t||"scale"===t||"origin"===t){if(e){var i=this[t];i||(i=this[t]=[]),i[0]=e[0],i[1]=e[1]}}else this[t]=e},hide:function(){this.ignore=!0,this.__zr&&this.__zr.refresh()},show:function(){this.ignore=!1,this.__zr&&this.__zr.refresh()},attr:function(t,e){if("string"==typeof t)this.attrKV(t,e);else if(w(t))for(var i in t)t.hasOwnProperty(i)&&this.attrKV(i,t[i]);return this.dirty(!1),this},setClipPath:function(t){var e=this.__zr;e&&t.addSelfToZr(e),this.clipPath&&this.clipPath!==t&&this.removeClipPath(),this.clipPath=t,t.__zr=e,t.__clipTarget=this,this.dirty(!1)},removeClipPath:function(){var t=this.clipPath;t&&(t.__zr&&t.removeSelfFromZr(t.__zr),t.__zr=null,t.__clipTarget=null,this.clipPath=null,this.dirty(!1))},addSelfToZr:function(t){this.__zr=t;var e=this.animators;if(e)for(var i=0;i<e.length;i++)t.animation.addAnimator(e[i]);this.clipPath&&this.clipPath.addSelfToZr(t)},removeSelfFromZr:function(t){this.__zr=null;var e=this.animators;if(e)for(var i=0;i<e.length;i++)t.animation.removeAnimator(e[i]);this.clipPath&&this.clipPath.removeSelfFromZr(t)}},u(Au,Cu),u(Au,lu),u(Au,eu);var ku=Y,Lu=Math.min,Du=Math.max;Ee.prototype={constructor:Ee,union:function(t){var e=Lu(t.x,this.x),i=Lu(t.y,this.y);this.width=Du(t.x+t.width,this.x+this.width)-e,this.height=Du(t.y+t.height,this.y+this.height)-i,this.x=e,this.y=i},applyTransform:function(){var t=[],e=[],i=[],n=[];return function(r){if(r){t[0]=i[0]=this.x,t[1]=n[1]=this.y,e[0]=n[0]=this.x+this.width,e[1]=i[1]=this.y+this.height,ku(t,t,r),ku(e,e,r),ku(i,i,r),ku(n,n,r),this.x=Lu(t[0],e[0],i[0],n[0]),this.y=Lu(t[1],e[1],i[1],n[1]);var o=Du(t[0],e[0],i[0],n[0]),a=Du(t[1],e[1],i[1],n[1]);this.width=o-this.x,this.height=a-this.y}}}(),calculateTransform:function(t){var e=this,i=t.width/e.width,n=t.height/e.height,r=te();return re(r,r,[-e.x,-e.y]),ae(r,r,[i,n]),re(r,r,[t.x,t.y]),r},intersect:function(t){if(!t)return!1;t instanceof Ee||(t=Ee.create(t));var e=this,i=e.x,n=e.x+e.width,r=e.y,o=e.y+e.height,a=t.x,s=t.x+t.width,l=t.y,h=t.y+t.height;return!(a>n||i>s||l>o||r>h)},contain:function(t,e){var i=this;return t>=i.x&&t<=i.x+i.width&&e>=i.y&&e<=i.y+i.height},clone:function(){return new Ee(this.x,this.y,this.width,this.height)},copy:function(t){this.x=t.x,this.y=t.y,this.width=t.width,this.height=t.height},plain:function(){return{x:this.x,y:this.y,width:this.width,height:this.height}}},Ee.create=function(t){return new Ee(t.x,t.y,t.width,t.height)};var Pu=function(t){t=t||{},Au.call(this,t);for(var e in t)t.hasOwnProperty(e)&&(this[e]=t[e]);this._children=[],this.__storage=null,this.__dirty=!0};Pu.prototype={constructor:Pu,isGroup:!0,type:"group",silent:!1,children:function(){return this._children.slice()},childAt:function(t){return this._children[t]},childOfName:function(t){for(var e=this._children,i=0;i<e.length;i++)if(e[i].name===t)return e[i]},childCount:function(){return this._children.length},add:function(t){return t&&t!==this&&t.parent!==this&&(this._children.push(t),this._doAdd(t)),this},addBefore:function(t,e){if(t&&t!==this&&t.parent!==this&&e&&e.parent===this){var i=this._children,n=i.indexOf(e);
n>=0&&(i.splice(n,0,t),this._doAdd(t))}return this},_doAdd:function(t){t.parent&&t.parent.remove(t),t.parent=this;var e=this.__storage,i=this.__zr;e&&e!==t.__storage&&(e.addToStorage(t),t instanceof Pu&&t.addChildrenToStorage(e)),i&&i.refresh()},remove:function(t){var e=this.__zr,i=this.__storage,n=this._children,r=l(n,t);return 0>r?this:(n.splice(r,1),t.parent=null,i&&(i.delFromStorage(t),t instanceof Pu&&t.delChildrenFromStorage(i)),e&&e.refresh(),this)},removeAll:function(){var t,e,i=this._children,n=this.__storage;for(e=0;e<i.length;e++)t=i[e],n&&(n.delFromStorage(t),t instanceof Pu&&t.delChildrenFromStorage(n)),t.parent=null;return i.length=0,this},eachChild:function(t,e){for(var i=this._children,n=0;n<i.length;n++){var r=i[n];t.call(e,r,n)}return this},traverse:function(t,e){for(var i=0;i<this._children.length;i++){var n=this._children[i];t.call(e,n),"group"===n.type&&n.traverse(t,e)}return this},addChildrenToStorage:function(t){for(var e=0;e<this._children.length;e++){var i=this._children[e];t.addToStorage(i),i instanceof Pu&&i.addChildrenToStorage(t)}},delChildrenFromStorage:function(t){for(var e=0;e<this._children.length;e++){var i=this._children[e];t.delFromStorage(i),i instanceof Pu&&i.delChildrenFromStorage(t)}},dirty:function(){return this.__dirty=!0,this.__zr&&this.__zr.refresh(),this},getBoundingRect:function(t){for(var e=null,i=new Ee(0,0,0,0),n=t||this._children,r=[],o=0;o<n.length;o++){var a=n[o];if(!a.ignore&&!a.invisible){var s=a.getBoundingRect(),l=a.getLocalTransform(r);l?(i.copy(s),i.applyTransform(l),e=e||i.clone(),e.union(i)):(e=e||s.clone(),e.union(s))}}return e||i}},h(Pu,Au);var Ou=32,zu=7,Bu=function(){this._roots=[],this._displayList=[],this._displayListLen=0};Bu.prototype={constructor:Bu,traverse:function(t,e){for(var i=0;i<this._roots.length;i++)this._roots[i].traverse(t,e)},getDisplayList:function(t,e){return e=e||!1,t&&this.updateDisplayList(e),this._displayList},updateDisplayList:function(t){this._displayListLen=0;for(var e=this._roots,i=this._displayList,n=0,r=e.length;r>n;n++)this._updateAndAddDisplayable(e[n],null,t);i.length=this._displayListLen,Eh.canvasSupported&&Xe(i,qe)},_updateAndAddDisplayable:function(t,e,i){if(!t.ignore||i){t.beforeUpdate(),t.__dirty&&t.update(),t.afterUpdate();var n=t.clipPath;if(n){e=e?e.slice():[];for(var r=n,o=t;r;)r.parent=o,r.updateTransform(),e.push(r),o=r,r=r.clipPath}if(t.isGroup){for(var a=t._children,s=0;s<a.length;s++){var l=a[s];t.__dirty&&(l.__dirty=!0),this._updateAndAddDisplayable(l,e,i)}t.__dirty=!1}else t.__clipPaths=e,this._displayList[this._displayListLen++]=t}},addRoot:function(t){t.__storage!==this&&(t instanceof Pu&&t.addChildrenToStorage(this),this.addToStorage(t),this._roots.push(t))},delRoot:function(t){if(null==t){for(var e=0;e<this._roots.length;e++){var i=this._roots[e];i instanceof Pu&&i.delChildrenFromStorage(this)}return this._roots=[],this._displayList=[],void(this._displayListLen=0)}if(t instanceof Array)for(var e=0,n=t.length;n>e;e++)this.delRoot(t[e]);else{var r=l(this._roots,t);r>=0&&(this.delFromStorage(t),this._roots.splice(r,1),t instanceof Pu&&t.delChildrenFromStorage(this))}},addToStorage:function(t){return t.__storage=this,t.dirty(!1),this},delFromStorage:function(t){return t&&(t.__storage=null),this},dispose:function(){this._renderList=this._roots=null},displayableSortFunc:qe};var Eu=[["shadowBlur",0],["shadowOffsetX",0],["shadowOffsetY",0],["shadowColor","#000"],["lineCap","butt"],["lineJoin","miter"],["miterLimit",10]],Ru=function(t,e){this.extendFrom(t,!1),this.host=e};Ru.prototype={constructor:Ru,host:null,fill:"#000",stroke:null,opacity:1,lineDash:null,lineDashOffset:0,shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,lineWidth:1,strokeNoScale:!1,text:null,font:null,textFont:null,fontStyle:null,fontWeight:null,fontSize:null,fontFamily:null,textTag:null,textFill:"#000",textStroke:null,textWidth:null,textHeight:null,textStrokeWidth:0,textLineHeight:null,textPosition:"inside",textRect:null,textOffset:null,textAlign:null,textVerticalAlign:null,textDistance:5,textShadowColor:"transparent",textShadowBlur:0,textShadowOffsetX:0,textShadowOffsetY:0,textBoxShadowColor:"transparent",textBoxShadowBlur:0,textBoxShadowOffsetX:0,textBoxShadowOffsetY:0,transformText:!1,textRotation:0,textOrigin:null,textBackgroundColor:null,textBorderColor:null,textBorderWidth:0,textBorderRadius:0,textPadding:null,rich:null,truncate:null,blend:null,bind:function(t,e,i){for(var n=this,r=i&&i.style,o=!r,a=0;a<Eu.length;a++){var s=Eu[a],l=s[0];(o||n[l]!==r[l])&&(t[l]=n[l]||s[1])}if((o||n.fill!==r.fill)&&(t.fillStyle=n.fill),(o||n.stroke!==r.stroke)&&(t.strokeStyle=n.stroke),(o||n.opacity!==r.opacity)&&(t.globalAlpha=null==n.opacity?1:n.opacity),(o||n.blend!==r.blend)&&(t.globalCompositeOperation=n.blend||"source-over"),this.hasStroke()){var h=n.lineWidth;t.lineWidth=h/(this.strokeNoScale&&e&&e.getLineScale?e.getLineScale():1)}},hasFill:function(){var t=this.fill;return null!=t&&"none"!==t},hasStroke:function(){var t=this.stroke;return null!=t&&"none"!==t&&this.lineWidth>0},extendFrom:function(t,e){if(t)for(var i in t)!t.hasOwnProperty(i)||e!==!0&&(e===!1?this.hasOwnProperty(i):null==t[i])||(this[i]=t[i])},set:function(t,e){"string"==typeof t?this[t]=e:this.extendFrom(t,!0)},clone:function(){var t=new this.constructor;return t.extendFrom(this,!0),t},getGradient:function(t,e,i){for(var n="radial"===e.type?Ue:Ye,r=n(t,e,i),o=e.colorStops,a=0;a<o.length;a++)r.addColorStop(o[a].offset,o[a].color);return r}};for(var Nu=Ru.prototype,Fu=0;Fu<Eu.length;Fu++){var Hu=Eu[Fu];Hu[0]in Nu||(Nu[Hu[0]]=Hu[1])}Ru.getGradient=Nu.getGradient;var Vu=function(t,e){this.image=t,this.repeat=e,this.type="pattern"};Vu.prototype.getCanvasPattern=function(t){return t.createPattern(this.image,this.repeat||"repeat")};var Wu=function(t,e,i){var n;i=i||Mu,"string"==typeof t?n=je(t,e,i):w(t)&&(n=t,t=n.id),this.id=t,this.dom=n;var r=n.style;r&&(n.onselectstart=Ze,r["-webkit-user-select"]="none",r["user-select"]="none",r["-webkit-touch-callout"]="none",r["-webkit-tap-highlight-color"]="rgba(0,0,0,0)",r.padding=0,r.margin=0,r["border-width"]=0),this.domBack=null,this.ctxBack=null,this.painter=e,this.config=null,this.clearColor=0,this.motionBlur=!1,this.lastFrameAlpha=.7,this.dpr=i};Wu.prototype={constructor:Wu,elCount:0,__dirty:!0,initContext:function(){this.ctx=this.dom.getContext("2d"),this.ctx.__currentValues={},this.ctx.dpr=this.dpr},createBackBuffer:function(){var t=this.dpr;this.domBack=je("back-"+this.id,this.painter,t),this.ctxBack=this.domBack.getContext("2d"),this.ctxBack.__currentValues={},1!=t&&this.ctxBack.scale(t,t)},resize:function(t,e){var i=this.dpr,n=this.dom,r=n.style,o=this.domBack;r.width=t+"px",r.height=e+"px",n.width=t*i,n.height=e*i,o&&(o.width=t*i,o.height=e*i,1!=i&&this.ctxBack.scale(i,i))},clear:function(t){var e=this.dom,i=this.ctx,n=e.width,r=e.height,o=this.clearColor,a=this.motionBlur&&!t,s=this.lastFrameAlpha,l=this.dpr;if(a&&(this.domBack||this.createBackBuffer(),this.ctxBack.globalCompositeOperation="copy",this.ctxBack.drawImage(e,0,0,n/l,r/l)),i.clearRect(0,0,n,r),o){var h;o.colorStops?(h=o.__canvasGradient||Ru.getGradient(i,o,{x:0,y:0,width:n,height:r}),o.__canvasGradient=h):o.image&&(h=Vu.prototype.getCanvasPattern.call(o,i)),i.save(),i.fillStyle=h||o,i.fillRect(0,0,n,r),i.restore()}if(a){var u=this.domBack;i.save(),i.globalAlpha=s,i.drawImage(u,0,0,n,r),i.restore()}}};var Gu="undefined"!=typeof window&&(window.requestAnimationFrame&&window.requestAnimationFrame.bind(window)||window.msRequestAnimationFrame&&window.msRequestAnimationFrame.bind(window)||window.mozRequestAnimationFrame||window.webkitRequestAnimationFrame)||function(t){setTimeout(t,16)},Xu=new pu(50),qu={},Yu=0,Uu=5e3,Zu=/\{([a-zA-Z0-9_]+)\|([^}]*)\}/g,ju="12px sans-serif",$u=function(t,e){var i=s();return i.font=e||ju,i.measureText(t)},Ku={measureText:function(t){$u=t}},Qu={left:1,right:1,center:1},Ju={top:1,bottom:1,middle:1},tc=new Ee,ec=function(){};ec.prototype={constructor:ec,drawRectText:function(t,e){var i=this.style;e=i.textRect||e,this.__dirty&&mi(i,!0);var n=i.text;if(null!=n&&(n+=""),zi(n,i)){t.save();var r=this.transform;i.transformText?this.setTransform(t):r&&(tc.copy(e),tc.applyTransform(r),e=tc),xi(this,t,n,i,e),t.restore()}}},Bi.prototype={constructor:Bi,type:"displayable",__dirty:!0,invisible:!1,z:0,z2:0,zlevel:0,draggable:!1,dragging:!1,silent:!1,culling:!1,cursor:"pointer",rectHover:!1,progressive:-1,beforeBrush:function(){},afterBrush:function(){},brush:function(){},getBoundingRect:function(){},contain:function(t,e){return this.rectContain(t,e)},traverse:function(t,e){t.call(e,this)},rectContain:function(t,e){var i=this.transformCoordToLocal(t,e),n=this.getBoundingRect();return n.contain(i[0],i[1])},dirty:function(){this.__dirty=!0,this._rect=null,this.__zr&&this.__zr.refresh()},animateStyle:function(t){return this.animate("style",t)},attrKV:function(t,e){"style"!==t?Au.prototype.attrKV.call(this,t,e):this.style.set(e)},setStyle:function(t,e){return this.style.set(t,e),this.dirty(!1),this},useStyle:function(t){return this.style=new Ru(t,this),this.dirty(!1),this}},h(Bi,Au),u(Bi,ec),Ei.prototype={constructor:Ei,type:"image",brush:function(t,e){var i=this.style,n=i.image;i.bind(t,this,e);var r=this._image=Ke(n,this._image,this,this.onload);if(r&&Je(r)){var o=i.x||0,a=i.y||0,s=i.width,l=i.height,h=r.width/r.height;if(null==s&&null!=l?s=l*h:null==l&&null!=s?l=s/h:null==s&&null==l&&(s=r.width,l=r.height),this.setTransform(t),i.sWidth&&i.sHeight){var u=i.sx||0,c=i.sy||0;t.drawImage(r,u,c,i.sWidth,i.sHeight,o,a,s,l)}else if(i.sx&&i.sy){var u=i.sx,c=i.sy,d=s-u,f=l-c;t.drawImage(r,u,c,d,f,o,a,s,l)}else t.drawImage(r,o,a,s,l);this.restoreTransform(t),null!=i.text&&this.drawRectText(t,this.getBoundingRect())}},getBoundingRect:function(){var t=this.style;return this._rect||(this._rect=new Ee(t.x||0,t.y||0,t.width||0,t.height||0)),this._rect}},h(Ei,Bi);var ic=5,nc=new Ee(0,0,0,0),rc=new Ee(0,0,0,0),oc=function(t,e,i){this.type="canvas";var n=!t.nodeName||"CANVAS"===t.nodeName.toUpperCase();this._opts=i=o({},i||{}),this.dpr=i.devicePixelRatio||Mu,this._singleCanvas=n,this.root=t;var r=t.style;r&&(r["-webkit-tap-highlight-color"]="transparent",r["-webkit-user-select"]=r["user-select"]=r["-webkit-touch-callout"]="none",t.innerHTML=""),this.storage=e;var a=this._zlevelList=[],s=this._layers={};if(this._layerConfig={},n){null!=i.width&&(t.width=i.width),null!=i.height&&(t.height=i.height);var l=t.width,h=t.height;this._width=l,this._height=h;var u=new Wu(t,this,1);u.initContext(),s[0]=u,a.push(0),this._domRoot=t}else{this._width=this._getSize(0),this._height=this._getSize(1);var c=this._domRoot=Xi(this._width,this._height);t.appendChild(c)}this._progressiveLayers=[],this._hoverlayer,this._hoverElements=[]};oc.prototype={constructor:oc,getType:function(){return"canvas"},isSingleCanvas:function(){return this._singleCanvas},getViewportRoot:function(){return this._domRoot},getViewportRootOffset:function(){var t=this.getViewportRoot();return t?{offsetLeft:t.offsetLeft||0,offsetTop:t.offsetTop||0}:void 0},refresh:function(t){var e=this.storage.getDisplayList(!0),i=this._zlevelList;this._paintList(e,t);for(var n=0;n<i.length;n++){var r=i[n],o=this._layers[r];!o.__builtin__&&o.refresh&&o.refresh()}return this.refreshHover(),this._progressiveLayers.length&&this._startProgessive(),this},addHover:function(t,e){if(!t.__hoverMir){var i=new t.constructor({style:t.style,shape:t.shape});i.__from=t,t.__hoverMir=i,i.setStyle(e),this._hoverElements.push(i)}},removeHover:function(t){var e=t.__hoverMir,i=this._hoverElements,n=l(i,e);n>=0&&i.splice(n,1),t.__hoverMir=null},clearHover:function(){for(var t=this._hoverElements,e=0;e<t.length;e++){var i=t[e].__from;i&&(i.__hoverMir=null)}t.length=0},refreshHover:function(){var t=this._hoverElements,e=t.length,i=this._hoverlayer;if(i&&i.clear(),e){Xe(t,this.storage.displayableSortFunc),i||(i=this._hoverlayer=this.getLayer(1e5));var n={};i.ctx.save();for(var r=0;e>r;){var o=t[r],a=o.__from;a&&a.__zr?(r++,a.invisible||(o.transform=a.transform,o.invTransform=a.invTransform,o.__clipPaths=a.__clipPaths,this._doPaintEl(o,i,!0,n))):(t.splice(r,1),a.__hoverMir=null,e--)}i.ctx.restore()}},_startProgessive:function(){function t(){i===e._progressiveToken&&e.storage&&(e._doPaintList(e.storage.getDisplayList()),e._furtherProgressive?(e._progress++,Gu(t)):e._progressiveToken=-1)}var e=this;if(e._furtherProgressive){var i=e._progressiveToken=+new Date;e._progress++,Gu(t)}},_clearProgressive:function(){this._progressiveToken=-1,this._progress=0,d(this._progressiveLayers,function(t){t.__dirty&&t.clear()})},_paintList:function(t,e){null==e&&(e=!1),this._updateLayerStatus(t),this._clearProgressive(),this.eachBuiltinLayer(Fi),this._doPaintList(t,e),this.eachBuiltinLayer(Hi)},_doPaintList:function(t,e){function i(t){var e=o.dpr||1;o.save(),o.globalAlpha=1,o.shadowBlur=0,n.__dirty=!0,o.setTransform(1,0,0,1,0,0),o.drawImage(t.dom,0,0,u*e,c*e),o.restore()}for(var n,r,o,a,s,l,h=0,u=this._width,c=this._height,f=this._progress,g=0,p=t.length;p>g;g++){var v=t[g],m=this._singleCanvas?0:v.zlevel,y=v.__frame;if(0>y&&s&&(i(s),s=null),r!==m&&(o&&o.restore(),a={},r=m,n=this.getLayer(r),n.__builtin__||Iu("ZLevel "+r+" has been used by unkown layer "+n.id),o=n.ctx,o.save(),n.__unusedCount=0,(n.__dirty||e)&&n.clear()),n.__dirty||e){if(y>=0){if(!s){if(s=this._progressiveLayers[Math.min(h++,ic-1)],s.ctx.save(),s.renderScope={},s&&s.__progress>s.__maxProgress){g=s.__nextIdxNotProg-1;continue}l=s.__progress,s.__dirty||(f=l),s.__progress=f+1}y===f&&this._doPaintEl(v,s,!0,s.renderScope)}else this._doPaintEl(v,n,e,a);v.__dirty=!1}}s&&i(s),o&&o.restore(),this._furtherProgressive=!1,d(this._progressiveLayers,function(t){t.__maxProgress>=t.__progress&&(this._furtherProgressive=!0)},this)},_doPaintEl:function(t,e,i,n){var r=e.ctx,o=t.transform;if(!(!e.__dirty&&!i||t.invisible||0===t.style.opacity||o&&!o[0]&&!o[3]||t.culling&&Vi(t,this._width,this._height))){var a=t.__clipPaths;(n.prevClipLayer!==e||Wi(a,n.prevElClipPaths))&&(n.prevElClipPaths&&(n.prevClipLayer.ctx.restore(),n.prevClipLayer=n.prevElClipPaths=null,n.prevEl=null),a&&(r.save(),Gi(a,r),n.prevClipLayer=e,n.prevElClipPaths=a)),t.beforeBrush&&t.beforeBrush(r),t.brush(r,n.prevEl||null),n.prevEl=t,t.afterBrush&&t.afterBrush(r)}},getLayer:function(t){if(this._singleCanvas)return this._layers[0];var e=this._layers[t];return e||(e=new Wu("zr_"+t,this,this.dpr),e.__builtin__=!0,this._layerConfig[t]&&n(e,this._layerConfig[t],!0),this.insertLayer(t,e),e.initContext()),e},insertLayer:function(t,e){var i=this._layers,n=this._zlevelList,r=n.length,o=null,a=-1,s=this._domRoot;if(i[t])return void Iu("ZLevel "+t+" has been used already");if(!Ni(e))return void Iu("Layer of zlevel "+t+" is not valid");if(r>0&&t>n[0]){for(a=0;r-1>a&&!(n[a]<t&&n[a+1]>t);a++);o=i[n[a]]}if(n.splice(a+1,0,t),i[t]=e,!e.virtual)if(o){var l=o.dom;l.nextSibling?s.insertBefore(e.dom,l.nextSibling):s.appendChild(e.dom)}else s.firstChild?s.insertBefore(e.dom,s.firstChild):s.appendChild(e.dom)},eachLayer:function(t,e){var i,n,r=this._zlevelList;for(n=0;n<r.length;n++)i=r[n],t.call(e,this._layers[i],i)},eachBuiltinLayer:function(t,e){var i,n,r,o=this._zlevelList;for(r=0;r<o.length;r++)n=o[r],i=this._layers[n],i.__builtin__&&t.call(e,i,n)},eachOtherLayer:function(t,e){var i,n,r,o=this._zlevelList;for(r=0;r<o.length;r++)n=o[r],i=this._layers[n],i.__builtin__||t.call(e,i,n)},getLayers:function(){return this._layers},_updateLayerStatus:function(t){var e=this._layers,i=this._progressiveLayers,n={},r={};this.eachBuiltinLayer(function(t,e){n[e]=t.elCount,t.elCount=0,t.__dirty=!1}),d(i,function(t,e){r[e]=t.elCount,t.elCount=0,t.__dirty=!1});for(var o,a,s=0,l=0,h=0,u=t.length;u>h;h++){var c=t[h],f=this._singleCanvas?0:c.zlevel,g=e[f],p=c.progressive;if(g&&(g.elCount++,g.__dirty=g.__dirty||c.__dirty),p>=0){a!==p&&(a=p,l++);var v=c.__frame=l-1;if(!o){var m=Math.min(s,ic-1);o=i[m],o||(o=i[m]=new Wu("progressive",this,this.dpr),o.initContext()),o.__maxProgress=0}o.__dirty=o.__dirty||c.__dirty,o.elCount++,o.__maxProgress=Math.max(o.__maxProgress,v),o.__maxProgress>=o.__progress&&(g.__dirty=!0)}else c.__frame=-1,o&&(o.__nextIdxNotProg=h,s++,o=null)}o&&(s++,o.__nextIdxNotProg=h),this.eachBuiltinLayer(function(t,e){n[e]!==t.elCount&&(t.__dirty=!0)}),i.length=Math.min(s,ic),d(i,function(t,e){r[e]!==t.elCount&&(c.__dirty=!0),t.__dirty&&(t.__progress=0)})},clear:function(){return this.eachBuiltinLayer(this._clearLayer),this},_clearLayer:function(t){t.clear()},configLayer:function(t,e){if(e){var i=this._layerConfig;i[t]?n(i[t],e,!0):i[t]=e;var r=this._layers[t];r&&n(r,i[t],!0)}},delLayer:function(t){var e=this._layers,i=this._zlevelList,n=e[t];n&&(n.dom.parentNode.removeChild(n.dom),delete e[t],i.splice(l(i,t),1))},resize:function(t,e){var i=this._domRoot;i.style.display="none";var n=this._opts;if(null!=t&&(n.width=t),null!=e&&(n.height=e),t=this._getSize(0),e=this._getSize(1),i.style.display="",this._width!=t||e!=this._height){i.style.width=t+"px",i.style.height=e+"px";for(var r in this._layers)this._layers.hasOwnProperty(r)&&this._layers[r].resize(t,e);d(this._progressiveLayers,function(i){i.resize(t,e)}),this.refresh(!0)}return this._width=t,this._height=e,this},clearLayer:function(t){var e=this._layers[t];e&&e.clear()},dispose:function(){this.root.innerHTML="",this.root=this.storage=this._domRoot=this._layers=null},getRenderedCanvas:function(t){function e(t,e){var n=a._zlevelList;null==t&&(t=-1/0);for(var r,o=0;o<n.length;o++){var s=n[o],l=a._layers[s];if(!l.__builtin__&&s>t&&e>s){r=l;break}}r&&r.renderToCanvas&&(i.ctx.save(),r.renderToCanvas(i.ctx),i.ctx.restore())}if(t=t||{},this._singleCanvas)return this._layers[0].dom;var i=new Wu("image",this,t.pixelRatio||this.dpr);i.initContext(),i.clearColor=t.backgroundColor,i.clear();for(var n,r=this.storage.getDisplayList(!0),o={},a=this,s=0;s<r.length;s++){var l=r[s];l.zlevel!==n&&(e(n,l.zlevel),n=l.zlevel),this._doPaintEl(l,i,!0,o)}return e(n,1/0),i.dom},getWidth:function(){return this._width},getHeight:function(){return this._height},_getSize:function(t){var e=this._opts,i=["width","height"][t],n=["clientWidth","clientHeight"][t],r=["paddingLeft","paddingTop"][t],o=["paddingRight","paddingBottom"][t];if(null!=e[i]&&"auto"!==e[i])return parseFloat(e[i]);var a=this.root,s=document.defaultView.getComputedStyle(a);return(a[n]||Ri(s[i])||Ri(a.style[i]))-(Ri(s[r])||0)-(Ri(s[o])||0)|0},pathToImage:function(t,e){e=e||this.dpr;var i=document.createElement("canvas"),n=i.getContext("2d"),r=t.getBoundingRect(),o=t.style,a=o.shadowBlur,s=o.shadowOffsetX,l=o.shadowOffsetY,h=o.hasStroke()?o.lineWidth:0,u=Math.max(h/2,-s+a),c=Math.max(h/2,s+a),d=Math.max(h/2,-l+a),f=Math.max(h/2,l+a),g=r.width+u+c,p=r.height+d+f;i.width=g*e,i.height=p*e,n.scale(e,e),n.clearRect(0,0,g,p),n.dpr=e;var v={position:t.position,rotation:t.rotation,scale:t.scale};t.position=[u-r.x,d-r.y],t.rotation=0,t.scale=[1,1],t.updateTransform(),t&&t.brush(n);var m=Ei,y=new m({style:{x:0,y:0,image:i}});return null!=v.position&&(y.position=t.position=v.position),null!=v.rotation&&(y.rotation=t.rotation=v.rotation),null!=v.scale&&(y.scale=t.scale=v.scale),y}};var ac="undefined"!=typeof window&&!!window.addEventListener,sc=/^(?:mouse|pointer|contextmenu|drag|drop)|click/,lc=ac?function(t){t.preventDefault(),t.stopPropagation(),t.cancelBubble=!0}:function(t){t.returnValue=!1,t.cancelBubble=!0},hc=function(t){t=t||{},this.stage=t.stage||{},this.onframe=t.onframe||function(){},this._clips=[],this._running=!1,this._time,this._pausedTime,this._pauseStart,this._paused=!1,eu.call(this)};hc.prototype={constructor:hc,addClip:function(t){this._clips.push(t)},addAnimator:function(t){t.animation=this;for(var e=t.getClips(),i=0;i<e.length;i++)this.addClip(e[i])},removeClip:function(t){var e=l(this._clips,t);e>=0&&this._clips.splice(e,1)},removeAnimator:function(t){for(var e=t.getClips(),i=0;i<e.length;i++)this.removeClip(e[i]);t.animation=null},_update:function(){for(var t=(new Date).getTime()-this._pausedTime,e=t-this._time,i=this._clips,n=i.length,r=[],o=[],a=0;n>a;a++){var s=i[a],l=s.step(t,e);l&&(r.push(l),o.push(s))}for(var a=0;n>a;)i[a]._needsRemove?(i[a]=i[n-1],i.pop(),n--):a++;n=r.length;for(var a=0;n>a;a++)o[a].fire(r[a]);this._time=t,this.onframe(e),this.trigger("frame",e),this.stage.update&&this.stage.update()},_startLoop:function(){function t(){e._running&&(Gu(t),!e._paused&&e._update())}var e=this;this._running=!0,Gu(t)},start:function(){this._time=(new Date).getTime(),this._pausedTime=0,this._startLoop()},stop:function(){this._running=!1},pause:function(){this._paused||(this._pauseStart=(new Date).getTime(),this._paused=!0)},resume:function(){this._paused&&(this._pausedTime+=(new Date).getTime()-this._pauseStart,this._paused=!1)},clear:function(){this._clips=[]},animate:function(t,e){e=e||{};var i=new wu(t,e.loop,e.getter,e.setter);return this.addAnimator(i),i}},u(hc,eu);var uc=function(){this._track=[]};uc.prototype={constructor:uc,recognize:function(t,e,i){return this._doTrack(t,e,i),this._recognize(t)},clear:function(){return this._track.length=0,this},_doTrack:function(t,e,i){var n=t.touches;if(n){for(var r={points:[],touches:[],target:e,event:t},o=0,a=n.length;a>o;o++){var s=n[o],l=Yi(i,s,{});r.points.push([l.zrX,l.zrY]),r.touches.push(s)}this._track.push(r)}},_recognize:function(t){for(var e in cc)if(cc.hasOwnProperty(e)){var i=cc[e](this._track,t);if(i)return i}}};var cc={pinch:function(t,e){var i=t.length;if(i){var n=(t[i-1]||{}).points,r=(t[i-2]||{}).points||n;if(r&&r.length>1&&n&&n.length>1){var o=Ki(n)/Ki(r);!isFinite(o)&&(o=1),e.pinchScale=o;var a=Qi(n);return e.pinchX=a[0],e.pinchY=a[1],{type:"pinch",target:t[0].target,event:e}}}}},dc=300,fc=["click","dblclick","mousewheel","mouseout","mouseup","mousedown","mousemove","contextmenu"],gc=["touchstart","touchend","touchmove"],pc={pointerdown:1,pointerup:1,pointermove:1,pointerout:1},vc=f(fc,function(t){var e=t.replace("mouse","pointer");return pc[e]?e:t}),mc={mousemove:function(t){t=Zi(this.dom,t),this.trigger("mousemove",t)},mouseout:function(t){t=Zi(this.dom,t);var e=t.toElement||t.relatedTarget;if(e!=this.dom)for(;e&&9!=e.nodeType;){if(e===this.dom)return;e=e.parentNode}this.trigger("mouseout",t)},touchstart:function(t){t=Zi(this.dom,t),t.zrByTouch=!0,this._lastTouchMoment=new Date,tn(this,t,"start"),mc.mousemove.call(this,t),mc.mousedown.call(this,t),en(this)},touchmove:function(t){t=Zi(this.dom,t),t.zrByTouch=!0,tn(this,t,"change"),mc.mousemove.call(this,t),en(this)},touchend:function(t){t=Zi(this.dom,t),t.zrByTouch=!0,tn(this,t,"end"),mc.mouseup.call(this,t),+new Date-this._lastTouchMoment<dc&&mc.click.call(this,t),en(this)},pointerdown:function(t){mc.mousedown.call(this,t)},pointermove:function(t){nn(t)||mc.mousemove.call(this,t)},pointerup:function(t){mc.mouseup.call(this,t)},pointerout:function(t){nn(t)||mc.mouseout.call(this,t)}};d(["click","mousedown","mouseup","mousewheel","dblclick","contextmenu"],function(t){mc[t]=function(e){e=Zi(this.dom,e),this.trigger(t,e)}});var yc=on.prototype;yc.dispose=function(){for(var t=fc.concat(gc),e=0;e<t.length;e++){var i=t[e];$i(this.dom,Ji(i),this._handlers[i])}},yc.setCursor=function(t){this.dom.style.cursor=t||"default"},u(on,eu);var xc=!Eh.canvasSupported,_c={canvas:oc},wc="3.7.0",bc=function(t,e,i){i=i||{},this.dom=e,this.id=t;var n=this,r=new Bu,o=i.renderer;if(xc){if(!_c.vml)throw new Error("You need to require 'zrender/vml/vml' to support IE8");o="vml"}else o&&_c[o]||(o="canvas");var a=new _c[o](e,r,i);this.storage=r,this.painter=a;var s=Eh.node?null:new on(a.getViewportRoot());this.handler=new ru(r,a,s,a.root),this.animation=new hc({stage:{update:v(this.flush,this)}}),this.animation.start(),this._needsRefresh;var l=r.delFromStorage,h=r.addToStorage;r.delFromStorage=function(t){l.call(r,t),t&&t.removeSelfFromZr(n)},r.addToStorage=function(t){h.call(r,t),t.addSelfToZr(n)}};bc.prototype={constructor:bc,getId:function(){return this.id},add:function(t){this.storage.addRoot(t),this._needsRefresh=!0},remove:function(t){this.storage.delRoot(t),this._needsRefresh=!0},configLayer:function(t,e){this.painter.configLayer(t,e),this._needsRefresh=!0},refreshImmediately:function(){this._needsRefresh=!1,this.painter.refresh(),this._needsRefresh=!1},refresh:function(){this._needsRefresh=!0},flush:function(){this._needsRefresh&&this.refreshImmediately(),this._needsRefreshHover&&this.refreshHoverImmediately()},addHover:function(t,e){this.painter.addHover&&(this.painter.addHover(t,e),this.refreshHover())},removeHover:function(t){this.painter.removeHover&&(this.painter.removeHover(t),this.refreshHover())},clearHover:function(){this.painter.clearHover&&(this.painter.clearHover(),this.refreshHover())},refreshHover:function(){this._needsRefreshHover=!0},refreshHoverImmediately:function(){this._needsRefreshHover=!1,this.painter.refreshHover&&this.painter.refreshHover()},resize:function(t){t=t||{},this.painter.resize(t.width,t.height),this.handler.resize()},clearAnimation:function(){this.animation.clear()},getWidth:function(){return this.painter.getWidth()},getHeight:function(){return this.painter.getHeight()},pathToImage:function(t,e){return this.painter.pathToImage(t,e)},setCursorStyle:function(t){this.handler.setCursorStyle(t)},findHover:function(t,e){return this.handler.findHover(t,e)},on:function(t,e,i){this.handler.on(t,e,i)},off:function(t,e){this.handler.off(t,e)},trigger:function(t,e){this.handler.trigger(t,e)},clear:function(){this.storage.delRoot(),this.painter.clear()},dispose:function(){this.animation.stop(),this.clear(),this.storage.dispose(),this.painter.dispose(),this.handler.dispose(),this.animation=this.storage=this.painter=this.handler=null}};var Sc=1e-4,Mc=/^(?:(\d{4})(?:[-\/](\d{1,2})(?:[-\/](\d{1,2})(?:[T ](\d{1,2})(?::(\d\d)(?::(\d\d)(?:[.,](\d+))?)?)?(Z|[\+\-]\d\d:?\d\d)?)?)?)?)?$/,Tc=k,Ic=["a","b","c","d","e","f","g"],Cc=function(t,e){return"{"+t+(null==e?"":e)+"}"},Ac=function(t){return 10>t?"0"+t:t},kc=si,Lc=".",Dc="___EC__COMPONENT__CONTAINER___",Pc="\x00ec_\x00",Oc=function(t){for(var e=0;e<t.length;e++)t[e][1]||(t[e][1]=t[e][0]);return function(e,i,n){for(var r={},o=0;o<t.length;o++){var a=t[o][1];if(!(i&&l(i,a)>=0||n&&l(n,a)<0)){var s=e.getShallow(a);null!=s&&(r[t[o][0]]=s)}}return r}},zc=Oc([["lineWidth","width"],["stroke","color"],["opacity"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"]]),Bc={getLineStyle:function(t){var e=zc(this,t),i=this.getLineDash(e.lineWidth);return i&&(e.lineDash=i),e},getLineDash:function(t){null==t&&(t=1);var e=this.get("type"),i=Math.max(t,2),n=4*t;return"solid"===e||null==e?null:"dashed"===e?[n,n]:[i,i]}},Ec=Oc([["fill","color"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["opacity"],["shadowColor"]]),Rc={getAreaStyle:function(t,e){return Ec(this,t,e)}},Nc=Math.pow,Fc=Math.sqrt,Hc=1e-8,Vc=1e-4,Wc=Fc(3),Gc=1/3,Xc=E(),qc=E(),Yc=E(),Uc=Math.min,Zc=Math.max,jc=Math.sin,$c=Math.cos,Kc=2*Math.PI,Qc=E(),Jc=E(),td=E(),ed=[],id=[],nd={M:1,L:2,C:3,Q:4,A:5,Z:6,R:7},rd=[],od=[],ad=[],sd=[],ld=Math.min,hd=Math.max,ud=Math.cos,cd=Math.sin,dd=Math.sqrt,fd=Math.abs,gd="undefined"!=typeof Float32Array,pd=function(t){this._saveData=!t,this._saveData&&(this.data=[]),this._ctx=null};pd.prototype={constructor:pd,_xi:0,_yi:0,_x0:0,_y0:0,_ux:0,_uy:0,_len:0,_lineDash:null,_dashOffset:0,_dashIdx:0,_dashSum:0,setScale:function(t,e){this._ux=fd(1/Mu/t)||0,this._uy=fd(1/Mu/e)||0},getContext:function(){return this._ctx},beginPath:function(t){return this._ctx=t,t&&t.beginPath(),t&&(this.dpr=t.dpr),this._saveData&&(this._len=0),this._lineDash&&(this._lineDash=null,this._dashOffset=0),this},moveTo:function(t,e){return this.addData(nd.M,t,e),this._ctx&&this._ctx.moveTo(t,e),this._x0=t,this._y0=e,this._xi=t,this._yi=e,this},lineTo:function(t,e){var i=fd(t-this._xi)>this._ux||fd(e-this._yi)>this._uy||this._len<5;return this.addData(nd.L,t,e),this._ctx&&i&&(this._needsDash()?this._dashedLineTo(t,e):this._ctx.lineTo(t,e)),i&&(this._xi=t,this._yi=e),this},bezierCurveTo:function(t,e,i,n,r,o){return this.addData(nd.C,t,e,i,n,r,o),this._ctx&&(this._needsDash()?this._dashedBezierTo(t,e,i,n,r,o):this._ctx.bezierCurveTo(t,e,i,n,r,o)),this._xi=r,this._yi=o,this},quadraticCurveTo:function(t,e,i,n){return this.addData(nd.Q,t,e,i,n),this._ctx&&(this._needsDash()?this._dashedQuadraticTo(t,e,i,n):this._ctx.quadraticCurveTo(t,e,i,n)),this._xi=i,this._yi=n,this},arc:function(t,e,i,n,r,o){return this.addData(nd.A,t,e,i,i,n,r-n,0,o?0:1),this._ctx&&this._ctx.arc(t,e,i,n,r,o),this._xi=ud(r)*i+t,this._yi=cd(r)*i+t,this},arcTo:function(t,e,i,n,r){return this._ctx&&this._ctx.arcTo(t,e,i,n,r),this},rect:function(t,e,i,n){return this._ctx&&this._ctx.rect(t,e,i,n),this.addData(nd.R,t,e,i,n),this},closePath:function(){this.addData(nd.Z);var t=this._ctx,e=this._x0,i=this._y0;return t&&(this._needsDash()&&this._dashedLineTo(e,i),t.closePath()),this._xi=e,this._yi=i,this},fill:function(t){t&&t.fill(),this.toStatic()},stroke:function(t){t&&t.stroke(),this.toStatic()},setLineDash:function(t){if(t instanceof Array){this._lineDash=t,this._dashIdx=0;for(var e=0,i=0;i<t.length;i++)e+=t[i];this._dashSum=e}return this},setLineDashOffset:function(t){return this._dashOffset=t,this},len:function(){return this._len},setData:function(t){var e=t.length;this.data&&this.data.length==e||!gd||(this.data=new Float32Array(e));for(var i=0;e>i;i++)this.data[i]=t[i];this._len=e},appendPath:function(t){t instanceof Array||(t=[t]);for(var e=t.length,i=0,n=this._len,r=0;e>r;r++)i+=t[r].len();gd&&this.data instanceof Float32Array&&(this.data=new Float32Array(n+i));for(var r=0;e>r;r++)for(var o=t[r].data,a=0;a<o.length;a++)this.data[n++]=o[a];this._len=n},addData:function(t){if(this._saveData){var e=this.data;this._len+arguments.length>e.length&&(this._expandData(),e=this.data);for(var i=0;i<arguments.length;i++)e[this._len++]=arguments[i];this._prevCmd=t}},_expandData:function(){if(!(this.data instanceof Array)){for(var t=[],e=0;e<this._len;e++)t[e]=this.data[e];this.data=t}},_needsDash:function(){return this._lineDash},_dashedLineTo:function(t,e){var i,n,r=this._dashSum,o=this._dashOffset,a=this._lineDash,s=this._ctx,l=this._xi,h=this._yi,u=t-l,c=e-h,d=dd(u*u+c*c),f=l,g=h,p=a.length;for(u/=d,c/=d,0>o&&(o=r+o),o%=r,f-=o*u,g-=o*c;u>0&&t>=f||0>u&&f>=t||0==u&&(c>0&&e>=g||0>c&&g>=e);)n=this._dashIdx,i=a[n],f+=u*i,g+=c*i,this._dashIdx=(n+1)%p,u>0&&l>f||0>u&&f>l||c>0&&h>g||0>c&&g>h||s[n%2?"moveTo":"lineTo"](u>=0?ld(f,t):hd(f,t),c>=0?ld(g,e):hd(g,e));u=f-t,c=g-e,this._dashOffset=-dd(u*u+c*c)},_dashedBezierTo:function(t,e,i,n,r,o){var a,s,l,h,u,c=this._dashSum,d=this._dashOffset,f=this._lineDash,g=this._ctx,p=this._xi,v=this._yi,m=Nn,y=0,x=this._dashIdx,_=f.length,w=0;for(0>d&&(d=c+d),d%=c,a=0;1>a;a+=.1)s=m(p,t,i,r,a+.1)-m(p,t,i,r,a),l=m(v,e,n,o,a+.1)-m(v,e,n,o,a),y+=dd(s*s+l*l);for(;_>x&&(w+=f[x],!(w>d));x++);for(a=(w-d)/y;1>=a;)h=m(p,t,i,r,a),u=m(v,e,n,o,a),x%2?g.moveTo(h,u):g.lineTo(h,u),a+=f[x]/y,x=(x+1)%_;x%2!==0&&g.lineTo(r,o),s=r-h,l=o-u,this._dashOffset=-dd(s*s+l*l)},_dashedQuadraticTo:function(t,e,i,n){var r=i,o=n;i=(i+2*t)/3,n=(n+2*e)/3,t=(this._xi+2*t)/3,e=(this._yi+2*e)/3,this._dashedBezierTo(t,e,i,n,r,o)},toStatic:function(){var t=this.data;t instanceof Array&&(t.length=this._len,gd&&(this.data=new Float32Array(t)))},getBoundingRect:function(){rd[0]=rd[1]=ad[0]=ad[1]=Number.MAX_VALUE,od[0]=od[1]=sd[0]=sd[1]=-Number.MAX_VALUE;for(var t=this.data,e=0,i=0,n=0,r=0,o=0;o<t.length;){var a=t[o++];switch(1==o&&(e=t[o],i=t[o+1],n=e,r=i),a){case nd.M:n=t[o++],r=t[o++],e=n,i=r,ad[0]=n,ad[1]=r,sd[0]=n,sd[1]=r;break;case nd.L:$n(e,i,t[o],t[o+1],ad,sd),e=t[o++],i=t[o++];break;case nd.C:Kn(e,i,t[o++],t[o++],t[o++],t[o++],t[o],t[o+1],ad,sd),e=t[o++],i=t[o++];break;case nd.Q:Qn(e,i,t[o++],t[o++],t[o],t[o+1],ad,sd),e=t[o++],i=t[o++];break;case nd.A:var s=t[o++],l=t[o++],h=t[o++],u=t[o++],c=t[o++],d=t[o++]+c,f=(t[o++],1-t[o++]);1==o&&(n=ud(c)*h+s,r=cd(c)*u+l),Jn(s,l,h,u,c,d,f,ad,sd),e=ud(d)*h+s,i=cd(d)*u+l;break;case nd.R:n=e=t[o++],r=i=t[o++];var g=t[o++],p=t[o++];$n(n,r,n+g,r+p,ad,sd);break;case nd.Z:e=n,i=r
}U(rd,rd,ad),Z(od,od,sd)}return 0===o&&(rd[0]=rd[1]=od[0]=od[1]=0),new Ee(rd[0],rd[1],od[0]-rd[0],od[1]-rd[1])},rebuildPath:function(t){for(var e,i,n,r,o,a,s=this.data,l=this._ux,h=this._uy,u=this._len,c=0;u>c;){var d=s[c++];switch(1==c&&(n=s[c],r=s[c+1],e=n,i=r),d){case nd.M:e=n=s[c++],i=r=s[c++],t.moveTo(n,r);break;case nd.L:o=s[c++],a=s[c++],(fd(o-n)>l||fd(a-r)>h||c===u-1)&&(t.lineTo(o,a),n=o,r=a);break;case nd.C:t.bezierCurveTo(s[c++],s[c++],s[c++],s[c++],s[c++],s[c++]),n=s[c-2],r=s[c-1];break;case nd.Q:t.quadraticCurveTo(s[c++],s[c++],s[c++],s[c++]),n=s[c-2],r=s[c-1];break;case nd.A:var f=s[c++],g=s[c++],p=s[c++],v=s[c++],m=s[c++],y=s[c++],x=s[c++],_=s[c++],w=p>v?p:v,b=p>v?1:p/v,S=p>v?v/p:1,M=Math.abs(p-v)>.001,T=m+y;M?(t.translate(f,g),t.rotate(x),t.scale(b,S),t.arc(0,0,w,m,T,1-_),t.scale(1/b,1/S),t.rotate(-x),t.translate(-f,-g)):t.arc(f,g,w,m,T,1-_),1==c&&(e=ud(m)*p+f,i=cd(m)*v+g),n=ud(T)*p+f,r=cd(T)*v+g;break;case nd.R:e=n=s[c],i=r=s[c+1],t.rect(s[c++],s[c++],s[c++],s[c++]);break;case nd.Z:t.closePath(),n=e,r=i}}}},pd.CMD=nd;var vd=2*Math.PI,md=2*Math.PI,yd=2*Math.PI,xd=1e-4,_d=[-1,-1,-1],wd=[-1,-1],bd=Vu.prototype.getCanvasPattern,Sd=Math.abs,Md=new pd(!0);gr.prototype={constructor:gr,type:"path",__dirtyPath:!0,strokeContainThreshold:5,brush:function(t,e){var i=this.style,n=this.path||Md,r=i.hasStroke(),o=i.hasFill(),a=i.fill,s=i.stroke,l=o&&!!a.colorStops,h=r&&!!s.colorStops,u=o&&!!a.image,c=r&&!!s.image;if(i.bind(t,this,e),this.setTransform(t),this.__dirty){var d;l&&(d=d||this.getBoundingRect(),this._fillGradient=i.getGradient(t,a,d)),h&&(d=d||this.getBoundingRect(),this._strokeGradient=i.getGradient(t,s,d))}l?t.fillStyle=this._fillGradient:u&&(t.fillStyle=bd.call(a,t)),h?t.strokeStyle=this._strokeGradient:c&&(t.strokeStyle=bd.call(s,t));var f=i.lineDash,g=i.lineDashOffset,p=!!t.setLineDash,v=this.getGlobalScale();n.setScale(v[0],v[1]),this.__dirtyPath||f&&!p&&r?(n.beginPath(t),f&&!p&&(n.setLineDash(f),n.setLineDashOffset(g)),this.buildPath(n,this.shape,!1),this.path&&(this.__dirtyPath=!1)):(t.beginPath(),this.path.rebuildPath(t)),o&&n.fill(t),f&&p&&(t.setLineDash(f),t.lineDashOffset=g),r&&n.stroke(t),f&&p&&t.setLineDash([]),this.restoreTransform(t),null!=i.text&&this.drawRectText(t,this.getBoundingRect())},buildPath:function(){},createPathProxy:function(){this.path=new pd},getBoundingRect:function(){var t=this._rect,e=this.style,i=!t;if(i){var n=this.path;n||(n=this.path=new pd),this.__dirtyPath&&(n.beginPath(),this.buildPath(n,this.shape,!1)),t=n.getBoundingRect()}if(this._rect=t,e.hasStroke()){var r=this._rectWithStroke||(this._rectWithStroke=t.clone());if(this.__dirty||i){r.copy(t);var o=e.lineWidth,a=e.strokeNoScale?this.getLineScale():1;e.hasFill()||(o=Math.max(o,this.strokeContainThreshold||4)),a>1e-10&&(r.width+=o/a,r.height+=o/a,r.x-=o/a/2,r.y-=o/a/2)}return r}return t},contain:function(t,e){var i=this.transformCoordToLocal(t,e),n=this.getBoundingRect(),r=this.style;if(t=i[0],e=i[1],n.contain(t,e)){var o=this.path.data;if(r.hasStroke()){var a=r.lineWidth,s=r.strokeNoScale?this.getLineScale():1;if(s>1e-10&&(r.hasFill()||(a=Math.max(a,this.strokeContainThreshold)),fr(o,a/s,t,e)))return!0}if(r.hasFill())return dr(o,t,e)}return!1},dirty:function(t){null==t&&(t=!0),t&&(this.__dirtyPath=t,this._rect=null),this.__dirty=!0,this.__zr&&this.__zr.refresh(),this.__clipTarget&&this.__clipTarget.dirty()},animateShape:function(t){return this.animate("shape",t)},attrKV:function(t,e){"shape"===t?(this.setShape(e),this.__dirtyPath=!0,this._rect=null):Bi.prototype.attrKV.call(this,t,e)},setShape:function(t,e){var i=this.shape;if(i){if(w(t))for(var n in t)t.hasOwnProperty(n)&&(i[n]=t[n]);else i[t]=e;this.dirty(!0)}return this},getLineScale:function(){var t=this.transform;return t&&Sd(t[0]-1)>1e-10&&Sd(t[3]-1)>1e-10?Math.sqrt(Sd(t[0]*t[3]-t[2]*t[1])):1}},gr.extend=function(t){var e=function(e){gr.call(this,e),t.style&&this.style.extendFrom(t.style,!1);var i=t.shape;if(i){this.shape=this.shape||{};var n=this.shape;for(var r in i)!n.hasOwnProperty(r)&&i.hasOwnProperty(r)&&(n[r]=i[r])}t.init&&t.init.call(this,e)};h(e,gr);for(var i in t)"style"!==i&&"shape"!==i&&(e.prototype[i]=t[i]);return e},h(gr,Bi);var Td=[[],[],[]],Id=Math.sqrt,Cd=Math.atan2,Ad=function(t,e){var i,n,r,o,a,s,l=t.data,h=nd.M,u=nd.C,c=nd.L,d=nd.R,f=nd.A,g=nd.Q;for(r=0,o=0;r<l.length;){switch(i=l[r++],o=r,n=0,i){case h:n=1;break;case c:n=1;break;case u:n=3;break;case g:n=2;break;case f:var p=e[4],v=e[5],m=Id(e[0]*e[0]+e[1]*e[1]),y=Id(e[2]*e[2]+e[3]*e[3]),x=Cd(-e[1]/y,e[0]/m);l[r]*=m,l[r++]+=p,l[r]*=y,l[r++]+=v,l[r++]*=m,l[r++]*=y,l[r++]+=x,l[r++]+=x,r+=2,o=r;break;case d:s[0]=l[r++],s[1]=l[r++],Y(s,s,e),l[o++]=s[0],l[o++]=s[1],s[0]+=l[r++],s[1]+=l[r++],Y(s,s,e),l[o++]=s[0],l[o++]=s[1]}for(a=0;n>a;a++){var s=Td[a];s[0]=l[r++],s[1]=l[r++],Y(s,s,e),l[o++]=s[0],l[o++]=s[1]}}},kd=["m","M","l","L","v","V","h","H","z","Z","c","C","q","Q","t","T","s","S","a","A"],Ld=Math.sqrt,Dd=Math.sin,Pd=Math.cos,Od=Math.PI,zd=function(t){return Math.sqrt(t[0]*t[0]+t[1]*t[1])},Bd=function(t,e){return(t[0]*e[0]+t[1]*e[1])/(zd(t)*zd(e))},Ed=function(t,e){return(t[0]*e[1]<t[1]*e[0]?-1:1)*Math.acos(Bd(t,e))},Rd=function(t){Bi.call(this,t)};Rd.prototype={constructor:Rd,type:"text",brush:function(t,e){var i=this.style;this.__dirty&&mi(i,!0),i.fill=i.stroke=i.shadowBlur=i.shadowColor=i.shadowOffsetX=i.shadowOffsetY=null;var n=i.text;null!=n&&(n+=""),i.bind(t,this,e),zi(n,i)&&(this.setTransform(t),xi(this,t,n,i),this.restoreTransform(t))},getBoundingRect:function(){var t=this.style;if(this.__dirty&&mi(t,!0),!this._rect){var e=t.text;null!=e?e+="":e="";var i=ei(t.text+"",t.font,t.textAlign,t.textVerticalAlign,t.textPadding,t.rich);if(i.x+=t.x||0,i.y+=t.y||0,Li(t.textStroke,t.textStrokeWidth)){var n=t.textStrokeWidth;i.x-=n/2,i.y-=n/2,i.width+=n,i.height+=n}this._rect=i}return this._rect}},h(Rd,Bi);var Nd=gr.extend({type:"circle",shape:{cx:0,cy:0,r:0},buildPath:function(t,e,i){i&&t.moveTo(e.cx+e.r,e.cy),t.arc(e.cx,e.cy,e.r,0,2*Math.PI,!0)}}),Fd=[["shadowBlur",0],["shadowColor","#000"],["shadowOffsetX",0],["shadowOffsetY",0]],Hd=function(t){return Eh.browser.ie&&Eh.browser.version>=11?function(){var e,i=this.__clipPaths,n=this.style;if(i)for(var r=0;r<i.length;r++){var o=i[r],a=o&&o.shape,s=o&&o.type;if(a&&("sector"===s&&a.startAngle===a.endAngle||"rect"===s&&(!a.width||!a.height))){for(var l=0;l<Fd.length;l++)Fd[l][2]=n[Fd[l][0]],n[Fd[l][0]]=Fd[l][1];e=!0;break}}if(t.apply(this,arguments),e)for(var l=0;l<Fd.length;l++)n[Fd[l][0]]=Fd[l][2]}:t},Vd=gr.extend({type:"sector",shape:{cx:0,cy:0,r0:0,r:0,startAngle:0,endAngle:2*Math.PI,clockwise:!0},brush:Hd(gr.prototype.brush),buildPath:function(t,e){var i=e.cx,n=e.cy,r=Math.max(e.r0||0,0),o=Math.max(e.r,0),a=e.startAngle,s=e.endAngle,l=e.clockwise,h=Math.cos(a),u=Math.sin(a);t.moveTo(h*r+i,u*r+n),t.lineTo(h*o+i,u*o+n),t.arc(i,n,o,a,s,!l),t.lineTo(Math.cos(s)*r+i,Math.sin(s)*r+n),0!==r&&t.arc(i,n,r,s,a,l),t.closePath()}}),Wd=gr.extend({type:"ring",shape:{cx:0,cy:0,r:0,r0:0},buildPath:function(t,e){var i=e.cx,n=e.cy,r=2*Math.PI;t.moveTo(i+e.r,n),t.arc(i,n,e.r,0,r,!1),t.moveTo(i+e.r0,n),t.arc(i,n,e.r0,0,r,!0)}}),Gd=function(t,e){for(var i=t.length,n=[],r=0,o=1;i>o;o++)r+=X(t[o-1],t[o]);var a=r/2;a=i>a?i:a;for(var o=0;a>o;o++){var s,l,h,u=o/(a-1)*(e?i:i-1),c=Math.floor(u),d=u-c,f=t[c%i];e?(s=t[(c-1+i)%i],l=t[(c+1)%i],h=t[(c+2)%i]):(s=t[0===c?c:c-1],l=t[c>i-2?i-1:c+1],h=t[c>i-3?i-1:c+2]);var g=d*d,p=d*g;n.push([wr(s[0],f[0],l[0],h[0],d,g,p),wr(s[1],f[1],l[1],h[1],d,g,p)])}return n},Xd=function(t,e,i,n){var r,o,a,s,l=[],h=[],u=[],c=[];if(n){a=[1/0,1/0],s=[-1/0,-1/0];for(var d=0,f=t.length;f>d;d++)U(a,a,t[d]),Z(s,s,t[d]);U(a,a,n[0]),Z(s,s,n[1])}for(var d=0,f=t.length;f>d;d++){var g=t[d];if(i)r=t[d?d-1:f-1],o=t[(d+1)%f];else{if(0===d||d===f-1){l.push(R(t[d]));continue}r=t[d-1],o=t[d+1]}F(h,o,r),W(h,h,e);var p=X(g,r),v=X(g,o),m=p+v;0!==m&&(p/=m,v/=m),W(u,h,-p),W(c,h,v);var y=N([],g,u),x=N([],g,c);n&&(Z(y,y,a),U(y,y,s),Z(x,x,a),U(x,x,s)),l.push(y),l.push(x)}return i&&l.push(l.shift()),l},qd=gr.extend({type:"polygon",shape:{points:null,smooth:!1,smoothConstraint:null},buildPath:function(t,e){br(t,e,!0)}}),Yd=gr.extend({type:"polyline",shape:{points:null,smooth:!1,smoothConstraint:null},style:{stroke:"#000",fill:null},buildPath:function(t,e){br(t,e,!1)}}),Ud=gr.extend({type:"rect",shape:{r:0,x:0,y:0,width:0,height:0},buildPath:function(t,e){var i=e.x,n=e.y,r=e.width,o=e.height;e.r?vi(t,e):t.rect(i,n,r,o),t.closePath()}}),Zd=gr.extend({type:"line",shape:{x1:0,y1:0,x2:0,y2:0,percent:1},style:{stroke:"#000",fill:null},buildPath:function(t,e){var i=e.x1,n=e.y1,r=e.x2,o=e.y2,a=e.percent;0!==a&&(t.moveTo(i,n),1>a&&(r=i*(1-a)+r*a,o=n*(1-a)+o*a),t.lineTo(r,o))},pointAt:function(t){var e=this.shape;return[e.x1*(1-t)+e.x2*t,e.y1*(1-t)+e.y2*t]}}),jd=[],$d=gr.extend({type:"bezier-curve",shape:{x1:0,y1:0,x2:0,y2:0,cpx1:0,cpy1:0,percent:1},style:{stroke:"#000",fill:null},buildPath:function(t,e){var i=e.x1,n=e.y1,r=e.x2,o=e.y2,a=e.cpx1,s=e.cpy1,l=e.cpx2,h=e.cpy2,u=e.percent;0!==u&&(t.moveTo(i,n),null==l||null==h?(1>u&&(Zn(i,a,r,u,jd),a=jd[1],r=jd[2],Zn(n,s,o,u,jd),s=jd[1],o=jd[2]),t.quadraticCurveTo(a,s,r,o)):(1>u&&(Wn(i,a,l,r,u,jd),a=jd[1],l=jd[2],r=jd[3],Wn(n,s,h,o,u,jd),s=jd[1],h=jd[2],o=jd[3]),t.bezierCurveTo(a,s,l,h,r,o)))},pointAt:function(t){return Sr(this.shape,t,!1)},tangentAt:function(t){var e=Sr(this.shape,t,!0);return G(e,e)}}),Kd=gr.extend({type:"arc",shape:{cx:0,cy:0,r:0,startAngle:0,endAngle:2*Math.PI,clockwise:!0},style:{stroke:"#000",fill:null},buildPath:function(t,e){var i=e.cx,n=e.cy,r=Math.max(e.r,0),o=e.startAngle,a=e.endAngle,s=e.clockwise,l=Math.cos(o),h=Math.sin(o);t.moveTo(l*r+i,h*r+n),t.arc(i,n,r,o,a,!s)}}),Qd=gr.extend({type:"compound",shape:{paths:null},_updatePathDirty:function(){for(var t=this.__dirtyPath,e=this.shape.paths,i=0;i<e.length;i++)t=t||e[i].__dirtyPath;this.__dirtyPath=t,this.__dirty=this.__dirty||t},beforeBrush:function(){this._updatePathDirty();for(var t=this.shape.paths||[],e=this.getGlobalScale(),i=0;i<t.length;i++)t[i].path||t[i].createPathProxy(),t[i].path.setScale(e[0],e[1])},buildPath:function(t,e){for(var i=e.paths||[],n=0;n<i.length;n++)i[n].buildPath(t,i[n].shape,!0)},afterBrush:function(){for(var t=this.shape.paths||[],e=0;e<t.length;e++)t[e].__dirtyPath=!1},getBoundingRect:function(){return this._updatePathDirty(),gr.prototype.getBoundingRect.call(this)}}),Jd=function(t){this.colorStops=t||[]};Jd.prototype={constructor:Jd,addColorStop:function(t,e){this.colorStops.push({offset:t,color:e})}};var tf=function(t,e,i,n,r,o){this.x=null==t?0:t,this.y=null==e?0:e,this.x2=null==i?1:i,this.y2=null==n?0:n,this.type="linear",this.global=o||!1,Jd.call(this,r)};tf.prototype={constructor:tf},h(tf,Jd);var ef=function(t,e,i,n,r){this.x=null==t?.5:t,this.y=null==e?.5:e,this.r=null==i?.5:i,this.type="radial",this.global=r||!1,Jd.call(this,n)};ef.prototype={constructor:ef},h(ef,Jd);var nf=Math.round,rf=Math.max,of=Math.min,af={},sf=_r,lf=(Object.freeze||Object)({extendShape:Mr,extendPath:Tr,makePath:Ir,makeImage:Cr,mergePath:sf,resizePath:kr,subPixelOptimizeLine:Lr,subPixelOptimizeRect:Dr,subPixelOptimize:Pr,setHoverStyle:qr,setLabelStyle:Yr,setTextStyle:Ur,setText:Zr,getFont:eo,updateProps:no,initProps:ro,getTransform:oo,applyTransform:ao,transformDirection:so,groupTransition:lo,clipPointsByRect:ho,clipRectByRect:uo,createIcon:co,Group:Pu,Image:Ei,Text:Rd,Circle:Nd,Sector:Vd,Ring:Wd,Polygon:qd,Polyline:Yd,Rect:Ud,Line:Zd,BezierCurve:$d,Arc:Kd,CompoundPath:Qd,LinearGradient:tf,RadialGradient:ef,BoundingRect:Ee}),hf=["textStyle","color"],uf={getTextColor:function(t){var e=this.ecModel;return this.getShallow("color")||(!t&&e?e.get(hf):null)},getFont:function(){return eo({fontStyle:this.getShallow("fontStyle"),fontWeight:this.getShallow("fontWeight"),fontSize:this.getShallow("fontSize"),fontFamily:this.getShallow("fontFamily")},this.ecModel)},getTextRect:function(t){return ei(t,this.getFont(),this.getShallow("align"),this.getShallow("verticalAlign")||this.getShallow("baseline"),this.getShallow("padding"),this.getShallow("rich"),this.getShallow("truncateText"))}},cf=Oc([["fill","color"],["stroke","borderColor"],["lineWidth","borderWidth"],["opacity"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"],["textPosition"],["textAlign"]]),df={getItemStyle:function(t,e){var i=cf(this,t,e),n=this.getBorderLineDash();return n&&(i.lineDash=n),i},getBorderLineDash:function(){var t=this.get("borderType");return"solid"===t||null==t?null:"dashed"===t?[5,5]:[1,1]}},ff=u;fo.prototype={constructor:fo,init:null,mergeOption:function(t){n(this.option,t,!0)},get:function(t,e){return null==t?this.option:go(this.option,this.parsePath(t),!e&&po(this,t))},getShallow:function(t,e){var i=this.option,n=null==i?i:i[t],r=!e&&po(this,t);return null==n&&r&&(n=r.getShallow(t)),n},getModel:function(t,e){var i,n=null==t?this.option:go(this.option,t=this.parsePath(t));return e=e||(i=po(this,t))&&i.getModel(t),new fo(n,e,this.ecModel)},isEmpty:function(){return null==this.option},restoreData:function(){},clone:function(){var t=this.constructor;return new t(i(this.option))},setReadOnly:function(){},parsePath:function(t){return"string"==typeof t&&(t=t.split(".")),t},customizeGetParent:function(t){Cn(this,"getParent",t)},isAnimationEnabled:function(){if(!Eh.node){if(null!=this.option.animation)return!!this.option.animation;if(this.parentModel)return this.parentModel.isAnimationEnabled()}}},Pn(fo),ff(fo,Bc),ff(fo,Rc),ff(fo,uf),ff(fo,df);var gf=d,pf=w,vf=["fontStyle","fontWeight","fontSize","fontFamily","rich","tag","color","textBorderColor","textBorderWidth","width","height","lineHeight","align","verticalAlign","baseline","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY","textShadowColor","textShadowBlur","textShadowOffsetX","textShadowOffsetY","backgroundColor","borderColor","borderWidth","borderRadius","padding"],mf={getDataParams:function(t,e){var i=this.getData(e),n=this.getRawValue(t,e),r=i.getRawIndex(t),o=i.getName(t,!0),a=i.getRawDataItem(t),s=i.getItemVisual(t,"color");return{componentType:this.mainType,componentSubType:this.subType,seriesType:"series"===this.mainType?this.subType:null,seriesIndex:this.seriesIndex,seriesId:this.id,seriesName:this.name,name:o,dataIndex:r,data:a,dataType:e,value:n,color:s,marker:Tn(s),$vars:["seriesName","name","value"]}},getFormattedLabel:function(t,e,i,n,r){e=e||"normal";var o=this.getData(i),a=o.getItemModel(t),s=this.getDataParams(t,i);null!=n&&s.value instanceof Array&&(s.value=s.value[n]);var l=a.get([r||"label",e,"formatter"]);return"function"==typeof l?(s.status=e,l(s)):"string"==typeof l?Mn(l,s):void 0},getRawValue:function(t,e){var i=this.getData(e),n=i.getRawDataItem(t);return null!=n?!pf(n)||n instanceof Array?n:n.value:void 0},formatTooltip:B},yf=function(){var t=0;return function(){var e="\x00__ec_prop_getter_"+t++;return function(t){return t[e]||(t[e]={})}}}(),xf=0,_f="_",wf=d,bf=["left","right","top","bottom","width","height"],Sf=[["width","left","right"],["height","top","bottom"]],Mf=Oo,Tf=(m(Oo,"vertical"),m(Oo,"horizontal"),{getBoxLayoutParams:function(){return{left:this.get("left"),top:this.get("top"),right:this.get("right"),bottom:this.get("bottom"),width:this.get("width"),height:this.get("height")}}}),If=Array.prototype.push,Cf=fo.extend({type:"component",id:"",name:"",mainType:"",subType:"",componentIndex:0,defaultOption:null,ecModel:null,dependentModels:[],uid:null,layoutMode:null,$constructor:function(t,e,i,n){fo.call(this,t,e,i,n),this.uid=Lo("componentModel")},init:function(t,e,i){this.mergeDefaultAndTheme(t,i)},mergeDefaultAndTheme:function(t,e){var i=this.layoutMode,r=i?Eo(t):{},o=e.getTheme();n(t,o.get(this.mainType)),n(t,this.getDefaultOption()),i&&Bo(t,r,i)},mergeOption:function(t){n(this.option,t,!0);var e=this.layoutMode;e&&Bo(this.option,t,e)},optionUpdated:function(){},getDefaultOption:function(){if(!kn(this,"__defaultOption")){for(var t=[],e=this.constructor;e;){var i=e.prototype.defaultOption;i&&t.push(i),e=e.superClass}for(var r={},o=t.length-1;o>=0;o--)r=n(r,t[o],!0);Cn(this,"__defaultOption",r)}return An(this,"__defaultOption")},getReferringComponents:function(t){return this.ecModel.queryComponents({mainType:t,index:this.get(t+"Index",!0),id:this.get(t+"Id",!0)})}});Bn(Cf,{registerWhenExtend:!0}),Do(Cf),Po(Cf,No),u(Cf,Tf);var Af="";"undefined"!=typeof navigator&&(Af=navigator.platform||"");var kf={color:["#c23531","#2f4554","#61a0a8","#d48265","#91c7ae","#749f83","#ca8622","#bda29a","#6e7074","#546570","#c4ccd3"],textStyle:{fontFamily:Af.match(/^Win/)?"Microsoft YaHei":"sans-serif",fontSize:12,fontStyle:"normal",fontWeight:"normal"},blendMode:null,animation:"auto",animationDuration:1e3,animationDurationUpdate:300,animationEasing:"exponentialOut",animationEasingUpdate:"cubicOut",animationThreshold:2e3,progressiveThreshold:3e3,progressive:400,hoverLayerThreshold:3e3,useUTC:!1},Lf={clearColorPalette:function(){Cn(this,"colorIdx",0),Cn(this,"colorNameMap",{})},getColorFromPalette:function(t,e){e=e||this;var i=An(e,"colorIdx")||0,n=An(e,"colorNameMap")||Cn(e,"colorNameMap",{});if(n.hasOwnProperty(t))return n[t];var r=this.get("color",!0)||[];if(r.length){var o=r[i];return t&&(n[t]=o),Cn(e,"colorIdx",(i+1)%r.length),o}}},Df=d,Pf=p,Of=f,zf=y,Bf=l,Ef=w,Rf="\x00_ec_inner",Nf=fo.extend({constructor:Nf,init:function(t,e,i,n){i=i||{},this.option=null,this._theme=new fo(i),this._optionManager=n},setOption:function(t,e){L(!(Rf in t),"please use chart.getOption()"),this._optionManager.setOption(t,e),this.resetOption(null)},resetOption:function(t){var e=!1,i=this._optionManager;if(!t||"recreate"===t){var n=i.mountOption("recreate"===t);this.option&&"recreate"!==t?(this.restoreData(),this.mergeOption(n)):Ho.call(this,n),e=!0}if(("timeline"===t||"media"===t)&&this.restoreData(),!t||"recreate"===t||"timeline"===t){var r=i.getTimelineOption(this);r&&(this.mergeOption(r),e=!0)}if(!t||"recreate"===t||"media"===t){var o=i.getMediaOption(this,this._api);o.length&&Df(o,function(t){this.mergeOption(t,e=!0)},this)}return e},mergeOption:function(t){function e(e,i){var n=vo(t[e]),s=wo(a.get(e),n);bo(s),Df(s,function(t){var i=t.option;Ef(i)&&(t.keyInfo.mainType=e,t.keyInfo.subType=Wo(e,i,t.exist))});var l=Vo(a,i);r[e]=[],a.set(e,[]),Df(s,function(t,i){var n=t.exist,s=t.option;if(L(Ef(s)||n,"Empty component definition"),s){var h=Cf.getClass(e,t.keyInfo.subType,!0);if(n&&n instanceof h)n.name=t.keyInfo.name,n.mergeOption(s,this),n.optionUpdated(s,!1);else{var u=o({dependentModels:l,componentIndex:i},t.keyInfo);n=new h(s,this,this,u),o(n,u),n.init(s,this,this,u),n.optionUpdated(null,!0)}}else n.mergeOption({},this),n.optionUpdated({},!1);a.get(e)[i]=n,r[e][i]=n.option},this),"series"===e&&(this._seriesIndices=Go(a.get("series")))}var r=this.option,a=this._componentsMap,s=[];Df(t,function(t,e){null!=t&&(Cf.hasClass(e)?s.push(e):r[e]=null==r[e]?i(t):n(r[e],t,!0))}),Cf.topologicalTravel(s,Cf.getAllClassMainTypes(),e,this),this._seriesIndices=this._seriesIndices||[]},getOption:function(){var t=i(this.option);return Df(t,function(e,i){if(Cf.hasClass(i)){for(var e=vo(e),n=e.length-1;n>=0;n--)So(e[n])&&e.splice(n,1);t[i]=e}}),delete t[Rf],t},getTheme:function(){return this._theme},getComponent:function(t,e){var i=this._componentsMap.get(t);return i?i[e||0]:void 0},queryComponents:function(t){var e=t.mainType;if(!e)return[];var i=t.index,n=t.id,r=t.name,o=this._componentsMap.get(e);if(!o||!o.length)return[];var a;if(null!=i)zf(i)||(i=[i]),a=Pf(Of(i,function(t){return o[t]}),function(t){return!!t});else if(null!=n){var s=zf(n);a=Pf(o,function(t){return s&&Bf(n,t.id)>=0||!s&&t.id===n})}else if(null!=r){var l=zf(r);a=Pf(o,function(t){return l&&Bf(r,t.name)>=0||!l&&t.name===r})}else a=o.slice();return Xo(a,t)},findComponents:function(t){function e(t){var e=r+"Index",i=r+"Id",n=r+"Name";return!t||null==t[e]&&null==t[i]&&null==t[n]?null:{mainType:r,index:t[e],id:t[i],name:t[n]}}function i(e){return t.filter?Pf(e,t.filter):e}var n=t.query,r=t.mainType,o=e(n),a=o?this.queryComponents(o):this._componentsMap.get(r);return i(Xo(a,t))},eachComponent:function(t,e,i){var n=this._componentsMap;if("function"==typeof t)i=e,e=t,n.each(function(t,n){Df(t,function(t,r){e.call(i,n,t,r)})});else if(_(t))Df(n.get(t),e,i);else if(Ef(t)){var r=this.findComponents(t);Df(r,e,i)}},getSeriesByName:function(t){var e=this._componentsMap.get("series");return Pf(e,function(e){return e.name===t})},getSeriesByIndex:function(t){return this._componentsMap.get("series")[t]},getSeriesByType:function(t){var e=this._componentsMap.get("series");return Pf(e,function(e){return e.subType===t})},getSeries:function(){return this._componentsMap.get("series").slice()},eachSeries:function(t,e){qo(this),Df(this._seriesIndices,function(i){var n=this._componentsMap.get("series")[i];t.call(e,n,i)},this)},eachRawSeries:function(t,e){Df(this._componentsMap.get("series"),t,e)},eachSeriesByType:function(t,e,i){qo(this),Df(this._seriesIndices,function(n){var r=this._componentsMap.get("series")[n];r.subType===t&&e.call(i,r,n)},this)},eachRawSeriesByType:function(t,e,i){return Df(this.getSeriesByType(t),e,i)},isSeriesFiltered:function(t){return qo(this),l(this._seriesIndices,t.componentIndex)<0},getCurrentSeriesIndices:function(){return(this._seriesIndices||[]).slice()},filterSeries:function(t,e){qo(this);var i=Pf(this._componentsMap.get("series"),t,e);this._seriesIndices=Go(i)},restoreData:function(){var t=this._componentsMap;this._seriesIndices=Go(t.get("series"));var e=[];t.each(function(t,i){e.push(i)}),Cf.topologicalTravel(e,Cf.getAllClassMainTypes(),function(e){Df(t.get(e),function(t){t.restoreData()})})}});u(Nf,Lf);var Ff=["getDom","getZr","getWidth","getHeight","getDevicePixelRatio","dispatchAction","isDisposed","on","off","getDataURL","getConnectedDataURL","getModel","getOption","getViewOfComponentModel","getViewOfSeriesModel"],Hf={};Uo.prototype={constructor:Uo,create:function(t,e){var i=[];d(Hf,function(n){var r=n.create(t,e);i=i.concat(r||[])}),this._coordinateSystems=i},update:function(t,e){d(this._coordinateSystems,function(i){i.update&&i.update(t,e)})},getCoordinateSystems:function(){return this._coordinateSystems.slice()}},Uo.register=function(t,e){Hf[t]=e},Uo.get=function(t){return Hf[t]};var Vf=d,Wf=i,Gf=f,Xf=n,qf=/^(min|max)?(.+)$/;Zo.prototype={constructor:Zo,setOption:function(t,e){t=Wf(t,!0);var i=this._optionBackup,n=jo.call(this,t,e,!i);this._newBaseOption=n.baseOption,i?(Jo(i.baseOption,n.baseOption),n.timelineOptions.length&&(i.timelineOptions=n.timelineOptions),n.mediaList.length&&(i.mediaList=n.mediaList),n.mediaDefault&&(i.mediaDefault=n.mediaDefault)):this._optionBackup=n},mountOption:function(t){var e=this._optionBackup;return this._timelineOptions=Gf(e.timelineOptions,Wf),this._mediaList=Gf(e.mediaList,Wf),this._mediaDefault=Wf(e.mediaDefault),this._currentMediaIndices=[],Wf(t?e.baseOption:this._newBaseOption)},getTimelineOption:function(t){var e,i=this._timelineOptions;if(i.length){var n=t.getComponent("timeline");n&&(e=Wf(i[n.getCurrentIndex()],!0))}return e},getMediaOption:function(){var t=this._api.getWidth(),e=this._api.getHeight(),i=this._mediaList,n=this._mediaDefault,r=[],o=[];if(!i.length&&!n)return o;for(var a=0,s=i.length;s>a;a++)$o(i[a].query,t,e)&&r.push(a);return!r.length&&n&&(r=[-1]),r.length&&!Qo(r,this._currentMediaIndices)&&(o=Gf(r,function(t){return Wf(-1===t?n.option:i[t].option)})),this._currentMediaIndices=r,o}};var Yf=d,Uf=w,Zf=["areaStyle","lineStyle","nodeStyle","linkStyle","chordStyle","label","labelLine"],jf=function(t,e){Yf(ra(t.series),function(t){Uf(t)&&na(t)});var i=["xAxis","yAxis","radiusAxis","angleAxis","singleAxis","parallelAxis","radar"];e&&i.push("valueAxis","categoryAxis","logAxis","timeAxis"),Yf(i,function(e){Yf(ra(t[e]),function(t){t&&(ea(t,"axisLabel"),ea(t.axisPointer,"label"))})}),Yf(ra(t.parallel),function(t){var e=t&&t.parallelAxisDefault;ea(e,"axisLabel"),ea(e&&e.axisPointer,"label")}),Yf(ra(t.calendar),function(t){ea(t,"dayLabel"),ea(t,"monthLabel"),ea(t,"yearLabel")}),Yf(ra(t.radar),function(t){ea(t,"name")}),Yf(ra(t.geo),function(t){Uf(t)&&(ia(t.label),Yf(ra(t.regions),function(t){ia(t.label)}))}),ia(oa(t.timeline).label),ea(oa(t.axisPointer),"label"),ea(oa(t.tooltip).axisPointer,"label")},$f=[["x","left"],["y","top"],["x2","right"],["y2","bottom"]],Kf=["grid","geo","parallel","legend","toolbox","title","visualMap","dataZoom","timeline"],Qf=["bar","boxplot","candlestick","chord","effectScatter","funnel","gauge","lines","graph","heatmap","line","map","parallel","pie","radar","sankey","scatter","treemap"],Jf=function(t,e){jf(t,e),t.series=vo(t.series),d(t.series,function(t){if(w(t)){var e=t.type;if(("pie"===e||"gauge"===e)&&null!=t.clockWise&&(t.clockwise=t.clockWise),"gauge"===e){var i=aa(t,"pointer.color");null!=i&&sa(t,"itemStyle.normal.color",i)}for(var n=0;n<Qf.length;n++)if(Qf[n]===t.type){la(t);break}}}),t.dataRange&&(t.visualMap=t.dataRange),d(Kf,function(e){var i=t[e];i&&(y(i)||(i=[i]),d(i,function(t){la(t)}))})},tg=Cf.extend({type:"series.__base__",seriesIndex:0,coordinateSystem:null,defaultOption:null,legendDataProvider:null,visualColorAccessPath:"itemStyle.normal.color",layoutMode:null,init:function(t,e,i){this.seriesIndex=this.componentIndex,this.mergeDefaultAndTheme(t,i);var n=this.getInitialData(t,i);L(n,"getInitialData returned invalid data."),Cn(this,"dataBeforeProcessed",n),this.restoreData()},mergeDefaultAndTheme:function(t,e){var i=this.layoutMode,r=i?Eo(t):{},o=this.subType;Cf.hasClass(o)&&(o+="Series"),n(t,e.getTheme().get(this.subType)),n(t,this.getDefaultOption()),mo(t.label,["show"]),this.fillDataTextStyle(t.data),i&&Bo(t,r,i)},mergeOption:function(t,e){t=n(this.option,t,!0),this.fillDataTextStyle(t.data);var i=this.layoutMode;i&&Bo(this.option,t,i);var r=this.getInitialData(t,e);r&&(Cn(this,"data",r),Cn(this,"dataBeforeProcessed",r.cloneShallow()))},fillDataTextStyle:function(t){if(t)for(var e=["show"],i=0;i<t.length;i++)t[i]&&t[i].label&&mo(t[i].label,e)},getInitialData:function(){},getData:function(t){var e=An(this,"data");return null==t?e:e.getLinkedData(t)},setData:function(t){Cn(this,"data",t)},getRawData:function(){return An(this,"dataBeforeProcessed")},coordDimToDataDim:function(t){return Co(this.getData(),t)},dataDimToCoordDim:function(t){return Io(this.getData(),t)},getBaseAxis:function(){var t=this.coordinateSystem;return t&&t.getBaseAxis&&t.getBaseAxis()},formatTooltip:function(t,e){function i(i){function r(t,i){var r=n.getDimensionInfo(i);if(r&&r.otherDims.tooltip!==!1){var s=r.type,l=(o?"- "+(r.tooltipName||r.name)+": ":"")+("ordinal"===s?t+"":"time"===s?e?"":In("yyyy/MM/dd hh:mm:ss",t):wn(t));l&&a.push(Sn(l))}}var o=g(i,function(t,e,i){var r=n.getDimensionInfo(i);return t|=r&&r.tooltip!==!1&&null!=r.tooltipName},0),a=[],s=Ao(n,"tooltip");return s.length?d(s,function(e){r(n.get(e,t),e)}):d(i,r),(o?"<br/>":"")+a.join(o?"<br/>":", ")}var n=An(this,"data"),r=this.getRawValue(t),o=y(r)?i(r):Sn(wn(r)),a=n.getName(t),s=n.getItemVisual(t,"color");w(s)&&s.colorStops&&(s=(s.colorStops[0]||{}).color),s=s||"transparent";var l=Tn(s),h=this.name;return"\x00-"===h&&(h=""),h=h?Sn(h)+(e?": ":"<br/>"):"",e?l+h+o:h+l+(a?Sn(a)+": "+o:o)},isAnimationEnabled:function(){if(Eh.node)return!1;var t=this.getShallow("animation");return t&&this.getData().count()>this.getShallow("animationThreshold")&&(t=!1),t},restoreData:function(){Cn(this,"data",An(this,"dataBeforeProcessed").cloneShallow())},getColorFromPalette:function(t,e){var i=this.ecModel,n=Lf.getColorFromPalette.call(this,t,e);return n||(n=i.getColorFromPalette(t,e)),n},getAxisTooltipData:null,getTooltipPosition:null});u(tg,mf),u(tg,Lf);var eg=function(){this.group=new Pu,this.uid=Lo("viewComponent")};eg.prototype={constructor:eg,init:function(){},render:function(){},dispose:function(){}};var ig=eg.prototype;ig.updateView=ig.updateLayout=ig.updateVisual=function(){},Pn(eg),Bn(eg,{registerWhenExtend:!0}),ha.prototype={type:"chart",init:function(){},render:function(){},highlight:function(t,e,i,n){ca(t.getData(),n,"emphasis")},downplay:function(t,e,i,n){ca(t.getData(),n,"normal")},remove:function(){this.group.removeAll()},dispose:function(){}};var ng=ha.prototype;ng.updateView=ng.updateLayout=ng.updateVisual=function(t,e,i,n){this.render(t,e,i,n)},Pn(ha,["dispose"]),Bn(ha,{registerWhenExtend:!0});var rg="\x00__throttleOriginMethod",og="\x00__throttleRate",ag="\x00__throttleType",sg=function(t){function e(e){var i=(e.visualColorAccessPath||"itemStyle.normal.color").split("."),n=e.getData(),r=e.get(i)||e.getColorFromPalette(e.get("name"));n.setVisual("color",r),t.isSeriesFiltered(e)||("function"!=typeof r||r instanceof Jd||n.each(function(t){n.setItemVisual(t,"color",r(e.getDataParams(t)))}),n.each(function(t){var e=n.getItemModel(t),r=e.get(i,!0);null!=r&&n.setItemVisual(t,"color",r)}))}t.eachRawSeries(e)},lg=Math.PI,hg=function(t,e){e=e||{},a(e,{text:"loading",color:"#c23531",textColor:"#000",maskColor:"rgba(255, 255, 255, 0.8)",zlevel:0});var i=new Ud({style:{fill:e.maskColor},zlevel:e.zlevel,z:1e4}),n=new Kd({shape:{startAngle:-lg/2,endAngle:-lg/2+.1,r:10},style:{stroke:e.color,lineCap:"round",lineWidth:5},zlevel:e.zlevel,z:10001}),r=new Ud({style:{fill:"none",text:e.text,textPosition:"right",textDistance:10,textFill:e.textColor},zlevel:e.zlevel,z:10001});n.animateShape(!0).when(1e3,{endAngle:3*lg/2}).start("circularInOut"),n.animateShape(!0).when(1e3,{startAngle:3*lg/2}).delay(300).start("circularInOut");var o=new Pu;return o.add(n),o.add(r),o.add(i),o.resize=function(){var e=t.getWidth()/2,o=t.getHeight()/2;n.setShape({cx:e,cy:o});var a=n.shape.r;r.setShape({x:e-a,y:o-a,width:2*a,height:2*a}),i.setShape({x:0,y:0,width:t.getWidth(),height:t.getHeight()})},o.resize(),o},ug=d,cg=Cf.parseClassType,dg="3.8.0",fg={zrender:"3.7.0"},gg=1e3,pg=5e3,vg=1e3,mg=2e3,yg=3e3,xg=4e3,_g=5e3,wg={PROCESSOR:{FILTER:gg,STATISTIC:pg},VISUAL:{LAYOUT:vg,GLOBAL:mg,CHART:yg,COMPONENT:xg,BRUSH:_g}},bg="__flagInMainProcess",Sg="__hasGradientOrPatternBg",Mg="__optionUpdated",Tg=/^[a-zA-Z0-9_]+$/;pa.prototype.on=ga("on"),pa.prototype.off=ga("off"),pa.prototype.one=ga("one"),u(pa,eu);var Ig=va.prototype;Ig._onframe=function(){if(this[Mg]){var t=this[Mg].silent;this[bg]=!0,Cg.prepareAndUpdate.call(this),this[bg]=!1,this[Mg]=!1,_a.call(this,t),wa.call(this,t)}},Ig.getDom=function(){return this._dom},Ig.getZr=function(){return this._zr},Ig.setOption=function(t,e,i){L(!this[bg],"`setOption` should not be called during main process.");var n;if(w(e)&&(i=e.lazyUpdate,n=e.silent,e=e.notMerge),this[bg]=!0,!this._model||e){var r=new Zo(this._api),o=this._theme,a=this._model=new Nf(null,null,o,r);a.init(null,null,o,r)}this._model.setOption(t,Pg),i?(this[Mg]={silent:n},this[bg]=!1):(Cg.prepareAndUpdate.call(this),this._zr.flush(),this[Mg]=!1,this[bg]=!1,_a.call(this,n),wa.call(this,n))},Ig.setTheme=function(){console.log("ECharts#setTheme() is DEPRECATED in ECharts 3.0")},Ig.getModel=function(){return this._model},Ig.getOption=function(){return this._model&&this._model.getOption()},Ig.getWidth=function(){return this._zr.getWidth()},Ig.getHeight=function(){return this._zr.getHeight()},Ig.getDevicePixelRatio=function(){return this._zr.painter.dpr||window.devicePixelRatio||1},Ig.getRenderedCanvas=function(t){if(Eh.canvasSupported){t=t||{},t.pixelRatio=t.pixelRatio||1,t.backgroundColor=t.backgroundColor||this._model.get("backgroundColor");var e=this._zr,i=e.storage.getDisplayList();return d(i,function(t){t.stopAnimation(!0)}),e.painter.getRenderedCanvas(t)}},Ig.getSvgDataUrl=function(){if(Eh.svgSupported){var t=this._zr,e=t.storage.getDisplayList();return d(e,function(t){t.stopAnimation(!0)}),t.painter.pathToSvg()}},Ig.getDataURL=function(t){t=t||{};var e=t.excludeComponents,i=this._model,n=[],r=this;ug(e,function(t){i.eachComponent({mainType:t},function(t){var e=r._componentsMap[t.__viewId];e.group.ignore||(n.push(e),e.group.ignore=!0)})});var o="svg"===this._zr.painter.getType()?this.getSvgDataUrl():this.getRenderedCanvas(t).toDataURL("image/"+(t&&t.type||"png"));return ug(n,function(t){t.group.ignore=!1}),o},Ig.getConnectedDataURL=function(t){if(Eh.canvasSupported){var e=this.group,n=Math.min,r=Math.max,o=1/0;
if(Ng[e]){var a=o,s=o,l=-o,h=-o,u=[],c=t&&t.pixelRatio||1;d(Rg,function(o){if(o.group===e){var c=o.getRenderedCanvas(i(t)),d=o.getDom().getBoundingClientRect();a=n(d.left,a),s=n(d.top,s),l=r(d.right,l),h=r(d.bottom,h),u.push({dom:c,left:d.left,top:d.top})}}),a*=c,s*=c,l*=c,h*=c;var f=l-a,g=h-s,p=Yh();p.width=f,p.height=g;var v=an(p);return ug(u,function(t){var e=new Ei({style:{x:t.left*c-a,y:t.top*c-s,image:t.dom}});v.add(e)}),v.refreshImmediately(),p.toDataURL("image/"+(t&&t.type||"png"))}return this.getDataURL(t)}},Ig.convertToPixel=m(ma,"convertToPixel"),Ig.convertFromPixel=m(ma,"convertFromPixel"),Ig.containPixel=function(t,e){var i,n=this._model;return t=To(n,t),d(t,function(t,n){n.indexOf("Models")>=0&&d(t,function(t){var r=t.coordinateSystem;if(r&&r.containPoint)i|=!!r.containPoint(e);else if("seriesModels"===n){var o=this._chartsMap[t.__viewId];o&&o.containPoint?i|=o.containPoint(e,t):console.warn(n+": "+(o?"The found component do not support containPoint.":"No view mapping to the found component."))}else console.warn(n+": containPoint is not supported")},this)},this),!!i},Ig.getVisual=function(t,e){var i=this._model;t=To(i,t,{defaultMainType:"series"});var n=t.seriesModel;n||console.warn("There is no specified seires model");var r=n.getData(),o=t.hasOwnProperty("dataIndexInside")?t.dataIndexInside:t.hasOwnProperty("dataIndex")?r.indexOfRawIndex(t.dataIndex):null;return null!=o?r.getItemVisual(o,e):r.getVisual(e)},Ig.getViewOfComponentModel=function(t){return this._componentsMap[t.__viewId]},Ig.getViewOfSeriesModel=function(t){return this._chartsMap[t.__viewId]};var Cg={update:function(t){var e=this._model,i=this._api,n=this._coordSysMgr,r=this._zr;if(e){e.restoreData(),n.create(this._model,this._api),Ma.call(this,e,i),Ta.call(this,e),n.update(e,i),Ca.call(this,e,t),Aa.call(this,e,t);var o=e.get("backgroundColor")||"transparent",a=r.painter;if(a.isSingleCanvas&&a.isSingleCanvas())r.configLayer(0,{clearColor:o});else{if(!Eh.canvasSupported){var s=ye(o);o=be(s,"rgb"),0===s[3]&&(o="transparent")}o.colorStops||o.image?(r.configLayer(0,{clearColor:o}),this[Sg]=!0,this._dom.style.background="transparent"):(this[Sg]&&r.configLayer(0,{clearColor:null}),this[Sg]=!1,this._dom.style.background=o)}ug(Og,function(t){t(e,i)})}},updateView:function(t){var e=this._model;e&&(e.eachSeries(function(t){t.getData().clearAllVisual()}),Ca.call(this,e,t),ba.call(this,"updateView",e,t))},updateVisual:function(t){var e=this._model;e&&(e.eachSeries(function(t){t.getData().clearAllVisual()}),Ca.call(this,e,t,!0),ba.call(this,"updateVisual",e,t))},updateLayout:function(t){var e=this._model;e&&(Ia.call(this,e,t),ba.call(this,"updateLayout",e,t))},prepareAndUpdate:function(t){var e=this._model;Sa.call(this,"component",e),Sa.call(this,"chart",e),Cg.update.call(this,t)}};Ig.resize=function(t){L(!this[bg],"`resize` should not be called during main process."),this[bg]=!0,this._zr.resize(t);var e=this._model&&this._model.resetOption("media"),i=e?"prepareAndUpdate":"update";Cg[i].call(this),this._loadingFX&&this._loadingFX.resize(),this[bg]=!1;var n=t&&t.silent;_a.call(this,n),wa.call(this,n)},Ig.showLoading=function(t,e){if(w(t)&&(e=t,t=""),t=t||"default",this.hideLoading(),!Eg[t])return void console.warn("Loading effects "+t+" not exists.");var i=Eg[t](this._api,e),n=this._zr;this._loadingFX=i,n.add(i)},Ig.hideLoading=function(){this._loadingFX&&this._zr.remove(this._loadingFX),this._loadingFX=null},Ig.makeActionFromEvent=function(t){var e=o({},t);return e.type=Lg[t.type],e},Ig.dispatchAction=function(t,e){if(w(e)||(e={silent:!!e}),kg[t.type]&&this._model){if(this[bg])return void this._pendingActions.push(t);xa.call(this,t,e.silent),e.flush?this._zr.flush(!0):e.flush!==!1&&Eh.browser.weChat&&this._throttledZrFlush(),_a.call(this,e.silent),wa.call(this,e.silent)}},Ig.on=ga("on"),Ig.off=ga("off"),Ig.one=ga("one");var Ag=["click","dblclick","mouseover","mouseout","mousemove","mousedown","mouseup","globalout","contextmenu"];Ig._initEvents=function(){ug(Ag,function(t){this._zr.on(t,function(e){var i,n=this.getModel(),r=e.target;if("globalout"===t)i={};else if(r&&null!=r.dataIndex){var a=r.dataModel||n.getSeriesByIndex(r.seriesIndex);i=a&&a.getDataParams(r.dataIndex,r.dataType)||{}}else r&&r.eventData&&(i=o({},r.eventData));i&&(i.event=e,i.type=t,this.trigger(t,i))},this)},this),ug(Lg,function(t,e){this._messageCenter.on(e,function(t){this.trigger(e,t)},this)},this)},Ig.isDisposed=function(){return this._disposed},Ig.clear=function(){this.setOption({series:[]},!0)},Ig.dispose=function(){if(this._disposed)return void console.warn("Instance "+this.id+" has been disposed");this._disposed=!0;var t=this._api,e=this._model;ug(this._componentsViews,function(i){i.dispose(e,t)}),ug(this._chartsViews,function(i){i.dispose(e,t)}),this._zr.dispose(),delete Rg[this.id]},u(va,eu);var kg={},Lg={},Dg=[],Pg=[],Og=[],zg=[],Bg={},Eg={},Rg={},Ng={},Fg=new Date-0,Hg=new Date-0,Vg="_echarts_instance_",Wg=Ea;Za(mg,sg),Va(Jf),ja("default",hg),Xa({type:"highlight",event:"highlight",update:"highlight"},B),Xa({type:"downplay",event:"downplay",update:"downplay"},B);var Gg={registerMap:function(e){t.registerMap=e},getMap:function(e){t.getMap=e},parseGeoJSON:function(e){t.parseGeoJSON=e}},Xg="__ec_stack_";as.getLayoutOnAxis=ns;var qg=ss.prototype;qg.parse=function(t){return t},qg.getSetting=function(t){return this._setting[t]},qg.contain=function(t){var e=this._extent;return t>=e[0]&&t<=e[1]},qg.normalize=function(t){var e=this._extent;return e[1]===e[0]?.5:(t-e[0])/(e[1]-e[0])},qg.scale=function(t){var e=this._extent;return t*(e[1]-e[0])+e[0]},qg.unionExtent=function(t){var e=this._extent;t[0]<e[0]&&(e[0]=t[0]),t[1]>e[1]&&(e[1]=t[1])},qg.unionExtentFromData=function(t,e){this.unionExtent(t.getDataExtent(e,!0))},qg.getExtent=function(){return this._extent.slice()},qg.setExtent=function(t,e){var i=this._extent;isNaN(t)||(i[0]=t),isNaN(e)||(i[1]=e)},qg.getTicksLabels=function(){for(var t=[],e=this.getTicks(),i=0;i<e.length;i++)t.push(this.getLabel(e[i]));return t},qg.isBlank=function(){return this._isBlank},qg.setBlank=function(t){this._isBlank=t},Pn(ss),Bn(ss,{registerWhenExtend:!0});var Yg=ss.prototype,Ug=ss.extend({type:"ordinal",init:function(t,e){this._data=t,this._extent=e||[0,t.length-1]},parse:function(t){return"string"==typeof t?l(this._data,t):Math.round(t)},contain:function(t){return t=this.parse(t),Yg.contain.call(this,t)&&null!=this._data[t]},normalize:function(t){return Yg.normalize.call(this,this.parse(t))},scale:function(t){return Math.round(Yg.scale.call(this,t))},getTicks:function(){for(var t=[],e=this._extent,i=e[0];i<=e[1];)t.push(i),i++;return t},getLabel:function(t){return this._data[t]},count:function(){return this._extent[1]-this._extent[0]+1},unionExtentFromData:function(t,e){this.unionExtent(t.getDataExtent(e,!1))},niceTicks:B,niceExtent:B});Ug.create=function(){return new Ug};var Zg=cn,jg=cn,$g=ss.extend({type:"interval",_interval:0,_intervalPrecision:2,setExtent:function(t,e){var i=this._extent;isNaN(t)||(i[0]=parseFloat(t)),isNaN(e)||(i[1]=parseFloat(e))},unionExtent:function(t){var e=this._extent;t[0]<e[0]&&(e[0]=t[0]),t[1]>e[1]&&(e[1]=t[1]),$g.prototype.setExtent.call(this,e[0],e[1])},getInterval:function(){return this._interval},setInterval:function(t){this._interval=t,this._niceExtent=this._extent.slice(),this._intervalPrecision=hs(t)},getTicks:function(){return ds(this._interval,this._extent,this._niceExtent,this._intervalPrecision)},getTicksLabels:function(){for(var t=[],e=this.getTicks(),i=0;i<e.length;i++)t.push(this.getLabel(e[i]));return t},getLabel:function(t,e){if(null==t)return"";var i=e&&e.precision;return null==i?i=dn(t)||0:"auto"===i&&(i=this._intervalPrecision),t=jg(t,i,!0),wn(t)},niceTicks:function(t,e,i){t=t||5;var n=this._extent,r=n[1]-n[0];if(isFinite(r)){0>r&&(r=-r,n.reverse());var o=ls(n,t,e,i);this._intervalPrecision=o.intervalPrecision,this._interval=o.interval,this._niceExtent=o.niceTickExtent}},niceExtent:function(t){var e=this._extent;if(e[0]===e[1])if(0!==e[0]){var i=e[0];t.fixMax?e[0]-=i/2:(e[1]+=i/2,e[0]-=i/2)}else e[1]=1;var n=e[1]-e[0];isFinite(n)||(e[0]=0,e[1]=1),this.niceTicks(t.splitNumber,t.minInterval,t.maxInterval);var r=this._interval;t.fixMin||(e[0]=jg(Math.floor(e[0]/r)*r)),t.fixMax||(e[1]=jg(Math.ceil(e[1]/r)*r))}});$g.create=function(){return new $g};var Kg=$g.prototype,Qg=Math.ceil,Jg=Math.floor,tp=1e3,ep=60*tp,ip=60*ep,np=24*ip,rp=function(t,e,i,n){for(;n>i;){var r=i+n>>>1;t[r][1]<e?i=r+1:n=r}return i},op=$g.extend({type:"time",getLabel:function(t){var e=this._stepLvl,i=new Date(t);return In(e[0],i,this.getSetting("useUTC"))},niceExtent:function(t){var e=this._extent;if(e[0]===e[1]&&(e[0]-=np,e[1]+=np),e[1]===-1/0&&1/0===e[0]){var i=new Date;e[1]=+new Date(i.getFullYear(),i.getMonth(),i.getDate()),e[0]=e[1]-np}this.niceTicks(t.splitNumber,t.minInterval,t.maxInterval);var n=this._interval;t.fixMin||(e[0]=cn(Jg(e[0]/n)*n)),t.fixMax||(e[1]=cn(Qg(e[1]/n)*n))},niceTicks:function(t,e,i){t=t||10;var n=this._extent,r=n[1]-n[0],o=r/t;null!=e&&e>o&&(o=e),null!=i&&o>i&&(o=i);var a=ap.length,s=rp(ap,o,0,a),l=ap[Math.min(s,a-1)],h=l[1];if("year"===l[0]){var u=r/h,c=_n(u/t,!0);h*=c}var d=this.getSetting("useUTC")?0:60*new Date(+n[0]||+n[1]).getTimezoneOffset()*1e3,f=[Math.round(Qg((n[0]-d)/h)*h+d),Math.round(Jg((n[1]-d)/h)*h+d)];cs(f,n),this._stepLvl=l,this._interval=h,this._niceExtent=f},parse:function(t){return+mn(t)}});d(["contain","normalize"],function(t){op.prototype[t]=function(e){return Kg[t].call(this,this.parse(e))}});var ap=[["hh:mm:ss",tp],["hh:mm:ss",5*tp],["hh:mm:ss",10*tp],["hh:mm:ss",15*tp],["hh:mm:ss",30*tp],["hh:mm\nMM-dd",ep],["hh:mm\nMM-dd",5*ep],["hh:mm\nMM-dd",10*ep],["hh:mm\nMM-dd",15*ep],["hh:mm\nMM-dd",30*ep],["hh:mm\nMM-dd",ip],["hh:mm\nMM-dd",2*ip],["hh:mm\nMM-dd",6*ip],["hh:mm\nMM-dd",12*ip],["MM-dd\nyyyy",np],["MM-dd\nyyyy",2*np],["MM-dd\nyyyy",3*np],["MM-dd\nyyyy",4*np],["MM-dd\nyyyy",5*np],["MM-dd\nyyyy",6*np],["week",7*np],["MM-dd\nyyyy",10*np],["week",14*np],["week",21*np],["month",31*np],["week",42*np],["month",62*np],["week",42*np],["quarter",380*np/4],["month",31*np*4],["month",31*np*5],["half-year",380*np/2],["month",31*np*8],["month",31*np*10],["year",380*np]];op.create=function(t){return new op({useUTC:t.ecModel.get("useUTC")})};var sp=ss.prototype,lp=$g.prototype,hp=dn,up=cn,cp=Math.floor,dp=Math.ceil,fp=Math.pow,gp=Math.log,pp=ss.extend({type:"log",base:10,$constructor:function(){ss.apply(this,arguments),this._originalScale=new $g},getTicks:function(){var t=this._originalScale,e=this._extent,i=t.getExtent();return f(lp.getTicks.call(this),function(n){var r=cn(fp(this.base,n));return r=n===e[0]&&t.__fixMin?fs(r,i[0]):r,r=n===e[1]&&t.__fixMax?fs(r,i[1]):r},this)},getLabel:lp.getLabel,scale:function(t){return t=sp.scale.call(this,t),fp(this.base,t)},setExtent:function(t,e){var i=this.base;t=gp(t)/gp(i),e=gp(e)/gp(i),lp.setExtent.call(this,t,e)},getExtent:function(){var t=this.base,e=sp.getExtent.call(this);e[0]=fp(t,e[0]),e[1]=fp(t,e[1]);var i=this._originalScale,n=i.getExtent();return i.__fixMin&&(e[0]=fs(e[0],n[0])),i.__fixMax&&(e[1]=fs(e[1],n[1])),e},unionExtent:function(t){this._originalScale.unionExtent(t);var e=this.base;t[0]=gp(t[0])/gp(e),t[1]=gp(t[1])/gp(e),sp.unionExtent.call(this,t)},unionExtentFromData:function(t,e){this.unionExtent(t.getDataExtent(e,!0,function(t){return t>0}))},niceTicks:function(t){t=t||10;var e=this._extent,i=e[1]-e[0];if(!(1/0===i||0>=i)){var n=yn(i),r=t/i*n;for(.5>=r&&(n*=10);!isNaN(n)&&Math.abs(n)<1&&Math.abs(n)>0;)n*=10;var o=[cn(dp(e[0]/n)*n),cn(cp(e[1]/n)*n)];this._interval=n,this._niceExtent=o}},niceExtent:function(t){lp.niceExtent.call(this,t);var e=this._originalScale;e.__fixMin=t.fixMin,e.__fixMax=t.fixMax}});d(["contain","normalize"],function(t){pp.prototype[t]=function(e){return e=gp(e)/gp(this.base),sp[t].call(this,e)}}),pp.create=function(){return new pp};var vp=function(t){this._axes={},this._dimList=[],this.name=t||""};vp.prototype={constructor:vp,type:"cartesian",getAxis:function(t){return this._axes[t]},getAxes:function(){return f(this._dimList,ws,this)},getAxesByScale:function(t){return t=t.toLowerCase(),p(this.getAxes(),function(e){return e.scale.type===t})},addAxis:function(t){var e=t.dim;this._axes[e]=t,this._dimList.push(e)},dataToCoord:function(t){return this._dataCoordConvert(t,"dataToCoord")},coordToData:function(t){return this._dataCoordConvert(t,"coordToData")},_dataCoordConvert:function(t,e){for(var i=this._dimList,n=t instanceof Array?[]:{},r=0;r<i.length;r++){var o=i[r],a=this._axes[o];n[o]=a[e](t[o])}return n}},bs.prototype={constructor:bs,type:"cartesian2d",dimensions:["x","y"],getBaseAxis:function(){return this.getAxesByScale("ordinal")[0]||this.getAxesByScale("time")[0]||this.getAxis("x")},containPoint:function(t){var e=this.getAxis("x"),i=this.getAxis("y");return e.contain(e.toLocalCoord(t[0]))&&i.contain(i.toLocalCoord(t[1]))},containData:function(t){return this.getAxis("x").containData(t[0])&&this.getAxis("y").containData(t[1])},dataToPoint:function(t,e){var i=this.getAxis("x"),n=this.getAxis("y");return[i.toGlobalCoord(i.dataToCoord(t[0],e)),n.toGlobalCoord(n.dataToCoord(t[1],e))]},pointToData:function(t,e){var i=this.getAxis("x"),n=this.getAxis("y");return[i.coordToData(i.toLocalCoord(t[0]),e),n.coordToData(n.toLocalCoord(t[1]),e)]},getOtherAxis:function(t){return this.getAxis("x"===t.dim?"y":"x")}},h(bs,vp);var mp=hn,yp=[0,1],xp=function(t,e,i){this.dim=t,this.scale=e,this._extent=i||[0,0],this.inverse=!1,this.onBand=!1,this._labelInterval};xp.prototype={constructor:xp,contain:function(t){var e=this._extent,i=Math.min(e[0],e[1]),n=Math.max(e[0],e[1]);return t>=i&&n>=t},containData:function(t){return this.contain(this.dataToCoord(t))},getExtent:function(){return this._extent.slice()},getPixelPrecision:function(t){return fn(t||this.scale.getExtent(),this._extent)},setExtent:function(t,e){var i=this._extent;i[0]=t,i[1]=e},dataToCoord:function(t,e){var i=this._extent,n=this.scale;return t=n.normalize(t),this.onBand&&"ordinal"===n.type&&(i=i.slice(),Ss(i,n.count())),mp(t,yp,i,e)},coordToData:function(t,e){var i=this._extent,n=this.scale;this.onBand&&"ordinal"===n.type&&(i=i.slice(),Ss(i,n.count()));var r=mp(t,i,yp,e);return this.scale.scale(r)},pointToData:function(){},getTicksCoords:function(t){if(this.onBand&&!t){for(var e=this.getBands(),i=[],n=0;n<e.length;n++)i.push(e[n][0]);return e[n-1]&&i.push(e[n-1][1]),i}return f(this.scale.getTicks(),this.dataToCoord,this)},getLabelsCoords:function(){return f(this.scale.getTicks(),this.dataToCoord,this)},getBands:function(){for(var t=this.getExtent(),e=[],i=this.scale.count(),n=t[0],r=t[1],o=r-n,a=0;i>a;a++)e.push([o*a/i+n,o*(a+1)/i+n]);return e},getBandWidth:function(){var t=this._extent,e=this.scale.getExtent(),i=e[1]-e[0]+(this.onBand?1:0);0===i&&(i=1);var n=Math.abs(t[1]-t[0]);return Math.abs(n)/i},isHorizontal:null,getRotate:null,getLabelInterval:function(){var t=this._labelInterval;if(!t){var e=this.model,i=e.getModel("axisLabel");t=i.get("interval"),"category"!==this.type||null!=t&&"auto"!==t||(t=ys(f(this.scale.getTicks(),this.dataToCoord,this),e.getFormattedLabels(),i.getFont(),this.getRotate?this.getRotate():this.isHorizontal&&!this.isHorizontal()?90:0,i.get("rotate"))),this._labelInterval=t}return t}};var _p=function(t,e,i,n,r){xp.call(this,t,e,i),this.type=n||"value",this.position=r||"bottom"};_p.prototype={constructor:_p,index:0,onZero:!1,model:null,isHorizontal:function(){var t=this.position;return"top"===t||"bottom"===t},getGlobalExtent:function(t){var e=this.getExtent();return e[0]=this.toGlobalCoord(e[0]),e[1]=this.toGlobalCoord(e[1]),t&&e[0]>e[1]&&e.reverse(),e},getOtherAxis:function(){this.grid.getOtherAxis()},isLabelIgnored:function(t){if("category"===this.type){var e=this.getLabelInterval();return"function"==typeof e&&!e(t,this.scale.getLabel(t))||t%(e+1)}},pointToData:function(t,e){return this.coordToData(this.toLocalCoord(t["x"===this.dim?0:1]),e)},toLocalCoord:null,toGlobalCoord:null},h(_p,xp);var wp={show:!0,zlevel:0,z:0,inverse:!1,name:"",nameLocation:"end",nameRotate:null,nameTruncate:{maxWidth:null,ellipsis:"...",placeholder:"."},nameTextStyle:{},nameGap:15,silent:!1,triggerEvent:!1,tooltip:{show:!1},axisPointer:{},axisLine:{show:!0,onZero:!0,onZeroAxisIndex:null,lineStyle:{color:"#333",width:1,type:"solid"},symbol:["none","none"],symbolSize:[10,15]},axisTick:{show:!0,inside:!1,length:5,lineStyle:{width:1}},axisLabel:{show:!0,inside:!1,rotate:0,showMinLabel:null,showMaxLabel:null,margin:8,fontSize:12},splitLine:{show:!0,lineStyle:{color:["#ccc"],width:1,type:"solid"}},splitArea:{show:!1,areaStyle:{color:["rgba(250,250,250,0.3)","rgba(200,200,200,0.3)"]}}},bp={};bp.categoryAxis=n({boundaryGap:!0,splitLine:{show:!1},axisTick:{alignWithLabel:!1,interval:"auto"},axisLabel:{interval:"auto"}},wp),bp.valueAxis=n({boundaryGap:[0,0],splitNumber:5},wp),bp.timeAxis=a({scale:!0,min:"dataMin",max:"dataMax"},bp.valueAxis),bp.logAxis=a({scale:!0,logBase:10},bp.valueAxis);var Sp=["value","category","time","log"],Mp=function(t,e,i,o){d(Sp,function(a){e.extend({type:t+"Axis."+a,mergeDefaultAndTheme:function(e,r){var o=this.layoutMode,s=o?Eo(e):{},l=r.getTheme();n(e,l.get(a+"Axis")),n(e,this.getDefaultOption()),e.type=i(t,e),o&&Bo(e,s,o)},defaultOption:r([{},bp[a+"Axis"],o],!0)})}),Cf.registerSubTypeDefaulter(t+"Axis",m(i,t))},Tp={getFormattedLabels:function(){return xs(this.axis,this.get("axisLabel.formatter"))},getCategories:function(){return"category"===this.get("type")&&f(this.get("data"),Ms)},getMin:function(t){var e=this.option,i=t||null==e.rangeStart?e.min:e.rangeStart;return this.axis&&null!=i&&"dataMin"!==i&&"function"!=typeof i&&!M(i)&&(i=this.axis.scale.parse(i)),i},getMax:function(t){var e=this.option,i=t||null==e.rangeEnd?e.max:e.rangeEnd;return this.axis&&null!=i&&"dataMax"!==i&&"function"!=typeof i&&!M(i)&&(i=this.axis.scale.parse(i)),i},getNeedCrossZero:function(){var t=this.option;return null!=t.rangeStart||null!=t.rangeEnd?!1:!t.scale},getCoordSysModel:B,setRange:function(t,e){this.option.rangeStart=t,this.option.rangeEnd=e},resetRange:function(){this.option.rangeStart=this.option.rangeEnd=null}},Ip=Cf.extend({type:"cartesian2dAxis",axis:null,init:function(){Ip.superApply(this,"init",arguments),this.resetRange()},mergeOption:function(){Ip.superApply(this,"mergeOption",arguments),this.resetRange()},restoreData:function(){Ip.superApply(this,"restoreData",arguments),this.resetRange()},getCoordSysModel:function(){return this.ecModel.queryComponents({mainType:"grid",index:this.option.gridIndex,id:this.option.gridId})[0]}});n(Ip.prototype,Tp);var Cp={offset:0};Mp("x",Ip,Ts,Cp),Mp("y",Ip,Ts,Cp),Cf.extend({type:"grid",dependencies:["xAxis","yAxis"],layoutMode:"box",coordinateSystem:null,defaultOption:{show:!1,zlevel:0,z:0,left:"10%",top:60,right:"10%",bottom:60,containLabel:!1,backgroundColor:"rgba(0,0,0,0)",borderWidth:1,borderColor:"#ccc"}});var Ap=d,kp=ms,Lp=ps,Dp=ks.prototype;Dp.type="grid",Dp.axisPointerEnabled=!0,Dp.getRect=function(){return this._rect},Dp.update=function(t,e){var i=this._axesMap;this._updateScale(t,this.model),Ap(i.x,function(t){Lp(t.scale,t.model)}),Ap(i.y,function(t){Lp(t.scale,t.model)}),Ap(i.x,function(t){Ls(i,"y",t)}),Ap(i.y,function(t){Ls(i,"x",t)}),this.resize(this.model,e)},Dp.resize=function(t,e,i){function n(){Ap(o,function(t){var e=t.isHorizontal(),i=e?[0,r.width]:[0,r.height],n=t.inverse?1:0;t.setExtent(i[n],i[1-n]),Ps(t,e?r.x:r.y)})}var r=zo(t.getBoxLayoutParams(),{width:e.getWidth(),height:e.getHeight()});this._rect=r;var o=this._axesList;n(),!i&&t.get("containLabel")&&(Ap(o,function(t){if(!t.model.get("axisLabel.inside")){var e=As(t);if(e){var i=t.isHorizontal()?"height":"width",n=t.model.get("axisLabel.margin");r[i]-=e[i]+n,"top"===t.position?r.y+=e.height+n:"left"===t.position&&(r.x+=e.width+n)}}}),n())},Dp.getAxis=function(t,e){var i=this._axesMap[t];if(null!=i){if(null==e)for(var n in i)if(i.hasOwnProperty(n))return i[n];return i[e]}},Dp.getAxes=function(){return this._axesList.slice()},Dp.getCartesian=function(t,e){if(null!=t&&null!=e){var i="x"+t+"y"+e;return this._coordsMap[i]}w(t)&&(e=t.yAxisIndex,t=t.xAxisIndex);for(var n=0,r=this._coordsList;n<r.length;n++)if(r[n].getAxis("x").index===t||r[n].getAxis("y").index===e)return r[n]},Dp.getCartesians=function(){return this._coordsList.slice()},Dp.convertToPixel=function(t,e,i){var n=this._findConvertTarget(t,e);return n.cartesian?n.cartesian.dataToPoint(i):n.axis?n.axis.toGlobalCoord(n.axis.dataToCoord(i)):null},Dp.convertFromPixel=function(t,e,i){var n=this._findConvertTarget(t,e);return n.cartesian?n.cartesian.pointToData(i):n.axis?n.axis.coordToData(n.axis.toLocalCoord(i)):null},Dp._findConvertTarget=function(t,e){var i,n,r=e.seriesModel,o=e.xAxisModel||r&&r.getReferringComponents("xAxis")[0],a=e.yAxisModel||r&&r.getReferringComponents("yAxis")[0],s=e.gridModel,h=this._coordsList;if(r)i=r.coordinateSystem,l(h,i)<0&&(i=null);else if(o&&a)i=this.getCartesian(o.componentIndex,a.componentIndex);else if(o)n=this.getAxis("x",o.componentIndex);else if(a)n=this.getAxis("y",a.componentIndex);else if(s){var u=s.coordinateSystem;u===this&&(i=this._coordsList[0])}return{cartesian:i,axis:n}},Dp.containPoint=function(t){var e=this._coordsList[0];return e?e.containPoint(t):void 0},Dp._initCartesian=function(t,e){function i(i){return function(a,s){if(Is(a,t,e)){var l=a.get("position");"x"===i?"top"!==l&&"bottom"!==l&&(l="bottom",n[l]&&(l="top"===l?"bottom":"top")):"left"!==l&&"right"!==l&&(l="left",n[l]&&(l="left"===l?"right":"left")),n[l]=!0;var h=new _p(i,vs(a),[0,0],a.get("type"),l),u="category"===h.type;h.onBand=u&&a.get("boundaryGap"),h.inverse=a.get("inverse"),h.onZero=a.get("axisLine.onZero"),h.onZeroAxisIndex=a.get("axisLine.onZeroAxisIndex"),a.axis=h,h.model=a,h.grid=this,h.index=s,this._axesList.push(h),r[i][s]=h,o[i]++}}}var n={left:!1,right:!1,top:!1,bottom:!1},r={x:{},y:{}},o={x:0,y:0};return e.eachComponent("xAxis",i("x"),this),e.eachComponent("yAxis",i("y"),this),o.x&&o.y?(this._axesMap=r,void Ap(r.x,function(e,i){Ap(r.y,function(n,r){var o="x"+i+"y"+r,a=new bs(o);a.grid=this,a.model=t,this._coordsMap[o]=a,this._coordsList.push(a),a.addAxis(e),a.addAxis(n)},this)},this)):(this._axesMap={},void(this._axesList=[]))},Dp._updateScale=function(t,e){function i(t,e,i){Ap(i.coordDimToDataDim(e.dim),function(i){e.scale.unionExtentFromData(t,i)})}d(this._axesList,function(t){t.scale.setExtent(1/0,-1/0)}),t.eachSeries(function(n){if(zs(n)){var r=Os(n,t),o=r[0],a=r[1];if(!Is(o,e,t)||!Is(a,e,t))return;var s=this.getCartesian(o.componentIndex,a.componentIndex),l=n.getData(),h=s.getAxis("x"),u=s.getAxis("y");"list"===l.type&&(i(l,h,n),i(l,u,n))}},this)},Dp.getTooltipAxes=function(t){var e=[],i=[];return Ap(this.getCartesians(),function(n){var r=null!=t&&"auto"!==t?n.getAxis(t):n.getBaseAxis(),o=n.getOtherAxis(r);l(e,r)<0&&e.push(r),l(i,o)<0&&i.push(o)}),{baseAxes:e,otherAxes:i}};var Pp=["xAxis","yAxis"];ks.create=function(t,e){var i=[];return t.eachComponent("grid",function(n,r){var o=new ks(n,t,e);o.name="grid_"+r,o.resize(n,e,!0),n.coordinateSystem=o,i.push(o)}),t.eachSeries(function(e){if(zs(e)){var i=Os(e,t),n=i[0],r=i[1],o=n.getCoordSysModel();if(!o)throw new Error('Grid "'+T(n.get("gridIndex"),n.get("gridId"),0)+'" not found');if(n.getCoordSysModel()!==r.getCoordSysModel())throw new Error("xAxis and yAxis must use the same grid");var a=o.coordinateSystem;e.coordinateSystem=a.getCartesian(n.componentIndex,r.componentIndex)}}),i},ks.dimensions=ks.prototype.dimensions=bs.prototype.dimensions,Uo.register("cartesian2d",ks),Es.prototype={constructor:Es,add:function(t){return this._add=t,this},update:function(t){return this._update=t,this},remove:function(t){return this._remove=t,this},execute:function(){var t,e=this._old,i=this._new,n={},r={},o=[],a=[];for(Rs(e,n,o,"_oldKeyGetter",this),Rs(i,r,a,"_newKeyGetter",this),t=0;t<e.length;t++){var s=o[t],l=r[s];if(null!=l){var h=l.length;h?(1===h&&(r[s]=null),l=l.unshift()):r[s]=null,this._update&&this._update(l,t)}else this._remove&&this._remove(t)}for(var t=0;t<a.length;t++){var s=a[t];if(r.hasOwnProperty(s)){var l=r[s];if(null==l)continue;if(l.length)for(var u=0,h=l.length;h>u;u++)this._add&&this._add(l[u]);else this._add&&this._add(l)}}}};var Op=w,zp="undefined",Bp=typeof window===zp?global:window,Ep={"float":typeof Bp.Float64Array===zp?Array:Bp.Float64Array,"int":typeof Bp.Int32Array===zp?Array:Bp.Int32Array,ordinal:Array,number:Array,time:Array},Rp=["stackedOn","hasItemOption","_nameList","_idList","_rawData"];Fs.prototype.pure=!1,Fs.prototype.count=function(){return this._array.length},Fs.prototype.getItem=function(t){return this._array[t]};var Np=function(t,e){t=t||["x","y"];for(var i={},n=[],r=0;r<t.length;r++){var o,a={};"string"==typeof t[r]?(o=t[r],a={name:o,coordDim:o,coordDimIndex:0,stackable:!1,type:"number"}):(a=t[r],o=a.name,a.type=a.type||"number",a.coordDim||(a.coordDim=o,a.coordDimIndex=0)),a.otherDims=a.otherDims||{},n.push(o),i[o]=a}this.dimensions=n,this._dimensionInfos=i,this.hostModel=e,this.dataType,this.indices=[],this._storage={},this._nameList=[],this._idList=[],this._optionModels=[],this.stackedOn=null,this._visual={},this._layout={},this._itemVisuals=[],this._itemLayouts=[],this._graphicEls=[],this._rawData,this._extent},Fp=Np.prototype;Fp.type="list",Fp.hasItemOption=!0,Fp.getDimension=function(t){return isNaN(t)||(t=this.dimensions[t]||t),t},Fp.getDimensionInfo=function(t){return i(this._dimensionInfos[this.getDimension(t)])},Fp.initData=function(t,e,i){t=t||[];var n=y(t);if(n&&(t=new Fs(t)),!n&&("function"!=typeof t.getItem||"function"!=typeof t.count))throw new Error("Inavlid data provider.");this._rawData=t;var r,o=this._storage={},a=this.indices=[],s=this.dimensions,l=this._dimensionInfos,h=t.count(),u=[],c={};e=e||[];for(var d=0;d<s.length;d++){var f=l[s[d]];0===f.otherDims.itemName&&(r=d);var g=Ep[f.type];o[s[d]]=new g(h)}var p=this;i||(p.hasItemOption=!1),i=i||function(t,e,i,n){var r=yo(t);return xo(t)&&(p.hasItemOption=!0),_o(r instanceof Array?r[n]:r,l[e])};for(var d=0;h>d;d++){for(var v=t.getItem(d),m=0;m<s.length;m++){var x=s[m],_=o[x];_[d]=i(v,x,d,m)}a.push(d)}for(var d=0;h>d;d++){var v=t.getItem(d);!e[d]&&v&&(null!=v.name?e[d]=v.name:null!=r&&(e[d]=o[s[r]][d]));var w=e[d]||"",b=v&&v.id;!b&&w&&(c[w]=c[w]||0,b=w,c[w]>0&&(b+="__ec__"+c[w]),c[w]++),b&&(u[d]=b)}this._nameList=e,this._idList=u},Fp.count=function(){return this.indices.length},Fp.get=function(t,e,i){var n=this._storage,r=this.indices[e];if(null==r||!n[t])return 0/0;var o=n[t][r];if(i){var a=this._dimensionInfos[t];if(a&&a.stackable)for(var s=this.stackedOn;s;){var l=s.get(t,e);(o>=0&&l>0||0>=o&&0>l)&&(o+=l),s=s.stackedOn}}return o},Fp.getValues=function(t,e,i){var n=[];y(t)||(i=e,e=t,t=this.dimensions);for(var r=0,o=t.length;o>r;r++)n.push(this.get(t[r],e,i));return n},Fp.hasValue=function(t){for(var e=this.dimensions,i=this._dimensionInfos,n=0,r=e.length;r>n;n++)if("ordinal"!==i[e[n]].type&&isNaN(this.get(e[n],t)))return!1;return!0},Fp.getDataExtent=function(t,e,i){t=this.getDimension(t);var n=this._storage[t],r=this.getDimensionInfo(t);e=r&&r.stackable&&e;var o,a=(this._extent||(this._extent={}))[t+!!e];if(a)return a;if(n){for(var s=1/0,l=-1/0,h=0,u=this.count();u>h;h++)o=this.get(t,h,e),(!i||i(o,t,h))&&(s>o&&(s=o),o>l&&(l=o));return this._extent[t+!!e]=[s,l]}return[1/0,-1/0]},Fp.getSum=function(t,e){var i=this._storage[t],n=0;if(i)for(var r=0,o=this.count();o>r;r++){var a=this.get(t,r,e);isNaN(a)||(n+=a)}return n},Fp.indexOf=function(t,e){var i=this._storage,n=i[t],r=this.indices;if(n)for(var o=0,a=r.length;a>o;o++){var s=r[o];if(n[s]===e)return o}return-1},Fp.indexOfName=function(t){for(var e=this.indices,i=this._nameList,n=0,r=e.length;r>n;n++){var o=e[n];if(i[o]===t)return n}return-1},Fp.indexOfRawIndex=function(t){var e=this.indices,i=e[t];if(null!=i&&i===t)return t;for(var n=0,r=e.length-1;r>=n;){var o=(n+r)/2|0;if(e[o]<t)n=o+1;else{if(!(e[o]>t))return o;r=o-1}}return-1},Fp.indicesOfNearest=function(t,e,i,n){var r=this._storage,o=r[t],a=[];if(!o)return a;null==n&&(n=1/0);for(var s=Number.MAX_VALUE,l=-1,h=0,u=this.count();u>h;h++){var c=e-this.get(t,h,i),d=Math.abs(c);n>=c&&s>=d&&((s>d||c>=0&&0>l)&&(s=d,l=c,a.length=0),a.push(h))}return a},Fp.getRawIndex=function(t){var e=this.indices[t];return null==e?-1:e},Fp.getRawDataItem=function(t){return this._rawData.getItem(this.getRawIndex(t))},Fp.getName=function(t){return this._nameList[this.indices[t]]||""},Fp.getId=function(t){return this._idList[this.indices[t]]||this.getRawIndex(t)+""},Fp.each=function(t,e,i,n){"function"==typeof t&&(n=i,i=e,e=t,t=[]),t=f(Hs(t),this.getDimension,this);var r=[],o=t.length,a=this.indices;n=n||this;for(var s=0;s<a.length;s++)switch(o){case 0:e.call(n,s);break;case 1:e.call(n,this.get(t[0],s,i),s);break;case 2:e.call(n,this.get(t[0],s,i),this.get(t[1],s,i),s);break;default:for(var l=0;o>l;l++)r[l]=this.get(t[l],s,i);r[l]=s,e.apply(n,r)}},Fp.filterSelf=function(t,e,i,n){"function"==typeof t&&(n=i,i=e,e=t,t=[]),t=f(Hs(t),this.getDimension,this);var r=[],o=[],a=t.length,s=this.indices;n=n||this;for(var l=0;l<s.length;l++){var h;if(a)if(1===a)h=e.call(n,this.get(t[0],l,i),l);else{for(var u=0;a>u;u++)o[u]=this.get(t[u],l,i);o[u]=l,h=e.apply(n,o)}else h=e.call(n,l);h&&r.push(s[l])}return this.indices=r,this._extent={},this},Fp.mapArray=function(t,e,i,n){"function"==typeof t&&(n=i,i=e,e=t,t=[]);var r=[];return this.each(t,function(){r.push(e&&e.apply(this,arguments))},i,n),r},Fp.map=function(t,e,i,n){t=f(Hs(t),this.getDimension,this);var r=Vs(this,t),o=r.indices=this.indices,a=r._storage,s=[];return this.each(t,function(){var i=arguments[arguments.length-1],n=e&&e.apply(this,arguments);if(null!=n){"number"==typeof n&&(s[0]=n,n=s);for(var r=0;r<n.length;r++){var l=t[r],h=a[l],u=o[i];h&&(h[u]=n[r])}}},i,n),r},Fp.downSample=function(t,e,i,n){for(var r=Vs(this,[t]),o=this._storage,a=r._storage,s=this.indices,l=r.indices=[],h=[],u=[],c=Math.floor(1/e),d=a[t],f=this.count(),g=0;g<o[t].length;g++)a[t][g]=o[t][g];for(var g=0;f>g;g+=c){c>f-g&&(c=f-g,h.length=c);for(var p=0;c>p;p++){var v=s[g+p];h[p]=d[v],u[p]=v}var m=i(h),v=u[n(h,m)||0];d[v]=m,l.push(v)}return r},Fp.getItemModel=function(t){var e=this.hostModel;return t=this.indices[t],new fo(this._rawData.getItem(t),e,e&&e.ecModel)},Fp.diff=function(t){var e,i=this._idList,n=t&&t._idList,r="e\x00\x00";return new Es(t?t.indices:[],this.indices,function(t){return null!=(e=n[t])?e:r+t},function(t){return null!=(e=i[t])?e:r+t})},Fp.getVisual=function(t){var e=this._visual;return e&&e[t]},Fp.setVisual=function(t,e){if(Op(t))for(var i in t)t.hasOwnProperty(i)&&this.setVisual(i,t[i]);else this._visual=this._visual||{},this._visual[t]=e},Fp.setLayout=function(t,e){if(Op(t))for(var i in t)t.hasOwnProperty(i)&&this.setLayout(i,t[i]);else this._layout[t]=e},Fp.getLayout=function(t){return this._layout[t]},Fp.getItemLayout=function(t){return this._itemLayouts[t]},Fp.setItemLayout=function(t,e,i){this._itemLayouts[t]=i?o(this._itemLayouts[t]||{},e):e},Fp.clearItemLayouts=function(){this._itemLayouts.length=0},Fp.getItemVisual=function(t,e,i){var n=this._itemVisuals[t],r=n&&n[e];return null!=r||i?r:this.getVisual(e)},Fp.setItemVisual=function(t,e,i){var n=this._itemVisuals[t]||{};if(this._itemVisuals[t]=n,Op(e))for(var r in e)e.hasOwnProperty(r)&&(n[r]=e[r]);else n[e]=i},Fp.clearAllVisual=function(){this._visual={},this._itemVisuals=[]};var Hp=function(t){t.seriesIndex=this.seriesIndex,t.dataIndex=this.dataIndex,t.dataType=this.dataType};Fp.setItemGraphicEl=function(t,e){var i=this.hostModel;e&&(e.dataIndex=t,e.dataType=this.dataType,e.seriesIndex=i&&i.seriesIndex,"group"===e.type&&e.traverse(Hp,e)),this._graphicEls[t]=e},Fp.getItemGraphicEl=function(t){return this._graphicEls[t]},Fp.eachItemGraphicEl=function(t,e){d(this._graphicEls,function(i,n){i&&t&&t.call(e,i,n)})},Fp.cloneShallow=function(){var t=f(this.dimensions,this.getDimensionInfo,this),e=new Np(t,this.hostModel);return e._storage=this._storage,Ns(e,this),e.indices=this.indices.slice(),this._extent&&(e._extent=o({},this._extent)),e
},Fp.wrapMethod=function(t,e){var i=this[t];"function"==typeof i&&(this.__wrappedMethods=this.__wrappedMethods||[],this.__wrappedMethods.push(t),this[t]=function(){var t=i.apply(this,arguments);return e.apply(this,[t].concat(A(arguments)))})},Fp.TRANSFERABLE_METHODS=["cloneShallow","downSample","map"],Fp.CHANGABLE_METHODS=["filterSelf"];var Vp=d,Wp=_,Gp=a,Xp={tooltip:1,label:1,itemName:1},qp=Ws.guessOrdinal=function(t,e){for(var i=0,n=t.length;n>i;i++){var r=Gs(t[i]);if(!y(r))return!1;var r=r[e];if(null!=r&&isFinite(r)&&""!==r)return!1;if(Wp(r)&&"-"!==r)return!0}return!1},Yp={cartesian2d:function(t,e,i,n){var r=f(["xAxis","yAxis"],function(t){return i.queryComponents({mainType:t,index:e.get(t+"Index"),id:e.get(t+"Id")})[0]}),o=r[0],a=r[1];if(!o)throw new Error('xAxis "'+T(e.get("xAxisIndex"),e.get("xAxisId"),0)+'" not found');if(!a)throw new Error('yAxis "'+T(e.get("xAxisIndex"),e.get("yAxisId"),0)+'" not found');var s=o.get("type"),l=a.get("type"),h=[{name:"x",type:Zs(s),stackable:Us(s)},{name:"y",type:Zs(l),stackable:Us(l)}],u="category"===s,c="category"===l;h=Ws(h,t,n);var d={};return u&&(d.x=o),c&&(d.y=a),{dimensions:h,categoryIndex:u?0:c?1:-1,categoryAxesModels:d}},singleAxis:function(t,e,i,n){var r=i.queryComponents({mainType:"singleAxis",index:e.get("singleAxisIndex"),id:e.get("singleAxisId")})[0];if(!r)throw new Error("singleAxis should be specified.");var o=r.get("type"),a="category"===o,s=[{name:"single",type:Zs(o),stackable:Us(o)}];s=Ws(s,t,n);var l={};return a&&(l.single=r),{dimensions:s,categoryIndex:a?0:-1,categoryAxesModels:l}},polar:function(t,e,i,n){var r=i.queryComponents({mainType:"polar",index:e.get("polarIndex"),id:e.get("polarId")})[0],o=r.findAxisModel("angleAxis"),a=r.findAxisModel("radiusAxis");if(!o)throw new Error("angleAxis option not found");if(!a)throw new Error("radiusAxis option not found");var s=a.get("type"),l=o.get("type"),h=[{name:"radius",type:Zs(s),stackable:Us(s)},{name:"angle",type:Zs(l),stackable:Us(l)}],u="category"===l,c="category"===s;h=Ws(h,t,n);var d={};return c&&(d.radius=a),u&&(d.angle=o),{dimensions:h,categoryIndex:u?1:c?0:-1,categoryAxesModels:d}},geo:function(t,e,i,n){return{dimensions:Ws([{name:"lng"},{name:"lat"}],t,n)}}},Up=tg.extend({type:"series.__base_bar__",getInitialData:function(t,e){return Ys(t.data,this,e)},getMarkerPosition:function(t){var e=this.coordinateSystem;if(e){var i=e.dataToPoint(t,!0),n=this.getData(),r=n.getLayout("offset"),o=n.getLayout("size"),a=e.getBaseAxis().isHorizontal()?0:1;return i[a]+=r+o/2,i}return[0/0,0/0]},defaultOption:{zlevel:0,z:2,coordinateSystem:"cartesian2d",legendHoverLink:!0,barMinHeight:0,barMinAngle:0,itemStyle:{}}});Up.extend({type:"series.bar",dependencies:["grid","polar"],brushSelector:"rect"});var Zp=Oc([["fill","color"],["stroke","borderColor"],["lineWidth","borderWidth"],["stroke","barBorderColor"],["lineWidth","barBorderWidth"],["opacity"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"]]),jp={getBarItemStyle:function(t){var e=Zp(this,t);if(this.getBorderLineDash){var i=this.getBorderLineDash();i&&(e.lineDash=i)}return e}},$p=["itemStyle","normal","barBorderWidth"];o(fo.prototype,jp),Ja({type:"bar",render:function(t,e,i){var n=t.get("coordinateSystem");return"cartesian2d"===n||"polar"===n?this._render(t,e,i):console.warn("Only cartesian2d and polar supported for bar."),this.group},dispose:B,_render:function(t){var e,i=this.group,n=t.getData(),r=this._data,o=t.coordinateSystem,a=o.getBaseAxis();"cartesian2d"===o.type?e=a.isHorizontal():"polar"===o.type&&(e="angle"===a.dim);var s=t.isAnimationEnabled()?t:null;n.diff(r).add(function(r){if(n.hasValue(r)){var a=n.getItemModel(r),l=Qp[o.type](n,r,a),h=Kp[o.type](n,r,a,l,e,s);n.setItemGraphicEl(r,h),i.add(h),tl(h,n,r,a,l,t,e,"polar"===o.type)}}).update(function(a,l){var h=r.getItemGraphicEl(l);if(!n.hasValue(a))return void i.remove(h);var u=n.getItemModel(a),c=Qp[o.type](n,a,u);h?no(h,{shape:c},s,a):h=Kp[o.type](n,a,u,c,e,s,!0),n.setItemGraphicEl(a,h),i.add(h),tl(h,n,a,u,c,t,e,"polar"===o.type)}).remove(function(t){var e=r.getItemGraphicEl(t);"cartesian2d"===o.type?e&&Qs(t,s,e):e&&Js(t,s,e)}).execute(),this._data=n},remove:function(t){var e=this.group,i=this._data;t.get("animation")?i&&i.eachItemGraphicEl(function(e){"sector"===e.type?Js(e.dataIndex,t,e):Qs(e.dataIndex,t,e)}):e.removeAll()}});var Kp={cartesian2d:function(t,e,i,n,r,a,s){var l=new Ud({shape:o({},n)});if(a){var h=l.shape,u=r?"height":"width",c={};h[u]=0,c[u]=n[u],lf[s?"updateProps":"initProps"](l,{shape:c},a,e)}return l},polar:function(t,e,i,n,r,a,s){var l=new Vd({shape:o({},n)});if(a){var h=l.shape,u=r?"r":"endAngle",c={};h[u]=r?0:n.startAngle,c[u]=n[u],lf[s?"updateProps":"initProps"](l,{shape:c},a,e)}return l}},Qp={cartesian2d:function(t,e,i){var n=t.getItemLayout(e),r=el(i,n),o=n.width>0?1:-1,a=n.height>0?1:-1;return{x:n.x+o*r/2,y:n.y+a*r/2,width:n.width-o*r,height:n.height-a*r}},polar:function(t,e){var i=t.getItemLayout(e);return{cx:i.cx,cy:i.cy,r0:i.r0,r:i.r,startAngle:i.startAngle,endAngle:i.endAngle}}},Jp=Mr({type:"triangle",shape:{cx:0,cy:0,width:0,height:0},buildPath:function(t,e){var i=e.cx,n=e.cy,r=e.width/2,o=e.height/2;t.moveTo(i,n-o),t.lineTo(i+r,n+o),t.lineTo(i-r,n+o),t.closePath()}}),tv=Mr({type:"diamond",shape:{cx:0,cy:0,width:0,height:0},buildPath:function(t,e){var i=e.cx,n=e.cy,r=e.width/2,o=e.height/2;t.moveTo(i,n-o),t.lineTo(i+r,n),t.lineTo(i,n+o),t.lineTo(i-r,n),t.closePath()}}),ev=Mr({type:"pin",shape:{x:0,y:0,width:0,height:0},buildPath:function(t,e){var i=e.x,n=e.y,r=e.width/5*3,o=Math.max(r,e.height),a=r/2,s=a*a/(o-a),l=n-o+a+s,h=Math.asin(s/a),u=Math.cos(h)*a,c=Math.sin(h),d=Math.cos(h),f=.6*a,g=.7*a;t.moveTo(i-u,l+s),t.arc(i,l,a,Math.PI-h,2*Math.PI+h),t.bezierCurveTo(i+u-c*f,l+s+d*f,i,n-g,i,n),t.bezierCurveTo(i,n-g,i-u+c*f,l+s+d*f,i-u,l+s),t.closePath()}}),iv=Mr({type:"arrow",shape:{x:0,y:0,width:0,height:0},buildPath:function(t,e){var i=e.height,n=e.width,r=e.x,o=e.y,a=n/3*2;t.moveTo(r,o),t.lineTo(r+a,o+i),t.lineTo(r,o+i/4*3),t.lineTo(r-a,o+i),t.lineTo(r,o),t.closePath()}}),nv={line:Zd,rect:Ud,roundRect:Ud,square:Ud,circle:Nd,diamond:tv,pin:ev,arrow:iv,triangle:Jp},rv={line:function(t,e,i,n,r){r.x1=t,r.y1=e+n/2,r.x2=t+i,r.y2=e+n/2},rect:function(t,e,i,n,r){r.x=t,r.y=e,r.width=i,r.height=n},roundRect:function(t,e,i,n,r){r.x=t,r.y=e,r.width=i,r.height=n,r.r=Math.min(i,n)/4},square:function(t,e,i,n,r){var o=Math.min(i,n);r.x=t,r.y=e,r.width=o,r.height=o},circle:function(t,e,i,n,r){r.cx=t+i/2,r.cy=e+n/2,r.r=Math.min(i,n)/2},diamond:function(t,e,i,n,r){r.cx=t+i/2,r.cy=e+n/2,r.width=i,r.height=n},pin:function(t,e,i,n,r){r.x=t+i/2,r.y=e+n/2,r.width=i,r.height=n},arrow:function(t,e,i,n,r){r.x=t+i/2,r.y=e+n/2,r.width=i,r.height=n},triangle:function(t,e,i,n,r){r.cx=t+i/2,r.cy=e+n/2,r.width=i,r.height=n}},ov={};d(nv,function(t,e){ov[e]=new t});var av=Mr({type:"symbol",shape:{symbolType:"",x:0,y:0,width:0,height:0},beforeBrush:function(){var t=this.style,e=this.shape;"pin"===e.symbolType&&"inside"===t.textPosition&&(t.textPosition=["50%","40%"],t.textAlign="center",t.textVerticalAlign="middle")},buildPath:function(t,e,i){var n=e.symbolType,r=ov[n];"none"!==e.symbolType&&(r||(n="rect",r=ov[n]),rv[n](e.x,e.y,e.width,e.height,r.shape),r.buildPath(t,r.shape,i))}}),sv=Math.PI,lv=function(t,e){this.opt=e,this.axisModel=t,a(e,{labelOffset:0,nameDirection:1,tickDirection:1,labelDirection:1,silent:!0}),this.group=new Pu;var i=new Pu({position:e.position.slice(),rotation:e.rotation});i.updateTransform(),this._transform=i.transform,this._dumbGroup=i};lv.prototype={constructor:lv,hasBuilder:function(t){return!!hv[t]},add:function(t){hv[t].call(this)},getGroup:function(){return this.group}};var hv={axisLine:function(){var t=this.opt,e=this.axisModel;if(e.get("axisLine.show")){var i=this.axisModel.axis.getExtent(),n=this._transform,r=[i[0],0],a=[i[1],0];n&&(Y(r,r,n),Y(a,a,n));var s=o({lineCap:"round"},e.getModel("axisLine.lineStyle").getLineStyle());this.group.add(new Zd(Lr({anid:"line",shape:{x1:r[0],y1:r[1],x2:a[0],y2:a[1]},style:s,strokeContainThreshold:t.strokeContainThreshold||5,silent:!0,z2:1})));var l=e.get("axisLine.symbol"),h=e.get("axisLine.symbolSize");if(null!=l){"string"==typeof l&&(l=[l,l]),("string"==typeof h||"number"==typeof h)&&(h=[h,h]);var u=h[0],c=h[1];d([[t.rotation+Math.PI/2,r],[t.rotation-Math.PI/2,a]],function(t,e){if("none"!==l[e]&&null!=l[e]){var i=nl(l[e],-u/2,-c/2,u,c,s.stroke,!0);i.attr({rotation:t[0],position:t[1],silent:!0}),this.group.add(i)}},this)}}},axisTickLabel:function(){var t=this.axisModel,e=this.opt,i=cl(this,t,e),n=dl(this,t,e);sl(t,n,i)},axisName:function(){var t=this.opt,e=this.axisModel,i=T(t.axisName,e.get("name"));if(i){var n,r=e.get("nameLocation"),a=t.nameDirection,s=e.getModel("nameTextStyle"),l=e.get("nameGap")||0,h=this.axisModel.axis.getExtent(),u=h[0]>h[1]?-1:1,c=["start"===r?h[0]-u*l:"end"===r?h[1]+u*l:(h[0]+h[1])/2,ul(r)?t.labelOffset+a*l:0],d=e.get("nameRotate");null!=d&&(d=d*sv/180);var f;ul(r)?n=uv(t.rotation,null!=d?d:t.rotation,a):(n=ol(t,r,d||0,h),f=t.axisNameAvailableWidth,null!=f&&(f=Math.abs(f/Math.sin(n.rotation)),!isFinite(f)&&(f=null)));var g=s.getFont(),p=e.get("nameTruncate",!0)||{},v=p.ellipsis,m=T(t.nameTruncateMaxWidth,p.maxWidth,f),y=null!=v&&null!=m?kc(i,m,g,v,{minChar:2,placeholder:p.placeholder}):i,x=e.get("tooltip",!0),_=e.mainType,w={componentType:_,name:i,$vars:["name"]};w[_+"Index"]=e.componentIndex;var b=new Rd({anid:"name",__fullText:i,__truncatedText:y,position:c,rotation:n.rotation,silent:al(e),z2:1,tooltip:x&&x.show?o({content:i,formatter:function(){return i},formatterParams:w},x):null});Ur(b.style,s,{text:y,textFont:g,textFill:s.getTextColor()||e.get("axisLine.lineStyle.color"),textAlign:n.textAlign,textVerticalAlign:n.textVerticalAlign}),e.get("triggerEvent")&&(b.eventData=rl(e),b.eventData.targetType="axisName",b.eventData.name=i),this._dumbGroup.add(b),b.updateTransform(),this.group.add(b),b.decomposeTransform()}}},uv=lv.innerTextLayout=function(t,e,i){var n,r,o=pn(e-t);return vn(o)?(r=i>0?"top":"bottom",n="center"):vn(o-sv)?(r=i>0?"bottom":"top",n="center"):(r="middle",n=o>0&&sv>o?i>0?"right":"left":i>0?"left":"right"),{rotation:o,textAlign:n,textVerticalAlign:r}},cv=lv.ifIgnoreOnTick=function(t,e,i,n,r,o){if(0===e&&r||e===n-1&&o)return!1;var a,s=t.scale;return"ordinal"===s.type&&("function"==typeof i?(a=s.getTicks()[e],!i(a,s.getLabel(a))):e%(i+1))},dv=lv.getInterval=function(t,e){var i=t.get("interval");return(null==i||"auto"==i)&&(i=e),i},fv=d,gv=m,pv=Ka({type:"axis",_axisPointer:null,axisPointerClass:null,render:function(t,e,i,n){this.axisPointerClass&&xl(t),pv.superApply(this,"render",arguments),Ml(this,t,e,i,n,!0)},updateAxisPointer:function(t,e,i,n){Ml(this,t,e,i,n,!1)},remove:function(t,e){var i=this._axisPointer;i&&i.remove(e),pv.superApply(this,"remove",arguments)},dispose:function(t,e){Tl(this,e),pv.superApply(this,"dispose",arguments)}}),vv=[];pv.registerAxisPointerClass=function(t,e){if(vv[t])throw new Error("axisPointer "+t+" exists");vv[t]=e},pv.getAxisPointerClass=function(t){return t&&vv[t]};var mv=lv.ifIgnoreOnTick,yv=lv.getInterval,xv=["axisLine","axisTickLabel","axisName"],_v=["splitArea","splitLine"],wv=pv.extend({type:"cartesianAxis",axisPointerClass:"CartesianAxisPointer",render:function(t,e,i,n){this.group.removeAll();var r=this._axisGroup;if(this._axisGroup=new Pu,this.group.add(this._axisGroup),t.get("show")){var o=t.getCoordSysModel(),a=Il(o,t),s=new lv(t,a);d(xv,s.add,s),this._axisGroup.add(s.getGroup()),d(_v,function(e){t.get(e+".show")&&this["_"+e](t,o,a.labelInterval)},this),lo(r,this._axisGroup,t),wv.superCall(this,"render",t,e,i,n)}},_splitLine:function(t,e,i){var n=t.axis;if(!n.scale.isBlank()){var r=t.getModel("splitLine"),o=r.getModel("lineStyle"),s=o.get("color"),l=yv(r,i);s=y(s)?s:[s];for(var h=e.coordinateSystem.getRect(),u=n.isHorizontal(),c=0,d=n.getTicksCoords(),f=n.scale.getTicks(),g=t.get("axisLabel.showMinLabel"),p=t.get("axisLabel.showMaxLabel"),v=[],m=[],x=o.getLineStyle(),_=0;_<d.length;_++)if(!mv(n,_,l,d.length,g,p)){var w=n.toGlobalCoord(d[_]);u?(v[0]=w,v[1]=h.y,m[0]=w,m[1]=h.y+h.height):(v[0]=h.x,v[1]=w,m[0]=h.x+h.width,m[1]=w);var b=c++%s.length;this._axisGroup.add(new Zd(Lr({anid:"line_"+f[_],shape:{x1:v[0],y1:v[1],x2:m[0],y2:m[1]},style:a({stroke:s[b]},x),silent:!0})))}}},_splitArea:function(t,e,i){var n=t.axis;if(!n.scale.isBlank()){var r=t.getModel("splitArea"),o=r.getModel("areaStyle"),s=o.get("color"),l=e.coordinateSystem.getRect(),h=n.getTicksCoords(),u=n.scale.getTicks(),c=n.toGlobalCoord(h[0]),d=n.toGlobalCoord(h[0]),f=0,g=yv(r,i),p=o.getAreaStyle();s=y(s)?s:[s];for(var v=t.get("axisLabel.showMinLabel"),m=t.get("axisLabel.showMaxLabel"),x=1;x<h.length;x++)if(!mv(n,x,g,h.length,v,m)){var _,w,b,S,M=n.toGlobalCoord(h[x]);n.isHorizontal()?(_=c,w=l.y,b=M-_,S=l.height):(_=l.x,w=d,b=l.width,S=M-w);var T=f++%s.length;this._axisGroup.add(new Ud({anid:"area_"+u[x],shape:{x:_,y:w,width:b,height:S},style:a({fill:s[T]},p),silent:!0})),c=_+b,d=w+S}}}});wv.extend({type:"xAxis"}),wv.extend({type:"yAxis"}),Ka({type:"grid",render:function(t){this.group.removeAll(),t.get("show")&&this.group.add(new Ud({shape:t.coordinateSystem.getRect(),style:a({fill:t.get("backgroundColor")},t.getItemStyle()),silent:!0,z2:-1}))}}),Va(function(t){t.xAxis&&t.yAxis&&!t.grid&&(t.grid={})}),Ua(m(as,"bar")),Za(function(t){t.eachSeriesByType("bar",function(t){var e=t.getData();e.setVisual("legendSymbol","roundRect")})});var bv={updateSelectedMap:function(t){this._targetList=t.slice(),this._selectTargetMap=g(t||[],function(t,e){return t.set(e.name,e),t},z())},select:function(t,e){var i=null!=e?this._targetList[e]:this._selectTargetMap.get(t),n=this.get("selectedMode");"single"===n&&this._selectTargetMap.each(function(t){t.selected=!1}),i&&(i.selected=!0)},unSelect:function(t,e){var i=null!=e?this._targetList[e]:this._selectTargetMap.get(t);i&&(i.selected=!1)},toggleSelected:function(t,e){var i=null!=e?this._targetList[e]:this._selectTargetMap.get(t);return null!=i?(this[i.selected?"unSelect":"select"](t,e),i.selected):void 0},isSelected:function(t,e){var i=null!=e?this._targetList[e]:this._selectTargetMap.get(t);return i&&i.selected}},Sv=Qa({type:"series.pie",init:function(t){Sv.superApply(this,"init",arguments),this.legendDataProvider=function(){return this.getRawData()},this.updateSelectedMap(t.data),this._defaultLabelLine(t)},mergeOption:function(t){Sv.superCall(this,"mergeOption",t),this.updateSelectedMap(this.option.data)},getInitialData:function(t){var e=Ws(["value"],t.data),i=new Np(e,this);return i.initData(t.data),i},getDataParams:function(t){var e=this.getData(),i=Sv.superCall(this,"getDataParams",t),n=[];return e.each("value",function(t){n.push(t)}),i.percent=gn(n,t,e.hostModel.get("percentPrecision")),i.$vars.push("percent"),i},_defaultLabelLine:function(t){mo(t.labelLine,["show"]);var e=t.labelLine.normal,i=t.labelLine.emphasis;e.show=e.show&&t.label.normal.show,i.show=i.show&&t.label.emphasis.show},defaultOption:{zlevel:0,z:2,legendHoverLink:!0,hoverAnimation:!0,center:["50%","50%"],radius:[0,"75%"],clockwise:!0,startAngle:90,minAngle:0,selectedOffset:10,hoverOffset:10,avoidLabelOverlap:!0,percentPrecision:2,stillShowZeroSum:!0,label:{normal:{rotate:!1,show:!0,position:"outer"},emphasis:{}},labelLine:{normal:{show:!0,length:15,length2:15,smooth:!1,lineStyle:{width:1,type:"solid"}}},itemStyle:{normal:{borderWidth:1},emphasis:{}},animationType:"expansion",animationEasing:"cubicOut",data:[]}});u(Sv,bv);var Mv=kl.prototype;Mv.updateData=function(t,e,i){function n(){s.stopAnimation(!0),s.animateTo({shape:{r:u.r+l.get("hoverOffset")}},300,"elasticOut")}function r(){s.stopAnimation(!0),s.animateTo({shape:{r:u.r}},300,"elasticOut")}var s=this.childAt(0),l=t.hostModel,h=t.getItemModel(e),u=t.getItemLayout(e),c=o({},u);if(c.label=null,i){s.setShape(c);var d=l.getShallow("animationType");"scale"===d?(s.shape.r=u.r0,ro(s,{shape:{r:u.r}},l,e)):(s.shape.endAngle=u.startAngle,no(s,{shape:{endAngle:u.endAngle}},l,e))}else no(s,{shape:c},l,e);var f=h.getModel("itemStyle"),g=t.getItemVisual(e,"color");s.useStyle(a({lineJoin:"bevel",fill:g},f.getModel("normal").getItemStyle())),s.hoverStyle=f.getModel("emphasis").getItemStyle();var p=h.getShallow("cursor");p&&s.attr("cursor",p),Al(this,t.getItemLayout(e),h.get("selected"),l.get("selectedOffset"),l.get("animation")),s.off("mouseover").off("mouseout").off("emphasis").off("normal"),h.get("hoverAnimation")&&l.isAnimationEnabled()&&s.on("mouseover",n).on("mouseout",r).on("emphasis",n).on("normal",r),this._updateLabel(t,e),qr(this)},Mv._updateLabel=function(t,e){var i=this.childAt(1),n=this.childAt(2),r=t.hostModel,o=t.getItemModel(e),a=t.getItemLayout(e),s=a.label,l=t.getItemVisual(e,"color");no(i,{shape:{points:s.linePoints||[[s.x,s.y],[s.x,s.y],[s.x,s.y]]}},r,e),no(n,{style:{x:s.x,y:s.y}},r,e),n.attr({rotation:s.rotation,origin:[s.x,s.y],z2:10});var h=o.getModel("label.normal"),u=o.getModel("label.emphasis"),c=o.getModel("labelLine.normal"),d=o.getModel("labelLine.emphasis"),l=t.getItemVisual(e,"color");Yr(n.style,n.hoverStyle={},h,u,{labelFetcher:t.hostModel,labelDataIndex:e,defaultText:t.getName(e),autoColor:l,useInsideStyle:!!s.inside},{textAlign:s.textAlign,textVerticalAlign:s.verticalAlign,opacity:t.getItemVisual(e,"opacity")}),n.ignore=n.normalIgnore=!h.get("show"),n.hoverIgnore=!u.get("show"),i.ignore=i.normalIgnore=!c.get("show"),i.hoverIgnore=!d.get("show"),i.setStyle({stroke:l,opacity:t.getItemVisual(e,"opacity")}),i.setStyle(c.getModel("lineStyle").getLineStyle()),i.hoverStyle=d.getModel("lineStyle").getLineStyle();var f=c.get("smooth");f&&f===!0&&(f=.4),i.setShape({smooth:f})},h(kl,Pu);var Tv=(ha.extend({type:"pie",init:function(){var t=new Pu;this._sectorGroup=t},render:function(t,e,i,n){if(!n||n.from!==this.uid){var r=t.getData(),o=this._data,a=this.group,s=e.get("animation"),l=!o,h=t.get("animationType"),u=m(Cl,this.uid,t,s,i),c=t.get("selectedMode");if(r.diff(o).add(function(t){var e=new kl(r,t);l&&"scale"!==h&&e.eachChild(function(t){t.stopAnimation(!0)}),c&&e.on("click",u),r.setItemGraphicEl(t,e),a.add(e)}).update(function(t,e){var i=o.getItemGraphicEl(e);i.updateData(r,t),i.off("click"),c&&i.on("click",u),a.add(i),r.setItemGraphicEl(t,i)}).remove(function(t){var e=o.getItemGraphicEl(t);a.remove(e)}).execute(),s&&l&&r.count()>0&&"scale"!==h){var d=r.getItemLayout(0),f=Math.max(i.getWidth(),i.getHeight())/2,g=v(a.removeClipPath,a);a.setClipPath(this._createClipPath(d.cx,d.cy,f,d.startAngle,d.clockwise,g,t))}this._data=r}},dispose:function(){},_createClipPath:function(t,e,i,n,r,o,a){var s=new Vd({shape:{cx:t,cy:e,r0:0,r:i,startAngle:n,endAngle:n,clockwise:r}});return ro(s,{shape:{endAngle:n+(r?1:-1)*Math.PI*2}},a,o),s},containPoint:function(t,e){var i=e.getData(),n=i.getItemLayout(0);if(n){var r=t[0]-n.cx,o=t[1]-n.cy,a=Math.sqrt(r*r+o*o);return a<=n.r&&a>=n.r0}}}),function(t,e){d(e,function(e){e.update="updateView",Xa(e,function(i,n){var r={};return n.eachComponent({mainType:"series",subType:t,query:i},function(t){t[e.method]&&t[e.method](i.name,i.dataIndex);var n=t.getData();n.each(function(e){var i=n.getName(e);r[i]=t.isSelected(i)||!1})}),{name:i.name,selected:r}})})}),Iv=function(t,e){var i={};e.eachRawSeriesByType(t,function(t){var n=t.getRawData(),r={};if(!e.isSeriesFiltered(t)){var o=t.getData();o.each(function(t){var e=o.getRawIndex(t);r[e]=t}),n.each(function(e){var a=r[e],s=null!=a&&o.getItemVisual(a,"color",!0);if(s)n.setItemVisual(e,"color",s);else{var l=n.getItemModel(e),h=l.get("itemStyle.normal.color")||t.getColorFromPalette(n.getName(e),i);n.setItemVisual(e,"color",h),null!=a&&o.setItemVisual(a,"color",h)}})}})},Cv=function(t,e,i,n){var r,o,a=t.getData(),s=[],l=!1;a.each(function(i){var n,h,u,c,d=a.getItemLayout(i),f=a.getItemModel(i),g=f.getModel("label.normal"),p=g.get("position")||f.get("label.emphasis.position"),v=f.getModel("labelLine.normal"),m=v.get("length"),y=v.get("length2"),x=(d.startAngle+d.endAngle)/2,_=Math.cos(x),w=Math.sin(x);r=d.cx,o=d.cy;var b="inside"===p||"inner"===p;if("center"===p)n=d.cx,h=d.cy,c="center";else{var S=(b?(d.r+d.r0)/2*_:d.r*_)+r,M=(b?(d.r+d.r0)/2*w:d.r*w)+o;if(n=S+3*_,h=M+3*w,!b){var T=S+_*(m+e-d.r),I=M+w*(m+e-d.r),C=T+(0>_?-1:1)*y,A=I;n=C+(0>_?-5:5),h=A,u=[[S,M],[T,I],[C,A]]}c=b?"center":_>0?"left":"right"}var k=g.getFont(),L=g.get("rotate")?0>_?-x+Math.PI:-x:0,D=t.getFormattedLabel(i,"normal")||a.getName(i),P=ei(D,k,c,"top");l=!!L,d.label={x:n,y:h,position:p,height:P.height,len:m,len2:y,linePoints:u,textAlign:c,verticalAlign:"middle",rotation:L,inside:b},b||s.push(d.label)}),!l&&t.get("avoidLabelOverlap")&&Dl(s,r,o,e,i,n)},Av=2*Math.PI,kv=Math.PI/180,Lv=function(t,e,i){e.eachSeriesByType(t,function(t){var e=t.get("center"),n=t.get("radius");y(n)||(n=[0,n]),y(e)||(e=[e,e]);var r=i.getWidth(),o=i.getHeight(),a=Math.min(r,o),s=un(e[0],r),l=un(e[1],o),h=un(n[0],a/2),u=un(n[1],a/2),c=t.getData(),d=-t.get("startAngle")*kv,f=t.get("minAngle")*kv,g=0;c.each("value",function(t){!isNaN(t)&&g++});var p=c.getSum("value"),v=Math.PI/(p||g)*2,m=t.get("clockwise"),x=t.get("roseType"),_=t.get("stillShowZeroSum"),w=c.getDataExtent("value");w[0]=0;var b=Av,S=0,M=d,T=m?1:-1;if(c.each("value",function(t,e){var i;if(isNaN(t))return void c.setItemLayout(e,{angle:0/0,startAngle:0/0,endAngle:0/0,clockwise:m,cx:s,cy:l,r0:h,r:x?0/0:u});i="area"!==x?0===p&&_?v:t*v:Av/g,f>i?(i=f,b-=f):S+=t;var n=M+T*i;c.setItemLayout(e,{angle:i,startAngle:M,endAngle:n,clockwise:m,cx:s,cy:l,r0:h,r:x?hn(t,w,[h,u]):u}),M=n},!0),Av>b&&g)if(.001>=b){var I=Av/g;c.each("value",function(t,e){if(!isNaN(t)){var i=c.getItemLayout(e);i.angle=I,i.startAngle=d+T*e*I,i.endAngle=d+T*(e+1)*I}})}else v=b/S,M=d,c.each("value",function(t,e){if(!isNaN(t)){var i=c.getItemLayout(e),n=i.angle===f?f:t*v;i.startAngle=M,i.endAngle=M+T*n,M+=T*n}});Cv(t,u,r,o)})},Dv=function(t,e){var i=e.findComponents({mainType:"legend"});i&&i.length&&e.eachSeriesByType(t,function(t){var e=t.getData();e.filterSelf(function(t){for(var n=e.getName(t),r=0;r<i.length;r++)if(!i[r].isSelected(n))return!1;return!0},this)},this)};Tv("pie",[{type:"pieToggleSelect",event:"pieselectchanged",method:"toggleSelected"},{type:"pieSelect",event:"pieselected",method:"select"},{type:"pieUnSelect",event:"pieunselected",method:"unSelect"}]),Za(m(Iv,"pie")),Ua(m(Lv,"pie")),Wa(m(Dv,"pie")),$a({type:"title",layoutMode:{type:"box",ignoreSize:!0},defaultOption:{zlevel:0,z:6,show:!0,text:"",target:"blank",subtext:"",subtarget:"blank",left:0,top:0,backgroundColor:"rgba(0,0,0,0)",borderColor:"#ccc",borderWidth:0,padding:5,itemGap:10,textStyle:{fontSize:18,fontWeight:"bolder",color:"#333"},subtextStyle:{color:"#aaa"}}}),Ka({type:"title",render:function(t,e,i){if(this.group.removeAll(),t.get("show")){var n=this.group,r=t.getModel("textStyle"),o=t.getModel("subtextStyle"),a=t.get("textAlign"),s=t.get("textBaseline"),l=new Rd({style:Ur({},r,{text:t.get("text"),textFill:r.getTextColor()},{disableBox:!0}),z2:10}),h=l.getBoundingRect(),u=t.get("subtext"),c=new Rd({style:Ur({},o,{text:u,textFill:o.getTextColor(),y:h.height+t.get("itemGap"),textVerticalAlign:"top"},{disableBox:!0}),z2:10}),d=t.get("link"),f=t.get("sublink");l.silent=!d,c.silent=!f,d&&l.on("click",function(){window.open(d,"_"+t.get("target"))}),f&&c.on("click",function(){window.open(f,"_"+t.get("subtarget"))}),n.add(l),u&&n.add(c);var g=n.getBoundingRect(),p=t.getBoxLayoutParams();p.width=g.width,p.height=g.height;var v=zo(p,{width:i.getWidth(),height:i.getHeight()},t.get("padding"));a||(a=t.get("left")||t.get("right"),"middle"===a&&(a="center"),"right"===a?v.x+=v.width:"center"===a&&(v.x+=v.width/2)),s||(s=t.get("top")||t.get("bottom"),"center"===s&&(s="middle"),"bottom"===s?v.y+=v.height:"middle"===s&&(v.y+=v.height/2),s=s||"top"),n.attr("position",[v.x,v.y]);var m={textAlign:a,textVerticalAlign:s};l.setStyle(m),c.setStyle(m),g=n.getBoundingRect();var y=v.margin,x=t.getItemStyle(["color","opacity"]);x.fill=t.get("backgroundColor");var _=new Ud({shape:{x:g.x-y[3],y:g.y-y[0],width:g.width+y[1]+y[3],height:g.height+y[0]+y[2],r:t.get("borderRadius")},style:x,silent:!0});Dr(_),n.add(_)}}});var Pv=$a({type:"legend.plain",dependencies:["series"],layoutMode:{type:"box",ignoreSize:!0},init:function(t,e,i){this.mergeDefaultAndTheme(t,i),t.selected=t.selected||{}},mergeOption:function(t){Pv.superCall(this,"mergeOption",t)},optionUpdated:function(){this._updateData(this.ecModel);var t=this._data;if(t[0]&&"single"===this.get("selectedMode")){for(var e=!1,i=0;i<t.length;i++){var n=t[i].get("name");if(this.isSelected(n)){this.select(n),e=!0;break}}!e&&this.select(t[0].get("name"))}},_updateData:function(t){var e=f(this.get("data")||[],function(t){return("string"==typeof t||"number"==typeof t)&&(t={name:t}),new fo(t,this,this.ecModel)},this);this._data=e;var i=f(t.getSeries(),function(t){return t.name});t.eachSeries(function(t){if(t.legendDataProvider){var e=t.legendDataProvider();i=i.concat(e.mapArray(e.getName))}}),this._availableNames=i},getData:function(){return this._data},select:function(t){var e=this.option.selected,i=this.get("selectedMode");if("single"===i){var n=this._data;d(n,function(t){e[t.get("name")]=!1})}e[t]=!0},unSelect:function(t){"single"!==this.get("selectedMode")&&(this.option.selected[t]=!1)},toggleSelected:function(t){var e=this.option.selected;e.hasOwnProperty(t)||(e[t]=!0),this[e[t]?"unSelect":"select"](t)},isSelected:function(t){var e=this.option.selected;return!(e.hasOwnProperty(t)&&!e[t])&&l(this._availableNames,t)>=0},defaultOption:{zlevel:0,z:4,show:!0,orient:"horizontal",left:"center",top:0,align:"auto",backgroundColor:"rgba(0,0,0,0)",borderColor:"#ccc",borderRadius:0,borderWidth:0,padding:5,itemGap:10,itemWidth:25,itemHeight:14,inactiveColor:"#ccc",textStyle:{color:"#333"},selectedMode:!0,tooltip:{show:!1}}});Xa("legendToggleSelect","legendselectchanged",m(Pl,"toggleSelected")),Xa("legendSelect","legendselected",m(Pl,"select")),Xa("legendUnSelect","legendunselected",m(Pl,"unSelect"));var Ov=m,zv=d,Bv=Pu,Ev=Ka({type:"legend.plain",newlineDisabled:!1,init:function(){this.group.add(this._contentGroup=new Bv),this._backgroundEl},getContentGroup:function(){return this._contentGroup},render:function(t,e,i){if(this.resetInner(),t.get("show",!0)){var n=t.get("align");n&&"auto"!==n||(n="right"===t.get("left")&&"vertical"===t.get("orient")?"right":"left"),this.renderInner(n,t,e,i);var r=t.getBoxLayoutParams(),o={width:i.getWidth(),height:i.getHeight()},s=t.get("padding"),l=zo(r,o,s),h=this.layoutInner(t,n,l),u=zo(a({width:h.width,height:h.height},r),o,s);this.group.attr("position",[u.x-h.x,u.y-h.y]),this.group.add(this._backgroundEl=Ol(h,t))}},resetInner:function(){this.getContentGroup().removeAll(),this._backgroundEl&&this.group.remove(this._backgroundEl)},renderInner:function(t,e,i,n){var r=this.getContentGroup(),o=z(),a=e.get("selectedMode");zv(e.getData(),function(s,l){var h=s.get("name");if(!this.newlineDisabled&&(""===h||"\n"===h))return void r.add(new Bv({newline:!0}));var u=i.getSeriesByName(h)[0];if(!o.get(h)){if(u){var c=u.getData(),d=c.getVisual("color");"function"==typeof d&&(d=d(u.getDataParams(0)));var f=c.getVisual("legendSymbol")||"roundRect",g=c.getVisual("symbol"),p=this._createItem(h,l,s,e,f,g,t,d,a);p.on("click",Ov(zl,h,n)).on("mouseover",Ov(Bl,u,null,n)).on("mouseout",Ov(El,u,null,n)),o.set(h,!0)}else i.eachRawSeries(function(i){if(!o.get(h)&&i.legendDataProvider){var r=i.legendDataProvider(),u=r.indexOfName(h);if(0>u)return;var c=r.getItemVisual(u,"color"),d="roundRect",f=this._createItem(h,l,s,e,d,null,t,c,a);f.on("click",Ov(zl,h,n)).on("mouseover",Ov(Bl,i,h,n)).on("mouseout",Ov(El,i,h,n)),o.set(h,!0)}},this);o.get(h)||console.warn(h+" series not exists. Legend data should be same with series name or data name.")}},this)},_createItem:function(t,e,i,n,r,a,s,l,h){var u=n.get("itemWidth"),c=n.get("itemHeight"),d=n.get("inactiveColor"),f=n.isSelected(t),g=new Bv,p=i.getModel("textStyle"),v=i.get("icon"),m=i.getModel("tooltip"),y=m.parentModel;if(r=v||r,g.add(nl(r,0,0,u,c,f?l:d,!0)),!v&&a&&(a!==r||"none"==a)){var x=.8*c;"none"===a&&(a="circle"),g.add(nl(a,(u-x)/2,(c-x)/2,x,x,f?l:d))}var _="left"===s?u+5:-5,w=s,b=n.get("formatter"),S=t;"string"==typeof b&&b?S=b.replace("{name}",null!=t?t:""):"function"==typeof b&&(S=b(t)),g.add(new Rd({style:Ur({},p,{text:S,x:_,y:c/2,textFill:f?p.getTextColor():d,textAlign:w,textVerticalAlign:"middle"})}));var M=new Ud({shape:g.getBoundingRect(),invisible:!0,tooltip:m.get("show")?o({content:t,formatter:y.get("formatter",!0)||function(){return t},formatterParams:{componentType:"legend",legendIndex:n.componentIndex,name:t,$vars:["name"]}},m.option):null});return g.add(M),g.eachChild(function(t){t.silent=!0}),M.silent=!h,this.getContentGroup().add(g),qr(g),g.__legendDataIndex=e,g},layoutInner:function(t,e,i){var n=this.getContentGroup();Mf(t.get("orient"),n,t.get("itemGap"),i.width,i.height);var r=n.getBoundingRect();return n.attr("position",[-r.x,-r.y]),this.group.getBoundingRect()}}),Rv=function(t){var e=t.findComponents({mainType:"legend"});e&&e.length&&t.filterSeries(function(t){for(var i=0;i<e.length;i++)if(!e[i].isSelected(t.name))return!1;return!0})};Wa(Rv),Cf.registerSubTypeDefaulter("legend",function(){return"plain"});var Nv=Pv.extend({type:"legend.scroll",setScrollDataIndex:function(t){this.option.scrollDataIndex=t},defaultOption:{scrollDataIndex:0,pageButtonItemGap:5,pageButtonGap:null,pageButtonPosition:"end",pageFormatter:"{current}/{total}",pageIcons:{horizontal:["M0,0L12,-10L12,10z","M0,0L-12,-10L-12,10z"],vertical:["M0,0L20,0L10,-20z","M0,0L20,0L10,20z"]},pageIconColor:"#2f4554",pageIconInactiveColor:"#aaa",pageIconSize:15,pageTextStyle:{color:"#333"},animationDurationUpdate:800},init:function(t,e,i,n){var r=Eo(t);Nv.superCall(this,"init",t,e,i,n),Rl(this,t,r)},mergeOption:function(t,e){Nv.superCall(this,"mergeOption",t,e),Rl(this,this.option,t)},getOrient:function(){return"vertical"===this.get("orient")?{index:1,name:"vertical"}:{index:0,name:"horizontal"}}}),Fv=Pu,Hv=["width","height"],Vv=["x","y"],Wv=Ev.extend({type:"legend.scroll",newlineDisabled:!0,init:function(){Wv.superCall(this,"init"),this._currentIndex=0,this.group.add(this._containerGroup=new Fv),this._containerGroup.add(this.getContentGroup()),this.group.add(this._controllerGroup=new Fv),this._showController},resetInner:function(){Wv.superCall(this,"resetInner"),this._controllerGroup.removeAll(),this._containerGroup.removeClipPath(),this._containerGroup.__rectSize=null},renderInner:function(t,e,i,n){function r(t,i){var r=t+"DataIndex",l=co(e.get("pageIcons",!0)[e.getOrient().name][i],{onclick:v(o._pageGo,o,r,e,n)},{x:-s[0]/2,y:-s[1]/2,width:s[0],height:s[1]});l.name=t,a.add(l)}var o=this;Wv.superCall(this,"renderInner",t,e,i,n);var a=this._controllerGroup,s=e.get("pageIconSize",!0);y(s)||(s=[s,s]),r("pagePrev",0);var l=e.getModel("pageTextStyle");a.add(new Rd({name:"pageText",style:{textFill:l.getTextColor(),font:l.getFont(),textVerticalAlign:"middle",textAlign:"center"},silent:!0})),r("pageNext",1)},layoutInner:function(t,e,i){var n=this.getContentGroup(),r=this._containerGroup,o=this._controllerGroup,a=t.getOrient().index,s=Hv[a],l=Hv[1-a],h=Vv[1-a];Mf(t.get("orient"),n,t.get("itemGap"),a?i.width:null,a?null:i.height),Mf("horizontal",o,t.get("pageButtonItemGap",!0));var u=n.getBoundingRect(),c=o.getBoundingRect(),d=this._showController=u[s]>i[s],f=[-u.x,-u.y];f[a]=n.position[a];var g=[0,0],p=[-c.x,-c.y],v=I(t.get("pageButtonGap",!0),t.get("itemGap",!0));if(d){var m=t.get("pageButtonPosition",!0);"end"===m?p[a]+=i[s]-c[s]:g[a]+=c[s]+v}p[1-a]+=u[l]/2-c[l]/2,n.attr("position",f),r.attr("position",g),o.attr("position",p);var y=this.group.getBoundingRect(),y={x:0,y:0};if(y[s]=d?i[s]:u[s],y[l]=Math.max(u[l],c[l]),y[h]=Math.min(0,c[h]+p[1-a]),r.__rectSize=i[s],d){var x={x:0,y:0};x[s]=Math.max(i[s]-c[s]-v,0),x[l]=y[l],r.setClipPath(new Ud({shape:x})),r.__rectSize=x[s]}else o.eachChild(function(t){t.attr({invisible:!0,silent:!0})});var _=this._getPageInfo(t);return null!=_.pageIndex&&no(n,{position:_.contentPosition},d?t:!1),this._updatePageInfoView(t,_),y},_pageGo:function(t,e,i){var n=this._getPageInfo(e)[t];
null!=n&&i.dispatchAction({type:"legendScroll",scrollDataIndex:n,legendId:e.id})},_updatePageInfoView:function(t,e){var i=this._controllerGroup;d(["pagePrev","pageNext"],function(n){var r=null!=e[n+"DataIndex"],o=i.childOfName(n);o&&(o.setStyle("fill",r?t.get("pageIconColor",!0):t.get("pageIconInactiveColor",!0)),o.cursor=r?"pointer":"default")});var n=i.childOfName("pageText"),r=t.get("pageFormatter"),o=e.pageIndex,a=null!=o?o+1:0,s=e.pageCount;n&&r&&n.setStyle("text",_(r)?r.replace("{current}",a).replace("{total}",s):r({current:a,total:s}))},_getPageInfo:function(t){function e(t){var e=t.getBoundingRect().clone();return e[f]+=t.position[u],e}var i,n,r,o,a=t.get("scrollDataIndex",!0),s=this.getContentGroup(),l=s.getBoundingRect(),h=this._containerGroup.__rectSize,u=t.getOrient().index,c=Hv[u],d=Hv[1-u],f=Vv[u],g=s.position.slice();this._showController?s.eachChild(function(t){t.__legendDataIndex===a&&(o=t)}):o=s.childAt(0);var p=h?Math.ceil(l[c]/h):0;if(o){var v=o.getBoundingRect(),m=o.position[u]+v[f];g[u]=-m-l[f],i=Math.floor(p*(m+v[f]+h/2)/l[c]),i=l[c]&&p?Math.max(0,Math.min(p-1,i)):-1;var y={x:0,y:0};y[c]=h,y[d]=l[d],y[f]=-g[u]-l[f];var x,_=s.children();if(s.eachChild(function(t,i){var n=e(t);n.intersect(y)&&(null==x&&(x=i),r=t.__legendDataIndex),i===_.length-1&&n[f]+n[c]<=y[f]+y[c]&&(r=null)}),null!=x){var w=_[x],b=e(w);if(y[f]=b[f]+b[c]-y[c],0>=x&&b[f]>=y[f])n=null;else{for(;x>0&&e(_[x-1]).intersect(y);)x--;n=_[x].__legendDataIndex}}}return{contentPosition:g,pageIndex:i,pageCount:p,pagePrevDataIndex:n,pageNextDataIndex:r}}});Xa("legendScroll","legendscroll",function(t,e){var i=t.scrollDataIndex;null!=i&&e.eachComponent({mainType:"legend",subType:"scroll",query:t},function(t){t.setScrollDataIndex(i)})});var Gv=function(t,e){var i,n=[],r=t.seriesIndex;if(null==r||!(i=e.getSeriesByIndex(r)))return{point:[]};var o=i.getData(),a=Mo(o,t);if(null==a||y(a))return{point:[]};var s=o.getItemGraphicEl(a),l=i.coordinateSystem;if(i.getTooltipPosition)n=i.getTooltipPosition(a)||[];else if(l&&l.dataToPoint)n=l.dataToPoint(o.getValues(f(l.dimensions,function(t){return i.coordDimToDataDim(t)[0]}),a,!0))||[];else if(s){var h=s.getBoundingRect().clone();h.applyTransform(s.transform),n=[h.x+h.width/2,h.y+h.height/2]}return{point:n,el:s}},Xv=d,qv=m,Yv=yf(),Uv=function(t,e,i){var n=t.currTrigger,r=[t.x,t.y],o=t,a=t.dispatchAction||v(i.dispatchAction,i),s=e.getComponent("axisPointer").coordSysAxesInfo;if(s){Ul(r)&&(r=Gv({seriesIndex:o.seriesIndex,dataIndex:o.dataIndex},e).point);var l=Ul(r),h=o.axesInfo,u=s.axesInfo,c="leave"===n||Ul(r),d={},f={},g={list:[],map:{}},p={showPointer:qv(Hl,f),showTooltip:qv(Vl,g)};Xv(s.coordSysMap,function(t,e){var i=l||t.containPoint(r);Xv(s.coordSysAxesInfo[e],function(t){var e=t.axis,n=ql(h,t);if(!c&&i&&(!h||n)){var o=n&&n.value;null!=o||l||(o=e.pointToData(r)),null!=o&&Nl(t,o,p,!1,d)}})});var m={};return Xv(u,function(t,e){var i=t.linkGroup;i&&!f[e]&&Xv(i.axesInfo,function(e,n){var r=f[n];if(e!==t&&r){var o=r.value;i.mapper&&(o=t.axis.scale.parse(i.mapper(o,Yl(e),Yl(t)))),m[t.key]=o}})}),Xv(m,function(t,e){Nl(u[e],t,p,!0,d)}),Wl(f,u,d),Gl(g,r,t,a),Xl(u,a,i),d}},Zv=($a({type:"axisPointer",coordSysAxesInfo:null,defaultOption:{show:"auto",triggerOn:null,zlevel:0,z:50,type:"line",snap:!1,triggerTooltip:!0,value:null,status:null,link:[],animation:null,animationDurationUpdate:200,lineStyle:{color:"#aaa",width:1,type:"solid"},shadowStyle:{color:"rgba(150,150,150,0.3)"},label:{show:!0,formatter:null,precision:"auto",margin:3,color:"#fff",padding:[5,7,5,7],backgroundColor:"auto",borderColor:null,borderWidth:0,shadowBlur:3,shadowColor:"#aaa"},handle:{show:!1,icon:"M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4h1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7v-1.2h6.6z M13.3,22H6.7v-1.2h6.6z M13.3,19.6H6.7v-1.2h6.6z",size:45,margin:50,color:"#333",shadowBlur:3,shadowColor:"#aaa",shadowOffsetX:0,shadowOffsetY:2,throttle:40}}}),yf()),jv=d,$v=Ka({type:"axisPointer",render:function(t,e,i){var n=e.getComponent("tooltip"),r=t.get("triggerOn")||n&&n.get("triggerOn")||"mousemove|click";Zl("axisPointer",i,function(t,e,i){"none"!==r&&("leave"===t||r.indexOf(t)>=0)&&i({type:"updateAxisPointer",currTrigger:t,x:e&&e.offsetX,y:e&&e.offsetY})})},remove:function(t,e){th(e.getZr(),"axisPointer"),$v.superApply(this._model,"remove",arguments)},dispose:function(t,e){th("axisPointer",e),$v.superApply(this._model,"dispose",arguments)}}),Kv=yf(),Qv=i,Jv=v;eh.prototype={_group:null,_lastGraphicKey:null,_handle:null,_dragging:!1,_lastValue:null,_lastStatus:null,_payloadInfo:null,animationThreshold:15,render:function(t,e,i,n){var r=e.get("value"),o=e.get("status");if(this._axisModel=t,this._axisPointerModel=e,this._api=i,n||this._lastValue!==r||this._lastStatus!==o){this._lastValue=r,this._lastStatus=o;var a=this._group,s=this._handle;if(!o||"hide"===o)return a&&a.hide(),void(s&&s.hide());a&&a.show(),s&&s.show();var l={};this.makeElOption(l,r,t,e,i);var h=l.graphicKey;h!==this._lastGraphicKey&&this.clear(i),this._lastGraphicKey=h;var u=this._moveAnimation=this.determineAnimation(t,e);if(a){var c=m(ih,e,u);this.updatePointerEl(a,l,c,e),this.updateLabelEl(a,l,c,e)}else a=this._group=new Pu,this.createPointerEl(a,l,t,e),this.createLabelEl(a,l,t,e),i.getZr().add(a);ah(a,e,!0),this._renderHandle(r)}},remove:function(t){this.clear(t)},dispose:function(t){this.clear(t)},determineAnimation:function(t,e){var i=e.get("animation"),n=t.axis,r="category"===n.type,o=e.get("snap");if(!o&&!r)return!1;if("auto"===i||null==i){var a=this.animationThreshold;if(r&&n.getBandWidth()>a)return!0;if(o){var s=_l(t).seriesDataCount,l=n.getExtent();return Math.abs(l[0]-l[1])/s>a}return!1}return i===!0},makeElOption:function(){},createPointerEl:function(t,e){var i=e.pointer;if(i){var n=Kv(t).pointerEl=new lf[i.type](Qv(e.pointer));t.add(n)}},createLabelEl:function(t,e,i,n){if(e.label){var r=Kv(t).labelEl=new Ud(Qv(e.label));t.add(r),rh(r,n)}},updatePointerEl:function(t,e,i){var n=Kv(t).pointerEl;n&&(n.setStyle(e.pointer.style),i(n,{shape:e.pointer.shape}))},updateLabelEl:function(t,e,i,n){var r=Kv(t).labelEl;r&&(r.setStyle(e.label.style),i(r,{shape:e.label.shape,position:e.label.position}),rh(r,n))},_renderHandle:function(t){if(!this._dragging&&this.updateHandleTransform){var e=this._axisPointerModel,i=this._api.getZr(),n=this._handle,r=e.getModel("handle"),o=e.get("status");if(!r.get("show")||!o||"hide"===o)return n&&i.remove(n),void(this._handle=null);var a;this._handle||(a=!0,n=this._handle=co(r.get("icon"),{cursor:"move",draggable:!0,onmousemove:function(t){lc(t.event)},onmousedown:Jv(this._onHandleDragMove,this,0,0),drift:Jv(this._onHandleDragMove,this),ondragend:Jv(this._onHandleDragEnd,this)}),i.add(n)),ah(n,e,!1);var s=["color","borderColor","borderWidth","opacity","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY"];n.setStyle(r.getItemStyle(null,s));var l=r.get("size");y(l)||(l=[l,l]),n.attr("scale",[l[0]/2,l[1]/2]),fa(this,"_doDispatchAxisPointer",r.get("throttle")||0,"fixRate"),this._moveHandleToValue(t,a)}},_moveHandleToValue:function(t,e){ih(this._axisPointerModel,!e&&this._moveAnimation,this._handle,oh(this.getHandleTransform(t,this._axisModel,this._axisPointerModel)))},_onHandleDragMove:function(t,e){var i=this._handle;if(i){this._dragging=!0;var n=this.updateHandleTransform(oh(i),[t,e],this._axisModel,this._axisPointerModel);this._payloadInfo=n,i.stopAnimation(),i.attr(oh(n)),Kv(i).lastProp=null,this._doDispatchAxisPointer()}},_doDispatchAxisPointer:function(){var t=this._handle;if(t){var e=this._payloadInfo,i=this._axisModel;this._api.dispatchAction({type:"updateAxisPointer",x:e.cursorPoint[0],y:e.cursorPoint[1],tooltipOption:e.tooltipOption,axesInfo:[{axisDim:i.axis.dim,axisIndex:i.componentIndex}]})}},_onHandleDragEnd:function(){this._dragging=!1;var t=this._handle;if(t){var e=this._axisPointerModel.get("value");this._moveHandleToValue(e),this._api.dispatchAction({type:"hideTip"})}},getHandleTransform:null,updateHandleTransform:null,clear:function(t){this._lastValue=null,this._lastStatus=null;var e=t.getZr(),i=this._group,n=this._handle;e&&i&&(this._lastGraphicKey=null,i&&e.remove(i),n&&e.remove(n),this._group=null,this._handle=null,this._payloadInfo=null)},doClear:function(){},buildLabel:function(t,e,i){return i=i||0,{x:t[i],y:t[1-i],width:e[i],height:e[1-i]}}},eh.prototype.constructor=eh,Pn(eh);var tm=eh.extend({makeElOption:function(t,e,i,n,r){var o=i.axis,a=o.grid,s=n.get("type"),l=ph(a,o).getOtherAxis(o).getGlobalExtent(),h=o.toGlobalCoord(o.dataToCoord(e,!0));if(s&&"none"!==s){var u=sh(n),c=em[s](o,h,l,u);c.style=u,t.graphicKey=c.type,t.pointer=c}var d=Il(a.model,i);dh(e,t,d,i,n,r)},getHandleTransform:function(t,e,i){var n=Il(e.axis.grid.model,e,{labelInside:!1});return n.labelMargin=i.get("handle.margin"),{position:ch(e.axis,t,n),rotation:n.rotation+(n.labelDirection<0?Math.PI:0)}},updateHandleTransform:function(t,e,i){var n=i.axis,r=n.grid,o=n.getGlobalExtent(!0),a=ph(r,n).getOtherAxis(n).getGlobalExtent(),s="x"===n.dim?0:1,l=t.position;l[s]+=e[s],l[s]=Math.min(o[1],l[s]),l[s]=Math.max(o[0],l[s]);var h=(a[1]+a[0])/2,u=[h,h];u[s]=l[s];var c=[{verticalAlign:"middle"},{align:"center"}];return{position:l,rotation:t.rotation,cursorPoint:u,tooltipOption:c[s]}}}),em={line:function(t,e,i,n){var r=fh([e,i[0]],[e,i[1]],vh(t));return Lr({shape:r,style:n}),{type:"Line",shape:r}},shadow:function(t,e,i){var n=t.getBandWidth(),r=i[1]-i[0];return{type:"Rect",shape:gh([e-n/2,i[0]],[n,r],vh(t))}}};pv.registerAxisPointerClass("CartesianAxisPointer",tm),Va(function(t){if(t){(!t.axisPointer||0===t.axisPointer.length)&&(t.axisPointer={});var e=t.axisPointer.link;e&&!y(e)&&(t.axisPointer.link=[e])}}),Wa(wg.PROCESSOR.STATISTIC,function(t,e){t.getComponent("axisPointer").coordSysAxesInfo=fl(t,e)}),Xa({type:"updateAxisPointer",event:"updateAxisPointer",update:":updateAxisPointer"},Uv),$a({type:"tooltip",dependencies:["axisPointer"],defaultOption:{zlevel:0,z:8,show:!0,showContent:!0,trigger:"item",triggerOn:"mousemove|click",alwaysShowContent:!1,displayMode:"single",confine:!1,showDelay:0,hideDelay:100,transitionDuration:.4,enterable:!1,backgroundColor:"rgba(50,50,50,0.7)",borderColor:"#333",borderRadius:4,borderWidth:0,padding:5,extraCssText:"",axisPointer:{type:"line",axis:"auto",animation:"auto",animationDurationUpdate:200,animationEasingUpdate:"exponentialOut",crossStyle:{color:"#999",width:1,type:"dashed",textStyle:{}}},textStyle:{color:"#fff",fontSize:14}}});var im=d,nm=bn,rm=["","-webkit-","-moz-","-o-"],om="position:absolute;display:block;border-style:solid;white-space:nowrap;z-index:9999999;";_h.prototype={constructor:_h,_enterable:!0,update:function(){var t=this._container,e=t.currentStyle||document.defaultView.getComputedStyle(t),i=t.style;"absolute"!==i.position&&"absolute"!==e.position&&(i.position="relative")},show:function(t){clearTimeout(this._hideTimeout);var e=this.el;e.style.cssText=om+xh(t)+";left:"+this._x+"px;top:"+this._y+"px;"+(t.get("extraCssText")||""),e.style.display=e.innerHTML?"block":"none",this._show=!0},setContent:function(t){this.el.innerHTML=null==t?"":t},setEnterable:function(t){this._enterable=t},getSize:function(){var t=this.el;return[t.clientWidth,t.clientHeight]},moveTo:function(t,e){var i,n=this._zr;n&&n.painter&&(i=n.painter.getViewportRootOffset())&&(t+=i.offsetLeft,e+=i.offsetTop);var r=this.el.style;r.left=t+"px",r.top=e+"px",this._x=t,this._y=e},hide:function(){this.el.style.display="none",this._show=!1},hideLater:function(t){!this._show||this._inContent&&this._enterable||(t?(this._hideDelay=t,this._show=!1,this._hideTimeout=setTimeout(v(this.hide,this),t)):this.hide())},isShow:function(){return this._show}};var am=v,sm=d,lm=un,hm=new Ud({shape:{x:-1,y:-1,width:2,height:2}});Ka({type:"tooltip",init:function(t,e){if(!Eh.node){var i=new _h(e.getDom(),e);this._tooltipContent=i}},render:function(t,e,i){if(!Eh.node){this.group.removeAll(),this._tooltipModel=t,this._ecModel=e,this._api=i,this._lastDataByCoordSys=null,this._alwaysShowContent=t.get("alwaysShowContent");var n=this._tooltipContent;n.update(),n.setEnterable(t.get("enterable")),this._initGlobalListener(),this._keepShow()}},_initGlobalListener:function(){var t=this._tooltipModel,e=t.get("triggerOn");Zl("itemTooltip",this._api,am(function(t,i,n){"none"!==e&&(e.indexOf(t)>=0?this._tryShow(i,n):"leave"===t&&this._hide(n))},this))},_keepShow:function(){var t=this._tooltipModel,e=this._ecModel,i=this._api;if(null!=this._lastX&&null!=this._lastY&&"none"!==t.get("triggerOn")){var n=this;clearTimeout(this._refreshUpdateTimeout),this._refreshUpdateTimeout=setTimeout(function(){n.manuallyShowTip(t,e,i,{x:n._lastX,y:n._lastY})})}},manuallyShowTip:function(t,e,i,n){if(n.from!==this.uid&&!Eh.node){var r=bh(n,i);this._ticket="";var o=n.dataByCoordSys;if(n.tooltip&&null!=n.x&&null!=n.y){var a=hm;a.position=[n.x,n.y],a.update(),a.tooltip=n.tooltip,this._tryShow({offsetX:n.x,offsetY:n.y,target:a},r)}else if(o)this._tryShow({offsetX:n.x,offsetY:n.y,position:n.position,event:{},dataByCoordSys:n.dataByCoordSys,tooltipOption:n.tooltipOption},r);else if(null!=n.seriesIndex){if(this._manuallyAxisShowTip(t,e,i,n))return;var s=Gv(n,e),l=s.point[0],h=s.point[1];null!=l&&null!=h&&this._tryShow({offsetX:l,offsetY:h,position:n.position,target:s.el,event:{}},r)}else null!=n.x&&null!=n.y&&(i.dispatchAction({type:"updateAxisPointer",x:n.x,y:n.y}),this._tryShow({offsetX:n.x,offsetY:n.y,position:n.position,target:i.getZr().findHover(n.x,n.y).target,event:{}},r))}},manuallyHideTip:function(t,e,i,n){var r=this._tooltipContent;this._alwaysShowContent||r.hideLater(this._tooltipModel.get("hideDelay")),this._lastX=this._lastY=null,n.from!==this.uid&&this._hide(bh(n,i))},_manuallyAxisShowTip:function(t,e,i,n){var r=n.seriesIndex,o=n.dataIndex,a=e.getComponent("axisPointer").coordSysAxesInfo;if(null!=r&&null!=o&&null!=a){var s=e.getSeriesByIndex(r);if(s){var l=s.getData(),t=wh([l.getItemModel(o),s,(s.coordinateSystem||{}).model,t]);if("axis"===t.get("trigger"))return i.dispatchAction({type:"updateAxisPointer",seriesIndex:r,dataIndex:o,position:n.position}),!0}}},_tryShow:function(t,e){var i=t.target,n=this._tooltipModel;if(n){this._lastX=t.offsetX,this._lastY=t.offsetY;var r=t.dataByCoordSys;r&&r.length?this._showAxisTooltip(r,t):i&&null!=i.dataIndex?(this._lastDataByCoordSys=null,this._showSeriesItemTooltip(t,i,e)):i&&i.tooltip?(this._lastDataByCoordSys=null,this._showComponentItemTooltip(t,i,e)):(this._lastDataByCoordSys=null,this._hide(e))}},_showOrMove:function(t,e){var i=t.get("showDelay");e=v(e,this),clearTimeout(this._showTimout),i>0?this._showTimout=setTimeout(e,i):e()},_showAxisTooltip:function(t,e){var i=this._ecModel,n=this._tooltipModel,r=[e.offsetX,e.offsetY],o=[],a=[],s=wh([e.tooltipOption,n]);sm(t,function(t){sm(t.dataByAxis,function(t){var e=i.getComponent(t.axisDim+"Axis",t.axisIndex),n=t.value,r=[];if(e&&null!=n){var s=uh(n,e.axis,i,t.seriesDataIndices,t.valueLabelOpt);d(t.seriesDataIndices,function(o){var l=i.getSeriesByIndex(o.seriesIndex),h=o.dataIndexInside,u=l&&l.getDataParams(h);u.axisDim=t.axisDim,u.axisIndex=t.axisIndex,u.axisType=t.axisType,u.axisId=t.axisId,u.axisValue=_s(e.axis,n),u.axisValueLabel=s,u&&(a.push(u),r.push(l.formatTooltip(h,!0)))});var l=s;o.push((l?Sn(l)+"<br />":"")+r.join("<br />"))}})},this),o.reverse(),o=o.join("<br /><br />");var l=e.position;this._showOrMove(s,function(){this._updateContentNotChangedOnAxis(t)?this._updatePosition(s,l,r[0],r[1],this._tooltipContent,a):this._showTooltipContent(s,o,a,Math.random(),r[0],r[1],l)})},_showSeriesItemTooltip:function(t,e,i){var n=this._ecModel,r=e.seriesIndex,o=n.getSeriesByIndex(r),a=e.dataModel||o,s=e.dataIndex,l=e.dataType,h=a.getData(),u=wh([h.getItemModel(s),a,o&&(o.coordinateSystem||{}).model,this._tooltipModel]),c=u.get("trigger");if(null==c||"item"===c){var d=a.getDataParams(s,l),f=a.formatTooltip(s,!1,l),g="item_"+a.name+"_"+s;this._showOrMove(u,function(){this._showTooltipContent(u,f,d,g,t.offsetX,t.offsetY,t.position,t.target)}),i({type:"showTip",dataIndexInside:s,dataIndex:h.getRawIndex(s),seriesIndex:r,from:this.uid})}},_showComponentItemTooltip:function(t,e,i){var n=e.tooltip;if("string"==typeof n){var r=n;n={content:r,formatter:r}}var o=new fo(n,this._tooltipModel,this._ecModel),a=o.get("content"),s=Math.random();this._showOrMove(o,function(){this._showTooltipContent(o,a,o.get("formatterParams")||{},s,t.offsetX,t.offsetY,t.position,e)}),i({type:"showTip",from:this.uid})},_showTooltipContent:function(t,e,i,n,r,o,a,s){if(this._ticket="",t.get("showContent")&&t.get("show")){var l=this._tooltipContent,h=t.get("formatter");a=a||t.get("position");var u=e;if(h&&"string"==typeof h)u=Mn(h,i,!0);else if("function"==typeof h){var c=am(function(e,n){e===this._ticket&&(l.setContent(n),this._updatePosition(t,a,r,o,l,i,s))},this);this._ticket=n,u=h(i,n,c)}l.setContent(u),l.show(t),this._updatePosition(t,a,r,o,l,i,s)}},_updatePosition:function(t,e,i,n,r,o,a){var s=this._api.getWidth(),l=this._api.getHeight();e=e||t.get("position");var h=r.getSize(),u=t.get("align"),c=t.get("verticalAlign"),d=a&&a.getBoundingRect().clone();if(a&&d.applyTransform(a.transform),"function"==typeof e&&(e=e([i,n],o,r.el,d,{viewSize:[s,l],contentSize:h.slice()})),y(e))i=lm(e[0],s),n=lm(e[1],l);else if(w(e)){e.width=h[0],e.height=h[1];var f=zo(e,{width:s,height:l});i=f.x,n=f.y,u=null,c=null}else if("string"==typeof e&&a){var g=Ih(e,d,h);i=g[0],n=g[1]}else{var g=Sh(i,n,r.el,s,l,u?null:20,c?null:20);i=g[0],n=g[1]}if(u&&(i-=Ch(u)?h[0]/2:"right"===u?h[0]:0),c&&(n-=Ch(c)?h[1]/2:"bottom"===c?h[1]:0),t.get("confine")){var g=Mh(i,n,r.el,s,l);i=g[0],n=g[1]}r.moveTo(i,n)},_updateContentNotChangedOnAxis:function(t){var e=this._lastDataByCoordSys,i=!!e&&e.length===t.length;return i&&sm(e,function(e,n){var r=e.dataByAxis||{},o=t[n]||{},a=o.dataByAxis||[];i&=r.length===a.length,i&&sm(r,function(t,e){var n=a[e]||{},r=t.seriesDataIndices||[],o=n.seriesDataIndices||[];i&=t.value===n.value&&t.axisType===n.axisType&&t.axisId===n.axisId&&r.length===o.length,i&&sm(r,function(t,e){var n=o[e];i&=t.seriesIndex===n.seriesIndex&&t.dataIndex===n.dataIndex})})}),this._lastDataByCoordSys=t,!!i},_hide:function(t){this._lastDataByCoordSys=null,t({type:"hideTip",from:this.uid})},dispose:function(t,e){Eh.node||(this._tooltipContent.hide(),th("itemTooltip",e))}}),Xa({type:"showTip",event:"showTip",update:"tooltip:manuallyShowTip"},function(){}),Xa({type:"hideTip",event:"hideTip",update:"tooltip:manuallyHideTip"},function(){});var um,cm="urn:schemas-microsoft-com:vml",dm=window,fm=!1,gm=dm&&dm.document;if(gm&&!Eh.canvasSupported)try{!gm.namespaces.zrvml&&gm.namespaces.add("zrvml",cm),um=function(t){return gm.createElement("<zrvml:"+t+' class="zrvml">')}}catch(pm){um=function(t){return gm.createElement("<"+t+' xmlns="'+cm+'" class="zrvml">')}}var vm=Math.round,mm=Math.sqrt,ym=Math.abs,xm=Math.cos,_m=Math.sin,wm=Math.max;if(!Eh.canvasSupported){var bm=",",Sm="progid:DXImageTransform.Microsoft",Mm=21600,Tm=Mm/2,Im=1e5,Cm=1e3,Am=function(t){t.style.cssText="position:absolute;left:0;top:0;width:1px;height:1px;",t.coordsize=Mm+","+Mm,t.coordorigin="0,0"},km=function(t){return String(t).replace(/&/g,"&amp;").replace(/"/g,"&quot;")},Lm=function(t,e,i){return"rgb("+[t,e,i].join(",")+")"},Dm=function(t,e){e&&t&&e.parentNode!==t&&t.appendChild(e)},Pm=function(t,e){e&&t&&e.parentNode===t&&t.removeChild(e)},Om=function(t,e,i){return(parseFloat(t)||0)*Im+(parseFloat(e)||0)*Cm+i},zm=function(t,e){return"string"==typeof t?t.lastIndexOf("%")>=0?parseFloat(t)/100*e:parseFloat(t):t},Bm=function(t,e,i){var n=ye(e);i=+i,isNaN(i)&&(i=1),n&&(t.color=Lm(n[0],n[1],n[2]),t.opacity=i*n[3])},Em=function(t){var e=ye(t);return[Lm(e[0],e[1],e[2]),e[3]]},Rm=function(t,e,i){var n=e.fill;if(null!=n)if(n instanceof Jd){var r,o=0,a=[0,0],s=0,l=1,h=i.getBoundingRect(),u=h.width,c=h.height;if("linear"===n.type){r="gradient";var d=i.transform,f=[n.x*u,n.y*c],g=[n.x2*u,n.y2*c];d&&(Y(f,f,d),Y(g,g,d));var p=g[0]-f[0],v=g[1]-f[1];o=180*Math.atan2(p,v)/Math.PI,0>o&&(o+=360),1e-6>o&&(o=0)}else{r="gradientradial";var f=[n.x*u,n.y*c],d=i.transform,m=i.scale,y=u,x=c;a=[(f[0]-h.x)/y,(f[1]-h.y)/x],d&&Y(f,f,d),y/=m[0]*Mm,x/=m[1]*Mm;var _=wm(y,x);s=0/_,l=2*n.r/_-s}var w=n.colorStops.slice();w.sort(function(t,e){return t.offset-e.offset});for(var b=w.length,S=[],M=[],T=0;b>T;T++){var I=w[T],C=Em(I.color);M.push(I.offset*l+s+" "+C[0]),(0===T||T===b-1)&&S.push(C)}if(b>=2){var A=S[0][0],k=S[1][0],L=S[0][1]*e.opacity,D=S[1][1]*e.opacity;t.type=r,t.method="none",t.focus="100%",t.angle=o,t.color=A,t.color2=k,t.colors=M.join(","),t.opacity=D,t.opacity2=L}"radial"===r&&(t.focusposition=a.join(","))}else Bm(t,n,e.opacity)},Nm=function(t,e){null!=e.lineDash&&(t.dashstyle=e.lineDash.join(" ")),null==e.stroke||e.stroke instanceof Jd||Bm(t,e.stroke,e.opacity)},Fm=function(t,e,i,n){var r="fill"==e,o=t.getElementsByTagName(e)[0];null!=i[e]&&"none"!==i[e]&&(r||!r&&i.lineWidth)?(t[r?"filled":"stroked"]="true",i[e]instanceof Jd&&Pm(t,o),o||(o=um(e)),r?Rm(o,i,n):Nm(o,i),Dm(t,o)):(t[r?"filled":"stroked"]="false",Pm(t,o))},Hm=[[],[],[]],Vm=function(t,e){var i,n,r,o,a,s,l=nd.M,h=nd.C,u=nd.L,c=nd.A,d=nd.Q,f=[],g=t.data,p=t.len();for(o=0;p>o;){switch(r=g[o++],n="",i=0,r){case l:n=" m ",i=1,a=g[o++],s=g[o++],Hm[0][0]=a,Hm[0][1]=s;break;case u:n=" l ",i=1,a=g[o++],s=g[o++],Hm[0][0]=a,Hm[0][1]=s;break;case d:case h:n=" c ",i=3;var v,m,y=g[o++],x=g[o++],_=g[o++],w=g[o++];r===d?(v=_,m=w,_=(_+2*y)/3,w=(w+2*x)/3,y=(a+2*y)/3,x=(s+2*x)/3):(v=g[o++],m=g[o++]),Hm[0][0]=y,Hm[0][1]=x,Hm[1][0]=_,Hm[1][1]=w,Hm[2][0]=v,Hm[2][1]=m,a=v,s=m;break;case c:var b=0,S=0,M=1,T=1,I=0;e&&(b=e[4],S=e[5],M=mm(e[0]*e[0]+e[1]*e[1]),T=mm(e[2]*e[2]+e[3]*e[3]),I=Math.atan2(-e[1]/T,e[0]/M));var C=g[o++],A=g[o++],k=g[o++],L=g[o++],D=g[o++]+I,P=g[o++]+D+I;o++;var O=g[o++],z=C+xm(D)*k,B=A+_m(D)*L,y=C+xm(P)*k,x=A+_m(P)*L,E=O?" wa ":" at ";Math.abs(z-y)<1e-4&&(Math.abs(P-D)>.01?O&&(z+=270/Mm):Math.abs(B-A)<1e-4?O&&C>z||!O&&z>C?x-=270/Mm:x+=270/Mm:O&&A>B||!O&&B>A?y+=270/Mm:y-=270/Mm),f.push(E,vm(((C-k)*M+b)*Mm-Tm),bm,vm(((A-L)*T+S)*Mm-Tm),bm,vm(((C+k)*M+b)*Mm-Tm),bm,vm(((A+L)*T+S)*Mm-Tm),bm,vm((z*M+b)*Mm-Tm),bm,vm((B*T+S)*Mm-Tm),bm,vm((y*M+b)*Mm-Tm),bm,vm((x*T+S)*Mm-Tm)),a=y,s=x;break;case nd.R:var R=Hm[0],N=Hm[1];R[0]=g[o++],R[1]=g[o++],N[0]=R[0]+g[o++],N[1]=R[1]+g[o++],e&&(Y(R,R,e),Y(N,N,e)),R[0]=vm(R[0]*Mm-Tm),N[0]=vm(N[0]*Mm-Tm),R[1]=vm(R[1]*Mm-Tm),N[1]=vm(N[1]*Mm-Tm),f.push(" m ",R[0],bm,R[1]," l ",N[0],bm,R[1]," l ",N[0],bm,N[1]," l ",R[0],bm,N[1]);break;case nd.Z:f.push(" x ")}if(i>0){f.push(n);for(var F=0;i>F;F++){var H=Hm[F];e&&Y(H,H,e),f.push(vm(H[0]*Mm-Tm),bm,vm(H[1]*Mm-Tm),i-1>F?bm:"")}}}return f.join("")};gr.prototype.brushVML=function(t){var e=this.style,i=this._vmlEl;i||(i=um("shape"),Am(i),this._vmlEl=i),Fm(i,"fill",e,this),Fm(i,"stroke",e,this);var n=this.transform,r=null!=n,o=i.getElementsByTagName("stroke")[0];if(o){var a=e.lineWidth;if(r&&!e.strokeNoScale){var s=n[0]*n[3]-n[1]*n[2];a*=mm(ym(s))}o.weight=a+"px"}var l=this.path||(this.path=new pd);this.__dirtyPath&&(l.beginPath(),this.buildPath(l,this.shape),l.toStatic(),this.__dirtyPath=!1),i.path=Vm(l,this.transform),i.style.zIndex=Om(this.zlevel,this.z,this.z2),Dm(t,i),null!=e.text?this.drawRectText(t,this.getBoundingRect()):this.removeRectText(t)},gr.prototype.onRemove=function(t){Pm(t,this._vmlEl),this.removeRectText(t)},gr.prototype.onAdd=function(t){Dm(t,this._vmlEl),this.appendRectText(t)};var Wm=function(t){return"object"==typeof t&&t.tagName&&"IMG"===t.tagName.toUpperCase()};Ei.prototype.brushVML=function(t){var e,i,n=this.style,r=n.image;if(Wm(r)){var o=r.src;if(o===this._imageSrc)e=this._imageWidth,i=this._imageHeight;else{var a=r.runtimeStyle,s=a.width,l=a.height;a.width="auto",a.height="auto",e=r.width,i=r.height,a.width=s,a.height=l,this._imageSrc=o,this._imageWidth=e,this._imageHeight=i}r=o}else r===this._imageSrc&&(e=this._imageWidth,i=this._imageHeight);if(r){var h=n.x||0,u=n.y||0,c=n.width,d=n.height,f=n.sWidth,g=n.sHeight,p=n.sx||0,v=n.sy||0,m=f&&g,y=this._vmlEl;y||(y=gm.createElement("div"),Am(y),this._vmlEl=y);var x,_=y.style,w=!1,b=1,S=1;if(this.transform&&(x=this.transform,b=mm(x[0]*x[0]+x[1]*x[1]),S=mm(x[2]*x[2]+x[3]*x[3]),w=x[1]||x[2]),w){var M=[h,u],T=[h+c,u],I=[h,u+d],C=[h+c,u+d];Y(M,M,x),Y(T,T,x),Y(I,I,x),Y(C,C,x);var A=wm(M[0],T[0],I[0],C[0]),k=wm(M[1],T[1],I[1],C[1]),L=[];L.push("M11=",x[0]/b,bm,"M12=",x[2]/S,bm,"M21=",x[1]/b,bm,"M22=",x[3]/S,bm,"Dx=",vm(h*b+x[4]),bm,"Dy=",vm(u*S+x[5])),_.padding="0 "+vm(A)+"px "+vm(k)+"px 0",_.filter=Sm+".Matrix("+L.join("")+", SizingMethod=clip)"}else x&&(h=h*b+x[4],u=u*S+x[5]),_.filter="",_.left=vm(h)+"px",_.top=vm(u)+"px";var D=this._imageEl,P=this._cropEl;D||(D=gm.createElement("div"),this._imageEl=D);var O=D.style;if(m){if(e&&i)O.width=vm(b*e*c/f)+"px",O.height=vm(S*i*d/g)+"px";else{var z=new Image,B=this;z.onload=function(){z.onload=null,e=z.width,i=z.height,O.width=vm(b*e*c/f)+"px",O.height=vm(S*i*d/g)+"px",B._imageWidth=e,B._imageHeight=i,B._imageSrc=r},z.src=r}P||(P=gm.createElement("div"),P.style.overflow="hidden",this._cropEl=P);var E=P.style;E.width=vm((c+p*c/f)*b),E.height=vm((d+v*d/g)*S),E.filter=Sm+".Matrix(Dx="+-p*c/f*b+",Dy="+-v*d/g*S+")",P.parentNode||y.appendChild(P),D.parentNode!=P&&P.appendChild(D)}else O.width=vm(b*c)+"px",O.height=vm(S*d)+"px",y.appendChild(D),P&&P.parentNode&&(y.removeChild(P),this._cropEl=null);var R="",N=n.opacity;1>N&&(R+=".Alpha(opacity="+vm(100*N)+") "),R+=Sm+".AlphaImageLoader(src="+r+", SizingMethod=scale)",O.filter=R,y.style.zIndex=Om(this.zlevel,this.z,this.z2),Dm(t,y),null!=n.text&&this.drawRectText(t,this.getBoundingRect())}},Ei.prototype.onRemove=function(t){Pm(t,this._vmlEl),this._vmlEl=null,this._cropEl=null,this._imageEl=null,this.removeRectText(t)},Ei.prototype.onAdd=function(t){Dm(t,this._vmlEl),this.appendRectText(t)};var Gm,Xm="normal",qm={},Ym=0,Um=100,Zm=document.createElement("div"),jm=function(t){var e=qm[t];if(!e){Ym>Um&&(Ym=0,qm={});var i,n=Zm.style;try{n.font=t,i=n.fontFamily.split(",")[0]}catch(r){}e={style:n.fontStyle||Xm,variant:n.fontVariant||Xm,weight:n.fontWeight||Xm,size:0|parseFloat(n.fontSize||12),family:i||"Microsoft YaHei"},qm[t]=e,Ym++}return e};Ku.measureText(function(t,e){var i=gm;Gm||(Gm=i.createElement("div"),Gm.style.cssText="position:absolute;top:-20000px;left:0;padding:0;margin:0;border:none;white-space:pre;",gm.body.appendChild(Gm));try{Gm.style.font=e}catch(n){}return Gm.innerHTML="",Gm.appendChild(i.createTextNode(t)),{width:Gm.offsetWidth}});for(var $m=new Ee,Km=function(t,e,i,n){var r=this.style;this.__dirty&&mi(r,!0);var o=r.text;if(null!=o&&(o+=""),o){if(r.rich){var a=fi(o,r);o=[];for(var s=0;s<a.lines.length;s++){for(var l=a.lines[s].tokens,h=[],u=0;u<l.length;u++)h.push(l[u].text);o.push(h.join(""))}o=o.join("\n")}var c,d,f=r.textAlign,g=r.textVerticalAlign,p=jm(r.font),v=p.style+" "+p.variant+" "+p.weight+" "+p.size+'px "'+p.family+'"';i=i||ei(o,v,f,g);var m=this.transform;if(m&&!n&&($m.copy(e),$m.applyTransform(m),e=$m),n)c=e.x,d=e.y;else{var y=r.textPosition,x=r.textDistance;if(y instanceof Array)c=e.x+zm(y[0],e.width),d=e.y+zm(y[1],e.height),f=f||"left";else{var _=ai(y,e,x);c=_.x,d=_.y,f=f||_.textAlign,g=g||_.textVerticalAlign}}c=ri(c,i.width,f),d=oi(d,i.height,g),d+=i.height/2;var w,b,S,M=um,T=this._textVmlEl;T?(S=T.firstChild,w=S.nextSibling,b=w.nextSibling):(T=M("line"),w=M("path"),b=M("textpath"),S=M("skew"),b.style["v-text-align"]="left",Am(T),w.textpathok=!0,b.on=!0,T.from="0 0",T.to="1000 0.05",Dm(T,S),Dm(T,w),Dm(T,b),this._textVmlEl=T);var I=[c,d],C=T.style;m&&n?(Y(I,I,m),S.on=!0,S.matrix=m[0].toFixed(3)+bm+m[2].toFixed(3)+bm+m[1].toFixed(3)+bm+m[3].toFixed(3)+",0,0",S.offset=(vm(I[0])||0)+","+(vm(I[1])||0),S.origin="0 0",C.left="0px",C.top="0px"):(S.on=!1,C.left=vm(c)+"px",C.top=vm(d)+"px"),b.string=km(o);try{b.style.font=v}catch(A){}Fm(T,"fill",{fill:r.textFill,opacity:r.opacity},this),Fm(T,"stroke",{stroke:r.textStroke,opacity:r.opacity,lineDash:r.lineDash},this),T.style.zIndex=Om(this.zlevel,this.z,this.z2),Dm(t,T)}},Qm=function(t){Pm(t,this._textVmlEl),this._textVmlEl=null},Jm=function(t){Dm(t,this._textVmlEl)},ty=[ec,Bi,Ei,gr,Rd],ey=0;ey<ty.length;ey++){var iy=ty[ey].prototype;iy.drawRectText=Km,iy.removeRectText=Qm,iy.appendRectText=Jm}Rd.prototype.brushVML=function(t){var e=this.style;null!=e.text?this.drawRectText(t,{x:e.x||0,y:e.y||0,width:0,height:0},this.getBoundingRect(),!0):this.removeRectText(t)},Rd.prototype.onRemove=function(t){this.removeRectText(t)},Rd.prototype.onAdd=function(t){this.appendRectText(t)}}Lh.prototype={constructor:Lh,getType:function(){return"vml"},getViewportRoot:function(){return this._vmlViewport},getViewportRootOffset:function(){var t=this.getViewportRoot();return t?{offsetLeft:t.offsetLeft||0,offsetTop:t.offsetTop||0}:void 0},refresh:function(){var t=this.storage.getDisplayList(!0,!0);this._paintList(t)},_paintList:function(t){for(var e=this._vmlRoot,i=0;i<t.length;i++){var n=t[i];n.invisible||n.ignore?(n.__alreadyNotVisible||n.onRemove(e),n.__alreadyNotVisible=!0):(n.__alreadyNotVisible&&n.onAdd(e),n.__alreadyNotVisible=!1,n.__dirty&&(n.beforeBrush&&n.beforeBrush(),(n.brushVML||n.brush).call(n,e),n.afterBrush&&n.afterBrush())),n.__dirty=!1}this._firstPaint&&(this._vmlViewport.appendChild(e),this._firstPaint=!1)},resize:function(t,e){var t=null==t?this._getWidth():t,e=null==e?this._getHeight():e;if(this._width!=t||this._height!=e){this._width=t,this._height=e;var i=this._vmlViewport.style;i.width=t+"px",i.height=e+"px"}},dispose:function(){this.root.innerHTML="",this._vmlRoot=this._vmlViewport=this.storage=null},getWidth:function(){return this._width},getHeight:function(){return this._height},clear:function(){this._vmlViewport&&this.root.removeChild(this._vmlViewport)},_getWidth:function(){var t=this.root,e=t.currentStyle;return(t.clientWidth||kh(e.width))-kh(e.paddingLeft)-kh(e.paddingRight)|0},_getHeight:function(){var t=this.root,e=t.currentStyle;return(t.clientHeight||kh(e.height))-kh(e.paddingTop)-kh(e.paddingBottom)|0}},d(["getLayer","insertLayer","eachLayer","eachBuiltinLayer","eachOtherLayer","getLayers","modLayer","delLayer","clearLayer","toDataURL","pathToImage"],function(t){Lh.prototype[t]=Dh(t)}),sn("vml",Lh),t.version=dg,t.dependencies=fg,t.PRIORITY=wg,t.init=za,t.connect=Ba,t.disConnect=Ea,t.disconnect=Wg,t.dispose=Ra,t.getInstanceByDom=Na,t.getInstanceById=Fa,t.registerTheme=Ha,t.registerPreprocessor=Va,t.registerProcessor=Wa,t.registerPostUpdate=Ga,t.registerAction=Xa,t.registerCoordinateSystem=qa,t.getCoordinateSystemDimensions=Ya,t.registerLayout=Ua,t.registerVisual=Za,t.registerLoading=ja,t.extendComponentModel=$a,t.extendComponentView=Ka,t.extendSeriesModel=Qa,t.extendChartView=Ja,t.setCanvasCreator=ts,t.$inject=Gg});