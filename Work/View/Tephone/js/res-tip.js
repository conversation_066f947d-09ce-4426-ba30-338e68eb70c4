define([
  'require',
  'jquery'
], function(require, $) {
  return function(info, failed, cb) {
    var messageHtml = failed ? fail(info) : suc(info),
        $mes = $(messageHtml);

    $('body').append($mes);
    setTimeout(function() {
      $mes.remove();
      cb && cb();
    }, 2000);

    // helper
    function suc(info) {
      return '<div class="res-suc">'+info+'</div>'
    }
    function fail(info) {
      return '<div class="res-fail">'+info+'</div>'
    }
  }
});