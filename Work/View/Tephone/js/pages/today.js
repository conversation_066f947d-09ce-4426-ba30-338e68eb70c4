require.config({
  baseUrl: '/Work/View/Tephone/js/',
  paths:{
      jquery: 'lib/jquery',
      rem: 'lib/int.rem.min',
  }
})
require([
  'jquery',
  'rem',
  'common',
  './utils/dot-render',
], function($,rem,common,dotRender) {
  console.log( $('.time-change .detail-time #times').text())
  var timesShow = $('.time-change .detail-time #times');
  var weeksShow = $('.time-change .detail-time #weeks');
  //获取当前日期
  function getNowTime() {
    // getShowTime(new Date());
    var todayTimes=new Date();
    var seperator1 = "-";
    var year = todayTimes.getFullYear();
    var month = todayTimes.getMonth() + 1;
    var strDate = todayTimes.getDate();
    var myddy = todayTimes.getDay(); //获取存储当前日期
    var weekday = ["周日", "周一", "周二", "周三", "周四", "周五", "周六"];
    if (month >= 1 && month <= 9) {
      month = "0" + month;
    }
    if (strDate >= 0 && strDate <= 9) {
      strDate = "0" + strDate;
    }
    var currentdate = year + seperator1 + month + seperator1 + strDate;
    // this.timeShow = currentdate;
    // this.weekShow = weekday[myddy];
    timesShow.text(currentdate);
    weeksShow.text(weekday[myddy]);
    // weekShow.text()
    console.log(currentdate,111)
    console.log(weekday[myddy],222)
   
  }
  // 时间格式以及周几设置
  function getShowTime(date) {
    var seperator1 = "-";
    var year = date.getFullYear();
    var month = date.getMonth() + 1;
    var strDate = date.getDate();
    var myddy = date.getDay(); //获取存储当前日期
    var weekday = ["周日", "周一", "周二", "周三", "周四", "周五", "周六"];
    if (month >= 1 && month <= 9) {
      month = "0" + month;
    }
    if (strDate >= 0 && strDate <= 9) {
      strDate = "0" + strDate;
    }
    var currentdate = year + seperator1 + month + seperator1 + strDate;
    // this.timeShow = currentdate;
    // this.weekShow = weekday[myddy];
    timesShow.text(currentdate);
    weeksShow.text(weekday[myddy]);
    // weekShow.text()
    console.log(currentdate,111)
    console.log(weekday[myddy],222)
    // loading(currentdate,currentdate)
    page=1;
    getIntention(page,currentdate,currentdate)
  }
  function dateToMs(date) {
    var result = new Date(date).getTime();
    return result;
  }
  // 时间选择++
  function addTime() {
    var now = timesShow.text();
    now = dateToMs(now); //年月日转换为秒数
    now = now + 1000 * 60 * 60 * 24;
    now = new Date(now); //秒数转化为北京标准时间
    // if (now > new Date()) {
    //   return false;
    // } else {
    //   getShowTime(now);
    // }
    getShowTime(now);
    
  }
   // 时间选择--
   function reduceTime() {
    // var yesterday = this.timeShow;
    // yesterday = this.dateToMs(yesterday); //年月日转换为秒数
    var yesterday = timesShow.text();
    yesterday = dateToMs(yesterday); //年月日转换为秒数

    yesterday = yesterday - 1000 * 60 * 60 * 24;
    yesterday = new Date(yesterday); //秒数转化为北京标准时间
    getShowTime(yesterday);
  }
  //页面加载显示当前时间
    getNowTime();

  //向左-点击
  $('.time-change').on('click','.arrow-left',function(){
    $('#popMto').empty();
    $('#popMto1').empty();
    results.list = [];
    results.clocking = {};
    reduceTime();
  })
  //向右-点击
  $('.time-change').on('click','.arrow-right',function(){
    $('#popMto').empty();
    $('#popMto1').empty();
    results.list = [];
    results.clocking = {};
    addTime();
  })

  //滑动加载
  mui.init({
    pullRefresh: {
      container: '#pullrefresh',
      down: {
        callback: pulldownRefresh
      },
      up: {
        contentrefresh: '正在加载...',
        callback: pullupRefresh
      }
    }
  });
  /**
   * 下拉刷新具体业务实现
   */
  var results=[];
  results.list = [];
  results.clocking = {};
  var page=0;
  function pulldownRefresh() {
    page=1;
    results.list=[];
    results.clocking = {};
    setTimeout(function() {
      var table = document.body.querySelector('.mui-table-view');
      var cells = document.body.querySelectorAll('.mui-table-view-cell');
      
      getIntention(page,timesShow.text(),timesShow.text())

      mui('#pullrefresh').pullRefresh().endPulldownToRefresh(); //refresh completed
      mui('#pullrefresh').pullRefresh().refresh(true);
    }, 1500);
  }
  var count = 0;
  /**
   * 上拉加载具体业务实现
   */
  function pullupRefresh() {
    page++;
    let arr = 0;

    getIntention(page,timesShow.text(),timesShow.text()).then(res=>{
      // console.log('接收到结果为：'+res);
      arr = res;
    })
    setTimeout(function() {
      mui('#pullrefresh').pullRefresh().endPullupToRefresh((arr<10)); //参数为true代表没有更多数据了。
      var table = document.body.querySelector('.mui-table-view');
      var cells = document.body.querySelectorAll('.mui-table-view-cell');


    }, 1500);
  }
  if (mui.os.plus) {
    mui.plusReady(function() {
      setTimeout(function() {
        mui('#pullrefresh').pullRefresh().pullupLoading();
      }, 1000);

    });
  } else {
    mui.ready(function() {
      mui('#pullrefresh').pullRefresh().pullupLoading();
    });
  };

  function getIntention(page,startTime,endTime){
    return new Promise(function(resolve){
      $.get('/Clocking/WeekClocking', {
        staffer_id: JSON.parse(localStorage.getItem('stafferId')),
        school_id : JSON.parse(localStorage.getItem('setBrand')),
        company_id:JSON.parse(localStorage.getItem('companyId')),
        start_day:startTime,
        end_day:endTime,
        p:page,
        num:10,
      }, function (res) {
        console.log(res)
        resolve(res.result.list.length);

        if (res.error == '0') {

          results.list = results.list.concat(res.result.list);
          results.clocking=res.result.clocking;

          dotRender('#allNumber', results, '#popMto');
          dotRender('#familyStu', results, '#popMto1');
        } else {
      
        }

      }, 'json');
    });
  }

  mui('body').on('tap','a',function(){document.location.href=this.href;});
  mui('body').on('click','a',function(){document.location.href=this.href;});
})