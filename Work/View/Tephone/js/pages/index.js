require.config({
  baseUrl: '/Work/View/Tephone/js/',
  paths:{
      jquery: 'lib/jquery',
      rem: 'lib/int.rem.min',
      swiper: 'lib/swiper.min'
  }
})
require(['jquery','rem','res-tip','swiper'], function($,rem,resTip,swiper) {
//存储staffer_id 存储company_id
var staffer_id = $('#staffer_id').val();
var company_id = $('#company_id').val();
var school_id = $('#school_id').val();
localStorage.setItem('stafferId', JSON.stringify(staffer_id));
localStorage.setItem('companyId', JSON.stringify(company_id));
localStorage.setItem('setBrand', JSON.stringify(school_id));
console.log(JSON.parse(localStorage.getItem('stafferId')))
console.log(JSON.parse(localStorage.getItem('companyId')))
console.log(JSON.parse(localStorage.getItem('setBrand')))

  //获取学校id
  var getbrandId = JSON.parse(localStorage.getItem('setBrand'))
  console.log(getbrandId)
  //学校id-传入后台
  var schoolIds=getbrandId;
  if (getbrandId) {
    var lis = $('.school-select .option li')
    console.log(lis)
    lis.each(function (i, e) {
      console.log(i, $(e).data('schoolid'), getbrandId)
      if ($(e).data('schoolid') == getbrandId) {
        $(e).addClass('active')
        $('.add-swiper-position .select .title').text($(e).text())
      }
    });
  } else {
   //第一次加载 显示 请选择学校
    // var firstVal = $('.school-select .select .option li').eq(0).text();
    // $('.school-select .select .title').text(firstVal);
    // getbrandId = $('.school-select .select .option li').eq(0).data('schoolid');
    // localStorage.setItem('Brand', JSON.stringify(getbrandId));
  }
  //页面加载-提交接口
  schoolPost(schoolIds)

  //存储学校id
  var falgs=false;//是否点击切换学校
  var $lis= $('.school-select .select .option li');
  $lis.on('click',function(){
    falgs=true;
    console.log($(this).data('schoolid'),11111);
    localStorage.setItem('setBrand', JSON.stringify($(this).data('schoolid')));
    $lis.removeClass('active').eq($(this).index()).addClass('active');
    console.log($(this).index())
    schoolIds = JSON.parse(localStorage.getItem('setBrand'));
    console.log(schoolIds,'我是id')
    schoolPost(schoolIds)
    // console.log(JSON.parse(localStorage.getItem('setBrand')))
  })

  
  //头部的下拉列表
  $('.school-select .select').on('click','.title',function(){
    var $t = $(this);
    var optionList = $t.parents('.school-select').find('.option');
    if(optionList.is(':hidden')){
      optionList.show();
      $('.cover-props').show();
    }else{
      optionList.hide();
      $('.cover-props').hide();
    }
  });

  $('.school-select .select .option').on('click','li',function(){
    var $t = $(this);
    var index = $t.index();
    var val = $t.text();
    var title = $t.parents('.school-select').find('.title');
    title.text(val);
    $t.parents('.option').hide();
    $('.cover-props').hide();
      console.log(index)
      console.log($(this).text())
  });

  //选择编号弹窗
  // $('.choose-brand-box').show();
  $('.choose-brand-box').on('click','.confirm',function(){
    $('.choose-brand-box').hide();
  });

  //导航轮播
  var swiper4 = new Swiper('.swiper-container4', {
    spaceBetween: 30,
    centeredSlides: true,
    slidesPerView: 1,
    autoplay: {
      delay: 2500,
      disableOnInteraction: false,
    },
    pagination: {
      el: '.swiper-pagination',
      clickable: true,
    },
  });
  function schoolPost(params){
    $.post('/Index/SwitchSchool', {
      school_id:params
    }, function (res) {
      console.log(res)

      if (res.error == '0') {

      } else {
    
      }
  
    }, 'json');
  }

})