require.config({
  baseUrl: '/Work/View/Tephone/js/',
  paths: {
    jquery: 'lib/jquery',
    rem: 'lib/int.rem.min',
    echarts: 'lib/echarts.min'
  }
})
require(['jquery', 'rem', 'common', 'goIndex', 'echarts'], function ($, rem, common, goIndex, echarts) {
  //意向星级
  var dom = document.getElementById("trace-0");
  var myChart = echarts.init(dom);
  var app = {};
  option = null;
  var weatherIcons = {
    'Sunny': '/Work/View/Tephone/images/myLove-cur.png',
  };
console.log(weatherIcons.Sunny,11111)
var arrs=[
  {name:'一颗星',value:20,num:0},
  {name:'二颗星',value:12,num:1},
  {name:'三颗星',value:2,num:3},
  {name:'四颗星',value:12,num:4},
  {name:'五颗星',value:12,num:5}
];
console.log(arrs[0].name)
  option = {
    tooltip: {
      trigger: 'item',
      formatter: "{a} <br/>{b} : {c} ({d}%)"
    },
    color: [
      '#ECF1FD', '#D2DFFF','#ACC4FF','#8BACFF','#446FDC'
    ],
    series: [{
      type: 'pie',
      // radius: '60%',
      radius: ['40%', '56%'],
      center: ['50%', '50%'],
      selectedMode: 'single',
      data: [{
          value: arrs[0].value,
          name: arrs[0].name,
          label: {
            normal: {
              formatter: [
                '{Sunny|}',
              ].join('\n'),
              // formatter:function(params){ 
               
              //     str = '{Sunny|}'
              //     return str
              // },
              rich: {
                Sunny: {
                  height: 15,
                  align: 'left',
                  backgroundColor: {
                    image: weatherIcons.Sunny
                  }
                },
                value: {
                  width: 20,
                  padding: [0, 20, 0, 30],
                  align: 'left'
                },
              }
            }
          }
        },
        {
          value: arrs[1].value,
          name: arrs[1].name,
          label: {
            normal: {
              formatter: [
                '{Sunny|} {Sunny|}',
              ].join('\n'),
              rich: {
                Sunny: {
                  height: 15,
                  align: 'left',
                  backgroundColor: {
                    image: weatherIcons.Sunny
                  }
                },
                value: {
                  width: 20,
                  padding: [0, 20, 0, 30],
                  align: 'left'
                },
              }
            }
          }
        },
        {
          value: arrs[2].value,
          name: arrs[2].name,
          label: {
            normal: {
              formatter: [
                '{Sunny|} {Sunny|} {Sunny|}',
              ].join('\n'),
              rich: {
                Sunny: {
                  height: 15,
                  align: 'left',
                  backgroundColor: {
                    image: weatherIcons.Sunny
                  }
                },
                value: {
                  width: 20,
                  padding: [0, 20, 0, 30],
                  align: 'left'
                },
              }
            }
          }
        },
        {
          value: arrs[3].value,
          name: arrs[3].name,
          label: {
            normal: {
              formatter: [
                '{Sunny|} {Sunny|} {Sunny|} {Sunny|}',
              ].join('\n'),
              rich: {
                Sunny: {
                  height: 15,
                  align: 'left',
                  backgroundColor: {
                    image: weatherIcons.Sunny
                  }
                },
                value: {
                  width: 20,
                  padding: [0, 20, 0, 30],
                  align: 'left'
                },
              }
            }
          }
        },
        {
          value: arrs[4].value,
          name: arrs[4].name,
          label: {
            normal: {
              formatter: [
                '{Sunny|} {Sunny|} {Sunny|} {Sunny|} {Sunny|}',
              ].join('\n'),
              rich: {
                Sunny: {
                  height: 15,
                  align: 'left',
                  backgroundColor: {
                    image: weatherIcons.Sunny
                  }
                },
                value: {
                  width: 20,
                  padding: [0, 20, 0, 30],
                  align: 'left'
                },
              }
            }
          }
        }
      ],
      itemStyle: {
        emphasis: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      },
    }]
  };;
  if (option && typeof option === "object") {
    myChart.setOption(option, true);
  }

  //招生转化率
  var dom1 = document.getElementById("trace-1");
  var myChart1 = echarts.init(dom1);
  var app = {};
  option1 = null;

  option1 = {

    tooltip: {
        trigger: 'axis',
        axisPointer: {
            type: 'shadow'
        },
        formatter: "{a} <br/>{b}: {c}"
    },
    itemStyle:{
      normal: {
        color: function(params) {
            var colorList = [
              '#ECF1FD', '#D2DFFF','#ACC4FF','#8BACFF','#446FDC'
            ];
            return colorList[params.dataIndex]
        },
      }
    },
    grid: {
        left: '0%',
        top:'5%',
        bottom: '3%',
        containLabel: true,
        borderWidth:0,
    },
    xAxis: {      
       type: 'value',
       show:false,
       boundaryGap:true,
       axisLabel:{
           margin:0
       },
       splitLine:{
           show:false,  //不显示分割线         
       }, 
    },
    yAxis: {
       type: 'category',
       boundaryGap:false,
       axisTick:{
           show:false
       },
       // y 轴线
       axisLine:{
           show:false,        
       },  
       axisLabel:{

       },
        data: ['1招生名单搜索','2客户','3邀约名单','4试听客户','5报名客户',],
    },
    series: [
        {
            name: '2011年',
            type: 'bar',
            data: [100, 200, 300, 400, 500]
        },
    ]
};
  if (option1 && typeof option1 === "object") {
      myChart1.setOption(option1, true);
  }
})