require.config({
  baseUrl: '/Work/View/Tephone/js/',
  paths:{
      jquery: 'lib/jquery',
      rem: 'lib/int.rem.min',
  },
})
require([
  'jquery',
  'rem',
  'common',
  './utils/dot-render',
], function($,rem,common,dotRender) {
  var startTime='';
  var endTime='';

  //获取今日时间
  function getDate(day) {
    var date1 = new Date(),
        time1 =
        date1.getFullYear() +
        "-" +
        (date1.getMonth() + 1) +
        "-" +
        date1.getDate(); //time1表示当前时间
    var date2 = new Date(date1);
    date2.setDate(date1.getDate() + day);

    var year = date2.getFullYear();
    var month = date2.getMonth() + 1;
    var strDate = date2.getDate();
    if (month >= 1 && month <= 9) {
        month = "0" + month;
    }
    if (strDate >= 0 && strDate <= 9) {
        strDate = "0" + strDate;
    }
    var time2 =
        year +
        "-" +
        month +
        "-" +
        strDate;
    console.log(time1, time2);
    return time2;
}
  //获取本周时间
  //获取当前日期yy-mm-dd
    //date 为时间对象
    function getDateStr3(date) {
      var year = "";
      var month = "";
      var day = "";
      var now = date;
      year = "" + now.getFullYear();
      if ((now.getMonth() + 1) < 10) {
          month = "0" + (now.getMonth() + 1);
      } else {
          month = "" + (now.getMonth() + 1);
      }
      if ((now.getDate()) < 10) {
          day = "0" + (now.getDate());
      } else {
          day = "" + (now.getDate());
      }
      return year + "-" + month + "-" + day;
  }
    // 获取星期
    function getWeek() {
      var week = new Date().getDay();
      return week;
    }

    // 时间格式以及周几设置
    function getTime(date) {
      let seperator1 = "-";
      let year = date.getFullYear();
      let month = date.getMonth() + 1;
      let strDate = date.getDate();
      if (month >= 1 && month <= 9) {
          month = "0" + month;
      }
      if (strDate >= 0 && strDate <= 9) {
          strDate = "0" + strDate;
      }
      let currentdate = year + seperator1 + month + seperator1 + strDate;
      return currentdate;
  }
   /** 
     * 获得相对当前周AddWeekCount个周的起止日期 
     * AddWeekCount为0代表当前周 为-1代表上一个周   为1代表下一个周以此类推
     * 
     **/
    function getWeekStartAndEnd(AddWeekCount) {
      //起止日期数组   
      var startStop = new Array();
      //一天的毫秒数   
      var millisecond = 1000 * 60 * 60 * 24;
      //获取当前时间   
      var currentDate = new Date();
      //相对于当前日期AddWeekCount个周的日期
      currentDate = new Date(currentDate.getTime() + (millisecond * 7 * AddWeekCount));
      //返回date是一周中的某一天
      var week = currentDate.getDay();
      //返回date是一个月中的某一天   
      var month = currentDate.getDate();
      console.log(month)
          //减去的天数   
      var minusDay = week != 0 ? week - 1 : 6;
      //获得当前周的第一天   
      var currentWeekFirstDay = new Date(currentDate.getTime() - (millisecond * minusDay));
      //获得当前周的最后一天
      var currentWeekLastDay = new Date(currentWeekFirstDay.getTime() + (millisecond * 6));
      //添加至数组   
      startStop.push(getDateStr3(currentWeekFirstDay));
      startStop.push(getDateStr3(currentWeekLastDay));

      return startStop;
  }
  function getMonthStartAndEnd(AddMonthCount) {
    //起止日期数组   
    var startStop = new Array();
    //获取当前时间   
    var currentDate = new Date();
    var month = currentDate.getMonth() + AddMonthCount;
    if (month < 0) {
        var n = parseInt((-month) / 12);
        month += n * 12;
        currentDate.setFullYear(currentDate.getFullYear() - n);
    }
    currentDate = new Date(currentDate.setMonth(month));
    //获得当前月份0-11   
    var currentMonth = currentDate.getMonth();
    //获得当前年份4位年   
    var currentYear = currentDate.getFullYear();
    //获得上一个月的第一天   
    var currentMonthFirstDay = new Date(currentYear, currentMonth, 1);
    //获得上一月的最后一天   
    var currentMonthLastDay = new Date(currentYear, currentMonth + 1, 0);
    //添加至数组   
    startStop.push(getDateStr3(currentMonthFirstDay));
    startStop.push(getDateStr3(currentMonthLastDay));
    //返回   
    return startStop;
}

  //头部的下拉列表
  $('.byDay-box .select').on('click','.title',function(){
    var $t = $(this);
    var optionList = $t.parents('.byDay-box').find('.option');
    if(optionList.is(':hidden')){
      optionList.show();
      $('.cover-props').show();
    }else{
      optionList.hide();
      $('.cover-props').hide();
    }
  });

  $('.byDay-box .select .option').on('click','li',function(){
    var $t = $(this);
    var index = $t.index();
    var val = $t.text();
    var title = $t.parents('.byDay-box').find('.title');
    var liList = $('.byDay-box .select .option li');
    title.text(val);
    $t.parents('.option').hide();
    $('.cover-props').hide();
    liList.removeClass('cur').eq(index).addClass('cur');
    if($t.index()==0){
       startTime='';
       endTime='';
       getIntention();
    }else if($t.index()==1){
      startTime=getDate(0);
       endTime=getDate(0);
       getIntention();
    }else if($t.index()==2){
      startTime=getWeekStartAndEnd(0)[0];
       endTime=getWeekStartAndEnd(0)[1];
       getIntention();
    }else if($t.index()==3){
      startTime=getMonthStartAndEnd(0)[0];
       endTime=getMonthStartAndEnd(0)[1];
       getIntention();
    }
  });
  //点击开始与结束时间
  $(document).on('click','.rolldate-panel .rolldate-confirm',function () {
    startTime=$('#date-group1-1').val();
    endTime=$('#date-group1-2').val();
    getIntention(1,$('#date-group1-1').val(),$('#date-group1-2').val());
  })


  //编辑
  $(document).on('touchend','.byDay-follow .item .edit',function(){
    var $t = $(this);
    var index = $t.index();
    var reason= $t.parents('.item').find('.follow p').text();
    var order = $t.parents('.item').attr('data-order');
    var trackId = $t.parents('.item').attr('data-trackId');
    $('.SpringFestival-prop .content-box .area').val(reason)
    $('.SpringFestival-prop').show();
    $('.SpringFestival-prop').attr('data-orderProp',order)
    $('.SpringFestival-prop').attr('data-trackId',trackId)
  });
  //修改沟通内容保存-提交
  $('.SpringFestival-prop .hot-box').on('touchend','.save',function(){
    //修改之后的值
    var propVlue= $(this).parents('.SpringFestival-prop').find('.content-box .area').val();
    var orderProp = $(this).parents('.SpringFestival-prop').attr('data-orderProp');
    $('.byDay-follow .item').eq(orderProp).find('.follow p').text(propVlue);
    $('.SpringFestival-prop').hide();

    $.post('/Intention/editTrackNote', {
      staffer_id: JSON.parse(localStorage.getItem('stafferId')),
      school_id : JSON.parse(localStorage.getItem('setBrand')),
      company_id:JSON.parse(localStorage.getItem('companyId')),
      track_id:$('.SpringFestival-prop').attr('data-trackId'),
      track_note:$('.SpringFestival-prop .content-box .area').val(),
    }, function (res) {
      console.log(res)
      results.list = results.list.concat(res.result.list);
      console.log(results,222222222222)
      if (res.error == '0') {
        dotRender('#allNumber', results, '#popMto');
      } else {
    
      }
  
    }, 'json');

  });
  //修改沟通内容取消
  $('.SpringFestival-prop .hot-box').on('touchend','.cancle',function(){
    $('.SpringFestival-prop').hide();
  });



  //跟进记录-接口
  // getIntention(1,$('#date-group1-1').val(),$('#date-group1-2').val());

  //滑动加载
  mui.init({
    pullRefresh: {
      container: '#pullrefresh',
      down: {
        callback: pulldownRefresh
      },
      up: {
        contentrefresh: '正在加载...',
        callback: pullupRefresh
      }
    }
  });
  /**
   * 下拉刷新具体业务实现
   */
  var results=[];
  results.list = [];
  // results.clocking = {};
  var page=0;
  function pulldownRefresh() {
    page=1;
    results.list=[];
    // results.clocking = {};
    setTimeout(function() {
      var table = document.body.querySelector('.mui-table-view');
      var cells = document.body.querySelectorAll('.mui-table-view-cell');
      
      // getIntention(page,$('#date-group1-1').val(),$('#date-group1-2').val())

      mui('#pullrefresh').pullRefresh().endPulldownToRefresh(); //refresh completed
      mui('#pullrefresh').pullRefresh().refresh(true);
    }, 1500);
  }
  var count = 0;
  /**
   * 上拉加载具体业务实现
   */
  function pullupRefresh() {
    page++;
    let arr = 0;

    // getIntention(page,$('#date-group1-1').val(),$('#date-group1-2').val()).then(res=>{
    //   // console.log('接收到结果为：'+res);
    //   arr = res;
    // })
    setTimeout(function() {
      mui('#pullrefresh').pullRefresh().endPullupToRefresh((arr<10)); //参数为true代表没有更多数据了。
      var table = document.body.querySelector('.mui-table-view');
      var cells = document.body.querySelectorAll('.mui-table-view-cell');
      // getIntention(page,$('#date-group1-1').val(),$('#date-group1-2').val())

    }, 1500);
  }
  if (mui.os.plus) {
    mui.plusReady(function() {
      setTimeout(function() {
        mui('#pullrefresh').pullRefresh().pullupLoading();
      }, 1000);

    });
  } else {
    mui.ready(function() {
      mui('#pullrefresh').pullRefresh().pullupLoading();
    });
  };

  //此页暂时不需要滑动加载
  getIntention();

  function getIntention(page,startTime,endTime){
    return new Promise(function(resolve){
      $.get('/Intention/clientOneTrack', {
        staffer_id: JSON.parse(localStorage.getItem('stafferId')),
        school_id : JSON.parse(localStorage.getItem('setBrand')),
        company_id:JSON.parse(localStorage.getItem('companyId')),
        client_id:$('.byDay-box').attr('data-clientid'),
        start_day:startTime,
        end_day:endTime,
        p:page,
        num:10,
      }, function (res) {
        console.log(res)
        // resolve(res.result.list.length);
        results.list = results.list.concat(res.result.list);
        console.log(results,222222222222)
        if (res.error == '0') {
          dotRender('#allNumber', results, '#popMto');
        } else {
      
        }

      }, 'json');
    });
  }

  mui('body').on('tap','a',function(){document.location.href=this.href;});
  mui('body').on('click','a',function(){document.location.href=this.href;});
})
