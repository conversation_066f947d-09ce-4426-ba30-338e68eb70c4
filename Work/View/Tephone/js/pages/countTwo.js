require.config({
  baseUrl: '/Work/View/Tephone/js/',
  paths:{
      jquery: 'lib/jquery',
      rem: 'lib/int.rem.min',
      echarts: 'lib/echarts.min'
  }
})
require(['jquery','rem','common','goIndex','echarts'], function($,rem,common,goIndex,echarts) {
  // 跟踪率
  function mkopt(data,index) {
    var app = {};
    option = null;
    app.title = '环形图';
    return option = {
      tooltip: {
        trigger: 'item',
        formatter: "{a} <br/>{b} : {c} ({d}%)",
        position: function (p) { //其中p为当前鼠标的位置
          return [p[0] - 200, p[1] - 10];
        },
      },
      calculable: true,
      color: [
        '#2E64ED', '#FFCC02'
      ],
      series: [{
        name:'跟踪率',
        type:'pie',
        radius :'80%',
        
        roseType : 'radius',
        label: {
            normal: {
                show: false
            },
            emphasis: {
                show: false
            }
        },
        lableLine: {
            normal: {
                show: false
            },
            emphasis: {
                show: false
            }
        },
        data: data
      }]
    };
  }
  //报名率
  function mkopt1(data,index) {
    var app = {};
    option = null;
    app.title = '环形图';
    return option = {
      tooltip: {
        trigger: 'item',
        formatter: "{a} <br/>{b} : {c} ({d}%)",
        position: function (p) { //其中p为当前鼠标的位置
          return [p[0] - 200, p[1] - 10];
        },
      },
      calculable: true,
      color: [
        '#39DEB9', '#9013FE'
      ],
      series: [{
        name:'跟踪率',
        type:'pie',
        radius :'80%',
        
        roseType : 'radius',
        label: {
            normal: {
                show: false
            },
            emphasis: {
                show: false
            }
        },
        lableLine: {
            normal: {
                show: false
            },
            emphasis: {
                show: false
            }
        },
        data: data
      }]
    };
  }
  var lists = $('.track-box .item');
    lists.each(function(index,element){
      // 跟踪率
      var objs={};
      var arrs=[];
      var dom = document.getElementById("trace-"+index);
      var myChart = echarts.init(dom);
      var data =[{
        'value':lists.eq(index).data('correctnums'),
        'name':'正确'
        },{
          'value':lists.eq(index).data('wrongnums'),
          'name':'错误'
        }
      ]
      myChart.setOption(mkopt(data), true);
      //报名率 
      var objs1={};
      var arrs1=[];
      var dom1 = document.getElementById("sign-"+index);
      var myChart1 = echarts.init(dom1);
      var data1 =[{
        'value':lists.eq(index).data('correctnums'),
        'name':'正确'
        },{
          'value':lists.eq(index).data('wrongnums'),
          'name':'错误'
        }
      ]
      myChart1.setOption(mkopt1(data1), true);
    });
})