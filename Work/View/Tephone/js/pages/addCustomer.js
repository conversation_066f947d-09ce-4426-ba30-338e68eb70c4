require.config({
  baseUrl: '/Work/View/Tephone/js/',
  paths:{
      jquery: 'lib/jquery',
      rem: 'lib/int.rem.min',
      swiper: 'lib/swiper.min',
  }
})
require([
  'jquery',
  'rem',
  'swiper',
  'common',
  'goIndex',
  './utils/dot-render',
  'res-tip'
], function($,rem,swiper,common,goIndex,dotRender,resTip) {
  //意向星级
  var loveLength=0;
  $('.addRecord-box .loveLevel .Love-box').on('click','.love',function () {
    var $t = $(this);
    var index = $t.index();
    var loveList = $('.addRecord-box .loveLevel .Love-box .love');
    if($t.hasClass('cur')){
      loveList.removeClass('cur');
    }else{
      $t.addClass('cur');
      $t.prevAll().addClass('cur');
    }
    loveLength = $('.addRecord-box .loveLevel .Love-box .love.cur').length;
    console.log(loveLength)
  })

  //意向客户
  //点击增加
  var addFlag=0;
  var addLength=1;
  $('.basicInfo').on('click','.addInfo',function(){
    addFlag++;
    console.log(addFlag)
    var html = '<div class="contact" data-order="'+addFlag+'">'+
                '<div class="choose-ipt mb20">'+
                    '<p class="type-title start">亲属关系</p>'+
                    '<input type="text" class="ipt specileId" id="order-'+addFlag+'" data-indexId="'+addFlag+'" name=""  placeholder="请选择" readonly>'+
                '</div>'+
                '<div class="choose-ipt mb20">'+
                    '<p class="type-title start">亲属姓名</p>'+
                    '<input type="text" class="ipt relativeName" name="" id="" placeholder="请填写">'+
                '</div>'+
                '<div class="choose-ipt mb20">'+
                    '<p class="type-title start">请输入联系电话</p>'+
                    '<input type="text" class="ipt addphone" name="" id="" placeholder="请填写">'+
                '</div>'+
                '<div class="main-contact">'+
                    '<div class="exchange">'+
                        '<input type="radio" class="radio" name="mainPerson">'+
                        '<label for="">设置为主要联系人</label>'+
                    '</div>'+
                    '<div class="delete"></div>'+
                '</div>'+
            '</div>';
    // console.log(html)
    console.log($('.contact').html())
    // $(html).appendTo($('.contact-box'))
    $('.contact-box').append(html);
  })
  //点击删除
  $('.contact-box').on('click',' .contact .main-contact .delete',function(){
    $(this).parents('.contact').remove()
  })
  //性别选择 
  $('.addRecord-box .choose-ipt').on('click','#scrollProp1',function(){
    $('.swiper-box-prop1').show();
    var swiper1 = new Swiper('.swiper-box-prop1 .swiper-container', {
        direction: 'vertical',
          grabCursor: true,
          effect: 'coverflow',
          slidesPerView: 4,
          centeredSlides: true,
          coverflowEffect: {
            rotate: 0,
            stretch: 0,
            depth: 300,
            modifier: 1,
            slideShadows: false
          }
      });
  })
  var swiperValue1='';
  $('.swiper-box-prop1').on('click','.confirm',function(e){
    e.stopPropagation();
    $('.swiper-box-prop1').hide();
    swiperValue1 = $('.swiper-box-prop1 .swiper-container .swiper-slide.swiper-slide-active').text();
    console.log(swiperValue1)
    $('#scrollProp1').val(swiperValue1);
  });
  //亲属关系选折
  $(document).on('click','.addRecord-box .choose-ipt .specileId',function(){
    $('.order-prop').show();
    var swiper1 = new Swiper('.order-prop .swiper-container', {
      direction: 'vertical',
        grabCursor: true,
        effect: 'coverflow',
        slidesPerView: 4,
        centeredSlides: true,
        coverflowEffect: {
          rotate: 0,
          stretch: 0,
          depth: 300,
          modifier: 1,
          slideShadows: false
        }
    });
    $(this).attr('data-indexId');
    $('.order-prop').attr('data-propId',$(this).attr('data-indexId'))

    $('.contact-box .contact').removeClass('addContact');
    $(this).parents('.contact').addClass('addContact')

  })
  var orderValue2='';

  $('.order-prop').on('click','.confirm',function(e){
    e.stopPropagation();
    $('.order-prop').hide();
    orderValue2 = $('.order-prop .swiper-container .swiper-slide.swiper-slide-active').text();
    console.log(orderValue2)
    // $('#scrollProp1').val(orderValue2);
    console.log($(this).parents('.order-prop'),'1111')
    console.log($(this).parents('.order-prop').attr('data-propId'), '2222')
    var data = $(this).parents('.order-prop').attr('data-propId');
    // $('.addRecord-box .choose-ipt .specileId').eq(data).val(orderValue2);
    // $('#order-'+addFlag+'').val(orderValue2)
    $('.addRecord-box .addContact .choose-ipt .specileId').val(orderValue2)

  });
  //招生来源 
  $('.addRecord-box .choose-ipt').on('click','#scrollProp2',function(){
    $('.swiper-box-prop2').show();
    var swiper2 = new Swiper('.swiper-box-prop2 .swiper-container', {
      direction: 'vertical',
        grabCursor: true,
        effect: 'coverflow',
        slidesPerView: 4,
        centeredSlides: true,
        coverflowEffect: {
          rotate: 0,
          stretch: 0,
          depth: 300,
          modifier: 1,
          slideShadows: false
        }
    });
    //清空  渠道明细 的显示 及选中的id
    $('#scrollProp2-1').val('');
     swiperValue21Id = ''; //选中的渠道明细的id
  });

  var swiperValue2='';
  $('.swiper-box-prop2').on('click','.confirm',function(e){
    e.stopPropagation();
    $('.swiper-box-prop2').hide();
    swiperValue2 = $('.swiper-box-prop2 .swiper-container .swiper-slide.swiper-slide-active').text();
    console.log(swiperValue2)
    $('#scrollProp2').val(swiperValue2);
  });

  //介绍人 
  // $('.addRecord-box .choose-ipt').on('click','#scrollProp3',function(){
  //   $('.swiper-box-prop3').show();
  //   var swiper3 = new Swiper('.swiper-box-prop3 .swiper-container', {
  //       direction: 'vertical',
  //         grabCursor: true,
  //         effect: 'coverflow',
  //         slidesPerView: 4,
  //         centeredSlides: true,
  //         coverflowEffect: {
  //           rotate: 0,
  //           stretch: 0,
  //           depth: 300,
  //           modifier: 1,
  //           slideShadows: false
  //         }
  //     });
  // })
  // var $('#')='';
  // $('.swiper-box-prop3').on('click','.confirm',function(e){
  //   e.stopPropagation();
  //   $('.swiper-box-prop3').hide();
  //   $('#') = $('.swiper-box-prop3 .swiper-container .swiper-slide.swiper-slide-active').text();
  //   console.log($('#'))
  //   $('#scrollProp3').val($('#'));
  // });

   //渠道明细-点击 
   $('.addRecord-box .choose-ipt').on('click', '#scrollProp2-1', function () {
    
     $('.swiper-box-prop2-1').show();
     var swiper2 = new Swiper('.swiper-box-prop2-1 .swiper-container', {
       direction: 'vertical',
       grabCursor: true,
       effect: 'coverflow',
       slidesPerView: 4,
       centeredSlides: true,
       coverflowEffect: {
         rotate: 0,
         stretch: 0,
         depth: 300,
         modifier: 1,
         slideShadows: false
       }
     });

   });
  getAuditionData();
   function getAuditionData() {
     console.log(swiperValue2, '我是渠道类型')
     var results = [];
     $.get('/Api/getChannel', {
       staffer_id: JSON.parse(localStorage.getItem('stafferId')),
       school_id: JSON.parse(localStorage.getItem('setBrand')),
       company_id: JSON.parse(localStorage.getItem('companyId')),
       channel_medianame: swiperValue2, //	渠道类型
     }, function (res) {
       console.log(res)
       if (res.error == '0') {
         results.list = res.result.list;
         dotRender('#allNumber', results, '#popMto');
       } else {
         dotRender('#allNumber', '', '#popMto');
       }

     }, 'json');
   }
   var swiperValue21 = '';
   var swiperValue21Id = '';//选中的渠道明细的id
   //渠道明细-点击-确定
   $('.swiper-box-prop2-1').on('click', '.confirm', function (e) {
     e.stopPropagation();
     $('.swiper-box-prop2-1').hide();
     swiperValue21 = $('.swiper-box-prop2-1 .swiper-container .swiper-slide.swiper-slide-active').text();
     swiperValue21Id = $('.swiper-box-prop2-1 .swiper-container .swiper-slide.swiper-slide-active').attr('data-channelid');
     console.log(swiperValue21)
     $('#scrollProp2-1').val(swiperValue21);
   });

  //意向课程
  $('.addRecord-box .choose-ipt').on('click','#scrollProp4',function(){
    $('.swiper-box-prop4').show();
    var swiper4 = new Swiper('.swiper-box-prop4 .swiper-container', {
        direction: 'vertical',
          grabCursor: true,
          effect: 'coverflow',
          slidesPerView: 4,
          centeredSlides: true,
          coverflowEffect: {
            rotate: 0,
            stretch: 0,
            depth: 300,
            modifier: 1,
            slideShadows: false
          }
      });
  })
  var swiperValue4='';
  var loveIdValue='';//意向课程id
  $('.swiper-box-prop4').on('click','.confirm',function(e){
    e.stopPropagation();
    $('.swiper-box-prop4').hide();
    swiperValue4 = $('.swiper-box-prop4 .swiper-container .swiper-slide.swiper-slide-active').text();
    loveIdValue = $('.swiper-box-prop4 .swiper-container .swiper-slide.swiper-slide-active').attr('data-coursecatid');
    console.log(swiperValue4)
    $('#scrollProp4').val(swiperValue4);
  });

  //提交
  $('.review-footer-box .footer').on('click','.btn',function(){
    // console.log(swiperValue21Id,'dada')
    var addArrs=[];//亲属关系 数组-变量待定
    var radioVlue='';
    $('.contact-box .contact').each(function(index,element){
      console.log($(this).find('.main-contact input[name=mainPerson]'))
      if($(this).find('.main-contact input[name=mainPerson]').is(':checked')){
        radioVlue = 1;
      }else{
        radioVlue = 0;
      }
      var obj={
        family_relation:$(this).find('.specileId').val(),
        family_cnname:$(this).find('.relativeName').val(),
        family_mobile:$(this).find('.addphone').val(),
        family_isdefault:radioVlue,//是否为主要联系人，1是主要，0是非主要
      }
      addArrs.push(obj)
      console.log($(this).find('.specileId'))
    })
    var loveIdArr = [];//意向课程id
    //意向课程id
    var loveObj={
      course_id:loveIdValue,
    }
    loveIdArr.push(loveObj)
    console.log(addArrs,99999)

    $.post('/Intention/addIntention', {
      staffer_id: JSON.parse(localStorage.getItem('stafferId')),
      school_id : JSON.parse(localStorage.getItem('setBrand')),
      company_id:JSON.parse(localStorage.getItem('companyId')),
      client_cnname:$('#name').val(),//姓名
      client_mobile:$('#phone').val(),//手机号
      client_sex:swiperValue1,//性别
      client_birthday:$('#date-group1-1').val(),//生日
      client_family_list:addArrs,//亲属关系 数组-变量待定
      client_source:swiperValue2,//来源
      client_intention_level:loveLength,//意向等级
      client_sponsor :$('#scrollProp3').val(),//客户介绍人
      intention_course:loveIdArr,//意向课程id
      client_remark:$('.area-box #area').val(),//备注信息
      channel_id: swiperValue21Id,//渠道明细id
    }, function (res) {
      console.log(res)
      if (res.error == '0') {
        window.location = '/Intention/Home';
        resTip(res.errortip, false);
      } else {
        resTip(res.errortip, true);
      }
  
    }, 'json');
  })

  
    
})