require.config({
  baseUrl: '/Work/View/Tephone/js/',
  paths: {
    jquery: 'lib/jquery',
    rem: 'lib/int.rem.min'
  }
})
require([
  'jquery',
   'rem', 
   'common',
   './utils/dot-render',
   'res-tip'
  ], function ($, rem, common,dotRender,resTip) {
  $('.schoolAnnounce-lists').on('tap','.items .i-first',function(){
    var $t = $(this);
    $('.announce-pop').show();
    $('.cover-props').show();
    // dotRender('#taskDetail', '', '#popMto1');
    getNotice($t.attr('data-noticeId'));
    
  });
  //关闭弹窗
  $('.announce-pop').on('touchend','.close',function(){
    $('.announce-pop').hide();
    $('.cover-props').hide();
  })

  //滑动加载
  mui.init({
    pullRefresh: {
      container: '#pullrefresh',
      down: {
        callback: pulldownRefresh
      },
      up: {
        contentrefresh: '正在加载...',
        callback: pullupRefresh
      }
    }
  });
  /**
   * 下拉刷新具体业务实现
   */
  var results=[];
  results.list = [];
  results.mescount = {};
  var page=0;
  function pulldownRefresh() {
    page=1;
    results.list=[];
    res.result.mescount = {};
    setTimeout(function() {
      var table = document.body.querySelector('.mui-table-view');
      var cells = document.body.querySelectorAll('.mui-table-view-cell');
      
      // getIntention(page)

      mui('#pullrefresh').pullRefresh().endPulldownToRefresh(); //refresh completed
      mui('#pullrefresh').pullRefresh().refresh(true);
    }, 1500);
  }
  var count = 0;
  /**
   * 上拉加载具体业务实现
   */
  function pullupRefresh() {
    page++;
    let arr = 0;

    // getIntention(page).then(res=>{
    //   // console.log('接收到结果为：'+res);
    //   arr = res;
    // })
    setTimeout(function() {
      mui('#pullrefresh').pullRefresh().endPullupToRefresh((arr<10)); //参数为true代表没有更多数据了。
      var table = document.body.querySelector('.mui-table-view');
      var cells = document.body.querySelectorAll('.mui-table-view-cell');

    }, 1500);
  }
  if (mui.os.plus) {
    mui.plusReady(function() {
      setTimeout(function() {
        mui('#pullrefresh').pullRefresh().pullupLoading();
      }, 1000);

    });
  } else {
    mui.ready(function() {
      mui('#pullrefresh').pullRefresh().pullupLoading();
    });
  };

  //此页暂时不需要滑动加载
  getIntention();

  function getIntention(page){
    return new Promise(function(resolve){
      $.get('/Notice/SchoolNotice', {
        staffer_id: JSON.parse(localStorage.getItem('stafferId')),
        school_id : JSON.parse(localStorage.getItem('setBrand')),
        company_id:JSON.parse(localStorage.getItem('companyId')),
        // p:page,
        // num:10,
      }, function (res) {
        console.log(res)
        if (res.error == '0') {
          // resolve(res.result.data);
          results.list = results.list.concat(res.result.data);
          results.mescount=res.result.mescount;
          console.log(results)
          dotRender('#allNumber', results, '#popMto');
        } else {
          // resTip(res.errortip, true);
        }
    
      }, 'json');
    });
  }
  mui('body').on('tap','a',function(){document.location.href=this.href;});
  mui('body').on('click','a',function(){document.location.href=this.href;});
 

  //学校公告弹窗data

  function getNotice(noticeId){
    var resultsNotice=[];
    $.get('/Notice/NoticeOne', {
      staffer_id: JSON.parse(localStorage.getItem('stafferId')),
      school_id : JSON.parse(localStorage.getItem('setBrand')),
      company_id:JSON.parse(localStorage.getItem('companyId')),
      notice_id:noticeId,
    }, function (res) {
      console.log(res)
      dotRender('#taskDetail', '', '#popMto1');
      if (res.error == '0') {
        resultsNotice.list=res.result.data;
        // results.list = results.list.concat(res.result.data);
        console.log(results)
        dotRender('#taskDetail', resultsNotice, '#popMto1');
      } else {
        resTip(res.errortip, true);
      }
  
    }, 'json');
  }

})