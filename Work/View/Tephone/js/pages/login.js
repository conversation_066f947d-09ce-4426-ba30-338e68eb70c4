require.config({
  baseUrl:  '/Work/View/Tephone/js/',
  paths:{
      jquery: 'lib/jquery',
      rem: 'lib/int.rem.min' 
  }
})
require(['jquery','rem','res-tip'], function($,rem,resTip) {
  //tab切换
  $('.account-box ul').on('click','.tent',function(){
    var $list= $('.account-list .list');
    var $num= $(this).index();
    $('.account-box ul .tent').removeClass('cur').eq($num).addClass('cur').end();
    $list.removeClass('active').eq($num).addClass('active').end();
  });

  //账号登录
  var $form = $('#loginform'),
  form = $form[0];

  $form.submit(function (e) {
    e.preventDefault();
      $.post('/Api/pswdlogin', {
      L_code:form.gmcBrand.value,//	企业编号
      L_name:form.usr.value,//	职工编号/手机号码
      L_pswd:form.password.value//	用户密码
    }, function (res) {
      console.log(res)
      if (res.error == '0') {
        resTip(res.errortip, false, function () {
          window.location = '/Index/Post'
        });
      } else {
        $('#loginform .tips').text(res.errortip);
      }
    }, 'json');
  });

   //60s倒计时
   $('.phone-login #phone-form .verification-code').on('click', '.btn', function () {
    var obj = $(this);
    var brand = $(this).parents('.phone-login').find('#gmcBrand').val();
    var phoneNmb = $('.phone-login #phone-form #phone').val();
    $.get('https://wwwapi.kedingdang.com/Login/getverifycodeApi', {
      L_code: brand,//	企业授权码
      L_mobile:phoneNmb//手机号码
      }, function (res) {
        console.log(res)
        if (res.error == 0) {
          resTip(res.errortip, false);
          settime(obj);
        }else{
          resTip(res.errortip, true);
        }

      },
      'json');
  });
  var countdown = 60;

  function settime(obj) { //发送验证码倒计时
    if (countdown == 0) {
      obj.attr('disabled', false);
      //obj.removeattr("disabled"); 
      obj.text("发送验证码");
      countdown = 60;
      obj.removeClass('cur');
      return;
    } else {
      obj.attr('disabled', true);
      obj.addClass('cur');
      obj.text("(" + countdown + "s)");
      countdown--;
    }
    setTimeout(function () {
      settime(obj)
    }, 1000)
  }

  //手机号快速登录
  var $formLogin = $('.phone-login #phone-form');
  $formLogin.submit(function (e) {
    e.preventDefault();
    var brand = $(this).parents('.phone-login').find('#gmcBrand').val();
    var phoneNmb = $('.phone-login #phone-form #phone').val();
    var verifycode = $('.phone-login #phone-form #verification').val();
    $.post('/Api/mobilelogin', {
      L_code: brand,//	企业授权码
      L_mobile:phoneNmb,//手机号码
      L_verifycode:verifycode//验证码
    }, function (res) {
      if (res.error == '0') {
        resTip(res.errortip, false, function () {
          window.location = '/Index/Post';
        });
      } else {
        resTip(res.errortip, true);
      }
    }, 'json');
  })
})