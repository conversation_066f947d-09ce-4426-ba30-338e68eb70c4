require.config({
  baseUrl: '/Work/View/Tephone/js/',
  paths:{
      jquery: 'lib/jquery',
      rem: 'lib/int.rem.min',
  }
})
require([
  'jquery',
  'rem',
  'common',
  './utils/dot-render',
], function($,rem,common,dotRender) {
  //获取当前日期yy-mm-dd
    //date 为时间对象
    function getDateStr3(date) {
      var year = "";
      var month = "";
      var day = "";
      var now = date;
      year = "" + now.getFullYear();
      if ((now.getMonth() + 1) < 10) {
          month = "0" + (now.getMonth() + 1);
      } else {
          month = "" + (now.getMonth() + 1);
      }
      if ((now.getDate()) < 10) {
          day = "0" + (now.getDate());
      } else {
          day = "" + (now.getDate());
      }
      return year + "-" + month + "-" + day;
  }
    // 获取星期
    function getWeek() {
      var week = new Date().getDay();
      return week;
    }

    // 时间格式以及周几设置
    function getTime(date) {
      let seperator1 = "-";
      let year = date.getFullYear();
      let month = date.getMonth() + 1;
      let strDate = date.getDate();
      if (month >= 1 && month <= 9) {
          month = "0" + month;
      }
      if (strDate >= 0 && strDate <= 9) {
          strDate = "0" + strDate;
      }
      let currentdate = year + seperator1 + month + seperator1 + strDate;
      return currentdate;
  }
   /** 
     * 获得相对当前周AddWeekCount个周的起止日期 
     * AddWeekCount为0代表当前周 为-1代表上一个周   为1代表下一个周以此类推
     * 
     **/
    function getWeekStartAndEnd(AddWeekCount) {
      //起止日期数组   
      var startStop = new Array();
      //一天的毫秒数   
      var millisecond = 1000 * 60 * 60 * 24;
      //获取当前时间   
      var currentDate = new Date();
      //相对于当前日期AddWeekCount个周的日期
      currentDate = new Date(currentDate.getTime() + (millisecond * 7 * AddWeekCount));
      //返回date是一周中的某一天
      var week = currentDate.getDay();
      //返回date是一个月中的某一天   
      var month = currentDate.getDate();
      console.log(month)
          //减去的天数   
      var minusDay = week != 0 ? week - 1 : 6;
      //获得当前周的第一天   
      var currentWeekFirstDay = new Date(currentDate.getTime() - (millisecond * minusDay));
      //获得当前周的最后一天
      var currentWeekLastDay = new Date(currentWeekFirstDay.getTime() + (millisecond * 6));
      //添加至数组   
      startStop.push(getDateStr3(currentWeekFirstDay));
      startStop.push(getDateStr3(currentWeekLastDay));

      return startStop;
  }
  getWeekStartAndEnd(1);
  //刚加载页面-默认本周
  var start = $('.time-change .detail-time #start');
  var end = $('.time-change .detail-time #end');
  Handtoday();

  //获取本周时间
  var time_num=0;
  function Handtoday(){
    var time_num=0;
    start.text(getWeekStartAndEnd(0)[0]);
    end.text(getWeekStartAndEnd(0)[1]);
    
    // getIntention(1,start.text(),end.text());
  }
  //上一周
  function reduceTime(){
    time_num--;
    start.text(getWeekStartAndEnd(time_num)[0]);
    end.text(getWeekStartAndEnd(time_num)[1]);
    page=1;
    getIntention(page,start.text(),end.text());
  }
  //上一周-点击
  $('.time-change').on('click','.arrow-left',function(){
    $('#popMto').empty();
    $('#popMto1').empty();
    results.list = [];
    results.clocking = {}
    reduceTime();
    mui('#pullrefresh').pullRefresh().refresh(true);
  })
  //下一周
  function addTime(){
    time_num++;
    start.text(getWeekStartAndEnd(time_num)[0]);
    end.text(getWeekStartAndEnd(time_num)[1]);
    page = 1;
    getIntention(page,start.text(),end.text());
  }
  //下一周-点击
  $('.time-change').on('click','.arrow-right',function(){
    $('#popMto').empty();
    $('#popMto1').empty();
    results.list = [];
    results.clocking = {}
    addTime();
    mui('#pullrefresh').pullRefresh().refresh(true);
  })

  // function loading(){
  //   //存储模板渲染进来的数据
  //   var results = [];
  //   // 页数
  //   var page = 0;
  //   // 每页展示10个
  //   var size = 10;
  //     var dropload = $('.content').dropload({
  //     scrollArea: window,
  //     loadDownFn: function (me) {
  //       console.log('滑动加载是否执行')
  //       var loading1='<div class="dropload-load"><span class="loading"></span>加载中...</div>';
  //       $('.lists').append(loading1);
  //       page++;
  //       // 拼接HTML
  //       $.get('/Clocking/WeekClocking', {
  //         staffer_id: JSON.parse(localStorage.getItem('stafferId')),
  //         school_id : JSON.parse(localStorage.getItem('setBrand')),
  //         company_id:JSON.parse(localStorage.getItem('companyId')),
  //         start_day:start.text(),
  //         end_day:end.text(),
  //         p:page,
  //         num:10,
  //       }, function (res) {
  //         console.log(res)
  //         results.clocking=res.result.clocking;
  //         results.allnum=res.allnum;
  //         results.list=res.result.list;
  //         var arrLen = res.result;
  //         if (res.error == '0') {
  //           dotRender('#allNumber', results, '#popMto');
  //           dotRender('#familyStu', results, '#popMto1');

  //           if (arrLen.length > 0) {
  //             me.resetload();
  //             // 如果没有数据
  //           } else {
  //             // 锁定
  //             me.lock();
  //             // 无数据
  //             me.noData();
  //             var loading='<div class="dropload-down"><div class="dropload-refresh">暂无数据</div></div>'
  //             $('.lists').append(loading);
  //           }
  //           // 为了测试，延迟1秒加载
  //           setTimeout(function () {
  //             // 插入数据到页面，放到最后面
  //             // $('#popMto1').append($('.lists'));
  //             // 每次数据插入，必须重置
  //             me.resetload();
  //           }, 1000);
  //         } else {
  //           // 即使加载出错，也得重置
  //           me.resetload();
  //           $('.dropload-down').hide();
  //         }

  //       }, 'json');
  //     }
  //   });
  // } 

   //滑动加载
   mui.init({
    pullRefresh: {
      container: '#pullrefresh',
      down: {
        callback: pulldownRefresh
      },
      up: {
        contentrefresh: '正在加载...',
        callback: pullupRefresh
      }
    }
  });
  /**
   * 下拉刷新具体业务实现
   */
  var results=[];
  results.list = [];
  results.clocking = {};
  var page=0;
  function pulldownRefresh() {
    page=1;
    results.list=[];
    results.clocking = {};
    setTimeout(function() {
      var table = document.body.querySelector('.mui-table-view');
      var cells = document.body.querySelectorAll('.mui-table-view-cell');
      
      getIntention(page,start.text(),end.text())

      mui('#pullrefresh').pullRefresh().endPulldownToRefresh(); //refresh completed
      mui('#pullrefresh').pullRefresh().refresh(true);
    }, 1500);
  }
  var count = 0;
  /**
   * 上拉加载具体业务实现
   */
  function pullupRefresh() {
    page++;
    let arr = 0;

    getIntention(page,start.text(),end.text()).then(res=>{
      // console.log('接收到结果为：'+res);
      arr = res;
    })
    setTimeout(function() {
      mui('#pullrefresh').pullRefresh().endPullupToRefresh((arr<10)); //参数为true代表没有更多数据了。
      var table = document.body.querySelector('.mui-table-view');
      var cells = document.body.querySelectorAll('.mui-table-view-cell');
      

    }, 1500);
  }
  if (mui.os.plus) {
    mui.plusReady(function() {
      setTimeout(function() {
        mui('#pullrefresh').pullRefresh().pullupLoading();
      }, 1000);

    });
  } else {
    mui.ready(function() {
      mui('#pullrefresh').pullRefresh().pullupLoading();
    });
  };

  function getIntention(page,startTime,endTime){
    return new Promise(function(resolve){
      $.get('/Clocking/WeekClocking', {
        staffer_id: JSON.parse(localStorage.getItem('stafferId')),
        school_id : JSON.parse(localStorage.getItem('setBrand')),
        company_id:JSON.parse(localStorage.getItem('companyId')),
        start_day:startTime,
        end_day:endTime,
        p:page,
        num:10,
      }, function (res) {
        console.log(res)
        resolve(res.result.list.length);
        results.list = results.list.concat(res.result.list);
        results.clocking=res.result.clocking;
        if (res.error == '0') {
          dotRender('#allNumber', results, '#popMto');
          dotRender('#familyStu', results, '#popMto1');
        } else {
      
        }

      }, 'json');
    });
    
  }
  mui('body').on('tap','a',function(){document.location.href=this.href;});
  mui('body').on('click','a',function(){document.location.href=this.href;});
})