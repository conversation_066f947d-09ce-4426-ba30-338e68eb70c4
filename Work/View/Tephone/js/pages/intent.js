require.config({
  baseUrl: '/Work/View/Tephone/js/',
  paths:{
      jquery: 'lib/jquery',
      rem: 'lib/int.rem.min',
  }
})
require(['jquery','rem','common'], function($,rem,common) {
  $('.customeBrief-box .brief-change').on('click','.change',function(){
    var $t=$(this);
    var index=$t.index();
    $('.customeBrief-box .brief-change .change').removeClass('cur').eq(index).addClass('cur');
    $('.texts-box-add .texts-item').addClass('none').eq(index).removeClass('none');
  })

})