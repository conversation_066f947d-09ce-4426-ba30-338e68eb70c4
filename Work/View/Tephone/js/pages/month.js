require.config({
  baseUrl: '/Work/View/Tephone/js/',
  paths:{
      jquery: 'lib/jquery',
      rem: 'lib/int.rem.min',
      drop: 'lib/dropload.min',
  }
})
require([
  'jquery',
  'rem',
  'common',
  './utils/dot-render',
  'drop',
  'goIndex'
], function($,rem,common,dotRender,drop,goIndex) {
  var startTime='';
  $(document).on('click','.month-calendar .calendar-ct .date-items li .days li',function(){
    startTime=$('.month-calendar #dt').val();
    $('.month-calendar #dt').val();
    console.log($('.month-calendar #dt').val());
    $('#popMto').empty();
    $('#popMto1').empty();
    loading();
  })
  function loading(){
    //存储模板渲染进来的数据
    var results = [];
    // 页数
    var page = 0;
    // 每页展示10个
    var size = 10;
      var dropload = $('.content').dropload({
      scrollArea: window,
      loadDownFn: function (me) {
        console.log('滑动加载是否执行')
        var loading1='<div class="dropload-load"><span class="loading"></span>加载中...</div>';
        $('.lists').append(loading1);
        page++;
        // 拼接HTML
        $.get('/Clocking/WeekClocking', {
          staffer_id: JSON.parse(localStorage.getItem('stafferId')),
          school_id : JSON.parse(localStorage.getItem('setBrand')),
          company_id:JSON.parse(localStorage.getItem('companyId')),
          start_day:startTime,
          end_day:startTime,
          p:page,
          num:10,
        }, function (res) {
          console.log(res)
          results.clocking=res.result.clocking;
          results.allnum=res.allnum;
          results.list=res.result.list;
          var arrLen = res.result;
          if (res.error == '0') {
            dotRender('#allNumber', results, '#popMto');
            dotRender('#familyStu', results, '#popMto1');

            if (arrLen.length > 0) {
              me.resetload();
              // 如果没有数据
            } else {
              // 锁定
              me.lock();
              // 无数据
              me.noData();
              var loading='<div class="dropload-down"><div class="dropload-refresh">暂无数据</div></div>'
              $('.lists').append(loading);
            }
            // 为了测试，延迟1秒加载
            setTimeout(function () {
              // 插入数据到页面，放到最后面
              // $('#popMto1').append($('.lists'));
              // 每次数据插入，必须重置
              me.resetload();
            }, 1000);
          } else {
            // 即使加载出错，也得重置
            me.resetload();
            $('.dropload-down').hide();
          }

        }, 'json');
      }
    });
  } 

  //
  loading();
})