require.config({
  baseUrl: '/Work/View/Tephone/js/',
  paths:{
      jquery: 'lib/jquery',
      rem: 'lib/int.rem.min',
  }
})
require(['jquery','rem','common'], function($,rem,common,goIndex) {
   //点击年月
  //  $('#top .month-box').on('click','.time',function(){
  //   // alert(1)
  //   // $('.rolldate-mask').show();
  //   // $('.rolldate-panel').show()
  //  });
   //点击年月日-确定按钮
   $(document).on('click','.rolldate-panel .rolldate-confirm',function(){
    var valueTime = $('#date-group1-1').val();
    $('.month-box .time .month-add').text(valueTime.split('-')[1]);
    $('.month-box .time .year-add').text(valueTime.split('-')[0]);
   });

   //默认当前  年
  function getDateyear() {
      var myDate = new Date(); //获取系统当前时间
      return myDate.getFullYear();
  };
  //默认当前  月
  function getDatemonth() {
    var myDate = new Date(); //获取系统当前时间
    var month = myDate.getMonth() + 1;
    if (month < 10) {
        month = "0" + month;
        // month=parseInt(month);
    }
    return month;
  };
   //默认当前时间
   $('.month-box .time .month-add').text(getDatemonth())
   $('.month-box .time .year-add').text(getDateyear())

  //月份加处理
  function addMonth() {
    var month = parseInt($('.month-box .time .month-add').text());
    var year = parseInt($('.month-box .time .year-add').text());
    //判断month
    if (month == 12) {
      year = year + 1;
      month = 1;
    } else {
      month = month + 1;
    }
    if (month < 10) {
        month = "0" + month;
    } else {
      month = month;
    }
    $('.month-box .time .year-add').text(year);
    $('.month-box .time .month-add').text(month);
  };

  //月份减处理
  function reduceMonth() {
    var month = parseInt($('.month-box .time .month-add').text());
    var year = parseInt($('.month-box .time .year-add').text());
    //判断month
    if (month == 1) {
      year = year - 1;
      month = 12;
    } else {
      month = month - 1;
    }
    if (month < 10) {
        month = "0" + month;
    } else {
       month = month;
    }
    $('.month-box .time .year-add').text(year);
    $('.month-box .time .month-add').text(month);
  };
  
  $('.month-box').on('click','.prev',function(){
    reduceMonth();
  });
  //月份切换 - 下个月
  $('.month-box').on('click','.next',function(){
    addMonth();
  });
  //获取当前页面的url
  var url = window.location.href;
 var firstYear = $('.month-box .time .year-add').text();
 var firstMonth = $('.month-box .time .month-add').text();
 console.log(url+"?year=" + firstYear + "&month=" +firstMonth)
  console.log(firstYear,firstMonth)
  $('.month-box .prev').attr('href',url+"?year=" + firstYear + "&month=" +firstMonth)

  //点击-切换时间
  $('.month-list').on('click','li',function(){
    // alert(1)
    var $t=$(this);
    var index = $t.index();
    // $('.month-list li').eq(index).addClass('isevent');
    $('.month-list li').find('span').removeClass('active');
    $('.month-list li').eq(index).find('span').addClass('active');
  })
  //点击查看-日程安排
  $('.month-list').on('click','.isevent',function(){
    $('.event-list .event-box').show();
  })
})