require.config({
  baseUrl: '/Work/View/Tephone/js/',
  paths:{
      jquery: 'lib/jquery',
      rem: 'lib/int.rem.min',
      // mui:'lib/mui.min'
  }
})
require([
  'jquery',
  'rem',
  'common',
  './utils/dot-render',
  // 'mui'
], function($,rem,common,dotRender,) {

  var isEmpty=false;//是否需要清空数据

  //筛选弹窗-显示与隐藏
  $('.sb-search-add').on('click','.filter-box',function(e){
    e.stopPropagation();
    $('.addCustom-prop').show();
    return false;
  });
  $(document).click(function (event) {
    var _con = $('.addCustom-prop .level-box');   // 设置目标区域
    if (!_con.is(event.target) && _con.has(event.target).length === 0) { // Mark 1
      $('.addCustom-prop').hide();         
    }
  });
  //意向星级
  var loveKeyword = '';
  $('.IntenteLevel .Love-box').on('click','.love',function () {
    var $t = $(this);
    var index = $t.index();
    var loveList = $('.IntenteLevel .Love-box .love');
    if($t.hasClass('cur')){
      loveList.removeClass('cur');
    }else{
      $t.addClass('cur');
      $t.prevAll().addClass('cur');
    }
    loveKeyword = $('.IntenteLevel .Love-box .love.cur').length;
    console.log($('.IntenteLevel .Love-box .love.cur').length)
    isEmpty=true;
    page = 1;
    getIntention(page)
  })
   //客户状态
   var customKeyword = '';
   $('.customState .state-sort').on('click','.state',function () {
    var $t = $(this);
    var index = $t.index();
    var loveList = $('.customState .state-sort .state');
    if($t.hasClass('cur')){
      loveList.removeClass('cur');
    }else{
      loveList.removeClass('cur');
      $t.addClass('cur');
    }
    customKeyword = $('.customState .state-sort .state.cur').attr('data-state');
    console.log(customKeyword)
    isEmpty=true;
    page = 1;
    getIntention(page)
  })

  //搜索
  $('.sb-search-add .search-box').on('click','.search',function(){
    console.log($('.sb-search-add #keyword').val(),4444)
    console.log(keyword,555)
    isEmpty=true;
    page=1;
    getIntention(page);
  })


  mui.init({
    pullRefresh: {
      container: '#pullrefresh',
      down: {
        callback: pulldownRefresh
      },
      up: {
        contentrefresh: '正在加载...',
        callback: pullupRefresh
      }
    }
  });
  /**
   * 下拉刷新具体业务实现
   */
  var results=[];
  results.list = [];
  var page=0;
  function pulldownRefresh() {
    page=1;
    results.list=[];
    setTimeout(function() {
      var table = document.body.querySelector('.mui-table-view');
      var cells = document.body.querySelectorAll('.mui-table-view-cell');
      
      getIntention(page)

      mui('#pullrefresh').pullRefresh().endPulldownToRefresh(); //refresh completed
      mui('#pullrefresh').pullRefresh().refresh(true);
    }, 1500);
  }
  var count = 0;
  /**
   * 上拉加载具体业务实现
   */
  function pullupRefresh() {
    page++;
    let arr = 0;

    getIntention(page).then(res=>{
      // console.log('接收到结果为：'+res);
      arr = res;
    })
    setTimeout(function() {
      mui('#pullrefresh').pullRefresh().endPullupToRefresh((arr<10)); //参数为true代表没有更多数据了。
      var table = document.body.querySelector('.mui-table-view');
      var cells = document.body.querySelectorAll('.mui-table-view-cell');
      

    }, 1500);
  }
  if (mui.os.plus) {
    mui.plusReady(function() {
      setTimeout(function() {
        mui('#pullrefresh').pullRefresh().pullupLoading();
      }, 1000);

    });
  } else {
    mui.ready(function() {
      mui('#pullrefresh').pullRefresh().pullupLoading();
    });
  };
  function getIntention(page){
    return new Promise(function(resolve){
      $.get('/Intention/getIntention', {
        staffer_id: JSON.parse(localStorage.getItem('stafferId')),
        school_id : JSON.parse(localStorage.getItem('setBrand')),
        company_id:JSON.parse(localStorage.getItem('companyId')),
        //筛选
        client_tracestatus:customKeyword,//客户状态
        track_intention_level:loveKeyword==0 ?'':loveKeyword,//意向星级
        keyword:$('.sb-search-add #keyword').val(),//关键词搜索
        p:page,
        num:10,
      }, function (res) {
        console.log(res)
        resolve(res.result.list.length);
        if(isEmpty==true){
          results.list = [];
        }
        results.list = results.list.concat(res.result.list);
        console.log( results.list)
        dotRender('#allNumber', results, '#popMto');

      }, 'json');
    });
  }

  mui('body').on('tap','a',function(){document.location.href=this.href;});
  mui('body').on('click','a',function(){document.location.href=this.href;});
})