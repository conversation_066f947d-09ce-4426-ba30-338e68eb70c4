require.config({
  baseUrl: '/Work/View/Tephone/js/',
  paths:{
      jquery: 'lib/jquery',
      rem: 'lib/int.rem.min',
      swiper: 'lib/swiper.min',
  }
})
require([
  'jquery',
  'rem',
  'swiper',
  'common',
  './utils/dot-render',
  'goIndex',
  'res-tip'
], function($,rem,swiper,common,dotRender,goIndex,resTip) {
  //本次跟进类型-显示与隐藏
  var endType='';

  $('.exchange-box-type').on('click','.exchange',function(){
    var index=$(this).index();
    $('.followType').addClass('none').eq(index).removeClass('none');
    endType = $(this).find('input[name=choose1]:checked').attr('data-type');
    console.log(endType)
    clearData();
  })

  //沟通类型 
  $('.addRecord-box .choose-ipt').on('click','#scrollProp1',function(){
    $('.swiper-box-prop1').show();
    var swiper1 = new Swiper('.swiper-box-prop1 .swiper-container', {
        direction: 'vertical',
          grabCursor: true,
          effect: 'coverflow',
          slidesPerView: 4,
          centeredSlides: true,
          coverflowEffect: {
            rotate: 0,
            stretch: 0,
            depth: 300,
            modifier: 1,
            slideShadows: false
          }
      });
  })
  var swiperValue1='';
  $('.swiper-box-prop1').on('click','.confirm',function(e){
    e.stopPropagation();
    $('.swiper-box-prop1').hide();
    swiperValue1 = $('.swiper-box-prop1 .swiper-container .swiper-slide.swiper-slide-active').text();
    console.log(swiperValue1)
    $('#scrollProp1').val(swiperValue1);
  });

  //沟通内容
  $('.addRecord-box .choose-ipt').on('click','#scrollProp2',function(){
    $('.swiper-box-prop2').show();
    var swiper2 = new Swiper('.swiper-box-prop2 .swiper-container', {
      direction: 'vertical',
        grabCursor: true,
        effect: 'coverflow',
        slidesPerView: 4,
        centeredSlides: true,
        coverflowEffect: {
          rotate: 0,
          stretch: 0,
          depth: 300,
          modifier: 1,
          slideShadows: false
        }
    });
     
  })
  var swiperValue2='';
  $('.swiper-box-prop2').on('click','.confirm',function(e){
    e.stopPropagation();
    $('.swiper-box-prop2').hide();
    swiperValue2 = $('.swiper-box-prop2 .swiper-container .swiper-slide.swiper-slide-active').text();
    console.log(swiperValue2)
    $('#scrollProp2').val(swiperValue2);
    $('#area-box').val(swiperValue2);
  });

  //下次跟进类型 
  $('.addRecord-box .choose-ipt').on('click','#scrollProp3',function(){
    $('.swiper-box-prop3').show();
    var swiper3 = new Swiper('.swiper-box-prop3 .swiper-container', {
        direction: 'vertical',
          grabCursor: true,
          effect: 'coverflow',
          slidesPerView: 4,
          centeredSlides: true,
          coverflowEffect: {
            rotate: 0,
            stretch: 0,
            depth: 300,
            modifier: 1,
            slideShadows: false
          }
      });
      
  })
  var swiperValue3='';
  var indexId='';
  $('.swiper-box-prop3').on('click','.confirm',function(e){
    e.stopPropagation();
    $('.swiper-box-prop3').hide();
    swiperValue3 = $('.swiper-box-prop3 .swiper-container .swiper-slide.swiper-slide-active').text();

    console.log(swiperValue3)
    $('#scrollProp3').val(swiperValue3);

    indexId=$('.swiper-box-prop3 .swiper-container .swiper-slide.swiper-slide-active').attr('data-ele');
    if(indexId==1){
      $('.follow-dateShow').show();
    }else{
      $('.follow-dateShow').hide();
    }
  });

  // function addRecordData(propShow){
  //   propShow.show();
  //   var swiperId  = new Swiper('.propShow .swiper-container', {
  //       direction: 'vertical',
  //         grabCursor: true,
  //         effect: 'coverflow',
  //         slidesPerView: 4,
  //         centeredSlides: true,
  //         coverflowEffect: {
  //           rotate: 0,
  //           stretch: 0,
  //           depth: 300,
  //           modifier: 1,
  //           slideShadows: false
  //         }
  //     });
  // }

  //柜询-接待人
  $('.addRecord-box .choose-ipt').on('click','#type1-add-1',function(){
    $('.type1-add-1-prop').show();
    var swiperId  = new Swiper('.type1-add-1-prop .swiper-container', {
      direction: 'vertical',
        grabCursor: true,
        effect: 'coverflow',
        slidesPerView: 4,
        centeredSlides: true,
        coverflowEffect: {
          rotate: 0,
          stretch: 0,
          depth: 300,
          modifier: 1,
          slideShadows: false
        }
    });
  })
  var swipertype11Value='';
  $('.type1-add-1-prop').on('click','.confirm',function(e){
    e.stopPropagation();
   
    $('.type1-add-1-prop').hide();
    swipertype11Value = $('.type1-add-1-prop .swiper-container .swiper-slide.swiper-slide-active').text();
    console.log(swipertype11Value)
    $('#type1-add-1').val(swipertype11Value);

  });

  //试听-柜询类型
  $('.addRecord-box .choose-ipt').on('click','#type1-add-2',function(){
    $('.type1-add-2-prop').show();
    var swipertype12Prop= new Swiper('.type1-add-2-prop .swiper-container', {
      direction: 'vertical',
        grabCursor: true,
        effect: 'coverflow',
        slidesPerView: 4,
        centeredSlides: true,
        coverflowEffect: {
          rotate: 0,
          stretch: 0,
          depth: 300,
          modifier: 1,
          slideShadows: false
        }
    });
  })
  var swipertype12Value='';
  $('.type1-add-2-prop').on('click','.confirm',function(e){
    e.stopPropagation();
    $('.type1-add-2-prop').hide();
    swipertype12Value = $('.type1-add-2-prop .swiper-container .swiper-slide.swiper-slide-active').text();
    console.log(swipertype12Value)
    $('#type1-add-2').val(swipertype12Value);
  });
  //试听-试听类型
  $('.addRecord-box .choose-ipt').on('click','#type2-add-1',function(){
    $('.type2-add-1-prop').show();
    var swipertype12Prop= new Swiper('.type2-add-1-prop .swiper-container', {
      direction: 'vertical',
        grabCursor: true,
        effect: 'coverflow',
        slidesPerView: 4,
        centeredSlides: true,
        coverflowEffect: {
          rotate: 0,
          stretch: 0,
          depth: 300,
          modifier: 1,
          slideShadows: false
        }
    });
  })
  var swipertype21Value='';
  var swipertype21Id='';
  $('.type2-add-1-prop').on('click','.confirm',function(e){
    e.stopPropagation();
    $('.type2-add-1-prop').hide();
    swipertype21Value = $('.type2-add-1-prop .swiper-container .swiper-slide.swiper-slide-active').text();
    console.log(swipertype21Value)
    $('#type2-add-1').val(swipertype21Value);
    swipertype21Id = $('.type2-add-1-prop .swiper-container .swiper-slide.swiper-slide-active').attr('data-ele');
    //试听类型-不为空-试听班级才显示
    if(swipertype21Value != '' &&  $('#date-visiter-2').val() !=''){
      $('#leasenClass').show();
      $('#leasenClass1').show();
      getAuditionData();
    }
    $('#type2-add-3').val('');
    $('#leasenClass1-1').val('');
  });

  //试听-接待人
  $('.addRecord-box .choose-ipt').on('click','#type2-add-2',function(){
    $('.type2-add-2-prop').show();
    var swipertype12Prop= new Swiper('.type2-add-2-prop .swiper-container', {
      direction: 'vertical',
        grabCursor: true,
        effect: 'coverflow',
        slidesPerView: 4,
        centeredSlides: true,
        coverflowEffect: {
          rotate: 0,
          stretch: 0,
          depth: 300,
          modifier: 1,
          slideShadows: false
        }
    });
  })
  var swipertype22Value='';
  $('.type2-add-2-prop').on('click','.confirm',function(e){
    e.stopPropagation();
    $('.type2-add-2-prop').hide();
    swipertype22Value = $('.type2-add-2-prop .swiper-container .swiper-slide.swiper-slide-active').text();
    console.log(swipertype22Value)
    $('#type2-add-2').val(swipertype22Value);
  });
  
  //试听类型-点击事件

  //选择试听日期-之后-才能选择试听班级
  $(document).on('click','.rolldate-panel .rolldate-btn',function(){
    if(swipertype21Value != '' &&  $('#date-visiter-2').val() !=''){
      $('#leasenClass').show();
      $('#leasenClass1').show();
      getAuditionData();
    }
  
  })
  //试听班级接口
  getAuditionData();
  function getAuditionData(){
    var results =[];
    $.get('/Api/getAudition', {
      staffer_id: JSON.parse(localStorage.getItem('stafferId')),
      school_id : JSON.parse(localStorage.getItem('setBrand')),
      company_id:JSON.parse(localStorage.getItem('companyId')),
      hour_day:$('#date-visiter-2').val(),//	试听日期
      audition_genre:swipertype21Id,//试听类型 0公开课试听 1-插班试听
    }, function (res) {
      console.log(res)
      if (res.error == '0') {
        results.list=res.result.list;
        dotRender('#allNumber', results, '#popMto');
      } else {
        dotRender('#allNumber', '', '#popMto');
      }

    }, 'json');
  }
  
  //试听-试听班级
  $('.addRecord-box .choose-ipt').on('click','#type2-add-3',function(){
    $('.type2-add-3-prop').show();
    var swipertype12Prop= new Swiper('.type2-add-3-prop .swiper-container', {
      direction: 'vertical',
        grabCursor: true,
        effect: 'coverflow',
        slidesPerView: 4,
        centeredSlides: true,
        coverflowEffect: {
          rotate: 0,
          stretch: 0,
          depth: 300,
          modifier: 1,
          slideShadows: false
        }
    });
  })
  var swipertype23Value='';
  var classIdValue='';//试听班级-class_id
  var courseIdValue='';//试听班级-course_id
  var swipertype23Value='';
  var hourIdValue='';//试听课时
  $('.type2-add-3-prop').on('click','.confirm',function(e){
    e.stopPropagation();
    $('.type2-add-3-prop').hide();
    swipertype23Value = $('.type2-add-3-prop .swiper-container .swiper-slide.swiper-slide-active').find('.cname-value').text();
    classIdValue = $('.type2-add-3-prop .swiper-container .swiper-slide.swiper-slide-active').attr('data-classId');
    courseIdValue = $('.type2-add-3-prop .swiper-container .swiper-slide.swiper-slide-active').attr('data-courseId');
    hourIdValue = $('.type2-add-3-prop .swiper-container .swiper-slide.swiper-slide-active').attr('data-hourId');
    console.log(swipertype23Value)
    $('#type2-add-3').val(swipertype23Value);
    $('#leasenClass1-1').val(swipertype23Value);

  });

  //已确认无意向-流失类型
  $('.addRecord-box .choose-ipt').on('click','#type4-add-1',function(){
    $('.type4-add-1-prop').show();
    var swipertype12Prop= new Swiper('.type4-add-1-prop .swiper-container', {
      direction: 'vertical',
        grabCursor: true,
        effect: 'coverflow',
        slidesPerView: 4,
        centeredSlides: true,
        coverflowEffect: {
          rotate: 0,
          stretch: 0,
          depth: 300,
          modifier: 1,
          slideShadows: false
        }
    });
  })
  var swipertype41Value='';
  var swipertype41Data='';
  $('.type4-add-1-prop').on('click','.confirm',function(e){
    e.stopPropagation();
    $('.type4-add-1-prop').hide();
    swipertype41Value = $('.type4-add-1-prop .swiper-container .swiper-slide.swiper-slide-active').text();
    swipertype41Data = $('.type4-add-1-prop .swiper-container .swiper-slide.swiper-slide-active').attr('data-ele');
    console.log(swipertype41Value)
    $('#type4-add-1').val(swipertype41Value);
  });

  //沟通对象
  $('.addRecord-box .choose-ipt').on('click','#scrollProp-add',function(){
    $('.scrollProp-add-prop').show();
    var scrollPropAddProp= new Swiper('.scrollProp-add-prop .swiper-container', {
      direction: 'vertical',
        grabCursor: true,
        effect: 'coverflow',
        slidesPerView: 4,
        centeredSlides: true,
        coverflowEffect: {
          rotate: 0,
          stretch: 0,
          depth: 300,
          modifier: 1,
          slideShadows: false
        }
    });
  })
  var scrollPropAddValue='';
  var scrollPropAddCode='';//沟通对象-传入后端的code
  $('.scrollProp-add-prop').on('click','.confirm',function(e){
    e.stopPropagation();
    $('.scrollProp-add-prop').hide();
    scrollPropAddValue = $('.scrollProp-add-prop .swiper-container .swiper-slide.swiper-slide-active').text();
    scrollPropAddCode = $('.scrollProp-add-prop .swiper-container .swiper-slide.swiper-slide-active').attr('data-ele');
    console.log(scrollPropAddValue)
    $('#scrollProp-add').val(scrollPropAddValue);
  });


  //意向星级
  var loveLength=0;
  $('.addRecord-box .loveLevel .Love-box').on('click','.love',function () {
    var $t = $(this);
    var index = $t.index();
    var loveList = $('.addRecord-box .loveLevel .Love-box .love');
    if($t.hasClass('cur')){
      loveList.removeClass('cur');
    }else{
      $t.addClass('cur');
      $t.prevAll().addClass('cur');
    }
    loveLength = $('.addRecord-box .loveLevel .Love-box .love.cur').length;
    console.log(loveLength)
  })

  //清空数据
  function clearData(){
    $('#type1-add-1').val('');
    $('#type1-add-2').val('');
    $('#date-visiter-1').val('');
    $('#type2-add-1').val('');
    $('#type2-add-2').val('');
    $('#date-visiter-2').val('');
    $('#type4-add-1').val('');
  }

  //提交
  $('.review-footer-box .footer').on('click','.btn',function(){
    //沟通效果
    var endresult='';
    $('.exchange-box-result .exchange').each(function(index,element){
      console.log($(this).find('input[name=choose]'))
      if($(this).find('input[name=choose]').is(':checked')){
        endresult = 1;
      }else{
        endresult = 0;
      }
    })
    var commPerson=''; //接待人
    commPerson= $('.exchange-box-type .exchange input[name=choose1]:checked').attr('data-type');
    if($('.exchange-box-type .exchange input[name=choose1]:checked').attr('data-type') ==1){
      commPerson=$('#type1-add-1').val();//柜询接待人
    }else if($('.exchange-box-type .exchange input[name=choose1]:checked').attr('data-type') ==2){
      commPerson=$('#type2-add-2').val();//试听接待人
    }

    // var addArrs=[];//亲属关系 数组-变量待定
    // var radioVlue='';
    // $('.contact-box .contact').each(function(index,element){
    //   console.log($(this).find('.main-contact input[name=mainPerson]'))
    //   if($(this).find('.main-contact input[name=mainPerson]').is(':checked')){
    //     radioVlue = 1;
    //   }else{
    //     radioVlue = 0;
    //   }
    //   var obj={
    //     family_relation:$(this).find('.specileId').val(),
    //     family_cnname:$(this).find('.relativeName').val(),
    //     family_mobile:$(this).find('.addphone').val(),
    //     family_isdefault:radioVlue,//是否为主要联系人，1是主要，0是非主要
    //   }
    //   addArrs.push(obj)
    //   console.log($(this).find('.specileId'))
    // })
    // var loveIdArr = [];//意向课程id
    // //意向课程id
    // var loveObj={
    //   course_id:loveIdValue,
    // }
    // loveIdArr.push(loveObj)
    // console.log(addArrs,99999)

    $.post('/Intention/trackClient', {
      staffer_id: JSON.parse(localStorage.getItem('stafferId')),
      school_id : JSON.parse(localStorage.getItem('setBrand')),
      company_id:JSON.parse(localStorage.getItem('companyId')),
      client_id:$('.addRecord-box').attr('data-clientid'),//客户id
      class_id:classIdValue,//班级id
      course_id:courseIdValue,//课程id
      track_linktype:swiperValue1,//	沟通类型 -number
      object_code:scrollPropAddCode,//沟通对象
      track_validinc:endresult,//是否有效
      track_intention_level:loveLength,//意向星级 number
      track_followmode:endType,//本次跟进模式 0普通回访 1柜询 2视听 -1已确认无意向
      track_note:$('#area-box').val(),//	沟通内容
      track_followuptype:indexId,//下次跟进类型0普通跟进1提醒跟进
      track_followuptime:$('#date-group1-1').val(),//下次更进时间 提醒跟进时必传
      receiver_name:commPerson,// 柜询-	接待人姓名 与 试听人姓名，同一个参数
      invite_genre:swipertype12Value,//
      hour_id:hourIdValue,//试听课时
      class_cnname:$('#leasenClass1-1').val(),
      invite_visittime:$('#date-visiter-1').val(),//柜询 - 到访日
      audition_genre:swipertype21Id,//试听类型
      audition_visittime:$('#date-visiter-2').val(),//	试听到访时间 -试听状态下必传
      client_lapsedtype:swipertype41Data,//	'流失类型：0已报异业1强烈抵触2其他'

    }, function (res) {
      console.log(res)
      if (res.error == '0') {
        resTip(res.errortip, false);
        window.location = '/Intention/followRecord?client_id='+$('.addRecord-box').attr('data-clientid')+'&school_id='+JSON.parse(localStorage.getItem('setBrand'))
      } else {
        resTip(res.errortip, true);
      }
  
    }, 'json');
  })
  
})