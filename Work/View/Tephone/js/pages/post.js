require.config({
  baseUrl: '/Work/View/Tephone/js/',
  paths:{
      jquery: 'lib/jquery',
      rem: 'lib/int.rem.min'
  }
})
require(['jquery','rem','common'], function($,rem,common) {
  //集团与校园职务切换
  $('.detail-post').on('click','.tab-post .postComm',function(){
    var $t = $(this);
    var index = $t.index();
    var tab = $('.tab-post .postComm');
    var list =  $('.tab-lists .list');
    tab.removeClass('cur').eq(index).addClass('cur');
    list.addClass('none').eq(index).removeClass('none');
    $('.tab-lists .list .item').removeClass('cur');
  })
  //集团职务
  $('.tab-lists .list1').on('click','.item',function(){  
    var $t = $(this);
    var index = $t.index();
    var tab = $('.tab-lists .list1 .item');
    if($t.hasClass('cur')){
      $t.removeClass('cur');
    }else{
      tab.removeClass('cur').eq(index).addClass('cur');
    }
  })
    //校园职务
  $('.tab-lists .list2').on('click','.item',function(){
    //集团职务
    var $t = $(this);
    var index = $t.index();
    var tab = $('.tab-lists .list2 .item');
    if($t.hasClass('cur')){
      $t.removeClass('cur');
    }else{
      tab.removeClass('cur').eq(index).addClass('cur');
    }
  });
  //选择职位的值
  $('.detail-post').on('click','.action .confirm',function(){
    var postVlue = $('.tab-lists .list .item.cur').attr('data-postbe_id');
    var schoolId = $('.tab-lists .list .item.cur').attr('data-school_id');
    console.log(postVlue)
    $.post('/Index/ChoosePost', {
      school_id : schoolId,
      postbe_id:postVlue,//职位id
    }, function (res) {
      console.log(res)
      if (res.error == '0') {
        window.location = '/Index'
      } else {

      }
  
    }, 'json');
  })



  
})