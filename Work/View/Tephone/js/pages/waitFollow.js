require.config({
  baseUrl: '/Work/View/Tephone/js/',
  paths:{
      jquery: 'lib/jquery',
      rem: 'lib/int.rem.min',
  }
})
require([
  'jquery',
  'rem',
  'common',
  './utils/dot-render',
], function($,rem,common,dotRender) {
    //滑动加载
    mui.init({
      pullRefresh: {
        container: '#pullrefresh',
        down: {
          callback: pulldownRefresh
        },
        up: {
          contentrefresh: '正在加载...',
          callback: pullupRefresh
        }
      }
    });
    /**
     * 下拉刷新具体业务实现
     */
    var results=[];
    results.list = [];
    var page=0;
    function pulldownRefresh() {
      page=1;
      results.list=[];
      setTimeout(function() {
        var table = document.body.querySelector('.mui-table-view');
        var cells = document.body.querySelectorAll('.mui-table-view-cell');
        
        getIntention(page)
  
        mui('#pullrefresh').pullRefresh().endPulldownToRefresh(); //refresh completed
        mui('#pullrefresh').pullRefresh().refresh(true);
      }, 1500);
    }
    var count = 0;
    /**
     * 上拉加载具体业务实现
     */
    function pullupRefresh() {
      page++;
      let arr = 0;

      getIntention(page).then(res=>{
        // console.log('接收到结果为：'+res);
        arr = res;
      })
      setTimeout(function() {
        mui('#pullrefresh').pullRefresh().endPullupToRefresh((arr<10)); //参数为true代表没有更多数据了。
        var table = document.body.querySelector('.mui-table-view');
        var cells = document.body.querySelectorAll('.mui-table-view-cell');

  
      }, 1500);
    }
    if (mui.os.plus) {
      mui.plusReady(function() {
        setTimeout(function() {
          mui('#pullrefresh').pullRefresh().pullupLoading();
        }, 1000);
  
      });
    } else {
      mui.ready(function() {
        mui('#pullrefresh').pullRefresh().pullupLoading();
      });
    };
  function getIntention(page){
    return new Promise(function(resolve){
      $.get('/Intention/waitClientFollow', {
        staffer_id: JSON.parse(localStorage.getItem('stafferId')),
        school_id : JSON.parse(localStorage.getItem('setBrand')),
        company_id:JSON.parse(localStorage.getItem('companyId')),
        is_count:1,
        p:page,
        num:10,
      }, function (res) {
        console.log(res)
        console.log(results,222222222222)
        resolve(res.result.list.length);
        if (res.error == '0') {
          results.list = results.list.concat(res.result.list);
          dotRender('#allNumber', results, '#popMto');
        } else {
          
        }
    
      }, 'json');
    });
  }

  mui('body').on('tap','a',function(){document.location.href=this.href;});
  mui('body').on('click','a',function(){document.location.href=this.href;});
})