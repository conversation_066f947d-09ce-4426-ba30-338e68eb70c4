/*分页*/
.pagemenu { padding:35px 0; text-align:right; height:30px;line-height:30px;color:#333;font-family:'微软雅黑';}
.pagemenu a { padding:4px 8px; border:1px solid #ccc;  margin:0 5px; color:#666; text-decoration:none;background:#f2f2f2;border-radius:3px;}
.pagemenu a:hover {border: #2a70ab 1px solid; color:#fff;background:#2a70ab;border-radius:3px; }
.pagemenu a:active {border: #4bb5e1 1px solid; color:#4bb5e1;border-radius:5px;}
.pagemenu .current { padding:4px 8px;  margin:0 5px;background:#4aabe9; color:#fff; border:1px solid #4aabe9; font-weight:bold;border-radius:3px;}
.pagemenu .disabled {border: #ddd 1px solid; padding:4px 8px; color:#999; margin:2px;}


.form-control[readonly]{
    cursor: pointer;
    background-color: #fff;
    opacity: 1;
}
/*信息提示框*/
.errormotify{position: fixed; top: 35%; left: 50%; width: 220px; padding: 0; margin: 0 0 0 -110px; z-index: 9999; background: #ea4335; color: #fff; font-size: 14px; line-height: 1.5em; border-radius: 6px; -webkit-box-shadow: 0px 1px 2px rgba(0,0,0,0.2); box-shadow: 0px 1px 2px rgba(0,0,0,0.2);}
.errormotify .motify-inner{padding: 10px 10px; text-align: center; word-wrap: break-word;}
.errormotify .motify-inner span{display:inline-block;width:22px;height:23px;background:url(../images/error.png) no-repeat;background-size:100%; vertical-align:middle;margin-right:0.6rem;}
.okmotify{position: fixed; top: 25%; left: 50%; width: 220px; padding: 0; margin: 0 0 0 -110px; z-index: 9999; background: #01b4bf; color: #fff; font-size: 14px; line-height: 1.5em; border-radius: 6px; -webkit-box-shadow: 0px 1px 2px rgba(0,0,0,0.2); box-shadow: 0px 1px 2px rgba(0,0,0,0.2);}
.okmotify .motify-inner{padding: 10px 10px; text-align: center; word-wrap: break-word;}
.okmotify .motify-inner span{display:inline-block;width:22px;height:23px;background:url(../images/tsok.png) no-repeat;background-size:100%; vertical-align:middle;margin-right:0.6rem;}
/**购车弹窗**/
.Tiplog{ position: fixed; left: 0; top: 0; width: 100%; height: 100%; z-index: 200; _position: absolute; _height: expression(eval(document.body.scrollHeight));}
.Tiplog .Tiplog-top{padding:0.8rem 1rem;border-bottom:1px solid #ddd;font-size:14px;color:#888;}
.Tiplog .Tiplog-top .Tiplog-Close-but{display:inline-block;width:15px;height:14px; cursor: pointer; background-size:100%; vertical-align:middle;float:right;margin-top:4px;}
.Tiplog .Tiplog-Curtain{background: #000;opacity:0.3; filter: alpha(opacity=30); position:absolute; width:100%; height:100%;}
.Tiplog .Tiplog-main{position:absolute; width:360px; left:50%; top:30%;margin-left: -180px;background:#FFF;}
.Tiplog .Tiplog-main .Tiplog-content{padding-top:16px;}
.Tiplog .Tiplog-main .Tiplog-content span.danger{ display:block; height:100px; font-size: 80px; line-height: 100px; text-align: center; color: #e76e70; width:20%; margin:0px auto;}
.Tiplog .Tiplog-main .Tiplog-content span.warning{ display:block; height:100px; font-size: 80px; line-height: 100px; text-align: center; color: #ce8f22; width:20%; margin:0px auto;}
.Tiplog .Tiplog-main .Tiplog-content span.success{ display:block; height:100px; font-size: 80px; line-height: 100px; text-align: center; color: #3c763d; width:20%; margin:0px auto;}
.Tiplog .Tiplog-main .Tiplog-content .tip-text{ margin:10px;}
.Tiplog .Tiplog-main .Tiplog-content .tip-text .tip-title{ font-size:18px; text-align:center; line-height:30px;}
.Tiplog .Tiplog-main .Tiplog-content .tip-text .tip-txt{ font-size:14px; color:#999; text-align:center; line-height:30px;}
.Tiplog .Tiplog-main .Tiplog-content .LogView{ text-align: center;}
.Tiplog .Tiplog-main .Tiplog-content .LogView img{ max-width: 95%;}

.Tiplog .Tiplog-main .Tiplog-content select{ line-height:32px; height:32px; border:1px solid #ddd; border-radius:3px;}

.Tiplog .Tiplog-main .Tiplog-footer{border-top:1px solid #dddddd; padding:20px; text-align:center;}
.Tiplog .Tiplog-main .Tiplog-footer .but{ display: inline-block;width:30%; text-align:center; line-height:35px; font-size:1rem; border-radius:10px; margin:0px 10px; cursor: pointer;}
.Tiplog .Tiplog-main .Tiplog-footer .but-red{ color:#ea4334;  border:2px solid #ea4334;}
.Tiplog .Tiplog-main .Tiplog-footer .but-black{ color:#999;  border:2px solid #ccc;}

.UpimgBox .uploadimg{background: rgba(0, 0, 0, 0.03) none repeat scroll 0 0;border: 1px solid rgba(0, 0, 0, 0.03);border-radius: 3px;height: 200px;width: 92%; margin:20px;position:relative;}
.UpimgBox .uploadimg > div.tc{position:absolute;left:0;width:100%;top:50%;margin-top:-30px;}
.UpimgBox .uploadimg img{max-width:100%;max-height:450px;}
.UpimgBox .imgpreview{position:absolute;left:0; right: 0; width: 100%; height: 100%;background: rgba(0, 0, 0, 0.03) none repeat scroll 0 0; text-align: center;}
.UpimgBox .imgpreview img{ max-width: 100%; max-width: 200px}



p#vtip {position: absolute; padding:5px 10px; left: 5px; font-size: 12px;
    background-color: white; border: 1px solid #a6c9e2; -moz-border-radius: 5px; -webkit-border-radius: 5px; z-index: 9999;
    font-family:"微软雅黑"; color:#F00; border-radius:5px;
}
p#vtip #vtipArrow { position: absolute; top: -10px; left: 5px ;}


.AjaxForm input.error-ipt-tip{border: 1px solid #fcd3d3;}
.AjaxForm input.ok-ipt-tip{border: 1px solid #8dbee2;}
.AjaxForm span.form-tip{height: 26px; line-height: 26px; vertical-align: middle; margin-left: 20px;}
.AjaxForm .ok-tip { display: inline-block;}
.AjaxForm .error-tip{display: inline-block;}
.AjaxForm .ok-tip em{ background: url(../images/right.png) center center no-repeat !important;}
.AjaxForm .error-tip em{ background: url(../images/mistake.png) 4px center no-repeat !important;}
.AjaxForm span.form-tip em{ display: inline-block; width: 26px; height: 26px; vertical-align: middle; }
.input_validation-failed,.error-ipt-tip { border: 1px solid #FF0000!important; color:red;}

/*商品配置*/
.transparentDiv{
	width: 100%;
	height: 100%;
	position: fixed;
	z-index: 9999;
	background-color: #000;
	opacity: 0.2;
}
.dialogDiv{
	width: 300px;
	height: 200px;
	z-index: 99999;
	position: fixed;
	left: 0;
	right: 0;
	top: 0;
	bottom: 0;
	margin: auto;
	background-color: #fff;
}
.dialogDiv h4{
	height: 50px;
	line-height: 50px;
	margin: 0;
	text-align: center;
	border-bottom: 1px solid #ddd;
}
.ordernums{
	display: block;
	margin: 20px auto;
	-webkit-appearance: none;
	-moz-appearance: textfield;
}
input::-webkit-outer-spin-button,input::-webkit-inner-spin-button {
    -webkit-appearance: none;
}
input[type="number"]{
    -moz-appearance: textfield;
}
.confirm{
	width: 80px;
	height: 40px;
	line-height: 40px;
	text-align: center;
	display: inline-block;
	border-radius: 5px;
	background-color: #0098d8;
	color: #fff;
	margin-left: 44px;
}
.cancel{
	width: 80px;
	height: 40px;
	line-height: 40px;
	text-align: center;
	display: inline-block;
	border-radius: 5px;
	margin-left: 44px;
	background-color: #d9534f;
	color: #fff;
}


/* 体检日历 */
.data_box{margin: 0px auto; height:100%;}
.datetimepicker {background:#fff;font-family: "微软雅黑"}
.auto {margin: 0 auto;}
.datetimepicker .data-title {position: relative;background-color: #edf4ff;width: 350px; border: 1px solid #eee;text-align: center;}
.datetimepicker .data-title h3 {margin:0 34px; height: 52px; line-height: 52px; color: #666666; text-align: center; font-size:16px; font-weight:normal;}
.datetimepicker .data-title a {position: absolute; top:20px; width: 8px; height: 14px;}
.datetimepicker .data-title .left-btn {left:10px; background: url(../images/listico35.png) no-repeat center center;background-size:100%;}
.datetimepicker .data-title .right-btn {right:10px;background: url(../images/listico34.png) no-repeat center center;background-size:100%;}
.datetimepicker .week_tit{height:40px;line-height:40px; width: 352px; border: 1px solid #eee; margin-left: -1px;}
.datetimepicker .week_tit b{font-weight:normal;background-color: #fff6f6;width: 50px;display:inline-block;text-align:center;font-size:14px; color: #666666;border-bottom: 1px solid #ddd;}
.datetimepicker-pos {position: relative;left:calc(80% - 350px);}
.datetimepicker .data-list {position: relative; height:  auto; overflow: hidden;padding-left: 1px; width: 351px;}
.datetimepicker .data-list a { position: relative; opacity: 1;box-sizing: border-box;
	filter: alpha(opacity=100); display: inline-block;vertical-align: top;  width: 50px;height: 40px; line-height: 40px; text-align: center; color: #9fa0a0;font-size:22px; cursor: pointer; color: #333;border-right: 1px solid #eee;border-bottom: 1px solid #eee;}
.datetimepicker .data-list a::after {
	content: ' ';
	position: absolute;
	height: 100%;
	border-left: 1px solid #ddd;
	left: -1px;
}
.datetimepicker .data-list a {*display: inline; cursor: default;}
.datetimepicker .data-list a.none {border-top: 0;}
.datetimepicker .data-list a.license {cursor: pointer;}
.datetimepicker .data-list a.active { color: #999; }
.datetimepicker .data-list a{color: #999;position: relative;font-size: 16px;background-color: #f8f8f8;}
.datetimepicker .data-list a span{ display:inline-block; position:absolute; left: 27px; bottom: 6px;width: 8px; height: 8px; border-radius: 8px;}
.datetimepicker .data-list a.has:hover { color: #fff;background: url(../images/date-active.png) no-repeat center center; }
.datetimepicker .data-list a.teday {/*color: #09F; border:1px solid #2fadc1;*/}
.datetimepicker .data-list a.has:after {position: absolute;left:50%; margin-left: -3px; bottom:-5px; width:5px; height: 5px; content: ''; background: url(../images/date-has.png);}
.datetimepicker .data-list a.ban{}
.datetimepicker .data-list a.license,.datetimepicker .data-list a.full,.datetimepicker .data-list a.booking{position:relative;}

.datetimepicker .data-list a.license span,
.datetimepicker .data-list a.ban span,
.datetimepicker .data-list a.full span,
.datetimepicker .data-list a.booking span{ display:none;background:url(../images/dataicon.png) no-repeat;position:absolute;}
.datetimepicker .data-list a.license span{ background: #4aabe9; color: #fff;}
.datetimepicker .data-list a.ban span{}
.datetimepicker .data-list a.ban{color: #999;background-color: #f8f8f8;}
.datetimepicker .data-list a.license{color: #2b8bc9;background-color: #fff;}
.datetimepicker .data-list a.ban,.datetimepicker .data-list a.full{}
.datetimepicker .data-list a.full span{background-position:-60px 0;}
.datetimepicker .data-list a.booking span{background-position:-120px 0;}
.datetimepicker .data-list a.booking{background: #4aabe9; color: #fff;}
.Tiplog .Tiplog-main .Tiplog-content{    padding-top: 0!important;}
.datetimepicker .data-list a.full{
    font-size: 14px;
}


.table_zx {

}

.table_zx table {
    margin-bottom: 20px;
}

.table_zx tr td {
    padding: 10px 8px;
    border: 1px solid #ddd;
}

.table_zx tr td.choucha span {
    display: inline-block;
    width: 60px;
    height: 21px;
    margin-left: 5px;
}

.table_zx tr td.choucha span i {
    display: inline-block;
    width: 22px;
    height: 21px;
    background: url(../images/plasce.png) no-repeat;
    margin-left: 5px;
    vertical-align: middle;
}

.table_zx tr td.choucha span i.good {
    background-position: 0px 0px;
}

.table_zx tr td.choucha span i.pingc {
    background-position: -35px 0px;
}

.table_zx tr td.choucha span i.cha {
    background-position: -70px 0px;
}

.table_zx tr th {
    padding: 10px 8px;
    text-align: center;
    color: #428cc5;
    font-size: 13px;
    border: 1px solid #ddd;
    background: #eaeaea;
}

.table_zx tr td strong {
    font-size: 13px;
}
.data_box {
    line-height: 38px;
    height: 38px; text-align: center;
}

.data_box a {
    display: inline-block;
    width: 29px;
    height: 25px;
    vertical-align: middle;
    cursor: pointer;
}

.data_box a.prev_ico {
    background: url(../images/list08.png) no-repeat;
}

.data_box a.next_ico {
    background: url(../images/list09.png) no-repeat;
}

.data_box a.prev_ico:hover {
    background: url(../images/list08cur.png) no-repeat;
}

.data_box a.next_ico:hover {
    background: url(../images/list09cur.png) no-repeat;
}

.data_box h4 {
    display: inline-block;
    font-weight: normal;
    font-size: 18px;
    font-family: '微软雅黑';
    margin: 0 8px;
}


#coursesTable {
    padding:0px;
}

.Courses-head {
    background-color: #edffff;
}

.Courses-head > div {
    text-align: center;
    font-size: 14px;
    line-height: 35px;
}

.left-hand-TextDom, .Courses-head {
    background-color: #f2f6f7;
}

.Courses-leftHand {
    background-color: #f2f6f7;
    font-size: 12px;
}

.Courses-leftHand .left-hand-index {
    color: #9c9c9c; margin-bottom: 4px !important; display: inline-block; width: 20%;
}

.Courses-leftHand .left-hand-name {
    color: #666; display: inline-block; width: 80%; text-align: left;
}

.Courses-leftHand p {
    text-align: center;
    font-weight: 900;
}

.Courses-head > div {
    border-left: none !important;
}

.Courses-leftHand > div {
    padding-top: 5px;
    border-bottom: 1px dashed rgb(219, 219, 219); border-right: 1px dashed rgb(219, 219, 219);
}

.Courses-leftHand > div:last-child {
    border-bottom: none !important;
}

.left-hand-TextDom, .Courses-head {
    border-bottom: 1px solid rgba(0, 0, 0, 0.1) !important;
}

.Courses-content > ul {
    border-bottom: 1px dashed rgb(219, 219, 219);
    box-sizing: border-box;
}

.Courses-content > ul:last-child {
    border-bottom: none !important;
}

.highlight-week {
    color: #02a9f5 !important;
}

.Courses-content li {
    text-align: center;
    color: #666666;
    font-size: 14px;
    line-height: 50px; border-right: 1px dashed rgb(219, 219, 219);
}

.Courses-content li span {
    padding: 6px 2px;
    box-sizing: border-box;
    line-height: 18px;
    white-space: normal;
    word-break: break-all;
    cursor: pointer;
    text-align: left;
    font-size: 10px;
}

.grid-active {
    z-index: 9999;
}

.grid-active span {
    box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.2);
}