.qeditor_border{background: #FFF; text-align: center;position: relative; height: auto;}
.qeditor_toolbar{text-align: left;background: #FFF;margin: 0 auto 3px auto; padding:0px 10px; background:#e5e5e5; height:35px; line-height:35px;}
.qeditor_toolbar span.vline{border-left: 1px solid #bbb;padding-right: 10px;}
.qeditor_toolbar a, .qeditor_toolbar .qe-icon{ margin-top:5px;color: #666;text-decoration: none;font-size: 14px; display:inline-block; height:26px; line-height:26px; padding:0px 7px; background:#FFF;}
.qeditor_toolbar a:hover, .qeditor_toolbar .qe-icon:hover{color: #43be4d;}
.qeditor_toolbar a.qe-state-on{color: #43be4d;}
.qeditor_toolbar i, .qeditor_toolbar b{ font-size:12px; font-weight: bold;font-style: inherit;}
.qeditor_toolbar .qe-heading{position: relative;}
.qeditor_toolbar .qe-heading .qe-menu{width: 120px;display: none;position: absolute;margin: 0;padding: 0;left: -25px;top: 15px;border: 1px solid #eee;border-right: 1px solid #ddd;border-bottom: 1px solid #ddd;z-index: 9991;background: #FFF;list-style: none;}
.qeditor_toolbar .qe-heading .qe-menu li{display: inline;padding: 0;margin: 0;}
.qeditor_toolbar .qe-heading .qe-menu a{display: block;margin: 0;padding: 2px 10px;}
.qeditor_toolbar .qe-heading .qe-menu a:hover{background: #f0f0f0;}
.qeditor_toolbar .qe-heading .qe-menu .qe-h1{font-size: 17px;font-weight: bold;}
.qeditor_toolbar .qe-heading .qe-menu .qe-h2{font-size: 16px;font-weight: bold;}
.qeditor_toolbar .qe-heading .qe-menu .qe-h3{font-size: 15px;font-weight: bold;}
.qeditor_toolbar .qe-heading .qe-menu .qe-h4{font-size: 14px;font-weight: bold;}
.qeditor_toolbar .qe-heading .qe-menu .qe-h5{font-size: 13px;font-weight: bold;}
.qeditor_toolbar .qe-heading .qe-menu .qe-h6{font-size: 12px;font-weight: bold;}
.qeditor_toolbar .qe-heading .qe-menu .qe-p{font-size: 12px;border-top: 1px solid #eee;}
.qeditor_toolbar .qe-heading.hover{background: #eee;}
.qeditor_toolbar .qe-heading.hover .qe-menu{background: #fff;display: block;}
.qeditor_preview{text-align: left;min-height: 80px;margin: 0 auto;overflow-y: scroll;}
.qeditor_placeholder{color: #999;}
.qeditor_fullscreen{position: fixed;background: #FFF;top: 0;left: 0;bottom: 0;right: 0;z-index: 99990;padding: 30px 0;overflow: hidden;}
.qeditor_fullscreen .qeditor_preview{max-width: 900px;box-sizing: border-box;width: 100%;height: 90%;margin-top: 20px;border: 0;-webkit-box-shadow: none;-moz-box-shadow: none;box-shadow: none;-webkit-transition: none;-moz-transition: none;-o-transition: none;transition: none;padding: 0;}
.qeditor_fullscreen .qeditor_toolbar{max-width: 900px;box-sizing: border-box;width: 100%;padding-bottom: 10px;}
.qeditor_fullscreen .qeditor_toolbar a, .qeditor_fullscreen .qeditor_toolbar .qe-icon{font-size: 18px;margin-right: 8px;}
.qeditor_border .textarea{background-color: #ffffff; padding: 4px 6px; font-size: 14px; line-height: 20px; color: #555555; -webkit-border-radius: 4px; -moz-border-radius: 4px; border-radius: 4px; vertical-align: middle; outline: none; height: 319px;}
.qeditor_border .mintextarea{background-color: #ffffff; padding: 4px 6px; font-size: 14px; line-height: 20px; color: #555555; -webkit-border-radius: 4px; -moz-border-radius: 4px; border-radius: 4px; vertical-align: middle; outline: none; height: 120px;}
