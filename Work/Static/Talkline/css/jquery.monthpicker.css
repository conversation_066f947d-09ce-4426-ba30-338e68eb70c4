.monthpicker {
  border: 1px solid #eee;
  padding: 1em;
  top: 6px;
  z-index: 1000;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);
  border-radius: .2em;
  background:#fff;
}

.monthpicker:before {
  content: '';
  border-bottom: 6px solid #fff;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  position: absolute;
  top: -6px;
  left: 6px;
  z-index: 1002;
}

.monthpicker:after {
  content: '';
  border-bottom: 6px solid #ddd;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  position: absolute;
  top: -7px;
  left: 6px;
  z-index: 1001;
}

.monthpicker .years {
  margin-bottom: .8em;
  text-align: center;
}

.monthpicker .years select {
  font-size: 1em;
  width: 100%;
}

.monthpicker .years select:focus {
  outline: none;
}

.monthpicker table {
  border-collapse: collapse;
  table-layout: fixed;
}

.monthpicker td {
  padding: .1em;
}

.monthpicker table button {
  width: 100%;
  border: none;
  background: #CAE3FB;
  font-size: .8em;
  padding: .6em;
  cursor: pointer;
  border-radius: .2em;
  transition: background .2s;
}

.monthpicker table button:hover {
  background: #B1D3F3;
}

.monthpicker table button:focus {
  outline: none;
}

