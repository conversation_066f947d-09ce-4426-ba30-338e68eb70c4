/*!
 * froala_editor v1.2.2 (http://editor.froala.com)
 * Copyright 2014-2014 Froala
 */
!function(a){a.Editable.prototype.refreshBlockStyles=function(){var b=this.getSelectionElements()[0],c=b.tagName.toLowerCase();this.$bttn_wrapper.find(".fr-block-style").empty();var d=this.options.blockStyles[c];if(void 0===d&&(d=this.options.defaultBlockStyle),void 0!==d){this.$bttn_wrapper.find('.fr-dropdown > button[data-name="blockStyle"].fr-trigger').removeAttr("disabled");for(var e in d){var f=d[e],g="";a(b).hasClass(e)&&(g=' class="active"'),this.$bttn_wrapper.find(".fr-block-style").append(a("<li"+g+">").append(a('<a href="#" data-text="true">').text(f).addClass(e)).attr("data-cmd","blockStyle").attr("data-val",e))}}},a.Editable.commands=a.extend(a.Editable.commands,{blockStyle:{title:"Block Style",icon:"fa fa-magic",refreshOnShow:a.Editable.prototype.refreshBlockStyles,callback:function(a,b,c){this.blockStyle(b,c)},undo:!0}}),a.Editable.DEFAULTS=a.extend(a.Editable.DEFAULTS,{defaultBlockStyle:{"f-italic":"Italic","f-typewriter":"Typewriter","f-spaced":"Spaced","f-uppercase":"Uppercase"},blockStylesToggle:!0,blockStyles:{}}),a.Editable.prototype.command_dispatcher=a.extend(a.Editable.prototype.command_dispatcher,{blockStyle:function(a){var b=this.buildDropdownBlockStyle(a),c=this.buildDropdownButton(a,b);return c}}),a.Editable.prototype.buildDropdownBlockStyle=function(){var a='<ul class="fr-dropdown-menu fr-block-style">';return a+="</ul>"},a.Editable.prototype.blockStyle=function(a,b){var c=this.getSelectionElements()[0],d=c.tagName.toLowerCase();this.formatBlock(d,a,b)}}(jQuery);