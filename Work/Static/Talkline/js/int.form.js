/**
 * Created by Mohism on 2016/12/15.
 */
function showRequest(formData, jqForm, options){
    //formData: 数组对象，提交表单时，Form插件会以Ajax方式自动提交这些数据，格式如：[{name:user,value:val },{name:pwd,value:pwd}]
    //jqForm:   jQuery对象，封装了表单的元素
    //options:  options对象
    //var queryString = $.param(formData);   //name=1&address=2
    //var formElement = jqForm[0];              //将jqForm转换为DOM对象
    //var address = formElement.address.value;  //访问jqForm的DOM元素
    return true;  //只要不返回false，表单都会提交,在这里可以对表单元素进行验证
};

function showResponse(responseText, statusText){
    CRMJS.Tiplog.Close();
    eval("var bakjson = " + responseText + ";") ;
    if(bakjson.bakfuntion == 'errormotify'){
        errormotify(bakjson.errortip);
    }else if(bakjson.bakfuntion == 'okmotify'){
        okmotify(bakjson.errortip);
    }else{
        window[bakjson.bakfuntion](bakjson.errortip);
    }
    if(typeof(bakjson.bakurl) !== "undefined" && bakjson.bakurl !== ''){
        window.location=bakjson.bakurl;
    }
};

var clearoptions = {
    //target: '#output',          //把服务器返回的内容放入id为output的元素中
    beforeSubmit: showRequest,  //提交前的回调函数
    success: showResponse,      //提交后的回调函数
    //url: url,                 //默认是form的action， 如果申明，则会覆盖
    //type: type,               //默认是form的method（get or post），如果申明，则会覆盖
    //dataType: null,           //html(默认), xml, script, json...接受服务端返回的类型
    //clearForm: true,          //成功提交后，清除所有表单元素的值
    resetForm: true,          //成功提交后，重置所有表单元素的值
    timeout: 3000               //限制请求的时间，当请求大于3秒后，跳出请求
};
var options = {
    //target: '#output',          //把服务器返回的内容放入id为output的元素中
    beforeSubmit: showRequest,  //提交前的回调函数
    success: showResponse,      //提交后的回调函数
    //url: url,                 //默认是form的action， 如果申明，则会覆盖
    //type: type,               //默认是form的method（get or post），如果申明，则会覆盖
    //dataType: null,           //html(默认), xml, script, json...接受服务端返回的类型
    //clearForm: true,          //成功提交后，清除所有表单元素的值
    //resetForm: true,          //成功提交后，重置所有表单元素的值
    timeout: 3000               //限制请求的时间，当请求大于3秒后，跳出请求
};
$(".clearAjaxForm").ajaxForm(clearoptions);
$(".AjaxForm").submit(function(){
    var isSubmit = true;
    $(this).find("[reg],[url]:not([reg])").each(function(){
        if($(this).attr("reg") == undefined){
            if(!ajax_validate($(this))){
                isSubmit = false;
            }
        }else{
            if(!validate($(this))){
                isSubmit = false;
            }
        }
    });

    if(isSubmit){
        if($(this).find("#SynchType").length > 0){
            if($(this).find("#SynchType").val() == '88' || $(this).find("#SynchType").val() == '99') {
                return true;
            }else{
                $(this).ajaxSubmit(options);
                return false;
            }
        }else{
            $(this).ajaxSubmit(options);
            return false;
        }
    }else{
        return false;
    }
});

//正确提示
function familyok(tip){
    CRMJS.Tiplog.OpenOkTip(tip,"您可根据情况，选择点击下面按钮","/iuser/myfamily","查看列表","继续添加");
}
//正确提示
function infook(tip){
    CRMJS.Tiplog.OpenOkTip(tip,"您可根据情况，选择点击下面按钮","/iuser/","会员中心","继续添加");
}

function validationExportOrder(){
	var len = $("input[name='tab_list[]']:checked").length;
	if(len<= 0){
		errormotify("未选择任何资料！");
		return false;
	}
	
	return true;
}