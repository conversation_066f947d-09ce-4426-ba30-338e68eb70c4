<!DOCTYPE HTML>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>无标题文档</title>
<!-- Bootstrap -->
<link href="css/bootstrap.min.css" rel="stylesheet" type="text/css">
<link href="css/jquery.sidr.light.css" rel="stylesheet" type="text/css">
<link href="css/style.css" rel="stylesheet" type="text/css">
<link href="css/pages.css" rel="stylesheet" type="text/css">
</head>

<body>
<div class="header navbar navbar-inverse">
  <div class="navbar-inner">
    <div class="header-seperation"><a href="/" class="c-f fr dib f16 pt20 pr20"><span class="glyphicon glyphicon-home"></span></a><a class="dib pt8 pl18"><img src="images/logo.png" width="98"></a></div>
    <div class="header-quick-nav">
      <div class="pull-left">
        <ul class="nav quick-section f16">
          <li class="quicklinks"><a class=" c-8 cp" id="layout-condensed-toggle"> <span class="glyphicon glyphicon-align-justify"></span> </a></li>
        </ul>
        <ul class="nav quick-section f16">
          <li class="quicklinks"><a href="#" class="c-8">
            <div class="iconset top-msg-dark "><span class="badge badge-important animated bounceIn" id="chat-message-count">1</span></div>
            </a></li>
          <li class="quicklinks"> <span class="h-seperate"></span></li>
          <li class="m-r-10 input-prepend inside search-form no-boarder"> <span class="add-on f16"><span class="glyphicon glyphicon-search"></span></span>
            <input name="" type="text" class="no-boarder " placeholder="Search Dashboard" style="width:250px;">
          </li>
        </ul>
      </div>
      <div class="pull-right">
        <div class="chat-toggler">
          <div class="profile-pic"><img src="images/img1.jpg" width="35" height="35"></div>
        </div>
        <ul class="nav quick-section ">
          <li class="quicklinks"> <a data-toggle="dropdown" class="dropdown-toggle  pull-right c-8 f16 " href="#" id="user-options"> <span class="glyphicon glyphicon-cog"></span> </a>
            <ul class="dropdown-menu  pull-right" role="menu" aria-labelledby="user-options">
              <li><a href="user-profile.html">我的资料</a> </li>
              <li><a href="calender.html">我的日历</a> </li>
              <li><a href="email.html">我的收件箱<span class="badge badge-important animated bounceIn ml10">2</span></a> </li>
              <li class="divider"></li>
              <li><a href="login.html"><span class="glyphicon glyphicon-off mr10 c-8"></span>退出</a></li>
            </ul>
          </li>
          <li class="quicklinks"> <span class="h-seperate"></span></li>
          <li class="quicklinks"> <a id="chat-menu-toggle" href="#sidr" class="chat-menu-toggle">
            <div class="iconset top-chat-dark "><span class="badge badge-important animated bounceIn" id="chat-message-count">1</span></div>
            </a> </li>
        </ul>
      </div>
    </div>
  </div>
</div>
<div class="page-container row condensed">
  <div class="page-sidebar c-f" id="main-menu">
    <div class="user-info my30 mx30">
      <div class="img fl"><img src="images/img1.jpg" width="69" height="69"></div>
      <div class="text mt16">
        <p class="f19">LINDA</p>
        <p class="mt4"><span class="c-debug mr16">管理</span><a href="" class="c-f"> <span class="status-icon green"></span> 在线</a></p>
      </div>
      <div class="clear"></div>
    </div>
    <ul>
      <li class="first"><a href=""><span class="fr r-links"></span><span class="glyphicon glyphicon-home"></span> <span class="title"></span></a></li>
      <li class="cur"><a href=""><span class="fr r-links"></span><span class="glyphicon glyphicon-cog"></span> <span class="title">系统设置</span></a></li>
      <li><a href=""><span class="fr r-links"></span><span class="glyphicon glyphicon-move"></span> <span class="title">站点管理</span></a></li>
      <li><a href=""><span class="fr r-links"></span><span class="glyphicon glyphicon-user"></span> <span class="title">用户管理</span></a></li>
      <li><a href=""><span class="fr r-links"></span><span class="glyphicon glyphicon-th-large"></span> <span class="title">功能模块</span></a></li>
      <li><a href=""><span class="fr r-links"></span><span class="glyphicon glyphicon-record"></span> <span class="title">接口维护,</span></a></li>
    </ul>
  </div>
  <div class="footer-widget">
    <p align="" class="f16"><a class="cp c-9"><span class="glyphicon glyphicon-off"></span></a></p>
  </div>
  <div class="page-content">
    <div class="content">
      <div id="container">
        <div class="row tc cz-area f14 cz-area-diy">
        	<div class="col-md-1 color1 act-add-ico"><div class="bg-f py16"><a href="">创建活动</a></div></div>
            <div class="col-md-1 color2 act-manage-ico"><div class="bg-f py16"><a href="">活动管理</a></div></div>
            <div class="col-md-1 color3 article-add-ico"><div class="bg-f py16"><a href="">添加文章</a></div></div>
            <div class="col-md-1 color4 article-manage-ico"><div class="bg-f py16"><a href="">文章管理</a></div></div>
            <div class="col-md-1 color5 file-manage-ico"><div class="bg-f py16"><a href="">文件管理</a></div></div>
            <div class="col-md-1 color6 order-manage-ico"><div class="bg-f py16"><a href="">报名管理</a></div></div>
            <div class="col-md-1 color7 payment-manage-ico"><div class="bg-f py16"><a href="">付款信息</a></div></div>
            <div class="col-md-1 color8 member-manage-ico"><div class="bg-f py16"><a href="">会员管理</a></div></div>
        </div>
        <div class="row tc cz-area cz-area-stysm f14">
            <div class="col-md-1 act-add-ico"><div class="bg-f py16"><a href="/Activity/Add"><span class="glyphicon glyphicon-pencil f40"></span><br />创建活动</a></div></div>
            <div class="col-md-1 act-manage-ico"><div class="bg-f py16"><a href="/Activity"><span class="glyphicon glyphicon-file f40"></span><br />活动管理</a></div></div>
            <div class="col-md-1 article-add-ico"><div class="bg-f py16"><a href=""><span class="glyphicon glyphicon-edit f40"></span><br />添加文章</a></div></div>
            <div class="col-md-1 article-manage-ico"><div class="bg-f py16"><a href=""><span class="glyphicon glyphicon-list-alt f40"></span><br />文章管理</a></div></div>
            <div class="col-md-1 file-manage-ico"><div class="bg-f py16"><a href=""><span class=" glyphicon glyphicon-folder-close f40"></span><br />文件管理</a></div></div>
            <div class="col-md-1 order-manage-ico"><div class="bg-f py16"><a href="/Ordering"><span class="glyphicon glyphicon-user f40"></span><br />报名管理</a></div></div>
            <div class="col-md-1 payment-manage-ico"><div class="bg-f py16"><a href="/{$u}/Payment"><span class="glyphicon glyphicon-shopping-cart f40"></span><br />付款信息</a></div></div>
            <div class="col-md-1 member-manage-ico"><div class="bg-f py16"><a href="/Member"><span class=" glyphicon glyphicon-book f40"></span><br />会员管理</a></div></div>
        </div>
        <div class="row mt20">
        	<div class="col-md-4">
            	<div class="tiles green mb10">
              <div class="tiles-body">
                <p class="f16 pt10 pb30"><span class="animate-number f30 fr" data-value="16" data-animation-duration="1200">0</span>未读消息</p>
                <p>系统应用实时提示提醒消息</p>
              </div>
            </div>
            <div class="tiles purple">
              <div class="tiles-body">
                <p class="f16 pt10 pb30"><span class="animate-number f30 fr" data-value="6" data-animation-duration="1200">0</span>未读反馈</p>
                <p>各站点访客信息反馈统计</p>
              </div>
            </div>
            </div>
            <div class="col-md-8 bg-f">
            	<div class="">
                	<div class="title f14 py16">操作日志</div>
                    
                </div>
            </div>
        </div>
      </div>
    </div>
  </div>
  <div id="sidr" class="chat-window-wrapper sidr right">
    <div class="slimScrollDiv" style="position: relative; overflow: hidden; width: auto; height: 900px;">
      <div id="main-chat-wrapper" style="overflow: hidden; width: auto; height: 900px;">
        <div class="chat-window-wrapper fadeIn animated" id="chat-users" style="display: block;">
          <div class="chat-header">
            <div>
              <button type="button" class="fr"><span class="glyphicon glyphicon-search c-9 f14"></span></button>
              <input placeholder="search" type="text" class="">
            </div>
          </div>
          <div class="side-widget fadeIn">
            <div id="favourites-list">
              <div class="side-widget-content">
                <div class="user-details-wrapper active" data-chat-status="online" data-chat-user-pic="images/img2.jpg" data-chat-user-pic-retina="images/img2.jpg" data-user-name="lisa">
                  <div class="user-profile"> <img src="images/img2.jpg" alt="" data-src="images/img2.jpg" data-src-retina="images/img2.jpg" width="35" height="35"> </div>
                  <div class="user-details">
                    <div class="user-name"> lisa </div>
                    <div class="user-more"> 你在哪里？ </div>
                  </div>
                  <div class="user-details-status-wrapper"> <span class="badge badge-important">3</span> </div>
                  <div class="user-details-count-wrapper">
                    <div class="status-icon green" title="在线"></div>
                  </div>
                  <div class="clearfix"></div>
                </div>
                <div class="user-details-wrapper" data-chat-status="busy" data-chat-user-pic="images/img2.jpg" data-chat-user-pic-retina="images/img2.jpg" data-user-name="max">
                  <div class="user-profile"> <img src="images/img1.jpg" alt="" data-src="images/img1.jpg" data-src-retina="images/img1.jpg" width="35" height="35"> </div>
                  <div class="user-details">
                    <div class="user-name"> max </div>
                    <div class="user-more"> 我现在很忙！！ </div>
                  </div>
                  <div class="user-details-status-wrapper">
                    <div class="clearfix"></div>
                  </div>
                  <div class="user-details-count-wrapper">
                    <div class="status-icon red" title="离线"></div>
                  </div>
                  <div class="clearfix"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="chat-window-wrapper fadeIn" id="messages-wrapper" style="display: none;">
          <div class="chat-header">
            <div>
              <button type="button" class="fr"><span class="glyphicon glyphicon-search c-9 f14"></span></button>
              <input placeholder="search" type="text" class="">
            </div>
          </div>
          <div class="clearfix"></div>
          <div class="chat-messages-header">
            <div class="status"></div>
            <span class="semi-bold">lisa</span> <a href="#" class="chat-back"><i class="glyphicon glyphicon-remove"></i></a> </div>
          <div class="chat-messages">
            <div class="sent_time">2012-12-12 11:15</div>
            <div class="user-details-wrapper ">
              <div class="user-profile"> <img src="images/img2.jpg" alt="" data-src="images/img2.jpg" data-src-retina="images/img2.jpg" width="35" height="35"> </div>
              <div class="user-details">
                <div class="bubble"> 你在哪里？ </div>
              </div>
              <div class="clearfix"></div>
              <div class="sent_time off">2012-12-12 11:25</div>
            </div>
            <div class="user-details-wrapper ">
              <div class="user-profile"> <img src="images/img2.jpg" alt="" data-src="images/img2.jpg" data-src-retina="images/img2.jpg" width="35" height="35"> </div>
              <div class="user-details">
                <div class="bubble"> 什么时候开会？ </div>
              </div>
              <div class="clearfix"></div>
              <div class="sent_time off">2012-12-12 12:00</div>
            </div>
            <div class="user-details-wrapper ">
              <div class="user-profile"> <img src="images/img2.jpg" alt="" data-src="images/img2.jpg" data-src-retina="images/img2.jpg" width="35" height="35"> </div>
              <div class="user-details">
                <div class="bubble"> 你闲的时候给我说下 </div>
              </div>
              <div class="clearfix"></div>
              <div class="sent_time off">2012-12-12 13:30</div>
            </div>
            <div class="sent_time ">今天 11:25</div>
            <div class="user-details-wrapper pull-right">
              <div class="user-details">
                <div class="bubble sender"> 你闲的时候给我说下 </div>
              </div>
              <div class="clearfix"></div>
              <div class="sent_time off">Sent On Tue, 2:45pm</div>
            </div>
          </div>
        </div>
        <div class="chat-input-wrapper" style="display: none;">
          <button type="submit" class="fr">发送</button>
          <textarea id="chat-message-input" rows="1" placeholder=""></textarea>
        </div>
        <div class="clearfix"></div>
      </div>
      <!--<div class="slimScrollBar ui-draggable" style="background: rgb(161, 178, 189) none repeat scroll 0% 0%; width: 7px; position: absolute; top: 0px; opacity: 0.4; display: none; border-radius: 7px; z-index: 99; right: 1px; height: 900px;"></div>
      <div class="slimScrollRail" style="width: 7px; height: 100%; position: absolute; top: 0px; display: none; border-radius: 7px; background: rgb(51, 51, 51) none repeat scroll 0% 0%; opacity: 0.2; z-index: 90; right: 1px;"></div>--> 
    </div>
  </div>
</div>
<script src="js/jquery.js"></script> 
<script src="js/bootstrap.min.js"></script> 
<script src="js/jquery.sidr.min.js"></script> 
<script src="js/jquery.animateNumbers.js"></script> 
<script src="js/int.web.js"></script> 
<script src="js/int.chat.js"></script>
</body>
</html>
