/*!
 * froala_editor v1.2.2 (http://editor.froala.com)
 * Copyright 2014-2014 Froala
 */
!function(a){a.Editable.DEFAULTS=a.extend(a.Editable.DEFAULTS,{fontList:["微软雅黑","宋体","隶书","Times New Roman,Times","Trebuchet MS","Verdana,Geneva"]}),a.Editable.prototype.refreshFontFamily=function(){var b=a(this.getSelectionElement());this.$editor.find('.fr-dropdown > button[data-name="fontFamily"] + ul li').removeClass("active"),this.$editor.find('.fr-dropdown > button[data-name="fontFamily"] + ul li[data-val="'+b.css("font-family")+'"]').addClass("active")},a.Editable.commands=a.extend(a.Editable.commands,{fontFamily:{title:"Font Family",icon:"fa fa-font",refreshOnShow:a.Editable.prototype.refreshFontFamily,callback:function(a,b){this.inlineStyle("font-family",a,b)},undo:!0,callbackWithoutSelection:function(a,b){this._startInFontExec("font-family",a,b)}}}),a.Editable.prototype.command_dispatcher=a.extend(a.Editable.prototype.command_dispatcher,{fontFamily:function(a){var b=this.buildDropdownFontFamily(),c=this.buildDropdownButton(a,b,"fr-family");return c}}),a.Editable.prototype.buildDropdownFontFamily=function(){for(var a='<ul class="fr-dropdown-menu">',b=0;b<this.options.fontList.length;b++){var c=this.options.fontList[b],d='<li data-cmd="fontFamily" data-val="'+c+'">';d+='<a href="#" data-text="true" title="'+c+'" style="font-family: '+c+';">'+c.split(",").join(", ")+"</a></li>",a+=d}return a+="</ul>"}}(jQuery);