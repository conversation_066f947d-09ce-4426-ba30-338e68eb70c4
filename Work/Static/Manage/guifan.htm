<!DOCTYPE HTML>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>无标题文档</title>
<!-- Bootstrap -->
<link href="css/editor-awesome.min.css" rel="stylesheet">
<link href="css/bootstrap.min.css" rel="stylesheet" type="text/css">
<link href="css/font-awesome.min.css" rel="stylesheet" type="text/css">
<link href="css/jquery.sidr.light.css" rel="stylesheet" type="text/css">
<link href="css/bootstrap-datetimepicker.css" rel="stylesheet" type="text/css">
<link rel="stylesheet" href="css/jquery.easydropdown.css" type="text/css">
<link rel="stylesheet" href="css/jquery.qeditor.css">
<!-- diy -->
<link href="css/style.css" rel="stylesheet" type="text/css">
<link href="css/pages.css" rel="stylesheet" type="text/css">
</head>

<body>
<div class="pace-activity"></div></div>
<div class="header navbar navbar-inverse">
  <div class="navbar-inner">
    <div class="header-seperation"><a href="/" class="c-f fr dib f16 pt20 pr20"><span class="glyphicon glyphicon-home"></span></a><a class="dib pt8 pl18"><img src="images/logo.png" width="98"></a></div>
    <div class="header-quick-nav">
      <div class="pull-left">
        <ul class="nav quick-section f16">
          <li class="quicklinks"><a class=" c-8 cp" id="layout-condensed-toggle"> <span class="glyphicon glyphicon-align-justify"></span> </a></li>
        </ul>
        <ul class="nav quick-section f16">
          <li class="quicklinks"><a href="#" class="c-8">
            <div class="iconset top-msg-dark "><span class="badge badge-important animated bounceIn" id="chat-message-count">1</span></div>
            </a></li>
          <li class="quicklinks"> <span class="h-seperate"></span></li>
          <li class="m-r-10 input-prepend inside search-form no-boarder"> <span class="add-on f16"><span class="glyphicon glyphicon-search"></span></span>
            <input name="" type="text" class="no-boarder " placeholder="Search Dashboard" style="width:250px;">
          </li>
        </ul>
      </div>
      <div class="pull-right">
        <div class="chat-toggler">
          <div class="profile-pic"><img src="images/img1.jpg" width="35" height="35"></div>
        </div>
        <ul class="nav quick-section ">
          <li class="quicklinks"> <a data-toggle="dropdown" class="dropdown-toggle  pull-right c-8 f16 " href="#" id="user-options"> <span class="glyphicon glyphicon-cog"></span> </a>
            <ul class="dropdown-menu  pull-right" role="menu" aria-labelledby="user-options">
              <li><a href="user-profile.html">我的资料</a> </li>
              <li><a href="calender.html">我的日历</a> </li>
              <li><a href="email.html">我的收件箱<span class="badge badge-important animated bounceIn ml10">2</span></a> </li>
              <li class="divider"></li>
              <li><a href="login.html"><span class="glyphicon glyphicon-off mr10 c-8"></span>退出</a></li>
            </ul>
          </li>
          <li class="quicklinks"> <span class="h-seperate"></span></li>
          <li class="quicklinks"> <a id="chat-menu-toggle" href="#sidr" class="chat-menu-toggle">
            <div class="iconset top-chat-dark "><span class="badge badge-important animated bounceIn" id="chat-message-count">1</span></div>
            </a> </li>
        </ul>
      </div>
    </div>
  </div>
</div>
<div class="page-container row condensed">
<div class="page-sidebar c-f" id="main-menu">
  <div class="user-info my30 mx30">
    <div class="img fl"><img src="images/img1.jpg" width="69" height="69"></div>
    <div class="text mt16">
      <p class="f19">LINDA</p>
      <p class="mt4"><span class="c-debug mr16">管理</span><a href="" class="c-f"> <span class="status-icon green"></span> 在线</a></p>
    </div>
    <div class="clear"></div>
  </div>
  <ul>
    <li class="first"><a href=""><span class="fr r-links"></span><span class="glyphicon glyphicon-home"></span> <span class="title"></span></a></li>
    <li class="cur"><a href=""><span class="fr r-links"></span><span class="glyphicon glyphicon-cog"></span> <span class="title">系统设置</span></a></li>
    <li><a href=""><span class="fr r-links"></span><span class="glyphicon glyphicon-move"></span> <span class="title">站点管理</span></a></li>
    <li><a href=""><span class="fr r-links"></span><span class="glyphicon glyphicon-user"></span> <span class="title">用户管理</span></a></li>
    <li><a href=""><span class="fr r-links"></span><span class="glyphicon glyphicon-th-large"></span> <span class="title">功能模块</span></a></li>
    <li><a href=""><span class="fr r-links"></span><span class="glyphicon glyphicon-record"></span> <span class="title">接口维护,</span></a></li>
  </ul>
</div>
<div class="footer-widget">
  <p align="" class="f16"><a class="cp c-9"><span class="glyphicon glyphicon-off"></span></a></p>
</div>
<div class="page-content">
  <div class="content">
    <div id="container">
      <div class="bg-f row py20">
        <div class="col-md-6">
          <h2>输入框</h2>
          <div class="pl20 py20 f14">
            <p class="c-3 f14">输入框样式</p>
            <p class="c-8 f14 pb20">白背景和黑背景的输入框样式不同。</p>
            <form role="form">
              <div class="form-group">
                <label for="exampleInputEmail1">用户名</label>
                <input type="email" class="form-control" id="exampleInputEmail1" placeholder="Enter email">
              </div>
              <div class="form-group">
                <label for="exampleInputPassword1">密码</label>
                <input type="password" class="form-control" id="exampleInputPassword1" placeholder="Password">
              </div>
              <div class="form-group">
                <label for="area">文本域</label>
                <textarea class="form-control" rows="3"></textarea>
              </div>
              <fieldset disabled>
                <div class="form-group">
                  <label for="disabledTextInput">不可用状态</label>
                  <input type="text" id="disabledTextInput" class="form-control" placeholder="Disabled input">
                </div>
                <div class="checkbox">
                  <label>
                    <input type="checkbox">
                    Can't check this </label>
                </div>
              </fieldset>
            </form>
            <form class="form-horizontal" role="form">
              <div class="form-group">
                <label for="inputEmail3" class="col-sm-2 control-label">用户名</label>
                <div class="col-sm-10">
                  <input type="email" class="form-control" id="inputEmail3" placeholder="Email">
                </div>
              </div>
              <div class="form-group">
                <label for="inputPassword3" class="col-sm-2 control-label">密码</label>
                <div class="col-sm-10">
                  <input type="password" class="form-control" id="inputPassword3" placeholder="Password">
                </div>
              </div>
              <div class="form-group">
                <div class="col-sm-offset-2 col-sm-10">
                  <div class="checkbox">
                    <label>
                      <input type="checkbox">
                      记住我 </label>
                  </div>
                </div>
              </div>
              <div class="form-group">
                <label for="">下拉框</label>
                <select class="form-control">
                  <option>1</option>
                  <option>2</option>
                  <option>3</option>
                  <option>4</option>
                  <option>5</option>
                </select>
              </div>
              <div class="form-group">
                <label for="">下拉选择显示更多</label>
                <select multiple class="form-control">
                  <option>1</option>
                  <option>2</option>
                  <option>3</option>
                  <option>4</option>
                  <option>5</option>
                </select>
                </div>
                <div class="form-group">
                <select class="form-control dropdown">
                <option value="">选择城市</option>
                <option value="AL">上海</option>
                <option value="AK">北京</option>
                <option value="AZ">广州</option>
                <option value="AR">深圳</option>
                <option value="CA">重庆</option>
                <option value="CO">厦门</option>
                <option value="CT">武汉</option>
                <option value="DE">郑州</option>
                <option value="FL">福建</option>
                <option value="GA">东莞</option>
                <option value="HI">中山</option>
                <option value="ID">信阳</option>
                <option value="IL">南阳</option>
                <option value="IN">安阳</option>
                <option value="IA">新疆</option>
                <option value="KS">呼和浩特</option>
                <option value="KY">大连</option>
            </select>
              </div>
            </form>
          </div>
          <h2>多选样式</h2>
          <div class="pl20 py20 f14">
            <div class="checkbox">
              <label>
                <input type="checkbox" value="">
                多选样式 </label>
            </div>
            <div class="checkbox disabled">
              <label>
                <input type="checkbox" value="" disabled>
                多选样式</label>
            </div>
            <div class="radio">
              <label>
                <input type="radio" name="optionsRadios" id="optionsRadios1" value="option1" checked>
                单选样式 </label>
            </div>
            <div class="radio">
              <label>
                <input type="radio" name="optionsRadios" id="optionsRadios2" value="option2">
                单选样式</label>
            </div>
            <div class="radio disabled">
              <label>
                <input type="radio" name="optionsRadios" id="optionsRadios3" value="option3" disabled>
                单选样式</label>
            </div>
            <div class="">
            <label class="control-label no-padding-right blue"> 单选框：  </label>
            <input name="form-field-radio" type="radio" class="ace">
                    <span class="lbl"></span>
          </div>
          <div class="">
            <label class="control-label no-padding-right blue"> 复选框：  </label>
            <input name="form-field-checkbox" type="checkbox" class="ace">
                    <span class="lbl"></span>
          </div>
          <div class="form-group">
          <label class="control-label no-padding-right"> 大号状态（评论框-<span class="blue">默认</span>）：  </label>
          <div class="control-box  debug-gray">
            <div class="box-editor">
             <textarea id="post_body" name="body" class="textarea" placeholder="请输入的用户信息"></textarea>
            </div>
          </div>
        </div>
          </div>
          <h2>输入框提示</h2>
          <div class="pl20 py20 f14">
            <div class="form-group has-success has-feedback">
              <label class="control-label" for="inputSuccess2">输入正确</label>
              <input type="text" class="form-control" id="inputSuccess2">
              <p class="text-success pt6"><span class="glyphicon glyphicon-ok-circle"></span> 输入信息正确</p>
            </div>
            <div class="form-group has-warning has-feedback">
              <label class="control-label" for="inputWarning2">其他输入提醒</label>
              <input type="text" class="form-control" id="inputWarning2">
              <p class="text-warning pt6"><span class="glyphicon glyphicon-exclamation-sign"></span> 请输入内容</p>
            </div>
            <div class="form-group has-error has-feedback">
              <label class="control-label" for="inputError2">输入错误</label>
              <input type="text" class="form-control" id="inputError2">
              <p class="text-danger pt6"><span class="glyphicon glyphicon-remove-circle"></span> 输入信息错误</p>
            </div>
          </div>
          <h2>弹框样式</h2>
          <div class="pl20 py20 f14"> 
            <!-- Button trigger modal -->
            <button type="button" class="btn btn-primary btn-lg" data-toggle="modal" data-target="#myModal"> 点我 </button>
            
            <!-- Modal -->
            <div class="modal fade" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
              <div class="modal-dialog">
                <div class="modal-content">
                  <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
                    <h4 class="modal-title" id="myModalLabel">操作位置提示</h4>
                  </div>
                  <div class="modal-body"> 这里是弹出内容，居中对齐可以两行排版 </div>
                  <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary">保存</button>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <h2>按钮样式</h2>
          <div class="pl20 py20 f14">
            <p class="pb12">
              <button type="button" class="btn btn-primary btn-lg">大按钮</button>
              <button type="button" class="btn btn-danger btn-lg">大按钮</button>
              <button type="button" class="btn btn-warning btn-lg">大按钮</button>
            </p>
            <p class="pb12">
              <button type="button" class="btn btn-primary">默认按钮</button>
              <button type="button" class="btn btn-danger">默认按钮</button>
              <button type="button" class="btn btn-warning">默认按钮</button>
            </p>
            <p class="pb12">
              <button type="button" class="btn btn-primary btn-sm">小按钮</button>
              <button type="button" class="btn btn-danger btn-sm">小按钮</button>
              <button type="button" class="btn btn-warning btn-sm">小按钮</button>
            </p>
            <p class="pb20">
              <button type="button" class="btn btn-primary btn-xs">加小按钮</button>
              <button type="button" class="btn btn-danger btn-xs">加小按钮</button>
              <button type="button" class="btn btn-warning btn-xs">加小按钮</button>
            </p>
            <div class="btn-group">
              <p class="f14 pb12">按钮下拉菜单</p>
              <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown"> Action <span class="caret"></span> </button>
              <ul class="dropdown-menu" role="menu">
                <li><a href="#">Action</a></li>
                <li><a href="#">Another action</a></li>
                <li><a href="#">Something else here</a></li>
                <li class="divider"></li>
                <li><a href="#">Separated link</a></li>
              </ul>
            </div>
            <p class="f14 pb12 pt20">自适应按钮</p>
            <p class="pb12">
              <button type="button" class="btn btn-primary btn-lg btn-block">自适应按钮</button>
              <button type="button" class="btn btn-default btn-lg btn-block">自适应按钮</button>
            </p>
            <p class="f14 pt20">翻页按钮</p>
            <nav>
              <ul class="pagination">
                <li><a href="#">&laquo;</a></li>
                <li><a href="#">1</a></li>
                <li><a href="#">2</a></li>
                <li><a href="#">3</a></li>
                <li><a href="#">4</a></li>
                <li><a href="#">5</a></li>
                <li><a href="#">&raquo;</a></li>
              </ul>
            </nav>
          </div>
        </div>
        <div class="col-md-6">
          <h2>标题文字大小</h2>
          <div class="pl20 py20 f14">
            <h1>H1 特大号标题，如邮件/新闻标题 26px<small class="ml10">副标题</small></h1>
            <h2>H2 大号标题，如站点标题 22px<small class="ml10">副标题</small></h2>
            <h3>H3 中号标题，如数据统计标题  16px<small class="ml10">副标题</small></h3>
            <h4>H4 普通标题，如栏目板块标题 14px<small class="ml10">副标题</small></h4>
          </div>
          <h2>文字大小颜色</h2>
          <div class="pl20 py20 f14">
            <p>默认文字</p>
            <p class="text-primary">蓝色文字</p>
            <p class="text-danger">红色文字</p>
            <p class="text-warning">黄色文字</p>
            <p class="text-muted">灰色文字</p>
              <p class="text-muted">-------------------</p>
              <p class="color1">颜色1</p>
              <p class="color2">颜色2</p>
              <p class="color3">颜色3</p>
              <p class="color4">颜色4</p>
          </div>
          <h2>标签</h2>
          <div class="pl20 py20 f14"> <span class="label label-default">Default</span> <span class="label label-primary">Primary</span> <span class="label label-success">Success</span> <span class="label label-info">Info</span> <span class="label label-warning">Warning</span> <span class="label label-danger">Danger</span> </div>
          <h2>警告框</h2>
          <div class="pl20 py20 f14">
            <div class="alert alert-danger alert-dismissible" role="alert">
              <button type="button" class="close" data-dismiss="alert"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
              <span class="glyphicon glyphicon-remove-circle f16 vam mr6"></span> 错误！请慎重操作或者重新操作</div>
            <div class="alert alert-warning alert-dismissible" role="alert">
              <button type="button" class="close" data-dismiss="alert"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
              <span class="glyphicon glyphicon-exclamation-sign f16 vam mr6"></span> 警告！此处不可直接操作</div>
            <div class="alert alert-success alert-dismissible" role="alert">
              <button type="button" class="close" data-dismiss="alert"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
              <span class="glyphicon glyphicon-ok-circle f16 vam mr6"></span> 操作成功！操作</div>
          </div>
          <h2>进度条</h2>
          <div class="pl20 py20 f14">
            <div class="progress">
              <div class="progress-bar progress-bar-success progress-bar-striped" role="progressbar" aria-valuenow="40" aria-valuemin="0" aria-valuemax="100" style="width: 40%"> <span class="sr-only">40% Complete (success)</span> </div>
            </div>
            <div class="progress">
              <div class="progress-bar progress-bar-info progress-bar-striped" role="progressbar" aria-valuenow="20" aria-valuemin="0" aria-valuemax="100" style="width: 20%"> <span class="sr-only">20% Complete</span> </div>
            </div>
            <div class="progress">
              <div class="progress-bar progress-bar-warning progress-bar-striped" role="progressbar" aria-valuenow="60" aria-valuemin="0" aria-valuemax="100" style="width: 60%"> <span class="sr-only">60% Complete (warning)</span> </div>
            </div>
            <div class="progress">
              <div class="progress-bar progress-bar-danger progress-bar-striped" role="progressbar" aria-valuenow="80" aria-valuemin="0" aria-valuemax="100" style="width: 80%"> <span class="sr-only">80% Complete (danger)</span> </div>
            </div>
          </div>
          <h2>情境效果</h2>
          <div class="pl20 py20 f14">
            <div class="panel panel-primary">
              <div class="panel-heading"> Panel content </div>
              <div class="panel-body">Panel footer</div>
            </div>
          </div>
          <h2>选项卡</h2>
          <div class="pl20 py20 f14"> 
            <!-- Nav tabs -->
            <ul class="nav nav-tabs" role="tablist">
              <li role="presentation" class="active"><a href="#home" role="tab" data-toggle="tab">TAB1</a></li>
              <li role="presentation"><a href="#profile" role="tab" data-toggle="tab">TAB2</a></li>
              <li role="presentation"><a href="#messages" role="tab" data-toggle="tab">TAB3</a></li>
              <li role="presentation"><a href="#settings" role="tab" data-toggle="tab">TAB4</a></li>
            </ul>
            
            <!-- Tab panes -->
            <div class="tab-content">
              <div role="tabpanel" class="tab-pane fade in active" id="home">content1</div>
              <div role="tabpanel" class="tab-pane fade" id="profile">content2</div>
              <div role="tabpanel" class="tab-pane fade" id="messages">content3</div>
              <div role="tabpanel" class="tab-pane fade" id="settings">content4</div>
            </div>
          </div>
          <div class="pl20 py20 f14">
            <div class="panel-group" id="accordion" role="tablist" aria-multiselectable="true">
              <div class="panel panel-default">
                <div class="panel-heading" role="tab" id="headingOne">
                  <h4 class="panel-title"> <a data-toggle="collapse" data-parent="#accordion" href="#collapseOne" aria-expanded="true" aria-controls="collapseOne"> TAB1 </a> </h4>
                </div>
                <div id="collapseOne" class="panel-collapse collapse in" role="tabpanel" aria-labelledby="headingOne">
                  <div class="panel-body"> content1 </div>
                </div>
              </div>
              <div class="panel panel-default">
                <div class="panel-heading" role="tab" id="headingTwo">
                  <h4 class="panel-title"> <a class="collapsed" data-toggle="collapse" data-parent="#accordion" href="#collapseTwo" aria-expanded="false" aria-controls="collapseTwo"> TAB2 </a> </h4>
                </div>
                <div id="collapseTwo" class="panel-collapse collapse" role="tabpanel" aria-labelledby="headingTwo">
                  <div class="panel-body"> content2</div>
                </div>
              </div>
              <div class="panel panel-default">
                <div class="panel-heading" role="tab" id="headingThree">
                  <h4 class="panel-title"> <a class="collapsed" data-toggle="collapse" data-parent="#accordion" href="#collapseThree" aria-expanded="false" aria-controls="collapseThree"> TAB3</a> </h4>
                </div>
                <div id="collapseThree" class="panel-collapse collapse" role="tabpanel" aria-labelledby="headingThree">
                  <div class="panel-body"> content3</div>
                </div>
              </div>
            </div>
          </div>
          <h2>日历</h2>
          <div class="pl20 py20 f14">
            <div class="input-group  date form_datetime">
              <input type="text" class="form-control" value="" readonly>
              <span class="input-group-addon"><span class="glyphicon glyphicon-calendar"></span></span> </div>
          </div>
          
          <p class="f14"><i class="icon-twitter"></i></p>
        </div>
      </div>
      <div class="row">
      <div class="bg-f">
      	<h2 class="p20">表格</h2>
          <div class="p20 f14">
          <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table table-striped tc table-hover">
          <thead>
            <tr>
              <th width="6%">&nbsp;</th>
              <th width="5%">ID</th>
              <th width="34%">名称</th>
              <th width="10%">发布时间</th>
              <th width="6%">浏览</th>
              <th width="6%">图文</th>
              <th width="5%">效果</th>
              <th width="4%">推荐</th>
              <th width="5%">置顶</th>
              <th width="5%">最新</th>
              <th width="14%">操作</th>
            </tr>
            </thead>
            <tbody>
            <tr>
              <td><input name="form-field-checkbox" type="checkbox" class="ace">
                    <span class="lbl"></span></td>
              <td>97</td>
              <td><span>新的活动内容</span></td>
              <td>2016-12-06</td>
              <td><span>11111</span></td>
              <td>无</td>
              <td><a class="cp showico" href="" title="查看效果"></a></td>
              <td><a class="cp"><span class="glyphicon glyphicon-ok-circle f18 text-primary"></span></a></td>
              <td><a class="cp"><span class="glyphicon glyphicon-ok-circle f18 text-primary"></span></a></td>
              <td><a class="cp"><span class="glyphicon glyphicon-ok-circle f18 text-primary"></span></a></td>
              <td><button type="button" class="btn btn-primary btn-sm"><span class="glyphicon glyphicon-pencil c-f"></span> 修改</button> <button type="button" class="btn btn-danger btn-sm"><span class="glyphicon glyphicon-remove c-f"></span> 删除</button></td>
            </tr>
             <tr>
              <td><input name="form-field-checkbox" type="checkbox" class="ace">
                    <span class="lbl"></span></td>
              <td>97</td>
              <td><span>新的活动内容</span></td>
              <td>2016-12-06</td>
              <td>&nbsp;</td>
              <td>无</td>
              <td><a class="cp showico" href="" title="查看效果"></a></td>
              <td><a class="cp"><span class="glyphicon glyphicon-remove-circle f18 text-danger"></span></a></td>
              <td><a class="cp"><span class="glyphicon glyphicon-remove-circle f18 text-danger"></span></a></td>
              <td><a class="cp"><span class="glyphicon glyphicon-remove-circle f18 text-danger"></span></a></td>
              <td><button type="button" class="btn btn-primary btn-sm"><span class="glyphicon glyphicon-pencil c-f"></span> 修改</button> <button type="button" class="btn btn-danger btn-sm"><span class="glyphicon glyphicon-remove c-f"></span> 删除</button></td>
            </tr>
            <tr>
              <td><input name="form-field-checkbox" type="checkbox" class="ace">
                    <span class="lbl"></span></td>
              <td>97</td>
              <td><span>新的活动内容</span></td>
              <td>2016-12-06</td>
              <td><span>11111</span></td>
              <td>无</td>
              <td><a class="cp showico" href="" title="查看效果"></a></td>
              <td><a class="cp"><span class="glyphicon glyphicon-ok-circle f18 text-primary"></span></a></td>
              <td><a class="cp"><span class="glyphicon glyphicon-ok-circle f18 text-primary"></span></a></td>
              <td><a class="cp"><span class="glyphicon glyphicon-ok-circle f18 text-primary"></span></a></td>
              <td><button type="button" class="btn btn-primary btn-sm"><span class="glyphicon glyphicon-pencil c-f"></span> 修改</button> <button type="button" class="btn btn-danger btn-sm"><span class="glyphicon glyphicon-remove c-f"></span> 删除</button></td>
            </tr>
             <tr>
              <td><input name="form-field-checkbox" type="checkbox" class="ace">
                    <span class="lbl"></span></td>
              <td>97</td>
              <td><span>新的活动内容</span></td>
              <td>2016-12-06</td>
              <td>&nbsp;</td>
              <td>无</td>
              <td><a class="cp showico" href="" title="查看效果"></a></td>
              <td><a class="cp"><span class="glyphicon glyphicon-remove-circle f18 text-danger"></span></a></td>
              <td><a class="cp"><span class="glyphicon glyphicon-remove-circle f18 text-danger"></span></a></td>
              <td><a class="cp"><span class="glyphicon glyphicon-remove-circle f18 text-danger"></span></a></td>
              <td><button type="button" class="btn btn-primary btn-sm"><span class="glyphicon glyphicon-pencil c-f"></span> 修改</button> <button type="button" class="btn btn-danger btn-sm"><span class="glyphicon glyphicon-remove c-f"></span> 删除</button></td>
            </tr>
            </tbody>
          </table>

          </div>
          </div>
      </div>
      <div class="row">
      <div class="bg-f">
      	<h2 class="p20"><span class="fr"><button type="button" class="btn btn-primary ml10">返回</button> <button type="button" class="btn btn-primary ml10">提交</button> <button type="button" class="btn btn-primary ml10">保存</button></span>表格</h2>
          <div class="p20 f14">
          <div class="form-group">
           <table width="100%" border="0" cellspacing="0" cellpadding="0">
            <tr>
              <td width="4%">分类：</td>
              <td width="14%"><select class="form-control dropdown">
                <option value="">综合问题</option>
                <option value="AL">上海</option>
                <option value="AK">北京</option>
                <option value="AZ">广州</option>
                <option value="AR">深圳</option>
                <option value="CA">重庆</option>
                <option value="CO">厦门</option>
                <option value="CT">武汉</option>
                <option value="DE">郑州</option>
                <option value="FL">福建</option>
                <option value="GA">东莞</option>
                <option value="HI">中山</option>
                <option value="ID">信阳</option>
                <option value="IL">南阳</option>
                <option value="IN">安阳</option>
                <option value="IA">新疆</option>
                <option value="KS">呼和浩特</option>
                <option value="KY">大连</option>
            </select></td>
              <td width="4%">搜索：</td>
              <td width="15%"><input class="form-control input-sm" id="inputEmail3" placeholder="请输入搜索目标名称或发布时间" type="email"></td>
              <td width="63%"><button type="button" class="btn btn-primary ml10"><span class="glyphicon glyphicon-search c-f"></span> 搜索</button> <button type="button" class="btn btn-primary ml20"><span class="glyphicon glyphicon-plus c-f"></span> 添加文章</button></td>
            </tr>
          </table>
   </div>
          <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table tc table-hover table-bordered">
          <thead>
            <tr>
              <th width="6%">&nbsp;</th>
              <th width="5%">ID</th>
              <th width="34%">名称<span class="dib sort"><a class="up cur"></a><a class="down"></a></span></th>
              <th width="10%">发布时间<span class="dib sort"><a class="up"></a><a class="down cur"></a></span></th>
              <th width="6%">浏览</th>
              <th width="6%">图文</th>
              <th width="5%">效果</th>
              <th width="4%">推荐</th>
              <th width="5%">置顶</th>
              <th width="5%">最新</th>
              <th width="14%">操作</th>
            </tr>
            </thead>
            <tbody>
            <tr>
              <td><input name="form-field-checkbox" type="checkbox" class="ace">
                    <span class="lbl"></span></td>
              <td>97</td>
              <td><span class="edit-input">新的活动内容</span></td>
              <td>2016-12-06</td>
              <td><span>11111</span></td>
              <td>无</td>
              <td><a class="cp showico" href="" title="查看效果"></a></td>
              <td><a class="cp"><span class="glyphicon glyphicon-ok-circle f18 text-primary"></span></a></td>
              <td><a class="cp"><span class="glyphicon glyphicon-ok-circle f18 text-primary"></span></a></td>
              <td><a class="cp"><span class="glyphicon glyphicon-ok-circle f18 text-primary"></span></a></td>
              <td><button type="button" class="btn btn-primary btn-sm"><span class="glyphicon glyphicon-pencil c-f"></span> 修改</button> <button type="button" class="btn btn-danger btn-sm"><span class="glyphicon glyphicon-remove c-f"></span> 删除</button></td>
            </tr>
             <tr>
              <td><input name="form-field-checkbox" type="checkbox" class="ace">
                    <span class="lbl"></span></td>
              <td>97</td>
              <td><span class="edit-input">新的活动内容</span></td>
              <td>2016-12-06</td>
              <td>&nbsp;</td>
              <td>无</td>
              <td><a class="cp showico" href="" title="查看效果"></a></td>
              <td><a class="cp"><span class="glyphicon glyphicon-remove-circle f18 text-danger"></span></a></td>
              <td><a class="cp"><span class="glyphicon glyphicon-remove-circle f18 text-danger"></span></a></td>
              <td><a class="cp"><span class="glyphicon glyphicon-remove-circle f18 text-danger"></span></a></td>
              <td><button type="button" class="btn btn-primary btn-sm"><span class="glyphicon glyphicon-pencil c-f"></span> 修改</button> <button type="button" class="btn btn-danger btn-sm"><span class="glyphicon glyphicon-remove c-f"></span> 删除</button></td>
            </tr>
            <tr>
              <td><input name="form-field-checkbox" type="checkbox" class="ace">
                    <span class="lbl"></span></td>
              <td>97</td>
              <td><span class="edit-input">新的活动内容</span></td>
              <td>2016-12-06</td>
              <td><span>11111</span></td>
              <td>无</td>
              <td><a class="cp showico" href="" title="查看效果"></a></td>
              <td><a class="cp"><span class="glyphicon glyphicon-ok-circle f18 text-primary"></span></a></td>
              <td><a class="cp"><span class="glyphicon glyphicon-ok-circle f18 text-primary"></span></a></td>
              <td><a class="cp"><span class="glyphicon glyphicon-ok-circle f18 text-primary"></span></a></td>
              <td><button type="button" class="btn btn-primary btn-sm"><span class="glyphicon glyphicon-pencil c-f"></span> 修改</button> <button type="button" class="btn btn-danger btn-sm"><span class="glyphicon glyphicon-remove c-f"></span> 删除</button></td>
            </tr>
             <tr>
              <td><input name="form-field-checkbox" type="checkbox" class="ace">
                    <span class="lbl"></span></td>
              <td>97</td>
              <td><span>新的活动内容</span></td>
              <td>2016-12-06</td>
              <td>&nbsp;</td>
              <td>无</td>
              <td><a class="cp showico" href="" title="查看效果"></a></td>
              <td><a class="cp"><span class="glyphicon glyphicon-remove-circle f18 text-danger"></span></a></td>
              <td><a class="cp"><span class="glyphicon glyphicon-remove-circle f18 text-danger"></span></a></td>
              <td><a class="cp"><span class="glyphicon glyphicon-remove-circle f18 text-danger"></span></a></td>
              <td><button type="button" class="btn btn-primary btn-sm"><span class="glyphicon glyphicon-pencil c-f"></span> 修改</button> <button type="button" class="btn btn-danger btn-sm"><span class="glyphicon glyphicon-remove c-f"></span> 删除</button></td>
            </tr>
            </tbody>
          </table>

          </div>
          </div>
      </div>

        <div class="bg-f row py20">
            <form action="/{$u}?c={$act}" role="form" method="post" enctype="application/x-www-form-urlencoded" class="AjaxForm">
                <input name="list_id" type="hidden" value="{$dataVar.list_id}">
                <input name="list_id" type="hidden" value="{$dataVar.list_id}">
                <h2 class="p20">{$moduleOne.module_name}<span class="fr">
                <button type="submit" class=" btn btn-success btn-cons"><i class="fa fa-check"></i>提交保存</button>
                <button type="button" class="btn btn-primary ml10 bakFromurl">返回</button></span>
                </h2>
                <div class="col-md-6" id="Form-Box-Operating">
                    <div class="pl20 py20 f14 col-md-12">
                        <div class="form-group col-md-12">
                            <label for="list_title">广告名称</label>
                            <input name="list_title" id="list_title" value="{$dataVar.list_title}" type="text" class="form-control" placeholder="请输入广告名称">
                        </div>
                        <div class="form-group col-md-8">
                            <label for="place_id">所属广告位</label>
                            <select name="place_id" id="place_id" class="form-control">
                                {foreach from=$placeList item=placeVar}
                                <option value="{$placeVar.site_id}">{$placeVar.site_title}->{$placeVar.place_name}->{$placeVar.place_width}px*{$placeVar.place_height}px</option>
                                {/foreach}
                            </select>
                        </div>
                        <div class="form-group col-md-4">
                            <label for="list_weight">排序</label>
                            <input name="list_weight" id="list_weight" value="{$dataVar.list_weight}" type="text" class="form-control" placeholder="越小越在前">
                        </div>
                        <div class="form-group">
                        <div class="col-md-9">
                            <label for="list_img">图片上传</label>
                            <input name="list_img" id="list_img" value="{$dataVar.list_img}" type="text" class="form-control" placeholder="先点击右侧按钮上传图片，或直接书写图片网址">
                            </div>
                            <div class="col-md-3">
                            <label for="list_img">&nbsp;</label>
                            <a data-color="rgb(255, 255, 255)" data-color-format="hex" id="cp4" class="btn btn-primary my-colorpicker-control upload-btn" data-colorpicker-guid="1">上传图片<input type="file"></a>
                            </div>
                        </div>
                        <div class="form-group col-md-12">
                            <label for="list_imgthum">缩略图</label>
                            <input name="list_imgthum" id="list_imgthum" value="{$dataVar.list_imgthum}" type="text" class="form-control" placeholder="通过系统上传图片后自动获得"readonly>
                        </div>
                        <div class="form-group col-md-12">
                            <label for="list_outlink">广告链接</label>
                            <input name="list_outlink" id="list_outlink" value="{$dataVar.list_outlink}" type="text" class="form-control" placeholder="请输入广告链接，如：http://xxx.com/">
                        </div>
                        <div class="form-group col-md-12">
                            <label for="list_mediaurl">媒体视频连接</label>
                            <input name="list_mediaurl" id="list_mediaurl" value="{$dataVar.list_mediaurl}" type="text" class="form-control" placeholder="请输入媒体视频连接，如：http://xxx.com/text.mp3">
                        </div>
                        <div class="form-group col-md-12">
                            <label for="list_intro">广告设置或内容</label>
                            <textarea name="list_intro" id="list_intro" class="form-control" rows="3">{$dataVar.list_intro}</textarea>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="upload-img">
                    <div class="f16 tc none">
                    	<p class="f30"><span class="glyphicon glyphicon-picture"></span></p>
                        <p>上传图片</p>
                    </div>
                    <div class="img"><img src="{$ImgUrl}img3.jpg"></div>
                    </div>
                </div>
            </form>
        </div>
    </div>
  </div>
  <div id="sidr" class="chat-window-wrapper sidr right">
    <div class="slimScrollDiv" style="position: relative; overflow: hidden; width: auto; height: 900px;">
      <div id="main-chat-wrapper" style="overflow: hidden; width: auto; height: 900px;">
        <div class="chat-window-wrapper fadeIn animated" id="chat-users" style="display: block;">
          <div class="chat-header">
            <div>
              <button type="button" class="fr"><span class="glyphicon glyphicon-search c-9 f14"></span></button>
              <input placeholder="search" type="text" class="">
            </div>
          </div>
          <div class="side-widget fadeIn">
            <div id="favourites-list">
              <div class="side-widget-content">
                <div class="user-details-wrapper active" data-chat-status="online" data-chat-user-pic="images/img2.jpg" data-chat-user-pic-retina="images/img2.jpg" data-user-name="lisa">
                  <div class="user-profile"> <img src="images/img2.jpg" alt="" data-src="images/img2.jpg" data-src-retina="images/img2.jpg" width="35" height="35"> </div>
                  <div class="user-details">
                    <div class="user-name"> lisa </div>
                    <div class="user-more"> 你在哪里？ </div>
                  </div>
                  <div class="user-details-status-wrapper"> <span class="badge badge-important">3</span> </div>
                  <div class="user-details-count-wrapper">
                    <div class="status-icon green" title="在线"></div>
                  </div>
                  <div class="clearfix"></div>
                </div>
                <div class="user-details-wrapper" data-chat-status="busy" data-chat-user-pic="images/img2.jpg" data-chat-user-pic-retina="images/img2.jpg" data-user-name="max">
                  <div class="user-profile"> <img src="images/img1.jpg" alt="" data-src="images/img1.jpg" data-src-retina="images/img1.jpg" width="35" height="35"> </div>
                  <div class="user-details">
                    <div class="user-name"> max </div>
                    <div class="user-more"> 我现在很忙！！ </div>
                  </div>
                  <div class="user-details-status-wrapper">
                    <div class="clearfix"></div>
                  </div>
                  <div class="user-details-count-wrapper">
                    <div class="status-icon red" title="离线"></div>
                  </div>
                  <div class="clearfix"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="chat-window-wrapper fadeIn" id="messages-wrapper" style="display: none;">
          <div class="chat-header">
            <div>
              <button type="button" class="fr"><span class="glyphicon glyphicon-search c-9 f14"></span></button>
              <input placeholder="search" type="text" class="">
            </div>
          </div>
          <div class="clearfix"></div>
          <div class="chat-messages-header">
            <div class="status"></div>
            <span class="semi-bold">lisa</span> <a href="#" class="chat-back"><i class="glyphicon glyphicon-remove"></i></a> </div>
          <div class="chat-messages">
            <div class="sent_time">2012-12-12 11:15</div>
            <div class="user-details-wrapper ">
              <div class="user-profile"> <img src="images/img2.jpg" alt="" data-src="images/img2.jpg" data-src-retina="images/img2.jpg" width="35" height="35"> </div>
              <div class="user-details">
                <div class="bubble"> 你在哪里？ </div>
              </div>
              <div class="clearfix"></div>
              <div class="sent_time off">2012-12-12 11:25</div>
            </div>
            <div class="user-details-wrapper ">
              <div class="user-profile"> <img src="images/img2.jpg" alt="" data-src="images/img2.jpg" data-src-retina="images/img2.jpg" width="35" height="35"> </div>
              <div class="user-details">
                <div class="bubble"> 什么时候开会？ </div>
              </div>
              <div class="clearfix"></div>
              <div class="sent_time off">2012-12-12 12:00</div>
            </div>
            <div class="user-details-wrapper ">
              <div class="user-profile"> <img src="images/img2.jpg" alt="" data-src="images/img2.jpg" data-src-retina="images/img2.jpg" width="35" height="35"> </div>
              <div class="user-details">
                <div class="bubble"> 你闲的时候给我说下 </div>
              </div>
              <div class="clearfix"></div>
              <div class="sent_time off">2012-12-12 13:30</div>
            </div>
            <div class="sent_time ">今天 11:25</div>
            <div class="user-details-wrapper pull-right">
              <div class="user-details">
                <div class="bubble sender"> 你闲的时候给我说下 </div>
              </div>
              <div class="clearfix"></div>
              <div class="sent_time off">Sent On Tue, 2:45pm</div>
            </div>
          </div>
        </div>
        <div class="chat-input-wrapper" style="display: none;">
          <button type="submit" class="fr">发送</button>
          <textarea id="chat-message-input" rows="1" placeholder=""></textarea>
        </div>
        <div class="clearfix"></div>
      </div>
      <!--<div class="slimScrollBar ui-draggable" style="background: rgb(161, 178, 189) none repeat scroll 0% 0%; width: 7px; position: absolute; top: 0px; opacity: 0.4; display: none; border-radius: 7px; z-index: 99; right: 1px; height: 900px;"></div>
      <div class="slimScrollRail" style="width: 7px; height: 100%; position: absolute; top: 0px; display: none; border-radius: 7px; background: rgb(51, 51, 51) none repeat scroll 0% 0%; opacity: 0.2; z-index: 90; right: 1px;"></div>--> 
    </div>
  </div>
</div>
</div>
<script src="js/jquery.js"></script> 
<script src="js/bootstrap.min.js"></script> 
<script src="js/jquery.sidr.min.js"></script> 
<script src="js/jquery.animateNumbers.js"></script> 
<script type="text/javascript" src="js/bootstrap-datetimepicker.js" charset="UTF-8"></script> 
<script type="text/javascript" src="js/bootstrap-datetimepicker.zh-CN.js" charset="UTF-8"></script> 
<script type="text/javascript"  src="js/jquery.easydropdown.js"></script>
<script type="text/javascript"  src="js/jquery.qeditor.js"></script>
<script type="text/javascript"  src="js/pace.min.js"></script>
<script src="js/int.web.js"></script> 
<script src="js/int.chat.js"></script> 
<script type="text/javascript">
    $(".form_datetime").datetimepicker({
      format: "yyyy-mm-dd hh:ii",
      autoclose: true,
      language:'zh-CN',
      pickerPosition:"bottom-left"
    });
  </script>
 <script type="text/javascript">
$("#post_body").qeditor({});
</script>
</body>
</html>
