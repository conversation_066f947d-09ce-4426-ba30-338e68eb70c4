/*分页*/
.pagemenu { padding:35px 0; text-align:center; height:30px;line-height:30px;color:#333;font-family:'微软雅黑';}
.pagemenu a { padding:4px 8px; border:1px solid #ccc;  margin:0 5px; color:#666; text-decoration:none;background:#f2f2f2;border-radius:3px;}
.pagemenu a:hover {border: #2a70ab 1px solid; color:#fff;background:#2a70ab;border-radius:3px; }
.pagemenu a:active {border: #4bb5e1 1px solid; color:#4bb5e1;border-radius:5px;}
.pagemenu .current { padding:4px 8px;  margin:0 5px;background:#2a70ab; color:#fff; border:1px solid #2a70ab; font-weight:bold;border-radius:3px;}
.pagemenu .disabled {border: #ddd 1px solid; padding:4px 8px; color:#999; margin:2px;}

/*信息提示框*/
.errormotify {
    position: fixed;
    top: 35%;
    left: 50%;
    width: 220px;
    padding: 0;
    margin: 0 0 0 -110px;
    z-index: 9999;
    background: #ea4335;
    color: #fff;
    font-size: 14px;
    line-height: 1.5em;
    border-radius: 6px;
    -webkit-box-shadow: 0px 1px 2px rgba(0,0,0,0.2);
    box-shadow: 0px 1px 2px rgba(0,0,0,0.2);
}
.errormotify .motify-inner {
    padding: 10px 10px;
    text-align: center;
    word-wrap: break-word;
}
.errormotify .motify-inner span{display:inline-block;width:22px;height:23px;background:url(../images/error.png) no-repeat;background-size:100%; vertical-align:middle;margin-right:0.6rem;}
.okmotify {
    position: fixed;
    top: 25%;
    left: 50%;
    width: 220px;
    padding: 0;
    margin: 0 0 0 -110px;
    z-index: 9999;
    background: #01b4bf;
    color: #fff;
    font-size: 14px;
    line-height: 1.5em;
    border-radius: 6px;
    -webkit-box-shadow: 0px 1px 2px rgba(0,0,0,0.2);
    box-shadow: 0px 1px 2px rgba(0,0,0,0.2);
}
.okmotify .motify-inner {
    padding: 10px 10px;
    text-align: center;
    word-wrap: break-word;
}
.okmotify .motify-inner span{display:inline-block;width:22px;height:23px;background:url(../images/tsok.png) no-repeat;background-size:100%; vertical-align:middle;margin-right:0.6rem;}

/**购车弹窗**/
.Tiplog{ position: fixed; left: 0; top: 0; width: 100%; height: 100%; z-index: 200; _position: absolute; _height: expression(eval(document.body.scrollHeight));}
.Tiplog .Tiplog-top{padding:0.8rem 1rem;border-bottom:1px solid #ddd;font-size:14px;color:#888;}
.Tiplog .Tiplog-top .Tiplog-Close-but{display:inline-block;width:15px;height:14px; cursor: pointer; background-size:100%; vertical-align:middle;float:right;margin-top:4px;}
.Tiplog .Tiplog-Curtain{background: #000;opacity:0.3; filter: alpha(opacity=30); position:absolute; width:100%; height:100%;}
.Tiplog .Tiplog-main{position:absolute; width:20%; left:50%; top:10%; -webkit-transform: translateX(-50%); transform: translateX(-50%); border-radius:15px; min-height:220px; background:#FFF;}
.Tiplog .Tiplog-main .Tiplog-content{ min-height:180px;padding-top:16px;}
.Tiplog .Tiplog-main .Tiplog-content span.danger{ display:block; height:100px; font-size: 80px; line-height: 100px; text-align: center; color: #e76e70; width:20%; margin:0px auto;}
.Tiplog .Tiplog-main .Tiplog-content span.warning{ display:block; height:100px; font-size: 80px; line-height: 100px; text-align: center; color: #ce8f22; width:20%; margin:0px auto;}
.Tiplog .Tiplog-main .Tiplog-content span.success{ display:block; height:100px; font-size: 80px; line-height: 100px; text-align: center; color: #3c763d; width:20%; margin:0px auto;}
.Tiplog .Tiplog-main .Tiplog-content .tip-text{ margin:10px;}
.Tiplog .Tiplog-main .Tiplog-content .tip-text .tip-title{ font-size:18px; text-align:center; line-height:30px;}
.Tiplog .Tiplog-main .Tiplog-content .tip-text .tip-txt{ font-size:14px; color:#999; text-align:center; line-height:30px;}
.Tiplog .Tiplog-main .Tiplog-content .LogView{ text-align: center;}
.Tiplog .Tiplog-main .Tiplog-content .LogView > * {
  width: 100%;
}
.Tiplog .Tiplog-main .Tiplog-content .LogView img{ max-width: 95%;}

.Tiplog .Tiplog-main .Tiplog-content select{ line-height:32px; height:32px; border:1px solid #ddd; border-radius:3px;}

.Tiplog .Tiplog-main .Tiplog-footer{border-top:1px solid #dddddd; padding:20px; text-align:center;}
.Tiplog .Tiplog-main .Tiplog-footer .but{ display: inline-block;width:30%; text-align:center; line-height:35px; font-size:1rem; border-radius:10px; margin:0px 10px; cursor: pointer;}
.Tiplog .Tiplog-main .Tiplog-footer .but-red{ color:#ea4334;  border:2px solid #ea4334;}
.Tiplog .Tiplog-main .Tiplog-footer .but-black{ color:#999;  border:2px solid #ccc;}

.UpimgBox .uploadimg{background: rgba(0, 0, 0, 0.03) none repeat scroll 0 0;border: 1px solid rgba(0, 0, 0, 0.03);border-radius: 3px;height: 200px;width: 92%; margin:20px;position:relative;}
.UpimgBox .uploadimg > div.tc{position:absolute;left:0;width:100%;top:50%;margin-top:-30px;}
.UpimgBox .uploadimg img{max-width:100%;max-height:450px;}
.UpimgBox .imgpreview{position:absolute;left:0; right: 0; width: 100%; height: 100%;background: rgba(0, 0, 0, 0.03) none repeat scroll 0 0; text-align: center;}
.UpimgBox .imgpreview img{ max-width: 100%; max-width: 200px}