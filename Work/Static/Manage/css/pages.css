/* CSS Initializing */
@charset "utf-8";

/**login**/
.body-main{}
.login-box{padding-top:100px;}
.login-form{background:rgba(228,239,246,0.9);border-radius:3px;border:1px solid #fff;width:370px;margin:56px auto;padding:50px 40px;}
.login-form .input-mk .mks{background:#289cce;border:1px solid #fff;height:24px;line-height:24px;padding:10px 7px;border-radius:3px;}
.login-form .input-mk .mks .userico,.login-form .input-mk .mks .keyico{display:inline-block;width:22px;height:24px; vertical-align:middle;padding:0 10px;border-right:1px solid #7ec4e2;}
.login-form .input-mk .mks .userico{background:url(../images/userico.png) no-repeat center center;}
.login-form .input-mk .mks .keyico{background:url(../images/keyico.png) no-repeat center center;}
.login-form .input-mk .mks .inputtip{border:none;background:none;width:285px;min-height: 24px !important;
    padding: 0 10px !important;height:24px;line-height:24px;color:#fff;}
.login-form .input-mk .mks .inputtip:-moz-placeholder { /* Mozilla Firefox 4 to 18 */color: #fff;  }

.login-form .input-mk .mks .inputtip::-moz-placeholder { /* Mozilla Firefox 19+ */color: #fff;}

.login-form .input-mk .mks .inputtip:-ms-input-placeholder {color: #fff;}

.login-form .input-mk .mks .inputtip::-webkit-input-placeholder {color: #fff;}
.login-form .input-mk .login-btn{font-size:18px;color:#fff;width:100%;border-radius:3px;border:1px solid #fff;background:#f8c00c;height:48px;cursor:pointer;}
.login-form .input-mk .login-btn:hover{filter:alpha(opacity=70); /*IE滤镜，透明度50%*/-moz-opacity:0.7; /*Firefox私有，透明度50%*/opacity:0.7;}

/***header*/
.header{width:100%;position:fixed; z-index:999;min-height:60px;border:none;border-radius:0;}
.navbar{background:#fff;}
.header .navbar-inner {width: 100%;}
.header .header-seperation {width: 250px;display: block;float: left;height: 60px;}
.header-seperation {background-color: #22262e;}
.navbar, .navbar-inverse{}
.header .header-quick-nav {padding-right: 10px;}
.header .quick-section { margin-left: 20px;margin-top: 16px;padding-right: 10px; display: inline-block;float: left;}
.header .quick-section .quicklinks > a {padding: 4px 2px !important; -webkit-user-select:none;
    -moz-user-select:none;
    -ms-user-select:none;
    user-select:none;}
.header .quick-section .quicklinks > a:hover,.header .quick-section .quicklinks > a:focus{background:none;}
.border-right-gray{border-right:1px solid #ddd;}
.header .nav > li{margin: 0px 10px 0 0 ; padding: 0px; text-align: center; display: inline-block; float: left;}
.header .search-form{display: inline-block; float: left; margin-left: 0px !important; top: -4.5px; position: relative;}
.input-prepend .add-on{background-color: #fff;position: relative; left: 3px; border-radius: 2px; padding-left: 8px; padding-right: 4px; padding-top: 4px; top: 0px; transition: border 0.2s linear 0s, box-shadow 0.2s linear 0s,color 0.2s linear 0s, box-shadow 0.2s linear 0s, background 0.2s linear 0s; color: #9ea9b2;float: left; height: 37px; min-width: 34px; padding: 4px 5px; font-weight: normal; line-height: 24px; text-align: center;}
.input-prepend .add-on:first-child{border:none;}
.input-prepend, .input-append{border:none;}
.input-prepend .add-on.input-focus{background-color:#f4f5f7}
.header .chat-toggler{position: relative; top: 10px; margin-right: 5px; display: inline-block; float: left;}
.header .chat-toggler .profile-pic{display: inline-block; border-radius: 100px 100px 100px 100px; display: inline-block; height: 35px; overflow: hidden; width: 35px; float: left;}
.dropdown-menu{position: absolute; top: 100%; left: 0; z-index: 1000; display: none; float: left; list-style: none; text-shadow: none; -webkit-box-shadow: 0px 0px 5px rgba(86, 96, 117, 0.20); -moz-box-shadow: 0px 0px 5px rgba(86, 96, 117, 0.20); box-shadow: 0px 0px 5px rgba(86, 96, 117, 0.20); border: none; border-radius: 3px; padding: 0px; font-size: 13px;}
.dropdown-menu li{padding-left: 0px;}
.dropdown-menu > li > a{line-height: 25px; color: #2a2e36; margin: 4px; border-radius: 3px; text-align: left;}
.header .nav .h-seperate{height: 6px; width: 2px; margin: 0px 10px 0px 10px; border-left: 1px solid #e1e1e1; display: inline; position: relative; top: 2px;}
.iconset.top-chat-dark{background:url(../images/friends.png) no-repeat; width: 19px; height: 17px;}
.iconset.top-msg-dark{background:url(../images/msg.png) no-repeat; width: 15px; height: 20px;}
.iconset.top-search{height: 16px; top: 5px; width: 16px;}
.label-important, .badge-important{background-color: #f35958; color: #fff;}
.iconset .badge{top: -12px; right: -10px; position: relative; padding: 4px 6px; font-size: 10px;}
.navbar .nav > li > .dropdown-menu::before{border-bottom: 7px solid rgba(0, 0, 0, 0.2); border-left: 7px solid transparent; border-right: 7px solid transparent; content: ""; display: inline-block; position: absolute; right: 9px; top: -7px;}
.navbar .nav > li > .dropdown-menu::after{border-bottom: 6px solid white; border-left: 6px solid transparent; border-right: 6px solid transparent; content: ""; display: inline-block; position: absolute; right: 10px; top: -6px;}
/**content*/
.page-container { margin: 0px; padding: 0px; position: relative;height: 100%;}
/***left*/
.page-sidebar {background-color: #1b1e24 !important; height: 100%;}
.user-info {display: block;}
.user-info .img {height: 65px; width: 65px; border-radius: 100px;overflow: hidden; display: inline-block; margin-right: 11px; margin-top: 9px;}
.user-info .text { display: inline-block;}
.page-sidebar > ul {list-style: none;margin: 0; padding: 0; margin: 0; padding: 0;}
.page-sidebar > ul > li.first{display:none;}
.page-sidebar > ul > li > a {display: block;position: relative;margin: 0; border: 0px;padding: 16px 31px 16px 31px;text-decoration: none;font-size: 14px;font-weight: normal;color:#b0bac4;}
.page-sidebar > ul > li > a  span{ vertical-align:middle;}
.page-sidebar > ul > li > a  span.glyphicon{margin-right:6px;}
.page-sidebar > ul > li > a:hover,.page-sidebar > ul > li.open > a{color:#fff;}
.r-links{border-left: 1px solid #a1a1a1; border-top: 1px solid #a1a1a1;content: ""; display: inline-block; height: 8px; margin: 0 5px 5px; position: relative; top: 6px;transform: rotate(135deg); width: 8px;-webkit-transform: rotate(135deg);}
.status-icon{background: rgba(0, 0, 0, 0) url("../images/status.png") no-repeat scroll 0 0; display: inline-block; margin-right: 10px; position: relative; top: 2px;}
.status-icon.green{background-position: -1px -1px; height: 14px; width: 14px;}
.status-icon.red{background-position: -17px -1px; height: 14px; width: 14px;}
.status-icon.blue{background-position: -33px -1px; height: 14px; width: 14px;}
.status-icon.yellow{background-position: -48px -1px; height: 14px; width: 15px;}
.status-icon.grey{background-position: -64px -1px; height: 14px; width: 14px;}

.open{position: relative;}
.navbar .pull-right > li.open > .dropdown-menu, .navbar .nav > li.open > .dropdown-menu.pull-right{margin-right: -6px; margin-top: 7px;}
.page-sidebar > ul > li > ul.sub-menu{background-color: #22262e; clear: both; display: none; list-style: outside none none; margin: 0; padding: 8px 0 10px;}
.page-sidebar > ul > li.active > ul.sub-menu{display: block;}
.page-sidebar > ul > li > ul.sub-menu > li{background: rgba(0, 0, 0, 0) none repeat scroll 0 0; margin-bottom: 0; margin-left: 0; margin-right: 0; margin-top: 1px !important; padding: 0;}
.page-sidebar > ul > li > ul.sub-menu > li > a{background: rgba(0, 0, 0, 0) none repeat scroll 0 0; color: #e1eaf1; display: block; font-size: 13px; font-weight: 300; margin: 0; padding-bottom: 5px; padding-left: 53px !important; padding-right: 0; padding-top: 5px; text-decoration: none; text-shadow: 0 1px 1px #000;}
.page-sidebar > ul > li > ul.sub-menu > li ul.sub-menu{clear: both; display: none; list-style: outside none none; margin: 0; padding-left: 0;}
.page-sidebar > ul > li > ul.sub-menu li > a > .arrow::before{content: ""; display: inline; float: right; font-family: FontAwesome; font-size: 16px; font-weight: 300; height: auto; margin-right: 20px; margin-top: 1px; text-shadow: none;}
.page-sidebar > ul > li > ul.sub-menu li > a > .arrow.open::before{content: ""; display: inline; float: right; font-family: FontAwesome; font-size: 16px; font-weight: 300; height: auto; margin-right: 18px; margin-top: 1px; text-shadow: none;}
.page-sidebar > ul > li.active > ul.sub-menu > li ul.sub-menu{display: block;}
.page-sidebar > ul > li > ul.sub-menu > li ul.sub-menu li{background: rgba(0, 0, 0, 0) none repeat scroll 0 0; margin: 0; padding: 0;}
.page-sidebar > ul > li > ul.sub-menu li > ul.sub-menu > li > a{color: #ccc; display: block; font-size: 13px; font-weight: 300; margin: 0; padding: 5px 0; text-decoration: none; text-shadow: 0 1px 1px #000;}
.page-sidebar > ul > li > ul.sub-menu > li > ul.sub-menu > li > a{padding-left: 70px;}
.page-sidebar > ul > li > ul.sub-menu > li > ul.sub-menu > li > ul.sub-menu > li > a{padding-left: 80px;}
.page-sidebar > ul > li > ul.sub-menu li > ul.sub-menu > li > a > i{font-size: 13px;}
.chat-window-wrapper{overflow: hidden;}
.page-sidebar > ul > li > ul.sub-menu > li > a:hover {background: #1b1e24 none repeat scroll 0 0 !important;}
/***footer*/
.footer-widget {position: absolute; bottom: 0px;display: block;padding: 11px 21px; background-color: #22262e; width: 250px; clear: both;text-align:right;}
.footer-widget.fotmini{width:50px;padding:11px 0;text-align:center;background-color:#20a5dd;}
.footer-widget.fotmini a{color:#fff;}
/***main*/
.page-content{margin-top: 0px; padding: 0px; background-color: #e5e9ec; overflow: auto; position: relative;min-height: 911px;}
.page-content.minbox{margin-left: 0px;}
.page-content.condensed{margin-left: 50px;}
.page-content .content{padding-left: 26px; padding-right: 26px; padding-top: 83px;}
.tiles{background-color: #bcbcbc; color: #fff; position: relative;}
.spacing-bottom{margin-bottom:16px;}
.added-margin{margin-right: -15px;}
.tiles.blue{background-color: #0090d9;}
.tiles.green{background-color: #00b7ac;}
.tiles.red{background-color: #d83c4b;}
.tiles.purple{background-color: #7d6294;}
.tiles .tiles-body{padding: 19px 18px 15px 24px;}
.cz-rz li{margin-bottom:10px;}
.cz-rz li.bg1{background:#e3f3fa;}
.cz-rz li.bg2{background:#fcede8;}
.cz-rz li.bg3{background:#e1faf7;}
.cz-rz li .img{height: 35px; width: 35px; margin-top:0;}
/***mini 菜单*/
.page-sidebar.mini{width: 50px;}
.page-sidebar.mini > ul{padding: 60px 0 0; width: 50px;}
.page-sidebar.mini > ul > li.first{display:block;}
.page-sidebar.mini > ul > li > a{color: #fff; padding: 13px;text-align:center;font-size:18px;}
.page-sidebar.mini > ul > li a i{color: #fff;}
.page-sidebar.mini > ul > li:hover > a,.page-sidebar.mini > ul > li.cur > a{background:#008bc5;}
.page-sidebar.mini .user-info{display: none;}
.page-sidebar.mini > p{display: none;}
.page-sidebar.mini .status-widget{display: none;}
.page-sidebar.mini .notification-alert{display: none;}
.page-sidebar.mini ul li span.title{display: none;}
.page-sidebar.mini ul li span.r-links{display: none;}
.page-sidebar.mini ul li ul li span.arrow{display: inline;}
.page-sidebar.mini ul li span.badge{display: none;}
.page-sidebar.mini .profile-wrapper{display: none;}
.page-sidebar.mini > .side-bar-widgets{display: none;}
.page-sidebar.mini .footer-widget{display: none;}
.page-sidebar.mini{background-color: #0098d8 !important; height: 100%; margin-top: 0; position: absolute;}

/***对话框**/
.chat-window-wrapper{overflow: hidden;}
.sidr{background-color: #171818; box-shadow: none;}
.sidr ul.chat-window li{border: 0 none;}
.sidr ul.chat-window li:hover > a, .sidr ul.chat-window li:hover > span, .sidr ul.chat-window li.active > a, .sidr ul.chat-window li.active > span, .sidr ul.chat-window li.sidr-class-active > a, .sidr ul.chat-window li.sidr-class-active > span{background-color: #fed8db; box-shadow: none; line-height: 16px;}
.sidr ul.chat-window li:nth-child(2n+1){background-color: #fff;}
.sidr ul.chat-window li:nth-child(2n){background-color: #f5f6f8;}
.chat-window-wrapper{ font-size: 13px;}
.chat-window-wrapper .chat-header{height: 58px;background:#282828;}
.chat-window-wrapper input[type="text"], .chat-window-wrapper input[type="password"], .chat-window-wrapper input[type="date"], .chat-window-wrapper input[type="datetime"], .chat-window-wrapper input[type="email"], .chat-window-wrapper input[type="number"], .chat-window-wrapper input[type="search"], .chat-window-wrapper input[type="tel"], .chat-window-wrapper input[type="time"], .chat-window-wrapper input[type="url"], .chat-window-wrapper textarea, .chat-window-wrapper select{background-color: #0d0f12; height: 28px;}
.chat-header {padding: 15px 16px 15px 16px;}
.chat-header div{background:#0d0f12;height:30px;}
.chat-header input[type="text"]{ width: 196px;border-radius:2px 0 0 2px;height:30px;min-height:30px;}
.chat-header button{background:#0d0f12;border:none;height:30px; vertical-align:top;border-radius:0 2px 2px 0;}
.chat-header .iconset{margin: 20px 15px;}
.chat-window-wrapper .user-details-wrapper{border-radius: 3px; display: block; margin: 8px; padding: 10px;}
.chat-window-wrapper .chat-messages .user-details-wrapper{padding: 6px;}
.chat-window-wrapper .chat-messages .user-details-wrapper:hover{background-color: #22262e; cursor: auto;}
.chat-window-wrapper .user-details-wrapper .status-icon{margin-right: 0;}
.chat-window-wrapper .user-details-wrapper.active{background-color: #372b32;}
.chat-window-wrapper .user-details-wrapper.active:hover{background-color: #482f36; cursor: pointer;}
.chat-window-wrapper .user-details-wrapper:hover{background-color: #2a2e36; cursor: pointer;}
.chat-window-wrapper .user-details-wrapper > .user-profile{border-radius: 100px; display: inline-block; float: left; height: 35px; margin-right: 13px; margin-top: 8px; overflow: hidden; width: 35px;}
.chat-window-wrapper .user-details-wrapper > .user-details{display: inline-block; float: left;}
.chat-window-wrapper .user-details-wrapper > .user-details > .user-name{color: #fff; display: block;}
.chat-window-wrapper .user-details-wrapper > .user-details > .user-more{color: #b0bac4; display: block; font-size: 11px; width: 120px;}
.chat-window-wrapper .user-details-status-wrapper{display: inline-block; float: left; margin-top: 8px; min-width: 32px;}
.chat-window-wrapper .user-details-count-wrapper{display: inline-block; float: left; margin-top: 8px;}
.chat-messages-header{background-color: #1b1e24; color: #fff; padding: 8px 8px 8px 21px;font-size:14px;}
.chat-messages-header .status{background-color: #e5e9ec; border-radius: 8px; display: inline-block; height: 10px; margin-right: 12px; width: 10px;}
.chat-messages-header .status.online{background-color: #0aa699;}
.chat-messages-header .status.busy{background-color: #f35958;}
.chat-messages-header .status.away{background-color: #0aa699;}
.chat-messages-header a > i{color: #ecf0f2; float: right; font-size: 14px; margin-right: 5px; margin-top: 5px;}
.chat-footer{background-color: #e5e9ec; bottom: 0; padding: 10px 10px 0; position: absolute; width: 92.6%;}
.user-profile img{border-radius: 100px;}
.bubble{background: #e5e9ec none repeat scroll 0 0; border-radius: 3px; color: #22262e; min-height: 40px; padding: 10px; position: relative; width: 165px;}
.bubble{cursor: pointer;}
.bubble.old{background: #0d0f12 none repeat scroll 0 0; color: #4c5264;}
.bubble.sender{background: #0098d8 none repeat scroll 0 0; color: #fff;}
.chat-messages .sent_time{color: #4c5264; font-weight: 600; margin-top: 10px; text-align: center; width: 100%;}
.chat-messages .sent_time.off{display: none;}
.bubble::after{border-color: transparent #e5e9ec; border-style: solid; border-width: 9px 7px 9px 0; content: ""; display: block; left: -7px; position: absolute; top: 12px; width: 0; z-index: 1;}
.bubble.old::after{border-color: transparent #0d0f12;}
.bubble.sender::after{border-color: transparent #0098d8; border-width: 9px 0 9px 7px; left: auto; right: -7px !important;}
.chat-input-wrapper{background-color: #1b1e24; bottom: 0; padding: 8px 10px; position: fixed; width: 260px;}
.chat-input-wrapper textarea{background-color: #fff; margin: 0; padding: 0;width:196px;}
.chat-input-wrapper button[type="submit"]{color:#fff;background:none;border:none;height:30px;}
.user-chat-wrapper{display: block; padding: 10px 15px;}
.user-chat-wrapper .profile-wrapper{border-radius: 100px; display: inline-block; float: left; height: 35px; margin: 0 10px 0 0; overflow: hidden; width: 35px;}
.user-chat-wrapper .user-chat{display: inline-block; float: left;}
.user-chat-wrapper .user-chat .user-name{color: #22262e; font-size: 12px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;}
.user-chat-wrapper .user-chat .user-chat-preview{display: block; float: left;}
.user-chat-wrapper .user-chat .more-details{color: #8b8f92; display: inline-block; float: left; font-size: 12px; margin-right: 10px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;}

/**input***/
input.no-boarder {border: medium none;}
input.dark {background-color: #e5e9ec;border: medium none;}
input {border: 1px solid #cecece;}
textarea {height: auto;padding-left: 12px !important;padding-top: 10px !important;}
.uneditable-input.focus, textarea:focus, select:focus, input[type="text"]:focus, input[type="password"]:focus, input[type="datetime"]:focus, input[type="datetime-local"]:focus, input[type="date"]:focus, input[type="month"]:focus, input[type="time"]:focus, input[type="week"]:focus, input[type="number"]:focus, input[type="email"]:focus, input[type="url"]:focus, input[type="search"]:focus, input[type="tel"]:focus, input[type="color"]:focus, .m-uneditable-input:focus {background-color: #f4f5f7;border-color: #e5e9ec !important;box-shadow: none;outline: 0 none !important;}
input[type="radio"], input[type="checkbox"] {box-sizing: border-box;cursor: pointer;line-height: normal;margin: 4px 0 0;}
select, input[type="file"] {height: 34px !important;line-height: 30px;}
select { background-color: #ffffff; background-image: none !important; border: 1px solid #e5e5e5; filter: none !important; min-height: 37px;outline: medium none; width: 220px;}
select[multiple], select[size] {height: auto !important;}
select:focus, input[type="file"]:focus, input[type="radio"]:focus, input[type="checkbox"]:focus{box-shadow: none !important; outline: thin dotted #333333; outline-offset: -2px;}
input[class*="span"]{float: none; height: 34px !important; margin-left: 0;}
select[class*="span"], textarea[class*="span"]{float: none; margin-left: 0;}
.input-lg{border-radius: 3px !important; font-size: 18px !important; height: 45px !important; line-height: 1.33 !important;}
.input-sm{font-size: 13px !important;}
.input-prepend, .input-append{border: 0 none; display: inline-block;}
.input-append .add-on, .input-prepend .add-on{ border: 1px solid #e5e5e5; display: inline-block; font-weight: normal; line-height: 24px; min-width: 16px; padding: 4px 5px; text-align: center; text-shadow: 0 1px 0 #ffffff; width: auto;}
.input-append .add-on > .halflings-icon, .input-prepend .add-on > i, .input-prepend .add-on > .fa{margin-left: 3px; margin-top: 5px;}
.input-append .add-on > .halflings-icon .input-append .add-on > i, .input-append .add-on > .fa{margin-left: 0; margin-top: 5px;}
.input-append, .input-append *:focus{border-right: 0 none !important;}
.input-append input[class*="span"], .input-prepend input[class*="span"], .input-append input, .input-prepend input{display: inline-block !important; float: left; width: 100%;}
.uneditable-input,
textarea,
input[type="text"],
input[type="password"],
input[type="datetime"],
input[type="datetime-local"],
input[type="date"],
input[type="month"],
input[type="time"],
input[type="week"],
input[type="number"],
input[type="email"],
input[type="url"],
input[type="search"],
input[type="tel"],
input[type="color"]{-webkit-appearance: none !important;color: #282323 ;outline: 0; height: 16px; padding: 6px 11px !important; line-height: 15px; font-size: 13px; font-weight: normal; vertical-align: top; background-color:#fff; min-height: 37px; filter: none !important; -webkit-box-shadow: none !important; -moz-box-shadow: none !important; box-shadow: none !important; -webkit-border-radius: 0px; -moz-border-radius: 0px; border-radius: 0px; -webkit-border-radius: 2px;
-moz-border-radius: 2px;
border-radius: 2px;
transition: background 0.2s linear 0s, box-shadow 0.2s linear 0s;}
input.no-boarder{border: none;}
label, .lbl{vertical-align: middle;}
input[type=checkbox].ace, input[type=radio].ace{opacity: 0;position: absolute;z-index: 12;width: 18px;height: 18px;cursor: pointer;}
.radio input[type="radio"], .radio-inline input[type="radio"], .checkbox input[type="checkbox"], .checkbox-inline input[type="checkbox"]{float: left;margin-left: -20px;}
input[type="radio"], input[type="checkbox"]{margin: 4px 0 0;margin-top: 1px \9;line-height: normal;}
input[type="checkbox"], input[type="radio"]{padding: 0;box-sizing: border-box;}
input[type=checkbox].ace+.lbl, input[type=radio].ace+.lbl{position: relative;z-index: 11;display: inline-block;margin: 0;line-height: 20px;min-height: 18px;min-width: 18px;font-weight: normal;}
input[type=checkbox].ace+.lbl::before, input[type=radio].ace+.lbl::before{font-family: fontAwesome;font-weight: normal;font-size: 12px;color: #32a3ce;content: "\a0";background-color: #fafafa;border: 1px solid #c8c8c8;box-shadow: 0 1px 2px rgba(0,0,0,0.05);border-radius: 0;display: inline-block;text-align: center;vertical-align: top;height: 16px;line-height: 14px;min-width: 16px;margin-right: 1px;}
input[type=checkbox].ace:checked+.lbl::before, input[type=radio].ace:checked+.lbl::before{display: inline-block;content: '\f00c';background-color: #f5f8fc;border-color: #adb8c0;box-shadow: 0 1px 2px rgba(0,0,0,0.05),inset 0 -15px 10px -12px rgba(0,0,0,0.05),inset 15px 10px -12px rgba(255,255,255,0.1);}
input[type=radio].ace+.lbl::before{border-radius: 100%;font-size: 11px;font-family: FontAwesome;text-shadow: 0 0 1px #32a3ce;line-height: 16px;height: 17px;min-width: 17px;}
input[type=radio].ace:checked+.lbl::before {content: "\f111";}
input[type=checkbox].ace:hover+.lbl::before, input[type=radio].ace:hover+.lbl::before, input[type=checkbox].ace+.lbl:hover::before, input[type=radio].ace+.lbl:hover::before{border-color: #ff893c;}
input[type=checkbox].ace:hover+.lbl::before, input[type=radio].ace:hover+.lbl::before, input[type=checkbox].ace+.lbl:hover::before, input[type=radio].ace+.lbl:hover::before{border-color: #ff893c;}
input[type=checkbox].ace:disabled+.lbl::before, input[type=radio].ace:disabled+.lbl::before, input[type=checkbox].ace[disabled]+.lbl::before, input[type=radio].ace[disabled]+.lbl::before, input[type=checkbox].ace.disabled+.lbl::before, input[type=radio].ace.disabled+.lbl::before{background-color: #DDD!important;border-color: #CCC!important;box-shadow: none!important;color: #BBB;}
input[type=checkbox].ace:disabled+.lbl::before, input[type=radio].ace:disabled+.lbl::before, input[type=checkbox].ace[disabled]+.lbl::before, input[type=radio].ace[disabled]+.lbl::before, input[type=checkbox].ace.disabled+.lbl::before, input[type=radio].ace.disabled+.lbl::before{background-color: #DDD!important;border-color: #CCC!important;box-shadow: none!important;color: #BBB;}
.radio, .checkbox{display: block;min-height: 20px; vertical-align: middle;}
/**个人资料***/
.friends-list{height:800px;overflow-y:auto;}
.data-head{position:relative;}
.data-head .tx-img{position:absolute;height:110px;border-radius:120px;left:20px;bottom:-50px;}
.data-head .tx-img img{width:110px;height:110px;border-radius:120px;;border:5px solid #fff;}
.data-head .tx-img span{font-size:18px;margin-top:10px;}
.data-show{padding:100px 0 40px;}
.friends-list li .img{height: 35px; width: 35px; margin-top:0;margin-right:12px;}
.friends-list li{padding:12px 16px;}
.friends-list li:nth-of-type(odd){background:#ecf0f2;}
.friends-list li:hover{background:#e1e4e8;}
/**单站点管理***/
.cz-area .col-md-1{width:12%;}
.color1 {color:#0098d8;}
.color2 {color:#00b7ac;}
.color3 {color:#d83c4b;}
.color4 {color:#7d6294;}
/**自定义图片***/
.cz-area-diy .col-md-1 a{display:block;}
.cz-area-diy .col-md-1.act-add-ico a{background:url(../images/editico1.png) no-repeat center top;background-size:26% auto;padding-top:32%;}
.cz-area-diy .col-md-1.act-manage-ico a{background:url(../images/editico2.png) no-repeat center top;background-size:26% auto;padding-top:32%;}
.cz-area-diy .col-md-1.article-add-ico a{background:url(../images/editico3.png) no-repeat center top;background-size:26% auto;padding-top:32%;}
.cz-area-diy .col-md-1.article-manage-ico a{background:url(../images/editico4.png) no-repeat center top;background-size:26% auto;padding-top:32%;}
.cz-area-diy .col-md-1.file-manage-ico a{background:url(../images/editico5.png) no-repeat center top;background-size:26% auto;padding-top:32%;}
.cz-area-diy .col-md-1.order-manage-ico a{background:url(../images/editico6.png) no-repeat center top;background-size:26% auto;padding-top:32%;}
.cz-area-diy .col-md-1.payment-manage-ico a{background:url(../images/editico7.png) no-repeat center top;background-size:26% auto;padding-top:32%;}
.cz-area-diy .col-md-1.member-manage-ico a{background:url(../images/editico8.png) no-repeat center top;background-size:26% auto;padding-top:32%;}
.cz-area-diy a:hover{filter:alpha(opacity=70); /*IE滤镜，透明度50%*/-moz-opacity:0.7; /*Firefox私有，透明度50%*/opacity:0.7;}
.cz-area-diy span{display:block;padding:0% 0 12%;}

.nav-tabs{background:#ecf0f2;border-radius:4px 4px 0 0;}
.datetimepicker th.dow,.datetimepicker th.prev,.datetimepicker th.next{color:#0098d8;}
.tab-content{border:1px solid #ddd;border-top:none;padding:10px;}
.index-weblist .img{background:#0098d8;color:#fff;position:relative;}
.index-weblist .webtitle{font-size:18px;padding:16px 20px;}
.index-weblist .img span{position:absolute;right:10px;bottom:10px;display:inline-block;padding:0 10px;border:1px solid #fff;}
.table > thead > tr > th{font-weight:normal;padding:12px 8px;}
.table > tbody > tr > td{color:#666;}
.table-bordered > thead > tr > th{background:#f9f9f9;}
.showico{display:inline-block;width:19px;height:19px;background:url(../images/showico.png) no-repeat;}
.videoIco{display:inline-block;width:19px;height:19px;background:url(../images/videoIco.png) no-repeat;}
.audioIco{display:inline-block;width:20px;height:20px;background:url(../images/audioIco.png) no-repeat;background-size: 100% 100%;cursor: pointer;}
.audioIco.pause{background:url(../images/audioIco-ing.png) no-repeat;}
.audioIco.playing { background:url(../images/audioPlaying.png) no-repeat; }

.showico:hover{background:url(../images/showicocur.png) no-repeat;}
.edit-input{border: 1px dashed #0098d8;display: block;height: 28px;line-height: 26px;}
.edit-input:hover{background:url(../images/penico.png) no-repeat 98% center; border: 1px solid #FC0;}
.edit-input.edok{ border:1px solid #3C3; background:url(../images/ok.png) no-repeat 90% center;}
.edit-input.edrun{ border:1px solid #FC0;}
/**插件库图标***/
.bs-glyphicons {margin: 0 -10px 20px;overflow: hidden;}
.bs-glyphicons-list {list-style: outside none none;padding-left: 0;}
.bs-glyphicons li {background-color: #f9f9f9;border: 1px solid #fff;float: left;font-size: 10px;height: 115px;line-height: 1.4;padding: 10px; text-align: center;width: 25%;}
.bs-glyphicons .glyphicon {font-size: 24px; margin-bottom: 10px; margin-top: 5px;}
.bs-glyphicons .glyphicon-class { display: block;overflow-wrap: break-word; text-align: center;}
.bs-glyphicons li:hover {background-color: #563d7c; color: #fff;}
.sort { vertical-align:middle;margin-left:4px;}
.sort a{display:block;width:15px;height:8px;margin:0px 0 3px;cursor:pointer;}
.sort a.up{background:url(../images/list1.png) no-repeat;}
.sort a.down{background:url(../images/list2.png) no-repeat;}
.sort a.up:hover,.sort a.up.cur{background:url(../images/list1cur.png) no-repeat;}
.sort a.down:hover,.sort a.down.cur{background:url(../images/list2cur.png) no-repeat;}
.upload-btn{position:relative;width:100%; text-align: center;}
.upload-btn input{position:absolute;left:0;top:0;width:100%;filter:alpha(opacity=0); /*IE滤镜，透明度50%*/-moz-opacity:0; /*Firefox私有，透明度50%*/opacity:0;cursor:pointer;}
.upload-img{background: rgba(0, 0, 0, 0.03) none repeat scroll 0 0;border: 1px solid rgba(0, 0, 0, 0.03);border-radius: 3px;min-height: 360px; padding: 23px;position:relative;}
.upload-img > div.tc{position:absolute;left:0;width:100%;top:50%;margin-top:-30px;}
.upload-img > div.tf{position:absolute;left:0;width:100%;top:10%;margin-top:-30px;}
.upload-img img{max-width:100%;max-height:450px;}

.FileList{ width: 100%;min-height: 120px;}
.FileList li{ width: 16%; margin: 2%; float: left; text-align: center; border: 1px solid #ddd; padding: 2%; overflow: hidden;}
.FileList li span{font-size: 80px; color: #0098D8;}

[class*="span"]{float: left; margin-left: 20px; min-height: 1px;}.span12{width: 940px;float:none;}
.span3{width: 220px;}
.the-icons{list-style-type: none; margin: 0;}
.the-icons li{border-radius: 6px; cursor: pointer; height: 32px; line-height: 32px; padding-left: 12px;}
.the-icons li [class^="icon-"], .the-icons li [class*=" icon-"]{font-size: 18px; width: 32px;}
.the-icons li:hover{background-color: #fbf4f4;}
.the-icons li:hover [class^="icon-"], .the-icons li:hover [class*=" icon-"]{}
.the-icons li:hover [class^="icon-"]::before, .the-icons li:hover [class*=" icon-"]::before{font-size: 28px; vertical-align: -5px;}
/**加载进度条***/
.pace{-moz-user-select: none; pointer-events: none;}
.pace-inactive{display: none;}
.pace .pace-progress{background: #0098d8 none repeat scroll 0 0; height: 2px; left: 0; position: fixed; top: 0; transition: width 1s ease 0s; z-index: 2000;}
.pace .pace-progress-inner{box-shadow: 0 0 10px #0098d8, 0 0 5px #0098d8; display: block; height: 100%; opacity: 1; position: absolute; right: 0; transform: rotate(3deg) translate(0px, -4px); width: 100px;}
.pace .pace-activity{-moz-border-bottom-colors: none; -moz-border-left-colors: none; -moz-border-right-colors: none; -moz-border-top-colors: none; animation: 400ms linear 0s normal none infinite running pace-spinner; border-color: #0098d8 transparent transparent #0098d8; border-image: none; border-radius: 10px; border-style: solid; border-width: 2px; display: block; height: 14px; position: fixed; right: 15px; top: 15px; width: 14px; z-index: 2000;}
@keyframes pace-spinner {
0% {
    transform: rotate(0deg);
}
100% {
    transform: rotate(360deg);
}
}
@keyframes pace-spinner {
0% {
    transform: rotate(0deg);
}
100% {
    transform: rotate(360deg);
}
}
@keyframes pace-spinner {
0% {
    transform: rotate(0deg);
}
100% {
    transform: rotate(360deg);
}
}
@media (min-width: 768px) {
.bs-glyphicons {
    margin-left: 0;
    margin-right: 0;
}
.bs-glyphicons li {
    font-size: 12px;
    width: 12.5%;
}
}

@media only screen and (min-width: 1824px){
.footer-widget { position: fixed;}
.page-container {height: auto;}
}
@media (min-width: 980px){
.page-sidebar {  display: inline-block;float: left;position: relative;width: 250px;margin-top: 58px;}
.page-content {margin-left: 250px;min-height: 1006px;}
.page-sidebar ul {width: 250px;}
}
/* 主题规划增加 */
.exchange-select-group .form-control {
    min-height: 300px !important;
}
.exchange-line {
    border-bottom: 1px solid #ddd;
    height: 20px;
    margin-bottom: 20px;
}
/*  */
.p1p24{
    padding: 1px 24px 10px;
}
.f14 {
    font-size: 14px;
}
.f16 {
    font-size: 16px;
}
/*  */
.scroll-w{
  height:180px;
}
.ItemList{
  height:160px;
}
/* 编辑器闪烁调整 */
.edui-default .edui-editor:before{
  content:'';
  display: block;
  height:110px;
}
.edui-default .edui-editor-toolbarbox{
      position: absolute !important;
      top: 0 !important;
  }

/* 新增题目 type11-type21 */
/* type21 */
.upload-img.none + .upload-img.add {
    display: block;
}
.upload-img.add {
    display: none;
}
.upload-img.extend .close {
    position: absolute;
    right: 5px;
    z-index: 1;
}
#operateType19 .upload-img.extend:nth-child(odd) {
    border-right: none;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}
#operateType19 .upload-img.extend:nth-child(even) {
    border-left: none;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}
#operateType17.auto .type-txt .upload-img {
    min-height: auto !important;
}
#operateType17.auto .type-img .upload-img {
    min-height: 160px !important;
}
#operateType17 .type-txt .tf {
    display: none;
}
#operateType17 .type-txt .upload-img.add {
    min-height: 86px !important;
}
#operateType17 .type-txt .add .tf {
    display: block;
}
#operateType17 .type-img input[name="answers_option[]"] {
    display: none;
}