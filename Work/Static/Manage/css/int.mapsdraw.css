body {
    height: 100%;
    overflow: hidden;
}
.okmotify {
    position: fixed;
    left: 50%;
    top: 50%;
    z-index: 10;
    transform: translate(-50%, -50%);
    background: #0098d8;
    color: #fff;
    line-height: 40px;
    padding: 0 15px;
    border-radius: 5px;
}
.draft {
    width: auto;
    border: 1px solid #ddd;
}
.draft .control {
    padding: 10px 0 10px 20px;
    line-height: 40px;
    border-bottom: 1px solid #ddd;
    font-size: 0;
}
.draft .control label {
    position: relative;
    display: inline-block;
    height: 40px;
    overflow: hidden;
    border: 1px solid #ddd;
    border-radius: 3px;
    margin-right: 15px;
    padding: 0 15px;
    color: #666;
    vertical-align: middle;
    font-size: 16px;
    cursor: pointer;
}
.draft .control label .s {
    display: none;
}
.draft .control label.open .f {
    display: none;
}
.draft .control label.open .s {
    display: inline;
}
.draft .control .maindecor + label {
    display: none;
}
.draft .control label.maindecor.open {
    margin: 0;
    border-right: none;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}
.draft .control label.maindecor.open + label {
    display: inline-block;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}
.draft .control button {
    height: 42px;
    padding: 0 15px;
    border: none;
    font-size: 16px;
    background: #0098d8;
    color: #fff;
    vertical-align: middle;
    border-radius: 5px;
    margin-right: 20px;
}
.draft .control .tip {
    font-size: 16px;
}
.draft .control .tip .o,
.draft .control .tip .x {
    display: inline-block;
    margin-right: 5px;
    background: rgba(0, 0, 0, 0.3);
    color: #fff;
    font-size: 16px;
    line-height: 24px;
    width: 24px;
    text-align: center;
    vertical-align: middle;
}
.draft .control .tip span {
    cursor: pointer;
}
.draft .control .tip .x {
    border-radius: 50%;
}
.draft .control label input {
    position: absolute;
    top: -40px;
    left: 0;
}
.draft .sketchpad {
    position: relative;
    width: 100%;
    overflow-x: auto;
    overflow-y: hidden;
}
.draft .sketchpad .gameview {
    position: relative;
    height: 512px;
}
.draft .sketchpad .gameview .step:hover,
.draft .sketchpad .gameview .decorate:hover {
    z-index: 19;
}
.draft .sketchpad .none {
    display: block;
}
.draft .sketchpad .gameview::after {
    content: ".";
    clear: both;
    display: block;
    height: 0;
    overflow: hidden;
}
.draft .sketchpad img {
    display: block;
    pointer-events: none;
    user-select: none;
    -moz-user-select: none;
}
.draft .sketchpad > .step img:active,
.draft .sketchpad > .decorate img:active {
    cursor: move;
}
.draft .sketchpad .gameview .confirm {
    display: none;
}
.draft .sketchpad .ctrl-bar {
    position: absolute;
    right: 0;
    top: 10px;
    white-space: nowrap;
    z-index: 1;
    font-size: 0;
}
.draft .sketchpad .ctrl-bar:hover + .img-border {
    border: 1px solid #ff0000;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 0;
}
.draft .sketchpad .ctrl-bar a,
.draft .sketchpad .ctrl-bar label {
    background: rgba(0, 0, 0, 0.3);
    color: #fff;
    font-size: 16px;
    line-height: 24px;
    display: inline-block;
    width: 24px;
    text-align: center;
    text-decoration: none;
    margin-right: 10px;
    position: relative;
    overflow: hidden;
    cursor: pointer;
}
.draft .sketchpad .ctrl-bar label input {
    position: absolute;
    top: -100px;
}
.draft .sketchpad .ctrl-bar a:hover,
.draft .sketchpad .ctrl-bar label:hover {
    background: #0098d8;
}
.draft .sketchpad .close {
    border-radius: 50%;
}
.draft .sketchpad .bg {
    float: left;
    height: 100%;
    position: relative;
}
.draft .sketchpad .bg img {
    height: 100%;
}
.draft .sketchpad .step,
.draft .sketchpad .decorate {
    position: absolute;
}
.none { display: none; }
.draft .sketchpad > .none {
    z-index: 9;
}
.draft .sketchpad .gameview .step {
    z-index: 6;
}
.draft .sketchpad .gameview .none span {
    display: none;
}
.draft .sketchpad .none span {
    position: absolute;
    left: 50%;
    top: 44px;
    transform: translateX(-50%);
    padding: 0 15px;
    font-size: 16px;
    line-height: 1.5;
    background: rgba(0,0,0,0.7);
    border-radius: 5px;
    color: #fff;
}
.draft .sketchpad .gameview .decorate {
    z-index: 3;
}
.draft .sketchpad .gameview .bg {
    z-index: 1;
}
.draft .loading {
    position: fixed;
    left: 50%;
    top: 50%;
    padding: 0 40px;
    line-height: 80px;
    font-size: 24px;
    color: #fff;
    z-index: 29;
    background: rgba(0,0,0,.5);
    transform: translate(-50%, -50%);
    border-radius: 5px;
    white-space: nowrap;
}