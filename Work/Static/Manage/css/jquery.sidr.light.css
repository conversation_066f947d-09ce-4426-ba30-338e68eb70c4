/* CSS Initializing */

.sidr {
    background: #f8f8f8 none repeat scroll 0 0;
    box-shadow: 0 0 5px 5px #ebebeb inset;
    color: #333;
    display: none;
    font-size: 15px;
    height: 100%;
    overflow-y: auto;
    position: fixed;
    top: 0;
    width: 260px;
    z-index: 999999;
}
.sidr .sidr-inner {
    padding: 0 0 15px;
}
.sidr .sidr-inner > p {
    margin-left: 15px;
    margin-right: 15px;
}
.sidr.right {
    left: auto;
    right: -260px;
}
.sidr.left {
    left: -260px;
    right: auto;
}
.sidr h1, .sidr h2, .sidr h3, .sidr h4, .sidr h5, .sidr h6 {
    background-image: linear-gradient(#ffffff, #dfdfdf);
    box-shadow: 0 5px 5px 3px rgba(0, 0, 0, 0.2);
    color: #333;
    font-size: 11px;
    font-weight: normal;
    line-height: 24px;
    margin: 0 0 5px;
    padding: 0 15px;
}
.sidr input[type="text"], .sidr input[type="password"], .sidr input[type="date"], .sidr input[type="datetime"], .sidr input[type="email"], .sidr input[type="number"], .sidr input[type="search"], .sidr input[type="tel"], .sidr input[type="time"], .sidr input[type="url"], .sidr textarea, .sidr select {
    background: rgba(0, 0, 0, 0.1) none repeat scroll 0 0;
    border: medium none;
    border-radius: 2px;
    box-sizing: border-box;
    clear: both;
    color: rgba(51, 51, 51, 0.6);
    font-size: 13px;
    margin: 0 0 10px;
    padding: 5px;
    width: 100%;
}
.sidr input[type="checkbox"] {
    clear: none;
    display: inline;
    width: auto;
}
.sidr input[type="button"], .sidr input[type="submit"] {
    background: #333 none repeat scroll 0 0;
    color: #f8f8f8;
}
.sidr input[type="button"]:hover, .sidr input[type="submit"]:hover {
    background: rgba(51, 51, 51, 0.9) none repeat scroll 0 0;
}



