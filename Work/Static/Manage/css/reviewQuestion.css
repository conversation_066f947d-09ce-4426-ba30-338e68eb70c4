.reviewMask {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 10;
  background-color: rgba(0,0,0,.5);
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}
.reviewMask .reviewContent {
  width: 600px;
  background-color: #fff;
  border-radius: 8px;
}
.reviewMask .readArtContent {
  max-height: 280px;
  overflow-y: auto;
}
.reviewMask .reviewContent .review-tit {
  line-height: 50px;
  text-align: center;
  font-size: 22px;
  color: #0099ff;
  position: relative;
}
.close-icon {
  position: absolute;
  top: 50%;
  right: 10px;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  width: 20px;
  height: 20px;
  background: url(../images/close.png) center center no-repeat;
  background-size: 100% 100%;
  cursor: pointer;
}
.reviewMask .reivew-text {
  margin-bottom: 20px;
  padding: 10px 80px 10px 20px;
  color: #fff;
  line-height: 20px;
  font-size: 16px;
  background-color: #0099ff;
  position: relative;
}
.reviewMask .reivew-text p {
  margin: 0;
}
.noice {
  position: absolute;
  width: 70px;
  height: 70px;
  right: 10px;
  top: 50%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  background: url(../images/noice.png) center center no-repeat;
  background-size: 55px 55px;
  border-radius: 50%;
  background-color: #fff;
  cursor: pointer;
}
.contents .tips {
  padding: 0 20px;
  color: #999;
  font-size: 16px;
  line-height: 22px;
}

/* 图片选择题 */
.contents .imgs-content {
  padding: 15px;
}
.contents .row-1 {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}
.contents .row-1 .imgs {
  width: 230px;
  margin-bottom: 20px;
}
.contents .row-1 img {
  width: 100%;
  border-radius: 3px;
}
.contents .row-2 {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}
.contents .row-2 .imgs {
  -webkit-box-flex: 1;
      -ms-flex: 1;
          flex: 1;
  margin-right: 10px;
}
.contents .row-2 .imgs:last-child {
  margin-right: 0;
}
.contents .row-2 img {
  width: 100%;
  border-radius: 3px;
}
.contents .row-2 p {
  padding-top: 15px;
  text-align: center;
  font-size: 16px;
}

/* 文字选择题 */
.contents .txt-content {
  padding: 15px;
}
.contents .row-1 {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}
.contents .row-1 .imgs {
  width: 230px;
  margin-bottom: 20px;
}
.contents .row-1 img {
  width: 100%;
  border-radius: 3px;
}
.contents .row-2 {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}
.contents .row-2 .querstion {
  -webkit-box-flex: 1;
      -ms-flex: 1;
          flex: 1;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  margin-right: 10px;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
.contents .row-2 .querstion:last-child {
  margin-right: 0;
}
.contents .row-2 span {
  width: 100%;
  padding: 8px 5px;
  line-height: 25px;
  font-size: 18px;
  text-align: center;
  border: 1px solid #ddd;
  border-radius: 3px;
}
.contents .row-2 p {
  padding-top: 15px;
  text-align: center;
  font-size: 16px;
}

/* 文字排序 */
.contents .sort-cont {
  padding: 5px 20px 50px;
}
.contents .sort-cont .line {
  height: 40px;
  border-bottom: 2px solid #ddd;
}
.contents .sort-cont .list {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  margin-top: 20px;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}
.contents .sort-cont .list span {
  padding: 5px 8px;
  margin: 0 10px 10px 0;
  font-size: 14px;
  border: 1px solid #ddd;
  cursor: pointer;
}
.answer {
  padding: 10px 0 0 0;
  color: #333;
  font-size: 15px;
}
.answer span {
  color: red;
}
/* 图片排序 */
.contents .imgSortlist {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  margin-bottom: 20px;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

.contents .imgSortlist .sort-num {
  min-height: 120px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  font-size: 28px;
  font-weight: bold;
  color: #fff;
  position: relative;
}
.contents .imgSortlist .sort-num img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}
.contents .imgSortlist .item {
  -webkit-box-flex: 1;
      -ms-flex: 1;
          flex: 1;
  margin-right: 10px;
  border: 2px solid #ddd;
}
.contents .imgSortlist .item span {
  position: relative;
  z-index: 2;
}
.contents .imgSortlist .item:last-child {
  margin-right: 0;
}
.contents .imgSortlist:last-child .item {
  border: none;
}
.contents .imgSortlist .item img {
  width: 100%;
}

/* 单词填空 */
.contents .missingLetterfill-cont {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  padding-bottom: 50px;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
.contents .missingLetterfill-cont .imgs,
.contents .missingLetterfill-cont img {
  max-width: 320px;
}
.contents .words {
  padding-top: 25px;
  font-size: 22px;
  color: #666;
}
.contents .missingLetterfill-cont .fill-ipt {
  min-width: 220px;
  height: 40px;
  margin-top: 15px;
  border-radius: 5px;
  line-height: 38px;
  text-align: center;
  font-size: 22px;
  color: #666;
  border: 1px solid #ddd;
}

/* 句子填空 */
.contents .missingSentencefill-cont {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  padding-bottom: 50px;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
.contents .missingSentencefill-cont .imgs,
.contents .missingSentencefill-cont img {
  max-width: 320px;
}
.contents .missingSentence-sentenct {
  /* width: 100%; */
  padding: 20px 20px 0;
}
.contents .missingSentence-sentenct p {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  font-size: 16px;
  color: #666;
}
.contents .missingSentence-sentenct span {
  height: 35px;
  text-align: center;
  line-height: 33px;
  margin: 0 5px;
  border-radius: 5px;
  border: 1px solid #ddd;
}
.contents .missingSentence-sentenct span.long {
  min-width: 120px;
}
.contents .missingSentence-sentenct span.short {
  min-width: 45px;
}

/* 单词补全 */
#wordsFill .words-cont {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  padding: 10px 10px 70px;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
#wordsFill .words-cont .imgs,
#wordsFill .words-cont img {
  max-width: 320px;
}
#wordsFill .words-cont .imgs {
  margin-bottom: 35px;
}
#wordsFill .words-cont p {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  font-size: 20px;
  color: #666;
}
#wordsFill .words-cont span {
  width: 60px;
  height: 40px;
  margin: 0 5px;
  border: 1px solid #ddd;
  border-radius: 3px;
}
/* 连连看 */
.link-cont {
  padding: 20px 40px;
}
.link-cont .row {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  margin-bottom: 20px;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}
.link-cont .row:first-child {
  margin-bottom: 40px;
}
.link-cont .item {
  -webkit-box-flex: 1;
      -ms-flex: 1;
          flex: 1;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  margin-right: 10px;
  position: relative;
}
.link-cont .item em {
  position: absolute;
  top: -25px;
  left: 50%;
  line-height: 25px;
  transform: translateX(-50%);
  font-size: 13px;
  padding: 0 10px;
  color: #fff;
  background-color: #0099ff;
  border-radius: 3px;
}
.link-cont .item:last-child {
  margin-right: 0;
}
.link-cont .item img {
  width: 100%;
  border-radius: 5px;
}
.link-cont .item .txt {
  width: 100%;
  padding: 10px 0;
  text-align: center;
  font-size: 16px;
  border: 1px solid #ccc;
  border-radius: 3px;
}

/* 文字判断题 */
.contents .judge-cont {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  padding-bottom: 50px;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  font-size: 30px;
  color: #666;
}
.contents .judge-cont .imgs {
  margin-bottom: 20px;
}
.contents .judge-cont .imgs,
.contents .judge-cont img {
  max-width: 320px;
}
.contents .judge-cont .judge {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  padding-top: 30px;
}
.contents .judge-cont .judge span {
  width: 80px;
  height: 80px;
  color: #fff;
  line-height: 80px;
  text-align: center;
  font-size: 40px;
  font-weight: bold;
  cursor: pointer;
}
.contents .judge-cont .judge .wrong {
  background-color: #ff3333;
}
.contents .judge-cont .judge .ok {
  background-color: #0099ff;
}
.contents .judge-cont .judge span:first-child {
  margin-right: 25px;
}

/* 图片点击题 */
.contents .imgsList {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  padding: 20px;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}
.contents .imgsList .item {
  width: 33.3%;
  border: 1px solid #ff3333;
  position: relative;
}
.contents .imgsList .item span {
  position: absolute;
  bottom: 5px;
  left: 50%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  font-size: 18px;
  color: #0099ff;
}
.contents .imgsList .item img {
  width: 100%;
}

/*  图片拖拽题 */
.contents .drag-cont {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  padding: 0 20px 35px;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
.contents .drag-cont .rows {
  width: 100%;
  padding-top: 20px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  margin-bottom: 15px;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}
.contents .drag-cont .head-rows,
.contents .drag-cont .next-rows {
  margin: auto;
  width: 135px;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}
.contents .drag-cont .item {
  /* width: 24.5%; */
  -webkit-box-flex: 1;
      -ms-flex: 1;
          flex: 1;
  margin-right: 10px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
}
.contents .rows .item span {
  width: 100%;
  font-size: 18px;
  line-height: 35px;
  text-align: center;
  color: #666;
  border: 1px solid #ddd;
  border-radius: 3px;
}
.contents .drag-cont .item:last-child {
  margin-right: 0;
}

.contents .drag-cont .rows .item p {
  padding-top: 15px;
  font-size: 14px;
  color: #999;
  text-align: center;
}
.contents .drag-cont .head-rows .item{
  width: 200px;
}
.contents .drag-cont .rows .item img {
  width: 100%;
}
.contents .drag-cont .next-rows .item {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 135px;
  height: 100px;
  border: 2px solid #ddd;
}
.contents .drag-cont .next-rows .item  img {
  max-width: 100%;
  max-height: 100%;
}

/*  文字拖拽题 */
#TxtDrag .drag-cont {
  padding: 0 20px 35px;
}
#TxtDrag .head-rows .item,
#TxtDrag .head-rows .item img {
  width: 280px;
  margin: auto;
}
#TxtDrag .drag-cont .rows,
#TxtDrag .drag-cont .next-rows {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  margin-top: 20px;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}
#TxtDrag .drag-cont .rows .item {
  -webkit-box-flex: 1;
      -ms-flex: 1;
          flex: 1;
  margin-right: 10px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
}
#TxtDrag .drag-cont .rows .item:last-child {
  margin-right: 0;
}
#TxtDrag .drag-cont .item span {
  width: 100%;
  font-size: 18px;
  line-height: 35px;
  text-align: center;
  color: #666;
  border: 1px solid #ddd;
  border-radius: 3px;
}
#TxtDrag .drag-cont .item p {
  padding: 20px 0 10px;
  font-size: 16px;
  text-align: center;
}
#TxtDrag .drag-cont .next-rows {
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}
#TxtDrag .drag-cont .next-rows span {
  width: 120px;
  height: 120px;
  border: 2px solid #ddd;
}

/* 句子 排序 题 */
.contents .SentenceSort-cont {
  padding: 0 20px 35px;
}
.contents .SentenceSort-cont li {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  margin-bottom: 10px;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.contents .SentenceSort-cont li span {
  width: 20px;
  height: 20px;
  margin-right: 10px;
  text-align: center;
  line-height: 20px;
  font-size: 15px;
  color: #fff;
  border-radius: 3px;
  background-color: #0099ff;
}
.contents .SentenceSort-cont li p {
  -webkit-box-flex: 1;
      -ms-flex: 1;
          flex: 1;
  margin: 0;
  padding: 6px 8px;
  font-size: 16px;
  color: #333;
  border: 1px solid #ddd;
  border-radius: 3px;
}
