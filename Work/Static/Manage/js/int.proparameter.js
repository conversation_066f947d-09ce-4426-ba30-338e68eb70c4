window.console = window.console || {};
console.log || (console.log = opera.postError);


/**
 *教学教务：周報表 数据插件开发
 ****/
(function($) {
    CMSJS.Proparameter =   {
        getAppend: function(Type) {
            var messages = {};
            $("#" + Type + " .ItemList").each(function(index, Item) {
                Item = $(Item);
                var items = {};
                var eles = Item.find(".items_key");
                var key = eles.data("keyname");
                if(eles.prop('checked')){
                    items[key] = eles.val();

                    var lsts = Item.find(".items_list");
                    var lstsitem = {};
                    lsts.each(function(j, ele_item) {
                        ele_item = $(ele_item);
                        if(ele_item.prop('checked')){
                            lstsitem[j] = ele_item.val();
                        }
                    });
                    if(Object.keys(lstsitem).length > 0 ){
                        items['list'] = lstsitem;
                    }
                    messages[index] = items;
                }
            });
            return messages;
        },
        /**
         *获取活动策略
         **/
        getListJson: function()  {
            var jsondata = {};
            jsondata = CMSJS.Proparameter.getAppend("Form-Box-Operating");
            return jsondata;
        },
        /**
         * 保存活动策略
         * **/
        ProparameterSave: function() {
            var json = CMSJS.Proparameter.getListJson();
            var procatalog_id = $("#ConterForm").data("procatalogid");
            var jsonTxt = JSON.stringify(json);
            console.log(jsonTxt);
            if($(".items_key").hasClass("ipt-errtip")){
                errormotify("填写信息不完整，禁止提交");
            }else{
                CMSJS.Proparameter.FormSave(procatalog_id,jsonTxt);
            }
        },
        /**
         * 保存进程
         **/
        FormSave: function(procatalog_id,jsonTxt) {
            var tiem_stimep = new Date().getTime();
            $.ajax({
                type: "post",
                cache: false,
                async: false,
                dataType: "json",
                url: "/Product?c=Productparameter&stimep="+tiem_stimep,
                data: {"procatalog_id": procatalog_id, "jsonTxt": jsonTxt},
                success: function(message) {
                    if (message.error == "1") {
                        errormotify(message.errortip);
                    } else if (message.error == "0") {
                        okmotify(message.errortip);
                        //refreshpage(message.errortip);
                    }
                }
            });
        }
    };
    $(document).ready(function() {

        //全选按钮
        $(".itemChoiceAll").change(function(){
            if($(this).prop("checked")){
                $(this).parents(".ItemList").find("input.items_list").prop("checked",true);
            }else{
                $(this).parents(".ItemList").find("input.items_list").prop("checked",false);
            }
        });

        //招生预算
        $("#ProparameterSave").click(function() {
            CMSJS.Proparameter.ProparameterSave();
        });

        /**
         * 禁止时间，单位，职位做回车操作
         * 13 => enter
         * 32 => space
         */
        $("span[data-type=value]").on("keydown", function(event) {
            if (event.keyCode == 13 || event.charCode == 13) {
                //errormotify("禁止回车");
                return false;
            }
        });


        //新增一行
        // $(document).on("click", ".copyTableToNext",function() {
        //     var TdHtml = $(this).parents("tr.tablelist").html();
        //     var NewHtml = "<tr class=\"tablelist ItemList\">"+TdHtml.replace(/(.*)(copyTableToNext)(.*)(\[)(\+)/i, "$1delTableTd$3$4-")+"</tr>";
        //     NewHtml = NewHtml.replaceAll("edit","add");
        //     $(this).parents("tbody").append(NewHtml);
        // });
        $(document).on("click", ".delTableTd",function() {
            $(this).parents("tr").remove();
        });
    });

})(jQuery);

String.prototype.replaceAll = function (FindText, RepText) {
    regExp = new RegExp(FindText, "g");
    return this.replace(regExp, RepText);
}

//供使用者调用
function trim(s) {
    return trimRight(trimLeft(s));
}
//去掉左边的空白
function trimLeft(s) {
    if (s == null) {
        return "";
    }
    var whitespace = new String(" \t\n\r");
    var str = new String(s);
    if (whitespace.indexOf(str.charAt(0)) != -1) {
        var j = 0,
            i = str.length;
        while (j < i && whitespace.indexOf(str.charAt(j)) != -1) {
            j++;
        }
        str = str.substring(j, i);
    }
    return str;
}

//去掉右边的空白 www.2cto.com
function trimRight(s) {
    if (s == null) return "";
    var whitespace = new String(" \t\n\r");
    var str = new String(s);
    if (whitespace.indexOf(str.charAt(str.length - 1)) != -1) {
        var i = str.length - 1;
        while (i >= 0 && whitespace.indexOf(str.charAt(i)) != -1) {
            i--;
        }
        str = str.substring(0, i + 1);
    }
    return str;
}
/**
 * 清除一些\n\r等等
 */
function clearText(text) {
    if (!text)
        return "";
    text = text.replace(/[\n]/ig, '');
    text = text.replace(/[\r]/ig, '');
    text = text.replace(/[\t]/ig, '');
    return trim(text);
}
/**
 * 清除所有的HTMl的标签，计算实际内容字数
 */
function clearAllHtmlText(text) {
    if (!text)
        return "";
    text = text.replace(/<[^>]+>/g, "");
    text = text.replace(/[\n]/ig, '');
    text = text.replace(/[\r]/ig, '');
    text = text.replace(/[\t]/ig, '');
    return trim(text);
}