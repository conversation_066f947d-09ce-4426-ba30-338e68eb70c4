/**
 * Created by Mohism on 2016/12/15.
 */
/*
 * Tiplog 1.0
 * version: 1.0
 * http://www.hcircle.cn
 * Copyright 2016, 2018 mohism [ <EMAIL> ]
 *
 */
(function($){
    //插件运行
    CMSJS.Tiplog = {
        OpenOkTip : function(tipTitel,tipConter,okurl,okbuttxt,closebuttxt) {
            $('<div class="Tiplog">'
            +'<div class="Tiplog-Curtain"></div>'
            +'<div class="Tiplog-main">'
            +'<div class="Tiplog-content">'
            +'<div class="ok-ico">'

            +'</div>'
            +'<div class="tip-text">'
            +'<p class="tip-title">'
            +tipTitel
            +'</p>'
            +'<p class="tip-txt">'
            +tipConter
            +'</p>'
            +'</div></div>'
            +'<div class="Tiplog-footer">'
            +'<a class="but but-red Tiplog-ok-but" href="'+okurl+'">'+okbuttxt+'</a>'
            +'<a class="but but-black Tiplog-Close-but">'+closebuttxt+'</a>'
            +'</div></div></div>').appendTo("body");
        },
        OpenErrorTip : function(tipTitel,tipConter,url,okbuttxt,closebuttxt) {
            $('<div class="Tiplog">'
            +'<div class="Tiplog-Curtain"></div>'
            +'<div class="Tiplog-main">'
            +'<div class="Tiplog-content">'
            +'<div class="error-ico">'

            +'</div>'
            +'<div class="tip-text">'
            +'<p class="tip-title">'
            +tipTitel
            +'</p>'
            +'<p class="tip-txt">'
            +tipConter
            +'</p>'
            +'</div></div>'
            +'<div class="Tiplog-footer">'
            +'<a class="but but-red Tiplog-ok-but" href="'+url+'">'+okbuttxt+'</a>'
            +'<a class="but but-black Tiplog-Close-but">'+closebuttxt+'</a>'
            +'</div></div></div>').appendTo("body");
        },
        OpenLoadTip : function(tipUrl) {
            $('<div class="Tiplog">'
            +'<div class="Tiplog-Curtain"></div>'
            +'<div class="Tiplog-main">'
            +'<div class="Tiplog-content"><div class="tip-text LogView"></div></div></div></div>').appendTo("body");
            $(".LogView").load(tipUrl);
        },
        OpenLoadHtml : function(tipHtml) {
            $('<div class="Tiplog">'
            +'<div class="Tiplog-Curtain"></div>'
            +'<div class="Tiplog-main">'
            +'<div class="Tiplog-content"><div class="tip-text LogView">'+tipHtml+'</div></div></div></div>').appendTo("body");
        },
        UrlOkTip : function(tipTitel,tipConter,okurl,okbuttxt,closeurl,closebuttxt) {
            $('<div class="Tiplog">'
            +'<div class="Tiplog-Curtain"></div>'
            +'<div class="Tiplog-main">'
            +'<div class="Tiplog-content">'
            +'<div class="ok-ico">'

            +'</div>'
            +'<div class="tip-text">'
            +'<p class="tip-title">'
            +tipTitel
            +'</p>'
            +'<p class="tip-txt">'
            +tipConter
            +'</p>'
            +'</div></div>'
            +'<div class="Tiplog-footer">'
            +'<a class="but but-red Tiplog-ok-but" href="'+okurl+'">'+okbuttxt+'</a>'
            +'<a class="but but-black" href="'+closeurl+'">'+closebuttxt+'</a>'
            +'</div></div></div>').appendTo("body");
        },
        OpenViewTip : function(tipTitle,tipUrl) {
            $('<div class="Tiplog">'
            +'<div class="Tiplog-Curtain"></div>'
            +'<div class="Tiplog-main"><div class="Tiplog-top">'
            +tipTitle
            +'<span class="Tiplog-Close-but"></span></div>'
            +'<div class="Tiplog-content"></div></div></div>').appendTo("body");
            $(".Tiplog-content").load(tipUrl);
        },
        FucOpenViewTip : function(tipTitle,tipUrl,okfuc,okbuttxt,closebuttxt) {
            $('<div class="Tiplog">' +
            '<div class="Tiplog-Curtain"></div>' +
            '<div class="Tiplog-main">' +
            '<div class="Tiplog-top">'+tipTitle+'<span class="Tiplog-Close-but icon-remove"></span></div>' +
            '<div class="Tiplog-content"></div>' +
            '<div class="Tiplog-footer"><a class="but but-red Tiplog-ok-but">'+okbuttxt+'</a>' +
            '<a class="but but-black Tiplog-Close-but">'+closebuttxt+'</a></div>' +
            '</div></div>').appendTo("body");
            $(".Tiplog-content").load(tipUrl);
            $(document).on('click', '.Tiplog-ok-but',function(e) {
                okfuc();
            });
        },
        FucWarningTip : function(tipTitel,tipConter,okfuc,okbuttxt,closebuttxt) {
            $('<div class="Tiplog">'
            +'<div class="Tiplog-Curtain"></div>'
            +'<div class="Tiplog-main">'
            +'<div class="Tiplog-content">'
            +'<span class="glyphicon glyphicon-question-sign warning"></span>'
            +'<div class="tip-text">'
            +'<p class="tip-title">'
            +tipTitel
            +'</p>'
            +'<p class="tip-txt">'
            +tipConter
            +'</p>'
            +'</div></div>'
            +'<div class="Tiplog-footer">'
            +'<a class="but but-red Tiplog-ok-but">'+okbuttxt+'</a>'
            +'<a class="but but-black Tiplog-Close-but">'+closebuttxt+'</a>'
            +'</div></div></div>').appendTo("body");
            $(document).on('click', '.Tiplog-ok-but',function(e) {
                okfuc();
            });
        },
        Close: function() {
            $(".Tiplog").remove();
        }
    };
    $(document).ready(function() {
        //关闭弹出窗
        $(document).on('click', '.Tiplog-Close-but',function(e) {
            e.preventDefault();
            CMSJS.Tiplog.Close();
        });

        //影藏
        $(document).on('click', '.Tiplog-Curtain',function(e) {
            e.preventDefault();
            CMSJS.Tiplog.Close();
        });
    });

})(jQuery);
