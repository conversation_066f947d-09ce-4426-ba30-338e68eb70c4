/**
 * Created by Administrator on 2017/2/19.
 */

(function($){
    //表格相关处理函数
    CMSJS.Table = {
        EditIptAjax : function() {

        }
    };
    $(document).ready(function() {
        //档案直接修改
        $(document).on("change", ".iptAjaxEdit",function() {
            var url = "?",thisobj = $(this),send_fuc=$(this).data("fuc"),send_id=$(this).data("id"),send_field=$(this).data("field"),send_val=$(this).val();
            thisobj.addClass("edrun").removeClass("edok");
            $.ajax({
                async:false,
                contentType: "application/json",
                dataType: "json",
                type: "GET",
                url: url,
                data: { "c": send_fuc,"id": send_id,"field": send_field,"value": send_val},
                success: function(bakjson){
                    if(bakjson['error'] == '1'){
                        errormotify(bakjson.errortip);
                    }
                    thisobj.addClass("edok").removeClass("edrun");
                },
                error: function(){
                    alert("机制错误");
                }
            });
            return false;
        });

    });

})(jQuery);