(function($){
    //站点插件
    var sort = {
      'A': 0,'B': 1,'C': 2,'D': 3,'E': 4,
      'F': 5,'G': 6,'H': 7,'I': 8
    }

    Handlebars.registerHelper('if_eq', function(v1, v2, opts) {
      if(v1 == v2)
        return opts.fn(this)
      else
        return opts.inverse(this)
    })

    CMSJS.Examine = {
        Question11 : function(html, list) {
            $('body').append(html);
            $('#imgChoose .row-2 .imgs').eq(sort[list.question_correct]).find('img').css({
              'border': '2px solid #f00'
            })
        },
        Question12 : function(html, list) {
            $('body').append(html); 
            $('#txtChoose .row-2 .querstion').eq(sort[list.question_correct]).find('span').css({
              'border': '2px solid #f00'
            })
        },
        Question13 : function(html, list) {
            $('body').append(html);
            var anwsers = list.question_correct.split('|'),
                sentence = '',
                html = '',
                data = list.option
        
            anwsers.forEach(function(e) {
              sentence = sentence + data[sort[e]].answers_option + ' '
            })
            html = '<p class="answer">本题的答案是：<span>'+ sentence +'</span> </p>'
            $('#txtSort .sort-cont').append(html)
        },
        Question14 : function(html, list) {
            $('body').append(html);
            var anwsers = list.question_correct.split('|'),
                data = list.option
            anwsers.forEach(function(e, i) {
              $('#imgSort .imgSortlist .sort-num').eq(i).append('<img src="'+ data[sort[e]].answers_optionimg +'"/>')
            })
        },
        Question15 : function(html, list) {
            $('body').append(html.replace(/\(\$\)/g, '<span class="tk long"></span>').replace(/\(\#\)/g, '<span class="tk short"></span>'));
            $('#missingLetter .fill-ipt').append(list.question_correct)
        },
        Question16 : function(html, list) {
            $('body').append(html.replace(/\(\$\)/g, '<span class="tk long"></span>').replace(/\(\#\)/g, '<span class="tk short"></span>'));
            var anwsers = list.question_correct.split('|')
            console.log(anwsers)
            anwsers.forEach(function(e, i) {
              $('#missingSentence .missingSentence-sentenct .tk').eq(i).append(e)
            })
        },
        Question17 : function(html, list) {
            $('body').append(html);
            var answers = list.question_correct.split('|')
                html = ''

            answers.forEach(function(e, i) {
              var iAnswer = e.split(',')
              html += 'A' + iAnswer[0] + '-B' + iAnswer[1] + ', '
            })
            $('#linkTolink .link-cont').append(' <p class="answer">本题的答案是： <span>'+ html +'</span></p>')
        },
        Question18 : function(html, list) {
            $('body').append(html);
            var answer = list.question_correct
            console.log(answer)
            if (answer !== 'false') {
              $('#TxtJudge .judge-cont .ok').css({
                'border': '2px solid #f00'
              })
            } else {
              $('#TxtJudge .judge-cont .wrong').css({
                'border': '2px solid #0099ff'
              })
            }
        },
        Question19 : function(html, list) {
            $('body').append(html);
            var anwsers = list.question_correct.split('|')
            anwsers.forEach(function(e, i) {
              $('#ImgClick .imgsList .item').eq(sort[e]).css({
                'border': '2px solid #0099ff'
              })
            })
        },
        Question20 : function(html, list) {
            $('body').append(html);
            var anwsers = list.question_correct.split('|'),
                data = list.option
            anwsers.forEach(function(e, i) {
              $('#ImgDrag .next-rows .item').append('<img src="'+ data[sort[e]].answers_optionimg +'"/>')
            })
        },
        Question21 : function(html) {
            $('body').append(html);
        },
        Question22 : function(html) {
            $('body').append(html);
        }
    };

    $(document).ready(function() {
        // 4-检测题库  题目预览弹框
        var types = {
            11: 'imgChoose', //图片选择题
            12: 'txtChoose', //文字选择题
            13: 'txtSort', //文字排序题
            14: 'imgSort', //图片排序题
            15: 'missingLetter', //填空题（A）
            16: 'missingSentence', //填空题（B）
            17: 'linkTolink', // PicToPic PicToTxt TxtToTxt 连连看
            18: 'TxtJudge', //判断题
            19: 'ImgClick', //图片点击题
            20: 'ImgDrag', // ImgDrag TxtDrag 拖拽题
            21: 'SentenceSort', //句子排序题
            22: 'readArt' //阅读题
        }

        $(document).on('click', '.reviewQuesBtn', function() {
            var id = $(this).data('id')
            var type = $(this).data('type')
            console.log(id, types[type])
            $.ajax({
                type: 'GET',
                dataType: 'JSON',
                url: 'http://stuapi.kidcastle.cn/Exam/getInsideQuestion?question_id='+id+'&question_correct=1',
                success: function(res) {
                    console.log(res)
                    if (res.error == 0) {
                        var list = res.result.list
                        var cont = $('#'+ types[type] + '-template')[0].innerHTML
                        var template  = Handlebars.compile(cont)
                        var html = template(list);

                        CMSJS.Examine['Question'+type](html, list);

                        if (type == 22) {
                            list.ids.forEach(function(e) {
                                $.ajax({
                                    type: 'GET',
                                    dataType: 'JSON',
                                    url: 'http://stuapi.kidcastle.cn/Exam/getInsideQuestion?question_id='+e.question_id,
                                    success: function(data) {
                                        console.log(data)
                                        if (data.error == 0) {
                                            var result = data.result.list
                                            console.log('#readArtCont-'+ result.category_id)
                                            var cont = $('#readArtCont-'+ result.category_id)[0].innerHTML
                                            var template  = Handlebars.compile(cont)
                                            var html = template(result);
                                            $('.readArt-cont').append(html.replace(/\(\$\)/g, '<span class="long"></span>').replace(/\(\#\)/g, '<span class="short"></span>'));
                                        }
                                    }
                                })
                            })
                        }

                        $('.reviewMask .close-icon').on('click', function() {
                            $('.reviewMask').remove()
                        });

                        $('span.noice').on('click', function() {
                            var audio = $('#audio')[0]
                            audio.play()
                        });
                    }
                }
            })
        })

    });
})(jQuery);