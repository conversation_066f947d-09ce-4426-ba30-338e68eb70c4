/**
 * Created by Mohism on 2016/12/15.
 */
function showRequest(formData, jqForm, options){
    //formData: 数组对象，提交表单时，Form插件会以Ajax方式自动提交这些数据，格式如：[{name:user,value:val },{name:pwd,value:pwd}]
    //jqForm:   jQuery对象，封装了表单的元素
    //options:  options对象
    //var queryString = $.param(formData);   //name=1&address=2
    //var formElement = jqForm[0];              //将jqForm转换为DOM对象
    //var address = formElement.address.value;  //访问jqForm的DOM元素
    return true;  //只要不返回false，表单都会提交,在这里可以对表单元素进行验证
};

function showResponse(responseText, statusText){
    eval("var bakjson = " + responseText + ";") ;
    if(bakjson.bakfuntion == 'errormotify'){
        errormotify(bakjson.errortip);
    }else if(bakjson.bakfuntion == 'okmotify'){
        okmotify(bakjson.errortip);
    }else{
        window[bakjson.bakfuntion](bakjson.errortip);
    }
    if(typeof(bakjson.bakurl) !== "undefined" && bakjson.bakurl !== ''){
        //展示两秒信息后再跳转
        if (bakjson.type && bakjson.type == 'replace') {
            window.setTimeout(function(){window.location.replace(bakjson.bakurl);},2000);
        } else {
            window.setTimeout(function(){window.location=bakjson.bakurl;},2000);
        }
    }
};

var clearoptions = {
    //target: '#output',          //把服务器返回的内容放入id为output的元素中
    beforeSubmit: showRequest,  //提交前的回调函数
    success: showResponse,      //提交后的回调函数
    //url: url,                 //默认是form的action， 如果申明，则会覆盖
    //type: type,               //默认是form的method（get or post），如果申明，则会覆盖
    //dataType: null,           //html(默认), xml, script, json...接受服务端返回的类型
    //clearForm: true,          //成功提交后，清除所有表单元素的值
    resetForm: true,          //成功提交后，重置所有表单元素的值
    timeout: 3000               //限制请求的时间，当请求大于3秒后，跳出请求
};
var options = {
    //target: '#output',          //把服务器返回的内容放入id为output的元素中
    beforeSubmit: showRequest,  //提交前的回调函数
    success: showResponse,      //提交后的回调函数
    //url: url,                 //默认是form的action， 如果申明，则会覆盖
    //type: type,               //默认是form的method（get or post），如果申明，则会覆盖
    //dataType: null,           //html(默认), xml, script, json...接受服务端返回的类型
    //clearForm: true,          //成功提交后，清除所有表单元素的值
    //resetForm: true,          //成功提交后，重置所有表单元素的值
    timeout: 3000               //限制请求的时间，当请求大于3秒后，跳出请求
};
$(".clearAjaxForm").ajaxForm(clearoptions);
$(".AjaxForm").ajaxForm(options);

//正确提示
function familyok(tip){
    CMSJS.Tiplog.OpenOkTip(tip,"您可根据情况，选择点击下面按钮","/iuser/myfamily","查看列表","继续添加");
}
//正确提示
function infook(tip){
    CMSJS.Tiplog.OpenOkTip(tip,"您可根据情况，选择点击下面按钮","/iuser/","会员中心","继续添加");
}
