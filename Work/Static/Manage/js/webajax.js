/**ajax配置判断**/
if (typeof Ajax != 'object')
{
    alert('Ajax object doesn\'t exists.');
}
/**数据处理配置判断**/
if (typeof Utils != 'object')
{
    alert('Utils object doesn\'t exists.');
}

var ajaxClass = new Object;

ajaxClass.query = "query";
ajaxClass.filter = new Object;
ajaxClass.url = location.href.lastIndexOf("?") == -1 ? location.href.substring((location.href.lastIndexOf("/")) + 1) : location.href.substring((location.href.lastIndexOf("/")) + 1, location.href.lastIndexOf("?"));
ajaxClass.url += "?";

/**
 * 删除列表中的一个记录
 */
ajaxClass.remove = function(id, cfm, opt)
{
    if (opt == null)
    {
        opt = "remove";
    }

    if (confirm(cfm))
    {
        var args = "action=" + opt + "&id=" + id + this.compileFilter();

        Ajax.call(this.url, args, this.listCallback, "GET", "HTML");
    }
}


/**
 * 简介栏目等级变动
 */
ajaxClass.ratespl = function(id)
{
    var args = "c=Gettwomenu&rate=" + id;
    Ajax.call(this.url, args, this.optionTxt, "GET", "JSON");
}
/**简介名称变动***/
ajaxClass.columspl= function(id)
{
    var args = "c=Getabout&partid=" + id;
    Ajax.call(this.url, args, this.bakidhtml_txt, "GET", "JSON");
}
/**模型选择查栏目***/
ajaxClass.Model_columnGet= function(id)
{
    var module_fieldid = $("input#tb_id").val();
    if(module_fieldid !=='all' && module_fieldid !==''){
        var args = "c=columnGet&modelid=" + id + '&module_fieldid=' + module_fieldid;
    }else{
        var args = "c=columnGet&modelid=" + id;
    }
    Ajax.call(this.url, args, this.bakidhtml, "GET", "JSON");
}
/**删除附加图片**/
ajaxClass.delattach= function(id)
{
    var args = "c=delattach&id=" + id;
    Ajax.call(this.url, args, this.bakdelid, "GET", "JSON");
}
/**删除附加主题**/
ajaxClass.delappend= function(id)
{
    var args = "c=delappend&id=" + id;
    Ajax.call(this.url, args, this.bakdelid, "GET", "JSON");
}

/**修改输入数值**/
ajaxClass.edit = function(obj, act, dbfild, id)
{
    var tag = obj.firstChild.tagName;

    if (typeof(tag) != "undefined" && tag.toLowerCase() == "input")
    {
        return;
    }

	/* 保存原始的内容 */
    var org = obj.innerHTML;
    var val = Browser.isIE ? obj.innerText : obj.textContent;

	/* 创建一个输入框 */
    var txt = document.createElement("INPUT");
    txt.value = (val == 'N/A') ? '' : val;
    txt.style.width = (obj.offsetWidth + 12) + "px" ;

	/* 隐藏对象中的内容，并将输入框加入到对象中 */
    obj.innerHTML = "";
    obj.appendChild(txt);
    txt.focus();

	/* 编辑区输入事件处理函数 */
    txt.onkeypress = function(e)
    {
        var evt = Utils.fixEvent(e);
        var obj = Utils.srcElement(e);

        if (evt.keyCode == 13)
        {
            obj.blur();

            return false;
        }

        if (evt.keyCode == 27)
        {
            obj.parentNode.innerHTML = org;
        }
    }
	/* 编辑区失去焦点的处理函数 */
    txt.onblur = function(e)
    {
        if (Utils.trim(txt.value).length > 0)
        {
            res = Ajax.call(ajaxClass.url, "c="+act+"&val=" + encodeURIComponent(Utils.trim(txt.value)) + "&id=" +id +"&dbfild="+dbfild, null, "POST", "JSON", false);
            if (res.message)
            {
                alert(res.message);
            }
            obj.innerHTML = (res.error == 0) ? Utils.changeTwoDecimal_f(res.content) : Utils.changeTwoDecimal_f(org);
        }
        else
        {
            obj.innerHTML = Utils.changeTwoDecimal_f(org);
        }
    }
}
/**修改输入框**/
ajaxClass.editvar = function(obj, act, dbfild, id)
{
    var tag = obj.firstChild.tagName;

    if (typeof(tag) != "undefined" && tag.toLowerCase() == "input")
    {
        return;
    }

	/* 保存原始的内容 */
    var org = obj.innerHTML;
    var val = Browser.isIE ? obj.innerText : obj.textContent;

	/* 创建一个输入框 */
    var txt = document.createElement("INPUT");
    txt.value = (val == 'N/A') ? '' : val;
    txt.style.width = (obj.offsetWidth - 16) + "px" ;

	/* 隐藏对象中的内容，并将输入框加入到对象中 */
    obj.innerHTML = "";
    obj.appendChild(txt);
    txt.focus();

	/* 编辑区输入事件处理函数 */
    txt.onkeypress = function(e)
    {
        var evt = Utils.fixEvent(e);
        var obj = Utils.srcElement(e);

        if (evt.keyCode == 13)
        {
            obj.blur();

            return false;
        }

        if (evt.keyCode == 27)
        {
            obj.parentNode.innerHTML = org;
        }
    }
	/* 编辑区失去焦点的处理函数 */
    txt.onblur = function(e)
    {
        if (Utils.trim(txt.value).length > 0)
        {
            res = Ajax.call(ajaxClass.url, "c="+act+"&val=" + encodeURIComponent(txt.value) + "&id=" +id +"&dbfild="+dbfild, null, "POST", "JSON", false);
            if (res.message)
            {
                alert(res.message);
            }
            obj.innerHTML = (res.error == 0) ? res.content : org;
        }
        else
        {
            obj.innerHTML = org;
        }
    }
}


/**排序***/
ajaxClass.orderbygoto= function(obg,orderby)
{
    var url = $id("url").value;
    if(obg.className == 'odery'){
        obg.className = 'odery1';
        var args = url + "&orderby=" + orderby + ' DESC';

    }else if(obg.className == 'odery1'){
        obg.className = 'odery2';
        var args = url + "&orderby=" + orderby + ' ASC';
    }else if(obg.className == 'odery2'){
        obg.className = 'odery';
        var args = url + "&orderby=jxpk_id DESC";
    }
    Utils.In_link(args);

}
/**选择文章**/
ajaxClass.searchArticle= function()
{
    var keywode = $("#keyword").val();
    var twomenu = $("#twomenuspl").val();
    if(keywode == ''){
        alert('关键词不可为空！');
    }else{
        var args = "c=searchArticle&keywode=" + keywode + "&twomenu=" + twomenu;
        Ajax.call(this.url, args, this.bakidhtml, "GET", "JSON");
    }
}

/**获取栏目**/
ajaxClass.Getthreemenu = function()
{
    var twomenu = $("#twomenu").val();
    var args = "c=Getthreemenu&twomenu=" + twomenu;
    Ajax.call(this.url, args, this.bakidhtml, "GET", "JSON");

}

/**数据删除**/
ajaxClass.confirm_del = function(promptinfo,urlstr)
{
    if(confirm(promptinfo)) {
        Utils.In_link(this.url + urlstr);
    }else{
        return false;
    }

}


ajaxClass.compileFilter = function()
{
    var args = '';
    for (var i in this.filter)
    {
        if (typeof(this.filter[i]) != "function" && typeof(this.filter[i]) != "undefined")
        {
            args += "&" + i + "=" + encodeURIComponent(this.filter[i]);
        }
    }

    return args;
}



ajaxClass.optionTxt = function(result, txt)
{
    var str = result;
    var conttent=str;
    if(conttent.length <= 1){
        alert('数据连接错误！');
    }
    try
    {
        if(conttent['error'] !== '0'){
            alert(conttent['message']);
        }else{
            $("#selectname").html(conttent['content']);
        }
    }
    catch (e)
    {
        alert(e.message);
    }
}

ajaxClass.bakidhtml = function(result, txt)
{
    var str = result;
    var conttent=str;
    if(conttent.length <= 1){
        alert('数据连接错误！');
    }
    try
    {
        if(conttent['error'] !== '0'){
            alert(conttent['message']);
        }else{
            $("#"+conttent['bakid']).html(conttent['content']);
        }
    }
    catch (e)
    {
        alert(e.message);
    }
}


ajaxClass.bakidhtml_txt = function(result, txt)
{
    var str = result;
    var conttent=str;
    if(conttent.length <= 1){
        alert('数据连接错误！');
    }
    try
    {
        if(conttent['error'] !== '0'){
            alert(conttent['message']);
        }else{
            $("#"+conttent['bakid']).html(conttent['content']);
            $('#intro_over').html('');
            Editor("cpat_txt");
        }
    }
    catch (e)
    {
        alert(e.message);
    }
}

ajaxClass.bakdelid = function(result, txt)
{
    var str = result;
    var conttent=str;
    if(conttent.length <= 1){
        alert('数据连接错误！');
    }

    try
    {
        if(conttent['error'] != '0'){
            alert(conttent['message']);
        }else{
            $("#"+conttent['delid']).remove();
        }
    }
    catch (e)
    {
        alert(e.message);
    }
}

