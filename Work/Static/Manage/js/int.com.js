/**
 * Created by Administrator on 2016/12/14.
 */
window.console = window.console || {};
console.log || (console.log = opera.postError);


/*
 * JDB 1.0
 * version: 1.0
 * http://www.baisui.la
 * Copyright 2016, 2018 mohism [ <EMAIL> ]
 *
 */

var CMSJS = CMSJS||{};
(function($){
    //兼容placeholder8
    CMSJS.Comint = {
        placeholderSupport : function() {
            return 'placeholder' in document.createElement('input');
        },
        placeholderStart : function() {
            if(!this.placeholderSupport()){
                $('[placeholder]').focus(function() {
                    var input = $(this);
                    if (input.val() == input.attr('placeholder')) {
                        input.val('');
                        input.removeClass('placeholder');
                    }
                }).blur(function() {
                    var input = $(this);
                    if (input.val() == '' || input.val() == input.attr('placeholder')) {
                        input.addClass('placeholder');
                        input.val(input.attr('placeholder'));
                    }
                }).blur();
            }
        },
        /**保留两位小数点**/
        TwoDecimal : function(x){
            var f_x = parseFloat(x);
            if (isNaN(f_x))
            {
                alert('function:changeTwoDecimal->parameter error');
                return false;
            }
            var f_x = Math.round(x*100)/100;
            var s_x = f_x.toString();
            var pos_decimal = s_x.indexOf('.');
            if (pos_decimal < 0)
            {
                pos_decimal = s_x.length;
                s_x += '.';
            }
            while (s_x.length <= pos_decimal + 2)
            {
                s_x += '0';
            }
            return s_x;
        },
        /**倒计时**/
        jumpDown : function (count,url) {
            window.setTimeout(function(){
                count--;
                if(count > 0) {
                    $('#timeCountdown').html(count);
                    CMSJS.Comint.jumpDown(count,url);
                } else {
                    location.href=url;
                }
            }, 1000);
        },
        /**倒计时**/
        DiyProgress : function () {
            var progress = 20;
            if($('input[name="age"]').is(':checked')){
                console.log(1);
                progress += 20;
            }
            if($('input[name="crowdtype[]"]').is(':checked')){
                progress += 20;
            }
            if($('input[name="marriage"]').is(':checked')){
                progress += 20;
            }
            if($('input[name="budget"]').is(':checked')){
                progress += 20;
            }
            console.log(progress);
            $(".Diy-Progress").find("em").css("width",progress+"%");
            $(".Diy-Progress").find(".progressval").html(progress+"%");
        },
        /**检索省市分类**/
        JsonGetCity : function (province_id) {
            $.ajax({
                url: "/Api/getCity",
                type: 'get',
                data: {"province_id":province_id},
                dataType: 'json'
            }).done(function(bakjson) {
                if(bakjson.error == '1'){
                    errormotify(bakjson.errortip);
                    $('#city_id').html("<option value=\"0\">请先选择省份</option>").attr("disabled","disabled");
                }else{
                    var classHtml = "<option value=\"0\">请选择所属市</option>";
                    var listjson = bakjson.result;
                    for(var k in listjson){
                        classHtml += "<option value=\""+listjson[k].regional_id+"\">"+listjson[k].regional_name+"</option>";
                    }
                    $('#city_id').html(classHtml).removeAttr("disabled");
                }
            });
        },
        /**检索市区分类**/
        JsonGetArea : function (city_id) {
            $.ajax({
                url: "/Api/getArea",
                type: 'get',
                data: {"city_id":city_id},
                dataType: 'json'
            }).done(function(bakjson) {
                if(bakjson.error == '1'){
                    errormotify(bakjson.errortip);
                    $('#area_id').html("<option value=\"0\">请先选择所属市</option>").attr("disabled","disabled");
                }else{
                    var classHtml = "<option value=\"0\">请选择所属区域</option>";
                    var listjson = bakjson.result;
                    for(var k in listjson){
                        classHtml += "<option value=\""+listjson[k].regional_id+"\">"+listjson[k].regional_name+"</option>";
                    }
                    $('#area_id').html(classHtml).removeAttr("disabled");
                }
            });
        }  ,
        /**检索产品分类**/
        JsonGetCate : function (procat_id) {
            $.ajax({
                url: "/Api/getCate",
                type: 'get',
                data: {"procat_id":procat_id},
                dataType: 'json'
            }).done(function(bakjson) {
                if(bakjson.error == '1'){
                    errormotify(bakjson.errortip);
                    $('#procat_id').html("<option value=\"0\">请先选择所属分类</option>").attr("disabled","disabled");
                }else{
                    var classHtml = "<option value=\"0\">请先选择所属分类</option>";
                    var listjson = bakjson.result;
                    for(var k in listjson){
                        classHtml += "<option value=\""+listjson[k].procat_id+"\">"+listjson[k].procat_name+"</option>";
                    }
                    $('#procat_id').html(classHtml).removeAttr("disabled");
                }
            });
        }  ,
        /**检索校园班级**/
        JsonGetClass : function () {
            var campus_id = $("#campus_id").val(),codecat = $("#codecat").val();
            $.ajax({
                url: "/api/getClass",
                type: 'get',
                data: {"campus_id":campus_id,"codecat":codecat},
                dataType: 'json'
            }).done(function(bakjson) {
                if(bakjson.error == '1'){
                    errormotify(bakjson.errortip);
                    $('#class_id').html("<option value=\"0\">请先选择校园</option>").attr("disabled","disabled");
                }else{
                    var classHtml = "<option value=\"0\">请先选择班级</option>";
                    var listjson = bakjson.result.list;
                    for(var k in listjson){
                        classHtml += "<option value=\""+listjson[k].class_id+"\">"+listjson[k].class_codecat+" ("+listjson[k].class_codecatdesc+") "+listjson[k].class_engname+"</option>";
                    }
                    $('#class_id').html(classHtml).removeAttr("disabled");
                }
            });
        },
        /**检索采供上下边的项目**/
        PurItemGet : function (city_id) {
            $.ajax({
                url: "/Api/getPuritem",
                type: 'get',
                data: {"city_id":city_id},
                dataType: 'json'
            }).done(function(bakjson) {
                if(bakjson.error == '1'){
                    errormotify(bakjson.errortip);
                    $('#Puritem').html("<option value=\"0\">请先选择采购商</option>").attr("disabled","disabled");
                }else{
                    var classHtml = "<option value=\"0\">请选择采购商</option>";
                    var listjson = bakjson.result;
                    for(var k in listjson){
                        classHtml += "<option value=\""+listjson[k].regional_id+"\">"+listjson[k].regional_name+"</option>";
                    }
                    $('#Puritem').html(classHtml).removeAttr("disabled");
                }
            });
        },
        /**暖心店再用  台湾筛选城市**/
        JsonGetAreaTw : function (city_id) {
            $.ajax({
                url: "/Api/getAreaTw",
                type: 'get',
                data: {"city_id":city_id},
                dataType: 'json'
            }).done(function(bakjson) {
                if(bakjson.error == '1'){
                    errormotify(bakjson.errortip);
                    $('#area_id').html("<option value=\"0\">请先选择县市</option>").attr("disabled","disabled");
                }else{
                    var classHtml = "<option value=\"0\">请先选择县市</option>";
                    var listjson = bakjson.result;
                    for(var k in listjson){
                        classHtml += "<option value=\""+listjson[k].regional_id+"\">"+listjson[k].regional_name+"</option>";
                    }
                    $('#area_id').html(classHtml).removeAttr("disabled");
                }
            });
        }
    };
    //插件运行
    CMSJS.Start = {
        GoStart : function() {
            CMSJS.Comint.placeholderStart();
        }
    };

    $(document).ready(function() {
        CMSJS.Start.GoStart();

        //采购商下边的项目
        $(document).on('change', '.Puritem-select',function() {
            CMSJS.Comint.PurItemGet($(this).val());
        });

        //暖心店再用  台湾筛选城市
        $(document).on('change', '.CityTw-select',function() { alert();
            CMSJS.Comint.JsonGetAreaTw($(this).val());
        });

        //选择省市分类
        $(document).on('change', '.Prov-select',function() {
            CMSJS.Comint.JsonGetCity($(this).val());
        });

        //选择市区分类
        $(document).on('change', '.City-select',function() {
            CMSJS.Comint.JsonGetArea($(this).val());
        });
        //选择产品分类
        $(document).on('change', '.Cate-select',function() {
            CMSJS.Comint.JsonGetCate($(this).val());
        });

        $(".bakFromurl").click(function() {
            window.history.back();
        });

        $(".forWardurl").click(function() {
            window.history.go(+1);
        });
        $(".reloadurl").click(function() {
            window.location.reload();
        });
        //查看图片
        $(document).on("click", ".Opon-Img-View",function() {
            CMSJS.Tiplog.OpenLoadHtml('<img src="'+$(this).data("imgurl")+'">');
        });

        $(document).on("click", ".Opon-video-View",function() {
          CMSJS.Tiplog.OpenLoadHtml('<video controls autoplay src="'+$(this).data("videourl")+'"></video>');
        });


        //上传OSS插件
        $(document).on("change", ".oss-file-click",function() {
            var fileoriginalipt = $(this).data("originalipt");
            var file = this.files[0];
            var fd = new FormData();
            fd.append("ossfile", file);
            var xhr = new XMLHttpRequest();
            xhr.open("POST", '/Images/OssUpload');

            xhr.send(fd);

            //获取执行状态
            xhr.onreadystatechange = function() {
                //如果执行状态成功，那么就把返回信息写到指定的层里
                if (xhr.readyState == 4) {
                    if (xhr.status == 200) {
                        eval("var bakjson = " + xhr.responseText + ";");
                        if (bakjson.error == '1') {
                            errormotify(bakjson.errortip);
                        } else {
                            $("#" + fileoriginalipt).val(bakjson.link);
                        }
                    }
                }
            }
        });


        //上传OSS插件 --- 校务后台帮助中心视频上传
        $(document).on("change", ".oss-file-click-video",function() {
            var fileoriginalipt = $(this).data("originalipt");
            var vtype = $(this).data("vtype");
            var file = this.files[0];
            var fd = new FormData();
            fd.append("ossfile", file);
            var xhr = new XMLHttpRequest();
            xhr.open("POST", '/Images/OssUploadVideo?Type='+vtype);

            xhr.send(fd);

            //获取执行状态
            xhr.onreadystatechange = function() {
                //如果执行状态成功，那么就把返回信息写到指定的层里
                if (xhr.readyState == 4) {
                    if (xhr.status == 200) {
                        eval("var bakjson = " + xhr.responseText + ";");
                        if (bakjson.error == '1') {
                            errormotify(bakjson.errortip);
                        } else {
                            $("#" + fileoriginalipt).val(bakjson.link);
                        }
                    }
                }
            }
        });

        //上传图片插件
        $(document).on("change", ".ipt-img-click",function() {
            var imgelement = $(this).data("element"),imgoriginalipt = $(this).data("originalipt"),imgthumbnailipt = $(this).data("thumbnailipt");
            var file = this.files[0];
            if (!file || !file.type.match(/image.*/)) {
                errormotify("只可以上传图像文件");
                return false;
            }

            var fd = new FormData();
            fd.append("ossfile", file);
            var xhr = new XMLHttpRequest();
            xhr.open("POST", '/Images?c=Updata');

            xhr.send(fd);

            //获取执行状态
            xhr.onreadystatechange = function() {
                //如果执行状态成功，那么就把返回信息写到指定的层里
                if (xhr.readyState == 4) {
                    if (xhr.status == 200) {
                        eval("var bakjson = " + xhr.responseText + ";");
                        if (bakjson.error == '1') {
                            errormotify(bakjson.errortip);
                        } else {
                            var original = bakjson.originalimg + "?tempstr=" + Math.random(), thumbnail = bakjson.thumbnailimg + "?tempstr=" + Math.random();
                            $("#" + imgelement).find(".default").hide();
                            $("#" + imgelement).find(".img").show();
                            $("#" + imgelement).find("img").attr("src", original).show();
                            $("#" + imgoriginalipt).val(bakjson.originalimg);
                            $("#" + imgthumbnailipt).val(bakjson.thumbnailimg);
                        }
                    }
                }
            }
        });


        //上传文件插件 --- 单个文件上传
        $(document).on("change", ".ipt-onefile-click",function() {
            var imgelement = $(this).data("element"),imgname = $(this).data("imgname");
            var file = this.files[0];
            var fd = new FormData();
            fd.append("file", file);
            var xhr = new XMLHttpRequest();
            xhr.open("POST", '/Images/FilesUpload?'+'filename='+file.name);

            xhr.send(fd);

            //获取执行状态
            xhr.onreadystatechange = function() {
                //如果执行状态成功，那么就把返回信息写到指定的层里
                if (xhr.readyState == 4 && xhr.status == 200) {
                    eval("var bakjson = " + xhr.responseText + ";") ;
                    if (bakjson.error == '1') {
                        dangerFromTip(bakjson.errortip);
                    } else {
                        var Filehtml = '<li><a href="'+bakjson.link+'" target="_blank"><span class="glyphicon glyphicon-file"></span>'
                            +'<p class="glyphicon-class">'+bakjson.filename+'<i class="glyphicon glyphicon-remove delThis"></i></p>'
                            +'<input name="'+imgname+'" type="hidden" value="'+bakjson.fileid+'"></a></li>';

                        $("#" + imgelement).find(".FileList").html(Filehtml);
                        //var original = bakjson.originalimg + "?tempstr=" + Math.random(), thumbnail = bakjson.thumbnailimg + "?tempstr=" + Math.random();
                        ///$("#" + imgoriginalipt).val(bakjson.originalimg);
                        //$("#" + imgthumbnailipt).val(bakjson.thumbnailimg);
                    }
                }
            }
        });


        //上传文件插件
        $(document).on("change", ".ipt-file-click",function() {
            var imgelement = $(this).data("element");
            var file = this.files[0];
            var fd = new FormData();
            fd.append("file", file);
            var xhr = new XMLHttpRequest();
            xhr.open("POST", '/Images/FilesUpload?'+'filename='+file.name);

            xhr.send(fd);

            //获取执行状态
            xhr.onreadystatechange = function() {
                //如果执行状态成功，那么就把返回信息写到指定的层里
                if (xhr.readyState == 4 && xhr.status == 200) {
                    eval("var bakjson = " + xhr.responseText + ";") ;
                    if (bakjson.error == '1') {
                        dangerFromTip(bakjson.errortip);
                    } else {
                        var Filehtml = '<li><a href="'+bakjson.link+'" target="_blank"><span class="glyphicon glyphicon-file"></span>'
                            +'<p class="glyphicon-class">'+bakjson.filename+'<i class="glyphicon glyphicon-remove delThis"></i></p>'
                            +'<input name="affix[]" type="hidden" value="'+bakjson.fileid+'"></a></li>';
                        $("#" + imgelement).find(".FileList").append(Filehtml);
                        //var original = bakjson.originalimg + "?tempstr=" + Math.random(), thumbnail = bakjson.thumbnailimg + "?tempstr=" + Math.random();
                        ///$("#" + imgoriginalipt).val(bakjson.originalimg);
                        //$("#" + imgthumbnailipt).val(bakjson.thumbnailimg);
                    }
                }
            }
        });

        //删除数据记录
        $(document).on("click", ".btn-del-action",function(e) {
            e.preventDefault();
            var  acturl = $(this).data("url"),
                element = $(this).data("element"),
                errortip = $(this).data("errortip");

            CMSJS.Tiplog.FucWarningTip("消息提示","你确定删除此条记录么！",function(){
                $.ajax({
                    async: false,
                    contentType: "application/json",
                    dataType: "json",
                    type: "GET",
                    url: acturl,
                    data: "",
                    success: function(bakjson) {
                        if(bakjson.error == '1'){
                            errormotify(bakjson.errortip);
                        }else{
                            okmotify(bakjson.errortip);
                            $("#"+element).remove();
                            CMSJS.Tiplog.Close();
                        }
                    },
                    error: function() {
                        errormotify("机制错误");
                    }
                });
            },"确定删除","取消");
        });
        //确认信息操作
        $(document).on("click", ".btn-confirm-action",function(e) {
            e.preventDefault();
            var  acturl = $(this).data("url"),
                tiptitle = $(this).data("tiptitle"),
                element = $(this).data("element"),
                errortip = $(this).data("errortip");

            (typeof(tiptitle)=="undefined")?tiptitle="你确定如此操作么?":tiptitle=tiptitle;

            CMSJS.Tiplog.FucWarningTip("消息提示",tiptitle,function(){
                $.ajax({
                    async: false,
                    contentType: "application/json",
                    dataType: "json",
                    type: "GET",
                    url: acturl,
                    data: "",
                    success: function(bakjson) {
                        if(bakjson.error == '1'){
                            errormotify(bakjson.errortip);
                        }else{
                            okmotify(bakjson.errortip);
                            window.location.reload();
                            CMSJS.Tiplog.Close();
                        }
                    },
                    error: function() {
                        errormotify("机制错误");
                    }
                });
            },"确定","取消");
        });
        //操作数据记录
        $(document).on("click", ".btn-send-action",function(e) {
            e.preventDefault();
            var  acturl = $(this).data("url");
            $.ajax({
                url: acturl,
                type: 'get',
                dataType: 'json'
            }).done(function(bakjson) {
                if(bakjson.error == '1'){
                    errormotify(bakjson.errortip);
                }else{
                    refreshpage(bakjson.errortip);
                }
            });
        });
        //操作数据记录
        $(document).on("click", ".delThis",function(e) {
            e.preventDefault();
            $(this).parents("li").remove();
        });

        //选择体检城市
        $(document).on('change', '.Campus-select',function() {
            CMSJS.Comint.JsonGetClass($(this).val());
        });
        $(document).on('change', '.Campus-select',function() {
            CMSJS.Comint.JsonGetClass($(this).val());
        });
        if($("#qeditor_body,#qeditor_body1,#qeditor_body2").length > 0 ){
            //编辑器
            $("#qeditor_body,#qeditor_body1,#qeditor_body2").editable({
                inlineMode: false,
                alwaysBlank: true,
                language: "zh_cn",
                height:"500px",
                imageUploadURL: '/Images/Imgupload',//上传到本地服务器
                imageUploadParams: {id: "edit"},
                //imageDeleteURL: 'lib/delete_image.php',//删除图片
                imagesLoadURL: '/Images/Manage'//管理图片
            });
        }

        if($(".EditorBody").length > 0 ){
            //编辑器
            $(".EditorBody").editable({
                inlineMode: false,
                alwaysBlank: true,
                language: "zh_cn",
                height:"500px",
                imageUploadURL: '/Images/Imgupload',//上传到本地服务器
                imageUploadParams: {id: "edit"},
                //imageDeleteURL: 'lib/delete_image.php',//删除图片
                imagesLoadURL: '/Images/Manage'//管理图片
            });
        }
        if($("#Ueditorbody").length > 0 ){
            //编辑器
            var ue = UE.getEditor('Ueditorbody');
        }
        if($(".chosen-select").length > 0 ){
            //选择器
            $(".chosen-select").chosen();
        }


        //新增一行
        $(document).on("click", ".copyTableToNext",function() {
            var TdHtml = $(this).parents("tr.tablelist").html();
            var NewHtml = "<tr>"+TdHtml.replace(/(.*)(copyTableToNext)(.*)(\[)(\+)/i, "$1delTableTd$3$4-")+"</tr>";
            $(this).parents("tbody").append(NewHtml);
        });
        $(document).on("click", ".delTableTd",function() {
            $(this).parents("tr").remove();
        });




        //项目产品联动
        $(document).on("click", "#itemType",function() {
            var url = "?",send_fuc=$(this).data("fuc"),send_val = $(this).val();
            $.ajax({
                url: url,
                async: false,
                contentType: "application/json",
                dataType: "json",
                type: "GET",
                data: {
                    "c": send_fuc,
                    "value": send_val
                },
                success: function(bakjson) {
                    if (bakjson.error == '1') {
                        // errormotify(bakjson.errortip);
                    } else {
                        console.log(bakjson.listjson);
                        var dataList = bakjson.listjson;
                        var theSelect = new Array();
                        for (var i = 0; i < dataList.length; i++) {
                            theSelect[i] = "<option value="+dataList[i]['product_id']+">"+dataList[i]['product_name']+"</option>";
                        };
                        $("#productType").html(theSelect);
                    }
                },
                error: function() {
                    errormotify("Ajax信息触发有误！");
                }
            });
        });
        //客户项目联动
        $(document).on("click", "#clientType",function() {
            var url = "?",send_fuc=$(this).data("fuc"),send_val = $(this).val();
            $.ajax({
                url: url,
                async: false,
                contentType: "application/json",
                dataType: "json",
                type: "GET",
                data: {
                    "c": send_fuc,
                    "value": send_val
                },
                success: function(bakjson) {
                    if (bakjson.error == '1') {
                        errormotify(bakjson.errortip);
                    } else {
                        console.log(bakjson.listjson);
                        var dataList = bakjson.listjson;
                        var theSelect = new Array();
                        for (var i = 0; i < dataList.length; i++) {
                            theSelect[i] = "<option value="+dataList[i]['item_id']+">"+dataList[i]['item_name']+"</option>";
                        };
                        $("#itemType").html(theSelect);
                    }
                },
                error: function() {
                    errormotify("Ajax信息触发有误！");
                }
            });
        });
        //数据展开
        $(document).on("click", ".OneMenuClick",function(e) {
            e.preventDefault();
            if($(this).hasClass("glyphicon-eye-open")){
                $(this).addClass("glyphicon-eye-close").removeClass("glyphicon-eye-open");
                $(this).parents(".OneMenu").nextUntil(".OneMenu").show();
            }else{
                $(this).addClass("glyphicon-eye-open").removeClass("glyphicon-eye-close");
                $(this).parents(".OneMenu").nextUntil(".OneMenu").hide();
            }
        });
        //数据展开
        $(document).on("click", ".TwoMenuClick",function(e) {
            e.preventDefault();
            if($(this).hasClass("glyphicon-eye-open")){
                $(this).addClass("glyphicon-eye-close").removeClass("glyphicon-eye-open");
                $(this).parents(".TwoMenu").nextUntil(".TwoMenu").show();
            }else{
                $(this).addClass("glyphicon-eye-open").removeClass("glyphicon-eye-close");
                $(this).parents(".TwoMenu").nextUntil(".TwoMenu").hide();
            }
        });
        //数据展开
        $(document).on("click", ".threeMenuClick",function(e) {
            e.preventDefault();
            if($(this).hasClass("glyphicon-eye-open")){
                $(this).addClass("glyphicon-eye-close").removeClass("glyphicon-eye-open");
                $(this).parents(".FourMenu").nextUntil(".FourMenu").show();
            }else{
                $(this).addClass("glyphicon-eye-open").removeClass("glyphicon-eye-close");
                $(this).parents(".FourMenu").nextUntil(".FourMenu").hide();
            }
        });

        //全选按钮
        $("#Choice_All").change(function(){
            if($(this).prop("checked")){
                $("input[name='tab_list[]']").prop("checked",true);
            }else{
                $("input[name='tab_list[]']").prop("checked",false);
            }
        });


        // 新增题目 type11-type21
        $('.upload-img > img').on('click', function() {
            var $t = $(this),
                $file = $t.siblings('.tf').find('.upload-btn .ipt-img-click');

            $file.trigger('click');
        })
        // type21、type20 type14 type11 disable
        var $eableGroup = $('#operateType21,#operateType20, #operateType14, #operateType11, #operateType12, #operateType13');
        $eableGroup.on('click', '.extend .close', function() {
            var $t = $(this),
                $p = $t.parent(),
                $n = $p.nextAll('.extend'),
                hasNext = $n.length > 0;
            $p.addClass('none').find('input').prop('disabled', true);
            if (hasNext) {
                $n.addClass('none').find('input').prop('disabled', true);
            }
        })
        // type21、type20 type14 type11 enable
        $eableGroup.on('click', '.add input.action', function() {
            var $t = $eableGroup.find('.upload-img.none').eq(0);
            if ($t.length > 0) {
                $t.removeClass('none').find('input').prop('disabled', false);
            }
        })
        // type19 disable
        $('#operateType19').on('click', '.extend .close', function() {
            var $t = $(this),
                $p = $t.parent(),
                $prev = $p.prev(),
                $n = $p.nextAll('.extend'),
                hasNext = $n.length > 0;
            $p.addClass('none').find('input').prop('disabled', true);
            $prev.addClass('none').find('input').prop('disabled', true);
            if (hasNext) {
                $n.addClass('none').find('input').prop('disabled', true);
            }
        })
        // type19 enable
        $('#operateType19').on('click', '.add input.action', function() {
            var $t = $('#operateType19').find('.upload-img.none');
            if ($t.length > 1) {
                $t.eq(0).removeClass('none').find('input').prop('disabled', false);
                $t.eq(1).removeClass('none').find('input').prop('disabled', false);
            }
        })
        // type17
        $('#questionType17Sel').on('change', function() {
            var $t = $(this),
                v = $t.val(),
                $p = $('#operateType17'),
                top = '#operateType17 .top',
                btm = '#operateType17 .bottom',
                $tb = $(top).add(btm);

            if (v == '') {
                $p.hide();
            } else {
                $p.show();
            }
            
            $p.addClass('auto');
            switch(v) {
                case '0':
                $tb.addClass('type-img').removeClass('type-txt')
                $tb.find('input[name="answers_isimg[]"]').val('1')
                break;
                case '1':
                $tb.addClass('type-txt').removeClass('type-img')
                $tb.find('input[name="answers_isimg[]"]').val('0')
                break;
                case '2':
                $(top).addClass('type-img').removeClass('type-txt')
                $(top).find('input[name="answers_isimg[]"]').val('1')                
                $(btm).addClass('type-txt').removeClass('type-img')
                $(btm).find('input[name="answers_isimg[]"]').val('0')
                break;
                default:
                $p.removeClass('auto');
                return;
            }
        })
        // type17 disable
        $('#operateType17').on('click', '.extend .close', function() {
            var $t = $(this),
                $ext = $t.parent(),
                Index = $ext.index() - 1,
                top = '#operateType17 .top .upload-img:gt('+ Index +'):not(.add)',
                btm = '#operateType17 .bottom .upload-img:gt('+ Index +')';

            $(top).add(btm).addClass('none').find('input').prop('disabled', true);
        })
        // type17 enable
        $('#operateType17').on('click', '.add input.action', function() {
            var top = '#operateType17 .top .upload-img.none:eq(0)',
                bottom = '#operateType17 .bottom .upload-img.none:eq(0)';
            $(top).add(bottom).removeClass('none').find('input').prop('disabled', false);
        })



    });

})(jQuery);


//错误提示
function dangerFromTip(tip){
    var Tiphtm = '<div class="alert alert-danger alert-dismissible" role="alert">'
            + '<button type="button" class="close" data-dismiss="alert"><span aria-hidden="true">×</span><span class="sr-only">Close</span></button>'
            + '<span class="glyphicon glyphicon-remove-circle f16 vam mr6"></span>'+ tip +'</div>';
    $(Tiphtm).prependTo("#Form-Box-Operating");
    setTimeout(function(){ $("#Form-Box-Operating").children(".alert-danger").hide(); },3000);
}

//警告提示
function warningFromTip(tip){
    var Tiphtm = '<div class="alert alert-warning alert-dismissible" role="alert">'
    + '<button type="button" class="close" data-dismiss="alert"><span aria-hidden="true">×</span><span class="sr-only">Close</span></button>'
    + '<span class="glyphicon glyphicon-exclamation-sign f16 vam mr6"></span>'+ tip +'</div>';
    $(Tiphtm).prependTo("#Form-Box-Operating");
    setTimeout(function(){ $("#Form-Box-Operating").children(".alert-warning").hide(); },3000);
}
//正确提示
function successFromTip(tip){
    var Tiphtm = '<div class="alert alert-success alert-dismissible" role="alert">'
        + '<button type="button" class="close" data-dismiss="alert"><span aria-hidden="true">×</span><span class="sr-only">Close</span></button>'
        + '<span class="glyphicon glyphicon-ok-circle f16 vam mr6"></span>'+ tip +'</div>';
    $(Tiphtm).prependTo("#Form-Box-Operating");
    setTimeout(function(){ $("#Form-Box-Operating").children(".alert-success").hide(); },3000);
}

//错误提示
function errormotify(tip){
    $('<div class="errormotify"><div class="motify-inner"><span></span>'+tip+'</div></div>').appendTo("body");
    setTimeout(function(){ $(".errormotify").hide(); },3000);
}

//正确提示
function okmotify(tip){
    $('<div class="okmotify"><div class="motify-inner"><span></span>'+tip+'</div></div>').appendTo("body");
    setTimeout(function(){ $(".okmotify").hide(); },3000);
}

function refreshpage(tip){
    $('<div class="okmotify"><div class="motify-inner"><span></span>'+tip+'</div></div>').appendTo("body");
    setTimeout(function(){ $(".okmotify").hide(); },3000);
    window.location.reload();//刷新当前页面.
}
