/**
 * Created by Administrator on 2016/12/14.
 */
/**
 * Created by mohism on 2016/12/14.
 */
/*
 Name : 站点插件管理器
 Copyright (C) 2015 - 2017
 WebSite:	http://www.mohism.cn/
 Author:		<PERSON><PERSON><PERSON>gong
 */
(function($){
    //插件运行
    CMSJS.ApiJson = {
        goAjax : function(apiurl,fun) {

        }
    };

    $(document).ready(function() {
        //所属集团
        $(".companyAjax").select2({
            ajax: {
                type:'GET',
                url: "/Api/getCompany",
                dataType: 'json',
                delay: 250,
                data: function (params) {
                    return {
                        nature:$("#company_id").val(),
                        keyword: params.term=='1'?"":params.term,
                        member_id:"manage",
                        page: params.page
                    };
                },
                processResults: function (data, params) {
                    params.page = params.page || 1;
                    var itemList = [];//当数据对象不是{id:0,text:'ANTS'}这种形式的时候，可以使用类似此方法创建新的数组对象
                    var arr = data.result.list;
                    for(item in arr){
                        itemList.push({id: arr[item].company_id, text: arr[item].company_cnname})
                    }
                    return {
                        results: itemList,
                        pagination: {
                            more: (params.page * 30) < data.total_count
                        }
                    };
                },
                cache: true
            },
            language: "zh-CN",
            tags: false,//允许手动添加
            allowClear: true,//允许清空
            width:'100%',
            escapeMarkup: function (markup) { return markup; }, // 自定义格式化防止xss注入
            //minimumInputLength: 1,//最少输入多少个字符后开始查询
            formatResult: function formatRepo(repo){return repo.text;}, // 函数用来渲染结果
            formatSelection: function formatRepoSelection(repo){return repo.text;} // 函数用于呈现当前的选择
        });
        $(".courseAjax").select2({
            ajax: {
                type:'GET',
                url: "/Api/getCourse",
                dataType: 'json',
                delay: 250,
                data: function (params) {
                    return {
                        nature:$("#course_id").val(),
                        keyword: params.term=='1'?"":params.term,
                        member_id:"manage",
                        company_id:$("#company_id").val(),
                        page: params.page
                    };
                },
                processResults: function (data, params) {
                    params.page = params.page || 1;
                    var itemList = [];//当数据对象不是{id:0,text:'ANTS'}这种形式的时候，可以使用类似此方法创建新的数组对象
                    var arr = data.result.list;
                    for(item in arr){
                        itemList.push({id: arr[item].course_id, text: arr[item].course_cnname})
                    }
                    return {
                        results: itemList,
                        pagination: {
                            more: (params.page * 30) < data.total_count
                        }
                    };
                },
                cache: true
            },
            language: "zh-CN",
            tags: false,//允许手动添加
            allowClear: true,//允许清空
            width:'100%',
            escapeMarkup: function (markup) { return markup; }, // 自定义格式化防止xss注入
            //minimumInputLength: 1,//最少输入多少个字符后开始查询
            formatResult: function formatRepo(repo){return repo.text;}, // 函数用来渲染结果
            formatSelection: function formatRepoSelection(repo){return repo.text;} // 函数用于呈现当前的选择
        });





    });
})(jQuery);