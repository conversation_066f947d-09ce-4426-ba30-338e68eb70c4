// 后台显示页js控制台
$(window.parent.document).find("#mianfm").load(function(){
  var minheight = parseInt($(window.parent).height())-parseInt($(window.parent.document).find("#header").height());
  var main = $(window.parent.document).find("#mianfm");
  main.height(minheight);
  /*var thisheight = $("body").height()+30;
  if(thisheight < minheight){
	main.height(minheight);
  }else{
	main.height(thisheight);
  }*/
});

if(window.jQuery){
	$(document).ready(function(){
		$('input#starttime').focus(function(){
			WdatePicker({dateFmt:'yyyy-MM-dd',maxDate:'%y-%M-%d'});
		});
		$('input#enttime').focus(function(){
		   WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'starttime\')}'});
		});
		$('input#starttimes').focus(function(){
			WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss'});
		});
		$('input#enttimes').focus(function(){
		   WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss',minDate:'#F{$dp.$D(\'starttimes\')}'});
		});
		$('input#timesecond').focus(function(){
		   WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss'});
		});
		
		// $("input[id$=_weight]").keynumLimit();
		// $("input[id$=_booknum]").keynumLimit();
		
		//栏目展开影藏
		$("img[id^=img_]").click(function(){
			var id = $(this).attr("id").split("_")[1];
	
			if($(this).attr("src") == "images/fclose.png"){
				$(".trtwo_"+id).hide();
				$(".trthree_"+id).hide();
				$(this).attr("src","images/fopen.png");
			}else{
				$(".trtwo_"+id).show();
				$(this).attr("src","images/fclose.png");
			}
	
		});
		$("img[id^=imgtwo_]").click(function(){
			var id = $(this).attr("id").split("_")[1];
			if($(this).attr("src") == "images/fclose.png"){
				$(".twothree_"+id).hide();
				$(this).attr("src","images/fopen.png");
			}else{
				$(".twothree_"+id).show();
				$(this).attr("src","images/fclose.png");
			}
	
		});
		
		$(".table_lmgl th[field]").mouseover(function(){
				$(this).css("cursor","pointer");
		});
		
		$(".table_lmgl th[field]").click(function(){
			if($(this).attr("class") == '' || typeof $(this).attr("class") == 'undefined'){
				$(this).attr("class","asc");
				Utils.In_link("?orderby="+ $(this).attr("field") + "&sequence=asc");
			}else if($(this).attr("class") == 'asc'){
				$(this).attr("class","desc");
				Utils.In_link("?orderby="+ $(this).attr("field") + "&sequence=desc");
			}else if($(this).attr("class") == 'desc'){
				$(this).attr("class","");
				Utils.In_link("?clearsession=1");
			}
		});
		
		if("undefined" !== typeof orderby){
		  $(".table_lmgl th[field="+ orderby +"]").attr("class",sequence);
		}
		
		// $(".playtype a").mouseclickdivCur({ tabId:".margins"});
		
		$("#addArticle").click(function(){
  　　　　　　　if($("#Article_select option:selected").length>0)
  　　　　　　　{
  　　　　　　　　　　　$("#Article_select option:selected").each(function(){
  　　　　　　　　　　　　　　$("#Article_list").append("<option value='"+$(this).val()+"'>"+$(this).text()+"</option");
  　　　　　　　　　　　　　　$(this).remove();　
  　　　　　　　　　　　})
  　　　　　　　}
  　　　　　　　else
  　　　　　　　{
				alert("请选择要添加的关联文章！");
  　　　　　　　}
  　　　})
	 $("#addArticleall").click(function(){
			$("#Article_select option").each(function(){
				$("#Article_list").append("<option value='"+$(this).val()+"'>"+$(this).text()+"</option");
				$(this).remove();　
		    })
  　　　})
      
	 $("#addmainarticle").click(function(){
		$("#Article_list option").each(function(){
			if($("#tb_mainarticle").val() == ''){
                $("#tb_mainarticle").val($(this).val());
			}else {
                $("#tb_mainarticle").val($(this).val() + ',' + $("#tb_mainarticle").val());
            }
			$(this).remove();　
		})
		// $("#tb_mainarticle").val($("#tb_mainarticle").val().slice(0,-1))
  　　　})
	 $("#addfumainarticle").click(function(){
		var fuarticle = '';
		var optionHtml = '';
		if($("input#futitle").val() !==''){
			var futitle = $("input#futitle").val();
			$("#Article_list option").each(function(){
				var $t = $(this);
				fuarticle = $t.val() + ',' + fuarticle;
                optionHtml += '<option value="'+ $t.val() + '">' + $t.text() + '</option>';
				$t.remove();
			})
			fuarticle = fuarticle.slice(0,-1);
			
			var spl = 1;
			$("input[name='fu_title[]']").each(function(){
				if($(this).val() == futitle){
					 spl = 0;
				}
			})
			if(spl == 1){
				// $("label.fulisttop").after('<p>副主题名称：<input name="fu_title[]" class="input-text" type="text" value="'+futitle+'" /> 副主题文章：<input name="fu_tarticle[]" class="input-text" type="text" value="'+fuarticle+'" /><a href="javascript:;" onclick="delThisp(this)">[-]</a></p>');
                $("label.fulisttop").after('<div class="row"><div class="col-md-5"><div class="input-group"><span class="input-group-addon">副主题名称</span><input name="fu_title[]" value="'+futitle+'" type="text" class="form-control"></div></div><div class="col-md-5"><div class="input-group"><span class="input-group-addon">副主题文章</span><input type="text" name="fu_tarticle[]" value="'+fuarticle+'" class="form-control"></div></div><div class="col-md-2" onclick="delThisp(this)"><span class="btn">[&times;]</span></div></div>');
			}else{
				$('#Article_list').html(optionHtml);
				alert('冲突的附加主题名称！');
			}
			
		}
  　　　})
		
		$("#deleteArticle").click(function(){
  　　　　　　　if($("#Article_list option:selected").length>0)
			  {
				  $("#Article_list option:selected").each(function(){
					  　　$("#Article_select").append("<option value='"+$(this).val()+"'>"+$(this).text()+"</option");
					  　　$(this).remove();　
					  　　})
			  }
  　　　　　　　else
  　　　　　　　{
				alert("请选择要取消的关联文章！");
  　　　　　　　}
  　　　})
		
		$("#deleteArticleall").click(function(){
  　　　　　　　$("#Article_list option").each(function(){
			　　$("#Article_select").append("<option value='"+$(this).val()+"'>"+$(this).text()+"</option");
			　　$(this).remove();　
		  　　})
  　　　})
		
		$("#bakurlfrom").click(function(){
			history.go(-1);
  　　　})
  
		//上传图片
		// $(".ipt-file").live("change",function(){
		// 	var thisobj = $(this);
		// 	var file = this.files[0];
		// 	if (!file || !file.type.match(/image.*/)) {
		// 	  alert("只可以上传图像文件");
		// 	  return false;
		// 	}
		//
		// 	var fd = new FormData();
		// 	fd.append("image", file);
		// 	//fd.append("key", "b7ea18a4ecbda8e92203fa4968d10660");
		// 	var xhr = new XMLHttpRequest();
		// 	xhr.open("POST", '/admin/?act_to=upimg');
		//
		// 	xhr.send(fd);
		//
		// 	//获取执行状态
		// 	xhr.onreadystatechange = function() {
		// 		//如果执行状态成功，那么就把返回信息写到指定的层里
		// 		if (xhr.readyState == 4 && xhr.status == 200) {
		// 			eval("var objarray = " + xhr.responseText + ";") ;
		// 			if(objarray.error == '1'){
		// 			  alert(objarray.errorinfo);
		// 			}else{
		// 			  var src = objarray.links+"?tempstr=" + Math.random();
		// 			  thisobj.parents(".img-upload").find("img.js-img").attr("src",src).show();
		// 			  thisobj.parents(".img-upload").find("input.js-upload-id").val(objarray.imgtmh);
		// 			}
		// 		}
		// 	}
		// });
		
	})
}

//文章添加判断
function addtype(){
	var newstr='';
	for(a=0;a<Utils.$nm("allow").length;a++){
		if(Utils.$nm("allow")[a].checked == true){
			newstr += '|'+Utils.$nm("allow")[a].value+'|';
		}
	}
	if(newstr ==''){
		alert('没有选择任何数据');
		$('#tb_allow').val("all")
	}else{
		//$('#tb_allow').val(newstr.slice(0,-1));
		$('#tb_allow').val(newstr);
	}
}
//文章添加tags判断
function addtags(){
	var newstr='';
	for(a=0;a<Utils.$nm("tagsvar").length;a++){
		if(Utils.$nm("tagsvar")[a].checked == true){
			newstr +=  Utils.$nm("tagsvar")[a].value+',';
		}
	}
	if(newstr ==''){
		alert('没有选择任何数据');
		$('#cpat_tags').val(" ")
	}else{
		$('#cpat_tags').val(newstr.slice(0,-1));
		//$('#cpat_tags').val(newstr);
	}
}

function addItem(){
	if($("#Article_select option:selected").length>0)
	{
		$("#Article_select option:selected").each(function(){
			　　$("#Article_list").append("<option value='"+$(this).val()+"'>"+$(this).text()+"</option");
			　　$(this).remove();　
			　　})
	}
	else
	{
		alert("请选择要添加的关联文章！");
	}
}

function delItem(){
	if($("#Article_list option:selected").length>0)
	{
		$("#Article_list option:selected").each(function(){
			　　$("#Article_select").append("<option value='"+$(this).val()+"'>"+$(this).text()+"</option");
			　　$(this).remove();　
			　　})
	}
	else
	{
		alert("请选择要取消的关联文章！");
	}
}



function confirm_del(){
  if(confirm('确认删除吗？')) $('#playfm').submit();
}

function confirm_action(txt){
  if(confirm(txt)) $('#playfm').submit();
}


function confirm_transfer(){
  if(confirm('确认移动/复制吗？')){
	  var falg = 0;
	  $("input[name='chick_id[]']:checkbox").each(function () {  if ($(this).attr("checked")) {  falg += 1;  } }) 
	  if($('#twomenu').val() == '0'){
		 alert('没有选择移动至的栏目！');
		 return false;
	  }else if(falg < 1){
		 alert('没有选择任何移动项！');
		 return false;
	  }else{
		$('#playfm').submit();
	  }
  }
}

function confirm_nofalg(){
  if(confirm('确认移动吗？')){
	  var falg = 0;
	  $("input[name='chick_id[]']:checkbox").each(function () {  if ($(this).attr("checked")) {  falg += 1;  } }) 
	  if(falg < 1){
		 alert('没有选择任何移动项！');
		 return false;
	  }else{
		$('#playfm').submit();
	  }
  }
}

function addarticleurl(){
  if(confirm('确认添加此栏目文章？')){
	  if($('#twomenu').val() == '0'){
		 alert('没有选择要添加的栏目！');
		 return false;
	  }else{
		  if($('#threemenu').val() == '0'){
			  Utils.In_link('article.php?act_url=add&menuid='+$('#twomenu').val());
		  }else{
			  Utils.In_link('article.php?act_url=add&menuid='+$('#threemenu').val());
		  }
	  }
  }
}
function addfileurl(){
  if(confirm('确认上传此栏目文件？')){
	  if($('#twomenu').val() == '0'){
		 alert('没有选择要添加的栏目！');
		 return false;
	  }else{
		  if($('#threemenu').val() == '0'){
			  Utils.In_link('file.php?act_url=add&menuid='+$('#twomenu').val());
		  }else{
			  Utils.In_link('file.php?act_url=add&menuid='+$('#threemenu').val());
		  }
	  }
  }
}
function addresourcesurl(){
  if(confirm('确认添加此栏目资源？')){
	  if($('#twomenu').val() == '0'){
		 alert('没有选择要添加的栏目！');
		 return false;
	  }else{
		  if($('#threemenu').val() == '0'){
			  Utils.In_link('resources.php?act_url=add&menuid='+$('#twomenu').val());
		  }else{
			  Utils.In_link('resources.php?act_url=add&menuid='+$('#threemenu').val());
		  }
	  }
  }
}

/**
* 新增一个图片
*/
function addImg(obj)
{
	var src  = obj.parentNode.parentNode;
	var idx  = rowindex(src);
	var tbl  = obj.parentNode.parentNode.parentNode;
	var row  = tbl.insertRow(idx + 1);
	var cell = row.insertCell(-1);
	cell.innerHTML = src.cells[0].innerHTML.replace(/(.*)(addImg)(.*)(\[)(\+)/i, "$1removeImg$3$4-");
}

/**
* 删除图片上传
*/
function removeImg(obj)
{
	var row = rowindex(obj.parentNode.parentNode);
	var tbl = obj.parentNode.parentNode.parentNode;
	tbl.deleteRow(row);
}

/**
* 删除本父节点
*/
function delThisp(obj)
{
	var row = obj.parentNode;
	var tbl = obj.parentNode.parentNode;
	tbl.removeChild(row);
}


//文章添加判断
function addArticleok(){
	var id = "";
	var x = document.getElementById("Article_list");
	var len = x.length;
	for(var i=0; i<len; i++){
		 id += x.options[i].value+","; 
	}
	id = id.substr(0, id.length-1);
	document.getElementById("cpat_associate").value = id;
	
	if($('#cpat_name').val() == ''){
		alert('名称不可为空');
		return false;
	}
	if($('#twomenu').val() == '0'){
		alert('栏目未选择');
		return false;
	}
	return true;
}


function field_setting(fieldtype) {
  $.getJSON("?act_to=field_setting&fieldtype="+fieldtype, function(data){
	  $('#setting').html(data.setting);
  });
}