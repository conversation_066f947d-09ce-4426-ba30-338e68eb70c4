/**
 * @author: 鲜成 <PERSON>
 * @since: 2018-10-24
 * @description: 制作游戏地图、渲染制作的地图
 */

$(function () {
    // init
    timer(5000);
    //获取地图背景资源
    $.get("/AppMaps/getResource", {
        "maps_id": $("#maps_id").val()
    }, function (bakjson) {
        if (bakjson.error == '1') {
            errormotify(bakjson.errortip);
        } else {
            okmotify(bakjson.errortip);
            bakjson.resource && renderBg(bakjson.resource);
        }
    }, 'json');

    // main
    var bakToU3D,
        bkg_images = [], // 'urls'
        steps = [],
        animations = [];

    var $tmpl = $('#tmpl'),
        $pad = $('.sketchpad'),
        sof = $pad.offset(),
        $gameview = $('.gameview'),
        bgList = [],
        style = { left: '79px', top: '82px', position: 'fixed' };

    // 删除图层
    $pad.on('click', '.close', function () {
        lock();
        $(this).parent().parent().remove();
        delImgBg($(this).parent().siblings('img').attr('src'));
    })
    // 锁定图层
    $pad.on('click', '.confirm', function () {
        lock();
    })
    // 背景图上传
    $('#bgfile').on('change', function () {
        lock();
        var $t = $(this);
        $t.prop('disabled', true);
        if (this.files.length < 1) {
            $t.prop('disabled', false)
        }
        // ossImg(this.files, function (url) {
        //     setImgGroup($gameview, '.bg', url);
        // })
        var arrayFiles = [];
        Array.prototype.push.apply(arrayFiles, this.files);
        fileUpStep();
        function fileUpStep() {
            ossImg(arrayFiles, function(url) {
                setImgGroup($gameview, '.bg', url);
                arrayFiles.shift();
                fileUpStep();
                if (arrayFiles.length < 1) {
                    $t.prop('disabled', false)
                }
            })
        }
    });
    // 关卡图上传
    $('#stepfile').on('change', function () {
        lock();
        ossImg(this.files, function (url) {
            var $step = setImgGroup($pad, '.step', url, style);
            $step.find('.order').text($('.gameview .step').length + 1);
        })
    });
    // 装饰图片、动画组上传
    var groupImgs = [],
        isNextFrame = true,
        $groupFrag;
    $('#decorfile').on('change', function () {
        if (isNextFrame) lock();
        ossImg(this.files, function (url) {
            groupImgs.push(url);
            if (isNextFrame) {
                isNextFrame = false;
                $('.maindecor').addClass('open');
                $groupFrag = setImgGroup($pad, '.decorate', url, style);
            }
            if (groupImgs.length > 1) {
                if (groupImgs.length > 2) {
                    groupImgs.shift();
                }
                $groupFrag.find('img').attr('src', groupImgs[1]);
            }

            $groupFrag.find('img').data('src', groupImgs.join(',,'));
        })
    });
    // 图片组上传完成
    $('#groupCancel').click(function () {
        lock();
    })
    // 背景图修改
    $pad.on('change', '.bg .ctrl-bar input', function(e) {
        lock();
        var _this = this;
        ossImg(_this.files, function(url) {
            $(_this).parents('.bg').find('img').attr('src', url);
        })
    })
    // 拖动
    var isDragAble = false;
    $('.draft').on('mousedown', '.sketchpad > .none', function (e) {
        if(e.stopPropagation) e.stopPropagation();
        if(e.preventDefault) e.preventDefault();
        e.cancelBubble=true;
        e.returnValue=false;

        var ofs = $(this).offset(),
            coor = { x: 0, y: 0 };

        isDragAble = true;
        coor.x = e.pageX - ofs.left;
        coor.y = e.pageY - ofs.top;

        $(this).mousemove(function (e) {
            var $t = $('.sketchpad > .none'),
                left = parseInt($t.css('left')),
                top = parseInt($t.css('top'));

            if (isDragAble) {
                $t.css({
                    left: e.pageX - coor.x,
                    top: e.pageY - coor.y
                })
                var coorIn = mkCoordinate($t, true);
                $t.find('span').html('x: ' + coorIn.x + ',<br>y: ' + coorIn.y);
            }
        })

        $(this).one('mouseup', function () {
            isDragAble = false;
            $(this).unbind();
        })
        $(this).one('mouseleave', function () {
            isDragAble = false;
            $(this).unbind();
        })
    })
    // 图层解锁
    $('.draft').on('dblclick', '.gameview .step, .gameview .decorate', function(e) {
        unlock($(this));
    })
    // 提交数据
    $('#save').click(function () {
        lock();
        if (!mkBg()) {
            alert('没有没有上传背景图');
            return;
        }

        bakToU3D = {
            bkg_images: bkg_images,
            steps: steps,
            animations: animations
        }

        $.post("/AppMaps?c=saveResource", {
            "maps_id": $("#maps_id").val(),
            "maps_resource": JSON.stringify(bakToU3D)
        }, function(bakjson) {
            if (bakjson.error == '1') {
                errormotify(bakjson.errortip);
            } else {
                okmotify(bakjson.errortip);
            }
        }, 'json');
    })

    // helper
    function toFix6(num) {
        return Math.ceil(num * 1000000) / 1000000;
    }
    function lock() {
        var $lock = $('.sketchpad > .none'),
            left = parseInt($lock.css('left')) - sof.left,
            top = parseInt($lock.css('top')) - sof.top,
            scrollLeft = $pad.scrollLeft(),
            style = {
                position: 'absolute',
                left: scrollLeft + left,
                top: top
            };

        $lock.css(style);
        $gameview.append($lock);

        // reset
        if(!isNextFrame) {
            isNextFrame = true;
            groupImgs = [];
            $groupFrag = null;
            $('.maindecor').removeClass('open');
        }
    }
    function unlock($t) {
        lock();
        var scrollLeft = $pad.scrollLeft(),
            left = parseInt($t.css('left')) + sof.left - scrollLeft,
            top = parseInt($t.css('top')) + sof.top,
            style = {
                position: 'fixed',
                left: left + 'px',
                top: top + 'px'
            };
        $t.css(style);
        $pad.append($t);
    }
    function setImgGroup($p, g, url, style) {
        var $frag = $tmpl.find(g).clone(),
            $img = $frag.find('img'),
            img = new Image();

        img.onload = function () {
            if (g == '.bg') {
                bgList.push({
                    url: url,
                    r: img.width / img.height
                })
                setStageWidth();
            } else {
                $img.css({ width: img.width / 2 });
            }
        }
        img.src = url;

        $img.attr('src', url);
        if (style) {
            $frag.css(style);
        }
        $p.append($frag);
        return $frag;
    }
    function delImgBg(url) {
        var del = -1;
        bgList.forEach(function(item, i) {
            if (url === item.url) {
                del = i;
            }
        })
        if (del > -1) {
            bgList.splice(del, 1);
        }
        setStageWidth();
    }
    function ossImg(files, cb) {
        var xhr = new XMLHttpRequest(),
            fd = new FormData();
        if (files.length < 1) return;
        fd.append('ossfile', files[0]);
        xhr.open("POST", '/Images/OssUpload?Type=maps');
        xhr.send(fd);

        //获取执行状态
        xhr.onreadystatechange = function () {
            //如果执行状态成功，那么就把返回信息写到指定的层里
            if (xhr.readyState == 4) {
                if (xhr.status == 200) {
                    eval("var bakjson = " + xhr.responseText + ";");
                    if (bakjson.error == '0') {
                        cb && cb(bakjson.link);
                    }
                }
            }
        }
    }
    function mkCoordinate($item, isDragAble) {
        var left = parseInt($item.css('left')),
            pl = $pad.scrollLeft(),
            top = parseInt($item.css('top')),
            w = $gameview.width(),
            h = $gameview.height();

        if (isDragAble) {
            left = left - sof.left + pl;
            top = top - sof.top;
        }

        return {
            x: toFix6(left / w),
            y: toFix6(top / h)
        }
    }
    function setStageWidth() {
        var w = 0,
            h = $gameview.height();
        bgList.forEach(function(d) {
            w += d.r * h;
        })
        $gameview.css({ width: Math.ceil(w) + 'px' });
    }

    //make bk data
    function mkBg() {
        bkg_images = [];
        steps = [];
        animations = [];

        $('.gameview .bg img').each(function () {
            bkg_images.push($(this).attr('src'));
        })
        $('.gameview .step').each(function (index, item) {
            steps.push({
                image: $(item).find('img').attr('src'),
                coordinate: mkCoordinate($(item)),
                key: +$(item).find('.order').text()
            })
        })

        steps.sort(function(next, prev) {
            return next.key > prev.key
        })

        $('.gameview .decorate').each(function (index, item) {
            animations.push({
                image_sequence: $(item).find('img').data('src').split(',,'),
                coordinate: mkCoordinate($(item))
            })
            animations.sort(function(next, prev) {
                return next.coordinate.x > prev.coordinate.x || next.coordinate.y > prev.coordinate.y
            })
        })
        return bkg_images.length > 0;
    }
    // render helper
    function renderBg(u3dJson) {
        var imgs = u3dJson.bkg_images,
            steps = u3dJson.steps,
            animations = u3dJson.animations;

        // stage render
        var progress = 0,
            progAll = imgs.length,
            stepProg = 0,
            stepALl = steps.length + animations.length;
        imgStepLoader();

        function scenRender() {
            var fragment = document.createDocumentFragment();
            steps.forEach(function(item) {
                var $step = renderStep('.step', item, item.image);
                $(fragment).append($step);
            });
            animations.forEach(function(item) {
                var $animation = renderStep('.decorate', item, item.image_sequence[item.image_sequence.length - 1]);
                $animation.find('img').data('src', item.image_sequence.join(',,'));
                $(fragment).append($animation);
            });
            $gameview.append($(fragment));
        }
        //step helper
        function renderStep(g, item, src) {
            var $imgGroup = $tmpl.find(g).clone(),
                left = $gameview.width() * item.coordinate.x,
                top = $gameview.height() * item.coordinate.y,
                img = $imgGroup.find('img')[0],
                $order = $imgGroup.find('.order');
            $imgGroup.css({
                left: Math.round(left) + 'px',
                top: Math.round(top) + 'px'
            });
            img.onload = function() {
                $(img).css({
                    width: img.width / 2
                })
                stepProg++;
                $('.draft .loading').text('关卡加载中：'+ stepProg + '/' + stepALl);
                if (stepProg === stepALl) {
                    $('.draft .loading').text('加载完成');
                    setTimeout(function() {
                        $('.draft .loading').text('');
                    },1000)
                }
                img.onload = null;
            }
            img.src = src;
            if (item.key) {
                $order.text(item.key);
            }
            return $imgGroup;
        }
        function imgStepLoader() {
            var url = imgs.pop();
            if (!url) return;
            var $bg = $tmpl.find('.bg').clone(),
                $img =  $bg.find('img'),
                img = $img[0];
            img.onload = function () {
                bgList.push({
                    url: url,
                    r: toFix6(img.width/img.height)
                })
                progress++;
                setStageWidth();
                $('.draft .loading').text('背景加载中：'+progress+'/'+progAll);
                if (progress === progAll) {
                    scenRender();
                    $('.draft .loading').text('关卡加载中：0/'+stepALl);
                }
                img.onload = null;
                $gameview.prepend($bg);
                imgStepLoader()
            }
            img.onerror = function() {
                alert('背景地图丢失，需要重新保存');
            }
            $img.attr('src', url);
        }
    }
    // animation
    function timer(duration) {
        setTimeout(function() {
            $gameview.find('.decorate').each(function(i, item) {
                var $t = $(this),
                    $img = $t.find('img'),
                    src = $img.attr('src'),
                    list = $img.data('src').split(',,');
                if (list.length > 1) {
                    if (src == list[0]) {
                        $img.attr('src', list[1])
                    } else {
                        $img.attr('src', list[0])
                    }
                }
            })
            timer();
        }, duration ? duration: 2000);
    }
})