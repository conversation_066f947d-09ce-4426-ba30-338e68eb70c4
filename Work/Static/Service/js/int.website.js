/**
 * Created by mohism on 2016/12/14.
 */
/*
 Name : 站点插件管理器
 Copyright (C) 2015 - 2017
 WebSite:	http://www.mohism.cn/
 Author:		<PERSON><PERSON><PERSON><PERSON>
 */
(function($){
    //站点插件
    CRMJS.Website = {
        Start : function() {
            if(CRMJS.Website.getCookie("menutoggle") == '1'){
                $.sidr('close', 'sidr');
                $('#main-menu').addClass('mini');
                $('.page-content').addClass('condensed');
                $('.scrollup').addClass('to-edge');
                $('.header-seperation').hide();
                $('.footer-widget').addClass('fotmini');
            }
        },
        stop : function (){
            console.log(2);
        },
        setCookie: function(name,value)
        {
            var Days = 30;
            var exp = new Date();
            exp.setTime(exp.getTime() + Days*24*60*60*1000);
            document.cookie = name + "="+ escape (value) + ";expires=" + exp.toGMTString();
        },
        getCookie : function (name)
        {
            var arr,reg=new RegExp("(^| )"+name+"=([^;]*)(;|$)");
            if(arr=document.cookie.match(reg))
                return unescape(arr[2]);
            else
                return null;
        }
    };


    $(document).ready(function() {
        CRMJS.Website.Start();

        $(document).on("click", "#layout-condensed-toggle",function(e) {
            if(CRMJS.Website.getCookie("menutoggle") == '1'){
                CRMJS.Website.setCookie("menutoggle", "0");
            }else{
                CRMJS.Website.setCookie("menutoggle", "1");
            }
        });
        //设置时间
        $('.form_datetimes').hunterTimePicker();
        //设置时间
        $('.form_datetime').datetimepicker({
            language:'zh',
            timepicker:false,
            format:'Y-m-d',
            formatDate:'Y-m-d'
        });
        //设置时间
        $('.form_datemonthtime').datetimepicker({
            language:'zh',
            timepicker:false,
            format:'Y-m',
            formatDate:'Y-m'
        });

    });
})(jQuery);