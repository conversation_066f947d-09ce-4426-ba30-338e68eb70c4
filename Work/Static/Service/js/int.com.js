/**
 * Created by Administrator on 2016/12/14.
 */
window.console = window.console || {};
console.log || (console.log = opera.postError);


/*
 * JDB 1.0
 * version: 1.0
 * http://www.baisui.la
 * Copyright 2016, 2018 mohism [ <EMAIL> ]
 *
 */

var CRMJS = CRMJS||{};
(function($){
    //兼容placeholder8
    CRMJS.Comint = {
        placeholderSupport : function() {
            return 'placeholder' in document.createElement('input');
        },
        placeholderStart : function() {
            if(!this.placeholderSupport()){
                $('[placeholder]').focus(function() {
                    var input = $(this);
                    if (input.val() == input.attr('placeholder')) {
                        input.val('');
                        input.removeClass('placeholder');
                    }
                }).blur(function() {
                    var input = $(this);
                    if (input.val() == '' || input.val() == input.attr('placeholder')) {
                        input.addClass('placeholder');
                        input.val(input.attr('placeholder'));
                    }
                }).blur();
            }
        },
        /**保留两位小数点**/
        TwoDecimal : function(x){
            var f_x = parseFloat(x);
            if (isNaN(f_x))
            {
                alert('function:changeTwoDecimal->parameter error');
                return false;
            }
            var f_x = Math.round(x*100)/100;
            var s_x = f_x.toString();
            var pos_decimal = s_x.indexOf('.');
            if (pos_decimal < 0)
            {
                pos_decimal = s_x.length;
                s_x += '.';
            }
            while (s_x.length <= pos_decimal + 2)
            {
                s_x += '0';
            }
            return s_x;
        },
        /**倒计时**/
        jumpDown : function (count,url) {
            window.setTimeout(function(){
                count--;
                if(count > 0) {
                    $('#timeCountdown').html(count);
                    CRMJS.Comint.jumpDown(count,url);
                } else {
                    location.href=url;
                }
            }, 1000);
        },
        /**倒计时**/
        DiyProgress : function () {
            var progress = 20;
            if($('input[name="age"]').is(':checked')){
                console.log(1);
                progress += 20;
            }
            if($('input[name="crowdtype[]"]').is(':checked')){
                progress += 20;
            }
            if($('input[name="marriage"]').is(':checked')){
                progress += 20;
            }
            if($('input[name="budget"]').is(':checked')){
                progress += 20;
            }
            console.log(progress);
            $(".Diy-Progress").find("em").css("width",progress+"%");
            $(".Diy-Progress").find(".progressval").html(progress+"%");
        }
    };
    //插件运行
    CRMJS.Start = {
        GoStart : function() {
            CRMJS.Comint.placeholderStart();
        }
    };

    $(document).ready(function() {
        CRMJS.Start.GoStart();
        $(".bakFromurl").click(function() {
            window.history.back();
        });

        $(".forWardurl").click(function() {
            window.history.go(+1);
        });
        $(".reloadurl").click(function() {
            window.location.reload();
        });
        //查看图片
        $(document).on("click", ".Opon-Img-View",function() {
            CRMJS.Tiplog.OpenLoadHtml('<img src="'+$(this).data("imgurl")+'">');
        });
        //删除数据记录
        $(document).on("click", ".btn-del-action",function(e) {
            e.preventDefault();
            var  acturl = $(this).data("url"),
                element = $(this).data("element"),
                errortip = $(this).data("errortip");

            CRMJS.Tiplog.FucWarningTip("消息提示","你确定删除此条记录么！",function(){
                $.ajax({
                    async: false,
                    contentType: "application/json",
                    dataType: "json",
                    type: "GET",
                    url: acturl,
                    data: "",
                    success: function(bakjson) {
                        if(bakjson.error == '1'){
                            errormotify(bakjson.errortip);
                        }else{
                            okmotify(bakjson.errortip);
                            $("#"+element).remove();
                            CRMJS.Tiplog.Close();
                        }
                    },
                    error: function() {
                        errormotify("机制错误");
                    }
                });
            },"确定删除","取消");
        });
        //确认信息操作
        $(document).on("click", ".btn-confirm-action",function(e) {
            e.preventDefault();
            var  acturl = $(this).data("url"),
                tiptitle = $(this).data("tiptitle"),
                tipname = $(this).data("tipname"),
                element = $(this).data("element"),
                errortip = $(this).data("errortip");


            (typeof(tiptitle)=="undefined")?tiptitle="你确定如此操作么?":tiptitle=tiptitle;
            (typeof(tipname)=="undefined")?tipname="消息提示":tipname=tipname;

            CRMJS.Tiplog.FucWarningTip(tipname,tiptitle,function(){
                $.ajax({
                    async: false,
                    contentType: "application/json",
                    dataType: "json",
                    type: "GET",
                    url: acturl,
                    data: "",
                    success: function(bakjson) {
                        if(bakjson.error == '1'){
                            errormotify(bakjson.errortip);
                        }else{
                            okmotify(bakjson.errortip);
                            window.location.reload();
                            CRMJS.Tiplog.Close();
                        }
                    },
                    error: function() {
                        errormotify("机制错误");
                    }
                });
            },"确定","取消");
        });
        //操作数据记录
        $(document).on("click", ".btn-send-action",function(e) {
            e.preventDefault();
            var  acturl = $(this).data("url");
            $.ajax({
                async: false,
                contentType: "application/json",
                dataType: "json",
                type: "GET",
                url: acturl,
                data: "",
                success: function(bakjson) {
                    if(bakjson.error == '1'){
                        errormotify(bakjson.errortip);
                    }else{
                        refreshpage(bakjson.errortip);
                    }
                },
                error: function() {
                    errormotify("机制错误");
                }
            });
        });
        //确认信息操作
        $(document).on("click", ".btn-form-action",function(e) {
            e.preventDefault();
            var  acturl = $(this).data("url"),
                tiptitle = $(this).data("tiptitle"),
                element = $(this).data("element")
            textname = $(this).data("textname"),
                placeholder = $(this).data("placeholder"),
                tipConter = '<input name="'+textname+'" class="form-control" type="text" placeholder="'+placeholder+'">';
            (typeof(tiptitle)=="undefined")?tiptitle="你确定如此操作么?":tiptitle=tiptitle;
            CRMJS.Tiplog.FucFormTip(tiptitle,acturl,tipConter,"确定","取消");
        });

        //操作数据记录
        $(document).on("click", ".btn-state-send",function(e) {
            e.preventDefault();
            var  acturl = $(this).data("url"),thisobg=$(this);
            $.ajax({
                async: false,
                contentType: "application/json",
                dataType: "json",
                type: "GET",
                url: acturl,
                data: "",
                success: function(bakjson) {
                    if(bakjson.error == '1'){
                        errormotify(bakjson.errortip);
                    }else{
                        if(bakjson.state == '1'){
                            thisobg.children("span").removeClass("glyphicon-remove-circle").removeClass("text-danger");
                            thisobg.children("span").addClass("glyphicon-ok-circle").addClass("text-primary");
                        }else{
                            thisobg.children("span").removeClass("glyphicon-ok-circle").removeClass("text-primary");
                            thisobg.children("span").addClass("glyphicon-remove-circle").addClass("text-danger");
                        }
                    }
                },
                error: function() {
                    errormotify("机制错误");
                }
            });
        });

        //操作数据记录
        $(document).on("click", ".delThis",function(e) {
            e.preventDefault();
            $(this).parents("li").remove();
        });

        if($(".chosen-select").length > 0 ){
            //选择器
            $(".chosen-select").chosen({
                no_results_text: "未检索到任何内容",
                placeholder_text : "可检索中文信息",
                search_contains: true
            });
        }


        //新增一行
        $(document).on("click", ".copyTableToNext",function() {
            var TdHtml = $(this).parents("tr.tablelist").html();
            var NewHtml = "<tr>"+TdHtml.replace(/(.*)(copyTableToNext)(.*)(\[)(\+)/i, "$1delTableTd$3$4-")+"</tr>";
            $(this).parents("tbody").append(NewHtml);
        });
        $(document).on("click", ".delTableTd",function() {
            $(this).parents("tr").remove();
        });

        //数据展开
        $(document).on("click", ".OneMenuClick",function(e) {
            e.preventDefault();
            if($(this).hasClass("glyphicon-eye-open")){
                $(this).addClass("glyphicon-eye-close").removeClass("glyphicon-eye-open");
                $(this).parents(".OneMenu").nextUntil(".OneMenu").show();
            }else{
                $(this).addClass("glyphicon-eye-open").removeClass("glyphicon-eye-close");
                $(this).parents(".OneMenu").nextUntil(".OneMenu").hide();
            }
        });
        //数据展开
        $(document).on("click", ".TwoMenuClick",function(e) {
            e.preventDefault();
            if($(this).hasClass("glyphicon-eye-open")){
                $(this).addClass("glyphicon-eye-close").removeClass("glyphicon-eye-open");
                $(this).parents(".TwoMenu").nextUntil(".TwoMenu").show();
            }else{
                $(this).addClass("glyphicon-eye-open").removeClass("glyphicon-eye-close");
                $(this).parents(".TwoMenu").nextUntil(".TwoMenu").hide();
            }
        });

        $("#misclassType li").click(function() {
            $("#misclass").val($(this).data("class"));
            $(this).addClass("cur").siblings().removeClass("cur");
            $("div#sendnote").children("div").eq($(this).index()).show().siblings().hide();
        });

        //全选按钮
        $("#Choice_All").change(function(){
            if($(this).prop("checked")){
                $("input[name='tab_list[]']").prop("checked",true);
            }else{
                $("input[name='tab_list[]']").prop("checked",false);
            }
        });

        //全选按钮
        $("#Choice_List").click(function(){
            if(!$(this).hasClass("cur")){
                $(this).addClass("cur").text('取消批量占位');
                $('.date-scrol').addClass('mana-by');
            }else{
                $(this).removeClass("cur").text('批量占位');
                $('.date-scrol').removeClass('mana-by');
                $("input[name='bookTime[]']").prop("checked",false);
                $("#OrderNums").text('0');
            }
        });

        $("input[name='bookTime[]']").change(function(){
            var itemnums = $("input[name='bookTime[]']:checked").length;
            $("#OrderNums").text(itemnums);
        });

        $(".form_timetable").hunterTimePicker();


        //工单里边的编辑器
        if($("#qeditor_body,#qeditor_body1,#qeditor_body2").length > 0 ){
            //编辑器
            $("#qeditor_body,#qeditor_body1,#qeditor_body2").editable({
                inlineMode: false,
                alwaysBlank: true,
                language: "zh_cn",
                height:"350px",
                imageUploadURL: '/Images/Imgupload',//上传到本地服务器
                imageUploadParams: {id: "edit"},
                //imageDeleteURL: 'lib/delete_image.php',//删除图片
                imagesLoadURL: '/Images/Manage'//管理图片
            });
        }


        $(".CompanyAjax").select2({
            ajax: {
                type:'GET',
                url: "/api/getCompany",
                dataType: 'json',
                delay: 250,
                data: function (params) {
                    return {
                        keyword: params.term=='1'?"":params.term,
                        member_id:"manage",
                        page: params.page
                    };
                },
                processResults: function (data, params) {
                    params.page = params.page || 1;
                    var itemList = [];//当数据对象不是{id:0,text:'ANTS'}这种形式的时候，可以使用类似此方法创建新的数组对象
                    var arr = data.result.list;
                    for(item in arr){
                        itemList.push({id: arr[item].company_id, text:arr[item].company_cnname})
                    }
                    return {
                        results: itemList,
                        pagination: {
                            more: (params.page * 30) < data.total_count
                        }
                    };
                },
                cache: true
            },
            language: "zh-CN",
            tags: false,//允许手动添加
            allowClear: true,//允许清空
            width:'100%',
            escapeMarkup: function (markup) { return markup; }, // 自定义格式化防止xss注入
            //minimumInputLength: 1,//最少输入多少个字符后开始查询
            formatResult: function formatRepo(repo){return repo.text;}, // 函数用来渲染结果
            formatSelection: function formatRepoSelection(repo){return repo.text;} // 函数用于呈现当前的选择
        });

        $(".SchoolAjax").select2({
            ajax: {
                type:'GET',
                url: "/Api/getSchool",
                dataType: 'json',
                delay: 250,
                data: function (params) {
                    return {
                        company_id:$("#company_id").val(),
                        keyword: params.term=='1'?"":params.term,
                        member_id:"manage",
                        page: params.page
                    };
                },
                processResults: function (data, params) {
                    // if(data.result.error == '1'){
                    //     errormotify(data.result.errortip);
                    // }else {
                        params.page = params.page || 1;
                        var itemList = [];//当数据对象不是{id:0,text:'ANTS'}这种形式的时候，可以使用类似此方法创建新的数组对象
                        var arr = data.result.list;
                        for (item in arr) {
                            itemList.push({id: arr[item].school_id, text: arr[item].school_cnname})
                        }
                        return {
                            results: itemList,
                            pagination: {
                                more: (params.page * 30) < data.total_count
                            }
                        };
                    // }
                },
                cache: true
            },
            language: "zh-CN",
            tags: false,//允许手动添加
            allowClear: true,//允许清空
            width:'100%',
            escapeMarkup: function (markup) { return markup; }, // 自定义格式化防止xss注入
            //minimumInputLength: 1,//最少输入多少个字符后开始查询
            formatResult: function formatRepo(repo){return repo.text;}, // 函数用来渲染结果
            formatSelection: function formatRepoSelection(repo){return repo.text;} // 函数用于呈现当前的选择
        });

    });

})(jQuery);

$(function(){
    $('#monthpicker').monthpicker({
        years: [2018, 2017, 2016, 2015, 2014],
        topOffset: 2
    })
});


//错误提示
function dangerFromTip(tip){
    var Tiphtm = '<div class="alert alert-danger alert-dismissible" role="alert">'
            + '<button type="button" class="close" data-dismiss="alert"><span aria-hidden="true">×</span><span class="sr-only">Close</span></button>'
            + '<span class="glyphicon glyphicon-remove-circle f16 vam mr6"></span>'+ tip +'</div>';
    $(Tiphtm).prependTo("#Form-Box-Operating");
    setTimeout(function(){ $("#Form-Box-Operating").children(".alert-danger").hide(); },3000);
}

//警告提示
function warningFromTip(tip){
    var Tiphtm = '<div class="alert alert-warning alert-dismissible" role="alert">'
    + '<button type="button" class="close" data-dismiss="alert"><span aria-hidden="true">×</span><span class="sr-only">Close</span></button>'
    + '<span class="glyphicon glyphicon-exclamation-sign f16 vam mr6"></span>'+ tip +'</div>';
    $(Tiphtm).prependTo("#Form-Box-Operating");
    setTimeout(function(){ $("#Form-Box-Operating").children(".alert-warning").hide(); },3000);
}
//正确提示
function successFromTip(tip){
    var Tiphtm = '<div class="alert alert-success alert-dismissible" role="alert">'
        + '<button type="button" class="close" data-dismiss="alert"><span aria-hidden="true">×</span><span class="sr-only">Close</span></button>'
        + '<span class="glyphicon glyphicon-ok-circle f16 vam mr6"></span>'+ tip +'</div>';
    $(Tiphtm).prependTo("#Form-Box-Operating");
    setTimeout(function(){ $("#Form-Box-Operating").children(".alert-success").hide(); },3000);
}

//错误提示
function errormotify(tip){
    $('<div class="errormotify"><div class="motify-inner"><span></span>'+tip+'</div></div>').appendTo("body");
    setTimeout(function(){ $(".errormotify").hide(); },3000);
}

//正确提示
function okmotify(tip){
    $('<div class="okmotify"><div class="motify-inner"><span></span>'+tip+'</div></div>').appendTo("body");
    setTimeout(function(){ $(".okmotify").hide(); },3000);
}

function refreshpage(tip){
    $('<div class="okmotify"><div class="motify-inner"><span></span>'+tip+'</div></div>').appendTo("body");
    setTimeout(function(){ $(".okmotify").hide(); },3000);
    window.location.reload();//刷新当前页面.
}