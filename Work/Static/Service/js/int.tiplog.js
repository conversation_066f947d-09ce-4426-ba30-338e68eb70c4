/**
 * Created by Mohism on 2016/12/15.
 */
/*
 * Tiplog 1.0
 * version: 1.0
 * http://www.hcircle.cn
 * Copyright 2016, 2018 mohism [ <EMAIL> ]
 *
 */
(function($){
    //插件运行
    CRMJS.Tiplog = {
        OpenOkTip : function(tipTitel,tipConter,okurl,okbuttxt,closebuttxt) {
            $('<div class="Tiplog">'
            +'<div class="Tiplog-Curtain"></div>'
            +'<div class="Tiplog-main">'
            +'<div class="Tiplog-content">'
            +'<div class="ok-ico">'

            +'</div>'
            +'<div class="tip-text">'
            +'<p class="tip-title">'
            +tipTitel
            +'</p>'
            +'<p class="tip-txt">'
            +tipConter
            +'</p>'
            +'</div></div>'
            +'<div class="Tiplog-footer">'
            +'<a class="but but-red Tiplog-ok-but" href="'+okurl+'">'+okbuttxt+'</a>'
            +'<a class="but but-black Tiplog-Close-but">'+closebuttxt+'</a>'
            +'</div></div></div>').appendTo("body");
        },
        OpenErrorTip : function(tipTitel,tipConter,url,okbuttxt,closebuttxt) {
            $('<div class="Tiplog">'
            +'<div class="Tiplog-Curtain"></div>'
            +'<div class="Tiplog-main">'
            +'<div class="Tiplog-content">'
            +'<div class="error-ico">'

            +'</div>'
            +'<div class="tip-text">'
            +'<p class="tip-title">'
            +tipTitel
            +'</p>'
            +'<p class="tip-txt">'
            +tipConter
            +'</p>'
            +'</div></div>'
            +'<div class="Tiplog-footer">'
            +'<a class="but but-red Tiplog-ok-but" href="'+url+'">'+okbuttxt+'</a>'
            +'<a class="but but-black Tiplog-Close-but">'+closebuttxt+'</a>'
            +'</div></div></div>').appendTo("body");
        },
        OpenLoadTip : function(tipUrl) {
            $('<div class="Tiplog">'
            +'<div class="Tiplog-Curtain"></div>'
            +'<div class="Tiplog-main">'
            +'<div class="Tiplog-content"><div class="tip-text LogView"></div></div></div></div>').appendTo("body");
            $(".LogView").load(tipUrl,function(){
                $('.form_datetime').datetimepicker({
                    language:'zh',
                    timepicker:false,
                    format:'Y-m-d',
                    formatDate:'Y-m-d'
                });
            });
        },
        OpenLoadHtml : function(tipHtml) {
            $('<div class="Tiplog">'
            +'<div class="Tiplog-Curtain"></div>'
            +'<div class="Tiplog-main">'
            +'<div class="Tiplog-content"><div class="tip-text LogView">'+tipHtml+'</div></div></div></div>').appendTo("body");
        },
        UrlOkTip : function(tipTitel,tipConter,okurl,okbuttxt,closeurl,closebuttxt) {
            $('<div class="Tiplog">'
            +'<div class="Tiplog-Curtain"></div>'
            +'<div class="Tiplog-main">'
            +'<div class="Tiplog-content">'
            +'<div class="ok-ico">'

            +'</div>'
            +'<div class="tip-text">'
            +'<p class="tip-title">'
            +tipTitel
            +'</p>'
            +'<p class="tip-txt">'
            +tipConter
            +'</p>'
            +'</div></div>'
            +'<div class="Tiplog-footer">'
            +'<a class="but but-red Tiplog-ok-but" href="'+okurl+'">'+okbuttxt+'</a>'
            +'<a class="but but-black" href="'+closeurl+'">'+closebuttxt+'</a>'
            +'</div></div></div>').appendTo("body");
        },
        OpenViewTip : function(tipTitle,tipUrl) {
            $('<div class="Tiplog">'
            +'<div class="Tiplog-Curtain"></div>'
            +'<div class="Tiplog-main"><div class="Tiplog-top">'
            +tipTitle
            +'<span class="Tiplog-Close-but"></span></div>'
            +'<div class="Tiplog-content"></div></div></div>').appendTo("body");
            $(".Tiplog-content").load(tipUrl);
        },
        FucOpenViewTip : function(tipTitle,tipUrl,okfuc,okbuttxt,closebuttxt) {
            $('<div class="Tiplog">' +
            '<div class="Tiplog-Curtain"></div>' +
            '<div class="Tiplog-main">' +
            '<div class="Tiplog-top">'+tipTitle+'<span class="Tiplog-Close-but icon-remove"></span></div>' +
            '<div class="Tiplog-content"></div>' +
            '<div class="Tiplog-footer"><a class="but but-red Tiplog-ok-but">'+okbuttxt+'</a>' +
            '<a class="but but-black Tiplog-Close-but">'+closebuttxt+'</a></div>' +
            '</div></div>').appendTo("body");
            $(".Tiplog-content").load(tipUrl);
            $(document).on('click', '.Tiplog-ok-but',function(e) {
                okfuc();
            });
        },
        FucWarningTip : function(tipTitel,tipConter,okfuc,okbuttxt,closebuttxt) {
            $('<div class="Tiplog">'
            +'<div class="Tiplog-Curtain"></div>'
            +'<div class="Tiplog-main">'
            +'<div class="Tiplog-content">'
            +'<span class="glyphicon glyphicon-question-sign warning"></span>'
            +'<div class="tip-text">'
            +'<p class="tip-title">'
            +tipTitel
            +'</p>'
            +'<p class="tip-txt">'
            +tipConter
            +'</p>'
            +'</div></div>'
            +'<div class="Tiplog-footer">'
            +'<a class="but but-red Tiplog-ok-but">'+okbuttxt+'</a>'
            +'<a class="but but-black Tiplog-Close-but">'+closebuttxt+'</a>'
            +'</div></div></div>').appendTo("body");
            $(document).on('click', '.Tiplog-ok-but',function(e) {
                okfuc();
            });
        },
        FucFormTip : function(tipTitel,tipUrl,tipConter,okbuttxt,closebuttxt) {
            $('<div class="Tiplog">'
            +'<div class="Tiplog-Curtain"></div>'
            +'<div class="Tiplog-main"><form action="'
            +tipUrl+'" method="post" enctype="application/x-www-form-urlencoded" class="AjaxForm">'
            +'<div class="Tiplog-content">'
            +'<span class="glyphicon glyphicon-question-sign warning"></span>'
            +'<div class="tip-text">'
            +'<p class="tip-title">'
            +tipTitel
            +'</p>'
            +'<div class="tip-txt">'
            +tipConter
            +'</div>'
            +'</div></div>'
            +'<div class="Tiplog-footer">'
            +'<button type="submit" class="but but-red Tiplog-ok-but">'+okbuttxt+'</button>'
            +'<a class="but but-black Tiplog-Close-but">'+closebuttxt+'</a>'
            +'</div></form></div></div>').appendTo("body");
            $(".AjaxForm").submit(function(){
                var isSubmit = true;
                $(this).find("[reg],[url]:not([reg])").each(function(){
                    if($(this).attr("reg") == undefined){
                        if(!ajax_validate($(this))){
                            isSubmit = false;
                        }
                    }else{
                        if(!validate($(this))){
                            isSubmit = false;
                        }
                    }
                });

                if(isSubmit){
                    $(this).ajaxSubmit(options);
                    return false;
                }else{
                    return false;
                }
            });
        },
        Close: function() {
            $(".Tiplog").remove();
        }
    };
    $(document).ready(function() {
        //关闭弹出窗
        $(document).on('click', '.Tiplog-Close-but',function(e) {
            e.preventDefault();
            $('.Tiplog-ok-but').unbind('click');
            CRMJS.Tiplog.Close();
        });

        //影藏
        $(document).on('click', '.Tiplog-Curtain',function(e) {
            e.preventDefault();
            $('.Tiplog-ok-but').unbind('click');
            CRMJS.Tiplog.Close();
        });
    });

})(jQuery);
