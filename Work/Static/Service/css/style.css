/* CSS Initializing */
@charset "utf-8";
pre{white-space: normal;}
body,dl,dd,ul,ol,form{margin:0px;padding:0px;}
body{font-size:12px;font-family:"Noto Sans CJK SC", "Source Han Sans CN", 'Hiragino Sans GB', 'Microsoft YaHei', '\5b8b\4f53', sans-serif;color: #20293b;line-height:23px;background:#354052;background-size:100%;height: 100%;width: 100%;-webkit-font-smoothing: antialiased;min-width:1300px;}
img{border:0; vertical-align:middle;}
ul,li{list-style:none;list-style-type: none}
h1, h2, h3, h4, h5, h6 { } /* 字体加粗与否，视页面标题更改 */
h1 a, h2 a, h3 a, h4 a, h5 a, h6 a{ font-weight:bold;} /* 字体加粗与否，视页面标题更改 */
i,b{ font-size:12px; font-weight:normal;font-style: normal;}
input,button, textarea, select{ vertical-align: middle;font-family: "Helvetica Neue",Helvetica,Arial,sans-serif; }
input{ }
select{}
button { cursor: pointer }
em, cite { font-style: normal; }
a{color:#333e45;text-decoration:none;}
a:hover{color: #4079c8;text-decoration: none; list-style:none;}
.txtlf{ text-align:left;}
.txtrg{ text-align:right;}
.td-un{text-decoration: underline; }
.fl{ float:left;}
.fr{ float:right;}
.fix:after {content:'.';display:block;clear:both;visibility:hidden;height:0px;}/*清除浮动的一种方法，将此class名写在内部有浮动的标签中，便可清除子字元素的浮动，使用非常方便*/
.fix {zoom:1;}
.clearfixed:after { content: "."; clear: both; display: block; height: 0; overflow: hidden }
.clearfixed { zoom: 1 } /* 增加清除浮动样式时，要注意当前元素是否为块结构，如果不是，要添加属性 display: block */
.clear { display: block!important; float: none!important; clear: both; width: auto!important; height: 0;margin: 0 auto!important; padding: 0!important; font-size: 0; line-height: 0 }
.brank{ line-height:8px; clear:both; height:8px;margin: 0 auto!important; padding: 0!important; font-size: 0; float: none!important;}
.none{ display:none;}
table{border-collapse:collapse;}
.vat,.vat_ > *,.vat_ > *,.vat- * {vertical-align: top;}
.vam,.vam_ > *,.vim_ > *,.vim- *,.vim-center > *,
.vim-center:after,.vam_ > *,
.vam- *,.table-middle,.table-middle_ > *,.vim-center_ > * > *,.vim-center_ > *:after {vertical-align: middle;}
.vab,.vab_ > *,.vab_ > *,.vab- * {vertical-align: bottom;}
.vatb,.vatb_ > * {vertical-align: text-bottom;}
.tl,.tl_ > * {text-align: left;}
.tc,.tc_ > * {text-align: center;}
.tr,.tr_ > * {text-align: right;}
.ti2e,.ti2e_ > * { text-indent: 2em;}
.ti0,.ti0_ > * {text-indent: 0;}
.ti_ {text-indent: -9999em;}
/**align  End**/
/**background-color Start**/
.bgn {background: none;}
.bg-0 {background-color: #000000;}
.bg-3 {background-color: #333333;}
.bg-6 {background-color: #666666;}
.bg-9 {background-color: #999999;}
.bg-c {background-color: #cccccc;}
.bg-e {background-color: #eeeeee;}
.bg-f {background-color: #ffffff !important;}
.bg-orange {background-color: #ff6600;}
.bg-orange-a {background-color: #ff9900;}
.bg-green {background-color: #00cc00;}
.bg-green-a {background-color: #98d198;}
.bg-red {background-color: #ff0000;}
.bg-red-a {background-color: #feedd4;}
.bg-cyan {background-color: #00c6c6;}
.bg-blue {background-color: #000099;}
.bg-blue-z {background-color: #4c5a65;}
.bg-gray {background-color: #f2f2f2;}
.bg-gray-a {background-color: #fbfbfb;}
/**background-color  End**/
/**blank Start**/
.blank,.blank0,.blank1,.blank2,.blank3,.blank4,.blank5,.blank6,.blank7,.blank8,.blank9,.blank10,.blank12,.blank14,.blank16,.blank18,.blank20,.blank25,.blank30,.blank35,.blank40,.blank45,.blank50,.xline1,.x-line-1,.x-line2 { display: block;font-size: 0;line-height: 0;overflow: hidden;clear: both;}
.blank0 {height: 0px;}
.blank1 {height: 1px;}
.blank2 {height: 2px;}
.blank3 {height: 3px;}
.blank4 {height: 4px;}
.blank5 {height: 5px;}
.blank6 {height: 6px;}
.blank7 {height: 7px;}
.blank8 {height: 8px;}
.blank9 {height: 9px;}
.blank10 {height: 10px;}
.blank12 {height: 12px;}
.blank14 {height: 14px;}
.blank16 {height: 16px;}
.blank18 {height: 18px;}
.blank20 {height: 20px;}
.blank25 {height: 25px;}
.blank30 {height: 30px;}
.blank35 {height: 35px;}
.blank40 {height: 40px;}
.blank45 {height: 45px;}
.blank50 {height: 50px;}
/**blank  End**/
/**clearfix Start**/
/**
 * http://nicolasgallagher.com/micro-clearfix-hack/
 * For modern browsers
 * 1. The space content is one way to avoid an Opera bug when the
 *    contenteditable attribute is included anywhere else in the document.
 *    Otherwise it causes space to appear at the top and bottom of elements
 *    that are clearfixed.
 * 2. The use of `table` rather than `block` is only necessary if using
 *    `:before` to contain the top-margins of child elements.
 * 3. Reference:http://nicolasgallagher.com/micro-clearfix-hack/
 */
.clfix:before,.clfix:after,.lfix_:before,.lfix_:after {content: " "; /* 1 */ display: table;/* 2 */}
.clfix:after,.lfix_:after {clear: both;}
/**
 * For IE 6/7 only
 * Include this rule to trigger hasLayout and contain floats.
 */
.clfix,.lfix_ {*zoom: 1;}
/*normal way to clearfix*/
.clearfix:after {clear: both;content: "\200B";display: block;height: 0;}
.clearfix {*zoom: 1;}
.cb { clear: both;}
/**clearfix  End**/
/**color Start**/
.c-0 {color: #000000;}
.c-3 {color: #333333;}
.c-6 {color: #666666;}
.c-9 {color: #999999;}
.c-8 {color: #888;}
.c-c {color: #cccccc;}
.c-e {color: #eeeeee;}
.c-f {color: #ffffff;}
.c-orange {color: #f89257;}
.c-orange-a {color: #ff9900;}
.c-green {color: #89aa63;}
.c-green-a {color: #a8b861;}
.c-red {color: #ff0000;}
.c-red-a {color: #feedd4;}
.c-cyan {color: #00c6c6;}
.c-blue {color: #0993cd;}
.c-blue-z {color: #4c5a65;}
.c-gray {color: #a2a2a1;}
.c-gray-a {color: #999;}
.c-yellow{color:#af8e58}
.c-debug{color:#b0bac4;}
/**color  End**/
/**cursor Start**/
.cp {cursor: pointer;}
.cd {cursor: default;}
/**cursor  End**/
/**debug Start**/
.debug-gray {border: 1px solid #e5e5e5;}
.debug-green {border: 1px solid #00ff00;}
.debug-blue {border: 1px solid #0000ff;}
.debug-yellow {border: 1px solid #eed8a0;}
/**debug  End**/
/**font Start**/
.b { font-weight: bold;}
.n {font-weight: normal;font-style: normal;}
.fbd-font {font: 62.5%/1 simsun, "\5b8b\4f53", Microsoft YaHei, Arial, "sans-serif";}
.fbd-size {font-size: 62.5%;}
.fbd-lh,.ls0-u,.ls0_ > * {line-height: 1;}
.fbd-family,.ls0-u,.ls0_ > * {font-family: simsun, "\5b8b\4f53", Microsoft YaHei, Arial, "sans-serif";}
.f12 {font-size: 12px;}
.f13 {font-size: 13px;}
.f14 {font-size: 14px;}
.f15 {font-size: 15px;}
.f16 {font-size: 16px;}
.f17 {font-size: 17px;}
.f18 {font-size: 18px;}
.f19 {font-size: 19px;}
.f20 {font-size: 20px;}
.f21 {font-size: 21px;}
.f22 {font-size: 22px;}
.f23 {font-size: 23px;}
.f24 {font-size: 24px;}
.f25 {font-size: 25px;}
.f26 {font-size: 26px;}
.f27 {font-size: 27px;}
.f28 {font-size: 28px;}
.f29 {font-size: 29px;}
.f30 {font-size: 30px;}
.f35 {font-size: 35px;}
.f40 {font-size: 40px;}
/**font  End**/
/**form Start**/
.frm-none { border: none;background: none;}
/**form  End**/
/**height Start**/
.h-p1,.h-p1_ > * {height: 10%;}
.h-p2,.h-p2_ > * {height: 20%;}
.h-p3,.h-p3_ > * {height: 30%;}
.h-p4,.h-p4_ > * {height: 40%;}
.h-p5,.h-p5_ > * {height: 50%;}
.h-p6,.h-p6_ > * {height: 60%;}
.h-p7,.h-p7_ > * {height: 70%;}
.h-p8,.h-p8_ > * {height: 80%;}
.h-p9,.h-p9_ > * {height: 90%;}
.h-p10,.h-p10_ > * {height: 100%;}
.h10,.h10_ > * {height: 10px;}
.h11,.h11_ > * {height: 11px;}
.h12,.h12_ > * {height: 12px;}
.h13,.h13_ > * {height: 13px;}
.h14,.h14_ > * {height: 14px;}
.h15,.h15_ > * {height: 15px;}
.h16,.h16_ > * {height: 16px;}
.h17,.h17_ > * {height: 17px;}
.h18,.h18_ > * {height: 18px;}
.h19,.h19_ > * {height: 19px;}
.h20,.h20_ > * {height: 20px;}
.h21,.h21_ > * {height: 21px;}
.h22,.h22_ > * {height: 22px;}
.h23,.h23_ > * {height: 23px;}
.h24,.h24_ > * {height: 24px;}
.h25,.h25_ > * {height: 25px;}
.h26,.h26_ > * {height: 26px;}
.h27,.h27_ > * {height: 27px;}
.h28,.h28_ > * {height: 28px;}
.h29,.h29_ > * {height: 29px;}
.h30,.h30_ > * {height: 30px;}
.h31,.h31_ > * {height: 31px;}
.h32,.h32_ > * {height: 32px;}
.h33,.h33_ > * {height: 33px;}
.h34,.h34_ > * {height: 34px;}
.h35,.h35_ > * {height: 35px;}
.h36,.h36_ > * {height: 36px;}
.h37,.h37_ > * {height: 37px;}
.h38,.h38_ > * {height: 38px;}
.h39,.h39_ > * {height: 39px;}
.h40,.h40_ > * {height: 40px;}
.h48,.h48_ > * {height: 48px;}
.h50,.h50_ > * {height: 50px;}
.lh10,.lh10_ > * {line-height: 10px;}
.lh11,.lh11_ > * {line-height: 11px;}
.lh12,.lh12_ > * {line-height: 12px;}
.lh13,.lh13_ > * {line-height: 13px;}
.lh14,.lh14_ > * {line-height: 14px;}
.lh15,.lh15_ > * {line-height: 15px;}
.lh16,.lh16_ > * {line-height: 16px;}
.lh17,.lh17_ > * {line-height: 17px;}
.lh18,.lh18_ > * {line-height: 18px;}
.lh19,.lh19_ > * {line-height: 19px;}
.lh20,.lh20_ > * {line-height: 20px;}
.lh21,.lh21_ > * {line-height: 21px;}
.lh22,.lh22_ > * {line-height: 22px;}
.lh23,.lh23_ > * {line-height: 23px;}
.lh24,.lh24_ > * {line-height: 24px;}
.lh25,.lh25_ > * {line-height: 25px;}
.lh26,.lh26_ > * {line-height: 26px;}
.lh27,.lh27_ > * {line-height: 27px;}
.lh28,.lh28_ > * {line-height: 28px;}
.lh29,.lh29_ > * {line-height: 29px;}
.lh30,.lh30_ > * {line-height: 30px;}
.lh31,.lh31_ > * {line-height: 31px;}
.lh32,.lh32_ > * {line-height: 32px;}
.lh33,.lh33_ > * {line-height: 33px;}
.lh34,.lh34_ > * {line-height: 34px;}
.lh35,.lh35_ > * {line-height: 35px;}
.lh36,.lh36_ > * {line-height: 36px;}
.lh37,.lh37_ > * {line-height: 37px;}
.lh38,.lh38_ > * {line-height: 38px;}
.lh39,.lh39_ > * {line-height: 39px;}
.lh40,.lh40_ > * {line-height: 40px;}
.lh48,.lh48_ > * {line-height: 48px;}
.lh50,.lh50_ > * {line-height: 50px;}
.hlh10,.hlh10_ > * {height: 10px;line-height: 10px;}
.hlh11,.hlh11_ > * {height: 11px;line-height: 11px;}
.hlh12,.hlh12_ > * {height: 12px;line-height: 12px;}
.hlh13,.hlh13_ > * {height: 13px;line-height: 13px;}
.hlh14,.hlh14_ > * {height: 14px;line-height: 14px;}
.hlh15,.hlh15_ > * {height: 15px;line-height: 15px;}
.hlh16,.hlh16_ > * {height: 16px;line-height: 16px;}
.hlh17,.hlh17_ > * {height: 17px;line-height: 17px;}
.hlh18,.hlh18_ > * {height: 18px;line-height: 18px;}
.hlh19,.hlh19_ > * {height: 19px;line-height: 19px;}
.hlh20,.hlh20_ > * {height: 20px;line-height: 20px;}
.hlh21,.hlh21_ > * {height: 21px;line-height: 21px;}
.hlh22,.hlh22_ > * {height: 22px;line-height: 22px;}
.hlh23,.hlh23_ > * {height: 23px;line-height: 23px;}
.hlh24,.hlh24_ > * {height: 24px;line-height: 24px;}
.hlh25,.hlh25_ > * {height: 25px;line-height: 25px;}
.hlh26,.hlh26_ > * {height: 26px;line-height: 26px;}
.hlh27,.hlh27_ > * {height: 27px;line-height: 27px;}
.hlh28,.hlh28_ > * {height: 28px;line-height: 28px;}
.hlh29,.hlh29_ > * {height: 29px;line-height: 29px;}
.hlh30,.hlh30_ > * {height: 30px;line-height: 30px;}
.hlh31,.hlh31_ > * {height: 31px;line-height: 31px;}
.hlh32,.hlh32_ > * {height: 32px;line-height: 32px;}
.hlh33,.hlh33_ > * {height: 33px;line-height: 33px;}
.hlh34,.hlh34_ > * {height: 34px;line-height: 34px;}
.hlh35,.hlh35_ > * {height: 35px;line-height: 35px;}
.hlh36,.hlh36_ > * {height: 36px;line-height: 36px;}
.hlh37,.hlh37_ > * {height: 37px;line-height: 37px;}
.hlh38,.hlh38_ > * {height: 38px;line-height: 38px;}
.hlh39,.hlh39_ > * {height: 39px;line-height: 39px;}
.hlh40,.hlh40_ > * {height: 40px;line-height: 40px;}
.hlh48,.hlh48_ > * {height: 48px;line-height: 48px;}
.hlh50,.hlh50_ > * {height: 50px;line-height: 50px;}
.max-h100 {max-height: 100%;}
.min-h100 {min-height: 100%;}
/**height  End**/
/**layout Start**/
.l,.l_ > * {float: left;}
.r,.r_ > * {float: right;}
.abs,.abs_ > *,.layt-long-tip,.layt-square-tip,.m-sex-select > .item[data-active=true]:after,.J_number_spinner > * {position: absolute;}
.rel,.rel_ > *,.m-sex-select > .item {position: relative;}
.fixed {position: fixed;}
.dtb,.eq-width_ {display: table;}
.dtbr {display: table-row;}
.dtbc,.dtbc_ > *,.table-middle,.table-middle_ > *,.cell,.eq-width_ > * {display: table-cell;*display: inline-block; *zoom: 1;}
.db,.db_ > * {display: block;}
.dib,.dib_ > * {display:inline-block;}
.dn,[hidden] {display: none;}
.z,.z_ > *,.z- * {zoom: 1;}
.trbl0,.abs-center {top: 0;right: 0;bottom: 0;left: 0;}
.lr0,.abs-center-x {left: 0;right: 0;}
.tb0,.abs-center-y {top: 0;bottom: 0;}
.t0 {top: 0;}
.r0 {right: 0;}
.b0 {bottom: 0;}
.l0 {left: 0;}
.t5 {top: 50%;}
.r5 {right: 50%;}
.b5 {bottom: 50%;}
.l5 {left: 50%;}
.t100 {top: 100%;}
.r100 {right: 100%;}
.b100 {bottom: 100%;}
.l100 {left: 100%;}
.z1 {z-index: 1;}
.z2 {z-index: 2;}
.z3 {z-index: 3;}
/**layout  End**/
/**margin Start**/
.auto {margin-left: auto; margin-right: auto;}
.m0 { margin: 0;}
.m0a {margin: 0 auto;}
.m0,.m0_ > * {margin: 0px;}
.m1,.m1_ > * {margin: 1px;}
.m2,.m2_ > * { margin: 2px;}
.m3,.m3_ > * { margin: 3px;}
.m4,.m4_ > * {margin: 4px;}
.m5,.m5_ > * {margin: 5px;}
.m6,.m6_ > * {margin: 6px;}
.m7,.m7_ > * { margin: 7px;}
.m8,.m8_ > * {margin: 8px;}
.m9,.m9_ > * {margin: 9px;}
.m10,.m10_ > * {margin: 10px;}
.m12,.m12_ > * {margin: 12px;}
.m14,.m14_ > * {margin: 14px;}
.m16,.m16_ > * {margin: 16px;}
.m18,.m18_ > * {margin: 18px;}
.m20,.m20_ > * {margin: 20px;}
.m25,.m25_ > * {margin: 25px;}
.m30,.m30_ > * {margin: 30px;}
.m35,.m35_ > * {margin: 35px;}
.m40,.m40_ > * {margin: 40px;}
.m45,.m45_ > * {margin: 45px;}
.m50,.m50_ > * {margin: 50px;}
.mt0,.mt0_ > * {margin-top: 0px;}
.mt1,.mt1_ > * {margin-top: 1px;}
.mt2,.mt2_ > * {margin-top: 2px;}
.mt3,.mt3_ > * {margin-top: 3px;}
.mt4,.mt4_ > * {margin-top: 4px;}
.mt5,.mt5_ > * {margin-top: 5px;}
.mt6,.mt6_ > * {margin-top: 6px;}
.mt7,.mt7_ > * {margin-top: 7px;}
.mt8,.mt8_ > * {margin-top: 8px;}
.mt9,.mt9_ > * {margin-top: 9px;}
.mt10,.mt10_ > * {margin-top: 10px;}
.mt12,.mt12_ > * {margin-top: 12px;}
.mt14,.mt14_ > * {margin-top: 14px;}
.mt16,.mt16_ > * {margin-top: 16px;}
.mt18,.mt18_ > * {margin-top: 18px;}
.mt20,.mt20_ > * {margin-top: 20px;}
.mt25,.mt25_ > * {margin-top: 25px;}
.mt30,.mt30_ > * {margin-top: 30px;}
.mt35,.mt35_ > * {margin-top: 35px;}
.mt40,.mt40_ > * {margin-top: 40px;}
.mt45,.mt45_ > * {margin-top: 45px;}
.mt50,.mt50_ > * {margin-top: 50px;}
.mr0,.mr0_ > * {margin-right: 0px;}
.mr1,.mr1_ > * {margin-right: 1px;}
.mr2,.mr2_ > * {margin-right: 2px;}
.mr3,.mr3_ > * {margin-right: 3px;}
.mr4,.mr4_ > * {margin-right: 4px;}
.mr5,.mr5_ > * {margin-right: 5px;}
.mr6,.mr6_ > * {margin-right: 6px;}
.mr7,.mr7_ > * {margin-right: 7px;}
.mr8,.mr8_ > * {margin-right: 8px;}
.mr9,.mr9_ > * {margin-right: 9px;}
.mr10,.mr10_ > * {margin-right: 10px;}
.mr12,.mr12_ > * {margin-right: 12px;}
.mr14,.mr14_ > * {margin-right: 14px;}
.mr16,.mr16_ > * {margin-right: 16px;}
.mr18,.mr18_ > * {margin-right: 18px;}
.mr20,.mr20_ > * {margin-right: 20px;}
.mr25,.mr25_ > * {margin-right: 25px;}
.mr30,.mr30_ > * {margin-right: 30px;}
.mr35,.mr35_ > * {margin-right: 35px;}
.mr40,.mr40_ > * {margin-right: 40px;}
.mr45,.mr45_ > * {margin-right: 45px;}
.mr50,.mr50_ > * {margin-right: 50px;}
.mb0,.mb0_ > * {margin-bottom: 0px;}
.mb1,.mb1_ > * { margin-bottom: 1px;}
.mb2,.mb2_ > * {margin-bottom: 2px;}
.mb3,.mb3_ > * {margin-bottom: 3px;}
.mb4,.mb4_ > * {margin-bottom: 4px;}
.mb5,.mb5_ > * {margin-bottom: 5px;}
.mb6,.mb6_ > * {margin-bottom: 6px;}
.mb7,.mb7_ > * {margin-bottom: 7px;}
.mb8,.mb8_ > * {margin-bottom: 8px;}
.mb9,.mb9_ > * {margin-bottom: 9px;}
.mb10,.mb10_ > * {margin-bottom: 10px;}
.mb12,.mb12_ > * {margin-bottom: 12px;}
.mb14,.mb14_ > * {margin-bottom: 14px;}
.mb16,.mb16_ > * {margin-bottom: 16px;}
.mb18,.mb18_ > * {margin-bottom: 18px;}
.mb20,.mb20_ > * {margin-bottom: 20px;}
.mb25,.mb25_ > * {margin-bottom: 25px;}
.mb30,.mb30_ > * {margin-bottom: 30px;}
.mb35,.mb35_ > * {margin-bottom: 35px;}
.mb40,.mb40_ > * {margin-bottom: 40px;}
.mb45,.mb45_ > * {margin-bottom: 45px;}
.mb50,.mb50_ > * {margin-bottom: 50px;}
.ml0,.ml0_ > * {margin-left: 0px;}
.ml1,.ml1_ > * {margin-left: 1px;}
.ml2,.ml2_ > * {margin-left: 2px;}
.ml3,.ml3_ > * {margin-left: 3px;}
.ml4,.ml4_ > * {margin-left: 4px;}
.ml5,.ml5_ > * {margin-left: 5px;}
.ml6,.ml6_ > * {margin-left: 6px;}
.ml7,.ml7_ > * {margin-left: 7px;}
.ml8,.ml8_ > * {margin-left: 8px;}
.ml9,.ml9_ > * {margin-left: 9px;}
.ml10,.ml10_ > * {margin-left: 10px;}
.ml12,.ml12_ > * {margin-left: 12px;}
.ml14,.ml14_ > * {margin-left: 14px;}
.ml16,.ml16_ > * {margin-left: 16px;}
.ml18,.ml18_ > * {margin-left: 18px;}
.ml20,.ml20_ > * {margin-left: 20px;}
.ml25,.ml25_ > * {margin-left: 25px;}
.ml30,.ml30_ > * {margin-left: 30px;}
.ml35,.ml35_ > * {margin-left: 35px;}
.ml40,.ml40_ > * {margin-left: 40px;}
.ml45,.ml45_ > * {margin-left: 45px;}
.ml50,.ml50_ > * {margin-left: 50px;}
.mx0,.mx0_ > * {margin-left: 0px;margin-right: 0px;}
.mx1,.mx1_ > * {margin-left: 1px;margin-right: 1px;}
.mx2,.mx2_ > * {margin-left: 2px;margin-right: 2px;}
.mx3,.mx3_ > * {margin-left: 3px;margin-right: 3px;}
.mx4,.mx4_ > * {margin-left: 4px;margin-right: 4px;}
.mx5,.mx5_ > * {margin-left: 5px;margin-right: 5px;}
.mx6,.mx6_ > * {margin-left: 6px;margin-right: 6px;}
.mx7,.mx7_ > * {margin-left: 7px;margin-right: 7px;}
.mx8,.mx8_ > * {margin-left: 8px;margin-right: 8px;}
.mx9,.mx9_ > * {margin-left: 9px;margin-right: 9px;}
.mx10,.mx10_ > * {margin-left: 10px;margin-right: 10px;}
.mx12,.mx12_ > * {margin-left: 12px;margin-right: 12px;}
.mx14,.mx14_ > * {margin-left: 14px;margin-right: 14px;}
.mx16,.mx16_ > * {margin-left: 16px;margin-right: 16px;}
.mx18,.mx18_ > * {margin-left: 18px;margin-right: 18px;}
.mx20,.mx20_ > * {margin-left: 20px;margin-right: 20px;}
.mx25,.mx25_ > * {margin-left: 25px; margin-right: 25px;}
.mx30,.mx30_ > * {margin-left: 30px;margin-right: 30px;}
.mx35,.mx35_ > * {margin-left: 35px;margin-right: 35px;}
.mx40,.mx40_ > * {margin-left: 40px;margin-right: 40px;}
.mx45,.mx45_ > * {margin-left: 45px;margin-right: 45px;}
.mx50,.mx50_ > * {margin-left: 50px;margin-right: 50px;}
.my0,.my0_ > * {margin-top: 0px;margin-bottom: 0px;}
.my1,.my1_ > * {margin-top: 1px;margin-bottom: 1px;}
.my2,.my2_ > * {margin-top: 2px;margin-bottom: 2px;}
.my3,.my3_ > * {margin-top: 3px;margin-bottom: 3px;}
.my4,.my4_ > * {margin-top: 4px;margin-bottom: 4px;}
.my5,.my5_ > * {margin-top: 5px;margin-bottom: 5px;}
.my6,.my6_ > * {margin-top: 6px;margin-bottom: 6px;}
.my7,.my7_ > * {margin-top: 7px;margin-bottom: 7px;}
.my8,.my8_ > * {margin-top: 8px;margin-bottom: 8px;}
.my9,.my9_ > * {margin-top: 9px;margin-bottom: 9px;}
.my10,.my10_ > * {margin-top: 10px;margin-bottom: 10px;}
.my12,.my12_ > * {margin-top: 12px;margin-bottom: 12px;}
.my14,.my14_ > * {margin-top: 14px;margin-bottom: 14px;}
.my16,.my16_ > * {margin-top: 16px;margin-bottom: 16px;}
.my18,.my18_ > * {margin-top: 18px;margin-bottom: 18px;}
.my20,.my20_ > * {margin-top: 20px;margin-bottom: 20px;}
.my25,.my25_ > * {margin-top: 25px;margin-bottom: 25px;}
.my30,.my30_ > * {margin-top: 30px;margin-bottom: 30px;}
.my35,.my35_ > * {margin-top: 35px;margin-bottom: 35px;}
.my40,.my40_ > * {margin-top: 40px;margin-bottom: 40px;}
.my45,.my45_ > * {margin-top: 45px;margin-bottom: 45px;}
.my50,.my50_ > * {margin-top: 50px;margin-bottom: 50px;}
/**margin  End**/
/**padding Start**/
.p0,.p0_ > * {padding: 0px;}
.p1,.p1_ > * {padding: 1px;}
.p2,.p2_ > * {padding: 2px;}
.p3,.p3_ > * {padding: 3px;}
.p4,.p4_ > * {padding: 4px;}
.p5,.p5_ > * {padding: 5px;}
.p6,.p6_ > * {padding: 6px;}
.p7,.p7_ > * {padding: 7px;}
.p8,.p8_ > * {padding: 8px;}
.p9,.p9_ > * {padding: 9px;}
.p10,.p10_ > * {padding: 10px;}
.p12,.p12_ > * {padding: 12px;}
.p14,.p14_ > * {padding: 14px;}
.p16,.p16_ > * {padding: 16px;}
.p18,.p18_ > * {padding: 18px;}
.p20,.p20_ > * {padding: 20px;}
.p25,.p25_ > * {padding: 25px;}
.p30,.p30_ > * {padding: 30px;}
.p35,.p35_ > * {padding: 35px;}
.p40,.p40_ > * {padding: 40px;}
.p45,.p45_ > * {padding: 45px;}
.p50,.p50_ > * {padding: 50px;}
.pt0,.pt0_ > * {padding-top: 0px;}
.pt1,.pt1_ > * {padding-top: 1px;}
.pt2,.pt2_ > * {padding-top: 2px;}
.pt3,.pt3_ > * {padding-top: 3px;}
.pt4,.pt4_ > * {padding-top: 4px;}
.pt5,.pt5_ > * {padding-top: 5px;}
.pt6,.pt6_ > * {padding-top: 6px;}
.pt7,.pt7_ > * {padding-top: 7px;}
.pt8,.pt8_ > * {padding-top: 8px;}
.pt9,.pt9_ > * {padding-top: 9px;}
.pt10,.pt10_ > * {padding-top: 10px;}
.pt12,.pt12_ > * {padding-top: 12px;}
.pt14,.pt14_ > * {padding-top: 14px;}
.pt16,.pt16_ > * {padding-top: 16px;}
.pt18,.pt18_ > * {padding-top: 18px;}
.pt20,.pt20_ > * {padding-top: 20px;}
.pt25,.pt25_ > * {padding-top: 25px;}
.pt30,.pt30_ > * {padding-top: 30px;}
.pt35,.pt35_ > * {padding-top: 35px;}
.pt40,.pt40_ > * {padding-top: 40px;}
.pt45,.pt45_ > * {padding-top: 45px;}
.pt50,.pt50_ > * {padding-top: 50px;}
.pr0,.pr0_ > * {padding-right: 0px;}
.pr1,.pr1_ > * {padding-right: 1px;}
.pr2,.pr2_ > * {padding-right: 2px;}
.pr3,.pr3_ > * {padding-right: 3px;}
.pr4,.pr4_ > * {padding-right: 4px;}
.pr5,.pr5_ > * {padding-right: 5px;}
.pr6,.pr6_ > * {padding-right: 6px;}
.pr7,.pr7_ > * {padding-right: 7px;}
.pr8,.pr8_ > * {padding-right: 8px;}
.pr9,.pr9_ > * {padding-right: 9px;}
.pr10,.pr10_ > * {padding-right: 10px;}
.pr12,.pr12_ > * {padding-right: 12px;}
.pr14,.pr14_ > * {padding-right: 14px;}
.pr16,.pr16_ > * {padding-right: 16px;}
.pr18,.pr18_ > * {padding-right: 18px;}
.pr20,.pr20_ > * {padding-right: 20px;}
.pr25,.pr25_ > * {padding-right: 25px;}
.pr30,.pr30_ > * {padding-right: 30px;}
.pr35,.pr35_ > * {padding-right: 35px;}
.pr40,.pr40_ > * {padding-right: 40px;}
.pr45,.pr45_ > * {padding-right: 45px;}
.pr50,.pr50_ > * {padding-right: 50px;}
.pb0,.pb0_ > *,.pb0- * {padding-bottom: 0px;}
.pb1,.pb1_ > *,.pb1- * {padding-bottom: 1px;}
.pb2,.pb2_ > *,.pb2- * {padding-bottom: 2px;}
.pb3,.pb3_ > *,.pb3- * {padding-bottom: 3px;}
.pb4,.pb4_ > *,.pb4- * {padding-bottom: 4px;}
.pb5,.pb5_ > *,.pb5- * {padding-bottom: 5px;}
.pb6,.pb6_ > *,.pb6- * {padding-bottom: 6px;}
.pb7,.pb7_ > *,.pb7- * {padding-bottom: 7px;}
.pb8,.pb8_ > *,.pb8- * {padding-bottom: 8px;}
.pb9,.pb9_ > *,.pb9- * {padding-bottom: 9px;}
.pb10,.pb10_ > *,.pb10- * {padding-bottom: 10px;}
.pb12,.pb12_ > *,.pb12- * {padding-bottom: 12px;}
.pb14,.pb14_ > *,.pb14- * {padding-bottom: 14px;}
.pb16,.pb16_ > *,.pb16- * {padding-bottom: 16px;}
.pb18,.pb18_ > *,.pb18- * {padding-bottom: 18px;}
.pb20,.pb20_ > *,.pb20- * {padding-bottom: 20px;}
.pb25,.pb25_ > *,.pb25- * {padding-bottom: 25px;}
.pb30,.pb30_ > *,.pb30- * {padding-bottom: 30px;}
.pb35,.pb35_ > *,.pb35- * {padding-bottom: 35px;}
.pb40,.pb40_ > *,.pb40- * {padding-bottom: 40px;}
.pb45,.pb45_ > *,.pb45- * {padding-bottom: 45px;}
.pb50,.pb50_ > *,.pb50- * {padding-bottom: 50px;}
.pl0,.pl0_ > * {padding-left: 0px;}
.pl1,.pl1_ > * {padding-left: 1px;}
.pl2,.pl2_ > * {padding-left: 2px;}
.pl3,.pl3_ > * {padding-left: 3px;}
.pl4,.pl4_ > * {padding-left: 4px;}
.pl5,.pl5_ > * {padding-left: 5px;}
.pl6,.pl6_ > * {padding-left: 6px;}
.pl7,.pl7_ > * {padding-left: 7px;}
.pl8,.pl8_ > * {padding-left: 8px;}
.pl9,.pl9_ > * {padding-left: 9px;}
.pl10,.pl10_ > * {padding-left: 10px;}
.pl12,.pl12_ > * {padding-left: 12px;}
.pl14,.pl14_ > * {padding-left: 14px;}
.pl16,.pl16_ > * {padding-left: 16px;}
.pl18,.pl18_ > * {padding-left: 18px;}
.pl20,.pl20_ > * {padding-left: 20px;}
.pl25,.pl25_ > * {padding-left: 25px;}
.pl30,.pl30_ > * {padding-left: 30px;}
.pl35,.pl35_ > * {padding-left: 35px;}
.pl40,.pl40_ > * {padding-left: 40px;}
.pl45,.pl45_ > * {padding-left: 45px;}
.pl50,.pl50_ > * {padding-left: 50px;}
.px0,.px0_ > * {padding-left: 0px;padding-right: 0px;}
.px1,.px1_ > * {padding-left: 1px;padding-right: 1px;}
.px2,.px2_ > * {padding-left: 2px;padding-right: 2px;}
.px3,.px3_ > * {padding-left: 3px;padding-right: 3px;}
.px4,.px4_ > * {padding-left: 4px;padding-right: 4px;}
.px5,.px5_ > * {padding-left: 5px;padding-right: 5px;}
.px6,.px6_ > * {padding-left: 6px;padding-right: 6px;}
.px7,.px7_ > * {padding-left: 7px;padding-right: 7px;}
.px8,.px8_ > * {padding-left: 8px;padding-right: 8px;}
.px9,.px9_ > * {padding-left: 9px;padding-right: 9px;}
.px10,.px10_ > * {padding-left: 10px;padding-right: 10px;}
.px12,.px12_ > * {padding-left: 12px;padding-right: 12px;}
.px14,.px14_ > * {padding-left: 14px;padding-right: 14px;}
.px16,.px16_ > * {padding-left: 16px;padding-right: 16px;}
.px18,.px18_ > * {padding-left: 18px;padding-right: 18px;}
.px20,.px20_ > * {padding-left: 20px;padding-right: 20px;}
.px25,.px25_ > * {padding-left: 25px;padding-right: 25px;}
.px30,.px30_ > * {padding-left: 30px;padding-right: 30px;}
.px35,.px35_ > * {padding-left: 35px;padding-right: 35px;}
.px40,.px40_ > * {padding-left: 40px;padding-right: 40px;}
.px45,.px45_ > * {padding-left: 45px;padding-right: 45px;}
.px50,.px50_ > * {padding-left: 50px;padding-right: 50px;}
.py0,.py0_ > * {padding-top: 0px;padding-bottom: 0px;}
.py1,.py1_ > * {padding-top: 1px;padding-bottom: 1px;}
.py2,.py2_ > * {padding-top: 2px;padding-bottom: 2px;}
.py3,.py3_ > * {padding-top: 3px;padding-bottom: 3px;}
.py4,.py4_ > * {padding-top: 4px;padding-bottom: 4px;}
.py5,.py5_ > * {padding-top: 5px;padding-bottom: 5px;}
.py6,.py6_ > * {padding-top: 6px;padding-bottom: 6px;}
.py7,.py7_ > * {padding-top: 7px;padding-bottom: 7px;}
.py8,.py8_ > * {padding-top: 8px;padding-bottom: 8px;}
.py9,.py9_ > * {padding-top: 9px;padding-bottom: 9px;}
.py10,.py10_ > * {padding-top: 10px;padding-bottom: 10px;}
.py12,.py12_ > * {padding-top: 12px;padding-bottom: 12px;}
.py14,.py14_ > * {padding-top: 14px;padding-bottom: 14px;}
.py16,.py16_ > * {padding-top: 16px;padding-bottom: 16px;}
.py18,.py18_ > * {padding-top: 18px;padding-bottom: 18px;}
.py20,.py20_ > * {padding-top: 20px;padding-bottom: 20px;}
.py25,.py25_ > * {padding-top: 25px;padding-bottom: 25px;}
.py30,.py30_ > * {padding-top: 30px;padding-bottom: 30px;}
.py35,.py35_ > * {padding-top: 35px;padding-bottom: 35px;}
.py40,.py40_ > * {padding-top: 40px;padding-bottom: 40px;}
.py45,.py45_ > * {padding-top: 45px;padding-bottom: 45px;}
.py50,.py50_ > * {padding-top: 50px;padding-bottom: 50px;}
/**padding  End**/
/**width Start**/
.w0 {
    width: 0;
}
.w-p1,.w-p1_ > * {width: 10%!important;}
.w-p2,.w-p2_ > * {width: 20%!important;}
.w-p3,.w-p3_ > * {width: 30%!important;}
.w-p4,.w-p4_ > * {width: 40%!important;}
.w-p5,.w-p5_ > * {width: 50%!important;}
.w-p6,.w-p6_ > * {width: 60%!important;}
.w-p7,.w-p7_ > * {width: 70%!important;}
.w-p8,.w-p8_ > * {width: 80%!important;}
.w-p9,.w-p9_ > * {width: 90%!important;}
.w-p10,.w-p10_ > * {width: 100%!important;}
.max-w100 {max-width: 100%;}
.min-w100 {min-width: 100%;}
/**width  End**/
/**shape Start**/
.radius0,.radius0_ > * {border-radius: 0px;}
.radius1,.radius1_ > * {border-radius: 1px;}
.radius2,.radius2_ > * {border-radius: 2px;}
.radius3,.radius3_ > * {border-radius: 3px;}
.radius4,.radius4_ > * {border-radius: 4px;}
.radius5,.radius5_ > * {border-radius: 5px;}
.radius10,.radius10_ > * {border-radius: 10px;}
.radius18,.radius18_ > * {border-radius: 18px;}
.radius100,.radius100_ > * {border-radius: 100px;}
.radius-top-0 {border-top-left-radius: 0!important;border-top-right-radius: 0!important;}
.radius-right-0 {border-top-right-radius: 0!important;border-bottom-right-radius: 0!important;}
.radius-bottom-0 {border-bottom-left-radius: 0!important;border-bottom-right-radius: 0!important;}
.radius-left-0 {border-bottom-left-radius: 0!important;border-top-left-radius: 0!important;}
.circle {border-radius: 50%!important;}
.rect {border-radius: 0!important;}
/**shape  End**/