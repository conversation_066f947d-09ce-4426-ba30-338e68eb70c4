/*! Copyright (c) 2011 Piotr <PERSON> (http://rocha.la)
 * Dual licensed under the MIT (http://www.opensource.org/licenses/mit-license.php)
 * and GPL (http://www.opensource.org/licenses/gpl-license.php) licenses.
 *
 * Version: 1.0.6
 *
 */
(function(d){jQuery.fn.extend({slimScroll:function(m){var a=d.extend({wheelStep:20,width:"auto",height:"250px",size:"7px",color:"#000",position:"right",distance:"1px",start:"top",opacity:0.4,alwaysVisible:!1,disableFadeOut:!1,railVisible:!1,railColor:"#333",railOpacity:"0.2",railClass:"slimScrollRail",barClass:"slimScrollBar",wrapperClass:"slimScrollDiv",allowPageScroll:!1,scroll:0,touchScrollStep:200},m);this.each(function(){function f(h,d,f){var g=h,e=b.outerHeight()-c.outerHeight();d&&(g=parseInt(c.css("top"))+
h*parseInt(a.wheelStep)/100*c.outerHeight(),g=Math.min(Math.max(g,0),e),c.css({top:g+"px"}));j=parseInt(c.css("top"))/(b.outerHeight()-c.outerHeight());g=j*(b[0].scrollHeight-b.outerHeight());f&&(g=h,h=g/b[0].scrollHeight*b.outerHeight(),h=Math.min(Math.max(h,0),e),c.css({top:h+"px"}));b.scrollTop(g);q();l()}function r(){s=Math.max(b.outerHeight()/b[0].scrollHeight*b.outerHeight(),A);c.css({height:s+"px"})}function q(){r();clearTimeout(w);j==~~j&&(n=a.allowPageScroll,x!=j&&b.trigger("slimscroll",
0==~~j?"top":"bottom"));x=j;s>=b.outerHeight()?n=!0:(c.stop(!0,!0).fadeIn("fast"),a.railVisible&&e.stop(!0,!0).fadeIn("fast"))}function l(){a.alwaysVisible||(w=setTimeout(function(){if((!a.disableFadeOut||!p)&&!t&&!u)c.fadeOut("slow"),e.fadeOut("slow")},1E3))}var p,t,u,w,y,s,j,x,A=30,n=!1,b=d(this);if(b.parent().hasClass("slimScrollDiv")){var k=b.scrollTop(),c=b.parent().find(".slimScrollBar"),e=b.parent().find(".slimScrollRail");r();m&&("scrollTo"in m?k=parseInt(a.scrollTo):"scrollBy"in m&&(k+=parseInt(a.scrollBy)),
f(k,!1,!0))}else{a.height="auto"==a.height?b.parent().innerHeight():a.height;k=d("<div></div>").addClass(a.wrapperClass).css({position:"relative",overflow:"hidden",width:a.width,height:a.height});b.css({overflow:"hidden",width:a.width,height:a.height});var e=d("<div></div>").addClass(a.railClass).css({width:a.size,height:"100%",position:"absolute",top:0,display:a.alwaysVisible&&a.railVisible?"block":"none","border-radius":a.size,background:a.railColor,opacity:a.railOpacity,zIndex:90}),c=d("<div></div>").addClass(a.barClass).css({background:a.color,
width:a.size,position:"absolute",top:0,opacity:a.opacity,display:a.alwaysVisible?"block":"none","border-radius":a.size,BorderRadius:a.size,MozBorderRadius:a.size,WebkitBorderRadius:a.size,zIndex:99}),z="right"==a.position?{right:a.distance}:{left:a.distance};e.css(z);c.css(z);b.wrap(k);b.parent().append(c);b.parent().append(e);c.draggable({axis:"y",containment:"parent",start:function(){u=!0},stop:function(){u=!1;l()},drag:function(){f(0,d(this).position().top,!1)}});e.hover(function(){q()},function(){l()});
c.hover(function(){t=!0},function(){t=!1});b.hover(function(){p=!0;q();l()},function(){p=!1;l()});b.bind("touchstart",function(a){a.originalEvent.touches.length&&(y=a.originalEvent.touches[0].pageY)});b.bind("touchmove",function(b){b.originalEvent.preventDefault();b.originalEvent.touches.length&&f((y-b.originalEvent.touches[0].pageY)/a.touchScrollStep,!0)});var v=function(a){if(p){a=a||window.event;var b=0;a.wheelDelta&&(b=-a.wheelDelta/120);a.detail&&(b=a.detail/3);f(b,!0);a.preventDefault&&!n&&
a.preventDefault();n||(a.returnValue=!1)}};(function(){window.addEventListener?(this.addEventListener("DOMMouseScroll",v,!1),this.addEventListener("mousewheel",v,!1)):document.attachEvent("onmousewheel",v)})();r();"bottom"==a.start?(c.css({top:b.outerHeight()-c.outerHeight()}),f(0,!0)):"object"==typeof a.start&&(f(d(a.start).position().top,null,!0),a.alwaysVisible||c.hide())}});return this}});jQuery.fn.extend({slimscroll:jQuery.fn.slimScroll})})(jQuery);