/*!
 * froala_editor v1.2.2 (http://editor.froala.com)
 * Copyright 2014-2014 Froala
 */
!function(a){a.Editable.DEFAULTS=a.extend(a.Editable.DEFAULTS,{maxCharacters:-1}),a.Editable.prototype.validKeyCode=function(a,b){return b?!1:a>47&&58>a||32==a||13==a||a>64&&91>a||a>95&&112>a||a>185&&193>a||a>218&&223>a},a.Editable.prototype.charNumber=function(){return this.getText().length},a.Editable.prototype.checkCharNumber=function(a,b,c,d){return b.options.maxCharacters<0?!0:b.charNumber()<b.options.maxCharacters?!0:b.validKeyCode(c,d)?!1:!0},a.Editable.prototype.checkCharNumberOnPaste=function(b,c,d){if(c.options.maxCharacters<0)return!0;var e=a("<div>").html(d).text().length;return e+c.charNumber()<=c.options.maxCharacters?d:""},a.Editable.prototype.updateCharNumber=function(a,b){b.options.maxCharacters>=0&&b.$element.attr("data-chars",b.charNumber()+"/"+b.options.maxCharacters)},a.Editable.prototype.initCharNumber=function(){this.$original_element.on("editable.keydown",this.checkCharNumber),this.$original_element.on("editable.onPaste",this.checkCharNumberOnPaste),this.$original_element.on("editable.keyup",this.updateCharNumber),this.updateCharNumber(null,this)},a.Editable.initializers.push(a.Editable.prototype.initCharNumber)}(jQuery);