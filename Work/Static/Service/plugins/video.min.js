/*!
 * froala_editor v1.2.2 (http://editor.froala.com)
 * Copyright 2014-2014 Froala
 */
!function(a){a.Editable.DEFAULTS=a.extend(a.Editable.DEFAULTS,{videoAllowedAttrs:["src","width","height","frameborder","allowfullscreen","webkitallowfullscreen","mozallowfullscreen","href","target","id","controls","value","name"],videoAllowedTags:["iframe","object","param","video","source","embed"]}),a.Editable.VIDEO_PROVIDERS=[{test_regex:/^.*((youtu.be\/)|(v\/)|(\/u\/\w\/)|(embed\/)|(watch\?))\??v?=?([^#\&\?]*).*/,url_regex:/(?:https?:\/\/)?(?:www\.)?(?:m\.)?(?:youtube\.com|youtu\.be)\/(?:watch\?v=|embed\/)?(.+)/g,url_text:"//www.youtube.com/embed/$1",html:'<iframe width="640" height="360" src="{url}" frameborder="0" allowfullscreen></iframe>'},{test_regex:/^.*(vimeo\.com\/)((channels\/[A-z]+\/)|(groups\/[A-z]+\/videos\/))?([0-9]+)/,url_regex:/(?:https?:\/\/)?(?:www\.)?(?:vimeo\.com)\/(?:channels\/[A-z]+\/|groups\/[A-z]+\/videos\/)?(.+)/g,url_text:"//player.vimeo.com/video/$1",html:'<iframe width="640" height="360" src="{url}" frameborder="0" allowfullscreen></iframe>'},{test_regex:/^.+(dailymotion.com|dai.ly)\/(video|hub)?\/?([^_]+)[^#]*(#video=([^_&]+))?/,url_regex:/(?:https?:\/\/)?(?:www\.)?(?:dailymotion\.com|dai\.ly)\/(?:video|hub)?\/?(.+)/g,url_text:"//www.dailymotion.com/embed/video/$1",html:'<iframe width="640" height="360" src="{url}" frameborder="0" allowfullscreen></iframe>'},{test_regex:/^.+(screen.yahoo.com)\/(videos-for-you|popular)?\/[^_&]+/,url_regex:"",url_text:"",html:'<iframe width="640" height="360" src="{url}?format=embed" frameborder="0" allowfullscreen="true" mozallowfullscreen="true" webkitallowfullscreen="true" allowtransparency="true"></iframe>'}],a.Editable.video_commands={floatVideoLeft:{title:"Float Left",icon:{type:"font",value:"fa fa-align-left"}},floatVideoNone:{title:"Float None",icon:{type:"font",value:"fa fa-align-justify"}},floatVideoRight:{title:"Float Right",icon:{type:"font",value:"fa fa-align-right"}},removeVideo:{title:"Remove Video",icon:{type:"font",value:"fa fa-trash-o"}}},a.Editable.DEFAULTS=a.extend(a.Editable.DEFAULTS,{videoButtons:["floatVideoLeft","floatVideoNone","floatVideoRight","removeVideo"]}),a.Editable.commands=a.extend(a.Editable.commands,{insertVideo:{title:"Insert Video",icon:"fa fa-video-camera",callback:function(){this.insertVideo()},undo:!1}}),a.Editable.prototype.insertVideo=function(){this.closeImageMode(),this.imageMode=!1,this.showInsertVideo(),this.saveSelection(),this.options.inlineMode||this.positionPopup("insertVideo"),this.$video_wrapper.find("textarea").val("")},a.Editable.prototype.insertVideoHTML=function(){var a='<div class="froala-popup froala-video-popup" style="display: block;"><h4><span data-text="true">Insert video</span><i title="Cancel" class="fa fa-times" id="f-video-close-'+this._id+'"></i></h4><div class="f-popup-line"><textarea placeholder="Embedded code" id="f-video-textarea-'+this._id+'"></textarea></div><p class="or"><span data-text="true">or</span></p><div class="f-popup-line"><input type="text" placeholder="http://youtube.com/" id="f-video-input-'+this._id+'"/><button data-text="true" class="f-ok" id="f-video-ok-'+this._id+'">OK</button></div></div>';return a},a.Editable.prototype.buildInsertVideo=function(){this.$video_wrapper=a(this.insertVideoHTML()),this.$popup_editor.append(this.$video_wrapper),this.addListener("hidePopups",this.hideVideoWrapper),this.$video_wrapper.on("mouseup",a.proxy(function(a){this.isResizing()||a.stopPropagation()},this)),this.$video_wrapper.on("mouseup keydown","input#f-video-input-"+this._id+", textarea#f-video-textarea-"+this._id,a.proxy(function(a){a.stopPropagation()},this));var b=this;this.$video_wrapper.on("change","input#f-video-input-"+this._id+", textarea#f-video-textarea-"+this._id,function(){"INPUT"==this.tagName?b.$video_wrapper.find("textarea#f-video-textarea-"+b._id).val(""):"TEXTAREA"==this.tagName&&b.$video_wrapper.find("input#f-video-input-"+b._id).val("")}),this.$video_wrapper.on("click","button#f-video-ok-"+this._id,a.proxy(function(){var a=this.$video_wrapper.find("input#f-video-input-"+this._id),b=this.$video_wrapper.find("textarea#f-video-textarea-"+this._id);""!==a.val()?this.writeVideo(a.val(),!1):""!==b.val()&&this.writeVideo(b.val(),!0)},this)),this.$video_wrapper.on("click","i#f-video-close-"+this._id,a.proxy(function(){this.$bttn_wrapper.show(),this.hideVideoWrapper(),this.options.inlineMode&&!this.imageMode&&0===this.options.buttons.length&&this.hide(),this.restoreSelection(),this.options.inlineMode||this.hide()},this)),this.$video_wrapper.on("click",function(a){a.stopPropagation()}),this.$video_wrapper.on("click","*",function(a){a.stopPropagation()})},a.Editable.prototype.destroyVideo=function(){this.$video_wrapper.html("").removeData().remove()},a.Editable.prototype.initVideo=function(){this.buildInsertVideo(),this.addVideoControls(),this.addListener("destroy",this.destroyVideo)},a.Editable.initializers.push(a.Editable.prototype.initVideo),a.Editable.prototype.hideVideoEditorPopup=function(){this.$video_editor&&(this.$video_editor.hide(),a("span.f-video-editor").removeClass("active"),this.$element.removeClass("f-non-selectable"),this.editableDisabled||this.isHTML||this.$element.attr("contenteditable",!0))},a.Editable.prototype.showVideoEditorPopup=function(){this.$video_editor&&this.$video_editor.show(),this.$element.removeAttr("contenteditable")},a.Editable.prototype.addVideoControlsHTML=function(){this.$video_editor=a('<div class="froala-popup froala-video-editor-popup" style="display: none">');for(var b=a('<div class="f-popup-line">').appendTo(this.$video_editor),c=0;c<this.options.videoButtons.length;c++){var d=this.options.videoButtons[c];if(void 0!==a.Editable.video_commands[d]){var e=a.Editable.video_commands[d],f='<button class="fr-bttn" data-cmd="'+d+'" title="'+e.title+'">';f+=void 0!==this.options.icons[d]?this.prepareIcon(this.options.icons[d],e.title):this.prepareIcon(e.icon,e.title),f+="</button>",b.append(f)}}var g=this;this.$video_editor.find("button").click(function(b){b.stopPropagation(),g[a(this).attr("data-cmd")]()}),this.addListener("hidePopups",this.hideVideoEditorPopup),this.$popup_editor.append(this.$video_editor)},a.Editable.prototype.floatVideoLeft=function(){a("span.f-video-editor.active").attr("class","f-video-editor active fr-fvl"),this.saveUndoStep(),this.triggerEvent("videoFloatedLeft"),a("span.f-video-editor.active").click()},a.Editable.prototype.floatVideoRight=function(){a("span.f-video-editor.active").attr("class","f-video-editor active fr-fvr"),this.saveUndoStep(),this.triggerEvent("videoFloatedRight"),a("span.f-video-editor.active").click()},a.Editable.prototype.floatVideoNone=function(){a("span.f-video-editor.active").attr("class","f-video-editor active fr-fvn"),this.saveUndoStep(),this.triggerEvent("videoFloatedNone"),a("span.f-video-editor.active").click()},a.Editable.prototype.removeVideo=function(){a("span.f-video-editor.active").remove(),this.hide(),this.saveUndoStep(),this.triggerEvent("videoRemoved"),this.focus()},a.Editable.prototype.refreshVideo=function(){this.$element.find("iframe, object").each(function(b,c){for(var d=a(c),e=0;e<a.Editable.VIDEO_PROVIDERS.length;e++){var f=a.Editable.VIDEO_PROVIDERS[e];if(f.test_regex.test(d.attr("src"))){0===d.parents(".f-video-editor").length&&d.wrap('<span class="f-video-editor fr-fvn" data-fr-verified="true">');break}}})},a.Editable.prototype.addVideoControls=function(){this.addVideoControlsHTML(),this.addListener("sync",this.refreshVideo),this.$element.on("click touchend","span.f-video-editor",a.proxy(function(b){if(this.isDisabled)return!1;b.preventDefault(),b.stopPropagation();var c=b.currentTarget;this.clearSelection(),a(c).addClass("active"),this.showByCoordinates(a(c).offset().left+a(c).width()/2,a(c).offset().top+a(c).height()),this.showVideoEditorPopup(),this.refreshVideoButtons(c)},this))},a.Editable.prototype.refreshVideoButtons=function(b){var c=a(b).css("float");this.$video_editor.find("[data-cmd]").removeClass("active"),"left"===c?this.$video_editor.find('[data-cmd="floatVideoLeft"]').addClass("active"):"right"===c?this.$video_editor.find('[data-cmd="floatVideoRight"]').addClass("active"):this.$video_editor.find('[data-cmd="floatVideoNone"]').addClass("active")},a.Editable.prototype.writeVideo=function(b,c){var d=null;if(c)d=this.clean(b,!0,!1,this.options.videoAllowedTags,this.options.videoAllowedAttrs);else for(var e=0;e<a.Editable.VIDEO_PROVIDERS.length;e++){var f=a.Editable.VIDEO_PROVIDERS[e];if(f.test_regex.test(b)){b=b.replace(f.url_regex,f.url_text),d=f.html.replace(/\{url\}/,b);break}}d?(this.restoreSelection(),this.$element.focus(),this.insertHTML('<span class="f-video-editor fr-fvn" data-fr-verified="true">'+d+"</span>"),this.saveUndoStep(),this.$bttn_wrapper.show(),this.hideVideoWrapper(),this.hide(),this.triggerEvent("videoInserted",[d])):this.triggerEvent("videoError")},a.Editable.prototype.showVideoWrapper=function(){this.$video_wrapper&&(this.$video_wrapper.show(),this.$video_wrapper.find(".f-popup-line input").val(""))},a.Editable.prototype.hideVideoWrapper=function(){this.$video_wrapper&&this.$video_wrapper.hide()},a.Editable.prototype.showInsertVideo=function(){this.hidePopups(),this.showVideoWrapper()}}(jQuery);