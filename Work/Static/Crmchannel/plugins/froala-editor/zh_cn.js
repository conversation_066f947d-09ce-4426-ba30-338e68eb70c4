/*!
 * froala_editor v1.2.2 (http://editor.froala.com)
 * Copyright 2014-2014 Froala
 */

/**
 * Simplified Chinese spoken in China.
 */

$.Editable.LANGS['zh_cn'] = {
  translation: {
    "Bold": "\u7c97\u4f53",
    "Italic": "\u659c\u4f53",
    "Underline": "\u4e0b\u5212\u7ebf",
    "Strikethrough": "\u5220\u9664\u7ebf",
    "Font Size": "\u5b57\u53f7",
    "Color": "\u989c\u8272",
    "Background Color": "\u80cc\u666f\u8272",
    "Text Color": "\u6587\u5b57\u989c\u8272",
    "Format Block": "\u683c\u5f0f",
    "Normal": "\u6b63\u5e38",
    "Paragraph": "\u6bb5\u843d",
    "Code": "\u4ee3\u7801",
    "Quote": "\u5f15\u7528",
    "Heading 1": "\u6807\u98981",
    "Heading 2": "\u6807\u98982",
    "Heading 3": "\u6807\u98983",
    "Heading 4": "\u6807\u98984",
    "Heading 5": "\u6807\u98985",
    "Heading 6": "\u6807\u98986",
    "Block Style": "\u5ea7\u5f0f",
    "Alignment": "\u5bf9\u9f50\u65b9\u5f0f",
    "Align Left": "\u5de6\u5bf9\u9f50",
    "Align Center": "\u5c45\u4e2d",
    "Align Right": "\u53f3\u5bf9\u9f50",
    "Justify": "\u4e24\u7aef\u5bf9\u9f50",
    "Numbered List": "\u7f16\u53f7\u5217\u8868",
    "Bulleted List": "\u9879\u76ee\u7b26\u53f7",
    "Indent Less": "\u51cf\u5c11\u7f29\u8fdb",
    "Indent More": "\u589e\u52a0\u7f29\u8fdb",
    "Select All": "\u5168\u9009",
    "Insert Link": "\u63d2\u5165\u94fe\u63a5",
    "Insert Image": "\u63d2\u5165\u56fe\u7247",
    "Insert Video": "\u63d2\u5165\u89c6\u9891",
    "Undo": "\u64a4\u6d88",
    "Redo": "\u91cd\u590d",
    "Show HTML": "\u663e\u793a\u7684HTML",
    "Float Left": "\u5de6\u5bf9\u9f50",
    "Float None": "\u65e0",
    "Float Right": "\u53f3\u5bf9\u9f50",
    "Replace Image": "\u66f4\u6362\u56fe\u50cf",
    "Remove Image": "\u5220\u9664\u56fe\u50cf",
    "Title": "\u6807\u9898",
    "Insert image": "\u63d2\u5165\u56fe\u7247",
    "Drop image": "\u56fe\u50cf\u62d6\u653e",
    "or click": "\u6216\u70b9\u51fb",
    "or": "\u6216",
    "Enter URL": "\u8f93\u5165\u7f51\u5740",
    "Please wait!": "\u8bf7\u7a0d\u7b49\uff01",
    "Are you sure? Image will be deleted.": "\u4f60\u786e\u5b9a\u5417\uff1f\u56fe\u50cf\u5c06\u88ab\u5220\u9664\u3002",
    "UNLINK": "\u5220\u9664\u94fe\u63a5",
    "Open in new tab": "\u5f00\u542f\u5728\u65b0\u6807\u7b7e\u9875",
    "Type something": "\u8f93\u5165\u4e00\u4e9b\u5185\u5bb9",
    "Cancel": "\u53d6\u6d88",
    "OK": "\u786e\u5b9a",
    "Manage images": "\u7ba1\u7406\u56fe\u50cf",
    "Delete": "\u5220\u9664",
    "Font Family": "\u5b57\u4f53",
    "Insert Horizontal Line": "\u63d2\u5165\u6c34\u5e73\u7ebf",
    "Table": "\u8868\u683c",
    "Insert table": "\u63d2\u5165\u8868\u683c",
    "Cell": "\u5355\u5143\u683c",
    "Row": "\u884c",
    "Column": "\u5217",
    "Delete table": "\u5220\u9664\u8868\u683c",
    "Insert cell before": "\u524d\u63d2\u5165\u5355\u5143\u683c",
    "Insert cell after": "\u540e\u63d2\u5165\u7535\u6c60",
    "Delete cell": "\u5220\u9664\u5355\u5143\u683c",
    "Merge cells": "\u5408\u5e76\u5355\u5143\u683c",
    "Horizontal split": "\u6c34\u5e73\u5206\u5272",
    "Vertical split": "\u5782\u76f4\u5206\u5272",
    "Insert row above": "\u5728\u4e0a\u65b9\u63d2\u5165",
    "Insert row below": "\u5728\u4e0b\u65b9\u63d2\u5165",
    "Delete row": "\u5220\u9664\u884c",
    "Insert column before": "\u5728\u5de6\u4fa7\u63d2\u5165",
    "Insert column after": "\u5728\u53f3\u4fa7\u63d2\u5165",
    "Delete column": "\u5220\u9664\u5217"
  },
  direction: "ltr"
};