.Hunter-time-picker{
	position: absolute;
	border: 2px solid #c9cbce;
	width: 390px;
	background: #ffffff;
	z-index: 999999;
	font-size: 0;
}
.Hunter-time-picker:before, .Hunter-time-picker:after{
	content: '';
	display: block;
	width: 0;
	height: 0;
	border-width: 10px;
	border-style: solid;
	position: absolute;
	left: 20px;
	z-index: 999999;
}
.Hunter-time-picker:before{
	border-color: transparent transparent #ffffff;
	top: -17px;
	z-index: 9999999;
}
.Hunter-time-picker:after{
	border-color: transparent transparent #c9cbce;
	top: -20px;
}
.Hunter-time-picker *{
	box-sizing: border-box;
	margin: 0 auto;
	padding: 0;
	color: #666666;
	font-family: "Microsoft YaHei";
	font-size: 14px;
}
.Hunter-time-picker ul{
	list-style: none;
}
.Hunter-time-picker ul li{
	display: inline-block;
	position: relative;
	margin: 4px;
	cursor: pointer;
}
.Hunter-time-picker p{
	font-weight: bold;
	padding: 0 4px;
	margin-top: 4px;
	margin-bottom: 10px;
}
.Hunter-time-picker .line{
	width: 340px;
	margin: 0 auto;
	margin-top: 4px;
	border-bottom: 1px solid #d8d8d8;
}

/*选择小时*/
.Hunter-time-picker .Hunter-wrap{
	position: relative;
	width: 100%;
	background: #ffffff;
	padding: 9px;
}
.Hunter-time-picker .Hunter-hour-name{
	display: inline-block;
	width: 50px;
	height: 30px;
	text-align: center;
	line-height: 30px;
	position: relative;
	background-color: #f5f5f5;
}
.Hunter-time-picker .Hunter-hour-name:hover{
	color: #002DFF;
}

.Hunter-time-picker .Hunter-hour.active{
	z-index: 999999999;
}
.Hunter-time-picker .active .Hunter-hour-name{
	color: #ffffff;
	background-color: #4aabe9;
}
.Hunter-time-picker .Hunter-minute-wrap{
	display: none;
	border: 1px solid #D8D8D8;
	background: #ffffff;
	position: absolute;
	top: 29px;
	width: 370px;
	padding: 10px 10px 5px 10px;
}
.Hunter-time-picker .Hunter-minute{
	width: 50px;
	height: 30px;
	text-align: center;
	line-height: 30px;
	color: #999999;
	background-color: #f5f5f5;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
}
.Hunter-time-picker .Hunter-minute:hover{
	color: #002DFF;
}
.Hunter-time-picker .Hunter-minute.active {
	color: #ffffff;
	background-color: #4aabe9;
}
.Hunter-time-picker .Hunter-clean-btn{
	width: 108px;
	height: 30px;
    background-color: #4aabe9!important;
    color: #ffffff;
    background-image: none !important;
    border: 5px solid #4aabe9;
    border-radius: 0;
}
.Hunter-time-picker .Hunter-clean-btn:hover{
	background-color: #0B4B94 !important;
    border-color: #4aabe9;
}