/* Copyright (c) 2010 <PERSON> (http://brandonaaron.net)
 * Licensed under the MIT License (LICENSE.txt).
 *
 * Version 2.1.2
 */
(function(a) {
	//a.fn.bgiframe = (a.browser.msie && /msie 6\.0/i.test(navigator.userAgent) ?
	a.fn.bgiframe = (/msie/.test(navigator.userAgent.toLowerCase()) && /msie 6\.0/i.test(navigator.userAgent) ?
	function(d) {
		d = a.extend({
			top: "auto",
			left: "auto",
			width: "auto",
			height: "auto",
			opacity: true,
			src: "javascript:false;"
		},
		d);
		var c = '<iframe class="bgiframe"frameborder="0"tabindex="-1"src="' + d.src + '"style="display:block;position:absolute;z-index:-1;' + (d.opacity !== false ? "filter:Alpha(Opacity='0');": "") + "top:" + (d.top == "auto" ? "expression(((parseInt(this.parentNode.currentStyle.borderTopWidth)||0)*-1)+'px')": b(d.top)) + ";left:" + (d.left == "auto" ? "expression(((parseInt(this.parentNode.currentStyle.borderLeftWidth)||0)*-1)+'px')": b(d.left)) + ";width:" + (d.width == "auto" ? "expression(this.parentNode.offsetWidth+'px')": b(d.width)) + ";height:" + (d.height == "auto" ? "expression(this.parentNode.offsetHeight+'px')": b(d.height)) + ';"/>';
		return this.each(function() {
			if (a(this).children("iframe.bgiframe").length === 0) {
				this.insertBefore(document.createElement(c), this.firstChild)
			}
		})
	}: function() {
		return this
	});
	a.fn.bgIframe = a.fn.bgiframe;
	function b(c) {
		return c && c.constructor === Number ? c + "px": c
	}
})(jQuery);