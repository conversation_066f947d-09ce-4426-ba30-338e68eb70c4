// z-index: 1 3  5 7 9;
// 通过int.test.less编译，请修改less文件;
@baseFontSize: 37.5;
@baseEmFontSize: 12;
html,
body {
  background-color: #f3f3f3;
  min-height: 100%;
}
html {
  // font-size: 10px;
  // font-size: 62.5%;
}

.body-color {
  background-color: #fff;
}

// pub css
* {
  font-family: Helvetica, Tahoma, Arial, "PingFang SC", "Hiragino Sans GB",
    "Heiti SC", "Microsoft YaHei", "WenQuanYi Micro Hei";
}

/* 修改占位文字的默认样式 */

input::-moz-placeholder,
textarea::-moz-placeholder {
  color: #666666;
  font-size: 14px;
}

input:-ms-input-placeholder,
textarea:-ms-input-placeholder {
  color: #666666;
  font-size: 14px;
}

input::-webkit-input-placeholder,
textarea::-webkit-input-placeholder {
  color: #666666;
  font-size: 14px;
}

.tc {
  text-align: center;
}

.tl {
  text-align: left;
}

.tr {
  text-align: right;
}

.none {
  display: none;
}

.mt20 {
  margin-top: 20px;
}
.mb20 {
  margin-bottom: 20px;
}
.ml20 {
  margin-left: 20px;
}
.mr20 {
  margin-right: 20px;
}

// 清楚浮动
.clearFloat::after {
  content: "";
  height: 0;
  display: block;
  overflow: hidden;
  line-height: 0;
  clear: both;
}
.clearFloat {
  zoom: 1;
}
// 左浮动
.pull-left {
  float: left;
}
// 右浮动
.pull-right {
  float: right;
}

//页面公共样式如下
.page-comon-box {
  // 头部样式
  .header {
    background: rgba(13, 25, 36, 1);
    height: 50px;
    line-height: 50px;
    display: flex;
    width: 100%;
    padding: 0 15%;
    justify-content: space-between;
    position: fixed;
    top: 0px;
    left: 0px;
    z-index: 100;
    a:hover {
      color: #fff;
    }
    .logo {
      width: 120px;
      img {
        width: 100%;
      }
    }
    .menu-list {
      display: inline-block;
      .itemOne {
        display: inline-block;
        position: relative;
        > a {
          display: block;
          color: #fff;
          position: relative;
          padding: 0px 10px;
          text-decoration: none;
        }
        .second-list {
          position: absolute;
          width: 150px;
          padding-top: 10px;
          top: 50px;
          a {
            color: #333;
          }
          > ul {
            width: 100%;
            height: 100%;
            padding: 20px 0px;
            background: #f1f6fb;
            text-align: left;
            min-height: 80px;
            max-height: 200px;
            position: relative;
          }
          > ul > li {
            line-height: 30px;
            padding-left: 10px;
            padding: 0px 10px;
            cursor: pointer;
          }
          > ul > li:hover {
            color: #fff;
            background: #3ca3ff;
            a {
              color: #fff;
            }
            .three-list {
              display: block;
              a {
                color: #333;
              }
            }
          }
          > ul::after {
            content: "";
            position: absolute;
            top: -5px;
            right: 110px;
            border-bottom: 6px solid #f1f6fb;
            border-left: 6px solid transparent;
            border-right: 6px solid transparent;
          }
        }
        .three-list {
          position: absolute;
          width: 150px;
          background: #fff;
          text-align: left;
          min-height: 80px;
          max-height: 200px;
          padding: 20px 10px;
          top: 0px;
          left: 150px;
          color: #333;
          a {
            color: #333;
          }
          ul > li {
            line-height: 30px;
            padding-left: 10px;
            cursor: pointer;
            .cicle {
              display: inline-block;
              width: 4px;
              height: 4px;
              margin-right: 5px;
              border-radius: 50%;
              background: rgba(60, 163, 255, 1);
            }
          }
          .titele {
            font-size: 16px;
            color: rgba(51, 51, 51, 1);
            line-height: 21px;
            padding: 0px 0px 10px 0px;
            margin-bottom: 10px;
            border-bottom: 1px dashed #ddd;
          }
        }
      }
      .itemOne:hover {
        a:after {
          position: absolute;
          content: "";
          bottom: 0;
          left: 50%;
          width: 100%;
          height: 3px;
          -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
          background-color: #fff;
        }
        .second-list {
          display: block;
        }
      }
      .active {
        a:after {
          position: absolute;
          content: "";
          bottom: 0;
          left: 50%;
          width: 100%;
          height: 3px;
          -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
          background-color: #fff;
        }
      }
    }
    .btn-grap {
      display: inline-block;
    }
  }
  .content {
    .big-title {
      font-size: 36px;
      color: #333;
      text-align: center;
      line-height: 47px;
    }
    .small-title {
      font-size: 18px;
      line-height: 26px;
      margin-top: 10px;
      color: #aaa;
      text-align: center;
    }
    .modal-contain {
      max-width: 1200px;
    }
    .top-theme {
      text-align: center;
      position: relative;
      .tit-bg {
        font-size: 116px;
        font-weight: bold;
        color: rgba(226, 226, 226, 1);
        line-height: 135px;
      }
      .tit {
        font-size: 36px;
        color: rgba(51, 51, 51, 1);
        line-height: 47px;
        position: absolute;
        width: 100%;
        top: 32%;
      }
    }
  }
  // 底部样式
  .footer {
    padding: 30px 10%;
    background: #0d1924;
    color: #fff;
    .keyword-list {
      display: flex;
      padding: 20px 0px;
      li {
        flex: 1;
        line-height: 40px;
        font-size: 18px;
        text-align: center;
        display: flex;
        align-items: center;
        justify-content: center;
        border-right: 1px solid #2f3943;
        i {
          font-size: 40px;
          display: inline-block;
          margin-right: 20px;
        }
        span {
          display: inline-block;
          line-height: 40px;
        }
      }
      li:nth-of-type(1) {
        justify-content: flex-start;
      }
      li:nth-of-type(4) {
        justify-content: flex-end;
        border-right: none;
      }
    }
    .middle {
      padding: 30px 0px;
      border-top: 1px solid #2f3943;
      border-bottom: 1px solid #2f3943;
      .tit {
        font-size: 16px;
        color: rgba(255, 255, 255, 1);
        line-height: 21px;
        margin-bottom: 10px;
      }
      .list {
        a,
        > p {
          display: block;
          font-size: 14px;
          color: rgba(170, 170, 170, 1);
          line-height: 19px;
          margin-bottom: 10px;
        }
      }
      .contact {
        margin-left: 10%;
        .list .layui-btn {
          background: #0d1924;
          color: #fff;
        }
        .list .layui-btn-primary:hover {
          border-color: #fff;
        }
      }
      .apply {
        .layui-form-item {
          width: 350px;
          .layui-input,
          .layui-select,
          .layui-textarea {
            border-radius: 16px;
            border: 1px solid rgba(170, 170, 170, 1);
            background: #0d1924;
            color: #aaaaaa;
          }
        }
        .select-city {
          select {
            display: inline-block;
            width: 173px;
            border-radius: 16px;
            border: 1px solid rgba(170, 170, 170, 1);
            background: #0d1924;
            color: #aaaaaa;
            height: 38px;
            padding-left: 10px;
            line-height: 38px;
          }
        }
        .phone {
          position: relative;
          input {
            padding-right: 45px;
          }
          .help {
            width: 25px;
            height: 25px;
            background: url("/images/icon/icon-shape.png") no-repeat;
            background-size: 100% 100%;
            position: absolute;
            top: 6px;
            right: 15px;
          }
        }
      }
    }
    .foot-bottom {
      padding: 30px 0px 0px 0px;
      .left {
        width: 60%;
        p {
          font-size: 16px;
          color: rgba(170, 170, 170, 1);
          line-height: 21px;
          margin-bottom: 15px;
        }
      }
      .right {
        a {
          display: inline-block;
          margin-right: 20px;
          img {
            width: 90px;
          }
          p {
            font-size: 14px;
            color: rgba(170, 170, 170, 1);
            line-height: 19px;
            text-align: center;
          }
        }
        a:nth-last-of-type(1) {
          margin-right: 0px;
        }
      }
    }
  }
  // 悬浮框
  .Suspension {
    width: 60px;
    position: fixed;
    bottom: 100px;
    right: 20px;
    z-index: 50;
    background: rgba(255, 255, 255, 1);
    border: 1px solid rgba(170, 170, 170, 1);
    a {
      font-size: 16px;
      color: #666666;
      line-height: 21px;
      display: block;
      padding: 10px 20px;
    }
    .btn-try {
      color: rgba(255, 255, 255, 1);
      background: rgba(0, 0, 0, 1);
    }
    .icon {
      width: 28px;
      height: 31px;
      background: url("/images/icon/icon-customer.png") no-repeat;
      background-size: 100% 100%;
      display: block;
      margin: 5px auto;
    }
    .btn-label {
      > span {
        width: 6px;
        height: 6px;
        background: rgba(102, 102, 102, 1);
        border-radius: 50%;
        display: block;
        margin: 5px auto;
      }
    }
  }
}
// 视频弹框样式
.frame-page {
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  position: fixed;
  z-index: 100;
  top: 0;
  left: 0;
  .frame-video-box {
    width: 50%;
    max-width: 998px;
    margin: 0 auto;
    padding-top: 15%;
    position: relative;
    video {
      width: 100%;
      margin: 0 auto;
      display: block;
    }
    .btn-colse {
      width: 33px;
      height: 33px;
      line-height: 33px;
      text-align: center;
      border-radius: 50%;
      color: #fff;
      position: absolute;
      top: 31%;
      right: -33px;
      z-index: 200;
      cursor: pointer;
      background: #8ea3a4;
      i {
        font-size: 18px;
        color: #fff;
      }
    }
  }
}

// 首页样式
.home-page {
  .header {
    background: rgba(13, 25, 36, 0);
  }
  .body {
    .content1 {
      position: relative;
      a:hover {
        color: #fff;
      }
      .swiper-container-horizontal > .swiper-pagination-bullets,
      .swiper-pagination-custom,
      .swiper-pagination-fraction {
        bottom: 55px;
      }
      .swiper-container-horizontal
        > .swiper-pagination-bullets
        .swiper-pagination-bullet {
        width: 16px;
        height: 6px;
        background: rgba(255, 255, 255, 1);
        border-radius: 5px;
        background: #65d0f4;
        opacity: 1;
      }
      .swiper-container-horizontal
        > .swiper-pagination-bullets
        .swiper-pagination-bullet-active {
        background: #fff;
      }
      .swiper-slide {
        img {
          width: 100%;
        }
      }
      .btn-top {
        font-size: 18px;
        position: absolute;
        bottom: 36%;
        left: 7.5%;
        z-index: 50;
      }
      .btn-arrow {
        position: absolute;
        bottom: 8px;
        left: 0;
        color: #fff;
        z-index: 50;
        width: 100%;
        p {
          margin: 0px;
          text-align: center;
        }
      }
      .top-logo {
        .layui-row > div {
          padding: 20px 0px;
          border-left: 1px solid #ddd;
          background: #f8f8f8;
        }
        .layui-row > div .col-cont {
          display: flex;
          justify-content: space-between;
          align-items: center;
          width: 330px;
          margin: 0 auto;
          .col-left {
            img {
              width: 90px;
            }
          }
          .col-right {
            width: 230px;
            .title {
              font-size: 20px;
            }
            .cont {
              font-size: 14px;
              color: #888888;
            }
          }
        }
        .layui-row > div::nth-last-of-type(1) {
          border-left: none;
        }
      }
    }
    .content2 {
      padding: 70px 20px;
      background: #fff;
      .modal-contain {
        width: 75%;
        margin: 60px auto 0 auto;
        display: flex;
        justify-content: space-between;
        .left {
          width: 60%;
          video {
            width: 100%;
            // height: 100%;
          }
        }
        .right {
          width: 40%;
          padding: 0px 0px 0px 20px;
          .tit {
            font-size: 18px;
            color: #333333;
            line-height: 24px;
            margin-bottom: 5px;
          }
          .cont {
            font-size: 14px;
            color: rgba(170, 170, 170, 1);
            line-height: 24px;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 3;
            overflow: hidden;
          }
          ul li {
            display: flex;
            justify-content: flex-start;
            margin-bottom: 20px;
            cursor: pointer;
          }
          ul li:nth-last-of-type(1) {
            margin-bottom: 0px;
          }
          ul li:hover {
            .icon {
              background: url("../images/icon/icon-play.png") no-repeat;
              background-size: 100% 100%;
            }
          }
          .icon {
            display: inline-block;
            width: 30px;
            height: 30px;
            background: url("../images/icon/icon-pause.png") no-repeat;
            background-size: 100% 100%;
          }
          .active {
            .icon {
              background: url("../images/icon/icon-play.png") no-repeat;
              background-size: 100% 100%;
            }
            .tit {
              color: #2a5082;
            }
          }
          .detail {
            width: calc(100% - 30px);
            padding-left: 10px;
          }
        }
      }
    }
    .content3 {
      padding: 70px 20px;
      background: #f1f6fb;
      .modal-contain {
        width: 75%;
        margin: 60px auto 0 auto;
        .list-row {
          display: flex;
          .col-list {
            flex: 1;
            .icon {
              width: 163px;
              height: 163px;
              border-radius: 50%;
              padding-top: 35px;
              margin: 0 auto;
              background: rgba(255, 255, 255, 1);
              box-shadow: 0px 0px 25px 0px rgba(0, 78, 137, 0.08);
              img {
                width: 96px;
                margin: 0 auto;
                display: block;
              }
            }
            p {
              width: 95%;
              text-align: center;
              margin: 0 auto;
            }
            .tit {
              font-size: 18px;
              color: rgba(51, 51, 51, 1);
              line-height: 24px;
              margin: 10px auto;
            }
            .cont {
              font-size: 14px;
              color: rgba(102, 102, 102, 1);
              line-height: 19px;
            }
          }
        }
      }
    }
    .content4 {
      padding: 70px 20px;
      background: #fff;
      .modal-contain {
        width: 75%;
        margin: 60px auto 0 auto;
        display: flex;
        .left {
          min-width: 150px;
          background: rgba(255, 255, 255, 1);
          box-shadow: 0px 0px 26px 0px rgba(0, 0, 0, 0.08);
          li {
            padding: 20px;
            cursor: pointer;
            img {
              width: 64px;
              display: block;
              margin: 0 auto 10px auto;
            }
            p {
              font-size: 24px;
              color: rgba(51, 51, 51, 1);
              line-height: 31px;
              text-align: center;
            }
          }
          .active {
            background: #3ca3ff;
            p {
              color: #fff;
            }
            position: relative;
          }
          .active::after {
            content: "";
            position: absolute;
            top: 60px;
            right: -15px;
            border-top: 10px solid transparent;
            border-left: 15px solid #3ca3ff;
            border-bottom: 10px solid transparent;
          }
        }
        .right {
          display: flex;
          padding-left: 30px;
          > div {
            flex: 1;
          }
          .r-left {
            padding-right: 30px;
            position: relative;
            .title {
              font-size: 34px;
              color: rgba(51, 51, 51, 1);
              line-height: 45px;
            }
            .line {
              width: 62px;
              height: 2px;
              background: rgba(60, 163, 255, 1);
              margin: 10px 0px 30px 0px;
            }
            .cont {
              font-size: 18px;
              color: rgba(102, 102, 102, 1);
              line-height: 24px;
              p {
                margin-bottom: 15px;
              }
            }
            .tip {
              font-size: 28px;
              color: rgba(102, 102, 102, 1);
              line-height: 37px;
              margin-bottom: 20px;
              span {
                color: #df825a;
              }
            }
            .layui-btn {
              position: absolute;
              bottom: 0px;
              left: 0px;
              padding: 0px 30px;
            }
          }
          .r-right {
            position: relative;
            img {
              position: absolute;
              bottom: 0px;
              left: 0px;
            }
          }
        }
      }
    }
    .content5 {
      padding: 70px 20px;
      background: #f1f6fb;
      .modal-contain {
        width: 75%;
        margin: 60px auto 0 auto;
        .news-list {
          li {
            float: left;
            width: 20%;
            a {
              display: block;
              width: 100%;
              .img {
                position: relative;
                .gb {
                  position: absolute;
                  top: 0;
                  left: 0;
                  width: 100%;
                  height: 100%;
                  background: rgba(0, 0, 0, 0.6);
                }
              }
              .cont {
                width: 100%;
                background: rgba(255, 255, 255, 1);
                box-shadow: 0px 0px 26px 0px rgba(0, 0, 0, 0.08);
                font-size: 30px;
                // padding: 65px 8%;
                color: rgba(51, 51, 51, 1);
                line-height: 45px;
                text-align: center;
                position: relative;
                img {
                  opacity: 0;
                }
                .text {
                  position: absolute;
                  top: 0;
                  left: 0;
                  width: 100%;
                  height: 100%;
                  vertical-align: middle;
                  padding-top: 35%;
                }
              }
            }
          }
          .item1 a .cont .text {
            padding-top: 25%;
          }
          li:hover {
            a .cont {
              color: #fff;
              background: #3ca3ff;
            }
          }
          .active a .cont {
            color: #fff;
            background: #3ca3ff;
          }
          .item2,
          .item4 {
            margin-top: 19.6%;
          }
        }
      }
    }
    .content6 {
      padding: 70px 20px;
      background: #fff;
      .btn-more {
        display: block;
        text-align: center;
        margin-top: 10px;
        span {
          font-size: 18px;
          color: #006ddf;
          line-height: 24px;
          i {
            font-size: 18px;
            width: 24px;
            height: 24px;
            display: inline-block;
            background: #006ddf;
            color: #fff;
            border-radius: 50%;
          }
        }
      }
      .modal-contain {
        width: 95%;
        margin: 60px auto 0 auto;
        .swiper-slide {
          padding: 20px;
          .slider-cont {
            padding: 30px 20px 20px 20px;
            background: rgba(255, 255, 255, 1);
            box-shadow: 0px 0px 26px 0px rgba(0, 0, 0, 0.08);
            text-align: center;
            .time {
              font-size: 14px;
              color: rgba(51, 51, 51, 1);
              line-height: 19px;
              margin-bottom: 20px;
            }
            .tit {
              font-size: 18px;
              color: rgba(51, 51, 51, 1);
              line-height: 24px;
            }
            a {
              color: #3ca3ff;
              font-size: 14px;
              margin: 20px 0px;
              display: block;
            }
            .cont {
              font-size: 14px;
              color: rgba(102, 102, 102, 1);
              line-height: 19px;
              text-align: left;
              margin-bottom: 20px;
              display: -webkit-box;
              -webkit-box-orient: vertical;
              -webkit-line-clamp: 3;
              overflow: hidden;
            }
          }
        }
        .swiper-container-horizontal > .swiper-scrollbar {
          opacity: 1 !important;
          height: 2px;
        }
        .swiper-button-prev {
          width: 48px;
          height: 48px;
          background: url("/images/icon/icon-prev-active.png") no-repeat;
          background-size: 48px 48px;
        }
        .swiper-button-next {
          width: 48px;
          height: 48px;
          background: url("/images/icon/icon-next-active.png") no-repeat;
          background-size: 48px 48px;
        }
      }
    }
  }
}
// 登录页面样式
.login-page {
  background-color: #f3f3f3;
  position: relative;
  min-height: calc(100vh - 50px);
  .content {
    width: 75%;
    max-width: 1200px;
    margin: 50px auto 0 auto;
    padding: 14% 0;
    .title {
      text-align: center;
      font-size: 28px;
      color: rgba(51, 51, 51, 1);
      line-height: 37px;
      margin-bottom: 5%;
    }
    .layui-row {
      > div {
        position: relative;
        a {
          display: block;
          width: 85%;
          margin: 0 auto;
        }
        .tit {
          position: absolute;
          top: 40%;
          left: 37%;
          text-align: center;
          font-size: 28px;
          color: rgba(255, 255, 255, 1);
          line-height: 37px;
        }
      }
    }
  }
  .bottom {
    width: 400px;
    position: absolute;
    bottom: 0;
    right: 0;
    img {
      width: 100%;
    }
  }
}
//帮助页面样式
.help-page,
.help-video,
.help-person,
.help-problem {
  background-color: #f3f3f3;
  min-height: calc(100vh - 50px);
  .header {
    background: #fff;
    box-shadow: 0px 0px 26px 0px rgba(0, 0, 0, 0.08);
    .menu-list li {
      display: inline-block;
    }
    .menu-list li:nth-of-type(1) {
      margin-right: 20px;
    }
    .menu-list li a {
      color: #333;
    }
  }
  .bg-1 {
    background: #84c5ff;
  }
  .bg-2 {
    background: #ffda99;
  }
  .bg-3 {
    background: #abf2ff;
  }
  .bg-4 {
    background: #ffc3c6;
  }
  .content {
    width: 100%;
    margin: 50px auto 0 auto;
    .top {
      padding: 30px 12.5%;
      margin: 0 auto;
      .layui-row {
        max-width: 1200px;
        margin: 0 auto;
      }
      .layui-row > div {
        max-width: 1200px;
        display: flex;
        align-items: center;
        justify-content: center;
        .img {
          width: 100px;
          height: 100px;
          border-radius: 50%;
          overflow: hidden;
          margin-right: 10%;
          img {
            width: 40px;
            display: block;
            margin: 28% auto 0 auto;
          }
        }
        .tit {
          font-size: 18px;
          color: rgba(51, 51, 51, 1);
          line-height: 24px;
        }
      }
      .layui-row > div:nth-of-type(1) {
        justify-content: flex-start;
      }
      .layui-row > div:nth-of-type(4) {
        justify-content: flex-end;
        border-right: none;
      }
    }
    .modal-cont {
      background: #f1f6fb;
      padding: 30px 12.5%;
      margin-bottom: 50px;
      input::-moz-placeholder,
      textarea::-moz-placeholder {
        color: #aaa;
        font-size: 14px;
      }

      input:-ms-input-placeholder,
      textarea:-ms-input-placeholder {
        color: #aaa;
        font-size: 14px;
      }

      input::-webkit-input-placeholder,
      textarea::-webkit-input-placeholder {
        color: #aaa;
        font-size: 14px;
      }
      .cont {
        max-width: 1200px;
        margin: 0 auto;
        .search {
          width: 100%;
          max-width: 540px;
          margin: 0 auto;
          position: relative;
          input {
            padding: 10px 10px 10px 40px;
            border: 2px solid rgba(60, 163, 255, 1);
          }
          input:hover {
            border: 2px solid rgba(60, 163, 255, 1);
          }
          > i {
            color: #aaa;
            font-size: 23px;
            position: absolute;
            display: block;
            top: 6px;
            left: 10px;
          }
        }
        .middle {
          display: flex;
          background: #fff;
          margin-top: 30px;
          .title {
            font-size: 16px;
            color: rgba(60, 163, 255, 1);
            line-height: 21px;
            margin-bottom: 15px;
            padding: 0 15px;
          }
          .left {
            padding: 20px 0;
            min-width: 150px;
            text-align: center;
            border-right: 1px solid #ddd;
          }
          .layui-tab-title {
            border: none;
            height: auto;
            li {
              display: block;
              position: static;
              font-size: 14px;
              position: relative;
              line-height: 20px;
              margin-bottom: 15px;
            }
            .layui-this {
              color: rgba(60, 163, 255, 1);
              border-left: 2px solid rgba(60, 163, 255, 1);
            }
          }
          .layui-tab-title .layui-this:after {
            display: none;
          }
          .right {
            width: 100%;
            padding: 20px 20px;
            .top-item {
              display: flex;
              align-items: center;
              justify-content: space-between;
              .sm-t {
                color: #aaaaaa;
                font-size: 14px;
              }
              .tips {
                color: #3ca3ff;
                font-size: 14px;
              }
            }
            .none {
              display: none;
            }
            .tab-cont {
              color: #333;
              font-size: 16px;
              > li {
                line-height: 30px;
                p {
                  cursor: pointer;
                }
                .detail {
                  color: #666666;
                  line-height: 24px;
                  padding-left: 20px;
                  font-size: 14px;
                  display: none;
                }
              }
            }
          }
        }
      }
    }
  }
  .footer {
    background: #fff;
    color: #666666;
    font-size: 16px;
    .left {
      border-right: 1px solid #ddd;
    }
    .tit {
      font-size: 18px;
      color: rgba(0, 0, 0, 1);
      line-height: 26px;
      margin-bottom: 15px;
    }
    ul li {
      margin-bottom: 15px;
    }
    ul li:nth-last-of-type(1) {
      margin-bottom: 0px;
    }
    > .layui-row {
      > div {
        padding-left: 8%;
      }
      .wechat {
        text-align: center;
        img {
          width: 120px;
          margin: 0 auto;
          display: block;
        }
      }
    }
  }
}
// 培训视频
.help-video {
  .tab-cont {
    li {
      width: 26%;
      float: left;
      margin-left: 5%;
      margin-bottom: 10px;
      video {
        width: 100%;
      }
      p {
        font-size: 14px;
        color: rgba(51, 51, 51, 1);
        line-height: 19px;
        text-align: center;
      }
    }
  }
}
// 人工支持
.help-person {
  .middle {
    display: block;
    .layui-row {
      width: 100%;
      padding: 10% 5%;
      table td {
        font-size: 18px;
        color: rgba(51, 51, 51, 1);
        line-height: 40px;
        vertical-align: baseline;
        span {
          color: #aaaaaa;
        }
      }
      .wechat-list {
        display: flex;
        justify-content: space-between;
        > div {
          width: 45%;
          font-size: 14px;
          color: rgba(102, 102, 102, 1);
          line-height: 19px;
          p {
            margin-top: 20px;
            text-align: center;
          }
        }
      }
    }
  }
}
// 产品页面
.product-page {
  .product-banner {
    position: relative;
    .layui-btn {
      position: absolute;
      left: 18%;
      bottom: 20%;
    }
  }
  .content .modal-contain {
    width: 75%;
    max-width: 1200px;
    margin: 0px auto 0 auto;
    padding: 40px 0;
  }
  .content2 {
    background: #f1f6fb;
    .tit {
      background: #fff;
      padding: 10px 0;
      > div {
        width: 75%;
        max-width: 1200px;
        margin: 0 auto;
        color: rgba(51, 51, 51, 1);
        line-height: 24px;
      }
    }
    .modal-contain {
      .left {
        position: relative;
        padding-left: 50px;
        .cont {
          font-size: 14px;
          color: rgba(102, 102, 102, 1);
          line-height: 24px;
          margin: 20px 0 40px 0;
          padding-right: 15%;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 3;
          overflow: hidden;
        }
        .message-list {
          padding-right: 15%;
          li {
            margin-bottom: 5px;
          }
          li a {
            font-size: 14px;
            color: rgba(60, 163, 255, 1);
            line-height: 19px;
            padding-left: 15px;
            position: relative;
            > span {
              position: absolute;
              top: 0px;
              left: 0px;
              width: 10px;
              height: 11px;
              display: inline-block;
            }
          }
          .icon-map {
            background: url("/images/icon/icon-map.png") no-repeat;
            background-size: 10px 11px;
          }
          .icon-fire {
            background: url("/images/icon/icon-fire.png") no-repeat;
            background-size: 10px 11px;
          }
        }
      }
      .left::before {
        content: "";
        position: absolute;
        top: 0px;
        left: 5px;
        width: 40px;
        height: 40px;
        cursor: pointer;
        display: inline-block;
        background: url("/Work/View/Guanwang/images/icon/icon-intrudce.png") no-repeat;
        background-size: 40px 40px;
      }
      .video-box {
        width: 450px;
        background: #fff;
        .video-info {
          padding: 20px;
          font-size: 14px;
          color: rgba(170, 170, 170, 1);
          line-height: 19px;
          padding-left: 60px;
          position: relative;
          .icon-video {
            position: absolute;
            top: 18px;
            left: 20px;
            width: 23px;
            height: 20px;
            cursor: pointer;
            display: inline-block;
            background: url("/Work/View/Guanwang/images/product/video-pause.png") no-repeat;
            background-size: 23px 20px;
          }
        }
      }
    }
  }
  .content3 {
    .list-row {
      display: flex;
      margin-bottom: 30px;
      .col-list {
        flex: 1;
        .icon {
          width: 90px;
          height: 90px;
          border-radius: 50%;
          margin: 0 auto;
          img {
            width: 100%;
            margin: 0 auto;
            display: block;
          }
        }
        p {
          width: 80%;
          text-align: center;
          margin: 0 auto;
        }
        .tit {
          font-size: 18px;
          color: rgba(51, 51, 51, 1);
          line-height: 24px;
          margin: 10px auto;
        }
        .cont {
          font-size: 14px;
          color: rgba(102, 102, 102, 1);
          line-height: 19px;
          max-height: 100px;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 3;
          overflow: hidden;
        }
      }
    }
    padding: 30px 0px;
  }
  .content4 {
    background: #f1f6fb;
    padding: 30px 0px;
    .layui-tab-title li {
      padding: 0 65px;
      font-size: 16px;
    }
    .layui-tab-title .layui-this:after {
      left: 34%;
      width: 30%;
    }
    .layui-tab-content {
      .layui-tab-item {
        .left {
          width: 60%;
          padding-left: 5%;
          padding-right: 10%;
          .cont {
            font-size: 14px;
            color: rgba(102, 102, 102, 1);
            line-height: 19px;
            margin-bottom: 20px;
          }
        }
        .right {
          width: 40%;
          position: relative;
          img {
            width: 300px;
          }
          .info {
            position: absolute;
            top: 15%;
            left: 8%;
            color: #fff;
            .tit {
              position: relative;
              font-size: 16px;
              color: rgba(255, 255, 255, 1);
              line-height: 21px;
              padding-left: 28px;
              > span {
                position: absolute;
                top: 0px;
                left: 0px;
                width: 21px;
                height: 21px;
                display: inline-block;
                background: url("/Work/View/Guanwang/images/icon/icon-intrudce.png") no-repeat;
                background-size: 21px 21px;
              }
            }
            ul {
              li {
                font-size: 14px;
                color: rgba(255, 255, 255, 1);
                line-height: 24px;
                .icon {
                  display: inline-block;
                  width: 6px;
                  height: 6px;
                  background: rgba(255, 255, 255, 1);
                  margin-right: 10px;
                }
              }
            }
            a {
              display: block;
              font-size: 14px;
              color: rgba(60, 163, 255, 1);
              line-height: 19px;
            }
          }
        }
      }
    }
  }
}
// 解决方案
.solution-page {
  .solution-banner {
    position: relative;
    .btn-contact {
      width: 100%;
      position: absolute;
      left: 0%;
      bottom: 20%;
      .layui-btn {
        display: block;
        margin: 0 auto;
      }
      a {
        display: block;
        color: #fff;
      }
    }
  }
  .content {
    a {
      display: block;
    }
  }
  .content .modal-contain {
    width: 75%;
    margin: 0px auto 0 auto;
    padding: 40px 0;
    .state1 {
      background: linear-gradient(
        315deg,
        rgba(6, 161, 255, 1) 0%,
        rgba(93, 196, 255, 1) 100%
      );
    }
    .state2 {
      background: linear-gradient(
        315deg,
        rgba(4, 88, 207, 1) 0%,
        rgba(114, 151, 255, 1) 100%
      );
    }
  }
  .content2 {
    padding: 30px 0px;
    .layui-row {
      display: flex;
      .big-img,
      .state {
        position: relative;
        .info {
          color: #fff;
          position: absolute;
          top: 30px;
          left: 30px;
          .tit {
            font-size: 24px;
            color: rgba(255, 255, 255, 1);
            line-height: 31px;
            margin-bottom: 30px;
          }
          .cont {
            max-width: 100%;
          }
        }
      }
      .state {
        width: 100%;
        height: 100%;
        .info {
          left: 0px;
          padding: 0 30px;
        }
      }
    }
  }
  .content3 {
    background: #f1f6fb;
    padding: 30px 0px;
    .layui-tab-title li {
      padding: 0 20px;
      font-size: 16px;
    }
    .layui-tab-title .layui-this:after {
      left: 34%;
      width: 30%;
    }
    .layui-tab-content {
      .layui-tab-item {
        .solution-list {
          li {
            width: 30%;
            float: left;
            background: rgba(255, 255, 255, 1);
            box-shadow: 0px 0px 16px 0px rgba(131, 131, 131, 0.08);
            margin-right: 3%;
            .top {
              display: flex;
              i {
                font-size: 30px;
              }
            }
            a {
              padding: 20px;
              min-height: 200px;
            }
          }
          .empty {
            padding: 20px;
            min-height: 200px;
            padding-top: 6%;
            padding-left: 6%;
            color: #aaaaaa;
            img {
              width: 70px;
              display: inline-block;
            }
          }
        }
      }
    }
  }
}
// 解决方案详情
.solution-detail-page {
  .solution-banner {
    .banner-item {
      position: relative;
    }
    .btn-contact {
      width: 100%;
      position: absolute;
      left: 0%;
      bottom: 20%;
      padding-left: 10%;
      .layui-btn {
        display: block;
      }
      a {
        display: block;
        color: #fff;
      }
    }
  }
  .content {
    a {
      display: block;
    }
  }
  .content .modal-contain {
    width: 75%;
    margin: 0px auto 0 auto;
    padding: 40px 0;
  }
  .content1 {
    background: #fff;
    .top-logo {
      padding: 20px 0;
      .layui-row {
        width: 75%;
        margin: 0px auto 0 auto;
        max-width: 1200px;
      }
      .line {
        background: #ddd;
        width: 100%;
        height: 1px;
        margin: 10px 0px 20px 0px;
      }
      .layui-row > div .col-cont {
        img {
          display: block;
          width: 88px;
          margin: 0 auto;
        }
        .cont {
          max-width: 200px;
          margin: 0 auto;
          font-size: 14px;
          text-align: center;
          color: #333;
        }
      }
    }
  }
  .content2 {
    background: #f1f6fb;
    padding: 30px 0px;
    .solution-list {
      > li {
        width: 23.8%;
        float: left;
        background: rgba(255, 255, 255, 1);
        box-shadow: 0px 0px 16px 0px rgba(131, 131, 131, 0.08);
        margin-right: 1.5%;
        .top {
          display: flex;
          align-items: center;
          i {
            font-size: 30px;
          }
        }
        a {
          padding: 20px;
          min-height: 350px;
        }
        .tag-list {
          margin-top: 10px;
          li {
            float: none;
            color: #666666;
            line-height: 19px;
            .icon {
              display: inline-block;
              width: 10px;
              height: 10px;
              background: rgba(60, 163, 255, 1);
              margin-right: 10px;
            }
          }
        }
      }
      li:nth-last-of-type(1) {
        margin-right: 0px;
      }
    }
    .modal-contain > a {
      font-size: 14px;
      text-align: center;
      color: rgba(60, 163, 255, 1);
      line-height: 19px;
      margin-top: 60px;
    }
  }
  .content3 {
    background: #fff;
    padding: 30px 0px;
    .modal-contain {
      display: flex;
      .left {
        padding-right: 10%;
        .tit {
          font-size: 16px;
          color: rgba(51, 51, 51, 1);
          line-height: 21px;
          margin-bottom: 15px;
        }
        .cont {
          p {
            font-size: 14px;
            color: rgba(102, 102, 102, 1);
            line-height: 19px;
            margin-bottom: 5px;
          }
        }
      }
    }
  }
}
// 关于我们
.about-us-page {
  .solution-banner {
    position: relative;
    .menu-tab {
      width: 100%;
      position: absolute;
      left: 0%;
      bottom: 0%;
      height: 50px;
      background: rgba(0, 0, 0, 0.5928);
      padding: 0 12.5%;
      a {
        color: #fff;
        text-align: center;
        font-size: 16px;
        line-height: 50px;
        width: 49%;
        display: inline-block;
      }
      .active {
        background: #3ca3ff;
      }
    }
  }
  .content {
    a {
      display: block;
    }
  }
  .content .modal-contain {
    width: 75%;
    margin: 0px auto 0 auto;
    padding: 40px 0;
  }
  .content1 {
    background: #fff;
    .top-logo {
      padding: 20px 0;
      .layui-row {
        width: 75%;
        margin: 0px auto 0 auto;
        max-width: 1200px;
      }
      .line {
        background: #ddd;
        width: 100%;
        height: 1px;
        margin: 10px 0px 20px 0px;
      }
      .layui-row > div .col-cont {
        img {
          display: block;
          width: 88px;
          margin: 0 auto;
        }
        .cont {
          max-width: 200px;
          margin: 0 auto;
          font-size: 14px;
          text-align: center;
          color: #333;
        }
      }
    }
  }
  .content2 {
    background: #ffffff;
    padding: 30px 0px;
    .layui-row {
      text-align: center;
      > div {
        padding-bottom: 10px;
      }
      > div:nth-last-of-type(2) {
        border-left: 1px solid #ddd;
        border-right: 1px solid #ddd;
      }
      .time {
        font-size: 74px;
        font-weight: bold;
        color: rgba(51, 51, 51, 1);
        line-height: 86px;
      }
    }
  }
  .content3 {
    background: #f1f6fb;
    padding: 30px 0px;
    .modal-contain {
      .layui-row > div .col-cont {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 250px;
        margin: 0 auto;
        .col-left {
          img {
            width: 66px;
          }
        }
        .col-right {
          width: 150px;
          .title {
            font-size: 20px;
          }
          .cont {
            font-size: 14px;
            color: #888888;
          }
        }
      }
      > .layui-row:nth-of-type(1) {
        margin-bottom: 40px;
      }
    }
  }
  .content4 {
    position: relative;
    .bg-cont {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      padding: 30px 0px;
      .top-theme .tit-bg {
        color: #707070;
      }
      .top-theme .tit {
        color: #fff;
      }
      .col-cont {
        width: 75%;
        margin: 0 auto;
        min-height: 300px;
        padding: 20px 30px;
        background: rgba(51, 51, 51, 1);
        color: #fff;
        text-align: center;
        .tit {
          font-size: 24px;
          color: rgba(255, 255, 255, 1);
          line-height: 31px;
        }
        .cont {
          font-size: 16px;
          line-height: 21px;
          margin-top: 20px;
        }
      }
    }
  }
  .content5 {
    padding: 30px 0px;
    background: #f1f6fb;
    .modal-contain {
      .col-cont {
        float: left;
        width: 20%;
        text-align: center;
        img {
          width: 66px;
          margin: 0 auto;
        }
        .title {
          font-size: 20px;
          line-height: 24px;
          margin: 15px 0px 5px 0px;
        }
        .cont {
          font-size: 14px;
          line-height: 19px;
          color: #888888;
        }
      }
    }
  }
  .content6 {
    padding: 30px 0px;
    background: #fff;
    .news-list {
      li {
        float: left;
        width: 20%;
        a {
          display: block;
          width: 100%;
          .img {
            position: relative;
            .gb {
              position: absolute;
              top: 0;
              left: 0;
              width: 100%;
              height: 100%;
              background: rgba(0, 0, 0, 0.6);
            }
          }
          .cont {
            width: 100%;
            background: rgba(255, 255, 255, 1);
            box-shadow: 0px 0px 26px 0px rgba(0, 0, 0, 0.08);
            font-size: 30px;
            // padding: 65px 8%;
            color: rgba(51, 51, 51, 1);
            line-height: 45px;
            text-align: center;
            position: relative;
            img {
              opacity: 0;
            }
            .text {
              position: absolute;
              top: 0;
              left: 0;
              width: 100%;
              height: 100%;
              vertical-align: middle;
              padding-top: 35%;
            }
          }
        }
      }
      .item1 a .cont .text {
        padding-top: 25%;
      }
      .active a .cont {
        color: #fff;
        background: #3ca3ff;
      }
      .item2,
      .item4 {
        margin-top: 19.6%;
      }
    }
  }
}
// 产品服务
.product-service-page {
  .service-banner-container {
    .swiper-pagination-bullet {
      width: 20px;
      height: 4px;
      border-radius: 0;
      background: #ddd;
    }
    > .swiper-pagination-bullets,
    .swiper-pagination-custom,
    .swiper-pagination-fraction {
      bottom: 40px;
    }
  }
  .content {
    a {
      display: block;
    }
  }
  .content .modal-contain {
    width: 75%;
    margin: 0px auto 0 auto;
    padding: 40px 0;
  }
  .content2 {
    background: #ffffff;
    padding: 30px 0px;
    .bg {
      background: #0d1924;
    }
    .layui-row {
      text-align: center;
      > div {
        position: relative;
        .info {
          position: absolute;
          cursor: pointer;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          padding: 0px 10px;
          padding-top: 35%;
          background: rgba(0, 0, 0, 0.7);
          transition: background 200ms;
          -moz-transition: background 200ms; /* Firefox 4 */
          -webkit-transition: background 200ms; /* Safari 和 Chrome */
          -o-transition: background 200ms; /* Opera */
          .icon {
            width: 64px;
            margin: 0 auto;
          }
          .line {
            width: 40px;
            height: 3px;
            margin: 20px auto;
            background: rgba(255, 255, 255, 1);
          }
          .tit {
            font-size: 22px;
            color: rgba(255, 255, 255, 1);
            line-height: 29px;
            margin-bottom: 20px;
          }
          .cont {
            display: none;
            transition: display 200ms;
            -moz-transition: display 200ms; /* Firefox 4 */
            -webkit-transition: display 200ms; /* Safari 和 Chrome */
            -o-transition: display 200ms; /* Opera */
            p{
              display: -webkit-box;
              -webkit-box-orient: vertical;
              -webkit-line-clamp: 2;
              overflow: hidden;
              font-size: 16px;
              color: rgba(255, 255, 255, 1);
              line-height: 24px;
            }
          }
          a {
            margin: 0 auto;
            margin-top: 20px;
            display: none;
            transition: display 200ms;
            -moz-transition: display 200ms; /* Firefox 4 */
            -webkit-transition: display 200ms; /* Safari 和 Chrome */
            -o-transition: display 200ms; /* Opera */
            width: 120px;
            height: 30px;
            line-height: 30px;
            color: #fff;
            border: 1px solid rgba(255, 255, 255, 1);
          }
        }
      }
      .info:hover {
        // transition：all ease-in 200ms;
        background: rgba(60, 163, 255, 0.8);
        padding-top: 20%;
        .cont,
        a {
          display: block;
        }
      }
    }
  }
  .content3 {
    background: #ffffff;
    padding: 30px 0px 0px 0px;
    .layui-row {
      text-align: center;
      > div {
        cursor: pointer;
      }
      .icon {
        width: 64px;
        margin: 0 auto;
      }
      .tit {
        font-size: 14px;
        line-height: 19px;
        margin-top: 10px;
      }
      .active {
        color: #006ddf;
      }
    }
    .bg {
      background: #f1f6fb;
      .modal-contain {
        display: flex;
      }
      .left {
        width: 40%;
        max-height: 386px;
        overflow-y: auto;
        li {
          display: block;
          padding: 0px 30px 0px 0px;
          padding-bottom: 40px;
          cursor: pointer;
          // border-right: 5px solid #e7e7e7;
          .tit {
            font-size: 34px;
            color: rgba(51, 51, 51, 1);
            line-height: 45px;
            span {
              position: relative;
            }
            span::after {
              content: "Hot";
              display: inline-block;
              background: rgba(223, 130, 90, 1);
              font-size: 18px;
              color: rgba(255, 255, 255, 1);
              line-height: 24px;
              padding: 0px 6px;
              position: absolute;
              right: -45px;
              top: 0px;
            }
          }
          .cont {
            font-size: 18px;
            color: rgba(102, 102, 102, 1);
            line-height: 25px;
            margin: 20px 0;
          }
          a {
            width: 80px;
            height: 30px;
            line-height: 30px;
            text-align: center;
            color: #3ca3ff;
            border: 1px solid rgba(60, 163, 255, 1);
          }
        }
        li:nth-last-of-type(1) {
          padding-bottom: 0px;
        }
        .is-active {
          border-right: 5px solid #b4dcff;
        }
      }
      .left::-webkit-scrollbar {/*滚动条整体样式*/
          width: 5px;     /*高宽分别对应横竖滚动条的尺寸*/
          height: 1px;
      }
      .left::-webkit-scrollbar-thumb {/*滚动条里面小方块*/
              border-radius: 5px;
              // -webkit-box-shadow: inset 0 0 5px rgba(0,0,0,0.2);
              background: #B4DCFF;
          }
      .left::-webkit-scrollbar-track {/*滚动条里面轨道*/
              // -webkit-box-shadow: inset 0 0 5px rgba(0,0,0,0.2);
              border-radius: 5px;
              background: #E7E7E7;
          }
      .right-content {
        width: 60%;
        padding: 20px 0px 20px 35px;
        .modal-list {
          li {
            width: 40%;
            margin-right: 30px;
            margin-bottom: 20px;
            .tit {
              font-size: 16px;
              color: rgba(51, 51, 51, 1);
              line-height: 21px;
              margin-bottom: 5px;
            }
            .cont {
              font-size: 14px;
              color: rgba(170, 170, 170, 1);
              line-height: 19px;
            }
            .btn-more {
              font-size: 14px;
              color: rgba(60, 163, 255, 1);
              line-height: 19px;
              margin-top: 24px;
            }
          }
        }
      }
    }
  }
}
