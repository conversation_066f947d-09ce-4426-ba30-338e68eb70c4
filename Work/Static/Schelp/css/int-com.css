.el-container {
  height: 100%;
}

.el-main {
  height: 100%;
  /* // margin-bottom: 20px; */
}

.content-collapse {
  left: 65px;
}

.header .logo {
  padding-left: 20px;
}

/* // 左浮动 */
.pull-left {
  float: left;
}

/* // 右浮动 */
.pull-right {
  float: right;
}

/* // 清楚浮动的类 */
.clearFloat::after {
  content: "";
  height: 0;
  display: block;
  overflow: hidden;
  line-height: 0;
  clear: both;
}

.clearFloat {
  zoom: 1;
}

.font-26 {
  font-size: 26px;
}

.h1 {
  font-size: 20px;
}

.h2 {
  font-size: 18px;
}

.h3 {
  font-size: 16px;
}

.h4 {
  font-size: 15px;
}

.text-1 {
  font-size: 14px;
}

.text-2 {
  font-size: 13px;
}

.text-3 {
  font-size: 12px;
}

/* // 字体颜色 */
.font-ccc {
  color: #cccccc;
}

.font-333 {
  color: #333333;
}

.font-fff {
  color: #fff;
}

.font-aaa {
  color: #aaaaaa;
}

.font-aaa {
  color: #aaaaaa;
}

.font-006 {
  color: #006ddf;
}

/* // 字体样式 */
.fb {
  font-weight: bold;
}

a {
  text-decoration: none;
}

/* //背景颜色 */
.bg-white {
  background-color: #fff;
}

.bg-f1f {
  background-color: #f1f6fb;
}

.bg-f8f {
  background-color: #f8f8f8;
}

.bg-f97 {
  background-color: #f9733d;
}

.bg-3ca {
  background-color: #3ca3ff;
}

.bg-006 {
  background-color: #006ddf;
}

/* flex */
.flex {
  display: flex;
}

.flex--align {
  display: flex;
  justify-content: center;
  align-items: center;
}

.flex--align1 {
  display: flex;
  align-items: center;
}

.flex--space {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.flex--space1 {
  display: flex;
  justify-content: space-between;
}

a {
  text-decoration: none;
  cursor: pointer;
}

a:hover {
  /* color: #fff; */
  text-decoration: none;
}

.mr10 {
  margin-right: 10px;
}

.mt10 {
  margin-top: 10px;
}

.ml10 {
  margin-left: 10px;
}

.mb10 {
  margin-bottom: 10px;
}

.mr20 {
  margin-right: 20px;
}

.mt20 {
  margin-top: 20px;
}

.ml20 {
  margin-left: 20px;
}

.mb20 {
  margin-bottom: 20px;
}

/* 按钮 */
.min-btn {
  padding: 0px 5px;
  border-radius: 2px;
  line-height: 20px;
  height: 20px;
  display: inline-block;
}

.btn-big {
  padding: 12px 20px;
  display: inline-block;
}

.btn-defaut {
  color: #fff;
  border: 1px solid rgba(255, 255, 255, 1);
}

.btn-blue {
  color: #fff;
  background-color: #3ca3ff;
}

.btn-f97 {
  color: #fff;
  background-color: #F9733D;
}

.body img {
  width: 100%;
}