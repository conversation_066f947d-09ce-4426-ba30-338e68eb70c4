/**
 * Created by Administrator on 2016/12/14.
 */
window.console = window.console || {};
console.log || (console.log = opera.postError);

/*
 * JDB 1.0
 * version: 1.0
 * http://www.baisui.la
 * Copyright 2016, 2018 mohism [ <EMAIL> ]
 *
 */

var CRMJS = CRMJS || {};
(function($) {
  //兼容placeholder8
  CRMJS.Comint = {
    placeholderSupport: function() {
      return "placeholder" in document.createElement("input");
    },
    placeholderStart: function() {
      if (!this.placeholderSupport()) {
        $("[placeholder]")
          .focus(function() {
            var input = $(this);
            if (input.val() == input.attr("placeholder")) {
              input.val("");
              input.removeClass("placeholder");
            }
          })
          .blur(function() {
            var input = $(this);
            if (input.val() == "" || input.val() == input.attr("placeholder")) {
              input.addClass("placeholder");
              input.val(input.attr("placeholder"));
            }
          })
          .blur();
      }
    },
    // 获取url上的参数值
    getQueryString: function(name) {
      var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
      var r = window.location.search.substr(1).match(reg);
      if (r != null) {
        return unescape(r[2]);
      }
      return null;
    },
    getQueryStringChina: function(name) {
      var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
      var r = window.location.search.substr(1).match(reg);
      if (r != null) {
        return unescape(decodeURI(r[2]));
      }
      return null;
    },
    // 获取localstorage
    getStorage: function(key) {
      return JSON.parse(localStorage.getItem(key));
    },
    // 设置localstorage
    setStorage: function(key, v) {
      localStorage.setItem(key, JSON.stringify(v));
    },
    // 移除localstorage
    removeStorage: function(key) {
      localStorage.removeItem(key);
    }
  };

  //插件运行
  CRMJS.Start = {
    GoStart: function() {
       
      layui.use('element', function(){
        var element = layui.element;
      });

    }
  };
  $(document).ready(function() {
    CRMJS.Start.GoStart();
  });
})(jQuery);
