<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2016/12/13
 * Time: 16:29
 */
if (!defined('IN_ADARO'))
{
    die('No Permissions');
}
if (__FILE__ == '')
{
    die('Fatal error code: 0');
}

/*error_reporting(E_ALL);
ini_set('display_errors', '1');*/
/* 取得当前Adaro所在的根目录 */
header("content-type:text/html; charset=utf-8");
date_default_timezone_set('PRC') or die('时区设置失败，请联系管理员！');
if(!defined('ROOT_PATH'))//项目绝对路径
    define('ROOT_PATH', str_replace('Core/int.php', '', str_replace('\\', '/', __FILE__)));
set_include_path(get_include_path() . PATH_SEPARATOR . "Core");

//默认引入
function Intautoload($object){
    $file = BASEDIR."/Core/Classlib/{$object}.php";

    if (file_exists($file))
    {
        require_once($file);
    }
}

spl_autoload_register('Intautoload');

require(ROOT_PATH . 'Config/webconfig.php');//加载常量文件
require(ROOT_PATH . 'Config/dataconfig.php');//加载数据库配置文件
require(ROOT_PATH . 'Core/base/import.php');//加载常用函数处理库
include(ROOT_PATH . "Engine/Smarty.class.php");

/* 对用户传入的变量进行转义操作。*/
if (!get_magic_quotes_gpc())
{
    $request = array_merge($_POST,$_GET,$_FILES);
    if (!empty($request))
    {
        $request  = addslashes_deep($request);
    }

    $_COOKIE   = addslashes_deep($_COOKIE);
}else{
    $request = array_merge($_POST,$_GET,$_FILES);
}

$bjip = real_ip();
$nowtime = gnmetime();
$agent = $_SERVER['HTTP_USER_AGENT'];