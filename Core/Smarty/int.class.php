<?php
/** 自定义模板处理函数
 * ============================================================================
 * 版权所有 (C)2012-11-02号 http://www.mohism.cn 。
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * @version : v1.0
 * ----------------------------------------------------------------------------
 */
function VariableList($params,$content,&$smarty)
{
    global $viewControl;
    extract($params);
    $variable = $viewControl->getOne("cms_variable","variable_string = '{$string}'");
    $dbList = $viewControl->getList("cms_variablelist","variable_id = {$variable['variable_id']}","order by list_weight ASC");
    if($dbList){
        $smarty->assign($listName, $dbList);
    }else{
        $smarty->assign($listName, false);
    }
    return $content;
}
$smarty->registerPlugin("block",'VariableList','VariableList');

/**产品品牌获取**/
function goodsBrandList($params,$content,&$smarty)
{
    global $viewControl;
    extract($params);
    $datawhere = "1";

    if(isset($rominc) && $rominc == '1'){
        $datawhere .= " and brand_rominc = '1'";
    }
    if(!isset($order)){
        $order = "Order by brand_id ASC";
    }

    if(isset($num)){
        $order .= " limit 0,{$num}";
    }

    $articleList = $viewControl->getList("myw_goods_brand",$datawhere,$order);
    $smarty->assign($listName, $articleList);
    return $content;
}
$smarty->registerPlugin("block",'goodsBrandList','goodsBrandList');

/**文章列表获取**/
function ArticleGetList($params,$content,&$smarty)
{
    global $viewControl;
    extract($params);
    $nowtime = gnmetime();
    $articlewhere = "(cpat_class='{$id}' and cpat_releasetime < {$nowtime})";
    if(isset($rominc) && $rominc == '1'){
        $articlewhere .= " and cpat_recommendinc = '1'";
    }
    if(isset($topinc) && $topinc == '1'){
        $articlewhere .= " and cpat_topinc = '1'";
    }
    if(isset($newinc) && $newinc == '1'){
        $articlewhere .= " and cpat_newsinc = '1'";
    }
    if(!isset($order)){
        $order = "cpat_releasetime DESC";
    }
    $articleList = $viewControl->getList("cms_article",$articlewhere,"ORDER BY {$order} limit 0,$num");
    $smarty->assign($listName, $articleList);
    return $content;
}
$smarty->registerPlugin("block",'ArticleGetList','ArticleGetList');//文章列表

/**产品目录获取**/
function proParameterList($params,$content,&$smarty)
{
    global $viewControl;
    extract($params);
    $datawhere = "1";

    if(isset($parameter_type) && $parameter_type !== ''){
        $datawhere .= " and parameter_type = '{$parameter_type}'";
    }
    if(!isset($order)){
        $order = "Order by parameter_sort ASC";
    }

    if(isset($num)){
        $order .= " limit 0,{$num}";
    }else{
        $order .= " limit 0,10";
    }

    $dataList = $viewControl->getList("myw_good_parameter",$datawhere,$order);
    if($dataList){
        $smarty->assign($listName, $dataList);
    }else{
        $smarty->assign($listName, false);
    }
    return $content;
}
$smarty->registerPlugin("block",'proParameterList','proParameterList');

/**时间处理**/
function Times_Get($params)
{
    extract($params);
    $timego = explode(" ",$time);
    $timen = explode("-",$timego[0]);
    $timef = explode(":",$timego[1]);
    $Surplus = mktime($timef[0],$timef[1],$timef[2],$timen[1],$timen[2],$timen[0]) - time();
    echo $Surplus;
}
$smarty->registerPlugin("function",'Times_Get','Times_Get');//时间处理

function baseGet($params)
{
    global $viewControl;
    extract($params);
    $menutype = $viewControl->getOne("cms_variable","variable_string='{$var}'","");
    echo $menutype['content'];
}
$smarty->registerPlugin("function",'baseGet','baseGet');//自定义变量

//时间处理
function timeSpacingOk($params)
{
    extract($params);
    $spacing = time()-$time;
    switch($spacing){
        case $spacing <= 60: echo "1分钟内";break;
        case $spacing <= 300 and $spacing > 60: echo "5分钟内";break;
        case $spacing <= 600 and $spacing > 300: echo "10分钟内";break;
        case $spacing <= 3600 and $spacing > 600: echo "一小时内";break;
        case $spacing <= 3600*2 and $spacing > 3600: echo "二小时内";break;
        case $spacing <= 3600*24 and $spacing > 3600*2: echo "一天内";break;
        case $spacing <= 3600*24*7 and $spacing > 3600*24: echo "一周内";break;
        case $spacing <= 3600*24*30 and $spacing > 3600*24*7: echo "一个月内";break;
        case $spacing > 3600*24*30: echo "一个月前";break;
    }
}
$smarty->registerPlugin("function",'timeSpacingOk','timeSpacingOk');//时间间距


//关键词 转换颜色
function connetReplaceurl($params)
{
    extract($params);
    $wordsArray = explode(" ",$keyword);

    foreach ($wordsArray as $change) {
        while (@preg_match("/[^\>](" . $change . ")[^\<]/i", " " . $varstr . " ", $regs)) {
            $varstr = preg_replace("/" . $regs[1] . "/i", "<b>" . $regs[1] . "</b>", $varstr);
        }
    }
    echo $varstr;
}
$smarty->registerPlugin("function",'connetReplaceurl','connetReplaceurl');//SEO变量替换





//附件路径问题处理
function FilePathGet($params)
{
    extract($params);
    if(strstr($file,'http://')){
        echo $file;
    }else{
        echo IMG_PATH.$file;
    }
}
$smarty->registerPlugin("function",'FilePathGet','FilePathGet');

/**活动列表获取**/
function ActivityGetList($params,$content,&$smarty)
{
    global $viewControl;
    extract($params);
    $nowtime = gnmetime();
    $Activitywhere = "(activity_type='{$id}' and activity_addtime < {$nowtime}";
    if(isset($rominc) && $rominc == '1'){
        $Activitywhere .= " and activity_recommend = '1'";
    }
    if(!isset($order)){
        $order = "activity_addtime DESC";
    }
    $articleList = $viewControl->getList("cms_article",$Activitywhere,"ORDER BY {$order} limit 0,$num");
    $smarty->assign($listName, $articleList);
    return $content;
}
$smarty->registerPlugin("block",'ActivityGetList','ActivityGetList');//活动列表获取

//职工id对应的名字
function articlemenuList($params,$content,&$smarty)
{
    global $viewControl;
    extract($params);
    $nowtime = gnmetime();
    $articlewhere = "cpat_releasetime < {$nowtime}";

    if(isset($menuid) && $menuid !== ''){
        $articlewhere .= " and cpat_2column in ({$menuid})";
    }
    if(isset($rominc) && $rominc == '1'){
        $articlewhere .= " and cpat_recommend = '1'";
    }
    if(isset($topinc) && $topinc == '1'){
        $articlewhere .= " and cpat_top = '1'";
    }
    if(isset($newinc) && $newinc == '1'){
        $articlewhere .= " and cpat_news = '1'";
    }
    if(!isset($order)){
        $order = "Order by cpat_topinc DESC,cpat_weight DESC,cpat_releasetime DESC";
    }

    if(isset($num)){
        $order .= " limit 0,{$num}";
    }else{
        $order .= " limit 0,10";
    }

    $articleList = $viewControl->getList("cms_article",$articlewhere,$order);
    if($articleList){
        $smarty->assign($listName, $articleList);
    }else{
        $smarty->assign($listName, false);
    }
    return $content;
}
$smarty->registerPlugin("block",'articlemenuList','articlemenuList');


/**广告列表获取**/
function mediaGetList($params,$content,&$smarty)
{
    global $viewControl;
    extract($params);
    $datawhere = "1";

    if(isset($place_id) && $place_id !== ''){
        $datawhere .= " and place_id = '{$place_id}'";
    }
    if(!isset($order)){
        $order = "Order by list_weight ASC";
    }

    if(isset($num)){
        $order .= " limit 0,{$num}";
    }else{
        $order .= " limit 0,10";
    }

    $dataList = $viewControl->getList("cms_medialist",$datawhere,$order);
    if($dataList){
        $smarty->assign($listName, $dataList);
    }else{
        $smarty->assign($listName, false);
    }
    return $content;
}
$smarty->registerPlugin("block",'mediaGetList','mediaGetList');

/**产品目录获取**/
function proCatalogList($params,$content,&$smarty)
{
    global $viewControl;
    extract($params);
    $datawhere = "1";

    if(isset($procatalog_type) && $procatalog_type !== ''){
        $datawhere .= " and procatalog_type = '{$procatalog_type}'";
    }
    if(isset($father_id) && $father_id !== ''){
        $datawhere .= " and father_id = '{$father_id}'";
    }
    if(!isset($order)){
        $order = "Order by procatalog_sort ASC";
    }

    if(isset($num)){
        $order .= " limit 0,{$num}";
    }else{
        $order .= " limit 0,10";
    }

    $dataList = $viewControl->getList("aps_procatalog",$datawhere,$order);
    if($dataList){
        $dataArray = array();
        foreach($dataList as $dataVar){
            $dataVar['twoList'] = $viewControl->getList("aps_procatalog","father_id = '{$dataVar['procatalog_id']}'",$order);
            $dataArray[] = $dataVar;
        }
        $smarty->assign($listName, $dataArray);
    }else{
        $smarty->assign($listName, false);
    }
    return $content;
}
$smarty->registerPlugin("block",'proCatalogList','proCatalogList');




//项目模板函数
function itemList($params,$content,&$smarty)
{
    global $viewControl;
    extract($params);
    $datawhere = "item_status = '1'";

    if(isset($rominc) && $rominc == '1'){
        $datawhere .= " and item_rominc = '1'";
    }

    if(!isset($order)){
        $order = "Order by item_rominc DESC,item_time DESC";
    }

    if(isset($num)){
        $order .= " limit 0,{$num}";
    }else{
        $order .= " limit 0,10";
    }

    $model = new \Model\BaseModel();
    $dataList = $viewControl->getList("aps_item",$datawhere,$order);
    if($dataList){
        foreach($dataList as &$dataVar){
            $dataVar['timeOnly'] = $model->timeOnly($dataVar['item_etime']);
            $dataVar['timeDiff'] = $model->timeChange($dataVar['item_time']);
        }

        $smarty->assign($listName, $dataList);
    }else{
        $smarty->assign($listName, false);
    }
    return $content;
}
$smarty->registerPlugin("block",'itemList','itemList');

//活动模板函数
function activityList($params,$content,&$smarty)
{
    global $viewControl;
    extract($params);
    $datawhere = "1";

    if(isset($rominc) && $rominc == '1'){
        $datawhere .= " and activity_recommend = '1'";
    }

    if(isset($oldinc) && $oldinc == '1'){
        $time = time();
        $datawhere .= " and activity_hosttime < {$time}";
    }

    if(!isset($order)){
        $order = "Order by activity_recommend DESC,activity_hosttime DESC";
    }

    if(isset($num)){
        $order .= " limit 0,{$num}";
    }else{
        $order .= " limit 0,10";
    }

    $dataList = $viewControl->getList("aps_activity",$datawhere,$order);
    if($dataList){
        $smarty->assign($listName, $dataList);
    }else{
        $smarty->assign($listName, false);
    }
    return $content;
}
$smarty->registerPlugin("block",'activityList','activityList');


//新闻资讯模板函数
function newsList($params,$content,&$smarty)
{
    global $viewControl;
    extract($params);
    $datawhere = "1 and cpat_class<>6";

    //不指定则默认无6分类（成功案例）
    if(!isset($class)){
        $datawhere .= " and cpat_class <> 6";
    }else{
        $datawhere .= " and cpat_class = {$class}";
    }

    //推荐--首页
    if(isset($rominc) && $rominc == '1'){
        $datawhere .= " and cpat_recommendinc = '1'";
    }

    //置顶
    if(isset($topinc) && $topinc == '1'){
        $datawhere .= " and cpat_topinc = '1'";
    }

    //最新--内页
    if(isset($newinc) && $newinc == '1'){
        $datawhere .= " and cpat_newsinc = '1'";
    }

    if(!isset($order)){
        $order = "Order by cpat_recommendinc DESC,cpat_addtime DESC";
    }

    if(isset($num)){
        $order .= " limit 0,{$num}";
    }else{
        $order .= " limit 0,10";
    }

    $dataList = $viewControl->getList("cms_article",$datawhere,$order);
    if($dataList){
        $smarty->assign($listName, $dataList);
    }else{
        $smarty->assign($listName, false);
    }
    return $content;
}
$smarty->registerPlugin("block",'newsList','newsList');

function HideStaring($params) {
    extract($params);
    if (strpos($str, '@')) {
        $email_array = explode("@", $str);
        $prevfix = (strlen($email_array[0]) < 4) ? "" : substr($str, 0, 3);
        $count = 0;
        $str = preg_replace('/([\d\w+_-]{0,100})@/', '***@', $str, -1, $count);
        $rs = $prevfix . $str;
    } else {
        if (preg_match("/^1[3456789]{1}\d{9}$/", $str)) {
            //$rs = preg_replace($pattern, '$1****$2', $str); // substr_replace($name,'****',3,4);
            $rs = substr($str, 0, 3) . "****" . substr($str, -4);
        } else {
            $rs = substr($str, 0, 5) . "***" . substr($str, -4);
        }
    }
    echo $rs;
}
$smarty->registerPlugin("function",'HideStaring','HideStaring');
?>
