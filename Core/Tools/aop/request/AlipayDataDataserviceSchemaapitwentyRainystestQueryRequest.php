<?php
/**
 * ALIPAY API: alipay.data.dataservice.schemaapitwenty.rainystest.query request
 *
 * <AUTHOR> create
 * @since 1.0, 2025-04-23 18:29:48
 */
class AlipayDataDataserviceSchemaapitwentyRainystestQueryRequest
{
	/** 
	 * 测试数据，强引用复杂类型示例
	 **/
	private $demoStrongRef;
	
	/** 
	 * 测试数据，弱引用复杂类型示例
	 **/
	private $demoWinkRef;

	private $apiParas = array();
	private $terminalType;
	private $terminalInfo;
	private $prodCode;
	private $apiVersion="1.0";
	private $notifyUrl;
	private $returnUrl;
    private $needEncrypt=false;

	
	public function setDemoStrongRef($demoStrongRef)
	{
		$this->demoStrongRef = $demoStrongRef;
		$this->apiParas["demo_strong_ref"] = $demoStrongRef;
	}

	public function getDemoStrongRef()
	{
		return $this->demoStrongRef;
	}

	public function setDemoWinkRef($demoWinkRef)
	{
		$this->demoWinkRef = $demoWinkRef;
		$this->apiParas["demo_wink_ref"] = $demoWinkRef;
	}

	public function getDemoWinkRef()
	{
		return $this->demoWinkRef;
	}

	public function getApiMethodName()
	{
		return "alipay.data.dataservice.schemaapitwenty.rainystest.query";
	}

	public function setNotifyUrl($notifyUrl)
	{
		$this->notifyUrl=$notifyUrl;
	}

	public function getNotifyUrl()
	{
		return $this->notifyUrl;
	}

	public function setReturnUrl($returnUrl)
	{
		$this->returnUrl=$returnUrl;
	}

	public function getReturnUrl()
	{
		return $this->returnUrl;
	}

	public function getApiParas()
	{
		return $this->apiParas;
	}

	public function getTerminalType()
	{
		return $this->terminalType;
	}

	public function setTerminalType($terminalType)
	{
		$this->terminalType = $terminalType;
	}

	public function getTerminalInfo()
	{
		return $this->terminalInfo;
	}

	public function setTerminalInfo($terminalInfo)
	{
		$this->terminalInfo = $terminalInfo;
	}

	public function getProdCode()
	{
		return $this->prodCode;
	}

	public function setProdCode($prodCode)
	{
		$this->prodCode = $prodCode;
	}

	public function setApiVersion($apiVersion)
	{
		$this->apiVersion=$apiVersion;
	}

	public function getApiVersion()
	{
		return $this->apiVersion;
	}

  public function setNeedEncrypt($needEncrypt)
  {

     $this->needEncrypt=$needEncrypt;

  }

  public function getNeedEncrypt()
  {
    return $this->needEncrypt;
  }

}
