import os
def findAllFiles():
    '''
    '''
    f = ['.pydevproject', '.buildpath', '.project', 'org.eclipse.php.core.prefs', 'deploy.py']
    fl = []
    dir = os.path.abspath('..')
    for root, dirs, files in os.walk(dir):
        for file in files:
            if(file not in f):
                fl.append(os.path.join(root, file))
    return fl

def scpFile():
    '''
    '''
    cmd = "sudo scp %s zhangxinxue@*************:%s"
    flist = findAllFiles();
    for flist in flist:
        print(cmd % (flist, flist.replace('/var/www/','/srv/')))
        os.system( cmd % (flist, flist.replace('/var/www/','/srv/')));
scpFile()
        