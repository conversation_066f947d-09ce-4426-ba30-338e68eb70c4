import os

def findAllFiles():
    '''
    '''
    f = ['weChat.js', 'index.js', 'wxeval.php']
    fl = []
    dir = os.path.abspath('..')
    for root, dirs, files in os.walk(dir):
        for file in files:
            if(file in f):
                fl.append(os.path.join(root, file))
    return fl

def scpFile():
    '''
    '''
    cmd = "sudo scp %s zhangxinxue@106.14.138.84:%s"
    flist = findAllFiles();
    for flist in flist:
        print(cmd % (flist, flist.replace('/var/www/','/srv/')))
        os.system( cmd % (flist, flist.replace('/var/www/','/srv/')))
scpFile()
        