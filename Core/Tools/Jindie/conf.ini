﻿[config]
; 第三方系统登录授权的账套ID
X-KDApi-AcctID = 662f2b1b94f31a
; 第三方系统登录授权的用户
X-KDApi-UserName = zjb
; 第三方系统登录授权的密码
X-KDApi-Password = 123.www.jdb.mohism
; 第三方系统登录授权的应用ID
X-KDApi-AppID = 401025_55dr1dHoVPA4xfTPX6wCU7XuSNTYQtlH
; 第三方系统登录授权的应用密钥
X-KDApi-AppSec = 4747c618f07b4330b8e3020c71ccc981
; 请求的产品环境地址，如果为公有云则无需填写
X-KDApi-ServerUrl = https://k3.kidcastle.com.cn/k3cloud/
; 账套语系，默认2052
 X-KDApi-LCID = 2052
; 组织编码，启用多组织时配置对应的组织编码才有效
; X-KDApi-OrgNum = 100
; 允许的最大连接延时，单位为秒
 X-KDApi-ConnectTimeout = 360
; 允许的最大读取延时，单位为秒
 X-KDApi-RequestTimeout = 360
; 若使用代理，配置此参数
; X-KDApi-Proxy = http://localhost:8888/

