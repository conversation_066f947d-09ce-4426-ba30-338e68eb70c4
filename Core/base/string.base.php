<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn 
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/1/8
 * Time: 23:59
 */
/**
 * 截取UTF-8编码下字符串的函数
 *
 * @param   string      $str        被截取的字符串
 * @param   int         $length     截取的长度
 * @param   bool        $append     是否附加省略号
 *
 * @return  string
 */
function sub_str($str, $length = 0, $append = true)
{
    $str = trim($str);
    $strlength = strlen($str);

    if ($length == 0 || $length >= $strlength)
    {
        return $str;
    }
    elseif ($length < 0)
    {
        $length = $strlength + $length;
        if ($length < 0)
        {
            $length = $strlength;
        }
    }

    if (function_exists('mb_substr'))
    {
        $newstr = mb_substr($str, 0, $length, MHS_CODING);
    }
    elseif (function_exists('iconv_substr'))
    {
        $newstr = iconv_substr($str, 0, $length, MHS_CODING);
    }
    else
    {
        //$newstr = trim_right(substr($str, 0, $length));
        $newstr = substr($str, 0, $length);
    }

    if ($append && $str != $newstr)
    {
        $newstr .= '...';
    }

    return $newstr;
}


//utf8 字符串截取
function msubstr($str, $start=0, $length=15, $charset="utf-8", $suffix=true)
{
    if(function_exists("mb_substr"))
    {
        $slice =  mb_substr($str, $start, $length, $charset);
        if($suffix&$slice!=$str) return $slice."…";
        return $slice;
    }
    elseif(function_exists('iconv_substr')) {
        return iconv_substr($str,$start,$length,$charset);
    }
    $re['utf-8']   = "/[\x01-\x7f]|[\xc2-\xdf][\x80-\xbf]|[\xe0-\xef][\x80-\xbf]{2}|[\xf0-\xff][\x80-\xbf]{3}/";
    $re['gb2312'] = "/[\x01-\x7f]|[\xb0-\xf7][\xa0-\xfe]/";
    $re['gbk']    = "/[\x01-\x7f]|[\x81-\xfe][\x40-\xfe]/";
    $re['big5']   = "/[\x01-\x7f]|[\x81-\xfe]([\x40-\x7e]|\xa1-\xfe])/";
    preg_match_all($re[$charset], $str, $match);
    $slice = join("",array_slice($match[0], $start, $length));
    if($suffix&&$slice!=$str) return $slice."…";
    return $slice;
}

//随机取数据某几个元素组合成字符才连接
function rand_array_to_str($formarray,$nums){
    $keyarray = array_rand($formarray,$nums);
    foreach($keyarray as $k=>$v){
        $idlist[]=$formarray[$v];
    }
    $string = implode(",", $idlist);

    return $string;
}

//取数据某几个元素组合成字符才连接
function array_to_str($formarray){
    foreach($formarray as $k=>$v){
        $idlist[]=$v;
    }
    $string = implode(",", $idlist);

    return $string;
}

//数据乱码转换
// $fContents 字符串
// $from 字符串的编码
// $to 要转换的编码
function auto_charset($fContents,$from='gbk',$to='utf-8'){
    $from   =  strtoupper($from)=='UTF8'? 'utf-8':$from;
    $to       =  strtoupper($to)=='UTF8'? 'utf-8':$to;
    if( strtoupper($from) === strtoupper($to) || empty($fContents) || (is_scalar($fContents) && !is_string($fContents)) ){
        //如果编码相同或者非字符串标量则不转换
        return $fContents;
    }
    if(is_string($fContents) ) {
        if(function_exists('mb_convert_encoding')){
            return mb_convert_encoding ($fContents, $to, $from);
        }else{
            return $fContents;
        }
    }
    elseif(is_array($fContents)){
        foreach ( $fContents as $key => $val ) {
            $_key =     auto_charset($key,$from,$to);
            $fContents[$_key] = auto_charset($val,$from,$to);
            if($key != $_key )
                unset($fContents[$key]);
        }
        return $fContents;
    }
    else{
        return $fContents;
    }
}


/**
 * 字符串加密函数
 * @param string $txt
 * @param string $key
 * @return string
 */
function passport_encrypt($txt, $key = 'IMEMBER_2013') {
    srand((double)microtime() * 1000000);
    $encrypt_key = md5(rand(0, 32000));
    $ctr = 0;
    $tmp = '';
    for($i = 0;$i < strlen($txt); $i++) {
        $ctr = $ctr == strlen($encrypt_key) ? 0 : $ctr;
        $tmp .= $encrypt_key[$ctr].($txt[$i] ^ $encrypt_key[$ctr++]);
    }
    return base64_encode(passport_key($tmp, $key));
}

/**
 * 字符串解密函数
 * @param string $txt
 * @param string $key
 * @return string
 */
function passport_decrypt($txt, $key = 'IMEMBER_2013') {
    $txt = passport_key(base64_decode($txt), $key);
    $tmp = '';
    for($i = 0;$i < strlen($txt); $i++) {
        if (empty($txt[$i+1])) {
            return false;
        }
        $md5 = $txt[$i];
        $tmp .= $txt[++$i] ^ $md5;
    }
    return $tmp;
}

function passport_key($txt, $encrypt_key) {
    $encrypt_key = md5($encrypt_key);
    $ctr = 0;
    $tmp = '';
    for($i = 0; $i < strlen($txt); $i++) {
        $ctr = $ctr == strlen($encrypt_key) ? 0 : $ctr;
        $tmp .= $txt[$i] ^ $encrypt_key[$ctr++];
    }
    return $tmp;
}

/**
 * 固定字符分割字符串
 */
function strsToArray($strs) {
    $result = array();
    $array = array();
    $strs = str_replace('，', ',', $strs);
    $strs = str_replace(' ', ',', $strs);
    $array = explode(',', $strs);
    foreach ($array as $key => $value) {
        if ('' != ($value = trim($value))) {
            $result[] = $value;
        }
    }
    return $result;
}

/**
 * 得到一个字符串中的某一部分
 * @param $sourceStr 源数据
 * @param $startStr 分离部分的开始标记
 * @param $endStart 分离部分的结束标记
 * @return boolean  操作成功返回true
 */

function getContent($sourceStr,$startStr,$endStart)
{
    $s = preg_quote($startStr);
    $e = preg_quote($endStart);
    $s = str_replace(" ","[[:space:]]",$s);
    $e = str_replace(" ","[[:space:]]",$e);
    $s = str_replace("\r\n","[[:cntrl:]]",$s);
    $e = str_replace("\r\n","[[:cntrl:]]",$e);
    preg_match_all("@".$s ."(.*?)".$e."@is",$sourceStr,$tpl);

    $content = $tpl[1];
    $content = implode("",$content);
    return $content;
}

/**
 * 得到一个字符串中的某一部分
 * @param $sourceStr 源数据
 * @param $startStr 分离部分的开始标记
 * @param $endStart 分离部分的结束标记
 * @return boolean  操作成功返回true
 */

function getContentarray($sourceStr,$startStr,$endStart)
{
    $s = preg_quote($startStr);
    $e = preg_quote($endStart);
    $s = str_replace(" ","[[:space:]]",$s);
    $e = str_replace(" ","[[:space:]]",$e);
    $s = str_replace("\r\n","[[:cntrl:]]",$s);
    $e = str_replace("\r\n","[[:cntrl:]]",$e);
    preg_match_all("@".$s ."(.*?)".$e."@is",$sourceStr,$tpl);
    $content = $tpl[1];
    //$content = implode("",$content);
    return $content;
}

//id数字处理
function Array_id_tostring($idarray,$idstr){
    if($idarray){
        foreach($idarray as $idarrayvar){
            $arrayid[] = $idarrayvar[$idstr];
        }
        $idarray = array_unique($arrayid);
        $inlist = implode(",", $idarray);
    }
    return $inlist;
}

/*取汉子首字母*/
function getfirstchar($s0){
  $firstchar_ord=ord(strtoupper($s0[0]));
  if (($firstchar_ord>=65 and $firstchar_ord<=91)or($firstchar_ord>=48 and $firstchar_ord<=57)) return $s0[0];
  $s=iconv("UTF-8","gb2312", $s0);
  $asc=ord($s[0])*256+ord($s[1])-65536;
  if($asc>=-20319 and $asc<=-20284)return "A";
  if($asc>=-20283 and $asc<=-19776)return "B";
  if($asc>=-19775 and $asc<=-19219)return "C";
  if($asc>=-19218 and $asc<=-18711)return "D";
  if($asc>=-18710 and $asc<=-18527)return "E";
  if($asc>=-18526 and $asc<=-18240)return "F";
  if($asc>=-18239 and $asc<=-17923)return "G";
  if($asc>=-17922 and $asc<=-17418)return "H";
  if($asc>=-17417 and $asc<=-16475)return "J";
  if($asc>=-16474 and $asc<=-16213)return "K";
  if($asc>=-16212 and $asc<=-15641)return "L";
  if($asc>=-15640 and $asc<=-15166)return "M";
  if($asc>=-15165 and $asc<=-14923)return "N";
  if($asc>=-14922 and $asc<=-14915)return "O";
  if($asc>=-14914 and $asc<=-14631)return "P";
  if($asc>=-14630 and $asc<=-14150)return "Q";
  if($asc>=-14149 and $asc<=-14091)return "R";
  if($asc>=-14090 and $asc<=-13319)return "S";
  if($asc>=-13318 and $asc<=-12839)return "T";
  if($asc>=-12838 and $asc<=-12557)return "W";
  if($asc>=-12556 and $asc<=-11848)return "X";
  if($asc>=-11847 and $asc<=-11056)return "Y";
  if($asc>=-11055 and $asc<=-10247)return "Z";
  return '';
}

/**
 * 根据HTML代码获取word文档内容
 * 创建一个本质为mht的文档，该函数会分析文件内容并从远程下载页面中的图片资源
 * 该函数依赖于类MhtFileMaker
 * 该函数会分析img标签，提取src的属性值。但是，src的属性值必须被引号包围，否则不能提取
 *
 * @param string $content HTML内容
 * @param string $absolutePath 网页的绝对路径。如果HTML内容里的图片路径为相对路径，那么就需要填写这个参数，来让该函数自动填补成绝对路径。这个参数最后需要以/结束
 * @param bool $isEraseLink 是否去掉HTML内容中的链接
 */
function getWordDocument( $content , $absolutePath = "" , $isEraseLink = true )
{
    $mht = new \MhtFileMaker();
    if ($isEraseLink)
        $content = preg_replace('/<a\s*.*?\s*>(\s*.*?\s*)<\/a>/i' , '$1' , $content);   //去掉链接

    $images = array();
    $files = array();
    $matches = array();
    //这个算法要求src后的属性值必须使用引号括起来
    if ( preg_match_all('/<img[.\n]*?src\s*?=\s*?[\"\'](.*?)[\"\'](.*?)\/>/i',$content ,$matches ) )
    {
        $arrPath = $matches[1];
        for ( $i=0;$i<count($arrPath);$i++)
        {
            $path = $arrPath[$i];
            $imgPath = trim( $path );
            if ( $imgPath != "" )
            {
                $files[] = $imgPath;
                if( substr($imgPath,0,7) == 'http://')
                {
                    //绝对链接，不加前缀
                }
                else
                {
                    $imgPath = $absolutePath.$imgPath;
                }
                $images[] = $imgPath;
            }
        }
    }
    $mht->AddContents("tmp.html",$mht->GetMimeType("tmp.html"),$content);

    for ( $i=0;$i<count($images);$i++)
    {
        $image = $images[$i];
        if ( @fopen($image , 'r') )
        {
            $imgcontent = @file_get_contents( $image );
            if ( $content )
                $mht->AddContents($files[$i],$mht->GetMimeType($image),$imgcontent);
        }
        else
        {
            echo "file:".$image." not exist!<br />";
        }
    }

    return $mht->GetFile();
}

//验证手机号码
function checkMobile($strMobile)
{
//    $pattern = "/^1[3456789]\d{9}$/";
    $pattern = "/^[1][2-9]\d{9}$|^([6|9])\d{7}$|^[0][9]\d{8}$|^[6]([8|6])\d{5}$/";
    if (preg_match($pattern, $strMobile)){
        return true;
    }else{
        return false;
    }
}

function logPrint($errtipLog){
    echo "<!--";
    print_r($errtipLog);
    echo "-->";
}


//姓名脱敏操作
function checkComplexPass($str)
{
    $nowstr = '';
    if (preg_match("/^[a-zA-Z0-9]+$/", $str)){
        $nowstr = substr_replace($str,"**",1);
    }elseif(mb_strlen($str) == '2'){
        $nowstr = mb_substr($str, 0, 1)."*";
    }elseif(mb_strlen($str) == '3'){
        $nowstr = mb_substr($str, 0, 1)."*".mb_substr($str, -1, 1);
    }else{
        $nowstr = mb_substr($str, 0, 2)."**".mb_substr($str, -1, 1);
    }
    return $nowstr;
}