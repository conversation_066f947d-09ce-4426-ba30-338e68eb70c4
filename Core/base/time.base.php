<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn 
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2016/12/13
 * Time: 17:04
 */

/**根据时间返回时间戳**/
function getVarToTime($timevar)
{
    $timego = explode(" ",$timevar);
    $timen = explode("-",$timego[0]);
    if(!isset($timego[1])){
        $timef = array('0','0','0');
    }else{
        $timef = explode(":",$timego[1]);
    }
    $times = mktime($timef[0],$timef[1],$timef[2],$timen[1],$timen[2],$timen[0]);
    return $times;
}
//月份第一天和最后一天
function GetTheMonth($date)
{
    $firstday = date('Y-m-01', strtotime($date));
    $lastday = date('Y-m-d', strtotime("$firstday +1 month -1 day"));
    return array($firstday,$lastday);
}

//月份第一天和最后一天
function GetMonthWorkday($date)
{
    $firstday = date('Y-m-01', strtotime($date));
    $lastday = date('Y-m-d', strtotime("$firstday +1 month -1 day"));
    $worknums = 0;
    for($i=0;$i<=31;$i++) {
        $dayTimes = (strtotime($firstday) + 3600 * 24 * $i);
        $dayDate = date("Y-m-d",(strtotime($firstday) + 3600 * 24 * $i));
        if($dayDate <= $lastday){
            $todayweek = date('N', $dayTimes);
            if($todayweek < 6){
                $worknums ++;
            }
        }
    }

    return $worknums;
}

/**
 * 获取指定日期对应星座
 *
 * @param integer $month 月份 1-12
 * @param integer $day 日期 1-31
 * @return boolean|string
 */
function getConstellation($month, $day)
{
    // 检查参数有效性
    if ($month < 1 || $month > 12 || $day < 1 || $day > 31) return false;

    // 星座名称以及开始日期
    $constellations = array(
        array( "20" => "宝瓶座"),
        array( "19" => "双鱼座"),
        array( "21" => "白羊座"),
        array( "20" => "金牛座"),
        array( "21" => "双子座"),
        array( "22" => "巨蟹座"),
        array( "23" => "狮子座"),
        array( "23" => "处女座"),
        array( "23" => "天秤座"),
        array( "24" => "天蝎座"),
        array( "22" => "射手座"),
        array( "22" => "摩羯座")
    );

    list($constellation_start, $constellation_name) = each($constellations[(int)$month-1]);

    if ($day < $constellation_start) list($constellation_start, $constellation_name) = each($constellations[($month -2 < 0) ? $month = 11: $month -= 2]);

    return $constellation_name;
}


//生日转年龄
function birthdaytoage($birthday){
    $birthday = str_replace("年","-",$birthday);
    $birthday = str_replace("月","-",$birthday);
    $birthday = str_replace("日","",$birthday);
    $age = strtotime($birthday);
    if($age === false){
        return 0;
    }

    list($y1,$m1,$d1) = explode("-",date("Y-m-d",$age));
    $now = strtotime("now");
    list($y2,$m2,$d2) = explode("-",date("Y-m-d",$now));
    $age = $y2 - $y1;
    if((int)($m2.$d2) < (int)($m1.$d1))
        $age -= 1;
    return $age;
}

//时间差计算
function WaitingTime( $begin_time, $end_time )
{
    if ( $begin_time < $end_time ) {
        $starttime = $begin_time;
        $endtime = $end_time;
    } else {
        $starttime = $end_time;
        $endtime = $begin_time;
    }
    $timediff = $endtime - $starttime;
    $days = intval( $timediff / 86400 );
    $remain = $timediff % 86400;
    $hours = intval( $remain / 3600 );
    $remain = $remain % 3600;
    $mins = intval( $remain / 60 );
    $secs = $remain % 60;
    $res = array( "day" => $days, "hour" => $hours, "min" => $mins, "sec" => $secs );
    return $res;
}

/**时间提示函数**/
function TimeTipInfo($times){
    $hour = date('G',$times);
    switch($hour){
        case $hour >= 0 && $hour < 6 : $tip = "深夜了，身体重要，请注意休息！";break;
        case $hour >= 6 && $hour < 8 : $tip = "早上好，一日之计在于晨，锻炼一下吧！";break;
        case $hour >= 8 && $hour < 12 : $tip = "上午好，喝杯咖啡，为吉的堡努力！";break;
        case $hour >= 12 && $hour < 14 : $tip = "中午好，吃好午餐，下午继续努力！";break;
        case $hour >= 14 && $hour < 19 : $tip = "下午好，起来运动一下，认真工作！";break;
        case $hour >= 19 && $hour < 22 : $tip = "晚上好，您是否在加班了，记得喝水哦！";break;
        case $hour >= 22 && $hour < 24 : $tip = "深夜了，您是否该休息了！";break;
    }
    return $tip;
}

function GetMonth($day,$sign="1")
{
    //得到系统的年月
    $tmp_date=date("Ym",strtotime($day));
    //切割出年份
    $tmp_year=substr($tmp_date,0,4);
    //切割出月份
    $tmp_mon =substr($tmp_date,4,2);
    $tmp_nextmonth=mktime(0,0,0,$tmp_mon+1,1,$tmp_year);
    $tmp_forwardmonth=mktime(0,0,0,$tmp_mon-1,1,$tmp_year);
    if($sign==0){
        //得到当前月的下一个月
        return $fm_next_month=date("Y-m",$tmp_nextmonth);
    }else{
        //得到当前月的上一个月
        return $fm_forward_month=date("Y-m",$tmp_forwardmonth);
    }
}

/**得出某一时间上下周**/
function GetWeekAll($day)
{
    $date = $day;  //当前日期
    $first=1; //$first =1 表示每周星期一为开始日期 0表示每周日为开始日期
    $w=date('w',strtotime($date));  //获取当前周的第几天 周日是 0 周一到周六是 1 - 6

    $weekday = array();

    $weekday['nowweek_start'] = date('Y-m-d',strtotime("$date -".($w ? $w - $first : 6).' days')); //获取本周开始日期，如果$w是0，则表示周日，减去 6 天
    $weekday['nowweek_end'] = date('Y-m-d',strtotime("{$weekday['nowweek_start']} +6 days"));  //本周结束日期
    $weekday['nowweek_cnname'] = date('Y年m月d',strtotime("$date -".($w ? $w - $first : 6).' days')) . '-'.date('d日',strtotime("{$weekday['nowweek_start']} +6 days"));  //本周中文描述

    $weekday['lastweek_start'] = date('Y-m-d',strtotime("{$weekday['nowweek_start']} - 7 days"));  //上周开始日期
    $weekday['lastweek_end'] = date('Y-m-d',strtotime("{$weekday['nowweek_start']} - 1 days"));  //上周结束日期

    $weekday['nextweek_start'] = date('Y-m-d',strtotime("{$weekday['nowweek_start']} + 7 days"));  //下周开始时间
    $weekday['nextweek_end'] = date('Y-m-d',strtotime("{$weekday['nowweek_start']} + 13 days"));  //下周结束日期

    return $weekday;
}

function is_time_cross($beginTime1 = '', $endTime1 = '', $beginTime2 = '', $endTime2 = '') {
    $status = $beginTime2 - $beginTime1;
    if ($status > 0) {
        $status2 = $beginTime2 - $endTime1;
        if ($status2 >= 0) {
            return false;
        } else {
            return true;
        }
    } else {
        $status2 = $endTime2 - $beginTime1;
        if ($status2 > 0) {
            return true;
        } else {
            return false;
        }
    }
}
/**时间处理**/
function get_stingtotime($timevar)
{
    $timego = explode(" ",$timevar);
    $timen = explode("/",$timego[0]);
    if(!isset($timego[1])){
        $timef = array('0','0','0');
    }else{
        $timef = explode(":",$timego[1]);
    }
    $times = mktime($timef[0],$timef[1],$timef[2],$timen[1],$timen[2],$timen[0]);
    return $times;
}

/**时间处理函数**/
function getDayCnWeek($daytimes){
    $weet = date('w',$daytimes);
    switch($weet){
        case "1": $wet = "星期一";break;
        case "2": $wet = "星期二";break;
        case "3": $wet = "星期三";break;
        case "4": $wet = "星期四";break;
        case "5": $wet = "星期五";break;
        case "6": $wet = "星期六";break;
        case "0": $wet = "星期日";break;
    }
    return $wet;
}

/**时间处理函数**/
function getDayshotCnWeek($daytimes){
    $weet = date('w',$daytimes);
    switch($weet){
        case "1": $wet = "周一";break;
        case "2": $wet = "周二";break;
        case "3": $wet = "周三";break;
        case "4": $wet = "周四";break;
        case "5": $wet = "周五";break;
        case "6": $wet = "周六";break;
        case "0": $wet = "周日";break;
    }
    return $wet;
}

function getmicrotime(){
    list($usec, $sec) = explode(" ",microtime());
    return ((float)$usec + (float)$sec);
}

function timeSpacing($time)
{
    $spacing = time()-$time;
    switch($spacing){
        case $spacing <= 60: $spacingTime = "1分钟内";break;
        case $spacing <= 300 and $spacing > 60: $spacingTime = "5分钟内";break;
        case $spacing <= 600 and $spacing > 300: $spacingTime = "10分钟内";break;
        case $spacing <= 3600 and $spacing > 600: $spacingTime = "一小时内";break;
        case $spacing <= 3600*2 and $spacing > 3600: $spacingTime = "二小时内";break;
        case $spacing <= 3600*24 and $spacing > 3600*2: $spacingTime = "一天内";break;
        case $spacing <= 3600*24*30 and $spacing > 3600*24: $spacingTime = "一个月内";break;
        case $spacing > 3600*24*30: $spacingTime = "一个月前";break;
    }

    return $spacingTime;
}