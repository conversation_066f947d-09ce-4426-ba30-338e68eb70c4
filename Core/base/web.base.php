<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2016/12/13
 * Time: 17:04
 */

/**
 * 获得 当前环境的 HTTP 协议方式
 *
 * @access  public
 *
 * @return  void
 */
function http()
{
    return (isset($_SERVER['HTTPS']) && (strtolower($_SERVER['HTTPS']) != 'off')) ? 'https://' : 'http://';
}

/**
 * 取得当前的域名
 *
 * @access  public
 *
 * @return  string      当前的域名
 */
function get_domain()
{
    /* 协议 */
    $protocol = http();

    /* 域名或IP地址 */
    if (isset($_SERVER['HTTP_X_FORWARDED_HOST']))
    {
        $host = $_SERVER['HTTP_X_FORWARDED_HOST'];
    }
    elseif (isset($_SERVER['HTTP_HOST']))
    {
        $host = $_SERVER['HTTP_HOST'];
    }
    else
    {
        /* 端口 */
        if (isset($_SERVER['SERVER_PORT']))
        {
            $port = ':' . $_SERVER['SERVER_PORT'];

            if ((':80' == $port && 'http://' == $protocol) || (':443' == $port && 'https://' == $protocol))
            {
                $port = '';
            }
        }
        else
        {
            $port = '';
        }

        if (isset($_SERVER['SERVER_NAME']))
        {
            $host = $_SERVER['SERVER_NAME'] . $port;
        }
        elseif (isset($_SERVER['SERVER_ADDR']))
        {
            $host = $_SERVER['SERVER_ADDR'] . $port;
        }
    }

    return $protocol . $host;
}



/**
 * 获得用户的真实IP地址
 *
 * @access  public
 * @return  string
 */
function real_ip()
{
    static $realip = NULL;

    if ($realip !== NULL)
    {
        return $realip;
    }

    if (isset($_SERVER))
    {
        if (isset($_SERVER['HTTP_X_FORWARDED_FOR']))
        {
            $arr = explode(',', $_SERVER['HTTP_X_FORWARDED_FOR']);

            /* 取X-Forwarded-For中第一个非unknown的有效IP字符串 */
            foreach ($arr AS $ip)
            {
                $ip = trim($ip);

                if ($ip != 'unknown')
                {
                    $realip = $ip;

                    break;
                }
            }
        }
        elseif (isset($_SERVER['HTTP_CLIENT_IP']))
        {
            $realip = $_SERVER['HTTP_CLIENT_IP'];
        }
        else
        {
            if (isset($_SERVER['REMOTE_ADDR']))
            {
                $realip = $_SERVER['REMOTE_ADDR'];
            }
            else
            {
                $realip = '0.0.0.0';
            }
        }
    }
    else
    {
        if (getenv('HTTP_X_FORWARDED_FOR'))
        {
            $realip = getenv('HTTP_X_FORWARDED_FOR');
        }
        elseif (getenv('HTTP_CLIENT_IP'))
        {
            $realip = getenv('HTTP_CLIENT_IP');
        }
        else
        {
            $realip = getenv('REMOTE_ADDR');
        }
    }

    preg_match("/[\d\.]{7,15}/", $realip, $onlineip);
    $realip = !empty($onlineip[0]) ? $onlineip[0] : '0.0.0.0';

    return $realip;
}

/**
 * Similar to PHP's builtin parse_url() function, but makes sure what the schema,
 * path and port keys are set to http, /, 80 respectively if they're missing
 *
 * @access     private
 * @param      string    $raw_url    Raw URL to be split into an array
 * <AUTHOR>
 * @return     array
 */
function parse_raw_url($raw_url)
{
    $retval   = array();
    $raw_url  = (string) $raw_url;

    // make sure parse_url() recognizes the URL correctly.
    if (strpos($raw_url, '://') === false)
    {
        $raw_url = 'http://' . $raw_url;
    }

    // split request into array
    $retval = parse_url($raw_url);

    // make sure a path key exists
    if (!isset($retval['path']))
    {
        $retval['path'] = '/';
    }

    // set port to 80 if none exists
    if (!isset($retval['port']))
    {
        $retval['port'] = '80';
    }

    return $retval;
}

/**
 * 使用curl进行连接
 *
 * @access  private
 * @param   string      $url            远程服务器的URL
 * @param   string      $params         查询参数，形如bar=foo&foo=bar
 * @param   string      $method         请求方式，是POST还是GET
 * @param   array       $my_header      用户要发送的头部信息，为一维关联数组，形如array('a'=>'aa',...)
 * @return  array                       成功返回一维关联数组，形如array('header'=>'bar', 'body'=>'foo')，
 *                                      失败返回false。
 */
function request_by_curl($url, $params, $method, $my_header= array())
{
    /* 开始一个新会话 */
    $curl_session = curl_init();

    /* 基本设置 */
    curl_setopt($curl_session, CURLOPT_FORBID_REUSE, true); // 处理完后，关闭连接，释放资源
    curl_setopt($curl_session, CURLOPT_HEADER, true);//结果中包含头部信息
    curl_setopt($curl_session, CURLOPT_RETURNTRANSFER, true);//把结果返回，而非直接输出
    curl_setopt($curl_session, CURLOPT_TIMEOUT, 20);
    curl_setopt($curl_session, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_0);//采用1.0版的HTTP协议

    $url_parts = parse_raw_url($url);

    /* 设置验证策略 */
    if (!empty($url_parts['user']))
    {
        $auth = $url_parts['user'] . ':' . $url_parts['pass'];
        curl_setopt($curl_session, CURLOPT_USERPWD, $auth);
        curl_setopt($curl_session, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
    }

    $header = array();

    /* 设置主机 */
    $header[] = 'Host: ' . $url_parts['host'];

    /* 格式化自定义头部信息 */
    if ($my_header && is_array($my_header))
    {
        foreach ($my_header as $key => $value)
        {
            $header[] = $key . ': ' . $value;
        }
    }

    if ($method === 'GET')
    {
        curl_setopt($curl_session, CURLOPT_HTTPGET, true);
        $url .= $params ? '?' . $params : '';
    }
    elseif ($method === 'FILE')
    {
        curl_setopt($curl_session, CURLOPT_POST, true);
        curl_setopt($curl_session, CURLOPT_POSTFIELDS, $params);
    }
    else
    {
        curl_setopt($curl_session, CURLOPT_POST, true);
        $header[] = 'Content-Type: application/x-www-form-urlencoded;charset=UTF-8';
        $header[] = 'Content-Length: ' . strlen($params);
        curl_setopt($curl_session, CURLOPT_POSTFIELDS, $params);
    }

    /* 设置请求地址 */
    curl_setopt($curl_session, CURLOPT_URL, $url);

    /* 设置头部信息 */
    curl_setopt($curl_session, CURLOPT_HTTPHEADER, $header);
    curl_setopt($curl_session,CURLOPT_SSL_VERIFYPEER,FALSE);
    curl_setopt($curl_session,CURLOPT_SSL_VERIFYHOST,FALSE);//严格校验

    /* 发送请求 */
    $http_response = curl_exec($curl_session);


    if (curl_errno($curl_session) != 0)
    {
        return false;
    }

    $separator = '/\r\n\r\n|\n\n|\r\r/';
    list($http_header, $http_body) = preg_split($separator, $http_response, 2);

    curl_close($curl_session);

    return $http_body;
}
/**
 * POST数据组合，url传递多维数组，格式化
 *
 * @internal
 * @param 数组 $data
 * @param 字符串 $keyprefix
 * @param 字符串 $keypostfix
 * @return 字符串
 */
function dataEncode($data, $keyprefix = '', $keypostfix = '')
{
    assert(is_array($data));
    $vars = '';
    foreach ($data as $key => $value)
    {
        if (TRUE == is_array($value)) $vars .= dataEncode($value, $keyprefix . $key . $keypostfix . urlencode('['), urlencode(']'));
        else $vars .= $keyprefix . $key . $keypostfix . '='.urlencode($value) . '&';
    }
    return $vars;
}


//是否Json格式
function is_not_json($str){
    if(is_null(json_decode($str))){
        return true;
    }else{
        return false;
    }
}

//获得当前的脚本网址
function get_php_url(){
    if(!empty($_SERVER["REQUEST_URI"])){
        $scriptName = $_SERVER["REQUEST_URI"];
        $nowurl = $scriptName;
    }else{
        $scriptName = $_SERVER["PHP_SELF"];
        if(empty($_SERVER["QUERY_STRING"])) $nowurl = $scriptName;
        else $nowurl = $scriptName."?".$_SERVER["QUERY_STRING"];
    }
    return $nowurl;
}

//获取图片真实路径
function img_path_get($img)
{
    if($img == ''){
        return '/mhsimg/img_logo.png';
    }else{
        if(strstr($img,'[') && strstr($img,']')){
            $json_play = new \Webjson();
            $imgarray = $json_play->decode($img,"1");
            $img = $imgarray[0];
            if(strstr($img,'http://') || strstr($img,'https://') || strstr($img,'//')){
                return $img;
            }else{
                return 'http://www.mohusou.com/'.$img;
            }
        }else{
            if(strstr($img,'http://') || strstr($img,'https://') || strstr($img,'//')){
                return $img;
            }else{
                return 'http://www.mohusou.com/'.$img;
            }
        }
    }
}


function url_exists($url)
{
    $u = parse_url($url);
    if(!$u || !isset($u['host'])) return false;
    $host = $u['host'];
    if(gethostbyname($host)!=$host){
        $h = @get_headers($url);
        if(!$h || !isset($h[0])) return false;
        $status = $h[0];
        //print_r($h);
        return preg_match("/.*200\s{1}OK/i", $status) ? true : false;
    }else return false;
}

//支持多种传值方式
function httpRequest($url, $data='', $method='GET',$header=''){
    $curl = curl_init();
    if ($method === 'GET') {
        curl_setopt($curl, CURLOPT_HTTPGET, true);
        $url .= $data ? '?' . $data : '';
    }
    curl_setopt($curl, CURLOPT_URL, $url);
    curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, 0);
    curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, 0);
    curl_setopt($curl, CURLOPT_USERAGENT, $_SERVER['HTTP_USER_AGENT']);
    curl_setopt($curl, CURLOPT_FOLLOWLOCATION, 1);
    curl_setopt($curl, CURLOPT_AUTOREFERER, 1);
    if($header!='') {//可以传递 RAW 方式下的 Josn 值
        curl_setopt($curl, CURLOPT_HTTPHEADER, $header);
    }
    if($method=='POST')
    {
        curl_setopt($curl, CURLOPT_POST, 1);
        if ($data != '')
        {
            curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
        }
    }

    curl_setopt($curl, CURLOPT_TIMEOUT, 30);
    curl_setopt($curl, CURLOPT_HEADER, 0);
    curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
    $result = curl_exec($curl);
    curl_close($curl);
    return $result;
}