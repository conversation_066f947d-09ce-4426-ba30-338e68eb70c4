<?php
/**接口类设置函数
 * ============================================================================
 * 版权所有 : http://www.mohism.cn 
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2016/12/13
 * Time: 17:44
 */



//N27088497d
//27088d83d3eb8df3
//
//


//短信发送函数111
function Sendmis($mobile,$content,$signature='课叮铛'){
    $frist = substr( $mobile, 0, 1 );
    if($frist == '1'){
//        if($signature == '课叮铛'){
//            $signid = '219543';
//        }else if($signature == '吉的堡教育'){
//            $signid = '219544';
//        }else if($signature == '吉的堡'){
//            $signid = '219545';
//        }else{
//            $signid = '219543';
//        }
//
//        $param = array(
//            'apikey' => "N27088497d", //
//            'secret' => "27088d83d3eb8df3", //
//            'mobile' => $mobile, //
//            'sign_id' => $signid, //
//            'content' => $content, //
//        );
//        $header = array();
//        $header[] = "Content-type: application/json;charset='utf-8'";
//
//        //POST参数 RAW中JSON方式传值获取结果
//        $getBackurl = httpRequest("https://api.4321.sh/sms/send", json_encode($param), "POST", $header);
//        $bakData = json_decode($getBackurl, true);
//        if($bakData['code'] == '0' && $bakData['code'] == 'OK'){
//            return true;
//        }else{
//            return false;
//        }

        //$sendbak = request_by_curl("http://api.feige.ee/SmsService/Sms","Account=kedingdang&Pwd=221a229c826bb60fd56397f4f&Mobile={$mobile}&Content=【{$signature}】{$content}","POST",array());
        $sendbak = request_by_curl("http://oldapi.4321.sh/SmsService/Sms","Account=kedingdang&Pwd=221a229c826bb60fd56397f4f&Mobile={$mobile}&Content=【{$signature}】{$content}","POST",array());
        $sendarray = json_decode($sendbak,1);
        if($sendarray['Message'] == 'OK'){
            return true;
        }else{
            return false;
        }
    }elseif($frist == '0'){
        $sbtxt = urlencode($signature);
        $content = urlencode($content);
        $sendbak = request_by_curl("http://biz.every8d.com/kidcastle/API21/HTTP/sendSMS.ashx","UID=SA64000&PWD=SA64000&SB={$sbtxt}&MSG={$content}&DEST={$mobile}&ST=","GET",array());
        $sendarray = explode(",",$sendbak);
        if($sendarray['0'] > 0){
            return true;
        }else{
            return false;
        }
    }else{
        return false;
    }
}

//短信发送函数111
function SendmisBak($mobile,$content){
    $frist = substr( $mobile, 0, 1 );
    if($frist == '1'){
//        $sendbak = request_by_curl("http://api.feige.ee/smsservice.aspx","name=kidcastlejz&pwd=d0a65f4406202bf217b122578&mobile={$mobile}&content={$content}&stime=&sign=吉的堡教育集团&type=pt&extno=","POST",array());
        $sendbak = request_by_curl("http://oldapi.4321.sh/smsservice.aspx","name=kidcastlejz&pwd=d0a65f4406202bf217b122578&mobile={$mobile}&content={$content}&stime=&sign=吉的堡教育集团&type=pt&extno=","POST",array());
        $sendarray = explode(",",$sendbak);
        if($sendarray['0'] == '0'){
            return true;
        }else{
            return false;
        }
    }elseif($frist == '0'){
        $sbtxt = urlencode("學習平台");
        $content = urlencode($content);
        $sendbak = request_by_curl("http://biz.every8d.com/kidcastle/API21/HTTP/sendSMS.ashx","UID=SA64000&PWD=SA64000&SB={$sbtxt}&MSG={$content}&DEST={$mobile}&ST=","GET",array());
        $sendarray = explode(",",$sendbak);
        if($sendarray['0'] > 0){
            return true;
        }else{
            return false;
        }
    }else{
        return false;
    }
}


//高德地图获取经纬度定位地址
function getPointsinfo($longitude,$latitude){
    $paramarray = array(
        'output' => "json",
        'location' => "{$longitude},{$latitude}",
        'key' => "cade9845bf6356370bbe6d57fa8f122c",
        'extensions' => "base"
    );
    $getMapurl = request_by_curl("http://restapi.amap.com/v3/geocode/regeo", dataEncode($paramarray),"GET");
    $json_play = new JSON();
    $PointsJson = $json_play->decode($getMapurl,"1");
    return $PointsJson;
}

//高德地图获取经纬度定位地址
function getAddressPoints($address){
    $paramarray = array(
        'output' => "json",
        'address' => $address,
        'key' => "cade9845bf6356370bbe6d57fa8f122c"
    );

    $getMapurl = request_by_curl("http://restapi.amap.com/v3/geocode/geo", dataEncode($paramarray),"GET");
    $json_play = new JSON();
    $PointsJson = $json_play->decode($getMapurl,"1");
    if($PointsJson['status'] == '1'){
        $res['status'] = 1;
        $res['message'] = "获取成功";
        $location = explode(",",$PointsJson['geocodes']['0']['location']);
        $res['longitude'] = $location['0'];
        $res['latitude'] = $location['1'];
    }else{
        $res['status'] = 0;
        $res['message'] = "获取失败";
    }

    return $res;
}