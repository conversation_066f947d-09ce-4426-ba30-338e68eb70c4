<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2018/10/15
 * Time: 0:45
 */

require(ROOT_PATH . 'Core/Tools/Osssdk/samples/Common.php');
use OSS\OssClient;
use OSS\Core\OssException;

/**
 * 生成随机的数字串
 *
 * @author: weber liu
 * @return string
 */
function random_imgname()
{
    $str = 'x';
    for($i = 0; $i < 9; $i++)
    {
        $str .= mt_rand(0, 9);
    }

    return date("YmdHi") . $str;
}

function UpOssFile($FILES,$dir = "schoolmanage/") {
    $bucket = \Common::getBucketName();
    $ossClient = \Common::getOssClient();
    if (is_null($ossClient)) exit(1);

    $randomname = random_imgname();
    if(isset($FILES["ossfile"])){
        $imgname = $dir . $randomname . '.' .check_file_type($FILES["ossfile"]['name']);
        $ossClient->uploadFile($bucket,$imgname, $FILES["ossfile"]["tmp_name"], array());
    }

    if(isset($FILES["file"])){
        $imgname = $dir . $randomname . '.' .check_file_type($FILES["file"]['name']);
        $ossClient->uploadFile($bucket,$imgname, $FILES["file"]["tmp_name"], array());
    }
    return "https://pic.kedingdang.com/".$imgname;
}

function UpOssVideoFile($mp3File,$dir = "recordvideo/") {
    $bucket = \Common::getBucketName();
    $ossClient = \Common::getOssClient();
    if (is_null($ossClient)) exit(1);

    $randomname = random_imgname();
    $imgname = $dir . $randomname . '.' .check_file_type($mp3File);
    $ossClient->uploadFile($bucket,$imgname, $mp3File, array());

    return "https://pic.kedingdang.com/".$imgname;
}