<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2016/12/13
 * Time: 17:04
 */

/**
 * 将对象成员变量或者数组的特殊字符进行转义
 *
 * @access   public
 * @param    mix        $obj      对象或者数组
 * <AUTHOR> Yan
 *
 * @return   mix                  对象或者数组
 */
function addslashes_deep_obj($obj)
{
    if (is_object($obj) == true)
    {

        foreach ($obj AS $key => $val)
        {
            $obj->$key = objaddslashes_deep($val);
            //$obj->$key = $val;
        }
    }
    else
    {
        $obj = objaddslashes_deep($obj);
    }

    return $obj;
}


/**
 * 递归方式的对变量中的特殊字符进行转义
 *
 * @access  public
 * @param   mix     $value
 *
 * @return  mix
 */
function objaddslashes_deep($value)
{
    if (empty($value))
    {
        return $value;
    }
    else
    {
        if(is_object($value)){
            $value = get_object_vars($value);
        }
        return is_array($value) ? array_map('addslashes_deep', $value) : addslashes($value);
    }
}

/**
 * 递归方式的对变量中的特殊字符进行转义
 *
 * @access  public
 * @param   mix     $value
 *
 * @return  mix
 */
function addslashes_deep($value)
{
    if (empty($value))
    {
        return $value;
    }
    else
    {
        return is_array($value) ? array_map('addslashes_deep', $value) : addslashes($value);
    }
}

/**
 * 屏蔽字符串攻击
 *
 * @access  public
 */
function pinbiAttack($str)
{
    $farr = array(
        "/\\s+/",
        "/<(\\/?)(script|i?frame|style|html|body|title|link|meta|object|\\?|\\%)([^>]*?)>/isU",
        "/(<[^>]*)on[a-zA-Z]+\s*=([^>]*>)/isU",
    );
    $str = preg_replace($farr,"",$str);
    //$str = htmlentities($str);
    return addslashes($str);
}

function fh_Attack($array)
{
    if (is_array($array))
    {
        foreach($array as $k => $v)
        {
            $array[$k] = fh_Attack($v);
        }
    }
    else
    {
        $array = pinbiAttack($array);
    }
    return $array;
}

/**
 *
 * 把对象转成数组
 * @param $object 要转的对象$object
 */
function objectToArray($object){

    $result = array();

    $object = is_object($object) ? get_object_vars($object) : $object;
    if($object){
        foreach ($object as $key => $val) {

            $val = (is_object($val) || is_array($val)) ? objectToArray($val) : $val;

            $result[$key] = $val;
        }
    }

    return $result;
}

//是否存在数组中的值
function FunStringExist($StrFiltrate,$ArrFiltrate){
    foreach ($ArrFiltrate as $key=>$value){
        if (stristr($StrFiltrate,$value)){
            return true;
        }
    }
    return false;
}

//多维转一维
function array_wscl($st_array){
    static $rs_array= array();
    foreach($st_array as $value){
        if(is_array($value)){
            array_wscl($value);
        }else{
            $re_array[]=$value;
        }

    }
    return $re_array;
}
//准确的时差
function gnmetime(){
    return time();
}

/*用户等级获取*/
function getuserstars($growing){
    switch($growing){
        case $growing<200 && $growing>=0:$stars="1";break;
        case $growing<800 && $growing>=200:$stars="2";break;
        case $growing<1500 && $growing>=800:$stars="3";break;
        case $growing<5000 && $growing>=1500:$stars="4";break;
        case $growing<10000 && $growing>=5000:$stars="5";break;
        case $growing<30000 && $growing>=10000:$stars="6";break;
        case $growing<50000 && $growing>=30000:$stars="7";break;
        case $growing<80000 && $growing>=50000:$stars="8";break;
        case $growing>=80000:$growing="9";break;
    }
    return $stars;
}

//获取随机数字或字母
function GetRandPid($type,$nums){
    $Str = "ABCDEFGHIJKLMNOPQRSTUVWXYZ123456789";
    $Randstr = "";
    if($type == '1'){//字母类
        for($i=1;$i<=$nums;$i++) {
            $Randstr .= $Str[rand(0,26)];
        }
    }elseif($type == '2'){//数字型
        for($i=1;$i<=$nums;$i++) {
            $Randstr .= $Str[rand(26,34)];
        }
    }elseif($type == '3'){//混合型
        for($i=1;$i<=$nums;$i++) {
            $Randstr .= $Str[rand(0,34)];
        }
    }elseif($type == '4'){//四个字母开头
        $Randstr = $Str[rand(0,26)].$Str[rand(0,26)].$Str[rand(0,26)].$Str[rand(0,26)];
        for($i=1;$i<=$nums-4;$i++) {
            $Randstr .= $Str[rand(0,34)];
        }
    }

    return $Randstr;
}

//图片路径处理
function imggetpath($img)
{
    if(strstr($img,'http://')){
        return $img;
    }else{
        if($img == ""){
            return URL_PATH."webhtm/images/img_tic.jpg";
        }else{
            return IMG_PATH.$img;
        }
    }

}

//把全角数字转为半角数字
function GetAlabNum($fnum){
    $nums = array("０","１","２","３","４","５","６","７","８","９");
    $fnums = "0123456789";
    for($i=0;$i<=9;$i++) $fnum = str_replace($nums[$i],$fnums[$i],$fnum);
    $fnum = ereg_replace("[^0-9\.]|^0{1,}","",$fnum);
    if($fnum=="") $fnum=0;
    return $fnum;
}
//去除HTML标记
function Text2Html($txt){
    $txt = str_replace("  ","　",$txt);
    $txt = str_replace("<","&lt;",$txt);
    $txt = str_replace(">","&gt;",$txt);
    $txt = preg_replace("/[\r\n]{1,}/isU","<br/>\r\n",$txt);
    return $txt;
}

//清除HTML标记
function ClearHtml($str){
    $str = str_replace('<','&lt;',$str);
    $str = str_replace('>','&gt;',$str);
    return $str;
}

//相对路径转化成绝对路径
function relative_to_absolute($content, $feed_url) {
    preg_match('/(http|https|ftp):\/\//', $feed_url, $protocol);
    $server_url = preg_replace("/(http|https|ftp|news):\/\//", "", $feed_url);
    $server_url = preg_replace("/\/.*/", "", $server_url);

    if ($server_url == '') {
        return $content;
    }

    if (isset($protocol[0])) {
        $new_content = preg_replace('/href="\//', 'href="'.$protocol[0].$server_url.'/', $content);
        $new_content = preg_replace('/src="\//', 'src="'.$protocol[0].$server_url.'/', $new_content);
    } else {
        $new_content = $content;
    }
    return $new_content;
}
//取得所有链接
function get_all_url($code){
    preg_match_all('/<a\s+href=["|\']?([^>"\' ]+)["|\']?\s*[^>]*>([^>]+)<\/a>/i',$code,$arr);
    return array('name'=>$arr[2],'url'=>$arr[1]);
}

//获取指定标记中的内容
function get_tag_data($str, $start, $end){
    if ( $start == '' || $end == '' ){
        return;
    }
    $str = explode($start, $str);
    $str = explode($end, $str[1]);
    return $str[0];
}
//HTML表格的每行转为CSV格式数组
function get_tr_array($table) {
    $table = preg_replace("'<td[^>]*?>'si",'"',$table);
    $table = str_replace("</td>",'",',$table);
    $table = str_replace("</tr>","{tr}",$table);
    //去掉 HTML 标记
    $table = preg_replace("'<[\/\!]*?[^<>]*?>'si","",$table);
    //去掉空白字符
    $table = preg_replace("'([\r\n])[\s]+'","",$table);
    $table = str_replace(" ","",$table);
    $table = str_replace(" ","",$table);

    $table = explode(",{tr}",$table);
    array_pop($table);
    return $table;
}

//将HTML表格的每行每列转为数组，采集表格数据
function get_td_array($table) {
    $table = preg_replace("'<table[^>]*?>'si","",$table);
    $table = preg_replace("'<tr[^>]*?>'si","",$table);
    $table = preg_replace("'<td[^>]*?>'si","",$table);
    $table = str_replace("</tr>","{tr}",$table);
    $table = str_replace("</td>","{td}",$table);
    //去掉 HTML 标记
    $table = preg_replace("'<[\/\!]*?[^<>]*?>'si","",$table);
    //去掉空白字符
    $table = preg_replace("'([\r\n])[\s]+'","",$table);
    $table = str_replace(" ","",$table);
    $table = str_replace(" ","",$table);

    $table = explode('{tr}', $table);
    array_pop($table);
    foreach ($table as $key=>$tr) {
        $td = explode('{td}', $tr);
        array_pop($td);
        $td_array[] = $td;
    }
    return $td_array;
}

//返回字符串中的所有单词 $distinct=true 去除重复
function split_en_str($str,$distinct=true) {
    preg_match_all('/([a-zA-Z]+)/',$str,$match);
    if ($distinct == true) {
        $match[1] = array_unique($match[1]);
    }
    sort($match[1]);
    return $match[1];
}

/**时间处理**/
function get_vartotime($timevar)
{
    $timego = explode(" ",$timevar);
    $timen = explode("-",$timego[0]);
    if(!isset($timego[1])){
        $timef = array('0','0','0');
    }else{
        $timef = explode(":",$timego[1]);
    }
    $times = mktime($timef[0],$timef[1],$timef[2],$timen[1],$timen[2],$timen[0]);
    return $times;
}

//七位随机数字ID
function getsevenId(){
    $Str = "1234567890";
    return $Str[rand(0,8)].$Str[rand(0,8)].$Str[rand(0,8)].$Str[rand(0,8)].$Str[rand(0,8)].$Str[rand(0,8)].$Str[rand(0,8)];
}

//数字ID
function digitalIdget(){
    $Str = "1234567890";
    return $Str[rand(0,8)].$Str[rand(0,8)].$Str[rand(0,8)].$Str[rand(0,8)].$Str[rand(0,8)].$Str[rand(0,8)].$Str[rand(0,8)].$Str[rand(0,8)];
}

//项目编号生产函数
function GetItemPid(){
    $Str = "ABCDEFGHIJKLMNOPQRSTUVWXYZ123456789";
    $Randstr = $Str[rand(0,26)].$Str[rand(0,26)].$Str[rand(0,26)].$Str[rand(0,26)];
    for($i=1;$i<=8;$i++) {
        $Randstr .= $Str[rand(0,34)];
    }
    return $Randstr;
}

//验证码
function miscode(){
    $Str = "1234567890";
    $rangtr = $Str[rand(0,8)].$Str[rand(0,9)].$Str[rand(0,9)].$Str[rand(0,9)].$Str[rand(0,9)].$Str[rand(0,9)];
    return $rangtr;
}
//邮箱正则表达式
function funcemail($str){
    return (preg_match("/^([0-9A-Za-z\\-_\\.]+)@([0-9a-z]+\\.[a-z]{2,3}(\\.[a-z]{2})?)$/i",$str))?true:false;
}
//手机号码正则表达试
function funcmtel($str){
    return (preg_match("/^1[3|4|5|7|8][0-9]\d{4,8}$/",$str))?true:false;
}

//项目编号生产函数
function arraysortPage($listArray,$start,$per_page='6'){
    if (count($listArray) == 0) {
        return false;
    }
    arsort ($listArray);

    $result_array_temp = $listArray;

    while (list($key, $value) = each ($result_array_temp)) {
        $result_array[$key] = $value;
    }

    $results = count($result_array);

    $keys = array_keys($result_array);

    for ($i = ($start -1)*$per_page; $i < min($results, ($start -1)*$per_page + $per_page) ; $i++) {
        $in[] = $keys[$i];
    }

    $inlist = implode(",", $in);
    return  $inlist;
}

function cmp($a, $b) {
    if ($a['weight'] == $b['weight'])
        return 0;
    return ($a['weight'] > $b['weight']) ? -1 : 1;
}

 function debug($v)
 {
       echo "<pre>";
       print_r($v);
       echo "</pre>";
 }



function hideNameString($str) {
    $strlen     = mb_strlen($str, 'utf-8');
    $firstStr     = mb_substr($str, 0, 1, 'utf-8');
    return $strlen == 2 ? $firstStr . @str_repeat('*', mb_strlen($str, 'utf-8') - 1) : $firstStr . @str_repeat("*", $strlen - 2). @str_repeat("*", $strlen - 2);
}

function hideNumberString($str) {
    if (strpos($str, '@')) {
        $email_array = explode("@", $str);
        $prevfix = (strlen($email_array[0]) < 4) ? "" : substr($str, 0, 3);
        $count = 0;
        $str = preg_replace('/([\d\w+_-]{0,100})@/', '***@', $str, -1, $count);
        $rs = $prevfix . $str;
    } else {
        if (preg_match("/^1[3456789]{1}\d{9}$/", $str)) {
            $rs = substr($str, 0, 3) . "****" . substr($str, -4);
        } else {
            $rs = substr($str, 0, 5) . "***" . substr($str, -4);
        }
    }
    return $rs;
}

//Tmk批次生成码
function getTmkBatchId(){
    $Str = "ABCDEFGHIJKLMNOPQRSTUVWXYZ123456789";
    return date("Ymd").$Str[rand(0,34)].$Str[rand(0,34)].$Str[rand(0,34)].$Str[rand(0,34)];
}

//Tmk批次生成码
function getCallBatchId(){
    $Str = "ABCDEFGHIJKLMNOPQRSTUVWXYZ123456789";
    return date("YmdHis").$Str[rand(0,34)].$Str[rand(0,34)].$Str[rand(0,34)].$Str[rand(0,34)];
}

//模拟生成 uuid
function generateUUID($prefix = '') {
    $chars = md5(uniqid(mt_rand(), true));
    $uuid = substr($chars,0,8).'-';
    $uuid .= substr($chars,8,4).'-';
    $uuid .= substr($chars,12,4).'-';
    $uuid .= substr($chars,16,4).'-';
    $uuid .= substr($chars,20,12);
    return $prefix.$uuid;
}
