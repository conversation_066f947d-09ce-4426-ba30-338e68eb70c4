<?php
/** excel表格操作函数
 * ============================================================================
 * 版权所有 (C)2013-11-22号 http://www.mohuso.com 。
 * 网站地址 : http://www.mohuso.com
 * <AUTHOR> Zhugong Qi
 * @version : v1.0
 * ----------------------------------------------------------------------------
 */
require(ROOT_PATH . 'Core/Tools/Phpexcel/PHPExcel.php');
require(ROOT_PATH . 'Core/Tools/Phpexcel/PHPExcel/Writer/Excel2007.php');

/**
 * @ys_array=array('name'=>'姓名','age'=>'年龄');映射字段名称
 */
function execl_to_array($filePath, $ys_array)
{
    $objPHPExcel = new PHPEXCEL();
    $PHPReader = new PHPExcel_Reader_Excel2007();
    if (!$PHPReader->canRead($filePath)) {
        $PHPReader = new PHPExcel_Reader_Excel5();
        if (!$PHPReader->canRead($filePath)) {
//            echo 'no Excel';
            return false;
        }
    }
    $PHPReader->setReadDataOnly(true); // 关键设置 --- 通过设置 setReadDataOnly(true)，忽略单元格格式和样式，显著减少内存占用：
    $PHPExcel = $PHPReader->load($filePath);

    //取得excel第一个文档
    $currentSheet = $PHPExcel->getSheet(0);

    //取得一共有多少列
    $columns = PHPExcel_Cell::columnIndexFromString($currentSheet->getHighestColumn());

    //取得一共有多少行
    $rows = $currentSheet->getHighestRow();

    $sqlarray = array();
    $header_fields = array();

    //取得列表数组，$header_fields的key为列名的排序，value为映射的英文字段名
    for ($field_index = 0; $field_index < $columns; $field_index++) {
        if ($currentSheet->getCellByColumnAndRow($field_index, 1)->getValue() !== '') {
            $headerSting = $currentSheet->getCellByColumnAndRow($field_index, 1)->getValue();
            if (is_object($headerSting)) $headerSting = $headerSting->__toString();
            if (isset($ys_array[$headerSting])) {
                $header_fields[$field_index] = $ys_array[$headerSting];
            }
        }
    }


    //将字段内容放在返回数组的第一个
    $sqlarray[0] = $header_fields;

    //将数据填充到数组中
    for ($currentRow = 2; $currentRow <= $rows; $currentRow++) {
        foreach ($header_fields as $key => $value) {
            $sqlarray[$currentRow - 1][$value] = (string)$currentSheet->getCellByColumnAndRow($key, $currentRow)->getValue();
        }
    }


    return $sqlarray;
}

/**
 * @ys_array=array('name'=>'姓名','age'=>'年龄');映射字段名称  -- 招行总行对账单
 */
function execl_to_array_zh($filePath, $ys_array)
{
    $objPHPExcel = new PHPEXCEL();
    $PHPReader = new PHPExcel_Reader_Excel2007();
    if (!$PHPReader->canRead($filePath)) {
        $PHPReader = new PHPExcel_Reader_Excel5();
        if (!$PHPReader->canRead($filePath)) {
//            echo 'no Excel';
            return false;
        }
    }
    $PHPExcel = $PHPReader->load($filePath);

    //取得excel第一个文档
    $currentSheet = $PHPExcel->getSheet(0);

    //取得一共有多少列
    $columns = PHPExcel_Cell::columnIndexFromString($currentSheet->getHighestColumn());

    //取得一共有多少行
    $rows = $currentSheet->getHighestRow();

    $sqlarray = array();
    $header_fields = array();

    //取得列表数组，$header_fields的key为列名的排序，value为映射的英文字段名
    for ($field_index = 0; $field_index < $columns; $field_index++) {
        if ($currentSheet->getCellByColumnAndRow($field_index, 14)->getValue() !== '') {
            $headerSting = $currentSheet->getCellByColumnAndRow($field_index, 14)->getValue();
            if (is_object($headerSting)) $headerSting = $headerSting->__toString();
            if (isset($ys_array[$headerSting])) {
                $header_fields[$field_index] = $ys_array[$headerSting];
            }
        }
    }

    //将字段内容放在返回数组的第一个
    $sqlarray[0] = $header_fields;

    //将数据填充到数组中
    for ($currentRow = 15; $currentRow <= $rows; $currentRow++) {
        foreach ($header_fields as $key => $value) {
            $sqlarray[$currentRow - 1][$value] = (string)$currentSheet->getCellByColumnAndRow($key, $currentRow)->getValue();
        }
    }

    return $sqlarray;
}

/**
 * @ys_array=array('name'=>'姓名','age'=>'年龄');映射字段名称  -- 招行总行对账单 -- 获取商户号
 */
function execl_to_array_zhone($filePath, $ys_array)
{
    $objPHPExcel = new PHPEXCEL();
    $PHPReader = new PHPExcel_Reader_Excel2007();
    if (!$PHPReader->canRead($filePath)) {
        $PHPReader = new PHPExcel_Reader_Excel5();
        if (!$PHPReader->canRead($filePath)) {
//            echo 'no Excel';
            return false;
        }
    }
    $PHPExcel = $PHPReader->load($filePath);

    //取得excel第一个文档
    $currentSheet = $PHPExcel->getSheet(0);

    //取得一共有多少列
    $columns = PHPExcel_Cell::columnIndexFromString($currentSheet->getHighestColumn());

    //取得一共有多少行
    $rows = $currentSheet->getHighestRow();

    $sqlarray = array();
    $header_fields = array();

    //取得列表数组，$header_fields的key为列名的排序，value为映射的英文字段名
    for ($field_index = 0; $field_index < $columns; $field_index++) {
        if ($currentSheet->getCellByColumnAndRow($field_index, 2)->getValue() !== '') {
            $headerSting = $currentSheet->getCellByColumnAndRow($field_index, 2)->getValue();
            if (is_object($headerSting)) $headerSting = $headerSting->__toString();
            if (isset($ys_array[$headerSting])) {
                $header_fields[$field_index] = $ys_array[$headerSting];
            }
        }
    }

    //将字段内容放在返回数组的第一个
    $sqlarray[0] = $header_fields;
    $rows = 3;
    //将数据填充到数组中
    for ($currentRow = 3; $currentRow <= $rows; $currentRow++) {
        foreach ($header_fields as $key => $value) {
            $sqlarray[$currentRow - 1][$value] = (string)$currentSheet->getCellByColumnAndRow($key, $currentRow)->getValue();
        }
    }

    return $sqlarray;
}

/**
 * @header array();头部字段名称数组
 * @query;查询结果
 * @filename：导出的excel名字
 */
function query_to_excel($header, $queryarray, $getfileds, $filename)
{
    $cacheMethod = PHPExcel_CachedObjectStorageFactory::cache_in_memory_gzip;
    $cacheSettings = array('memoryCacheSize' => '512MB');
    PHPExcel_Settings::setCacheStorageMethod($cacheMethod, $cacheSettings);
    $objPHPExcel = new PHPEXCEL();
    foreach ($header as $key => $value) {
        $objPHPExcel->getActiveSheet()->setCellValueExplicitByColumnAndRow($key, 1, $value);
    }

    foreach ($queryarray as $row => $item) {
        $i = 0;
        $row = $row + 2;
        foreach ($item as $key => $field) {
            if (in_array($key, $getfileds)) {
                $objPHPExcel->getActiveSheet()->setCellValueExplicitByColumnAndRow($i, $row, $field);
                $i++;
            }

        }
    }

    $objWriter = new PHPExcel_Writer_Excel2007($objPHPExcel);
    header("Pragma: public");
    header("Expires: 0");
    header("Cache-Control:must-revalidate, post-check=0, pre-check=0");
    header("Content-Type:application/force-download");
    header("Content-Type:application/vnd.ms-excel; charset=UTF-8");
    header("Content-Type:application/octet-stream");
    header("Content-Type:application/download");
    header("Content-Disposition:attachment;filename=" . urlencode($filename));
    header("Content-Transfer-Encoding:binary");
    $objWriter->save("php://output");
    exit;
}

function query_to_excel2($header, $queryarray, $getfileds, $header2, $queryarray2, $getfileds2, $filename)
{
    $cacheMethod = PHPExcel_CachedObjectStorageFactory::cache_in_memory_gzip;
    $cacheSettings = array('memoryCacheSize' => '512MB');
    PHPExcel_Settings::setCacheStorageMethod($cacheMethod, $cacheSettings);
    $objPHPExcel = new PHPEXCEL();

    $objPHPExcel->setactivesheetindex(0);
    foreach ($header as $key => $value) {
        $objPHPExcel->getActiveSheet()->setCellValueExplicitByColumnAndRow($key, 1, $value);
    }

    foreach ($queryarray as $row => $item) {
        $i = 0;
        $row = $row + 2;
        foreach ($item as $key => $field) {
            if (in_array($key, $getfileds)) {
                $objPHPExcel->getActiveSheet()->setCellValueExplicitByColumnAndRow($i, $row, $field);
                $i++;
            }
        }
    }

    $objPHPExcel->createSheet();
    $objPHPExcel->setactivesheetindex(1);

    foreach ($header2 as $key => $value) {
        $objPHPExcel->getActiveSheet()->setCellValueExplicitByColumnAndRow($key, 1, $value);
    }

    foreach ($queryarray2 as $row => $item) {
        $i = 0;
        $row = $row + 2;
        foreach ($item as $key => $field) {
            if (in_array($key, $getfileds2)) {
                $objPHPExcel->getActiveSheet()->setCellValueExplicitByColumnAndRow($i, $row, $field);
                $i++;
            }
        }
    }

//    $objWriter = new PHPExcel_Writer_Excel2007($objPHPExcel);
    ob_clean();
    $objWriter = new PHPExcel_Writer_Excel2007($objPHPExcel);
    header("Pragma: public");
    header("Expires: 0");
    header("Cache-Control:must-revalidate, post-check=0, pre-check=0");
    header("Content-Type:application/force-download");
    header("Content-Type:application/vnd.ms-excel; charset=UTF-8");
    header("Content-Type:application/octet-stream");
    header("Content-Type:application/download");
    header("Content-Disposition:attachment;filename=" . urlencode($filename));
    header("Content-Transfer-Encoding:binary");
    $objWriter->save("php://output");
    exit;
}

function query_to_excel_split($header, $queryarray, $getfileds, $filename)
{
    $cacheMethod = PHPExcel_CachedObjectStorageFactory::cache_in_memory_gzip;
    $cacheSettings = array('memoryCacheSize' => '512MB');
    PHPExcel_Settings::setCacheStorageMethod($cacheMethod, $cacheSettings);
    $objPHPExcel = new PHPEXCEL();
    foreach ($header as $key => $value) {
        $objPHPExcel->getActiveSheet()->setCellValueExplicitByColumnAndRow($key, 1, $value);
    }

//    for ($j = 0; $j < count($queryarray); $j++) {
//        $row = $j + 2;
//        foreach ($queryarray[$j] as $key => $field) {
//            if (in_array($key, $getfileds)) {
//                $objPHPExcel->getActiveSheet()->setCellValueExplicitByColumnAndRow($key, $row, $field);
//            }
//        }
//    }

    debug('0' . strtotime(date()));
    $page = ceil(count($queryarray) / 10000);//一次处理一万条

    for ($num = 0; $num < $page; $num++) {
        for ($j = 0; $j < 10000; $j++) {
            $row = $num * 10000 + $j;
            $i = 0;
            if ($row < count($queryarray)) {
                foreach ($queryarray[$row] as $key => $field) {
                    if (in_array($key, $getfileds)) {
                        debug($row . strtotime(date()));
                        $objPHPExcel->getActiveSheet()->setCellValueExplicitByColumnAndRow($i, $row + 2, $field);
                        $i++;
                    }
                }
            }
        }
    }
    debug('1' . strtotime(date()));

    ob_end_clean();
    $objWriter = new PHPExcel_Writer_Excel2007($objPHPExcel);
    header("Pragma: public");
    header("Expires: 0");
    header("Cache-Control:must-revalidate, post-check=0, pre-check=0");
    header("Content-Type:application/force-download");
    header("Content-Type:application/vnd.ms-excel; charset=UTF-8");
    header("Content-Type:application/octet-stream");
    header("Content-Type:application/download");

    header("Content-Disposition:attachment;filename=" . urlencode($filename));
    header("Content-Transfer-Encoding:binary");
    $objWriter->save("php://output");
    exit;
}

function IntToChr($index, $start = 65)
{
    $str = '';
    if (floor($index / 26) > 0) {
        $str .= IntToChr(floor($index / 26) - 1);
    }
    return $str . chr($index % 26 + $start);
}

function query_to_excel_only($schoolOne, $mainList, $classhourList, $studstudyList, $hourstudyList, $stucheck, $fileName, $fileType)
{
    $cacheMethod = PHPExcel_CachedObjectStorageFactory::cache_in_memory_gzip;
    $cacheSettings = array('memoryCacheSize' => '512MB');
    PHPExcel_Settings::setCacheStorageMethod($cacheMethod, $cacheSettings);
    $obj = new \PHPExcel();

    // 以下内容是excel文件的信息描述信息
    $obj->getProperties()->setCreator(''); //设置创建者
    $obj->getProperties()->setLastModifiedBy(''); //设置修改者
    $obj->getProperties()->setTitle(''); //设置标题
    $obj->getProperties()->setSubject(''); //设置主题
    $obj->getProperties()->setDescription(''); //设置描述
    $obj->getProperties()->setKeywords('');//设置关键词
    $obj->getProperties()->setCategory('');//设置类型

    // 设置当前sheet
    $obj->setActiveSheetIndex(0);

    // 设置当前sheet的名称
    $obj->getActiveSheet()->setTitle('student');

    $colIndex = 0;
    $rowIndex = 0;

    $colname = IntToChr($colIndex);

    $rowIndex++;// 填充第一行数据

    $obj->getActiveSheet()->mergeCells('A1:R1');//合并单元格（如果要拆分单元格是需要先合并再拆分的，否则程序会报错）
    $obj->getActiveSheet()->setCellValue($colname . $rowIndex, "{$schoolOne['school_cnname']} 班级考勤表");
    $obj->getActiveSheet()->getStyle('A1')->getFont()->setBold(true);
    $obj->getActiveSheet()->getStyle('A1')->getFont()->setSize(18);
    $obj->getActiveSheet()->getStyle('A1')->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
    $obj->getActiveSheet()->getStyle('A1')->getAlignment()->setVertical(PHPExcel_Style_Alignment::VERTICAL_CENTER);

    $rowIndex++;
    $obj->getActiveSheet()->mergeCells('A' . $rowIndex . ':D' . $rowIndex);
    $obj->getActiveSheet()->setCellValue($colname . $rowIndex, "出席:√    缺席:×");

    $rowIndex++;
    $obj->getActiveSheet()->mergeCells('A' . $rowIndex . ':D' . $rowIndex);
    $obj->getActiveSheet()->setCellValue($colname . $rowIndex, "班级名称：{$mainList['class_enname']}-{$mainList['class_cnname']}");

    $colname = IntToChr($colIndex + 4);
    $obj->getActiveSheet()->mergeCells('E' . $rowIndex . ':H' . $rowIndex);
    $obj->getActiveSheet()->setCellValue($colname . $rowIndex, "主教：{$mainList['main_teacher']} 助教：{$mainList['sub_teacher']}");

    $rowIndex++;

    $length = count($classhourList);
    $obj->getActiveSheet()
        ->mergeCells('A' . $rowIndex . ':A' . ($rowIndex + 3))
        ->mergeCells('B' . $rowIndex . ':B' . ($rowIndex + 3))
        ->mergeCells('C' . $rowIndex . ':C' . ($rowIndex + 3))
        ->mergeCells('D' . $rowIndex . ':D' . ($rowIndex + 3))
        ->mergeCells('E' . $rowIndex . ':' . (IntToChr($colIndex + 3 + $length)) . ($rowIndex));
    $obj->getActiveSheet()
        ->setCellValue(IntToChr($colIndex) . $rowIndex, '序号')
        ->setCellValue(IntToChr($colIndex + 1) . $rowIndex, '中文名称')
        ->setCellValue(IntToChr($colIndex + 2) . $rowIndex, '英文名称')
        ->setCellValue(IntToChr($colIndex + 3) . $rowIndex, '联系方式')
        ->setCellValue(IntToChr($colIndex + 4) . $rowIndex, '上课序号/上课日期/上课时间');

    $rowIndex++;
    $i = 0;
    if ($length > 0) {
        foreach ($classhourList as $classhourvar) {
            $i++;
            $colname = IntToChr($colIndex + $i + 3);
            $is_warming = $classhourvar['hour_iswarming'];
            if ($is_warming == '1') {
                $obj->getActiveSheet()->setCellValue($colname . ($rowIndex), '暖身课');
            } elseif($is_warming == '2') {
                $obj->getActiveSheet()->setCellValue($colname . ($rowIndex), '复习课');
            }else{
                $obj->getActiveSheet()->setCellValue($colname . ($rowIndex), $classhourvar['hour_lessontimes']);

            }
            $obj->getActiveSheet()->setCellValue($colname . ($rowIndex + 1), $classhourvar['hour_day']);
            $obj->getActiveSheet()->getStyle($colname . ($rowIndex + 2))->getAlignment()->setWrapText(true);
            $obj->getActiveSheet()->setCellValue($colname . ($rowIndex + 2), $classhourvar['hour_starttime'] . "\r\n" . $classhourvar['hour_endtime']);
        }
    }

    $rowIndex = $rowIndex + 3;
    $colIndex = 0;
    $obj->getActiveSheet()
        ->mergeCells('A' . $rowIndex . ':D' . $rowIndex)
        ->mergeCells('A' . ($rowIndex + 1) . ':D' . ($rowIndex + 1));
    $obj->getActiveSheet()
        ->setCellValue('A' . $rowIndex, "实际上课日期")
        ->setCellValue('A' . ($rowIndex + 1), "上课教师");

    $rowIndex = $rowIndex + 2;
    $height = count($studstudyList);
    $values = count($hourstudyList);
    $i = 0;
    if ($height > 0) {

        foreach ($studstudyList as $studstudyvar) {
            $i++;
            $student_id = $studstudyvar['student_id'];
            $obj->getActiveSheet()
                ->setCellValue('A' . ($rowIndex), $i)
                ->setCellValue('B' . ($rowIndex), $studstudyvar['student_cnname'])
                ->setCellValue('C' . ($rowIndex), $studstudyvar['student_enname'])
                ->setCellValue('D' . ($rowIndex), $studstudyvar['family_mobile'], PHPExcel_Cell_DataType::TYPE_STRING);

            if ($values > 0) {
                $j = 0;
                foreach ($classhourList as $classhourvar) {
                    $j++;
                    $colname = IntToChr($colIndex + $j + 3);
                    $hour_id = $classhourvar['hour_id'];
                    if($hourstudyList){
                        foreach ($hourstudyList as $hourstudyvar) {
                            if ($hour_id == $hourstudyvar['hour_id'] && $student_id == $hourstudyvar['student_id']) {

                                $obj->getActiveSheet()->setCellValue($colname . $rowIndex, $stucheck[$hourstudyvar['stuchecktype_code']]);
                            }
                        }
                    }
                }
            }
            $rowIndex++;
        }
    }

    $obj->getActiveSheet()
        ->getStyle('A2:' . IntToChr($length + 3) . ($height + 9))->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_CENTER);//主体水平居中

    $obj->getActiveSheet()->getColumnDimension('D')->setWidth(15);
    $obj->getActiveSheet()->getRowDimension(7)->setRowHeight(30);

    $style_array = array(
        'borders' => array(
            'allborders' => array(
                'style' => \PHPExcel_Style_Border::BORDER_THIN
            )
        ));
    $obj->getActiveSheet()->getStyle('A4:' . IntToChr($length + 3) . ($height + 9))->applyFromArray($style_array);

    //导出
    ob_clean();
    $objWriter = new PHPExcel_Writer_Excel2007($obj);
    header("Pragma: public");
    header("Expires: 0");
    header("Cache-Control:must-revalidate, post-check=0, pre-check=0");
    header("Content-Type:application/force-download");
    header("Content-Type:application/vnd.ms-excel; charset=UTF-8");
    header("Content-Type:application/octet-stream");
    header("Content-Type:application/download");
    header("Content-Disposition:attachment;filename=" . urlencode($fileName) . urlencode($fileType));
    header("Content-Transfer-Encoding:binary");
    $objWriter->save("php://output");
    exit;
}

//周次教师一览
function query_to_excel_weektimelist($header, $queryarray, $getfileds, $filename, $weekNOvalues = array())
{
    $cacheMethod = PHPExcel_CachedObjectStorageFactory::cache_in_memory_gzip;
    $cacheSettings = array('memoryCacheSize' => '512MB');
    PHPExcel_Settings::setCacheStorageMethod($cacheMethod, $cacheSettings);
    $objPHPExcel = new PHPEXCEL();
    foreach ($header as $key => $value) {
        $objPHPExcel->getActiveSheet()->setCellValueExplicitByColumnAndRow($key, 1, $value);
    }

    foreach ($queryarray as $row => $item) {
        $i = 0;
        $row = $row + 2;
        foreach ($item as $key => $field) {
            if (in_array($key, $getfileds)) {
                $objPHPExcel->getActiveSheet()->setCellValueExplicitByColumnAndRow($i, $row, $field);
                $i++;
            }
        }
    }
    $rowsstart = 2;
    if ($weekNOvalues) {
        $key_array = array_keys($weekNOvalues);

        if (in_array('1', $key_array)) {
            $rowslenths = $weekNOvalues[1] + $rowsstart - 1;
            $objPHPExcel->getActiveSheet()->mergeCells('A' . $rowsstart . ':A' . $rowslenths);
            $rowsstart = $rowslenths + 1;
        }
        if (in_array('2', $key_array)) {
            $rowslenths = $weekNOvalues[2] + $rowsstart - 1;
            $objPHPExcel->getActiveSheet()->mergeCells('A' . $rowsstart . ':A' . $rowslenths);
            $rowsstart = $rowslenths + 1;
        }
        if (in_array('3', $key_array)) {
            $rowslenths = $weekNOvalues[3] + $rowsstart - 1;
            $objPHPExcel->getActiveSheet()->mergeCells('A' . $rowsstart . ':A' . $rowslenths);
            $rowsstart = $rowslenths + 1;
        }
        if (in_array('4', $key_array)) {
            $rowslenths = $weekNOvalues[4] + $rowsstart - 1;
            $objPHPExcel->getActiveSheet()->mergeCells('A' . $rowsstart . ':A' . $rowslenths);
            $rowsstart = $rowslenths + 1;
        }
        if (in_array('5', $key_array)) {
            $rowslenths = $weekNOvalues[5] + $rowsstart - 1;
            $objPHPExcel->getActiveSheet()->mergeCells('A' . $rowsstart . ':A' . $rowslenths);
            $rowsstart = $rowslenths + 1;
        }
        if (in_array('6', $key_array)) {
            $rowslenths = $weekNOvalues[6] + $rowsstart - 1;
            $objPHPExcel->getActiveSheet()->mergeCells('A' . $rowsstart . ':A' . $rowslenths);
            $rowsstart = $rowslenths + 1;
        }
        if (in_array('7', $key_array)) {
            $rowslenths = $weekNOvalues[7] + $rowsstart - 1;
            $objPHPExcel->getActiveSheet()->mergeCells('A' . $rowsstart . ':A' . $rowslenths);
            $rowsstart = $rowslenths + 1;
        }
        $objPHPExcel->getActiveSheet()
            ->getStyle('A1:' . 'H' . $rowsstart)->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_CENTER)
            ->setVertical(PHPExcel_Style_Alignment::VERTICAL_CENTER);//主体水平居中

        $objPHPExcel->getActiveSheet()->getColumnDimension('A')->setWidth(25);
        $objPHPExcel->getActiveSheet()->getColumnDimension('B')->setWidth(25);
        $objPHPExcel->getActiveSheet()->getColumnDimension('C')->setWidth(15);
        $objPHPExcel->getActiveSheet()->getColumnDimension('D')->setWidth(15);
        $objPHPExcel->getActiveSheet()->getColumnDimension('E')->setWidth(15);
        $objPHPExcel->getActiveSheet()->getColumnDimension('F')->setWidth(15);
        $objPHPExcel->getActiveSheet()->getColumnDimension('G')->setWidth(15);
        $objPHPExcel->getActiveSheet()->getColumnDimension('H')->setWidth(15);
    }

    $objWriter = new PHPExcel_Writer_Excel2007($objPHPExcel);
    header("Pragma: public");
    header("Expires: 0");
    header("Cache-Control:must-revalidate, post-check=0, pre-check=0");
    header("Content-Type:application/force-download");
    header("Content-Type:application/vnd.ms-excel; charset=UTF-8");
    header("Content-Type:application/octet-stream");
    header("Content-Type:application/download");
    header("Content-Disposition:attachment;filename=" . urlencode($filename));
    header("Content-Transfer-Encoding:binary");
    $objWriter->save("php://output");
    exit;
}

function excelTime($date, $time = false)
{
    if (function_exists('GregorianToJD')) {
        if (is_numeric($date)) {
            $jd = GregorianToJD(1, 1, 1970);
            $gregorian = JDToGregorian($jd + intval($date) - 25569);
            $date = explode('/', $gregorian);
            $date_str = str_pad($date [2], 4, '0', STR_PAD_LEFT) . "-" . str_pad($date [0], 2, '0', STR_PAD_LEFT) . "-" . str_pad($date [1], 2, '0', STR_PAD_LEFT) . ($time ? " 00:00:00" : '');
            return $date_str;
        }
    } else {
        $date = $date > 25568 ? $date + 1 : 25569;
        $ofs = (70 * 365 + 17 + 2) * 86400;

        if ($time) {
            $date = date("Y-m-d", ($date * 86400) - $ofs) . " 00:00:00";
        } else {
            $date = date("Y-m-d", ($date * 86400) - $ofs);
        }
    }
    return $date;
}

function excelfoo($s)
{
    $s = strtoupper($s);
    $res = 0;
    for ($i = 0; $i < strlen($s); $i++) {
        $res = 26 * $res + ord($s[$i]) - 64;
    }
    return $res;
}

function format_exceltoarray($filePath = '', $sheet = 0)
{
    if (empty($filePath) or !file_exists($filePath)) {
        die('file not exists');
    }
    $PHPReader = new PHPExcel_Reader_Excel2007();        //建立reader对象
    if (!$PHPReader->canRead($filePath)) {
        $PHPReader = new PHPExcel_Reader_Excel5();
        if (!$PHPReader->canRead($filePath)) {
            echo 'no Excel';
            return;
        }
    }
    $PHPExcel = $PHPReader->load($filePath);        //建立excel对象
    $currentSheet = $PHPExcel->getSheet($sheet);        //**读取excel文件中的指定工作表*/
    $allColumn = $currentSheet->getHighestColumn();        //**取得最大的列号*/
    $allRow = $currentSheet->getHighestRow();        //**取得一共有多少行*/
    $data = array();
    $lineKey = 0;
    for ($rowIndex = 1; $rowIndex <= $allRow; $rowIndex++) {
        for ($colIndex = 'A'; excelfoo($colIndex) <= excelfoo($allColumn); $colIndex++) {
            $addr = $colIndex . $rowIndex;
            $cell = $currentSheet->getCell($addr)->getValue();
            if ($cell instanceof PHPExcel_RichText) {
                $cell = $cell->__toString();
            }
            $data[$lineKey][$colIndex] = $cell;
        }
        $lineKey++;
    }
    return $data;
}

function excelcustom_studCourseEstimate($is_containsbreakoff, $excelDetailHeader, $excelClassHeader, $excelSchoolHeader, $mainData, $monthRange, $fileName, $fileType)
{
    $cacheMethod = PHPExcel_CachedObjectStorageFactory::cache_in_memory_gzip;
    $cacheSettings = array('memoryCacheSize' => '512MB');
    PHPExcel_Settings::setCacheStorageMethod($cacheMethod, $cacheSettings);
    $obj = new \PHPExcel();

    // 以下内容是excel文件的信息描述信息
    $obj->getProperties()->setCreator(''); //设置创建者
    $obj->getProperties()->setLastModifiedBy(''); //设置修改者
    $obj->getProperties()->setTitle(''); //设置标题
    $obj->getProperties()->setSubject(''); //设置主题
    $obj->getProperties()->setDescription(''); //设置描述
    $obj->getProperties()->setKeywords('');//设置关键词
    $obj->getProperties()->setCategory('');//设置类型

    // 设置当前sheet
    $obj->setActiveSheetIndex(0);

    // 设置当前sheet的名称
    if ($is_containsbreakoff == '1') {
        $obj->getActiveSheet()->setTitle('升班预估明细(含拆班)');
    } else {
        $obj->getActiveSheet()->setTitle('升班预估明细(不含拆班)');
    }

    $colIndex = 0;
    $rowIndex = 0;

    foreach ($excelDetailHeader as $key => $value) {
        $obj->getActiveSheet()->setCellValueExplicitByColumnAndRow($key, 1, $value);
    }

    $rowIndex++;
    $class_id = 0;

    $total_count = 0;
    $total_count_by_channel = 0;
    $renewal_count = 0;
    $renewal_count_by_channel = 0;
    $renewal_count_real = 0;
    $renewal_count_real_by_channel = 0;

    $mainClassData = array();
    $classarray = array();
    foreach ($mainData as $classstudentvar) {
        $i = 0;
        $rowIndex++;

        $obj->getActiveSheet()
            ->setCellValueExplicit(IntToChr($i++) . ($rowIndex), $classstudentvar['school_branch'], PHPExcel_Cell_DataType::TYPE_STRING)
            ->setCellValue(IntToChr($i++) . ($rowIndex), $classstudentvar['school_cnname'])
            ->setCellValueExplicit(IntToChr($i++) . ($rowIndex), $classstudentvar['student_branch'], PHPExcel_Cell_DataType::TYPE_STRING)
            ->setCellValue(IntToChr($i++) . ($rowIndex), $classstudentvar['student_cnname'])
            ->setCellValue(IntToChr($i++) . ($rowIndex), $classstudentvar['student_enname'])
            ->setCellValue(IntToChr($i++) . ($rowIndex), $classstudentvar['is_renewal'])
            ->setCellValue(IntToChr($i++) . ($rowIndex), $classstudentvar['renewal_times'])
            ->setCellValue(IntToChr($i++) . ($rowIndex), $classstudentvar['renewal_amount'])
            ->setCellValue(IntToChr($i++) . ($rowIndex), $classstudentvar['unpaid_price'])
            ->setCellValue(IntToChr($i++) . ($rowIndex), $classstudentvar['spend_price'])
            ->setCellValue(IntToChr($i++) . ($rowIndex), $classstudentvar['estimate_price'])
            ->setCellValue(IntToChr($i++) . ($rowIndex), $classstudentvar['coursebalance_time'])
            ->setCellValue(IntToChr($i++) . ($rowIndex), $classstudentvar['coursebalance_figure'])
            ->setCellValue(IntToChr($i++) . ($rowIndex), $classstudentvar['left_free_times'])
            ->setCellValue(IntToChr($i++) . ($rowIndex), $classstudentvar['channel_name'])
//            ->setCellValue(IntToChr($i++) . ($rowIndex), $classstudentvar['free_times_left_sent'])
//            ->setCellValue(IntToChr($i++) . ($rowIndex), $classstudentvar['free_times_left_unsent'])
//            ->setCellValue(IntToChr($i++) . ($rowIndex), $classstudentvar['free_times_left_all'])
            ->setCellValue(IntToChr($i++) . ($rowIndex), $classstudentvar['connect_times'])
            ->setCellValue(IntToChr($i++) . ($rowIndex), $classstudentvar['track_note'])
            ->setCellValueExplicit(IntToChr($i++) . ($rowIndex), $classstudentvar['class_branch'], PHPExcel_Cell_DataType::TYPE_STRING)
            ->setCellValue(IntToChr($i++) . ($rowIndex), $classstudentvar['class_cnname'])
            ->setCellValue(IntToChr($i++) . ($rowIndex), $classstudentvar['class_enname'])
            ->setCellValue(IntToChr($i++) . ($rowIndex), $classstudentvar['class_enddate'])
            ->setCellValue(IntToChr($i++) . ($rowIndex), $classstudentvar['main_teacher'])
            ->setCellValue(IntToChr($i++) . ($rowIndex), $classstudentvar['course_branch'])
            ->setCellValue(IntToChr($i++) . ($rowIndex), $classstudentvar['course_cnname'])
            ->setCellValue(IntToChr($i++) . ($rowIndex), $classstudentvar['coursecat_branch'])
            ->setCellValue(IntToChr($i++) . ($rowIndex), $classstudentvar['coursecat_cnname'])
            ->setCellValue(IntToChr($i++) . ($rowIndex), $classstudentvar['coursetype_branch'])
            ->setCellValue(IntToChr($i++) . ($rowIndex), $classstudentvar['coursetype_cnname'])
            ->setCellValue(IntToChr($i++) . ($rowIndex), $classstudentvar['class_num'])
            ->setCellValue(IntToChr($i++) . ($rowIndex), $classstudentvar['course_class_num']);

        if ($class_id == 0) {
            //首行
            $classarray = array();
            $classarray['school_id'] = $classstudentvar['school_id'];
            $classarray['coursetype_id'] = $classstudentvar['coursetype_id'];
            $classarray['coursecat_id'] = $classstudentvar['coursecat_id'];
            $classarray['school_branch'] = $classstudentvar['school_branch'];
            $classarray['school_cnname'] = $classstudentvar['school_cnname'];
            $classarray['class_branch'] = $classstudentvar['class_branch'];
            $classarray['class_enname'] = $classstudentvar['class_enname'];
            $classarray['class_enddate'] = $classstudentvar['class_enddate'];
            $classarray['class_endmonth'] = date("Ym", strtotime($classstudentvar['class_enddate']));
            $classarray['main_teacher'] = $classstudentvar['main_teacher'];
            $classarray['coursecat_branch'] = $classstudentvar['coursecat_branch'];
            $classarray['course_branch'] = $classstudentvar['course_branch'];
            $class_id = $classstudentvar['class_id'];
        }

        if ($classstudentvar['class_id'] == $class_id) {
            $total_count++;
            if (trim($classstudentvar['channel_name']) !== '') {
                $total_count_by_channel++;
                if ($classstudentvar['is_renewal'] == 'Y') {
                    $renewal_count_by_channel++;
                }
                if ($classstudentvar['is_renewal_real'] == 'Y') {
                    $renewal_count_real_by_channel++;
                }
            }
            if ($classstudentvar['is_renewal'] == 'Y') {
                $renewal_count++;
            }
            if ($classstudentvar['is_renewal_real'] == 'Y') {
                $renewal_count_real++;
            }
        } else {
            $classarray['total_count'] = $total_count;
            $classarray['total_count_by_channel'] = $total_count_by_channel;
            $classarray['renewal_count'] = $renewal_count;
            $classarray['renewal_count_by_channel'] = $renewal_count_by_channel;
            $classarray['renewal_count_real'] = $renewal_count_real;
            $classarray['renewal_count_real_by_channel'] = $renewal_count_real_by_channel;
            $mainClassData[] = $classarray;

            //新的一行
            $class_id = $classstudentvar['class_id'];
            $classarray = array();
            $classarray['school_id'] = $classstudentvar['school_id'];
            $classarray['coursetype_id'] = $classstudentvar['coursetype_id'];
            $classarray['coursecat_id'] = $classstudentvar['coursecat_id'];
            $classarray['school_branch'] = $classstudentvar['school_branch'];
            $classarray['school_cnname'] = $classstudentvar['school_cnname'];
            $classarray['class_branch'] = $classstudentvar['class_branch'];
            $classarray['class_enname'] = $classstudentvar['class_enname'];
            $classarray['class_enddate'] = $classstudentvar['class_enddate'];
            $classarray['class_endmonth'] = date("Ym", strtotime($classstudentvar['class_enddate']));
            $classarray['main_teacher'] = $classstudentvar['main_teacher'];
            $classarray['coursecat_branch'] = $classstudentvar['coursecat_branch'];
            $classarray['course_branch'] = $classstudentvar['course_branch'];
            $total_count = 1;
            if (trim($classstudentvar['channel_name']) !== '') {
                $total_count_by_channel = 1;
                if ($classstudentvar['is_renewal'] == 'Y') {
                    $renewal_count_by_channel = 1;
                } else {
                    $renewal_count_by_channel = 0;
                }
                if ($classstudentvar['is_renewal_real'] == 'Y') {
                    $renewal_count_real_by_channel = 1;
                } else {
                    $renewal_count_real_by_channel = 0;
                }
            } else {
                $total_count_by_channel = 0;
                $renewal_count_by_channel = 0;
                $renewal_count_real_by_channel = 0;
            }
            if ($classstudentvar['is_renewal'] == 'Y') {
                $renewal_count = 1;
            } else {
                $renewal_count = 0;
            }

            if ($classstudentvar['is_renewal_real'] == 'Y') {
                $renewal_count_real = 1;
            } else {
                $renewal_count_real = 0;
            }
        }
    }
    $classarray['total_count'] = $total_count;
    $classarray['total_count_by_channel'] = $total_count_by_channel;
    $classarray['renewal_count'] = $renewal_count;
    $classarray['renewal_count_by_channel'] = $renewal_count_by_channel;

    $classarray['renewal_count_real'] = $renewal_count_real;
    $classarray['renewal_count_real_by_channel'] = $renewal_count_real_by_channel;
    $mainClassData[] = $classarray;

//    var_dump($mainClassData);

    //创建一个新的工作空间(sheet)
    $obj->createSheet();
    $obj->setactivesheetindex(1);

    if ($is_containsbreakoff == '1') {
        $obj->getActiveSheet()->setTitle('班级预估汇总(含拆班)');
    } else {
        $obj->getActiveSheet()->setTitle('班级预估汇总(不含拆班)');
    }

    foreach ($excelClassHeader as $key => $value) {
        $obj->getActiveSheet()->setCellValueExplicitByColumnAndRow($key, 1, $value);
    }

    $monthList = array();
    if (!is_array($monthRange) && $monthRange) {
        $obj->getActiveSheet()->setCellValueExplicitByColumnAndRow($key + count($excelClassHeader), 1, date('m', strtotime($monthRange . '01')) . '月');
        array_push($monthList, $monthRange);
    } else {
        foreach ($monthRange as $key => $value) {
            $obj->getActiveSheet()->setCellValueExplicitByColumnAndRow($key + count($excelClassHeader), 1, date('m', strtotime($value . '01')) . '月');
            array_push($monthList, $value);
        }
    }

    $obj->getActiveSheet()->getStyle('Q:' . IntToChr(18 + count($monthList)))
        ->getNumberFormat()->setFormatCode(PHPExcel_Style_NumberFormat::FORMAT_PERCENTAGE);

    $rowIndex = 1;
    $school_id = 0;
    $coursecat_id = 0;
    $class_endmonth = 0;

    $coursecat_count = 0;
    $renewal_coursecat_count = 0;
    $renewal_coursecat_real_count = 0;

    $schoolList = array();
    $mainSchoolData = array();
    $schoolcoursecatmontharray = array();
    $coursecatList = array();
    foreach ($mainClassData as $schoolclassvar) {
        $i = 0;
        $rowIndex++;

        $obj->getActiveSheet()
            ->setCellValueExplicit(IntToChr($i++) . ($rowIndex), $schoolclassvar['school_branch'], PHPExcel_Cell_DataType::TYPE_STRING)
            ->setCellValue(IntToChr($i++) . ($rowIndex), $schoolclassvar['school_cnname'])
            ->setCellValueExplicit(IntToChr($i++) . ($rowIndex), $schoolclassvar['class_branch'], PHPExcel_Cell_DataType::TYPE_STRING)
            ->setCellValue(IntToChr($i++) . ($rowIndex), $schoolclassvar['coursecat_branch'])
            ->setCellValue(IntToChr($i++) . ($rowIndex), $schoolclassvar['course_branch'])
            ->setCellValue(IntToChr($i++) . ($rowIndex), $schoolclassvar['class_enname'])
            ->setCellValue(IntToChr($i++) . ($rowIndex), $schoolclassvar['class_enddate'])
            ->setCellValue(IntToChr($i++) . ($rowIndex), $schoolclassvar['class_endmonth'])
            ->setCellValue(IntToChr($i++) . ($rowIndex), $schoolclassvar['main_teacher'])
            ->setCellValue(IntToChr($i++) . ($rowIndex), $schoolclassvar['total_count'])
            ->setCellValue(IntToChr($i++) . ($rowIndex), $schoolclassvar['total_count_by_channel'])
            ->setCellValue(IntToChr($i++) . ($rowIndex), $schoolclassvar['renewal_count'])
            ->setCellValue(IntToChr($i++) . ($rowIndex), $schoolclassvar['renewal_count_by_channel']);
        $remian_count = $schoolclassvar['total_count'] - $schoolclassvar['total_count_by_channel'];//非公益学生数
        $renewal_remian_count = $schoolclassvar['renewal_count'] - $schoolclassvar['renewal_count_by_channel'];//预估非公益学生续费数
        $renewal_remian_real_count = $schoolclassvar['renewal_count_real'] - $schoolclassvar['renewal_count_real_by_channel'];//实际非公益学生续费数
        if ($remian_count > 0) {
            $obj->getActiveSheet()
                ->setCellValue(IntToChr($i++) . ($rowIndex), $remian_count)
                ->setCellValue(IntToChr($i++) . ($rowIndex), $renewal_remian_real_count)
                ->setCellValue(IntToChr($i++) . ($rowIndex), $renewal_remian_count)
                ->setCellValueExplicit(IntToChr($i++) . ($rowIndex), round($renewal_remian_real_count / $remian_count, 2), PHPExcel_Cell_DataType::TYPE_NUMERIC)
                ->setCellValueExplicit(IntToChr($i++) . ($rowIndex), round($renewal_remian_count / $remian_count, 2), PHPExcel_Cell_DataType::TYPE_NUMERIC)
                ->setCellValueExplicit(IntToChr(18 + array_search($schoolclassvar['class_endmonth'], $monthList)) . ($rowIndex), round($renewal_remian_count / $remian_count, 2), PHPExcel_Cell_DataType::TYPE_NUMERIC);
        }

        if (!in_array($schoolclassvar['coursecat_branch'], $coursecatList)) {
            array_push($coursecatList, $schoolclassvar['coursecat_branch']);
        }

        if ($school_id == 0) {
            //首行
            $schoolcoursecatmontharray = array();
            $school_id = $schoolclassvar['school_id'];
            $coursecat_id = $schoolclassvar['coursecat_id'];
            $class_endmonth = $schoolclassvar['class_endmonth'];
            $schoolcoursecatmontharray['school_id'] = $school_id;
            $schoolcoursecatmontharray['coursecat_id'] = $coursecat_id;
            $schoolcoursecatmontharray['school_branch'] = $schoolclassvar['school_branch'];
            $schoolcoursecatmontharray['school_cnname'] = $schoolclassvar['school_cnname'];
            $schoolcoursecatmontharray['class_endmonth'] = $class_endmonth;
            $schoolcoursecatmontharray['coursecat_branch'] = $schoolclassvar['coursecat_branch'];
        }

        if ($schoolclassvar['school_id'] == $school_id && $schoolclassvar['coursecat_id'] == $coursecat_id && $schoolclassvar['class_endmonth'] == $class_endmonth) {
            $coursecat_count += $remian_count;
            $renewal_coursecat_count += $renewal_remian_count;
            $renewal_coursecat_real_count += $renewal_remian_real_count;
        } else {
            $schoolcoursecatmontharray['coursecat_count'] = $coursecat_count;
            $schoolcoursecatmontharray['renewal_coursecat_count'] = $renewal_coursecat_count;
            $schoolcoursecatmontharray['renewal_coursecat_real_count'] = $renewal_coursecat_real_count;
            $mainSchoolData[] = $schoolcoursecatmontharray;

            //新的一行
            $schoolcoursecatmontharray = array();
            $school_id = $schoolclassvar['school_id'];
            $coursecat_id = $schoolclassvar['coursecat_id'];
            $class_endmonth = $schoolclassvar['class_endmonth'];
            $schoolcoursecatmontharray['school_id'] = $school_id;
            $schoolcoursecatmontharray['coursecat_id'] = $coursecat_id;
            $schoolcoursecatmontharray['school_branch'] = $schoolclassvar['school_branch'];
            $schoolcoursecatmontharray['school_cnname'] = $schoolclassvar['school_cnname'];
            $schoolcoursecatmontharray['class_endmonth'] = $class_endmonth;
            $schoolcoursecatmontharray['coursecat_branch'] = $schoolclassvar['coursecat_branch'];
            $coursecat_count = $remian_count;
            $renewal_coursecat_count = $renewal_remian_count;
            $renewal_coursecat_real_count = $renewal_remian_real_count;
        }

        if (!in_array($school_id, $schoolList)) {
            array_push($schoolList, $school_id);
        }
    }
    $schoolcoursecatmontharray['coursecat_count'] = $coursecat_count;
    $schoolcoursecatmontharray['renewal_coursecat_count'] = $renewal_coursecat_count;
    $schoolcoursecatmontharray['renewal_coursecat_real_count'] = $renewal_coursecat_real_count;
    $mainSchoolData[] = $schoolcoursecatmontharray;

    //创建一个新的工作空间(sheet)
    $obj->createSheet();
    $obj->setactivesheetindex(2);
    $obj->getActiveSheet()->freezePane('C4');
    $school_id = 0;

    if ($is_containsbreakoff == '1') {
        $obj->getActiveSheet()->setTitle('分校预估汇总(含拆班)');
    } else {
        $obj->getActiveSheet()->setTitle('分校预估汇总(不含拆班)');
    }

//    $obj->getActiveSheet()->setTitle('分校预估汇总');

    $obj->getActiveSheet()->setCellValueExplicitByColumnAndRow(0, 1, '导出时间：' . date("Y/m/d H:i"));
//    $obj->getActiveSheet()->setCellValueExplicitByColumnAndRow(1, 1, '主数据条数：' . count($mainSchoolData));
    $obj->getActiveSheet()->setCellValueExplicitByColumnAndRow(2, 1, '月数：' . count($monthList));
    $obj->getActiveSheet()->setCellValueExplicitByColumnAndRow(3, 1, '班种数：' . count($coursecatList));
    foreach ($excelSchoolHeader as $key => $value) {
        $obj->getActiveSheet()->setCellValueExplicitByColumnAndRow($key, 3, $value);
    }
    $obj->getActiveSheet()->setCellValueExplicitByColumnAndRow(0, 2, '基本资料');
    $obj->getActiveSheet()->mergeCells('A2:B2');

    $colname = IntToChr(count($coursecatList) * (count($monthList) + 2) * 2 + 4);
    $obj->getActiveSheet()->getStyle('A2:' . $colname . '3')->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_CENTER);

    foreach ($coursecatList as $coursecatkey => $coursecat) {
        $obj->getActiveSheet()->setCellValueExplicitByColumnAndRow($coursecatkey * count($monthList) + 2, 2, $coursecat);

        $colname1 = IntToChr($coursecatkey * count($monthList) + 2);
        $colname2 = IntToChr(($coursecatkey + 1) * count($monthList) + 1);
        $obj->getActiveSheet()->mergeCells($colname1 . '2:' . $colname2 . '2');

        foreach ($monthList as $monthkey => $month) {
            $obj->getActiveSheet()->setCellValueExplicitByColumnAndRow($coursecatkey * count($monthList) + $monthkey + 2, 3, date('m', strtotime($month . '01')) . '月');
        }
    }

    foreach ($monthList as $monthkey => $month) {
        $colname = IntToChr(count($coursecatList) * count($monthList) + $monthkey * 2 + 2);
        $colname1 = IntToChr(count($coursecatList) * count($monthList) + $monthkey * 2 + 3);
        $obj->getActiveSheet()
            ->setCellValueExplicitByColumnAndRow(count($coursecatList) * count($monthList) + $monthkey * 2 + 2, 2, date('m', strtotime($month . '01')) . '月合计')
            ->setCellValueExplicitByColumnAndRow(count($coursecatList) * count($monthList) + $monthkey * 2 + 2, 3, '实际')
            ->setCellValueExplicitByColumnAndRow(count($coursecatList) * count($monthList) + $monthkey * 2 + 3, 3, '预估');
        $obj->getActiveSheet()->mergeCells($colname . '2:' . $colname1 . '2');
        $obj->getActiveSheet()->getStyle($colname . '2:' . $colname1 . '3')->getAlignment()->setWrapText(true);
    }
    $colname = IntToChr((count($coursecatList) + 2) * count($monthList) + 2);
    $colname1 = IntToChr((count($coursecatList) + 2) * count($monthList) + 3);
    $obj->getActiveSheet()
        ->setCellValueExplicitByColumnAndRow((count($coursecatList) + 2) * count($monthList) + 2, 2, '总合计')
        ->setCellValueExplicitByColumnAndRow((count($coursecatList) + 2) * count($monthList) + 2, 3, '实际')
        ->setCellValueExplicitByColumnAndRow((count($coursecatList) + 2) * count($monthList) + 3, 3, '预估');
    $obj->getActiveSheet()->mergeCells($colname . '2:' . $colname1 . '2');
    $obj->getActiveSheet()->getStyle($colname . '2:' . $colname1 . '3')->getAlignment()->setWrapText(true);

    $obj->getActiveSheet()->getStyle('C:' . IntToChr((count($coursecatList) + 2) * count($monthList) + 3))
        ->getNumberFormat()->setFormatCode(PHPExcel_Style_NumberFormat::FORMAT_PERCENTAGE);
//    $obj->getActiveSheet()->getColumnDimension('A:' . IntToChr((count($coursecatList) + 2) * count($monthList) + 3))->setWidth(45);

    $school_id = 0;
    $rowNum = 3;//正式数据从第4行开始
    $i = 0;
    $month_key = 0;
    $coursecat_key = 0;
    $totalcalc_count = 0;
    $totalcalc_count_renewal = 0;
    $totalcalc_count_real_renewal = 0;

    $monthdata = array();

    //用于总计行

    $toatalcalcdata = array();
    $toatalcalccell = array();
    $toatalcalccell['col_num'] = 1;
    $toatalcalcdata[] = $toatalcalccell;


    foreach ($mainSchoolData as $key => $value) {
        //新的学校，不是第一行--补齐上一行数据
        if ($value['school_id'] !== $school_id && $school_id !== 0) {
            //总计%
            $obj->getActiveSheet()
                ->setCellValueExplicit(IntToChr((count($coursecatList) + 2) * count($monthList) + 2) . ($rowNum), round($totalcalc_count_real_renewal / $totalcalc_count, 2), PHPExcel_Cell_DataType::TYPE_NUMERIC)
                ->setCellValueExplicit(IntToChr((count($coursecatList) + 2) * count($monthList) + 3) . ($rowNum), round($totalcalc_count_renewal / $totalcalc_count, 2), PHPExcel_Cell_DataType::TYPE_NUMERIC);

            $cellexists = 0;
            foreach ($toatalcalcdata as $key => $calcdata) {
                if ($calcdata['col_num'] == ((count($coursecatList) + 2) * count($monthList) + 2)) {
                    $cellexists = 1;
                    $calcdata['numerator'] += $totalcalc_count_real_renewal;
                    $calcdata['denominator'] += $totalcalc_count;
                    $toatalcalcdata[$key] = $calcdata;
                }
            }
            if ($cellexists == 0) {
                $calcdata = array();
                $calcdata['col_num'] = ((count($coursecatList) + 2) * count($monthList) + 2);
                $calcdata['numerator'] += $totalcalc_count_real_renewal;
                $calcdata['denominator'] += $totalcalc_count;
                $toatalcalcdata[] = $calcdata;
            }

            $cellexists = 0;
            foreach ($toatalcalcdata as $key => $calcdata) {
                if ($calcdata['col_num'] == ((count($coursecatList) + 2) * count($monthList) + 3)) {
                    $cellexists = 1;
                    $calcdata['numerator'] += $totalcalc_count_renewal;
                    $calcdata['denominator'] += $totalcalc_count;
                    $toatalcalcdata[$key] = $calcdata;
                }
            }
            if ($cellexists == 0) {
                $calcdata = array();
                $calcdata['col_num'] = ((count($coursecatList) + 2) * count($monthList) + 3);
                $calcdata['numerator'] += $totalcalc_count_renewal;
                $calcdata['denominator'] += $totalcalc_count;
                $toatalcalcdata[] = $calcdata;
            }

            //分月%
            if ($monthdata) {
                foreach ($monthdata as $monthlydata) {
                    if ($monthlydata['total_count'] > 0) {
                        $obj->getActiveSheet()
                            ->setCellValueExplicit(IntToChr(count($coursecatList) * count($monthList) + $monthlydata['month_key'] * 2 + 2) . ($rowNum), round($monthlydata['renewal_real_count'] / $monthlydata['total_count'], 2), PHPExcel_Cell_DataType::TYPE_NUMERIC)
                            ->setCellValueExplicit(IntToChr(count($coursecatList) * count($monthList) + $monthlydata['month_key'] * 2 + 3) . ($rowNum), round($monthlydata['renewal_count'] / $monthlydata['total_count'], 2), PHPExcel_Cell_DataType::TYPE_NUMERIC);

                        $cellexists = 0;
                        foreach ($toatalcalcdata as $key => $calcdata) {
                            if ($calcdata['col_num'] == (count($coursecatList) * count($monthList) + $monthlydata['month_key'] * 2 + 2)) {
                                $cellexists = 1;
                                $calcdata['numerator'] += $monthlydata['renewal_real_count'];
                                $calcdata['denominator'] += $monthlydata['total_count'];
                                $toatalcalcdata[$key] = $calcdata;
                            }
                        }
                        if ($cellexists == 0) {
                            $calcdata = array();
                            $calcdata['col_num'] = (count($coursecatList) * count($monthList) + $monthlydata['month_key'] * 2 + 2);
                            $calcdata['numerator'] += $monthlydata['renewal_real_count'];
                            $calcdata['denominator'] += $monthlydata['total_count'];
                            $toatalcalcdata[] = $calcdata;
                        }

                        $cellexists = 0;
                        foreach ($toatalcalcdata as $key => $calcdata) {
                            if ($calcdata['col_num'] == (count($coursecatList) * count($monthList) + $monthlydata['month_key'] * 2 + 3)) {
                                $cellexists = 1;
                                $calcdata['numerator'] += $monthlydata['renewal_count'];
                                $calcdata['denominator'] += $monthlydata['total_count'];
                                $toatalcalcdata[$key] = $calcdata;
                            }
                        }
                        if ($cellexists == 0) {
                            $calcdata = array();
                            $calcdata['col_num'] = (count($coursecatList) * count($monthList) + $monthlydata['month_key'] * 2 + 3);
                            $calcdata['numerator'] += $monthlydata['renewal_count'];
                            $calcdata['denominator'] += $monthlydata['total_count'];
                            $toatalcalcdata[] = $calcdata;
                        }
                    }
                }
            }
            $totalcalc_count = 0;
            $totalcalc_count_renewal = 0;
            $totalcalc_count_real_renewal = 0;
            $monthdata = array();
        }
        //新的学校--显示学校信息
        if ($school_id == 0 || $value['school_id'] !== $school_id) {
            $school_id = $value['school_id'];
            $i = 0;
            $rowNum++;
            $obj->getActiveSheet()
                ->setCellValueExplicit(IntToChr($i++) . ($rowNum), $value['school_branch'], PHPExcel_Cell_DataType::TYPE_STRING)
                ->setCellValue(IntToChr($i++) . ($rowNum), $value['school_cnname']);

            $monthdata = array();
        }

        if ($value['school_id'] == $school_id) {
            $month_key = array_search($value['class_endmonth'], $monthList);
            $coursecat_key = array_search($value['coursecat_branch'], $coursecatList);
            if ($value['coursecat_count'] > 0) {
                $obj->getActiveSheet()
                    ->setCellValueExplicit(IntToChr($coursecat_key * count($monthList) + $month_key + 2) . ($rowNum), round($value['renewal_coursecat_count'] / $value['coursecat_count'], 2), PHPExcel_Cell_DataType::TYPE_NUMERIC);

                $cellexists = 0;
                foreach ($toatalcalcdata as $key => $calcdata) {
                    if ($calcdata['col_num'] == ($coursecat_key * count($monthList) + $month_key + 2)) {
                        $cellexists = 1;
                        $calcdata['numerator'] += $value['renewal_coursecat_count'];
                        $calcdata['denominator'] += $value['coursecat_count'];
                        $toatalcalcdata[$key] = $calcdata;
                    }
                }
                if ($cellexists == 0) {
                    $calcdata = array();
                    $calcdata['col_num'] = ($coursecat_key * count($monthList) + $month_key + 2);
                    $calcdata['numerator'] += $value['renewal_coursecat_count'];
                    $calcdata['denominator'] += $value['coursecat_count'];
                    $toatalcalcdata[] = $calcdata;
                }
            }
            $totalcalc_count += $value['coursecat_count'];
            $totalcalc_count_renewal += $value['renewal_coursecat_count'];
            $totalcalc_count_real_renewal += $value['renewal_coursecat_real_count'];

            if ($monthdata) {
                $monthexists = 0;
                foreach ($monthdata as $key => $monthlydata) {
                    if ($monthlydata['month_key'] == $month_key) {
                        $monthexists = 1;
                        $monthlydata['month_key'] = $month_key;
                        $monthlydata['total_count'] += $value['coursecat_count'];
                        $monthlydata['renewal_count'] += $value['renewal_coursecat_count'];
                        $monthlydata['renewal_real_count'] += $value['renewal_coursecat_real_count'];
                        $monthdata[$key] = $monthlydata;
                    }
                }
                if ($monthexists == 0 && $value['coursecat_count'] > 0) {
                    $monthlydata = array();
                    $monthlydata['month_key'] = $month_key;
                    $monthlydata['total_count'] += $value['coursecat_count'];
                    $monthlydata['renewal_count'] += $value['renewal_coursecat_count'];
                    $monthlydata['renewal_real_count'] += $value['renewal_coursecat_real_count'];
                    $monthdata[] = $monthlydata;
                }
            } else {
                if ($value['coursecat_count'] > 0) {
                    $monthlydata = array();
                    $monthlydata['month_key'] = $month_key;
                    $monthlydata['total_count'] += $value['coursecat_count'];
                    $monthlydata['renewal_count'] += $value['renewal_coursecat_count'];
                    $monthlydata['renewal_real_count'] += $value['renewal_coursecat_real_count'];
                    $monthdata[] = $monthlydata;
                }
            }
        }
    }

    $obj->getActiveSheet()
        ->setCellValueExplicit(IntToChr((count($coursecatList) + 2) * count($monthList) + 2) . ($rowNum), round($totalcalc_count_real_renewal / $totalcalc_count, 2), PHPExcel_Cell_DataType::TYPE_NUMERIC)
        ->setCellValueExplicit(IntToChr((count($coursecatList) + 2) * count($monthList) + 3) . ($rowNum), round($totalcalc_count_renewal / $totalcalc_count, 2), PHPExcel_Cell_DataType::TYPE_NUMERIC);

    $cellexists = 0;
    foreach ($toatalcalcdata as $key => $calcdata) {
        if ($calcdata['col_num'] == ((count($coursecatList) + 2) * count($monthList) + 2)) {
            $cellexists = 1;
            $calcdata['numerator'] += $totalcalc_count_real_renewal;
            $calcdata['denominator'] += $totalcalc_count;
            $toatalcalcdata[$key] = $calcdata;
        }
    }
    if ($cellexists == 0) {
        $calcdata = array();
        $calcdata['col_num'] = ((count($coursecatList) + 2) * count($monthList) + 2);
        $calcdata['numerator'] += $totalcalc_count_real_renewal;
        $calcdata['denominator'] += $totalcalc_count;
        $toatalcalcdata[] = $calcdata;
    }

    $cellexists = 0;
    foreach ($toatalcalcdata as $key => $calcdata) {
        if ($calcdata['col_num'] == ((count($coursecatList) + 2) * count($monthList) + 3)) {
            $cellexists = 1;
            $calcdata['numerator'] += $totalcalc_count_renewal;
            $calcdata['denominator'] += $totalcalc_count;
            $toatalcalcdata[$key] = $calcdata;
        }
    }
    if ($cellexists == 0) {
        $calcdata = array();
        $calcdata['col_num'] = ((count($coursecatList) + 2) * count($monthList) + 3);
        $calcdata['numerator'] += $totalcalc_count_renewal;
        $calcdata['denominator'] += $totalcalc_count;
        $toatalcalcdata[] = $calcdata;
    }

    if ($monthdata) {
        foreach ($monthdata as $monthlydata) {
            if ($monthlydata['total_count'] > 0) {
                $obj->getActiveSheet()
                    ->setCellValueExplicit(IntToChr(count($coursecatList) * count($monthList) + $monthlydata['month_key'] * 2 + 2) . ($rowNum), round($monthlydata['renewal_real_count'] / $monthlydata['total_count'], 2), PHPExcel_Cell_DataType::TYPE_NUMERIC)
                    ->setCellValueExplicit(IntToChr(count($coursecatList) * count($monthList) + $monthlydata['month_key'] * 2 + 3) . ($rowNum), round($monthlydata['renewal_count'] / $monthlydata['total_count'], 2), PHPExcel_Cell_DataType::TYPE_NUMERIC);

                $cellexists = 0;
                foreach ($toatalcalcdata as $key => $calcdata) {
                    if ($calcdata['col_num'] == (count($coursecatList) * count($monthList) + $monthlydata['month_key'] * 2 + 2)) {
                        $cellexists = 1;
                        $calcdata['numerator'] += $monthlydata['renewal_real_count'];
                        $calcdata['denominator'] += $monthlydata['total_count'];
                        $toatalcalcdata[$key] = $calcdata;
                    }
                }
                if ($cellexists == 0) {
                    $calcdata = array();
                    $calcdata['col_num'] = (count($coursecatList) * count($monthList) + $monthlydata['month_key'] * 2 + 2);
                    $calcdata['numerator'] += $monthlydata['renewal_real_count'];
                    $calcdata['denominator'] += $monthlydata['total_count'];
                    $toatalcalcdata[] = $calcdata;
                }

                $cellexists = 0;
                foreach ($toatalcalcdata as $key => $calcdata) {
                    if ($calcdata['col_num'] == (count($coursecatList) * count($monthList) + $monthlydata['month_key'] * 2 + 3)) {
                        $cellexists = 1;
                        $calcdata['numerator'] += $monthlydata['renewal_count'];
                        $calcdata['denominator'] += $monthlydata['total_count'];
                        $toatalcalcdata[$key] = $calcdata;
                    }
                }
                if ($cellexists == 0) {
                    $calcdata = array();
                    $calcdata['col_num'] = (count($coursecatList) * count($monthList) + $monthlydata['month_key'] * 2 + 3);
                    $calcdata['numerator'] += $monthlydata['renewal_count'];
                    $calcdata['denominator'] += $monthlydata['total_count'];
                    $toatalcalcdata[] = $calcdata;
                }
            }
        }
    }

    //添加合计行
    $rowNum++;
    if ($toatalcalcdata) {
        foreach ($toatalcalcdata as $celldata) {
            if ($celldata['col_num'] == 1) {
                $obj->getActiveSheet()
                    ->setCellValueExplicit(IntToChr($celldata['col_num']) . ($rowNum), '合计', PHPExcel_Cell_DataType::TYPE_STRING);
            } else {
                $obj->getActiveSheet()
                    ->setCellValueExplicit(IntToChr($celldata['col_num']) . ($rowNum), round($celldata['numerator'] / $celldata['denominator'], 2), PHPExcel_Cell_DataType::TYPE_NUMERIC);
            }
        }
    }


    //导出
    ob_clean();
    $objWriter = new PHPExcel_Writer_Excel2007($obj);
    header("Pragma: public");
    header("Expires: 0");
    header("Cache-Control:must-revalidate, post-check=0, pre-check=0");
    header("Content-Type:application/force-download");
    header("Content-Type:application/vnd.ms-excel; charset=UTF-8");
    header("Content-Type:application/octet-stream");
    header("Content-Type:application/download");
    header("Content-Disposition:attachment;filename=" . urlencode($fileName) . urlencode($fileType));
    header("Content-Transfer-Encoding:binary");
    $objWriter->save("php://output");
    exit;
}

function excelcustom_studEndcalcEstimate($is_containsbreakoff, $excelDetailHeader, $excelClassHeader, $excelSchoolHeader, $mainData, $monthRange, $fileName, $fileType)
{
    $cacheMethod = PHPExcel_CachedObjectStorageFactory::cache_in_memory_gzip;
    $cacheSettings = array('memoryCacheSize' => '512MB');
    PHPExcel_Settings::setCacheStorageMethod($cacheMethod, $cacheSettings);
    $obj = new \PHPExcel();

    // 以下内容是excel文件的信息描述信息
    $obj->getProperties()->setCreator(''); //设置创建者
    $obj->getProperties()->setLastModifiedBy(''); //设置修改者
    $obj->getProperties()->setTitle(''); //设置标题
    $obj->getProperties()->setSubject(''); //设置主题
    $obj->getProperties()->setDescription(''); //设置描述
    $obj->getProperties()->setKeywords('');//设置关键词
    $obj->getProperties()->setCategory('');//设置类型

    // 设置当前sheet
    $obj->setActiveSheetIndex(0);

    // 设置当前sheet的名称
    if ($is_containsbreakoff == '1') {
        $obj->getActiveSheet()->setTitle('学员留班明细(含拆班)');
    } else {
        $obj->getActiveSheet()->setTitle('学员留班明细(不含拆班)');
    }

    $colIndex = 0;
    $rowIndex = 0;

    foreach ($excelDetailHeader as $key => $value) {
        $obj->getActiveSheet()->setCellValueExplicitByColumnAndRow($key, 1, $value);
    }

    $rowIndex++;
    $class_id = 0;

    $total_count = 0;
    $total_count_by_channel = 0;
    $renewal_count = 0;
    $renewal_count_by_channel = 0;

    $mainClassData = array();
    $classarray = array();
    foreach ($mainData as $classstudentvar) {
        $i = 0;
        $rowIndex++;

        $obj->getActiveSheet()
            ->setCellValueExplicit(IntToChr($i++) . ($rowIndex), $classstudentvar['school_branch'], PHPExcel_Cell_DataType::TYPE_STRING)
            ->setCellValue(IntToChr($i++) . ($rowIndex), $classstudentvar['school_cnname'])
            ->setCellValueExplicit(IntToChr($i++) . ($rowIndex), $classstudentvar['student_branch'], PHPExcel_Cell_DataType::TYPE_STRING)
            ->setCellValue(IntToChr($i++) . ($rowIndex), $classstudentvar['student_cnname'])
            ->setCellValue(IntToChr($i++) . ($rowIndex), $classstudentvar['student_enname'])
            ->setCellValue(IntToChr($i++) . ($rowIndex), $classstudentvar['channel_name'])
            ->setCellValue(IntToChr($i++) . ($rowIndex), $classstudentvar['is_renewal'])
            ->setCellValue(IntToChr($i++) . ($rowIndex), $classstudentvar['study_nexttimes'])
            ->setCellValue(IntToChr($i++) . ($rowIndex), $classstudentvar['study_nextprice'])
            ->setCellValue(IntToChr($i++) . ($rowIndex), $classstudentvar['study_upgraderate'])
            ->setCellValue(IntToChr($i++) . ($rowIndex), $classstudentvar['connect_times'])
            ->setCellValue(IntToChr($i++) . ($rowIndex), $classstudentvar['track_note'])
            ->setCellValueExplicit(IntToChr($i++) . ($rowIndex), $classstudentvar['class_branch'], PHPExcel_Cell_DataType::TYPE_STRING)
            ->setCellValue(IntToChr($i++) . ($rowIndex), $classstudentvar['class_cnname'])
            ->setCellValue(IntToChr($i++) . ($rowIndex), $classstudentvar['class_enname'])
            ->setCellValue(IntToChr($i++) . ($rowIndex), $classstudentvar['class_enddate'])
            ->setCellValue(IntToChr($i++) . ($rowIndex), $classstudentvar['study_endday'])
            ->setCellValue(IntToChr($i++) . ($rowIndex), $classstudentvar['main_teacher'])
            ->setCellValue(IntToChr($i++) . ($rowIndex), $classstudentvar['course_branch'])
            ->setCellValue(IntToChr($i++) . ($rowIndex), $classstudentvar['course_cnname'])
            ->setCellValue(IntToChr($i++) . ($rowIndex), $classstudentvar['coursecat_branch'])
            ->setCellValue(IntToChr($i++) . ($rowIndex), $classstudentvar['coursecat_cnname'])
            ->setCellValue(IntToChr($i++) . ($rowIndex), $classstudentvar['coursetype_branch'])
            ->setCellValue(IntToChr($i++) . ($rowIndex), $classstudentvar['coursetype_cnname'])
            ->setCellValue(IntToChr($i++) . ($rowIndex), $classstudentvar['class_num'])
            ->setCellValue(IntToChr($i++) . ($rowIndex), $classstudentvar['course_class_num']);

        if ($class_id == 0) {
            //首行
            $classarray = array();
            $classarray['school_id'] = $classstudentvar['school_id'];
            $classarray['coursetype_id'] = $classstudentvar['coursetype_id'];
            $classarray['coursecat_id'] = $classstudentvar['coursecat_id'];
            $classarray['school_branch'] = $classstudentvar['school_branch'];
            $classarray['school_tagbak'] = $classstudentvar['school_tagbak'];
            $classarray['school_cnname'] = $classstudentvar['school_cnname'];
            $classarray['class_branch'] = $classstudentvar['class_branch'];
            $classarray['class_enname'] = $classstudentvar['class_enname'];
            $classarray['class_enddate'] = $classstudentvar['class_enddate'];
            $classarray['class_endmonth'] = date("Ym", strtotime($classstudentvar['class_enddate']));
            $classarray['main_teacher'] = $classstudentvar['main_teacher'];
            $classarray['coursecat_branch'] = $classstudentvar['coursecat_branch'];
            $classarray['course_branch'] = $classstudentvar['course_branch'];
            $class_id = $classstudentvar['class_id'];
        }

        if ($classstudentvar['class_id'] == $class_id) {
            $total_count++;
            if (trim($classstudentvar['channel_name']) !== '') {
                $total_count_by_channel++;
                if ($classstudentvar['is_renewal'] == 'Y') {
                    $renewal_count_by_channel++;
                }
            }
            if ($classstudentvar['is_renewal'] == 'Y') {
                $renewal_count++;
            }
        } else {
            $classarray['total_count'] = $total_count;
            $classarray['total_count_by_channel'] = $total_count_by_channel;
            $classarray['renewal_count'] = $renewal_count;
            $classarray['renewal_count_by_channel'] = $renewal_count_by_channel;
            $mainClassData[] = $classarray;

            //新的一行
            $class_id = $classstudentvar['class_id'];
            $classarray = array();
            $classarray['school_id'] = $classstudentvar['school_id'];
            $classarray['coursetype_id'] = $classstudentvar['coursetype_id'];
            $classarray['coursecat_id'] = $classstudentvar['coursecat_id'];
            $classarray['school_branch'] = $classstudentvar['school_branch'];
            $classarray['school_tagbak'] = $classstudentvar['school_tagbak'];
            $classarray['school_cnname'] = $classstudentvar['school_cnname'];
            $classarray['class_branch'] = $classstudentvar['class_branch'];
            $classarray['class_enname'] = $classstudentvar['class_enname'];
            $classarray['class_enddate'] = $classstudentvar['class_enddate'];
            $classarray['class_endmonth'] = date("Ym", strtotime($classstudentvar['class_enddate']));
            $classarray['main_teacher'] = $classstudentvar['main_teacher'];
            $classarray['coursecat_branch'] = $classstudentvar['coursecat_branch'];
            $classarray['course_branch'] = $classstudentvar['course_branch'];
            $total_count = 1;
            if (trim($classstudentvar['channel_name']) !== '') {
                $total_count_by_channel = 1;
                if ($classstudentvar['is_renewal'] == 'Y') {
                    $renewal_count_by_channel = 1;
                } else {
                    $renewal_count_by_channel = 0;
                }
            } else {
                $total_count_by_channel = 0;
                $renewal_count_by_channel = 0;
            }
            if ($classstudentvar['is_renewal'] == 'Y') {
                $renewal_count = 1;
            } else {
                $renewal_count = 0;
            }
        }
    }
    $classarray['total_count'] = $total_count;
    $classarray['total_count_by_channel'] = $total_count_by_channel;
    $classarray['renewal_count'] = $renewal_count;
    $classarray['renewal_count_by_channel'] = $renewal_count_by_channel;

    $mainClassData[] = $classarray;

    //创建一个新的工作空间(sheet)
    $obj->createSheet();
    $obj->setactivesheetindex(1);

    if ($is_containsbreakoff == '1') {
        $obj->getActiveSheet()->setTitle('班级实际留班汇总(含拆班)');
    } else {
        $obj->getActiveSheet()->setTitle('班级实际留班汇总(不含拆班)');
    }

    foreach ($excelClassHeader as $key => $value) {
        $obj->getActiveSheet()->setCellValueExplicitByColumnAndRow($key, 1, $value);
    }

    $monthList = array();
    if (!is_array($monthRange) && $monthRange) {
        $obj->getActiveSheet()->setCellValueExplicitByColumnAndRow($key + count($excelClassHeader), 1, date('m', strtotime($monthRange . '01')) . '月');
        array_push($monthList, $monthRange);
    } else {
        foreach ($monthRange as $key => $value) {
            $obj->getActiveSheet()->setCellValueExplicitByColumnAndRow($key + count($excelClassHeader), 1, date('m', strtotime($value . '01')) . '月');
            array_push($monthList, $value);
        }
    }

    $obj->getActiveSheet()->getStyle('P:' . IntToChr(16 + count($monthList)))
        ->getNumberFormat()->setFormatCode(PHPExcel_Style_NumberFormat::FORMAT_PERCENTAGE);

    $rowIndex = 1;
    $school_id = 0;
    $coursecat_id = 0;
    $class_endmonth = 0;

    $coursecat_count = 0;
    $renewal_coursecat_count = 0;

    $schoolList = array();
    $mainSchoolData = array();
    $schoolcoursecatmontharray = array();
    $coursecatList = array();
    foreach ($mainClassData as $schoolclassvar) {
        $i = 0;
        $rowIndex++;

        $obj->getActiveSheet()
            ->setCellValueExplicit(IntToChr($i++) . ($rowIndex), $schoolclassvar['school_branch'], PHPExcel_Cell_DataType::TYPE_STRING)
            ->setCellValue(IntToChr($i++) . ($rowIndex), $schoolclassvar['school_cnname'])
            ->setCellValueExplicit(IntToChr($i++) . ($rowIndex), $schoolclassvar['class_branch'], PHPExcel_Cell_DataType::TYPE_STRING)
            ->setCellValue(IntToChr($i++) . ($rowIndex), $schoolclassvar['coursecat_branch'])
            ->setCellValue(IntToChr($i++) . ($rowIndex), $schoolclassvar['course_branch'])
            ->setCellValue(IntToChr($i++) . ($rowIndex), $schoolclassvar['class_enname'])
            ->setCellValue(IntToChr($i++) . ($rowIndex), $schoolclassvar['class_enddate'])
            ->setCellValue(IntToChr($i++) . ($rowIndex), $schoolclassvar['class_endmonth'])
            ->setCellValue(IntToChr($i++) . ($rowIndex), $schoolclassvar['main_teacher'])
            ->setCellValue(IntToChr($i++) . ($rowIndex), $schoolclassvar['total_count'])
            ->setCellValue(IntToChr($i++) . ($rowIndex), $schoolclassvar['total_count_by_channel'])
            ->setCellValue(IntToChr($i++) . ($rowIndex), $schoolclassvar['renewal_count'])
            ->setCellValue(IntToChr($i++) . ($rowIndex), $schoolclassvar['renewal_count_by_channel']);
        $remian_count = $schoolclassvar['total_count'] - $schoolclassvar['total_count_by_channel'];//非公益学生数
        $renewal_remian_count = $schoolclassvar['renewal_count'] - $schoolclassvar['renewal_count_by_channel'];//预估非公益学生续费数

        if ($remian_count > 0) {
            $obj->getActiveSheet()
                ->setCellValue(IntToChr($i++) . ($rowIndex), $remian_count)
                ->setCellValue(IntToChr($i++) . ($rowIndex), $renewal_remian_count)
                ->setCellValueExplicit(IntToChr($i++) . ($rowIndex), round($renewal_remian_count / $remian_count, 2), PHPExcel_Cell_DataType::TYPE_NUMERIC)
                ->setCellValueExplicit(IntToChr(16 + array_search($schoolclassvar['class_endmonth'], $monthList)) . ($rowIndex), round($renewal_remian_count / $remian_count, 2), PHPExcel_Cell_DataType::TYPE_NUMERIC);
        }

        if (!in_array($schoolclassvar['coursecat_branch'], $coursecatList)) {
            array_push($coursecatList, $schoolclassvar['coursecat_branch']);
        }

        if ($school_id == 0) {
            //首行
            $schoolcoursecatmontharray = array();
            $school_id = $schoolclassvar['school_id'];
            $coursecat_id = $schoolclassvar['coursecat_id'];
            $class_endmonth = $schoolclassvar['class_endmonth'];
            $schoolcoursecatmontharray['school_id'] = $school_id;
            $schoolcoursecatmontharray['coursecat_id'] = $coursecat_id;
            $schoolcoursecatmontharray['school_branch'] = $schoolclassvar['school_branch'];
            $schoolcoursecatmontharray['school_tagbak'] = $schoolclassvar['school_tagbak'];
            $schoolcoursecatmontharray['school_cnname'] = $schoolclassvar['school_cnname'];
            $schoolcoursecatmontharray['class_endmonth'] = $class_endmonth;
            $schoolcoursecatmontharray['coursecat_branch'] = $schoolclassvar['coursecat_branch'];
        }

        if ($schoolclassvar['school_id'] == $school_id && $schoolclassvar['coursecat_id'] == $coursecat_id && $schoolclassvar['class_endmonth'] == $class_endmonth) {
            $coursecat_count += $remian_count;
            $renewal_coursecat_count += $renewal_remian_count;
        } else {
            $schoolcoursecatmontharray['coursecat_count'] = $coursecat_count;
            $schoolcoursecatmontharray['renewal_coursecat_count'] = $renewal_coursecat_count;
            $mainSchoolData[] = $schoolcoursecatmontharray;

            //新的一行
            $schoolcoursecatmontharray = array();
            $school_id = $schoolclassvar['school_id'];
            $coursecat_id = $schoolclassvar['coursecat_id'];
            $class_endmonth = $schoolclassvar['class_endmonth'];
            $schoolcoursecatmontharray['school_id'] = $school_id;
            $schoolcoursecatmontharray['coursecat_id'] = $coursecat_id;
            $schoolcoursecatmontharray['school_branch'] = $schoolclassvar['school_branch'];
            $schoolcoursecatmontharray['school_tagbak'] = $schoolclassvar['school_tagbak'];
            $schoolcoursecatmontharray['school_cnname'] = $schoolclassvar['school_cnname'];
            $schoolcoursecatmontharray['class_endmonth'] = $class_endmonth;
            $schoolcoursecatmontharray['coursecat_branch'] = $schoolclassvar['coursecat_branch'];
            $coursecat_count = $remian_count;
            $renewal_coursecat_count = $renewal_remian_count;
        }

        if (!in_array($school_id, $schoolList)) {
            array_push($schoolList, $school_id);
        }
    }
    $schoolcoursecatmontharray['coursecat_count'] = $coursecat_count;
    $schoolcoursecatmontharray['renewal_coursecat_count'] = $renewal_coursecat_count;
    $mainSchoolData[] = $schoolcoursecatmontharray;

    //创建一个新的工作空间(sheet)
    $obj->createSheet();
    $obj->setactivesheetindex(2);
    $obj->getActiveSheet()->freezePane('C4');
    $school_id = 0;

    if ($is_containsbreakoff == '1') {
        $obj->getActiveSheet()->setTitle('分校实际留班汇总(含拆班)');
    } else {
        $obj->getActiveSheet()->setTitle('分校实际留班汇总(不含拆班)');
    }

    $obj->getActiveSheet()->setCellValueExplicitByColumnAndRow(0, 1, '导出时间：' . date("Y/m/d H:i"));
    $obj->getActiveSheet()->setCellValueExplicitByColumnAndRow(2, 1, '月数：' . count($monthList));
    $obj->getActiveSheet()->setCellValueExplicitByColumnAndRow(3, 1, '班种数：' . count($coursecatList));
    foreach ($excelSchoolHeader as $key => $value) {
        $obj->getActiveSheet()->setCellValueExplicitByColumnAndRow($key, 3, $value);
    }
    $obj->getActiveSheet()->setCellValueExplicitByColumnAndRow(0, 2, '基本资料');
    $obj->getActiveSheet()->mergeCells('A2:B2');

    $colname = IntToChr(count($coursecatList) * (count($monthList) + 2) * 2 + 4);
    $obj->getActiveSheet()->getStyle('A2:' . $colname . '3')->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_CENTER);

    foreach ($coursecatList as $coursecatkey => $coursecat) {
        $obj->getActiveSheet()->setCellValueExplicitByColumnAndRow($coursecatkey * count($monthList) + 2, 2, $coursecat);

        $colname1 = IntToChr($coursecatkey * count($monthList) + 2);
        $colname2 = IntToChr(($coursecatkey + 1) * count($monthList) + 1);
        $obj->getActiveSheet()->mergeCells($colname1 . '2:' . $colname2 . '2');

        foreach ($monthList as $monthkey => $month) {
            $obj->getActiveSheet()->setCellValueExplicitByColumnAndRow($coursecatkey * count($monthList) + $monthkey + 2, 3, date('m', strtotime($month . '01')) . '月');
        }
    }

    foreach ($monthList as $monthkey => $month) {
        $colname = IntToChr(count($coursecatList) * count($monthList) + $monthkey + 2);
        $colname1 = IntToChr(count($coursecatList) * count($monthList) + $monthkey + 3);
        $obj->getActiveSheet()
            ->setCellValueExplicitByColumnAndRow(count($coursecatList) * count($monthList) + $monthkey + 2, 2, date('m', strtotime($month . '01')) . '月合计')
            ->setCellValueExplicitByColumnAndRow(count($coursecatList) * count($monthList) + $monthkey + 2, 3, '实际');
        $obj->getActiveSheet()->getStyle($colname . '2:' . $colname1 . '3')->getAlignment()->setWrapText(true);
    }
    $colname = IntToChr((count($coursecatList) + 1) * count($monthList) + 2);
    $colname1 = IntToChr((count($coursecatList) + 1) * count($monthList) + 3);
    $obj->getActiveSheet()
        ->setCellValueExplicitByColumnAndRow((count($coursecatList) + 1) * count($monthList) + 2, 2, '总合计')
        ->setCellValueExplicitByColumnAndRow((count($coursecatList) + 1) * count($monthList) + 2, 3, '实际')
        ->setCellValueExplicitByColumnAndRow((count($coursecatList) + 1) * count($monthList) + 3, 3, '督导');
    $obj->getActiveSheet()->getStyle($colname . '2:' . $colname1 . '3')->getAlignment()->setWrapText(true);

    $obj->getActiveSheet()->getStyle('C:' . IntToChr((count($coursecatList) + 1) * count($monthList) + 3))
        ->getNumberFormat()->setFormatCode(PHPExcel_Style_NumberFormat::FORMAT_PERCENTAGE);

    $school_id = 0;
    $rowNum = 3;//正式数据从第4行开始
    $i = 0;
    $month_key = 0;
    $coursecat_key = 0;
    $totalcalc_count = 0;
    $totalcalc_count_renewal = 0;

    $monthdata = array();

    //用于总计行

    $toatalcalcdata = array();
    $toatalcalccell = array();
    $toatalcalccell['col_num'] = 1;
    $toatalcalcdata[] = $toatalcalccell;


    foreach ($mainSchoolData as $key => $value) {
        //新的学校，不是第一行--补齐上一行数据
        if ($value['school_id'] !== $school_id && $school_id !== 0) {
            //总计%
            $obj->getActiveSheet()
                ->setCellValueExplicit(IntToChr((count($coursecatList) + 1) * count($monthList) + 2) . ($rowNum), round($totalcalc_count_renewal / $totalcalc_count, 2), PHPExcel_Cell_DataType::TYPE_NUMERIC);

//            $cellexists = 0;
//            foreach ($toatalcalcdata as $key => $calcdata) {
//                if ($calcdata['col_num'] == ((count($coursecatList) + 1) * count($monthList) + 2)) {
//                    $cellexists = 1;
//                    $calcdata['numerator'] += $totalcalc_count_renewal;
//                    $calcdata['denominator'] += $totalcalc_count;
//                    $toatalcalcdata[$key] = $calcdata;
//                }
//            }
//            if ($cellexists == 0) {
//                $calcdata = array();
//                $calcdata['col_num'] = ((count($coursecatList) + 1) * count($monthList) + 2);
//                $calcdata['numerator'] += $totalcalc_count_renewal;
//                $calcdata['denominator'] += $totalcalc_count;
//                $toatalcalcdata[] = $calcdata;
//            }

            //分月%
            if ($monthdata) {
                foreach ($monthdata as $monthlydata) {
                    if ($monthlydata['total_count'] > 0) {
                        $obj->getActiveSheet()
                            ->setCellValueExplicit(IntToChr(count($coursecatList) * count($monthList) + $monthlydata['month_key'] + 2) . ($rowNum), round($monthlydata['renewal_count'] / $monthlydata['total_count'], 2), PHPExcel_Cell_DataType::TYPE_NUMERIC);

//                        $cellexists = 0;
//                        foreach ($toatalcalcdata as $key => $calcdata) {
//                            if ($calcdata['col_num'] == (count($coursecatList) * count($monthList) + $monthlydata['month_key'] + 2)) {
//                                $cellexists = 1;
//                                $calcdata['numerator'] += $monthlydata['renewal_count'];
//                                $calcdata['denominator'] += $monthlydata['total_count'];
//                                $toatalcalcdata[$key] = $calcdata;
//                            }
//                        }
//                        if ($cellexists == 0) {
//                            $calcdata = array();
//                            $calcdata['col_num'] = (count($coursecatList) * count($monthList) + $monthlydata['month_key'] + 2);
//                            $calcdata['numerator'] += $monthlydata['renewal_count'];
//                            $calcdata['denominator'] += $monthlydata['total_count'];
//                            $toatalcalcdata[] = $calcdata;
//                        }
                    }
                }
            }
            $totalcalc_count = 0;
            $totalcalc_count_renewal = 0;
            $monthdata = array();
        }
        //新的学校--显示学校信息
        if ($school_id == 0 || $value['school_id'] !== $school_id) {
            $school_id = $value['school_id'];
            $i = 0;
            $rowNum++;
            $obj->getActiveSheet()
                ->setCellValueExplicit(IntToChr($i++) . ($rowNum), $value['school_branch'], PHPExcel_Cell_DataType::TYPE_STRING)
                ->setCellValue(IntToChr($i++) . ($rowNum), $value['school_cnname'])
                ->setCellValue(IntToChr((count($coursecatList) + 1) * count($monthList) + 3) . ($rowNum), $value['school_tagbak']);

            $monthdata = array();
        }

        if ($value['school_id'] == $school_id) {
            $month_key = array_search($value['class_endmonth'], $monthList);
            $coursecat_key = array_search($value['coursecat_branch'], $coursecatList);
            if ($value['coursecat_count'] > 0) {
                $obj->getActiveSheet()
                    ->setCellValueExplicit(IntToChr($coursecat_key * count($monthList) + $month_key + 2) . ($rowNum), round($value['renewal_coursecat_count'] / $value['coursecat_count'], 2), PHPExcel_Cell_DataType::TYPE_NUMERIC);

//                $cellexists = 0;
//                foreach ($toatalcalcdata as $key => $calcdata) {
//                    if ($calcdata['col_num'] == ($coursecat_key * count($monthList) + $month_key + 2)) {
//                        $cellexists = 1;
//                        $calcdata['numerator'] += $value['renewal_coursecat_count'];
//                        $calcdata['denominator'] += $value['coursecat_count'];
//                        $toatalcalcdata[$key] = $calcdata;
//                    }
//                }
//                if ($cellexists == 0) {
//                    $calcdata = array();
//                    $calcdata['col_num'] = ($coursecat_key * count($monthList) + $month_key + 2);
//                    $calcdata['numerator'] += $value['renewal_coursecat_count'];
//                    $calcdata['denominator'] += $value['coursecat_count'];
//                    $toatalcalcdata[] = $calcdata;
//                }
            }
            $totalcalc_count += $value['coursecat_count'];
            $totalcalc_count_renewal += $value['renewal_coursecat_count'];

            if ($monthdata) {
                $monthexists = 0;
                foreach ($monthdata as $key => $monthlydata) {
                    if ($monthlydata['month_key'] == $month_key) {
                        $monthexists = 1;
                        $monthlydata['month_key'] = $month_key;
                        $monthlydata['total_count'] += $value['coursecat_count'];
                        $monthlydata['renewal_count'] += $value['renewal_coursecat_count'];
                        $monthdata[$key] = $monthlydata;
                    }
                }
                if ($monthexists == 0 && $value['coursecat_count'] > 0) {
                    $monthlydata = array();
                    $monthlydata['month_key'] = $month_key;
                    $monthlydata['total_count'] += $value['coursecat_count'];
                    $monthlydata['renewal_count'] += $value['renewal_coursecat_count'];
                    $monthdata[] = $monthlydata;
                }
            } else {
                if ($value['coursecat_count'] > 0) {
                    $monthlydata = array();
                    $monthlydata['month_key'] = $month_key;
                    $monthlydata['total_count'] += $value['coursecat_count'];
                    $monthlydata['renewal_count'] += $value['renewal_coursecat_count'];
                    $monthdata[] = $monthlydata;
                }
            }
        }
    }

    $obj->getActiveSheet()
        ->setCellValueExplicit(IntToChr((count($coursecatList) + 1) * count($monthList) + 2) . ($rowNum), round($totalcalc_count_renewal / $totalcalc_count, 2), PHPExcel_Cell_DataType::TYPE_NUMERIC);

//    $cellexists = 0;
//    foreach ($toatalcalcdata as $key => $calcdata) {
//        if ($calcdata['col_num'] == ((count($coursecatList) + 1) * count($monthList) + 2)) {
//            $cellexists = 1;
//            $calcdata['numerator'] += $totalcalc_count_renewal;
//            $calcdata['denominator'] += $totalcalc_count;
//            $toatalcalcdata[$key] = $calcdata;
//        }
//    }
//    if ($cellexists == 0) {
//        $calcdata = array();
//        $calcdata['col_num'] = ((count($coursecatList) + 1) * count($monthList) + 2);
//        $calcdata['numerator'] += $totalcalc_count_renewal;
//        $calcdata['denominator'] += $totalcalc_count;
//        $toatalcalcdata[] = $calcdata;
//    }

    if ($monthdata) {
        foreach ($monthdata as $monthlydata) {
            if ($monthlydata['total_count'] > 0) {
                $obj->getActiveSheet()
                    ->setCellValueExplicit(IntToChr(count($coursecatList) * count($monthList) + $monthlydata['month_key'] + 2) . ($rowNum), round($monthlydata['renewal_count'] / $monthlydata['total_count'], 2), PHPExcel_Cell_DataType::TYPE_NUMERIC);

//                $cellexists = 0;
//                foreach ($toatalcalcdata as $key => $calcdata) {
//                    if ($calcdata['col_num'] == (count($coursecatList) * count($monthList) + $monthlydata['month_key'] + 2)) {
//                        $cellexists = 1;
//                        $calcdata['numerator'] += $monthlydata['renewal_count'];
//                        $calcdata['denominator'] += $monthlydata['total_count'];
//                        $toatalcalcdata[$key] = $calcdata;
//                    }
//                }
//                if ($cellexists == 0) {
//                    $calcdata = array();
//                    $calcdata['col_num'] = (count($coursecatList) * count($monthList) + $monthlydata['month_key'] + 2);
//                    $calcdata['numerator'] += $monthlydata['renewal_count'];
//                    $calcdata['denominator'] += $monthlydata['total_count'];
//                    $toatalcalcdata[] = $calcdata;
//                }
            }
        }
    }

    //添加合计行
    $rowNum++;
//    if ($toatalcalcdata) {
//        foreach ($toatalcalcdata as $celldata) {
//            if ($celldata['col_num'] == 1) {
//                $obj->getActiveSheet()
//                    ->setCellValueExplicit(IntToChr($celldata['col_num']) . ($rowNum), '合计', PHPExcel_Cell_DataType::TYPE_STRING);
//            } else {
//                $obj->getActiveSheet()
//                    ->setCellValueExplicit(IntToChr($celldata['col_num']) . ($rowNum), round($celldata['numerator'] / $celldata['denominator'], 2), PHPExcel_Cell_DataType::TYPE_NUMERIC);
//            }
//        }
//    }


    //导出
    ob_clean();
    $objWriter = new PHPExcel_Writer_Excel2007($obj);
    header("Pragma: public");
    header("Expires: 0");
    header("Cache-Control:must-revalidate, post-check=0, pre-check=0");
    header("Content-Type:application/force-download");
    header("Content-Type:application/vnd.ms-excel; charset=UTF-8");
    header("Content-Type:application/octet-stream");
    header("Content-Type:application/download");
    header("Content-Disposition:attachment;filename=" . urlencode($fileName) . urlencode($fileType));
    header("Content-Transfer-Encoding:binary");
    $objWriter->save("php://output");
    exit;
}

function excelcustom_bishopClassrenew($excelDetailHeader, $mainData, $calcData, $fileName, $fileType)
{
    $cacheMethod = PHPExcel_CachedObjectStorageFactory::cache_in_memory_gzip;
    $cacheSettings = array('memoryCacheSize' => '512MB');
    PHPExcel_Settings::setCacheStorageMethod($cacheMethod, $cacheSettings);
    $obj = new \PHPExcel();

    // 以下内容是excel文件的信息描述信息
    $obj->getProperties()->setCreator(''); //设置创建者
    $obj->getProperties()->setLastModifiedBy(''); //设置修改者
    $obj->getProperties()->setTitle(''); //设置标题
    $obj->getProperties()->setSubject(''); //设置主题
    $obj->getProperties()->setDescription(''); //设置描述
    $obj->getProperties()->setKeywords('');//设置关键词
    $obj->getProperties()->setCategory('');//设置类型

    // 设置当前sheet
    $obj->setActiveSheetIndex(0);

    // 设置当前sheet的名称
    $obj->getActiveSheet()->setTitle($fileName . '明细');

    $colIndex = 0;
    $rowIndex = 0;

    foreach ($excelDetailHeader as $key => $value) {
        $obj->getActiveSheet()->setCellValueExplicitByColumnAndRow($key, 1, $value);
    }

    $rowIndex++;

    $monthList = array();
    $yearList = array();
    $catList = array();
    $school_branch = '';
    $staffer_id = '';
    $calcListData = array();
    foreach ($mainData as $detlvar) {
        $i = 0;
        $rowIndex++;

        $obj->getActiveSheet()
            ->setCellValueExplicit(IntToChr($i++) . ($rowIndex), $detlvar['school_tagbak'], PHPExcel_Cell_DataType::TYPE_STRING)
            ->setCellValueExplicit(IntToChr($i++) . ($rowIndex), $detlvar['school_branch'], PHPExcel_Cell_DataType::TYPE_STRING)
            ->setCellValue(IntToChr($i++) . ($rowIndex), $detlvar['school_name'])
            ->setCellValue(IntToChr($i++) . ($rowIndex), $detlvar['staffer_cnname'])
            ->setCellValue(IntToChr($i++) . ($rowIndex), $detlvar['staffer_enname'])
            ->setCellValueExplicit(IntToChr($i++) . ($rowIndex), $detlvar['staffer_branch'], PHPExcel_Cell_DataType::TYPE_STRING)
            ->setCellValue(IntToChr($i++) . ($rowIndex), $detlvar['job_name'])
            ->setCellValue(IntToChr($i++) . ($rowIndex), $detlvar['coursecat_branch'])
            ->setCellValue(IntToChr($i++) . ($rowIndex), $detlvar['coursecat_cnname'])
            ->setCellValue(IntToChr($i++) . ($rowIndex), $detlvar['course_branch'])
            ->setCellValue(IntToChr($i++) . ($rowIndex), $detlvar['course_cnname'])
            ->setCellValueExplicit(IntToChr($i++) . ($rowIndex), $detlvar['class_branch'], PHPExcel_Cell_DataType::TYPE_STRING)
            ->setCellValue(IntToChr($i++) . ($rowIndex), $detlvar['class_enname'])
            ->setCellValue(IntToChr($i++) . ($rowIndex), $detlvar['class_endmonth'])
            ->setCellValue(IntToChr($i++) . ($rowIndex), $detlvar['all_num'])
            ->setCellValue(IntToChr($i++) . ($rowIndex), $detlvar['next_num'])
            ->setCellValue(IntToChr($i++) . ($rowIndex), $detlvar['renew_num'])
            ->setCellValue(IntToChr($i++) . ($rowIndex), $detlvar['renew_rate'])
            ->setCellValue(IntToChr($i++) . ($rowIndex), $detlvar['teach_rate']);

        $bool_incalcList = false;
        if ($calcListData && count($calcListData) > 0) {
            foreach ($calcListData as $value) {
                if ($value['school_branch'] == $detlvar['school_branch'] && $value['staffer_id'] == $detlvar['staffer_id']) {
                    $bool_incalcList = true;
                }
            }
        }
        if (!$bool_incalcList) {
            $calcarray = array();
            $calcarray['school_tagbak'] = $detlvar['school_tagbak'];
            $calcarray['school_branch'] = $detlvar['school_branch'];
            $calcarray['school_name'] = $detlvar['school_name'];
            $calcarray['staffer_id'] = $detlvar['staffer_id'];
            $calcarray['staffer_cnname'] = $detlvar['staffer_cnname'];
            $calcarray['staffer_enname'] = $detlvar['staffer_enname'];
            $calcarray['staffer_branch'] = $detlvar['staffer_branch'];
            $calcarray['job_name'] = $detlvar['job_name'];
            $calcListData[] = $calcarray;
        }

        $cat = $detlvar['coursecat_branch'];
        $month = $detlvar['class_endmonth'];
        $year = $detlvar['class_endyear'];

        if (!in_array($cat, $catList)) {
            array_push($catList, $cat);
        }
        if (!in_array($month, $monthList)) {
            array_push($monthList, $month);
        }
        if (!in_array($year, $yearList)) {
            array_push($yearList, $year);
        }
    }

    sort($catList);
    sort($monthList);
    sort($yearList);

    //创建一个新的工作空间(sheet)
    $obj->createSheet();
    $obj->setactivesheetindex(1);

    $obj->getActiveSheet()->setTitle($fileName . '汇总');

    $head_col = 4;//循环左侧列数
    $head_row = 2;//循环上侧行数

    $col_cursor = $head_col;
    $row_cursor = $head_row;
    foreach ($monthList as $key => $value) {
        $col_cursor = $head_col + (count($catList) + 1) * $key;

        $obj->getActiveSheet()->setCellValueExplicitByColumnAndRow($col_cursor, $row_cursor - 1, date('m', strtotime($value . '-01')) . '月');

        foreach ($catList as $key1 => $value1) {
            $obj->getActiveSheet()->setCellValueExplicitByColumnAndRow($col_cursor + $key1, $row_cursor, $value1 . '月平均留班率');
        }
        $obj->getActiveSheet()->setCellValueExplicitByColumnAndRow($col_cursor + count($catList), $row_cursor, '月度平均留班率');

        $obj->getActiveSheet()
            ->mergeCells(IntToChr($col_cursor) . '1:' . IntToChr($col_cursor + count($catList)) . '1');
    }

    foreach ($yearList as $key => $value) {
        $col_cursor = $head_col + (count($catList) + 1) * (count($monthList) + $key);
        $obj->getActiveSheet()->setCellValueExplicitByColumnAndRow($col_cursor, $row_cursor - 1, date('Y', strtotime($value . '-01')) . '年');

        foreach ($catList as $key1 => $value1) {
            $obj->getActiveSheet()->setCellValueExplicitByColumnAndRow($col_cursor + $key1, $row_cursor, $value1 . '年平均留班率');
        }
        $obj->getActiveSheet()->setCellValueExplicitByColumnAndRow($col_cursor + count($catList), $row_cursor, '年度平均留班率');

        $obj->getActiveSheet()
            ->mergeCells(IntToChr($col_cursor) . '1:' . IntToChr($col_cursor + count($catList)) . '1');
    }

    $obj->getActiveSheet()
        ->getStyle('E1:' . IntToChr($head_col + (count($catList) + 1) * (count($monthList) + count($yearList))) . '1')
        ->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_CENTER)
        ->setVertical(PHPExcel_Style_Alignment::VERTICAL_CENTER);//主体水平居中

    $obj->getActiveSheet()->setCellValueExplicitByColumnAndRow(0, $row_cursor, '督导');
    $obj->getActiveSheet()->setCellValueExplicitByColumnAndRow(1, $row_cursor, '学校');
    $obj->getActiveSheet()->setCellValueExplicitByColumnAndRow(2, $row_cursor, '带班主教');
    $obj->getActiveSheet()->setCellValueExplicitByColumnAndRow(3, $row_cursor, '职称');

    $obj->getActiveSheet()->getStyle(IntToChr($head_col) . ':' . IntToChr($head_col + 1 + (count($monthList) + count($yearList)) * (count($catList) + 1)))
        ->getNumberFormat()->setFormatCode(PHPExcel_Style_NumberFormat::FORMAT_PERCENTAGE);

    $yearcatcalc = array();
    $monthcatcalc = array();

    //循环学校-教师数据
    foreach ($calcListData as $rowvar) {
        $col_cursor = $head_col;
        $row_cursor++;

        $obj->getActiveSheet()
            ->setCellValueExplicit(IntToChr(0) . ($row_cursor), $rowvar['school_tagbak'], PHPExcel_Cell_DataType::TYPE_STRING)
            ->setCellValueExplicit(IntToChr(1) . ($row_cursor), $rowvar['school_name'], PHPExcel_Cell_DataType::TYPE_STRING)
            ->setCellValue(IntToChr(2) . ($row_cursor), $rowvar['staffer_cnname'])
            ->setCellValue(IntToChr(3) . ($row_cursor), $rowvar['job_name']);

        //循环主数据
        foreach ($calcData as $calckey => $calcOne) {
            if ($calcOne['school_branch'] == $rowvar['school_branch'] && $calcOne['staffer_id'] == $rowvar['staffer_id']) {

                $monthkey = array_search($calcOne['class_endmonth'], $monthList);
                $catkey = array_search($calcOne['coursecat_branch'], $catList);

                $obj->getActiveSheet()
                    ->setCellValueExplicitByColumnAndRow($col_cursor + (count($catList) + 1) * $monthkey + $catkey, $row_cursor, round($calcOne['renew_num_calc'] / $calcOne['all_num_calc'], 2), PHPExcel_Cell_DataType::TYPE_NUMERIC);

                $bool_inmonth = false;
                if ($monthcatcalc) {
                    foreach ($monthcatcalc as $key => $monthcatOne) {
                        if ($monthcatOne['class_endmonth'] == $calcOne['class_endmonth'] && $monthcatOne['school_branch'] == $calcOne['school_branch'] && $monthcatOne['staffer_id'] == $calcOne['staffer_id']) {
                            $monthcatcalc[$key]['row_cursor'] = $row_cursor;
                            $monthcatcalc[$key]['all_num_calc'] += $calcOne['all_num_calc'];
                            $monthcatcalc[$key]['renew_num_calc'] += $calcOne['renew_num_calc'];
                            $bool_inmonth = true;
                        }
                    }
                }
                if (!$bool_inmonth) {
                    $monthcatOne = array();
                    $monthcatOne['class_endmonth'] = $calcOne['class_endmonth'];
                    $monthcatOne['school_branch'] = $calcOne['school_branch'];
                    $monthcatOne['staffer_id'] = $calcOne['staffer_id'];
                    $monthcatOne['row_cursor'] = $row_cursor;
                    $monthcatOne['all_num_calc'] = $calcOne['all_num_calc'];
                    $monthcatOne['renew_num_calc'] = $calcOne['renew_num_calc'];
                    $monthcatcalc[] = $monthcatOne;
                }

                $bool_inyear = false;
                if ($yearcatcalc) {
                    foreach ($yearcatcalc as $key => $yearcatOne) {
                        if ($yearcatOne['class_endyear'] == $calcOne['class_endyear'] && $yearcatOne['coursecat_branch'] == $calcOne['coursecat_branch'] && $yearcatOne['school_branch'] == $calcOne['school_branch'] && $yearcatOne['staffer_id'] == $calcOne['staffer_id']) {
                            $yearcatcalc[$key]['row_cursor'] = $row_cursor;
                            $yearcatcalc[$key]['all_num_calc'] += $calcOne['all_num_calc'];
                            $yearcatcalc[$key]['renew_num_calc'] += $calcOne['renew_num_calc'];
                            $bool_inyear = true;
                        }
                    }
                }
                if (!$bool_inyear) {
                    $yearcatOne = array();
                    $yearcatOne['class_endyear'] = $calcOne['class_endyear'];
                    $yearcatOne['school_branch'] = $calcOne['school_branch'];
                    $yearcatOne['staffer_id'] = $calcOne['staffer_id'];
                    $yearcatOne['coursecat_branch'] = $calcOne['coursecat_branch'];
                    $yearcatOne['row_cursor'] = $row_cursor;
                    $yearcatOne['all_num_calc'] = $calcOne['all_num_calc'];
                    $yearcatOne['renew_num_calc'] = $calcOne['renew_num_calc'];
                    $yearcatcalc[] = $yearcatOne;
                }
            }
        }
    }

    foreach ($monthcatcalc as $key => $value) {
        $monthkey = array_search($value['class_endmonth'], $monthList);
        $rowkey = $value['row_cursor'];

        $obj->getActiveSheet()
            ->setCellValueExplicitByColumnAndRow($col_cursor + (count($catList) + 1) * ($monthkey + 1) - 1, $rowkey, round($value['renew_num_calc'] / $value['all_num_calc'], 2), PHPExcel_Cell_DataType::TYPE_NUMERIC);
    }

    $allyearcalc = array();
    foreach ($yearcatcalc as $value) {
        $yearkey = array_search($value['class_endyear'], $yearList);
        $catkey = array_search($value['coursecat_branch'], $catList);
        $rowkey = $value['row_cursor'];

        $obj->getActiveSheet()
            ->setCellValueExplicitByColumnAndRow($col_cursor + (count($catList) + 1) * (count($monthList) + $yearkey) + $catkey, $rowkey, round($value['renew_num_calc'] / $value['all_num_calc'], 2), PHPExcel_Cell_DataType::TYPE_NUMERIC);

        $bool_allyear = false;
        if ($allyearcalc) {
            foreach ($allyearcalc as $key => $allyearone) {
                if ($allyearone['row_cursor'] == $rowkey && $allyearone['class_endyear'] == $value['class_endyear']) {
                    $allyearcalc[$key]['renew_num_calc'] += $value['renew_num_calc'];
                    $allyearcalc[$key]['all_num_calc'] += $value['all_num_calc'];
                    $bool_allyear = true;
                }
            }
        }
        if (!$bool_allyear) {
            $allyearone = array();
            $allyearone['row_cursor'] = $rowkey;
            $allyearone['class_endyear'] = $value['class_endyear'];
            $allyearone['renew_num_calc'] = $value['renew_num_calc'];
            $allyearone['all_num_calc'] = $value['all_num_calc'];
            $allyearcalc[] = $allyearone;
        }
    }

    foreach ($allyearcalc as $key => $value) {
        $rowkey = $value['row_cursor'];
        $yearkey = array_search($value['class_endyear'], $yearList);

        $obj->getActiveSheet()
            ->setCellValueExplicitByColumnAndRow($col_cursor + (count($catList) + 1) * (count($monthList) + $yearkey + 1) - 1, $rowkey, round($value['renew_num_calc'] / $value['all_num_calc'], 2), PHPExcel_Cell_DataType::TYPE_NUMERIC);
    }

    //导出
    ob_clean();
    $objWriter = new PHPExcel_Writer_Excel2007($obj);
    header("Pragma: public");
    header("Expires: 0");
    header("Cache-Control:must-revalidate, post-check=0, pre-check=0");
    header("Content-Type:application/force-download");
    header("Content-Type:application/vnd.ms-excel; charset=UTF-8");
    header("Content-Type:application/octet-stream");
    header("Content-Type:application/download");
    header("Content-Disposition:attachment;filename=" . urlencode($fileName) . urlencode($fileType));
    header("Content-Transfer-Encoding:binary");
    $objWriter->save("php://output");
    exit;
}

function excelcustom_weeklyNumber($contentarray, $header, $queryarray, $getfileds, $filename)
{
    $cacheMethod = PHPExcel_CachedObjectStorageFactory::cache_in_memory_gzip;
    $cacheSettings = array('memoryCacheSize' => '512MB');
    PHPExcel_Settings::setCacheStorageMethod($cacheMethod, $cacheSettings);
    $objPHPExcel = new PHPEXCEL();
    foreach ($header as $key => $value) {
        $objPHPExcel->getActiveSheet()->setCellValueExplicitByColumnAndRow($key, 1, $value);
    }

    foreach ($queryarray as $row => $item) {
        $i = 0;
        $row = $row + 2;
        foreach ($item as $key => $field) {
            if (in_array($key, $getfileds)) {
                $objPHPExcel->getActiveSheet()->setCellValueExplicitByColumnAndRow($i, $row, $field);
                if ($key == 'newweeknum') {
                    $students = '';
                    $class_branch = $item['class_branch'];
                    if (isset($class_branch) && $class_branch !== '') {
                        foreach ($contentarray as $content) {
                            if ($content['class_branch'] == $class_branch) {
                                $students .= $content['student_cnname'] . "\r\n";
                            }
                        }
                        $objPHPExcel->getActiveSheet()->getComment(IntToChr($i) . $row)->getText()->createTextRun($students);
                    }
                }
                $i++;
            }
        }
    }

    $objWriter = new PHPExcel_Writer_Excel2007($objPHPExcel);
    header("Pragma: public");
    header("Expires: 0");
    header("Cache-Control:must-revalidate, post-check=0, pre-check=0");
    header("Content-Type:application/force-download");
    header("Content-Type:application/vnd.ms-excel; charset=UTF-8");
    header("Content-Type:application/octet-stream");
    header("Content-Type:application/download");
    header("Content-Disposition:attachment;filename=" . urlencode($filename));
    header("Content-Transfer-Encoding:binary");
    $objWriter->save("php://output");
    exit;
}
