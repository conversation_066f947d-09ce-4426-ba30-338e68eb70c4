<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn 
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2016/12/13
 * Time: 17:05
 */

/**
 *功能：js信息提示(取自定义信息提示)
 *返回
 **/
function jserror_tip($error_str,$location,$no = '0'){
    $res = array('error' => '1', 'errortip' => $error_str,'bakurl'=>$location,'bakfuntion'=>'errormotify');
    $Webjson_play = new Webjson();
    exit($Webjson_play->encode($res));
}

/**
 *功能：js信息提示(取自定义信息提示)
 *返回
 **/
function jsok_tip($error_str,$location,$no = '0'){
    $res = array('error' => '1', 'errortip' => $error_str,'bakurl'=>$location,'bakfuntion'=>'okmotify');
    $Webjson_play = new Webjson();
    exit($Webjson_play->encode($res));
}

/**
 *功能：js信息提示(取自定义信息提示)
 *返回
 **/
function jsbakerror_tip($error_str,$no = '0'){
    $res = array('error' => '1', 'errortip' => $error_str,'url'=>"","bak"=>"history");
    $Webjson_play = new Webjson();
    exit($Webjson_play->encode($res));
}

/**
 *功能：js信息提示(取自定义信息提示)
 *返回
 **/
function jslocationerror_tip($error_str,$no = '0'){
    $res = array('error' => '1', 'errortip' => $error_str,'url'=>"","bak"=>"refreshpage");
    $Webjson_play = new Webjson();
    exit($Webjson_play->encode($res));
}
/**
 *功能：js信息提示(取自定义信息提示)
 *返回
 **/
function jserror_spl($error_str,$location,$no = '0'){
    echo "<script language=\"javascript\">";
    echo "alert('{$error_str}');";
    echo "window.location=\"{$location}\";</script>";
    if($no == '0'){
        exit;
    }
}
/**
 *功能：js信息提示(无提示)
 *返回
 **/
function jslocal_spl($location,$no = '0'){
    echo "<script language=\"javascript\">";
    echo "window.location=\"{$location}\";</script>";
    if($no == '0'){
        exit;
    }
}
/**
 *功能：js信息提示(取自定义信息提示)
 *返回
 **/
function jsbak_spl($no = '0'){
    echo "<script language=\"javascript\">";
    echo "history.go(-1);</script>";
    if($no == '0'){
        exit;
    }
}
/**
 *功能：js信息提示(取自定义信息提示)
 *返回
 **/
function jsbakerror_spl($error_str,$no = '0'){
    echo "<script language=\"javascript\">";
    echo "alert('{$error_str}');";
    echo "history.go(-1);</script>";
    if($no == '0'){
        exit;
    }
}
/**
 *功能：js信息提示(取自定义信息提示)
 *返回
 **/
function jsbakerrortwo_spl($error_str,$no = '0'){
    echo "<script language=\"javascript\">";
    echo "alert('{$error_str}');";
    echo "history.go(-2);</script>";
    if($no == '0'){
        exit;
    }
}

/**
 *功能：js信息提示(取自定义信息提示)
 *返回
 **/
function jserror_url($error_str,$location,$no = '0'){
    echo "<script language=\"javascript\">";
    echo "alert('{$error_str}');";
    echo "window.location=\"{$location}\";</script>";
    if($no == '0'){
        exit;
    }
}


/**
 *功能：总js信息提示(取自定义信息提示)
 *返回
 **/
function jsparent_url($error_str,$location,$no = '0'){
    echo "<script language=\"javascript\">";
    echo "alert('{$error_str}');";
    echo "parent.window.location=\"{$location}\";</script>";
    if($no == '0'){
        exit;
    }
}
/**
 *功能：js信息提示(取自定义信息提示)
 *返回
 **/
function jserror_return($error_str,$no = '0'){
    echo "<script language=\"javascript\">";
    echo "alert('{$error_str}');";
    echo "history.go(-1);</script>";
    if($no == '0'){
        exit;
    }
}

function js404_spl(){
    header("http/1.1 404 not found");
    header("status: 404 not found");
    echo "不存在的链接页面！";
    exit();
}

/*ajax返回*/
function ajax_return($data,$language_type='zh',$isall='0')
{
    if($language_type == 'zh') {
        $json_play = new \Webjson();
        $data['serverlbs'] = Lbsstite;
        exit($json_play->encode($data));
    }elseif($language_type == 'tw'){
        $Model = new \Model\jianfanModel();
        //转义 errortip
        $data['errortip'] = $Model->gb2312_big5($data['errortip']);
        if($isall == '1'){
            $data = $Model->gb2312_big5(json_encode($data, JSON_UNESCAPED_UNICODE));
            $data = json_decode($data, true);
        }else {
            if (isset($data['result']['field']) && is_array($data['result']['field'])) {
                //转义 field
                $data['result']['field'] = $Model->gb2312_big5(json_encode($data['result']['field'], JSON_UNESCAPED_UNICODE));
                $data['result']['field'] = json_decode($data['result']['field'], true);
                //转义 errortip
                $data['errortip'] = $Model->gb2312_big5(json_encode($data['errortip'], JSON_UNESCAPED_UNICODE));
                $data['errortip'] = json_decode($data['errortip'], true);
            }
        }

        $json_play = new \Webjson();
        $data['serverlbs'] = Lbsstite;
        exit($json_play->encode($data));
    }else{
        $json_play = new \Webjson();
        $data['serverlbs'] = Lbsstite;
        exit($json_play->encode($data));
    }
}

