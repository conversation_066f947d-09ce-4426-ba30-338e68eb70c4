<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn 
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2016/12/13
 * Time: 18:03
 */

class Incsession{
    /**
     * 功能：初始化构造函数，开始session_start
     */
    function __construct(){
        session_start();
        $_SESSION["Copy_priv"] = 'MYADARO';
        setcookie("Copy_priv",'MYADARO',time()+3600*24,'/',".".SITE_URL);
    }


    /**
     *生成session
     *$gostr注册值
     *$govar注册值内容
     *$gotime _COOKIE 保存天数
     **/
    function set_session($gostr,$govar,$gotime){
        if($this->cookie_spl()){
            setcookie($gostr,$govar,time()+3600*24*$gotime,'/',".".SITE_URL);
            return true;
        }elseif($this->session_spl()){
            $_SESSION[$gostr] = $govar;
            return true;
        }else{
            return false;
        }
    }

    /**session获取**/
    function session_Get($str){
        if($this->cookie_spl()){
            if(isset($_COOKIE[$str])){
                return $_COOKIE[$str];
            }else{
                return false;
            }
        }elseif($this->session_spl()){
            if(isset($_SESSION[$str])){
                return $_SESSION[$str];
            }else{
                return false;
            }
        }else{
            return false;
        }
    }
    /**
     *注销session
     *$gostr注册值
     **/
    function del_session($gostr){
        if($this->cookie_spl()){
            setcookie($gostr,'',time()-3600,'/',SITE_URL);
        }elseif($this->session_spl()){
            $_SESSION[$gostr] = "";
        }else{
            return false;
        }
    }
    /**
     *注销cookie
     *$gostr注册值
     **/
    function del_all(){
        if($this->cookie_spl()){
            $date = $_COOKIE;
            if(is_array($date)){
                foreach($date as $datevar){
                    setcookie($datevar,'',time()-3600,'/',SITE_URL);
                }
            }
        }elseif($this->session_spl()){
            session_destroy();
        }else{
            return false;
        }
    }

    /**session是否开启**/
    function session_spl(){
        if(isset($_SESSION["Copy_priv"]) && $_SESSION["Copy_priv"] =='MYADARO'){
            return true;
        }else{
            return false;
        }
    }
    /**cookie是否开启**/
    function cookie_spl(){
        return true;
//        if(isset($_COOKIE["Copy_priv"]) && $_COOKIE["Copy_priv"] =='MYADARO'){
//            return true;
//        }else{
//            return false;
//        }
    }
    /**内部信息输出**/
    function session_date(){
        if($this->cookie_spl()){
            return $_COOKIE;
        }elseif($this->session_spl()){
            return $_SESSION;
        }else{
            return false;
        }
    }
    /**内部信息输出**/
    function judge($str){
        if($this->cookie_spl()){
            if(isset($_COOKIE[$str]) && $_COOKIE[$str] !==''){
                return true;
            }else{
                return false;
            }
        }elseif($this->session_spl()){
            if(isset($_SESSION[$str]) && $_SESSION[$str] !==''){
                return true;
            }else{
                return false;
            }
        }else{
            return false;
        }
    }
    /**数值注册**/
    function setCookiearray($var,$value='',$time)
    {
        if($value != '')
        {
            $value = $this->CookieEncrypt(serialize($value));
        }
        return $this->set_session($var,$value,$time);
    }
    function getCookiearray($var)
    {
        if($this->session_Get($var)){
            $value='';
            $session_date = $this->session_date();
            if(array_key_exists($var,$session_date))
            {
                $value = $this->session_Get($var);
                $value = unserialize($this->CookieDecode($value));
            }
            return $value;
        }else{
            return false;
        }
    }

    //Cookie加密
    function CookieEncrypt($ciphertext){
        $key = substr(md5("tt4emAuppFefuioL"), 8, 16);
        $rep = new \Encrypt($key);
        $decodeText = $rep->encryptAES($ciphertext);
        return $decodeText;
    }

    //Cookie解密
    function CookieDecode($ciphertext){
        $key = substr(md5("tt4emAuppFefuioL"), 8, 16);
        $rep = new \Encrypt($key);
        $decodeText = $rep->decryptAES($ciphertext);
        return $decodeText;
    }

}