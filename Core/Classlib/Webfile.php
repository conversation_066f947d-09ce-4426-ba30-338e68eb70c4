<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn 
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2016/12/13
 * Time: 18:09
 */

class Webfile {
    var $uploadfile = array();   //用户上传的文件
    var $files_dir = "update/file";     //存放用户上传文件的路径
    var $max_file_size = 2097152;//上传大小限制，单位是"KB"，默认为：1024KB
    var $last_error;      //记录最后一次出错信息
    var $updatastatus = true;      //上传状态
    //默认允许用户上传的文件类型
    var $allow_type = array('zip', 'rar', 'txt', 'doc', 'pdf', 'xls','csv','xlsx');
    var $final_file_path;   //最终保存的文件名
    var $save_info = array(); //返回一组有用信息，用于提示用户。

    /**
     * 构造函数，用与初始化相关信息，用户待上传文件、存储路径等
     *
     * @param Array $file 用户上传的文件
     * @param String $path 存储用户上传文件的路径
     * @param Integer $size 允许用户上传文件的大小(字节)
     * @param Array $type   此数组中存放允计用户上传的文件类型
     */
    function __construct($uploadfile, $files_dir='update/file', $size = 2097152, $allow_type = '') {
        $this->uploadfile = $uploadfile;
        $this->files_dir = $files_dir;
        $this->max_file_size = $size;   //如果用户不填写文件大小，则默认为2M.
        if ($allow_type != '')
            $this->allow_type = $allow_type;
    }

    /**
     * 存储用户上传文件，检验合法性通过后，存储至指定位置。
     * @access public
     * @return int    值为0时上传失败，非0表示上传成功的个数。
     */
    function upload($file_name = '') {
        //如果当前文件上传功能，则执行下一步。
        if ($this->uploadfile['error'] == 0) {
            //取当前文件名、临时文件名、大小、扩展名，后面将用到。
            $name = $this->uploadfile['name'];
            $tmpname = $this->uploadfile['tmp_name'];
            $size = $this->uploadfile['size'];
            $mime_type = $this->uploadfile['type'];
            $type = $this->getFileExt($this->uploadfile['name']);

            //检测当前上传文件大小是否合法。
            if (!$this->checkSize($size)) {
                $this->last_error = "您的文件太大超过系统支持. 上传的文件名为: ".$name;
                return $this->halt($this->last_error);
            }
            //检测当前上传文件扩展名是否合法。
            if (!$this->checkType($type)) {
                $this->last_error = "系统不支持您的文件格式: .".$type." 上传的文件名为: ".$name;
                return $this->halt($this->last_error);
            }
            //检测当前上传文件是否非法提交。
            if(!is_uploaded_file($tmpname)) {
                $this->last_error = "您的上传方式非法. 上传的文件名为: ".$name;
                return $this->halt($this->last_error);
            }
            //移动文件后，重命名文件用。
            $this->getBaseName($name, ".".$type);
            //echo $name;
            //移动后的文件名
            $saveas = $this->random_filename().".".$type;
            //组合新文件名再存到指定目录下，格式：存储路径 + 文件名 + 时间 + 扩展名
            if (empty($file_name))
            {
                $file_name = ROOT_PATH.$this->files_dir."/".$saveas;
            }

            if ($this->move_file($this->uploadfile, $file_name))
            {
                $this->final_file_path = str_replace(ROOT_PATH, '', $file_name);
                return $this->final_file_path;
            }
            else
            {
                $this->last_error = "Upload Failed!";
                return $this->halt($this->last_error);
            }
        }
    }

    /**
     * 检测用户提交文件大小是否合法
     * @param Integer $size 用户上传文件的大小
     * @access private
     * @return boolean 如果为true说明大小合法，反之不合法
     */
    function checkSize($size) {
        if ($size > $this->max_file_size) {
            return false;
        }
        else {
            return true;
        }
    }

    /**
     * 检测用户提交文件类型是否合法
     * @access private
     * @return boolean 如果为true说明类型合法，反之不合法
     */
    function checkType($extension) {
        foreach ($this->allow_type as $type) {
            if (strcasecmp($extension , $type) == 0)
                return true;
        }
        return false;
    }

    /**
     * 显示出错信息
     * @param $msg    要显示的出错信息
     * @access private
     */
    function halt($msg) {
        $this->updatastatus = false;
        return "<b>上传出错</b> {$msg} <br>\n";
    }

    /**
     * 取文件扩展名
     * @param String $filename 给定要取扩展名的文件
     * @access private
     * @return String      返回给定文件扩展名
     */
    function getFileExt($filename) {
        $stuff = pathinfo($filename);
        return $stuff['extension'];
    }
    /**
     * 取给定文件文件名，不包括扩展名。
     * eg: getBaseName("j:/hexuzhong.jpg"); //返回 hexuzhong
     *
     * @param String $filename 给定要取文件名的文件
     * @access private
     * @return String 返回文件名
     */
    function getBaseName($filename, $type) {
        $basename = basename($filename, $type);
        return $basename;
    }
    /**
     * 生成随机的数字串
     *
     * @author: weber liu
     * @return string
     */
    function random_filename()
    {
        $str = 'l';
        for($i = 0; $i < 9; $i++)
        {
            $str .= mt_rand(0, 9);
        }

        return gnmetime() . $str;
    }
    /**
     *
     *上传文件
     * @access  public
     * @param
     *
     * @return void
     */
    function move_file($upload, $target)
    {
        if (isset($upload['error']) && $upload['error'] > 0)
        {
            return false;
        }

        if (!move_upload_file($upload['tmp_name'], $target))
        {
            return false;
        }

        return true;
    }
}