<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn 
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2016/12/13
 * Time: 18:07
 */

class Websting {
    /**
     * 得到一个字符串中的某一部分
     * @param $sourceStr 源数据
     * @param $startStr 分离部分的开始标记
     * @param $endStart 分离部分的结束标记
     * @return boolean  操作成功返回true
     */

    function getContent($sourceStr,$startStr,$endStart)
    {
        $s = preg_quote($startStr);
        $e = preg_quote($endStart);
        $s = str_replace(" ","[[:space:]]",$s);
        $e = str_replace(" ","[[:space:]]",$e);
        $s = str_replace("\r\n","[[:cntrl:]]",$s);
        $e = str_replace("\r\n","[[:cntrl:]]",$e);
        preg_match_all("@".$s ."(.*?)".$e."@is",$sourceStr,$tpl);
        $content = $tpl[1];
        $content = implode("",$content);
        return $content;
    }
    /**
     * 得到一个字符串中的某一部分
     * @param $sourceStr 源数据
     * @param $startStr 分离部分的开始标记
     * @param $endStart 分离部分的结束标记
     * @return boolean  操作成功返回true
     */

    function getContentarray($sourceStr,$startStr,$endStart)
    {
        $s = preg_quote($startStr);
        $e = preg_quote($endStart);
        $s = str_replace(" ","[[:space:]]",$s);
        $e = str_replace(" ","[[:space:]]",$e);
        $s = str_replace("\r\n","[[:cntrl:]]",$s);
        $e = str_replace("\r\n","[[:cntrl:]]",$e);
        preg_match_all("@".$s ."(.*?)".$e."@is",$sourceStr,$tpl);
        $content = $tpl[1];
        //$content = implode("",$content);
        return $content;
    }
    /* 得到一个字符串莫一字符后面的字符（空格去除）
     * @param $sourceStr 源数据
     * @param $startStr 分离部分的开始标记
     * @return boolean  操作成功返回true
     */

    function getStrat($sourceStr,$startStr)
    {
        $s = preg_quote($startStr);
        $s = str_replace(" ","[[:space:]]",$s);
        $s = str_replace("\r\n","[[:cntrl:]]",$s);
        //echo $s;
        preg_match_all("/".$s ."(\S+)/is",$sourceStr,$tpl);
        $content = $tpl[1];
        //print_r($content);
        $content = implode("",$content);
        //echo $content;
        return $content;
    }
    /* 得到一个字符串莫一字符后面的几位字符
     * @param $sourceStr 源数据
     * @param $startStr 分离部分的开始标记
     * @return boolean  操作成功返回true
     */

    function getStrat_num($sourceStr,$startStr,$num)
    {
        $s = preg_quote($startStr);
        $s = str_replace(" ","[[:space:]]",$s);
        $s = str_replace("\r\n","[[:cntrl:]]",$s);
        //echo $s;
        preg_match_all("/".$s ."(\S+)/is",$sourceStr,$tpl);
        $content = $tpl[1];
        //print_r($content);
        $content = implode("",$content);
        $content = substr($content,0,$num);
        //echo $content;
        return $content;
    }
    /* 得到一个字符串莫一字符之前的字符
     * @param $sourceStr 源数据
     * @param $startStr 分离部分的开始标记
     * @return boolean  操作成功返回true
     */
    function getEnd($sourceStr,$end)
    {
        $str_array=explode($end,$sourceStr);//分割符号
        $content = $str_array[0];
        return $content;
    }
    /* 得到一个字符串莫一字符之后的字符（空格不去除）
     * @param $sourceStr 源数据
     * @param $startStr 分离部分的开始标记
     * @return boolean  操作成功返回true
     */
    function getStratall($sourceStr,$end)
    {
        $str_array=explode($end,$sourceStr);//分割符号
        $content = $str_array[1];
        return $content;
    }
    /**
     * 截取UTF-8编码下字符串的函数
     *
     * @param   string      $str        被截取的字符串
     * @param   int         $length     截取的长度
     * @param   bool        $append     是否附加省略号
     *
     * @return  string
     */
    function sub_str($str, $length = 0, $append = true)
    {
        $str = trim($str);
        $strlength = strlen($str);

        if ($length == 0 || $length >= $strlength)
        {
            return $str;
        }
        elseif ($length < 0)
        {
            $length = $strlength + $length;
            if ($length < 0)
            {
                $length = $strlength;
            }
        }

        if (function_exists('mb_substr'))
        {
            $newstr = mb_substr($str, 0, $length, MHS_CODING);
        }
        elseif (function_exists('iconv_substr'))
        {
            $newstr = iconv_substr($str, 0, $length, MHS_CODING);
        }
        else
        {
            //$newstr = trim_right(substr($str, 0, $length));
            $newstr = substr($str, 0, $length);
        }

        if ($append && $str != $newstr)
        {
            $newstr .= '...';
        }

        return $newstr;
    }
    /**
     * 计算字符串的长度（汉字按照两个字符计算）
     *
     * @param   string      $str        字符串
     *
     * @return  int
     */
    function str_len($str)
    {
        $length = strlen(preg_replace('/[\x00-\x7F]/', '', $str));

        if ($length)
        {
            return strlen($str) - $length + intval($length / 3) * 2;
        }
        else
        {
            return strlen($str);
        }
    }


    /**
     * 去除字符串右侧可能出现的乱码
     *
     * @param   string      $str        字符串
     *
     * @return  string
     */
    function trim_right($str)
    {
        $len = strlen($str);
        /* 为空或单个字符直接返回 */
        if ($len == 0 || ord($str{$len-1}) < 127)
        {
            return $str;
        }
        /* 有前导字符的直接把前导字符去掉 */
        if (ord($str{$len-1}) >= 192)
        {
            return substr($str, 0, $len-1);
        }
        /* 有非独立的字符，先把非独立字符去掉，再验证非独立的字符是不是一个完整的字，不是连原来前导字符也截取掉 */
        $r_len = strlen(rtrim($str, "\x80..\xBF"));
        if ($r_len == 0 || ord($str{$r_len-1}) < 127)
        {
            return sub_str($str, 0, $r_len);
        }

        $as_num = ord(~$str{$r_len -1});
        if ($as_num > (1<<(6 + $r_len - $len)))
        {
            return $str;
        }
        else
        {
            return substr($str, 0, $r_len-1);
        }
    }
}