<?php
/** mssql数据库操作类
 * ============================================================================
 * 版权所有 (C)2012-9-01号 http://www.mohism.cn 。
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * @version : v1.0
 * ----------------------------------------------------------------------------
 **/
class Dbmssql {
    private $dbhost;
    private $dbuser;
    private $dbpw;
    private $dbname;
    private $port;
    private $result;
    private $connid = 0;
    private $cursor = 0;
    private $querynum = 0;
    public $linkFalse = false;


    public function __construct($Server='rm-bp1eyr053465c70awso.sqlserver.rds.aliyuncs.com',$port='3433',$UserName='jdbwebsiteuser',$PassWord='Time2012',$DataBase='kid20200326')
    {
//        'rm-bp1eyr053465c70awso.sqlserver.rds.aliyuncs.com', '3433', 'jdbwebsiteuser', 'Time2012', 'kid20200326'
//    $Server='************',$port='9433',$UserName='JdbmohismUser',$PassWord='KID67086888',$DataBase='KidSims'
        if(!function_exists("mssql_connect")){
            //echo "<pre>请先安装 mssql 扩展。";
            return false;
        }else{
            $this->linkFalse = true;
            $this->dbhost = !empty($Server) ? $Server : 'localhost';
            $this->dbuser = $UserName;
            $this->dbpw = $PassWord;
            $this->dbname = $DataBase;
            $this->port = !empty($port) ? $port : 1433;
            $this->Connect();
        }
    }

    /*连接MSSql数据库，参数：Server->数据库服务器地址，UserName->登陆用户名，PassWord->登陆密码，DataBase->数据库名字*/
    private function Connect() {
        try {
            $serverName = "{$this->dbhost}:{$this->port}";
            if($this->connid = @mssql_connect($serverName, $this->dbuser, $this->dbpw, true)) {
                $this->query('SET TEXTSIZE 2147483647');
                if (@mssql_select_db($this->dbname, $this->connid)) {
                } else {
                    $this->halt('Can not Select DataBase');
                    return false;
                }
            } else {
                $this->halt('Can not connect to MSSQL server');
            }
        }catch (WebException $e)
        {
            return false;
        }
    }
    /*执行sql语句，返回对应的结果标识*/
    public function query($sql) {
        if(!$this->linkFalse){
            return false;
        }

        $sql = iconv('utf-8', "gbk//ignore",$sql);
        if($result = @mssql_query($sql, $this->connid)) {
            $this->querynum++;
            $this->result = $result;
            return $this->result;
        } else {
            $this->querynum++;
            $this->halt('MSSQL query Error', $sql);
        }
    }
    /*执行Insert Into语句，并返回最后的insert操作所产生的自动增长的id*/
    public function Insert($table, $iarr) {
        if(!$this->linkFalse){
            return false;
        }
        $value = $this->InsertSql($iarr);
        $query = $this->query('INSERT INTO ' . $table . ' ' . $value . '; SELECT SCOPE_IDENTITY() AS [insertid];');
        $record = $this->GetRow($query);
        $this->Clear($query);
        return $record['insertid'];
    }
    /*执行Update语句，并返回最后的update操作所影响的行数*/
    public function Update($table, $uarr, $condition = '') {
        if(!$this->linkFalse){
            return false;
        }
        $value = $this->UpdateSql($uarr);
        if ($condition) {
            $condition = ' WHERE ' . $condition;
        }
        $query = $this->query('UPDATE ' . $table . ' SET ' . $value . $condition . '; SELECT @@ROWCOUNT AS [rowcount];');
        $record = $this->GetRow($query);
        $this->Clear($query);
        return $record['rowcount'];
    }
    /*执行Delete语句，并返回最后的Delete操作所影响的行数*/
    public function Delete($table, $condition = '') {
        if(!$this->linkFalse){
            return false;
        }
        if ($condition) {
            $condition = ' WHERE ' . $condition;
        }
        $query = $this->query('DELETE ' . $table . $condition . '; SELECT @@ROWCOUNT AS [rowcount];');
        $record = $this->GetRow($query);
        $this->Clear($query);
        return $record['rowcount'];
    }
    /*将字符转为可以安全保存的mssql值，比如a'a转为a''a*/
    public function EnCode($str) {
        return str_replace("'", "''", str_replace('', '', $str));
    }
    /*将可以安全保存的mssql值转为正常的值，比如a''a转为a'a*/
    public function DeCode($str) {
        return str_replace("''", "''", $str);
    }
    /*将对应的列和值生成对应的insert语句，如：array('id' => 1, 'name' => 'name')返回([id], [name]) VALUES (1, 'name')*/
    public function InsertSql($iarr) {
        if(!$this->linkFalse){
            return false;
        }
        if (is_array($iarr)) {
            $fstr = '';
            $vstr = '';
            foreach ($iarr as $key => $val) {
                $fstr .= '[' . $key . '], ';
                $vstr .= "'" . $val . "', ";
            }
            if ($fstr) {
                $fstr = '(' . substr($fstr, 0, -2) . ')';
                $vstr = '(' . substr($vstr, 0, -2) . ')';
                return $fstr . ' VALUES ' . $vstr;
            } else {
                return '';
            }
        } else {
            return '';
        }
    }
    /*将对应的列和值生成对应的insert语句，如：array('id' => 1, 'name' => 'name')返回[id] = 1, [name] = 'name'*/
    public function UpdateSql($uarr) {
        if(!$this->linkFalse){
            return false;
        }
        if (is_array($uarr)) {
            $ustr = '';
            foreach ($uarr as $key => $val) {
                $ustr .= "[' . $key . '] = '" . $val . "', ";
            }
            if ($ustr) {
                return substr($ustr, 0, -2);
            } else {
                return '';
            }
        } else {
            return '';
        }
    }

    /**
     * 对象转数组
     * @param $obj
     * @return array
     */
    private function objectToArray($obj){
        $ret = array();
        foreach ($obj as $key => $value) {
            if (gettype($value) == "array" || gettype($value) == "object"){
                $ret[$key] =  $this->objectToArray($value);
            }else{
                $ret[$key] = $value;
            }
        }
        return $ret;
    }


    /**
     * 获取一条数据（一维数组）
     * @param $sql
     * @return array|bool
     */
    public function find($sql)
    {
        if(!$this->linkFalse){
            return false;
        }
        $this->result = $this->query($sql);
        $args = $this->fetch_array($this->result);
        return $args ;
    }

    /**
     * 获取多条（二维数组）
     * @param $sql
     * @param string $keyfield
     * @return array
     */
    public function findAll($sql, $keyfield = '')
    {
        if(!$this->linkFalse){
            return array();
        }
        $array = array();
        $this->result = $this->query($sql);
        while($r = $this->fetch_array($this->result)){
            if($keyfield){
                $key = $r[$keyfield];
                $array[$key] = $r;
            }else{
                $array[] = $this->objectToArray($r);
            }
        }
        return $array;
    }

    /**
     * 获取多条（二维数组）
     * @param $sql
     * @param string $keyfield
     * @return array
     */
    public function findOne($sql, $keyfield = '')
    {
        if(!$this->linkFalse){
            return false;
        }
        $array = array();
        $this->result = $this->query($sql);
        while($r = $this->fetch_array($this->result)){
            if($keyfield){
                $key = $r[$keyfield];
                $array[$key] = $r;
            }else{
                $array[] = $this->objectToArray($r);
            }
        }
        return $array[0];
    }
    public function fetch_array($query, $type = MSSQL_ASSOC)
    {
        if(is_resource($query)) {
            return auto_charset(mssql_fetch_array($query, $type),'gbk','utf-8');
        }
        if($this->cursor < count($query)){
            return $query[$this->cursor++];
        }
        return false;
    }
    /*返回对应的查询标识的结果的一行*/
    public function GetRow($query, $result_type = MSSQL_ASSOC) {
        return mssql_fetch_array($query, $result_type);
    }
    /*清空查询结果所占用的内存资源*/
    public function Clear($query) {
        return mssql_free_result($query);
    }
    /*关闭数据库*/
    public function Close() {
        return mssql_close($this->connid);
    }
    function halt($message = '', $sql = '') {
        $message .= '<br />MSSql Error:' . mssql_get_last_message();
        if ($sql) {
            $sql = '<br />sql:' . $sql;
        }
        //exit("DataBase Error.<br />Message $message $sql");
        $this->linkFalse = false;
    }
}