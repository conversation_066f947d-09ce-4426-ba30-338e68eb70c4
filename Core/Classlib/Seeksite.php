<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn 
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/1/11
 * Time: 11:48
 */

class Seeksite {
    public $viewControl;
    public $length_of_link_desc = 0;//当从数据库中查询的量较大时，设置一个较低的值（例如：250 或 100，0为不限制）,可以在较慢的机器上明显得到速度的提升。查询量不大时，效果不明显。
    public $show_meta_description = '1';//在结果页优先显示网页描述（meta description）否则显示从网页中文字中摘取
    public $stem_words=0;//在索引时使用stemming关键词算法，(例如：在寻找"run"时,寻找站点中含有的"runs"，"running"）。
    public $did_you_mean_enabled=1;//使用搜索建议(您是不是想要搜索...)
    public $results_per_page = '10';//默认每页返回的查询结果数
    public $links_to_next=5;//每页显示的链接条数。
    public $show_query_scores='1';//显示查询等级
    public $desc_length=250;//在搜索结果中显示的页面摘要最大长度
    public $min_word_length = 3;//关键字索引最小长度
    public $bancommon = array();//禁止搜索词
    public $xattr = "n,r,v,p";//检索词性
    public $sph_messages =  Array (
        "Categories" => "类别",
        "CATEGORIES" => "类别",
        "Untitled" => "未命名",
        "Powered by" => "Powered by",
        "Previous" => "上一页",
        "Next" => "下一页",
        "Result page" => "结果页面",
        "Only in category" => "只在类别",
        "Search" => "搜索",
        "All sites" => "所有站点",
        "Web pages" => "网页",
        "noMatch" => "搜索 \"%query\" 没有任何文件匹配",
        "ignoredWords" => "以下词被忽略了(太短,至少3字节（中文2字）): %ignored_words",
        "resultsFor" => "结果是:",
        "Results" => "显示结果 %from - %to  由于 %all %matchword (%secs 秒)", //
        "match" => "匹配",     //
        "matches" => "匹配", //
        "andSearch" => "与搜索",
        "orSearch" => "或搜索",
        "phraseSearch" => "词组搜索",
        "show" => "显示 ",
        "resultsPerPage" => "每页结果",
        "DidYouMean" => "你是不是想找"
    );
    public $entities = array(
        "&amp" => "&",
        "&apos" => "'",
        "&THORN;"  => "",
        "&szlig;"  => "",
        "&agrave;" => "",
        "&aacute;" => "",
        "&acirc;"  => "",
        "&atilde;" => "",
        "&auml;"   => "",
        "&aring;"  => "",
        "&aelig;"  => "",
        "&ccedil;" => "",
        "&egrave;" => "",
        "&eacute;" => "",
        "&ecirc;"  => "",
        "&euml;"   => "",
        "&igrave;" => "",
        "&iacute;" => "",
        "&icirc;"  => "",
        "&iuml;"   => "",
        "&eth;"    => "",
        "&ntilde;" => "",
        "&ograve;" => "",
        "&oacute;" => "",
        "&ocirc;"  => "",
        "&otilde;" => "",
        "&ouml;"   => "",
        "&oslash;" => "",
        "&ugrave;" => "",
        "&uacute;" => "",
        "&ucirc;"  => "",
        "&uuml;"   => "",
        "&yacute;" => "",
        "&thorn;"  => "",
        "&yuml;"   => "",
        "&THORN;"  => "",
        "&szlig;"  => "",
        "&Agrave;" => "",
        "&Aacute;" => "",
        "&Acirc;"  => "",
        "&Atilde;" => "",
        "&Auml;"   => "",
        "&Aring;"  => "",
        "&Aelig;"  => "",
        "&Ccedil;" => "",
        "&Egrave;" => "",
        "&Eacute;" => "",
        "&Ecirc;"  => "",
        "&Euml;"   => "",
        "&Igrave;" => "",
        "&Iacute;" => "",
        "&Icirc;"  => "",
        "&Iuml;"   => "",
        "&ETH;"    => "",
        "&Ntilde;" => "",
        "&Ograve;" => "",
        "&Oacute;" => "",
        "&Ocirc;"  => "",
        "&Otilde;" => "",
        "&Ouml;"   => "",
        "&Oslash;" => "",
        "&Ugrave;" => "",
        "&Uacute;" => "",
        "&Ucirc;"  => "",
        "&Uuml;"   => "",
        "&Yacute;" => "",
        "&Yhorn;"  => "",
        "&Yuml;"   => ""
    );
    public $regex_consonant = '(?:[bcdfghjklmnpqrstvwxz]|(?<=[aeiou])y|^y)';//Regex for matching a consonant
    public $regex_vowel = '(?:[aeiou]|(?<![aeiou])y)';//Regex for matching a vowel

    function __construct() {
        $this->viewControl = new \Dbwrite();
        $result = $this->viewControl->query("select tb_keywords from soweb_keywords where tb_ban = '1'");
        while ($row = mysql_fetch_row($result)) {
            $this->bancommon[] = $row[0];
        }
    }

    /**
     * Stems a word. Simple huh?
     *
     * @param  string $word Word to stem
     * @return string       Stemmed word
     */
    public function stem($word)
    {
        if (strlen($word) <= 2) {
            return $word;
        }

        $word = $this->step1c($word);
        $word = $this->step3($word);
        $word = $this->step5($word);

        return $word;
    }

    /**
     * Step 1c
     * @param string $word Word to stem
     */
    public function step1c($word)
    {
        $v = $this->regex_vowel;

        if (substr($word, -1) == 'y' && preg_match("#$v+#", substr($word, 0, -1))) {
            replace($word, 'y', 'i');
        }

        return $word;
    }
    /**
     * Step 3
     * @param string $word String to stem
     */
    public function step3($word)
    {
        switch (substr($word, -2, 1)) {
            case 'a':
                replace($word, 'ical', 'ic', 0);
                break;

            case 's':
                replace($word, 'ness', '', 0);
                break;

            case 't':
                replace($word, 'icate', 'ic', 0)
                OR replace($word, 'iciti', 'ic', 0);
                break;

            case 'u':
                replace($word, 'ful', '', 0);
                break;

            case 'v':
                replace($word, 'ative', '', 0);
                break;

            case 'z':
                replace($word, 'alize', 'al', 0);
                break;
        }

        return $word;
    }
    /**
     * Step 5
     * @param string $word Word to stem
     */
    public function step5($word)
    {
        // Part a
        if (substr($word, -1) == 'e') {
            if ($this->m(substr($word, 0, -1)) > 1) {
                replace($word, 'e', '');

            } else if ($this->m(substr($word, 0, -1)) == 1) {

                if (!$this->cvc(substr($word, 0, -1))) {
                    replace($word, 'e', '');
                }
            }
        }

        // Part b
        if (m($word) > 1 and $this->doubleConsonant($word) and substr($word, -1) == 'l') {
            $word = substr($word, 0, -1);
        }

        return $word;
    }
    /**
     * Returns true/false as to whether the given string contains two
     * of the same consonant next to each other at the end of the string.
     *
     * @param  string $str String to check
     * @return bool        Result
     */
    public function doubleConsonant($str)
    {
        $c = $this->regex_consonant;
        return preg_match("#$c{2}$#", $str, $matches) and $matches[0]{0} == $matches[0]{1};
    }
    /**
     * What, you mean it's not obvious from the name?
     *
     * m() measures the number of consonant sequences in $str. if c is
     * a consonant sequence and v a vowel sequence, and <..> indicates arbitrary
     * presence,
     *
     * <c><v>       gives 0
     * <c>vc<v>     gives 1
     * <c>vcvc<v>   gives 2
     * <c>vcvcvc<v> gives 3
     *
     * @param  string $str The string to return the m count for
     * @return int         The m count
     */
    public function m($str)
    {
        $c = $this->regex_consonant;
        $v = $this->regex_vowel;

        $str = preg_replace("#^$c+#", '', $str);
        $str = preg_replace("#$v+$#", '', $str);

        preg_match_all("#($v+$c+)#", $str, $matches);

        return count($matches[1]);
    }
    /**
     * Checks for ending CVC sequence where second C is not W, X or Y
     *
     * @param  string $str String to check
     * @return bool        Result
     */
    public function cvc($str)
    {
        $c = $this->regex_consonant;
        $v = $this->regex_vowel;

        return     preg_match("#($c$v$c)$#", $str, $matches)
        and strlen($matches[1]) == 3
        and $matches[1]{2} != 'w'
        and $matches[1]{2} != 'x'
        and $matches[1]{2} != 'y';
    }
    /**
     * Replaces the first string with the second, at the end of the string. If third
     * arg is given, then the preceding string must match that m count at least.
     *
     * @param  string $str   String to check
     * @param  string $check Ending to check for
     * @param  string $repl  Replacement string
     * @param  int    $m     Optional minimum number of m() to meet
     * @return bool          Whether the $check string was at the end
     *                       of the $str string. True does not necessarily mean
     *                       that it was replaced.
     */
    public function replace(&$str, $check, $repl, $m = null)
    {
        $len = 0 - strlen($check);

        if (substr($str, $len) == $check) {
            $substr = substr($str, 0, $len);
            if (is_null($m) OR $this->m($substr) > $m) {
                $str = $substr . $repl;
            }

            return true;
        }

        return false;
    }
    //禁止搜索词
    public function ignoreWord($word) {
        if (in_array($word,$this->bancommon)) {
            return 1;
        } else {
            return 0;
        }
    }
    //搜索记录
    public function saveToLog ($query, $elapsed, $results) {
        if ($results =="") {
            $results = 0;
        }
        $this->viewControl->query("insert into soweb_query_log (query, time, elapsed, results) values ('$query', now(), '$elapsed', '$results')");
    }
    //中搜词
    public function makeboollist($a) {
        $entities = $this->entities;
        $stem_words = $this->stem_words;
        while ($char = each($entities)) {
            $a = preg_replace("/".$char[0]."/i", $char[1], $a);
        }
        $a = trim($a);

        $a = preg_replace("/&quot;/i", "\"", $a);
        $returnWords = array();
        //get all phrases
        $regs = array();
        while (preg_match("/([-]?)\"([^\"]+)\"/", $a, $regs)) {
            if ($regs[1] == '') {
                $returnWords['+s'][] = $regs[2];
                $returnWords['hilight'][] = $regs[2];
            } else {
                $returnWords['-s'][] = $regs[2];
            }
            $a = str_replace($regs[0], "", $a);
        }
        $a = strtolower(preg_replace("/[ ]+/", " ", $a));

        $a = trim($a);

        $cws = scws_new();
        $cws->set_charset('utf8');
        $cws->set_rule('C:/Program Files/scws/etc/rules.utf8.ini'); //注意路径
        $cws->set_dict('C:/Program Files/scws/etc/dict.utf8.xdb');
        //设置分词所用规则
        //$cws->set_rule('/usr/local/scws/etc/rules.utf8.ini');
        //设置分词所用词典(此处使用utf8的词典)
        //$cws->set_dict('/usr/local/scws/etc/dict.utf8.xdb');

        $cws->add_dict(ROOT_PATH."Core/Temp/dict_extra.txt", SCWS_XDICT_TXT);//新增词库
        $cws->set_ignore(true);
        //var_dump($cws);
        //添加中文分词
        //二次分词
        $cws->send_text($a);
        $word_list = $cws->get_tops(10, $this->xattr);

        if(count($word_list) == 0){//未分词
            if(!$this->viewControl->getOne("soweb_nokeywords","query = '{$a}'")){
                $this->viewControl->query("insert into soweb_nokeywords (query,frequency) values ('{$a}','1')");
            }else{
                $this->viewControl->query("update soweb_nokeywords set frequency = frequency+1 where query='{$a}'");
            }
        }elseif(count($word_list) > 1){
            if($this->viewControl->getOne("soweb_keywords","tb_keywords = '{$a}' and tb_subject = '0'")){
                $this->viewControl->query("update soweb_keywords set tb_subject = 1 where tb_keywords='{$a}'");
            }
        }

        settype($word_list, 'array');

        $words=array();

        foreach ($word_list as $word_tmp)
        {
            $words[]=$word_tmp['word'];
        }


        if ($a=="") {
            $limit = 0;
        } else {
            $limit = count($words);
        }


        $k = 0;
        //得到所有单词（包括include和exlude ）
        $includeWords = array();
        while ($k < $limit) {
            if (substr($words[$k], 0, 1) == '+') {
                $includeWords[] = substr($words[$k], 1);
                if (!$this->ignoreWord(substr($words[$k], 1))) {
                    $returnWords['hilight'][] = substr($words[$k], 1);
                    if ($stem_words == 1) {
                        $returnWords['hilight'][] = $this->stem(substr($words[$k], 1));
                    }
                }
            } else if (substr($words[$k], 0, 1) == '-') {
                $returnWords['-'][] = substr($words[$k], 1);
            } else {
                $includeWords[] = $words[$k];
                if (!$this->ignoreWord($words[$k])) {
                    $returnWords['hilight'][] = $words[$k];
                    if ($stem_words == 1) {
                        $returnWords['hilight'][] = $this->stem($words[$k]);
                    }
                }
            }
            $k++;
        }

        //加上从短语词包括
        if (isset($returnWords['+s'])) {
            foreach ($returnWords['+s'] as $phrase) {
                $phrase = strtolower(preg_replace("/[ ]+/", " ", $phrase));
                $phrase = trim($phrase);
                $temparr = explode(' ', $phrase);
                foreach ($temparr as $w)
                    $includeWords[] = $w;
            }
        }

        foreach ($includeWords as $word) {
            if (!($word =='')) {
                if ($this->ignoreWord($word)) {
                    $returnWords['ignore'][] = $word;
                } else {
                    $returnWords['+'][] = $word;
                }
            }

        }

        return $returnWords;

    }
    //二次搜词
    public function twoboollist($a) {
        $entities=$this->entities;
        while ($char = each($entities)) {
            $a = preg_replace("/".$char[0]."/i", $char[1], $a);
        }
        $a = trim($a);

        $a = preg_replace("/&quot;/i", "\"", $a);
        $returnWords = array();
        //get all phrases
        $regs = array();
        while (preg_match("/([-]?)\"([^\"]+)\"/", $a, $regs)) {
            if ($regs[1] == '') {
                $returnWords['+s'][] = $regs[2];
                $returnWords['hilight'][] = $regs[2];
            } else {
                $returnWords['-s'][] = $regs[2];
            }
            $a = str_replace($regs[0], "", $a);
        }
        $a = strtolower(preg_replace("/[ ]+/", " ", $a));
        //$a = remove_accents($a);
        $a = trim($a);

        //$words = explode(' ', $a);

        $cws = scws_new();
        $cws->set_charset('utf8');
        $cws->set_rule('C:/Program Files/scws/etc/rules.utf8.ini'); //注意路径
        $cws->set_dict('C:/Program Files/scws/etc/dict.utf8.xdb');
        //设置分词所用规则
        //$cws->set_rule('/usr/local/scws/etc/rules.utf8.ini');
        //设置分词所用词典(此处使用utf8的词典)
        //$cws->set_dict('/usr/local/scws/etc/dict.utf8.xdb');
        $cws->set_ignore(true);
        //var_dump($cws);
        //添加中文分词
        //二次分词
        $cws->send_text($a);
        $word_list = $cws->get_tops(10, $this->xattr);

        settype($word_list, 'array');

        $words=array();

        foreach ($word_list as $word_tmp)
        {
            $words[]=$word_tmp['word'];
        }


        if ($a=="") {
            $limit = 0;
        } else {
            $limit = count($words);
        }


        $k = 0;
        //得到所有单词（包括include和exlude ）
        $includeWords = array();
        while ($k < $limit) {
            if (substr($words[$k], 0, 1) == '+') {
                $includeWords[] = substr($words[$k], 1);
                if (!$this->ignoreWord(substr($words[$k], 1))) {
                    $returnWords['hilight'][] = substr($words[$k], 1);
                    if ($this->stem_words == 1) {
                        $returnWords['hilight'][] = $this->stem(substr($words[$k], 1));
                    }
                }
            } else if (substr($words[$k], 0, 1) == '-') {
                $returnWords['-'][] = substr($words[$k], 1);
            } else {
                $includeWords[] = $words[$k];
                if (!$this->ignoreWord($words[$k])) {
                    $returnWords['hilight'][] = $words[$k];
                    if ($this->stem_words == 1) {
                        $returnWords['hilight'][] = $this->stem($words[$k]);
                    }
                }
            }
            $k++;
        }

        //加上从短语词包括
        if (isset($returnWords['+s'])) {
            foreach ($returnWords['+s'] as $phrase) {
                $phrase = strtolower(preg_replace("/[ ]+/", " ", $phrase));
                $phrase = trim($phrase);
                $temparr = explode(' ', $phrase);
                foreach ($temparr as $w)
                    $includeWords[] = $w;
            }
        }

        foreach ($includeWords as $word) {
            if (!($word =='')) {
                if ($this->ignoreWord($word)) {
                    $returnWords['ignore'][] = $word;
                } else {
                    $returnWords['+'][] = $word;
                }
            }
        }

        return $returnWords;

    }
    //三次搜词
    public function threeboollist($a) {
        $words = array();
        $result = $this->viewControl->query("select tb_keywords from soweb_keywords where tb_keywords like '%$a%'");
        if (mysql_num_rows($result) > 0) {
            $row = mysql_fetch_row($result);
            $words[] = $row[0];
        }
        while($row=mysql_fetch_array($result)) {
            $words[] = $row[0];
        }

        if ($a=="") {
            $limit = 0;
        } else {
            $limit = count($words);
        }


        $k = 0;
        //得到所有单词（包括include和exlude ）
        $includeWords = array();
        while ($k < $limit) {
            if (substr($words[$k], 0, 1) == '+') {
                $includeWords[] = substr($words[$k], 1);
                if (!$this->ignoreWord(substr($words[$k], 1))) {
                    $returnWords['hilight'][] = substr($words[$k], 1);
                    if ($this->stem_words == 1) {
                        $returnWords['hilight'][] = $this->stem(substr($words[$k], 1));
                    }
                }
            } else if (substr($words[$k], 0, 1) == '-') {
                $returnWords['-'][] = substr($words[$k], 1);
            } else {
                $includeWords[] = $words[$k];
                if (!$this->ignoreWord($words[$k])) {
                    $returnWords['hilight'][] = $words[$k];
                    if ($this->stem_words == 1) {
                        $returnWords['hilight'][] = $this->stem($words[$k]);
                    }
                }
            }
            $k++;
        }

        //加上从短语词包括
        if (isset($returnWords['+s'])) {
            foreach ($returnWords['+s'] as $phrase) {
                $phrase = strtolower(preg_replace("/[ ]+/", " ", $phrase));
                $phrase = trim($phrase);
                $temparr = explode(' ', $phrase);
                foreach ($temparr as $w)
                    $includeWords[] = $w;
            }
        }

        foreach ($includeWords as $word) {
            if (!($word =='')) {
                if ($this->ignoreWord($word)) {

                    $returnWords['ignore'][] = $word;
                } else {
                    $returnWords['+'][] = $word;
                }
            }
        }

        return $returnWords;
    }
    //检索信息
    public function search($searchstr, $start, $per_page, $type, $site_id="") {
        //$starttime = time();
        $length_of_link_desc=$this->length_of_link_desc;
        $show_meta_description=$this->show_meta_description;
        $possible_to_find = 1;
        if(isset($site_id) && $site_id !==''){
            $result = $this->iewControl->query("select site_id from soweb_sites where site_id = '$site_id'");
            if (mysql_num_rows($result)> 0) {
                $thisrow = mysql_fetch_array($result);
                $sites_qry = "and sites = ".$thisrow[0];
            } else {
                $sites_qry = "";
            }
        }else{
            $sites_qry = "";
        }


        //发现可搜索的关键词
        if (count($searchstr['+']) == 0) {
            return null;
        }
        $wordarray = $searchstr['-'];
        $notlist = array();
        $not_words = 0;

        while ($not_words < count($wordarray)) {
            if ($this->stem_words == 1) {
                $searchword = addslashes($this->stem($wordarray[$not_words]));
            } else {
                $searchword = addslashes($wordarray[$not_words]);
            }
            $wordmd5 = substr(md5($searchword), 0, 1);

            $query1 = "SELECT link_id from soweb_link_keywords$wordmd5, soweb_keywords where soweb_link_keywords$wordmd5.keyword_id= soweb_keywords.keywords_id and tb_keywords='$searchword'";
            //echo 'q1='.$query1;
            $result = $this->viewControl->query($query1);

            while ($row = mysql_fetch_row($result)) {
                $notlist[$not_words]['id'][$row[0]] = 1;
            }
            $not_words++;
        }

        //找到包含搜索短语的所有网站
        $wordarray = $searchstr['+s'];
        $phrase_words = 0;
        while ($phrase_words < count($wordarray)) {

            $searchword = addslashes($wordarray[$phrase_words]);
            $query1 = "SELECT link_id from soweb_site_links where tb_description like '%$searchword%'";
            $result = $this->viewControl->query($query1);
            $num_rows = mysql_num_rows($result);
            if ($num_rows == 0) {
                $possible_to_find = 0;
                break;
            }
            while ($row = mysql_fetch_row($result)) {
                $phraselist[$phrase_words]['id'][$row[0]] = 1;
            }
            $phrase_words++;
        }

        //查找包含搜索词的所有网站
        $wordarray = $searchstr['+'];
        $words = 0;
        $starttime = getmicrotime();
        while (($words < count($wordarray)) && $possible_to_find == 1) {
            if ($this->stem_words == 1) {
                $searchword = addslashes($this->stem($wordarray[$words]));
            } else {
                $searchword = addslashes($wordarray[$words]);
            }
            $wordmd5 = substr(md5($searchword), 0, 1);

            $query1 = "SELECT slk.link_id, slk.weight, sslk.tb_home, slk.sites from soweb_link_keywords$wordmd5 as slk Left Join soweb_keywords as sk On slk.keyword_id = sk.keywords_id Left Join soweb_site_links as sslk On sslk.link_id = slk.link_id where sk.tb_keywords='$searchword' $sites_qry order by sslk.tb_home desc, slk.weight desc";

            $result = $this->viewControl->query($query1);
            $num_rows = mysql_num_rows($result);
            if ($num_rows == 0) {
                if ($type != "or") {
                    $possible_to_find = 0;
                    break;
                }
            }
            if ($type == "or") {
                $indx = 0;
            } else {
                $indx = $words;
            }

            while ($row = mysql_fetch_row($result)) {
                $linklist[$indx]['id'][] = $row[0];
                $sites[$row[0]] = $row[3];
                $linklist[$indx]['weight'][$row[0]] = $row[1]+($row[2]*100);
            }
            $words++;
        }
        //$entime4 = time();

        if ($type == "or") {
            $words = 1;
        }
        $result_array_full = array();
        if ($possible_to_find !=0) {
            if ($words == 1 && $not_words == 0) { //如果只有一个搜索词，我们已经有了结果
                $result_array_full = $linklist[0]['weight'];
            } else { //否则建立的所有结果的交集
                $j= 1;
                $min = 0;
                while ($j < $words) {
                    if (count($linklist[$min]['id']) > count($linklist[$j]['id'])) {
                        $min = $j;
                    }
                    $j++;
                }

                $j = 0;


                $temp_array = $linklist[$min]['id'];
                $count = 0;
                while ($j < count($temp_array)) {
                    $k = 0; //和字计数器
                    $n = 0; //不字计数器
                    $o = 0; //短语字计数器
                    $weight = 1;
                    $break = 0;
                    while ($k < $words && $break== 0) {
                        if ($linklist[$k]['weight'][$temp_array[$j]] > 0) {
                            $weight = $weight + $linklist[$k]['weight'][$temp_array[$j]];
                        } else {
                            $break = 1;
                        }
                        $k++;
                    }
                    while ($n < $not_words && $break== 0) {
                        if ($notlist[$n]['id'][$temp_array[$j]] > 0) {
                            $break = 1;
                        }
                        $n++;
                    }

                    while ($o < $phrase_words && $break== 0) {
                        if ($phraselist[$n]['id'][$temp_array[$j]] != 1) {
                            $break = 1;
                        }
                        $o++;
                    }

                    if ($break == 0) {
                        $result_array_full[$temp_array[$j]] = $weight;
                        $count ++;
                    }
                    $j++;
                }
            }
        }
        $end = getmicrotime()- $starttime;

        if (count($result_array_full) == 0) {
            return null;
        }
        arsort ($result_array_full);

        $result_array_temp = $result_array_full;

        while (list($key, $value) = each ($result_array_temp)) {
            $result_array[$key] = $value;
        }


        $results = count($result_array);

        $keys = array_keys($result_array);
        $maxweight = $result_array[$keys[0]];


        for ($i = ($start -1)*$per_page; $i <min($results, ($start -1)*$per_page + $per_page) ; $i++) {
            $in[] = $keys[$i];

        }
        if (!is_array($in)) {
            $res['results'] = $results;
            return $res;
        }

        $inlist = implode(",", $in);

        if ($length_of_link_desc == 0) {
            $fulltxt = "tb_fulltxt";
        } else {
            $fulltxt = "substring(tb_fulltxt, 1, $length_of_link_desc)";
        }

        $query1 = "SELECT distinct link_id, tb_url, tb_title, tb_description,  $fulltxt, tb_size,tb_indexdate,tb_home FROM soweb_site_links WHERE link_id in ($inlist)";

        $result = $this->viewControl->query($query1);

        $i = 0;
        while ($row = mysql_fetch_row($result)) {
            $res[$i]['title'] = $row[2];
            $res[$i]['url'] = $row[1];
            if ($row[3] !=='' && $show_meta_description == 1){
                $res[$i]['fulltxt'] = $row[3];
            }else {
                $res[$i]['fulltxt'] = $row[4];
            }
            $res[$i]['size'] = $row[5];
            $res[$i]['weight'] = $result_array[$row[0]];
            $dom_result = $this->viewControl->query("select tb_url,tb_leave from soweb_sites where site_id='".$sites[$row[0]]."'");
            $dom_row = mysql_fetch_row($dom_result);
            $res[$i]['site_url'] = $dom_row[0];
            $res[$i]['tb_indexdate'] = $row[6];
            $res[$i]['tb_home'] = $row[7];
            $res[$i]['leave'] = $dom_row[1];
            //$res[$i]['site_favicon'] = $res[$i]['site_url']."favicon.ico";

            $i++;
        }


        usort($res, "cmp");

        $res['maxweight'] = $maxweight;
        $res['results'] = $results;
        return $res;
        /**/
    }
    //检所站的
    public function search_site($searchstr, $start, $per_page) {
        $result = $this->viewControl->query("select l.link_id from soweb_site_links as l LEFT JOIN soweb_sites AS s ON l.site_id = s.site_id where s.tb_title like '%{$searchstr}%' group by l.link_id");

        while ($row = mysql_fetch_row($result)) {
            $link_idarray[] = $row['0'];
        }
        $results_num = count($link_idarray);

        if($results_num > 0){

            for ($i = ($start -1)*$per_page; $i < min($results_num, ($start -1)*$per_page + $per_page) ; $i++) {
                $in[] = $link_idarray[$i];
            }

            $inlist = implode(",", $in);

            $result = $this->viewControl->query("SELECT distinct link_id, tb_url, tb_title, tb_description , tb_size,tb_indexdate,tb_home,site_id FROM soweb_site_links WHERE link_id in ($inlist)");

            $i = 0;
            while ($row = mysql_fetch_row($result)) {
                $res[$i]['title'] = $row[2];
                $res[$i]['url'] = $row[1];
                $res[$i]['fulltxt'] = $row[3];
                $res[$i]['size'] = $row[4];
                $dom_result = $this->viewControl->query("select tb_url,tb_quality,tb_leave from soweb_sites where site_id='".$row[7]."'");
                $dom_row = mysql_fetch_row($dom_result);
                $res[$i]['site_url'] = $dom_row[0];
                $res[$i]['weight'] = $dom_row[1];
                $jhweight[] = $dom_row[1];
                $res[$i]['tb_indexdate'] = $row[5];
                $res[$i]['tb_home'] = $row[6];
                $res[$i]['leave'] = $dom_row[2];
                //$res[$i]['site_favicon'] = $res[$i]['site_url']."favicon.ico";

                $i++;
            }

            rsort($jhweight);
            usort($res, "cmp");

            $res['maxweight'] = $jhweight['0'];
            $res['results'] = $results_num;
        }else{
            return null;
        }
        return $res;
        /**/
    }
    /*
    *搜索结果函数
    */
    public function get_search_results($query, $start, $searchtype, $results) {
        $sph_messages = $this->sph_messages;
        $results_per_page = $this->results_per_page;
        $links_to_next = $this->links_to_next;
        $desc_length = $this->desc_length;
        $did_you_mean_enabled = $this->did_you_mean_enabled;

        if ($results != "") {
            $results_per_page = $results;
        }

        if ($searchtype == "phrase") {
            $query=str_replace('"','',$query);
            $query = "\"".$query."\"";
        }

        $starttime = getmicrotime();
        // catch " if only one time entered
        if (substr_count($query,'"')==1){
            $query=str_replace('"','',$query);
        }
        $words = $this->makeboollist($query);

        //print_r($words);

        //$ignorewords = $words['ignore'];

        $full_result['ignore_words'] = $words['ignore'];

        if ($start==0)
            $start=1;

        $result = $this->search($words, $start, $results_per_page, $searchtype);
        if(!isset($result['results'])){
            $words = $this->twoboollist($query);

            //$ignorewords = $words['ignore'];

            $full_result['ignore_words'] = $words['ignore'];

            if ($start==0)
                $start=1;

            $result = $this->search($words, $start, $results_per_page, "or");


            if(!isset($result['results'])){
                $words = $this->threeboollist($query);

                //$ignorewords = $words['ignore'];

                $full_result['ignore_words'] = $words['ignore'];

                if ($start==0)
                    $start=1;

                $result = $this->search($words, $start, $results_per_page, "or");

                if(!isset($result['results'])){
                    $words = array();
                    $words['hilight']['0'] = $query;
                    $words['+']['0'] = $query;
                    $result = $this->search_site($query, $start, $results_per_page);
                }
            }
        }


        $did_you_meanarray = array();
        if ($did_you_mean_enabled == 1) {
            $did_you_words = $this->twoboollist($query);
            if($did_you_words){
                $near_words = array();
                reset($did_you_words['+']);
                foreach ($did_you_words['+'] as $did_you_word) {
                    $did_you_word = addslashes($did_you_word);
                    $resultto = $this->viewControl->query("select query from soweb_query_log where query like '%{$did_you_word}%' and query <> '{$did_you_word}' and results > '2' group by query order by results limit 0,5");
                    while ($row=mysql_fetch_row($resultto)) {
                        $near_words[] = $row[0];
                    }

                }

                $did_you_meanarray = array_unique($near_words);
            }
        }

        $query= stripslashes($query);

        $entitiesQuery = htmlspecialchars($query);
        $full_result['ent_query'] = $entitiesQuery;

        $endtime = getmicrotime() - $starttime;

        $rows = $result['results'];
        $time = round($endtime*100)/100;


        $full_result['time'] = $time;

        $full_result['did_you_mean'] = $did_you_meanarray;

        $matchword = $sph_messages["matches"];
        if ($rows == 1) {
            $matchword= $sph_messages["match"];
        }

        $num_of_results = count($result) - 2;

        $full_result['num_of_results'] = $num_of_results;


        if ($start < 2)
            $this->saveToLog(addslashes($query), $time, $rows);
        $from = ($start-1) * $results_per_page+1;
        $to = min(($start)*$results_per_page, $rows);

        $full_result['from'] = $from;
        $full_result['to'] = $to;
        $full_result['total_results'] = $rows;


        if ($rows>0) {
            $maxweight = $result['maxweight'];
            $i = 0;
            while ($i < $num_of_results && $i < $results_per_page) {
                $title = $result[$i]['title'];
                $url = $result[$i]['url'];
                $fulltxt = $result[$i]['fulltxt'];
                $page_size = $result[$i]['size'];
                $site_url = $result[$i]['site_url'];
                $indexdate = $result[$i]['tb_indexdate'];
                //$site_favicon = $result[$i]['site_favicon'];
                $leave = $result[$i]['leave'];
                $home = $result[$i]['tb_home'];
                if ($page_size!="")
                    $page_size = number_format($page_size, 1)."kb";


                $txtlen = strlen($fulltxt);
                if ($txtlen > $desc_length) {
                    $fulltxt = sub_str($fulltxt,$desc_length).'...';
                }


                $weight = number_format($result[$i]['weight']/$maxweight*100, 2);
                if ($title=='')
                    $title = $sph_messages["Untitled"];
                $regs = array();

                if (strlen($title) > 80) {
                    $title = sub_str($title,40);
                }


                foreach($words['hilight'] as $change) {
                    while (@preg_match("/[^\>](".$change.")[^\<]/i", " ".$title." ", $regs)) {
                        $title = preg_replace("/".$regs[1]."/i", "<b>".$regs[1]."</b>", $title);
                    }

                    while (@preg_match("/[^\>](".$change.")[^\<]/i", " ".$fulltxt." ", $regs)) {
                        $fulltxt = preg_replace("/".$regs[1]."/i", "<b>".$regs[1]."</b>", $fulltxt);
                    }
                    $url2 = $url;
                    while (@preg_match("/[^\>](".$change.")[^\<]/i", $url2, $regs)) {
                        $url2 = preg_replace("/".$regs[1]."/i", "<b>".$regs[1]."</b>", $url2);
                    }
                }


                $num = $from + $i;

                $full_result['qry_results'][$i]['num'] =  $num;
                $full_result['qry_results'][$i]['weight'] =  $weight;
                $full_result['qry_results'][$i]['url'] =  $url;
                $full_result['qry_results'][$i]['title'] =  $title;
                $full_result['qry_results'][$i]['fulltxt'] =  $fulltxt;
                $full_result['qry_results'][$i]['url2'] =  $url2;
                $full_result['qry_results'][$i]['page_size'] =  $page_size;
                $full_result['qry_results'][$i]['site_url'] =  $site_url;
                $full_result['qry_results'][$i]['indexdate'] =  $indexdate;
                //$full_result['qry_results'][$i]['site_favicon'] =  $site_favicon;
                $full_result['qry_results'][$i]['home'] =  $home;
                $full_result['qry_results'][$i]['leave'] =  $leave;
                $i++;
            }
        }



        $pages = ceil($rows / $results_per_page);
        $full_result['pages'] = $pages;
        $prev = $start - 1;
        $full_result['prev'] = $prev;
        $next = $start + 1;
        $full_result['next'] = $next;
        $full_result['start'] = $start;
        $full_result['query'] = $entitiesQuery;

        if ($from <= $to) {

            $firstpage = $start - $links_to_next;
            if ($firstpage < 1) $firstpage = 1;
            $lastpage = $start + $links_to_next;
            if ($lastpage > $pages) $lastpage = $pages;

            for ($x=$firstpage; $x<=$lastpage; $x++)
                $full_result['other_pages'][] = $x;

        }

        return $full_result;

    }
}